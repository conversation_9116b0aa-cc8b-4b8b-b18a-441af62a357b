"""
Unit tests for enhanced migration framework.

This module tests the migration framework enhancements including:
- Migration validation and verification
- Rollback procedures with safety checks
- Schema comparison and drift detection
- Data migration helpers
- Migration history tracking
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock, mock_open
from datetime import datetime

from app.db.migrations import (
    MigrationInfo,
    MigrationValidationResult,
    SchemaComparisonResult,
    MigrationManager,
    DataMigrationHelper,
    run_migrations,
    create_migration,
    rollback_migration,
    get_migration_status
)


class TestMigrationInfo:
    """Test cases for MigrationInfo dataclass."""
    
    def test_migration_info_creation(self):
        """Test MigrationInfo creation and attributes."""
        migration_info = MigrationInfo(
            revision="abc123",
            description="Test migration",
            branch_labels=None,
            depends_on=None,
            created_at=datetime.utcnow(),
            is_head=True,
            is_current=False
        )
        
        assert migration_info.revision == "abc123"
        assert migration_info.description == "Test migration"
        assert migration_info.is_head is True
        assert migration_info.is_current is False


class TestMigrationValidationResult:
    """Test cases for MigrationValidationResult dataclass."""
    
    def test_validation_result_creation(self):
        """Test MigrationValidationResult creation."""
        result = MigrationValidationResult(
            is_valid=True,
            errors=[],
            warnings=["Test warning"],
            recommendations=["Test recommendation"]
        )
        
        assert result.is_valid is True
        assert result.errors == []
        assert result.warnings == ["Test warning"]
        assert result.recommendations == ["Test recommendation"]


class TestSchemaComparisonResult:
    """Test cases for SchemaComparisonResult dataclass."""
    
    def test_schema_comparison_result_creation(self):
        """Test SchemaComparisonResult creation."""
        result = SchemaComparisonResult(
            has_differences=True,
            missing_tables=["table1"],
            extra_tables=["table2"],
            column_differences={"table3": ["missing col1"]},
            index_differences={},
            constraint_differences={}
        )
        
        assert result.has_differences is True
        assert result.missing_tables == ["table1"]
        assert result.extra_tables == ["table2"]
        assert "table3" in result.column_differences


class TestMigrationManager:
    """Test cases for MigrationManager class."""
    
    @pytest.fixture
    def mock_migration_manager(self):
        """Create a mock migration manager for testing."""
        with patch('app.db.migrations.Config') as mock_config:
            with patch('app.db.migrations.ScriptDirectory') as mock_script_dir:
                mock_config.return_value = MagicMock()
                mock_script_dir.from_config.return_value = MagicMock()
                
                manager = MigrationManager("test_alembic.ini")
                return manager
    
    def test_migration_manager_initialization(self, mock_migration_manager):
        """Test MigrationManager initialization."""
        assert mock_migration_manager.alembic_config_path == "test_alembic.ini"
        assert mock_migration_manager.alembic_config is not None
        assert mock_migration_manager.script_directory is not None
    
    def test_validate_migration_missing_revision(self, mock_migration_manager):
        """Test migration validation with missing revision."""
        mock_migration_manager.script_directory.get_revision.return_value = None
        
        result = mock_migration_manager.validate_migration("nonexistent")
        
        assert result.is_valid is False
        assert "Migration nonexistent not found" in result.errors
    
    def test_validate_migration_missing_file(self, mock_migration_manager):
        """Test migration validation with missing file."""
        mock_script = MagicMock()
        mock_script.path = "/nonexistent/path.py"
        mock_migration_manager.script_directory.get_revision.return_value = mock_script
        
        result = mock_migration_manager.validate_migration("test_rev")
        
        assert result.is_valid is False
        assert "Migration file" in result.errors[0]
        assert "not found" in result.errors[0]
    
    def test_validate_migration_dangerous_operations(self, mock_migration_manager):
        """Test migration validation detects dangerous operations."""
        mock_script = MagicMock()
        mock_script.path = "/test/migration.py"
        mock_migration_manager.script_directory.get_revision.return_value = mock_script
        
        migration_content = """
        def upgrade():
            op.drop_table('old_table')
            op.drop_column('users', 'old_column')
        
        def downgrade():
            op.create_table('old_table')
        """
        
        with patch('builtins.open', mock_open(read_data=migration_content)):
            with patch('os.path.exists', return_value=True):
                result = mock_migration_manager.validate_migration("test_rev")
        
        assert result.is_valid is True  # No errors, just warnings
        assert len(result.warnings) >= 2  # Should detect DROP TABLE and DROP COLUMN
        assert any("table drop" in warning.lower() for warning in result.warnings)
        assert any("column drop" in warning.lower() for warning in result.warnings)
    
    def test_validate_migration_missing_downgrade(self, mock_migration_manager):
        """Test migration validation detects missing downgrade function."""
        mock_script = MagicMock()
        mock_script.path = "/test/migration.py"
        mock_migration_manager.script_directory.get_revision.return_value = mock_script
        
        migration_content = """
        def upgrade():
            op.create_table('new_table')
        """
        
        with patch('builtins.open', mock_open(read_data=migration_content)):
            with patch('os.path.exists', return_value=True):
                result = mock_migration_manager.validate_migration("test_rev")
        
        assert result.is_valid is False
        assert "Migration missing downgrade function" in result.errors
    
    def test_validate_migration_empty_downgrade(self, mock_migration_manager):
        """Test migration validation detects empty downgrade function."""
        mock_script = MagicMock()
        mock_script.path = "/test/migration.py"
        mock_migration_manager.script_directory.get_revision.return_value = mock_script
        
        migration_content = """
        def upgrade():
            op.create_table('new_table')
        
        def downgrade():
            pass
        """
        
        with patch('builtins.open', mock_open(read_data=migration_content)):
            with patch('os.path.exists', return_value=True):
                result = mock_migration_manager.validate_migration("test_rev")
        
        assert result.is_valid is True  # Valid but with warnings
        assert any("empty or minimal downgrade" in warning for warning in result.warnings)
    
    def test_validate_migration_data_operations(self, mock_migration_manager):
        """Test migration validation detects data operations."""
        mock_script = MagicMock()
        mock_script.path = "/test/migration.py"
        mock_migration_manager.script_directory.get_revision.return_value = mock_script
        
        migration_content = """
        def upgrade():
            op.create_table('new_table')
            op.execute("INSERT INTO new_table VALUES (1, 'test')")
        
        def downgrade():
            op.drop_table('new_table')
        """
        
        with patch('builtins.open', mock_open(read_data=migration_content)):
            with patch('os.path.exists', return_value=True):
                result = mock_migration_manager.validate_migration("test_rev")
        
        assert result.is_valid is True
        assert any("data operations" in rec for rec in result.recommendations)
    
    def test_create_backup_sqlite(self, mock_migration_manager):
        """Test backup creation for SQLite database."""
        with patch('app.db.migrations.settings') as mock_settings:
            mock_settings.DATABASE_URL = "sqlite:///test.db"
            
            with patch('shutil.copy2') as mock_copy:
                with patch('tempfile.gettempdir', return_value="/tmp"):
                    backup_path = mock_migration_manager.create_backup("test_backup.sql")
                    
                    assert backup_path == "/tmp/test_backup.sql"
                    mock_copy.assert_called_once()
    
    def test_create_backup_postgresql(self, mock_migration_manager):
        """Test backup creation for PostgreSQL database."""
        with patch('app.db.migrations.settings') as mock_settings:
            mock_settings.DATABASE_URL = "postgresql://user:pass@localhost/db"
            
            with patch('tempfile.gettempdir', return_value="/tmp"):
                backup_path = mock_migration_manager.create_backup("test_backup.sql")
                
                assert backup_path == "/tmp/test_backup.sql"
    
    def test_get_current_revision_success(self, mock_migration_manager):
        """Test successful current revision retrieval."""
        with patch('app.db.migrations.sync_engine') as mock_engine:
            mock_connection = MagicMock()
            mock_context = MagicMock()
            mock_context.get_current_revision.return_value = "abc123"
            
            mock_engine.connect.return_value.__enter__.return_value = mock_connection
            
            with patch('app.db.migrations.MigrationContext') as mock_migration_context:
                mock_migration_context.configure.return_value = mock_context
                
                revision = mock_migration_manager.get_current_revision()
                
                assert revision == "abc123"
    
    def test_get_current_revision_failure(self, mock_migration_manager):
        """Test current revision retrieval failure."""
        with patch('app.db.migrations.sync_engine') as mock_engine:
            mock_engine.connect.side_effect = Exception("Database error")
            
            revision = mock_migration_manager.get_current_revision()
            
            assert revision is None


class TestDataMigrationHelper:
    """Test cases for DataMigrationHelper class."""
    
    @pytest.fixture
    def migration_helper(self):
        """Create a DataMigrationHelper instance for testing."""
        return DataMigrationHelper(batch_size=100)
    
    def test_data_migration_helper_initialization(self, migration_helper):
        """Test DataMigrationHelper initialization."""
        assert migration_helper.batch_size == 100
    
    @pytest.mark.asyncio
    async def test_batch_update_success(self, migration_helper):
        """Test successful batch update operation."""
        with patch('app.db.migrations.get_async_session_context') as mock_session_context:
            mock_session = MagicMock()
            mock_session_context.return_value.__aenter__.return_value = mock_session
            mock_session_context.return_value.__aexit__.return_value = None
            
            # Mock count query result
            mock_count_result = MagicMock()
            mock_count_result.scalar.return_value = 250  # Total rows
            
            # Mock update query results
            mock_update_result = MagicMock()
            mock_update_result.rowcount = 100  # Rows updated per batch
            
            mock_session.execute.side_effect = [
                mock_count_result,  # Count query
                mock_update_result,  # First batch
                mock_update_result,  # Second batch
                mock_update_result,  # Third batch (partial)
            ]
            
            total_updated = await migration_helper.batch_update(
                "test_table",
                "UPDATE test_table SET status = 'active'",
                "created_at > '2023-01-01'"
            )
            
            assert total_updated == 300  # 3 batches * 100 rows
            assert mock_session.execute.call_count == 4  # 1 count + 3 updates
            assert mock_session.commit.call_count == 3  # 3 batch commits
    
    @pytest.mark.asyncio
    async def test_safe_column_migration_success(self, migration_helper):
        """Test successful column migration."""
        with patch('app.db.migrations.get_async_session_context') as mock_session_context:
            with patch('app.db.migrations.inspect') as mock_inspect:
                with patch('app.db.migrations.sync_engine') as mock_engine:
                    # Setup mocks
                    mock_session = MagicMock()
                    mock_session_context.return_value.__aenter__.return_value = mock_session
                    mock_session_context.return_value.__aexit__.return_value = None
                    
                    mock_inspector = MagicMock()
                    mock_inspector.get_columns.return_value = [
                        {'name': 'old_column'},
                        {'name': 'new_column'},
                        {'name': 'other_column'}
                    ]
                    mock_inspect.return_value = mock_inspector
                    
                    # Mock update result
                    mock_update_result = MagicMock()
                    mock_update_result.rowcount = 50
                    mock_session.execute.return_value = mock_update_result
                    
                    # Execute migration
                    result = await migration_helper.safe_column_migration(
                        "test_table",
                        "old_column",
                        "new_column",
                        "UPPER"
                    )
                    
                    assert result is True
                    mock_session.execute.assert_called_once()
                    mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_safe_column_migration_missing_column(self, migration_helper):
        """Test column migration with missing source column."""
        with patch('app.db.migrations.get_async_session_context') as mock_session_context:
            with patch('app.db.migrations.inspect') as mock_inspect:
                with patch('app.db.migrations.sync_engine') as mock_engine:
                    # Setup mocks
                    mock_session = MagicMock()
                    mock_session_context.return_value.__aenter__.return_value = mock_session
                    mock_session_context.return_value.__aexit__.return_value = None
                    
                    mock_inspector = MagicMock()
                    mock_inspector.get_columns.return_value = [
                        {'name': 'new_column'},
                        {'name': 'other_column'}
                    ]  # Missing 'old_column'
                    mock_inspect.return_value = mock_inspector
                    
                    # Execute migration
                    result = await migration_helper.safe_column_migration(
                        "test_table",
                        "old_column",
                        "new_column",
                        "UPPER"
                    )
                    
                    assert result is False


class TestMigrationUtilityFunctions:
    """Test cases for migration utility functions."""
    
    def test_run_migrations_function(self):
        """Test run_migrations utility function."""
        with patch('app.db.migrations.migration_manager') as mock_manager:
            mock_manager.upgrade_with_validation.return_value = True
            
            result = run_migrations()
            
            assert result is True
            mock_manager.upgrade_with_validation.assert_called_once()
    
    def test_create_migration_function(self):
        """Test create_migration utility function."""
        with patch('app.db.migrations.migration_manager') as mock_manager:
            mock_manager.generate_migration.return_value = "abc123"
            
            result = create_migration("Test migration", autogenerate=True)
            
            assert result == "abc123"
            mock_manager.generate_migration.assert_called_once_with("Test migration", True)
    
    def test_rollback_migration_function(self):
        """Test rollback_migration utility function."""
        with patch('app.db.migrations.migration_manager') as mock_manager:
            mock_manager.downgrade_with_validation.return_value = True
            
            result = rollback_migration("abc123", confirm=True)
            
            assert result is True
            mock_manager.downgrade_with_validation.assert_called_once_with("abc123", True)
    
    def test_get_migration_status_function(self):
        """Test get_migration_status utility function."""
        with patch('app.db.migrations.migration_manager') as mock_manager:
            # Setup mock returns
            mock_manager.get_current_revision.return_value = "abc123"
            mock_manager.script_directory.get_current_head.return_value = "def456"
            mock_manager.get_migration_history.return_value = [
                MagicMock(is_current=True),
                MagicMock(is_current=False),
                MagicMock(is_current=False)
            ]
            
            status = get_migration_status()
            
            assert status["current_revision"] == "abc123"
            assert status["head_revision"] == "def456"
            assert status["is_up_to_date"] is False
            assert status["migration_count"] == 3
            assert status["pending_migrations"] == 2


class TestMigrationIntegration:
    """Test cases for migration framework integration."""
    
    def test_migration_framework_imports(self):
        """Test that all migration framework components can be imported."""
        from app.db.migrations import (
            MigrationManager,
            DataMigrationHelper,
            migration_manager,
            data_migration_helper
        )
        
        assert MigrationManager is not None
        assert DataMigrationHelper is not None
        assert migration_manager is not None
        assert data_migration_helper is not None
    
    def test_migration_dataclasses_import(self):
        """Test that migration dataclasses can be imported."""
        from app.db.migrations import (
            MigrationInfo,
            MigrationValidationResult,
            SchemaComparisonResult
        )
        
        assert MigrationInfo is not None
        assert MigrationValidationResult is not None
        assert SchemaComparisonResult is not None
    
    def test_migration_utility_functions_import(self):
        """Test that migration utility functions can be imported."""
        from app.db.migrations import (
            run_migrations,
            create_migration,
            rollback_migration,
            get_migration_status
        )
        
        assert run_migrations is not None
        assert create_migration is not None
        assert rollback_migration is not None
        assert get_migration_status is not None
