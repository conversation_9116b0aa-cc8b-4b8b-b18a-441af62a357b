"""
Infrastructure Optimization schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic V2 schemas for infrastructure optimization including:
- Cache configuration and metrics schemas
- Background task management and monitoring schemas
- Performance monitoring and analytics schemas
- Infrastructure optimization request/response schemas

Implements Task 3.2.3 requirements with production-grade validation,
Pydantic V2 compliance, and seamless integration with existing
marketplace optimization services from Task 3.2.2.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, List, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field
from pydantic.types import PositiveInt, PositiveFloat, confloat

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin


class CacheType(str, Enum):
    """Cache type enumeration for different optimization data."""
    OPTIMIZATION_SCORE = "optimization_score"
    MARKET_INTELLIGENCE = "market_intelligence"
    VENDOR_SUMMARY = "vendor_summary"
    PERFORMANCE_ANALYTICS = "performance_analytics"
    COMPETITIVE_ANALYSIS = "competitive_analysis"
    SEO_ANALYSIS = "seo_analysis"
    MOBILE_OPTIMIZATION = "mobile_optimization"


class TaskStatus(str, Enum):
    """Background task status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILURE = "failure"
    RETRY = "retry"
    REVOKED = "revoked"


class TaskPriority(str, Enum):
    """Background task priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


# Cache Configuration Schemas

class CacheConfigurationBase(BaseSchema):
    """Base schema for cache configuration."""
    cache_type: CacheType = Field(..., description="Type of cache data")
    cache_key_pattern: str = Field(..., min_length=1, max_length=255, description="Cache key pattern")
    ttl_seconds: PositiveInt = Field(..., description="Time to live in seconds")
    max_size_mb: PositiveInt = Field(default=100, description="Maximum cache size in MB")
    compression_enabled: bool = Field(default=True, description="Enable compression")
    invalidation_rules: Dict[str, Any] = Field(default_factory=dict, description="Cache invalidation rules")
    is_active: bool = Field(default=True, description="Configuration active status")

    @field_validator('cache_key_pattern')
    @classmethod
    def validate_cache_key_pattern(cls, v: str) -> str:
        """Validate cache key pattern format."""
        if not v.strip():
            raise ValueError("Cache key pattern cannot be empty")
        # Basic pattern validation
        if '{' in v and '}' not in v:
            raise ValueError("Invalid cache key pattern format")
        return v.strip()

    @field_validator('ttl_seconds')
    @classmethod
    def validate_ttl_seconds(cls, v: int) -> int:
        """Validate TTL is within reasonable bounds."""
        if v < 60:  # Minimum 1 minute
            raise ValueError("TTL must be at least 60 seconds")
        if v > 86400 * 7:  # Maximum 7 days
            raise ValueError("TTL cannot exceed 7 days")
        return v


class CacheConfigurationCreate(CacheConfigurationBase):
    """Schema for creating cache configuration."""
    pass


class CacheConfigurationUpdate(BaseSchema):
    """Schema for updating cache configuration."""
    cache_type: Optional[CacheType] = None
    cache_key_pattern: Optional[str] = Field(None, min_length=1, max_length=255)
    ttl_seconds: Optional[PositiveInt] = None
    max_size_mb: Optional[PositiveInt] = None
    compression_enabled: Optional[bool] = None
    invalidation_rules: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class CacheConfigurationResponse(CacheConfigurationBase, UUIDMixin, TimestampMixin):
    """Schema for cache configuration response."""
    created_by: Optional[UUID] = Field(None, description="Configuration creator")

    model_config = ConfigDict(from_attributes=True)


# Background Task Schemas

class BackgroundTaskBase(BaseSchema):
    """Base schema for background task."""
    task_name: str = Field(..., min_length=1, max_length=255, description="Task name")
    task_type: str = Field(..., min_length=1, max_length=100, description="Task type")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="Task priority")
    queue_name: str = Field(default="default", max_length=100, description="Queue name")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    task_args: Dict[str, Any] = Field(default_factory=dict, description="Task arguments")
    task_kwargs: Dict[str, Any] = Field(default_factory=dict, description="Task keyword arguments")


class BackgroundTaskCreate(BackgroundTaskBase):
    """Schema for creating background task."""
    correlation_id: Optional[str] = Field(None, max_length=255, description="Request correlation ID")


class BackgroundTaskUpdate(BaseSchema):
    """Schema for updating background task."""
    status: Optional[TaskStatus] = None
    worker_name: Optional[str] = Field(None, max_length=255)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[PositiveFloat] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_traceback: Optional[str] = None


class BackgroundTaskResponse(BackgroundTaskBase, UUIDMixin):
    """Schema for background task response."""
    task_id: str = Field(..., description="Celery task ID")
    status: TaskStatus = Field(..., description="Task status")
    retry_count: int = Field(default=0, description="Current retry count")
    
    # Timing information
    queued_at: datetime = Field(..., description="Task queued timestamp")
    started_at: Optional[datetime] = Field(None, description="Task started timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completed timestamp")
    execution_time_seconds: Optional[float] = Field(None, description="Execution time in seconds")
    
    # Results and errors
    result_data: Optional[Dict[str, Any]] = Field(None, description="Task result data")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    # Metadata
    created_by: Optional[UUID] = Field(None, description="Task creator")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")

    @computed_field
    @property
    def is_completed(self) -> bool:
        """Check if task is completed (success or failure)."""
        return self.status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]

    @computed_field
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate task duration if completed."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    model_config = ConfigDict(from_attributes=True)


# Performance Monitoring Schemas

class PerformanceMonitoringBase(BaseSchema):
    """Base schema for performance monitoring."""
    query_type: str = Field(..., min_length=1, max_length=100, description="Query type")
    query_hash: str = Field(..., min_length=1, max_length=64, description="Query hash")
    table_names: List[str] = Field(default_factory=list, description="Tables involved in query")
    execution_time_ms: PositiveFloat = Field(..., description="Execution time in milliseconds")
    rows_examined: int = Field(default=0, ge=0, description="Rows examined")
    rows_returned: int = Field(default=0, ge=0, description="Rows returned")
    memory_usage_mb: Optional[PositiveFloat] = Field(None, description="Memory usage in MB")
    cpu_usage_percent: Optional[confloat(ge=0, le=100)] = Field(None, description="CPU usage percentage")
    endpoint_path: Optional[str] = Field(None, max_length=255, description="API endpoint path")


class PerformanceMonitoringCreate(PerformanceMonitoringBase):
    """Schema for creating performance monitoring record."""
    query_plan: Optional[Dict[str, Any]] = Field(None, description="Query execution plan")
    index_usage: Dict[str, Any] = Field(default_factory=dict, description="Index usage statistics")
    optimization_suggestions: List[str] = Field(default_factory=list, description="Optimization suggestions")
    correlation_id: Optional[str] = Field(None, max_length=255, description="Request correlation ID")

    @field_validator('execution_time_ms')
    @classmethod
    def validate_execution_time(cls, v: float) -> float:
        """Validate execution time is reasonable."""
        if v > 300000:  # 5 minutes
            raise ValueError("Execution time seems unreasonably high")
        return v


class PerformanceMonitoringResponse(PerformanceMonitoringBase, UUIDMixin):
    """Schema for performance monitoring response."""
    query_plan: Optional[Dict[str, Any]] = Field(None, description="Query execution plan")
    index_usage: Dict[str, Any] = Field(default_factory=dict, description="Index usage statistics")
    optimization_suggestions: List[str] = Field(default_factory=list, description="Optimization suggestions")
    is_slow_query: bool = Field(default=False, description="Slow query flag")
    user_id: Optional[UUID] = Field(None, description="User who executed query")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")
    executed_at: datetime = Field(..., description="Query execution timestamp")

    @computed_field
    @property
    def efficiency_score(self) -> float:
        """Calculate query efficiency score (0-100)."""
        if self.rows_examined == 0:
            return 100.0
        
        efficiency = (self.rows_returned / self.rows_examined) * 100
        return min(100.0, max(0.0, efficiency))

    model_config = ConfigDict(from_attributes=True)


# Cache Metrics Schemas

class CacheMetricsBase(BaseSchema):
    """Base schema for cache metrics."""
    cache_hits: int = Field(default=0, ge=0, description="Cache hits count")
    cache_misses: int = Field(default=0, ge=0, description="Cache misses count")
    memory_usage_mb: float = Field(default=0.0, ge=0, description="Memory usage in MB")
    key_count: int = Field(default=0, ge=0, description="Number of keys")
    expired_keys: int = Field(default=0, ge=0, description="Expired keys count")
    evicted_keys: int = Field(default=0, ge=0, description="Evicted keys count")
    avg_response_time_ms: float = Field(default=0.0, ge=0, description="Average response time")
    max_response_time_ms: float = Field(default=0.0, ge=0, description="Maximum response time")
    operations_per_second: float = Field(default=0.0, ge=0, description="Operations per second")
    period_start: datetime = Field(..., description="Metrics period start")
    period_end: datetime = Field(..., description="Metrics period end")

    @computed_field
    @property
    def cache_hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total_requests = self.cache_hits + self.cache_misses
        if total_requests == 0:
            return 0.0
        return (self.cache_hits / total_requests) * 100

    @field_validator('period_end')
    @classmethod
    def validate_period_end(cls, v: datetime, info) -> datetime:
        """Validate period end is after period start."""
        if 'period_start' in info.data and v <= info.data['period_start']:
            raise ValueError("Period end must be after period start")
        return v


class CacheMetricsCreate(CacheMetricsBase):
    """Schema for creating cache metrics."""
    configuration_id: UUID = Field(..., description="Cache configuration ID")


class CacheMetricsResponse(CacheMetricsBase, UUIDMixin):
    """Schema for cache metrics response."""
    configuration_id: UUID = Field(..., description="Cache configuration ID")
    recorded_at: datetime = Field(..., description="Metrics recorded timestamp")

    model_config = ConfigDict(from_attributes=True)


# Task Metrics Schemas

class TaskMetricsBase(BaseSchema):
    """Base schema for task metrics."""
    queue_wait_time_seconds: float = Field(default=0.0, ge=0, description="Queue wait time")
    execution_time_seconds: float = Field(default=0.0, ge=0, description="Execution time")
    memory_peak_mb: float = Field(default=0.0, ge=0, description="Peak memory usage")
    cpu_usage_percent: confloat(ge=0, le=100) = Field(default=0.0, description="CPU usage percentage")
    database_queries_count: int = Field(default=0, ge=0, description="Database queries count")
    cache_operations_count: int = Field(default=0, ge=0, description="Cache operations count")
    external_api_calls: int = Field(default=0, ge=0, description="External API calls count")
    throughput_items_per_second: float = Field(default=0.0, ge=0, description="Throughput rate")
    error_rate_percent: confloat(ge=0, le=100) = Field(default=0.0, description="Error rate percentage")
    success_rate_percent: confloat(ge=0, le=100) = Field(default=100.0, description="Success rate percentage")
    worker_node: Optional[str] = Field(None, max_length=255, description="Worker node identifier")


class TaskMetricsCreate(TaskMetricsBase):
    """Schema for creating task metrics."""
    task_id: UUID = Field(..., description="Background task ID")


class TaskMetricsResponse(TaskMetricsBase, UUIDMixin):
    """Schema for task metrics response."""
    task_id: UUID = Field(..., description="Background task ID")
    measured_at: datetime = Field(..., description="Metrics measured timestamp")

    @computed_field
    @property
    def efficiency_score(self) -> float:
        """Calculate task efficiency score (0-100)."""
        # Combine success rate and resource efficiency
        resource_efficiency = 100 - (self.cpu_usage_percent * 0.5 + min(self.memory_peak_mb / 1000, 1) * 50)
        return (self.success_rate_percent * 0.7 + resource_efficiency * 0.3)

    model_config = ConfigDict(from_attributes=True)


# Infrastructure Optimization Dashboard Schemas

class InfrastructureOptimizationDashboard(BaseSchema):
    """Comprehensive infrastructure optimization dashboard schema."""
    cache_performance: Dict[str, Any] = Field(..., description="Cache performance metrics")
    task_performance: Dict[str, Any] = Field(..., description="Task performance metrics")
    database_performance: Dict[str, Any] = Field(..., description="Database performance metrics")
    optimization_recommendations: List[str] = Field(..., description="Infrastructure optimization recommendations")
    system_health_score: float = Field(..., ge=0, le=100, description="Overall system health score")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Dashboard generation timestamp")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "cache_performance": {
                    "hit_rate": 85.5,
                    "memory_usage_mb": 512.3,
                    "operations_per_second": 1250.0
                },
                "task_performance": {
                    "avg_execution_time": 2.5,
                    "success_rate": 98.2,
                    "queue_length": 15
                },
                "database_performance": {
                    "avg_query_time": 45.2,
                    "slow_queries_count": 3,
                    "connection_pool_usage": 65.0
                },
                "optimization_recommendations": [
                    "Increase cache TTL for optimization scores",
                    "Add composite index for analytics queries",
                    "Optimize background task queue configuration"
                ],
                "system_health_score": 92.5
            }
        }
    )
