[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    -p no:web3
    -p no:ethereum
    --asyncio-mode=auto
    --forked
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    slow: Slow running tests
    api: API endpoint tests
    booking: Booking system tests
    websocket: WebSocket tests
    security: Security tests
asyncio_mode = auto
filterwarnings =
    ignore::DeprecationWarning
    ignore::PydanticDeprecatedSince20
    ignore::UserWarning:pydantic._internal._config
    ignore::ImportWarning:cryptography
    ignore::RuntimeWarning:cryptography
