"""
Competitive Analysis Repository for Culture Connect Backend API.

This module provides data access layer for competitive analysis operations including:
- Market positioning queries and competitor comparison methods
- Trend analysis and competitive intelligence
- Market opportunity identification and threat analysis
- Integration with service and vendor competitive workflows

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade async operations and comprehensive competitive analysis capabilities.
"""

import logging
from datetime import datetime, timezone, timedelta, date
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from app.models.marketplace_optimization import CompetitiveAnalysis
from app.models.service import Service
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository, RepositoryError


logger = logging.getLogger(__name__)


class CompetitiveAnalysisRepository(BaseRepository[CompetitiveAnalysis]):
    """
    Repository for competitive analysis data access operations.
    
    Provides specialized methods for:
    - Market positioning queries and competitor comparison
    - Trend analysis and competitive intelligence
    - Market opportunity identification and threat analysis
    - Service and vendor competitive analysis workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize competitive analysis repository."""
        super().__init__(CompetitiveAnalysis, db)

    async def get_latest_by_service(self, service_id: int) -> Optional[CompetitiveAnalysis]:
        """
        Get the latest competitive analysis for a service.
        
        Args:
            service_id: Service ID to get analysis for
            
        Returns:
            Latest competitive analysis or None if not found
        """
        try:
            result = await self.db.execute(
                Select(CompetitiveAnalysis)
                .where(CompetitiveAnalysis.service_id == service_id)
                .order_by(desc(CompetitiveAnalysis.analysis_date))
                .limit(1)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get latest competitive analysis for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get latest competitive analysis", e)

    async def get_market_positioning(
        self,
        market_category: str,
        geographic_scope: str = "local",
        limit: int = 50
    ) -> List[CompetitiveAnalysis]:
        """
        Get market positioning data for a category and geographic scope.
        
        Args:
            market_category: Market category to analyze
            geographic_scope: Geographic scope (local, regional, national)
            limit: Maximum number of results
            
        Returns:
            List of competitive analyses ordered by market position
        """
        try:
            # Get latest analysis for each service in the category
            latest_subquery = (
                Select(
                    CompetitiveAnalysis.service_id,
                    func.max(CompetitiveAnalysis.analysis_date).label('latest_date')
                )
                .where(
                    and_(
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope
                    )
                )
                .group_by(CompetitiveAnalysis.service_id)
                .subquery()
            )
            
            result = await self.db.execute(
                Select(CompetitiveAnalysis)
                .join(
                    latest_subquery,
                    and_(
                        CompetitiveAnalysis.service_id == latest_subquery.c.service_id,
                        CompetitiveAnalysis.analysis_date == latest_subquery.c.latest_date
                    )
                )
                .options(
                    joinedload(CompetitiveAnalysis.service),
                    joinedload(CompetitiveAnalysis.vendor)
                )
                .order_by(asc(CompetitiveAnalysis.market_position_rank))
                .limit(limit)
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get market positioning for {market_category}: {str(e)}")
            raise RepositoryError(f"Failed to get market positioning", e)

    async def get_competitor_comparison(
        self,
        service_id: int,
        market_category: str,
        geographic_scope: str = "local"
    ) -> Dict[str, Any]:
        """
        Get detailed competitor comparison for a service.
        
        Args:
            service_id: Service ID to compare
            market_category: Market category for comparison
            geographic_scope: Geographic scope for comparison
            
        Returns:
            Detailed competitor comparison data
        """
        try:
            # Get service's latest analysis
            service_analysis = await self.get_latest_by_service(service_id)
            if not service_analysis:
                return {}
            
            # Get market statistics
            market_stats = await self.db.execute(
                Select(
                    func.avg(CompetitiveAnalysis.market_position_rank).label('avg_rank'),
                    func.avg(CompetitiveAnalysis.price_competitiveness_score).label('avg_price_score'),
                    func.avg(CompetitiveAnalysis.quality_score_vs_competitors).label('avg_quality_score'),
                    func.avg(CompetitiveAnalysis.visibility_score).label('avg_visibility'),
                    func.avg(CompetitiveAnalysis.average_competitor_price).label('market_avg_price'),
                    func.count(CompetitiveAnalysis.id).label('total_competitors')
                )
                .where(
                    and_(
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope
                    )
                )
            )
            stats = market_stats.first()
            
            return {
                "service_analysis": {
                    "market_position_rank": service_analysis.market_position_rank,
                    "market_share_percentage": float(service_analysis.market_share_percentage),
                    "price_competitiveness_score": float(service_analysis.price_competitiveness_score),
                    "quality_score_vs_competitors": float(service_analysis.quality_score_vs_competitors),
                    "visibility_score": float(service_analysis.visibility_score),
                    "price_position": service_analysis.price_position
                },
                "market_averages": {
                    "avg_rank": float(stats.avg_rank or 0),
                    "avg_price_score": float(stats.avg_price_score or 0),
                    "avg_quality_score": float(stats.avg_quality_score or 0),
                    "avg_visibility": float(stats.avg_visibility or 0),
                    "market_avg_price": float(stats.market_avg_price or 0),
                    "total_competitors": stats.total_competitors
                },
                "competitive_position": {
                    "rank_vs_average": service_analysis.market_position_rank - float(stats.avg_rank or 0),
                    "price_vs_average": float(service_analysis.price_competitiveness_score) - float(stats.avg_price_score or 0),
                    "quality_vs_average": float(service_analysis.quality_score_vs_competitors) - float(stats.avg_quality_score or 0),
                    "visibility_vs_average": float(service_analysis.visibility_score) - float(stats.avg_visibility or 0)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get competitor comparison for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get competitor comparison", e)

    async def get_market_trends(
        self,
        market_category: str,
        geographic_scope: str = "local",
        days: int = 90
    ) -> Dict[str, Any]:
        """
        Get market trends and evolution over time.
        
        Args:
            market_category: Market category to analyze
            geographic_scope: Geographic scope for analysis
            days: Number of days to analyze trends
            
        Returns:
            Market trends and evolution data
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Get trend data over time
            trend_data = await self.db.execute(
                Select(
                    func.date_trunc('week', CompetitiveAnalysis.analysis_date).label('week'),
                    func.avg(CompetitiveAnalysis.price_competitiveness_score).label('avg_price_score'),
                    func.avg(CompetitiveAnalysis.quality_score_vs_competitors).label('avg_quality_score'),
                    func.avg(CompetitiveAnalysis.visibility_score).label('avg_visibility'),
                    func.avg(CompetitiveAnalysis.average_competitor_price).label('avg_price'),
                    func.count(CompetitiveAnalysis.id).label('analysis_count')
                )
                .where(
                    and_(
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope,
                        CompetitiveAnalysis.analysis_date >= cutoff_date
                    )
                )
                .group_by(func.date_trunc('week', CompetitiveAnalysis.analysis_date))
                .order_by(asc('week'))
            )
            trends = trend_data.all()
            
            # Calculate trend directions
            if len(trends) >= 2:
                first_week = trends[0]
                last_week = trends[-1]
                
                price_trend = float(last_week.avg_price_score) - float(first_week.avg_price_score)
                quality_trend = float(last_week.avg_quality_score) - float(first_week.avg_quality_score)
                visibility_trend = float(last_week.avg_visibility) - float(first_week.avg_visibility)
                price_change = float(last_week.avg_price) - float(first_week.avg_price)
            else:
                price_trend = quality_trend = visibility_trend = price_change = 0.0
            
            return {
                "market_category": market_category,
                "geographic_scope": geographic_scope,
                "analysis_period": {
                    "start_date": cutoff_date.date().isoformat(),
                    "end_date": datetime.now(timezone.utc).date().isoformat(),
                    "weeks_analyzed": len(trends)
                },
                "trends": {
                    "price_competitiveness": {
                        "direction": "increasing" if price_trend > 0 else "decreasing" if price_trend < 0 else "stable",
                        "change": price_trend
                    },
                    "quality_scores": {
                        "direction": "increasing" if quality_trend > 0 else "decreasing" if quality_trend < 0 else "stable",
                        "change": quality_trend
                    },
                    "visibility": {
                        "direction": "increasing" if visibility_trend > 0 else "decreasing" if visibility_trend < 0 else "stable",
                        "change": visibility_trend
                    },
                    "average_pricing": {
                        "direction": "increasing" if price_change > 0 else "decreasing" if price_change < 0 else "stable",
                        "change": price_change
                    }
                },
                "weekly_data": [
                    {
                        "week": row.week.isoformat(),
                        "avg_price_score": float(row.avg_price_score),
                        "avg_quality_score": float(row.avg_quality_score),
                        "avg_visibility": float(row.avg_visibility),
                        "avg_price": float(row.avg_price),
                        "analysis_count": row.analysis_count
                    }
                    for row in trends
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get market trends for {market_category}: {str(e)}")
            raise RepositoryError(f"Failed to get market trends", e)

    async def get_market_opportunities(
        self,
        vendor_id: int,
        market_category: str,
        geographic_scope: str = "local"
    ) -> Dict[str, Any]:
        """
        Identify market opportunities for a vendor.
        
        Args:
            vendor_id: Vendor ID to analyze opportunities for
            market_category: Market category to analyze
            geographic_scope: Geographic scope for analysis
            
        Returns:
            Market opportunities and recommendations
        """
        try:
            # Get vendor's current position
            vendor_analyses = await self.db.execute(
                Select(CompetitiveAnalysis)
                .where(
                    and_(
                        CompetitiveAnalysis.vendor_id == vendor_id,
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope
                    )
                )
                .order_by(desc(CompetitiveAnalysis.analysis_date))
                .limit(5)  # Get recent analyses
            )
            vendor_data = vendor_analyses.scalars().all()
            
            if not vendor_data:
                return {"error": "No competitive analysis data found for vendor"}
            
            latest_analysis = vendor_data[0]
            
            # Get market gaps and opportunities
            market_gaps = await self.db.execute(
                Select(
                    CompetitiveAnalysis.unique_selling_points,
                    CompetitiveAnalysis.competitive_gaps,
                    CompetitiveAnalysis.market_opportunities,
                    CompetitiveAnalysis.threat_analysis
                )
                .where(
                    and_(
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope
                    )
                )
                .order_by(desc(CompetitiveAnalysis.analysis_date))
                .limit(20)  # Analyze recent market data
            )
            market_data = market_gaps.all()
            
            # Aggregate opportunities and gaps
            all_opportunities = []
            all_gaps = []
            all_usps = []
            
            for row in market_data:
                if row.market_opportunities:
                    all_opportunities.extend(row.market_opportunities)
                if row.competitive_gaps:
                    all_gaps.extend(row.competitive_gaps)
                if row.unique_selling_points:
                    all_usps.extend(row.unique_selling_points)
            
            # Count frequency of opportunities and gaps
            opportunity_counts = {}
            gap_counts = {}
            
            for opp in all_opportunities:
                opportunity_counts[opp] = opportunity_counts.get(opp, 0) + 1
            
            for gap in all_gaps:
                gap_counts[gap] = gap_counts.get(gap, 0) + 1
            
            # Sort by frequency
            top_opportunities = sorted(opportunity_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            common_gaps = sorted(gap_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                "vendor_position": {
                    "market_rank": latest_analysis.market_position_rank,
                    "market_share": float(latest_analysis.market_share_percentage),
                    "price_position": latest_analysis.price_position,
                    "quality_score": float(latest_analysis.quality_score_vs_competitors),
                    "visibility_score": float(latest_analysis.visibility_score)
                },
                "market_opportunities": [
                    {"opportunity": opp, "frequency": count, "priority": "high" if count >= 3 else "medium"}
                    for opp, count in top_opportunities
                ],
                "common_market_gaps": [
                    {"gap": gap, "frequency": count, "potential": "high" if count >= 3 else "medium"}
                    for gap, count in common_gaps
                ],
                "vendor_advantages": latest_analysis.unique_selling_points or [],
                "vendor_weaknesses": latest_analysis.competitive_gaps or [],
                "recommendations": self._generate_opportunity_recommendations(
                    latest_analysis, top_opportunities, common_gaps
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to get market opportunities for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get market opportunities", e)

    async def get_threat_analysis(
        self,
        service_id: int,
        market_category: str,
        geographic_scope: str = "local"
    ) -> Dict[str, Any]:
        """
        Get threat analysis for a service in its market.
        
        Args:
            service_id: Service ID to analyze threats for
            market_category: Market category for analysis
            geographic_scope: Geographic scope for analysis
            
        Returns:
            Threat analysis and competitive risks
        """
        try:
            # Get service's latest analysis
            service_analysis = await self.get_latest_by_service(service_id)
            if not service_analysis:
                return {"error": "No competitive analysis found for service"}
            
            # Get competitor threat data
            threats = await self.db.execute(
                Select(CompetitiveAnalysis.threat_analysis)
                .where(
                    and_(
                        CompetitiveAnalysis.market_category == market_category,
                        CompetitiveAnalysis.geographic_scope == geographic_scope,
                        CompetitiveAnalysis.service_id != service_id
                    )
                )
                .order_by(desc(CompetitiveAnalysis.analysis_date))
                .limit(10)
            )
            threat_data = [row.threat_analysis for row in threats.all() if row.threat_analysis]
            
            # Analyze competitive threats
            threat_categories = {
                "new_entrants": [],
                "price_competition": [],
                "quality_competition": [],
                "innovation_threats": []
            }
            
            for threat_analysis in threat_data:
                for category, threats_list in threat_analysis.items():
                    if category in threat_categories and isinstance(threats_list, list):
                        threat_categories[category].extend(threats_list)
            
            return {
                "service_position": {
                    "market_rank": service_analysis.market_position_rank,
                    "total_competitors": service_analysis.total_competitors,
                    "market_share": float(service_analysis.market_share_percentage),
                    "competitive_vulnerabilities": service_analysis.competitive_gaps or []
                },
                "threat_analysis": {
                    "new_entrants": {
                        "level": self._assess_threat_level(threat_categories["new_entrants"]),
                        "threats": list(set(threat_categories["new_entrants"]))[:3]
                    },
                    "price_competition": {
                        "level": self._assess_threat_level(threat_categories["price_competition"]),
                        "threats": list(set(threat_categories["price_competition"]))[:3]
                    },
                    "quality_competition": {
                        "level": self._assess_threat_level(threat_categories["quality_competition"]),
                        "threats": list(set(threat_categories["quality_competition"]))[:3]
                    },
                    "innovation_threats": {
                        "level": self._assess_threat_level(threat_categories["innovation_threats"]),
                        "threats": list(set(threat_categories["innovation_threats"]))[:3]
                    }
                },
                "recommendations": service_analysis.competitive_recommendations or []
            }
            
        except Exception as e:
            logger.error(f"Failed to get threat analysis for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get threat analysis", e)

    def _generate_opportunity_recommendations(
        self,
        analysis: CompetitiveAnalysis,
        opportunities: List[Tuple[str, int]],
        gaps: List[Tuple[str, int]]
    ) -> List[str]:
        """Generate opportunity-based recommendations."""
        recommendations = []
        
        # Position-based recommendations
        if analysis.market_position_rank > 5:
            recommendations.append("Focus on improving market visibility and brand recognition")
        
        # Price-based recommendations
        if analysis.price_competitiveness_score < 70:
            recommendations.append("Review pricing strategy to improve competitiveness")
        
        # Quality-based recommendations
        if analysis.quality_score_vs_competitors < 75:
            recommendations.append("Invest in service quality improvements")
        
        # Opportunity-based recommendations
        for opp, count in opportunities[:2]:
            if count >= 2:
                recommendations.append(f"Capitalize on market opportunity: {opp}")
        
        return recommendations

    def _assess_threat_level(self, threats: List[str]) -> str:
        """Assess threat level based on frequency and severity."""
        if len(threats) >= 5:
            return "high"
        elif len(threats) >= 3:
            return "medium"
        elif len(threats) >= 1:
            return "low"
        else:
            return "minimal"
