"""
Unit tests for push notification API endpoints.

This module provides comprehensive unit tests for push notification endpoint classes:
- Device token management endpoints (registration, CRUD operations, user device listing)
- Notification template management endpoints (CRUD operations, versioning, category filtering)
- Notification sending endpoints (single, batch, scheduled notifications)
- User notification preferences endpoints (category-specific settings, DND scheduling)
- Notification analytics endpoints (delivery tracking, queue statistics, performance monitoring)
- Queue management endpoints (processing triggers, status monitoring, cleanup operations)

Implements Task 2.3.2 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from uuid import uuid4
from fastapi.testclient import TestClient
from fastapi import status

from app.api.v1.endpoints.push_notifications import router
from app.models.push_notification_models import (
    DeviceToken, NotificationTemplate, NotificationDelivery,
    NotificationPreference, NotificationQueue,
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)
from app.schemas.push_notification_schemas import (
    Device<PERSON>oken<PERSON>reate, DeviceTokenResponse, NotificationTemplateCreate,
    NotificationSendRequest, NotificationPreferenceUpdate
)


@pytest.fixture
def test_client():
    """Create test client for push notification endpoints."""
    from fastapi import FastAPI
    app = FastAPI()
    app.include_router(router, prefix="/push-notifications")
    return TestClient(app)


@pytest.fixture
def mock_current_user():
    """Mock current user for authentication tests."""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "role": "customer",
        "is_active": True,
        "is_verified": True
    }


@pytest.fixture
def mock_admin_user():
    """Mock admin user for authentication tests."""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "User",
        "role": "admin",
        "is_active": True,
        "is_verified": True
    }


@pytest.fixture
def sample_device_token_response():
    """Sample device token response for testing."""
    return DeviceTokenResponse(
        id=uuid4(),
        user_id=1,
        token="test_fcm_token_123",
        platform=DevicePlatform.ANDROID,
        device_id="device_android_123",
        device_name="Samsung Galaxy S21",
        is_active=True,
        is_validated=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        last_used_at=datetime.now(timezone.utc)
    )


class TestDeviceTokenEndpoints:
    """Test device token management endpoints."""

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_register_device_token_success(self, mock_service_class, mock_session, mock_auth, 
                                         test_client, mock_current_user, sample_device_token_response):
        """Test successful device token registration."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.register_device = AsyncMock(return_value=sample_device_token_response)
        
        # Test data
        device_data = {
            "token": "test_fcm_token_123",
            "platform": "android",
            "device_id": "device_android_123",
            "device_name": "Samsung Galaxy S21"
        }
        
        # Make request
        response = test_client.post("/push-notifications/devices/register", json=device_data)
        
        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["token"] == "test_fcm_token_123"
        assert response_data["platform"] == "android"
        mock_service.register_device.assert_called_once()

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_register_device_token_invalid_data(self, mock_service_class, mock_session, mock_auth, 
                                               test_client, mock_current_user):
        """Test device token registration with invalid data."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.register_device = AsyncMock(side_effect=ValueError("Invalid token format"))
        
        # Test data with invalid token
        device_data = {
            "token": "",  # Empty token
            "platform": "android",
            "device_id": "device_123"
        }
        
        # Make request
        response = test_client.post("/push-notifications/devices/register", json=device_data)
        
        # Assertions
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid device registration data" in response.json()["detail"]

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_get_user_devices_success(self, mock_service_class, mock_session, mock_auth, 
                                    test_client, mock_current_user, sample_device_token_response):
        """Test successful retrieval of user devices."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.get_user_devices = AsyncMock(return_value=[sample_device_token_response])
        
        # Make request
        response = test_client.get("/push-notifications/devices?platform=android&active_only=true")
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["total"] == 1
        assert len(response_data["items"]) == 1
        assert response_data["items"][0]["platform"] == "android"
        mock_service.get_user_devices.assert_called_once()

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_get_device_token_success(self, mock_service_class, mock_session, mock_auth, 
                                    test_client, mock_current_user, sample_device_token_response):
        """Test successful retrieval of specific device token."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        
        # Mock device token with correct user_id
        device_token = DeviceToken(
            id=sample_device_token_response.id,
            user_id=mock_current_user["id"],  # Same as current user
            token=sample_device_token_response.token,
            platform=DevicePlatform.ANDROID,
            device_id=sample_device_token_response.device_id,
            is_active=True
        )
        mock_service.get = AsyncMock(return_value=device_token)
        
        # Make request
        response = test_client.get(f"/push-notifications/devices/{sample_device_token_response.id}")
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["id"] == str(sample_device_token_response.id)
        assert response_data["user_id"] == mock_current_user["id"]
        mock_service.get.assert_called_once()

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_get_device_token_access_denied(self, mock_service_class, mock_session, mock_auth, 
                                          test_client, mock_current_user, sample_device_token_response):
        """Test device token retrieval with access denied."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        
        # Mock device token with different user_id
        device_token = DeviceToken(
            id=sample_device_token_response.id,
            user_id=999,  # Different user
            token=sample_device_token_response.token,
            platform=DevicePlatform.ANDROID,
            device_id=sample_device_token_response.device_id,
            is_active=True
        )
        mock_service.get = AsyncMock(return_value=device_token)
        
        # Make request
        response = test_client.get(f"/push-notifications/devices/{sample_device_token_response.id}")
        
        # Assertions
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "Access denied" in response.json()["detail"]

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_update_device_token_success(self, mock_service_class, mock_session, mock_auth, 
                                       test_client, mock_current_user, sample_device_token_response):
        """Test successful device token update."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        
        # Mock existing device
        existing_device = DeviceToken(
            id=sample_device_token_response.id,
            user_id=mock_current_user["id"],
            token=sample_device_token_response.token,
            platform=DevicePlatform.ANDROID,
            device_id=sample_device_token_response.device_id,
            device_name="Old Device Name",
            is_active=True
        )
        mock_service.get = AsyncMock(return_value=existing_device)
        
        # Mock updated device
        updated_device = DeviceToken(
            id=sample_device_token_response.id,
            user_id=mock_current_user["id"],
            token=sample_device_token_response.token,
            platform=DevicePlatform.ANDROID,
            device_id=sample_device_token_response.device_id,
            device_name="Updated Device Name",
            is_active=True
        )
        mock_service.update = AsyncMock(return_value=updated_device)
        
        # Test data
        update_data = {
            "device_name": "Updated Device Name",
            "app_version": "2.0.0"
        }
        
        # Make request
        response = test_client.put(
            f"/push-notifications/devices/{sample_device_token_response.id}", 
            json=update_data
        )
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["device_name"] == "Updated Device Name"
        mock_service.update.assert_called_once()

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_deactivate_device_token_success(self, mock_service_class, mock_session, mock_auth, 
                                           test_client, mock_current_user, sample_device_token_response):
        """Test successful device token deactivation."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.deactivate_device = AsyncMock(return_value=True)
        
        # Make request
        response = test_client.delete(f"/push-notifications/devices/{sample_device_token_response.id}")
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "deactivated successfully" in response_data["message"]
        mock_service.deactivate_device.assert_called_once_with(
            sample_device_token_response.id, 
            mock_current_user["id"]
        )

    @patch('app.api.v1.endpoints.push_notifications.get_current_user')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.DeviceTokenService')
    def test_deactivate_device_token_not_found(self, mock_service_class, mock_session, mock_auth, 
                                              test_client, mock_current_user, sample_device_token_response):
        """Test device token deactivation with device not found."""
        # Setup mocks
        mock_auth.return_value = mock_current_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.deactivate_device = AsyncMock(return_value=False)
        
        # Make request
        response = test_client.delete(f"/push-notifications/devices/{sample_device_token_response.id}")
        
        # Assertions
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found or access denied" in response.json()["detail"]


class TestNotificationTemplateEndpoints:
    """Test notification template management endpoints."""

    @patch('app.api.v1.endpoints.push_notifications.require_permission')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.NotificationTemplateService')
    def test_create_template_success(self, mock_service_class, mock_session, mock_auth, 
                                   test_client, mock_admin_user):
        """Test successful template creation."""
        # Setup mocks
        mock_auth.return_value = mock_admin_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        
        # Mock created template
        from app.schemas.push_notification_schemas import NotificationTemplateResponse
        mock_template = NotificationTemplateResponse(
            id=uuid4(),
            name="Test Template",
            category=NotificationCategory.SYSTEM,
            title_template="Test {{variable}}",
            body_template="Test message with {{variable}}",
            version=1,
            is_active=True,
            created_by=mock_admin_user["id"],
            usage_count=0,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        mock_service.create_template = AsyncMock(return_value=mock_template)
        
        # Test data
        template_data = {
            "name": "Test Template",
            "category": "system",
            "title_template": "Test {{variable}}",
            "body_template": "Test message with {{variable}}",
            "required_variables": ["variable"]
        }
        
        # Make request
        response = test_client.post("/push-notifications/templates", json=template_data)
        
        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == "Test Template"
        assert response_data["category"] == "system"
        mock_service.create_template.assert_called_once()

    @patch('app.api.v1.endpoints.push_notifications.require_permission')
    @patch('app.api.v1.endpoints.push_notifications.get_async_session')
    @patch('app.api.v1.endpoints.push_notifications.NotificationTemplateService')
    def test_create_template_invalid_syntax(self, mock_service_class, mock_session, mock_auth, 
                                          test_client, mock_admin_user):
        """Test template creation with invalid Jinja2 syntax."""
        # Setup mocks
        mock_auth.return_value = mock_admin_user
        mock_session.return_value = AsyncMock()
        mock_service = mock_service_class.return_value
        mock_service.create_template = AsyncMock(side_effect=ValueError("Invalid template syntax"))
        
        # Test data with invalid syntax
        template_data = {
            "name": "Invalid Template",
            "category": "system",
            "title_template": "Invalid {{variable",  # Missing closing brace
            "body_template": "Test message"
        }
        
        # Make request
        response = test_client.post("/push-notifications/templates", json=template_data)
        
        # Assertions
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid template data" in response.json()["detail"]
