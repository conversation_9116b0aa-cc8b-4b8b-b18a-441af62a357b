# Culture Connect Backend - Hetzner Cloud Outputs
# Output values for Hetzner Cloud infrastructure

# Network Information
output "network_id" {
  description = "ID of the private network"
  value       = hcloud_network.main.id
}

output "network_cidr" {
  description = "CIDR block of the private network"
  value       = hcloud_network.main.ip_range
}

output "subnet_id" {
  description = "ID of the subnet"
  value       = hcloud_network_subnet.main.id
}

output "subnet_cidr" {
  description = "CIDR block of the subnet"
  value       = hcloud_network_subnet.main.ip_range
}

# Load Balancer Information
output "load_balancer_id" {
  description = "ID of the load balancer"
  value       = hcloud_load_balancer.main.id
}

output "load_balancer_public_ip" {
  description = "Public IP address of the load balancer"
  value       = hcloud_load_balancer.main.public_net[0].ipv4[0].ip
}

output "load_balancer_private_ip" {
  description = "Private IP address of the load balancer"
  value       = hcloud_load_balancer_network.main.ip
}

output "floating_ip" {
  description = "Floating IP address for high availability"
  value       = hcloud_floating_ip.main.ip_address
}

# Master Nodes Information
output "master_nodes" {
  description = "Information about master nodes"
  value = {
    for i, server in hcloud_server.masters : server.name => {
      id         = server.id
      public_ip  = server.public_net[0].ipv4[0].ip
      private_ip = hcloud_server_network.masters[i].ip
      status     = server.status
      location   = server.location
    }
  }
}

output "master_public_ips" {
  description = "Public IP addresses of master nodes"
  value       = [for server in hcloud_server.masters : server.public_net[0].ipv4[0].ip]
}

output "master_private_ips" {
  description = "Private IP addresses of master nodes"
  value       = [for network in hcloud_server_network.masters : network.ip]
}

# Worker Nodes Information
output "worker_nodes" {
  description = "Information about worker nodes"
  value = {
    for i, server in hcloud_server.workers : server.name => {
      id         = server.id
      public_ip  = server.public_net[0].ipv4[0].ip
      private_ip = hcloud_server_network.workers[i].ip
      status     = server.status
      location   = server.location
    }
  }
}

output "worker_public_ips" {
  description = "Public IP addresses of worker nodes"
  value       = [for server in hcloud_server.workers : server.public_net[0].ipv4[0].ip]
}

output "worker_private_ips" {
  description = "Private IP addresses of worker nodes"
  value       = [for network in hcloud_server_network.workers : network.ip]
}

# Database Information
output "database_server" {
  description = "Database server information"
  value = {
    id         = hcloud_server.database.id
    name       = hcloud_server.database.name
    private_ip = hcloud_server_network.database.ip
    status     = hcloud_server.database.status
    location   = hcloud_server.database.location
  }
}

output "database_private_ip" {
  description = "Private IP address of the database server"
  value       = hcloud_server_network.database.ip
}

output "database_connection_string" {
  description = "Database connection string"
  value       = "postgresql://${var.postgres_user}:${var.postgres_password}@${hcloud_server_network.database.ip}:5432/${var.postgres_db}"
  sensitive   = true
}

# Redis Information
output "redis_server" {
  description = "Redis server information"
  value = {
    id         = hcloud_server.redis.id
    name       = hcloud_server.redis.name
    private_ip = hcloud_server_network.redis.ip
    status     = hcloud_server.redis.status
    location   = hcloud_server.redis.location
  }
}

output "redis_private_ip" {
  description = "Private IP address of the Redis server"
  value       = hcloud_server_network.redis.ip
}

output "redis_connection_string" {
  description = "Redis connection string"
  value       = "redis://:${var.redis_password}@${hcloud_server_network.redis.ip}:6379/0"
  sensitive   = true
}

# Storage Information
output "database_volume" {
  description = "Database volume information"
  value = {
    id       = hcloud_volume.database_storage.id
    name     = hcloud_volume.database_storage.name
    size     = hcloud_volume.database_storage.size
    location = hcloud_volume.database_storage.location
  }
}

output "redis_volume" {
  description = "Redis volume information"
  value = {
    id       = hcloud_volume.redis_storage.id
    name     = hcloud_volume.redis_storage.name
    size     = hcloud_volume.redis_storage.size
    location = hcloud_volume.redis_storage.location
  }
}

# SSH Access Information
output "ssh_private_key" {
  description = "Private SSH key for server access"
  value       = tls_private_key.ssh_key.private_key_pem
  sensitive   = true
}

output "ssh_public_key" {
  description = "Public SSH key"
  value       = tls_private_key.ssh_key.public_key_openssh
}

# k3s Cluster Information
output "k3s_token" {
  description = "k3s cluster token"
  value       = random_password.k3s_token.result
  sensitive   = true
}

output "k3s_server_url" {
  description = "k3s server URL"
  value       = "https://${hcloud_load_balancer.main.public_net[0].ipv4[0].ip}:6443"
}

output "kubeconfig_command" {
  description = "Command to get kubeconfig"
  value       = "scp root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip}:/etc/rancher/k3s/k3s.yaml ./kubeconfig && sed -i 's/127.0.0.1/${hcloud_load_balancer.main.public_net[0].ipv4[0].ip}/g' ./kubeconfig"
}

# Firewall Information
output "firewall_id" {
  description = "ID of the firewall"
  value       = hcloud_firewall.main.id
}

# Cost Information
output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown"
  value = {
    masters_cost_eur     = "${var.master_count * 8.21}"
    workers_cost_eur     = "${var.worker_count * 16.41}"
    database_cost_eur    = "8.21"
    redis_cost_eur       = "4.51"
    load_balancer_eur    = "5.39"
    storage_cost_eur     = "${(var.database_volume_size + var.redis_volume_size) * 0.10}"
    floating_ip_eur      = "1.19"
    total_eur           = "${var.master_count * 8.21 + var.worker_count * 16.41 + 8.21 + 4.51 + 5.39 + (var.database_volume_size + var.redis_volume_size) * 0.10 + 1.19}"
    total_usd_approx    = "${(var.master_count * 8.21 + var.worker_count * 16.41 + 8.21 + 4.51 + 5.39 + (var.database_volume_size + var.redis_volume_size) * 0.10 + 1.19) * 1.10}"
  }
}

# Deployment Information
output "deployment_info" {
  description = "Deployment information and next steps"
  value = {
    cluster_name     = local.cluster_name
    environment      = var.environment
    location         = var.location
    k3s_version      = var.k3s_version
    master_count     = var.master_count
    worker_count     = var.worker_count
    deployment_date  = timestamp()
    
    next_steps = [
      "1. Wait for servers to initialize (5-10 minutes)",
      "2. Get kubeconfig: ${output.kubeconfig_command.value}",
      "3. Verify cluster: kubectl get nodes",
      "4. Deploy applications: kubectl apply -f ../kubernetes/",
      "5. Configure DNS: Point ${var.subdomain_prefix}.${var.domain_name} to ${hcloud_load_balancer.main.public_net[0].ipv4[0].ip}",
      "6. Set up SSL certificates with cert-manager",
      "7. Configure monitoring and alerting"
    ]
  }
}

# Connection Information
output "connection_info" {
  description = "Connection information for services"
  value = {
    api_url              = "https://${var.subdomain_prefix}.${var.domain_name}"
    load_balancer_ip     = hcloud_load_balancer.main.public_net[0].ipv4[0].ip
    floating_ip          = hcloud_floating_ip.main.ip_address
    ssh_master_1         = "ssh root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip}"
    database_internal    = "${hcloud_server_network.database.ip}:5432"
    redis_internal       = "${hcloud_server_network.redis.ip}:6379"
  }
}

# Security Information
output "security_notes" {
  description = "Important security notes and recommendations"
  value = {
    ssh_access = "SSH access is currently open to 0.0.0.0/0 - restrict allowed_ssh_ips in production"
    k8s_api    = "Kubernetes API is currently open to 0.0.0.0/0 - restrict allowed_k8s_api_ips in production"
    database   = "Database is only accessible from private network"
    redis      = "Redis is only accessible from private network"
    ssl        = "Configure SSL certificates after DNS setup"
    backups    = "Set up automated backups for database and application data"
    monitoring = "Configure monitoring and alerting for production use"
    updates    = "Regularly update servers and k3s cluster"
  }
}

# Backup Information
output "backup_commands" {
  description = "Commands for backup and restore operations"
  value = {
    database_backup  = "ssh root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip} 'kubectl exec -n culture-connect deployment/postgres -- pg_dump -U ${var.postgres_user} ${var.postgres_db} > /backup/db_$(date +%Y%m%d_%H%M%S).sql'"
    database_restore = "ssh root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip} 'kubectl exec -i -n culture-connect deployment/postgres -- psql -U ${var.postgres_user} ${var.postgres_db} < /backup/db_backup.sql'"
    redis_backup     = "ssh root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip} 'kubectl exec -n culture-connect deployment/redis -- redis-cli --rdb /backup/redis_$(date +%Y%m%d_%H%M%S).rdb'"
    cluster_backup   = "ssh root@${hcloud_server.masters[0].public_net[0].ipv4[0].ip} 'k3s etcd-snapshot save'"
  }
}
