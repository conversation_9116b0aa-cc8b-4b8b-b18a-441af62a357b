"""
Enhanced permission system and RBAC middleware for Culture Connect Backend API.

This module provides comprehensive permission management including:
- Production-ready permission decorators and middleware
- Role-based access control with resource-level permissions
- Audit logging for all access control decisions
- Integration with existing JWT authentication system

Implements Task 2.2.3 requirements for complete role-based access control
with production-grade middleware and security features.
"""

import logging
from functools import wraps
from typing import Optional, List, Callable, Any, Dict
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.rbac_service import RBACService
from app.core.deps import get_current_user
from app.schemas.auth import UserResponse
from app.core.security import Permission, UserRole
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


def require_permission(
    permission: str,
    resource_type: Optional[str] = None,
    resource_id_param: Optional[str] = None,
    allow_resource_owner: bool = False
):
    """
    Enhanced permission decorator with resource-level access control and audit logging.

    Args:
        permission: Required permission
        resource_type: Resource type for resource-specific permissions
        resource_id_param: Parameter name containing resource ID
        allow_resource_owner: Allow resource owner access regardless of permission

    Returns:
        Decorator function that checks permission with comprehensive audit logging
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract dependencies from kwargs
            current_user = None
            db = None
            request = None

            for key, value in kwargs.items():
                if isinstance(value, UserResponse):
                    current_user = value
                elif isinstance(value, AsyncSession):
                    db = value
                elif isinstance(value, Request):
                    request = value

            # If dependencies not found in kwargs, try to get them
            if not current_user:
                # This should not happen in normal flow, but handle gracefully
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )

            if not db:
                # Get database session if not provided
                async for session in get_async_session():
                    db = session
                    break

            # Extract resource ID if specified
            resource_id = None
            if resource_id_param and resource_id_param in kwargs:
                resource_id = str(kwargs[resource_id_param])

            # Check resource ownership if allowed
            if allow_resource_owner and resource_id:
                if await _check_resource_ownership(
                    current_user.id, resource_type, resource_id, db
                ):
                    logger.info(
                        f"Access granted via resource ownership: {current_user.email}",
                        extra={
                            "correlation_id": correlation_id.get(''),
                            "user_id": current_user.id,
                            "resource_type": resource_type,
                            "resource_id": resource_id
                        }
                    )
                    return await func(*args, **kwargs)

            # Initialize RBAC service
            rbac_service = RBACService(db)

            # Get request details for audit logging
            endpoint = getattr(request, 'url', {}).path if request else "unknown"
            method = getattr(request, 'method', 'UNKNOWN') if request else "UNKNOWN"
            ip_address = None
            user_agent = None

            if request:
                ip_address = request.client.host if hasattr(request, 'client') else None
                user_agent = request.headers.get('user-agent')

            # Check permission with comprehensive audit logging
            permission_result = await rbac_service.check_permission(
                user_id=str(current_user.id),
                user_role=current_user.role,
                permission=permission,
                resource_type=resource_type,
                resource_id=resource_id,
                endpoint=endpoint,
                method=method,
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Raise exception if permission denied
            if not permission_result.has_permission:
                logger.warning(
                    f"Permission denied: {current_user.email} -> {permission}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": current_user.id,
                        "user_role": current_user.role,
                        "permission": permission,
                        "resource_type": resource_type,
                        "resource_id": resource_id,
                        "reason": permission_result.reason
                    }
                )

                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission}' required. {permission_result.reason}"
                )

            # Permission granted, proceed with function execution
            logger.info(
                f"Permission granted: {current_user.email} -> {permission}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": current_user.id,
                    "user_role": current_user.role,
                    "permission": permission,
                    "resource_type": resource_type,
                    "resource_id": resource_id
                }
            )

            return await func(*args, **kwargs)

        return wrapper
    return decorator


def require_role(allowed_roles: List[str]):
    """
    Role-based access control decorator.

    Args:
        allowed_roles: List of allowed roles

    Returns:
        Decorator function that checks user role
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, UserResponse):
                    current_user = value
                    break

            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )

            # Check if user role is allowed
            if current_user.role not in allowed_roles:
                logger.warning(
                    f"Role access denied: {current_user.email} ({current_user.role}) not in {allowed_roles}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": current_user.id,
                        "user_role": current_user.role,
                        "allowed_roles": allowed_roles
                    }
                )

                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role '{current_user.role}' not authorized. Required: {', '.join(allowed_roles)}"
                )

            logger.info(
                f"Role access granted: {current_user.email} ({current_user.role})",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": current_user.id,
                    "user_role": current_user.role,
                    "allowed_roles": allowed_roles
                }
            )

            return await func(*args, **kwargs)

        return wrapper
    return decorator


def require_admin():
    """
    Decorator for admin-only endpoints.

    Returns:
        Decorator function that requires admin role
    """
    return require_role([UserRole.ADMIN, UserRole.SUPER_ADMIN])


def require_vendor():
    """
    Decorator for vendor endpoints.

    Returns:
        Decorator function that requires vendor role or higher
    """
    return require_role([UserRole.VENDOR, UserRole.ADMIN, UserRole.SUPER_ADMIN])


def require_customer():
    """
    Decorator for customer endpoints.

    Returns:
        Decorator function that requires customer role or higher
    """
    return require_role([UserRole.CUSTOMER, UserRole.VENDOR, UserRole.ADMIN, UserRole.SUPER_ADMIN])


# Enhanced dependency functions for FastAPI
async def get_current_admin_user(
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    """
    Dependency to get current admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current admin user

    Raises:
        HTTPException: If user is not admin
    """
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        logger.warning(
            f"Admin access denied: {current_user.email} ({current_user.role})",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "user_role": current_user.role
            }
        )

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )

    return current_user


async def get_current_vendor_user(
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    """
    Dependency to get current vendor user.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current vendor user

    Raises:
        HTTPException: If user is not vendor or higher
    """
    if current_user.role not in [UserRole.VENDOR, UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        logger.warning(
            f"Vendor access denied: {current_user.email} ({current_user.role})",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "user_role": current_user.role
            }
        )

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vendor privileges required"
        )

    return current_user


async def check_permission_dependency(
    permission: str,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None
):
    """
    Dependency factory for permission checking.

    Args:
        permission: Required permission
        resource_type: Resource type for resource-specific permissions
        resource_id: Resource ID for resource-specific permissions

    Returns:
        Dependency function that checks permission
    """
    async def permission_dependency(
        current_user: UserResponse = Depends(get_current_user),
        db: AsyncSession = Depends(get_async_session)
    ) -> UserResponse:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Check permission
        permission_result = await rbac_service.check_permission(
            user_id=str(current_user.id),
            user_role=current_user.role,
            permission=permission,
            resource_type=resource_type,
            resource_id=resource_id
        )

        # Raise exception if permission denied
        if not permission_result.has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )

        return current_user

    return permission_dependency


async def _check_resource_ownership(
    user_id: str,
    resource_type: Optional[str],
    resource_id: str,
    db: AsyncSession
) -> bool:
    """
    Check if user owns the specified resource.

    Args:
        user_id: User ID to check ownership for
        resource_type: Type of resource
        resource_id: Resource ID
        db: Database session

    Returns:
        bool: True if user owns the resource, False otherwise
    """
    try:
        # This is a placeholder implementation
        # In a real application, you would check ownership based on resource type
        # For example:
        # - For user resources: check if user_id matches
        # - For vendor resources: check if user owns the vendor
        # - For service resources: check if user owns the service

        if resource_type == "user":
            return user_id == resource_id

        # Add more resource ownership checks as needed
        # This would typically involve database queries to check ownership

        return False

    except Exception as e:
        logger.error(
            f"Failed to check resource ownership: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": user_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "error": str(e)
            }
        )
        return False
