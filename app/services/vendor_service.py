"""
Vendor Service for Culture Connect Backend API.

This module provides comprehensive vendor management functionality including:
- Multi-step vendor registration workflow with business logic validation
- Business type selection and configuration management
- Document upload coordination with existing file handling infrastructure
- Registration progress tracking with onboarding step management
- Seamless integration with existing authentication, RBAC, and user management systems

Implements Task 3.1.1 Phase 1 requirements for vendor service layer with
production-grade functionality and seamless business logic integration.
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.vendor_repository import VendorRepository
from app.models.vendor import (
    Vendor, VendorProfile, VendorDocument,
    VendorType, VerificationStatus, MarketplaceStatus,
    DocumentType, DocumentStatus
)
from app.schemas.vendor import (
    VendorCreate, VendorUpdate, VendorResponse,
    VendorProfileCreate, VendorProfileUpdate, VendorProfileResponse,
    VendorDocumentCreate, VendorDocumentUpdate, VendorDocumentResponse,
    VendorRegistrationRequest, VendorRegistrationResponse,
    OnboardingStepUpdate, OnboardingStatusResponse
)
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class VendorService(BaseService[Vendor, VendorRepository]):
    """
    Vendor service for comprehensive vendor management operations.

    Provides business logic for:
    - Multi-step vendor registration with validation
    - Business type configuration and validation
    - Document upload and verification coordination
    - Onboarding progress tracking and management
    - Vendor profile management with marketplace integration
    """

    def __init__(self, db: AsyncSession):
        """Initialize vendor service."""
        super().__init__(VendorRepository, Vendor, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate vendor creation data.

        Args:
            data: Vendor creation data

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['user_id', 'business_name', 'business_type']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError(f"Missing required field: {field}")

        # Validate business type
        if data['business_type'] not in [bt.value for bt in VendorType]:
            raise ValidationError(f"Invalid business type: {data['business_type']}")

        # Convert business_type string to enum if needed
        if isinstance(data['business_type'], str):
            data['business_type'] = VendorType(data['business_type'])

        # Set default values for new vendors
        data.setdefault('verification_status', VerificationStatus.PENDING)
        data.setdefault('marketplace_status', MarketplaceStatus.INACTIVE)
        data.setdefault('onboarding_completed', False)
        data.setdefault('onboarding_step', 1)
        data.setdefault('commission_rate', 0.15)  # 15% default commission

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate vendor update data.

        Args:
            data: Update data
            existing_id: Existing vendor ID

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Validate business type if provided
        if 'business_type' in data and data['business_type']:
            if data['business_type'] not in [bt.value for bt in VendorType]:
                raise ValidationError(f"Invalid business type: {data['business_type']}")

            # Convert to enum if needed
            if isinstance(data['business_type'], str):
                data['business_type'] = VendorType(data['business_type'])

        # Validate status fields if provided
        if 'verification_status' in data and data['verification_status']:
            if isinstance(data['verification_status'], str):
                data['verification_status'] = VerificationStatus(data['verification_status'])

        if 'marketplace_status' in data and data['marketplace_status']:
            if isinstance(data['marketplace_status'], str):
                data['marketplace_status'] = MarketplaceStatus(data['marketplace_status'])

        # Validate onboarding step range
        if 'onboarding_step' in data:
            step = data['onboarding_step']
            if not isinstance(step, int) or step < 1 or step > 6:
                raise ValidationError("Onboarding step must be between 1 and 6")

        # Validate commission rate
        if 'commission_rate' in data:
            rate = data['commission_rate']
            if not isinstance(rate, (int, float)) or rate < 0.0 or rate > 1.0:
                raise ValidationError("Commission rate must be between 0.0 and 1.0")

        return data

    async def register_vendor(
        self,
        user_id: int,
        registration_data: VendorRegistrationRequest
    ) -> VendorRegistrationResponse:
        """
        Register a new vendor with multi-step onboarding workflow.

        Args:
            user_id: User ID for vendor registration
            registration_data: Vendor registration data

        Returns:
            VendorRegistrationResponse: Registration result with onboarding steps

        Raises:
            ConflictError: If vendor already exists for user
            ValidationError: If registration data is invalid
            ServiceError: If registration fails
        """
        correlation = self.log_operation_start("register_vendor", user_id=user_id)

        try:
            # Check if vendor already exists for user
            existing_vendor = await self.repository.get_by_user_id(user_id)
            if existing_vendor:
                raise ConflictError(f"Vendor already exists for user {user_id}")

            # Prepare vendor data
            vendor_data = {
                'user_id': user_id,
                'business_name': registration_data.business_name,
                'business_type': registration_data.business_type,
                'business_registration_number': registration_data.business_registration_number,
                'tax_id': registration_data.tax_id,
                'verification_status': VerificationStatus.PENDING,
                'marketplace_status': MarketplaceStatus.INACTIVE,
                'onboarding_completed': False,
                'onboarding_step': 1
            }

            # Prepare profile data if provided
            profile_data = None
            if hasattr(registration_data, 'profile') and registration_data.profile:
                profile_data = registration_data.profile.dict(exclude_unset=True)

            # Create vendor with profile
            vendor = await self.repository.create_vendor(user_id, vendor_data, profile_data)

            # Generate onboarding steps
            onboarding_steps = self._generate_onboarding_steps(vendor.business_type)

            # Determine if verification is required
            verification_required = self._requires_verification(vendor.business_type)

            self.log_operation_success(
                correlation,
                f"Vendor registered successfully for user {user_id}"
            )

            return VendorRegistrationResponse(
                vendor=VendorResponse.model_validate(vendor),
                onboarding_steps=onboarding_steps,
                verification_required=verification_required,
                message="Vendor registration successful. Please complete the onboarding process."
            )

        except Exception as e:
            await self.handle_service_error(e, "register_vendor", {"user_id": user_id})

    def _generate_onboarding_steps(self, business_type: VendorType) -> List[Dict[str, Any]]:
        """
        Generate onboarding steps based on business type.

        Args:
            business_type: Type of business

        Returns:
            List[Dict[str, Any]]: Onboarding steps
        """
        base_steps = [
            {
                "step": 1,
                "title": "Business Information",
                "description": "Complete your business profile and contact information",
                "required": True,
                "completed": False
            },
            {
                "step": 2,
                "title": "Business Verification",
                "description": "Upload required business documents for verification",
                "required": True,
                "completed": False
            },
            {
                "step": 3,
                "title": "Service Details",
                "description": "Add your services and pricing information",
                "required": True,
                "completed": False
            },
            {
                "step": 4,
                "title": "Media Upload",
                "description": "Upload photos and videos of your business",
                "required": False,
                "completed": False
            },
            {
                "step": 5,
                "title": "Payment Setup",
                "description": "Configure payment methods and banking details",
                "required": True,
                "completed": False
            },
            {
                "step": 6,
                "title": "Review & Launch",
                "description": "Review your profile and launch your business",
                "required": True,
                "completed": False
            }
        ]

        # Customize steps based on business type
        if business_type in [VendorType.GUIDE, VendorType.ACTIVITY_PROVIDER]:
            base_steps[1]["description"] += " (Guide license required)"
        elif business_type == VendorType.RESTAURANT:
            base_steps[1]["description"] += " (Food service license required)"
        elif business_type == VendorType.HOTEL:
            base_steps[1]["description"] += " (Hospitality license required)"

        return base_steps

    def _requires_verification(self, business_type: VendorType) -> bool:
        """
        Determine if business type requires verification.

        Args:
            business_type: Type of business

        Returns:
            bool: True if verification required
        """
        # All business types require verification for marketplace safety
        return True

    async def get_vendor_by_user_id(self, user_id: int) -> Optional[Vendor]:
        """
        Get vendor by user ID.

        Args:
            user_id: User ID

        Returns:
            Optional[Vendor]: Vendor if found, None otherwise

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("get_vendor_by_user_id", user_id=user_id)

        try:
            vendor = await self.repository.get_by_user_id(user_id)

            if vendor:
                self.log_operation_success(correlation, f"Found vendor for user {user_id}")
            else:
                self.log_operation_success(correlation, f"No vendor found for user {user_id}")

            return vendor

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_by_user_id", {"user_id": user_id})

    async def update_vendor_profile(
        self,
        vendor_id: int,
        profile_data: VendorProfileUpdate
    ) -> VendorProfile:
        """
        Update vendor profile information.

        Args:
            vendor_id: Vendor ID
            profile_data: Profile update data

        Returns:
            VendorProfile: Updated profile

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If profile data is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("update_vendor_profile", vendor_id=vendor_id)

        try:
            # Validate vendor exists
            await self.get_by_id_or_raise(vendor_id)

            # Update profile
            profile_dict = profile_data.model_dump(exclude_unset=True)
            updated_profile = await self.repository.update_vendor_profile(vendor_id, profile_dict)

            if not updated_profile:
                raise NotFoundError("VendorProfile", vendor_id)

            self.log_operation_success(correlation, f"Updated vendor profile for vendor {vendor_id}")
            return updated_profile

        except Exception as e:
            await self.handle_service_error(e, "update_vendor_profile", {"vendor_id": vendor_id})

    async def update_onboarding_step(
        self,
        vendor_id: int,
        step_data: OnboardingStepUpdate
    ) -> OnboardingStatusResponse:
        """
        Update vendor onboarding step and progress.

        Args:
            vendor_id: Vendor ID
            step_data: Onboarding step update data

        Returns:
            OnboardingStatusResponse: Updated onboarding status

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If step data is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("update_onboarding_step", vendor_id=vendor_id)

        try:
            # Validate vendor exists
            vendor = await self.get_by_id_or_raise(vendor_id)

            # Validate step progression
            current_step = vendor.onboarding_step
            new_step = step_data.step

            if new_step < current_step:
                raise ValidationError("Cannot move to a previous onboarding step")

            if new_step > current_step + 1:
                raise ValidationError("Cannot skip onboarding steps")

            # Update vendor onboarding step
            update_data = {
                'onboarding_step': new_step,
                'onboarding_completed': new_step >= 6
            }

            updated_vendor = await self.update(vendor_id, update_data)

            # Generate updated onboarding status
            onboarding_steps = self._generate_onboarding_steps(updated_vendor.business_type)

            # Mark completed steps
            for step in onboarding_steps:
                if step["step"] <= new_step:
                    step["completed"] = True

            completion_percentage = (new_step / 6) * 100

            self.log_operation_success(
                correlation,
                f"Updated onboarding step to {new_step} for vendor {vendor_id}"
            )

            return OnboardingStatusResponse(
                current_step=new_step,
                total_steps=6,
                completed=updated_vendor.onboarding_completed,
                completion_percentage=completion_percentage,
                steps=onboarding_steps,
                next_step_title=onboarding_steps[new_step]["title"] if new_step < 6 else None,
                next_step_description=onboarding_steps[new_step]["description"] if new_step < 6 else None
            )

        except Exception as e:
            await self.handle_service_error(e, "update_onboarding_step", {"vendor_id": vendor_id})

    async def upload_vendor_document(
        self,
        vendor_id: int,
        document_data: VendorDocumentCreate
    ) -> VendorDocument:
        """
        Upload vendor verification document.

        Args:
            vendor_id: Vendor ID
            document_data: Document upload data

        Returns:
            VendorDocument: Created document record

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If document data is invalid
            ServiceError: If upload fails
        """
        correlation = self.log_operation_start("upload_vendor_document", vendor_id=vendor_id)

        try:
            # Validate vendor exists
            await self.get_by_id_or_raise(vendor_id)

            # Prepare document data
            doc_dict = document_data.model_dump()
            doc_dict['vendor_id'] = vendor_id
            doc_dict['status'] = DocumentStatus.PENDING
            doc_dict['uploaded_at'] = datetime.now(datetime.timezone.utc)

            # Create document record
            document = await self.repository.create_vendor_document(doc_dict)

            self.log_operation_success(
                correlation,
                f"Uploaded document {document.document_type.value} for vendor {vendor_id}"
            )

            return document

        except Exception as e:
            await self.handle_service_error(e, "upload_vendor_document", {"vendor_id": vendor_id})

    async def get_vendor_documents(
        self,
        vendor_id: int,
        document_type: Optional[DocumentType] = None
    ) -> List[VendorDocument]:
        """
        Get vendor documents with optional filtering.

        Args:
            vendor_id: Vendor ID
            document_type: Optional document type filter

        Returns:
            List[VendorDocument]: List of vendor documents

        Raises:
            NotFoundError: If vendor not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("get_vendor_documents", vendor_id=vendor_id)

        try:
            # Validate vendor exists
            await self.get_by_id_or_raise(vendor_id)

            # Get documents
            documents = await self.repository.get_vendor_documents(vendor_id, document_type)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(documents)} documents for vendor {vendor_id}"
            )

            return documents

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_documents", {"vendor_id": vendor_id})

    async def get_public_vendor_profile(self, vendor_id: int) -> Optional[Dict[str, Any]]:
        """
        Get public vendor profile for customer viewing.

        Args:
            vendor_id: Vendor ID

        Returns:
            Optional[Dict[str, Any]]: Public vendor profile data

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("get_public_vendor_profile", vendor_id=vendor_id)

        try:
            vendor = await self.repository.get_vendor_with_profile(vendor_id)

            if not vendor:
                self.log_operation_success(correlation, f"Vendor {vendor_id} not found")
                return None

            # Only return public information for active, verified vendors
            if (vendor.marketplace_status != MarketplaceStatus.ACTIVE or
                vendor.verification_status != VerificationStatus.VERIFIED):
                self.log_operation_success(correlation, f"Vendor {vendor_id} not publicly available")
                return None

            # Build public profile
            public_profile = {
                'id': vendor.id,
                'business_name': vendor.business_name,
                'business_type': vendor.business_type.value,
                'average_rating': vendor.average_rating,
                'total_reviews': vendor.total_reviews,
                'response_rate': vendor.response_rate,
                'response_time_hours': vendor.response_time_hours,
                'verified': vendor.verification_status == VerificationStatus.VERIFIED,
                'profile': None
            }

            # Add profile information if available
            if vendor.profile:
                public_profile['profile'] = {
                    'description': vendor.profile.description,
                    'short_description': vendor.profile.short_description,
                    'tagline': vendor.profile.tagline,
                    'city': vendor.profile.city,
                    'state': vendor.profile.state,
                    'country': vendor.profile.country,
                    'website_url': vendor.profile.website_url,
                    'logo_url': vendor.profile.logo_url,
                    'cover_image_url': vendor.profile.cover_image_url,
                    'gallery_images': vendor.profile.gallery_images,
                    'languages_spoken': vendor.profile.languages_spoken,
                    'years_in_business': vendor.profile.years_in_business
                }

            self.log_operation_success(correlation, f"Retrieved public profile for vendor {vendor_id}")
            return public_profile

        except Exception as e:
            await self.handle_service_error(e, "get_public_vendor_profile", {"vendor_id": vendor_id})
