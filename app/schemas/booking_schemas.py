"""
Booking schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic V2 schemas for booking workflow validation
including booking creation, updates, status management, and communication.

Implements Task 4.1.1 requirements for booking workflow implementation with:
- Complete booking lifecycle validation
- Vendor approval system schemas
- Customer notification workflow schemas
- Booking communication validation
- Real-time availability checking schemas
- Audit trail and status history schemas

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, validator, field_validator
from pydantic.types import UUID4

from app.models.booking import (
    BookingStatus, VendorResponseType, BookingPriority,
    CommunicationType, ModificationType
)


# Base schemas with common configuration
class BookingBaseSchema(BaseModel):
    """Base schema for booking-related models with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-24T12:00:00Z",
                "updated_at": "2025-01-24T12:00:00Z"
            }
        }
    )


# Booking creation and update schemas
class BookingCreateSchema(BookingBaseSchema):
    """Schema for creating a new booking."""

    service_id: int = Field(
        ...,
        description="ID of the service being booked",
        gt=0
    )

    availability_id: Optional[int] = Field(
        None,
        description="ID of the specific availability slot (optional for flexible bookings)",
        gt=0
    )

    booking_date: date = Field(
        ...,
        description="Date of the service"
    )

    booking_time: Optional[time] = Field(
        None,
        description="Time of the service (null for all-day services)"
    )

    duration_hours: Optional[Decimal] = Field(
        None,
        description="Service duration in hours",
        ge=Decimal("0.5"),
        le=Decimal("24.0")
    )

    participant_count: int = Field(
        1,
        description="Number of participants",
        ge=1,
        le=100
    )

    special_requirements: Optional[str] = Field(
        None,
        description="Customer special requirements",
        max_length=2000
    )

    customer_notes: Optional[str] = Field(
        None,
        description="Customer notes and preferences",
        max_length=1000
    )

    accessibility_requirements: Optional[Dict[str, Any]] = Field(
        None,
        description="Accessibility requirements and accommodations"
    )

    priority: BookingPriority = Field(
        BookingPriority.NORMAL,
        description="Booking priority level"
    )

    booking_source: str = Field(
        "web",
        description="Source of booking (web, mobile, api)",
        max_length=50
    )

    booking_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional booking metadata"
    )

    @field_validator('booking_date')
    @classmethod
    def validate_booking_date(cls, v: date) -> date:
        """Validate booking date is not in the past."""
        from datetime import date as date_class
        if v < date_class.today():
            raise ValueError("Booking date cannot be in the past")
        return v

    @field_validator('booking_time')
    @classmethod
    def validate_booking_time(cls, v: Optional[time]) -> Optional[time]:
        """Validate booking time format."""
        if v is not None:
            # Ensure time is within business hours (6 AM to 11 PM)
            if v.hour < 6 or v.hour >= 23:
                raise ValueError("Booking time must be between 6:00 AM and 11:00 PM")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "service_id": 123,
                "availability_id": 456,
                "booking_date": "2025-02-15",
                "booking_time": "14:30:00",
                "duration_hours": "2.5",
                "participant_count": 2,
                "special_requirements": "Vegetarian meal required",
                "customer_notes": "Celebrating anniversary",
                "accessibility_requirements": {
                    "wheelchair_accessible": True,
                    "hearing_assistance": False
                },
                "priority": "normal",
                "booking_source": "web"
            }
        }
    )


class BookingUpdateSchema(BookingBaseSchema):
    """Schema for updating an existing booking."""

    booking_date: Optional[date] = Field(
        None,
        description="Updated booking date"
    )

    booking_time: Optional[time] = Field(
        None,
        description="Updated booking time"
    )

    duration_hours: Optional[Decimal] = Field(
        None,
        description="Updated service duration in hours",
        ge=Decimal("0.5"),
        le=Decimal("24.0")
    )

    participant_count: Optional[int] = Field(
        None,
        description="Updated number of participants",
        ge=1,
        le=100
    )

    special_requirements: Optional[str] = Field(
        None,
        description="Updated special requirements",
        max_length=2000
    )

    customer_notes: Optional[str] = Field(
        None,
        description="Updated customer notes",
        max_length=1000
    )

    accessibility_requirements: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated accessibility requirements"
    )

    priority: Optional[BookingPriority] = Field(
        None,
        description="Updated booking priority"
    )

    @field_validator('booking_date')
    @classmethod
    def validate_booking_date(cls, v: Optional[date]) -> Optional[date]:
        """Validate booking date is not in the past."""
        if v is not None:
            from datetime import date as date_class
            if v < date_class.today():
                raise ValueError("Booking date cannot be in the past")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "booking_date": "2025-02-16",
                "participant_count": 3,
                "special_requirements": "Updated requirements",
                "priority": "high"
            }
        }
    )


class BookingStatusUpdateSchema(BookingBaseSchema):
    """Schema for updating booking status."""

    status: BookingStatus = Field(
        ...,
        description="New booking status"
    )

    change_reason: Optional[str] = Field(
        None,
        description="Reason for status change",
        max_length=255
    )

    change_notes: Optional[str] = Field(
        None,
        description="Additional notes about the change",
        max_length=1000
    )

    notify_customer: bool = Field(
        True,
        description="Whether to notify the customer"
    )

    notify_vendor: bool = Field(
        True,
        description="Whether to notify the vendor"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "status": "confirmed",
                "change_reason": "Vendor approved booking",
                "change_notes": "All requirements can be accommodated",
                "notify_customer": True,
                "notify_vendor": False
            }
        }
    )


class VendorResponseSchema(BookingBaseSchema):
    """Schema for vendor response to booking request."""

    response_type: VendorResponseType = Field(
        ...,
        description="Type of vendor response"
    )

    vendor_notes: Optional[str] = Field(
        None,
        description="Vendor notes and comments",
        max_length=2000
    )

    counter_offer_price: Optional[Decimal] = Field(
        None,
        description="Counter offer price (if applicable)",
        ge=Decimal("0.00")
    )

    alternative_dates: Optional[List[date]] = Field(
        None,
        description="Alternative dates suggested by vendor"
    )

    alternative_times: Optional[List[time]] = Field(
        None,
        description="Alternative times suggested by vendor"
    )

    modifications_required: Optional[Dict[str, Any]] = Field(
        None,
        description="Required modifications to the booking"
    )

    @field_validator('alternative_dates')
    @classmethod
    def validate_alternative_dates(cls, v: Optional[List[date]]) -> Optional[List[date]]:
        """Validate alternative dates are not in the past."""
        if v is not None:
            from datetime import date as date_class
            today = date_class.today()
            for alt_date in v:
                if alt_date < today:
                    raise ValueError("Alternative dates cannot be in the past")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "response_type": "approved",
                "vendor_notes": "We can accommodate all your requirements",
                "counter_offer_price": None,
                "alternative_dates": None,
                "alternative_times": None
            }
        }
    )


# Response schemas
class BookingResponseSchema(BookingBaseSchema):
    """Schema for booking response data."""

    id: int = Field(..., description="Booking ID")
    uuid: UUID4 = Field(..., description="Booking UUID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Core booking information
    customer_id: int = Field(..., description="Customer ID")
    vendor_id: int = Field(..., description="Vendor ID")
    service_id: int = Field(..., description="Service ID")
    availability_id: Optional[int] = Field(None, description="Availability slot ID")

    # Booking reference and tracking
    booking_reference: str = Field(..., description="Unique booking reference")
    external_reference: Optional[str] = Field(None, description="External reference")

    # Booking details
    booking_date: date = Field(..., description="Service date")
    booking_time: Optional[time] = Field(None, description="Service time")
    duration_hours: Optional[Decimal] = Field(None, description="Duration in hours")
    participant_count: int = Field(..., description="Number of participants")

    # Status and workflow
    status: BookingStatus = Field(..., description="Current booking status")
    priority: BookingPriority = Field(..., description="Booking priority")

    # Vendor response
    vendor_response_type: Optional[VendorResponseType] = Field(None, description="Vendor response type")
    vendor_response_at: Optional[datetime] = Field(None, description="Vendor response timestamp")
    vendor_response_deadline: Optional[datetime] = Field(None, description="Response deadline")
    vendor_notes: Optional[str] = Field(None, description="Vendor notes")

    # Customer requirements
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    customer_notes: Optional[str] = Field(None, description="Customer notes")
    accessibility_requirements: Optional[Dict[str, Any]] = Field(None, description="Accessibility requirements")

    # Pricing
    base_price: Decimal = Field(..., description="Base service price")
    additional_fees: Decimal = Field(..., description="Additional fees")
    discount_amount: Decimal = Field(..., description="Discount amount")
    total_amount: Decimal = Field(..., description="Total booking amount")
    currency: str = Field(..., description="Currency code")

    # Payment tracking
    payment_status: str = Field(..., description="Payment status")
    payment_method: Optional[str] = Field(None, description="Payment method")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    paid_at: Optional[datetime] = Field(None, description="Payment timestamp")

    # Service delivery
    service_start_time: Optional[datetime] = Field(None, description="Service start time")
    service_end_time: Optional[datetime] = Field(None, description="Service end time")
    completion_confirmed_by_customer: bool = Field(..., description="Customer completion confirmation")
    completion_confirmed_by_vendor: bool = Field(..., description="Vendor completion confirmation")

    # Communication
    last_communication_at: Optional[datetime] = Field(None, description="Last communication")
    unread_messages_customer: int = Field(..., description="Unread messages for customer")
    unread_messages_vendor: int = Field(..., description="Unread messages for vendor")

    # Cancellation
    cancelled_at: Optional[datetime] = Field(None, description="Cancellation timestamp")
    cancelled_by: Optional[int] = Field(None, description="User who cancelled")
    cancellation_reason: Optional[str] = Field(None, description="Cancellation reason")
    refund_amount: Decimal = Field(..., description="Refund amount")

    # Reviews
    customer_rating: Optional[Decimal] = Field(None, description="Customer rating")
    customer_review: Optional[str] = Field(None, description="Customer review")
    vendor_rating: Optional[Decimal] = Field(None, description="Vendor rating")
    vendor_review: Optional[str] = Field(None, description="Vendor review")
    reviewed_at: Optional[datetime] = Field(None, description="Review timestamp")

    # Metadata
    booking_source: str = Field(..., description="Booking source")
    booking_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 123,
                "uuid": "550e8400-e29b-41d4-a716-************",
                "booking_reference": "BK-2025-001234",
                "status": "confirmed",
                "booking_date": "2025-02-15",
                "total_amount": "150.00",
                "payment_status": "paid"
            }
        }
    )


class BookingListResponseSchema(BookingBaseSchema):
    """Schema for paginated booking list response."""

    bookings: List[BookingResponseSchema] = Field(
        ...,
        description="List of bookings"
    )

    total: int = Field(
        ...,
        description="Total number of bookings",
        ge=0
    )

    page: int = Field(
        ...,
        description="Current page number",
        ge=1
    )

    per_page: int = Field(
        ...,
        description="Number of items per page",
        ge=1,
        le=100
    )

    pages: int = Field(
        ...,
        description="Total number of pages",
        ge=1
    )

    has_next: bool = Field(
        ...,
        description="Whether there is a next page"
    )

    has_prev: bool = Field(
        ...,
        description="Whether there is a previous page"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "bookings": [],
                "total": 25,
                "page": 1,
                "per_page": 10,
                "pages": 3,
                "has_next": True,
                "has_prev": False
            }
        }
    )
