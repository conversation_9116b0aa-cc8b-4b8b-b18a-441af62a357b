"""
Tests for Multi-Layer Cache Service.

This module provides comprehensive tests for the multi-layer caching system including:
- MultiLayerCacheService functionality testing
- Cache performance and hit rate validation
- Cache warming and invalidation testing
- Integration with performance monitoring
- Memory cache optimization testing

Implements Phase 7.3.2 testing requirements with >80% coverage validation.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal

from app.services.multi_layer_cache_service import (
    MultiLayerCacheService,
    MemoryCache,
    CacheWarmingConfig,
    CacheLayerMetrics
)
from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.models.analytics_models import PerformanceMetricType, AnalyticsTimeframe


class TestMemoryCache:
    """Test cases for MemoryCache component."""

    def test_memory_cache_basic_operations(self):
        """Test basic get/set/delete operations."""
        cache = MemoryCache(max_size=10, max_memory_mb=1.0)
        
        # Test set and get
        assert cache.set("key1", "value1") is True
        assert cache.get("key1") == "value1"
        
        # Test non-existent key
        assert cache.get("non_existent") is None
        
        # Test delete
        assert cache.delete("key1") is True
        assert cache.get("key1") is None
        assert cache.delete("non_existent") is False

    def test_memory_cache_lru_eviction(self):
        """Test LRU eviction when cache size limit is reached."""
        cache = MemoryCache(max_size=3, max_memory_mb=10.0)
        
        # Fill cache to capacity
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.set("key3", "value3")
        
        # Access key1 to make it recently used
        cache.get("key1")
        
        # Add another item, should evict key2 (least recently used)
        cache.set("key4", "value4")
        
        assert cache.get("key1") == "value1"  # Should still exist
        assert cache.get("key2") is None      # Should be evicted
        assert cache.get("key3") == "value3"  # Should still exist
        assert cache.get("key4") == "value4"  # Should exist

    def test_memory_cache_clear(self):
        """Test cache clear functionality."""
        cache = MemoryCache(max_size=10, max_memory_mb=1.0)
        
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        
        assert cache.get("key1") == "value1"
        assert cache.get("key2") == "value2"
        
        cache.clear()
        
        assert cache.get("key1") is None
        assert cache.get("key2") is None

    def test_memory_cache_metrics(self):
        """Test cache metrics collection."""
        cache = MemoryCache(max_size=10, max_memory_mb=1.0)
        
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        
        metrics = cache.get_metrics()
        
        assert metrics["cache_size"] == 2
        assert metrics["max_size"] == 10
        assert metrics["max_memory_mb"] == 1.0
        assert "memory_usage_mb" in metrics


class TestMultiLayerCacheService:
    """Test cases for MultiLayerCacheService."""

    @pytest.fixture
    async def cache_service(self):
        """Create cache service for testing."""
        # Mock the performance service to avoid database dependencies
        mock_performance_service = Mock(spec=PerformanceMonitoringService)
        mock_performance_service.record_performance_metric = AsyncMock()
        
        service = MultiLayerCacheService(performance_service=mock_performance_service)
        
        # Mock the L2 cache to avoid Redis dependencies
        service.l2_cache = Mock()
        service.l2_cache.get = AsyncMock(return_value=None)
        service.l2_cache.set = AsyncMock(return_value=True)
        service.l2_cache.delete = AsyncMock(return_value=True)
        service.l2_cache.redis = Mock()
        service.l2_cache.redis.smembers = AsyncMock(return_value=set())
        service.l2_cache.redis.sadd = AsyncMock()
        service.l2_cache.redis.delete = AsyncMock()
        
        await service.initialize()
        return service

    @pytest.mark.asyncio
    async def test_cache_service_initialization(self, cache_service):
        """Test cache service initialization."""
        assert cache_service.l1_cache is not None
        assert cache_service.l2_cache is not None
        assert cache_service.performance_service is not None

    @pytest.mark.asyncio
    async def test_cache_get_l1_hit(self, cache_service):
        """Test cache get with L1 hit."""
        # Set value in L1 cache
        cache_service.l1_cache.set("test_key", "test_value")
        
        # Get value should hit L1 cache
        result = await cache_service.get("test_key")
        
        assert result == "test_value"
        assert cache_service.l1_metrics.hit_count == 1
        assert cache_service.l1_metrics.total_requests == 1

    @pytest.mark.asyncio
    async def test_cache_get_l2_hit(self, cache_service):
        """Test cache get with L2 hit after L1 miss."""
        # Mock L2 cache to return value
        cache_service.l2_cache.get.return_value = "test_value_l2"
        
        # Get value should miss L1 but hit L2
        result = await cache_service.get("test_key")
        
        assert result == "test_value_l2"
        assert cache_service.l1_metrics.miss_count == 1
        assert cache_service.l2_metrics.hit_count == 1
        
        # Value should now be in L1 cache for future requests
        l1_result = cache_service.l1_cache.get("test_key")
        assert l1_result == "test_value_l2"

    @pytest.mark.asyncio
    async def test_cache_get_miss_all_layers(self, cache_service):
        """Test cache get with miss on all layers."""
        # Both L1 and L2 should miss
        result = await cache_service.get("non_existent_key")
        
        assert result is None
        assert cache_service.l1_metrics.miss_count == 1
        assert cache_service.l2_metrics.miss_count == 1

    @pytest.mark.asyncio
    async def test_cache_set_both_layers(self, cache_service):
        """Test cache set to both layers."""
        result = await cache_service.set("test_key", "test_value", ttl=300)
        
        assert result is True
        
        # Verify L1 cache has the value
        l1_result = cache_service.l1_cache.get("test_key")
        assert l1_result == "test_value"
        
        # Verify L2 cache set was called
        cache_service.l2_cache.set.assert_called_once_with("test_key", "test_value", 300)

    @pytest.mark.asyncio
    async def test_cache_set_with_tags(self, cache_service):
        """Test cache set with tags for invalidation."""
        tags = ["tag1", "tag2"]
        result = await cache_service.set("test_key", "test_value", tags=tags)
        
        assert result is True
        
        # Verify tags were added
        cache_service.l2_cache.redis.sadd.assert_any_call("tag:tag1", "test_key")
        cache_service.l2_cache.redis.sadd.assert_any_call("tag:tag2", "test_key")

    @pytest.mark.asyncio
    async def test_cache_delete(self, cache_service):
        """Test cache delete from both layers."""
        # Set value in L1 cache
        cache_service.l1_cache.set("test_key", "test_value")
        
        result = await cache_service.delete("test_key")
        
        assert result is True
        
        # Verify L1 cache no longer has the value
        assert cache_service.l1_cache.get("test_key") is None
        
        # Verify L2 cache delete was called
        cache_service.l2_cache.delete.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_cache_invalidate_by_tags(self, cache_service):
        """Test cache invalidation by tags."""
        # Mock Redis to return cache keys for tags
        cache_service.l2_cache.redis.smembers.return_value = {"key1", "key2"}
        
        # Set some values in L1 cache
        cache_service.l1_cache.set("key1", "value1")
        cache_service.l1_cache.set("key2", "value2")
        
        result = await cache_service.invalidate_by_tags(["test_tag"])
        
        assert result == 2  # Two keys invalidated
        
        # Verify keys were removed from L1 cache
        assert cache_service.l1_cache.get("key1") is None
        assert cache_service.l1_cache.get("key2") is None

    @pytest.mark.asyncio
    async def test_cache_warming(self, cache_service):
        """Test cache warming functionality."""
        warming_config = CacheWarmingConfig(
            cache_key_patterns=["pattern1", "pattern2"],
            warming_interval_seconds=300,
            max_warming_items=100
        )
        
        result = await cache_service.warm_cache(warming_config)
        
        # Should return number of patterns processed
        assert result == 2

    @pytest.mark.asyncio
    async def test_cache_metrics(self, cache_service):
        """Test cache metrics collection."""
        # Simulate some cache operations
        cache_service.l1_metrics.hit_count = 80
        cache_service.l1_metrics.miss_count = 20
        cache_service.l1_metrics.total_requests = 100
        cache_service.l1_metrics.avg_response_time = 5.0
        
        cache_service.l2_metrics.hit_count = 15
        cache_service.l2_metrics.miss_count = 5
        cache_service.l2_metrics.total_requests = 20
        cache_service.l2_metrics.avg_response_time = 45.0
        
        metrics = await cache_service.get_cache_metrics()
        
        assert "overall_metrics" in metrics
        assert "l1_cache" in metrics
        assert "l2_cache" in metrics
        
        # Check overall hit rate calculation
        overall_hit_rate = metrics["overall_metrics"]["hit_rate_percentage"]
        expected_hit_rate = (80 + 15) / (100 + 20) * 100
        assert abs(overall_hit_rate - expected_hit_rate) < 0.1
        
        # Check L1 metrics
        assert metrics["l1_cache"]["hit_rate_percentage"] == 80.0
        assert metrics["l1_cache"]["hit_count"] == 80
        assert metrics["l1_cache"]["miss_count"] == 20
        
        # Check L2 metrics
        assert metrics["l2_cache"]["hit_rate_percentage"] == 75.0
        assert metrics["l2_cache"]["hit_count"] == 15
        assert metrics["l2_cache"]["miss_count"] == 5

    @pytest.mark.asyncio
    async def test_cache_performance_targets(self, cache_service):
        """Test cache performance targets are met."""
        # Test L1 cache performance (should be < 5ms)
        start_time = time.time()
        cache_service.l1_cache.set("perf_test", "value")
        result = cache_service.l1_cache.get("perf_test")
        l1_time = (time.time() - start_time) * 1000  # Convert to ms
        
        assert result == "value"
        assert l1_time < 5.0  # Should be under 5ms for L1 cache
        
        # Test overall cache get performance (should be < 50ms)
        start_time = time.time()
        result = await cache_service.get("perf_test")
        total_time = (time.time() - start_time) * 1000  # Convert to ms
        
        assert result == "value"
        assert total_time < 50.0  # Should be under 50ms for cache access

    @pytest.mark.asyncio
    async def test_cache_error_handling(self, cache_service):
        """Test cache error handling and resilience."""
        # Mock L2 cache to raise exception
        cache_service.l2_cache.get.side_effect = Exception("Redis connection error")
        
        # Cache should still work with L1 only
        cache_service.l1_cache.set("error_test", "value")
        
        # Should not raise exception, should fall back gracefully
        result = await cache_service.get("error_test", use_l2=False)
        assert result == "value"
        
        # With L2 enabled, should handle error gracefully
        try:
            result = await cache_service.get("error_test", use_l2=True)
            # Should either return None or raise ServiceError
        except Exception as e:
            assert "Failed to get cache value" in str(e)
