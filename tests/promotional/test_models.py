"""
Unit tests for promotional models.

This module provides comprehensive unit tests for promotional system models:
- Campaign: Core campaign management with budget tracking and multi-provider support
- Advertisement: Advertisement management with creative assets and A/B testing
- CampaignMetrics: Performance metrics tracking with time-series data
- PromotionalListing: Featured placement management with priority-based positioning
- AdSpend: Financial tracking and billing integration
- Enum validation and business logic constraints
- BaseModelWithAudit integration and relationship testing

Implements Phase 5: Comprehensive Testing Infrastructure with >85% test coverage.
"""

import pytest
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from uuid import uuid4

from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend,
    CampaignStatus, CampaignType, CampaignObjective, BidStrategy,
    AdFormat, AdStatus, PlacementType, PlacementStatus, SpendCategory
)
from app.core.payment.config import PaymentProviderType


class TestCampaignModel:
    """Test Campaign model functionality."""

    def test_campaign_creation(self, campaign_factory):
        """Test basic campaign creation."""
        campaign = campaign_factory()

        assert campaign.vendor_id == 1
        assert campaign.name == "Summer Tourism Campaign 2025"
        assert campaign.campaign_type == CampaignType.FEATURED_LISTING
        assert campaign.campaign_objective == CampaignObjective.BOOKING_CONVERSION
        assert campaign.status == CampaignStatus.DRAFT
        assert campaign.total_budget == Decimal("50000.00")
        assert campaign.daily_budget == Decimal("2000.00")
        assert campaign.payment_provider == PaymentProviderType.PAYSTACK
        assert campaign.bid_strategy == BidStrategy.MANUAL_CPC

    def test_campaign_status_enum_values(self):
        """Test campaign status enum values."""
        expected_statuses = {
            "draft", "pending_approval", "approved", "active",
            "paused", "completed", "cancelled", "rejected"
        }
        actual_statuses = {status.value for status in CampaignStatus}
        assert actual_statuses == expected_statuses

    def test_campaign_type_enum_values(self):
        """Test campaign type enum values."""
        expected_types = {
            "featured_listing", "sponsored_search", "discovery_feed",
            "banner_ad", "promoted_service", "category_spotlight"
        }
        actual_types = {campaign_type.value for campaign_type in CampaignType}
        assert actual_types == expected_types

    def test_campaign_objective_enum_values(self):
        """Test campaign objective enum values."""
        expected_objectives = {
            "brand_awareness", "lead_generation", "booking_conversion",
            "traffic_increase", "engagement", "reach"
        }
        actual_objectives = {obj.value for obj in CampaignObjective}
        assert actual_objectives == expected_objectives

    def test_campaign_bid_strategy_enum_values(self):
        """Test bid strategy enum values."""
        expected_strategies = {
            "manual_cpc", "auto_cpc", "target_cpa",
            "target_roas", "maximize_clicks", "maximize_conversions"
        }
        actual_strategies = {strategy.value for strategy in BidStrategy}
        assert actual_strategies == expected_strategies

    def test_campaign_budget_properties(self, campaign_factory):
        """Test campaign budget calculation properties."""
        campaign = campaign_factory(
            total_budget=Decimal("10000.00"),
            spent_amount=Decimal("3000.00")
        )

        assert campaign.budget_utilization_percentage == Decimal("30.00")
        assert campaign.remaining_budget == Decimal("7000.00")

    def test_campaign_performance_properties(self, campaign_factory):
        """Test campaign performance calculation properties."""
        campaign = campaign_factory(
            impressions=1000,
            clicks=50,
            conversions=5,
            spent_amount=Decimal("250.00"),
            revenue_generated=Decimal("2500.00")
        )

        assert campaign.click_through_rate == Decimal("5.00")
        assert campaign.conversion_rate == Decimal("10.00")
        assert campaign.cost_per_click == Decimal("5.00")
        assert campaign.cost_per_acquisition == Decimal("50.00")
        assert campaign.return_on_ad_spend == Decimal("10.00")

    def test_campaign_status_properties(self, campaign_factory):
        """Test campaign status check properties."""
        # Test active campaign
        active_campaign = campaign_factory(
            status=CampaignStatus.ACTIVE,
            start_date=date.today() - timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )
        assert active_campaign.is_active_status is True
        assert active_campaign.is_running is True

        # Test completed campaign
        completed_campaign = campaign_factory(
            status=CampaignStatus.COMPLETED,
            end_date=date.today() - timedelta(days=1)
        )
        assert completed_campaign.is_active_status is False
        assert completed_campaign.is_running is False

    def test_campaign_targeting_config(self, campaign_factory):
        """Test campaign targeting configuration."""
        targeting_config = {
            "age_range": {"min": 25, "max": 55},
            "interests": ["travel", "culture", "food"],
            "device_types": ["mobile", "desktop"]
        }

        campaign = campaign_factory(targeting_config=targeting_config)
        assert campaign.targeting_config == targeting_config
        assert campaign.targeting_config["age_range"]["min"] == 25
        assert "travel" in campaign.targeting_config["interests"]

    def test_campaign_geographic_targeting(self, campaign_factory):
        """Test campaign geographic targeting."""
        geographic_targeting = {
            "countries": ["NG", "US", "GB"],
            "states": ["Lagos", "Abuja"],
            "cities": ["Lagos", "Abuja", "Port Harcourt"],
            "radius_km": 50
        }

        campaign = campaign_factory(geographic_targeting=geographic_targeting)
        assert campaign.geographic_targeting == geographic_targeting
        assert len(campaign.geographic_targeting["countries"]) == 3
        assert campaign.geographic_targeting["radius_km"] == 50

    def test_campaign_repr(self, campaign_factory):
        """Test campaign string representation."""
        campaign = campaign_factory(id=123, name="Test Campaign")
        expected_repr = "<Campaign(id=123, name='Test Campaign', status='CampaignStatus.DRAFT')>"
        assert repr(campaign) == expected_repr


class TestAdvertisementModel:
    """Test Advertisement model functionality."""

    def test_advertisement_creation(self, advertisement_factory):
        """Test basic advertisement creation."""
        ad = advertisement_factory()

        assert ad.campaign_id == 1
        assert ad.name == "Summer Tourism Hero Ad"
        assert ad.ad_format == AdFormat.IMAGE
        assert ad.status == AdStatus.DRAFT
        assert ad.headline == "Discover Nigeria Hidden Cultural Gems This Summer"
        assert ad.call_to_action == "Book Now"

    def test_ad_format_enum_values(self):
        """Test advertisement format enum values."""
        expected_formats = {
            "image", "video", "carousel", "text", "rich_media", "interactive"
        }
        actual_formats = {format_.value for format_ in AdFormat}
        assert actual_formats == expected_formats

    def test_ad_status_enum_values(self):
        """Test advertisement status enum values."""
        expected_statuses = {
            "draft", "pending_review", "approved", "active",
            "paused", "rejected", "expired"
        }
        actual_statuses = {status.value for status in AdStatus}
        assert actual_statuses == expected_statuses

    def test_advertisement_performance_properties(self, advertisement_factory):
        """Test advertisement performance calculation properties."""
        ad = advertisement_factory(
            impressions=2000,
            clicks=100,
            conversions=10,
            spend_amount=Decimal("500.00")
        )

        assert ad.click_through_rate == Decimal("5.00")
        assert ad.conversion_rate == Decimal("10.00")
        assert ad.cost_per_click == Decimal("5.00")
        assert ad.cost_per_acquisition == Decimal("50.00")

    def test_advertisement_a_b_testing(self, advertisement_factory):
        """Test advertisement A/B testing functionality."""
        ad_variant_a = advertisement_factory(
            variant_group="summer_tourism_2025",
            variant_name="hero_variant_a",
            test_percentage=Decimal("50.00")
        )

        ad_variant_b = advertisement_factory(
            id=2,
            variant_group="summer_tourism_2025",
            variant_name="hero_variant_b",
            test_percentage=Decimal("50.00")
        )

        assert ad_variant_a.variant_group == ad_variant_b.variant_group
        assert ad_variant_a.variant_name != ad_variant_b.variant_name
        assert ad_variant_a.test_percentage + ad_variant_b.test_percentage == Decimal("100.00")

    def test_advertisement_media_assets(self, advertisement_factory):
        """Test advertisement media assets handling."""
        media_assets = {
            "images": [
                {"url": "https://cdn.example.com/image1.jpg", "alt": "Tourism Image 1"},
                {"url": "https://cdn.example.com/image2.jpg", "alt": "Tourism Image 2"}
            ],
            "videos": [
                {"url": "https://cdn.example.com/video1.mp4", "duration": 30}
            ]
        }

        ad = advertisement_factory(media_assets=media_assets)
        assert ad.media_assets == media_assets
        assert len(ad.media_assets["images"]) == 2
        assert ad.media_assets["videos"][0]["duration"] == 30

    def test_advertisement_placement_preferences(self, advertisement_factory):
        """Test advertisement placement preferences."""
        placement_preferences = {
            "preferred_placements": ["homepage_hero", "category_top"],
            "excluded_placements": ["mobile_banner"],
            "priority_level": 8,
            "time_restrictions": ["morning", "evening"]
        }

        ad = advertisement_factory(placement_preferences=placement_preferences)
        assert ad.placement_preferences == placement_preferences
        assert ad.placement_preferences["priority_level"] == 8
        assert "homepage_hero" in ad.placement_preferences["preferred_placements"]

    def test_advertisement_repr(self, advertisement_factory):
        """Test advertisement string representation."""
        ad = advertisement_factory(id=456, name="Test Ad")
        expected_repr = "<Advertisement(id=456, name='Test Ad', status='AdStatus.DRAFT')>"
        assert repr(ad) == expected_repr


class TestCampaignMetricsModel:
    """Test CampaignMetrics model functionality."""

    def test_campaign_metrics_creation(self, campaign_metrics_factory):
        """Test basic campaign metrics creation."""
        metrics = campaign_metrics_factory()

        assert metrics.campaign_id == 1
        assert metrics.date == date.today()
        assert metrics.hour == 12
        assert metrics.impressions == 1000
        assert metrics.clicks == 50
        assert metrics.conversions == 5

    def test_campaign_metrics_calculations(self, campaign_metrics_factory):
        """Test campaign metrics calculations."""
        metrics = campaign_metrics_factory(
            impressions=2000,
            clicks=100,
            conversions=10,
            spend_amount=Decimal("500.00"),
            revenue_generated=Decimal("5000.00")
        )

        assert metrics.click_through_rate == Decimal("5.00")
        assert metrics.conversion_rate == Decimal("10.00")
        assert metrics.cost_per_click == Decimal("5.00")
        assert metrics.cost_per_acquisition == Decimal("50.00")
        assert metrics.return_on_ad_spend == Decimal("10.00")

    def test_campaign_metrics_quality_scores(self, campaign_metrics_factory):
        """Test campaign metrics quality scoring."""
        metrics = campaign_metrics_factory(
            quality_score=Decimal("85.00"),
            relevance_score=Decimal("90.00"),
            landing_page_score=Decimal("80.00"),
            optimization_score=Decimal("75.00")
        )

        assert metrics.quality_score == Decimal("85.00")
        assert metrics.relevance_score == Decimal("90.00")
        assert metrics.landing_page_score == Decimal("80.00")
        assert metrics.optimization_score == Decimal("75.00")

    def test_campaign_metrics_device_breakdown(self, campaign_metrics_factory):
        """Test campaign metrics device breakdown."""
        metrics = campaign_metrics_factory(
            desktop_impressions=600,
            mobile_impressions=350,
            tablet_impressions=50
        )

        total_impressions = (
            metrics.desktop_impressions +
            metrics.mobile_impressions +
            metrics.tablet_impressions
        )
        assert total_impressions == 1000
        assert metrics.mobile_impressions > metrics.tablet_impressions

    def test_campaign_metrics_geographic_performance(self, campaign_metrics_factory):
        """Test campaign metrics geographic performance tracking."""
        top_locations = {
            "Lagos": {"impressions": 400, "clicks": 25, "conversions": 3},
            "Abuja": {"impressions": 300, "clicks": 15, "conversions": 1},
            "Port Harcourt": {"impressions": 200, "clicks": 8, "conversions": 1}
        }

        metrics = campaign_metrics_factory(top_performing_locations=top_locations)
        assert metrics.top_performing_locations == top_locations
        assert metrics.top_performing_locations["Lagos"]["impressions"] == 400

    def test_campaign_metrics_recommendations(self, campaign_metrics_factory):
        """Test campaign metrics optimization recommendations."""
        recommendations = {
            "budget_optimization": "Increase daily budget by 20%",
            "targeting_optimization": "Expand to Rivers State",
            "creative_optimization": "Test new headline variations",
            "bidding_optimization": "Switch to auto CPC bidding"
        }

        metrics = campaign_metrics_factory(recommendations=recommendations)
        assert metrics.recommendations == recommendations
        assert "budget_optimization" in metrics.recommendations


class TestPromotionalListingModel:
    """Test PromotionalListing model functionality."""

    def test_promotional_listing_creation(self, promotional_listing_factory):
        """Test basic promotional listing creation."""
        listing = promotional_listing_factory()

        assert listing.advertisement_id == 1
        assert listing.vendor_id == 1
        assert listing.service_id == 1
        assert listing.placement_type == PlacementType.HOMEPAGE_HERO
        assert listing.placement_status == PlacementStatus.AVAILABLE
        assert listing.priority_level == 8

    def test_placement_type_enum_values(self):
        """Test placement type enum values."""
        expected_types = {
            "homepage_hero", "category_top", "search_results",
            "service_detail", "vendor_profile", "mobile_banner", "email_newsletter"
        }
        actual_types = {placement.value for placement in PlacementType}
        assert actual_types == expected_types

    def test_placement_status_enum_values(self):
        """Test placement status enum values."""
        expected_statuses = {
            "available", "reserved", "occupied", "maintenance"
        }
        actual_statuses = {status.value for status in PlacementStatus}
        assert actual_statuses == expected_statuses

    def test_promotional_listing_pricing(self, promotional_listing_factory):
        """Test promotional listing pricing calculations."""
        listing = promotional_listing_factory(
            base_price=Decimal("5000.00"),
            premium_multiplier=Decimal("1.50")
        )

        expected_final_price = listing.base_price * listing.premium_multiplier
        assert listing.final_price == expected_final_price

    def test_promotional_listing_performance(self, promotional_listing_factory):
        """Test promotional listing performance tracking."""
        listing = promotional_listing_factory(
            impressions=5000,
            clicks=250,
            conversions=25,
            revenue_generated=Decimal("12500.00")
        )

        assert listing.click_through_rate == Decimal("5.00")
        assert listing.conversion_rate == Decimal("10.00")
        assert listing.revenue_per_impression == Decimal("2.50")

    def test_promotional_listing_targeting_config(self, promotional_listing_factory):
        """Test promotional listing targeting configuration."""
        targeting_config = {
            "device_types": ["mobile", "desktop"],
            "time_of_day": ["morning", "evening"],
            "user_segments": ["returning_customers", "high_value_users"]
        }

        listing = promotional_listing_factory(targeting_config=targeting_config)
        assert listing.targeting_config == targeting_config
        assert "mobile" in listing.targeting_config["device_types"]

    def test_promotional_listing_optimization_settings(self, promotional_listing_factory):
        """Test promotional listing optimization settings."""
        optimization_settings = {
            "auto_bidding": True,
            "performance_threshold": 5.0,
            "budget_pacing": "even",
            "dayparting": {"enabled": True, "peak_hours": [9, 10, 11, 17, 18, 19]}
        }

        listing = promotional_listing_factory(optimization_settings=optimization_settings)
        assert listing.optimization_settings == optimization_settings
        assert listing.optimization_settings["auto_bidding"] is True


class TestAdSpendModel:
    """Test AdSpend model functionality."""

    def test_ad_spend_creation(self, ad_spend_factory):
        """Test basic ad spend creation."""
        spend = ad_spend_factory()

        assert spend.campaign_id == 1
        assert spend.advertisement_id == 1
        assert spend.vendor_id == 1
        assert spend.spend_category == SpendCategory.CAMPAIGN_BUDGET
        assert spend.amount == Decimal("250.00")
        assert spend.currency == "NGN"
        assert spend.payment_provider == PaymentProviderType.PAYSTACK

    def test_spend_category_enum_values(self):
        """Test spend category enum values."""
        expected_categories = {
            "campaign_budget", "placement_fee", "creative_production",
            "targeting_premium", "performance_bonus", "platform_fee"
        }
        actual_categories = {category.value for category in SpendCategory}
        assert actual_categories == expected_categories

    def test_ad_spend_financial_calculations(self, ad_spend_factory):
        """Test ad spend financial calculations."""
        spend = ad_spend_factory(
            amount=Decimal("250.00"),
            platform_fee=Decimal("12.50"),
            tax_amount=Decimal("18.75")
        )

        expected_total = spend.amount + spend.platform_fee + spend.tax_amount
        assert spend.total_amount == expected_total

    def test_ad_spend_performance_attribution(self, ad_spend_factory):
        """Test ad spend performance attribution."""
        spend = ad_spend_factory(
            impressions_attributed=1000,
            clicks_attributed=50,
            conversions_attributed=5,
            revenue_attributed=Decimal("2500.00"),
            amount=Decimal("250.00")
        )

        assert spend.cost_per_impression == Decimal("0.25")
        assert spend.cost_per_click == Decimal("5.00")
        assert spend.cost_per_acquisition == Decimal("50.00")
        assert spend.return_on_ad_spend == Decimal("10.00")

    def test_ad_spend_reconciliation(self, ad_spend_factory):
        """Test ad spend reconciliation tracking."""
        spend = ad_spend_factory(
            reconciled=True,
            reconciled_at=datetime.now(timezone.utc),
            reconciliation_reference="REC-2025-001234"
        )

        assert spend.reconciled is True
        assert spend.reconciled_at is not None
        assert spend.reconciliation_reference == "REC-2025-001234"

    def test_ad_spend_payment_integration(self, ad_spend_factory):
        """Test ad spend payment integration."""
        spend = ad_spend_factory(
            payment_provider=PaymentProviderType.STRIPE,
            payment_reference="CC-STRIPE-SPEND-001",
            payment_status="completed"
        )

        assert spend.payment_provider == PaymentProviderType.STRIPE
        assert spend.payment_reference == "CC-STRIPE-SPEND-001"
        assert spend.payment_status == "completed"


class TestPromotionalModelConstraints:
    """Test promotional model constraints and validations."""

    def test_campaign_budget_constraints(self, campaign_factory):
        """Test campaign budget validation constraints."""
        # Test minimum budget constraint
        campaign = campaign_factory(total_budget=Decimal("1000.00"))
        assert campaign.total_budget >= Decimal("1000.00")

        # Test maximum budget constraint
        campaign = campaign_factory(total_budget=Decimal("10000000.00"))
        assert campaign.total_budget <= Decimal("10000000.00")

        # Test daily budget constraint
        campaign = campaign_factory(daily_budget=Decimal("100.00"))
        assert campaign.daily_budget >= Decimal("100.00")

    def test_advertisement_content_constraints(self, advertisement_factory):
        """Test advertisement content validation constraints."""
        # Test headline length constraints
        ad = advertisement_factory(headline="Valid Headline Length")
        assert len(ad.headline) >= 10
        assert len(ad.headline) <= 100

        # Test call to action length
        ad = advertisement_factory(call_to_action="Buy")
        assert len(ad.call_to_action) >= 3

    def test_promotional_listing_priority_constraints(self, promotional_listing_factory):
        """Test promotional listing priority validation constraints."""
        listing = promotional_listing_factory(priority_level=5)
        assert 1 <= listing.priority_level <= 10

    def test_ad_spend_amount_constraints(self, ad_spend_factory):
        """Test ad spend amount validation constraints."""
        spend = ad_spend_factory(
            amount=Decimal("100.00"),
            platform_fee=Decimal("5.00"),
            tax_amount=Decimal("7.50")
        )

        assert spend.amount >= Decimal("0.00")
        assert spend.platform_fee >= Decimal("0.00")
        assert spend.tax_amount >= Decimal("0.00")
        assert spend.total_amount >= spend.amount

    def test_campaign_metrics_percentage_constraints(self, campaign_metrics_factory):
        """Test campaign metrics percentage validation constraints."""
        metrics = campaign_metrics_factory(
            click_through_rate=Decimal("5.00"),
            conversion_rate=Decimal("10.00"),
            bounce_rate=Decimal("25.00")
        )

        assert Decimal("0.00") <= metrics.click_through_rate <= Decimal("100.00")
        assert Decimal("0.00") <= metrics.conversion_rate <= Decimal("100.00")
        assert Decimal("0.00") <= metrics.bounce_rate <= Decimal("100.00")
