# Culture Connect Backend API - Production Dockerfile
# Multi-stage build for optimized production image with security scanning and optimization

# Build stage
FROM python:3.11-slim as builder

# Set build arguments for optimization
ARG BUILDPLATFORM
ARG TARGETPLATFORM
ARG BUILDX_VERSION

# Set environment variables for build optimization
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Install system dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    git \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies with optimization
COPY requirements.txt .
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt && \
    pip check

# Cache stage for build optimization
FROM builder as cache
RUN --mount=type=cache,target=/tmp/poetry_cache \
    pip install --upgrade pip

# Development stage
FROM python:3.11-slim as development

# Set environment variables for development
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app" \
    CC_ENVIRONMENT=development \
    CC_DEBUG=true \
    CC_LOG_LEVEL=DEBUG \
    CC_RELOAD=true

# Install development dependencies and tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    git \
    vim \
    htop \
    procps \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && update-ca-certificates

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create non-root user for development
RUN groupadd -r -g 1001 appuser && \
    useradd -r -u 1001 -g appuser -m -d /home/<USER>/bin/bash appuser

# Create application directories with proper permissions
RUN mkdir -p /app /app/logs /app/uploads /app/data && \
    chown -R appuser:appuser /app

# Set working directory
WORKDIR /app

# Copy application code with proper ownership
COPY --chown=appuser:appuser . .

# Install development dependencies if they exist
RUN if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Development health check (more frequent for faster feedback)
HEALTHCHECK --interval=15s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Development startup command with hot reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

# Production stage
FROM python:3.11-slim as production

# Set build arguments for metadata
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# Add metadata labels
LABEL maintainer="Culture Connect Team <<EMAIL>>" \
      org.opencontainers.image.title="Culture Connect Backend API" \
      org.opencontainers.image.description="Production-ready FastAPI backend for Culture Connect platform" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_DATE}" \
      org.opencontainers.image.revision="${VCS_REF}" \
      org.opencontainers.image.vendor="Culture Connect" \
      org.opencontainers.image.licenses="MIT"

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app" \
    CC_ENVIRONMENT=production \
    CC_WORKERS=4 \
    CC_LOG_LEVEL=INFO

# Install runtime dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && update-ca-certificates

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create non-root user with specific UID/GID for security
RUN groupadd -r -g 1001 appuser && \
    useradd -r -u 1001 -g appuser -m -d /home/<USER>/bin/bash appuser

# Create application directories with proper permissions
RUN mkdir -p /app /app/logs /app/uploads /app/data && \
    chown -R appuser:appuser /app

# Set working directory
WORKDIR /app

# Copy application code with proper ownership
COPY --chown=appuser:appuser . .

# Create additional directories for production
RUN mkdir -p /app/static /app/media /app/tmp && \
    chown -R appuser:appuser /app/static /app/media /app/tmp

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Enhanced health check with proper timeout and retry logic
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production-optimized startup command
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--access-log", "--log-config", "/app/logging.conf"]