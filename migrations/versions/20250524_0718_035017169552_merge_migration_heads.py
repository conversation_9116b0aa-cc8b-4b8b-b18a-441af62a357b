"""Merge migration heads

Revision ID: 035017169552
Revises: 7db7e0b9dacb, jwt_auth_tables
Create Date: 2025-05-24 07:18:02.725255+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '035017169552'
down_revision = ('7db7e0b9dacb', 'jwt_auth_tables')
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    pass


def downgrade() -> None:
    """Downgrade database schema."""
    pass
