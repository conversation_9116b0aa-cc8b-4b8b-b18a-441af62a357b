# Culture Connect Backend - AI Development Guardrails

## 📋 Document Overview

This document establishes strict development guidelines and constraints for AI assistants working on the Culture Connect Backend project. These guardrails ensure adherence to our 16-week implementation roadmap, Phase 5 Promotional/Advertising System specifications, geo-location-based payment system, and production-grade standards.

**Project Context**: FastAPI monolithic architecture with modular design
**Timeline**: 16-week implementation (9 phases)
**Critical Phase**: Phase 5 - Promotional & Advertising System with Dual Payment Providers
**Workspace**: `/Users/<USER>/Desktop/CurrentProject/cultureConnectBackend`
**Multi-Project Integration**:
- Backend API: `/Users/<USER>/Desktop/CurrentProject/cultureConnectBackend`
- PWA Web App: `/Users/<USER>/Desktop/CurrentProject/cultureConnectPWAWeb`
- Mobile App: `/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect`

## 🌍 Geo-Location Payment System Overview

**Payment Provider Strategy**:
- **African Markets** (Nigeria, Ghana, Kenya, etc.): Paystack as primary processor
- **Diaspora Markets** (US, UK, Canada, Europe, etc.): Stripe as primary processor
- **Fallback Mechanisms**: Automatic provider switching when primary fails
- **AI Integration**: AIML API (https://aimlapi.com/) for intelligent payment optimization and other Ai related function like sentiment analysis, recommender systems etc
---

## 🏗️ 1. Project Structure & File Organization

### ✅ MANDATORY Structure Adherence

```
cultureConnectBackend/
├── app/
│   ├── api/v1/          # API endpoints ONLY
│   │   ├── payments/    # Payment provider endpoints (Paystack, Stripe)
│   │   ├── promotional/ # Phase 5 promotional/advertising endpoints
│   │   ├── vendors/     # Vendor management endpoints
│   │   └── geo/         # Geo-location services endpoints
│   ├── core/            # Core utilities, config, security
│   │   ├── payment/     # Payment provider configurations
│   │   ├── geo/         # Geo-location utilities
│   │   └── ai/          # AI/ML API integrations
│   ├── db/              # Database session, base classes
│   ├── models/          # SQLAlchemy models ONLY
│   │   ├── payment/     # Payment-related models (dual provider)
│   │   ├── promotional/ # Phase 5 promotional models
│   │   └── geo/         # Geo-location models
│   ├── schemas/         # Pydantic schemas ONLY
│   │   ├── payment/     # Payment schemas (Paystack, Stripe)
│   │   ├── promotional/ # Promotional schemas
│   │   └── geo/         # Geo-location schemas
│   ├── services/        # Business logic ONLY
│   │   ├── payment/     # Payment provider services
│   │   │   ├── paystack_service.py
│   │   │   ├── stripe_service.py
│   │   │   └── payment_router.py
│   │   ├── promotional/ # Phase 5 promotional services
│   │   ├── geo/         # Geo-location services
│   │   └── ai/          # AI/ML services (AIML API integration)
│   ├── repositories/    # Data access layer ONLY
│   │   ├── payment/     # Payment data repositories
│   │   ├── promotional/ # Promotional data repositories
│   │   └── geo/         # Geo-location data repositories
│   └── tasks/           # Celery tasks ONLY
│       ├── payment/     # Payment processing tasks
│       ├── promotional/ # Promotional campaign tasks
│       └── ai/          # AI processing tasks
├── config/              # Configuration files
│   ├── payment/         # Payment provider configurations
│   ├── geo/             # Geo-location configurations
│   └── ai/              # AI/ML API configurations
├── scripts/             # Utility scripts
├── tests/               # All test files
│   ├── payment/         # Payment system tests
│   ├── promotional/     # Promotional system tests
│   └── geo/             # Geo-location tests
├── migrations/          # Alembic migrations
└── requirements.txt     # Dependencies (including geo-location, AI libs)
```

### 🚫 PROHIBITED Actions

- **NEVER** create files outside `/Users/<USER>/Desktop/CurrentProject/cultureConnectBackend`
- **NEVER** mix concerns (e.g., business logic in API endpoints)
- **NEVER** create flat file structures without proper organization
- **NEVER** use generic names like `utils.py`, `helpers.py`

### ✅ REQUIRED Naming Conventions

```python
# Files: snake_case
promotional_campaigns.py
vendor_analytics.py

# Classes: PascalCase
class PromotionalCampaign(Base):
class VendorAnalyticsService:

# Functions/Variables: snake_case
def create_campaign():
campaign_id = uuid4()

# Constants: UPPER_SNAKE_CASE
MAX_CAMPAIGN_BUDGET = 1000000
DEFAULT_BID_STRATEGY = "manual_cpc"
```

### 🚫 NAMING VIOLATIONS

```python
# ❌ WRONG
class promotionalCampaign:  # Wrong case
def CreateCampaign():       # Wrong case
CAMPAIGN_id = "123"         # Inconsistent case

# ❌ WRONG FILE PLACEMENT
app/promotional_api.py      # Should be in app/api/v1/
app/campaign_logic.py       # Should be in app/services/
```

---

## 📝 2. Code Quality & Documentation Standards

### ✅ MANDATORY Documentation

```python
def create_promotional_campaign(
    vendor_id: UUID,
    campaign_data: PromotionalCampaignCreate,
    db: Session = Depends(get_db)
) -> PromotionalCampaignResponse:
    """
    Create a new promotional campaign for a vendor.

    This function handles the complete campaign creation workflow including
    budget validation, targeting setup, and initial performance tracking.

    Args:
        vendor_id: Unique identifier for the vendor
        campaign_data: Campaign configuration and settings
        db: Database session dependency

    Returns:
        PromotionalCampaignResponse: Created campaign with initial metrics

    Raises:
        HTTPException: 400 if budget exceeds vendor limits
        HTTPException: 403 if vendor not eligible for advertising
        HTTPException: 422 if targeting criteria invalid

    Example:
        >>> campaign = create_promotional_campaign(
        ...     vendor_id=UUID("123e4567-e89b-12d3-a456-************"),
        ...     campaign_data=PromotionalCampaignCreate(
        ...         name="Summer Promotion",
        ...         budget_amount=50000,
        ...         campaign_type="featured_listing"
        ...     )
        ... )
    """
```

### ✅ REQUIRED Type Hints

```python
# ✅ CORRECT
from typing import List, Optional, Dict, Any
from decimal import Decimal
from uuid import UUID

def calculate_campaign_roi(
    campaign_id: UUID,
    revenue_data: List[Dict[str, Any]],
    spend_amount: Decimal
) -> Optional[float]:
    """Calculate ROI for promotional campaign."""
    pass

# ✅ CORRECT Class Annotations
class PromotionalCampaign(Base):
    id: UUID
    vendor_id: UUID
    budget_amount: Decimal
    status: CampaignStatus
    created_at: datetime
```

### 🚫 DOCUMENTATION VIOLATIONS

```python
# ❌ WRONG - No docstring
def create_campaign(vendor_id, data):
    pass

# ❌ WRONG - No type hints
def calculate_roi(campaign_id, revenue, spend):
    return revenue / spend

# ❌ WRONG - Inadequate docstring
def complex_algorithm():
    """Does stuff."""
    # Complex 50-line algorithm without explanation
```

### ✅ REQUIRED Code Coverage

- **Minimum 80% overall coverage**
- **100% coverage for promotional/advertising features**
- **90% coverage for payment processing**
- **85% coverage for API endpoints**

---

## 🔒 3. Security & Production Standards

### ✅ MANDATORY Security Practices

```python
# ✅ CORRECT - Input Validation
from pydantic import BaseModel, validator
from typing import Literal

class CampaignCreate(BaseModel):
    name: str
    budget_amount: Decimal
    campaign_type: Literal["featured_listing", "sponsored_search", "discovery_feed"]

    @validator('budget_amount')
    def validate_budget(cls, v):
        if v < 1000:  # Minimum ₦1,000
            raise ValueError('Budget must be at least ₦1,000')
        if v > 10000000:  # Maximum ₦10M
            raise ValueError('Budget cannot exceed ₦10,000,000')
        return v

# ✅ CORRECT - Authentication
from app.core.security import verify_jwt_token, get_current_vendor

@router.post("/campaigns")
async def create_campaign(
    campaign_data: CampaignCreate,
    current_vendor: Vendor = Depends(get_current_vendor)
):
    """Create campaign with proper authentication."""
    pass

# ✅ CORRECT - Error Handling
try:
    campaign = await campaign_service.create(campaign_data)
    return campaign
except ValidationError as e:
    raise HTTPException(
        status_code=422,
        detail="Invalid campaign data"  # Don't expose internal details
    )
except InsufficientFundsError:
    raise HTTPException(
        status_code=400,
        detail="Insufficient advertising credits"
    )
```

### 🚫 SECURITY VIOLATIONS

```python
# ❌ WRONG - No input validation
@router.post("/campaigns")
async def create_campaign(data: dict):  # Raw dict, no validation
    pass

# ❌ WRONG - Exposing sensitive information
except Exception as e:
    raise HTTPException(500, detail=str(e))  # Exposes internal errors

# ❌ WRONG - No authentication
@router.delete("/campaigns/{campaign_id}")
async def delete_campaign(campaign_id: str):  # No auth check
    pass

# ❌ WRONG - SQL injection risk
query = f"SELECT * FROM campaigns WHERE vendor_id = '{vendor_id}'"  # Never do this
```

### ✅ REQUIRED Security Headers

```python
# ✅ CORRECT - CORS Configuration
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://vendor.cultureconnect.ng"],  # Specific origins only
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# ✅ CORRECT - Security Headers
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    return response
```

---

## 🛠️ 4. Technology Stack Constraints

### ✅ APPROVED Technologies ONLY

```python
# ✅ APPROVED Core Stack
FastAPI==0.104.1          # Web framework
SQLAlchemy==2.0.23        # ORM
PostgreSQL==15.x          # Database
Redis==7.2.x              # Caching
Celery==5.3.4             # Task queue
Pydantic==2.5.0           # Data validation
Alembic==1.13.0           # Migrations

# ✅ APPROVED Payment Providers (TRIPLE SYSTEM)
paystack==2.0.0           # African markets payment processing
stripe==7.8.0             # Diaspora markets payment processing
requests==2.31.0          # HTTP client for payment APIs

# ✅ APPROVED Cryptocurrency Payment Integration
web3==6.11.3             # Ethereum blockchain interaction
bitcoin==1.1.42          # Bitcoin blockchain utilities
qrcode==7.4.2            # QR code generation for crypto addresses
cryptography==41.0.7     # Crypto security and encryption
ccxt==4.1.0              # Cryptocurrency exchange integration

# ✅ APPROVED Geo-Location Services
geoip2==4.7.0             # IP-based geo-location
maxminddb==2.2.0          # MaxMind GeoIP database
geopy==2.4.0              # Geocoding and distance calculations
pycountry==23.12.11       # Country code utilities

# ✅ APPROVED AI/ML Integration
openai==1.3.0             # AIML API client
httpx==0.25.2             # Async HTTP client for AI APIs
tenacity==8.2.3           # Retry logic for AI API calls

# ✅ APPROVED Integrations
boto3==1.34.0             # AWS S3
websockets==12.0          # Real-time features
pytest==7.4.3            # Testing
pytest-asyncio==0.21.1   # Async testing
```

### 🚫 PROHIBITED Technologies

```python
# ❌ PROHIBITED - Experimental/Unstable
from some_experimental_lib import *  # No experimental libraries
import beta_package                   # No beta versions
from unstable_orm import Model        # No alternative ORMs

# ❌ PROHIBITED - Wrong Versions
FastAPI==0.68.0          # Too old
SQLAlchemy==1.4.x        # Use 2.0+ only
Pydantic==1.x            # Use 2.x only
```

### ✅ REQUIRED Version Pinning

```txt
# requirements.txt - EXACT versions required
fastapi==0.104.1
sqlalchemy==2.0.23
pydantic==2.5.0
redis==5.0.1
celery==5.3.4
paystack==2.0.0
pytest==7.4.3
pytest-cov==4.1.0
```

---

## 🌐 5. API Development Guidelines

### ✅ MANDATORY RESTful Design

```python
# ✅ CORRECT - Geo-Location Based Payment API Endpoints
@router.post("/payments/initialize", response_model=PaymentInitResponse)
@router.post("/payments/process", response_model=PaymentProcessResponse)
@router.get("/payments/{payment_id}/status", response_model=PaymentStatusResponse)
@router.post("/payments/{payment_id}/verify", response_model=PaymentVerificationResponse)

# ✅ CORRECT - Payment Provider Specific Endpoints
@router.post("/payments/paystack/initialize", response_model=PaystackInitResponse)
@router.post("/payments/stripe/initialize", response_model=StripeInitResponse)
@router.post("/payments/crypto/initialize", response_model=CryptoInitResponse)
@router.post("/webhooks/paystack", status_code=200)
@router.post("/webhooks/stripe", status_code=200)
@router.post("/webhooks/crypto", status_code=200)

# ✅ CORRECT - Cryptocurrency Specific Endpoints
@router.get("/crypto/supported-currencies", response_model=List[CryptoCurrencyResponse])
@router.get("/crypto/exchange-rates", response_model=ExchangeRatesResponse)
@router.post("/crypto/generate-address", response_model=CryptoAddressResponse)
@router.get("/crypto/transaction/{tx_hash}/status", response_model=CryptoTransactionStatusResponse)
@router.post("/crypto/verify-payment", response_model=CryptoVerificationResponse)

# ✅ CORRECT - Geo-Location Service Endpoints
@router.get("/geo/detect-location", response_model=LocationResponse)
@router.get("/geo/payment-provider/{country_code}", response_model=PaymentProviderResponse)
@router.get("/geo/supported-countries", response_model=List[CountryResponse])

# ✅ CORRECT - Promotional API Endpoints (Phase 5)
@router.get("/vendors/{vendor_id}/campaigns", response_model=List[CampaignResponse])
@router.post("/vendors/{vendor_id}/campaigns", response_model=CampaignResponse)
@router.get("/vendors/{vendor_id}/campaigns/{campaign_id}", response_model=CampaignResponse)
@router.put("/vendors/{vendor_id}/campaigns/{campaign_id}", response_model=CampaignResponse)
@router.delete("/vendors/{vendor_id}/campaigns/{campaign_id}", status_code=204)

# ✅ CORRECT - AI-Enhanced Endpoints
@router.post("/ai/payment-optimization", response_model=PaymentOptimizationResponse)
@router.post("/ai/campaign-optimization", response_model=CampaignOptimizationResponse)
@router.get("/ai/market-insights/{country_code}", response_model=MarketInsightsResponse)

# ✅ CORRECT - Status Codes
@router.post("/payments", status_code=201)  # Created
@router.put("/payments/{id}", status_code=200)  # Updated
@router.delete("/payments/{id}", status_code=204)  # No Content
```

### ✅ REQUIRED Error Responses

```python
# ✅ CORRECT - Standardized Error Format
class ErrorResponse(BaseModel):
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime
    request_id: str

# ✅ CORRECT - Proper HTTP Status Codes
if not campaign:
    raise HTTPException(
        status_code=404,
        detail=ErrorResponse(
            error="CAMPAIGN_NOT_FOUND",
            message="Campaign not found",
            timestamp=datetime.utcnow(),
            request_id=request.headers.get("X-Request-ID")
        ).dict()
    )
```

### 🚫 API VIOLATIONS

```python
# ❌ WRONG - Non-RESTful endpoints
@router.get("/get_campaigns")           # Should be GET /campaigns
@router.post("/create_campaign")        # Should be POST /campaigns
@router.get("/campaign_delete/{id}")    # Should be DELETE /campaigns/{id}

# ❌ WRONG - Inconsistent responses
return {"data": campaign}               # Inconsistent format
return campaign                         # No standardization
return {"success": True, "result": campaign}  # Non-standard
```

---

## 💾 6. Database & Data Management

### ✅ MANDATORY Migration Practices

```python
# ✅ CORRECT - Alembic Migration
"""Add promotional campaigns table

Revision ID: 001_promotional_campaigns
Revises: 000_initial_schema
Create Date: 2024-01-15 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

def upgrade():
    op.create_table(
        'promotional_campaigns',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('vendor_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('budget_amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('status', sa.Enum('draft', 'active', 'paused', 'completed'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.Index('idx_campaigns_vendor_status', 'vendor_id', 'status')
    )

def downgrade():
    op.drop_table('promotional_campaigns')
```

### ✅ REQUIRED Model Definitions

```python
# ✅ CORRECT - Geo-Location Based Payment Models
class PaymentProvider(str, Enum):
    PAYSTACK = "paystack"
    STRIPE = "stripe"
    CRYPTO = "crypto"

class CryptoCurrency(str, Enum):
    BTC = "bitcoin"
    ETH = "ethereum"
    USDC = "usd_coin"
    USDT = "tether"
    CNGN = "cngn"  # Nigerian Central Bank Digital Currency

class PaymentTransaction(Base):
    __tablename__ = "payment_transactions"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), ForeignKey("vendors.id"))
    amount: Mapped[Decimal] = mapped_column(Numeric(12, 2), nullable=False)
    currency: Mapped[str] = mapped_column(String(3), nullable=False)  # ISO 4217
    provider: Mapped[PaymentProvider] = mapped_column(Enum(PaymentProvider), nullable=False)
    provider_transaction_id: Mapped[str] = mapped_column(String(255), nullable=True)
    status: Mapped[str] = mapped_column(String(50), nullable=False)
    country_code: Mapped[str] = mapped_column(String(2), nullable=False)  # ISO 3166-1
    ip_address: Mapped[str] = mapped_column(String(45), nullable=True)  # IPv4/IPv6
    metadata: Mapped[dict] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor: Mapped["Vendor"] = relationship("Vendor", back_populates="payment_transactions")

    # Constraints
    __table_args__ = (
        CheckConstraint('amount > 0', name='positive_amount_check'),
        Index('idx_payment_vendor_status', 'vendor_id', 'status'),
        Index('idx_payment_provider_status', 'provider', 'status'),
        Index('idx_payment_country_provider', 'country_code', 'provider'),
    )

class CryptoPaymentTransaction(Base):
    __tablename__ = "crypto_payment_transactions"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    payment_transaction_id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), ForeignKey("payment_transactions.id"))
    cryptocurrency: Mapped[CryptoCurrency] = mapped_column(Enum(CryptoCurrency), nullable=False)
    crypto_amount: Mapped[Decimal] = mapped_column(Numeric(18, 8), nullable=False)  # High precision for crypto
    wallet_address: Mapped[str] = mapped_column(String(255), nullable=False)
    transaction_hash: Mapped[str] = mapped_column(String(255), nullable=True)
    blockchain_confirmations: Mapped[int] = mapped_column(Integer, default=0)
    required_confirmations: Mapped[int] = mapped_column(Integer, default=3)
    crypto_provider: Mapped[str] = mapped_column(String(50), nullable=False)  # busha, coinbase, etc.
    exchange_rate: Mapped[Decimal] = mapped_column(Numeric(18, 8), nullable=False)  # Crypto to fiat rate
    network_fee: Mapped[Decimal] = mapped_column(Numeric(18, 8), nullable=True)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    confirmed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    payment_transaction: Mapped["PaymentTransaction"] = relationship("PaymentTransaction")

    # Constraints
    __table_args__ = (
        CheckConstraint('crypto_amount > 0', name='positive_crypto_amount_check'),
        CheckConstraint('exchange_rate > 0', name='positive_exchange_rate_check'),
        CheckConstraint('blockchain_confirmations >= 0', name='non_negative_confirmations_check'),
        Index('idx_crypto_payment_hash', 'transaction_hash'),
        Index('idx_crypto_payment_address', 'wallet_address'),
        Index('idx_crypto_payment_currency', 'cryptocurrency'),
        Index('idx_crypto_payment_status', 'blockchain_confirmations', 'required_confirmations'),
    )

class CryptoWalletAddress(Base):
    __tablename__ = "crypto_wallet_addresses"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    address: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    cryptocurrency: Mapped[CryptoCurrency] = mapped_column(Enum(CryptoCurrency), nullable=False)
    provider: Mapped[str] = mapped_column(String(50), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    last_used_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)

    # Constraints
    __table_args__ = (
        Index('idx_crypto_address_currency', 'cryptocurrency', 'is_active'),
        Index('idx_crypto_address_provider', 'provider', 'is_active'),
    )

class GeoLocationData(Base):
    __tablename__ = "geo_location_data"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    ip_address: Mapped[str] = mapped_column(String(45), nullable=False, unique=True)
    country_code: Mapped[str] = mapped_column(String(2), nullable=False)
    country_name: Mapped[str] = mapped_column(String(100), nullable=False)
    region: Mapped[str] = mapped_column(String(100), nullable=True)
    city: Mapped[str] = mapped_column(String(100), nullable=True)
    latitude: Mapped[float] = mapped_column(Float, nullable=True)
    longitude: Mapped[float] = mapped_column(Float, nullable=True)
    preferred_provider: Mapped[PaymentProvider] = mapped_column(Enum(PaymentProvider), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    # Constraints
    __table_args__ = (
        Index('idx_geo_country_provider', 'country_code', 'preferred_provider'),
        Index('idx_geo_ip_lookup', 'ip_address'),
    )

# ✅ CORRECT - Promotional Models (Phase 5)
class PromotionalCampaign(Base):
    __tablename__ = "promotional_campaigns"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), ForeignKey("vendors.id"))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    budget_amount: Mapped[Decimal] = mapped_column(Numeric(12, 2), nullable=False)
    status: Mapped[CampaignStatus] = mapped_column(Enum(CampaignStatus), default=CampaignStatus.DRAFT)
    payment_provider: Mapped[PaymentProvider] = mapped_column(Enum(PaymentProvider), nullable=False)
    target_countries: Mapped[list] = mapped_column(postgresql.ARRAY(String(2)), nullable=True)

    # Relationships
    vendor: Mapped["Vendor"] = relationship("Vendor", back_populates="campaigns")
    creatives: Mapped[List["AdCreative"]] = relationship("AdCreative", back_populates="campaign")
    payment_transactions: Mapped[List["PaymentTransaction"]] = relationship("PaymentTransaction")

    # Constraints
    __table_args__ = (
        CheckConstraint('budget_amount >= 1000', name='min_budget_check'),
        Index('idx_campaigns_vendor_status', 'vendor_id', 'status'),
        Index('idx_campaigns_provider', 'payment_provider'),
    )

# ✅ CORRECT - AI Integration Models
class AIOptimizationRequest(Base):
    __tablename__ = "ai_optimization_requests"

    id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id: Mapped[UUID] = mapped_column(postgresql.UUID(as_uuid=True), ForeignKey("vendors.id"))
    request_type: Mapped[str] = mapped_column(String(50), nullable=False)  # payment, campaign, market
    input_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    ai_response: Mapped[dict] = mapped_column(JSON, nullable=True)
    status: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    vendor: Mapped["Vendor"] = relationship("Vendor")

    # Constraints
    __table_args__ = (
        Index('idx_ai_vendor_type', 'vendor_id', 'request_type'),
        Index('idx_ai_status', 'status'),
    )
```

### 🚫 DATABASE VIOLATIONS

```python
# ❌ WRONG - Raw SQL queries
cursor.execute(f"SELECT * FROM campaigns WHERE vendor_id = '{vendor_id}'")  # SQL injection risk

# ❌ WRONG - Missing constraints
class Campaign(Base):
    budget_amount = Column(Decimal)  # No validation, no constraints

# ❌ WRONG - Poor indexing
class Campaign(Base):
    vendor_id = Column(UUID)  # No index on frequently queried field
```

---

## 🧪 7. Testing Requirements

### ✅ MANDATORY Test Structure

```python
# ✅ CORRECT - Unit Test Example
import pytest
from decimal import Decimal
from uuid import uuid4
from app.services.promotional_campaigns import PromotionalCampaignService
from app.schemas.promotional import PromotionalCampaignCreate

class TestPromotionalCampaignService:
    """Test suite for promotional campaign service."""

    @pytest.fixture
    def campaign_service(self, db_session):
        """Create campaign service instance."""
        return PromotionalCampaignService(db_session)

    @pytest.fixture
    def valid_campaign_data(self):
        """Valid campaign creation data."""
        return PromotionalCampaignCreate(
            name="Test Campaign",
            budget_amount=Decimal("50000"),
            campaign_type="featured_listing",
            targeting_criteria={"location": "Lagos"}
        )

    async def test_create_campaign_success(self, campaign_service, valid_campaign_data, mock_vendor):
        """Test successful campaign creation."""
        # Arrange
        vendor_id = mock_vendor.id

        # Act
        campaign = await campaign_service.create_campaign(vendor_id, valid_campaign_data)

        # Assert
        assert campaign.id is not None
        assert campaign.vendor_id == vendor_id
        assert campaign.name == "Test Campaign"
        assert campaign.budget_amount == Decimal("50000")
        assert campaign.status == CampaignStatus.DRAFT

    async def test_create_campaign_insufficient_budget(self, campaign_service, mock_vendor):
        """Test campaign creation with insufficient budget."""
        # Arrange
        invalid_data = PromotionalCampaignCreate(
            name="Test Campaign",
            budget_amount=Decimal("500"),  # Below minimum
            campaign_type="featured_listing"
        )

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await campaign_service.create_campaign(mock_vendor.id, invalid_data)

        assert "Budget must be at least ₦1,000" in str(exc_info.value)
```

### ✅ REQUIRED Integration Tests

```python
# ✅ CORRECT - API Integration Test
import pytest
from httpx import AsyncClient
from app.main import app

class TestPromotionalCampaignAPI:
    """Integration tests for promotional campaign API."""

    @pytest.mark.asyncio
    async def test_create_campaign_endpoint(self, client: AsyncClient, auth_headers):
        """Test campaign creation endpoint."""
        # Arrange
        campaign_data = {
            "name": "Integration Test Campaign",
            "budget_amount": 75000,
            "campaign_type": "sponsored_search",
            "targeting_criteria": {"location": "Abuja"}
        }

        # Act
        response = await client.post(
            "/api/v1/vendors/123e4567-e89b-12d3-a456-************/campaigns",
            json=campaign_data,
            headers=auth_headers
        )

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Integration Test Campaign"
        assert data["budget_amount"] == 75000
        assert data["status"] == "draft"
```

### 🚫 TESTING VIOLATIONS

```python
# ❌ WRONG - No assertions
def test_create_campaign():
    campaign = create_campaign()  # No assertions

# ❌ WRONG - Testing implementation details
def test_campaign_creation():
    assert campaign._internal_method() == "something"  # Testing private methods

# ❌ WRONG - No error case testing
def test_campaign_service():
    # Only testing happy path, no error scenarios
    pass
```

---

---

## 🔗 8. Integration Constraints

### ✅ MANDATORY Integration Patterns

```python
# ✅ CORRECT - Geo-Location Based Payment Router Service
class PaymentRouterService:
    """Service for routing payments based on geo-location."""

    def __init__(self, db: Session, geo_service: GeoLocationService):
        self.db = db
        self.geo_service = geo_service
        self.paystack_service = PaystackService()
        self.stripe_service = StripeService()
        self.crypto_service = CryptoPaymentService()

    async def determine_payment_provider(self, ip_address: str, country_code: str = None) -> PaymentProvider:
        """Determine appropriate payment provider based on location."""
        if not country_code:
            location_data = await self.geo_service.get_location_from_ip(ip_address)
            country_code = location_data.country_code

        # African markets use Paystack as primary
        african_countries = ['NG', 'GH', 'KE', 'ZA', 'TZ', 'UG', 'RW', 'SN', 'CI', 'BF']
        if country_code in african_countries:
            return PaymentProvider.PAYSTACK

        # Diaspora markets use Stripe as primary
        return PaymentProvider.STRIPE

    async def get_payment_provider_priority(self, ip_address: str, country_code: str = None) -> List[PaymentProvider]:
        """Get prioritized list of payment providers based on location."""
        if not country_code:
            location_data = await self.geo_service.get_location_from_ip(ip_address)
            country_code = location_data.country_code

        african_countries = ['NG', 'GH', 'KE', 'ZA', 'TZ', 'UG', 'RW', 'SN', 'CI', 'BF']

        if country_code in african_countries:
            # African Markets: Paystack → Crypto → Stripe
            return [PaymentProvider.PAYSTACK, PaymentProvider.CRYPTO, PaymentProvider.STRIPE]
        else:
            # Diaspora Markets: Stripe → Crypto → Paystack
            return [PaymentProvider.STRIPE, PaymentProvider.CRYPTO, PaymentProvider.PAYSTACK]

    async def process_payment(self, payment_data: PaymentRequest) -> PaymentResponse:
        """Process payment with appropriate provider."""
        provider = await self.determine_payment_provider(
            payment_data.ip_address,
            payment_data.country_code
        )

        # Get prioritized provider list for fallback
        provider_priority = await self.get_payment_provider_priority(
            payment_data.ip_address,
            payment_data.country_code
        )

        # If user manually selected crypto, prioritize it
        if payment_data.preferred_provider == PaymentProvider.CRYPTO:
            provider_priority = [PaymentProvider.CRYPTO] + [p for p in provider_priority if p != PaymentProvider.CRYPTO]

        last_error = None
        for provider in provider_priority:
            try:
                if provider == PaymentProvider.PAYSTACK:
                    return await self.paystack_service.process_payment(payment_data)
                elif provider == PaymentProvider.STRIPE:
                    return await self.stripe_service.process_payment(payment_data)
                elif provider == PaymentProvider.CRYPTO:
                    return await self.crypto_service.process_payment(payment_data)
            except PaymentProviderError as e:
                last_error = e
                logger.warning(f"Provider {provider} failed: {e}, trying next provider")
                continue

        # All providers failed
        raise PaymentProviderError(f"All payment providers failed. Last error: {last_error}")

# ✅ CORRECT - Cryptocurrency Payment Service Integration
class CryptoPaymentService:
    """Service for cryptocurrency payment processing using Busha.co and other providers."""

    def __init__(self, busha_client: BushaClient, blockchain_monitor: BlockchainMonitor):
        self.busha_client = busha_client
        self.blockchain_monitor = blockchain_monitor
        self.supported_currencies = [CryptoCurrency.BTC, CryptoCurrency.ETH, CryptoCurrency.USDC, CryptoCurrency.USDT, CryptoCurrency.CNGN]

    async def process_payment(self, payment_data: PaymentRequest) -> CryptoPaymentResponse:
        """Process cryptocurrency payment with Busha.co integration."""
        try:
            # Step 1: Get real-time exchange rates
            exchange_rates = await self.get_exchange_rates(payment_data.currency)

            # Step 2: Generate crypto payment invoice
            crypto_invoice = await self.generate_crypto_invoice(
                amount=payment_data.amount,
                currency=payment_data.currency,
                cryptocurrency=payment_data.preferred_crypto or CryptoCurrency.BTC,
                exchange_rates=exchange_rates
            )

            # Step 3: Generate wallet address and QR code
            wallet_address = await self.generate_wallet_address(crypto_invoice.cryptocurrency)
            qr_code_data = await self.generate_qr_code(wallet_address, crypto_invoice.crypto_amount)

            # Step 4: Store crypto transaction record
            crypto_transaction = await self.create_crypto_transaction_record(
                payment_data=payment_data,
                crypto_invoice=crypto_invoice,
                wallet_address=wallet_address
            )

            return CryptoPaymentResponse(
                transaction_id=crypto_transaction.id,
                wallet_address=wallet_address,
                crypto_amount=crypto_invoice.crypto_amount,
                cryptocurrency=crypto_invoice.cryptocurrency,
                exchange_rate=crypto_invoice.exchange_rate,
                qr_code_data=qr_code_data,
                expires_at=crypto_invoice.expires_at,
                network_fee=crypto_invoice.network_fee,
                required_confirmations=crypto_invoice.required_confirmations
            )

        except Exception as e:
            logger.error(f"Crypto payment processing failed: {e}")
            raise PaymentProviderError(f"Cryptocurrency payment failed: {e}")

    async def get_exchange_rates(self, fiat_currency: str) -> Dict[CryptoCurrency, Decimal]:
        """Get real-time crypto-to-fiat exchange rates."""
        try:
            rates = {}
            for crypto in self.supported_currencies:
                rate = await self.busha_client.get_exchange_rate(
                    from_currency=crypto.value,
                    to_currency=fiat_currency
                )
                rates[crypto] = Decimal(str(rate))
            return rates
        except Exception as e:
            logger.error(f"Failed to get exchange rates: {e}")
            raise PaymentProviderError("Unable to get current exchange rates")

    async def generate_crypto_invoice(
        self,
        amount: Decimal,
        currency: str,
        cryptocurrency: CryptoCurrency,
        exchange_rates: Dict[CryptoCurrency, Decimal]
    ) -> CryptoInvoice:
        """Generate cryptocurrency payment invoice with amount and expiration."""
        exchange_rate = exchange_rates[cryptocurrency]
        crypto_amount = amount / exchange_rate

        # Add network fee estimation
        network_fee = await self.estimate_network_fee(cryptocurrency, crypto_amount)

        # Set expiration time (15 minutes for crypto payments)
        expires_at = datetime.utcnow() + timedelta(minutes=15)

        return CryptoInvoice(
            cryptocurrency=cryptocurrency,
            crypto_amount=crypto_amount,
            fiat_amount=amount,
            fiat_currency=currency,
            exchange_rate=exchange_rate,
            network_fee=network_fee,
            expires_at=expires_at,
            required_confirmations=self.get_required_confirmations(cryptocurrency)
        )

    async def generate_wallet_address(self, cryptocurrency: CryptoCurrency) -> str:
        """Generate or retrieve wallet address for cryptocurrency payment."""
        try:
            # Use Busha.co API to generate new wallet address
            address_response = await self.busha_client.generate_address(cryptocurrency.value)
            return address_response['address']
        except Exception as e:
            logger.error(f"Failed to generate wallet address: {e}")
            raise PaymentProviderError("Unable to generate payment address")

    async def generate_qr_code(self, wallet_address: str, crypto_amount: Decimal) -> str:
        """Generate QR code for crypto payment with amount."""
        import qrcode
        import base64
        from io import BytesIO

        # Create payment URI (Bitcoin URI format, adaptable for other cryptos)
        payment_uri = f"bitcoin:{wallet_address}?amount={crypto_amount}"

        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(payment_uri)
        qr.make(fit=True)

        # Convert to base64 string for mobile app
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()

        return qr_code_base64

    async def monitor_blockchain_transaction(self, crypto_transaction_id: UUID) -> None:
        """Monitor blockchain for transaction confirmation."""
        crypto_transaction = await self.get_crypto_transaction(crypto_transaction_id)

        # Start monitoring blockchain for incoming transaction
        await self.blockchain_monitor.start_monitoring(
            wallet_address=crypto_transaction.wallet_address,
            expected_amount=crypto_transaction.crypto_amount,
            cryptocurrency=crypto_transaction.cryptocurrency,
            transaction_id=crypto_transaction_id
        )

    def get_required_confirmations(self, cryptocurrency: CryptoCurrency) -> int:
        """Get required blockchain confirmations for each cryptocurrency."""
        confirmation_requirements = {
            CryptoCurrency.BTC: 3,
            CryptoCurrency.ETH: 12,
            CryptoCurrency.USDC: 12,
            CryptoCurrency.USDT: 12,
            CryptoCurrency.CNGN: 6
        }
        return confirmation_requirements.get(cryptocurrency, 3)

# ✅ CORRECT - Blockchain Transaction Monitoring Service
class BlockchainMonitor:
    """Service for monitoring blockchain transactions and confirmations."""

    def __init__(self, web3_client: Web3Client, bitcoin_client: BitcoinClient):
        self.web3_client = web3_client
        self.bitcoin_client = bitcoin_client
        self.monitoring_tasks = {}

    async def start_monitoring(
        self,
        wallet_address: str,
        expected_amount: Decimal,
        cryptocurrency: CryptoCurrency,
        transaction_id: UUID
    ) -> None:
        """Start monitoring blockchain for incoming transaction."""
        monitoring_task = asyncio.create_task(
            self._monitor_transaction(wallet_address, expected_amount, cryptocurrency, transaction_id)
        )
        self.monitoring_tasks[transaction_id] = monitoring_task

    async def _monitor_transaction(
        self,
        wallet_address: str,
        expected_amount: Decimal,
        cryptocurrency: CryptoCurrency,
        transaction_id: UUID
    ) -> None:
        """Monitor specific transaction until confirmation."""
        timeout = 3600  # 1 hour timeout
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                if cryptocurrency == CryptoCurrency.BTC:
                    tx_info = await self._check_bitcoin_transaction(wallet_address, expected_amount)
                elif cryptocurrency in [CryptoCurrency.ETH, CryptoCurrency.USDC, CryptoCurrency.USDT]:
                    tx_info = await self._check_ethereum_transaction(wallet_address, expected_amount, cryptocurrency)
                else:
                    # Handle other cryptocurrencies
                    tx_info = await self._check_generic_transaction(wallet_address, expected_amount, cryptocurrency)

                if tx_info and tx_info['confirmations'] >= self.get_required_confirmations(cryptocurrency):
                    # Transaction confirmed, update database and notify
                    await self._handle_confirmed_transaction(transaction_id, tx_info)
                    break

            except Exception as e:
                logger.error(f"Error monitoring transaction {transaction_id}: {e}")

            # Wait 30 seconds before next check
            await asyncio.sleep(30)

        # Clean up monitoring task
        if transaction_id in self.monitoring_tasks:
            del self.monitoring_tasks[transaction_id]

    async def _handle_confirmed_transaction(self, transaction_id: UUID, tx_info: dict) -> None:
        """Handle confirmed cryptocurrency transaction."""
        # Update crypto transaction record
        await self.update_crypto_transaction(
            transaction_id=transaction_id,
            transaction_hash=tx_info['hash'],
            confirmations=tx_info['confirmations'],
            confirmed_at=datetime.utcnow()
        )

        # Trigger payment completion webhook
        await self.trigger_payment_completion_webhook(transaction_id)

# ✅ CORRECT - AI-Enhanced Payment Optimization Service
class AIPaymentOptimizationService:
    """Service for AI-powered payment optimization using AIML API."""

    def __init__(self, aiml_client: AIMLClient):
        self.aiml_client = aiml_client

    async def optimize_payment_flow(self, vendor_id: UUID, payment_history: List[PaymentTransaction]) -> PaymentOptimizationResponse:
        """Use AI to optimize payment flow for vendor."""
        optimization_prompt = f"""
        Analyze payment data for vendor {vendor_id} and provide optimization recommendations:

        Payment History: {json.dumps([p.dict() for p in payment_history], default=str)}

        Provide recommendations for:
        1. Optimal payment provider selection
        2. Currency conversion strategies
        3. Payment timing optimization
        4. Risk mitigation strategies

        Return JSON format with specific actionable recommendations.
        """

        ai_response = await self.aiml_client.generate_completion(
            prompt=optimization_prompt,
            model="gpt-4",
            max_tokens=1000
        )

        return PaymentOptimizationResponse.parse_raw(ai_response.content)

# ✅ CORRECT - Vendor Portal Integration
class PromotionalCampaignService:
    """Service integrating with existing vendor functionality."""

    def __init__(self, db: Session, vendor_service: VendorService, payment_router: PaymentRouterService):
        self.db = db
        self.vendor_service = vendor_service
        self.payment_router = payment_router

    async def create_campaign(self, vendor_id: UUID, campaign_data: CampaignCreate, ip_address: str) -> Campaign:
        """Create campaign with vendor eligibility check and payment provider selection."""
        # Check vendor eligibility using existing service
        vendor = await self.vendor_service.get_vendor_with_eligibility(vendor_id)
        if not vendor.advertising_eligible:
            raise HTTPException(403, "Vendor not eligible for advertising")

        # Determine payment provider based on vendor location
        payment_provider = await self.payment_router.determine_payment_provider(ip_address)
        campaign_data.payment_provider = payment_provider

        # Integrate with existing experience listings
        if campaign_data.experience_ids:
            experiences = await self.vendor_service.get_vendor_experiences(
                vendor_id, campaign_data.experience_ids
            )
            if len(experiences) != len(campaign_data.experience_ids):
                raise HTTPException(400, "Invalid experience IDs")

        return await self._create_campaign_internal(vendor_id, campaign_data)

# ✅ CORRECT - WebSocket Integration
class PromotionalWebSocketManager:
    """WebSocket manager for real-time promotional updates."""

    def __init__(self):
        self.active_connections: Dict[UUID, WebSocket] = {}

    async def connect(self, websocket: WebSocket, vendor_id: UUID):
        """Connect vendor to promotional updates."""
        await websocket.accept()
        self.active_connections[vendor_id] = websocket

    async def send_campaign_update(self, vendor_id: UUID, update_data: dict):
        """Send real-time campaign updates."""
        if vendor_id in self.active_connections:
            await self.active_connections[vendor_id].send_json({
                "type": "campaign_update",
                "data": update_data,
                "timestamp": datetime.utcnow().isoformat()
            })

# ✅ CORRECT - Paystack Integration
class PaystackPromotionalService:
    """Paystack integration for promotional payments."""

    def __init__(self, paystack_client: PaystackClient):
        self.paystack = paystack_client

    async def process_advertising_payment(
        self,
        vendor_id: UUID,
        amount: Decimal,
        metadata: dict
    ) -> PaymentResult:
        """Process advertising credit purchase."""
        try:
            # Initialize payment with Paystack
            payment_data = {
                "amount": int(amount * 100),  # Convert to kobo
                "email": metadata["vendor_email"],
                "metadata": {
                    "vendor_id": str(vendor_id),
                    "payment_type": "advertising_credits",
                    "campaign_id": metadata.get("campaign_id")
                },
                "callback_url": f"{settings.BASE_URL}/api/v1/payments/advertising/callback"
            }

            response = await self.paystack.initialize_transaction(payment_data)
            return PaymentResult(
                reference=response["reference"],
                authorization_url=response["authorization_url"],
                status="pending"
            )
        except PaystackError as e:
            logger.error(f"Paystack payment failed: {e}")
            raise HTTPException(500, "Payment processing failed")
```

### 🚫 INTEGRATION VIOLATIONS

```python
# ❌ WRONG - Bypassing existing services
def create_campaign(vendor_id: UUID):
    # Direct database access instead of using vendor service
    vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()  # Wrong

# ❌ WRONG - Hardcoded integrations
PAYSTACK_SECRET = "sk_test_123456789"  # Should use environment variables
websocket_url = "ws://localhost:8000"  # Should use configuration

# ❌ WRONG - No error handling for external services
def process_payment():
    response = paystack.charge()  # No try/catch for external service
    return response.data
```

---

## ⚡ 9. Performance & Scalability Guidelines

### ✅ MANDATORY Caching Strategies

```python
# ✅ CORRECT - Redis Caching Implementation
from redis import Redis
from typing import Optional
import json

class PromotionalCacheService:
    """Redis caching for promotional features."""

    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour

    async def get_campaign_performance(self, campaign_id: UUID) -> Optional[dict]:
        """Get cached campaign performance data."""
        cache_key = f"campaign_performance:{campaign_id}"
        cached_data = await self.redis.get(cache_key)

        if cached_data:
            return json.loads(cached_data)
        return None

    async def set_campaign_performance(self, campaign_id: UUID, data: dict, ttl: int = None):
        """Cache campaign performance data."""
        cache_key = f"campaign_performance:{campaign_id}"
        ttl = ttl or self.default_ttl

        await self.redis.setex(
            cache_key,
            ttl,
            json.dumps(data, default=str)
        )

# ✅ CORRECT - Database Query Optimization
class PromotionalCampaignRepository:
    """Optimized database queries for campaigns."""

    async def get_vendor_campaigns_with_performance(
        self,
        vendor_id: UUID,
        limit: int = 20,
        offset: int = 0
    ) -> List[Campaign]:
        """Get campaigns with optimized joins and pagination."""
        query = (
            select(PromotionalCampaign)
            .options(
                selectinload(PromotionalCampaign.creatives),
                selectinload(PromotionalCampaign.performance_metrics)
            )
            .where(PromotionalCampaign.vendor_id == vendor_id)
            .order_by(PromotionalCampaign.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self.db.execute(query)
        return result.scalars().all()

# ✅ CORRECT - Background Task Processing
from celery import Celery

@celery_app.task(bind=True, max_retries=3)
def update_campaign_performance(self, campaign_id: str):
    """Background task to update campaign performance metrics."""
    try:
        # Process performance updates
        campaign_service = PromotionalCampaignService()
        campaign_service.calculate_and_update_metrics(UUID(campaign_id))

    except Exception as exc:
        logger.error(f"Performance update failed for campaign {campaign_id}: {exc}")
        raise self.retry(countdown=60, exc=exc)
```

### ✅ REQUIRED Performance Monitoring

```python
# ✅ CORRECT - Performance Monitoring
import time
from functools import wraps

def monitor_performance(operation_name: str):
    """Decorator to monitor operation performance."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time

                # Log performance metrics
                logger.info(f"{operation_name} completed in {duration:.3f}s")

                # Send to monitoring system
                metrics.histogram(f"{operation_name}.duration", duration)
                metrics.increment(f"{operation_name}.success")

                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics.increment(f"{operation_name}.error")
                logger.error(f"{operation_name} failed after {duration:.3f}s: {e}")
                raise
        return wrapper
    return decorator

# Usage
@monitor_performance("campaign_creation")
async def create_campaign(vendor_id: UUID, campaign_data: CampaignCreate):
    """Monitored campaign creation."""
    pass
```

### 🚫 PERFORMANCE VIOLATIONS

```python
# ❌ WRONG - N+1 Query Problem
campaigns = db.query(Campaign).all()
for campaign in campaigns:
    creatives = db.query(Creative).filter(Creative.campaign_id == campaign.id).all()  # N+1

# ❌ WRONG - No caching for expensive operations
def get_campaign_analytics(campaign_id):
    # Expensive calculation every time, no caching
    return calculate_complex_analytics(campaign_id)

# ❌ WRONG - Blocking operations in async context
async def process_campaign():
    time.sleep(5)  # Blocking call in async function
```

---

## 🚀 10. Deployment & DevOps Constraints

### ✅ MANDATORY Docker Configuration

```dockerfile
# ✅ CORRECT - Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# ✅ CORRECT - docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - PAYSTACK_SECRET_KEY=${PAYSTACK_SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./app:/app/app
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  celery:
    build: .
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - db
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
```

### ✅ REQUIRED Environment Management

```python
# ✅ CORRECT - Environment Configuration
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """Application settings with validation."""

    # Database
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30

    # Redis
    REDIS_URL: str
    REDIS_POOL_SIZE: int = 10

    # Security
    SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 60

    # Paystack
    PAYSTACK_SECRET_KEY: str
    PAYSTACK_PUBLIC_KEY: str
    PAYSTACK_WEBHOOK_SECRET: str

    # AWS
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_S3_BUCKET: Optional[str] = None

    # Monitoring
    SENTRY_DSN: Optional[str] = None
    LOG_LEVEL: str = "INFO"

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### ✅ MANDATORY CI/CD Pipeline

```yaml
# ✅ CORRECT - .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run linting
      run: |
        flake8 app tests
        black --check app tests
        mypy app

    - name: Run tests
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-fail-under=80
      env:
        DATABASE_URL: postgresql://postgres:test@localhost/test_db
        REDIS_URL: redis://localhost:6379

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run security scan
      run: |
        pip install bandit safety
        bandit -r app/
        safety check
```

### 🚫 DEPLOYMENT VIOLATIONS

```dockerfile
# ❌ WRONG - Insecure Dockerfile
FROM python:latest  # No specific version
COPY . .
RUN pip install -r requirements.txt
USER root  # Running as root
CMD python app.py  # No proper WSGI server
```

```yaml
# ❌ WRONG - Insecure docker-compose
services:
  api:
    build: .
    environment:
      - SECRET_KEY=hardcoded_secret  # Hardcoded secrets
      - DEBUG=True  # Debug in production
```

---

## 📋 Summary of Critical Constraints

### 🚨 ABSOLUTE PROHIBITIONS

1. **NEVER** create files outside the designated workspace
2. **NEVER** use experimental or unstable libraries
3. **NEVER** expose sensitive information in error messages
4. **NEVER** bypass authentication/authorization checks
5. **NEVER** use raw SQL queries without parameterization
6. **NEVER** commit secrets or credentials to code
7. **NEVER** skip input validation on API endpoints
8. **NEVER** ignore error handling for external services
9. **NEVER** deploy without proper testing (>80% coverage)
10. **NEVER** use blocking operations in async contexts

### ✅ MANDATORY REQUIREMENTS

1. **ALWAYS** follow the established project structure
2. **ALWAYS** include comprehensive docstrings and type hints
3. **ALWAYS** validate input data using Pydantic schemas
4. **ALWAYS** implement proper error handling and logging
5. **ALWAYS** use approved technologies and exact versions
6. **ALWAYS** follow RESTful API design principles
7. **ALWAYS** implement caching for expensive operations
8. **ALWAYS** monitor performance and security metrics
9. **ALWAYS** use environment variables for configuration
10. **ALWAYS** maintain >80% test coverage

### 🎯 PHASE 5 SPECIFIC CONSTRAINTS

1. **MUST** implement all 35+ promotional API endpoints as specified
2. **MUST** integrate with existing vendor portal functionality
3. **MUST** implement dual payment provider system (Paystack + Stripe)
4. **MUST** implement geo-location based payment routing
5. **MUST** implement real-time updates via WebSocket
6. **MUST** follow the promotional database models exactly
7. **MUST** implement proper campaign performance tracking
8. **MUST** ensure security for advertising financial transactions
9. **MUST** optimize for high-volume campaign operations
10. **MUST** implement proper audit logging for all promotional activities
11. **MUST** maintain backward compatibility with existing features
12. **MUST** integrate AI optimization using AIML API

### 🌍 GEO-LOCATION PAYMENT SYSTEM CONSTRAINTS

1. **MUST** implement automatic payment provider detection based on IP/country
2. **MUST** support fallback mechanisms when primary provider fails
3. **MUST** maintain separate configurations for Paystack, Stripe, and Crypto providers
4. **MUST** implement currency conversion and localization including crypto-to-fiat
5. **MUST** track payment provider performance by region including crypto success rates
6. **MUST** ensure compliance with regional payment regulations including crypto compliance
7. **MUST** implement geo-location caching for performance
8. **MUST** support manual payment provider override for testing
9. **MUST** maintain audit trails for payment provider selection including crypto transactions
10. **MUST** implement A/B testing for payment provider optimization

### 💰 CRYPTOCURRENCY PAYMENT SYSTEM CONSTRAINTS

1. **MUST** support Bitcoin (BTC), Ethereum (ETH), USDC, USDT as primary cryptocurrencies
2. **MUST** support local African cryptocurrencies (cNGN for Nigeria) where applicable
3. **MUST** use Busha.co as primary crypto payment processor for Nigerian market
4. **MUST** implement enhanced payment router logic with crypto as secondary option
5. **MUST** provide real-time crypto-to-fiat conversion rates with price volatility handling
6. **MUST** implement QR code generation and scanning for crypto wallet addresses
7. **MUST** support blockchain transaction confirmation tracking (minimum confirmations)
8. **MUST** implement crypto payment expiration timers and automatic cancellation
9. **MUST** provide crypto payment education and onboarding for new users
10. **MUST** implement comprehensive AML/KYC compliance for crypto transactions
11. **MUST** ensure secure wallet address generation without storing private keys
12. **MUST** implement crypto transaction monitoring and fraud detection
13. **MUST** provide transparent crypto transaction fee disclosure
14. **MUST** support crypto payment history and portfolio tracking
15. **MUST** implement automatic fiat conversion upon crypto payment confirmation

### 🤖 AI INTEGRATION CONSTRAINTS

1. **MUST** use AIML API (https://aimlapi.com/) for all AI features
2. **MUST** implement retry logic with exponential backoff for AI API calls
3. **MUST** cache AI responses to minimize API costs
4. **MUST** implement fallback mechanisms when AI services are unavailable
5. **MUST** validate and sanitize all AI-generated content
6. **MUST** implement rate limiting for AI API usage
7. **MUST** track AI API usage and costs per vendor
8. **MUST** implement AI response quality scoring
9. **MUST** ensure AI recommendations are explainable and auditable
10. **MUST** implement privacy controls for AI data processing

### 📱 MULTI-PLATFORM INTEGRATION CONSTRAINTS

1. **MUST** ensure consistent payment flow across Backend API, PWA, and Mobile App
2. **MUST** implement shared payment provider detection logic
3. **MUST** maintain synchronized payment status across all platforms
4. **MUST** implement platform-specific payment optimizations
5. **MUST** ensure real-time payment updates across all platforms
6. **MUST** implement consistent error handling across platforms
7. **MUST** maintain shared payment security standards
8. **MUST** implement cross-platform payment analytics
9. **MUST** ensure consistent user experience across platforms
10. **MUST** implement platform-specific payment method support

---

**Final Guardrails Summary**: ✅ Comprehensive Development Constraints Established

This enhanced guardrails document provides strict guidelines covering all aspects of the Culture Connect Backend development, with special emphasis on:

- **Phase 5 Promotional/Advertising System** with dual payment provider support
- **Geo-Location Based Payment Routing** for African and Diaspora markets
- **AI Integration** using AIML API for intelligent optimization
- **Multi-Platform Consistency** across Backend API, PWA, and Mobile App
- **Advanced Security** for dual payment provider systems
- **Performance Optimization** for global payment processing

All AI assistants working on this project must adhere to these constraints to ensure code quality, security, performance, architectural consistency, and seamless global payment processing capabilities.
