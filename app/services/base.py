"""
Base service for Culture Connect Backend API.

This module provides the base service class with common functionality patterns
including CRUD operations, error handling, dependency injection, transaction
management, and standardized logging integration.
"""

import logging
import uuid
from abc import ABC, abstractmethod
from typing import (
    Generic, TypeVar, Type, Optional, List, Dict, Any, AsyncGenerator
)
from contextlib import asynccontextmanager
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import (
    IntegrityError, OperationalError,
    DatabaseError, TimeoutError as SQLTimeoutError
)
from fastapi import status

from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.db.session import DatabaseSessionManager
from app.db.base import BaseModel
from app.repositories.base import BaseRepository

# Type variables for generic service implementation
ModelType = TypeVar("ModelType", bound=BaseModel)
RepositoryType = TypeVar("RepositoryType", bound=BaseRepository)

logger = logging.getLogger(__name__)


class ServiceError(Exception):
    """Base exception for service layer errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "SERVICE_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        self.original_error = original_error
        super().__init__(message)


class ValidationError(ServiceError):
    """Exception for validation errors."""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            **kwargs
        )
        if field:
            self.details["field"] = field


class NotFoundError(ServiceError):
    """Exception for resource not found errors."""

    def __init__(self, resource: str, identifier: Any, **kwargs):
        super().__init__(
            message=f"{resource} not found: {identifier}",
            error_code="NOT_FOUND",
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource": resource, "identifier": str(identifier)},
            **kwargs
        )


class ConflictError(ServiceError):
    """Exception for resource conflict errors."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="CONFLICT",
            status_code=status.HTTP_409_CONFLICT,
            **kwargs
        )


class PermissionError(ServiceError):
    """Exception for permission/authorization errors."""

    def __init__(self, message: str, required_permission: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="PERMISSION_DENIED",
            status_code=status.HTTP_403_FORBIDDEN,
            **kwargs
        )
        if required_permission:
            self.details["required_permission"] = required_permission


class BaseService(Generic[ModelType, RepositoryType], ABC):
    """
    Base service class with common functionality patterns.

    Provides standardized patterns for:
    - CRUD operations with error handling
    - Transaction management with rollback capabilities
    - Dependency injection and repository management
    - Structured logging with correlation IDs
    - Performance monitoring integration
    - Comprehensive error handling and validation
    """

    def __init__(
        self,
        repository_class: Type[RepositoryType],
        model_class: Type[ModelType],
        db_session: Optional[AsyncSession] = None,
        session_manager: Optional[DatabaseSessionManager] = None
    ):
        """
        Initialize base service.

        Args:
            repository_class: Repository class for data access
            model_class: SQLAlchemy model class
            db_session: Optional database session (for dependency injection)
            session_manager: Optional session manager (defaults to global instance)
        """
        self.repository_class = repository_class
        self.model_class = model_class
        self._db_session = db_session
        # Import session_manager here to avoid circular imports
        if session_manager is None:
            from app.db.session import session_manager as default_session_manager
            self._session_manager = default_session_manager
        else:
            self._session_manager = session_manager
        self._repository: Optional[RepositoryType] = None

        # Service metadata
        self.service_name = self.__class__.__name__
        self.service_id = str(uuid.uuid4())

        # Initialize logger with service context
        self.logger = logging.getLogger(f"{__name__}.{self.service_name}")

        # Performance tracking
        self._operation_metrics = {}

    @property
    def repository(self) -> RepositoryType:
        """Get repository instance with current database session."""
        if not self._repository or (self._db_session and self._repository.db != self._db_session):
            if not self._db_session:
                raise ServiceError(
                    "Database session not available. Use service within async context or provide session.",
                    error_code="NO_DB_SESSION"
                )
            # Try to instantiate repository with just db session first (for email repositories)
            try:
                self._repository = self.repository_class(self._db_session)
            except TypeError:
                # Fallback to model_class, db_session pattern for other repositories
                self._repository = self.repository_class(self.model_class, self._db_session)
        return self._repository

    @asynccontextmanager
    async def get_session_context(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session context manager.

        Yields:
            AsyncSession: Database session for operations
        """
        if self._db_session:
            # Use provided session
            yield self._db_session
        else:
            # Create new session
            async with self._session_manager.get_session() as session:
                self._db_session = session
                yield session
                self._db_session = None

    @asynccontextmanager
    async def get_transaction_context(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database transaction context manager.

        Yields:
            AsyncSession: Database session within transaction
        """
        async with self._session_manager.get_transaction() as session:
            old_session = self._db_session
            self._db_session = session
            try:
                yield session
            finally:
                self._db_session = old_session

    def with_session(self, session: AsyncSession) -> 'BaseService':
        """
        Create service instance with specific database session.

        Args:
            session: Database session to use

        Returns:
            BaseService: Service instance with session
        """
        return self.__class__(
            repository_class=self.repository_class,
            model_class=self.model_class,
            db_session=session,
            session_manager=self._session_manager
        )

    async def handle_service_error(
        self,
        error: Exception,
        operation: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Handle service errors with comprehensive logging and monitoring.

        Args:
            error: Exception that occurred
            operation: Operation being performed
            context: Additional context information
        """
        context = context or {}
        correlation = correlation_id.get('')

        # Log error with context
        self.logger.error(
            f"Service operation failed: {operation}",
            extra={
                "correlation_id": correlation,
                "service_name": self.service_name,
                "operation": operation,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context
            },
            exc_info=True
        )

        # Track error metrics
        metrics_collector.record_business_event(
            f"error_{type(error).__name__.lower()}",
            1
        )

        # Convert to appropriate service error
        if isinstance(error, ServiceError):
            raise error
        elif isinstance(error, IntegrityError):
            raise ConflictError(
                f"Data integrity violation in {operation}",
                original_error=error,
                details=context
            )
        elif isinstance(error, (OperationalError, DatabaseError, SQLTimeoutError)):
            raise ServiceError(
                f"Database operation failed in {operation}",
                error_code="DATABASE_ERROR",
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                original_error=error,
                details=context
            )
        else:
            raise ServiceError(
                f"Unexpected error in {operation}: {str(error)}",
                error_code="INTERNAL_ERROR",
                original_error=error,
                details=context
            )

    def log_operation_start(self, operation: str, **kwargs) -> str:
        """
        Log operation start with correlation ID.

        Args:
            operation: Operation name
            **kwargs: Additional context

        Returns:
            str: Correlation ID for tracking
        """
        correlation = correlation_id.get() or str(uuid.uuid4())
        correlation_id.set(correlation)

        self.logger.info(
            f"Service operation started: {operation}",
            extra={
                "correlation_id": correlation,
                "service_name": self.service_name,
                "operation": operation,
                **kwargs
            }
        )

        # Track operation start time
        self._operation_metrics[correlation] = {
            "operation": operation,
            "start_time": datetime.utcnow(),
            "context": kwargs
        }

        return correlation

    def log_operation_success(self, correlation: str, result_summary: Optional[str] = None) -> None:
        """
        Log successful operation completion.

        Args:
            correlation: Correlation ID from operation start
            result_summary: Optional summary of operation result
        """
        if correlation in self._operation_metrics:
            metrics = self._operation_metrics.pop(correlation)
            duration = (datetime.utcnow() - metrics["start_time"]).total_seconds()

            self.logger.info(
                f"Service operation completed: {metrics['operation']}",
                extra={
                    "correlation_id": correlation,
                    "service_name": self.service_name,
                    "operation": metrics["operation"],
                    "duration": duration,
                    "result_summary": result_summary
                }
            )

            # Record performance metrics
            metrics_collector.record_business_event(
                f"operation_{metrics['operation'].lower()}_completed",
                1
            )

    @abstractmethod
    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for create operations.

        Args:
            data: Data to validate

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        pass

    @abstractmethod
    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate data for update operations.

        Args:
            data: Data to validate
            existing_id: ID of existing record

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        pass

    # CRUD Operations with standardized error handling and logging

    async def create(self, data: Dict[str, Any]) -> ModelType:
        """
        Create a new record with validation and error handling.

        Args:
            data: Data for creating the record

        Returns:
            ModelType: Created model instance

        Raises:
            ValidationError: If data validation fails
            ConflictError: If creation violates constraints
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("create", data_keys=list(data.keys()))

        try:
            # Validate input data
            validated_data = await self.validate_create_data(data)

            # Create record using repository
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                result = await repository.create(validated_data)

                self.log_operation_success(correlation, f"Created {self.model_class.__name__} with ID: {result.id}")
                return result

        except Exception as e:
            await self.handle_service_error(e, "create", {"data": data})

    async def get_by_id(self, record_id: Any) -> Optional[ModelType]:
        """
        Get record by ID with error handling.

        Args:
            record_id: Record identifier

        Returns:
            Optional[ModelType]: Model instance if found, None otherwise

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("get_by_id", record_id=record_id)

        try:
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                result = await repository.get(record_id)

                if result:
                    self.log_operation_success(correlation, f"Found {self.model_class.__name__}")
                else:
                    self.log_operation_success(correlation, f"{self.model_class.__name__} not found")

                return result

        except Exception as e:
            await self.handle_service_error(e, "get_by_id", {"record_id": record_id})

    async def get_by_id_or_raise(self, record_id: Any) -> ModelType:
        """
        Get record by ID or raise NotFoundError.

        Args:
            record_id: Record identifier

        Returns:
            ModelType: Model instance

        Raises:
            NotFoundError: If record not found
            ServiceError: If operation fails
        """
        result = await self.get_by_id(record_id)
        if not result:
            raise NotFoundError(self.model_class.__name__, record_id)
        return result

    async def update(self, record_id: Any, data: Dict[str, Any]) -> ModelType:
        """
        Update record with validation and error handling.

        Args:
            record_id: Record identifier
            data: Data for updating the record

        Returns:
            ModelType: Updated model instance

        Raises:
            NotFoundError: If record not found
            ValidationError: If data validation fails
            ConflictError: If update violates constraints
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("update", record_id=record_id, data_keys=list(data.keys()))

        try:
            # Validate input data
            validated_data = await self.validate_update_data(data, record_id)

            # Update record using repository
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                result = await repository.update(record_id, validated_data)

                if not result:
                    raise NotFoundError(self.model_class.__name__, record_id)

                self.log_operation_success(correlation, f"Updated {self.model_class.__name__} with ID: {record_id}")
                return result

        except Exception as e:
            await self.handle_service_error(e, "update", {"record_id": record_id, "data": data})

    async def delete(self, record_id: Any, soft_delete: bool = True) -> bool:
        """
        Delete record with error handling.

        Args:
            record_id: Record identifier
            soft_delete: Whether to use soft delete if supported

        Returns:
            bool: True if record was deleted, False if not found

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("delete", record_id=record_id, soft_delete=soft_delete)

        try:
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)

                if soft_delete:
                    result = await repository.soft_delete(record_id)
                    success = result is not None
                else:
                    success = await repository.delete(record_id)

                delete_type = "soft deleted" if soft_delete else "deleted"
                if success:
                    self.log_operation_success(correlation, f"{self.model_class.__name__} {delete_type}")
                else:
                    self.log_operation_success(correlation, f"{self.model_class.__name__} not found for deletion")

                return success

        except Exception as e:
            await self.handle_service_error(e, "delete", {"record_id": record_id, "soft_delete": soft_delete})

    async def list_with_pagination(
        self,
        limit: int = 100,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        List records with pagination and filtering.

        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip
            filters: Optional filters to apply
            order_by: Optional field to order by
            order_desc: Whether to order in descending order

        Returns:
            List[ModelType]: List of model instances

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "list_with_pagination",
            limit=limit,
            offset=offset,
            filters=filters,
            order_by=order_by,
            order_desc=order_desc
        )

        try:
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                results = await repository.get_multi(
                    limit=limit,
                    offset=offset,
                    filters=filters,
                    order_by=order_by,
                    order_desc=order_desc
                )

                self.log_operation_success(correlation, f"Retrieved {len(results)} {self.model_class.__name__} records")
                return results

        except Exception as e:
            await self.handle_service_error(
                e, "list_with_pagination",
                {"limit": limit, "offset": offset, "filters": filters}
            )

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering.

        Args:
            filters: Optional filters to apply

        Returns:
            int: Number of records matching criteria

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("count", filters=filters)

        try:
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                result = await repository.count(filters)

                self.log_operation_success(correlation, f"Counted {result} {self.model_class.__name__} records")
                return result

        except Exception as e:
            await self.handle_service_error(e, "count", {"filters": filters})

    async def exists(self, record_id: Any) -> bool:
        """
        Check if record exists by ID.

        Args:
            record_id: Record identifier

        Returns:
            bool: True if record exists, False otherwise

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("exists", record_id=record_id)

        try:
            async with self.get_session_context() as session:
                repository = self.repository_class(self.model_class, session)
                result = await repository.exists(record_id)

                self.log_operation_success(correlation, f"{self.model_class.__name__} exists: {result}")
                return result

        except Exception as e:
            await self.handle_service_error(e, "exists", {"record_id": record_id})

    async def bulk_create(self, data_list: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple records in bulk with validation.

        Args:
            data_list: List of data dictionaries for creating records

        Returns:
            List[ModelType]: List of created model instances

        Raises:
            ValidationError: If any data validation fails
            ConflictError: If creation violates constraints
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("bulk_create", count=len(data_list))

        try:
            # Validate all data first
            validated_data_list = []
            for i, data in enumerate(data_list):
                try:
                    validated_data = await self.validate_create_data(data)
                    validated_data_list.append(validated_data)
                except ValidationError as e:
                    e.details["bulk_index"] = i
                    raise e

            # Create records using repository
            async with self.get_transaction_context() as session:
                repository = self.repository_class(self.model_class, session)
                results = await repository.bulk_create(validated_data_list)

                self.log_operation_success(correlation, f"Bulk created {len(results)} {self.model_class.__name__} records")
                return results

        except Exception as e:
            await self.handle_service_error(e, "bulk_create", {"count": len(data_list)})


# Service registry for dependency injection and service discovery
class ServiceRegistry:
    """
    Service registry for dependency injection and service discovery.

    Provides centralized service registration, lifecycle management,
    and dependency injection capabilities.
    """

    def __init__(self):
        self._services: Dict[str, Type[BaseService]] = {}
        self._instances: Dict[str, BaseService] = {}
        self._dependencies: Dict[str, List[str]] = {}
        self.logger = logging.getLogger(f"{__name__}.ServiceRegistry")

    def register(
        self,
        service_class: Type[BaseService],
        name: Optional[str] = None,
        dependencies: Optional[List[str]] = None
    ) -> None:
        """
        Register a service class.

        Args:
            service_class: Service class to register
            name: Optional service name (defaults to class name)
            dependencies: Optional list of dependency service names
        """
        service_name = name or service_class.__name__
        self._services[service_name] = service_class
        self._dependencies[service_name] = dependencies or []

        self.logger.info(f"Registered service: {service_name}")

    def get_service(self, name: str, **kwargs) -> BaseService:
        """
        Get service instance with dependency injection.

        Args:
            name: Service name
            **kwargs: Additional arguments for service initialization

        Returns:
            BaseService: Service instance

        Raises:
            ServiceError: If service not found or initialization fails
        """
        if name not in self._services:
            raise ServiceError(f"Service not found: {name}", error_code="SERVICE_NOT_FOUND")

        # Check if instance already exists
        if name in self._instances:
            return self._instances[name]

        try:
            # Resolve dependencies
            resolved_dependencies = {}
            for dep_name in self._dependencies[name]:
                resolved_dependencies[dep_name] = self.get_service(dep_name)

            # Create service instance
            service_class = self._services[name]
            instance = service_class(**kwargs, **resolved_dependencies)

            # Cache instance
            self._instances[name] = instance

            self.logger.info(f"Created service instance: {name}")
            return instance

        except Exception as e:
            raise ServiceError(
                f"Failed to create service instance: {name}",
                error_code="SERVICE_CREATION_FAILED",
                original_error=e
            )

    def clear_instances(self) -> None:
        """Clear all cached service instances."""
        self._instances.clear()
        self.logger.info("Cleared all service instances")

    def list_services(self) -> List[str]:
        """Get list of registered service names."""
        return list(self._services.keys())


# Global service registry instance
service_registry = ServiceRegistry()
