"""
Repositories package for Culture Connect Backend API.

This package contains data access layer repositories organized by functionality:
- Base repository with common CRUD operations
- User and authentication repositories
- Vendor management repositories
- Booking and payment repositories
- Promotional and advertising repositories
- Analytics and reporting repositories

Repositories implement the data access layer between services and database models.
"""

# Base repository classes
from .base import (
    BaseRepository, PaginationParams, CursorPaginationParams,
    SortParams, FilterParams, QueryResult, CursorQueryResult,
    QueryPerformanceMetrics, RepositoryError
)

# Enhanced repository classes
from .enhanced_base import EnhancedBaseRepository
from .bulk_operations import BulkOperationsMixin, FullRepository

# Vendor repositories (implemented in Phase 1)
from .vendor_repository import VendorRepository, VendorProfileRepository, VendorDocumentRepository

# Authentication repositories (implemented in Task 2.1.1)
from .auth_repository import (
    UserRepository, TokenBlacklistRepository, SecurityEventRepository, UserSessionRepository
)

# Password security repositories (implemented in Task 2.1.2)
from .password_security_repository import (
    PasswordHistoryRepository, PasswordResetTokenRepository, AccountLockoutRepository
)

# Email service repositories (implemented in Task 2.3.1)
from .email_repository import (
    EmailTemplateRepository, EmailDeliveryRepository, EmailPreferenceRepository, EmailQueueRepository
)

# Push notification repositories (implemented in Task 2.3.2)
from .push_notification_repositories import (
    DeviceTokenRepository, NotificationTemplateRepository, NotificationDeliveryRepository,
    NotificationPreferenceRepository, NotificationQueueRepository
)

# Booking repositories (implemented in Task 4.1.1)
from .booking_repository import BookingRepository

# Booking communication repositories (implemented in Task 4.1.3)
from .booking_communication_repository import (
    BookingMessageRepository, MessageAttachmentRepository,
    MessageTemplateRepository, MessageDeliveryLogRepository,
    ValidationError, NotFoundError, ConflictError
)

# Availability repositories (implemented in Task 4.1.2)
from .availability_repository import (
    VendorAvailabilityRepository, RecurringAvailabilityRepository,
    AvailabilitySlotRepository, AvailabilityExceptionRepository
)

# Payment repositories (implemented in Step 5 - Payment & Transaction Management System)
from .payment_repository import PaymentRepository, PaymentMethodRepository
from .transaction_repository import TransactionRepository, TransactionEventRepository
from .payout_repository import VendorPayoutRepository, EscrowAccountRepository
from .financial_repository import RevenueRecordRepository, ReconciliationRecordRepository

# VPN Detection repositories (implemented in Phase 2.2 - VPN Detection Repository)
from .vpn_detection_repository import (
    VPNDetectionResultRepository, ProxyDetectionResultRepository,
    AnonymizationDetectionResultRepository, VPNDetectionAnalyticsRepository
)

# Geolocation Analytics repositories (implemented in Phase 2.2 - Analytics Dashboard)
from .geolocation_analytics_repository import (
    GeolocationAnalyticsRepository, ProviderPerformanceRepository,
    ConversionAnalyticsRepository, RoutingMetricsRepository
)

# A/B Testing repositories (implemented in Phase 2.3 - A/B Testing Framework)
from .ab_testing_repository import (
    ABTestRepository, ABTestAssignmentRepository
)
from .ab_testing_result_repository import (
    ABTestResultRepository, ABTestAnalysisRepository
)

# Review repositories (implemented in Task 4.4.1 Phase 3)
from .review_repository import ReviewRepository
from .review_response_repository import ReviewResponseRepository
from .review_moderation_repository import ReviewModerationRepository
from .review_analytics_repository import ReviewAnalyticsRepository

# WebSocket repositories (implemented in Task 6.1.1 Phase 2)
from .websocket_repositories import (
    WebSocketConnectionRepository, WebSocketEventRepository,
    UserPresenceRepository, WebSocketMetricsRepository,
    WebSocketRoomRepository, WebSocketRoomParticipantRepository
)

# Workflow repositories (implemented in Task 6.2.2 Phase 2)
from .workflow_repositories import (
    WorkflowRepository, JobDependencyRepository, WorkflowStepRepository,
    JobScheduleRepository, WorkflowAlertRepository,
    JobMonitoringSessionRepository, AlertTriggerEventRepository, AlertDeliveryRecordRepository
)

# Analytics repositories (implemented in Phase 7.1 - Analytics Dashboard System)
from .analytics_repositories import (
    AnalyticsRepository, VendorAnalyticsRepository
)
from .dashboard_repositories import (
    BookingAnalyticsRepository, SystemMetricsRepository
)
from .widget_repositories import (
    DashboardWidgetRepository, KPIDefinitionRepository
)

# Performance monitoring repositories (implemented in Phase 7.2 - Performance Monitoring System)
from .performance_monitoring_repositories import (
    PerformanceMetricsRepository, SystemHealthRepository
)

# TODO: Add other repositories as they are implemented
# from .promotion_repository import CampaignRepository, AdvertisementRepository

__all__ = [
    # Base repository classes
    "BaseRepository",
    "PaginationParams",
    "CursorPaginationParams",
    "SortParams",
    "FilterParams",
    "QueryResult",
    "CursorQueryResult",
    "QueryPerformanceMetrics",
    "RepositoryError",

    # Enhanced repository classes
    "EnhancedBaseRepository",
    "BulkOperationsMixin",
    "FullRepository",

    # Vendor repositories (implemented in Phase 1)
    "VendorRepository",
    "VendorProfileRepository",
    "VendorDocumentRepository",

    # Authentication repositories (implemented in Task 2.1.1)
    "UserRepository",
    "TokenBlacklistRepository",
    "SecurityEventRepository",
    "UserSessionRepository",

    # Password security repositories (implemented in Task 2.1.2)
    "PasswordHistoryRepository",
    "PasswordResetTokenRepository",
    "AccountLockoutRepository",

    # Email service repositories (implemented in Task 2.3.1)
    "EmailTemplateRepository",
    "EmailDeliveryRepository",
    "EmailPreferenceRepository",
    "EmailQueueRepository",

    # Push notification repositories (implemented in Task 2.3.2)
    "DeviceTokenRepository",
    "NotificationTemplateRepository",
    "NotificationDeliveryRepository",
    "NotificationPreferenceRepository",
    "NotificationQueueRepository",

    # Booking repositories (implemented in Task 4.1.1)
    "BookingRepository",

    # Booking communication repositories (implemented in Task 4.1.3)
    "BookingMessageRepository",
    "MessageAttachmentRepository",
    "MessageTemplateRepository",
    "MessageDeliveryLogRepository",
    "ValidationError",
    "NotFoundError",
    "ConflictError",

    # Availability repositories (implemented in Task 4.1.2)
    "VendorAvailabilityRepository",
    "RecurringAvailabilityRepository",
    "AvailabilitySlotRepository",
    "AvailabilityExceptionRepository",

    # Payment repositories (implemented in Step 5 - Payment & Transaction Management System)
    "PaymentRepository",
    "PaymentMethodRepository",
    "TransactionRepository",
    "TransactionEventRepository",
    "VendorPayoutRepository",
    "EscrowAccountRepository",
    "RevenueRecordRepository",
    "ReconciliationRecordRepository",

    # VPN Detection repositories (implemented in Phase 2.2 - VPN Detection Repository)
    "VPNDetectionResultRepository",
    "ProxyDetectionResultRepository",
    "AnonymizationDetectionResultRepository",
    "VPNDetectionAnalyticsRepository",

    # Geolocation Analytics repositories (implemented in Phase 2.2 - Analytics Dashboard)
    "GeolocationAnalyticsRepository",
    "ProviderPerformanceRepository",
    "ConversionAnalyticsRepository",
    "RoutingMetricsRepository",

    # A/B Testing repositories (implemented in Phase 2.3 - A/B Testing Framework)
    "ABTestRepository",
    "ABTestAssignmentRepository",
    "ABTestResultRepository",
    "ABTestAnalysisRepository",

    # Review repositories (implemented in Task 4.4.1 Phase 3)
    "ReviewRepository",
    "ReviewResponseRepository",
    "ReviewModerationRepository",
    "ReviewAnalyticsRepository",

    # WebSocket repositories (implemented in Task 6.1.1 Phase 2)
    "WebSocketConnectionRepository",
    "WebSocketEventRepository",
    "UserPresenceRepository",
    "WebSocketMetricsRepository",
    "WebSocketRoomRepository",
    "WebSocketRoomParticipantRepository",

    # Workflow repositories (implemented in Task 6.2.2 Phase 2)
    "WorkflowRepository",
    "JobDependencyRepository",
    "WorkflowStepRepository",
    "JobScheduleRepository",
    "WorkflowAlertRepository",

    # Enhanced Monitoring repositories (implemented in Task 6.2.2 Phase 4.2)
    "JobMonitoringSessionRepository",
    "AlertTriggerEventRepository",
    "AlertDeliveryRecordRepository",

    # Analytics repositories (implemented in Phase 7.1 - Analytics Dashboard System)
    "AnalyticsRepository",
    "VendorAnalyticsRepository",
    "BookingAnalyticsRepository",
    "SystemMetricsRepository",
    "DashboardWidgetRepository",
    "KPIDefinitionRepository",

    # Performance monitoring repositories (implemented in Phase 7.2 - Performance Monitoring System)
    "PerformanceMetricsRepository",
    "SystemHealthRepository",

    # TODO: Add other repositories as they are implemented
    # "CampaignRepository",
    # "AdvertisementRepository",
]
