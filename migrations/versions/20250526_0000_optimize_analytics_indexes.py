"""Optimize analytics indexes for Phase 7.3.1 Database Optimization

Revision ID: 20250526_0000_analytics_opt
Revises: 20250525_2300_perf_monitoring
Create Date: 2025-01-26 00:00:00.000000

This migration implements Phase 7.3.1 database optimization including:
- Advanced composite indexes for analytics workloads and time-series optimization
- Performance-optimized indexes for user, vendor, and booking analytics
- Time-series data optimization for performance metrics and system health
- Query optimization indexes for dashboard and reporting workloads

Performance Targets:
- Analytics queries: <200ms response time
- Time-series aggregations: <500ms for complex queries
- Dashboard loading: <1000ms for comprehensive views
- Index creation: CONCURRENTLY to avoid blocking operations
"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20250526_0000_analytics_opt'
down_revision = '20250525_2300_perf_monitoring'
branch_labels = None
depends_on = None


def upgrade():
    """Add optimized indexes for analytics workloads and time-series data."""

    # Focus on existing tables - Performance Metrics and System Health from Phase 7.2

    # Performance Metrics Time-series Optimization Indexes
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_performance_metrics_timeseries
        ON performance_metrics (timestamp DESC, component, metric_type, value)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_performance_metrics_aggregation
        ON performance_metrics (component, metric_type, timeframe, timestamp DESC)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_performance_metrics_component_analysis
        ON performance_metrics (component, timestamp DESC, value)
        WHERE metric_type IN ('API_RESPONSE_TIME', 'DATABASE_QUERY_TIME', 'THROUGHPUT')
    """)

    # System Health Monitoring Optimization Indexes
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_system_health_monitoring
        ON system_health (component, status, last_check_at DESC)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_system_health_alerting
        ON system_health (status, alert_sent, last_check_at DESC)
        WHERE status IN ('WARNING', 'CRITICAL', 'DOWN')
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_system_health_component_history
        ON system_health (component, service_name, last_check_at DESC, status)
    """)

    # Additional performance indexes for common queries
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_performance_metrics_component_type
        ON performance_metrics (component, metric_type, timestamp DESC)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_system_health_status_component
        ON system_health (status, component, last_check_at DESC)
    """)


def downgrade():
    """Remove optimized analytics indexes."""

    # Focus on existing tables - Performance Metrics and System Health from Phase 7.2

    # Performance Metrics Indexes
    op.execute("DROP INDEX IF EXISTS idx_performance_metrics_timeseries")
    op.execute("DROP INDEX IF EXISTS idx_performance_metrics_aggregation")
    op.execute("DROP INDEX IF EXISTS idx_performance_metrics_component_analysis")
    op.execute("DROP INDEX IF EXISTS idx_performance_metrics_component_type")

    # System Health Indexes
    op.execute("DROP INDEX IF EXISTS idx_system_health_monitoring")
    op.execute("DROP INDEX IF EXISTS idx_system_health_alerting")
    op.execute("DROP INDEX IF EXISTS idx_system_health_component_history")
    op.execute("DROP INDEX IF EXISTS idx_system_health_status_component")
