# Culture Connect Backend - Alertmanager Configuration
# Alert routing and notification configuration for production monitoring

apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: config
data:
  alertmanager.yml: |
    global:
      # Global SMTP configuration
      smtp_smarthost: 'smtp.cultureconnect.ng:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'REPLACE_WITH_SMTP_PASSWORD'
      smtp_require_tls: true
      
      # Global Slack configuration
      slack_api_url: 'REPLACE_WITH_SLACK_WEBHOOK_URL'
      
      # Global PagerDuty configuration
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
      
      # Resolve timeout
      resolve_timeout: 5m

    # Templates for notifications
    templates:
      - '/etc/alertmanager/templates/*.tmpl'

    # Route tree for alert routing
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default-receiver'
      
      routes:
      # Critical alerts - immediate notification
      - match:
          severity: critical
        receiver: 'critical-alerts'
        group_wait: 0s
        group_interval: 5s
        repeat_interval: 15m
        routes:
        # API down - highest priority
        - match:
            alertname: ServiceDown
        receiver: 'api-down-alerts'
        group_wait: 0s
        repeat_interval: 5m
        
        # Database issues - high priority
        - match_re:
            alertname: '(DatabaseDown|DatabaseHighConnections|DatabaseSlowQueries)'
        receiver: 'database-alerts'
        group_wait: 0s
        repeat_interval: 10m

      # Warning alerts - standard notification
      - match:
          severity: warning
        receiver: 'warning-alerts'
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 2h

      # Info alerts - low priority
      - match:
          severity: info
        receiver: 'info-alerts'
        group_wait: 5m
        group_interval: 10m
        repeat_interval: 12h

      # Component-specific routing
      - match:
          component: api
        receiver: 'api-team'
        
      - match:
          component: database
        receiver: 'database-team'
        
      - match:
          component: infrastructure
        receiver: 'devops-team'

    # Inhibition rules to reduce noise
    inhibit_rules:
    # Inhibit warning alerts if critical alert is firing
    - source_match:
        severity: 'critical'
      target_match:
        severity: 'warning'
      equal: ['alertname', 'cluster', 'service']

    # Inhibit info alerts if warning or critical is firing
    - source_match_re:
        severity: '(critical|warning)'
      target_match:
        severity: 'info'
      equal: ['alertname', 'cluster', 'service']

    # Receivers configuration
    receivers:
    # Default receiver
    - name: 'default-receiver'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Culture Connect] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Component: {{ .Labels.component }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}

    # Critical alerts - multiple channels
    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] Culture Connect Alert - {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Component: {{ .Labels.component }}
          Environment: {{ .Labels.environment }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
          
          Please investigate immediately.
      
      slack_configs:
      - channel: '#critical-alerts'
        title: '🚨 Critical Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Component:* {{ .Labels.component }}
          *Environment:* {{ .Labels.environment }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
        send_resolved: true
      
      pagerduty_configs:
      - routing_key: 'REPLACE_WITH_PAGERDUTY_INTEGRATION_KEY'
        description: '{{ .GroupLabels.alertname }} - {{ .Annotations.summary }}'
        severity: 'critical'

    # API down alerts - highest priority
    - name: 'api-down-alerts'
      email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[URGENT] Culture Connect API DOWN'
        body: |
          🔥 URGENT: Culture Connect API is DOWN 🔥
          
          The main API service is not responding. This affects all users.
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
          
          IMMEDIATE ACTION REQUIRED
      
      slack_configs:
      - channel: '#api-alerts'
        title: '🔥 API DOWN - URGENT'
        text: |
          @channel The Culture Connect API is DOWN!
          
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
        send_resolved: true

    # Database alerts
    - name: 'database-alerts'
      email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[DATABASE] Culture Connect Database Alert'
        body: |
          Database Alert for Culture Connect
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
      
      slack_configs:
      - channel: '#database-alerts'
        title: '🗄️ Database Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
        send_resolved: true

    # Warning alerts
    - name: 'warning-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] Culture Connect Alert - {{ .GroupLabels.alertname }}'
        body: |
          Warning Alert for Culture Connect
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Component: {{ .Labels.component }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
      
      slack_configs:
      - channel: '#monitoring'
        title: '⚠️ Warning - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Component:* {{ .Labels.component }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ if .Annotations.runbook_url }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
        send_resolved: true

    # Info alerts
    - name: 'info-alerts'
      slack_configs:
      - channel: '#monitoring'
        title: 'ℹ️ Info - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        send_resolved: true

    # Team-specific receivers
    - name: 'api-team'
      slack_configs:
      - channel: '#api-team'
        title: '🔧 API Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        send_resolved: true

    - name: 'database-team'
      slack_configs:
      - channel: '#database-team'
        title: '🗄️ Database Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        send_resolved: true

    - name: 'devops-team'
      slack_configs:
      - channel: '#devops'
        title: '⚙️ Infrastructure Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        send_resolved: true
