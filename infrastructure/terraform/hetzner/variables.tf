# Culture Connect Backend - Hetzner Cloud Variables
# Cost-effective self-hosting configuration variables

# General Configuration
variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "culture-connect"
}

variable "environment" {
  description = "Environment name (production, staging, development)"
  type        = string
  default     = "production"
  
  validation {
    condition     = contains(["production", "staging", "development"], var.environment)
    error_message = "Environment must be production, staging, or development."
  }
}

variable "hcloud_token" {
  description = "Hetzner Cloud API token"
  type        = string
  sensitive   = true
}

# Location and Network Configuration
variable "location" {
  description = "Hetzner Cloud location"
  type        = string
  default     = "nbg1"  # Nuremberg, Germany
  
  validation {
    condition = contains([
      "ash", "fsn1", "hel1", "nbg1", "hil"
    ], var.location)
    error_message = "Location must be a valid Hetzner Cloud location."
  }
}

variable "network_zone" {
  description = "Hetzner Cloud network zone"
  type        = string
  default     = "eu-central"
}

variable "network_cidr" {
  description = "CIDR block for the private network"
  type        = string
  default     = "10.0.0.0/16"
}

variable "subnet_cidr" {
  description = "CIDR block for the subnet"
  type        = string
  default     = "********/24"
}

# Server Configuration
variable "server_image" {
  description = "Server image to use"
  type        = string
  default     = "ubuntu-22.04"
}

variable "master_count" {
  description = "Number of master nodes"
  type        = number
  default     = 3
  
  validation {
    condition     = var.master_count >= 1 && var.master_count <= 5
    error_message = "Master count must be between 1 and 5."
  }
}

variable "worker_count" {
  description = "Number of worker nodes"
  type        = number
  default     = 3
  
  validation {
    condition     = var.worker_count >= 1 && var.worker_count <= 10
    error_message = "Worker count must be between 1 and 10."
  }
}

variable "master_server_type" {
  description = "Server type for master nodes"
  type        = string
  default     = "cpx21"  # 3 vCPU, 8GB RAM, 80GB SSD - €8.21/month
}

variable "worker_server_type" {
  description = "Server type for worker nodes"
  type        = string
  default     = "cpx31"  # 4 vCPU, 16GB RAM, 160GB SSD - €16.41/month
}

variable "database_server_type" {
  description = "Server type for database"
  type        = string
  default     = "cpx21"  # 3 vCPU, 8GB RAM, 80GB SSD - €8.21/month
}

variable "redis_server_type" {
  description = "Server type for Redis"
  type        = string
  default     = "cpx11"  # 2 vCPU, 4GB RAM, 40GB SSD - €4.51/month
}

# Load Balancer Configuration
variable "load_balancer_type" {
  description = "Load balancer type"
  type        = string
  default     = "lb11"  # Up to 5,000 concurrent connections - €5.39/month
}

variable "load_balancer_private_ip" {
  description = "Private IP for load balancer"
  type        = string
  default     = "**********"
}

# Storage Configuration
variable "database_volume_size" {
  description = "Size of database volume in GB"
  type        = number
  default     = 100
}

variable "redis_volume_size" {
  description = "Size of Redis volume in GB"
  type        = number
  default     = 20
}

# k3s Configuration
variable "k3s_version" {
  description = "k3s version to install"
  type        = string
  default     = "v1.28.4+k3s1"
}

# Database Configuration
variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
  default     = "culture_connect"
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "postgres_db" {
  description = "PostgreSQL database name"
  type        = string
  default     = "culture_connect_db"
}

# Redis Configuration
variable "redis_password" {
  description = "Redis password"
  type        = string
  sensitive   = true
}

# Security Configuration
variable "allowed_ssh_ips" {
  description = "List of IP addresses allowed to SSH"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Restrict this in production
}

variable "allowed_k8s_api_ips" {
  description = "List of IP addresses allowed to access Kubernetes API"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Restrict this in production
}

# Cost Optimization
variable "enable_spot_instances" {
  description = "Enable spot instances for cost savings (not available in Hetzner)"
  type        = bool
  default     = false
}

variable "auto_shutdown_schedule" {
  description = "Cron schedule for auto-shutdown (development environments)"
  type        = string
  default     = ""  # Empty means no auto-shutdown
}

variable "auto_startup_schedule" {
  description = "Cron schedule for auto-startup (development environments)"
  type        = string
  default     = ""  # Empty means no auto-startup
}

# Backup Configuration
variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 7
}

variable "enable_automated_backups" {
  description = "Enable automated backups"
  type        = bool
  default     = true
}

# Monitoring Configuration
variable "enable_monitoring" {
  description = "Enable monitoring stack (Prometheus, Grafana)"
  type        = bool
  default     = true
}

variable "monitoring_retention_days" {
  description = "Retention period for monitoring data in days"
  type        = number
  default     = 15  # Reduced for cost savings
}

# Domain Configuration
variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "cultureconnect.ng"
}

variable "subdomain_prefix" {
  description = "Subdomain prefix for this environment"
  type        = string
  default     = "api"
}

# Feature Flags
variable "enable_ssl_termination" {
  description = "Enable SSL termination at load balancer"
  type        = bool
  default     = true
}

variable "enable_auto_scaling" {
  description = "Enable auto-scaling (manual scaling for cost control)"
  type        = bool
  default     = false  # Manual scaling for cost control
}

variable "enable_log_aggregation" {
  description = "Enable log aggregation (ELK stack)"
  type        = bool
  default     = false  # Disabled for cost savings
}

# Resource Tagging
variable "additional_labels" {
  description = "Additional labels to apply to all resources"
  type        = map(string)
  default     = {}
}

# Cost Estimation (informational)
variable "estimated_monthly_cost_eur" {
  description = "Estimated monthly cost in EUR (informational)"
  type        = map(string)
  default = {
    masters_3x_cpx21     = "24.63"   # 3 × €8.21
    workers_3x_cpx31     = "49.23"   # 3 × €16.41
    database_cpx21       = "8.21"    # 1 × €8.21
    redis_cpx11          = "4.51"    # 1 × €4.51
    load_balancer_lb11   = "5.39"    # 1 × €5.39
    volumes_120gb        = "12.00"   # 120GB × €0.10/GB
    floating_ip          = "1.19"    # 1 × €1.19
    total_estimated      = "105.16"  # Total per month
    total_estimated_usd  = "115.00"  # Approximate USD equivalent
  }
}
