# Culture Connect AI Engine Integration API Documentation

**Target Audience**: AI integration developers and Phase 10 implementation teams  
**API Version**: v1  
**Base URL**: `https://api.cultureconnect.ng/api/v1`  
**Documentation Version**: 1.0.0  
**Last Updated**: 2025-02-01

## 🤖 Overview

The Culture Connect AI Engine Integration API provides comprehensive endpoints for integrating external AI services to power intelligent travel planning, personalized recommendations, and cultural experience discovery. This API is designed for Phase 10 implementation, enabling seamless integration with third-party AI engines while maintaining data privacy and performance standards.

### Key Features
- **Travel Planning AI**: Intelligent itinerary generation and optimization
- **Personalized Recommendations**: ML-powered experience suggestions
- **Cultural Context Analysis**: AI-driven cultural insights and matching
- **Natural Language Processing**: Conversational travel planning interface
- **Real-time Optimization**: Dynamic itinerary adjustments based on conditions
- **Multi-language Support**: AI-powered translation and localization
- **Predictive Analytics**: Demand forecasting and pricing optimization

## 🔐 Authentication & Security

### AI Service Authentication
```http
POST /ai/auth/register-service
Authorization: Bearer {admin_access_token}
Content-Type: application/json

{
  "service_info": {
    "name": "TravelGPT AI Engine",
    "provider": "OpenAI",
    "service_type": "travel_planning",
    "version": "1.0.0",
    "capabilities": [
      "itinerary_generation",
      "recommendation_engine",
      "natural_language_processing",
      "cultural_analysis"
    ]
  },
  "authentication": {
    "auth_type": "api_key",
    "api_key": "sk-ai-engine-key-*********",
    "webhook_secret": "webhook_secret_abc123",
    "rate_limits": {
      "requests_per_minute": 100,
      "requests_per_hour": 5000,
      "requests_per_day": 50000
    }
  },
  "endpoints": {
    "base_url": "https://ai-engine.travelgpt.com/api/v1",
    "health_check": "/health",
    "generate_itinerary": "/travel/itinerary",
    "get_recommendations": "/travel/recommendations",
    "analyze_preferences": "/user/preferences"
  },
  "data_privacy": {
    "data_retention_days": 30,
    "anonymize_user_data": true,
    "gdpr_compliant": true,
    "data_processing_location": "EU"
  }
}
```

**Response (201 Created):**
```json
{
  "ai_service": {
    "id": "ai_service_123",
    "name": "TravelGPT AI Engine",
    "status": "registered",
    "api_key_hash": "sha256:abc123...",
    "webhook_url": "https://api.cultureconnect.ng/api/v1/ai/webhooks/ai_service_123",
    "integration_token": "cc_ai_token_xyz789",
    "created_at": "2025-02-01T10:00:00Z"
  },
  "integration_guide": {
    "documentation_url": "https://docs.cultureconnect.ng/ai/integration",
    "sample_requests": "https://docs.cultureconnect.ng/ai/samples",
    "testing_environment": "https://staging-api.cultureconnect.ng/api/v1/ai"
  }
}
```

### API Key Management
```http
POST /ai/auth/rotate-key
Authorization: Bearer {admin_access_token}
Content-Type: application/json

{
  "service_id": "ai_service_123",
  "rotation_reason": "scheduled_rotation"
}
```

## 🧠 Travel Planning AI

### Generate Intelligent Itinerary
```http
POST /ai/travel/generate-itinerary
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "user_context": {
    "user_id": "user_456",
    "preferences": {
      "interests": ["cultural_heritage", "traditional_food", "art", "music"],
      "activity_level": "moderate",
      "budget_range": {
        "min": 50000,
        "max": 200000,
        "currency": "NGN"
      },
      "group_composition": {
        "adults": 2,
        "children": 1,
        "ages": [30, 28, 8]
      },
      "accessibility_needs": ["wheelchair_accessible"],
      "dietary_restrictions": ["vegetarian", "halal"]
    },
    "travel_history": [
      {
        "destination": "Lagos",
        "experiences": ["cultural_tour", "food_experience"],
        "satisfaction_rating": 4.8,
        "date": "2024-12-15"
      }
    ]
  },
  "trip_parameters": {
    "destination": {
      "city": "Abuja",
      "state": "FCT",
      "country": "Nigeria"
    },
    "duration": {
      "start_date": "2025-03-15",
      "end_date": "2025-03-18",
      "total_days": 4
    },
    "accommodation": {
      "type": "hotel",
      "location_preference": "city_center",
      "budget_per_night": 25000
    },
    "transportation": {
      "arrival_method": "flight",
      "local_transport_preference": "mix",
      "include_transfers": true
    }
  },
  "optimization_criteria": {
    "primary_goal": "cultural_immersion",
    "secondary_goals": ["cost_optimization", "time_efficiency"],
    "flexibility_level": "moderate",
    "weather_consideration": true,
    "local_events_integration": true
  },
  "ai_parameters": {
    "creativity_level": 0.7,
    "personalization_weight": 0.8,
    "local_insights_priority": 0.9,
    "real_time_optimization": true
  }
}
```

**Response (200 OK):**
```json
{
  "itinerary": {
    "id": "itinerary_abc123",
    "user_id": "user_456",
    "destination": "Abuja, FCT, Nigeria",
    "duration_days": 4,
    "total_estimated_cost": 180000,
    "currency": "NGN",
    "confidence_score": 0.92,
    "personalization_score": 0.88,
    "generated_at": "2025-02-01T10:00:00Z",
    "ai_model": "TravelGPT-v2.1",
    "daily_schedule": [
      {
        "day": 1,
        "date": "2025-03-15",
        "theme": "Arrival & Cultural Introduction",
        "activities": [
          {
            "time": "10:00",
            "duration_hours": 2,
            "activity": {
              "id": "exp_abuja_001",
              "title": "Abuja Cultural Heritage Tour",
              "type": "cultural_tour",
              "description": "Introduction to Nigeria's capital with focus on cultural diversity",
              "location": {
                "name": "National Arts Theatre",
                "address": "Central Business District, Abuja",
                "coordinates": {
                  "latitude": 9.0579,
                  "longitude": 7.4951
                }
              },
              "cost": 15000,
              "booking_url": "https://api.cultureconnect.ng/experiences/exp_abuja_001",
              "ai_match_score": 0.95,
              "match_reasons": [
                "Aligns with cultural heritage interest",
                "Suitable for family with child",
                "Wheelchair accessible",
                "Within budget range"
              ]
            }
          },
          {
            "time": "14:00",
            "duration_hours": 3,
            "activity": {
              "id": "exp_abuja_002",
              "title": "Traditional Nigerian Cuisine Workshop",
              "type": "food_experience",
              "description": "Hands-on cooking class featuring vegetarian Nigerian dishes",
              "location": {
                "name": "Abuja Culinary Center",
                "address": "Wuse II, Abuja",
                "coordinates": {
                  "latitude": 9.0579,
                  "longitude": 7.4951
                }
              },
              "cost": 20000,
              "booking_url": "https://api.cultureconnect.ng/experiences/exp_abuja_002",
              "ai_match_score": 0.91,
              "match_reasons": [
                "Accommodates vegetarian dietary restriction",
                "Interactive family-friendly experience",
                "Cultural food exploration interest match"
              ]
            }
          }
        ],
        "transportation": [
          {
            "from": "Nnamdi Azikiwe International Airport",
            "to": "National Arts Theatre",
            "method": "taxi",
            "duration_minutes": 45,
            "cost": 8000,
            "booking_info": "Pre-arranged airport transfer"
          }
        ],
        "meals": [
          {
            "time": "12:30",
            "type": "lunch",
            "restaurant": "Jevinik Restaurant",
            "cuisine": "Nigerian",
            "cost": 12000,
            "dietary_accommodations": ["vegetarian", "halal"],
            "ai_recommendation_score": 0.89
          }
        ],
        "daily_cost": 55000,
        "daily_summary": "Perfect introduction to Abuja's cultural landscape with hands-on experiences"
      }
    ],
    "accommodation": {
      "hotel": {
        "name": "Transcorp Hilton Abuja",
        "address": "1 Aguiyi Ironsi Street, Maitama, Abuja",
        "rating": 4.5,
        "cost_per_night": 22000,
        "total_cost": 66000,
        "amenities": ["wheelchair_accessible", "family_friendly", "halal_food"],
        "booking_url": "https://api.cultureconnect.ng/accommodations/hotel_abuja_001"
      }
    },
    "alternative_options": [
      {
        "day": 1,
        "alternative_activity": {
          "id": "exp_abuja_003",
          "title": "Abuja City Walking Tour",
          "reason": "Lower cost option if budget is tight",
          "cost_difference": -8000
        }
      }
    ],
    "weather_forecast": [
      {
        "date": "2025-03-15",
        "condition": "partly_cloudy",
        "temperature_range": "24-32°C",
        "precipitation_chance": 0.2,
        "recommendations": ["Light clothing", "Sun protection"]
      }
    ],
    "local_insights": [
      {
        "category": "cultural_etiquette",
        "insight": "Greeting elders with respect is highly valued in Nigerian culture",
        "relevance_score": 0.9
      },
      {
        "category": "practical_tips",
        "insight": "Carry cash as many local vendors prefer cash payments",
        "relevance_score": 0.8
      }
    ],
    "optimization_notes": [
      "Itinerary optimized for minimal travel time between activities",
      "All activities selected are wheelchair accessible as requested",
      "Vegetarian and halal options confirmed for all meal recommendations"
    ]
  },
  "ai_analysis": {
    "processing_time_ms": 2340,
    "data_sources_used": [
      "user_preference_history",
      "local_experience_database",
      "weather_api",
      "real_time_availability",
      "cultural_context_database"
    ],
    "confidence_factors": {
      "preference_matching": 0.92,
      "availability_accuracy": 0.89,
      "cost_estimation": 0.85,
      "weather_integration": 0.94
    },
    "personalization_applied": [
      "Cultural heritage focus based on user interests",
      "Family-friendly activities for group composition",
      "Accessibility requirements fully accommodated",
      "Dietary restrictions considered in all recommendations"
    ]
  },
  "booking_assistance": {
    "auto_booking_available": true,
    "estimated_booking_time": "15 minutes",
    "booking_support_url": "https://api.cultureconnect.ng/ai/booking-assistant",
    "payment_options": ["paystack", "stripe", "busha"],
    "cancellation_policies": "Flexible cancellation up to 24 hours before each activity"
  }
}
```

### Get AI-Powered Recommendations
```http
POST /ai/recommendations/personalized
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "user_context": {
    "user_id": "user_456",
    "current_location": {
      "latitude": 9.0579,
      "longitude": 7.4951,
      "city": "Abuja"
    },
    "preferences": {
      "interests": ["cultural_heritage", "traditional_food"],
      "budget_range": {
        "min": 10000,
        "max": 50000,
        "currency": "NGN"
      },
      "time_available_hours": 4,
      "group_size": 3
    }
  },
  "recommendation_type": "immediate_experiences",
  "ai_parameters": {
    "diversity_factor": 0.7,
    "novelty_preference": 0.6,
    "local_popularity_weight": 0.8,
    "real_time_factors": true
  }
}
```

**Response (200 OK):**
```json
{
  "recommendations": [
    {
      "experience_id": "exp_abuja_004",
      "title": "Pottery Making with Gwari Artisans",
      "ai_score": 0.94,
      "recommendation_reasons": [
        "Matches cultural heritage interest (95% confidence)",
        "Unique local experience not commonly found",
        "Perfect for 4-hour time window",
        "Highly rated by similar user profiles"
      ],
      "personalization_factors": {
        "interest_alignment": 0.95,
        "budget_fit": 0.92,
        "time_compatibility": 0.98,
        "group_suitability": 0.89
      },
      "real_time_factors": {
        "current_availability": "high",
        "weather_suitability": "excellent",
        "crowd_level": "low",
        "local_events_impact": "none"
      },
      "predicted_satisfaction": 0.91,
      "booking_urgency": "medium",
      "alternative_times": ["10:00", "14:00", "16:00"]
    }
  ],
  "ai_insights": {
    "user_profile_confidence": 0.87,
    "recommendation_diversity": 0.73,
    "local_context_integration": 0.92,
    "real_time_optimization": 0.85
  }
}
```

### Real-time Itinerary Optimization
```http
POST /ai/travel/optimize-itinerary
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "itinerary_id": "itinerary_abc123",
  "optimization_trigger": "weather_change",
  "current_conditions": {
    "weather": {
      "condition": "heavy_rain",
      "temperature": 26,
      "precipitation_probability": 0.9
    },
    "traffic": {
      "congestion_level": "high",
      "estimated_delays": 30
    },
    "availability_changes": [
      {
        "experience_id": "exp_abuja_001",
        "status": "temporarily_unavailable",
        "reason": "venue_maintenance"
      }
    ]
  },
  "optimization_preferences": {
    "maintain_budget": true,
    "preserve_key_experiences": ["exp_abuja_002"],
    "flexibility_level": "high",
    "notification_preference": "immediate"
  }
}
```

**Response (200 OK):**
```json
{
  "optimization_result": {
    "status": "optimized",
    "changes_made": 3,
    "impact_score": 0.15,
    "user_satisfaction_prediction": 0.89,
    "optimized_itinerary": {
      "day": 1,
      "changes": [
        {
          "type": "activity_replacement",
          "original_activity": "exp_abuja_001",
          "new_activity": {
            "id": "exp_abuja_005",
            "title": "Indoor Nigerian Art Gallery Experience",
            "reason": "Weather-appropriate alternative",
            "cost_difference": 0,
            "ai_match_score": 0.88
          }
        },
        {
          "type": "time_adjustment",
          "activity": "exp_abuja_002",
          "original_time": "14:00",
          "new_time": "15:30",
          "reason": "Traffic optimization"
        }
      ]
    },
    "ai_reasoning": {
      "weather_impact_mitigation": "Replaced outdoor activity with indoor alternative",
      "traffic_optimization": "Adjusted timing to avoid peak congestion",
      "budget_preservation": "Maintained total daily cost within 2% of original"
    },
    "user_notification": {
      "message": "We've optimized your itinerary due to weather changes.",
      "urgency": "medium",
      "requires_approval": false,
      "auto_applied": true
    }
  }
}
```

## 🗣️ Natural Language Processing

### Conversational Travel Planning
```http
POST /ai/nlp/travel-chat
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "conversation_context": {
    "user_id": "user_456",
    "session_id": "chat_session_789",
    "conversation_history": [
      {
        "role": "user",
        "message": "I want to plan a cultural trip to Nigeria",
        "timestamp": "2025-02-01T10:00:00Z"
      }
    ]
  },
  "user_message": {
    "text": "I'm interested in traditional music and local food. I have 5 days and $1000 budget.",
    "language": "en"
  },
  "ai_parameters": {
    "response_style": "friendly_expert",
    "detail_level": "moderate",
    "include_suggestions": true
  }
}
```

**Response (200 OK):**
```json
{
  "ai_response": {
    "message": "Perfect! Traditional music and local food are incredible ways to experience Nigerian culture. With $1000 and 5 days, I can create an amazing itinerary for you.",
    "intent_analysis": {
      "primary_intent": "trip_planning",
      "extracted_preferences": {
        "interests": ["traditional_music", "local_food"],
        "budget": 1000,
        "currency": "USD",
        "duration_days": 5
      },
      "confidence_score": 0.94
    },
    "suggested_actions": [
      {
        "action": "specify_destinations",
        "prompt": "Choose your preferred cities",
        "options": ["Lagos", "Abuja", "Kano", "Multi-city tour"]
      }
    ]
  }
}
```

## 📚 Integration Examples & Resources

### Complete AI Integration
```javascript
class CultureConnectAIService {
  constructor(apiKey, baseUrl = 'https://api.cultureconnect.ng/api/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async generateItinerary(userContext, tripParameters) {
    const response = await fetch(`${this.baseUrl}/ai/travel/generate-itinerary`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_context: userContext,
        trip_parameters: tripParameters
      })
    });

    return response.json();
  }

  async getRecommendations(userContext) {
    const response = await fetch(`${this.baseUrl}/ai/recommendations/personalized`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_context: userContext,
        recommendation_type: 'immediate_experiences'
      })
    });

    return response.json();
  }
}
```

### Additional Resources
- **Integration Guide**: `https://docs.cultureconnect.ng/ai/integration-guide`
- **API Reference**: `https://docs.cultureconnect.ng/ai/api-reference`
- **Sample Code**: `https://github.com/culture-connect/ai-integration-samples`
- **Support Email**: `<EMAIL>`
- **Slack Community**: `#culture-connect-ai-dev`

### Changelog
- **v1.0.0** (2025-02-01): Initial AI integration API release
- **New Features**: Travel planning, recommendations, NLP, and real-time optimization
