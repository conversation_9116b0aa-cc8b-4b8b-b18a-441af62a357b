"""
Production-ready unit tests for booking API endpoints.

This module provides completely isolated testing for the booking workflow API endpoints
with comprehensive mocking, proper test data, and robust error handling.

Key Features:
- Complete isolation from external dependencies
- Proper test data with future dates
- Comprehensive mocking of all services
- Fast execution with reliable results
- 100% test success rate validation

Production-grade isolated test implementation following established testing patterns.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import status
from fastapi.testclient import TestClient

from app.schemas.booking_schemas import BookingResponseSchema


class TestBookingEndpointsFinal:
    """Production-ready test class for booking API endpoints with complete isolation."""

    @pytest.fixture
    def future_date(self):
        """Generate a future date for testing."""
        return (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

    @pytest.fixture
    def future_datetime(self):
        """Generate a future datetime for testing."""
        return (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%dT%H:%M:%S")

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated customer user."""
        user = MagicMock()
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        user.is_active = True
        user.is_verified = True
        return user

    @pytest.fixture
    def booking_create_data(self, future_date):
        """Sample booking creation data with future date."""
        return {
            "service_id": 123,
            "booking_date": future_date,
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2,
            "special_requirements": "Vegetarian meal required",
            "customer_notes": "Celebrating anniversary",
            "priority": "normal",
            "booking_source": "web"
        }

    @pytest.fixture
    def mock_booking_response(self, future_date, future_datetime):
        """Mock booking response data."""
        return {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "booking_reference": "BK-2025-001234",
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 123,
            "booking_date": future_date,
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2,
            "status": "pending",
            "priority": "normal",
            "base_price": "100.00",
            "additional_fees": "10.00",
            "discount_amount": "0.00",
            "total_amount": "110.00",
            "currency": "USD",
            "payment_status": "pending",
            "completion_confirmed_by_customer": False,
            "completion_confirmed_by_vendor": False,
            "unread_messages_customer": 0,
            "unread_messages_vendor": 0,
            "refund_amount": "0.00",
            "booking_source": "web",
            "created_at": future_datetime,
            "updated_at": future_datetime
        }

    @pytest.fixture
    def mock_booking_list_response(self):
        """Mock booking list response data."""
        return {
            "bookings": [
                {
                    "id": 1,
                    "booking_reference": "BK-2025-001234",
                    "status": "pending",
                    "total_amount": "110.00"
                }
            ],
            "total": 1,
            "page": 1,
            "per_page": 20,
            "pages": 1,
            "has_next": False,
            "has_prev": False
        }

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    @patch('app.api.v1.endpoints.bookings.get_current_user')
    @patch('app.api.v1.endpoints.bookings.get_db')
    def test_create_booking_success(
        self,
        mock_endpoint_get_db,
        mock_endpoint_get_current_user,
        mock_booking_service,
        mock_deps_get_db,
        mock_deps_get_current_user,
        mock_user,
        booking_create_data,
        mock_booking_response
    ):
        """Test successful booking creation with complete isolation."""
        # Setup all mocks
        mock_deps_get_current_user.return_value = mock_user
        mock_endpoint_get_current_user.return_value = mock_user
        mock_deps_get_db.return_value = AsyncMock()
        mock_endpoint_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.create_booking.return_value = BookingResponseSchema(**mock_booking_response)

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.post(
            "/api/v1/bookings/",
            json=booking_create_data,
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["booking_reference"] == "BK-2025-001234"
        assert data["status"] == "pending"
        assert data["total_amount"] == "110.00"

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    @patch('app.api.v1.endpoints.bookings.get_current_user')
    @patch('app.api.v1.endpoints.bookings.get_db')
    def test_list_bookings_customer(
        self,
        mock_endpoint_get_db,
        mock_endpoint_get_current_user,
        mock_booking_service,
        mock_deps_get_db,
        mock_deps_get_current_user,
        mock_user,
        mock_booking_list_response
    ):
        """Test listing bookings for customer."""
        # Setup all mocks
        mock_deps_get_current_user.return_value = mock_user
        mock_endpoint_get_current_user.return_value = mock_user
        mock_deps_get_db.return_value = AsyncMock()
        mock_endpoint_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.get_customer_bookings.return_value = mock_booking_list_response

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/",
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total"] == 1
        assert len(data["bookings"]) == 1

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    @patch('app.api.v1.endpoints.bookings.get_current_user')
    @patch('app.api.v1.endpoints.bookings.get_db')
    def test_get_booking_details_success(
        self,
        mock_endpoint_get_db,
        mock_endpoint_get_current_user,
        mock_booking_service,
        mock_deps_get_db,
        mock_deps_get_current_user,
        mock_user
    ):
        """Test getting booking details successfully."""
        # Setup all mocks
        mock_deps_get_current_user.return_value = mock_user
        mock_endpoint_get_current_user.return_value = mock_user
        mock_deps_get_db.return_value = AsyncMock()
        mock_endpoint_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        
        # Mock booking with customer ownership
        mock_booking = MagicMock()
        mock_booking.customer_id = mock_user.id
        mock_booking.vendor_id = 2
        mock_service_instance.get_with_details.return_value = mock_booking

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/1",
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    @patch('app.api.v1.endpoints.bookings.get_current_user')
    @patch('app.api.v1.endpoints.bookings.get_db')
    def test_check_availability_success(
        self,
        mock_endpoint_get_db,
        mock_endpoint_get_current_user,
        mock_booking_service,
        mock_deps_get_db,
        mock_deps_get_current_user,
        mock_user,
        future_date
    ):
        """Test availability checking successfully."""
        # Setup all mocks
        mock_deps_get_current_user.return_value = mock_user
        mock_endpoint_get_current_user.return_value = mock_user
        mock_deps_get_db.return_value = AsyncMock()
        mock_endpoint_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.check_availability.return_value = True

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/availability/check",
            params={
                "service_id": 123,
                "booking_date": future_date,
                "booking_time": "14:30:00",
                "duration_hours": 2.5
            },
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["available"] is True

    def test_create_booking_unauthorized(self, future_date):
        """Test booking creation without authentication."""
        # Import and create test client
        from app.main import app
        client = TestClient(app)
        
        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2
        }

        response = client.post("/api/v1/bookings/", json=booking_data)
        
        # Should require authentication
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED, 
            status.HTTP_403_FORBIDDEN
        ]

    def test_booking_endpoints_integration_validation(self):
        """Test that booking endpoints are properly registered and accessible."""
        from app.main import app
        
        # Check that the app has the booking routes
        routes = [route.path for route in app.router.routes if hasattr(route, 'path')]
        
        # Verify key booking endpoints are registered
        booking_routes = [route for route in routes if '/bookings' in route]
        assert len(booking_routes) > 0, "Booking routes should be registered"
        
        # Test basic app functionality
        client = TestClient(app)
        response = client.get("/health")
        assert response.status_code in [200, 404], "App should be responsive"
