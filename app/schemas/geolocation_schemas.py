"""
Geolocation API Schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic schemas for geolocation API endpoints including:
- GeolocationDetectionRequest/Response: IP-based location detection with VPN analysis
- PaymentProviderRecommendationResponse: Intelligent provider selection based on geography
- SupportedCountriesResponse: Comprehensive country support with provider mappings
- GeolocationAnalyticsResponse: Performance analytics and optimization insights

Implements Phase 2.4.1 Enhanced OpenAPI Documentation requirements with
comprehensive validation, real-world examples, and production-ready schemas.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

from app.schemas.base import BaseSchema


class GeolocationDetectionRequest(BaseModel):
    """
    Request schema for IP-based geolocation detection.

    Supports both explicit IP address specification and automatic client IP detection
    with optional VPN detection and provider recommendation features.
    """

    ip_address: Optional[str] = Field(
        None,
        description="IP address to analyze (IPv4 or IPv6). If not provided, client IP will be used",
        example="************"
    )

    include_vpn_detection: bool = Field(
        default=True,
        description="Include VPN/proxy detection analysis in the response"
    )

    include_provider_recommendation: bool = Field(
        default=True,
        description="Include payment provider recommendation based on detected location"
    )

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v):
        """Validate IP address format if provided."""
        if v is not None:
            import ipaddress
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError("Invalid IP address format")
        return v


class GeolocationDetectionResponse(BaseSchema):
    """
    Response schema for IP-based geolocation detection with comprehensive metadata.

    Provides detailed geolocation information including VPN detection, risk assessment,
    and payment provider recommendations for intelligent routing decisions.
    """

    # Core geolocation data
    ip_address: str = Field(..., description="Analyzed IP address")
    country_code: Optional[str] = Field(None, description="ISO 3166-1 alpha-2 country code", example="NG")
    country_name: Optional[str] = Field(None, description="Country name", example="Nigeria")
    continent_code: Optional[str] = Field(None, description="Continent code", example="AF")
    continent_name: Optional[str] = Field(None, description="Continent name", example="Africa")

    # Detection metadata
    detection_method: str = Field(..., description="Detection method used", example="maxmind_geoip")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Detection confidence (0-1)", example=0.98)
    detection_time_ms: float = Field(..., description="Detection time in milliseconds", example=45.5)

    # VPN/Proxy detection
    vpn_detected: bool = Field(default=False, description="VPN usage detected")
    vpn_provider: Optional[str] = Field(None, description="Detected VPN provider name", example="ExpressVPN")
    vpn_confidence: float = Field(default=0.0, ge=0.0, le=1.0, description="VPN detection confidence")
    proxy_detected: bool = Field(default=False, description="Proxy usage detected")

    # Risk assessment
    risk_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall risk score", example=0.1)
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")

    # Provider recommendation
    recommended_provider: Optional[str] = Field(None, description="Recommended payment provider", example="paystack")
    provider_selection_reason: Optional[str] = Field(
        None,
        description="Reason for provider selection",
        example="Nigerian IP detected, optimal for Paystack processing"
    )
    supported_currencies: List[str] = Field(
        default_factory=list,
        description="Supported currencies for recommended provider",
        example=["NGN", "USD", "GBP", "EUR"]
    )

    # Timestamp
    detected_at: datetime = Field(..., description="Detection timestamp")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "ip_address": "************",
                "country_code": "NG",
                "country_name": "Nigeria",
                "continent_code": "AF",
                "continent_name": "Africa",
                "detection_method": "maxmind_geoip",
                "confidence_score": 0.98,
                "detection_time_ms": 45.5,
                "vpn_detected": False,
                "vpn_provider": None,
                "vpn_confidence": 0.0,
                "proxy_detected": False,
                "risk_score": 0.1,
                "risk_factors": [],
                "recommended_provider": "paystack",
                "provider_selection_reason": "Nigerian IP detected, optimal for Paystack processing",
                "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                "detected_at": "2024-01-15T10:30:00Z"
            }
        }


class PaymentProviderRecommendationResponse(BaseSchema):
    """
    Response schema for payment provider recommendations based on geographic location.

    Provides intelligent provider selection with performance metrics, fee information,
    and supported payment methods for optimal payment processing.
    """

    # Geographic context
    country_code: str = Field(..., description="Country code for recommendation", example="NG")
    country_name: str = Field(..., description="Country name", example="Nigeria")

    # Provider recommendation
    recommended_provider: str = Field(..., description="Recommended payment provider", example="paystack")
    selection_reason: str = Field(
        ...,
        description="Detailed reason for provider selection",
        example="Optimal provider for Nigerian market with local payment methods"
    )
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Recommendation confidence", example=0.95)

    # Provider capabilities
    supported_currencies: List[str] = Field(
        ...,
        description="Currencies supported by recommended provider",
        example=["NGN", "USD", "GBP", "EUR"]
    )
    supported_payment_methods: List[str] = Field(
        ...,
        description="Payment methods supported",
        example=["card", "bank_transfer", "ussd", "mobile_money"]
    )

    # Performance metrics
    estimated_success_rate: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Estimated payment success rate",
        example=0.94
    )
    average_processing_time_ms: int = Field(
        ...,
        description="Average payment processing time in milliseconds",
        example=2500
    )

    # Fee information
    transaction_fees: Dict[str, Any] = Field(
        ...,
        description="Transaction fee structure",
        example={
            "percentage": 1.5,
            "fixed_fee_ngn": 0,
            "cap_ngn": 2000
        }
    )

    class Config:
        schema_extra = {
            "example": {
                "country_code": "NG",
                "country_name": "Nigeria",
                "recommended_provider": "paystack",
                "selection_reason": "Optimal provider for Nigerian market with local payment methods",
                "confidence_score": 0.95,
                "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                "supported_payment_methods": ["card", "bank_transfer", "ussd", "mobile_money"],
                "estimated_success_rate": 0.94,
                "average_processing_time_ms": 2500,
                "transaction_fees": {
                    "percentage": 1.5,
                    "fixed_fee_ngn": 0,
                    "cap_ngn": 2000
                }
            }
        }


class CountryInfo(BaseModel):
    """Individual country information with provider support details."""

    country_code: str = Field(..., description="ISO 3166-1 alpha-2 country code", example="NG")
    country_name: str = Field(..., description="Country name", example="Nigeria")
    continent: str = Field(..., description="Continent name", example="Africa")
    primary_provider: str = Field(..., description="Primary payment provider", example="paystack")
    supported_providers: List[str] = Field(..., description="All supported providers", example=["paystack", "busha"])
    supported_currencies: List[str] = Field(..., description="Supported currencies", example=["NGN", "USD", "GBP", "EUR"])
    crypto_supported: bool = Field(..., description="Cryptocurrency support available")
    success_rate: float = Field(..., ge=0.0, le=1.0, description="Payment success rate", example=0.94)
    avg_processing_time_ms: int = Field(..., description="Average processing time", example=2500)


class SupportedCountriesResponse(BaseSchema):
    """
    Response schema for supported countries with comprehensive provider mappings.

    Provides complete overview of geographic coverage with provider statistics,
    currency support, and performance metrics for each supported country.
    """

    # Summary statistics
    total_countries: int = Field(..., description="Total number of supported countries", example=195)
    paystack_countries: int = Field(..., description="Countries supported by Paystack", example=54)
    stripe_countries: int = Field(..., description="Countries supported by Stripe", example=120)
    busha_countries: int = Field(..., description="Countries supported by Busha", example=180)

    # Detailed country information
    countries: List[CountryInfo] = Field(..., description="Detailed country information")

    # Metadata
    last_updated: datetime = Field(..., description="Last update timestamp")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "total_countries": 195,
                "paystack_countries": 54,
                "stripe_countries": 120,
                "busha_countries": 180,
                "countries": [
                    {
                        "country_code": "NG",
                        "country_name": "Nigeria",
                        "continent": "Africa",
                        "primary_provider": "paystack",
                        "supported_providers": ["paystack", "busha"],
                        "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                        "crypto_supported": True,
                        "success_rate": 0.94,
                        "avg_processing_time_ms": 2500
                    }
                ],
                "last_updated": "2024-01-15T10:30:00Z"
            }
        }


class AnalyticsSummary(BaseModel):
    """Summary analytics for geolocation routing performance."""

    total_payments: int = Field(..., description="Total payments processed", example=15420)
    total_countries: int = Field(..., description="Number of countries served", example=45)
    avg_success_rate: float = Field(..., ge=0.0, le=1.0, description="Average success rate", example=0.94)
    avg_processing_time_ms: int = Field(..., description="Average processing time", example=2100)
    vpn_detection_rate: float = Field(..., ge=0.0, le=1.0, description="VPN detection rate", example=0.12)
    top_performing_provider: str = Field(..., description="Best performing provider", example="stripe")


class ProviderPerformance(BaseModel):
    """Provider-specific performance metrics."""

    provider: str = Field(..., description="Payment provider name", example="paystack")
    total_payments: int = Field(..., description="Total payments processed", example=8500)
    success_rate: float = Field(..., ge=0.0, le=1.0, description="Payment success rate", example=0.94)
    avg_processing_time_ms: int = Field(..., description="Average processing time", example=2500)
    top_countries: List[str] = Field(..., description="Top performing countries", example=["NG", "GH", "KE"])
    revenue_share: float = Field(..., ge=0.0, le=1.0, description="Revenue share percentage", example=0.55)


class GeographicInsight(BaseModel):
    """Geographic performance insights."""

    country_code: str = Field(..., description="Country code", example="NG")
    country_name: str = Field(..., description="Country name", example="Nigeria")
    total_payments: int = Field(..., description="Total payments", example=4200)
    success_rate: float = Field(..., ge=0.0, le=1.0, description="Success rate", example=0.94)
    primary_provider: str = Field(..., description="Primary provider", example="paystack")
    vpn_detection_rate: float = Field(..., ge=0.0, le=1.0, description="VPN detection rate", example=0.08)


class OptimizationRecommendation(BaseModel):
    """Optimization recommendation for routing improvements."""

    type: str = Field(..., description="Recommendation type", example="provider_routing")
    description: str = Field(
        ...,
        description="Detailed recommendation description",
        example="Consider routing UK payments to Paystack for improved success rates"
    )
    potential_improvement: float = Field(..., description="Potential improvement percentage", example=0.03)
    confidence: float = Field(..., ge=0.0, le=1.0, description="Recommendation confidence", example=0.85)


class GeolocationAnalyticsResponse(BaseSchema):
    """
    Response schema for comprehensive geolocation routing analytics.

    Provides detailed performance analytics, geographic insights, provider comparisons,
    and optimization recommendations for data-driven routing improvements.
    """

    # Summary analytics
    summary: AnalyticsSummary = Field(..., description="Summary analytics")

    # Provider performance
    provider_performance: List[ProviderPerformance] = Field(..., description="Provider-specific metrics")

    # Geographic insights
    geographic_insights: List[GeographicInsight] = Field(..., description="Country-specific insights")

    # Optimization recommendations
    optimization_recommendations: List[OptimizationRecommendation] = Field(
        ...,
        description="Data-driven optimization recommendations"
    )

    # Metadata
    generated_at: datetime = Field(..., description="Analytics generation timestamp")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
