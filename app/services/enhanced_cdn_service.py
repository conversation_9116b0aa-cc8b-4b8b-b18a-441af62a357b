"""
Enhanced CDN Service for Culture Connect Backend API.

This module provides production-grade CDN optimization and asset delivery services including:
- EnhancedCDNService: Database-backed CDN optimization with comprehensive tracking
- Asset optimization with database persistence and performance monitoring
- Asset bundling with compression strategies achieving >30% size reduction
- CDN metrics collection and analytics for performance optimization
- Integration with Phase 7.3.2 caching and Phase 7.3.3 scaling infrastructure

Implements Phase 7.3.4 requirements for CDN optimization and asset delivery with:
- Production-grade CDN optimization with database persistence and tracking
- Asset bundling and compression strategies with comprehensive metrics
- Cache headers and content delivery optimization for <100ms asset delivery
- Performance monitoring and analytics with real-time dashboard capabilities
- Seamless integration with existing caching and scaling infrastructure
"""

import asyncio
import hashlib
import gzip
import mimetypes
import os
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from uuid import UUID

import logging
from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.core.logging import correlation_id
from app.models.cdn_models import (
    CDNConfiguration, AssetOptimization, AssetBundle, CDNMetrics,
    AssetDelivery, AssetType, OptimizationType, DeliveryStatus, CDNProvider
)
from app.repositories.cdn_repositories import (
    CDNConfigurationRepository, AssetOptimizationRepository,
    AssetBundleRepository, CDNMetricsRepository, AssetDeliveryRepository
)
from app.schemas.cdn_schemas import (
    CDNConfigurationCreate, AssetOptimizationCreate, AssetBundleCreate,
    CDNMetricsCreate, AssetDeliveryCreate
)
from app.core.circuit_breaker import get_circuit_breaker, CircuitBreakerConfig

# Optional import to avoid circular dependencies
try:
    from app.services.performance_monitoring_service import PerformanceMonitoringService
except ImportError:
    PerformanceMonitoringService = None

logger = logging.getLogger(__name__)


class EnhancedCDNService(BaseService):
    """
    Enhanced CDN service with database persistence and comprehensive tracking.

    Provides production-grade CDN optimization capabilities including:
    - Database-backed asset optimization tracking
    - Performance metrics collection and analytics
    - Asset bundling with compression strategies
    - CDN configuration management
    - Real-time delivery monitoring
    """

    def __init__(
        self,
        cdn_config_repo: CDNConfigurationRepository,
        asset_optimization_repo: AssetOptimizationRepository,
        asset_bundle_repo: AssetBundleRepository,
        cdn_metrics_repo: CDNMetricsRepository,
        asset_delivery_repo: AssetDeliveryRepository,
        performance_service: Optional[Any] = None
    ):
        """Initialize enhanced CDN service with repository dependencies."""
        super().__init__()
        self.cdn_config_repo = cdn_config_repo
        self.asset_optimization_repo = asset_optimization_repo
        self.asset_bundle_repo = asset_bundle_repo
        self.cdn_metrics_repo = cdn_metrics_repo
        self.asset_delivery_repo = asset_delivery_repo
        self.performance_service = performance_service or (PerformanceMonitoringService() if PerformanceMonitoringService else None)

        # Circuit breaker for CDN operations
        self._circuit_breaker = get_circuit_breaker(
            "enhanced_cdn_service",
            CircuitBreakerConfig(failure_threshold=5, timeout=60)
        )

    async def create_cdn_configuration(
        self,
        config_data: CDNConfigurationCreate,
        correlation_id: Optional[str] = None
    ) -> CDNConfiguration:
        """
        Create a new CDN configuration.

        Performance Metrics:
        - Target creation time: <500ms
        - Validation and persistence with comprehensive error handling

        Args:
            config_data: CDN configuration data
            correlation_id: Request correlation ID

        Returns:
            CDNConfiguration: Created configuration

        Raises:
            ValidationError: If configuration data is invalid
            ServiceError: If creation fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id or correlation_id.get('')

        try:
            # Validate configuration data
            await self._validate_cdn_configuration(config_data)

            # Check for existing default if setting as default
            if config_data.is_default:
                existing_default = await self.cdn_config_repo.get_default_configuration()
                if existing_default:
                    await self.cdn_config_repo.set_default_configuration(existing_default.id)

            # Create configuration
            config_dict = config_data.model_dump()
            if config_data.api_key:
                # In production, encrypt the API key
                config_dict["api_key_encrypted"] = self._encrypt_api_key(config_data.api_key)
                del config_dict["api_key"]

            configuration = await self.cdn_config_repo.create(config_dict)

            creation_time = time.time() - start_time
            logger.info(
                f"CDN configuration created successfully",
                extra={
                    "correlation_id": correlation_id_val,
                    "configuration_id": str(configuration.id),
                    "provider": configuration.provider,
                    "creation_time": creation_time
                }
            )

            return configuration

        except Exception as e:
            creation_time = time.time() - start_time
            logger.error(
                f"Failed to create CDN configuration: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "error": str(e),
                    "creation_time": creation_time
                }
            )
            raise ServiceError(f"Failed to create CDN configuration: {str(e)}")

    async def optimize_asset(
        self,
        asset_path: str,
        cdn_configuration_id: Optional[UUID] = None,
        optimization_type: OptimizationType = OptimizationType.COMPRESSION,
        correlation_id: Optional[str] = None
    ) -> AssetOptimization:
        """
        Optimize an asset for CDN delivery with database tracking.

        Performance Metrics:
        - Target optimization time: <2000ms for asset processing
        - Compression ratio target: >30% size reduction for compressible assets
        - Database persistence: <100ms for optimization record creation

        Args:
            asset_path: Path to the asset file
            cdn_configuration_id: CDN configuration to use (uses default if None)
            optimization_type: Type of optimization to apply
            correlation_id: Request correlation ID

        Returns:
            AssetOptimization: Optimization record with results

        Raises:
            ValidationError: If asset is invalid
            ServiceError: If optimization fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id or correlation_id.get('')

        try:
            # Get CDN configuration
            if cdn_configuration_id:
                cdn_config = await self.cdn_config_repo.get_by_id(cdn_configuration_id)
                if not cdn_config:
                    raise NotFoundError(f"CDN configuration not found: {cdn_configuration_id}")
            else:
                cdn_config = await self.cdn_config_repo.get_default_configuration()
                if not cdn_config:
                    raise ValidationError("No default CDN configuration found")

            # Validate asset file
            if not os.path.exists(asset_path):
                raise ValidationError(f"Asset file not found: {asset_path}")

            # Get file info and determine asset type
            file_stats = os.stat(asset_path)
            original_size = file_stats.st_size
            file_extension = Path(asset_path).suffix.lower().lstrip('.')
            asset_type = self._determine_asset_type(file_extension)

            # Validate file size
            max_size_bytes = float(cdn_config.max_file_size_mb) * 1024 * 1024
            if original_size > max_size_bytes:
                raise ValidationError(f"Asset file too large: {original_size} bytes")

            # Create initial optimization record
            optimization_data = AssetOptimizationCreate(
                cdn_configuration_id=cdn_config.id,
                original_path=asset_path,
                asset_type=asset_type,
                optimization_type=optimization_type
            )

            optimization_dict = optimization_data.model_dump()
            optimization_dict.update({
                "optimized_path": "",  # Will be updated after optimization
                "original_size_bytes": original_size,
                "optimized_size_bytes": original_size,  # Initial value
                "compression_ratio": Decimal("0.0"),
                "optimization_time_ms": 0,
                "delivery_status": DeliveryStatus.OPTIMIZING
            })

            optimization = await self.asset_optimization_repo.create_optimization(optimization_dict)

            try:
                # Perform optimization
                optimization_start = time.time()
                optimized_path, optimized_size = await self._optimize_asset_by_type(
                    asset_path, file_extension, optimization_type, cdn_config
                )
                optimization_time_ms = int((time.time() - optimization_start) * 1000)

                # Calculate compression ratio
                compression_ratio = Decimal(str(
                    (original_size - optimized_size) / original_size * 100
                    if original_size > 0 else 0
                ))

                # Generate cache headers and CDN URL
                cache_headers = self._generate_cache_headers(file_extension, cdn_config)
                cdn_url = self._generate_cdn_url(optimized_path, cdn_config)

                # Update optimization record
                await self.asset_optimization_repo.update_optimization_status(
                    optimization.id,
                    DeliveryStatus.READY,
                    optimized_path=optimized_path,
                    optimized_size_bytes=optimized_size,
                    compression_ratio=compression_ratio,
                    optimization_time_ms=optimization_time_ms,
                    cdn_url=cdn_url,
                    cache_headers=cache_headers
                )

                # Refresh optimization record
                optimization = await self.asset_optimization_repo.get_by_id(optimization.id)

                total_time = time.time() - start_time
                logger.info(
                    f"Asset optimization completed successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "optimization_id": str(optimization.id),
                        "asset_path": asset_path,
                        "compression_ratio": float(compression_ratio),
                        "optimization_time_ms": optimization_time_ms,
                        "total_time": total_time
                    }
                )

                return optimization

            except Exception as opt_error:
                # Update optimization record with error
                await self.asset_optimization_repo.update_optimization_status(
                    optimization.id,
                    DeliveryStatus.FAILED,
                    error_message=str(opt_error),
                    retry_count=1
                )
                raise opt_error

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(
                f"Failed to optimize asset: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "asset_path": asset_path,
                    "error": str(e),
                    "total_time": total_time
                }
            )
            raise ServiceError(f"Failed to optimize asset: {str(e)}")

    async def create_asset_bundle(
        self,
        bundle_name: str,
        asset_paths: List[str],
        bundle_type: str,
        cdn_configuration_id: Optional[UUID] = None,
        minification_enabled: bool = True,
        correlation_id: Optional[str] = None
    ) -> AssetBundle:
        """
        Create an optimized asset bundle with database tracking.

        Performance Metrics:
        - Target bundling time: <3000ms for bundle processing
        - Compression ratio target: >30% size reduction
        - Database persistence: <100ms for bundle record creation

        Args:
            bundle_name: Name for the bundle
            asset_paths: List of asset file paths to bundle
            bundle_type: Type of bundle (css, js, mixed)
            cdn_configuration_id: CDN configuration to use
            minification_enabled: Whether to enable minification
            correlation_id: Request correlation ID

        Returns:
            AssetBundle: Bundle record with optimization results

        Raises:
            ValidationError: If bundle data is invalid
            ServiceError: If bundling fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id or correlation_id.get('')

        try:
            # Get CDN configuration
            if cdn_configuration_id:
                cdn_config = await self.cdn_config_repo.get_by_id(cdn_configuration_id)
                if not cdn_config:
                    raise NotFoundError(f"CDN configuration not found: {cdn_configuration_id}")
            else:
                cdn_config = await self.cdn_config_repo.get_default_configuration()
                if not cdn_config:
                    raise ValidationError("No default CDN configuration found")

            # Validate asset files
            total_original_size = 0
            for asset_path in asset_paths:
                if not os.path.exists(asset_path):
                    raise ValidationError(f"Asset file not found: {asset_path}")
                total_original_size += os.path.getsize(asset_path)

            # Create bundle record
            bundle_data = AssetBundleCreate(
                cdn_configuration_id=cdn_config.id,
                bundle_name=bundle_name,
                bundle_type=bundle_type,
                asset_paths=asset_paths,
                minification_enabled=minification_enabled
            )

            bundle_dict = bundle_data.model_dump()
            bundle_dict.update({
                "bundled_path": "",  # Will be updated after bundling
                "total_original_size_bytes": total_original_size,
                "bundled_size_bytes": total_original_size,  # Initial value
                "compression_ratio": Decimal("0.0"),
                "bundling_time_ms": 0,
                "delivery_status": DeliveryStatus.OPTIMIZING
            })

            bundle = await self.asset_bundle_repo.create_bundle(bundle_dict)

            try:
                # Perform bundling
                bundling_start = time.time()
                bundled_path, bundled_size = await self._create_asset_bundle(
                    asset_paths, bundle_type, bundle_name, cdn_config, minification_enabled
                )
                bundling_time_ms = int((time.time() - bundling_start) * 1000)

                # Calculate compression ratio
                compression_ratio = Decimal(str(
                    (total_original_size - bundled_size) / total_original_size * 100
                    if total_original_size > 0 else 0
                ))

                # Generate cache headers and CDN URL
                cache_headers = self._generate_cache_headers(bundle_type, cdn_config)
                cdn_url = self._generate_cdn_url(bundled_path, cdn_config)

                # Generate bundle version hash
                bundle_version = hashlib.md5(
                    f"{bundle_name}:{','.join(asset_paths)}:{bundling_time_ms}".encode()
                ).hexdigest()[:8]

                # Update bundle record
                update_data = {
                    "bundled_path": bundled_path,
                    "bundled_size_bytes": bundled_size,
                    "compression_ratio": compression_ratio,
                    "bundling_time_ms": bundling_time_ms,
                    "cdn_url": cdn_url,
                    "cache_headers": cache_headers,
                    "bundle_version": bundle_version,
                    "delivery_status": DeliveryStatus.READY,
                    "updated_at": datetime.now(timezone.utc)
                }

                await self.asset_bundle_repo.update(bundle.id, update_data)

                # Refresh bundle record
                bundle = await self.asset_bundle_repo.get_by_id(bundle.id)

                total_time = time.time() - start_time
                logger.info(
                    f"Asset bundle created successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "bundle_id": str(bundle.id),
                        "bundle_name": bundle_name,
                        "asset_count": len(asset_paths),
                        "compression_ratio": float(compression_ratio),
                        "bundling_time_ms": bundling_time_ms,
                        "total_time": total_time
                    }
                )

                return bundle

            except Exception as bundle_error:
                # Update bundle record with error
                await self.asset_bundle_repo.update(
                    bundle.id,
                    {
                        "delivery_status": DeliveryStatus.FAILED,
                        "updated_at": datetime.now(timezone.utc)
                    }
                )
                raise bundle_error

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(
                f"Failed to create asset bundle: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "bundle_name": bundle_name,
                    "error": str(e),
                    "total_time": total_time
                }
            )
            raise ServiceError(f"Failed to create asset bundle: {str(e)}")

    async def record_asset_delivery(
        self,
        asset_optimization_id: UUID,
        delivery_data: AssetDeliveryCreate,
        correlation_id: Optional[str] = None
    ) -> AssetDelivery:
        """
        Record an asset delivery event for analytics.

        Args:
            asset_optimization_id: Asset optimization ID
            delivery_data: Delivery event data
            correlation_id: Request correlation ID

        Returns:
            AssetDelivery: Delivery record
        """
        correlation_id_val = correlation_id or correlation_id.get('')

        try:
            # Validate asset optimization exists
            optimization = await self.asset_optimization_repo.get_by_id(asset_optimization_id)
            if not optimization:
                raise NotFoundError(f"Asset optimization not found: {asset_optimization_id}")

            # Create delivery record
            delivery_dict = delivery_data.model_dump()
            delivery_dict["asset_optimization_id"] = asset_optimization_id

            delivery = await self.asset_delivery_repo.create_delivery_record(delivery_dict)

            logger.debug(
                f"Asset delivery recorded",
                extra={
                    "correlation_id": correlation_id_val,
                    "delivery_id": str(delivery.id),
                    "asset_optimization_id": str(asset_optimization_id),
                    "response_time_ms": delivery.response_time_ms,
                    "cache_status": delivery.cache_status
                }
            )

            return delivery

        except Exception as e:
            logger.error(
                f"Failed to record asset delivery: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "asset_optimization_id": str(asset_optimization_id),
                    "error": str(e)
                }
            )
            raise ServiceError(f"Failed to record asset delivery: {str(e)}")

    async def _validate_cdn_configuration(self, config_data: CDNConfigurationCreate):
        """Validate CDN configuration data."""
        if not config_data.name or not config_data.name.strip():
            raise ValidationError("CDN configuration name is required")

        if not config_data.base_url or not config_data.base_url.strip():
            raise ValidationError("CDN base URL is required")

        if config_data.cache_ttl_seconds <= 0:
            raise ValidationError("Cache TTL must be positive")

        if config_data.max_file_size_mb <= 0:
            raise ValidationError("Max file size must be positive")

    def _determine_asset_type(self, file_extension: str) -> AssetType:
        """Determine asset type from file extension."""
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'ico']
        if file_extension in image_extensions:
            return AssetType.IMAGE
        elif file_extension == 'css':
            return AssetType.STYLESHEET
        elif file_extension == 'js':
            return AssetType.JAVASCRIPT
        elif file_extension in ['woff', 'woff2', 'ttf', 'otf']:
            return AssetType.FONT
        elif file_extension in ['mp4', 'webm', 'ogg']:
            return AssetType.VIDEO
        elif file_extension in ['pdf', 'doc', 'docx']:
            return AssetType.DOCUMENT
        else:
            return AssetType.OTHER

    def _encrypt_api_key(self, api_key: str) -> str:
        """Encrypt API key for storage (simplified implementation)."""
        # In production, use proper encryption like Fernet or AWS KMS
        return hashlib.sha256(api_key.encode()).hexdigest()

    async def _optimize_asset_by_type(
        self,
        asset_path: str,
        file_extension: str,
        optimization_type: OptimizationType,
        cdn_config: CDNConfiguration
    ) -> Tuple[str, int]:
        """Optimize asset based on type and configuration."""
        if optimization_type == OptimizationType.COMPRESSION:
            return await self._compress_asset(asset_path, cdn_config)
        elif optimization_type == OptimizationType.MINIFICATION:
            return await self._minify_asset(asset_path, file_extension)
        else:
            # Default compression
            return await self._compress_asset(asset_path, cdn_config)

    async def _compress_asset(self, asset_path: str, cdn_config: CDNConfiguration) -> Tuple[str, int]:
        """Compress asset using gzip."""
        if not cdn_config.compression_enabled:
            # Return original file if compression disabled
            return asset_path, os.path.getsize(asset_path)

        compressed_path = f"{asset_path}.gz"

        with open(asset_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                f_out.write(f_in.read())

        return compressed_path, os.path.getsize(compressed_path)

    async def _minify_asset(self, asset_path: str, file_extension: str) -> Tuple[str, int]:
        """Minify asset (simplified implementation)."""
        # In production, use proper minification libraries
        minified_path = f"{asset_path}.min"

        if file_extension in ['js', 'css', 'html']:
            # Simple minification - remove extra whitespace
            with open(asset_path, 'r', encoding='utf-8') as f_in:
                content = f_in.read()

            # Basic minification
            minified_content = ' '.join(content.split())

            with open(minified_path, 'w', encoding='utf-8') as f_out:
                f_out.write(minified_content)
        else:
            # For non-text files, just copy
            with open(asset_path, 'rb') as f_in:
                with open(minified_path, 'wb') as f_out:
                    f_out.write(f_in.read())

        return minified_path, os.path.getsize(minified_path)

    async def _create_asset_bundle(
        self,
        asset_paths: List[str],
        bundle_type: str,
        bundle_name: str,
        cdn_config: CDNConfiguration,
        minification_enabled: bool
    ) -> Tuple[str, int]:
        """Create asset bundle from multiple files."""
        # Generate bundle filename
        bundle_hash = hashlib.md5(f"{bundle_name}:{','.join(asset_paths)}".encode()).hexdigest()[:8]
        bundle_filename = f"{bundle_name}_{bundle_hash}.{bundle_type}"
        bundle_path = f"/tmp/{bundle_filename}"

        # Combine files
        combined_content = ""
        for asset_path in asset_paths:
            if os.path.exists(asset_path):
                with open(asset_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if minification_enabled and cdn_config.minification_enabled:
                        # Basic minification
                        content = ' '.join(content.split())
                    combined_content += content + "\n"

        # Write bundle file
        with open(bundle_path, 'w', encoding='utf-8') as f:
            f.write(combined_content)

        # Compress if enabled
        if cdn_config.compression_enabled:
            compressed_path = f"{bundle_path}.gz"
            with open(bundle_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.write(f_in.read())
            return compressed_path, os.path.getsize(compressed_path)
        else:
            return bundle_path, os.path.getsize(bundle_path)

    def _generate_cache_headers(self, file_extension: str, cdn_config: CDNConfiguration) -> Dict[str, str]:
        """Generate cache headers based on asset type and configuration."""
        # Use configuration cache headers if available
        if cdn_config.cache_headers:
            return cdn_config.cache_headers.copy()

        # Default cache strategies
        if file_extension in ["jpg", "jpeg", "png", "gif", "webp", "svg", "ico"]:
            max_age = 2592000  # 30 days for images
        elif file_extension in ["css", "js"]:
            max_age = 604800   # 7 days for stylesheets and scripts
        else:
            max_age = cdn_config.cache_ttl_seconds

        headers = {
            "Cache-Control": f"public, max-age={max_age}",
            "Expires": (datetime.now(timezone.utc) + timedelta(seconds=max_age)).strftime("%a, %d %b %Y %H:%M:%S GMT"),
            "ETag": f'"{hashlib.md5(str(time.time()).encode()).hexdigest()}"'
        }

        if cdn_config.compression_enabled:
            headers["Content-Encoding"] = "gzip"

        return headers

    def _generate_cdn_url(self, asset_path: str, cdn_config: CDNConfiguration) -> str:
        """Generate CDN URL for optimized asset."""
        filename = os.path.basename(asset_path)
        return f"{cdn_config.base_url}/assets/{filename}"
