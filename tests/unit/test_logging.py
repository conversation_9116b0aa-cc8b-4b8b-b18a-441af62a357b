"""
Unit tests for logging infrastructure.

This module tests the comprehensive logging system including:
- Structured logging configuration
- Log rotation and retention
- Request/response logging with correlation IDs
- Security-focused logging
- Performance logging
- Business event logging
"""

import json
import logging
import os
import tempfile
import uuid
from datetime import datetime
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from fastapi import Request, Response
from fastapi.testclient import TestClient

from app.core.logging import (
    setup_logging,
    CorrelationIdFilter,
    JSONFormatter,
    SecurityLogger,
    PerformanceLogger,
    BusinessLogger,
    CorrelationIdMiddleware,
    EnhancedRequestLoggingMiddleware,
    correlation_id
)


class TestCorrelationIdFilter:
    """Test cases for CorrelationIdFilter."""

    def test_filter_adds_correlation_id(self):
        """Test that filter adds correlation ID to log record."""
        filter_instance = CorrelationIdFilter()
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="test message",
            args=(),
            exc_info=None
        )
        
        # Set correlation ID in context
        test_correlation_id = "test-correlation-id"
        correlation_id.set(test_correlation_id)
        
        # Apply filter
        result = filter_instance.filter(record)
        
        assert result is True
        assert hasattr(record, 'correlation_id')
        assert record.correlation_id == test_correlation_id

    def test_filter_with_empty_correlation_id(self):
        """Test filter with empty correlation ID."""
        filter_instance = CorrelationIdFilter()
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="test message",
            args=(),
            exc_info=None
        )
        
        # Clear correlation ID
        correlation_id.set('')
        
        # Apply filter
        result = filter_instance.filter(record)
        
        assert result is True
        assert hasattr(record, 'correlation_id')
        assert record.correlation_id == ''


class TestJSONFormatter:
    """Test cases for JSONFormatter."""

    def test_format_basic_record(self):
        """Test formatting basic log record as JSON."""
        formatter = JSONFormatter()
        record = logging.LogRecord(
            name="test.logger",
            level=logging.INFO,
            pathname="/path/to/file.py",
            lineno=42,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.correlation_id = "test-correlation-id"
        
        formatted = formatter.format(record)
        parsed = json.loads(formatted)
        
        assert parsed['level'] == 'INFO'
        assert parsed['logger'] == 'test.logger'
        assert parsed['message'] == 'Test message'
        assert parsed['line'] == 42
        assert parsed['correlation_id'] == 'test-correlation-id'
        assert 'timestamp' in parsed
        assert 'environment' in parsed

    def test_format_record_with_exception(self):
        """Test formatting log record with exception info."""
        formatter = JSONFormatter()
        
        try:
            raise ValueError("Test exception")
        except ValueError:
            record = logging.LogRecord(
                name="test.logger",
                level=logging.ERROR,
                pathname="/path/to/file.py",
                lineno=42,
                msg="Error occurred",
                args=(),
                exc_info=True
            )
            record.correlation_id = "test-correlation-id"
            
            formatted = formatter.format(record)
            parsed = json.loads(formatted)
            
            assert parsed['level'] == 'ERROR'
            assert parsed['message'] == 'Error occurred'
            assert 'exception' in parsed
            assert 'ValueError' in parsed['exception']

    def test_format_record_with_extra_fields(self):
        """Test formatting log record with extra fields."""
        formatter = JSONFormatter()
        record = logging.LogRecord(
            name="test.logger",
            level=logging.INFO,
            pathname="/path/to/file.py",
            lineno=42,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.correlation_id = "test-correlation-id"
        record.user_id = 123
        record.event_type = "test_event"
        
        formatted = formatter.format(record)
        parsed = json.loads(formatted)
        
        assert parsed['user_id'] == 123
        assert parsed['event_type'] == 'test_event'


class TestSecurityLogger:
    """Test cases for SecurityLogger."""

    def test_log_authentication_attempt(self):
        """Test logging authentication attempt."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            security_logger = SecurityLogger()
            
            security_logger.log_authentication_attempt(
                email="<EMAIL>",
                success=True,
                ip_address="***********",
                user_agent="Mozilla/5.0"
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Authentication attempt"
            assert call_args[1]['extra']['event_type'] == 'authentication'
            assert call_args[1]['extra']['email'] == '<EMAIL>'
            assert call_args[1]['extra']['success'] is True
            assert call_args[1]['extra']['ip_address'] == '***********'

    def test_log_permission_check(self):
        """Test logging permission check."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            security_logger = SecurityLogger()
            
            security_logger.log_permission_check(
                user_id=123,
                permission="read_vendor",
                resource="vendor:456",
                granted=True
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Permission check"
            assert call_args[1]['extra']['event_type'] == 'permission_check'
            assert call_args[1]['extra']['user_id'] == 123
            assert call_args[1]['extra']['permission'] == 'read_vendor'
            assert call_args[1]['extra']['granted'] is True

    def test_log_suspicious_activity(self):
        """Test logging suspicious activity."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            security_logger = SecurityLogger()
            
            security_logger.log_suspicious_activity(
                user_id=123,
                activity="multiple_failed_logins",
                details={"attempts": 5, "timeframe": "5 minutes"}
            )
            
            mock_logger.warning.assert_called_once()
            call_args = mock_logger.warning.call_args
            
            assert call_args[0][0] == "Suspicious activity detected"
            assert call_args[1]['extra']['event_type'] == 'suspicious_activity'
            assert call_args[1]['extra']['activity'] == 'multiple_failed_logins'
            assert call_args[1]['extra']['details']['attempts'] == 5

    def test_log_data_access(self):
        """Test logging data access."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            security_logger = SecurityLogger()
            
            security_logger.log_data_access(
                user_id=123,
                resource_type="vendor",
                resource_id="456",
                action="read"
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Data access"
            assert call_args[1]['extra']['event_type'] == 'data_access'
            assert call_args[1]['extra']['user_id'] == 123
            assert call_args[1]['extra']['resource_type'] == 'vendor'
            assert call_args[1]['extra']['action'] == 'read'


class TestPerformanceLogger:
    """Test cases for PerformanceLogger."""

    def test_log_request_metrics(self):
        """Test logging request performance metrics."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            performance_logger = PerformanceLogger()
            
            performance_logger.log_request_metrics(
                method="GET",
                path="/api/v1/vendors",
                status_code=200,
                duration=0.123,
                user_id=123
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Request metrics"
            assert call_args[1]['extra']['event_type'] == 'request_metrics'
            assert call_args[1]['extra']['method'] == 'GET'
            assert call_args[1]['extra']['path'] == '/api/v1/vendors'
            assert call_args[1]['extra']['status_code'] == 200
            assert call_args[1]['extra']['duration'] == 0.123

    def test_log_database_query(self):
        """Test logging database query performance."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            performance_logger = PerformanceLogger()
            
            performance_logger.log_database_query(
                query_type="SELECT",
                table="vendors",
                duration=0.045,
                rows_affected=10
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Database query metrics"
            assert call_args[1]['extra']['event_type'] == 'database_query'
            assert call_args[1]['extra']['query_type'] == 'SELECT'
            assert call_args[1]['extra']['table'] == 'vendors'
            assert call_args[1]['extra']['duration'] == 0.045
            assert call_args[1]['extra']['rows_affected'] == 10

    def test_log_external_api_call(self):
        """Test logging external API call performance."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            performance_logger = PerformanceLogger()
            
            performance_logger.log_external_api_call(
                service="paystack",
                endpoint="/transaction/initialize",
                duration=0.234,
                status_code=200,
                success=True
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "External API call metrics"
            assert call_args[1]['extra']['event_type'] == 'external_api_call'
            assert call_args[1]['extra']['service'] == 'paystack'
            assert call_args[1]['extra']['endpoint'] == '/transaction/initialize'
            assert call_args[1]['extra']['success'] is True


class TestBusinessLogger:
    """Test cases for BusinessLogger."""

    def test_log_vendor_registration(self):
        """Test logging vendor registration."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            business_logger = BusinessLogger()
            
            business_logger.log_vendor_registration(
                vendor_id=123,
                user_id=456,
                business_type="restaurant"
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Vendor registration"
            assert call_args[1]['extra']['event_type'] == 'vendor_registration'
            assert call_args[1]['extra']['vendor_id'] == 123
            assert call_args[1]['extra']['user_id'] == 456
            assert call_args[1]['extra']['business_type'] == 'restaurant'

    def test_log_payment_transaction(self):
        """Test logging payment transaction."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            business_logger = BusinessLogger()
            
            business_logger.log_payment_transaction(
                transaction_id="txn_123",
                amount=100.50,
                currency="NGN",
                payment_method="paystack",
                status="completed",
                user_id=456
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Payment transaction"
            assert call_args[1]['extra']['event_type'] == 'payment_transaction'
            assert call_args[1]['extra']['transaction_id'] == 'txn_123'
            assert call_args[1]['extra']['amount'] == 100.50
            assert call_args[1]['extra']['currency'] == 'NGN'
            assert call_args[1]['extra']['payment_method'] == 'paystack'

    def test_log_promotional_campaign(self):
        """Test logging promotional campaign."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            business_logger = BusinessLogger()
            
            business_logger.log_promotional_campaign(
                campaign_id=789,
                vendor_id=123,
                campaign_type="sponsored_listing",
                budget=500.0,
                action="created"
            )
            
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            
            assert call_args[0][0] == "Promotional campaign"
            assert call_args[1]['extra']['event_type'] == 'promotional_campaign'
            assert call_args[1]['extra']['campaign_id'] == 789
            assert call_args[1]['extra']['vendor_id'] == 123
            assert call_args[1]['extra']['campaign_type'] == 'sponsored_listing'
            assert call_args[1]['extra']['budget'] == 500.0
            assert call_args[1]['extra']['action'] == 'created'


class TestSetupLogging:
    """Test cases for setup_logging function."""

    def test_setup_logging_development(self):
        """Test logging setup for development environment."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.LOG_LEVEL = 'DEBUG'
            mock_settings.LOG_FILE = None
            mock_settings.is_production = False
            mock_settings.ENVIRONMENT = 'development'
            
            with patch('logging.getLogger') as mock_get_logger:
                mock_logger = MagicMock()
                mock_get_logger.return_value = mock_logger
                
                setup_logging()
                
                # Verify logger configuration was called
                mock_get_logger.assert_called()

    def test_setup_logging_with_file_rotation(self):
        """Test logging setup with file rotation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = os.path.join(temp_dir, "test.log")
            
            with patch('app.core.config.settings') as mock_settings:
                mock_settings.LOG_LEVEL = 'INFO'
                mock_settings.LOG_FILE = log_file
                mock_settings.LOG_ROTATION = '1 day'
                mock_settings.LOG_RETENTION = '30 days'
                mock_settings.is_production = True
                mock_settings.ENVIRONMENT = 'production'
                
                with patch('logging.getLogger') as mock_get_logger:
                    mock_logger = MagicMock()
                    mock_get_logger.return_value = mock_logger
                    
                    setup_logging()
                    
                    # Verify log directory was created
                    assert Path(log_file).parent.exists()


class TestCorrelationIdMiddleware:
    """Test cases for CorrelationIdMiddleware."""

    @pytest.mark.asyncio
    async def test_middleware_generates_correlation_id(self):
        """Test that middleware generates correlation ID when not provided."""
        middleware = CorrelationIdMiddleware(app=MagicMock())
        
        # Mock request without correlation ID header
        request = MagicMock()
        request.headers = {}
        
        # Mock response
        response = MagicMock()
        response.headers = {}
        
        # Mock call_next
        async def mock_call_next(req):
            return response
        
        result = await middleware.dispatch(request, mock_call_next)
        
        # Verify correlation ID was added to response headers
        assert 'X-Correlation-ID' in response.headers
        assert len(response.headers['X-Correlation-ID']) > 0

    @pytest.mark.asyncio
    async def test_middleware_uses_existing_correlation_id(self):
        """Test that middleware uses existing correlation ID from header."""
        middleware = CorrelationIdMiddleware(app=MagicMock())
        
        test_correlation_id = "existing-correlation-id"
        
        # Mock request with correlation ID header
        request = MagicMock()
        request.headers = {'X-Correlation-ID': test_correlation_id}
        
        # Mock response
        response = MagicMock()
        response.headers = {}
        
        # Mock call_next
        async def mock_call_next(req):
            return response
        
        result = await middleware.dispatch(request, mock_call_next)
        
        # Verify existing correlation ID was used
        assert response.headers['X-Correlation-ID'] == test_correlation_id


class TestEnhancedRequestLoggingMiddleware:
    """Test cases for EnhancedRequestLoggingMiddleware."""

    @pytest.mark.asyncio
    async def test_middleware_logs_request_and_response(self):
        """Test that middleware logs request and response details."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            middleware = EnhancedRequestLoggingMiddleware(app=MagicMock())
            
            # Mock request
            request = MagicMock()
            request.method = 'GET'
            request.url.path = '/api/v1/vendors'
            request.query_params = {}
            request.headers = {'User-Agent': 'TestAgent'}
            request.client.host = '***********'
            
            # Mock response
            response = MagicMock()
            response.status_code = 200
            response.headers = {}
            
            # Mock call_next
            async def mock_call_next(req):
                return response
            
            result = await middleware.dispatch(request, mock_call_next)
            
            # Verify logging was called
            assert mock_logger.info.call_count >= 2  # Request start and complete
            
            # Verify processing time header was added
            assert 'X-Process-Time' in response.headers

    @pytest.mark.asyncio
    async def test_middleware_skips_excluded_paths(self):
        """Test that middleware skips logging for excluded paths."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            middleware = EnhancedRequestLoggingMiddleware(
                app=MagicMock(),
                exclude_paths=['/health']
            )
            
            # Mock request to excluded path
            request = MagicMock()
            request.url.path = '/health'
            
            # Mock response
            response = MagicMock()
            
            # Mock call_next
            async def mock_call_next(req):
                return response
            
            result = await middleware.dispatch(request, mock_call_next)
            
            # Verify no logging occurred
            mock_logger.info.assert_not_called()

    @pytest.mark.asyncio
    async def test_middleware_logs_exceptions(self):
        """Test that middleware logs exceptions properly."""
        with patch('app.core.logging.logging.getLogger') as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            middleware = EnhancedRequestLoggingMiddleware(app=MagicMock())
            
            # Mock request
            request = MagicMock()
            request.method = 'GET'
            request.url.path = '/api/v1/vendors'
            request.query_params = {}
            request.headers = {'User-Agent': 'TestAgent'}
            request.client.host = '***********'
            
            # Mock call_next that raises exception
            async def mock_call_next(req):
                raise ValueError("Test exception")
            
            with pytest.raises(ValueError):
                await middleware.dispatch(request, mock_call_next)
            
            # Verify error logging occurred
            mock_logger.error.assert_called_once()
