"""
Health check response schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic models for health check endpoints including:
- Basic health status responses
- Detailed system health with metrics
- Geolocation service health validation
- Circuit breaker status reporting
- Performance metrics and thresholds
- Kubernetes probe compatibility

Implements Phase 2.1 API documentation enhancement with comprehensive response models.
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from enum import Enum


class HealthStatus(str, Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ServiceStatus(str, Enum):
    """Service status enumeration."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEGRADED = "degraded"
    NOT_IMPLEMENTED = "not_implemented"


class CircuitBreakerState(str, Enum):
    """Circuit breaker state enumeration."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class BasicHealthResponse(BaseModel):
    """Basic health check response model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: HealthStatus = Field(
        description="Overall health status of the service",
        example="healthy"
    )
    timestamp: float = Field(
        description="Unix timestamp when health check was performed",
        example=**********.123
    )
    service: str = Field(
        description="Service name and identifier",
        example="Culture Connect Backend API"
    )
    version: str = Field(
        description="Service version",
        example="1.0.0"
    )
    environment: str = Field(
        description="Deployment environment",
        example="production"
    )


class SystemMetrics(BaseModel):
    """System resource metrics model."""
    model_config = ConfigDict(from_attributes=True)
    
    cpu_percent: float = Field(
        description="CPU usage percentage",
        ge=0.0,
        le=100.0,
        example=25.5
    )
    memory_percent: float = Field(
        description="Memory usage percentage", 
        ge=0.0,
        le=100.0,
        example=45.2
    )
    disk_percent: float = Field(
        description="Disk usage percentage",
        ge=0.0,
        le=100.0,
        example=60.8
    )
    load_average: Optional[List[float]] = Field(
        description="System load average (1, 5, 15 minutes)",
        example=[0.5, 0.7, 0.9]
    )


class DatabaseHealthCheck(BaseModel):
    """Database connectivity health check model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: ServiceStatus = Field(
        description="Database connectivity status",
        example="available"
    )
    response_time_ms: Optional[float] = Field(
        description="Database response time in milliseconds",
        ge=0.0,
        example=15.5
    )
    connection_pool_size: Optional[int] = Field(
        description="Current connection pool size",
        ge=0,
        example=10
    )
    active_connections: Optional[int] = Field(
        description="Number of active database connections",
        ge=0,
        example=3
    )
    error: Optional[str] = Field(
        description="Error message if database is unhealthy",
        example=None
    )


class ExternalServiceCheck(BaseModel):
    """External service health check model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: ServiceStatus = Field(
        description="External service status",
        example="available"
    )
    response_time_ms: Optional[float] = Field(
        description="Service response time in milliseconds",
        ge=0.0,
        example=120.0
    )
    last_check: Optional[datetime] = Field(
        description="Timestamp of last successful check",
        example="2024-01-15T10:30:00Z"
    )
    error: Optional[str] = Field(
        description="Error message if service is unhealthy",
        example=None
    )


class CircuitBreakerStatus(BaseModel):
    """Circuit breaker status model."""
    model_config = ConfigDict(from_attributes=True)
    
    state: CircuitBreakerState = Field(
        description="Current circuit breaker state",
        example="closed"
    )
    failure_count: int = Field(
        description="Current failure count",
        ge=0,
        example=0
    )
    failure_threshold: int = Field(
        description="Failure threshold for opening circuit",
        ge=1,
        example=10
    )
    success_rate: float = Field(
        description="Success rate percentage",
        ge=0.0,
        le=1.0,
        example=0.98
    )
    last_failure_time: Optional[datetime] = Field(
        description="Timestamp of last failure",
        example=None
    )
    recovery_timeout: int = Field(
        description="Recovery timeout in seconds",
        ge=1,
        example=60
    )


class GeolocationPerformanceMetrics(BaseModel):
    """Geolocation service performance metrics model."""
    model_config = ConfigDict(from_attributes=True)
    
    total_requests: int = Field(
        description="Total number of geolocation requests",
        ge=0,
        example=1500
    )
    cache_hits: int = Field(
        description="Number of cache hits",
        ge=0,
        example=1350
    )
    cache_misses: int = Field(
        description="Number of cache misses",
        ge=0,
        example=150
    )
    cache_hit_rate: float = Field(
        description="Cache hit rate percentage",
        ge=0.0,
        le=1.0,
        example=0.90
    )
    avg_detection_time_ms: float = Field(
        description="Average detection time in milliseconds",
        ge=0.0,
        example=45.5
    )
    circuit_breaker_trips: int = Field(
        description="Number of circuit breaker activations",
        ge=0,
        example=2
    )
    fallback_activations: int = Field(
        description="Number of fallback mechanism activations",
        ge=0,
        example=5
    )


class GeolocationDatabaseStatus(BaseModel):
    """Geolocation database status model."""
    model_config = ConfigDict(from_attributes=True)
    
    database_path: str = Field(
        description="Path to MaxMind GeoIP database",
        example="./data/GeoLite2-Country.mmdb"
    )
    database_available: bool = Field(
        description="Whether MaxMind database is available",
        example=True
    )
    database_size_mb: Optional[float] = Field(
        description="Database file size in megabytes",
        ge=0.0,
        example=25.5
    )
    last_updated: Optional[datetime] = Field(
        description="Timestamp of last database update",
        example="2024-01-15T02:00:00Z"
    )
    cache_ttl_hours: float = Field(
        description="Cache TTL in hours",
        ge=0.0,
        example=24.0
    )


class GeolocationHealthResponse(BaseModel):
    """Comprehensive geolocation service health response model."""
    model_config = ConfigDict(from_attributes=True)
    
    service: str = Field(
        description="Service identifier",
        example="geolocation"
    )
    status: HealthStatus = Field(
        description="Overall geolocation service health status",
        example="healthy"
    )
    timestamp: float = Field(
        description="Unix timestamp when health check was performed",
        example=**********.123
    )
    details: Dict[str, Any] = Field(
        description="Detailed health check results",
        example={
            "database": {"status": "healthy", "test_result": "US"},
            "cache": {"status": "healthy", "hit_rate": 0.90},
            "circuit_breaker": {"status": "healthy", "state": "closed"}
        }
    )
    performance: GeolocationPerformanceMetrics = Field(
        description="Performance metrics for geolocation service"
    )
    database_status: GeolocationDatabaseStatus = Field(
        description="MaxMind database status information"
    )
    circuit_breaker: CircuitBreakerStatus = Field(
        description="Circuit breaker status and metrics"
    )


class DetailedHealthResponse(BaseModel):
    """Detailed health check response with all system components."""
    model_config = ConfigDict(from_attributes=True)
    
    status: HealthStatus = Field(
        description="Overall system health status",
        example="healthy"
    )
    timestamp: float = Field(
        description="Unix timestamp when health check was performed",
        example=**********.123
    )
    service: str = Field(
        description="Service name and identifier",
        example="Culture Connect Backend API"
    )
    version: str = Field(
        description="Service version",
        example="1.0.0"
    )
    environment: str = Field(
        description="Deployment environment",
        example="production"
    )
    checks: Dict[str, Union[DatabaseHealthCheck, SystemMetrics, Dict[str, ExternalServiceCheck]]] = Field(
        description="Individual component health checks",
        example={
            "database": {"status": "available", "response_time_ms": 15.5},
            "system": {"cpu_percent": 25.5, "memory_percent": 45.2},
            "external_services": {
                "paystack": {"status": "available"},
                "redis": {"status": "available"}
            }
        }
    )


class ReadinessResponse(BaseModel):
    """Kubernetes readiness probe response model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: str = Field(
        description="Readiness status (ready/not_ready)",
        example="ready"
    )
    timestamp: float = Field(
        description="Unix timestamp when readiness check was performed",
        example=**********.123
    )
    checks: Dict[str, str] = Field(
        description="Individual readiness checks",
        example={"database": "connected", "cache": "available"}
    )


class LivenessResponse(BaseModel):
    """Kubernetes liveness probe response model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: str = Field(
        description="Liveness status (alive/dead)",
        example="alive"
    )
    timestamp: float = Field(
        description="Unix timestamp when liveness check was performed",
        example=**********.123
    )
    uptime: float = Field(
        description="Service uptime in seconds",
        ge=0.0,
        example=86400.0
    )


class HealthCheckError(BaseModel):
    """Health check error response model."""
    model_config = ConfigDict(from_attributes=True)
    
    status: HealthStatus = Field(
        description="Error health status",
        example="unhealthy"
    )
    timestamp: float = Field(
        description="Unix timestamp when error occurred",
        example=**********.123
    )
    error: str = Field(
        description="Error message describing the health check failure",
        example="Database connection failed"
    )
    details: Optional[Dict[str, Any]] = Field(
        description="Additional error details and context",
        example={"component": "database", "error_code": "CONNECTION_TIMEOUT"}
    )
