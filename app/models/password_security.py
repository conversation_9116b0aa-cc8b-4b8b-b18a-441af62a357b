"""
Password Security models for Culture Connect Backend API.

This module defines comprehensive password security models including:
- PasswordHistory: Track user password history to prevent reuse
- PasswordResetToken: Secure password reset token management with expiration
- AccountLockout: Account lockout tracking for failed login attempts

Implements Task 2.1.2 requirements for password security and hashing with
production-grade security features and PostgreSQL optimization.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit


class PasswordHistory(BaseModelWithAudit):
    """
    Password history model for tracking user password changes.

    This model stores hashed passwords to prevent users from reusing
    recent passwords, enhancing security by enforcing password rotation.
    Configured to keep the last 5 passwords per user.
    """

    __tablename__ = "password_history"

    # User relationship
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Foreign key to users table"
    )

    # Password hash storage
    password_hash = Column(
        String(255),
        nullable=False,
        doc="Bcrypt hashed password for comparison"
    )

    # Timestamp for ordering and cleanup
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="When this password was set"
    )

    # Relationships
    user = relationship("User", back_populates="password_history", foreign_keys=[user_id])

    # Database indexes for performance
    __table_args__ = (
        Index('idx_password_history_user_created', 'user_id', 'created_at'),
        Index('idx_password_history_cleanup', 'created_at'),
    )

    def __repr__(self):
        return f"<PasswordHistory(user_id={self.user_id}, created_at={self.created_at})>"


class PasswordResetToken(BaseModelWithAudit):
    """
    Password reset token model for secure password reset functionality.

    This model manages password reset tokens with expiration, usage tracking,
    and IP address logging for security auditing. Tokens are cryptographically
    secure and single-use only.
    """

    __tablename__ = "password_reset_tokens"

    # Primary key as UUID for security
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        doc="Unique identifier for the reset token"
    )

    # User relationship
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Foreign key to users table"
    )

    # Token security
    token_hash = Column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        doc="Hashed reset token for secure storage"
    )

    # Token lifecycle
    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Token expiration timestamp"
    )

    used_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the token was used (if used)"
    )

    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether the token is still active"
    )

    # Security tracking
    created_ip = Column(
        String(45),  # IPv6 support
        nullable=False,
        doc="IP address where token was requested"
    )

    used_ip = Column(
        String(45),  # IPv6 support
        nullable=True,
        doc="IP address where token was used"
    )

    # Additional security metadata
    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string for security tracking"
    )

    # Relationships
    user = relationship("User", back_populates="password_reset_tokens", foreign_keys=[user_id])

    # Database indexes for performance and security
    __table_args__ = (
        Index('idx_reset_token_hash_active', 'token_hash', 'is_active'),
        Index('idx_reset_token_expires', 'expires_at', 'is_active'),
        Index('idx_reset_token_user_active', 'user_id', 'is_active'),
        Index('idx_reset_token_cleanup', 'expires_at'),
    )

    def __repr__(self):
        return f"<PasswordResetToken(user_id={self.user_id}, expires_at={self.expires_at}, is_active={self.is_active})>"

    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid (active and not expired)."""
        return self.is_active and not self.is_expired and self.used_at is None


class AccountLockout(BaseModelWithAudit):
    """
    Account lockout model for tracking failed login attempts and lockout status.

    This model implements brute force protection by tracking failed login
    attempts and temporarily locking accounts after too many failures.
    Configured for 5 failed attempts resulting in 15-minute lockout.
    """

    __tablename__ = "account_lockouts"

    # User relationship (one-to-one)
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        doc="Foreign key to users table (unique per user)"
    )

    # Failed attempt tracking
    failed_attempts = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of consecutive failed login attempts"
    )

    # Lockout management
    locked_until = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When the account lockout expires (null if not locked)"
    )

    # Attempt tracking
    last_attempt_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Timestamp of the last login attempt"
    )

    last_attempt_ip = Column(
        String(45),  # IPv6 support
        nullable=False,
        doc="IP address of the last login attempt"
    )

    # Security metadata
    lockout_reason = Column(
        String(100),
        nullable=True,
        doc="Reason for lockout (e.g., 'too_many_attempts', 'suspicious_activity')"
    )

    # Relationships
    user = relationship("User", back_populates="account_lockout", foreign_keys=[user_id])

    # Database indexes for performance
    __table_args__ = (
        Index('idx_lockout_user_status', 'user_id', 'locked_until'),
        Index('idx_lockout_cleanup', 'locked_until'),
        Index('idx_lockout_attempts', 'failed_attempts', 'last_attempt_at'),
    )

    def __repr__(self):
        return f"<AccountLockout(user_id={self.user_id}, failed_attempts={self.failed_attempts}, locked_until={self.locked_until})>"

    @property
    def is_locked(self) -> bool:
        """Check if the account is currently locked."""
        if not self.locked_until:
            return False
        return datetime.utcnow() < self.locked_until

    @property
    def remaining_lockout_seconds(self) -> Optional[int]:
        """Get remaining lockout time in seconds."""
        if not self.is_locked:
            return None
        remaining = self.locked_until - datetime.utcnow()
        return max(0, int(remaining.total_seconds()))
