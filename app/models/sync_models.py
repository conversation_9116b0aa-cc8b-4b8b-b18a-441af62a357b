"""
Real-time Synchronization models for Culture Connect Backend API.

This module defines comprehensive models for real-time data synchronization including:
- Data synchronization tracking and conflict resolution
- Change tracking with audit trails
- Event prioritization and batching
- Optimistic locking and rollback mechanisms
- Performance monitoring and sync metrics

Implements Task 6.1.2 Phase 1 requirements for real-time synchronization with:
- Conflict resolution mechanisms (last-write-wins, server-side validation)
- Event prioritization (critical, standard, historical)
- Change tracking with comprehensive audit trails
- Integration with existing WebSocket infrastructure
- Performance targets: <100ms sync, <50ms conflict resolution, >95% success rate
"""

import enum
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Float, DECIMAL,
    ForeignKey, Index, CheckConstraint, JSON as SQLJSON, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from app.db.base import BaseModelWithAudit


# Enums for synchronization
class SyncStatus(str, enum.Enum):
    """Synchronization status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"
    ROLLED_BACK = "rolled_back"


class ConflictResolutionStrategy(str, enum.Enum):
    """Conflict resolution strategy enumeration."""
    LAST_WRITE_WINS = "last_write_wins"
    SERVER_AUTHORITATIVE = "server_authoritative"
    CLIENT_MERGE = "client_merge"
    MANUAL_RESOLUTION = "manual_resolution"
    OPTIMISTIC_LOCK = "optimistic_lock"


class SyncPriority(str, enum.Enum):
    """Synchronization priority enumeration."""
    CRITICAL = "critical"      # Immediate delivery
    STANDARD = "standard"      # 30-second batches
    HISTORICAL = "historical"  # 5-minute sync
    BULK = "bulk"             # Off-peak processing


class ChangeType(str, enum.Enum):
    """Change type enumeration."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    BATCH_UPDATE = "batch_update"
    RESTORE = "restore"


class SyncScope(str, enum.Enum):
    """Synchronization scope enumeration."""
    USER = "user"              # User-specific data
    CONNECTION = "connection"   # Connection-specific data
    GLOBAL = "global"          # Global data
    ROOM = "room"              # Room/channel-specific data


class DataSyncRecord(BaseModelWithAudit):
    """
    Data synchronization record model.

    Tracks all data synchronization operations with conflict resolution,
    change tracking, and performance monitoring.
    """

    __tablename__ = "data_sync_records"

    # Sync identification
    sync_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        default=lambda: str(uuid.uuid4()),
        doc="Unique synchronization identifier"
    )

    # Source information
    source_connection_id = Column(
        String(255),
        ForeignKey("websocket_connections.connection_id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Source WebSocket connection ID"
    )

    source_user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Source user ID"
    )

    # Target information
    target_scope = Column(
        SQLEnum(SyncScope, native_enum=False),
        nullable=False,
        index=True,
        doc="Synchronization scope"
    )

    target_identifier = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Target identifier (user_id, connection_id, room_id, etc.)"
    )

    # Data information
    entity_type = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Type of entity being synchronized"
    )

    entity_id = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Entity identifier"
    )

    change_type = Column(
        SQLEnum(ChangeType, native_enum=False),
        nullable=False,
        index=True,
        doc="Type of change"
    )

    # Synchronization details
    priority = Column(
        SQLEnum(SyncPriority, native_enum=False),
        nullable=False,
        default=SyncPriority.STANDARD,
        index=True,
        doc="Synchronization priority"
    )

    status = Column(
        SQLEnum(SyncStatus, native_enum=False),
        nullable=False,
        default=SyncStatus.PENDING,
        index=True,
        doc="Synchronization status"
    )

    # Data payload
    data_before = Column(
        JSONB,
        nullable=True,
        doc="Data state before change"
    )

    data_after = Column(
        JSONB,
        nullable=False,
        doc="Data state after change"
    )

    data_delta = Column(
        JSONB,
        nullable=True,
        doc="Change delta for efficient sync"
    )

    # Version control
    version_before = Column(
        Integer,
        nullable=True,
        doc="Version before change"
    )

    version_after = Column(
        Integer,
        nullable=False,
        doc="Version after change"
    )

    # Conflict resolution
    conflict_resolution_strategy = Column(
        SQLEnum(ConflictResolutionStrategy, native_enum=False),
        nullable=False,
        default=ConflictResolutionStrategy.LAST_WRITE_WINS,
        doc="Conflict resolution strategy"
    )

    has_conflicts = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether conflicts were detected"
    )

    conflict_details = Column(
        JSONB,
        nullable=True,
        doc="Conflict details and resolution information"
    )

    # Timing information
    initiated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Sync initiation timestamp"
    )

    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Sync start timestamp"
    )

    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Sync completion timestamp"
    )

    # Performance metrics
    processing_time_ms = Column(
        Float,
        nullable=True,
        doc="Processing time in milliseconds"
    )

    conflict_resolution_time_ms = Column(
        Float,
        nullable=True,
        doc="Conflict resolution time in milliseconds"
    )

    # Retry information
    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of retry attempts"
    )

    max_retries = Column(
        Integer,
        nullable=False,
        default=3,
        doc="Maximum retry attempts"
    )

    next_retry_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Next retry attempt timestamp"
    )

    # Error tracking
    error_message = Column(
        Text,
        nullable=True,
        doc="Error message if sync failed"
    )

    error_details = Column(
        JSONB,
        nullable=True,
        doc="Detailed error information"
    )

    # Metadata
    sync_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional synchronization metadata"
    )

    # Relationships
    source_user = relationship(
        "User",
        foreign_keys=[source_user_id],
        doc="Source user"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_sync_status_priority", "status", "priority"),
        Index("idx_sync_entity", "entity_type", "entity_id"),
        Index("idx_sync_target", "target_scope", "target_identifier"),
        Index("idx_sync_timing", "initiated_at", "completed_at"),
        Index("idx_sync_conflicts", "has_conflicts", "status"),
        Index("idx_sync_retry", "retry_count", "next_retry_at"),

        # Check constraints
        CheckConstraint(
            "processing_time_ms >= 0",
            name="ck_sync_processing_time_positive"
        ),
        CheckConstraint(
            "conflict_resolution_time_ms >= 0",
            name="ck_sync_conflict_time_positive"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="ck_sync_retry_count_positive"
        ),
        CheckConstraint(
            "max_retries >= 0",
            name="ck_sync_max_retries_positive"
        ),
        CheckConstraint(
            "version_after > 0",
            name="ck_sync_version_after_positive"
        ),
        CheckConstraint(
            "(version_before IS NULL AND change_type = 'create') OR (version_before IS NOT NULL)",
            name="ck_sync_version_before_create"
        ),
        CheckConstraint(
            "(completed_at IS NULL) OR (completed_at >= started_at)",
            name="ck_sync_completion_after_start"
        ),
        CheckConstraint(
            "(started_at IS NULL) OR (started_at >= initiated_at)",
            name="ck_sync_start_after_initiation"
        )
    )


class SyncConflict(BaseModelWithAudit):
    """
    Synchronization conflict tracking model.

    Tracks conflicts that occur during data synchronization
    with detailed resolution information and audit trails.
    """

    __tablename__ = "sync_conflicts"

    # Conflict identification
    conflict_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        default=lambda: str(uuid.uuid4()),
        doc="Unique conflict identifier"
    )

    # Associated sync record
    sync_record_id = Column(
        Integer,
        ForeignKey("data_sync_records.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated sync record ID"
    )

    # Conflict details
    conflict_type = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Type of conflict (version, data, permission, etc.)"
    )

    conflicting_version = Column(
        Integer,
        nullable=True,
        doc="Conflicting version number"
    )

    conflicting_data = Column(
        JSONB,
        nullable=True,
        doc="Conflicting data state"
    )

    conflicting_source = Column(
        String(255),
        nullable=True,
        doc="Source of conflicting change"
    )

    # Resolution information
    resolution_strategy = Column(
        SQLEnum(ConflictResolutionStrategy, native_enum=False),
        nullable=False,
        doc="Strategy used to resolve conflict"
    )

    resolution_data = Column(
        JSONB,
        nullable=True,
        doc="Resolution data and merged result"
    )

    resolved_by_user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who resolved the conflict (if manual)"
    )

    resolved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Conflict resolution timestamp"
    )

    resolution_time_ms = Column(
        Float,
        nullable=True,
        doc="Time taken to resolve conflict in milliseconds"
    )

    # Metadata
    conflict_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional conflict metadata"
    )

    # Relationships
    sync_record = relationship(
        "DataSyncRecord",
        back_populates="conflicts",
        doc="Associated sync record"
    )

    resolved_by = relationship(
        "User",
        foreign_keys=[resolved_by_user_id],
        doc="User who resolved the conflict"
    )

    # Indexes
    __table_args__ = (
        Index("idx_conflict_type_status", "conflict_type", "resolved_at"),
        Index("idx_conflict_resolution", "resolution_strategy", "resolved_at"),
        Index("idx_conflict_timing", "created_at", "resolved_at"),

        # Check constraints
        CheckConstraint(
            "resolution_time_ms >= 0",
            name="ck_conflict_resolution_time_positive"
        ),
        CheckConstraint(
            "(resolved_at IS NULL AND resolved_by_user_id IS NULL) OR (resolved_at IS NOT NULL)",
            name="ck_conflict_resolution_consistency"
        )
    )


class SyncBatch(BaseModelWithAudit):
    """
    Synchronization batch model.

    Groups multiple sync operations for efficient batch processing
    based on priority and timing requirements.
    """

    __tablename__ = "sync_batches"

    # Batch identification
    batch_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        default=lambda: str(uuid.uuid4()),
        doc="Unique batch identifier"
    )

    # Batch configuration
    priority = Column(
        SQLEnum(SyncPriority, native_enum=False),
        nullable=False,
        index=True,
        doc="Batch priority level"
    )

    target_scope = Column(
        SQLEnum(SyncScope, native_enum=False),
        nullable=False,
        index=True,
        doc="Batch target scope"
    )

    # Batch status
    status = Column(
        SQLEnum(SyncStatus, native_enum=False),
        nullable=False,
        default=SyncStatus.PENDING,
        index=True,
        doc="Batch processing status"
    )

    # Timing
    scheduled_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Scheduled processing time"
    )

    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Batch processing start time"
    )

    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Batch processing completion time"
    )

    # Metrics
    total_records = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total records in batch"
    )

    processed_records = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Successfully processed records"
    )

    failed_records = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Failed records"
    )

    processing_time_ms = Column(
        Float,
        nullable=True,
        doc="Total processing time in milliseconds"
    )

    # Configuration
    batch_config = Column(
        JSONB,
        nullable=True,
        doc="Batch processing configuration"
    )

    # Indexes
    __table_args__ = (
        Index("idx_batch_priority_status", "priority", "status"),
        Index("idx_batch_scheduled", "scheduled_at", "status"),
        Index("idx_batch_scope_priority", "target_scope", "priority"),

        # Check constraints
        CheckConstraint(
            "total_records >= 0",
            name="ck_batch_total_records_positive"
        ),
        CheckConstraint(
            "processed_records >= 0",
            name="ck_batch_processed_records_positive"
        ),
        CheckConstraint(
            "failed_records >= 0",
            name="ck_batch_failed_records_positive"
        ),
        CheckConstraint(
            "processed_records + failed_records <= total_records",
            name="ck_batch_records_consistency"
        )
    )


class SyncMetrics(BaseModelWithAudit):
    """
    Synchronization performance metrics model.

    Tracks synchronization performance metrics for monitoring
    and optimization of sync operations.
    """

    __tablename__ = "sync_metrics"

    # Metrics identification
    metric_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Metrics date"
    )

    metric_hour = Column(
        Integer,
        nullable=False,
        index=True,
        doc="Metrics hour (0-23)"
    )

    # Scope metrics
    scope = Column(
        SQLEnum(SyncScope, native_enum=False),
        nullable=False,
        index=True,
        doc="Metrics scope"
    )

    priority = Column(
        SQLEnum(SyncPriority, native_enum=False),
        nullable=False,
        index=True,
        doc="Metrics priority"
    )

    # Performance metrics
    total_syncs = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total sync operations"
    )

    successful_syncs = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Successful sync operations"
    )

    failed_syncs = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Failed sync operations"
    )

    conflicts_detected = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Conflicts detected"
    )

    conflicts_resolved = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Conflicts resolved"
    )

    # Timing metrics
    avg_processing_time_ms = Column(
        Float,
        nullable=True,
        doc="Average processing time in milliseconds"
    )

    max_processing_time_ms = Column(
        Float,
        nullable=True,
        doc="Maximum processing time in milliseconds"
    )

    avg_conflict_resolution_time_ms = Column(
        Float,
        nullable=True,
        doc="Average conflict resolution time in milliseconds"
    )

    # Success rate
    success_rate = Column(
        Float,
        nullable=True,
        doc="Success rate percentage"
    )

    # Data volume
    total_data_size_bytes = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total data synchronized in bytes"
    )

    # Indexes
    __table_args__ = (
        Index("idx_sync_metrics_date_hour", "metric_date", "metric_hour"),
        Index("idx_metrics_scope_priority", "scope", "priority"),
        Index("idx_sync_metrics_performance", "success_rate", "avg_processing_time_ms"),

        # Unique constraint for metrics aggregation
        Index("idx_metrics_unique", "metric_date", "metric_hour", "scope", "priority", unique=True),

        # Check constraints
        CheckConstraint(
            "metric_hour >= 0 AND metric_hour <= 23",
            name="ck_metrics_hour_valid"
        ),
        CheckConstraint(
            "total_syncs >= 0",
            name="ck_metrics_total_syncs_positive"
        ),
        CheckConstraint(
            "successful_syncs >= 0",
            name="ck_metrics_successful_syncs_positive"
        ),
        CheckConstraint(
            "failed_syncs >= 0",
            name="ck_metrics_failed_syncs_positive"
        ),
        CheckConstraint(
            "successful_syncs + failed_syncs <= total_syncs",
            name="ck_metrics_syncs_consistency"
        ),
        CheckConstraint(
            "success_rate >= 0 AND success_rate <= 100",
            name="ck_metrics_success_rate_valid"
        ),
        CheckConstraint(
            "avg_processing_time_ms >= 0",
            name="ck_metrics_avg_time_positive"
        ),
        CheckConstraint(
            "max_processing_time_ms >= 0",
            name="ck_metrics_max_time_positive"
        )
    )


# Add relationship back to DataSyncRecord
DataSyncRecord.conflicts = relationship(
    "SyncConflict",
    back_populates="sync_record",
    cascade="all, delete-orphan",
    doc="Associated conflicts"
)
