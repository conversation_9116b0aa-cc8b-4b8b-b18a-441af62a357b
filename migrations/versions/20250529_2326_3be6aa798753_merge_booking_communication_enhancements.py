"""merge_booking_communication_enhancements

Revision ID: 3be6aa798753
Revises: 0003_enhance_job_schedules, enhance_booking_comm_perf
Create Date: 2025-05-29 23:26:11.750666+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '3be6aa798753'
down_revision = ('0003_enhance_job_schedules', 'enhance_booking_comm_perf')
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    pass


def downgrade() -> None:
    """Downgrade database schema."""
    pass
