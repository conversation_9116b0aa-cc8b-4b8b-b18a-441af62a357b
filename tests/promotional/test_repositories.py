"""
Unit tests for promotional repositories.

This module provides comprehensive unit tests for promotional system repositories:
- CampaignRepository: Campaign CRUD operations with vendor filtering
- AdvertisementRepository: Advertisement management with campaign relationships
- CampaignMetricsRepository: Time-series metrics data with aggregation
- PromotionalListingRepository: Placement management with availability checking
- AdSpendRepository: Financial tracking with reconciliation support
- BaseRepository pattern compliance and performance optimization
- Bulk operations and pagination testing
- Error handling and transaction management

Implements Phase 5: Comprehensive Testing Infrastructure with >85% test coverage.
"""

import pytest
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import selectinload

from app.repositories.promotional_repositories import (
    CampaignRepository, AdvertisementRepository, CampaignMetricsRepository,
    PromotionalListingRepository, AdSpendRepository
)
from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend,
    CampaignStatus, AdStatus, PlacementStatus
)
from app.core.exceptions import NotFoundError, ValidationError


class TestCampaignRepository:
    """Test CampaignRepository functionality."""

    @pytest.fixture
    def campaign_repository(self, mock_async_session):
        """Create campaign repository with mocked session."""
        return CampaignRepository(mock_async_session)

    @pytest.mark.asyncio
    async def test_create_campaign(self, campaign_repository, campaign_factory):
        """Test campaign creation."""
        campaign_data = campaign_factory()
        campaign_repository.session.add = MagicMock()
        campaign_repository.session.commit = AsyncMock()
        campaign_repository.session.refresh = AsyncMock()

        result = await campaign_repository.create(campaign_data)

        campaign_repository.session.add.assert_called_once_with(campaign_data)
        campaign_repository.session.commit.assert_called_once()
        campaign_repository.session.refresh.assert_called_once_with(campaign_data)
        assert result == campaign_data

    @pytest.mark.asyncio
    async def test_get_campaign_by_id(self, campaign_repository, campaign_factory):
        """Test getting campaign by ID."""
        campaign = campaign_factory(id=1)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = campaign
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_by_id(1)

        assert result == campaign
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaign_by_uuid(self, campaign_repository, campaign_factory):
        """Test getting campaign by UUID."""
        campaign_uuid = uuid4()
        campaign = campaign_factory(uuid=campaign_uuid)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = campaign
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_by_uuid(campaign_uuid)

        assert result == campaign
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaigns_by_vendor(self, campaign_repository, campaign_factory):
        """Test getting campaigns by vendor ID."""
        campaigns = [campaign_factory(id=1, vendor_id=1), campaign_factory(id=2, vendor_id=1)]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_by_vendor_id(1)

        assert result == campaigns
        assert len(result) == 2
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_campaigns(self, campaign_repository, campaign_factory):
        """Test getting active campaigns."""
        active_campaigns = [
            campaign_factory(id=1, status=CampaignStatus.ACTIVE),
            campaign_factory(id=2, status=CampaignStatus.ACTIVE)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = active_campaigns
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_active_campaigns()

        assert result == active_campaigns
        assert len(result) == 2
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_campaign(self, campaign_repository, campaign_factory):
        """Test campaign update."""
        campaign = campaign_factory(id=1)
        update_data = {"name": "Updated Campaign", "daily_budget": Decimal("1500.00")}

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = campaign
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)
        campaign_repository.session.commit = AsyncMock()
        campaign_repository.session.refresh = AsyncMock()

        result = await campaign_repository.update(1, update_data)

        assert result.name == "Updated Campaign"
        assert result.daily_budget == Decimal("1500.00")
        campaign_repository.session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_campaign(self, campaign_repository, campaign_factory):
        """Test campaign deletion."""
        campaign = campaign_factory(id=1)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = campaign
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)
        campaign_repository.session.delete = MagicMock()
        campaign_repository.session.commit = AsyncMock()

        result = await campaign_repository.delete(1)

        assert result is True
        campaign_repository.session.delete.assert_called_once_with(campaign)
        campaign_repository.session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaigns_by_status(self, campaign_repository, campaign_factory):
        """Test getting campaigns by status."""
        draft_campaigns = [
            campaign_factory(id=1, status=CampaignStatus.DRAFT),
            campaign_factory(id=2, status=CampaignStatus.DRAFT)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = draft_campaigns
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_by_status(CampaignStatus.DRAFT)

        assert result == draft_campaigns
        assert len(result) == 2
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaigns_by_date_range(self, campaign_repository, campaign_factory):
        """Test getting campaigns by date range."""
        start_date = date.today()
        end_date = date.today() + timedelta(days=30)
        campaigns = [campaign_factory(id=1), campaign_factory(id=2)]

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_by_date_range(start_date, end_date)

        assert result == campaigns
        campaign_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_update_campaign_status(self, campaign_repository):
        """Test bulk campaign status update."""
        campaign_ids = [1, 2, 3]
        new_status = CampaignStatus.PAUSED

        mock_result = MagicMock()
        mock_result.rowcount = 3
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)
        campaign_repository.session.commit = AsyncMock()

        result = await campaign_repository.bulk_update_status(campaign_ids, new_status)

        assert result == 3
        campaign_repository.session.execute.assert_called_once()
        campaign_repository.session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaign_performance_summary(self, campaign_repository):
        """Test getting campaign performance summary."""
        performance_data = {
            "total_campaigns": 10,
            "active_campaigns": 7,
            "total_spend": Decimal("50000.00"),
            "total_revenue": Decimal("500000.00"),
            "average_roas": Decimal("10.00")
        }

        mock_result = MagicMock()
        mock_result.fetchone.return_value = performance_data
        campaign_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await campaign_repository.get_performance_summary(vendor_id=1)

        assert result == performance_data
        campaign_repository.session.execute.assert_called_once()


class TestAdvertisementRepository:
    """Test AdvertisementRepository functionality."""

    @pytest.fixture
    def advertisement_repository(self, mock_async_session):
        """Create advertisement repository with mocked session."""
        return AdvertisementRepository(mock_async_session)

    @pytest.mark.asyncio
    async def test_create_advertisement(self, advertisement_repository, advertisement_factory):
        """Test advertisement creation."""
        ad_data = advertisement_factory()
        advertisement_repository.session.add = MagicMock()
        advertisement_repository.session.commit = AsyncMock()
        advertisement_repository.session.refresh = AsyncMock()

        result = await advertisement_repository.create(ad_data)

        advertisement_repository.session.add.assert_called_once_with(ad_data)
        advertisement_repository.session.commit.assert_called_once()
        assert result == ad_data

    @pytest.mark.asyncio
    async def test_get_advertisements_by_campaign(self, advertisement_repository, advertisement_factory):
        """Test getting advertisements by campaign ID."""
        ads = [
            advertisement_factory(id=1, campaign_id=1),
            advertisement_factory(id=2, campaign_id=1)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = ads
        advertisement_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await advertisement_repository.get_by_campaign_id(1)

        assert result == ads
        assert len(result) == 2
        advertisement_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_advertisements_by_status(self, advertisement_repository, advertisement_factory):
        """Test getting advertisements by status."""
        active_ads = [
            advertisement_factory(id=1, status=AdStatus.ACTIVE),
            advertisement_factory(id=2, status=AdStatus.ACTIVE)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = active_ads
        advertisement_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await advertisement_repository.get_by_status(AdStatus.ACTIVE)

        assert result == active_ads
        advertisement_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_advertisement_variants(self, advertisement_repository, advertisement_factory):
        """Test getting advertisement variants for A/B testing."""
        variant_group = "summer_tourism_2025"
        variants = [
            advertisement_factory(id=1, variant_group=variant_group, variant_name="variant_a"),
            advertisement_factory(id=2, variant_group=variant_group, variant_name="variant_b")
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = variants
        advertisement_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await advertisement_repository.get_variants(variant_group)

        assert result == variants
        assert len(result) == 2
        advertisement_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_advertisement_performance(self, advertisement_repository, advertisement_factory):
        """Test updating advertisement performance metrics."""
        ad = advertisement_factory(id=1)
        performance_data = {
            "impressions": 1000,
            "clicks": 50,
            "conversions": 5,
            "spend_amount": Decimal("250.00")
        }

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = ad
        advertisement_repository.session.execute = AsyncMock(return_value=mock_result)
        advertisement_repository.session.commit = AsyncMock()

        result = await advertisement_repository.update_performance(1, performance_data)

        assert result.impressions == 1000
        assert result.clicks == 50
        advertisement_repository.session.commit.assert_called_once()


class TestCampaignMetricsRepository:
    """Test CampaignMetricsRepository functionality."""

    @pytest.fixture
    def metrics_repository(self, mock_async_session):
        """Create metrics repository with mocked session."""
        return CampaignMetricsRepository(mock_async_session)

    @pytest.mark.asyncio
    async def test_create_metrics_record(self, metrics_repository, campaign_metrics_factory):
        """Test creating metrics record."""
        metrics_data = campaign_metrics_factory()
        metrics_repository.session.add = MagicMock()
        metrics_repository.session.commit = AsyncMock()
        metrics_repository.session.refresh = AsyncMock()

        result = await metrics_repository.create(metrics_data)

        metrics_repository.session.add.assert_called_once_with(metrics_data)
        metrics_repository.session.commit.assert_called_once()
        assert result == metrics_data

    @pytest.mark.asyncio
    async def test_get_metrics_by_campaign_and_date(self, metrics_repository, campaign_metrics_factory):
        """Test getting metrics by campaign and date range."""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        metrics = [
            campaign_metrics_factory(id=1, campaign_id=1, date=start_date),
            campaign_metrics_factory(id=2, campaign_id=1, date=end_date)
        ]

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = metrics
        metrics_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await metrics_repository.get_by_campaign_and_date_range(1, start_date, end_date)

        assert result == metrics
        assert len(result) == 2
        metrics_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_aggregated_metrics(self, metrics_repository):
        """Test getting aggregated metrics."""
        aggregated_data = {
            "total_impressions": 10000,
            "total_clicks": 500,
            "total_conversions": 50,
            "total_spend": Decimal("2500.00"),
            "average_ctr": Decimal("5.00"),
            "average_conversion_rate": Decimal("10.00")
        }

        mock_result = MagicMock()
        mock_result.fetchone.return_value = aggregated_data
        metrics_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await metrics_repository.get_aggregated_metrics(
            campaign_id=1,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )

        assert result == aggregated_data
        metrics_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_insert_metrics(self, metrics_repository, campaign_metrics_factory):
        """Test bulk inserting metrics records."""
        metrics_list = [
            campaign_metrics_factory(id=1),
            campaign_metrics_factory(id=2),
            campaign_metrics_factory(id=3)
        ]

        metrics_repository.session.add_all = MagicMock()
        metrics_repository.session.commit = AsyncMock()

        result = await metrics_repository.bulk_insert(metrics_list)

        assert result == len(metrics_list)
        metrics_repository.session.add_all.assert_called_once_with(metrics_list)
        metrics_repository.session.commit.assert_called_once()


class TestPromotionalListingRepository:
    """Test PromotionalListingRepository functionality."""

    @pytest.fixture
    def listing_repository(self, mock_async_session):
        """Create listing repository with mocked session."""
        return PromotionalListingRepository(mock_async_session)

    @pytest.mark.asyncio
    async def test_create_promotional_listing(self, listing_repository, promotional_listing_factory):
        """Test promotional listing creation."""
        listing_data = promotional_listing_factory()
        listing_repository.session.add = MagicMock()
        listing_repository.session.commit = AsyncMock()
        listing_repository.session.refresh = AsyncMock()

        result = await listing_repository.create(listing_data)

        listing_repository.session.add.assert_called_once_with(listing_data)
        listing_repository.session.commit.assert_called_once()
        assert result == listing_data

    @pytest.mark.asyncio
    async def test_get_available_placements(self, listing_repository, promotional_listing_factory):
        """Test getting available placements."""
        available_listings = [
            promotional_listing_factory(id=1, placement_status=PlacementStatus.AVAILABLE),
            promotional_listing_factory(id=2, placement_status=PlacementStatus.AVAILABLE)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = available_listings
        listing_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await listing_repository.get_available_placements("homepage_hero")

        assert result == available_listings
        assert len(result) == 2
        listing_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_placement_availability(self, listing_repository):
        """Test checking placement availability."""
        mock_result = MagicMock()
        mock_result.scalar.return_value = 3  # 3 available slots
        listing_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await listing_repository.check_availability(
            placement_type="homepage_hero",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=30)
        )

        assert result == 3
        listing_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_listings_by_priority(self, listing_repository, promotional_listing_factory):
        """Test getting listings ordered by priority."""
        priority_listings = [
            promotional_listing_factory(id=1, priority_level=10),
            promotional_listing_factory(id=2, priority_level=8),
            promotional_listing_factory(id=3, priority_level=5)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = priority_listings
        listing_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await listing_repository.get_by_priority("homepage_hero")

        assert result == priority_listings
        # Verify highest priority first
        assert result[0].priority_level == 10
        listing_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_reserve_placement(self, listing_repository, promotional_listing_factory):
        """Test reserving a placement."""
        listing = promotional_listing_factory(id=1, placement_status=PlacementStatus.AVAILABLE)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = listing
        listing_repository.session.execute = AsyncMock(return_value=mock_result)
        listing_repository.session.commit = AsyncMock()

        result = await listing_repository.reserve_placement(1)

        assert result.placement_status == PlacementStatus.RESERVED
        listing_repository.session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_listings_by_vendor(self, listing_repository, promotional_listing_factory):
        """Test getting listings by vendor."""
        vendor_listings = [
            promotional_listing_factory(id=1, vendor_id=1),
            promotional_listing_factory(id=2, vendor_id=1)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = vendor_listings
        listing_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await listing_repository.get_by_vendor_id(1)

        assert result == vendor_listings
        assert len(result) == 2
        listing_repository.session.execute.assert_called_once()


class TestAdSpendRepository:
    """Test AdSpendRepository functionality."""

    @pytest.fixture
    def spend_repository(self, mock_async_session):
        """Create spend repository with mocked session."""
        return AdSpendRepository(mock_async_session)

    @pytest.mark.asyncio
    async def test_create_ad_spend(self, spend_repository, ad_spend_factory):
        """Test ad spend creation."""
        spend_data = ad_spend_factory()
        spend_repository.session.add = MagicMock()
        spend_repository.session.commit = AsyncMock()
        spend_repository.session.refresh = AsyncMock()

        result = await spend_repository.create(spend_data)

        spend_repository.session.add.assert_called_once_with(spend_data)
        spend_repository.session.commit.assert_called_once()
        assert result == spend_data

    @pytest.mark.asyncio
    async def test_get_spend_by_campaign(self, spend_repository, ad_spend_factory):
        """Test getting spend records by campaign."""
        spend_records = [
            ad_spend_factory(id=1, campaign_id=1),
            ad_spend_factory(id=2, campaign_id=1)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = spend_records
        spend_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await spend_repository.get_by_campaign_id(1)

        assert result == spend_records
        assert len(result) == 2
        spend_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_spend_by_vendor_and_period(self, spend_repository, ad_spend_factory):
        """Test getting spend by vendor and billing period."""
        start_date = datetime.now(timezone.utc) - timedelta(days=30)
        end_date = datetime.now(timezone.utc)
        spend_records = [
            ad_spend_factory(id=1, vendor_id=1),
            ad_spend_factory(id=2, vendor_id=1)
        ]

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = spend_records
        spend_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await spend_repository.get_by_vendor_and_period(1, start_date, end_date)

        assert result == spend_records
        spend_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_unreconciled_spend(self, spend_repository, ad_spend_factory):
        """Test getting unreconciled spend records."""
        unreconciled_records = [
            ad_spend_factory(id=1, reconciled=False),
            ad_spend_factory(id=2, reconciled=False)
        ]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = unreconciled_records
        spend_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await spend_repository.get_unreconciled_spend()

        assert result == unreconciled_records
        assert all(not record.reconciled for record in result)
        spend_repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_as_reconciled(self, spend_repository, ad_spend_factory):
        """Test marking spend records as reconciled."""
        spend_ids = [1, 2, 3]
        reconciliation_ref = "REC-2025-001234"

        mock_result = MagicMock()
        mock_result.rowcount = 3
        spend_repository.session.execute = AsyncMock(return_value=mock_result)
        spend_repository.session.commit = AsyncMock()

        result = await spend_repository.mark_as_reconciled(spend_ids, reconciliation_ref)

        assert result == 3
        spend_repository.session.execute.assert_called_once()
        spend_repository.session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_spend_summary_by_provider(self, spend_repository):
        """Test getting spend summary by payment provider."""
        summary_data = {
            "paystack": Decimal("10000.00"),
            "stripe": Decimal("5000.00"),
            "busha": Decimal("2000.00")
        }

        mock_result = MagicMock()
        mock_result.fetchall.return_value = [
            ("paystack", Decimal("10000.00")),
            ("stripe", Decimal("5000.00")),
            ("busha", Decimal("2000.00"))
        ]
        spend_repository.session.execute = AsyncMock(return_value=mock_result)

        result = await spend_repository.get_spend_summary_by_provider(
            vendor_id=1,
            start_date=datetime.now(timezone.utc) - timedelta(days=30),
            end_date=datetime.now(timezone.utc)
        )

        assert result["paystack"] == Decimal("10000.00")
        assert result["stripe"] == Decimal("5000.00")
        spend_repository.session.execute.assert_called_once()


class TestRepositoryErrorHandling:
    """Test repository error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_campaign_not_found_error(self, mock_async_session):
        """Test campaign not found error handling."""
        repository = CampaignRepository(mock_async_session)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        repository.session.execute = AsyncMock(return_value=mock_result)

        with pytest.raises(NotFoundError) as exc_info:
            await repository.get_by_id(999)

        assert "Campaign not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_database_integrity_error(self, mock_async_session, campaign_factory):
        """Test database integrity error handling."""
        repository = CampaignRepository(mock_async_session)
        campaign_data = campaign_factory()

        repository.session.add = MagicMock()
        repository.session.commit = AsyncMock(side_effect=IntegrityError("", "", ""))
        repository.session.rollback = AsyncMock()

        with pytest.raises(ValidationError):
            await repository.create(campaign_data)

        repository.session.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_repository_transaction_rollback(self, mock_async_session):
        """Test repository transaction rollback on error."""
        repository = CampaignRepository(mock_async_session)

        repository.session.execute = AsyncMock(side_effect=Exception("Database error"))
        repository.session.rollback = AsyncMock()

        with pytest.raises(Exception):
            await repository.get_by_id(1)

        repository.session.rollback.assert_called_once()


class TestRepositoryPerformance:
    """Test repository performance optimizations."""

    @pytest.mark.asyncio
    async def test_bulk_operations_performance(self, mock_async_session, campaign_metrics_factory):
        """Test bulk operations performance."""
        repository = CampaignMetricsRepository(mock_async_session)

        # Test bulk insert of 1000 records
        metrics_list = [campaign_metrics_factory(id=i) for i in range(1000)]

        repository.session.add_all = MagicMock()
        repository.session.commit = AsyncMock()

        start_time = datetime.now()
        result = await repository.bulk_insert(metrics_list)
        end_time = datetime.now()

        # Should handle 1000 records efficiently
        assert result == 1000
        processing_time = (end_time - start_time).total_seconds()
        assert processing_time < 1.0  # Should complete in under 1 second

    @pytest.mark.asyncio
    async def test_query_optimization_with_joins(self, mock_async_session):
        """Test query optimization with proper joins."""
        repository = CampaignRepository(mock_async_session)

        # Mock query with selectinload for optimization
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        repository.session.execute = AsyncMock(return_value=mock_result)

        await repository.get_campaigns_with_advertisements(vendor_id=1)

        # Verify that execute was called (indicating optimized query)
        repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_pagination_performance(self, mock_async_session):
        """Test pagination performance."""
        repository = CampaignRepository(mock_async_session)

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        repository.session.execute = AsyncMock(return_value=mock_result)

        # Test pagination with large offset
        await repository.get_paginated_campaigns(page=100, page_size=50)

        # Should still execute efficiently
        repository.session.execute.assert_called_once()
