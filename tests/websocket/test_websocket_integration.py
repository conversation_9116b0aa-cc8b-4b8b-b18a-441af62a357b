"""
Integration tests for WebSocket functionality.

This module provides comprehensive integration tests for WebSocket infrastructure including:
- Real WebSocket connection testing with authentication
- Cross-service communication validation
- Database transaction rollback mechanisms
- Message routing and broadcasting integration
- Performance validation with real connections

Implements Task 6.1.1 Phase 5 requirements with real cross-service communication testing.
"""

import pytest
import asyncio
import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import Async<PERSON>lient

from app.api.v1.endpoints.websocket_endpoints import router, connection_manager
from app.services.websocket_services import (
    WebSocketConnectionService, WebSocketEventService, 
    UserPresenceService, WebSocketMetricsService
)
from app.schemas.websocket_schemas import (
    WebSocketConnectionCreate, WebSocketEventCreate,
    EventTypeEnum, EventPriorityEnum, PresenceStatusEnum
)
from app.models.websocket_models import ConnectionStatus


@pytest.mark.integration
class TestWebSocketConnectionIntegration:
    """Integration tests for WebSocket connection functionality."""
    
    @pytest.fixture
    def test_app(self):
        """Create test FastAPI app with WebSocket endpoints."""
        app = FastAPI()
        app.include_router(router, prefix="/websocket")
        return app
    
    @pytest.fixture
    async def async_client(self, test_app):
        """Create async test client."""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            yield client
    
    async def test_websocket_connection_lifecycle(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        jwt_token,
        performance_timer
    ):
        """Test complete WebSocket connection lifecycle with database integration."""
        # Arrange
        connection_service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        presence_service = UserPresenceService(mock_async_session, mock_cache_manager)
        
        # Mock database operations
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.connection_id = "test_conn_123"
        mock_connection.user_id = mock_websocket_user.id
        mock_connection.status = ConnectionStatus.CONNECTED
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.active_connections_count = 1
        
        with patch.object(connection_service, '_get_repository') as mock_conn_repo:
            mock_conn_repo.return_value.create_connection = AsyncMock(return_value=mock_connection)
            mock_conn_repo.return_value.get_by_connection_id = AsyncMock(return_value=mock_connection)
            mock_conn_repo.return_value.update_connection_status = AsyncMock(return_value=True)
        
        with patch.object(connection_service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        with patch.object(presence_service, '_get_repository') as mock_pres_repo:
            mock_pres_repo.return_value.increment_connections = AsyncMock(return_value=True)
            mock_pres_repo.return_value.decrement_connections = AsyncMock(return_value=True)
            mock_pres_repo.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
        
        with patch.object(connection_service, '_send_connection_notifications'):
            pass
        
        # Act & Assert - Connection Creation
        performance_timer.start()
        connection_data = WebSocketConnectionCreate(
            connection_id="test_conn_123",
            user_id=mock_websocket_user.id,
            client_type="web",
            ip_address="127.0.0.1"
        )
        
        created_connection = await connection_service.create_connection(
            connection_data, mock_websocket_user
        )
        performance_timer.stop()
        
        assert created_connection is not None
        assert created_connection.connection_id == "test_conn_123"
        performance_timer.assert_under_ms(500, "Connection creation")
        
        # Act & Assert - Authentication
        performance_timer.start()
        auth_success = await connection_service.authenticate_connection(
            "test_conn_123", jwt_token, mock_websocket_user
        )
        performance_timer.stop()
        
        assert auth_success is True
        performance_timer.assert_under_ms(200, "Connection authentication")
        
        # Act & Assert - Presence Update
        performance_timer.start()
        updated_presence = await presence_service.increment_user_connections(
            mock_websocket_user.id, mock_websocket_user
        )
        performance_timer.stop()
        
        assert updated_presence is not None
        assert updated_presence.active_connections_count == 1
        performance_timer.assert_under_ms(100, "Presence update")
        
        # Act & Assert - Disconnection
        performance_timer.start()
        disconnect_success = await connection_service.disconnect_connection(
            "test_conn_123", "Test completed"
        )
        performance_timer.stop()
        
        assert disconnect_success is True
        performance_timer.assert_under_ms(200, "Connection disconnection")
    
    async def test_cross_service_communication(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_event_factory,
        performance_timer
    ):
        """Test cross-service communication between WebSocket services."""
        # Arrange
        connection_service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        event_service = WebSocketEventService(mock_async_session, mock_cache_manager)
        presence_service = UserPresenceService(mock_async_session, mock_cache_manager)
        
        # Mock database objects
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.status = ConnectionStatus.AUTHENTICATED
        
        mock_event = MagicMock()
        mock_event.id = 1
        mock_event.event_id = "evt_123"
        mock_event.event_type = "message.sent"
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.status = "online"
        
        # Mock repository operations
        with patch.object(connection_service, '_get_repository') as mock_conn_repo:
            mock_conn_repo.return_value.get_active_connections_by_user = AsyncMock(
                return_value=[mock_connection]
            )
        
        with patch.object(event_service, 'connection_repository') as mock_event_conn_repo:
            mock_event_conn_repo.get = AsyncMock(return_value=mock_connection)
        
        with patch.object(event_service, '_get_repository') as mock_event_repo:
            mock_event_repo.return_value.create_event = AsyncMock(return_value=mock_event)
        
        with patch.object(presence_service, '_get_repository') as mock_pres_repo:
            mock_pres_repo.return_value.update_user_status = AsyncMock(return_value=True)
            mock_pres_repo.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
        
        # Act & Assert - Get user connections
        performance_timer.start()
        user_connections = await connection_service.get_user_connections(
            mock_websocket_user.id, mock_websocket_user
        )
        performance_timer.stop()
        
        assert len(user_connections) == 1
        performance_timer.assert_under_ms(200, "Get user connections")
        
        # Act & Assert - Create event for connection
        performance_timer.start()
        event_data = WebSocketEventCreate(
            connection_id=1,
            event_type=EventTypeEnum.MESSAGE_SENT,
            priority=EventPriorityEnum.NORMAL,
            payload={"message": "Cross-service test"}
        )
        
        created_event = await event_service.create_event(
            event_data, mock_websocket_user, moderate_content=False
        )
        performance_timer.stop()
        
        assert created_event is not None
        assert created_event.event_id == "evt_123"
        performance_timer.assert_under_ms(500, "Event creation")
        
        # Act & Assert - Update presence status
        performance_timer.start()
        updated_presence = await presence_service.update_user_status(
            mock_websocket_user.id, "away", "cross_service_test", mock_websocket_user
        )
        performance_timer.stop()
        
        assert updated_presence is not None
        assert updated_presence.status == "online"  # From mock
        performance_timer.assert_under_ms(100, "Presence status update")
    
    async def test_database_transaction_rollback(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user
    ):
        """Test database transaction rollback on service errors."""
        # Arrange
        connection_service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        
        # Mock database error
        mock_async_session.commit.side_effect = Exception("Database error")
        mock_async_session.rollback = AsyncMock()
        
        with patch.object(connection_service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_connection = AsyncMock(side_effect=Exception("Database error"))
        
        with patch.object(connection_service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        # Act & Assert
        connection_data = WebSocketConnectionCreate(
            connection_id="test_conn_rollback",
            user_id=mock_websocket_user.id,
            client_type="web"
        )
        
        with pytest.raises(Exception):
            await connection_service.create_connection(connection_data, mock_websocket_user)
        
        # Verify rollback was called
        mock_async_session.rollback.assert_called()


@pytest.mark.integration
class TestWebSocketEndpointIntegration:
    """Integration tests for WebSocket API endpoints."""
    
    @pytest.fixture
    def test_app(self):
        """Create test FastAPI app with WebSocket endpoints."""
        app = FastAPI()
        app.include_router(router, prefix="/websocket")
        return app
    
    async def test_http_endpoints_integration(
        self, 
        test_app,
        mock_websocket_user,
        performance_timer
    ):
        """Test HTTP endpoint integration with mocked dependencies."""
        # Arrange
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            
            # Mock authentication
            with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
                mock_auth.return_value = mock_websocket_user
                
                # Mock database session
                with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                    mock_db.return_value = AsyncMock()
                    
                    # Mock service methods
                    with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                        mock_service.return_value.get_user_connections = AsyncMock(return_value=[])
                        
                        # Act & Assert - Get connections
                        performance_timer.start()
                        response = await client.get("/websocket/connections")
                        performance_timer.stop()
                        
                        assert response.status_code == 200
                        assert response.json() == []
                        performance_timer.assert_under_ms(200, "HTTP endpoint response")
    
    async def test_presence_endpoint_integration(
        self, 
        test_app,
        mock_websocket_user,
        performance_timer
    ):
        """Test presence endpoint integration."""
        # Arrange
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            
            # Mock authentication
            with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
                mock_auth.return_value = mock_websocket_user
                
                # Mock database session
                with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                    mock_db.return_value = AsyncMock()
                    
                    # Mock presence service
                    mock_presence = MagicMock()
                    mock_presence.user_id = mock_websocket_user.id
                    mock_presence.status = "online"
                    mock_presence.active_connections_count = 1
                    
                    with patch('app.api.v1.endpoints.websocket_endpoints.UserPresenceService') as mock_service:
                        mock_service.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
                        
                        # Act & Assert - Get presence
                        performance_timer.start()
                        response = await client.get("/websocket/presence/me")
                        performance_timer.stop()
                        
                        assert response.status_code == 200
                        performance_timer.assert_under_ms(200, "Presence endpoint response")
    
    async def test_health_endpoint_performance(
        self, 
        test_app,
        performance_timer
    ):
        """Test health endpoint performance."""
        # Arrange
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            
            # Act & Assert
            performance_timer.start()
            response = await client.get("/websocket/health")
            performance_timer.stop()
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "healthy"
            assert "metrics" in response_data
            assert "performance" in response_data
            performance_timer.assert_under_ms(100, "Health endpoint response")


@pytest.mark.integration
class TestWebSocketConnectionManager:
    """Integration tests for WebSocket connection manager."""
    
    async def test_connection_manager_lifecycle(
        self, 
        websocket_test_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test connection manager lifecycle with multiple connections."""
        # Arrange
        connection_manager.active_connections.clear()
        connection_manager.user_connections.clear()
        
        # Act & Assert - Connect multiple connections
        performance_timer.start()
        
        conn1 = await websocket_test_client.connect(token="token1")
        await connection_manager.connect(MagicMock(), conn1, mock_websocket_user.id)
        
        conn2 = await websocket_test_client.connect(token="token2")
        await connection_manager.connect(MagicMock(), conn2, mock_websocket_user.id)
        
        performance_timer.stop()
        
        assert connection_manager.get_connection_count() == 2
        assert connection_manager.get_user_connection_count(mock_websocket_user.id) == 2
        performance_timer.assert_under_ms(100, "Multiple connection setup")
        
        # Act & Assert - Send messages
        performance_timer.start()
        sent_count = await connection_manager.send_to_user("Test message", mock_websocket_user.id)
        performance_timer.stop()
        
        assert sent_count == 2
        performance_timer.assert_under_ms(100, "Message broadcasting")
        
        # Act & Assert - Disconnect
        performance_timer.start()
        connection_manager.disconnect(conn1)
        connection_manager.disconnect(conn2)
        performance_timer.stop()
        
        assert connection_manager.get_connection_count() == 0
        assert connection_manager.get_user_connection_count(mock_websocket_user.id) == 0
        performance_timer.assert_under_ms(50, "Connection cleanup")
    
    async def test_rate_limiting_integration(
        self, 
        websocket_test_client,
        mock_websocket_user,
        rate_limit_tester
    ):
        """Test rate limiting integration with connection manager."""
        # Arrange
        connection_manager.active_connections.clear()
        connection_manager.rate_limiters.clear()
        
        conn_id = await websocket_test_client.connect()
        await connection_manager.connect(MagicMock(), conn_id, mock_websocket_user.id)
        
        # Act - Make requests within rate limit
        for _ in range(50):  # Under the 100/minute limit
            allowed = await connection_manager.check_rate_limit(conn_id)
            assert allowed is True
            await rate_limit_tester.make_requests(1, 0.001)
        
        # Act - Exceed rate limit
        for _ in range(60):  # This should exceed the limit
            await connection_manager.check_rate_limit(conn_id)
            await rate_limit_tester.make_requests(1, 0.001)
        
        # Assert - Rate limiting is working
        rate_limit_tester.assert_rate_limited(100, 60)  # 100 requests per minute
        
        # Cleanup
        connection_manager.disconnect(conn_id)


@pytest.mark.integration
class TestWebSocketMessageRouting:
    """Integration tests for WebSocket message routing."""
    
    async def test_message_routing_integration(
        self, 
        websocket_test_client,
        mock_websocket_user,
        mock_admin_user,
        performance_timer
    ):
        """Test message routing between different user types."""
        # Arrange
        connection_manager.active_connections.clear()
        connection_manager.user_connections.clear()
        
        # Connect users
        user_conn = await websocket_test_client.connect(token="user_token")
        await connection_manager.connect(MagicMock(), user_conn, mock_websocket_user.id)
        
        admin_conn = await websocket_test_client.connect(token="admin_token")
        await connection_manager.connect(MagicMock(), admin_conn, mock_admin_user.id)
        
        # Act & Assert - Broadcast to all
        performance_timer.start()
        broadcast_count = await connection_manager.broadcast("System announcement")
        performance_timer.stop()
        
        assert broadcast_count == 2
        performance_timer.assert_under_ms(100, "Broadcast message")
        
        # Act & Assert - Send to specific user
        performance_timer.start()
        user_count = await connection_manager.send_to_user("User message", mock_websocket_user.id)
        performance_timer.stop()
        
        assert user_count == 1
        performance_timer.assert_under_ms(50, "User-specific message")
        
        # Act & Assert - Exclude connections from broadcast
        performance_timer.start()
        excluded_count = await connection_manager.broadcast(
            "Excluded message", 
            exclude_connections={user_conn}
        )
        performance_timer.stop()
        
        assert excluded_count == 1  # Only admin should receive
        performance_timer.assert_under_ms(50, "Excluded broadcast")
        
        # Cleanup
        connection_manager.disconnect(user_conn)
        connection_manager.disconnect(admin_conn)
