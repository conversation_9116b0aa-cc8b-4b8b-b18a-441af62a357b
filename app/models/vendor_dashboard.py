"""
Vendor Dashboard models for Culture Connect Backend API.

This module defines comprehensive vendor dashboard models including:
- VendorDashboardMetrics: Performance metrics and KPI tracking
- VendorActivityFeed: Activity logging and feed management
- VendorNotification: Notification system with priority and status
- VendorQuickAction: Quick action items and workflow optimization
- VendorAnalytics: Comprehensive analytics and reporting data

Implements Task 3.3.1 requirements for vendor dashboard and analytics
with production-grade PostgreSQL optimization and seamless integration with
existing marketplace systems from Tasks 3.2.1, 3.2.2, 3.2.3, and 3.2.4.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class MetricType(str, Enum):
    """Metric type enumeration for dashboard metrics."""
    REVENUE = "revenue"
    BOOKINGS = "bookings"
    VIEWS = "views"
    INQUIRIES = "inquiries"
    CONVERSION_RATE = "conversion_rate"
    RATING = "rating"
    RESPONSE_TIME = "response_time"
    COMPLETION_RATE = "completion_rate"


class ActivityType(str, Enum):
    """Activity type enumeration for vendor activity feed."""
    SERVICE_CREATED = "service_created"
    SERVICE_UPDATED = "service_updated"
    SERVICE_PUBLISHED = "service_published"
    BOOKING_RECEIVED = "booking_received"
    BOOKING_CONFIRMED = "booking_confirmed"
    BOOKING_COMPLETED = "booking_completed"
    REVIEW_RECEIVED = "review_received"
    PAYMENT_RECEIVED = "payment_received"
    PROFILE_UPDATED = "profile_updated"
    CONTENT_MODERATED = "content_moderated"
    RANKING_CHANGED = "ranking_changed"


class NotificationType(str, Enum):
    """Notification type enumeration."""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    URGENT = "urgent"


class NotificationPriority(str, Enum):
    """Notification priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ActionType(str, Enum):
    """Quick action type enumeration."""
    OPTIMIZE_SERVICE = "optimize_service"
    IMPROVE_CONTENT = "improve_content"
    UPDATE_PRICING = "update_pricing"
    RESPOND_TO_INQUIRY = "respond_to_inquiry"
    COMPLETE_PROFILE = "complete_profile"
    UPLOAD_MEDIA = "upload_media"
    SET_AVAILABILITY = "set_availability"
    REVIEW_BOOKING = "review_booking"


class VendorDashboardMetrics(Base):
    """
    Vendor dashboard metrics model for performance tracking and KPI management.

    Tracks comprehensive performance metrics including revenue, bookings,
    service performance, and marketplace analytics for vendor insights.
    """
    __tablename__ = "vendor_dashboard_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)

    # Metric identification
    metric_type = Column(String(50), nullable=False, index=True)
    metric_name = Column(String(100), nullable=False)
    metric_description = Column(Text)

    # Metric values
    current_value = Column(Float, default=0.0)
    previous_value = Column(Float, default=0.0)
    target_value = Column(Float)

    # Percentage changes and trends
    percentage_change = Column(Float, default=0.0)
    trend_direction = Column(String(20), default="stable")  # up, down, stable

    # Time period information
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    comparison_period_start = Column(DateTime(timezone=True))
    comparison_period_end = Column(DateTime(timezone=True))

    # Additional metric data
    metric_data = Column(JSONB, default=dict)
    breakdown_data = Column(JSONB, default=dict)  # Category/service breakdown

    # Performance indicators
    performance_score = Column(Float, default=0.0)
    benchmark_comparison = Column(Float, default=0.0)
    market_position = Column(Integer)  # Ranking position

    # Metadata
    calculated_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True, index=True)

    # Relationships
    vendor = relationship("Vendor", back_populates="dashboard_metrics")

    # Indexes
    __table_args__ = (
        Index("idx_vendor_metrics_vendor_type", "vendor_id", "metric_type"),
        Index("idx_vendor_metrics_period", "period_start", "period_end"),
        Index("idx_vendor_metrics_calculated", "calculated_at"),
        Index("idx_vendor_metrics_active", "is_active"),
        UniqueConstraint("vendor_id", "metric_type", "period_start", "period_end", name="uq_vendor_metrics_period"),
        CheckConstraint("current_value >= 0", name="ck_vendor_metrics_current_positive"),
        CheckConstraint("previous_value >= 0", name="ck_vendor_metrics_previous_positive"),
        CheckConstraint("performance_score >= 0 AND performance_score <= 100", name="ck_vendor_metrics_performance"),
        CheckConstraint("period_end > period_start", name="ck_vendor_metrics_period_valid"),
    )


class VendorActivityFeed(Base):
    """
    Vendor activity feed model for tracking and displaying vendor activities.

    Maintains comprehensive activity log with real-time updates for
    vendor dashboard activity feed and timeline visualization.
    """
    __tablename__ = "vendor_activity_feed"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)

    # Activity identification
    activity_type = Column(String(50), nullable=False, index=True)
    activity_title = Column(String(200), nullable=False)
    activity_description = Column(Text)

    # Related entity information
    related_entity_type = Column(String(50))  # service, booking, review, etc.
    related_entity_id = Column(PostgreSQLUUID(as_uuid=True))
    related_entity_data = Column(JSONB, default=dict)

    # Activity metadata
    activity_data = Column(JSONB, default=dict)
    impact_score = Column(Float, default=0.0)  # Business impact of activity
    visibility_level = Column(String(20), default="normal")  # high, normal, low

    # Status and tracking
    is_read = Column(Boolean, default=False, index=True)
    is_important = Column(Boolean, default=False, index=True)
    is_archived = Column(Boolean, default=False, index=True)

    # User interaction
    user_action_required = Column(Boolean, default=False, index=True)
    action_deadline = Column(DateTime(timezone=True))
    action_completed = Column(Boolean, default=False)

    # Timing information
    occurred_at = Column(DateTime(timezone=True), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    vendor = relationship("Vendor", back_populates="activity_feed")

    # Indexes
    __table_args__ = (
        Index("idx_vendor_activity_vendor_type", "vendor_id", "activity_type"),
        Index("idx_vendor_activity_occurred", "occurred_at"),
        Index("idx_vendor_activity_unread", "vendor_id", "is_read"),
        Index("idx_vendor_activity_important", "vendor_id", "is_important"),
        Index("idx_vendor_activity_action_required", "vendor_id", "user_action_required"),
        Index("idx_vendor_activity_deadline", "action_deadline"),
        CheckConstraint("impact_score >= 0 AND impact_score <= 10", name="ck_vendor_activity_impact"),
    )


class VendorNotification(Base):
    """
    Vendor notification model for managing alerts and system notifications.

    Provides priority-based notification system with actionable alerts,
    delivery tracking, and comprehensive notification management.
    """
    __tablename__ = "vendor_notifications"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)

    # Notification content
    notification_type = Column(String(20), nullable=False, index=True)
    priority = Column(String(20), nullable=False, default=NotificationPriority.MEDIUM, index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)

    # Rich content and actions
    rich_content = Column(JSONB, default=dict)  # HTML, images, links
    action_buttons = Column(JSONB, default=list)  # Quick action buttons
    action_url = Column(String(500))  # Deep link or action URL

    # Categorization and targeting
    category = Column(String(50), index=True)  # booking, payment, content, etc.
    tags = Column(JSONB, default=list)

    # Status tracking
    is_read = Column(Boolean, default=False, index=True)
    is_dismissed = Column(Boolean, default=False, index=True)
    is_actionable = Column(Boolean, default=False, index=True)
    action_taken = Column(Boolean, default=False)

    # Delivery and interaction
    delivery_method = Column(JSONB, default=list)  # push, email, sms, in_app
    delivered_at = Column(DateTime(timezone=True))
    read_at = Column(DateTime(timezone=True))
    action_taken_at = Column(DateTime(timezone=True))

    # Scheduling and expiration
    scheduled_for = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor = relationship("Vendor", back_populates="notifications")

    # Indexes
    __table_args__ = (
        Index("idx_vendor_notification_vendor_type", "vendor_id", "notification_type"),
        Index("idx_vendor_notification_priority", "vendor_id", "priority"),
        Index("idx_vendor_notification_unread", "vendor_id", "is_read"),
        Index("idx_vendor_notification_actionable", "vendor_id", "is_actionable"),
        Index("idx_vendor_notification_category", "vendor_id", "category"),
        Index("idx_vendor_notification_scheduled", "scheduled_for"),
        Index("idx_vendor_notification_expires", "expires_at"),
        Index("idx_vendor_notification_created", "created_at"),
    )


class VendorQuickAction(Base):
    """
    Vendor quick action model for workflow optimization and performance enhancement.

    Provides intelligent quick action recommendations based on vendor
    performance, marketplace data, and optimization opportunities.
    """
    __tablename__ = "vendor_quick_actions"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)

    # Action identification
    action_type = Column(String(50), nullable=False, index=True)
    action_title = Column(String(200), nullable=False)
    action_description = Column(Text)

    # Action details
    action_url = Column(String(500))  # Deep link or action URL
    action_data = Column(JSONB, default=dict)  # Action parameters
    estimated_impact = Column(String(20), default="medium")  # low, medium, high
    estimated_time_minutes = Column(Integer, default=5)

    # Priority and urgency
    priority_score = Column(Float, default=5.0)  # 1-10 scale
    urgency_level = Column(String(20), default="normal")  # low, normal, high, urgent

    # Business impact
    potential_revenue_impact = Column(Float, default=0.0)
    potential_booking_impact = Column(Integer, default=0)
    potential_ranking_impact = Column(Float, default=0.0)

    # Status and tracking
    is_active = Column(Boolean, default=True, index=True)
    is_completed = Column(Boolean, default=False, index=True)
    is_dismissed = Column(Boolean, default=False)

    # Completion tracking
    completed_at = Column(DateTime(timezone=True))
    completion_method = Column(String(50))  # manual, automated, assisted
    completion_notes = Column(Text)

    # Effectiveness tracking
    actual_impact = Column(JSONB, default=dict)
    effectiveness_score = Column(Float)  # Measured after completion

    # Scheduling and expiration
    suggested_completion_date = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor = relationship("Vendor", back_populates="quick_actions")

    # Indexes
    __table_args__ = (
        Index("idx_vendor_action_vendor_type", "vendor_id", "action_type"),
        Index("idx_vendor_action_priority", "vendor_id", "priority_score"),
        Index("idx_vendor_action_active", "vendor_id", "is_active"),
        Index("idx_vendor_action_pending", "vendor_id", "is_completed"),
        Index("idx_vendor_action_urgency", "vendor_id", "urgency_level"),
        Index("idx_vendor_action_suggested", "suggested_completion_date"),
        Index("idx_vendor_action_expires", "expires_at"),
        CheckConstraint("priority_score >= 1 AND priority_score <= 10", name="ck_vendor_action_priority"),
        CheckConstraint("estimated_time_minutes >= 0", name="ck_vendor_action_time_positive"),
        CheckConstraint("potential_revenue_impact >= 0", name="ck_vendor_action_revenue_positive"),
        CheckConstraint("potential_booking_impact >= 0", name="ck_vendor_action_booking_positive"),
    )


# VendorAnalytics model is defined in app.models.analytics_models
# to avoid duplicate table definitions
