"""
Email Preference Service for Culture Connect Backend API.

This module provides comprehensive email preference management including:
- User email notification preferences management
- Opt-out and opt-in functionality with GDPR compliance
- Category-based email preference controls
- Preference validation and default settings
- Integration with email delivery for preference checking

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
production-grade preference management, GDPR compliance, and user control.
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.models.email_models import EmailPreference, EmailTemplateCategory
from app.repositories.email_repository import EmailPreferenceRepository
from app.schemas.email_schemas import (
    EmailPreferenceUpdate, EmailPreferenceResponse,
    EmailOptOutRequest, EmailOptOutResponse
)
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class EmailPreferenceService(BaseService[EmailPreference, EmailPreferenceRepository]):
    """
    Email preference service for user notification preference management.

    Provides comprehensive preference management with GDPR compliance,
    category-based controls, and integration with email delivery systems.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email preference service."""
        super().__init__(EmailPreferenceRepository, EmailPreference, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for email preference creation."""
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for email preference updates."""
        return data

    async def get_user_preferences(self, user_id: int) -> EmailPreferenceResponse:
        """
        Get user email preferences, creating defaults if none exist.

        Args:
            user_id: User ID

        Returns:
            EmailPreferenceResponse: User email preferences

        Raises:
            ServiceError: If operation fails
        """
        try:
            preferences = await self.repository.get_user_preferences(user_id)

            if not preferences:
                # Create default preferences for user
                preferences = await self.repository.create_default_preferences(user_id)

                logger.info(
                    f"Created default email preferences for user",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "preferences_id": str(preferences.id)
                    }
                )

            return EmailPreferenceResponse.model_validate(preferences)

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving email preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve email preferences: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error retrieving email preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error retrieving email preferences: {str(e)}")

    async def update_user_preferences(
        self,
        user_id: int,
        preference_update: EmailPreferenceUpdate
    ) -> EmailPreferenceResponse:
        """
        Update user email preferences.

        Args:
            user_id: User ID
            preference_update: Preference update data

        Returns:
            EmailPreferenceResponse: Updated preferences

        Raises:
            ValidationError: If preference data is invalid
            ServiceError: If operation fails
        """
        try:
            # Validate preference update
            self._validate_preference_update(preference_update)

            # Get or create preferences
            preferences = await self.repository.get_user_preferences(user_id)
            if not preferences:
                preferences = await self.repository.create_default_preferences(user_id)

            # Update preferences
            updated_preferences = await self.repository.update_user_preferences(
                user_id=user_id,
                **preference_update.model_dump(exclude_unset=True)
            )

            logger.info(
                f"Email preferences updated successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "preferences_id": str(updated_preferences.id),
                    "updated_fields": list(preference_update.model_dump(exclude_unset=True).keys())
                }
            )

            return EmailPreferenceResponse.model_validate(updated_preferences)

        except ValidationError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error updating email preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to update email preferences: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error updating email preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error updating email preferences: {str(e)}")

    async def opt_out_user(
        self,
        user_id: int,
        opt_out_request: EmailOptOutRequest
    ) -> EmailOptOutResponse:
        """
        Opt user out of email communications.

        Args:
            user_id: User ID
            opt_out_request: Opt-out request data

        Returns:
            EmailOptOutResponse: Opt-out confirmation

        Raises:
            ServiceError: If operation fails
        """
        try:
            # Get or create preferences
            preferences = await self.repository.get_user_preferences(user_id)
            if not preferences:
                preferences = await self.repository.create_default_preferences(user_id)

            # Determine what to opt out of
            if opt_out_request.opt_out_all:
                # Opt out of all non-essential emails
                update_data = {
                    "marketing_emails": False,
                    "booking_notifications": False,
                    "vendor_notifications": False,
                    "opted_out_at": datetime.utcnow()
                }
                opt_out_categories = ["marketing", "booking", "vendor"]
            else:
                # Opt out of specific categories
                update_data = {"opted_out_at": datetime.utcnow()}
                opt_out_categories = []

                for category in opt_out_request.categories or []:
                    if category == EmailTemplateCategory.MARKETING:
                        update_data["marketing_emails"] = False
                        opt_out_categories.append("marketing")
                    elif category == EmailTemplateCategory.BOOKING:
                        update_data["booking_notifications"] = False
                        opt_out_categories.append("booking")
                    elif category == EmailTemplateCategory.NOTIFICATION:
                        update_data["vendor_notifications"] = False
                        opt_out_categories.append("vendor")

            # Update preferences
            updated_preferences = await self.repository.update_user_preferences(
                user_id=user_id,
                **update_data
            )

            logger.info(
                f"User opted out of email categories",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "opt_out_all": opt_out_request.opt_out_all,
                    "categories": opt_out_categories
                }
            )

            return EmailOptOutResponse(
                user_id=user_id,
                opted_out_categories=opt_out_categories,
                opted_out_at=updated_preferences.opted_out_at,
                message="Successfully opted out of email communications"
            )

        except SQLAlchemyError as e:
            logger.error(
                f"Database error during email opt-out: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to opt out of emails: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error during email opt-out: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error during email opt-out: {str(e)}")

    async def opt_in_user(
        self,
        user_id: int,
        categories: Optional[list[EmailTemplateCategory]] = None
    ) -> EmailPreferenceResponse:
        """
        Opt user back into email communications.

        Args:
            user_id: User ID
            categories: Optional specific categories to opt into

        Returns:
            EmailPreferenceResponse: Updated preferences

        Raises:
            ServiceError: If operation fails
        """
        try:
            # Get or create preferences
            preferences = await self.repository.get_user_preferences(user_id)
            if not preferences:
                preferences = await self.repository.create_default_preferences(user_id)

            # Determine what to opt into
            if not categories:
                # Opt into all categories (restore defaults)
                update_data = {
                    "verification_emails": True,
                    "security_emails": True,
                    "marketing_emails": False,  # Keep marketing opt-in explicit
                    "booking_notifications": True,
                    "vendor_notifications": True,
                    "system_notifications": True,
                    "opted_out_at": None
                }
            else:
                # Opt into specific categories
                update_data = {"opted_out_at": None}

                for category in categories:
                    if category == EmailTemplateCategory.MARKETING:
                        update_data["marketing_emails"] = True
                    elif category == EmailTemplateCategory.BOOKING:
                        update_data["booking_notifications"] = True
                    elif category == EmailTemplateCategory.NOTIFICATION:
                        update_data["vendor_notifications"] = True
                    elif category == EmailTemplateCategory.VERIFICATION:
                        update_data["verification_emails"] = True
                    elif category == EmailTemplateCategory.SECURITY:
                        update_data["security_emails"] = True
                    elif category == EmailTemplateCategory.SYSTEM:
                        update_data["system_notifications"] = True

            # Update preferences
            updated_preferences = await self.repository.update_user_preferences(
                user_id=user_id,
                **update_data
            )

            logger.info(
                f"User opted back into email categories",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "categories": [cat.value for cat in categories] if categories else "all"
                }
            )

            return EmailPreferenceResponse.model_validate(updated_preferences)

        except SQLAlchemyError as e:
            logger.error(
                f"Database error during email opt-in: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to opt into emails: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error during email opt-in: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error during email opt-in: {str(e)}")

    async def can_send_email(
        self,
        user_id: int,
        email_category: EmailTemplateCategory
    ) -> bool:
        """
        Check if user can receive emails of a specific category.

        Args:
            user_id: User ID
            email_category: Email category to check

        Returns:
            bool: True if user can receive emails of this category

        Raises:
            ServiceError: If operation fails
        """
        try:
            return await self.repository.can_send_email(user_id, email_category)

        except Exception as e:
            logger.error(
                f"Error checking email permission: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "category": email_category.value,
                    "error_type": type(e).__name__
                }
            )
            # Default to allowing essential emails (verification, security)
            return email_category in [
                EmailTemplateCategory.VERIFICATION,
                EmailTemplateCategory.SECURITY,
                EmailTemplateCategory.SYSTEM
            ]

    async def get_preference_statistics(self) -> Dict[str, Any]:
        """
        Get email preference statistics for analytics.

        Returns:
            Dict[str, Any]: Preference statistics

        Raises:
            ServiceError: If operation fails
        """
        try:
            stats = await self.repository.get_preference_statistics()

            logger.info(
                f"Retrieved email preference statistics",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "total_users": stats.get("total_users", 0),
                    "opted_out_users": stats.get("opted_out_users", 0)
                }
            )

            return stats

        except Exception as e:
            logger.error(
                f"Error retrieving preference statistics: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve preference statistics: {str(e)}")

    def _validate_preference_update(self, preference_update: EmailPreferenceUpdate) -> None:
        """
        Validate email preference update data.

        Args:
            preference_update: Preference update data

        Raises:
            ValidationError: If data is invalid
        """
        # Ensure essential emails cannot be disabled
        if preference_update.verification_emails is False:
            raise ValidationError("Verification emails cannot be disabled")

        if preference_update.security_emails is False:
            raise ValidationError("Security emails cannot be disabled")

        if preference_update.system_notifications is False:
            raise ValidationError("System notifications cannot be disabled")

        # Validate that at least one update field is provided
        update_fields = preference_update.model_dump(exclude_unset=True)
        if not update_fields:
            raise ValidationError("At least one preference field must be updated")
