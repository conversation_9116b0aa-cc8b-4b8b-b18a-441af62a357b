# Culture Connect Backend - Redis Development Configuration
# Redis configuration optimized for development environment
# This configuration prioritizes ease of use and debugging over security

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Bind to all interfaces for development
bind 0.0.0.0

# Default port
port 6379

# No password for development (not recommended for production)
# requirepass your_redis_password

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Set the number of databases
databases 16

# Enable logging
loglevel notice
logfile ""

# =============================================================================
# MEMORY CONFIGURATION
# =============================================================================

# Maximum memory usage (128MB for development)
maxmemory 128mb

# Memory eviction policy
maxmemory-policy allkeys-lru

# =============================================================================
# PERSISTENCE CONFIGURATION
# =============================================================================

# Save the DB on disk (development settings)
save 900 1
save 300 10
save 60 10000

# Disable RDB compression for faster saves in development
rdbcompression no

# RDB file name
dbfilename dump.rdb

# Working directory
dir /data

# =============================================================================
# APPEND ONLY FILE (AOF) CONFIGURATION
# =============================================================================

# Enable AOF for development (optional)
appendonly no

# AOF filename
appendfilename "appendonly.aof"

# AOF sync policy
appendfsync everysec

# =============================================================================
# SLOW LOG CONFIGURATION
# =============================================================================

# Log queries slower than 10ms
slowlog-log-slower-than 10000

# Keep last 128 slow queries
slowlog-max-len 128

# =============================================================================
# CLIENT CONFIGURATION
# =============================================================================

# Maximum number of clients
maxclients 100

# Client timeout (0 = no timeout)
timeout 0

# =============================================================================
# DEVELOPMENT SPECIFIC SETTINGS
# =============================================================================

# Disable protected mode for development
protected-mode no

# Enable keyspace notifications for development
notify-keyspace-events "Ex"

# =============================================================================
# PERFORMANCE TUNING (DEVELOPMENT)
# =============================================================================

# TCP keepalive
tcp-keepalive 300

# TCP backlog
tcp-backlog 511

# =============================================================================
# SECURITY (DEVELOPMENT)
# =============================================================================

# Disable dangerous commands in development (optional)
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# =============================================================================
# MODULES (IF NEEDED)
# =============================================================================

# Load modules if needed
# loadmodule /path/to/module.so

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================

# This configuration is optimized for development:
# 1. No authentication for ease of use
# 2. Binds to all interfaces for container access
# 3. Lower memory limits suitable for development
# 4. Simplified persistence settings
# 5. Enhanced logging for debugging
#
# DO NOT USE THIS CONFIGURATION IN PRODUCTION!
# Production configurations should include:
# - Strong authentication (requirepass)
# - Restricted bind addresses
# - Proper SSL/TLS configuration
# - Enhanced security settings
# - Optimized memory and persistence settings
