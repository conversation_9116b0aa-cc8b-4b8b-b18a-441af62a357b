"""
Authentication endpoints for Culture Connect Backend API.

This module provides production-ready authentication and authorization endpoints:
- User registration with email verification
- User login with email/password and OAuth
- JWT token management (access and refresh)
- Password reset and change functionality
- OAuth integration (Google, Facebook)
- Account verification and management

Implements Task 2.2.1 requirements with complete integration to existing
AuthService, OAuthService, and PasswordSecurityService infrastructure.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.auth_service import AuthService
from app.services.oauth_service import OAuthService
from app.services.password_security_service import PasswordSecurityService
from app.schemas.auth import (
    UserCreate, UserLogin, UserResponse, LoginResponse, TokenResponse,
    TokenRefresh, PasswordReset, PasswordResetRequest, PasswordChange,
    EmailVerification, MessageResponse, LogoutRequest
)
from app.schemas.oauth_schemas import (
    OAuthAuthorizationResponse, OAuthCallbackResponse
)
from app.api.v1.auth import get_current_user
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Security scheme for protected endpoints
security = HTTPBearer()

# Create router
router = APIRouter()


@router.post(
    "/login",
    response_model=LoginResponse,
    status_code=status.HTTP_200_OK,
    summary="User login",
    description="Authenticate user with email and password, return JWT tokens"
)
async def login(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> LoginResponse:
    """
    Authenticate user and return JWT tokens.

    Args:
        login_data: User login credentials (email and password)
        request: HTTP request object for client IP and user agent
        db: Database session dependency

    Returns:
        LoginResponse: User data and authentication tokens

    Raises:
        HTTPException: 401 if credentials are invalid
        HTTPException: 401 if account is locked or deactivated
        HTTPException: 500 if authentication service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # Initialize authentication service
        auth_service = AuthService(db)

        # Authenticate user
        user, tokens = await auth_service.authenticate_user(
            login_data, client_ip, user_agent
        )

        logger.info(
            f"User login successful: {user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": str(user.id),
                "client_ip": client_ip,
                "user_agent": user_agent
            }
        )

        return LoginResponse(
            user=user,
            tokens=TokenResponse(
                access_token=tokens.access_token,
                refresh_token=tokens.refresh_token,
                token_type=tokens.token_type,
                expires_in=tokens.expires_in
            ),
            message="Login successful"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Login failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": login_data.email,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="User registration",
    description="Register a new user account with email verification"
)
async def register(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Register a new user account.

    Args:
        user_data: User registration data including email, password, and profile info
        request: HTTP request object for client IP and user agent
        db: Database session dependency

    Returns:
        UserResponse: Created user information (without sensitive data)

    Raises:
        HTTPException: 400 if email already exists
        HTTPException: 422 if validation fails
        HTTPException: 500 if registration service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # Initialize authentication service
        auth_service = AuthService(db)

        # Register user
        user = await auth_service.register_user(user_data, client_ip, user_agent)

        logger.info(
            f"User registration successful: {user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": str(user.id),
                "client_ip": client_ip,
                "user_agent": user_agent
            }
        )

        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"User registration failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": user_data.email,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post(
    "/refresh",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Refresh JWT token",
    description="Refresh access token using valid refresh token"
)
async def refresh_token(
    token_data: TokenRefresh,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> TokenResponse:
    """
    Refresh access token using refresh token.

    Args:
        token_data: Refresh token data
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        TokenResponse: New access and refresh tokens

    Raises:
        HTTPException: 401 if refresh token is invalid or expired
        HTTPException: 500 if token refresh service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize authentication service
        auth_service = AuthService(db)

        # Refresh token
        new_tokens = await auth_service.refresh_token(token_data.refresh_token)

        logger.info(
            "Token refresh successful",
            extra={
                "correlation_id": correlation_id.get(''),
                "client_ip": client_ip
            }
        )

        return new_tokens

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Token refresh failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post(
    "/logout",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="User logout",
    description="Logout user and blacklist current token"
)
async def logout(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Logout user and blacklist current token.

    Args:
        request: HTTP request object for client information
        credentials: Bearer token from Authorization header
        db: Database session dependency

    Returns:
        MessageResponse: Logout confirmation message

    Raises:
        HTTPException: 401 if token is invalid
        HTTPException: 500 if logout service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"
        token = credentials.credentials

        # Initialize authentication service
        auth_service = AuthService(db)

        # Logout user (blacklist token)
        await auth_service.logout_user(token, client_ip)

        logger.info(
            "User logout successful",
            extra={
                "correlation_id": correlation_id.get(''),
                "client_ip": client_ip
            }
        )

        return MessageResponse(message="Logout successful")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Logout failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post(
    "/forgot-password",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Request password reset",
    description="Send password reset email to user"
)
async def forgot_password(
    reset_request: PasswordResetRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Request password reset for user account.

    Args:
        reset_request: Password reset request data (email)
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        MessageResponse: Confirmation message (always success for security)

    Note:
        Always returns success message for security reasons, even if email doesn't exist.
        This prevents email enumeration attacks.
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize password security service
        password_service = PasswordSecurityService(db)

        # Request password reset (service handles email existence check internally)
        await password_service.request_password_reset(
            reset_request.email, client_ip
        )

        logger.info(
            f"Password reset requested for email: {reset_request.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": reset_request.email,
                "client_ip": client_ip
            }
        )

        # Always return success message for security
        return MessageResponse(
            message="If the email exists, a password reset link has been sent"
        )

    except Exception as e:
        logger.error(
            f"Password reset request failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": reset_request.email,
                "error": str(e)
            }
        )
        # Still return success message for security
        return MessageResponse(
            message="If the email exists, a password reset link has been sent"
        )


@router.post(
    "/reset-password",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Reset password",
    description="Reset user password using reset token"
)
async def reset_password(
    reset_data: PasswordReset,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Reset user password using reset token.

    Args:
        reset_data: Password reset data (token and new password)
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        MessageResponse: Password reset confirmation

    Raises:
        HTTPException: 400 if reset token is invalid or expired
        HTTPException: 422 if new password doesn't meet requirements
        HTTPException: 500 if password reset service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize password security service
        password_service = PasswordSecurityService(db)

        # Reset password
        await password_service.reset_password(
            reset_data.token, reset_data.new_password, client_ip
        )

        logger.info(
            "Password reset successful",
            extra={
                "correlation_id": correlation_id.get(''),
                "client_ip": client_ip
            }
        )

        return MessageResponse(message="Password reset successful")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Password reset failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post(
    "/verify-email",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Verify email address",
    description="Verify user email address using verification token"
)
async def verify_email(
    verification_data: EmailVerification,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Verify user email address using verification token.

    Args:
        verification_data: Email verification data (token)
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        MessageResponse: Email verification confirmation

    Raises:
        HTTPException: 400 if verification token is invalid or expired
        HTTPException: 500 if email verification service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize authentication service
        auth_service = AuthService(db)

        # Verify email
        await auth_service.verify_email(verification_data.token, client_ip)

        logger.info(
            "Email verification successful",
            extra={
                "correlation_id": correlation_id.get(''),
                "client_ip": client_ip
            }
        )

        return MessageResponse(message="Email verified successfully")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Email verification failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )


@router.post(
    "/change-password",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Change password",
    description="Change user password (requires authentication)"
)
async def change_password(
    password_data: PasswordChange,
    request: Request,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Change user password (authenticated endpoint).

    Args:
        password_data: Password change data (current and new password)
        request: HTTP request object for client information
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        MessageResponse: Password change confirmation

    Raises:
        HTTPException: 400 if current password is incorrect
        HTTPException: 422 if new password doesn't meet requirements
        HTTPException: 500 if password change service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize password security service
        password_service = PasswordSecurityService(db)

        # Change password
        await password_service.change_password(
            current_user["id"],
            password_data.current_password,
            password_data.new_password,
            client_ip
        )

        logger.info(
            f"Password change successful for user: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "client_ip": client_ip
            }
        )

        return MessageResponse(message="Password changed successfully")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Password change failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.get(
    "/oauth/{provider}/authorize",
    response_model=OAuthAuthorizationResponse,
    status_code=status.HTTP_200_OK,
    summary="OAuth authorization URL",
    description="Get OAuth authorization URL for specified provider"
)
async def oauth_authorize(
    provider: str,
    request: Request,
    db: AsyncSession = Depends(get_async_session),
    redirect_uri: str = Query(..., description="OAuth redirect URI")
) -> OAuthAuthorizationResponse:
    """
    Get OAuth authorization URL for specified provider.

    Args:
        provider: OAuth provider name (google, facebook)
        redirect_uri: OAuth redirect URI after authorization
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        OAuthAuthorizationResponse: Authorization URL and state token

    Raises:
        HTTPException: 404 if provider not found
        HTTPException: 500 if OAuth service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # Initialize OAuth service
        oauth_service = OAuthService(db)

        # Get authorization URL
        auth_response = await oauth_service.get_authorization_url(
            provider_name=provider,
            redirect_uri=redirect_uri,
            client_ip=client_ip,
            user_agent=user_agent
        )

        logger.info(
            f"OAuth authorization URL generated for provider: {provider}",
            extra={
                "correlation_id": correlation_id.get(''),
                "provider": provider,
                "client_ip": client_ip
            }
        )

        return auth_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"OAuth authorization failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "provider": provider,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth authorization failed"
        )


@router.post(
    "/oauth/{provider}/callback",
    response_model=LoginResponse,
    status_code=status.HTTP_200_OK,
    summary="OAuth callback handler",
    description="Handle OAuth callback and authenticate user"
)
async def oauth_callback(
    provider: str,
    callback_data: OAuthCallbackResponse,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> LoginResponse:
    """
    Handle OAuth callback and authenticate user.

    Args:
        provider: OAuth provider name (google, facebook)
        callback_data: OAuth callback data (code, state)
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        LoginResponse: User data and authentication tokens

    Raises:
        HTTPException: 400 if callback data is invalid
        HTTPException: 401 if OAuth authentication fails
        HTTPException: 500 if OAuth service fails
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # Initialize OAuth service
        oauth_service = OAuthService(db)

        # Handle OAuth callback
        user, tokens, is_new_user = await oauth_service.handle_callback(
            provider_name=provider,
            authorization_code=callback_data.code,
            state_token=callback_data.state,
            redirect_uri=callback_data.redirect_uri,
            client_ip=client_ip,
            user_agent=user_agent
        )

        logger.info(
            f"OAuth callback successful for provider: {provider}, new_user: {is_new_user}",
            extra={
                "correlation_id": correlation_id.get(''),
                "provider": provider,
                "user_id": str(user.id),
                "is_new_user": is_new_user,
                "client_ip": client_ip
            }
        )

        return LoginResponse(
            user=user,
            tokens=TokenResponse(
                access_token=tokens.access_token,
                refresh_token=tokens.refresh_token,
                token_type=tokens.token_type,
                expires_in=tokens.expires_in
            ),
            message=f"OAuth login successful via {provider}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"OAuth callback failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "provider": provider,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth authentication failed"
        )


@router.get(
    "/me",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Get current user",
    description="Get current authenticated user information"
)
async def get_current_user_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Get current authenticated user information.

    Args:
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        UserResponse: Current user information

    Raises:
        HTTPException: 401 if token is invalid
        HTTPException: 500 if user service fails
    """
    try:
        # Initialize authentication service
        auth_service = AuthService(db)

        # Get user details
        user = await auth_service.get_user_by_id(current_user["id"])

        logger.info(
            f"User info retrieved: {user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": str(user.id)
            }
        )

        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Get current user failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )


@router.post(
    "/resend-verification",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Resend email verification",
    description="Resend email verification link to user"
)
async def resend_verification(
    email_request: PasswordResetRequest,  # Reuse schema for email
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Resend email verification link to user.

    Args:
        email_request: Email address to send verification to
        request: HTTP request object for client information
        db: Database session dependency

    Returns:
        MessageResponse: Confirmation message (always success for security)

    Note:
        Always returns success message for security reasons, even if email doesn't exist.
    """
    try:
        # Extract client information for security logging
        client_ip = request.client.host if request.client else "unknown"

        # Initialize authentication service
        auth_service = AuthService(db)

        # Resend verification email
        await auth_service.resend_verification_email(
            email_request.email, client_ip
        )

        logger.info(
            f"Verification email resent to: {email_request.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": email_request.email,
                "client_ip": client_ip
            }
        )

        return MessageResponse(
            message="If the email exists, a verification link has been sent"
        )

    except Exception as e:
        logger.error(
            f"Resend verification failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "email": email_request.email,
                "error": str(e)
            }
        )
        # Still return success message for security
        return MessageResponse(
            message="If the email exists, a verification link has been sent"
        )
