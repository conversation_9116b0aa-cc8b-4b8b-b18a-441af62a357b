"""
Payment Services for Culture Connect Backend API.

This module provides comprehensive payment functionality including:
- PaymentService: Core payment processing with multi-provider support
- PaymentMethodService: Payment method management with encrypted storage
- PaymentWorkflowService: Payment workflow orchestration and automation

Implements Step 6 Payment & Transaction Management System service layer with
production-grade functionality, multi-provider integration, and comprehensive business logic.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.payment_repository import PaymentRepository, PaymentMethodRepository
from app.models.payment import Payment, PaymentMethod, PaymentStatus, PaymentMethodType
from app.core.payment.config import PaymentProviderType
from app.schemas.payment_schemas import (
    PaymentCreate, PaymentUpdate, PaymentResponse,
    PaymentMethodCreate, PaymentMethodUpdate, PaymentMethodResponse,
    PaymentProcessRequest, PaymentProcessResponse
)
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.services.geolocation_service import get_geolocation_service, GeolocationResult
from app.core.payment.providers import GeoLocationBasedSelector, PaymentProviderManager

# Configure logging
logger = logging.getLogger(__name__)


class PaymentService(BaseService[Payment, PaymentRepository]):
    """
    Payment service for core payment processing and management.

    Provides comprehensive payment operations including:
    - Multi-provider payment processing (Stripe, Paystack, Busha)
    - Payment status management and workflow automation
    - Geo-location based provider routing
    - Performance-optimized payment operations
    - Comprehensive audit logging and security features
    """

    def __init__(self, db: AsyncSession):
        """Initialize payment service."""
        super().__init__(PaymentRepository, Payment, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate payment creation data with business rules.

        Args:
            data: Payment creation data

        Returns:
            Validated payment data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['booking_id', 'user_id', 'vendor_id', 'amount']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate amount
        amount = data.get('amount')
        if not isinstance(amount, (int, float, Decimal)) or amount <= 0:
            raise ValidationError("Amount must be positive", field="amount")

        # Validate currency
        currency = data.get('currency', 'NGN')
        if currency not in ['NGN', 'USD', 'EUR', 'GBP']:
            raise ValidationError("Invalid currency", field="currency")

        # Validate provider
        provider = data.get('provider', PaymentProviderType.PAYSTACK)
        if not isinstance(provider, PaymentProviderType):
            raise ValidationError("Invalid payment provider", field="provider")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate payment update data with business rules.

        Args:
            data: Payment update data
            existing_id: Existing payment ID

        Returns:
            Validated payment data

        Raises:
            ValidationError: If validation fails
        """
        # Prevent modification of critical fields
        protected_fields = ['booking_id', 'user_id', 'vendor_id', 'amount', 'currency']
        for field in protected_fields:
            if field in data:
                raise ValidationError(f"Cannot modify {field} after creation", field=field)

        # Validate status transitions
        if 'status' in data:
            status = data['status']
            if not isinstance(status, PaymentStatus):
                raise ValidationError("Invalid payment status", field="status")

        return data

    async def create_payment(
        self,
        booking_id: int,
        user_id: int,
        vendor_id: int,
        amount: Decimal,
        currency: str = "NGN",
        provider: Optional[PaymentProviderType] = None,
        payment_method_id: Optional[int] = None,
        crypto_currency: Optional[str] = None,
        preferred_provider: Optional[str] = None,
        geolocation_result: Optional[GeolocationResult] = None,
        **kwargs
    ) -> Payment:
        """
        Create a new payment with optimized performance (<500ms target).

        Args:
            booking_id: Associated booking ID
            user_id: Customer user ID
            vendor_id: Service vendor ID
            amount: Payment amount
            currency: Payment currency
            provider: Payment provider (auto-selected if None)
            payment_method_id: Optional payment method ID
            **kwargs: Additional payment fields

        Returns:
            Created payment instance

        Raises:
            ValidationError: If payment data is invalid
            ServiceError: If payment creation fails
        """
        correlation = self.log_operation_start(
            "create_payment",
            booking_id=booking_id,
            user_id=user_id,
            vendor_id=vendor_id,
            amount=float(amount)
        )

        try:
            # Auto-select provider if not specified
            if provider is None:
                provider = await self._select_optimal_provider(
                    user_id, currency, amount, crypto_currency, preferred_provider, geolocation_result
                )

            # Prepare payment data
            payment_data = {
                "booking_id": booking_id,
                "user_id": user_id,
                "vendor_id": vendor_id,
                "amount": amount,
                "currency": currency,
                "provider": provider,
                "payment_method_id": payment_method_id,
                **kwargs
            }

            # Create payment using repository
            async with self.get_transaction_context() as session:
                repository = PaymentRepository(session)
                payment = await repository.create_payment(**payment_data)

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Payment created with ID: {payment.id}, reference: {payment.transaction_reference}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "payment_created",
                    tags={"provider": provider.value, "currency": currency}
                )

                return payment

        except Exception as e:
            await self.handle_service_error(e, "create_payment", {
                "booking_id": booking_id,
                "user_id": user_id,
                "vendor_id": vendor_id,
                "amount": float(amount)
            })

    async def update_payment_status(
        self,
        payment_id: int,
        status: PaymentStatus,
        provider_reference: Optional[str] = None,
        failure_reason: Optional[str] = None,
        **kwargs
    ) -> Payment:
        """
        Update payment status with optimized performance (<100ms target).

        Args:
            payment_id: Payment ID to update
            status: New payment status
            provider_reference: Provider transaction reference
            failure_reason: Failure reason if status is failed
            **kwargs: Additional fields to update

        Returns:
            Updated payment instance

        Raises:
            NotFoundError: If payment not found
            ValidationError: If status transition is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_payment_status",
            payment_id=payment_id,
            status=status.value
        )

        try:
            # Validate status transition
            await self._validate_status_transition(payment_id, status)

            # Update payment using repository
            async with self.get_transaction_context() as session:
                repository = PaymentRepository(session)
                payment = await repository.update_payment_status(
                    payment_id=payment_id,
                    status=status,
                    provider_reference=provider_reference,
                    failure_reason=failure_reason,
                    **kwargs
                )

                if not payment:
                    raise NotFoundError("Payment", payment_id)

                # Log successful update
                self.log_operation_success(
                    correlation,
                    f"Payment {payment_id} status updated to {status.value}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "payment_status_updated",
                    tags={"status": status.value, "provider": payment.provider.value}
                )

                return payment

        except Exception as e:
            await self.handle_service_error(e, "update_payment_status", {
                "payment_id": payment_id,
                "status": status.value
            })

    async def get_user_payments(
        self,
        user_id: int,
        status: Optional[PaymentStatus] = None,
        provider: Optional[PaymentProviderType] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """
        Get user payments with filtering and pagination.

        Args:
            user_id: User ID to filter by
            status: Optional payment status filter
            provider: Optional payment provider filter
            page: Page number
            size: Page size

        Returns:
            Paginated payment results

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = PaymentRepository(session)

                from app.repositories.base import PaginationParams
                pagination = PaginationParams(page=page, size=size)

                result = await repository.get_payments_by_user(
                    user_id=user_id,
                    status=status,
                    provider=provider,
                    pagination=pagination
                )

                return {
                    "items": result.items,
                    "total": result.total,
                    "page": result.page,
                    "size": result.size,
                    "has_next": result.has_next,
                    "has_previous": result.has_previous
                }

        except Exception as e:
            await self.handle_service_error(e, "get_user_payments", {"user_id": user_id})

    async def get_payment_by_reference(self, transaction_reference: str) -> Optional[Payment]:
        """
        Get payment by transaction reference.

        Args:
            transaction_reference: Unique transaction reference

        Returns:
            Payment instance or None if not found

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = PaymentRepository(session)
                return await repository.get_payment_by_reference(transaction_reference)

        except Exception as e:
            await self.handle_service_error(e, "get_payment_by_reference", {
                "transaction_reference": transaction_reference
            })

    async def _select_optimal_provider(
        self,
        user_id: int,
        currency: str,
        amount: Decimal,
        crypto_currency: Optional[str] = None,
        preferred_provider: Optional[str] = None,
        geolocation_result: Optional[GeolocationResult] = None
    ) -> PaymentProviderType:
        """
        Select optimal payment provider based on enhanced 4-priority geolocation routing.

        Implements intelligent 4-priority routing system:
        Priority 1: Cryptocurrency → Busha (BTC, ETH, USDT, USDC)
        Priority 2: User Preference → Validated against geo-compatibility
        Priority 3: Geolocation-Based → IP detection with country routing
        Priority 4: Currency-Based Fallback → Traditional logic as safety net

        Geographic Intelligence:
        - African Countries (NG, GH, KE, etc.) → Paystack
        - Diaspora Countries (US, GB, CA, EU, etc.) → Stripe
        - Unknown/Other → Currency-based fallback

        Args:
            user_id: User ID for preferences and analytics
            currency: Payment currency (fallback selection criteria)
            amount: Payment amount (for provider fee optimization)
            crypto_currency: Cryptocurrency type (BTC, ETH, USDT, USDC) - triggers Busha
            preferred_provider: User's preferred provider (paystack, stripe, busha)
            geolocation_result: IP-based geolocation data for geographic routing

        Returns:
            Optimal payment provider for the transaction

        Raises:
            ValidationError: If no suitable provider found
        """
        correlation = correlation_id.get()

        self.logger.info(
            f"Selecting optimal payment provider (4-priority geolocation system)",
            extra={
                "correlation_id": correlation,
                "user_id": user_id,
                "currency": currency,
                "amount": float(amount),
                "crypto_currency": crypto_currency,
                "preferred_provider": preferred_provider,
                "country_code": geolocation_result.country_code if geolocation_result else None,
                "detection_method": geolocation_result.detection_method if geolocation_result else None
            }
        )

        # Priority 1: Cryptocurrency payments → Busha
        if crypto_currency:
            selected_provider = PaymentProviderType.BUSHA
            self.logger.info(
                f"Selected Busha for cryptocurrency payment",
                extra={
                    "correlation_id": correlation,
                    "provider": "busha",
                    "crypto_currency": crypto_currency,
                    "reason": "cryptocurrency_payment"
                }
            )

        # Priority 2: User preferred provider (if valid for currency)
        elif preferred_provider:
            if preferred_provider == "busha":
                selected_provider = PaymentProviderType.BUSHA
                reason = "user_preference_busha"
            elif preferred_provider == "stripe" and currency in ["USD", "EUR", "GBP"]:
                selected_provider = PaymentProviderType.STRIPE
                reason = "user_preference_stripe"
            elif preferred_provider == "paystack" and currency == "NGN":
                selected_provider = PaymentProviderType.PAYSTACK
                reason = "user_preference_paystack"
            else:
                # Invalid preference, fall back to currency-based selection
                selected_provider = self._get_currency_based_provider(currency)
                reason = "invalid_preference_fallback"

            self.logger.info(
                f"Selected provider based on user preference",
                extra={
                    "correlation_id": correlation,
                    "provider": selected_provider.value,
                    "preferred_provider": preferred_provider,
                    "reason": reason
                }
            )

        # Priority 3: Geolocation-based provider selection
        elif geolocation_result and geolocation_result.country_code:
            selected_provider = self._get_geolocation_based_provider(
                geolocation_result.country_code, currency
            )
            self.logger.info(
                f"Selected provider based on geolocation",
                extra={
                    "correlation_id": correlation,
                    "provider": selected_provider.value,
                    "country_code": geolocation_result.country_code,
                    "detection_method": geolocation_result.detection_method,
                    "confidence_score": geolocation_result.confidence_score,
                    "reason": "geolocation_based"
                }
            )

        # Priority 4: Currency-based provider selection (fallback)
        else:
            selected_provider = self._get_currency_based_provider(currency)
            self.logger.info(
                f"Selected provider based on currency fallback",
                extra={
                    "correlation_id": correlation,
                    "provider": selected_provider.value,
                    "currency": currency,
                    "reason": "currency_fallback"
                }
            )

        # Track provider selection metrics
        metrics_collector.record_business_event(
            f"payment_provider_selected_{selected_provider.value}_{currency}_{crypto_currency or 'fiat'}"
        )

        return selected_provider

    def _get_currency_based_provider(self, currency: str) -> PaymentProviderType:
        """
        Get provider based on currency (traditional routing logic).

        Args:
            currency: Payment currency

        Returns:
            PaymentProviderType based on currency
        """
        correlation = correlation_id.get()

        if currency == "NGN":
            # Nigerian Naira → Paystack (African markets)
            selected_provider = PaymentProviderType.PAYSTACK
            self.logger.info(
                f"Selected Paystack for NGN currency",
                extra={
                    "correlation_id": correlation,
                    "provider": "paystack",
                    "reason": "currency_ngn"
                }
            )

        elif currency in ["USD", "EUR", "GBP"]:
            # International currencies → Stripe (Diaspora markets)
            selected_provider = PaymentProviderType.STRIPE
            self.logger.info(
                f"Selected Stripe for international currency",
                extra={
                    "correlation_id": correlation,
                    "provider": "stripe",
                    "currency": currency,
                    "reason": "currency_international"
                }
            )

        else:
            # Fallback to Paystack for unsupported currencies
            selected_provider = PaymentProviderType.PAYSTACK
            self.logger.warning(
                f"Unsupported currency, falling back to Paystack",
                extra={
                    "correlation_id": correlation,
                    "currency": currency,
                    "provider": "paystack",
                    "reason": "currency_fallback"
                }
            )

        return selected_provider

    def _get_geolocation_based_provider(self, country_code: str, currency: str) -> PaymentProviderType:
        """
        Get provider based on geolocation with intelligent routing.

        Args:
            country_code: ISO 3166-1 alpha-2 country code
            currency: Payment currency for validation

        Returns:
            PaymentProviderType based on geographic location
        """
        correlation = correlation_id.get()

        # African countries → Paystack (optimized for African markets)
        african_countries = {
            "NG", "GH", "KE", "ZA", "EG", "MA", "TN", "UG", "TZ", "RW",
            "SN", "CI", "BF", "ML", "NE", "TD", "CM", "CF", "GA", "CG",
            "CD", "AO", "ZM", "ZW", "BW", "NA", "SZ", "LS", "MW", "MZ",
            "MG", "MU", "SC", "KM", "DJ", "SO", "ET", "ER", "SD", "SS"
        }

        # Diaspora countries → Stripe (optimized for international markets)
        diaspora_countries = {
            "US", "GB", "CA", "AU", "DE", "FR", "IT", "ES", "NL", "BE",
            "SE", "NO", "DK", "FI", "CH", "AT", "IE", "PT", "GR", "PL",
            "CZ", "HU", "SK", "SI", "HR", "BG", "RO", "LT", "LV", "EE"
        }

        if country_code in african_countries:
            # African markets → Paystack
            selected_provider = PaymentProviderType.PAYSTACK
            self.logger.info(
                f"Selected Paystack for African market",
                extra={
                    "correlation_id": correlation,
                    "provider": "paystack",
                    "country_code": country_code,
                    "reason": "african_market_geolocation"
                }
            )

        elif country_code in diaspora_countries:
            # Diaspora markets → Stripe
            selected_provider = PaymentProviderType.STRIPE
            self.logger.info(
                f"Selected Stripe for diaspora market",
                extra={
                    "correlation_id": correlation,
                    "provider": "stripe",
                    "country_code": country_code,
                    "reason": "diaspora_market_geolocation"
                }
            )

        else:
            # Unknown country → fallback to currency-based selection
            selected_provider = self._get_currency_based_provider(currency)
            self.logger.info(
                f"Unknown country, falling back to currency-based selection",
                extra={
                    "correlation_id": correlation,
                    "provider": selected_provider.value,
                    "country_code": country_code,
                    "currency": currency,
                    "reason": "unknown_country_fallback"
                }
            )

        return selected_provider

    async def _validate_status_transition(self, payment_id: int, new_status: PaymentStatus) -> None:
        """
        Validate payment status transition.

        Args:
            payment_id: Payment ID
            new_status: New payment status

        Raises:
            ValidationError: If transition is invalid
        """
        # Get current payment
        payment = await self.get_by_id(payment_id)
        if not payment:
            raise NotFoundError("Payment", payment_id)

        current_status = payment.status

        # Define valid transitions
        valid_transitions = {
            PaymentStatus.PENDING: [PaymentStatus.PROCESSING, PaymentStatus.CANCELLED, PaymentStatus.EXPIRED],
            PaymentStatus.PROCESSING: [PaymentStatus.COMPLETED, PaymentStatus.FAILED, PaymentStatus.CANCELLED],
            PaymentStatus.COMPLETED: [PaymentStatus.REFUNDED],
            PaymentStatus.FAILED: [PaymentStatus.PENDING],  # Allow retry
            PaymentStatus.CANCELLED: [],  # Terminal state
            PaymentStatus.EXPIRED: [],  # Terminal state
            PaymentStatus.REFUNDED: []  # Terminal state
        }

        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(
                f"Invalid status transition from {current_status.value} to {new_status.value}",
                field="status"
            )


class PaymentMethodService(BaseService[PaymentMethod, PaymentMethodRepository]):
    """
    Payment method service for user payment preferences and provider integration.

    Provides comprehensive payment method operations including:
    - Secure payment method creation with encrypted storage
    - Default payment method management
    - Payment method validation and expiry checking
    - Usage tracking and failure monitoring
    - Provider-specific payment method handling
    """

    def __init__(self, db: AsyncSession):
        """Initialize payment method service."""
        super().__init__(PaymentMethodRepository, PaymentMethod, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate payment method creation data.

        Args:
            data: Payment method creation data

        Returns:
            Validated payment method data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['user_id', 'method_type', 'provider', 'display_name']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate method type
        method_type = data.get('method_type')
        if not isinstance(method_type, PaymentMethodType):
            raise ValidationError("Invalid payment method type", field="method_type")

        # Validate provider
        provider = data.get('provider')
        if not isinstance(provider, PaymentProviderType):
            raise ValidationError("Invalid payment provider", field="provider")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate payment method update data.

        Args:
            data: Payment method update data
            existing_id: Existing payment method ID

        Returns:
            Validated payment method data

        Raises:
            ValidationError: If validation fails
        """
        # Prevent modification of critical fields
        protected_fields = ['user_id', 'method_type', 'provider']
        for field in protected_fields:
            if field in data:
                raise ValidationError(f"Cannot modify {field} after creation", field=field)

        return data

    async def create_payment_method(
        self,
        user_id: int,
        method_type: PaymentMethodType,
        provider: PaymentProviderType,
        display_name: str,
        encrypted_metadata: Optional[str] = None,
        **kwargs
    ) -> PaymentMethod:
        """
        Create a new payment method with security features.

        Args:
            user_id: User ID who owns the payment method
            method_type: Type of payment method
            provider: Payment provider
            display_name: User-friendly display name
            encrypted_metadata: Encrypted sensitive payment data
            **kwargs: Additional payment method fields

        Returns:
            Created payment method instance

        Raises:
            ValidationError: If payment method data is invalid
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start(
            "create_payment_method",
            user_id=user_id,
            method_type=method_type.value,
            provider=provider.value
        )

        try:
            # Create payment method using repository
            async with self.get_transaction_context() as session:
                repository = PaymentMethodRepository(session)
                payment_method = await repository.create_payment_method(
                    user_id=user_id,
                    method_type=method_type,
                    provider=provider,
                    display_name=display_name,
                    encrypted_metadata=encrypted_metadata,
                    **kwargs
                )

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Payment method created with ID: {payment_method.id}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "payment_method_created",
                    tags={"type": method_type.value, "provider": provider.value}
                )

                return payment_method

        except Exception as e:
            await self.handle_service_error(e, "create_payment_method", {
                "user_id": user_id,
                "method_type": method_type.value,
                "provider": provider.value
            })

    async def get_user_payment_methods(
        self,
        user_id: int,
        active_only: bool = True,
        provider: Optional[PaymentProviderType] = None
    ) -> List[PaymentMethod]:
        """
        Get user's payment methods with filtering.

        Args:
            user_id: User ID to filter by
            active_only: Whether to return only active payment methods
            provider: Optional provider filter

        Returns:
            List of payment methods

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = PaymentMethodRepository(session)
                return await repository.get_user_payment_methods(
                    user_id=user_id,
                    active_only=active_only,
                    provider=provider
                )

        except Exception as e:
            await self.handle_service_error(e, "get_user_payment_methods", {"user_id": user_id})

    async def set_default_payment_method(
        self,
        user_id: int,
        payment_method_id: int
    ) -> PaymentMethod:
        """
        Set a payment method as default for a user.

        Args:
            user_id: User ID
            payment_method_id: Payment method ID to set as default

        Returns:
            Updated payment method

        Raises:
            NotFoundError: If payment method not found
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "set_default_payment_method",
            user_id=user_id,
            payment_method_id=payment_method_id
        )

        try:
            async with self.get_transaction_context() as session:
                repository = PaymentMethodRepository(session)
                payment_method = await repository.set_default_payment_method(
                    user_id=user_id,
                    payment_method_id=payment_method_id
                )

                if not payment_method:
                    raise NotFoundError("PaymentMethod", payment_method_id)

                # Log successful update
                self.log_operation_success(
                    correlation,
                    f"Payment method {payment_method_id} set as default for user {user_id}"
                )

                return payment_method

        except Exception as e:
            await self.handle_service_error(e, "set_default_payment_method", {
                "user_id": user_id,
                "payment_method_id": payment_method_id
            })

    async def update_usage_tracking(
        self,
        payment_method_id: int,
        success: bool = True
    ) -> PaymentMethod:
        """
        Update payment method usage tracking.

        Args:
            payment_method_id: Payment method ID
            success: Whether the usage was successful

        Returns:
            Updated payment method

        Raises:
            NotFoundError: If payment method not found
            ServiceError: If update fails
        """
        try:
            async with self.get_transaction_context() as session:
                repository = PaymentMethodRepository(session)
                payment_method = await repository.update_usage_tracking(
                    payment_method_id=payment_method_id,
                    success=success
                )

                if not payment_method:
                    raise NotFoundError("PaymentMethod", payment_method_id)

                return payment_method

        except Exception as e:
            await self.handle_service_error(e, "update_usage_tracking", {
                "payment_method_id": payment_method_id,
                "success": success
            })

    async def get_expired_payment_methods(self) -> List[PaymentMethod]:
        """
        Get expired payment methods for cleanup.

        Returns:
            List of expired payment methods

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = PaymentMethodRepository(session)
                return await repository.get_expired_payment_methods()

        except Exception as e:
            await self.handle_service_error(e, "get_expired_payment_methods", {})


