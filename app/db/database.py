"""
Database configuration and session management for Culture Connect Backend API.

This module provides SQLAlchemy database engine, session management, and
connection utilities with proper async support and connection pooling.
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.core.config import settings, get_database_config

# Configure logging
logger = logging.getLogger(__name__)

# Database metadata and base model
metadata = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s"
    }
)

Base = declarative_base(metadata=metadata)

# Database configuration with optimizations
db_config = get_database_config()

# Import optimized configuration
from app.db.config import get_optimized_engine_args, get_optimized_session_args

# Get optimized engine arguments
engine_args = get_optimized_engine_args()

# Async database engine
async_engine = create_async_engine(
    db_config["url"],
    **engine_args
)

# Sync database engine (for migrations and admin tasks)
sync_engine_args = engine_args.copy()
sync_engine = create_engine(
    settings.database_url_sync,
    **sync_engine_args
)

# Session makers with optimized configuration
base_async_session_args = {
    "bind": async_engine,
    "class_": AsyncSession,
}

base_sync_session_args = {
    "bind": sync_engine,
}

# Apply optimizations
optimized_async_args = get_optimized_session_args(base_async_session_args)
optimized_sync_args = get_optimized_session_args(base_sync_session_args)

AsyncSessionLocal = async_sessionmaker(**optimized_async_args)
SessionLocal = sessionmaker(**optimized_sync_args)


# Database event listeners for connection management
@event.listens_for(sync_engine, "connect")
def set_postgresql_settings(dbapi_connection, connection_record):
    """Set PostgreSQL connection settings for better performance."""
    if "postgresql" in settings.DATABASE_URL:
        with dbapi_connection.cursor() as cursor:
            # Set timezone to UTC
            cursor.execute("SET timezone TO 'UTC'")
            # Set statement timeout (30 seconds)
            cursor.execute("SET statement_timeout = '30s'")
            # Set lock timeout (10 seconds)
            cursor.execute("SET lock_timeout = '10s'")
            # Enable JIT compilation for complex queries
            cursor.execute("SET jit = on")
            # Set work memory for sorting and hashing
            cursor.execute("SET work_mem = '4MB'")
            # Set maintenance work memory
            cursor.execute("SET maintenance_work_mem = '64MB'")


@event.listens_for(sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log database connection checkout."""
    logger.debug("Database connection checked out")


@event.listens_for(sync_engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log database connection checkin."""
    logger.debug("Database connection checked in")


# Dependency for FastAPI
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get async database session.

    This function provides an async database session for FastAPI endpoints
    with proper session lifecycle management and error handling.

    Yields:
        AsyncSession: Database session for async operations

    Raises:
        Exception: Database connection or transaction errors
    """
    async with AsyncSessionLocal() as session:
        try:
            logger.debug("Creating new async database session")
            yield session
            await session.commit()
            logger.debug("Async database session committed successfully")
        except Exception as e:
            logger.error(f"Database session error: {str(e)}")
            await session.rollback()
            raise
        finally:
            await session.close()
            logger.debug("Async database session closed")


def get_sync_session() -> Session:
    """
    Get synchronous database session.

    This function provides a sync database session for non-async operations
    like migrations, admin tasks, and background jobs.

    Returns:
        Session: Database session for sync operations
    """
    return SessionLocal()


def get_engine():
    """
    Get synchronous database engine.

    Returns:
        Engine: Synchronous database engine
    """
    return sync_engine


def get_async_engine():
    """
    Get asynchronous database engine.

    Returns:
        AsyncEngine: Asynchronous database engine
    """
    return async_engine


@asynccontextmanager
async def get_async_session_context() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for async database session.

    This context manager provides an async database session with automatic
    commit/rollback handling and proper resource cleanup.

    Yields:
        AsyncSession: Database session for async operations

    Example:
        async with get_async_session_context() as session:
            result = await session.execute(select(User))
            users = result.scalars().all()
    """
    async with AsyncSessionLocal() as session:
        try:
            logger.debug("Creating async session context")
            yield session
            await session.commit()
            logger.debug("Async session context committed")
        except Exception as e:
            logger.error(f"Async session context error: {str(e)}")
            await session.rollback()
            raise
        finally:
            await session.close()
            logger.debug("Async session context closed")


# Database health check
async def check_database_health() -> bool:
    """
    Check database connectivity and health.

    This function performs a simple database health check by executing
    a basic query to verify connectivity and responsiveness.

    Returns:
        bool: True if database is healthy, False otherwise
    """
    try:
        from sqlalchemy import text

        async with get_async_session_context() as session:
            result = await session.execute(text("SELECT 1"))
            logger.info("Database health check passed")
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return False


# Database initialization
async def init_database():
    """
    Initialize database with tables and initial data.

    This function creates all database tables and performs any necessary
    initialization tasks for a fresh database setup.
    """
    try:
        logger.info("Initializing database...")

        # Import all models to ensure they are registered
        from app.models import user, vendor, service, booking, payment, promotional

        # Create all tables
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("Database initialized successfully")

    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise


# Database cleanup
async def close_database():
    """
    Close database connections and cleanup resources.

    This function properly closes all database connections and cleans up
    resources when the application shuts down.
    """
    try:
        logger.info("Closing database connections...")
        await async_engine.dispose()
        sync_engine.dispose()
        logger.info("Database connections closed successfully")
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")


# Transaction management utilities
class DatabaseTransaction:
    """
    Database transaction context manager for complex operations.

    This class provides a context manager for handling complex database
    transactions with proper rollback and error handling.
    """

    def __init__(self, session: AsyncSession):
        self.session = session
        self._transaction = None

    async def __aenter__(self):
        """Start database transaction."""
        self._transaction = await self.session.begin()
        logger.debug("Database transaction started")
        return self.session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """End database transaction with commit or rollback."""
        if exc_type is not None:
            await self._transaction.rollback()
            logger.error(f"Database transaction rolled back due to: {exc_val}")
        else:
            await self._transaction.commit()
            logger.debug("Database transaction committed")


# Database utilities
async def execute_raw_sql(query: str, params: Optional[dict] = None) -> any:
    """
    Execute raw SQL query with parameters.

    Args:
        query: SQL query string
        params: Query parameters dictionary

    Returns:
        Query result

    Warning:
        Use with caution. Prefer ORM queries for better security.
    """
    async with get_async_session_context() as session:
        result = await session.execute(query, params or {})
        return result


async def get_table_row_count(table_name: str) -> int:
    """
    Get row count for a specific table.

    Args:
        table_name: Name of the table

    Returns:
        int: Number of rows in the table
    """
    query = f"SELECT COUNT(*) FROM {table_name}"
    result = await execute_raw_sql(query)
    return result.scalar()


# Database migration utilities
def run_migrations():
    """
    Run database migrations using Alembic.

    This function executes pending database migrations to bring
    the database schema up to date.
    """
    try:
        from alembic.config import Config
        from alembic import command

        logger.info("Running database migrations...")

        alembic_cfg = Config("alembic.ini")
        command.upgrade(alembic_cfg, "head")

        logger.info("Database migrations completed successfully")

    except Exception as e:
        logger.error(f"Database migration failed: {str(e)}")
        raise


# Export commonly used items
__all__ = [
    "Base",
    "async_engine",
    "sync_engine",
    "AsyncSessionLocal",
    "SessionLocal",
    "get_async_session",
    "get_sync_session",
    "get_engine",
    "get_async_engine",
    "get_async_session_context",
    "check_database_health",
    "init_database",
    "close_database",
    "DatabaseTransaction",
    "execute_raw_sql",
    "get_table_row_count",
    "run_migrations",
]
