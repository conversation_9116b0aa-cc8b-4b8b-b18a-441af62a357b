"""
Synchronization background tasks for Culture Connect Backend API.

This module provides comprehensive sync task processing including:
- Data synchronization operations
- Conflict resolution processing
- Batch sync operations
- Sync verification and integrity checks
- Real-time sync coordination

Implements Task 6.2.1 requirements for sync task processing with
integration to existing sync services from Task 6.1.2.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from uuid import UUID

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="sync.process_data_sync", queue="sync")
def process_data_sync(self, sync_request: Dict[str, Any]):
    """
    Process data synchronization operation.
    
    Args:
        sync_request: Sync request data including entity_type, entity_id, data_payload
    """
    try:
        async def _process_sync():
            from app.services.sync_services import DataSyncService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                sync_service = DataSyncService(session)
                
                # Extract sync parameters
                entity_type = sync_request["entity_type"]
                entity_id = sync_request["entity_id"]
                data_payload = sync_request["data_payload"]
                priority = sync_request.get("priority", "standard")
                conflict_strategy = sync_request.get("conflict_strategy", "last_write_wins")
                
                # Process the sync operation
                result = await sync_service.initiate_sync(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    data_payload=data_payload,
                    priority=priority,
                    conflict_strategy=conflict_strategy,
                    user_id=sync_request.get("user_id")
                )
                
                logger.info(f"Data sync processed for {entity_type}:{entity_id}", extra={
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                    "sync_id": result.sync_id,
                    "priority": priority,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "sync_id": result.sync_id,
                    "status": result.status,
                    "conflicts_detected": result.conflicts_detected,
                    "processing_time_ms": result.processing_time_ms
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_sync())
            return {
                "status": "success",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process data sync: {str(e)}", extra={
            "sync_request": sync_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=30, max_retries=5)


@celery_app.task(bind=True, name="sync.resolve_sync_conflict", queue="sync")
def resolve_sync_conflict(self, conflict_id: str, resolution_data: Dict[str, Any]):
    """
    Resolve synchronization conflict.
    
    Args:
        conflict_id: Conflict identifier
        resolution_data: Resolution strategy and data
    """
    try:
        async def _resolve_conflict():
            from app.services.sync_services import ConflictResolutionService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                conflict_service = ConflictResolutionService(session)
                
                # Resolve the conflict
                result = await conflict_service.resolve_conflict(
                    conflict_id=UUID(conflict_id),
                    resolution_strategy=resolution_data["strategy"],
                    resolution_data=resolution_data.get("data"),
                    user_id=resolution_data.get("user_id")
                )
                
                logger.info(f"Sync conflict resolved: {conflict_id}", extra={
                    "conflict_id": conflict_id,
                    "strategy": resolution_data["strategy"],
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "conflict_id": conflict_id,
                    "resolution_strategy": result.resolution_strategy,
                    "resolution_timestamp": result.resolution_timestamp.isoformat(),
                    "sync_status": result.sync_status
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_resolve_conflict())
            return {
                "status": "success",
                "resolved_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to resolve sync conflict {conflict_id}: {str(e)}", extra={
            "conflict_id": conflict_id,
            "resolution_data": resolution_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="sync.batch_sync_operation", queue="sync")
def batch_sync_operation(self, batch_data: Dict[str, Any]):
    """
    Process batch synchronization operation.
    
    Args:
        batch_data: Batch sync data including sync_ids and processing options
    """
    try:
        async def _process_batch():
            from app.services.sync_services import SyncBatchService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                batch_service = SyncBatchService(session)
                
                # Extract batch parameters
                sync_ids = [UUID(sid) for sid in batch_data["sync_ids"]]
                priority = batch_data.get("priority", "standard")
                processing_options = batch_data.get("processing_options", {})
                
                # Create and process the batch
                batch = await batch_service.create_batch(
                    sync_ids=sync_ids,
                    priority=priority,
                    processing_options=processing_options,
                    user_id=batch_data.get("user_id")
                )
                
                # Process the batch
                result = await batch_service.process_batch(
                    batch_id=batch.batch_id,
                    user_id=batch_data.get("user_id")
                )
                
                logger.info(f"Batch sync operation processed: {batch.batch_id}", extra={
                    "batch_id": str(batch.batch_id),
                    "sync_count": len(sync_ids),
                    "priority": priority,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "batch_id": str(batch.batch_id),
                    "sync_count": len(sync_ids),
                    "status": result.status,
                    "progress": result.progress,
                    "processing_time_ms": result.processing_time_ms
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_batch())
            return {
                "status": "success",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process batch sync operation: {str(e)}", extra={
            "batch_data": batch_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=120, max_retries=3)


@celery_app.task(bind=True, name="sync.verify_sync_integrity", queue="sync")
def verify_sync_integrity(self, entity_type: Optional[str] = None):
    """
    Verify synchronization integrity across the system.
    
    Args:
        entity_type: Optional entity type to focus verification on
    """
    try:
        async def _verify_integrity():
            from app.services.sync_services import DataSyncService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                sync_service = DataSyncService(session)
                
                # Perform integrity verification
                verification_result = await sync_service.verify_sync_integrity(
                    entity_type=entity_type,
                    check_conflicts=True,
                    check_orphaned_records=True,
                    check_data_consistency=True
                )
                
                logger.info(f"Sync integrity verification completed", extra={
                    "entity_type": entity_type,
                    "issues_found": verification_result.get("issues_count", 0),
                    "correlation_id": correlation_id.get()
                })
                
                return verification_result
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_verify_integrity())
            return {
                "status": "success",
                "verified_at": datetime.now(timezone.utc).isoformat(),
                "entity_type": entity_type,
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to verify sync integrity: {str(e)}", extra={
            "entity_type": entity_type,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="sync.cleanup_completed_syncs", queue="cleanup")
def cleanup_completed_syncs(self, retention_days: int = 30):
    """
    Clean up completed sync operations older than retention period.
    
    Args:
        retention_days: Number of days to retain completed sync records
    """
    try:
        async def _cleanup_syncs():
            from app.services.sync_services import DataSyncService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                sync_service = DataSyncService(session)
                
                # Calculate cutoff date
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)
                
                # Perform cleanup
                cleanup_result = await sync_service.cleanup_completed_syncs(
                    cutoff_date=cutoff_date,
                    preserve_failed=True,  # Keep failed syncs for analysis
                    preserve_conflicts=True  # Keep conflict records
                )
                
                logger.info(f"Sync cleanup completed", extra={
                    "retention_days": retention_days,
                    "records_cleaned": cleanup_result.get("records_cleaned", 0),
                    "correlation_id": correlation_id.get()
                })
                
                return cleanup_result
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_cleanup_syncs())
            return {
                "status": "success",
                "cleaned_at": datetime.now(timezone.utc).isoformat(),
                "retention_days": retention_days,
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to cleanup completed syncs: {str(e)}", extra={
            "retention_days": retention_days,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=600, max_retries=2)


@celery_app.task(bind=True, name="sync.update_sync_metrics", queue="analytics")
def update_sync_metrics(self, time_period_hours: int = 24):
    """
    Update synchronization performance metrics.
    
    Args:
        time_period_hours: Time period for metrics calculation
    """
    try:
        async def _update_metrics():
            from app.services.sync_services import SyncMetricsService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                metrics_service = SyncMetricsService(session)
                
                # Calculate time period
                end_time = datetime.now(timezone.utc)
                start_time = end_time - timedelta(hours=time_period_hours)
                
                # Update metrics
                metrics_result = await metrics_service.calculate_performance_metrics(
                    start_time=start_time,
                    end_time=end_time,
                    include_trends=True,
                    include_entity_breakdown=True
                )
                
                logger.info(f"Sync metrics updated", extra={
                    "time_period_hours": time_period_hours,
                    "total_syncs": metrics_result.get("total_syncs", 0),
                    "success_rate": metrics_result.get("success_rate", 0),
                    "correlation_id": correlation_id.get()
                })
                
                return metrics_result
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_update_metrics())
            return {
                "status": "success",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "time_period_hours": time_period_hours,
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to update sync metrics: {str(e)}", extra={
            "time_period_hours": time_period_hours,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=180, max_retries=2)
