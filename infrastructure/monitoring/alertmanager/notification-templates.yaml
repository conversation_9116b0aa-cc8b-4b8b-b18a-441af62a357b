# Culture Connect Backend - Alertmanager Notification Templates
# Custom templates for alert notifications

apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-templates
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: templates
data:
  email.tmpl: |
    {{ define "email.culture_connect.subject" }}
    [{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] Culture Connect Alert - {{ .GroupLabels.alertname }}
    {{ end }}

    {{ define "email.culture_connect.html" }}
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Culture Connect Alert</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background-color: {{ if eq .Status "firing" }}#d32f2f{{ else }}#388e3c{{ end }}; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .alert { margin-bottom: 20px; padding: 15px; border-left: 4px solid {{ if eq .Status "firing" }}#d32f2f{{ else }}#388e3c{{ end }}; background-color: #f9f9f9; }
            .alert-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; color: #333; }
            .alert-description { margin-bottom: 10px; color: #666; }
            .alert-details { font-size: 12px; color: #888; }
            .footer { background-color: #f0f0f0; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .severity-critical { border-left-color: #d32f2f; }
            .severity-warning { border-left-color: #f57c00; }
            .severity-info { border-left-color: #1976d2; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{{ if eq .Status "firing" }}🚨 Alert Firing{{ else }}✅ Alert Resolved{{ end }}</h1>
                <p>Culture Connect Monitoring System</p>
            </div>
            <div class="content">
                <p><strong>Status:</strong> {{ .Status | toUpper }}</p>
                <p><strong>Environment:</strong> {{ .GroupLabels.environment | default "production" }}</p>
                <p><strong>Component:</strong> {{ .GroupLabels.component | default "unknown" }}</p>
                <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp | default now }}</p>
                
                {{ range .Alerts }}
                <div class="alert severity-{{ .Labels.severity }}">
                    <div class="alert-title">{{ .Annotations.summary }}</div>
                    <div class="alert-description">{{ .Annotations.description }}</div>
                    <div class="alert-details">
                        <strong>Severity:</strong> {{ .Labels.severity | toUpper }}<br>
                        <strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}<br>
                        {{ if .EndsAt }}<strong>Ended:</strong> {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}<br>{{ end }}
                        {{ if .Annotations.runbook_url }}
                        <a href="{{ .Annotations.runbook_url }}" class="button">View Runbook</a>
                        {{ end }}
                    </div>
                </div>
                {{ end }}
                
                <div style="margin-top: 20px;">
                    <a href="https://grafana.cultureconnect.ng" class="button">View Dashboards</a>
                    <a href="https://alertmanager.cultureconnect.ng" class="button">Manage Alerts</a>
                </div>
            </div>
            <div class="footer">
                <p>Culture Connect Backend Monitoring System</p>
                <p>This is an automated message from the Culture Connect monitoring infrastructure.</p>
            </div>
        </div>
    </body>
    </html>
    {{ end }}

  slack.tmpl: |
    {{ define "slack.culture_connect.title" }}
    {{ if eq .Status "firing" }}🚨{{ else }}✅{{ end }} {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}
    {{ end }}

    {{ define "slack.culture_connect.text" }}
    {{ if eq .Status "firing" }}
    *🚨 ALERT FIRING*
    {{ else }}
    *✅ ALERT RESOLVED*
    {{ end }}

    *Environment:* {{ .GroupLabels.environment | default "production" }}
    *Component:* {{ .GroupLabels.component | default "unknown" }}
    *Alerts:* {{ .Alerts | len }}

    {{ range .Alerts }}
    ---
    *{{ .Annotations.summary }}*
    {{ .Annotations.description }}

    *Severity:* {{ .Labels.severity | toUpper }}
    *Started:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
    {{ if .EndsAt }}*Ended:* {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
    {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
    {{ end }}

    <https://grafana.cultureconnect.ng|📊 View Dashboards> | <https://alertmanager.cultureconnect.ng|🔧 Manage Alerts>
    {{ end }}

    {{ define "slack.culture_connect.color" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.severity "critical" }}danger{{ else if eq .CommonLabels.severity "warning" }}warning{{ else }}good{{ end }}
    {{ else }}
      good
    {{ end }}
    {{ end }}

  pagerduty.tmpl: |
    {{ define "pagerduty.culture_connect.description" }}
    {{ .GroupLabels.alertname }}: {{ .CommonAnnotations.summary }}
    {{ end }}

    {{ define "pagerduty.culture_connect.details" }}
    {
      "environment": "{{ .GroupLabels.environment | default "production" }}",
      "component": "{{ .GroupLabels.component | default "unknown" }}",
      "severity": "{{ .CommonLabels.severity }}",
      "alert_count": {{ .Alerts | len }},
      "firing_alerts": [
        {{ range $i, $alert := .Alerts.Firing }}
        {{ if $i }},{{ end }}
        {
          "summary": "{{ $alert.Annotations.summary }}",
          "description": "{{ $alert.Annotations.description }}",
          "started_at": "{{ $alert.StartsAt.Format "2006-01-02T15:04:05Z07:00" }}",
          "runbook_url": "{{ $alert.Annotations.runbook_url }}"
        }
        {{ end }}
      ],
      "dashboards_url": "https://grafana.cultureconnect.ng",
      "alertmanager_url": "https://alertmanager.cultureconnect.ng"
    }
    {{ end }}

  teams.tmpl: |
    {{ define "teams.culture_connect.title" }}
    {{ if eq .Status "firing" }}🚨 Alert Firing{{ else }}✅ Alert Resolved{{ end }} - {{ .GroupLabels.alertname }}
    {{ end }}

    {{ define "teams.culture_connect.text" }}
    **Environment:** {{ .GroupLabels.environment | default "production" }}  
    **Component:** {{ .GroupLabels.component | default "unknown" }}  
    **Status:** {{ .Status | toUpper }}  
    **Alert Count:** {{ .Alerts | len }}

    {{ range .Alerts }}
    ---
    **{{ .Annotations.summary }}**  
    {{ .Annotations.description }}

    - **Severity:** {{ .Labels.severity | toUpper }}
    - **Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
    {{ if .EndsAt }}  - **Ended:** {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
    {{ if .Annotations.runbook_url }}  - **Runbook:** [View Runbook]({{ .Annotations.runbook_url }}){{ end }}
    {{ end }}

    [📊 View Dashboards](https://grafana.cultureconnect.ng) | [🔧 Manage Alerts](https://alertmanager.cultureconnect.ng)
    {{ end }}

    {{ define "teams.culture_connect.color" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.severity "critical" }}Attention{{ else if eq .CommonLabels.severity "warning" }}Warning{{ else }}Good{{ end }}
    {{ else }}
      Good
    {{ end }}
    {{ end }}

  webhook.tmpl: |
    {{ define "webhook.culture_connect.payload" }}
    {
      "version": "1.0",
      "status": "{{ .Status }}",
      "environment": "{{ .GroupLabels.environment | default "production" }}",
      "component": "{{ .GroupLabels.component | default "unknown" }}",
      "alert_name": "{{ .GroupLabels.alertname }}",
      "alert_count": {{ .Alerts | len }},
      "timestamp": "{{ now.Format "2006-01-02T15:04:05Z07:00" }}",
      "alerts": [
        {{ range $i, $alert := .Alerts }}
        {{ if $i }},{{ end }}
        {
          "status": "{{ $alert.Status }}",
          "summary": "{{ $alert.Annotations.summary }}",
          "description": "{{ $alert.Annotations.description }}",
          "severity": "{{ $alert.Labels.severity }}",
          "started_at": "{{ $alert.StartsAt.Format "2006-01-02T15:04:05Z07:00" }}",
          {{ if $alert.EndsAt }}"ended_at": "{{ $alert.EndsAt.Format "2006-01-02T15:04:05Z07:00" }}",{{ end }}
          "runbook_url": "{{ $alert.Annotations.runbook_url }}",
          "labels": {
            {{ range $key, $value := $alert.Labels }}
            "{{ $key }}": "{{ $value }}"{{ if ne $key (last $alert.Labels) }},{{ end }}
            {{ end }}
          }
        }
        {{ end }}
      ],
      "external_urls": {
        "grafana": "https://grafana.cultureconnect.ng",
        "alertmanager": "https://alertmanager.cultureconnect.ng"
      }
    }
    {{ end }}
