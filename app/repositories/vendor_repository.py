"""
Vendor repository for Culture Connect Backend API.

This module provides data access layer for vendor-related operations
including CRUD operations for vendors, profiles, and documents.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from app.models.vendor import (
    Vendor, VendorProfile, VendorDocument,
    VendorType, VerificationStatus, MarketplaceStatus,
    DocumentType, DocumentStatus
)
from app.repositories.base import BaseRepository


class VendorRepository(BaseRepository[Vendor]):
    """Repository for vendor operations."""

    def __init__(self, db: Session):
        """Initialize vendor repository."""
        super().__init__(Vendor, db)

    async def create_vendor(
        self,
        user_id: int,
        vendor_data: Dict[str, Any],
        profile_data: Optional[Dict[str, Any]] = None
    ) -> Vendor:
        """
        Create a new vendor with optional profile.

        Args:
            user_id: Associated user ID
            vendor_data: Vendor creation data
            profile_data: Optional profile creation data

        Returns:
            Created vendor instance

        Raises:
            IntegrityError: If vendor already exists for user
        """
        try:
            # Create vendor
            vendor = Vendor(user_id=user_id, **vendor_data)
            self.db.add(vendor)
            self.db.flush()  # Get vendor ID

            # Create profile if provided
            if profile_data:
                profile = VendorProfile(vendor_id=vendor.id, **profile_data)
                self.db.add(profile)

            self.db.commit()
            self.db.refresh(vendor)
            return vendor

        except IntegrityError as e:
            self.db.rollback()
            raise e

    async def get_by_user_id(self, user_id: int) -> Optional[Vendor]:
        """
        Get vendor by user ID.

        Args:
            user_id: User ID to search for

        Returns:
            Vendor instance if found, None otherwise
        """
        return self.db.query(Vendor).filter(Vendor.user_id == user_id).first()

    async def get_with_profile(self, vendor_id: int) -> Optional[Vendor]:
        """
        Get vendor with profile data.

        Args:
            vendor_id: Vendor ID

        Returns:
            Vendor instance with profile if found
        """
        return (
            self.db.query(Vendor)
            .options(selectinload(Vendor.profile))
            .filter(Vendor.id == vendor_id)
            .first()
        )

    async def get_with_documents(self, vendor_id: int) -> Optional[Vendor]:
        """
        Get vendor with documents.

        Args:
            vendor_id: Vendor ID

        Returns:
            Vendor instance with documents if found
        """
        return (
            self.db.query(Vendor)
            .options(selectinload(Vendor.documents))
            .filter(Vendor.id == vendor_id)
            .first()
        )

    async def get_with_full_data(self, vendor_id: int) -> Optional[Vendor]:
        """
        Get vendor with all related data.

        Args:
            vendor_id: Vendor ID

        Returns:
            Vendor instance with all related data if found
        """
        return (
            self.db.query(Vendor)
            .options(
                selectinload(Vendor.profile),
                selectinload(Vendor.documents),
                selectinload(Vendor.user)
            )
            .filter(Vendor.id == vendor_id)
            .first()
        )

    async def get_by_business_name(self, business_name: str) -> Optional[Vendor]:
        """
        Get vendor by business name.

        Args:
            business_name: Business name to search for

        Returns:
            Vendor instance if found
        """
        return (
            self.db.query(Vendor)
            .filter(func.lower(Vendor.business_name) == business_name.lower())
            .first()
        )

    async def get_by_verification_status(
        self,
        status: VerificationStatus,
        limit: int = 100,
        offset: int = 0
    ) -> List[Vendor]:
        """
        Get vendors by verification status.

        Args:
            status: Verification status to filter by
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor instances
        """
        return (
            self.db.query(Vendor)
            .filter(Vendor.verification_status == status)
            .order_by(desc(Vendor.created_at))
            .limit(limit)
            .offset(offset)
            .all()
        )

    async def get_by_marketplace_status(
        self,
        status: MarketplaceStatus,
        limit: int = 100,
        offset: int = 0
    ) -> List[Vendor]:
        """
        Get vendors by marketplace status.

        Args:
            status: Marketplace status to filter by
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor instances
        """
        return (
            self.db.query(Vendor)
            .filter(Vendor.marketplace_status == status)
            .order_by(desc(Vendor.last_activity_at))
            .limit(limit)
            .offset(offset)
            .all()
        )

    async def get_by_business_type(
        self,
        business_type: VendorType,
        limit: int = 100,
        offset: int = 0
    ) -> List[Vendor]:
        """
        Get vendors by business type.

        Args:
            business_type: Business type to filter by
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor instances
        """
        return (
            self.db.query(Vendor)
            .filter(Vendor.business_type == business_type)
            .order_by(desc(Vendor.marketplace_ranking))
            .limit(limit)
            .offset(offset)
            .all()
        )

    async def search_vendors(
        self,
        query: str,
        business_type: Optional[VendorType] = None,
        verification_status: Optional[VerificationStatus] = None,
        marketplace_status: Optional[MarketplaceStatus] = None,
        city: Optional[str] = None,
        state: Optional[str] = None,
        country: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Vendor]:
        """
        Search vendors with filters.

        Args:
            query: Search query for business name
            business_type: Optional business type filter
            verification_status: Optional verification status filter
            marketplace_status: Optional marketplace status filter
            city: Optional city filter
            state: Optional state filter
            country: Optional country filter
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor instances matching criteria
        """
        query_filter = self.db.query(Vendor).options(selectinload(Vendor.profile))

        # Text search on business name
        if query:
            query_filter = query_filter.filter(
                Vendor.business_name.ilike(f"%{query}%")
            )

        # Apply filters
        if business_type:
            query_filter = query_filter.filter(Vendor.business_type == business_type)

        if verification_status:
            query_filter = query_filter.filter(Vendor.verification_status == verification_status)

        if marketplace_status:
            query_filter = query_filter.filter(Vendor.marketplace_status == marketplace_status)

        # Location filters (requires join with profile)
        if city or state or country:
            query_filter = query_filter.join(VendorProfile)

            if city:
                query_filter = query_filter.filter(
                    func.lower(VendorProfile.city) == city.lower()
                )

            if state:
                query_filter = query_filter.filter(
                    func.lower(VendorProfile.state) == state.lower()
                )

            if country:
                query_filter = query_filter.filter(
                    func.lower(VendorProfile.country) == country.lower()
                )

        return (
            query_filter
            .order_by(desc(Vendor.marketplace_ranking), desc(Vendor.average_rating))
            .limit(limit)
            .offset(offset)
            .all()
        )

    async def update_verification_status(
        self,
        vendor_id: int,
        status: VerificationStatus,
        notes: Optional[str] = None,
        reviewed_by: Optional[int] = None
    ) -> Optional[Vendor]:
        """
        Update vendor verification status.

        Args:
            vendor_id: Vendor ID
            status: New verification status
            notes: Optional verification notes
            reviewed_by: Optional reviewer user ID

        Returns:
            Updated vendor instance if found
        """
        vendor = await self.get(vendor_id)
        if not vendor:
            return None

        vendor.verification_status = status
        if notes:
            vendor.verification_notes = notes

        if status == VerificationStatus.VERIFIED:
            from datetime import datetime
            vendor.verified_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(vendor)
        return vendor

    async def update_marketplace_status(
        self,
        vendor_id: int,
        status: MarketplaceStatus
    ) -> Optional[Vendor]:
        """
        Update vendor marketplace status.

        Args:
            vendor_id: Vendor ID
            status: New marketplace status

        Returns:
            Updated vendor instance if found
        """
        vendor = await self.get(vendor_id)
        if not vendor:
            return None

        vendor.marketplace_status = status
        self.db.commit()
        self.db.refresh(vendor)
        return vendor

    async def update_performance_metrics(
        self,
        vendor_id: int,
        metrics: Dict[str, Any]
    ) -> Optional[Vendor]:
        """
        Update vendor performance metrics.

        Args:
            vendor_id: Vendor ID
            metrics: Performance metrics to update

        Returns:
            Updated vendor instance if found
        """
        vendor = await self.get(vendor_id)
        if not vendor:
            return None

        # Update allowed performance metrics
        allowed_metrics = [
            'total_earnings', 'total_bookings', 'average_rating',
            'total_reviews', 'response_rate', 'response_time_hours',
            'seo_score', 'marketplace_ranking', 'listing_quality_score'
        ]

        for key, value in metrics.items():
            if key in allowed_metrics and hasattr(vendor, key):
                setattr(vendor, key, value)

        self.db.commit()
        self.db.refresh(vendor)
        return vendor

    async def get_top_performers(
        self,
        business_type: Optional[VendorType] = None,
        limit: int = 10
    ) -> List[Vendor]:
        """
        Get top performing vendors.

        Args:
            business_type: Optional business type filter
            limit: Maximum number of results

        Returns:
            List of top performing vendors
        """
        query_filter = (
            self.db.query(Vendor)
            .filter(
                and_(
                    Vendor.marketplace_status == MarketplaceStatus.ACTIVE,
                    Vendor.verification_status == VerificationStatus.VERIFIED
                )
            )
        )

        if business_type:
            query_filter = query_filter.filter(Vendor.business_type == business_type)

        return (
            query_filter
            .order_by(
                desc(Vendor.average_rating),
                desc(Vendor.total_bookings),
                asc(Vendor.response_time_hours)
            )
            .limit(limit)
            .all()
        )

    # Task 3.2.2: Marketplace optimization query methods
    async def get_vendor_optimization_summary(
        self,
        vendor_id: int,
        include_services: bool = True
    ) -> Dict[str, Any]:
        """Get comprehensive optimization summary for a vendor."""
        from app.models.marketplace_optimization import (
            SEOAnalysis, PerformanceMetrics, CompetitiveAnalysis,
            OptimizationRecommendation, MobileOptimization
        )

        vendor = await self.get(vendor_id)
        if not vendor:
            return {"error": "Vendor not found"}

        # Get latest optimization data
        latest_seo = (
            self.db.query(SEOAnalysis)
            .filter(SEOAnalysis.vendor_id == vendor_id)
            .order_by(desc(SEOAnalysis.analysis_date))
            .first()
        )

        latest_performance = (
            self.db.query(PerformanceMetrics)
            .filter(PerformanceMetrics.vendor_id == vendor_id)
            .order_by(desc(PerformanceMetrics.metric_date))
            .first()
        )

        latest_competitive = (
            self.db.query(CompetitiveAnalysis)
            .filter(CompetitiveAnalysis.vendor_id == vendor_id)
            .order_by(desc(CompetitiveAnalysis.analysis_date))
            .first()
        )

        latest_mobile = (
            self.db.query(MobileOptimization)
            .filter(MobileOptimization.vendor_id == vendor_id)
            .order_by(desc(MobileOptimization.analysis_date))
            .first()
        )

        # Get pending recommendations
        pending_recommendations = (
            self.db.query(OptimizationRecommendation)
            .filter(
                and_(
                    OptimizationRecommendation.vendor_id == vendor_id,
                    OptimizationRecommendation.status == "pending"
                )
            )
            .order_by(desc(OptimizationRecommendation.expected_impact_score))
            .limit(5)
            .all()
        )

        summary = {
            "vendor_id": vendor_id,
            "vendor_name": vendor.business_name,
            "optimization_scores": {
                "seo_score": float(latest_seo.overall_seo_score) if latest_seo else None,
                "mobile_score": float(latest_mobile.overall_mobile_score) if latest_mobile else None,
                "market_position": latest_competitive.market_position_rank if latest_competitive else None,
                "conversion_rate": float(latest_performance.conversion_rate) if latest_performance else None
            },
            "pending_recommendations_count": len(pending_recommendations),
            "high_priority_recommendations": [
                {
                    "id": rec.id,
                    "title": rec.title,
                    "type": rec.recommendation_type,
                    "priority": rec.priority,
                    "expected_impact": float(rec.expected_impact_score)
                }
                for rec in pending_recommendations
            ]
        }

        if include_services:
            from app.models.service import Service
            services_count = (
                self.db.query(func.count(Service.id))
                .filter(Service.vendor_id == vendor_id)
                .scalar()
            )
            summary["total_services"] = services_count

        return summary

    async def get_vendors_needing_optimization(
        self,
        seo_threshold: float = 70.0,
        mobile_threshold: float = 70.0,
        limit: int = 50
    ) -> List[Vendor]:
        """Get vendors that need optimization improvements."""
        from app.models.marketplace_optimization import SEOAnalysis, MobileOptimization

        # Subquery for latest SEO scores
        latest_seo = (
            self.db.query(
                SEOAnalysis.vendor_id,
                func.avg(SEOAnalysis.overall_seo_score).label('avg_seo_score')
            )
            .group_by(SEOAnalysis.vendor_id)
            .subquery()
        )

        # Subquery for latest mobile scores
        latest_mobile = (
            self.db.query(
                MobileOptimization.vendor_id,
                func.avg(MobileOptimization.overall_mobile_score).label('avg_mobile_score')
            )
            .group_by(MobileOptimization.vendor_id)
            .subquery()
        )

        query = (
            self.db.query(Vendor)
            .filter(Vendor.marketplace_status == MarketplaceStatus.ACTIVE)
            .outerjoin(latest_seo, Vendor.id == latest_seo.c.vendor_id)
            .outerjoin(latest_mobile, Vendor.id == latest_mobile.c.vendor_id)
            .filter(
                or_(
                    latest_seo.c.avg_seo_score < seo_threshold,
                    latest_mobile.c.avg_mobile_score < mobile_threshold,
                    latest_seo.c.avg_seo_score.is_(None),
                    latest_mobile.c.avg_mobile_score.is_(None)
                )
            )
            .order_by(desc(Vendor.total_bookings))
            .limit(limit)
        )

        return query.all()

    async def get_vendor_performance_trends(
        self,
        vendor_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get performance trends for a vendor."""
        from app.models.marketplace_optimization import PerformanceMetrics
        from datetime import datetime, timedelta

        cutoff_date = datetime.utcnow() - timedelta(days=days)

        metrics = (
            self.db.query(PerformanceMetrics)
            .filter(
                and_(
                    PerformanceMetrics.vendor_id == vendor_id,
                    PerformanceMetrics.metric_date >= cutoff_date
                )
            )
            .order_by(asc(PerformanceMetrics.metric_date))
            .all()
        )

        if not metrics:
            return {"error": "No performance data found"}

        # Calculate trends
        total_views = sum(m.listing_views for m in metrics)
        total_inquiries = sum(m.booking_inquiries for m in metrics)
        total_revenue = sum(float(m.revenue_generated) for m in metrics)
        avg_conversion = sum(float(m.conversion_rate) for m in metrics) / len(metrics)

        return {
            "vendor_id": vendor_id,
            "period": {
                "start_date": cutoff_date.date().isoformat(),
                "end_date": datetime.utcnow().date().isoformat(),
                "days": days
            },
            "totals": {
                "views": total_views,
                "inquiries": total_inquiries,
                "revenue": total_revenue
            },
            "averages": {
                "conversion_rate": round(avg_conversion, 2)
            },
            "daily_data": [
                {
                    "date": m.metric_date.isoformat(),
                    "views": m.listing_views,
                    "inquiries": m.booking_inquiries,
                    "revenue": float(m.revenue_generated),
                    "conversion_rate": float(m.conversion_rate)
                }
                for m in metrics
            ]
        }

    async def get_vendor_competitive_position(
        self,
        vendor_id: int,
        market_category: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get vendor's competitive position in the market."""
        from app.models.marketplace_optimization import CompetitiveAnalysis

        # Get vendor's latest competitive analyses
        query = (
            self.db.query(CompetitiveAnalysis)
            .filter(CompetitiveAnalysis.vendor_id == vendor_id)
        )

        if market_category:
            query = query.filter(CompetitiveAnalysis.market_category == market_category)

        analyses = query.order_by(desc(CompetitiveAnalysis.analysis_date)).limit(5).all()

        if not analyses:
            return {"error": "No competitive analysis data found"}

        latest = analyses[0]

        return {
            "vendor_id": vendor_id,
            "market_category": latest.market_category,
            "current_position": {
                "market_rank": latest.market_position_rank,
                "total_competitors": latest.total_competitors,
                "market_share": float(latest.market_share_percentage),
                "price_position": latest.price_position,
                "quality_score": float(latest.quality_score_vs_competitors),
                "visibility_score": float(latest.visibility_score)
            },
            "competitive_advantages": latest.unique_selling_points or [],
            "areas_for_improvement": latest.competitive_gaps or [],
            "market_opportunities": latest.market_opportunities or [],
            "strategic_recommendations": latest.competitive_recommendations or []
        }


class VendorProfileRepository(BaseRepository[VendorProfile]):
    """Repository for vendor profile operations."""

    def __init__(self, db: Session):
        """Initialize vendor profile repository."""
        super().__init__(VendorProfile, db)

    async def get_by_vendor_id(self, vendor_id: int) -> Optional[VendorProfile]:
        """
        Get vendor profile by vendor ID.

        Args:
            vendor_id: Vendor ID

        Returns:
            Vendor profile instance if found
        """
        return (
            self.db.query(VendorProfile)
            .filter(VendorProfile.vendor_id == vendor_id)
            .first()
        )

    async def create_for_vendor(
        self,
        vendor_id: int,
        profile_data: Dict[str, Any]
    ) -> VendorProfile:
        """
        Create profile for vendor.

        Args:
            vendor_id: Vendor ID
            profile_data: Profile creation data

        Returns:
            Created vendor profile instance
        """
        profile = VendorProfile(vendor_id=vendor_id, **profile_data)
        self.db.add(profile)
        self.db.commit()
        self.db.refresh(profile)
        return profile

    async def update_operating_hours(
        self,
        vendor_id: int,
        operating_hours: Dict[str, Any],
        timezone: Optional[str] = None
    ) -> Optional[VendorProfile]:
        """
        Update vendor operating hours.

        Args:
            vendor_id: Vendor ID
            operating_hours: Operating hours data
            timezone: Optional timezone override

        Returns:
            Updated vendor profile instance if found
        """
        profile = await self.get_by_vendor_id(vendor_id)
        if not profile:
            return None

        profile.operating_hours = operating_hours
        if timezone:
            profile.timezone = timezone

        self.db.commit()
        self.db.refresh(profile)
        return profile

    async def update_media_urls(
        self,
        vendor_id: int,
        logo_url: Optional[str] = None,
        cover_image_url: Optional[str] = None,
        gallery_images: Optional[List[str]] = None
    ) -> Optional[VendorProfile]:
        """
        Update vendor media URLs.

        Args:
            vendor_id: Vendor ID
            logo_url: Optional logo URL
            cover_image_url: Optional cover image URL
            gallery_images: Optional gallery image URLs

        Returns:
            Updated vendor profile instance if found
        """
        profile = await self.get_by_vendor_id(vendor_id)
        if not profile:
            return None

        if logo_url is not None:
            profile.logo_url = logo_url
        if cover_image_url is not None:
            profile.cover_image_url = cover_image_url
        if gallery_images is not None:
            profile.gallery_images = gallery_images

        self.db.commit()
        self.db.refresh(profile)
        return profile

    async def get_profiles_by_completion_status(
        self,
        min_completion_percentage: float = 0.0,
        max_completion_percentage: float = 100.0,
        limit: int = 100,
        offset: int = 0
    ) -> List[VendorProfile]:
        """
        Get vendor profiles by completion percentage range.

        Args:
            min_completion_percentage: Minimum completion percentage
            max_completion_percentage: Maximum completion percentage
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor profiles within completion range
        """
        # This would require a computed field or separate calculation
        # For now, return all profiles and filter in service layer
        return (
            self.db.query(VendorProfile)
            .join(Vendor)
            .limit(limit)
            .offset(offset)
            .all()
        )

    async def get_profiles_missing_fields(
        self,
        required_fields: List[str],
        limit: int = 100,
        offset: int = 0
    ) -> List[VendorProfile]:
        """
        Get vendor profiles missing specific required fields.

        Args:
            required_fields: List of required field names
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of vendor profiles missing required fields
        """
        query = self.db.query(VendorProfile).join(Vendor)

        # Build dynamic filter for missing fields
        for field in required_fields:
            if hasattr(VendorProfile, field):
                query = query.filter(getattr(VendorProfile, field).is_(None))
            elif hasattr(Vendor, field):
                query = query.filter(getattr(Vendor, field).is_(None))

        return query.limit(limit).offset(offset).all()

    async def get_profile_analytics(self, vendor_id: int) -> Dict[str, Any]:
        """
        Get profile analytics data.

        Args:
            vendor_id: Vendor ID

        Returns:
            Profile analytics data
        """
        profile = await self.get_by_vendor_id(vendor_id)
        if not profile:
            return {}

        # Calculate basic analytics
        analytics = {
            'profile_created_at': profile.created_at,
            'last_updated_at': profile.updated_at,
            'has_logo': bool(profile.logo_url),
            'has_cover_image': bool(profile.cover_image_url),
            'gallery_image_count': len(profile.gallery_images) if profile.gallery_images else 0,
            'has_operating_hours': bool(profile.operating_hours),
            'languages_count': len(profile.languages_spoken) if profile.languages_spoken else 0,
            'specializations_count': len(profile.specializations) if profile.specializations else 0,
            'has_social_media': bool(profile.social_media_links),
            'has_website': bool(profile.website_url),
            'has_business_license': bool(profile.business_license_number),
            'has_insurance': bool(profile.insurance_details),
        }

        return analytics


class VendorDocumentRepository(BaseRepository[VendorDocument]):
    """Repository for vendor document operations."""

    def __init__(self, db: Session):
        """Initialize vendor document repository."""
        super().__init__(VendorDocument, db)

    async def get_by_vendor_id(self, vendor_id: int) -> List[VendorDocument]:
        """
        Get all documents for a vendor.

        Args:
            vendor_id: Vendor ID

        Returns:
            List of vendor documents
        """
        return (
            self.db.query(VendorDocument)
            .filter(VendorDocument.vendor_id == vendor_id)
            .order_by(desc(VendorDocument.created_at))
            .all()
        )

    async def get_by_type(
        self,
        vendor_id: int,
        document_type: DocumentType
    ) -> List[VendorDocument]:
        """
        Get documents by type for a vendor.

        Args:
            vendor_id: Vendor ID
            document_type: Document type to filter by

        Returns:
            List of vendor documents of specified type
        """
        return (
            self.db.query(VendorDocument)
            .filter(
                and_(
                    VendorDocument.vendor_id == vendor_id,
                    VendorDocument.document_type == document_type
                )
            )
            .order_by(desc(VendorDocument.created_at))
            .all()
        )

    async def get_primary_document(
        self,
        vendor_id: int,
        document_type: DocumentType
    ) -> Optional[VendorDocument]:
        """
        Get primary document of specified type for vendor.

        Args:
            vendor_id: Vendor ID
            document_type: Document type

        Returns:
            Primary document if found
        """
        return (
            self.db.query(VendorDocument)
            .filter(
                and_(
                    VendorDocument.vendor_id == vendor_id,
                    VendorDocument.document_type == document_type,
                    VendorDocument.is_primary == True
                )
            )
            .first()
        )

    async def update_document_status(
        self,
        document_id: int,
        status: DocumentStatus,
        review_notes: Optional[str] = None,
        reviewed_by: Optional[int] = None
    ) -> Optional[VendorDocument]:
        """
        Update document verification status.

        Args:
            document_id: Document ID
            status: New document status
            review_notes: Optional review notes
            reviewed_by: Optional reviewer user ID

        Returns:
            Updated document instance if found
        """
        document = await self.get(document_id)
        if not document:
            return None

        document.status = status
        if review_notes:
            document.review_notes = review_notes
        if reviewed_by:
            document.reviewed_by = reviewed_by

        if status in [DocumentStatus.APPROVED, DocumentStatus.REJECTED]:
            from datetime import datetime
            document.reviewed_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(document)
        return document
