"""
Unit tests for AdvancedSchedulerService (Task 6.2.2 Phase 3.3).

Tests for advanced scheduler service including:
- Schedule lifecycle management (create, update, delete, activate, deactivate)
- Conflict detection and resolution with priority-based scheduling
- Real-time schedule execution using CronExpressionParser and ScheduleCalculator
- Integration with WorkflowOrchestrationService and TaskService
- Performance monitoring with metrics collection and alerting
- Circuit breaker patterns and comprehensive error handling

Performance Targets:
- Schedule creation: <500ms for complex schedules
- Schedule calculation: <100ms for next execution time
- Conflict resolution: <50ms for conflict detection
- Monitoring latency: <50ms for status updates
- Throughput: >2000 schedules/minute processing capacity
"""

import pytest
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from app.services.advanced_scheduler_service import (
    AdvancedSchedulerService, ScheduleConflictError
)
from app.schemas.workflow_schemas import (
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse,
    ScheduleConflictCheck, ScheduleConflictResult,
    ScheduleCalculation, ScheduleCalculationResult
)
from app.models.workflow_models import (
    JobSchedule, ScheduleType
)
from app.services.base import ValidationError, NotFoundError
from app.core.cron_parser import ParsedCronExpression, CronParseError


class TestAdvancedSchedulerService:
    """Test suite for AdvancedSchedulerService."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock()
        session.__aenter__ = AsyncMock(return_value=session)
        session.__aexit__ = AsyncMock(return_value=None)
        return session

    @pytest.fixture
    def mock_workflow_service(self):
        """Create mock workflow orchestration service."""
        service = AsyncMock()
        service.create_and_start_execution = AsyncMock()
        return service

    @pytest.fixture
    def mock_task_service(self):
        """Create mock task service."""
        service = AsyncMock()
        service.submit_task = AsyncMock()
        return service

    @pytest.fixture
    def scheduler_service(self, mock_db_session, mock_workflow_service, mock_task_service):
        """Create AdvancedSchedulerService instance with mocked dependencies."""
        with patch('app.services.advanced_scheduler_service.JobScheduleRepository'), \
             patch('app.services.advanced_scheduler_service.WorkflowRepository'), \
             patch('app.services.advanced_scheduler_service.WorkflowStepRepository'), \
             patch('app.services.advanced_scheduler_service.CronExpressionParser'), \
             patch('app.services.advanced_scheduler_service.ScheduleCalculator'):

            service = AdvancedSchedulerService(
                db_session=mock_db_session,
                workflow_service=mock_workflow_service,
                task_service=mock_task_service
            )

            # Mock the repositories
            service.schedule_repository = AsyncMock()
            service.workflow_repository = AsyncMock()
            service.step_repository = AsyncMock()

            # Mock the parsers
            service.cron_parser = MagicMock()
            service.schedule_calculator = MagicMock()

            return service

    @pytest.fixture
    def valid_schedule_data(self):
        """Valid schedule creation data."""
        return JobScheduleCreate(
            name="test-schedule",
            description="Test schedule for unit testing",
            cron_expression="0 9 * * 1-5",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            business_days_only=True,
            exclude_holidays=False,
            priority=5,
            is_active=True,
            max_concurrent_runs=1
        )

    @pytest.fixture
    def mock_job_schedule(self):
        """Mock JobSchedule model instance."""
        schedule = MagicMock(spec=JobSchedule)
        schedule.id = uuid4()
        schedule.name = "test-schedule"
        schedule.cron_expression = "0 9 * * 1-5"
        schedule.timezone = "UTC"
        schedule.is_active = True
        schedule.priority = 5
        schedule.business_days_only = True
        schedule.exclude_holidays = False
        schedule.holiday_calendar = None
        schedule.workflow_definition_id = None
        schedule.task_name = None
        schedule.task_configuration = {}
        schedule.task_queue = None
        schedule.run_count = 0
        schedule.next_run_at = datetime.now(timezone.utc) + timedelta(hours=1)
        schedule.last_run_at = None
        schedule.__dict__ = {
            'id': schedule.id,
            'name': schedule.name,
            'cron_expression': schedule.cron_expression,
            'timezone': schedule.timezone,
            'is_active': schedule.is_active,
            'priority': schedule.priority,
            'business_days_only': schedule.business_days_only,
            'exclude_holidays': schedule.exclude_holidays,
            'holiday_calendar': schedule.holiday_calendar
        }
        return schedule

    # Schedule Lifecycle Management Tests

    @pytest.mark.asyncio
    async def test_create_schedule_success(self, scheduler_service, valid_schedule_data, mock_job_schedule):
        """Test successful schedule creation."""
        # Arrange
        scheduler_service.schedule_repository.create_job_schedule = AsyncMock(return_value=mock_job_schedule)
        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(return_value=MagicMock())
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._calculate_next_execution = AsyncMock(return_value=datetime.now(timezone.utc))
        scheduler_service._start_schedule_monitoring = AsyncMock()

        start_time = time.time()

        # Act
        result = await scheduler_service.create_schedule(valid_schedule_data)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 500  # Performance target: <500ms
        assert isinstance(result, JobScheduleResponse)
        assert result.name == "test-schedule"
        scheduler_service.schedule_repository.create_job_schedule.assert_called_once()
        scheduler_service._start_schedule_monitoring.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_schedule_with_conflicts(self, scheduler_service, valid_schedule_data):
        """Test schedule creation with conflicts."""
        # Arrange
        conflicts = [{"schedule_id": uuid4(), "priority": 6}]
        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(return_value=MagicMock())
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=conflicts)

        # Act & Assert
        with pytest.raises(ScheduleConflictError) as exc_info:
            await scheduler_service.create_schedule(valid_schedule_data)

        assert "Schedule conflicts detected" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_schedule_invalid_cron(self, scheduler_service, valid_schedule_data):
        """Test schedule creation with invalid cron expression."""
        # Arrange
        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(side_effect=ValidationError("Invalid cron"))

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await scheduler_service.create_schedule(valid_schedule_data)

        assert "Invalid cron" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_schedule_success(self, scheduler_service, mock_job_schedule):
        """Test successful schedule update."""
        # Arrange
        schedule_id = mock_job_schedule.id
        update_data = JobScheduleUpdate(priority=7, is_active=False)

        scheduler_service.schedule_repository.get_job_schedule_by_id = AsyncMock(return_value=mock_job_schedule)
        scheduler_service.schedule_repository.update_job_schedule = AsyncMock(return_value=mock_job_schedule)
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._stop_schedule_monitoring = AsyncMock()

        start_time = time.time()

        # Act
        result = await scheduler_service.update_schedule(schedule_id, update_data)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 300  # Performance target: <300ms
        assert isinstance(result, JobScheduleResponse)
        scheduler_service.schedule_repository.update_job_schedule.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_schedule_not_found(self, scheduler_service):
        """Test schedule update with non-existent schedule."""
        # Arrange
        schedule_id = uuid4()
        update_data = JobScheduleUpdate(priority=7)
        scheduler_service.schedule_repository.get_job_schedule_by_id = AsyncMock(return_value=None)

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            await scheduler_service.update_schedule(schedule_id, update_data)

        assert f"Schedule not found: {schedule_id}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_schedule_success(self, scheduler_service):
        """Test successful schedule deletion."""
        # Arrange
        schedule_id = uuid4()
        scheduler_service.schedule_repository.delete_job_schedule = AsyncMock(return_value=True)
        scheduler_service._stop_schedule_monitoring = AsyncMock()

        start_time = time.time()

        # Act
        result = await scheduler_service.delete_schedule(schedule_id)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 200  # Performance target: <200ms
        assert result is True
        scheduler_service._stop_schedule_monitoring.assert_called_once_with(schedule_id)

    @pytest.mark.asyncio
    async def test_activate_schedule_success(self, scheduler_service, mock_job_schedule):
        """Test successful schedule activation."""
        # Arrange
        schedule_id = mock_job_schedule.id
        scheduler_service.update_schedule = AsyncMock(return_value=JobScheduleResponse.model_validate(mock_job_schedule))
        scheduler_service._start_schedule_monitoring = AsyncMock()

        start_time = time.time()

        # Act
        result = await scheduler_service.activate_schedule(schedule_id)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 100  # Performance target: <100ms
        assert isinstance(result, JobScheduleResponse)
        scheduler_service._start_schedule_monitoring.assert_called_once()

    @pytest.mark.asyncio
    async def test_deactivate_schedule_success(self, scheduler_service, mock_job_schedule):
        """Test successful schedule deactivation."""
        # Arrange
        schedule_id = mock_job_schedule.id
        scheduler_service.update_schedule = AsyncMock(return_value=JobScheduleResponse.model_validate(mock_job_schedule))
        scheduler_service._stop_schedule_monitoring = AsyncMock()

        start_time = time.time()

        # Act
        result = await scheduler_service.deactivate_schedule(schedule_id)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 100  # Performance target: <100ms
        assert isinstance(result, JobScheduleResponse)
        scheduler_service._stop_schedule_monitoring.assert_called_once()

    # Conflict Detection and Resolution Tests

    @pytest.mark.asyncio
    async def test_detect_schedule_conflicts_success(self, scheduler_service):
        """Test successful conflict detection."""
        # Arrange
        conflict_check = ScheduleConflictCheck(
            cron_expression="0 9 * * 1-5",
            timezone="UTC",
            max_concurrent_runs=1
        )

        conflicts = [
            {
                "schedule_id": uuid4(),
                "schedule_name": "existing-schedule",
                "conflict_type": "time_overlap",
                "priority": 6,
                "overlap_duration_minutes": 15
            }
        ]

        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=conflicts)
        scheduler_service._calculate_conflict_severity = MagicMock(return_value=7)
        scheduler_service._generate_conflict_resolutions = MagicMock(return_value=["Adjust timing"])

        start_time = time.time()

        # Act
        result = await scheduler_service.detect_schedule_conflicts(conflict_check)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 50  # Performance target: <50ms
        assert isinstance(result, ScheduleConflictResult)
        assert result.has_conflicts is True
        assert len(result.conflicting_schedules) == 1
        assert len(result.conflict_details) == 1
        assert result.conflict_details[0]["severity"] == 7

    @pytest.mark.asyncio
    async def test_detect_schedule_conflicts_no_conflicts(self, scheduler_service):
        """Test conflict detection with no conflicts."""
        # Arrange
        conflict_check = ScheduleConflictCheck(
            cron_expression="0 2 * * *",
            timezone="UTC",
            max_concurrent_runs=1
        )

        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._generate_conflict_resolutions = MagicMock(return_value=[])

        # Act
        result = await scheduler_service.detect_schedule_conflicts(conflict_check)

        # Assert
        assert isinstance(result, ScheduleConflictResult)
        assert result.has_conflicts is False
        assert len(result.conflicting_schedules) == 0
        assert len(result.conflict_details) == 0

    @pytest.mark.asyncio
    async def test_calculate_schedule_executions_success(self, scheduler_service):
        """Test successful schedule execution calculation."""
        # Arrange
        calculation_request = ScheduleCalculation(
            cron_expression="0 9 * * 1-5",
            max_results=5,
            timezone="UTC",
            business_days_only=True
        )

        mock_calc_result = MagicMock()
        mock_calc_result.next_executions = [
            MagicMock(
                execution_time=datetime.now(timezone.utc) + timedelta(days=i),
                timezone="UTC",
                is_business_day=True,
                is_holiday=False,
                conflict_detected=False,
                conflict_reason=None
            ) for i in range(5)
        ]
        mock_calc_result.total_calculated = 5
        mock_calc_result.business_days_filtered = 0
        mock_calc_result.holidays_filtered = 0
        mock_calc_result.conflicts_detected = 0

        scheduler_service.schedule_calculator.calculate_next_executions = MagicMock(return_value=mock_calc_result)

        start_time = time.time()

        # Act
        result = await scheduler_service.calculate_schedule_executions(calculation_request)

        # Assert
        execution_time = (time.time() - start_time) * 1000
        assert execution_time < 100  # Performance target: <100ms
        assert isinstance(result, ScheduleCalculationResult)
        assert len(result.next_run_times) == 5
        assert result.schedule_summary["total_calculated"] == 5

    # Helper Method Tests

    @pytest.mark.asyncio
    async def test_validate_schedule_data_success(self, scheduler_service, valid_schedule_data):
        """Test successful schedule data validation."""
        # Act
        result = await scheduler_service._validate_schedule_data(valid_schedule_data)

        # Assert
        assert isinstance(result, dict)
        assert result["name"] == "test-schedule"
        assert result["cron_expression"] == "0 9 * * 1-5"
        assert result["priority"] == 5

    @pytest.mark.asyncio
    async def test_validate_schedule_data_invalid_priority(self, scheduler_service):
        """Test schedule data validation with invalid priority."""
        # Arrange
        invalid_data = JobScheduleCreate(
            name="test-schedule",
            cron_expression="0 9 * * 1-5",
            schedule_type=ScheduleType.CRON,
            priority=15  # Invalid: > 10
        )

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await scheduler_service._validate_schedule_data(invalid_data)

        assert "Priority must be between 1 and 10" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_schedule_data_missing_cron(self, scheduler_service):
        """Test schedule data validation with missing cron expression."""
        # Arrange
        invalid_data = JobScheduleCreate(
            name="test-schedule",
            schedule_type=ScheduleType.CRON,
            # Missing cron_expression
        )

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await scheduler_service._validate_schedule_data(invalid_data)

        assert "Cron schedule type requires cron_expression" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_parse_and_validate_cron_success(self, scheduler_service):
        """Test successful cron expression parsing."""
        # Arrange
        cron_expression = "0 9 * * 1-5"
        mock_parsed = MagicMock(spec=ParsedCronExpression)
        scheduler_service.cron_parser.parse = MagicMock(return_value=mock_parsed)

        # Act
        result = await scheduler_service._parse_and_validate_cron(cron_expression)

        # Assert
        assert result == mock_parsed
        scheduler_service.cron_parser.parse.assert_called_once_with(
            cron_expression,
            timezone="UTC",
            business_days_only=False,
            exclude_holidays=False,
            holiday_calendar=None
        )

    @pytest.mark.asyncio
    async def test_parse_and_validate_cron_invalid(self, scheduler_service):
        """Test cron expression parsing with invalid expression."""
        # Arrange
        cron_expression = "invalid cron"
        scheduler_service.cron_parser.parse = MagicMock(side_effect=CronParseError("Invalid expression"))

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await scheduler_service._parse_and_validate_cron(cron_expression)

        assert "Invalid cron expression" in str(exc_info.value)

    def test_calculate_conflict_severity_high(self, scheduler_service):
        """Test conflict severity calculation for high severity conflicts."""
        # Arrange
        new_schedule = {"priority": 5, "workflow_definition_id": uuid4()}
        existing_conflict = {
            "priority": 8,
            "workflow_definition_id": new_schedule["workflow_definition_id"],
            "overlap_duration_minutes": 45
        }

        # Act
        severity = scheduler_service._calculate_conflict_severity(new_schedule, existing_conflict)

        # Assert
        assert severity >= 7  # High severity due to same workflow + long overlap + priority diff

    def test_calculate_conflict_severity_low(self, scheduler_service):
        """Test conflict severity calculation for low severity conflicts."""
        # Arrange
        new_schedule = {"priority": 5, "workflow_definition_id": uuid4()}
        existing_conflict = {
            "priority": 5,
            "workflow_definition_id": uuid4(),  # Different workflow
            "overlap_duration_minutes": 5
        }

        # Act
        severity = scheduler_service._calculate_conflict_severity(new_schedule, existing_conflict)

        # Assert
        assert severity <= 3  # Low severity due to different workflow + short overlap + same priority

    def test_generate_conflict_resolutions_high_severity(self, scheduler_service):
        """Test conflict resolution generation for high severity conflicts."""
        # Arrange
        conflicts = [
            {"severity": 8, "conflict_type": "time_overlap", "schedule_name": "workflow-schedule"}
        ]

        # Act
        suggestions = scheduler_service._generate_conflict_resolutions(conflicts)

        # Assert
        assert len(suggestions) > 0
        assert any("priority" in suggestion.lower() for suggestion in suggestions)
        assert any("execution time" in suggestion.lower() for suggestion in suggestions)

    def test_generate_conflict_resolutions_no_conflicts(self, scheduler_service):
        """Test conflict resolution generation with no conflicts."""
        # Arrange
        conflicts = []

        # Act
        suggestions = scheduler_service._generate_conflict_resolutions(conflicts)

        # Assert
        assert len(suggestions) == 0

    @pytest.mark.asyncio
    async def test_calculate_next_execution_cron(self, scheduler_service):
        """Test next execution calculation for cron schedule."""
        # Arrange
        schedule_data = {
            "cron_expression": "0 9 * * 1-5",
            "timezone": "UTC",
            "business_days_only": True
        }

        next_time = datetime.now(timezone.utc) + timedelta(hours=1)
        mock_calc_result = MagicMock()
        mock_calc_result.next_executions = [MagicMock(execution_time=next_time)]

        scheduler_service.schedule_calculator.calculate_next_executions = MagicMock(return_value=mock_calc_result)

        # Act
        result = await scheduler_service._calculate_next_execution(schedule_data)

        # Assert
        assert result == next_time

    @pytest.mark.asyncio
    async def test_calculate_next_execution_interval(self, scheduler_service):
        """Test next execution calculation for interval schedule."""
        # Arrange
        schedule_data = {"interval_seconds": 3600}  # 1 hour

        # Act
        result = await scheduler_service._calculate_next_execution(schedule_data)

        # Assert
        assert result is not None
        assert isinstance(result, datetime)
        # Should be approximately 1 hour from now
        time_diff = (result - datetime.now(timezone.utc)).total_seconds()
        assert 3590 <= time_diff <= 3610  # Allow 10 second tolerance

    # Monitoring and Execution Tests

    @pytest.mark.asyncio
    async def test_start_schedule_monitoring_success(self, scheduler_service):
        """Test successful schedule monitoring start."""
        # Arrange
        schedule_id = uuid4()
        correlation_id = "test-correlation-123"

        # Act
        await scheduler_service._start_schedule_monitoring(schedule_id, correlation_id)

        # Assert
        assert schedule_id in scheduler_service._execution_tasks
        assert schedule_id in scheduler_service._active_schedules
        assert scheduler_service._active_schedules[schedule_id]["correlation_id"] == correlation_id

    @pytest.mark.asyncio
    async def test_stop_schedule_monitoring_success(self, scheduler_service):
        """Test successful schedule monitoring stop."""
        # Arrange
        schedule_id = uuid4()
        mock_task = AsyncMock()
        scheduler_service._execution_tasks[schedule_id] = mock_task
        scheduler_service._active_schedules[schedule_id] = {"test": "data"}

        # Act
        await scheduler_service._stop_schedule_monitoring(schedule_id)

        # Assert
        mock_task.cancel.assert_called_once()
        assert schedule_id not in scheduler_service._execution_tasks
        assert schedule_id not in scheduler_service._active_schedules

    @pytest.mark.asyncio
    async def test_execute_schedule_workflow_success(self, scheduler_service, mock_job_schedule, mock_workflow_service):
        """Test successful workflow schedule execution."""
        # Arrange
        mock_job_schedule.workflow_definition_id = uuid4()
        mock_job_schedule.task_name = None
        correlation_id = "test-correlation-123"

        scheduler_service.workflow_service = mock_workflow_service
        scheduler_service.schedule_repository.update_job_schedule = AsyncMock()
        scheduler_service._calculate_next_execution = AsyncMock(return_value=datetime.now(timezone.utc) + timedelta(hours=1))

        # Act
        await scheduler_service._execute_schedule(mock_job_schedule, correlation_id)

        # Assert
        mock_workflow_service.create_and_start_execution.assert_called_once_with(
            mock_job_schedule.workflow_definition_id,
            correlation_id=correlation_id
        )
        scheduler_service.schedule_repository.update_job_schedule.assert_called_once()
        assert scheduler_service._execution_metrics["schedules_executed"] > 0

    @pytest.mark.asyncio
    async def test_execute_schedule_task_success(self, scheduler_service, mock_job_schedule, mock_task_service):
        """Test successful task schedule execution."""
        # Arrange
        mock_job_schedule.workflow_definition_id = None
        mock_job_schedule.task_name = "test_task"
        mock_job_schedule.task_configuration = {"param": "value"}
        mock_job_schedule.task_queue = "test_queue"
        correlation_id = "test-correlation-123"

        scheduler_service.task_service = mock_task_service
        scheduler_service.schedule_repository.update_job_schedule = AsyncMock()
        scheduler_service._calculate_next_execution = AsyncMock(return_value=datetime.now(timezone.utc) + timedelta(hours=1))

        # Act
        await scheduler_service._execute_schedule(mock_job_schedule, correlation_id)

        # Assert
        mock_task_service.submit_task.assert_called_once()
        scheduler_service.schedule_repository.update_job_schedule.assert_called_once()
        assert scheduler_service._execution_metrics["schedules_executed"] > 0

    @pytest.mark.asyncio
    async def test_execute_schedule_failure_handling(self, scheduler_service, mock_job_schedule):
        """Test schedule execution failure handling."""
        # Arrange
        correlation_id = "test-correlation-123"
        scheduler_service.workflow_service = None
        scheduler_service.task_service = None
        mock_job_schedule.workflow_definition_id = uuid4()
        mock_job_schedule.task_name = None

        # Act
        await scheduler_service._execute_schedule(mock_job_schedule, correlation_id)

        # Assert
        assert scheduler_service._execution_metrics["execution_failures"] == 0  # No service available, no failure recorded

    # Performance Tests

    @pytest.mark.asyncio
    async def test_performance_schedule_creation_under_500ms(self, scheduler_service, valid_schedule_data, mock_job_schedule):
        """Test that schedule creation meets <500ms performance target."""
        # Arrange
        scheduler_service.schedule_repository.create_job_schedule = AsyncMock(return_value=mock_job_schedule)
        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(return_value=MagicMock())
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._calculate_next_execution = AsyncMock(return_value=datetime.now(timezone.utc))
        scheduler_service._start_schedule_monitoring = AsyncMock()

        # Act
        start_time = time.time()
        await scheduler_service.create_schedule(valid_schedule_data)
        execution_time = (time.time() - start_time) * 1000

        # Assert
        assert execution_time < 500  # Performance target: <500ms

    @pytest.mark.asyncio
    async def test_performance_conflict_detection_under_50ms(self, scheduler_service):
        """Test that conflict detection meets <50ms performance target."""
        # Arrange
        conflict_check = ScheduleConflictCheck(
            cron_expression="0 9 * * 1-5",
            timezone="UTC",
            max_concurrent_runs=1
        )

        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._generate_conflict_resolutions = MagicMock(return_value=[])

        # Act
        start_time = time.time()
        await scheduler_service.detect_schedule_conflicts(conflict_check)
        execution_time = (time.time() - start_time) * 1000

        # Assert
        assert execution_time < 50  # Performance target: <50ms

    @pytest.mark.asyncio
    async def test_performance_schedule_calculation_under_100ms(self, scheduler_service):
        """Test that schedule calculation meets <100ms performance target."""
        # Arrange
        calculation_request = ScheduleCalculation(
            cron_expression="0 9 * * 1-5",
            max_results=10,
            timezone="UTC"
        )

        mock_calc_result = MagicMock()
        mock_calc_result.next_executions = [MagicMock() for _ in range(10)]
        mock_calc_result.total_calculated = 10
        mock_calc_result.business_days_filtered = 0
        mock_calc_result.holidays_filtered = 0
        mock_calc_result.conflicts_detected = 0

        scheduler_service.schedule_calculator.calculate_next_executions = MagicMock(return_value=mock_calc_result)

        # Act
        start_time = time.time()
        await scheduler_service.calculate_schedule_executions(calculation_request)
        execution_time = (time.time() - start_time) * 1000

        # Assert
        assert execution_time < 100  # Performance target: <100ms

    # Integration Tests

    @pytest.mark.asyncio
    async def test_integration_full_schedule_lifecycle(self, scheduler_service, valid_schedule_data, mock_job_schedule):
        """Test complete schedule lifecycle integration."""
        # Arrange
        scheduler_service.schedule_repository.create_job_schedule = AsyncMock(return_value=mock_job_schedule)
        scheduler_service.schedule_repository.get_job_schedule_by_id = AsyncMock(return_value=mock_job_schedule)
        scheduler_service.schedule_repository.update_job_schedule = AsyncMock(return_value=mock_job_schedule)
        scheduler_service.schedule_repository.delete_job_schedule = AsyncMock(return_value=True)

        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(return_value=MagicMock())
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[])
        scheduler_service._calculate_next_execution = AsyncMock(return_value=datetime.now(timezone.utc))
        scheduler_service._start_schedule_monitoring = AsyncMock()
        scheduler_service._stop_schedule_monitoring = AsyncMock()

        # Act & Assert - Create
        created_schedule = await scheduler_service.create_schedule(valid_schedule_data)
        assert isinstance(created_schedule, JobScheduleResponse)

        # Act & Assert - Update
        update_data = JobScheduleUpdate(priority=7)
        updated_schedule = await scheduler_service.update_schedule(mock_job_schedule.id, update_data)
        assert isinstance(updated_schedule, JobScheduleResponse)

        # Act & Assert - Deactivate
        deactivated_schedule = await scheduler_service.deactivate_schedule(mock_job_schedule.id)
        assert isinstance(deactivated_schedule, JobScheduleResponse)

        # Act & Assert - Delete
        deleted = await scheduler_service.delete_schedule(mock_job_schedule.id)
        assert deleted is True

    @pytest.mark.asyncio
    async def test_integration_error_handling_chain(self, scheduler_service, valid_schedule_data):
        """Test error handling throughout the service chain."""
        # Arrange - Setup various failure scenarios
        scheduler_service._validate_schedule_data = AsyncMock(side_effect=ValidationError("Validation failed"))

        # Act & Assert - Validation error propagation
        with pytest.raises(ValidationError):
            await scheduler_service.create_schedule(valid_schedule_data)

        # Arrange - Setup conflict error scenario
        scheduler_service._validate_schedule_data = AsyncMock(return_value=valid_schedule_data.model_dump())
        scheduler_service._parse_and_validate_cron = AsyncMock(return_value=MagicMock())
        scheduler_service._detect_schedule_conflicts = AsyncMock(return_value=[{"schedule_id": uuid4(), "priority": 8}])

        # Act & Assert - Conflict error propagation
        with pytest.raises(ScheduleConflictError):
            await scheduler_service.create_schedule(valid_schedule_data)

    def test_service_initialization(self, mock_db_session, mock_workflow_service, mock_task_service):
        """Test service initialization with all dependencies."""
        # Act
        with patch('app.services.advanced_scheduler_service.JobScheduleRepository'), \
             patch('app.services.advanced_scheduler_service.WorkflowRepository'), \
             patch('app.services.advanced_scheduler_service.WorkflowStepRepository'), \
             patch('app.services.advanced_scheduler_service.CronExpressionParser'), \
             patch('app.services.advanced_scheduler_service.ScheduleCalculator'):

            service = AdvancedSchedulerService(
                db_session=mock_db_session,
                workflow_service=mock_workflow_service,
                task_service=mock_task_service
            )

        # Assert
        assert service.service_name == "AdvancedSchedulerService"
        assert service.workflow_service == mock_workflow_service
        assert service.task_service == mock_task_service
        assert isinstance(service._execution_metrics, dict)
        assert isinstance(service._active_schedules, dict)
        assert isinstance(service._execution_tasks, dict)
        assert isinstance(service._conflict_cache, dict)
