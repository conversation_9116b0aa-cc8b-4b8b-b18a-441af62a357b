"""
Authentication API endpoints for Culture Connect Backend API.

This module provides REST API endpoints for user authentication, registration,
password management, and token operations.
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.auth_service import AuthService
from app.schemas.auth import (
    UserCreate, UserLogin, UserResponse, LoginResponse, TokenResponse,
    TokenRefresh, PasswordReset, PasswordResetRequest, PasswordChange,
    EmailVerification, MessageResponse, AuthStatus, UserPermissions
)
from app.core.security import UserRole, Permission

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])

# Security scheme
security = HTTPBearer()


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Register a new user account with email verification"
)
async def register_user(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Register a new user account.

    Args:
        user_data: User registration data
        request: HTTP request object
        db: Database session

    Returns:
        UserResponse: Created user information

    Raises:
        HTTPException: If registration fails
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.register_user(user_data)

        logger.info(f"User registered successfully: {user.email}")
        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User registration failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="User login",
    description="Authenticate user and return access tokens"
)
async def login_user(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> LoginResponse:
    """
    Authenticate user and return tokens.

    Args:
        login_data: User login credentials
        request: HTTP request object
        db: Database session

    Returns:
        LoginResponse: User data and authentication tokens

    Raises:
        HTTPException: If authentication fails
    """
    try:
        auth_service = AuthService(db)
        user, tokens = await auth_service.authenticate_user(login_data)

        logger.info(f"User logged in successfully: {user.email}")

        return LoginResponse(
            user=user,
            tokens=TokenResponse(
                access_token=tokens.access_token,
                refresh_token=tokens.refresh_token,
                token_type=tokens.token_type,
                expires_in=tokens.expires_in
            ),
            message="Login successful"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User login failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post(
    "/refresh",
    response_model=TokenResponse,
    summary="Refresh access token",
    description="Refresh access token using valid refresh token"
)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_async_session)
) -> TokenResponse:
    """
    Refresh access token.

    Args:
        token_data: Refresh token data
        db: Database session

    Returns:
        TokenResponse: New token pair

    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        auth_service = AuthService(db)
        tokens = await auth_service.refresh_token(token_data.refresh_token)

        logger.info("Token refreshed successfully")
        return TokenResponse(
            access_token=tokens.access_token,
            refresh_token=tokens.refresh_token,
            token_type=tokens.token_type,
            expires_in=tokens.expires_in
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user",
    description="Get current authenticated user information"
)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Get current authenticated user.

    Args:
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        UserResponse: Current user information

    Raises:
        HTTPException: If token is invalid or user not found
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_current_user(credentials)

        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get current user failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.get(
    "/status",
    response_model=AuthStatus,
    summary="Get authentication status",
    description="Check if user is authenticated and get basic info"
)
async def get_auth_status(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> AuthStatus:
    """
    Get authentication status.

    Args:
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        AuthStatus: Authentication status and user info
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_current_user(credentials)

        return AuthStatus(
            is_authenticated=True,
            user=user,
            permissions=auth_service._get_user_scopes(user.role)
        )

    except HTTPException:
        return AuthStatus(is_authenticated=False)
    except Exception:
        return AuthStatus(is_authenticated=False)


@router.get(
    "/permissions",
    response_model=UserPermissions,
    summary="Get user permissions",
    description="Get current user's role and permissions"
)
async def get_user_permissions(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> UserPermissions:
    """
    Get user permissions.

    Args:
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        UserPermissions: User role and permissions

    Raises:
        HTTPException: If token is invalid or user not found
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_current_user(credentials)

        from app.core.security import get_user_permissions
        permissions = get_user_permissions(user.role)

        return UserPermissions(
            permissions=permissions,
            role=user.role
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user permissions failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user permissions"
        )


@router.post(
    "/password/reset-request",
    response_model=MessageResponse,
    summary="Request password reset",
    description="Request password reset link via email"
)
async def request_password_reset(
    reset_request: PasswordResetRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Request password reset.

    Args:
        reset_request: Password reset request data
        request: HTTP request object
        db: Database session

    Returns:
        MessageResponse: Success message
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.request_password_reset(reset_request.email)

        logger.info(f"Password reset requested for: {reset_request.email}")

        return MessageResponse(
            message=result["message"],
            success=True
        )

    except Exception as e:
        logger.error(f"Password reset request failed: {str(e)}")
        # Always return success message for security
        return MessageResponse(
            message="If the email exists, a reset link has been sent",
            success=True
        )


@router.post(
    "/password/reset",
    response_model=MessageResponse,
    summary="Reset password",
    description="Reset password using reset token"
)
async def reset_password(
    reset_data: PasswordReset,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Reset password using token.

    Args:
        reset_data: Password reset data
        request: HTTP request object
        db: Database session

    Returns:
        MessageResponse: Success message

    Raises:
        HTTPException: If reset token is invalid or expired
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.reset_password(reset_data)

        logger.info("Password reset completed successfully")

        return MessageResponse(
            message=result["message"],
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post(
    "/password/change",
    response_model=MessageResponse,
    summary="Change password",
    description="Change password for authenticated user"
)
async def change_password(
    password_data: PasswordChange,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Change user password.

    Args:
        password_data: Password change data
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        MessageResponse: Success message

    Raises:
        HTTPException: If current password is incorrect or new password is invalid
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_current_user(credentials)

        result = await auth_service.change_password(user.id, password_data)

        logger.info(f"Password changed for user: {user.email}")

        return MessageResponse(
            message=result["message"],
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.post(
    "/verify-email",
    response_model=MessageResponse,
    summary="Verify email",
    description="Verify user email using verification token"
)
async def verify_email(
    verification_data: EmailVerification,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Verify user email.

    Args:
        verification_data: Email verification data
        request: HTTP request object
        db: Database session

    Returns:
        MessageResponse: Success message

    Raises:
        HTTPException: If verification token is invalid
    """
    try:
        # TODO: Implement email verification logic
        # This would involve checking the verification token and updating user status

        logger.info("Email verification completed successfully")

        return MessageResponse(
            message="Email verified successfully",
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )


@router.post(
    "/logout",
    response_model=MessageResponse,
    summary="User logout",
    description="Logout user and invalidate tokens"
)
async def logout_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Logout user.

    Args:
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        MessageResponse: Success message
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_current_user(credentials)

        # TODO: Implement token blacklisting/invalidation
        # This would involve adding tokens to a blacklist or removing from cache

        logger.info(f"User logged out successfully: {user.email}")

        return MessageResponse(
            message="Logout successful",
            success=True
        )

    except HTTPException:
        # Even if token is invalid, return success for logout
        return MessageResponse(
            message="Logout successful",
            success=True
        )
    except Exception as e:
        logger.error(f"Logout failed: {str(e)}")
        return MessageResponse(
            message="Logout successful",
            success=True
        )


# Dependency functions for role-based access control
async def get_current_active_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Dependency to get current active user.

    Args:
        credentials: HTTP authorization credentials
        db: Database session

    Returns:
        UserResponse: Current active user

    Raises:
        HTTPException: If user is not authenticated or inactive
    """
    auth_service = AuthService(db)
    user = await auth_service.get_current_user(credentials)

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )

    return user


async def get_current_admin_user(
    current_user: UserResponse = Depends(get_current_active_user)
) -> UserResponse:
    """
    Dependency to get current admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current admin user

    Raises:
        HTTPException: If user is not an admin
    """
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    return current_user


async def get_current_vendor_user(
    current_user: UserResponse = Depends(get_current_active_user)
) -> UserResponse:
    """
    Dependency to get current vendor user.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current vendor user

    Raises:
        HTTPException: If user is not a vendor
    """
    if current_user.role != UserRole.VENDOR:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vendor access required"
        )

    return current_user


def require_permission(permission: str):
    """
    Dependency factory for permission-based access control.

    Args:
        permission: Required permission

    Returns:
        Dependency function that checks permission
    """
    async def permission_dependency(
        current_user: UserResponse = Depends(get_current_active_user)
    ) -> UserResponse:
        auth_service = AuthService(None)  # No DB needed for permission check

        if not auth_service.check_permission(current_user.role, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )

        return current_user

    return permission_dependency


# Export commonly used dependencies
__all__ = [
    "router",
    "get_current_active_user",
    "get_current_admin_user",
    "get_current_vendor_user",
    "require_permission",
]
