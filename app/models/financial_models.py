"""
Financial models for Culture Connect Backend API.

This module defines comprehensive financial tracking and reconciliation models
for revenue management, analytics, and financial reporting.

Implements Phase 1 of Payment & Transaction Management System with:
- Revenue tracking and analytics with detailed breakdowns
- Financial reconciliation and audit trails
- Performance-optimized reporting queries
- Compliance and regulatory reporting features
- Integration with payment and payout systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Numeric, CheckConstraint, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from app.db.base import BaseModelWithAudit
from app.core.payment.config import PaymentProviderType


class RevenueCategory(str, Enum):
    """Revenue category enumeration."""
    SERVICE_BOOKING = "service_booking"
    PLATFORM_FEE = "platform_fee"
    PAYMENT_PROCESSING = "payment_processing"
    SUBSCRIPTION = "subscription"
    ADVERTISING = "advertising"
    OTHER = "other"


class ReconciliationStatus(str, Enum):
    """Reconciliation status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    DISCREPANCY = "discrepancy"
    MANUAL_REVIEW = "manual_review"


class RevenueRecord(BaseModelWithAudit):
    """
    Revenue record model for comprehensive financial tracking and analytics.

    This model tracks all revenue streams including service bookings,
    platform fees, and other income sources for detailed financial reporting.

    Features:
    - Comprehensive revenue tracking with detailed breakdowns
    - Multi-currency support with conversion tracking
    - Performance-optimized analytics queries
    - Integration with payment and booking systems
    - Compliance and regulatory reporting
    """

    __tablename__ = "revenue_records"

    # Core revenue information
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated booking ID (if applicable)"
    )

    payment_id = Column(
        Integer,
        ForeignKey("payments.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated payment ID (if applicable)"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated vendor ID (if applicable)"
    )

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated user ID (if applicable)"
    )

    # Revenue details
    category = Column(
        SQLEnum(RevenueCategory),
        nullable=False,
        index=True,
        doc="Revenue category"
    )

    gross_amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Gross revenue amount"
    )

    platform_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform commission fee"
    )

    processing_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Payment processing fee"
    )

    net_vendor_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Net amount for vendor"
    )

    net_platform_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Net amount for platform"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Revenue currency (ISO 4217)"
    )

    # Provider and method tracking
    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=True,
        index=True,
        doc="Payment provider used"
    )

    payment_method = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Payment method used"
    )

    # Timing information
    transaction_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Transaction date"
    )

    settlement_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Settlement date"
    )

    # Tax and compliance
    tax_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Tax amount (if applicable)"
    )

    tax_rate = Column(
        Numeric(5, 4),
        nullable=False,
        default=Decimal("0.0000"),
        doc="Tax rate applied"
    )

    # Currency conversion (if applicable)
    original_currency = Column(
        String(3),
        nullable=True,
        doc="Original currency before conversion"
    )

    original_amount = Column(
        Numeric(12, 2),
        nullable=True,
        doc="Original amount before conversion"
    )

    exchange_rate = Column(
        Numeric(10, 6),
        nullable=True,
        doc="Exchange rate used for conversion"
    )

    # Reference and tracking
    reference_id = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique revenue record reference"
    )

    external_reference = Column(
        String(255),
        nullable=True,
        index=True,
        doc="External system reference"
    )

    # Metadata and additional information
    revenue_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional revenue metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        doc="Associated booking"
    )

    payment = relationship(
        "Payment",
        doc="Associated payment"
    )

    vendor = relationship(
        "Vendor",
        doc="Associated vendor"
    )

    user = relationship(
        "User",
        foreign_keys=[user_id],
        doc="Associated user"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "gross_amount > 0",
            name="check_gross_amount_positive"
        ),
        CheckConstraint(
            "platform_fee >= 0",
            name="check_platform_fee_non_negative"
        ),
        CheckConstraint(
            "processing_fee >= 0",
            name="check_processing_fee_non_negative"
        ),
        CheckConstraint(
            "net_vendor_amount >= 0",
            name="check_net_vendor_amount_non_negative"
        ),
        CheckConstraint(
            "net_platform_amount >= 0",
            name="check_net_platform_amount_non_negative"
        ),
        CheckConstraint(
            "tax_amount >= 0",
            name="check_tax_amount_non_negative"
        ),
        CheckConstraint(
            "tax_rate >= 0.0 AND tax_rate <= 1.0",
            name="check_tax_rate_range"
        ),
        CheckConstraint(
            "exchange_rate IS NULL OR exchange_rate > 0",
            name="check_exchange_rate_positive"
        ),

        # Performance indexes
        Index("idx_revenue_category_date", "category", "transaction_date"),
        Index("idx_revenue_vendor_date", "vendor_id", "transaction_date"),
        Index("idx_revenue_user_date", "user_id", "transaction_date"),
        Index("idx_revenue_provider_date", "provider", "transaction_date"),
        Index("idx_revenue_currency_amount", "currency", "gross_amount"),
        Index("idx_revenue_settlement_date", "settlement_date", "category"),
        Index("idx_revenue_booking_payment", "booking_id", "payment_id"),
    )

    def __repr__(self) -> str:
        """String representation of the revenue record."""
        return f"<RevenueRecord(id={self.id}, ref='{self.reference_id}', category='{self.category}', amount={self.gross_amount})>"

    @property
    def total_fees(self) -> Decimal:
        """Calculate total fees."""
        return self.platform_fee + self.processing_fee + self.tax_amount

    @property
    def platform_revenue(self) -> Decimal:
        """Calculate platform revenue."""
        return self.platform_fee + self.processing_fee


class ReconciliationRecord(BaseModelWithAudit):
    """
    Reconciliation record model for financial audit and compliance.

    This model tracks financial reconciliation between internal records
    and external provider statements for audit and compliance purposes.

    Features:
    - Automated reconciliation with provider statements
    - Discrepancy detection and resolution tracking
    - Performance-optimized reconciliation queries
    - Compliance and audit trail management
    - Integration with payment and revenue systems
    """

    __tablename__ = "reconciliation_records"

    # Core reconciliation information
    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=False,
        index=True,
        doc="Payment provider being reconciled"
    )

    # Reconciliation period
    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Reconciliation period start date"
    )

    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Reconciliation period end date"
    )

    # Financial amounts
    expected_amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Expected amount from internal records"
    )

    actual_amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Actual amount from provider statement"
    )

    variance = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Variance between expected and actual"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Reconciliation currency (ISO 4217)"
    )

    # Status and tracking
    status = Column(
        SQLEnum(ReconciliationStatus),
        nullable=False,
        default=ReconciliationStatus.PENDING,
        index=True,
        doc="Reconciliation status"
    )

    # Transaction counts
    expected_transaction_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Expected number of transactions"
    )

    actual_transaction_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Actual number of transactions"
    )

    # Timing information
    reconciled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Reconciliation completion timestamp"
    )

    reconciled_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who performed reconciliation"
    )

    # Provider statement information
    provider_statement_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider statement ID"
    )

    provider_statement_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Provider statement date"
    )

    # Discrepancy tracking
    discrepancy_details = Column(
        JSON,
        nullable=True,
        doc="Detailed discrepancy information"
    )

    resolution_notes = Column(
        Text,
        nullable=True,
        doc="Resolution notes and actions taken"
    )

    # Reference and tracking
    reference_id = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique reconciliation reference"
    )

    # Metadata and additional information
    reconciliation_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional reconciliation metadata"
    )

    # Relationships
    reconciled_by_user = relationship(
        "User",
        foreign_keys=[reconciled_by],
        doc="User who performed reconciliation"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "expected_amount >= 0",
            name="check_expected_amount_non_negative"
        ),
        CheckConstraint(
            "actual_amount >= 0",
            name="check_actual_amount_non_negative"
        ),
        CheckConstraint(
            "expected_transaction_count >= 0",
            name="check_expected_count_non_negative"
        ),
        CheckConstraint(
            "actual_transaction_count >= 0",
            name="check_actual_count_non_negative"
        ),
        CheckConstraint(
            "period_start < period_end",
            name="check_period_valid"
        ),

        # Performance indexes
        Index("idx_reconciliation_provider_period", "provider", "period_start", "period_end"),
        Index("idx_reconciliation_status_date", "status", "reconciled_at"),
        Index("idx_reconciliation_provider_status", "provider", "status"),
        Index("idx_reconciliation_variance", "variance", "status"),
        Index("idx_reconciliation_statement", "provider_statement_id"),
    )

    def __repr__(self) -> str:
        """String representation of the reconciliation record."""
        return f"<ReconciliationRecord(id={self.id}, provider='{self.provider}', ref='{self.reference_id}', status='{self.status}')>"

    @property
    def variance_percentage(self) -> Decimal:
        """Calculate variance percentage."""
        if self.expected_amount == 0:
            return Decimal("0.00")
        return (self.variance / self.expected_amount) * 100

    @property
    def has_discrepancy(self) -> bool:
        """Check if there is a discrepancy."""
        return abs(self.variance) > Decimal("0.01")  # Allow for minor rounding differences

    @property
    def is_completed(self) -> bool:
        """Check if reconciliation is completed."""
        return self.status == ReconciliationStatus.COMPLETED
