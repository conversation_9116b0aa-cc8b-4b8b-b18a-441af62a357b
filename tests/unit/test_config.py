"""
Unit tests for configuration management.

This module tests the Settings class and configuration validation
to ensure proper environment variable handling and validation.
"""

import os
import pytest
from unittest.mock import patch
from pydantic import ValidationError

from app.core.config import Settings, get_settings


class TestSettings:
    """Test cases for Settings configuration class."""

    def test_default_settings(self):
        """Test default configuration values."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            settings = Settings()

            # Test default values
            assert settings.APP_NAME == "Culture Connect Backend API"
            assert settings.APP_VERSION == "1.0.0"
            assert settings.ENVIRONMENT == "development"
            assert settings.DEBUG is False
            assert settings.DATABASE_POOL_SIZE == 10
            assert settings.REDIS_URL == "redis://localhost:6379/0"
            assert settings.LOG_LEVEL == "INFO"
            assert settings.ENABLE_PROMOTIONAL_SYSTEM is True

    def test_environment_validation(self):
        """Test environment setting validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_ENVIRONMENT": "invalid_env",
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "ENVIRONMENT must be one of" in str(exc_info.value)

    def test_secret_key_validation(self):
        """Test secret key length validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "short",  # Too short
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "SECRET_KEY must be at least 32 characters long" in str(exc_info.value)

    def test_log_level_validation(self):
        """Test log level validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_LOG_LEVEL": "INVALID",
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "LOG_LEVEL must be one of" in str(exc_info.value)

    def test_smtp_port_validation(self):
        """Test SMTP port validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_SMTP_PORT": "99999",  # Invalid port
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "SMTP_PORT must be between 1 and 65535" in str(exc_info.value)

    def test_database_pool_validation(self):
        """Test database pool size validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_DATABASE_POOL_SIZE": "0",  # Invalid pool size
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "Value must be positive" in str(exc_info.value)

    def test_sentry_sample_rate_validation(self):
        """Test Sentry traces sample rate validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_SENTRY_TRACES_SAMPLE_RATE": "1.5",  # Invalid rate
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "SENTRY_TRACES_SAMPLE_RATE must be between 0.0 and 1.0" in str(exc_info.value)

    def test_bcrypt_rounds_validation(self):
        """Test bcrypt rounds validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_BCRYPT_ROUNDS": "50",  # Too high
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "BCRYPT_ROUNDS must be between 4 and 31" in str(exc_info.value)

    def test_email_validation(self):
        """Test email format validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_FROM_EMAIL": "invalid-email",  # Invalid email
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "Invalid email format" in str(exc_info.value)

    def test_url_validation(self):
        """Test URL format validation."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_FRONTEND_URL": "invalid-url",  # Invalid URL
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            assert "Invalid URL format" in str(exc_info.value)


class TestSettingsProperties:
    """Test cases for Settings properties and methods."""

    @pytest.fixture
    def valid_settings(self):
        """Create valid settings instance for testing."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            return Settings()

    def test_environment_properties(self, valid_settings):
        """Test environment detection properties."""
        # Test development environment
        valid_settings.ENVIRONMENT = "development"
        assert valid_settings.is_development is True
        assert valid_settings.is_staging is False
        assert valid_settings.is_production is False

        # Test staging environment
        valid_settings.ENVIRONMENT = "staging"
        assert valid_settings.is_development is False
        assert valid_settings.is_staging is True
        assert valid_settings.is_production is False

        # Test production environment
        valid_settings.ENVIRONMENT = "production"
        assert valid_settings.is_development is False
        assert valid_settings.is_staging is False
        assert valid_settings.is_production is True

    def test_docs_enabled_property(self, valid_settings):
        """Test docs enabled property."""
        # Docs enabled in development
        valid_settings.ENVIRONMENT = "development"
        assert valid_settings.docs_enabled is True

        # Docs enabled in staging
        valid_settings.ENVIRONMENT = "staging"
        assert valid_settings.docs_enabled is True

        # Docs disabled in production
        valid_settings.ENVIRONMENT = "production"
        assert valid_settings.docs_enabled is False

    def test_debug_enabled_property(self, valid_settings):
        """Test debug enabled property."""
        # Debug enabled in development
        valid_settings.ENVIRONMENT = "development"
        assert valid_settings.debug_enabled is True

        # Debug disabled in other environments
        valid_settings.ENVIRONMENT = "production"
        assert valid_settings.debug_enabled is False

    def test_database_url_sync_property(self, valid_settings):
        """Test synchronous database URL property."""
        valid_settings.DATABASE_URL = "postgresql+asyncpg://user:pass@host/db"
        assert valid_settings.database_url_sync == "******************************"


class TestSettingsMethods:
    """Test cases for Settings methods."""

    @pytest.fixture
    def valid_settings(self):
        """Create valid settings instance for testing."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            return Settings()

    def test_get_sentry_config_with_dsn(self, valid_settings):
        """Test Sentry configuration with DSN."""
        valid_settings.SENTRY_DSN = "https://<EMAIL>/123"
        valid_settings.SENTRY_ENVIRONMENT = "test"

        config = valid_settings.get_sentry_config()

        assert config["dsn"] == "https://<EMAIL>/123"
        assert config["environment"] == "test"
        assert config["traces_sample_rate"] == 0.1
        assert config["debug"] is True

    def test_get_sentry_config_without_dsn(self, valid_settings):
        """Test Sentry configuration without DSN."""
        valid_settings.SENTRY_DSN = None

        config = valid_settings.get_sentry_config()

        assert config == {}

    def test_get_email_config(self, valid_settings):
        """Test email configuration."""
        config = valid_settings.get_email_config()

        assert config["host"] == "localhost"
        assert config["port"] == 587
        assert config["use_tls"] is True
        assert config["from_email"] == "<EMAIL>"
        assert config["from_name"] == "Culture Connect"

    def test_validate_required_secrets_development(self, valid_settings):
        """Test secret validation in development."""
        valid_settings.ENVIRONMENT = "development"

        missing = valid_settings.validate_required_secrets()

        assert missing == []  # No validation in development

    def test_validate_required_secrets_production_valid(self, valid_settings):
        """Test secret validation in production with valid secrets."""
        valid_settings.ENVIRONMENT = "production"

        missing = valid_settings.validate_required_secrets()

        assert missing == []

    def test_validate_required_secrets_production_missing(self, valid_settings):
        """Test secret validation in production with missing secrets."""
        valid_settings.ENVIRONMENT = "production"
        valid_settings.SECRET_KEY = "your-secret-key"  # Invalid placeholder

        missing = valid_settings.validate_required_secrets()

        assert "SECRET_KEY" in missing


class TestGetSettings:
    """Test cases for get_settings function."""

    def test_get_settings_caching(self):
        """Test that get_settings returns cached instance."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            # Clear cache first
            get_settings.cache_clear()

            settings1 = get_settings()
            settings2 = get_settings()

            # Should return the same instance due to caching
            assert settings1 is settings2


class TestConfigurationHelpers:
    """Test cases for configuration helper functions."""

    @pytest.fixture
    def valid_settings(self):
        """Create valid settings instance for testing."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
        }, clear=True):
            return Settings()

    def test_cors_origins_parsing(self, valid_settings):
        """Test CORS origins parsing from string."""
        # Test string parsing
        valid_settings.BACKEND_CORS_ORIGINS = "http://localhost:3000,http://localhost:8080"
        origins = valid_settings.cors_origins_list
        assert len(origins) == 2
        assert "http://localhost:3000" in origins
        assert "http://localhost:8080" in origins

    def test_file_types_parsing(self, valid_settings):
        """Test allowed file types parsing."""
        # Should parse from default list
        file_types = valid_settings.ALLOWED_FILE_TYPES
        assert "image/jpeg" in file_types
        assert "image/png" in file_types
        assert "application/pdf" in file_types


class TestEnvironmentSpecificBehavior:
    """Test environment-specific configuration behavior."""

    def test_development_environment_config(self):
        """Test development environment specific settings."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_ENVIRONMENT": "development",
        }, clear=True):
            settings = Settings()

            assert settings.is_development is True
            assert settings.docs_enabled is True
            assert settings.debug_enabled is True

    def test_production_environment_config(self):
        """Test production environment specific settings."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "production-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://prod:prod@prod-db/prod_db",
            "CC_PAYSTACK_SECRET_KEY": "prod_paystack_key",
            "CC_STRIPE_SECRET_KEY": "prod_stripe_key",
            "CC_BUSHA_API_KEY": "prod_busha_key",
            "CC_AIML_API_KEY": "prod_aiml_key",
            "CC_ENVIRONMENT": "production",
        }, clear=True):
            settings = Settings()

            assert settings.is_production is True
            assert settings.docs_enabled is False
            assert settings.debug_enabled is False

    def test_staging_environment_config(self):
        """Test staging environment specific settings."""
        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "staging-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://staging:staging@staging-db/staging_db",
            "CC_PAYSTACK_SECRET_KEY": "staging_paystack_key",
            "CC_STRIPE_SECRET_KEY": "staging_stripe_key",
            "CC_BUSHA_API_KEY": "staging_busha_key",
            "CC_AIML_API_KEY": "staging_aiml_key",
            "CC_ENVIRONMENT": "staging",
        }, clear=True):
            settings = Settings()

            assert settings.is_staging is True
            assert settings.docs_enabled is True
            assert settings.debug_enabled is False


class TestConfigurationIntegration:
    """Integration tests for configuration with other components."""

    def test_database_config_generation(self):
        """Test database configuration generation."""
        from app.core.config import get_database_config

        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_DATABASE_POOL_SIZE": "15",
            "CC_DATABASE_MAX_OVERFLOW": "25",
        }, clear=True):
            config = get_database_config()

            assert config["url"] == "postgresql+asyncpg://test:test@localhost/test_db"
            assert config["pool_size"] == 15
            assert config["max_overflow"] == 25
            assert config["pool_pre_ping"] is True

    def test_redis_config_generation(self):
        """Test Redis configuration generation."""
        from app.core.config import get_redis_config

        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_REDIS_URL": "redis://localhost:6379/1",
            "CC_REDIS_PASSWORD": "test_password",
        }, clear=True):
            config = get_redis_config()

            assert config["url"] == "redis://localhost:6379/1"
            assert config["password"] == "test_password"
            assert config["decode_responses"] is True

    def test_celery_config_generation(self):
        """Test Celery configuration generation."""
        from app.core.config import get_celery_config

        with patch.dict(os.environ, {
            "CC_SECRET_KEY": "test-secret-key-at-least-32-characters-long",
            "CC_DATABASE_URL": "postgresql+asyncpg://test:test@localhost/test_db",
            "CC_PAYSTACK_SECRET_KEY": "test_paystack_key",
            "CC_STRIPE_SECRET_KEY": "test_stripe_key",
            "CC_BUSHA_API_KEY": "test_busha_key",
            "CC_AIML_API_KEY": "test_aiml_key",
            "CC_CELERY_BROKER_URL": "redis://localhost:6379/2",
            "CC_CELERY_RESULT_BACKEND": "redis://localhost:6379/2",
        }, clear=True):
            config = get_celery_config()

            assert config["broker_url"] == "redis://localhost:6379/2"
            assert config["result_backend"] == "redis://localhost:6379/2"
            assert config["task_serializer"] == "json"
