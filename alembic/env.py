"""
Alembic environment configuration for Culture Connect Backend API.

This module configures Alembic for database migrations with comprehensive
support for async operations, environment-specific configurations, and
proper model registration.
"""

import asyncio
import logging
import os
import sys
from logging.config import fileConfig
from pathlib import Path

from sqlalchemy import pool, create_engine
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from alembic import context

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the base model and configuration
from app.db.base import Base
from app.core.config import settings

# Import all models to ensure they are registered with SQLAlchemy
# Only import models that currently exist
from app.models.user import User, APIKey, UserSession
from app.models.vendor import Vendor, VendorProfile, VendorDocument

# TODO: Import additional models as they are implemented
# from app.models.service import Service, ServiceCategory, ServiceImage, ServiceAvailability
# from app.models.booking import Booking, BookingItem, BookingStatus, Review
# from app.models.payment import Payment, PaymentMethod, Transaction, Refund
# from app.models.promotional import Campaign, CampaignMetrics, Advertisement, PromotionalCode

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Configure logging for migrations
logger = logging.getLogger('alembic.env')

# Set the SQLAlchemy URL from settings
# Use sync URL for migrations
database_url = settings.database_url_sync
config.set_main_option("sqlalchemy.url", database_url)

logger.info(f"Using database URL: {database_url.split('@')[1] if '@' in database_url else 'configured'}")

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# Configure naming convention for constraints
target_metadata.naming_convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        include_schemas=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """
    Run migrations with the given connection.

    Args:
        connection: Database connection to use for migrations
    """
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
        include_schemas=True,
        # Include object name in migration operations
        include_object=include_object,
        # Custom naming convention for constraints
        render_as_batch=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def include_object(obj, name, type_, reflected, compare_to):
    """
    Filter objects to include in migrations.

    This function determines which database objects should be included
    in the migration process.

    Args:
        obj: The database object
        name: Name of the object
        type_: Type of the object (table, index, etc.)
        reflected: Whether the object was reflected from the database
        compare_to: The object being compared to

    Returns:
        bool: True if the object should be included, False otherwise
    """
    # Skip Alembic's version table
    if type_ == "table" and name == "alembic_version":
        return False

    # Skip temporary tables
    if type_ == "table" and name.startswith("tmp_"):
        return False

    # Skip test tables in production
    if type_ == "table" and name.startswith("test_") and not settings.is_development:
        return False

    # Include all other objects
    return True


async def run_async_migrations() -> None:
    """
    Run migrations in 'online' mode using async engine.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    # Create async engine configuration
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = settings.DATABASE_URL

    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """
    Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    # Check if we're running in async mode
    try:
        asyncio.run(run_async_migrations())
    except Exception as e:
        logging.error(f"Async migration failed: {e}")
        # Fallback to sync mode if async fails
        connectable = create_engine(
            settings.database_url_sync,
            poolclass=pool.NullPool,
        )

        with connectable.connect() as connection:
            do_run_migrations(connection)


# Determine migration mode and run appropriate function
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
