"""
Promotional system test fixtures and configuration.

This module provides comprehensive test fixtures for promotional system testing:
- Model factories for all promotional entities
- Mock services and dependencies
- Test data generators
- Performance testing utilities
- Security testing helpers

Implements Phase 5: Comprehensive Testing Infrastructure with >85% code coverage.
"""

import pytest
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from uuid import uuid4
from typing import Dict, Any, List
from unittest.mock import AsyncMock, MagicMock

from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend,
    CampaignStatus, CampaignType, CampaignObjective, BidStrategy,
    AdFormat, AdStatus, PlacementType, PlacementStatus, SpendCategory
)
from app.core.payment.config import PaymentProviderType


# ============================================================================
# PROMOTIONAL MODEL FACTORIES
# ============================================================================

@pytest.fixture
def campaign_factory():
    """Factory for creating campaign instances."""
    def _create_campaign(**kwargs):
        defaults = {
            "id": 1,
            "uuid": uuid4(),
            "vendor_id": 1,
            "name": "Summer Tourism Campaign 2025",
            "description": "Promote summer tourism packages to international visitors",
            "campaign_type": CampaignType.FEATURED_LISTING,
            "campaign_objective": CampaignObjective.BOOKING_CONVERSION,
            "status": CampaignStatus.DRAFT,
            "total_budget": Decimal("50000.00"),
            "daily_budget": Decimal("2000.00"),
            "spent_amount": Decimal("0.00"),
            "remaining_budget": Decimal("50000.00"),
            "payment_provider": PaymentProviderType.PAYSTACK,
            "bid_strategy": BidStrategy.MANUAL_CPC,
            "target_cpc": Decimal("25.00"),
            "start_date": date.today() + timedelta(days=7),
            "end_date": date.today() + timedelta(days=37),
            "timezone": "Africa/Lagos",
            "targeting_config": {
                "age_range": {"min": 25, "max": 55},
                "interests": ["travel", "culture", "food"]
            },
            "geographic_targeting": {
                "countries": ["NG", "US", "GB"],
                "states": ["Lagos", "Abuja"],
                "radius_km": 50
            },
            "demographic_targeting": {
                "gender": ["male", "female"],
                "income_level": ["middle", "high"]
            },
            "impressions": 0,
            "clicks": 0,
            "conversions": 0,
            "revenue_generated": Decimal("0.00"),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": 1,
            "updated_by": 1,
            "is_active": True
        }
        defaults.update(kwargs)
        return Campaign(**defaults)
    return _create_campaign


@pytest.fixture
def advertisement_factory():
    """Factory for creating advertisement instances."""
    def _create_advertisement(**kwargs):
        defaults = {
            "id": 1,
            "uuid": uuid4(),
            "campaign_id": 1,
            "name": "Summer Tourism Hero Ad",
            "description": "Compelling advertisement for summer tourism packages",
            "ad_format": AdFormat.IMAGE,
            "status": AdStatus.DRAFT,
            "headline": "Discover Nigeria Hidden Cultural Gems This Summer",
            "description_text": "Experience authentic Nigerian culture with our curated tourism packages",
            "call_to_action": "Book Now",
            "image_url": "https://cdn.cultureconnect.ng/ads/summer-tourism-hero.jpg",
            "target_url": "https://cultureconnect.ng/services/summer-tourism",
            "placement_preferences": {
                "preferred_placements": ["homepage_hero", "category_top"],
                "priority_level": 8
            },
            "impressions": 0,
            "clicks": 0,
            "conversions": 0,
            "spend_amount": Decimal("0.00"),
            "variant_group": "summer_tourism_2025",
            "variant_name": "hero_variant_a",
            "test_percentage": Decimal("50.00"),
            "start_date": datetime.now(timezone.utc) + timedelta(days=7),
            "end_date": datetime.now(timezone.utc) + timedelta(days=37),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": 1,
            "updated_by": 1,
            "is_active": True
        }
        defaults.update(kwargs)
        return Advertisement(**defaults)
    return _create_advertisement


@pytest.fixture
def campaign_metrics_factory():
    """Factory for creating campaign metrics instances."""
    def _create_metrics(**kwargs):
        defaults = {
            "id": 1,
            "uuid": uuid4(),
            "campaign_id": 1,
            "date": date.today(),
            "hour": 12,
            "impressions": 1000,
            "clicks": 50,
            "conversions": 5,
            "spend_amount": Decimal("250.00"),
            "revenue_generated": Decimal("2500.00"),
            "click_through_rate": Decimal("5.00"),
            "conversion_rate": Decimal("10.00"),
            "cost_per_click": Decimal("5.00"),
            "cost_per_acquisition": Decimal("50.00"),
            "return_on_ad_spend": Decimal("10.00"),
            "quality_score": Decimal("85.00"),
            "relevance_score": Decimal("90.00"),
            "landing_page_score": Decimal("80.00"),
            "unique_clicks": 45,
            "bounce_rate": Decimal("25.00"),
            "average_session_duration": Decimal("180.00"),
            "pages_per_session": Decimal("3.50"),
            "desktop_impressions": 600,
            "mobile_impressions": 350,
            "tablet_impressions": 50,
            "top_performing_locations": {
                "Lagos": {"impressions": 400, "clicks": 25, "conversions": 3},
                "Abuja": {"impressions": 300, "clicks": 15, "conversions": 1}
            },
            "optimization_score": Decimal("75.00"),
            "recommendations": {
                "budget_optimization": "Increase daily budget by 20%",
                "targeting_optimization": "Expand to Rivers State"
            },
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "is_active": True
        }
        defaults.update(kwargs)
        return CampaignMetrics(**defaults)
    return _create_metrics


@pytest.fixture
def promotional_listing_factory():
    """Factory for creating promotional listing instances."""
    def _create_listing(**kwargs):
        defaults = {
            "id": 1,
            "uuid": uuid4(),
            "advertisement_id": 1,
            "vendor_id": 1,
            "service_id": 1,
            "title": "Featured Summer Tourism Package",
            "description": "Premium placement for summer tourism services",
            "placement_type": PlacementType.HOMEPAGE_HERO,
            "placement_status": PlacementStatus.AVAILABLE,
            "priority_level": 8,
            "position_index": 1,
            "base_price": Decimal("5000.00"),
            "premium_multiplier": Decimal("1.50"),
            "final_price": Decimal("7500.00"),
            "billing_cycle": "daily",
            "start_date": datetime.now(timezone.utc) + timedelta(days=7),
            "end_date": datetime.now(timezone.utc) + timedelta(days=37),
            "timezone": "Africa/Lagos",
            "impressions": 0,
            "clicks": 0,
            "conversions": 0,
            "revenue_generated": Decimal("0.00"),
            "targeting_config": {
                "device_types": ["mobile", "desktop"],
                "time_of_day": ["morning", "evening"]
            },
            "optimization_settings": {
                "auto_bidding": True,
                "performance_threshold": 5.0
            },
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": 1,
            "updated_by": 1,
            "is_active": True
        }
        defaults.update(kwargs)
        return PromotionalListing(**defaults)
    return _create_listing


@pytest.fixture
def ad_spend_factory():
    """Factory for creating ad spend instances."""
    def _create_spend(**kwargs):
        defaults = {
            "id": 1,
            "uuid": uuid4(),
            "campaign_id": 1,
            "advertisement_id": 1,
            "vendor_id": 1,
            "spend_category": SpendCategory.CAMPAIGN_BUDGET,
            "amount": Decimal("250.00"),
            "currency": "NGN",
            "description": "Daily campaign budget spend",
            "payment_provider": PaymentProviderType.PAYSTACK,
            "payment_reference": "CC-SPEND-2025-001234",
            "payment_status": "completed",
            "billing_period_start": datetime.now(timezone.utc),
            "billing_period_end": datetime.now(timezone.utc) + timedelta(days=1),
            "impressions_attributed": 1000,
            "clicks_attributed": 50,
            "conversions_attributed": 5,
            "revenue_attributed": Decimal("2500.00"),
            "platform_fee": Decimal("12.50"),
            "tax_amount": Decimal("18.75"),
            "total_amount": Decimal("281.25"),
            "processed_at": datetime.now(timezone.utc),
            "processed_by": 1,
            "reconciled": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "created_by": 1,
            "updated_by": 1,
            "is_active": True
        }
        defaults.update(kwargs)
        return AdSpend(**defaults)
    return _create_spend


# ============================================================================
# MOCK SERVICES AND DEPENDENCIES
# ============================================================================

@pytest.fixture
def mock_promotional_repository():
    """Mock promotional repository for service testing."""
    repository = AsyncMock()
    repository.create = AsyncMock()
    repository.get_by_id = AsyncMock()
    repository.get_by_uuid = AsyncMock()
    repository.update = AsyncMock()
    repository.delete = AsyncMock()
    repository.list = AsyncMock()
    repository.count = AsyncMock()
    return repository


@pytest.fixture
def mock_payment_service():
    """Mock payment service for promotional billing testing."""
    service = AsyncMock()
    service.process_payment = AsyncMock()
    service.create_payment_intent = AsyncMock()
    service.confirm_payment = AsyncMock()
    service.refund_payment = AsyncMock()
    return service


@pytest.fixture
def mock_rbac_service():
    """Mock RBAC service for permission testing."""
    service = AsyncMock()
    service.check_permission = AsyncMock(return_value=True)
    service.get_user_permissions = AsyncMock()
    service.has_role = AsyncMock(return_value=True)
    return service


# ============================================================================
# TEST DATA GENERATORS
# ============================================================================

@pytest.fixture
def sample_campaign_data():
    """Sample campaign creation data."""
    return {
        "name": "Test Campaign 2025",
        "description": "Test campaign for promotional system",
        "campaign_type": "featured_listing",
        "campaign_objective": "booking_conversion",
        "total_budget": 25000.00,
        "daily_budget": 1000.00,
        "payment_provider": "paystack",
        "bid_strategy": "manual_cpc",
        "target_cpc": 20.00,
        "start_date": "2025-02-15",
        "end_date": "2025-03-15",
        "geographic_targeting": {
            "countries": ["NG"],
            "states": ["Lagos"],
            "radius_km": 25
        }
    }


@pytest.fixture
def sample_advertisement_data():
    """Sample advertisement creation data."""
    return {
        "campaign_id": 1,
        "name": "Test Advertisement",
        "ad_format": "image",
        "headline": "Test Headline for Advertisement",
        "call_to_action": "Learn More",
        "target_url": "https://example.com/test",
        "placement_preferences": {
            "preferred_placements": ["homepage_hero"],
            "priority_level": 5
        }
    }


@pytest.fixture
def performance_test_data():
    """Performance testing data sets."""
    return {
        "bulk_campaigns": 100,
        "bulk_advertisements": 500,
        "bulk_metrics": 1000,
        "concurrent_users": 50,
        "query_timeout_ms": 200
    }
