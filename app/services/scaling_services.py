"""
Scaling services for Culture Connect Backend API.

This module provides comprehensive scaling and load balancing services:
- ScalingMetricsService: Real-time scaling metrics management
- AutoScalingService: Auto-scaling policy management and execution
- LoadBalancerService: Load balancer configuration and management
- ContainerOrchestrationService: Container orchestration and management
- ScalingEventService: Scaling event tracking and analysis

Implements Phase 7.3.3 requirements with production-grade service patterns,
comprehensive monitoring, and seamless integration with Phase 7.2 performance
monitoring and Phase 7.3.2 caching systems.
"""

import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, Optional, List, Tuple
from uuid import UUID

from app.models.scaling_models import (
    ScalingMetrics, AutoScalingPolicy, LoadBalancerConfig,
    ContainerMetrics, ScalingEvent, ScalingTriggerType,
    ScalingDirection, LoadBalancerStrategy, ContainerStatus,
    ScalingEventType
)
from app.repositories.scaling_repositories import (
    ScalingMetricsRepository, AutoScalingPolicyRepository,
    LoadBalancerConfigRepository, ContainerMetricsRepository,
    ScalingEventRepository
)
from app.schemas.scaling_schemas import (
    ScalingMetricsCreate, AutoScalingPolicyCreate, LoadBalancerConfigCreate,
    ContainerMetricsCreate, ScalingEventCreate, ScalingDashboardData,
    ScalingRecommendation
)
from app.services.base import BaseService
import logging
from app.core.circuit_breaker import BaseCircuitBreaker, CircuitBreakerConfig

logger = logging.getLogger(__name__)


class ScalingMetricsService(BaseService):
    """
    Service for real-time scaling metrics management.

    Provides business logic for scaling metrics collection, analysis,
    and decision-making with integration to Phase 7.2 monitoring.
    """

    def __init__(
        self,
        scaling_metrics_repo: ScalingMetricsRepository,
        circuit_breaker: Optional[BaseCircuitBreaker] = None
    ):
        super().__init__()
        self.scaling_metrics_repo = scaling_metrics_repo
        self.circuit_breaker = circuit_breaker or BaseCircuitBreaker(
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=Exception,
                name="scaling_metrics"
            )
        )

    async def record_metric(
        self,
        metric_data: ScalingMetricsCreate,
        correlation_id: Optional[str] = None
    ) -> ScalingMetrics:
        """
        Record a new scaling metric.

        Args:
            metric_data: Metric data to record
            correlation_id: Optional correlation ID for tracking

        Returns:
            Created scaling metric
        """
        try:
            self._log_operation_start("record_metric", correlation_id)

            # Validate metric data
            await self._validate_metric_data(metric_data)

            # Create metric record
            metric = await self.scaling_metrics_repo.create_metric(
                metric_name=metric_data.metric_name,
                metric_type=metric_data.metric_type,
                component=metric_data.component,
                current_value=metric_data.current_value,
                threshold_value=metric_data.threshold_value,
                target_value=metric_data.target_value,
                cpu_utilization=metric_data.cpu_utilization,
                memory_utilization=metric_data.memory_utilization,
                request_rate=metric_data.request_rate,
                response_time_ms=metric_data.response_time_ms,
                error_rate=metric_data.error_rate,
                tags=metric_data.tags,
                metadata=metric_data.metadata
            )

            self._log_operation_success("record_metric", correlation_id)
            return metric

        except Exception as e:
            self._log_operation_error("record_metric", str(e), correlation_id)
            raise

    async def get_component_metrics(
        self,
        component: str,
        metric_types: Optional[List[str]] = None,
        limit: int = 100,
        correlation_id: Optional[str] = None
    ) -> List[ScalingMetrics]:
        """
        Get latest metrics for a component.

        Args:
            component: Component name
            metric_types: Optional list of metric types to filter
            limit: Maximum number of metrics to return
            correlation_id: Optional correlation ID for tracking

        Returns:
            List of scaling metrics
        """
        try:
            self._log_operation_start("get_component_metrics", correlation_id)

            metrics = await self.scaling_metrics_repo.get_latest_metrics_by_component(
                component=component,
                metric_types=metric_types,
                limit=limit
            )

            self._log_operation_success("get_component_metrics", correlation_id)
            return metrics

        except Exception as e:
            self._log_operation_error("get_component_metrics", str(e), correlation_id)
            raise

    async def get_metrics_time_series(
        self,
        component: str,
        metric_name: str,
        hours: int = 24,
        interval_minutes: int = 5,
        correlation_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get time-series metrics data for analysis.

        Args:
            component: Component name
            metric_name: Metric name
            hours: Number of hours of data to retrieve
            interval_minutes: Aggregation interval in minutes
            correlation_id: Optional correlation ID for tracking

        Returns:
            List of time-series data points
        """
        try:
            self._log_operation_start("get_metrics_time_series", correlation_id)

            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=hours)

            time_series = await self.scaling_metrics_repo.get_metrics_time_series(
                component=component,
                metric_name=metric_name,
                start_time=start_time,
                end_time=end_time,
                interval_minutes=interval_minutes
            )

            self._log_operation_success("get_metrics_time_series", correlation_id)
            return time_series

        except Exception as e:
            self._log_operation_error("get_metrics_time_series", str(e), correlation_id)
            raise

    async def get_utilization_summary(
        self,
        components: Optional[List[str]] = None,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get current utilization summary for scaling decisions.

        Args:
            components: Optional list of components to include
            correlation_id: Optional correlation ID for tracking

        Returns:
            Dictionary of component utilization data
        """
        try:
            self._log_operation_start("get_utilization_summary", correlation_id)

            summary = await self.scaling_metrics_repo.get_current_utilization_summary(
                components=components
            )

            self._log_operation_success("get_utilization_summary", correlation_id)
            return summary

        except Exception as e:
            self._log_operation_error("get_utilization_summary", str(e), correlation_id)
            raise

    async def analyze_scaling_need(
        self,
        component: str,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze if scaling is needed for a component.

        Args:
            component: Component name
            correlation_id: Optional correlation ID for tracking

        Returns:
            Scaling analysis results
        """
        try:
            self._log_operation_start("analyze_scaling_need", correlation_id)

            # Get recent metrics
            metrics = await self.scaling_metrics_repo.get_latest_metrics_by_component(
                component=component,
                limit=10
            )

            if not metrics:
                return {
                    "component": component,
                    "scaling_needed": False,
                    "reason": "No metrics available",
                    "recommendation": "no_action"
                }

            # Analyze metrics for scaling indicators
            latest_metric = metrics[0]

            analysis = {
                "component": component,
                "current_cpu": float(latest_metric.cpu_utilization or 0),
                "current_memory": float(latest_metric.memory_utilization or 0),
                "current_response_time": latest_metric.response_time_ms or 0,
                "current_error_rate": float(latest_metric.error_rate or 0),
                "scaling_needed": False,
                "scaling_direction": ScalingDirection.NO_CHANGE,
                "reason": "",
                "recommendation": "no_action",
                "confidence": 0.0
            }

            # Check for scale-up conditions
            scale_up_indicators = []
            if analysis["current_cpu"] > 80:
                scale_up_indicators.append("High CPU utilization")
            if analysis["current_memory"] > 85:
                scale_up_indicators.append("High memory utilization")
            if analysis["current_response_time"] > 1000:
                scale_up_indicators.append("High response time")
            if analysis["current_error_rate"] > 0.05:
                scale_up_indicators.append("High error rate")

            # Check for scale-down conditions
            scale_down_indicators = []
            if analysis["current_cpu"] < 20 and analysis["current_memory"] < 30:
                scale_down_indicators.append("Low resource utilization")
            if analysis["current_response_time"] < 100:
                scale_down_indicators.append("Low response time")

            # Make scaling recommendation
            if scale_up_indicators:
                analysis.update({
                    "scaling_needed": True,
                    "scaling_direction": ScalingDirection.SCALE_UP,
                    "reason": "; ".join(scale_up_indicators),
                    "recommendation": "scale_up",
                    "confidence": min(len(scale_up_indicators) * 0.3, 1.0)
                })
            elif scale_down_indicators and len(scale_down_indicators) >= 2:
                analysis.update({
                    "scaling_needed": True,
                    "scaling_direction": ScalingDirection.SCALE_DOWN,
                    "reason": "; ".join(scale_down_indicators),
                    "recommendation": "scale_down",
                    "confidence": 0.6
                })

            self._log_operation_success("analyze_scaling_need", correlation_id)
            return analysis

        except Exception as e:
            self._log_operation_error("analyze_scaling_need", str(e), correlation_id)
            raise

    async def cleanup_old_metrics(
        self,
        retention_days: int = 30,
        correlation_id: Optional[str] = None
    ) -> int:
        """
        Clean up old metrics data.

        Args:
            retention_days: Number of days to retain metrics
            correlation_id: Optional correlation ID for tracking

        Returns:
            Number of deleted records
        """
        try:
            self._log_operation_start("cleanup_old_metrics", correlation_id)

            deleted_count = await self.scaling_metrics_repo.cleanup_old_metrics(
                retention_days=retention_days
            )

            self._log_operation_success("cleanup_old_metrics", correlation_id)
            return deleted_count

        except Exception as e:
            self._log_operation_error("cleanup_old_metrics", str(e), correlation_id)
            raise

    async def _validate_metric_data(self, metric_data: ScalingMetricsCreate):
        """Validate metric data before recording."""
        if not metric_data.metric_name or not metric_data.metric_name.strip():
            raise ValueError("Metric name is required")

        if not metric_data.component or not metric_data.component.strip():
            raise ValueError("Component is required")

        if metric_data.current_value < 0:
            raise ValueError("Current value must be non-negative")

        if metric_data.cpu_utilization is not None and (
            metric_data.cpu_utilization < 0 or metric_data.cpu_utilization > 100
        ):
            raise ValueError("CPU utilization must be between 0 and 100")

        if metric_data.memory_utilization is not None and (
            metric_data.memory_utilization < 0 or metric_data.memory_utilization > 100
        ):
            raise ValueError("Memory utilization must be between 0 and 100")

        if metric_data.error_rate is not None and (
            metric_data.error_rate < 0 or metric_data.error_rate > 1
        ):
            raise ValueError("Error rate must be between 0 and 1")


class AutoScalingService(BaseService):
    """
    Service for auto-scaling policy management and execution.

    Provides business logic for auto-scaling policies, scaling decisions,
    and integration with Kubernetes HPA and custom metrics.
    """

    def __init__(
        self,
        auto_scaling_repo: AutoScalingPolicyRepository,
        scaling_metrics_service: ScalingMetricsService,
        scaling_event_repo: ScalingEventRepository,
        circuit_breaker: Optional[BaseCircuitBreaker] = None
    ):
        super().__init__()
        self.auto_scaling_repo = auto_scaling_repo
        self.scaling_metrics_service = scaling_metrics_service
        self.scaling_event_repo = scaling_event_repo
        self.circuit_breaker = circuit_breaker or BaseCircuitBreaker(
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=Exception,
                name="auto_scaling"
            )
        )

    async def create_scaling_policy(
        self,
        policy_data: AutoScalingPolicyCreate,
        correlation_id: Optional[str] = None
    ) -> AutoScalingPolicy:
        """
        Create a new auto-scaling policy.

        Args:
            policy_data: Policy configuration data
            correlation_id: Optional correlation ID for tracking

        Returns:
            Created auto-scaling policy
        """
        try:
            self._log_operation_start("create_scaling_policy", correlation_id)

            # Validate policy data
            await self._validate_policy_data(policy_data)

            # Create policy
            policy = await self.auto_scaling_repo.create(policy_data.model_dump())

            # Log policy creation event
            await self.scaling_event_repo.create_scaling_event(
                event_type=ScalingEventType.POLICY_UPDATED,
                component=policy.component,
                trigger_type=ScalingTriggerType.CUSTOM_METRIC,
                policy_id=policy.id,
                metadata={"action": "policy_created", "policy_name": policy.name}
            )

            self._log_operation_success("create_scaling_policy", correlation_id)
            return policy

        except Exception as e:
            self._log_operation_error("create_scaling_policy", str(e), correlation_id)
            raise

    async def get_active_policies(
        self,
        correlation_id: Optional[str] = None
    ) -> List[AutoScalingPolicy]:
        """
        Get all active auto-scaling policies.

        Args:
            correlation_id: Optional correlation ID for tracking

        Returns:
            List of active auto-scaling policies
        """
        try:
            self._log_operation_start("get_active_policies", correlation_id)

            policies = await self.auto_scaling_repo.get_active_policies()

            self._log_operation_success("get_active_policies", correlation_id)
            return policies

        except Exception as e:
            self._log_operation_error("get_active_policies", str(e), correlation_id)
            raise

    async def evaluate_scaling_policies(
        self,
        correlation_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Evaluate all active scaling policies and make scaling decisions.

        Args:
            correlation_id: Optional correlation ID for tracking

        Returns:
            List of scaling decisions
        """
        try:
            self._log_operation_start("evaluate_scaling_policies", correlation_id)

            # Get all active policies
            policies = await self.auto_scaling_repo.get_active_policies()

            scaling_decisions = []

            for policy in policies:
                try:
                    # Check cooldown period
                    if await self._is_in_cooldown(policy):
                        continue

                    # Get current metrics for the component
                    utilization = await self.scaling_metrics_service.get_utilization_summary(
                        components=[policy.component]
                    )

                    if policy.component not in utilization:
                        continue

                    component_metrics = utilization[policy.component]

                    # Evaluate scaling decision
                    decision = await self._evaluate_policy(policy, component_metrics)

                    if decision["action"] != "no_action":
                        scaling_decisions.append(decision)

                        # Create scaling event
                        await self.scaling_event_repo.create_scaling_event(
                            event_type=ScalingEventType.SCALE_UP_TRIGGERED if decision["action"] == "scale_up" else ScalingEventType.SCALE_DOWN_TRIGGERED,
                            component=policy.component,
                            trigger_type=ScalingTriggerType.CPU_UTILIZATION if decision["trigger"] == "cpu" else ScalingTriggerType.MEMORY_UTILIZATION,
                            scaling_direction=ScalingDirection.SCALE_UP if decision["action"] == "scale_up" else ScalingDirection.SCALE_DOWN,
                            trigger_value=Decimal(str(decision["trigger_value"])),
                            threshold_value=Decimal(str(decision["threshold"])),
                            policy_id=policy.id,
                            metadata=decision
                        )

                        # Update policy last scaling event
                        await self.auto_scaling_repo.update_last_scaling_event(
                            policy.id,
                            datetime.now(timezone.utc)
                        )

                except Exception as e:
                    logger.error(f"Error evaluating policy {policy.name}: {str(e)}")
                    continue

            self._log_operation_success("evaluate_scaling_policies", correlation_id)
            return scaling_decisions

        except Exception as e:
            self._log_operation_error("evaluate_scaling_policies", str(e), correlation_id)
            raise

    async def execute_scaling_decision(
        self,
        decision: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a scaling decision.

        Args:
            decision: Scaling decision to execute
            correlation_id: Optional correlation ID for tracking

        Returns:
            Execution result
        """
        try:
            self._log_operation_start("execute_scaling_decision", correlation_id)

            # This would integrate with Kubernetes API or container orchestration
            # For now, we'll simulate the scaling operation

            component = decision["component"]
            action = decision["action"]
            current_replicas = decision.get("current_replicas", 1)

            if action == "scale_up":
                target_replicas = min(current_replicas + 1, decision.get("max_replicas", 10))
            elif action == "scale_down":
                target_replicas = max(current_replicas - 1, decision.get("min_replicas", 1))
            else:
                target_replicas = current_replicas

            # Simulate scaling operation
            await asyncio.sleep(0.1)  # Simulate API call delay

            result = {
                "component": component,
                "action": action,
                "previous_replicas": current_replicas,
                "target_replicas": target_replicas,
                "actual_replicas": target_replicas,  # In real implementation, this would come from K8s
                "success": True,
                "duration_seconds": 5,  # Simulated duration
                "timestamp": datetime.now(timezone.utc)
            }

            self._log_operation_success("execute_scaling_decision", correlation_id)
            return result

        except Exception as e:
            self._log_operation_error("execute_scaling_decision", str(e), correlation_id)
            raise

    async def _is_in_cooldown(self, policy: AutoScalingPolicy) -> bool:
        """Check if policy is in cooldown period."""
        if not policy.last_scaling_event:
            return False

        now = datetime.now(timezone.utc)
        time_since_last_event = (now - policy.last_scaling_event).total_seconds()

        # Use the longer cooldown period for safety
        cooldown_seconds = max(
            policy.scale_up_cooldown_seconds,
            policy.scale_down_cooldown_seconds
        )

        return time_since_last_event < cooldown_seconds

    async def _evaluate_policy(
        self,
        policy: AutoScalingPolicy,
        metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Evaluate a scaling policy against current metrics."""
        decision = {
            "policy_id": str(policy.id),
            "component": policy.component,
            "action": "no_action",
            "trigger": None,
            "trigger_value": 0,
            "threshold": 0,
            "current_replicas": 1,  # Would come from K8s in real implementation
            "min_replicas": policy.min_replicas,
            "max_replicas": policy.max_replicas,
            "confidence": 0.0
        }

        # Check CPU utilization
        if policy.target_cpu_utilization and metrics.get("cpu_utilization", 0) > 0:
            cpu_utilization = metrics["cpu_utilization"]

            if cpu_utilization > policy.scale_up_threshold:
                decision.update({
                    "action": "scale_up",
                    "trigger": "cpu",
                    "trigger_value": cpu_utilization,
                    "threshold": float(policy.scale_up_threshold),
                    "confidence": min((cpu_utilization - float(policy.scale_up_threshold)) / 20, 1.0)
                })
            elif cpu_utilization < policy.scale_down_threshold:
                decision.update({
                    "action": "scale_down",
                    "trigger": "cpu",
                    "trigger_value": cpu_utilization,
                    "threshold": float(policy.scale_down_threshold),
                    "confidence": min((float(policy.scale_down_threshold) - cpu_utilization) / 20, 1.0)
                })

        # Check memory utilization (if CPU didn't trigger)
        if (decision["action"] == "no_action" and
            policy.target_memory_utilization and
            metrics.get("memory_utilization", 0) > 0):

            memory_utilization = metrics["memory_utilization"]

            if memory_utilization > policy.scale_up_threshold:
                decision.update({
                    "action": "scale_up",
                    "trigger": "memory",
                    "trigger_value": memory_utilization,
                    "threshold": float(policy.scale_up_threshold),
                    "confidence": min((memory_utilization - float(policy.scale_up_threshold)) / 20, 1.0)
                })
            elif memory_utilization < policy.scale_down_threshold:
                decision.update({
                    "action": "scale_down",
                    "trigger": "memory",
                    "trigger_value": memory_utilization,
                    "threshold": float(policy.scale_down_threshold),
                    "confidence": min((float(policy.scale_down_threshold) - memory_utilization) / 20, 1.0)
                })

        return decision

    async def _validate_policy_data(self, policy_data: AutoScalingPolicyCreate):
        """Validate auto-scaling policy data."""
        if not policy_data.name or not policy_data.name.strip():
            raise ValueError("Policy name is required")

        if not policy_data.component or not policy_data.component.strip():
            raise ValueError("Component is required")

        if policy_data.min_replicas < 1:
            raise ValueError("Minimum replicas must be at least 1")

        if policy_data.max_replicas < policy_data.min_replicas:
            raise ValueError("Maximum replicas must be greater than or equal to minimum replicas")

        if policy_data.scale_up_threshold <= policy_data.scale_down_threshold:
            raise ValueError("Scale up threshold must be greater than scale down threshold")

        if policy_data.scale_up_cooldown_seconds < 60:
            raise ValueError("Scale up cooldown must be at least 60 seconds")

        if policy_data.scale_down_cooldown_seconds < 60:
            raise ValueError("Scale down cooldown must be at least 60 seconds")


class LoadBalancerService(BaseService):
    """
    Service for business-aware load balancing and traffic distribution.

    Provides intelligent traffic routing based on Culture Connect business logic,
    complementing Kubernetes Service mesh with application-specific policies.
    """

    def __init__(
        self,
        load_balancer_repo: LoadBalancerConfigRepository,
        scaling_metrics_service: ScalingMetricsService,
        circuit_breaker: Optional[BaseCircuitBreaker] = None
    ):
        super().__init__()
        self.load_balancer_repo = load_balancer_repo
        self.scaling_metrics_service = scaling_metrics_service
        self.circuit_breaker = circuit_breaker or BaseCircuitBreaker(
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=Exception,
                name="load_balancer"
            )
        )

    async def create_load_balancer_config(
        self,
        config_data: LoadBalancerConfigCreate,
        correlation_id: Optional[str] = None
    ) -> LoadBalancerConfig:
        """
        Create a new load balancer configuration.

        Args:
            config_data: Load balancer configuration data
            correlation_id: Optional correlation ID for tracking

        Returns:
            Created load balancer configuration
        """
        try:
            self._log_operation_start("create_load_balancer_config", correlation_id)

            # Validate configuration data
            await self._validate_config_data(config_data)

            # Create configuration
            config = await self.load_balancer_repo.create(config_data.model_dump())

            self._log_operation_success("create_load_balancer_config", correlation_id)
            return config

        except Exception as e:
            self._log_operation_error("create_load_balancer_config", str(e), correlation_id)
            raise

    async def get_optimal_routing_strategy(
        self,
        service_name: str,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Determine optimal routing strategy based on business metrics.

        Args:
            service_name: Service name for routing optimization
            correlation_id: Optional correlation ID for tracking

        Returns:
            Optimal routing strategy configuration
        """
        try:
            self._log_operation_start("get_optimal_routing_strategy", correlation_id)

            # Get current service metrics
            utilization = await self.scaling_metrics_service.get_utilization_summary(
                components=[service_name]
            )

            if service_name not in utilization:
                return await self._get_default_routing_strategy(service_name)

            metrics = utilization[service_name]

            # Business-aware routing logic
            strategy = await self._analyze_routing_requirements(service_name, metrics)

            self._log_operation_success("get_optimal_routing_strategy", correlation_id)
            return strategy

        except Exception as e:
            self._log_operation_error("get_optimal_routing_strategy", str(e), correlation_id)
            raise

    async def evaluate_health_check_policies(
        self,
        service_name: str,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate and optimize health check policies for a service.

        Args:
            service_name: Service name for health check evaluation
            correlation_id: Optional correlation ID for tracking

        Returns:
            Health check policy recommendations
        """
        try:
            self._log_operation_start("evaluate_health_check_policies", correlation_id)

            # Get current configuration
            config = await self.load_balancer_repo.get_config_by_service(service_name)
            if not config:
                return await self._get_default_health_check_policy(service_name)

            # Get service performance metrics
            metrics = await self.scaling_metrics_service.get_component_metrics(
                component=service_name,
                limit=50
            )

            # Analyze health check effectiveness
            policy = await self._analyze_health_check_effectiveness(config, metrics)

            self._log_operation_success("evaluate_health_check_policies", correlation_id)
            return policy

        except Exception as e:
            self._log_operation_error("evaluate_health_check_policies", str(e), correlation_id)
            raise

    async def manage_session_affinity(
        self,
        service_name: str,
        enable_affinity: bool,
        affinity_config: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Manage session affinity for stateful operations.

        Args:
            service_name: Service name for affinity management
            enable_affinity: Whether to enable session affinity
            affinity_config: Optional affinity configuration
            correlation_id: Optional correlation ID for tracking

        Returns:
            Session affinity configuration result
        """
        try:
            self._log_operation_start("manage_session_affinity", correlation_id)

            # Get current configuration
            config = await self.load_balancer_repo.get_config_by_service(service_name)
            if not config:
                raise ValueError(f"Load balancer configuration not found for service: {service_name}")

            # Update session affinity settings
            update_data = {
                "session_affinity": enable_affinity,
                "updated_at": datetime.now(timezone.utc)
            }

            if enable_affinity and affinity_config:
                update_data["sticky_session_timeout"] = affinity_config.get("timeout", 3600)
                update_data["configuration"] = {
                    **(config.configuration or {}),
                    "session_affinity": affinity_config
                }

            updated_config = await self.load_balancer_repo.update(config.id, update_data)

            result = {
                "service_name": service_name,
                "session_affinity_enabled": enable_affinity,
                "configuration": updated_config.configuration,
                "updated_at": updated_config.updated_at
            }

            self._log_operation_success("manage_session_affinity", correlation_id)
            return result

        except Exception as e:
            self._log_operation_error("manage_session_affinity", str(e), correlation_id)
            raise

    async def update_upstream_servers(
        self,
        service_name: str,
        upstream_servers: List[Dict[str, Any]],
        correlation_id: Optional[str] = None
    ) -> LoadBalancerConfig:
        """
        Update upstream servers for a load balancer configuration.

        Args:
            service_name: Service name
            upstream_servers: List of upstream server configurations
            correlation_id: Optional correlation ID for tracking

        Returns:
            Updated load balancer configuration
        """
        try:
            self._log_operation_start("update_upstream_servers", correlation_id)

            # Get current configuration
            config = await self.load_balancer_repo.get_config_by_service(service_name)
            if not config:
                raise ValueError(f"Load balancer configuration not found for service: {service_name}")

            # Validate upstream servers
            await self._validate_upstream_servers(upstream_servers)

            # Update configuration
            upstream_config = {
                "servers": upstream_servers,
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "total_servers": len(upstream_servers),
                "active_servers": len([s for s in upstream_servers if s.get("active", True)])
            }

            updated_config = await self.load_balancer_repo.update_upstream_servers(
                config.id,
                upstream_config
            )

            self._log_operation_success("update_upstream_servers", correlation_id)
            return updated_config

        except Exception as e:
            self._log_operation_error("update_upstream_servers", str(e), correlation_id)
            raise

    async def _analyze_routing_requirements(
        self,
        service_name: str,
        metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze routing requirements based on business metrics."""

        # Business-aware routing logic for Culture Connect
        cpu_utilization = metrics.get("cpu_utilization", 0)
        memory_utilization = metrics.get("memory_utilization", 0)
        response_time = metrics.get("response_time_ms", 0)
        error_rate = metrics.get("error_rate", 0)

        strategy = {
            "service_name": service_name,
            "recommended_strategy": LoadBalancerStrategy.ROUND_ROBIN,
            "session_affinity": False,
            "health_check_frequency": "normal",
            "reasoning": [],
            "confidence": 0.8
        }

        # High load scenarios - use least connections
        if cpu_utilization > 70 or memory_utilization > 75:
            strategy.update({
                "recommended_strategy": LoadBalancerStrategy.LEAST_CONNECTIONS,
                "health_check_frequency": "high"
            })
            strategy["reasoning"].append("High resource utilization detected")

        # High error rate - use least response time
        if error_rate > 0.05:
            strategy.update({
                "recommended_strategy": LoadBalancerStrategy.LEAST_RESPONSE_TIME,
                "health_check_frequency": "high"
            })
            strategy["reasoning"].append("High error rate requires response time optimization")

        # Booking service specific logic
        if "booking" in service_name.lower():
            strategy.update({
                "session_affinity": True,
                "sticky_session_timeout": 1800  # 30 minutes for booking sessions
            })
            strategy["reasoning"].append("Booking service requires session affinity")

        # Payment service specific logic
        if "payment" in service_name.lower():
            strategy.update({
                "recommended_strategy": LoadBalancerStrategy.LEAST_RESPONSE_TIME,
                "health_check_frequency": "high"
            })
            strategy["reasoning"].append("Payment service requires low latency routing")

        return strategy

    async def _get_default_routing_strategy(self, service_name: str) -> Dict[str, Any]:
        """Get default routing strategy for a service."""
        return {
            "service_name": service_name,
            "recommended_strategy": LoadBalancerStrategy.ROUND_ROBIN,
            "session_affinity": False,
            "health_check_frequency": "normal",
            "reasoning": ["Default configuration - no metrics available"],
            "confidence": 0.5
        }

    async def _analyze_health_check_effectiveness(
        self,
        config: LoadBalancerConfig,
        metrics: List[ScalingMetrics]
    ) -> Dict[str, Any]:
        """Analyze health check effectiveness and provide recommendations."""

        if not metrics:
            return await self._get_default_health_check_policy(config.service_name)

        # Analyze recent metrics for health check optimization
        recent_metrics = metrics[:10]  # Last 10 metrics
        avg_response_time = sum(m.response_time_ms or 0 for m in recent_metrics) / len(recent_metrics)
        avg_error_rate = sum(float(m.error_rate or 0) for m in recent_metrics) / len(recent_metrics)

        policy = {
            "service_name": config.service_name,
            "current_interval": config.health_check_interval_seconds,
            "current_timeout": config.health_check_timeout_seconds,
            "current_retries": config.health_check_retries,
            "recommendations": {},
            "reasoning": []
        }

        # Adjust based on service performance
        if avg_response_time > 1000:  # High response time
            policy["recommendations"].update({
                "interval_seconds": max(config.health_check_interval_seconds + 10, 60),
                "timeout_seconds": min(config.health_check_timeout_seconds + 2, 15),
                "retries": min(config.health_check_retries + 1, 5)
            })
            policy["reasoning"].append("Increased intervals due to high response time")

        if avg_error_rate > 0.05:  # High error rate
            policy["recommendations"].update({
                "interval_seconds": max(config.health_check_interval_seconds - 5, 10),
                "timeout_seconds": config.health_check_timeout_seconds,
                "retries": min(config.health_check_retries + 1, 5)
            })
            policy["reasoning"].append("Decreased intervals due to high error rate")

        # Stable service - optimize for efficiency
        if avg_response_time < 200 and avg_error_rate < 0.01:
            policy["recommendations"].update({
                "interval_seconds": min(config.health_check_interval_seconds + 15, 120),
                "timeout_seconds": config.health_check_timeout_seconds,
                "retries": max(config.health_check_retries - 1, 2)
            })
            policy["reasoning"].append("Optimized intervals for stable service")

        return policy

    async def _get_default_health_check_policy(self, service_name: str) -> Dict[str, Any]:
        """Get default health check policy for a service."""
        return {
            "service_name": service_name,
            "current_interval": 30,
            "current_timeout": 5,
            "current_retries": 3,
            "recommendations": {
                "interval_seconds": 30,
                "timeout_seconds": 5,
                "retries": 3
            },
            "reasoning": ["Default health check policy"]
        }

    async def _validate_config_data(self, config_data: LoadBalancerConfigCreate):
        """Validate load balancer configuration data."""
        if not config_data.name or not config_data.name.strip():
            raise ValueError("Configuration name is required")

        if not config_data.service_name or not config_data.service_name.strip():
            raise ValueError("Service name is required")

        if config_data.health_check_interval_seconds < 10:
            raise ValueError("Health check interval must be at least 10 seconds")

        if config_data.health_check_timeout_seconds < 1:
            raise ValueError("Health check timeout must be at least 1 second")

        if config_data.health_check_retries < 1:
            raise ValueError("Health check retries must be at least 1")

    async def _validate_upstream_servers(self, upstream_servers: List[Dict[str, Any]]):
        """Validate upstream server configurations."""
        if not upstream_servers:
            raise ValueError("At least one upstream server is required")

        for i, server in enumerate(upstream_servers):
            if not server.get("host"):
                raise ValueError(f"Server {i}: host is required")

            if not server.get("port") or not isinstance(server["port"], int):
                raise ValueError(f"Server {i}: valid port number is required")

            if server["port"] < 1 or server["port"] > 65535:
                raise ValueError(f"Server {i}: port must be between 1 and 65535")


class ContainerOrchestrationService(BaseService):
    """
    Service for container orchestration and Kubernetes integration.

    Provides abstraction layer over Kubernetes APIs with Culture Connect-specific
    scaling logic and business-aware container lifecycle management.
    """

    def __init__(
        self,
        container_metrics_repo: ContainerMetricsRepository,
        auto_scaling_service: AutoScalingService,
        scaling_event_repo: ScalingEventRepository,
        circuit_breaker: Optional[BaseCircuitBreaker] = None
    ):
        super().__init__()
        self.container_metrics_repo = container_metrics_repo
        self.auto_scaling_service = auto_scaling_service
        self.scaling_event_repo = scaling_event_repo
        self.circuit_breaker = circuit_breaker or BaseCircuitBreaker(
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=Exception,
                name="container_metrics"
            )
        )

    async def record_container_metrics(
        self,
        metrics_data: ContainerMetricsCreate,
        correlation_id: Optional[str] = None
    ) -> ContainerMetrics:
        """
        Record container performance metrics.

        Args:
            metrics_data: Container metrics data
            correlation_id: Optional correlation ID for tracking

        Returns:
            Created container metrics record
        """
        try:
            self._log_operation_start("record_container_metrics", correlation_id)

            # Validate metrics data
            await self._validate_container_metrics(metrics_data)

            # Create metrics record
            metrics = await self.container_metrics_repo.create_container_metric(
                container_id=metrics_data.container_id,
                pod_name=metrics_data.pod_name,
                namespace=metrics_data.namespace,
                node_name=metrics_data.node_name,
                cpu_usage_cores=metrics_data.cpu_usage_cores,
                cpu_limit_cores=metrics_data.cpu_limit_cores,
                memory_usage_bytes=metrics_data.memory_usage_bytes,
                memory_limit_bytes=metrics_data.memory_limit_bytes,
                request_count=metrics_data.request_count,
                error_count=metrics_data.error_count,
                avg_response_time_ms=metrics_data.avg_response_time_ms,
                status=metrics_data.status,
                restart_count=metrics_data.restart_count,
                network_rx_bytes=metrics_data.network_rx_bytes,
                network_tx_bytes=metrics_data.network_tx_bytes,
                labels=metrics_data.labels,
                annotations=metrics_data.annotations
            )

            self._log_operation_success("record_container_metrics", correlation_id)
            return metrics

        except Exception as e:
            self._log_operation_error("record_container_metrics", str(e), correlation_id)
            raise

    async def get_namespace_utilization(
        self,
        namespace: str,
        time_window_minutes: int = 10,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get resource utilization summary for a namespace.

        Args:
            namespace: Kubernetes namespace
            time_window_minutes: Time window for metrics aggregation
            correlation_id: Optional correlation ID for tracking

        Returns:
            Namespace utilization summary
        """
        try:
            self._log_operation_start("get_namespace_utilization", correlation_id)

            utilization = await self.container_metrics_repo.get_resource_utilization_summary(
                namespace=namespace,
                time_window_minutes=time_window_minutes
            )

            self._log_operation_success("get_namespace_utilization", correlation_id)
            return utilization

        except Exception as e:
            self._log_operation_error("get_namespace_utilization", str(e), correlation_id)
            raise

    async def coordinate_cross_service_scaling(
        self,
        primary_service: str,
        dependent_services: List[str],
        correlation_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Coordinate scaling across dependent services.

        Args:
            primary_service: Primary service driving scaling decisions
            dependent_services: List of dependent services to scale
            correlation_id: Optional correlation ID for tracking

        Returns:
            List of scaling coordination results
        """
        try:
            self._log_operation_start("coordinate_cross_service_scaling", correlation_id)

            # Get primary service scaling decisions
            primary_decisions = await self.auto_scaling_service.evaluate_scaling_policies()
            primary_decision = next(
                (d for d in primary_decisions if d["component"] == primary_service),
                None
            )

            if not primary_decision or primary_decision["action"] == "no_action":
                return []

            coordination_results = []

            # Apply coordinated scaling to dependent services
            for dependent_service in dependent_services:
                try:
                    result = await self._apply_coordinated_scaling(
                        dependent_service,
                        primary_decision,
                        correlation_id
                    )
                    coordination_results.append(result)

                except Exception as e:
                    logger.error(f"Error coordinating scaling for {dependent_service}: {str(e)}")
                    coordination_results.append({
                        "service": dependent_service,
                        "success": False,
                        "error": str(e)
                    })

            self._log_operation_success("coordinate_cross_service_scaling", correlation_id)
            return coordination_results

        except Exception as e:
            self._log_operation_error("coordinate_cross_service_scaling", str(e), correlation_id)
            raise

    async def export_custom_metrics(
        self,
        namespace: str,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Export custom business metrics for Kubernetes HPA.

        Args:
            namespace: Kubernetes namespace
            correlation_id: Optional correlation ID for tracking

        Returns:
            Custom metrics for HPA consumption
        """
        try:
            self._log_operation_start("export_custom_metrics", correlation_id)

            # Get namespace utilization
            utilization = await self.get_namespace_utilization(namespace, 5)

            # Calculate business-specific metrics
            custom_metrics = await self._calculate_business_metrics(namespace, utilization)

            # Format for Kubernetes custom metrics API
            k8s_metrics = await self._format_for_kubernetes_metrics_api(custom_metrics)

            self._log_operation_success("export_custom_metrics", correlation_id)
            return k8s_metrics

        except Exception as e:
            self._log_operation_error("export_custom_metrics", str(e), correlation_id)
            raise

    async def manage_container_lifecycle(
        self,
        namespace: str,
        deployment_name: str,
        action: str,
        target_replicas: Optional[int] = None,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Manage container lifecycle operations.

        Args:
            namespace: Kubernetes namespace
            deployment_name: Deployment name
            action: Lifecycle action (scale, restart, update)
            target_replicas: Target replica count for scaling
            correlation_id: Optional correlation ID for tracking

        Returns:
            Lifecycle operation result
        """
        try:
            self._log_operation_start("manage_container_lifecycle", correlation_id)

            # Validate action
            valid_actions = ["scale", "restart", "update", "rollback"]
            if action not in valid_actions:
                raise ValueError(f"Invalid action: {action}. Must be one of {valid_actions}")

            # Execute lifecycle operation
            result = await self._execute_lifecycle_operation(
                namespace,
                deployment_name,
                action,
                target_replicas,
                correlation_id
            )

            # Record scaling event if applicable
            if action == "scale" and target_replicas is not None:
                await self.scaling_event_repo.create_scaling_event(
                    event_type=ScalingEventType.SCALING_COMPLETED,
                    component=deployment_name,
                    trigger_type=ScalingTriggerType.CUSTOM_METRIC,
                    target_replicas=target_replicas,
                    actual_replicas=result.get("actual_replicas", target_replicas),
                    success=result.get("success", True),
                    metadata={
                        "namespace": namespace,
                        "action": action,
                        "correlation_id": correlation_id
                    }
                )

            self._log_operation_success("manage_container_lifecycle", correlation_id)
            return result

        except Exception as e:
            self._log_operation_error("manage_container_lifecycle", str(e), correlation_id)
            raise

    async def generate_kubernetes_manifests(
        self,
        service_config: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Generate Kubernetes manifests for a service.

        Args:
            service_config: Service configuration
            correlation_id: Optional correlation ID for tracking

        Returns:
            Dictionary of manifest names to YAML content
        """
        try:
            self._log_operation_start("generate_kubernetes_manifests", correlation_id)

            manifests = {}

            # Generate deployment manifest
            manifests["deployment.yaml"] = await self._generate_deployment_manifest(service_config)

            # Generate service manifest
            manifests["service.yaml"] = await self._generate_service_manifest(service_config)

            # Generate HPA manifest
            manifests["hpa.yaml"] = await self._generate_hpa_manifest(service_config)

            # Generate ingress manifest if needed
            if service_config.get("expose_externally", False):
                manifests["ingress.yaml"] = await self._generate_ingress_manifest(service_config)

            # Generate configmap if needed
            if service_config.get("config_data"):
                manifests["configmap.yaml"] = await self._generate_configmap_manifest(service_config)

            self._log_operation_success("generate_kubernetes_manifests", correlation_id)
            return manifests

        except Exception as e:
            self._log_operation_error("generate_kubernetes_manifests", str(e), correlation_id)
            raise

    async def _apply_coordinated_scaling(
        self,
        service: str,
        primary_decision: Dict[str, Any],
        correlation_id: Optional[str]
    ) -> Dict[str, Any]:
        """Apply coordinated scaling to a dependent service."""

        # Culture Connect specific coordination logic
        coordination_rules = {
            "booking-service": {
                "payment-service": {"ratio": 0.8, "min_replicas": 2},
                "notification-service": {"ratio": 0.6, "min_replicas": 1},
                "vendor-service": {"ratio": 0.5, "min_replicas": 1}
            },
            "payment-service": {
                "audit-service": {"ratio": 1.0, "min_replicas": 1}
            }
        }

        primary_service = primary_decision["component"]
        if primary_service not in coordination_rules:
            return {"service": service, "action": "no_coordination_rule", "success": True}

        if service not in coordination_rules[primary_service]:
            return {"service": service, "action": "not_dependent", "success": True}

        rule = coordination_rules[primary_service][service]
        primary_target = primary_decision.get("target_replicas", 1)

        # Calculate target replicas for dependent service
        target_replicas = max(
            int(primary_target * rule["ratio"]),
            rule["min_replicas"]
        )

        # Execute scaling for dependent service
        result = await self.manage_container_lifecycle(
            namespace="culture-connect",  # Default namespace
            deployment_name=service,
            action="scale",
            target_replicas=target_replicas,
            correlation_id=correlation_id
        )

        return {
            "service": service,
            "action": "coordinated_scaling",
            "target_replicas": target_replicas,
            "success": result.get("success", True),
            "coordination_rule": rule
        }

    async def _calculate_business_metrics(
        self,
        namespace: str,
        utilization: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate business-specific metrics for HPA."""

        # Culture Connect business metrics
        metrics = {
            "booking_queue_length": 0,
            "vendor_availability_ratio": 1.0,
            "payment_processing_load": 0,
            "notification_queue_depth": 0,
            "user_session_count": 0
        }

        # Calculate booking queue length based on container metrics
        if "booking" in namespace or any("booking" in str(v) for v in utilization.values()):
            request_count = utilization.get("performance", {}).get("total_requests", 0)
            error_count = utilization.get("performance", {}).get("total_errors", 0)

            # Estimate queue length based on request patterns
            metrics["booking_queue_length"] = max(0, request_count - (request_count - error_count))

        # Calculate vendor availability ratio
        running_containers = utilization.get("running_containers", 0)
        total_containers = utilization.get("container_count", 1)
        metrics["vendor_availability_ratio"] = running_containers / max(total_containers, 1)

        # Calculate payment processing load
        avg_response_time = utilization.get("performance", {}).get("avg_response_time_ms", 0)
        if avg_response_time > 0:
            # Higher response time indicates higher load
            metrics["payment_processing_load"] = min(avg_response_time / 100, 100)

        return metrics

    async def _format_for_kubernetes_metrics_api(
        self,
        custom_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format custom metrics for Kubernetes custom metrics API."""

        formatted_metrics = {
            "apiVersion": "custom.metrics.k8s.io/v1beta1",
            "kind": "MetricValueList",
            "metadata": {
                "selfLink": "/apis/custom.metrics.k8s.io/v1beta1"
            },
            "items": []
        }

        for metric_name, value in custom_metrics.items():
            formatted_metrics["items"].append({
                "describedObject": {
                    "kind": "Pod",
                    "namespace": "culture-connect",
                    "name": "*"
                },
                "metricName": metric_name,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "value": str(value)
            })

        return formatted_metrics

    async def _execute_lifecycle_operation(
        self,
        namespace: str,
        deployment_name: str,
        action: str,
        target_replicas: Optional[int],
        correlation_id: Optional[str]
    ) -> Dict[str, Any]:
        """Execute container lifecycle operation."""

        # Simulate Kubernetes API operations
        # In production, this would use the Kubernetes Python client

        result = {
            "namespace": namespace,
            "deployment": deployment_name,
            "action": action,
            "success": True,
            "timestamp": datetime.now(timezone.utc),
            "correlation_id": correlation_id
        }

        if action == "scale":
            # Simulate scaling operation
            await asyncio.sleep(0.1)  # Simulate API call delay
            result.update({
                "previous_replicas": 1,  # Would come from K8s API
                "target_replicas": target_replicas,
                "actual_replicas": target_replicas,
                "scaling_duration_seconds": 5
            })

        elif action == "restart":
            # Simulate restart operation
            await asyncio.sleep(0.2)
            result.update({
                "restart_count": 1,
                "restart_duration_seconds": 10
            })

        elif action == "update":
            # Simulate update operation
            await asyncio.sleep(0.3)
            result.update({
                "update_strategy": "RollingUpdate",
                "update_duration_seconds": 30
            })

        elif action == "rollback":
            # Simulate rollback operation
            await asyncio.sleep(0.2)
            result.update({
                "rollback_revision": "previous",
                "rollback_duration_seconds": 15
            })

        return result

    async def _generate_deployment_manifest(self, service_config: Dict[str, Any]) -> str:
        """Generate Kubernetes deployment manifest."""

        manifest = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {service_config.get('name', 'culture-connect-service')}
  namespace: {service_config.get('namespace', 'culture-connect')}
  labels:
    app: {service_config.get('name', 'culture-connect-service')}
    version: {service_config.get('version', 'v1')}
spec:
  replicas: {service_config.get('replicas', 3)}
  selector:
    matchLabels:
      app: {service_config.get('name', 'culture-connect-service')}
  template:
    metadata:
      labels:
        app: {service_config.get('name', 'culture-connect-service')}
        version: {service_config.get('version', 'v1')}
    spec:
      containers:
      - name: {service_config.get('name', 'culture-connect-service')}
        image: {service_config.get('image', 'culture-connect:latest')}
        ports:
        - containerPort: {service_config.get('port', 8000)}
        resources:
          requests:
            memory: "{service_config.get('memory_request', '256Mi')}"
            cpu: "{service_config.get('cpu_request', '250m')}"
          limits:
            memory: "{service_config.get('memory_limit', '512Mi')}"
            cpu: "{service_config.get('cpu_limit', '500m')}"
        livenessProbe:
          httpGet:
            path: /health
            port: {service_config.get('port', 8000)}
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: {service_config.get('port', 8000)}
          initialDelaySeconds: 5
          periodSeconds: 5
        env:
        - name: ENVIRONMENT
          value: "{service_config.get('environment', 'production')}"
"""
        return manifest

    async def _generate_service_manifest(self, service_config: Dict[str, Any]) -> str:
        """Generate Kubernetes service manifest."""

        manifest = f"""apiVersion: v1
kind: Service
metadata:
  name: {service_config.get('name', 'culture-connect-service')}
  namespace: {service_config.get('namespace', 'culture-connect')}
  labels:
    app: {service_config.get('name', 'culture-connect-service')}
spec:
  selector:
    app: {service_config.get('name', 'culture-connect-service')}
  ports:
  - protocol: TCP
    port: {service_config.get('service_port', 80)}
    targetPort: {service_config.get('port', 8000)}
  type: {service_config.get('service_type', 'ClusterIP')}
"""
        return manifest

    async def _generate_hpa_manifest(self, service_config: Dict[str, Any]) -> str:
        """Generate Kubernetes HPA manifest."""

        manifest = f"""apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {service_config.get('name', 'culture-connect-service')}-hpa
  namespace: {service_config.get('namespace', 'culture-connect')}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {service_config.get('name', 'culture-connect-service')}
  minReplicas: {service_config.get('min_replicas', 2)}
  maxReplicas: {service_config.get('max_replicas', 10)}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {service_config.get('cpu_target', 70)}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {service_config.get('memory_target', 80)}
  - type: Pods
    pods:
      metric:
        name: booking_queue_length
      target:
        type: AverageValue
        averageValue: "{service_config.get('queue_target', '10')}"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: {service_config.get('scale_up_stabilization', 60)}
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: {service_config.get('scale_down_stabilization', 300)}
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
"""
        return manifest

    async def _generate_ingress_manifest(self, service_config: Dict[str, Any]) -> str:
        """Generate Kubernetes ingress manifest."""

        manifest = f"""apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {service_config.get('name', 'culture-connect-service')}-ingress
  namespace: {service_config.get('namespace', 'culture-connect')}
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - {service_config.get('domain', 'api.cultureconnect.com')}
    secretName: {service_config.get('name', 'culture-connect-service')}-tls
  rules:
  - host: {service_config.get('domain', 'api.cultureconnect.com')}
    http:
      paths:
      - path: {service_config.get('path', '/')}
        pathType: Prefix
        backend:
          service:
            name: {service_config.get('name', 'culture-connect-service')}
            port:
              number: {service_config.get('service_port', 80)}
"""
        return manifest

    async def _generate_configmap_manifest(self, service_config: Dict[str, Any]) -> str:
        """Generate Kubernetes configmap manifest."""

        config_data = service_config.get('config_data', {})
        data_section = "\n".join([f"  {key}: {value}" for key, value in config_data.items()])

        manifest = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: {service_config.get('name', 'culture-connect-service')}-config
  namespace: {service_config.get('namespace', 'culture-connect')}
data:
{data_section}
"""
        return manifest

    async def _validate_container_metrics(self, metrics_data: ContainerMetricsCreate):
        """Validate container metrics data."""
        if not metrics_data.container_id or not metrics_data.container_id.strip():
            raise ValueError("Container ID is required")

        if not metrics_data.pod_name or not metrics_data.pod_name.strip():
            raise ValueError("Pod name is required")

        if not metrics_data.namespace or not metrics_data.namespace.strip():
            raise ValueError("Namespace is required")

        if metrics_data.cpu_usage_cores is not None and metrics_data.cpu_usage_cores < 0:
            raise ValueError("CPU usage must be non-negative")

        if metrics_data.memory_usage_bytes is not None and metrics_data.memory_usage_bytes < 0:
            raise ValueError("Memory usage must be non-negative")

        if metrics_data.request_count < 0:
            raise ValueError("Request count must be non-negative")

        if metrics_data.error_count < 0:
            raise ValueError("Error count must be non-negative")
