"""
Review Repository for Culture Connect Backend API.

This module provides comprehensive repository class for review data access operations
including CRUD operations, advanced querying, and business logic support.

Implements Task 4.4.1 Phase 3 requirements for review repository layer with:
- Complete review lifecycle data access
- Booking validation ensuring only completed bookings can be reviewed
- Advanced filtering with full-text search capabilities
- Performance optimization with <200ms query targets
- Integration with AI moderation workflow
- Comprehensive audit logging and error handling

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
from datetime import datetime, date, timedelta, timezone
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from app.repositories.base import BaseRepository, PaginationParams, QueryResult, RepositoryError
from app.models.review_models import Review, ReviewStatus
from app.models.booking import Booking, BookingStatus
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service
from app.schemas.review_schemas import ReviewFilterSchema


class ReviewRepository(BaseRepository[Review]):
    """
    Repository for review data access operations.

    Provides comprehensive review data management including:
    - CRUD operations with performance optimization (<200ms targets)
    - Advanced querying with filtering and full-text search
    - Booking validation for review eligibility
    - Review lifecycle management with status transitions
    - Duplicate review prevention per booking
    - Integration with AI moderation workflow
    """

    def __init__(self, db):
        """Initialize review repository."""
        super().__init__(Review, db)
        self.logger = logging.getLogger(f"{__name__}.ReviewRepository")

    async def create_review(
        self,
        customer_id: int,
        booking_id: int,
        review_data: Dict[str, Any]
    ) -> Review:
        """
        Create a new review with booking validation and duplicate prevention.

        Performance Metrics:
        - Target response time: <200ms for review creation
        - Booking validation: <50ms using booking_id index
        - Duplicate check: <30ms using unique constraint
        - Review creation: <100ms with optimized insert

        Args:
            customer_id: Customer creating the review
            booking_id: Associated completed booking ID
            review_data: Review creation data (rating, title, content)

        Returns:
            Created Review instance

        Raises:
            RepositoryError: If review creation fails
            IntegrityError: If duplicate review for booking exists
        """
        start_time = time.time()

        try:
            # Validate booking eligibility for review
            booking = await self._validate_booking_for_review(booking_id, customer_id)
            if not booking:
                raise RepositoryError(
                    f"Booking {booking_id} is not eligible for review by customer {customer_id}"
                )

            # Check for existing review
            existing_review = await self.get_review_by_booking(booking_id)
            if existing_review:
                raise IntegrityError(
                    f"Review already exists for booking {booking_id}",
                    None, None
                )

            # Prepare review object data
            review_obj_data = {
                "booking_id": booking_id,
                "customer_id": customer_id,
                "vendor_id": booking.vendor_id,
                "service_id": booking.service_id,
                "status": ReviewStatus.PENDING,
                "is_verified_purchase": True,  # Always true for booking-based reviews
                "helpful_count": 0,
                "reported_count": 0,
                **review_data
            }

            # Create review
            review = await self.create(review_obj_data)

            query_time = time.time() - start_time
            self.logger.info(
                f"Review created successfully",
                extra={
                    "review_id": review.id,
                    "booking_id": booking_id,
                    "customer_id": customer_id,
                    "query_time": query_time
                }
            )

            return review

        except IntegrityError:
            # Re-raise integrity errors (duplicate reviews)
            raise
        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to create review",
                extra={
                    "booking_id": booking_id,
                    "customer_id": customer_id,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to create review", e)

    async def get_review_by_booking(self, booking_id: int) -> Optional[Review]:
        """
        Get review by booking ID with performance optimization.

        Uses idx_review_booking_id index for <50ms response time.

        Args:
            booking_id: Booking ID

        Returns:
            Review instance or None if not found
        """
        try:
            stmt = select(Review).where(Review.booking_id == booking_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to get review by booking: {str(e)}")
            raise RepositoryError(f"Failed to get review by booking", e)

    async def get_reviews_with_details(
        self,
        review_ids: List[int]
    ) -> List[Review]:
        """
        Get multiple reviews with all related data.

        Performance optimized with selectinload for related entities.

        Args:
            review_ids: List of review IDs

        Returns:
            List of Review instances with related data
        """
        try:
            stmt = (
                select(Review)
                .options(
                    selectinload(Review.customer),
                    selectinload(Review.vendor),
                    selectinload(Review.service),
                    selectinload(Review.booking),
                    selectinload(Review.responses),
                    selectinload(Review.moderation_records)
                )
                .where(Review.id.in_(review_ids))
            )

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(f"Failed to get reviews with details: {str(e)}")
            raise RepositoryError(f"Failed to get reviews with details", e)

    async def get_vendor_reviews(
        self,
        vendor_id: int,
        status_filter: Optional[List[ReviewStatus]] = None,
        rating_filter: Optional[Tuple[int, int]] = None,
        date_range: Optional[Tuple[date, date]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """
        Get reviews for a specific vendor with filtering.

        Performance Metrics:
        - Target response time: <200ms for vendor review queries
        - Uses idx_review_vendor_status composite index
        - Optimized pagination with cursor support

        Args:
            vendor_id: Vendor ID
            status_filter: Optional list of review statuses
            rating_filter: Optional tuple of (min_rating, max_rating)
            date_range: Optional tuple of (start_date, end_date)
            pagination: Pagination parameters

        Returns:
            Query result with vendor reviews
        """
        try:
            stmt = select(Review).where(Review.vendor_id == vendor_id)

            # Apply status filter
            if status_filter:
                stmt = stmt.where(Review.status.in_(status_filter))

            # Apply rating filter
            if rating_filter:
                min_rating, max_rating = rating_filter
                stmt = stmt.where(
                    and_(
                        Review.rating >= min_rating,
                        Review.rating <= max_rating
                    )
                )

            # Apply date range filter
            if date_range:
                start_date, end_date = date_range
                stmt = stmt.where(
                    and_(
                        Review.created_at >= start_date,
                        Review.created_at <= end_date
                    )
                )

            # Apply ordering (newest first)
            stmt = stmt.order_by(desc(Review.created_at))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get vendor reviews: {str(e)}")
            raise RepositoryError(f"Failed to get vendor reviews", e)

    async def get_service_reviews(
        self,
        service_id: int,
        status_filter: Optional[List[ReviewStatus]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """
        Get reviews for a specific service.

        Uses idx_review_service_status composite index for performance.

        Args:
            service_id: Service ID
            status_filter: Optional list of review statuses
            pagination: Pagination parameters

        Returns:
            Query result with service reviews
        """
        try:
            stmt = select(Review).where(Review.service_id == service_id)

            # Apply status filter
            if status_filter:
                stmt = stmt.where(Review.status.in_(status_filter))

            # Apply ordering (highest rated first, then newest)
            stmt = stmt.order_by(desc(Review.rating), desc(Review.created_at))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get service reviews: {str(e)}")
            raise RepositoryError(f"Failed to get service reviews", e)

    async def search_reviews(
        self,
        search_query: str,
        filters: Optional[ReviewFilterSchema] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """
        Search reviews using full-text search with advanced filtering.

        Performance Metrics:
        - Target response time: <200ms for full-text search
        - Uses GIN index on review_content_search for text search
        - Combines full-text search with structured filters

        Args:
            search_query: Text to search in title and content
            filters: Optional advanced filters
            pagination: Pagination parameters

        Returns:
            Query result with matching reviews
        """
        try:
            # Base query with full-text search
            stmt = select(Review).where(
                or_(
                    Review.title.ilike(f"%{search_query}%"),
                    Review.content.ilike(f"%{search_query}%")
                )
            )

            # Apply additional filters if provided
            if filters:
                stmt = await self._apply_review_filters(stmt, filters)

            # Apply ordering based on relevance and date
            stmt = stmt.order_by(desc(Review.helpful_count), desc(Review.created_at))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to search reviews: {str(e)}")
            raise RepositoryError(f"Failed to search reviews", e)

    async def update_review_status(
        self,
        review_id: int,
        new_status: ReviewStatus,
        moderation_reason: Optional[str] = None
    ) -> Optional[Review]:
        """
        Update review status with audit logging.

        Performance target: <100ms for status updates.

        Args:
            review_id: Review ID
            new_status: New review status
            moderation_reason: Optional reason for status change

        Returns:
            Updated review instance or None if not found
        """
        try:
            review = await self.get(review_id)
            if not review:
                return None

            old_status = review.status
            update_data = {
                "status": new_status,
                "updated_at": datetime.now(timezone.utc)
            }

            if moderation_reason:
                update_data["moderation_reason"] = moderation_reason

            updated_review = await self.update(review_id, update_data)

            self.logger.info(
                f"Review status updated",
                extra={
                    "review_id": review_id,
                    "old_status": old_status,
                    "new_status": new_status,
                    "reason": moderation_reason
                }
            )

            return updated_review

        except Exception as e:
            self.logger.error(f"Failed to update review status: {str(e)}")
            raise RepositoryError(f"Failed to update review status", e)

    async def increment_helpful_count(self, review_id: int) -> Optional[Review]:
        """
        Increment helpful count for a review.

        Performance target: <50ms using atomic update.

        Args:
            review_id: Review ID

        Returns:
            Updated review instance or None if not found
        """
        try:
            stmt = (
                update(Review)
                .where(Review.id == review_id)
                .values(
                    helpful_count=Review.helpful_count + 1,
                    updated_at=datetime.now(timezone.utc)
                )
                .returning(Review)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            updated_review = result.scalar_one_or_none()
            if updated_review:
                await self.db.refresh(updated_review)

            return updated_review

        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"Failed to increment helpful count: {str(e)}")
            raise RepositoryError(f"Failed to increment helpful count", e)

    async def increment_reported_count(self, review_id: int) -> Optional[Review]:
        """
        Increment reported count for a review.

        Performance target: <50ms using atomic update.

        Args:
            review_id: Review ID

        Returns:
            Updated review instance or None if not found
        """
        try:
            stmt = (
                update(Review)
                .where(Review.id == review_id)
                .values(
                    reported_count=Review.reported_count + 1,
                    updated_at=datetime.now(timezone.utc)
                )
                .returning(Review)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            updated_review = result.scalar_one_or_none()
            if updated_review:
                await self.db.refresh(updated_review)

            return updated_review

        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"Failed to increment reported count: {str(e)}")
            raise RepositoryError(f"Failed to increment reported count", e)

    async def get_review_statistics(
        self,
        vendor_id: Optional[int] = None,
        service_id: Optional[int] = None,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get review statistics with performance optimization.

        Performance target: <200ms for statistical queries.

        Args:
            vendor_id: Optional vendor ID filter
            service_id: Optional service ID filter
            date_range: Optional date range filter

        Returns:
            Dictionary with review statistics
        """
        try:
            base_query = select(Review)

            # Apply filters
            if vendor_id:
                base_query = base_query.where(Review.vendor_id == vendor_id)
            if service_id:
                base_query = base_query.where(Review.service_id == service_id)
            if date_range:
                start_date, end_date = date_range
                base_query = base_query.where(
                    and_(
                        Review.created_at >= start_date,
                        Review.created_at <= end_date
                    )
                )

            # Get aggregate statistics
            stats_query = select(
                func.count(Review.id).label('total_reviews'),
                func.avg(Review.rating).label('average_rating'),
                func.sum(Review.helpful_count).label('total_helpful_votes'),
                func.sum(Review.reported_count).label('total_reported'),
                func.count(func.nullif(Review.is_verified_purchase, False)).label('verified_reviews')
            ).select_from(base_query.subquery())

            result = await self.db.execute(stats_query)
            stats = result.first()

            # Get rating distribution
            rating_dist_query = select(
                Review.rating,
                func.count(Review.id).label('count')
            ).select_from(base_query.subquery()).group_by(Review.rating)

            rating_result = await self.db.execute(rating_dist_query)
            rating_distribution = {str(row.rating): row.count for row in rating_result}

            return {
                'total_reviews': stats.total_reviews or 0,
                'average_rating': float(stats.average_rating) if stats.average_rating else 0.0,
                'total_helpful_votes': stats.total_helpful_votes or 0,
                'total_reported': stats.total_reported or 0,
                'verified_reviews': stats.verified_reviews or 0,
                'rating_distribution': rating_distribution
            }

        except Exception as e:
            self.logger.error(f"Failed to get review statistics: {str(e)}")
            raise RepositoryError(f"Failed to get review statistics", e)

    # Private helper methods

    async def _validate_booking_for_review(
        self,
        booking_id: int,
        customer_id: int
    ) -> Optional[Booking]:
        """
        Validate that booking is eligible for review.

        A booking is eligible if:
        - It exists and belongs to the customer
        - It has been completed successfully
        - It doesn't already have a review

        Args:
            booking_id: Booking ID
            customer_id: Customer ID

        Returns:
            Booking instance if eligible, None otherwise
        """
        try:
            stmt = (
                select(Booking)
                .where(
                    and_(
                        Booking.id == booking_id,
                        Booking.customer_id == customer_id,
                        Booking.status == BookingStatus.COMPLETED
                    )
                )
            )

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to validate booking for review: {str(e)}")
            return None

    async def _apply_review_filters(
        self,
        stmt,
        filters: ReviewFilterSchema
    ):
        """
        Apply advanced filters to review query.

        Args:
            stmt: SQLAlchemy select statement
            filters: Review filter schema

        Returns:
            Modified select statement with filters applied
        """
        if filters.vendor_id:
            stmt = stmt.where(Review.vendor_id == filters.vendor_id)

        if filters.service_id:
            stmt = stmt.where(Review.service_id == filters.service_id)

        if filters.customer_id:
            stmt = stmt.where(Review.customer_id == filters.customer_id)

        if filters.rating:
            stmt = stmt.where(Review.rating == filters.rating)

        if filters.min_rating:
            stmt = stmt.where(Review.rating >= filters.min_rating)

        if filters.max_rating:
            stmt = stmt.where(Review.rating <= filters.max_rating)

        if filters.status:
            stmt = stmt.where(Review.status == filters.status)

        if filters.is_verified_purchase is not None:
            stmt = stmt.where(Review.is_verified_purchase == filters.is_verified_purchase)

        if filters.date_from:
            stmt = stmt.where(Review.created_at >= filters.date_from)

        if filters.date_to:
            stmt = stmt.where(Review.created_at <= filters.date_to)

        return stmt

    async def _execute_paginated_query(
        self,
        stmt,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """Execute paginated query with default pagination."""
        if pagination is None:
            pagination = PaginationParams(page=1, per_page=20)

        return await self.get_paginated(
            stmt=stmt,
            pagination=pagination
        )
