#!/bin/bash

# Culture Connect Backend - Production Environment Validation Script
# Comprehensive validation of production deployment readiness

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNING_CHECKS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

# Check function wrapper
check() {
    ((TOTAL_CHECKS++))
    local description="$1"
    local command="$2"
    
    log_info "Checking: $description"
    
    if eval "$command" &>/dev/null; then
        log_success "$description"
        return 0
    else
        log_error "$description"
        return 1
    fi
}

# Check with warning
check_warn() {
    ((TOTAL_CHECKS++))
    local description="$1"
    local command="$2"
    
    log_info "Checking: $description"
    
    if eval "$command" &>/dev/null; then
        log_success "$description"
        return 0
    else
        log_warning "$description"
        return 1
    fi
}

# System requirements validation
validate_system_requirements() {
    log_info "=== System Requirements Validation ==="
    
    # Docker
    check "Docker installation" "command -v docker"
    check "Docker service running" "docker info"
    check "Docker Compose installation" "command -v docker-compose"
    
    # System resources
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    check "Minimum memory (2GB)" "[[ $available_memory -ge 2048 ]]"
    
    local available_disk=$(df / | awk 'NR==2 {print $4}')
    check "Minimum disk space (10GB)" "[[ $available_disk -ge 10485760 ]]"
    
    # Network connectivity
    check "Internet connectivity" "ping -c 1 google.com"
    check "Docker Hub connectivity" "docker pull hello-world"
    
    # Clean up test image
    docker rmi hello-world &>/dev/null || true
}

# File structure validation
validate_file_structure() {
    log_info "=== File Structure Validation ==="
    
    cd "$PROJECT_ROOT"
    
    # Core files
    check "Dockerfile exists" "[[ -f Dockerfile ]]"
    check "Production docker-compose exists" "[[ -f docker/production/docker-compose.prod.yml ]]"
    check "Swarm docker-compose exists" "[[ -f docker/staging/docker-compose.swarm.yml ]]"
    check "Development docker-compose exists" "[[ -f docker/development/docker-compose.yml ]]"
    check "Requirements file exists" "[[ -f requirements.txt ]]"
    check "Logging configuration exists" "[[ -f logging.conf ]]"

    # Environment files
    check "Production environment file exists" "[[ -f environments/production/.env.production ]]"
    check "Staging environment file exists" "[[ -f environments/staging/.env.staging ]]"
    check "Development environment file exists" "[[ -f environments/development/.env.development ]]"

    # Scripts
    check "Production deployment script exists" "[[ -f scripts/deployment/production/deploy-production.sh ]]"
    check "PostgreSQL production config exists" "[[ -f scripts/postgres-prod-config.sql ]]"
    check "Deployment script is executable" "[[ -x scripts/deployment/production/deploy-production.sh ]]"
    
    # Application structure
    check "App directory exists" "[[ -d app ]]"
    check "Main application file exists" "[[ -f app/main.py ]]"
    check "Core module exists" "[[ -d app/core ]]"
    check "API module exists" "[[ -d app/api ]]"
    check "Models module exists" "[[ -d app/models ]]"
    check "Services module exists" "[[ -d app/services ]]"
    check "Repositories module exists" "[[ -d app/repositories ]]"
    check "Schemas module exists" "[[ -d app/schemas ]]"
}

# Environment configuration validation
validate_environment_config() {
    log_info "=== Environment Configuration Validation ==="
    
    local env_file="environments/$ENVIRONMENT/.env.$ENVIRONMENT"
    
    if [[ ! -f "$env_file" ]]; then
        log_error "Environment file not found: $env_file"
        return 1
    fi
    
    # Source environment file for validation
    set -a
    source "$env_file"
    set +a
    
    # Critical environment variables
    check "CC_ENVIRONMENT is set" "[[ -n '${CC_ENVIRONMENT:-}' ]]"
    check "CC_SECRET_KEY is set" "[[ -n '${CC_SECRET_KEY:-}' ]]"
    check "CC_DATABASE_URL is set" "[[ -n '${CC_DATABASE_URL:-}' ]]"
    check "CC_REDIS_URL is set" "[[ -n '${CC_REDIS_URL:-}' ]]"
    
    # Payment provider configuration
    check "Paystack secret key is set" "[[ -n '${CC_PAYSTACK_SECRET_KEY:-}' ]]"
    check "Stripe secret key is set" "[[ -n '${CC_STRIPE_SECRET_KEY:-}' ]]"
    check_warn "Busha API key is set" "[[ -n '${CC_BUSHA_API_KEY:-}' ]]"
    
    # Security validation
    check "Secret key is not default" "[[ '${CC_SECRET_KEY:-}' != 'your-secret-key-here-development-only' ]]"
    check "JWT secret key is set" "[[ -n '${CC_JWT_SECRET_KEY:-}' ]]"
    
    # Database configuration
    check "PostgreSQL password is set" "[[ -n '${CC_POSTGRES_PASSWORD:-}' ]]"
    check "Redis password is set" "[[ -n '${CC_REDIS_PASSWORD:-}' ]]"
    
    # External services
    check_warn "Sentry DSN is configured" "[[ -n '${CC_SENTRY_DSN:-}' ]]"
    check_warn "AIML API key is configured" "[[ -n '${CC_AIML_API_KEY:-}' ]]"
    
    # URLs and domains
    check "Frontend URL is configured" "[[ -n '${CC_FRONTEND_URL:-}' ]]"
    check "PWA URL is configured" "[[ -n '${CC_PWA_URL:-}' ]]"
}

# Docker configuration validation
validate_docker_config() {
    log_info "=== Docker Configuration Validation ==="
    
    cd "$PROJECT_ROOT"
    
    # Dockerfile validation
    check "Dockerfile has production stage" "grep -q 'FROM.*as production' Dockerfile"
    check "Dockerfile has non-root user" "grep -q 'USER appuser' Dockerfile"
    check "Dockerfile has health check" "grep -q 'HEALTHCHECK' Dockerfile"
    check "Dockerfile has proper labels" "grep -q 'LABEL maintainer' Dockerfile"
    
    # Docker Compose validation
    check "Production compose has API service" "grep -q 'api:' docker/production/docker-compose.prod.yml"
    check "Production compose has database service" "grep -q 'db:' docker/production/docker-compose.prod.yml"
    check "Production compose has Redis service" "grep -q 'redis:' docker/production/docker-compose.prod.yml"
    check "Production compose has Celery worker" "grep -q 'celery_worker:' docker/production/docker-compose.prod.yml"
    check "Production compose has health checks" "grep -q 'healthcheck:' docker/production/docker-compose.prod.yml"
    check "Production compose has resource limits" "grep -q 'resources:' docker/production/docker-compose.prod.yml"
    check "Production compose has restart policies" "grep -q 'restart_policy:' docker/production/docker-compose.prod.yml"

    # Network and volume configuration
    check "Production compose has networks" "grep -q 'networks:' docker/production/docker-compose.prod.yml"
    check "Production compose has volumes" "grep -q 'volumes:' docker/production/docker-compose.prod.yml"
    check "Production compose has persistent volumes" "grep -q 'postgres_data_prod:' docker/production/docker-compose.prod.yml"
}

# Security validation
validate_security() {
    log_info "=== Security Configuration Validation ==="
    
    # File permissions
    check "Environment files have secure permissions" "[[ $(stat -c '%a' .env.production 2>/dev/null || echo '644') == '600' ]] || [[ $(stat -c '%a' .env.production 2>/dev/null || echo '644') == '644' ]]"
    
    # Secret validation
    local env_file=".env.$ENVIRONMENT"
    if [[ -f "$env_file" ]]; then
        check "No default passwords in environment" "! grep -q 'password_here' '$env_file'"
        check "No test keys in production" "! grep -q 'test_.*_key' '$env_file'"
        check "SSL is enabled" "grep -q 'CC_SSL_ENABLED=true' '$env_file'"
    fi
    
    # Docker security
    check "Dockerfile uses non-root user" "grep -q 'USER appuser' Dockerfile"
    check "Dockerfile has security updates" "grep -q 'apt-get upgrade' Dockerfile"
    
    # Network security
    check_warn "Firewall is configured" "command -v ufw && ufw status | grep -q 'Status: active'"
}

# Performance validation
validate_performance() {
    log_info "=== Performance Configuration Validation ==="
    
    # Resource limits in Docker Compose
    check "API service has memory limits" "grep -A 10 'api:' docker-compose.prod.yml | grep -q 'memory: 1G'"
    check "Database service has memory limits" "grep -A 10 'db:' docker-compose.prod.yml | grep -q 'memory: 2G'"
    check "Redis service has memory limits" "grep -A 10 'redis:' docker-compose.prod.yml | grep -q 'memory: 512M'"
    
    # PostgreSQL configuration
    check "PostgreSQL production config exists" "[[ -f scripts/postgres-prod-config.sql ]]"
    check "PostgreSQL config has performance tuning" "grep -q 'shared_buffers' scripts/postgres-prod-config.sql"
    check "PostgreSQL config has monitoring" "grep -q 'log_min_duration_statement' scripts/postgres-prod-config.sql"
    
    # Redis configuration
    check "Redis has memory policy configured" "grep -q 'maxmemory-policy' docker-compose.prod.yml"
    check "Redis has persistence configured" "grep -q 'appendonly yes' docker-compose.prod.yml"
}

# Monitoring validation
validate_monitoring() {
    log_info "=== Monitoring Configuration Validation ==="
    
    # Logging configuration
    check "Logging configuration exists" "[[ -f logging.conf ]]"
    check "Logging has JSON formatter" "grep -q 'formatter_json' logging.conf"
    check "Logging has file rotation" "grep -q 'RotatingFileHandler' logging.conf"
    check "Logging has Sentry handler" "grep -q 'SentryHandler' logging.conf"
    
    # Health check endpoints
    check_warn "Health check endpoint configured" "grep -q '/health' app/main.py || find app -name '*.py' -exec grep -l '/health' {} \\;"
    
    # Metrics and monitoring
    check_warn "Sentry configuration in environment" "grep -q 'CC_SENTRY_DSN' .env.$ENVIRONMENT"
}

# Backup validation
validate_backup() {
    log_info "=== Backup Configuration Validation ==="
    
    # Backup volumes
    check "Backup volumes configured" "grep -q 'postgres_backups:' docker-compose.prod.yml"
    check "Backup directories in compose" "grep -q 'CC_BACKUP_PATH' docker-compose.prod.yml"
    
    # PostgreSQL backup configuration
    check "PostgreSQL archive mode configured" "grep -q 'archive_mode = on' scripts/postgres-prod-config.sql"
    check "PostgreSQL archive command configured" "grep -q 'archive_command' scripts/postgres-prod-config.sql"
}

# Generate summary report
generate_summary() {
    log_info "=== Validation Summary ==="
    
    echo
    echo "Total Checks: $TOTAL_CHECKS"
    echo -e "${GREEN}Passed: $PASSED_CHECKS${NC}"
    echo -e "${YELLOW}Warnings: $WARNING_CHECKS${NC}"
    echo -e "${RED}Failed: $FAILED_CHECKS${NC}"
    echo
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        log_success "Production validation completed successfully! Success rate: ${success_rate}%"
        if [[ $WARNING_CHECKS -gt 0 ]]; then
            log_warning "There are $WARNING_CHECKS warnings that should be addressed for optimal production deployment"
        fi
        return 0
    else
        log_error "Production validation failed! $FAILED_CHECKS critical issues must be resolved before deployment"
        return 1
    fi
}

# Main validation function
main() {
    log_info "Starting Culture Connect Backend production validation"
    log_info "Environment: $ENVIRONMENT"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    echo
    
    validate_system_requirements
    echo
    validate_file_structure
    echo
    validate_environment_config
    echo
    validate_docker_config
    echo
    validate_security
    echo
    validate_performance
    echo
    validate_monitoring
    echo
    validate_backup
    echo
    
    generate_summary
}

# Run main function
main "$@"
