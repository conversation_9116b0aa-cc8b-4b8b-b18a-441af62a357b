"""
Unit tests for email services.

This module provides comprehensive unit tests for email service classes:
- EmailTemplateService: Template management and rendering
- EmailDeliveryService: Email delivery and SMTP handling
- EmailPreferenceService: User preference management
- EmailQueueService: Queue management and processing
- EmailNotificationService: High-level notification workflows
- EmailService: Main service integration

Implements Task 2.3.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from uuid import uuid4
from jinja2 import Template, TemplateError

from app.services.email_template_service import EmailTemplateService
from app.services.email_delivery_service import EmailDeliveryService
from app.services.email_preference_service import EmailPreferenceService
from app.services.email_queue_service import EmailQueueService
from app.services.email_notification_service import EmailNotificationService
from app.services.email_service import EmailService
from app.models.email_models import (
    EmailTemplate, EmailDelivery, EmailPreference, <PERSON>ailQueue,
    EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
)
from app.schemas.email_schemas import (
    EmailTemplateCreate, EmailSendRequest, EmailVerificationRequest,
    PasswordResetEmailRequest, EmailPreferenceUpdate
)


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    return AsyncMock()


@pytest.fixture
def email_template_service(mock_db_session):
    """Create EmailTemplateService with mock dependencies."""
    return EmailTemplateService(mock_db_session)


@pytest.fixture
def email_delivery_service(mock_db_session):
    """Create EmailDeliveryService with mock dependencies."""
    return EmailDeliveryService(mock_db_session)


@pytest.fixture
def email_preference_service(mock_db_session):
    """Create EmailPreferenceService with mock dependencies."""
    return EmailPreferenceService(mock_db_session)


@pytest.fixture
def email_queue_service(mock_db_session):
    """Create EmailQueueService with mock dependencies."""
    return EmailQueueService(mock_db_session)


@pytest.fixture
def email_notification_service(mock_db_session):
    """Create EmailNotificationService with mock dependencies."""
    return EmailNotificationService(mock_db_session)


@pytest.fixture
def email_service(mock_db_session):
    """Create EmailService with mock dependencies."""
    return EmailService(mock_db_session)


@pytest.fixture
def sample_template():
    """Create a sample email template."""
    return EmailTemplate(
        id=uuid4(),
        name="welcome_email",
        category=EmailTemplateCategory.NOTIFICATION,
        subject_template="Welcome {{user_name}}!",
        body_template="Hello {{user_name}}, welcome to our platform!",
        variables={"user_name": "string"},
        version=1,
        is_active=True,
        created_by=1
    )


@pytest.fixture
def sample_delivery():
    """Create a sample email delivery."""
    return EmailDelivery(
        id=uuid4(),
        user_id=1,
        recipient_email="<EMAIL>",
        subject="Test Subject",
        body="Test Body",
        status=EmailDeliveryStatus.PENDING,
        priority=3
    )


class TestEmailTemplateService:
    """Test EmailTemplateService."""

    @pytest.mark.asyncio
    async def test_create_template_success(self, email_template_service):
        """Test successful template creation."""
        template_data = EmailTemplateCreate(
            name="test_template",
            category=EmailTemplateCategory.VERIFICATION,
            subject_template="Test Subject {{name}}",
            body_template="Test Body {{name}}",
            variables={"name": "string"}
        )

        # Mock repository method
        from datetime import datetime, timezone
        created_template = EmailTemplate(
            id=uuid4(),
            name=template_data.name,
            category=template_data.category,
            subject_template=template_data.subject_template,
            body_template=template_data.body_template,
            variables=template_data.variables,
            version=1,
            is_active=True,
            created_by=1,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        with patch.object(email_template_service.repository, 'get_template_by_name_and_version', return_value=None):
            with patch.object(email_template_service.repository, 'create_template', return_value=created_template) as mock_create:
                result = await email_template_service.create_template(template_data, created_by=1)

        assert result.name == template_data.name
        assert result.category == template_data.category
        mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_template_success(self, email_template_service, sample_template):
        """Test successful template retrieval."""
        with patch.object(email_template_service.repository, 'get_template_by_id', return_value=sample_template):
            result = await email_template_service.get_template(sample_template.id)

        assert result == sample_template
        email_template_service.repository.get_template_by_id.assert_called_once_with(sample_template.id)

    @pytest.mark.asyncio
    async def test_render_template_success(self, email_template_service, sample_template):
        """Test successful template rendering."""
        variables = {"user_name": "John Doe"}

        with patch.object(email_template_service.repository, 'get_template_by_id', return_value=sample_template):
            subject, body = await email_template_service.render_template(sample_template.id, variables)

        assert subject == "Welcome John Doe!"
        assert body == "Hello John Doe, welcome to our platform!"

    @pytest.mark.asyncio
    async def test_render_template_missing_variable(self, email_template_service, sample_template):
        """Test template rendering with missing variable."""
        variables = {}  # Missing user_name

        with patch.object(email_template_service.repository, 'get_template_by_id', return_value=sample_template):
            with pytest.raises(ValueError, match="Missing required template variable"):
                await email_template_service.render_template(sample_template.id, variables)

    @pytest.mark.asyncio
    async def test_list_templates_success(self, email_template_service):
        """Test successful template listing."""
        templates = [sample_template]
        total = 1

        with patch.object(email_template_service.repository, 'list_templates', return_value=(templates, total)):
            result = await email_template_service.list_templates(
                category=EmailTemplateCategory.NOTIFICATION,
                skip=0,
                limit=10
            )

        assert result.templates == templates
        assert result.total == total
        email_template_service.repository.list_templates.assert_called_once()


class TestEmailDeliveryService:
    """Test EmailDeliveryService."""

    @pytest.mark.asyncio
    async def test_send_email_success(self, email_delivery_service):
        """Test successful email sending."""
        email_request = EmailSendRequest(
            recipient_email="<EMAIL>",
            subject="Test Subject",
            body="Test Body"
        )

        # Mock delivery creation
        created_delivery = EmailDelivery(
            id=uuid4(),
            user_id=1,
            recipient_email=email_request.recipient_email,
            subject=email_request.subject,
            body=email_request.body,
            status=EmailDeliveryStatus.PENDING
        )

        with patch.object(email_delivery_service.repository, 'create_delivery', return_value=created_delivery):
            with patch.object(email_delivery_service, '_send_smtp_email', return_value=True):
                result = await email_delivery_service.send_email(email_request, user_id=1)

        assert result.recipient_email == email_request.recipient_email
        assert result.status == EmailDeliveryStatus.SENT
        email_delivery_service.repository.create_delivery.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_email_smtp_failure(self, email_delivery_service):
        """Test email sending with SMTP failure."""
        email_request = EmailSendRequest(
            recipient_email="<EMAIL>",
            subject="Test Subject",
            body="Test Body"
        )

        # Mock delivery creation
        created_delivery = EmailDelivery(
            id=uuid4(),
            user_id=1,
            recipient_email=email_request.recipient_email,
            subject=email_request.subject,
            body=email_request.body,
            status=EmailDeliveryStatus.PENDING
        )

        with patch.object(email_delivery_service.repository, 'create_delivery', return_value=created_delivery):
            with patch.object(email_delivery_service, '_send_smtp_email', side_effect=Exception("SMTP Error")):
                with patch.object(email_delivery_service.repository, 'update_delivery_status'):
                    result = await email_delivery_service.send_email(email_request, user_id=1)

        assert result.status == EmailDeliveryStatus.FAILED
        email_delivery_service.repository.update_delivery_status.assert_called()

    @pytest.mark.asyncio
    async def test_get_delivery_status_success(self, email_delivery_service, sample_delivery):
        """Test successful delivery status retrieval."""
        with patch.object(email_delivery_service.repository, 'get_delivery_by_id', return_value=sample_delivery):
            result = await email_delivery_service.get_delivery_status(sample_delivery.id)

        assert result == sample_delivery
        email_delivery_service.repository.get_delivery_by_id.assert_called_once_with(sample_delivery.id)

    @pytest.mark.asyncio
    async def test_send_batch_emails_success(self, email_delivery_service):
        """Test successful batch email sending."""
        from app.schemas.email_schemas import EmailBatchSendRequest

        batch_request = EmailBatchSendRequest(
            template_id=uuid4(),
            recipients=[
                {"email": "<EMAIL>", "variables": {"name": "User 1"}},
                {"email": "<EMAIL>", "variables": {"name": "User 2"}}
            ]
        )

        # Mock template retrieval and rendering
        template = EmailTemplate(
            id=batch_request.template_id,
            subject_template="Hello {{name}}",
            body_template="Welcome {{name}}!"
        )

        with patch.object(email_delivery_service.template_service, 'get_template', return_value=template):
            with patch.object(email_delivery_service.template_service, 'render_template',
                            side_effect=[("Hello User 1", "Welcome User 1!"), ("Hello User 2", "Welcome User 2!")]):
                with patch.object(email_delivery_service.queue_service, 'enqueue_email') as mock_enqueue:
                    result = await email_delivery_service.send_batch_emails(batch_request, user_id=1)

        assert result.total_recipients == 2
        assert result.queued_count == 2
        assert mock_enqueue.call_count == 2


class TestEmailPreferenceService:
    """Test EmailPreferenceService."""

    @pytest.mark.asyncio
    async def test_get_user_preferences_success(self, email_preference_service):
        """Test successful user preferences retrieval."""
        preferences = EmailPreference(
            user_id=1,
            marketing_emails=True,
            booking_notifications=True,
            security_emails=True,
            verification_emails=True,
            system_notifications=False
        )

        with patch.object(email_preference_service.repository, 'get_user_preferences', return_value=preferences):
            result = await email_preference_service.get_user_preferences(1)

        assert result == preferences
        email_preference_service.repository.get_user_preferences.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_get_user_preferences_create_default(self, email_preference_service):
        """Test user preferences creation when not found."""
        # Mock repository returning None (not found)
        with patch.object(email_preference_service.repository, 'get_user_preferences', return_value=None):
            with patch.object(email_preference_service.repository, 'create_user_preferences') as mock_create:
                default_preferences = EmailPreference(user_id=1)
                mock_create.return_value = default_preferences

                result = await email_preference_service.get_user_preferences(1)

        assert result == default_preferences
        mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_user_preferences_success(self, email_preference_service):
        """Test successful user preferences update."""
        preferences_data = EmailPreferenceUpdate(
            marketing_emails=False,
            booking_notifications=True
        )

        updated_preferences = EmailPreference(
            user_id=1,
            marketing_emails=False,
            booking_notifications=True,
            security_emails=True,
            verification_emails=True,
            system_notifications=True
        )

        with patch.object(email_preference_service.repository, 'update_user_preferences', return_value=updated_preferences):
            result = await email_preference_service.update_user_preferences(1, preferences_data)

        assert result == updated_preferences
        email_preference_service.repository.update_user_preferences.assert_called_once()

    @pytest.mark.asyncio
    async def test_can_send_email_allowed(self, email_preference_service):
        """Test email sending permission check - allowed."""
        with patch.object(email_preference_service.repository, 'can_send_email', return_value=True):
            result = await email_preference_service.can_send_email(1, EmailTemplateCategory.NOTIFICATION)

        assert result is True
        email_preference_service.repository.can_send_email.assert_called_once()

    @pytest.mark.asyncio
    async def test_can_send_email_blocked(self, email_preference_service):
        """Test email sending permission check - blocked."""
        with patch.object(email_preference_service.repository, 'can_send_email', return_value=False):
            result = await email_preference_service.can_send_email(1, EmailTemplateCategory.MARKETING)

        assert result is False
        email_preference_service.repository.can_send_email.assert_called_once()


class TestEmailQueueService:
    """Test EmailQueueService."""

    @pytest.mark.asyncio
    async def test_enqueue_email_success(self, email_queue_service):
        """Test successful email enqueuing."""
        email_data = {
            "user_id": 1,
            "recipient_email": "<EMAIL>",
            "subject": "Test Subject",
            "body": "Test Body",
            "priority": 1
        }

        queued_item = EmailQueue(**email_data, id=uuid4())

        with patch.object(email_queue_service.repository, 'enqueue_email', return_value=queued_item):
            result = await email_queue_service.enqueue_email(email_data)

        assert result == queued_item
        email_queue_service.repository.enqueue_email.assert_called_once_with(email_data)

    @pytest.mark.asyncio
    async def test_get_next_batch_success(self, email_queue_service):
        """Test successful next batch retrieval."""
        queue_items = [
            EmailQueue(id=uuid4(), user_id=1, recipient_email="<EMAIL>",
                      subject="Test 1", body="Body 1", priority=1),
            EmailQueue(id=uuid4(), user_id=1, recipient_email="<EMAIL>",
                      subject="Test 2", body="Body 2", priority=2)
        ]

        with patch.object(email_queue_service.repository, 'get_next_batch', return_value=queue_items):
            result = await email_queue_service.get_next_batch(batch_size=10)

        assert result == queue_items
        email_queue_service.repository.get_next_batch.assert_called_once_with(10)

    @pytest.mark.asyncio
    async def test_mark_as_sent_success(self, email_queue_service):
        """Test successful queue item marking as sent."""
        queue_id = uuid4()
        delivery_id = uuid4()

        updated_item = EmailQueue(
            id=queue_id,
            user_id=1,
            recipient_email="<EMAIL>",
            subject="Test",
            body="Body",
            status=EmailQueueStatus.SENT,
            delivery_id=delivery_id
        )

        with patch.object(email_queue_service.repository, 'mark_as_sent', return_value=updated_item):
            result = await email_queue_service.mark_as_sent(queue_id, delivery_id)

        assert result == updated_item
        email_queue_service.repository.mark_as_sent.assert_called_once_with(queue_id, delivery_id)

    @pytest.mark.asyncio
    async def test_get_queue_statistics_success(self, email_queue_service):
        """Test successful queue statistics retrieval."""
        stats = {
            "queued": 50,
            "processing": 10,
            "sent": 100,
            "failed": 5,
            "total": 165
        }

        with patch.object(email_queue_service.repository, 'get_queue_statistics', return_value=stats):
            result = await email_queue_service.get_queue_statistics()

        assert result == stats
        email_queue_service.repository.get_queue_statistics.assert_called_once()


class TestEmailNotificationService:
    """Test EmailNotificationService."""

    @pytest.mark.asyncio
    async def test_send_verification_email_success(self, email_notification_service):
        """Test successful verification email sending."""
        verification_request = EmailVerificationRequest(
            user_id=1,
            email="<EMAIL>",
            user_name="John Doe",
            verification_token="token_123"
        )

        # Mock template retrieval and rendering
        template = EmailTemplate(
            id=uuid4(),
            name="email_verification",
            subject_template="Verify your email",
            body_template="Click to verify: {{verification_url}}"
        )

        with patch.object(email_notification_service.template_service, 'get_template_by_name', return_value=template):
            with patch.object(email_notification_service.template_service, 'render_template',
                            return_value=("Verify your email", "Click to verify: http://example.com/verify")):
                with patch.object(email_notification_service.delivery_service, 'send_email') as mock_send:
                    mock_send.return_value = EmailDelivery(id=uuid4(), status=EmailDeliveryStatus.SENT)

                    result = await email_notification_service.send_verification_email(verification_request)

        assert result.delivery_id is not None
        assert "sent successfully" in result.message
        mock_send.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_password_reset_email_success(self, email_notification_service):
        """Test successful password reset email sending."""
        reset_request = PasswordResetEmailRequest(
            user_id=1,
            email="<EMAIL>",
            user_name="John Doe",
            reset_token="reset_token_123"
        )

        # Mock template retrieval and rendering
        template = EmailTemplate(
            id=uuid4(),
            name="password_reset",
            subject_template="Reset your password",
            body_template="Click to reset: {{reset_url}}"
        )

        with patch.object(email_notification_service.template_service, 'get_template_by_name', return_value=template):
            with patch.object(email_notification_service.template_service, 'render_template',
                            return_value=("Reset your password", "Click to reset: http://example.com/reset")):
                with patch.object(email_notification_service.delivery_service, 'send_email') as mock_send:
                    mock_send.return_value = EmailDelivery(id=uuid4(), status=EmailDeliveryStatus.SENT)

                    result = await email_notification_service.send_password_reset_email(reset_request)

        assert result.delivery_id is not None
        assert "sent successfully" in result.message
        mock_send.assert_called_once()


class TestEmailService:
    """Test main EmailService integration."""

    @pytest.mark.asyncio
    async def test_health_check_success(self, email_service):
        """Test successful email service health check."""
        # Mock all sub-service health checks
        with patch.object(email_service.template_service, 'health_check', return_value={"status": "healthy"}):
            with patch.object(email_service.delivery_service, 'health_check', return_value={"status": "healthy"}):
                with patch.object(email_service.preference_service, 'health_check', return_value={"status": "healthy"}):
                    with patch.object(email_service.queue_service, 'health_check', return_value={"status": "healthy"}):
                        result = await email_service.health_check()

        assert result["status"] == "healthy"
        assert result["services"]["template_service"]["status"] == "healthy"
        assert result["services"]["delivery_service"]["status"] == "healthy"

    @pytest.mark.asyncio
    async def test_get_email_analytics_success(self, email_service):
        """Test successful email analytics retrieval."""
        analytics_data = {
            "total_sent": 1000,
            "total_delivered": 950,
            "total_failed": 50,
            "delivery_rate": 95.0
        }

        with patch.object(email_service.delivery_service, 'get_analytics', return_value=analytics_data):
            result = await email_service.get_email_analytics(days_back=30)

        assert result.total_sent == 1000
        assert result.total_delivered == 950
        assert result.delivery_rate == 95.0
