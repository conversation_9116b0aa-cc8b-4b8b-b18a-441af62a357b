"""
Push Notification API endpoints for Culture Connect Backend API.

This module provides comprehensive push notification endpoints including:
- Device token management (registration, CRUD operations, user device listing)
- Notification template management (CRUD operations, versioning, category filtering)
- Notification sending (single, batch, scheduled notifications)
- User notification preferences (category-specific settings, DND scheduling)
- Notification analytics (delivery tracking, queue statistics, performance monitoring)
- Queue management (processing triggers, status monitoring, cleanup operations)

Implements Task 2.3.2 Phase 5 requirements for push notification API endpoints with
production-grade FastAPI integration, authentication, rate limiting, and error handling.
"""

import logging
from typing import Dict, Any, Optional, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.push_notification_services import (
    DeviceTokenService, NotificationTemplateService, NotificationDeliveryService,
    NotificationPreferenceService, NotificationQueueService, PushNotificationService
)
from app.schemas.push_notification_schemas import (
    # Device token schemas
    DeviceTokenCreate, DeviceTokenUpdate, DeviceTokenResponse, DeviceTokenListResponse,
    # Template schemas
    NotificationTemplateCreate, NotificationTemplateUpdate, NotificationTemplateResponse,
    NotificationTemplateListResponse,
    # Notification sending schemas
    NotificationSendRequest, NotificationSendResponse, NotificationBatchSendRequest,
    NotificationBatchSendResponse,
    # Delivery tracking schemas
    NotificationDeliveryResponse, NotificationDeliveryListResponse, NotificationDeliveryStatusUpdate,
    # Preference management schemas
    NotificationPreferenceUpdate, NotificationPreferenceResponse,
    # FCM schemas
    FCMMessageRequest, FCMMessageResponse, FCMBatchRequest, FCMBatchResponse
)
from app.schemas.auth import UserResponse, MessageResponse
from app.schemas.base import PaginationMixin
from app.api.v1.auth import get_current_user, get_current_admin_user, require_permission
from app.models.push_notification_models import (
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)
from app.core.logging import correlation_id
from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Security
security = HTTPBearer()


# ============================================================================
# DEVICE TOKEN MANAGEMENT ENDPOINTS
# ============================================================================

@router.post(
    "/devices/register",
    response_model=DeviceTokenResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register device token",
    description="Register a new device token for push notifications"
)
async def register_device_token(
    device_data: DeviceTokenCreate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DeviceTokenResponse:
    """
    Register a new device token for push notifications.

    This endpoint allows users to register their device tokens for receiving
    push notifications. If a device token already exists, it will be updated
    with the new information.

    **Rate Limiting**: 100 requests per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Device registration request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "platform": device_data.platform.value if device_data.platform else None
        }
    )

    try:
        device_service = DeviceTokenService(db)
        device_token = await device_service.register_device(current_user.id, device_data)

        logger.info(
            f"Device registered successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "device_id": str(device_token.id)
            }
        )

        return device_token

    except ValueError as e:
        logger.warning(f"Invalid device registration data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid device registration data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Device registration failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register device token"
        )


@router.get(
    "/devices",
    response_model=DeviceTokenListResponse,
    summary="Get user devices",
    description="Get all device tokens for the current user"
)
async def get_user_devices(
    platform: Optional[DevicePlatform] = Query(None, description="Filter by device platform"),
    active_only: bool = Query(True, description="Return only active devices"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DeviceTokenListResponse:
    """
    Get all device tokens for the current user.

    This endpoint returns all device tokens registered by the current user,
    with optional filtering by platform and active status.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get user devices request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "platform": platform.value if platform else None,
            "active_only": active_only
        }
    )

    try:
        device_service = DeviceTokenService(db)
        devices = await device_service.get_user_devices(current_user.id, platform, active_only)

        logger.info(
            f"Retrieved {len(devices)} devices",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "device_count": len(devices)
            }
        )

        return DeviceTokenListResponse(
            items=devices,
            total=len(devices),
            page=1,
            per_page=len(devices),
            pages=1
        )

    except Exception as e:
        logger.error(f"Get user devices failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user devices"
        )


@router.get(
    "/devices/{device_id}",
    response_model=DeviceTokenResponse,
    summary="Get device token",
    description="Get a specific device token by ID"
)
async def get_device_token(
    device_id: UUID = Path(..., description="Device token ID"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DeviceTokenResponse:
    """
    Get a specific device token by ID.

    This endpoint returns details for a specific device token.
    Users can only access their own device tokens.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get device token request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "device_id": str(device_id)
        }
    )

    try:
        device_service = DeviceTokenService(db)
        device_token = await device_service.get(device_id)

        if not device_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device token not found"
            )

        # Verify ownership
        if device_token.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to device token"
            )

        logger.info(
            f"Device token retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "device_id": str(device_id)
            }
        )

        return DeviceTokenResponse.model_validate(device_token)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get device token failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve device token"
        )


@router.put(
    "/devices/{device_id}",
    response_model=DeviceTokenResponse,
    summary="Update device token",
    description="Update a device token's information"
)
async def update_device_token(
    device_id: UUID = Path(..., description="Device token ID"),
    device_data: DeviceTokenUpdate = ...,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DeviceTokenResponse:
    """
    Update a device token's information.

    This endpoint allows users to update their device token information
    such as device name, app version, and metadata.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update device token request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "device_id": str(device_id)
        }
    )

    try:
        device_service = DeviceTokenService(db)

        # Verify device exists and ownership
        existing_device = await device_service.get(device_id)
        if not existing_device:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device token not found"
            )

        if existing_device.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to device token"
            )

        # Update device
        update_data = device_data.model_dump(exclude_unset=True)
        updated_device = await device_service.update(device_id, update_data)

        logger.info(
            f"Device token updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "device_id": str(device_id)
            }
        )

        return DeviceTokenResponse.model_validate(updated_device)

    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"Invalid device update data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid device update data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Update device token failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update device token"
        )


@router.delete(
    "/devices/{device_id}",
    response_model=MessageResponse,
    summary="Deactivate device token",
    description="Deactivate a device token (soft delete)"
)
async def deactivate_device_token(
    device_id: UUID = Path(..., description="Device token ID"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Deactivate a device token (soft delete).

    This endpoint deactivates a device token, preventing it from receiving
    push notifications. The token is not permanently deleted for audit purposes.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Deactivate device token request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "device_id": str(device_id)
        }
    )

    try:
        device_service = DeviceTokenService(db)
        success = await device_service.deactivate_device(device_id, current_user.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device token not found or access denied"
            )

        logger.info(
            f"Device token deactivated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "device_id": str(device_id)
            }
        )

        return MessageResponse(message="Device token deactivated successfully")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Deactivate device token failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate device token"
        )


# ============================================================================
# NOTIFICATION TEMPLATE MANAGEMENT ENDPOINTS
# ============================================================================

@router.post(
    "/templates",
    response_model=NotificationTemplateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create notification template",
    description="Create a new notification template"
)
async def create_notification_template(
    template_data: NotificationTemplateCreate,
    current_user: UserResponse = Depends(require_permission("notifications:templates:create")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationTemplateResponse:
    """
    Create a new notification template.

    This endpoint allows authorized users to create notification templates
    with Jinja2 syntax for dynamic content rendering.

    **Required Permission**: notifications:templates:create
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Create notification template request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "template_name": template_data.name
        }
    )

    try:
        template_service = NotificationTemplateService(db)
        template = await template_service.create_template(template_data, current_user.id)

        logger.info(
            f"Notification template created successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_id": str(template.id),
                "template_name": template_data.name
            }
        )

        return template

    except ValueError as e:
        logger.warning(f"Invalid template data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid template data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Create notification template failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create notification template"
        )


@router.get(
    "/templates",
    response_model=NotificationTemplateListResponse,
    summary="Get notification templates",
    description="Get notification templates with filtering and pagination"
)
async def get_notification_templates(
    category: Optional[NotificationCategory] = Query(None, description="Filter by category"),
    active_only: bool = Query(True, description="Return only active templates"),
    pagination: PaginationMixin = Depends(),
    current_user: UserResponse = Depends(require_permission("notifications:templates:read")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationTemplateListResponse:
    """
    Get notification templates with filtering and pagination.

    This endpoint returns notification templates with optional filtering
    by category and active status.

    **Required Permission**: notifications:templates:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get notification templates request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "category": category.value if category else None,
            "active_only": active_only,
            "page": pagination.page,
            "size": pagination.size
        }
    )

    try:
        template_service = NotificationTemplateService(db)

        if category:
            templates = await template_service.get_templates_by_category(category, active_only)
            # Apply pagination manually for category filtering
            start_idx = pagination.offset
            end_idx = start_idx + pagination.size
            paginated_templates = templates[start_idx:end_idx]
            total = len(templates)
        else:
            # Get all templates with pagination
            result = await template_service.get_paginated(pagination)
            paginated_templates = [NotificationTemplateResponse.model_validate(t) for t in result.items]
            total = result.total

        logger.info(
            f"Retrieved {len(paginated_templates)} notification templates",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_count": len(paginated_templates),
                "total": total
            }
        )

        return NotificationTemplateListResponse(
            items=paginated_templates,
            total=total,
            page=pagination.page,
            per_page=pagination.size,
            pages=(total + pagination.size - 1) // pagination.size
        )

    except Exception as e:
        logger.error(f"Get notification templates failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notification templates"
        )


@router.get(
    "/templates/{template_id}",
    response_model=NotificationTemplateResponse,
    summary="Get notification template",
    description="Get a specific notification template by ID"
)
async def get_notification_template(
    template_id: UUID = Path(..., description="Template ID"),
    current_user: UserResponse = Depends(require_permission("notifications:templates:read")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationTemplateResponse:
    """
    Get a specific notification template by ID.

    **Required Permission**: notifications:templates:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get notification template request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "template_id": str(template_id)
        }
    )

    try:
        template_service = NotificationTemplateService(db)
        template = await template_service.get_by_id(template_id)

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification template not found"
            )

        logger.info(
            f"Notification template retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return NotificationTemplateResponse.model_validate(template)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get notification template failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notification template"
        )


@router.put(
    "/templates/{template_id}",
    response_model=NotificationTemplateResponse,
    summary="Update notification template",
    description="Update a notification template"
)
async def update_notification_template(
    template_id: UUID = Path(..., description="Template ID"),
    template_data: NotificationTemplateUpdate = ...,
    current_user: UserResponse = Depends(require_permission("notifications:templates:update")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationTemplateResponse:
    """
    Update a notification template.

    **Required Permission**: notifications:templates:update
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update notification template request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "template_id": str(template_id)
        }
    )

    try:
        template_service = NotificationTemplateService(db)

        # Verify template exists
        existing_template = await template_service.get_by_id(template_id)
        if not existing_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification template not found"
            )

        # Update template
        update_data = template_data.model_dump(exclude_unset=True)
        updated_template = await template_service.update(template_id, update_data)

        logger.info(
            f"Notification template updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return NotificationTemplateResponse.model_validate(updated_template)

    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"Invalid template update data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid template update data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Update notification template failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification template"
        )


@router.delete(
    "/templates/{template_id}",
    response_model=MessageResponse,
    summary="Delete notification template",
    description="Delete a notification template (soft delete)"
)
async def delete_notification_template(
    template_id: UUID = Path(..., description="Template ID"),
    current_user: UserResponse = Depends(require_permission("notifications:templates:delete")),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Delete a notification template (soft delete).

    **Required Permission**: notifications:templates:delete
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Delete notification template request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "template_id": str(template_id)
        }
    )

    try:
        template_service = NotificationTemplateService(db)
        success = await template_service.delete(template_id, soft_delete=True)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification template not found"
            )

        logger.info(
            f"Notification template deleted successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return MessageResponse(message="Notification template deleted successfully")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete notification template failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete notification template"
        )


@router.post(
    "/templates/{template_id}/render",
    response_model=Dict[str, str],
    summary="Render notification template",
    description="Test render a notification template with variables"
)
async def render_notification_template(
    template_id: UUID = Path(..., description="Template ID"),
    variables: Dict[str, Any] = ...,
    current_user: UserResponse = Depends(require_permission("notifications:templates:read")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, str]:
    """
    Test render a notification template with variables.

    This endpoint allows testing template rendering with provided variables
    without actually sending notifications.

    **Required Permission**: notifications:templates:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Render notification template request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "template_id": str(template_id)
        }
    )

    try:
        template_service = NotificationTemplateService(db)
        rendered = await template_service.render_template(template_id, variables)

        logger.info(
            f"Notification template rendered successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return rendered

    except ValueError as e:
        logger.warning(f"Template rendering failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Template rendering failed: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Render notification template failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to render notification template"
        )


# ============================================================================
# NOTIFICATION SENDING ENDPOINTS
# ============================================================================

@router.post(
    "/notifications/send",
    response_model=NotificationSendResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send push notification",
    description="Send a push notification to specific devices or all user devices"
)
async def send_push_notification(
    notification_request: NotificationSendRequest,
    current_user: UserResponse = Depends(require_permission("notifications:send")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationSendResponse:
    """
    Send a push notification to specific devices or all user devices.

    This endpoint allows sending push notifications with template support,
    scheduling, and comprehensive delivery tracking.

    **Required Permission**: notifications:send
    **Rate Limiting**: 1000 notifications per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Send push notification request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "target_user_id": notification_request.user_id,
            "device_count": len(notification_request.device_token_ids) if notification_request.device_token_ids else 0,
            "template_id": str(notification_request.template_id) if notification_request.template_id else None,
            "scheduled": notification_request.scheduled_at is not None
        }
    )

    try:
        push_service = PushNotificationService(db)
        response = await push_service.send_notification(notification_request)

        logger.info(
            f"Push notification sent successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "total_sent": response.total_sent,
                "failed_count": response.failed_count,
                "scheduled_count": response.scheduled_count
            }
        )

        return response

    except ValueError as e:
        logger.warning(f"Invalid notification request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid notification request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Send push notification failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send push notification"
        )


@router.post(
    "/notifications/send-batch",
    response_model=NotificationBatchSendResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send batch push notifications",
    description="Send multiple push notifications in a single request"
)
async def send_batch_push_notifications(
    batch_request: NotificationBatchSendRequest,
    current_user: UserResponse = Depends(require_permission("notifications:send")),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationBatchSendResponse:
    """
    Send multiple push notifications in a single request.

    This endpoint allows sending multiple notifications efficiently
    with comprehensive error handling and delivery tracking.

    **Required Permission**: notifications:send
    **Rate Limiting**: 1000 notifications per hour per user (total across all notifications in batch)
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Send batch push notifications request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "batch_size": len(batch_request.notifications)
        }
    )

    try:
        push_service = PushNotificationService(db)
        response = await push_service.send_batch_notifications(batch_request)

        logger.info(
            f"Batch push notifications sent successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "total_requested": response.total_requested,
                "total_sent": response.total_sent,
                "total_failed": response.total_failed,
                "total_scheduled": response.total_scheduled
            }
        )

        return response

    except ValueError as e:
        logger.warning(f"Invalid batch notification request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid batch notification request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Send batch push notifications failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send batch push notifications"
        )


# ============================================================================
# NOTIFICATION PREFERENCE ENDPOINTS
# ============================================================================

@router.get(
    "/preferences",
    response_model=NotificationPreferenceResponse,
    summary="Get notification preferences",
    description="Get current user's notification preferences"
)
async def get_notification_preferences(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationPreferenceResponse:
    """
    Get current user's notification preferences.

    This endpoint returns the user's notification preferences including
    category-specific settings and DND scheduling.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get notification preferences request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        preference_service = NotificationPreferenceService(db)
        preferences = await preference_service.get_user_preferences(current_user.id)

        logger.info(
            f"Notification preferences retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id
            }
        )

        return preferences

    except Exception as e:
        logger.error(f"Get notification preferences failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notification preferences"
        )


@router.put(
    "/preferences",
    response_model=NotificationPreferenceResponse,
    summary="Update notification preferences",
    description="Update current user's notification preferences"
)
async def update_notification_preferences(
    preference_data: NotificationPreferenceUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> NotificationPreferenceResponse:
    """
    Update current user's notification preferences.

    This endpoint allows users to update their notification preferences
    including category-specific settings and DND scheduling.
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update notification preferences request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        preference_service = NotificationPreferenceService(db)
        preferences = await preference_service.update_user_preferences(current_user.id, preference_data)

        logger.info(
            f"Notification preferences updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id
            }
        )

        return preferences

    except ValueError as e:
        logger.warning(f"Invalid preference data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid preference data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Update notification preferences failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification preferences"
        )


# ============================================================================
# NOTIFICATION ANALYTICS ENDPOINTS
# ============================================================================

@router.get(
    "/analytics/deliveries",
    response_model=Dict[str, Any],
    summary="Get delivery analytics",
    description="Get notification delivery analytics and statistics"
)
async def get_delivery_analytics(
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    current_user: UserResponse = Depends(require_permission("notifications:analytics:read")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get notification delivery analytics and statistics.

    This endpoint provides comprehensive analytics on notification delivery
    performance, success rates, and failure analysis.

    **Required Permission**: notifications:analytics:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get delivery analytics request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "start_date": start_date,
            "end_date": end_date
        }
    )

    try:
        from datetime import datetime

        # Parse dates if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            parsed_start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            parsed_end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))

        delivery_service = NotificationDeliveryService(db)
        analytics = await delivery_service.get_delivery_analytics(parsed_start_date, parsed_end_date)

        logger.info(
            f"Delivery analytics retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id
            }
        )

        return analytics

    except ValueError as e:
        logger.warning(f"Invalid date format: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid date format: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Get delivery analytics failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve delivery analytics"
        )


@router.get(
    "/analytics/comprehensive",
    response_model=Dict[str, Any],
    summary="Get comprehensive analytics",
    description="Get comprehensive notification analytics including delivery, queue, and device statistics"
)
async def get_comprehensive_analytics(
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    current_user: UserResponse = Depends(require_permission("notifications:analytics:read")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get comprehensive notification analytics.

    This endpoint provides comprehensive analytics combining delivery,
    queue, and device statistics for complete system monitoring.

    **Required Permission**: notifications:analytics:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get comprehensive analytics request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "start_date": start_date,
            "end_date": end_date
        }
    )

    try:
        from datetime import datetime

        # Parse dates if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            parsed_start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            parsed_end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))

        push_service = PushNotificationService(db)
        analytics = await push_service.get_notification_analytics(parsed_start_date, parsed_end_date)

        logger.info(
            f"Comprehensive analytics retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id
            }
        )

        return analytics

    except ValueError as e:
        logger.warning(f"Invalid date format: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid date format: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Get comprehensive analytics failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve comprehensive analytics"
        )


# ============================================================================
# QUEUE MANAGEMENT ENDPOINTS
# ============================================================================

@router.post(
    "/queue/process",
    response_model=Dict[str, Any],
    summary="Process notification queue",
    description="Trigger processing of pending notifications in the queue"
)
async def process_notification_queue(
    batch_size: int = Query(100, ge=1, le=1000, description="Maximum batch size to process"),
    current_user: UserResponse = Depends(require_permission("notifications:queue:manage")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Trigger processing of pending notifications in the queue.

    This endpoint manually triggers queue processing for pending notifications.
    Typically used for administrative purposes or manual queue management.

    **Required Permission**: notifications:queue:manage
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Process notification queue request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "batch_size": batch_size
        }
    )

    try:
        push_service = PushNotificationService(db)
        result = await push_service.process_notification_queue(batch_size)

        logger.info(
            f"Notification queue processed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "processed_count": result["processed_count"],
                "success_count": result["success_count"],
                "failed_count": result["failed_count"]
            }
        )

        return result

    except Exception as e:
        logger.error(f"Process notification queue failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process notification queue"
        )


@router.get(
    "/queue/status",
    response_model=Dict[str, Any],
    summary="Get queue status",
    description="Get current notification queue status and statistics"
)
async def get_queue_status(
    current_user: UserResponse = Depends(require_permission("notifications:queue:read")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get current notification queue status and statistics.

    This endpoint provides real-time queue status including pending
    notifications, processing statistics, and performance metrics.

    **Required Permission**: notifications:queue:read
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get queue status request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        queue_service = NotificationQueueService(db)
        status_data = await queue_service.get_queue_statistics()

        logger.info(
            f"Queue status retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id
            }
        )

        return status_data

    except Exception as e:
        logger.error(f"Get queue status failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve queue status"
        )


@router.post(
    "/queue/cleanup",
    response_model=Dict[str, Any],
    summary="Cleanup expired notifications",
    description="Clean up expired notifications from the queue"
)
async def cleanup_expired_notifications(
    current_user: UserResponse = Depends(require_permission("notifications:queue:manage")),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Clean up expired notifications from the queue.

    This endpoint removes expired notifications from the queue to maintain
    optimal performance and storage efficiency.

    **Required Permission**: notifications:queue:manage
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Cleanup expired notifications request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        queue_service = NotificationQueueService(db)
        cleaned_count = await queue_service.cleanup_expired_notifications()

        logger.info(
            f"Expired notifications cleaned up successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "cleaned_count": cleaned_count
            }
        )

        return {
            "message": "Expired notifications cleaned up successfully",
            "cleaned_count": cleaned_count
        }

    except Exception as e:
        logger.error(f"Cleanup expired notifications failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup expired notifications"
        )
