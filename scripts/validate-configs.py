#!/usr/bin/env python3
"""
Culture Connect Backend - Configuration Validation Script
Validates Docker configurations, environment files, and deployment readiness
without requiring <PERSON><PERSON> to be running.
"""

import os
import sys
import yaml
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

class ConfigValidator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.total_checks = 0
        self.passed_checks = 0
        self.failed_checks = 0
        self.warning_checks = 0
        
    def log_info(self, message: str):
        print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")
        
    def log_success(self, message: str):
        print(f"{Colors.GREEN}[PASS]{Colors.NC} {message}")
        self.passed_checks += 1
        
    def log_warning(self, message: str):
        print(f"{Colors.YELLOW}[WARN]{Colors.NC} {message}")
        self.warning_checks += 1
        
    def log_error(self, message: str):
        print(f"{Colors.RED}[FAIL]{Colors.NC} {message}")
        self.failed_checks += 1
        
    def check(self, description: str, condition: bool, error_msg: str = ""):
        self.total_checks += 1
        self.log_info(f"Checking: {description}")
        
        if condition:
            self.log_success(description)
            return True
        else:
            self.log_error(f"{description} - {error_msg}")
            return False
            
    def check_warn(self, description: str, condition: bool, warning_msg: str = ""):
        self.total_checks += 1
        self.log_info(f"Checking: {description}")
        
        if condition:
            self.log_success(description)
            return True
        else:
            self.log_warning(f"{description} - {warning_msg}")
            return False

    def validate_yaml_file(self, file_path: Path) -> Tuple[bool, str]:
        """Validate YAML file syntax"""
        try:
            with open(file_path, 'r') as f:
                yaml.safe_load(f)
            return True, ""
        except yaml.YAMLError as e:
            return False, str(e)
        except FileNotFoundError:
            return False, "File not found"
        except Exception as e:
            return False, str(e)

    def validate_docker_compose_files(self):
        """Validate Docker Compose file syntax and structure"""
        self.log_info("=== Docker Compose Files Validation ===")
        
        compose_files = [
            'docker-compose.yml',
            'docker-compose.prod.yml',
            'docker-compose.swarm.yml'
        ]
        
        for file_name in compose_files:
            file_path = self.project_root / file_name
            
            # Check file exists
            exists = file_path.exists()
            self.check(f"{file_name} exists", exists, f"File not found: {file_path}")
            
            if exists:
                # Validate YAML syntax
                is_valid, error = self.validate_yaml_file(file_path)
                self.check(f"{file_name} has valid YAML syntax", is_valid, error)
                
                if is_valid:
                    # Load and validate structure
                    with open(file_path, 'r') as f:
                        compose_data = yaml.safe_load(f)
                    
                    # Check version
                    has_version = 'version' in compose_data
                    self.check(f"{file_name} has version specified", has_version)
                    
                    # Check services
                    has_services = 'services' in compose_data
                    self.check(f"{file_name} has services section", has_services)
                    
                    if has_services:
                        services = compose_data['services']
                        
                        # Check for required services in production file
                        if file_name == 'docker-compose.prod.yml':
                            required_services = ['api', 'db', 'redis']
                            for service in required_services:
                                has_service = service in services
                                self.check(f"{file_name} has {service} service", has_service)
                                
                                if has_service and service == 'api':
                                    api_service = services[service]
                                    
                                    # Check health checks
                                    has_healthcheck = 'healthcheck' in api_service
                                    self.check(f"API service has health check", has_healthcheck)
                                    
                                    # Check resource limits
                                    has_deploy = 'deploy' in api_service
                                    if has_deploy:
                                        deploy_config = api_service['deploy']
                                        has_resources = 'resources' in deploy_config
                                        self.check(f"API service has resource limits", has_resources)

    def validate_dockerfile(self):
        """Validate Dockerfile structure and best practices"""
        self.log_info("=== Dockerfile Validation ===")
        
        dockerfile_path = self.project_root / 'Dockerfile'
        
        # Check file exists
        exists = dockerfile_path.exists()
        self.check("Dockerfile exists", exists)
        
        if exists:
            with open(dockerfile_path, 'r') as f:
                dockerfile_content = f.read()
            
            # Check multi-stage build
            has_builder_stage = 'FROM python:3.11-slim as builder' in dockerfile_content
            self.check("Dockerfile has builder stage", has_builder_stage)
            
            has_production_stage = 'FROM python:3.11-slim as production' in dockerfile_content
            self.check("Dockerfile has production stage", has_production_stage)
            
            # Check security practices
            has_non_root_user = 'USER appuser' in dockerfile_content
            self.check("Dockerfile uses non-root user", has_non_root_user)
            
            has_healthcheck = 'HEALTHCHECK' in dockerfile_content
            self.check("Dockerfile has health check", has_healthcheck)
            
            # Check labels
            has_labels = 'LABEL maintainer' in dockerfile_content
            self.check("Dockerfile has metadata labels", has_labels)
            
            # Check security updates
            has_security_updates = 'apt-get upgrade' in dockerfile_content
            self.check("Dockerfile includes security updates", has_security_updates)

    def validate_environment_files(self):
        """Validate environment configuration files"""
        self.log_info("=== Environment Files Validation ===")
        
        env_files = [
            '.env.production',
            '.env.staging'
        ]
        
        for env_file in env_files:
            file_path = self.project_root / env_file
            
            # Check file exists
            exists = file_path.exists()
            self.check(f"{env_file} exists", exists)
            
            if exists:
                with open(file_path, 'r') as f:
                    env_content = f.read()
                
                # Check critical environment variables
                critical_vars = [
                    'CC_ENVIRONMENT',
                    'CC_SECRET_KEY',
                    'CC_DATABASE_URL',
                    'CC_REDIS_URL',
                    'CC_POSTGRES_PASSWORD',
                    'CC_REDIS_PASSWORD'
                ]
                
                for var in critical_vars:
                    has_var = f'{var}=' in env_content
                    self.check(f"{env_file} has {var}", has_var)
                
                # Check for default/insecure values
                has_secure_secret = 'your-secret-key-here' not in env_content
                self.check(f"{env_file} doesn't use default secret key", has_secure_secret)
                
                # Check payment provider configuration
                payment_vars = [
                    'CC_PAYSTACK_SECRET_KEY',
                    'CC_STRIPE_SECRET_KEY'
                ]
                
                for var in payment_vars:
                    has_var = f'{var}=' in env_content
                    self.check_warn(f"{env_file} has {var}", has_var, "Payment provider not configured")

    def validate_scripts(self):
        """Validate deployment and utility scripts"""
        self.log_info("=== Scripts Validation ===")
        
        scripts = [
            'scripts/deploy-production.sh',
            'scripts/validate-production.sh',
            'scripts/postgres-prod-config.sql'
        ]
        
        for script in scripts:
            script_path = self.project_root / script
            
            # Check file exists
            exists = script_path.exists()
            self.check(f"{script} exists", exists)
            
            if exists and script.endswith('.sh'):
                # Check if executable
                is_executable = os.access(script_path, os.X_OK)
                self.check(f"{script} is executable", is_executable)
                
                # Check shebang
                with open(script_path, 'r') as f:
                    first_line = f.readline().strip()
                
                has_shebang = first_line.startswith('#!/bin/bash')
                self.check(f"{script} has proper shebang", has_shebang)

    def validate_logging_config(self):
        """Validate logging configuration"""
        self.log_info("=== Logging Configuration Validation ===")
        
        logging_config_path = self.project_root / 'logging.conf'
        
        # Check file exists
        exists = logging_config_path.exists()
        self.check("logging.conf exists", exists)
        
        if exists:
            with open(logging_config_path, 'r') as f:
                logging_content = f.read()
            
            # Check required sections
            required_sections = ['loggers', 'handlers', 'formatters']
            for section in required_sections:
                has_section = f'[{section}]' in logging_content
                self.check(f"logging.conf has {section} section", has_section)
            
            # Check for JSON formatter
            has_json_formatter = 'formatter_json' in logging_content
            self.check("logging.conf has JSON formatter", has_json_formatter)
            
            # Check for file rotation
            has_rotation = 'RotatingFileHandler' in logging_content
            self.check("logging.conf has file rotation", has_rotation)

    def validate_application_structure(self):
        """Validate application directory structure"""
        self.log_info("=== Application Structure Validation ===")
        
        required_dirs = [
            'app',
            'app/api',
            'app/core',
            'app/models',
            'app/services',
            'app/repositories',
            'app/schemas'
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            exists = full_path.exists() and full_path.is_dir()
            self.check(f"{dir_path} directory exists", exists)
        
        # Check main application file
        main_file = self.project_root / 'app' / 'main.py'
        exists = main_file.exists()
        self.check("app/main.py exists", exists)
        
        # Check requirements file
        requirements_file = self.project_root / 'requirements.txt'
        exists = requirements_file.exists()
        self.check("requirements.txt exists", exists)

    def generate_summary(self):
        """Generate validation summary"""
        self.log_info("=== Validation Summary ===")
        
        print()
        print(f"Total Checks: {self.total_checks}")
        print(f"{Colors.GREEN}Passed: {self.passed_checks}{Colors.NC}")
        print(f"{Colors.YELLOW}Warnings: {self.warning_checks}{Colors.NC}")
        print(f"{Colors.RED}Failed: {self.failed_checks}{Colors.NC}")
        print()
        
        if self.total_checks > 0:
            success_rate = (self.passed_checks * 100) // self.total_checks
            
            if self.failed_checks == 0:
                self.log_success(f"Configuration validation completed successfully! Success rate: {success_rate}%")
                if self.warning_checks > 0:
                    self.log_warning(f"There are {self.warning_checks} warnings that should be addressed")
                return True
            else:
                self.log_error(f"Configuration validation failed! {self.failed_checks} critical issues must be resolved")
                return False
        else:
            self.log_error("No validation checks were performed")
            return False

    def run_all_validations(self):
        """Run all validation checks"""
        self.log_info("Starting Culture Connect Backend configuration validation")
        self.log_info(f"Project root: {self.project_root}")
        print()
        
        self.validate_application_structure()
        print()
        self.validate_docker_compose_files()
        print()
        self.validate_dockerfile()
        print()
        self.validate_environment_files()
        print()
        self.validate_scripts()
        print()
        self.validate_logging_config()
        print()
        
        return self.generate_summary()

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    validator = ConfigValidator(project_root)
    success = validator.run_all_validations()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
