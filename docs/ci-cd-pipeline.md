# Culture Connect Backend - CI/CD Pipeline Documentation

## Overview

The Culture Connect Backend uses a comprehensive CI/CD pipeline built with GitHub Actions to automate building, testing, security scanning, and deployment across multiple environments. The pipeline supports three deployment strategies and includes robust security scanning and quality gates.

## Pipeline Architecture

### 🔄 **Main CI/CD Pipeline** (`.github/workflows/ci-cd-pipeline.yml`)

**Triggers:**
- Push to `main`, `develop`, `staging` branches
- Pull requests to `main`, `develop`
- Release publications

**Stages:**
1. **Code Quality & Security** - Linting, formatting, security scanning
2. **Automated Testing** - Unit tests with PostgreSQL and Redis services
3. **Docker Build & Security Scan** - Multi-stage build with Trivy scanning
4. **Environment Deployments** - Automated deployment to development, staging, production

### 🔒 **Security Scanning Pipeline** (`.github/workflows/security-scan.yml`)

**Triggers:**
- Push to main branches
- Pull requests
- Daily scheduled scans (2 AM UTC)

**Security Scans:**
- **Code Security**: Bandit, Safety, Semgrep, CodeQL
- **Dependency Scanning**: pip-audit, SBOM generation
- **Container Security**: Trivy vulnerability scanning
- **Infrastructure Security**: Checkov for IaC scanning

### 🚀 **Environment-Specific Deployments**

#### Development (`.github/workflows/deploy-development.yml`)
- **Trigger**: Push to `develop` branch
- **Strategy**: Docker Compose
- **Features**: Automated deployment, smoke tests, integration tests
- **Target**: Local development environment

#### Staging (`.github/workflows/deploy-staging.yml`)
- **Trigger**: Push to `staging` branch
- **Strategy**: Docker Swarm
- **Features**: Pre-deployment validation, integration tests, load testing
- **Target**: Staging server environment

#### Production (`.github/workflows/deploy-production.yml`)
- **Trigger**: Push to `main` branch, releases, manual dispatch
- **Strategy**: Docker Compose (Production)
- **Features**: Manual approval gates, backup creation, comprehensive validation
- **Target**: Production server environment

## File Structure

```
.github/workflows/
├── ci-cd-pipeline.yml          # Main CI/CD pipeline
├── security-scan.yml           # Security scanning workflow
├── deploy-development.yml      # Development deployment
├── deploy-staging.yml          # Staging deployment
└── deploy-production.yml       # Production deployment

environments/
├── development/
│   └── .env.development        # Development configuration
├── staging/
│   └── .env.staging           # Staging configuration
└── production/
    └── .env.production        # Production configuration

docker/
├── development/
│   └── docker-compose.yml     # Development Docker setup
├── staging/
│   └── docker-compose.swarm.yml  # Staging Docker Swarm
└── production/
    └── docker-compose.prod.yml   # Production Docker setup

scripts/deployment/
├── development/               # Development deployment scripts
├── staging/                  # Staging deployment scripts
└── production/               # Production deployment scripts
    ├── deploy-production.sh  # Production deployment script
    └── validate-production.sh # Production validation script
```

## Environment Configuration

### Development Environment
- **Purpose**: Local development and testing
- **Configuration**: `environments/development/.env.development`
- **Features**: Debug mode, test payment providers, local services
- **Deployment**: Automatic on push to `develop`

### Staging Environment
- **Purpose**: Pre-production testing and validation
- **Configuration**: `environments/staging/.env.staging`
- **Features**: Production-like setup, test payment providers, staging domains
- **Deployment**: Automatic on push to `staging`

### Production Environment
- **Purpose**: Live production environment
- **Configuration**: `environments/production/.env.production`
- **Features**: Live payment providers, production domains, monitoring
- **Deployment**: Manual approval required

## Security Features

### Code Security Scanning
- **Bandit**: Python security linter
- **Safety**: Dependency vulnerability scanner
- **Semgrep**: Static analysis security scanner
- **CodeQL**: GitHub's semantic code analysis

### Container Security
- **Trivy**: Comprehensive vulnerability scanner
- **Docker Scout**: Container security analysis
- **Multi-stage builds**: Minimal attack surface
- **Non-root containers**: Security hardening

### Infrastructure Security
- **Checkov**: Infrastructure as Code security scanner
- **Configuration validation**: Docker Compose validation
- **Secret management**: Environment-based secret injection
- **Network security**: Isolated container networks

## Quality Gates

### Code Quality Requirements
- ✅ **Black formatting**: Code must be properly formatted
- ✅ **Import sorting**: Imports must be sorted with isort
- ✅ **Linting**: No critical Flake8 violations
- ✅ **Security**: No high-severity security issues

### Testing Requirements
- ✅ **Unit tests**: >80% code coverage required
- ✅ **Integration tests**: All critical paths tested
- ✅ **Database tests**: PostgreSQL integration validated
- ✅ **Cache tests**: Redis integration validated

### Deployment Requirements
- ✅ **Health checks**: All services must be healthy
- ✅ **Smoke tests**: Critical endpoints must respond
- ✅ **Migration validation**: Database migrations must succeed
- ✅ **Performance validation**: Response times within SLA

## Deployment Strategies

### 1. Docker Compose (Development & Production)
```bash
# Development
docker-compose -f docker/development/docker-compose.yml up -d

# Production
docker-compose -f docker/production/docker-compose.prod.yml up -d
```

### 2. Docker Swarm (Staging)
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker/staging/docker-compose.swarm.yml culture-connect
```

### 3. Kubernetes (Future)
- Helm charts for Kubernetes deployment
- Auto-scaling with HPA
- Service mesh integration

## Manual Operations

### Manual Deployment
```bash
# Development
.github/workflows/deploy-development.yml (workflow_dispatch)

# Staging
.github/workflows/deploy-staging.yml (workflow_dispatch)

# Production
.github/workflows/deploy-production.yml (workflow_dispatch)
```

### Rollback Procedures
```bash
# Production rollback
./scripts/deployment/production/deploy-production.sh rollback current_version previous_version
```

### Emergency Procedures
```bash
# Skip approval for emergency deployment
# Use workflow_dispatch with skip_approval: true
```

## Monitoring and Observability

### Pipeline Monitoring
- **GitHub Actions**: Workflow status and logs
- **Artifact storage**: Test results, security reports, deployment logs
- **Notifications**: Slack integration for deployment status

### Application Monitoring
- **Health checks**: `/health`, `/health/db`, `/health/redis`
- **Metrics**: Prometheus metrics endpoint
- **Logging**: Structured JSON logging with correlation IDs
- **Error tracking**: Sentry integration

## Secrets Management

### Required Secrets
```bash
# GitHub Repository Secrets
GITHUB_TOKEN                    # GitHub Actions token
PRODUCTION_APPROVERS            # Production deployment approvers
SLACK_WEBHOOK_URL              # Slack notifications

# Environment-specific secrets (in environment files)
CC_SECRET_KEY                  # Application secret key
CC_DATABASE_URL                # Database connection string
CC_REDIS_URL                   # Redis connection string
CC_PAYSTACK_SECRET_KEY         # Paystack payment provider
CC_STRIPE_SECRET_KEY           # Stripe payment provider
CC_SENTRY_DSN                  # Sentry error tracking
```

### Secret Rotation
1. Update secrets in environment files
2. Deploy to staging for validation
3. Deploy to production with approval
4. Verify all services are working

## Troubleshooting

### Common Issues

#### Pipeline Failures
```bash
# Check workflow logs in GitHub Actions
# Review failed step details
# Check artifact uploads for detailed reports
```

#### Deployment Failures
```bash
# Check container logs
docker-compose logs

# Validate configuration
./scripts/deployment/production/validate-production.sh

# Check service health
curl http://localhost:8000/health
```

#### Security Scan Failures
```bash
# Review security scan artifacts
# Update dependencies with vulnerabilities
# Fix code security issues
# Update container base images
```

### Recovery Procedures

#### Failed Deployment
1. Check deployment logs and artifacts
2. Identify root cause
3. Fix issues in code or configuration
4. Redeploy or rollback as needed

#### Security Vulnerabilities
1. Review security scan reports
2. Update affected dependencies
3. Apply security patches
4. Re-run security scans
5. Deploy fixes through normal pipeline

## Best Practices

### Development Workflow
1. Create feature branch from `develop`
2. Implement changes with tests
3. Create pull request to `develop`
4. Automated testing and security scanning
5. Merge to `develop` triggers development deployment
6. Promote to `staging` for integration testing
7. Promote to `main` for production deployment

### Security Best Practices
1. Regular dependency updates
2. Security scan monitoring
3. Secret rotation schedule
4. Access control review
5. Vulnerability response procedures

### Deployment Best Practices
1. Always test in staging first
2. Use manual approval for production
3. Monitor deployments closely
4. Have rollback plan ready
5. Document all changes

## Support and Maintenance

### Regular Maintenance
- **Weekly**: Review security scan results
- **Monthly**: Update dependencies and base images
- **Quarterly**: Review and update pipeline configuration

### Support Contacts
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Emergency**: +234-XXX-XXX-XXXX
