#!/bin/bash
# scripts/setup_geoip.sh - MaxMind GeoLite2-Country database setup script
#
# This script handles the initial setup of MaxMind GeoLite2-Country database
# for the Culture Connect Backend geolocation enhancement system.
#
# Features:
# - Secure MaxMind license key validation
# - Database download with integrity verification
# - Backup mechanisms and error handling
# - Integration with existing logging infrastructure
# - Production-grade error handling and recovery

set -e  # Exit on any error
set -u  # Exit on undefined variables

# Script configuration
SCRIPT_NAME="setup_geoip"
SCRIPT_VERSION="1.0.0"
LOG_FILE="logs/geoip/setup_geoip.log"
DATA_DIR="./data"
BACKUP_DIR="./data/backups"
GEOIP_DATABASE_FILE="GeoLite2-Country.mmdb"
DOWNLOAD_URL_BASE="https://download.maxmind.com/app/geoip_download"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Log to file
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    # Log to console with colors
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        *)
            echo "[$level] $message"
            ;;
    esac
}

# Error handling function
handle_error() {
    local exit_code=$?
    local line_number=$1
    log "ERROR" "Script failed at line $line_number with exit code $exit_code"
    log "ERROR" "MaxMind GeoIP database setup failed"
    exit $exit_code
}

# Set up error trap
trap 'handle_error $LINENO' ERR

# Banner function
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================================="
    echo "  Culture Connect Backend - MaxMind GeoIP Database Setup"
    echo "  Version: $SCRIPT_VERSION"
    echo "  Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================================="
    echo -e "${NC}"
}

# Validate environment and prerequisites
validate_environment() {
    log "INFO" "Validating environment and prerequisites..."
    
    # Check if MaxMind license key is set
    if [ -z "${MAXMIND_LICENSE_KEY:-}" ]; then
        log "ERROR" "MAXMIND_LICENSE_KEY environment variable not set"
        log "ERROR" "Please register at https://dev.maxmind.com/geoip/geolite2-free-geolocation-data"
        log "ERROR" "Set MAXMIND_LICENSE_KEY environment variable with your license key"
        exit 1
    fi
    
    # Validate license key format (basic validation)
    if [[ ! "$MAXMIND_LICENSE_KEY" =~ ^[A-Za-z0-9_]{16,}$ ]]; then
        log "WARN" "MaxMind license key format appears invalid (should be 16+ alphanumeric characters)"
    fi
    
    # Check required commands
    local required_commands=("curl" "tar" "python3")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log "ERROR" "Required command '$cmd' not found"
            exit 1
        fi
    done
    
    # Check disk space (require at least 100MB free)
    local available_space=$(df . | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 102400 ]; then  # 100MB in KB
        log "ERROR" "Insufficient disk space. At least 100MB required."
        exit 1
    fi
    
    log "INFO" "Environment validation completed successfully"
}

# Create directory structure
create_directories() {
    log "INFO" "Creating directory structure..."
    
    # Create required directories
    mkdir -p "$DATA_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Set appropriate permissions
    chmod 755 "$DATA_DIR"
    chmod 755 "$BACKUP_DIR"
    chmod 755 "$(dirname "$LOG_FILE")"
    
    log "INFO" "Directory structure created successfully"
}

# Backup existing database
backup_existing_database() {
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    if [ -f "$database_path" ]; then
        log "INFO" "Backing up existing GeoIP database..."
        
        local backup_filename="GeoLite2-Country_backup_$(date +%Y%m%d_%H%M%S).mmdb"
        local backup_path="$BACKUP_DIR/$backup_filename"
        
        cp "$database_path" "$backup_path"
        
        # Verify backup
        if [ -f "$backup_path" ]; then
            log "INFO" "Database backed up to: $backup_path"
        else
            log "ERROR" "Failed to create backup"
            exit 1
        fi
        
        # Clean up old backups (keep last 5)
        log "INFO" "Cleaning up old backups (keeping last 5)..."
        cd "$BACKUP_DIR"
        ls -t GeoLite2-Country_backup_*.mmdb 2>/dev/null | tail -n +6 | xargs -r rm -f
        cd - > /dev/null
    else
        log "INFO" "No existing database found, skipping backup"
    fi
}

# Download MaxMind database
download_database() {
    log "INFO" "Downloading MaxMind GeoLite2-Country database..."
    
    local download_url="${DOWNLOAD_URL_BASE}?edition_id=GeoLite2-Country&license_key=${MAXMIND_LICENSE_KEY}&suffix=tar.gz"
    local temp_file="$DATA_DIR/GeoLite2-Country.tar.gz"
    
    # Download with progress and retry logic
    local max_retries=3
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        log "INFO" "Download attempt $((retry_count + 1)) of $max_retries..."
        
        if curl -L \
            --connect-timeout 30 \
            --max-time 300 \
            --retry 2 \
            --retry-delay 5 \
            --fail \
            --show-error \
            --progress-bar \
            -o "$temp_file" \
            "$download_url"; then
            log "INFO" "Database downloaded successfully"
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log "WARN" "Download failed, retrying in 10 seconds..."
                sleep 10
            else
                log "ERROR" "Failed to download database after $max_retries attempts"
                exit 1
            fi
        fi
    done
    
    # Verify download
    if [ ! -f "$temp_file" ] || [ ! -s "$temp_file" ]; then
        log "ERROR" "Downloaded file is missing or empty"
        exit 1
    fi
    
    log "INFO" "Download verification completed"
}

# Extract database
extract_database() {
    log "INFO" "Extracting GeoIP database..."
    
    local temp_file="$DATA_DIR/GeoLite2-Country.tar.gz"
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    # Extract database file from tar.gz
    cd "$DATA_DIR"
    
    if tar -xzf "GeoLite2-Country.tar.gz" --strip-components=1 --wildcards "*/$GEOIP_DATABASE_FILE"; then
        log "INFO" "Database extracted successfully"
    else
        log "ERROR" "Failed to extract database"
        exit 1
    fi
    
    # Clean up temporary file
    rm -f "GeoLite2-Country.tar.gz"
    
    # Verify extracted database
    if [ -f "$database_path" ] && [ -s "$database_path" ]; then
        local file_size=$(stat -f%z "$database_path" 2>/dev/null || stat -c%s "$database_path" 2>/dev/null)
        log "INFO" "Database extracted: $database_path (${file_size} bytes)"
    else
        log "ERROR" "Extracted database file is missing or empty"
        exit 1
    fi
    
    cd - > /dev/null
}

# Verify database integrity
verify_database() {
    log "INFO" "Verifying database integrity..."
    
    # Use Python verification script
    if python3 scripts/verify_geoip.py; then
        log "INFO" "Database verification completed successfully"
    else
        log "ERROR" "Database verification failed"
        exit 1
    fi
}

# Set file permissions
set_permissions() {
    log "INFO" "Setting file permissions..."
    
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    # Set read-only permissions for database file
    chmod 644 "$database_path"
    
    # Set directory permissions
    chmod 755 "$DATA_DIR"
    
    log "INFO" "File permissions set successfully"
}

# Main execution function
main() {
    print_banner
    
    log "INFO" "Starting MaxMind GeoIP database setup..."
    log "INFO" "Script: $SCRIPT_NAME v$SCRIPT_VERSION"
    
    # Execute setup steps
    validate_environment
    create_directories
    backup_existing_database
    download_database
    extract_database
    verify_database
    set_permissions
    
    # Success message
    log "INFO" "MaxMind GeoIP database setup completed successfully!"
    log "INFO" "Database location: $DATA_DIR/$GEOIP_DATABASE_FILE"
    
    # Display database info
    if [ -f "$DATA_DIR/$GEOIP_DATABASE_FILE" ]; then
        local file_size=$(stat -f%z "$DATA_DIR/$GEOIP_DATABASE_FILE" 2>/dev/null || stat -c%s "$DATA_DIR/$GEOIP_DATABASE_FILE" 2>/dev/null)
        local file_date=$(stat -f%Sm "$DATA_DIR/$GEOIP_DATABASE_FILE" 2>/dev/null || stat -c%y "$DATA_DIR/$GEOIP_DATABASE_FILE" 2>/dev/null)
        
        echo -e "${GREEN}"
        echo "=================================================================="
        echo "  Setup Complete!"
        echo "  Database: $DATA_DIR/$GEOIP_DATABASE_FILE"
        echo "  Size: $file_size bytes"
        echo "  Date: $file_date"
        echo "=================================================================="
        echo -e "${NC}"
    fi
}

# Execute main function
main "$@"
