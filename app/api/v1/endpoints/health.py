"""
Health check endpoints for Culture Connect Backend API.

This module provides comprehensive health check and monitoring endpoints for:
- Application health status with detailed metrics
- Database connectivity and performance monitoring
- External service availability validation
- System resource metrics and diagnostics
- Geolocation service validation (MaxMind GeoIP)
- Payment provider routing health
- Circuit breaker status and performance metrics
- Kubernetes readiness and liveness probes

Implements Phase 2.1 API documentation enhancement with comprehensive response models,
detailed examples, and integration guides for production monitoring and Kubernetes deployment.

Health Check Endpoints:
- GET /health/ - Basic health status
- GET /health/detailed - Comprehensive system health with metrics
- GET /health/readiness - Kubernetes readiness probe
- GET /health/liveness - Kubernetes liveness probe
- GET /health/geolocation - Geolocation service health validation

Performance Targets:
- Basic health check: <50ms response time
- Detailed health check: <200ms response time
- Geolocation health check: <100ms response time
- Database connectivity: <50ms response time

Integration Examples:
- Kubernetes readiness probe: /health/readiness
- Kubernetes liveness probe: /health/liveness
- Load balancer health check: /health/
- Monitoring system integration: /health/detailed
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
import time
import psutil
import os
import asyncio
from datetime import datetime

from app.db.database import get_async_session
from app.core.config import settings
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.schemas.health import (
    BasicHealthResponse,
    DetailedHealthResponse,
    GeolocationHealthResponse,
    ReadinessResponse,
    LivenessResponse,
    HealthCheckError,
    HealthStatus,
    ServiceStatus
)

router = APIRouter()


@router.get("/", response_model=BasicHealthResponse)
async def health_check():
    """
    Basic health check endpoint for load balancers and monitoring systems.

    This endpoint provides a lightweight health status check suitable for:
    - Load balancer health checks
    - Basic monitoring system integration
    - Quick service availability validation

    Performance Target: <50ms response time

    Returns:
        BasicHealthResponse: Basic health status with service information

    Example Response:
        ```json
        {
            "status": "healthy",
            "timestamp": **********.123,
            "service": "Culture Connect Backend API",
            "version": "1.0.0",
            "environment": "production"
        }
        ```

    Status Values:
        - healthy: Service is operating normally
        - degraded: Service is operational but with reduced performance
        - unhealthy: Service is not functioning properly

    Integration Examples:
        - Load Balancer: Configure health check to GET /health/
        - Monitoring: Use for basic service availability monitoring
        - Uptime Monitoring: Simple endpoint for uptime validation
    """
    return BasicHealthResponse(
        status=HealthStatus.HEALTHY,
        timestamp=time.time(),
        service="Culture Connect Backend API",
        version="1.0.0",
        environment=settings.ENVIRONMENT
    )


@router.get("/detailed", response_model=DetailedHealthResponse)
async def detailed_health_check(db: AsyncSession = Depends(get_async_session)):
    """
    Comprehensive health check with database connectivity, system metrics, and external services.

    This endpoint provides detailed health information suitable for:
    - Comprehensive monitoring system integration
    - System administration and debugging
    - Performance analysis and optimization
    - Detailed service status reporting

    Performance Target: <200ms response time

    Args:
        db: Database session dependency for connectivity testing

    Returns:
        DetailedHealthResponse: Comprehensive health status with detailed metrics

    Raises:
        HTTPException: 503 Service Unavailable if critical services are down

    Example Response:
        ```json
        {
            "status": "healthy",
            "timestamp": **********.123,
            "service": "Culture Connect Backend API",
            "version": "1.0.0",
            "environment": "production",
            "checks": {
                "database": {
                    "status": "available",
                    "response_time_ms": 15.5,
                    "connection_pool_size": 10,
                    "active_connections": 3
                },
                "system": {
                    "cpu_percent": 25.5,
                    "memory_percent": 45.2,
                    "disk_percent": 60.8,
                    "load_average": [0.5, 0.7, 0.9]
                },
                "external_services": {
                    "paystack": {"status": "available", "response_time_ms": 120.0},
                    "redis": {"status": "available", "response_time_ms": 5.0},
                    "email_service": {"status": "available"}
                }
            }
        }
        ```

    Health Check Components:
        - Database: PostgreSQL connectivity and performance
        - System: CPU, memory, disk usage and load average
        - External Services: Payment providers, Redis, email service
        - Performance: Response times and resource utilization

    Integration Examples:
        - Monitoring Dashboard: Use for comprehensive system overview
        - Alerting System: Monitor individual component health
        - Performance Analysis: Track system resource utilization
        - Debugging: Detailed status for troubleshooting
    """
    health_data = {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "Culture Connect Backend API",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "checks": {}
    }

    # Database connectivity check
    try:
        from sqlalchemy import text
        await db.execute(text("SELECT 1"))
        health_data["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": 0  # Will be implemented with proper timing
        }
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_data["status"] = "unhealthy"

    # System metrics
    try:
        health_data["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    except Exception as e:
        health_data["checks"]["system"] = {
            "status": "unhealthy",
            "error": str(e)
        }

    # External services check (will be implemented as services are added)
    health_data["checks"]["external_services"] = {
        "paystack": {"status": "not_implemented"},
        "redis": {"status": "not_implemented"},
        "email_service": {"status": "not_implemented"}
    }

    if health_data["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_data
        )

    return health_data


@router.get("/readiness", response_model=ReadinessResponse)
async def readiness_check(db: AsyncSession = Depends(get_async_session)):
    """
    Kubernetes readiness probe endpoint for traffic routing decisions.

    This endpoint determines if the service is ready to accept traffic by validating:
    - Database connectivity and responsiveness
    - Critical service dependencies
    - Essential system resources

    Performance Target: <100ms response time

    Args:
        db: Database session dependency for connectivity validation

    Returns:
        ReadinessResponse: Service readiness status for traffic routing

    Raises:
        HTTPException: 503 Service Unavailable if service is not ready for traffic

    Example Response (Ready):
        ```json
        {
            "status": "ready",
            "timestamp": **********.123,
            "checks": {
                "database": "connected",
                "cache": "available",
                "geolocation": "operational"
            }
        }
        ```

    Example Response (Not Ready):
        ```json
        {
            "status": "not_ready",
            "timestamp": **********.123,
            "error": "Database connection failed",
            "checks": {
                "database": "disconnected",
                "cache": "available"
            }
        }
        ```

    Kubernetes Integration:
        ```yaml
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        ```

    Readiness Criteria:
        - Database: Must be connected and responsive
        - Cache: Redis must be available for session management
        - Configuration: Essential configuration must be loaded
        - Dependencies: Critical external services must be reachable

    Traffic Routing:
        - ready: Service can accept and process requests
        - not_ready: Service should not receive traffic

    Use Cases:
        - Load Balancer: Route traffic only to ready instances
        - Kubernetes: Control pod traffic routing during deployments
        - Auto-scaling: Ensure new instances are ready before receiving traffic
        - Rolling Updates: Graceful traffic migration during updates
    """
    try:
        # Check database connectivity
        from sqlalchemy import text
        await db.execute(text("SELECT 1"))

        return {
            "status": "ready",
            "timestamp": time.time(),
            "checks": {
                "database": "connected"
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "not_ready",
                "timestamp": time.time(),
                "error": str(e)
            }
        )


@router.get("/liveness", response_model=LivenessResponse)
async def liveness_check():
    """
    Kubernetes liveness probe endpoint for container restart decisions.

    This endpoint determines if the service process is alive and functioning by validating:
    - Application process responsiveness
    - Basic service functionality
    - Critical system resources availability

    Performance Target: <50ms response time

    Returns:
        LivenessResponse: Service liveness status for restart decisions

    Example Response (Alive):
        ```json
        {
            "status": "alive",
            "timestamp": **********.123,
            "uptime": 86400.0
        }
        ```

    Kubernetes Integration:
        ```yaml
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        ```

    Liveness Criteria:
        - Process: Application process must be running and responsive
        - Memory: Sufficient memory available for basic operations
        - Threading: Main application threads must be functional
        - Basic I/O: File system and network I/O must be operational

    Restart Decisions:
        - alive: Container is healthy and should continue running
        - dead: Container should be restarted by Kubernetes

    Failure Scenarios:
        - Application deadlock or infinite loop
        - Memory exhaustion preventing response
        - Critical thread failure
        - Unrecoverable application state

    Use Cases:
        - Kubernetes: Automatic container restart on failure
        - Process Monitoring: Detect and recover from application hangs
        - Health Monitoring: Basic service availability validation
        - Auto-recovery: Automated restart of failed instances

    Note: This probe should be lightweight and only check if the application
    process is alive. It should not validate external dependencies.
    """
    return LivenessResponse(
        status="alive",
        timestamp=time.time(),
        uptime=time.time()  # TODO: Implement proper uptime tracking
    )


@router.get("/geolocation", response_model=GeolocationHealthResponse)
async def check_geolocation_health():
    """
    Comprehensive geolocation service health check with MaxMind database validation.

    This endpoint provides detailed geolocation service health information including:
    - MaxMind GeoIP database connectivity and performance
    - Redis caching functionality and hit rates
    - Circuit breaker status and failure detection
    - End-to-end geolocation detection performance
    - Fallback mechanism validation

    Performance Target: <100ms response time

    Returns:
        GeolocationHealthResponse: Comprehensive geolocation service health status

    Raises:
        HTTPException: 503 Service Unavailable if geolocation service is unhealthy

    Example Response:
        ```json
        {
            "service": "geolocation",
            "status": "healthy",
            "timestamp": **********.123,
            "details": {
                "database": {"status": "healthy", "test_result": "US"},
                "cache": {"status": "healthy", "hit_rate": 0.90},
                "circuit_breaker": {"status": "healthy", "state": "closed"}
            },
            "performance": {
                "total_requests": 1500,
                "cache_hits": 1350,
                "cache_misses": 150,
                "cache_hit_rate": 0.90,
                "avg_detection_time_ms": 45.5,
                "circuit_breaker_trips": 0,
                "fallback_activations": 2
            },
            "database_status": {
                "database_path": "./data/GeoLite2-Country.mmdb",
                "database_available": true,
                "database_size_mb": 25.5,
                "last_updated": "2024-01-15T02:00:00Z",
                "cache_ttl_hours": 24.0
            },
            "circuit_breaker": {
                "state": "closed",
                "failure_count": 0,
                "failure_threshold": 10,
                "success_rate": 0.98,
                "last_failure_time": null,
                "recovery_timeout": 60
            }
        }
        ```

    Health Check Components:
        - MaxMind Database: Connectivity, file validation, and test detection
        - Performance Metrics: Detection times, cache performance, success rates
        - Circuit Breaker: State monitoring and failure detection
        - Cache System: Redis integration and hit rate validation
        - Fallback Mechanisms: Backup detection method validation

    Status Indicators:
        - healthy: All components functioning within performance targets
        - degraded: Service operational but with reduced performance
        - unhealthy: Critical components failing or performance below thresholds

    Performance Thresholds:
        - Detection Time: <100ms for cached results, <200ms for database lookups
        - Cache Hit Rate: >80% for optimal performance
        - Circuit Breaker: <5% failure rate for healthy status
        - Database Response: <50ms for MaxMind database queries

    Integration Examples:
        - Monitoring Dashboard: Real-time geolocation service status
        - Alerting System: Automated alerts for performance degradation
        - Performance Optimization: Identify bottlenecks and optimization opportunities
        - Capacity Planning: Monitor usage patterns and scaling requirements
    """
    correlation = correlation_id.get()
    health_status = {
        "service": "geolocation",
        "status": "healthy",
        "timestamp": time.time(),
        "details": {},
        "performance": {}
    }

    try:
        # Import geolocation service
        from app.services.geolocation_service import get_geolocation_service
        geolocation_service = get_geolocation_service()

        # Test 1: MaxMind database connectivity
        start_time = time.perf_counter()
        test_result = await geolocation_service.detect_country_from_ip("*******")
        detection_time_ms = (time.perf_counter() - start_time) * 1000

        # Test 2: Performance validation
        if detection_time_ms > 200:  # Warning threshold
            health_status["status"] = "degraded"
            health_status["details"]["performance_warning"] = f"Detection time: {detection_time_ms:.2f}ms"

        # Test 3: Cache functionality
        cache_start_time = time.perf_counter()
        cache_test_result = await geolocation_service.detect_country_from_ip("*******")
        cache_time_ms = (time.perf_counter() - cache_start_time) * 1000

        if test_result.country_code != cache_test_result.country_code:
            health_status["status"] = "unhealthy"
            health_status["details"]["cache_error"] = "Cache consistency failure"

        # Test 4: Database file validation
        if not geolocation_service.geoip_reader:
            health_status["status"] = "unhealthy"
            health_status["details"]["database_error"] = "MaxMind database not available"

        # Record metrics
        metrics_collector.record_gauge("geolocation_detection_time_ms", detection_time_ms)
        metrics_collector.record_gauge("geolocation_cache_time_ms", cache_time_ms)
        metrics_collector.record_gauge("geolocation_health_check_success", 1)

        # Performance metrics
        health_status["performance"] = {
            "detection_time_ms": round(detection_time_ms, 2),
            "cache_time_ms": round(cache_time_ms, 2),
            "cache_hit": cache_time_ms < detection_time_ms * 0.5
        }

        health_status["details"].update({
            "database_available": geolocation_service.geoip_reader is not None,
            "test_country": test_result.country_code,
            "detection_method": test_result.detection_method,
            "confidence_score": test_result.confidence_score,
            "cache_functional": test_result.country_code == cache_test_result.country_code
        })

    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["details"]["error"] = str(e)
        metrics_collector.record_gauge("geolocation_health_check_success", 0)

        # Log error with correlation ID
        import logging
        logger = logging.getLogger(__name__)
        logger.error(
            f"Geolocation health check failed: {str(e)}",
            extra={"correlation_id": correlation}
        )

    # Return appropriate HTTP status
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )
    elif health_status["status"] == "degraded":
        # Return 200 but with degraded status for monitoring
        pass

    return health_status


@router.get("/payment-routing", response_model=Dict[str, Any])
async def check_payment_routing_health():
    """
    Payment provider routing health check.

    Validates geolocation-based payment provider selection
    and routing logic functionality.

    Returns:
        Dict containing payment routing health status

    Raises:
        HTTPException: If payment routing is unhealthy
    """
    correlation = correlation_id.get()
    health_status = {
        "service": "payment_routing",
        "status": "healthy",
        "timestamp": time.time(),
        "details": {},
        "providers": {}
    }

    try:
        # Import payment provider manager
        from app.core.payment.providers import get_payment_provider_manager
        provider_manager = get_payment_provider_manager()

        # Test routing for different countries
        test_scenarios = [
            {"country": "NG", "currency": "NGN", "expected_provider": "paystack"},
            {"country": "US", "currency": "USD", "expected_provider": "stripe"},
            {"country": "GB", "currency": "GBP", "expected_provider": "stripe"},
        ]

        routing_results = {}
        successful_routes = 0

        for scenario in test_scenarios:
            try:
                start_time = time.perf_counter()
                selected_provider, routing_decision = await provider_manager.select_provider(
                    country_code=scenario["country"],
                    currency=scenario["currency"],
                    amount=100.0
                )
                routing_time_ms = (time.perf_counter() - start_time) * 1000

                provider_match = selected_provider.value.lower() == scenario["expected_provider"]
                if provider_match:
                    successful_routes += 1

                routing_results[scenario["country"]] = {
                    "expected_provider": scenario["expected_provider"],
                    "selected_provider": selected_provider.value.lower(),
                    "routing_time_ms": round(routing_time_ms, 2),
                    "match": provider_match,
                    "routing_reason": routing_decision.routing_reason
                }

            except Exception as e:
                routing_results[scenario["country"]] = {
                    "expected_provider": scenario["expected_provider"],
                    "error": str(e),
                    "match": False
                }

        # Calculate success rate
        success_rate = successful_routes / len(test_scenarios)

        if success_rate < 0.8:  # 80% success threshold
            health_status["status"] = "unhealthy"
            health_status["details"]["routing_error"] = f"Low success rate: {success_rate:.2%}"
        elif success_rate < 1.0:
            health_status["status"] = "degraded"
            health_status["details"]["routing_warning"] = f"Partial success rate: {success_rate:.2%}"

        health_status["details"].update({
            "success_rate": success_rate,
            "successful_routes": successful_routes,
            "total_scenarios": len(test_scenarios)
        })

        health_status["providers"] = routing_results

        # Record metrics
        metrics_collector.record_gauge("payment_routing_success_rate", success_rate)
        metrics_collector.record_gauge("payment_routing_health_check_success", 1)

    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["details"]["error"] = str(e)
        metrics_collector.record_gauge("payment_routing_health_check_success", 0)

        # Log error with correlation ID
        import logging
        logger = logging.getLogger(__name__)
        logger.error(
            f"Payment routing health check failed: {str(e)}",
            extra={"correlation_id": correlation}
        )

    # Return appropriate HTTP status
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )

    return health_status
