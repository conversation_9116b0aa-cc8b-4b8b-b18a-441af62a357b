#!/bin/bash

# Culture Connect Backend - Development Deployment Script
# This script handles the complete deployment process for the development environment
# including Docker container management, database migrations, and health checks.

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-development}"
VERSION="${VERSION:-dev-latest}"
COMPOSE_FILE="$PROJECT_ROOT/docker/development/docker-compose.yml"
ENV_FILE="$PROJECT_ROOT/environments/development/.env.development"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

error_exit() {
    log_error "$1"
    exit 1
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Cleanup function
cleanup() {
    if [[ $? -ne 0 ]]; then
        log_error "Deployment failed. Cleaning up..."
        # Add cleanup logic here if needed
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Validate environment
validate_environment() {
    log_info "Validating development environment setup"
    
    # Check required files
    local required_files=(
        "$COMPOSE_FILE"
        "$ENV_FILE"
        "$PROJECT_ROOT/Dockerfile"
        "$PROJECT_ROOT/requirements.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error_exit "Required file not found: $file"
        fi
    done
    
    # Check Docker and Docker Compose
    if ! command -v docker &> /dev/null; then
        error_exit "Docker is not installed or not in PATH"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose is not installed or not in PATH"
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error_exit "Docker daemon is not running"
    fi
    
    log_success "Environment validation passed"
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks"
    
    cd "$PROJECT_ROOT"
    
    # Check if containers are already running
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_warning "Some containers are already running. Stopping them..."
        docker-compose -f "$COMPOSE_FILE" down
    fi
    
    # Clean up any orphaned containers
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    
    # Prune unused Docker resources (development only)
    log_info "Cleaning up unused Docker resources..."
    docker system prune -f --volumes
    
    log_success "Pre-deployment checks completed"
}

# Build images
build_images() {
    log_info "Building Docker images for development"
    
    cd "$PROJECT_ROOT"
    
    # Build with no cache for development to ensure latest changes
    docker-compose -f "$COMPOSE_FILE" build --no-cache --parallel
    
    if [[ $? -eq 0 ]]; then
        log_success "Docker images built successfully"
    else
        error_exit "Failed to build Docker images"
    fi
}

# Deploy services
deploy_services() {
    log_info "Deploying development services"
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables
    export CC_VERSION="$VERSION"
    export CC_ENVIRONMENT="$DEPLOYMENT_ENV"
    
    # Deploy with Docker Compose
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    if [[ $? -eq 0 ]]; then
        log_success "Services deployed successfully"
    else
        error_exit "Service deployment failed"
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations"
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    docker-compose -f "$COMPOSE_FILE" exec -T api alembic upgrade head
    
    if [[ $? -eq 0 ]]; then
        log_success "Database migrations completed"
    else
        error_exit "Database migrations failed"
    fi
}

# Health checks
health_checks() {
    log_info "Performing health checks"
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts"
        
        # Check API health
        if curl -f -s http://localhost:8000/health > /dev/null; then
            log_success "API health check passed"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "Health checks failed after $max_attempts attempts"
        fi
        
        sleep 10
        ((attempt++))
    done
}

# Development-specific setup
development_setup() {
    log_info "Setting up development environment"
    
    # Create development directories
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/uploads"
    mkdir -p "$PROJECT_ROOT/data"
    
    # Set permissions
    chmod 755 "$PROJECT_ROOT/logs"
    chmod 755 "$PROJECT_ROOT/uploads"
    chmod 755 "$PROJECT_ROOT/data"
    
    # Install development dependencies (if needed)
    if [[ -f "$PROJECT_ROOT/requirements-dev.txt" ]]; then
        log_info "Installing development dependencies..."
        docker-compose -f "$COMPOSE_FILE" exec -T api pip install -r requirements-dev.txt
    fi
    
    # Seed development data (optional)
    if [[ -f "$PROJECT_ROOT/scripts/seed_dev_data.py" ]]; then
        log_info "Seeding development data..."
        docker-compose -f "$COMPOSE_FILE" exec -T api python scripts/seed_dev_data.py
    fi
    
    log_success "Development environment setup completed"
}

# Show deployment information
show_deployment_info() {
    log_success "Development deployment completed successfully!"
    echo
    log_info "=== Deployment Information ==="
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    echo
    log_info "=== Service URLs ==="
    log_info "API: http://localhost:8000"
    log_info "API Documentation: http://localhost:8000/docs"
    log_info "Adminer (Database): http://localhost:8080"
    log_info "MailHog (Email Testing): http://localhost:8025"
    echo
    log_info "=== Useful Commands ==="
    log_info "View logs: docker-compose -f $COMPOSE_FILE logs -f"
    log_info "Stop services: docker-compose -f $COMPOSE_FILE down"
    log_info "Restart services: docker-compose -f $COMPOSE_FILE restart"
    log_info "Access API container: docker-compose -f $COMPOSE_FILE exec api bash"
    echo
    log_info "=== Development Tools ==="
    log_info "Start dev tools: docker-compose -f $COMPOSE_FILE --profile dev up -d"
    log_info "Stop dev tools: docker-compose -f $COMPOSE_FILE --profile dev down"
}

# Main deployment function
main() {
    log_info "Starting Culture Connect Backend development deployment"
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    
    validate_environment
    pre_deployment_checks
    build_images
    deploy_services
    run_migrations
    development_setup
    health_checks
    show_deployment_info
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
