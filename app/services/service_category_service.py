"""
Service Category Service for Culture Connect Backend API.

This module provides comprehensive service category management functionality including:
- Category CRUD operations with hierarchical support
- Category tree management and navigation
- Category validation and business rules
- Integration with service listing system
- Category performance tracking and analytics

Implements Task 3.2.1 requirements for category management service with
production-grade business logic, validation, and hierarchical categorization.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import correlation_id
from app.services.base import BaseService, NotFoundError, ValidationError, ServiceError, ConflictError
from app.models.service import ServiceCategory
from app.repositories.service_repository import ServiceCategoryRepository
from app.schemas.service import (
    ServiceCategoryCreate, ServiceCategoryUpdate, ServiceCategoryResponse
)

logger = logging.getLogger(__name__)


class ServiceCategoryService(BaseService[ServiceCategory, ServiceCategoryRepository]):
    """
    Service category service for comprehensive category management operations.

    Provides business logic for:
    - Category CRUD operations with validation
    - Hierarchical category tree management
    - Category slug generation and validation
    - Category performance tracking
    - Integration with service listing system
    """

    def __init__(self, db: AsyncSession):
        """Initialize service category service."""
        super().__init__(ServiceCategoryRepository, ServiceCategory, db_session=db)

    async def create_category(
        self,
        category_data: ServiceCategoryCreate
    ) -> ServiceCategoryResponse:
        """
        Create a new service category.

        Args:
            category_data: Category creation data

        Returns:
            ServiceCategoryResponse: Created category with details

        Raises:
            ValidationError: If category data is invalid
            ConflictError: If category slug already exists
        """
        correlation = self.log_operation_start(
            "create_category",
            category_name=category_data.name,
            parent_id=category_data.parent_id
        )

        try:
            # Validate category data
            await self._validate_category_data(category_data)

            # Generate unique slug
            slug = await self._generate_unique_slug(category_data.name)

            # Validate parent category if specified
            if category_data.parent_id:
                parent = await self.repository.get_by_id(category_data.parent_id)
                if not parent:
                    raise ValidationError(f"Parent category with ID {category_data.parent_id} not found")
                
                if not parent.is_active:
                    raise ValidationError("Parent category must be active")

                # Check depth limit (prevent too deep nesting)
                depth = await self._get_category_depth(parent)
                if depth >= 3:  # Max 3 levels deep
                    raise ValidationError("Category nesting too deep (maximum 3 levels)")

            # Create category
            category_dict = category_data.model_dump(exclude_unset=True)
            category_dict.update({
                "slug": slug,
                "is_active": True,
                "service_count": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })

            category = await self.repository.create(category_dict)

            response = ServiceCategoryResponse.model_validate(category)

            self.log_operation_success(
                correlation,
                f"Category created successfully: {category.name} (ID: {category.id})"
            )

            return response

        except (ValidationError, ConflictError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "create_category",
                {"category_name": category_data.name}
            )

    async def update_category(
        self,
        category_id: int,
        category_data: ServiceCategoryUpdate
    ) -> ServiceCategoryResponse:
        """
        Update an existing service category.

        Args:
            category_id: ID of the category to update
            category_data: Category update data

        Returns:
            ServiceCategoryResponse: Updated category with details

        Raises:
            NotFoundError: If category not found
            ValidationError: If update data is invalid
        """
        correlation = self.log_operation_start(
            "update_category",
            category_id=category_id
        )

        try:
            # Get existing category
            category = await self.repository.get_by_id(category_id)
            if not category:
                raise NotFoundError(f"Category with ID {category_id} not found")

            # Validate update data
            await self._validate_category_update(category, category_data)

            # Update category
            update_dict = category_data.model_dump(exclude_unset=True)
            update_dict["updated_at"] = datetime.now(timezone.utc)

            # Handle slug update if name changed
            if "name" in update_dict and update_dict["name"] != category.name:
                update_dict["slug"] = await self._generate_unique_slug(
                    update_dict["name"], exclude_category_id=category_id
                )

            updated_category = await self.repository.update(category_id, update_dict)

            response = ServiceCategoryResponse.model_validate(updated_category)

            self.log_operation_success(
                correlation,
                f"Category updated successfully: {updated_category.name} (ID: {category_id})"
            )

            return response

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "update_category",
                {"category_id": category_id}
            )

    async def get_category(self, category_id: int) -> ServiceCategoryResponse:
        """
        Get a category by ID.

        Args:
            category_id: ID of the category to retrieve

        Returns:
            ServiceCategoryResponse: Category with details

        Raises:
            NotFoundError: If category not found
        """
        correlation = self.log_operation_start(
            "get_category",
            category_id=category_id
        )

        try:
            category = await self.repository.get_by_id(category_id)
            if not category:
                raise NotFoundError(f"Category with ID {category_id} not found")

            response = ServiceCategoryResponse.model_validate(category)

            self.log_operation_success(
                correlation,
                f"Category retrieved successfully: {category.name} (ID: {category_id})"
            )

            return response

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_category",
                {"category_id": category_id}
            )

    async def get_category_by_slug(self, slug: str) -> ServiceCategoryResponse:
        """
        Get a category by slug.

        Args:
            slug: Slug of the category to retrieve

        Returns:
            ServiceCategoryResponse: Category with details

        Raises:
            NotFoundError: If category not found
        """
        correlation = self.log_operation_start(
            "get_category_by_slug",
            slug=slug
        )

        try:
            category = await self.repository.get_by_slug(slug)
            if not category:
                raise NotFoundError(f"Category with slug '{slug}' not found")

            response = ServiceCategoryResponse.model_validate(category)

            self.log_operation_success(
                correlation,
                f"Category retrieved by slug: {category.name} (slug: {slug})"
            )

            return response

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_category_by_slug",
                {"slug": slug}
            )

    async def list_root_categories(self) -> List[ServiceCategoryResponse]:
        """
        List all root categories (categories without parent).

        Returns:
            List[ServiceCategoryResponse]: List of root categories
        """
        correlation = self.log_operation_start("list_root_categories")

        try:
            categories = await self.repository.get_root_categories()

            responses = [
                ServiceCategoryResponse.model_validate(category)
                for category in categories
            ]

            self.log_operation_success(
                correlation,
                f"Retrieved {len(responses)} root categories"
            )

            return responses

        except Exception as e:
            await self.handle_service_error(e, "list_root_categories", {})

    async def list_category_children(self, parent_id: int) -> List[ServiceCategoryResponse]:
        """
        List all child categories of a parent category.

        Args:
            parent_id: ID of the parent category

        Returns:
            List[ServiceCategoryResponse]: List of child categories

        Raises:
            NotFoundError: If parent category not found
        """
        correlation = self.log_operation_start(
            "list_category_children",
            parent_id=parent_id
        )

        try:
            # Verify parent exists
            parent = await self.repository.get_by_id(parent_id)
            if not parent:
                raise NotFoundError(f"Parent category with ID {parent_id} not found")

            children = await self.repository.get_children(parent_id)

            responses = [
                ServiceCategoryResponse.model_validate(category)
                for category in children
            ]

            self.log_operation_success(
                correlation,
                f"Retrieved {len(responses)} child categories for parent {parent_id}"
            )

            return responses

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "list_category_children",
                {"parent_id": parent_id}
            )

    async def get_category_tree(self) -> List[Dict[str, Any]]:
        """
        Get the complete category tree structure.

        Returns:
            List[Dict[str, Any]]: Hierarchical category tree
        """
        correlation = self.log_operation_start("get_category_tree")

        try:
            # Get all categories
            all_categories = await self.repository.get_all_active()

            # Build tree structure
            tree = self._build_category_tree(all_categories)

            self.log_operation_success(
                correlation,
                f"Built category tree with {len(tree)} root categories"
            )

            return tree

        except Exception as e:
            await self.handle_service_error(e, "get_category_tree", {})

    async def delete_category(self, category_id: int) -> bool:
        """
        Delete a category (soft delete by setting is_active to False).

        Args:
            category_id: ID of the category to delete

        Returns:
            bool: True if deletion successful

        Raises:
            NotFoundError: If category not found
            ValidationError: If category has active services or children
        """
        correlation = self.log_operation_start(
            "delete_category",
            category_id=category_id
        )

        try:
            # Get category
            category = await self.repository.get_by_id(category_id)
            if not category:
                raise NotFoundError(f"Category with ID {category_id} not found")

            # Check if category has active services
            if category.service_count > 0:
                raise ValidationError("Cannot delete category with active services")

            # Check if category has active children
            children = await self.repository.get_children(category_id)
            active_children = [child for child in children if child.is_active]
            if active_children:
                raise ValidationError("Cannot delete category with active child categories")

            # Soft delete
            await self.repository.update(category_id, {
                "is_active": False,
                "updated_at": datetime.now(timezone.utc)
            })

            self.log_operation_success(
                correlation,
                f"Category deleted successfully: {category.name} (ID: {category_id})"
            )

            return True

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "delete_category",
                {"category_id": category_id}
            )

    async def _validate_category_data(self, category_data: ServiceCategoryCreate) -> None:
        """Validate category creation data."""
        # Check for duplicate name at the same level
        if category_data.parent_id:
            siblings = await self.repository.get_children(category_data.parent_id)
        else:
            siblings = await self.repository.get_root_categories()

        for sibling in siblings:
            if sibling.name.lower() == category_data.name.lower():
                raise ConflictError("Category name already exists at this level")

    async def _validate_category_update(
        self,
        category: ServiceCategory,
        update_data: ServiceCategoryUpdate
    ) -> None:
        """Validate category update data."""
        # Check for name conflicts if name is being changed
        if hasattr(update_data, 'name') and update_data.name and update_data.name != category.name:
            if category.parent_id:
                siblings = await self.repository.get_children(category.parent_id)
            else:
                siblings = await self.repository.get_root_categories()

            for sibling in siblings:
                if sibling.id != category.id and sibling.name.lower() == update_data.name.lower():
                    raise ConflictError("Category name already exists at this level")

    async def _get_category_depth(self, category: ServiceCategory) -> int:
        """Get the depth of a category in the hierarchy."""
        depth = 0
        current = category
        
        while current.parent_id:
            depth += 1
            current = await self.repository.get_by_id(current.parent_id)
            if not current:
                break
                
        return depth

    async def _generate_unique_slug(
        self,
        name: str,
        exclude_category_id: Optional[int] = None
    ) -> str:
        """Generate a unique slug for the category."""
        import re
        
        # Create base slug from name
        base_slug = re.sub(r'[^a-zA-Z0-9\s-]', '', name.lower())
        base_slug = re.sub(r'\s+', '-', base_slug.strip())
        
        # Check for uniqueness
        counter = 0
        slug = base_slug
        
        while True:
            existing = await self.repository.get_by_slug(slug)
            if not existing or (exclude_category_id and existing.id == exclude_category_id):
                break
                
            counter += 1
            slug = f"{base_slug}-{counter}"
            
        return slug

    def _build_category_tree(self, categories: List[ServiceCategory]) -> List[Dict[str, Any]]:
        """Build hierarchical tree structure from flat category list."""
        category_dict = {cat.id: cat for cat in categories}
        tree = []

        for category in categories:
            if category.parent_id is None:
                # Root category
                tree_node = self._build_tree_node(category, category_dict)
                tree.append(tree_node)

        return tree

    def _build_tree_node(
        self,
        category: ServiceCategory,
        category_dict: Dict[int, ServiceCategory]
    ) -> Dict[str, Any]:
        """Build a tree node with children."""
        node = {
            "id": category.id,
            "name": category.name,
            "slug": category.slug,
            "description": category.description,
            "icon": category.icon,
            "display_order": category.display_order,
            "service_count": category.service_count,
            "children": []
        }

        # Add children
        for cat_id, cat in category_dict.items():
            if cat.parent_id == category.id:
                child_node = self._build_tree_node(cat, category_dict)
                node["children"].append(child_node)

        # Sort children by display_order
        node["children"].sort(key=lambda x: x["display_order"])

        return node
