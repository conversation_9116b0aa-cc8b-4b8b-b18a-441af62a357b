"""Add payment system tables for Phase 1 Payment & Transaction Management System

Revision ID: payment_system_tables
Revises: availability_models
Create Date: 2025-01-24 15:00:00.000000

This migration creates comprehensive payment system tables including:
- Core payment and payment method models with multi-provider support
- Transaction tracking and audit trails for compliance
- Vendor payout and escrow account management
- Revenue tracking and financial reconciliation
- Performance-optimized indexes and constraints
- PostgreSQL-specific optimizations and enum types

Implements Phase 1 of Payment & Transaction Management System with:
- Multi-provider support (Stripe, Paystack, Busha)
- Encrypted sensitive data storage with security features
- Comprehensive audit logging and compliance features
- Performance targets: <500ms payment creation, <100ms status updates
- Integration with existing User, Vendor, and Booking models
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'payment_system_tables'
down_revision = 'availability_models'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema - Add payment system tables."""

    # Create PostgreSQL enum types for payment system

    # Payment provider types
    op.execute("""
        CREATE TYPE paymentprovidertype AS ENUM (
            'stripe', 'paystack', 'busha'
        )
    """)

    # Payment status types
    op.execute("""
        CREATE TYPE paymentstatus AS ENUM (
            'pending', 'processing', 'completed', 'failed',
            'cancelled', 'refunded', 'partially_refunded',
            'disputed', 'expired'
        )
    """)

    # Payment method types
    op.execute("""
        CREATE TYPE paymentmethodtype AS ENUM (
            'card', 'bank_transfer', 'mobile_money',
            'ussd', 'crypto', 'wallet'
        )
    """)

    # Transaction types
    op.execute("""
        CREATE TYPE transactiontype AS ENUM (
            'charge', 'refund', 'payout', 'fee',
            'adjustment', 'chargeback', 'reversal'
        )
    """)

    # Transaction status types
    op.execute("""
        CREATE TYPE transactionstatus AS ENUM (
            'pending', 'processing', 'completed', 'failed',
            'cancelled', 'reversed', 'disputed'
        )
    """)

    # Transaction event types
    op.execute("""
        CREATE TYPE transactioneventtype AS ENUM (
            'created', 'processing', 'completed', 'failed',
            'cancelled', 'refunded', 'disputed', 'reversed',
            'webhook_received', 'provider_callback'
        )
    """)

    # Payout status types
    op.execute("""
        CREATE TYPE payoutstatus AS ENUM (
            'pending', 'processing', 'completed', 'failed',
            'cancelled', 'reversed', 'on_hold'
        )
    """)

    # Escrow status types
    op.execute("""
        CREATE TYPE escrowstatus AS ENUM (
            'active', 'held', 'released', 'disputed',
            'expired', 'cancelled'
        )
    """)

    # Release condition types
    op.execute("""
        CREATE TYPE releasecondition AS ENUM (
            'service_completion', 'customer_confirmation',
            'time_based', 'manual_release', 'dispute_resolution'
        )
    """)

    # Revenue category types
    op.execute("""
        CREATE TYPE revenuecategory AS ENUM (
            'service_booking', 'platform_fee', 'payment_processing',
            'subscription', 'advertising', 'other'
        )
    """)

    # Reconciliation status types
    op.execute("""
        CREATE TYPE reconciliationstatus AS ENUM (
            'pending', 'in_progress', 'completed', 'failed',
            'discrepancy', 'manual_review'
        )
    """)

    # 1. Create payment_methods table
    op.create_table(
        'payment_methods',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core payment method information
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('type', postgresql.ENUM('card', 'bank_transfer', 'mobile_money', 'ussd', 'crypto', 'wallet', name='paymentmethodtype'), nullable=False),
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=False),

        # Provider-specific information
        sa.Column('provider_method_id', sa.String(255), nullable=True),

        # Display information (non-sensitive)
        sa.Column('display_name', sa.String(100), nullable=True),
        sa.Column('last_four', sa.String(4), nullable=True),
        sa.Column('brand', sa.String(50), nullable=True),
        sa.Column('expiry_month', sa.Integer(), nullable=True),
        sa.Column('expiry_year', sa.Integer(), nullable=True),

        # Status and preferences
        sa.Column('is_default', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, default=False),

        # Security and metadata
        sa.Column('encrypted_metadata', sa.Text(), nullable=True),
        sa.Column('verification_data', sa.Text(), nullable=True),

        # Usage tracking
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=False, default=0),

        # Failure tracking
        sa.Column('failure_count', sa.Integer(), nullable=False, default=0),
        sa.Column('last_failure_at', sa.DateTime(timezone=True), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints
        sa.CheckConstraint('usage_count >= 0', name='check_usage_count_non_negative'),
        sa.CheckConstraint('failure_count >= 0', name='check_failure_count_non_negative'),
        sa.CheckConstraint('expiry_month IS NULL OR (expiry_month >= 1 AND expiry_month <= 12)', name='check_expiry_month_range'),
        sa.CheckConstraint('expiry_year IS NULL OR expiry_year >= 2020', name='check_expiry_year_valid'),
    )

    # 2. Create payments table
    op.create_table(
        'payments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core payment information
        sa.Column('booking_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),

        # Financial details
        sa.Column('amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Payment method and provider
        sa.Column('payment_method_id', sa.Integer(), nullable=True),
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=False),

        # Status and tracking
        sa.Column('status', postgresql.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded', 'disputed', 'expired', name='paymentstatus'), nullable=False, default='pending'),

        # Provider-specific identifiers
        sa.Column('payment_intent_id', sa.String(255), nullable=True),
        sa.Column('transaction_reference', sa.String(100), nullable=False),
        sa.Column('provider_reference', sa.String(255), nullable=True),

        # Payment URLs and client secrets
        sa.Column('payment_url', sa.String(500), nullable=True),
        sa.Column('client_secret', sa.Text(), nullable=True),

        # Timing and expiration
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('paid_at', sa.DateTime(timezone=True), nullable=True),

        # Fee and commission tracking
        sa.Column('platform_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('provider_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('net_amount', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Failure and error tracking
        sa.Column('failure_reason', sa.String(255), nullable=True),
        sa.Column('failure_code', sa.String(50), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),

        # Metadata and additional information
        sa.Column('payment_metadata', postgresql.JSONB(), nullable=True),
        sa.Column('provider_response', sa.Text(), nullable=True),

        # Customer information
        sa.Column('customer_ip', sa.String(45), nullable=True),
        sa.Column('customer_user_agent', sa.String(500), nullable=True),

        # Geo-location information
        sa.Column('country_code', sa.String(2), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['payment_method_id'], ['payment_methods.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('transaction_reference'),

        # Check constraints
        sa.CheckConstraint('amount > 0', name='check_payment_amount_positive'),
        sa.CheckConstraint('platform_fee >= 0', name='check_platform_fee_non_negative'),
        sa.CheckConstraint('provider_fee >= 0', name='check_provider_fee_non_negative'),
        sa.CheckConstraint('net_amount >= 0', name='check_net_amount_non_negative'),
        sa.CheckConstraint('retry_count >= 0', name='check_retry_count_non_negative'),
    )

    # 3. Create transactions table
    op.create_table(
        'transactions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core transaction information
        sa.Column('payment_id', sa.Integer(), nullable=False),

        # Transaction details
        sa.Column('type', postgresql.ENUM('charge', 'refund', 'payout', 'fee', 'adjustment', 'chargeback', 'reversal', name='transactiontype'), nullable=False),
        sa.Column('status', postgresql.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed', 'disputed', name='transactionstatus'), nullable=False, default='pending'),

        # Financial details
        sa.Column('amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Provider information
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=False),
        sa.Column('provider_transaction_id', sa.String(255), nullable=True),
        sa.Column('reference_id', sa.String(100), nullable=False),

        # Provider response and metadata
        sa.Column('provider_response', sa.Text(), nullable=True),
        sa.Column('provider_status_code', sa.String(50), nullable=True),
        sa.Column('provider_message', sa.String(500), nullable=True),

        # Timing information
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),

        # Fee tracking
        sa.Column('provider_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('platform_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('net_amount', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Error and failure tracking
        sa.Column('failure_reason', sa.String(255), nullable=True),
        sa.Column('failure_code', sa.String(50), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),

        # Reconciliation tracking
        sa.Column('reconciled', sa.Boolean(), nullable=False, default=False),
        sa.Column('reconciled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reconciliation_reference', sa.String(100), nullable=True),

        # Metadata
        sa.Column('transaction_metadata', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['payment_id'], ['payments.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('reference_id'),

        # Check constraints
        sa.CheckConstraint('amount > 0', name='check_transaction_amount_positive'),
        sa.CheckConstraint('provider_fee >= 0', name='check_transaction_provider_fee_non_negative'),
        sa.CheckConstraint('platform_fee >= 0', name='check_transaction_platform_fee_non_negative'),
        sa.CheckConstraint('net_amount >= 0', name='check_transaction_net_amount_non_negative'),
        sa.CheckConstraint('retry_count >= 0', name='check_transaction_retry_count_non_negative'),
    )

    # 4. Create transaction_events table
    op.create_table(
        'transaction_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core event information
        sa.Column('transaction_id', sa.Integer(), nullable=False),

        # Event details
        sa.Column('event_type', postgresql.ENUM('created', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'disputed', 'reversed', 'webhook_received', 'provider_callback', name='transactioneventtype'), nullable=False),
        sa.Column('event_source', sa.String(50), nullable=False),

        # Event data
        sa.Column('event_data', postgresql.JSONB(), nullable=True),
        sa.Column('provider_event_id', sa.String(255), nullable=True),

        # Status tracking
        sa.Column('previous_status', sa.String(50), nullable=True),
        sa.Column('new_status', sa.String(50), nullable=True),

        # Processing information
        sa.Column('processing_time_ms', sa.Integer(), nullable=True),

        # Error tracking
        sa.Column('error_message', sa.String(500), nullable=True),
        sa.Column('error_code', sa.String(50), nullable=True),

        # Metadata
        sa.Column('event_metadata', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['transaction_id'], ['transactions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints
        sa.CheckConstraint('processing_time_ms IS NULL OR processing_time_ms >= 0', name='check_processing_time_non_negative'),
    )

    # 5. Create vendor_payouts table
    op.create_table(
        'vendor_payouts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core payout information
        sa.Column('vendor_id', sa.Integer(), nullable=False),

        # Payout period
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),

        # Financial details
        sa.Column('total_earnings', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('platform_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('processing_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('adjustment_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('net_amount', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Payout method and provider
        sa.Column('payout_method_id', sa.Integer(), nullable=True),
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=True),

        # Status and tracking
        sa.Column('status', postgresql.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed', 'on_hold', name='payoutstatus'), nullable=False, default='pending'),

        # Provider information
        sa.Column('provider_payout_id', sa.String(255), nullable=True),
        sa.Column('reference_id', sa.String(100), nullable=False),

        # Timing information
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('scheduled_at', sa.DateTime(timezone=True), nullable=True),

        # Failure tracking
        sa.Column('failure_reason', sa.String(255), nullable=True),
        sa.Column('failure_code', sa.String(50), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),

        # Metadata
        sa.Column('payout_metadata', postgresql.JSONB(), nullable=True),
        sa.Column('provider_response', sa.Text(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['payout_method_id'], ['payment_methods.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('reference_id'),

        # Check constraints
        sa.CheckConstraint('total_earnings >= 0', name='check_total_earnings_non_negative'),
        sa.CheckConstraint('platform_fee >= 0', name='check_payout_platform_fee_non_negative'),
        sa.CheckConstraint('processing_fee >= 0', name='check_payout_processing_fee_non_negative'),
        sa.CheckConstraint('net_amount >= 0', name='check_payout_net_amount_non_negative'),
        sa.CheckConstraint('retry_count >= 0', name='check_payout_retry_count_non_negative'),
        sa.CheckConstraint('period_start < period_end', name='check_period_valid'),
    )

    # 6. Create escrow_accounts table
    op.create_table(
        'escrow_accounts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core escrow information
        sa.Column('booking_id', sa.Integer(), nullable=False),
        sa.Column('payment_id', sa.Integer(), nullable=False),

        # Financial details
        sa.Column('amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Status and conditions
        sa.Column('status', postgresql.ENUM('active', 'held', 'released', 'disputed', 'expired', 'cancelled', name='escrowstatus'), nullable=False, default='active'),
        sa.Column('release_condition', postgresql.ENUM('service_completion', 'customer_confirmation', 'time_based', 'manual_release', 'dispute_resolution', name='releasecondition'), nullable=False, default='service_completion'),

        # Timing information
        sa.Column('hold_until', sa.DateTime(timezone=True), nullable=True),
        sa.Column('released_at', sa.DateTime(timezone=True), nullable=True),

        # Release tracking
        sa.Column('released_by', sa.Integer(), nullable=True),
        sa.Column('release_reason', sa.String(255), nullable=True),
        sa.Column('auto_release_enabled', sa.Boolean(), nullable=False, default=True),

        # Dispute information
        sa.Column('disputed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('disputed_by', sa.Integer(), nullable=True),
        sa.Column('dispute_reason', sa.Text(), nullable=True),
        sa.Column('dispute_resolved_at', sa.DateTime(timezone=True), nullable=True),

        # Metadata
        sa.Column('escrow_metadata', postgresql.JSONB(), nullable=True),
        sa.Column('release_conditions_data', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['payment_id'], ['payments.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['released_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['disputed_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints
        sa.CheckConstraint('amount > 0', name='check_escrow_amount_positive'),
    )

    # 7. Create revenue_records table
    op.create_table(
        'revenue_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core revenue information
        sa.Column('booking_id', sa.Integer(), nullable=True),
        sa.Column('payment_id', sa.Integer(), nullable=True),
        sa.Column('vendor_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),

        # Revenue details
        sa.Column('category', postgresql.ENUM('service_booking', 'platform_fee', 'payment_processing', 'subscription', 'advertising', 'other', name='revenuecategory'), nullable=False),
        sa.Column('gross_amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('platform_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('processing_fee', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('net_vendor_amount', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('net_platform_amount', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Provider and method tracking
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=True),
        sa.Column('payment_method', sa.String(50), nullable=True),

        # Timing information
        sa.Column('transaction_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('settlement_date', sa.DateTime(timezone=True), nullable=True),

        # Tax and compliance
        sa.Column('tax_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('tax_rate', sa.Numeric(5, 4), nullable=False, default=0.0000),

        # Currency conversion
        sa.Column('original_currency', sa.String(3), nullable=True),
        sa.Column('original_amount', sa.Numeric(12, 2), nullable=True),
        sa.Column('exchange_rate', sa.Numeric(10, 6), nullable=True),

        # Reference and tracking
        sa.Column('reference_id', sa.String(100), nullable=False),
        sa.Column('external_reference', sa.String(255), nullable=True),

        # Metadata
        sa.Column('revenue_metadata', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['payment_id'], ['payments.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('reference_id'),

        # Check constraints
        sa.CheckConstraint('gross_amount > 0', name='check_gross_amount_positive'),
        sa.CheckConstraint('platform_fee >= 0', name='check_revenue_platform_fee_non_negative'),
        sa.CheckConstraint('processing_fee >= 0', name='check_revenue_processing_fee_non_negative'),
        sa.CheckConstraint('net_vendor_amount >= 0', name='check_net_vendor_amount_non_negative'),
        sa.CheckConstraint('net_platform_amount >= 0', name='check_net_platform_amount_non_negative'),
        sa.CheckConstraint('tax_amount >= 0', name='check_tax_amount_non_negative'),
        sa.CheckConstraint('tax_rate >= 0.0 AND tax_rate <= 1.0', name='check_tax_rate_range'),
        sa.CheckConstraint('exchange_rate IS NULL OR exchange_rate > 0', name='check_exchange_rate_positive'),
    )

    # 8. Create reconciliation_records table
    op.create_table(
        'reconciliation_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core reconciliation information
        sa.Column('provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=False),

        # Reconciliation period
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),

        # Financial amounts
        sa.Column('expected_amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('actual_amount', sa.Numeric(12, 2), nullable=False),
        sa.Column('variance', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),

        # Status and tracking
        sa.Column('status', postgresql.ENUM('pending', 'in_progress', 'completed', 'failed', 'discrepancy', 'manual_review', name='reconciliationstatus'), nullable=False, default='pending'),

        # Transaction counts
        sa.Column('expected_transaction_count', sa.Integer(), nullable=False, default=0),
        sa.Column('actual_transaction_count', sa.Integer(), nullable=False, default=0),

        # Timing information
        sa.Column('reconciled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reconciled_by', sa.Integer(), nullable=True),

        # Provider statement information
        sa.Column('provider_statement_id', sa.String(255), nullable=True),
        sa.Column('provider_statement_date', sa.DateTime(timezone=True), nullable=True),

        # Discrepancy tracking
        sa.Column('discrepancy_details', postgresql.JSONB(), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),

        # Reference and tracking
        sa.Column('reference_id', sa.String(100), nullable=False),

        # Metadata
        sa.Column('reconciliation_metadata', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['reconciled_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('reference_id'),

        # Check constraints
        sa.CheckConstraint('expected_amount >= 0', name='check_expected_amount_non_negative'),
        sa.CheckConstraint('actual_amount >= 0', name='check_actual_amount_non_negative'),
        sa.CheckConstraint('expected_transaction_count >= 0', name='check_expected_count_non_negative'),
        sa.CheckConstraint('actual_transaction_count >= 0', name='check_actual_count_non_negative'),
        sa.CheckConstraint('period_start < period_end', name='check_reconciliation_period_valid'),
    )

    # Create performance-optimized indexes for all payment tables
    # These indexes are designed to meet performance targets:
    # - Payment creation: <500ms
    # - Status updates: <100ms
    # - Financial queries: <200ms

    # Payment method indexes
    op.create_index('idx_payment_method_user_type', 'payment_methods', ['user_id', 'type'])
    op.create_index('idx_payment_method_user_default', 'payment_methods', ['user_id', 'is_default'])
    op.create_index('idx_payment_method_provider_active', 'payment_methods', ['provider', 'is_active'])
    op.create_index('idx_payment_method_user_active', 'payment_methods', ['user_id', 'is_active'])
    op.create_index('idx_payment_method_last_used', 'payment_methods', ['last_used_at', 'is_active'])

    # Payment indexes
    op.create_index('idx_payment_booking_status', 'payments', ['booking_id', 'status'])
    op.create_index('idx_payment_user_status', 'payments', ['user_id', 'status'])
    op.create_index('idx_payment_vendor_status', 'payments', ['vendor_id', 'status'])
    op.create_index('idx_payment_provider_status', 'payments', ['provider', 'status'])
    op.create_index('idx_payment_currency_amount', 'payments', ['currency', 'amount'])
    op.create_index('idx_payment_date_status', 'payments', ['created_at', 'status'])
    op.create_index('idx_payment_paid_date', 'payments', ['paid_at', 'status'])
    op.create_index('idx_payment_country_provider', 'payments', ['country_code', 'provider'])
    op.create_index('idx_payment_reference_provider', 'payments', ['transaction_reference', 'provider'])

    # Transaction indexes
    op.create_index('idx_transaction_payment_type', 'transactions', ['payment_id', 'type'])
    op.create_index('idx_transaction_provider_status', 'transactions', ['provider', 'status'])
    op.create_index('idx_transaction_type_status', 'transactions', ['type', 'status'])
    op.create_index('idx_transaction_currency_amount', 'transactions', ['currency', 'amount'])
    op.create_index('idx_transaction_date_status', 'transactions', ['created_at', 'status'])
    op.create_index('idx_transaction_processed_date', 'transactions', ['processed_at', 'status'])
    op.create_index('idx_transaction_reconciled', 'transactions', ['reconciled', 'reconciled_at'])
    op.create_index('idx_transaction_reference_provider', 'transactions', ['reference_id', 'provider'])

    # Transaction event indexes
    op.create_index('idx_transaction_event_transaction_type', 'transaction_events', ['transaction_id', 'event_type'])
    op.create_index('idx_transaction_event_type_date', 'transaction_events', ['event_type', 'created_at'])
    op.create_index('idx_transaction_event_source_date', 'transaction_events', ['event_source', 'created_at'])
    op.create_index('idx_transaction_event_provider_id', 'transaction_events', ['provider_event_id'])

    # Vendor payout indexes
    op.create_index('idx_payout_vendor_status', 'vendor_payouts', ['vendor_id', 'status'])
    op.create_index('idx_payout_vendor_period', 'vendor_payouts', ['vendor_id', 'period_start', 'period_end'])
    op.create_index('idx_payout_status_scheduled', 'vendor_payouts', ['status', 'scheduled_at'])
    op.create_index('idx_payout_provider_status', 'vendor_payouts', ['provider', 'status'])
    op.create_index('idx_payout_currency_amount', 'vendor_payouts', ['currency', 'net_amount'])
    op.create_index('idx_payout_processed_date', 'vendor_payouts', ['processed_at', 'status'])

    # Escrow account indexes
    op.create_index('idx_escrow_booking_status', 'escrow_accounts', ['booking_id', 'status'])
    op.create_index('idx_escrow_payment_status', 'escrow_accounts', ['payment_id', 'status'])
    op.create_index('idx_escrow_status_hold_until', 'escrow_accounts', ['status', 'hold_until'])
    op.create_index('idx_escrow_release_condition', 'escrow_accounts', ['release_condition', 'status'])
    op.create_index('idx_escrow_disputed', 'escrow_accounts', ['disputed_at', 'status'])
    op.create_index('idx_escrow_auto_release', 'escrow_accounts', ['auto_release_enabled', 'hold_until'])

    # Revenue record indexes
    op.create_index('idx_revenue_category_date', 'revenue_records', ['category', 'transaction_date'])
    op.create_index('idx_revenue_vendor_date', 'revenue_records', ['vendor_id', 'transaction_date'])
    op.create_index('idx_revenue_user_date', 'revenue_records', ['user_id', 'transaction_date'])
    op.create_index('idx_revenue_provider_date', 'revenue_records', ['provider', 'transaction_date'])
    op.create_index('idx_revenue_currency_amount', 'revenue_records', ['currency', 'gross_amount'])
    op.create_index('idx_revenue_settlement_date', 'revenue_records', ['settlement_date', 'category'])
    op.create_index('idx_revenue_booking_payment', 'revenue_records', ['booking_id', 'payment_id'])

    # Reconciliation record indexes
    op.create_index('idx_reconciliation_provider_period', 'reconciliation_records', ['provider', 'period_start', 'period_end'])
    op.create_index('idx_reconciliation_status_date', 'reconciliation_records', ['status', 'reconciled_at'])
    op.create_index('idx_reconciliation_provider_status', 'reconciliation_records', ['provider', 'status'])
    op.create_index('idx_reconciliation_variance', 'reconciliation_records', ['variance', 'status'])
    op.create_index('idx_reconciliation_statement', 'reconciliation_records', ['provider_statement_id'])


def downgrade() -> None:
    """Downgrade database schema - Remove payment system tables."""

    # Drop all indexes first
    op.drop_index('idx_reconciliation_statement', table_name='reconciliation_records')
    op.drop_index('idx_reconciliation_variance', table_name='reconciliation_records')
    op.drop_index('idx_reconciliation_provider_status', table_name='reconciliation_records')
    op.drop_index('idx_reconciliation_status_date', table_name='reconciliation_records')
    op.drop_index('idx_reconciliation_provider_period', table_name='reconciliation_records')

    op.drop_index('idx_revenue_booking_payment', table_name='revenue_records')
    op.drop_index('idx_revenue_settlement_date', table_name='revenue_records')
    op.drop_index('idx_revenue_currency_amount', table_name='revenue_records')
    op.drop_index('idx_revenue_provider_date', table_name='revenue_records')
    op.drop_index('idx_revenue_user_date', table_name='revenue_records')
    op.drop_index('idx_revenue_vendor_date', table_name='revenue_records')
    op.drop_index('idx_revenue_category_date', table_name='revenue_records')

    op.drop_index('idx_escrow_auto_release', table_name='escrow_accounts')
    op.drop_index('idx_escrow_disputed', table_name='escrow_accounts')
    op.drop_index('idx_escrow_release_condition', table_name='escrow_accounts')
    op.drop_index('idx_escrow_status_hold_until', table_name='escrow_accounts')
    op.drop_index('idx_escrow_payment_status', table_name='escrow_accounts')
    op.drop_index('idx_escrow_booking_status', table_name='escrow_accounts')

    op.drop_index('idx_payout_processed_date', table_name='vendor_payouts')
    op.drop_index('idx_payout_currency_amount', table_name='vendor_payouts')
    op.drop_index('idx_payout_provider_status', table_name='vendor_payouts')
    op.drop_index('idx_payout_status_scheduled', table_name='vendor_payouts')
    op.drop_index('idx_payout_vendor_period', table_name='vendor_payouts')
    op.drop_index('idx_payout_vendor_status', table_name='vendor_payouts')

    op.drop_index('idx_transaction_event_provider_id', table_name='transaction_events')
    op.drop_index('idx_transaction_event_source_date', table_name='transaction_events')
    op.drop_index('idx_transaction_event_type_date', table_name='transaction_events')
    op.drop_index('idx_transaction_event_transaction_type', table_name='transaction_events')

    op.drop_index('idx_transaction_reference_provider', table_name='transactions')
    op.drop_index('idx_transaction_reconciled', table_name='transactions')
    op.drop_index('idx_transaction_processed_date', table_name='transactions')
    op.drop_index('idx_transaction_date_status', table_name='transactions')
    op.drop_index('idx_transaction_currency_amount', table_name='transactions')
    op.drop_index('idx_transaction_type_status', table_name='transactions')
    op.drop_index('idx_transaction_provider_status', table_name='transactions')
    op.drop_index('idx_transaction_payment_type', table_name='transactions')

    op.drop_index('idx_payment_reference_provider', table_name='payments')
    op.drop_index('idx_payment_country_provider', table_name='payments')
    op.drop_index('idx_payment_paid_date', table_name='payments')
    op.drop_index('idx_payment_date_status', table_name='payments')
    op.drop_index('idx_payment_currency_amount', table_name='payments')
    op.drop_index('idx_payment_provider_status', table_name='payments')
    op.drop_index('idx_payment_vendor_status', table_name='payments')
    op.drop_index('idx_payment_user_status', table_name='payments')
    op.drop_index('idx_payment_booking_status', table_name='payments')

    op.drop_index('idx_payment_method_last_used', table_name='payment_methods')
    op.drop_index('idx_payment_method_user_active', table_name='payment_methods')
    op.drop_index('idx_payment_method_provider_active', table_name='payment_methods')
    op.drop_index('idx_payment_method_user_default', table_name='payment_methods')
    op.drop_index('idx_payment_method_user_type', table_name='payment_methods')

    # Drop tables in reverse order (respecting foreign key dependencies)
    op.drop_table('reconciliation_records')
    op.drop_table('revenue_records')
    op.drop_table('escrow_accounts')
    op.drop_table('vendor_payouts')
    op.drop_table('transaction_events')
    op.drop_table('transactions')
    op.drop_table('payments')
    op.drop_table('payment_methods')

    # Drop enum types
    op.execute('DROP TYPE IF EXISTS reconciliationstatus')
    op.execute('DROP TYPE IF EXISTS revenuecategory')
    op.execute('DROP TYPE IF EXISTS releasecondition')
    op.execute('DROP TYPE IF EXISTS escrowstatus')
    op.execute('DROP TYPE IF EXISTS payoutstatus')
    op.execute('DROP TYPE IF EXISTS transactioneventtype')
    op.execute('DROP TYPE IF EXISTS transactionstatus')
    op.execute('DROP TYPE IF EXISTS transactiontype')
    op.execute('DROP TYPE IF EXISTS paymentmethodtype')
    op.execute('DROP TYPE IF EXISTS paymentstatus')
    op.execute('DROP TYPE IF EXISTS paymentprovidertype')
