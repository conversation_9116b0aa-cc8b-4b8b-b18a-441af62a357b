# Culture Connect Backend - Grafana Deployment
# Grafana dashboard and visualization for monitoring

apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: config
data:
  grafana.ini: |
    [analytics]
    check_for_updates = false
    reporting_enabled = false

    [security]
    admin_user = admin
    admin_password = $__env{GF_SECURITY_ADMIN_PASSWORD}
    disable_gravatar = true
    cookie_secure = true
    cookie_samesite = strict
    content_type_protection = true
    x_content_type_options = true
    x_xss_protection = true

    [server]
    protocol = http
    http_port = 3000
    domain = grafana.internal.cultureconnect.ng
    root_url = https://grafana.internal.cultureconnect.ng/
    serve_from_sub_path = false

    [database]
    type = postgres
    host = $__env{GF_DATABASE_HOST}
    name = $__env{GF_DATABASE_NAME}
    user = $__env{GF_DATABASE_USER}
    password = $__env{GF_DATABASE_PASSWORD}
    ssl_mode = require

    [session]
    provider = postgres
    provider_config = $__env{GF_SESSION_PROVIDER_CONFIG}

    [dataproxy]
    timeout = 30
    keep_alive_seconds = 30

    [alerting]
    enabled = true
    execute_alerts = true

    [explore]
    enabled = true

    [log]
    mode = console
    level = info

    [paths]
    data = /var/lib/grafana
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning

    [smtp]
    enabled = true
    host = $__env{GF_SMTP_HOST}
    user = $__env{GF_SMTP_USER}
    password = $__env{GF_SMTP_PASSWORD}
    from_address = <EMAIL>
    from_name = Culture Connect Monitoring

  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
      editable: false
      jsonData:
        timeInterval: "15s"
        queryTimeout: "60s"
        httpMethod: "POST"
    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100
      editable: false
      jsonData:
        maxLines: 1000

  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: secrets
type: Opaque
stringData:
  GF_SECURITY_ADMIN_PASSWORD: "REPLACE_WITH_SECURE_PASSWORD"
  GF_DATABASE_HOST: "culture-connect-postgres"
  GF_DATABASE_NAME: "grafana"
  GF_DATABASE_USER: "grafana"
  GF_DATABASE_PASSWORD: "REPLACE_WITH_DB_PASSWORD"
  GF_SESSION_PROVIDER_CONFIG: "user=grafana password=REPLACE_WITH_DB_PASSWORD host=culture-connect-postgres port=5432 dbname=grafana sslmode=require"
  GF_SMTP_HOST: "smtp.cultureconnect.ng:587"
  GF_SMTP_USER: "<EMAIL>"
  GF_SMTP_PASSWORD: "REPLACE_WITH_SMTP_PASSWORD"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: gp3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
    app.kubernetes.io/version: "10.2.0"
    app.kubernetes.io/component: dashboard
    app.kubernetes.io/part-of: monitoring-stack
    app.kubernetes.io/managed-by: kubernetes
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
      app.kubernetes.io/component: dashboard
  template:
    metadata:
      labels:
        app.kubernetes.io/name: grafana
        app.kubernetes.io/component: dashboard
      annotations:
        checksum/config: "{{ include (print $.Template.BasePath \"/configmap.yaml\") . | sha256sum }}"
        checksum/secret: "{{ include (print $.Template.BasePath \"/secret.yaml\") . | sha256sum }}"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        runAsGroup: 472
        fsGroup: 472
      containers:
      - name: grafana
        image: grafana/grafana:10.2.0
        ports:
        - name: grafana
          containerPort: 3000
          protocol: TCP
        env:
        - name: GF_PATHS_DATA
          value: /var/lib/grafana
        - name: GF_PATHS_LOGS
          value: /var/log/grafana
        - name: GF_PATHS_PLUGINS
          value: /var/lib/grafana/plugins
        - name: GF_PATHS_PROVISIONING
          value: /etc/grafana/provisioning
        envFrom:
        - secretRef:
            name: grafana-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: grafana
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: grafana
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 472
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: config
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
          readOnly: true
        - name: datasources
          mountPath: /etc/grafana/provisioning/datasources
          readOnly: true
        - name: dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
          readOnly: true
        - name: storage
          mountPath: /var/lib/grafana
        - name: logs
          mountPath: /var/log/grafana
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: grafana-config
      - name: datasources
        configMap:
          name: grafana-config
          items:
          - key: datasources.yaml
            path: datasources.yaml
      - name: dashboards-config
        configMap:
          name: grafana-config
          items:
          - key: dashboards.yaml
            path: dashboards.yaml
      - name: storage
        persistentVolumeClaim:
          claimName: grafana-storage
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      - name: tmp
        emptyDir:
          sizeLimit: 1Gi

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: dashboard
spec:
  type: ClusterIP
  ports:
  - name: grafana
    port: 3000
    targetPort: grafana
    protocol: TCP
  selector:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: dashboard

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: ingress
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: monitoring
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:us-west-2:ACCOUNT_ID:certificate/CERT_ID"
    
    # IP whitelist for internal access
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    external-dns.alpha.kubernetes.io/hostname: "grafana.internal.cultureconnect.ng"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

spec:
  ingressClassName: alb
  tls:
  - hosts:
    - grafana.internal.cultureconnect.ng
    secretName: grafana-tls
  rules:
  - host: grafana.internal.cultureconnect.ng
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
