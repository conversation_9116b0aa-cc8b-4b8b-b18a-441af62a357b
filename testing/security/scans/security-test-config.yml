# Culture Connect Backend - Security Testing Configuration
# Comprehensive security validation for production deployment

# OWASP ZAP Security Scan Configuration
zap_scan:
  target_url: "https://api.cultureconnect.ng"
  scan_types:
    - baseline
    - full_scan
    - api_scan
  
  # Authentication configuration
  authentication:
    method: "form_based"
    login_url: "https://api.cultureconnect.ng/api/v1/auth/login"
    username_field: "email"
    password_field: "password"
    username: "<EMAIL>"
    password: "SecurityTest123!"
    
  # Context configuration
  contexts:
    - name: "Culture Connect API"
      include_regex: "https://api\\.cultureconnect\\.ng/.*"
      exclude_regex: "https://api\\.cultureconnect\\.ng/static/.*"
      
  # Scan policies
  scan_policies:
    - name: "API Security Policy"
      rules:
        - id: 10003  # Vulnerable JS Library
          threshold: "LOW"
        - id: 10020  # X-Frame-Options Header
          threshold: "MEDIUM"
        - id: 10021  # X-Content-Type-Options Header
          threshold: "LOW"
        - id: 10023  # Information Disclosure - Debug Error Messages
          threshold: "LOW"
        - id: 10024  # Information Disclosure - Sensitive Information in URL
          threshold: "MEDIUM"
        - id: 10025  # Information Disclosure - Sensitive Information in HTTP Referrer Header
          threshold: "MEDIUM"
        - id: 10026  # HTTP Parameter Override
          threshold: "MEDIUM"
        - id: 10027  # Information Disclosure - Suspicious Comments
          threshold: "LOW"
        - id: 10028  # Open Redirect
          threshold: "MEDIUM"
        - id: 10029  # Cookie Poisoning
          threshold: "MEDIUM"
        - id: 10030  # User Controllable Charset
          threshold: "MEDIUM"
        - id: 10031  # User Controllable HTML Element Attribute (Potential XSS)
          threshold: "MEDIUM"
        - id: 10032  # Viewstate Scanner
          threshold: "MEDIUM"
        - id: 10033  # Directory Browsing
          threshold: "MEDIUM"
        - id: 10034  # Heartbleed OpenSSL Vulnerability
          threshold: "HIGH"
        - id: 10035  # Strict-Transport-Security Header
          threshold: "LOW"
        - id: 10036  # Server Leaks Version Information via "Server" HTTP Response Header Field
          threshold: "LOW"
        - id: 10037  # Server Leaks Information via "X-Powered-By" HTTP Response Header Field(s)
          threshold: "LOW"
        - id: 10038  # Content Security Policy (CSP) Header Not Set
          threshold: "MEDIUM"
        - id: 10039  # X-Backend-Server Header Information Leak
          threshold: "LOW"
        - id: 10040  # Secure Pages Include Mixed Content
          threshold: "MEDIUM"
        - id: 10041  # HTTP to HTTPS Insecure Transition in Form Post
          threshold: "MEDIUM"
        - id: 10042  # HTTPS to HTTP Insecure Transition in Form Post
          threshold: "MEDIUM"
        - id: 10043  # User Controllable JavaScript Event (XSS)
          threshold: "HIGH"
        - id: 10044  # Big Redirect Detected (Potential Sensitive Information Leak)
          threshold: "LOW"
        - id: 10045  # Source Code Disclosure - /WEB-INF folder
          threshold: "HIGH"
        - id: 10046  # Source Code Disclosure - Git
          threshold: "MEDIUM"
        - id: 10047  # Source Code Disclosure - SVN
          threshold: "MEDIUM"
        - id: 10048  # Remote Code Execution - Shell Shock
          threshold: "HIGH"
        - id: 10049  # Content Cacheability
          threshold: "LOW"
        - id: 10050  # Retrieved from Cache
          threshold: "LOW"
        - id: 10051  # Relative Path Confusion
          threshold: "MEDIUM"
        - id: 10052  # X-ChromeLogger-Data (XCOLD) Header Information Leak
          threshold: "MEDIUM"
        - id: 10053  # Apache Range Header DoS (CVE-2011-3192)
          threshold: "MEDIUM"
        - id: 10054  # Cookie Without SameSite Attribute
          threshold: "LOW"
        - id: 10055  # CSP Scanner
          threshold: "MEDIUM"
        - id: 10056  # X-Debug-Token Information Leak
          threshold: "LOW"
        - id: 10057  # Username Hash Found
          threshold: "LOW"
        - id: 10058  # GET for POST
          threshold: "MEDIUM"
        - id: 10059  # PII Scanner
          threshold: "HIGH"
        - id: 10060  # Secure Cookie Attribute
          threshold: "LOW"
        - id: 10061  # X-AspNet-Version Response Header
          threshold: "LOW"
        - id: 10062  # PII Disclosure
          threshold: "HIGH"
        - id: 10063  # Feature Policy Header Not Set
          threshold: "LOW"
        - id: 10096  # Timestamp Disclosure
          threshold: "LOW"
        - id: 10097  # Hash Disclosure
          threshold: "LOW"
        - id: 10098  # Cross-Domain Misconfiguration
          threshold: "MEDIUM"
        - id: 10099  # Source Code Disclosure
          threshold: "HIGH"
        - id: 10100  # Weak Authentication Method
          threshold: "MEDIUM"
        - id: 10101  # Insecure HTTP Method
          threshold: "MEDIUM"
        - id: 10102  # HTTP Only Site
          threshold: "MEDIUM"
        - id: 10103  # Clickjacking Protection
          threshold: "MEDIUM"
        - id: 10104  # User Agent Fuzzer
          threshold: "LOW"
        - id: 10105  # Weak Hash
          threshold: "MEDIUM"
        - id: 10106  # HTTP Parameter Pollution
          threshold: "MEDIUM"
        - id: 10107  # Httpoxy - Proxy Header Misuse
          threshold: "MEDIUM"
        - id: 10108  # Reverse Tabnabbing
          threshold: "MEDIUM"
        - id: 10109  # Modern Web Application
          threshold: "LOW"
        - id: 10110  # Dangerous JS Functions
          threshold: "LOW"
        - id: 10111  # Authentication Credentials Captured
          threshold: "HIGH"
        - id: 10112  # Session Fixation
          threshold: "MEDIUM"
        - id: 10113  # Session ID in URL Rewrite
          threshold: "MEDIUM"
        - id: 10114  # Authentication Bypass
          threshold: "HIGH"
        - id: 10115  # Authorization Bypass
          threshold: "HIGH"

# Nuclei Security Scan Configuration
nuclei_scan:
  target: "https://api.cultureconnect.ng"
  templates:
    - cves/
    - vulnerabilities/
    - exposures/
    - misconfiguration/
    - technologies/
    - default-logins/
    - file/
    - network/
    - dns/
    - http/
    - ssl/
  
  severity_levels:
    - critical
    - high
    - medium
    - low
    - info
  
  rate_limit: 150  # requests per second
  timeout: 10      # seconds
  retries: 1
  
  # Custom headers
  headers:
    - "User-Agent: Nuclei Security Scanner"
    - "Accept: application/json"

# SSL/TLS Security Testing
ssl_tests:
  target: "api.cultureconnect.ng"
  port: 443
  
  tests:
    - name: "SSL Certificate Validation"
      checks:
        - certificate_expiry
        - certificate_chain
        - certificate_authority
        - certificate_hostname
        - certificate_key_strength
    
    - name: "SSL/TLS Protocol Testing"
      checks:
        - supported_protocols
        - cipher_suites
        - perfect_forward_secrecy
        - hsts_header
        - certificate_transparency
    
    - name: "SSL Vulnerabilities"
      checks:
        - heartbleed
        - poodle
        - beast
        - crime
        - breach
        - freak
        - logjam
        - drown
        - sweet32
        - ticketbleed

# API Security Testing
api_security_tests:
  base_url: "https://api.cultureconnect.ng/api/v1"
  
  authentication_tests:
    - name: "JWT Token Security"
      tests:
        - token_expiration
        - token_signature_validation
        - token_algorithm_confusion
        - token_none_algorithm
        - token_weak_secret
    
    - name: "Authentication Bypass"
      tests:
        - sql_injection_auth
        - nosql_injection_auth
        - ldap_injection_auth
        - parameter_pollution
        - header_injection
    
    - name: "Session Management"
      tests:
        - session_fixation
        - session_hijacking
        - concurrent_sessions
        - session_timeout
        - secure_cookie_flags

  authorization_tests:
    - name: "RBAC Testing"
      tests:
        - privilege_escalation
        - horizontal_privilege_escalation
        - vertical_privilege_escalation
        - role_manipulation
        - permission_bypass
    
    - name: "IDOR Testing"
      tests:
        - direct_object_references
        - parameter_manipulation
        - path_traversal
        - file_inclusion

  input_validation_tests:
    - name: "Injection Attacks"
      tests:
        - sql_injection
        - nosql_injection
        - ldap_injection
        - xpath_injection
        - command_injection
        - code_injection
    
    - name: "XSS Testing"
      tests:
        - reflected_xss
        - stored_xss
        - dom_xss
        - blind_xss
    
    - name: "XXE Testing"
      tests:
        - xml_external_entity
        - xml_bomb
        - xml_injection

  business_logic_tests:
    - name: "Payment Security"
      tests:
        - amount_manipulation
        - currency_manipulation
        - payment_bypass
        - race_conditions
    
    - name: "Booking Security"
      tests:
        - booking_manipulation
        - date_manipulation
        - capacity_bypass
        - pricing_manipulation

# Security Headers Testing
security_headers:
  target: "https://api.cultureconnect.ng"
  
  required_headers:
    - name: "Strict-Transport-Security"
      expected: "max-age=31536000; includeSubDomains"
      severity: "HIGH"
    
    - name: "X-Content-Type-Options"
      expected: "nosniff"
      severity: "MEDIUM"
    
    - name: "X-Frame-Options"
      expected: "DENY"
      severity: "MEDIUM"
    
    - name: "X-XSS-Protection"
      expected: "1; mode=block"
      severity: "MEDIUM"
    
    - name: "Content-Security-Policy"
      expected: "default-src 'self'"
      severity: "HIGH"
    
    - name: "Referrer-Policy"
      expected: "strict-origin-when-cross-origin"
      severity: "LOW"
    
    - name: "Permissions-Policy"
      expected: "geolocation=(), microphone=(), camera=()"
      severity: "LOW"

# Reporting Configuration
reporting:
  formats:
    - json
    - html
    - xml
    - pdf
  
  output_directory: "./reports"
  
  severity_thresholds:
    critical: 0    # No critical vulnerabilities allowed
    high: 2        # Maximum 2 high severity vulnerabilities
    medium: 10     # Maximum 10 medium severity vulnerabilities
    low: 50        # Maximum 50 low severity vulnerabilities
  
  compliance_frameworks:
    - OWASP_TOP_10
    - NIST_CYBERSECURITY_FRAMEWORK
    - ISO_27001
    - PCI_DSS
    - GDPR

# Test Execution Configuration
execution:
  parallel_scans: true
  max_concurrent_scans: 3
  scan_timeout: 3600  # 1 hour
  retry_failed_scans: true
  
  notification:
    slack_webhook: "${SLACK_WEBHOOK_URL}"
    email_recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    
  integration:
    jira_project: "SECURITY"
    create_tickets: true
    ticket_priority_mapping:
      critical: "Highest"
      high: "High"
      medium: "Medium"
      low: "Low"
