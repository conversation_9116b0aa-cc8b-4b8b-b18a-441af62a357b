"""
Concurrent User Simulation Performance Tests for Culture Connect Backend API.

This module provides comprehensive concurrent user simulation testing including:
- Realistic User Journey Simulation (Tourist and Vendor behavior patterns)
- Concurrent Access Pattern Testing (Simultaneous operations across user types)
- Performance Degradation Analysis (Response time analysis under increasing load)
- Mixed User Type Scenarios (Peak usage time simulation with realistic load patterns)

Implements Phase 8.2: Performance Testing Implementation with >1000 concurrent users,
realistic user behavior patterns, and comprehensive performance degradation analysis.
"""

import pytest
import asyncio
import time
import random
import statistics
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional, Tuple
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock

from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VendorStatus
from app.models.booking import Booking, BookingStatus
from app.models.payment_models import Payment, PaymentStatus, PaymentProvider
from app.services.load_testing_service import LoadTestingService
from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.db.session import get_async_session_context
from app.core.security import create_token_pair
from app.core.logging import get_logger

logger = get_logger(__name__)


@pytest.mark.performance
class TestConcurrentUserSimulation:
    """Comprehensive concurrent user simulation performance tests."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client with real app."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    async def db_session(self):
        """Create database session with transaction rollback."""
        async with get_async_session_context() as session:
            transaction = await session.begin()
            try:
                yield session
            finally:
                await transaction.rollback()

    @pytest.fixture
    async def load_testing_service(self):
        """Create load testing service for performance testing."""
        service = LoadTestingService()
        yield service

    @pytest.fixture
    async def performance_monitoring_service(self):
        """Create performance monitoring service for metrics collection."""
        service = PerformanceMonitoringService()
        yield service

    @pytest.fixture
    async def simulation_users_pool(self, db_session: AsyncSession):
        """Create pool of users for concurrent simulation testing."""
        users = []
        for i in range(100):  # Create 100 users for simulation
            user_data = {
                "email": f"sim_user_{i}_{uuid4().hex[:8]}@test.com",
                "first_name": f"SimUser{i}",
                "last_name": "Test",
                "role": UserRole.CUSTOMER if i % 3 != 0 else UserRole.VENDOR,  # 2/3 customers, 1/3 vendors
                "is_active": True,
                "is_verified": True,
                "hashed_password": "$2b$12$test_hash"
            }

            user = User(**user_data)
            db_session.add(user)
            users.append(user)

        await db_session.commit()
        for user in users:
            await db_session.refresh(user)

        return users

    @pytest.fixture
    async def simulation_vendors_pool(self, db_session: AsyncSession, simulation_users_pool: List[User]):
        """Create pool of vendors for concurrent simulation testing."""
        vendors = []
        vendor_users = [user for user in simulation_users_pool if user.role == UserRole.VENDOR]

        for i, user in enumerate(vendor_users):
            vendor_data = {
                "user_id": user.id,
                "business_name": f"Simulation Tours {i}",
                "business_type": VendorType.TOUR_GUIDE,
                "status": VendorStatus.ACTIVE,
                "verified": True,
                "phone": f"+234123{i:06d}",
                "address": f"Lagos, Nigeria - Simulation Zone {i}"
            }

            vendor = Vendor(**vendor_data)
            db_session.add(vendor)
            vendors.append(vendor)

        await db_session.commit()
        for vendor in vendors:
            await db_session.refresh(vendor)

        return vendors

    @pytest.fixture
    def user_behavior_simulator(self):
        """User behavior simulation utility for realistic patterns."""
        class UserBehaviorSimulator:
            def __init__(self):
                self.customer_journey_steps = [
                    "profile_access", "vendor_search", "vendor_details",
                    "booking_creation", "payment_processing", "communication"
                ]
                self.vendor_journey_steps = [
                    "profile_access", "dashboard_overview", "booking_management",
                    "analytics_access", "customer_communication"
                ]

            def get_realistic_delay(self, step_type: str) -> float:
                """Get realistic delay between user actions."""
                delays = {
                    "profile_access": (0.5, 2.0),      # 0.5-2s thinking time
                    "vendor_search": (1.0, 3.0),       # 1-3s search consideration
                    "vendor_details": (2.0, 5.0),      # 2-5s vendor evaluation
                    "booking_creation": (3.0, 8.0),    # 3-8s booking form completion
                    "payment_processing": (1.0, 3.0),  # 1-3s payment confirmation
                    "communication": (2.0, 6.0),       # 2-6s message composition
                    "dashboard_overview": (1.0, 3.0),  # 1-3s dashboard review
                    "booking_management": (2.0, 5.0),  # 2-5s booking review
                    "analytics_access": (1.0, 4.0),    # 1-4s analytics review
                }
                min_delay, max_delay = delays.get(step_type, (0.5, 2.0))
                return random.uniform(min_delay, max_delay)

            def should_complete_journey(self, step_index: int, total_steps: int) -> bool:
                """Determine if user should complete the journey (realistic dropout)."""
                completion_rates = [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]  # Decreasing completion rate
                rate = completion_rates[min(step_index, len(completion_rates) - 1)]
                return random.random() < rate

        return UserBehaviorSimulator()

    @pytest.mark.asyncio
    async def test_realistic_user_journey_simulation(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        simulation_users_pool: List[User],
        simulation_vendors_pool: List[Vendor],
        user_behavior_simulator,
        performance_monitoring_service: PerformanceMonitoringService
    ):
        """
        Test Realistic User Journey Simulation with tourist and vendor behavior patterns.

        Validates: Tourist journey (search → book → pay → communicate), Vendor journey (manage → respond → dashboard → analytics)
        """
        correlation_id = f"user_journey_sim_{uuid4().hex[:8]}"

        logger.info(f"Starting realistic user journey simulation with correlation_id: {correlation_id}")

        # Step 1: Tourist User Journey Simulation
        async def simulate_tourist_journey(customer: User) -> Dict[str, Any]:
            """Simulate realistic tourist user journey with timing patterns."""
            journey_start = time.perf_counter()
            journey_data = {
                "user_id": customer.id,
                "user_type": "tourist",
                "steps_completed": [],
                "step_times": {},
                "total_time": 0,
                "completed_successfully": False
            }

            # Create auth headers
            token_data = {
                "sub": str(customer.id),
                "email": customer.email,
                "role": customer.role.value,
                "scopes": ["customer:read", "customer:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            try:
                # Step 1: Profile Access
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("profile_access"))
                step_start = time.perf_counter()
                profile_response = await async_client.get("/api/v1/users/profile", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if profile_response.status_code == 200:
                    journey_data["steps_completed"].append("profile_access")
                    journey_data["step_times"]["profile_access"] = step_time

                if not user_behavior_simulator.should_complete_journey(0, 6):
                    return journey_data

                # Step 2: Vendor Search
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("vendor_search"))
                step_start = time.perf_counter()
                search_response = await async_client.get(
                    "/api/v1/vendors/search",
                    params={"location": "Lagos", "service_type": "cultural_tour"},
                    headers=headers
                )
                step_time = (time.perf_counter() - step_start) * 1000

                if search_response.status_code == 200:
                    journey_data["steps_completed"].append("vendor_search")
                    journey_data["step_times"]["vendor_search"] = step_time

                if not user_behavior_simulator.should_complete_journey(1, 6):
                    return journey_data

                # Step 3: Vendor Details
                vendor = random.choice(simulation_vendors_pool)
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("vendor_details"))
                step_start = time.perf_counter()
                details_response = await async_client.get(f"/api/v1/vendors/{vendor.id}", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if details_response.status_code == 200:
                    journey_data["steps_completed"].append("vendor_details")
                    journey_data["step_times"]["vendor_details"] = step_time

                if not user_behavior_simulator.should_complete_journey(2, 6):
                    return journey_data

                # Step 4: Booking Creation
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("booking_creation"))
                booking_data = {
                    "vendor_id": vendor.id,
                    "service_id": 1,
                    "booking_date": (datetime.now() + timedelta(days=random.randint(3, 14))).isoformat(),
                    "total_amount": random.uniform(150.0, 500.0),
                    "number_of_guests": random.randint(1, 4),
                    "special_requests": f"Tourist journey simulation {uuid4().hex[:8]}"
                }

                step_start = time.perf_counter()
                booking_response = await async_client.post("/api/v1/bookings/", json=booking_data, headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if booking_response.status_code == 201:
                    journey_data["steps_completed"].append("booking_creation")
                    journey_data["step_times"]["booking_creation"] = step_time
                    booking_id = booking_response.json().get("id")

                    if not user_behavior_simulator.should_complete_journey(3, 6):
                        return journey_data

                    # Step 5: Payment Processing
                    await asyncio.sleep(user_behavior_simulator.get_realistic_delay("payment_processing"))
                    payment_data = {
                        "booking_id": booking_id,
                        "amount": Decimal(str(booking_data["total_amount"])),
                        "currency": "NGN",
                        "payment_method": "card"
                    }

                    step_start = time.perf_counter()
                    with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
                        mock_payment.return_value = {
                            "status": "success",
                            "data": {"reference": f"ref_{uuid4().hex[:8]}"}
                        }

                        payment_response = await async_client.post(
                            "/api/v1/payments/initialize",
                            json=payment_data,
                            headers=headers
                        )
                    step_time = (time.perf_counter() - step_start) * 1000

                    if payment_response.status_code == 200:
                        journey_data["steps_completed"].append("payment_processing")
                        journey_data["step_times"]["payment_processing"] = step_time

                        if not user_behavior_simulator.should_complete_journey(4, 6):
                            return journey_data

                        # Step 6: Communication
                        await asyncio.sleep(user_behavior_simulator.get_realistic_delay("communication"))
                        message_data = {
                            "booking_id": booking_id,
                            "message_type": "CUSTOMER_MESSAGE",
                            "content": f"Looking forward to the experience! Journey simulation {uuid4().hex[:8]}",
                            "sender_type": "customer"
                        }

                        step_start = time.perf_counter()
                        message_response = await async_client.post(
                            "/api/v1/booking-communication/messages",
                            json=message_data,
                            headers=headers
                        )
                        step_time = (time.perf_counter() - step_start) * 1000

                        if message_response.status_code == 201:
                            journey_data["steps_completed"].append("communication")
                            journey_data["step_times"]["communication"] = step_time
                            journey_data["completed_successfully"] = True

            except Exception as e:
                logger.warning(f"Tourist journey simulation error: {e}")

            journey_data["total_time"] = (time.perf_counter() - journey_start) * 1000
            return journey_data

        # Execute 50 concurrent tourist journeys
        customers = [user for user in simulation_users_pool if user.role == UserRole.CUSTOMER][:50]
        tourist_tasks = [simulate_tourist_journey(customer) for customer in customers]

        tourist_results = await asyncio.gather(*tourist_tasks, return_exceptions=True)

        # Analyze tourist journey performance
        successful_tourists = [r for r in tourist_results if isinstance(r, dict)]
        completed_journeys = [r for r in successful_tourists if r["completed_successfully"]]

        # Validate tourist journey performance
        assert len(successful_tourists) >= 45  # >90% successful journey starts
        completion_rate = len(completed_journeys) / len(successful_tourists) if successful_tourists else 0
        assert completion_rate >= 0.3  # >30% completion rate (realistic with dropouts)

        # Analyze step performance
        step_performance = {}
        for step in ["profile_access", "vendor_search", "vendor_details", "booking_creation", "payment_processing", "communication"]:
            step_times = [r["step_times"].get(step, 0) for r in successful_tourists if step in r["step_times"]]
            if step_times:
                step_performance[step] = {
                    "count": len(step_times),
                    "avg_time": statistics.mean(step_times),
                    "max_time": max(step_times)
                }

        # Validate step performance targets
        assert step_performance["profile_access"]["avg_time"] < 200    # <200ms profile access
        assert step_performance["vendor_search"]["avg_time"] < 200     # <200ms search
        assert step_performance["booking_creation"]["avg_time"] < 500  # <500ms booking creation

        logger.info(f"Tourist journey simulation: {len(completed_journeys)} completed journeys, "
                   f"completion rate: {completion_rate:.2%}")

        # Step 2: Vendor User Journey Simulation
        async def simulate_vendor_journey(vendor_user: User, vendor: Vendor) -> Dict[str, Any]:
            """Simulate realistic vendor user journey with management patterns."""
            journey_start = time.perf_counter()
            journey_data = {
                "user_id": vendor_user.id,
                "vendor_id": vendor.id,
                "user_type": "vendor",
                "steps_completed": [],
                "step_times": {},
                "total_time": 0,
                "completed_successfully": False
            }

            # Create auth headers
            token_data = {
                "sub": str(vendor_user.id),
                "email": vendor_user.email,
                "role": vendor_user.role.value,
                "scopes": ["vendor:read", "vendor:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            try:
                # Step 1: Profile Access
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("profile_access"))
                step_start = time.perf_counter()
                profile_response = await async_client.get("/api/v1/vendors/profile", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if profile_response.status_code == 200:
                    journey_data["steps_completed"].append("profile_access")
                    journey_data["step_times"]["profile_access"] = step_time

                # Step 2: Dashboard Overview
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("dashboard_overview"))
                step_start = time.perf_counter()
                dashboard_response = await async_client.get("/api/v1/vendors/dashboard/overview", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if dashboard_response.status_code == 200:
                    journey_data["steps_completed"].append("dashboard_overview")
                    journey_data["step_times"]["dashboard_overview"] = step_time

                # Step 3: Booking Management
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("booking_management"))
                step_start = time.perf_counter()
                bookings_response = await async_client.get("/api/v1/vendors/bookings", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if bookings_response.status_code == 200:
                    journey_data["steps_completed"].append("booking_management")
                    journey_data["step_times"]["booking_management"] = step_time

                # Step 4: Analytics Access
                await asyncio.sleep(user_behavior_simulator.get_realistic_delay("analytics_access"))
                step_start = time.perf_counter()
                analytics_response = await async_client.get("/api/v1/analytics/vendor/dashboard", headers=headers)
                step_time = (time.perf_counter() - step_start) * 1000

                if analytics_response.status_code == 200:
                    journey_data["steps_completed"].append("analytics_access")
                    journey_data["step_times"]["analytics_access"] = step_time
                    journey_data["completed_successfully"] = True

            except Exception as e:
                logger.warning(f"Vendor journey simulation error: {e}")

            journey_data["total_time"] = (time.perf_counter() - journey_start) * 1000
            return journey_data

        # Execute 30 concurrent vendor journeys
        vendor_users = [user for user in simulation_users_pool if user.role == UserRole.VENDOR][:30]
        vendor_tasks = []
        for i, vendor_user in enumerate(vendor_users):
            vendor = simulation_vendors_pool[i % len(simulation_vendors_pool)]
            vendor_tasks.append(simulate_vendor_journey(vendor_user, vendor))

        vendor_results = await asyncio.gather(*vendor_tasks, return_exceptions=True)

        # Analyze vendor journey performance
        successful_vendors = [r for r in vendor_results if isinstance(r, dict)]
        completed_vendor_journeys = [r for r in successful_vendors if r["completed_successfully"]]

        # Validate vendor journey performance
        assert len(successful_vendors) >= 27  # >90% successful journey starts
        vendor_completion_rate = len(completed_vendor_journeys) / len(successful_vendors) if successful_vendors else 0
        assert vendor_completion_rate >= 0.8  # >80% completion rate for vendors

        # Analyze vendor step performance
        vendor_step_performance = {}
        for step in ["profile_access", "dashboard_overview", "booking_management", "analytics_access"]:
            step_times = [r["step_times"].get(step, 0) for r in successful_vendors if step in r["step_times"]]
            if step_times:
                vendor_step_performance[step] = {
                    "count": len(step_times),
                    "avg_time": statistics.mean(step_times),
                    "max_time": max(step_times)
                }

        # Validate vendor step performance targets
        assert vendor_step_performance["dashboard_overview"]["avg_time"] < 200  # <200ms dashboard
        assert vendor_step_performance["analytics_access"]["avg_time"] < 200    # <200ms analytics

        logger.info(f"Vendor journey simulation: {len(completed_vendor_journeys)} completed journeys, "
                   f"completion rate: {vendor_completion_rate:.2%}")

        # Step 3: Overall Journey Performance Analysis
        total_successful_journeys = len(completed_journeys) + len(completed_vendor_journeys)
        total_journey_attempts = len(successful_tourists) + len(successful_vendors)

        overall_completion_rate = total_successful_journeys / total_journey_attempts if total_journey_attempts else 0

        logger.info(f"Realistic user journey simulation completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Overall performance: {total_successful_journeys} completed journeys, "
                   f"overall completion rate: {overall_completion_rate:.2%}")

        # Validate overall performance
        assert total_successful_journeys >= 25  # At least 25 completed journeys
        assert overall_completion_rate >= 0.4   # >40% overall completion rate

    @pytest.mark.asyncio
    async def test_concurrent_access_pattern_testing(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        simulation_users_pool: List[User],
        simulation_vendors_pool: List[Vendor],
        load_testing_service: LoadTestingService
    ):
        """
        Test Concurrent Access Pattern Testing with simultaneous operations across user types.

        Validates: Simultaneous booking creation, concurrent vendor dashboard access, real-time communication, payment processing
        """
        correlation_id = f"concurrent_access_{uuid4().hex[:8]}"

        logger.info(f"Starting concurrent access pattern testing with correlation_id: {correlation_id}")

        # Step 1: Simultaneous Booking Creation Testing
        async def create_simultaneous_booking(customer: User, vendor: Vendor, booking_index: int) -> Dict[str, Any]:
            """Create booking simultaneously with other users."""
            start_time = time.perf_counter()

            token_data = {
                "sub": str(customer.id),
                "email": customer.email,
                "role": customer.role.value,
                "scopes": ["customer:read", "customer:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            booking_data = {
                "vendor_id": vendor.id,
                "service_id": 1,
                "booking_date": (datetime.now() + timedelta(days=5 + booking_index % 10)).isoformat(),
                "total_amount": 200.0 + (booking_index * 10),
                "number_of_guests": 1 + (booking_index % 4),
                "special_requests": f"Concurrent booking test {booking_index}"
            }

            try:
                response = await async_client.post("/api/v1/bookings/", json=booking_data, headers=headers)
                response_time = (time.perf_counter() - start_time) * 1000

                return {
                    "booking_index": booking_index,
                    "customer_id": customer.id,
                    "vendor_id": vendor.id,
                    "status_code": response.status_code,
                    "response_time_ms": response_time,
                    "booking_id": response.json().get("id") if response.status_code == 201 else None,
                    "success": response.status_code == 201
                }
            except Exception as e:
                response_time = (time.perf_counter() - start_time) * 1000
                logger.warning(f"Concurrent booking error: {e}")
                return {
                    "booking_index": booking_index,
                    "customer_id": customer.id,
                    "vendor_id": vendor.id,
                    "status_code": 500,
                    "response_time_ms": response_time,
                    "booking_id": None,
                    "success": False,
                    "error": str(e)
                }

        # Execute 100 simultaneous booking creations
        customers = [user for user in simulation_users_pool if user.role == UserRole.CUSTOMER]
        booking_tasks = []

        for i in range(100):
            customer = customers[i % len(customers)]
            vendor = simulation_vendors_pool[i % len(simulation_vendors_pool)]
            booking_tasks.append(create_simultaneous_booking(customer, vendor, i))

        booking_start_time = time.perf_counter()
        booking_results = await asyncio.gather(*booking_tasks, return_exceptions=True)
        booking_end_time = time.perf_counter()

        # Analyze simultaneous booking performance
        successful_bookings = [r for r in booking_results if isinstance(r, dict) and r["success"]]
        booking_response_times = [r["response_time_ms"] for r in successful_bookings]

        assert len(successful_bookings) >= 80  # >80% success rate for simultaneous bookings
        assert statistics.mean(booking_response_times) < 600  # <600ms average response time under load
        assert max(booking_response_times) < 1200  # <1200ms max response time

        total_booking_time = (booking_end_time - booking_start_time) * 1000
        booking_throughput = len(successful_bookings) / (total_booking_time / 1000)  # bookings per second

        logger.info(f"Simultaneous booking creation: {len(successful_bookings)} successful, "
                   f"avg time: {statistics.mean(booking_response_times):.2f}ms, "
                   f"throughput: {booking_throughput:.2f} bookings/sec")

        # Step 2: Concurrent Vendor Dashboard Access Testing
        async def access_vendor_dashboard_concurrent(vendor_user: User, access_index: int) -> Dict[str, Any]:
            """Access vendor dashboard concurrently with other vendors."""
            start_time = time.perf_counter()

            token_data = {
                "sub": str(vendor_user.id),
                "email": vendor_user.email,
                "role": vendor_user.role.value,
                "scopes": ["vendor:read", "vendor:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            dashboard_operations = []

            try:
                # Dashboard overview
                overview_start = time.perf_counter()
                overview_response = await async_client.get("/api/v1/vendors/dashboard/overview", headers=headers)
                overview_time = (time.perf_counter() - overview_start) * 1000
                dashboard_operations.append(("overview", overview_response.status_code, overview_time))

                # Vendor bookings
                bookings_start = time.perf_counter()
                bookings_response = await async_client.get("/api/v1/vendors/bookings", headers=headers)
                bookings_time = (time.perf_counter() - bookings_start) * 1000
                dashboard_operations.append(("bookings", bookings_response.status_code, bookings_time))

                # Analytics access
                analytics_start = time.perf_counter()
                analytics_response = await async_client.get("/api/v1/analytics/vendor/dashboard", headers=headers)
                analytics_time = (time.perf_counter() - analytics_start) * 1000
                dashboard_operations.append(("analytics", analytics_response.status_code, analytics_time))

                total_time = (time.perf_counter() - start_time) * 1000
                success = all(op[1] == 200 for op in dashboard_operations)

                return {
                    "access_index": access_index,
                    "vendor_user_id": vendor_user.id,
                    "operations": dashboard_operations,
                    "total_time_ms": total_time,
                    "success": success
                }

            except Exception as e:
                total_time = (time.perf_counter() - start_time) * 1000
                logger.warning(f"Concurrent dashboard access error: {e}")
                return {
                    "access_index": access_index,
                    "vendor_user_id": vendor_user.id,
                    "operations": dashboard_operations,
                    "total_time_ms": total_time,
                    "success": False,
                    "error": str(e)
                }

        # Execute 60 concurrent vendor dashboard accesses
        vendor_users = [user for user in simulation_users_pool if user.role == UserRole.VENDOR]
        dashboard_tasks = []

        for i in range(60):
            vendor_user = vendor_users[i % len(vendor_users)]
            dashboard_tasks.append(access_vendor_dashboard_concurrent(vendor_user, i))

        dashboard_start_time = time.perf_counter()
        dashboard_results = await asyncio.gather(*dashboard_tasks, return_exceptions=True)
        dashboard_end_time = time.perf_counter()

        # Analyze concurrent dashboard access performance
        successful_dashboards = [r for r in dashboard_results if isinstance(r, dict) and r["success"]]
        dashboard_total_times = [r["total_time_ms"] for r in successful_dashboards]

        assert len(successful_dashboards) >= 54  # >90% success rate for dashboard access
        assert statistics.mean(dashboard_total_times) < 800  # <800ms average total time

        # Analyze individual dashboard operation performance
        overview_times = []
        bookings_times = []
        analytics_times = []

        for result in successful_dashboards:
            for op_name, status_code, op_time in result["operations"]:
                if status_code == 200:
                    if op_name == "overview":
                        overview_times.append(op_time)
                    elif op_name == "bookings":
                        bookings_times.append(op_time)
                    elif op_name == "analytics":
                        analytics_times.append(op_time)

        assert statistics.mean(overview_times) < 250   # <250ms overview under concurrent load
        assert statistics.mean(bookings_times) < 250   # <250ms bookings under concurrent load
        assert statistics.mean(analytics_times) < 250  # <250ms analytics under concurrent load

        total_dashboard_time = (dashboard_end_time - dashboard_start_time) * 1000
        dashboard_throughput = len(successful_dashboards) / (total_dashboard_time / 1000)

        logger.info(f"Concurrent dashboard access: {len(successful_dashboards)} successful, "
                   f"avg time: {statistics.mean(dashboard_total_times):.2f}ms, "
                   f"throughput: {dashboard_throughput:.2f} accesses/sec")

        # Step 3: Real-time Communication Concurrent Message Delivery
        async def send_concurrent_message(customer: User, booking_id: Optional[int], message_index: int) -> Dict[str, Any]:
            """Send message concurrently with other users."""
            if not booking_id:
                return {"message_index": message_index, "success": False, "reason": "no_booking_id"}

            start_time = time.perf_counter()

            token_data = {
                "sub": str(customer.id),
                "email": customer.email,
                "role": customer.role.value,
                "scopes": ["customer:read", "customer:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            message_data = {
                "booking_id": booking_id,
                "message_type": "CUSTOMER_MESSAGE",
                "content": f"Concurrent message test {message_index} - {uuid4().hex[:8]}",
                "sender_type": "customer"
            }

            try:
                response = await async_client.post(
                    "/api/v1/booking-communication/messages",
                    json=message_data,
                    headers=headers
                )
                response_time = (time.perf_counter() - start_time) * 1000

                return {
                    "message_index": message_index,
                    "customer_id": customer.id,
                    "booking_id": booking_id,
                    "status_code": response.status_code,
                    "response_time_ms": response_time,
                    "success": response.status_code == 201
                }
            except Exception as e:
                response_time = (time.perf_counter() - start_time) * 1000
                logger.warning(f"Concurrent message error: {e}")
                return {
                    "message_index": message_index,
                    "customer_id": customer.id,
                    "booking_id": booking_id,
                    "status_code": 500,
                    "response_time_ms": response_time,
                    "success": False,
                    "error": str(e)
                }

        # Use successful bookings for message testing
        message_tasks = []
        for i, booking_result in enumerate(successful_bookings[:50]):  # Use first 50 successful bookings
            customer_id = booking_result["customer_id"]
            customer = next((u for u in customers if u.id == customer_id), None)
            if customer:
                message_tasks.append(send_concurrent_message(customer, booking_result["booking_id"], i))

        if message_tasks:  # Only run if we have bookings to message about
            message_start_time = time.perf_counter()
            message_results = await asyncio.gather(*message_tasks, return_exceptions=True)
            message_end_time = time.perf_counter()

            # Analyze concurrent message performance
            successful_messages = [r for r in message_results if isinstance(r, dict) and r["success"]]
            message_response_times = [r["response_time_ms"] for r in successful_messages]

            if message_response_times:
                assert len(successful_messages) >= len(message_tasks) * 0.8  # >80% success rate
                assert statistics.mean(message_response_times) < 600  # <600ms average response time

                total_message_time = (message_end_time - message_start_time) * 1000
                message_throughput = len(successful_messages) / (total_message_time / 1000)

                logger.info(f"Concurrent message delivery: {len(successful_messages)} successful, "
                           f"avg time: {statistics.mean(message_response_times):.2f}ms, "
                           f"throughput: {message_throughput:.2f} messages/sec")

        # Step 4: Overall Concurrent Access Performance Summary
        total_concurrent_operations = len(successful_bookings) + len(successful_dashboards) + len(successful_messages if message_tasks else [])

        logger.info(f"Concurrent access pattern testing completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Total concurrent operations: {total_concurrent_operations}")

        # Validate overall concurrent access performance
        assert total_concurrent_operations >= 150  # At least 150 successful concurrent operations

    @pytest.mark.asyncio
    async def test_performance_degradation_analysis_and_mixed_scenarios(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        simulation_users_pool: List[User],
        simulation_vendors_pool: List[Vendor],
        performance_monitoring_service: PerformanceMonitoringService
    ):
        """
        Test Performance Degradation Analysis and Mixed User Type Scenarios with peak usage simulation.

        Validates: Response time analysis under increasing load, breaking point identification, mixed user scenarios
        """
        correlation_id = f"performance_degradation_{uuid4().hex[:8]}"

        logger.info(f"Starting performance degradation analysis with correlation_id: {correlation_id}")

        # Step 1: Progressive Load Testing with Performance Degradation Analysis
        load_levels = [25, 50, 100, 200, 400]  # Progressive load levels
        performance_data = []

        async def execute_mixed_user_operations(users_subset: List[User], vendors_subset: List[Vendor], load_level: int) -> Dict[str, Any]:
            """Execute mixed user operations at specified load level."""
            start_time = time.perf_counter()

            async def single_user_operation(user: User, operation_index: int) -> Dict[str, Any]:
                """Execute single user operation based on user type."""
                op_start = time.perf_counter()

                token_data = {
                    "sub": str(user.id),
                    "email": user.email,
                    "role": user.role.value,
                    "scopes": ["customer:read", "customer:write"] if user.role == UserRole.CUSTOMER else ["vendor:read", "vendor:write"]
                }
                tokens = create_token_pair(token_data)
                headers = {"Authorization": f"Bearer {tokens['access_token']}"}

                operations_completed = []

                try:
                    if user.role == UserRole.CUSTOMER:
                        # Customer operations
                        # Profile access
                        profile_response = await async_client.get("/api/v1/users/profile", headers=headers)
                        operations_completed.append(("profile", profile_response.status_code))

                        # Vendor search
                        search_response = await async_client.get(
                            "/api/v1/vendors/search",
                            params={"location": "Lagos"},
                            headers=headers
                        )
                        operations_completed.append(("search", search_response.status_code))

                        # Analytics access
                        analytics_response = await async_client.get("/api/v1/analytics/user/dashboard", headers=headers)
                        operations_completed.append(("analytics", analytics_response.status_code))

                    else:  # Vendor user
                        # Vendor operations
                        # Dashboard overview
                        dashboard_response = await async_client.get("/api/v1/vendors/dashboard/overview", headers=headers)
                        operations_completed.append(("dashboard", dashboard_response.status_code))

                        # Vendor bookings
                        bookings_response = await async_client.get("/api/v1/vendors/bookings", headers=headers)
                        operations_completed.append(("bookings", bookings_response.status_code))

                        # Vendor analytics
                        vendor_analytics_response = await async_client.get("/api/v1/analytics/vendor/dashboard", headers=headers)
                        operations_completed.append(("vendor_analytics", vendor_analytics_response.status_code))

                    operation_time = (time.perf_counter() - op_start) * 1000
                    success = all(op[1] in [200, 201] for op in operations_completed)

                    return {
                        "operation_index": operation_index,
                        "user_id": user.id,
                        "user_type": user.role.value,
                        "operations": operations_completed,
                        "response_time_ms": operation_time,
                        "success": success
                    }

                except Exception as e:
                    operation_time = (time.perf_counter() - op_start) * 1000
                    logger.warning(f"Mixed user operation error: {e}")
                    return {
                        "operation_index": operation_index,
                        "user_id": user.id,
                        "user_type": user.role.value,
                        "operations": operations_completed,
                        "response_time_ms": operation_time,
                        "success": False,
                        "error": str(e)
                    }

            # Execute operations for current load level
            operation_tasks = []
            for i in range(load_level):
                user = users_subset[i % len(users_subset)]
                operation_tasks.append(single_user_operation(user, i))

            # Execute all operations concurrently
            operation_results = await asyncio.gather(*operation_tasks, return_exceptions=True)
            total_time = (time.perf_counter() - start_time) * 1000

            # Analyze results
            successful_operations = [r for r in operation_results if isinstance(r, dict) and r["success"]]
            response_times = [r["response_time_ms"] for r in successful_operations]

            # Separate by user type
            customer_operations = [r for r in successful_operations if r["user_type"] == "CUSTOMER"]
            vendor_operations = [r for r in successful_operations if r["user_type"] == "VENDOR"]

            customer_times = [r["response_time_ms"] for r in customer_operations]
            vendor_times = [r["response_time_ms"] for r in vendor_operations]

            return {
                "load_level": load_level,
                "total_operations": len(operation_tasks),
                "successful_operations": len(successful_operations),
                "success_rate": len(successful_operations) / len(operation_tasks) if operation_tasks else 0,
                "average_response_time": statistics.mean(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "customer_avg_time": statistics.mean(customer_times) if customer_times else 0,
                "vendor_avg_time": statistics.mean(vendor_times) if vendor_times else 0,
                "customer_operations": len(customer_operations),
                "vendor_operations": len(vendor_operations),
                "total_duration_ms": total_time,
                "throughput_ops_per_sec": len(successful_operations) / (total_time / 1000) if total_time > 0 else 0
            }

        # Execute progressive load testing
        for load_level in load_levels:
            logger.info(f"Testing load level: {load_level} concurrent mixed operations")

            # Select users for this load level
            users_subset = simulation_users_pool[:min(load_level, len(simulation_users_pool))]
            vendors_subset = simulation_vendors_pool[:min(load_level // 3, len(simulation_vendors_pool))]

            # Execute mixed operations
            load_result = await execute_mixed_user_operations(users_subset, vendors_subset, load_level)
            performance_data.append(load_result)

            logger.info(f"Load level {load_level}: {load_result['successful_operations']} successful ops, "
                       f"avg time: {load_result['average_response_time']:.2f}ms, "
                       f"success rate: {load_result['success_rate']:.2%}")

            # Small delay between load levels
            await asyncio.sleep(0.5)

        # Step 2: Performance Degradation Analysis

        # Validate success rates remain acceptable across load levels
        for data in performance_data:
            assert data["success_rate"] >= 0.85  # >85% success rate at all load levels

        # Analyze response time degradation
        response_times = [data["average_response_time"] for data in performance_data]
        baseline_time = response_times[0]  # First load level as baseline
        max_time = max(response_times)

        # Calculate degradation factor
        degradation_factor = max_time / baseline_time if baseline_time > 0 else 1

        # Validate acceptable degradation
        assert degradation_factor < 4.0  # Response time shouldn't degrade more than 4x
        assert max_time < 1500  # Max response time should stay under 1.5s

        # Analyze throughput degradation
        throughputs = [data["throughput_ops_per_sec"] for data in performance_data]
        peak_throughput = max(throughputs)
        min_throughput = min(throughputs)

        # Validate throughput doesn't degrade too much
        throughput_retention = min_throughput / peak_throughput if peak_throughput > 0 else 0
        assert throughput_retention >= 0.6  # Throughput should retain at least 60% at high load

        # Step 3: Breaking Point Identification
        breaking_point_data = None
        for data in performance_data:
            if data["success_rate"] < 0.9 or data["average_response_time"] > 1000:
                breaking_point_data = data
                break

        if breaking_point_data:
            logger.info(f"Breaking point identified at load level: {breaking_point_data['load_level']}, "
                       f"success rate: {breaking_point_data['success_rate']:.2%}, "
                       f"avg response time: {breaking_point_data['average_response_time']:.2f}ms")
        else:
            logger.info("No breaking point reached within tested load levels")

        # Step 4: Mixed User Type Performance Analysis

        # Analyze customer vs vendor performance differences
        customer_avg_times = [data["customer_avg_time"] for data in performance_data if data["customer_avg_time"] > 0]
        vendor_avg_times = [data["vendor_avg_time"] for data in performance_data if data["vendor_avg_time"] > 0]

        if customer_avg_times and vendor_avg_times:
            customer_overall_avg = statistics.mean(customer_avg_times)
            vendor_overall_avg = statistics.mean(vendor_avg_times)

            # Validate both user types perform within acceptable ranges
            assert customer_overall_avg < 800  # <800ms average for customer operations
            assert vendor_overall_avg < 800    # <800ms average for vendor operations

            # Performance difference should be reasonable
            performance_difference = abs(customer_overall_avg - vendor_overall_avg)
            assert performance_difference < 300  # <300ms difference between user types

            logger.info(f"User type performance - Customers: {customer_overall_avg:.2f}ms, "
                       f"Vendors: {vendor_overall_avg:.2f}ms")

        # Step 5: Peak Usage Time Simulation
        async def simulate_peak_usage_scenario() -> Dict[str, Any]:
            """Simulate realistic peak usage with mixed user behavior."""
            peak_start = time.perf_counter()

            # Simulate realistic peak usage pattern (70% customers, 30% vendors)
            peak_customers = [user for user in simulation_users_pool if user.role == UserRole.CUSTOMER][:70]
            peak_vendors = [user for user in simulation_users_pool if user.role == UserRole.VENDOR][:30]

            peak_tasks = []

            # Customer peak operations
            for i, customer in enumerate(peak_customers):
                # Stagger operations to simulate realistic arrival pattern
                delay = random.uniform(0, 2.0)  # 0-2 second stagger

                async def delayed_customer_operation(user, delay_time):
                    await asyncio.sleep(delay_time)
                    return await single_user_operation(user, i)

                peak_tasks.append(delayed_customer_operation(customer, delay))

            # Vendor peak operations
            for i, vendor_user in enumerate(peak_vendors):
                delay = random.uniform(0, 1.0)  # 0-1 second stagger for vendors

                async def delayed_vendor_operation(user, delay_time):
                    await asyncio.sleep(delay_time)
                    return await single_user_operation(user, i + len(peak_customers))

                peak_tasks.append(delayed_vendor_operation(vendor_user, delay))

            # Execute peak scenario
            peak_results = await asyncio.gather(*peak_tasks, return_exceptions=True)
            peak_duration = (time.perf_counter() - peak_start) * 1000

            # Analyze peak performance
            successful_peak = [r for r in peak_results if isinstance(r, dict) and r["success"]]
            peak_response_times = [r["response_time_ms"] for r in successful_peak]

            return {
                "total_peak_operations": len(peak_tasks),
                "successful_peak_operations": len(successful_peak),
                "peak_success_rate": len(successful_peak) / len(peak_tasks) if peak_tasks else 0,
                "peak_avg_response_time": statistics.mean(peak_response_times) if peak_response_times else 0,
                "peak_max_response_time": max(peak_response_times) if peak_response_times else 0,
                "peak_duration_ms": peak_duration,
                "peak_throughput": len(successful_peak) / (peak_duration / 1000) if peak_duration > 0 else 0
            }

        # Execute peak usage simulation
        peak_result = await simulate_peak_usage_scenario()

        # Validate peak usage performance
        assert peak_result["peak_success_rate"] >= 0.8   # >80% success rate during peak
        assert peak_result["peak_avg_response_time"] < 1000  # <1s average response time during peak
        assert peak_result["peak_throughput"] >= 20     # >20 operations/second during peak

        logger.info(f"Peak usage simulation: {peak_result['successful_peak_operations']} successful ops, "
                   f"success rate: {peak_result['peak_success_rate']:.2%}, "
                   f"avg time: {peak_result['peak_avg_response_time']:.2f}ms, "
                   f"throughput: {peak_result['peak_throughput']:.2f} ops/sec")

        # Step 6: Overall Performance Summary
        total_operations_tested = sum(data["successful_operations"] for data in performance_data) + peak_result["successful_peak_operations"]

        logger.info(f"Performance degradation analysis completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Total operations tested: {total_operations_tested}, "
                   f"degradation factor: {degradation_factor:.2f}x, "
                   f"throughput retention: {throughput_retention:.2%}")

        # Validate overall performance degradation analysis
        assert total_operations_tested >= 500  # At least 500 operations tested across all scenarios
        assert degradation_factor < 4.0        # Acceptable performance degradation
        assert throughput_retention >= 0.6     # Acceptable throughput retention
