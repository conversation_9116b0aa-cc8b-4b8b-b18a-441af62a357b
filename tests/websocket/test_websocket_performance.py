"""
Performance tests for WebSocket functionality.

This module provides comprehensive performance tests for WebSocket infrastructure including:
- Connection establishment and authentication performance
- Message delivery and processing performance
- Bulk operations and high-throughput scenarios
- Concurrent connection handling
- Memory and resource usage validation

Implements Task 6.1.1 Phase 5 requirements with performance target validation.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from concurrent.futures import ThreadPoolExecutor
import statistics

from app.services.websocket_services import (
    WebSocketConnectionService, WebSocketEventService, 
    UserPresenceService, WebSocketMetricsService
)
from app.schemas.websocket_schemas import (
    WebSocketConnectionCreate, WebSocketEventCreate,
    EventTypeEnum, EventPriorityEnum
)
from app.api.v1.endpoints.websocket_endpoints import connection_manager


@pytest.mark.performance
class TestWebSocketConnectionPerformance:
    """Performance tests for WebSocket connection operations."""
    
    async def test_connection_creation_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_connection_factory,
        performance_timer
    ):
        """Test connection creation performance under load."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        
        # Mock successful operations
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.connection_id = "test_conn"
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_connection = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=None)
        
        with patch.object(service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        with patch.object(service, '_send_connection_notifications'):
            pass
        
        # Act - Create multiple connections
        connection_times = []
        num_connections = 100
        
        for i in range(num_connections):
            connection_data = WebSocketConnectionCreate(
                connection_id=f"perf_conn_{i}",
                user_id=mock_websocket_user.id,
                client_type="web"
            )
            
            performance_timer.start()
            await service.create_connection(connection_data, mock_websocket_user)
            performance_timer.stop()
            
            connection_times.append(performance_timer.elapsed_ms())
        
        # Assert performance targets
        avg_time = statistics.mean(connection_times)
        max_time = max(connection_times)
        p95_time = statistics.quantiles(connection_times, n=20)[18]  # 95th percentile
        
        assert avg_time < 500, f"Average connection creation time {avg_time:.2f}ms exceeds 500ms target"
        assert max_time < 1000, f"Maximum connection creation time {max_time:.2f}ms exceeds 1000ms limit"
        assert p95_time < 750, f"95th percentile connection creation time {p95_time:.2f}ms exceeds 750ms target"
        
        print(f"Connection creation performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Maximum: {max_time:.2f}ms")
        print(f"  95th percentile: {p95_time:.2f}ms")
    
    async def test_authentication_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        jwt_token,
        performance_timer
    ):
        """Test authentication performance under load."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        
        mock_connection = MagicMock()
        mock_connection.id = 1
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.update_connection_status = AsyncMock(return_value=True)
        
        with patch.object(service, '_send_connection_notifications'):
            pass
        
        # Act - Authenticate multiple connections
        auth_times = []
        num_auths = 100
        
        for i in range(num_auths):
            connection_id = f"auth_conn_{i}"
            
            performance_timer.start()
            await service.authenticate_connection(connection_id, jwt_token, mock_websocket_user)
            performance_timer.stop()
            
            auth_times.append(performance_timer.elapsed_ms())
        
        # Assert performance targets
        avg_time = statistics.mean(auth_times)
        max_time = max(auth_times)
        p95_time = statistics.quantiles(auth_times, n=20)[18]
        
        assert avg_time < 200, f"Average authentication time {avg_time:.2f}ms exceeds 200ms target"
        assert max_time < 500, f"Maximum authentication time {max_time:.2f}ms exceeds 500ms limit"
        assert p95_time < 300, f"95th percentile authentication time {p95_time:.2f}ms exceeds 300ms target"
        
        print(f"Authentication performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Maximum: {max_time:.2f}ms")
        print(f"  95th percentile: {p95_time:.2f}ms")
    
    async def test_concurrent_connections_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user
    ):
        """Test concurrent connection handling performance."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        
        mock_connection = MagicMock()
        mock_connection.id = 1
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_connection = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=None)
        
        with patch.object(service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        with patch.object(service, '_send_connection_notifications'):
            pass
        
        # Act - Create connections concurrently
        num_concurrent = 50
        start_time = time.time()
        
        tasks = []
        for i in range(num_concurrent):
            connection_data = WebSocketConnectionCreate(
                connection_id=f"concurrent_conn_{i}",
                user_id=mock_websocket_user.id,
                client_type="web"
            )
            
            task = service.create_connection(connection_data, mock_websocket_user)
            tasks.append(task)
        
        # Wait for all connections to complete
        await asyncio.gather(*tasks)
        
        total_time = (time.time() - start_time) * 1000  # Convert to ms
        avg_time_per_connection = total_time / num_concurrent
        
        # Assert performance targets
        assert total_time < 5000, f"Total concurrent connection time {total_time:.2f}ms exceeds 5000ms target"
        assert avg_time_per_connection < 500, f"Average concurrent connection time {avg_time_per_connection:.2f}ms exceeds 500ms target"
        
        print(f"Concurrent connection performance:")
        print(f"  Total time for {num_concurrent} connections: {total_time:.2f}ms")
        print(f"  Average time per connection: {avg_time_per_connection:.2f}ms")


@pytest.mark.performance
class TestWebSocketEventPerformance:
    """Performance tests for WebSocket event operations."""
    
    async def test_event_creation_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_event_factory,
        performance_timer
    ):
        """Test event creation performance under load."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        
        mock_connection = MagicMock()
        mock_connection.status = "authenticated"
        
        mock_event = MagicMock()
        mock_event.id = 1
        mock_event.event_id = "test_event"
        
        with patch.object(service, 'connection_repository') as mock_conn_repo:
            mock_conn_repo.get = AsyncMock(return_value=mock_connection)
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_event = AsyncMock(return_value=mock_event)
        
        # Act - Create multiple events
        event_times = []
        num_events = 100
        
        for i in range(num_events):
            event_data = WebSocketEventCreate(
                connection_id=1,
                event_type=EventTypeEnum.MESSAGE_SENT,
                priority=EventPriorityEnum.NORMAL,
                payload={"message": f"Performance test message {i}"}
            )
            
            performance_timer.start()
            await service.create_event(event_data, mock_websocket_user, moderate_content=False)
            performance_timer.stop()
            
            event_times.append(performance_timer.elapsed_ms())
        
        # Assert performance targets
        avg_time = statistics.mean(event_times)
        max_time = max(event_times)
        p95_time = statistics.quantiles(event_times, n=20)[18]
        
        assert avg_time < 500, f"Average event creation time {avg_time:.2f}ms exceeds 500ms target"
        assert max_time < 1000, f"Maximum event creation time {max_time:.2f}ms exceeds 1000ms limit"
        assert p95_time < 750, f"95th percentile event creation time {p95_time:.2f}ms exceeds 750ms target"
        
        print(f"Event creation performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Maximum: {max_time:.2f}ms")
        print(f"  95th percentile: {p95_time:.2f}ms")
    
    async def test_event_status_update_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        performance_timer
    ):
        """Test event status update performance."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.mark_event_sent = AsyncMock(return_value=True)
            mock_repo.return_value.mark_event_delivered = AsyncMock(return_value=True)
            mock_repo.return_value.mark_event_acknowledged = AsyncMock(return_value=True)
        
        # Act - Update event statuses
        update_times = []
        num_updates = 100
        
        for i in range(num_updates):
            event_id = f"perf_event_{i}"
            
            # Test sent status update
            performance_timer.start()
            await service.mark_event_sent(event_id, 50.0)
            performance_timer.stop()
            update_times.append(performance_timer.elapsed_ms())
            
            # Test delivered status update
            performance_timer.start()
            await service.mark_event_delivered(event_id)
            performance_timer.stop()
            update_times.append(performance_timer.elapsed_ms())
            
            # Test acknowledged status update
            performance_timer.start()
            await service.mark_event_acknowledged(event_id)
            performance_timer.stop()
            update_times.append(performance_timer.elapsed_ms())
        
        # Assert performance targets
        avg_time = statistics.mean(update_times)
        max_time = max(update_times)
        
        assert avg_time < 100, f"Average event status update time {avg_time:.2f}ms exceeds 100ms target"
        assert max_time < 200, f"Maximum event status update time {max_time:.2f}ms exceeds 200ms limit"
        
        print(f"Event status update performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Maximum: {max_time:.2f}ms")
        print(f"  Total updates: {len(update_times)}")


@pytest.mark.performance
class TestWebSocketPresencePerformance:
    """Performance tests for user presence operations."""
    
    async def test_presence_update_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test presence update performance under load."""
        # Arrange
        service = UserPresenceService(mock_async_session, mock_cache_manager)
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.status = "online"
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.update_user_status = AsyncMock(return_value=True)
            mock_repo.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
        
        # Act - Update presence multiple times
        update_times = []
        num_updates = 100
        statuses = ["online", "away", "offline"]
        
        for i in range(num_updates):
            status = statuses[i % len(statuses)]
            
            performance_timer.start()
            await service.update_user_status(
                mock_websocket_user.id, status, "performance_test", mock_websocket_user
            )
            performance_timer.stop()
            
            update_times.append(performance_timer.elapsed_ms())
        
        # Assert performance targets
        avg_time = statistics.mean(update_times)
        max_time = max(update_times)
        p95_time = statistics.quantiles(update_times, n=20)[18]
        
        assert avg_time < 100, f"Average presence update time {avg_time:.2f}ms exceeds 100ms target"
        assert max_time < 200, f"Maximum presence update time {max_time:.2f}ms exceeds 200ms limit"
        assert p95_time < 150, f"95th percentile presence update time {p95_time:.2f}ms exceeds 150ms target"
        
        print(f"Presence update performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Maximum: {max_time:.2f}ms")
        print(f"  95th percentile: {p95_time:.2f}ms")


@pytest.mark.performance
class TestWebSocketMetricsPerformance:
    """Performance tests for WebSocket metrics operations."""
    
    async def test_bulk_metrics_creation_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_admin_user,
        websocket_metrics_factory
    ):
        """Test bulk metrics creation performance (>1000 records/second target)."""
        # Arrange
        service = WebSocketMetricsService(mock_async_session, mock_cache_manager)
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.bulk_create_metrics = AsyncMock(return_value=1000)
        
        # Prepare bulk data
        num_records = 1000
        metrics_data = [websocket_metrics_factory() for _ in range(num_records)]
        
        # Act
        start_time = time.time()
        created_count = await service.bulk_create_metrics(metrics_data, mock_admin_user)
        end_time = time.time()
        
        # Assert performance targets
        total_time = end_time - start_time
        records_per_second = created_count / total_time
        
        assert records_per_second > 1000, f"Bulk creation rate {records_per_second:.0f} records/second below 1000 target"
        assert created_count == num_records, f"Created {created_count} records, expected {num_records}"
        
        print(f"Bulk metrics creation performance:")
        print(f"  Records created: {created_count}")
        print(f"  Total time: {total_time:.3f}s")
        print(f"  Records per second: {records_per_second:.0f}")
    
    async def test_metrics_query_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_admin_user,
        performance_timer
    ):
        """Test metrics query performance."""
        # Arrange
        service = WebSocketMetricsService(mock_async_session, mock_cache_manager)
        
        mock_summary = {
            "avg_message_delivery_time_ms": 75.0,
            "peak_concurrent_connections": 100,
            "total_messages_processed": 1000
        }
        
        mock_aggregates = {
            "total_connections": 500,
            "peak_active_connections": 100,
            "total_messages_sent": 10000
        }
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_performance_summary = AsyncMock(return_value=mock_summary)
            mock_repo.return_value.get_daily_aggregates = AsyncMock(return_value=mock_aggregates)
        
        # Act & Assert - Performance summary
        performance_timer.start()
        summary = await service.get_performance_summary(24, mock_admin_user)
        performance_timer.stop()
        
        assert summary is not None
        performance_timer.assert_under_ms(200, "Performance summary query")
        
        # Act & Assert - Daily aggregates
        performance_timer.start()
        aggregates = await service.get_daily_aggregates(datetime.now(), mock_admin_user)
        performance_timer.stop()
        
        assert aggregates is not None
        performance_timer.assert_under_ms(200, "Daily aggregates query")
        
        print(f"Metrics query performance validated")


@pytest.mark.performance
class TestWebSocketConnectionManagerPerformance:
    """Performance tests for WebSocket connection manager."""
    
    async def test_connection_manager_scalability(
        self, 
        websocket_test_client,
        mock_websocket_user
    ):
        """Test connection manager scalability with many connections."""
        # Arrange
        connection_manager.active_connections.clear()
        connection_manager.user_connections.clear()
        
        num_connections = 1000
        mock_websockets = []
        
        # Act - Add many connections
        start_time = time.time()
        
        for i in range(num_connections):
            mock_websocket = MagicMock()
            mock_websocket.send = AsyncMock()
            connection_id = f"scale_conn_{i}"
            
            await connection_manager.connect(mock_websocket, connection_id, mock_websocket_user.id)
            mock_websockets.append((connection_id, mock_websocket))
        
        connection_time = time.time() - start_time
        
        # Assert connection performance
        assert connection_manager.get_connection_count() == num_connections
        assert connection_time < 5.0, f"Connection setup took {connection_time:.2f}s, expected under 5s"
        
        # Act - Broadcast message to all connections
        start_time = time.time()
        sent_count = await connection_manager.broadcast("Performance test message")
        broadcast_time = time.time() - start_time
        
        # Assert broadcast performance
        assert sent_count == num_connections
        assert broadcast_time < 2.0, f"Broadcast took {broadcast_time:.2f}s, expected under 2s"
        
        # Act - Clean up connections
        start_time = time.time()
        for connection_id, _ in mock_websockets:
            connection_manager.disconnect(connection_id)
        cleanup_time = time.time() - start_time
        
        # Assert cleanup performance
        assert connection_manager.get_connection_count() == 0
        assert cleanup_time < 3.0, f"Cleanup took {cleanup_time:.2f}s, expected under 3s"
        
        print(f"Connection manager scalability:")
        print(f"  Connections: {num_connections}")
        print(f"  Connection setup time: {connection_time:.3f}s")
        print(f"  Broadcast time: {broadcast_time:.3f}s")
        print(f"  Cleanup time: {cleanup_time:.3f}s")
