"""
Review Management schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic V2 schemas for review system validation
including review creation, vendor responses, moderation workflow, and analytics.

Implements Task 4.4.1 Phase 2 requirements for review management system with:
- Complete review lifecycle validation
- Vendor response system schemas
- AI-powered content moderation schemas
- Review analytics and performance metrics schemas
- Integration with booking, user, vendor, and service systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field

from app.models.review_models import ReviewStatus, ResponseStatus, ModerationAction


# Base schemas with common configuration
class ReviewBaseSchema(BaseModel):
    """Base schema for review-related models with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-27T12:00:00Z",
                "updated_at": "2025-01-27T12:00:00Z"
            }
        }
    )


# Review creation and update schemas
class ReviewCreateSchema(ReviewBaseSchema):
    """Schema for creating a new review."""

    booking_id: int = Field(
        ...,
        description="ID of the completed booking being reviewed",
        gt=0
    )

    rating: int = Field(
        ...,
        description="Rating score (1-5 scale)",
        ge=1,
        le=5
    )

    title: str = Field(
        ...,
        description="Review title",
        min_length=5,
        max_length=200
    )

    content: str = Field(
        ...,
        description="Review content",
        min_length=10,
        max_length=2000
    )

    @field_validator('title')
    @classmethod
    def validate_title(cls, v: str) -> str:
        """Validate review title content."""
        if not v.strip():
            raise ValueError("Review title cannot be empty")

        # Check for inappropriate content patterns
        inappropriate_patterns = ['spam', 'fake', 'bot', 'test123']
        title_lower = v.lower()
        for pattern in inappropriate_patterns:
            if pattern in title_lower:
                raise ValueError(f"Review title contains inappropriate content: {pattern}")

        return v.strip()

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Validate review content."""
        if not v.strip():
            raise ValueError("Review content cannot be empty")

        # Check minimum meaningful content
        words = v.strip().split()
        if len(words) < 3:
            raise ValueError("Review content must contain at least 3 words")

        # Check for spam patterns
        if len(set(words)) < len(words) * 0.3:  # Too many repeated words
            raise ValueError("Review content appears to be spam (too many repeated words)")

        return v.strip()

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "booking_id": 123,
                "rating": 5,
                "title": "Amazing cultural experience!",
                "content": "The tour guide was knowledgeable and friendly. We learned so much about the local culture and history. Highly recommend this experience to anyone visiting the area."
            }
        }
    )


class ReviewUpdateSchema(ReviewBaseSchema):
    """Schema for updating an existing review (limited fields)."""

    rating: Optional[int] = Field(
        None,
        description="Updated rating score (1-5 scale)",
        ge=1,
        le=5
    )

    title: Optional[str] = Field(
        None,
        description="Updated review title",
        min_length=5,
        max_length=200
    )

    content: Optional[str] = Field(
        None,
        description="Updated review content",
        min_length=10,
        max_length=2000
    )

    @field_validator('title')
    @classmethod
    def validate_title(cls, v: Optional[str]) -> Optional[str]:
        """Validate review title content."""
        if v is not None:
            if not v.strip():
                raise ValueError("Review title cannot be empty")
            return v.strip()
        return v

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: Optional[str]) -> Optional[str]:
        """Validate review content."""
        if v is not None:
            if not v.strip():
                raise ValueError("Review content cannot be empty")

            words = v.strip().split()
            if len(words) < 3:
                raise ValueError("Review content must contain at least 3 words")

            return v.strip()
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "rating": 4,
                "title": "Updated: Great cultural experience",
                "content": "Updated review: The tour was informative and well-organized. The guide was professional and answered all our questions."
            }
        }
    )


# Review response schemas
class ReviewResponseCreateSchema(ReviewBaseSchema):
    """Schema for creating a vendor response to a review."""

    review_id: int = Field(
        ...,
        description="ID of the review being responded to",
        gt=0
    )

    content: str = Field(
        ...,
        description="Response content",
        min_length=10,
        max_length=1000
    )

    status: ResponseStatus = Field(
        ResponseStatus.DRAFT,
        description="Response status (draft, published, hidden)"
    )

    is_official_response: bool = Field(
        True,
        description="Whether this is an official vendor response"
    )

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Validate response content."""
        if not v.strip():
            raise ValueError("Response content cannot be empty")

        words = v.strip().split()
        if len(words) < 3:
            raise ValueError("Response content must contain at least 3 words")

        return v.strip()

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "review_id": 123,
                "content": "Thank you for your wonderful review! We're delighted that you enjoyed the cultural experience and found our guide knowledgeable. We look forward to welcoming you back soon.",
                "status": "published",
                "is_official_response": True
            }
        }
    )


class ReviewResponseUpdateSchema(ReviewBaseSchema):
    """Schema for updating a vendor response."""

    content: Optional[str] = Field(
        None,
        description="Updated response content",
        min_length=10,
        max_length=1000
    )

    status: Optional[ResponseStatus] = Field(
        None,
        description="Updated response status"
    )

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: Optional[str]) -> Optional[str]:
        """Validate response content."""
        if v is not None:
            if not v.strip():
                raise ValueError("Response content cannot be empty")

            words = v.strip().split()
            if len(words) < 3:
                raise ValueError("Response content must contain at least 3 words")

            return v.strip()
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "content": "Updated response: Thank you for your feedback. We appreciate your kind words about our cultural tour experience.",
                "status": "published"
            }
        }
    )


# Review moderation schemas
class ReviewModerationCreateSchema(ReviewBaseSchema):
    """Schema for creating a review moderation record."""

    review_id: int = Field(
        ...,
        description="ID of the review being moderated",
        gt=0
    )

    action: ModerationAction = Field(
        ...,
        description="Moderation action to take"
    )

    reason: Optional[str] = Field(
        None,
        description="Reason for moderation action",
        max_length=1000
    )

    ai_confidence_score: Optional[float] = Field(
        None,
        description="AI confidence score (0.0 to 1.0)",
        ge=0.0,
        le=1.0
    )

    manual_review_required: bool = Field(
        False,
        description="Whether manual review is required"
    )

    ai_analysis_results: Optional[Dict[str, Any]] = Field(
        None,
        description="Detailed AI analysis results"
    )

    @field_validator('reason')
    @classmethod
    def validate_reason(cls, v: Optional[str]) -> Optional[str]:
        """Validate moderation reason."""
        if v is not None:
            if not v.strip():
                raise ValueError("Moderation reason cannot be empty")
            return v.strip()
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "review_id": 123,
                "action": "approve",
                "reason": "Review content is appropriate and follows community guidelines",
                "ai_confidence_score": 0.95,
                "manual_review_required": False,
                "ai_analysis_results": {
                    "sentiment": "positive",
                    "toxicity_score": 0.02,
                    "spam_probability": 0.01
                }
            }
        }
    )


class ReviewModerationUpdateSchema(ReviewBaseSchema):
    """Schema for updating a review moderation record."""

    action: Optional[ModerationAction] = Field(
        None,
        description="Updated moderation action"
    )

    reason: Optional[str] = Field(
        None,
        description="Updated reason for moderation action",
        max_length=1000
    )

    processed_at: Optional[datetime] = Field(
        None,
        description="When moderation was processed"
    )

    @field_validator('reason')
    @classmethod
    def validate_reason(cls, v: Optional[str]) -> Optional[str]:
        """Validate moderation reason."""
        if v is not None:
            if not v.strip():
                raise ValueError("Moderation reason cannot be empty")
            return v.strip()
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "action": "flag",
                "reason": "Review requires additional manual review for content verification",
                "processed_at": "2025-01-27T14:30:00Z"
            }
        }
    )


# Response schemas for API endpoints
class ReviewResponseSchema(ReviewBaseSchema):
    """Schema for review response data."""

    id: int = Field(..., description="Review ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Core review information
    booking_id: int = Field(..., description="Associated booking ID")
    customer_id: int = Field(..., description="Customer who wrote the review")
    vendor_id: int = Field(..., description="Vendor being reviewed")
    service_id: int = Field(..., description="Service being reviewed")

    # Review content
    rating: int = Field(..., description="Rating score (1-5 scale)")
    title: str = Field(..., description="Review title")
    content: str = Field(..., description="Review content")

    # Review status and moderation
    status: ReviewStatus = Field(..., description="Review moderation status")
    moderation_reason: Optional[str] = Field(None, description="Reason for rejection or flagging")

    # Verification and authenticity
    is_verified_purchase: bool = Field(..., description="Whether review is from verified purchase")

    # Community engagement
    helpful_count: int = Field(..., description="Number of helpful votes")
    reported_count: int = Field(..., description="Number of times reported")

    # AI analysis
    sentiment_score: Optional[float] = Field(None, description="AI-generated sentiment score (-1.0 to 1.0)")
    language_code: Optional[str] = Field(None, description="Auto-detected language code")

    # Computed fields
    @computed_field
    @property
    def is_positive_review(self) -> bool:
        """Check if review is positive (rating >= 4)."""
        return self.rating >= 4

    @computed_field
    @property
    def has_vendor_response(self) -> bool:
        """Check if vendor has responded to this review."""
        # This will be populated by the service layer
        return hasattr(self, '_has_vendor_response') and self._has_vendor_response

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 123,
                "booking_id": 456,
                "customer_id": 789,
                "vendor_id": 101,
                "service_id": 202,
                "rating": 5,
                "title": "Amazing cultural experience!",
                "content": "The tour guide was knowledgeable and friendly...",
                "status": "approved",
                "is_verified_purchase": True,
                "helpful_count": 12,
                "reported_count": 0,
                "sentiment_score": 0.85,
                "language_code": "en",
                "is_positive_review": True,
                "has_vendor_response": True,
                "created_at": "2025-01-27T12:00:00Z",
                "updated_at": "2025-01-27T12:00:00Z"
            }
        }
    )


class VendorResponseSchema(ReviewBaseSchema):
    """Schema for vendor response data."""

    id: int = Field(..., description="Response ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Core response information
    review_id: int = Field(..., description="Associated review ID")
    vendor_id: int = Field(..., description="Vendor who wrote the response")

    # Response content
    content: str = Field(..., description="Response content")

    # Response status
    status: ResponseStatus = Field(..., description="Response publication status")

    # Response properties
    is_official_response: bool = Field(..., description="Whether this is an official vendor response")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 456,
                "review_id": 123,
                "vendor_id": 101,
                "content": "Thank you for your wonderful review! We're delighted...",
                "status": "published",
                "is_official_response": True,
                "created_at": "2025-01-27T13:00:00Z",
                "updated_at": "2025-01-27T13:00:00Z"
            }
        }
    )


class ReviewModerationSchema(ReviewBaseSchema):
    """Schema for review moderation data."""

    id: int = Field(..., description="Moderation ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Core moderation information
    review_id: int = Field(..., description="Associated review ID")
    moderator_id: Optional[int] = Field(None, description="Admin who performed manual moderation")

    # Moderation decision
    action: ModerationAction = Field(..., description="Moderation action taken")
    reason: Optional[str] = Field(None, description="Reason for moderation action")

    # AI analysis
    ai_confidence_score: Optional[float] = Field(None, description="AI confidence score (0.0 to 1.0)")
    manual_review_required: bool = Field(..., description="Whether manual review is required")

    # Processing timestamps
    processed_at: Optional[datetime] = Field(None, description="When moderation was processed")

    # AI analysis results
    ai_analysis_results: Optional[Dict[str, Any]] = Field(None, description="Detailed AI analysis results")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 789,
                "review_id": 123,
                "moderator_id": None,
                "action": "approve",
                "reason": "Review content is appropriate",
                "ai_confidence_score": 0.95,
                "manual_review_required": False,
                "processed_at": "2025-01-27T12:05:00Z",
                "ai_analysis_results": {
                    "sentiment": "positive",
                    "toxicity_score": 0.02,
                    "spam_probability": 0.01
                },
                "created_at": "2025-01-27T12:00:00Z",
                "updated_at": "2025-01-27T12:05:00Z"
            }
        }
    )


# Review analytics schemas
class ReviewAnalyticsSchema(ReviewBaseSchema):
    """Schema for review analytics data."""

    id: int = Field(..., description="Analytics ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Core analytics information
    vendor_id: int = Field(..., description="Vendor for analytics")

    # Analytics period
    period_start: date = Field(..., description="Analytics period start date")
    period_end: date = Field(..., description="Analytics period end date")

    # Review metrics
    total_reviews: int = Field(..., description="Total number of reviews in period")
    average_rating: Optional[Decimal] = Field(None, description="Average rating for the period")

    # Rating distribution (1-5 stars)
    rating_distribution: Optional[Dict[str, int]] = Field(None, description="Rating distribution breakdown")

    # Sentiment analysis
    sentiment_breakdown: Optional[Dict[str, int]] = Field(None, description="Sentiment breakdown")

    # Response metrics
    response_rate: Optional[Decimal] = Field(None, description="Vendor response rate (0.0 to 1.0)")
    average_response_time: Optional[int] = Field(None, description="Average response time in hours")

    # Quality metrics
    verified_reviews_count: int = Field(..., description="Number of verified purchase reviews")
    helpful_votes_total: int = Field(..., description="Total helpful votes received")
    reported_reviews_count: int = Field(..., description="Number of reported reviews")

    # Performance trends
    rating_trend: Optional[str] = Field(None, description="Rating trend (improving, declining, stable)")
    review_volume_trend: Optional[str] = Field(None, description="Review volume trend")

    # Computed fields
    @computed_field
    @property
    def review_quality_score(self) -> Optional[float]:
        """Calculate review quality score based on various metrics."""
        if self.total_reviews == 0:
            return None

        # Base score from average rating (normalized to 0-1)
        rating_score = (float(self.average_rating) - 1) / 4 if self.average_rating else 0

        # Verified reviews bonus
        verified_ratio = self.verified_reviews_count / self.total_reviews
        verified_bonus = verified_ratio * 0.1

        # Helpful votes bonus
        helpful_ratio = min(self.helpful_votes_total / self.total_reviews, 1.0)
        helpful_bonus = helpful_ratio * 0.1

        # Reported reviews penalty
        reported_ratio = self.reported_reviews_count / self.total_reviews
        reported_penalty = reported_ratio * 0.2

        quality_score = rating_score + verified_bonus + helpful_bonus - reported_penalty
        return max(0.0, min(1.0, quality_score))

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 101,
                "vendor_id": 123,
                "period_start": "2025-01-01",
                "period_end": "2025-01-31",
                "total_reviews": 25,
                "average_rating": "4.6",
                "rating_distribution": {
                    "1": 0,
                    "2": 1,
                    "3": 2,
                    "4": 8,
                    "5": 14
                },
                "sentiment_breakdown": {
                    "positive": 22,
                    "neutral": 2,
                    "negative": 1
                },
                "response_rate": "0.8800",
                "average_response_time": 6,
                "verified_reviews_count": 24,
                "helpful_votes_total": 156,
                "reported_reviews_count": 0,
                "rating_trend": "improving",
                "review_volume_trend": "increasing",
                "review_quality_score": 0.92,
                "created_at": "2025-02-01T00:00:00Z",
                "updated_at": "2025-02-01T00:00:00Z"
            }
        }
    )


# List response schemas for paginated endpoints
class ReviewListResponseSchema(ReviewBaseSchema):
    """Schema for paginated review list response."""

    reviews: List[ReviewResponseSchema] = Field(
        ...,
        description="List of reviews"
    )

    total: int = Field(
        ...,
        description="Total number of reviews",
        ge=0
    )

    page: int = Field(
        ...,
        description="Current page number",
        ge=1
    )

    per_page: int = Field(
        ...,
        description="Number of items per page",
        ge=1,
        le=100
    )

    pages: int = Field(
        ...,
        description="Total number of pages",
        ge=1
    )

    has_next: bool = Field(
        ...,
        description="Whether there is a next page"
    )

    has_prev: bool = Field(
        ...,
        description="Whether there is a previous page"
    )

    # Aggregated metrics for the current page/filter
    average_rating: Optional[Decimal] = Field(
        None,
        description="Average rating for displayed reviews"
    )

    rating_distribution: Optional[Dict[str, int]] = Field(
        None,
        description="Rating distribution for displayed reviews"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "reviews": [],
                "total": 150,
                "page": 1,
                "per_page": 20,
                "pages": 8,
                "has_next": True,
                "has_prev": False,
                "average_rating": "4.3",
                "rating_distribution": {
                    "1": 2,
                    "2": 5,
                    "3": 18,
                    "4": 45,
                    "5": 80
                }
            }
        }
    )


class VendorResponseListSchema(ReviewBaseSchema):
    """Schema for vendor response list response."""

    responses: List[VendorResponseSchema] = Field(
        ...,
        description="List of vendor responses"
    )

    total: int = Field(
        ...,
        description="Total number of responses",
        ge=0
    )

    page: int = Field(
        ...,
        description="Current page number",
        ge=1
    )

    per_page: int = Field(
        ...,
        description="Number of items per page",
        ge=1,
        le=100
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "responses": [],
                "total": 45,
                "page": 1,
                "per_page": 20
            }
        }
    )


# Filter and search schemas
class ReviewFilterSchema(ReviewBaseSchema):
    """Schema for review filtering and search."""

    vendor_id: Optional[int] = Field(
        None,
        description="Filter by vendor ID",
        gt=0
    )

    service_id: Optional[int] = Field(
        None,
        description="Filter by service ID",
        gt=0
    )

    customer_id: Optional[int] = Field(
        None,
        description="Filter by customer ID",
        gt=0
    )

    rating: Optional[int] = Field(
        None,
        description="Filter by specific rating",
        ge=1,
        le=5
    )

    min_rating: Optional[int] = Field(
        None,
        description="Filter by minimum rating",
        ge=1,
        le=5
    )

    max_rating: Optional[int] = Field(
        None,
        description="Filter by maximum rating",
        ge=1,
        le=5
    )

    status: Optional[ReviewStatus] = Field(
        None,
        description="Filter by review status"
    )

    is_verified_purchase: Optional[bool] = Field(
        None,
        description="Filter by verified purchase status"
    )

    has_vendor_response: Optional[bool] = Field(
        None,
        description="Filter by vendor response presence"
    )

    date_from: Optional[date] = Field(
        None,
        description="Filter reviews from this date"
    )

    date_to: Optional[date] = Field(
        None,
        description="Filter reviews to this date"
    )

    search_query: Optional[str] = Field(
        None,
        description="Search in review title and content",
        max_length=100
    )

    sort_by: Optional[str] = Field(
        "created_at",
        description="Sort field (created_at, rating, helpful_count)",
        pattern=r"^(created_at|rating|helpful_count|updated_at)$"
    )

    sort_order: Optional[str] = Field(
        "desc",
        description="Sort order (asc, desc)",
        pattern=r"^(asc|desc)$"
    )

    @field_validator('date_to')
    @classmethod
    def validate_date_range(cls, v: Optional[date], info) -> Optional[date]:
        """Validate date range."""
        if v is not None and 'date_from' in info.data and info.data['date_from'] is not None:
            if v < info.data['date_from']:
                raise ValueError("date_to must be after date_from")
        return v

    @field_validator('max_rating')
    @classmethod
    def validate_rating_range(cls, v: Optional[int], info) -> Optional[int]:
        """Validate rating range."""
        if v is not None and 'min_rating' in info.data and info.data['min_rating'] is not None:
            if v < info.data['min_rating']:
                raise ValueError("max_rating must be greater than or equal to min_rating")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "vendor_id": 123,
                "min_rating": 4,
                "status": "approved",
                "is_verified_purchase": True,
                "has_vendor_response": False,
                "date_from": "2025-01-01",
                "date_to": "2025-01-31",
                "search_query": "cultural tour",
                "sort_by": "created_at",
                "sort_order": "desc"
            }
        }
    )


# Summary and statistics schemas
class ReviewSummarySchema(ReviewBaseSchema):
    """Schema for review summary statistics."""

    total_reviews: int = Field(..., description="Total number of reviews")
    average_rating: Decimal = Field(..., description="Overall average rating")

    rating_distribution: Dict[str, int] = Field(
        ...,
        description="Rating distribution (1-5 stars)"
    )

    sentiment_breakdown: Dict[str, int] = Field(
        ...,
        description="Sentiment breakdown (positive/neutral/negative)"
    )

    verified_reviews_percentage: float = Field(
        ...,
        description="Percentage of verified purchase reviews"
    )

    response_rate: float = Field(
        ...,
        description="Vendor response rate"
    )

    average_response_time_hours: Optional[float] = Field(
        None,
        description="Average vendor response time in hours"
    )

    recent_reviews_count: int = Field(
        ...,
        description="Number of reviews in last 30 days"
    )

    trending_rating: str = Field(
        ...,
        description="Rating trend (improving/declining/stable)"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "total_reviews": 247,
                "average_rating": "4.6",
                "rating_distribution": {
                    "1": 3,
                    "2": 8,
                    "3": 25,
                    "4": 89,
                    "5": 122
                },
                "sentiment_breakdown": {
                    "positive": 211,
                    "neutral": 28,
                    "negative": 8
                },
                "verified_reviews_percentage": 94.3,
                "response_rate": 87.4,
                "average_response_time_hours": 8.5,
                "recent_reviews_count": 18,
                "trending_rating": "improving"
            }
        }
    )
