"""
Geolocation Analytics Service for Culture Connect Backend API.

This module provides comprehensive geolocation analytics service for performance tracking including:
- Provider performance analysis by geographic regions
- Conversion rate optimization insights
- Routing decision effectiveness analytics
- VPN/Proxy impact assessment on payment performance
- A/B testing integration and statistical analysis

Implements Phase 2.2 Analytics Dashboard requirements with production-grade
performance targets (<200ms analytics queries), comprehensive business logic,
and seamless integration with existing payment and VPN detection systems.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError
from app.repositories.geolocation_analytics_repository import (
    GeolocationAnalyticsRepository, ProviderPerformanceRepository,
    ConversionAnalyticsRepository, RoutingMetricsRepository
)
from app.models.geolocation_analytics import (
    GeolocationAnalytics, ProviderPerformanceMetrics, ConversionAnalytics,
    GeolocationRoutingMetrics, AnalyticsPeriodType, RoutingDecisionType
)
from app.schemas.geolocation_analytics import (
    AnalyticsFilterRequest, GeolocationAnalyticsResponse, ProviderPerformanceResponse,
    ConversionAnalyticsResponse, RoutingMetricsResponse, AnalyticsSummaryResponse
)
from app.models.payment import Payment, PaymentStatus
from app.core.payment.config import PaymentProviderType
import logging

logger = logging.getLogger(__name__)


class GeolocationAnalyticsService(BaseService[GeolocationAnalytics, GeolocationAnalyticsRepository]):
    """
    Analytics service for geolocation routing performance.

    Provides comprehensive analytics on:
    - Provider selection effectiveness
    - Geographic routing performance
    - Conversion rate optimization
    - A/B testing insights
    - VPN/Proxy impact assessment
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize geolocation analytics service."""
        super().__init__(GeolocationAnalyticsRepository, GeolocationAnalytics, db_session=db_session)

        # Initialize additional repositories
        self.provider_performance_repo = ProviderPerformanceRepository(db_session)
        self.conversion_analytics_repo = ConversionAnalyticsRepository(db_session)
        self.routing_metrics_repo = RoutingMetricsRepository(db_session)

    async def get_provider_performance_by_geography(
        self,
        start_date: datetime,
        end_date: datetime,
        country_codes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Analyze payment provider performance by geographic regions.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            country_codes: Optional list of country codes to filter

        Returns:
            Provider performance metrics by geography

        Performance Target: <200ms for complex analytics queries
        """
        correlation = self.log_operation_start("get_provider_performance_by_geography")

        try:
            # Validate date range
            if start_date >= end_date:
                raise ValidationError("Start date must be before end date")

            if (end_date - start_date).days > 365:
                raise ValidationError("Date range cannot exceed 365 days")

            # Get provider performance data
            provider_metrics = await self.provider_performance_repo.get_provider_performance_by_country(
                start_date=start_date,
                end_date=end_date,
                country_codes=country_codes
            )

            # Get overall analytics data
            analytics_data = await self.repository.get_analytics_by_period(
                start_date=start_date,
                end_date=end_date,
                period_type=AnalyticsPeriodType.DAILY,
                country_codes=country_codes
            )

            # Aggregate performance data by provider and geography
            performance_summary = {}

            for metric in provider_metrics:
                key = f"{metric.country_code}_{metric.provider}"

                performance_summary[key] = {
                    "country_code": metric.country_code,
                    "provider": metric.provider,
                    "total_transactions": metric.total_transactions,
                    "successful_transactions": metric.successful_transactions,
                    "success_rate": metric.success_rate,
                    "total_volume": float(metric.total_volume),
                    "avg_processing_time_ms": metric.avg_processing_time_ms,
                    "cost_effectiveness_score": metric.cost_effectiveness_score,
                    "market_share_percentage": metric.market_share_percentage,
                    "relative_performance_score": metric.relative_performance_score
                }

            # Calculate overall metrics
            total_transactions = sum(metric.total_transactions for metric in provider_metrics)
            total_volume = sum(float(metric.total_volume) for metric in provider_metrics)
            avg_success_rate = sum(metric.success_rate for metric in provider_metrics) / len(provider_metrics) if provider_metrics else 0

            result = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "country_filter": country_codes
                },
                "overall_metrics": {
                    "total_transactions": total_transactions,
                    "total_volume": total_volume,
                    "average_success_rate": avg_success_rate,
                    "analytics_records": len(analytics_data)
                },
                "provider_performance": performance_summary,
                "geographic_insights": await self._generate_geographic_insights(analytics_data),
                "optimization_recommendations": await self._generate_optimization_recommendations(provider_metrics)
            }

            self.log_operation_success(correlation, f"Analyzed {len(provider_metrics)} provider performance records")
            return result

        except Exception as e:
            await self.handle_service_error(e, "get_provider_performance_by_geography", {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "country_codes": country_codes
            })

    async def get_conversion_analytics(
        self,
        filter_request: AnalyticsFilterRequest
    ) -> Dict[str, Any]:
        """
        Get comprehensive conversion analytics with funnel analysis.

        Args:
            filter_request: Analytics filter parameters

        Returns:
            Conversion analytics data with optimization insights

        Performance Target: <150ms for conversion analytics
        """
        correlation = self.log_operation_start("get_conversion_analytics")

        try:
            # Get conversion funnel data
            funnel_data = await self.conversion_analytics_repo.get_conversion_funnel_data(
                start_date=filter_request.start_date,
                end_date=filter_request.end_date,
                country_codes=filter_request.country_codes,
                market_segment=filter_request.market_segment.value if filter_request.market_segment else None
            )

            # Get VPN impact analytics
            vpn_analytics = await self.repository.get_vpn_impact_analytics(
                start_date=filter_request.start_date,
                end_date=filter_request.end_date,
                country_codes=filter_request.country_codes
            )

            # Get provider distribution
            provider_distribution = await self.repository.get_provider_distribution(
                start_date=filter_request.start_date,
                end_date=filter_request.end_date
            )

            # Calculate conversion optimization insights
            optimization_insights = await self._calculate_conversion_optimization_insights(
                funnel_data, vpn_analytics, provider_distribution
            )

            result = {
                "period": {
                    "start_date": filter_request.start_date.isoformat(),
                    "end_date": filter_request.end_date.isoformat(),
                    "period_type": filter_request.period_type.value
                },
                "conversion_funnel": funnel_data,
                "vpn_impact": vpn_analytics,
                "provider_distribution": provider_distribution,
                "optimization_insights": optimization_insights,
                "performance_benchmarks": await self._get_conversion_benchmarks()
            }

            self.log_operation_success(correlation, "Generated conversion analytics")
            return result

        except Exception as e:
            await self.handle_service_error(e, "get_conversion_analytics", {
                "filter_request": filter_request.dict()
            })

    async def get_routing_effectiveness_analytics(
        self,
        start_date: datetime,
        end_date: datetime,
        routing_decision: Optional[RoutingDecisionType] = None
    ) -> Dict[str, Any]:
        """
        Get routing decision effectiveness analytics.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            routing_decision: Optional routing decision filter

        Returns:
            Routing effectiveness analytics

        Performance Target: <150ms for routing analytics
        """
        correlation = self.log_operation_start("get_routing_effectiveness_analytics")

        try:
            # Get routing effectiveness data
            routing_effectiveness = await self.routing_metrics_repo.get_routing_effectiveness(
                start_date=start_date,
                end_date=end_date,
                routing_decision=routing_decision
            )

            # Get top performing countries for context
            top_countries = await self.repository.get_top_performing_countries(
                start_date=start_date,
                end_date=end_date,
                limit=10,
                metric="conversion_rate"
            )

            # Calculate routing optimization recommendations
            optimization_recommendations = await self._generate_routing_optimization_recommendations(
                routing_effectiveness, top_countries
            )

            result = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "routing_filter": routing_decision.value if routing_decision else None
                },
                "routing_effectiveness": routing_effectiveness,
                "top_performing_countries": top_countries,
                "optimization_recommendations": optimization_recommendations,
                "routing_strategy_comparison": await self._compare_routing_strategies(routing_effectiveness)
            }

            self.log_operation_success(correlation, "Generated routing effectiveness analytics")
            return result

        except Exception as e:
            await self.handle_service_error(e, "get_routing_effectiveness_analytics", {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "routing_decision": routing_decision.value if routing_decision else None
            })

    async def get_analytics_dashboard_summary(
        self,
        period_days: int = 30
    ) -> AnalyticsSummaryResponse:
        """
        Get comprehensive analytics summary for dashboard.

        Args:
            period_days: Number of days to analyze (default: 30)

        Returns:
            Analytics summary for dashboard display

        Performance Target: <300ms for dashboard summary
        """
        correlation = self.log_operation_start("get_analytics_dashboard_summary")

        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=period_days)

            # Get overall analytics
            analytics_data = await self.repository.get_analytics_by_period(
                start_date=start_date,
                end_date=end_date,
                period_type=AnalyticsPeriodType.DAILY
            )

            # Calculate summary metrics
            total_payments = sum(record.total_payments for record in analytics_data)
            total_volume = sum(float(record.total_amount) for record in analytics_data)
            avg_conversion_rate = sum(record.conversion_rate for record in analytics_data) / len(analytics_data) if analytics_data else 0
            avg_processing_time = sum(record.avg_processing_time_ms or 0 for record in analytics_data) / len(analytics_data) if analytics_data else 0

            # Get provider performance scores
            provider_performance_scores = await self._calculate_provider_performance_scores(analytics_data)

            # Get top performing countries
            top_countries = await self.repository.get_top_performing_countries(
                start_date=start_date,
                end_date=end_date,
                limit=5,
                metric="conversion_rate"
            )

            # Get VPN impact
            vpn_analytics = await self.repository.get_vpn_impact_analytics(
                start_date=start_date,
                end_date=end_date
            )

            # Generate insights and recommendations
            key_insights = await self._generate_key_insights(analytics_data, vpn_analytics)
            optimization_opportunities = await self._generate_optimization_opportunities(analytics_data)

            summary = AnalyticsSummaryResponse(
                period_start=start_date,
                period_end=end_date,
                period_type=AnalyticsPeriodType.DAILY,
                total_payments=total_payments,
                total_volume=Decimal(str(total_volume)),
                overall_conversion_rate=avg_conversion_rate,
                avg_processing_time_ms=avg_processing_time,
                best_performing_provider=max(provider_performance_scores.keys(), key=lambda k: provider_performance_scores[k]) if provider_performance_scores else "unknown",
                provider_performance_scores=provider_performance_scores,
                top_performing_countries=top_countries,
                geographic_distribution={country["country_code"]: country["total_payments"] for country in top_countries},
                vpn_detection_rate=vpn_analytics.get("vpn_detection_rate", 0.0),
                vpn_impact_on_performance=vpn_analytics.get("vpn_impact_on_conversion", 0.0),
                key_insights=key_insights,
                optimization_opportunities=optimization_opportunities,
                performance_trends=await self._calculate_performance_trends(analytics_data)
            )

            self.log_operation_success(correlation, f"Generated dashboard summary for {period_days} days")
            return summary

        except Exception as e:
            await self.handle_service_error(e, "get_analytics_dashboard_summary", {
                "period_days": period_days
            })

    # Abstract methods from BaseService
    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for analytics creation."""
        # Basic validation for analytics data
        required_fields = ['analysis_date', 'period_type']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for analytics updates."""
        # Analytics records are typically immutable, but allow metadata updates
        allowed_fields = ['provider_performance_data', 'routing_effectiveness_data', 'geographic_insights']
        filtered_data = {k: v for k, v in data.items() if k in allowed_fields}
        return filtered_data

    # Helper methods for analytics calculations
    async def _generate_geographic_insights(self, analytics_data: List[GeolocationAnalytics]) -> Dict[str, Any]:
        """Generate geographic insights from analytics data."""
        if not analytics_data:
            return {}

        country_performance = {}
        for record in analytics_data:
            if record.country_code:
                if record.country_code not in country_performance:
                    country_performance[record.country_code] = {
                        "total_payments": 0,
                        "successful_payments": 0,
                        "total_amount": 0.0,
                        "conversion_rates": []
                    }

                country_performance[record.country_code]["total_payments"] += record.total_payments
                country_performance[record.country_code]["successful_payments"] += record.successful_payments
                country_performance[record.country_code]["total_amount"] += float(record.total_amount)
                country_performance[record.country_code]["conversion_rates"].append(record.conversion_rate)

        # Calculate insights
        insights = {
            "countries_analyzed": len(country_performance),
            "top_volume_countries": sorted(
                country_performance.items(),
                key=lambda x: x[1]["total_amount"],
                reverse=True
            )[:5],
            "top_conversion_countries": sorted(
                country_performance.items(),
                key=lambda x: sum(x[1]["conversion_rates"]) / len(x[1]["conversion_rates"]) if x[1]["conversion_rates"] else 0,
                reverse=True
            )[:5]
        }

        return insights

    async def _generate_optimization_recommendations(self, provider_metrics: List[ProviderPerformanceMetrics]) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on provider performance."""
        recommendations = []

        if not provider_metrics:
            return recommendations

        # Analyze provider performance by country
        country_provider_performance = {}
        for metric in provider_metrics:
            if metric.country_code not in country_provider_performance:
                country_provider_performance[metric.country_code] = {}

            country_provider_performance[metric.country_code][metric.provider] = {
                "success_rate": metric.success_rate,
                "cost_effectiveness": metric.cost_effectiveness_score,
                "processing_time": metric.avg_processing_time_ms
            }

        # Generate recommendations
        for country, providers in country_provider_performance.items():
            if len(providers) > 1:
                best_provider = max(providers.keys(), key=lambda p: providers[p]["success_rate"])
                worst_provider = min(providers.keys(), key=lambda p: providers[p]["success_rate"])

                success_rate_diff = providers[best_provider]["success_rate"] - providers[worst_provider]["success_rate"]

                if success_rate_diff > 5.0:  # 5% difference threshold
                    recommendations.append({
                        "type": "provider_optimization",
                        "country": country,
                        "recommendation": f"Consider routing more traffic to {best_provider} in {country}",
                        "impact": f"Potential {success_rate_diff:.1f}% improvement in success rate",
                        "priority": "high" if success_rate_diff > 10.0 else "medium"
                    })

        return recommendations

    async def _calculate_conversion_optimization_insights(
        self,
        funnel_data: Dict[str, Any],
        vpn_analytics: Dict[str, Any],
        provider_distribution: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate conversion optimization insights."""
        insights = {
            "funnel_bottlenecks": [],
            "vpn_impact_assessment": {},
            "provider_optimization_opportunities": []
        }

        # Analyze funnel bottlenecks
        conversion_rates = funnel_data.get("conversion_rates", {})
        if conversion_rates.get("initiation_rate", 0) < 80:
            insights["funnel_bottlenecks"].append({
                "stage": "initiation",
                "issue": "Low payment initiation rate",
                "current_rate": conversion_rates.get("initiation_rate", 0),
                "recommendation": "Improve payment UI/UX and reduce friction"
            })

        if conversion_rates.get("authorization_rate", 0) < 90:
            insights["funnel_bottlenecks"].append({
                "stage": "authorization",
                "issue": "High authorization failure rate",
                "current_rate": conversion_rates.get("authorization_rate", 0),
                "recommendation": "Review payment method validation and provider settings"
            })

        # VPN impact assessment
        vpn_impact = vpn_analytics.get("vpn_impact_on_conversion", 0)
        if abs(vpn_impact) > 2.0:  # 2% impact threshold
            insights["vpn_impact_assessment"] = {
                "impact_magnitude": vpn_impact,
                "assessment": "significant" if abs(vpn_impact) > 5.0 else "moderate",
                "recommendation": "Consider VPN-specific routing strategies" if vpn_impact < -2.0 else "VPN detection is working well"
            }

        return insights

    async def _get_conversion_benchmarks(self) -> Dict[str, float]:
        """Get industry conversion benchmarks."""
        return {
            "industry_average_conversion_rate": 78.5,
            "top_quartile_conversion_rate": 85.2,
            "mobile_conversion_rate": 72.1,
            "desktop_conversion_rate": 81.3,
            "african_market_average": 76.8,
            "diaspora_market_average": 82.1
        }

    async def _generate_routing_optimization_recommendations(
        self,
        routing_effectiveness: Dict[str, Any],
        top_countries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate routing optimization recommendations."""
        recommendations = []

        routing_strategies = routing_effectiveness.get("routing_strategies", {})

        for strategy, metrics in routing_strategies.items():
            success_rate = metrics.get("avg_success_rate", 0)
            total_decisions = metrics.get("total_decisions", 0)

            if total_decisions > 100:  # Only consider strategies with sufficient data
                if success_rate < 75:  # Below threshold
                    recommendations.append({
                        "strategy": strategy,
                        "issue": "Low success rate",
                        "current_rate": success_rate,
                        "recommendation": f"Review {strategy} routing logic and provider selection",
                        "priority": "high" if success_rate < 70 else "medium"
                    })

        return recommendations

    async def _compare_routing_strategies(self, routing_effectiveness: Dict[str, Any]) -> Dict[str, Any]:
        """Compare routing strategies performance."""
        strategies = routing_effectiveness.get("routing_strategies", {})

        if not strategies:
            return {}

        comparison = {
            "best_strategy": None,
            "worst_strategy": None,
            "performance_gap": 0.0,
            "strategy_rankings": []
        }

        # Rank strategies by success rate
        strategy_rankings = sorted(
            strategies.items(),
            key=lambda x: x[1].get("avg_success_rate", 0),
            reverse=True
        )

        if strategy_rankings:
            comparison["best_strategy"] = strategy_rankings[0][0]
            comparison["worst_strategy"] = strategy_rankings[-1][0]
            comparison["performance_gap"] = (
                strategy_rankings[0][1].get("avg_success_rate", 0) -
                strategy_rankings[-1][1].get("avg_success_rate", 0)
            )
            comparison["strategy_rankings"] = [
                {
                    "strategy": strategy,
                    "success_rate": metrics.get("avg_success_rate", 0),
                    "total_decisions": metrics.get("total_decisions", 0)
                }
                for strategy, metrics in strategy_rankings
            ]

        return comparison

    async def _calculate_provider_performance_scores(self, analytics_data: List[GeolocationAnalytics]) -> Dict[str, float]:
        """Calculate overall provider performance scores."""
        provider_scores = {"paystack": 0.0, "stripe": 0.0, "busha": 0.0}
        provider_counts = {"paystack": 0, "stripe": 0, "busha": 0}

        for record in analytics_data:
            total_payments = record.total_payments
            if total_payments > 0:
                # Weight by conversion rate and volume
                paystack_score = (record.paystack_payments / total_payments) * record.conversion_rate
                stripe_score = (record.stripe_payments / total_payments) * record.conversion_rate
                busha_score = (record.busha_payments / total_payments) * record.conversion_rate

                provider_scores["paystack"] += paystack_score
                provider_scores["stripe"] += stripe_score
                provider_scores["busha"] += busha_score

                if record.paystack_payments > 0:
                    provider_counts["paystack"] += 1
                if record.stripe_payments > 0:
                    provider_counts["stripe"] += 1
                if record.busha_payments > 0:
                    provider_counts["busha"] += 1

        # Calculate averages
        for provider in provider_scores:
            if provider_counts[provider] > 0:
                provider_scores[provider] = provider_scores[provider] / provider_counts[provider]

        return provider_scores

    async def _generate_key_insights(self, analytics_data: List[GeolocationAnalytics], vpn_analytics: Dict[str, Any]) -> List[str]:
        """Generate key insights for dashboard."""
        insights = []

        if analytics_data:
            avg_conversion = sum(record.conversion_rate for record in analytics_data) / len(analytics_data)
            insights.append(f"Average conversion rate: {avg_conversion:.1f}%")

            total_payments = sum(record.total_payments for record in analytics_data)
            insights.append(f"Total payments processed: {total_payments:,}")

            vpn_rate = vpn_analytics.get("vpn_detection_rate", 0)
            if vpn_rate > 10:
                insights.append(f"High VPN usage detected: {vpn_rate:.1f}% of payments")

        return insights

    async def _generate_optimization_opportunities(self, analytics_data: List[GeolocationAnalytics]) -> List[Dict[str, Any]]:
        """Generate optimization opportunities."""
        opportunities = []

        if analytics_data:
            # Find countries with low conversion rates
            low_conversion_countries = [
                record for record in analytics_data
                if record.conversion_rate < 75 and record.total_payments > 50
            ]

            for record in low_conversion_countries[:3]:  # Top 3 opportunities
                opportunities.append({
                    "type": "conversion_optimization",
                    "country": record.country_code,
                    "current_rate": record.conversion_rate,
                    "potential_improvement": f"{85 - record.conversion_rate:.1f}%",
                    "impact": "high" if record.total_payments > 200 else "medium"
                })

        return opportunities

    async def _calculate_performance_trends(self, analytics_data: List[GeolocationAnalytics]) -> Dict[str, Any]:
        """Calculate performance trends over time."""
        if len(analytics_data) < 2:
            return {}

        # Sort by date
        sorted_data = sorted(analytics_data, key=lambda x: x.analysis_date)

        # Calculate trends
        first_half = sorted_data[:len(sorted_data)//2]
        second_half = sorted_data[len(sorted_data)//2:]

        first_avg_conversion = sum(record.conversion_rate for record in first_half) / len(first_half)
        second_avg_conversion = sum(record.conversion_rate for record in second_half) / len(second_half)

        conversion_trend = second_avg_conversion - first_avg_conversion

        return {
            "conversion_rate_trend": {
                "direction": "improving" if conversion_trend > 0 else "declining",
                "magnitude": abs(conversion_trend),
                "percentage_change": (conversion_trend / first_avg_conversion * 100) if first_avg_conversion > 0 else 0
            }
        }
