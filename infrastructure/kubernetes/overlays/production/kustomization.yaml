# Culture Connect Backend - Production Kubernetes Overlay
# Kustomize configuration for production environment deployment

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Base resources to customize
resources:
  - ../../base

# Namespace for production
namespace: culture-connect

# Common labels for all resources
commonLabels:
  environment: production
  deployment-tier: production
  app.kubernetes.io/environment: production

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/environment: production
  config.kubernetes.io/local-config: "false"
  monitoring.coreos.com/enabled: "true"

# Images to use (production tags)
images:
  - name: ghcr.io/culture-connect/backend
    newTag: latest

# ConfigMap generator for production-specific config
configMapGenerator:
  - name: culture-connect-config
    behavior: merge
    literals:
      - CC_ENVIRONMENT=production
      - CC_DEBUG=false
      - CC_LOG_LEVEL=INFO
      - CC_WORKERS=4
      - CC_RELOAD=false
      - CC_ENABLE_DOCS=false
      - CC_ENABLE_REDOC=false
      - CC_ENABLE_PROFILER=false
      - CC_ENABLE_DEBUG_TOOLBAR=false
      - CC_MOCK_EXTERNAL_SERVICES=false
      - CC_SKIP_EMAIL_VERIFICATION=false
      - CC_BYPASS_RATE_LIMITING=false
      - CC_ENABLE_SQL_LOGGING=false
      - CC_ENABLE_GZIP=true
      - CC_ENABLE_STATIC_FILE_SERVING=false
      - CC_ENABLE_CORS=true
      - CC_GDPR_COMPLIANCE_ENABLED=true
      - CC_DATA_RETENTION_DAYS=90
      - CC_ANONYMIZE_IPS=true
      - CC_CONSENT_REQUIRED=true
      - CC_AUDIT_ENABLED=true
      - CC_AUDIT_RETENTION_DAYS=365
      - CC_AUDIT_ENCRYPTION=true

# Secret generator for production secrets (use external secret management in real production)
secretGenerator:
  - name: culture-connect-secrets
    behavior: merge
    literals:
      - CC_SECRET_KEY=REPLACE_WITH_SECURE_SECRET_KEY_256_BITS_MINIMUM
      - CC_DATABASE_URL=postgresql+asyncpg://culture_connect_prod:REPLACE_WITH_SECURE_PASSWORD@postgres:5432/culture_connect_prod_db
      - CC_REDIS_URL=redis://:REPLACE_WITH_SECURE_REDIS_PASSWORD@redis:6379/0
      - CC_PAYSTACK_SECRET_KEY=sk_live_REPLACE_WITH_LIVE_PAYSTACK_SECRET_KEY
      - CC_STRIPE_SECRET_KEY=sk_live_REPLACE_WITH_LIVE_STRIPE_SECRET_KEY
      - CC_BUSHA_API_KEY=REPLACE_WITH_LIVE_BUSHA_API_KEY
      - CC_AIML_API_KEY=REPLACE_WITH_PRODUCTION_AIML_API_KEY
      - CC_SENTRY_DSN=REPLACE_WITH_PRODUCTION_SENTRY_DSN

# Patches for production-specific modifications
patches:
  # Production resource requirements (optimized for performance)
  - target:
      kind: Deployment
      name: culture-connect-api
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "512Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "250m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "1Gi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "500m"

  # Production Celery worker resources
  - target:
      kind: Deployment
      name: culture-connect-celery-worker
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "256Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "125m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "512Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "250m"

  # Production HPA configuration (aggressive scaling)
  - target:
      kind: HorizontalPodAutoscaler
      name: culture-connect-api-hpa
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 3
      - op: replace
        path: /spec/maxReplicas
        value: 20
      - op: replace
        path: /spec/metrics/0/resource/target/averageUtilization
        value: 60

  # Production ingress configuration with SSL and security
  - target:
      kind: Ingress
      name: culture-connect-ingress
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: api.cultureconnect.ng
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1rate-limit
        value: "5000"
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1rate-limit-window
        value: "1h"
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1ssl-redirect
        value: "true"
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1force-ssl-redirect
        value: "true"
      - op: add
        path: /metadata/annotations/cert-manager.io~1cluster-issuer
        value: "letsencrypt-prod"

# Production-specific resource configurations
replicas:
  - name: culture-connect-api
    count: 3
  - name: culture-connect-celery-worker
    count: 3
  - name: culture-connect-celery-beat
    count: 1

# Additional production resources
patchesStrategicMerge:
  - production-security-patch.yaml
  - production-monitoring-patch.yaml
  - production-backup-patch.yaml
