"""
Document verification service for Culture Connect Backend API.

This module provides service layer for document verification workflows,
automated validation, manual review processes, and notification integration.

Implements Task 3.1.2: Document Verification System service layer with
automated document validation, manual review workflow, and comprehensive
business logic for verification processes.
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal

from app.services.base import ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.verification_repository import (
    DocumentVerificationWorkflowRepository, VerificationHistoryRepository,
    AutomatedValidationRuleRepository
)
from app.repositories.vendor_repository import VendorRepository, VendorDocumentRepository
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.models.verification import (
    DocumentVerificationWorkflow, VerificationHistory, AutomatedValidationRule,
    VerificationWorkflowStatus, VerificationPriority, AutomatedValidationStatus,
    VerificationActionType
)
from app.models.vendor import VendorDocument, DocumentType, DocumentStatus
from app.schemas.verification import (
    DocumentVerificationWorkflowCreate, DocumentVerificationWorkflowUpdate,
    DocumentVerificationWorkflowResponse, VerificationHistoryResponse,
    AutomatedValidationRuleCreate, AutomatedValidationRuleUpdate,
    AutomatedValidationRuleResponse, WorkflowAssignmentRequest,
    WorkflowReviewRequest, AutomatedValidationRequest, AutomatedValidationResponse,
    AutomatedValidationResult, WorkflowListFilters, WorkflowListResponse,
    VerificationStatistics
)
logger = logging.getLogger(__name__)


class DocumentVerificationService:
    """Service for document verification workflow management."""

    def __init__(
        self,
        workflow_repository: DocumentVerificationWorkflowRepository,
        history_repository: VerificationHistoryRepository,
        rule_repository: AutomatedValidationRuleRepository,
        vendor_repository: VendorRepository,
        document_repository: VendorDocumentRepository,
        email_service: EmailService,
        notification_service: PushNotificationService
    ):
        """Initialize service with dependencies."""
        super().__init__(workflow_repository)
        self.workflow_repository = workflow_repository
        self.history_repository = history_repository
        self.rule_repository = rule_repository
        self.vendor_repository = vendor_repository
        self.document_repository = document_repository
        self.email_service = email_service
        self.notification_service = notification_service

    def not_found_error(self, resource: str, identifier: Any) -> NotFoundError:
        """Create NotFoundError instance."""
        return NotFoundError(resource, identifier)

    def validation_error(self, message: str, field: Optional[str] = None) -> ValidationError:
        """Create ValidationError instance."""
        return ValidationError(message, field)

    async def create_verification_workflow(
        self,
        document_id: int,
        priority: VerificationPriority = VerificationPriority.NORMAL,
        is_expedited: bool = False,
        user_id: Optional[int] = None,
        correlation_id: Optional[str] = None
    ) -> DocumentVerificationWorkflowResponse:
        """
        Create a new document verification workflow.

        Args:
            document_id: Document ID to verify
            priority: Verification priority
            is_expedited: Whether this is expedited
            user_id: User creating the workflow
            correlation_id: Request correlation ID

        Returns:
            Created workflow response

        Raises:
            NotFoundError: If document not found
            ValidationError: If document already has active workflow
        """
        logger.info(
            "Creating verification workflow",
            extra={
                "document_id": document_id,
                "priority": priority.value,
                "is_expedited": is_expedited,
                "user_id": user_id,
                "correlation_id": correlation_id
            }
        )

        # Get document and validate
        document = await self.document_repository.get_by_id(document_id)
        if not document:
            raise self.not_found_error("Document", document_id)

        # Check for existing active workflow
        existing_workflow = await self.workflow_repository.get_by_filters([
            DocumentVerificationWorkflow.document_id == document_id,
            DocumentVerificationWorkflow.status.in_([
                VerificationWorkflowStatus.PENDING,
                VerificationWorkflowStatus.IN_PROGRESS,
                VerificationWorkflowStatus.UNDER_REVIEW
            ])
        ])

        if existing_workflow:
            raise self.validation_error(
                f"Document {document_id} already has an active verification workflow"
            )

        # Determine if manual review is required
        requires_manual_review = await self._requires_manual_review(document)

        # Create workflow
        workflow_data = DocumentVerificationWorkflowCreate(
            vendor_id=document.vendor_id,
            document_id=document_id,
            priority=priority,
            requires_manual_review=requires_manual_review,
            is_expedited=is_expedited,
            workflow_data={
                "created_by": user_id,
                "document_type": document.document_type.value,
                "vendor_type": document.vendor.business_type.value if document.vendor else None
            }
        )

        workflow = await self.workflow_repository.create_workflow(workflow_data)

        # Log workflow creation
        await self.history_repository.log_workflow_action(
            workflow_id=workflow.id,
            action_type=VerificationActionType.SUBMITTED,
            performed_by=user_id,
            description=f"Verification workflow created for document {document_id}",
            new_status=workflow.status.value,
            action_data={
                "document_type": document.document_type.value,
                "priority": priority.value,
                "is_expedited": is_expedited
            }
        )

        # Start automated validation
        await self._start_automated_validation(workflow.id, correlation_id)

        # Send notifications
        await self._send_workflow_notifications(
            workflow, "created", user_id, correlation_id
        )

        logger.info(
            "Verification workflow created successfully",
            extra={
                "workflow_id": workflow.id,
                "workflow_reference": workflow.workflow_reference,
                "correlation_id": correlation_id
            }
        )

        return DocumentVerificationWorkflowResponse.model_validate(workflow)

    async def assign_workflow(
        self,
        workflow_id: int,
        assignment_request: WorkflowAssignmentRequest,
        assigned_by: Optional[int] = None,
        correlation_id: Optional[str] = None
    ) -> DocumentVerificationWorkflowResponse:
        """
        Assign workflow to an admin for review.

        Args:
            workflow_id: Workflow ID
            assignment_request: Assignment details
            assigned_by: Admin making the assignment
            correlation_id: Request correlation ID

        Returns:
            Updated workflow response

        Raises:
            NotFoundError: If workflow not found
            ValidationError: If workflow cannot be assigned
        """
        logger.info(
            "Assigning verification workflow",
            extra={
                "workflow_id": workflow_id,
                "assigned_to": assignment_request.assigned_to,
                "assigned_by": assigned_by,
                "correlation_id": correlation_id
            }
        )

        # Get workflow and validate
        workflow = await self.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            raise self.not_found_error("Workflow", workflow_id)

        # Validate workflow can be assigned
        if workflow.status not in [
            VerificationWorkflowStatus.PENDING,
            VerificationWorkflowStatus.IN_PROGRESS
        ]:
            raise self.validation_error(
                f"Workflow {workflow_id} cannot be assigned in status {workflow.status.value}"
            )

        # Assign workflow
        previous_status = workflow.status.value
        updated_workflow = await self.workflow_repository.assign_workflow(
            workflow_id=workflow_id,
            assigned_to=assignment_request.assigned_to,
            assigned_by=assigned_by
        )

        if not updated_workflow:
            raise self.not_found_error("Workflow", workflow_id)

        # Update priority if provided
        if assignment_request.priority:
            update_data = {"priority": assignment_request.priority}
            updated_workflow = await self.workflow_repository.update(
                updated_workflow, update_data
            )

        # Log assignment
        await self.history_repository.log_workflow_action(
            workflow_id=workflow_id,
            action_type=VerificationActionType.ASSIGNED,
            performed_by=assigned_by,
            description=f"Workflow assigned to admin {assignment_request.assigned_to}",
            previous_status=previous_status,
            new_status=updated_workflow.status.value,
            action_data={
                "assigned_to": assignment_request.assigned_to,
                "priority": assignment_request.priority.value if assignment_request.priority else None,
                "notes": assignment_request.notes
            }
        )

        # Send notifications
        await self._send_workflow_notifications(
            updated_workflow, "assigned", assigned_by, correlation_id
        )

        logger.info(
            "Workflow assigned successfully",
            extra={
                "workflow_id": workflow_id,
                "assigned_to": assignment_request.assigned_to,
                "correlation_id": correlation_id
            }
        )

        return DocumentVerificationWorkflowResponse.model_validate(updated_workflow)

    async def review_workflow(
        self,
        workflow_id: int,
        review_request: WorkflowReviewRequest,
        reviewed_by: int,
        correlation_id: Optional[str] = None
    ) -> DocumentVerificationWorkflowResponse:
        """
        Complete workflow review with decision.

        Args:
            workflow_id: Workflow ID
            review_request: Review decision and notes
            reviewed_by: Admin completing review
            correlation_id: Request correlation ID

        Returns:
            Updated workflow response

        Raises:
            NotFoundError: If workflow not found
            ValidationError: If workflow cannot be reviewed
        """
        logger.info(
            "Reviewing verification workflow",
            extra={
                "workflow_id": workflow_id,
                "status": review_request.status.value,
                "reviewed_by": reviewed_by,
                "correlation_id": correlation_id
            }
        )

        # Get workflow and validate
        workflow = await self.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            raise self.not_found_error("Workflow", workflow_id)

        # Validate workflow can be reviewed
        if workflow.status not in [
            VerificationWorkflowStatus.IN_PROGRESS,
            VerificationWorkflowStatus.UNDER_REVIEW
        ]:
            raise self.validation_error(
                f"Workflow {workflow_id} cannot be reviewed in status {workflow.status.value}"
            )

        # Complete workflow
        previous_status = workflow.status.value
        updated_workflow = await self.workflow_repository.complete_workflow(
            workflow_id=workflow_id,
            status=review_request.status,
            reviewed_by=reviewed_by,
            review_notes=review_request.review_notes,
            rejection_reason=review_request.rejection_reason
        )

        if not updated_workflow:
            raise self.not_found_error("Workflow", workflow_id)

        # Update document status based on review decision
        await self._update_document_status(updated_workflow, review_request)

        # Log review
        await self.history_repository.log_workflow_action(
            workflow_id=workflow_id,
            action_type=VerificationActionType.REVIEWED,
            performed_by=reviewed_by,
            description=f"Workflow reviewed with decision: {review_request.status.value}",
            previous_status=previous_status,
            new_status=updated_workflow.status.value,
            action_data={
                "review_decision": review_request.status.value,
                "review_notes": review_request.review_notes,
                "rejection_reason": review_request.rejection_reason,
                "requires_resubmission": review_request.requires_resubmission
            }
        )

        # Send notifications
        await self._send_workflow_notifications(
            updated_workflow, "reviewed", reviewed_by, correlation_id
        )

        logger.info(
            "Workflow reviewed successfully",
            extra={
                "workflow_id": workflow_id,
                "decision": review_request.status.value,
                "correlation_id": correlation_id
            }
        )

        return DocumentVerificationWorkflowResponse.model_validate(updated_workflow)

    # Private helper methods
    async def _requires_manual_review(self, document: VendorDocument) -> bool:
        """Determine if document requires manual review."""
        # High-risk document types always require manual review
        high_risk_types = [
            DocumentType.BUSINESS_LICENSE,
            DocumentType.PROFESSIONAL_CERTIFICATION,
            DocumentType.IDENTITY_DOCUMENT
        ]

        if document.document_type in high_risk_types:
            return True

        # Check if vendor is new (less than 30 days)
        if document.vendor and document.vendor.created_at:
            days_since_registration = (datetime.now(timezone.utc) - document.vendor.created_at).days
            if days_since_registration < 30:
                return True

        return False

    async def _start_automated_validation(
        self,
        workflow_id: int,
        correlation_id: Optional[str] = None
    ) -> None:
        """Start automated validation for workflow."""
        try:
            workflow = await self.workflow_repository.get_by_id(workflow_id)
            if not workflow:
                return

            document = await self.document_repository.get_by_id(workflow.document_id)
            if not document:
                return

            # Run automated validation
            validation_request = AutomatedValidationRequest(
                document_id=document.id,
                force_revalidation=True
            )

            validation_response = await self.run_automated_validation(
                validation_request, correlation_id
            )

            # Update workflow with results
            await self.workflow_repository.update_automation_results(
                workflow_id=workflow_id,
                validation_status=validation_response.overall_status,
                validation_score=float(validation_response.overall_score) if validation_response.overall_score else None,
                validation_results={"results": [result.dict() for result in validation_response.validation_results]}
            )

        except Exception as e:
            logger.error(
                "Automated validation failed",
                extra={
                    "workflow_id": workflow_id,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )

    async def _execute_validation_rule(
        self,
        rule: AutomatedValidationRule,
        document: VendorDocument,
        correlation_id: Optional[str] = None
    ) -> AutomatedValidationResult:
        """Execute a single validation rule."""
        start_time = datetime.now(timezone.utc)

        try:
            # TODO: Implement actual validation logic based on rule type
            # This is a placeholder implementation

            if rule.validation_type == "format_validation":
                # Validate file format and structure
                result = await self._validate_document_format(document, rule.rule_config)
            elif rule.validation_type == "ocr_extraction":
                # Extract text using OCR
                result = await self._extract_document_text(document, rule.rule_config)
            elif rule.validation_type == "business_logic":
                # Apply business logic validation
                result = await self._validate_business_logic(document, rule.rule_config)
            else:
                # Unknown validation type
                result = {
                    "status": AutomatedValidationStatus.SKIPPED,
                    "confidence_score": 0.0,
                    "message": f"Unknown validation type: {rule.validation_type}"
                }

            execution_time = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

            return AutomatedValidationResult(
                rule_id=rule.id,
                rule_name=rule.rule_name,
                validation_type=rule.validation_type,
                status=result["status"],
                confidence_score=result.get("confidence_score"),
                result_data=result.get("data"),
                execution_time_ms=execution_time
            )

        except Exception as e:
            execution_time = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)

            return AutomatedValidationResult(
                rule_id=rule.id,
                rule_name=rule.rule_name,
                validation_type=rule.validation_type,
                status=AutomatedValidationStatus.ERROR,
                error_message=str(e),
                execution_time_ms=execution_time
            )

    async def _validate_document_format(
        self,
        document: VendorDocument,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate document format and structure."""
        # TODO: Implement actual format validation
        # Placeholder implementation
        return {
            "status": AutomatedValidationStatus.PASSED,
            "confidence_score": 95.0,
            "data": {"format_valid": True, "file_type": document.file_type}
        }

    async def _extract_document_text(
        self,
        document: VendorDocument,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract text from document using OCR."""
        # TODO: Implement actual OCR integration
        # Placeholder implementation
        return {
            "status": AutomatedValidationStatus.PASSED,
            "confidence_score": 85.0,
            "data": {"extracted_text": "Sample extracted text", "confidence": 85.0}
        }

    async def _validate_business_logic(
        self,
        document: VendorDocument,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply business logic validation."""
        # TODO: Implement actual business logic validation
        # Placeholder implementation
        return {
            "status": AutomatedValidationStatus.PASSED,
            "confidence_score": 90.0,
            "data": {"business_rules_passed": True}
        }

    def _calculate_overall_validation_status(
        self,
        results: List[AutomatedValidationResult]
    ) -> AutomatedValidationStatus:
        """Calculate overall validation status from individual results."""
        if not results:
            return AutomatedValidationStatus.SKIPPED

        # If any critical validation failed, overall status is failed
        failed_count = sum(1 for r in results if r.status == AutomatedValidationStatus.FAILED)
        error_count = sum(1 for r in results if r.status == AutomatedValidationStatus.ERROR)
        passed_count = sum(1 for r in results if r.status == AutomatedValidationStatus.PASSED)

        if failed_count > 0:
            return AutomatedValidationStatus.FAILED
        elif error_count > 0 and passed_count == 0:
            return AutomatedValidationStatus.ERROR
        elif passed_count > 0:
            return AutomatedValidationStatus.PASSED
        else:
            return AutomatedValidationStatus.SKIPPED

    def _calculate_overall_validation_score(
        self,
        results: List[AutomatedValidationResult]
    ) -> Optional[Decimal]:
        """Calculate overall validation confidence score."""
        scores = [r.confidence_score for r in results if r.confidence_score is not None]
        if not scores:
            return None

        # Calculate weighted average (simple average for now)
        average_score = sum(scores) / len(scores)
        return Decimal(str(round(average_score, 2)))

    async def _update_document_status(
        self,
        workflow: DocumentVerificationWorkflow,
        review_request: WorkflowReviewRequest
    ) -> None:
        """Update document status based on workflow decision."""
        document = await self.document_repository.get_by_id(workflow.document_id)
        if not document:
            return

        if review_request.status == VerificationWorkflowStatus.APPROVED:
            new_status = DocumentStatus.APPROVED
        elif review_request.status == VerificationWorkflowStatus.REJECTED:
            new_status = DocumentStatus.REJECTED
        else:
            return  # No status change for other workflow statuses

        await self.document_repository.update(
            document,
            {
                "status": new_status,
                "reviewed_at": datetime.now(timezone.utc),
                "reviewed_by": workflow.reviewed_by,
                "review_notes": review_request.review_notes
            }
        )

    async def _send_workflow_notifications(
        self,
        workflow: DocumentVerificationWorkflow,
        event_type: str,
        user_id: Optional[int] = None,
        correlation_id: Optional[str] = None
    ) -> None:
        """Send notifications for workflow events."""
        try:
            # TODO: Implement actual notification sending
            # This is a placeholder for notification integration

            logger.info(
                "Workflow notification sent",
                extra={
                    "workflow_id": workflow.id,
                    "event_type": event_type,
                    "user_id": user_id,
                    "correlation_id": correlation_id
                }
            )

        except Exception as e:
            logger.error(
                "Failed to send workflow notification",
                extra={
                    "workflow_id": workflow.id,
                    "event_type": event_type,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )