"""
Service Listing (Experiences) API endpoints for Culture Connect Backend API.

This module provides comprehensive service listing endpoints including:
- Service CRUD operations for vendors
- Public service discovery and search
- Service category management
- Service media upload and management
- Service availability management

Implements Task 3.2.1 requirements for service listing API endpoints with
production-grade FastAPI integration, authentication, rate limiting, and error handling.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import correlation_id
from app.db.database import get_async_session
from app.core.deps import get_current_user
from app.schemas.auth import UserResponse
from app.schemas.service_extended import (
    ServiceCreate, ServiceUpdate, ServiceResponse,
    ServiceListResponse, ServiceSearchRequest
)
from app.schemas.service import (
    ServiceCategoryResponse, ServiceImageResponse,
    ServiceImageUpdate
)
from app.services.service_listing_service import ServiceListingService
from app.services.service_category_service import ServiceCategoryService
from app.services.service_media_service import ServiceMediaService
from app.services.service_search_service import ServiceSearchService
from app.services.vendor_service import VendorService

logger = logging.getLogger(__name__)

router = APIRouter()


# Public Service Discovery Endpoints

@router.get("/", response_model=ServiceListResponse)
async def search_services(
    query: Optional[str] = Query(None, description="Search query"),
    category_id: Optional[int] = Query(None, description="Category filter"),
    location: Optional[str] = Query(None, description="Location filter"),
    min_price: Optional[float] = Query(None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceListResponse:
    """
    Search and filter services with pagination.

    Public endpoint for discovering services with advanced filtering options.
    Supports text search, category filtering, location filtering, and price range.

    **Rate Limiting**: 100 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Service search request",
        extra={
            "correlation_id": correlation,
            "query": query,
            "category_id": category_id,
            "location": location,
            "page": page
        }
    )

    try:
        # Create search request
        search_request = ServiceSearchRequest(
            query=query,
            category_id=category_id,
            location=location,
            min_price=min_price,
            max_price=max_price,
            page=page,
            per_page=per_page,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # Perform search
        search_service = ServiceSearchService(db)
        results = await search_service.search_services(search_request)

        logger.info(
            f"Service search completed: {len(results.services)} results",
            extra={
                "correlation_id": correlation,
                "total_results": results.total,
                "page": page
            }
        )

        return results

    except Exception as e:
        logger.error(f"Service search failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search services"
        )


@router.get("/{service_id}", response_model=ServiceResponse)
async def get_service(
    service_id: int = Path(..., description="Service ID"),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceResponse:
    """
    Get a specific service by ID.

    Public endpoint for retrieving detailed service information.
    Returns full service details including vendor info, images, and availability.

    **Rate Limiting**: 200 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get service request",
        extra={
            "correlation_id": correlation,
            "service_id": service_id
        }
    )

    try:
        service_service = ServiceListingService(db)
        service = await service_service.get_service(service_id)

        logger.info(
            f"Service retrieved: {service.title}",
            extra={
                "correlation_id": correlation,
                "service_id": service_id,
                "vendor_id": service.vendor_id
            }
        )

        return service

    except Exception as e:
        logger.error(f"Get service failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve service"
        )


@router.get("/featured", response_model=List[ServiceResponse])
async def get_featured_services(
    limit: int = Query(10, ge=1, le=50, description="Number of featured services"),
    category_id: Optional[int] = Query(None, description="Category filter"),
    location: Optional[str] = Query(None, description="Location filter"),
    db: AsyncSession = Depends(get_async_session)
) -> List[ServiceResponse]:
    """
    Get featured services for homepage or discovery.

    Public endpoint for retrieving featured services with optional filtering.
    Returns high-quality services promoted by the platform.

    **Rate Limiting**: 100 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get featured services request",
        extra={
            "correlation_id": correlation,
            "limit": limit,
            "category_id": category_id,
            "location": location
        }
    )

    try:
        search_service = ServiceSearchService(db)
        featured_services = await search_service.search_featured_services(
            limit=limit,
            category_id=category_id,
            location=location
        )

        logger.info(
            f"Featured services retrieved: {len(featured_services)}",
            extra={
                "correlation_id": correlation,
                "count": len(featured_services)
            }
        )

        return featured_services

    except Exception as e:
        logger.error(f"Get featured services failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve featured services"
        )


# Service Category Endpoints

@router.get("/categories", response_model=List[ServiceCategoryResponse])
async def list_categories(
    parent_id: Optional[int] = Query(None, description="Parent category ID for subcategories"),
    db: AsyncSession = Depends(get_async_session)
) -> List[ServiceCategoryResponse]:
    """
    List service categories.

    Public endpoint for retrieving service categories.
    If parent_id is provided, returns subcategories; otherwise returns root categories.

    **Rate Limiting**: 100 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"List categories request",
        extra={
            "correlation_id": correlation,
            "parent_id": parent_id
        }
    )

    try:
        category_service = ServiceCategoryService(db)

        if parent_id:
            categories = await category_service.list_category_children(parent_id)
        else:
            categories = await category_service.list_root_categories()

        logger.info(
            f"Categories retrieved: {len(categories)}",
            extra={
                "correlation_id": correlation,
                "parent_id": parent_id,
                "count": len(categories)
            }
        )

        return categories

    except Exception as e:
        logger.error(f"List categories failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve categories"
        )


@router.get("/categories/tree", response_model=List[Dict[str, Any]])
async def get_category_tree(
    db: AsyncSession = Depends(get_async_session)
) -> List[Dict[str, Any]]:
    """
    Get complete category tree structure.

    Public endpoint for retrieving the hierarchical category tree.
    Returns nested structure with all categories and subcategories.

    **Rate Limiting**: 50 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get category tree request",
        extra={"correlation_id": correlation}
    )

    try:
        category_service = ServiceCategoryService(db)
        tree = await category_service.get_category_tree()

        logger.info(
            f"Category tree retrieved: {len(tree)} root categories",
            extra={
                "correlation_id": correlation,
                "root_count": len(tree)
            }
        )

        return tree

    except Exception as e:
        logger.error(f"Get category tree failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve category tree"
        )


# Vendor Service Management Endpoints (Authentication Required)

@router.post("/", response_model=ServiceResponse)
async def create_service(
    service_data: ServiceCreate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceResponse:
    """
    Create a new service listing.

    Vendor endpoint for creating new service listings.
    Requires vendor authentication and active vendor status.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 10 creations per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Create service request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_title": service_data.title
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Create service
        service_service = ServiceListingService(db)
        service = await service_service.create_service(vendor.id, service_data)

        logger.info(
            f"Service created successfully: {service.title}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "service_id": service.id
            }
        )

        return service

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create service failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create service"
        )


@router.put("/{service_id}", response_model=ServiceResponse)
async def update_service(
    service_id: int = Path(..., description="Service ID"),
    service_data: ServiceUpdate = ...,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceResponse:
    """
    Update an existing service listing.

    Vendor endpoint for updating service listings.
    Only the service owner can update their services.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 20 updates per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update service request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Update service
        service_service = ServiceListingService(db)
        service = await service_service.update_service(service_id, vendor.id, service_data)

        logger.info(
            f"Service updated successfully: {service.title}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "service_id": service_id
            }
        )

        return service

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update service failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        elif "does not belong" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Service does not belong to this vendor"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update service"
        )


@router.delete("/{service_id}")
async def delete_service(
    service_id: int = Path(..., description="Service ID"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, str]:
    """
    Delete a service listing.

    Vendor endpoint for deleting service listings.
    Only the service owner can delete their services.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 5 deletions per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Delete service request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Delete service (soft delete by setting status to inactive)
        service_service = ServiceListingService(db)
        await service_service.delete_service(service_id, vendor.id)

        logger.info(
            f"Service deleted successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "service_id": service_id
            }
        )

        return {"message": "Service deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete service failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        elif "does not belong" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Service does not belong to this vendor"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete service"
        )


@router.get("/my", response_model=ServiceListResponse)
async def list_my_services(
    status: Optional[str] = Query(None, description="Service status filter"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceListResponse:
    """
    List current vendor's services.

    Vendor endpoint for retrieving their own service listings.
    Supports filtering by status and pagination.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 100 requests per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"List my services request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "status": status,
            "page": page
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Parse status filter
        status_filter = None
        if status:
            from app.models.service import ServiceStatus
            try:
                status_filter = ServiceStatus(status.upper())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {status}"
                )

        # List vendor services
        service_service = ServiceListingService(db)
        skip = (page - 1) * per_page
        services = await service_service.list_vendor_services(
            vendor.id, status_filter, skip, per_page
        )

        logger.info(
            f"Vendor services listed: {len(services.services)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "total": services.total
            }
        )

        return services

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"List my services failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve services"
        )


# Service Media Management Endpoints

@router.post("/{service_id}/images", response_model=ServiceImageResponse)
async def upload_service_image(
    service_id: int = Path(..., description="Service ID"),
    image: UploadFile = File(..., description="Image file"),
    alt_text: Optional[str] = Query(None, description="Alt text for accessibility"),
    is_primary: bool = Query(False, description="Set as primary image"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceImageResponse:
    """
    Upload an image for a service.

    Vendor endpoint for uploading service images.
    Supports image optimization and thumbnail generation.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 20 uploads per hour per vendor
    **File Limits**: Max 10MB, JPEG/PNG/WEBP only
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Upload service image request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "filename": image.filename,
            "is_primary": is_primary
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Read image file
        image_content = await image.read()

        # Upload image
        media_service = ServiceMediaService(db)
        uploaded_image = await media_service.upload_service_image(
            service_id=service_id,
            vendor_id=vendor.id,
            image_file=image_content,
            filename=image.filename,
            content_type=image.content_type,
            alt_text=alt_text,
            is_primary=is_primary
        )

        logger.info(
            f"Service image uploaded successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "service_id": service_id,
                "image_id": uploaded_image.id
            }
        )

        return uploaded_image

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload service image failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        elif "does not belong" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Service does not belong to this vendor"
            )
        elif "validation" in str(e).lower() or "invalid" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload image"
        )


@router.get("/{service_id}/images", response_model=List[ServiceImageResponse])
async def list_service_images(
    service_id: int = Path(..., description="Service ID"),
    db: AsyncSession = Depends(get_async_session)
) -> List[ServiceImageResponse]:
    """
    List images for a service.

    Public endpoint for retrieving service images.
    Returns all images ordered by display order.

    **Rate Limiting**: 100 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"List service images request",
        extra={
            "correlation_id": correlation,
            "service_id": service_id
        }
    )

    try:
        media_service = ServiceMediaService(db)
        images = await media_service.list_service_images(service_id)

        logger.info(
            f"Service images listed: {len(images)}",
            extra={
                "correlation_id": correlation,
                "service_id": service_id,
                "count": len(images)
            }
        )

        return images

    except Exception as e:
        logger.error(f"List service images failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve images"
        )


@router.put("/{service_id}/images/{image_id}", response_model=ServiceImageResponse)
async def update_service_image(
    service_id: int = Path(..., description="Service ID"),
    image_id: int = Path(..., description="Image ID"),
    image_data: ServiceImageUpdate = ...,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ServiceImageResponse:
    """
    Update service image metadata.

    Vendor endpoint for updating image alt text, primary status, etc.
    Only the service owner can update their service images.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 50 updates per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update service image request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "image_id": image_id
        }
    )

    try:
        # Get vendor for current user
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a registered vendor"
            )

        # Update image
        media_service = ServiceMediaService(db)
        updated_image = await media_service.update_service_image(
            image_id, vendor.id, image_data
        )

        logger.info(
            f"Service image updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "service_id": service_id,
                "image_id": image_id
            }
        )

        return updated_image

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update service image failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Image not found"
            )
        elif "does not belong" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Image does not belong to this vendor"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update image"
        )
