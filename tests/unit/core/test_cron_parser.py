"""
Comprehensive tests for Cron Expression Parser - Task 6.2.2 Phase 3.2

Tests cover:
- Standard 5-part and extended 6-part cron expressions
- Named schedule patterns (@daily, @weekly, etc.)
- Special characters (*, -, /, ?, L, W, #) validation
- Range expressions and step values
- Timezone validation
- Performance benchmarking (<50ms validation targets)
- Error handling and edge cases
"""

import pytest
from datetime import datetime
import time

from app.core.cron_parser import (
    CronExpressionParser,
    CronParseError,
    ParsedCronExpression,
    CronFieldType
)
from app.models.workflow_models import NamedSchedule, HolidayCalendar


class TestCronExpressionParser:
    """Test suite for CronExpressionParser."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = CronExpressionParser()
    
    def test_standard_5_part_cron_expressions(self):
        """Test standard 5-part cron expressions."""
        # Test basic expressions
        test_cases = [
            ("0 0 * * *", [0], [0], [0], list(range(1, 32)), list(range(1, 13)), list(range(0, 8))),
            ("30 14 * * *", [0], [30], [14], list(range(1, 32)), list(range(1, 13)), list(range(0, 8))),
            ("0 0 1 1 *", [0], [0], [0], [1], [1], list(range(0, 8))),
            ("*/15 * * * *", [0], [0, 15, 30, 45], list(range(0, 24)), list(range(1, 32)), list(range(1, 13)), list(range(0, 8))),
        ]
        
        for expr, exp_sec, exp_min, exp_hour, exp_day, exp_month, exp_weekday in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.seconds == exp_sec
            assert parsed.minutes == exp_min
            assert parsed.hours == exp_hour
            assert not parsed.is_named_schedule
    
    def test_extended_6_part_cron_expressions(self):
        """Test extended 6-part cron expressions with seconds."""
        test_cases = [
            ("0 0 0 * * *", [0], [0], [0]),
            ("30 30 14 * * *", [30], [30], [14]),
            ("*/10 */15 * * * *", list(range(0, 60, 10)), list(range(0, 60, 15)), list(range(0, 24))),
        ]
        
        for expr, exp_sec, exp_min, exp_hour in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.seconds == exp_sec
            assert parsed.minutes == exp_min
            assert parsed.hours == exp_hour
    
    def test_named_schedule_patterns(self):
        """Test named schedule patterns."""
        test_cases = [
            ("@daily", NamedSchedule.DAILY),
            ("@weekly", NamedSchedule.WEEKLY),
            ("@monthly", NamedSchedule.MONTHLY),
            ("@yearly", NamedSchedule.YEARLY),
            ("@hourly", NamedSchedule.HOURLY),
            ("@minutely", NamedSchedule.MINUTELY),
        ]
        
        for expr, expected_named in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.is_named_schedule
            assert parsed.named_schedule == expected_named
            assert parsed.original_expression == expr
    
    def test_special_characters_wildcard(self):
        """Test wildcard (*) character."""
        parsed = self.parser.parse("* * * * *")
        assert parsed.minutes == list(range(0, 60))
        assert parsed.hours == list(range(0, 24))
        assert parsed.days == list(range(1, 32))
        assert parsed.months == list(range(1, 13))
        assert parsed.weekdays == list(range(0, 8))
    
    def test_special_characters_ranges(self):
        """Test range expressions (-)."""
        test_cases = [
            ("0-5 * * * *", list(range(0, 6))),
            ("9-17 * * * *", list(range(9, 18))),
            ("1-5 * * * *", list(range(1, 6))),
        ]
        
        for expr, expected_values in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.minutes == expected_values
    
    def test_special_characters_steps(self):
        """Test step values (/)."""
        test_cases = [
            ("*/15 * * * *", [0, 15, 30, 45]),
            ("0-30/10 * * * *", [0, 10, 20, 30]),
            ("*/5 * * * *", list(range(0, 60, 5))),
        ]
        
        for expr, expected_values in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.minutes == expected_values
    
    def test_special_characters_lists(self):
        """Test comma-separated lists."""
        test_cases = [
            ("0,15,30,45 * * * *", [0, 15, 30, 45]),
            ("1,3,5 * * * *", [1, 3, 5]),
            ("0,30 0,12 * * *", [0, 30], [0, 12]),
        ]
        
        for expr, expected_min, *expected_hour in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.minutes == expected_min
            if expected_hour:
                assert parsed.hours == expected_hour[0]
    
    def test_month_aliases(self):
        """Test month name aliases."""
        test_cases = [
            ("0 0 1 JAN *", [1]),
            ("0 0 1 FEB,MAR *", [2, 3]),
            ("0 0 1 DEC *", [12]),
        ]
        
        for expr, expected_months in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.months == expected_months
    
    def test_weekday_aliases(self):
        """Test weekday name aliases."""
        test_cases = [
            ("0 0 * * SUN", [0]),
            ("0 0 * * MON,FRI", [1, 5]),
            ("0 0 * * SAT", [6]),
        ]
        
        for expr, expected_weekdays in test_cases:
            parsed = self.parser.parse(expr)
            assert parsed.weekdays == expected_weekdays
    
    def test_sunday_as_0_and_7(self):
        """Test Sunday represented as both 0 and 7."""
        parsed_0 = self.parser.parse("0 0 * * 0")
        parsed_7 = self.parser.parse("0 0 * * 7")
        
        assert parsed_0.weekdays == [0]
        assert parsed_7.weekdays == [0]  # 7 should be converted to 0
    
    def test_timezone_validation(self):
        """Test timezone validation."""
        # Valid timezones
        valid_timezones = ["UTC", "America/New_York", "Europe/London", "Asia/Tokyo"]
        for tz in valid_timezones:
            parsed = self.parser.parse("0 0 * * *", timezone=tz)
            assert parsed.timezone == tz
        
        # Invalid timezone
        with pytest.raises(CronParseError, match="Invalid timezone"):
            self.parser.parse("0 0 * * *", timezone="Invalid/Timezone")
    
    def test_business_days_and_holidays(self):
        """Test business days and holiday calendar integration."""
        parsed = self.parser.parse(
            "0 9 * * *",
            business_days_only=True,
            exclude_holidays=True,
            holiday_calendar=HolidayCalendar.US
        )
        
        assert parsed.business_days_only is True
        assert parsed.exclude_holidays is True
        assert parsed.holiday_calendar == HolidayCalendar.US
    
    def test_invalid_expressions(self):
        """Test invalid cron expressions."""
        invalid_cases = [
            ("", "Invalid cron expression format"),
            ("0 0 0", "Invalid cron expression format"),
            ("0 0 0 0 0 0 0", "Invalid cron expression format"),
            ("60 * * * *", "out of range"),
            ("* 25 * * *", "out of range"),
            ("* * 32 * *", "out of range"),
            ("* * * 13 *", "out of range"),
            ("* * * * 8", "out of range"),
            ("invalid * * * *", "Invalid value"),
        ]
        
        for expr, expected_error in invalid_cases:
            with pytest.raises(CronParseError, match=expected_error):
                self.parser.parse(expr)
    
    def test_invalid_named_schedules(self):
        """Test invalid named schedules."""
        with pytest.raises(CronParseError, match="Invalid named schedule"):
            self.parser.parse("@invalid")
    
    def test_performance_validation_target(self):
        """Test that validation meets <50ms performance target."""
        expressions = [
            "0 0 * * *",
            "*/15 */2 1-5 1-12 1-5",
            "@daily",
            "0,15,30,45 9-17 * * MON-FRI",
            "0 0 1 JAN,APR,JUL,OCT *"
        ]
        
        for expr in expressions:
            start_time = time.time()
            result = self.parser.validate_expression(expr)
            end_time = time.time()
            
            validation_time_ms = (end_time - start_time) * 1000
            
            assert result["valid"] is True
            assert validation_time_ms < 50, f"Validation took {validation_time_ms}ms, expected <50ms"
            assert result["validation_time_ms"] < 50
    
    def test_validation_results_structure(self):
        """Test validation results structure."""
        # Valid expression
        result = self.parser.validate_expression("0 0 * * *")
        
        assert "valid" in result
        assert "parsed" in result
        assert "validation_time_ms" in result
        assert "field_counts" in result
        assert "is_named_schedule" in result
        
        assert result["valid"] is True
        assert isinstance(result["parsed"], ParsedCronExpression)
        assert isinstance(result["validation_time_ms"], float)
        assert isinstance(result["field_counts"], dict)
        
        # Invalid expression
        result = self.parser.validate_expression("invalid")
        
        assert result["valid"] is False
        assert "error" in result
        assert "validation_time_ms" in result
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Leap year handling
        parsed = self.parser.parse("0 0 29 2 *")
        assert 29 in parsed.days
        assert 2 in parsed.months
        
        # End of month
        parsed = self.parser.parse("0 0 31 * *")
        assert 31 in parsed.days
        
        # Midnight
        parsed = self.parser.parse("0 0 0 * * *")
        assert parsed.seconds == [0]
        assert parsed.minutes == [0]
        assert parsed.hours == [0]
    
    def test_complex_expressions(self):
        """Test complex real-world cron expressions."""
        complex_cases = [
            # Every 15 minutes during business hours on weekdays
            "*/15 9-17 * * 1-5",
            # First Monday of every month at 9 AM
            "0 9 1-7 * 1",
            # Every 30 seconds during the first 5 minutes of every hour
            "*/30 0-4 * * * *",
            # Multiple specific times
            "0 0,6,12,18 * * *",
        ]
        
        for expr in complex_cases:
            parsed = self.parser.parse(expr)
            assert parsed is not None
            assert not parsed.is_named_schedule
            
            # Validate performance
            result = self.parser.validate_expression(expr)
            assert result["valid"] is True
            assert result["validation_time_ms"] < 50
