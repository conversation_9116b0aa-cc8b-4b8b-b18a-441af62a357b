"""
Review Analytics Service for Culture Connect Backend API.

This module provides comprehensive review analytics business logic orchestration including:
- Performance metrics calculation and aggregation with trend analysis
- Automated report generation for vendors and platform administrators
- Bulk analytics processing with >1000 records/second performance targets
- Integration with Redis caching for frequently accessed analytics data
- Real-time analytics updates and dashboard data preparation

Implements Task 4.4.1 Phase 4 requirements for review analytics service implementation with
production-grade business logic following established BaseService patterns.
"""

import logging
from datetime import datetime, timezone, timedelta, date
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.repositories.review_analytics_repository import ReviewAnalyticsRepository
from app.repositories.review_repository import ReviewRepository
from app.models.review_models import ReviewAnalytics, Review
from app.schemas.review_schemas import (
    ReviewAnalyticsSchema, ReviewSummarySchema
)
from app.repositories.base import PaginationParams, QueryResult
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.config import settings

logger = logging.getLogger(__name__)


class ReviewAnalyticsService(BaseService[ReviewAnalytics, ReviewAnalyticsRepository]):
    """
    Review analytics service for performance metrics and business intelligence.

    Provides comprehensive analytics management including:
    - Performance metrics calculation with trend analysis
    - Automated report generation for vendors and administrators
    - Bulk analytics processing with >1000 records/second targets
    - Real-time analytics updates and dashboard preparation
    - Integration with caching for frequently accessed data
    """

    def __init__(self, db: AsyncSession):
        """Initialize review analytics service with dependencies."""
        super().__init__(
            repository_class=ReviewAnalyticsRepository,
            model_class=ReviewAnalytics,
            db_session=db
        )

        # Initialize repositories
        self.review_repository = ReviewRepository(db)

        # Analytics configuration
        self.default_period_days = getattr(settings, 'ANALYTICS_DEFAULT_PERIOD_DAYS', 30)
        self.bulk_batch_size = getattr(settings, 'ANALYTICS_BULK_BATCH_SIZE', 1000)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate analytics creation data.

        Args:
            data: Analytics creation data

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['vendor_id', 'period_start', 'period_end', 'total_reviews']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate period dates
        period_start = data.get('period_start')
        period_end = data.get('period_end')
        
        if not isinstance(period_start, date) or not isinstance(period_end, date):
            raise ValidationError("Period start and end must be date objects")

        if period_start >= period_end:
            raise ValidationError("Period start must be before period end")

        # Validate numeric fields
        total_reviews = data.get('total_reviews', 0)
        if not isinstance(total_reviews, int) or total_reviews < 0:
            raise ValidationError("Total reviews must be a non-negative integer")

        # Validate average rating if provided
        avg_rating = data.get('average_rating')
        if avg_rating is not None:
            if not isinstance(avg_rating, (int, float, Decimal)) or avg_rating < 1 or avg_rating > 5:
                raise ValidationError("Average rating must be between 1 and 5")

        return data

    async def calculate_vendor_analytics(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date,
        save_to_db: bool = True
    ) -> ReviewAnalytics:
        """
        Calculate comprehensive analytics for a vendor and period.

        Performance target: <500ms for analytics calculation including database save.

        Args:
            vendor_id: Vendor ID
            period_start: Period start date
            period_end: Period end date
            save_to_db: Whether to save results to database

        Returns:
            ReviewAnalytics instance with calculated metrics

        Raises:
            ValidationError: If parameters are invalid
            ServiceError: If calculation fails
        """
        correlation = self.log_operation_start(
            "calculate_vendor_analytics",
            vendor_id=vendor_id,
            period_start=period_start,
            period_end=period_end
        )

        try:
            # Validate period
            if period_start >= period_end:
                raise ValidationError("Period start must be before period end")

            # Calculate analytics using repository
            async with self.get_session_context() as session:
                repository = ReviewAnalyticsRepository(session)
                
                analytics_data = await repository.calculate_vendor_analytics(
                    vendor_id=vendor_id,
                    period_start=period_start,
                    period_end=period_end
                )

                # Create or update analytics record if requested
                analytics_record = None
                if save_to_db:
                    # Check for existing record
                    existing_analytics = await repository.get_analytics_by_period(
                        vendor_id=vendor_id,
                        period_start=period_start,
                        period_end=period_end
                    )

                    if existing_analytics:
                        # Update existing record
                        analytics_record = await repository.update(
                            existing_analytics.id,
                            analytics_data
                        )
                    else:
                        # Create new record
                        analytics_record = await repository.create_analytics_record(
                            vendor_id=vendor_id,
                            period_start=period_start,
                            period_end=period_end,
                            analytics_data=analytics_data
                        )

                # Track metrics
                metrics_collector.increment_counter(
                    "vendor_analytics_calculated",
                    tags={
                        "vendor_id": str(vendor_id),
                        "total_reviews": str(analytics_data.get('total_reviews', 0))
                    }
                )

                self.log_operation_success(
                    correlation,
                    f"Analytics calculated for vendor {vendor_id}: {analytics_data.get('total_reviews', 0)} reviews"
                )

                return analytics_record or self._create_analytics_object(
                    vendor_id, period_start, period_end, analytics_data
                )

        except Exception as e:
            await self.handle_service_error(e, "calculate_vendor_analytics", {
                "vendor_id": vendor_id,
                "period_start": period_start,
                "period_end": period_end
            })

    async def generate_vendor_report(
        self,
        vendor_id: int,
        period_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive vendor performance report.

        Performance target: <1000ms for complete vendor report generation.

        Args:
            vendor_id: Vendor ID
            period_days: Number of days to include in report (default: 30)

        Returns:
            Comprehensive vendor report dictionary

        Raises:
            ServiceError: If report generation fails
        """
        correlation = self.log_operation_start(
            "generate_vendor_report",
            vendor_id=vendor_id,
            period_days=period_days
        )

        try:
            period_days = period_days or self.default_period_days
            period_end = date.today()
            period_start = period_end - timedelta(days=period_days)

            # Calculate current period analytics
            current_analytics = await self.calculate_vendor_analytics(
                vendor_id=vendor_id,
                period_start=period_start,
                period_end=period_end,
                save_to_db=False
            )

            # Calculate previous period for comparison
            prev_period_end = period_start
            prev_period_start = prev_period_end - timedelta(days=period_days)
            
            previous_analytics = await self.calculate_vendor_analytics(
                vendor_id=vendor_id,
                period_start=prev_period_start,
                period_end=prev_period_end,
                save_to_db=False
            )

            # Generate comparison metrics
            comparison = self._calculate_period_comparison(current_analytics, previous_analytics)

            # Get recent analytics history
            async with self.get_session_context() as session:
                repository = ReviewAnalyticsRepository(session)
                
                history_start = period_end - timedelta(days=period_days * 3)  # 3 months
                history_result = await repository.get_vendor_analytics(
                    vendor_id=vendor_id,
                    date_range=(history_start, period_end),
                    pagination=PaginationParams(page=1, per_page=100)
                )

            # Compile comprehensive report
            report = {
                'vendor_id': vendor_id,
                'report_period': {
                    'start_date': period_start,
                    'end_date': period_end,
                    'days': period_days
                },
                'current_metrics': self._extract_analytics_metrics(current_analytics),
                'previous_metrics': self._extract_analytics_metrics(previous_analytics),
                'comparison': comparison,
                'trends': self._analyze_trends(history_result.items),
                'recommendations': self._generate_recommendations(current_analytics, comparison),
                'generated_at': datetime.now(timezone.utc)
            }

            self.log_operation_success(
                correlation,
                f"Vendor report generated for vendor {vendor_id}"
            )

            return report

        except Exception as e:
            await self.handle_service_error(e, "generate_vendor_report", {
                "vendor_id": vendor_id,
                "period_days": period_days
            })

    async def bulk_update_analytics(
        self,
        vendor_ids: List[int],
        period_start: date,
        period_end: date
    ) -> Dict[str, Any]:
        """
        Bulk update analytics for multiple vendors.

        Performance target: >1000 records/second for bulk analytics processing.

        Args:
            vendor_ids: List of vendor IDs to process
            period_start: Period start date
            period_end: Period end date

        Returns:
            Bulk processing results summary

        Raises:
            ServiceError: If bulk processing fails
        """
        correlation = self.log_operation_start(
            "bulk_update_analytics",
            vendor_count=len(vendor_ids),
            period_start=period_start,
            period_end=period_end
        )

        try:
            start_time = datetime.now(timezone.utc)
            processed_count = 0
            failed_count = 0
            failed_vendors = []

            # Process vendors in batches
            batch_size = min(self.bulk_batch_size, 100)  # Limit concurrent operations
            
            for i in range(0, len(vendor_ids), batch_size):
                batch = vendor_ids[i:i + batch_size]
                
                # Process batch concurrently
                tasks = [
                    self._process_vendor_analytics(vendor_id, period_start, period_end)
                    for vendor_id in batch
                ]
                
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for vendor_id, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        failed_count += 1
                        failed_vendors.append({
                            'vendor_id': vendor_id,
                            'error': str(result)
                        })
                        logger.error(f"Failed to process analytics for vendor {vendor_id}: {result}")
                    else:
                        processed_count += 1

            # Calculate performance metrics
            end_time = datetime.now(timezone.utc)
            processing_time = (end_time - start_time).total_seconds()
            records_per_second = processed_count / processing_time if processing_time > 0 else 0

            # Track metrics
            metrics_collector.increment_counter(
                "bulk_analytics_processed",
                tags={"processed": str(processed_count), "failed": str(failed_count)}
            )

            results = {
                'total_vendors': len(vendor_ids),
                'processed_count': processed_count,
                'failed_count': failed_count,
                'failed_vendors': failed_vendors,
                'processing_time_seconds': processing_time,
                'records_per_second': records_per_second,
                'period_start': period_start,
                'period_end': period_end,
                'processed_at': end_time
            }

            self.log_operation_success(
                correlation,
                f"Bulk analytics processed: {processed_count}/{len(vendor_ids)} vendors, {records_per_second:.2f} records/sec"
            )

            return results

        except Exception as e:
            await self.handle_service_error(e, "bulk_update_analytics", {
                "vendor_count": len(vendor_ids),
                "period_start": period_start,
                "period_end": period_end
            })

    async def get_platform_analytics_summary(
        self,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get platform-wide analytics summary.

        Performance target: <200ms for platform summary queries.

        Args:
            date_range: Optional date range filter

        Returns:
            Platform analytics summary
        """
        correlation = self.log_operation_start("get_platform_analytics_summary")

        try:
            async with self.get_session_context() as session:
                repository = ReviewAnalyticsRepository(session)
                
                summary = await repository.get_analytics_summary(date_range=date_range)

                # Enhance with additional platform metrics
                enhanced_summary = {
                    **summary,
                    'date_range': {
                        'start': date_range[0] if date_range else None,
                        'end': date_range[1] if date_range else None
                    },
                    'generated_at': datetime.now(timezone.utc)
                }

                self.log_operation_success(
                    correlation,
                    f"Platform summary generated: {summary.get('active_vendors', 0)} vendors"
                )

                return enhanced_summary

        except Exception as e:
            await self.handle_service_error(e, "get_platform_analytics_summary", {})

    # Private helper methods

    async def _process_vendor_analytics(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date
    ) -> ReviewAnalytics:
        """Process analytics for a single vendor."""
        return await self.calculate_vendor_analytics(
            vendor_id=vendor_id,
            period_start=period_start,
            period_end=period_end,
            save_to_db=True
        )

    def _create_analytics_object(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date,
        analytics_data: Dict[str, Any]
    ) -> ReviewAnalytics:
        """Create analytics object from calculated data."""
        # This would typically be done by the repository, but for non-persisted objects
        analytics = ReviewAnalytics()
        analytics.vendor_id = vendor_id
        analytics.period_start = period_start
        analytics.period_end = period_end
        
        for key, value in analytics_data.items():
            if hasattr(analytics, key):
                setattr(analytics, key, value)
        
        return analytics

    def _extract_analytics_metrics(self, analytics: ReviewAnalytics) -> Dict[str, Any]:
        """Extract key metrics from analytics object."""
        if not analytics:
            return {}
        
        return {
            'total_reviews': analytics.total_reviews,
            'average_rating': float(analytics.average_rating) if analytics.average_rating else 0.0,
            'rating_distribution': analytics.rating_distribution or {},
            'sentiment_breakdown': analytics.sentiment_breakdown or {},
            'response_rate': float(analytics.response_rate) if analytics.response_rate else 0.0,
            'verified_reviews_count': analytics.verified_reviews_count,
            'helpful_votes_total': analytics.helpful_votes_total,
            'rating_trend': analytics.rating_trend,
            'review_volume_trend': analytics.review_volume_trend
        }

    def _calculate_period_comparison(
        self,
        current: ReviewAnalytics,
        previous: ReviewAnalytics
    ) -> Dict[str, Any]:
        """Calculate comparison metrics between periods."""
        if not current or not previous:
            return {}

        def safe_percentage_change(current_val, previous_val):
            if not previous_val or previous_val == 0:
                return 0.0
            return ((current_val - previous_val) / previous_val) * 100

        current_rating = float(current.average_rating) if current.average_rating else 0.0
        previous_rating = float(previous.average_rating) if previous.average_rating else 0.0

        return {
            'review_count_change': current.total_reviews - previous.total_reviews,
            'review_count_change_percent': safe_percentage_change(
                current.total_reviews, previous.total_reviews
            ),
            'rating_change': current_rating - previous_rating,
            'rating_change_percent': safe_percentage_change(current_rating, previous_rating),
            'response_rate_change': float(current.response_rate or 0) - float(previous.response_rate or 0),
            'helpful_votes_change': current.helpful_votes_total - previous.helpful_votes_total
        }

    def _analyze_trends(self, history: List[ReviewAnalytics]) -> Dict[str, Any]:
        """Analyze trends from historical analytics data."""
        if len(history) < 2:
            return {'trend_analysis': 'Insufficient data for trend analysis'}

        # Sort by period end date
        sorted_history = sorted(history, key=lambda x: x.period_end)
        
        # Calculate trend indicators
        ratings = [float(a.average_rating) if a.average_rating else 0.0 for a in sorted_history]
        review_counts = [a.total_reviews for a in sorted_history]

        return {
            'rating_trend_direction': self._calculate_trend_direction(ratings),
            'volume_trend_direction': self._calculate_trend_direction(review_counts),
            'periods_analyzed': len(sorted_history),
            'trend_strength': self._calculate_trend_strength(ratings)
        }

    def _calculate_trend_direction(self, values: List[float]) -> str:
        """Calculate trend direction from a series of values."""
        if len(values) < 2:
            return 'stable'
        
        increases = sum(1 for i in range(1, len(values)) if values[i] > values[i-1])
        decreases = sum(1 for i in range(1, len(values)) if values[i] < values[i-1])
        
        if increases > decreases:
            return 'improving'
        elif decreases > increases:
            return 'declining'
        else:
            return 'stable'

    def _calculate_trend_strength(self, values: List[float]) -> str:
        """Calculate trend strength."""
        if len(values) < 3:
            return 'weak'
        
        # Simple trend strength based on consistency
        changes = [values[i] - values[i-1] for i in range(1, len(values))]
        consistent_direction = sum(1 for c in changes if c > 0) / len(changes)
        
        if consistent_direction > 0.7 or consistent_direction < 0.3:
            return 'strong'
        elif consistent_direction > 0.6 or consistent_direction < 0.4:
            return 'moderate'
        else:
            return 'weak'

    def _generate_recommendations(
        self,
        analytics: ReviewAnalytics,
        comparison: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable recommendations based on analytics."""
        recommendations = []
        
        if not analytics:
            return recommendations

        # Rating-based recommendations
        avg_rating = float(analytics.average_rating) if analytics.average_rating else 0.0
        if avg_rating < 3.5:
            recommendations.append("Focus on improving service quality to increase average rating")
        
        # Response rate recommendations
        response_rate = float(analytics.response_rate) if analytics.response_rate else 0.0
        if response_rate < 0.5:
            recommendations.append("Increase response rate to customer reviews to improve engagement")
        
        # Volume recommendations
        if analytics.total_reviews < 10:
            recommendations.append("Encourage more customers to leave reviews to build credibility")
        
        # Trend-based recommendations
        if comparison.get('rating_change', 0) < 0:
            recommendations.append("Address recent decline in ratings by analyzing negative feedback")
        
        if comparison.get('review_count_change', 0) < 0:
            recommendations.append("Implement strategies to increase review volume")

        return recommendations
