# Culture Connect Backend - Kubernetes Deployment
# Main application deployment with auto-scaling and production optimizations

apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: api
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "Culture Connect Backend API deployment"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: api
  template:
    metadata:
      labels:
        app.kubernetes.io/name: culture-connect
        app.kubernetes.io/instance: culture-connect
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/component: api
        app.kubernetes.io/part-of: culture-connect-backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        co.elastic.logs/enabled: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.add_error_key: "true"
    spec:
      serviceAccountName: culture-connect-api
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      imagePullSecrets:
      - name: culture-connect-registry
      containers:
      - name: api
        image: ghcr.io/culture-connect/backend:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        env:
        - name: CC_VERSION
          value: "1.0.0"
        - name: CC_BUILD_ID
          value: "production-001"
        envFrom:
        - configMapRef:
            name: culture-connect-config
        - secretRef:
            name: culture-connect-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
            ephemeral-storage: "1Gi"
          limits:
            memory: "1Gi"
            cpu: "500m"
            ephemeral-storage: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
        - name: app-uploads
          mountPath: /app/uploads
        - name: geoip-data
          mountPath: /app/data
          readOnly: true
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
      - name: nginx
        image: nginx:1.25-alpine
        ports:
        - name: nginx-http
          containerPort: 80
          protocol: TCP
        - name: nginx-https
          containerPort: 443
          protocol: TCP
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: nginx-http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /health
            port: nginx-http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 101
          runAsGroup: 101
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
        - name: culture-connect-tls
          mountPath: /etc/ssl/certs
          readOnly: true
        - name: culture-connect-tls
          mountPath: /etc/ssl/private
          readOnly: true
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 1Gi
      - name: app-logs
        persistentVolumeClaim:
          claimName: culture-connect-logs-pvc
      - name: app-uploads
        persistentVolumeClaim:
          claimName: culture-connect-uploads-pvc
      - name: geoip-data
        configMap:
          name: geoip-database
      - name: nginx-config
        configMap:
          name: culture-connect-nginx-config
      - name: nginx-cache
        emptyDir:
          sizeLimit: 100Mi
      - name: nginx-run
        emptyDir:
          sizeLimit: 10Mi
      - name: culture-connect-tls
        secret:
          secretName: culture-connect-tls
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - culture-connect
                - key: app.kubernetes.io/component
                  operator: In
                  values:
                  - api
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      terminationGracePeriodSeconds: 60
      dnsPolicy: ClusterFirst
      restartPolicy: Always

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-worker
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-worker
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: celery-worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: culture-connect
        app.kubernetes.io/component: celery-worker
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9540"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: culture-connect-api
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      imagePullSecrets:
      - name: culture-connect-registry
      containers:
      - name: celery-worker
        image: ghcr.io/culture-connect/backend:latest
        command: ["celery", "-A", "app.core.celery_config", "worker", "--loglevel=info", "--concurrency=4"]
        envFrom:
        - configMapRef:
            name: culture-connect-config
        - secretRef:
            name: culture-connect-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - app.core.celery_config
            - inspect
            - ping
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 500Mi
      - name: app-logs
        persistentVolumeClaim:
          claimName: culture-connect-logs-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-beat
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-beat
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: celery-beat
  template:
    metadata:
      labels:
        app.kubernetes.io/name: culture-connect
        app.kubernetes.io/component: celery-beat
    spec:
      serviceAccountName: culture-connect-api
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      imagePullSecrets:
      - name: culture-connect-registry
      containers:
      - name: celery-beat
        image: ghcr.io/culture-connect/backend:latest
        command: ["celery", "-A", "app.core.celery_config", "beat", "--loglevel=info"]
        envFrom:
        - configMapRef:
            name: culture-connect-config
        - secretRef:
            name: culture-connect-secrets
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
        livenessProbe:
          exec:
            command:
            - test
            - -f
            - /app/celerybeat-schedule
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 10
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: celery-beat-data
          mountPath: /app/celerybeat-schedule
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      - name: celery-beat-data
        persistentVolumeClaim:
          claimName: culture-connect-celery-beat-pvc
      - name: app-logs
        persistentVolumeClaim:
          claimName: culture-connect-logs-pvc
