"""
WebSocket Infrastructure models for Culture Connect Backend API.

This module defines comprehensive WebSocket models for real-time communication including:
- WebSocket connection management and tracking
- Real-time event handling and routing
- Message delivery and acknowledgment tracking
- Connection state management and monitoring
- Performance metrics and analytics

Implements Task 6.1.1 Phase 1 requirements for WebSocket infrastructure with:
- Connection lifecycle management with authentication
- Event-driven messaging with type safety
- Performance monitoring and metrics collection
- Scalable architecture supporting concurrent connections
- Integration with existing RBAC and notification systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Float, CheckConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.db.base import BaseModelWithAudit
from app.core.security import UserRole


class ConnectionStatus(str, Enum):
    """WebSocket connection status enumeration."""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    RECONNECTING = "reconnecting"


class EventType(str, Enum):
    """WebSocket event type enumeration."""
    # Connection events
    CONNECTION_ESTABLISHED = "connection.established"
    CONNECTION_AUTHENTICATED = "connection.authenticated"
    CONNECTION_CLOSED = "connection.closed"
    CONNECTION_ERROR = "connection.error"

    # Booking events
    BOOKING_CREATED = "booking.created"
    BOOKING_UPDATED = "booking.updated"
    BOOKING_STATUS_CHANGED = "booking.status_changed"
    BOOKING_CANCELLED = "booking.cancelled"

    # Message events
    MESSAGE_SENT = "message.sent"
    MESSAGE_RECEIVED = "message.received"
    MESSAGE_READ = "message.read"
    TYPING_START = "typing.start"
    TYPING_STOP = "typing.stop"

    # Review events
    REVIEW_CREATED = "review.created"
    REVIEW_UPDATED = "review.updated"
    REVIEW_RESPONSE_ADDED = "review.response_added"

    # Notification events
    NOTIFICATION_SENT = "notification.sent"
    NOTIFICATION_READ = "notification.read"
    NOTIFICATION_DELIVERED = "notification.delivered"
    NOTIFICATION_DISMISSED = "notification.dismissed"

    # Real-time communication events
    CONVERSATION_STARTED = "conversation.started"
    CONVERSATION_ENDED = "conversation.ended"
    PARTICIPANT_JOINED = "participant.joined"
    PARTICIPANT_LEFT = "participant.left"
    CONVERSATION_SUMMARY_UPDATED = "conversation.summary_updated"

    # Enhanced user presence events
    USER_ONLINE = "user.online"
    USER_OFFLINE = "user.offline"
    USER_AWAY = "user.away"
    USER_PRESENCE_CHANGED = "user.presence_changed"
    USER_ACTIVITY_DETECTED = "user.activity_detected"

    # System events
    SYSTEM_MAINTENANCE = "system.maintenance"
    SYSTEM_ANNOUNCEMENT = "system.announcement"
    SYSTEM_BROADCAST = "system.broadcast"
    SYSTEM_HEALTH_CHECK = "system.health_check"

    # Connection management events
    CONNECTION_HEARTBEAT = "connection.heartbeat"
    CONNECTION_RECONNECT = "connection.reconnect"
    CONNECTION_RATE_LIMITED = "connection.rate_limited"


class EventPriority(str, Enum):
    """Event priority levels for message routing."""
    CRITICAL = "critical"      # Immediate delivery required
    HIGH = "high"             # High priority, fast delivery
    NORMAL = "normal"         # Standard delivery
    LOW = "low"              # Can be batched or delayed


class WebSocketConnection(BaseModelWithAudit):
    """
    WebSocket connection tracking model.

    Tracks active WebSocket connections with authentication,
    session management, and performance monitoring.
    """

    __tablename__ = "websocket_connections"

    # Connection identification
    connection_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique connection identifier"
    )

    session_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Session identifier for connection grouping"
    )

    # User association
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Connected user ID"
    )

    # Connection details
    status = Column(
        String(20),
        nullable=False,
        default=ConnectionStatus.CONNECTING,
        index=True,
        doc="Current connection status"
    )

    client_type = Column(
        String(50),
        nullable=True,
        doc="Client type (web, mobile, pwa)"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="Client user agent string"
    )

    ip_address = Column(
        String(45),
        nullable=True,
        index=True,
        doc="Client IP address"
    )

    # Authentication
    authenticated_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Authentication timestamp"
    )

    auth_token_hash = Column(
        String(255),
        nullable=True,
        doc="Hashed authentication token"
    )

    # Connection lifecycle
    connected_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Connection establishment timestamp"
    )

    last_activity_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Last activity timestamp"
    )

    disconnected_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Disconnection timestamp"
    )

    # Performance metrics
    message_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total messages sent through this connection"
    )

    bytes_sent = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total bytes sent through this connection"
    )

    bytes_received = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total bytes received through this connection"
    )

    # Error tracking
    error_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of errors encountered"
    )

    last_error = Column(
        Text,
        nullable=True,
        doc="Last error message"
    )

    last_error_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last error timestamp"
    )

    # Connection metadata
    metadata_json = Column(
        JSONB,
        nullable=True,
        doc="Additional connection metadata"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="websocket_connections",
        foreign_keys=[user_id],
        doc="Connected user"
    )

    events = relationship(
        "WebSocketEvent",
        back_populates="connection",
        cascade="all, delete-orphan",
        doc="Events sent through this connection"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "status IN ('connecting', 'connected', 'authenticated', 'disconnecting', 'disconnected', 'error', 'reconnecting')",
            name="ck_websocket_connection_status"
        ),
        CheckConstraint(
            "message_count >= 0",
            name="ck_websocket_connection_message_count"
        ),
        CheckConstraint(
            "bytes_sent >= 0",
            name="ck_websocket_connection_bytes_sent"
        ),
        CheckConstraint(
            "bytes_received >= 0",
            name="ck_websocket_connection_bytes_received"
        ),
        CheckConstraint(
            "error_count >= 0",
            name="ck_websocket_connection_error_count"
        ),
        # Performance indexes
        Index("idx_websocket_connection_user_status", "user_id", "status"),
        Index("idx_websocket_connection_active", "status", "last_activity_at"),
        Index("idx_websocket_connection_session", "session_id", "user_id"),
        Index("idx_websocket_connection_performance", "connected_at", "message_count"),
    )

    def __repr__(self) -> str:
        """String representation of the WebSocket connection."""
        return f"<WebSocketConnection(id={self.id}, connection_id='{self.connection_id}', user_id={self.user_id}, status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if connection is currently active."""
        return self.status in [ConnectionStatus.CONNECTED, ConnectionStatus.AUTHENTICATED]

    @property
    def is_authenticated(self) -> bool:
        """Check if connection is authenticated."""
        return self.status == ConnectionStatus.AUTHENTICATED

    @property
    def connection_duration(self) -> Optional[timedelta]:
        """Calculate connection duration."""
        if self.disconnected_at:
            return self.disconnected_at - self.connected_at
        return datetime.now(timezone.utc) - self.connected_at

    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity_at = datetime.now(timezone.utc)

    def increment_message_count(self, bytes_count: int = 0):
        """Increment message and bytes counters."""
        self.message_count += 1
        self.bytes_sent += bytes_count
        self.update_activity()

    def record_error(self, error_message: str):
        """Record connection error."""
        self.error_count += 1
        self.last_error = error_message
        self.last_error_at = datetime.now(timezone.utc)


class WebSocketEvent(BaseModelWithAudit):
    """
    WebSocket event tracking model.

    Tracks all events sent through WebSocket connections with
    delivery confirmation, retry logic, and performance metrics.
    """

    __tablename__ = "websocket_events"

    # Event identification
    event_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        default=lambda: str(uuid.uuid4()),
        doc="Unique event identifier"
    )

    # Connection association
    connection_id = Column(
        Integer,
        ForeignKey("websocket_connections.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="WebSocket connection ID"
    )

    # Event details
    event_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of event"
    )

    priority = Column(
        String(20),
        nullable=False,
        default=EventPriority.NORMAL,
        index=True,
        doc="Event priority level"
    )

    # Event data
    payload = Column(
        JSONB,
        nullable=False,
        doc="Event payload data"
    )

    # Delivery tracking
    sent_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Event sent timestamp"
    )

    delivered_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Event delivery confirmation timestamp"
    )

    acknowledged_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Event acknowledgment timestamp"
    )

    # Retry logic
    retry_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of delivery retry attempts"
    )

    max_retries = Column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum retry attempts"
    )

    next_retry_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Next retry attempt timestamp"
    )

    # Status tracking
    delivery_status = Column(
        String(20),
        nullable=False,
        default="pending",
        index=True,
        doc="Delivery status (pending, sent, delivered, acknowledged, failed)"
    )

    # Performance metrics
    payload_size = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Event payload size in bytes"
    )

    processing_time_ms = Column(
        Float,
        nullable=True,
        doc="Event processing time in milliseconds"
    )

    # Error tracking
    error_message = Column(
        Text,
        nullable=True,
        doc="Error message if delivery failed"
    )

    error_code = Column(
        String(50),
        nullable=True,
        doc="Error code for categorization"
    )

    # Event metadata
    metadata_json = Column(
        JSONB,
        nullable=True,
        doc="Additional event metadata"
    )

    # Relationships
    connection = relationship(
        "WebSocketConnection",
        back_populates="events",
        doc="Associated WebSocket connection"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "priority IN ('critical', 'high', 'normal', 'low')",
            name="ck_websocket_event_priority"
        ),
        CheckConstraint(
            "delivery_status IN ('pending', 'sent', 'delivered', 'acknowledged', 'failed')",
            name="ck_websocket_event_delivery_status"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="ck_websocket_event_retry_count"
        ),
        CheckConstraint(
            "max_retries >= 0",
            name="ck_websocket_event_max_retries"
        ),
        CheckConstraint(
            "payload_size >= 0",
            name="ck_websocket_event_payload_size"
        ),
        CheckConstraint(
            "processing_time_ms >= 0",
            name="ck_websocket_event_processing_time"
        ),
        # Performance indexes
        Index("idx_websocket_event_type_priority", "event_type", "priority"),
        Index("idx_websocket_event_delivery", "delivery_status", "sent_at"),
        Index("idx_websocket_event_retry", "next_retry_at", "retry_count"),
        Index("idx_websocket_event_performance", "sent_at", "processing_time_ms"),
    )

    def __repr__(self) -> str:
        """String representation of the WebSocket event."""
        return f"<WebSocketEvent(id={self.id}, event_id='{self.event_id}', event_type='{self.event_type}', status='{self.delivery_status}')>"

    @property
    def is_delivered(self) -> bool:
        """Check if event has been delivered."""
        return self.delivery_status in ["delivered", "acknowledged"]

    @property
    def is_failed(self) -> bool:
        """Check if event delivery has failed."""
        return self.delivery_status == "failed"

    @property
    def can_retry(self) -> bool:
        """Check if event can be retried."""
        return self.retry_count < self.max_retries and not self.is_delivered

    def mark_sent(self):
        """Mark event as sent."""
        self.delivery_status = "sent"
        self.sent_at = datetime.now(timezone.utc)

    def mark_delivered(self):
        """Mark event as delivered."""
        self.delivery_status = "delivered"
        self.delivered_at = datetime.now(timezone.utc)

    def mark_acknowledged(self):
        """Mark event as acknowledged."""
        self.delivery_status = "acknowledged"
        self.acknowledged_at = datetime.now(timezone.utc)

    def mark_failed(self, error_message: str, error_code: str = None):
        """Mark event as failed."""
        self.delivery_status = "failed"
        self.error_message = error_message
        self.error_code = error_code

    def increment_retry(self, next_retry_delay_seconds: int = 60):
        """Increment retry count and set next retry time."""
        self.retry_count += 1
        self.next_retry_at = datetime.now(timezone.utc) + timedelta(seconds=next_retry_delay_seconds)

        if self.retry_count >= self.max_retries:
            self.mark_failed("Maximum retry attempts exceeded")


class WebSocketConnectionMetrics(BaseModelWithAudit):
    """
    WebSocket connection performance metrics model.

    Tracks aggregated performance metrics for WebSocket connections
    to enable monitoring, alerting, and optimization.
    """

    __tablename__ = "websocket_connection_metrics"

    # Time period
    metric_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Date for this metric period"
    )

    metric_hour = Column(
        Integer,
        nullable=False,
        index=True,
        doc="Hour of the day (0-23)"
    )

    # Connection metrics
    total_connections = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total connections established"
    )

    active_connections = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Peak active connections"
    )

    authenticated_connections = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Successfully authenticated connections"
    )

    failed_connections = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Failed connection attempts"
    )

    # Performance metrics
    avg_connection_duration_seconds = Column(
        Float,
        nullable=True,
        doc="Average connection duration in seconds"
    )

    avg_message_delivery_time_ms = Column(
        Float,
        nullable=True,
        doc="Average message delivery time in milliseconds"
    )

    total_messages_sent = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total messages sent"
    )

    total_bytes_transferred = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total bytes transferred"
    )

    # Error metrics
    total_errors = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total errors encountered"
    )

    error_rate_percent = Column(
        Float,
        nullable=True,
        doc="Error rate as percentage"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "metric_hour >= 0 AND metric_hour <= 23",
            name="ck_websocket_metrics_hour"
        ),
        CheckConstraint(
            "total_connections >= 0",
            name="ck_websocket_metrics_total_connections"
        ),
        CheckConstraint(
            "active_connections >= 0",
            name="ck_websocket_metrics_active_connections"
        ),
        CheckConstraint(
            "error_rate_percent >= 0 AND error_rate_percent <= 100",
            name="ck_websocket_metrics_error_rate"
        ),
        # Unique constraint for time periods
        Index("idx_websocket_metrics_unique", "metric_date", "metric_hour", unique=True),
        Index("idx_websocket_metrics_performance", "metric_date", "avg_message_delivery_time_ms"),
    )

    def __repr__(self) -> str:
        """String representation of the WebSocket metrics."""
        return f"<WebSocketConnectionMetrics(date={self.metric_date.date()}, hour={self.metric_hour}, connections={self.total_connections})>"


class WebSocketRoom(BaseModelWithAudit):
    """
    WebSocket room model for real-time conversation management.

    Manages real-time conversation rooms for booking communications,
    group chats, and broadcast channels with participant tracking
    and message routing capabilities.
    """

    __tablename__ = "websocket_rooms"

    # Room identification
    room_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique room identifier"
    )

    room_name = Column(
        String(255),
        nullable=False,
        doc="Human-readable room name"
    )

    room_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Room type (booking, group, broadcast, system)"
    )

    # Room configuration
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether room is active"
    )

    is_private = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether room is private"
    )

    max_participants = Column(
        Integer,
        default=100,
        nullable=False,
        doc="Maximum number of participants"
    )

    # Associated entities
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated booking ID for booking rooms"
    )

    owner_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Room owner/creator"
    )

    # Room lifecycle
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Room creation timestamp"
    )

    closed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Room closure timestamp"
    )

    last_activity_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Last activity in room"
    )

    # Room statistics
    participant_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Current number of participants"
    )

    total_messages = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total messages sent in room"
    )

    # Room metadata
    room_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional room configuration and metadata"
    )

    # Relationships
    owner = relationship(
        "User",
        foreign_keys=[owner_id],
        doc="Room owner"
    )

    participants = relationship(
        "WebSocketRoomParticipant",
        back_populates="room",
        cascade="all, delete-orphan",
        doc="Room participants"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "room_type IN ('booking', 'group', 'broadcast', 'system')",
            name="ck_websocket_room_type"
        ),
        CheckConstraint(
            "max_participants > 0",
            name="ck_websocket_room_max_participants"
        ),
        CheckConstraint(
            "participant_count >= 0",
            name="ck_websocket_room_participant_count"
        ),
        CheckConstraint(
            "total_messages >= 0",
            name="ck_websocket_room_total_messages"
        ),
        # Performance indexes
        Index("idx_websocket_room_type_active", "room_type", "is_active"),
        Index("idx_websocket_room_booking", "booking_id", "is_active"),
        Index("idx_websocket_room_owner", "owner_id", "created_at"),
        Index("idx_websocket_room_activity", "last_activity_at", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of the WebSocket room."""
        return f"<WebSocketRoom(id={self.id}, room_id='{self.room_id}', type='{self.room_type}', participants={self.participant_count})>"

    @property
    def is_full(self) -> bool:
        """Check if room is at capacity."""
        return self.participant_count >= self.max_participants

    @property
    def is_open(self) -> bool:
        """Check if room is open for new participants."""
        return self.is_active and not self.is_full and self.closed_at is None

    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity_at = datetime.now(timezone.utc)

    def increment_message_count(self):
        """Increment total message count."""
        self.total_messages += 1
        self.update_activity()


class WebSocketRoomParticipant(BaseModelWithAudit):
    """
    WebSocket room participant model for tracking room membership.

    Tracks user participation in WebSocket rooms with join/leave
    timestamps, permissions, and activity monitoring.
    """

    __tablename__ = "websocket_room_participants"

    # Participant identification
    room_id = Column(
        Integer,
        ForeignKey("websocket_rooms.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Room ID"
    )

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Participant user ID"
    )

    connection_id = Column(
        Integer,
        ForeignKey("websocket_connections.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Active WebSocket connection ID"
    )

    # Participant status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether participant is active in room"
    )

    role = Column(
        String(50),
        default="participant",
        nullable=False,
        doc="Participant role (owner, moderator, participant)"
    )

    # Participation lifecycle
    joined_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Room join timestamp"
    )

    left_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Room leave timestamp"
    )

    last_seen_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Last activity timestamp"
    )

    # Participant metadata
    participant_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional participant metadata"
    )

    # Relationships
    room = relationship(
        "WebSocketRoom",
        back_populates="participants",
        doc="Associated room"
    )

    user = relationship(
        "User",
        foreign_keys=[user_id],
        doc="Participant user"
    )

    connection = relationship(
        "WebSocketConnection",
        doc="Active WebSocket connection"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "role IN ('owner', 'moderator', 'participant')",
            name="ck_websocket_room_participant_role"
        ),
        # Unique constraint for active participants
        Index("idx_websocket_room_participant_unique", "room_id", "user_id", unique=True),
        Index("idx_websocket_room_participant_active", "room_id", "is_active"),
        Index("idx_websocket_room_participant_user", "user_id", "is_active"),
        Index("idx_websocket_room_participant_connection", "connection_id", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of the room participant."""
        return f"<WebSocketRoomParticipant(room_id={self.room_id}, user_id={self.user_id}, role='{self.role}', active={self.is_active})>"

    def update_activity(self):
        """Update last seen timestamp."""
        self.last_seen_at = datetime.now(timezone.utc)

    def leave_room(self):
        """Mark participant as having left the room."""
        self.is_active = False
        self.left_at = datetime.now(timezone.utc)


class UserPresence(BaseModelWithAudit):
    """
    User presence tracking model.

    Tracks user online/offline status and activity for real-time
    presence indicators and user experience optimization.
    """

    __tablename__ = "user_presence"

    # User association
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User ID"
    )

    # Presence status
    status = Column(
        String(20),
        nullable=False,
        default="offline",
        index=True,
        doc="User presence status (online, away, offline)"
    )

    # Activity tracking
    last_seen_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Last activity timestamp"
    )

    last_activity_type = Column(
        String(50),
        nullable=True,
        doc="Type of last activity"
    )

    # Connection tracking
    active_connections_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of active WebSocket connections"
    )

    # Device information
    primary_device_type = Column(
        String(50),
        nullable=True,
        doc="Primary device type (web, mobile, pwa)"
    )

    # Presence metadata
    status_message = Column(
        String(200),
        nullable=True,
        doc="Custom status message"
    )

    auto_away_enabled = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether to automatically set away status"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="presence",
        foreign_keys=[user_id],
        doc="Associated user"
    )

    # Database constraints
    __table_args__ = (
        CheckConstraint(
            "status IN ('online', 'away', 'offline')",
            name="ck_user_presence_status"
        ),
        CheckConstraint(
            "active_connections_count >= 0",
            name="ck_user_presence_connections"
        ),
        # Unique constraint per user
        Index("idx_user_presence_unique", "user_id", unique=True),
        Index("idx_user_presence_status", "status", "last_seen_at"),
    )

    def __repr__(self) -> str:
        """String representation of user presence."""
        return f"<UserPresence(user_id={self.user_id}, status='{self.status}', last_seen={self.last_seen_at})>"

    @property
    def is_online(self) -> bool:
        """Check if user is currently online."""
        return self.status == "online" and self.active_connections_count > 0

    @property
    def is_away(self) -> bool:
        """Check if user is away."""
        return self.status == "away"

    @property
    def is_offline(self) -> bool:
        """Check if user is offline."""
        return self.status == "offline" or self.active_connections_count == 0

    def update_activity(self, activity_type: str = None):
        """Update user activity timestamp."""
        self.last_seen_at = datetime.now(timezone.utc)
        if activity_type:
            self.last_activity_type = activity_type

    def set_online(self):
        """Set user status to online."""
        self.status = "online"
        self.update_activity("status_change")

    def set_away(self):
        """Set user status to away."""
        self.status = "away"
        self.update_activity("status_change")

    def set_offline(self):
        """Set user status to offline."""
        self.status = "offline"
        self.active_connections_count = 0
        self.update_activity("status_change")

    def increment_connections(self):
        """Increment active connections count."""
        self.active_connections_count += 1
        if self.active_connections_count > 0:
            self.set_online()

    def decrement_connections(self):
        """Decrement active connections count."""
        self.active_connections_count = max(0, self.active_connections_count - 1)
        if self.active_connections_count == 0:
            self.set_offline()
