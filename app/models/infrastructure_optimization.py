"""
Infrastructure Optimization models for Culture Connect Backend API.

This module defines comprehensive infrastructure optimization models including:
- CacheConfiguration: Redis caching configuration and TTL management
- BackgroundTask: Celery task definitions for heavy analytical workloads
- PerformanceMonitoring: Query performance tracking and optimization metrics
- CacheMetrics: Cache hit/miss rates and performance analytics
- TaskMetrics: Background task execution and queue management metrics

Implements Task 3.2.3 requirements for infrastructure optimization with
production-grade PostgreSQL optimization and seamless integration with
existing marketplace optimization services from Task 3.2.2.
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class CacheType(str, Enum):
    """Cache type enumeration for different optimization data."""
    OPTIMIZATION_SCORE = "optimization_score"
    MARKET_INTELLIGENCE = "market_intelligence"
    VENDOR_SUMMARY = "vendor_summary"
    PERFORMANCE_ANALYTICS = "performance_analytics"
    COMPETITIVE_ANALYSIS = "competitive_analysis"
    SEO_ANALYSIS = "seo_analysis"
    MOBILE_OPTIMIZATION = "mobile_optimization"


class TaskStatus(str, Enum):
    """Background task status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILURE = "failure"
    RETRY = "retry"
    REVOKED = "revoked"


class TaskPriority(str, Enum):
    """Background task priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class CacheConfiguration(Base):
    """
    Cache configuration model for optimization data management.
    
    Tracks cache policies, TTL settings, and invalidation rules
    for different types of optimization data.
    """
    __tablename__ = "cache_configurations"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    cache_type = Column(String(50), nullable=False, index=True)
    cache_key_pattern = Column(String(255), nullable=False)
    ttl_seconds = Column(Integer, nullable=False)
    max_size_mb = Column(Integer, default=100)
    compression_enabled = Column(Boolean, default=True)
    invalidation_rules = Column(JSONB, default=dict)
    
    # Metadata
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    cache_metrics = relationship("CacheMetrics", back_populates="configuration")
    
    # Indexes
    __table_args__ = (
        Index("idx_cache_config_type_active", "cache_type", "is_active"),
        Index("idx_cache_config_created", "created_at"),
        UniqueConstraint("cache_type", "cache_key_pattern", name="uq_cache_config_type_pattern"),
        CheckConstraint("ttl_seconds > 0", name="ck_cache_config_ttl_positive"),
        CheckConstraint("max_size_mb > 0", name="ck_cache_config_size_positive"),
    )


class BackgroundTask(Base):
    """
    Background task model for heavy analytical workloads.
    
    Tracks Celery task execution, queue management, and
    performance metrics for optimization analytics.
    """
    __tablename__ = "background_tasks"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_id = Column(String(255), unique=True, nullable=False, index=True)
    task_name = Column(String(255), nullable=False, index=True)
    task_type = Column(String(100), nullable=False, index=True)
    priority = Column(String(20), nullable=False, default=TaskPriority.NORMAL)
    status = Column(String(20), nullable=False, default=TaskStatus.PENDING, index=True)
    
    # Task execution details
    queue_name = Column(String(100), nullable=False, default="default")
    worker_name = Column(String(255))
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # Timing information
    queued_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    execution_time_seconds = Column(Float)
    
    # Task data
    task_args = Column(JSONB, default=dict)
    task_kwargs = Column(JSONB, default=dict)
    result_data = Column(JSONB)
    error_message = Column(Text)
    error_traceback = Column(Text)
    
    # Metadata
    created_by = Column(Integer, ForeignKey("users.id"))
    correlation_id = Column(String(255), index=True)
    
    # Relationships
    task_metrics = relationship("TaskMetrics", back_populates="task")
    
    # Indexes
    __table_args__ = (
        Index("idx_background_task_status_priority", "status", "priority"),
        Index("idx_background_task_type_status", "task_type", "status"),
        Index("idx_background_task_queued", "queued_at"),
        Index("idx_background_task_correlation", "correlation_id"),
        CheckConstraint("retry_count >= 0", name="ck_background_task_retry_positive"),
        CheckConstraint("max_retries >= 0", name="ck_background_task_max_retry_positive"),
        CheckConstraint("execution_time_seconds >= 0", name="ck_background_task_exec_time_positive"),
    )


class PerformanceMonitoring(Base):
    """
    Performance monitoring model for optimization query tracking.
    
    Tracks database query performance, execution times, and
    optimization recommendations for marketplace analytics.
    """
    __tablename__ = "performance_monitoring"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    query_type = Column(String(100), nullable=False, index=True)
    query_hash = Column(String(64), nullable=False, index=True)
    table_names = Column(JSONB, default=list)
    
    # Performance metrics
    execution_time_ms = Column(Float, nullable=False)
    rows_examined = Column(Integer, default=0)
    rows_returned = Column(Integer, default=0)
    memory_usage_mb = Column(Float)
    cpu_usage_percent = Column(Float)
    
    # Query optimization data
    query_plan = Column(JSONB)
    index_usage = Column(JSONB, default=dict)
    optimization_suggestions = Column(JSONB, default=list)
    is_slow_query = Column(Boolean, default=False, index=True)
    
    # Context information
    endpoint_path = Column(String(255))
    user_id = Column(Integer, ForeignKey("users.id"))
    correlation_id = Column(String(255), index=True)
    
    # Metadata
    executed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index("idx_perf_monitoring_type_time", "query_type", "execution_time_ms"),
        Index("idx_perf_monitoring_slow", "is_slow_query", "executed_at"),
        Index("idx_perf_monitoring_executed", "executed_at"),
        Index("idx_perf_monitoring_correlation", "correlation_id"),
        CheckConstraint("execution_time_ms >= 0", name="ck_perf_monitoring_exec_time_positive"),
        CheckConstraint("rows_examined >= 0", name="ck_perf_monitoring_rows_examined_positive"),
        CheckConstraint("rows_returned >= 0", name="ck_perf_monitoring_rows_returned_positive"),
    )


class CacheMetrics(Base):
    """
    Cache metrics model for Redis performance tracking.
    
    Tracks cache hit/miss rates, memory usage, and
    performance analytics for optimization data caching.
    """
    __tablename__ = "cache_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    configuration_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("cache_configurations.id"), nullable=False)
    
    # Cache performance metrics
    cache_hits = Column(Integer, default=0)
    cache_misses = Column(Integer, default=0)
    cache_hit_rate = Column(Float, default=0.0)
    
    # Memory and storage metrics
    memory_usage_mb = Column(Float, default=0.0)
    key_count = Column(Integer, default=0)
    expired_keys = Column(Integer, default=0)
    evicted_keys = Column(Integer, default=0)
    
    # Performance metrics
    avg_response_time_ms = Column(Float, default=0.0)
    max_response_time_ms = Column(Float, default=0.0)
    operations_per_second = Column(Float, default=0.0)
    
    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Metadata
    recorded_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    configuration = relationship("CacheConfiguration", back_populates="cache_metrics")
    
    # Indexes
    __table_args__ = (
        Index("idx_cache_metrics_config_period", "configuration_id", "period_start", "period_end"),
        Index("idx_cache_metrics_hit_rate", "cache_hit_rate"),
        Index("idx_cache_metrics_recorded", "recorded_at"),
        CheckConstraint("cache_hits >= 0", name="ck_cache_metrics_hits_positive"),
        CheckConstraint("cache_misses >= 0", name="ck_cache_metrics_misses_positive"),
        CheckConstraint("cache_hit_rate >= 0 AND cache_hit_rate <= 1", name="ck_cache_metrics_hit_rate_valid"),
        CheckConstraint("period_end > period_start", name="ck_cache_metrics_period_valid"),
    )


class TaskMetrics(Base):
    """
    Task metrics model for background task performance tracking.
    
    Tracks task execution performance, queue metrics, and
    resource utilization for optimization analytics.
    """
    __tablename__ = "task_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("background_tasks.id"), nullable=False)
    
    # Performance metrics
    queue_wait_time_seconds = Column(Float, default=0.0)
    execution_time_seconds = Column(Float, default=0.0)
    memory_peak_mb = Column(Float, default=0.0)
    cpu_usage_percent = Column(Float, default=0.0)
    
    # Resource utilization
    database_queries_count = Column(Integer, default=0)
    cache_operations_count = Column(Integer, default=0)
    external_api_calls = Column(Integer, default=0)
    
    # Task efficiency metrics
    throughput_items_per_second = Column(Float, default=0.0)
    error_rate_percent = Column(Float, default=0.0)
    success_rate_percent = Column(Float, default=100.0)
    
    # Metadata
    measured_at = Column(DateTime(timezone=True), server_default=func.now())
    worker_node = Column(String(255))
    
    # Relationships
    task = relationship("BackgroundTask", back_populates="task_metrics")
    
    # Indexes
    __table_args__ = (
        Index("idx_task_metrics_task_measured", "task_id", "measured_at"),
        Index("idx_task_metrics_execution_time", "execution_time_seconds"),
        Index("idx_task_metrics_success_rate", "success_rate_percent"),
        CheckConstraint("queue_wait_time_seconds >= 0", name="ck_task_metrics_wait_time_positive"),
        CheckConstraint("execution_time_seconds >= 0", name="ck_task_metrics_exec_time_positive"),
        CheckConstraint("error_rate_percent >= 0 AND error_rate_percent <= 100", name="ck_task_metrics_error_rate_valid"),
        CheckConstraint("success_rate_percent >= 0 AND success_rate_percent <= 100", name="ck_task_metrics_success_rate_valid"),
    )
