# Culture Connect Backend - Hetzner Cloud Infrastructure
# Cost-effective self-hosting solution with k3s for startups

terraform {
  required_version = ">= 1.0"
  required_providers {
    hcloud = {
      source  = "hetznercloud/hcloud"
      version = "~> 1.42"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.4"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }

  backend "s3" {
    bucket         = "culture-connect-terraform-state-hetzner"
    key            = "hetzner/terraform.tfstate"
    region         = "eu-central-1"
    encrypt        = true
    endpoint       = "https://s3.eu-central-1.amazonaws.com"
  }
}

# Configure Hetzner Cloud Provider
provider "hcloud" {
  token = var.hcloud_token
}

# Local values
locals {
  cluster_name = "${var.project_name}-${var.environment}"
  common_labels = {
    project     = var.project_name
    environment = var.environment
    managed_by  = "terraform"
  }
}

# SSH Key for server access
resource "tls_private_key" "ssh_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "hcloud_ssh_key" "default" {
  name       = "${local.cluster_name}-ssh-key"
  public_key = tls_private_key.ssh_key.public_key_openssh
  labels     = local.common_labels
}

# Network for private communication
resource "hcloud_network" "main" {
  name     = "${local.cluster_name}-network"
  ip_range = var.network_cidr
  labels   = local.common_labels
}

resource "hcloud_network_subnet" "main" {
  type         = "cloud"
  network_id   = hcloud_network.main.id
  network_zone = var.network_zone
  ip_range     = var.subnet_cidr
}

# Load Balancer for high availability
resource "hcloud_load_balancer" "main" {
  name               = "${local.cluster_name}-lb"
  load_balancer_type = var.load_balancer_type
  location           = var.location
  labels             = local.common_labels

  algorithm {
    type = "round_robin"
  }
}

resource "hcloud_load_balancer_network" "main" {
  load_balancer_id = hcloud_load_balancer.main.id
  network_id       = hcloud_network.main.id
  ip               = var.load_balancer_private_ip
}

# Load Balancer Target for API servers
resource "hcloud_load_balancer_target" "api_servers" {
  count            = var.master_count
  type             = "server"
  load_balancer_id = hcloud_load_balancer.main.id
  server_id        = hcloud_server.masters[count.index].id
  use_private_ip   = true
}

# Load Balancer Service for HTTPS
resource "hcloud_load_balancer_service" "https" {
  load_balancer_id = hcloud_load_balancer.main.id
  protocol         = "https"
  listen_port      = 443
  destination_port = 443

  health_check {
    protocol = "https"
    port     = 443
    interval = 15
    timeout  = 10
    retries  = 3
    http {
      path         = "/health"
      status_codes = ["200"]
      tls          = true
    }
  }

  http {
    redirect_http = true
    sticky_sessions = false
  }
}

# Load Balancer Service for HTTP (redirect to HTTPS)
resource "hcloud_load_balancer_service" "http" {
  load_balancer_id = hcloud_load_balancer.main.id
  protocol         = "http"
  listen_port      = 80
  destination_port = 80

  health_check {
    protocol = "http"
    port     = 80
    interval = 15
    timeout  = 10
    retries  = 3
    http {
      path         = "/health"
      status_codes = ["200", "301", "302"]
    }
  }

  http {
    redirect_http = true
  }
}

# Master nodes (control plane)
resource "hcloud_server" "masters" {
  count       = var.master_count
  name        = "${local.cluster_name}-master-${count.index + 1}"
  image       = var.server_image
  server_type = var.master_server_type
  location    = var.location
  ssh_keys    = [hcloud_ssh_key.default.id]
  labels      = merge(local.common_labels, {
    role = "master"
    node = "master-${count.index + 1}"
  })

  user_data = templatefile("${path.module}/cloud-init-master.yaml", {
    node_name    = "${local.cluster_name}-master-${count.index + 1}"
    cluster_name = local.cluster_name
    is_first_master = count.index == 0
    master_ip    = "10.0.1.${10 + count.index}"
    lb_ip        = var.load_balancer_private_ip
    k3s_token    = random_password.k3s_token.result
    k3s_version  = var.k3s_version
  })

  public_net {
    ipv4_enabled = true
    ipv6_enabled = false
  }

  depends_on = [hcloud_network_subnet.main]
}

# Attach masters to private network
resource "hcloud_server_network" "masters" {
  count     = var.master_count
  server_id = hcloud_server.masters[count.index].id
  network_id = hcloud_network.main.id
  ip        = "10.0.1.${10 + count.index}"
}

# Worker nodes
resource "hcloud_server" "workers" {
  count       = var.worker_count
  name        = "${local.cluster_name}-worker-${count.index + 1}"
  image       = var.server_image
  server_type = var.worker_server_type
  location    = var.location
  ssh_keys    = [hcloud_ssh_key.default.id]
  labels      = merge(local.common_labels, {
    role = "worker"
    node = "worker-${count.index + 1}"
  })

  user_data = templatefile("${path.module}/cloud-init-worker.yaml", {
    node_name    = "${local.cluster_name}-worker-${count.index + 1}"
    cluster_name = local.cluster_name
    master_ip    = hcloud_server.masters[0].network[0].ip
    k3s_token    = random_password.k3s_token.result
    k3s_version  = var.k3s_version
  })

  public_net {
    ipv4_enabled = true
    ipv6_enabled = false
  }

  depends_on = [hcloud_server.masters]
}

# Attach workers to private network
resource "hcloud_server_network" "workers" {
  count     = var.worker_count
  server_id = hcloud_server.workers[count.index].id
  network_id = hcloud_network.main.id
  ip        = "10.0.1.${20 + count.index}"
}

# Database server (PostgreSQL)
resource "hcloud_server" "database" {
  name        = "${local.cluster_name}-database"
  image       = var.server_image
  server_type = var.database_server_type
  location    = var.location
  ssh_keys    = [hcloud_ssh_key.default.id]
  labels      = merge(local.common_labels, {
    role = "database"
  })

  user_data = templatefile("${path.module}/cloud-init-database.yaml", {
    postgres_password = var.postgres_password
    postgres_user     = var.postgres_user
    postgres_db       = var.postgres_db
  })

  public_net {
    ipv4_enabled = false
    ipv6_enabled = false
  }
}

# Attach database to private network
resource "hcloud_server_network" "database" {
  server_id  = hcloud_server.database.id
  network_id = hcloud_network.main.id
  ip         = "**********"
}

# Redis server
resource "hcloud_server" "redis" {
  name        = "${local.cluster_name}-redis"
  image       = var.server_image
  server_type = var.redis_server_type
  location    = var.location
  ssh_keys    = [hcloud_ssh_key.default.id]
  labels      = merge(local.common_labels, {
    role = "redis"
  })

  user_data = templatefile("${path.module}/cloud-init-redis.yaml", {
    redis_password = var.redis_password
  })

  public_net {
    ipv4_enabled = false
    ipv6_enabled = false
  }
}

# Attach Redis to private network
resource "hcloud_server_network" "redis" {
  server_id  = hcloud_server.redis.id
  network_id = hcloud_network.main.id
  ip         = "**********"
}

# Volumes for persistent storage
resource "hcloud_volume" "database_storage" {
  name     = "${local.cluster_name}-database-storage"
  size     = var.database_volume_size
  location = var.location
  labels   = local.common_labels
}

resource "hcloud_volume_attachment" "database_storage" {
  volume_id = hcloud_volume.database_storage.id
  server_id = hcloud_server.database.id
  automount = true
}

resource "hcloud_volume" "redis_storage" {
  name     = "${local.cluster_name}-redis-storage"
  size     = var.redis_volume_size
  location = var.location
  labels   = local.common_labels
}

resource "hcloud_volume_attachment" "redis_storage" {
  volume_id = hcloud_volume.redis_storage.id
  server_id = hcloud_server.redis.id
  automount = true
}

# Firewall rules
resource "hcloud_firewall" "main" {
  name   = "${local.cluster_name}-firewall"
  labels = local.common_labels

  # SSH access
  rule {
    direction = "in"
    port      = "22"
    protocol  = "tcp"
    source_ips = var.allowed_ssh_ips
  }

  # HTTP/HTTPS access
  rule {
    direction = "in"
    port      = "80"
    protocol  = "tcp"
    source_ips = ["0.0.0.0/0", "::/0"]
  }

  rule {
    direction = "in"
    port      = "443"
    protocol  = "tcp"
    source_ips = ["0.0.0.0/0", "::/0"]
  }

  # Kubernetes API
  rule {
    direction = "in"
    port      = "6443"
    protocol  = "tcp"
    source_ips = var.allowed_k8s_api_ips
  }

  # Internal cluster communication
  rule {
    direction = "in"
    port      = "any"
    protocol  = "tcp"
    source_ips = [var.network_cidr]
  }

  rule {
    direction = "in"
    port      = "any"
    protocol  = "udp"
    source_ips = [var.network_cidr]
  }
}

# Apply firewall to all servers
resource "hcloud_firewall_attachment" "masters" {
  count       = var.master_count
  firewall_id = hcloud_firewall.main.id
  server_ids  = [hcloud_server.masters[count.index].id]
}

resource "hcloud_firewall_attachment" "workers" {
  count       = var.worker_count
  firewall_id = hcloud_firewall.main.id
  server_ids  = [hcloud_server.workers[count.index].id]
}

resource "hcloud_firewall_attachment" "database" {
  firewall_id = hcloud_firewall.main.id
  server_ids  = [hcloud_server.database.id]
}

resource "hcloud_firewall_attachment" "redis" {
  firewall_id = hcloud_firewall.main.id
  server_ids  = [hcloud_server.redis.id]
}

# Random password for k3s cluster token
resource "random_password" "k3s_token" {
  length  = 32
  special = true
}

# Floating IP for high availability
resource "hcloud_floating_ip" "main" {
  type          = "ipv4"
  home_location = var.location
  name          = "${local.cluster_name}-floating-ip"
  labels        = local.common_labels
}

resource "hcloud_floating_ip_assignment" "main" {
  floating_ip_id = hcloud_floating_ip.main.id
  server_id      = hcloud_server.masters[0].id
}
