"""
Test fixtures and utilities for geolocation testing.

This module provides comprehensive test fixtures for geolocation functionality including:
- Mock MaxMind database responses
- Test IP addresses with known countries
- Geolocation service test utilities
- Performance testing helpers
- Circuit breaker test scenarios

Follows Culture Connect Backend testing standards with >80% coverage requirements.
"""

import pytest
import asyncio
import time
import tempfile
import shutil
from typing import Dict, List, Any, Optional, AsyncGenerator
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
import json

from app.services.geolocation_service import GeolocationService, GeolocationResult
from app.core.circuit_breaker import GeolocationCircuitBreaker, CircuitBreakerConfig
from app.core.geolocation_resilience import GeolocationResilienceManager, ResilienceConfig


class GeolocationTestData:
    """Test data for geolocation testing."""

    @property
    def test_ips(self):
        """Get test IP addresses."""
        return list(self.TEST_IPS.keys())

    # Test IP addresses with known countries
    TEST_IPS = {
        "*******": {
            "country_code": "US",
            "country_name": "United States",
            "continent_code": "NA",
            "is_vpn": False,
            "confidence": 0.95
        },
        "*******": {
            "country_code": "US",
            "country_name": "United States",
            "continent_code": "NA",
            "is_vpn": False,
            "confidence": 0.95
        },
        "************": {
            "country_code": "NG",
            "country_name": "Nigeria",
            "continent_code": "AF",
            "is_vpn": False,
            "confidence": 0.90
        },
        "***********": {
            "country_code": "ZA",
            "country_name": "South Africa",
            "continent_code": "AF",
            "is_vpn": False,
            "confidence": 0.88
        },
        "************": {
            "country_code": "EG",
            "country_name": "Egypt",
            "continent_code": "AF",
            "is_vpn": False,
            "confidence": 0.85
        },
        "***********": {
            "country_code": None,
            "country_name": None,
            "continent_code": None,
            "is_vpn": False,
            "confidence": 0.0,
            "error": "Private IP address"
        },
        "********": {
            "country_code": None,
            "country_name": None,
            "continent_code": None,
            "is_vpn": False,
            "confidence": 0.0,
            "error": "Private IP address"
        }
    }

    # Payment provider routing test scenarios
    PAYMENT_ROUTING_SCENARIOS = [
        {
            "country": "NG",
            "currency": "NGN",
            "expected_provider": "paystack",
            "amount": 10000.0,
            "description": "Nigerian Naira payment"
        },
        {
            "country": "US",
            "currency": "USD",
            "expected_provider": "stripe",
            "amount": 100.0,
            "description": "US Dollar payment"
        },
        {
            "country": "GB",
            "currency": "GBP",
            "expected_provider": "stripe",
            "amount": 75.0,
            "description": "British Pound payment"
        },
        {
            "country": "CA",
            "currency": "CAD",
            "expected_provider": "stripe",
            "amount": 125.0,
            "description": "Canadian Dollar payment"
        },
        {
            "country": "NG",
            "currency": "BTC",
            "expected_provider": "busha",
            "amount": 0.001,
            "description": "Bitcoin payment"
        }
    ]

    # Performance test scenarios
    PERFORMANCE_SCENARIOS = {
        "single_detection": {
            "ip_count": 1,
            "max_time_ms": 100,
            "description": "Single IP detection"
        },
        "batch_detection": {
            "ip_count": 10,
            "max_time_ms": 500,
            "description": "Batch IP detection"
        },
        "high_load": {
            "ip_count": 100,
            "max_time_ms": 2000,
            "description": "High load detection"
        },
        "cache_performance": {
            "ip_count": 50,
            "cache_hit_rate": 0.9,
            "description": "Cache performance test"
        }
    }


@pytest.fixture
def geolocation_test_data():
    """Provide geolocation test data."""
    return GeolocationTestData()


@pytest.fixture
def test_ip_addresses():
    """Provide test IP addresses."""
    return list(GeolocationTestData.TEST_IPS.keys())


@pytest.fixture
def known_country_ips():
    """Provide IP addresses with known countries."""
    return {
        ip: data for ip, data in GeolocationTestData.TEST_IPS.items()
        if data["country_code"] is not None
    }


@pytest.fixture
def private_ip_addresses():
    """Provide private IP addresses for error testing."""
    return [
        "***********",
        "********",
        "**********",
        "127.0.0.1"
    ]


@pytest.fixture
def invalid_ip_addresses():
    """Provide invalid IP addresses for error testing."""
    return [
        "invalid.ip",
        "999.999.999.999",
        "not.an.ip.address",
        "",
        None
    ]


@pytest.fixture
async def mock_geolocation_service():
    """Create a mock geolocation service for testing."""
    service = Mock(spec=GeolocationService)

    async def mock_detect_country(ip_address: str) -> GeolocationResult:
        """Mock country detection."""
        test_data = GeolocationTestData.TEST_IPS.get(ip_address)

        if not test_data:
            raise ValueError(f"Unknown test IP: {ip_address}")

        if "error" in test_data:
            raise Exception(test_data["error"])

        return GeolocationResult(
            country_code=test_data["country_code"],
            country_name=test_data["country_name"],
            continent_code=test_data["continent_code"],
            ip_address=ip_address,
            detection_method="mock_test",
            confidence_score=test_data["confidence"],
            detected_at=time.time()
        )

    service.detect_country_from_ip = AsyncMock(side_effect=mock_detect_country)
    return service


@pytest.fixture
def test_circuit_breaker():
    """Create a test circuit breaker with fast settings."""
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=5,  # 5 seconds for fast testing
        success_threshold=2,
        timeout=2.0,
        name="test_geolocation"
    )
    return GeolocationCircuitBreaker.__new__(GeolocationCircuitBreaker)


@pytest.fixture
def test_resilience_manager():
    """Create a test resilience manager with fast settings."""
    config = ResilienceConfig(
        max_retries=2,
        base_delay=0.1,  # 100ms for fast testing
        max_delay=1.0,   # 1 second max
        fallback_country="NG",
        enable_circuit_breaker=True,
        cache_fallback_results=True
    )
    return GeolocationResilienceManager(config)


@pytest.fixture
async def temp_test_database():
    """Create a temporary test database file."""
    temp_dir = tempfile.mkdtemp()
    test_db_path = Path(temp_dir) / "test_geoip.mmdb"

    # Create a minimal test database file
    test_db_path.write_bytes(b"MOCK_GEOIP_DATABASE_FOR_TESTING")

    yield str(test_db_path)

    # Cleanup
    shutil.rmtree(temp_dir)


@pytest.fixture
def performance_test_config():
    """Configuration for performance testing."""
    return {
        "detection_timeout_ms": 100,
        "cache_hit_rate_threshold": 0.9,
        "concurrent_requests": 50,
        "test_duration_seconds": 30,
        "memory_limit_mb": 100,
        "cpu_limit_percent": 80
    }


class GeolocationTestHelper:
    """Helper class for geolocation testing."""

    @staticmethod
    async def measure_detection_time(geolocation_service, ip_address: str) -> float:
        """Measure geolocation detection time in milliseconds."""
        start_time = time.perf_counter()
        await geolocation_service.detect_country_from_ip(ip_address)
        end_time = time.perf_counter()
        return (end_time - start_time) * 1000

    @staticmethod
    async def test_cache_performance(geolocation_service, ip_addresses: List[str]) -> Dict[str, Any]:
        """Test cache performance with multiple IP addresses."""
        # First round - populate cache
        first_round_times = []
        for ip in ip_addresses:
            detection_time = await GeolocationTestHelper.measure_detection_time(
                geolocation_service, ip
            )
            first_round_times.append(detection_time)

        # Second round - test cache hits
        second_round_times = []
        for ip in ip_addresses:
            detection_time = await GeolocationTestHelper.measure_detection_time(
                geolocation_service, ip
            )
            second_round_times.append(detection_time)

        # Calculate cache performance
        avg_first_round = sum(first_round_times) / len(first_round_times)
        avg_second_round = sum(second_round_times) / len(second_round_times)
        cache_improvement = (avg_first_round - avg_second_round) / avg_first_round

        return {
            "first_round_avg_ms": avg_first_round,
            "second_round_avg_ms": avg_second_round,
            "cache_improvement_percent": cache_improvement * 100,
            "cache_hit_rate": cache_improvement if cache_improvement > 0 else 0
        }

    @staticmethod
    async def simulate_concurrent_requests(
        geolocation_service,
        ip_addresses: List[str],
        concurrent_count: int
    ) -> Dict[str, Any]:
        """Simulate concurrent geolocation requests."""
        start_time = time.perf_counter()

        # Create concurrent tasks
        tasks = []
        for i in range(concurrent_count):
            ip = ip_addresses[i % len(ip_addresses)]
            task = asyncio.create_task(
                geolocation_service.detect_country_from_ip(ip)
            )
            tasks.append(task)

        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        # Analyze results
        successful_requests = sum(1 for r in results if not isinstance(r, Exception))
        failed_requests = len(results) - successful_requests
        success_rate = successful_requests / len(results)
        avg_time_per_request = total_time / len(results)

        return {
            "total_requests": len(results),
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": success_rate,
            "total_time_seconds": total_time,
            "avg_time_per_request_ms": avg_time_per_request * 1000,
            "requests_per_second": len(results) / total_time
        }

    @staticmethod
    def create_test_geolocation_result(
        ip_address: str,
        country_code: str = "NG",
        confidence: float = 0.9
    ) -> GeolocationResult:
        """Create a test GeolocationResult."""
        country_names = {
            "NG": "Nigeria",
            "US": "United States",
            "GB": "United Kingdom",
            "ZA": "South Africa",
            "EG": "Egypt"
        }

        continent_codes = {
            "NG": "AF", "ZA": "AF", "EG": "AF",
            "US": "NA", "GB": "EU"
        }

        return GeolocationResult(
            country_code=country_code,
            country_name=country_names.get(country_code, "Unknown"),
            continent_code=continent_codes.get(country_code, "UN"),
            ip_address=ip_address,
            detection_method="test_fixture",
            confidence_score=confidence,
            detected_at=time.time()
        )


@pytest.fixture
def geolocation_test_helper():
    """Provide geolocation test helper."""
    return GeolocationTestHelper()


@pytest.fixture
async def redis_test_client():
    """Create a test Redis client for caching tests."""
    try:
        import redis.asyncio as redis

        # Use Redis database 15 for testing (separate from production)
        client = redis.Redis(
            host="localhost",
            port=6379,
            db=15,
            decode_responses=True
        )

        # Clear test database
        await client.flushdb()

        yield client

        # Cleanup
        await client.flushdb()
        await client.close()

    except ImportError:
        # Mock Redis client if redis is not available
        mock_client = AsyncMock()
        yield mock_client


@pytest.fixture
def mock_payment_provider_manager():
    """Create a mock payment provider manager for routing tests."""
    from app.core.payment.providers import PaymentProvider
    from app.core.payment.routing import RoutingDecision

    manager = Mock()

    async def mock_select_provider(country_code: str, currency: str, amount: float):
        """Mock provider selection based on country."""
        # Simple routing logic for testing
        if country_code in ["NG", "GH", "KE"] and currency == "NGN":
            provider = PaymentProvider.PAYSTACK
            reason = "African country with NGN currency"
        elif currency in ["BTC", "ETH", "USDT"]:
            provider = PaymentProvider.BUSHA
            reason = "Cryptocurrency payment"
        else:
            provider = PaymentProvider.STRIPE
            reason = "International payment"

        routing_decision = RoutingDecision(
            selected_provider=provider,
            routing_reason=reason,
            confidence_score=0.9,
            fallback_providers=[],
            routing_metadata={}
        )

        return provider, routing_decision

    manager.select_provider = AsyncMock(side_effect=mock_select_provider)
    return manager


# Test configuration constants
TEST_CONFIG = {
    "PERFORMANCE_THRESHOLDS": {
        "max_detection_time_ms": 100,
        "min_cache_hit_rate": 0.9,
        "max_memory_usage_mb": 100,
        "max_cpu_usage_percent": 80
    },
    "LOAD_TEST_SETTINGS": {
        "concurrent_requests": 50,
        "test_duration_seconds": 30,
        "ramp_up_seconds": 5
    },
    "CIRCUIT_BREAKER_TEST": {
        "failure_threshold": 3,
        "recovery_timeout_seconds": 5,
        "test_timeout_seconds": 2
    }
}
