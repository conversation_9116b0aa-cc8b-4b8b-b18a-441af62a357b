"""
Unit tests for WebSocket services.

This module provides comprehensive unit tests for WebSocket service layer including:
- WebSocketConnectionService unit tests with mocking
- WebSocketEventService unit tests with content moderation
- UserPresenceService unit tests with real-time status management
- WebSocketMetricsService unit tests with analytics validation

Implements Task 6.1.1 Phase 5 requirements with >85% code coverage and performance validation.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from decimal import Decimal

from app.services.websocket_services import (
    WebSocketConnectionService, WebSocketEventService, 
    UserPresenceService, WebSocketMetricsService
)
from app.schemas.websocket_schemas import (
    WebSocketConnectionCreate, WebSocketEventCreate, UserPresenceCreate,
    EventTypeEnum, EventPriorityEnum, PresenceStatusEnum
)
from app.models.websocket_models import ConnectionStatus, EventType, EventPriority
from app.services.base import ValidationError, NotFoundError, ConflictError


@pytest.mark.unit
class TestWebSocketConnectionService:
    """Unit tests for WebSocket connection service."""
    
    async def test_create_connection_success(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_connection_factory
    ):
        """Test successful WebSocket connection creation."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_data = WebSocketConnectionCreate(
            connection_id="test_conn_123",
            user_id=mock_websocket_user.id,
            client_type="web",
            ip_address="127.0.0.1"
        )
        
        # Mock repository methods
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.connection_id = "test_conn_123"
        mock_connection.user_id = mock_websocket_user.id
        mock_connection.status = ConnectionStatus.CONNECTED
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_connection = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=None)
        
        with patch.object(service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        with patch.object(service, '_send_connection_notifications') as mock_notify:
            mock_notify.return_value = None
        
        # Act
        result = await service.create_connection(connection_data, mock_websocket_user)
        
        # Assert
        assert result is not None
        assert result.connection_id == "test_conn_123"
    
    async def test_create_connection_duplicate_error(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_connection_factory
    ):
        """Test connection creation with duplicate connection ID."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_data = WebSocketConnectionCreate(
            connection_id="existing_conn",
            user_id=mock_websocket_user.id,
            client_type="web"
        )
        
        # Mock existing connection
        existing_connection = MagicMock()
        existing_connection.status = ConnectionStatus.CONNECTED
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=existing_connection)
        
        with patch.object(service, 'user_repository') as mock_user_repo:
            mock_user_repo.get = AsyncMock(return_value=mock_websocket_user)
        
        # Act & Assert
        with pytest.raises(ConflictError):
            await service.create_connection(connection_data, mock_websocket_user)
    
    async def test_authenticate_connection_success(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful WebSocket connection authentication with performance validation."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_id = "test_conn_123"
        auth_token = "valid_jwt_token"
        
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.connection_id = connection_id
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.update_connection_status = AsyncMock(return_value=True)
        
        with patch.object(service, '_send_connection_notifications') as mock_notify:
            mock_notify.return_value = None
        
        # Act
        performance_timer.start()
        result = await service.authenticate_connection(connection_id, auth_token, mock_websocket_user)
        performance_timer.stop()
        
        # Assert
        assert result is True
        performance_timer.assert_under_ms(200, "Connection authentication")
    
    async def test_authenticate_connection_not_found(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user
    ):
        """Test authentication with non-existent connection."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_id = "nonexistent_conn"
        auth_token = "valid_jwt_token"
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=None)
        
        # Act & Assert
        with pytest.raises(NotFoundError):
            await service.authenticate_connection(connection_id, auth_token, mock_websocket_user)
    
    async def test_update_connection_activity_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        performance_timer
    ):
        """Test connection activity update with performance validation."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_id = "test_conn_123"
        
        mock_connection = MagicMock()
        mock_connection.status = ConnectionStatus.AUTHENTICATED
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.update_connection_status = AsyncMock(return_value=True)
        
        # Act
        performance_timer.start()
        result = await service.update_connection_activity(connection_id)
        performance_timer.stop()
        
        # Assert
        assert result is True
        performance_timer.assert_under_ms(100, "Connection activity update")
    
    async def test_disconnect_connection_success(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful connection disconnection with performance validation."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        connection_id = "test_conn_123"
        reason = "User requested disconnect"
        
        mock_connection = MagicMock()
        mock_connection.id = 1
        mock_connection.user_id = mock_websocket_user.id
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_by_connection_id = AsyncMock(return_value=mock_connection)
            mock_repo.return_value.update_connection_status = AsyncMock(return_value=True)
        
        with patch.object(service, '_send_connection_notifications') as mock_notify:
            mock_notify.return_value = None
        
        # Act
        performance_timer.start()
        result = await service.disconnect_connection(connection_id, reason)
        performance_timer.stop()
        
        # Assert
        assert result is True
        performance_timer.assert_under_ms(200, "Connection disconnection")
    
    async def test_get_user_connections_with_rbac(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test getting user connections with RBAC validation."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        
        mock_connections = [MagicMock(), MagicMock()]
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_active_connections_by_user = AsyncMock(return_value=mock_connections)
        
        # Act
        performance_timer.start()
        result = await service.get_user_connections(mock_websocket_user.id, mock_websocket_user)
        performance_timer.stop()
        
        # Assert
        assert len(result) == 2
        performance_timer.assert_under_ms(200, "Get user connections")
    
    async def test_get_user_connections_permission_denied(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user
    ):
        """Test getting user connections with insufficient permissions."""
        # Arrange
        service = WebSocketConnectionService(mock_async_session, mock_cache_manager)
        other_user_id = 999
        
        # Act & Assert
        with pytest.raises(ValidationError, match="Cannot view connections for another user"):
            await service.get_user_connections(other_user_id, mock_websocket_user)


@pytest.mark.unit
class TestWebSocketEventService:
    """Unit tests for WebSocket event service."""
    
    async def test_create_event_success(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        websocket_event_factory,
        performance_timer
    ):
        """Test successful event creation with content moderation."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        event_data = WebSocketEventCreate(
            connection_id=1,
            event_type=EventTypeEnum.MESSAGE_SENT,
            priority=EventPriorityEnum.NORMAL,
            payload={"message": "Hello, world!"}
        )
        
        mock_connection = MagicMock()
        mock_connection.status = ConnectionStatus.AUTHENTICATED
        
        mock_event = MagicMock()
        mock_event.id = 1
        mock_event.event_id = "evt_123"
        mock_event.event_type = EventType.MESSAGE_SENT
        
        with patch.object(service, 'connection_repository') as mock_conn_repo:
            mock_conn_repo.get = AsyncMock(return_value=mock_connection)
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_event = AsyncMock(return_value=mock_event)
        
        with patch.object(service, '_moderate_event_content') as mock_moderate:
            mock_moderate.return_value = event_data.payload
        
        # Act
        performance_timer.start()
        result = await service.create_event(event_data, mock_websocket_user, moderate_content=True)
        performance_timer.stop()
        
        # Assert
        assert result is not None
        assert result.event_id == "evt_123"
        performance_timer.assert_under_ms(500, "Event creation")
        mock_moderate.assert_called_once()
    
    async def test_create_event_invalid_connection(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user
    ):
        """Test event creation with invalid connection."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        event_data = WebSocketEventCreate(
            connection_id=999,
            event_type=EventTypeEnum.MESSAGE_SENT,
            priority=EventPriorityEnum.NORMAL,
            payload={"message": "Hello, world!"}
        )
        
        with patch.object(service, 'connection_repository') as mock_conn_repo:
            mock_conn_repo.get = AsyncMock(return_value=None)
        
        # Act & Assert
        with pytest.raises(ValidationError, match="Connection 999 not found"):
            await service.create_event(event_data, mock_websocket_user)
    
    async def test_mark_event_sent_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        performance_timer
    ):
        """Test marking event as sent with performance validation."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        event_id = "evt_123"
        processing_time_ms = 50.0
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.mark_event_sent = AsyncMock(return_value=True)
        
        # Act
        performance_timer.start()
        result = await service.mark_event_sent(event_id, processing_time_ms)
        performance_timer.stop()
        
        # Assert
        assert result is True
        performance_timer.assert_under_ms(100, "Mark event sent")
    
    async def test_content_moderation(
        self, 
        mock_async_session,
        mock_cache_manager
    ):
        """Test content moderation functionality."""
        # Arrange
        service = WebSocketEventService(mock_async_session, mock_cache_manager)
        payload = {"content": "This contains spam content"}
        event_type = "message.sent"
        
        # Act
        moderated_payload = await service._moderate_event_content(payload, event_type)
        
        # Assert
        assert "***" in moderated_payload["content"]
        assert moderated_payload.get("moderated") is True
        assert "moderation_reason" in moderated_payload


@pytest.mark.unit
class TestUserPresenceService:
    """Unit tests for user presence service."""
    
    async def test_update_user_status_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test user status update with performance validation."""
        # Arrange
        service = UserPresenceService(mock_async_session, mock_cache_manager)
        status = "online"
        activity_type = "manual_update"
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.status = status
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.update_user_status = AsyncMock(return_value=True)
            mock_repo.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
        
        # Act
        performance_timer.start()
        result = await service.update_user_status(
            mock_websocket_user.id, status, activity_type, mock_websocket_user
        )
        performance_timer.stop()
        
        # Assert
        assert result is not None
        assert result.status == status
        performance_timer.assert_under_ms(100, "User status update")
    
    async def test_increment_user_connections_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test incrementing user connections with performance validation."""
        # Arrange
        service = UserPresenceService(mock_async_session, mock_cache_manager)
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.active_connections_count = 2
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.increment_connections = AsyncMock(return_value=True)
            mock_repo.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
        
        # Act
        performance_timer.start()
        result = await service.increment_user_connections(mock_websocket_user.id, mock_websocket_user)
        performance_timer.stop()
        
        # Assert
        assert result is not None
        assert result.active_connections_count == 2
        performance_timer.assert_under_ms(100, "Increment user connections")
    
    async def test_get_online_users_with_rbac(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_websocket_user,
        performance_timer
    ):
        """Test getting online users with RBAC validation."""
        # Arrange
        service = UserPresenceService(mock_async_session, mock_cache_manager)
        limit = 50
        
        mock_users = [MagicMock() for _ in range(3)]
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_online_users = AsyncMock(return_value=mock_users)
        
        # Act
        performance_timer.start()
        result = await service.get_online_users(limit, mock_websocket_user)
        performance_timer.stop()
        
        # Assert
        assert len(result) == 3
        performance_timer.assert_under_ms(200, "Get online users")


@pytest.mark.unit
class TestWebSocketMetricsService:
    """Unit tests for WebSocket metrics service."""
    
    async def test_create_hourly_metrics_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_admin_user,
        websocket_metrics_factory,
        performance_timer
    ):
        """Test creating hourly metrics with performance validation."""
        # Arrange
        service = WebSocketMetricsService(mock_async_session, mock_cache_manager)
        metric_date = datetime.now(timezone.utc)
        metric_hour = 14
        metrics_data = websocket_metrics_factory()
        
        mock_metrics = MagicMock()
        mock_metrics.id = 1
        mock_metrics.metric_date = metric_date
        mock_metrics.metric_hour = metric_hour
        mock_metrics.total_connections = 10
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.create_or_update_hourly_metrics = AsyncMock(return_value=mock_metrics)
        
        # Act
        performance_timer.start()
        result = await service.create_or_update_hourly_metrics(
            metric_date, metric_hour, metrics_data, mock_admin_user
        )
        performance_timer.stop()
        
        # Assert
        assert result is not None
        assert result["total_connections"] == 10
        performance_timer.assert_under_ms(500, "Create hourly metrics")
    
    async def test_get_performance_summary_performance(
        self, 
        mock_async_session,
        mock_cache_manager,
        mock_admin_user,
        performance_timer
    ):
        """Test getting performance summary with performance validation."""
        # Arrange
        service = WebSocketMetricsService(mock_async_session, mock_cache_manager)
        hours = 24
        
        mock_summary = {
            "avg_message_delivery_time_ms": 75.0,
            "peak_concurrent_connections": 100,
            "total_messages_processed": 1000
        }
        
        with patch.object(service, '_get_repository') as mock_repo:
            mock_repo.return_value.get_performance_summary = AsyncMock(return_value=mock_summary)
        
        # Act
        performance_timer.start()
        result = await service.get_performance_summary(hours, mock_admin_user)
        performance_timer.stop()
        
        # Assert
        assert result is not None
        assert result["avg_message_delivery_time_ms"] == 75.0
        performance_timer.assert_under_ms(200, "Get performance summary")
