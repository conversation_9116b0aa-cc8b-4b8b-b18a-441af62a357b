"""
Tests for API versioning support and compatibility.

This module tests the versioning system features:
- API version management and negotiation
- Schema version compatibility and transformations
- Backward compatibility support
- Deprecation warnings and migration handling
"""

import pytest
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

from pydantic import Field

from app.schemas.versioning import (
    APIVersion, VersionInfo, VersionRegistry, VersionedSchema,
    VersionCompatibilityMixin, VersionNegotiator, VersionedResponse
)
from app.schemas.base import BaseSchema


class TestAPIVersion:
    """Test API version enumeration and utilities."""
    
    def test_version_values(self):
        """Test API version values."""
        assert APIVersion.V1_0.value == "1.0"
        assert APIVersion.V1_1.value == "1.1"
        assert APIVersion.V2_0.value == "2.0"
    
    def test_latest_version(self):
        """Test latest version detection."""
        latest = APIVersion.latest()
        assert latest == APIVersion.V2_0
    
    def test_supported_versions(self):
        """Test supported versions list."""
        versions = APIVersion.supported_versions()
        assert "1.0" in versions
        assert "1.1" in versions
        assert "2.0" in versions
        assert len(versions) == 3
    
    def test_deprecation_check(self):
        """Test version deprecation status."""
        assert APIVersion.V1_0.is_deprecated() is True
        assert APIVersion.V1_1.is_deprecated() is False
        assert APIVersion.V2_0.is_deprecated() is False


class TestVersionInfo:
    """Test version information and metadata."""
    
    def test_version_info_creation(self):
        """Test version info creation."""
        release_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
        deprecation_date = datetime(2024, 6, 1, tzinfo=timezone.utc)
        
        info = VersionInfo(
            version=APIVersion.V1_0,
            release_date=release_date,
            deprecation_date=deprecation_date,
            breaking_changes=["Change 1", "Change 2"]
        )
        
        assert info.version == APIVersion.V1_0
        assert info.release_date == release_date
        assert info.deprecation_date == deprecation_date
        assert len(info.breaking_changes) == 2
    
    def test_deprecation_status(self):
        """Test deprecation status calculation."""
        now = datetime.now(timezone.utc)
        
        # Not deprecated (no deprecation date)
        info1 = VersionInfo(
            version=APIVersion.V2_0,
            release_date=now - timedelta(days=30)
        )
        assert info1.is_deprecated is False
        
        # Deprecated (past deprecation date)
        info2 = VersionInfo(
            version=APIVersion.V1_0,
            release_date=now - timedelta(days=365),
            deprecation_date=now - timedelta(days=30)
        )
        assert info2.is_deprecated is True
        
        # Not yet deprecated (future deprecation date)
        info3 = VersionInfo(
            version=APIVersion.V1_1,
            release_date=now - timedelta(days=30),
            deprecation_date=now + timedelta(days=30)
        )
        assert info3.is_deprecated is False
    
    def test_end_of_life_status(self):
        """Test end of life status calculation."""
        now = datetime.now(timezone.utc)
        
        # Not end of life (no EOL date)
        info1 = VersionInfo(
            version=APIVersion.V2_0,
            release_date=now - timedelta(days=30)
        )
        assert info1.is_end_of_life is False
        
        # End of life (past EOL date)
        info2 = VersionInfo(
            version=APIVersion.V1_0,
            release_date=now - timedelta(days=365),
            end_of_life_date=now - timedelta(days=30)
        )
        assert info2.is_end_of_life is True


class TestVersionRegistry:
    """Test version registry functionality."""
    
    def test_get_version_info(self):
        """Test retrieving version information."""
        info = VersionRegistry.get_version_info(APIVersion.V1_0)
        
        assert info is not None
        assert info.version == APIVersion.V1_0
        assert isinstance(info.breaking_changes, list)
        assert len(info.breaking_changes) > 0
    
    def test_get_supported_versions(self):
        """Test getting supported (non-EOL) versions."""
        supported = VersionRegistry.get_supported_versions()
        
        # Should include all versions that are not end-of-life
        assert APIVersion.V1_0 in supported  # May be deprecated but not EOL
        assert APIVersion.V1_1 in supported
        assert APIVersion.V2_0 in supported
    
    def test_get_latest_version(self):
        """Test getting latest supported version."""
        latest = VersionRegistry.get_latest_version()
        assert latest == APIVersion.V2_0


class TestVersionedSchema:
    """Test versioned schema functionality."""
    
    def test_versioned_schema_creation(self):
        """Test creating versioned schema."""
        
        class TestVersionedSchema(VersionedSchema):
            name: str = Field(..., description="Test name")
            value: int = Field(..., description="Test value")
        
        data = {"name": "test", "value": 42}
        schema = TestVersionedSchema(**data)
        
        assert schema.name == "test"
        assert schema.value == 42
        assert schema._api_version == APIVersion.latest()
    
    def test_explicit_version_setting(self):
        """Test setting explicit API version."""
        
        class TestVersionedSchema(VersionedSchema):
            name: str
        
        data = {"name": "test", "_api_version": "1.0"}
        schema = TestVersionedSchema(**data)
        
        assert schema.name == "test"
        assert schema._api_version == APIVersion.V1_0
    
    def test_invalid_version_handling(self):
        """Test handling of invalid version strings."""
        
        class TestVersionedSchema(VersionedSchema):
            name: str
        
        # Should fall back to latest version for invalid version
        data = {"name": "test", "_api_version": "invalid"}
        schema = TestVersionedSchema(**data)
        
        assert schema.name == "test"
        assert schema._api_version == APIVersion.latest()
    
    def test_version_specific_serialization(self):
        """Test version-specific serialization."""
        
        class TestVersionedSchema(VersionedSchema):
            name: str
            new_field: str = "default"
            
            def _format_v1_0(self, data: Dict[str, Any]) -> Dict[str, Any]:
                # Remove new_field for v1.0 compatibility
                data.pop('new_field', None)
                return data
        
        schema = TestVersionedSchema(name="test", new_field="value")
        
        # Default serialization (latest version)
        default_dict = schema.dict()
        assert "new_field" in default_dict
        
        # v1.0 serialization
        v1_dict = schema.dict(version=APIVersion.V1_0)
        assert "new_field" not in v1_dict
        assert "name" in v1_dict


class TestVersionCompatibilityMixin:
    """Test version compatibility mixin."""
    
    def test_v1_0_transformations(self):
        """Test v1.0 compatibility transformations."""
        
        class TestSchema(VersionedSchema, VersionCompatibilityMixin):
            page: int = 1
            size: int = 20
            is_active: bool = True
        
        # Test legacy field name transformation
        data = {
            "page_number": 2,
            "page_size": 50,
            "is_active": "true",
            "_api_version": "1.0"
        }
        
        schema = TestSchema(**data)
        
        assert schema.page == 2  # Transformed from page_number
        assert schema.size == 50  # Transformed from page_size
        assert schema.is_active is True  # Converted from string
    
    def test_v1_0_output_formatting(self):
        """Test v1.0 output formatting."""
        
        class TestSchema(VersionedSchema, VersionCompatibilityMixin):
            page: int = 1
            size: int = 20
            metadata: dict = {}
        
        schema = TestSchema(page=2, size=50)
        
        # Test v1.0 output format
        v1_dict = schema.dict(version=APIVersion.V1_0)
        
        assert "page_number" in v1_dict  # Converted back to legacy name
        assert "page_size" in v1_dict    # Converted back to legacy name
        assert "metadata" not in v1_dict  # Excluded for v1.0
        assert v1_dict["page_number"] == 2
        assert v1_dict["page_size"] == 50


class TestVersionNegotiator:
    """Test version negotiation functionality."""
    
    def test_exact_version_match(self):
        """Test exact version matching."""
        supported = [APIVersion.V1_0, APIVersion.V1_1, APIVersion.V2_0]
        
        # Test exact match
        negotiated = VersionNegotiator.negotiate_version(
            requested_version="1.1",
            supported_versions=supported
        )
        
        assert negotiated == APIVersion.V1_1
    
    def test_unsupported_version_fallback(self):
        """Test fallback for unsupported versions."""
        supported = [APIVersion.V1_1, APIVersion.V2_0]
        
        # Test unsupported version falls back to default
        negotiated = VersionNegotiator.negotiate_version(
            requested_version="3.0",  # Not supported
            supported_versions=supported,
            default_version=APIVersion.V2_0
        )
        
        assert negotiated == APIVersion.V2_0
    
    def test_no_version_requested(self):
        """Test behavior when no version is requested."""
        supported = [APIVersion.V1_1, APIVersion.V2_0]
        
        negotiated = VersionNegotiator.negotiate_version(
            requested_version=None,
            supported_versions=supported,
            default_version=APIVersion.V2_0
        )
        
        assert negotiated == APIVersion.V2_0
    
    def test_invalid_version_format(self):
        """Test handling of invalid version format."""
        supported = [APIVersion.V1_1, APIVersion.V2_0]
        
        negotiated = VersionNegotiator.negotiate_version(
            requested_version="invalid-version",
            supported_versions=supported,
            default_version=APIVersion.V2_0
        )
        
        assert negotiated == APIVersion.V2_0
    
    def test_compatibility_check(self):
        """Test version compatibility checking."""
        # Exact match should be compatible
        assert VersionNegotiator.check_compatibility(
            APIVersion.V1_1, APIVersion.V1_1
        ) is True
        
        # Different versions should not be compatible (for now)
        assert VersionNegotiator.check_compatibility(
            APIVersion.V1_0, APIVersion.V1_1
        ) is False


class TestVersionedResponse:
    """Test versioned response wrapper."""
    
    def test_versioned_response_creation(self):
        """Test creating versioned response."""
        data = {"message": "success", "items": [1, 2, 3]}
        
        response = VersionedResponse(
            data=data,
            version=APIVersion.V1_1
        )
        
        assert response.data == data
        assert response.version == APIVersion.V1_1
        assert isinstance(response.timestamp, datetime)
        assert response.deprecation_warning is None  # V1.1 not deprecated
    
    def test_deprecation_warning(self):
        """Test deprecation warning for deprecated versions."""
        data = {"message": "success"}
        
        response = VersionedResponse(
            data=data,
            version=APIVersion.V1_0  # This version is deprecated
        )
        
        assert response.data == data
        assert response.version == APIVersion.V1_0
        assert response.deprecation_warning is not None
        assert "deprecated" in response.deprecation_warning.lower()
        assert APIVersion.latest().value in response.deprecation_warning
    
    def test_non_deprecated_version(self):
        """Test no warning for non-deprecated versions."""
        data = {"message": "success"}
        
        response = VersionedResponse(
            data=data,
            version=APIVersion.V2_0  # Latest version
        )
        
        assert response.deprecation_warning is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
