# PostgreSQL Migration Guide for Culture Connect Backend

## 🚨 Critical Database Architecture Issues Identified

### **Problem Statement**
The Culture Connect Backend was incorrectly configured with SQLite for development, while the production target is PostgreSQL as specified in `guardrails.md`. This creates fundamental compatibility issues that must be resolved immediately.

### **Issues Identified**
1. **Database Target Mismatch**: SQLite development vs PostgreSQL production
2. **Model Compatibility**: Some features may not work correctly across databases
3. **Migration Compatibility**: Migrations created for SQLite may fail in PostgreSQL
4. **Performance Implications**: SQLite cannot handle enterprise-level concurrent connections
5. **Data Type Differences**: JSON handling and other data types differ between databases

## 📋 **Migration Action Plan**

### **Phase 1: PostgreSQL Environment Setup**

#### **1.1 Install PostgreSQL**
```bash
# macOS (using Homebrew)
brew install postgresql@15
brew services start postgresql@15

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-15 postgresql-contrib-15

# CentOS/RHEL
sudo dnf install postgresql15-server postgresql15-contrib
sudo postgresql-15-setup initdb
sudo systemctl enable postgresql-15
sudo systemctl start postgresql-15
```

#### **1.2 Create Development Database**
```bash
# Connect as postgres user
sudo -u postgres psql

# Create user and database
CREATE USER culture_connect WITH PASSWORD 'culture_connect_dev_password';
CREATE DATABASE culture_connect_dev OWNER culture_connect;
GRANT ALL PRIVILEGES ON DATABASE culture_connect_dev TO culture_connect;

# Enable required extensions
\c culture_connect_dev
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

\q
```

#### **1.3 Update Environment Configuration**
The `.env` file has been updated with PostgreSQL configuration:
```env
CC_DATABASE_URL=postgresql+asyncpg://culture_connect:culture_connect_dev_password@localhost:5432/culture_connect_dev
```

### **Phase 2: Database Migration Process**

#### **2.1 Backup Existing SQLite Data (if needed)**
```bash
# Export existing data to SQL
sqlite3 culture_connect_dev.db .dump > sqlite_backup.sql

# Or export specific tables
sqlite3 culture_connect_dev.db "SELECT * FROM users;" > users_backup.csv
```

#### **2.2 Run PostgreSQL Setup Script**
```bash
# Setup PostgreSQL database with optimizations
python scripts/setup_postgresql.py --env development

# Verify connection
python scripts/setup_postgresql.py --env development --verify-only
```

#### **2.3 Run Database Migrations**
```bash
# Reset migration history (if needed)
alembic stamp head

# Run initial migration
alembic upgrade head

# Run PostgreSQL optimization migration
alembic upgrade postgresql_optimized
```

### **Phase 3: Data Migration (if needed)**

#### **3.1 Convert SQLite Data to PostgreSQL**
```python
# scripts/migrate_sqlite_to_postgresql.py
import asyncio
import sqlite3
from sqlalchemy import create_engine
from app.db.database import AsyncSessionLocal
from app.models.user import User
from app.models.vendor import Vendor, VendorProfile

async def migrate_data():
    # Connect to SQLite
    sqlite_conn = sqlite3.connect('culture_connect_dev.db')
    sqlite_conn.row_factory = sqlite3.Row
    
    # Migrate users
    async with AsyncSessionLocal() as session:
        cursor = sqlite_conn.execute("SELECT * FROM users")
        for row in cursor:
            user = User(**dict(row))
            session.add(user)
        await session.commit()
    
    sqlite_conn.close()

if __name__ == "__main__":
    asyncio.run(migrate_data())
```

### **Phase 4: Testing and Validation**

#### **4.1 Database Connection Test**
```bash
# Test database connection
python -c "
import asyncio
from app.db.session import test_database_connection
print('Database connection:', asyncio.run(test_database_connection()))
"
```

#### **4.2 Run Seeding Script**
```bash
# Seed database with test data
python -m app.db.seed seed development
```

#### **4.3 Run Test Suite**
```bash
# Run database-related tests
python -m pytest tests/unit/test_database_setup.py -v
python -m pytest tests/integration/test_database_setup.py -v
```

## 🔧 **PostgreSQL-Specific Optimizations**

### **Performance Enhancements**
1. **JSONB Columns**: Better performance than JSON for complex queries
2. **GIN Indexes**: For full-text search and JSONB operations
3. **GIST Indexes**: For spatial data and range queries
4. **Trigram Indexes**: For fuzzy text search
5. **Partial Indexes**: For conditional indexing
6. **Connection Pooling**: Optimized for PostgreSQL

### **Production-Grade Features**
1. **UUID Primary Keys**: Using PostgreSQL's uuid-ossp extension
2. **Full-Text Search**: Built-in PostgreSQL search capabilities
3. **Spatial Data Support**: PostGIS extension for geo-location features
4. **Advanced Constraints**: Check constraints and custom validators
5. **Triggers**: Automatic search vector updates
6. **Enum Types**: Type-safe enumeration values

## 📊 **Compatibility Matrix**

| Feature | SQLite | PostgreSQL | Status |
|---------|--------|------------|--------|
| JSON Columns | ✅ | ✅ (JSONB) | ✅ Compatible |
| UUID Support | ❌ | ✅ | ⚠️ Requires migration |
| Full-Text Search | Limited | ✅ | ✅ Enhanced |
| Spatial Data | ❌ | ✅ (PostGIS) | ✅ New feature |
| Concurrent Connections | Limited | ✅ | ✅ Production-ready |
| Advanced Indexing | Limited | ✅ | ✅ Performance boost |
| Triggers | Basic | ✅ | ✅ Enhanced |
| Custom Types | ❌ | ✅ | ✅ Type safety |

## 🚀 **Production Deployment Considerations**

### **Environment-Specific Configurations**

#### **Development**
```env
CC_DATABASE_URL=postgresql+asyncpg://culture_connect:dev_password@localhost:5432/culture_connect_dev
CC_DATABASE_POOL_SIZE=5
CC_DATABASE_MAX_OVERFLOW=10
```

#### **Staging**
```env
CC_DATABASE_URL=postgresql+asyncpg://culture_connect_staging:staging_password@staging-db:5432/culture_connect_staging
CC_DATABASE_POOL_SIZE=10
CC_DATABASE_MAX_OVERFLOW=20
```

#### **Production**
```env
CC_DATABASE_URL=postgresql+asyncpg://culture_connect_prod:secure_password@prod-db:5432/culture_connect_prod
CC_DATABASE_POOL_SIZE=20
CC_DATABASE_MAX_OVERFLOW=40
```

### **Security Considerations**
1. **SSL/TLS Encryption**: Required for production
2. **Connection Limits**: Prevent connection exhaustion
3. **Query Timeouts**: Prevent long-running queries
4. **Access Controls**: Role-based database permissions
5. **Audit Logging**: Track database changes
6. **Backup Strategy**: Regular automated backups

## ✅ **Validation Checklist**

### **Pre-Migration**
- [ ] PostgreSQL 15+ installed and running
- [ ] Database user and permissions configured
- [ ] Required extensions enabled
- [ ] Environment variables updated
- [ ] Backup of existing data (if any)

### **Post-Migration**
- [ ] Database connection successful
- [ ] All migrations applied successfully
- [ ] Seeding script runs without errors
- [ ] Test suite passes (>80% coverage)
- [ ] Performance benchmarks meet requirements
- [ ] Full-text search functionality working
- [ ] Spatial queries functional (if applicable)
- [ ] Connection pooling optimized

### **Production Readiness**
- [ ] SSL/TLS configured
- [ ] Monitoring and alerting setup
- [ ] Backup and recovery tested
- [ ] Performance tuning completed
- [ ] Security audit passed
- [ ] Load testing completed
- [ ] Disaster recovery plan documented

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Connection Errors**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql-15

# Check connection
psql -h localhost -U culture_connect -d culture_connect_dev
```

#### **Migration Failures**
```bash
# Reset migrations
alembic downgrade base
alembic upgrade head
```

#### **Performance Issues**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'your_table';
```

## 📚 **Additional Resources**

1. **PostgreSQL Documentation**: https://www.postgresql.org/docs/15/
2. **SQLAlchemy PostgreSQL Dialect**: https://docs.sqlalchemy.org/en/20/dialects/postgresql.html
3. **Alembic Migration Guide**: https://alembic.sqlalchemy.org/en/latest/
4. **FastAPI Database Guide**: https://fastapi.tiangolo.com/tutorial/sql-databases/
5. **PostgreSQL Performance Tuning**: https://wiki.postgresql.org/wiki/Performance_Optimization

---

**Next Steps**: Execute this migration plan systematically, ensuring each phase is completed and validated before proceeding to the next.
