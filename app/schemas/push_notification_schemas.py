"""
Push Notification schemas for Culture Connect Backend API.

This module defines comprehensive push notification management schemas including:
- DeviceToken schemas: Device registration and management with platform validation
- NotificationTemplate schemas: Template management with variable substitution
- NotificationDelivery schemas: Delivery tracking and status reporting
- NotificationPreference schemas: User notification preference management
- FCM schemas: Firebase Cloud Messaging request/response validation

Implements Task 2.3.2 Phase 2 requirements for push notification system with
production-grade Pydantic V2 validation and seamless integration with Phase 1 models.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from pydantic import field_validator, Field, ConfigDict

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin, PaginationMixin
from app.models.push_notification_models import (
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)


class PushNotificationBaseSchema(BaseSchema):
    """Base schema for push notification with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-25T01:00:00Z",
                "updated_at": "2025-01-25T01:00:00Z"
            }
        }
    )


# Device Token Schemas
class DeviceTokenBase(PushNotificationBaseSchema):
    """Base schema for device token with common fields."""

    token: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="FCM device registration token",
        example="fGxqZ2JHRzpBUEE5MWJIaUVoajJfVGhHVzFkWnZOaUlkdWlRcVo4NjNHMzRkX1E"
    )

    platform: DevicePlatform = Field(
        ...,
        description="Device platform (iOS, Android, Web)",
        example=DevicePlatform.ANDROID
    )

    device_id: Optional[str] = Field(
        None,
        max_length=255,
        description="Unique device identifier",
        example="550e8400-e29b-41d4-a716-446655440000"
    )

    device_name: Optional[str] = Field(
        None,
        max_length=255,
        description="Human-readable device name",
        example="John's iPhone 15"
    )

    app_version: Optional[str] = Field(
        None,
        max_length=50,
        description="Application version",
        example="1.2.3"
    )

    os_version: Optional[str] = Field(
        None,
        max_length=50,
        description="Operating system version",
        example="iOS 17.2"
    )

    @field_validator('token')
    @classmethod
    def validate_fcm_token_format(cls, v):
        """Validate FCM token format."""
        if not v:
            raise ValueError("FCM token cannot be empty")

        # Basic FCM token validation (alphanumeric, hyphens, underscores)
        import re
        if not re.match(r'^[A-Za-z0-9_-]+$', v):
            raise ValueError("Invalid FCM token format")

        return v

    @field_validator('device_id')
    @classmethod
    def validate_device_id_format(cls, v):
        """Validate device ID format."""
        if v is None:
            return v

        # Allow UUID format or alphanumeric with hyphens
        import re
        if not re.match(r'^[A-Za-z0-9_-]+$', v):
            raise ValueError("Invalid device ID format")

        return v


class DeviceTokenCreate(DeviceTokenBase):
    """Schema for device token registration."""

    registration_ip: Optional[str] = Field(
        None,
        description="Client IP address during registration",
        example="*************"
    )

    user_agent: Optional[str] = Field(
        None,
        description="Client user agent string",
        example="Culture Connect/1.2.3 (iOS; iPhone15,2)"
    )

    device_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional device metadata",
        example={"timezone": "America/New_York", "language": "en-US"}
    )

    @field_validator('registration_ip')
    @classmethod
    def validate_ip_address(cls, v):
        """Validate IP address format."""
        if v is None:
            return v

        import ipaddress
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError("Invalid IP address format")

        return v


class DeviceTokenUpdate(PushNotificationBaseSchema):
    """Schema for device token updates."""

    device_name: Optional[str] = Field(
        None,
        max_length=255,
        description="Updated device name"
    )

    app_version: Optional[str] = Field(
        None,
        max_length=50,
        description="Updated application version"
    )

    os_version: Optional[str] = Field(
        None,
        max_length=50,
        description="Updated operating system version"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Device token active status"
    )

    device_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated device metadata"
    )


class DeviceTokenResponse(DeviceTokenBase, TimestampMixin, UUIDMixin):
    """Schema for device token response."""

    id: UUID = Field(description="Device token unique identifier")
    user_id: int = Field(description="Associated user ID")
    is_active: bool = Field(description="Device token active status")
    is_validated: bool = Field(description="Device token validation status")
    last_used_at: Optional[datetime] = Field(None, description="Last usage timestamp")
    validation_attempts: int = Field(description="Number of validation attempts")
    expires_at: Optional[datetime] = Field(None, description="Token expiration timestamp")


class DeviceTokenListResponse(PaginationMixin):
    """Schema for device token list response."""

    items: List[DeviceTokenResponse] = Field(description="List of device tokens")


# Notification Template Schemas
class NotificationTemplateBase(PushNotificationBaseSchema):
    """Base schema for notification template with common fields."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Template name identifier (unique per version)",
        example="booking_confirmation"
    )

    category: NotificationCategory = Field(
        ...,
        description="Notification category for organization and targeting",
        example=NotificationCategory.BOOKING
    )

    title_template: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Jinja2 template for notification title with variable substitution",
        example="Booking Confirmed - {{venue_name}}"
    )

    body_template: str = Field(
        ...,
        min_length=1,
        description="Jinja2 template for notification body with variable substitution",
        example="Your booking at {{venue_name}} on {{booking_date}} has been confirmed!"
    )

    variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Required template variables with descriptions and types",
        example={"venue_name": "string", "booking_date": "datetime", "booking_id": "integer"}
    )

    default_priority: NotificationPriority = Field(
        NotificationPriority.NORMAL,
        description="Default notification priority level"
    )

    is_active: bool = Field(
        True,
        description="Template active status for lifecycle management"
    )

    @field_validator('title_template', 'body_template')
    @classmethod
    def validate_template_syntax(cls, v):
        """Validate Jinja2 template syntax."""
        if not v:
            return v

        try:
            from jinja2 import Template
            Template(v)
        except Exception as e:
            raise ValueError(f"Invalid Jinja2 template syntax: {str(e)}")

        return v

    @field_validator('variables')
    @classmethod
    def validate_variables_format(cls, v):
        """Validate template variables format."""
        if not isinstance(v, dict):
            raise ValueError("Variables must be a dictionary")

        # Ensure all keys are strings
        for key in v.keys():
            if not isinstance(key, str):
                raise ValueError("Variable names must be strings")

        return v


class NotificationTemplateCreate(NotificationTemplateBase):
    """Schema for notification template creation."""

    # Platform-specific payload configurations
    ios_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="iOS-specific notification payload (APNs format)",
        example={
            "aps": {
                "sound": "default",
                "badge": 1,
                "content-available": 1
            },
            "custom_data": {"action": "view_booking"}
        }
    )

    android_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Android-specific notification payload (FCM format)",
        example={
            "notification": {
                "icon": "ic_notification",
                "color": "#FF6B35"
            },
            "data": {"action": "view_booking"}
        }
    )

    web_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Web-specific notification payload",
        example={
            "icon": "/icons/notification.png",
            "badge": "/icons/badge.png",
            "actions": [{"action": "view", "title": "View Booking"}]
        }
    )

    # A/B testing and analytics
    click_action: Optional[str] = Field(
        None,
        max_length=255,
        description="Action to perform when notification is clicked",
        example="FLUTTER_NOTIFICATION_CLICK"
    )

    deep_link: Optional[str] = Field(
        None,
        max_length=500,
        description="Deep link URL for navigation",
        example="cultureconnect://booking/12345"
    )

    image_url: Optional[str] = Field(
        None,
        max_length=500,
        description="Image URL for rich notifications",
        example="https://example.com/images/booking-confirmation.jpg"
    )

    @field_validator('ios_payload', 'android_payload', 'web_payload')
    @classmethod
    def validate_platform_payload(cls, v):
        """Validate platform-specific payload format."""
        if v is None:
            return v

        if not isinstance(v, dict):
            raise ValueError("Platform payload must be a dictionary")

        return v

    @field_validator('deep_link')
    @classmethod
    def validate_deep_link_format(cls, v):
        """Validate deep link URL format."""
        if v is None:
            return v

        import re
        # Allow custom schemes and standard URLs
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9+.-]*:', v):
            raise ValueError("Invalid deep link format")

        return v


class NotificationTemplateUpdate(PushNotificationBaseSchema):
    """Schema for notification template updates."""

    title_template: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="Updated notification title template"
    )

    body_template: Optional[str] = Field(
        None,
        min_length=1,
        description="Updated notification body template"
    )

    variables: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated template variables"
    )

    default_priority: Optional[NotificationPriority] = Field(
        None,
        description="Updated default priority"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Updated active status"
    )

    ios_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated iOS payload"
    )

    android_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated Android payload"
    )

    web_payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated Web payload"
    )

    click_action: Optional[str] = Field(
        None,
        max_length=255,
        description="Updated click action"
    )

    deep_link: Optional[str] = Field(
        None,
        max_length=500,
        description="Updated deep link"
    )

    image_url: Optional[str] = Field(
        None,
        max_length=500,
        description="Updated image URL"
    )


class NotificationTemplateResponse(NotificationTemplateBase, TimestampMixin):
    """Schema for notification template response."""

    id: UUID = Field(description="Template unique identifier")
    version: int = Field(description="Template version number")
    created_by: int = Field(description="User ID who created the template")
    ios_payload: Optional[Dict[str, Any]] = Field(None, description="iOS-specific payload")
    android_payload: Optional[Dict[str, Any]] = Field(None, description="Android-specific payload")
    web_payload: Optional[Dict[str, Any]] = Field(None, description="Web-specific payload")
    click_action: Optional[str] = Field(None, description="Click action")
    deep_link: Optional[str] = Field(None, description="Deep link URL")
    image_url: Optional[str] = Field(None, description="Image URL")


class NotificationTemplateListResponse(PaginationMixin):
    """Schema for notification template list response."""

    items: List[NotificationTemplateResponse] = Field(description="List of notification templates")


# Notification Sending Schemas
class NotificationSendRequest(PushNotificationBaseSchema):
    """Schema for push notification sending requests."""

    device_token_ids: Optional[List[UUID]] = Field(
        None,
        description="Specific device token IDs to send to (if not using user_id)"
    )

    user_id: Optional[int] = Field(
        None,
        description="User ID to send notification to all their devices"
    )

    template_id: Optional[UUID] = Field(
        None,
        description="Template ID for template-based notifications"
    )

    title: Optional[str] = Field(
        None,
        description="Notification title (required if not using template)",
        max_length=500
    )

    body: Optional[str] = Field(
        None,
        description="Notification body (required if not using template)"
    )

    template_variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Variables for template substitution",
        example={"user_name": "John Doe", "booking_id": "12345"}
    )

    priority: NotificationPriority = Field(
        NotificationPriority.NORMAL,
        description="Notification priority level"
    )

    scheduled_at: Optional[datetime] = Field(
        None,
        description="Optional scheduled delivery time"
    )

    expires_at: Optional[datetime] = Field(
        None,
        description="Optional expiration time"
    )

    payload: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional notification payload data"
    )

    @field_validator('device_token_ids', 'user_id')
    @classmethod
    def validate_recipient_specification(cls, v, info):
        """Ensure either device_token_ids or user_id is specified."""
        values = info.data if hasattr(info, 'data') else {}
        device_token_ids = values.get('device_token_ids')
        user_id = values.get('user_id')

        if not device_token_ids and not user_id:
            raise ValueError("Either device_token_ids or user_id must be specified")

        if device_token_ids and user_id:
            raise ValueError("Cannot specify both device_token_ids and user_id")

        return v

    @field_validator('title', 'body')
    @classmethod
    def validate_content_or_template(cls, v, info):
        """Ensure content is provided if not using template."""
        values = info.data if hasattr(info, 'data') else {}
        template_id = values.get('template_id')
        title = values.get('title')
        body = values.get('body')

        if not template_id and (not title or not body):
            raise ValueError("Title and body are required when not using a template")

        return v


class NotificationSendResponse(PushNotificationBaseSchema):
    """Schema for notification send response."""

    delivery_ids: List[UUID] = Field(description="List of delivery tracking IDs")
    total_sent: int = Field(description="Total number of notifications sent")
    failed_count: int = Field(description="Number of failed deliveries")
    scheduled_count: int = Field(description="Number of scheduled notifications")


class NotificationBatchSendRequest(PushNotificationBaseSchema):
    """Schema for batch notification sending."""

    notifications: List[NotificationSendRequest] = Field(
        description="List of notification send requests",
        min_length=1,
        max_length=1000
    )

    @field_validator('notifications')
    @classmethod
    def validate_batch_size(cls, v):
        """Validate batch size limits."""
        if len(v) > 1000:
            raise ValueError("Batch size cannot exceed 1000 notifications")

        return v


class NotificationBatchSendResponse(PushNotificationBaseSchema):
    """Schema for batch notification send response."""

    total_requested: int = Field(description="Total notifications requested")
    total_sent: int = Field(description="Total notifications sent successfully")
    total_failed: int = Field(description="Total notifications failed")
    total_scheduled: int = Field(description="Total notifications scheduled")
    delivery_ids: List[UUID] = Field(description="List of all delivery tracking IDs")
    failed_requests: List[Dict[str, Any]] = Field(
        description="Details of failed notification requests"
    )


# Notification Delivery Schemas
class NotificationDeliveryResponse(PushNotificationBaseSchema, TimestampMixin):
    """Schema for notification delivery response."""

    id: UUID = Field(description="Delivery unique identifier")
    user_id: int = Field(description="Associated user ID")
    device_token_id: UUID = Field(description="Device token ID")
    template_id: Optional[UUID] = Field(None, description="Template ID if used")

    title: str = Field(description="Notification title", max_length=500)
    body: str = Field(description="Notification body content")
    status: NotificationStatus = Field(description="Current delivery status")
    priority: NotificationPriority = Field(description="Notification priority level")

    # FCM response data
    fcm_message_id: Optional[str] = Field(None, description="FCM message ID")
    fcm_response: Optional[Dict[str, Any]] = Field(None, description="FCM response data")
    error_code: Optional[str] = Field(None, description="Error code if failed")
    error_message: Optional[str] = Field(None, description="Error message if failed")

    # Timing information
    sent_at: Optional[datetime] = Field(None, description="When notification was sent")
    delivered_at: Optional[datetime] = Field(None, description="When notification was delivered")
    clicked_at: Optional[datetime] = Field(None, description="When notification was clicked")

    # Retry information
    retry_count: int = Field(description="Number of retry attempts")
    max_retries: int = Field(description="Maximum retry attempts allowed")
    next_retry_at: Optional[datetime] = Field(None, description="Next retry timestamp")

    # Additional data
    payload: Optional[Dict[str, Any]] = Field(None, description="Notification payload")
    delivery_metadata: Dict[str, Any] = Field(description="Delivery metadata")


class NotificationDeliveryListResponse(PaginationMixin):
    """Schema for notification delivery list response."""

    items: List[NotificationDeliveryResponse] = Field(description="List of notification deliveries")


class NotificationDeliveryStatusUpdate(PushNotificationBaseSchema):
    """Schema for updating notification delivery status."""

    status: NotificationStatus = Field(description="Updated delivery status")
    fcm_message_id: Optional[str] = Field(None, description="FCM message ID")
    error_code: Optional[str] = Field(None, description="Error code if applicable")
    error_message: Optional[str] = Field(None, description="Error message if applicable")
    delivered_at: Optional[datetime] = Field(None, description="Delivery timestamp")
    clicked_at: Optional[datetime] = Field(None, description="Click timestamp")


# Notification Preference Schemas
class NotificationPreferenceBase(PushNotificationBaseSchema):
    """Base schema for notification preferences."""

    push_notifications_enabled: bool = Field(True, description="Global push notification toggle")

    # Category-specific preferences
    authentication_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Authentication notification frequency"
    )
    booking_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Booking notification frequency"
    )
    payment_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Payment notification frequency"
    )
    promotional_notifications: NotificationFrequency = Field(
        NotificationFrequency.DAILY, description="Promotional notification frequency"
    )
    system_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="System notification frequency"
    )
    security_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Security notification frequency"
    )
    social_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Social notification frequency"
    )
    reminder_notifications: NotificationFrequency = Field(
        NotificationFrequency.IMMEDIATE, description="Reminder notification frequency"
    )

    # Do not disturb settings
    dnd_enabled: bool = Field(False, description="Do not disturb mode enabled")
    dnd_start_time: Optional[str] = Field(
        None, description="Do not disturb start time (HH:MM format)", pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
    )
    dnd_end_time: Optional[str] = Field(
        None, description="Do not disturb end time (HH:MM format)", pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
    )
    dnd_timezone: str = Field("UTC", description="Timezone for do not disturb settings", max_length=50)

    # Platform-specific settings
    ios_badge_count: bool = Field(True, description="Enable iOS badge count")
    android_vibration: bool = Field(True, description="Enable Android vibration")
    sound_enabled: bool = Field(True, description="Enable notification sounds")

    # Advanced preferences
    language_code: str = Field(
        "en", description="Notification language code", max_length=10, pattern=r'^[a-z]{2}(-[A-Z]{2})?$'
    )
    max_daily_notifications: Optional[int] = Field(
        None, description="Maximum notifications per day (null = unlimited)", ge=0, le=1000
    )


class NotificationPreferenceUpdate(PushNotificationBaseSchema):
    """Schema for updating notification preferences."""

    push_notifications_enabled: Optional[bool] = Field(None, description="Global toggle")
    authentication_notifications: Optional[NotificationFrequency] = Field(None, description="Auth frequency")
    booking_notifications: Optional[NotificationFrequency] = Field(None, description="Booking frequency")
    payment_notifications: Optional[NotificationFrequency] = Field(None, description="Payment frequency")
    promotional_notifications: Optional[NotificationFrequency] = Field(None, description="Promo frequency")
    system_notifications: Optional[NotificationFrequency] = Field(None, description="System frequency")
    security_notifications: Optional[NotificationFrequency] = Field(None, description="Security frequency")
    social_notifications: Optional[NotificationFrequency] = Field(None, description="Social frequency")
    reminder_notifications: Optional[NotificationFrequency] = Field(None, description="Reminder frequency")
    dnd_enabled: Optional[bool] = Field(None, description="DND enabled")
    dnd_start_time: Optional[str] = Field(None, description="DND start time")
    dnd_end_time: Optional[str] = Field(None, description="DND end time")
    dnd_timezone: Optional[str] = Field(None, description="DND timezone")
    ios_badge_count: Optional[bool] = Field(None, description="iOS badge count")
    android_vibration: Optional[bool] = Field(None, description="Android vibration")
    sound_enabled: Optional[bool] = Field(None, description="Sound enabled")
    language_code: Optional[str] = Field(None, description="Language code")
    max_daily_notifications: Optional[int] = Field(None, description="Max daily notifications")


class NotificationPreferenceResponse(NotificationPreferenceBase, TimestampMixin):
    """Schema for notification preference response."""

    id: UUID = Field(description="Preference unique identifier")
    user_id: int = Field(description="Associated user ID")
    preference_metadata: Dict[str, Any] = Field(description="Additional preference metadata")


# FCM Schemas
class FCMMessageRequest(PushNotificationBaseSchema):
    """Schema for FCM message request."""

    token: str = Field(description="FCM device token")

    notification: Optional[Dict[str, Any]] = Field(
        None,
        description="FCM notification payload",
        example={"title": "Booking Confirmed", "body": "Your booking has been confirmed!", "image": "https://example.com/image.jpg"}
    )

    data: Optional[Dict[str, Any]] = Field(
        None,
        description="FCM data payload",
        example={"booking_id": "12345", "action": "view_booking"}
    )

    android: Optional[Dict[str, Any]] = Field(None, description="Android-specific configuration")
    apns: Optional[Dict[str, Any]] = Field(None, description="APNs-specific configuration")
    webpush: Optional[Dict[str, Any]] = Field(None, description="WebPush-specific configuration")


class FCMMessageResponse(PushNotificationBaseSchema):
    """Schema for FCM message response."""

    name: str = Field(description="FCM message name/ID")
    success: bool = Field(description="Whether message was sent successfully")
    error: Optional[Dict[str, Any]] = Field(None, description="Error details if failed")


class FCMBatchRequest(PushNotificationBaseSchema):
    """Schema for FCM batch request."""

    messages: List[FCMMessageRequest] = Field(
        description="List of FCM messages", min_length=1, max_length=500
    )


class FCMBatchResponse(PushNotificationBaseSchema):
    """Schema for FCM batch response."""

    responses: List[FCMMessageResponse] = Field(description="List of FCM responses")
    success_count: int = Field(description="Number of successful sends")
    failure_count: int = Field(description="Number of failed sends")


# All schemas are defined in this file for Phase 2 completion