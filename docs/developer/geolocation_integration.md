# Geolocation Integration Developer Guide

## 🚀 Complete Integration Walkthrough

This guide provides comprehensive instructions for integrating Culture Connect's geolocation-enhanced payment system into your applications. Follow this systematic approach to implement intelligent payment routing with VPN detection and analytics.

## Prerequisites

### **System Requirements**
- **Python**: 3.9+ with FastAPI framework
- **Database**: PostgreSQL 13+ with PostGIS extension
- **Cache**: Redis 6+ for geolocation caching
- **External Services**: MaxMind GeoIP database access
- **Network**: HTTPS-enabled environment for webhook handling

### **Required Dependencies**
```bash
# Core geolocation dependencies
pip install geoip2>=3.0.0
pip install maxminddb>=2.2.0
pip install redis>=4.5.0
pip install httpx>=0.24.0

# Payment provider SDKs
pip install stripe>=5.5.0
pip install paystack>=1.6.0
pip install requests>=2.31.0  # For Busha integration

# Analytics and monitoring
pip install prometheus-client>=0.16.0
pip install sentry-sdk[fastapi]>=1.32.0
```

### **Environment Configuration**
```bash
# Geolocation Configuration
MAXMIND_LICENSE_KEY=your_maxmind_license_key
MAXMIND_DATABASE_PATH=/app/data/GeoLite2-Country.mmdb
GEOLOCATION_CACHE_TTL=3600
GEOLOCATION_TIMEOUT_SECONDS=5.0

# VPN Detection Configuration
VPN_DETECTION_ENABLED=true
VPN_CONFIDENCE_THRESHOLD=0.7
VPN_CACHE_TTL=1800
IP_REPUTATION_API_KEY=your_ip_reputation_key

# Payment Provider Configuration
# TODO-PAYSTACK-API-URL=https://api.paystack.co
# TODO-STRIPE-API-URL=https://api.stripe.com
# TODO-BUSHA-API-URL=https://api.busha.co
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret
STRIPE_SECRET_KEY=sk_test_your_stripe_secret
BUSHA_API_KEY=your_busha_api_key

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_GEOLOCATION_DB=1
REDIS_VPN_DETECTION_DB=2

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90
PERFORMANCE_MONITORING_ENABLED=true
```

## Core Integration Components

### **1. Geolocation Service Integration**

#### **Basic Setup**
```python
from app.services.geolocation_service import get_geolocation_service
from app.core.config import settings

# Initialize geolocation service
geolocation_service = get_geolocation_service()

# Basic IP detection
async def detect_user_location(ip_address: str):
    """
    Detect user location from IP address.

    Returns:
        GeolocationResult with country, confidence, and metadata
    """
    try:
        result = await geolocation_service.detect_country_from_ip(ip_address)
        return {
            "country_code": result.country_code,
            "country_name": result.country_name,
            "confidence": result.confidence_score,
            "detection_time_ms": result.detection_time_ms
        }
    except Exception as e:
        # Fallback to default routing
        return {"country_code": "US", "confidence": 0.0, "fallback": True}
```

#### **Advanced Integration with VPN Detection**
```python
from app.services.vpn_detection_service import VPNDetectionService

async def enhanced_location_detection(ip_address: str, user_id: Optional[int] = None):
    """
    Enhanced location detection with VPN analysis.

    Performance Target: <100ms
    """
    # Basic geolocation
    geo_result = await geolocation_service.detect_country_from_ip(ip_address)

    if not geo_result:
        return None

    # VPN detection if enabled
    if settings.VPN_DETECTION_ENABLED:
        vpn_service = VPNDetectionService()
        enhanced_result = await vpn_service.detect_vpn_proxy(
            ip_address=ip_address,
            geolocation_result=geo_result,
            user_id=user_id
        )
        return enhanced_result

    return geo_result
```

### **2. Payment Provider Selection**

#### **Smart Provider Routing**
```python
from app.services.payment_service import PaymentService
from app.core.payment.config import PaymentProviderType

async def select_optimal_provider(
    country_code: str,
    currency: str,
    crypto_currency: Optional[str] = None,
    user_preferences: Optional[dict] = None,
    geolocation_result: Optional[dict] = None
) -> dict:
    """
    Implement 4-priority routing system for provider selection.

    Priority 1: Cryptocurrency → Busha
    Priority 2: User Preference → Validated against geo-compatibility
    Priority 3: Geolocation-Based → Country-specific optimization
    Priority 4: Currency-Based Fallback → Traditional routing
    """

    # Priority 1: Cryptocurrency
    if crypto_currency:
        return {
            "provider": PaymentProviderType.BUSHA,
            "reason": "cryptocurrency_payment",
            "confidence": 0.95,
            "routing_priority": 1
        }

    # Priority 2: User Preference
    if user_preferences and user_preferences.get("preferred_provider"):
        preferred = user_preferences["preferred_provider"]
        if await validate_provider_compatibility(preferred, country_code):
            return {
                "provider": preferred,
                "reason": "user_preference",
                "confidence": 0.90,
                "routing_priority": 2
            }

    # Priority 3: Geolocation-Based
    if country_code:
        provider = get_optimal_provider_for_country(country_code)
        return {
            "provider": provider,
            "reason": "geolocation_optimized",
            "confidence": 0.85,
            "routing_priority": 3
        }

    # Priority 4: Currency-Based Fallback
    provider = get_provider_for_currency(currency)
    return {
        "provider": provider,
        "reason": "currency_fallback",
        "confidence": 0.70,
        "routing_priority": 4
    }

def get_optimal_provider_for_country(country_code: str) -> PaymentProviderType:
    """Map countries to optimal payment providers."""

    # African markets → Paystack
    african_countries = {
        "NG", "GH", "KE", "ZA", "UG", "TZ", "RW", "SN", "CI", "BF", "ML", "NE"
    }

    # Diaspora markets → Stripe
    diaspora_countries = {
        "US", "UK", "CA", "AU", "DE", "FR", "IT", "ES", "NL", "BE", "SE", "NO", "DK"
    }

    if country_code in african_countries:
        return PaymentProviderType.PAYSTACK
    elif country_code in diaspora_countries:
        return PaymentProviderType.STRIPE
    else:
        # Default to Stripe for unknown countries
        return PaymentProviderType.STRIPE
```

### **3. Complete Payment Flow Integration**

#### **Enhanced Payment Creation**
```python
from fastapi import Request
from app.schemas.payment_schemas import PaymentCreate, PaymentResponse

async def create_geolocation_enhanced_payment(
    payment_data: PaymentCreate,
    request: Request,
    current_user: User,
    db: AsyncSession
) -> PaymentResponse:
    """
    Create payment with full geolocation enhancement.

    Performance Target: <500ms end-to-end
    """
    start_time = time.perf_counter()

    try:
        # Step 1: Extract client IP
        client_ip = get_client_ip(request)

        # Step 2: Enhanced location detection
        geo_result = await enhanced_location_detection(
            ip_address=client_ip,
            user_id=current_user.id
        )

        # Step 3: Provider selection
        provider_info = await select_optimal_provider(
            country_code=geo_result.country_code if geo_result else "US",
            currency=payment_data.currency,
            crypto_currency=getattr(payment_data, 'crypto_currency', None),
            user_preferences=await get_user_payment_preferences(current_user.id),
            geolocation_result=geo_result
        )

        # Step 4: Create payment with geolocation metadata
        payment_dict = payment_data.dict()
        payment_dict.update({
            "user_id": current_user.id,
            "provider": provider_info["provider"],
            "geolocation_metadata": {
                "detected_country": geo_result.country_code if geo_result else None,
                "country_name": geo_result.country_name if geo_result else None,
                "routing_decision": provider_info["reason"],
                "provider_selection_reason": provider_info.get("detailed_reason"),
                "detection_confidence": geo_result.confidence_score if geo_result else 0.0,
                "detection_time_ms": geo_result.detection_time_ms if geo_result else 0,
                "vpn_detected": getattr(geo_result, 'is_vpn_detected', False),
                "risk_score": getattr(geo_result, 'risk_score', 0.0)
            }
        })

        # Step 5: Initialize payment with selected provider
        payment_service = PaymentService(db)
        payment = await payment_service.create_payment(payment_dict)

        # Step 6: Record analytics
        await record_payment_analytics(payment, geo_result, provider_info)

        # Step 7: Performance logging
        total_time_ms = (time.perf_counter() - start_time) * 1000
        logger.info(
            f"Geolocation-enhanced payment created",
            extra={
                "payment_id": payment.id,
                "provider": provider_info["provider"],
                "country": geo_result.country_code if geo_result else "unknown",
                "total_time_ms": total_time_ms,
                "vpn_detected": getattr(geo_result, 'is_vpn_detected', False)
            }
        )

        return PaymentResponse.from_orm(payment)

    except Exception as e:
        logger.error(f"Geolocation-enhanced payment creation failed: {str(e)}")
        # Fallback to standard payment creation
        return await create_standard_payment(payment_data, current_user, db)

def get_client_ip(request: Request) -> str:
    """Extract client IP with proxy support."""
    # Check for forwarded headers (load balancer/proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    # Check for real IP header
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fallback to direct connection
    return request.client.host
```

## Advanced Integration Patterns

### **4. Caching Strategy**

#### **Redis-Based Geolocation Caching**
```python
import redis.asyncio as redis
from app.core.cache import get_redis_client

class GeolocationCache:
    """High-performance geolocation caching."""

    def __init__(self):
        self.redis = get_redis_client(db=settings.REDIS_GEOLOCATION_DB)
        self.ttl = settings.GEOLOCATION_CACHE_TTL

    async def get_cached_location(self, ip_address: str) -> Optional[dict]:
        """Get cached geolocation result."""
        cache_key = f"geo:{ip_address}"
        cached_data = await self.redis.get(cache_key)

        if cached_data:
            return json.loads(cached_data)
        return None

    async def cache_location(self, ip_address: str, geo_result: dict):
        """Cache geolocation result with TTL."""
        cache_key = f"geo:{ip_address}"
        await self.redis.setex(
            cache_key,
            self.ttl,
            json.dumps(geo_result, default=str)
        )

    async def get_cache_stats(self) -> dict:
        """Get cache performance statistics."""
        info = await self.redis.info("stats")
        return {
            "cache_hits": info.get("keyspace_hits", 0),
            "cache_misses": info.get("keyspace_misses", 0),
            "hit_rate": info.get("keyspace_hits", 0) / max(
                info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1
            )
        }

# Usage in geolocation service
async def cached_location_detection(ip_address: str) -> dict:
    """Location detection with caching."""
    cache = GeolocationCache()

    # Try cache first
    cached_result = await cache.get_cached_location(ip_address)
    if cached_result:
        return cached_result

    # Perform detection
    geo_result = await geolocation_service.detect_country_from_ip(ip_address)

    # Cache result
    if geo_result:
        await cache.cache_location(ip_address, geo_result.dict())

    return geo_result
```

### **5. Circuit Breaker Pattern**

#### **Resilient Geolocation Service**
```python
from app.core.circuit_breaker import CircuitBreaker

class ResilientGeolocationService:
    """Geolocation service with circuit breaker protection."""

    def __init__(self):
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=30,
            expected_exception=Exception
        )

    async def detect_with_fallback(self, ip_address: str) -> dict:
        """Location detection with circuit breaker and fallback."""
        try:
            # Try primary geolocation service
            return await self.circuit_breaker.call(
                self._primary_detection, ip_address
            )
        except Exception as e:
            logger.warning(f"Primary geolocation failed: {e}")
            # Fallback to secondary methods
            return await self._fallback_detection(ip_address)

    async def _primary_detection(self, ip_address: str) -> dict:
        """Primary MaxMind-based detection."""
        return await geolocation_service.detect_country_from_ip(ip_address)

    async def _fallback_detection(self, ip_address: str) -> dict:
        """Fallback detection methods."""
        # Try IP-API service as fallback
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"http://ip-api.com/json/{ip_address}",
                    timeout=2.0
                )
                data = response.json()

                if data.get("status") == "success":
                    return {
                        "country_code": data.get("countryCode"),
                        "country_name": data.get("country"),
                        "confidence_score": 0.7,  # Lower confidence for fallback
                        "detection_method": "ip_api_fallback"
                    }
        except Exception:
            pass

        # Ultimate fallback - use default routing
        return {
            "country_code": "US",
            "country_name": "United States",
            "confidence_score": 0.0,
            "detection_method": "default_fallback"
        }
```

### **6. Analytics Integration**

#### **Performance Monitoring**
```python
from app.services.geolocation_analytics_service import GeolocationAnalyticsService

async def record_payment_analytics(
    payment: Payment,
    geo_result: Optional[dict],
    provider_info: dict
):
    """Record comprehensive payment analytics."""
    analytics_service = GeolocationAnalyticsService(db)

    analytics_data = {
        "payment_id": payment.id,
        "country_code": geo_result.country_code if geo_result else None,
        "provider": provider_info["provider"],
        "routing_decision": provider_info["reason"],
        "detection_confidence": geo_result.confidence_score if geo_result else 0.0,
        "detection_time_ms": geo_result.detection_time_ms if geo_result else 0,
        "vpn_detected": getattr(geo_result, 'is_vpn_detected', False),
        "risk_score": getattr(geo_result, 'risk_score', 0.0),
        "amount": payment.amount,
        "currency": payment.currency,
        "created_at": payment.created_at
    }

    await analytics_service.record_payment_event(analytics_data)

async def get_performance_metrics() -> dict:
    """Get real-time performance metrics."""
    analytics_service = GeolocationAnalyticsService(db)

    return await analytics_service.get_performance_dashboard({
        "period_days": 7,
        "include_provider_breakdown": True,
        "include_geographic_analysis": True,
        "include_vpn_impact": True
    })
```

## Testing Integration

### **7. Unit Testing**

#### **Geolocation Service Tests**
```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_geolocation_detection():
    """Test basic geolocation detection."""
    # Mock MaxMind response
    with patch('app.services.geolocation_service.geoip2') as mock_geoip:
        mock_response = AsyncMock()
        mock_response.country.iso_code = "NG"
        mock_response.country.name = "Nigeria"
        mock_geoip.database.Reader.return_value.country.return_value = mock_response

        # Test detection
        result = await geolocation_service.detect_country_from_ip("************")

        assert result.country_code == "NG"
        assert result.country_name == "Nigeria"
        assert result.confidence_score > 0.9

@pytest.mark.asyncio
async def test_provider_selection_priority():
    """Test 4-priority provider selection."""
    # Test Priority 1: Cryptocurrency
    result = await select_optimal_provider(
        country_code="NG",
        currency="USD",
        crypto_currency="BTC"
    )
    assert result["provider"] == PaymentProviderType.BUSHA
    assert result["routing_priority"] == 1

    # Test Priority 3: Geolocation
    result = await select_optimal_provider(
        country_code="NG",
        currency="USD"
    )
    assert result["provider"] == PaymentProviderType.PAYSTACK
    assert result["routing_priority"] == 3

@pytest.mark.asyncio
async def test_vpn_detection():
    """Test VPN detection functionality."""
    vpn_service = VPNDetectionService()

    # Test known VPN IP
    result = await vpn_service.detect_vpn_proxy("*******")  # Google DNS (often used by VPNs)

    assert hasattr(result, 'is_vpn_detected')
    assert hasattr(result, 'confidence_score')
    assert hasattr(result, 'detection_method')
```

### **8. Integration Testing**

#### **End-to-End Payment Flow**
```python
@pytest.mark.asyncio
async def test_complete_payment_flow():
    """Test complete geolocation-enhanced payment flow."""
    # Setup test data
    payment_data = PaymentCreate(
        booking_id=123,
        amount=50000.00,
        currency="USD",
        return_url="https://test.example.com/return"
    )

    # Mock request with Nigerian IP
    mock_request = AsyncMock()
    mock_request.client.host = "************"
    mock_request.headers = {}

    # Create payment
    result = await create_geolocation_enhanced_payment(
        payment_data=payment_data,
        request=mock_request,
        current_user=test_user,
        db=test_db
    )

    # Verify geolocation routing
    assert result.provider == "paystack"  # Nigerian IP should route to Paystack
    assert result.geolocation_metadata["detected_country"] == "NG"
    assert result.geolocation_metadata["routing_decision"] == "geolocation_optimized"

@pytest.mark.asyncio
async def test_performance_targets():
    """Test performance targets are met."""
    start_time = time.perf_counter()

    # Test geolocation detection speed
    result = await geolocation_service.detect_country_from_ip("*******")
    detection_time = (time.perf_counter() - start_time) * 1000

    # Verify <100ms target
    assert detection_time < 100, f"Detection took {detection_time}ms, target is <100ms"

    # Test complete payment creation speed
    start_time = time.perf_counter()
    payment = await create_geolocation_enhanced_payment(test_payment_data, mock_request, test_user, test_db)
    total_time = (time.perf_counter() - start_time) * 1000

    # Verify <500ms target
    assert total_time < 500, f"Payment creation took {total_time}ms, target is <500ms"
```

---

## 📚 Related Developer Guides

- **[API Examples](api_examples.md)** - Comprehensive code examples and testing strategies
- **[Configuration Guide](configuration_guide.md)** - Environment setup and production deployment
- **[User Guides](../user_guide/geolocation_features.md)** - User-facing documentation
- **[API Documentation](../README.md)** - Complete API reference

---

**Last Updated**: January 15, 2024
**Version**: 2.4.3
**Part of**: Culture Connect Backend API Documentation v2.4.3
