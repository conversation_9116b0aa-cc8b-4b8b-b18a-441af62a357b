"""
Vendor Dashboard schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic V2 schemas for vendor dashboard functionality including:
- VendorDashboardMetrics schemas with performance tracking and KPI validation
- VendorActivityFeed schemas with activity logging and feed management
- VendorNotification schemas with priority-based alerts and notification management
- VendorQuickAction schemas with workflow optimization and action tracking
- VendorAnalytics schemas with comprehensive reporting and business intelligence
- VendorDashboardOverview schema for complete dashboard data aggregation

Implements Task 3.3.1 requirements with Pydantic V2 compliance using from_attributes
and json_schema_extra, comprehensive validation, and computed fields for enhanced
vendor dashboard experience and analytics.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, computed_field, field_validator, ConfigDict
from pydantic.types import NonNegativeInt, NonNegativeFloat, PositiveFloat

from app.schemas.base import BaseSchema, TimestampMixin
from app.models.vendor_dashboard import (
    MetricType, ActivityType, NotificationType, NotificationPriority, ActionType
)


# Base schemas for vendor dashboard components
class VendorDashboardMetricsBase(BaseModel):
    """Base schema for vendor dashboard metrics."""
    metric_type: MetricType = Field(..., description="Type of metric being tracked")
    metric_name: str = Field(..., max_length=100, description="Human-readable metric name")
    metric_description: Optional[str] = Field(None, description="Detailed metric description")
    current_value: NonNegativeFloat = Field(default=0.0, description="Current metric value")
    previous_value: NonNegativeFloat = Field(default=0.0, description="Previous period metric value")
    target_value: Optional[NonNegativeFloat] = Field(None, description="Target metric value")
    period_start: datetime = Field(..., description="Metric period start date")
    period_end: datetime = Field(..., description="Metric period end date")
    comparison_period_start: Optional[datetime] = Field(None, description="Comparison period start")
    comparison_period_end: Optional[datetime] = Field(None, description="Comparison period end")
    metric_data: Dict[str, Any] = Field(default_factory=dict, description="Additional metric data")
    breakdown_data: Dict[str, Any] = Field(default_factory=dict, description="Metric breakdown by category")
    performance_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Performance score (0-100)")
    benchmark_comparison: float = Field(default=0.0, description="Comparison to benchmark")
    market_position: Optional[NonNegativeInt] = Field(None, description="Market ranking position")

    @field_validator('period_end')
    @classmethod
    def validate_period_end(cls, v, info):
        """Validate that period_end is after period_start."""
        if 'period_start' in info.data and v <= info.data['period_start']:
            raise ValueError('Period end must be after period start')
        return v

    @field_validator('comparison_period_end')
    @classmethod
    def validate_comparison_period_end(cls, v, info):
        """Validate comparison period end if provided."""
        if v and 'comparison_period_start' in info.data and info.data['comparison_period_start']:
            if v <= info.data['comparison_period_start']:
                raise ValueError('Comparison period end must be after comparison period start')
        return v


class VendorActivityFeedBase(BaseModel):
    """Base schema for vendor activity feed."""
    activity_type: ActivityType = Field(..., description="Type of activity")
    activity_title: str = Field(..., max_length=200, description="Activity title")
    activity_description: Optional[str] = Field(None, description="Detailed activity description")
    related_entity_type: Optional[str] = Field(None, max_length=50, description="Related entity type")
    related_entity_id: Optional[UUID] = Field(None, description="Related entity ID")
    related_entity_data: Dict[str, Any] = Field(default_factory=dict, description="Related entity data")
    activity_data: Dict[str, Any] = Field(default_factory=dict, description="Activity metadata")
    impact_score: NonNegativeFloat = Field(default=0.0, ge=0, le=10, description="Business impact score (0-10)")
    visibility_level: str = Field(default="normal", pattern="^(high|normal|low)$", description="Activity visibility")
    user_action_required: bool = Field(default=False, description="Whether user action is required")
    action_deadline: Optional[datetime] = Field(None, description="Action deadline if required")
    occurred_at: datetime = Field(..., description="When the activity occurred")

    @field_validator('action_deadline')
    @classmethod
    def validate_action_deadline(cls, v, info):
        """Validate action deadline is in the future if user action is required."""
        if info.data.get('user_action_required') and v and v <= datetime.utcnow():
            raise ValueError('Action deadline must be in the future when action is required')
        return v


class VendorNotificationBase(BaseModel):
    """Base schema for vendor notifications."""
    notification_type: NotificationType = Field(..., description="Type of notification")
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM, description="Notification priority")
    title: str = Field(..., max_length=200, description="Notification title")
    message: str = Field(..., description="Notification message content")
    rich_content: Dict[str, Any] = Field(default_factory=dict, description="Rich content data")
    action_buttons: List[Dict[str, Any]] = Field(default_factory=list, description="Action buttons")
    action_url: Optional[str] = Field(None, max_length=500, description="Action URL or deep link")
    category: Optional[str] = Field(None, max_length=50, description="Notification category")
    tags: List[str] = Field(default_factory=list, description="Notification tags")
    is_actionable: bool = Field(default=False, description="Whether notification has actions")
    delivery_method: List[str] = Field(default_factory=list, description="Delivery methods")
    scheduled_for: Optional[datetime] = Field(None, description="Scheduled delivery time")
    expires_at: Optional[datetime] = Field(None, description="Notification expiration time")

    @field_validator('expires_at')
    @classmethod
    def validate_expires_at(cls, v, info):
        """Validate expiration is after scheduled time."""
        if v and 'scheduled_for' in info.data and info.data['scheduled_for']:
            if v <= info.data['scheduled_for']:
                raise ValueError('Expiration time must be after scheduled time')
        return v

    @field_validator('delivery_method')
    @classmethod
    def validate_delivery_method(cls, v):
        """Validate delivery methods."""
        valid_methods = {'push', 'email', 'sms', 'in_app'}
        for method in v:
            if method not in valid_methods:
                raise ValueError(f'Invalid delivery method: {method}. Must be one of {valid_methods}')
        return v


class VendorQuickActionBase(BaseModel):
    """Base schema for vendor quick actions."""
    action_type: ActionType = Field(..., description="Type of quick action")
    action_title: str = Field(..., max_length=200, description="Action title")
    action_description: Optional[str] = Field(None, description="Action description")
    action_url: Optional[str] = Field(None, max_length=500, description="Action URL")
    action_data: Dict[str, Any] = Field(default_factory=dict, description="Action parameters")
    estimated_impact: str = Field(default="medium", pattern="^(low|medium|high)$", description="Estimated impact")
    estimated_time_minutes: NonNegativeInt = Field(default=5, description="Estimated completion time")
    priority_score: PositiveFloat = Field(default=5.0, ge=1, le=10, description="Priority score (1-10)")
    urgency_level: str = Field(default="normal", pattern="^(low|normal|high|urgent)$", description="Urgency level")
    potential_revenue_impact: NonNegativeFloat = Field(default=0.0, description="Potential revenue impact")
    potential_booking_impact: NonNegativeInt = Field(default=0, description="Potential booking impact")
    potential_ranking_impact: float = Field(default=0.0, description="Potential ranking impact")
    suggested_completion_date: Optional[datetime] = Field(None, description="Suggested completion date")
    expires_at: Optional[datetime] = Field(None, description="Action expiration time")

    @field_validator('expires_at')
    @classmethod
    def validate_expires_at(cls, v, info):
        """Validate expiration is after suggested completion date."""
        if v and 'suggested_completion_date' in info.data and info.data['suggested_completion_date']:
            if v <= info.data['suggested_completion_date']:
                raise ValueError('Expiration time must be after suggested completion date')
        return v


class VendorAnalyticsBase(BaseModel):
    """Base schema for vendor analytics."""
    analytics_type: str = Field(..., max_length=50, description="Analytics type (daily, weekly, monthly, quarterly)")
    period_start: datetime = Field(..., description="Analytics period start")
    period_end: datetime = Field(..., description="Analytics period end")

    # Revenue analytics
    total_revenue: NonNegativeFloat = Field(default=0.0, description="Total revenue for period")
    average_order_value: NonNegativeFloat = Field(default=0.0, description="Average order value")
    revenue_growth_rate: float = Field(default=0.0, description="Revenue growth rate percentage")
    revenue_by_service: Dict[str, float] = Field(default_factory=dict, description="Revenue breakdown by service")
    revenue_by_month: Dict[str, float] = Field(default_factory=dict, description="Revenue by month")

    # Booking analytics
    total_bookings: NonNegativeInt = Field(default=0, description="Total bookings")
    confirmed_bookings: NonNegativeInt = Field(default=0, description="Confirmed bookings")
    cancelled_bookings: NonNegativeInt = Field(default=0, description="Cancelled bookings")
    booking_conversion_rate: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Booking conversion rate")
    average_booking_value: NonNegativeFloat = Field(default=0.0, description="Average booking value")
    booking_trends: Dict[str, Any] = Field(default_factory=dict, description="Booking trend data")

    # Service performance
    total_services: NonNegativeInt = Field(default=0, description="Total services")
    active_services: NonNegativeInt = Field(default=0, description="Active services")
    service_views: NonNegativeInt = Field(default=0, description="Service views")
    service_inquiries: NonNegativeInt = Field(default=0, description="Service inquiries")
    service_conversion_rate: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Service conversion rate")
    top_performing_services: List[Dict[str, Any]] = Field(default_factory=list, description="Top performing services")

    # Quality metrics
    average_rating: NonNegativeFloat = Field(default=0.0, ge=0, le=5, description="Average rating")
    total_reviews: NonNegativeInt = Field(default=0, description="Total reviews")
    content_quality_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Content quality score")
    content_moderation_status: Dict[str, Any] = Field(default_factory=dict, description="Content moderation status")
    seo_performance_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="SEO performance score")

    # Marketplace performance
    marketplace_ranking: Optional[NonNegativeInt] = Field(None, description="Marketplace ranking")
    visibility_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Visibility score")
    search_impressions: NonNegativeInt = Field(default=0, description="Search impressions")
    search_clicks: NonNegativeInt = Field(default=0, description="Search clicks")
    search_ctr: NonNegativeFloat = Field(default=0.0, ge=0, le=100, description="Search click-through rate")
    featured_placements: NonNegativeInt = Field(default=0, description="Featured placements")

    @field_validator('period_end')
    @classmethod
    def validate_period_end(cls, v, info):
        """Validate that period_end is after period_start."""
        if 'period_start' in info.data and v <= info.data['period_start']:
            raise ValueError('Period end must be after period start')
        return v

    @field_validator('analytics_type')
    @classmethod
    def validate_analytics_type(cls, v):
        """Validate analytics type."""
        valid_types = {'daily', 'weekly', 'monthly', 'quarterly', 'yearly'}
        if v not in valid_types:
            raise ValueError(f'Invalid analytics type: {v}. Must be one of {valid_types}')
        return v

    @field_validator('confirmed_bookings')
    @classmethod
    def validate_confirmed_bookings(cls, v, info):
        """Validate confirmed bookings don't exceed total bookings."""
        if 'total_bookings' in info.data and v > info.data['total_bookings']:
            raise ValueError('Confirmed bookings cannot exceed total bookings')
        return v

    @field_validator('cancelled_bookings')
    @classmethod
    def validate_cancelled_bookings(cls, v, info):
        """Validate cancelled bookings don't exceed total bookings."""
        if 'total_bookings' in info.data and v > info.data['total_bookings']:
            raise ValueError('Cancelled bookings cannot exceed total bookings')
        return v


# Create schemas for vendor dashboard metrics
class VendorDashboardMetricsCreate(VendorDashboardMetricsBase):
    """Schema for creating vendor dashboard metrics."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "metric_type": "revenue",
                "metric_name": "Monthly Revenue",
                "metric_description": "Total revenue for the current month",
                "current_value": 15000.0,
                "previous_value": 12000.0,
                "target_value": 18000.0,
                "period_start": "2025-01-01T00:00:00Z",
                "period_end": "2025-01-31T23:59:59Z",
                "performance_score": 85.5,
                "benchmark_comparison": 12.5,
                "market_position": 15
            }
        }
    )


class VendorDashboardMetricsUpdate(BaseModel):
    """Schema for updating vendor dashboard metrics."""
    current_value: Optional[NonNegativeFloat] = None
    target_value: Optional[NonNegativeFloat] = None
    metric_data: Optional[Dict[str, Any]] = None
    breakdown_data: Optional[Dict[str, Any]] = None
    performance_score: Optional[NonNegativeFloat] = Field(None, ge=0, le=100)
    benchmark_comparison: Optional[float] = None
    market_position: Optional[NonNegativeInt] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "current_value": 16500.0,
                "performance_score": 88.2,
                "benchmark_comparison": 15.3
            }
        }
    )


class VendorDashboardMetricsResponse(VendorDashboardMetricsBase, TimestampMixin):
    """Schema for vendor dashboard metrics response."""
    id: UUID = Field(..., description="Metric ID")
    vendor_id: UUID = Field(..., description="Vendor ID")
    percentage_change: float = Field(default=0.0, description="Percentage change from previous period")
    trend_direction: str = Field(default="stable", description="Trend direction")
    calculated_at: datetime = Field(..., description="When metric was calculated")
    is_active: bool = Field(default=True, description="Whether metric is active")

    @computed_field
    @property
    def performance_indicator(self) -> str:
        """Get performance indicator based on score."""
        if self.performance_score >= 90:
            return "excellent"
        elif self.performance_score >= 75:
            return "good"
        elif self.performance_score >= 60:
            return "average"
        elif self.performance_score >= 40:
            return "below_average"
        else:
            return "poor"

    @computed_field
    @property
    def target_achievement_rate(self) -> Optional[float]:
        """Calculate target achievement rate."""
        if self.target_value and self.target_value > 0:
            return (self.current_value / self.target_value) * 100
        return None

    @computed_field
    @property
    def improvement_needed(self) -> Optional[float]:
        """Calculate improvement needed to reach target."""
        if self.target_value and self.target_value > self.current_value:
            return self.target_value - self.current_value
        return None

    model_config = ConfigDict(from_attributes=True)


# Create schemas for vendor activity feed
class VendorActivityFeedCreate(VendorActivityFeedBase):
    """Schema for creating vendor activity feed entries."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "activity_type": "service_created",
                "activity_title": "New Photography Service Created",
                "activity_description": "Professional wedding photography service has been created and is pending approval",
                "related_entity_type": "service",
                "related_entity_id": "456e7890-e89b-12d3-a456-************",
                "impact_score": 7.5,
                "visibility_level": "high",
                "user_action_required": True,
                "occurred_at": "2025-01-15T10:30:00Z"
            }
        }
    )


class VendorActivityFeedUpdate(BaseModel):
    """Schema for updating vendor activity feed entries."""
    is_read: Optional[bool] = None
    is_important: Optional[bool] = None
    is_archived: Optional[bool] = None
    action_completed: Optional[bool] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "is_read": True,
                "action_completed": True
            }
        }
    )


class VendorActivityFeedResponse(VendorActivityFeedBase, TimestampMixin):
    """Schema for vendor activity feed response."""
    id: UUID = Field(..., description="Activity ID")
    vendor_id: UUID = Field(..., description="Vendor ID")
    is_read: bool = Field(default=False, description="Whether activity is read")
    is_important: bool = Field(default=False, description="Whether activity is important")
    is_archived: bool = Field(default=False, description="Whether activity is archived")
    action_completed: bool = Field(default=False, description="Whether required action is completed")

    @computed_field
    @property
    def time_since_occurred(self) -> str:
        """Get human-readable time since activity occurred."""
        now = datetime.utcnow()
        diff = now - self.occurred_at

        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"

    @computed_field
    @property
    def urgency_indicator(self) -> str:
        """Get urgency indicator based on action deadline."""
        if not self.user_action_required or not self.action_deadline:
            return "none"

        now = datetime.utcnow()
        time_left = self.action_deadline - now

        if time_left.total_seconds() <= 0:
            return "overdue"
        elif time_left.days == 0 and time_left.seconds <= 3600:  # 1 hour
            return "urgent"
        elif time_left.days == 0:
            return "today"
        elif time_left.days <= 1:
            return "tomorrow"
        else:
            return "normal"

    model_config = ConfigDict(from_attributes=True)


# Create schemas for vendor notifications
class VendorNotificationCreate(VendorNotificationBase):
    """Schema for creating vendor notifications."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "notification_type": "success",
                "priority": "high",
                "title": "Service Approved",
                "message": "Your photography service has been approved and is now live on the marketplace",
                "category": "content",
                "is_actionable": True,
                "action_url": "/vendor/services/456e7890-e89b-12d3-a456-************",
                "delivery_method": ["push", "in_app"]
            }
        }
    )


class VendorNotificationUpdate(BaseModel):
    """Schema for updating vendor notifications."""
    is_read: Optional[bool] = None
    is_dismissed: Optional[bool] = None
    action_taken: Optional[bool] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "is_read": True,
                "action_taken": True
            }
        }
    )


class VendorNotificationResponse(VendorNotificationBase, TimestampMixin):
    """Schema for vendor notification response."""
    id: UUID = Field(..., description="Notification ID")
    vendor_id: UUID = Field(..., description="Vendor ID")
    is_read: bool = Field(default=False, description="Whether notification is read")
    is_dismissed: bool = Field(default=False, description="Whether notification is dismissed")
    action_taken: bool = Field(default=False, description="Whether action was taken")
    delivered_at: Optional[datetime] = Field(None, description="When notification was delivered")
    read_at: Optional[datetime] = Field(None, description="When notification was read")
    action_taken_at: Optional[datetime] = Field(None, description="When action was taken")

    @computed_field
    @property
    def is_expired(self) -> bool:
        """Check if notification is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    @computed_field
    @property
    def priority_score(self) -> int:
        """Get numeric priority score for sorting."""
        priority_scores = {
            NotificationPriority.CRITICAL: 4,
            NotificationPriority.HIGH: 3,
            NotificationPriority.MEDIUM: 2,
            NotificationPriority.LOW: 1
        }
        return priority_scores.get(self.priority, 2)

    @computed_field
    @property
    def requires_attention(self) -> bool:
        """Check if notification requires immediate attention."""
        return (
            not self.is_read and
            not self.is_dismissed and
            self.priority in [NotificationPriority.HIGH, NotificationPriority.CRITICAL] and
            not self.is_expired
        )

    model_config = ConfigDict(from_attributes=True)


# Create schemas for vendor quick actions
class VendorQuickActionCreate(VendorQuickActionBase):
    """Schema for creating vendor quick actions."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "action_type": "optimize_service",
                "action_title": "Optimize Service Listing",
                "action_description": "Improve your service description and add more photos to increase visibility",
                "estimated_impact": "high",
                "estimated_time_minutes": 15,
                "priority_score": 8.5,
                "urgency_level": "high",
                "potential_revenue_impact": 500.0,
                "potential_booking_impact": 3,
                "action_url": "/vendor/services/optimize/456e7890-e89b-12d3-a456-************"
            }
        }
    )


class VendorQuickActionUpdate(BaseModel):
    """Schema for updating vendor quick actions."""
    is_completed: Optional[bool] = None
    is_dismissed: Optional[bool] = None
    completion_method: Optional[str] = Field(None, max_length=50)
    completion_notes: Optional[str] = None
    actual_impact: Optional[Dict[str, Any]] = None
    effectiveness_score: Optional[NonNegativeFloat] = Field(None, ge=0, le=10)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "is_completed": True,
                "completion_method": "manual",
                "completion_notes": "Added 5 new photos and improved description",
                "effectiveness_score": 8.2
            }
        }
    )


class VendorQuickActionResponse(VendorQuickActionBase, TimestampMixin):
    """Schema for vendor quick action response."""
    id: UUID = Field(..., description="Action ID")
    vendor_id: UUID = Field(..., description="Vendor ID")
    is_active: bool = Field(default=True, description="Whether action is active")
    is_completed: bool = Field(default=False, description="Whether action is completed")
    is_dismissed: bool = Field(default=False, description="Whether action is dismissed")
    completed_at: Optional[datetime] = Field(None, description="When action was completed")
    completion_method: Optional[str] = Field(None, description="How action was completed")
    completion_notes: Optional[str] = Field(None, description="Completion notes")
    actual_impact: Dict[str, Any] = Field(default_factory=dict, description="Actual impact achieved")
    effectiveness_score: Optional[float] = Field(None, description="Effectiveness score")

    @computed_field
    @property
    def impact_level(self) -> str:
        """Get impact level based on potential impacts."""
        revenue_impact = self.potential_revenue_impact or 0
        booking_impact = self.potential_booking_impact or 0

        if revenue_impact >= 1000 or booking_impact >= 5:
            return "high"
        elif revenue_impact >= 500 or booking_impact >= 3:
            return "medium"
        else:
            return "low"

    @computed_field
    @property
    def completion_status(self) -> str:
        """Get completion status."""
        if self.is_completed:
            return "completed"
        elif self.is_dismissed:
            return "dismissed"
        elif self.expires_at and datetime.utcnow() > self.expires_at:
            return "expired"
        else:
            return "pending"

    @computed_field
    @property
    def roi_estimate(self) -> Optional[float]:
        """Calculate estimated ROI based on time investment."""
        if self.estimated_time_minutes > 0 and self.potential_revenue_impact > 0:
            # Assume hourly rate of 50 for calculation
            time_cost = (self.estimated_time_minutes / 60) * 50
            return (self.potential_revenue_impact / time_cost) * 100 if time_cost > 0 else None
        return None

    model_config = ConfigDict(from_attributes=True)


# Create schemas for vendor analytics
class VendorAnalyticsCreate(VendorAnalyticsBase):
    """Schema for creating vendor analytics."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "analytics_type": "monthly",
                "period_start": "2025-01-01T00:00:00Z",
                "period_end": "2025-01-31T23:59:59Z",
                "total_revenue": 15000.0,
                "total_bookings": 25,
                "confirmed_bookings": 23,
                "booking_conversion_rate": 85.5,
                "average_rating": 4.7,
                "total_reviews": 18,
                "content_quality_score": 88.2,
                "marketplace_ranking": 15,
                "visibility_score": 82.3
            }
        }
    )


class VendorAnalyticsUpdate(BaseModel):
    """Schema for updating vendor analytics."""
    total_revenue: Optional[NonNegativeFloat] = None
    total_bookings: Optional[NonNegativeInt] = None
    confirmed_bookings: Optional[NonNegativeInt] = None
    average_rating: Optional[NonNegativeFloat] = Field(None, ge=0, le=5)
    content_quality_score: Optional[NonNegativeFloat] = Field(None, ge=0, le=100)
    marketplace_ranking: Optional[NonNegativeInt] = None
    visibility_score: Optional[NonNegativeFloat] = Field(None, ge=0, le=100)
    key_insights: Optional[List[Dict[str, Any]]] = None
    improvement_recommendations: Optional[List[Dict[str, Any]]] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_revenue": 16500.0,
                "confirmed_bookings": 25,
                "average_rating": 4.8,
                "content_quality_score": 90.1
            }
        }
    )


class VendorAnalyticsResponse(VendorAnalyticsBase, TimestampMixin):
    """Schema for vendor analytics response."""
    id: UUID = Field(..., description="Analytics ID")
    vendor_id: UUID = Field(..., description="Vendor ID")

    # Additional analytics fields
    customer_retention_rate: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    customer_lifetime_value: NonNegativeFloat = Field(default=0.0)
    customer_satisfaction_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Competitive analysis
    market_share: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    competitive_position: Optional[str] = Field(None)
    price_competitiveness: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    service_differentiation_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Operational metrics
    response_time_hours: NonNegativeFloat = Field(default=0.0)
    completion_rate: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    on_time_delivery_rate: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    communication_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Financial health
    profit_margin: float = Field(default=0.0)
    cost_per_acquisition: NonNegativeFloat = Field(default=0.0)
    return_on_investment: float = Field(default=0.0)
    cash_flow_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Growth indicators
    growth_rate: float = Field(default=0.0)
    growth_trajectory: str = Field(default="stable")
    market_opportunity_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    expansion_potential: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Predictive analytics
    forecasted_revenue: NonNegativeFloat = Field(default=0.0)
    forecasted_bookings: NonNegativeInt = Field(default=0)
    risk_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    churn_probability: NonNegativeFloat = Field(default=0.0, ge=0, le=100)

    # Recommendations and insights
    key_insights: List[Dict[str, Any]] = Field(default_factory=list)
    improvement_recommendations: List[Dict[str, Any]] = Field(default_factory=list)
    growth_opportunities: List[Dict[str, Any]] = Field(default_factory=list)
    risk_factors: List[Dict[str, Any]] = Field(default_factory=list)

    # Data quality
    data_completeness_score: NonNegativeFloat = Field(default=0.0, ge=0, le=100)
    last_updated: datetime = Field(..., description="When analytics were last updated")
    calculation_duration_ms: NonNegativeInt = Field(default=0)
    is_active: bool = Field(default=True)

    @computed_field
    @property
    def overall_performance_score(self) -> float:
        """Calculate overall performance score."""
        scores = [
            self.booking_conversion_rate,
            self.service_conversion_rate,
            (self.average_rating / 5) * 100,
            self.content_quality_score,
            self.visibility_score,
            self.completion_rate,
            self.customer_satisfaction_score
        ]
        valid_scores = [score for score in scores if score > 0]
        return sum(valid_scores) / len(valid_scores) if valid_scores else 0.0

    @computed_field
    @property
    def business_health_indicator(self) -> str:
        """Get business health indicator."""
        score = self.overall_performance_score
        if score >= 85:
            return "excellent"
        elif score >= 70:
            return "good"
        elif score >= 55:
            return "average"
        elif score >= 40:
            return "needs_improvement"
        else:
            return "critical"

    @computed_field
    @property
    def revenue_per_booking(self) -> float:
        """Calculate revenue per booking."""
        if self.confirmed_bookings > 0:
            return self.total_revenue / self.confirmed_bookings
        return 0.0

    @computed_field
    @property
    def cancellation_rate(self) -> float:
        """Calculate booking cancellation rate."""
        if self.total_bookings > 0:
            return (self.cancelled_bookings / self.total_bookings) * 100
        return 0.0

    model_config = ConfigDict(from_attributes=True)


# Comprehensive dashboard overview schema
class VendorDashboardOverview(BaseModel):
    """Comprehensive vendor dashboard overview schema."""
    vendor_id: UUID = Field(..., description="Vendor ID")

    # Key metrics summary
    key_metrics: List[VendorDashboardMetricsResponse] = Field(default_factory=list, description="Key performance metrics")

    # Recent activity
    recent_activities: List[VendorActivityFeedResponse] = Field(default_factory=list, description="Recent activities")

    # Notifications
    unread_notifications: List[VendorNotificationResponse] = Field(default_factory=list, description="Unread notifications")
    urgent_notifications: List[VendorNotificationResponse] = Field(default_factory=list, description="Urgent notifications")

    # Quick actions
    recommended_actions: List[VendorQuickActionResponse] = Field(default_factory=list, description="Recommended quick actions")
    high_priority_actions: List[VendorQuickActionResponse] = Field(default_factory=list, description="High priority actions")

    # Analytics summary
    current_analytics: Optional[VendorAnalyticsResponse] = Field(None, description="Current period analytics")
    previous_analytics: Optional[VendorAnalyticsResponse] = Field(None, description="Previous period analytics")

    # Dashboard metadata
    last_updated: datetime = Field(..., description="When dashboard was last updated")
    data_freshness_minutes: NonNegativeInt = Field(default=0, description="Data freshness in minutes")

    @computed_field
    @property
    def notification_summary(self) -> Dict[str, int]:
        """Get notification summary counts."""
        return {
            "total_unread": len(self.unread_notifications),
            "urgent_count": len(self.urgent_notifications),
            "critical_count": len([n for n in self.urgent_notifications if n.priority == NotificationPriority.CRITICAL]),
            "actionable_count": len([n for n in self.unread_notifications if n.is_actionable])
        }

    @computed_field
    @property
    def action_summary(self) -> Dict[str, int]:
        """Get action summary counts."""
        return {
            "total_recommended": len(self.recommended_actions),
            "high_priority": len(self.high_priority_actions),
            "urgent_actions": len([a for a in self.recommended_actions if a.urgency_level == "urgent"]),
            "high_impact": len([a for a in self.recommended_actions if a.estimated_impact == "high"])
        }

    @computed_field
    @property
    def performance_trend(self) -> Optional[str]:
        """Get performance trend compared to previous period."""
        if not self.current_analytics or not self.previous_analytics:
            return None

        current_score = self.current_analytics.overall_performance_score
        previous_score = self.previous_analytics.overall_performance_score

        if current_score > previous_score + 5:
            return "improving"
        elif current_score < previous_score - 5:
            return "declining"
        else:
            return "stable"

    @computed_field
    @property
    def dashboard_health_score(self) -> float:
        """Calculate overall dashboard health score."""
        scores = []

        # Analytics score
        if self.current_analytics:
            scores.append(self.current_analytics.overall_performance_score)

        # Activity engagement score (based on recent activities)
        activity_score = min(len(self.recent_activities) * 10, 100)
        scores.append(activity_score)

        # Notification management score (lower unread count is better)
        notification_score = max(100 - len(self.unread_notifications) * 5, 0)
        scores.append(notification_score)

        # Action completion score (fewer pending actions is better)
        action_score = max(100 - len(self.recommended_actions) * 3, 0)
        scores.append(action_score)

        return sum(scores) / len(scores) if scores else 0.0

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "vendor_id": "123e4567-e89b-12d3-a456-************",
                "key_metrics": [],
                "recent_activities": [],
                "unread_notifications": [],
                "urgent_notifications": [],
                "recommended_actions": [],
                "high_priority_actions": [],
                "current_analytics": None,
                "previous_analytics": None,
                "last_updated": "2025-01-15T10:30:00Z",
                "data_freshness_minutes": 5
            }
        }
    )