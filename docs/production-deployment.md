# Culture Connect Backend - Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Culture Connect Backend to production environments. The deployment supports three strategies: Kubernetes (enterprise), Docker Swarm (development/staging), and cost-effective self-hosting.

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 20GB free space (50GB recommended)
- **CPU**: Minimum 2 cores (4 cores recommended)
- **Network**: Stable internet connection with ports 80, 443, 8000 accessible

### Software Requirements

- Docker 20.10+
- Docker Compose 2.0+
- Git 2.25+
- curl
- SSL certificates (for production)

## Quick Start

### 1. Clone and Prepare

```bash
# Clone the repository
git clone https://github.com/your-org/culture-connect-backend.git
cd culture-connect-backend

# Validate production readiness
./scripts/validate-production.sh production

# Deploy to production
./scripts/deploy-production.sh production v1.0.0
```

### 2. Environment Configuration

Copy and customize the production environment file:

```bash
cp .env.production .env.production.local
# Edit .env.production.local with your actual values
```

**Critical Variables to Update:**

```bash
# Security
CC_SECRET_KEY=your_secure_random_secret_key_here
CC_JWT_SECRET_KEY=your_jwt_secret_key_here

# Database
CC_POSTGRES_PASSWORD=your_secure_database_password
CC_REDIS_PASSWORD=your_secure_redis_password

# Payment Providers
CC_PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
CC_STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
CC_BUSHA_API_KEY=your_busha_live_api_key

# External Services
CC_SENTRY_DSN=https://<EMAIL>/project_id
CC_AIML_API_KEY=your_aiml_api_key

# Domains
CC_FRONTEND_URL=https://cultureconnect.ng
CC_PWA_URL=https://app.cultureconnect.ng
```

## Deployment Strategies

### Strategy 1: Kubernetes (Enterprise Production)

**Best for**: High-traffic production environments (>1000 concurrent users)

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/redis.yaml
kubectl apply -f k8s/api.yaml
kubectl apply -f k8s/ingress.yaml
```

**Features:**
- Auto-scaling with HPA
- Rolling updates with zero downtime
- Multi-zone deployment
- Integrated monitoring and logging
- SSL termination with cert-manager

### Strategy 2: Docker Swarm (Development/Staging)

**Best for**: Development, staging, and small production environments

```bash
# Initialize Docker Swarm
docker swarm init

# Create external networks
docker network create --driver overlay traefik_network

# Deploy with Docker Swarm
docker stack deploy -c docker-compose.swarm.yml culture-connect
```

**Features:**
- Service discovery and load balancing
- Rolling updates
- Health checks and restart policies
- Traefik integration for SSL

### Strategy 3: Docker Compose (Cost-Effective)

**Best for**: Small deployments, development, and cost-conscious production

```bash
# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
```

**Features:**
- Simple deployment and management
- Resource limits and health checks
- Persistent volumes and networking
- Monitoring and logging integration

## Security Configuration

### SSL/TLS Setup

1. **Obtain SSL Certificates**:
   ```bash
   # Using Let's Encrypt with Certbot
   sudo certbot certonly --standalone -d api.cultureconnect.ng
   ```

2. **Configure SSL in Environment**:
   ```bash
   CC_SSL_ENABLED=true
   CC_SSL_CERT_PATH=/etc/ssl/certs/api.cultureconnect.ng.crt
   CC_SSL_KEY_PATH=/etc/ssl/private/api.cultureconnect.ng.key
   ```

### Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 8000/tcp  # API (if direct access needed)
```

### Secret Management

For production environments, use external secret management:

- **Kubernetes**: Use Kubernetes Secrets or external secret operators
- **Docker Swarm**: Use Docker Secrets
- **Docker Compose**: Use external secret files with restricted permissions

```bash
# Set secure file permissions
chmod 600 .env.production
chown root:root .env.production
```

## Database Setup

### PostgreSQL Configuration

The production deployment includes optimized PostgreSQL settings:

- **Performance tuning** for production workloads
- **Replication readiness** for high availability
- **Monitoring and logging** configuration
- **Backup and archiving** setup

### Database Migration

```bash
# Run migrations manually if needed
docker exec culture_connect_api_prod alembic upgrade head

# Check migration status
docker exec culture_connect_api_prod alembic current
```

## Monitoring and Observability

### Health Checks

The deployment includes comprehensive health checks:

- **Application health**: `/health`
- **Database health**: `/health/db`
- **Redis health**: `/health/redis`
- **Detailed status**: `/health/detailed`

### Logging

Structured JSON logging with:

- **Log rotation** (100MB files, 5 backups)
- **Multiple log levels** (INFO, ERROR, ACCESS)
- **Correlation IDs** for request tracing
- **Sentry integration** for error tracking

### Metrics and Monitoring

- **Prometheus metrics** endpoint: `/metrics`
- **Grafana dashboards** for visualization
- **Sentry APM** for performance monitoring
- **Custom business metrics** for analytics

## Backup and Recovery

### Automated Backups

The production deployment includes automated backup configuration:

```bash
# PostgreSQL backups (daily at 2 AM)
0 2 * * * /opt/culture-connect/scripts/backup-postgres.sh

# Application data backups (daily at 3 AM)
0 3 * * * /opt/culture-connect/scripts/backup-application.sh
```

### Manual Backup

```bash
# Create manual backup
docker exec culture_connect_db_prod pg_dump -U culture_connect culture_connect_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
docker exec -i culture_connect_db_prod psql -U culture_connect culture_connect_db < backup_file.sql
```

## Scaling and Performance

### Horizontal Scaling

```bash
# Scale API service (Docker Compose)
docker-compose -f docker-compose.prod.yml up -d --scale api=5

# Scale API service (Docker Swarm)
docker service scale culture-connect_api=5

# Scale API service (Kubernetes)
kubectl scale deployment culture-connect-api --replicas=5
```

### Performance Optimization

1. **Database optimization**:
   - Connection pooling (configured)
   - Query optimization with indexes
   - Regular VACUUM and ANALYZE

2. **Redis caching**:
   - Application-level caching
   - Session storage
   - Rate limiting

3. **Application optimization**:
   - Async/await patterns
   - Connection pooling
   - Resource limits

## Troubleshooting

### Common Issues

1. **Container startup failures**:
   ```bash
   # Check container logs
   docker logs culture_connect_api_prod
   
   # Check container status
   docker ps -a
   ```

2. **Database connection issues**:
   ```bash
   # Test database connectivity
   docker exec culture_connect_api_prod python -c "from app.db.session import get_db_session; print('DB OK')"
   ```

3. **Redis connection issues**:
   ```bash
   # Test Redis connectivity
   docker exec culture_connect_redis_prod redis-cli ping
   ```

### Performance Issues

1. **High memory usage**:
   ```bash
   # Check memory usage
   docker stats
   
   # Adjust memory limits in docker-compose.prod.yml
   ```

2. **Slow database queries**:
   ```bash
   # Check slow queries
   docker exec culture_connect_db_prod psql -U culture_connect -d culture_connect_db -c "SELECT * FROM slow_queries;"
   ```

### Recovery Procedures

1. **Service recovery**:
   ```bash
   # Restart specific service
   docker-compose -f docker-compose.prod.yml restart api
   
   # Full stack restart
   docker-compose -f docker-compose.prod.yml restart
   ```

2. **Database recovery**:
   ```bash
   # Restore from backup
   ./scripts/restore-database.sh backup_file.sql
   ```

3. **Rollback deployment**:
   ```bash
   # Rollback to previous version
   ./scripts/deploy-production.sh rollback v1.0.0 v0.9.0
   ```

## Maintenance

### Regular Maintenance Tasks

1. **Weekly**:
   - Review application logs
   - Check disk space usage
   - Verify backup integrity
   - Update security patches

2. **Monthly**:
   - Database maintenance (VACUUM, REINDEX)
   - Log rotation and cleanup
   - Performance review
   - Security audit

3. **Quarterly**:
   - Dependency updates
   - Security penetration testing
   - Disaster recovery testing
   - Capacity planning review

### Update Procedures

```bash
# Update to new version
git pull origin main
./scripts/validate-production.sh production
./scripts/deploy-production.sh production v1.1.0 v1.0.0
```

## Support and Documentation

- **API Documentation**: https://api.cultureconnect.ng/docs
- **Monitoring Dashboard**: https://monitoring.cultureconnect.ng
- **Support Email**: <EMAIL>
- **Emergency Contact**: +234-XXX-XXX-XXXX

## Security Considerations

1. **Regular security updates**
2. **SSL certificate renewal**
3. **Secret rotation**
4. **Access control review**
5. **Vulnerability scanning**
6. **Backup encryption**
7. **Network security**
8. **Compliance monitoring**
