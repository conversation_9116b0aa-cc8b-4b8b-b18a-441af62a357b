"""
Email schemas for Culture Connect Backend API.

This module defines comprehensive email management schemas including:
- EmailTemplate schemas: Template management with validation and versioning
- EmailSend schemas: Email sending request/response validation
- EmailDelivery schemas: Delivery tracking and status reporting
- EmailPreference schemas: User notification preference management

Implements Task 2.3.1 Phase 2 requirements for email service implementation with
production-grade Pydantic validation and seamless integration with Phase 1 models.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, validator, EmailStr, constr, conint
from pydantic.types import Json

from app.models.email_models import (
    EmailTemplateCategory,
    EmailDeliveryStatus,
    EmailQueueStatus
)


# Base schemas for common functionality
class EmailBaseSchema(BaseModel):
    """Base schema for email-related models with common configuration."""

    class Config:
        """Pydantic configuration for email schemas."""
        from_attributes = True
        use_enum_values = True
        validate_assignment = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


# Email Template Schemas
class EmailTemplateBase(EmailBaseSchema):
    """Base schema for email template with common fields."""

    name: constr(min_length=1, max_length=255, strip_whitespace=True) = Field(
        ...,
        description="Template name identifier (unique per version)",
        example="welcome_email"
    )

    category: EmailTemplateCategory = Field(
        ...,
        description="Template category for organization and access control",
        example=EmailTemplateCategory.VERIFICATION
    )

    subject_template: constr(min_length=1, max_length=500, strip_whitespace=True) = Field(
        ...,
        description="Jinja2 template for email subject with variable substitution",
        example="Welcome to Culture Connect, {{user_name}}!"
    )

    body_template: constr(min_length=1, strip_whitespace=True) = Field(
        ...,
        description="Jinja2 template for email body with variable substitution",
        example="Hello {{user_name}}, welcome to our platform!"
    )

    html_template: Optional[str] = Field(
        None,
        description="Optional HTML template for rich email formatting",
        example="<h1>Welcome {{user_name}}</h1><p>Thank you for joining!</p>"
    )

    variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Required template variables with descriptions and types",
        example={"user_name": "string", "verification_url": "url"}
    )

    is_active: bool = Field(
        True,
        description="Template active status for lifecycle management"
    )

    @validator('subject_template', 'body_template', 'html_template')
    def validate_template_syntax(cls, v):
        """Validate Jinja2 template syntax."""
        if v is None:
            return v

        try:
            from jinja2 import Template
            Template(v)
        except Exception as e:
            raise ValueError(f"Invalid Jinja2 template syntax: {str(e)}")

        return v

    @validator('variables')
    def validate_variables_format(cls, v):
        """Validate template variables format."""
        if not isinstance(v, dict):
            raise ValueError("Variables must be a dictionary")

        # Ensure all keys are strings
        for key in v.keys():
            if not isinstance(key, str):
                raise ValueError("Variable names must be strings")

        return v


class EmailTemplateCreate(EmailTemplateBase):
    """Schema for creating new email templates."""

    version: conint(ge=1) = Field(
        1,
        description="Template version number (auto-incremented)"
    )

    @validator('name')
    def validate_name_format(cls, v):
        """Validate template name format."""
        import re
        if not re.match(r'^[a-z0-9_]+$', v):
            raise ValueError("Template name must contain only lowercase letters, numbers, and underscores")
        return v


class EmailTemplateUpdate(EmailBaseSchema):
    """Schema for updating existing email templates."""

    name: Optional[constr(min_length=1, max_length=255, strip_whitespace=True)] = Field(
        None,
        description="Template name identifier"
    )

    category: Optional[EmailTemplateCategory] = Field(
        None,
        description="Template category"
    )

    subject_template: Optional[constr(min_length=1, max_length=500, strip_whitespace=True)] = Field(
        None,
        description="Jinja2 template for email subject"
    )

    body_template: Optional[constr(min_length=1, strip_whitespace=True)] = Field(
        None,
        description="Jinja2 template for email body"
    )

    html_template: Optional[str] = Field(
        None,
        description="Optional HTML template"
    )

    variables: Optional[Dict[str, Any]] = Field(
        None,
        description="Required template variables"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Template active status"
    )


class EmailTemplateResponse(EmailTemplateBase):
    """Schema for email template responses."""

    id: UUID = Field(
        ...,
        description="Template unique identifier"
    )

    version: int = Field(
        ...,
        description="Template version number"
    )

    created_by: int = Field(
        ...,
        description="ID of user who created the template"
    )

    created_at: datetime = Field(
        ...,
        description="Template creation timestamp"
    )

    updated_at: datetime = Field(
        ...,
        description="Template last update timestamp"
    )


class EmailTemplateListResponse(EmailBaseSchema):
    """Schema for paginated email template list responses."""

    templates: List[EmailTemplateResponse] = Field(
        ...,
        description="List of email templates"
    )

    total: int = Field(
        ...,
        description="Total number of templates"
    )

    page: int = Field(
        ...,
        description="Current page number"
    )

    size: int = Field(
        ...,
        description="Page size"
    )

    pages: int = Field(
        ...,
        description="Total number of pages"
    )


# Email Sending Schemas
class EmailSendRequest(EmailBaseSchema):
    """Schema for email sending requests."""

    recipient_email: EmailStr = Field(
        ...,
        description="Recipient email address",
        example="<EMAIL>"
    )

    template_id: Optional[UUID] = Field(
        None,
        description="Template ID for template-based emails"
    )

    subject: Optional[constr(min_length=1, max_length=500, strip_whitespace=True)] = Field(
        None,
        description="Email subject (required if not using template)"
    )

    body: Optional[constr(min_length=1, strip_whitespace=True)] = Field(
        None,
        description="Email body (required if not using template)"
    )

    html_body: Optional[str] = Field(
        None,
        description="Optional HTML email body"
    )

    template_variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Variables for template substitution",
        example={"user_name": "John Doe", "verification_code": "123456"}
    )

    priority: conint(ge=1, le=5) = Field(
        3,
        description="Email priority (1=highest, 5=lowest)"
    )

    scheduled_at: Optional[datetime] = Field(
        None,
        description="Optional scheduled delivery time"
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Optional correlation ID for request tracking"
    )

    @validator('body')
    def validate_content_or_template(cls, v, values):
        """Validate that either template_id or subject/body is provided."""
        template_id = values.get('template_id')
        subject = values.get('subject')

        # If no template_id, then subject and body are required
        if not template_id:
            if not subject or not v:
                raise ValueError("Either template_id or both subject and body must be provided")

        return v

    @validator('scheduled_at')
    def validate_scheduled_time(cls, v):
        """Validate scheduled time is in the future."""
        if v and v <= datetime.utcnow():
            raise ValueError("Scheduled time must be in the future")
        return v


class EmailSendResponse(EmailBaseSchema):
    """Schema for email sending responses."""

    id: UUID = Field(
        ...,
        description="Email delivery unique identifier"
    )

    recipient_email: EmailStr = Field(
        ...,
        description="Recipient email address"
    )

    subject: str = Field(
        ...,
        description="Email subject"
    )

    status: EmailDeliveryStatus = Field(
        ...,
        description="Email delivery status"
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Request correlation ID"
    )

    created_at: datetime = Field(
        ...,
        description="Email creation timestamp"
    )

    message: str = Field(
        "Email queued for delivery",
        description="Status message"
    )


class EmailBatchSendRequest(EmailBaseSchema):
    """Schema for batch email sending requests."""

    recipients: List[EmailStr] = Field(
        ...,
        min_items=1,
        max_items=1000,
        description="List of recipient email addresses (max 1000)"
    )

    template_id: UUID = Field(
        ...,
        description="Template ID for batch emails"
    )

    template_variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Common variables for all recipients"
    )

    recipient_variables: Optional[Dict[str, Dict[str, Any]]] = Field(
        None,
        description="Per-recipient variables (email -> variables mapping)"
    )

    priority: conint(ge=1, le=5) = Field(
        3,
        description="Email priority (1=highest, 5=lowest)"
    )

    scheduled_at: Optional[datetime] = Field(
        None,
        description="Optional scheduled delivery time"
    )

    @validator('recipient_variables')
    def validate_recipient_variables(cls, v, values):
        """Validate recipient variables mapping."""
        if v is None:
            return v

        recipients = values.get('recipients', [])
        for email in v.keys():
            if email not in recipients:
                raise ValueError(f"Recipient variables provided for unknown email: {email}")

        return v


class EmailBatchSendResponse(EmailBaseSchema):
    """Schema for batch email sending responses."""

    batch_id: str = Field(
        ...,
        description="Batch processing identifier"
    )

    total_recipients: int = Field(
        ...,
        description="Total number of recipients"
    )

    queued_count: int = Field(
        ...,
        description="Number of emails successfully queued"
    )

    failed_count: int = Field(
        ...,
        description="Number of emails that failed to queue"
    )

    failed_recipients: List[str] = Field(
        default_factory=list,
        description="List of failed recipient emails"
    )

    message: str = Field(
        ...,
        description="Batch processing status message"
    )


# Email Delivery Tracking Schemas
class EmailDeliveryBase(EmailBaseSchema):
    """Base schema for email delivery tracking."""

    recipient_email: EmailStr = Field(
        ...,
        description="Recipient email address"
    )

    subject: str = Field(
        ...,
        description="Email subject"
    )

    status: EmailDeliveryStatus = Field(
        ...,
        description="Delivery status"
    )


class EmailDeliveryResponse(EmailDeliveryBase):
    """Schema for email delivery responses."""

    id: UUID = Field(
        ...,
        description="Delivery unique identifier"
    )

    user_id: Optional[int] = Field(
        None,
        description="Associated user ID"
    )

    template_id: Optional[UUID] = Field(
        None,
        description="Template ID if template-based"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message if delivery failed"
    )

    sent_at: Optional[datetime] = Field(
        None,
        description="Email sent timestamp"
    )

    delivered_at: Optional[datetime] = Field(
        None,
        description="Email delivered timestamp"
    )

    opened_at: Optional[datetime] = Field(
        None,
        description="Email opened timestamp"
    )

    email_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional delivery metadata"
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Request correlation ID"
    )

    created_at: datetime = Field(
        ...,
        description="Delivery record creation timestamp"
    )


class EmailDeliveryListResponse(EmailBaseSchema):
    """Schema for paginated email delivery list responses."""

    deliveries: List[EmailDeliveryResponse] = Field(
        ...,
        description="List of email deliveries"
    )

    total: int = Field(
        ...,
        description="Total number of deliveries"
    )

    page: int = Field(
        ...,
        description="Current page number"
    )

    size: int = Field(
        ...,
        description="Page size"
    )

    pages: int = Field(
        ...,
        description="Total number of pages"
    )


class EmailDeliveryStatusUpdate(EmailBaseSchema):
    """Schema for updating email delivery status."""

    status: EmailDeliveryStatus = Field(
        ...,
        description="New delivery status"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message if status is failed"
    )

    delivered_at: Optional[datetime] = Field(
        None,
        description="Delivery timestamp"
    )

    email_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata to update"
    )


# Email Preference Schemas
class EmailPreferenceBase(EmailBaseSchema):
    """Base schema for email preferences."""

    verification_emails: bool = Field(
        True,
        description="Enable email verification notifications"
    )

    security_emails: bool = Field(
        True,
        description="Enable security-related email notifications"
    )

    marketing_emails: bool = Field(
        False,
        description="Enable marketing and promotional emails"
    )

    booking_notifications: bool = Field(
        True,
        description="Enable booking-related notifications"
    )

    vendor_notifications: bool = Field(
        True,
        description="Enable vendor-related notifications"
    )

    system_notifications: bool = Field(
        True,
        description="Enable system and maintenance notifications"
    )


class EmailPreferenceUpdate(EmailPreferenceBase):
    """Schema for updating email preferences."""

    verification_emails: Optional[bool] = Field(
        None,
        description="Enable email verification notifications"
    )

    security_emails: Optional[bool] = Field(
        None,
        description="Enable security-related email notifications"
    )

    marketing_emails: Optional[bool] = Field(
        None,
        description="Enable marketing and promotional emails"
    )

    booking_notifications: Optional[bool] = Field(
        None,
        description="Enable booking-related notifications"
    )

    vendor_notifications: Optional[bool] = Field(
        None,
        description="Enable vendor-related notifications"
    )

    system_notifications: Optional[bool] = Field(
        None,
        description="Enable system and maintenance notifications"
    )

    def __init__(self, **data):
        """Initialize and validate that at least one field is provided."""
        super().__init__(**data)
        # Check if at least one field is not None
        provided_fields = {k: v for k, v in data.items() if v is not None}
        if not provided_fields:
            raise ValueError("At least one preference field must be provided for update")


class EmailPreferenceResponse(EmailPreferenceBase):
    """Schema for email preference responses."""

    id: UUID = Field(
        ...,
        description="Preference unique identifier"
    )

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    opted_out_at: Optional[datetime] = Field(
        None,
        description="Global opt-out timestamp"
    )

    created_at: datetime = Field(
        ...,
        description="Preference creation timestamp"
    )

    updated_at: datetime = Field(
        ...,
        description="Preference last update timestamp"
    )


class EmailOptOutRequest(EmailBaseSchema):
    """Schema for global email opt-out requests."""

    email: EmailStr = Field(
        ...,
        description="Email address to opt out"
    )

    reason: Optional[constr(max_length=500)] = Field(
        None,
        description="Optional reason for opting out"
    )


class EmailOptOutResponse(EmailBaseSchema):
    """Schema for email opt-out responses."""

    email: EmailStr = Field(
        ...,
        description="Email address that was opted out"
    )

    opted_out_at: datetime = Field(
        ...,
        description="Opt-out timestamp"
    )

    message: str = Field(
        "Successfully opted out from all email communications",
        description="Confirmation message"
    )


# Email Queue Schemas
class EmailQueueResponse(EmailBaseSchema):
    """Schema for email queue responses."""

    id: UUID = Field(
        ...,
        description="Queue item unique identifier"
    )

    user_id: Optional[int] = Field(
        None,
        description="Associated user ID"
    )

    template_id: Optional[UUID] = Field(
        None,
        description="Template ID if template-based"
    )

    recipient_email: EmailStr = Field(
        ...,
        description="Recipient email address"
    )

    subject: str = Field(
        ...,
        description="Email subject"
    )

    priority: int = Field(
        ...,
        description="Email priority (1=highest, 5=lowest)"
    )

    scheduled_at: datetime = Field(
        ...,
        description="Scheduled delivery time"
    )

    attempts: int = Field(
        ...,
        description="Number of delivery attempts"
    )

    max_attempts: int = Field(
        ...,
        description="Maximum allowed attempts"
    )

    status: EmailQueueStatus = Field(
        ...,
        description="Queue processing status"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message if processing failed"
    )

    queue_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional queue metadata"
    )

    created_at: datetime = Field(
        ...,
        description="Queue item creation timestamp"
    )

    processed_at: Optional[datetime] = Field(
        None,
        description="Processing completion timestamp"
    )


class EmailQueueListResponse(EmailBaseSchema):
    """Schema for paginated email queue list responses."""

    queue_items: List[EmailQueueResponse] = Field(
        ...,
        description="List of email queue items"
    )

    total: int = Field(
        ...,
        description="Total number of queue items"
    )

    page: int = Field(
        ...,
        description="Current page number"
    )

    size: int = Field(
        ...,
        description="Page size"
    )

    pages: int = Field(
        ...,
        description="Total number of pages"
    )


# Email Analytics Schemas
class EmailAnalyticsResponse(EmailBaseSchema):
    """Schema for email analytics responses."""

    total_sent: int = Field(
        ...,
        description="Total emails sent"
    )

    total_delivered: int = Field(
        ...,
        description="Total emails delivered"
    )

    total_failed: int = Field(
        ...,
        description="Total emails failed"
    )

    total_bounced: int = Field(
        ...,
        description="Total emails bounced"
    )

    delivery_rate: float = Field(
        ...,
        description="Delivery success rate (percentage)"
    )

    bounce_rate: float = Field(
        ...,
        description="Bounce rate (percentage)"
    )

    by_category: Dict[str, Dict[str, int]] = Field(
        default_factory=dict,
        description="Statistics by email category"
    )

    by_status: Dict[str, int] = Field(
        default_factory=dict,
        description="Statistics by delivery status"
    )

    period_start: datetime = Field(
        ...,
        description="Analytics period start"
    )

    period_end: datetime = Field(
        ...,
        description="Analytics period end"
    )


# Email Verification Schemas (Integration with Task 2.2.1)
class EmailVerificationRequest(EmailBaseSchema):
    """Schema for email verification requests."""

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    email: EmailStr = Field(
        ...,
        description="Email address to verify"
    )

    user_name: str = Field(
        ...,
        description="User's full name for personalization"
    )

    verification_token: str = Field(
        ...,
        description="Email verification token"
    )

    verification_type: str = Field(
        "registration",
        description="Type of verification (registration, email_change, etc.)"
    )

    @validator('verification_type')
    def validate_verification_type(cls, v):
        """Validate verification type."""
        allowed_types = ['registration', 'email_change', 'password_reset', 'account_recovery']
        if v not in allowed_types:
            raise ValueError(f"Verification type must be one of: {', '.join(allowed_types)}")
        return v


class EmailVerificationResponse(EmailBaseSchema):
    """Schema for email verification responses."""

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    email: EmailStr = Field(
        ...,
        description="Email address being verified"
    )

    delivery_id: UUID = Field(
        ...,
        description="Email delivery tracking ID"
    )

    status: EmailDeliveryStatus = Field(
        ...,
        description="Email delivery status"
    )

    message: str = Field(
        "Verification email sent successfully",
        description="Response message"
    )


# Password Reset Email Schemas (Integration with Task 2.1.2)
class PasswordResetEmailRequest(EmailBaseSchema):
    """Schema for password reset email requests."""

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    email: EmailStr = Field(
        ...,
        description="Email address for password reset"
    )

    user_name: str = Field(
        ...,
        description="User's full name for personalization"
    )

    reset_token: str = Field(
        ...,
        description="Password reset token"
    )

    ip_address: Optional[str] = Field(
        None,
        description="IP address of the reset request"
    )

    user_agent: Optional[str] = Field(
        None,
        description="User agent of the reset request"
    )


class PasswordResetEmailResponse(EmailBaseSchema):
    """Schema for password reset email responses."""

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    email: EmailStr = Field(
        ...,
        description="Email address"
    )

    delivery_id: UUID = Field(
        ...,
        description="Email delivery tracking ID"
    )

    status: EmailDeliveryStatus = Field(
        ...,
        description="Email delivery status"
    )

    message: str = Field(
        "Password reset email sent successfully",
        description="Response message"
    )
