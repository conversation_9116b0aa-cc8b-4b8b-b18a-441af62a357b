# Culture Connect API Endpoints Reference

**Version**: v1.0.0  
**Base URL**: `https://api.cultureconnect.ng/api/v1`  
**Last Updated**: 2025-02-01

## 📋 Overview

This document provides a comprehensive reference of all API endpoints across the Culture Connect platform, organized by functional categories and consumer types. Use this as a quick reference guide for endpoint discovery and integration planning.

**Consumer Types**:
- 📱 **Mobile App**: Customer-facing mobile applications (Flutter/React Native)
- 🏢 **PWA Vendor Portal**: Vendor management progressive web application
- 🤖 **AI Integration**: External AI engine integration for travel planning

---

## 🔐 Authentication & Security

### Core Authentication
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/auth/register` | User registration with email verification | 📱 Mobile |
| `POST` | `/auth/login` | User login with JWT token generation | 📱 Mobile |
| `POST` | `/auth/refresh` | Refresh JWT access token | 📱 Mobile |
| `POST` | `/auth/logout` | User logout and token invalidation | 📱 Mobile |
| `POST` | `/auth/forgot-password` | Initiate password reset process | 📱 Mobile |
| `POST` | `/auth/reset-password` | Complete password reset with token | 📱 Mobile |
| `POST` | `/auth/verify-email` | Verify email address with verification token | 📱 Mobile |

### Vendor Authentication
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/auth/vendor/register` | Vendor business registration with verification | 🏢 PWA Vendor |
| `POST` | `/auth/vendor/login` | Vendor login with RBAC permissions | 🏢 PWA Vendor |
| `POST` | `/auth/vendor/refresh` | Refresh vendor JWT token | 🏢 PWA Vendor |

### AI Service Authentication
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/auth/register-service` | Register external AI service provider | 🤖 AI Integration |
| `POST` | `/ai/auth/rotate-key` | Rotate AI service API keys | 🤖 AI Integration |

---

## 👤 User Management

### User Profile
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/users/profile` | Get current user profile information | 📱 Mobile |
| `PUT` | `/users/profile` | Update user profile details | 📱 Mobile |
| `POST` | `/users/profile/avatar` | Upload user profile avatar image | 📱 Mobile |
| `GET` | `/users/preferences` | Get user travel and cultural preferences | 📱 Mobile |
| `PUT` | `/users/preferences` | Update user preferences and interests | 📱 Mobile |

### User Travel History
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/users/travel-history` | Get user's past bookings and experiences | 📱 Mobile |
| `GET` | `/users/favorites` | Get user's favorite experiences | 📱 Mobile |
| `POST` | `/users/favorites/{experience_id}` | Add experience to favorites | 📱 Mobile |
| `DELETE` | `/users/favorites/{experience_id}` | Remove experience from favorites | 📱 Mobile |

---

## 🎭 Cultural Experiences

### Experience Discovery (Mobile)
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/experiences` | Search and filter cultural experiences | 📱 Mobile |
| `GET` | `/experiences/{id}` | Get detailed experience information | 📱 Mobile |
| `GET` | `/experiences/nearby` | Find experiences near user location | 📱 Mobile |
| `GET` | `/experiences/categories` | Get available experience categories | 📱 Mobile |
| `GET` | `/experiences/featured` | Get featured and promoted experiences | 📱 Mobile |
| `GET` | `/experiences/{id}/availability` | Check real-time availability for dates | 📱 Mobile |
| `GET` | `/experiences/{id}/reviews` | Get customer reviews and ratings | 📱 Mobile |

### Experience Management (Vendor)
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/vendor/experiences` | Create new cultural experience | 🏢 PWA Vendor |
| `GET` | `/vendor/experiences` | Get vendor's experiences with filters | 🏢 PWA Vendor |
| `GET` | `/vendor/experiences/{id}` | Get detailed vendor experience info | 🏢 PWA Vendor |
| `PUT` | `/vendor/experiences/{id}` | Update experience details | 🏢 PWA Vendor |
| `DELETE` | `/vendor/experiences/{id}` | Delete/deactivate experience | 🏢 PWA Vendor |
| `POST` | `/vendor/experiences/{id}/media` | Upload experience media files | 🏢 PWA Vendor |
| `PUT` | `/vendor/experiences/{id}/availability` | Update availability calendar | 🏢 PWA Vendor |

---

## 📅 Booking Management

### Customer Bookings (Mobile)
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/bookings` | Create new booking for experience | 📱 Mobile |
| `GET` | `/bookings` | Get user's booking history and upcoming | 📱 Mobile |
| `GET` | `/bookings/{id}` | Get detailed booking information | 📱 Mobile |
| `PUT` | `/bookings/{id}` | Modify booking details (if allowed) | 📱 Mobile |
| `DELETE` | `/bookings/{id}` | Cancel booking with refund processing | 📱 Mobile |
| `POST` | `/bookings/{id}/reviews` | Submit review after experience completion | 📱 Mobile |

### Vendor Booking Management
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/vendor/bookings` | Get all vendor bookings with filters | 🏢 PWA Vendor |
| `GET` | `/vendor/bookings/{id}` | Get detailed booking with customer info | 🏢 PWA Vendor |
| `PATCH` | `/vendor/bookings/{id}/status` | Update booking status (confirm/complete) | 🏢 PWA Vendor |
| `POST` | `/vendor/bookings/{id}/messages` | Send message to customer | 🏢 PWA Vendor |

---

## 💳 Payment Processing

### Payment Operations
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/payments/initialize` | Initialize payment for booking | 📱 Mobile |
| `GET` | `/payments/{id}/verify` | Verify payment completion status | 📱 Mobile |
| `GET` | `/payments/exchange-rates` | Get current currency exchange rates | 📱 Mobile |
| `GET` | `/payments/methods` | Get available payment methods by location | 📱 Mobile |

### Vendor Payment Management
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/vendor/payments` | Get vendor payment history and payouts | 🏢 PWA Vendor |
| `GET` | `/vendor/payments/summary` | Get payment summary and pending amounts | 🏢 PWA Vendor |

---

## 🔔 Notifications & Communication

### Push Notifications
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/notifications/register-device` | Register device for push notifications | 📱 Mobile |
| `PUT` | `/notifications/preferences` | Update notification preferences | 📱 Mobile |
| `GET` | `/notifications/history` | Get notification history | 📱 Mobile |

### Vendor Notifications
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/vendor/notifications/subscribe` | Subscribe to vendor push notifications | 🏢 PWA Vendor |
| `GET` | `/vendor/notifications` | Get vendor notification history | 🏢 PWA Vendor |

---

## 🌍 Location & Geolocation

### Location Services
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/recommendations/location` | Get location-based experience recommendations | 📱 Mobile |
| `GET` | `/services/nearby` | Find nearby services (ATM, hospital, etc.) | 📱 Mobile |
| `GET` | `/locations/cities` | Get supported cities and regions | 📱 Mobile |

---

## 📊 Analytics & Reporting

### User Analytics
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/analytics/events` | Track user behavior events | 📱 Mobile |
| `POST` | `/analytics/performance` | Submit app performance metrics | 📱 Mobile |

### Vendor Analytics
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/vendor/analytics/dashboard` | Get comprehensive vendor dashboard data | 🏢 PWA Vendor |
| `GET` | `/vendor/analytics/revenue` | Get detailed revenue analytics | 🏢 PWA Vendor |
| `GET` | `/vendor/analytics/customers` | Get customer analytics and demographics | 🏢 PWA Vendor |
| `GET` | `/vendor/analytics/experiences` | Get experience performance analytics | 🏢 PWA Vendor |

---

## 🤖 AI Integration & Travel Planning

### AI Travel Planning
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/travel/generate-itinerary` | Generate intelligent travel itinerary | 🤖 AI Integration |
| `POST` | `/ai/travel/optimize-itinerary` | Real-time itinerary optimization | 🤖 AI Integration |
| `POST` | `/ai/recommendations/personalized` | Get AI-powered experience recommendations | 🤖 AI Integration |

### AI Natural Language Processing
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/nlp/travel-chat` | Conversational travel planning interface | 🤖 AI Integration |
| `POST` | `/ai/nlp/translate-and-localize` | Multi-language translation and localization | 🤖 AI Integration |

### AI Cultural Analysis
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/culture/analyze-preferences` | Analyze user cultural preferences and compatibility | 🤖 AI Integration |

### AI Predictive Analytics
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/analytics/demand-forecast` | Predict demand for experiences and pricing | 🤖 AI Integration |

### AI Provider Management
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `POST` | `/ai/external/register-provider` | Register external AI service provider | 🤖 AI Integration |
| `GET` | `/ai/external/health-check/{provider_id}` | Check AI provider health and status | 🤖 AI Integration |

---

## 🔄 Real-time Communication

### WebSocket Endpoints
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `WS` | `/websocket/user/{user_id}` | Real-time updates for mobile users | 📱 Mobile |
| `WS` | `/websocket/vendor/{vendor_id}` | Real-time updates for vendor portal | 🏢 PWA Vendor |
| `WS` | `/websocket/rooms/{room_id}` | Room-based communication | 📱 Mobile, 🏢 PWA Vendor |
| `WS` | `/websocket/broadcast` | System-wide broadcast messages | 📱 Mobile, 🏢 PWA Vendor |

---

## 🏥 System Health & Monitoring

### Health Checks
| Method | Endpoint | Description | Consumer |
|--------|----------|-------------|----------|
| `GET` | `/health` | Basic API health check | All |
| `GET` | `/health/detailed` | Detailed system health with dependencies | All |
| `GET` | `/status` | API status and version information | All |

---

## 📈 Summary Statistics

**Total Endpoints**: 75+
- **Mobile App Endpoints**: 35+
- **PWA Vendor Portal Endpoints**: 25+
- **AI Integration Endpoints**: 15+

**Functional Categories**: 11
- Authentication & Security
- User Management
- Cultural Experiences
- Booking Management
- Payment Processing
- Notifications & Communication
- Location & Geolocation
- Analytics & Reporting
- AI Integration & Travel Planning
- Real-time Communication
- System Health & Monitoring

**HTTP Methods Used**: GET, POST, PUT, PATCH, DELETE, WS (WebSocket)

---

*For detailed request/response schemas, authentication requirements, and integration examples, refer to the specific API documentation files:*
- 📱 Mobile App API: `docs/api/mobile/README.md`
- 🏢 PWA Vendor Portal API: `docs/api/pwa/README.md`
- 🤖 AI Integration API: `docs/api/ai/README.md`
