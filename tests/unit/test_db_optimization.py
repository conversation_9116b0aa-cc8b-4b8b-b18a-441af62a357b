"""
Unit tests for database configuration optimization and performance monitoring.

This module tests the database optimization features including:
- Environment-specific configuration
- Connection pool optimization
- SSL configuration
- Query performance monitoring
- Performance analysis and recommendations
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

from app.db.config import (
    DatabaseEnvironment,
    DatabaseType,
    DatabaseConfigManager,
    ConnectionPoolConfig,
    SSLConfig,
    QueryConfig,
    get_optimized_engine_args,
    get_optimized_session_args,
    get_database_health_thresholds
)
from app.db.performance import (
    QueryPerformanceMetric,
    ConnectionPoolMetrics,
    QueryPerformanceMonitor,
    ConnectionPoolMonitor,
    DatabasePerformanceAnalyzer,
    performance_analyzer
)


class TestDatabaseConfigManager:
    """Test cases for DatabaseConfigManager class."""
    
    @pytest.fixture
    def config_manager(self):
        """Create a fresh config manager instance for testing."""
        return DatabaseConfigManager()
    
    def test_environment_detection(self, config_manager):
        """Test environment detection from settings."""
        with patch('app.db.config.settings') as mock_settings:
            # Test production detection
            mock_settings.ENVIRONMENT = "production"
            manager = DatabaseConfigManager()
            assert manager._detect_environment() == DatabaseEnvironment.PRODUCTION
            
            # Test staging detection
            mock_settings.ENVIRONMENT = "staging"
            manager = DatabaseConfigManager()
            assert manager._detect_environment() == DatabaseEnvironment.STAGING
            
            # Test testing detection
            mock_settings.ENVIRONMENT = "testing"
            manager = DatabaseConfigManager()
            assert manager._detect_environment() == DatabaseEnvironment.TESTING
            
            # Test development detection (default)
            mock_settings.ENVIRONMENT = "development"
            manager = DatabaseConfigManager()
            assert manager._detect_environment() == DatabaseEnvironment.DEVELOPMENT
    
    def test_database_type_detection(self, config_manager):
        """Test database type detection from URL."""
        with patch('app.db.config.settings') as mock_settings:
            # Test PostgreSQL detection
            mock_settings.DATABASE_URL = "postgresql://user:pass@localhost/db"
            manager = DatabaseConfigManager()
            assert manager._detect_database_type() == DatabaseType.POSTGRESQL
            
            # Test SQLite detection
            mock_settings.DATABASE_URL = "sqlite:///test.db"
            manager = DatabaseConfigManager()
            assert manager._detect_database_type() == DatabaseType.SQLITE
            
            # Test MySQL detection
            mock_settings.DATABASE_URL = "mysql://user:pass@localhost/db"
            manager = DatabaseConfigManager()
            assert manager._detect_database_type() == DatabaseType.MYSQL
    
    def test_production_connection_pool_config(self, config_manager):
        """Test production connection pool configuration."""
        with patch.object(config_manager, 'environment', DatabaseEnvironment.PRODUCTION):
            config = config_manager.get_connection_pool_config()
            
            assert isinstance(config, ConnectionPoolConfig)
            assert config.pool_size == 20  # Higher for production
            assert config.max_overflow == 30
            assert config.pool_pre_ping is True
            assert config.pool_reset_on_return == "commit"
    
    def test_testing_connection_pool_config(self, config_manager):
        """Test testing connection pool configuration."""
        with patch.object(config_manager, 'environment', DatabaseEnvironment.TESTING):
            config = config_manager.get_connection_pool_config()
            
            assert isinstance(config, ConnectionPoolConfig)
            assert config.pool_size == 1  # Minimal for testing
            assert config.max_overflow == 2
            assert config.pool_pre_ping is False  # Skip for speed
            assert config.pool_reset_on_return == "rollback"  # For test isolation
    
    def test_production_ssl_config(self, config_manager):
        """Test production SSL configuration."""
        with patch.object(config_manager, 'environment', DatabaseEnvironment.PRODUCTION):
            config = config_manager.get_ssl_config()
            
            assert isinstance(config, SSLConfig)
            assert config.enabled is True
            assert config.mode == "require"
            assert config.check_hostname is True
    
    def test_development_ssl_config(self, config_manager):
        """Test development SSL configuration."""
        with patch.object(config_manager, 'environment', DatabaseEnvironment.DEVELOPMENT):
            config = config_manager.get_ssl_config()
            
            assert isinstance(config, SSLConfig)
            assert config.enabled is False
            assert config.mode == "disable"
            assert config.check_hostname is False
    
    def test_query_config_by_environment(self, config_manager):
        """Test query configuration varies by environment."""
        # Production config
        with patch.object(config_manager, 'environment', DatabaseEnvironment.PRODUCTION):
            prod_config = config_manager.get_query_config()
            assert isinstance(prod_config, QueryConfig)
            assert prod_config.statement_timeout == 300  # 5 minutes
        
        # Testing config
        with patch.object(config_manager, 'environment', DatabaseEnvironment.TESTING):
            test_config = config_manager.get_query_config()
            assert isinstance(test_config, QueryConfig)
            assert test_config.statement_timeout == 30  # Shorter for tests
    
    def test_engine_args_generation(self, config_manager):
        """Test engine arguments generation."""
        with patch.object(config_manager, 'database_type', DatabaseType.POSTGRESQL):
            with patch.object(config_manager, 'environment', DatabaseEnvironment.PRODUCTION):
                args = config_manager.get_engine_args()
                
                assert isinstance(args, dict)
                assert "echo" in args
                assert "future" in args
                assert "pool_size" in args
                assert "max_overflow" in args
    
    def test_sqlite_engine_args(self, config_manager):
        """Test SQLite-specific engine arguments."""
        with patch.object(config_manager, 'database_type', DatabaseType.SQLITE):
            args = config_manager.get_engine_args()
            
            # SQLite should not have pool settings
            assert "pool_size" not in args
            assert "max_overflow" not in args
    
    def test_postgresql_optimizations(self, config_manager):
        """Test PostgreSQL-specific optimizations."""
        with patch.object(config_manager, 'environment', DatabaseEnvironment.PRODUCTION):
            optimizations = config_manager.get_postgresql_optimizations()
            
            assert isinstance(optimizations, dict)
            assert "statement_timeout" in optimizations
            assert "lock_timeout" in optimizations
            assert "shared_preload_libraries" in optimizations  # Production-specific


class TestQueryPerformanceMonitor:
    """Test cases for QueryPerformanceMonitor class."""
    
    @pytest.fixture
    def monitor(self):
        """Create a fresh query monitor instance for testing."""
        return QueryPerformanceMonitor(slow_query_threshold=0.5)
    
    def test_metric_recording(self, monitor):
        """Test query metric recording."""
        metric = QueryPerformanceMetric(
            query_hash="test_hash",
            query_type="SELECT",
            execution_time=0.1,
            rows_affected=10,
            timestamp=datetime.utcnow(),
            success=True
        )
        
        monitor._record_metric(metric)
        
        assert len(monitor.query_metrics) == 1
        assert monitor.query_metrics[0] == metric
    
    def test_slow_query_detection(self, monitor):
        """Test slow query detection."""
        # Add fast query
        fast_metric = QueryPerformanceMetric(
            query_hash="fast",
            query_type="SELECT",
            execution_time=0.1,
            rows_affected=10,
            timestamp=datetime.utcnow(),
            success=True
        )
        monitor._record_metric(fast_metric)
        
        # Add slow query
        slow_metric = QueryPerformanceMetric(
            query_hash="slow",
            query_type="SELECT",
            execution_time=1.0,
            rows_affected=100,
            timestamp=datetime.utcnow(),
            success=True
        )
        monitor._record_metric(slow_metric)
        
        slow_queries = monitor.get_slow_queries()
        assert len(slow_queries) == 1
        assert slow_queries[0] == slow_metric
    
    def test_query_statistics(self, monitor):
        """Test query statistics calculation."""
        # Add successful queries
        for i in range(3):
            metric = QueryPerformanceMetric(
                query_hash=f"query_{i}",
                query_type="SELECT",
                execution_time=0.1 * (i + 1),
                rows_affected=10,
                timestamp=datetime.utcnow(),
                success=True
            )
            monitor._record_metric(metric)
        
        # Add failed query
        failed_metric = QueryPerformanceMetric(
            query_hash="failed",
            query_type="INSERT",
            execution_time=0.0,
            rows_affected=0,
            timestamp=datetime.utcnow(),
            success=False,
            error_message="Test error"
        )
        monitor._record_metric(failed_metric)
        
        stats = monitor.get_query_statistics()
        
        assert stats["total_queries"] == 4
        assert stats["successful_queries"] == 3
        assert stats["failed_queries"] == 1
        assert stats["success_rate"] == 75.0
        assert stats["average_execution_time"] == 0.2  # (0.1 + 0.2 + 0.3) / 3


class TestConnectionPoolMonitor:
    """Test cases for ConnectionPoolMonitor class."""
    
    @pytest.fixture
    def monitor(self):
        """Create a fresh pool monitor instance for testing."""
        return ConnectionPoolMonitor()
    
    def test_checkout_time_recording(self, monitor):
        """Test connection checkout time recording."""
        monitor.record_checkout_time(0.1)
        monitor.record_checkout_time(0.2)
        monitor.record_checkout_time(0.3)
        
        assert len(monitor.checkout_times) == 3
        assert monitor.checkout_times == [0.1, 0.2, 0.3]
    
    def test_checkout_time_history_limit(self, monitor):
        """Test checkout time history limit."""
        # Add more than max history
        for i in range(150):
            monitor.record_checkout_time(0.1)
        
        assert len(monitor.checkout_times) == monitor.max_checkout_history
    
    def test_pool_metrics_calculation(self, monitor):
        """Test pool metrics calculation."""
        # Record some checkout times
        monitor.record_checkout_time(0.1)
        monitor.record_checkout_time(0.2)
        
        with patch('app.db.performance.async_engine') as mock_engine:
            with patch('app.db.performance.session_manager') as mock_session_manager:
                # Mock engine pool
                mock_pool = MagicMock()
                mock_pool.size.return_value = 10
                mock_pool.checkedout.return_value = 3
                mock_pool.overflow.return_value = 2
                mock_pool.checkedin.return_value = 5
                mock_engine.pool = mock_pool
                
                # Mock session manager stats
                mock_session_manager.get_connection_stats.return_value = {
                    "total_connections": 20,
                    "active_connections": 8,
                    "failed_connections": 1,
                    "retry_attempts": 2
                }
                
                metrics = monitor.get_pool_metrics()
                
                assert isinstance(metrics, ConnectionPoolMetrics)
                assert metrics.pool_size == 10
                assert metrics.checked_out == 3
                assert metrics.pool_utilization == 30.0  # 3/10 * 100
                assert metrics.average_checkout_time == 0.15  # (0.1 + 0.2) / 2


class TestDatabasePerformanceAnalyzer:
    """Test cases for DatabasePerformanceAnalyzer class."""
    
    @pytest.fixture
    def analyzer(self):
        """Create a fresh performance analyzer instance for testing."""
        return DatabasePerformanceAnalyzer()
    
    def test_health_assessment_excellent(self, analyzer):
        """Test excellent health assessment."""
        pool_metrics = ConnectionPoolMetrics(
            pool_size=10,
            checked_out=2,
            overflow=0,
            checked_in=8,
            total_connections=100,
            active_connections=10,
            failed_connections=0,
            retry_attempts=0,
            average_checkout_time=0.1,
            pool_utilization=20.0,
            timestamp=datetime.utcnow()
        )
        
        query_stats = {
            "average_execution_time": 0.1,
            "success_rate": 100.0
        }
        
        health = analyzer._assess_overall_health(pool_metrics, query_stats)
        assert health == "excellent"
    
    def test_health_assessment_poor(self, analyzer):
        """Test poor health assessment."""
        pool_metrics = ConnectionPoolMetrics(
            pool_size=10,
            checked_out=9,  # High utilization
            overflow=5,
            checked_in=1,
            total_connections=100,
            active_connections=10,
            failed_connections=20,  # High failure rate
            retry_attempts=50,
            average_checkout_time=2.0,  # Slow checkout
            pool_utilization=90.0,
            timestamp=datetime.utcnow()
        )
        
        query_stats = {
            "average_execution_time": 5.0,  # Slow queries
            "success_rate": 80.0  # Low success rate
        }
        
        health = analyzer._assess_overall_health(pool_metrics, query_stats)
        assert health == "poor"
    
    def test_recommendations_generation(self, analyzer):
        """Test performance recommendations generation."""
        pool_metrics = ConnectionPoolMetrics(
            pool_size=10,
            checked_out=9,  # High utilization
            overflow=0,
            checked_in=1,
            total_connections=100,
            active_connections=10,
            failed_connections=5,  # Some failures
            retry_attempts=10,
            average_checkout_time=1.5,  # Slow checkout
            pool_utilization=90.0,
            timestamp=datetime.utcnow()
        )
        
        query_stats = {
            "average_execution_time": 2.0,  # Slow queries
            "success_rate": 90.0,  # Decent success rate
            "slow_queries_count": 5
        }
        
        recommendations = analyzer._generate_recommendations(pool_metrics, query_stats)
        
        assert len(recommendations) > 0
        assert any("pool size" in rec.lower() for rec in recommendations)
        assert any("connection failures" in rec.lower() for rec in recommendations)
        assert any("checkout time" in rec.lower() for rec in recommendations)
        assert any("query execution" in rec.lower() for rec in recommendations)


class TestOptimizationFunctions:
    """Test cases for optimization utility functions."""
    
    def test_get_optimized_engine_args(self):
        """Test optimized engine args function."""
        args = get_optimized_engine_args()
        
        assert isinstance(args, dict)
        assert "echo" in args
        assert "future" in args
    
    def test_get_optimized_session_args(self):
        """Test optimized session args function."""
        base_args = {"bind": "test_engine"}
        optimized_args = get_optimized_session_args(base_args)
        
        assert isinstance(optimized_args, dict)
        assert "bind" in optimized_args
        assert optimized_args["bind"] == "test_engine"
    
    def test_get_database_health_thresholds(self):
        """Test database health thresholds function."""
        thresholds = get_database_health_thresholds()
        
        assert isinstance(thresholds, dict)
        assert "max_response_time" in thresholds
        assert "max_pool_failure_rate" in thresholds
        assert "min_available_connections" in thresholds
        
        # Verify all values are numeric
        for key, value in thresholds.items():
            assert isinstance(value, (int, float))


class TestPerformanceIntegration:
    """Test cases for performance monitoring integration."""
    
    def test_performance_analyzer_import(self):
        """Test that performance analyzer can be imported."""
        from app.db.performance import performance_analyzer
        
        assert performance_analyzer is not None
        assert hasattr(performance_analyzer, 'query_monitor')
        assert hasattr(performance_analyzer, 'pool_monitor')
        assert hasattr(performance_analyzer, 'generate_performance_report')
    
    def test_config_manager_import(self):
        """Test that config manager can be imported."""
        from app.db.config import db_config_manager
        
        assert db_config_manager is not None
        assert hasattr(db_config_manager, 'environment')
        assert hasattr(db_config_manager, 'database_type')
        assert hasattr(db_config_manager, 'get_engine_args')
