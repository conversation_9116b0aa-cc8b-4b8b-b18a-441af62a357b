"""Initial schema

Revision ID: 4a9bc5c64e13
Revises:
Create Date: 2025-05-24 00:46:31.190589+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '4a9bc5c64e13'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=False),
    sa.Column('last_name', sa.String(length=50), nullable=False),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('is_verified', sa.<PERSON>(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('account_locked_until', sa.DateTime(timezone=True), nullable=True),
    sa.Column('password_reset_token', sa.String(length=255), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True),
    sa.Column('email_verification_token', sa.String(length=255), nullable=True),
    sa.Column('email_verification_expires', sa.DateTime(timezone=True), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('location', sa.String(length=100), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=True),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('profile_visibility', sa.String(length=20), nullable=False),
    sa.Column('email_notifications', sa.Boolean(), nullable=False),
    sa.Column('sms_notifications', sa.Boolean(), nullable=False),
    sa.Column('marketing_emails', sa.Boolean(), nullable=False),
    sa.Column('terms_accepted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('privacy_policy_accepted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index('idx_user_created_role', ['created_at', 'role'], unique=False)
        batch_op.create_index('idx_user_email_active', ['email', 'is_active'], unique=False)
        batch_op.create_index('idx_user_last_login', ['last_login'], unique=False)
        batch_op.create_index('idx_user_role_active', ['role', 'is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_email_verification_token'), ['email_verification_token'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_is_verified'), ['is_verified'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_password_reset_token'), ['password_reset_token'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_phone_number'), ['phone_number'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_role'), ['role'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_uuid'), ['uuid'], unique=True)

    op.create_table('api_keys',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('key_hash', sa.String(length=255), nullable=False),
    sa.Column('scopes', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_used', sa.DateTime(timezone=True), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_api_keys_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_api_keys'))
    )
    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_api_keys_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_key_hash'), ['key_hash'], unique=True)
        batch_op.create_index(batch_op.f('ix_api_keys_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_api_keys_uuid'), ['uuid'], unique=True)

    op.create_table('user_sessions',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('last_activity', sa.DateTime(timezone=True), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_sessions_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_sessions'))
    )
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_sessions_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_session_id'), ['session_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_user_sessions_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_uuid'), ['uuid'], unique=True)

    op.create_table('vendors',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('business_name', sa.String(length=255), nullable=False),
    sa.Column('business_type', sa.String(length=50), nullable=False),
    sa.Column('business_registration_number', sa.String(length=100), nullable=True),
    sa.Column('tax_id', sa.String(length=50), nullable=True),
    sa.Column('verification_status', sa.String(length=30), nullable=False),
    sa.Column('marketplace_status', sa.String(length=20), nullable=False),
    sa.Column('onboarding_completed', sa.Boolean(), nullable=False),
    sa.Column('onboarding_step', sa.Integer(), nullable=False),
    sa.Column('commission_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('total_earnings', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('total_bookings', sa.Integer(), nullable=False),
    sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('total_reviews', sa.Integer(), nullable=False),
    sa.Column('response_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('response_time_hours', sa.Numeric(precision=8, scale=2), nullable=False),
    sa.Column('seo_score', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('marketplace_ranking', sa.Integer(), nullable=False),
    sa.Column('listing_quality_score', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.CheckConstraint('average_rating >= 0.0 AND average_rating <= 5.0', name=op.f('ck_vendors_check_average_rating_range')),
    sa.CheckConstraint('commission_rate >= 0.0 AND commission_rate <= 1.0', name=op.f('ck_vendors_check_commission_rate_range')),
    sa.CheckConstraint('listing_quality_score >= 0.0 AND listing_quality_score <= 100.0', name=op.f('ck_vendors_check_listing_quality_score_range')),
    sa.CheckConstraint('onboarding_step >= 1 AND onboarding_step <= 6', name=op.f('ck_vendors_check_onboarding_step_range')),
    sa.CheckConstraint('response_rate >= 0.0 AND response_rate <= 100.0', name=op.f('ck_vendors_check_response_rate_range')),
    sa.CheckConstraint('seo_score >= 0.0 AND seo_score <= 100.0', name=op.f('ck_vendors_check_seo_score_range')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_vendors_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_vendors'))
    )
    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.create_index('idx_vendor_activity', ['last_activity_at', 'marketplace_status'], unique=False)
        batch_op.create_index('idx_vendor_business_type_status', ['business_type', 'marketplace_status'], unique=False)
        batch_op.create_index('idx_vendor_marketplace_ranking', ['marketplace_ranking', 'business_type'], unique=False)
        batch_op.create_index('idx_vendor_onboarding', ['onboarding_completed', 'onboarding_step'], unique=False)
        batch_op.create_index('idx_vendor_performance', ['average_rating', 'total_bookings'], unique=False)
        batch_op.create_index('idx_vendor_verification_status', ['verification_status', 'created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_business_name'), ['business_name'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_business_registration_number'), ['business_registration_number'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendors_business_type'), ['business_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_last_activity_at'), ['last_activity_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_marketplace_status'), ['marketplace_status'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_onboarding_completed'), ['onboarding_completed'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_tax_id'), ['tax_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendors_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendors_user_id'), ['user_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendors_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendors_verification_status'), ['verification_status'], unique=False)

    op.create_table('vendor_documents',
    sa.Column('vendor_id', sa.Integer(), nullable=False),
    sa.Column('document_type', sa.String(length=50), nullable=False),
    sa.Column('document_name', sa.String(length=255), nullable=False),
    sa.Column('document_url', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('file_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('reviewed_by', sa.Integer(), nullable=True),
    sa.Column('review_notes', sa.Text(), nullable=True),
    sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('document_number', sa.String(length=100), nullable=True),
    sa.Column('issuing_authority', sa.String(length=255), nullable=True),
    sa.Column('verification_data', sa.JSON(), nullable=True),
    sa.Column('is_primary', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], name=op.f('fk_vendor_documents_reviewed_by_users')),
    sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], name=op.f('fk_vendor_documents_vendor_id_vendors'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_vendor_documents')),
    sa.UniqueConstraint('vendor_id', 'document_type', 'is_primary', name='uq_vendor_primary_document')
    )
    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.create_index('idx_vendor_document_expiry', ['expiry_date', 'status'], unique=False)
        batch_op.create_index('idx_vendor_document_review', ['reviewed_at', 'reviewed_by'], unique=False)
        batch_op.create_index('idx_vendor_document_type_status', ['document_type', 'status'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_document_type'), ['document_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_expiry_date'), ['expiry_date'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_documents_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendor_documents_vendor_id'), ['vendor_id'], unique=False)

    op.create_table('vendor_profiles',
    sa.Column('vendor_id', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('short_description', sa.String(length=500), nullable=True),
    sa.Column('tagline', sa.String(length=255), nullable=True),
    sa.Column('contact_phone', sa.String(length=20), nullable=True),
    sa.Column('contact_email', sa.String(length=255), nullable=True),
    sa.Column('website_url', sa.String(length=255), nullable=True),
    sa.Column('business_address', sa.Text(), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=True),
    sa.Column('state', sa.String(length=100), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('postal_code', sa.String(length=20), nullable=True),
    sa.Column('latitude', sa.Numeric(precision=10, scale=8), nullable=True),
    sa.Column('longitude', sa.Numeric(precision=11, scale=8), nullable=True),
    sa.Column('operating_hours', sa.JSON(), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=True),
    sa.Column('social_media_links', sa.JSON(), nullable=True),
    sa.Column('languages_spoken', sa.JSON(), nullable=True),
    sa.Column('specializations', sa.JSON(), nullable=True),
    sa.Column('certifications', sa.JSON(), nullable=True),
    sa.Column('max_group_size', sa.Integer(), nullable=True),
    sa.Column('min_advance_booking', sa.Integer(), nullable=True),
    sa.Column('cancellation_policy', sa.Text(), nullable=True),
    sa.Column('logo_url', sa.String(length=500), nullable=True),
    sa.Column('cover_image_url', sa.String(length=500), nullable=True),
    sa.Column('gallery_images', sa.JSON(), nullable=True),
    sa.Column('years_in_business', sa.Integer(), nullable=True),
    sa.Column('business_license_number', sa.String(length=100), nullable=True),
    sa.Column('insurance_details', sa.JSON(), nullable=True),
    sa.Column('auto_accept_bookings', sa.Boolean(), nullable=False),
    sa.Column('instant_booking_enabled', sa.Boolean(), nullable=False),
    sa.Column('additional_info', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.CheckConstraint('latitude >= -90.0 AND latitude <= 90.0', name=op.f('ck_vendor_profiles_check_latitude_range')),
    sa.CheckConstraint('longitude >= -180.0 AND longitude <= 180.0', name=op.f('ck_vendor_profiles_check_longitude_range')),
    sa.CheckConstraint('max_group_size > 0', name=op.f('ck_vendor_profiles_check_max_group_size_positive')),
    sa.CheckConstraint('min_advance_booking >= 0', name=op.f('ck_vendor_profiles_check_min_advance_booking_non_negative')),
    sa.CheckConstraint('years_in_business >= 0', name=op.f('ck_vendor_profiles_check_years_in_business_non_negative')),
    sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], name=op.f('fk_vendor_profiles_vendor_id_vendors'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_vendor_profiles'))
    )
    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.create_index('idx_vendor_profile_contact', ['contact_phone', 'contact_email'], unique=False)
        batch_op.create_index('idx_vendor_profile_coordinates', ['latitude', 'longitude'], unique=False)
        batch_op.create_index('idx_vendor_profile_location', ['city', 'state', 'country'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_city'), ['city'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_contact_email'), ['contact_email'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_contact_phone'), ['contact_phone'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_country'), ['country'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_state'), ['state'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_vendor_profiles_vendor_id'), ['vendor_id'], unique=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_vendor_id'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_uuid'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_updated_by'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_updated_at'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_state'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_id'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_created_by'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_created_at'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_country'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_contact_phone'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_contact_email'))
        batch_op.drop_index(batch_op.f('ix_vendor_profiles_city'))
        batch_op.drop_index('idx_vendor_profile_location')
        batch_op.drop_index('idx_vendor_profile_coordinates')
        batch_op.drop_index('idx_vendor_profile_contact')

    op.drop_table('vendor_profiles')
    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_vendor_documents_vendor_id'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_uuid'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_updated_by'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_updated_at'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_status'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_id'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_expiry_date'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_document_type'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_created_by'))
        batch_op.drop_index(batch_op.f('ix_vendor_documents_created_at'))
        batch_op.drop_index('idx_vendor_document_type_status')
        batch_op.drop_index('idx_vendor_document_review')
        batch_op.drop_index('idx_vendor_document_expiry')

    op.drop_table('vendor_documents')
    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_vendors_verification_status'))
        batch_op.drop_index(batch_op.f('ix_vendors_uuid'))
        batch_op.drop_index(batch_op.f('ix_vendors_user_id'))
        batch_op.drop_index(batch_op.f('ix_vendors_updated_by'))
        batch_op.drop_index(batch_op.f('ix_vendors_updated_at'))
        batch_op.drop_index(batch_op.f('ix_vendors_tax_id'))
        batch_op.drop_index(batch_op.f('ix_vendors_onboarding_completed'))
        batch_op.drop_index(batch_op.f('ix_vendors_marketplace_status'))
        batch_op.drop_index(batch_op.f('ix_vendors_last_activity_at'))
        batch_op.drop_index(batch_op.f('ix_vendors_id'))
        batch_op.drop_index(batch_op.f('ix_vendors_created_by'))
        batch_op.drop_index(batch_op.f('ix_vendors_created_at'))
        batch_op.drop_index(batch_op.f('ix_vendors_business_type'))
        batch_op.drop_index(batch_op.f('ix_vendors_business_registration_number'))
        batch_op.drop_index(batch_op.f('ix_vendors_business_name'))
        batch_op.drop_index('idx_vendor_verification_status')
        batch_op.drop_index('idx_vendor_performance')
        batch_op.drop_index('idx_vendor_onboarding')
        batch_op.drop_index('idx_vendor_marketplace_ranking')
        batch_op.drop_index('idx_vendor_business_type_status')
        batch_op.drop_index('idx_vendor_activity')

    op.drop_table('vendors')
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_sessions_uuid'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_user_id'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_updated_by'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_updated_at'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_session_id'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_is_active'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_id'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_created_by'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_created_at'))

    op.drop_table('user_sessions')
    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_api_keys_uuid'))
        batch_op.drop_index(batch_op.f('ix_api_keys_user_id'))
        batch_op.drop_index(batch_op.f('ix_api_keys_updated_by'))
        batch_op.drop_index(batch_op.f('ix_api_keys_updated_at'))
        batch_op.drop_index(batch_op.f('ix_api_keys_key_hash'))
        batch_op.drop_index(batch_op.f('ix_api_keys_is_active'))
        batch_op.drop_index(batch_op.f('ix_api_keys_id'))
        batch_op.drop_index(batch_op.f('ix_api_keys_created_by'))
        batch_op.drop_index(batch_op.f('ix_api_keys_created_at'))

    op.drop_table('api_keys')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_uuid'))
        batch_op.drop_index(batch_op.f('ix_users_updated_by'))
        batch_op.drop_index(batch_op.f('ix_users_updated_at'))
        batch_op.drop_index(batch_op.f('ix_users_role'))
        batch_op.drop_index(batch_op.f('ix_users_phone_number'))
        batch_op.drop_index(batch_op.f('ix_users_password_reset_token'))
        batch_op.drop_index(batch_op.f('ix_users_is_verified'))
        batch_op.drop_index(batch_op.f('ix_users_is_active'))
        batch_op.drop_index(batch_op.f('ix_users_id'))
        batch_op.drop_index(batch_op.f('ix_users_email_verification_token'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_created_by'))
        batch_op.drop_index(batch_op.f('ix_users_created_at'))
        batch_op.drop_index('idx_user_role_active')
        batch_op.drop_index('idx_user_last_login')
        batch_op.drop_index('idx_user_email_active')
        batch_op.drop_index('idx_user_created_role')

    op.drop_table('users')
    # ### end Alembic commands ###
