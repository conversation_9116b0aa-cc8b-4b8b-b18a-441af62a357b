"""
Document verification models for Culture Connect Backend API.

This module defines models for document verification workflows, automated validation,
manual review processes, and verification audit trails.

Implements Task 3.1.2: Document Verification System models with status tracking,
workflow management, and comprehensive audit capabilities.
"""

import enum
from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Numeric, Enum, JSON, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit


class VerificationWorkflowStatus(str, enum.Enum):
    """Enumeration of verification workflow statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    RESUBMISSION_REQUIRED = "resubmission_required"
    ESCALATED = "escalated"
    COMPLETED = "completed"


class VerificationPriority(str, enum.Enum):
    """Enumeration of verification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class AutomatedValidationStatus(str, enum.Enum):
    """Enumeration of automated validation statuses."""
    PENDING = "pending"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"
    SKIPPED = "skipped"


class VerificationActionType(str, enum.Enum):
    """Enumeration of verification action types."""
    SUBMITTED = "submitted"
    ASSIGNED = "assigned"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"
    RESUBMISSION_REQUESTED = "resubmission_requested"
    ESCALATED = "escalated"
    COMMENT_ADDED = "comment_added"
    STATUS_CHANGED = "status_changed"


class DocumentVerificationWorkflow(BaseModelWithAudit):
    """
    Document verification workflow model.

    This model manages the complete verification process for vendor documents,
    including automated validation, manual review, and approval workflows.
    """

    __tablename__ = "document_verification_workflows"

    # Core workflow information
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    document_id = Column(
        Integer,
        ForeignKey("vendor_documents.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated document ID"
    )

    workflow_reference = Column(
        String(50),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique workflow reference number"
    )

    # Workflow status and priority
    status = Column(
        Enum(VerificationWorkflowStatus),
        nullable=False,
        default=VerificationWorkflowStatus.PENDING,
        index=True,
        doc="Current workflow status"
    )

    priority = Column(
        Enum(VerificationPriority),
        nullable=False,
        default=VerificationPriority.NORMAL,
        index=True,
        doc="Verification priority level"
    )

    # Assignment and review
    assigned_to = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        index=True,
        doc="ID of admin assigned to review"
    )

    assigned_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp when workflow was assigned"
    )

    reviewed_by = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        doc="ID of admin who completed review"
    )

    reviewed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Timestamp when review was completed"
    )

    # Workflow timing
    submitted_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Timestamp when workflow was submitted"
    )

    due_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Expected completion date"
    )

    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Timestamp when workflow was completed"
    )

    # Review details
    review_notes = Column(
        Text,
        nullable=True,
        doc="Review notes and feedback"
    )

    rejection_reason = Column(
        Text,
        nullable=True,
        doc="Reason for rejection if applicable"
    )

    escalation_reason = Column(
        Text,
        nullable=True,
        doc="Reason for escalation if applicable"
    )

    # Automated validation results
    automated_validation_status = Column(
        Enum(AutomatedValidationStatus),
        nullable=False,
        default=AutomatedValidationStatus.PENDING,
        index=True,
        doc="Status of automated validation"
    )

    automated_validation_score = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Automated validation confidence score (0.00-100.00)"
    )

    automated_validation_results = Column(
        JSON,
        nullable=True,
        doc="Detailed automated validation results"
    )

    # Workflow metadata
    workflow_data = Column(
        JSON,
        nullable=True,
        doc="Additional workflow metadata"
    )

    requires_manual_review = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether manual review is required"
    )

    is_expedited = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is an expedited workflow"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        doc="Associated vendor"
    )

    document = relationship(
        "VendorDocument",
        doc="Associated document"
    )

    assigned_admin = relationship(
        "User",
        foreign_keys=[assigned_to],
        doc="Admin assigned to review"
    )

    reviewing_admin = relationship(
        "User",
        foreign_keys=[reviewed_by],
        doc="Admin who completed review"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "automated_validation_score >= 0.0 AND automated_validation_score <= 100.0",
            name="check_validation_score_range"
        ),

        # Performance indexes
        Index("idx_workflow_status_priority", "status", "priority"),
        Index("idx_workflow_assigned", "assigned_to", "assigned_at"),
        Index("idx_workflow_due_date", "due_date", "status"),
        Index("idx_workflow_vendor_status", "vendor_id", "status"),
        Index("idx_workflow_automation", "automated_validation_status", "requires_manual_review"),
    )

    def __repr__(self) -> str:
        """String representation of the verification workflow."""
        return f"<DocumentVerificationWorkflow(id={self.id}, ref='{self.workflow_reference}', status='{self.status}')>"

    @property
    def is_overdue(self) -> bool:
        """Check if workflow is overdue."""
        if not self.due_date or self.completed_at:
            return False
        return datetime.utcnow() > self.due_date.replace(tzinfo=None)

    @property
    def processing_time_hours(self) -> Optional[float]:
        """Calculate processing time in hours."""
        if not self.completed_at:
            return None

        start_time = self.submitted_at.replace(tzinfo=None)
        end_time = self.completed_at.replace(tzinfo=None)
        delta = end_time - start_time
        return delta.total_seconds() / 3600.0

    @property
    def is_automated_validation_passed(self) -> bool:
        """Check if automated validation passed."""
        return self.automated_validation_status == AutomatedValidationStatus.PASSED


class VerificationHistory(BaseModelWithAudit):
    """
    Verification history model for audit trail.

    This model tracks all actions and changes in the verification workflow
    for comprehensive audit and compliance purposes.
    """

    __tablename__ = "verification_history"

    # Core history information
    workflow_id = Column(
        Integer,
        ForeignKey("document_verification_workflows.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated workflow ID"
    )

    action_type = Column(
        Enum(VerificationActionType),
        nullable=False,
        index=True,
        doc="Type of action performed"
    )

    # Action details
    performed_by = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        index=True,
        doc="ID of user who performed the action"
    )

    performed_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Timestamp when action was performed"
    )

    # Action data
    action_description = Column(
        Text,
        nullable=True,
        doc="Description of the action performed"
    )

    previous_status = Column(
        String(50),
        nullable=True,
        doc="Previous status before action"
    )

    new_status = Column(
        String(50),
        nullable=True,
        doc="New status after action"
    )

    action_data = Column(
        JSON,
        nullable=True,
        doc="Additional action data and metadata"
    )

    # System information
    ip_address = Column(
        String(45),
        nullable=True,
        doc="IP address of user performing action"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string"
    )

    # Relationships
    workflow = relationship(
        "DocumentVerificationWorkflow",
        doc="Associated verification workflow"
    )

    user = relationship(
        "User",
        foreign_keys=[performed_by],
        doc="User who performed the action"
    )

    # Constraints and indexes
    __table_args__ = (
        # Performance indexes
        Index("idx_history_workflow_action", "workflow_id", "action_type"),
        Index("idx_history_user_action", "performed_by", "performed_at"),
        Index("idx_history_action_time", "action_type", "performed_at"),
    )

    def __repr__(self) -> str:
        """String representation of the verification history."""
        return f"<VerificationHistory(id={self.id}, workflow_id={self.workflow_id}, action='{self.action_type}')>"


class AutomatedValidationRule(BaseModelWithAudit):
    """
    Automated validation rule model.

    This model defines rules for automated document validation including
    OCR text extraction, format validation, and business logic checks.
    """

    __tablename__ = "automated_validation_rules"

    # Rule identification
    rule_name = Column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique rule name"
    )

    rule_description = Column(
        Text,
        nullable=True,
        doc="Description of the validation rule"
    )

    # Rule configuration
    document_types = Column(
        JSON,
        nullable=False,
        doc="Document types this rule applies to"
    )

    vendor_types = Column(
        JSON,
        nullable=True,
        doc="Vendor types this rule applies to (null = all types)"
    )

    # Rule logic
    validation_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of validation (ocr, format, business_logic, etc.)"
    )

    rule_config = Column(
        JSON,
        nullable=False,
        doc="Rule configuration and parameters"
    )

    # Rule status and priority
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Whether rule is active"
    )

    priority = Column(
        Integer,
        nullable=False,
        default=100,
        doc="Rule execution priority (lower = higher priority)"
    )

    # Rule performance
    success_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Rule success rate percentage"
    )

    total_executions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of rule executions"
    )

    successful_executions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of successful executions"
    )

    # Rule metadata
    created_by = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        doc="ID of user who created the rule"
    )

    last_executed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Timestamp of last execution"
    )

    # Relationships
    creator = relationship(
        "User",
        foreign_keys=[created_by],
        doc="User who created the rule"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "success_rate >= 0.0 AND success_rate <= 100.0",
            name="check_success_rate_range"
        ),
        CheckConstraint(
            "successful_executions <= total_executions",
            name="check_execution_counts"
        ),

        # Performance indexes
        Index("idx_rule_type_active", "validation_type", "is_active"),
        Index("idx_rule_priority", "priority", "is_active"),
        Index("idx_rule_performance", "success_rate", "total_executions"),
    )

    def __repr__(self) -> str:
        """String representation of the validation rule."""
        return f"<AutomatedValidationRule(id={self.id}, name='{self.rule_name}', type='{self.validation_type}')>"

    @property
    def calculated_success_rate(self) -> float:
        """Calculate current success rate."""
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100.0
