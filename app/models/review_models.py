"""
Review Management models for Culture Connect Backend API.

This module defines comprehensive review and rating models including:
- Review: Customer feedback and rating system with booking validation
- ReviewResponse: Vendor response system with status management
- ReviewModeration: AI-powered content moderation with confidence scoring
- ReviewAnalytics: Vendor performance metrics and rating distribution

Implements Task 4.4.1 requirements for review management system with:
- Complete review lifecycle management
- Vendor response capabilities
- AI-powered content moderation workflow
- Review analytics and performance tracking
- Integration with booking, user, and service systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import enum
from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Date, Text, ForeignKey,
    Numeric, Enum, JSON, Index, CheckConstraint, UniqueConstraint, Float
)
from sqlalchemy.orm import relationship, validates
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit


class ReviewStatus(enum.Enum):
    """Review status enumeration."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    FLAGGED = "flagged"
    HIDDEN = "hidden"


class ResponseStatus(enum.Enum):
    """Review response status enumeration."""
    DRAFT = "draft"
    PUBLISHED = "published"
    HIDDEN = "hidden"


class ModerationAction(enum.Enum):
    """Review moderation action enumeration."""
    APPROVE = "approve"
    REJECT = "reject"
    FLAG = "flag"
    REQUEST_EDIT = "request_edit"
    ESCALATE = "escalate"


class Review(BaseModelWithAudit):
    """
    Core review model for customer feedback and ratings.

    This model manages customer reviews for completed bookings including
    rating scores, content moderation, sentiment analysis, and vendor responses.
    """

    __tablename__ = "reviews"

    # Core review information
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,  # One review per booking
        index=True,
        doc="Associated booking ID (unique constraint)"
    )

    customer_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Customer who wrote the review"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Vendor being reviewed"
    )

    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Service being reviewed"
    )

    # Review content
    rating = Column(
        Integer,
        nullable=False,
        doc="Rating score (1-5 scale)"
    )

    title = Column(
        String(200),
        nullable=False,
        doc="Review title"
    )

    content = Column(
        Text,
        nullable=False,
        doc="Review content (max 2000 chars)"
    )

    # Review status and moderation
    status = Column(
        Enum(ReviewStatus),
        nullable=False,
        default=ReviewStatus.PENDING,
        index=True,
        doc="Review moderation status"
    )

    moderation_reason = Column(
        Text,
        nullable=True,
        doc="Reason for rejection or flagging"
    )

    # Verification and authenticity
    is_verified_purchase = Column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Whether review is from verified purchase"
    )

    # Community engagement
    helpful_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of helpful votes"
    )

    reported_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times reported"
    )

    # AI analysis
    sentiment_score = Column(
        Float,
        nullable=True,
        doc="AI-generated sentiment score (-1.0 to 1.0)"
    )

    language_code = Column(
        String(10),
        nullable=True,
        doc="Auto-detected language code"
    )

    # Additional metadata
    review_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional review metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="review",
        doc="Associated booking"
    )

    customer = relationship(
        "User",
        foreign_keys=[customer_id],
        back_populates="customer_reviews",
        doc="Customer who wrote the review"
    )

    vendor = relationship(
        "Vendor",
        back_populates="reviews",
        doc="Vendor being reviewed"
    )

    service = relationship(
        "Service",
        back_populates="reviews",
        doc="Service being reviewed"
    )

    responses = relationship(
        "ReviewResponse",
        back_populates="review",
        cascade="all, delete-orphan",
        order_by="ReviewResponse.created_at.desc()",
        doc="Vendor responses to this review"
    )

    moderation_records = relationship(
        "ReviewModeration",
        back_populates="review",
        cascade="all, delete-orphan",
        order_by="ReviewModeration.created_at.desc()",
        doc="Moderation history for this review"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "rating >= 1 AND rating <= 5",
            name="check_review_rating_range"
        ),
        CheckConstraint(
            "LENGTH(title) <= 200",
            name="check_review_title_length"
        ),
        CheckConstraint(
            "LENGTH(content) <= 2000",
            name="check_review_content_length"
        ),
        CheckConstraint(
            "helpful_count >= 0",
            name="check_review_helpful_count_non_negative"
        ),
        CheckConstraint(
            "reported_count >= 0",
            name="check_review_reported_count_non_negative"
        ),
        CheckConstraint(
            "sentiment_score IS NULL OR (sentiment_score >= -1.0 AND sentiment_score <= 1.0)",
            name="check_review_sentiment_score_range"
        ),
        # Performance indexes
        Index('idx_reviews_vendor_rating', 'vendor_id', 'rating', 'created_at'),
        Index('idx_reviews_service_rating', 'service_id', 'rating', 'created_at'),
        Index('idx_reviews_status_created', 'status', 'created_at'),
        Index('idx_reviews_customer_created', 'customer_id', 'created_at'),
        Index('idx_reviews_verified_rating', 'is_verified_purchase', 'rating'),
        Index('idx_reviews_sentiment_rating', 'sentiment_score', 'rating'),
        # Full-text search index for content (using btree for compatibility)
        Index('idx_reviews_content_search', 'title', 'content'),
    )

    @validates('rating')
    def validate_rating(self, key, rating):
        """Validate rating is within 1-5 range."""
        if rating is not None and (rating < 1 or rating > 5):
            raise ValueError("Rating must be between 1 and 5")
        return rating

    @validates('title')
    def validate_title(self, key, title):
        """Validate title length."""
        if title and len(title) > 200:
            raise ValueError("Title cannot exceed 200 characters")
        return title

    @validates('content')
    def validate_content(self, key, content):
        """Validate content length."""
        if content and len(content) > 2000:
            raise ValueError("Content cannot exceed 2000 characters")
        return content

    @validates('sentiment_score')
    def validate_sentiment_score(self, key, score):
        """Validate sentiment score range."""
        if score is not None and (score < -1.0 or score > 1.0):
            raise ValueError("Sentiment score must be between -1.0 and 1.0")
        return score

    @hybrid_property
    def is_positive_review(self) -> bool:
        """Check if review is positive (rating >= 4)."""
        return self.rating >= 4

    @hybrid_property
    def has_vendor_response(self) -> bool:
        """Check if vendor has responded to this review."""
        return len(self.responses) > 0

    def __repr__(self) -> str:
        return f"<Review(id={self.id}, booking_id={self.booking_id}, rating={self.rating})>"


class ReviewResponse(BaseModelWithAudit):
    """
    Review response model for vendor replies to customer reviews.

    This model manages vendor responses to customer reviews including
    content management, status tracking, and publication workflow.
    """

    __tablename__ = "review_responses"

    # Core response information
    review_id = Column(
        Integer,
        ForeignKey("reviews.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated review ID"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Vendor who wrote the response"
    )

    # Response content
    content = Column(
        Text,
        nullable=False,
        doc="Response content (max 1000 chars)"
    )

    # Response status
    status = Column(
        Enum(ResponseStatus),
        nullable=False,
        default=ResponseStatus.DRAFT,
        index=True,
        doc="Response publication status"
    )

    # Response properties
    is_official_response = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether this is an official vendor response"
    )

    # Response metadata
    response_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional response metadata"
    )

    # Relationships
    review = relationship(
        "Review",
        back_populates="responses",
        doc="Associated review"
    )

    vendor = relationship(
        "Vendor",
        back_populates="review_responses",
        doc="Vendor who wrote the response"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "LENGTH(content) <= 1000",
            name="check_response_content_length"
        ),
        # Performance indexes
        Index('idx_review_responses_review_status', 'review_id', 'status'),
        Index('idx_review_responses_vendor_created', 'vendor_id', 'created_at'),
        Index('idx_review_responses_status_created', 'status', 'created_at'),
    )

    @validates('content')
    def validate_content(self, key, content):
        """Validate content length."""
        if content and len(content) > 1000:
            raise ValueError("Response content cannot exceed 1000 characters")
        return content

    def __repr__(self) -> str:
        return f"<ReviewResponse(id={self.id}, review_id={self.review_id}, status='{self.status.value}')>"


class ReviewModeration(BaseModelWithAudit):
    """
    Review moderation model for AI-powered content moderation workflow.

    This model manages the review moderation process including AI analysis,
    manual review decisions, and moderation audit trail.
    """

    __tablename__ = "review_moderation"

    # Core moderation information
    review_id = Column(
        Integer,
        ForeignKey("reviews.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated review ID"
    )

    moderator_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Admin who performed manual moderation"
    )

    # Moderation decision
    action = Column(
        Enum(ModerationAction),
        nullable=False,
        index=True,
        doc="Moderation action taken"
    )

    reason = Column(
        Text,
        nullable=True,
        doc="Reason for moderation action"
    )

    # AI analysis
    ai_confidence_score = Column(
        Float,
        nullable=True,
        doc="AI confidence score (0.0 to 1.0)"
    )

    manual_review_required = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether manual review is required"
    )

    # Processing timestamps
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When moderation was processed"
    )

    # AI analysis results
    ai_analysis_results = Column(
        JSON,
        nullable=True,
        doc="Detailed AI analysis results"
    )

    # Moderation metadata
    moderation_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional moderation metadata"
    )

    # Relationships
    review = relationship(
        "Review",
        back_populates="moderation_records",
        doc="Associated review"
    )

    moderator = relationship(
        "User",
        foreign_keys=[moderator_id],
        doc="Admin who performed moderation"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "ai_confidence_score IS NULL OR (ai_confidence_score >= 0.0 AND ai_confidence_score <= 1.0)",
            name="check_moderation_ai_confidence_range"
        ),
        # Performance indexes
        Index('idx_review_moderation_review_action', 'review_id', 'action'),
        Index('idx_review_moderation_action_processed', 'action', 'processed_at'),
        Index('idx_review_moderation_manual_required', 'manual_review_required', 'created_at'),
        Index('idx_review_moderation_moderator_processed', 'moderator_id', 'processed_at'),
    )

    @validates('ai_confidence_score')
    def validate_ai_confidence_score(self, key, score):
        """Validate AI confidence score range."""
        if score is not None and (score < 0.0 or score > 1.0):
            raise ValueError("AI confidence score must be between 0.0 and 1.0")
        return score

    def __repr__(self) -> str:
        return f"<ReviewModeration(id={self.id}, review_id={self.review_id}, action='{self.action.value}')>"


class ReviewAnalytics(BaseModelWithAudit):
    """
    Review analytics model for vendor performance metrics and rating distribution.

    This model stores aggregated review analytics for vendors including
    rating distributions, sentiment analysis, response metrics, and performance trends.
    """

    __tablename__ = "review_analytics"

    # Core analytics information
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Vendor for analytics"
    )

    # Analytics period
    period_start = Column(
        Date,
        nullable=False,
        index=True,
        doc="Analytics period start date"
    )

    period_end = Column(
        Date,
        nullable=False,
        index=True,
        doc="Analytics period end date"
    )

    # Review metrics
    total_reviews = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of reviews in period"
    )

    average_rating = Column(
        Numeric(3, 2),
        nullable=True,
        doc="Average rating for the period"
    )

    # Rating distribution (1-5 stars)
    rating_distribution = Column(
        JSON,
        nullable=True,
        doc="Rating distribution breakdown (1-5 star counts)"
    )

    # Sentiment analysis
    sentiment_breakdown = Column(
        JSON,
        nullable=True,
        doc="Sentiment breakdown (positive/neutral/negative counts)"
    )

    # Response metrics
    response_rate = Column(
        Numeric(5, 4),
        nullable=True,
        doc="Vendor response rate (0.0 to 1.0)"
    )

    average_response_time = Column(
        Integer,
        nullable=True,
        doc="Average response time in hours"
    )

    # Quality metrics
    verified_reviews_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of verified purchase reviews"
    )

    helpful_votes_total = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total helpful votes received"
    )

    reported_reviews_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of reported reviews"
    )

    # Performance trends
    rating_trend = Column(
        String(20),
        nullable=True,
        doc="Rating trend (improving, declining, stable)"
    )

    review_volume_trend = Column(
        String(20),
        nullable=True,
        doc="Review volume trend (increasing, decreasing, stable)"
    )

    # Analytics metadata
    analytics_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional analytics metadata"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="review_analytics",
        doc="Vendor for analytics"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "total_reviews >= 0",
            name="check_analytics_total_reviews_non_negative"
        ),
        CheckConstraint(
            "average_rating IS NULL OR (average_rating >= 1.00 AND average_rating <= 5.00)",
            name="check_analytics_average_rating_range"
        ),
        CheckConstraint(
            "response_rate IS NULL OR (response_rate >= 0.0 AND response_rate <= 1.0)",
            name="check_analytics_response_rate_range"
        ),
        CheckConstraint(
            "average_response_time IS NULL OR average_response_time >= 0",
            name="check_analytics_response_time_non_negative"
        ),
        CheckConstraint(
            "verified_reviews_count >= 0",
            name="check_analytics_verified_reviews_non_negative"
        ),
        CheckConstraint(
            "helpful_votes_total >= 0",
            name="check_analytics_helpful_votes_non_negative"
        ),
        CheckConstraint(
            "reported_reviews_count >= 0",
            name="check_analytics_reported_reviews_non_negative"
        ),
        CheckConstraint(
            "period_start <= period_end",
            name="check_analytics_period_valid"
        ),
        # Unique constraint for vendor-period combination
        UniqueConstraint(
            'vendor_id', 'period_start', 'period_end',
            name='uq_review_analytics_vendor_period'
        ),
        # Performance indexes
        Index('idx_review_analytics_vendor_period', 'vendor_id', 'period_start', 'period_end'),
        Index('idx_review_analytics_period_rating', 'period_start', 'period_end', 'average_rating'),
        Index('idx_review_analytics_vendor_rating', 'vendor_id', 'average_rating'),
    )

    @validates('average_rating')
    def validate_average_rating(self, key, rating):
        """Validate average rating range."""
        if rating is not None and (rating < 1.00 or rating > 5.00):
            raise ValueError("Average rating must be between 1.00 and 5.00")
        return rating

    @validates('response_rate')
    def validate_response_rate(self, key, rate):
        """Validate response rate range."""
        if rate is not None and (rate < 0.0 or rate > 1.0):
            raise ValueError("Response rate must be between 0.0 and 1.0")
        return rate

    @hybrid_property
    def review_quality_score(self) -> Optional[float]:
        """Calculate review quality score based on various metrics."""
        if self.total_reviews == 0:
            return None

        # Base score from average rating (normalized to 0-1)
        rating_score = (self.average_rating - 1) / 4 if self.average_rating else 0

        # Verified reviews bonus
        verified_ratio = self.verified_reviews_count / self.total_reviews
        verified_bonus = verified_ratio * 0.1

        # Helpful votes bonus
        helpful_ratio = min(self.helpful_votes_total / self.total_reviews, 1.0)
        helpful_bonus = helpful_ratio * 0.1

        # Reported reviews penalty
        reported_ratio = self.reported_reviews_count / self.total_reviews
        reported_penalty = reported_ratio * 0.2

        quality_score = rating_score + verified_bonus + helpful_bonus - reported_penalty
        return max(0.0, min(1.0, quality_score))

    def __repr__(self) -> str:
        return f"<ReviewAnalytics(id={self.id}, vendor_id={self.vendor_id}, period={self.period_start}-{self.period_end})>"
