"""
Advanced database configuration and optimization for Culture Connect Backend API.

This module provides production-grade database configuration including:
- Environment-specific connection pool optimization
- SSL configuration for production environments
- Query timeout and performance tuning
- Connection retry and failover logic
- Database-specific optimizations for PostgreSQL
"""

import logging
import ssl
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)


class DatabaseEnvironment(Enum):
    """Database environment types for configuration optimization."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class DatabaseType(Enum):
    """Supported database types."""
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    MYSQL = "mysql"


@dataclass
class ConnectionPoolConfig:
    """Database connection pool configuration."""
    pool_size: int
    max_overflow: int
    pool_timeout: int
    pool_recycle: int
    pool_pre_ping: bool
    pool_reset_on_return: str


@dataclass
class SSLConfig:
    """SSL configuration for database connections."""
    enabled: bool
    mode: str
    cert_file: Optional[str] = None
    key_file: Optional[str] = None
    ca_file: Optional[str] = None
    check_hostname: bool = True


@dataclass
class QueryConfig:
    """Query execution configuration."""
    statement_timeout: int  # seconds
    lock_timeout: int  # seconds
    idle_in_transaction_session_timeout: int  # seconds
    query_timeout: int  # seconds


@dataclass
class DatabaseOptimizationConfig:
    """Database-specific optimization settings."""
    connection_pool: ConnectionPoolConfig
    ssl: SSLConfig
    query: QueryConfig
    echo: bool
    echo_pool: bool
    future: bool
    isolation_level: Optional[str] = None


class DatabaseConfigManager:
    """
    Advanced database configuration manager.
    
    Provides environment-specific database configuration with production-grade
    optimizations, SSL support, and performance tuning.
    """
    
    def __init__(self):
        self.environment = self._detect_environment()
        self.database_type = self._detect_database_type()
        
    def _detect_environment(self) -> DatabaseEnvironment:
        """Detect current environment from settings."""
        env_name = settings.ENVIRONMENT.lower()
        
        if env_name in ["prod", "production"]:
            return DatabaseEnvironment.PRODUCTION
        elif env_name in ["stage", "staging"]:
            return DatabaseEnvironment.STAGING
        elif env_name in ["test", "testing"]:
            return DatabaseEnvironment.TESTING
        else:
            return DatabaseEnvironment.DEVELOPMENT
    
    def _detect_database_type(self) -> DatabaseType:
        """Detect database type from connection URL."""
        url = settings.DATABASE_URL.lower()
        
        if url.startswith("postgresql"):
            return DatabaseType.POSTGRESQL
        elif url.startswith("sqlite"):
            return DatabaseType.SQLITE
        elif url.startswith("mysql"):
            return DatabaseType.MYSQL
        else:
            # Default to PostgreSQL for unknown types
            return DatabaseType.POSTGRESQL
    
    def get_connection_pool_config(self) -> ConnectionPoolConfig:
        """
        Get optimized connection pool configuration based on environment.
        
        Returns:
            ConnectionPoolConfig: Optimized pool configuration
        """
        if self.environment == DatabaseEnvironment.PRODUCTION:
            return ConnectionPoolConfig(
                pool_size=20,  # Higher pool size for production
                max_overflow=30,  # Allow more overflow connections
                pool_timeout=60,  # Longer timeout for high load
                pool_recycle=3600,  # Recycle connections every hour
                pool_pre_ping=True,  # Always verify connections
                pool_reset_on_return="commit"  # Reset connections on return
            )
        elif self.environment == DatabaseEnvironment.STAGING:
            return ConnectionPoolConfig(
                pool_size=10,
                max_overflow=15,
                pool_timeout=30,
                pool_recycle=1800,  # 30 minutes
                pool_pre_ping=True,
                pool_reset_on_return="commit"
            )
        elif self.environment == DatabaseEnvironment.TESTING:
            return ConnectionPoolConfig(
                pool_size=1,  # Minimal pool for testing
                max_overflow=2,
                pool_timeout=10,
                pool_recycle=300,  # 5 minutes
                pool_pre_ping=False,  # Skip pre-ping for speed
                pool_reset_on_return="rollback"  # Rollback for test isolation
            )
        else:  # DEVELOPMENT
            return ConnectionPoolConfig(
                pool_size=5,
                max_overflow=10,
                pool_timeout=20,
                pool_recycle=600,  # 10 minutes
                pool_pre_ping=True,
                pool_reset_on_return="commit"
            )
    
    def get_ssl_config(self) -> SSLConfig:
        """
        Get SSL configuration based on environment and settings.
        
        Returns:
            SSLConfig: SSL configuration
        """
        # SSL is required for production environments
        if self.environment == DatabaseEnvironment.PRODUCTION:
            return SSLConfig(
                enabled=True,
                mode="require",  # Require SSL connection
                cert_file=getattr(settings, 'DATABASE_SSL_CERT', None),
                key_file=getattr(settings, 'DATABASE_SSL_KEY', None),
                ca_file=getattr(settings, 'DATABASE_SSL_CA', None),
                check_hostname=True
            )
        elif self.environment == DatabaseEnvironment.STAGING:
            return SSLConfig(
                enabled=True,
                mode="prefer",  # Prefer SSL but allow non-SSL
                check_hostname=False  # Less strict for staging
            )
        else:
            # Development and testing - SSL optional
            return SSLConfig(
                enabled=False,
                mode="disable",
                check_hostname=False
            )
    
    def get_query_config(self) -> QueryConfig:
        """
        Get query execution configuration based on environment.
        
        Returns:
            QueryConfig: Query configuration
        """
        if self.environment == DatabaseEnvironment.PRODUCTION:
            return QueryConfig(
                statement_timeout=300,  # 5 minutes max per statement
                lock_timeout=60,  # 1 minute max for locks
                idle_in_transaction_session_timeout=600,  # 10 minutes idle timeout
                query_timeout=120  # 2 minutes query timeout
            )
        elif self.environment == DatabaseEnvironment.TESTING:
            return QueryConfig(
                statement_timeout=30,  # Shorter timeouts for tests
                lock_timeout=10,
                idle_in_transaction_session_timeout=60,
                query_timeout=30
            )
        else:
            return QueryConfig(
                statement_timeout=120,  # 2 minutes
                lock_timeout=30,
                idle_in_transaction_session_timeout=300,  # 5 minutes
                query_timeout=60
            )
    
    def get_database_optimization_config(self) -> DatabaseOptimizationConfig:
        """
        Get complete database optimization configuration.
        
        Returns:
            DatabaseOptimizationConfig: Complete optimization configuration
        """
        return DatabaseOptimizationConfig(
            connection_pool=self.get_connection_pool_config(),
            ssl=self.get_ssl_config(),
            query=self.get_query_config(),
            echo=self.environment == DatabaseEnvironment.DEVELOPMENT,
            echo_pool=self.environment == DatabaseEnvironment.DEVELOPMENT,
            future=True,  # Always use SQLAlchemy 2.0 style
            isolation_level=self._get_isolation_level()
        )
    
    def _get_isolation_level(self) -> Optional[str]:
        """Get appropriate isolation level for the database type."""
        if self.database_type == DatabaseType.POSTGRESQL:
            if self.environment == DatabaseEnvironment.PRODUCTION:
                return "READ_COMMITTED"  # Default for PostgreSQL
            else:
                return "READ_COMMITTED"
        elif self.database_type == DatabaseType.MYSQL:
            return "READ_COMMITTED"
        else:
            return None  # SQLite doesn't support isolation levels
    
    def get_engine_args(self) -> Dict[str, Any]:
        """
        Get SQLAlchemy engine arguments with optimizations.
        
        Returns:
            Dict[str, Any]: Engine configuration arguments
        """
        config = self.get_database_optimization_config()
        pool_config = config.connection_pool
        
        engine_args = {
            "echo": config.echo,
            "echo_pool": config.echo_pool,
            "future": config.future,
        }
        
        # Add isolation level if specified
        if config.isolation_level:
            engine_args["isolation_level"] = config.isolation_level
        
        # Add connection pool settings for non-SQLite databases
        if self.database_type != DatabaseType.SQLITE:
            engine_args.update({
                "pool_size": pool_config.pool_size,
                "max_overflow": pool_config.max_overflow,
                "pool_timeout": pool_config.pool_timeout,
                "pool_recycle": pool_config.pool_recycle,
                "pool_pre_ping": pool_config.pool_pre_ping,
                "pool_reset_on_return": pool_config.pool_reset_on_return,
            })
        
        # Add SSL configuration for PostgreSQL
        if self.database_type == DatabaseType.POSTGRESQL and config.ssl.enabled:
            connect_args = {}
            
            if config.ssl.mode:
                connect_args["sslmode"] = config.ssl.mode
            
            if config.ssl.cert_file:
                connect_args["sslcert"] = config.ssl.cert_file
            
            if config.ssl.key_file:
                connect_args["sslkey"] = config.ssl.key_file
            
            if config.ssl.ca_file:
                connect_args["sslrootcert"] = config.ssl.ca_file
            
            if connect_args:
                engine_args["connect_args"] = connect_args
        
        return engine_args
    
    def get_postgresql_optimizations(self) -> Dict[str, str]:
        """
        Get PostgreSQL-specific optimization parameters.
        
        Returns:
            Dict[str, str]: PostgreSQL configuration parameters
        """
        config = self.get_query_config()
        
        optimizations = {
            "statement_timeout": f"{config.statement_timeout}s",
            "lock_timeout": f"{config.lock_timeout}s",
            "idle_in_transaction_session_timeout": f"{config.idle_in_transaction_session_timeout}s",
        }
        
        # Add production-specific optimizations
        if self.environment == DatabaseEnvironment.PRODUCTION:
            optimizations.update({
                "shared_preload_libraries": "pg_stat_statements",
                "log_statement": "mod",
                "log_min_duration_statement": "1000",  # Log slow queries (>1s)
                "log_checkpoints": "on",
                "log_connections": "on",
                "log_disconnections": "on",
                "log_lock_waits": "on",
            })
        
        return optimizations
    
    def apply_session_optimizations(self, session_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply session-level optimizations.
        
        Args:
            session_args: Base session arguments
            
        Returns:
            Dict[str, Any]: Optimized session arguments
        """
        optimized_args = session_args.copy()
        
        # Environment-specific session optimizations
        if self.environment == DatabaseEnvironment.PRODUCTION:
            optimized_args.update({
                "expire_on_commit": False,  # Keep objects accessible after commit
                "autoflush": False,  # Manual flush control for performance
                "autocommit": False,  # Explicit transaction control
            })
        elif self.environment == DatabaseEnvironment.TESTING:
            optimized_args.update({
                "expire_on_commit": True,  # Ensure test isolation
                "autoflush": True,  # Immediate flushing for test predictability
                "autocommit": False,
            })
        else:
            optimized_args.update({
                "expire_on_commit": False,
                "autoflush": False,
                "autocommit": False,
            })
        
        return optimized_args


# Global configuration manager instance
db_config_manager = DatabaseConfigManager()


def get_optimized_engine_args() -> Dict[str, Any]:
    """
    Get optimized engine arguments for the current environment.
    
    Returns:
        Dict[str, Any]: Optimized engine configuration
    """
    return db_config_manager.get_engine_args()


def get_optimized_session_args(base_args: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get optimized session arguments for the current environment.
    
    Args:
        base_args: Base session arguments to optimize
        
    Returns:
        Dict[str, Any]: Optimized session configuration
    """
    base_args = base_args or {}
    return db_config_manager.apply_session_optimizations(base_args)


def get_database_health_thresholds() -> Dict[str, float]:
    """
    Get database health check thresholds based on environment.
    
    Returns:
        Dict[str, float]: Health check thresholds
    """
    env = db_config_manager.environment
    
    if env == DatabaseEnvironment.PRODUCTION:
        return {
            "max_response_time": 2.0,  # 2 seconds max
            "max_pool_failure_rate": 0.05,  # 5% max failure rate
            "min_available_connections": 0.2,  # 20% min available
        }
    elif env == DatabaseEnvironment.STAGING:
        return {
            "max_response_time": 3.0,
            "max_pool_failure_rate": 0.1,  # 10% max failure rate
            "min_available_connections": 0.1,
        }
    else:
        return {
            "max_response_time": 5.0,
            "max_pool_failure_rate": 0.2,  # 20% max failure rate
            "min_available_connections": 0.1,
        }


# Export commonly used items
__all__ = [
    'DatabaseEnvironment',
    'DatabaseType',
    'ConnectionPoolConfig',
    'SSLConfig',
    'QueryConfig',
    'DatabaseOptimizationConfig',
    'DatabaseConfigManager',
    'db_config_manager',
    'get_optimized_engine_args',
    'get_optimized_session_args',
    'get_database_health_thresholds',
]
