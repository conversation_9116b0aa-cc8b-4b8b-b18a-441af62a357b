# Culture Connect Backend - CI/CD Pipeline
# Comprehensive automated build, test, and deployment pipeline
# Supports multi-environment deployment with security scanning and quality gates

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # =============================================================================
  # CODE QUALITY AND SECURITY CHECKS
  # =============================================================================
  
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 black isort bandit safety pytest-cov

    - name: Code formatting check (Black)
      run: black --check --diff .

    - name: Import sorting check (isort)
      run: isort --check-only --diff .

    - name: Linting (Flake8)
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

    - name: Security scan (Bandit)
      run: bandit -r app/ -f json -o bandit-report.json || true

    - name: Dependency vulnerability scan (Safety)
      run: safety check --json --output safety-report.json || true

    - name: Generate version
      id: version
      run: |
        if [[ ${{ github.event_name }} == 'release' ]]; then
          VERSION=${{ github.event.release.tag_name }}
        elif [[ ${{ github.ref }} == 'refs/heads/main' ]]; then
          VERSION="v$(date +%Y%m%d)-${GITHUB_SHA::8}"
        else
          VERSION="${GITHUB_REF_NAME}-${GITHUB_SHA::8}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Generated version: $VERSION"

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # =============================================================================
  # AUTOMATED TESTING
  # =============================================================================
  
  test:
    name: Automated Testing
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: culture_connect_test
          POSTGRES_DB: culture_connect_test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov pytest-mock

    - name: Set up test environment
      run: |
        cp environments/development/.env.development .env
        mkdir -p logs data uploads

    - name: Run database migrations
      run: |
        export CC_DATABASE_URL="postgresql+asyncpg://culture_connect_test:test_password@localhost:5432/culture_connect_test_db"
        alembic upgrade head

    - name: Run unit tests
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html --cov-fail-under=80
      env:
        CC_DATABASE_URL: postgresql+asyncpg://culture_connect_test:test_password@localhost:5432/culture_connect_test_db
        CC_REDIS_URL: redis://localhost:6379/0
        CC_TESTING_MODE: true

    - name: Upload test coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          coverage.xml
          htmlcov/

  # =============================================================================
  # DOCKER BUILD AND SECURITY SCAN
  # =============================================================================
  
  build:
    name: Build & Security Scan
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=${{ needs.code-quality.outputs.version }}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ needs.code-quality.outputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.code-quality.outputs.version }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # =============================================================================
  # DEVELOPMENT DEPLOYMENT
  # =============================================================================
  
  deploy-development:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to development environment
      run: |
        echo "Deploying to development environment..."
        echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.code-quality.outputs.version }}"
        # Add actual deployment logic here

    - name: Run smoke tests
      run: |
        echo "Running development smoke tests..."
        # Add smoke test logic here

  # =============================================================================
  # STAGING DEPLOYMENT
  # =============================================================================
  
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/staging'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging environment
      run: |
        echo "Deploying to staging environment..."
        echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.code-quality.outputs.version }}"
        # Add actual deployment logic here

    - name: Run integration tests
      run: |
        echo "Running staging integration tests..."
        # Add integration test logic here

  # =============================================================================
  # PRODUCTION DEPLOYMENT
  # =============================================================================
  
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Production deployment approval
      uses: trstringer/manual-approval@v1
      with:
        secret: ${{ github.TOKEN }}
        approvers: ${{ secrets.PRODUCTION_APPROVERS }}
        minimum-approvals: 2
        issue-title: "Production Deployment Approval Required"
        issue-body: |
          Please review and approve the production deployment:
          - Version: ${{ needs.code-quality.outputs.version }}
          - Commit: ${{ github.sha }}
          - Branch: ${{ github.ref }}

    - name: Deploy to production environment
      run: |
        echo "Deploying to production environment..."
        echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.code-quality.outputs.version }}"
        # Add actual deployment logic here

    - name: Run production smoke tests
      run: |
        echo "Running production smoke tests..."
        # Add production smoke test logic here

    - name: Notify deployment success
      if: success()
      run: |
        echo "Production deployment successful!"
        # Add notification logic here
