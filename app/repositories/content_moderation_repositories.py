"""
Content Moderation repositories for Culture Connect Backend API.

This module provides comprehensive repository layer for content moderation including:
- ContentModerationWorkflowRepository: Workflow management and tracking
- ContentQualityScoreRepository: Quality assessment and analytics
- PlagiarismCheckRepository: Plagiarism detection and tracking
- ContentModerationRuleRepository: Rule management and configuration
- ContentApprovalHistoryRepository: Approval audit trail

Implements Task 3.2.4 requirements with async/await patterns, PostgreSQL optimization,
and seamless integration with existing BaseRepository infrastructure.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID

from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import select, update, delete

from app.repositories.base import BaseRepository
from app.models.content_moderation import (
    ContentModerationWorkflow, ContentQualityScore, PlagiarismCheck,
    ContentModerationRule, ContentApprovalHistory,
    ContentStatus, ContentType, ModerationAction, PlagiarismStatus
)
# Schemas imported for type hints in docstrings

logger = logging.getLogger(__name__)


class ContentModerationWorkflowRepository(BaseRepository[ContentModerationWorkflow]):
    """Repository for content moderation workflow operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(ContentModerationWorkflow, session)

    async def get_by_content(self, content_type: ContentType, content_id: UUID) -> Optional[ContentModerationWorkflow]:
        """Get workflow by content type and ID."""
        try:
            query = select(self.model).where(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id
                )
            ).options(
                selectinload(self.model.quality_scores),
                selectinload(self.model.plagiarism_checks),
                selectinload(self.model.approval_history)
            )

            result = await self.session.execute(query)
            workflow = result.scalar_one_or_none()

            if workflow:
                logger.info(f"Retrieved workflow for content {content_type}:{content_id}")

            return workflow

        except Exception as e:
            logger.error(f"Error retrieving workflow for content {content_type}:{content_id}: {str(e)}")
            raise

    async def get_pending_reviews(self, limit: int = 50, priority_order: bool = True) -> List[ContentModerationWorkflow]:
        """Get pending reviews ordered by priority and submission time."""
        try:
            query = select(self.model).where(
                self.model.status.in_([ContentStatus.PENDING, ContentStatus.UNDER_REVIEW])
            ).options(
                selectinload(self.model.vendor),
                selectinload(self.model.assigned_reviewer)
            )

            if priority_order:
                query = query.order_by(desc(self.model.priority), asc(self.model.submitted_at))
            else:
                query = query.order_by(asc(self.model.submitted_at))

            query = query.limit(limit)

            result = await self.session.execute(query)
            workflows = result.scalars().all()

            logger.info(f"Retrieved {len(workflows)} pending reviews")
            return list(workflows)

        except Exception as e:
            logger.error(f"Error retrieving pending reviews: {str(e)}")
            raise

    async def get_overdue_reviews(self, hours_overdue: int = 24) -> List[ContentModerationWorkflow]:
        """Get reviews that are overdue for completion."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours_overdue)

            query = select(self.model).where(
                and_(
                    self.model.status.in_([ContentStatus.PENDING, ContentStatus.UNDER_REVIEW]),
                    or_(
                        self.model.auto_approval_deadline < cutoff_time,
                        and_(
                            self.model.auto_approval_deadline.is_(None),
                            self.model.submitted_at < cutoff_time
                        )
                    )
                )
            ).options(
                selectinload(self.model.vendor),
                selectinload(self.model.assigned_reviewer)
            ).order_by(asc(self.model.submitted_at))

            result = await self.session.execute(query)
            workflows = result.scalars().all()

            logger.info(f"Retrieved {len(workflows)} overdue reviews")
            return list(workflows)

        except Exception as e:
            logger.error(f"Error retrieving overdue reviews: {str(e)}")
            raise

    async def get_by_vendor(self, vendor_id: UUID, status: Optional[ContentStatus] = None, limit: int = 50) -> List[ContentModerationWorkflow]:
        """Get workflows by vendor with optional status filter."""
        try:
            query = select(self.model).where(self.model.vendor_id == vendor_id)

            if status:
                query = query.where(self.model.status == status)

            query = query.options(
                selectinload(self.model.quality_scores),
                selectinload(self.model.plagiarism_checks)
            ).order_by(desc(self.model.submitted_at)).limit(limit)

            result = await self.session.execute(query)
            workflows = result.scalars().all()

            logger.info(f"Retrieved {len(workflows)} workflows for vendor {vendor_id}")
            return list(workflows)

        except Exception as e:
            logger.error(f"Error retrieving workflows for vendor {vendor_id}: {str(e)}")
            raise

    async def get_by_reviewer(self, reviewer_id: UUID, status: Optional[ContentStatus] = None, limit: int = 50) -> List[ContentModerationWorkflow]:
        """Get workflows assigned to a specific reviewer."""
        try:
            query = select(self.model).where(self.model.assigned_reviewer_id == reviewer_id)

            if status:
                query = query.where(self.model.status == status)

            query = query.options(
                selectinload(self.model.vendor),
                selectinload(self.model.quality_scores)
            ).order_by(desc(self.model.priority), asc(self.model.submitted_at)).limit(limit)

            result = await self.session.execute(query)
            workflows = result.scalars().all()

            logger.info(f"Retrieved {len(workflows)} workflows for reviewer {reviewer_id}")
            return list(workflows)

        except Exception as e:
            logger.error(f"Error retrieving workflows for reviewer {reviewer_id}: {str(e)}")
            raise

    async def assign_reviewer(self, workflow_id: UUID, reviewer_id: UUID) -> bool:
        """Assign a reviewer to a workflow."""
        try:
            query = update(self.model).where(
                self.model.id == workflow_id
            ).values(
                assigned_reviewer_id=reviewer_id,
                status=ContentStatus.UNDER_REVIEW,
                review_started_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            result = await self.session.execute(query)
            await self.session.commit()

            success = result.rowcount > 0
            if success:
                logger.info(f"Assigned reviewer {reviewer_id} to workflow {workflow_id}")

            return success

        except Exception as e:
            logger.error(f"Error assigning reviewer to workflow {workflow_id}: {str(e)}")
            await self.session.rollback()
            raise

    async def update_scores(self, workflow_id: UUID, moderation_score: float = None,
                          quality_score: float = None, plagiarism_score: float = None) -> bool:
        """Update workflow scores."""
        try:
            update_values = {"updated_at": datetime.utcnow()}

            if moderation_score is not None:
                update_values["moderation_score"] = moderation_score
            if quality_score is not None:
                update_values["quality_score"] = quality_score
            if plagiarism_score is not None:
                update_values["plagiarism_score"] = plagiarism_score

            query = update(self.model).where(
                self.model.id == workflow_id
            ).values(**update_values)

            result = await self.session.execute(query)
            await self.session.commit()

            success = result.rowcount > 0
            if success:
                logger.info(f"Updated scores for workflow {workflow_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating scores for workflow {workflow_id}: {str(e)}")
            await self.session.rollback()
            raise

    async def get_workflow_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get workflow statistics for the specified period."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            # Total workflows
            total_query = select(func.count(self.model.id)).where(
                self.model.created_at >= cutoff_date
            )
            total_result = await self.session.execute(total_query)
            total_workflows = total_result.scalar() or 0

            # Status distribution
            status_query = select(
                self.model.status,
                func.count(self.model.id).label('count')
            ).where(
                self.model.created_at >= cutoff_date
            ).group_by(self.model.status)

            status_result = await self.session.execute(status_query)
            status_distribution = {row.status: row.count for row in status_result}

            # Average scores
            scores_query = select(
                func.avg(self.model.moderation_score).label('avg_moderation'),
                func.avg(self.model.quality_score).label('avg_quality'),
                func.avg(self.model.plagiarism_score).label('avg_plagiarism')
            ).where(
                and_(
                    self.model.created_at >= cutoff_date,
                    self.model.status.in_([ContentStatus.APPROVED, ContentStatus.REJECTED, ContentStatus.AUTO_APPROVED])
                )
            )

            scores_result = await self.session.execute(scores_query)
            scores_row = scores_result.first()

            # Average review time
            review_time_query = select(
                func.avg(
                    func.extract('epoch', self.model.review_completed_at - self.model.review_started_at) / 3600
                ).label('avg_hours')
            ).where(
                and_(
                    self.model.created_at >= cutoff_date,
                    self.model.review_completed_at.is_not(None),
                    self.model.review_started_at.is_not(None)
                )
            )

            review_time_result = await self.session.execute(review_time_query)
            avg_review_time = review_time_result.scalar() or 0.0

            statistics = {
                "total_workflows": total_workflows,
                "status_distribution": status_distribution,
                "average_moderation_score": float(scores_row.avg_moderation or 0),
                "average_quality_score": float(scores_row.avg_quality or 0),
                "average_plagiarism_score": float(scores_row.avg_plagiarism or 0),
                "average_review_time_hours": float(avg_review_time),
                "period_days": days
            }

            logger.info(f"Generated workflow statistics for {days} days")
            return statistics

        except Exception as e:
            logger.error(f"Error generating workflow statistics: {str(e)}")
            raise


class ContentQualityScoreRepository(BaseRepository[ContentQualityScore]):
    """Repository for content quality score operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(ContentQualityScore, session)

    async def get_by_workflow(self, workflow_id: UUID) -> Optional[ContentQualityScore]:
        """Get quality score by workflow ID."""
        try:
            query = select(self.model).where(
                self.model.workflow_id == workflow_id
            ).options(selectinload(self.model.workflow))

            result = await self.session.execute(query)
            score = result.scalar_one_or_none()

            if score:
                logger.info(f"Retrieved quality score for workflow {workflow_id}")

            return score

        except Exception as e:
            logger.error(f"Error retrieving quality score for workflow {workflow_id}: {str(e)}")
            raise

    async def get_quality_trends(self, days: int = 30, content_type: Optional[ContentType] = None) -> List[Dict[str, Any]]:
        """Get quality score trends over time."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = select(
                func.date_trunc('day', self.model.calculated_at).label('date'),
                func.avg(self.model.overall_score).label('avg_overall'),
                func.avg(self.model.content_quality_score).label('avg_content'),
                func.avg(self.model.image_quality_score).label('avg_image'),
                func.avg(self.model.seo_optimization_score).label('avg_seo'),
                func.count(self.model.id).label('count')
            ).where(
                self.model.calculated_at >= cutoff_date
            )

            if content_type:
                query = query.join(ContentModerationWorkflow).where(
                    ContentModerationWorkflow.content_type == content_type
                )

            query = query.group_by(
                func.date_trunc('day', self.model.calculated_at)
            ).order_by(
                func.date_trunc('day', self.model.calculated_at)
            )

            result = await self.session.execute(query)
            trends = []

            for row in result:
                trends.append({
                    "date": row.date.isoformat(),
                    "average_overall_score": float(row.avg_overall or 0),
                    "average_content_score": float(row.avg_content or 0),
                    "average_image_score": float(row.avg_image or 0),
                    "average_seo_score": float(row.avg_seo or 0),
                    "sample_count": row.count
                })

            logger.info(f"Generated quality trends for {days} days")
            return trends

        except Exception as e:
            logger.error(f"Error generating quality trends: {str(e)}")
            raise


class PlagiarismCheckRepository(BaseRepository[PlagiarismCheck]):
    """Repository for plagiarism check operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(PlagiarismCheck, session)

    async def get_by_workflow(self, workflow_id: UUID) -> Optional[PlagiarismCheck]:
        """Get plagiarism check by workflow ID."""
        try:
            query = select(self.model).where(
                self.model.workflow_id == workflow_id
            ).options(selectinload(self.model.workflow))

            result = await self.session.execute(query)
            check = result.scalar_one_or_none()

            if check:
                logger.info(f"Retrieved plagiarism check for workflow {workflow_id}")

            return check

        except Exception as e:
            logger.error(f"Error retrieving plagiarism check for workflow {workflow_id}: {str(e)}")
            raise

    async def get_by_content_hash(self, content_hash: str) -> List[PlagiarismCheck]:
        """Get plagiarism checks by content hash."""
        try:
            query = select(self.model).where(
                self.model.content_hash == content_hash
            ).options(selectinload(self.model.workflow)).order_by(desc(self.model.checked_at))

            result = await self.session.execute(query)
            checks = result.scalars().all()

            logger.info(f"Retrieved {len(checks)} plagiarism checks for hash {content_hash}")
            return list(checks)

        except Exception as e:
            logger.error(f"Error retrieving plagiarism checks for hash {content_hash}: {str(e)}")
            raise

    async def get_high_similarity_content(self, threshold: float = 80.0, limit: int = 50) -> List[PlagiarismCheck]:
        """Get content with high similarity scores."""
        try:
            query = select(self.model).where(
                and_(
                    self.model.similarity_percentage >= threshold,
                    self.model.status == PlagiarismStatus.SIMILAR_FOUND
                )
            ).options(
                selectinload(self.model.workflow)
            ).order_by(desc(self.model.similarity_percentage)).limit(limit)

            result = await self.session.execute(query)
            checks = result.scalars().all()

            logger.info(f"Retrieved {len(checks)} high similarity content items")
            return list(checks)

        except Exception as e:
            logger.error(f"Error retrieving high similarity content: {str(e)}")
            raise

    async def get_plagiarism_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get plagiarism detection statistics."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            # Total checks
            total_query = select(func.count(self.model.id)).where(
                self.model.checked_at >= cutoff_date
            )
            total_result = await self.session.execute(total_query)
            total_checks = total_result.scalar() or 0

            # Status distribution
            status_query = select(
                self.model.status,
                func.count(self.model.id).label('count')
            ).where(
                self.model.checked_at >= cutoff_date
            ).group_by(self.model.status)

            status_result = await self.session.execute(status_query)
            status_distribution = {row.status: row.count for row in status_result}

            # Average similarity and originality scores
            scores_query = select(
                func.avg(self.model.similarity_percentage).label('avg_similarity'),
                func.avg(self.model.originality_score).label('avg_originality'),
                func.avg(self.model.confidence_level).label('avg_confidence')
            ).where(
                and_(
                    self.model.checked_at >= cutoff_date,
                    self.model.status != PlagiarismStatus.NOT_CHECKED
                )
            )

            scores_result = await self.session.execute(scores_query)
            scores_row = scores_result.first()

            # High similarity count
            high_similarity_query = select(func.count(self.model.id)).where(
                and_(
                    self.model.checked_at >= cutoff_date,
                    self.model.similarity_percentage >= 80.0
                )
            )
            high_similarity_result = await self.session.execute(high_similarity_query)
            high_similarity_count = high_similarity_result.scalar() or 0

            statistics = {
                "total_checks": total_checks,
                "status_distribution": status_distribution,
                "average_similarity_percentage": float(scores_row.avg_similarity or 0),
                "average_originality_score": float(scores_row.avg_originality or 0),
                "average_confidence_level": float(scores_row.avg_confidence or 0),
                "high_similarity_count": high_similarity_count,
                "high_similarity_rate": (high_similarity_count / total_checks * 100) if total_checks > 0 else 0,
                "period_days": days
            }

            logger.info(f"Generated plagiarism statistics for {days} days")
            return statistics

        except Exception as e:
            logger.error(f"Error generating plagiarism statistics: {str(e)}")
            raise


class ContentModerationRuleRepository(BaseRepository[ContentModerationRule]):
    """Repository for content moderation rule operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(ContentModerationRule, session)

    async def get_active_rules(self, content_type: Optional[ContentType] = None) -> List[ContentModerationRule]:
        """Get active moderation rules, optionally filtered by content type."""
        try:
            query = select(self.model).where(self.model.is_active == True)

            if content_type:
                query = query.where(
                    self.model.content_types.contains([content_type.value])
                )

            query = query.order_by(desc(self.model.rule_severity), asc(self.model.rule_name))

            result = await self.session.execute(query)
            rules = result.scalars().all()

            logger.info(f"Retrieved {len(rules)} active moderation rules")
            return list(rules)

        except Exception as e:
            logger.error(f"Error retrieving active moderation rules: {str(e)}")
            raise

    async def get_by_category(self, category: str, active_only: bool = True) -> List[ContentModerationRule]:
        """Get moderation rules by category."""
        try:
            query = select(self.model).where(self.model.rule_category == category)

            if active_only:
                query = query.where(self.model.is_active == True)

            query = query.order_by(desc(self.model.rule_severity), asc(self.model.rule_name))

            result = await self.session.execute(query)
            rules = result.scalars().all()

            logger.info(f"Retrieved {len(rules)} rules for category {category}")
            return list(rules)

        except Exception as e:
            logger.error(f"Error retrieving rules for category {category}: {str(e)}")
            raise

    async def update_rule_performance(self, rule_id: UUID, successful: bool) -> bool:
        """Update rule performance metrics."""
        try:
            if successful:
                query = update(self.model).where(
                    self.model.id == rule_id
                ).values(
                    total_applications=self.model.total_applications + 1,
                    successful_detections=self.model.successful_detections + 1,
                    updated_at=datetime.utcnow()
                )
            else:
                query = update(self.model).where(
                    self.model.id == rule_id
                ).values(
                    total_applications=self.model.total_applications + 1,
                    false_positives=self.model.false_positives + 1,
                    updated_at=datetime.utcnow()
                )

            result = await self.session.execute(query)

            # Update accuracy rate
            accuracy_query = update(self.model).where(
                self.model.id == rule_id
            ).values(
                accuracy_rate=func.case(
                    (self.model.total_applications > 0,
                     (self.model.successful_detections * 100.0) / self.model.total_applications),
                    else_=0.0
                )
            )

            await self.session.execute(accuracy_query)
            await self.session.commit()

            success = result.rowcount > 0
            if success:
                logger.info(f"Updated performance for rule {rule_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating rule performance for {rule_id}: {str(e)}")
            await self.session.rollback()
            raise

    async def get_rule_effectiveness_report(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get rule effectiveness report."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = select(
                self.model.id,
                self.model.rule_name,
                self.model.rule_category,
                self.model.rule_severity,
                self.model.total_applications,
                self.model.successful_detections,
                self.model.false_positives,
                self.model.accuracy_rate
            ).where(
                and_(
                    self.model.is_active == True,
                    self.model.updated_at >= cutoff_date
                )
            ).order_by(desc(self.model.accuracy_rate))

            result = await self.session.execute(query)
            report = []

            for row in result:
                effectiveness_score = 0.0
                if row.total_applications > 0:
                    effectiveness_score = (row.successful_detections / row.total_applications) * 100

                report.append({
                    "rule_id": str(row.id),
                    "rule_name": row.rule_name,
                    "category": row.rule_category,
                    "severity": row.rule_severity,
                    "total_applications": row.total_applications,
                    "successful_detections": row.successful_detections,
                    "false_positives": row.false_positives,
                    "accuracy_rate": float(row.accuracy_rate),
                    "effectiveness_score": effectiveness_score
                })

            logger.info(f"Generated rule effectiveness report for {days} days")
            return report

        except Exception as e:
            logger.error(f"Error generating rule effectiveness report: {str(e)}")
            raise


class ContentApprovalHistoryRepository(BaseRepository[ContentApprovalHistory]):
    """Repository for content approval history operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(ContentApprovalHistory, session)

    async def get_by_workflow(self, workflow_id: UUID, limit: int = 50) -> List[ContentApprovalHistory]:
        """Get approval history by workflow ID."""
        try:
            query = select(self.model).where(
                self.model.workflow_id == workflow_id
            ).options(
                selectinload(self.model.reviewer),
                selectinload(self.model.escalated_reviewer)
            ).order_by(desc(self.model.decision_made_at)).limit(limit)

            result = await self.session.execute(query)
            history = result.scalars().all()

            logger.info(f"Retrieved {len(history)} approval history entries for workflow {workflow_id}")
            return list(history)

        except Exception as e:
            logger.error(f"Error retrieving approval history for workflow {workflow_id}: {str(e)}")
            raise

    async def get_by_reviewer(self, reviewer_id: UUID, days: int = 30, limit: int = 100) -> List[ContentApprovalHistory]:
        """Get approval history by reviewer."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = select(self.model).where(
                and_(
                    self.model.reviewer_id == reviewer_id,
                    self.model.decision_made_at >= cutoff_date
                )
            ).options(
                selectinload(self.model.workflow)
            ).order_by(desc(self.model.decision_made_at)).limit(limit)

            result = await self.session.execute(query)
            history = result.scalars().all()

            logger.info(f"Retrieved {len(history)} approval history entries for reviewer {reviewer_id}")
            return list(history)

        except Exception as e:
            logger.error(f"Error retrieving approval history for reviewer {reviewer_id}: {str(e)}")
            raise

    async def get_reviewer_performance(self, reviewer_id: UUID, days: int = 30) -> Dict[str, Any]:
        """Get reviewer performance metrics."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            # Total decisions
            total_query = select(func.count(self.model.id)).where(
                and_(
                    self.model.reviewer_id == reviewer_id,
                    self.model.decision_made_at >= cutoff_date,
                    self.model.is_automated == False
                )
            )
            total_result = await self.session.execute(total_query)
            total_decisions = total_result.scalar() or 0

            # Action distribution
            action_query = select(
                self.model.action_taken,
                func.count(self.model.id).label('count')
            ).where(
                and_(
                    self.model.reviewer_id == reviewer_id,
                    self.model.decision_made_at >= cutoff_date,
                    self.model.is_automated == False
                )
            ).group_by(self.model.action_taken)

            action_result = await self.session.execute(action_query)
            action_distribution = {row.action_taken: row.count for row in action_result}

            # Average metrics
            metrics_query = select(
                func.avg(self.model.confidence_score).label('avg_confidence'),
                func.avg(self.model.time_spent_minutes).label('avg_time'),
                func.avg(self.model.review_duration_minutes).label('avg_duration')
            ).where(
                and_(
                    self.model.reviewer_id == reviewer_id,
                    self.model.decision_made_at >= cutoff_date,
                    self.model.is_automated == False
                )
            )

            metrics_result = await self.session.execute(metrics_query)
            metrics_row = metrics_result.first()

            performance = {
                "reviewer_id": str(reviewer_id),
                "total_decisions": total_decisions,
                "action_distribution": action_distribution,
                "average_confidence_score": float(metrics_row.avg_confidence or 0),
                "average_time_spent_minutes": float(metrics_row.avg_time or 0),
                "average_review_duration_minutes": float(metrics_row.avg_duration or 0),
                "period_days": days
            }

            logger.info(f"Generated performance metrics for reviewer {reviewer_id}")
            return performance

        except Exception as e:
            logger.error(f"Error generating reviewer performance for {reviewer_id}: {str(e)}")
            raise
