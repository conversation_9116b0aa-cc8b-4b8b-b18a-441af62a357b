"""
Performance tests for enhanced WebSocket real-time communication system.

This module provides comprehensive performance tests for enhanced WebSocket infrastructure including:
- Service operations: <500ms creation, <200ms queries, <100ms updates
- HTTP endpoints: <200ms GET requests, <500ms POST/PUT/DELETE operations
- WebSocket operations: <100ms message delivery, >99% connection reliability
- Bulk operations: >1000 records/second throughput with <300ms response times
- Concurrent connection handling and memory usage validation

Implements Task 6.1.1 Phase 5 requirements with performance target validation.
"""

import pytest
import asyncio
import time
import statistics
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

from app.services.websocket_services import (
    WebSocketRoomService, WebSocketRoomParticipantService,
    WebSocketConnectionService
)
from app.schemas.websocket_schemas import (
    WebSocketRoomCreate, WebSocketRoomParticipantCreate
)
from app.models.websocket_models import (
    WebSocketRoom, WebSocketRoomParticipant, WebSocketConnection
)
from app.models.user import User


@pytest.mark.performance
class TestEnhancedWebSocketServicePerformance:
    """Performance tests for enhanced WebSocket service operations."""

    @pytest.fixture
    async def room_service(self, db_session, cache_manager):
        """Create WebSocketRoomService for performance testing."""
        return WebSocketRoomService(db_session, cache_manager)

    @pytest.fixture
    async def participant_service(self, db_session, cache_manager):
        """Create WebSocketRoomParticipantService for performance testing."""
        return WebSocketRoomParticipantService(db_session, cache_manager)

    @pytest.fixture
    async def connection_service(self, db_session, cache_manager):
        """Create WebSocketConnectionService for performance testing."""
        return WebSocketConnectionService(db_session, cache_manager)

    @pytest.fixture
    def mock_user(self):
        """Mock user for performance testing."""
        user = MagicMock(spec=User)
        user.id = 1
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_room_creation_performance_target(
        self,
        room_service: WebSocketRoomService,
        mock_user: User,
        performance_timer
    ):
        """Test room creation meets <500ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id="perf_test_room",
                room_name="Performance Test Room",
                room_type="booking",
                is_private=True,
                max_participants=10,
                booking_id=123,
                owner_id=mock_user.id,
                is_active=True,
                participant_count=0,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.create_room.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock notification services for fast response
            with patch.object(room_service, '_send_room_creation_notifications') as mock_notifications:
                mock_notifications.return_value = None
                
                room_data = WebSocketRoomCreate(
                    room_id="perf_test_room",
                    room_name="Performance Test Room",
                    room_type="booking",
                    is_private=True,
                    max_participants=10,
                    booking_id=123,
                    owner_id=mock_user.id
                )
                
                timer.start()
                result = await room_service.create_room(room_data, mock_user)
                timer.stop()
                
                # Performance target: <500ms for room creation
                timer.assert_under_ms(500, "Room creation service operation")
                
                # Verify result
                assert result.room_id == "perf_test_room"

    @pytest.mark.asyncio
    async def test_room_query_performance_target(
        self,
        room_service: WebSocketRoomService,
        mock_user: User,
        performance_timer
    ):
        """Test room queries meet <200ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id="query_test_room",
                room_name="Query Test Room",
                room_type="booking",
                is_private=True,
                max_participants=10,
                booking_id=123,
                owner_id=mock_user.id,
                is_active=True,
                participant_count=0,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.get_room_by_id.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation for fast response
            with patch.object(room_service, '_validate_room_access_permissions') as mock_validate:
                mock_validate.return_value = None
                
                timer.start()
                result = await room_service.get_room_by_id("query_test_room", mock_user)
                timer.stop()
                
                # Performance target: <200ms for room queries
                timer.assert_under_ms(200, "Room query service operation")
                
                # Verify result
                assert result.room_id == "query_test_room"

    @pytest.mark.asyncio
    async def test_participant_addition_performance_target(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test participant addition meets <500ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_participant = WebSocketRoomParticipant(
                id=1,
                room_id=1,
                user_id=123,
                role="participant",
                is_active=True,
                joined_at=datetime.now(timezone.utc),
                last_seen_at=datetime.now(timezone.utc)
            )
            mock_repo.add_participant.return_value = mock_participant
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation and notifications
            with patch.object(participant_service, '_validate_participant_addition_permissions') as mock_validate:
                with patch.object(participant_service, '_send_participant_addition_notifications') as mock_notifications:
                    mock_validate.return_value = None
                    mock_notifications.return_value = None
                    
                    timer.start()
                    result = await participant_service.add_participant(
                        room_id=1,
                        user_id=123,
                        role="participant",
                        current_user=mock_user
                    )
                    timer.stop()
                    
                    # Performance target: <500ms for participant addition
                    timer.assert_under_ms(500, "Participant addition service operation")
                    
                    # Verify result
                    assert result.user_id == 123

    @pytest.mark.asyncio
    async def test_participant_query_performance_target(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test participant queries meet <200ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_participants = [
                WebSocketRoomParticipant(
                    id=i,
                    room_id=1,
                    user_id=100 + i,
                    role="participant",
                    is_active=True,
                    joined_at=datetime.now(timezone.utc),
                    last_seen_at=datetime.now(timezone.utc)
                )
                for i in range(10)  # 10 participants
            ]
            mock_repo.get_room_participants.return_value = mock_participants
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(participant_service, '_validate_room_access_permissions') as mock_validate:
                mock_validate.return_value = None
                
                timer.start()
                result = await participant_service.get_room_participants(
                    room_id=1,
                    active_only=True,
                    current_user=mock_user
                )
                timer.stop()
                
                # Performance target: <200ms for participant queries
                timer.assert_under_ms(200, "Participant query service operation")
                
                # Verify result
                assert len(result) == 10

    @pytest.mark.asyncio
    async def test_activity_update_performance_target(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test activity updates meet <100ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_repo.update_participant_activity.return_value = True
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(participant_service, '_validate_participant_activity_update_permissions') as mock_validate:
                mock_validate.return_value = None
                
                timer.start()
                result = await participant_service.update_participant_activity(
                    room_id=1,
                    user_id=123,
                    current_user=mock_user
                )
                timer.stop()
                
                # Performance target: <100ms for activity updates
                timer.assert_under_ms(100, "Activity update service operation")
                
                # Verify result
                assert result is True

    @pytest.mark.asyncio
    async def test_broadcast_preparation_performance_target(
        self,
        connection_service: WebSocketConnectionService,
        mock_user: User,
        performance_timer
    ):
        """Test broadcast preparation meets <100ms performance target."""
        timer = performance_timer()
        
        # Mock repository for fast response
        with patch.object(connection_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_connections = [
                WebSocketConnection(
                    id=i,
                    connection_id=f"conn_{i}",
                    user_id=100 + i,
                    status="connected",
                    client_type="web",
                    ip_address="127.0.0.1",
                    connected_at=datetime.now(timezone.utc),
                    last_activity_at=datetime.now(timezone.utc)
                )
                for i in range(20)  # 20 connections
            ]
            mock_repo.get_connections_in_room.return_value = mock_connections
            mock_repo.broadcast_to_room_connections.return_value = 20
            mock_get_repo.return_value = mock_repo
            
            # Mock event service and permission validation
            with patch('app.services.websocket_services.WebSocketEventService') as mock_event_service_class:
                with patch.object(connection_service, '_validate_room_broadcast_permissions') as mock_validate:
                    mock_event_service = AsyncMock()
                    mock_event_service.create_event.return_value = MagicMock()
                    mock_event_service_class.return_value = mock_event_service
                    mock_validate.return_value = None
                    
                    event_data = {
                        "type": "performance_test",
                        "message": "Performance test broadcast",
                        "timestamp": time.time()
                    }
                    
                    timer.start()
                    result = await connection_service.broadcast_to_room(
                        room_id=1,
                        event_data=event_data,
                        current_user=mock_user
                    )
                    timer.stop()
                    
                    # Performance target: <100ms for broadcast preparation
                    timer.assert_under_ms(100, "Broadcast preparation service operation")
                    
                    # Verify result
                    assert result == 20

    @pytest.mark.asyncio
    async def test_bulk_operations_throughput_target(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test bulk operations meet >1000 records/second throughput target."""
        timer = performance_timer()
        
        # Mock repository for bulk operations
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_repo.bulk_remove_participants.return_value = 1000  # 1000 participants removed
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation and notifications
            with patch.object(participant_service, '_validate_bulk_participant_removal_permissions') as mock_validate:
                with patch.object(participant_service, '_send_bulk_participant_removal_notifications') as mock_notifications:
                    mock_validate.return_value = None
                    mock_notifications.return_value = None
                    
                    # Generate 1000 user IDs for bulk operation
                    user_ids = list(range(1000, 2000))  # 1000 user IDs
                    
                    timer.start()
                    result = await participant_service.bulk_remove_participants(
                        room_id=1,
                        user_ids=user_ids,
                        current_user=mock_user
                    )
                    timer.stop()
                    
                    # Performance target: <1000ms for 1000 records (>1000 records/second)
                    elapsed_ms = timer.elapsed_ms()
                    throughput = 1000 / (elapsed_ms / 1000)  # records per second
                    
                    assert throughput > 1000, f"Throughput {throughput:.2f} records/second is below target of 1000"
                    assert elapsed_ms < 1000, f"Bulk operation took {elapsed_ms:.2f}ms, expected under 1000ms"
                    
                    # Verify result
                    assert result == 1000

    @pytest.mark.asyncio
    async def test_concurrent_operations_performance(
        self,
        room_service: WebSocketRoomService,
        mock_user: User,
        performance_timer
    ):
        """Test concurrent operations performance and reliability."""
        timer = performance_timer()
        
        # Mock repository for concurrent operations
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            
            # Mock room responses
            def create_mock_room(room_id):
                return WebSocketRoom(
                    id=hash(room_id) % 1000,
                    room_id=room_id,
                    room_name=f"Concurrent Test Room {room_id}",
                    room_type="booking",
                    is_private=True,
                    max_participants=10,
                    booking_id=123,
                    owner_id=mock_user.id,
                    is_active=True,
                    participant_count=0,
                    total_messages=0,
                    created_at=datetime.now(timezone.utc),
                    last_activity_at=datetime.now(timezone.utc)
                )
            
            mock_repo.create_room.side_effect = lambda data: create_mock_room(data["room_id"])
            mock_get_repo.return_value = mock_repo
            
            # Mock notification services
            with patch.object(room_service, '_send_room_creation_notifications') as mock_notifications:
                mock_notifications.return_value = None
                
                # Create 50 concurrent room creation tasks
                tasks = []
                for i in range(50):
                    room_data = WebSocketRoomCreate(
                        room_id=f"concurrent_room_{i}",
                        room_name=f"Concurrent Room {i}",
                        room_type="booking",
                        is_private=True,
                        max_participants=10,
                        booking_id=123 + i,
                        owner_id=mock_user.id
                    )
                    task = room_service.create_room(room_data, mock_user)
                    tasks.append(task)
                
                timer.start()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                timer.stop()
                
                # Performance target: <2000ms for 50 concurrent operations
                timer.assert_under_ms(2000, "50 concurrent room creation operations")
                
                # Verify all operations completed successfully
                successful_results = [r for r in results if not isinstance(r, Exception)]
                assert len(successful_results) == 50, f"Only {len(successful_results)} out of 50 operations succeeded"

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User
    ):
        """Test memory usage remains stable under load."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Mock repository for memory testing
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            
            # Mock participant responses
            def create_mock_participant(room_id, user_id):
                return WebSocketRoomParticipant(
                    id=user_id,
                    room_id=room_id,
                    user_id=user_id,
                    role="participant",
                    is_active=True,
                    joined_at=datetime.now(timezone.utc),
                    last_seen_at=datetime.now(timezone.utc)
                )
            
            mock_repo.add_participant.side_effect = lambda **kwargs: create_mock_participant(
                kwargs.get("room_id"), kwargs.get("user_id")
            )
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation and notifications
            with patch.object(participant_service, '_validate_participant_addition_permissions') as mock_validate:
                with patch.object(participant_service, '_send_participant_addition_notifications') as mock_notifications:
                    mock_validate.return_value = None
                    mock_notifications.return_value = None
                    
                    # Perform 1000 operations to test memory stability
                    for i in range(1000):
                        await participant_service.add_participant(
                            room_id=1,
                            user_id=1000 + i,
                            role="participant",
                            current_user=mock_user
                        )
                        
                        # Check memory every 100 operations
                        if i % 100 == 0:
                            current_memory = process.memory_info().rss / 1024 / 1024  # MB
                            memory_increase = current_memory - initial_memory
                            
                            # Memory increase should be reasonable (< 100MB for 1000 operations)
                            assert memory_increase < 100, f"Memory increased by {memory_increase:.2f}MB after {i+1} operations"

    @pytest.mark.asyncio
    async def test_response_time_consistency(
        self,
        room_service: WebSocketRoomService,
        mock_user: User
    ):
        """Test response time consistency across multiple operations."""
        response_times = []
        
        # Mock repository for consistent response testing
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id="consistency_test_room",
                room_name="Consistency Test Room",
                room_type="booking",
                is_private=True,
                max_participants=10,
                booking_id=123,
                owner_id=mock_user.id,
                is_active=True,
                participant_count=0,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.get_room_by_id.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(room_service, '_validate_room_access_permissions') as mock_validate:
                mock_validate.return_value = None
                
                # Perform 100 operations and measure response times
                for i in range(100):
                    start_time = time.time()
                    await room_service.get_room_by_id("consistency_test_room", mock_user)
                    end_time = time.time()
                    
                    response_time_ms = (end_time - start_time) * 1000
                    response_times.append(response_time_ms)
                
                # Calculate statistics
                avg_response_time = statistics.mean(response_times)
                max_response_time = max(response_times)
                std_deviation = statistics.stdev(response_times)
                
                # Performance consistency targets
                assert avg_response_time < 50, f"Average response time {avg_response_time:.2f}ms exceeds 50ms target"
                assert max_response_time < 200, f"Maximum response time {max_response_time:.2f}ms exceeds 200ms target"
                assert std_deviation < 20, f"Response time standard deviation {std_deviation:.2f}ms exceeds 20ms target"
