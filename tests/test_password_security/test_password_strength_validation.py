"""
Test suite for password strength validation functionality.

This module tests the comprehensive password strength validation features
including configurable requirements, entropy calculation, and security scoring.

Tests cover Task 2.1.2 password strength validation requirements.
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.schemas.password_security import (
    PasswordRequirements, PasswordValidationResult, PasswordStrengthLevel
)


class TestPasswordStrengthValidation:
    """Test password strength validation functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        return PasswordSecurityService(mock_db_session)

    @pytest.fixture
    def default_requirements(self):
        """Default password requirements for testing."""
        return PasswordRequirements(
            min_length=8,
            max_length=128,
            require_uppercase=True,
            require_lowercase=True,
            require_numbers=True,
            require_special_chars=True,
            min_special_chars=1,
            max_repeated_chars=3
        )

    @pytest.mark.asyncio
    async def test_valid_strong_password(self, password_service, default_requirements):
        """Test validation of a strong password."""
        password = "MySecureP@ssw0rd!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is True
        assert result.strength_score >= 80
        assert result.strength_level in [PasswordStrengthLevel.STRONG, PasswordStrengthLevel.VERY_STRONG]
        assert len(result.violations) == 0
        assert result.entropy_bits > 50

    @pytest.mark.asyncio
    async def test_password_too_short(self, password_service, default_requirements):
        """Test validation of password that's too short."""
        password = "Short1!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("at least 8 characters" in violation for violation in result.violations)
        assert result.strength_level == PasswordStrengthLevel.VERY_WEAK

    @pytest.mark.asyncio
    async def test_password_missing_uppercase(self, password_service, default_requirements):
        """Test validation of password missing uppercase letters."""
        password = "mysecurep@ssw0rd!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("uppercase letter" in violation for violation in result.violations)
        assert "Add uppercase letters (A-Z)" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_missing_lowercase(self, password_service, default_requirements):
        """Test validation of password missing lowercase letters."""
        password = "MYSECUREP@SSW0RD!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("lowercase letter" in violation for violation in result.violations)
        assert "Add lowercase letters (a-z)" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_missing_numbers(self, password_service, default_requirements):
        """Test validation of password missing numbers."""
        password = "MySecureP@ssword!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("number" in violation for violation in result.violations)
        assert "Add numbers (0-9)" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_missing_special_chars(self, password_service, default_requirements):
        """Test validation of password missing special characters."""
        password = "MySecurePassw0rd"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("special character" in violation for violation in result.violations)
        assert "Add special characters (!@#$%^&*)" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_with_forbidden_patterns(self, password_service, default_requirements):
        """Test validation of password containing forbidden patterns."""
        password = "Password123!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("common pattern" in violation for violation in result.violations)
        assert "Avoid common words and patterns" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_with_forbidden_sequences(self, password_service, default_requirements):
        """Test validation of password containing forbidden sequences."""
        password = "MySecure123P@ss!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("sequence: 123" in violation for violation in result.violations)
        assert "Avoid sequential characters" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_with_excessive_repeated_chars(self, password_service, default_requirements):
        """Test validation of password with too many repeated characters."""
        password = "MySecuuuureP@ss!"
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert any("repeated characters" in violation for violation in result.violations)
        assert "Reduce repeated characters" in result.suggestions

    @pytest.mark.asyncio
    async def test_password_too_long(self, password_service):
        """Test validation of password that exceeds maximum length."""
        long_password = "A" * 130 + "1!"
        requirements = PasswordRequirements(max_length=128)
        
        result = await password_service.validate_password_strength(long_password, requirements)
        
        assert result.is_valid is False
        assert any("must not exceed 128 characters" in violation for violation in result.violations)

    @pytest.mark.asyncio
    async def test_entropy_calculation(self, password_service, default_requirements):
        """Test entropy calculation for different password types."""
        # Simple password
        simple_password = "password"
        simple_result = await password_service.validate_password_strength(simple_password, default_requirements)
        
        # Complex password
        complex_password = "MyC0mpl3x!P@ssw0rd#2024"
        complex_result = await password_service.validate_password_strength(complex_password, default_requirements)
        
        assert complex_result.entropy_bits > simple_result.entropy_bits
        assert complex_result.strength_score > simple_result.strength_score

    @pytest.mark.asyncio
    async def test_strength_level_determination(self, password_service, default_requirements):
        """Test strength level determination based on score and violations."""
        test_cases = [
            ("weak", "weak123", PasswordStrengthLevel.VERY_WEAK),
            ("medium", "Medium123!", PasswordStrengthLevel.GOOD),
            ("strong", "VeryStr0ng!P@ssw0rd#2024", PasswordStrengthLevel.VERY_STRONG)
        ]
        
        for name, password, expected_min_level in test_cases:
            result = await password_service.validate_password_strength(password, default_requirements)
            # Note: Actual level may be higher than expected minimum
            assert result.strength_level is not None

    @pytest.mark.asyncio
    async def test_crack_time_estimation(self, password_service, default_requirements):
        """Test crack time estimation based on entropy."""
        weak_password = "123456"
        strong_password = "MyVeryStr0ng!P@ssw0rd#WithL0tsOfEntropy2024"
        
        weak_result = await password_service.validate_password_strength(weak_password, default_requirements)
        strong_result = await password_service.validate_password_strength(strong_password, default_requirements)
        
        # Strong password should have longer estimated crack time
        crack_time_order = ["seconds", "minutes", "hours", "days", "years", "decades", "centuries"]
        weak_index = crack_time_order.index(weak_result.estimated_crack_time)
        strong_index = crack_time_order.index(strong_result.estimated_crack_time)
        
        assert strong_index >= weak_index

    @pytest.mark.asyncio
    async def test_custom_requirements(self, password_service):
        """Test validation with custom requirements."""
        custom_requirements = PasswordRequirements(
            min_length=12,
            require_uppercase=False,
            require_special_chars=False,
            min_special_chars=0
        )
        
        password = "mylongpassword123"
        result = await password_service.validate_password_strength(password, custom_requirements)
        
        assert result.is_valid is True
        assert len(result.violations) == 0

    @pytest.mark.asyncio
    async def test_default_requirements_fallback(self, password_service):
        """Test that default requirements are used when none provided."""
        password = "TestP@ssw0rd123!"
        
        with patch.object(password_service, '_get_default_requirements') as mock_defaults:
            mock_defaults.return_value = PasswordRequirements()
            result = await password_service.validate_password_strength(password)
            
            mock_defaults.assert_called_once()
            assert result is not None

    @pytest.mark.asyncio
    async def test_password_suggestions_generation(self, password_service):
        """Test generation of password improvement suggestions."""
        suggestions = await password_service.generate_password_suggestions()
        
        assert len(suggestions) > 0
        assert any("uppercase" in suggestion.lower() for suggestion in suggestions)
        assert any("lowercase" in suggestion.lower() for suggestion in suggestions)
        assert any("numbers" in suggestion.lower() for suggestion in suggestions)
        assert any("special" in suggestion.lower() for suggestion in suggestions)

    @pytest.mark.asyncio
    async def test_validation_error_handling(self, password_service):
        """Test error handling in password validation."""
        # Test with None password (should be handled gracefully)
        with pytest.raises(Exception):
            await password_service.validate_password_strength(None)

    @pytest.mark.asyncio
    async def test_multiple_violations(self, password_service, default_requirements):
        """Test password with multiple requirement violations."""
        password = "short"  # Too short, missing uppercase, numbers, special chars
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is False
        assert len(result.violations) >= 4  # Multiple violations
        assert len(result.suggestions) >= 4  # Multiple suggestions
        assert result.strength_level == PasswordStrengthLevel.VERY_WEAK

    @pytest.mark.asyncio
    async def test_edge_case_minimum_valid_password(self, password_service, default_requirements):
        """Test edge case of minimum valid password."""
        password = "Aa1!Aa1!"  # Exactly 8 chars, meets all requirements
        
        result = await password_service.validate_password_strength(password, default_requirements)
        
        assert result.is_valid is True
        assert len(result.violations) == 0
        assert result.strength_score > 0
