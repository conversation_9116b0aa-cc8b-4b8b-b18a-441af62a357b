"""
Unit tests for promotional schemas.

This module provides comprehensive unit tests for promotional system schemas:
- Campaign schemas: CampaignCreate, CampaignUpdate, CampaignResponse
- Advertisement schemas: AdvertisementCreate, AdvertisementUpdate, AdvertisementResponse
- Metrics schemas: CampaignMetricsResponse, PerformanceAnalyticsSchema
- Listing schemas: PromotionalListingCreate, PromotionalListingUpdate
- Financial schemas: AdSpendCreate, AdSpendResponse, PromotionalBillingSchema
- Targeting schemas: GeographicTargetingSchema, DemographicTargetingSchema
- Pydantic V2 compliance and validation testing
- Custom validators and business logic validation

Implements Phase 5: Comprehensive Testing Infrastructure with >85% test coverage.
"""

import pytest
from datetime import date, timedelta
from decimal import Decimal
from pydantic import ValidationError

from app.schemas.promotional_schemas import (
    CampaignCreate, CampaignUpdate, CampaignResponse, CampaignListResponse,
    AdvertisementCreate, AdvertisementUpdate, AdvertisementResponse, AdVariantSchema,
    CampaignMetricsResponse, PerformanceAnalyticsSchema, OptimizationInsightsSchema,
    PromotionalListingCreate, PromotionalListingUpdate, PlacementAvailabilitySchema,
    AdSpendCreate, AdSpendResponse, PromotionalBillingSchema,
    GeographicTargetingSchema, DemographicTargetingSchema, BehavioralTargetingSchema,
    PaymentIntegrationSchema
)
from app.models.promotional import CampaignType, CampaignObjective, BidStrategy, AdFormat
from app.core.payment.config import PaymentProviderType


class TestCampaignSchemas:
    """Test campaign-related schemas."""

    def test_campaign_create_valid_data(self, sample_campaign_data):
        """Test valid campaign creation data."""
        campaign = CampaignCreate(**sample_campaign_data)

        assert campaign.name == "Test Campaign 2025"
        assert campaign.campaign_type == CampaignType.FEATURED_LISTING
        assert campaign.campaign_objective == CampaignObjective.BOOKING_CONVERSION
        assert campaign.total_budget == Decimal("25000.00")
        assert campaign.daily_budget == Decimal("1000.00")
        assert campaign.payment_provider == PaymentProviderType.PAYSTACK

    def test_campaign_create_budget_validation(self):
        """Test campaign budget validation constraints."""
        # Test minimum budget validation
        with pytest.raises(ValidationError) as exc_info:
            CampaignCreate(
                name="Test Campaign",
                campaign_type=CampaignType.FEATURED_LISTING,
                campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                total_budget=Decimal("500.00")  # Below minimum ₦1,000
            )
        assert "Total budget must be at least ₦1,000" in str(exc_info.value)

        # Test maximum budget validation
        with pytest.raises(ValidationError) as exc_info:
            CampaignCreate(
                name="Test Campaign",
                campaign_type=CampaignType.FEATURED_LISTING,
                campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                total_budget=Decimal("15000000.00")  # Above maximum ₦10,000,000
            )
        assert "Total budget cannot exceed ₦10,000,000" in str(exc_info.value)

        # Test daily budget validation
        with pytest.raises(ValidationError) as exc_info:
            CampaignCreate(
                name="Test Campaign",
                campaign_type=CampaignType.FEATURED_LISTING,
                campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                total_budget=Decimal("5000.00"),
                daily_budget=Decimal("50.00")  # Below minimum ₦100
            )
        assert "Daily budget must be at least ₦100" in str(exc_info.value)

    def test_campaign_create_date_validation(self):
        """Test campaign date validation."""
        future_start = date.today() + timedelta(days=7)
        future_end = date.today() + timedelta(days=37)

        # Test valid future dates
        campaign = CampaignCreate(
            name="Test Campaign",
            campaign_type=CampaignType.FEATURED_LISTING,
            campaign_objective=CampaignObjective.BOOKING_CONVERSION,
            total_budget=Decimal("5000.00"),
            start_date=future_start,
            end_date=future_end
        )
        assert campaign.start_date == future_start
        assert campaign.end_date == future_end

        # Test past start date validation
        with pytest.raises(ValidationError) as exc_info:
            CampaignCreate(
                name="Test Campaign",
                campaign_type=CampaignType.FEATURED_LISTING,
                campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                total_budget=Decimal("5000.00"),
                start_date=date.today() - timedelta(days=1)  # Past date
            )
        assert "Start date cannot be in the past" in str(exc_info.value)

        # Test end date before start date validation
        with pytest.raises(ValidationError) as exc_info:
            CampaignCreate(
                name="Test Campaign",
                campaign_type=CampaignType.FEATURED_LISTING,
                campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                total_budget=Decimal("5000.00"),
                start_date=future_end,
                end_date=future_start  # End before start
            )
        assert "End date must be after start date" in str(exc_info.value)

    def test_campaign_create_targeting_validation(self):
        """Test campaign targeting configuration validation."""
        valid_targeting = {
            "countries": ["NG", "US", "GB"],
            "states": ["Lagos", "Abuja"],
            "radius_km": 50
        }

        campaign = CampaignCreate(
            name="Test Campaign",
            campaign_type=CampaignType.FEATURED_LISTING,
            campaign_objective=CampaignObjective.BOOKING_CONVERSION,
            total_budget=Decimal("5000.00"),
            geographic_targeting=valid_targeting
        )
        assert campaign.geographic_targeting == valid_targeting

    def test_campaign_update_schema(self):
        """Test campaign update schema."""
        update_data = {
            "name": "Updated Campaign Name",
            "daily_budget": Decimal("1500.00"),
            "status": "active"
        }

        campaign_update = CampaignUpdate(**update_data)
        assert campaign_update.name == "Updated Campaign Name"
        assert campaign_update.daily_budget == Decimal("1500.00")

    def test_campaign_response_schema(self, campaign_factory):
        """Test campaign response schema."""
        campaign = campaign_factory()

        # Test Pydantic V2 from_attributes functionality
        campaign_response = CampaignResponse.model_validate(campaign)
        assert campaign_response.id == campaign.id
        assert campaign_response.name == campaign.name
        assert campaign_response.total_budget == campaign.total_budget


class TestAdvertisementSchemas:
    """Test advertisement-related schemas."""

    def test_advertisement_create_valid_data(self, sample_advertisement_data):
        """Test valid advertisement creation data."""
        ad = AdvertisementCreate(**sample_advertisement_data)

        assert ad.campaign_id == 1
        assert ad.name == "Test Advertisement"
        assert ad.ad_format == AdFormat.IMAGE
        assert ad.headline == "Test Headline for Advertisement"
        assert ad.call_to_action == "Learn More"

    def test_advertisement_create_content_validation(self):
        """Test advertisement content validation."""
        # Test headline length validation
        with pytest.raises(ValidationError) as exc_info:
            AdvertisementCreate(
                campaign_id=1,
                name="Test Ad",
                ad_format=AdFormat.IMAGE,
                headline="Short",  # Too short
                call_to_action="Click"
            )
        assert "Headline must be between 10 and 100 characters" in str(exc_info.value)

        # Test XSS prevention
        with pytest.raises(ValidationError) as exc_info:
            AdvertisementCreate(
                campaign_id=1,
                name="Test Ad",
                ad_format=AdFormat.IMAGE,
                headline="<script>alert('xss')</script>Valid Headline",
                call_to_action="Click"
            )
        assert "Invalid characters detected" in str(exc_info.value)

    def test_advertisement_create_url_validation(self):
        """Test advertisement URL validation."""
        # Test valid URL
        ad = AdvertisementCreate(
            campaign_id=1,
            name="Test Ad",
            ad_format=AdFormat.IMAGE,
            headline="Valid Advertisement Headline",
            call_to_action="Click Here",
            target_url="https://example.com/valid-url"
        )
        assert ad.target_url == "https://example.com/valid-url"

        # Test invalid URL
        with pytest.raises(ValidationError) as exc_info:
            AdvertisementCreate(
                campaign_id=1,
                name="Test Ad",
                ad_format=AdFormat.IMAGE,
                headline="Valid Advertisement Headline",
                call_to_action="Click Here",
                target_url="not-a-valid-url"
            )
        assert "Invalid URL format" in str(exc_info.value)

    def test_ad_variant_schema(self):
        """Test advertisement variant schema for A/B testing."""
        variant_data = {
            "variant_group": "summer_campaign_2025",
            "variant_name": "headline_test_a",
            "test_percentage": 50.0,
            "control_group": False
        }

        variant = AdVariantSchema(**variant_data)
        assert variant.variant_group == "summer_campaign_2025"
        assert variant.test_percentage == Decimal("50.0")

        # Test percentage validation
        with pytest.raises(ValidationError) as exc_info:
            AdVariantSchema(
                variant_group="test",
                variant_name="test",
                test_percentage=150.0  # Invalid percentage
            )
        assert "Test percentage must be between 0 and 100" in str(exc_info.value)


class TestTargetingSchemas:
    """Test targeting-related schemas."""

    def test_geographic_targeting_schema(self):
        """Test geographic targeting schema."""
        targeting_data = {
            "countries": ["NG", "US", "GB"],
            "states": ["Lagos", "Abuja", "Rivers"],
            "cities": ["Lagos", "Abuja", "Port Harcourt"],
            "radius_km": 50,
            "coordinates": {"latitude": 6.5244, "longitude": 3.3792}
        }

        targeting = GeographicTargetingSchema(**targeting_data)
        assert targeting.countries == ["NG", "US", "GB"]
        assert targeting.radius_km == 50
        assert targeting.coordinates["latitude"] == 6.5244

        # Test coordinate validation
        with pytest.raises(ValidationError) as exc_info:
            GeographicTargetingSchema(
                countries=["NG"],
                coordinates={"latitude": 95.0, "longitude": 3.3792}  # Invalid latitude
            )
        assert "Latitude must be between -90 and 90" in str(exc_info.value)

    def test_demographic_targeting_schema(self):
        """Test demographic targeting schema."""
        targeting_data = {
            "age_range": {"min": 25, "max": 55},
            "gender": ["male", "female"],
            "income_level": ["middle", "high"],
            "education": ["university", "postgraduate"]
        }

        targeting = DemographicTargetingSchema(**targeting_data)
        assert targeting.age_range["min"] == 25
        assert targeting.age_range["max"] == 55
        assert "male" in targeting.gender

        # Test age range validation
        with pytest.raises(ValidationError) as exc_info:
            DemographicTargetingSchema(
                age_range={"min": 10, "max": 55}  # Below minimum age
            )
        assert "Minimum age must be at least 13" in str(exc_info.value)

    def test_behavioral_targeting_schema(self):
        """Test behavioral targeting schema."""
        targeting_data = {
            "interests": ["travel", "culture", "food"],
            "device_types": ["mobile", "desktop"],
            "time_of_day": ["morning", "evening"],
            "user_segments": ["returning_customers", "high_value_users"]
        }

        targeting = BehavioralTargetingSchema(**targeting_data)
        assert targeting.interests == ["travel", "culture", "food"]
        assert "mobile" in targeting.device_types

    def test_payment_integration_schema(self):
        """Test payment integration schema."""
        integration_data = {
            "provider_preferences": {
                "primary": "paystack",
                "fallback": ["stripe", "busha"],
                "currency_routing": {
                    "NGN": "paystack",
                    "USD": "stripe",
                    "BTC": "busha"
                }
            },
            "webhook_endpoints": {
                "payment_success": "/webhooks/promotional/payment-success",
                "payment_failed": "/webhooks/promotional/payment-failed"
            }
        }

        integration = PaymentIntegrationSchema(**integration_data)
        assert integration.provider_preferences["primary"] == "paystack"
        assert "stripe" in integration.provider_preferences["fallback"]


class TestMetricsSchemas:
    """Test metrics and analytics schemas."""

    def test_campaign_metrics_response_schema(self, campaign_metrics_factory):
        """Test campaign metrics response schema."""
        metrics = campaign_metrics_factory()

        # Test Pydantic V2 from_attributes functionality
        metrics_response = CampaignMetricsResponse.model_validate(metrics)
        assert metrics_response.campaign_id == metrics.campaign_id
        assert metrics_response.impressions == metrics.impressions
        assert metrics_response.clicks == metrics.clicks
        assert metrics_response.click_through_rate == metrics.click_through_rate

    def test_performance_analytics_schema(self):
        """Test performance analytics schema."""
        analytics_data = {
            "time_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "metrics_summary": {
                "total_impressions": 50000,
                "total_clicks": 2500,
                "total_conversions": 250,
                "total_spend": 12500.00,
                "total_revenue": 125000.00
            },
            "performance_trends": {
                "impressions_trend": "increasing",
                "ctr_trend": "stable",
                "conversion_trend": "improving"
            },
            "top_performing_campaigns": [
                {"campaign_id": 1, "name": "Summer Tourism", "roas": 10.5},
                {"campaign_id": 2, "name": "Cultural Events", "roas": 8.2}
            ]
        }

        analytics = PerformanceAnalyticsSchema(**analytics_data)
        assert analytics.metrics_summary["total_impressions"] == 50000
        assert analytics.performance_trends["impressions_trend"] == "increasing"
        assert len(analytics.top_performing_campaigns) == 2

    def test_optimization_insights_schema(self):
        """Test optimization insights schema."""
        insights_data = {
            "campaign_id": 1,
            "optimization_score": 75.5,
            "recommendations": [
                {
                    "type": "budget_optimization",
                    "priority": "high",
                    "description": "Increase daily budget by 20%",
                    "expected_impact": "15% increase in conversions"
                },
                {
                    "type": "targeting_optimization",
                    "priority": "medium",
                    "description": "Expand geographic targeting to Rivers State",
                    "expected_impact": "10% increase in reach"
                }
            ],
            "performance_alerts": [
                {
                    "type": "budget_exhaustion",
                    "severity": "warning",
                    "message": "Campaign budget 80% depleted"
                }
            ]
        }

        insights = OptimizationInsightsSchema(**insights_data)
        assert insights.optimization_score == Decimal("75.5")
        assert len(insights.recommendations) == 2
        assert insights.recommendations[0]["priority"] == "high"


class TestPromotionalListingSchemas:
    """Test promotional listing schemas."""

    def test_promotional_listing_create_schema(self):
        """Test promotional listing creation schema."""
        listing_data = {
            "advertisement_id": 1,
            "service_id": 1,
            "placement_type": "homepage_hero",
            "priority_level": 8,
            "base_price": 5000.00,
            "premium_multiplier": 1.5,
            "billing_cycle": "daily",
            "start_date": "2025-02-15T00:00:00Z",
            "end_date": "2025-03-15T23:59:59Z"
        }

        listing = PromotionalListingCreate(**listing_data)
        assert listing.advertisement_id == 1
        assert listing.priority_level == 8
        assert listing.base_price == Decimal("5000.00")
        assert listing.premium_multiplier == Decimal("1.5")

        # Test priority level validation
        with pytest.raises(ValidationError) as exc_info:
            PromotionalListingCreate(
                advertisement_id=1,
                service_id=1,
                placement_type="homepage_hero",
                priority_level=15,  # Above maximum
                base_price=5000.00
            )
        assert "Priority level must be between 1 and 10" in str(exc_info.value)

    def test_placement_availability_schema(self):
        """Test placement availability schema."""
        availability_data = {
            "placement_type": "homepage_hero",
            "available_slots": 3,
            "total_slots": 5,
            "next_available_date": "2025-02-20",
            "pricing_tiers": [
                {"priority_level": 10, "base_price": 10000.00},
                {"priority_level": 8, "base_price": 7500.00},
                {"priority_level": 5, "base_price": 5000.00}
            ]
        }

        availability = PlacementAvailabilitySchema(**availability_data)
        assert availability.available_slots == 3
        assert availability.total_slots == 5
        assert len(availability.pricing_tiers) == 3


class TestFinancialSchemas:
    """Test financial and billing schemas."""

    def test_ad_spend_create_schema(self):
        """Test ad spend creation schema."""
        spend_data = {
            "campaign_id": 1,
            "advertisement_id": 1,
            "spend_category": "campaign_budget",
            "amount": 250.00,
            "currency": "NGN",
            "description": "Daily campaign budget spend",
            "payment_provider": "paystack"
        }

        spend = AdSpendCreate(**spend_data)
        assert spend.campaign_id == 1
        assert spend.amount == Decimal("250.00")
        assert spend.currency == "NGN"

        # Test amount validation
        with pytest.raises(ValidationError) as exc_info:
            AdSpendCreate(
                campaign_id=1,
                advertisement_id=1,
                spend_category="campaign_budget",
                amount=-100.00,  # Negative amount
                currency="NGN"
            )
        assert "Amount must be positive" in str(exc_info.value)

    def test_promotional_billing_schema(self):
        """Test promotional billing schema."""
        billing_data = {
            "vendor_id": 1,
            "billing_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "total_spend": 15000.00,
            "platform_fees": 750.00,
            "tax_amount": 1125.00,
            "total_amount": 16875.00,
            "payment_breakdown": {
                "paystack": 10000.00,
                "stripe": 5000.00,
                "busha": 0.00
            },
            "campaign_breakdown": [
                {"campaign_id": 1, "spend": 8000.00},
                {"campaign_id": 2, "spend": 7000.00}
            ]
        }

        billing = PromotionalBillingSchema(**billing_data)
        assert billing.total_spend == Decimal("15000.00")
        assert billing.total_amount == Decimal("16875.00")
        assert billing.payment_breakdown["paystack"] == Decimal("10000.00")


class TestSchemaValidationEdgeCases:
    """Test schema validation edge cases and error handling."""

    def test_currency_validation(self):
        """Test multi-currency support validation."""
        # Test supported currencies
        supported_currencies = ["NGN", "USD", "EUR", "GBP", "BTC", "ETH", "USDT", "USDC"]

        for currency in supported_currencies:
            spend = AdSpendCreate(
                campaign_id=1,
                advertisement_id=1,
                spend_category="campaign_budget",
                amount=100.00,
                currency=currency
            )
            assert spend.currency == currency

        # Test unsupported currency
        with pytest.raises(ValidationError) as exc_info:
            AdSpendCreate(
                campaign_id=1,
                advertisement_id=1,
                spend_category="campaign_budget",
                amount=100.00,
                currency="JPY"  # Unsupported currency
            )
        assert "Unsupported currency" in str(exc_info.value)

    def test_xss_prevention_validation(self):
        """Test XSS prevention across all text fields."""
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "&#60;script&#62;alert('xss')&#60;/script&#62;"
        ]

        for malicious_input in malicious_inputs:
            with pytest.raises(ValidationError):
                CampaignCreate(
                    name=f"Campaign {malicious_input}",
                    campaign_type=CampaignType.FEATURED_LISTING,
                    campaign_objective=CampaignObjective.BOOKING_CONVERSION,
                    total_budget=Decimal("5000.00")
                )

    def test_pydantic_v2_compliance(self, campaign_factory):
        """Test Pydantic V2 compliance and ConfigDict functionality."""
        campaign = campaign_factory()

        # Test model_validate (Pydantic V2)
        campaign_response = CampaignResponse.model_validate(campaign)
        assert campaign_response.id == campaign.id

        # Test model_dump (Pydantic V2)
        campaign_dict = campaign_response.model_dump()
        assert isinstance(campaign_dict, dict)
        assert campaign_dict["id"] == campaign.id

        # Test model_dump with exclude
        campaign_dict_partial = campaign_response.model_dump(exclude={"created_at", "updated_at"})
        assert "created_at" not in campaign_dict_partial
        assert "updated_at" not in campaign_dict_partial

    def test_decimal_precision_validation(self):
        """Test decimal precision validation for financial fields."""
        # Test valid precision
        campaign = CampaignCreate(
            name="Test Campaign",
            campaign_type=CampaignType.FEATURED_LISTING,
            campaign_objective=CampaignObjective.BOOKING_CONVERSION,
            total_budget=Decimal("5000.50"),  # 2 decimal places
            daily_budget=Decimal("200.25")    # 2 decimal places
        )
        assert campaign.total_budget == Decimal("5000.50")

        # Test excessive precision (should be rounded)
        campaign = CampaignCreate(
            name="Test Campaign",
            campaign_type=CampaignType.FEATURED_LISTING,
            campaign_objective=CampaignObjective.BOOKING_CONVERSION,
            total_budget=Decimal("5000.123456"),  # More than 2 decimal places
            daily_budget=Decimal("200.789")       # More than 2 decimal places
        )
        # Should be rounded to 2 decimal places
        assert campaign.total_budget == Decimal("5000.12")
        assert campaign.daily_budget == Decimal("200.79")
