# Culture Connect Backend - Kubernetes Namespace
# Base namespace configuration for the application

apiVersion: v1
kind: Namespace
metadata:
  name: culture-connect
  labels:
    name: culture-connect
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
    environment: production
    project: culture-connect
  annotations:
    description: "Culture Connect Backend application namespace"
    contact: "<EMAIL>"
    documentation: "https://docs.cultureconnect.ng"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: culture-connect-quota
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: resource-quota
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    pods: "50"
    services: "20"
    secrets: "20"
    configmaps: "20"
    count/deployments.apps: "20"
    count/replicasets.apps: "50"
    count/statefulsets.apps: "10"
    count/jobs.batch: "20"
    count/cronjobs.batch: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: culture-connect-limits
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: limit-range
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - max:
      cpu: "2"
      memory: "4Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
    type: Container
  - max:
      storage: "10Gi"
    min:
      storage: "1Gi"
    type: PersistentVolumeClaim
