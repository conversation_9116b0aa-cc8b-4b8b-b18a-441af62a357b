"""
Service Listing Service for Culture Connect Backend API.

This module provides comprehensive service listing management functionality including:
- Service CRUD operations with validation and business rules
- Service status management and workflow
- Service search and filtering capabilities
- Integration with vendor profile and authentication systems
- Service performance tracking and analytics

Implements Task 3.2.1 requirements for service listing system with
production-grade business logic, validation, and integration with existing systems.
"""

import logging
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.logging import correlation_id
from app.services.base import BaseService, NotFoundError, ValidationError, ServiceError, ConflictError
from app.models.service import Service, ServiceStatus
from app.models.vendor import Vendor
from app.repositories.service_repository import ServiceRepository
from app.repositories.vendor_repository import VendorRepository
from app.schemas.service_extended import (
    ServiceCreate, ServiceUpdate, ServiceResponse,
    ServiceListResponse, ServiceSearchRequest
)

logger = logging.getLogger(__name__)


class ServiceListingService(BaseService[Service, ServiceRepository]):
    """
    Service listing service for comprehensive service management operations.

    Provides business logic for:
    - Service CRUD operations with validation
    - Service status management and workflow
    - Service search and filtering capabilities
    - Integration with vendor profile system
    - Service performance tracking
    """

    def __init__(self, db: AsyncSession):
        """Initialize service listing service."""
        super().__init__(ServiceRepository, Service, db_session=db)
        self.vendor_repository = VendorRepository(db)

    async def create_service(
        self,
        vendor_id: int,
        service_data: ServiceCreate
    ) -> ServiceResponse:
        """
        Create a new service listing for a vendor.

        Args:
            vendor_id: ID of the vendor creating the service
            service_data: Service creation data

        Returns:
            ServiceResponse: Created service with full details

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If service data is invalid
            ConflictError: If service slug already exists for vendor
        """
        correlation = self.log_operation_start(
            "create_service",
            vendor_id=vendor_id,
            service_title=service_data.title
        )

        try:
            # Verify vendor exists and is active
            vendor = await self.vendor_repository.get_by_id(vendor_id)
            if not vendor:
                raise NotFoundError(f"Vendor with ID {vendor_id} not found")

            if vendor.marketplace_status.value != "active":
                raise ValidationError("Vendor must be active to create services")

            # Validate service data
            await self._validate_service_data(service_data, vendor)

            # Generate unique slug
            slug = await self._generate_unique_slug(vendor_id, service_data.title)

            # Create service
            service_dict = service_data.model_dump(exclude_unset=True)
            service_dict.update({
                "vendor_id": vendor_id,
                "slug": slug,
                "status": ServiceStatus.DRAFT,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })

            service = await self.repository.create(service_dict)

            # Load service with relations for response
            service_with_relations = await self.repository.get_with_relations(service.id)

            response = ServiceResponse.model_validate(service_with_relations)

            self.log_operation_success(
                correlation,
                f"Service created successfully: {service.title} (ID: {service.id})"
            )

            return response

        except (NotFoundError, ValidationError, ConflictError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "create_service",
                {"vendor_id": vendor_id, "service_title": service_data.title}
            )

    async def update_service(
        self,
        service_id: int,
        vendor_id: int,
        service_data: ServiceUpdate
    ) -> ServiceResponse:
        """
        Update an existing service listing.

        Args:
            service_id: ID of the service to update
            vendor_id: ID of the vendor (for ownership verification)
            service_data: Service update data

        Returns:
            ServiceResponse: Updated service with full details

        Raises:
            NotFoundError: If service not found
            ValidationError: If update data is invalid
            ConflictError: If vendor doesn't own the service
        """
        correlation = self.log_operation_start(
            "update_service",
            service_id=service_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing service
            service = await self.repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Verify ownership
            if service.vendor_id != vendor_id:
                raise ConflictError("Service does not belong to this vendor")

            # Validate update data
            await self._validate_service_update(service, service_data)

            # Update service
            update_dict = service_data.model_dump(exclude_unset=True)
            update_dict["updated_at"] = datetime.now(timezone.utc)

            # Handle slug update if title changed
            if "title" in update_dict and update_dict["title"] != service.title:
                update_dict["slug"] = await self._generate_unique_slug(
                    vendor_id, update_dict["title"], exclude_service_id=service_id
                )

            updated_service = await self.repository.update(service_id, update_dict)

            # Load service with relations for response
            service_with_relations = await self.repository.get_with_relations(updated_service.id)

            response = ServiceResponse.model_validate(service_with_relations)

            self.log_operation_success(
                correlation,
                f"Service updated successfully: {updated_service.title} (ID: {service_id})"
            )

            return response

        except (NotFoundError, ValidationError, ConflictError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "update_service",
                {"service_id": service_id, "vendor_id": vendor_id}
            )

    async def get_service(
        self,
        service_id: int,
        include_inactive: bool = False
    ) -> ServiceResponse:
        """
        Get a service by ID with full details.

        Args:
            service_id: ID of the service to retrieve
            include_inactive: Whether to include inactive services

        Returns:
            ServiceResponse: Service with full details

        Raises:
            NotFoundError: If service not found
        """
        correlation = self.log_operation_start(
            "get_service",
            service_id=service_id,
            include_inactive=include_inactive
        )

        try:
            service = await self.repository.get_with_relations(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Check if service is active (unless explicitly including inactive)
            if not include_inactive and service.status == ServiceStatus.INACTIVE:
                raise NotFoundError(f"Service with ID {service_id} not found")

            response = ServiceResponse.model_validate(service)

            self.log_operation_success(
                correlation,
                f"Service retrieved successfully: {service.title} (ID: {service_id})"
            )

            return response

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_service",
                {"service_id": service_id, "include_inactive": include_inactive}
            )

    async def list_vendor_services(
        self,
        vendor_id: int,
        status: Optional[ServiceStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> ServiceListResponse:
        """
        List services for a specific vendor.

        Args:
            vendor_id: ID of the vendor
            status: Optional status filter
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            ServiceListResponse: List of services with pagination

        Raises:
            NotFoundError: If vendor not found
        """
        correlation = self.log_operation_start(
            "list_vendor_services",
            vendor_id=vendor_id,
            status=status.value if status else None,
            skip=skip,
            limit=limit
        )

        try:
            # Verify vendor exists
            vendor = await self.vendor_repository.get_by_id(vendor_id)
            if not vendor:
                raise NotFoundError(f"Vendor with ID {vendor_id} not found")

            # Get services
            services = await self.repository.get_by_vendor(vendor_id, status)

            # Apply pagination
            total = len(services)
            paginated_services = services[skip:skip + limit]

            # Convert to response format
            service_responses = [
                ServiceResponse.model_validate(service)
                for service in paginated_services
            ]

            # Calculate pagination metadata
            pages = (total + limit - 1) // limit
            current_page = (skip // limit) + 1

            response = ServiceListResponse(
                services=service_responses,
                total=total,
                page=current_page,
                per_page=limit,
                pages=pages,
                has_next=current_page < pages,
                has_prev=current_page > 1
            )

            self.log_operation_success(
                correlation,
                f"Listed {len(service_responses)} services for vendor {vendor_id}"
            )

            return response

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "list_vendor_services",
                {"vendor_id": vendor_id, "status": status, "skip": skip, "limit": limit}
            )

    async def search_services(
        self,
        search_request: ServiceSearchRequest
    ) -> ServiceListResponse:
        """
        Search services with advanced filtering and sorting.

        Args:
            search_request: Search criteria and pagination

        Returns:
            ServiceListResponse: Search results with pagination
        """
        correlation = self.log_operation_start(
            "search_services",
            query=search_request.query,
            category_id=search_request.category_id,
            page=search_request.page
        )

        try:
            # Perform search
            services, total = await self.repository.search_services(search_request)

            # Convert to response format
            service_responses = [
                ServiceResponse.model_validate(service)
                for service in services
            ]

            # Calculate pagination metadata
            pages = (total + search_request.per_page - 1) // search_request.per_page

            response = ServiceListResponse(
                services=service_responses,
                total=total,
                page=search_request.page,
                per_page=search_request.per_page,
                pages=pages,
                has_next=search_request.page < pages,
                has_prev=search_request.page > 1
            )

            self.log_operation_success(
                correlation,
                f"Search returned {len(service_responses)} services (total: {total})"
            )

            return response

        except Exception as e:
            await self.handle_service_error(
                e, "search_services",
                {"search_request": search_request.model_dump()}
            )

    async def _validate_service_data(self, service_data: ServiceCreate, vendor: Vendor) -> None:
        """Validate service creation data."""
        # Check if vendor can create more services (business rules)
        existing_services = await self.repository.get_by_vendor(vendor.id)

        # Example business rule: limit services based on vendor tier
        max_services = self._get_max_services_for_vendor(vendor)
        if len(existing_services) >= max_services:
            raise ValidationError(f"Vendor has reached maximum service limit ({max_services})")

        # Validate pricing
        if service_data.base_price and service_data.base_price < 0:
            raise ValidationError("Base price cannot be negative")

        # Validate participants
        if service_data.max_participants and service_data.min_participants:
            if service_data.max_participants < service_data.min_participants:
                raise ValidationError("Maximum participants cannot be less than minimum participants")

    async def _validate_service_update(self, service: Service, update_data: ServiceUpdate) -> None:
        """Validate service update data."""
        # Validate status transitions
        if hasattr(update_data, 'status') and update_data.status:
            if not self._is_valid_status_transition(service.status, update_data.status):
                raise ValidationError(f"Invalid status transition from {service.status} to {update_data.status}")

    def _get_max_services_for_vendor(self, vendor: Vendor) -> int:
        """Get maximum number of services allowed for vendor based on tier."""
        # This could be based on vendor tier, subscription, etc.
        return 50  # Default limit

    def _is_valid_status_transition(self, current_status: ServiceStatus, new_status: ServiceStatus) -> bool:
        """Check if status transition is valid."""
        valid_transitions = {
            ServiceStatus.DRAFT: [ServiceStatus.ACTIVE, ServiceStatus.INACTIVE],
            ServiceStatus.ACTIVE: [ServiceStatus.INACTIVE, ServiceStatus.DRAFT],
            ServiceStatus.INACTIVE: [ServiceStatus.ACTIVE, ServiceStatus.DRAFT]
        }
        return new_status in valid_transitions.get(current_status, [])

    async def _generate_unique_slug(
        self,
        vendor_id: int,
        title: str,
        exclude_service_id: Optional[int] = None
    ) -> str:
        """Generate a unique slug for the service."""
        import re

        # Create base slug from title
        base_slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
        base_slug = re.sub(r'\s+', '-', base_slug.strip())

        # Check for uniqueness
        counter = 0
        slug = base_slug

        while True:
            # Check if slug exists for this vendor
            existing = await self.repository.get_by_vendor_slug(vendor_id, slug)
            if not existing or (exclude_service_id and existing.id == exclude_service_id):
                break

            counter += 1
            slug = f"{base_slug}-{counter}"

        return slug

    async def delete_service(
        self,
        service_id: int,
        vendor_id: int
    ) -> bool:
        """
        Delete a service listing (soft delete by setting status to inactive).

        Args:
            service_id: ID of the service to delete
            vendor_id: ID of the vendor (for ownership verification)

        Returns:
            bool: True if deletion successful

        Raises:
            NotFoundError: If service not found
            ValidationError: If vendor doesn't own the service
        """
        correlation = self.log_operation_start(
            "delete_service",
            service_id=service_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing service
            service = await self.repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Verify ownership
            if service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Soft delete by setting status to inactive
            await self.repository.update(service_id, {
                "status": ServiceStatus.INACTIVE,
                "updated_at": datetime.now(timezone.utc)
            })

            self.log_operation_success(
                correlation,
                f"Service deleted successfully: {service.title} (ID: {service_id})"
            )

            return True

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "delete_service",
                {"service_id": service_id, "vendor_id": vendor_id}
            )
