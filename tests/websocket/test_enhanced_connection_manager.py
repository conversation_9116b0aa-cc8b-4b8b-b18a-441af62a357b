#!/usr/bin/env python3
"""
Enhanced WebSocket Connection Manager Tests for Task 6.1.1 Phase 6.

This module tests the enhanced WebSocket connection manager implementation including:
- Real-time connection establishment with JWT authentication
- Room-based connection management and participant tracking
- Message broadcasting with performance optimization
- Connection health monitoring with heartbeat/ping-pong
- Integration with existing WebSocket services and repositories

Performance targets: <1000ms connection, <100ms message delivery, >99% reliability
"""

import pytest
import asyncio
import json
import time
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import WebSocket
from sqlalchemy.ext.asyncio import AsyncSession

# Import the enhanced connection manager
from app.api.v1.endpoints.websocket_endpoints import EnhancedWebSocketConnectionManager
from app.models.user import User
from app.schemas.websocket_schemas import WebSocketConnectionCreate


class TestEnhancedWebSocketConnectionManager:
    """Test suite for enhanced WebSocket connection manager."""

    @pytest.fixture
    def connection_manager(self):
        """Create a fresh connection manager for each test."""
        return EnhancedWebSocketConnectionManager()

    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket connection."""
        websocket = AsyncMock(spec=WebSocket)
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()
        websocket.close = AsyncMock()
        websocket.ping = AsyncMock()
        websocket.client = MagicMock()
        websocket.client.host = "127.0.0.1"
        websocket.headers = {"user-agent": "test-client"}
        return websocket

    @pytest.fixture
    def mock_user(self):
        """Create a mock user."""
        user = User()
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_active = True
        return user

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.mark.asyncio
    async def test_enhanced_connection_establishment(
        self, 
        connection_manager, 
        mock_websocket, 
        mock_user, 
        mock_db_session
    ):
        """Test enhanced WebSocket connection establishment with authentication."""
        connection_id = "test_connection_123"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.create_connection = AsyncMock()
            
            # Test connection establishment
            start_time = time.time()
            success = await connection_manager.connect(
                mock_websocket, connection_id, mock_user.id, mock_db_session, mock_user
            )
            connection_time = (time.time() - start_time) * 1000
            
            # Verify connection success
            assert success is True
            assert connection_time < 1000  # <1000ms target
            
            # Verify WebSocket was accepted
            mock_websocket.accept.assert_called_once()
            
            # Verify connection tracking
            assert connection_id in connection_manager.active_connections
            assert mock_user.id in connection_manager.user_connections
            assert connection_id in connection_manager.user_connections[mock_user.id]
            
            # Verify metadata storage
            assert connection_id in connection_manager.connection_metadata
            metadata = connection_manager.connection_metadata[connection_id]
            assert metadata["user_id"] == mock_user.id
            assert metadata["user"] == mock_user
            assert "connected_at" in metadata
            assert "client_info" in metadata
            
            # Verify health monitoring initialization
            assert connection_id in connection_manager.connection_health
            health = connection_manager.connection_health[connection_id]
            assert health["is_healthy"] is True
            assert health["missed_pings"] == 0
            
            # Verify message queue initialization
            assert connection_id in connection_manager.message_queues
            assert connection_manager.message_queues[connection_id].maxsize == 1000

    @pytest.mark.asyncio
    async def test_room_joining_and_leaving(
        self, 
        connection_manager, 
        mock_websocket, 
        mock_user, 
        mock_db_session
    ):
        """Test room joining and leaving functionality."""
        connection_id = "test_connection_123"
        room_id = 1
        
        # Mock services
        with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService'), \
             patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_room_service, \
             patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomParticipantService') as mock_participant_service:
            
            # Setup mocks
            mock_room_service_instance = AsyncMock()
            mock_room_service.return_value = mock_room_service_instance
            mock_room_service_instance.get_room_by_id = AsyncMock(return_value=MagicMock())
            
            mock_participant_service_instance = AsyncMock()
            mock_participant_service.return_value = mock_participant_service_instance
            mock_participant_service_instance.add_participant = AsyncMock()
            mock_participant_service_instance.remove_participant = AsyncMock()
            
            # Establish connection first
            await connection_manager.connect(
                mock_websocket, connection_id, mock_user.id, mock_db_session, mock_user
            )
            
            # Test room joining
            start_time = time.time()
            join_success = await connection_manager.join_room(connection_id, room_id, mock_db_session)
            join_time = (time.time() - start_time) * 1000
            
            assert join_success is True
            assert join_time < 500  # <500ms target
            
            # Verify room tracking
            assert room_id in connection_manager.room_connections
            assert connection_id in connection_manager.room_connections[room_id]
            assert room_id in connection_manager.connection_metadata[connection_id]["rooms"]
            
            # Test room leaving
            start_time = time.time()
            leave_success = await connection_manager.leave_room(connection_id, room_id, mock_db_session)
            leave_time = (time.time() - start_time) * 1000
            
            assert leave_success is True
            assert leave_time < 500  # <500ms target
            
            # Verify room cleanup
            assert room_id not in connection_manager.room_connections or \
                   connection_id not in connection_manager.room_connections[room_id]
            assert room_id not in connection_manager.connection_metadata[connection_id]["rooms"]

    @pytest.mark.asyncio
    async def test_message_broadcasting_performance(
        self, 
        connection_manager, 
        mock_user, 
        mock_db_session
    ):
        """Test message broadcasting performance to multiple connections."""
        room_id = 1
        message = json.dumps({"type": "test", "data": "broadcast message"})
        
        # Create multiple mock connections
        connections = []
        for i in range(5):
            mock_websocket = AsyncMock(spec=WebSocket)
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            mock_websocket.client = MagicMock()
            mock_websocket.client.host = f"127.0.0.{i+1}"
            mock_websocket.headers = {"user-agent": "test-client"}
            
            connection_id = f"test_connection_{i}"
            
            with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService'):
                await connection_manager.connect(
                    mock_websocket, connection_id, mock_user.id + i, mock_db_session, mock_user
                )
            
            # Add to room
            connection_manager.room_connections[room_id] = connection_manager.room_connections.get(room_id, set())
            connection_manager.room_connections[room_id].add(connection_id)
            connection_manager.connection_metadata[connection_id]["rooms"].add(room_id)
            
            connections.append((connection_id, mock_websocket))
        
        # Test broadcasting performance
        start_time = time.time()
        delivered_count = await connection_manager.broadcast_to_room(room_id, message)
        broadcast_time = (time.time() - start_time) * 1000
        
        # Verify performance and delivery
        assert broadcast_time < 100  # <100ms target
        assert delivered_count == 5  # All connections should receive the message
        
        # Verify all WebSockets received the message
        for connection_id, mock_websocket in connections:
            mock_websocket.send_text.assert_called_with(message)

    @pytest.mark.asyncio
    async def test_connection_health_monitoring(
        self, 
        connection_manager, 
        mock_websocket, 
        mock_user, 
        mock_db_session
    ):
        """Test connection health monitoring and heartbeat functionality."""
        connection_id = "test_connection_123"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService'):
            # Establish connection
            await connection_manager.connect(
                mock_websocket, connection_id, mock_user.id, mock_db_session, mock_user
            )
            
            # Verify health monitoring initialization
            assert connection_id in connection_manager.connection_health
            health = connection_manager.connection_health[connection_id]
            assert health["is_healthy"] is True
            assert health["missed_pings"] == 0
            assert health["ping_count"] == 0
            
            # Test marking connection as unhealthy
            await connection_manager._mark_connection_for_cleanup(connection_id)
            
            # Verify health status update
            health = connection_manager.connection_health[connection_id]
            assert health["is_healthy"] is False
            assert health["missed_pings"] == 1

    @pytest.mark.asyncio
    async def test_connection_statistics(
        self, 
        connection_manager, 
        mock_websocket, 
        mock_user, 
        mock_db_session
    ):
        """Test connection statistics and metrics collection."""
        connection_id = "test_connection_123"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService'):
            # Establish connection
            await connection_manager.connect(
                mock_websocket, connection_id, mock_user.id, mock_db_session, mock_user
            )
            
            # Get statistics
            stats = await connection_manager.get_connection_stats()
            
            # Verify statistics
            assert stats["total_connections"] == 1
            assert stats["active_connections"] == 1
            assert stats["active_users"] == 1
            assert stats["active_rooms"] == 0
            assert connection_id in stats["message_queue_sizes"]
            assert stats["message_queue_sizes"][connection_id] == 0
            assert connection_id in stats["connection_health"]
            assert stats["connection_health"][connection_id] is True

    @pytest.mark.asyncio
    async def test_enhanced_disconnection_cleanup(
        self, 
        connection_manager, 
        mock_websocket, 
        mock_user, 
        mock_db_session
    ):
        """Test enhanced disconnection with comprehensive cleanup."""
        connection_id = "test_connection_123"
        room_id = 1
        
        with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.create_connection = AsyncMock()
            
            # Establish connection and join room
            await connection_manager.connect(
                mock_websocket, connection_id, mock_user.id, mock_db_session, mock_user
            )
            
            # Add to room manually for testing
            connection_manager.room_connections[room_id] = {connection_id}
            connection_manager.connection_metadata[connection_id]["rooms"].add(room_id)
            
            # Test disconnection
            start_time = time.time()
            await connection_manager.disconnect(connection_id, mock_db_session, "test_disconnect")
            disconnect_time = (time.time() - start_time) * 1000
            
            # Verify performance
            assert disconnect_time < 200  # <200ms target
            
            # Verify cleanup
            assert connection_id not in connection_manager.active_connections
            assert connection_id not in connection_manager.connection_metadata
            assert connection_id not in connection_manager.connection_health
            assert connection_id not in connection_manager.rate_limiters
            assert connection_id not in connection_manager.message_queues
            
            # Verify room cleanup
            assert room_id not in connection_manager.room_connections or \
                   connection_id not in connection_manager.room_connections[room_id]
            
            # Verify user connections cleanup
            assert mock_user.id not in connection_manager.user_connections or \
                   connection_id not in connection_manager.user_connections[mock_user.id]
            
            # Verify WebSocket was closed
            mock_websocket.close.assert_called_once_with(code=1000, reason="test_disconnect")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
