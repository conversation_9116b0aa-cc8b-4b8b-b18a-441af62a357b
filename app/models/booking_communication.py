"""
Booking Communication System models for Culture Connect Backend API.

This module defines enhanced communication models for booking-related messaging,
file attachments, automated templates, and delivery tracking.

Implements Task 4.1.3 requirements for booking communication system with:
- Enhanced messaging with threading and delivery tracking
- File attachment support with security validation
- Automated message templates with event triggers
- Multi-channel delivery tracking and retry logic
- Real-time messaging support with WebSocket integration

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import enum
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, JSON, ForeignKey,
    Index, CheckConstraint, Enum, BigInteger
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit


class MessageType(str, enum.Enum):
    """Enumeration of message types for communication categorization."""
    USER_MESSAGE = "user_message"           # Direct user-to-user message
    AUTOMATED = "automated"                 # Automated system message
    SYSTEM = "system"                      # System notification
    BOOKING_UPDATE = "booking_update"       # Booking status update
    PAYMENT_REMINDER = "payment_reminder"   # Payment-related message
    REVIEW_REQUEST = "review_request"       # Review request message


class MessageStatus(str, enum.Enum):
    """Enumeration of message delivery statuses."""
    DRAFT = "draft"                        # Message saved as draft
    SENT = "sent"                         # Message sent successfully
    DELIVERED = "delivered"               # Message delivered to recipient
    READ = "read"                         # Message read by recipient
    FAILED = "failed"                     # Message delivery failed


class DeliveryMethod(str, enum.Enum):
    """Enumeration of message delivery methods."""
    WEBSOCKET = "websocket"               # Real-time WebSocket delivery
    EMAIL = "email"                       # Email notification
    PUSH = "push"                        # Push notification
    SMS = "sms"                          # SMS notification


class DeliveryStatus(str, enum.Enum):
    """Enumeration of delivery attempt statuses."""
    PENDING = "pending"                   # Delivery pending
    SENT = "sent"                        # Delivery attempt made
    DELIVERED = "delivered"              # Successfully delivered
    FAILED = "failed"                    # Delivery failed
    RETRY = "retry"                      # Scheduled for retry


class TemplateCategory(str, enum.Enum):
    """Enumeration of message template categories."""
    BOOKING_LIFECYCLE = "booking_lifecycle"     # Booking status changes
    PAYMENT = "payment"                         # Payment-related messages
    AVAILABILITY = "availability"               # Availability updates
    COMMUNICATION = "communication"             # General communication
    MARKETING = "marketing"                     # Marketing messages
    SYSTEM = "system"                          # System notifications


class BookingMessage(BaseModelWithAudit):
    """
    Enhanced booking message model with threading and delivery tracking.

    Manages all communication between customers and vendors related to
    specific bookings including direct messages, automated notifications,
    and system updates with comprehensive threading and delivery tracking.
    """

    __tablename__ = "booking_messages"

    # Foreign key relationships
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )

    sender_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who sent the message (null for system messages)"
    )

    recipient_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who should receive the message"
    )

    # Message content and classification
    message_type = Column(
        Enum(MessageType),
        nullable=False,
        default=MessageType.USER_MESSAGE,
        index=True,
        doc="Type of message for categorization"
    )

    subject = Column(
        String(200),
        nullable=True,
        doc="Message subject line"
    )

    content = Column(
        Text,
        nullable=False,
        doc="Message content body"
    )

    is_automated = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether message was automatically generated"
    )

    template_id = Column(
        Integer,
        ForeignKey("message_templates.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Template used for automated messages"
    )

    # Threading and conversation organization
    thread_id = Column(
        UUID(as_uuid=True),
        default=uuid.uuid4,
        nullable=True,
        index=True,
        doc="Thread identifier for conversation grouping"
    )

    parent_message_id = Column(
        Integer,
        ForeignKey("booking_messages.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Parent message for reply threading"
    )

    message_order = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Message order within thread"
    )

    # Status and delivery tracking
    status = Column(
        Enum(MessageStatus),
        nullable=False,
        default=MessageStatus.SENT,
        index=True,
        doc="Current message status"
    )

    is_read = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether message has been read by recipient"
    )

    read_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp when message was read"
    )

    # Delivery tracking
    delivery_status = Column(
        JSON,
        nullable=True,
        doc="Detailed delivery status for multiple channels"
    )

    # Message metadata and context
    message_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional message context and metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="messages",
        doc="Associated booking"
    )

    sender = relationship(
        "User",
        foreign_keys=[sender_id],
        doc="Message sender"
    )

    recipient = relationship(
        "User",
        foreign_keys=[recipient_id],
        doc="Message recipient"
    )

    template = relationship(
        "MessageTemplate",
        back_populates="messages",
        doc="Associated message template"
    )

    parent_message = relationship(
        "BookingMessage",
        remote_side="BookingMessage.id",
        doc="Parent message for threading"
    )

    attachments = relationship(
        "MessageAttachment",
        back_populates="message",
        cascade="all, delete-orphan",
        doc="Message file attachments"
    )

    delivery_logs = relationship(
        "MessageDeliveryLog",
        back_populates="message",
        cascade="all, delete-orphan",
        doc="Message delivery tracking logs"
    )

    # Performance indexes
    __table_args__ = (
        Index("idx_booking_messages_booking_id", "booking_id"),
        Index("idx_booking_messages_thread", "thread_id", "message_order"),
        Index("idx_booking_messages_status", "status", "created_at"),
        Index("idx_booking_messages_unread", "recipient_id", "is_read", "created_at"),
        Index("idx_booking_messages_sender_date", "sender_id", "created_at"),
        Index("idx_booking_messages_type_date", "message_type", "created_at"),
        Index("idx_booking_messages_automated", "is_automated", "created_at"),
    )

    def __repr__(self) -> str:
        """String representation of the booking message."""
        return f"<BookingMessage(id={self.id}, booking_id={self.booking_id}, type='{self.message_type}')>"

    @property
    def is_system_message(self) -> bool:
        """Check if message is a system-generated message."""
        return self.message_type in {MessageType.SYSTEM, MessageType.AUTOMATED}

    @property
    def has_attachments(self) -> bool:
        """Check if message has file attachments."""
        return len(self.attachments) > 0

    @property
    def delivery_summary(self) -> Dict[str, Any]:
        """Get summary of delivery status across all channels."""
        if not self.delivery_status:
            return {"total_channels": 0, "successful": 0, "failed": 0, "pending": 0}

        summary = {"total_channels": 0, "successful": 0, "failed": 0, "pending": 0}
        for channel, status in self.delivery_status.items():
            summary["total_channels"] += 1
            if status.get("status") == "delivered":
                summary["successful"] += 1
            elif status.get("status") == "failed":
                summary["failed"] += 1
            else:
                summary["pending"] += 1

        return summary


class MessageAttachment(BaseModelWithAudit):
    """
    Message attachment model with security validation and multi-provider storage.

    Handles file attachments for booking messages with comprehensive security
    validation, virus scanning, and support for multiple storage providers.
    """

    __tablename__ = "message_attachments"

    # Foreign key relationship
    message_id = Column(
        Integer,
        ForeignKey("booking_messages.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated message ID"
    )

    # File identification and metadata
    filename = Column(
        String(255),
        nullable=False,
        doc="Stored filename (may be different from original)"
    )

    original_filename = Column(
        String(255),
        nullable=False,
        doc="Original filename as uploaded by user"
    )

    file_size = Column(
        BigInteger,
        nullable=False,
        doc="File size in bytes"
    )

    mime_type = Column(
        String(100),
        nullable=False,
        index=True,
        doc="MIME type of the file"
    )

    file_path = Column(
        String(500),
        nullable=False,
        doc="Storage path or key for file retrieval"
    )

    # Storage provider details
    storage_provider = Column(
        String(50),
        default="local",
        nullable=False,
        index=True,
        doc="Storage provider (local, s3, gcs)"
    )

    storage_url = Column(
        Text,
        nullable=True,
        doc="Public URL for file access (if applicable)"
    )

    thumbnail_url = Column(
        Text,
        nullable=True,
        doc="Thumbnail URL for image attachments"
    )

    # Security and validation
    file_hash = Column(
        String(64),
        nullable=True,
        index=True,
        doc="SHA-256 hash for file integrity verification"
    )

    virus_scan_status = Column(
        String(50),
        default="pending",
        nullable=False,
        index=True,
        doc="Virus scan status (pending, clean, infected, error)"
    )

    virus_scan_result = Column(
        JSON,
        nullable=True,
        doc="Detailed virus scan results and metadata"
    )

    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether file is publicly accessible"
    )

    # Upload and processing metadata
    upload_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional upload and processing metadata"
    )

    # Relationships
    message = relationship(
        "BookingMessage",
        back_populates="attachments",
        doc="Associated booking message"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint(
            "file_size > 0 AND file_size <= 52428800",  # 50MB limit
            name="chk_file_size_limit"
        ),
        Index("idx_message_attachments_message", "message_id"),
        Index("idx_message_attachments_status", "virus_scan_status"),
        Index("idx_message_attachments_type", "mime_type"),
        Index("idx_message_attachments_provider", "storage_provider"),
    )

    def __repr__(self) -> str:
        """String representation of the message attachment."""
        return f"<MessageAttachment(id={self.id}, filename='{self.filename}', size={self.file_size})>"

    @property
    def is_image(self) -> bool:
        """Check if attachment is an image file."""
        return self.mime_type.startswith("image/") if self.mime_type else False

    @property
    def is_document(self) -> bool:
        """Check if attachment is a document file."""
        document_types = {
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/plain",
            "text/csv"
        }
        return self.mime_type in document_types if self.mime_type else False

    @property
    def file_size_mb(self) -> float:
        """Get file size in megabytes."""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0.0

    @property
    def is_safe(self) -> bool:
        """Check if file has passed security validation."""
        return self.virus_scan_status == "clean"


class MessageTemplate(BaseModelWithAudit):
    """
    Message template model for automated messaging with event triggers.

    Manages reusable message templates for automated booking communications
    with variable substitution, event triggers, and version control.
    """

    __tablename__ = "message_templates"

    # Template identification
    template_key = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique template identifier key"
    )

    name = Column(
        String(200),
        nullable=False,
        doc="Human-readable template name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Template description and usage notes"
    )

    category = Column(
        Enum(TemplateCategory),
        nullable=False,
        index=True,
        doc="Template category for organization"
    )

    # Template content
    subject_template = Column(
        Text,
        nullable=False,
        doc="Subject line template with variable placeholders"
    )

    content_template = Column(
        Text,
        nullable=False,
        doc="Message content template with variable placeholders"
    )

    template_variables = Column(
        JSON,
        nullable=True,
        doc="Available variables for template substitution"
    )

    # Targeting and activation conditions
    trigger_events = Column(
        JSON,
        nullable=True,
        doc="Events that trigger this template"
    )

    conditions = Column(
        JSON,
        nullable=True,
        doc="Conditions for template activation"
    )

    recipient_type = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Target recipient type (customer, vendor, both)"
    )

    # Template management
    version = Column(
        Integer,
        default=1,
        nullable=False,
        doc="Template version number"
    )

    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether template is active and available"
    )

    is_system_template = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether template is a system template"
    )

    # Template authorship
    created_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who created the template"
    )

    # Relationships
    created_by_user = relationship(
        "User",
        foreign_keys=[created_by],
        doc="User who created the template"
    )

    messages = relationship(
        "BookingMessage",
        back_populates="template",
        doc="Messages generated from this template"
    )

    # Performance indexes
    __table_args__ = (
        Index("idx_message_templates_key", "template_key"),
        Index("idx_message_templates_category", "category", "is_active"),
        Index("idx_message_templates_active", "is_active", "created_at"),
        Index("idx_message_templates_system", "is_system_template", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of the message template."""
        return f"<MessageTemplate(id={self.id}, key='{self.template_key}', version={self.version})>"

    @property
    def variable_count(self) -> int:
        """Get count of available template variables."""
        return len(self.template_variables) if self.template_variables else 0

    @property
    def trigger_event_count(self) -> int:
        """Get count of trigger events."""
        return len(self.trigger_events) if self.trigger_events else 0


class MessageDeliveryLog(BaseModelWithAudit):
    """
    Message delivery log model for tracking multi-channel delivery attempts.

    Tracks delivery attempts across multiple channels (WebSocket, email, push)
    with comprehensive retry logic, error handling, and performance analytics.
    """

    __tablename__ = "message_delivery_logs"

    # Foreign key relationship
    message_id = Column(
        Integer,
        ForeignKey("booking_messages.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated message ID"
    )

    # Delivery method and provider
    delivery_method = Column(
        Enum(DeliveryMethod),
        nullable=False,
        index=True,
        doc="Delivery method used (websocket, email, push, sms)"
    )

    delivery_status = Column(
        Enum(DeliveryStatus),
        nullable=False,
        default=DeliveryStatus.PENDING,
        index=True,
        doc="Current delivery status"
    )

    delivery_provider = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Delivery provider (internal, smtp, fcm, twilio)"
    )

    # Timing information
    attempted_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="When delivery was attempted"
    )

    delivered_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When delivery was confirmed successful"
    )

    failed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When delivery failed"
    )

    # Error handling and retry logic
    error_code = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Error code for failed deliveries"
    )

    error_message = Column(
        Text,
        nullable=True,
        doc="Detailed error message for failed deliveries"
    )

    retry_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of retry attempts made"
    )

    max_retries = Column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum number of retry attempts allowed"
    )

    next_retry_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When next retry attempt is scheduled"
    )

    # Delivery metadata and context
    delivery_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional delivery metadata and provider-specific data"
    )

    # Relationships
    message = relationship(
        "BookingMessage",
        back_populates="delivery_logs",
        doc="Associated booking message"
    )

    # Performance indexes
    __table_args__ = (
        Index("idx_delivery_logs_message", "message_id"),
        Index("idx_delivery_logs_status", "delivery_status", "attempted_at"),
        Index("idx_delivery_logs_method", "delivery_method", "delivery_status"),
        Index("idx_delivery_logs_retry", "next_retry_at", "retry_count"),
        Index("idx_delivery_logs_provider", "delivery_provider", "delivery_status"),
        Index("idx_delivery_logs_error", "error_code", "failed_at"),
    )

    def __repr__(self) -> str:
        """String representation of the delivery log."""
        return f"<MessageDeliveryLog(id={self.id}, method='{self.delivery_method}', status='{self.delivery_status}')>"

    @property
    def is_successful(self) -> bool:
        """Check if delivery was successful."""
        return self.delivery_status == DeliveryStatus.DELIVERED

    @property
    def is_failed(self) -> bool:
        """Check if delivery failed."""
        return self.delivery_status == DeliveryStatus.FAILED

    @property
    def can_retry(self) -> bool:
        """Check if delivery can be retried."""
        return (
            self.delivery_status in {DeliveryStatus.FAILED, DeliveryStatus.RETRY} and
            self.retry_count < self.max_retries
        )

    @property
    def delivery_duration(self) -> Optional[float]:
        """Get delivery duration in seconds."""
        if self.delivered_at and self.attempted_at:
            return (self.delivered_at - self.attempted_at).total_seconds()
        return None

    @property
    def time_since_attempt(self) -> float:
        """Get time since last attempt in seconds."""
        return (datetime.now(timezone.utc) - self.attempted_at.replace(tzinfo=None)).total_seconds()

    def increment_retry(self, next_retry_delay_minutes: int = 5):
        """
        Increment retry count and schedule next retry.

        Args:
            next_retry_delay_minutes: Delay in minutes before next retry
        """
        from datetime import timedelta

        self.retry_count += 1
        if self.retry_count < self.max_retries:
            # Exponential backoff: 5, 15, 45 minutes
            delay_minutes = next_retry_delay_minutes * (3 ** (self.retry_count - 1))
            self.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
            self.delivery_status = DeliveryStatus.RETRY
        else:
            self.delivery_status = DeliveryStatus.FAILED
            self.next_retry_at = None
