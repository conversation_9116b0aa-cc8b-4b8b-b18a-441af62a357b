"""
Analytics background tasks for Culture Connect Backend API.

This module provides comprehensive analytics task processing including:
- Metrics calculation and aggregation
- Dashboard data updates
- Performance analytics
- Trend analysis
- Real-time analytics processing

Implements Task 6.2.1 requirements for analytics task processing with
production-grade data processing and insights generation.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="analytics.calculate_metrics", queue="analytics")
def calculate_metrics(self, calculation_request: Dict[str, Any]):
    """
    Calculate system metrics and KPIs.
    
    Args:
        calculation_request: Metrics calculation parameters
    """
    try:
        async def _calculate_metrics():
            from app.services.performance_analytics_service import PerformanceAnalyticsService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                analytics_service = PerformanceAnalyticsService(session)
                
                time_period = calculation_request.get("time_period", "24h")
                metrics_types = calculation_request.get("metrics", ["all"])
                
                # Calculate time range
                if time_period == "1h":
                    start_time = datetime.now(timezone.utc) - timedelta(hours=1)
                elif time_period == "24h":
                    start_time = datetime.now(timezone.utc) - timedelta(days=1)
                elif time_period == "7d":
                    start_time = datetime.now(timezone.utc) - timedelta(days=7)
                elif time_period == "30d":
                    start_time = datetime.now(timezone.utc) - timedelta(days=30)
                else:
                    start_time = datetime.now(timezone.utc) - timedelta(days=1)
                
                end_time = datetime.now(timezone.utc)
                
                # Calculate metrics
                metrics_result = await analytics_service.calculate_system_metrics(
                    start_time=start_time,
                    end_time=end_time,
                    metrics_types=metrics_types,
                    include_trends=True
                )
                
                logger.info(f"Metrics calculated for {time_period}", extra={
                    "time_period": time_period,
                    "metrics_types": metrics_types,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "time_period": time_period,
                    "metrics_calculated": len(metrics_result),
                    "calculation_time_ms": metrics_result.get("calculation_time_ms", 0)
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_calculate_metrics())
            return {
                "status": "success",
                "calculated_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to calculate metrics: {str(e)}", extra={
            "calculation_request": calculation_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=180, max_retries=3)


@celery_app.task(bind=True, name="analytics.update_dashboards", queue="analytics")
def update_dashboards(self, update_request: Dict[str, Any]):
    """
    Update dashboard data and cache.
    
    Args:
        update_request: Dashboard update parameters
    """
    try:
        async def _update_dashboards():
            from app.services.vendor_dashboard_services import VendorDashboardService
            from app.services.performance_analytics_service import PerformanceAnalyticsService
            from app.core.cache import cache_manager
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                dashboard_service = VendorDashboardService(session)
                analytics_service = PerformanceAnalyticsService(session)
                
                dashboard_types = update_request.get("dashboard_types", ["vendor", "admin"])
                force_refresh = update_request.get("force_refresh", False)
                
                updated_dashboards = []
                
                # Update vendor dashboards
                if "vendor" in dashboard_types:
                    vendor_result = await dashboard_service.refresh_all_vendor_dashboards(
                        force_refresh=force_refresh
                    )
                    updated_dashboards.append({
                        "type": "vendor",
                        "count": vendor_result.get("updated_count", 0)
                    })
                
                # Update admin dashboards
                if "admin" in dashboard_types:
                    admin_result = await analytics_service.refresh_admin_dashboards(
                        force_refresh=force_refresh
                    )
                    updated_dashboards.append({
                        "type": "admin",
                        "count": admin_result.get("updated_count", 0)
                    })
                
                logger.info(f"Dashboards updated", extra={
                    "dashboard_types": dashboard_types,
                    "updated_dashboards": updated_dashboards,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "dashboard_types": dashboard_types,
                    "updated_dashboards": updated_dashboards,
                    "force_refresh": force_refresh
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_update_dashboards())
            return {
                "status": "success",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to update dashboards: {str(e)}", extra={
            "update_request": update_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="analytics.process_events", queue="analytics")
def process_events(self, event_batch: Dict[str, Any]):
    """
    Process analytics events in batch.
    
    Args:
        event_batch: Batch of analytics events to process
    """
    try:
        async def _process_events():
            from app.services.performance_analytics_service import PerformanceAnalyticsService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                analytics_service = PerformanceAnalyticsService(session)
                
                events = event_batch.get("events", [])
                batch_id = event_batch.get("batch_id", "unknown")
                
                processed_events = 0
                failed_events = 0
                
                for event in events:
                    try:
                        await analytics_service.process_analytics_event(
                            event_type=event["type"],
                            event_data=event["data"],
                            timestamp=datetime.fromisoformat(event["timestamp"]),
                            user_id=event.get("user_id"),
                            session_id=event.get("session_id")
                        )
                        processed_events += 1
                        
                    except Exception as e:
                        logger.warning(f"Failed to process event: {str(e)}", extra={
                            "event": event,
                            "batch_id": batch_id
                        })
                        failed_events += 1
                
                logger.info(f"Event batch processed", extra={
                    "batch_id": batch_id,
                    "total_events": len(events),
                    "processed": processed_events,
                    "failed": failed_events,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "batch_id": batch_id,
                    "total_events": len(events),
                    "processed_events": processed_events,
                    "failed_events": failed_events
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_events())
            return {
                "status": "success",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process event batch: {str(e)}", extra={
            "event_batch": event_batch,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=120, max_retries=3)
