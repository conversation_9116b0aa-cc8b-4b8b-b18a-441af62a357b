"""
Integration tests for availability API endpoints.

This module provides comprehensive integration tests for availability API endpoints:
- Vendor availability configuration endpoints with authentication
- Real-time availability checking with performance validation
- Recurring pattern management with business logic validation
- Availability slot management with capacity tracking
- Exception handling with pattern override validation
- Complete workflow testing with booking system integration

Implements Task 4.1.2 Phase 6 requirements with >85% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, time, timedelta, timezone
from fastapi.testclient import TestClient
from fastapi import status
import json

from app.main import app
from app.core.deps import get_db, get_current_user
from app.models.user import User


class TestAvailabilityEndpointsIntegration:
    """Integration test cases for availability API endpoints."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session
    
    @pytest.fixture
    def mock_vendor_user(self):
        """Mock authenticated vendor user."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "vendor"
        user.is_active = True
        user.is_verified = True
        user.vendor_id = 123
        return user
    
    @pytest.fixture
    def mock_customer_user(self):
        """Mock authenticated customer user."""
        user = MagicMock(spec=User)
        user.id = 2
        user.email = "<EMAIL>"
        user.role = "customer"
        user.is_active = True
        user.is_verified = True
        return user
    
    @pytest.fixture
    def test_client_vendor(self, mock_db_session, mock_vendor_user):
        """Test client with vendor authentication."""
        app.dependency_overrides[get_db] = lambda: mock_db_session
        app.dependency_overrides[get_current_user] = lambda: mock_vendor_user
        
        with patch('app.api.v1.endpoints.availability.VendorRepository') as mock_vendor_repo:
            mock_vendor_instance = MagicMock()
            mock_vendor_instance.id = 123
            mock_vendor_repo.return_value.get_by_user_id.return_value = mock_vendor_instance
            
            client = TestClient(app)
            yield client
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def test_client_customer(self, mock_db_session, mock_customer_user):
        """Test client with customer authentication."""
        app.dependency_overrides[get_db] = lambda: mock_db_session
        app.dependency_overrides[get_current_user] = lambda: mock_customer_user
        
        client = TestClient(app)
        yield client
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def vendor_availability_data(self):
        """Sample vendor availability data."""
        return {
            "timezone": "America/New_York",
            "advance_booking_days": 90,
            "min_booking_notice_hours": 24,
            "max_booking_notice_days": 30,
            "default_slot_duration_minutes": 60,
            "buffer_time_minutes": 15,
            "earliest_booking_time": "09:00:00",
            "latest_booking_time": "17:00:00"
        }
    
    @pytest.fixture
    def recurring_pattern_data(self):
        """Sample recurring pattern data."""
        return {
            "pattern_type": "weekly",
            "day_of_week": 1,
            "start_time": "10:00:00",
            "end_time": "16:00:00",
            "slot_duration_minutes": 60,
            "max_bookings_per_slot": 2,
            "valid_from": (date.today() + timedelta(days=1)).isoformat(),
            "auto_generate": True
        }
    
    def test_create_vendor_availability_success(
        self, test_client_vendor, vendor_availability_data
    ):
        """Test successful vendor availability creation."""
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_response = MagicMock()
            mock_response.id = 1
            mock_response.vendor_id = 123
            mock_service_instance.create_vendor_availability.return_value = mock_response
            mock_service.return_value = mock_service_instance
            
            response = test_client_vendor.post(
                "/api/v1/availability/vendor-availability",
                json=vendor_availability_data
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            mock_service_instance.create_vendor_availability.assert_called_once()
    
    def test_create_vendor_availability_unauthorized(
        self, test_client_customer, vendor_availability_data
    ):
        """Test vendor availability creation with customer role (unauthorized)."""
        response = test_client_customer.post(
            "/api/v1/availability/vendor-availability",
            json=vendor_availability_data
        )
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "Vendor role required" in response.json()["detail"]
    
    def test_get_vendor_availability_success(self, test_client_vendor):
        """Test successful vendor availability retrieval."""
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_response = MagicMock()
            mock_response.id = 1
            mock_response.vendor_id = 123
            mock_service_instance.get_vendor_availability.return_value = mock_response
            mock_service.return_value = mock_service_instance
            
            response = test_client_vendor.get("/api/v1/availability/vendor-availability")
            
            assert response.status_code == status.HTTP_200_OK
            mock_service_instance.get_vendor_availability.assert_called_once()
    
    def test_get_vendor_availability_not_found(self, test_client_vendor):
        """Test vendor availability not found."""
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service_instance.get_vendor_availability.return_value = None
            mock_service.return_value = mock_service_instance
            
            response = test_client_vendor.get("/api/v1/availability/vendor-availability")
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
            assert "not found" in response.json()["detail"]
    
    def test_check_availability_success(self, test_client_customer):
        """Test successful availability checking."""
        check_data = {
            "vendor_id": 123,
            "start_datetime": (datetime.now(timezone.utc) + timedelta(days=1, hours=10)).isoformat(),
            "end_datetime": (datetime.now(timezone.utc) + timedelta(days=1, hours=11)).isoformat(),
            "required_capacity": 1
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_response = MagicMock()
            mock_response.is_available = True
            mock_response.reason = "Available"
            mock_response.available_slots = []
            mock_response.conflicting_bookings = []
            mock_response.alternative_suggestions = []
            mock_service_instance.check_availability.return_value = mock_response
            mock_service.return_value = mock_service_instance
            
            response = test_client_customer.post(
                "/api/v1/availability/check-availability",
                json=check_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["is_available"] is True
            assert response_data["reason"] == "Available"
    
    def test_check_availability_validation_error(self, test_client_customer):
        """Test availability checking with validation error."""
        check_data = {
            "vendor_id": 123,
            "start_datetime": (datetime.now(timezone.utc) + timedelta(days=1, hours=11)).isoformat(),
            "end_datetime": (datetime.now(timezone.utc) + timedelta(days=1, hours=10)).isoformat(),  # Invalid
            "required_capacity": 1
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service_instance.check_availability.side_effect = ValueError("start_datetime must be before end_datetime")
            mock_service.return_value = mock_service_instance
            
            response = test_client_customer.post(
                "/api/v1/availability/check-availability",
                json=check_data
            )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_generate_slots_success(self, test_client_vendor):
        """Test successful slot generation."""
        availability_id = 1
        generation_data = {
            "start_date": (date.today() + timedelta(days=1)).isoformat(),
            "end_date": (date.today() + timedelta(days=7)).isoformat(),
            "auto_commit": True
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            
            # Mock get_vendor_availability to return valid config
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            
            # Mock slot generation response
            mock_response = MagicMock()
            mock_response.created_count = 50
            mock_response.skipped_count = 0
            mock_response.error_count = 0
            mock_response.created_slot_ids = list(range(1, 51))
            mock_response.performance_metrics = {"patterns_processed": 2}
            mock_service_instance.generate_slots_from_patterns.return_value = mock_response
            
            mock_service.return_value = mock_service_instance
            
            response = test_client_vendor.post(
                f"/api/v1/availability/vendor-availability/{availability_id}/generate-slots",
                json=generation_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["created_count"] == 50
            assert response_data["error_count"] == 0
    
    def test_generate_slots_unauthorized_vendor(self, test_client_vendor):
        """Test slot generation for unauthorized vendor."""
        availability_id = 999  # Different vendor's availability
        generation_data = {
            "start_date": (date.today() + timedelta(days=1)).isoformat(),
            "end_date": (date.today() + timedelta(days=7)).isoformat(),
            "auto_commit": True
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service_instance.get_vendor_availability.return_value = None
            mock_service.return_value = mock_service_instance
            
            response = test_client_vendor.post(
                f"/api/v1/availability/vendor-availability/{availability_id}/generate-slots",
                json=generation_data
            )
            
            assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_create_recurring_pattern_success(
        self, test_client_vendor, recurring_pattern_data
    ):
        """Test successful recurring pattern creation."""
        availability_id = 1
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service, \
             patch('app.repositories.availability_repository.RecurringAvailabilityRepository') as mock_repo:
            
            # Mock availability service
            mock_service_instance = AsyncMock()
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            mock_service.return_value = mock_service_instance
            
            # Mock repository
            mock_repo_instance = AsyncMock()
            mock_pattern = MagicMock()
            mock_pattern.id = 1
            mock_pattern.pattern_type = "weekly"
            mock_repo_instance.create.return_value = mock_pattern
            mock_repo.return_value = mock_repo_instance
            
            response = test_client_vendor.post(
                f"/api/v1/availability/vendor-availability/{availability_id}/recurring-patterns",
                json=recurring_pattern_data
            )
            
            assert response.status_code == status.HTTP_201_CREATED
    
    def test_list_recurring_patterns_success(self, test_client_vendor):
        """Test successful recurring pattern listing."""
        availability_id = 1
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service, \
             patch('app.repositories.availability_repository.RecurringAvailabilityRepository') as mock_repo:
            
            # Mock availability service
            mock_service_instance = AsyncMock()
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            mock_service.return_value = mock_service_instance
            
            # Mock repository
            mock_repo_instance = AsyncMock()
            mock_patterns = [MagicMock(), MagicMock()]
            mock_repo_instance.get_active_patterns_for_vendor.return_value = mock_patterns
            mock_repo.return_value = mock_repo_instance
            
            response = test_client_vendor.get(
                f"/api/v1/availability/vendor-availability/{availability_id}/recurring-patterns"
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert len(response_data) == 2
    
    def test_list_availability_slots_success(self, test_client_vendor):
        """Test successful availability slot listing."""
        availability_id = 1
        start_date = (date.today() + timedelta(days=1)).isoformat()
        end_date = (date.today() + timedelta(days=7)).isoformat()
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service, \
             patch('app.repositories.availability_repository.AvailabilitySlotRepository') as mock_repo:
            
            # Mock availability service
            mock_service_instance = AsyncMock()
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            mock_service.return_value = mock_service_instance
            
            # Mock repository
            mock_repo_instance = AsyncMock()
            mock_slots = [MagicMock() for _ in range(10)]
            mock_repo_instance.get_slots_by_date_range.return_value = mock_slots
            mock_repo.return_value = mock_repo_instance
            
            response = test_client_vendor.get(
                f"/api/v1/availability/vendor-availability/{availability_id}/slots",
                params={
                    "start_date": start_date,
                    "end_date": end_date,
                    "page": 1,
                    "per_page": 50
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["total_count"] == 10
            assert response_data["page"] == 1
            assert response_data["per_page"] == 50
    
    def test_create_availability_slot_success(self, test_client_vendor):
        """Test successful individual slot creation."""
        availability_id = 1
        slot_data = {
            "availability_date": (date.today() + timedelta(days=1)).isoformat(),
            "start_time": "10:00:00",
            "end_time": "11:00:00",
            "max_bookings": 2
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service, \
             patch('app.repositories.availability_repository.AvailabilitySlotRepository') as mock_repo:
            
            # Mock availability service
            mock_service_instance = AsyncMock()
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            mock_service.return_value = mock_service_instance
            
            # Mock repository
            mock_repo_instance = AsyncMock()
            mock_slot = MagicMock()
            mock_slot.id = 1
            mock_repo_instance.create.return_value = mock_slot
            mock_repo.return_value = mock_repo_instance
            
            response = test_client_vendor.post(
                f"/api/v1/availability/vendor-availability/{availability_id}/slots",
                json=slot_data
            )
            
            assert response.status_code == status.HTTP_201_CREATED
    
    def test_create_availability_exception_success(self, test_client_vendor):
        """Test successful availability exception creation."""
        availability_id = 1
        exception_data = {
            "exception_date": (date.today() + timedelta(days=7)).isoformat(),
            "exception_type": "unavailable",
            "reason": "Vendor holiday"
        }
        
        with patch('app.services.availability_service.AvailabilityService') as mock_service, \
             patch('app.repositories.availability_repository.AvailabilityExceptionRepository') as mock_repo:
            
            # Mock availability service
            mock_service_instance = AsyncMock()
            mock_availability = MagicMock()
            mock_availability.id = availability_id
            mock_service_instance.get_vendor_availability.return_value = mock_availability
            mock_service.return_value = mock_service_instance
            
            # Mock repository
            mock_repo_instance = AsyncMock()
            mock_exception = MagicMock()
            mock_exception.id = 1
            mock_repo_instance.create.return_value = mock_exception
            mock_repo.return_value = mock_repo_instance
            
            response = test_client_vendor.post(
                f"/api/v1/availability/vendor-availability/{availability_id}/exceptions",
                json=exception_data
            )
            
            assert response.status_code == status.HTTP_201_CREATED
