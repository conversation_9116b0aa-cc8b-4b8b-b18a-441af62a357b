"""
Real-time Synchronization schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic schemas for real-time data synchronization including:
- Data synchronization request and response schemas
- Conflict resolution and change tracking schemas
- Event prioritization and batching schemas
- Performance monitoring and metrics schemas
- Integration with existing WebSocket infrastructure

Implements Task 6.1.2 Phase 1 requirements for real-time synchronization schemas with:
- Type-safe synchronization definitions with validation
- Conflict resolution strategy schemas
- Event prioritization with performance targets
- Integration with existing RBAC and WebSocket systems
- Production-grade error handling and validation
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, ConfigDict, field_validator, model_validator
from enum import Enum

from app.models.sync_models import (
    SyncStatus, ConflictResolutionStrategy, SyncPriority,
    ChangeType, SyncScope
)


# Enum schemas for API
class SyncStatusEnum(str, Enum):
    """Synchronization status enumeration for API."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"
    ROLLED_BACK = "rolled_back"


class ConflictResolutionStrategyEnum(str, Enum):
    """Conflict resolution strategy enumeration for API."""
    LAST_WRITE_WINS = "last_write_wins"
    SERVER_AUTHORITATIVE = "server_authoritative"
    CLIENT_MERGE = "client_merge"
    MANUAL_RESOLUTION = "manual_resolution"
    OPTIMISTIC_LOCK = "optimistic_lock"


class SyncPriorityEnum(str, Enum):
    """Synchronization priority enumeration for API."""
    CRITICAL = "critical"      # Immediate delivery
    STANDARD = "standard"      # 30-second batches
    HISTORICAL = "historical"  # 5-minute sync
    BULK = "bulk"             # Off-peak processing


class ChangeTypeEnum(str, Enum):
    """Change type enumeration for API."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    BATCH_UPDATE = "batch_update"
    RESTORE = "restore"


class SyncScopeEnum(str, Enum):
    """Synchronization scope enumeration for API."""
    USER = "user"              # User-specific data
    CONNECTION = "connection"   # Connection-specific data
    GLOBAL = "global"          # Global data
    ROOM = "room"              # Room/channel-specific data


# Base schemas
class SyncBaseSchema(BaseModel):
    """Base schema for synchronization operations."""
    model_config = ConfigDict(from_attributes=True)


# Data Sync Record schemas
class DataSyncRecordCreate(SyncBaseSchema):
    """Schema for creating data synchronization records."""

    source_connection_id: Optional[str] = Field(
        None,
        description="Source WebSocket connection ID",
        max_length=255
    )
    source_user_id: int = Field(
        ...,
        description="Source user ID",
        gt=0
    )
    target_scope: SyncScopeEnum = Field(
        ...,
        description="Synchronization scope"
    )
    target_identifier: Optional[str] = Field(
        None,
        description="Target identifier (user_id, connection_id, room_id, etc.)",
        max_length=255
    )
    entity_type: str = Field(
        ...,
        description="Type of entity being synchronized",
        max_length=100
    )
    entity_id: str = Field(
        ...,
        description="Entity identifier",
        max_length=255
    )
    change_type: ChangeTypeEnum = Field(
        ...,
        description="Type of change"
    )
    priority: SyncPriorityEnum = Field(
        SyncPriorityEnum.STANDARD,
        description="Synchronization priority"
    )
    data_before: Optional[Dict[str, Any]] = Field(
        None,
        description="Data state before change"
    )
    data_after: Dict[str, Any] = Field(
        ...,
        description="Data state after change"
    )
    data_delta: Optional[Dict[str, Any]] = Field(
        None,
        description="Change delta for efficient sync"
    )
    version_before: Optional[int] = Field(
        None,
        description="Version before change",
        ge=0
    )
    version_after: int = Field(
        ...,
        description="Version after change",
        gt=0
    )
    conflict_resolution_strategy: ConflictResolutionStrategyEnum = Field(
        ConflictResolutionStrategyEnum.LAST_WRITE_WINS,
        description="Conflict resolution strategy"
    )
    max_retries: int = Field(
        3,
        description="Maximum retry attempts",
        ge=0,
        le=10
    )
    sync_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional synchronization metadata"
    )

    @field_validator('version_after')
    @classmethod
    def validate_version_after(cls, v, info):
        """Validate version_after is greater than version_before."""
        if info.data and 'version_before' in info.data:
            version_before = info.data['version_before']
            if version_before is not None and v <= version_before:
                raise ValueError('version_after must be greater than version_before')
        return v

    @field_validator('data_after')
    @classmethod
    def validate_data_after_not_empty(cls, v):
        """Validate data_after is not empty."""
        if not v:
            raise ValueError('data_after cannot be empty')
        return v

    @model_validator(mode='after')
    @classmethod
    def validate_create_operation(cls, values):
        """Validate create operation constraints."""
        if values.change_type == ChangeTypeEnum.CREATE:
            if values.version_before is not None:
                raise ValueError('version_before must be None for create operations')
            if values.data_before is not None:
                raise ValueError('data_before must be None for create operations')
        return values


class DataSyncRecordUpdate(SyncBaseSchema):
    """Schema for updating data synchronization records."""

    status: Optional[SyncStatusEnum] = Field(
        None,
        description="Synchronization status"
    )
    started_at: Optional[datetime] = Field(
        None,
        description="Sync start timestamp"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="Sync completion timestamp"
    )
    processing_time_ms: Optional[float] = Field(
        None,
        description="Processing time in milliseconds",
        ge=0
    )
    conflict_resolution_time_ms: Optional[float] = Field(
        None,
        description="Conflict resolution time in milliseconds",
        ge=0
    )
    retry_count: Optional[int] = Field(
        None,
        description="Number of retry attempts",
        ge=0
    )
    next_retry_at: Optional[datetime] = Field(
        None,
        description="Next retry attempt timestamp"
    )
    error_message: Optional[str] = Field(
        None,
        description="Error message if sync failed"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Detailed error information"
    )
    has_conflicts: Optional[bool] = Field(
        None,
        description="Whether conflicts were detected"
    )
    conflict_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Conflict details and resolution information"
    )


class DataSyncRecordResponse(SyncBaseSchema):
    """Schema for data synchronization record responses."""

    id: int = Field(..., description="Sync record ID")
    sync_id: str = Field(..., description="Unique synchronization identifier")
    source_connection_id: Optional[str] = Field(None, description="Source WebSocket connection ID")
    source_user_id: int = Field(..., description="Source user ID")
    target_scope: SyncScopeEnum = Field(..., description="Synchronization scope")
    target_identifier: Optional[str] = Field(None, description="Target identifier")
    entity_type: str = Field(..., description="Type of entity being synchronized")
    entity_id: str = Field(..., description="Entity identifier")
    change_type: ChangeTypeEnum = Field(..., description="Type of change")
    priority: SyncPriorityEnum = Field(..., description="Synchronization priority")
    status: SyncStatusEnum = Field(..., description="Synchronization status")
    data_before: Optional[Dict[str, Any]] = Field(None, description="Data state before change")
    data_after: Dict[str, Any] = Field(..., description="Data state after change")
    data_delta: Optional[Dict[str, Any]] = Field(None, description="Change delta")
    version_before: Optional[int] = Field(None, description="Version before change")
    version_after: int = Field(..., description="Version after change")
    conflict_resolution_strategy: ConflictResolutionStrategyEnum = Field(
        ..., description="Conflict resolution strategy"
    )
    has_conflicts: bool = Field(..., description="Whether conflicts were detected")
    conflict_details: Optional[Dict[str, Any]] = Field(None, description="Conflict details")
    initiated_at: datetime = Field(..., description="Sync initiation timestamp")
    started_at: Optional[datetime] = Field(None, description="Sync start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Sync completion timestamp")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    conflict_resolution_time_ms: Optional[float] = Field(
        None, description="Conflict resolution time in milliseconds"
    )
    retry_count: int = Field(..., description="Number of retry attempts")
    max_retries: int = Field(..., description="Maximum retry attempts")
    next_retry_at: Optional[datetime] = Field(None, description="Next retry attempt timestamp")
    error_message: Optional[str] = Field(None, description="Error message if sync failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")
    sync_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional sync metadata")
    created_at: datetime = Field(..., description="Record creation timestamp")
    updated_at: datetime = Field(..., description="Record update timestamp")


# Sync Conflict schemas
class SyncConflictCreate(SyncBaseSchema):
    """Schema for creating synchronization conflicts."""

    sync_record_id: int = Field(
        ...,
        description="Associated sync record ID",
        gt=0
    )
    conflict_type: str = Field(
        ...,
        description="Type of conflict (version, data, permission, etc.)",
        max_length=100
    )
    conflicting_version: Optional[int] = Field(
        None,
        description="Conflicting version number",
        ge=0
    )
    conflicting_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Conflicting data state"
    )
    conflicting_source: Optional[str] = Field(
        None,
        description="Source of conflicting change",
        max_length=255
    )
    resolution_strategy: ConflictResolutionStrategyEnum = Field(
        ...,
        description="Strategy used to resolve conflict"
    )
    resolution_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Resolution data and merged result"
    )
    conflict_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional conflict metadata"
    )


class SyncConflictUpdate(SyncBaseSchema):
    """Schema for updating synchronization conflicts."""

    resolution_strategy: Optional[ConflictResolutionStrategyEnum] = Field(
        None,
        description="Strategy used to resolve conflict"
    )
    resolution_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Resolution data and merged result"
    )
    resolved_by_user_id: Optional[int] = Field(
        None,
        description="User who resolved the conflict",
        gt=0
    )
    resolved_at: Optional[datetime] = Field(
        None,
        description="Conflict resolution timestamp"
    )
    resolution_time_ms: Optional[float] = Field(
        None,
        description="Time taken to resolve conflict in milliseconds",
        ge=0
    )
    conflict_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional conflict metadata"
    )


class SyncConflictResponse(SyncBaseSchema):
    """Schema for synchronization conflict responses."""

    id: int = Field(..., description="Conflict ID")
    conflict_id: str = Field(..., description="Unique conflict identifier")
    sync_record_id: int = Field(..., description="Associated sync record ID")
    conflict_type: str = Field(..., description="Type of conflict")
    conflicting_version: Optional[int] = Field(None, description="Conflicting version number")
    conflicting_data: Optional[Dict[str, Any]] = Field(None, description="Conflicting data state")
    conflicting_source: Optional[str] = Field(None, description="Source of conflicting change")
    resolution_strategy: ConflictResolutionStrategyEnum = Field(
        ..., description="Strategy used to resolve conflict"
    )
    resolution_data: Optional[Dict[str, Any]] = Field(None, description="Resolution data")
    resolved_by_user_id: Optional[int] = Field(None, description="User who resolved the conflict")
    resolved_at: Optional[datetime] = Field(None, description="Conflict resolution timestamp")
    resolution_time_ms: Optional[float] = Field(
        None, description="Time taken to resolve conflict in milliseconds"
    )
    conflict_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    created_at: datetime = Field(..., description="Conflict creation timestamp")
    updated_at: datetime = Field(..., description="Conflict update timestamp")


# Sync Batch schemas
class SyncBatchCreate(SyncBaseSchema):
    """Schema for creating synchronization batches."""

    priority: SyncPriorityEnum = Field(
        ...,
        description="Batch priority level"
    )
    target_scope: SyncScopeEnum = Field(
        ...,
        description="Batch target scope"
    )
    scheduled_at: datetime = Field(
        ...,
        description="Scheduled processing time"
    )
    batch_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Batch processing configuration"
    )

    @field_validator('scheduled_at')
    @classmethod
    def validate_scheduled_at_future(cls, v):
        """Validate scheduled_at is in the future."""
        if v <= datetime.now(timezone.utc):
            raise ValueError('scheduled_at must be in the future')
        return v


class SyncBatchUpdate(SyncBaseSchema):
    """Schema for updating synchronization batches."""

    status: Optional[SyncStatusEnum] = Field(
        None,
        description="Batch processing status"
    )
    started_at: Optional[datetime] = Field(
        None,
        description="Batch processing start time"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="Batch processing completion time"
    )
    total_records: Optional[int] = Field(
        None,
        description="Total records in batch",
        ge=0
    )
    processed_records: Optional[int] = Field(
        None,
        description="Successfully processed records",
        ge=0
    )
    failed_records: Optional[int] = Field(
        None,
        description="Failed records",
        ge=0
    )
    processing_time_ms: Optional[float] = Field(
        None,
        description="Total processing time in milliseconds",
        ge=0
    )
    batch_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Batch processing configuration"
    )


class SyncBatchResponse(SyncBaseSchema):
    """Schema for synchronization batch responses."""

    id: int = Field(..., description="Batch ID")
    batch_id: str = Field(..., description="Unique batch identifier")
    priority: SyncPriorityEnum = Field(..., description="Batch priority level")
    target_scope: SyncScopeEnum = Field(..., description="Batch target scope")
    status: SyncStatusEnum = Field(..., description="Batch processing status")
    scheduled_at: datetime = Field(..., description="Scheduled processing time")
    started_at: Optional[datetime] = Field(None, description="Batch processing start time")
    completed_at: Optional[datetime] = Field(None, description="Batch processing completion time")
    total_records: int = Field(..., description="Total records in batch")
    processed_records: int = Field(..., description="Successfully processed records")
    failed_records: int = Field(..., description="Failed records")
    processing_time_ms: Optional[float] = Field(
        None, description="Total processing time in milliseconds"
    )
    batch_config: Optional[Dict[str, Any]] = Field(None, description="Batch processing configuration")
    created_at: datetime = Field(..., description="Batch creation timestamp")
    updated_at: datetime = Field(..., description="Batch update timestamp")


# Sync Metrics schemas
class SyncMetricsCreate(SyncBaseSchema):
    """Schema for creating synchronization metrics."""

    metric_date: datetime = Field(
        ...,
        description="Metrics date"
    )
    metric_hour: int = Field(
        ...,
        description="Metrics hour (0-23)",
        ge=0,
        le=23
    )
    scope: SyncScopeEnum = Field(
        ...,
        description="Metrics scope"
    )
    priority: SyncPriorityEnum = Field(
        ...,
        description="Metrics priority"
    )
    total_syncs: int = Field(
        0,
        description="Total sync operations",
        ge=0
    )
    successful_syncs: int = Field(
        0,
        description="Successful sync operations",
        ge=0
    )
    failed_syncs: int = Field(
        0,
        description="Failed sync operations",
        ge=0
    )
    conflicts_detected: int = Field(
        0,
        description="Conflicts detected",
        ge=0
    )
    conflicts_resolved: int = Field(
        0,
        description="Conflicts resolved",
        ge=0
    )
    avg_processing_time_ms: Optional[float] = Field(
        None,
        description="Average processing time in milliseconds",
        ge=0
    )
    max_processing_time_ms: Optional[float] = Field(
        None,
        description="Maximum processing time in milliseconds",
        ge=0
    )
    avg_conflict_resolution_time_ms: Optional[float] = Field(
        None,
        description="Average conflict resolution time in milliseconds",
        ge=0
    )
    success_rate: Optional[float] = Field(
        None,
        description="Success rate percentage",
        ge=0,
        le=100
    )
    total_data_size_bytes: int = Field(
        0,
        description="Total data synchronized in bytes",
        ge=0
    )

    @model_validator(mode='after')
    @classmethod
    def validate_sync_consistency(cls, values):
        """Validate sync metrics consistency."""
        if values.successful_syncs + values.failed_syncs > values.total_syncs:
            raise ValueError('successful_syncs + failed_syncs cannot exceed total_syncs')
        return values


class SyncMetricsResponse(SyncBaseSchema):
    """Schema for synchronization metrics responses."""

    id: int = Field(..., description="Metrics ID")
    metric_date: datetime = Field(..., description="Metrics date")
    metric_hour: int = Field(..., description="Metrics hour (0-23)")
    scope: SyncScopeEnum = Field(..., description="Metrics scope")
    priority: SyncPriorityEnum = Field(..., description="Metrics priority")
    total_syncs: int = Field(..., description="Total sync operations")
    successful_syncs: int = Field(..., description="Successful sync operations")
    failed_syncs: int = Field(..., description="Failed sync operations")
    conflicts_detected: int = Field(..., description="Conflicts detected")
    conflicts_resolved: int = Field(..., description="Conflicts resolved")
    avg_processing_time_ms: Optional[float] = Field(
        None, description="Average processing time in milliseconds"
    )
    max_processing_time_ms: Optional[float] = Field(
        None, description="Maximum processing time in milliseconds"
    )
    avg_conflict_resolution_time_ms: Optional[float] = Field(
        None, description="Average conflict resolution time in milliseconds"
    )
    success_rate: Optional[float] = Field(None, description="Success rate percentage")
    total_data_size_bytes: int = Field(..., description="Total data synchronized in bytes")
    created_at: datetime = Field(..., description="Metrics creation timestamp")
    updated_at: datetime = Field(..., description="Metrics update timestamp")


# Synchronization operation schemas
class SyncOperationRequest(SyncBaseSchema):
    """Schema for synchronization operation requests."""

    entity_type: str = Field(
        ...,
        description="Type of entity to synchronize",
        max_length=100
    )
    entity_id: str = Field(
        ...,
        description="Entity identifier",
        max_length=255
    )
    operation: ChangeTypeEnum = Field(
        ...,
        description="Synchronization operation"
    )
    data: Dict[str, Any] = Field(
        ...,
        description="Entity data"
    )
    version: Optional[int] = Field(
        None,
        description="Entity version for optimistic locking",
        ge=0
    )
    priority: SyncPriorityEnum = Field(
        SyncPriorityEnum.STANDARD,
        description="Operation priority"
    )
    target_scope: SyncScopeEnum = Field(
        SyncScopeEnum.USER,
        description="Synchronization scope"
    )
    target_identifier: Optional[str] = Field(
        None,
        description="Target identifier",
        max_length=255
    )
    conflict_resolution: ConflictResolutionStrategyEnum = Field(
        ConflictResolutionStrategyEnum.LAST_WRITE_WINS,
        description="Conflict resolution strategy"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional operation metadata"
    )


class SyncOperationResponse(SyncBaseSchema):
    """Schema for synchronization operation responses."""

    sync_id: str = Field(..., description="Synchronization identifier")
    status: SyncStatusEnum = Field(..., description="Operation status")
    entity_type: str = Field(..., description="Type of entity synchronized")
    entity_id: str = Field(..., description="Entity identifier")
    operation: ChangeTypeEnum = Field(..., description="Synchronization operation")
    version: int = Field(..., description="New entity version")
    has_conflicts: bool = Field(..., description="Whether conflicts were detected")
    conflict_details: Optional[Dict[str, Any]] = Field(None, description="Conflict details")
    processing_time_ms: Optional[float] = Field(
        None, description="Processing time in milliseconds"
    )
    initiated_at: datetime = Field(..., description="Operation initiation timestamp")
    completed_at: Optional[datetime] = Field(None, description="Operation completion timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Operation metadata")


# Batch operation schemas
class BatchSyncRequest(SyncBaseSchema):
    """Schema for batch synchronization requests."""

    operations: List[SyncOperationRequest] = Field(
        ...,
        description="List of synchronization operations",
        min_items=1,
        max_items=1000
    )
    batch_priority: SyncPriorityEnum = Field(
        SyncPriorityEnum.STANDARD,
        description="Batch priority"
    )
    batch_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Batch processing configuration"
    )

    @field_validator('operations')
    @classmethod
    def validate_operations_not_empty(cls, v):
        """Validate operations list is not empty."""
        if not v:
            raise ValueError('operations cannot be empty')
        return v


class BatchSyncResponse(SyncBaseSchema):
    """Schema for batch synchronization responses."""

    batch_id: str = Field(..., description="Batch identifier")
    status: SyncStatusEnum = Field(..., description="Batch status")
    total_operations: int = Field(..., description="Total operations in batch")
    successful_operations: int = Field(..., description="Successful operations")
    failed_operations: int = Field(..., description="Failed operations")
    operations: List[SyncOperationResponse] = Field(..., description="Operation results")
    processing_time_ms: Optional[float] = Field(
        None, description="Total batch processing time in milliseconds"
    )
    initiated_at: datetime = Field(..., description="Batch initiation timestamp")
    completed_at: Optional[datetime] = Field(None, description="Batch completion timestamp")


# Performance and monitoring schemas
class SyncPerformanceMetrics(SyncBaseSchema):
    """Schema for synchronization performance metrics."""

    avg_sync_time_ms: float = Field(..., description="Average sync time in milliseconds")
    max_sync_time_ms: float = Field(..., description="Maximum sync time in milliseconds")
    min_sync_time_ms: float = Field(..., description="Minimum sync time in milliseconds")
    avg_conflict_resolution_time_ms: float = Field(
        ..., description="Average conflict resolution time in milliseconds"
    )
    success_rate: float = Field(..., description="Success rate percentage")
    conflict_rate: float = Field(..., description="Conflict rate percentage")
    throughput_ops_per_second: float = Field(..., description="Throughput in operations per second")
    total_operations: int = Field(..., description="Total operations processed")
    total_conflicts: int = Field(..., description="Total conflicts detected")
    total_data_size_bytes: int = Field(..., description="Total data size processed in bytes")
    time_period_start: datetime = Field(..., description="Metrics period start")
    time_period_end: datetime = Field(..., description="Metrics period end")


# Additional schemas for API endpoints
class DataSyncRequest(SyncBaseSchema):
    """Schema for data synchronization requests via API."""

    entity_type: str = Field(..., description="Type of entity to sync", max_length=100)
    entity_id: str = Field(..., description="Entity identifier", max_length=255)
    data_payload: Dict[str, Any] = Field(..., description="Entity data to sync")
    priority: SyncPriorityEnum = Field(SyncPriorityEnum.STANDARD, description="Sync priority")
    conflict_strategy: ConflictResolutionStrategyEnum = Field(
        ConflictResolutionStrategyEnum.LAST_WRITE_WINS,
        description="Conflict resolution strategy"
    )
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class DataSyncResponse(SyncBaseSchema):
    """Schema for data synchronization responses via API."""

    sync_id: str = Field(..., description="Unique sync identifier")
    status: SyncStatusEnum = Field(..., description="Sync status")
    conflicts_detected: bool = Field(..., description="Whether conflicts were detected")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    real_time_updates: str = Field(..., description="WebSocket endpoint for real-time updates")
    processing_priority: SyncPriorityEnum = Field(..., description="Processing priority")
    created_at: datetime = Field(..., description="Creation timestamp")


class SyncStatusResponse(SyncBaseSchema):
    """Schema for sync status responses."""

    sync_id: str = Field(..., description="Sync identifier")
    status: SyncStatusEnum = Field(..., description="Current sync status")
    progress: int = Field(..., description="Completion percentage (0-100)", ge=0, le=100)
    conflicts_detected: int = Field(..., description="Number of conflicts detected", ge=0)
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Error details if failed")


class ConflictResolutionRequest(SyncBaseSchema):
    """Schema for conflict resolution requests."""

    conflict_id: str = Field(..., description="Conflict identifier")
    resolution_strategy: ConflictResolutionStrategyEnum = Field(..., description="Resolution strategy")
    resolution_data: Optional[Dict[str, Any]] = Field(None, description="Resolution data")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")


class ConflictResolutionResponse(SyncBaseSchema):
    """Schema for conflict resolution responses."""

    conflict_id: str = Field(..., description="Conflict identifier")
    resolution_strategy: ConflictResolutionStrategyEnum = Field(..., description="Resolution strategy used")
    resolution_timestamp: datetime = Field(..., description="When conflict was resolved")
    audit_trail: Dict[str, Any] = Field(..., description="Audit trail information")
    sync_status: SyncStatusEnum = Field(..., description="Updated sync status")


class ConflictListResponse(SyncBaseSchema):
    """Schema for conflict list responses."""

    conflicts: List[Dict[str, Any]] = Field(..., description="List of conflicts")
    total_count: int = Field(..., description="Total number of conflicts", ge=0)
    has_more: bool = Field(..., description="Whether more conflicts are available")
    pagination: Dict[str, Any] = Field(..., description="Pagination information")


class SyncBatchCreateRequest(SyncBaseSchema):
    """Schema for sync batch creation requests."""

    sync_ids: List[str] = Field(..., description="List of sync IDs to batch", min_items=1)
    priority: SyncPriorityEnum = Field(..., description="Batch priority")
    batch_name: Optional[str] = Field(None, description="Optional batch name")
    scheduled_at: Optional[datetime] = Field(None, description="Scheduled processing time")
    processing_options: Optional[Dict[str, Any]] = Field(None, description="Processing options")


class SyncBatchStatusResponse(SyncBaseSchema):
    """Schema for sync batch status responses."""

    batch_id: str = Field(..., description="Batch identifier")
    status: SyncStatusEnum = Field(..., description="Batch status")
    progress: int = Field(..., description="Overall progress percentage", ge=0, le=100)
    sync_statuses: List[Dict[str, Any]] = Field(..., description="Individual sync statuses")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")


class SyncMetricsResponse(SyncBaseSchema):
    """Schema for sync metrics responses."""

    total_syncs: int = Field(..., description="Total sync operations", ge=0)
    success_rate: float = Field(..., description="Success rate percentage", ge=0, le=100)
    average_processing_time: float = Field(..., description="Average processing time in ms", ge=0)
    conflict_rate: float = Field(..., description="Conflict rate percentage", ge=0, le=100)
    performance_trends: List[Dict[str, Any]] = Field(..., description="Performance trend data")
    entity_breakdown: Dict[str, Any] = Field(..., description="Metrics by entity type")


class SyncHistoryResponse(SyncBaseSchema):
    """Schema for sync history responses."""

    sync_operations: List[Dict[str, Any]] = Field(..., description="List of sync operations")
    total_count: int = Field(..., description="Total number of operations", ge=0)
    has_more: bool = Field(..., description="Whether more records are available")
    pagination: Dict[str, Any] = Field(..., description="Pagination information")
    summary_stats: Dict[str, Any] = Field(..., description="Summary statistics")


class ForceSyncRequest(SyncBaseSchema):
    """Schema for force sync requests."""

    entity_type: str = Field(..., description="Entity type to force sync")
    entity_id: Optional[str] = Field(None, description="Specific entity ID (optional)")
    override_conflicts: bool = Field(False, description="Whether to override conflicts")
    force_all: bool = Field(False, description="Whether to force sync all entities")
    reason: str = Field(..., description="Reason for forcing sync")
    emergency_mode: bool = Field(False, description="Whether this is emergency sync")


class ForceSyncResponse(SyncBaseSchema):
    """Schema for force sync responses."""

    force_sync_id: str = Field(..., description="Force sync operation identifier")
    entities_affected: int = Field(..., description="Number of entities affected", ge=0)
    override_applied: bool = Field(..., description="Whether overrides were applied")
    processing_priority: SyncPriorityEnum = Field(..., description="Processing priority")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    admin_audit_trail: Dict[str, Any] = Field(..., description="Administrative audit trail")


class SyncListFilters(SyncBaseSchema):
    """Schema for sync list filtering."""

    entity_type: Optional[str] = Field(None, description="Filter by entity type")
    status: Optional[SyncStatusEnum] = Field(None, description="Filter by status")
    priority: Optional[SyncPriorityEnum] = Field(None, description="Filter by priority")
    start_date: Optional[datetime] = Field(None, description="Start date filter")
    end_date: Optional[datetime] = Field(None, description="End date filter")
    limit: int = Field(50, description="Number of records to return", ge=1, le=200)
    offset: int = Field(0, description="Number of records to skip", ge=0)
