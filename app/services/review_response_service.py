"""
Review Response Service for Culture Connect Backend API.

This module provides comprehensive vendor response business logic orchestration including:
- Complete response lifecycle management (creation, status workflow, publication)
- Vendor response workflow with status transitions (draft → published → hidden)
- Integration with email/push notification services for response events
- Performance optimization with <200ms response creation targets
- Comprehensive RBAC integration and audit logging

Implements Task 4.4.1 Phase 4 requirements for review response service implementation with
production-grade business logic following established BaseService patterns.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.review_response_repository import ReviewResponseRepository
from app.repositories.review_repository import ReviewRepository
from app.models.review_models import ReviewResponse, ResponseStatus, Review, ReviewStatus
from app.schemas.review_schemas import (
    ReviewResponseCreateSchema, ReviewResponseUpdateSchema, VendorResponseSchema
)
from app.repositories.base import PaginationParams, QueryResult
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

logger = logging.getLogger(__name__)


class ReviewResponseService(BaseService[ReviewResponse, ReviewResponseRepository]):
    """
    Review response service for vendor response workflow management.

    Provides comprehensive vendor response management including:
    - Response creation with vendor validation and review eligibility
    - Status workflow management (draft → published → hidden)
    - Integration with email/push notification services
    - Performance optimization with <200ms response creation targets
    - Comprehensive audit logging and metrics tracking
    """

    def __init__(self, db: AsyncSession):
        """Initialize review response service with dependencies."""
        super().__init__(
            repository_class=ReviewResponseRepository,
            model_class=ReviewResponse,
            db_session=db
        )

        # Initialize dependent services
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)

        # Initialize repositories
        self.review_repository = ReviewRepository(db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate response creation data with business rules.

        Args:
            data: Response creation data

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['review_id', 'vendor_id', 'content']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate content length
        content = data.get('content', '').strip()
        if len(content) < 10 or len(content) > 1000:
            raise ValidationError("Response content must be between 10 and 1000 characters")

        # Validate status if provided
        status = data.get('status', ResponseStatus.DRAFT)
        if not isinstance(status, ResponseStatus):
            raise ValidationError(f"Invalid response status: {status}")

        # Clean and return validated data
        return {
            **data,
            'content': content,
            'status': status
        }

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate response update data.

        Args:
            data: Response update data
            existing_id: Existing response ID

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate content length if provided
        if 'content' in data:
            content = data['content'].strip()
            if len(content) < 10 or len(content) > 1000:
                raise ValidationError("Response content must be between 10 and 1000 characters")
            data['content'] = content

        # Validate status if provided
        if 'status' in data:
            status = data['status']
            if not isinstance(status, ResponseStatus):
                raise ValidationError(f"Invalid response status: {status}")

        return data

    async def create_response(
        self,
        vendor_id: int,
        review_id: int,
        response_data: ReviewResponseCreateSchema
    ) -> ReviewResponse:
        """
        Create a new vendor response with comprehensive validation.

        Performance target: <200ms for response creation.

        Args:
            vendor_id: Vendor creating the response
            review_id: Associated review ID
            response_data: Response creation data

        Returns:
            Created ReviewResponse instance

        Raises:
            ValidationError: If response data is invalid
            ConflictError: If duplicate response exists
            NotFoundError: If review not found or not eligible
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start(
            "create_response",
            vendor_id=vendor_id,
            review_id=review_id
        )

        try:
            # Validate review eligibility
            review = await self._validate_review_eligibility(review_id, vendor_id)

            # Check for existing response
            async with self.get_session_context() as session:
                repository = ReviewResponseRepository(session)
                existing_response = await repository.get_response_by_review(review_id)

                if existing_response:
                    raise ConflictError(f"Response already exists for review {review_id}")

                # Prepare response data
                validated_data = await self.validate_create_data({
                    'review_id': review_id,
                    'vendor_id': vendor_id,
                    'content': response_data.content,
                    'status': response_data.status,
                    'is_official_response': response_data.is_official_response
                })

                # Create response
                response = await repository.create_response(
                    vendor_id=vendor_id,
                    review_id=review_id,
                    response_data=validated_data
                )

                # Send notifications
                await self._send_response_notifications(response, review, "created")

                # Track metrics
                metrics_collector.increment_counter(
                    "vendor_response_created",
                    tags={"status": response.status.value, "vendor_id": str(vendor_id)}
                )

                self.log_operation_success(
                    correlation,
                    f"Vendor response created with ID: {response.id}, status: {response.status.value}"
                )

                return response

        except Exception as e:
            await self.handle_service_error(e, "create_response", {
                "vendor_id": vendor_id,
                "review_id": review_id
            })

    async def update_response(
        self,
        response_id: int,
        vendor_id: int,
        update_data: ReviewResponseUpdateSchema
    ) -> ReviewResponse:
        """
        Update an existing vendor response with validation.

        Performance target: <200ms for response updates.

        Args:
            response_id: Response ID to update
            vendor_id: Vendor updating the response
            update_data: Response update data

        Returns:
            Updated ReviewResponse instance

        Raises:
            NotFoundError: If response not found
            ValidationError: If update data is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_response",
            response_id=response_id,
            vendor_id=vendor_id
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewResponseRepository(session)

                # Get existing response
                response = await repository.get(response_id)
                if not response:
                    raise NotFoundError("Response", response_id)

                # Validate ownership
                if response.vendor_id != vendor_id:
                    raise ValidationError("Vendor can only update their own responses")

                # Validate response is editable
                if response.status == ResponseStatus.HIDDEN:
                    raise ValidationError("Hidden responses cannot be edited")

                # Prepare update data
                validated_data = await self.validate_update_data(
                    update_data.model_dump(exclude_none=True),
                    response_id
                )

                # Update response
                updated_response = await repository.update(response_id, validated_data)

                # Send notifications if published
                if updated_response.status == ResponseStatus.PUBLISHED:
                    review = await self.review_repository.get(response.review_id)
                    await self._send_response_notifications(updated_response, review, "updated")

                self.log_operation_success(
                    correlation,
                    f"Vendor response updated with ID: {response_id}"
                )

                return updated_response

        except Exception as e:
            await self.handle_service_error(e, "update_response", {
                "response_id": response_id,
                "vendor_id": vendor_id
            })

    async def publish_response(self, response_id: int, vendor_id: int) -> ReviewResponse:
        """
        Publish a draft response.

        Performance target: <100ms for status update.

        Args:
            response_id: Response ID
            vendor_id: Vendor publishing the response

        Returns:
            Updated ReviewResponse instance

        Raises:
            NotFoundError: If response not found
            ValidationError: If response cannot be published
            ServiceError: If publication fails
        """
        correlation = self.log_operation_start(
            "publish_response",
            response_id=response_id,
            vendor_id=vendor_id
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewResponseRepository(session)

                # Get existing response
                response = await repository.get(response_id)
                if not response:
                    raise NotFoundError("Response", response_id)

                # Validate ownership
                if response.vendor_id != vendor_id:
                    raise ValidationError("Vendor can only publish their own responses")

                # Validate current status
                if response.status != ResponseStatus.DRAFT:
                    raise ValidationError(f"Only draft responses can be published (current: {response.status.value})")

                # Publish response
                published_response = await repository.publish_response(response_id)

                # Send notifications
                review = await self.review_repository.get(response.review_id)
                await self._send_response_notifications(published_response, review, "published")

                # Track metrics
                metrics_collector.increment_counter(
                    "vendor_response_published",
                    tags={"vendor_id": str(vendor_id)}
                )

                self.log_operation_success(
                    correlation,
                    f"Vendor response published with ID: {response_id}"
                )

                return published_response

        except Exception as e:
            await self.handle_service_error(e, "publish_response", {
                "response_id": response_id,
                "vendor_id": vendor_id
            })

    async def hide_response(self, response_id: int, vendor_id: int) -> ReviewResponse:
        """
        Hide a published response.

        Performance target: <100ms for status update.

        Args:
            response_id: Response ID
            vendor_id: Vendor hiding the response

        Returns:
            Updated ReviewResponse instance

        Raises:
            NotFoundError: If response not found
            ValidationError: If response cannot be hidden
            ServiceError: If hiding fails
        """
        correlation = self.log_operation_start(
            "hide_response",
            response_id=response_id,
            vendor_id=vendor_id
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewResponseRepository(session)

                # Get existing response
                response = await repository.get(response_id)
                if not response:
                    raise NotFoundError("Response", response_id)

                # Validate ownership
                if response.vendor_id != vendor_id:
                    raise ValidationError("Vendor can only hide their own responses")

                # Hide response
                hidden_response = await repository.hide_response(response_id)

                self.log_operation_success(
                    correlation,
                    f"Vendor response hidden with ID: {response_id}"
                )

                return hidden_response

        except Exception as e:
            await self.handle_service_error(e, "hide_response", {
                "response_id": response_id,
                "vendor_id": vendor_id
            })

    async def get_vendor_responses(
        self,
        vendor_id: int,
        status_filter: Optional[List[ResponseStatus]] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewResponse]:
        """
        Get responses for a specific vendor with filtering.

        Performance target: <200ms for vendor response queries.

        Args:
            vendor_id: Vendor ID
            status_filter: Optional list of response statuses
            date_range: Optional date range filter
            pagination: Pagination parameters

        Returns:
            Query result with vendor responses
        """
        correlation = self.log_operation_start("get_vendor_responses", vendor_id=vendor_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewResponseRepository(session)

                # Convert datetime to date if provided
                date_range_dates = None
                if date_range:
                    date_range_dates = (date_range[0].date(), date_range[1].date())

                result = await repository.get_vendor_responses(
                    vendor_id=vendor_id,
                    status_filter=status_filter,
                    date_range=date_range_dates,
                    pagination=pagination
                )

                self.log_operation_success(
                    correlation,
                    f"Retrieved {len(result.items)} responses for vendor {vendor_id}"
                )

                return result

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_responses", {"vendor_id": vendor_id})

    # Private helper methods

    async def _validate_review_eligibility(self, review_id: int, vendor_id: int) -> Review:
        """
        Validate that review is eligible for vendor response.

        Args:
            review_id: Review ID
            vendor_id: Vendor ID

        Returns:
            Review instance if eligible

        Raises:
            NotFoundError: If review not found
            ValidationError: If review not eligible
        """
        review = await self.review_repository.get(review_id)

        if not review:
            raise NotFoundError("Review", review_id)

        if review.vendor_id != vendor_id:
            raise ValidationError("Vendor can only respond to their own reviews")

        if review.status != ReviewStatus.APPROVED:
            raise ValidationError(f"Only approved reviews can be responded to (current status: {review.status})")

        return review

    async def _send_response_notifications(
        self,
        response: ReviewResponse,
        review: Review,
        action: str
    ) -> None:
        """
        Send notifications for response events.

        Args:
            response: Response instance
            review: Associated review instance
            action: Action performed (created, updated, published)
        """
        try:
            # TODO: Implement notification logic
            # - Email to customer about vendor response
            # - Push notification to customer
            # - Email to vendor confirming response action
            pass
        except Exception as e:
            logger.error(
                f"Failed to send response notifications: {str(e)}",
                extra={"correlation_id": correlation_id.get('')}
            )
