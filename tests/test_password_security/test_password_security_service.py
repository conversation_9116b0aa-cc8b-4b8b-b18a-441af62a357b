"""
Test suite for PasswordSecurityService class.

This module tests the main password security service class including
initialization, dependency injection, and service-level functionality.

Tests cover Task 2.1.2 service layer requirements.
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.repositories.password_security_repository import (
    PasswordHistoryRepository,
    PasswordResetTokenRepository,
    AccountLockoutRepository
)
from app.schemas.password_security import PasswordRequirements


class TestPasswordSecurityService:
    """Test PasswordSecurityService class functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        return PasswordSecurityService(mock_db_session)

    def test_service_initialization(self, mock_db_session):
        """Test proper service initialization."""
        service = PasswordSecurityService(mock_db_session)
        
        assert service.db == mock_db_session
        assert isinstance(service.password_history_repo, PasswordHistoryRepository)
        assert isinstance(service.reset_token_repo, PasswordResetTokenRepository)
        assert isinstance(service.lockout_repo, AccountLockoutRepository)

    def test_service_inherits_from_base_service(self, password_service):
        """Test that service inherits from BaseService."""
        from app.services.base import BaseService
        assert isinstance(password_service, BaseService)

    @pytest.mark.asyncio
    async def test_get_default_requirements(self, password_service):
        """Test getting default password requirements."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.PASSWORD_MIN_LENGTH = 10
            mock_settings.PASSWORD_MAX_LENGTH = 256
            mock_settings.PASSWORD_REQUIRE_UPPERCASE = True
            mock_settings.PASSWORD_REQUIRE_LOWERCASE = True
            mock_settings.PASSWORD_REQUIRE_NUMBERS = True
            mock_settings.PASSWORD_REQUIRE_SPECIAL_CHARS = True
            mock_settings.PASSWORD_MIN_SPECIAL_CHARS = 2
            mock_settings.PASSWORD_MAX_REPEATED_CHARS = 2
            
            requirements = password_service._get_default_requirements()
            
            assert requirements.min_length == 10
            assert requirements.max_length == 256
            assert requirements.require_uppercase is True
            assert requirements.require_lowercase is True
            assert requirements.require_numbers is True
            assert requirements.require_special_chars is True
            assert requirements.min_special_chars == 2
            assert requirements.max_repeated_chars == 2

    @pytest.mark.asyncio
    async def test_get_default_requirements_fallback(self, password_service):
        """Test default requirements fallback values."""
        with patch('app.core.config.settings') as mock_settings:
            # Remove attributes to test fallback
            del mock_settings.PASSWORD_MIN_LENGTH
            del mock_settings.PASSWORD_MAX_LENGTH
            
            requirements = password_service._get_default_requirements()
            
            # Should use default values
            assert requirements.min_length == 8
            assert requirements.max_length == 128

    def test_has_excessive_repeated_chars(self, password_service):
        """Test detection of excessive repeated characters."""
        # Test cases: (password, max_repeated, expected_result)
        test_cases = [
            ("password", 3, False),
            ("passsword", 3, True),  # 3 consecutive 's'
            ("passssword", 3, True),  # 4 consecutive 's'
            ("pa$$word", 3, False),  # Only 2 consecutive '$'
            ("pa$$$word", 3, True),  # 3 consecutive '$'
            ("aaa", 2, True),  # 3 consecutive 'a' with max 2
            ("aa", 2, False),  # 2 consecutive 'a' with max 2
        ]
        
        for password, max_repeated, expected in test_cases:
            result = password_service._has_excessive_repeated_chars(password, max_repeated)
            assert result == expected, f"Failed for password '{password}' with max {max_repeated}"

    def test_calculate_entropy(self, password_service):
        """Test password entropy calculation."""
        test_cases = [
            ("a", 0.0),  # Single lowercase
            ("A", 0.0),  # Single uppercase  
            ("1", 0.0),  # Single digit
            ("!", 0.0),  # Single special
            ("aA", 2 * 52),  # Mixed case
            ("aA1", 3 * 62),  # Mixed case + digit
            ("aA1!", 4 * 94),  # All character types
        ]
        
        for password, expected_min in test_cases:
            entropy = password_service._calculate_entropy(password)
            if expected_min == 0.0:
                assert entropy >= 0.0
            else:
                # Allow some tolerance for floating point calculations
                assert entropy >= expected_min * 0.9

    def test_determine_strength_level(self, password_service):
        """Test password strength level determination."""
        from app.schemas.password_security import PasswordStrengthLevel
        
        test_cases = [
            (0, 1, PasswordStrengthLevel.VERY_WEAK),  # Has violations
            (95, 0, PasswordStrengthLevel.VERY_STRONG),  # High score, no violations
            (80, 0, PasswordStrengthLevel.STRONG),  # Good score, no violations
            (65, 0, PasswordStrengthLevel.GOOD),  # Medium score, no violations
            (45, 0, PasswordStrengthLevel.FAIR),  # Low-medium score, no violations
            (25, 0, PasswordStrengthLevel.WEAK),  # Low score, no violations
            (10, 0, PasswordStrengthLevel.VERY_WEAK),  # Very low score, no violations
        ]
        
        for score, violations_count, expected_level in test_cases:
            level = password_service._determine_strength_level(score, violations_count)
            assert level == expected_level

    def test_estimate_crack_time(self, password_service):
        """Test crack time estimation."""
        test_cases = [
            (15, "seconds"),
            (25, "minutes"),
            (35, "hours"),
            (45, "days"),
            (55, "years"),
            (65, "decades"),
            (75, "centuries"),
        ]
        
        for entropy_bits, expected_time in test_cases:
            crack_time = password_service._estimate_crack_time(entropy_bits)
            assert crack_time == expected_time

    @pytest.mark.asyncio
    async def test_service_method_error_handling(self, password_service):
        """Test error handling in service methods."""
        # Test that service methods handle repository errors gracefully
        with patch.object(password_service.password_history_repo, 'check_password_reuse') as mock_check:
            mock_check.side_effect = Exception("Database error")
            
            # Should return False (allow password) in case of error
            result = await password_service.check_password_history(1, "test_password")
            assert result is False

    @pytest.mark.asyncio
    async def test_service_logging(self, password_service):
        """Test that service methods perform appropriate logging."""
        with patch('app.services.password_security_service.logger') as mock_logger:
            with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
                mock_add.return_value = Mock()
                
                await password_service.add_password_to_history(1, "hash")
                
                # Verify logging was called
                mock_logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_service_correlation_id_usage(self, password_service):
        """Test that service uses correlation IDs for logging."""
        with patch('app.core.logging.correlation_id') as mock_correlation_id:
            mock_correlation_id.get.return_value = "test-correlation-id"
            
            with patch('app.services.password_security_service.logger') as mock_logger:
                with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
                    mock_add.return_value = Mock()
                    
                    await password_service.add_password_to_history(1, "hash")
                    
                    # Verify correlation ID was used in logging
                    mock_logger.info.assert_called()
                    call_args = mock_logger.info.call_args
                    assert "correlation_id" in call_args[1]["extra"]

    @pytest.mark.asyncio
    async def test_repository_dependency_injection(self, mock_db_session):
        """Test that repositories are properly injected."""
        service = PasswordSecurityService(mock_db_session)
        
        # Verify all repositories are initialized with the same session
        assert service.password_history_repo.db == mock_db_session
        assert service.reset_token_repo.db == mock_db_session
        assert service.lockout_repo.db == mock_db_session

    @pytest.mark.asyncio
    async def test_service_configuration_integration(self, password_service):
        """Test service integration with configuration settings."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS = 48
            
            user_id = 1
            client_ip = "*************"
            
            with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
                mock_create.return_value = Mock()
                
                await password_service.create_password_reset_token(user_id, client_ip)
                
                # Verify configuration was used
                call_args = mock_create.call_args
                expires_at = call_args[1]['expires_at']
                # Should be approximately 48 hours from now
                from datetime import datetime, timedelta
                expected_expiry = datetime.utcnow() + timedelta(hours=48)
                assert abs((expires_at - expected_expiry).total_seconds()) < 60

    @pytest.mark.asyncio
    async def test_service_transaction_handling(self, password_service):
        """Test that service properly handles database transactions."""
        # This would test transaction rollback on errors, but since we're using mocks,
        # we'll test that the service doesn't interfere with transaction management
        
        with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
            mock_add.side_effect = Exception("Database error")
            
            result = await password_service.add_password_to_history(1, "hash")
            
            # Service should handle the error and return False
            assert result is False

    @pytest.mark.asyncio
    async def test_service_method_parameter_validation(self, password_service):
        """Test service method parameter validation."""
        # Test with invalid parameters
        with pytest.raises(Exception):
            await password_service.validate_password_strength(None)

    @pytest.mark.asyncio
    async def test_service_return_type_consistency(self, password_service):
        """Test that service methods return consistent types."""
        # Test password strength validation returns PasswordValidationResult
        result = await password_service.validate_password_strength("TestP@ssw0rd!")
        from app.schemas.password_security import PasswordValidationResult
        assert isinstance(result, PasswordValidationResult)
        
        # Test suggestions returns list of strings
        suggestions = await password_service.generate_password_suggestions()
        assert isinstance(suggestions, list)
        assert all(isinstance(suggestion, str) for suggestion in suggestions)

    @pytest.mark.asyncio
    async def test_service_performance_considerations(self, password_service):
        """Test service performance considerations."""
        # Test that expensive operations are not called unnecessarily
        with patch.object(password_service, '_calculate_entropy') as mock_entropy:
            mock_entropy.return_value = 50.0
            
            await password_service.validate_password_strength("TestP@ssw0rd!")
            
            # Entropy should only be calculated once
            mock_entropy.assert_called_once()

    @pytest.mark.asyncio
    async def test_service_security_considerations(self, password_service):
        """Test service security considerations."""
        # Test that sensitive data is not logged
        with patch('app.services.password_security_service.logger') as mock_logger:
            await password_service.validate_password_strength("SecretP@ssw0rd!")
            
            # Verify password is not in log messages
            for call in mock_logger.info.call_args_list:
                log_message = str(call)
                assert "SecretP@ssw0rd!" not in log_message

    def test_service_memory_management(self, password_service):
        """Test service memory management."""
        # Test that service doesn't hold references to sensitive data
        import gc
        
        # Create some test data
        test_password = "TestP@ssw0rd!" * 1000  # Large string
        
        # Use the password in validation
        # (This would be an async test in real implementation)
        
        # Clear the reference
        del test_password
        
        # Force garbage collection
        gc.collect()
        
        # Service should not hold references to the password
        # (This is more of a conceptual test - actual implementation would need memory profiling)
        assert True  # Placeholder assertion
