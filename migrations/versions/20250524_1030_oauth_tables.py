"""Add OAuth tables for Task 2.1.3

Revision ID: oauth_tables
Revises: password_security_tables
Create Date: 2025-05-24 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'oauth_tables'
down_revision = 'password_security_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add OAuth tables."""
    
    # Create oauth_providers table
    op.create_table('oauth_providers',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=False),
        sa.Column('client_id', sa.String(length=255), nullable=False),
        sa.Column('client_secret_encrypted', sa.Text(), nullable=False),
        sa.Column('authorization_url', sa.String(length=500), nullable=False),
        sa.Column('token_url', sa.String(length=500), nullable=False),
        sa.Column('user_info_url', sa.String(length=500), nullable=False),
        sa.Column('scopes', sa.JSON(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('icon_url', sa.String(length=500), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='pk_oauth_providers'),
        sa.UniqueConstraint('name', name='uq_oauth_providers_name'),
        sa.UniqueConstraint('uuid', name='uq_oauth_providers_uuid')
    )
    
    # Create indexes for oauth_providers
    op.create_index('idx_oauth_provider_name_active', 'oauth_providers', ['name', 'is_active'])
    op.create_index('idx_oauth_provider_active', 'oauth_providers', ['is_active'])
    
    # Create oauth_accounts table
    op.create_table('oauth_accounts',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('provider_id', sa.Integer(), nullable=False),
        sa.Column('provider_user_id', sa.String(length=255), nullable=False),
        sa.Column('provider_username', sa.String(length=255), nullable=True),
        sa.Column('provider_email', sa.String(length=255), nullable=True),
        sa.Column('profile_data', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, default=False),
        sa.Column('first_connected_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_connected_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='pk_oauth_accounts'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_oauth_accounts_user_id_users', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['provider_id'], ['oauth_providers.id'], name='fk_oauth_accounts_provider_id_oauth_providers', ondelete='CASCADE'),
        sa.UniqueConstraint('provider_id', 'provider_user_id', name='uq_oauth_accounts_provider_user'),
        sa.UniqueConstraint('uuid', name='uq_oauth_accounts_uuid')
    )
    
    # Create indexes for oauth_accounts
    op.create_index('idx_oauth_account_user_provider', 'oauth_accounts', ['user_id', 'provider_id'])
    op.create_index('idx_oauth_account_provider_user', 'oauth_accounts', ['provider_id', 'provider_user_id'])
    op.create_index('idx_oauth_account_email', 'oauth_accounts', ['provider_email'])
    op.create_index('idx_oauth_account_active', 'oauth_accounts', ['is_active'])
    
    # Create oauth_tokens table
    op.create_table('oauth_tokens',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('oauth_account_id', sa.Integer(), nullable=False),
        sa.Column('access_token_encrypted', sa.Text(), nullable=False),
        sa.Column('refresh_token_encrypted', sa.Text(), nullable=True),
        sa.Column('token_type', sa.String(length=50), nullable=False, default='Bearer'),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('scopes', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_revoked', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='pk_oauth_tokens'),
        sa.ForeignKeyConstraint(['oauth_account_id'], ['oauth_accounts.id'], name='fk_oauth_tokens_oauth_account_id_oauth_accounts', ondelete='CASCADE'),
        sa.UniqueConstraint('uuid', name='uq_oauth_tokens_uuid')
    )
    
    # Create indexes for oauth_tokens
    op.create_index('idx_oauth_token_account_active', 'oauth_tokens', ['oauth_account_id', 'is_active'])
    op.create_index('idx_oauth_token_expires', 'oauth_tokens', ['expires_at'])
    op.create_index('idx_oauth_token_revoked', 'oauth_tokens', ['is_revoked'])
    
    # Create oauth_states table
    op.create_table('oauth_states',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('state_token', sa.String(length=255), nullable=False),
        sa.Column('provider_id', sa.Integer(), nullable=False),
        sa.Column('redirect_uri', sa.String(length=500), nullable=False),
        sa.Column('scopes', sa.JSON(), nullable=True),
        sa.Column('client_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('is_used', sa.Boolean(), nullable=False, default=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='pk_oauth_states'),
        sa.ForeignKeyConstraint(['provider_id'], ['oauth_providers.id'], name='fk_oauth_states_provider_id_oauth_providers', ondelete='CASCADE'),
        sa.UniqueConstraint('state_token', name='uq_oauth_states_state_token'),
        sa.UniqueConstraint('uuid', name='uq_oauth_states_uuid')
    )
    
    # Create indexes for oauth_states
    op.create_index('idx_oauth_state_token_used', 'oauth_states', ['state_token', 'is_used'])
    op.create_index('idx_oauth_state_expires', 'oauth_states', ['expires_at'])
    op.create_index('idx_oauth_state_provider', 'oauth_states', ['provider_id'])


def downgrade() -> None:
    """Remove OAuth tables."""
    
    # Drop tables in reverse order due to foreign key constraints
    op.drop_table('oauth_states')
    op.drop_table('oauth_tokens')
    op.drop_table('oauth_accounts')
    op.drop_table('oauth_providers')
