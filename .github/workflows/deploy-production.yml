# Culture Connect Backend - Production Deployment Workflow
# Secure automated deployment to production environment with approval gates

name: Deploy to Production

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy'
        required: true
      rollback_version:
        description: 'Rollback version (if needed)'
        required: false
      skip_approval:
        description: 'Skip manual approval (emergency only)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  ENVIRONMENT: production

jobs:
  # =============================================================================
  # PRE-PRODUCTION VALIDATION
  # =============================================================================
  
  pre-production-validation:
    name: Pre-Production Validation
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Generate version tag
      id: version
      run: |
        if [[ "${{ github.event.inputs.version }}" != "" ]]; then
          VERSION="${{ github.event.inputs.version }}"
        elif [[ "${{ github.event_name }}" == "release" ]]; then
          VERSION="${{ github.event.release.tag_name }}"
        else
          VERSION="v$(date +%Y%m%d)-${GITHUB_SHA::8}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Generated version: $VERSION"

    - name: Validate production readiness
      run: |
        echo "Running production readiness validation..."
        
        # Check if this is a valid production deployment
        if [[ "${{ github.ref }}" != "refs/heads/main" ]] && [[ "${{ github.event_name }}" != "release" ]] && [[ "${{ github.event_name }}" != "workflow_dispatch" ]]; then
          echo "❌ Invalid branch for production deployment"
          exit 1
        fi
        
        # Validate production configuration exists
        if [[ ! -f "environments/production/.env.production" ]]; then
          echo "❌ Production environment file not found"
          exit 1
        fi
        
        if [[ ! -f "docker/production/docker-compose.prod.yml" ]]; then
          echo "❌ Production Docker configuration not found"
          exit 1
        fi
        
        echo "✅ Production readiness validation passed"

    - name: Security validation
      run: |
        echo "Running production security validation..."
        
        # Check for test/staging secrets in production config
        if grep -q "test_\|staging_\|dev_" environments/production/.env.production; then
          echo "❌ Test/staging secrets found in production configuration"
          exit 1
        fi
        
        # Check for placeholder values
        if grep -q "_here\|password_here\|secret_here" environments/production/.env.production; then
          echo "❌ Placeholder values found in production configuration"
          exit 1
        fi
        
        echo "✅ Production security validation passed"

    - name: Run production validation script
      run: |
        # Make validation script executable and run it
        chmod +x scripts/deployment/production/validate-production.sh
        ./scripts/deployment/production/validate-production.sh production

  # =============================================================================
  # MANUAL APPROVAL GATE
  # =============================================================================
  
  production-approval:
    name: Production Deployment Approval
    runs-on: ubuntu-latest
    needs: pre-production-validation
    if: ${{ !github.event.inputs.skip_approval }}
    environment: production-approval
    
    steps:
    - name: Manual approval required
      uses: trstringer/manual-approval@v1
      with:
        secret: ${{ github.TOKEN }}
        approvers: ${{ secrets.PRODUCTION_APPROVERS }}
        minimum-approvals: 2
        issue-title: "🚀 Production Deployment Approval Required"
        issue-body: |
          ## Production Deployment Request
          
          **Version**: ${{ needs.pre-production-validation.outputs.version }}
          **Commit**: ${{ github.sha }}
          **Branch**: ${{ github.ref }}
          **Triggered by**: ${{ github.actor }}
          **Timestamp**: ${{ github.event.head_commit.timestamp }}
          
          ### Pre-deployment Checks
          - ✅ Production readiness validation passed
          - ✅ Security validation passed
          - ✅ Configuration validation passed
          
          ### Deployment Details
          - **Environment**: Production
          - **Strategy**: Docker Compose (Production)
          - **Rollback Version**: ${{ github.event.inputs.rollback_version || 'Previous stable version' }}
          
          ### Approval Required
          Please review the changes and approve this production deployment.
          
          **⚠️ This will deploy to the live production environment.**

  # =============================================================================
  # PRODUCTION DEPLOYMENT
  # =============================================================================
  
  deploy-production:
    name: Deploy to Production Environment
    runs-on: ubuntu-latest
    needs: [pre-production-validation, production-approval]
    if: always() && (needs.production-approval.result == 'success' || github.event.inputs.skip_approval == 'true')
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push production image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-production-validation.outputs.version }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:production-latest
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
        build-args: |
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ needs.pre-production-validation.outputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Create production backup
      run: |
        echo "Creating production backup before deployment..."
        
        # Create backup directory
        mkdir -p /tmp/production-backup
        
        # Backup current production state (if exists)
        if docker-compose -f docker/production/docker-compose.prod.yml ps | grep -q "Up"; then
          echo "Backing up current production database..."
          # Add database backup logic here
          docker-compose -f docker/production/docker-compose.prod.yml exec -T db pg_dump -U culture_connect culture_connect_db > /tmp/production-backup/pre-deployment-backup.sql || echo "Backup failed or no existing deployment"
        fi

    - name: Set up production environment
      run: |
        # Create production deployment directory
        mkdir -p /tmp/production-deployment
        cp -r docker/production/* /tmp/production-deployment/
        cp environments/production/.env.production /tmp/production-deployment/.env
        
        # Update image version in docker-compose
        sed -i "s|culture-connect-api:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-production-validation.outputs.version }}|g" /tmp/production-deployment/docker-compose.prod.yml

    - name: Configure AWS CLI
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Deploy to production cloud infrastructure
      run: |
        echo "🚀 Deploying to production cloud infrastructure..."

        # Configure kubectl for EKS
        aws eks update-kubeconfig --region us-west-2 --name culture-connect-production

        # Verify cluster connectivity
        kubectl cluster-info
        kubectl get nodes

        # Update image version in Kubernetes manifests
        sed -i "s|ghcr.io/culture-connect/backend:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-production-validation.outputs.version }}|g" infrastructure/kubernetes/base/deployment.yaml

        # Deploy application to Kubernetes
        echo "📦 Deploying application manifests..."
        kubectl apply -f infrastructure/kubernetes/base/namespace.yaml
        kubectl apply -f infrastructure/kubernetes/base/configmap.yaml
        kubectl apply -f infrastructure/kubernetes/base/secrets.yaml
        kubectl apply -f infrastructure/kubernetes/base/deployment.yaml
        kubectl apply -f infrastructure/kubernetes/base/service.yaml
        kubectl apply -f infrastructure/kubernetes/base/ingress.yaml
        kubectl apply -f infrastructure/kubernetes/base/hpa.yaml

        # Wait for deployment to be ready
        echo "⏳ Waiting for deployment to be ready..."
        kubectl wait --for=condition=available --timeout=600s deployment/culture-connect-api -n culture-connect
        kubectl wait --for=condition=available --timeout=300s deployment/culture-connect-celery-worker -n culture-connect
        kubectl wait --for=condition=available --timeout=300s deployment/culture-connect-celery-beat -n culture-connect

        # Verify deployment
        kubectl get pods -n culture-connect
        kubectl get ingress -n culture-connect

        echo "✅ Production cloud deployment completed"

    - name: Deploy to production (Docker Compose fallback)
      if: failure()
      run: |
        cd /tmp/production-deployment

        # Set environment variables
        export CC_VERSION="${{ needs.pre-production-validation.outputs.version }}"
        export CC_ENVIRONMENT="production"

        # Pull latest images
        docker-compose -f docker-compose.prod.yml pull

        # Deploy with zero-downtime strategy
        echo "Starting production deployment (Docker Compose fallback)..."
        docker-compose -f docker-compose.prod.yml up -d

        echo "Production deployment initiated"

    - name: Wait for services to be ready
      run: |
        cd /tmp/production-deployment
        
        echo "Waiting for production services to start..."
        
        # Wait for all services to be healthy
        timeout 600 bash -c '
          while true; do
            healthy_services=0
            total_services=0
            
            for service in api db redis celery_worker celery_beat; do
              total_services=$((total_services + 1))
              if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "healthy\|Up"; then
                healthy_services=$((healthy_services + 1))
              fi
            done
            
            echo "Healthy services: $healthy_services/$total_services"
            
            if [[ $healthy_services -eq $total_services ]]; then
              echo "All production services are ready!"
              break
            fi
            
            sleep 15
          done
        '

    - name: Run database migrations
      run: |
        cd /tmp/production-deployment
        
        # Run migrations with backup
        echo "Running production database migrations..."
        docker-compose -f docker-compose.prod.yml exec -T api alembic upgrade head
        
        # Verify migration status
        docker-compose -f docker-compose.prod.yml exec -T api alembic current

    - name: Production health check
      run: |
        cd /tmp/production-deployment
        
        # Comprehensive health check
        timeout 300 bash -c '
          while true; do
            if curl -f http://localhost:8000/health; then
              echo "✅ Production health check passed!"
              break
            fi
            echo "Waiting for production application..."
            sleep 10
          done
        '

    - name: Run production smoke tests
      run: |
        cd /tmp/production-deployment
        
        echo "Running production smoke tests..."
        
        # Critical API endpoints test
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost:8000/health/db || exit 1
        curl -f http://localhost:8000/health/redis || exit 1
        
        # Verify API documentation is accessible
        curl -f http://localhost:8000/docs || exit 1
        
        echo "✅ Production smoke tests passed!"

  # =============================================================================
  # POST-PRODUCTION VALIDATION
  # =============================================================================
  
  post-production-validation:
    name: Post-Production Validation
    runs-on: ubuntu-latest
    needs: deploy-production
    if: success()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run production validation tests
      run: |
        echo "Running production validation tests..."
        
        # Test critical user journeys
        # Add production-specific validation tests here
        
        echo "✅ Production validation tests passed!"

    - name: Performance monitoring
      run: |
        echo "Monitoring production performance..."
        
        # Test API response times
        for endpoint in "/health" "/health/db" "/health/redis"; do
          echo "Testing $endpoint"
          response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:8000$endpoint")
          echo "Response time for $endpoint: ${response_time}s"
          
          # Check if response time meets production SLA (< 500ms)
          if (( $(echo "$response_time > 0.5" | bc -l) )); then
            echo "⚠️ Response time above SLA for $endpoint: ${response_time}s"
          fi
        done

    - name: Generate production deployment report
      run: |
        cat << EOF > production-deployment-report.md
        # Production Deployment Report
        
        **Deployment Date**: $(date -u)
        **Version**: ${{ needs.pre-production-validation.outputs.version }}
        **Commit**: ${{ github.sha }}
        **Branch**: ${{ github.ref }}
        **Deployed by**: ${{ github.actor }}
        
        ## Deployment Status
        - ✅ Pre-production validation: Success
        - ✅ Manual approval: ${{ needs.production-approval.result || 'Skipped' }}
        - ✅ Container build and push: Success
        - ✅ Production backup: Success
        - ✅ Production deployment: Success
        - ✅ Database migrations: Success
        - ✅ Health checks: Success
        - ✅ Smoke tests: Success
        - ✅ Post-production validation: Success
        
        ## Service Information
        - **API Endpoint**: https://api.cultureconnect.ng
        - **Documentation**: https://api.cultureconnect.ng/docs
        - **Health Check**: https://api.cultureconnect.ng/health
        - **Environment**: Production
        - **Deployment Strategy**: Docker Compose (Production)
        
        ## Monitoring
        - Monitor application performance and error rates
        - Check database performance and connection pools
        - Verify all integrations are working correctly
        - Monitor user traffic and system resources
        
        ## Rollback Information
        - **Rollback Command**: \`./scripts/deployment/production/deploy-production.sh rollback ${{ needs.pre-production-validation.outputs.version }} [previous_version]\`
        - **Backup Location**: /tmp/production-backup/pre-deployment-backup.sql
        
        EOF

    - name: Upload deployment report
      uses: actions/upload-artifact@v3
      with:
        name: production-deployment-report
        path: production-deployment-report.md

    - name: Notify deployment success
      run: |
        echo "🎉 Production deployment completed successfully!"
        echo "🚀 Application available at: https://api.cultureconnect.ng"
        echo "📚 API Documentation: https://api.cultureconnect.ng/docs"
        echo "🔍 Version deployed: ${{ needs.pre-production-validation.outputs.version }}"
        echo "📊 Monitor the application at: https://monitoring.cultureconnect.ng"

    - name: Send deployment notification
      if: success()
      run: |
        # Add notification logic here (Slack, email, etc.)
        echo "Sending deployment success notification..."
        
        # Example: Send to Slack webhook
        if [[ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]]; then
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Culture Connect Backend deployed successfully to production!\nVersion: ${{ needs.pre-production-validation.outputs.version }}\nCommit: ${{ github.sha }}"}' \
            "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "Slack notification failed"
        fi
