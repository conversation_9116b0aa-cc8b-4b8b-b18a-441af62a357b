"""
API Versioning Support for Culture Connect Backend API.

This module provides comprehensive API versioning capabilities with:
- Schema version management and backward compatibility
- Version-specific validation rules and transformations
- Automatic schema migration and deprecation handling
- Version negotiation and client compatibility checks
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, ClassVar
from enum import Enum
from dataclasses import dataclass

from pydantic import BaseModel, Field, field_validator

from app.schemas.base import BaseSchema

# Logger for versioning operations
logger = logging.getLogger(__name__)


class APIVersion(str, Enum):
    """Supported API versions."""

    V1_0 = "1.0"
    V1_1 = "1.1"
    V2_0 = "2.0"

    @classmethod
    def latest(cls) -> "APIVersion":
        """Get the latest API version."""
        return cls.V2_0

    @classmethod
    def supported_versions(cls) -> List[str]:
        """Get list of supported versions."""
        return [version.value for version in cls]

    def is_deprecated(self) -> bool:
        """Check if this version is deprecated."""
        deprecated_versions = [self.V1_0]
        return self in deprecated_versions


@dataclass
class VersionInfo:
    """Version information and metadata."""

    version: APIVersion
    release_date: datetime
    deprecation_date: Optional[datetime] = None
    end_of_life_date: Optional[datetime] = None
    breaking_changes: List[str] = None
    migration_guide: Optional[str] = None

    def __post_init__(self):
        if self.breaking_changes is None:
            self.breaking_changes = []

    @property
    def is_deprecated(self) -> bool:
        """Check if version is deprecated."""
        if self.deprecation_date is None:
            return False
        return datetime.now(timezone.utc) >= self.deprecation_date

    @property
    def is_end_of_life(self) -> bool:
        """Check if version has reached end of life."""
        if self.end_of_life_date is None:
            return False
        return datetime.now(timezone.utc) >= self.end_of_life_date


class VersionRegistry:
    """Registry for managing API versions and their metadata."""

    _versions: Dict[APIVersion, VersionInfo] = {
        APIVersion.V1_0: VersionInfo(
            version=APIVersion.V1_0,
            release_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
            deprecation_date=datetime(2024, 6, 1, tzinfo=timezone.utc),
            end_of_life_date=datetime(2024, 12, 31, tzinfo=timezone.utc),
            breaking_changes=[
                "Removed legacy authentication endpoints",
                "Changed pagination parameter names"
            ],
            migration_guide="https://docs.cultureconnect.ng/migration/v1.0-to-v1.1"
        ),
        APIVersion.V1_1: VersionInfo(
            version=APIVersion.V1_1,
            release_date=datetime(2024, 3, 1, tzinfo=timezone.utc),
            breaking_changes=[
                "Enhanced validation rules for vendor schemas",
                "Updated error response format"
            ]
        ),
        APIVersion.V2_0: VersionInfo(
            version=APIVersion.V2_0,
            release_date=datetime(2024, 6, 1, tzinfo=timezone.utc),
            breaking_changes=[
                "Complete schema restructure",
                "New authentication system",
                "Enhanced promotional features"
            ]
        )
    }

    @classmethod
    def get_version_info(cls, version: APIVersion) -> Optional[VersionInfo]:
        """Get version information."""
        return cls._versions.get(version)

    @classmethod
    def get_supported_versions(cls) -> List[APIVersion]:
        """Get list of supported versions (non-EOL)."""
        return [
            version for version, info in cls._versions.items()
            if not info.is_end_of_life
        ]

    @classmethod
    def get_latest_version(cls) -> APIVersion:
        """Get the latest supported version."""
        supported = cls.get_supported_versions()
        return max(supported, key=lambda v: v.value)


class VersionedSchema(BaseSchema):
    """
    Base class for versioned schemas with automatic compatibility handling.

    Features:
    - Version-specific validation rules
    - Automatic field migration between versions
    - Backward compatibility support
    - Deprecation warnings
    """

    # Schema version metadata (using ClassVar to avoid field validation)
    api_version: ClassVar[APIVersion] = APIVersion.latest()
    min_version: ClassVar[APIVersion] = APIVersion.V1_0
    max_version: ClassVar[APIVersion] = APIVersion.latest()

    model_config = {
        "use_enum_values": True,
        "validate_assignment": True,
        "extra": "forbid"
    }

    def __init__(self, **data):
        """Initialize with version-specific processing."""
        # Extract version from data or headers
        api_version = self._extract_version(data)

        # Apply version-specific transformations
        transformed_data = self._transform_for_version(data, api_version)

        # Initialize with transformed data
        super().__init__(**transformed_data)

        # Store version in model's __dict__ to avoid Pydantic validation
        object.__setattr__(self, '_current_api_version', api_version)

        # Log deprecation warnings
        self._check_deprecation_warnings(api_version)

    def _extract_version(self, data: Dict[str, Any]) -> APIVersion:
        """Extract API version from data or default to latest."""
        # Check for explicit version in data
        version_keys = ['api_version', '_api_version', 'version']
        for key in version_keys:
            if key in data:
                version_str = data.pop(key)
                try:
                    return APIVersion(version_str)
                except ValueError:
                    logger.warning(f"Invalid API version: {version_str}")

        # Default to latest supported version
        return APIVersion.latest()

    def _transform_for_version(self, data: Dict[str, Any], version: APIVersion) -> Dict[str, Any]:
        """Apply version-specific transformations to data."""
        # Get transformation method for this version
        transform_method = getattr(self, f'_transform_v{version.value.replace(".", "_")}', None)

        if transform_method:
            return transform_method(data)

        return data

    def _check_deprecation_warnings(self, version: APIVersion):
        """Check and log deprecation warnings."""
        version_info = VersionRegistry.get_version_info(version)

        if version_info and version_info.is_deprecated:
            logger.warning(
                f"API version {version.value} is deprecated. "
                f"Please migrate to version {APIVersion.latest().value}. "
                f"Migration guide: {version_info.migration_guide}"
            )

    def model_dump(self, version: Optional[APIVersion] = None, **kwargs) -> Dict[str, Any]:
        """
        Serialize to dict with version-specific formatting.

        Args:
            version: Target API version for serialization
            **kwargs: Additional serialization options

        Returns:
            Version-specific dictionary representation
        """
        if version is None:
            version = getattr(self, '_current_api_version', APIVersion.latest())

        # Get base dictionary
        result = super().model_dump(**kwargs)

        # Apply version-specific output transformations
        return self._format_for_version(result, version)

    # Backward compatibility method
    def dict(self, version: Optional[APIVersion] = None, **kwargs) -> Dict[str, Any]:
        """Backward compatibility method."""
        return self.model_dump(version=version, **kwargs)

    def _format_for_version(self, data: Dict[str, Any], version: APIVersion) -> Dict[str, Any]:
        """Format output data for specific API version."""
        # Get formatting method for this version
        format_method = getattr(self, f'_format_v{version.value.replace(".", "_")}', None)

        if format_method:
            return format_method(data)

        return data


class VersionCompatibilityMixin:
    """Mixin for handling version compatibility in schemas."""

    def _transform_v1_0(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform data for API version 1.0 compatibility."""
        # Handle legacy field names
        if 'page_number' in data:
            data['page'] = data.pop('page_number')

        if 'page_size' in data:
            data['size'] = data.pop('page_size')

        # Handle legacy boolean values
        for field in ['is_active', 'is_verified', 'is_featured']:
            if field in data and isinstance(data[field], str):
                data[field] = data[field].lower() in ('true', '1', 'yes')

        return data

    def _transform_v1_1(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform data for API version 1.1 compatibility."""
        # Enhanced validation for v1.1
        if 'email' in data and data['email']:
            data['email'] = data['email'].lower().strip()

        return data

    def _format_v1_0(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format output for API version 1.0."""
        # Convert new field names back to legacy names
        if 'page' in data:
            data['page_number'] = data.pop('page')

        if 'size' in data:
            data['page_size'] = data.pop('size')

        # Remove fields not available in v1.0
        v1_0_excluded_fields = ['metadata', 'tags', 'enhanced_features']
        for field in v1_0_excluded_fields:
            data.pop(field, None)

        return data

    def _format_v1_1(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format output for API version 1.1."""
        # Add version-specific enhancements
        if 'created_at' in data:
            data['creation_timestamp'] = data['created_at']

        return data


class VersionNegotiator:
    """Handles API version negotiation and compatibility checks."""

    @staticmethod
    def negotiate_version(
        requested_version: Optional[str],
        supported_versions: List[APIVersion],
        default_version: APIVersion = None
    ) -> APIVersion:
        """
        Negotiate API version based on client request and server support.

        Args:
            requested_version: Version requested by client
            supported_versions: Versions supported by server
            default_version: Default version if negotiation fails

        Returns:
            Negotiated API version
        """
        if default_version is None:
            default_version = APIVersion.latest()

        # If no version requested, return default
        if not requested_version:
            return default_version

        # Try to match exact version
        try:
            requested = APIVersion(requested_version)
            if requested in supported_versions:
                return requested
        except ValueError:
            logger.warning(f"Invalid version format: {requested_version}")

        # Try to find compatible version
        # For now, return default if exact match not found
        # In future, could implement semantic version matching

        logger.info(
            f"Version {requested_version} not supported, "
            f"using default {default_version.value}"
        )
        return default_version

    @staticmethod
    def check_compatibility(
        client_version: APIVersion,
        server_version: APIVersion
    ) -> bool:
        """
        Check if client and server versions are compatible.

        Args:
            client_version: Version used by client
            server_version: Version supported by server

        Returns:
            True if versions are compatible
        """
        # For now, exact match required
        # In future, could implement semantic compatibility rules
        return client_version == server_version


# Version-aware response wrapper
class VersionedResponse(BaseSchema):
    """Wrapper for versioned API responses."""

    data: Any = Field(..., description="Response data")
    version: APIVersion = Field(..., description="API version used")
    timestamp: datetime = Field(
        default=None,
        description="Response timestamp"
    )
    deprecation_warning: Optional[str] = Field(
        default=None,
        description="Deprecation warning if applicable"
    )

    def __init__(self, data: Any, version: APIVersion, **kwargs):
        """Initialize versioned response with deprecation check."""
        deprecation_warning = None

        # Check for deprecation
        version_info = VersionRegistry.get_version_info(version)
        if version_info and version_info.is_deprecated:
            deprecation_warning = (
                f"API version {version.value} is deprecated. "
                f"Please migrate to {APIVersion.latest().value}"
            )

        super().__init__(
            data=data,
            version=version,
            deprecation_warning=deprecation_warning,
            **kwargs
        )


# Export versioning utilities
__all__ = [
    'APIVersion',
    'VersionInfo',
    'VersionRegistry',
    'VersionedSchema',
    'VersionCompatibilityMixin',
    'VersionNegotiator',
    'VersionedResponse',
]
