"""
Unit tests for availability repositories.

This module provides comprehensive unit tests for availability repository classes:
- VendorAvailabilityRepository: Configuration data access with service filtering
- RecurringAvailabilityRepository: Pattern management with date range filtering
- AvailabilitySlotRepository: Slot management with conflict checking and bulk operations
- AvailabilityExceptionRepository: Exception management with date-specific queries

Implements Task 4.1.2 Phase 6 requirements with >85% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, time, timedelta, timezone
from decimal import Decimal
from typing import List, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.repositories.availability_repository import (
    VendorAvailabilityRepository, RecurringAvailabilityRepository,
    AvailabilitySlotRepository, AvailabilityExceptionRepository
)
from app.models.availability import (
    VendorAvailability, RecurringAvailability, AvailabilitySlot, AvailabilityException
)
from app.repositories.base import RepositoryError


class TestVendorAvailabilityRepository:
    """Test cases for VendorAvailabilityRepository."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session

    @pytest.fixture
    def vendor_availability_repo(self, mock_db_session):
        """Create VendorAvailabilityRepository instance."""
        return VendorAvailabilityRepository(mock_db_session)

    @pytest.fixture
    def sample_vendor_availability(self):
        """Sample vendor availability instance."""
        availability = MagicMock(spec=VendorAvailability)
        availability.id = 1
        availability.vendor_id = 123
        availability.service_id = None
        availability.timezone = "America/New_York"
        availability.advance_booking_days = 90
        availability.min_booking_notice_hours = 24
        availability.max_booking_notice_days = 30
        availability.is_active = True
        return availability

    @pytest.mark.asyncio
    async def test_get_by_vendor_and_service_success(
        self, vendor_availability_repo, mock_db_session, sample_vendor_availability
    ):
        """Test successful vendor availability retrieval by vendor and service."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_vendor_availability
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await vendor_availability_repo.get_by_vendor_and_service(
            vendor_id=123,
            service_id=None
        )

        # Assertions
        assert result == sample_vendor_availability
        assert result.vendor_id == 123
        assert result.service_id is None
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_vendor_and_service_not_found(
        self, vendor_availability_repo, mock_db_session
    ):
        """Test vendor availability not found."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await vendor_availability_repo.get_by_vendor_and_service(
            vendor_id=999,
            service_id=None
        )

        # Assertions
        assert result is None
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vendor_availabilities_with_patterns(
        self, vendor_availability_repo, mock_db_session, sample_vendor_availability
    ):
        """Test getting vendor availabilities with related patterns."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_vendor_availability]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await vendor_availability_repo.get_vendor_availabilities_with_patterns(
            vendor_id=123
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_vendor_availability
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_vendor_availability_success(
        self, vendor_availability_repo, mock_db_session, sample_vendor_availability
    ):
        """Test successful vendor availability update."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_vendor_availability
        mock_db_session.execute.return_value = mock_result

        update_data = {
            "advance_booking_days": 120,
            "min_booking_notice_hours": 48
        }

        # Execute method
        result = await vendor_availability_repo.update_vendor_availability(
            availability_id=1,
            update_data=update_data
        )

        # Assertions
        assert result == sample_vendor_availability
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_vendor_availability_error(
        self, vendor_availability_repo, mock_db_session
    ):
        """Test vendor availability update error handling."""
        # Mock database error
        mock_db_session.execute.side_effect = IntegrityError("", "", "")

        update_data = {"advance_booking_days": 120}

        # Execute method and expect error
        with pytest.raises(RepositoryError):
            await vendor_availability_repo.update_vendor_availability(
                availability_id=1,
                update_data=update_data
            )

        mock_db_session.rollback.assert_called_once()


class TestRecurringAvailabilityRepository:
    """Test cases for RecurringAvailabilityRepository."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session

    @pytest.fixture
    def recurring_availability_repo(self, mock_db_session):
        """Create RecurringAvailabilityRepository instance."""
        return RecurringAvailabilityRepository(mock_db_session)

    @pytest.fixture
    def sample_recurring_pattern(self):
        """Sample recurring availability pattern."""
        pattern = MagicMock(spec=RecurringAvailability)
        pattern.id = 1
        pattern.vendor_availability_id = 1
        pattern.pattern_type = "weekly"
        pattern.day_of_week = 1
        pattern.start_time = time(10, 0)
        pattern.end_time = time(16, 0)
        pattern.is_active = True
        pattern.auto_generate = True
        return pattern

    @pytest.mark.asyncio
    async def test_get_active_patterns_for_vendor(
        self, recurring_availability_repo, mock_db_session, sample_recurring_pattern
    ):
        """Test getting active patterns for vendor."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_recurring_pattern]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await recurring_availability_repo.get_active_patterns_for_vendor(
            vendor_availability_id=1
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_recurring_pattern
        assert result[0].is_active is True
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_patterns_with_date_range(
        self, recurring_availability_repo, mock_db_session, sample_recurring_pattern
    ):
        """Test getting active patterns with date range filtering."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_recurring_pattern]
        mock_db_session.execute.return_value = mock_result

        start_date = date.today()
        end_date = date.today() + timedelta(days=30)

        # Execute method
        result = await recurring_availability_repo.get_active_patterns_for_vendor(
            vendor_availability_id=1,
            date_range=(start_date, end_date)
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_recurring_pattern
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_patterns_for_generation(
        self, recurring_availability_repo, mock_db_session, sample_recurring_pattern
    ):
        """Test getting patterns for slot generation."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_recurring_pattern]
        mock_db_session.execute.return_value = mock_result

        target_date = date.today() + timedelta(days=1)

        # Execute method
        result = await recurring_availability_repo.get_patterns_for_generation(
            vendor_availability_id=1,
            target_date=target_date
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_recurring_pattern
        assert result[0].auto_generate is True
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_last_generated_date(
        self, recurring_availability_repo, mock_db_session
    ):
        """Test updating last generated date."""
        generated_date = date.today()

        # Execute method
        await recurring_availability_repo.update_last_generated_date(
            pattern_id=1,
            generated_date=generated_date
        )

        # Assertions
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()


class TestAvailabilitySlotRepository:
    """Test cases for AvailabilitySlotRepository."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session

    @pytest.fixture
    def slot_repo(self, mock_db_session):
        """Create AvailabilitySlotRepository instance."""
        return AvailabilitySlotRepository(mock_db_session)

    @pytest.fixture
    def sample_availability_slot(self):
        """Sample availability slot."""
        slot = MagicMock(spec=AvailabilitySlot)
        slot.id = 1
        slot.vendor_availability_id = 1
        slot.date = date.today() + timedelta(days=1)
        slot.start_time = time(10, 0)
        slot.end_time = time(11, 0)
        slot.max_bookings = 2
        slot.current_bookings = 0
        slot.is_available = True
        return slot

    @pytest.mark.asyncio
    async def test_get_slots_by_date_range(
        self, slot_repo, mock_db_session, sample_availability_slot
    ):
        """Test getting slots by date range."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_availability_slot]
        mock_db_session.execute.return_value = mock_result

        start_date = date.today()
        end_date = date.today() + timedelta(days=7)

        # Execute method
        result = await slot_repo.get_slots_by_date_range(
            vendor_availability_id=1,
            start_date=start_date,
            end_date=end_date
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_availability_slot
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_slot_availability_available(
        self, slot_repo, mock_db_session, sample_availability_slot
    ):
        """Test slot availability check - available."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_availability_slot]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        is_available, conflicting_slots = await slot_repo.check_slot_availability(
            vendor_availability_id=1,
            target_date=date.today() + timedelta(days=1),
            start_time=time(10, 0),
            end_time=time(11, 0),
            required_capacity=1
        )

        # Assertions
        assert is_available is True
        assert len(conflicting_slots) == 0
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_slot_availability_conflict(
        self, slot_repo, mock_db_session, sample_availability_slot
    ):
        """Test slot availability check - conflict."""
        # Set slot to full capacity
        sample_availability_slot.current_bookings = 2
        sample_availability_slot.max_bookings = 2

        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_availability_slot]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        is_available, conflicting_slots = await slot_repo.check_slot_availability(
            vendor_availability_id=1,
            target_date=date.today() + timedelta(days=1),
            start_time=time(10, 0),
            end_time=time(11, 0),
            required_capacity=1
        )

        # Assertions
        assert is_available is False
        assert len(conflicting_slots) == 1
        assert conflicting_slots[0] == sample_availability_slot
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_slot_booking_count_increment(
        self, slot_repo, mock_db_session, sample_availability_slot
    ):
        """Test incrementing slot booking count."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_availability_slot
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await slot_repo.update_slot_booking_count(
            slot_id=1,
            increment=True,
            count=1
        )

        # Assertions
        assert result == sample_availability_slot
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_slot_booking_count_decrement(
        self, slot_repo, mock_db_session, sample_availability_slot
    ):
        """Test decrementing slot booking count."""
        # Set initial booking count
        sample_availability_slot.current_bookings = 1

        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_availability_slot
        mock_db_session.execute.return_value = mock_result

        # Execute method
        result = await slot_repo.update_slot_booking_count(
            slot_id=1,
            increment=False,
            count=1
        )

        # Assertions
        assert result == sample_availability_slot
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_create_slots_success(
        self, slot_repo, mock_db_session
    ):
        """Test successful bulk slot creation."""
        slots_data = [
            {
                "vendor_availability_id": 1,
                "date": date.today() + timedelta(days=1),
                "start_time": time(10, 0),
                "end_time": time(11, 0),
                "max_bookings": 2,
                "current_bookings": 0,
                "is_available": True
            },
            {
                "vendor_availability_id": 1,
                "date": date.today() + timedelta(days=1),
                "start_time": time(11, 0),
                "end_time": time(12, 0),
                "max_bookings": 2,
                "current_bookings": 0,
                "is_available": True
            }
        ]

        # Mock database response
        mock_result = MagicMock()
        mock_result.fetchall.return_value = [(1,), (2,)]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        created_count, created_ids = await slot_repo.bulk_create_slots(
            slots_data=slots_data,
            batch_size=1000
        )

        # Assertions
        assert created_count == 2
        assert created_ids == [1, 2]
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_create_slots_error(
        self, slot_repo, mock_db_session
    ):
        """Test bulk slot creation error handling."""
        slots_data = [{"invalid": "data"}]

        # Mock database error
        mock_db_session.execute.side_effect = IntegrityError("", "", "")

        # Execute method and expect error
        with pytest.raises(RepositoryError):
            await slot_repo.bulk_create_slots(
                slots_data=slots_data,
                batch_size=1000
            )

        mock_db_session.rollback.assert_called_once()


class TestAvailabilityExceptionRepository:
    """Test cases for AvailabilityExceptionRepository."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session

    @pytest.fixture
    def exception_repo(self, mock_db_session):
        """Create AvailabilityExceptionRepository instance."""
        return AvailabilityExceptionRepository(mock_db_session)

    @pytest.fixture
    def sample_exception(self):
        """Sample availability exception."""
        exception = MagicMock(spec=AvailabilityException)
        exception.id = 1
        exception.vendor_availability_id = 1
        exception.recurring_availability_id = 1
        exception.exception_date = date.today() + timedelta(days=7)
        exception.exception_type = "unavailable"
        exception.reason = "Vendor holiday"
        exception.is_active = True
        return exception

    @pytest.mark.asyncio
    async def test_get_exceptions_for_date_range(
        self, exception_repo, mock_db_session, sample_exception
    ):
        """Test getting exceptions for date range."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_exception]
        mock_db_session.execute.return_value = mock_result

        start_date = date.today()
        end_date = date.today() + timedelta(days=30)

        # Execute method
        result = await exception_repo.get_exceptions_for_date_range(
            vendor_availability_id=1,
            start_date=start_date,
            end_date=end_date
        )

        # Assertions
        assert len(result) == 1
        assert result[0] == sample_exception
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_exception_for_date(
        self, exception_repo, mock_db_session, sample_exception
    ):
        """Test getting exception for specific date."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_exception
        mock_db_session.execute.return_value = mock_result

        exception_date = date.today() + timedelta(days=7)

        # Execute method
        result = await exception_repo.get_exception_for_date(
            vendor_availability_id=1,
            exception_date=exception_date
        )

        # Assertions
        assert result == sample_exception
        assert result.exception_date == exception_date
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_date_has_exception_true(
        self, exception_repo, mock_db_session
    ):
        """Test checking if date has exception - true."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar.return_value = 1
        mock_db_session.execute.return_value = mock_result

        check_date = date.today() + timedelta(days=7)

        # Execute method
        result = await exception_repo.check_date_has_exception(
            vendor_availability_id=1,
            check_date=check_date
        )

        # Assertions
        assert result is True
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_date_has_exception_false(
        self, exception_repo, mock_db_session
    ):
        """Test checking if date has exception - false."""
        # Mock database response
        mock_result = MagicMock()
        mock_result.scalar.return_value = 0
        mock_db_session.execute.return_value = mock_result

        check_date = date.today() + timedelta(days=7)

        # Execute method
        result = await exception_repo.check_date_has_exception(
            vendor_availability_id=1,
            check_date=check_date
        )

        # Assertions
        assert result is False
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_create_exceptions_success(
        self, exception_repo, mock_db_session
    ):
        """Test successful bulk exception creation."""
        exceptions_data = [
            {
                "vendor_availability_id": 1,
                "recurring_availability_id": 1,
                "exception_date": date.today() + timedelta(days=7),
                "exception_type": "unavailable",
                "reason": "Holiday"
            },
            {
                "vendor_availability_id": 1,
                "recurring_availability_id": 1,
                "exception_date": date.today() + timedelta(days=14),
                "exception_type": "unavailable",
                "reason": "Maintenance"
            }
        ]

        # Mock database response
        mock_result = MagicMock()
        mock_result.fetchall.return_value = [(1,), (2,)]
        mock_db_session.execute.return_value = mock_result

        # Execute method
        created_count, created_ids = await exception_repo.bulk_create_exceptions(
            exceptions_data=exceptions_data,
            batch_size=500
        )

        # Assertions
        assert created_count == 2
        assert created_ids == [1, 2]
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()
