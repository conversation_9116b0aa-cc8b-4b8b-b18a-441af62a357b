"""
Service Availability Service for Culture Connect Backend API.

This module provides comprehensive service availability management functionality including:
- Availability calendar management and time slot configuration
- Recurring availability patterns and schedule management
- Booking conflict prevention and availability checking
- Real-time availability updates and synchronization
- Integration with booking system and service listings

Implements Task 3.2.1 requirements for availability management service with
production-grade calendar logic, conflict resolution, and booking integration.
"""

import logging
from datetime import datetime, timezone, date, time, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import correlation_id
from app.services.base import BaseService, NotFoundError, ValidationError, ServiceError, ConflictError
from app.models.service import ServiceAvailability, AvailabilityType, Service
from app.repositories.service_repository import ServiceAvailabilityRepository, ServiceRepository
from app.schemas.service_extended import (
    ServiceAvailabilityCreate, ServiceAvailabilityUpdate, ServiceAvailabilityResponse
)

logger = logging.getLogger(__name__)


class ServiceAvailabilityService(BaseService[ServiceAvailability, ServiceAvailabilityRepository]):
    """
    Service availability service for comprehensive availability management operations.

    Provides business logic for:
    - Availability calendar management
    - Time slot configuration and validation
    - Recurring availability patterns
    - Booking conflict prevention
    - Real-time availability updates
    """

    def __init__(self, db: AsyncSession):
        """Initialize service availability service."""
        super().__init__(ServiceAvailabilityRepository, ServiceAvailability, db_session=db)
        self.service_repository = ServiceRepository(db)

    async def create_availability(
        self,
        service_id: int,
        vendor_id: int,
        availability_data: ServiceAvailabilityCreate
    ) -> ServiceAvailabilityResponse:
        """
        Create a new availability slot for a service.

        Args:
            service_id: ID of the service
            vendor_id: ID of the vendor (for ownership verification)
            availability_data: Availability creation data

        Returns:
            ServiceAvailabilityResponse: Created availability slot

        Raises:
            NotFoundError: If service not found
            ValidationError: If availability data is invalid
            ConflictError: If availability conflicts with existing slots
        """
        correlation = self.log_operation_start(
            "create_availability",
            service_id=service_id,
            vendor_id=vendor_id,
            date=availability_data.date,
            start_time=availability_data.start_time
        )

        try:
            # Verify service exists and ownership
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            if service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Validate availability data
            await self._validate_availability_data(service, availability_data)

            # Check for conflicts
            await self._check_availability_conflicts(service_id, availability_data)

            # Create availability
            availability_dict = availability_data.model_dump(exclude_unset=True)
            availability_dict.update({
                "service_id": service_id,
                "current_bookings": 0,
                "is_available": True,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })

            availability = await self.repository.create(availability_dict)

            response = ServiceAvailabilityResponse.model_validate(availability)

            self.log_operation_success(
                correlation,
                f"Availability created for service {service_id}: {availability_data.date} {availability_data.start_time}"
            )

            return response

        except (NotFoundError, ValidationError, ConflictError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "create_availability",
                {"service_id": service_id, "vendor_id": vendor_id}
            )

    async def update_availability(
        self,
        availability_id: int,
        vendor_id: int,
        availability_data: ServiceAvailabilityUpdate
    ) -> ServiceAvailabilityResponse:
        """
        Update an existing availability slot.

        Args:
            availability_id: ID of the availability to update
            vendor_id: ID of the vendor (for ownership verification)
            availability_data: Availability update data

        Returns:
            ServiceAvailabilityResponse: Updated availability slot

        Raises:
            NotFoundError: If availability not found
            ValidationError: If update data is invalid
        """
        correlation = self.log_operation_start(
            "update_availability",
            availability_id=availability_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing availability
            availability = await self.repository.get_by_id(availability_id)
            if not availability:
                raise NotFoundError(f"Availability with ID {availability_id} not found")

            # Verify ownership through service
            service = await self.service_repository.get_by_id(availability.service_id)
            if not service or service.vendor_id != vendor_id:
                raise ValidationError("Availability does not belong to this vendor")

            # Validate update data
            await self._validate_availability_update(availability, availability_data)

            # Update availability
            update_dict = availability_data.model_dump(exclude_unset=True)
            update_dict["updated_at"] = datetime.now(timezone.utc)

            updated_availability = await self.repository.update(availability_id, update_dict)

            response = ServiceAvailabilityResponse.model_validate(updated_availability)

            self.log_operation_success(
                correlation,
                f"Availability updated: {availability.date} {availability.start_time} (ID: {availability_id})"
            )

            return response

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "update_availability",
                {"availability_id": availability_id, "vendor_id": vendor_id}
            )

    async def delete_availability(
        self,
        availability_id: int,
        vendor_id: int
    ) -> bool:
        """
        Delete an availability slot.

        Args:
            availability_id: ID of the availability to delete
            vendor_id: ID of the vendor (for ownership verification)

        Returns:
            bool: True if deletion successful

        Raises:
            NotFoundError: If availability not found
            ValidationError: If availability has active bookings
        """
        correlation = self.log_operation_start(
            "delete_availability",
            availability_id=availability_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing availability
            availability = await self.repository.get_by_id(availability_id)
            if not availability:
                raise NotFoundError(f"Availability with ID {availability_id} not found")

            # Verify ownership through service
            service = await self.service_repository.get_by_id(availability.service_id)
            if not service or service.vendor_id != vendor_id:
                raise ValidationError("Availability does not belong to this vendor")

            # Check for active bookings
            if availability.current_bookings > 0:
                raise ValidationError("Cannot delete availability slot with active bookings")

            # Delete availability
            await self.repository.delete(availability_id)

            self.log_operation_success(
                correlation,
                f"Availability deleted: {availability.date} {availability.start_time} (ID: {availability_id})"
            )

            return True

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "delete_availability",
                {"availability_id": availability_id, "vendor_id": vendor_id}
            )

    async def get_service_availability(
        self,
        service_id: int,
        start_date: date,
        end_date: date,
        available_only: bool = True
    ) -> List[ServiceAvailabilityResponse]:
        """
        Get availability for a service within a date range.

        Args:
            service_id: ID of the service
            start_date: Start date for availability search
            end_date: End date for availability search
            available_only: Whether to return only available slots

        Returns:
            List[ServiceAvailabilityResponse]: List of availability slots

        Raises:
            NotFoundError: If service not found
            ValidationError: If date range is invalid
        """
        correlation = self.log_operation_start(
            "get_service_availability",
            service_id=service_id,
            start_date=start_date,
            end_date=end_date,
            available_only=available_only
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Validate date range
            if start_date > end_date:
                raise ValidationError("Start date cannot be after end date")

            # Get availability
            availability_slots = await self.repository.get_by_date_range(
                service_id, start_date, end_date, available_only
            )

            responses = [
                ServiceAvailabilityResponse.model_validate(slot)
                for slot in availability_slots
            ]

            self.log_operation_success(
                correlation,
                f"Retrieved {len(responses)} availability slots for service {service_id}"
            )

            return responses

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_service_availability",
                {"service_id": service_id, "start_date": start_date, "end_date": end_date}
            )

    async def check_availability(
        self,
        service_id: int,
        date: date,
        start_time: time,
        participants: int = 1
    ) -> Tuple[bool, Optional[ServiceAvailabilityResponse]]:
        """
        Check if a specific time slot is available for booking.

        Args:
            service_id: ID of the service
            date: Date to check
            start_time: Start time to check
            participants: Number of participants

        Returns:
            Tuple[bool, Optional[ServiceAvailabilityResponse]]: 
                (is_available, availability_slot)

        Raises:
            NotFoundError: If service not found
        """
        correlation = self.log_operation_start(
            "check_availability",
            service_id=service_id,
            date=date,
            start_time=start_time,
            participants=participants
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Find matching availability slot
            availability = await self.repository.get_by_date_time(service_id, date, start_time)

            if not availability:
                self.log_operation_success(correlation, "No availability slot found")
                return False, None

            # Check if slot is available
            is_available = (
                availability.is_available and
                availability.current_bookings + participants <= availability.max_capacity
            )

            response = ServiceAvailabilityResponse.model_validate(availability) if availability else None

            self.log_operation_success(
                correlation,
                f"Availability check result: {is_available} for {participants} participants"
            )

            return is_available, response

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "check_availability",
                {"service_id": service_id, "date": date, "start_time": start_time}
            )

    async def create_recurring_availability(
        self,
        service_id: int,
        vendor_id: int,
        start_date: date,
        end_date: date,
        days_of_week: List[int],  # 0=Monday, 6=Sunday
        time_slots: List[Tuple[time, time]],  # (start_time, end_time)
        max_capacity: int
    ) -> List[ServiceAvailabilityResponse]:
        """
        Create recurring availability slots.

        Args:
            service_id: ID of the service
            vendor_id: ID of the vendor (for ownership verification)
            start_date: Start date for recurring pattern
            end_date: End date for recurring pattern
            days_of_week: List of weekdays (0=Monday, 6=Sunday)
            time_slots: List of (start_time, end_time) tuples
            max_capacity: Maximum capacity for each slot

        Returns:
            List[ServiceAvailabilityResponse]: Created availability slots

        Raises:
            NotFoundError: If service not found
            ValidationError: If recurring pattern is invalid
        """
        correlation = self.log_operation_start(
            "create_recurring_availability",
            service_id=service_id,
            vendor_id=vendor_id,
            start_date=start_date,
            end_date=end_date,
            days_count=len(days_of_week),
            slots_count=len(time_slots)
        )

        try:
            # Verify service exists and ownership
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            if service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Validate recurring pattern
            await self._validate_recurring_pattern(
                start_date, end_date, days_of_week, time_slots
            )

            created_slots = []
            current_date = start_date

            while current_date <= end_date:
                # Check if current date matches any of the specified weekdays
                if current_date.weekday() in days_of_week:
                    # Create slots for each time slot on this date
                    for start_time, end_time in time_slots:
                        try:
                            availability_data = ServiceAvailabilityCreate(
                                date=current_date,
                                start_time=start_time,
                                end_time=end_time,
                                max_capacity=max_capacity,
                                availability_type=AvailabilityType.RECURRING
                            )

                            # Check for conflicts before creating
                            conflicts = await self._check_availability_conflicts(
                                service_id, availability_data, raise_on_conflict=False
                            )

                            if not conflicts:
                                slot = await self.create_availability(
                                    service_id, vendor_id, availability_data
                                )
                                created_slots.append(slot)

                        except Exception as e:
                            logger.warning(
                                f"Failed to create availability slot for {current_date} {start_time}: {str(e)}"
                            )

                current_date += timedelta(days=1)

            self.log_operation_success(
                correlation,
                f"Created {len(created_slots)} recurring availability slots"
            )

            return created_slots

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "create_recurring_availability",
                {"service_id": service_id, "vendor_id": vendor_id}
            )

    async def _validate_availability_data(
        self,
        service: Service,
        availability_data: ServiceAvailabilityCreate
    ) -> None:
        """Validate availability creation data."""
        # Check date is not in the past
        if availability_data.date < date.today():
            raise ValidationError("Cannot create availability for past dates")

        # Check time range
        if availability_data.end_time <= availability_data.start_time:
            raise ValidationError("End time must be after start time")

        # Check capacity
        if availability_data.max_capacity <= 0:
            raise ValidationError("Maximum capacity must be greater than 0")

        # Check against service constraints
        if service.max_participants and availability_data.max_capacity > service.max_participants:
            raise ValidationError(f"Capacity cannot exceed service maximum ({service.max_participants})")

    async def _validate_availability_update(
        self,
        availability: ServiceAvailability,
        update_data: ServiceAvailabilityUpdate
    ) -> None:
        """Validate availability update data."""
        # Check if reducing capacity below current bookings
        if hasattr(update_data, 'max_capacity') and update_data.max_capacity:
            if update_data.max_capacity < availability.current_bookings:
                raise ValidationError("Cannot reduce capacity below current bookings")

    async def _check_availability_conflicts(
        self,
        service_id: int,
        availability_data: ServiceAvailabilityCreate,
        raise_on_conflict: bool = True
    ) -> bool:
        """Check for availability conflicts."""
        existing = await self.repository.get_by_date_time(
            service_id, availability_data.date, availability_data.start_time
        )

        if existing:
            if raise_on_conflict:
                raise ConflictError(
                    f"Availability already exists for {availability_data.date} at {availability_data.start_time}"
                )
            return True

        return False

    async def _validate_recurring_pattern(
        self,
        start_date: date,
        end_date: date,
        days_of_week: List[int],
        time_slots: List[Tuple[time, time]]
    ) -> None:
        """Validate recurring availability pattern."""
        # Check date range
        if start_date > end_date:
            raise ValidationError("Start date cannot be after end date")

        # Check days of week
        if not days_of_week or not all(0 <= day <= 6 for day in days_of_week):
            raise ValidationError("Invalid days of week (must be 0-6)")

        # Check time slots
        if not time_slots:
            raise ValidationError("At least one time slot is required")

        for start_time, end_time in time_slots:
            if end_time <= start_time:
                raise ValidationError(f"Invalid time slot: {start_time} - {end_time}")

        # Check for overlapping time slots
        sorted_slots = sorted(time_slots)
        for i in range(len(sorted_slots) - 1):
            if sorted_slots[i][1] > sorted_slots[i + 1][0]:
                raise ValidationError("Time slots cannot overlap")
