"""
Unit tests for task service functionality.

This module provides comprehensive testing for:
- Task submission and execution management
- Task status monitoring and cancellation
- Bulk task operations
- System metrics and health monitoring
- Error handling and retry logic

Implements Task 6.2.1 testing requirements with >85% code coverage
and 100% test success rate validation.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4, UUID

from app.services.task_service import TaskService
from app.models.task_models import TaskExecution, TaskStatus, TaskPriority
from app.schemas.task_schemas import (
    TaskSubmissionRequest, TaskSubmissionResponse, TaskStatusResponse,
    TaskCancellationRequest, BulkTaskSubmissionRequest, SystemMetricsResponse
)


class TestTaskService:
    """Test cases for TaskService."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        session = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.execute = AsyncMock()
        return session

    @pytest.fixture
    def task_service(self, mock_session):
        """Create TaskService instance with mocked session."""
        return TaskService(mock_session)

    @pytest.fixture
    def sample_task_request(self):
        """Sample task submission request."""
        return TaskSubmissionRequest(
            task_name="test.sample_task",
            args=["arg1", "arg2"],
            kwargs={"key1": "value1"},
            priority=TaskPriority.STANDARD,
            max_retries=3
        )

    @pytest.fixture
    def sample_user_id(self):
        """Sample user ID."""
        return uuid4()

    @pytest.mark.asyncio
    async def test_submit_task_success(self, task_service, sample_task_request, sample_user_id, mock_session):
        """Test successful task submission."""
        # Mock Celery task
        mock_celery_task = Mock()
        mock_celery_task.id = "test-task-id-123"
        
        with patch.object(task_service.celery_app, 'send_task', return_value=mock_celery_task):
            # Submit task
            response = await task_service.submit_task(
                request=sample_task_request,
                user_id=sample_user_id
            )
            
            # Verify response
            assert isinstance(response, TaskSubmissionResponse)
            assert response.task_id == "test-task-id-123"
            assert response.task_name == "test.sample_task"
            assert response.priority == TaskPriority.STANDARD
            assert response.status == TaskStatus.PENDING
            
            # Verify database operations
            mock_session.add.assert_called_once()
            mock_session.commit.assert_called_once()
            
            # Verify Celery task submission
            task_service.celery_app.send_task.assert_called_once_with(
                "test.sample_task",
                args=["arg1", "arg2"],
                kwargs={"key1": "value1"},
                queue="default",
                eta=None,
                countdown=None,
                retry=True,
                retry_policy={
                    'max_retries': 3,
                    'interval_start': 0,
                    'interval_step': 0.2,
                    'interval_max': 2.0,
                }
            )

    @pytest.mark.asyncio
    async def test_submit_task_with_custom_queue(self, task_service, sample_user_id, mock_session):
        """Test task submission with custom queue."""
        request = TaskSubmissionRequest(
            task_name="test.custom_task",
            queue_name="custom_queue",
            priority=TaskPriority.HIGH
        )
        
        mock_celery_task = Mock()
        mock_celery_task.id = "custom-task-id"
        
        with patch.object(task_service.celery_app, 'send_task', return_value=mock_celery_task):
            response = await task_service.submit_task(request, sample_user_id)
            
            assert response.queue_name == "custom_queue"
            
            # Verify queue was used
            task_service.celery_app.send_task.assert_called_once()
            call_kwargs = task_service.celery_app.send_task.call_args[1]
            assert call_kwargs['queue'] == "custom_queue"

    @pytest.mark.asyncio
    async def test_submit_task_priority_queue_mapping(self, task_service, sample_user_id, mock_session):
        """Test priority-based queue mapping."""
        test_cases = [
            (TaskPriority.CRITICAL, "critical"),
            (TaskPriority.HIGH, "high_priority"),
            (TaskPriority.STANDARD, "default"),
            (TaskPriority.LOW, "low_priority")
        ]
        
        for priority, expected_queue in test_cases:
            request = TaskSubmissionRequest(
                task_name="test.priority_task",
                priority=priority
            )
            
            mock_celery_task = Mock()
            mock_celery_task.id = f"task-{priority.value}"
            
            with patch.object(task_service.celery_app, 'send_task', return_value=mock_celery_task):
                response = await task_service.submit_task(request, sample_user_id)
                
                assert response.queue_name == expected_queue
                
                # Verify correct queue was used
                call_kwargs = task_service.celery_app.send_task.call_args[1]
                assert call_kwargs['queue'] == expected_queue

    @pytest.mark.asyncio
    async def test_get_task_status_success(self, task_service, mock_session):
        """Test successful task status retrieval."""
        task_id = "test-task-123"
        
        # Mock database result
        mock_task_execution = TaskExecution(
            id=uuid4(),
            task_id=task_id,
            task_name="test.task",
            queue_name="default",
            status=TaskStatus.SUCCESS.value,
            submitted_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc),
            execution_time_ms=1500.0,
            retry_count=0
        )
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_task_execution
        mock_session.execute.return_value = mock_result
        
        # Mock Celery result
        mock_celery_result = Mock()
        mock_celery_result.status = TaskStatus.SUCCESS.value
        mock_celery_result.successful.return_value = True
        mock_celery_result.result = {"output": "success"}
        
        with patch('app.services.task_service.AsyncResult', return_value=mock_celery_result):
            response = await task_service.get_task_status(
                task_id=task_id,
                include_result=True
            )
            
            assert isinstance(response, TaskStatusResponse)
            assert response.task_id == task_id
            assert response.task_name == "test.task"
            assert response.status == TaskStatus.SUCCESS
            assert response.execution_time_ms == 1500.0
            assert response.retry_count == 0
            assert response.result == {"output": "success"}

    @pytest.mark.asyncio
    async def test_get_task_status_not_found(self, task_service, mock_session):
        """Test task status retrieval for non-existent task."""
        task_id = "non-existent-task"
        
        # Mock database result - no task found
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        with pytest.raises(ValueError, match="Task not found"):
            await task_service.get_task_status(task_id)

    @pytest.mark.asyncio
    async def test_cancel_task_success(self, task_service, mock_session):
        """Test successful task cancellation."""
        task_id = "test-task-cancel"
        
        request = TaskCancellationRequest(
            task_id=task_id,
            terminate=True,
            reason="User requested cancellation"
        )
        
        # Mock database result
        mock_task_execution = TaskExecution(
            id=uuid4(),
            task_id=task_id,
            task_name="test.task",
            queue_name="default",
            status=TaskStatus.STARTED.value
        )
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_task_execution
        mock_session.execute.return_value = mock_result
        
        # Mock Celery control
        mock_control = Mock()
        mock_control.revoke = Mock()
        task_service.celery_app.control = mock_control
        
        response = await task_service.cancel_task(request, uuid4())
        
        assert response.task_id == task_id
        assert response.status == "cancelled"
        assert "User requested cancellation" in response.message
        
        # Verify Celery revoke was called
        mock_control.revoke.assert_called_once_with(
            task_id,
            terminate=True,
            signal='SIGKILL'
        )
        
        # Verify database update
        assert mock_task_execution.status == TaskStatus.REVOKED.value
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_submit_bulk_tasks_success(self, task_service, sample_user_id, mock_session):
        """Test successful bulk task submission."""
        tasks = [
            TaskSubmissionRequest(task_name="test.task1", priority=TaskPriority.HIGH),
            TaskSubmissionRequest(task_name="test.task2", priority=TaskPriority.STANDARD),
            TaskSubmissionRequest(task_name="test.task3", priority=TaskPriority.LOW)
        ]
        
        request = BulkTaskSubmissionRequest(
            tasks=tasks,
            batch_name="test_batch",
            fail_fast=False
        )
        
        # Mock successful task submissions
        mock_responses = []
        for i, task in enumerate(tasks):
            mock_celery_task = Mock()
            mock_celery_task.id = f"bulk-task-{i}"
            
            mock_response = TaskSubmissionResponse(
                id=uuid4(),
                task_id=f"bulk-task-{i}",
                task_name=task.task_name,
                queue_name="default",
                priority=task.priority,
                status=TaskStatus.PENDING,
                submitted_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            mock_responses.append(mock_response)
        
        with patch.object(task_service, 'submit_task', side_effect=mock_responses):
            response = await task_service.submit_bulk_tasks(request, sample_user_id)
            
            assert response.total_tasks == 3
            assert response.successful_submissions == 3
            assert response.failed_submissions == 0
            assert len(response.task_ids) == 3
            assert len(response.errors) == 0

    @pytest.mark.asyncio
    async def test_submit_bulk_tasks_with_failures(self, task_service, sample_user_id, mock_session):
        """Test bulk task submission with some failures."""
        tasks = [
            TaskSubmissionRequest(task_name="test.task1"),
            TaskSubmissionRequest(task_name="test.task2"),
            TaskSubmissionRequest(task_name="test.task3")
        ]
        
        request = BulkTaskSubmissionRequest(
            tasks=tasks,
            fail_fast=False
        )
        
        # Mock mixed success/failure responses
        def mock_submit_side_effect(task_req, user_id):
            if task_req.task_name == "test.task2":
                raise Exception("Task submission failed")
            
            return TaskSubmissionResponse(
                id=uuid4(),
                task_id=f"task-{task_req.task_name}",
                task_name=task_req.task_name,
                queue_name="default",
                priority=task_req.priority,
                status=TaskStatus.PENDING,
                submitted_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        
        with patch.object(task_service, 'submit_task', side_effect=mock_submit_side_effect):
            response = await task_service.submit_bulk_tasks(request, sample_user_id)
            
            assert response.total_tasks == 3
            assert response.successful_submissions == 2
            assert response.failed_submissions == 1
            assert len(response.task_ids) == 2
            assert len(response.errors) == 1
            assert response.errors[0]["task_name"] == "test.task2"

    @pytest.mark.asyncio
    async def test_get_system_metrics_success(self, task_service, mock_session):
        """Test successful system metrics retrieval."""
        # Mock database statistics
        mock_stats = Mock()
        mock_stats.total = 100
        mock_stats.successful = 85
        mock_stats.failed = 15
        mock_stats.avg_execution_time = 2500.0
        
        mock_stats_result = Mock()
        mock_stats_result.first.return_value = mock_stats
        mock_session.execute.return_value = mock_stats_result
        
        # Mock queue count
        mock_queue_count = Mock()
        mock_queue_count.scalar.return_value = 5
        
        # Mock health status
        mock_health = {
            "broker_connected": True,
            "active_workers": 3
        }
        
        with patch('app.services.task_service.check_celery_health', return_value=mock_health):
            # Mock multiple execute calls
            mock_session.execute.side_effect = [mock_stats_result, mock_queue_count]
            
            response = await task_service.get_system_metrics()
            
            assert isinstance(response, SystemMetricsResponse)
            assert response.total_tasks_today == 100
            assert response.successful_tasks_today == 85
            assert response.failed_tasks_today == 15
            assert response.overall_success_rate == 85.0
            assert response.average_execution_time_ms == 2500.0
            assert response.total_queues == 5
            assert response.active_workers == 3
            assert 0 <= response.health_score <= 100

    @pytest.mark.asyncio
    async def test_health_score_calculation(self, task_service):
        """Test health score calculation logic."""
        # Test perfect health
        score = task_service._calculate_health_score(
            success_rate=100.0,
            broker_connected=True,
            active_workers=10
        )
        assert score == 100.0
        
        # Test poor health
        score = task_service._calculate_health_score(
            success_rate=50.0,
            broker_connected=False,
            active_workers=0
        )
        assert score == 20.0  # Only 50% success rate contributes
        
        # Test partial health
        score = task_service._calculate_health_score(
            success_rate=80.0,
            broker_connected=True,
            active_workers=2
        )
        assert score == 82.0  # 32 + 30 + 10 (capped at 30 for workers)

    @pytest.mark.asyncio
    async def test_default_queue_mapping(self, task_service):
        """Test default queue mapping based on priority."""
        assert task_service._get_default_queue(TaskPriority.CRITICAL) == "critical"
        assert task_service._get_default_queue(TaskPriority.HIGH) == "high_priority"
        assert task_service._get_default_queue(TaskPriority.STANDARD) == "default"
        assert task_service._get_default_queue(TaskPriority.LOW) == "low_priority"

    @pytest.mark.asyncio
    async def test_task_submission_error_handling(self, task_service, sample_task_request, sample_user_id):
        """Test error handling during task submission."""
        # Mock Celery error
        with patch.object(task_service.celery_app, 'send_task', side_effect=Exception("Celery connection failed")):
            with pytest.raises(Exception, match="Celery connection failed"):
                await task_service.submit_task(sample_task_request, sample_user_id)

    @pytest.mark.asyncio
    async def test_task_status_update_on_retrieval(self, task_service, mock_session):
        """Test task status update when retrieving status."""
        task_id = "test-task-update"
        
        # Mock database result with outdated status
        mock_task_execution = TaskExecution(
            id=uuid4(),
            task_id=task_id,
            task_name="test.task",
            queue_name="default",
            status=TaskStatus.STARTED.value,  # Outdated status
            submitted_at=datetime.now(timezone.utc)
        )
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_task_execution
        mock_session.execute.return_value = mock_result
        
        # Mock Celery result with updated status
        mock_celery_result = Mock()
        mock_celery_result.status = TaskStatus.SUCCESS.value  # Updated status
        mock_celery_result.successful.return_value = True
        
        with patch('app.services.task_service.AsyncResult', return_value=mock_celery_result):
            response = await task_service.get_task_status(task_id)
            
            # Verify status was updated
            assert mock_task_execution.status == TaskStatus.SUCCESS.value
            assert response.status == TaskStatus.SUCCESS
            mock_session.commit.assert_called_once()
