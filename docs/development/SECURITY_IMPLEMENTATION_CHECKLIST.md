# Security Implementation Checklist - Culture Connect Backend

## 📋 Overview

This comprehensive security checklist ensures the Culture Connect Backend meets enterprise-grade security standards, protecting user data, financial transactions, and business operations against modern threats.

## 🔐 Authentication & Authorization Security

### 1. Password Security Implementation

#### Password Policy Enforcement
```python
# Password Validation Rules
password_policy = {
    "min_length": 8,
    "max_length": 128,
    "require_uppercase": True,
    "require_lowercase": True,
    "require_digits": True,
    "require_special_chars": True,
    "forbidden_patterns": [
        "password", "123456", "qwerty", "admin",
        "user", "guest", "test", "demo"
    ],
    "max_consecutive_chars": 3,
    "prevent_reuse_count": 5  # Last 5 passwords
}

# Secure Password Hashing
import bcrypt
from passlib.context import CryptContext

pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12  # Increased rounds for better security
)

def hash_password(password: str) -> str:
    """Hash password with bcrypt and salt"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)

# Password Strength Validation
def validate_password_strength(password: str, user_data: dict = None) -> dict:
    """Comprehensive password validation"""
    errors = []
    
    # Length check
    if len(password) < password_policy["min_length"]:
        errors.append(f"Password must be at least {password_policy['min_length']} characters")
    
    # Character requirements
    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain uppercase letters")
    
    if not re.search(r'[a-z]', password):
        errors.append("Password must contain lowercase letters")
    
    if not re.search(r'\d', password):
        errors.append("Password must contain digits")
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        errors.append("Password must contain special characters")
    
    # Check against user data
    if user_data:
        user_info = [user_data.get('email', ''), user_data.get('first_name', ''), 
                    user_data.get('last_name', '')]
        for info in user_info:
            if info and info.lower() in password.lower():
                errors.append("Password cannot contain personal information")
    
    return {"valid": len(errors) == 0, "errors": errors}
```

#### Account Security Measures
```python
# Account Lockout Protection
class AccountLockoutManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.max_attempts = 5
        self.lockout_duration = 900  # 15 minutes
        self.attempt_window = 300    # 5 minutes
    
    async def record_failed_attempt(self, identifier: str):
        """Record failed login attempt"""
        key = f"failed_attempts:{identifier}"
        
        # Increment attempt count
        attempts = await self.redis.incr(key)
        
        # Set expiration on first attempt
        if attempts == 1:
            await self.redis.expire(key, self.attempt_window)
        
        # Lock account if max attempts reached
        if attempts >= self.max_attempts:
            await self.lock_account(identifier)
        
        return attempts
    
    async def lock_account(self, identifier: str):
        """Lock account for security"""
        lockout_key = f"account_locked:{identifier}"
        await self.redis.setex(lockout_key, self.lockout_duration, "locked")
        
        # Send security notification
        await send_security_notification(identifier, "account_locked")
    
    async def is_account_locked(self, identifier: str) -> bool:
        """Check if account is locked"""
        lockout_key = f"account_locked:{identifier}"
        return await self.redis.exists(lockout_key)
    
    async def clear_failed_attempts(self, identifier: str):
        """Clear failed attempts on successful login"""
        key = f"failed_attempts:{identifier}"
        await self.redis.delete(key)

# Multi-Factor Authentication
class MFAManager:
    def __init__(self):
        self.totp_issuer = "CultureConnect"
        self.backup_codes_count = 10
    
    def generate_totp_secret(self) -> str:
        """Generate TOTP secret for user"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, user_email: str, secret: str) -> str:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.totp_issuer
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # Convert to base64 string
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    def verify_totp(self, secret: str, token: str) -> bool:
        """Verify TOTP token"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)  # Allow 30s window
    
    def generate_backup_codes(self) -> List[str]:
        """Generate backup codes for account recovery"""
        codes = []
        for _ in range(self.backup_codes_count):
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            codes.append(f"{code[:4]}-{code[4:]}")
        return codes
```

### 2. JWT Token Security

#### Secure JWT Implementation
```python
# JWT Configuration
jwt_config = {
    "algorithm": "RS256",  # Use RSA instead of HS256
    "access_token_expire_minutes": 15,
    "refresh_token_expire_days": 7,
    "issuer": "cultureconnect.com",
    "audience": ["mobile-app", "web-portal"]
}

# JWT Token Manager
class JWTManager:
    def __init__(self):
        self.private_key = self._load_private_key()
        self.public_key = self._load_public_key()
        self.blacklisted_tokens = set()  # Use Redis in production
    
    def create_access_token(self, user_data: dict) -> str:
        """Create secure access token"""
        now = datetime.utcnow()
        payload = {
            "sub": str(user_data["id"]),
            "email": user_data["email"],
            "role": user_data["role"],
            "iat": now,
            "exp": now + timedelta(minutes=jwt_config["access_token_expire_minutes"]),
            "iss": jwt_config["issuer"],
            "aud": jwt_config["audience"],
            "jti": str(uuid.uuid4()),  # Unique token ID for blacklisting
            "token_type": "access"
        }
        
        return jwt.encode(payload, self.private_key, algorithm=jwt_config["algorithm"])
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create secure refresh token"""
        now = datetime.utcnow()
        payload = {
            "sub": user_id,
            "iat": now,
            "exp": now + timedelta(days=jwt_config["refresh_token_expire_days"]),
            "iss": jwt_config["issuer"],
            "jti": str(uuid.uuid4()),
            "token_type": "refresh"
        }
        
        return jwt.encode(payload, self.private_key, algorithm=jwt_config["algorithm"])
    
    def verify_token(self, token: str) -> dict:
        """Verify and decode JWT token"""
        try:
            # Check if token is blacklisted
            decoded = jwt.decode(
                token, 
                self.public_key, 
                algorithms=[jwt_config["algorithm"]],
                issuer=jwt_config["issuer"],
                audience=jwt_config["audience"]
            )
            
            if decoded["jti"] in self.blacklisted_tokens:
                raise jwt.InvalidTokenError("Token has been revoked")
            
            return decoded
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    def blacklist_token(self, token: str):
        """Add token to blacklist"""
        try:
            decoded = jwt.decode(token, options={"verify_signature": False})
            self.blacklisted_tokens.add(decoded["jti"])
        except:
            pass  # Invalid token format
```

## 🛡️ Input Validation & Sanitization

### 1. Comprehensive Input Validation

#### Pydantic Security Models
```python
# Secure Input Validation
from pydantic import BaseModel, validator, EmailStr, constr
import bleach

class SecureUserInput(BaseModel):
    email: EmailStr
    first_name: constr(min_length=1, max_length=50, regex=r'^[a-zA-Z\s\-\'\.]+$')
    last_name: constr(min_length=1, max_length=50, regex=r'^[a-zA-Z\s\-\'\.]+$')
    phone_number: Optional[constr(regex=r'^\+?[1-9]\d{1,14}$')]  # E.164 format
    
    @validator('first_name', 'last_name')
    def sanitize_name(cls, v):
        """Sanitize name fields"""
        if v:
            # Remove HTML tags and dangerous characters
            sanitized = bleach.clean(v, tags=[], strip=True)
            # Remove excessive whitespace
            sanitized = ' '.join(sanitized.split())
            return sanitized
        return v
    
    @validator('phone_number')
    def validate_phone(cls, v):
        """Validate phone number format"""
        if v:
            # Remove all non-digit characters except +
            cleaned = re.sub(r'[^\d+]', '', v)
            if not re.match(r'^\+?[1-9]\d{1,14}$', cleaned):
                raise ValueError('Invalid phone number format')
            return cleaned
        return v

# SQL Injection Prevention
class SecureQueryBuilder:
    @staticmethod
    def build_search_query(filters: dict) -> tuple:
        """Build parameterized query to prevent SQL injection"""
        where_clauses = []
        params = {}
        
        if filters.get('category'):
            where_clauses.append("category = :category")
            params['category'] = filters['category']
        
        if filters.get('location'):
            where_clauses.append("location ILIKE :location")
            params['location'] = f"%{filters['location']}%"
        
        if filters.get('price_min'):
            where_clauses.append("price >= :price_min")
            params['price_min'] = float(filters['price_min'])
        
        where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
        
        return where_clause, params

# File Upload Security
class SecureFileUpload:
    ALLOWED_EXTENSIONS = {
        'image': {'.jpg', '.jpeg', '.png', '.gif', '.webp'},
        'document': {'.pdf', '.doc', '.docx'},
        'video': {'.mp4', '.avi', '.mov'}
    }
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    @staticmethod
    def validate_file(file: UploadFile, file_type: str) -> dict:
        """Validate uploaded file"""
        errors = []
        
        # Check file size
        if file.size > SecureFileUpload.MAX_FILE_SIZE:
            errors.append("File size exceeds maximum limit")
        
        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        allowed_exts = SecureFileUpload.ALLOWED_EXTENSIONS.get(file_type, set())
        
        if file_ext not in allowed_exts:
            errors.append(f"File type not allowed. Allowed: {', '.join(allowed_exts)}")
        
        # Check MIME type
        if not SecureFileUpload._validate_mime_type(file, file_type):
            errors.append("File content does not match extension")
        
        return {"valid": len(errors) == 0, "errors": errors}
    
    @staticmethod
    def _validate_mime_type(file: UploadFile, file_type: str) -> bool:
        """Validate MIME type matches file extension"""
        mime_mappings = {
            'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'document': ['application/pdf', 'application/msword'],
            'video': ['video/mp4', 'video/avi', 'video/quicktime']
        }
        
        allowed_mimes = mime_mappings.get(file_type, [])
        return file.content_type in allowed_mimes
```

## 🔒 Data Protection & Privacy

### 1. Encryption Implementation

#### Field-Level Encryption
```python
# Encryption for Sensitive Data
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class DataEncryption:
    def __init__(self, master_key: str):
        self.master_key = master_key.encode()
        self.fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """Create Fernet instance with derived key"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'culture_connect_salt',  # Use random salt in production
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key))
        return Fernet(key)
    
    def encrypt_field(self, data: str) -> str:
        """Encrypt sensitive field data"""
        if not data:
            return data
        
        encrypted = self.fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt_field(self, encrypted_data: str) -> str:
        """Decrypt sensitive field data"""
        if not encrypted_data:
            return encrypted_data
        
        try:
            decoded = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.fernet.decrypt(decoded)
            return decrypted.decode()
        except Exception:
            raise ValueError("Failed to decrypt data")

# Encrypted Model Fields
class EncryptedField:
    def __init__(self, encryption_manager: DataEncryption):
        self.encryption = encryption_manager
    
    def __set_name__(self, owner, name):
        self.name = name
        self.private_name = f'_{name}'
    
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        
        encrypted_value = getattr(obj, self.private_name, None)
        if encrypted_value:
            return self.encryption.decrypt_field(encrypted_value)
        return None
    
    def __set__(self, obj, value):
        if value:
            encrypted_value = self.encryption.encrypt_field(value)
            setattr(obj, self.private_name, encrypted_value)
        else:
            setattr(obj, self.private_name, None)
```

### 2. GDPR Compliance Implementation

#### Data Privacy Management
```python
# GDPR Compliance Manager
class GDPRComplianceManager:
    def __init__(self, db_session):
        self.db = db_session
    
    async def export_user_data(self, user_id: str) -> dict:
        """Export all user data for GDPR compliance"""
        user_data = {}
        
        # User profile data
        user = await self.db.get(User, user_id)
        if user:
            user_data['profile'] = {
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'phone_number': user.phone_number,
                'created_at': user.created_at.isoformat(),
                'updated_at': user.updated_at.isoformat()
            }
        
        # Booking history
        bookings = await self.db.execute(
            select(Booking).where(Booking.tourist_id == user_id)
        )
        user_data['bookings'] = [
            {
                'id': str(booking.id),
                'experience_title': booking.experience.title,
                'date': booking.created_at.isoformat(),
                'amount': float(booking.total_amount),
                'status': booking.status
            }
            for booking in bookings.scalars().all()
        ]
        
        # Reviews and ratings
        reviews = await self.db.execute(
            select(Review).where(Review.tourist_id == user_id)
        )
        user_data['reviews'] = [
            {
                'experience_title': review.experience.title,
                'rating': review.rating,
                'comment': review.comment,
                'date': review.created_at.isoformat()
            }
            for review in reviews.scalars().all()
        ]
        
        return user_data
    
    async def delete_user_data(self, user_id: str, retention_policy: dict = None):
        """Delete user data according to GDPR right to erasure"""
        
        # Anonymize instead of delete for legal/business requirements
        user = await self.db.get(User, user_id)
        if user:
            # Anonymize personal data
            user.email = f"deleted_user_{user_id}@anonymized.com"
            user.first_name = "Deleted"
            user.last_name = "User"
            user.phone_number = None
            user.profile_image = None
            user.is_active = False
            
            # Mark as anonymized
            user.gdpr_anonymized = True
            user.gdpr_anonymized_at = datetime.utcnow()
        
        # Handle related data based on retention policy
        if retention_policy and retention_policy.get('delete_bookings'):
            # Delete booking data older than retention period
            retention_date = datetime.utcnow() - timedelta(
                days=retention_policy['booking_retention_days']
            )
            
            await self.db.execute(
                delete(Booking).where(
                    and_(
                        Booking.tourist_id == user_id,
                        Booking.created_at < retention_date
                    )
                )
            )
        
        await self.db.commit()
    
    async def log_data_access(self, user_id: str, accessed_by: str, purpose: str):
        """Log data access for audit trail"""
        access_log = DataAccessLog(
            user_id=user_id,
            accessed_by=accessed_by,
            purpose=purpose,
            timestamp=datetime.utcnow(),
            ip_address=request.client.host if request else None
        )
        
        self.db.add(access_log)
        await self.db.commit()
```

## 🚨 Security Monitoring & Incident Response

### 1. Security Event Monitoring

#### Security Event Detection
```python
# Security Event Monitor
class SecurityEventMonitor:
    def __init__(self, redis_client, notification_service):
        self.redis = redis_client
        self.notifications = notification_service
        self.threat_patterns = self._load_threat_patterns()
    
    async def monitor_login_attempts(self, request: Request, user_id: str = None):
        """Monitor for suspicious login patterns"""
        client_ip = request.client.host
        user_agent = request.headers.get('user-agent', '')
        
        # Check for brute force attacks
        await self._check_brute_force(client_ip)
        
        # Check for unusual login patterns
        if user_id:
            await self._check_unusual_login(user_id, client_ip, user_agent)
    
    async def _check_brute_force(self, ip_address: str):
        """Detect brute force attacks"""
        key = f"login_attempts:{ip_address}"
        attempts = await self.redis.incr(key)
        
        if attempts == 1:
            await self.redis.expire(key, 3600)  # 1 hour window
        
        if attempts > 20:  # More than 20 attempts per hour
            await self._trigger_security_alert(
                "brute_force_detected",
                {"ip_address": ip_address, "attempts": attempts}
            )
    
    async def _check_unusual_login(self, user_id: str, ip_address: str, user_agent: str):
        """Detect unusual login patterns"""
        # Check for new device/location
        device_key = f"user_devices:{user_id}"
        device_hash = hashlib.md5(user_agent.encode()).hexdigest()
        
        known_devices = await self.redis.smembers(device_key)
        
        if device_hash not in known_devices:
            # New device detected
            await self._trigger_security_alert(
                "new_device_login",
                {"user_id": user_id, "ip_address": ip_address, "device_hash": device_hash}
            )
            
            # Add to known devices
            await self.redis.sadd(device_key, device_hash)
            await self.redis.expire(device_key, 86400 * 30)  # 30 days
    
    async def _trigger_security_alert(self, alert_type: str, data: dict):
        """Trigger security alert"""
        alert = SecurityAlert(
            alert_type=alert_type,
            severity="high" if alert_type == "brute_force_detected" else "medium",
            data=data,
            timestamp=datetime.utcnow()
        )
        
        # Send to security team
        await self.notifications.send_security_alert(alert)
        
        # Log to security event store
        await self._log_security_event(alert)

# Rate Limiting Implementation
class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def check_rate_limit(self, identifier: str, limit: int, window: int) -> bool:
        """Check if request is within rate limit"""
        key = f"rate_limit:{identifier}"
        
        current = await self.redis.get(key)
        
        if current is None:
            # First request in window
            await self.redis.setex(key, window, 1)
            return True
        
        if int(current) >= limit:
            return False
        
        # Increment counter
        await self.redis.incr(key)
        return True
    
    async def get_rate_limit_info(self, identifier: str, window: int) -> dict:
        """Get current rate limit status"""
        key = f"rate_limit:{identifier}"
        
        current = await self.redis.get(key)
        ttl = await self.redis.ttl(key)
        
        return {
            "current_requests": int(current) if current else 0,
            "reset_time": ttl if ttl > 0 else window
        }
```

## ✅ Security Implementation Checklist

### Phase 1: Authentication & Authorization (Week 1)
- [ ] Implement secure password hashing with bcrypt
- [ ] Create JWT token management with RS256
- [ ] Set up account lockout protection
- [ ] Implement multi-factor authentication
- [ ] Configure OAuth2 providers
- [ ] Set up role-based access control

### Phase 2: Input Validation & Data Protection (Week 2)
- [ ] Implement comprehensive input validation
- [ ] Set up SQL injection prevention
- [ ] Create secure file upload system
- [ ] Implement field-level encryption
- [ ] Set up GDPR compliance tools
- [ ] Configure data anonymization

### Phase 3: Security Monitoring (Week 3)
- [ ] Implement security event monitoring
- [ ] Set up rate limiting
- [ ] Create intrusion detection
- [ ] Configure security alerting
- [ ] Set up audit logging
- [ ] Implement threat detection

### Phase 4: Production Security (Week 4)
- [ ] Configure HTTPS/TLS
- [ ] Set up security headers
- [ ] Implement CORS properly
- [ ] Configure firewall rules
- [ ] Set up backup encryption
- [ ] Create incident response plan

---

**Security Priority**: Implement authentication and input validation first (highest risk), followed by data protection, then monitoring and incident response capabilities.
