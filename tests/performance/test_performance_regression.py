"""
Performance Regression Testing for Culture Connect Backend API.

This module provides comprehensive performance regression testing including:
- Performance Regression Testing (VendorDashboardService and recent implementation impact validation)
- Optimization Validation Testing (Database query optimization, caching performance >90%, index effectiveness)
- Stress Testing and Breaking Point Analysis (System limits identification, recovery time measurement)
- Baseline Performance Comparison (Historical metrics comparison, automated regression detection)

Implements Phase 8.2: Performance Testing Implementation with zero performance regression,
stress testing validation, and comprehensive baseline performance comparison.
"""

import pytest
import asyncio
import time
import psutil
import statistics
import gc
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional, Tuple
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock

from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.main import app
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VendorStatus
from app.models.booking import Booking, BookingStatus
from app.services.load_testing_service import LoadTestingService
from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.services.vendor_dashboard_service import VendorDashboardService
from app.db.session import get_async_session_context
from app.core.security import create_token_pair
from app.core.logging import get_logger

logger = get_logger(__name__)


@pytest.mark.performance
class TestPerformanceRegression:
    """Comprehensive performance regression testing validation."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client with real app."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    async def db_session(self):
        """Create database session with transaction rollback."""
        async with get_async_session_context() as session:
            transaction = await session.begin()
            try:
                yield session
            finally:
                await transaction.rollback()

    @pytest.fixture
    async def performance_monitoring_service(self):
        """Create performance monitoring service for metrics collection."""
        service = PerformanceMonitoringService()
        yield service

    @pytest.fixture
    async def vendor_dashboard_service(self):
        """Create vendor dashboard service for regression testing."""
        service = VendorDashboardService()
        yield service

    @pytest.fixture
    async def regression_test_users(self, db_session: AsyncSession):
        """Create users for regression testing."""
        users = []
        for i in range(20):  # Create 20 users for regression testing
            user_data = {
                "email": f"regression_user_{i}_{uuid4().hex[:8]}@test.com",
                "first_name": f"RegressionUser{i}",
                "last_name": "Test",
                "role": UserRole.CUSTOMER if i % 2 == 0 else UserRole.VENDOR,
                "is_active": True,
                "is_verified": True,
                "hashed_password": "$2b$12$test_hash"
            }

            user = User(**user_data)
            db_session.add(user)
            users.append(user)

        await db_session.commit()
        for user in users:
            await db_session.refresh(user)

        return users

    @pytest.fixture
    async def regression_test_vendors(self, db_session: AsyncSession, regression_test_users: List[User]):
        """Create vendors for regression testing."""
        vendors = []
        vendor_users = [user for user in regression_test_users if user.role == UserRole.VENDOR]

        for i, user in enumerate(vendor_users):
            vendor_data = {
                "user_id": user.id,
                "business_name": f"Regression Test Vendor {i}",
                "business_type": VendorType.TOUR_GUIDE,
                "status": VendorStatus.ACTIVE,
                "verified": True,
                "phone": f"+234700{i:06d}",
                "address": f"Lagos, Nigeria - Regression Zone {i}"
            }

            vendor = Vendor(**vendor_data)
            db_session.add(vendor)
            vendors.append(vendor)

        await db_session.commit()
        for vendor in vendors:
            await db_session.refresh(vendor)

        return vendors

    @pytest.fixture
    def performance_baseline(self):
        """Performance baseline metrics for regression comparison."""
        return {
            "profile_access_ms": 150,           # <200ms target
            "vendor_search_ms": 180,            # <200ms target
            "booking_creation_ms": 450,         # <500ms target
            "payment_processing_ms": 400,       # <500ms target
            "dashboard_overview_ms": 180,       # <200ms target
            "analytics_access_ms": 170,         # <200ms target
            "vendor_dashboard_total_ms": 500,   # <600ms target
            "database_query_ms": 80,            # <100ms target
            "cache_hit_rate": 0.92,             # >90% target
            "memory_usage_mb": 800,             # <1000MB target
            "cpu_usage_percent": 65,            # <80% target
            "concurrent_users_supported": 1200, # >1000 target
            "throughput_ops_per_sec": 150       # >100 target
        }

    @pytest.fixture
    def memory_monitor(self):
        """Memory monitoring utility for leak detection."""
        class MemoryMonitor:
            def __init__(self):
                self.process = psutil.Process()
                self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                self.memory_samples = [self.initial_memory]

            def sample_memory(self):
                current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                self.memory_samples.append(current_memory)
                return current_memory

            def detect_memory_leak(self, threshold_mb: float = 100) -> bool:
                """Detect potential memory leak based on memory growth."""
                if len(self.memory_samples) < 5:
                    return False

                # Check if memory consistently increases
                recent_samples = self.memory_samples[-5:]
                growth = recent_samples[-1] - recent_samples[0]
                return growth > threshold_mb

            def get_memory_stats(self) -> Dict[str, float]:
                return {
                    "initial_memory_mb": self.initial_memory,
                    "current_memory_mb": self.memory_samples[-1],
                    "peak_memory_mb": max(self.memory_samples),
                    "memory_growth_mb": self.memory_samples[-1] - self.initial_memory,
                    "average_memory_mb": statistics.mean(self.memory_samples)
                }

        return MemoryMonitor()

    @pytest.mark.asyncio
    async def test_performance_regression_testing(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        regression_test_users: List[User],
        regression_test_vendors: List[Vendor],
        vendor_dashboard_service: VendorDashboardService,
        performance_baseline: Dict[str, float],
        memory_monitor
    ):
        """
        Test Performance Regression Testing with VendorDashboardService and recent implementation validation.

        Validates: VendorDashboardService performance, recent implementation impact, baseline comparison
        """
        correlation_id = f"performance_regression_{uuid4().hex[:8]}"

        logger.info(f"Starting performance regression testing with correlation_id: {correlation_id}")

        # Step 1: VendorDashboardService Performance Regression Testing
        async def test_vendor_dashboard_performance(vendor_user: User, vendor: Vendor) -> Dict[str, Any]:
            """Test VendorDashboardService performance against baseline."""
            token_data = {
                "sub": str(vendor_user.id),
                "email": vendor_user.email,
                "role": vendor_user.role.value,
                "scopes": ["vendor:read", "vendor:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            dashboard_metrics = {}

            # Dashboard Overview Performance
            start_time = time.perf_counter()
            overview_response = await async_client.get("/api/v1/vendors/dashboard/overview", headers=headers)
            dashboard_metrics["overview_ms"] = (time.perf_counter() - start_time) * 1000

            # Vendor Analytics Performance
            start_time = time.perf_counter()
            analytics_response = await async_client.get("/api/v1/analytics/vendor/dashboard", headers=headers)
            dashboard_metrics["analytics_ms"] = (time.perf_counter() - start_time) * 1000

            # Vendor Bookings Performance
            start_time = time.perf_counter()
            bookings_response = await async_client.get("/api/v1/vendors/bookings", headers=headers)
            dashboard_metrics["bookings_ms"] = (time.perf_counter() - start_time) * 1000

            # Total dashboard time
            dashboard_metrics["total_ms"] = sum([
                dashboard_metrics["overview_ms"],
                dashboard_metrics["analytics_ms"],
                dashboard_metrics["bookings_ms"]
            ])

            success = all(r.status_code == 200 for r in [overview_response, analytics_response, bookings_response])

            return {
                "vendor_user_id": vendor_user.id,
                "vendor_id": vendor.id,
                "dashboard_metrics": dashboard_metrics,
                "success": success
            }

        # Test VendorDashboardService with multiple vendors
        vendor_users = [user for user in regression_test_users if user.role == UserRole.VENDOR]
        dashboard_tasks = []

        for i, vendor_user in enumerate(vendor_users):
            vendor = regression_test_vendors[i % len(regression_test_vendors)]
            dashboard_tasks.append(test_vendor_dashboard_performance(vendor_user, vendor))

        dashboard_results = await asyncio.gather(*dashboard_tasks, return_exceptions=True)

        # Analyze VendorDashboardService performance
        successful_dashboards = [r for r in dashboard_results if isinstance(r, dict) and r["success"]]

        # Extract performance metrics
        overview_times = [r["dashboard_metrics"]["overview_ms"] for r in successful_dashboards]
        analytics_times = [r["dashboard_metrics"]["analytics_ms"] for r in successful_dashboards]
        bookings_times = [r["dashboard_metrics"]["bookings_ms"] for r in successful_dashboards]
        total_times = [r["dashboard_metrics"]["total_ms"] for r in successful_dashboards]

        # Validate against baseline performance
        avg_overview_time = statistics.mean(overview_times)
        avg_analytics_time = statistics.mean(analytics_times)
        avg_total_time = statistics.mean(total_times)

        # Performance regression validation
        assert avg_overview_time <= performance_baseline["dashboard_overview_ms"] * 1.1  # Allow 10% variance
        assert avg_analytics_time <= performance_baseline["analytics_access_ms"] * 1.1   # Allow 10% variance
        assert avg_total_time <= performance_baseline["vendor_dashboard_total_ms"] * 1.1 # Allow 10% variance

        logger.info(f"VendorDashboardService performance: Overview {avg_overview_time:.2f}ms, "
                   f"Analytics {avg_analytics_time:.2f}ms, Total {avg_total_time:.2f}ms")

        # Step 2: Recent Implementation Impact Testing
        async def test_core_operations_performance() -> Dict[str, float]:
            """Test core operations performance to detect recent implementation impact."""
            customer_users = [user for user in regression_test_users if user.role == UserRole.CUSTOMER]
            customer = customer_users[0]

            token_data = {
                "sub": str(customer.id),
                "email": customer.email,
                "role": customer.role.value,
                "scopes": ["customer:read", "customer:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            core_metrics = {}

            # Profile Access
            start_time = time.perf_counter()
            profile_response = await async_client.get("/api/v1/users/profile", headers=headers)
            core_metrics["profile_access_ms"] = (time.perf_counter() - start_time) * 1000

            # Vendor Search
            start_time = time.perf_counter()
            search_response = await async_client.get(
                "/api/v1/vendors/search",
                params={"location": "Lagos"},
                headers=headers
            )
            core_metrics["vendor_search_ms"] = (time.perf_counter() - start_time) * 1000

            # Analytics Access
            start_time = time.perf_counter()
            analytics_response = await async_client.get("/api/v1/analytics/user/dashboard", headers=headers)
            core_metrics["analytics_access_ms"] = (time.perf_counter() - start_time) * 1000

            # Booking Creation
            vendor = regression_test_vendors[0]
            booking_data = {
                "vendor_id": vendor.id,
                "service_id": 1,
                "booking_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "total_amount": 300.00,
                "number_of_guests": 2,
                "special_requests": "Regression test booking"
            }

            start_time = time.perf_counter()
            booking_response = await async_client.post("/api/v1/bookings/", json=booking_data, headers=headers)
            core_metrics["booking_creation_ms"] = (time.perf_counter() - start_time) * 1000

            return core_metrics

        # Test core operations performance
        core_performance = await test_core_operations_performance()

        # Validate core operations against baseline
        assert core_performance["profile_access_ms"] <= performance_baseline["profile_access_ms"] * 1.1
        assert core_performance["vendor_search_ms"] <= performance_baseline["vendor_search_ms"] * 1.1
        assert core_performance["booking_creation_ms"] <= performance_baseline["booking_creation_ms"] * 1.1

        logger.info(f"Core operations performance: Profile {core_performance['profile_access_ms']:.2f}ms, "
                   f"Search {core_performance['vendor_search_ms']:.2f}ms, "
                   f"Booking {core_performance['booking_creation_ms']:.2f}ms")

        # Step 3: Memory Usage Regression Testing
        memory_monitor.sample_memory()

        # Execute memory-intensive operations
        for i in range(10):
            await test_vendor_dashboard_performance(vendor_users[0], regression_test_vendors[0])
            memory_monitor.sample_memory()

            # Force garbage collection
            gc.collect()
            await asyncio.sleep(0.1)

        # Check for memory leaks
        memory_stats = memory_monitor.get_memory_stats()
        has_memory_leak = memory_monitor.detect_memory_leak(50)  # 50MB threshold

        # Validate memory usage
        assert not has_memory_leak  # No memory leak detected
        assert memory_stats["peak_memory_mb"] <= performance_baseline["memory_usage_mb"] * 1.2  # Allow 20% variance
        assert memory_stats["memory_growth_mb"] < 100  # <100MB growth during testing

        logger.info(f"Memory regression testing: Peak {memory_stats['peak_memory_mb']:.2f}MB, "
                   f"Growth {memory_stats['memory_growth_mb']:.2f}MB, "
                   f"Leak detected: {has_memory_leak}")

        # Step 4: Performance Baseline Comparison Summary
        regression_summary = {
            "vendor_dashboard_regression": {
                "overview_baseline_ms": performance_baseline["dashboard_overview_ms"],
                "overview_actual_ms": avg_overview_time,
                "overview_regression_percent": ((avg_overview_time - performance_baseline["dashboard_overview_ms"]) / performance_baseline["dashboard_overview_ms"]) * 100,
                "passed": avg_overview_time <= performance_baseline["dashboard_overview_ms"] * 1.1
            },
            "core_operations_regression": {
                "profile_baseline_ms": performance_baseline["profile_access_ms"],
                "profile_actual_ms": core_performance["profile_access_ms"],
                "profile_regression_percent": ((core_performance["profile_access_ms"] - performance_baseline["profile_access_ms"]) / performance_baseline["profile_access_ms"]) * 100,
                "passed": core_performance["profile_access_ms"] <= performance_baseline["profile_access_ms"] * 1.1
            },
            "memory_regression": {
                "memory_baseline_mb": performance_baseline["memory_usage_mb"],
                "memory_actual_mb": memory_stats["peak_memory_mb"],
                "memory_growth_mb": memory_stats["memory_growth_mb"],
                "leak_detected": has_memory_leak,
                "passed": not has_memory_leak and memory_stats["peak_memory_mb"] <= performance_baseline["memory_usage_mb"] * 1.2
            }
        }

        # Validate overall regression testing
        all_regression_tests_passed = all(
            regression_summary[test]["passed"] for test in regression_summary
        )

        assert all_regression_tests_passed  # All regression tests must pass

        logger.info(f"Performance regression testing completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Regression summary: VendorDashboard passed: {regression_summary['vendor_dashboard_regression']['passed']}, "
                   f"Core operations passed: {regression_summary['core_operations_regression']['passed']}, "
                   f"Memory regression passed: {regression_summary['memory_regression']['passed']}")

        return regression_summary

    @pytest.mark.asyncio
    async def test_optimization_validation_and_stress_testing(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        regression_test_users: List[User],
        regression_test_vendors: List[Vendor],
        performance_baseline: Dict[str, float],
        memory_monitor
    ):
        """
        Test Optimization Validation and Stress Testing with database optimization and breaking point analysis.

        Validates: Database query optimization, caching performance >90%, stress testing, breaking point analysis
        """
        correlation_id = f"optimization_stress_{uuid4().hex[:8]}"

        logger.info(f"Starting optimization validation and stress testing with correlation_id: {correlation_id}")

        # Step 1: Database Query Optimization Validation
        async def test_database_optimization() -> Dict[str, Any]:
            """Test database query performance and optimization."""
            async with get_async_session_context() as session:
                optimization_metrics = {}

                # Test simple query performance
                start_time = time.perf_counter()
                result = await session.execute(text("SELECT COUNT(*) FROM users"))
                count = result.scalar()
                optimization_metrics["simple_query_ms"] = (time.perf_counter() - start_time) * 1000

                # Test complex join query performance
                start_time = time.perf_counter()
                complex_query = text("""
                    SELECT u.id, u.email, v.business_name
                    FROM users u
                    LEFT JOIN vendors v ON u.id = v.user_id
                    WHERE u.is_active = true
                    LIMIT 10
                """)
                result = await session.execute(complex_query)
                rows = result.fetchall()
                optimization_metrics["complex_query_ms"] = (time.perf_counter() - start_time) * 1000

                # Test index effectiveness (vendor search simulation)
                start_time = time.perf_counter()
                vendor_query = text("""
                    SELECT * FROM vendors
                    WHERE status = 'ACTIVE' AND verified = true
                    LIMIT 20
                """)
                result = await session.execute(vendor_query)
                vendors = result.fetchall()
                optimization_metrics["indexed_query_ms"] = (time.perf_counter() - start_time) * 1000

                return {
                    "optimization_metrics": optimization_metrics,
                    "query_results": {
                        "user_count": count,
                        "complex_rows": len(rows),
                        "vendor_rows": len(vendors)
                    }
                }

        db_optimization_result = await test_database_optimization()
        db_metrics = db_optimization_result["optimization_metrics"]

        # Validate database optimization performance
        assert db_metrics["simple_query_ms"] < 50      # <50ms for simple queries
        assert db_metrics["complex_query_ms"] < 100    # <100ms for complex queries
        assert db_metrics["indexed_query_ms"] < 80     # <80ms for indexed queries

        logger.info(f"Database optimization: Simple {db_metrics['simple_query_ms']:.2f}ms, "
                   f"Complex {db_metrics['complex_query_ms']:.2f}ms, "
                   f"Indexed {db_metrics['indexed_query_ms']:.2f}ms")

        # Step 2: Caching Performance Validation (Simulated)
        async def test_caching_performance() -> Dict[str, Any]:
            """Test caching performance and hit rate validation."""
            cache_metrics = {
                "cache_hits": 0,
                "cache_misses": 0,
                "cache_response_times": []
            }

            # Simulate cache testing with repeated API calls
            customer = [user for user in regression_test_users if user.role == UserRole.CUSTOMER][0]
            token_data = {
                "sub": str(customer.id),
                "email": customer.email,
                "role": customer.role.value,
                "scopes": ["customer:read", "customer:write"]
            }
            tokens = create_token_pair(token_data)
            headers = {"Authorization": f"Bearer {tokens['access_token']}"}

            # First call (cache miss simulation)
            start_time = time.perf_counter()
            first_response = await async_client.get("/api/v1/vendors/search", params={"location": "Lagos"}, headers=headers)
            first_call_time = (time.perf_counter() - start_time) * 1000
            cache_metrics["cache_response_times"].append(first_call_time)
            cache_metrics["cache_misses"] += 1

            # Subsequent calls (cache hit simulation)
            for i in range(10):
                start_time = time.perf_counter()
                response = await async_client.get("/api/v1/vendors/search", params={"location": "Lagos"}, headers=headers)
                response_time = (time.perf_counter() - start_time) * 1000
                cache_metrics["cache_response_times"].append(response_time)

                # Simulate cache hit (faster response)
                if response_time < first_call_time * 0.8:  # 20% faster indicates cache hit
                    cache_metrics["cache_hits"] += 1
                else:
                    cache_metrics["cache_misses"] += 1

            total_requests = cache_metrics["cache_hits"] + cache_metrics["cache_misses"]
            cache_hit_rate = cache_metrics["cache_hits"] / total_requests if total_requests > 0 else 0
            avg_response_time = statistics.mean(cache_metrics["cache_response_times"])

            return {
                "cache_hit_rate": cache_hit_rate,
                "average_response_time_ms": avg_response_time,
                "total_requests": total_requests,
                "cache_hits": cache_metrics["cache_hits"],
                "cache_misses": cache_metrics["cache_misses"]
            }

        caching_result = await test_caching_performance()

        # Validate caching performance (relaxed for testing environment)
        assert caching_result["cache_hit_rate"] >= 0.5  # >50% hit rate in test environment
        assert caching_result["average_response_time_ms"] < 300  # <300ms average response time

        logger.info(f"Caching performance: Hit rate {caching_result['cache_hit_rate']:.2%}, "
                   f"Avg response time {caching_result['average_response_time_ms']:.2f}ms")

        # Step 3: Stress Testing and Breaking Point Analysis
        async def execute_stress_test(stress_level: int) -> Dict[str, Any]:
            """Execute stress test at specified level."""
            stress_start = time.perf_counter()

            async def stress_operation(user: User, operation_index: int) -> Dict[str, Any]:
                """Single stress test operation."""
                op_start = time.perf_counter()

                token_data = {
                    "sub": str(user.id),
                    "email": user.email,
                    "role": user.role.value,
                    "scopes": ["customer:read", "customer:write"] if user.role == UserRole.CUSTOMER else ["vendor:read", "vendor:write"]
                }
                tokens = create_token_pair(token_data)
                headers = {"Authorization": f"Bearer {tokens['access_token']}"}

                try:
                    if user.role == UserRole.CUSTOMER:
                        # Customer stress operations
                        profile_response = await async_client.get("/api/v1/users/profile", headers=headers)
                        search_response = await async_client.get("/api/v1/vendors/search", params={"location": "Lagos"}, headers=headers)
                        success = profile_response.status_code == 200 and search_response.status_code == 200
                    else:
                        # Vendor stress operations
                        dashboard_response = await async_client.get("/api/v1/vendors/dashboard/overview", headers=headers)
                        bookings_response = await async_client.get("/api/v1/vendors/bookings", headers=headers)
                        success = dashboard_response.status_code == 200 and bookings_response.status_code == 200

                    response_time = (time.perf_counter() - op_start) * 1000

                    return {
                        "operation_index": operation_index,
                        "user_id": user.id,
                        "response_time_ms": response_time,
                        "success": success
                    }

                except Exception as e:
                    response_time = (time.perf_counter() - op_start) * 1000
                    logger.warning(f"Stress operation error: {e}")
                    return {
                        "operation_index": operation_index,
                        "user_id": user.id,
                        "response_time_ms": response_time,
                        "success": False,
                        "error": str(e)
                    }

            # Execute stress operations
            stress_tasks = []
            for i in range(stress_level):
                user = regression_test_users[i % len(regression_test_users)]
                stress_tasks.append(stress_operation(user, i))

            # Sample memory during stress test
            memory_before = memory_monitor.sample_memory()

            stress_results = await asyncio.gather(*stress_tasks, return_exceptions=True)

            memory_after = memory_monitor.sample_memory()
            stress_duration = (time.perf_counter() - stress_start) * 1000

            # Analyze stress test results
            successful_operations = [r for r in stress_results if isinstance(r, dict) and r["success"]]
            response_times = [r["response_time_ms"] for r in successful_operations]

            return {
                "stress_level": stress_level,
                "total_operations": len(stress_tasks),
                "successful_operations": len(successful_operations),
                "success_rate": len(successful_operations) / len(stress_tasks) if stress_tasks else 0,
                "average_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "max_response_time_ms": max(response_times) if response_times else 0,
                "stress_duration_ms": stress_duration,
                "memory_before_mb": memory_before,
                "memory_after_mb": memory_after,
                "memory_increase_mb": memory_after - memory_before,
                "throughput_ops_per_sec": len(successful_operations) / (stress_duration / 1000) if stress_duration > 0 else 0
            }

        # Execute progressive stress testing
        stress_levels = [100, 300, 600, 1000]  # Progressive stress levels
        stress_results = []
        breaking_point = None

        for stress_level in stress_levels:
            logger.info(f"Executing stress test at level: {stress_level}")

            stress_result = await execute_stress_test(stress_level)
            stress_results.append(stress_result)

            logger.info(f"Stress level {stress_level}: {stress_result['successful_operations']} successful, "
                       f"success rate: {stress_result['success_rate']:.2%}, "
                       f"avg time: {stress_result['average_response_time_ms']:.2f}ms")

            # Check for breaking point
            if stress_result["success_rate"] < 0.8 or stress_result["average_response_time_ms"] > 2000:
                breaking_point = stress_result
                logger.info(f"Breaking point identified at stress level: {stress_level}")
                break

            # Small delay between stress levels
            await asyncio.sleep(1)

        # Step 4: Recovery Time Measurement
        if breaking_point:
            logger.info("Testing system recovery after stress...")

            # Wait for system recovery
            await asyncio.sleep(3)

            # Test recovery with light load
            recovery_start = time.perf_counter()
            recovery_result = await execute_stress_test(50)  # Light load
            recovery_time = (time.perf_counter() - recovery_start) * 1000

            # Validate recovery
            assert recovery_result["success_rate"] >= 0.9  # >90% success rate after recovery
            assert recovery_result["average_response_time_ms"] < 500  # <500ms after recovery

            logger.info(f"System recovery: Success rate {recovery_result['success_rate']:.2%}, "
                       f"Recovery time {recovery_time:.2f}ms")

        # Step 5: Stress Testing Validation
        max_successful_stress_level = max(r["stress_level"] for r in stress_results if r["success_rate"] >= 0.8)

        # Validate stress testing results
        assert max_successful_stress_level >= 600  # Support at least 600 concurrent operations

        # Validate memory efficiency during stress
        max_memory_increase = max(r["memory_increase_mb"] for r in stress_results)
        assert max_memory_increase < 200  # <200MB memory increase during stress

        # Validate throughput under stress
        min_throughput = min(r["throughput_ops_per_sec"] for r in stress_results if r["throughput_ops_per_sec"] > 0)
        assert min_throughput >= 50  # >50 ops/sec minimum throughput

        logger.info(f"Optimization validation and stress testing completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Max stress level supported: {max_successful_stress_level}, "
                   f"Max memory increase: {max_memory_increase:.2f}MB, "
                   f"Min throughput: {min_throughput:.2f} ops/sec")

        return {
            "database_optimization": db_metrics,
            "caching_performance": caching_result,
            "stress_testing": stress_results,
            "breaking_point": breaking_point,
            "max_stress_level": max_successful_stress_level
        }
