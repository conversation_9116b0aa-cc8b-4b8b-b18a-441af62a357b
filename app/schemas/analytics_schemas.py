"""
Analytics schemas for Culture Connect Backend API.

This module defines comprehensive analytics and dashboard management schemas including:
- UserAnalytics schemas: User behavior tracking and engagement metrics
- VendorAnalytics schemas: Vendor performance metrics and business intelligence
- BookingAnalytics schemas: Booking conversion and revenue analytics
- SystemMetrics schemas: Application performance and system health monitoring
- DashboardWidget schemas: Configurable dashboard components and KPI definitions

Implements Phase 7.1 requirements for analytics dashboard system with
production-grade Pydantic V2 validation and comprehensive error handling.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin, PaginationMixin
from typing import Generic, TypeVar, List
from app.models.analytics_models import AnalyticsTimeframe, MetricType, DashboardWidgetType


# ============================================================================
# User Analytics Schemas
# ============================================================================

class UserAnalyticsBase(BaseModel):
    """Base schema for user analytics data."""

    model_config = ConfigDict(from_attributes=True)

    timeframe: AnalyticsTimeframe = Field(
        description="Time aggregation period"
    )
    period_start: datetime = Field(
        description="Start of the analytics period"
    )
    period_end: datetime = Field(
        description="End of the analytics period"
    )
    session_count: int = Field(
        default=0,
        ge=0,
        description="Number of user sessions"
    )
    total_session_duration: int = Field(
        default=0,
        ge=0,
        description="Total session duration in seconds"
    )
    page_views: int = Field(
        default=0,
        ge=0,
        description="Total page views"
    )
    unique_pages_visited: int = Field(
        default=0,
        ge=0,
        description="Number of unique pages visited"
    )
    bookings_viewed: int = Field(
        default=0,
        ge=0,
        description="Number of bookings viewed"
    )
    bookings_created: int = Field(
        default=0,
        ge=0,
        description="Number of bookings created"
    )
    bookings_completed: int = Field(
        default=0,
        ge=0,
        description="Number of bookings completed"
    )
    total_spent: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total amount spent"
    )
    average_order_value: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Average order value"
    )
    device_type: Optional[str] = Field(
        default=None,
        max_length=50,
        description="Primary device type used"
    )
    location_country: Optional[str] = Field(
        default=None,
        max_length=2,
        description="User's country code"
    )
    location_city: Optional[str] = Field(
        default=None,
        max_length=100,
        description="User's city"
    )
    custom_metrics: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional custom metrics"
    )


class UserAnalyticsCreate(UserAnalyticsBase):
    """Schema for creating user analytics records."""

    user_id: UUID = Field(
        description="Reference to the user"
    )


class UserAnalyticsUpdate(BaseModel):
    """Schema for updating user analytics records."""

    model_config = ConfigDict(from_attributes=True)

    session_count: Optional[int] = Field(default=None, ge=0)
    total_session_duration: Optional[int] = Field(default=None, ge=0)
    page_views: Optional[int] = Field(default=None, ge=0)
    unique_pages_visited: Optional[int] = Field(default=None, ge=0)
    bookings_viewed: Optional[int] = Field(default=None, ge=0)
    bookings_created: Optional[int] = Field(default=None, ge=0)
    bookings_completed: Optional[int] = Field(default=None, ge=0)
    total_spent: Optional[Decimal] = Field(default=None, ge=0)
    average_order_value: Optional[Decimal] = Field(default=None, ge=0)
    device_type: Optional[str] = Field(default=None, max_length=50)
    location_country: Optional[str] = Field(default=None, max_length=2)
    location_city: Optional[str] = Field(default=None, max_length=100)
    custom_metrics: Optional[Dict[str, Any]] = Field(default=None)


class UserAnalyticsResponse(UserAnalyticsBase, UUIDMixin, TimestampMixin):
    """Schema for user analytics API responses."""

    user_id: UUID = Field(
        description="Reference to the user"
    )


# ============================================================================
# Vendor Analytics Schemas
# ============================================================================

class VendorAnalyticsBase(BaseModel):
    """Base schema for vendor analytics data."""

    model_config = ConfigDict(from_attributes=True)

    timeframe: AnalyticsTimeframe = Field(
        description="Time aggregation period"
    )
    period_start: datetime = Field(
        description="Start of the analytics period"
    )
    period_end: datetime = Field(
        description="End of the analytics period"
    )
    profile_views: int = Field(
        default=0,
        ge=0,
        description="Number of profile views"
    )
    service_views: int = Field(
        default=0,
        ge=0,
        description="Number of service views"
    )
    booking_requests: int = Field(
        default=0,
        ge=0,
        description="Number of booking requests received"
    )
    bookings_accepted: int = Field(
        default=0,
        ge=0,
        description="Number of bookings accepted"
    )
    bookings_completed: int = Field(
        default=0,
        ge=0,
        description="Number of bookings completed"
    )
    bookings_cancelled: int = Field(
        default=0,
        ge=0,
        description="Number of bookings cancelled"
    )
    total_revenue: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total revenue generated"
    )
    commission_paid: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total commission paid to platform"
    )
    net_revenue: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Net revenue after commission"
    )
    average_booking_value: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Average booking value"
    )
    response_time_avg: int = Field(
        default=0,
        ge=0,
        description="Average response time to bookings in minutes"
    )
    customer_rating_avg: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=5,
        description="Average customer rating"
    )
    repeat_customer_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        description="Repeat customer rate as percentage"
    )
    custom_metrics: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional custom metrics"
    )


class VendorAnalyticsCreate(VendorAnalyticsBase):
    """Schema for creating vendor analytics records."""

    vendor_id: UUID = Field(
        description="Reference to the vendor"
    )


class VendorAnalyticsUpdate(BaseModel):
    """Schema for updating vendor analytics records."""

    model_config = ConfigDict(from_attributes=True)

    profile_views: Optional[int] = Field(default=None, ge=0)
    service_views: Optional[int] = Field(default=None, ge=0)
    booking_requests: Optional[int] = Field(default=None, ge=0)
    bookings_accepted: Optional[int] = Field(default=None, ge=0)
    bookings_completed: Optional[int] = Field(default=None, ge=0)
    bookings_cancelled: Optional[int] = Field(default=None, ge=0)
    total_revenue: Optional[Decimal] = Field(default=None, ge=0)
    commission_paid: Optional[Decimal] = Field(default=None, ge=0)
    net_revenue: Optional[Decimal] = Field(default=None, ge=0)
    average_booking_value: Optional[Decimal] = Field(default=None, ge=0)
    response_time_avg: Optional[int] = Field(default=None, ge=0)
    customer_rating_avg: Optional[Decimal] = Field(default=None, ge=0, le=5)
    repeat_customer_rate: Optional[Decimal] = Field(default=None, ge=0, le=100)
    custom_metrics: Optional[Dict[str, Any]] = Field(default=None)


class VendorAnalyticsResponse(VendorAnalyticsBase, UUIDMixin, TimestampMixin):
    """Schema for vendor analytics API responses."""

    vendor_id: UUID = Field(
        description="Reference to the vendor"
    )


# ============================================================================
# Booking Analytics Schemas
# ============================================================================

class BookingAnalyticsBase(BaseModel):
    """Base schema for booking analytics data."""

    model_config = ConfigDict(from_attributes=True)

    timeframe: AnalyticsTimeframe = Field(
        description="Time aggregation period"
    )
    period_start: datetime = Field(
        description="Start of the analytics period"
    )
    period_end: datetime = Field(
        description="End of the analytics period"
    )
    booking_views: int = Field(
        default=0,
        ge=0,
        description="Number of booking page views"
    )
    booking_starts: int = Field(
        default=0,
        ge=0,
        description="Number of booking processes started"
    )
    booking_submissions: int = Field(
        default=0,
        ge=0,
        description="Number of booking submissions"
    )
    booking_completions: int = Field(
        default=0,
        ge=0,
        description="Number of completed bookings"
    )
    total_booking_value: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total value of all bookings"
    )
    completed_booking_value: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total value of completed bookings"
    )
    average_booking_value: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Average booking value"
    )
    commission_revenue: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        description="Total commission revenue"
    )
    view_to_start_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        description="View to start conversion rate as percentage"
    )
    start_to_completion_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        description="Start to completion conversion rate as percentage"
    )
    overall_conversion_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        description="Overall conversion rate as percentage"
    )
    top_countries: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Top countries by booking volume"
    )
    top_cities: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Top cities by booking volume"
    )
    device_breakdown: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Booking breakdown by device type"
    )
    payment_method_breakdown: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Booking breakdown by payment method"
    )
    custom_metrics: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional custom metrics"
    )


class BookingAnalyticsCreate(BookingAnalyticsBase):
    """Schema for creating booking analytics records."""
    pass


class BookingAnalyticsUpdate(BaseModel):
    """Schema for updating booking analytics records."""

    model_config = ConfigDict(from_attributes=True)

    booking_views: Optional[int] = Field(default=None, ge=0)
    booking_starts: Optional[int] = Field(default=None, ge=0)
    booking_submissions: Optional[int] = Field(default=None, ge=0)
    booking_completions: Optional[int] = Field(default=None, ge=0)
    total_booking_value: Optional[Decimal] = Field(default=None, ge=0)
    completed_booking_value: Optional[Decimal] = Field(default=None, ge=0)
    average_booking_value: Optional[Decimal] = Field(default=None, ge=0)
    commission_revenue: Optional[Decimal] = Field(default=None, ge=0)
    view_to_start_rate: Optional[Decimal] = Field(default=None, ge=0, le=100)
    start_to_completion_rate: Optional[Decimal] = Field(default=None, ge=0, le=100)
    overall_conversion_rate: Optional[Decimal] = Field(default=None, ge=0, le=100)
    top_countries: Optional[Dict[str, Any]] = Field(default=None)
    top_cities: Optional[Dict[str, Any]] = Field(default=None)
    device_breakdown: Optional[Dict[str, Any]] = Field(default=None)
    payment_method_breakdown: Optional[Dict[str, Any]] = Field(default=None)
    custom_metrics: Optional[Dict[str, Any]] = Field(default=None)


class BookingAnalyticsResponse(BookingAnalyticsBase, UUIDMixin, TimestampMixin):
    """Schema for booking analytics API responses."""
    pass


# ============================================================================
# System Metrics Schemas
# ============================================================================

class SystemMetricsBase(BaseModel):
    """Base schema for system metrics data."""

    model_config = ConfigDict(from_attributes=True)

    metric_name: str = Field(
        max_length=100,
        description="Name of the metric"
    )
    metric_type: MetricType = Field(
        description="Type of metric"
    )
    timeframe: AnalyticsTimeframe = Field(
        description="Time aggregation period"
    )
    period_start: datetime = Field(
        description="Start of the metrics period"
    )
    period_end: datetime = Field(
        description="End of the metrics period"
    )
    value: Decimal = Field(
        description="Primary metric value"
    )
    min_value: Optional[Decimal] = Field(
        default=None,
        description="Minimum value in the period"
    )
    max_value: Optional[Decimal] = Field(
        default=None,
        description="Maximum value in the period"
    )
    avg_value: Optional[Decimal] = Field(
        default=None,
        description="Average value in the period"
    )
    count: int = Field(
        default=1,
        gt=0,
        description="Number of data points"
    )
    tags: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Metric tags for filtering and grouping"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metric metadata"
    )


class SystemMetricsCreate(SystemMetricsBase):
    """Schema for creating system metrics records."""
    pass


class SystemMetricsUpdate(BaseModel):
    """Schema for updating system metrics records."""

    model_config = ConfigDict(from_attributes=True)

    value: Optional[Decimal] = Field(default=None)
    min_value: Optional[Decimal] = Field(default=None)
    max_value: Optional[Decimal] = Field(default=None)
    avg_value: Optional[Decimal] = Field(default=None)
    count: Optional[int] = Field(default=None, gt=0)
    tags: Optional[Dict[str, Any]] = Field(default=None)
    metadata: Optional[Dict[str, Any]] = Field(default=None)


class SystemMetricsResponse(SystemMetricsBase, UUIDMixin, TimestampMixin):
    """Schema for system metrics API responses."""
    pass


# ============================================================================
# Dashboard Widget Schemas
# ============================================================================

class DashboardWidgetBase(BaseModel):
    """Base schema for dashboard widget data."""

    model_config = ConfigDict(from_attributes=True)

    name: str = Field(
        max_length=100,
        description="Widget name"
    )
    title: str = Field(
        max_length=200,
        description="Widget display title"
    )
    description: Optional[str] = Field(
        default=None,
        description="Widget description"
    )
    widget_type: DashboardWidgetType = Field(
        description="Type of dashboard widget"
    )
    data_source: str = Field(
        max_length=100,
        description="Data source for the widget"
    )
    query_config: Dict[str, Any] = Field(
        description="Query configuration for data retrieval"
    )
    display_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Display configuration (colors, formatting, etc.)"
    )
    refresh_interval: int = Field(
        default=300,
        gt=0,
        description="Refresh interval in seconds"
    )
    is_public: bool = Field(
        default=False,
        description="Whether widget is publicly accessible"
    )
    allowed_roles: Optional[List[str]] = Field(
        default=None,
        description="Roles allowed to view this widget"
    )
    dashboard_id: Optional[UUID] = Field(
        default=None,
        description="Dashboard this widget belongs to"
    )
    position_x: int = Field(
        default=0,
        ge=0,
        description="X position on dashboard grid"
    )
    position_y: int = Field(
        default=0,
        ge=0,
        description="Y position on dashboard grid"
    )
    width: int = Field(
        default=4,
        gt=0,
        description="Widget width in grid units"
    )
    height: int = Field(
        default=3,
        gt=0,
        description="Widget height in grid units"
    )
    is_active: bool = Field(
        default=True,
        description="Whether widget is active"
    )
    last_updated: Optional[datetime] = Field(
        default=None,
        description="Last time widget data was updated"
    )
    cache_duration: int = Field(
        default=300,
        ge=0,
        description="Cache duration in seconds"
    )
    custom_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional custom configuration"
    )


class DashboardWidgetCreate(DashboardWidgetBase):
    """Schema for creating dashboard widget records."""
    pass


class DashboardWidgetUpdate(BaseModel):
    """Schema for updating dashboard widget records."""

    model_config = ConfigDict(from_attributes=True)

    name: Optional[str] = Field(default=None, max_length=100)
    title: Optional[str] = Field(default=None, max_length=200)
    description: Optional[str] = Field(default=None)
    widget_type: Optional[DashboardWidgetType] = Field(default=None)
    data_source: Optional[str] = Field(default=None, max_length=100)
    query_config: Optional[Dict[str, Any]] = Field(default=None)
    display_config: Optional[Dict[str, Any]] = Field(default=None)
    refresh_interval: Optional[int] = Field(default=None, gt=0)
    is_public: Optional[bool] = Field(default=None)
    allowed_roles: Optional[List[str]] = Field(default=None)
    dashboard_id: Optional[UUID] = Field(default=None)
    position_x: Optional[int] = Field(default=None, ge=0)
    position_y: Optional[int] = Field(default=None, ge=0)
    width: Optional[int] = Field(default=None, gt=0)
    height: Optional[int] = Field(default=None, gt=0)
    is_active: Optional[bool] = Field(default=None)
    cache_duration: Optional[int] = Field(default=None, ge=0)
    custom_config: Optional[Dict[str, Any]] = Field(default=None)


class DashboardWidgetResponse(DashboardWidgetBase, UUIDMixin, TimestampMixin):
    """Schema for dashboard widget API responses."""
    pass


# ============================================================================
# KPI Definition Schemas
# ============================================================================

class KPIDefinitionBase(BaseModel):
    """Base schema for KPI definition data."""

    model_config = ConfigDict(from_attributes=True)

    name: str = Field(
        max_length=100,
        description="KPI name (unique identifier)"
    )
    display_name: str = Field(
        max_length=200,
        description="KPI display name"
    )
    description: Optional[str] = Field(
        default=None,
        description="KPI description"
    )
    category: str = Field(
        max_length=50,
        description="KPI category (revenue, engagement, performance, etc.)"
    )
    calculation_method: str = Field(
        max_length=50,
        description="Calculation method (sum, avg, count, rate, etc.)"
    )
    data_source: str = Field(
        max_length=100,
        description="Primary data source for calculation"
    )
    calculation_config: Dict[str, Any] = Field(
        description="Detailed calculation configuration"
    )
    unit: Optional[str] = Field(
        default=None,
        max_length=20,
        description="Unit of measurement (%, $, count, etc.)"
    )
    format_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Number formatting configuration"
    )
    target_value: Optional[Decimal] = Field(
        default=None,
        description="Target value for this KPI"
    )
    warning_threshold: Optional[Decimal] = Field(
        default=None,
        description="Warning threshold value"
    )
    critical_threshold: Optional[Decimal] = Field(
        default=None,
        description="Critical threshold value"
    )
    is_active: bool = Field(
        default=True,
        description="Whether KPI is active"
    )
    update_frequency: str = Field(
        default="daily",
        max_length=20,
        description="How often KPI should be updated"
    )
    custom_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional custom configuration"
    )


class KPIDefinitionCreate(KPIDefinitionBase):
    """Schema for creating KPI definition records."""
    pass


class KPIDefinitionUpdate(BaseModel):
    """Schema for updating KPI definition records."""

    model_config = ConfigDict(from_attributes=True)

    display_name: Optional[str] = Field(default=None, max_length=200)
    description: Optional[str] = Field(default=None)
    category: Optional[str] = Field(default=None, max_length=50)
    calculation_method: Optional[str] = Field(default=None, max_length=50)
    data_source: Optional[str] = Field(default=None, max_length=100)
    calculation_config: Optional[Dict[str, Any]] = Field(default=None)
    unit: Optional[str] = Field(default=None, max_length=20)
    format_config: Optional[Dict[str, Any]] = Field(default=None)
    target_value: Optional[Decimal] = Field(default=None)
    warning_threshold: Optional[Decimal] = Field(default=None)
    critical_threshold: Optional[Decimal] = Field(default=None)
    is_active: Optional[bool] = Field(default=None)
    update_frequency: Optional[str] = Field(default=None, max_length=20)
    custom_config: Optional[Dict[str, Any]] = Field(default=None)


class KPIDefinitionResponse(KPIDefinitionBase, UUIDMixin, TimestampMixin):
    """Schema for KPI definition API responses."""
    pass


# ============================================================================
# Analytics Query and Filter Schemas
# ============================================================================

class AnalyticsQueryParams(PaginationMixin, BaseModel):
    """Schema for analytics query parameters."""

    model_config = ConfigDict(from_attributes=True)

    timeframe: Optional[AnalyticsTimeframe] = Field(
        default=None,
        description="Filter by timeframe"
    )
    start_date: Optional[datetime] = Field(
        default=None,
        description="Start date for filtering"
    )
    end_date: Optional[datetime] = Field(
        default=None,
        description="End date for filtering"
    )
    user_id: Optional[UUID] = Field(
        default=None,
        description="Filter by user ID"
    )
    vendor_id: Optional[UUID] = Field(
        default=None,
        description="Filter by vendor ID"
    )
    country: Optional[str] = Field(
        default=None,
        max_length=2,
        description="Filter by country code"
    )
    device_type: Optional[str] = Field(
        default=None,
        max_length=50,
        description="Filter by device type"
    )

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def validate_dates(cls, v):
        """Validate date fields."""
        if v is None:
            return v

        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError('Invalid date format')

        return v

    @model_validator(mode='after')
    def validate_date_range(self):
        """Validate date range."""
        if self.start_date and self.end_date:
            if self.start_date >= self.end_date:
                raise ValueError('Start date must be before end date')

        return self


class DashboardDataResponse(BaseModel):
    """Schema for dashboard data API responses."""

    model_config = ConfigDict(from_attributes=True)

    widget_id: UUID = Field(
        description="Widget identifier"
    )
    data: Dict[str, Any] = Field(
        description="Widget data"
    )
    last_updated: datetime = Field(
        description="Last update timestamp"
    )
    cache_expires: Optional[datetime] = Field(
        default=None,
        description="Cache expiration timestamp"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata"
    )


# ============================================================================
# GENERIC RESPONSE SCHEMAS
# ============================================================================

T = TypeVar('T')

class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response schema."""

    items: List[T] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Items per page")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")

    model_config = ConfigDict(from_attributes=True)
