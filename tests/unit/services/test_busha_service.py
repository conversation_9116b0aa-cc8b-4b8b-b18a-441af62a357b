"""
Unit tests for Busha cryptocurrency service integration.

This module provides comprehensive tests for Busha payment processing including:
- Cryptocurrency payment creation and verification
- Multi-currency crypto support (BTC, ETH, USDT, USDC)
- Exchange rate fetching and conversion
- Webhook signature validation and event processing
- Error handling and retry logic
- Performance and security testing

Implements Task 4.3.3 Phase 4 requirements for comprehensive testing with
>80% test coverage and 100% test success rate validation.
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

import httpx

from app.services.payment.busha_service import BushaService, BushaError
from app.schemas.payment_schemas import WebhookProcessingResult


class TestBushaService:
    """Test suite for BushaService."""

    @pytest.fixture
    def busha_service(self):
        """Create BushaService instance for testing."""
        with patch('app.services.payment.busha_service.PaymentProviderSettings') as mock_settings:
            mock_settings.get_provider_config.return_value = {
                "api_key": "busha_test_api_key",
                "secret_key": "busha_test_secret",
                "base_url": "https://api.busha.co/v1",
                "webhook_secret": "busha_webhook_secret"
            }
            
            service = BushaService()
            return service

    @pytest.fixture
    def sample_exchange_rates(self):
        """Sample exchange rates for testing."""
        return {
            "data": {
                "BTC": Decimal("45000.00"),
                "ETH": Decimal("3000.00"),
                "USDT": Decimal("1.00"),
                "USDC": Decimal("1.00")
            }
        }

    @pytest.fixture
    def sample_crypto_payment_data(self):
        """Sample crypto payment data for testing."""
        return {
            "amount": Decimal("100000.00"),  # NGN
            "currency": "NGN",
            "crypto_currency": "BTC",
            "customer_email": "<EMAIL>",
            "reference": "test_ref_123456789",
            "metadata": {"booking_id": 123}
        }

    @pytest.fixture
    def sample_crypto_payment_response(self):
        """Sample Busha crypto payment response for testing."""
        return {
            "data": {
                "id": "busha_payment_123456789",
                "reference": "test_ref_123456789",
                "amount": 100000.00,
                "currency": "NGN",
                "crypto_currency": "BTC",
                "crypto_amount": 0.00222222,
                "wallet_address": "******************************************",
                "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
                "expires_at": "2024-01-01T12:00:00Z",
                "network": "bitcoin"
            }
        }

    @pytest.fixture
    def sample_webhook_event(self):
        """Sample Busha webhook event for testing."""
        return {
            "event": "payment.completed",
            "data": {
                "reference": "test_ref_123456789",
                "status": "completed",
                "amount": 100000.00,
                "currency": "NGN",
                "crypto_currency": "BTC",
                "crypto_amount": 0.00222222,
                "transaction_hash": "a1b2c3d4e5f6789012345678901234567890abcdef",
                "confirmations": 6,
                "verified_at": "2024-01-01T12:00:00Z"
            }
        }

    @pytest.mark.asyncio
    async def test_get_exchange_rates_success(self, busha_service, sample_exchange_rates):
        """Test successful exchange rates retrieval."""
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.json.return_value = sample_exchange_rates
        mock_response.raise_for_status.return_value = None

        with patch.object(busha_service.http_client, 'get', return_value=mock_response):
            result = await busha_service.get_exchange_rates("NGN")

            # Assertions
            assert result["base_currency"] == "NGN"
            assert result["provider"] == "busha"
            assert "rates" in result
            assert "timestamp" in result

            # Verify HTTP call
            busha_service.http_client.get.assert_called_once_with(
                "https://api.busha.co/v1/exchange-rates",
                params={"base": "NGN"}
            )

    @pytest.mark.asyncio
    async def test_get_exchange_rates_api_error(self, busha_service):
        """Test exchange rates retrieval with API error."""
        # Mock HTTP error
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Invalid currency"
        
        http_error = httpx.HTTPStatusError("Bad Request", request=MagicMock(), response=mock_response)
        
        with patch.object(busha_service.http_client, 'get', side_effect=http_error):
            with pytest.raises(BushaError) as exc_info:
                await busha_service.get_exchange_rates("INVALID")

            assert "Exchange rates fetch failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_crypto_payment_success(self, busha_service, sample_crypto_payment_data, sample_crypto_payment_response):
        """Test successful crypto payment creation."""
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.json.return_value = sample_crypto_payment_response
        mock_response.raise_for_status.return_value = None

        with patch.object(busha_service.http_client, 'post', return_value=mock_response):
            result = await busha_service.create_crypto_payment(**sample_crypto_payment_data)

            # Assertions
            assert result["payment_id"] == "busha_payment_123456789"
            assert result["reference"] == "test_ref_123456789"
            assert result["crypto_currency"] == "BTC"
            assert result["wallet_address"] == "******************************************"
            assert result["status"] == "pending"

            # Verify HTTP call
            busha_service.http_client.post.assert_called_once_with(
                "https://api.busha.co/v1/payments/crypto",
                json={
                    "amount": 100000.0,
                    "currency": "NGN",
                    "crypto_currency": "BTC",
                    "customer_email": "<EMAIL>",
                    "reference": "test_ref_123456789",
                    "metadata": {"booking_id": 123},
                    "callback_url": None
                }
            )

    @pytest.mark.asyncio
    async def test_create_crypto_payment_unsupported_currency(self, busha_service, sample_crypto_payment_data):
        """Test crypto payment creation with unsupported cryptocurrency."""
        sample_crypto_payment_data["crypto_currency"] = "DOGE"  # Unsupported

        with pytest.raises(BushaError) as exc_info:
            await busha_service.create_crypto_payment(**sample_crypto_payment_data)

        assert "Unsupported cryptocurrency: DOGE" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_crypto_payment_success(self, busha_service):
        """Test successful crypto payment verification."""
        # Mock verification response
        verification_data = {
            "data": {
                "reference": "test_ref_123456789",
                "status": "completed",
                "amount": 100000.00,
                "currency": "NGN",
                "crypto_currency": "BTC",
                "crypto_amount": 0.00222222,
                "transaction_hash": "a1b2c3d4e5f6789012345678901234567890abcdef",
                "confirmations": 6,
                "required_confirmations": 3,
                "verified_at": "2024-01-01T12:00:00Z",
                "network": "bitcoin"
            }
        }

        mock_response = MagicMock()
        mock_response.json.return_value = verification_data
        mock_response.raise_for_status.return_value = None

        with patch.object(busha_service.http_client, 'get', return_value=mock_response):
            result = await busha_service.verify_crypto_payment("test_ref_123456789")

            # Assertions
            assert result["reference"] == "test_ref_123456789"
            assert result["status"] == "completed"
            assert result["crypto_currency"] == "BTC"
            assert result["transaction_hash"] == "a1b2c3d4e5f6789012345678901234567890abcdef"
            assert result["confirmations"] == 6
            assert result["required_confirmations"] == 3

            # Verify HTTP call
            busha_service.http_client.get.assert_called_once_with(
                "https://api.busha.co/v1/payments/verify/test_ref_123456789"
            )

    def test_validate_webhook_signature_valid(self, busha_service):
        """Test webhook signature validation with valid signature."""
        payload = b'{"event": "payment.completed", "data": {"reference": "test_ref"}}'
        
        # Calculate expected signature
        import hmac
        import hashlib
        expected_signature = hmac.new(
            "busha_webhook_secret".encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        signature = f"sha256={expected_signature}"

        is_valid = busha_service.validate_webhook_signature(payload, signature)
        assert is_valid is True

    def test_validate_webhook_signature_invalid(self, busha_service):
        """Test webhook signature validation with invalid signature."""
        payload = b'{"event": "payment.completed", "data": {"reference": "test_ref"}}'
        signature = "sha256=invalid_signature"

        is_valid = busha_service.validate_webhook_signature(payload, signature)
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_process_webhook_event_payment_completed(self, busha_service, sample_webhook_event):
        """Test processing payment.completed webhook event."""
        payload = json.dumps(sample_webhook_event).encode('utf-8')
        
        # Calculate valid signature
        import hmac
        import hashlib
        expected_signature = hmac.new(
            "busha_webhook_secret".encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        signature = f"sha256={expected_signature}"

        result = await busha_service.process_webhook_event(payload, signature)

        # Assertions
        assert isinstance(result, WebhookProcessingResult)
        assert result.processed is True
        assert result.event_type == "payment.completed"
        assert result.message == "Crypto payment completed successfully"

    @pytest.mark.asyncio
    async def test_process_webhook_event_invalid_signature(self, busha_service, sample_webhook_event):
        """Test webhook processing with invalid signature."""
        payload = json.dumps(sample_webhook_event).encode('utf-8')
        signature = "sha256=invalid_signature"

        result = await busha_service.process_webhook_event(payload, signature)

        # Assertions
        assert result.processed is False
        assert "Processing failed" in result.message

    @pytest.mark.asyncio
    async def test_get_supported_cryptocurrencies_success(self, busha_service):
        """Test successful supported cryptocurrencies retrieval."""
        # Mock response
        crypto_data = {
            "data": [
                {"symbol": "BTC", "name": "Bitcoin", "network": "bitcoin"},
                {"symbol": "ETH", "name": "Ethereum", "network": "ethereum"},
                {"symbol": "USDT", "name": "Tether", "network": "ethereum"},
                {"symbol": "USDC", "name": "USD Coin", "network": "ethereum"}
            ]
        }

        mock_response = MagicMock()
        mock_response.json.return_value = crypto_data
        mock_response.raise_for_status.return_value = None

        with patch.object(busha_service.http_client, 'get', return_value=mock_response):
            result = await busha_service.get_supported_cryptocurrencies()

            # Assertions
            assert len(result) == 4
            assert result[0]["symbol"] == "BTC"
            assert result[1]["symbol"] == "ETH"

    @pytest.mark.asyncio
    async def test_get_supported_cryptocurrencies_fallback(self, busha_service):
        """Test supported cryptocurrencies with API error fallback."""
        # Mock HTTP error
        with patch.object(busha_service.http_client, 'get', side_effect=Exception("API Error")):
            result = await busha_service.get_supported_cryptocurrencies()

            # Should return default cryptocurrencies
            assert len(result) == 4
            assert any(crypto["symbol"] == "BTC" for crypto in result)
            assert any(crypto["symbol"] == "ETH" for crypto in result)

    @pytest.mark.asyncio
    async def test_multi_cryptocurrency_support(self, busha_service, sample_crypto_payment_response):
        """Test multi-cryptocurrency payment support."""
        cryptocurrencies = ["BTC", "ETH", "USDT", "USDC"]
        
        for crypto in cryptocurrencies:
            # Mock response for each crypto
            response_data = sample_crypto_payment_response.copy()
            response_data["data"]["crypto_currency"] = crypto
            
            mock_response = MagicMock()
            mock_response.json.return_value = response_data
            mock_response.raise_for_status.return_value = None

            with patch.object(busha_service.http_client, 'post', return_value=mock_response):
                result = await busha_service.create_crypto_payment(
                    amount=Decimal("100000.00"),
                    currency="NGN",
                    crypto_currency=crypto,
                    customer_email="<EMAIL>",
                    reference=f"test_ref_{crypto}"
                )

                # Assertions
                assert result["crypto_currency"] == crypto

    @pytest.mark.asyncio
    async def test_close_http_client(self, busha_service):
        """Test HTTP client cleanup."""
        # Mock the aclose method
        busha_service.http_client.aclose = AsyncMock()
        
        await busha_service.close()
        
        # Verify aclose was called
        busha_service.http_client.aclose.assert_called_once()

    def test_busha_error_exception(self):
        """Test BushaError exception."""
        error_message = "Test Busha error message"
        error = BushaError(error_message)
        assert str(error) == error_message
