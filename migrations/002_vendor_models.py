"""Add vendor models and update user model

Revision ID: 002_vendor_models
Revises: 001_initial_auth
Create Date: 2024-01-15 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002_vendor_models'
down_revision = '001_initial_auth'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    
    # Create vendor types enum
    vendor_type_enum = postgresql.ENUM(
        'guide', 'restaurant', 'hotel', 'transport', 'activity_provider',
        'private_beach', 'apartment_rental', 'car_hire', 'security_service', 'other',
        name='vendortype'
    )
    vendor_type_enum.create(op.get_bind())
    
    # Create verification status enum
    verification_status_enum = postgresql.ENUM(
        'pending', 'under_review', 'verified', 'rejected', 'suspended', 'resubmission_required',
        name='verificationstatus'
    )
    verification_status_enum.create(op.get_bind())
    
    # Create marketplace status enum
    marketplace_status_enum = postgresql.ENUM(
        'active', 'inactive', 'paused', 'suspended', 'banned',
        name='marketplacestatus'
    )
    marketplace_status_enum.create(op.get_bind())
    
    # Create document type enum
    document_type_enum = postgresql.ENUM(
        'business_license', 'tax_certificate', 'identity_document', 'professional_certification',
        'insurance_certificate', 'bank_statement', 'utility_bill', 'other',
        name='documenttype'
    )
    document_type_enum.create(op.get_bind())
    
    # Create document status enum
    document_status_enum = postgresql.ENUM(
        'pending', 'approved', 'rejected', 'expired',
        name='documentstatus'
    )
    document_status_enum.create(op.get_bind())
    
    # Create vendors table
    op.create_table(
        'vendors',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        
        # Core vendor information
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('business_name', sa.String(length=255), nullable=False),
        sa.Column('business_type', vendor_type_enum, nullable=False),
        sa.Column('business_registration_number', sa.String(length=100), nullable=True),
        sa.Column('tax_id', sa.String(length=50), nullable=True),
        
        # Marketplace-specific fields
        sa.Column('verification_status', verification_status_enum, nullable=False, server_default='pending'),
        sa.Column('marketplace_status', marketplace_status_enum, nullable=False, server_default='inactive'),
        sa.Column('onboarding_completed', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('onboarding_step', sa.Integer(), nullable=False, server_default='1'),
        
        # Performance metrics
        sa.Column('commission_rate', sa.Numeric(precision=5, scale=4), nullable=False, server_default='0.15'),
        sa.Column('total_earnings', sa.Numeric(precision=12, scale=2), nullable=False, server_default='0.00'),
        sa.Column('total_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=False, server_default='0.00'),
        sa.Column('total_reviews', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('response_rate', sa.Numeric(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('response_time_hours', sa.Numeric(precision=8, scale=2), nullable=False, server_default='24.00'),
        
        # SEO and optimization
        sa.Column('seo_score', sa.Numeric(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('marketplace_ranking', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('listing_quality_score', sa.Numeric(precision=5, scale=2), nullable=False, server_default='0.00'),
        
        # Verification and compliance
        sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('verification_notes', sa.Text(), nullable=True),
        sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True),
        
        # Constraints
        sa.CheckConstraint('onboarding_step >= 1 AND onboarding_step <= 6', name='check_onboarding_step_range'),
        sa.CheckConstraint('commission_rate >= 0.0 AND commission_rate <= 1.0', name='check_commission_rate_range'),
        sa.CheckConstraint('average_rating >= 0.0 AND average_rating <= 5.0', name='check_average_rating_range'),
        sa.CheckConstraint('response_rate >= 0.0 AND response_rate <= 100.0', name='check_response_rate_range'),
        sa.CheckConstraint('seo_score >= 0.0 AND seo_score <= 100.0', name='check_seo_score_range'),
        sa.CheckConstraint('listing_quality_score >= 0.0 AND listing_quality_score <= 100.0', name='check_listing_quality_score_range'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Unique constraints
        sa.UniqueConstraint('user_id'),
        sa.UniqueConstraint('business_registration_number'),
        sa.UniqueConstraint('tax_id'),
        sa.UniqueConstraint('uuid'),
    )
    
    # Create vendor profiles table
    op.create_table(
        'vendor_profiles',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        
        # Foreign key to vendor
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        
        # Business description and story
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('short_description', sa.String(length=500), nullable=True),
        sa.Column('tagline', sa.String(length=255), nullable=True),
        
        # Contact information
        sa.Column('contact_phone', sa.String(length=20), nullable=True),
        sa.Column('contact_email', sa.String(length=255), nullable=True),
        sa.Column('website_url', sa.String(length=255), nullable=True),
        
        # Business address
        sa.Column('business_address', sa.Text(), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=True),
        sa.Column('state', sa.String(length=100), nullable=True),
        sa.Column('country', sa.String(length=100), nullable=True),
        sa.Column('postal_code', sa.String(length=20), nullable=True),
        sa.Column('latitude', sa.Numeric(precision=10, scale=8), nullable=True),
        sa.Column('longitude', sa.Numeric(precision=11, scale=8), nullable=True),
        
        # Operating details
        sa.Column('operating_hours', sa.JSON(), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=True, server_default='UTC'),
        sa.Column('social_media_links', sa.JSON(), nullable=True),
        sa.Column('languages_spoken', postgresql.ARRAY(sa.String(length=50)), nullable=True),
        sa.Column('specializations', postgresql.ARRAY(sa.String(length=100)), nullable=True),
        sa.Column('certifications', postgresql.ARRAY(sa.String(length=255)), nullable=True),
        
        # Capacity and logistics
        sa.Column('max_group_size', sa.Integer(), nullable=True),
        sa.Column('min_advance_booking', sa.Integer(), nullable=True, server_default='24'),
        sa.Column('cancellation_policy', sa.Text(), nullable=True),
        
        # Media
        sa.Column('logo_url', sa.String(length=500), nullable=True),
        sa.Column('cover_image_url', sa.String(length=500), nullable=True),
        sa.Column('gallery_images', postgresql.ARRAY(sa.String(length=500)), nullable=True),
        
        # Business verification details
        sa.Column('years_in_business', sa.Integer(), nullable=True),
        sa.Column('business_license_number', sa.String(length=100), nullable=True),
        sa.Column('insurance_details', sa.JSON(), nullable=True),
        
        # Marketplace preferences
        sa.Column('auto_accept_bookings', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('instant_booking_enabled', sa.Boolean(), nullable=False, server_default='false'),
        
        # Additional metadata
        sa.Column('additional_info', sa.JSON(), nullable=True),
        
        # Constraints
        sa.CheckConstraint('max_group_size > 0', name='check_max_group_size_positive'),
        sa.CheckConstraint('min_advance_booking >= 0', name='check_min_advance_booking_non_negative'),
        sa.CheckConstraint('years_in_business >= 0', name='check_years_in_business_non_negative'),
        sa.CheckConstraint('latitude >= -90.0 AND latitude <= 90.0', name='check_latitude_range'),
        sa.CheckConstraint('longitude >= -180.0 AND longitude <= 180.0', name='check_longitude_range'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Unique constraints
        sa.UniqueConstraint('vendor_id'),
        sa.UniqueConstraint('uuid'),
    )
    
    # Create vendor documents table
    op.create_table(
        'vendor_documents',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        
        # Foreign key to vendor
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        
        # Document information
        sa.Column('document_type', document_type_enum, nullable=False),
        sa.Column('document_name', sa.String(length=255), nullable=False),
        sa.Column('document_url', sa.String(length=500), nullable=False),
        sa.Column('file_size', sa.Integer(), nullable=False),
        sa.Column('file_type', sa.String(length=50), nullable=False),
        
        # Document status and verification
        sa.Column('status', document_status_enum, nullable=False, server_default='pending'),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reviewed_by', sa.Integer(), nullable=True),
        sa.Column('review_notes', sa.Text(), nullable=True),
        
        # Document metadata
        sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('document_number', sa.String(length=100), nullable=True),
        sa.Column('issuing_authority', sa.String(length=255), nullable=True),
        sa.Column('verification_data', sa.JSON(), nullable=True),
        sa.Column('is_primary', sa.Boolean(), nullable=False, server_default='false'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['reviewed_by'], ['users.id']),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Unique constraints
        sa.UniqueConstraint('vendor_id', 'document_type', 'is_primary', name='uq_vendor_primary_document'),
        sa.UniqueConstraint('uuid'),
    )
    
    # Add vendor_id column to users table
    op.add_column('users', sa.Column('vendor_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_users_vendor_id_vendors', 'users', 'vendors', ['vendor_id'], ['id'], ondelete='SET NULL')
    
    # Create indexes for performance
    
    # Vendor indexes
    op.create_index('idx_vendor_business_type_status', 'vendors', ['business_type', 'marketplace_status'])
    op.create_index('idx_vendor_verification_status', 'vendors', ['verification_status', 'created_at'])
    op.create_index('idx_vendor_marketplace_ranking', 'vendors', ['marketplace_ranking', 'business_type'])
    op.create_index('idx_vendor_performance', 'vendors', ['average_rating', 'total_bookings'])
    op.create_index('idx_vendor_activity', 'vendors', ['last_activity_at', 'marketplace_status'])
    op.create_index('idx_vendor_onboarding', 'vendors', ['onboarding_completed', 'onboarding_step'])
    op.create_index('ix_vendors_business_name', 'vendors', ['business_name'])
    op.create_index('ix_vendors_business_registration_number', 'vendors', ['business_registration_number'])
    op.create_index('ix_vendors_business_type', 'vendors', ['business_type'])
    op.create_index('ix_vendors_created_at', 'vendors', ['created_at'])
    op.create_index('ix_vendors_created_by', 'vendors', ['created_by'])
    op.create_index('ix_vendors_last_activity_at', 'vendors', ['last_activity_at'])
    op.create_index('ix_vendors_marketplace_status', 'vendors', ['marketplace_status'])
    op.create_index('ix_vendors_onboarding_completed', 'vendors', ['onboarding_completed'])
    op.create_index('ix_vendors_tax_id', 'vendors', ['tax_id'])
    op.create_index('ix_vendors_updated_by', 'vendors', ['updated_by'])
    op.create_index('ix_vendors_user_id', 'vendors', ['user_id'])
    op.create_index('ix_vendors_uuid', 'vendors', ['uuid'])
    op.create_index('ix_vendors_verification_status', 'vendors', ['verification_status'])
    
    # Vendor profile indexes
    op.create_index('idx_vendor_profile_location', 'vendor_profiles', ['city', 'state', 'country'])
    op.create_index('idx_vendor_profile_coordinates', 'vendor_profiles', ['latitude', 'longitude'])
    op.create_index('idx_vendor_profile_contact', 'vendor_profiles', ['contact_phone', 'contact_email'])
    op.create_index('ix_vendor_profiles_city', 'vendor_profiles', ['city'])
    op.create_index('ix_vendor_profiles_contact_email', 'vendor_profiles', ['contact_email'])
    op.create_index('ix_vendor_profiles_contact_phone', 'vendor_profiles', ['contact_phone'])
    op.create_index('ix_vendor_profiles_country', 'vendor_profiles', ['country'])
    op.create_index('ix_vendor_profiles_created_at', 'vendor_profiles', ['created_at'])
    op.create_index('ix_vendor_profiles_created_by', 'vendor_profiles', ['created_by'])
    op.create_index('ix_vendor_profiles_state', 'vendor_profiles', ['state'])
    op.create_index('ix_vendor_profiles_updated_by', 'vendor_profiles', ['updated_by'])
    op.create_index('ix_vendor_profiles_uuid', 'vendor_profiles', ['uuid'])
    op.create_index('ix_vendor_profiles_vendor_id', 'vendor_profiles', ['vendor_id'])
    
    # Vendor document indexes
    op.create_index('idx_vendor_document_type_status', 'vendor_documents', ['document_type', 'status'])
    op.create_index('idx_vendor_document_expiry', 'vendor_documents', ['expiry_date', 'status'])
    op.create_index('idx_vendor_document_review', 'vendor_documents', ['reviewed_at', 'reviewed_by'])
    op.create_index('ix_vendor_documents_created_at', 'vendor_documents', ['created_at'])
    op.create_index('ix_vendor_documents_created_by', 'vendor_documents', ['created_by'])
    op.create_index('ix_vendor_documents_document_type', 'vendor_documents', ['document_type'])
    op.create_index('ix_vendor_documents_expiry_date', 'vendor_documents', ['expiry_date'])
    op.create_index('ix_vendor_documents_status', 'vendor_documents', ['status'])
    op.create_index('ix_vendor_documents_updated_by', 'vendor_documents', ['updated_by'])
    op.create_index('ix_vendor_documents_uuid', 'vendor_documents', ['uuid'])
    op.create_index('ix_vendor_documents_vendor_id', 'vendor_documents', ['vendor_id'])
    
    # User table index for vendor relationship
    op.create_index('idx_user_vendor_role', 'users', ['vendor_id', 'role'])
    op.create_index('ix_users_vendor_id', 'users', ['vendor_id'])


def downgrade() -> None:
    """Downgrade database schema."""
    
    # Drop indexes
    op.drop_index('ix_users_vendor_id', table_name='users')
    op.drop_index('idx_user_vendor_role', table_name='users')
    
    # Drop vendor document indexes
    op.drop_index('ix_vendor_documents_vendor_id', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_uuid', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_updated_by', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_status', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_expiry_date', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_document_type', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_created_by', table_name='vendor_documents')
    op.drop_index('ix_vendor_documents_created_at', table_name='vendor_documents')
    op.drop_index('idx_vendor_document_review', table_name='vendor_documents')
    op.drop_index('idx_vendor_document_expiry', table_name='vendor_documents')
    op.drop_index('idx_vendor_document_type_status', table_name='vendor_documents')
    
    # Drop vendor profile indexes
    op.drop_index('ix_vendor_profiles_vendor_id', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_uuid', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_updated_by', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_state', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_created_by', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_created_at', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_country', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_contact_phone', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_contact_email', table_name='vendor_profiles')
    op.drop_index('ix_vendor_profiles_city', table_name='vendor_profiles')
    op.drop_index('idx_vendor_profile_contact', table_name='vendor_profiles')
    op.drop_index('idx_vendor_profile_coordinates', table_name='vendor_profiles')
    op.drop_index('idx_vendor_profile_location', table_name='vendor_profiles')
    
    # Drop vendor indexes
    op.drop_index('ix_vendors_verification_status', table_name='vendors')
    op.drop_index('ix_vendors_uuid', table_name='vendors')
    op.drop_index('ix_vendors_user_id', table_name='vendors')
    op.drop_index('ix_vendors_updated_by', table_name='vendors')
    op.drop_index('ix_vendors_tax_id', table_name='vendors')
    op.drop_index('ix_vendors_onboarding_completed', table_name='vendors')
    op.drop_index('ix_vendors_marketplace_status', table_name='vendors')
    op.drop_index('ix_vendors_last_activity_at', table_name='vendors')
    op.drop_index('ix_vendors_created_by', table_name='vendors')
    op.drop_index('ix_vendors_created_at', table_name='vendors')
    op.drop_index('ix_vendors_business_type', table_name='vendors')
    op.drop_index('ix_vendors_business_registration_number', table_name='vendors')
    op.drop_index('ix_vendors_business_name', table_name='vendors')
    op.drop_index('idx_vendor_onboarding', table_name='vendors')
    op.drop_index('idx_vendor_activity', table_name='vendors')
    op.drop_index('idx_vendor_performance', table_name='vendors')
    op.drop_index('idx_vendor_marketplace_ranking', table_name='vendors')
    op.drop_index('idx_vendor_verification_status', table_name='vendors')
    op.drop_index('idx_vendor_business_type_status', table_name='vendors')
    
    # Drop foreign key and column from users table
    op.drop_constraint('fk_users_vendor_id_vendors', 'users', type_='foreignkey')
    op.drop_column('users', 'vendor_id')
    
    # Drop tables
    op.drop_table('vendor_documents')
    op.drop_table('vendor_profiles')
    op.drop_table('vendors')
    
    # Drop enums
    op.execute('DROP TYPE documentstatus')
    op.execute('DROP TYPE documenttype')
    op.execute('DROP TYPE marketplacestatus')
    op.execute('DROP TYPE verificationstatus')
    op.execute('DROP TYPE vendortype')
