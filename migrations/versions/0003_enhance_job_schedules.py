"""Enhance job schedules for advanced scheduling system

Revision ID: 0003_enhance_job_schedules
Revises: 0002_add_workflow_models
Create Date: 2024-12-28 18:00:00.000000

Enhanced for Task 6.2.2 Phase 3: Advanced Scheduling System with:
- Advanced cron expression support with validation
- Named schedule patterns (@daily, @weekly, etc.)
- Comprehensive timezone handling with DST support
- Business day and holiday calendar integration
- Schedule conflict detection and resolution
- Performance optimization for <100ms schedule calculation
- Execution tracking and missed run handling
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0003_enhance_job_schedules'
down_revision = 'promotional_system_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema for enhanced job schedules."""

    # Create enum types for advanced scheduling
    schedule_type_enum = postgresql.ENUM(
        'cron', 'interval', 'named', 'once',
        name='scheduletype',
        create_type=False
    )
    schedule_type_enum.create(op.get_bind(), checkfirst=True)

    named_schedule_enum = postgresql.ENUM(
        '@yearly', '@annually', '@monthly', '@weekly',
        '@daily', '@hourly', '@minutely', '@reboot',
        name='namedschedule',
        create_type=False
    )
    named_schedule_enum.create(op.get_bind(), checkfirst=True)

    holiday_calendar_enum = postgresql.ENUM(
        'US', 'UK', 'EU', 'CA', 'AU', 'JP', 'CUSTOM',
        name='holidaycalendar',
        create_type=False
    )
    holiday_calendar_enum.create(op.get_bind(), checkfirst=True)

    # Add new columns to job_schedules table
    op.add_column('job_schedules', sa.Column('named_schedule', named_schedule_enum, nullable=True))
    op.add_column('job_schedules', sa.Column('schedule_type', schedule_type_enum, nullable=False, server_default='cron'))
    op.add_column('job_schedules', sa.Column('business_days_only', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('job_schedules', sa.Column('exclude_holidays', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('job_schedules', sa.Column('holiday_calendar', holiday_calendar_enum, nullable=True))

    # Status and control fields
    op.add_column('job_schedules', sa.Column('next_run_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('job_schedules', sa.Column('run_count', sa.Integer(), nullable=False, server_default='0'))

    # Execution tracking fields
    op.add_column('job_schedules', sa.Column('success_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('job_schedules', sa.Column('failure_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('job_schedules', sa.Column('last_success_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('job_schedules', sa.Column('last_failure_at', sa.DateTime(timezone=True), nullable=True))

    # Conflict resolution and priority fields
    op.add_column('job_schedules', sa.Column('priority', sa.Integer(), nullable=False, server_default='5'))
    op.add_column('job_schedules', sa.Column('allow_overlap', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('job_schedules', sa.Column('max_concurrent_runs', sa.Integer(), nullable=False, server_default='1'))

    # Missed execution handling fields
    op.add_column('job_schedules', sa.Column('catchup_missed_runs', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('job_schedules', sa.Column('max_catchup_runs', sa.Integer(), nullable=False, server_default='3'))

    # Performance and monitoring fields
    op.add_column('job_schedules', sa.Column('average_duration_seconds', sa.Numeric(10, 3), nullable=True))
    op.add_column('job_schedules', sa.Column('last_duration_seconds', sa.Numeric(10, 3), nullable=True))

    # Audit fields
    op.add_column('job_schedules', sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('job_schedules', sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True))

    # Make name column unique
    op.create_unique_constraint('uq_job_schedules_name', 'job_schedules', ['name'])

    # Add enhanced check constraints for data validation
    op.create_check_constraint(
        'valid_priority_range',
        'job_schedules',
        'priority >= 1 AND priority <= 10'
    )
    op.create_check_constraint(
        'positive_max_concurrent_runs',
        'job_schedules',
        'max_concurrent_runs >= 1'
    )
    op.create_check_constraint(
        'non_negative_max_catchup_runs',
        'job_schedules',
        'max_catchup_runs >= 0'
    )
    op.create_check_constraint(
        'non_negative_run_count',
        'job_schedules',
        'run_count >= 0'
    )
    op.create_check_constraint(
        'non_negative_success_count',
        'job_schedules',
        'success_count >= 0'
    )
    op.create_check_constraint(
        'non_negative_failure_count',
        'job_schedules',
        'failure_count >= 0'
    )
    op.create_check_constraint(
        'schedule_configuration_required',
        'job_schedules',
        '(cron_expression IS NOT NULL) OR (interval_seconds IS NOT NULL) OR (named_schedule IS NOT NULL)'
    )
    op.create_check_constraint(
        'valid_date_range',
        'job_schedules',
        'start_date IS NULL OR end_date IS NULL OR start_date < end_date'
    )

    # Add performance indexes for schedule calculation (<100ms target)
    op.create_index('idx_schedule_active_next_run', 'job_schedules', ['is_active', 'next_run_at'])
    op.create_index('idx_schedule_workflow_active', 'job_schedules', ['workflow_definition_id', 'is_active'])
    op.create_index('idx_schedule_type_active', 'job_schedules', ['schedule_type', 'is_active'])
    op.create_index('idx_schedule_timezone', 'job_schedules', ['timezone'])
    op.create_index('idx_schedule_priority', 'job_schedules', ['priority', 'is_active'])
    op.create_index('idx_schedule_last_run', 'job_schedules', ['last_run_at'])
    op.create_index('idx_schedule_business_days', 'job_schedules', ['business_days_only', 'exclude_holidays'])

    # Composite indexes for complex queries
    op.create_index('idx_schedule_execution_tracking', 'job_schedules', ['is_active', 'next_run_at', 'priority'])
    op.create_index('idx_schedule_conflict_resolution', 'job_schedules', ['allow_overlap', 'max_concurrent_runs', 'priority'])


def downgrade():
    """Downgrade database schema for enhanced job schedules."""

    # Drop indexes
    op.drop_index('idx_schedule_conflict_resolution', table_name='job_schedules')
    op.drop_index('idx_schedule_execution_tracking', table_name='job_schedules')
    op.drop_index('idx_schedule_business_days', table_name='job_schedules')
    op.drop_index('idx_schedule_last_run', table_name='job_schedules')
    op.drop_index('idx_schedule_priority', table_name='job_schedules')
    op.drop_index('idx_schedule_timezone', table_name='job_schedules')
    op.drop_index('idx_schedule_type_active', table_name='job_schedules')
    op.drop_index('idx_schedule_workflow_active', table_name='job_schedules')
    op.drop_index('idx_schedule_active_next_run', table_name='job_schedules')

    # Drop check constraints
    op.drop_constraint('valid_date_range', 'job_schedules', type_='check')
    op.drop_constraint('schedule_configuration_required', 'job_schedules', type_='check')
    op.drop_constraint('non_negative_failure_count', 'job_schedules', type_='check')
    op.drop_constraint('non_negative_success_count', 'job_schedules', type_='check')
    op.drop_constraint('non_negative_run_count', 'job_schedules', type_='check')
    op.drop_constraint('non_negative_max_catchup_runs', 'job_schedules', type_='check')
    op.drop_constraint('positive_max_concurrent_runs', 'job_schedules', type_='check')
    op.drop_constraint('valid_priority_range', 'job_schedules', type_='check')

    # Drop unique constraint
    op.drop_constraint('uq_job_schedules_name', 'job_schedules', type_='unique')

    # Drop columns
    op.drop_column('job_schedules', 'updated_by')
    op.drop_column('job_schedules', 'created_by')
    op.drop_column('job_schedules', 'last_duration_seconds')
    op.drop_column('job_schedules', 'average_duration_seconds')
    op.drop_column('job_schedules', 'max_catchup_runs')
    op.drop_column('job_schedules', 'catchup_missed_runs')
    op.drop_column('job_schedules', 'max_concurrent_runs')
    op.drop_column('job_schedules', 'allow_overlap')
    op.drop_column('job_schedules', 'priority')
    op.drop_column('job_schedules', 'last_failure_at')
    op.drop_column('job_schedules', 'last_success_at')
    op.drop_column('job_schedules', 'failure_count')
    op.drop_column('job_schedules', 'success_count')
    op.drop_column('job_schedules', 'run_count')
    op.drop_column('job_schedules', 'next_run_at')
    op.drop_column('job_schedules', 'holiday_calendar')
    op.drop_column('job_schedules', 'exclude_holidays')
    op.drop_column('job_schedules', 'business_days_only')
    op.drop_column('job_schedules', 'schedule_type')
    op.drop_column('job_schedules', 'named_schedule')

    # Drop enum types
    holiday_calendar_enum = postgresql.ENUM(name='holidaycalendar')
    holiday_calendar_enum.drop(op.get_bind(), checkfirst=True)

    named_schedule_enum = postgresql.ENUM(name='namedschedule')
    named_schedule_enum.drop(op.get_bind(), checkfirst=True)

    schedule_type_enum = postgresql.ENUM(name='scheduletype')
    schedule_type_enum.drop(op.get_bind(), checkfirst=True)
