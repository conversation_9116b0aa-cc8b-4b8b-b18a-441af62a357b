"""Add vendor dashboard tables for Task 3.2 & 3.3

Revision ID: vendor_dashboard_tables
Revises: 20250527_2211_d8b8c4817a77_add_review_management_system_tables_for_
Create Date: 2025-01-31 20:00:00.000000+00:00

This migration creates comprehensive vendor dashboard and service management tables including:
- VendorDashboardMetrics: Performance metrics and KPI tracking
- VendorActivityFeed: Activity logging and feed management
- VendorNotification: Notification system with priority and status
- VendorQuickAction: Quick action items and workflow optimization
- VendorAnalytics: Comprehensive analytics and reporting data

Implements Task 3.2 (Vendor Dashboard & Analytics) and Task 3.3 (Service Management System)
requirements with production-grade PostgreSQL optimization and comprehensive indexing.
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from datetime import datetime, timezone

# revision identifiers, used by Alembic.
revision = 'vendor_dashboard_tables'
down_revision = '20250131_1500_add_scaling_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema with vendor dashboard tables."""

    # Create enum types for vendor dashboard
    metric_type_enum = postgresql.ENUM(
        'revenue', 'bookings', 'customers', 'ratings', 'views', 'conversions',
        'response_time', 'completion_rate', 'cancellation_rate', 'repeat_customers',
        name='metrictype'
    )
    metric_type_enum.create(op.get_bind())

    activity_type_enum = postgresql.ENUM(
        'booking_received', 'booking_confirmed', 'booking_completed', 'booking_cancelled',
        'review_received', 'payment_received', 'service_updated', 'profile_updated',
        'message_received', 'promotion_started', 'promotion_ended', 'milestone_achieved',
        name='activitytype'
    )
    activity_type_enum.create(op.get_bind())

    notification_type_enum = postgresql.ENUM(
        'booking_request', 'booking_update', 'payment_received', 'review_received',
        'message_received', 'promotion_update', 'system_alert', 'performance_alert',
        'verification_update', 'policy_update', 'feature_announcement', 'maintenance_notice',
        name='notificationtype'
    )
    notification_type_enum.create(op.get_bind())

    notification_priority_enum = postgresql.ENUM(
        'low', 'medium', 'high', 'critical',
        name='notificationpriority'
    )
    notification_priority_enum.create(op.get_bind())

    action_type_enum = postgresql.ENUM(
        'complete_profile', 'upload_documents', 'add_services', 'set_availability',
        'respond_to_booking', 'update_pricing', 'improve_seo', 'add_photos',
        'respond_to_review', 'verify_account', 'setup_payments', 'optimize_listing',
        name='actiontype'
    )
    action_type_enum.create(op.get_bind())

    # Create vendor_dashboard_metrics table
    op.create_table(
        'vendor_dashboard_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('metric_type', metric_type_enum, nullable=False),
        sa.Column('metric_name', sa.String(length=255), nullable=False),
        sa.Column('metric_value', sa.Numeric(precision=15, scale=4), nullable=False),
        sa.Column('previous_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('percentage_change', sa.Float(), nullable=True),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
        sa.Column('calculation_method', sa.String(length=100), nullable=True),
        sa.Column('target_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('benchmark_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('is_trending_up', sa.Boolean(), nullable=False, default=False),
        sa.Column('trend_direction', sa.String(length=20), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.CheckConstraint('metric_value >= 0', name='ck_vendor_dashboard_metrics_value_positive'),
        sa.CheckConstraint('period_end > period_start', name='ck_vendor_dashboard_metrics_period_valid'),
    )

    # Create vendor_activity_feed table
    op.create_table(
        'vendor_activity_feed',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('activity_type', activity_type_enum, nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('entity_type', sa.String(length=50), nullable=True),
        sa.Column('entity_id', sa.Integer(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_important', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_read', sa.Boolean(), nullable=False, default=False),
        sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
    )

    # Create vendor_notifications table
    op.create_table(
        'vendor_notifications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('notification_type', notification_type_enum, nullable=False),
        sa.Column('priority', notification_priority_enum, nullable=False, default='medium'),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('action_url', sa.String(length=500), nullable=True),
        sa.Column('action_text', sa.String(length=100), nullable=True),
        sa.Column('entity_type', sa.String(length=50), nullable=True),
        sa.Column('entity_id', sa.Integer(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_read', sa.Boolean(), nullable=False, default=False),
        sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
    )

    # Create vendor_quick_actions table
    op.create_table(
        'vendor_quick_actions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('action_type', action_type_enum, nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('action_url', sa.String(length=500), nullable=False),
        sa.Column('icon', sa.String(length=100), nullable=True),
        sa.Column('priority_score', sa.Float(), nullable=False, default=5.0),
        sa.Column('estimated_time_minutes', sa.Integer(), nullable=True),
        sa.Column('potential_impact', sa.String(length=50), nullable=True),
        sa.Column('completion_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('is_completed', sa.Boolean(), nullable=False, default=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_dismissed', sa.Boolean(), nullable=False, default=False),
        sa.Column('dismissed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.CheckConstraint('priority_score >= 0.0 AND priority_score <= 10.0', name='ck_vendor_quick_actions_priority_range'),
        sa.CheckConstraint('completion_rate >= 0.0 AND completion_rate <= 100.0', name='ck_vendor_quick_actions_completion_range'),
    )

    # Create vendor_analytics table
    op.create_table(
        'vendor_analytics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('analytics_type', sa.String(length=50), nullable=False),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),

        # Revenue analytics
        sa.Column('total_revenue', sa.Numeric(precision=12, scale=2), nullable=False, default=0.0),
        sa.Column('average_order_value', sa.Numeric(precision=10, scale=2), nullable=False, default=0.0),
        sa.Column('revenue_growth_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('revenue_by_service', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Booking analytics
        sa.Column('total_bookings', sa.Integer(), nullable=False, default=0),
        sa.Column('confirmed_bookings', sa.Integer(), nullable=False, default=0),
        sa.Column('cancelled_bookings', sa.Integer(), nullable=False, default=0),
        sa.Column('booking_conversion_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('average_booking_value', sa.Numeric(precision=10, scale=2), nullable=False, default=0.0),

        # Service performance
        sa.Column('total_services', sa.Integer(), nullable=False, default=0),
        sa.Column('active_services', sa.Integer(), nullable=False, default=0),
        sa.Column('service_views', sa.Integer(), nullable=False, default=0),
        sa.Column('service_inquiries', sa.Integer(), nullable=False, default=0),
        sa.Column('service_conversion_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('top_performing_services', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Customer analytics
        sa.Column('total_customers', sa.Integer(), nullable=False, default=0),
        sa.Column('new_customers', sa.Integer(), nullable=False, default=0),
        sa.Column('returning_customers', sa.Integer(), nullable=False, default=0),
        sa.Column('customer_retention_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('customer_lifetime_value', sa.Float(), nullable=False, default=0.0),
        sa.Column('customer_satisfaction_score', sa.Float(), nullable=False, default=0.0),

        # Quality and content metrics
        sa.Column('average_rating', sa.Float(), nullable=False, default=0.0),
        sa.Column('total_reviews', sa.Integer(), nullable=False, default=0),
        sa.Column('content_quality_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('content_moderation_status', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('seo_performance_score', sa.Float(), nullable=False, default=0.0),

        # Marketplace performance
        sa.Column('marketplace_ranking', sa.Integer(), nullable=True),
        sa.Column('visibility_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('search_impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('search_clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('search_ctr', sa.Float(), nullable=False, default=0.0),
        sa.Column('featured_placements', sa.Integer(), nullable=False, default=0),

        # Additional analytics data
        sa.Column('analytics_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc)),

        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.CheckConstraint('period_end > period_start', name='ck_vendor_analytics_period_valid'),
        sa.CheckConstraint('total_revenue >= 0', name='ck_vendor_analytics_revenue_positive'),
        sa.CheckConstraint('average_rating >= 0.0 AND average_rating <= 5.0', name='ck_vendor_analytics_rating_range'),
        sa.CheckConstraint('booking_conversion_rate >= 0.0 AND booking_conversion_rate <= 100.0', name='ck_vendor_analytics_conversion_range'),
    )

    # Create comprehensive indexes for performance optimization

    # Vendor Dashboard Metrics indexes
    op.create_index('idx_vendor_dashboard_metrics_vendor_type', 'vendor_dashboard_metrics', ['vendor_id', 'metric_type'])
    op.create_index('idx_vendor_dashboard_metrics_period', 'vendor_dashboard_metrics', ['period_start', 'period_end'])
    op.create_index('idx_vendor_dashboard_metrics_trending', 'vendor_dashboard_metrics', ['vendor_id', 'is_trending_up'])
    op.create_index('idx_vendor_dashboard_metrics_created', 'vendor_dashboard_metrics', ['created_at'])

    # Vendor Activity Feed indexes
    op.create_index('idx_vendor_activity_feed_vendor_type', 'vendor_activity_feed', ['vendor_id', 'activity_type'])
    op.create_index('idx_vendor_activity_feed_important', 'vendor_activity_feed', ['vendor_id', 'is_important'])
    op.create_index('idx_vendor_activity_feed_unread', 'vendor_activity_feed', ['vendor_id', 'is_read'])
    op.create_index('idx_vendor_activity_feed_entity', 'vendor_activity_feed', ['entity_type', 'entity_id'])
    op.create_index('idx_vendor_activity_feed_created', 'vendor_activity_feed', ['created_at'])

    # Vendor Notifications indexes
    op.create_index('idx_vendor_notifications_vendor_priority', 'vendor_notifications', ['vendor_id', 'priority'])
    op.create_index('idx_vendor_notifications_unread', 'vendor_notifications', ['vendor_id', 'is_read'])
    op.create_index('idx_vendor_notifications_type', 'vendor_notifications', ['vendor_id', 'notification_type'])
    op.create_index('idx_vendor_notifications_expires', 'vendor_notifications', ['expires_at'])
    op.create_index('idx_vendor_notifications_created', 'vendor_notifications', ['created_at'])

    # Vendor Quick Actions indexes
    op.create_index('idx_vendor_quick_actions_vendor_type', 'vendor_quick_actions', ['vendor_id', 'action_type'])
    op.create_index('idx_vendor_quick_actions_priority', 'vendor_quick_actions', ['vendor_id', 'priority_score'])
    op.create_index('idx_vendor_quick_actions_completed', 'vendor_quick_actions', ['vendor_id', 'is_completed'])
    op.create_index('idx_vendor_quick_actions_dismissed', 'vendor_quick_actions', ['vendor_id', 'is_dismissed'])

    # Vendor Analytics indexes
    op.create_index('idx_vendor_analytics_vendor_type', 'vendor_analytics', ['vendor_id', 'analytics_type'])
    op.create_index('idx_vendor_analytics_period', 'vendor_analytics', ['vendor_id', 'period_start', 'period_end'])
    op.create_index('idx_vendor_analytics_revenue', 'vendor_analytics', ['vendor_id', 'total_revenue'])
    op.create_index('idx_vendor_analytics_rating', 'vendor_analytics', ['vendor_id', 'average_rating'])
    op.create_index('idx_vendor_analytics_created', 'vendor_analytics', ['created_at'])

    # GIN indexes for JSONB columns
    op.create_index('idx_vendor_dashboard_metrics_metadata_gin', 'vendor_dashboard_metrics', ['metadata'], postgresql_using='gin')
    op.create_index('idx_vendor_activity_feed_metadata_gin', 'vendor_activity_feed', ['metadata'], postgresql_using='gin')
    op.create_index('idx_vendor_notifications_metadata_gin', 'vendor_notifications', ['metadata'], postgresql_using='gin')
    op.create_index('idx_vendor_quick_actions_metadata_gin', 'vendor_quick_actions', ['metadata'], postgresql_using='gin')
    op.create_index('idx_vendor_analytics_revenue_by_service_gin', 'vendor_analytics', ['revenue_by_service'], postgresql_using='gin')
    op.create_index('idx_vendor_analytics_top_services_gin', 'vendor_analytics', ['top_performing_services'], postgresql_using='gin')
    op.create_index('idx_vendor_analytics_metadata_gin', 'vendor_analytics', ['analytics_metadata'], postgresql_using='gin')


def downgrade() -> None:
    """Downgrade database schema by removing vendor dashboard tables."""

    # Drop indexes first
    op.drop_index('idx_vendor_analytics_metadata_gin', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_top_services_gin', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_revenue_by_service_gin', table_name='vendor_analytics')
    op.drop_index('idx_vendor_quick_actions_metadata_gin', table_name='vendor_quick_actions')
    op.drop_index('idx_vendor_notifications_metadata_gin', table_name='vendor_notifications')
    op.drop_index('idx_vendor_activity_feed_metadata_gin', table_name='vendor_activity_feed')
    op.drop_index('idx_vendor_dashboard_metrics_metadata_gin', table_name='vendor_dashboard_metrics')

    op.drop_index('idx_vendor_analytics_created', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_rating', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_revenue', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_period', table_name='vendor_analytics')
    op.drop_index('idx_vendor_analytics_vendor_type', table_name='vendor_analytics')

    op.drop_index('idx_vendor_quick_actions_dismissed', table_name='vendor_quick_actions')
    op.drop_index('idx_vendor_quick_actions_completed', table_name='vendor_quick_actions')
    op.drop_index('idx_vendor_quick_actions_priority', table_name='vendor_quick_actions')
    op.drop_index('idx_vendor_quick_actions_vendor_type', table_name='vendor_quick_actions')

    op.drop_index('idx_vendor_notifications_created', table_name='vendor_notifications')
    op.drop_index('idx_vendor_notifications_expires', table_name='vendor_notifications')
    op.drop_index('idx_vendor_notifications_type', table_name='vendor_notifications')
    op.drop_index('idx_vendor_notifications_unread', table_name='vendor_notifications')
    op.drop_index('idx_vendor_notifications_vendor_priority', table_name='vendor_notifications')

    op.drop_index('idx_vendor_activity_feed_created', table_name='vendor_activity_feed')
    op.drop_index('idx_vendor_activity_feed_entity', table_name='vendor_activity_feed')
    op.drop_index('idx_vendor_activity_feed_unread', table_name='vendor_activity_feed')
    op.drop_index('idx_vendor_activity_feed_important', table_name='vendor_activity_feed')
    op.drop_index('idx_vendor_activity_feed_vendor_type', table_name='vendor_activity_feed')

    op.drop_index('idx_vendor_dashboard_metrics_created', table_name='vendor_dashboard_metrics')
    op.drop_index('idx_vendor_dashboard_metrics_trending', table_name='vendor_dashboard_metrics')
    op.drop_index('idx_vendor_dashboard_metrics_period', table_name='vendor_dashboard_metrics')
    op.drop_index('idx_vendor_dashboard_metrics_vendor_type', table_name='vendor_dashboard_metrics')

    # Drop tables
    op.drop_table('vendor_analytics')
    op.drop_table('vendor_quick_actions')
    op.drop_table('vendor_notifications')
    op.drop_table('vendor_activity_feed')
    op.drop_table('vendor_dashboard_metrics')

    # Drop enum types
    op.execute('DROP TYPE actiontype')
    op.execute('DROP TYPE notificationpriority')
    op.execute('DROP TYPE notificationtype')
    op.execute('DROP TYPE activitytype')
    op.execute('DROP TYPE metrictype')
