# Culture Connect Backend - Alertmanager Deployment
# Alertmanager deployment for production monitoring and alerting

apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
    app.kubernetes.io/part-of: culture-connect-monitoring
    app.kubernetes.io/version: "0.26.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: alertmanager
      app.kubernetes.io/component: alerting
  template:
    metadata:
      labels:
        app.kubernetes.io/name: alertmanager
        app.kubernetes.io/component: alerting
        app.kubernetes.io/part-of: culture-connect-monitoring
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9093"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: alertmanager
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.26.0
        imagePullPolicy: IfNotPresent
        args:
        - --config.file=/etc/alertmanager/alertmanager.yml
        - --storage.path=/alertmanager
        - --data.retention=120h
        - --web.listen-address=0.0.0.0:9093
        - --web.external-url=https://alertmanager.cultureconnect.ng
        - --web.route-prefix=/
        - --cluster.listen-address=0.0.0.0:9094
        - --cluster.peer=alertmanager-0.alertmanager.monitoring.svc.cluster.local:9094
        - --cluster.peer=alertmanager-1.alertmanager.monitoring.svc.cluster.local:9094
        - --log.level=info
        - --log.format=json
        ports:
        - name: web
          containerPort: 9093
          protocol: TCP
        - name: cluster
          containerPort: 9094
          protocol: TCP
        env:
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
            ephemeral-storage: "1Gi"
          limits:
            memory: "256Mi"
            cpu: "200m"
            ephemeral-storage: "2Gi"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: web
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /-/ready
            port: web
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          runAsGroup: 65534
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
          readOnly: true
        - name: templates
          mountPath: /etc/alertmanager/templates
          readOnly: true
        - name: storage
          mountPath: /alertmanager
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: alertmanager-config
          defaultMode: 0644
      - name: templates
        configMap:
          name: alertmanager-templates
          defaultMode: 0644
      - name: storage
        persistentVolumeClaim:
          claimName: alertmanager-storage
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - alertmanager
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      terminationGracePeriodSeconds: 60

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
spec:
  type: ClusterIP
  ports:
  - name: web
    port: 9093
    targetPort: web
    protocol: TCP
  - name: cluster
    port: 9094
    targetPort: cluster
    protocol: TCP
  selector:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: alertmanager
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: alertmanager
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: alertmanager
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: alertmanager
subjects:
- kind: ServiceAccount
  name: alertmanager
  namespace: monitoring

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alertmanager-storage
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: gp3

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alertmanager-ingress
  namespace: monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: alertmanager-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Alertmanager Authentication Required"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - alertmanager.cultureconnect.ng
    secretName: alertmanager-tls
  rules:
  - host: alertmanager.cultureconnect.ng
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alertmanager
            port:
              number: 9093
