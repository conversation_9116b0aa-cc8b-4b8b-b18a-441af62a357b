"""
Unit tests for Payment Services.

Tests for PaymentService and PaymentMethodService covering:
- Payment creation and status management
- Payment method management and security
- Business logic validation and error handling
- Performance and integration testing
- Multi-provider support and geo-location routing
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, timezone

from app.services.payment_service import PaymentService, PaymentMethodService
from app.services.base import ValidationError, NotFoundError, ServiceError
from app.models.payment import Payment, PaymentMethod, PaymentStatus, PaymentMethodType
from app.core.payment.config import PaymentProviderType
from app.repositories.base import QueryResult
from app.services.geolocation_service import GeolocationResult


class TestPaymentService:
    """Test cases for PaymentService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def payment_service(self, mock_db_session):
        """Create PaymentService instance with mocked dependencies."""
        return PaymentService(mock_db_session)

    @pytest.fixture
    def sample_payment_data(self):
        """Sample payment data for testing."""
        return {
            "booking_id": 123,
            "user_id": 456,
            "vendor_id": 789,
            "amount": Decimal("100.00"),
            "currency": "NGN",
            "provider": PaymentProviderType.PAYSTACK
        }

    @pytest.fixture
    def mock_payment(self):
        """Mock payment instance."""
        return Payment(
            id=1,
            booking_id=123,
            user_id=456,
            vendor_id=789,
            amount=Decimal("100.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            status=PaymentStatus.PENDING,
            transaction_reference="PAY_123456789",
            created_at=datetime.now(timezone.utc)
        )

    @pytest.mark.asyncio
    async def test_create_payment_success(self, payment_service, sample_payment_data, mock_payment):
        """Test successful payment creation."""
        # Mock repository method
        with patch.object(payment_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_payment.return_value = mock_payment

                # Mock provider selection
                with patch.object(payment_service, '_select_optimal_provider') as mock_select:
                    mock_select.return_value = PaymentProviderType.PAYSTACK

                    # Execute
                    result = await payment_service.create_payment(**sample_payment_data)

                    # Verify
                    assert result == mock_payment
                    mock_repo.create_payment.assert_called_once()
                    mock_select.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_payment_validation_error(self, payment_service):
        """Test payment creation with validation error."""
        # Test with invalid amount
        with pytest.raises(ValidationError) as exc_info:
            await payment_service.create_payment(
                booking_id=123,
                user_id=456,
                vendor_id=789,
                amount=Decimal("-100.00"),  # Invalid negative amount
                currency="NGN"
            )

        assert "Amount must be positive" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_payment_status_success(self, payment_service, mock_payment):
        """Test successful payment status update."""
        # Mock repository method
        with patch.object(payment_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.update_payment_status.return_value = mock_payment

                # Mock status transition validation
                with patch.object(payment_service, '_validate_status_transition') as mock_validate:
                    mock_validate.return_value = None

                    # Execute
                    result = await payment_service.update_payment_status(
                        payment_id=1,
                        status=PaymentStatus.COMPLETED,
                        provider_reference="stripe_pi_123"
                    )

                    # Verify
                    assert result == mock_payment
                    mock_repo.update_payment_status.assert_called_once()
                    mock_validate.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_payment_status_not_found(self, payment_service):
        """Test payment status update with payment not found."""
        with patch.object(payment_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.update_payment_status.return_value = None

                with patch.object(payment_service, '_validate_status_transition'):
                    # Execute and verify
                    with pytest.raises(NotFoundError):
                        await payment_service.update_payment_status(
                            payment_id=999,
                            status=PaymentStatus.COMPLETED
                        )

    @pytest.mark.asyncio
    async def test_get_user_payments_success(self, payment_service, mock_payment):
        """Test successful user payments retrieval."""
        # Mock query result
        mock_query_result = QueryResult(
            items=[mock_payment],
            total=1,
            page=1,
            size=20,
            has_next=False,
            has_previous=False
        )

        with patch.object(payment_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get_payments_by_user.return_value = mock_query_result

                # Execute
                result = await payment_service.get_user_payments(
                    user_id=456,
                    status=PaymentStatus.COMPLETED,
                    page=1,
                    size=20
                )

                # Verify
                assert result["items"] == [mock_payment]
                assert result["total"] == 1
                assert result["page"] == 1
                assert result["size"] == 20
                mock_repo.get_payments_by_user.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_payment_by_reference_success(self, payment_service, mock_payment):
        """Test successful payment retrieval by reference."""
        with patch.object(payment_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get_payment_by_reference.return_value = mock_payment

                # Execute
                result = await payment_service.get_payment_by_reference("PAY_123456789")

                # Verify
                assert result == mock_payment
                mock_repo.get_payment_by_reference.assert_called_once_with("PAY_123456789")

    @pytest.mark.asyncio
    async def test_select_optimal_provider_ngn(self, payment_service):
        """Test optimal provider selection for NGN currency."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",
            amount=Decimal("100.00")
        )

        assert result == PaymentProviderType.PAYSTACK

    @pytest.mark.asyncio
    async def test_select_optimal_provider_usd(self, payment_service):
        """Test optimal provider selection for USD currency."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",
            amount=Decimal("100.00")
        )

        assert result == PaymentProviderType.STRIPE

    @pytest.mark.asyncio
    async def test_validate_status_transition_valid(self, payment_service, mock_payment):
        """Test valid payment status transition."""
        with patch.object(payment_service, 'get_by_id') as mock_get:
            mock_get.return_value = mock_payment

            # Should not raise exception for valid transition
            await payment_service._validate_status_transition(1, PaymentStatus.PROCESSING)

    @pytest.mark.asyncio
    async def test_validate_status_transition_invalid(self, payment_service, mock_payment):
        """Test invalid payment status transition."""
        # Set payment to completed status
        mock_payment.status = PaymentStatus.COMPLETED

        with patch.object(payment_service, 'get_by_id') as mock_get:
            mock_get.return_value = mock_payment

            # Should raise ValidationError for invalid transition
            with pytest.raises(ValidationError) as exc_info:
                await payment_service._validate_status_transition(1, PaymentStatus.PENDING)

            assert "Invalid status transition" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, payment_service, sample_payment_data):
        """Test successful payment creation data validation."""
        result = await payment_service.validate_create_data(sample_payment_data)
        assert result == sample_payment_data

    @pytest.mark.asyncio
    async def test_validate_create_data_missing_field(self, payment_service):
        """Test payment creation data validation with missing field."""
        invalid_data = {
            "user_id": 456,
            "vendor_id": 789,
            "amount": Decimal("100.00")
            # Missing booking_id
        }

        with pytest.raises(ValidationError) as exc_info:
            await payment_service.validate_create_data(invalid_data)

        assert "booking_id is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_protected_field(self, payment_service):
        """Test payment update data validation with protected field."""
        update_data = {
            "amount": Decimal("200.00")  # Protected field
        }

        with pytest.raises(ValidationError) as exc_info:
            await payment_service.validate_update_data(update_data, 1)

        assert "Cannot modify amount after creation" in str(exc_info.value)

    # Geolocation-Enhanced Payment Provider Selection Tests

    @pytest.fixture
    def mock_geolocation_result_nigeria(self):
        """Mock geolocation result for Nigeria."""
        return GeolocationResult(
            country_code="NG",
            country_name="Nigeria",
            continent_code="AF",
            ip_address="***********",
            detection_method="maxmind_geoip",
            confidence_score=0.95
        )

    @pytest.fixture
    def mock_geolocation_result_usa(self):
        """Mock geolocation result for USA."""
        return GeolocationResult(
            country_code="US",
            country_name="United States",
            continent_code="NA",
            ip_address="************",
            detection_method="maxmind_geoip",
            confidence_score=0.95
        )

    @pytest.fixture
    def mock_geolocation_result_unknown(self):
        """Mock geolocation result for unknown country."""
        return GeolocationResult(
            country_code=None,
            country_name="Unknown",
            continent_code=None,
            ip_address="***********",
            detection_method="fallback_failed",
            confidence_score=0.0
        )

    @pytest.mark.asyncio
    async def test_select_optimal_provider_geolocation_african_country(self, payment_service, mock_geolocation_result_nigeria):
        """Test optimal provider selection for African country via geolocation."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",  # Non-NGN currency
            amount=Decimal("100.00"),
            geolocation_result=mock_geolocation_result_nigeria
        )

        # Should select Paystack for African country despite USD currency
        assert result == PaymentProviderType.PAYSTACK

    @pytest.mark.asyncio
    async def test_select_optimal_provider_geolocation_diaspora_country(self, payment_service, mock_geolocation_result_usa):
        """Test optimal provider selection for diaspora country via geolocation."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",  # NGN currency
            amount=Decimal("100.00"),
            geolocation_result=mock_geolocation_result_usa
        )

        # Should select Stripe for diaspora country despite NGN currency
        assert result == PaymentProviderType.STRIPE

    @pytest.mark.asyncio
    async def test_select_optimal_provider_cryptocurrency_priority(self, payment_service, mock_geolocation_result_usa):
        """Test cryptocurrency payment takes priority over geolocation."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",
            amount=Decimal("100.00"),
            crypto_currency="BTC",
            geolocation_result=mock_geolocation_result_usa
        )

        # Should select Busha for cryptocurrency despite USA geolocation
        assert result == PaymentProviderType.BUSHA

    @pytest.mark.asyncio
    async def test_select_optimal_provider_user_preference_priority(self, payment_service, mock_geolocation_result_nigeria):
        """Test user preference takes priority over geolocation when valid."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",
            amount=Decimal("100.00"),
            preferred_provider="stripe",
            geolocation_result=mock_geolocation_result_nigeria
        )

        # Should select Stripe based on user preference despite Nigerian geolocation
        assert result == PaymentProviderType.STRIPE

    @pytest.mark.asyncio
    async def test_select_optimal_provider_geolocation_fallback_to_currency(self, payment_service, mock_geolocation_result_unknown):
        """Test fallback to currency-based selection when geolocation fails."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",
            amount=Decimal("100.00"),
            geolocation_result=mock_geolocation_result_unknown
        )

        # Should fallback to currency-based selection (NGN -> Paystack)
        assert result == PaymentProviderType.PAYSTACK

    @pytest.mark.asyncio
    async def test_select_optimal_provider_no_geolocation_data(self, payment_service):
        """Test provider selection without geolocation data."""
        result = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",
            amount=Decimal("100.00"),
            geolocation_result=None
        )

        # Should fallback to currency-based selection (USD -> Stripe)
        assert result == PaymentProviderType.STRIPE

    @pytest.mark.asyncio
    async def test_get_geolocation_based_provider_african_countries(self, payment_service):
        """Test geolocation-based provider selection for various African countries."""
        african_countries = ["NG", "GH", "KE", "ZA", "EG"]

        for country_code in african_countries:
            result = payment_service._get_geolocation_based_provider(country_code, "USD")
            assert result == PaymentProviderType.PAYSTACK, f"Failed for country: {country_code}"

    @pytest.mark.asyncio
    async def test_get_geolocation_based_provider_diaspora_countries(self, payment_service):
        """Test geolocation-based provider selection for various diaspora countries."""
        diaspora_countries = ["US", "GB", "CA", "AU", "DE", "FR"]

        for country_code in diaspora_countries:
            result = payment_service._get_geolocation_based_provider(country_code, "NGN")
            assert result == PaymentProviderType.STRIPE, f"Failed for country: {country_code}"

    @pytest.mark.asyncio
    async def test_get_geolocation_based_provider_unknown_country(self, payment_service):
        """Test geolocation-based provider selection for unknown country."""
        # Mock the currency-based provider method
        with patch.object(payment_service, '_get_currency_based_provider') as mock_currency_based:
            mock_currency_based.return_value = PaymentProviderType.PAYSTACK

            result = payment_service._get_geolocation_based_provider("XX", "NGN")

            # Should fallback to currency-based selection
            assert result == PaymentProviderType.PAYSTACK
            mock_currency_based.assert_called_once_with("NGN")

    @pytest.mark.asyncio
    async def test_create_payment_with_geolocation_success(self, payment_service, sample_payment_data, mock_payment, mock_geolocation_result_nigeria):
        """Test payment creation with geolocation data."""
        # Mock repository method
        with patch.object(payment_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_payment.return_value = mock_payment

                # Mock provider selection with geolocation
                with patch.object(payment_service, '_select_optimal_provider') as mock_select:
                    mock_select.return_value = PaymentProviderType.PAYSTACK

                    # Execute with geolocation data
                    result = await payment_service.create_payment(
                        **sample_payment_data,
                        geolocation_result=mock_geolocation_result_nigeria
                    )

                    # Verify
                    assert result == mock_payment
                    mock_repo.create_payment.assert_called_once()
                    mock_select.assert_called_once()

                    # Verify geolocation data was passed to provider selection
                    call_args = mock_select.call_args[0]
                    assert len(call_args) == 6  # Including geolocation_result parameter

    @pytest.mark.asyncio
    async def test_4_priority_routing_system_complete(self, payment_service, mock_geolocation_result_usa):
        """Test complete 4-priority routing system."""

        # Priority 1: Cryptocurrency (highest priority)
        result1 = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",
            amount=Decimal("100.00"),
            crypto_currency="BTC",
            preferred_provider="stripe",
            geolocation_result=mock_geolocation_result_usa
        )
        assert result1 == PaymentProviderType.BUSHA  # Crypto overrides everything

        # Priority 2: User preference (when valid)
        result2 = await payment_service._select_optimal_provider(
            user_id=456,
            currency="USD",
            amount=Decimal("100.00"),
            preferred_provider="stripe",
            geolocation_result=mock_geolocation_result_usa
        )
        assert result2 == PaymentProviderType.STRIPE  # User preference honored

        # Priority 3: Geolocation-based
        result3 = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",
            amount=Decimal("100.00"),
            geolocation_result=mock_geolocation_result_usa
        )
        assert result3 == PaymentProviderType.STRIPE  # USA geolocation overrides NGN currency

        # Priority 4: Currency-based fallback
        result4 = await payment_service._select_optimal_provider(
            user_id=456,
            currency="NGN",
            amount=Decimal("100.00")
        )
        assert result4 == PaymentProviderType.PAYSTACK  # Currency-based fallback


class TestPaymentMethodService:
    """Test cases for PaymentMethodService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def payment_method_service(self, mock_db_session):
        """Create PaymentMethodService instance with mocked dependencies."""
        return PaymentMethodService(mock_db_session)

    @pytest.fixture
    def sample_payment_method_data(self):
        """Sample payment method data for testing."""
        return {
            "user_id": 456,
            "method_type": PaymentMethodType.CARD,
            "provider": PaymentProviderType.STRIPE,
            "display_name": "Visa ending in 1234",
            "encrypted_metadata": "encrypted_card_data"
        }

    @pytest.fixture
    def mock_payment_method(self):
        """Mock payment method instance."""
        return PaymentMethod(
            id=1,
            user_id=456,
            type=PaymentMethodType.CARD,
            provider=PaymentProviderType.STRIPE,
            display_name="Visa ending in 1234",
            encrypted_metadata="encrypted_card_data",
            is_active=True,
            is_verified=False,
            is_default=False,
            created_at=datetime.now(timezone.utc)
        )

    @pytest.mark.asyncio
    async def test_create_payment_method_success(self, payment_method_service, sample_payment_method_data, mock_payment_method):
        """Test successful payment method creation."""
        with patch.object(payment_method_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_payment_method.return_value = mock_payment_method

                # Execute
                result = await payment_method_service.create_payment_method(**sample_payment_method_data)

                # Verify
                assert result == mock_payment_method
                mock_repo.create_payment_method.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_payment_method_validation_error(self, payment_method_service):
        """Test payment method creation with validation error."""
        # Test with missing required field
        with pytest.raises(ValidationError) as exc_info:
            await payment_method_service.create_payment_method(
                user_id=456,
                method_type=PaymentMethodType.CARD,
                provider=PaymentProviderType.STRIPE
                # Missing display_name
            )

        assert "display_name is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_user_payment_methods_success(self, payment_method_service, mock_payment_method):
        """Test successful user payment methods retrieval."""
        with patch.object(payment_method_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get_user_payment_methods.return_value = [mock_payment_method]

                # Execute
                result = await payment_method_service.get_user_payment_methods(
                    user_id=456,
                    active_only=True,
                    provider=PaymentProviderType.STRIPE
                )

                # Verify
                assert result == [mock_payment_method]
                mock_repo.get_user_payment_methods.assert_called_once_with(
                    user_id=456,
                    active_only=True,
                    provider=PaymentProviderType.STRIPE
                )

    @pytest.mark.asyncio
    async def test_set_default_payment_method_success(self, payment_method_service, mock_payment_method):
        """Test successful default payment method setting."""
        with patch.object(payment_method_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.set_default_payment_method.return_value = mock_payment_method

                # Execute
                result = await payment_method_service.set_default_payment_method(
                    user_id=456,
                    payment_method_id=1
                )

                # Verify
                assert result == mock_payment_method
                mock_repo.set_default_payment_method.assert_called_once_with(
                    user_id=456,
                    payment_method_id=1
                )

    @pytest.mark.asyncio
    async def test_set_default_payment_method_not_found(self, payment_method_service):
        """Test default payment method setting with method not found."""
        with patch.object(payment_method_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.set_default_payment_method.return_value = None

                # Execute and verify
                with pytest.raises(NotFoundError):
                    await payment_method_service.set_default_payment_method(
                        user_id=456,
                        payment_method_id=999
                    )

    @pytest.mark.asyncio
    async def test_update_usage_tracking_success(self, payment_method_service, mock_payment_method):
        """Test successful usage tracking update."""
        with patch.object(payment_method_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.update_usage_tracking.return_value = mock_payment_method

                # Execute
                result = await payment_method_service.update_usage_tracking(
                    payment_method_id=1,
                    success=True
                )

                # Verify
                assert result == mock_payment_method
                mock_repo.update_usage_tracking.assert_called_once_with(
                    payment_method_id=1,
                    success=True
                )

    @pytest.mark.asyncio
    async def test_get_expired_payment_methods_success(self, payment_method_service, mock_payment_method):
        """Test successful expired payment methods retrieval."""
        with patch.object(payment_method_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch('app.services.payment_service.PaymentMethodRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get_expired_payment_methods.return_value = [mock_payment_method]

                # Execute
                result = await payment_method_service.get_expired_payment_methods()

                # Verify
                assert result == [mock_payment_method]
                mock_repo.get_expired_payment_methods.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, payment_method_service, sample_payment_method_data):
        """Test successful payment method creation data validation."""
        result = await payment_method_service.validate_create_data(sample_payment_method_data)
        assert result == sample_payment_method_data

    @pytest.mark.asyncio
    async def test_validate_create_data_invalid_type(self, payment_method_service):
        """Test payment method creation data validation with invalid type."""
        invalid_data = {
            "user_id": 456,
            "method_type": "invalid_type",  # Invalid type
            "provider": PaymentProviderType.STRIPE,
            "display_name": "Test Method"
        }

        with pytest.raises(ValidationError) as exc_info:
            await payment_method_service.validate_create_data(invalid_data)

        assert "Invalid payment method type" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_protected_field(self, payment_method_service):
        """Test payment method update data validation with protected field."""
        update_data = {
            "user_id": 789  # Protected field
        }

        with pytest.raises(ValidationError) as exc_info:
            await payment_method_service.validate_update_data(update_data, 1)

        assert "Cannot modify user_id after creation" in str(exc_info.value)
