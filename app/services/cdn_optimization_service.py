"""
CDN Optimization Service for Culture Connect Backend API.

This module provides comprehensive CDN optimization and static asset management including:
- CDNOptimizationService: Static asset optimization and delivery infrastructure
- Asset bundling and compression strategies for improved performance
- Cache headers and content delivery optimization
- Integration with Phase 7.3.2 caching infrastructure for asset management

Implements Phase 7.3.2 requirements for CDN integration preparation with:
- Static asset optimization and delivery infrastructure
- Cache headers and content delivery optimization
- Asset bundling and compression strategies
- Performance optimization targeting <100ms asset delivery

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
import asyncio
import hashlib
import gzip
import mimetypes
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from dataclasses import dataclass
from pathlib import Path
import os

from app.services.base import BaseService, ServiceError, ValidationError
from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.models.analytics_models import PerformanceMetricType, AnalyticsTimeframe
from app.core.circuit_breaker import get_circuit_breaker, CircuitBreakerConfig
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

try:
    import sentry_sdk
    from sentry_sdk import capture_exception, capture_message, set_tag, set_context
    SENTRY_AVAILABLE = True
except ImportError:
    SENTRY_AVAILABLE = False


@dataclass
class AssetOptimizationResult:
    """Result of asset optimization process."""
    original_path: str
    optimized_path: str
    original_size_bytes: int
    optimized_size_bytes: int
    compression_ratio: float
    optimization_type: str
    cache_headers: Dict[str, str]
    cdn_url: Optional[str] = None


@dataclass
class CDNConfiguration:
    """CDN configuration settings."""
    base_url: str
    cache_ttl_seconds: int
    compression_enabled: bool
    supported_formats: List[str]
    max_file_size_mb: float
    optimization_quality: int = 85


class CDNOptimizationService:
    """
    CDN optimization service for static asset management and delivery.

    Provides comprehensive CDN optimization capabilities including:
    - Static asset optimization and compression
    - Cache headers and content delivery optimization
    - Asset bundling and compression strategies
    - Performance optimization targeting <100ms asset delivery
    """

    def __init__(self, performance_service: Optional[PerformanceMonitoringService] = None):
        """Initialize CDN optimization service."""
        self.logger = logging.getLogger(f"{__name__}.CDNOptimizationService")
        self.performance_service = performance_service or PerformanceMonitoringService()
        
        # CDN configuration
        self.cdn_config = CDNConfiguration(
            base_url="https://cdn.cultureconnect.com",  # Example CDN URL
            cache_ttl_seconds=86400,  # 24 hours
            compression_enabled=True,
            supported_formats=["jpg", "jpeg", "png", "gif", "webp", "css", "js", "html", "json"],
            max_file_size_mb=10.0,
            optimization_quality=85
        )
        
        # Asset optimization cache
        self.optimization_cache: Dict[str, AssetOptimizationResult] = {}
        
        # Circuit breaker for CDN operations
        self._circuit_breaker = get_circuit_breaker(
            "cdn_optimization_service",
            CircuitBreakerConfig(failure_threshold=3, timeout=30)
        )

    async def optimize_static_asset(
        self,
        asset_path: str,
        optimization_type: str = "auto"
    ) -> AssetOptimizationResult:
        """
        Optimize static asset for CDN delivery.

        Performance Metrics:
        - Target optimization time: <2000ms for asset processing
        - Compression ratio target: >30% size reduction for compressible assets
        - Asset delivery target: <100ms for optimized assets

        Args:
            asset_path: Path to the asset file
            optimization_type: Type of optimization (auto, compress, resize, etc.)

        Returns:
            AssetOptimizationResult: Optimization result with metrics

        Raises:
            ServiceError: If asset optimization fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            # Check if already optimized
            if asset_path in self.optimization_cache:
                cached_result = self.optimization_cache[asset_path]
                self.logger.debug(
                    f"Using cached optimization result for asset: {asset_path}",
                    extra={
                        "correlation_id": correlation_id_val,
                        "asset_path": asset_path,
                        "cached_compression_ratio": cached_result.compression_ratio
                    }
                )
                return cached_result

            # Validate asset file
            if not os.path.exists(asset_path):
                raise ValidationError(f"Asset file not found: {asset_path}")

            # Get file info
            file_stats = os.stat(asset_path)
            original_size = file_stats.st_size
            file_extension = Path(asset_path).suffix.lower().lstrip('.')

            # Check file size limits
            if original_size > self.cdn_config.max_file_size_mb * 1024 * 1024:
                raise ValidationError(f"Asset file too large: {original_size} bytes")

            # Check supported formats
            if file_extension not in self.cdn_config.supported_formats:
                raise ValidationError(f"Unsupported asset format: {file_extension}")

            # Perform optimization based on file type
            optimized_path, optimized_size = await self._optimize_asset_by_type(
                asset_path, file_extension, optimization_type
            )

            # Calculate compression ratio
            compression_ratio = (original_size - optimized_size) / original_size * 100 if original_size > 0 else 0

            # Generate cache headers
            cache_headers = self._generate_cache_headers(file_extension)

            # Generate CDN URL
            cdn_url = self._generate_cdn_url(optimized_path)

            # Create optimization result
            result = AssetOptimizationResult(
                original_path=asset_path,
                optimized_path=optimized_path,
                original_size_bytes=original_size,
                optimized_size_bytes=optimized_size,
                compression_ratio=compression_ratio,
                optimization_type=optimization_type,
                cache_headers=cache_headers,
                cdn_url=cdn_url
            )

            # Cache the result
            self.optimization_cache[asset_path] = result

            optimization_time = time.time() - start_time
            self.logger.info(
                f"Asset optimization completed",
                extra={
                    "correlation_id": correlation_id_val,
                    "asset_path": asset_path,
                    "optimization_time": optimization_time,
                    "original_size": original_size,
                    "optimized_size": optimized_size,
                    "compression_ratio": compression_ratio
                }
            )

            # Record performance metric
            await self.performance_service.record_performance_metric(
                metric_type=PerformanceMetricType.API_RESPONSE_TIME,
                metric_name="asset_optimization",
                component="cdn_optimization",
                value=Decimal(str(optimization_time * 1000)),
                timeframe=AnalyticsTimeframe.REAL_TIME,
                tags={
                    "file_extension": file_extension,
                    "optimization_type": optimization_type,
                    "compression_ratio": round(compression_ratio, 2)
                }
            )

            return result

        except Exception as e:
            optimization_time = time.time() - start_time
            self.logger.error(
                f"Failed to optimize static asset: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "asset_path": asset_path,
                    "error": str(e),
                    "optimization_time": optimization_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            raise ServiceError(f"Failed to optimize static asset: {str(e)}")

    async def generate_asset_bundle(
        self,
        asset_paths: List[str],
        bundle_type: str = "js"
    ) -> AssetOptimizationResult:
        """
        Generate optimized asset bundle from multiple files.

        Args:
            asset_paths: List of asset file paths to bundle
            bundle_type: Type of bundle (js, css, etc.)

        Returns:
            AssetOptimizationResult: Bundle optimization result
        """
        start_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            # Generate bundle filename
            bundle_hash = hashlib.md5(''.join(asset_paths).encode()).hexdigest()[:8]
            bundle_filename = f"bundle_{bundle_hash}.{bundle_type}"
            bundle_path = f"/tmp/{bundle_filename}"

            # Combine files
            combined_content = ""
            total_original_size = 0

            for asset_path in asset_paths:
                if os.path.exists(asset_path):
                    with open(asset_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        combined_content += content + "\n"
                        total_original_size += len(content.encode('utf-8'))

            # Write bundle file
            with open(bundle_path, 'w', encoding='utf-8') as f:
                f.write(combined_content)

            # Compress bundle if enabled
            if self.cdn_config.compression_enabled:
                compressed_path = f"{bundle_path}.gz"
                with open(bundle_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        f_out.write(f_in.read())
                
                optimized_size = os.path.getsize(compressed_path)
                final_path = compressed_path
            else:
                optimized_size = os.path.getsize(bundle_path)
                final_path = bundle_path

            # Calculate compression ratio
            compression_ratio = (total_original_size - optimized_size) / total_original_size * 100 if total_original_size > 0 else 0

            # Generate cache headers
            cache_headers = self._generate_cache_headers(bundle_type)

            # Generate CDN URL
            cdn_url = self._generate_cdn_url(final_path)

            result = AssetOptimizationResult(
                original_path=f"bundle:{','.join(asset_paths)}",
                optimized_path=final_path,
                original_size_bytes=total_original_size,
                optimized_size_bytes=optimized_size,
                compression_ratio=compression_ratio,
                optimization_type="bundle",
                cache_headers=cache_headers,
                cdn_url=cdn_url
            )

            bundling_time = time.time() - start_time
            self.logger.info(
                f"Asset bundling completed",
                extra={
                    "correlation_id": correlation_id_val,
                    "bundle_type": bundle_type,
                    "asset_count": len(asset_paths),
                    "bundling_time": bundling_time,
                    "compression_ratio": compression_ratio
                }
            )

            return result

        except Exception as e:
            bundling_time = time.time() - start_time
            self.logger.error(
                f"Failed to generate asset bundle: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "bundle_type": bundle_type,
                    "error": str(e),
                    "bundling_time": bundling_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            raise ServiceError(f"Failed to generate asset bundle: {str(e)}")

    async def get_optimization_metrics(self) -> Dict[str, Any]:
        """Get CDN optimization performance metrics."""
        try:
            total_assets = len(self.optimization_cache)
            total_original_size = sum(result.original_size_bytes for result in self.optimization_cache.values())
            total_optimized_size = sum(result.optimized_size_bytes for result in self.optimization_cache.values())
            
            overall_compression_ratio = (
                (total_original_size - total_optimized_size) / total_original_size * 100
                if total_original_size > 0 else 0
            )

            # Group by optimization type
            optimization_types = {}
            for result in self.optimization_cache.values():
                opt_type = result.optimization_type
                if opt_type not in optimization_types:
                    optimization_types[opt_type] = {
                        "count": 0,
                        "total_original_size": 0,
                        "total_optimized_size": 0
                    }
                
                optimization_types[opt_type]["count"] += 1
                optimization_types[opt_type]["total_original_size"] += result.original_size_bytes
                optimization_types[opt_type]["total_optimized_size"] += result.optimized_size_bytes

            # Calculate compression ratios by type
            for opt_type, data in optimization_types.items():
                if data["total_original_size"] > 0:
                    data["compression_ratio"] = (
                        (data["total_original_size"] - data["total_optimized_size"]) /
                        data["total_original_size"] * 100
                    )
                else:
                    data["compression_ratio"] = 0.0

            return {
                "overall_metrics": {
                    "total_assets_optimized": total_assets,
                    "total_original_size_mb": round(total_original_size / (1024 * 1024), 2),
                    "total_optimized_size_mb": round(total_optimized_size / (1024 * 1024), 2),
                    "overall_compression_ratio": round(overall_compression_ratio, 2),
                    "space_saved_mb": round((total_original_size - total_optimized_size) / (1024 * 1024), 2)
                },
                "optimization_types": {
                    opt_type: {
                        "count": data["count"],
                        "compression_ratio": round(data["compression_ratio"], 2),
                        "original_size_mb": round(data["total_original_size"] / (1024 * 1024), 2),
                        "optimized_size_mb": round(data["total_optimized_size"] / (1024 * 1024), 2)
                    }
                    for opt_type, data in optimization_types.items()
                },
                "cdn_configuration": {
                    "base_url": self.cdn_config.base_url,
                    "cache_ttl_hours": self.cdn_config.cache_ttl_seconds / 3600,
                    "compression_enabled": self.cdn_config.compression_enabled,
                    "max_file_size_mb": self.cdn_config.max_file_size_mb,
                    "supported_formats": self.cdn_config.supported_formats
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to get optimization metrics: {str(e)}")
            return {}

    async def _optimize_asset_by_type(
        self,
        asset_path: str,
        file_extension: str,
        optimization_type: str
    ) -> Tuple[str, int]:
        """Optimize asset based on file type."""
        try:
            if file_extension in ["js", "css", "html", "json"]:
                # Text-based files - apply compression
                return await self._compress_text_asset(asset_path)
            elif file_extension in ["jpg", "jpeg", "png", "gif", "webp"]:
                # Image files - apply image optimization (simplified)
                return await self._optimize_image_asset(asset_path)
            else:
                # Default - just copy the file
                optimized_path = f"{asset_path}.optimized"
                with open(asset_path, 'rb') as src, open(optimized_path, 'wb') as dst:
                    dst.write(src.read())
                return optimized_path, os.path.getsize(optimized_path)

        except Exception as e:
            self.logger.warning(f"Failed to optimize asset {asset_path}: {str(e)}")
            # Fallback - return original file
            return asset_path, os.path.getsize(asset_path)

    async def _compress_text_asset(self, asset_path: str) -> Tuple[str, int]:
        """Compress text-based assets."""
        compressed_path = f"{asset_path}.gz"
        
        with open(asset_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                f_out.write(f_in.read())
        
        return compressed_path, os.path.getsize(compressed_path)

    async def _optimize_image_asset(self, asset_path: str) -> Tuple[str, int]:
        """Optimize image assets (simplified implementation)."""
        # In a real implementation, you would use image optimization libraries
        # like Pillow, ImageIO, or external services
        
        # For now, just apply gzip compression as a placeholder
        return await self._compress_text_asset(asset_path)

    def _generate_cache_headers(self, file_extension: str) -> Dict[str, str]:
        """Generate appropriate cache headers for asset type."""
        # Different cache strategies for different asset types
        if file_extension in ["jpg", "jpeg", "png", "gif", "webp"]:
            # Images - long cache
            max_age = 2592000  # 30 days
        elif file_extension in ["css", "js"]:
            # Stylesheets and scripts - medium cache
            max_age = 604800   # 7 days
        else:
            # Default - short cache
            max_age = 86400    # 1 day

        return {
            "Cache-Control": f"public, max-age={max_age}",
            "Expires": (datetime.utcnow() + timedelta(seconds=max_age)).strftime("%a, %d %b %Y %H:%M:%S GMT"),
            "ETag": f'"{hashlib.md5(str(time.time()).encode()).hexdigest()}"',
            "Content-Encoding": "gzip" if self.cdn_config.compression_enabled else "identity"
        }

    def _generate_cdn_url(self, asset_path: str) -> str:
        """Generate CDN URL for optimized asset."""
        filename = os.path.basename(asset_path)
        return f"{self.cdn_config.base_url}/assets/{filename}"
