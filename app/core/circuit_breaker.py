"""
Circuit breaker pattern implementation for Culture Connect Backend API.

This module provides comprehensive circuit breaker patterns for resilient
geolocation and payment provider services including:
- Circuit breaker state management (CLOSED, OPEN, HALF_OPEN)
- Configurable failure thresholds and recovery timeouts
- Metrics collection and monitoring integration
- Geolocation-specific circuit breaker implementations

Implements production-grade resilience patterns following the NewEnhancement.md
specifications for MaxMind database protection and service reliability.
"""

import asyncio
import time
import logging
from enum import Enum
from typing import Callable, Any, Optional, Dict, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import threading

from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"        # Normal operation
    OPEN = "open"            # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    success_threshold: int = 3  # For half-open state
    timeout: float = 30.0
    expected_exception: type = Exception
    name: str = "default"


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    state_changes: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    current_consecutive_failures: int = 0
    current_consecutive_successes: int = 0


class CircuitBreakerError(Exception):
    """Circuit breaker specific exception."""
    
    def __init__(self, message: str, circuit_name: str, state: CircuitState):
        super().__init__(message)
        self.circuit_name = circuit_name
        self.state = state


class BaseCircuitBreaker:
    """
    Base circuit breaker implementation.
    
    Provides core circuit breaker functionality with state management,
    failure tracking, and recovery mechanisms.
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        """Initialize circuit breaker."""
        self.config = config
        self.state = CircuitState.CLOSED
        self.stats = CircuitBreakerStats()
        self.next_attempt_time = None
        self._lock = threading.RLock()
        
        logger.info(f"Initialized circuit breaker: {config.name}")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        correlation = correlation_id.get()
        
        with self._lock:
            # Check if circuit is open
            if self.state == CircuitState.OPEN:
                if time.time() < self.next_attempt_time:
                    self._record_rejected_request()
                    raise CircuitBreakerError(
                        f"Circuit breaker {self.config.name} is OPEN",
                        self.config.name,
                        self.state
                    )
                else:
                    # Transition to half-open
                    self._transition_to_half_open()
        
        # Execute the function
        start_time = time.perf_counter()
        
        try:
            # Set timeout for the operation
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            execution_time = time.perf_counter() - start_time
            self._on_success(execution_time)
            
            logger.debug(
                f"Circuit breaker {self.config.name} call succeeded in {execution_time:.3f}s",
                extra={"correlation_id": correlation}
            )
            
            return result
        
        except asyncio.TimeoutError:
            execution_time = time.perf_counter() - start_time
            self._on_failure(execution_time, "timeout")
            
            logger.warning(
                f"Circuit breaker {self.config.name} call timed out after {execution_time:.3f}s",
                extra={"correlation_id": correlation}
            )
            
            raise CircuitBreakerError(
                f"Operation timed out after {self.config.timeout}s",
                self.config.name,
                self.state
            )
        
        except self.config.expected_exception as e:
            execution_time = time.perf_counter() - start_time
            self._on_failure(execution_time, type(e).__name__)
            
            logger.warning(
                f"Circuit breaker {self.config.name} call failed: {str(e)}",
                extra={"correlation_id": correlation}
            )
            
            raise e
        
        except Exception as e:
            execution_time = time.perf_counter() - start_time
            self._on_failure(execution_time, type(e).__name__)
            
            logger.error(
                f"Circuit breaker {self.config.name} unexpected error: {str(e)}",
                extra={"correlation_id": correlation}
            )
            
            raise e
    
    def _on_success(self, execution_time: float):
        """Handle successful operation."""
        with self._lock:
            self.stats.total_requests += 1
            self.stats.successful_requests += 1
            self.stats.current_consecutive_failures = 0
            self.stats.current_consecutive_successes += 1
            self.stats.last_success_time = datetime.utcnow()
            
            # Record metrics
            metrics_collector.record_histogram(
                f"circuit_breaker_{self.config.name}_execution_time",
                execution_time * 1000  # Convert to milliseconds
            )
            metrics_collector.increment_counter(
                f"circuit_breaker_{self.config.name}_requests",
                tags={"result": "success", "state": self.state.value}
            )
            
            # Transition from half-open to closed if enough successes
            if (self.state == CircuitState.HALF_OPEN and 
                self.stats.current_consecutive_successes >= self.config.success_threshold):
                self._transition_to_closed()
    
    def _on_failure(self, execution_time: float, error_type: str):
        """Handle failed operation."""
        with self._lock:
            self.stats.total_requests += 1
            self.stats.failed_requests += 1
            self.stats.current_consecutive_successes = 0
            self.stats.current_consecutive_failures += 1
            self.stats.last_failure_time = datetime.utcnow()
            
            # Record metrics
            metrics_collector.record_histogram(
                f"circuit_breaker_{self.config.name}_execution_time",
                execution_time * 1000  # Convert to milliseconds
            )
            metrics_collector.increment_counter(
                f"circuit_breaker_{self.config.name}_requests",
                tags={"result": "failure", "state": self.state.value, "error_type": error_type}
            )
            
            # Check if we should open the circuit
            if (self.state == CircuitState.CLOSED and 
                self.stats.current_consecutive_failures >= self.config.failure_threshold):
                self._transition_to_open()
            elif (self.state == CircuitState.HALF_OPEN and 
                  self.stats.current_consecutive_failures >= 1):
                # Any failure in half-open state opens the circuit
                self._transition_to_open()
    
    def _record_rejected_request(self):
        """Record rejected request metrics."""
        metrics_collector.increment_counter(
            f"circuit_breaker_{self.config.name}_requests",
            tags={"result": "rejected", "state": self.state.value}
        )
    
    def _transition_to_open(self):
        """Transition circuit breaker to OPEN state."""
        self.state = CircuitState.OPEN
        self.next_attempt_time = time.time() + self.config.recovery_timeout
        self.stats.state_changes += 1
        
        metrics_collector.increment_counter(
            f"circuit_breaker_{self.config.name}_state_changes",
            tags={"from_state": "closed", "to_state": "open"}
        )
        
        logger.warning(f"Circuit breaker {self.config.name} opened due to failures")
    
    def _transition_to_half_open(self):
        """Transition circuit breaker to HALF_OPEN state."""
        self.state = CircuitState.HALF_OPEN
        self.stats.current_consecutive_successes = 0
        self.stats.current_consecutive_failures = 0
        self.stats.state_changes += 1
        
        metrics_collector.increment_counter(
            f"circuit_breaker_{self.config.name}_state_changes",
            tags={"from_state": "open", "to_state": "half_open"}
        )
        
        logger.info(f"Circuit breaker {self.config.name} transitioned to half-open")
    
    def _transition_to_closed(self):
        """Transition circuit breaker to CLOSED state."""
        self.state = CircuitState.CLOSED
        self.stats.current_consecutive_failures = 0
        self.stats.state_changes += 1
        
        metrics_collector.increment_counter(
            f"circuit_breaker_{self.config.name}_state_changes",
            tags={"from_state": "half_open", "to_state": "closed"}
        )
        
        logger.info(f"Circuit breaker {self.config.name} closed - service recovered")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        with self._lock:
            success_rate = (
                self.stats.successful_requests / self.stats.total_requests
                if self.stats.total_requests > 0 else 0
            )
            
            return {
                "name": self.config.name,
                "state": self.state.value,
                "total_requests": self.stats.total_requests,
                "successful_requests": self.stats.successful_requests,
                "failed_requests": self.stats.failed_requests,
                "success_rate": success_rate,
                "state_changes": self.stats.state_changes,
                "current_consecutive_failures": self.stats.current_consecutive_failures,
                "current_consecutive_successes": self.stats.current_consecutive_successes,
                "last_failure_time": self.stats.last_failure_time.isoformat() if self.stats.last_failure_time else None,
                "last_success_time": self.stats.last_success_time.isoformat() if self.stats.last_success_time else None,
                "next_attempt_time": datetime.fromtimestamp(self.next_attempt_time).isoformat() if self.next_attempt_time else None
            }
    
    def reset(self):
        """Reset circuit breaker to initial state."""
        with self._lock:
            self.state = CircuitState.CLOSED
            self.stats = CircuitBreakerStats()
            self.next_attempt_time = None
            
            logger.info(f"Circuit breaker {self.config.name} reset")


class GeolocationCircuitBreaker(BaseCircuitBreaker):
    """
    Specialized circuit breaker for geolocation services.
    
    Provides MaxMind database-specific error handling and
    geolocation service resilience patterns.
    """
    
    def __init__(self):
        """Initialize geolocation circuit breaker."""
        config = CircuitBreakerConfig(
            failure_threshold=10,
            recovery_timeout=60,
            success_threshold=3,
            timeout=5.0,
            expected_exception=Exception,
            name="geolocation"
        )
        super().__init__(config)
    
    async def detect_country_with_protection(self, geolocation_service, ip_address: str):
        """Execute geolocation detection with circuit breaker protection."""
        return await self.call(
            geolocation_service.detect_country_from_ip,
            ip_address
        )


# Global circuit breaker instances
_circuit_breakers: Dict[str, BaseCircuitBreaker] = {}
_circuit_breaker_lock = threading.RLock()


def get_circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None) -> BaseCircuitBreaker:
    """Get or create a circuit breaker instance."""
    with _circuit_breaker_lock:
        if name not in _circuit_breakers:
            if config is None:
                config = CircuitBreakerConfig(name=name)
            _circuit_breakers[name] = BaseCircuitBreaker(config)
        
        return _circuit_breakers[name]


def get_geolocation_circuit_breaker() -> GeolocationCircuitBreaker:
    """Get the geolocation circuit breaker instance."""
    with _circuit_breaker_lock:
        if "geolocation" not in _circuit_breakers:
            _circuit_breakers["geolocation"] = GeolocationCircuitBreaker()
        
        return _circuit_breakers["geolocation"]


def get_all_circuit_breaker_stats() -> List[Dict[str, Any]]:
    """Get statistics for all circuit breakers."""
    with _circuit_breaker_lock:
        return [cb.get_stats() for cb in _circuit_breakers.values()]
