"""Add JWT authentication models - TokenBlacklist and SecurityEvent

Revision ID: 7db7e0b9dacb
Revises: postgresql_optimized
Create Date: 2025-05-24 06:15:33.681091+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7db7e0b9dacb'
down_revision = 'postgresql_optimized'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('security_events',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('event_category', sa.String(length=30), nullable=False),
    sa.Column('severity', sa.String(length=20), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('details', sa.JSON(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('correlation_id', sa.String(length=255), nullable=True),
    sa.Column('resolved', sa.Boolean(), nullable=False),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved_by', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['resolved_by'], ['users.id'], name=op.f('fk_security_events_resolved_by_users'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_security_events_user_id_users'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_security_events'))
    )
    with op.batch_alter_table('security_events', schema=None) as batch_op:
        batch_op.create_index('idx_security_events_category', ['event_category'], unique=False)
        batch_op.create_index('idx_security_events_correlation', ['correlation_id'], unique=False)
        batch_op.create_index('idx_security_events_created', ['created_at'], unique=False)
        batch_op.create_index('idx_security_events_ip', ['ip_address'], unique=False)
        batch_op.create_index('idx_security_events_severity', ['severity'], unique=False)
        batch_op.create_index('idx_security_events_type', ['event_type'], unique=False)
        batch_op.create_index('idx_security_events_unresolved', ['resolved', 'severity'], unique=False)
        batch_op.create_index('idx_security_events_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_security_events_user_type', ['user_id', 'event_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_event_category'), ['event_category'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_event_type'), ['event_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_ip_address'), ['ip_address'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_security_events_uuid'), ['uuid'], unique=True)

    op.create_table('token_blacklist',
    sa.Column('jti', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token_type', sa.String(length=20), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('blacklisted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('reason', sa.String(length=100), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_token_blacklist_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_token_blacklist'))
    )
    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.create_index('idx_token_blacklist_expires_at', ['expires_at'], unique=False)
        batch_op.create_index('idx_token_blacklist_jti', ['jti'], unique=False)
        batch_op.create_index('idx_token_blacklist_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_token_blacklist_user_type', ['user_id', 'token_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_expires_at'), ['expires_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_jti'), ['jti'], unique=True)
        batch_op.create_index(batch_op.f('ix_token_blacklist_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('role',
               existing_type=postgresql.ENUM('admin', 'customer', 'vendor', 'moderator', name='user_role'),
               type_=sa.String(length=20),
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')
        batch_op.drop_index('idx_users_created_at_btree')
        batch_op.drop_index('idx_users_email_trgm', postgresql_using='gin')
        batch_op.drop_index('idx_users_name_trgm', postgresql_using='gin')
        batch_op.drop_index('idx_users_search_vector_gin', postgresql_using='gin')
        batch_op.drop_column('search_vector')

    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.alter_column('document_type',
               existing_type=postgresql.ENUM('business_license', 'tax_certificate', 'insurance_certificate', 'professional_certification', 'identity_document', 'utility_bill', name='document_type'),
               type_=sa.Enum('BUSINESS_LICENSE', 'TAX_CERTIFICATE', 'IDENTITY_DOCUMENT', 'PROFESSIONAL_CERTIFICATION', 'INSURANCE_CERTIFICATE', 'BANK_STATEMENT', 'UTILITY_BILL', 'OTHER', name='documenttype'),
               existing_nullable=False)
        batch_op.alter_column('status',
               existing_type=postgresql.ENUM('pending', 'approved', 'rejected', 'expired', name='document_status'),
               type_=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED', name='documentstatus'),
               existing_nullable=False)
        batch_op.alter_column('verification_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')
        batch_op.drop_index('idx_vendor_documents_verification_data_gin', postgresql_using='gin')

    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.alter_column('operating_hours',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('social_media_links',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('languages_spoken',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('specializations',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('certifications',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('gallery_images',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('insurance_details',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('additional_info',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')
        batch_op.drop_index('idx_vendor_profiles_languages_gin', postgresql_using='gin')
        batch_op.drop_index('idx_vendor_profiles_latitude')
        batch_op.drop_index('idx_vendor_profiles_longitude')
        batch_op.drop_index('idx_vendor_profiles_search_vector_gin', postgresql_using='gin')
        batch_op.drop_index('idx_vendor_profiles_specializations_gin', postgresql_using='gin')
        batch_op.drop_column('search_vector')

    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.alter_column('business_type',
               existing_type=postgresql.ENUM('guide', 'restaurant', 'hotel', 'transport', 'activity_provider', 'private_beach', 'apartment_rental', 'car_hire', 'security_service', 'other', name='vendor_type'),
               type_=sa.Enum('GUIDE', 'RESTAURANT', 'HOTEL', 'TRANSPORT', 'ACTIVITY_PROVIDER', 'PRIVATE_BEACH', 'APARTMENT_RENTAL', 'CAR_HIRE', 'SECURITY_SERVICE', 'OTHER', name='vendortype'),
               existing_nullable=False)
        batch_op.alter_column('verification_status',
               existing_type=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'suspended', name='verification_status'),
               type_=sa.Enum('PENDING', 'UNDER_REVIEW', 'VERIFIED', 'REJECTED', 'SUSPENDED', 'RESUBMISSION_REQUIRED', name='verificationstatus'),
               existing_nullable=False)
        batch_op.alter_column('marketplace_status',
               existing_type=postgresql.ENUM('active', 'inactive', 'suspended', 'pending', name='marketplace_status'),
               type_=sa.Enum('ACTIVE', 'INACTIVE', 'PAUSED', 'SUSPENDED', 'BANNED', name='marketplacestatus'),
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid::uuid')
        batch_op.drop_index('idx_vendors_performance_composite')

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.create_index('idx_vendors_performance_composite', ['average_rating', 'total_bookings', 'marketplace_status'], unique=False)
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('marketplace_status',
               existing_type=sa.Enum('ACTIVE', 'INACTIVE', 'PAUSED', 'SUSPENDED', 'BANNED', name='marketplacestatus'),
               type_=postgresql.ENUM('active', 'inactive', 'suspended', 'pending', name='marketplace_status'),
               existing_nullable=False)
        batch_op.alter_column('verification_status',
               existing_type=sa.Enum('PENDING', 'UNDER_REVIEW', 'VERIFIED', 'REJECTED', 'SUSPENDED', 'RESUBMISSION_REQUIRED', name='verificationstatus'),
               type_=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'suspended', name='verification_status'),
               existing_nullable=False)
        batch_op.alter_column('business_type',
               existing_type=sa.Enum('GUIDE', 'RESTAURANT', 'HOTEL', 'TRANSPORT', 'ACTIVITY_PROVIDER', 'PRIVATE_BEACH', 'APARTMENT_RENTAL', 'CAR_HIRE', 'SECURITY_SERVICE', 'OTHER', name='vendortype'),
               type_=postgresql.ENUM('guide', 'restaurant', 'hotel', 'transport', 'activity_provider', 'private_beach', 'apartment_rental', 'car_hire', 'security_service', 'other', name='vendor_type'),
               existing_nullable=False)

    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True))
        batch_op.create_index('idx_vendor_profiles_specializations_gin', ['specializations'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_vendor_profiles_search_vector_gin', ['search_vector'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_vendor_profiles_longitude', ['longitude'], unique=False)
        batch_op.create_index('idx_vendor_profiles_latitude', ['latitude'], unique=False)
        batch_op.create_index('idx_vendor_profiles_languages_gin', ['languages_spoken'], unique=False, postgresql_using='gin')
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('additional_info',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('insurance_details',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('gallery_images',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('certifications',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('specializations',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('languages_spoken',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('social_media_links',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('operating_hours',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)

    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.create_index('idx_vendor_documents_verification_data_gin', ['verification_data'], unique=False, postgresql_using='gin')
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('verification_data',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED', name='documentstatus'),
               type_=postgresql.ENUM('pending', 'approved', 'rejected', 'expired', name='document_status'),
               existing_nullable=False)
        batch_op.alter_column('document_type',
               existing_type=sa.Enum('BUSINESS_LICENSE', 'TAX_CERTIFICATE', 'IDENTITY_DOCUMENT', 'PROFESSIONAL_CERTIFICATION', 'INSURANCE_CERTIFICATE', 'BANK_STATEMENT', 'UTILITY_BILL', 'OTHER', name='documenttype'),
               type_=postgresql.ENUM('business_license', 'tax_certificate', 'insurance_certificate', 'professional_certification', 'identity_document', 'utility_bill', name='document_type'),
               existing_nullable=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True))
        batch_op.create_index('idx_users_search_vector_gin', ['search_vector'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_name_trgm', [sa.text("((first_name::text || ' '::text) || last_name::text)")], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_email_trgm', ['email'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_created_at_btree', ['created_at'], unique=False)
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('role',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('admin', 'customer', 'vendor', 'moderator', name='user_role'),
               existing_nullable=False)

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)

    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)

    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_token_blacklist_uuid'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_user_id'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_updated_by'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_updated_at'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_jti'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_id'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_expires_at'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_created_by'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_created_at'))
        batch_op.drop_index('idx_token_blacklist_user_type')
        batch_op.drop_index('idx_token_blacklist_user_id')
        batch_op.drop_index('idx_token_blacklist_jti')
        batch_op.drop_index('idx_token_blacklist_expires_at')

    op.drop_table('token_blacklist')
    with op.batch_alter_table('security_events', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_security_events_uuid'))
        batch_op.drop_index(batch_op.f('ix_security_events_user_id'))
        batch_op.drop_index(batch_op.f('ix_security_events_updated_by'))
        batch_op.drop_index(batch_op.f('ix_security_events_updated_at'))
        batch_op.drop_index(batch_op.f('ix_security_events_ip_address'))
        batch_op.drop_index(batch_op.f('ix_security_events_id'))
        batch_op.drop_index(batch_op.f('ix_security_events_event_type'))
        batch_op.drop_index(batch_op.f('ix_security_events_event_category'))
        batch_op.drop_index(batch_op.f('ix_security_events_created_by'))
        batch_op.drop_index(batch_op.f('ix_security_events_created_at'))
        batch_op.drop_index(batch_op.f('ix_security_events_correlation_id'))
        batch_op.drop_index('idx_security_events_user_type')
        batch_op.drop_index('idx_security_events_user_id')
        batch_op.drop_index('idx_security_events_unresolved')
        batch_op.drop_index('idx_security_events_type')
        batch_op.drop_index('idx_security_events_severity')
        batch_op.drop_index('idx_security_events_ip')
        batch_op.drop_index('idx_security_events_created')
        batch_op.drop_index('idx_security_events_correlation')
        batch_op.drop_index('idx_security_events_category')

    op.drop_table('security_events')
    # ### end Alembic commands ###
