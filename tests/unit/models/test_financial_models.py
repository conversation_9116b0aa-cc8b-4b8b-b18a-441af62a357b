"""
Unit tests for financial models.

This module provides comprehensive unit tests for financial-related database models:
- RevenueRecord: Comprehensive revenue tracking and analytics
- ReconciliationRecord: Financial reconciliation and audit trails
- Enum validation and business logic constraints
- Multi-currency support and tax compliance features
- Financial reporting and analytics validation

Implements Phase 1 Payment & Transaction Management System with >85% test coverage.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from sqlalchemy.exc import IntegrityError

from app.models.financial_models import (
    RevenueRecord, ReconciliationRecord, RevenueCategory, ReconciliationStatus
)
from app.core.payment.config import PaymentProviderType


class TestRevenueRecordModel:
    """Test RevenueRecord model functionality."""

    def test_revenue_record_creation(self):
        """Test basic revenue record creation."""
        transaction_date = datetime.now(timezone.utc)
        settlement_date = transaction_date + timedelta(days=2)

        revenue = RevenueRecord(
            booking_id=1,
            payment_id=1,
            vendor_id=1,
            user_id=1,
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),
            platform_fee=Decimal("50.00"),
            processing_fee=Decimal("30.00"),
            net_vendor_amount=Decimal("920.00"),
            net_platform_amount=Decimal("80.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            payment_method="card",
            transaction_date=transaction_date,
            settlement_date=settlement_date,
            reference_id="REV-2025-001234"
        )

        assert revenue.booking_id == 1
        assert revenue.payment_id == 1
        assert revenue.vendor_id == 1
        assert revenue.user_id == 1
        assert revenue.category == RevenueCategory.SERVICE_BOOKING
        assert revenue.gross_amount == Decimal("1000.00")
        assert revenue.platform_fee == Decimal("50.00")
        assert revenue.processing_fee == Decimal("30.00")
        assert revenue.net_vendor_amount == Decimal("920.00")
        assert revenue.net_platform_amount == Decimal("80.00")
        assert revenue.currency == "NGN"
        assert revenue.provider == PaymentProviderType.PAYSTACK
        assert revenue.payment_method == "card"
        assert revenue.transaction_date == transaction_date
        assert revenue.settlement_date == settlement_date
        assert revenue.reference_id == "REV-2025-001234"

    def test_revenue_category_enum_values(self):
        """Test revenue category enum values."""
        expected_categories = {
            "service_booking", "platform_fee", "payment_processing",
            "subscription", "advertising", "other"
        }
        actual_categories = {category.value for category in RevenueCategory}
        assert actual_categories == expected_categories

    def test_revenue_record_defaults(self):
        """Test revenue record default values."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-DEFAULTS-001"
        )

        # Test default values (SQLAlchemy defaults are applied on save, not instantiation)
        # For unit tests, we test the column defaults are defined correctly
        from app.models.financial_models import RevenueRecord as RevenueRecordModel

        # Check that defaults are defined in the model
        assert RevenueRecordModel.__table__.columns['platform_fee'].default.arg == Decimal("0.00")
        assert RevenueRecordModel.__table__.columns['processing_fee'].default.arg == Decimal("0.00")
        assert RevenueRecordModel.__table__.columns['net_vendor_amount'].default.arg == Decimal("0.00")
        assert RevenueRecordModel.__table__.columns['net_platform_amount'].default.arg == Decimal("0.00")
        assert RevenueRecordModel.__table__.columns['currency'].default.arg == "NGN"
        assert RevenueRecordModel.__table__.columns['tax_amount'].default.arg == Decimal("0.00")
        assert RevenueRecordModel.__table__.columns['tax_rate'].default.arg == Decimal("0.0000")

    def test_revenue_record_calculations(self):
        """Test revenue record calculation properties."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),
            platform_fee=Decimal("50.00"),
            processing_fee=Decimal("30.00"),
            tax_amount=Decimal("75.00"),
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-CALC-001"
        )

        # Test total fees calculation (includes tax)
        assert revenue.total_fees == Decimal("155.00")  # 50 + 30 + 75

        # Test platform revenue calculation
        assert revenue.platform_revenue == Decimal("80.00")  # 50 + 30

    def test_revenue_record_tax_calculations(self):
        """Test revenue record tax calculations."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),
            tax_rate=Decimal("0.075"),  # 7.5% VAT
            tax_amount=Decimal("75.00"),
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-TAX-001"
        )

        assert revenue.tax_rate == Decimal("0.075")
        assert revenue.tax_amount == Decimal("75.00")

    def test_revenue_record_currency_conversion(self):
        """Test revenue record currency conversion features."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("100.00"),  # USD amount
            currency="USD",
            original_currency="NGN",
            original_amount=Decimal("41000.00"),  # NGN amount
            exchange_rate=Decimal("410.0000"),  # 1 USD = 410 NGN
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-CURRENCY-001"
        )

        assert revenue.currency == "USD"
        assert revenue.original_currency == "NGN"
        assert revenue.original_amount == Decimal("41000.00")
        assert revenue.exchange_rate == Decimal("410.0000")

    def test_revenue_record_provider_tracking(self):
        """Test revenue record provider tracking."""
        # Stripe revenue
        stripe_revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("100.00"),
            currency="USD",
            provider=PaymentProviderType.STRIPE,
            payment_method="card",
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-STRIPE-001"
        )

        assert stripe_revenue.provider == PaymentProviderType.STRIPE
        assert stripe_revenue.currency == "USD"

        # Paystack revenue
        paystack_revenue = RevenueRecord(
            category=RevenueCategory.PLATFORM_FEE,
            gross_amount=Decimal("5000.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            payment_method="bank_transfer",
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-PAYSTACK-001"
        )

        assert paystack_revenue.provider == PaymentProviderType.PAYSTACK
        assert paystack_revenue.currency == "NGN"

        # Busha revenue
        busha_revenue = RevenueRecord(
            category=RevenueCategory.PAYMENT_PROCESSING,
            gross_amount=Decimal("0.01"),
            currency="BTC",
            provider=PaymentProviderType.BUSHA,
            payment_method="crypto",
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-BUSHA-001"
        )

        assert busha_revenue.provider == PaymentProviderType.BUSHA
        assert busha_revenue.currency == "BTC"

    def test_revenue_record_metadata(self):
        """Test revenue record metadata handling."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-METADATA-001",
            revenue_metadata={
                "booking_type": "cultural_tour",
                "commission_rate": "5%",
                "promotional_discount": "10%",
                "customer_segment": "premium"
            },
            external_reference="EXT-REF-123456"
        )

        assert revenue.revenue_metadata["booking_type"] == "cultural_tour"
        assert revenue.revenue_metadata["commission_rate"] == "5%"
        assert revenue.external_reference == "EXT-REF-123456"

    def test_revenue_record_repr(self):
        """Test revenue record string representation."""
        revenue = RevenueRecord(
            id=123,
            category=RevenueCategory.SERVICE_BOOKING,
            reference_id="REV-REPR-001",
            gross_amount=Decimal("1000.00")
        )

        expected_repr = "<RevenueRecord(id=123, ref='REV-REPR-001', category='RevenueCategory.SERVICE_BOOKING', amount=1000.00)>"
        assert repr(revenue) == expected_repr


class TestReconciliationRecordModel:
    """Test ReconciliationRecord model functionality."""

    def test_reconciliation_record_creation(self):
        """Test basic reconciliation record creation."""
        period_start = datetime.now(timezone.utc) - timedelta(days=1)
        period_end = datetime.now(timezone.utc)
        reconciled_time = datetime.now(timezone.utc)

        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=period_start,
            period_end=period_end,
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("9950.00"),
            variance=Decimal("-50.00"),
            currency="NGN",
            status=ReconciliationStatus.COMPLETED,
            expected_transaction_count=25,
            actual_transaction_count=24,
            reconciled_at=reconciled_time,
            reconciled_by=1,
            reference_id="REC-2025-001234"
        )

        assert reconciliation.provider == PaymentProviderType.PAYSTACK
        assert reconciliation.period_start == period_start
        assert reconciliation.period_end == period_end
        assert reconciliation.expected_amount == Decimal("10000.00")
        assert reconciliation.actual_amount == Decimal("9950.00")
        assert reconciliation.variance == Decimal("-50.00")
        assert reconciliation.currency == "NGN"
        assert reconciliation.status == ReconciliationStatus.COMPLETED
        assert reconciliation.expected_transaction_count == 25
        assert reconciliation.actual_transaction_count == 24
        assert reconciliation.reconciled_at == reconciled_time
        assert reconciliation.reconciled_by == 1
        assert reconciliation.reference_id == "REC-2025-001234"

    def test_reconciliation_status_enum_values(self):
        """Test reconciliation status enum values."""
        expected_statuses = {
            "pending", "in_progress", "completed", "failed",
            "discrepancy", "manual_review"
        }
        actual_statuses = {status.value for status in ReconciliationStatus}
        assert actual_statuses == expected_statuses

    def test_reconciliation_record_defaults(self):
        """Test reconciliation record default values."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("10000.00"),
            reference_id="REC-DEFAULTS-001"
        )

        # Test default values (SQLAlchemy defaults are applied on save, not instantiation)
        # For unit tests, we test the column defaults are defined correctly
        from app.models.financial_models import ReconciliationRecord as ReconciliationRecordModel

        # Check that defaults are defined in the model
        assert ReconciliationRecordModel.__table__.columns['variance'].default.arg == Decimal("0.00")
        assert ReconciliationRecordModel.__table__.columns['currency'].default.arg == "NGN"
        assert ReconciliationRecordModel.__table__.columns['status'].default.arg == 'pending'
        assert ReconciliationRecordModel.__table__.columns['expected_transaction_count'].default.arg == 0
        assert ReconciliationRecordModel.__table__.columns['actual_transaction_count'].default.arg == 0

    def test_reconciliation_record_properties(self):
        """Test reconciliation record model properties."""
        # Test reconciled record
        reconciled_record = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("10000.00"),
            variance=Decimal("0.00"),
            status=ReconciliationStatus.COMPLETED,
            reference_id="REC-RECONCILED-001"
        )

        assert reconciled_record.is_completed is True
        assert reconciled_record.has_discrepancy is False

        # Test record with discrepancy
        discrepancy_record = ReconciliationRecord(
            provider=PaymentProviderType.STRIPE,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("5000.00"),
            actual_amount=Decimal("4950.00"),
            variance=Decimal("-50.00"),
            status=ReconciliationStatus.DISCREPANCY,
            reference_id="REC-DISCREPANCY-001"
        )

        assert discrepancy_record.is_completed is False
        assert discrepancy_record.has_discrepancy is True

        # Test pending record
        pending_record = ReconciliationRecord(
            provider=PaymentProviderType.BUSHA,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("1000.00"),
            actual_amount=Decimal("1000.00"),
            variance=Decimal("0.00"),
            status=ReconciliationStatus.PENDING,
            reference_id="REC-PENDING-001"
        )

        assert pending_record.is_completed is False
        assert pending_record.has_discrepancy is False

    def test_reconciliation_variance_calculations(self):
        """Test reconciliation variance calculations."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("9950.00"),
            variance=Decimal("-50.00"),
            reference_id="REC-VARIANCE-001"
        )

        # Test variance percentage (available property)
        assert reconciliation.variance_percentage == Decimal("-0.50")  # -50 / 10000 * 100

    def test_reconciliation_transaction_count_variance(self):
        """Test reconciliation transaction count variance."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("10000.00"),
            expected_transaction_count=25,
            actual_transaction_count=24,
            reference_id="REC-COUNT-VARIANCE-001"
        )

        # Test transaction count fields
        assert reconciliation.expected_transaction_count == 25
        assert reconciliation.actual_transaction_count == 24

    def test_reconciliation_provider_statement_tracking(self):
        """Test reconciliation provider statement tracking."""
        statement_date = datetime.now(timezone.utc) - timedelta(hours=6)

        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.STRIPE,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("5000.00"),
            actual_amount=Decimal("5000.00"),
            provider_statement_id="stmt_stripe_123456",
            provider_statement_date=statement_date,
            reference_id="REC-STATEMENT-001"
        )

        assert reconciliation.provider_statement_id == "stmt_stripe_123456"
        assert reconciliation.provider_statement_date == statement_date

    def test_reconciliation_discrepancy_tracking(self):
        """Test reconciliation discrepancy tracking."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("9950.00"),
            variance=Decimal("-50.00"),
            status=ReconciliationStatus.DISCREPANCY,
            discrepancy_details={
                "missing_transactions": ["txn_123", "txn_456"],
                "amount_differences": [
                    {"txn_id": "txn_789", "expected": "100.00", "actual": "95.00"}
                ],
                "investigation_notes": "Possible refund not recorded"
            },
            resolution_notes="Discrepancy resolved - refund confirmed",
            reference_id="REC-DISCREPANCY-DETAIL-001"
        )

        assert reconciliation.discrepancy_details["missing_transactions"] == ["txn_123", "txn_456"]
        assert reconciliation.resolution_notes == "Discrepancy resolved - refund confirmed"

    def test_reconciliation_metadata(self):
        """Test reconciliation metadata handling."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("10000.00"),
            reconciliation_metadata={
                "reconciliation_method": "automated",
                "data_sources": ["internal_db", "provider_api", "webhook_logs"],
                "processing_time_seconds": 45,
                "confidence_score": 0.98
            },
            reference_id="REC-METADATA-001"
        )

        assert reconciliation.reconciliation_metadata["reconciliation_method"] == "automated"
        assert reconciliation.reconciliation_metadata["confidence_score"] == 0.98

    def test_reconciliation_record_repr(self):
        """Test reconciliation record string representation."""
        reconciliation = ReconciliationRecord(
            id=456,
            provider=PaymentProviderType.PAYSTACK,
            reference_id="REC-REPR-001",
            status=ReconciliationStatus.COMPLETED
        )

        expected_repr = "<ReconciliationRecord(id=456, provider='PaymentProviderType.PAYSTACK', ref='REC-REPR-001', status='ReconciliationStatus.COMPLETED')>"
        assert repr(reconciliation) == expected_repr


class TestFinancialModelConstraints:
    """Test financial model constraints and validations."""

    def test_revenue_record_amount_constraints(self):
        """Test revenue record amount validation constraints."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("1000.00"),  # Must be > 0
            platform_fee=Decimal("50.00"),    # Must be >= 0
            processing_fee=Decimal("30.00"),  # Must be >= 0
            net_vendor_amount=Decimal("920.00"),     # Must be >= 0
            net_platform_amount=Decimal("80.00"),   # Must be >= 0
            tax_amount=Decimal("75.00"),       # Must be >= 0
            tax_rate=Decimal("0.075"),         # Must be 0.0-1.0
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-CONSTRAINT-001"
        )

        assert revenue.gross_amount > 0
        assert revenue.platform_fee >= 0
        assert revenue.processing_fee >= 0
        assert revenue.net_vendor_amount >= 0
        assert revenue.net_platform_amount >= 0
        assert revenue.tax_amount >= 0
        assert 0.0 <= revenue.tax_rate <= 1.0

    def test_revenue_record_exchange_rate_constraints(self):
        """Test revenue record exchange rate constraints."""
        revenue = RevenueRecord(
            category=RevenueCategory.SERVICE_BOOKING,
            gross_amount=Decimal("100.00"),
            currency="USD",
            original_currency="NGN",
            original_amount=Decimal("41000.00"),
            exchange_rate=Decimal("410.0000"),  # Must be > 0 if not null
            transaction_date=datetime.now(timezone.utc),
            reference_id="REV-EXCHANGE-CONSTRAINT-001"
        )

        assert revenue.exchange_rate > 0

    def test_reconciliation_record_amount_constraints(self):
        """Test reconciliation record amount validation constraints."""
        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=datetime.now(timezone.utc) - timedelta(days=1),
            period_end=datetime.now(timezone.utc),
            expected_amount=Decimal("10000.00"),  # Must be >= 0
            actual_amount=Decimal("9950.00"),     # Must be >= 0
            expected_transaction_count=25,        # Must be >= 0
            actual_transaction_count=24,          # Must be >= 0
            reference_id="REC-CONSTRAINT-001"
        )

        assert reconciliation.expected_amount >= 0
        assert reconciliation.actual_amount >= 0
        assert reconciliation.expected_transaction_count >= 0
        assert reconciliation.actual_transaction_count >= 0

    def test_reconciliation_record_period_constraints(self):
        """Test reconciliation record period validation constraints."""
        period_start = datetime.now(timezone.utc) - timedelta(days=1)
        period_end = datetime.now(timezone.utc)

        reconciliation = ReconciliationRecord(
            provider=PaymentProviderType.PAYSTACK,
            period_start=period_start,
            period_end=period_end,
            expected_amount=Decimal("10000.00"),
            actual_amount=Decimal("10000.00"),
            reference_id="REC-PERIOD-CONSTRAINT-001"
        )

        # Test period start < period end constraint
        assert reconciliation.period_start < reconciliation.period_end
