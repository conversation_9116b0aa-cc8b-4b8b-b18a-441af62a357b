"""
Integration tests for payment API endpoints.

This module provides comprehensive integration tests for payment endpoints including:
- Payment creation and processing with Paystack integration
- Payment status management and verification
- Payment method management
- Webhook processing and validation

Implements Task 4.3.1 Phase 4 requirements for integration testing with
>80% test coverage and end-to-end payment flow validation.
"""

import pytest
import json
import hashlib
import hmac
from decimal import Decimal
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.payment import Payment, PaymentStatus
from app.models.user import User
from app.core.payment.config import PaymentProviderType
from tests.conftest import TestingSessionLocal


class TestPaymentEndpoints:
    """Integration test suite for payment API endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Create test user."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    def auth_headers(self, test_user):
        """Create authentication headers."""
        # Mock JWT token for testing
        with patch('app.core.security.verify_jwt_token') as mock_verify:
            mock_verify.return_value = {"sub": str(test_user.id), "email": test_user.email}
            return {"Authorization": "Bearer test_token"}

    @pytest.fixture
    def sample_payment_data(self):
        """Sample payment creation data."""
        return {
            "booking_id": 1,
            "amount": "1000.00",
            "currency": "NGN",
            "return_url": "https://example.com/return",
            "metadata": {"test": "data"}
        }

    @pytest.mark.asyncio
    async def test_create_payment_success(self, client, auth_headers, sample_payment_data, test_user):
        """Test successful payment creation."""
        with patch('app.services.payment.paystack_service.PaystackService') as mock_paystack:
            # Mock Paystack service
            mock_service = AsyncMock()
            mock_service.initialize_payment.return_value = MagicMock(
                authorization_url="https://checkout.paystack.com/test123",
                access_code="test_access_code",
                reference="test_ref_123"
            )
            mock_paystack.return_value = mock_service

            # Mock get_current_user dependency
            with patch('app.core.security.get_current_user', return_value=test_user):
                response = client.post(
                    "/api/v1/payments/create",
                    json=sample_payment_data,
                    headers=auth_headers
                )

            assert response.status_code == 201
            data = response.json()
            assert data["amount"] == "1000.00"
            assert data["currency"] == "NGN"
            assert data["status"] == "pending"
            assert data["user_id"] == test_user.id

    @pytest.mark.asyncio
    async def test_create_payment_paystack_error(self, client, auth_headers, sample_payment_data, test_user):
        """Test payment creation with Paystack error."""
        with patch('app.services.payment.paystack_service.PaystackService') as mock_paystack:
            # Mock Paystack service error
            mock_service = AsyncMock()
            mock_service.initialize_payment.side_effect = Exception("Paystack error")
            mock_paystack.return_value = mock_service

            # Mock get_current_user dependency
            with patch('app.core.security.get_current_user', return_value=test_user):
                response = client.post(
                    "/api/v1/payments/create",
                    json=sample_payment_data,
                    headers=auth_headers
                )

            # Should still create payment record but mark as failed
            assert response.status_code == 201
            data = response.json()
            assert data["status"] in ["pending", "failed"]  # Depends on implementation

    def test_create_payment_unauthorized(self, client, sample_payment_data):
        """Test payment creation without authentication."""
        response = client.post(
            "/api/v1/payments/create",
            json=sample_payment_data
        )
        assert response.status_code == 401

    def test_create_payment_invalid_data(self, client, auth_headers, test_user):
        """Test payment creation with invalid data."""
        invalid_data = {
            "booking_id": "invalid",  # Should be integer
            "amount": "-100.00",      # Should be positive
            "currency": "INVALID"     # Invalid currency
        }

        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.post(
                "/api/v1/payments/create",
                json=invalid_data,
                headers=auth_headers
            )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_payment_success(self, client, auth_headers, test_user, db_session):
        """Test successful payment retrieval."""
        # Create test payment
        payment = Payment(
            booking_id=1,
            user_id=test_user.id,
            vendor_id=1,
            amount=Decimal("1000.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            status=PaymentStatus.PENDING,
            transaction_reference="test_ref_123"
        )
        db_session.add(payment)
        await db_session.commit()
        await db_session.refresh(payment)

        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.get(
                f"/api/v1/payments/{payment.id}",
                headers=auth_headers
            )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == payment.id
        assert data["amount"] == "1000.00"
        assert data["transaction_reference"] == "test_ref_123"

    def test_get_payment_not_found(self, client, auth_headers, test_user):
        """Test payment retrieval for non-existent payment."""
        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.get(
                "/api/v1/payments/99999",
                headers=auth_headers
            )

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_payment_access_denied(self, client, auth_headers, db_session):
        """Test payment retrieval access control."""
        # Create another user
        other_user = User(
            email="<EMAIL>",
            username="otheruser",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=True
        )
        db_session.add(other_user)
        await db_session.commit()
        await db_session.refresh(other_user)

        # Create payment for other user
        payment = Payment(
            booking_id=1,
            user_id=other_user.id,
            vendor_id=1,
            amount=Decimal("1000.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            status=PaymentStatus.PENDING,
            transaction_reference="test_ref_123"
        )
        db_session.add(payment)
        await db_session.commit()
        await db_session.refresh(payment)

        # Try to access with different user
        current_user = User(id=999, email="<EMAIL>")
        with patch('app.core.security.get_current_user', return_value=current_user):
            response = client.get(
                f"/api/v1/payments/{payment.id}",
                headers=auth_headers
            )

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_get_user_payments(self, client, auth_headers, test_user, db_session):
        """Test user payment history retrieval."""
        # Create test payments
        for i in range(3):
            payment = Payment(
                booking_id=i + 1,
                user_id=test_user.id,
                vendor_id=1,
                amount=Decimal(f"{(i + 1) * 100}.00"),
                currency="NGN",
                provider=PaymentProviderType.PAYSTACK,
                status=PaymentStatus.PENDING,
                transaction_reference=f"test_ref_{i + 1}"
            )
            db_session.add(payment)

        await db_session.commit()

        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.get(
                f"/api/v1/payments/user/{test_user.id}",
                headers=auth_headers
            )

        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 3
        assert len(data["items"]) == 3

    def test_get_user_payments_access_denied(self, client, auth_headers, test_user):
        """Test user payment history access control."""
        other_user_id = 999

        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.get(
                f"/api/v1/payments/user/{other_user_id}",
                headers=auth_headers
            )

        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_process_payment_success(self, client, auth_headers, test_user, db_session):
        """Test successful payment processing."""
        # Create test payment
        payment = Payment(
            booking_id=1,
            user_id=test_user.id,
            vendor_id=1,
            amount=Decimal("1000.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            status=PaymentStatus.PENDING,
            transaction_reference="test_ref_123"
        )
        db_session.add(payment)
        await db_session.commit()
        await db_session.refresh(payment)

        with patch('app.services.payment.paystack_service.PaystackService') as mock_paystack:
            # Mock successful verification
            mock_service = AsyncMock()
            mock_service.verify_payment.return_value = MagicMock(
                status=True,
                gateway_response="Successful",
                reference="test_ref_123"
            )
            mock_paystack.return_value = mock_service

            process_data = {"payment_id": payment.id}

            with patch('app.core.security.get_current_user', return_value=test_user):
                response = client.post(
                    "/api/v1/payments/process",
                    json=process_data,
                    headers=auth_headers
                )

        assert response.status_code == 200
        data = response.json()
        assert data["payment_id"] == payment.id
        assert data["message"] == "Payment completed successfully"

    @pytest.mark.asyncio
    async def test_create_payment_method(self, client, auth_headers, test_user):
        """Test payment method creation."""
        method_data = {
            "method_type": "card",
            "provider": "paystack",
            "display_name": "Test Card",
            "last_four": "1234",
            "brand": "visa",
            "expiry_month": 12,
            "expiry_year": 2025
        }

        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.post(
                "/api/v1/payments/methods",
                json=method_data,
                headers=auth_headers
            )

        assert response.status_code == 201
        data = response.json()
        assert data["display_name"] == "Test Card"
        assert data["method_type"] == "card"
        assert data["user_id"] == test_user.id

    @pytest.mark.asyncio
    async def test_get_user_payment_methods(self, client, auth_headers, test_user):
        """Test user payment methods retrieval."""
        with patch('app.core.security.get_current_user', return_value=test_user):
            response = client.get(
                f"/api/v1/payments/methods/user/{test_user.id}",
                headers=auth_headers
            )

        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data


class TestWebhookEndpoints:
    """Integration test suite for webhook API endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def sample_webhook_payload(self):
        """Sample webhook payload."""
        return {
            "event": "charge.success",
            "data": {
                "id": 123456789,
                "reference": "test_ref_123",
                "amount": 100000,
                "currency": "NGN",
                "status": "success",
                "gateway_response": "Successful",
                "paid_at": "2025-01-27T10:00:00Z",
                "channel": "card"
            }
        }

    def test_paystack_webhook_success(self, client, sample_webhook_payload):
        """Test successful Paystack webhook processing."""
        payload = json.dumps(sample_webhook_payload).encode('utf-8')

        # Generate valid signature
        webhook_secret = "whsec_test_secret"
        signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()

        with patch('app.services.payment.paystack_service.PaystackService') as mock_paystack:
            mock_service = AsyncMock()
            mock_service.process_webhook_event.return_value = MagicMock(
                event_id="evt_123",
                event_type="charge.success",
                processed=True,
                payment_id=1,
                message="Payment completed successfully"
            )
            mock_paystack.return_value = mock_service

            response = client.post(
                "/api/v1/webhooks/paystack",
                content=payload,
                headers={"x-paystack-signature": signature}
            )

        assert response.status_code == 200
        data = response.json()
        assert data["processed"] is True
        assert data["event_type"] == "charge.success"

    def test_paystack_webhook_missing_signature(self, client, sample_webhook_payload):
        """Test webhook processing without signature."""
        payload = json.dumps(sample_webhook_payload).encode('utf-8')

        response = client.post(
            "/api/v1/webhooks/paystack",
            content=payload
        )

        assert response.status_code == 400
        assert "Missing webhook signature" in response.json()["detail"]

    def test_paystack_webhook_invalid_json(self, client):
        """Test webhook processing with invalid JSON."""
        invalid_payload = b"invalid json content"
        signature = "test_signature"

        response = client.post(
            "/api/v1/webhooks/paystack",
            content=invalid_payload,
            headers={"x-paystack-signature": signature}
        )

        assert response.status_code == 400
        assert "Invalid JSON payload" in response.json()["detail"]

    def test_paystack_webhook_invalid_signature(self, client, sample_webhook_payload):
        """Test webhook processing with invalid signature."""
        payload = json.dumps(sample_webhook_payload).encode('utf-8')
        invalid_signature = "invalid_signature"

        with patch('app.services.payment.paystack_service.PaystackService') as mock_paystack:
            mock_service = AsyncMock()
            mock_service.process_webhook_event.return_value = MagicMock(
                event_id="evt_123",
                event_type="charge.success",
                processed=False,
                payment_id=None,
                message="Processing failed: Invalid webhook signature"
            )
            mock_paystack.return_value = mock_service

            response = client.post(
                "/api/v1/webhooks/paystack",
                content=payload,
                headers={"x-paystack-signature": invalid_signature}
            )

        assert response.status_code == 200  # Webhook should return 200 even for processing failures
        data = response.json()
        assert data["processed"] is False
