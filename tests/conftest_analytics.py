"""
Analytics system test fixtures and configuration for Phase 7.1.

This module provides comprehensive test fixtures for analytics system testing:
- Model factories for all analytics entities
- Mock services and dependencies
- Test data generators
- Performance testing utilities
- Database rollback mechanisms

Implements Phase 7.1: Analytics & Performance System Testing Infrastructure with >85% code coverage.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from typing import Dict, Any, List
from unittest.mock import AsyncMock, MagicMock

from sqlalchemy.ext.asyncio import AsyncSession
from app.models.analytics_models import (
    UserAnalytics, VendorAnalytics, BookingAnalytics,
    SystemMetrics, DashboardWidget, KPIDefinition,
    AnalyticsTimeframe, MetricType
)


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


# ============================================================================
# ANALYTICS MODEL FACTORIES
# ============================================================================

@pytest.fixture
def user_analytics_factory():
    """Factory for creating UserAnalytics test instances."""
    def create_user_analytics(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "user_id": uuid4(),  # Use UUID instead of integer
            "timeframe": AnalyticsTimeframe.DAILY,
            "period_start": datetime.now(timezone.utc) - timedelta(days=1),
            "period_end": datetime.now(timezone.utc),
            "session_count": 5,  # Add missing field
            "total_session_duration": 2730,  # 45.5 minutes * 60 seconds
            "page_views": 25,
            "unique_pages_visited": 8,  # Add missing field
            "bookings_viewed": 3,  # Add missing field
            "bookings_created": 2,
            "bookings_completed": 1,  # Add missing field
            "total_spent": Decimal("150.00"),
            "average_order_value": Decimal("75.00"),  # Add missing field
            "device_type": "mobile",
            "location_country": "NG",
            "location_city": "Lagos",
            "custom_metrics": {
                "feature_usage": {"search_filters": 5, "wishlist_adds": 3},
                "user_journey": {"pages_visited": ["home", "search", "booking"]},
                "engagement_details": {"time_on_site": 2700, "bounce_rate": 0.25}
            }
        }
        defaults.update(kwargs)
        return UserAnalytics(**defaults)
    return create_user_analytics


@pytest.fixture
def vendor_analytics_factory():
    """Factory for creating VendorAnalytics test instances."""
    def create_vendor_analytics(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "vendor_id": 1,
            "timeframe": AnalyticsTimeframe.DAILY,
            "period_start": datetime.now(timezone.utc) - timedelta(days=1),
            "period_end": datetime.now(timezone.utc),
            "profile_views": 150,
            "service_views": 320,
            "booking_requests": 25,
            "bookings_completed": 20,
            "revenue_earned": Decimal("2500.00"),
            "average_rating": Decimal("4.75"),
            "response_time_hours": Decimal("2.50"),
            "cancellation_rate": Decimal("5.00"),
            "repeat_customer_rate": Decimal("35.00"),
            "efficiency_score": Decimal("85.50"),
            "growth_rate": Decimal("12.5000"),
            "market_share": Decimal("8.25"),
            "customer_acquisition_cost": Decimal("45.00"),
            "lifetime_value": Decimal("850.00"),
            "top_services": {
                "cultural_tours": {"bookings": 12, "revenue": 1200.00},
                "cooking_classes": {"bookings": 8, "revenue": 800.00}
            },
            "performance_metrics": {
                "response_rate": 0.95,
                "quality_score": 4.8,
                "reliability_index": 0.92
            },
            "custom_metrics": {
                "seasonal_performance": {"peak_months": ["Dec", "Jan"]},
                "customer_feedback": {"positive_reviews": 18, "negative_reviews": 2}
            }
        }
        defaults.update(kwargs)
        return VendorAnalytics(**defaults)
    return create_vendor_analytics


@pytest.fixture
def booking_analytics_factory():
    """Factory for creating BookingAnalytics test instances."""
    def create_booking_analytics(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "timeframe": AnalyticsTimeframe.DAILY,
            "period_start": datetime.now(timezone.utc) - timedelta(days=1),
            "period_end": datetime.now(timezone.utc),
            "total_bookings": 45,
            "completed_bookings": 38,
            "cancelled_bookings": 5,
            "pending_bookings": 2,
            "total_revenue": Decimal("4500.00"),
            "average_booking_value": Decimal("118.42"),
            "conversion_rate": Decimal("8.50"),
            "overall_conversion_rate": Decimal("7.25"),
            "top_countries": {
                "NG": {"bookings": 25, "revenue": 2500.00},
                "GH": {"bookings": 12, "revenue": 1200.00},
                "KE": {"bookings": 8, "revenue": 800.00}
            },
            "top_cities": {
                "Lagos": {"bookings": 15, "revenue": 1500.00},
                "Accra": {"bookings": 10, "revenue": 1000.00},
                "Nairobi": {"bookings": 8, "revenue": 800.00}
            },
            "device_breakdown": {
                "mobile": {"bookings": 28, "percentage": 62.2},
                "desktop": {"bookings": 12, "percentage": 26.7},
                "tablet": {"bookings": 5, "percentage": 11.1}
            },
            "payment_method_breakdown": {
                "paystack": {"bookings": 25, "revenue": 2500.00},
                "stripe": {"bookings": 15, "revenue": 1500.00},
                "busha": {"bookings": 5, "revenue": 500.00}
            },
            "custom_metrics": {
                "booking_sources": {"web": 30, "mobile_app": 15},
                "time_to_booking": {"average_hours": 24.5},
                "customer_segments": {"new": 28, "returning": 17}
            }
        }
        defaults.update(kwargs)
        return BookingAnalytics(**defaults)
    return create_booking_analytics


@pytest.fixture
def system_metrics_factory():
    """Factory for creating SystemMetrics test instances."""
    def create_system_metrics(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "metric_name": "api_response_time",
            "metric_type": MetricType.TIMER,
            "value": Decimal("125.500000"),
            "timeframe": AnalyticsTimeframe.HOURLY,
            "recorded_at": datetime.now(timezone.utc),
            "tags": {
                "endpoint": "/api/v1/bookings",
                "method": "POST",
                "status_code": 200
            },
            "metric_metadata": {
                "server_id": "web-01",
                "region": "us-west-2",
                "version": "1.0.0"
            }
        }
        defaults.update(kwargs)
        return SystemMetrics(**defaults)
    return create_system_metrics


@pytest.fixture
def dashboard_widget_factory():
    """Factory for creating DashboardWidget test instances."""
    def create_dashboard_widget(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "name": "Revenue Chart",
            "widget_type": "line_chart",
            "data_source": "booking_analytics",
            "configuration": {
                "chart_type": "line",
                "data_points": 30,
                "refresh_rate": 300,
                "filters": {"timeframe": "daily"},
                "display_options": {
                    "show_legend": True,
                    "show_grid": True,
                    "color_scheme": "blue"
                }
            },
            "position_x": 0,
            "position_y": 0,
            "width": 6,
            "height": 4,
            "is_active": True,
            "refresh_interval_seconds": 300
        }
        defaults.update(kwargs)
        return DashboardWidget(**defaults)
    return create_dashboard_widget


@pytest.fixture
def kpi_definition_factory():
    """Factory for creating KPIDefinition test instances."""
    def create_kpi_definition(**kwargs):
        defaults = {
            "uuid": uuid4(),
            "name": "Monthly Revenue Growth",
            "category": "revenue",
            "description": "Month-over-month revenue growth percentage",
            "calculation_method": "percentage_change",
            "data_source": "booking_analytics",
            "target_value": Decimal("15.0000"),
            "warning_threshold": Decimal("10.0000"),
            "critical_threshold": Decimal("5.0000"),
            "unit_of_measurement": "percentage",
            "is_active": True,
            "configuration": {
                "calculation_period": "monthly",
                "comparison_period": "previous_month",
                "aggregation_method": "sum",
                "data_fields": ["total_revenue"],
                "filters": {"status": "completed"}
            }
        }
        defaults.update(kwargs)
        return KPIDefinition(**defaults)
    return create_kpi_definition


# ============================================================================
# MOCK SERVICES AND REPOSITORIES
# ============================================================================

@pytest.fixture
def mock_analytics_repository():
    """Mock analytics repository for testing."""
    repository = AsyncMock()

    # Mock repository methods
    repository.create_user_analytics = AsyncMock()
    repository.get_user_analytics_by_id = AsyncMock()
    repository.get_user_analytics_aggregated = AsyncMock()
    repository.create_vendor_analytics = AsyncMock()
    repository.get_vendor_analytics_by_id = AsyncMock()
    repository.get_vendor_analytics_aggregated = AsyncMock()
    repository.create_booking_analytics = AsyncMock()
    repository.get_booking_analytics_aggregated = AsyncMock()
    repository.get_conversion_funnel_analytics = AsyncMock()

    return repository


@pytest.fixture
def mock_dashboard_repository():
    """Mock dashboard repository for testing."""
    repository = AsyncMock()

    # Mock repository methods
    repository.create_dashboard_widget = AsyncMock()
    repository.get_dashboard_widget_by_id = AsyncMock()
    repository.get_dashboard_widgets = AsyncMock()
    repository.update_dashboard_widget = AsyncMock()
    repository.delete_dashboard_widget = AsyncMock()
    repository.record_system_metric = AsyncMock()
    repository.get_metrics_time_series = AsyncMock()
    repository.get_metrics_aggregated = AsyncMock()

    return repository


@pytest.fixture
def mock_kpi_repository():
    """Mock KPI repository for testing."""
    repository = AsyncMock()

    # Mock repository methods
    repository.create_kpi_definition = AsyncMock()
    repository.get_kpi_definition_by_id = AsyncMock()
    repository.get_kpi_definitions = AsyncMock()
    repository.update_kpi_definition = AsyncMock()
    repository.delete_kpi_definition = AsyncMock()
    repository.calculate_kpi_value = AsyncMock()
    repository.get_kpi_history = AsyncMock()

    return repository


@pytest.fixture
def mock_analytics_service():
    """Mock analytics service for testing."""
    service = AsyncMock()

    # Mock service methods
    service.create_user_analytics = AsyncMock()
    service.get_user_analytics_aggregated = AsyncMock()
    service.create_vendor_analytics = AsyncMock()
    service.get_vendor_analytics_aggregated = AsyncMock()

    return service


@pytest.fixture
def mock_dashboard_service():
    """Mock dashboard service for testing."""
    service = AsyncMock()

    # Mock service methods
    service.create_dashboard_widget = AsyncMock()
    service.get_dashboard_widgets = AsyncMock()
    service.update_widget_position = AsyncMock()
    service.record_system_metric = AsyncMock()
    service.get_real_time_dashboard_data = AsyncMock()

    return service


@pytest.fixture
def mock_reporting_service():
    """Mock reporting service for testing."""
    service = AsyncMock()

    # Mock service methods
    service.generate_kpi_report = AsyncMock()
    service.generate_conversion_funnel_report = AsyncMock()
    service.generate_performance_dashboard_data = AsyncMock()

    return service


# ============================================================================
# TEST DATA FIXTURES
# ============================================================================

@pytest.fixture
def sample_user_analytics_data():
    """Sample user analytics creation data."""
    return {
        "user_id": str(uuid4()),
        "timeframe": "daily",
        "period_start": "2025-01-14T00:00:00Z",
        "period_end": "2025-01-15T00:00:00Z",
        "page_views": 25,
        "total_session_duration": 2730,  # 45.5 minutes * 60 seconds
        "bookings_created": 2,
        "total_spent": 150.00,
        "device_type": "mobile",
        "location_country": "NG",
        "location_city": "Lagos",
        "custom_metrics": {
            "feature_usage": {"search_filters": 5, "wishlist_adds": 3},
            "user_journey": {"pages_visited": ["home", "search", "booking"]},
            "engagement_details": {"time_on_site": 2700, "bounce_rate": 0.25}
        }
    }


@pytest.fixture
def sample_vendor_analytics_data():
    """Sample vendor analytics creation data."""
    return {
        "vendor_id": str(uuid4()),
        "timeframe": "daily",
        "period_start": "2025-01-14T00:00:00Z",
        "period_end": "2025-01-15T00:00:00Z",
        "profile_views": 150,
        "service_views": 320,
        "booking_requests": 25,
        "bookings_completed": 20,
        "revenue_earned": "2500.00",
        "average_rating": "4.75",
        "response_time_hours": "2.50",
        "cancellation_rate": "5.00",
        "repeat_customer_rate": "35.00",
        "efficiency_score": "85.50",
        "growth_rate": "12.5000",
        "market_share": "8.25",
        "customer_acquisition_cost": "45.00",
        "lifetime_value": "850.00",
        "top_services": {
            "cultural_tours": {"bookings": 12, "revenue": 1200.00},
            "cooking_classes": {"bookings": 8, "revenue": 800.00}
        },
        "performance_metrics": {
            "response_rate": 0.95,
            "quality_score": 4.8,
            "reliability_index": 0.92
        },
        "custom_metrics": {
            "seasonal_performance": {"peak_months": ["Dec", "Jan"]},
            "customer_feedback": {"positive_reviews": 18, "negative_reviews": 2}
        }
    }


@pytest.fixture
def sample_dashboard_widget_data():
    """Sample dashboard widget creation data."""
    return {
        "name": "Revenue Chart",
        "widget_type": "line_chart",
        "data_source": "booking_analytics",
        "configuration": {
            "chart_type": "line",
            "data_points": 30,
            "refresh_rate": 300,
            "filters": {"timeframe": "daily"},
            "display_options": {
                "show_legend": True,
                "show_grid": True,
                "color_scheme": "blue"
            }
        },
        "position_x": 0,
        "position_y": 0,
        "width": 6,
        "height": 4,
        "refresh_interval_seconds": 300
    }


@pytest.fixture
def sample_kpi_definition_data():
    """Sample KPI definition creation data."""
    return {
        "name": "Monthly Revenue Growth",
        "category": "revenue",
        "description": "Month-over-month revenue growth percentage",
        "calculation_method": "percentage_change",
        "data_source": "booking_analytics",
        "target_value": "15.0000",
        "warning_threshold": "10.0000",
        "critical_threshold": "5.0000",
        "unit_of_measurement": "percentage",
        "configuration": {
            "calculation_period": "monthly",
            "comparison_period": "previous_month",
            "aggregation_method": "sum",
            "data_fields": ["total_revenue"],
            "filters": {"status": "completed"}
        }
    }


@pytest.fixture
def performance_test_data():
    """Performance testing data sets."""
    return {
        "bulk_user_analytics": 100,
        "bulk_vendor_analytics": 50,
        "bulk_booking_analytics": 30,
        "bulk_system_metrics": 1000,
        "bulk_dashboard_widgets": 20,
        "bulk_kpi_definitions": 15,
        "concurrent_users": 50,
        "query_timeout_ms": 200,
        "creation_timeout_ms": 500
    }


@pytest.fixture
def analytics_time_series_data():
    """Time-series analytics test data."""
    base_date = datetime.now(timezone.utc) - timedelta(days=30)

    return {
        "user_analytics_series": [
            {
                "date": base_date + timedelta(days=i),
                "page_views": 20 + (i % 10),
                "engagement_score": 70.0 + (i % 20),
                "revenue_generated": 100.0 + (i * 5)
            }
            for i in range(30)
        ],
        "vendor_analytics_series": [
            {
                "date": base_date + timedelta(days=i),
                "profile_views": 100 + (i % 20),
                "bookings_completed": 15 + (i % 5),
                "revenue_earned": 1500.0 + (i * 50)
            }
            for i in range(30)
        ],
        "booking_analytics_series": [
            {
                "date": base_date + timedelta(days=i),
                "total_bookings": 30 + (i % 15),
                "conversion_rate": 7.0 + (i % 3),
                "total_revenue": 3000.0 + (i * 100)
            }
            for i in range(30)
        ]
    }