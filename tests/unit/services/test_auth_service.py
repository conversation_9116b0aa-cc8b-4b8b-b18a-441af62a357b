"""
Unit tests for AuthService class.

This module tests the authentication service functionality including
user registration, login, token management, and integration with BaseService.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.auth_service import AuthService, UserRepository
from app.services.base import ValidationError, ConflictError, ServiceError
from app.models.user import User
from app.schemas.auth import UserCreate, UserLogin, UserResponse
from app.core.security import UserRole


class TestAuthService:
    """Test cases for AuthService class."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        return session
    
    @pytest.fixture
    def auth_service(self, mock_session):
        """Create AuthService instance."""
        return AuthService(db_session=mock_session)
    
    @pytest.fixture
    def user_create_data(self):
        """Create user registration data."""
        return UserCreate(
            email="<EMAIL>",
            password="SecurePassword123!",
            first_name="John",
            last_name="Doe",
            phone_number="+1234567890",
            role=UserRole.CUSTOMER
        )
    
    @pytest.fixture
    def mock_user(self):
        """Create mock user instance."""
        return User(
            id=1,
            email="<EMAIL>",
            hashed_password="$2b$12$hashed_password",
            first_name="John",
            last_name="Doe",
            phone_number="+1234567890",
            role=UserRole.CUSTOMER,
            is_active=True,
            is_verified=False,
            created_at=datetime.utcnow()
        )
    
    @pytest.mark.asyncio
    async def test_auth_service_initialization(self, mock_session):
        """Test AuthService initialization."""
        service = AuthService(db_session=mock_session)
        
        assert service.repository_class == UserRepository
        assert service.model_class == User
        assert service._db_session == mock_session
        assert service.service_name == "AuthService"
        assert service.email_service is not None
    
    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, auth_service):
        """Test successful validation of create data."""
        data = {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "Jane",
            "last_name": "Doe"
        }
        
        with patch.object(auth_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock repository to return no existing user
            with patch('app.services.auth_service.UserRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo.get_by_email.return_value = None
                mock_repo_class.return_value = mock_repo
                
                result = await auth_service.validate_create_data(data)
                
                assert result == data
                mock_repo.get_by_email.assert_called_once_with("<EMAIL>")
    
    @pytest.mark.asyncio
    async def test_validate_create_data_user_exists(self, auth_service, mock_user):
        """Test validation failure when user already exists."""
        data = {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "Jane",
            "last_name": "Doe"
        }
        
        with patch.object(auth_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock repository to return existing user
            with patch('app.services.auth_service.UserRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo.get_by_email.return_value = mock_user
                mock_repo_class.return_value = mock_repo
                
                with pytest.raises(ConflictError) as exc_info:
                    await auth_service.validate_create_data(data)
                
                assert "User with this email already exists" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_validate_create_data_weak_password(self, auth_service):
        """Test validation failure with weak password."""
        data = {
            "email": "<EMAIL>",
            "password": "weak",  # Weak password
            "first_name": "Jane",
            "last_name": "Doe"
        }
        
        with patch.object(auth_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.services.auth_service.UserRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo.get_by_email.return_value = None
                mock_repo_class.return_value = mock_repo
                
                with patch('app.services.auth_service.check_password_strength') as mock_check:
                    mock_check.return_value = {
                        "is_valid": False,
                        "errors": ["Password too short", "Missing special characters"]
                    }
                    
                    with pytest.raises(ValidationError) as exc_info:
                        await auth_service.validate_create_data(data)
                    
                    assert "Password does not meet requirements" in str(exc_info.value)
                    assert exc_info.value.details["field"] == "password"
    
    @pytest.mark.asyncio
    async def test_validate_update_data_success(self, auth_service):
        """Test successful validation of update data."""
        data = {
            "first_name": "Updated",
            "last_name": "Name"
        }
        
        result = await auth_service.validate_update_data(data, 1)
        assert result == data
    
    @pytest.mark.asyncio
    async def test_validate_update_data_email_conflict(self, auth_service, mock_user):
        """Test validation failure when email conflicts with another user."""
        data = {
            "email": "<EMAIL>",
            "first_name": "Updated"
        }
        
        with patch.object(auth_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock repository to return existing user with different ID
            with patch('app.services.auth_service.UserRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                existing_user = User(id=2, email="<EMAIL>")
                mock_repo.get_by_email.return_value = existing_user
                mock_repo_class.return_value = mock_repo
                
                with pytest.raises(ConflictError) as exc_info:
                    await auth_service.validate_update_data(data, 1)
                
                assert "Email already in use by another user" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_register_user_success(self, auth_service, user_create_data, mock_user):
        """Test successful user registration."""
        with patch.object(auth_service, 'create') as mock_create:
            mock_create.return_value = mock_user
            
            with patch.object(auth_service, '_send_verification_email') as mock_send_email:
                mock_send_email.return_value = None
                
                with patch('app.services.auth_service.get_password_hash') as mock_hash:
                    mock_hash.return_value = "hashed_password"
                    
                    result = await auth_service.register_user(user_create_data)
                    
                    assert isinstance(result, UserResponse)
                    mock_create.assert_called_once()
                    mock_send_email.assert_called_once_with(mock_user)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, mock_user):
        """Test successful user authentication."""
        login_data = UserLogin(email="<EMAIL>", password="password123")
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.services.auth_service.verify_password') as mock_verify:
                mock_verify.return_value = True
                
                with patch('app.services.auth_service.create_token_pair') as mock_create_tokens:
                    mock_tokens = {"access_token": "access", "refresh_token": "refresh"}
                    mock_create_tokens.return_value = mock_tokens
                    
                    with patch.object(auth_service, 'db') as mock_db:
                        mock_db.commit = AsyncMock()
                        
                        result = await auth_service.authenticate_user(login_data)
                        
                        assert isinstance(result, tuple)
                        user_response, tokens = result
                        assert isinstance(user_response, UserResponse)
                        assert tokens == mock_tokens
                        mock_verify.assert_called_once()
                        mock_create_tokens.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_email(self, auth_service):
        """Test authentication failure with invalid email."""
        login_data = UserLogin(email="<EMAIL>", password="password123")
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = None
            
            with pytest.raises(Exception):  # Should raise HTTPException
                await auth_service.authenticate_user(login_data)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_password(self, auth_service, mock_user):
        """Test authentication failure with invalid password."""
        login_data = UserLogin(email="<EMAIL>", password="wrongpassword")
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.services.auth_service.verify_password') as mock_verify:
                mock_verify.return_value = False
                
                with patch.object(auth_service, '_log_failed_login') as mock_log:
                    mock_log.return_value = None
                    
                    with pytest.raises(Exception):  # Should raise HTTPException
                        await auth_service.authenticate_user(login_data)
                    
                    mock_log.assert_called_once_with(mock_user.id, login_data.email)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive_account(self, auth_service, mock_user):
        """Test authentication failure with inactive account."""
        login_data = UserLogin(email="<EMAIL>", password="password123")
        mock_user.is_active = False
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.services.auth_service.verify_password') as mock_verify:
                mock_verify.return_value = True
                
                with pytest.raises(Exception):  # Should raise HTTPException
                    await auth_service.authenticate_user(login_data)
    
    def test_get_user_scopes(self, auth_service):
        """Test user scope generation based on role."""
        admin_scopes = auth_service._get_user_scopes(UserRole.ADMIN)
        vendor_scopes = auth_service._get_user_scopes(UserRole.VENDOR)
        customer_scopes = auth_service._get_user_scopes(UserRole.CUSTOMER)
        
        assert "admin" in admin_scopes
        assert "vendor" in admin_scopes
        assert "customer" in admin_scopes
        
        assert "vendor" in vendor_scopes
        assert "admin" not in vendor_scopes
        
        assert "customer" in customer_scopes
        assert "admin" not in customer_scopes
        assert "vendor" not in customer_scopes
    
    def test_check_permission(self, auth_service):
        """Test permission checking."""
        with patch('app.services.auth_service.has_permission') as mock_has_permission:
            mock_has_permission.return_value = True
            
            result = auth_service.check_permission(UserRole.ADMIN, "admin_access")
            
            assert result is True
            mock_has_permission.assert_called_once_with(UserRole.ADMIN, "admin_access")


class TestUserRepository:
    """Test cases for UserRepository class."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def user_repository(self, mock_session):
        """Create UserRepository instance."""
        return UserRepository(User, mock_session)
    
    @pytest.mark.asyncio
    async def test_get_by_email(self, user_repository, mock_session):
        """Test get user by email."""
        mock_user = User(id=1, email="<EMAIL>")
        
        # Mock the database query
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_session.execute.return_value = mock_result
        
        result = await user_repository.get_by_email("<EMAIL>")
        
        assert result == mock_user
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_reset_token(self, user_repository, mock_session):
        """Test get user by reset token."""
        mock_user = User(
            id=1,
            email="<EMAIL>",
            password_reset_token="valid_token",
            password_reset_expires=datetime.utcnow() + timedelta(hours=1)
        )
        
        # Mock the database query
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_session.execute.return_value = mock_result
        
        result = await user_repository.get_by_reset_token("valid_token")
        
        assert result == mock_user
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_reset_token_expired(self, user_repository, mock_session):
        """Test get user by expired reset token."""
        # Mock the database query to return None for expired token
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await user_repository.get_by_reset_token("expired_token")
        
        assert result is None
        mock_session.execute.assert_called_once()
