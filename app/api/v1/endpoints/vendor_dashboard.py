"""
Vendor Dashboard API endpoints for Culture Connect Backend API.

This module provides comprehensive FastAPI endpoints for vendor dashboard functionality including:
- Dashboard overview with key metrics and insights
- Performance analytics with trends and forecasting
- Activity feed management with real-time updates
- Notification center with priority-based alerts
- Quick actions for workflow optimization

Implements Task 3.3.1 requirements with FastAPI best practices, authentication/RBAC integration,
rate limiting, comprehensive error handling, and seamless integration with existing systems.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.deps import get_db, get_current_user, get_current_vendor_user
# Rate limiting will be implemented later
# from app.core.rate_limiting import rate_limit
from app.core.security import UserRole
from app.models.user import User
from app.schemas.vendor_dashboard import (
    VendorDashboardOverview, VendorDashboardMetricsResponse, VendorDashboardMetricsCreate,
    VendorDashboardMetricsUpdate, VendorActivityFeedResponse, VendorActivityFeedCreate,
    VendorActivityFeedUpdate, VendorNotificationResponse, VendorNotificationCreate,
    VendorNotificationUpdate, VendorQuickActionResponse, VendorQuickActionCreate,
    VendorQuickActionUpdate, VendorAnalyticsResponse, VendorAnalyticsCreate
)
from app.services.vendor_dashboard_services import (
    VendorDashboardService, VendorAnalyticsService, VendorActivityService,
    VendorNotificationService
)
from app.repositories.vendor_dashboard_repositories import (
    VendorDashboardRepository, VendorMetricsRepository, VendorActivityRepository,
    VendorNotificationRepository, VendorQuickActionRepository, VendorAnalyticsRepository
)
from app.models.vendor_dashboard import (
    MetricType, ActivityType, NotificationType, NotificationPriority, ActionType
)

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


# Dependency injection for services
async def get_vendor_dashboard_service(db: AsyncSession = Depends(get_db)) -> VendorDashboardService:
    """Get vendor dashboard service with all dependencies."""
    dashboard_repo = VendorDashboardRepository(db)
    metrics_repo = VendorMetricsRepository(db)
    activity_repo = VendorActivityRepository(db)
    notification_repo = VendorNotificationRepository(db)
    quick_action_repo = VendorQuickActionRepository(db)
    analytics_repo = VendorAnalyticsRepository(db)

    return VendorDashboardService(
        dashboard_repository=dashboard_repo,
        metrics_repository=metrics_repo,
        activity_repository=activity_repo,
        notification_repository=notification_repo,
        quick_action_repository=quick_action_repo,
        analytics_repository=analytics_repo
    )


async def get_vendor_analytics_service(db: AsyncSession = Depends(get_db)) -> VendorAnalyticsService:
    """Get vendor analytics service."""
    analytics_repo = VendorAnalyticsRepository(db)
    return VendorAnalyticsService(analytics_repo)


async def get_vendor_activity_service(db: AsyncSession = Depends(get_db)) -> VendorActivityService:
    """Get vendor activity service."""
    activity_repo = VendorActivityRepository(db)
    return VendorActivityService(activity_repo)


async def get_vendor_notification_service(db: AsyncSession = Depends(get_db)) -> VendorNotificationService:
    """Get vendor notification service."""
    notification_repo = VendorNotificationRepository(db)
    return VendorNotificationService(notification_repo)


# Dashboard Overview Endpoints
@router.get(
    "/overview",
    response_model=VendorDashboardOverview,
    summary="Get vendor dashboard overview",
    description="Retrieve comprehensive vendor dashboard overview with key metrics, activities, and notifications",
    # Rate limiting will be added later
)
async def get_vendor_dashboard_overview(
    current_user: User = Depends(get_current_vendor_user),
    dashboard_service: VendorDashboardService = Depends(get_vendor_dashboard_service)
) -> VendorDashboardOverview:
    """
    Get comprehensive vendor dashboard overview.

    Returns:
    - Key performance metrics
    - Recent activity feed
    - Unread and urgent notifications
    - Recommended quick actions
    - Current and previous analytics
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"dashboard_overview_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Getting dashboard overview for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            }
        )

        dashboard_overview = await dashboard_service.get_vendor_dashboard_overview(
            vendor_id=vendor_id,
            correlation_id=correlation_id
        )

        logger.info(
            f"Successfully retrieved dashboard overview for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "metrics_count": len(dashboard_overview.key_metrics),
                "activities_count": len(dashboard_overview.recent_activities)
            }
        )

        return dashboard_overview

    except Exception as e:
        logger.error(
            f"Error getting dashboard overview for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard overview"
        )


@router.post(
    "/refresh-metrics",
    response_model=Dict[str, Any],
    summary="Refresh vendor metrics",
    description="Refresh vendor performance metrics from all data sources",
    # Rate limiting will be added later
)
async def refresh_vendor_metrics(
    current_user: User = Depends(get_current_vendor_user),
    dashboard_service: VendorDashboardService = Depends(get_vendor_dashboard_service)
) -> Dict[str, Any]:
    """
    Refresh vendor metrics from various data sources.

    This endpoint triggers a comprehensive refresh of vendor metrics including:
    - Revenue and booking data
    - Service performance metrics
    - Quality scores and ratings
    - Marketplace ranking data
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"refresh_metrics_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Refreshing metrics for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            }
        )

        refresh_summary = await dashboard_service.refresh_vendor_metrics(
            vendor_id=vendor_id,
            correlation_id=correlation_id
        )

        logger.info(
            f"Successfully refreshed metrics for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "metrics_refreshed": refresh_summary.get("metrics_refreshed", 0)
            }
        )

        return refresh_summary

    except Exception as e:
        logger.error(
            f"Error refreshing metrics for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh vendor metrics"
        )


@router.get(
    "/insights",
    response_model=Dict[str, Any],
    summary="Get performance insights",
    description="Generate performance insights and recommendations based on vendor data",
    # Rate limiting will be added later
)
async def get_performance_insights(
    current_user: User = Depends(get_current_vendor_user),
    dashboard_service: VendorDashboardService = Depends(get_vendor_dashboard_service)
) -> Dict[str, Any]:
    """
    Generate performance insights and recommendations.

    Returns:
    - Performance analysis and trends
    - Competitive insights
    - Actionable recommendations
    - Market position analysis
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"performance_insights_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Generating performance insights for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            }
        )

        insights = await dashboard_service.generate_performance_insights(
            vendor_id=vendor_id,
            correlation_id=correlation_id
        )

        logger.info(
            f"Successfully generated performance insights for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "insights_count": len(insights.get("insights", [])),
                "recommendations_count": len(insights.get("recommendations", []))
            }
        )

        return insights

    except Exception as e:
        logger.error(
            f"Error generating performance insights for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate performance insights"
        )


# Analytics Endpoints
@router.get(
    "/analytics",
    response_model=VendorAnalyticsResponse,
    summary="Get vendor analytics",
    description="Retrieve comprehensive vendor analytics for specified period",
    # Rate limiting will be added later
)
async def get_vendor_analytics(
    analytics_type: str = Query(default="monthly", description="Analytics type (daily, weekly, monthly, quarterly)"),
    current_user: User = Depends(get_current_vendor_user),
    analytics_service: VendorAnalyticsService = Depends(get_vendor_analytics_service)
) -> VendorAnalyticsResponse:
    """
    Get comprehensive vendor analytics.

    Args:
        analytics_type: Type of analytics (daily, weekly, monthly, quarterly)

    Returns:
        Comprehensive analytics including revenue, bookings, performance metrics
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"analytics_{analytics_type}_{vendor_id}_{datetime.utcnow().timestamp()}"

        # Validate analytics type
        valid_types = {"daily", "weekly", "monthly", "quarterly"}
        if analytics_type not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid analytics type. Must be one of: {', '.join(valid_types)}"
            )

        logger.info(
            f"Getting {analytics_type} analytics for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "analytics_type": analytics_type,
                "user_id": current_user.id
            }
        )

        analytics = await analytics_service.generate_vendor_analytics(
            vendor_id=vendor_id,
            analytics_type=analytics_type,
            correlation_id=correlation_id
        )

        logger.info(
            f"Successfully retrieved {analytics_type} analytics for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "analytics_type": analytics_type,
                "total_revenue": analytics.total_revenue,
                "total_bookings": analytics.total_bookings
            }
        )

        return analytics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting analytics for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "analytics_type": analytics_type,
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve vendor analytics"
        )


@router.get(
    "/analytics/trends",
    response_model=Dict[str, Any],
    summary="Get performance trends",
    description="Calculate performance trends over specified time period",
    # Rate limiting will be added later
)
async def get_performance_trends(
    days: int = Query(default=90, ge=7, le=365, description="Number of days for trend analysis"),
    current_user: User = Depends(get_current_vendor_user),

    analytics_service: VendorAnalyticsService = Depends(get_vendor_analytics_service)
) -> Dict[str, Any]:
    """
    Calculate performance trends over time.

    Args:
        days: Number of days for trend analysis (7-365)

    Returns:
        Performance trends including growth rates, forecasts, and patterns
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"trends_{days}d_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Calculating performance trends for vendor {vendor_id} over {days} days",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "days": days,
                "user_id": current_user.id
            }
        )

        trends = await analytics_service.calculate_performance_trends(
            vendor_id=vendor_id,
            days=days,
            correlation_id=correlation_id
        )

        logger.info(
            f"Successfully calculated performance trends for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "days": days,
                "revenue_growth": trends.get("revenue_trend", {}).get("growth_rate", 0)
            }
        )

        return trends

    except Exception as e:
        logger.error(
            f"Error calculating performance trends for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "days": days,
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate performance trends"
        )


# Activity Feed Endpoints
@router.get(
    "/activities",
    response_model=List[VendorActivityFeedResponse],
    summary="Get vendor activities",
    description="Retrieve vendor activity feed with optional filtering",
    # Rate limiting will be added later
)
async def get_vendor_activities(
    limit: int = Query(default=20, ge=1, le=100, description="Number of activities to retrieve"),
    activity_types: Optional[List[ActivityType]] = Query(default=None, description="Filter by activity types"),
    unread_only: bool = Query(default=False, description="Show only unread activities"),
    current_user: User = Depends(get_current_vendor_user),

    activity_service: VendorActivityService = Depends(get_vendor_activity_service)
) -> List[VendorActivityFeedResponse]:
    """
    Get vendor activity feed.

    Args:
        limit: Number of activities to retrieve (1-100)
        activity_types: Optional filter by activity types
        unread_only: Show only unread activities

    Returns:
        List of vendor activities with metadata
    """
    try:
        vendor_id = current_user.vendor_profile.id

        logger.info(
            f"Getting activities for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "limit": limit,
                "unread_only": unread_only,
                "user_id": current_user.id
            }
        )

        if unread_only:
            activities = await activity_service.activity_repository.get_unread_activities(
                vendor_id=vendor_id,
                limit=limit
            )
        else:
            activities = await activity_service.activity_repository.get_recent_activities(
                vendor_id=vendor_id,
                limit=limit,
                activity_types=activity_types
            )

        activity_responses = [
            VendorActivityFeedResponse.model_validate(activity)
            for activity in activities
        ]

        logger.info(
            f"Successfully retrieved {len(activity_responses)} activities for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "activities_count": len(activity_responses),
                "unread_only": unread_only
            }
        )

        return activity_responses

    except Exception as e:
        logger.error(
            f"Error getting activities for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve vendor activities"
        )


@router.post(
    "/activities",
    response_model=VendorActivityFeedResponse,
    summary="Log vendor activity",
    description="Log a new vendor activity",
    # Rate limiting will be added later
)
async def log_vendor_activity(
    activity_data: VendorActivityFeedCreate,
    current_user: User = Depends(get_current_vendor_user),

    activity_service: VendorActivityService = Depends(get_vendor_activity_service)
) -> VendorActivityFeedResponse:
    """
    Log a new vendor activity.

    Args:
        activity_data: Activity data to log

    Returns:
        Created activity with metadata
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"log_activity_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Logging activity {activity_data.activity_type} for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "activity_type": activity_data.activity_type,
                "user_id": current_user.id
            }
        )

        # Extract activity data
        activity_dict = activity_data.model_dump(exclude={"vendor_id"})

        activity_response = await activity_service.log_vendor_activity(
            vendor_id=vendor_id,
            correlation_id=correlation_id,
            **activity_dict
        )

        logger.info(
            f"Successfully logged activity for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "activity_id": str(activity_response.id),
                "activity_type": activity_data.activity_type
            }
        )

        return activity_response

    except Exception as e:
        logger.error(
            f"Error logging activity for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "activity_type": activity_data.activity_type,
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to log vendor activity"
        )


@router.patch(
    "/activities/{activity_id}",
    response_model=VendorActivityFeedResponse,
    summary="Update activity status",
    description="Update activity read status or mark action as completed",
    # Rate limiting will be added later
)
async def update_activity_status(
    activity_id: UUID = Path(..., description="Activity ID"),
    update_data: VendorActivityFeedUpdate = ...,
    current_user: User = Depends(get_current_vendor_user),

    activity_service: VendorActivityService = Depends(get_vendor_activity_service)
) -> VendorActivityFeedResponse:
    """
    Update activity status.

    Args:
        activity_id: Activity ID to update
        update_data: Update data

    Returns:
        Updated activity
    """
    try:
        vendor_id = current_user.vendor_profile.id

        logger.info(
            f"Updating activity {activity_id} for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "activity_id": str(activity_id),
                "user_id": current_user.id
            }
        )

        # Get the activity first to verify ownership
        activity = await activity_service.activity_repository.get(activity_id)
        if not activity or activity.vendor_id != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Activity not found"
            )

        # Update the activity
        update_dict = update_data.model_dump(exclude_unset=True)
        updated_activity = await activity_service.activity_repository.update(
            activity_id, update_dict
        )

        activity_response = VendorActivityFeedResponse.model_validate(updated_activity)

        logger.info(
            f"Successfully updated activity {activity_id} for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "activity_id": str(activity_id),
                "updates": list(update_dict.keys())
            }
        )

        return activity_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error updating activity {activity_id} for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "activity_id": str(activity_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update activity"
        )


# Notification Endpoints
@router.get(
    "/notifications",
    response_model=List[VendorNotificationResponse],
    summary="Get vendor notifications",
    description="Retrieve vendor notifications with optional filtering",
    # Rate limiting will be added later
)
async def get_vendor_notifications(
    limit: int = Query(default=50, ge=1, le=100, description="Number of notifications to retrieve"),
    unread_only: bool = Query(default=False, description="Show only unread notifications"),
    priority: Optional[NotificationPriority] = Query(default=None, description="Filter by priority"),
    current_user: User = Depends(get_current_vendor_user),

    notification_service: VendorNotificationService = Depends(get_vendor_notification_service)
) -> List[VendorNotificationResponse]:
    """
    Get vendor notifications.

    Args:
        limit: Number of notifications to retrieve (1-100)
        unread_only: Show only unread notifications
        priority: Filter by notification priority

    Returns:
        List of vendor notifications
    """
    try:
        vendor_id = current_user.vendor_profile.id

        logger.info(
            f"Getting notifications for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "limit": limit,
                "unread_only": unread_only,
                "priority": priority,
                "user_id": current_user.id
            }
        )

        if priority:
            notifications = await notification_service.notification_repository.get_notifications_by_priority(
                vendor_id=vendor_id,
                priority=priority
            )
            notifications = notifications[:limit]  # Apply limit
        elif unread_only:
            notifications = await notification_service.notification_repository.get_unread_notifications(
                vendor_id=vendor_id,
                limit=limit
            )
        else:
            # Get all notifications (would need a new repository method)
            notifications = await notification_service.notification_repository.get_unread_notifications(
                vendor_id=vendor_id,
                limit=limit
            )

        notification_responses = [
            VendorNotificationResponse.model_validate(notification)
            for notification in notifications
        ]

        logger.info(
            f"Successfully retrieved {len(notification_responses)} notifications for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "notifications_count": len(notification_responses),
                "unread_only": unread_only,
                "priority": priority
            }
        )

        return notification_responses

    except Exception as e:
        logger.error(
            f"Error getting notifications for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve vendor notifications"
        )


@router.post(
    "/notifications",
    response_model=VendorNotificationResponse,
    summary="Create vendor notification",
    description="Create a new vendor notification",
    # Rate limiting will be added later
)
async def create_vendor_notification(
    notification_data: VendorNotificationCreate,
    current_user: User = Depends(get_current_vendor_user),

    notification_service: VendorNotificationService = Depends(get_vendor_notification_service)
) -> VendorNotificationResponse:
    """
    Create a new vendor notification.

    Args:
        notification_data: Notification data

    Returns:
        Created notification
    """
    try:
        vendor_id = current_user.vendor_profile.id
        correlation_id = f"create_notification_{vendor_id}_{datetime.utcnow().timestamp()}"

        logger.info(
            f"Creating notification {notification_data.notification_type} for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "notification_type": notification_data.notification_type,
                "user_id": current_user.id
            }
        )

        # Extract notification data
        notification_dict = notification_data.model_dump(exclude={"vendor_id"})

        notification_response = await notification_service.create_vendor_notification(
            vendor_id=vendor_id,
            correlation_id=correlation_id,
            **notification_dict
        )

        logger.info(
            f"Successfully created notification for vendor {vendor_id}",
            extra={
                "correlation_id": correlation_id,
                "vendor_id": str(vendor_id),
                "notification_id": str(notification_response.id),
                "notification_type": notification_data.notification_type
            }
        )

        return notification_response

    except Exception as e:
        logger.error(
            f"Error creating notification for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "notification_type": notification_data.notification_type,
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create vendor notification"
        )


@router.patch(
    "/notifications/{notification_id}",
    response_model=VendorNotificationResponse,
    summary="Update notification status",
    description="Update notification read status or dismiss notification",
    # Rate limiting will be added later
)
async def update_notification_status(
    notification_id: UUID = Path(..., description="Notification ID"),
    update_data: VendorNotificationUpdate = ...,
    current_user: User = Depends(get_current_vendor_user),

    notification_service: VendorNotificationService = Depends(get_vendor_notification_service)
) -> VendorNotificationResponse:
    """
    Update notification status.

    Args:
        notification_id: Notification ID to update
        update_data: Update data

    Returns:
        Updated notification
    """
    try:
        vendor_id = current_user.vendor_profile.id

        logger.info(
            f"Updating notification {notification_id} for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "notification_id": str(notification_id),
                "user_id": current_user.id
            }
        )

        # Get the notification first to verify ownership
        notification = await notification_service.notification_repository.get(notification_id)
        if not notification or notification.vendor_id != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        # Handle specific update actions
        update_dict = update_data.model_dump(exclude_unset=True)

        if update_dict.get("is_read"):
            await notification_service.notification_repository.mark_notification_as_read(
                notification_id, vendor_id
            )

        if update_dict.get("is_dismissed"):
            await notification_service.notification_repository.dismiss_notification(
                notification_id, vendor_id
            )

        # Get updated notification
        updated_notification = await notification_service.notification_repository.get(notification_id)
        notification_response = VendorNotificationResponse.model_validate(updated_notification)

        logger.info(
            f"Successfully updated notification {notification_id} for vendor {vendor_id}",
            extra={
                "vendor_id": str(vendor_id),
                "notification_id": str(notification_id),
                "updates": list(update_dict.keys())
            }
        )

        return notification_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error updating notification {notification_id} for vendor {vendor_id}: {str(e)}",
            extra={
                "vendor_id": str(vendor_id),
                "notification_id": str(notification_id),
                "user_id": current_user.id
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification"
        )
