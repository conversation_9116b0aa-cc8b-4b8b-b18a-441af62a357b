"""
Document verification schemas for Culture Connect Backend API.

This module defines Pydantic schemas for document verification workflows,
automated validation, manual review processes, and verification audit trails.

Implements Task 3.1.2: Document Verification System schemas with request/response
validation, workflow status transitions, and automated validation rule schemas.
"""

from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, ConfigDict, field_validator
from enum import Enum

from app.models.verification import (
    VerificationWorkflowStatus, VerificationPriority, AutomatedValidationStatus,
    VerificationActionType
)
from app.models.vendor import DocumentType, DocumentStatus


class VerificationWorkflowStatusEnum(str, Enum):
    """Verification workflow status enumeration for schemas."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    RESUBMISSION_REQUIRED = "resubmission_required"
    ESCALATED = "escalated"
    COMPLETED = "completed"


class VerificationPriorityEnum(str, Enum):
    """Verification priority enumeration for schemas."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class AutomatedValidationStatusEnum(str, Enum):
    """Automated validation status enumeration for schemas."""
    PENDING = "pending"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"
    SKIPPED = "skipped"


class VerificationActionTypeEnum(str, Enum):
    """Verification action type enumeration for schemas."""
    SUBMITTED = "submitted"
    ASSIGNED = "assigned"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"
    RESUBMISSION_REQUESTED = "resubmission_requested"
    ESCALATED = "escalated"
    COMMENT_ADDED = "comment_added"
    STATUS_CHANGED = "status_changed"


# Base schemas
class VerificationBaseSchema(BaseModel):
    """Base schema for verification-related models."""
    model_config = ConfigDict(from_attributes=True, use_enum_values=True)


# Document Verification Workflow Schemas
class DocumentVerificationWorkflowCreate(VerificationBaseSchema):
    """Schema for creating a document verification workflow."""
    vendor_id: int = Field(..., description="Associated vendor ID")
    document_id: int = Field(..., description="Associated document ID")
    priority: VerificationPriorityEnum = Field(
        default=VerificationPriorityEnum.NORMAL,
        description="Verification priority level"
    )
    requires_manual_review: bool = Field(
        default=True,
        description="Whether manual review is required"
    )
    is_expedited: bool = Field(
        default=False,
        description="Whether this is an expedited workflow"
    )
    workflow_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional workflow metadata"
    )

    @field_validator('vendor_id', 'document_id')
    @classmethod
    def validate_positive_ids(cls, v):
        """Validate that IDs are positive integers."""
        if v <= 0:
            raise ValueError("ID must be a positive integer")
        return v


class DocumentVerificationWorkflowUpdate(VerificationBaseSchema):
    """Schema for updating a document verification workflow."""
    status: Optional[VerificationWorkflowStatusEnum] = Field(
        default=None,
        description="Updated workflow status"
    )
    priority: Optional[VerificationPriorityEnum] = Field(
        default=None,
        description="Updated priority level"
    )
    assigned_to: Optional[int] = Field(
        default=None,
        description="ID of admin assigned to review"
    )
    review_notes: Optional[str] = Field(
        default=None,
        max_length=2000,
        description="Review notes and feedback"
    )
    rejection_reason: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Reason for rejection if applicable"
    )
    escalation_reason: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Reason for escalation if applicable"
    )
    workflow_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated workflow metadata"
    )


class DocumentVerificationWorkflowResponse(VerificationBaseSchema):
    """Schema for document verification workflow response."""
    id: int = Field(..., description="Workflow ID")
    vendor_id: int = Field(..., description="Associated vendor ID")
    document_id: int = Field(..., description="Associated document ID")
    workflow_reference: str = Field(..., description="Unique workflow reference number")
    status: VerificationWorkflowStatusEnum = Field(..., description="Current workflow status")
    priority: VerificationPriorityEnum = Field(..., description="Verification priority level")

    # Assignment and review
    assigned_to: Optional[int] = Field(default=None, description="ID of assigned admin")
    assigned_at: Optional[datetime] = Field(default=None, description="Assignment timestamp")
    reviewed_by: Optional[int] = Field(default=None, description="ID of reviewing admin")
    reviewed_at: Optional[datetime] = Field(default=None, description="Review completion timestamp")

    # Timing
    submitted_at: datetime = Field(..., description="Submission timestamp")
    due_date: Optional[datetime] = Field(default=None, description="Expected completion date")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")

    # Review details
    review_notes: Optional[str] = Field(default=None, description="Review notes and feedback")
    rejection_reason: Optional[str] = Field(default=None, description="Rejection reason")
    escalation_reason: Optional[str] = Field(default=None, description="Escalation reason")

    # Automated validation
    automated_validation_status: AutomatedValidationStatusEnum = Field(
        ..., description="Automated validation status"
    )
    automated_validation_score: Optional[Decimal] = Field(
        default=None, description="Validation confidence score"
    )
    automated_validation_results: Optional[Dict[str, Any]] = Field(
        default=None, description="Detailed validation results"
    )

    # Metadata
    workflow_data: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional workflow metadata"
    )
    requires_manual_review: bool = Field(..., description="Whether manual review is required")
    is_expedited: bool = Field(..., description="Whether this is expedited")

    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Computed properties
    @property
    def is_overdue(self) -> bool:
        """Check if workflow is overdue."""
        if not self.due_date or self.completed_at:
            return False
        return datetime.now(timezone.utc) > self.due_date.replace(tzinfo=None)

    @property
    def processing_time_hours(self) -> Optional[float]:
        """Calculate processing time in hours."""
        if not self.completed_at:
            return None

        start_time = self.submitted_at.replace(tzinfo=None)
        end_time = self.completed_at.replace(tzinfo=None)
        delta = end_time - start_time
        return delta.total_seconds() / 3600.0


# Verification History Schemas
class VerificationHistoryCreate(VerificationBaseSchema):
    """Schema for creating verification history entry."""
    workflow_id: int = Field(..., description="Associated workflow ID")
    action_type: VerificationActionTypeEnum = Field(..., description="Type of action performed")
    performed_by: Optional[int] = Field(default=None, description="ID of user performing action")
    action_description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Description of the action"
    )
    previous_status: Optional[str] = Field(
        default=None,
        max_length=50,
        description="Previous status before action"
    )
    new_status: Optional[str] = Field(
        default=None,
        max_length=50,
        description="New status after action"
    )
    action_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional action data"
    )
    ip_address: Optional[str] = Field(
        default=None,
        max_length=45,
        description="IP address of user"
    )
    user_agent: Optional[str] = Field(
        default=None,
        max_length=500,
        description="User agent string"
    )

    @field_validator('workflow_id')
    @classmethod
    def validate_workflow_id(cls, v):
        """Validate workflow ID."""
        if v <= 0:
            raise ValueError("Workflow ID must be a positive integer")
        return v


class VerificationHistoryResponse(VerificationBaseSchema):
    """Schema for verification history response."""
    id: int = Field(..., description="History entry ID")
    workflow_id: int = Field(..., description="Associated workflow ID")
    action_type: VerificationActionTypeEnum = Field(..., description="Type of action performed")
    performed_by: Optional[int] = Field(default=None, description="ID of user performing action")
    performed_at: datetime = Field(..., description="Action timestamp")
    action_description: Optional[str] = Field(default=None, description="Action description")
    previous_status: Optional[str] = Field(default=None, description="Previous status")
    new_status: Optional[str] = Field(default=None, description="New status")
    action_data: Optional[Dict[str, Any]] = Field(default=None, description="Action data")
    ip_address: Optional[str] = Field(default=None, description="User IP address")
    user_agent: Optional[str] = Field(default=None, description="User agent")
    created_at: datetime = Field(..., description="Creation timestamp")


# Automated Validation Rule Schemas
class AutomatedValidationRuleCreate(VerificationBaseSchema):
    """Schema for creating automated validation rule."""
    rule_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Unique rule name"
    )
    rule_description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Rule description"
    )
    document_types: List[str] = Field(
        ...,
        min_items=1,
        description="Document types this rule applies to"
    )
    vendor_types: Optional[List[str]] = Field(
        default=None,
        description="Vendor types this rule applies to"
    )
    validation_type: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Type of validation"
    )
    rule_config: Dict[str, Any] = Field(
        ...,
        description="Rule configuration and parameters"
    )
    priority: int = Field(
        default=100,
        ge=1,
        le=1000,
        description="Rule execution priority"
    )
    is_active: bool = Field(
        default=True,
        description="Whether rule is active"
    )

    @field_validator('rule_name')
    @classmethod
    def validate_rule_name(cls, v):
        """Validate rule name format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError("Rule name must contain only alphanumeric characters, hyphens, and underscores")
        return v

    @field_validator('document_types')
    @classmethod
    def validate_document_types(cls, v):
        """Validate document types."""
        valid_types = [dt.value for dt in DocumentType]
        for doc_type in v:
            if doc_type not in valid_types:
                raise ValueError(f"Invalid document type: {doc_type}")
        return v


class AutomatedValidationRuleUpdate(VerificationBaseSchema):
    """Schema for updating automated validation rule."""
    rule_description: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Updated rule description"
    )
    document_types: Optional[List[str]] = Field(
        default=None,
        min_items=1,
        description="Updated document types"
    )
    vendor_types: Optional[List[str]] = Field(
        default=None,
        description="Updated vendor types"
    )
    rule_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated rule configuration"
    )
    priority: Optional[int] = Field(
        default=None,
        ge=1,
        le=1000,
        description="Updated priority"
    )
    is_active: Optional[bool] = Field(
        default=None,
        description="Updated active status"
    )

    @field_validator('document_types')
    @classmethod
    def validate_document_types(cls, v):
        """Validate document types."""
        if v is not None:
            valid_types = [dt.value for dt in DocumentType]
            for doc_type in v:
                if doc_type not in valid_types:
                    raise ValueError(f"Invalid document type: {doc_type}")
        return v


class AutomatedValidationRuleResponse(VerificationBaseSchema):
    """Schema for automated validation rule response."""
    id: int = Field(..., description="Rule ID")
    rule_name: str = Field(..., description="Rule name")
    rule_description: Optional[str] = Field(default=None, description="Rule description")
    document_types: List[str] = Field(..., description="Applicable document types")
    vendor_types: Optional[List[str]] = Field(default=None, description="Applicable vendor types")
    validation_type: str = Field(..., description="Type of validation")
    rule_config: Dict[str, Any] = Field(..., description="Rule configuration")
    is_active: bool = Field(..., description="Whether rule is active")
    priority: int = Field(..., description="Rule execution priority")
    success_rate: Decimal = Field(..., description="Rule success rate percentage")
    total_executions: int = Field(..., description="Total executions")
    successful_executions: int = Field(..., description="Successful executions")
    created_by: Optional[int] = Field(default=None, description="Creator user ID")
    last_executed_at: Optional[datetime] = Field(default=None, description="Last execution timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    @property
    def calculated_success_rate(self) -> float:
        """Calculate current success rate."""
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100.0


# Workflow Management Schemas
class WorkflowAssignmentRequest(VerificationBaseSchema):
    """Schema for assigning workflow to admin."""
    assigned_to: int = Field(..., description="ID of admin to assign workflow to")
    priority: Optional[VerificationPriorityEnum] = Field(
        default=None,
        description="Updated priority level"
    )
    notes: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Assignment notes"
    )

    @field_validator('assigned_to')
    @classmethod
    def validate_assigned_to(cls, v):
        """Validate assigned admin ID."""
        if v <= 0:
            raise ValueError("Admin ID must be a positive integer")
        return v


class WorkflowReviewRequest(VerificationBaseSchema):
    """Schema for reviewing workflow."""
    status: VerificationWorkflowStatusEnum = Field(
        ...,
        description="Review decision status"
    )
    review_notes: str = Field(
        ...,
        min_length=1,
        max_length=2000,
        description="Review notes and feedback"
    )
    rejection_reason: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Reason for rejection"
    )
    escalation_reason: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Reason for escalation"
    )
    requires_resubmission: bool = Field(
        default=False,
        description="Whether resubmission is required"
    )

    @field_validator('status')
    @classmethod
    def validate_review_status(cls, v):
        """Validate review status."""
        valid_statuses = [
            VerificationWorkflowStatusEnum.APPROVED,
            VerificationWorkflowStatusEnum.REJECTED,
            VerificationWorkflowStatusEnum.RESUBMISSION_REQUIRED,
            VerificationWorkflowStatusEnum.ESCALATED
        ]
        if v not in valid_statuses:
            raise ValueError(f"Invalid review status: {v}")
        return v


class WorkflowStatusUpdate(VerificationBaseSchema):
    """Schema for updating workflow status."""
    status: VerificationWorkflowStatusEnum = Field(
        ...,
        description="New workflow status"
    )
    notes: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Status update notes"
    )


# Automated Validation Schemas
class AutomatedValidationRequest(VerificationBaseSchema):
    """Schema for automated validation request."""
    document_id: int = Field(..., description="Document ID to validate")
    validation_types: Optional[List[str]] = Field(
        default=None,
        description="Specific validation types to run"
    )
    force_revalidation: bool = Field(
        default=False,
        description="Force revalidation even if already validated"
    )

    @field_validator('document_id')
    @classmethod
    def validate_document_id(cls, v):
        """Validate document ID."""
        if v <= 0:
            raise ValueError("Document ID must be a positive integer")
        return v


class AutomatedValidationResult(VerificationBaseSchema):
    """Schema for automated validation result."""
    rule_id: int = Field(..., description="Validation rule ID")
    rule_name: str = Field(..., description="Validation rule name")
    validation_type: str = Field(..., description="Type of validation")
    status: AutomatedValidationStatusEnum = Field(..., description="Validation status")
    confidence_score: Optional[Decimal] = Field(
        default=None,
        description="Confidence score (0.00-100.00)"
    )
    result_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Detailed validation results"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if validation failed"
    )
    execution_time_ms: Optional[int] = Field(
        default=None,
        description="Execution time in milliseconds"
    )


class AutomatedValidationResponse(VerificationBaseSchema):
    """Schema for automated validation response."""
    document_id: int = Field(..., description="Validated document ID")
    overall_status: AutomatedValidationStatusEnum = Field(
        ..., description="Overall validation status"
    )
    overall_score: Optional[Decimal] = Field(
        default=None,
        description="Overall confidence score"
    )
    validation_results: List[AutomatedValidationResult] = Field(
        ..., description="Individual validation results"
    )
    total_rules_executed: int = Field(..., description="Total rules executed")
    successful_validations: int = Field(..., description="Successful validations")
    failed_validations: int = Field(..., description="Failed validations")
    validation_timestamp: datetime = Field(..., description="Validation timestamp")
    processing_time_ms: int = Field(..., description="Total processing time")

    @property
    def success_rate(self) -> float:
        """Calculate validation success rate."""
        if self.total_rules_executed == 0:
            return 0.0
        return (self.successful_validations / self.total_rules_executed) * 100.0


# List and Filter Schemas
class WorkflowListFilters(VerificationBaseSchema):
    """Schema for workflow list filters."""
    status: Optional[List[VerificationWorkflowStatusEnum]] = Field(
        default=None,
        description="Filter by workflow statuses"
    )
    priority: Optional[List[VerificationPriorityEnum]] = Field(
        default=None,
        description="Filter by priority levels"
    )
    assigned_to: Optional[int] = Field(
        default=None,
        description="Filter by assigned admin"
    )
    vendor_id: Optional[int] = Field(
        default=None,
        description="Filter by vendor ID"
    )
    document_type: Optional[List[str]] = Field(
        default=None,
        description="Filter by document types"
    )
    is_overdue: Optional[bool] = Field(
        default=None,
        description="Filter by overdue status"
    )
    requires_manual_review: Optional[bool] = Field(
        default=None,
        description="Filter by manual review requirement"
    )
    is_expedited: Optional[bool] = Field(
        default=None,
        description="Filter by expedited status"
    )
    date_from: Optional[datetime] = Field(
        default=None,
        description="Filter by submission date from"
    )
    date_to: Optional[datetime] = Field(
        default=None,
        description="Filter by submission date to"
    )


class WorkflowListResponse(VerificationBaseSchema):
    """Schema for workflow list response."""
    workflows: List[DocumentVerificationWorkflowResponse] = Field(
        ..., description="List of workflows"
    )
    total_count: int = Field(..., description="Total number of workflows")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")


# Statistics and Analytics Schemas
class VerificationStatistics(VerificationBaseSchema):
    """Schema for verification statistics."""
    total_workflows: int = Field(..., description="Total number of workflows")
    pending_workflows: int = Field(..., description="Pending workflows")
    in_progress_workflows: int = Field(..., description="In progress workflows")
    completed_workflows: int = Field(..., description="Completed workflows")
    approved_workflows: int = Field(..., description="Approved workflows")
    rejected_workflows: int = Field(..., description="Rejected workflows")
    overdue_workflows: int = Field(..., description="Overdue workflows")
    average_processing_time_hours: Optional[Decimal] = Field(
        default=None, description="Average processing time in hours"
    )
    approval_rate: Decimal = Field(..., description="Approval rate percentage")
    automation_rate: Decimal = Field(..., description="Automation success rate")
    manual_review_rate: Decimal = Field(..., description="Manual review rate")


# Export all schemas
__all__ = [
    # Enums
    "VerificationWorkflowStatusEnum",
    "VerificationPriorityEnum",
    "AutomatedValidationStatusEnum",
    "VerificationActionTypeEnum",

    # Workflow schemas
    "DocumentVerificationWorkflowCreate",
    "DocumentVerificationWorkflowUpdate",
    "DocumentVerificationWorkflowResponse",

    # History schemas
    "VerificationHistoryCreate",
    "VerificationHistoryResponse",

    # Rule schemas
    "AutomatedValidationRuleCreate",
    "AutomatedValidationRuleUpdate",
    "AutomatedValidationRuleResponse",

    # Management schemas
    "WorkflowAssignmentRequest",
    "WorkflowReviewRequest",
    "WorkflowStatusUpdate",

    # Validation schemas
    "AutomatedValidationRequest",
    "AutomatedValidationResult",
    "AutomatedValidationResponse",

    # List and filter schemas
    "WorkflowListFilters",
    "WorkflowListResponse",

    # Statistics schemas
    "VerificationStatistics",
]
