"""
Booking repository for Culture Connect Backend API.

This module provides comprehensive data access layer for booking workflow management
including CRUD operations, advanced querying, and business logic support.

Implements Task 4.1.1 requirements for booking repository layer with:
- Complete booking lifecycle data access
- Vendor approval workflow queries
- Customer notification data management
- Booking communication data access
- Real-time availability checking
- Audit trail and status history queries

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from app.repositories.base import BaseRepository, PaginationParams, QueryResult, RepositoryError
from app.models.booking import (
    Booking, BookingStatusHistory, BookingCommunication, BookingModification,
    BookingStatus, VendorResponseType, BookingPriority, CommunicationType, ModificationType
)
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service, ServiceAvailability


class BookingRepository(BaseRepository[Booking]):
    """
    Repository for booking data access operations.

    Provides comprehensive booking data management including:
    - CRUD operations with performance optimization
    - Advanced querying with filtering and pagination
    - Booking lifecycle management
    - Vendor approval workflow support
    - Customer communication data access
    - Real-time availability checking
    """

    def __init__(self, db):
        """Initialize booking repository."""
        super().__init__(Booking, db)
        self.logger = logging.getLogger(f"{__name__}.BookingRepository")

    async def create_booking(
        self,
        customer_id: int,
        vendor_id: int,
        service_id: int,
        booking_data: Dict[str, Any]
    ) -> Booking:
        """
        Create a new booking with comprehensive validation.

        Args:
            customer_id: Customer ID
            vendor_id: Vendor ID
            service_id: Service ID
            booking_data: Booking creation data

        Returns:
            Created booking instance

        Raises:
            RepositoryError: If booking creation fails
        """
        try:
            # Generate unique booking reference
            booking_reference = await self._generate_booking_reference()

            # Prepare booking data
            booking_obj_data = {
                "customer_id": customer_id,
                "vendor_id": vendor_id,
                "service_id": service_id,
                "booking_reference": booking_reference,
                "status": BookingStatus.PENDING,
                **booking_data
            }

            # Create booking
            booking = await self.create(booking_obj_data)

            # Create initial status history entry
            await self._create_status_history(
                booking.id,
                None,
                BookingStatus.PENDING,
                customer_id,
                "Booking created",
                automated_change=True,
                system_trigger="booking_creation"
            )

            return booking

        except Exception as e:
            self.logger.error(f"Failed to create booking: {str(e)}")
            raise RepositoryError(f"Failed to create booking", e)

    async def get_booking_with_details(self, booking_id: int) -> Optional[Booking]:
        """
        Get booking with all related data.

        Args:
            booking_id: Booking ID

        Returns:
            Booking instance with related data if found
        """
        try:
            stmt = (
                select(Booking)
                .options(
                    selectinload(Booking.customer),
                    selectinload(Booking.vendor),
                    selectinload(Booking.service),
                    selectinload(Booking.availability),
                    selectinload(Booking.status_history),
                    selectinload(Booking.communications),
                    selectinload(Booking.modifications)
                )
                .where(Booking.id == booking_id)
            )

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to get booking with details: {str(e)}")
            raise RepositoryError(f"Failed to get booking with details", e)

    async def get_bookings_by_customer(
        self,
        customer_id: int,
        status_filter: Optional[List[BookingStatus]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Booking]:
        """
        Get bookings for a specific customer.

        Args:
            customer_id: Customer ID
            status_filter: Optional list of statuses to filter by
            pagination: Pagination parameters

        Returns:
            Query result with bookings
        """
        try:
            stmt = select(Booking).where(Booking.customer_id == customer_id)

            # Apply status filter
            if status_filter:
                stmt = stmt.where(Booking.status.in_(status_filter))

            # Apply ordering
            stmt = stmt.order_by(desc(Booking.created_at))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get customer bookings: {str(e)}")
            raise RepositoryError(f"Failed to get customer bookings", e)

    async def get_bookings_by_vendor(
        self,
        vendor_id: int,
        status_filter: Optional[List[BookingStatus]] = None,
        date_range: Optional[Tuple[date, date]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Booking]:
        """
        Get bookings for a specific vendor.

        Args:
            vendor_id: Vendor ID
            status_filter: Optional list of statuses to filter by
            date_range: Optional date range filter (start_date, end_date)
            pagination: Pagination parameters

        Returns:
            Query result with bookings
        """
        try:
            stmt = select(Booking).where(Booking.vendor_id == vendor_id)

            # Apply status filter
            if status_filter:
                stmt = stmt.where(Booking.status.in_(status_filter))

            # Apply date range filter
            if date_range:
                start_date, end_date = date_range
                stmt = stmt.where(
                    and_(
                        Booking.booking_date >= start_date,
                        Booking.booking_date <= end_date
                    )
                )

            # Apply ordering
            stmt = stmt.order_by(desc(Booking.booking_date), desc(Booking.created_at))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get vendor bookings: {str(e)}")
            raise RepositoryError(f"Failed to get vendor bookings", e)

    async def get_bookings_by_service(
        self,
        service_id: int,
        status_filter: Optional[List[BookingStatus]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Booking]:
        """
        Get bookings for a specific service.

        Args:
            service_id: Service ID
            status_filter: Optional list of statuses to filter by
            pagination: Pagination parameters

        Returns:
            Query result with bookings
        """
        try:
            stmt = select(Booking).where(Booking.service_id == service_id)

            # Apply status filter
            if status_filter:
                stmt = stmt.where(Booking.status.in_(status_filter))

            # Apply ordering
            stmt = stmt.order_by(desc(Booking.booking_date))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get service bookings: {str(e)}")
            raise RepositoryError(f"Failed to get service bookings", e)

    async def get_pending_vendor_responses(
        self,
        vendor_id: Optional[int] = None,
        overdue_only: bool = False
    ) -> List[Booking]:
        """
        Get bookings pending vendor response.

        Args:
            vendor_id: Optional vendor ID to filter by
            overdue_only: Whether to return only overdue responses

        Returns:
            List of bookings pending vendor response
        """
        try:
            stmt = select(Booking).where(
                and_(
                    Booking.status.in_([BookingStatus.PENDING, BookingStatus.VENDOR_REVIEW]),
                    Booking.vendor_response_type.is_(None)
                )
            )

            # Filter by vendor
            if vendor_id:
                stmt = stmt.where(Booking.vendor_id == vendor_id)

            # Filter overdue responses
            if overdue_only:
                stmt = stmt.where(
                    and_(
                        Booking.vendor_response_deadline.isnot(None),
                        Booking.vendor_response_deadline < datetime.utcnow()
                    )
                )

            # Order by deadline and creation date
            stmt = stmt.order_by(
                asc(Booking.vendor_response_deadline),
                asc(Booking.created_at)
            )

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(f"Failed to get pending vendor responses: {str(e)}")
            raise RepositoryError(f"Failed to get pending vendor responses", e)

    async def update_booking_status(
        self,
        booking_id: int,
        new_status: BookingStatus,
        changed_by: Optional[int] = None,
        change_reason: Optional[str] = None,
        change_notes: Optional[str] = None,
        automated_change: bool = False,
        system_trigger: Optional[str] = None
    ) -> Optional[Booking]:
        """
        Update booking status with history tracking.

        Args:
            booking_id: Booking ID
            new_status: New booking status
            changed_by: User who made the change
            change_reason: Reason for status change
            change_notes: Additional notes
            automated_change: Whether change was automated
            system_trigger: System trigger that caused the change

        Returns:
            Updated booking instance if found
        """
        try:
            # Get current booking
            booking = await self.get(booking_id)
            if not booking:
                return None

            previous_status = booking.status

            # Update booking status
            booking.status = new_status
            await self.db.commit()
            await self.db.refresh(booking)

            # Create status history entry
            await self._create_status_history(
                booking_id,
                previous_status,
                new_status,
                changed_by,
                change_reason,
                change_notes,
                automated_change,
                system_trigger
            )

            return booking

        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"Failed to update booking status: {str(e)}")
            raise RepositoryError(f"Failed to update booking status", e)

    async def update_vendor_response(
        self,
        booking_id: int,
        response_type: VendorResponseType,
        vendor_notes: Optional[str] = None,
        vendor_id: Optional[int] = None
    ) -> Optional[Booking]:
        """
        Update vendor response for booking.

        Args:
            booking_id: Booking ID
            response_type: Vendor response type
            vendor_notes: Vendor notes
            vendor_id: Vendor ID for validation

        Returns:
            Updated booking instance if found
        """
        try:
            booking = await self.get(booking_id)
            if not booking:
                return None

            # Validate vendor ownership
            if vendor_id and booking.vendor_id != vendor_id:
                raise RepositoryError("Vendor not authorized for this booking")

            # Update vendor response
            booking.vendor_response_type = response_type
            booking.vendor_response_at = datetime.utcnow()
            if vendor_notes:
                booking.vendor_notes = vendor_notes

            # Update status based on response
            if response_type == VendorResponseType.APPROVED:
                booking.status = BookingStatus.CONFIRMED
            elif response_type == VendorResponseType.REJECTED:
                booking.status = BookingStatus.REJECTED

            await self.db.commit()
            await self.db.refresh(booking)

            return booking

        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"Failed to update vendor response: {str(e)}")
            raise RepositoryError(f"Failed to update vendor response", e)

    async def check_availability_conflict(
        self,
        service_id: int,
        booking_date: date,
        booking_time: Optional[datetime] = None,
        duration_hours: Optional[Decimal] = None,
        exclude_booking_id: Optional[int] = None
    ) -> bool:
        """
        Check for booking conflicts on the same service.

        Args:
            service_id: Service ID
            booking_date: Booking date
            booking_time: Booking time
            duration_hours: Service duration
            exclude_booking_id: Booking ID to exclude from conflict check

        Returns:
            True if conflict exists, False otherwise
        """
        try:
            stmt = select(Booking).where(
                and_(
                    Booking.service_id == service_id,
                    Booking.booking_date == booking_date,
                    Booking.status.in_([
                        BookingStatus.CONFIRMED,
                        BookingStatus.PAYMENT_PENDING,
                        BookingStatus.PAID,
                        BookingStatus.IN_PROGRESS
                    ])
                )
            )

            # Exclude specific booking
            if exclude_booking_id:
                stmt = stmt.where(Booking.id != exclude_booking_id)

            # Time-based conflict checking
            if booking_time and duration_hours:
                # Check for time overlaps
                end_time = booking_time + timedelta(hours=float(duration_hours))
                stmt = stmt.where(
                    or_(
                        # Booking starts during existing booking
                        and_(
                            Booking.booking_time <= booking_time,
                            func.datetime(Booking.booking_time, text("'+' || CAST(duration_hours AS TEXT) || ' hours'")) > booking_time
                        ),
                        # Booking ends during existing booking
                        and_(
                            Booking.booking_time < end_time,
                            func.datetime(Booking.booking_time, text("'+' || CAST(duration_hours AS TEXT) || ' hours'")) >= end_time
                        ),
                        # Existing booking is within new booking
                        and_(
                            Booking.booking_time >= booking_time,
                            func.datetime(Booking.booking_time, text("'+' || CAST(duration_hours AS TEXT) || ' hours'")) <= end_time
                        )
                    )
                )

            result = await self.db.execute(stmt)
            conflicting_bookings = result.scalars().all()

            return len(conflicting_bookings) > 0

        except Exception as e:
            self.logger.error(f"Failed to check availability conflict: {str(e)}")
            raise RepositoryError(f"Failed to check availability conflict", e)

    async def get_booking_analytics(
        self,
        vendor_id: Optional[int] = None,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get booking analytics data.

        Args:
            vendor_id: Optional vendor ID to filter by
            date_range: Optional date range filter

        Returns:
            Dictionary with analytics data
        """
        try:
            base_query = select(Booking)

            # Apply filters
            if vendor_id:
                base_query = base_query.where(Booking.vendor_id == vendor_id)

            if date_range:
                start_date, end_date = date_range
                base_query = base_query.where(
                    and_(
                        Booking.booking_date >= start_date,
                        Booking.booking_date <= end_date
                    )
                )

            # Total bookings
            total_bookings_stmt = select(func.count(Booking.id)).select_from(base_query.subquery())
            total_bookings_result = await self.db.execute(total_bookings_stmt)
            total_bookings = total_bookings_result.scalar()

            # Bookings by status
            status_stats_stmt = (
                select(Booking.status, func.count(Booking.id))
                .select_from(base_query.subquery())
                .group_by(Booking.status)
            )
            status_stats_result = await self.db.execute(status_stats_stmt)
            status_stats = dict(status_stats_result.fetchall())

            # Revenue analytics
            revenue_stmt = (
                select(
                    func.sum(Booking.total_amount).label('total_revenue'),
                    func.avg(Booking.total_amount).label('average_booking_value'),
                    func.sum(Booking.commission_amount).label('total_commission')
                )
                .select_from(base_query.subquery())
                .where(Booking.status.in_([BookingStatus.PAID, BookingStatus.COMPLETED]))
            )
            revenue_result = await self.db.execute(revenue_stmt)
            revenue_data = revenue_result.fetchone()

            return {
                'total_bookings': total_bookings,
                'status_distribution': status_stats,
                'total_revenue': float(revenue_data.total_revenue or 0),
                'average_booking_value': float(revenue_data.average_booking_value or 0),
                'total_commission': float(revenue_data.total_commission or 0),
                'completion_rate': (
                    status_stats.get(BookingStatus.COMPLETED, 0) / total_bookings * 100
                    if total_bookings > 0 else 0
                )
            }

        except Exception as e:
            self.logger.error(f"Failed to get booking analytics: {str(e)}")
            raise RepositoryError(f"Failed to get booking analytics", e)

    # Helper methods
    async def _generate_booking_reference(self) -> str:
        """Generate unique booking reference."""
        import random
        import string

        while True:
            # Generate reference: BK-YYYY-NNNNNN
            year = datetime.now().year
            random_part = ''.join(random.choices(string.digits, k=6))
            reference = f"BK-{year}-{random_part}"

            # Check if reference already exists
            existing = await self.db.execute(
                select(Booking).where(Booking.booking_reference == reference)
            )
            if not existing.scalar_one_or_none():
                return reference

    async def _create_status_history(
        self,
        booking_id: int,
        previous_status: Optional[BookingStatus],
        new_status: BookingStatus,
        changed_by: Optional[int],
        change_reason: Optional[str] = None,
        change_notes: Optional[str] = None,
        automated_change: bool = False,
        system_trigger: Optional[str] = None
    ) -> BookingStatusHistory:
        """Create booking status history entry."""
        try:
            history_data = {
                'booking_id': booking_id,
                'previous_status': previous_status,
                'new_status': new_status,
                'changed_by': changed_by,
                'change_reason': change_reason,
                'change_notes': change_notes,
                'automated_change': automated_change,
                'system_trigger': system_trigger
            }

            history_entry = BookingStatusHistory(**history_data)
            self.db.add(history_entry)
            await self.db.commit()
            await self.db.refresh(history_entry)

            return history_entry

        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"Failed to create status history: {str(e)}")
            raise RepositoryError(f"Failed to create status history", e)

    async def _execute_paginated_query(
        self,
        stmt,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Booking]:
        """Execute paginated query with default pagination."""
        if pagination is None:
            pagination = PaginationParams(page=1, per_page=20)

        # Get total count
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # Apply pagination
        offset = (pagination.page - 1) * pagination.per_page
        paginated_stmt = stmt.offset(offset).limit(pagination.per_page)

        # Execute query
        result = await self.db.execute(paginated_stmt)
        items = result.scalars().all()

        # Calculate pagination info
        pages = (total + pagination.per_page - 1) // pagination.per_page
        has_next = pagination.page < pages
        has_prev = pagination.page > 1

        return QueryResult(
            items=items,
            total=total,
            page=pagination.page,
            per_page=pagination.per_page,
            pages=pages,
            has_next=has_next,
            has_prev=has_prev
        )
