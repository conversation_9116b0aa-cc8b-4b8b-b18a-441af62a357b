"""
Database performance optimization and monitoring utilities.

This module provides tools for database performance optimization including:
- Query performance analysis
- Connection pool monitoring
- Database statistics collection
- Performance tuning recommendations
- Slow query detection and logging
"""

import logging
import time
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from contextlib import asynccontextmanager

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_session_context, session_manager
from app.db.config import db_config_manager, get_database_health_thresholds

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class QueryPerformanceMetric:
    """Query performance measurement data."""
    query_hash: str
    query_type: str
    execution_time: float
    rows_affected: int
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None


@dataclass
class ConnectionPoolMetrics:
    """Connection pool performance metrics."""
    pool_size: int
    checked_out: int
    overflow: int
    checked_in: int
    total_connections: int
    active_connections: int
    failed_connections: int
    retry_attempts: int
    average_checkout_time: float
    pool_utilization: float
    timestamp: datetime


@dataclass
class DatabasePerformanceReport:
    """Comprehensive database performance report."""
    timestamp: datetime
    overall_health: str
    connection_pool: ConnectionPoolMetrics
    query_metrics: List[QueryPerformanceMetric]
    slow_queries: List[QueryPerformanceMetric]
    recommendations: List[str]
    statistics: Dict[str, Any]


class QueryPerformanceMonitor:
    """
    Monitor and analyze database query performance.
    
    Tracks query execution times, identifies slow queries, and provides
    performance optimization recommendations.
    """
    
    def __init__(self, slow_query_threshold: float = 1.0):
        self.slow_query_threshold = slow_query_threshold
        self.query_metrics: List[QueryPerformanceMetric] = []
        self.max_metrics_history = 1000
    
    @asynccontextmanager
    async def monitor_query(self, query_type: str = "unknown"):
        """
        Context manager to monitor query performance.
        
        Args:
            query_type: Type of query being executed
            
        Yields:
            AsyncSession: Database session with performance monitoring
        """
        start_time = time.time()
        query_hash = f"{query_type}_{int(start_time * 1000)}"
        success = True
        error_message = None
        rows_affected = 0
        
        try:
            async with get_async_session_context() as session:
                yield session
                
                # Try to get rows affected (if available)
                try:
                    if hasattr(session, 'rowcount'):
                        rows_affected = session.rowcount or 0
                except:
                    rows_affected = 0
                    
        except Exception as e:
            success = False
            error_message = str(e)
            logger.error(f"Query execution failed: {error_message}")
            raise
        finally:
            execution_time = time.time() - start_time
            
            # Record performance metric
            metric = QueryPerformanceMetric(
                query_hash=query_hash,
                query_type=query_type,
                execution_time=execution_time,
                rows_affected=rows_affected,
                timestamp=datetime.utcnow(),
                success=success,
                error_message=error_message
            )
            
            self._record_metric(metric)
            
            # Log slow queries
            if execution_time > self.slow_query_threshold:
                logger.warning(
                    f"Slow query detected: {query_type} took {execution_time:.3f}s",
                    extra={
                        "query_type": query_type,
                        "execution_time": execution_time,
                        "rows_affected": rows_affected,
                        "performance_metric": "slow_query"
                    }
                )
    
    def _record_metric(self, metric: QueryPerformanceMetric):
        """Record a query performance metric."""
        self.query_metrics.append(metric)
        
        # Maintain maximum history size
        if len(self.query_metrics) > self.max_metrics_history:
            self.query_metrics = self.query_metrics[-self.max_metrics_history:]
    
    def get_slow_queries(self, threshold: Optional[float] = None) -> List[QueryPerformanceMetric]:
        """
        Get list of slow queries.
        
        Args:
            threshold: Custom threshold for slow queries
            
        Returns:
            List[QueryPerformanceMetric]: List of slow queries
        """
        threshold = threshold or self.slow_query_threshold
        return [
            metric for metric in self.query_metrics
            if metric.execution_time > threshold and metric.success
        ]
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """
        Get query performance statistics.
        
        Returns:
            Dict[str, Any]: Query performance statistics
        """
        if not self.query_metrics:
            return {"total_queries": 0}
        
        successful_queries = [m for m in self.query_metrics if m.success]
        failed_queries = [m for m in self.query_metrics if not m.success]
        
        if successful_queries:
            execution_times = [m.execution_time for m in successful_queries]
            avg_execution_time = sum(execution_times) / len(execution_times)
            max_execution_time = max(execution_times)
            min_execution_time = min(execution_times)
        else:
            avg_execution_time = max_execution_time = min_execution_time = 0.0
        
        return {
            "total_queries": len(self.query_metrics),
            "successful_queries": len(successful_queries),
            "failed_queries": len(failed_queries),
            "success_rate": len(successful_queries) / len(self.query_metrics) * 100,
            "average_execution_time": avg_execution_time,
            "max_execution_time": max_execution_time,
            "min_execution_time": min_execution_time,
            "slow_queries_count": len(self.get_slow_queries()),
            "queries_by_type": self._get_queries_by_type()
        }
    
    def _get_queries_by_type(self) -> Dict[str, int]:
        """Get query count by type."""
        query_types = {}
        for metric in self.query_metrics:
            query_types[metric.query_type] = query_types.get(metric.query_type, 0) + 1
        return query_types


class ConnectionPoolMonitor:
    """
    Monitor database connection pool performance.
    
    Tracks connection pool utilization, checkout times, and provides
    optimization recommendations.
    """
    
    def __init__(self):
        self.checkout_times: List[float] = []
        self.max_checkout_history = 100
    
    def record_checkout_time(self, checkout_time: float):
        """Record connection checkout time."""
        self.checkout_times.append(checkout_time)
        
        # Maintain maximum history size
        if len(self.checkout_times) > self.max_checkout_history:
            self.checkout_times = self.checkout_times[-self.max_checkout_history:]
    
    def get_pool_metrics(self) -> ConnectionPoolMetrics:
        """
        Get current connection pool metrics.
        
        Returns:
            ConnectionPoolMetrics: Current pool metrics
        """
        from app.db.database import async_engine
        
        # Get session manager stats
        session_stats = session_manager.get_connection_stats()
        
        # Get engine pool stats
        pool = async_engine.pool
        pool_size = pool.size()
        checked_out = pool.checkedout()
        overflow = pool.overflow()
        checked_in = pool.checkedin()
        
        # Calculate metrics
        total_connections = session_stats.get("total_connections", 0)
        active_connections = session_stats.get("active_connections", 0)
        failed_connections = session_stats.get("failed_connections", 0)
        retry_attempts = session_stats.get("retry_attempts", 0)
        
        # Calculate average checkout time
        avg_checkout_time = (
            sum(self.checkout_times) / len(self.checkout_times)
            if self.checkout_times else 0.0
        )
        
        # Calculate pool utilization
        pool_utilization = (checked_out / pool_size * 100) if pool_size > 0 else 0.0
        
        return ConnectionPoolMetrics(
            pool_size=pool_size,
            checked_out=checked_out,
            overflow=overflow,
            checked_in=checked_in,
            total_connections=total_connections,
            active_connections=active_connections,
            failed_connections=failed_connections,
            retry_attempts=retry_attempts,
            average_checkout_time=avg_checkout_time,
            pool_utilization=pool_utilization,
            timestamp=datetime.utcnow()
        )


class DatabasePerformanceAnalyzer:
    """
    Comprehensive database performance analyzer.
    
    Combines query monitoring, connection pool monitoring, and provides
    performance optimization recommendations.
    """
    
    def __init__(self):
        self.query_monitor = QueryPerformanceMonitor()
        self.pool_monitor = ConnectionPoolMonitor()
        self.health_thresholds = get_database_health_thresholds()
    
    async def generate_performance_report(self) -> DatabasePerformanceReport:
        """
        Generate comprehensive database performance report.
        
        Returns:
            DatabasePerformanceReport: Complete performance analysis
        """
        # Get current metrics
        pool_metrics = self.pool_monitor.get_pool_metrics()
        query_stats = self.query_monitor.get_query_statistics()
        slow_queries = self.query_monitor.get_slow_queries()
        
        # Determine overall health
        overall_health = self._assess_overall_health(pool_metrics, query_stats)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(pool_metrics, query_stats)
        
        # Collect additional statistics
        statistics = await self._collect_database_statistics()
        
        return DatabasePerformanceReport(
            timestamp=datetime.utcnow(),
            overall_health=overall_health,
            connection_pool=pool_metrics,
            query_metrics=self.query_monitor.query_metrics[-10:],  # Last 10 queries
            slow_queries=slow_queries,
            recommendations=recommendations,
            statistics=statistics
        )
    
    def _assess_overall_health(self, pool_metrics: ConnectionPoolMetrics, query_stats: Dict[str, Any]) -> str:
        """Assess overall database health based on metrics."""
        health_issues = []
        
        # Check pool utilization
        if pool_metrics.pool_utilization > 80:
            health_issues.append("high_pool_utilization")
        
        # Check failure rate
        if pool_metrics.total_connections > 0:
            failure_rate = pool_metrics.failed_connections / pool_metrics.total_connections
            if failure_rate > self.health_thresholds["max_pool_failure_rate"]:
                health_issues.append("high_failure_rate")
        
        # Check query performance
        avg_execution_time = query_stats.get("average_execution_time", 0)
        if avg_execution_time > self.health_thresholds["max_response_time"]:
            health_issues.append("slow_queries")
        
        # Check success rate
        success_rate = query_stats.get("success_rate", 100)
        if success_rate < 95:
            health_issues.append("low_success_rate")
        
        # Determine overall health
        if not health_issues:
            return "excellent"
        elif len(health_issues) == 1:
            return "good"
        elif len(health_issues) <= 2:
            return "acceptable"
        else:
            return "poor"
    
    def _generate_recommendations(self, pool_metrics: ConnectionPoolMetrics, query_stats: Dict[str, Any]) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # Pool-related recommendations
        if pool_metrics.pool_utilization > 80:
            recommendations.append("Consider increasing connection pool size")
        
        if pool_metrics.failed_connections > 0:
            recommendations.append("Investigate connection failures and implement retry logic")
        
        if pool_metrics.average_checkout_time > 1.0:
            recommendations.append("Connection checkout time is high - check for connection leaks")
        
        # Query-related recommendations
        avg_execution_time = query_stats.get("average_execution_time", 0)
        if avg_execution_time > 1.0:
            recommendations.append("Average query execution time is high - consider query optimization")
        
        slow_queries_count = query_stats.get("slow_queries_count", 0)
        if slow_queries_count > 0:
            recommendations.append(f"Found {slow_queries_count} slow queries - review and optimize")
        
        success_rate = query_stats.get("success_rate", 100)
        if success_rate < 95:
            recommendations.append("Query success rate is low - investigate error patterns")
        
        # Environment-specific recommendations
        env = db_config_manager.environment
        if env.value == "production":
            recommendations.append("Enable query logging for production monitoring")
            recommendations.append("Consider implementing read replicas for read-heavy workloads")
        
        return recommendations
    
    async def _collect_database_statistics(self) -> Dict[str, Any]:
        """Collect additional database statistics."""
        try:
            async with get_async_session_context() as session:
                # Basic database info
                stats = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "environment": db_config_manager.environment.value,
                    "database_type": db_config_manager.database_type.value,
                }
                
                # Try to get database-specific statistics
                if db_config_manager.database_type.value == "postgresql":
                    # PostgreSQL-specific stats
                    try:
                        result = await session.execute(text("SELECT count(*) FROM pg_stat_activity"))
                        stats["active_connections"] = result.scalar()
                        
                        result = await session.execute(text("SELECT pg_database_size(current_database())"))
                        stats["database_size_bytes"] = result.scalar()
                    except:
                        pass
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to collect database statistics: {str(e)}")
            return {"error": str(e)}


# Global performance analyzer instance
performance_analyzer = DatabasePerformanceAnalyzer()


async def get_performance_report() -> DatabasePerformanceReport:
    """
    Get comprehensive database performance report.
    
    Returns:
        DatabasePerformanceReport: Performance analysis report
    """
    return await performance_analyzer.generate_performance_report()


async def monitor_query_performance(query_type: str = "unknown"):
    """
    Context manager for monitoring query performance.
    
    Args:
        query_type: Type of query being monitored
        
    Returns:
        Context manager for query monitoring
    """
    return performance_analyzer.query_monitor.monitor_query(query_type)


def record_connection_checkout_time(checkout_time: float):
    """
    Record connection checkout time for monitoring.
    
    Args:
        checkout_time: Time taken to checkout connection
    """
    performance_analyzer.pool_monitor.record_checkout_time(checkout_time)


# Export commonly used items
__all__ = [
    'QueryPerformanceMetric',
    'ConnectionPoolMetrics',
    'DatabasePerformanceReport',
    'QueryPerformanceMonitor',
    'ConnectionPoolMonitor',
    'DatabasePerformanceAnalyzer',
    'performance_analyzer',
    'get_performance_report',
    'monitor_query_performance',
    'record_connection_checkout_time',
]
