"""
Financial Services for Culture Connect Backend API.

This module provides comprehensive financial functionality including:
- RevenueRecordService: Revenue tracking and analytics with multi-currency support
- ReconciliationRecordService: Financial reconciliation and variance detection
- FinancialAnalyticsService: Financial analytics and reporting

Implements Step 6 Payment & Transaction Management System service layer with
production-grade functionality, financial analytics, and comprehensive reconciliation.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.financial_repository import RevenueRecordRepository, ReconciliationRecordRepository
from app.models.financial_models import (
    RevenueRecord, ReconciliationRecord, RevenueCategory,
    ReconciliationStatus
)
from app.core.payment.config import PaymentProviderType


class VarianceType(str, Enum):
    """Variance type enumeration for reconciliation."""
    MINOR = "minor"
    MAJOR = "major"
# TODO: Import financial schemas when implemented
# from app.schemas.financial_schemas import (
#     RevenueRecordCreate, RevenueRecordUpdate, RevenueRecordResponse,
#     ReconciliationRecordCreate, ReconciliationRecordUpdate, ReconciliationRecordResponse
# )
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)


class RevenueRecordService(BaseService[RevenueRecord, RevenueRecordRepository]):
    """
    Revenue record service for tracking and analytics with multi-currency support.

    Provides comprehensive revenue operations including:
    - Revenue tracking and categorization by type
    - Multi-currency revenue analytics and reporting
    - Period-based revenue aggregation and analysis
    - Revenue performance metrics and KPI calculation
    - Automated revenue recognition and booking
    """

    def __init__(self, db: AsyncSession):
        """Initialize revenue record service."""
        super().__init__(RevenueRecordRepository, RevenueRecord, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate revenue record creation data with business rules.

        Args:
            data: Revenue record creation data

        Returns:
            Validated revenue data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['amount', 'revenue_type', 'transaction_id']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate amount
        amount = data.get('amount')
        if not isinstance(amount, (int, float, Decimal)) or amount <= 0:
            raise ValidationError("Amount must be positive", field="amount")

        # Validate revenue type
        revenue_type = data.get('revenue_type')
        if not isinstance(revenue_type, RevenueCategory):
            raise ValidationError("Invalid revenue type", field="revenue_type")

        # Validate currency
        currency = data.get('currency', 'NGN')
        if currency not in ['NGN', 'USD', 'EUR', 'GBP']:
            raise ValidationError("Invalid currency", field="currency")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate revenue record update data with business rules.

        Args:
            data: Revenue record update data
            existing_id: Existing revenue record ID

        Returns:
            Validated revenue data

        Raises:
            ValidationError: If validation fails
        """
        # Prevent modification of critical fields
        protected_fields = ['amount', 'revenue_type', 'transaction_id', 'currency']
        for field in protected_fields:
            if field in data:
                raise ValidationError(f"Cannot modify {field} after creation", field=field)

        return data

    async def create_revenue_record(
        self,
        amount: Decimal,
        revenue_type: RevenueCategory,
        transaction_id: int,
        currency: str = "NGN",
        vendor_id: Optional[int] = None,
        booking_id: Optional[int] = None,
        **kwargs
    ) -> RevenueRecord:
        """
        Create a new revenue record with automated categorization.

        Args:
            amount: Revenue amount
            revenue_type: Type of revenue
            transaction_id: Associated transaction ID
            currency: Revenue currency
            vendor_id: Optional vendor ID
            booking_id: Optional booking ID
            **kwargs: Additional revenue fields

        Returns:
            Created revenue record instance

        Raises:
            ValidationError: If revenue data is invalid
            ServiceError: If revenue creation fails
        """
        correlation = self.log_operation_start(
            "create_revenue_record",
            amount=float(amount),
            revenue_type=revenue_type.value,
            transaction_id=transaction_id
        )

        try:
            # Create revenue record using repository
            async with self.get_transaction_context() as session:
                repository = RevenueRecordRepository(session)
                revenue = await repository.create_revenue_record(
                    amount=amount,
                    revenue_type=revenue_type,
                    transaction_id=transaction_id,
                    currency=currency,
                    vendor_id=vendor_id,
                    booking_id=booking_id,
                    **kwargs
                )

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Revenue record created with ID: {revenue.id}, amount: {amount}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "revenue_record_created",
                    tags={
                        "revenue_type": revenue_type.value,
                        "currency": currency,
                        "vendor_id": str(vendor_id) if vendor_id else "none"
                    }
                )

                return revenue

        except Exception as e:
            await self.handle_service_error(e, "create_revenue_record", {
                "amount": float(amount),
                "revenue_type": revenue_type.value,
                "transaction_id": transaction_id
            })

    async def get_revenue_summary(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        revenue_type: Optional[RevenueCategory] = None,
        currency: str = "NGN",
        vendor_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get revenue summary with filtering and aggregation.

        Args:
            date_from: Start date filter
            date_to: End date filter
            revenue_type: Optional revenue type filter
            currency: Currency filter
            vendor_id: Optional vendor filter

        Returns:
            Revenue summary dictionary

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = RevenueRecordRepository(session)
                return await repository.get_revenue_summary(
                    date_from=date_from,
                    date_to=date_to,
                    revenue_type=revenue_type,
                    currency=currency,
                    vendor_id=vendor_id
                )

        except Exception as e:
            await self.handle_service_error(e, "get_revenue_summary", {
                "currency": currency,
                "vendor_id": vendor_id
            })

    async def get_revenue_analytics(
        self,
        period: str = "month",
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get revenue analytics with period-based aggregation.

        Args:
            period: Aggregation period (day, week, month, year)
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Revenue analytics dictionary

        Raises:
            ServiceError: If analytics query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = RevenueRecordRepository(session)
                return await repository.get_revenue_analytics(
                    period=period,
                    date_from=date_from,
                    date_to=date_to,
                    currency=currency
                )

        except Exception as e:
            await self.handle_service_error(e, "get_revenue_analytics", {
                "period": period,
                "currency": currency
            })

    async def get_vendor_revenue_breakdown(
        self,
        vendor_id: int,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get detailed revenue breakdown for a specific vendor.

        Args:
            vendor_id: Vendor ID
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Vendor revenue breakdown

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = RevenueRecordRepository(session)
                return await repository.get_vendor_revenue_breakdown(
                    vendor_id=vendor_id,
                    date_from=date_from,
                    date_to=date_to,
                    currency=currency
                )

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_revenue_breakdown", {
                "vendor_id": vendor_id,
                "currency": currency
            })


class ReconciliationRecordService(BaseService[ReconciliationRecord, ReconciliationRecordRepository]):
    """
    Reconciliation record service for variance detection and financial reconciliation.

    Provides comprehensive reconciliation operations including:
    - Variance detection algorithms with configurable thresholds
    - Provider statement reconciliation automation
    - Discrepancy tracking and resolution workflows
    - Multi-provider reconciliation support
    - Automated reconciliation reporting and alerts
    """

    def __init__(self, db: AsyncSession):
        """Initialize reconciliation record service."""
        super().__init__(ReconciliationRecordRepository, ReconciliationRecord, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate reconciliation record creation data with business rules.

        Args:
            data: Reconciliation record creation data

        Returns:
            Validated reconciliation data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['provider', 'period_start', 'period_end']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate provider
        provider = data.get('provider')
        if not isinstance(provider, PaymentProviderType):
            raise ValidationError("Invalid payment provider", field="provider")

        # Validate period dates
        period_start = data.get('period_start')
        period_end = data.get('period_end')

        if period_start and period_end and period_start >= period_end:
            raise ValidationError("Period start must be before period end", field="period_start")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate reconciliation record update data with business rules.

        Args:
            data: Reconciliation record update data
            existing_id: Existing reconciliation record ID

        Returns:
            Validated reconciliation data

        Raises:
            ValidationError: If validation fails
        """
        # Prevent modification of critical fields
        protected_fields = ['provider', 'period_start', 'period_end']
        for field in protected_fields:
            if field in data:
                raise ValidationError(f"Cannot modify {field} after creation", field=field)

        # Validate status
        if 'status' in data:
            status = data['status']
            if not isinstance(status, ReconciliationStatus):
                raise ValidationError("Invalid reconciliation status", field="status")

        return data

    async def create_reconciliation_record(
        self,
        provider: PaymentProviderType,
        period_start: datetime,
        period_end: datetime,
        expected_amount: Decimal,
        actual_amount: Decimal,
        currency: str = "NGN",
        **kwargs
    ) -> ReconciliationRecord:
        """
        Create a new reconciliation record with variance detection.

        Args:
            provider: Payment provider
            period_start: Reconciliation period start
            period_end: Reconciliation period end
            expected_amount: Expected amount from internal records
            actual_amount: Actual amount from provider statement
            currency: Reconciliation currency
            **kwargs: Additional reconciliation fields

        Returns:
            Created reconciliation record instance

        Raises:
            ValidationError: If reconciliation data is invalid
            ServiceError: If reconciliation creation fails
        """
        correlation = self.log_operation_start(
            "create_reconciliation_record",
            provider=provider.value,
            expected_amount=float(expected_amount),
            actual_amount=float(actual_amount)
        )

        try:
            # Calculate variance
            variance_amount = actual_amount - expected_amount
            variance_percentage = (variance_amount / expected_amount * 100) if expected_amount != 0 else 0

            # Determine variance type and status
            variance_type = self._determine_variance_type(variance_amount, variance_percentage)
            status = self._determine_reconciliation_status(variance_amount, variance_percentage)

            # Create reconciliation record using repository
            async with self.get_transaction_context() as session:
                repository = ReconciliationRecordRepository(session)
                reconciliation = await repository.create_reconciliation_record(
                    provider=provider,
                    period_start=period_start,
                    period_end=period_end,
                    expected_amount=expected_amount,
                    actual_amount=actual_amount,
                    variance_amount=variance_amount,
                    variance_percentage=variance_percentage,
                    variance_type=variance_type,
                    status=status,
                    currency=currency,
                    **kwargs
                )

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Reconciliation record created with ID: {reconciliation.id}, variance: {variance_amount}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "reconciliation_record_created",
                    tags={
                        "provider": provider.value,
                        "currency": currency,
                        "variance_type": variance_type.value if variance_type else "none",
                        "status": status.value
                    }
                )

                return reconciliation

        except Exception as e:
            await self.handle_service_error(e, "create_reconciliation_record", {
                "provider": provider.value,
                "expected_amount": float(expected_amount),
                "actual_amount": float(actual_amount)
            })

    async def update_reconciliation_status(
        self,
        reconciliation_id: int,
        status: ReconciliationStatus,
        resolution_notes: Optional[str] = None,
        resolved_by: Optional[int] = None,
        **kwargs
    ) -> ReconciliationRecord:
        """
        Update reconciliation record status with resolution tracking.

        Args:
            reconciliation_id: Reconciliation record ID
            status: New reconciliation status
            resolution_notes: Optional resolution notes
            resolved_by: User ID who resolved the reconciliation
            **kwargs: Additional fields to update

        Returns:
            Updated reconciliation record instance

        Raises:
            NotFoundError: If reconciliation record not found
            ValidationError: If status transition is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_reconciliation_status",
            reconciliation_id=reconciliation_id,
            status=status.value
        )

        try:
            # Update reconciliation using repository
            async with self.get_transaction_context() as session:
                repository = ReconciliationRecordRepository(session)
                reconciliation = await repository.update_reconciliation_status(
                    reconciliation_id=reconciliation_id,
                    status=status,
                    resolution_notes=resolution_notes,
                    resolved_by=resolved_by,
                    **kwargs
                )

                if not reconciliation:
                    raise NotFoundError("ReconciliationRecord", reconciliation_id)

                # Log successful update
                self.log_operation_success(
                    correlation,
                    f"Reconciliation {reconciliation_id} status updated to {status.value}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "reconciliation_status_updated",
                    tags={"status": status.value}
                )

                return reconciliation

        except Exception as e:
            await self.handle_service_error(e, "update_reconciliation_status", {
                "reconciliation_id": reconciliation_id,
                "status": status.value
            })

    async def get_reconciliation_summary(
        self,
        provider: Optional[PaymentProviderType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        status: Optional[ReconciliationStatus] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get reconciliation summary with filtering and aggregation.

        Args:
            provider: Optional provider filter
            date_from: Start date filter
            date_to: End date filter
            status: Optional status filter
            currency: Currency filter

        Returns:
            Reconciliation summary dictionary

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = ReconciliationRecordRepository(session)
                return await repository.get_reconciliation_summary(
                    provider=provider,
                    date_from=date_from,
                    date_to=date_to,
                    status=status,
                    currency=currency
                )

        except Exception as e:
            await self.handle_service_error(e, "get_reconciliation_summary", {
                "provider": provider.value if provider else None,
                "currency": currency
            })

    async def detect_variances(
        self,
        provider: PaymentProviderType,
        period_start: datetime,
        period_end: datetime,
        threshold_percentage: float = 1.0
    ) -> List[Dict[str, Any]]:
        """
        Detect variances in reconciliation records.

        Args:
            provider: Payment provider
            period_start: Period start date
            period_end: Period end date
            threshold_percentage: Variance threshold percentage

        Returns:
            List of detected variances

        Raises:
            ServiceError: If variance detection fails
        """
        try:
            async with self.get_session_context() as session:
                repository = ReconciliationRecordRepository(session)
                return await repository.detect_variances(
                    provider=provider,
                    period_start=period_start,
                    period_end=period_end,
                    threshold_percentage=threshold_percentage
                )

        except Exception as e:
            await self.handle_service_error(e, "detect_variances", {
                "provider": provider.value,
                "threshold_percentage": threshold_percentage
            })

    def _determine_variance_type(self, variance_amount: Decimal, variance_percentage: float) -> Optional[VarianceType]:
        """
        Determine variance type based on amount and percentage.

        Args:
            variance_amount: Variance amount
            variance_percentage: Variance percentage

        Returns:
            Variance type or None if no significant variance
        """
        # Define thresholds
        MINOR_THRESHOLD = 1.0  # 1%
        MAJOR_THRESHOLD = 5.0  # 5%

        abs_percentage = abs(variance_percentage)

        if abs_percentage < MINOR_THRESHOLD:
            return None  # No significant variance
        elif abs_percentage < MAJOR_THRESHOLD:
            return VarianceType.MINOR
        else:
            return VarianceType.MAJOR

    def _determine_reconciliation_status(self, variance_amount: Decimal, variance_percentage: float) -> ReconciliationStatus:
        """
        Determine reconciliation status based on variance.

        Args:
            variance_amount: Variance amount
            variance_percentage: Variance percentage

        Returns:
            Reconciliation status
        """
        # Define thresholds
        AUTO_APPROVE_THRESHOLD = 0.5  # 0.5%

        abs_percentage = abs(variance_percentage)

        if abs_percentage <= AUTO_APPROVE_THRESHOLD:
            return ReconciliationStatus.APPROVED
        else:
            return ReconciliationStatus.PENDING
