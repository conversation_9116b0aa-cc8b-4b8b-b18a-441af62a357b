# Culture Connect Backend - Development Kubernetes Overlay
# Kustomize configuration for development environment deployment

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Base resources to customize
resources:
  - ../../base

# Namespace for development
namespace: culture-connect-dev

# Common labels for all resources
commonLabels:
  environment: development
  deployment-tier: development
  app.kubernetes.io/environment: development

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/environment: development
  config.kubernetes.io/local-config: "true"

# Name prefix for all resources
namePrefix: dev-

# Images to use (development tags)
images:
  - name: ghcr.io/culture-connect/backend
    newTag: dev-latest

# ConfigMap generator for development-specific config
configMapGenerator:
  - name: culture-connect-config
    behavior: merge
    literals:
      - CC_ENVIRONMENT=development
      - CC_DEBUG=true
      - CC_LOG_LEVEL=DEBUG
      - CC_WORKERS=1
      - CC_RELOAD=true
      - CC_ENABLE_DOCS=true
      - CC_ENABLE_REDOC=true
      - CC_ENABLE_PROFILER=true
      - CC_ENABLE_DEBUG_TOOLBAR=true
      - CC_MOCK_EXTERNAL_SERVICES=true
      - CC_SKIP_EMAIL_VERIFICATION=true
      - CC_BYPASS_RATE_LIMITING=true
      - CC_ENABLE_SQL_LOGGING=true

# Secret generator for development secrets
secretGenerator:
  - name: culture-connect-secrets
    behavior: merge
    literals:
      - CC_SECRET_KEY=dev_secret_key_change_in_production_12345678901234567890
      - CC_DATABASE_URL=postgresql+asyncpg://culture_connect_dev:dev_password_123@postgres:5432/culture_connect_dev_db
      - CC_REDIS_URL=redis://redis:6379/0
      - CC_PAYSTACK_SECRET_KEY=sk_test_dev_paystack_secret_key
      - CC_STRIPE_SECRET_KEY=sk_test_dev_stripe_secret_key
      - CC_BUSHA_API_KEY=test_dev_busha_api_key
      - CC_AIML_API_KEY=test_dev_aiml_api_key
      - CC_SENTRY_DSN=https://<EMAIL>/dev_project_id

# Patches for development-specific modifications
patches:
  # Reduce resource requirements for development
  - target:
      kind: Deployment
      name: culture-connect-api
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "256Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "100m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "512Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "250m"

  # Reduce Celery worker resources
  - target:
      kind: Deployment
      name: culture-connect-celery-worker
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "128Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "50m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "256Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "125m"

  # Disable HPA for development
  - target:
      kind: HorizontalPodAutoscaler
      name: culture-connect-api-hpa
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 1
      - op: replace
        path: /spec/maxReplicas
        value: 2

  # Development ingress configuration
  - target:
      kind: Ingress
      name: culture-connect-ingress
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: dev-api.localhost
      - op: remove
        path: /spec/tls

# Replace strategy for development (faster deployments)
replicas:
  - name: culture-connect-api
    count: 1
  - name: culture-connect-celery-worker
    count: 1
  - name: culture-connect-celery-beat
    count: 1
