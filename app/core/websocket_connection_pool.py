"""
Production-grade WebSocket Connection Pool for Culture Connect Backend API.

This module provides advanced connection pooling infrastructure for high-traffic scenarios including:
- Connection pool optimization with configurable limits and health monitoring
- Automatic connection cleanup and resource management for stale connections
- Load balancing across multiple WebSocket connections per user
- Memory usage optimization with connection lifecycle tracking

Implements Task 6.1.1 Phase 8 requirements for production optimization with:
- Advanced connection pooling supporting >10,000 concurrent connections
- Resource optimization with automatic cleanup and health monitoring
- Load balancing and connection distribution for optimal performance
- Memory usage tracking and optimization for production deployment

Performance targets: >10,000 concurrent connections, <100ms connection establishment, >99.9% reliability
"""

import asyncio
import logging
import time
import weakref
from datetime import datetime, timezone, timedelta
from typing import Dict, Set, Optional, List, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from uuid import uuid4
from collections import defaultdict, deque

from fastapi import WebSocket
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import correlation_id
from app.core.cache import CacheManager
from app.models.user import User

logger = logging.getLogger(__name__)


class ConnectionState(str, Enum):
    """WebSocket connection states."""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    IDLE = "idle"
    ACTIVE = "active"
    STALE = "stale"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"


class PoolHealthStatus(str, Enum):
    """Connection pool health status."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DEGRADED = "degraded"


@dataclass
class ConnectionMetrics:
    """Connection performance metrics."""
    connection_id: str
    user_id: int
    established_at: datetime
    last_activity: datetime
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    ping_latency: float = 0.0
    state: ConnectionState = ConnectionState.CONNECTED
    error_count: int = 0
    last_error: Optional[str] = None


@dataclass
class PoolConfiguration:
    """Connection pool configuration."""
    max_connections: int = 10000
    max_connections_per_user: int = 5
    connection_timeout: int = 300  # 5 minutes
    idle_timeout: int = 600  # 10 minutes
    stale_connection_threshold: int = 900  # 15 minutes
    cleanup_interval: int = 60  # 1 minute
    health_check_interval: int = 30  # 30 seconds
    ping_interval: int = 30  # 30 seconds
    max_message_queue_size: int = 1000
    memory_threshold_mb: int = 1024  # 1GB
    enable_load_balancing: bool = True
    enable_auto_scaling: bool = True


class AdvancedWebSocketConnectionPool:
    """
    Production-grade WebSocket connection pool with advanced features.

    Provides comprehensive connection management including:
    - Connection pool optimization with configurable limits
    - Automatic connection cleanup and resource management
    - Load balancing across multiple connections per user
    - Memory usage optimization and health monitoring
    - Real-time metrics collection and performance tracking
    """

    def __init__(self, config: Optional[PoolConfiguration] = None, cache_manager: Optional[CacheManager] = None):
        self.config = config or PoolConfiguration()
        self.cache = cache_manager

        # Connection storage
        self.connections: Dict[str, WebSocket] = {}
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        self.user_connections: Dict[int, Set[str]] = defaultdict(set)
        self.room_connections: Dict[int, Set[str]] = defaultdict(set)

        # Connection queues for load balancing
        self.connection_queues: Dict[int, deque] = defaultdict(deque)
        self.message_queues: Dict[str, asyncio.Queue] = {}

        # Health monitoring
        self.pool_metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "idle_connections": 0,
            "stale_connections": 0,
            "failed_connections": 0,
            "memory_usage_mb": 0,
            "average_latency": 0.0,
            "messages_per_second": 0,
            "connection_errors": 0
        }

        # Background tasks
        self._background_tasks: Set[asyncio.Task] = set()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._metrics_task: Optional[asyncio.Task] = None

        # Performance tracking
        self._message_count_window = deque(maxlen=60)  # 1 minute window
        self._last_metrics_update = time.time()

        # Background tasks will be started when needed
        self._tasks_started = False

    async def _start_background_tasks(self):
        """Start background monitoring and cleanup tasks."""
        if self._tasks_started:
            return

        self._cleanup_task = asyncio.create_task(self._cleanup_stale_connections())
        self._health_monitor_task = asyncio.create_task(self._monitor_pool_health())
        self._metrics_task = asyncio.create_task(self._update_metrics())

        self._background_tasks.update([
            self._cleanup_task,
            self._health_monitor_task,
            self._metrics_task
        ])

        self._tasks_started = True

    async def add_connection(
        self,
        connection_id: str,
        websocket: WebSocket,
        user_id: int,
        user: User
    ) -> bool:
        """
        Add connection to pool with load balancing and resource optimization.

        Performance target: <100ms connection establishment.
        """
        start_time = time.time()

        # Start background tasks if not already started
        await self._start_background_tasks()

        try:
            # Check pool capacity
            if len(self.connections) >= self.config.max_connections:
                logger.warning(f"Connection pool at capacity ({self.config.max_connections})")
                return False

            # Check per-user connection limit
            if len(self.user_connections[user_id]) >= self.config.max_connections_per_user:
                # Remove oldest connection for this user
                await self._remove_oldest_user_connection(user_id)

            # Add connection
            self.connections[connection_id] = websocket
            self.user_connections[user_id].add(connection_id)

            # Initialize connection metrics
            self.connection_metrics[connection_id] = ConnectionMetrics(
                connection_id=connection_id,
                user_id=user_id,
                established_at=datetime.now(timezone.utc),
                last_activity=datetime.now(timezone.utc),
                state=ConnectionState.CONNECTED
            )

            # Initialize message queue
            self.message_queues[connection_id] = asyncio.Queue(
                maxsize=self.config.max_message_queue_size
            )

            # Add to load balancing queue
            if self.config.enable_load_balancing:
                self.connection_queues[user_id].append(connection_id)

            # Update metrics
            self.pool_metrics["total_connections"] += 1
            self.pool_metrics["active_connections"] = len(self.connections)

            establishment_time = (time.time() - start_time) * 1000
            logger.info(f"Connection {connection_id} added to pool in {establishment_time:.2f}ms")

            return True

        except Exception as e:
            logger.error(f"Failed to add connection {connection_id} to pool: {str(e)}")
            self.pool_metrics["connection_errors"] += 1
            return False

    async def remove_connection(self, connection_id: str) -> bool:
        """Remove connection from pool with cleanup."""
        try:
            if connection_id not in self.connections:
                return False

            # Get connection metrics
            metrics = self.connection_metrics.get(connection_id)
            if metrics:
                user_id = metrics.user_id

                # Remove from user connections
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]

                # Remove from load balancing queue
                if user_id in self.connection_queues:
                    try:
                        self.connection_queues[user_id].remove(connection_id)
                    except ValueError:
                        pass  # Connection not in queue

            # Remove from room connections
            for room_id, room_connections in self.room_connections.items():
                room_connections.discard(connection_id)

            # Cleanup resources
            del self.connections[connection_id]
            if connection_id in self.connection_metrics:
                del self.connection_metrics[connection_id]
            if connection_id in self.message_queues:
                del self.message_queues[connection_id]

            # Update metrics
            self.pool_metrics["active_connections"] = len(self.connections)

            logger.debug(f"Connection {connection_id} removed from pool")
            return True

        except Exception as e:
            logger.error(f"Failed to remove connection {connection_id}: {str(e)}")
            return False

    async def get_user_connections(self, user_id: int) -> List[str]:
        """Get all active connections for a user."""
        return list(self.user_connections.get(user_id, set()))

    async def get_load_balanced_connection(self, user_id: int) -> Optional[str]:
        """Get optimal connection for user using load balancing."""
        if not self.config.enable_load_balancing:
            # Return first available connection
            user_connections = self.user_connections.get(user_id, set())
            return next(iter(user_connections)) if user_connections else None

        # Round-robin load balancing
        queue = self.connection_queues.get(user_id)
        if not queue:
            return None

        # Rotate to next connection
        connection_id = queue.popleft()
        queue.append(connection_id)

        # Verify connection is still active
        if connection_id in self.connections:
            return connection_id
        else:
            # Remove stale connection from queue
            try:
                queue.remove(connection_id)
            except ValueError:
                pass
            return await self.get_load_balanced_connection(user_id)  # Retry

    async def _remove_oldest_user_connection(self, user_id: int):
        """Remove oldest connection for user to make room for new one."""
        user_connections = self.user_connections.get(user_id, set())
        if not user_connections:
            return

        # Find oldest connection
        oldest_connection = None
        oldest_time = datetime.now(timezone.utc)

        for connection_id in user_connections:
            metrics = self.connection_metrics.get(connection_id)
            if metrics and metrics.established_at < oldest_time:
                oldest_time = metrics.established_at
                oldest_connection = connection_id

        if oldest_connection:
            # Close and remove oldest connection
            websocket = self.connections.get(oldest_connection)
            if websocket:
                try:
                    await websocket.close(code=1000, reason="Connection limit exceeded")
                except:
                    pass  # Connection may already be closed

            await self.remove_connection(oldest_connection)
            logger.info(f"Removed oldest connection {oldest_connection} for user {user_id}")

    async def _cleanup_stale_connections(self):
        """Background task to cleanup stale and idle connections."""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)

                current_time = datetime.now(timezone.utc)
                stale_connections = []
                idle_connections = []

                for connection_id, metrics in self.connection_metrics.items():
                    # Check for stale connections
                    time_since_activity = (current_time - metrics.last_activity).total_seconds()

                    if time_since_activity > self.config.stale_connection_threshold:
                        stale_connections.append(connection_id)
                        metrics.state = ConnectionState.STALE
                    elif time_since_activity > self.config.idle_timeout:
                        idle_connections.append(connection_id)
                        metrics.state = ConnectionState.IDLE
                    else:
                        metrics.state = ConnectionState.ACTIVE

                # Cleanup stale connections
                for connection_id in stale_connections:
                    websocket = self.connections.get(connection_id)
                    if websocket:
                        try:
                            await websocket.close(code=1001, reason="Connection stale")
                        except:
                            pass  # Connection may already be closed

                    await self.remove_connection(connection_id)

                # Update metrics
                self.pool_metrics["stale_connections"] = len(stale_connections)
                self.pool_metrics["idle_connections"] = len(idle_connections)

                if stale_connections:
                    logger.info(f"Cleaned up {len(stale_connections)} stale connections")

            except Exception as e:
                logger.error(f"Error in connection cleanup: {str(e)}")

    async def _monitor_pool_health(self):
        """Background task to monitor pool health and performance."""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)

                # Calculate health metrics
                total_connections = len(self.connections)
                memory_usage = await self._calculate_memory_usage()
                average_latency = await self._calculate_average_latency()

                # Update pool metrics
                self.pool_metrics.update({
                    "active_connections": total_connections,
                    "memory_usage_mb": memory_usage,
                    "average_latency": average_latency
                })

                # Determine health status
                health_status = self._determine_health_status()

                # Log health status if not healthy
                if health_status != PoolHealthStatus.HEALTHY:
                    logger.warning(f"Connection pool health: {health_status.value}")
                    logger.warning(f"Pool metrics: {self.pool_metrics}")

                # Trigger auto-scaling if enabled
                if self.config.enable_auto_scaling:
                    await self._handle_auto_scaling(health_status)

            except Exception as e:
                logger.error(f"Error in health monitoring: {str(e)}")

    async def _update_metrics(self):
        """Background task to update performance metrics."""
        while True:
            try:
                await asyncio.sleep(1)  # Update every second

                current_time = time.time()

                # Calculate messages per second
                if len(self._message_count_window) > 0:
                    messages_per_second = sum(self._message_count_window) / len(self._message_count_window)
                    self.pool_metrics["messages_per_second"] = messages_per_second

                # Add current message count to window
                current_messages = sum(
                    metrics.messages_sent + metrics.messages_received
                    for metrics in self.connection_metrics.values()
                )
                self._message_count_window.append(current_messages)

                self._last_metrics_update = current_time

            except Exception as e:
                logger.error(f"Error updating metrics: {str(e)}")

    async def _calculate_memory_usage(self) -> float:
        """Calculate approximate memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB
        except ImportError:
            # Estimate based on connection count
            return len(self.connections) * 0.1  # Rough estimate: 100KB per connection

    async def _calculate_average_latency(self) -> float:
        """Calculate average ping latency across all connections."""
        latencies = [
            metrics.ping_latency
            for metrics in self.connection_metrics.values()
            if metrics.ping_latency > 0
        ]

        if latencies:
            return sum(latencies) / len(latencies)
        return 0.0

    def _determine_health_status(self) -> PoolHealthStatus:
        """Determine overall pool health status."""
        total_connections = self.pool_metrics["active_connections"]
        memory_usage = self.pool_metrics["memory_usage_mb"]
        error_rate = self.pool_metrics["connection_errors"] / max(total_connections, 1)

        # Critical conditions
        if (total_connections >= self.config.max_connections * 0.95 or
            memory_usage >= self.config.memory_threshold_mb or
            error_rate > 0.1):
            return PoolHealthStatus.CRITICAL

        # Warning conditions
        if (total_connections >= self.config.max_connections * 0.8 or
            memory_usage >= self.config.memory_threshold_mb * 0.8 or
            error_rate > 0.05):
            return PoolHealthStatus.WARNING

        # Degraded conditions
        if (total_connections >= self.config.max_connections * 0.6 or
            self.pool_metrics["average_latency"] > 200):
            return PoolHealthStatus.DEGRADED

        return PoolHealthStatus.HEALTHY

    async def _handle_auto_scaling(self, health_status: PoolHealthStatus):
        """Handle auto-scaling based on health status."""
        if health_status == PoolHealthStatus.CRITICAL:
            # Implement emergency measures
            await self._emergency_connection_cleanup()
        elif health_status == PoolHealthStatus.WARNING:
            # Proactive cleanup of idle connections
            await self._cleanup_idle_connections()

    async def _emergency_connection_cleanup(self):
        """Emergency cleanup of connections to free resources."""
        # Remove idle connections first
        idle_connections = [
            connection_id for connection_id, metrics in self.connection_metrics.items()
            if metrics.state == ConnectionState.IDLE
        ]

        for connection_id in idle_connections[:len(idle_connections)//2]:  # Remove half
            websocket = self.connections.get(connection_id)
            if websocket:
                try:
                    await websocket.close(code=1001, reason="Emergency cleanup")
                except:
                    pass
            await self.remove_connection(connection_id)

        logger.warning(f"Emergency cleanup removed {len(idle_connections)//2} idle connections")

    async def _cleanup_idle_connections(self):
        """Cleanup idle connections proactively."""
        current_time = datetime.now(timezone.utc)
        idle_connections = []

        for connection_id, metrics in self.connection_metrics.items():
            time_since_activity = (current_time - metrics.last_activity).total_seconds()
            if time_since_activity > self.config.idle_timeout:
                idle_connections.append(connection_id)

        # Remove oldest idle connections
        for connection_id in idle_connections[:10]:  # Limit to 10 at a time
            websocket = self.connections.get(connection_id)
            if websocket:
                try:
                    await websocket.close(code=1000, reason="Idle timeout")
                except:
                    pass
            await self.remove_connection(connection_id)

    async def get_pool_status(self) -> Dict[str, Any]:
        """Get comprehensive pool status and metrics."""
        return {
            "health_status": self._determine_health_status().value,
            "metrics": self.pool_metrics.copy(),
            "configuration": {
                "max_connections": self.config.max_connections,
                "max_connections_per_user": self.config.max_connections_per_user,
                "connection_timeout": self.config.connection_timeout,
                "idle_timeout": self.config.idle_timeout,
                "memory_threshold_mb": self.config.memory_threshold_mb
            },
            "connection_states": {
                state.value: sum(
                    1 for metrics in self.connection_metrics.values()
                    if metrics.state == state
                )
                for state in ConnectionState
            }
        }

    async def shutdown(self):
        """Graceful shutdown of connection pool."""
        logger.info("Shutting down WebSocket connection pool...")

        # Cancel background tasks
        for task in self._background_tasks:
            task.cancel()

        # Close all connections gracefully
        close_tasks = []
        for connection_id, websocket in self.connections.items():
            try:
                close_tasks.append(websocket.close(code=1001, reason="Server shutdown"))
            except:
                pass

        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)

        # Clear all data structures
        self.connections.clear()
        self.connection_metrics.clear()
        self.user_connections.clear()
        self.room_connections.clear()
        self.connection_queues.clear()
        self.message_queues.clear()

        logger.info("WebSocket connection pool shutdown complete")


# Global connection pool instance - will be created when needed
connection_pool = None


def get_connection_pool(config: Optional[PoolConfiguration] = None, cache_manager: Optional[CacheManager] = None) -> AdvancedWebSocketConnectionPool:
    """Get or create global connection pool instance."""
    global connection_pool
    if connection_pool is None:
        connection_pool = AdvancedWebSocketConnectionPool(config, cache_manager)
    return connection_pool
