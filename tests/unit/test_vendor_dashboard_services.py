"""
Unit Tests for Vendor Dashboard Services.

This module provides comprehensive unit tests for vendor dashboard service layer including:
- VendorDashboardService business logic validation
- Metrics calculation and aggregation testing
- Activity feed management testing
- Notification system testing
- Quick actions workflow testing
- Analytics data processing testing

Implements >80% test coverage requirement with pytest-asyncio patterns,
comprehensive error handling validation, and performance testing.
"""

import pytest
import asyncio
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any
from uuid import uuid4

from app.services.vendor_dashboard_service import VendorDashboardService
from app.models.vendor import Vendor
from app.models.vendor_dashboard import (
    VendorDashboardMetrics,
    VendorActivityFeed,
    VendorNotification,
    VendorQuickAction
)
from app.models.analytics_models import VendorAnalytics
from app.schemas.vendor_dashboard import VendorDashboardOverview


class TestVendorDashboardService:
    """Test suite for VendorDashboardService."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def mock_vendor_repo(self):
        """Mock vendor repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_dashboard_repo(self):
        """Mock vendor dashboard repository."""
        return AsyncMock()

    @pytest.fixture
    def vendor_dashboard_service(self, mock_db, mock_vendor_repo, mock_dashboard_repo):
        """Create VendorDashboardService instance with mocked dependencies."""
        service = VendorDashboardService()
        service.db = mock_db
        service.vendor_repo = mock_vendor_repo
        service.dashboard_repo = mock_dashboard_repo
        return service

    @pytest.fixture
    def sample_vendor(self):
        """Sample vendor for testing."""
        vendor_id = uuid4()
        return Vendor(
            id=vendor_id,
            user_id=uuid4(),
            business_name="Test Cultural Vendor",
            business_type="photography",
            verification_status="verified",
            average_rating=Decimal("4.5"),
            total_reviews=25,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    @pytest.fixture
    def sample_metrics(self, sample_vendor):
        """Sample metrics data for testing."""
        vendor_id = sample_vendor.id  # Use the same vendor_id as the vendor fixture
        now = datetime.now(timezone.utc)
        return [
            VendorDashboardMetrics(
                id=uuid4(),
                vendor_id=vendor_id,
                metric_type="revenue",
                metric_name="total_revenue",
                current_value=150000.00,
                previous_value=120000.00,
                percentage_change=25.0,
                period_start=now - timedelta(days=30),
                period_end=now,
                trend_direction="up",
                metric_data={},
                breakdown_data={},
                performance_score=85.0,
                benchmark_comparison=15.0,
                calculated_at=now,
                is_active=True
            ),
            VendorDashboardMetrics(
                id=uuid4(),
                vendor_id=vendor_id,
                metric_type="bookings",
                metric_name="total_bookings",
                current_value=45.0,
                previous_value=38.0,
                percentage_change=18.4,
                period_start=now - timedelta(days=30),
                period_end=now,
                trend_direction="up",
                metric_data={},
                breakdown_data={},
                performance_score=78.0,
                benchmark_comparison=8.0,
                calculated_at=now,
                is_active=True
            )
        ]

    @pytest.mark.asyncio
    async def test_get_dashboard_overview_success(
        self,
        vendor_dashboard_service,
        sample_vendor,
        sample_metrics
    ):
        """Test successful dashboard overview retrieval."""
        # Arrange
        vendor_id = sample_vendor.id  # Use the same UUID from the fixture
        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.get_vendor_metrics.return_value = sample_metrics
        vendor_dashboard_service.dashboard_repo.get_recent_activities.return_value = []
        vendor_dashboard_service.dashboard_repo.get_unread_notifications.return_value = []
        vendor_dashboard_service.dashboard_repo.get_pending_actions.return_value = []

        # Act
        result = await vendor_dashboard_service.get_dashboard_overview(vendor_id)

        # Assert
        assert result is not None
        assert isinstance(result, VendorDashboardOverview)
        assert result.vendor_id == vendor_id
        assert len(result.key_metrics) == 2
        assert result.key_metrics[0].metric_type == "revenue"
        assert result.key_metrics[1].metric_type == "bookings"

        # Verify repository calls
        vendor_dashboard_service.vendor_repo.get_by_id.assert_called_once_with(vendor_id)
        vendor_dashboard_service.dashboard_repo.get_vendor_metrics.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_dashboard_overview_vendor_not_found(self, vendor_dashboard_service):
        """Test dashboard overview when vendor not found."""
        # Arrange
        vendor_id = 999
        vendor_dashboard_service.vendor_repo.get_by_id.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="Vendor not found"):
            await vendor_dashboard_service.get_dashboard_overview(vendor_id)

    @pytest.mark.asyncio
    async def test_calculate_metrics_success(self, vendor_dashboard_service, sample_vendor):
        """Test successful metrics calculation."""
        # Arrange
        vendor_id = 1
        period_start = datetime.now(timezone.utc) - timedelta(days=30)
        period_end = datetime.now(timezone.utc)

        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.calculate_revenue_metrics.return_value = {
            "total_revenue": Decimal("150000.00"),
            "average_order_value": Decimal("3333.33"),
            "revenue_growth_rate": 25.0
        }
        vendor_dashboard_service.dashboard_repo.calculate_booking_metrics.return_value = {
            "total_bookings": 45,
            "confirmed_bookings": 42,
            "booking_conversion_rate": 93.3
        }

        # Act
        result = await vendor_dashboard_service.calculate_metrics(
            vendor_id, period_start, period_end
        )

        # Assert
        assert result is not None
        assert "revenue_metrics" in result
        assert "booking_metrics" in result
        assert result["revenue_metrics"]["total_revenue"] == Decimal("150000.00")
        assert result["booking_metrics"]["total_bookings"] == 45

    @pytest.mark.asyncio
    async def test_create_activity_feed_entry_success(self, vendor_dashboard_service, sample_vendor):
        """Test successful activity feed entry creation."""
        # Arrange
        vendor_id = 1
        activity_data = {
            "activity_type": "booking_received",
            "title": "New booking received",
            "description": "Photography session booked for tomorrow",
            "entity_type": "booking",
            "entity_id": 123,
            "is_important": True
        }

        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.create_activity.return_value = VendorActivityFeed(
            id=1,
            vendor_id=vendor_id,
            **activity_data,
            created_at=datetime.now(timezone.utc)
        )

        # Act
        result = await vendor_dashboard_service.create_activity_feed_entry(vendor_id, activity_data)

        # Assert
        assert result is not None
        assert result.vendor_id == vendor_id
        assert result.activity_type == "booking_received"
        assert result.is_important is True

    @pytest.mark.asyncio
    async def test_send_notification_success(self, vendor_dashboard_service, sample_vendor):
        """Test successful notification sending."""
        # Arrange
        vendor_id = 1
        notification_data = {
            "notification_type": "booking_request",
            "priority": "high",
            "title": "New Booking Request",
            "message": "You have a new booking request for photography session",
            "action_url": "/bookings/123",
            "action_text": "View Booking"
        }

        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.create_notification.return_value = VendorNotification(
            id=1,
            vendor_id=vendor_id,
            **notification_data,
            created_at=datetime.now(timezone.utc)
        )

        # Act
        result = await vendor_dashboard_service.send_notification(vendor_id, notification_data)

        # Assert
        assert result is not None
        assert result.vendor_id == vendor_id
        assert result.notification_type == "booking_request"
        assert result.priority == "high"

    @pytest.mark.asyncio
    async def test_create_quick_action_success(self, vendor_dashboard_service, sample_vendor):
        """Test successful quick action creation."""
        # Arrange
        vendor_id = 1
        action_data = {
            "action_type": "complete_profile",
            "title": "Complete Your Profile",
            "description": "Add more details to improve your visibility",
            "action_url": "/profile/edit",
            "priority_score": 8.5,
            "estimated_time_minutes": 15,
            "potential_impact": "high"
        }

        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.create_quick_action.return_value = VendorQuickAction(
            id=1,
            vendor_id=vendor_id,
            **action_data,
            created_at=datetime.now(timezone.utc)
        )

        # Act
        result = await vendor_dashboard_service.create_quick_action(vendor_id, action_data)

        # Assert
        assert result is not None
        assert result.vendor_id == vendor_id
        assert result.action_type == "complete_profile"
        assert result.priority_score == 8.5

    @pytest.mark.asyncio
    async def test_generate_analytics_report_success(self, vendor_dashboard_service, sample_vendor):
        """Test successful analytics report generation."""
        # Arrange
        vendor_id = 1
        period_start = datetime.now(timezone.utc) - timedelta(days=30)
        period_end = datetime.now(timezone.utc)
        analytics_type = "monthly"

        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.generate_analytics.return_value = VendorAnalytics(
            id=1,
            vendor_id=vendor_id,
            analytics_type=analytics_type,
            period_start=period_start,
            period_end=period_end,
            total_revenue=Decimal("150000.00"),
            total_bookings=45,
            average_rating=4.5,
            created_at=datetime.now(timezone.utc)
        )

        # Act
        result = await vendor_dashboard_service.generate_analytics_report(
            vendor_id, period_start, period_end, analytics_type
        )

        # Assert
        assert result is not None
        assert result.vendor_id == vendor_id
        assert result.analytics_type == analytics_type
        assert result.total_revenue == Decimal("150000.00")
        assert result.total_bookings == 45

    @pytest.mark.asyncio
    async def test_mark_notification_as_read_success(self, vendor_dashboard_service):
        """Test successful notification marking as read."""
        # Arrange
        vendor_id = 1
        notification_id = 1

        vendor_dashboard_service.dashboard_repo.mark_notification_read.return_value = True

        # Act
        result = await vendor_dashboard_service.mark_notification_as_read(vendor_id, notification_id)

        # Assert
        assert result is True
        vendor_dashboard_service.dashboard_repo.mark_notification_read.assert_called_once_with(
            vendor_id, notification_id
        )

    @pytest.mark.asyncio
    async def test_complete_quick_action_success(self, vendor_dashboard_service):
        """Test successful quick action completion."""
        # Arrange
        vendor_id = 1
        action_id = 1

        vendor_dashboard_service.dashboard_repo.complete_quick_action.return_value = True

        # Act
        result = await vendor_dashboard_service.complete_quick_action(vendor_id, action_id)

        # Assert
        assert result is True
        vendor_dashboard_service.dashboard_repo.complete_quick_action.assert_called_once_with(
            vendor_id, action_id
        )

    @pytest.mark.asyncio
    async def test_service_error_handling(self, vendor_dashboard_service):
        """Test service error handling."""
        # Arrange
        vendor_id = 1
        vendor_dashboard_service.vendor_repo.get_by_id.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception):
            await vendor_dashboard_service.get_dashboard_overview(vendor_id)

    @pytest.mark.asyncio
    async def test_performance_metrics_calculation(self, vendor_dashboard_service, sample_vendor):
        """Test performance metrics calculation within target times."""
        # Arrange
        vendor_id = 1
        vendor_dashboard_service.vendor_repo.get_by_id.return_value = sample_vendor
        vendor_dashboard_service.dashboard_repo.get_vendor_metrics.return_value = []
        vendor_dashboard_service.dashboard_repo.get_recent_activities.return_value = []
        vendor_dashboard_service.dashboard_repo.get_unread_notifications.return_value = []
        vendor_dashboard_service.dashboard_repo.get_pending_actions.return_value = []

        # Act
        start_time = datetime.now(timezone.utc)
        result = await vendor_dashboard_service.get_dashboard_overview(vendor_id)
        end_time = datetime.now(timezone.utc)

        # Assert
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        assert execution_time_ms < 500  # Target: <500ms for dashboard operations
        assert result is not None
