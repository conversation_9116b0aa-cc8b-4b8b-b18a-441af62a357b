"""
Unit tests for email repositories.

This module provides comprehensive unit tests for email-related repository classes:
- EmailTemplateRepository: Template CRUD operations and queries
- EmailDeliveryRepository: Delivery tracking and analytics
- EmailPreferenceRepository: User preference management
- EmailQueueRepository: Queue management and processing

Implements Task 2.3.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.repositories.email_repository import (
    EmailTemplateRepository, EmailDeliveryRepository,
    EmailPreferenceRepository, EmailQueueRepository,
    NotFoundError, ValidationError, ConflictError
)
from app.models.email_models import (
    EmailTemplate, EmailDelivery, EmailPreference, EmailQueue,
    EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
)


@pytest.fixture
def mock_session():
    """Create a mock async session."""
    session = AsyncMock(spec=AsyncSession)
    return session


@pytest.fixture
def email_template_repo(mock_session):
    """Create EmailTemplateRepository with mock session."""
    return EmailTemplateRepository(mock_session)


@pytest.fixture
def email_delivery_repo(mock_session):
    """Create EmailDeliveryRepository with mock session."""
    return EmailDeliveryRepository(mock_session)


@pytest.fixture
def email_preference_repo(mock_session):
    """Create EmailPreferenceRepository with mock session."""
    return EmailPreferenceRepository(mock_session)


@pytest.fixture
def email_queue_repo(mock_session):
    """Create EmailQueueRepository with mock session."""
    return EmailQueueRepository(mock_session)


@pytest.fixture
def sample_template():
    """Create a sample email template."""
    template = MagicMock(spec=EmailTemplate)
    template.id = uuid4()
    template.name = "welcome_email"
    template.category = EmailTemplateCategory.NOTIFICATION
    template.subject_template = "Welcome {{user_name}}!"
    template.body_template = "Hello {{user_name}}, welcome!"
    template.variables = {"user_name": "string"}
    template.version = 1
    template.is_active = True
    template.created_by = 1
    template.created_at = datetime.now(timezone.utc)
    template.updated_at = datetime.now(timezone.utc)
    return template


@pytest.fixture
def sample_delivery():
    """Create a sample email delivery."""
    delivery = MagicMock(spec=EmailDelivery)
    delivery.id = uuid4()
    delivery.user_id = 1
    delivery.recipient_email = "<EMAIL>"
    delivery.subject = "Test Subject"
    delivery.body = "Test Body"
    delivery.status = EmailDeliveryStatus.PENDING
    delivery.priority = 3
    delivery.created_at = datetime.now(timezone.utc)
    delivery.updated_at = datetime.now(timezone.utc)
    return delivery


@pytest.fixture
def sample_preference():
    """Create a sample email preference."""
    preference = MagicMock(spec=EmailPreference)
    preference.user_id = 1
    preference.marketing_emails = True
    preference.booking_notifications = True
    preference.security_emails = True
    preference.verification_emails = True
    preference.system_notifications = False
    preference.created_at = datetime.now(timezone.utc)
    preference.updated_at = datetime.now(timezone.utc)
    return preference


@pytest.fixture
def sample_queue_item():
    """Create a sample email queue item."""
    queue_item = MagicMock(spec=EmailQueue)
    queue_item.id = uuid4()
    queue_item.user_id = 1
    queue_item.recipient_email = "<EMAIL>"
    queue_item.subject = "Queued Email"
    queue_item.body = "Queued Body"
    queue_item.priority = 1
    queue_item.status = EmailQueueStatus.QUEUED
    queue_item.retry_count = 0
    queue_item.max_retries = 3
    queue_item.created_at = datetime.now(timezone.utc)
    queue_item.scheduled_at = datetime.now(timezone.utc)
    return queue_item


class TestEmailTemplateRepository:
    """Test EmailTemplateRepository."""

    @pytest.mark.asyncio
    async def test_create_template_success(self, email_template_repo, mock_session):
        """Test successful template creation."""
        from app.schemas.email_schemas import EmailTemplateCreate

        template_data = EmailTemplateCreate(
            name="test_template",
            category=EmailTemplateCategory.VERIFICATION,
            subject_template="Test Subject",
            body_template="Test Body"
        )

        # Mock the template creation
        created_template = MagicMock(spec=EmailTemplate)
        created_template.name = "test_template"
        created_template.category = EmailTemplateCategory.VERIFICATION
        created_template.id = uuid4()

        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.refresh = MagicMock()

        with patch.object(EmailTemplate, '__new__', return_value=created_template):
            result = email_template_repo.create_template(template_data, created_by=1)

        assert result.name == "test_template"
        assert result.category == EmailTemplateCategory.VERIFICATION
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_template_by_id_success(self, email_template_repo, mock_session, sample_template):
        """Test successful template retrieval by ID."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = sample_template
        mock_session.query.return_value.filter.return_value = mock_result

        result = email_template_repo.get(sample_template.id)

        assert result == sample_template
        mock_session.query.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_template_by_id_not_found(self, email_template_repo, mock_session):
        """Test template retrieval when not found."""
        # Mock database query returning None
        mock_result = MagicMock()
        mock_result.first.return_value = None
        mock_session.query.return_value.filter.return_value = mock_result

        result = email_template_repo.get(uuid4())

        assert result is None
        mock_session.query.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_template_by_name_and_version(self, email_template_repo, mock_session, sample_template):
        """Test template retrieval by name and version."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_session.execute.return_value = mock_result

        result = await email_template_repo.get_template_by_name_and_version(
            sample_template.name, sample_template.version
        )

        assert result == sample_template
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_templates_with_filters(self, email_template_repo, mock_session, sample_template):
        """Test template listing with filters."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_template]
        mock_session.execute.return_value = mock_result

        # Mock count query
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_count_result

        templates, total = await email_template_repo.list_templates(
            category=EmailTemplateCategory.NOTIFICATION,
            is_active=True,
            skip=0,
            limit=10
        )

        assert len(templates) == 1
        assert templates[0] == sample_template
        assert total == 1

    @pytest.mark.asyncio
    async def test_update_template_success(self, email_template_repo, mock_session, sample_template):
        """Test successful template update."""
        update_data = {"subject_template": "Updated Subject"}

        # Mock template retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()

        result = await email_template_repo.update_template(sample_template.id, update_data)

        assert result.subject_template == "Updated Subject"
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_template_success(self, email_template_repo, mock_session, sample_template):
        """Test successful template deletion (soft delete)."""
        # Mock template retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_template
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        result = await email_template_repo.delete_template(sample_template.id)

        assert result is True
        assert sample_template.is_active is False
        mock_session.commit.assert_called_once()


class TestEmailDeliveryRepository:
    """Test EmailDeliveryRepository."""

    @pytest.mark.asyncio
    async def test_create_delivery_success(self, email_delivery_repo, mock_session):
        """Test successful delivery creation."""
        # Mock the delivery creation
        created_delivery = MagicMock(spec=EmailDelivery)
        created_delivery.recipient_email = "<EMAIL>"
        created_delivery.subject = "Test Subject"
        created_delivery.id = uuid4()

        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.refresh = MagicMock()

        with patch.object(EmailDelivery, '__new__', return_value=created_delivery):
            result = email_delivery_repo.create_delivery_record(
                user_id=1,
                template_id=None,
                recipient_email="<EMAIL>",
                subject="Test Subject"
            )

        assert result.recipient_email == "<EMAIL>"
        assert result.subject == "Test Subject"
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_delivery_by_id_success(self, email_delivery_repo, mock_session, sample_delivery):
        """Test successful delivery retrieval by ID."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_delivery
        mock_session.execute.return_value = mock_result

        result = await email_delivery_repo.get_delivery_by_id(sample_delivery.id)

        assert result == sample_delivery
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_delivery_status_success(self, email_delivery_repo, mock_session, sample_delivery):
        """Test successful delivery status update."""
        # Mock delivery retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_delivery
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        result = await email_delivery_repo.update_delivery_status(
            sample_delivery.id,
            EmailDeliveryStatus.SENT
        )

        assert result.status == EmailDeliveryStatus.SENT
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_deliveries_with_filters(self, email_delivery_repo, mock_session, sample_delivery):
        """Test delivery listing with filters."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_delivery]
        mock_session.execute.return_value = mock_result

        # Mock count query
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_count_result

        deliveries, total = await email_delivery_repo.list_deliveries(
            user_id=1,
            status=EmailDeliveryStatus.PENDING,
            skip=0,
            limit=10
        )

        assert len(deliveries) == 1
        assert deliveries[0] == sample_delivery
        assert total == 1

    @pytest.mark.asyncio
    async def test_get_delivery_analytics(self, email_delivery_repo, mock_session):
        """Test delivery analytics retrieval."""
        from datetime import datetime, timedelta

        # Mock analytics query results
        mock_session.query.return_value.filter.return_value.count.return_value = 100
        mock_session.query.return_value.filter.return_value.group_by.return_value.all.return_value = [
            (EmailDeliveryStatus.DELIVERED, 95),
            (EmailDeliveryStatus.FAILED, 5)
        ]

        start_date = datetime.utcnow() - timedelta(days=30)
        end_date = datetime.utcnow()

        analytics = email_delivery_repo.get_delivery_analytics(start_date=start_date, end_date=end_date)

        assert analytics["total_sent"] == 100
        assert analytics["status_breakdown"][EmailDeliveryStatus.DELIVERED] == 95
        assert analytics["status_breakdown"][EmailDeliveryStatus.FAILED] == 5


class TestEmailPreferenceRepository:
    """Test EmailPreferenceRepository."""

    @pytest.mark.asyncio
    async def test_get_user_preferences_success(self, email_preference_repo, mock_session, sample_preference):
        """Test successful user preferences retrieval."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.first.return_value = sample_preference
        mock_session.query.return_value.filter.return_value = mock_result

        result = email_preference_repo.get_user_preferences(1)

        assert result == sample_preference
        mock_session.query.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_user_preferences_success(self, email_preference_repo, mock_session):
        """Test successful user preferences creation."""
        # Mock the preference creation
        created_preference = MagicMock(spec=EmailPreference)
        created_preference.user_id = 1
        created_preference.marketing_emails = False
        created_preference.id = uuid4()

        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.refresh = MagicMock()

        with patch.object(EmailPreference, '__new__', return_value=created_preference):
            result = email_preference_repo.create_default_preferences(user_id=1)

        assert result.user_id == 1
        assert result.marketing_emails is False
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_user_preferences_success(self, email_preference_repo, mock_session, sample_preference):
        """Test successful user preferences update."""
        update_data = {"marketing_emails": False}

        # Mock preference retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_preference
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        result = await email_preference_repo.update_user_preferences(1, update_data)

        assert result.marketing_emails is False
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_can_send_email_allowed(self, email_preference_repo, mock_session, sample_preference):
        """Test email sending permission check - allowed."""
        # Mock preference retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_preference
        mock_session.execute.return_value = mock_result

        result = await email_preference_repo.can_send_email(1, EmailTemplateCategory.NOTIFICATION)

        assert result is True

    @pytest.mark.asyncio
    async def test_can_send_email_blocked(self, email_preference_repo, mock_session):
        """Test email sending permission check - blocked."""
        # Create preference with marketing disabled
        blocked_preference = MagicMock(spec=EmailPreference)
        blocked_preference.user_id = 1
        blocked_preference.marketing_emails = False
        blocked_preference.booking_notifications = True
        blocked_preference.security_emails = True
        blocked_preference.verification_emails = True
        blocked_preference.system_notifications = True

        # Mock preference retrieval
        mock_result = MagicMock()
        mock_result.first.return_value = blocked_preference
        mock_session.query.return_value.filter.return_value = mock_result

        result = email_preference_repo.can_send_email(1, EmailTemplateCategory.MARKETING)

        assert result is False


class TestEmailQueueRepository:
    """Test EmailQueueRepository."""

    @pytest.mark.asyncio
    async def test_enqueue_email_success(self, email_queue_repo, mock_session):
        """Test successful email enqueuing."""
        # Mock the queue item creation
        created_item = MagicMock(spec=EmailQueue)
        created_item.recipient_email = "<EMAIL>"
        created_item.priority = 1
        created_item.id = uuid4()

        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.refresh = MagicMock()

        with patch.object(EmailQueue, '__new__', return_value=created_item):
            result = email_queue_repo.enqueue_email(
                user_id=1,
                template_id=None,
                recipient_email="<EMAIL>",
                subject="Queued Email",
                body="Queued Body",
                priority=1
            )

        assert result.recipient_email == "<EMAIL>"
        assert result.priority == 1
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_next_batch_success(self, email_queue_repo, mock_session, sample_queue_item):
        """Test successful next batch retrieval."""
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_queue_item]
        mock_session.execute.return_value = mock_result

        result = await email_queue_repo.get_next_batch(batch_size=10)

        assert len(result) == 1
        assert result[0] == sample_queue_item
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_as_sent_success(self, email_queue_repo, mock_session, sample_queue_item):
        """Test successful queue item marking as sent."""
        # Mock queue item retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_queue_item
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        result = await email_queue_repo.mark_as_sent(sample_queue_item.id, uuid4())

        assert result.status == EmailQueueStatus.SENT
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_as_failed_success(self, email_queue_repo, mock_session, sample_queue_item):
        """Test successful queue item marking as failed."""
        # Mock queue item retrieval
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_queue_item
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        result = await email_queue_repo.mark_as_failed(sample_queue_item.id, "SMTP Error")

        assert result.status == EmailQueueStatus.FAILED
        assert result.error_message == "SMTP Error"
        mock_session.commit.assert_called_once()


