"""
Security tests for enhanced WebSocket real-time communication system.

This module provides comprehensive security tests for enhanced WebSocket infrastructure including:
- JWT authentication middleware validation for all WebSocket endpoints
- RBAC permission testing for room access, participant management, and broadcasting
- Rate limiting validation and XSS prevention for WebSocket message content
- Input validation and SQL injection prevention testing
- Authorization testing for room ownership and participant role management

Implements Task 6.1.1 Phase 5 requirements with comprehensive security validation.
"""

import pytest
import json
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTTPException, status
from jose import jwt

from app.services.websocket_services import (
    WebSocketRoomService, WebSocketRoomParticipantService,
    WebSocketConnectionService
)
from app.schemas.websocket_schemas import (
    WebSocketRoomCreate, WebSocketRoomParticipantCreate
)
from app.models.user import User
from app.core.security import create_access_token, verify_token
from app.services.base import ValidationError, ServiceError


@pytest.mark.security
class TestEnhancedWebSocketAuthentication:
    """Security tests for enhanced WebSocket authentication and authorization."""

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user for security testing."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        return user

    @pytest.fixture
    def mock_admin_user(self):
        """Mock admin user for security testing."""
        user = MagicMock(spec=User)
        user.id = 2
        user.email = "<EMAIL>"
        user.role = "admin"
        return user

    @pytest.fixture
    def valid_jwt_token(self, mock_user):
        """Create valid JWT token for testing."""
        return create_access_token(data={"sub": str(mock_user.id)})

    @pytest.fixture
    def expired_jwt_token(self, mock_user):
        """Create expired JWT token for testing."""
        return create_access_token(
            data={"sub": str(mock_user.id)},
            expires_delta=timedelta(seconds=-1)  # Already expired
        )

    @pytest.fixture
    def invalid_jwt_token(self):
        """Create invalid JWT token for testing."""
        return "invalid.jwt.token"

    @pytest.mark.asyncio
    async def test_room_access_permission_validation(
        self,
        mock_user: User,
        mock_admin_user: User
    ):
        """Test room access permission validation."""
        room_service = MagicMock()

        # Test private room access by non-owner
        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock private room owned by different user
            mock_room = MagicMock()
            mock_room.is_private = True
            mock_room.owner_id = 999  # Different owner
            mock_room.id = 1

            # Mock repository
            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                mock_repo.get_room_by_id.return_value = mock_room
                mock_get_repo.return_value = mock_repo

                # Mock permission validation to raise error
                with patch.object(mock_service, '_validate_room_access_permissions') as mock_validate:
                    mock_validate.side_effect = ValidationError("Access denied to private room")

                    # Should raise permission error
                    with pytest.raises(ValidationError) as exc_info:
                        await mock_service.get_room_by_id("private_room", mock_user)

                    assert "Access denied" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_participant_role_based_permissions(
        self,
        mock_user: User
    ):
        """Test participant role-based permission validation."""
        participant_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test adding moderator role without sufficient permissions
            with patch.object(mock_service, '_validate_participant_addition_permissions') as mock_validate:
                mock_validate.side_effect = ValidationError("Insufficient permissions to assign moderator role")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.add_participant(
                        room_id=1,
                        user_id=123,
                        role="moderator",
                        current_user=mock_user
                    )

                assert "Insufficient permissions" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_room_ownership_validation(
        self,
        mock_user: User
    ):
        """Test room ownership validation for administrative operations."""
        room_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock room owned by different user
            mock_room = MagicMock()
            mock_room.owner_id = 999  # Different owner
            mock_room.id = 1

            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                mock_repo.get_room_by_id.return_value = mock_room
                mock_get_repo.return_value = mock_repo

                # Test room closure by non-owner
                with patch.object(mock_service, '_validate_room_closure_permissions') as mock_validate:
                    mock_validate.side_effect = ValidationError("Only room owner can close the room")

                    with pytest.raises(ValidationError) as exc_info:
                        await mock_service.close_room("test_room", mock_user)

                    assert "Only room owner" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_admin_override_permissions(
        self,
        mock_admin_user: User
    ):
        """Test admin override permissions for all operations."""
        room_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock room owned by different user
            mock_room = MagicMock()
            mock_room.owner_id = 999  # Different owner
            mock_room.id = 1
            mock_room.is_private = True

            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                mock_repo.get_room_by_id.return_value = mock_room
                mock_repo.update.return_value = mock_room
                mock_get_repo.return_value = mock_repo

                # Mock admin permission validation (should pass)
                with patch.object(mock_service, '_validate_room_closure_permissions') as mock_validate:
                    mock_validate.return_value = None  # Admin can access

                    # Mock participant service and notifications
                    with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_participant_service_class:
                        with patch.object(mock_service, '_notify_room_closure') as mock_notifications:
                            mock_participant_service = AsyncMock()
                            mock_participant_service.get_room_participants.return_value = []
                            mock_participant_service.bulk_remove_participants.return_value = 0
                            mock_participant_service_class.return_value = mock_participant_service
                            mock_notifications.return_value = None

                            # Admin should be able to close any room
                            result = await mock_service.close_room("test_room", mock_admin_user)
                            assert result is True

    @pytest.mark.asyncio
    async def test_input_validation_xss_prevention(
        self,
        mock_user: User
    ):
        """Test input validation and XSS prevention."""
        room_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test XSS attempt in room name
            malicious_room_data = WebSocketRoomCreate(
                room_id="xss_test_room",
                room_name="<script>alert('XSS')</script>",
                room_type="booking",
                is_private=True,
                max_participants=10,
                booking_id=123,
                owner_id=mock_user.id
            )

            # Mock validation to catch XSS
            with patch.object(mock_service, '_validate_room_creation_data') as mock_validate:
                mock_validate.side_effect = ValidationError("Invalid characters in room name")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.create_room(malicious_room_data, mock_user)

                assert "Invalid characters" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_sql_injection_prevention(
        self,
        mock_user: User
    ):
        """Test SQL injection prevention in queries."""
        room_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test SQL injection attempt in room ID
            malicious_room_id = "'; DROP TABLE websocket_rooms; --"

            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                # Repository should handle SQL injection safely
                mock_repo.get_room_by_id.return_value = None  # No room found
                mock_get_repo.return_value = mock_repo

                # Should return None safely without SQL injection
                result = await mock_service.get_room_by_id(malicious_room_id, mock_user)
                assert result is None

                # Verify repository was called with the malicious input (safely handled)
                mock_repo.get_room_by_id.assert_called_with(malicious_room_id)

    @pytest.mark.asyncio
    async def test_rate_limiting_validation(
        self,
        mock_user: User
    ):
        """Test rate limiting for WebSocket operations."""
        connection_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketConnectionService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock rate limiting check
            with patch.object(mock_service, '_check_rate_limit') as mock_rate_limit:
                mock_rate_limit.side_effect = ValidationError("Rate limit exceeded")

                event_data = {
                    "type": "spam_message",
                    "message": "Spam message",
                    "timestamp": time.time()
                }

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.broadcast_to_room(
                        room_id=1,
                        event_data=event_data,
                        current_user=mock_user
                    )

                assert "Rate limit exceeded" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_message_content_validation(
        self,
        mock_user: User
    ):
        """Test message content validation and sanitization."""
        connection_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketConnectionService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test malicious message content
            malicious_event_data = {
                "type": "message",
                "message": "<script>alert('XSS')</script><img src=x onerror=alert('XSS')>",
                "timestamp": time.time()
            }

            # Mock content validation
            with patch.object(mock_service, '_validate_message_content') as mock_validate:
                mock_validate.side_effect = ValidationError("Message contains prohibited content")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.broadcast_to_room(
                        room_id=1,
                        event_data=malicious_event_data,
                        current_user=mock_user
                    )

                assert "prohibited content" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_participant_self_management_permissions(
        self,
        mock_user: User
    ):
        """Test participant self-management permission validation."""
        participant_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test user trying to remove another user without permissions
            with patch.object(mock_service, '_validate_participant_removal_permissions') as mock_validate:
                mock_validate.side_effect = ValidationError("Cannot remove other participants")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.remove_participant(
                        room_id=1,
                        user_id=999,  # Different user
                        current_user=mock_user
                    )

                assert "Cannot remove other participants" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_bulk_operation_permission_validation(
        self,
        mock_user: User
    ):
        """Test bulk operation permission validation."""
        participant_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test bulk removal without sufficient permissions
            user_ids = [100, 101, 102, 103, 104]  # 5 users

            with patch.object(mock_service, '_validate_bulk_participant_removal_permissions') as mock_validate:
                mock_validate.side_effect = ValidationError("Insufficient permissions for bulk operations")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.bulk_remove_participants(
                        room_id=1,
                        user_ids=user_ids,
                        current_user=mock_user
                    )

                assert "Insufficient permissions for bulk operations" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_room_capacity_security_validation(
        self,
        mock_user: User
    ):
        """Test room capacity security validation."""
        participant_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock repository to simulate capacity check
            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                mock_repo.add_participant.side_effect = ValidationError("Room capacity exceeded")
                mock_get_repo.return_value = mock_repo

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.add_participant(
                        room_id=1,
                        user_id=123,
                        role="participant",
                        current_user=mock_user
                    )

                assert "capacity exceeded" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_connection_hijacking_prevention(
        self,
        mock_user: User
    ):
        """Test connection hijacking prevention."""
        connection_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketConnectionService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test user trying to join room with someone else's connection
            with patch.object(mock_service, '_validate_connection_ownership') as mock_validate:
                mock_validate.side_effect = ValidationError("Connection does not belong to user")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.join_room(
                        connection_id="foreign_connection_123",
                        room_id=1,
                        current_user=mock_user
                    )

                assert "Connection does not belong to user" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_data_exposure_prevention(
        self,
        mock_user: User
    ):
        """Test prevention of sensitive data exposure."""
        room_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketRoomService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Mock room with sensitive data
            mock_room = MagicMock()
            mock_room.id = 1
            mock_room.room_id = "sensitive_room"
            mock_room.is_private = True
            mock_room.owner_id = 999  # Different owner

            with patch.object(mock_service, '_get_repository') as mock_get_repo:
                mock_repo = AsyncMock()
                mock_repo.get_room_by_id.return_value = mock_room
                mock_get_repo.return_value = mock_repo

                # Mock data filtering for unauthorized access
                with patch.object(mock_service, '_filter_sensitive_room_data') as mock_filter:
                    mock_filter.return_value = None  # No data returned for unauthorized user

                    result = await mock_service.get_room_by_id("sensitive_room", mock_user)

                    # Should not expose sensitive data
                    assert result is None

                    # Verify filtering was applied
                    mock_filter.assert_called_once()

    @pytest.mark.asyncio
    async def test_session_validation_security(
        self,
        mock_user: User
    ):
        """Test session validation and security."""
        connection_service = MagicMock()

        with patch('app.services.websocket_services.WebSocketConnectionService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service

            # Test invalid session access
            with patch.object(mock_service, '_validate_session_security') as mock_validate:
                mock_validate.side_effect = ValidationError("Invalid session or session expired")

                with pytest.raises(ValidationError) as exc_info:
                    await mock_service.get_user_active_connections_with_rooms(
                        user_id=mock_user.id,
                        current_user=mock_user
                    )

                assert "Invalid session" in str(exc_info.value)
