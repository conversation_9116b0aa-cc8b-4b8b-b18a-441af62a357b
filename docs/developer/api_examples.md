# API Examples and Testing Strategies

## 🧪 Comprehensive Code Examples and Testing Guide

This guide provides practical code examples, testing strategies, and implementation patterns for Culture Connect's geolocation-enhanced payment system.

## API Usage Examples

### **1. Basic Geolocation Detection**

#### **Python/Requests Example**
```python
import requests
import json

# Basic IP detection
def detect_location(ip_address: str, api_token: str) -> dict:
    """Detect location from IP address."""

    url = "https://api.cultureconnect.ng/api/v1/geolocation/detect"
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "ip_address": ip_address,
        "include_vpn_detection": True,
        "include_provider_recommendation": True
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"API Error: {response.status_code} - {response.text}")

# Example usage
try:
    result = detect_location("************", "your_jwt_token")
    print(f"Country: {result['country_name']}")
    print(f"Recommended Provider: {result['recommended_provider']}")
    print(f"VPN Detected: {result['vpn_detected']}")
except Exception as e:
    print(f"Error: {e}")
```

#### **JavaScript/Fetch Example**
```javascript
// Geolocation detection with error handling
async function detectUserLocation(ipAddress, apiToken) {
    const url = 'https://api.cultureconnect.ng/api/v1/geolocation/detect';

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ip_address: ipAddress,
                include_vpn_detection: true,
                include_provider_recommendation: true
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;

    } catch (error) {
        console.error('Geolocation detection failed:', error);
        // Fallback to default routing
        return {
            country_code: 'US',
            recommended_provider: 'stripe',
            fallback: true
        };
    }
}

// Usage with automatic IP detection
async function detectCurrentUserLocation(apiToken) {
    // Let server detect client IP
    return await detectUserLocation(null, apiToken);
}
```

#### **cURL Example**
```bash
# Basic geolocation detection
curl -X POST "https://api.cultureconnect.ng/api/v1/geolocation/detect" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "ip_address": "************",
    "include_vpn_detection": true,
    "include_provider_recommendation": true
  }'

# Response example
{
  "ip_address": "************",
  "country_code": "NG",
  "country_name": "Nigeria",
  "continent_code": "AF",
  "continent_name": "Africa",
  "detection_method": "maxmind_geoip",
  "confidence_score": 0.98,
  "detection_time_ms": 45.5,
  "vpn_detected": false,
  "recommended_provider": "paystack",
  "provider_selection_reason": "Nigerian IP detected, optimal for Paystack processing",
  "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
  "detected_at": "2024-01-15T10:30:00Z"
}
```

### **2. Enhanced Payment Creation**

#### **Python Example with Geolocation**
```python
import requests
from typing import Optional

class CultureConnectPayments:
    """Culture Connect payment client with geolocation support."""

    def __init__(self, api_token: str, base_url: str = "https://api.cultureconnect.ng"):
        self.api_token = api_token
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        })

    def create_payment(
        self,
        booking_id: int,
        amount: float,
        currency: str = "USD",
        crypto_currency: Optional[str] = None,
        return_url: str = None,
        metadata: Optional[dict] = None
    ) -> dict:
        """Create geolocation-enhanced payment."""

        url = f"{self.base_url}/api/v1/payments/create"

        payload = {
            "booking_id": booking_id,
            "amount": amount,
            "currency": currency,
            "return_url": return_url or "https://yourapp.com/payment/return",
            "metadata": metadata or {}
        }

        # Add cryptocurrency if specified
        if crypto_currency:
            payload["crypto_currency"] = crypto_currency

        response = self.session.post(url, json=payload)

        if response.status_code == 201:
            return response.json()
        else:
            raise Exception(f"Payment creation failed: {response.status_code} - {response.text}")

    def get_payment_status(self, payment_id: str) -> dict:
        """Get payment status with geolocation metadata."""
        url = f"{self.base_url}/api/v1/payments/{payment_id}"
        response = self.session.get(url)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Payment status check failed: {response.status_code}")

# Example usage
client = CultureConnectPayments("your_jwt_token")

# Create payment (system will auto-detect location and route optimally)
payment = client.create_payment(
    booking_id=123,
    amount=50000.00,
    currency="USD",
    return_url="https://myapp.com/payment/success"
)

print(f"Payment ID: {payment['id']}")
print(f"Provider: {payment['provider']}")
print(f"Payment URL: {payment['payment_url']}")
print(f"Detected Country: {payment['geolocation_metadata']['detected_country']}")
print(f"Routing Decision: {payment['geolocation_metadata']['routing_decision']}")
```

#### **React/TypeScript Frontend Example**
```typescript
interface GeolocationMetadata {
  detected_country: string;
  country_name: string;
  routing_decision: string;
  provider_selection_reason: string;
  detection_confidence: number;
  vpn_detected: boolean;
  risk_score: number;
}

interface PaymentResponse {
  id: string;
  booking_id: number;
  amount: number;
  currency: string;
  provider: string;
  status: string;
  payment_url: string;
  geolocation_metadata: GeolocationMetadata;
  created_at: string;
}

class CultureConnectAPI {
  private apiToken: string;
  private baseUrl: string;

  constructor(apiToken: string, baseUrl: string = 'https://api.cultureconnect.ng') {
    this.apiToken = apiToken;
    this.baseUrl = baseUrl;
  }

  async createPayment(paymentData: {
    booking_id: number;
    amount: number;
    currency: string;
    crypto_currency?: string;
    return_url: string;
  }): Promise<PaymentResponse> {
    const response = await fetch(`${this.baseUrl}/api/v1/payments/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    if (!response.ok) {
      throw new Error(`Payment creation failed: ${response.statusText}`);
    }

    return await response.json();
  }

  async getProviderRecommendation(countryCode: string, currency?: string): Promise<any> {
    const params = new URLSearchParams();
    if (currency) params.append('currency', currency);

    const response = await fetch(
      `${this.baseUrl}/api/v1/geolocation/provider-recommendation/${countryCode}?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Provider recommendation failed: ${response.statusText}`);
    }

    return await response.json();
  }
}

// React component example
import React, { useState, useEffect } from 'react';

const PaymentComponent: React.FC = () => {
  const [paymentData, setPaymentData] = useState<PaymentResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const api = new CultureConnectAPI('your_jwt_token');

  const handlePayment = async () => {
    setLoading(true);
    setError(null);

    try {
      const payment = await api.createPayment({
        booking_id: 123,
        amount: 250.00,
        currency: 'USD',
        return_url: window.location.origin + '/payment/success',
      });

      setPaymentData(payment);

      // Redirect to payment URL
      window.location.href = payment.payment_url;

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Payment failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="payment-component">
      <button onClick={handlePayment} disabled={loading}>
        {loading ? 'Processing...' : 'Pay Now'}
      </button>

      {error && <div className="error">{error}</div>}

      {paymentData && (
        <div className="payment-info">
          <p>Payment Provider: {paymentData.provider}</p>
          <p>Detected Location: {paymentData.geolocation_metadata.country_name}</p>
          <p>Routing: {paymentData.geolocation_metadata.routing_decision}</p>
          {paymentData.geolocation_metadata.vpn_detected && (
            <p className="warning">VPN detected - enhanced security applied</p>
          )}
        </div>
      )}
    </div>
  );
};
```

### **3. Provider Recommendation API**

#### **Get Optimal Provider for Country**
```bash
# Get provider recommendation for Nigeria
curl -X GET "https://api.cultureconnect.ng/api/v1/geolocation/provider-recommendation/NG?currency=USD" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Response
{
  "country_code": "NG",
  "country_name": "Nigeria",
  "recommended_provider": "paystack",
  "selection_reason": "Optimal provider for Nigerian market with local payment methods",
  "confidence_score": 0.95,
  "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
  "supported_payment_methods": ["card", "bank_transfer", "ussd", "mobile_money"],
  "estimated_success_rate": 0.94,
  "average_processing_time_ms": 2500,
  "transaction_fees": {
    "percentage": 1.5,
    "fixed_fee_ngn": 0,
    "cap_ngn": 2000
  }
}
```

#### **Python Helper Function**
```python
def get_optimal_payment_setup(country_code: str, currency: str, api_token: str) -> dict:
    """Get complete payment setup recommendation."""

    url = f"https://api.cultureconnect.ng/api/v1/geolocation/provider-recommendation/{country_code}"
    params = {"currency": currency}
    headers = {"Authorization": f"Bearer {api_token}"}

    response = requests.get(url, params=params, headers=headers)

    if response.status_code == 200:
        data = response.json()
        return {
            "provider": data["recommended_provider"],
            "payment_methods": data["supported_payment_methods"],
            "currencies": data["supported_currencies"],
            "success_rate": data["estimated_success_rate"],
            "processing_time": data["average_processing_time_ms"],
            "fees": data["transaction_fees"]
        }
    else:
        # Fallback configuration
        return {
            "provider": "stripe",
            "payment_methods": ["card"],
            "currencies": [currency],
            "success_rate": 0.85,
            "processing_time": 3000,
            "fees": {"percentage": 2.9, "fixed_fee_cents": 30}
        }
```

## Testing Strategies

### **4. Unit Testing Framework**

#### **Pytest Configuration**
```python
# conftest.py
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from app.services.geolocation_service import GeolocationService
from app.services.vpn_detection_service import VPNDetectionService

@pytest.fixture
def mock_geolocation_service():
    """Mock geolocation service for testing."""
    service = AsyncMock(spec=GeolocationService)

    # Mock Nigerian IP detection
    service.detect_country_from_ip.return_value = MagicMock(
        country_code="NG",
        country_name="Nigeria",
        continent_code="AF",
        confidence_score=0.98,
        detection_time_ms=45.5,
        detection_method="maxmind_geoip"
    )

    return service

@pytest.fixture
def mock_vpn_service():
    """Mock VPN detection service for testing."""
    service = AsyncMock(spec=VPNDetectionService)

    # Mock no VPN detected
    service.detect_vpn_proxy.return_value = MagicMock(
        is_vpn_detected=False,
        confidence_score=0.1,
        detection_method="asn_analysis",
        risk_score=0.1
    )

    return service

@pytest.fixture
def sample_payment_data():
    """Sample payment data for testing."""
    return {
        "booking_id": 123,
        "amount": 50000.00,
        "currency": "USD",
        "return_url": "https://test.example.com/return"
    }
```

#### **Geolocation Tests**
```python
# test_geolocation.py
import pytest
from unittest.mock import patch, AsyncMock

@pytest.mark.asyncio
async def test_nigerian_ip_detection(mock_geolocation_service):
    """Test Nigerian IP detection returns correct country."""
    result = await mock_geolocation_service.detect_country_from_ip("************")

    assert result.country_code == "NG"
    assert result.country_name == "Nigeria"
    assert result.confidence_score > 0.9

@pytest.mark.asyncio
async def test_provider_selection_for_nigeria():
    """Test provider selection for Nigerian users."""
    from app.services.payment_service import PaymentService

    provider_info = await PaymentService.get_optimal_provider_for_country("NG")

    assert provider_info["provider"] == "paystack"
    assert provider_info["routing_priority"] == 3
    assert "Nigerian" in provider_info["reason"]

@pytest.mark.asyncio
async def test_vpn_detection_impact():
    """Test VPN detection affects routing decisions."""
    # Mock VPN detected scenario
    with patch('app.services.vpn_detection_service.VPNDetectionService') as mock_vpn:
        mock_vpn.return_value.detect_vpn_proxy.return_value = AsyncMock(
            is_vpn_detected=True,
            vpn_provider="ExpressVPN",
            confidence_score=0.92,
            risk_score=0.6
        )

        # Test routing adjustment for VPN users
        result = await enhanced_location_detection("*******")

        assert result.is_vpn_detected == True
        assert result.risk_score > 0.5

@pytest.mark.asyncio
async def test_performance_targets():
    """Test that performance targets are met."""
    import time

    start_time = time.perf_counter()

    # Test geolocation detection speed
    result = await mock_geolocation_service.detect_country_from_ip("*******")

    detection_time = (time.perf_counter() - start_time) * 1000

    # Verify <100ms target (mocked, so should be very fast)
    assert detection_time < 100
    assert result.detection_time_ms < 100

@pytest.mark.asyncio
async def test_fallback_mechanisms():
    """Test fallback when geolocation fails."""
    with patch('app.services.geolocation_service.geoip2') as mock_geoip:
        # Simulate MaxMind database failure
        mock_geoip.database.Reader.side_effect = Exception("Database unavailable")

        # Should fallback gracefully
        result = await resilient_geolocation_service.detect_with_fallback("*******")

        assert result["country_code"] == "US"  # Default fallback
        assert result["detection_method"] == "default_fallback"
        assert result["confidence_score"] == 0.0
```

### **5. Integration Testing**

#### **API Integration Tests**
```python
# test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_geolocation_detection_endpoint():
    """Test geolocation detection API endpoint."""
    response = client.post(
        "/api/v1/geolocation/detect",
        json={
            "ip_address": "************",
            "include_vpn_detection": True,
            "include_provider_recommendation": True
        },
        headers={"Authorization": "Bearer test_token"}
    )

    assert response.status_code == 200
    data = response.json()

    assert "country_code" in data
    assert "recommended_provider" in data
    assert "detection_time_ms" in data
    assert data["detection_time_ms"] < 100  # Performance target

def test_payment_creation_with_geolocation():
    """Test payment creation with geolocation enhancement."""
    response = client.post(
        "/api/v1/payments/create",
        json={
            "booking_id": 123,
            "amount": 50000.00,
            "currency": "USD",
            "return_url": "https://test.example.com/return"
        },
        headers={"Authorization": "Bearer test_token"}
    )

    assert response.status_code == 201
    data = response.json()

    assert "geolocation_metadata" in data
    assert "provider" in data
    assert "routing_decision" in data["geolocation_metadata"]

def test_provider_recommendation_endpoint():
    """Test provider recommendation API."""
    response = client.get(
        "/api/v1/geolocation/provider-recommendation/NG?currency=USD",
        headers={"Authorization": "Bearer test_token"}
    )

    assert response.status_code == 200
    data = response.json()

    assert data["country_code"] == "NG"
    assert data["recommended_provider"] in ["paystack", "stripe", "busha"]
    assert "supported_currencies" in data
    assert "estimated_success_rate" in data
```

### **6. Load Testing**

#### **Locust Performance Tests**
```python
# locustfile.py
from locust import HttpUser, task, between
import random

class GeolocationAPIUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        """Setup test user."""
        self.token = "test_jwt_token"
        self.headers = {"Authorization": f"Bearer {self.token}"}

    @task(3)
    def test_geolocation_detection(self):
        """Test geolocation detection performance."""
        test_ips = [
            "************",  # Nigeria
            "*******",       # US
            "***********",   # UK
            "*******"        # Cloudflare
        ]

        ip = random.choice(test_ips)

        self.client.post(
            "/api/v1/geolocation/detect",
            json={
                "ip_address": ip,
                "include_vpn_detection": True,
                "include_provider_recommendation": True
            },
            headers=self.headers,
            name="geolocation_detection"
        )

    @task(2)
    def test_payment_creation(self):
        """Test payment creation performance."""
        self.client.post(
            "/api/v1/payments/create",
            json={
                "booking_id": random.randint(1, 1000),
                "amount": random.uniform(10.0, 1000.0),
                "currency": random.choice(["USD", "NGN", "GBP", "EUR"]),
                "return_url": "https://test.example.com/return"
            },
            headers=self.headers,
            name="payment_creation"
        )

    @task(1)
    def test_provider_recommendation(self):
        """Test provider recommendation performance."""
        countries = ["NG", "US", "UK", "CA", "GH", "KE"]
        country = random.choice(countries)

        self.client.get(
            f"/api/v1/geolocation/provider-recommendation/{country}",
            headers=self.headers,
            name="provider_recommendation"
        )

# Run with: locust -f locustfile.py --host=https://api.cultureconnect.ng
```

#### **Performance Validation Script**
```python
# performance_test.py
import asyncio
import time
import statistics
from typing import List

async def measure_geolocation_performance(ip_addresses: List[str], iterations: int = 100):
    """Measure geolocation detection performance."""
    times = []

    for _ in range(iterations):
        for ip in ip_addresses:
            start_time = time.perf_counter()

            # Simulate geolocation detection
            result = await geolocation_service.detect_country_from_ip(ip)

            end_time = time.perf_counter()
            detection_time = (end_time - start_time) * 1000
            times.append(detection_time)

    return {
        "mean_time_ms": statistics.mean(times),
        "median_time_ms": statistics.median(times),
        "p95_time_ms": statistics.quantiles(times, n=20)[18],  # 95th percentile
        "p99_time_ms": statistics.quantiles(times, n=100)[98], # 99th percentile
        "max_time_ms": max(times),
        "min_time_ms": min(times)
    }

# Performance validation
async def validate_performance_targets():
    """Validate that performance targets are met."""
    test_ips = ["************", "*******", "***********"]

    results = await measure_geolocation_performance(test_ips, 100)

    print("Geolocation Performance Results:")
    print(f"Mean: {results['mean_time_ms']:.2f}ms")
    print(f"P95: {results['p95_time_ms']:.2f}ms")
    print(f"P99: {results['p99_time_ms']:.2f}ms")

    # Validate targets
    assert results['p95_time_ms'] < 100, f"P95 time {results['p95_time_ms']:.2f}ms exceeds 100ms target"
    assert results['p99_time_ms'] < 200, f"P99 time {results['p99_time_ms']:.2f}ms exceeds 200ms target"

    print("✅ All performance targets met!")

if __name__ == "__main__":
    asyncio.run(validate_performance_targets())
```

---

## 📚 Related Developer Guides

- **[Integration Guide](geolocation_integration.md)** - Complete integration walkthrough with code examples
- **[Configuration Guide](configuration_guide.md)** - Environment setup and production deployment
- **[User Guides](../user_guide/geolocation_features.md)** - User-facing documentation
- **[API Documentation](../README.md)** - Complete API reference

---

**Last Updated**: January 15, 2024
**Version**: 2.4.3
**Part of**: Culture Connect Backend API Documentation v2.4.3
