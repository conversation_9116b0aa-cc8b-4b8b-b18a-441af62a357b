"""
Alembic environment configuration for Culture Connect Backend API.

This module configures Alembic for database migrations with comprehensive
support for async operations, environment-specific configurations, and
proper model registration.
"""

import logging
import sys
from logging.config import fileConfig
from pathlib import Path

from sqlalchemy import pool, create_engine
from sqlalchemy.engine import Connection
from alembic import context

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the base model and configuration
from app.db.base import Base
from app.core.config import settings

# Import all models to ensure they are registered with SQLAlchemy
# Only import models that currently exist
from app.models.user import User, APIKey, UserSession
from app.models.vendor import Vendor, VendorProfile, VendorDocument
from app.models.infrastructure_optimization import (
    CacheConfiguration, BackgroundTask, PerformanceMonitoring,
    CacheMetrics, TaskMetrics
)
from app.models.content_moderation import (
    ContentModerationWorkflow, ContentQualityScore, PlagiarismCheck,
    ContentModerationRule, ContentApprovalHistory
)

# Payment models (implemented in Phase 1 - Payment & Transaction Management System)
from app.models.payment import Payment, PaymentMethod
from app.models.transaction_models import Transaction, TransactionEvent
from app.models.payout_models import VendorPayout, EscrowAccount
from app.models.financial_models import RevenueRecord, ReconciliationRecord

# Promotional models (implemented in Phase 5 - Promotional & Advertising System)
from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend
)

# TODO: Import additional models as they are implemented
# from app.models.service import Service, ServiceCategory, ServiceImage, ServiceAvailability
# from app.models.booking import Booking, BookingItem, BookingStatus, Review
# from app.models.payment import Payment, PaymentMethod, Transaction, Refund

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# Configure naming convention for constraints
target_metadata.naming_convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}


def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    # Get the alembic config
    config = context.config

    # Set the database URL
    config.set_main_option("sqlalchemy.url", settings.database_url_sync)

    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        include_schemas=True,
        include_object=include_object,
        render_as_batch=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """
    Run migrations with the given connection.

    Args:
        connection: Database connection to use for migrations
    """
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
        include_schemas=True,
        # Include object name in migration operations
        include_object=include_object,
        # Custom naming convention for constraints
        render_as_batch=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def include_object(obj, name, type_, reflected, compare_to):
    """
    Filter objects to include in migrations.

    This function determines which database objects should be included
    in the migration process.

    Args:
        obj: The database object (unused but required by alembic)
        name: Name of the object
        type_: Type of the object (table, index, etc.)
        reflected: Whether the object was reflected from the database (unused)
        compare_to: The object being compared to (unused)

    Returns:
        bool: True if the object should be included, False otherwise
    """
    # Skip Alembic's version table
    if type_ == "table" and name == "alembic_version":
        return False

    # Skip temporary tables
    if type_ == "table" and name.startswith("tmp_"):
        return False

    # Skip test tables in production
    if type_ == "table" and name.startswith("test_") and not settings.is_development:
        return False

    # Include all other objects
    return True


def run_migrations_online() -> None:
    """
    Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    # Use sync engine for migrations
    connectable = create_engine(
        settings.database_url_sync,
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        do_run_migrations(connection)


# Determine migration mode and run appropriate function
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
