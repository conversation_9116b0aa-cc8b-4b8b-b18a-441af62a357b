"""Add review management system tables for Task 4.4.1

Revision ID: d8b8c4817a77
Revises: payment_system_tables
Create Date: 2025-05-27 22:11:38.662069+00:00

This migration creates comprehensive review management system tables including:
- Review: Core customer feedback and rating system with booking validation
- ReviewResponse: Vendor response system with status management
- ReviewModeration: AI-powered content moderation with confidence scoring
- ReviewAnalytics: Vendor performance metrics and rating distribution

Implements Task 4.4.1 requirements for review management system with:
- Complete review lifecycle management
- Vendor response capabilities
- AI-powered content moderation workflow
- Review analytics and performance tracking
- Integration with booking, user, vendor, and service systems
- Performance-optimized indexes for <200ms query targets
- Full-text search capabilities for review content
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'd8b8c4817a77'
down_revision = 'payment_system_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create review management system tables."""

    # Create review status enum
    review_status_enum = postgresql.ENUM(
        'pending', 'approved', 'rejected', 'flagged', 'hidden',
        name='reviewstatus'
    )
    review_status_enum.create(op.get_bind())

    # Create response status enum
    response_status_enum = postgresql.ENUM(
        'draft', 'published', 'hidden',
        name='responsestatus'
    )
    response_status_enum.create(op.get_bind())

    # Create moderation action enum
    moderation_action_enum = postgresql.ENUM(
        'approve', 'reject', 'flag', 'request_edit', 'escalate',
        name='moderationaction'
    )
    moderation_action_enum.create(op.get_bind())

    # Create reviews table
    op.create_table(
        'reviews',
        # BaseModelWithAudit fields
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core review information
        sa.Column('booking_id', sa.Integer(), nullable=False),
        sa.Column('customer_id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('service_id', sa.Integer(), nullable=False),

        # Review content
        sa.Column('rating', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),

        # Review status and moderation
        sa.Column('status', postgresql.ENUM('pending', 'approved', 'rejected', 'flagged', 'hidden', name='reviewstatus'), nullable=False, server_default='pending'),
        sa.Column('moderation_reason', sa.Text(), nullable=True),

        # Verification and authenticity
        sa.Column('is_verified_purchase', sa.Boolean(), nullable=False, server_default='true'),

        # Community engagement
        sa.Column('helpful_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('reported_count', sa.Integer(), nullable=False, server_default='0'),

        # AI analysis
        sa.Column('sentiment_score', sa.Float(), nullable=True),
        sa.Column('language_code', sa.String(length=10), nullable=True),

        # Additional metadata
        sa.Column('review_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Foreign keys
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['customer_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ondelete='SET NULL'),

        # Unique constraints
        sa.UniqueConstraint('booking_id', name='uq_reviews_booking_id'),

        # Check constraints
        sa.CheckConstraint('rating >= 1 AND rating <= 5', name='check_review_rating_range'),
        sa.CheckConstraint('LENGTH(title) <= 200', name='check_review_title_length'),
        sa.CheckConstraint('LENGTH(content) <= 2000', name='check_review_content_length'),
        sa.CheckConstraint('helpful_count >= 0', name='check_review_helpful_count_non_negative'),
        sa.CheckConstraint('reported_count >= 0', name='check_review_reported_count_non_negative'),
        sa.CheckConstraint('sentiment_score IS NULL OR (sentiment_score >= -1.0 AND sentiment_score <= 1.0)', name='check_review_sentiment_score_range'),
    )

    # Create review responses table
    op.create_table(
        'review_responses',
        # BaseModelWithAudit fields
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core response information
        sa.Column('review_id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),

        # Response content
        sa.Column('content', sa.Text(), nullable=False),

        # Response status
        sa.Column('status', postgresql.ENUM('draft', 'published', 'hidden', name='responsestatus'), nullable=False, server_default='draft'),

        # Response properties
        sa.Column('is_official_response', sa.Boolean(), nullable=False, server_default='true'),

        # Response metadata
        sa.Column('response_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Foreign keys
        sa.ForeignKeyConstraint(['review_id'], ['reviews.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ondelete='SET NULL'),

        # Check constraints
        sa.CheckConstraint('LENGTH(content) <= 1000', name='check_response_content_length'),
    )

    # Create review moderation table
    op.create_table(
        'review_moderation',
        # BaseModelWithAudit fields
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core moderation information
        sa.Column('review_id', sa.Integer(), nullable=False),
        sa.Column('moderator_id', sa.Integer(), nullable=True),

        # Moderation decision
        sa.Column('action', postgresql.ENUM('approve', 'reject', 'flag', 'request_edit', 'escalate', name='moderationaction'), nullable=False),
        sa.Column('reason', sa.Text(), nullable=True),

        # AI analysis
        sa.Column('ai_confidence_score', sa.Float(), nullable=True),
        sa.Column('manual_review_required', sa.Boolean(), nullable=False, server_default='false'),

        # Processing timestamps
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),

        # AI analysis results
        sa.Column('ai_analysis_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Moderation metadata
        sa.Column('moderation_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Foreign keys
        sa.ForeignKeyConstraint(['review_id'], ['reviews.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['moderator_id'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ondelete='SET NULL'),

        # Check constraints
        sa.CheckConstraint('ai_confidence_score IS NULL OR (ai_confidence_score >= 0.0 AND ai_confidence_score <= 1.0)', name='check_moderation_ai_confidence_range'),
    )

    # Create review analytics table
    op.create_table(
        'review_analytics',
        # BaseModelWithAudit fields
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core analytics information
        sa.Column('vendor_id', sa.Integer(), nullable=False),

        # Analytics period
        sa.Column('period_start', sa.Date(), nullable=False),
        sa.Column('period_end', sa.Date(), nullable=False),

        # Review metrics
        sa.Column('total_reviews', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=True),

        # Rating distribution (1-5 stars)
        sa.Column('rating_distribution', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Sentiment analysis
        sa.Column('sentiment_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Response metrics
        sa.Column('response_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('average_response_time', sa.Integer(), nullable=True),

        # Quality metrics
        sa.Column('verified_reviews_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('helpful_votes_total', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('reported_reviews_count', sa.Integer(), nullable=False, server_default='0'),

        # Performance trends
        sa.Column('rating_trend', sa.String(length=20), nullable=True),
        sa.Column('review_volume_trend', sa.String(length=20), nullable=True),

        # Analytics metadata
        sa.Column('analytics_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Foreign keys
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ondelete='SET NULL'),

        # Unique constraints
        sa.UniqueConstraint('vendor_id', 'period_start', 'period_end', name='uq_review_analytics_vendor_period'),

        # Check constraints
        sa.CheckConstraint('total_reviews >= 0', name='check_analytics_total_reviews_non_negative'),
        sa.CheckConstraint('average_rating IS NULL OR (average_rating >= 1.00 AND average_rating <= 5.00)', name='check_analytics_average_rating_range'),
        sa.CheckConstraint('response_rate IS NULL OR (response_rate >= 0.0 AND response_rate <= 1.0)', name='check_analytics_response_rate_range'),
        sa.CheckConstraint('average_response_time IS NULL OR average_response_time >= 0', name='check_analytics_response_time_non_negative'),
        sa.CheckConstraint('verified_reviews_count >= 0', name='check_analytics_verified_reviews_non_negative'),
        sa.CheckConstraint('helpful_votes_total >= 0', name='check_analytics_helpful_votes_non_negative'),
        sa.CheckConstraint('reported_reviews_count >= 0', name='check_analytics_reported_reviews_non_negative'),
        sa.CheckConstraint('period_start <= period_end', name='check_analytics_period_valid'),
    )

    # Create performance indexes for reviews table
    op.create_index('idx_reviews_vendor_rating', 'reviews', ['vendor_id', 'rating', 'created_at'])
    op.create_index('idx_reviews_service_rating', 'reviews', ['service_id', 'rating', 'created_at'])
    op.create_index('idx_reviews_status_created', 'reviews', ['status', 'created_at'])
    op.create_index('idx_reviews_customer_created', 'reviews', ['customer_id', 'created_at'])
    op.create_index('idx_reviews_verified_rating', 'reviews', ['is_verified_purchase', 'rating'])
    op.create_index('idx_reviews_sentiment_rating', 'reviews', ['sentiment_score', 'rating'])

    # Create full-text search index for review content (PostgreSQL GIN index)
    op.create_index('idx_reviews_content_search', 'reviews', ['title', 'content'], postgresql_using='gin')

    # Create performance indexes for review_responses table
    op.create_index('idx_review_responses_review_status', 'review_responses', ['review_id', 'status'])
    op.create_index('idx_review_responses_vendor_created', 'review_responses', ['vendor_id', 'created_at'])
    op.create_index('idx_review_responses_status_created', 'review_responses', ['status', 'created_at'])

    # Create performance indexes for review_moderation table
    op.create_index('idx_review_moderation_review_action', 'review_moderation', ['review_id', 'action'])
    op.create_index('idx_review_moderation_action_processed', 'review_moderation', ['action', 'processed_at'])
    op.create_index('idx_review_moderation_manual_required', 'review_moderation', ['manual_review_required', 'created_at'])
    op.create_index('idx_review_moderation_moderator_processed', 'review_moderation', ['moderator_id', 'processed_at'])

    # Create performance indexes for review_analytics table
    op.create_index('idx_review_analytics_vendor_period', 'review_analytics', ['vendor_id', 'period_start', 'period_end'])
    op.create_index('idx_review_analytics_period_rating', 'review_analytics', ['period_start', 'period_end', 'average_rating'])
    op.create_index('idx_review_analytics_vendor_rating', 'review_analytics', ['vendor_id', 'average_rating'])


def downgrade() -> None:
    """Drop review management system tables."""

    # Drop indexes first
    op.drop_index('idx_review_analytics_vendor_rating', table_name='review_analytics')
    op.drop_index('idx_review_analytics_period_rating', table_name='review_analytics')
    op.drop_index('idx_review_analytics_vendor_period', table_name='review_analytics')

    op.drop_index('idx_review_moderation_moderator_processed', table_name='review_moderation')
    op.drop_index('idx_review_moderation_manual_required', table_name='review_moderation')
    op.drop_index('idx_review_moderation_action_processed', table_name='review_moderation')
    op.drop_index('idx_review_moderation_review_action', table_name='review_moderation')

    op.drop_index('idx_review_responses_status_created', table_name='review_responses')
    op.drop_index('idx_review_responses_vendor_created', table_name='review_responses')
    op.drop_index('idx_review_responses_review_status', table_name='review_responses')

    op.drop_index('idx_reviews_content_search', table_name='reviews')
    op.drop_index('idx_reviews_sentiment_rating', table_name='reviews')
    op.drop_index('idx_reviews_verified_rating', table_name='reviews')
    op.drop_index('idx_reviews_customer_created', table_name='reviews')
    op.drop_index('idx_reviews_status_created', table_name='reviews')
    op.drop_index('idx_reviews_service_rating', table_name='reviews')
    op.drop_index('idx_reviews_vendor_rating', table_name='reviews')

    # Drop tables in reverse order (respecting foreign key dependencies)
    op.drop_table('review_analytics')
    op.drop_table('review_moderation')
    op.drop_table('review_responses')
    op.drop_table('reviews')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS moderationaction')
    op.execute('DROP TYPE IF EXISTS responsestatus')
    op.execute('DROP TYPE IF EXISTS reviewstatus')
