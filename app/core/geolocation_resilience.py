"""
Geolocation resilience patterns for Culture Connect Backend API.

This module provides comprehensive resilience patterns for geolocation services including:
- Fallback mechanisms for MaxMind database failures
- Retry logic with exponential backoff
- Graceful degradation strategies
- Service health monitoring and recovery

Implements production-grade resilience patterns following the NewEnhancement.md
specifications for reliable geolocation-based payment routing.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass
from enum import Enum
import random

from app.core.circuit_breaker import get_geolocation_circuit_breaker, CircuitBreakerError
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

logger = logging.getLogger(__name__)


# Simple GeolocationResult class to avoid circular imports
@dataclass
class GeolocationResult:
    """Geolocation detection result with metadata."""
    country_code: Optional[str]
    country_name: Optional[str]
    continent_code: Optional[str]
    ip_address: str
    detection_method: str
    confidence_score: float
    is_vpn_detected: bool = False
    is_proxy_detected: bool = False
    detected_at: float = None

    def __post_init__(self):
        if self.detected_at is None:
            self.detected_at = time.time()


class FallbackStrategy(Enum):
    """Fallback strategies for geolocation failures."""
    DEFAULT_COUNTRY = "default_country"
    IP_RANGE_MAPPING = "ip_range_mapping"
    CACHED_RESULT = "cached_result"
    USER_PREFERENCE = "user_preference"
    GRACEFUL_DEGRADATION = "graceful_degradation"


@dataclass
class ResilienceConfig:
    """Configuration for geolocation resilience patterns."""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    exponential_base: float = 2.0
    jitter: bool = True
    fallback_country: str = "NG"
    enable_circuit_breaker: bool = True
    cache_fallback_results: bool = True
    fallback_confidence_score: float = 0.3


class GeolocationResilienceManager:
    """
    Comprehensive resilience manager for geolocation services.

    Provides retry logic, fallback mechanisms, and graceful degradation
    for reliable geolocation-based payment routing.
    """

    def __init__(self, config: Optional[ResilienceConfig] = None):
        """Initialize resilience manager."""
        self.config = config or ResilienceConfig()
        self.circuit_breaker = get_geolocation_circuit_breaker()
        self.fallback_cache = {}

        # IP range to country mappings for fallback
        self.ip_range_mappings = {
            # Nigerian IP ranges (simplified examples)
            "197.210.": "NG",
            "41.203.": "ZA",
            "105.112.": "EG",
            "8.8.": "US",
            "1.1.": "US",
        }

        logger.info("Initialized geolocation resilience manager")

    async def detect_country_with_resilience(
        self,
        geolocation_service,
        ip_address: str,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> GeolocationResult:
        """
        Detect country with comprehensive resilience patterns.

        Args:
            geolocation_service: Geolocation service instance
            ip_address: IP address to analyze
            user_preferences: Optional user preferences for fallback

        Returns:
            GeolocationResult with fallback handling
        """
        correlation = correlation_id.get()

        logger.debug(
            f"Starting resilient geolocation detection for {ip_address}",
            extra={"correlation_id": correlation}
        )

        # Try primary detection with circuit breaker protection
        if self.config.enable_circuit_breaker:
            try:
                result = await self.circuit_breaker.detect_country_with_protection(
                    geolocation_service, ip_address
                )

                # Cache successful result
                if self.config.cache_fallback_results:
                    self.fallback_cache[ip_address] = result

                metrics_collector.increment_counter(
                    "geolocation_resilience_detection",
                    tags={"method": "primary", "result": "success"}
                )

                return result

            except CircuitBreakerError:
                logger.warning(
                    f"Circuit breaker open for geolocation service, using fallback for {ip_address}",
                    extra={"correlation_id": correlation}
                )

                metrics_collector.increment_counter(
                    "geolocation_resilience_detection",
                    tags={"method": "circuit_breaker_fallback", "result": "fallback"}
                )

                return await self._execute_fallback_strategy(ip_address, user_preferences)

        # Try with retry logic if circuit breaker is disabled
        return await self._detect_with_retry(geolocation_service, ip_address, user_preferences)

    async def _detect_with_retry(
        self,
        geolocation_service,
        ip_address: str,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> GeolocationResult:
        """Execute geolocation detection with retry logic."""
        correlation = correlation_id.get()
        last_exception = None

        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_retry_delay(attempt)
                    logger.debug(
                        f"Retrying geolocation detection for {ip_address}, attempt {attempt + 1}, delay {delay:.2f}s",
                        extra={"correlation_id": correlation}
                    )
                    await asyncio.sleep(delay)

                result = await geolocation_service.detect_country_from_ip(ip_address)

                # Cache successful result
                if self.config.cache_fallback_results:
                    self.fallback_cache[ip_address] = result

                metrics_collector.increment_counter(
                    "geolocation_resilience_detection",
                    tags={"method": "retry_success", "attempt": str(attempt + 1), "result": "success"}
                )

                return result

            except Exception as e:
                last_exception = e

                logger.warning(
                    f"Geolocation detection attempt {attempt + 1} failed for {ip_address}: {str(e)}",
                    extra={"correlation_id": correlation}
                )

                metrics_collector.increment_counter(
                    "geolocation_resilience_detection",
                    tags={"method": "retry_failure", "attempt": str(attempt + 1), "result": "failure"}
                )

        # All retries failed, use fallback
        logger.error(
            f"All geolocation detection attempts failed for {ip_address}, using fallback",
            extra={"correlation_id": correlation}
        )

        return await self._execute_fallback_strategy(ip_address, user_preferences, last_exception)

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate retry delay with exponential backoff and jitter."""
        delay = min(
            self.config.base_delay * (self.config.exponential_base ** (attempt - 1)),
            self.config.max_delay
        )

        if self.config.jitter:
            # Add random jitter (±25%)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)

        return max(delay, 0.1)  # Minimum 100ms delay

    async def _execute_fallback_strategy(
        self,
        ip_address: str,
        user_preferences: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ) -> GeolocationResult:
        """Execute fallback strategy for geolocation detection."""
        correlation = correlation_id.get()

        # Strategy 1: Check cached result
        if ip_address in self.fallback_cache:
            cached_result = self.fallback_cache[ip_address]
            logger.info(
                f"Using cached geolocation result for {ip_address}: {cached_result.country_code}",
                extra={"correlation_id": correlation}
            )

            metrics_collector.increment_counter(
                "geolocation_resilience_fallback",
                tags={"strategy": "cached_result", "result": "success"}
            )

            return cached_result

        # Strategy 2: User preference fallback
        if user_preferences and "preferred_country" in user_preferences:
            preferred_country = user_preferences["preferred_country"]
            logger.info(
                f"Using user preference fallback for {ip_address}: {preferred_country}",
                extra={"correlation_id": correlation}
            )

            metrics_collector.increment_counter(
                "geolocation_resilience_fallback",
                tags={"strategy": "user_preference", "result": "success"}
            )

            return GeolocationResult(
                country_code=preferred_country,
                country_name=self._get_country_name(preferred_country),
                continent_code=self._get_continent_code(preferred_country),
                ip_address=ip_address,
                detection_method="user_preference_fallback",
                confidence_score=self.config.fallback_confidence_score,
                detected_at=time.time()
            )

        # Strategy 3: IP range mapping fallback
        for ip_prefix, country_code in self.ip_range_mappings.items():
            if ip_address.startswith(ip_prefix):
                logger.info(
                    f"Using IP range mapping fallback for {ip_address}: {country_code}",
                    extra={"correlation_id": correlation}
                )

                metrics_collector.increment_counter(
                    "geolocation_resilience_fallback",
                    tags={"strategy": "ip_range_mapping", "result": "success"}
                )

                return GeolocationResult(
                    country_code=country_code,
                    country_name=self._get_country_name(country_code),
                    continent_code=self._get_continent_code(country_code),
                    ip_address=ip_address,
                    detection_method="ip_range_fallback",
                    confidence_score=self.config.fallback_confidence_score,
                    detected_at=time.time()
                )

        # Strategy 4: Default country fallback
        logger.warning(
            f"Using default country fallback for {ip_address}: {self.config.fallback_country}",
            extra={"correlation_id": correlation}
        )

        metrics_collector.increment_counter(
            "geolocation_resilience_fallback",
            tags={"strategy": "default_country", "result": "success"}
        )

        return GeolocationResult(
            country_code=self.config.fallback_country,
            country_name=self._get_country_name(self.config.fallback_country),
            continent_code=self._get_continent_code(self.config.fallback_country),
            ip_address=ip_address,
            detection_method="default_country_fallback",
            confidence_score=self.config.fallback_confidence_score,
            detected_at=time.time()
        )

    def _get_country_name(self, country_code: str) -> str:
        """Get country name from country code."""
        country_names = {
            "NG": "Nigeria",
            "US": "United States",
            "GB": "United Kingdom",
            "ZA": "South Africa",
            "EG": "Egypt",
            "GH": "Ghana",
            "KE": "Kenya",
        }
        return country_names.get(country_code, "Unknown")

    def _get_continent_code(self, country_code: str) -> str:
        """Get continent code from country code."""
        continent_mapping = {
            "NG": "AF", "ZA": "AF", "EG": "AF", "GH": "AF", "KE": "AF",
            "US": "NA", "GB": "EU",
        }
        return continent_mapping.get(country_code, "UN")

    def get_resilience_stats(self) -> Dict[str, Any]:
        """Get resilience manager statistics."""
        circuit_breaker_stats = self.circuit_breaker.get_stats()

        return {
            "config": {
                "max_retries": self.config.max_retries,
                "base_delay": self.config.base_delay,
                "max_delay": self.config.max_delay,
                "fallback_country": self.config.fallback_country,
                "enable_circuit_breaker": self.config.enable_circuit_breaker,
            },
            "circuit_breaker": circuit_breaker_stats,
            "fallback_cache_size": len(self.fallback_cache),
            "ip_range_mappings_count": len(self.ip_range_mappings),
        }

    def clear_fallback_cache(self):
        """Clear the fallback cache."""
        self.fallback_cache.clear()
        logger.info("Cleared geolocation fallback cache")


# Global resilience manager instance
_resilience_manager: Optional[GeolocationResilienceManager] = None


def get_geolocation_resilience_manager() -> GeolocationResilienceManager:
    """Get the global geolocation resilience manager instance."""
    global _resilience_manager

    if _resilience_manager is None:
        _resilience_manager = GeolocationResilienceManager()

    return _resilience_manager
