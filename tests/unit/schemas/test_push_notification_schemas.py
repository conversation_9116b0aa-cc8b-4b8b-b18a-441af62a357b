"""
Unit tests for push notification schemas.

This module provides comprehensive unit tests for push notification schema classes:
- DeviceToken schemas: Device registration and management with platform validation
- NotificationTemplate schemas: Template management with variable substitution
- NotificationDelivery schemas: Delivery tracking and status reporting
- NotificationPreference schemas: User notification preference management
- FCM schemas: Firebase Cloud Messaging request/response validation

Implements Task 2.3.2 Phase 6 requirements with >80% test coverage.
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4
from pydantic import ValidationError

from app.schemas.push_notification_schemas import (
    # Device token schemas
    DeviceTokenCreate, DeviceTokenUpdate, DeviceTokenResponse,
    # Template schemas
    NotificationTemplateCreate, NotificationTemplateUpdate, NotificationTemplateResponse,
    # Notification sending schemas
    NotificationSendRequest, NotificationSendResponse, NotificationBatchSendRequest,
    # Preference schemas
    NotificationPreferenceUpdate, NotificationPreferenceResponse,
    # FCM schemas
    FCMMessageRequest, FCMMessageResponse
)
from app.models.push_notification_models import (
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)


class TestDeviceTokenSchemas:
    """Test DeviceToken schema validation and serialization."""

    def test_device_token_create_valid(self):
        """Test valid device token creation schema."""
        data = {
            "token": "fcm_token_123456789",
            "platform": DevicePlatform.ANDROID,
            "device_id": "device_android_123",
            "device_name": "Samsung Galaxy S21",
            "app_version": "1.0.0",
            "os_version": "Android 12",
            "registration_ip": "***********",
            "user_agent": "MyApp/1.0.0 (Android 12; Samsung Galaxy S21)",
            "device_metadata": {
                "manufacturer": "Samsung",
                "model": "Galaxy S21",
                "screen_resolution": "1080x2400"
            }
        }
        
        schema = DeviceTokenCreate(**data)
        assert schema.token == "fcm_token_123456789"
        assert schema.platform == DevicePlatform.ANDROID
        assert schema.device_id == "device_android_123"
        assert schema.device_name == "Samsung Galaxy S21"
        assert schema.device_metadata["manufacturer"] == "Samsung"

    def test_device_token_create_minimal(self):
        """Test device token creation with minimal required fields."""
        data = {
            "token": "fcm_token_minimal",
            "platform": DevicePlatform.IOS,
            "device_id": "device_ios_minimal"
        }
        
        schema = DeviceTokenCreate(**data)
        assert schema.token == "fcm_token_minimal"
        assert schema.platform == DevicePlatform.IOS
        assert schema.device_id == "device_ios_minimal"
        assert schema.device_name is None
        assert schema.device_metadata == {}

    def test_device_token_create_invalid_platform(self):
        """Test device token creation with invalid platform."""
        data = {
            "token": "fcm_token_123",
            "platform": "invalid_platform",
            "device_id": "device_123"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DeviceTokenCreate(**data)
        
        assert "platform" in str(exc_info.value)

    def test_device_token_create_empty_token(self):
        """Test device token creation with empty token."""
        data = {
            "token": "",
            "platform": DevicePlatform.ANDROID,
            "device_id": "device_123"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DeviceTokenCreate(**data)
        
        assert "token" in str(exc_info.value)

    def test_device_token_update_partial(self):
        """Test device token update with partial data."""
        data = {
            "device_name": "Updated Device Name",
            "app_version": "2.0.0"
        }
        
        schema = DeviceTokenUpdate(**data)
        assert schema.device_name == "Updated Device Name"
        assert schema.app_version == "2.0.0"
        assert schema.os_version is None

    def test_device_token_response_serialization(self):
        """Test device token response serialization."""
        data = {
            "id": uuid4(),
            "user_id": 1,
            "token": "fcm_token_response",
            "platform": DevicePlatform.WEB,
            "device_id": "device_web_123",
            "device_name": "Chrome Browser",
            "is_active": True,
            "is_validated": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "last_used_at": datetime.now(timezone.utc)
        }
        
        schema = DeviceTokenResponse(**data)
        assert str(schema.id) == str(data["id"])
        assert schema.user_id == 1
        assert schema.platform == DevicePlatform.WEB
        assert schema.is_active is True


class TestNotificationTemplateSchemas:
    """Test NotificationTemplate schema validation and serialization."""

    def test_template_create_valid(self):
        """Test valid template creation schema."""
        data = {
            "name": "Welcome Notification",
            "category": NotificationCategory.AUTHENTICATION,
            "title_template": "Welcome {{user_name}}!",
            "body_template": "Hello {{user_name}}, welcome to {{app_name}}!",
            "required_variables": ["user_name", "app_name"],
            "description": "Welcome notification for new users",
            "template_metadata": {
                "tags": ["onboarding", "welcome"],
                "priority": "high"
            }
        }
        
        schema = NotificationTemplateCreate(**data)
        assert schema.name == "Welcome Notification"
        assert schema.category == NotificationCategory.AUTHENTICATION
        assert schema.title_template == "Welcome {{user_name}}!"
        assert schema.required_variables == ["user_name", "app_name"]

    def test_template_create_minimal(self):
        """Test template creation with minimal required fields."""
        data = {
            "name": "Minimal Template",
            "category": NotificationCategory.SYSTEM,
            "title_template": "System Notification",
            "body_template": "This is a system notification."
        }
        
        schema = NotificationTemplateCreate(**data)
        assert schema.name == "Minimal Template"
        assert schema.required_variables == []
        assert schema.template_metadata == {}

    def test_template_create_invalid_category(self):
        """Test template creation with invalid category."""
        data = {
            "name": "Test Template",
            "category": "invalid_category",
            "title_template": "Test",
            "body_template": "Test message"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            NotificationTemplateCreate(**data)
        
        assert "category" in str(exc_info.value)

    def test_template_create_empty_name(self):
        """Test template creation with empty name."""
        data = {
            "name": "",
            "category": NotificationCategory.SYSTEM,
            "title_template": "Test",
            "body_template": "Test message"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            NotificationTemplateCreate(**data)
        
        assert "name" in str(exc_info.value)

    def test_template_update_partial(self):
        """Test template update with partial data."""
        data = {
            "description": "Updated description",
            "is_active": False
        }
        
        schema = NotificationTemplateUpdate(**data)
        assert schema.description == "Updated description"
        assert schema.is_active is False
        assert schema.name is None

    def test_template_response_serialization(self):
        """Test template response serialization."""
        data = {
            "id": uuid4(),
            "name": "Response Template",
            "category": NotificationCategory.BOOKING,
            "title_template": "Booking Confirmed",
            "body_template": "Your booking has been confirmed.",
            "version": 1,
            "is_active": True,
            "created_by": 1,
            "usage_count": 10,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        schema = NotificationTemplateResponse(**data)
        assert str(schema.id) == str(data["id"])
        assert schema.category == NotificationCategory.BOOKING
        assert schema.version == 1
        assert schema.usage_count == 10


class TestNotificationSendingSchemas:
    """Test notification sending schema validation."""

    def test_notification_send_request_with_template(self):
        """Test notification send request with template."""
        data = {
            "user_id": 1,
            "template_id": uuid4(),
            "template_variables": {
                "user_name": "John Doe",
                "app_name": "Culture Connect"
            },
            "priority": NotificationPriority.HIGH,
            "payload": {
                "action": "open_screen",
                "screen": "dashboard"
            }
        }
        
        schema = NotificationSendRequest(**data)
        assert schema.user_id == 1
        assert schema.template_id is not None
        assert schema.template_variables["user_name"] == "John Doe"
        assert schema.priority == NotificationPriority.HIGH

    def test_notification_send_request_direct_content(self):
        """Test notification send request with direct content."""
        data = {
            "device_token_ids": [uuid4(), uuid4()],
            "title": "Direct Notification",
            "body": "This is a direct notification message.",
            "priority": NotificationPriority.NORMAL
        }
        
        schema = NotificationSendRequest(**data)
        assert len(schema.device_token_ids) == 2
        assert schema.title == "Direct Notification"
        assert schema.body == "This is a direct notification message."
        assert schema.template_id is None

    def test_notification_send_request_scheduled(self):
        """Test notification send request with scheduling."""
        scheduled_time = datetime.now(timezone.utc)
        expires_time = datetime.now(timezone.utc)
        
        data = {
            "user_id": 1,
            "title": "Scheduled Notification",
            "body": "This is scheduled.",
            "scheduled_at": scheduled_time,
            "expires_at": expires_time
        }
        
        schema = NotificationSendRequest(**data)
        assert schema.scheduled_at == scheduled_time
        assert schema.expires_at == expires_time

    def test_notification_send_response(self):
        """Test notification send response schema."""
        data = {
            "delivery_ids": [uuid4(), uuid4()],
            "total_sent": 2,
            "failed_count": 0,
            "scheduled_count": 0
        }
        
        schema = NotificationSendResponse(**data)
        assert len(schema.delivery_ids) == 2
        assert schema.total_sent == 2
        assert schema.failed_count == 0

    def test_notification_batch_send_request(self):
        """Test batch notification send request."""
        notifications = [
            {
                "user_id": 1,
                "title": "Notification 1",
                "body": "First notification"
            },
            {
                "user_id": 2,
                "title": "Notification 2",
                "body": "Second notification"
            }
        ]
        
        data = {
            "notifications": notifications
        }
        
        schema = NotificationBatchSendRequest(**data)
        assert len(schema.notifications) == 2
        assert schema.notifications[0].title == "Notification 1"


class TestNotificationPreferenceSchemas:
    """Test notification preference schema validation."""

    def test_preference_update_partial(self):
        """Test preference update with partial data."""
        data = {
            "push_notifications_enabled": False,
            "promotional_notifications": NotificationFrequency.DISABLED,
            "dnd_enabled": True,
            "dnd_start_time": "22:00",
            "dnd_end_time": "08:00"
        }
        
        schema = NotificationPreferenceUpdate(**data)
        assert schema.push_notifications_enabled is False
        assert schema.promotional_notifications == NotificationFrequency.DISABLED
        assert schema.dnd_enabled is True
        assert schema.dnd_start_time == "22:00"

    def test_preference_update_invalid_frequency(self):
        """Test preference update with invalid frequency."""
        data = {
            "booking_notifications": "invalid_frequency"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            NotificationPreferenceUpdate(**data)
        
        assert "booking_notifications" in str(exc_info.value)

    def test_preference_response_serialization(self):
        """Test preference response serialization."""
        data = {
            "id": uuid4(),
            "user_id": 1,
            "push_notifications_enabled": True,
            "authentication_notifications": NotificationFrequency.IMMEDIATE,
            "booking_notifications": NotificationFrequency.IMMEDIATE,
            "payment_notifications": NotificationFrequency.IMMEDIATE,
            "promotional_notifications": NotificationFrequency.DAILY,
            "system_notifications": NotificationFrequency.IMMEDIATE,
            "security_notifications": NotificationFrequency.IMMEDIATE,
            "social_notifications": NotificationFrequency.HOURLY,
            "reminder_notifications": NotificationFrequency.IMMEDIATE,
            "dnd_enabled": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        schema = NotificationPreferenceResponse(**data)
        assert schema.user_id == 1
        assert schema.push_notifications_enabled is True
        assert schema.promotional_notifications == NotificationFrequency.DAILY


class TestFCMSchemas:
    """Test FCM schema validation."""

    def test_fcm_message_request(self):
        """Test FCM message request schema."""
        data = {
            "token": "fcm_token_123",
            "title": "FCM Test",
            "body": "This is an FCM test message",
            "data": {
                "action": "test",
                "value": "123"
            },
            "priority": "high"
        }
        
        schema = FCMMessageRequest(**data)
        assert schema.token == "fcm_token_123"
        assert schema.title == "FCM Test"
        assert schema.data["action"] == "test"
        assert schema.priority == "high"

    def test_fcm_message_response(self):
        """Test FCM message response schema."""
        data = {
            "message_id": "fcm_msg_123456",
            "success": True,
            "error_code": None,
            "error_message": None
        }
        
        schema = FCMMessageResponse(**data)
        assert schema.message_id == "fcm_msg_123456"
        assert schema.success is True
        assert schema.error_code is None

    def test_fcm_message_response_error(self):
        """Test FCM message response with error."""
        data = {
            "message_id": None,
            "success": False,
            "error_code": "INVALID_TOKEN",
            "error_message": "The FCM token is invalid"
        }
        
        schema = FCMMessageResponse(**data)
        assert schema.success is False
        assert schema.error_code == "INVALID_TOKEN"
        assert schema.error_message == "The FCM token is invalid"


class TestSchemaValidationEdgeCases:
    """Test schema validation edge cases and error handling."""

    def test_device_token_long_strings(self):
        """Test device token with very long strings."""
        data = {
            "token": "a" * 1000,  # Very long token
            "platform": DevicePlatform.ANDROID,
            "device_id": "device_123",
            "device_name": "b" * 500  # Very long device name
        }
        
        # Should handle long strings appropriately
        schema = DeviceTokenCreate(**data)
        assert len(schema.token) == 1000
        assert len(schema.device_name) == 500

    def test_template_special_characters(self):
        """Test template with special characters."""
        data = {
            "name": "Template with émojis 🎉",
            "category": NotificationCategory.SOCIAL,
            "title_template": "Hello {{user_name}} 👋",
            "body_template": "Welcome to our app! 🚀 Enjoy your experience 😊"
        }
        
        schema = NotificationTemplateCreate(**data)
        assert "émojis" in schema.name
        assert "👋" in schema.title_template
        assert "🚀" in schema.body_template

    def test_metadata_size_limits(self):
        """Test metadata size handling."""
        large_metadata = {f"key_{i}": f"value_{i}" * 100 for i in range(100)}
        
        data = {
            "token": "test_token",
            "platform": DevicePlatform.ANDROID,
            "device_id": "device_123",
            "device_metadata": large_metadata
        }
        
        # Should handle large metadata appropriately
        schema = DeviceTokenCreate(**data)
        assert len(schema.device_metadata) == 100
