"""
Unit tests for cache infrastructure.

Tests for Task 3.2.3 cache infrastructure including:
- CacheManager Redis operations and compression
- OptimizationCacheService specialized caching for marketplace data
- CacheMetricsCollector performance monitoring
- Cache decorators and key generation
- Error handling and connection management

Implements comprehensive test coverage with >80% requirement for
production-grade cache infrastructure functionality.
"""

import pytest
import json
import gzip
from datetime import datetime
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from app.core.cache import (
    CacheManager, OptimizationCacheService, CacheMetricsCollector,
    CacheConfig, cache_result, get_cache_manager, get_optimization_cache
)
from app.schemas.infrastructure_optimization import CacheType


@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    redis_mock = AsyncMock()
    redis_mock.ping = AsyncMock(return_value=True)
    redis_mock.get = AsyncMock()
    redis_mock.set = AsyncMock(return_value=True)
    redis_mock.setex = AsyncMock(return_value=True)
    redis_mock.delete = AsyncMock(return_value=1)
    redis_mock.keys = AsyncMock(return_value=[])
    redis_mock.exists = AsyncMock(return_value=1)
    redis_mock.ttl = AsyncMock(return_value=3600)
    redis_mock.close = AsyncMock()
    return redis_mock


@pytest.fixture
def cache_manager(mock_redis):
    """Cache manager fixture."""
    manager = CacheManager(mock_redis)
    return manager


@pytest.fixture
def optimization_cache(cache_manager):
    """Optimization cache service fixture."""
    return OptimizationCacheService(cache_manager)


class TestCacheMetricsCollector:
    """Test CacheMetricsCollector."""

    def test_metrics_collector_initialization(self):
        """Test metrics collector initialization."""
        collector = CacheMetricsCollector()
        
        assert collector.hits == 0
        assert collector.misses == 0
        assert collector.operations == 0
        assert collector.total_response_time == 0.0
        assert collector.max_response_time == 0.0
        assert isinstance(collector.start_time, datetime)

    def test_record_hit(self):
        """Test recording cache hit."""
        collector = CacheMetricsCollector()
        
        collector.record_hit(0.005)  # 5ms response time
        
        assert collector.hits == 1
        assert collector.misses == 0
        assert collector.operations == 1
        assert collector.total_response_time == 0.005
        assert collector.max_response_time == 0.005

    def test_record_miss(self):
        """Test recording cache miss."""
        collector = CacheMetricsCollector()
        
        collector.record_miss(0.010)  # 10ms response time
        
        assert collector.hits == 0
        assert collector.misses == 1
        assert collector.operations == 1
        assert collector.total_response_time == 0.010
        assert collector.max_response_time == 0.010

    def test_hit_rate_calculation(self):
        """Test hit rate calculation."""
        collector = CacheMetricsCollector()
        
        # Record some hits and misses
        collector.record_hit(0.005)
        collector.record_hit(0.003)
        collector.record_hit(0.007)
        collector.record_miss(0.010)
        
        # Hit rate should be 75% (3 hits out of 4 operations)
        assert collector.hit_rate == 75.0

    def test_hit_rate_no_operations(self):
        """Test hit rate with no operations."""
        collector = CacheMetricsCollector()
        
        assert collector.hit_rate == 0.0

    def test_avg_response_time_calculation(self):
        """Test average response time calculation."""
        collector = CacheMetricsCollector()
        
        collector.record_hit(0.005)
        collector.record_hit(0.015)
        
        # Average should be (0.005 + 0.015) / 2 = 0.010
        assert collector.avg_response_time == 0.010

    def test_get_metrics(self):
        """Test getting metrics summary."""
        collector = CacheMetricsCollector()
        
        collector.record_hit(0.005)
        collector.record_miss(0.010)
        
        metrics = collector.get_metrics()
        
        assert metrics["hits"] == 1
        assert metrics["misses"] == 1
        assert metrics["operations"] == 2
        assert metrics["hit_rate"] == 50.0
        assert metrics["avg_response_time_ms"] == 7.5  # (5 + 10) / 2
        assert metrics["max_response_time_ms"] == 10.0
        assert "uptime_seconds" in metrics


class TestCacheManager:
    """Test CacheManager."""

    @pytest.mark.asyncio
    async def test_initialize_success(self, mock_redis):
        """Test successful cache manager initialization."""
        manager = CacheManager()
        
        with patch('app.core.cache.redis.from_url', return_value=mock_redis):
            await manager.initialize()
        
        assert manager.redis == mock_redis
        mock_redis.ping.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_failure(self):
        """Test cache manager initialization failure."""
        manager = CacheManager()
        
        with patch('app.core.cache.redis.from_url') as mock_from_url:
            mock_redis = AsyncMock()
            mock_redis.ping.side_effect = Exception("Connection failed")
            mock_from_url.return_value = mock_redis
            
            with pytest.raises(Exception) as exc_info:
                await manager.initialize()
            
            assert "Connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_close(self, cache_manager, mock_redis):
        """Test closing cache manager."""
        await cache_manager.close()
        mock_redis.close.assert_called_once()

    def test_generate_key_simple(self, cache_manager):
        """Test simple key generation."""
        key = cache_manager._generate_key("test", "123")
        assert key == "test:123"

    def test_generate_key_with_kwargs(self, cache_manager):
        """Test key generation with kwargs."""
        key = cache_manager._generate_key("test", "123", category="music", location="lagos")
        assert "test:123" in key
        assert "category:music" in key
        assert "location:lagos" in key

    def test_generate_key_long_key_hashing(self, cache_manager):
        """Test key generation with long keys that get hashed."""
        long_identifier = "x" * 300  # Very long identifier
        key = cache_manager._generate_key("test", long_identifier)
        
        # Should be hashed due to length
        assert key.startswith("test:hash:")
        assert len(key) <= CacheConfig.MAX_KEY_LENGTH

    def test_compress_data(self, cache_manager):
        """Test data compression."""
        # Small data should not be compressed
        small_data = b"small"
        compressed = cache_manager._compress_data(small_data)
        assert compressed == small_data

        # Large data should be compressed
        large_data = b"x" * 2000  # Larger than compression threshold
        compressed = cache_manager._compress_data(large_data)
        assert len(compressed) < len(large_data)

    def test_decompress_data(self, cache_manager):
        """Test data decompression."""
        # Uncompressed data should pass through
        uncompressed = b"test data"
        result = cache_manager._decompress_data(uncompressed)
        assert result == uncompressed

        # Compressed data should be decompressed
        original_data = b"x" * 2000
        compressed = gzip.compress(original_data)
        decompressed = cache_manager._decompress_data(compressed)
        assert decompressed == original_data

    def test_serialize_value_string(self, cache_manager):
        """Test serializing string value."""
        value = "test string"
        serialized = cache_manager._serialize_value(value)
        
        # Should be JSON serialized
        expected = json.dumps(value).encode('utf-8')
        assert serialized == expected

    def test_serialize_value_dict(self, cache_manager):
        """Test serializing dictionary value."""
        value = {"key": "value", "number": 123}
        serialized = cache_manager._serialize_value(value)
        
        # Should be JSON serialized
        expected = json.dumps(value, default=str).encode('utf-8')
        assert serialized == expected

    def test_serialize_value_too_large(self, cache_manager):
        """Test serializing value that's too large."""
        # Create a value that exceeds the size limit
        large_value = "x" * (CacheConfig.MAX_VALUE_SIZE + 1)
        
        with pytest.raises(ValueError) as exc_info:
            cache_manager._serialize_value(large_value)
        
        assert "Value too large for cache" in str(exc_info.value)

    def test_deserialize_value(self, cache_manager):
        """Test deserializing value."""
        original_value = {"key": "value", "number": 123}
        serialized = json.dumps(original_value).encode('utf-8')
        
        deserialized = cache_manager._deserialize_value(serialized)
        assert deserialized == original_value

    @pytest.mark.asyncio
    async def test_get_cache_hit(self, cache_manager, mock_redis):
        """Test cache get with hit."""
        # Mock Redis response
        test_data = {"key": "value"}
        serialized_data = json.dumps(test_data).encode('utf-8')
        mock_redis.get.return_value = serialized_data
        
        result = await cache_manager.get("test:key")
        
        assert result == test_data
        mock_redis.get.assert_called_once_with("test:key")
        assert cache_manager.metrics.hits == 1
        assert cache_manager.metrics.misses == 0

    @pytest.mark.asyncio
    async def test_get_cache_miss(self, cache_manager, mock_redis):
        """Test cache get with miss."""
        # Mock Redis response
        mock_redis.get.return_value = None
        
        result = await cache_manager.get("test:key")
        
        assert result is None
        mock_redis.get.assert_called_once_with("test:key")
        assert cache_manager.metrics.hits == 0
        assert cache_manager.metrics.misses == 1

    @pytest.mark.asyncio
    async def test_set_with_ttl(self, cache_manager, mock_redis):
        """Test cache set with TTL."""
        test_data = {"key": "value"}
        
        result = await cache_manager.set("test:key", test_data, ttl=3600)
        
        assert result is True
        mock_redis.setex.assert_called_once()
        # Verify the call was made with correct parameters
        call_args = mock_redis.setex.call_args
        assert call_args[0][0] == "test:key"  # key
        assert call_args[0][1] == 3600  # ttl

    @pytest.mark.asyncio
    async def test_set_without_ttl(self, cache_manager, mock_redis):
        """Test cache set without TTL."""
        test_data = {"key": "value"}
        
        result = await cache_manager.set("test:key", test_data)
        
        assert result is True
        mock_redis.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete(self, cache_manager, mock_redis):
        """Test cache delete."""
        mock_redis.delete.return_value = 1
        
        result = await cache_manager.delete("test:key")
        
        assert result is True
        mock_redis.delete.assert_called_once_with("test:key")

    @pytest.mark.asyncio
    async def test_delete_pattern(self, cache_manager, mock_redis):
        """Test cache delete pattern."""
        mock_redis.keys.return_value = ["test:key1", "test:key2"]
        mock_redis.delete.return_value = 2
        
        result = await cache_manager.delete_pattern("test:*")
        
        assert result == 2
        mock_redis.keys.assert_called_once_with("test:*")
        mock_redis.delete.assert_called_once_with("test:key1", "test:key2")

    @pytest.mark.asyncio
    async def test_exists(self, cache_manager, mock_redis):
        """Test cache exists."""
        mock_redis.exists.return_value = 1
        
        result = await cache_manager.exists("test:key")
        
        assert result is True
        mock_redis.exists.assert_called_once_with("test:key")

    @pytest.mark.asyncio
    async def test_get_ttl(self, cache_manager, mock_redis):
        """Test getting TTL."""
        mock_redis.ttl.return_value = 3600
        
        result = await cache_manager.get_ttl("test:key")
        
        assert result == 3600
        mock_redis.ttl.assert_called_once_with("test:key")

    def test_get_metrics(self, cache_manager):
        """Test getting cache metrics."""
        # Record some operations
        cache_manager.metrics.record_hit(0.005)
        cache_manager.metrics.record_miss(0.010)
        
        metrics = cache_manager.get_metrics()
        
        assert "hits" in metrics
        assert "misses" in metrics
        assert "hit_rate" in metrics
        assert "avg_response_time_ms" in metrics


class TestOptimizationCacheService:
    """Test OptimizationCacheService."""

    @pytest.mark.asyncio
    async def test_get_optimization_score(self, optimization_cache, cache_manager, mock_redis):
        """Test getting optimization score from cache."""
        service_id = uuid4()
        test_data = {"score": 85.5, "recommendations": ["test"]}
        serialized_data = json.dumps(test_data).encode('utf-8')
        mock_redis.get.return_value = serialized_data
        
        result = await optimization_cache.get_optimization_score(service_id)
        
        assert result == test_data
        mock_redis.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_optimization_score(self, optimization_cache, cache_manager, mock_redis):
        """Test setting optimization score in cache."""
        service_id = uuid4()
        score_data = {"score": 85.5, "recommendations": ["test"]}
        
        result = await optimization_cache.set_optimization_score(service_id, score_data)
        
        assert result is True
        mock_redis.setex.assert_called_once()
        # Verify TTL is set correctly
        call_args = mock_redis.setex.call_args
        assert call_args[0][1] == CacheConfig.OPTIMIZATION_SCORE_TTL

    @pytest.mark.asyncio
    async def test_get_market_intelligence(self, optimization_cache, cache_manager, mock_redis):
        """Test getting market intelligence from cache."""
        test_data = {"market_size": "large", "competition": "medium"}
        serialized_data = json.dumps(test_data).encode('utf-8')
        mock_redis.get.return_value = serialized_data
        
        result = await optimization_cache.get_market_intelligence("music", "lagos")
        
        assert result == test_data
        mock_redis.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_market_intelligence(self, optimization_cache, cache_manager, mock_redis):
        """Test setting market intelligence in cache."""
        data = {"market_size": "large", "competition": "medium"}
        
        result = await optimization_cache.set_market_intelligence("music", data, "lagos")
        
        assert result is True
        mock_redis.setex.assert_called_once()
        # Verify TTL is set correctly
        call_args = mock_redis.setex.call_args
        assert call_args[0][1] == CacheConfig.MARKET_INTELLIGENCE_TTL

    @pytest.mark.asyncio
    async def test_invalidate_service_cache(self, optimization_cache, cache_manager, mock_redis):
        """Test invalidating service cache."""
        service_id = uuid4()
        mock_redis.keys.return_value = ["opt:service1", "perf:service1"]
        mock_redis.delete.return_value = 2
        
        result = await optimization_cache.invalidate_service_cache(service_id)
        
        assert result is True
        # Should call keys multiple times for different patterns
        assert mock_redis.keys.call_count >= 1

    @pytest.mark.asyncio
    async def test_invalidate_vendor_cache(self, optimization_cache, cache_manager, mock_redis):
        """Test invalidating vendor cache."""
        vendor_id = uuid4()
        mock_redis.keys.return_value = ["vendor:123"]
        mock_redis.delete.return_value = 1
        
        result = await optimization_cache.invalidate_vendor_cache(vendor_id)
        
        assert result is True
        mock_redis.keys.assert_called_once()


class TestCacheDecorator:
    """Test cache result decorator."""

    @pytest.mark.asyncio
    async def test_cache_result_decorator_hit(self):
        """Test cache decorator with cache hit."""
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = "cached_result"
        mock_cache_manager.set.return_value = True
        mock_cache_manager._generate_key.return_value = "test:key"
        
        @cache_result(CacheType.OPTIMIZATION_SCORE, ttl=3600)
        async def test_function(arg1, arg2):
            return f"result_{arg1}_{arg2}"
        
        with patch('app.core.cache.cache_manager', mock_cache_manager):
            result = await test_function("a", "b")
        
        assert result == "cached_result"
        mock_cache_manager.get.assert_called_once()
        mock_cache_manager.set.assert_not_called()  # Should not set if hit

    @pytest.mark.asyncio
    async def test_cache_result_decorator_miss(self):
        """Test cache decorator with cache miss."""
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None  # Cache miss
        mock_cache_manager.set.return_value = True
        mock_cache_manager._generate_key.return_value = "test:key"
        
        @cache_result(CacheType.OPTIMIZATION_SCORE, ttl=3600)
        async def test_function(arg1, arg2):
            return f"result_{arg1}_{arg2}"
        
        with patch('app.core.cache.cache_manager', mock_cache_manager):
            result = await test_function("a", "b")
        
        assert result == "result_a_b"
        mock_cache_manager.get.assert_called_once()
        mock_cache_manager.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_result_decorator_custom_key_generator(self):
        """Test cache decorator with custom key generator."""
        mock_cache_manager = AsyncMock()
        mock_cache_manager.get.return_value = None
        mock_cache_manager.set.return_value = True
        
        def custom_key_gen(arg1, arg2):
            return f"custom:{arg1}:{arg2}"
        
        @cache_result(CacheType.OPTIMIZATION_SCORE, key_generator=custom_key_gen)
        async def test_function(arg1, arg2):
            return f"result_{arg1}_{arg2}"
        
        with patch('app.core.cache.cache_manager', mock_cache_manager):
            result = await test_function("a", "b")
        
        assert result == "result_a_b"
        mock_cache_manager.get.assert_called_once_with("custom:a:b")


class TestCacheUtilityFunctions:
    """Test cache utility functions."""

    @pytest.mark.asyncio
    async def test_get_cache_manager(self):
        """Test getting cache manager."""
        with patch('app.core.cache.cache_manager') as mock_manager:
            mock_manager.redis = None
            mock_manager.initialize = AsyncMock()
            
            result = await get_cache_manager()
            
            assert result == mock_manager
            mock_manager.initialize.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_optimization_cache(self):
        """Test getting optimization cache."""
        with patch('app.core.cache.optimization_cache') as mock_opt_cache:
            mock_opt_cache.cache.redis = None
            mock_opt_cache.cache.initialize = AsyncMock()
            
            result = await get_optimization_cache()
            
            assert result == mock_opt_cache
            mock_opt_cache.cache.initialize.assert_called_once()
