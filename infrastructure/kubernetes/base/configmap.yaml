# Culture Connect Backend - Kubernetes ConfigMap
# Application configuration for production deployment

apiVersion: v1
kind: ConfigMap
metadata:
  name: culture-connect-config
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
data:
  # Environment Configuration
  CC_ENVIRONMENT: "production"
  CC_LOG_LEVEL: "INFO"
  CC_DEBUG: "false"
  CC_WORKERS: "4"
  CC_MAX_CONNECTIONS: "1000"
  CC_KEEPALIVE_TIMEOUT: "65"
  CC_GRACEFUL_TIMEOUT: "30"

  # API Configuration
  CC_API_VERSION: "v1"
  CC_API_TITLE: "Culture Connect Backend API"
  CC_API_DESCRIPTION: "Culture Connect Backend API - Production Environment"
  CC_FRONTEND_URL: "https://cultureconnect.ng"
  CC_PWA_URL: "https://app.cultureconnect.ng"
  CC_ADMIN_URL: "https://admin.cultureconnect.ng"

  # Database Configuration
  CC_POSTGRES_DB: "culture_connect_db"
  CC_POSTGRES_USER: "culture_connect"
  CC_POSTGRES_PORT: "5432"
  CC_DATABASE_POOL_SIZE: "20"
  CC_DATABASE_MAX_OVERFLOW: "30"
  CC_DATABASE_POOL_TIMEOUT: "30"
  CC_DATABASE_POOL_RECYCLE: "3600"

  # Redis Configuration
  CC_REDIS_PORT: "6379"
  CC_REDIS_DB: "0"
  CC_REDIS_MAX_CONNECTIONS: "100"
  CC_REDIS_RETRY_ON_TIMEOUT: "true"
  CC_REDIS_SOCKET_KEEPALIVE: "true"

  # JWT Configuration
  CC_JWT_ALGORITHM: "HS256"
  CC_JWT_ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  CC_JWT_REFRESH_TOKEN_EXPIRE_DAYS: "7"

  # Payment Provider Configuration
  CC_PAYSTACK_BASE_URL: "https://api.paystack.co"
  CC_STRIPE_API_VERSION: "2023-10-16"
  CC_BUSHA_BASE_URL: "https://api.busha.co"

  # AI/ML Configuration
  CC_AIML_BASE_URL: "https://api.aimlapi.com"
  CC_AIML_MODEL: "gpt-4"
  CC_AIML_MAX_TOKENS: "1000"
  CC_AIML_TEMPERATURE: "0.7"

  # Geolocation Configuration
  CC_GEOIP_DATABASE_PATH: "/app/data/GeoLite2-Country.mmdb"
  CC_ENABLE_GEOLOCATION_ROUTING: "true"
  CC_GEOLOCATION_FALLBACK_COUNTRY: "NG"
  CC_GEOLOCATION_TIMEOUT_MS: "5000"
  CC_GEOLOCATION_MAX_RETRIES: "3"
  CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD: "10"
  CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT: "60"
  CC_GEOLOCATION_CACHE_TTL_HOURS: "24"
  CC_GEOLOCATION_CACHE_MAX_SIZE: "10000"
  CC_GEOLOCATION_CACHE_COMPRESSION: "true"
  CC_GEOLOCATION_REDIS_PREFIX: "geo:prod:"
  CC_GEOLOCATION_REDIS_TTL: "86400"

  # Monitoring Configuration
  CC_SENTRY_ENVIRONMENT: "production"
  CC_SENTRY_TRACES_SAMPLE_RATE: "0.1"
  CC_SENTRY_PROFILES_SAMPLE_RATE: "0.1"

  # Logging Configuration
  CC_LOG_FORMAT: "json"
  CC_LOG_DATE_FORMAT: "%Y-%m-%d %H:%M:%S"
  CC_LOG_FILE_PATH: "/app/logs/culture_connect.log"
  CC_LOG_MAX_BYTES: "*********"
  CC_LOG_BACKUP_COUNT: "5"

  # Email Configuration
  CC_SMTP_PORT: "587"
  CC_SMTP_USE_TLS: "true"
  CC_SMTP_USE_SSL: "false"
  CC_EMAIL_FROM: "<EMAIL>"
  CC_EMAIL_FROM_NAME: "Culture Connect"
  CC_EMAIL_TEMPLATE_DIR: "/app/templates/email"

  # Celery Configuration
  CC_CELERY_TASK_SERIALIZER: "json"
  CC_CELERY_RESULT_SERIALIZER: "json"
  CC_CELERY_ACCEPT_CONTENT: '["json"]'
  CC_CELERY_TIMEZONE: "UTC"
  CC_CELERY_ENABLE_UTC: "true"
  CELERY_WORKER_CONCURRENCY: "4"
  CELERY_WORKER_PREFETCH_MULTIPLIER: "1"
  CELERY_WORKER_MAX_TASKS_PER_CHILD: "1000"
  CELERY_WORKER_DISABLE_RATE_LIMITS: "false"

  # Feature Flags
  CC_ENABLE_PROMOTIONAL_SYSTEM: "true"
  CC_ENABLE_CRYPTO_PAYMENTS: "true"
  CC_ENABLE_AI_OPTIMIZATION: "true"
  CC_ENABLE_GEOLOCATION_ROUTING: "true"
  CC_ENABLE_WEBSOCKET_COMMUNICATION: "true"
  CC_ENABLE_BACKGROUND_JOBS: "true"
  CC_ENABLE_ANALYTICS_SYSTEM: "true"
  CC_ENABLE_PERFORMANCE_MONITORING: "true"

  # SSL/TLS Configuration
  CC_SSL_ENABLED: "true"
  CC_SECURITY_HEADERS_ENABLED: "true"
  CC_HSTS_MAX_AGE: "********"
  CC_CONTENT_SECURITY_POLICY: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

  # Health Check Configuration
  CC_HEALTH_CHECK_INTERVAL: "30"
  CC_HEALTH_CHECK_TIMEOUT: "10"
  CC_HEALTH_CHECK_RETRIES: "3"

  # Performance Configuration
  CC_MAX_MEMORY_MB: "1024"
  CC_MAX_CPU_PERCENT: "80"
  CC_MAX_CONNECTIONS_TOTAL: "1000"

  # Backup Configuration
  CC_BACKUP_ENABLED: "true"
  CC_BACKUP_SCHEDULE: "0 2 * * *"
  CC_BACKUP_RETENTION_DAYS: "30"
  CC_BACKUP_COMPRESSION: "true"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: culture-connect-nginx-config
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: nginx-config
data:
  nginx.conf: |
    upstream api_backend {
        least_conn;
        server culture-connect-api:8000 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        server_name api.cultureconnect.ng;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.cultureconnect.ng;

        ssl_certificate /etc/ssl/certs/tls.crt;
        ssl_certificate_key /etc/ssl/private/tls.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

        location / {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        location /health {
            proxy_pass http://api_backend/health;
            access_log off;
        }

        location /metrics {
            proxy_pass http://api_backend/metrics;
            allow 10.0.0.0/8;
            deny all;
        }
    }
