"""
Booking models for Culture Connect Backend API.

This module defines comprehensive booking-related models for reservation management,
booking status tracking, vendor approval workflow, and customer communication.

Implements Task 4.1.1 requirements for booking workflow implementation with:
- Complete booking lifecycle management
- Vendor approval system integration
- Customer notification workflows
- Booking communication system
- Real-time availability checking
- Audit trail and status history

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import enum
from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Date, Time, Text, ForeignKey,
    Numeric, Enum, JSON, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit


class BookingStatus(str, enum.Enum):
    """Enumeration of booking statuses for lifecycle management."""
    PENDING = "pending"                    # Initial booking request
    VENDOR_REVIEW = "vendor_review"        # Awaiting vendor approval
    CONFIRMED = "confirmed"                # Vendor approved, booking confirmed
    PAYMENT_PENDING = "payment_pending"    # Awaiting payment completion
    PAID = "paid"                         # Payment completed
    IN_PROGRESS = "in_progress"           # Service is being delivered
    COMPLETED = "completed"               # Service completed successfully
    CANCELLED_BY_CUSTOMER = "cancelled_by_customer"  # Customer cancelled
    CANCELLED_BY_VENDOR = "cancelled_by_vendor"      # Vendor cancelled
    REJECTED = "rejected"                 # Vendor rejected booking
    REFUNDED = "refunded"                # Payment refunded
    DISPUTED = "disputed"                # Booking under dispute
    NO_SHOW = "no_show"                  # Customer didn't show up


class VendorResponseType(str, enum.Enum):
    """Enumeration of vendor response types."""
    APPROVED = "approved"
    REJECTED = "rejected"
    MODIFICATION_REQUESTED = "modification_requested"
    COUNTER_OFFER = "counter_offer"


class BookingPriority(str, enum.Enum):
    """Enumeration of booking priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class CommunicationType(str, enum.Enum):
    """Enumeration of communication types."""
    MESSAGE = "message"
    SYSTEM_NOTIFICATION = "system_notification"
    STATUS_UPDATE = "status_update"
    MODIFICATION_REQUEST = "modification_request"
    PAYMENT_REMINDER = "payment_reminder"
    REVIEW_REQUEST = "review_request"


class ModificationType(str, enum.Enum):
    """Enumeration of booking modification types."""
    DATE_CHANGE = "date_change"
    TIME_CHANGE = "time_change"
    PARTICIPANT_COUNT = "participant_count"
    SERVICE_UPGRADE = "service_upgrade"
    SPECIAL_REQUIREMENTS = "special_requirements"
    PRICING_ADJUSTMENT = "pricing_adjustment"


class Booking(BaseModelWithAudit):
    """
    Core booking model for service reservations.

    This model manages the complete booking lifecycle including customer requests,
    vendor approvals, payment processing, and service delivery tracking.
    """

    __tablename__ = "bookings"

    # Core booking information
    customer_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Customer who made the booking"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Vendor providing the service"
    )

    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Service being booked"
    )

    availability_id = Column(
        Integer,
        ForeignKey("service_availability.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Specific availability slot booked"
    )

    # Booking reference and tracking
    booking_reference = Column(
        String(20),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique booking reference number"
    )

    external_reference = Column(
        String(100),
        nullable=True,
        index=True,
        doc="External system reference (payment gateway, etc.)"
    )

    # Booking details
    booking_date = Column(
        Date,
        nullable=False,
        index=True,
        doc="Date of the service"
    )

    booking_time = Column(
        Time,
        nullable=True,
        doc="Time of the service (null for all-day services)"
    )

    duration_hours = Column(
        Numeric(4, 2),
        nullable=True,
        doc="Service duration in hours"
    )

    participant_count = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Number of participants"
    )

    # Status and workflow
    status = Column(
        Enum(BookingStatus),
        nullable=False,
        default=BookingStatus.PENDING,
        index=True,
        doc="Current booking status"
    )

    priority = Column(
        Enum(BookingPriority),
        nullable=False,
        default=BookingPriority.NORMAL,
        index=True,
        doc="Booking priority level"
    )

    # Vendor response management
    vendor_response_type = Column(
        Enum(VendorResponseType),
        nullable=True,
        index=True,
        doc="Type of vendor response"
    )

    vendor_response_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When vendor responded to booking"
    )

    vendor_response_deadline = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Deadline for vendor response"
    )

    vendor_notes = Column(
        Text,
        nullable=True,
        doc="Vendor notes and comments"
    )

    # Customer requirements and preferences
    special_requirements = Column(
        Text,
        nullable=True,
        doc="Customer special requirements"
    )

    customer_notes = Column(
        Text,
        nullable=True,
        doc="Customer notes and preferences"
    )

    accessibility_requirements = Column(
        JSON,
        nullable=True,
        doc="Accessibility requirements and accommodations"
    )

    # Pricing and payment
    base_price = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Base service price"
    )

    additional_fees = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Additional fees and charges"
    )

    discount_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Discount amount applied"
    )

    total_amount = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Total booking amount"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        doc="Currency code (ISO 4217)"
    )

    commission_rate = Column(
        Numeric(5, 4),
        nullable=False,
        default=Decimal("0.15"),
        doc="Platform commission rate"
    )

    commission_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform commission amount"
    )

    vendor_payout = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Amount to be paid to vendor"
    )

    # Payment tracking
    payment_status = Column(
        String(50),
        nullable=False,
        default="pending",
        index=True,
        doc="Payment processing status"
    )

    payment_method = Column(
        String(50),
        nullable=True,
        doc="Payment method used"
    )

    payment_reference = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Payment gateway reference"
    )

    paid_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Payment completion timestamp"
    )

    # Service delivery tracking
    service_start_time = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Actual service start time"
    )

    service_end_time = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Actual service end time"
    )

    completion_confirmed_by_customer = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Customer confirmed service completion"
    )

    completion_confirmed_by_vendor = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Vendor confirmed service completion"
    )

    # Communication and notifications
    last_communication_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Last communication timestamp"
    )

    unread_messages_customer = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Unread messages count for customer"
    )

    unread_messages_vendor = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Unread messages count for vendor"
    )

    customer_notified_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last customer notification timestamp"
    )

    vendor_notified_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last vendor notification timestamp"
    )

    # Cancellation and refund
    cancelled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Cancellation timestamp"
    )

    cancelled_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who cancelled the booking"
    )

    cancellation_reason = Column(
        Text,
        nullable=True,
        doc="Reason for cancellation"
    )

    refund_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Refund amount processed"
    )

    refund_processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Refund processing timestamp"
    )

    # Review and rating
    customer_rating = Column(
        Numeric(3, 2),
        nullable=True,
        doc="Customer rating (1.00-5.00)"
    )

    customer_review = Column(
        Text,
        nullable=True,
        doc="Customer review text"
    )

    vendor_rating = Column(
        Numeric(3, 2),
        nullable=True,
        doc="Vendor rating of customer (1.00-5.00)"
    )

    vendor_review = Column(
        Text,
        nullable=True,
        doc="Vendor review of customer"
    )

    reviewed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Review submission timestamp"
    )

    # Metadata and tracking
    booking_source = Column(
        String(50),
        nullable=False,
        default="web",
        doc="Source of booking (web, mobile, api)"
    )

    user_agent = Column(
        String(500),
        nullable=True,
        doc="User agent string for tracking"
    )

    ip_address = Column(
        String(45),
        nullable=True,
        doc="IP address of booking request"
    )

    booking_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional booking metadata"
    )

    # Relationships
    customer = relationship(
        "User",
        foreign_keys=[customer_id],
        back_populates="customer_bookings",
        doc="Customer who made the booking"
    )

    vendor = relationship(
        "Vendor",
        back_populates="bookings",
        doc="Vendor providing the service"
    )

    service = relationship(
        "Service",
        back_populates="bookings",
        doc="Service being booked"
    )

    availability = relationship(
        "ServiceAvailability",
        back_populates="bookings",
        doc="Availability slot booked"
    )

    cancelled_by_user = relationship(
        "User",
        foreign_keys=[cancelled_by],
        doc="User who cancelled the booking"
    )

    status_history = relationship(
        "BookingStatusHistory",
        back_populates="booking",
        cascade="all, delete-orphan",
        order_by="BookingStatusHistory.created_at.desc()",
        doc="Booking status change history"
    )

    communications = relationship(
        "BookingCommunication",
        back_populates="booking",
        cascade="all, delete-orphan",
        order_by="BookingCommunication.created_at.desc()",
        doc="Booking communications"
    )

    modifications = relationship(
        "BookingModification",
        back_populates="booking",
        cascade="all, delete-orphan",
        order_by="BookingModification.created_at.desc()",
        doc="Booking modification requests"
    )

    # Enhanced communication system (Task 4.1.3)
    messages = relationship(
        "BookingMessage",
        back_populates="booking",
        cascade="all, delete-orphan",
        order_by="BookingMessage.created_at.desc()",
        doc="Enhanced booking messages with threading and delivery tracking"
    )

    # Payment relationships (implemented in Phase 1 - Payment & Transaction Management System)
    payments = relationship(
        "Payment",
        back_populates="booking",
        cascade="all, delete-orphan",
        doc="Payments for this booking"
    )

    escrow_accounts = relationship(
        "EscrowAccount",
        back_populates="booking",
        cascade="all, delete-orphan",
        doc="Escrow accounts for this booking"
    )

    # Review relationships (Task 4.4.1)
    review = relationship(
        "Review",
        back_populates="booking",
        uselist=False,
        cascade="all, delete-orphan",
        doc="Review for this booking (one-to-one)"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "participant_count > 0",
            name="check_participant_count_positive"
        ),
        CheckConstraint(
            "base_price >= 0",
            name="check_base_price_non_negative"
        ),
        CheckConstraint(
            "total_amount >= 0",
            name="check_total_amount_non_negative"
        ),
        CheckConstraint(
            "commission_rate >= 0.0 AND commission_rate <= 1.0",
            name="check_commission_rate_range"
        ),
        CheckConstraint(
            "customer_rating IS NULL OR (customer_rating >= 1.0 AND customer_rating <= 5.0)",
            name="check_customer_rating_range"
        ),
        CheckConstraint(
            "vendor_rating IS NULL OR (vendor_rating >= 1.0 AND vendor_rating <= 5.0)",
            name="check_vendor_rating_range"
        ),
        CheckConstraint(
            "unread_messages_customer >= 0",
            name="check_unread_messages_customer_non_negative"
        ),
        CheckConstraint(
            "unread_messages_vendor >= 0",
            name="check_unread_messages_vendor_non_negative"
        ),

        # Performance indexes
        Index("idx_booking_customer_status", "customer_id", "status"),
        Index("idx_booking_vendor_status", "vendor_id", "status"),
        Index("idx_booking_service_date", "service_id", "booking_date"),
        Index("idx_booking_date_status", "booking_date", "status"),
        Index("idx_booking_reference", "booking_reference"),
        Index("idx_booking_payment_status", "payment_status", "paid_at"),
        Index("idx_booking_vendor_response", "vendor_id", "vendor_response_deadline"),
        Index("idx_booking_priority_status", "priority", "status"),
        Index("idx_booking_communication", "last_communication_at", "status"),
        Index("idx_booking_completion", "completion_confirmed_by_customer", "completion_confirmed_by_vendor"),
    )

    def __repr__(self) -> str:
        """String representation of the booking."""
        return f"<Booking(id={self.id}, ref='{self.booking_reference}', status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if booking is in an active state."""
        active_statuses = {
            BookingStatus.PENDING,
            BookingStatus.VENDOR_REVIEW,
            BookingStatus.CONFIRMED,
            BookingStatus.PAYMENT_PENDING,
            BookingStatus.PAID,
            BookingStatus.IN_PROGRESS
        }
        return self.status in active_statuses

    @property
    def is_completed(self) -> bool:
        """Check if booking is completed."""
        return self.status == BookingStatus.COMPLETED

    @property
    def is_cancelled(self) -> bool:
        """Check if booking is cancelled."""
        cancelled_statuses = {
            BookingStatus.CANCELLED_BY_CUSTOMER,
            BookingStatus.CANCELLED_BY_VENDOR,
            BookingStatus.REJECTED
        }
        return self.status in cancelled_statuses

    @property
    def requires_vendor_response(self) -> bool:
        """Check if booking requires vendor response."""
        return (
            self.status in {BookingStatus.PENDING, BookingStatus.VENDOR_REVIEW} and
            self.vendor_response_type is None
        )

    @property
    def is_overdue_response(self) -> bool:
        """Check if vendor response is overdue."""
        if not self.vendor_response_deadline:
            return False
        return (
            self.requires_vendor_response and
            datetime.utcnow() > self.vendor_response_deadline.replace(tzinfo=None)
        )

    @property
    def can_be_cancelled(self) -> bool:
        """Check if booking can be cancelled."""
        cancellable_statuses = {
            BookingStatus.PENDING,
            BookingStatus.VENDOR_REVIEW,
            BookingStatus.CONFIRMED,
            BookingStatus.PAYMENT_PENDING,
            BookingStatus.PAID
        }
        return self.status in cancellable_statuses

    @property
    def can_be_modified(self) -> bool:
        """Check if booking can be modified."""
        modifiable_statuses = {
            BookingStatus.PENDING,
            BookingStatus.VENDOR_REVIEW,
            BookingStatus.CONFIRMED
        }
        return self.status in modifiable_statuses


class BookingStatusHistory(BaseModelWithAudit):
    """
    Booking status history model for audit trail.

    Tracks all status changes throughout the booking lifecycle for
    audit purposes and customer/vendor transparency.
    """

    __tablename__ = "booking_status_history"

    # Foreign key
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )

    # Status change details
    previous_status = Column(
        Enum(BookingStatus),
        nullable=True,
        doc="Previous booking status"
    )

    new_status = Column(
        Enum(BookingStatus),
        nullable=False,
        index=True,
        doc="New booking status"
    )

    changed_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who made the status change"
    )

    change_reason = Column(
        String(255),
        nullable=True,
        doc="Reason for status change"
    )

    change_notes = Column(
        Text,
        nullable=True,
        doc="Additional notes about the change"
    )

    # System tracking
    automated_change = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether change was automated"
    )

    system_trigger = Column(
        String(100),
        nullable=True,
        doc="System trigger that caused the change"
    )

    # Notification tracking
    customer_notified = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether customer was notified"
    )

    vendor_notified = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether vendor was notified"
    )

    notification_sent_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notifications were sent"
    )

    # Metadata
    change_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional change metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="status_history",
        doc="Associated booking"
    )

    changed_by_user = relationship(
        "User",
        foreign_keys=[changed_by],
        doc="User who made the change"
    )

    # Constraints and indexes
    __table_args__ = (
        # Performance indexes
        Index("idx_status_history_booking_date", "booking_id", "created_at"),
        Index("idx_status_history_status_date", "new_status", "created_at"),
        Index("idx_status_history_user_date", "changed_by", "created_at"),
    )

    def __repr__(self) -> str:
        """String representation of the status history."""
        return f"<BookingStatusHistory(booking_id={self.booking_id}, {self.previous_status} -> {self.new_status})>"


class BookingCommunication(BaseModelWithAudit):
    """
    Booking communication model for vendor-customer messaging.

    Manages all communication between customers and vendors related to
    specific bookings including messages, notifications, and system updates.
    """

    __tablename__ = "booking_communications"

    # Foreign key
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )

    # Communication details
    sender_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who sent the message"
    )

    recipient_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who should receive the message"
    )

    communication_type = Column(
        Enum(CommunicationType),
        nullable=False,
        index=True,
        doc="Type of communication"
    )

    subject = Column(
        String(255),
        nullable=True,
        doc="Message subject line"
    )

    message = Column(
        Text,
        nullable=False,
        doc="Message content"
    )

    # Message status
    is_read = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether message has been read"
    )

    read_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When message was read"
    )

    is_system_message = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether message is system-generated"
    )

    requires_response = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether message requires a response"
    )

    response_deadline = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Deadline for response"
    )

    # Attachments and media
    has_attachments = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether message has attachments"
    )

    attachment_urls = Column(
        JSON,
        nullable=True,
        doc="URLs of attached files"
    )

    attachment_metadata = Column(
        JSON,
        nullable=True,
        doc="Metadata about attachments"
    )

    # Notification tracking
    email_sent = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether email notification was sent"
    )

    push_sent = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether push notification was sent"
    )

    sms_sent = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether SMS notification was sent"
    )

    notification_sent_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notifications were sent"
    )

    # Message metadata
    message_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional message metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="communications",
        doc="Associated booking"
    )

    sender = relationship(
        "User",
        foreign_keys=[sender_id],
        doc="Message sender"
    )

    recipient = relationship(
        "User",
        foreign_keys=[recipient_id],
        doc="Message recipient"
    )

    # Constraints and indexes
    __table_args__ = (
        # Performance indexes
        Index("idx_communication_booking_date", "booking_id", "created_at"),
        Index("idx_communication_sender_date", "sender_id", "created_at"),
        Index("idx_communication_recipient_unread", "recipient_id", "is_read"),
        Index("idx_communication_type_date", "communication_type", "created_at"),
        Index("idx_communication_system_date", "is_system_message", "created_at"),
    )

    def __repr__(self) -> str:
        """String representation of the communication."""
        return f"<BookingCommunication(booking_id={self.booking_id}, type='{self.communication_type}')>"


class BookingModification(BaseModelWithAudit):
    """
    Booking modification model for change requests.

    Manages booking modification requests from customers and vendors
    including date changes, participant count adjustments, and service upgrades.
    """

    __tablename__ = "booking_modifications"

    # Foreign key
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )

    # Modification details
    requested_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who requested the modification"
    )

    modification_type = Column(
        Enum(ModificationType),
        nullable=False,
        index=True,
        doc="Type of modification requested"
    )

    modification_reason = Column(
        Text,
        nullable=True,
        doc="Reason for modification request"
    )

    # Original values
    original_values = Column(
        JSON,
        nullable=False,
        doc="Original booking values before modification"
    )

    # Requested changes
    requested_changes = Column(
        JSON,
        nullable=False,
        doc="Requested changes to booking"
    )

    # Approval workflow
    approval_status = Column(
        String(50),
        nullable=False,
        default="pending",
        index=True,
        doc="Modification approval status"
    )

    approved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who approved/rejected the modification"
    )

    approved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When modification was approved/rejected"
    )

    approval_notes = Column(
        Text,
        nullable=True,
        doc="Notes about approval/rejection"
    )

    # Implementation tracking
    implemented = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether modification has been implemented"
    )

    implemented_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When modification was implemented"
    )

    implemented_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who implemented the modification"
    )

    # Pricing impact
    price_impact = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Price change due to modification"
    )

    additional_fees = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Additional fees for modification"
    )

    # Notification tracking
    customer_notified = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether customer was notified"
    )

    vendor_notified = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether vendor was notified"
    )

    # Metadata
    modification_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional modification metadata"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="modifications",
        doc="Associated booking"
    )

    requested_by_user = relationship(
        "User",
        foreign_keys=[requested_by],
        doc="User who requested modification"
    )

    approved_by_user = relationship(
        "User",
        foreign_keys=[approved_by],
        doc="User who approved modification"
    )

    implemented_by_user = relationship(
        "User",
        foreign_keys=[implemented_by],
        doc="User who implemented modification"
    )

    # Constraints and indexes
    __table_args__ = (
        # Performance indexes
        Index("idx_modification_booking_date", "booking_id", "created_at"),
        Index("idx_modification_status_date", "approval_status", "created_at"),
        Index("idx_modification_type_date", "modification_type", "created_at"),
        Index("idx_modification_requested_by", "requested_by", "created_at"),
    )

    def __repr__(self) -> str:
        """String representation of the modification."""
        return f"<BookingModification(booking_id={self.booking_id}, type='{self.modification_type}', status='{self.approval_status}')>"

# class Review(BaseModelWithAudit):
#     """Review model for customer feedback."""
#     pass
