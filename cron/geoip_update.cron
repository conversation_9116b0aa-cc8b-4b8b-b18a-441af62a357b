# MaxMind GeoIP Database Update Cron Configuration
# Culture Connect Backend - Geolocation Enhancement System
#
# This cron configuration handles automated weekly updates of the MaxMind
# GeoLite2-Country database for production geolocation services.
#
# Schedule: Every Tuesday at 2:00 AM (low traffic period)
# Rationale: MaxMind typically updates their databases on Tuesdays
#
# Installation Instructions:
# 1. Copy this file to your cron configuration directory
# 2. Update the PROJECT_PATH to match your deployment location
# 3. Ensure the culture-connect user has appropriate permissions
# 4. Install using: crontab -u culture-connect cron/geoip_update.cron
#
# Monitoring:
# - Logs are written to /var/log/culture-connect/geoip_update.log
# - Failed updates trigger webhook notifications
# - Health checks validate database integrity post-update

# Environment Variables
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
MAILTO=<EMAIL>

# Project Configuration
# TODO-DEPLOYMENT-PATH: Update this path for your production deployment
PROJECT_PATH=/opt/culture-connect/backend

# MaxMind License Configuration
# TODO-MAXMIND-LICENSE: Set your MaxMind license key in environment
# MAXMIND_LICENSE_KEY=your_license_key_here

# Notification Configuration
# TODO-WEBHOOK-URL: Configure webhook URL for update notifications
# GEOIP_UPDATE_WEBHOOK_URL=https://your-monitoring-system.com/webhooks/geoip

# Weekly GeoIP Database Update
# Runs every Tuesday at 2:00 AM (Cron: minute hour day month weekday)
# 0 2 * * 2 = At 02:00 on Tuesday
0 2 * * 2 cd $PROJECT_PATH && ./scripts/update_geoip.sh >> /var/log/culture-connect/geoip_update.log 2>&1

# Daily Health Check (Optional)
# Runs every day at 6:00 AM to verify database integrity
# Uncomment the following line to enable daily health checks:
# 0 6 * * * cd $PROJECT_PATH && python3 scripts/verify_geoip.py >> /var/log/culture-connect/geoip_health.log 2>&1

# Monthly Backup Cleanup
# Runs on the 1st day of each month at 3:00 AM to clean old backups
# Keeps last 12 monthly backups (1 year retention)
0 3 1 * * find $PROJECT_PATH/data/backups -name "GeoLite2-Country_*_backup_*.mmdb" -mtime +365 -delete >> /var/log/culture-connect/geoip_cleanup.log 2>&1

# Log Rotation for GeoIP Logs
# Runs weekly on Sunday at 1:00 AM to rotate log files
# Keeps last 4 weeks of logs (28 days retention)
0 1 * * 0 find /var/log/culture-connect -name "geoip_*.log" -mtime +28 -delete

# Emergency Database Recovery (Commented out by default)
# Uncomment in case of database corruption for automatic recovery
# This will attempt to restore from the latest backup if verification fails
# */30 * * * * cd $PROJECT_PATH && python3 scripts/verify_geoip.py || (echo "Database verification failed, attempting recovery" && cp data/backups/$(ls -t data/backups/GeoLite2-Country_*_backup_*.mmdb | head -1) data/GeoLite2-Country.mmdb) >> /var/log/culture-connect/geoip_recovery.log 2>&1

# Production Deployment Notes:
# 
# 1. Log Directory Setup:
#    sudo mkdir -p /var/log/culture-connect
#    sudo chown culture-connect:culture-connect /var/log/culture-connect
#    sudo chmod 755 /var/log/culture-connect
#
# 2. Cron Installation:
#    sudo crontab -u culture-connect cron/geoip_update.cron
#
# 3. Environment Variables:
#    Add to /etc/environment or user's .bashrc:
#    export MAXMIND_LICENSE_KEY="your_license_key"
#    export GEOIP_UPDATE_WEBHOOK_URL="your_webhook_url"
#
# 4. Permissions:
#    chmod +x scripts/setup_geoip.sh
#    chmod +x scripts/update_geoip.sh
#    chmod +x scripts/verify_geoip.py
#
# 5. Testing:
#    # Test the update script manually first:
#    cd /opt/culture-connect/backend
#    ./scripts/update_geoip.sh
#
# 6. Monitoring Integration:
#    Configure your monitoring system to receive webhook notifications
#    Set up alerts for failed database updates
#    Monitor log files for errors and warnings
#
# 7. Backup Strategy:
#    Automated backups are created before each update
#    Monthly cleanup removes backups older than 1 year
#    Consider additional off-site backup for disaster recovery
#
# 8. Security Considerations:
#    Store MaxMind license key securely (environment variables)
#    Restrict file permissions on database files (644)
#    Use HTTPS for webhook notifications
#    Regularly rotate log files to prevent disk space issues
#
# 9. Performance Optimization:
#    Schedule updates during low-traffic periods
#    Monitor database update impact on application performance
#    Consider using database replication for zero-downtime updates
#
# 10. Troubleshooting:
#     Check logs in /var/log/culture-connect/
#     Verify MaxMind license key is valid and not expired
#     Ensure network connectivity to MaxMind servers
#     Validate file permissions and disk space availability
