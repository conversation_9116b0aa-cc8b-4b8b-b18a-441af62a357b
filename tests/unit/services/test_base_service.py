"""
Unit tests for BaseService class.

This module tests the base service functionality including CRUD operations,
error handling, transaction management, and service registry.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError, OperationalError

from app.services.base import (
    BaseService, ServiceError, ValidationError, NotFoundError, ConflictError,
    ServiceRegistry, service_registry
)
from app.repositories.base import BaseRepository
from app.db.base import BaseModel


# Mock model for testing
class MockModel(BaseModel):
    """Mock model for testing purposes."""
    __tablename__ = "mock_table"

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
        if not hasattr(self, 'id'):
            self.id = 1


# Mock repository for testing
class MockRepository(BaseRepository[MockModel]):
    """Mock repository for testing purposes."""

    def __init__(self, model_class, db_session):
        super().__init__(model_class, db_session)
        self.mock_data = {}
        self.next_id = 1

    async def create(self, obj_in: Dict[str, Any]) -> MockModel:
        """Mock create method."""
        obj_in['id'] = self.next_id
        self.next_id += 1
        instance = MockModel(**obj_in)
        self.mock_data[instance.id] = instance
        return instance

    async def get(self, id: int) -> Optional[MockModel]:
        """Mock get method."""
        return self.mock_data.get(id)

    async def update(self, id: int, obj_in: Dict[str, Any]) -> Optional[MockModel]:
        """Mock update method."""
        if id in self.mock_data:
            for key, value in obj_in.items():
                setattr(self.mock_data[id], key, value)
            return self.mock_data[id]
        return None

    async def delete(self, id: int) -> bool:
        """Mock delete method."""
        if id in self.mock_data:
            del self.mock_data[id]
            return True
        return False

    async def soft_delete(self, id: int) -> Optional[MockModel]:
        """Mock soft delete method."""
        if id in self.mock_data:
            self.mock_data[id].is_deleted = True
            return self.mock_data[id]
        return None

    async def get_multi(self, limit: int = 100, offset: int = 0, **kwargs) -> list:
        """Mock get_multi method."""
        items = list(self.mock_data.values())[offset:offset + limit]
        return items

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Mock count method."""
        return len(self.mock_data)

    async def exists(self, id: int) -> bool:
        """Mock exists method."""
        return id in self.mock_data

    async def bulk_create(self, objects: list) -> list:
        """Mock bulk create method."""
        results = []
        for obj_data in objects:
            obj_data['id'] = self.next_id
            self.next_id += 1
            instance = MockModel(**obj_data)
            self.mock_data[instance.id] = instance
            results.append(instance)
        return results


# Concrete service implementation for testing
class TestService(BaseService[MockModel, MockRepository]):
    """Test service implementation."""

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate create data."""
        if 'name' not in data:
            raise ValidationError("Name is required", field="name")
        if data.get('name') == 'invalid':
            raise ValidationError("Invalid name", field="name")
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate update data."""
        if 'name' in data and data['name'] == 'invalid':
            raise ValidationError("Invalid name", field="name")
        return data


class TestBaseService:
    """Test cases for BaseService class."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        return session

    @pytest.fixture
    def service(self, mock_session):
        """Create test service instance."""
        return TestService(
            repository_class=MockRepository,
            model_class=MockModel,
            db_session=mock_session
        )

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_session):
        """Test service initialization."""
        service = TestService(
            repository_class=MockRepository,
            model_class=MockModel,
            db_session=mock_session
        )

        assert service.repository_class == MockRepository
        assert service.model_class == MockModel
        assert service._db_session == mock_session
        assert service.service_name == "TestService"
        assert service.service_id is not None

    @pytest.mark.asyncio
    async def test_create_success(self, service):
        """Test successful record creation."""
        data = {"name": "test", "value": "test_value"}

        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.create(data)

                assert result.name == "test"
                assert result.value == "test_value"
                assert result.id == 1

    @pytest.mark.asyncio
    async def test_create_validation_error(self, service):
        """Test create with validation error."""
        data = {"value": "test_value"}  # Missing required 'name'

        with pytest.raises(ValidationError) as exc_info:
            await service.create(data)

        assert exc_info.value.message == "Name is required"
        assert exc_info.value.details.get("field") == "name"

    @pytest.mark.asyncio
    async def test_get_by_id_success(self, service):
        """Test successful get by ID."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            # Pre-populate mock data
            mock_repo.mock_data[1] = MockModel(id=1, name="test")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.get_by_id(1)

                assert result is not None
                assert result.id == 1
                assert result.name == "test"

    @pytest.mark.asyncio
    async def test_get_by_id_not_found(self, service):
        """Test get by ID when record not found."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.get_by_id(999)

                assert result is None

    @pytest.mark.asyncio
    async def test_get_by_id_or_raise_success(self, service):
        """Test get by ID or raise when record exists."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            mock_repo.mock_data[1] = MockModel(id=1, name="test")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.get_by_id_or_raise(1)

                assert result.id == 1
                assert result.name == "test"

    @pytest.mark.asyncio
    async def test_get_by_id_or_raise_not_found(self, service):
        """Test get by ID or raise when record not found."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)

            with patch.object(service, 'repository_class', return_value=mock_repo):
                with pytest.raises(NotFoundError) as exc_info:
                    await service.get_by_id_or_raise(999)

                assert "MockModel not found: 999" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_success(self, service):
        """Test successful record update."""
        data = {"name": "updated"}

        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            mock_repo.mock_data[1] = MockModel(id=1, name="original")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.update(1, data)

                assert result.id == 1
                assert result.name == "updated"

    @pytest.mark.asyncio
    async def test_update_not_found(self, service):
        """Test update when record not found."""
        data = {"name": "updated"}

        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)

            with patch.object(service, 'repository_class', return_value=mock_repo):
                with pytest.raises(NotFoundError):
                    await service.update(999, data)

    @pytest.mark.asyncio
    async def test_delete_success(self, service):
        """Test successful record deletion."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            mock_repo.mock_data[1] = MockModel(id=1, name="test")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.delete(1, soft_delete=False)

                assert result is True
                assert 1 not in mock_repo.mock_data

    @pytest.mark.asyncio
    async def test_soft_delete_success(self, service):
        """Test successful soft deletion."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            mock_repo.mock_data[1] = MockModel(id=1, name="test")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                result = await service.delete(1, soft_delete=True)

                assert result is True
                assert mock_repo.mock_data[1].is_deleted is True

    @pytest.mark.asyncio
    async def test_list_with_pagination(self, service):
        """Test list with pagination."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            # Pre-populate mock data
            for i in range(5):
                mock_repo.mock_data[i + 1] = MockModel(id=i + 1, name=f"test_{i}")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                results = await service.list_with_pagination(limit=3, offset=1)

                assert len(results) == 3
                assert results[0].id == 2

    @pytest.mark.asyncio
    async def test_count(self, service):
        """Test count records."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            # Pre-populate mock data
            for i in range(3):
                mock_repo.mock_data[i + 1] = MockModel(id=i + 1, name=f"test_{i}")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                count = await service.count()

                assert count == 3

    @pytest.mark.asyncio
    async def test_exists(self, service):
        """Test record existence check."""
        with patch.object(service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)
            mock_repo.mock_data[1] = MockModel(id=1, name="test")

            with patch.object(service, 'repository_class', return_value=mock_repo):
                exists = await service.exists(1)
                not_exists = await service.exists(999)

                assert exists is True
                assert not_exists is False

    @pytest.mark.asyncio
    async def test_bulk_create_success(self, service):
        """Test successful bulk creation."""
        data_list = [
            {"name": "test1", "value": "value1"},
            {"name": "test2", "value": "value2"},
            {"name": "test3", "value": "value3"}
        ]

        with patch.object(service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session

            mock_repo = MockRepository(MockModel, mock_session)

            with patch.object(service, 'repository_class', return_value=mock_repo):
                results = await service.bulk_create(data_list)

                assert len(results) == 3
                assert results[0].name == "test1"
                assert results[1].name == "test2"
                assert results[2].name == "test3"

    @pytest.mark.asyncio
    async def test_bulk_create_validation_error(self, service):
        """Test bulk create with validation error."""
        data_list = [
            {"name": "test1", "value": "value1"},
            {"value": "value2"},  # Missing name
            {"name": "test3", "value": "value3"}
        ]

        with pytest.raises(ValidationError) as exc_info:
            await service.bulk_create(data_list)

        assert exc_info.value.message == "Name is required"
        assert exc_info.value.details.get("bulk_index") == 1

    @pytest.mark.asyncio
    async def test_handle_service_error_integrity_error(self, service):
        """Test error handling for integrity errors."""
        error = IntegrityError("statement", "params", "orig")

        with pytest.raises(ConflictError):
            await service.handle_service_error(error, "test_operation")

    @pytest.mark.asyncio
    async def test_handle_service_error_operational_error(self, service):
        """Test error handling for operational errors."""
        error = OperationalError("statement", "params", "orig")

        with pytest.raises(ServiceError) as exc_info:
            await service.handle_service_error(error, "test_operation")

        assert exc_info.value.status_code == 503
        assert exc_info.value.error_code == "DATABASE_ERROR"

    @pytest.mark.asyncio
    async def test_handle_service_error_generic_error(self, service):
        """Test error handling for generic errors."""
        error = ValueError("Test error")

        with pytest.raises(ServiceError) as exc_info:
            await service.handle_service_error(error, "test_operation")

        assert exc_info.value.error_code == "INTERNAL_ERROR"

    def test_log_operation_start(self, service):
        """Test operation logging start."""
        correlation = service.log_operation_start("test_operation", param1="value1")

        assert correlation is not None
        assert correlation in service._operation_metrics
        assert service._operation_metrics[correlation]["operation"] == "test_operation"

    def test_log_operation_success(self, service):
        """Test operation logging success."""
        correlation = service.log_operation_start("test_operation")
        service.log_operation_success(correlation, "Operation completed")

        assert correlation not in service._operation_metrics


class TestServiceRegistry:
    """Test cases for ServiceRegistry class."""

    @pytest.fixture
    def registry(self):
        """Create fresh service registry."""
        return ServiceRegistry()

    def test_register_service(self, registry):
        """Test service registration."""
        registry.register(TestService, "TestService", [])

        assert "TestService" in registry._services
        assert registry._services["TestService"] == TestService
        assert registry._dependencies["TestService"] == []

    def test_get_service_success(self, registry):
        """Test successful service retrieval."""
        registry.register(TestService, "TestService", [])

        service = registry.get_service(
            "TestService",
            repository_class=MockRepository,
            model_class=MockModel
        )

        assert isinstance(service, TestService)
        assert service.repository_class == MockRepository
        assert service.model_class == MockModel

    def test_get_service_not_found(self, registry):
        """Test service retrieval when service not found."""
        with pytest.raises(ServiceError) as exc_info:
            registry.get_service("NonExistentService")

        assert exc_info.value.error_code == "SERVICE_NOT_FOUND"

    def test_clear_instances(self, registry):
        """Test clearing service instances."""
        registry.register(TestService, "TestService", [])

        # Create instance
        service = registry.get_service(
            "TestService",
            repository_class=MockRepository,
            model_class=MockModel
        )

        assert "TestService" in registry._instances

        # Clear instances
        registry.clear_instances()

        assert len(registry._instances) == 0

    def test_list_services(self, registry):
        """Test listing registered services."""
        registry.register(TestService, "TestService1", [])
        registry.register(TestService, "TestService2", [])

        services = registry.list_services()

        assert "TestService1" in services
        assert "TestService2" in services
        assert len(services) == 2


class TestServiceErrors:
    """Test cases for service error classes."""

    def test_service_error_creation(self):
        """Test ServiceError creation."""
        error = ServiceError(
            message="Test error",
            error_code="TEST_ERROR",
            status_code=400,
            details={"field": "value"}
        )

        assert error.message == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.status_code == 400
        assert error.details == {"field": "value"}

    def test_validation_error_creation(self):
        """Test ValidationError creation."""
        error = ValidationError("Invalid field", field="test_field")

        assert error.message == "Invalid field"
        assert error.error_code == "VALIDATION_ERROR"
        assert error.status_code == 422
        assert error.details["field"] == "test_field"

    def test_not_found_error_creation(self):
        """Test NotFoundError creation."""
        error = NotFoundError("User", 123)

        assert "User not found: 123" in error.message
        assert error.error_code == "NOT_FOUND"
        assert error.status_code == 404
        assert error.details["resource"] == "User"
        assert error.details["identifier"] == "123"

    def test_conflict_error_creation(self):
        """Test ConflictError creation."""
        error = ConflictError("Resource already exists")

        assert error.message == "Resource already exists"
        assert error.error_code == "CONFLICT"
        assert error.status_code == 409
