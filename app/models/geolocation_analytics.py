"""
Geolocation Analytics Models for Culture Connect Backend API.

This module defines comprehensive geolocation analytics models for performance tracking including:
- GeolocationAnalytics: Core analytics aggregation with provider performance metrics
- ProviderPerformanceMetrics: Provider-specific performance tracking by geography
- GeolocationRoutingMetrics: Routing decision analytics and optimization insights
- ConversionAnalytics: Conversion rate tracking by geographic regions
- VPNDetectionAnalytics: VPN/Proxy detection impact on routing performance

Implements Phase 2.2 Analytics Dashboard requirements with production-grade
PostgreSQL optimization, comprehensive indexing, and seamless integration with
existing payment and VPN detection systems.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index, Numeric
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from decimal import Decimal

from app.db.base import BaseModelWithAudit


class AnalyticsPeriodType(str, Enum):
    """Analytics aggregation period types."""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class RoutingDecisionType(str, Enum):
    """Payment routing decision types."""
    GEOLOCATION_BASED = "geolocation_based"
    CURRENCY_BASED = "currency_based"
    USER_PREFERENCE = "user_preference"
    CRYPTOCURRENCY = "cryptocurrency"
    FALLBACK = "fallback"
    VPN_ADJUSTED = "vpn_adjusted"


class GeolocationAnalytics(BaseModelWithAudit):
    """
    Core geolocation analytics model for performance tracking and optimization.

    Stores aggregated analytics data for geolocation routing performance including
    provider effectiveness, geographic conversion rates, and routing optimization metrics.
    """
    __tablename__ = "geolocation_analytics"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Time period aggregation
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default=AnalyticsPeriodType.DAILY)
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)

    # Geographic information
    country_code = Column(String(2), nullable=True, index=True)  # ISO country code
    country_name = Column(String(100), nullable=True)
    continent_code = Column(String(2), nullable=True, index=True)
    region = Column(String(100), nullable=True)

    # Payment volume metrics
    total_payments = Column(Integer, nullable=False, default=0)
    successful_payments = Column(Integer, nullable=False, default=0)
    failed_payments = Column(Integer, nullable=False, default=0)
    total_amount = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    successful_amount = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))

    # Performance metrics
    conversion_rate = Column(Float, nullable=False, default=0.0)  # Success rate percentage
    avg_processing_time_ms = Column(Float, nullable=True)
    avg_detection_time_ms = Column(Float, nullable=True)
    cache_hit_rate = Column(Float, nullable=False, default=0.0)

    # Provider distribution
    paystack_payments = Column(Integer, nullable=False, default=0)
    stripe_payments = Column(Integer, nullable=False, default=0)
    busha_payments = Column(Integer, nullable=False, default=0)

    # Routing decision analytics
    geolocation_routing_count = Column(Integer, nullable=False, default=0)
    currency_routing_count = Column(Integer, nullable=False, default=0)
    user_preference_routing_count = Column(Integer, nullable=False, default=0)
    fallback_routing_count = Column(Integer, nullable=False, default=0)

    # VPN/Proxy impact metrics
    vpn_detected_payments = Column(Integer, nullable=False, default=0)
    proxy_detected_payments = Column(Integer, nullable=False, default=0)
    vpn_impact_on_conversion = Column(Float, nullable=False, default=0.0)

    # Additional analytics metadata
    unique_users = Column(Integer, nullable=False, default=0)
    repeat_users = Column(Integer, nullable=False, default=0)
    avg_transaction_value = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))

    # JSON metadata for flexible analytics
    provider_performance_data = Column(JSONB, nullable=True)  # Provider-specific metrics
    routing_effectiveness_data = Column(JSONB, nullable=True)  # Routing decision analysis
    geographic_insights = Column(JSONB, nullable=True)  # Geographic-specific insights

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_geolocation_analytics_date_country', 'analysis_date', 'country_code'),
        Index('idx_geolocation_analytics_period', 'period_start', 'period_end', 'period_type'),
        Index('idx_geolocation_analytics_conversion', 'conversion_rate', 'total_payments'),
        Index('idx_geolocation_analytics_performance', 'avg_processing_time_ms', 'cache_hit_rate'),
        Index('idx_geolocation_analytics_vpn_impact', 'vpn_detected_payments', 'vpn_impact_on_conversion'),
    )


class ProviderPerformanceMetrics(BaseModelWithAudit):
    """
    Provider-specific performance metrics by geographic regions.

    Tracks detailed performance analytics for each payment provider
    across different geographic markets for optimization insights.
    """
    __tablename__ = "provider_performance_metrics"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Time period
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default=AnalyticsPeriodType.DAILY)

    # Geographic and provider information
    country_code = Column(String(2), nullable=False, index=True)
    provider = Column(String(20), nullable=False, index=True)  # paystack, stripe, busha

    # Volume metrics
    total_transactions = Column(Integer, nullable=False, default=0)
    successful_transactions = Column(Integer, nullable=False, default=0)
    failed_transactions = Column(Integer, nullable=False, default=0)

    # Financial metrics
    total_volume = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    successful_volume = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    avg_transaction_amount = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))

    # Performance metrics
    success_rate = Column(Float, nullable=False, default=0.0)
    avg_processing_time_ms = Column(Float, nullable=True)
    avg_response_time_ms = Column(Float, nullable=True)

    # Cost and fee analysis
    total_fees = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))
    avg_fee_percentage = Column(Float, nullable=False, default=0.0)
    cost_effectiveness_score = Column(Float, nullable=False, default=0.0)

    # Quality metrics
    error_rate = Column(Float, nullable=False, default=0.0)
    timeout_rate = Column(Float, nullable=False, default=0.0)
    retry_rate = Column(Float, nullable=False, default=0.0)

    # User experience metrics
    user_satisfaction_score = Column(Float, nullable=True)
    abandonment_rate = Column(Float, nullable=False, default=0.0)
    completion_rate = Column(Float, nullable=False, default=0.0)

    # Competitive analysis
    market_share_percentage = Column(Float, nullable=False, default=0.0)
    relative_performance_score = Column(Float, nullable=False, default=0.0)

    # Additional metadata
    currency_breakdown = Column(JSONB, nullable=True)  # Currency-specific metrics
    failure_reasons = Column(JSONB, nullable=True)  # Failure analysis
    optimization_recommendations = Column(JSONB, nullable=True)  # AI-generated recommendations

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_provider_performance_date_country_provider', 'analysis_date', 'country_code', 'provider'),
        Index('idx_provider_performance_success_rate', 'success_rate', 'total_transactions'),
        Index('idx_provider_performance_volume', 'total_volume', 'successful_volume'),
        Index('idx_provider_performance_cost', 'cost_effectiveness_score', 'avg_fee_percentage'),
    )


class GeolocationRoutingMetrics(BaseModelWithAudit):
    """
    Routing decision analytics and optimization insights.

    Tracks the effectiveness of different routing strategies and provides
    insights for routing algorithm optimization.
    """
    __tablename__ = "geolocation_routing_metrics"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Time period
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default=AnalyticsPeriodType.DAILY)

    # Routing decision tracking
    routing_decision = Column(String(30), nullable=False, index=True)  # RoutingDecisionType
    country_code = Column(String(2), nullable=True, index=True)
    selected_provider = Column(String(20), nullable=False, index=True)

    # Decision effectiveness metrics
    total_decisions = Column(Integer, nullable=False, default=0)
    successful_outcomes = Column(Integer, nullable=False, default=0)
    failed_outcomes = Column(Integer, nullable=False, default=0)
    decision_accuracy = Column(Float, nullable=False, default=0.0)

    # Performance impact
    avg_success_rate = Column(Float, nullable=False, default=0.0)
    avg_processing_time_ms = Column(Float, nullable=True)
    cost_efficiency_score = Column(Float, nullable=False, default=0.0)

    # VPN/Proxy impact on routing
    vpn_affected_decisions = Column(Integer, nullable=False, default=0)
    vpn_routing_accuracy = Column(Float, nullable=False, default=0.0)
    confidence_score_impact = Column(Float, nullable=False, default=0.0)

    # Geographic routing effectiveness
    optimal_routing_percentage = Column(Float, nullable=False, default=0.0)
    suboptimal_routing_count = Column(Integer, nullable=False, default=0)
    routing_improvement_potential = Column(Float, nullable=False, default=0.0)

    # A/B testing integration
    ab_test_group = Column(String(10), nullable=True)  # A, B, or null
    ab_test_name = Column(String(50), nullable=True)

    # Optimization insights
    optimization_score = Column(Float, nullable=False, default=0.0)
    recommended_adjustments = Column(JSONB, nullable=True)
    performance_trends = Column(JSONB, nullable=True)

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_routing_metrics_date_decision', 'analysis_date', 'routing_decision'),
        Index('idx_routing_metrics_provider_accuracy', 'selected_provider', 'decision_accuracy'),
        Index('idx_routing_metrics_vpn_impact', 'vpn_affected_decisions', 'vpn_routing_accuracy'),
        Index('idx_routing_metrics_optimization', 'optimization_score', 'routing_improvement_potential'),
    )


class ConversionAnalytics(BaseModelWithAudit):
    """
    Conversion rate tracking by geographic regions and routing strategies.

    Provides detailed conversion analytics for optimization of payment
    routing strategies and geographic market performance.
    """
    __tablename__ = "conversion_analytics"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Time period
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default=AnalyticsPeriodType.DAILY)

    # Geographic segmentation
    country_code = Column(String(2), nullable=False, index=True)
    region = Column(String(100), nullable=True)
    market_segment = Column(String(20), nullable=True)  # african, diaspora, global

    # Conversion funnel metrics
    total_payment_attempts = Column(Integer, nullable=False, default=0)
    initiated_payments = Column(Integer, nullable=False, default=0)
    authorized_payments = Column(Integer, nullable=False, default=0)
    completed_payments = Column(Integer, nullable=False, default=0)

    # Conversion rates at each stage
    initiation_rate = Column(Float, nullable=False, default=0.0)  # initiated/attempts
    authorization_rate = Column(Float, nullable=False, default=0.0)  # authorized/initiated
    completion_rate = Column(Float, nullable=False, default=0.0)  # completed/authorized
    overall_conversion_rate = Column(Float, nullable=False, default=0.0)  # completed/attempts

    # Provider-specific conversion rates
    paystack_conversion_rate = Column(Float, nullable=False, default=0.0)
    stripe_conversion_rate = Column(Float, nullable=False, default=0.0)
    busha_conversion_rate = Column(Float, nullable=False, default=0.0)

    # VPN/Proxy impact on conversion
    vpn_conversion_rate = Column(Float, nullable=False, default=0.0)
    non_vpn_conversion_rate = Column(Float, nullable=False, default=0.0)
    vpn_conversion_impact = Column(Float, nullable=False, default=0.0)  # Difference percentage

    # Time-based conversion analysis
    avg_time_to_completion_seconds = Column(Float, nullable=True)
    abandonment_rate = Column(Float, nullable=False, default=0.0)
    retry_success_rate = Column(Float, nullable=False, default=0.0)

    # Financial impact
    total_conversion_value = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    avg_conversion_value = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))
    lost_revenue_estimate = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))

    # Optimization insights
    conversion_optimization_score = Column(Float, nullable=False, default=0.0)
    improvement_potential_percentage = Column(Float, nullable=False, default=0.0)
    recommended_optimizations = Column(JSONB, nullable=True)

    # Comparative analysis
    benchmark_conversion_rate = Column(Float, nullable=True)  # Industry benchmark
    relative_performance = Column(Float, nullable=False, default=0.0)  # vs benchmark

    # Additional metadata
    user_segment_breakdown = Column(JSONB, nullable=True)  # New vs returning users
    device_type_breakdown = Column(JSONB, nullable=True)  # Mobile vs desktop
    currency_conversion_impact = Column(JSONB, nullable=True)  # Currency-specific rates

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_conversion_analytics_date_country', 'analysis_date', 'country_code'),
        Index('idx_conversion_analytics_rates', 'overall_conversion_rate', 'total_payment_attempts'),
        Index('idx_conversion_analytics_vpn_impact', 'vpn_conversion_impact', 'vpn_conversion_rate'),
        Index('idx_conversion_analytics_optimization', 'conversion_optimization_score', 'improvement_potential_percentage'),
        Index('idx_conversion_analytics_market', 'market_segment', 'region'),
    )
