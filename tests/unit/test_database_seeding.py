"""
Unit tests for database seeding functionality.

This module tests the database seeding infrastructure including:
- DatabaseSeeder class methods
- Data generation and validation
- Seeding utilities and helpers
- Error handling and edge cases
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from decimal import Decimal
from datetime import datetime

from app.db.seed import DatabaseSeeder, seed_database, clear_database
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VerificationStatus, MarketplaceStatus


class TestDatabaseSeeder:
    """Test DatabaseSeeder class functionality."""
    
    @pytest.fixture
    def seeder(self):
        """Create DatabaseSeeder instance for testing."""
        return DatabaseSeeder()
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock()
        session.add = MagicMock()
        session.flush = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.execute = AsyncMock()
        return session
    
    @pytest.mark.asyncio
    async def test_seeder_initialization(self, seeder):
        """Test DatabaseSeeder initialization."""
        assert seeder.fake is not None
        assert seeder.created_users == []
        assert seeder.created_vendors == []
    
    @pytest.mark.asyncio
    async def test_clear_existing_data(self, seeder, mock_session):
        """Test clearing existing data."""
        await seeder._clear_existing_data(mock_session)
        
        # Should execute delete statements for all tables
        assert mock_session.execute.call_count >= 6  # 6 tables to clear
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_seed_users(self, seeder, mock_session):
        """Test user seeding functionality."""
        # Mock user IDs after flush
        mock_session.flush.side_effect = lambda: setattr(
            seeder.created_users[0], 'id', 1
        ) if seeder.created_users else None
        
        users = await seeder._seed_users(mock_session, count=5)
        
        assert len(users) == 5
        assert mock_session.add.call_count == 5
        mock_session.flush.assert_called_once()
        
        # Check admin user is created
        admin_user = users[0]
        assert admin_user.email == "<EMAIL>"
        assert admin_user.role == UserRole.ADMIN
        assert admin_user.is_active is True
        assert admin_user.is_verified is True
    
    @pytest.mark.asyncio
    async def test_seed_vendors(self, seeder, mock_session):
        """Test vendor seeding functionality."""
        # Create mock users
        mock_users = []
        for i in range(3):
            user = MagicMock()
            user.id = i + 1
            user.role = UserRole.VENDOR
            mock_users.append(user)
        
        # Mock vendor IDs after flush
        def mock_flush():
            for i, vendor in enumerate(seeder.created_vendors):
                vendor.id = i + 1
        
        mock_session.flush.side_effect = mock_flush
        
        vendors = await seeder._seed_vendors(mock_session, mock_users)
        
        assert len(vendors) == 3
        assert mock_session.add.call_count == 3
        mock_session.flush.assert_called_once()
        
        # Check vendor properties
        vendor = vendors[0]
        assert vendor.user_id == 1
        assert vendor.business_name is not None
        assert isinstance(vendor.business_type, VendorType)
        assert isinstance(vendor.verification_status, VerificationStatus)
        assert isinstance(vendor.marketplace_status, MarketplaceStatus)
    
    @pytest.mark.asyncio
    async def test_seed_vendor_profiles(self, seeder, mock_session):
        """Test vendor profile seeding functionality."""
        # Create mock vendors
        mock_vendors = []
        for i in range(2):
            vendor = MagicMock()
            vendor.id = i + 1
            mock_vendors.append(vendor)
        
        profiles = await seeder._seed_vendor_profiles(mock_session, mock_vendors)
        
        assert len(profiles) == 2
        assert mock_session.add.call_count == 2
        mock_session.flush.assert_called_once()
        
        # Check profile properties
        profile = profiles[0]
        assert profile.vendor_id == 1
        assert profile.business_description is not None
        assert profile.city is not None
        assert profile.country == "Nigeria"
        assert profile.timezone == "Africa/Lagos"
    
    @pytest.mark.asyncio
    async def test_seed_vendor_documents(self, seeder, mock_session):
        """Test vendor document seeding functionality."""
        # Create mock vendors
        mock_vendors = []
        for i in range(2):
            vendor = MagicMock()
            vendor.id = i + 1
            vendor.business_name = f"Business {i + 1}"
            mock_vendors.append(vendor)
        
        documents = await seeder._seed_vendor_documents(mock_session, mock_vendors)
        
        assert len(documents) >= 4  # At least 2 documents per vendor
        assert mock_session.add.call_count >= 4
        mock_session.flush.assert_called_once()
        
        # Check document properties
        document = documents[0]
        assert document.vendor_id == 1
        assert document.document_name is not None
        assert document.file_url is not None
        assert document.mime_type == "application/pdf"
    
    @pytest.mark.asyncio
    async def test_seed_api_keys(self, seeder, mock_session):
        """Test API key seeding functionality."""
        # Create mock users
        mock_users = []
        for i in range(5):
            user = MagicMock()
            user.id = i + 1
            user.first_name = f"User{i + 1}"
            mock_users.append(user)
        
        api_keys = await seeder._seed_api_keys(mock_session, mock_users)
        
        # Not all users get API keys (33% chance)
        assert len(api_keys) <= 5
        mock_session.flush.assert_called_once()
        
        if api_keys:
            api_key = api_keys[0]
            assert api_key.user_id is not None
            assert api_key.name is not None
            assert api_key.key_hash is not None
            assert api_key.key_prefix is not None
    
    @pytest.mark.asyncio
    async def test_seed_user_sessions(self, seeder, mock_session):
        """Test user session seeding functionality."""
        # Create mock users
        mock_users = []
        for i in range(3):
            user = MagicMock()
            user.id = i + 1
            user.is_active = True
            mock_users.append(user)
        
        sessions = await seeder._seed_user_sessions(mock_session, mock_users)
        
        # Active users get 1-3 sessions each
        assert len(sessions) >= 3
        assert len(sessions) <= 9
        mock_session.flush.assert_called_once()
        
        if sessions:
            session = sessions[0]
            assert session.user_id is not None
            assert session.session_token is not None
            assert session.device_info is not None
            assert session.ip_address is not None
    
    @pytest.mark.asyncio
    async def test_seed_all_success(self, seeder, mock_session):
        """Test complete seeding process."""
        # Mock all the individual seeding methods
        with patch.multiple(
            seeder,
            _clear_existing_data=AsyncMock(),
            _seed_users=AsyncMock(return_value=[MagicMock() for _ in range(50)]),
            _seed_vendors=AsyncMock(return_value=[MagicMock() for _ in range(30)]),
            _seed_vendor_profiles=AsyncMock(return_value=[MagicMock() for _ in range(30)]),
            _seed_vendor_documents=AsyncMock(return_value=[MagicMock() for _ in range(60)]),
            _seed_api_keys=AsyncMock(return_value=[MagicMock() for _ in range(10)]),
            _seed_user_sessions=AsyncMock(return_value=[MagicMock() for _ in range(20)])
        ):
            counts = await seeder.seed_all(mock_session, "development")
            
            assert counts["users"] == 50
            assert counts["vendors"] == 30
            assert counts["vendor_profiles"] == 30
            assert counts["vendor_documents"] == 60
            assert counts["api_keys"] == 10
            assert counts["user_sessions"] == 20
            
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_seed_all_failure(self, seeder, mock_session):
        """Test seeding failure handling."""
        # Mock an exception during seeding
        with patch.object(seeder, '_seed_users', side_effect=Exception("Seeding failed")):
            with pytest.raises(Exception, match="Seeding failed"):
                await seeder.seed_all(mock_session, "development")
            
            mock_session.rollback.assert_called_once()


class TestSeedingUtilities:
    """Test seeding utility functions."""
    
    @pytest.mark.asyncio
    async def test_seed_database_function(self):
        """Test seed_database utility function."""
        with patch('app.db.seed.get_async_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.db.seed.DatabaseSeeder') as mock_seeder_class:
                mock_seeder = AsyncMock()
                mock_seeder.seed_all.return_value = {"users": 10, "vendors": 5}
                mock_seeder_class.return_value = mock_seeder
                
                result = await seed_database("testing")
                
                assert result == {"users": 10, "vendors": 5}
                mock_seeder.seed_all.assert_called_once_with(mock_session, "testing")
    
    @pytest.mark.asyncio
    async def test_clear_database_function(self):
        """Test clear_database utility function."""
        with patch('app.db.seed.get_async_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.db.seed.DatabaseSeeder') as mock_seeder_class:
                mock_seeder = AsyncMock()
                mock_seeder_class.return_value = mock_seeder
                
                await clear_database()
                
                mock_seeder._clear_existing_data.assert_called_once_with(mock_session)


class TestSeedingDataValidation:
    """Test data validation in seeding process."""
    
    @pytest.fixture
    def seeder(self):
        """Create DatabaseSeeder instance for testing."""
        return DatabaseSeeder()
    
    def test_user_data_generation(self, seeder):
        """Test user data generation produces valid data."""
        # Test email generation
        email = seeder.fake.email()
        assert "@" in email
        assert "." in email
        
        # Test phone number generation
        phone = "+234" + seeder.fake.numerify("##########")
        assert phone.startswith("+234")
        assert len(phone) == 14
    
    def test_vendor_data_generation(self, seeder):
        """Test vendor data generation produces valid data."""
        # Test business registration number
        reg_number = f"RC{seeder.fake.numerify('######')}"
        assert reg_number.startswith("RC")
        assert len(reg_number) == 8
        
        # Test tax ID
        tax_id = f"TIN{seeder.fake.numerify('#########')}"
        assert tax_id.startswith("TIN")
        assert len(tax_id) == 12
    
    def test_decimal_field_validation(self, seeder):
        """Test decimal field generation produces valid values."""
        # Test commission rate (0.10 to 0.20)
        commission_rate = Decimal(str(seeder.fake.pyfloat(min_value=0.10, max_value=0.20, right_digits=4)))
        assert 0.10 <= commission_rate <= 0.20
        
        # Test average rating (3.0 to 5.0)
        rating = Decimal(str(seeder.fake.pyfloat(min_value=3.0, max_value=5.0, right_digits=2)))
        assert 3.0 <= rating <= 5.0
    
    def test_json_field_validation(self, seeder):
        """Test JSON field generation produces valid data."""
        # Test operating hours
        operating_hours = {
            "monday": {"open": "09:00", "close": "17:00"},
            "tuesday": {"open": "09:00", "close": "17:00"},
            "wednesday": {"open": "09:00", "close": "17:00"},
            "thursday": {"open": "09:00", "close": "17:00"},
            "friday": {"open": "09:00", "close": "17:00"},
            "saturday": {"open": "10:00", "close": "16:00"},
            "sunday": {"closed": True}
        }
        
        assert "monday" in operating_hours
        assert "open" in operating_hours["monday"]
        assert operating_hours["sunday"]["closed"] is True
        
        # Test social media links
        social_media = {
            "facebook": f"https://facebook.com/{seeder.fake.user_name()}",
            "instagram": f"https://instagram.com/{seeder.fake.user_name()}",
            "twitter": f"https://twitter.com/{seeder.fake.user_name()}"
        }
        
        assert all(link.startswith("https://") for link in social_media.values())


class TestSeedingEdgeCases:
    """Test edge cases and error conditions in seeding."""
    
    @pytest.fixture
    def seeder(self):
        """Create DatabaseSeeder instance for testing."""
        return DatabaseSeeder()
    
    @pytest.mark.asyncio
    async def test_empty_user_list_for_vendors(self, seeder):
        """Test vendor seeding with empty user list."""
        mock_session = AsyncMock()
        
        vendors = await seeder._seed_vendors(mock_session, [])
        
        assert len(vendors) == 0
        assert mock_session.add.call_count == 0
    
    @pytest.mark.asyncio
    async def test_non_vendor_users_for_vendors(self, seeder):
        """Test vendor seeding with non-vendor users."""
        mock_session = AsyncMock()
        
        # Create tourist users
        mock_users = []
        for i in range(3):
            user = MagicMock()
            user.id = i + 1
            user.role = UserRole.TOURIST
            mock_users.append(user)
        
        vendors = await seeder._seed_vendors(mock_session, mock_users)
        
        assert len(vendors) == 0
        assert mock_session.add.call_count == 0
    
    @pytest.mark.asyncio
    async def test_inactive_users_for_sessions(self, seeder):
        """Test session seeding with inactive users."""
        mock_session = AsyncMock()
        
        # Create inactive users
        mock_users = []
        for i in range(3):
            user = MagicMock()
            user.id = i + 1
            user.is_active = False
            mock_users.append(user)
        
        sessions = await seeder._seed_user_sessions(mock_session, mock_users)
        
        assert len(sessions) == 0
        assert mock_session.add.call_count == 0
