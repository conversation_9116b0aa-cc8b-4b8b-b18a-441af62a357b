"""
Review Moderation Service for Culture Connect Backend API.

This module provides comprehensive AI moderation business logic orchestration including:
- Complete AI moderation pipeline with confidence scoring and manual review escalation
- Integration with external AI/ML API (aimlapi.com) for content analysis
- Manual review workflow management with admin approval processes
- Circuit breaker patterns for external service reliability
- Performance optimization with <200ms moderation processing targets

Implements Task 4.4.1 Phase 4 requirements for review moderation service implementation with
production-grade business logic following established BaseService patterns.
"""

import logging
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
import aiohttp
import json

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.repositories.review_moderation_repository import ReviewModerationRepository
from app.repositories.review_repository import ReviewRepository
from app.models.review_models import ReviewModeration, ModerationAction, Review, ReviewStatus
from app.schemas.review_schemas import (
    ReviewModerationCreateSchema, ReviewModerationUpdateSchema, ReviewModerationSchema
)
from app.repositories.base import PaginationParams, QueryResult
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.config import settings

logger = logging.getLogger(__name__)


class ReviewModerationService(BaseService[ReviewModeration, ReviewModerationRepository]):
    """
    Review moderation service for AI moderation pipeline management.

    Provides comprehensive moderation workflow including:
    - AI-powered content analysis with confidence scoring
    - Manual review escalation and processing
    - Circuit breaker patterns for external AI service reliability
    - Integration with review status management
    - Performance optimization with <200ms processing targets
    """

    def __init__(self, db: AsyncSession):
        """Initialize review moderation service with dependencies."""
        super().__init__(
            repository_class=ReviewModerationRepository,
            model_class=ReviewModeration,
            db_session=db
        )

        # Initialize repositories
        self.review_repository = ReviewRepository(db)

        # AI service configuration
        self.ai_api_url = getattr(settings, 'AI_ML_API_URL', 'https://api.aimlapi.com')
        self.ai_api_key = getattr(settings, 'AI_ML_API_KEY', None)
        self.confidence_threshold = getattr(settings, 'AI_CONFIDENCE_THRESHOLD', 0.8)
        self.circuit_breaker_threshold = getattr(settings, 'AI_CIRCUIT_BREAKER_THRESHOLD', 5)
        self.circuit_breaker_timeout = getattr(settings, 'AI_CIRCUIT_BREAKER_TIMEOUT', 300)

        # Circuit breaker state
        self._circuit_breaker_failures = 0
        self._circuit_breaker_last_failure = None
        self._circuit_breaker_open = False

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate moderation creation data.

        Args:
            data: Moderation creation data

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['review_id', 'action']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate action
        action = data.get('action')
        if not isinstance(action, ModerationAction):
            raise ValidationError(f"Invalid moderation action: {action}")

        # Validate confidence score if provided
        confidence = data.get('ai_confidence_score')
        if confidence is not None:
            if not isinstance(confidence, (int, float)) or confidence < 0.0 or confidence > 1.0:
                raise ValidationError("AI confidence score must be between 0.0 and 1.0")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate moderation update data.

        Args:
            data: Moderation update data
            existing_id: Existing moderation ID

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate action if provided
        if 'action' in data:
            action = data['action']
            if not isinstance(action, ModerationAction):
                raise ValidationError(f"Invalid moderation action: {action}")

        return data

    async def create_moderation_record(
        self,
        review_id: int,
        review_content: Dict[str, Any],
        moderator_id: Optional[int] = None
    ) -> ReviewModeration:
        """
        Create a new moderation record with AI analysis.

        Performance target: <200ms for moderation record creation including AI analysis.

        Args:
            review_id: Associated review ID
            review_content: Review content for analysis
            moderator_id: Optional admin moderator ID

        Returns:
            Created ReviewModeration instance

        Raises:
            ValidationError: If moderation data is invalid
            NotFoundError: If review not found
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start(
            "create_moderation_record",
            review_id=review_id,
            moderator_id=moderator_id
        )

        try:
            # Validate review exists
            review = await self.review_repository.get(review_id)
            if not review:
                raise NotFoundError("Review", review_id)

            # Perform AI analysis
            ai_analysis = await self._analyze_content_with_ai(review_content)

            # Determine moderation action based on AI analysis
            action, manual_review_required = self._determine_moderation_action(ai_analysis)

            # Prepare moderation data
            moderation_data = {
                'review_id': review_id,
                'moderator_id': moderator_id,
                'action': action,
                'ai_confidence_score': ai_analysis.get('confidence_score'),
                'manual_review_required': manual_review_required,
                'ai_analysis_results': ai_analysis,
                'reason': ai_analysis.get('reason', 'AI-powered content analysis')
            }

            # Create moderation record
            async with self.get_session_context() as session:
                repository = ReviewModerationRepository(session)
                moderation = await repository.create_moderation_record(
                    review_id=review_id,
                    moderation_data=moderation_data,
                    moderator_id=moderator_id
                )

                # Update review status if action is definitive
                if not manual_review_required:
                    await self._apply_moderation_action(review, action)

                # Track metrics
                metrics_collector.increment_counter(
                    "review_moderation_created",
                    tags={
                        "action": action.value,
                        "manual_required": str(manual_review_required),
                        "confidence": self._get_confidence_bucket(ai_analysis.get('confidence_score', 0))
                    }
                )

                self.log_operation_success(
                    correlation,
                    f"Moderation record created with ID: {moderation.id}, action: {action.value}"
                )

                return moderation

        except Exception as e:
            await self.handle_service_error(e, "create_moderation_record", {
                "review_id": review_id,
                "moderator_id": moderator_id
            })

    async def process_manual_review(
        self,
        moderation_id: int,
        admin_id: int,
        action: ModerationAction,
        reason: Optional[str] = None
    ) -> ReviewModeration:
        """
        Process a manual review decision.

        Performance target: <100ms for manual review processing.

        Args:
            moderation_id: Moderation record ID
            admin_id: Admin processing the review
            action: Final moderation action
            reason: Optional reason for the decision

        Returns:
            Updated ReviewModeration instance

        Raises:
            NotFoundError: If moderation record not found
            ValidationError: If moderation cannot be processed
            ServiceError: If processing fails
        """
        correlation = self.log_operation_start(
            "process_manual_review",
            moderation_id=moderation_id,
            admin_id=admin_id,
            action=action.value
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewModerationRepository(session)
                
                # Get moderation record
                moderation = await repository.get(moderation_id)
                if not moderation:
                    raise NotFoundError("Moderation", moderation_id)

                # Validate moderation is pending
                if moderation.processed_at:
                    raise ValidationError("Moderation has already been processed")

                # Process moderation
                processed_moderation = await repository.process_moderation(
                    moderation_id=moderation_id,
                    action=action,
                    moderator_id=admin_id,
                    reason=reason
                )

                # Apply moderation action to review
                review = await self.review_repository.get(moderation.review_id)
                await self._apply_moderation_action(review, action)

                # Track metrics
                metrics_collector.increment_counter(
                    "manual_review_processed",
                    tags={"action": action.value, "admin_id": str(admin_id)}
                )

                self.log_operation_success(
                    correlation,
                    f"Manual review processed: {action.value} by admin {admin_id}"
                )

                return processed_moderation

        except Exception as e:
            await self.handle_service_error(e, "process_manual_review", {
                "moderation_id": moderation_id,
                "admin_id": admin_id,
                "action": action.value
            })

    async def get_pending_manual_reviews(
        self,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewModeration]:
        """
        Get moderation records requiring manual review.

        Performance target: <200ms for pending review queries.

        Args:
            pagination: Pagination parameters

        Returns:
            Query result with pending manual reviews
        """
        correlation = self.log_operation_start("get_pending_manual_reviews")

        try:
            async with self.get_session_context() as session:
                repository = ReviewModerationRepository(session)
                
                result = await repository.get_pending_manual_reviews(pagination=pagination)

                self.log_operation_success(
                    correlation,
                    f"Retrieved {len(result.items)} pending manual reviews"
                )

                return result

        except Exception as e:
            await self.handle_service_error(e, "get_pending_manual_reviews", {})

    async def get_ai_moderation_queue(
        self,
        confidence_threshold: Optional[float] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewModeration]:
        """
        Get AI moderation records below confidence threshold.

        Performance target: <200ms for AI queue queries.

        Args:
            confidence_threshold: AI confidence threshold for manual review
            pagination: Pagination parameters

        Returns:
            Query result with low-confidence AI moderation records
        """
        correlation = self.log_operation_start("get_ai_moderation_queue")

        try:
            threshold = confidence_threshold or self.confidence_threshold
            
            async with self.get_session_context() as session:
                repository = ReviewModerationRepository(session)
                
                result = await repository.get_ai_moderation_queue(
                    confidence_threshold=threshold,
                    pagination=pagination
                )

                self.log_operation_success(
                    correlation,
                    f"Retrieved {len(result.items)} low-confidence AI moderation records"
                )

                return result

        except Exception as e:
            await self.handle_service_error(e, "get_ai_moderation_queue", {})

    # Private helper methods

    async def _analyze_content_with_ai(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze content using external AI/ML API with circuit breaker pattern.

        Args:
            content: Content to analyze

        Returns:
            AI analysis results
        """
        # Check circuit breaker
        if self._is_circuit_breaker_open():
            logger.warning("AI service circuit breaker is open, using fallback analysis")
            return self._get_fallback_analysis(content)

        try:
            # Prepare AI analysis request
            analysis_request = {
                'text': f"{content.get('title', '')} {content.get('content', '')}",
                'tasks': ['sentiment', 'toxicity', 'spam_detection'],
                'language': 'en'
            }

            # Call AI service with timeout
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                headers = {
                    'Authorization': f'Bearer {self.ai_api_key}',
                    'Content-Type': 'application/json'
                }
                
                async with session.post(
                    f"{self.ai_api_url}/analyze",
                    json=analysis_request,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        ai_result = await response.json()
                        self._reset_circuit_breaker()
                        return self._process_ai_response(ai_result, content)
                    else:
                        raise ServiceError(f"AI service returned status {response.status}")

        except Exception as e:
            self._record_circuit_breaker_failure()
            logger.error(f"AI analysis failed: {str(e)}")
            return self._get_fallback_analysis(content)

    def _process_ai_response(self, ai_result: Dict[str, Any], content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process AI service response into standardized format.

        Args:
            ai_result: Raw AI service response
            content: Original content

        Returns:
            Processed analysis results
        """
        # Extract sentiment score
        sentiment_score = ai_result.get('sentiment', {}).get('score', 0.0)
        
        # Extract toxicity score
        toxicity_score = ai_result.get('toxicity', {}).get('score', 0.0)
        
        # Extract spam probability
        spam_score = ai_result.get('spam_detection', {}).get('probability', 0.0)

        # Calculate overall confidence
        confidence_score = 1.0 - max(toxicity_score, spam_score)

        # Determine if content is appropriate
        is_appropriate = toxicity_score < 0.7 and spam_score < 0.5

        return {
            'sentiment_score': sentiment_score,
            'toxicity_score': toxicity_score,
            'spam_probability': spam_score,
            'confidence_score': confidence_score,
            'is_appropriate': is_appropriate,
            'reason': self._generate_analysis_reason(toxicity_score, spam_score, sentiment_score),
            'analysis_timestamp': datetime.now(timezone.utc).isoformat()
        }

    def _get_fallback_analysis(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Provide fallback analysis when AI service is unavailable.

        Args:
            content: Content to analyze

        Returns:
            Fallback analysis results
        """
        # Simple rule-based analysis
        text = f"{content.get('title', '')} {content.get('content', '')}".lower()
        
        # Check for obvious spam patterns
        spam_indicators = ['spam', 'fake', 'bot', 'test123', 'click here', 'buy now']
        spam_score = sum(1 for indicator in spam_indicators if indicator in text) / len(spam_indicators)
        
        # Basic sentiment based on rating
        rating = content.get('rating', 3)
        sentiment_score = (rating - 3) / 2  # Convert 1-5 to -1 to 1
        
        # Conservative toxicity assessment
        toxicity_score = 0.1  # Assume low toxicity for fallback
        
        return {
            'sentiment_score': sentiment_score,
            'toxicity_score': toxicity_score,
            'spam_probability': spam_score,
            'confidence_score': 0.5,  # Low confidence for fallback
            'is_appropriate': spam_score < 0.3 and toxicity_score < 0.5,
            'reason': 'Fallback analysis (AI service unavailable)',
            'analysis_timestamp': datetime.now(timezone.utc).isoformat(),
            'fallback': True
        }

    def _determine_moderation_action(self, analysis: Dict[str, Any]) -> Tuple[ModerationAction, bool]:
        """
        Determine moderation action based on AI analysis.

        Args:
            analysis: AI analysis results

        Returns:
            Tuple of (action, manual_review_required)
        """
        confidence = analysis.get('confidence_score', 0.0)
        is_appropriate = analysis.get('is_appropriate', True)
        
        # High confidence decisions
        if confidence >= self.confidence_threshold:
            if is_appropriate:
                return ModerationAction.APPROVE, False
            else:
                return ModerationAction.REJECT, False
        
        # Low confidence - require manual review
        if is_appropriate:
            return ModerationAction.APPROVE, True
        else:
            return ModerationAction.FLAG, True

    async def _apply_moderation_action(self, review: Review, action: ModerationAction) -> None:
        """
        Apply moderation action to review status.

        Args:
            review: Review to update
            action: Moderation action to apply
        """
        status_mapping = {
            ModerationAction.APPROVE: ReviewStatus.APPROVED,
            ModerationAction.REJECT: ReviewStatus.REJECTED,
            ModerationAction.FLAG: ReviewStatus.FLAGGED
        }
        
        new_status = status_mapping.get(action)
        if new_status and review.status != new_status:
            await self.review_repository.update_review_status(
                review_id=review.id,
                new_status=new_status,
                moderation_reason=f"Moderation action: {action.value}"
            )

    # Circuit breaker methods

    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open."""
        if not self._circuit_breaker_open:
            return False
        
        # Check if timeout has passed
        if self._circuit_breaker_last_failure:
            time_since_failure = (datetime.now(timezone.utc) - self._circuit_breaker_last_failure).total_seconds()
            if time_since_failure > self.circuit_breaker_timeout:
                self._circuit_breaker_open = False
                self._circuit_breaker_failures = 0
                return False
        
        return True

    def _record_circuit_breaker_failure(self) -> None:
        """Record a circuit breaker failure."""
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = datetime.now(timezone.utc)
        
        if self._circuit_breaker_failures >= self.circuit_breaker_threshold:
            self._circuit_breaker_open = True
            logger.warning(f"AI service circuit breaker opened after {self._circuit_breaker_failures} failures")

    def _reset_circuit_breaker(self) -> None:
        """Reset circuit breaker after successful call."""
        self._circuit_breaker_failures = 0
        self._circuit_breaker_open = False
        self._circuit_breaker_last_failure = None

    def _get_confidence_bucket(self, confidence: float) -> str:
        """Get confidence bucket for metrics."""
        if confidence >= 0.9:
            return "high"
        elif confidence >= 0.7:
            return "medium"
        else:
            return "low"

    def _generate_analysis_reason(self, toxicity: float, spam: float, sentiment: float) -> str:
        """Generate human-readable analysis reason."""
        if toxicity > 0.7:
            return "Content flagged for potentially toxic language"
        elif spam > 0.5:
            return "Content flagged as potential spam"
        elif sentiment < -0.5:
            return "Content has very negative sentiment"
        else:
            return "Content appears appropriate"
