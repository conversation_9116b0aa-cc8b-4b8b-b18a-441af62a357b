"""
End-to-end integration tests for geolocation functionality.

This module provides comprehensive integration testing for:
- MaxMind database connectivity and functionality validation
- GeolocationService integration with circuit breaker patterns
- Payment provider routing based on geolocation detection
- Redis caching performance and consistency verification
- Health check endpoint validation

Follows Culture Connect Backend testing standards with >80% coverage requirements.
"""

import pytest
import asyncio
import time
import os
from typing import Dict, Any, List
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import Async<PERSON>lient

from app.main import app
from app.services.geolocation_service import get_geolocation_service, GeolocationResult
from app.core.circuit_breaker import get_geolocation_circuit_breaker
from app.core.geolocation_resilience import get_geolocation_resilience_manager
from tests.fixtures.geolocation_fixtures import (
    GeolocationTestData,
    GeolocationTestHelper,
    TEST_CONFIG
)


class TestGeolocationIntegration:
    """Integration tests for geolocation functionality."""
    
    @pytest.mark.asyncio
    async def test_maxmind_database_connectivity(self, temp_test_database):
        """Test MaxMind database connectivity and basic functionality."""
        # Mock the database path to use test database
        with patch('app.core.config.settings.GEOIP_DATABASE_PATH', temp_test_database):
            geolocation_service = get_geolocation_service()
            
            # Test database initialization
            assert geolocation_service is not None
            
            # Test with mock data since we're using a test database
            with patch.object(geolocation_service, 'detect_country_from_ip') as mock_detect:
                mock_detect.return_value = GeolocationTestHelper.create_test_geolocation_result(
                    "*******", "US", 0.95
                )
                
                result = await geolocation_service.detect_country_from_ip("*******")
                
                assert result.country_code == "US"
                assert result.confidence_score == 0.95
                assert result.ip_address == "*******"
                mock_detect.assert_called_once_with("*******")
    
    @pytest.mark.asyncio
    async def test_geolocation_service_with_known_ips(self, known_country_ips, mock_geolocation_service):
        """Test geolocation service with known IP addresses."""
        for ip_address, expected_data in known_country_ips.items():
            result = await mock_geolocation_service.detect_country_from_ip(ip_address)
            
            assert result.country_code == expected_data["country_code"]
            assert result.country_name == expected_data["country_name"]
            assert result.continent_code == expected_data["continent_code"]
            assert result.ip_address == ip_address
            assert result.confidence_score == expected_data["confidence"]
    
    @pytest.mark.asyncio
    async def test_geolocation_service_error_handling(self, private_ip_addresses, mock_geolocation_service):
        """Test geolocation service error handling with private IPs."""
        for private_ip in private_ip_addresses:
            with pytest.raises(Exception):
                await mock_geolocation_service.detect_country_from_ip(private_ip)
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self, test_circuit_breaker, mock_geolocation_service):
        """Test circuit breaker integration with geolocation service."""
        # Configure mock to fail initially
        failure_count = 0
        
        async def failing_detect_country(ip_address: str):
            nonlocal failure_count
            failure_count += 1
            if failure_count <= 3:  # Fail first 3 attempts
                raise Exception("Simulated database failure")
            return GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
        
        mock_geolocation_service.detect_country_from_ip.side_effect = failing_detect_country
        
        # Test circuit breaker opening after failures
        for i in range(3):
            with pytest.raises(Exception):
                await test_circuit_breaker.detect_country_with_protection(
                    mock_geolocation_service, "*******"
                )
        
        # Circuit breaker should be open now
        stats = test_circuit_breaker.get_stats()
        assert stats["state"] == "open"
        assert stats["failed_requests"] == 3
    
    @pytest.mark.asyncio
    async def test_resilience_manager_fallback(self, test_resilience_manager, mock_geolocation_service):
        """Test resilience manager fallback mechanisms."""
        # Configure mock to always fail
        mock_geolocation_service.detect_country_from_ip.side_effect = Exception("Database unavailable")
        
        # Test fallback to default country
        result = await test_resilience_manager.detect_country_with_resilience(
            mock_geolocation_service, "*******"
        )
        
        assert result.country_code == "NG"  # Default fallback country
        assert result.detection_method == "default_country_fallback"
        assert result.confidence_score == 0.3  # Fallback confidence
    
    @pytest.mark.asyncio
    async def test_resilience_manager_user_preference_fallback(self, test_resilience_manager, mock_geolocation_service):
        """Test resilience manager user preference fallback."""
        # Configure mock to always fail
        mock_geolocation_service.detect_country_from_ip.side_effect = Exception("Database unavailable")
        
        # Test fallback to user preference
        user_preferences = {"preferred_country": "US"}
        result = await test_resilience_manager.detect_country_with_resilience(
            mock_geolocation_service, "*******", user_preferences
        )
        
        assert result.country_code == "US"
        assert result.detection_method == "user_preference_fallback"
        assert result.confidence_score == 0.3
    
    @pytest.mark.asyncio
    async def test_redis_caching_integration(self, redis_test_client, mock_geolocation_service):
        """Test Redis caching integration."""
        # Mock geolocation service with caching
        cache_key = "geo:test:*******"
        test_result = GeolocationTestHelper.create_test_geolocation_result("*******", "US", 0.95)
        
        # Test cache miss
        cached_result = await redis_test_client.get(cache_key)
        assert cached_result is None
        
        # Simulate caching the result
        import json
        await redis_test_client.setex(
            cache_key, 
            3600,  # 1 hour TTL
            json.dumps({
                "country_code": test_result.country_code,
                "country_name": test_result.country_name,
                "continent_code": test_result.continent_code,
                "confidence_score": test_result.confidence_score,
                "detection_method": test_result.detection_method,
                "detected_at": test_result.detected_at
            })
        )
        
        # Test cache hit
        cached_result = await redis_test_client.get(cache_key)
        assert cached_result is not None
        
        cached_data = json.loads(cached_result)
        assert cached_data["country_code"] == "US"
        assert cached_data["confidence_score"] == 0.95
    
    @pytest.mark.asyncio
    async def test_payment_routing_integration(self, mock_payment_provider_manager, geolocation_test_data):
        """Test payment provider routing based on geolocation."""
        for scenario in geolocation_test_data.PAYMENT_ROUTING_SCENARIOS:
            provider, routing_decision = await mock_payment_provider_manager.select_provider(
                country_code=scenario["country"],
                currency=scenario["currency"],
                amount=scenario["amount"]
            )
            
            assert provider.value.lower() == scenario["expected_provider"]
            assert routing_decision.selected_provider == provider
            assert routing_decision.confidence_score > 0.8
    
    @pytest.mark.asyncio
    async def test_health_check_endpoints(self):
        """Test geolocation health check endpoints."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Test main health endpoint
            response = await client.get("/api/v1/health/")
            assert response.status_code == 200
            
            health_data = response.json()
            assert health_data["status"] in ["healthy", "degraded"]
            assert "checks" in health_data
            
            # Test geolocation-specific health endpoint
            with patch('app.services.geolocation_service.get_geolocation_service') as mock_service:
                mock_service.return_value.detect_country_from_ip = AsyncMock(
                    return_value=GeolocationTestHelper.create_test_geolocation_result("*******", "US", 0.95)
                )
                mock_service.return_value.geoip_reader = True  # Mock database availability
                
                response = await client.get("/api/v1/health/geolocation")
                assert response.status_code == 200
                
                geo_health = response.json()
                assert geo_health["service"] == "geolocation"
                assert geo_health["status"] in ["healthy", "degraded"]
                assert "performance" in geo_health
                assert "details" in geo_health
    
    @pytest.mark.asyncio
    async def test_payment_routing_health_check(self):
        """Test payment routing health check endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            with patch('app.core.payment.providers.get_payment_provider_manager') as mock_manager:
                # Mock successful routing
                from app.core.payment.providers import PaymentProvider
                from app.core.payment.routing import RoutingDecision
                
                async def mock_select_provider(country_code, currency, amount):
                    if country_code == "NG":
                        provider = PaymentProvider.PAYSTACK
                    elif currency in ["BTC", "ETH"]:
                        provider = PaymentProvider.BUSHA
                    else:
                        provider = PaymentProvider.STRIPE
                    
                    routing_decision = RoutingDecision(
                        selected_provider=provider,
                        routing_reason="Test routing",
                        confidence_score=0.9,
                        fallback_providers=[],
                        routing_metadata={}
                    )
                    return provider, routing_decision
                
                mock_manager.return_value.select_provider = AsyncMock(side_effect=mock_select_provider)
                
                response = await client.get("/api/v1/health/payment-routing")
                assert response.status_code == 200
                
                routing_health = response.json()
                assert routing_health["service"] == "payment_routing"
                assert routing_health["status"] in ["healthy", "degraded"]
                assert "providers" in routing_health
                assert "details" in routing_health
    
    @pytest.mark.asyncio
    async def test_end_to_end_geolocation_flow(self, mock_geolocation_service, mock_payment_provider_manager):
        """Test complete end-to-end geolocation flow."""
        # Step 1: Detect country from IP
        ip_address = "************"  # Nigerian IP
        geo_result = await mock_geolocation_service.detect_country_from_ip(ip_address)
        
        assert geo_result.country_code == "NG"
        assert geo_result.continent_code == "AF"
        
        # Step 2: Route payment based on detected country
        provider, routing_decision = await mock_payment_provider_manager.select_provider(
            country_code=geo_result.country_code,
            currency="NGN",
            amount=10000.0
        )
        
        assert provider.value.lower() == "paystack"
        assert routing_decision.routing_reason == "African country with NGN currency"
        
        # Step 3: Verify routing decision confidence
        assert routing_decision.confidence_score >= 0.8
    
    @pytest.mark.asyncio
    async def test_concurrent_geolocation_requests(self, mock_geolocation_service, geolocation_test_helper):
        """Test concurrent geolocation requests."""
        ip_addresses = ["*******", "*******", "************", "***********"]
        concurrent_count = 20
        
        results = await geolocation_test_helper.simulate_concurrent_requests(
            mock_geolocation_service, ip_addresses, concurrent_count
        )
        
        assert results["total_requests"] == concurrent_count
        assert results["success_rate"] >= 0.95  # 95% success rate
        assert results["avg_time_per_request_ms"] < 200  # Under 200ms average
    
    @pytest.mark.asyncio
    async def test_cache_consistency_validation(self, mock_geolocation_service, geolocation_test_helper):
        """Test cache consistency validation."""
        ip_addresses = ["*******", "*******", "************"]
        
        cache_results = await geolocation_test_helper.test_cache_performance(
            mock_geolocation_service, ip_addresses
        )
        
        # Verify cache improves performance
        assert cache_results["cache_improvement_percent"] >= 0  # Some improvement expected
        assert cache_results["second_round_avg_ms"] <= cache_results["first_round_avg_ms"]
    
    @pytest.mark.asyncio
    async def test_error_recovery_scenarios(self, test_resilience_manager, mock_geolocation_service):
        """Test error recovery scenarios."""
        failure_count = 0
        
        async def intermittent_failure(ip_address: str):
            nonlocal failure_count
            failure_count += 1
            if failure_count % 3 == 0:  # Fail every 3rd request
                raise Exception("Intermittent database failure")
            return GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
        
        mock_geolocation_service.detect_country_from_ip.side_effect = intermittent_failure
        
        # Test multiple requests with intermittent failures
        successful_requests = 0
        total_requests = 10
        
        for i in range(total_requests):
            try:
                result = await test_resilience_manager.detect_country_with_resilience(
                    mock_geolocation_service, f"8.8.8.{i}"
                )
                successful_requests += 1
                assert result.country_code in ["US", "NG"]  # Either detected or fallback
            except Exception:
                pass  # Some failures expected
        
        # Should have high success rate due to fallback mechanisms
        success_rate = successful_requests / total_requests
        assert success_rate >= 0.8  # 80% success rate with fallbacks
