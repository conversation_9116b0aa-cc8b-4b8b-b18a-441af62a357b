"""
Unit tests for CompetitiveAnalysisService.

This module provides comprehensive unit tests for the competitive analysis service
implementing Task 3.2.2 Phase 5 requirements with >80% test coverage.

Test Coverage:
- Market position analysis
- Competitive intelligence gathering
- Market opportunity identification
- Pricing and quality analysis
- Error handling and edge cases
- Service method validation
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Dict, Any

from app.services.competitive_analysis_service import CompetitiveAnalysisService
from app.schemas.marketplace_optimization import CompetitiveAnalysisResponse


class TestableCompetitiveAnalysisService(CompetitiveAnalysisService):
    """Testable version of CompetitiveAnalysisService with implemented abstract methods."""
    
    async def validate_create_data(self, data: dict) -> dict:
        """Mock implementation of abstract method."""
        return data
    
    async def validate_update_data(self, data: dict, record_id: int = None) -> dict:
        """Mock implementation of abstract method."""
        return data


class TestCompetitiveAnalysisService:
    """Test suite for CompetitiveAnalysisService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_db_session):
        """Create CompetitiveAnalysisService instance with mocked dependencies."""
        return TestableCompetitiveAnalysisService(mock_db_session)

    @pytest.fixture
    def sample_service_data(self):
        """Sample service data for testing."""
        return MagicMock(
            id=1,
            title="Cultural Heritage Tour",
            description="Experience authentic Nigerian culture",
            vendor_id=1,
            category_id=1,
            price=Decimal("50.00"),
            rating=Decimal("4.5"),
            review_count=25,
            created_at=datetime.now() - timedelta(days=30)
        )

    @pytest.fixture
    def sample_competitor_data(self):
        """Sample competitor data for testing."""
        return [
            {
                "id": 2,
                "title": "Lagos Cultural Experience",
                "price": Decimal("45.00"),
                "rating": Decimal("4.2"),
                "review_count": 30,
                "vendor_id": 2
            },
            {
                "id": 3,
                "title": "Traditional Nigerian Tour",
                "price": Decimal("60.00"),
                "rating": Decimal("4.7"),
                "review_count": 40,
                "vendor_id": 3
            },
            {
                "id": 4,
                "title": "Heritage Walk Lagos",
                "price": Decimal("35.00"),
                "rating": Decimal("4.0"),
                "review_count": 15,
                "vendor_id": 4
            }
        ]

    @pytest.fixture
    def sample_competitive_analysis(self):
        """Sample competitive analysis response."""
        return CompetitiveAnalysisResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            market_category="cultural_tours",
            geographic_scope="local",
            total_competitors=10,
            market_position_rank=3,
            market_share_percentage=Decimal("15.50"),
            pricing_position="mid_range",
            quality_position="above_average",
            unique_selling_points=["authentic experience", "expert guides"],
            competitive_advantages=["local expertise", "cultural authenticity"],
            competitive_gaps=["limited marketing", "fewer reviews"],
            threat_level="medium",
            opportunity_score=Decimal("75.00"),
            analysis_date=datetime.now(),
            analysis_version="1.0"
        )

    @pytest.mark.asyncio
    async def test_analyze_market_position_success(self, service, sample_service_data, sample_competitor_data):
        """Test successful market position analysis."""
        service_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"
        force_refresh = False

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_competitive_repo, \
             patch.object(service, 'competitor_repository') as mock_competitor_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock no existing analysis
            mock_competitive_repo.get_latest_by_service.return_value = None
            
            # Mock competitor data
            mock_competitor_repo.get_competitors.return_value = sample_competitor_data

            # Execute method
            result = await service.analyze_market_position(
                service_id, market_category, geographic_scope, force_refresh
            )

            # Assertions
            assert isinstance(result, CompetitiveAnalysisResponse)
            assert result.service_id == service_id
            assert result.vendor_id == sample_service_data.vendor_id
            assert result.market_category == market_category
            assert result.geographic_scope == geographic_scope
            assert result.total_competitors > 0
            assert 1 <= result.market_position_rank <= result.total_competitors
            assert 0 <= result.market_share_percentage <= 100
            assert result.pricing_position in ["budget", "mid_range", "premium"]
            assert result.quality_position in ["below_average", "average", "above_average", "premium"]

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)
            mock_competitor_repo.get_competitors.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_market_position_existing_analysis_no_refresh(self, service, sample_competitive_analysis):
        """Test market position analysis returns existing analysis when not forcing refresh."""
        service_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"
        force_refresh = False

        with patch.object(service, 'repository') as mock_competitive_repo:
            # Mock existing recent analysis
            mock_competitive_repo.get_latest_by_service.return_value = sample_competitive_analysis

            # Execute method
            result = await service.analyze_market_position(
                service_id, market_category, geographic_scope, force_refresh
            )

            # Assertions
            assert result == sample_competitive_analysis
            mock_competitive_repo.get_latest_by_service.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_analyze_market_position_force_refresh(self, service, sample_service_data, sample_competitor_data, sample_competitive_analysis):
        """Test market position analysis with force refresh creates new analysis."""
        service_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"
        force_refresh = True

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_competitive_repo, \
             patch.object(service, 'competitor_repository') as mock_competitor_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock existing analysis (should be ignored due to force_refresh)
            mock_competitive_repo.get_latest_by_service.return_value = sample_competitive_analysis
            
            # Mock competitor data
            mock_competitor_repo.get_competitors.return_value = sample_competitor_data

            # Execute method
            result = await service.analyze_market_position(
                service_id, market_category, geographic_scope, force_refresh
            )

            # Assertions
            assert isinstance(result, CompetitiveAnalysisResponse)
            assert result.service_id == service_id
            # Should create new analysis, not return existing one
            assert result.analysis_date != sample_competitive_analysis.analysis_date

    @pytest.mark.asyncio
    async def test_analyze_market_position_service_not_found(self, service):
        """Test market position analysis when service not found."""
        service_id = 999
        market_category = "cultural_tours"
        geographic_scope = "local"

        with patch.object(service, 'service_repository') as mock_service_repo:
            mock_service_repo.get_by_id.return_value = None

            with pytest.raises(Exception) as exc_info:
                await service.analyze_market_position(service_id, market_category, geographic_scope, False)
            
            assert "not found" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_get_market_intelligence_success(self, service, sample_competitor_data):
        """Test successful market intelligence generation."""
        market_category = "cultural_tours"
        geographic_scope = "local"
        days = 90

        with patch.object(service, 'market_repository') as mock_market_repo, \
             patch.object(service, 'competitor_repository') as mock_competitor_repo:

            # Mock market data
            mock_market_repo.get_market_statistics.return_value = {
                "total_services": 50,
                "total_vendors": 25,
                "average_price": Decimal("45.00"),
                "average_rating": Decimal("4.3"),
                "market_growth_rate": Decimal("15.5")
            }
            
            # Mock competitor data
            mock_competitor_repo.get_market_competitors.return_value = sample_competitor_data

            # Execute method
            result = await service.get_market_intelligence(market_category, geographic_scope, days)

            # Assertions
            assert isinstance(result, dict)
            assert "market_category" in result
            assert "geographic_scope" in result
            assert "market_statistics" in result
            assert "market_trends" in result
            assert "market_segmentation" in result
            assert "top_performers" in result
            assert "market_opportunities" in result
            assert "competitive_dynamics" in result

            # Verify market statistics
            market_stats = result["market_statistics"]
            assert "total_competitors" in market_stats
            assert "market_size" in market_stats
            assert "average_pricing" in market_stats
            assert "quality_distribution" in market_stats

            # Verify market segmentation
            segmentation = result["market_segmentation"]
            assert "leaders" in segmentation
            assert "challengers" in segmentation
            assert "followers" in segmentation
            assert "niche_players" in segmentation

            # Verify repository calls
            mock_market_repo.get_market_statistics.assert_called_once()
            mock_competitor_repo.get_market_competitors.assert_called_once()

    @pytest.mark.asyncio
    async def test_identify_competitive_opportunities_success(self, service, sample_service_data, sample_competitor_data):
        """Test successful competitive opportunities identification."""
        vendor_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"

        with patch.object(service, 'vendor_repository') as mock_vendor_repo, \
             patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'competitor_repository') as mock_competitor_repo, \
             patch.object(service, 'market_repository') as mock_market_repo:

            # Mock vendor exists
            mock_vendor_repo.get_by_id.return_value = MagicMock(id=vendor_id, business_name="Test Vendor")
            
            # Mock vendor services
            mock_service_repo.get_by_vendor.return_value = [sample_service_data]
            
            # Mock competitor data
            mock_competitor_repo.get_market_competitors.return_value = sample_competitor_data
            
            # Mock market gaps
            mock_market_repo.get_market_gaps.return_value = [
                {"gap_type": "pricing", "opportunity": "premium_segment"},
                {"gap_type": "service", "opportunity": "family_tours"}
            ]

            # Execute method
            result = await service.identify_competitive_opportunities(
                vendor_id, market_category, geographic_scope
            )

            # Assertions
            assert isinstance(result, dict)
            assert "vendor_id" in result
            assert "market_opportunities" in result
            assert "threat_analysis" in result
            assert "strategic_recommendations" in result
            assert "opportunity_scoring" in result
            assert "priority_actions" in result

            # Verify market opportunities
            opportunities = result["market_opportunities"]
            assert isinstance(opportunities, list)
            assert len(opportunities) > 0

            # Verify threat analysis
            threat_analysis = result["threat_analysis"]
            assert "competitive_threats" in threat_analysis
            assert "market_risks" in threat_analysis
            assert "threat_level" in threat_analysis

            # Verify strategic recommendations
            recommendations = result["strategic_recommendations"]
            assert isinstance(recommendations, list)
            assert len(recommendations) > 0

            # Verify repository calls
            mock_vendor_repo.get_by_id.assert_called_once_with(vendor_id)
            mock_service_repo.get_by_vendor.assert_called_once_with(vendor_id)

    @pytest.mark.asyncio
    async def test_calculate_market_position_rank(self, service, sample_service_data, sample_competitor_data):
        """Test market position rank calculation."""
        # Execute method (assuming this is a testable method)
        rank = service._calculate_market_position_rank(sample_service_data, sample_competitor_data)

        # Assertions
        assert isinstance(rank, int)
        assert 1 <= rank <= len(sample_competitor_data) + 1

    @pytest.mark.asyncio
    async def test_calculate_market_share(self, service, sample_service_data, sample_competitor_data):
        """Test market share calculation."""
        # Execute method (assuming this is a testable method)
        market_share = service._calculate_market_share(sample_service_data, sample_competitor_data)

        # Assertions
        assert isinstance(market_share, (int, float, Decimal))
        assert 0 <= market_share <= 100

    @pytest.mark.asyncio
    async def test_determine_pricing_position(self, service, sample_service_data, sample_competitor_data):
        """Test pricing position determination."""
        # Execute method (assuming this is a testable method)
        pricing_position = service._determine_pricing_position(sample_service_data, sample_competitor_data)

        # Assertions
        assert pricing_position in ["budget", "mid_range", "premium"]

    @pytest.mark.asyncio
    async def test_determine_quality_position(self, service, sample_service_data, sample_competitor_data):
        """Test quality position determination."""
        # Execute method (assuming this is a testable method)
        quality_position = service._determine_quality_position(sample_service_data, sample_competitor_data)

        # Assertions
        assert quality_position in ["below_average", "average", "above_average", "premium"]

    @pytest.mark.asyncio
    async def test_identify_unique_selling_points(self, service, sample_service_data, sample_competitor_data):
        """Test unique selling points identification."""
        # Execute method (assuming this is a testable method)
        usps = service._identify_unique_selling_points(sample_service_data, sample_competitor_data)

        # Assertions
        assert isinstance(usps, list)
        # Should identify at least some unique aspects
        assert len(usps) > 0

    @pytest.mark.asyncio
    async def test_analyze_competitive_gaps(self, service, sample_service_data, sample_competitor_data):
        """Test competitive gaps analysis."""
        # Execute method (assuming this is a testable method)
        gaps = service._analyze_competitive_gaps(sample_service_data, sample_competitor_data)

        # Assertions
        assert isinstance(gaps, list)
        # Should identify areas for improvement
        for gap in gaps:
            assert "gap_type" in gap
            assert "description" in gap
            assert "impact" in gap

    @pytest.mark.asyncio
    async def test_calculate_threat_level(self, service, sample_service_data, sample_competitor_data):
        """Test threat level calculation."""
        # Execute method (assuming this is a testable method)
        threat_level = service._calculate_threat_level(sample_service_data, sample_competitor_data)

        # Assertions
        assert threat_level in ["low", "medium", "high"]

    @pytest.mark.asyncio
    async def test_calculate_opportunity_score(self, service):
        """Test opportunity score calculation."""
        market_data = {
            "market_growth_rate": 15.5,
            "competition_intensity": "medium",
            "market_gaps": ["premium_segment", "family_tours"],
            "vendor_strengths": ["local_expertise", "authenticity"]
        }

        # Execute method (assuming this is a testable method)
        opportunity_score = service._calculate_opportunity_score(market_data)

        # Assertions
        assert isinstance(opportunity_score, (int, float, Decimal))
        assert 0 <= opportunity_score <= 100

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db_session):
        """Test service initialization with dependencies."""
        service = TestableCompetitiveAnalysisService(mock_db_session)
        
        # Verify service is properly initialized
        assert service._db_session == mock_db_session
        assert hasattr(service, 'service_repository')
        assert hasattr(service, 'vendor_repository')
        assert hasattr(service, 'competitor_repository')
        assert hasattr(service, 'market_repository')
        assert hasattr(service, 'repository')

    @pytest.mark.asyncio
    async def test_error_handling_database_error(self, service):
        """Test error handling for database errors."""
        service_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"
        
        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock database error
            mock_service_repo.get_by_id.side_effect = Exception("Database connection error")

            with pytest.raises(Exception) as exc_info:
                await service.analyze_market_position(service_id, market_category, geographic_scope, False)
            
            assert "Database connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_competitive_analysis_with_no_competitors(self, service, sample_service_data):
        """Test competitive analysis with no competitors in market."""
        service_id = 1
        market_category = "cultural_tours"
        geographic_scope = "local"

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_competitive_repo, \
             patch.object(service, 'competitor_repository') as mock_competitor_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock no existing analysis
            mock_competitive_repo.get_latest_by_service.return_value = None
            
            # Mock no competitors
            mock_competitor_repo.get_competitors.return_value = []

            # Execute method
            result = await service.analyze_market_position(service_id, market_category, geographic_scope, False)

            # Assertions
            assert isinstance(result, CompetitiveAnalysisResponse)
            assert result.total_competitors == 0
            assert result.market_position_rank == 1  # Should be #1 if no competitors
            assert result.market_share_percentage == Decimal("100.00")  # Should have 100% market share
