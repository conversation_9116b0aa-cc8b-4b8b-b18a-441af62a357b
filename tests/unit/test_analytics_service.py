"""
Unit tests for Analytics Service.

This module provides comprehensive unit tests for the analytics service layer:
- AnalyticsService functionality testing
- User analytics processing validation
- Vendor analytics calculation testing
- Dashboard widget data generation
- Performance metrics validation

Implements >80% test coverage with pytest patterns and mock dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4

from app.services.analytics_service import AnalyticsService
from app.models.analytics_models import (
    UserAnalytics, VendorAnalytics, DashboardWidget,
    AnalyticsTimeframe, PerformanceMetricType
)
from app.schemas.analytics_schemas import (
    UserAnalyticsCreate, VendorAnalyticsCreate, DashboardWidgetCreate
)
from app.services.base import ServiceError, ValidationError


class TestAnalyticsService:
    """Test suite for AnalyticsService."""

    @pytest.fixture
    def mock_user_analytics_repo(self):
        """Mock user analytics repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_by_user_id = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        repo.get_analytics_summary = AsyncMock()
        return repo

    @pytest.fixture
    def mock_vendor_analytics_repo(self):
        """Mock vendor analytics repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_by_vendor_id = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        repo.get_analytics_summary = AsyncMock()
        return repo

    @pytest.fixture
    def mock_dashboard_widget_repo(self):
        """Mock dashboard widget repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_by_user_id = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        repo.get_active_widgets = AsyncMock()
        return repo

    @pytest.fixture
    def analytics_service(self, mock_user_analytics_repo, mock_vendor_analytics_repo, mock_dashboard_widget_repo):
        """Create AnalyticsService instance with mocked dependencies."""
        return AnalyticsService(
            user_analytics_repo=mock_user_analytics_repo,
            vendor_analytics_repo=mock_vendor_analytics_repo,
            dashboard_widget_repo=mock_dashboard_widget_repo
        )

    @pytest.fixture
    def sample_user_analytics_data(self):
        """Sample user analytics data."""
        return {
            "user_id": uuid4(),
            "timeframe": AnalyticsTimeframe.DAILY,
            "total_bookings": 5,
            "total_spent": Decimal("250.00"),
            "avg_booking_value": Decimal("50.00"),
            "favorite_categories": ["tours", "experiences"],
            "booking_frequency": Decimal("1.2"),
            "satisfaction_score": Decimal("4.5"),
            "period_start": datetime.now(timezone.utc) - timedelta(days=1),
            "period_end": datetime.now(timezone.utc)
        }

    @pytest.fixture
    def sample_vendor_analytics_data(self):
        """Sample vendor analytics data."""
        return {
            "vendor_id": uuid4(),
            "timeframe": AnalyticsTimeframe.DAILY,
            "total_bookings": 15,
            "total_revenue": Decimal("750.00"),
            "avg_booking_value": Decimal("50.00"),
            "booking_conversion_rate": Decimal("0.25"),
            "customer_satisfaction": Decimal("4.3"),
            "response_time_avg": Decimal("120.5"),
            "cancellation_rate": Decimal("0.05"),
            "period_start": datetime.now(timezone.utc) - timedelta(days=1),
            "period_end": datetime.now(timezone.utc)
        }

    @pytest.mark.asyncio
    async def test_create_user_analytics_success(self, analytics_service, mock_user_analytics_repo, sample_user_analytics_data):
        """Test successful user analytics creation."""
        # Arrange
        analytics_id = uuid4()
        mock_analytics = UserAnalytics(id=analytics_id, **sample_user_analytics_data)
        mock_user_analytics_repo.create.return_value = mock_analytics

        # Act
        result = await analytics_service.create_user_analytics(sample_user_analytics_data)

        # Assert
        assert result.id == analytics_id
        assert result.user_id == sample_user_analytics_data["user_id"]
        assert result.total_bookings == sample_user_analytics_data["total_bookings"]
        mock_user_analytics_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_user_analytics_validation_error(self, analytics_service, sample_user_analytics_data):
        """Test user analytics creation with validation error."""
        # Arrange
        invalid_data = sample_user_analytics_data.copy()
        invalid_data["total_bookings"] = -1  # Invalid negative value

        # Act & Assert
        with pytest.raises(ValidationError, match="Total bookings must be non-negative"):
            await analytics_service.create_user_analytics(invalid_data)

    @pytest.mark.asyncio
    async def test_create_vendor_analytics_success(self, analytics_service, mock_vendor_analytics_repo, sample_vendor_analytics_data):
        """Test successful vendor analytics creation."""
        # Arrange
        analytics_id = uuid4()
        mock_analytics = VendorAnalytics(id=analytics_id, **sample_vendor_analytics_data)
        mock_vendor_analytics_repo.create.return_value = mock_analytics

        # Act
        result = await analytics_service.create_vendor_analytics(sample_vendor_analytics_data)

        # Assert
        assert result.id == analytics_id
        assert result.vendor_id == sample_vendor_analytics_data["vendor_id"]
        assert result.total_revenue == sample_vendor_analytics_data["total_revenue"]
        mock_vendor_analytics_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_analytics_summary_success(self, analytics_service, mock_user_analytics_repo):
        """Test successful user analytics summary retrieval."""
        # Arrange
        user_id = uuid4()
        timeframe = AnalyticsTimeframe.WEEKLY
        mock_summary = {
            "total_bookings": 25,
            "total_spent": Decimal("1250.00"),
            "avg_booking_value": Decimal("50.00"),
            "booking_frequency": Decimal("3.5")
        }
        mock_user_analytics_repo.get_analytics_summary.return_value = mock_summary

        # Act
        result = await analytics_service.get_user_analytics_summary(user_id, timeframe)

        # Assert
        assert result == mock_summary
        mock_user_analytics_repo.get_analytics_summary.assert_called_once_with(user_id, timeframe)

    @pytest.mark.asyncio
    async def test_get_vendor_analytics_summary_success(self, analytics_service, mock_vendor_analytics_repo):
        """Test successful vendor analytics summary retrieval."""
        # Arrange
        vendor_id = uuid4()
        timeframe = AnalyticsTimeframe.MONTHLY
        mock_summary = {
            "total_bookings": 150,
            "total_revenue": Decimal("7500.00"),
            "avg_booking_value": Decimal("50.00"),
            "conversion_rate": Decimal("0.28")
        }
        mock_vendor_analytics_repo.get_analytics_summary.return_value = mock_summary

        # Act
        result = await analytics_service.get_vendor_analytics_summary(vendor_id, timeframe)

        # Assert
        assert result == mock_summary
        mock_vendor_analytics_repo.get_analytics_summary.assert_called_once_with(vendor_id, timeframe)

    @pytest.mark.asyncio
    async def test_create_dashboard_widget_success(self, analytics_service, mock_dashboard_widget_repo):
        """Test successful dashboard widget creation."""
        # Arrange
        widget_data = {
            "user_id": uuid4(),
            "widget_type": "revenue_chart",
            "title": "Monthly Revenue",
            "configuration": {"chart_type": "line", "period": "monthly"},
            "position_x": 0,
            "position_y": 0,
            "width": 6,
            "height": 4,
            "is_active": True
        }
        widget_id = uuid4()
        mock_widget = DashboardWidget(id=widget_id, **widget_data)
        mock_dashboard_widget_repo.create.return_value = mock_widget

        # Act
        result = await analytics_service.create_dashboard_widget(widget_data)

        # Assert
        assert result.id == widget_id
        assert result.widget_type == widget_data["widget_type"]
        assert result.title == widget_data["title"]
        mock_dashboard_widget_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_dashboard_widgets_success(self, analytics_service, mock_dashboard_widget_repo):
        """Test successful user dashboard widgets retrieval."""
        # Arrange
        user_id = uuid4()
        mock_widgets = [
            DashboardWidget(
                id=uuid4(),
                user_id=user_id,
                widget_type="revenue_chart",
                title="Revenue Chart",
                configuration={},
                position_x=0,
                position_y=0,
                width=6,
                height=4,
                is_active=True
            ),
            DashboardWidget(
                id=uuid4(),
                user_id=user_id,
                widget_type="booking_stats",
                title="Booking Statistics",
                configuration={},
                position_x=6,
                position_y=0,
                width=6,
                height=4,
                is_active=True
            )
        ]
        mock_dashboard_widget_repo.get_by_user_id.return_value = mock_widgets

        # Act
        result = await analytics_service.get_user_dashboard_widgets(user_id)

        # Assert
        assert len(result) == 2
        assert all(widget.user_id == user_id for widget in result)
        assert all(widget.is_active for widget in result)
        mock_dashboard_widget_repo.get_by_user_id.assert_called_once_with(user_id)

    @pytest.mark.asyncio
    async def test_calculate_performance_metrics_success(self, analytics_service):
        """Test successful performance metrics calculation."""
        # Arrange
        metric_type = PerformanceMetricType.API_RESPONSE_TIME
        component = "analytics_service"
        timeframe = AnalyticsTimeframe.HOURLY

        with patch.object(analytics_service, '_calculate_api_response_metrics') as mock_calculate:
            mock_metrics = {
                "avg_response_time": Decimal("150.5"),
                "max_response_time": Decimal("500.0"),
                "min_response_time": Decimal("50.0"),
                "total_requests": 1000
            }
            mock_calculate.return_value = mock_metrics

            # Act
            result = await analytics_service.calculate_performance_metrics(metric_type, component, timeframe)

            # Assert
            assert result == mock_metrics
            mock_calculate.assert_called_once_with(component, timeframe)

    @pytest.mark.asyncio
    async def test_update_user_analytics_success(self, analytics_service, mock_user_analytics_repo):
        """Test successful user analytics update."""
        # Arrange
        analytics_id = uuid4()
        update_data = {
            "total_bookings": 10,
            "total_spent": Decimal("500.00"),
            "satisfaction_score": Decimal("4.8")
        }
        mock_updated_analytics = UserAnalytics(id=analytics_id, **update_data)
        mock_user_analytics_repo.update.return_value = mock_updated_analytics

        # Act
        result = await analytics_service.update_user_analytics(analytics_id, update_data)

        # Assert
        assert result.id == analytics_id
        assert result.total_bookings == update_data["total_bookings"]
        assert result.satisfaction_score == update_data["satisfaction_score"]
        mock_user_analytics_repo.update.assert_called_once_with(analytics_id, update_data)

    @pytest.mark.asyncio
    async def test_delete_dashboard_widget_success(self, analytics_service, mock_dashboard_widget_repo):
        """Test successful dashboard widget deletion."""
        # Arrange
        widget_id = uuid4()
        mock_dashboard_widget_repo.delete.return_value = True

        # Act
        result = await analytics_service.delete_dashboard_widget(widget_id)

        # Assert
        assert result is True
        mock_dashboard_widget_repo.delete.assert_called_once_with(widget_id)

    @pytest.mark.asyncio
    async def test_service_error_handling(self, analytics_service, mock_user_analytics_repo):
        """Test service error handling."""
        # Arrange
        mock_user_analytics_repo.create.side_effect = Exception("Database error")
        analytics_data = {"user_id": uuid4(), "total_bookings": 5}

        # Act & Assert
        with pytest.raises(ServiceError, match="Failed to create user analytics"):
            await analytics_service.create_user_analytics(analytics_data)

    @pytest.mark.asyncio
    async def test_analytics_data_validation(self, analytics_service):
        """Test analytics data validation."""
        # Test negative values
        with pytest.raises(ValidationError):
            await analytics_service.create_user_analytics({
                "user_id": uuid4(),
                "total_bookings": -1
            })

        # Test invalid timeframe
        with pytest.raises(ValidationError):
            await analytics_service.create_vendor_analytics({
                "vendor_id": uuid4(),
                "timeframe": "invalid_timeframe"
            })

    @pytest.mark.asyncio
    async def test_widget_position_validation(self, analytics_service):
        """Test dashboard widget position validation."""
        # Test negative position
        with pytest.raises(ValidationError):
            await analytics_service.create_dashboard_widget({
                "user_id": uuid4(),
                "widget_type": "chart",
                "title": "Test Widget",
                "position_x": -1,
                "position_y": 0,
                "width": 4,
                "height": 4
            })

        # Test zero dimensions
        with pytest.raises(ValidationError):
            await analytics_service.create_dashboard_widget({
                "user_id": uuid4(),
                "widget_type": "chart",
                "title": "Test Widget",
                "position_x": 0,
                "position_y": 0,
                "width": 0,
                "height": 4
            })

    @pytest.mark.asyncio
    async def test_performance_metrics_caching(self, analytics_service):
        """Test performance metrics caching behavior."""
        # This test would verify that repeated calls to calculate_performance_metrics
        # use cached results when appropriate
        metric_type = PerformanceMetricType.DATABASE_QUERY_TIME
        component = "user_repository"
        timeframe = AnalyticsTimeframe.DAILY

        with patch.object(analytics_service, '_get_cached_metrics') as mock_cache:
            mock_cache.return_value = {"cached": True}

            result = await analytics_service.calculate_performance_metrics(metric_type, component, timeframe)

            assert result == {"cached": True}
            mock_cache.assert_called_once()
