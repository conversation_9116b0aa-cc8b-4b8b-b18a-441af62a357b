"""
Unit tests for enhanced WebSocket services.

This module provides comprehensive unit tests for enhanced WebSocket service layer including:
- WebSocketRoomService with room management and business logic validation
- WebSocketRoomParticipantService with participant tracking and role management
- Enhanced WebSocketConnectionService with room integration and broadcasting
- Performance validation with <500ms service operation targets

Implements Task 6.1.1 Phase 5 requirements with >85% code coverage and business logic testing.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.websocket_services import (
    WebSocketRoomService, WebSocketRoomParticipantService,
    WebSocketConnectionService
)
from app.schemas.websocket_schemas import (
    WebSocketRoomCreate, WebSocketRoomResponse,
    WebSocketRoomParticipantCreate, WebSocketRoomParticipantResponse,
    WebSocketConnectionResponse
)
from app.models.websocket_models import (
    WebSocketRoom, WebSocketRoomParticipant, WebSocketConnection
)
from app.models.user import User
from app.services.base import ValidationError, ConflictError, NotFoundError, ServiceError
from app.core.cache import CacheManager


@pytest.mark.unit
class TestWebSocketRoomService:
    """Unit tests for WebSocketRoomService with business logic validation."""

    @pytest.fixture
    async def room_service(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomService instance for testing."""
        return WebSocketRoomService(db_session, cache_manager)

    @pytest.fixture
    def sample_room_create(self):
        """Sample room creation data."""
        return WebSocketRoomCreate(
            room_id="test_room_123",
            room_name="Test Room",
            room_type="booking",
            is_private=True,
            max_participants=10,
            booking_id=123,
            owner_id=1
        )

    @pytest.fixture
    def mock_user(self):
        """Mock user for testing."""
        user = MagicMock(spec=User)
        user.id = 1
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_create_room_with_validation(
        self,
        room_service: WebSocketRoomService,
        sample_room_create: WebSocketRoomCreate,
        mock_user: User,
        performance_timer
    ):
        """Test room creation with comprehensive validation."""
        timer = performance_timer()
        
        # Mock repository operations
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id=sample_room_create.room_id,
                room_name=sample_room_create.room_name,
                room_type=sample_room_create.room_type,
                is_private=sample_room_create.is_private,
                max_participants=sample_room_create.max_participants,
                booking_id=sample_room_create.booking_id,
                owner_id=sample_room_create.owner_id,
                is_active=True,
                participant_count=0,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.create_room.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock participant service for owner addition
            with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_participant_service_class:
                mock_participant_service = AsyncMock()
                mock_participant_service_class.return_value = mock_participant_service
                
                # Mock notification sending
                with patch.object(room_service, '_send_room_creation_notifications') as mock_notifications:
                    mock_notifications.return_value = None
                    
                    timer.start()
                    result = await room_service.create_room(sample_room_create, mock_user)
                    timer.stop()
                    
                    # Performance validation: <200ms for room creation
                    timer.assert_under_ms(200, "Room creation")
                    
                    # Verify result
                    assert isinstance(result, WebSocketRoomResponse)
                    assert result.room_id == sample_room_create.room_id
                    assert result.room_name == sample_room_create.room_name
                    
                    # Verify owner was added as participant
                    mock_participant_service.add_participant.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_room_by_id_with_access_control(
        self,
        room_service: WebSocketRoomService,
        sample_room_create: WebSocketRoomCreate,
        mock_user: User,
        performance_timer
    ):
        """Test room retrieval with access control validation."""
        timer = performance_timer()
        room_id = sample_room_create.room_id
        
        # Mock repository operations
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id=room_id,
                room_name=sample_room_create.room_name,
                room_type=sample_room_create.room_type,
                is_private=True,
                owner_id=mock_user.id,
                is_active=True,
                participant_count=0,
                max_participants=10,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.get_room_by_id.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock access validation
            with patch.object(room_service, '_validate_room_access_permissions') as mock_validate:
                mock_validate.return_value = None
                
                timer.start()
                result = await room_service.get_room_by_id(room_id, mock_user)
                timer.stop()
                
                # Performance validation: <100ms for room retrieval
                timer.assert_under_ms(100, "Room retrieval")
                
                # Verify result
                assert isinstance(result, WebSocketRoomResponse)
                assert result.room_id == room_id
                
                # Verify access validation was called
                mock_validate.assert_called_once_with(mock_user, mock_room)

    @pytest.mark.asyncio
    async def test_close_room_with_participant_notification(
        self,
        room_service: WebSocketRoomService,
        sample_room_create: WebSocketRoomCreate,
        mock_user: User,
        performance_timer
    ):
        """Test room closure with participant notification."""
        timer = performance_timer()
        room_id = sample_room_create.room_id
        
        # Mock repository operations
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id=room_id,
                room_name=sample_room_create.room_name,
                room_type=sample_room_create.room_type,
                is_private=True,
                owner_id=mock_user.id,
                is_active=True,
                participant_count=2,
                max_participants=10,
                total_messages=5,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.get_room_by_id.return_value = mock_room
            mock_repo.update.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(room_service, '_validate_room_closure_permissions') as mock_validate:
                mock_validate.return_value = None
                
                # Mock participant service for bulk removal
                with patch('app.services.websocket_services.WebSocketRoomParticipantService') as mock_participant_service_class:
                    mock_participant_service = AsyncMock()
                    mock_participants = [
                        MagicMock(user_id=1),
                        MagicMock(user_id=2)
                    ]
                    mock_participant_service.get_room_participants.return_value = mock_participants
                    mock_participant_service.bulk_remove_participants.return_value = 2
                    mock_participant_service_class.return_value = mock_participant_service
                    
                    # Mock notification sending
                    with patch.object(room_service, '_notify_room_closure') as mock_notifications:
                        mock_notifications.return_value = None
                        
                        timer.start()
                        result = await room_service.close_room(room_id, mock_user)
                        timer.stop()
                        
                        # Performance validation: <300ms for room closure
                        timer.assert_under_ms(300, "Room closure")
                        
                        # Verify result
                        assert result is True
                        
                        # Verify participants were removed
                        mock_participant_service.bulk_remove_participants.assert_called_once()
                        
                        # Verify notifications were sent
                        mock_notifications.assert_called_once()

    @pytest.mark.asyncio
    async def test_room_not_found_handling(
        self,
        room_service: WebSocketRoomService,
        mock_user: User
    ):
        """Test handling of room not found scenarios."""
        room_id = "nonexistent_room"
        
        # Mock repository returning None
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_repo.get_room_by_id.return_value = None
            mock_get_repo.return_value = mock_repo
            
            result = await room_service.get_room_by_id(room_id, mock_user)
            
            # Should return None for not found
            assert result is None

    @pytest.mark.asyncio
    async def test_permission_validation_errors(
        self,
        room_service: WebSocketRoomService,
        sample_room_create: WebSocketRoomCreate,
        mock_user: User
    ):
        """Test permission validation error handling."""
        # Test room creation permission error
        room_create_data = sample_room_create.model_copy()
        room_create_data.owner_id = 999  # Different owner
        
        with patch.object(room_service, '_validate_room_creation_permissions') as mock_validate:
            mock_validate.side_effect = ValidationError("Insufficient permissions")
            
            with pytest.raises(ValidationError) as exc_info:
                await room_service.create_room(room_create_data, mock_user)
            
            assert "Insufficient permissions" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_circuit_breaker_email_notifications(
        self,
        room_service: WebSocketRoomService,
        sample_room_create: WebSocketRoomCreate,
        mock_user: User
    ):
        """Test circuit breaker pattern for email notifications."""
        # Mock repository operations
        with patch.object(room_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_room = WebSocketRoom(
                id=1,
                room_id=sample_room_create.room_id,
                room_name=sample_room_create.room_name,
                room_type=sample_room_create.room_type,
                is_private=sample_room_create.is_private,
                max_participants=sample_room_create.max_participants,
                booking_id=sample_room_create.booking_id,
                owner_id=sample_room_create.owner_id,
                is_active=True,
                participant_count=0,
                total_messages=0,
                created_at=datetime.now(timezone.utc),
                last_activity_at=datetime.now(timezone.utc)
            )
            mock_repo.create_room.return_value = mock_room
            mock_get_repo.return_value = mock_repo
            
            # Mock circuit breaker failure
            with patch.object(room_service._email_circuit_breaker, 'call') as mock_circuit_breaker:
                mock_circuit_breaker.side_effect = Exception("Email service unavailable")
                
                # Should still succeed despite email failure
                result = await room_service.create_room(sample_room_create, mock_user)
                
                # Verify room was created
                assert isinstance(result, WebSocketRoomResponse)
                assert result.room_id == sample_room_create.room_id


@pytest.mark.unit
class TestWebSocketRoomParticipantService:
    """Unit tests for WebSocketRoomParticipantService with role management."""

    @pytest.fixture
    async def participant_service(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomParticipantService instance for testing."""
        return WebSocketRoomParticipantService(db_session, cache_manager)

    @pytest.fixture
    def mock_user(self):
        """Mock user for testing."""
        user = MagicMock(spec=User)
        user.id = 1
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_add_participant_with_role_validation(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test participant addition with role validation."""
        timer = performance_timer()
        room_id = 1
        user_id = 123
        role = "moderator"
        
        # Mock repository operations
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_participant = WebSocketRoomParticipant(
                id=1,
                room_id=room_id,
                user_id=user_id,
                role=role,
                is_active=True,
                joined_at=datetime.now(timezone.utc),
                last_seen_at=datetime.now(timezone.utc)
            )
            mock_repo.add_participant.return_value = mock_participant
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(participant_service, '_validate_participant_addition_permissions') as mock_validate:
                mock_validate.return_value = None
                
                # Mock notification sending
                with patch.object(participant_service, '_send_participant_addition_notifications') as mock_notifications:
                    mock_notifications.return_value = None
                    
                    timer.start()
                    result = await participant_service.add_participant(
                        room_id=room_id,
                        user_id=user_id,
                        role=role,
                        current_user=mock_user
                    )
                    timer.stop()
                    
                    # Performance validation: <200ms for participant addition
                    timer.assert_under_ms(200, "Participant addition")
                    
                    # Verify result
                    assert isinstance(result, WebSocketRoomParticipantResponse)
                    assert result.user_id == user_id
                    assert result.role == role
                    
                    # Verify notifications were sent
                    mock_notifications.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_remove_participants_performance(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User,
        performance_timer
    ):
        """Test bulk participant removal with performance validation."""
        timer = performance_timer()
        room_id = 1
        user_ids = [1, 2, 3, 4, 5]  # 5 participants
        
        # Mock repository operations
        with patch.object(participant_service, '_get_repository') as mock_get_repo:
            mock_repo = AsyncMock()
            mock_repo.bulk_remove_participants.return_value = 5
            mock_get_repo.return_value = mock_repo
            
            # Mock permission validation
            with patch.object(participant_service, '_validate_bulk_participant_removal_permissions') as mock_validate:
                mock_validate.return_value = None
                
                # Mock notification sending
                with patch.object(participant_service, '_send_bulk_participant_removal_notifications') as mock_notifications:
                    mock_notifications.return_value = None
                    
                    timer.start()
                    result = await participant_service.bulk_remove_participants(
                        room_id=room_id,
                        user_ids=user_ids,
                        current_user=mock_user
                    )
                    timer.stop()
                    
                    # Performance validation: <300ms for bulk operations
                    timer.assert_under_ms(300, "Bulk participant removal")
                    
                    # Verify result
                    assert result == 5
                    
                    # Verify notifications were sent
                    mock_notifications.assert_called_once()

    @pytest.mark.asyncio
    async def test_participant_role_permission_validation(
        self,
        participant_service: WebSocketRoomParticipantService,
        mock_user: User
    ):
        """Test role-based permission validation."""
        room_id = 1
        target_user_id = 123
        
        # Test insufficient permissions
        with patch.object(participant_service, '_validate_participant_addition_permissions') as mock_validate:
            mock_validate.side_effect = ValidationError("Insufficient permissions to add participant")
            
            with pytest.raises(ValidationError) as exc_info:
                await participant_service.add_participant(
                    room_id=room_id,
                    user_id=target_user_id,
                    current_user=mock_user
                )
            
            assert "Insufficient permissions" in str(exc_info.value)
