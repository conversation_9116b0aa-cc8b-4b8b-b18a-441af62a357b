"""
Payment provider configuration and management for Culture Connect Backend API.

This package provides centralized payment provider configuration management including:
- Multi-provider support (<PERSON>e, Paystack, Busha)
- Geo-location based payment routing
- Provider switching and fallback logic
- Secure configuration management with environment variables
- Comprehensive TODO markers for production deployment

Implements Phase 1 of Payment & Transaction Management System with:
- Centralized payment provider configurations
- Environment-based configuration management
- Provider-specific API endpoint configurations
- Webhook URL management and security
- Fallback mechanisms for provider failures

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from .config import (
    PaymentProviderConfig,
    StripeConfig,
    PaystackConfig,
    BushaConfig,
    PaymentProviderType,
    PaymentProviderSettings,
    get_payment_provider_config
)

from .providers import (
    PaymentProviderManager,
    PaymentProviderSelector,
    ProviderSwitchingStrategy,
    GeoLocationBasedSelector,
    FallbackProviderManager,
    get_payment_provider_manager
)

from .security import (
    PaymentSecurityManager,
    WebhookSignatureValidator,
    PaymentDataEncryption,
    SecureTokenGenerator,
    get_payment_security_manager
)

__all__ = [
    # Configuration classes
    "PaymentProviderConfig",
    "StripeConfig", 
    "PaystackConfig",
    "BushaConfig",
    "PaymentProviderType",
    "PaymentProviderSettings",
    "get_payment_provider_config",
    
    # Provider management
    "PaymentProviderManager",
    "PaymentProviderSelector",
    "ProviderSwitchingStrategy",
    "GeoLocationBasedSelector", 
    "FallbackProviderManager",
    "get_payment_provider_manager",
    
    # Security utilities
    "PaymentSecurityManager",
    "WebhookSignatureValidator",
    "PaymentDataEncryption",
    "SecureTokenGenerator",
    "get_payment_security_manager",
]
