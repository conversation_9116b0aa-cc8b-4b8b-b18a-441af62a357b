#!/bin/bash
# scripts/update_geoip.sh - Automated MaxMind GeoIP database updates
#
# This script handles automated updates of the MaxMind GeoLite2-Country database
# for the Culture Connect Backend geolocation enhancement system.
#
# Features:
# - Automated weekly database updates via cron
# - Database integrity verification before and after updates
# - Rollback mechanisms for failed updates
# - Health check integration and monitoring
# - Production-grade error handling and notifications

set -e  # Exit on any error
set -u  # Exit on undefined variables

# Script configuration
SCRIPT_NAME="update_geoip"
SCRIPT_VERSION="1.0.0"
LOG_FILE="logs/geoip/update_geoip.log"
DATA_DIR="./data"
BACKUP_DIR="./data/backups"
GEOIP_DATABASE_FILE="GeoLite2-Country.mmdb"
TEMP_DATABASE_FILE="GeoLite2-Country.mmdb.tmp"
DOWNLOAD_URL_BASE="https://download.maxmind.com/app/geoip_download"

# Update configuration
MAX_RETRIES=3
RETRY_DELAY=10
HEALTH_CHECK_URL="http://localhost:8000/api/v1/health/geolocation"
NOTIFICATION_WEBHOOK_URL="${GEOIP_UPDATE_WEBHOOK_URL:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function with enhanced formatting
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Log to file with structured format
    echo "[$timestamp] [$level] [update_geoip] $message" >> "$LOG_FILE"
    
    # Log to console with colors
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        *)
            echo "[$level] $message"
            ;;
    esac
}

# Error handling with rollback capability
handle_error() {
    local exit_code=$?
    local line_number=$1
    
    log "ERROR" "Update script failed at line $line_number with exit code $exit_code"
    
    # Attempt rollback if backup exists
    rollback_database
    
    # Send failure notification
    send_notification "FAILED" "GeoIP database update failed at line $line_number"
    
    log "ERROR" "MaxMind GeoIP database update failed"
    exit $exit_code
}

# Set up error trap
trap 'handle_error $LINENO' ERR

# Banner function
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================================="
    echo "  Culture Connect Backend - MaxMind GeoIP Database Update"
    echo "  Version: $SCRIPT_VERSION"
    echo "  Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================================="
    echo -e "${NC}"
}

# Send notification function
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$NOTIFICATION_WEBHOOK_URL" ]; then
        log "INFO" "Sending notification: $status - $message"
        
        local payload=$(cat <<EOF
{
    "status": "$status",
    "message": "$message",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "service": "geoip_update",
    "version": "$SCRIPT_VERSION"
}
EOF
)
        
        curl -X POST \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$NOTIFICATION_WEBHOOK_URL" \
            --connect-timeout 10 \
            --max-time 30 \
            --silent \
            --fail || log "WARN" "Failed to send notification"
    fi
}

# Check if update is needed
check_update_needed() {
    log "INFO" "Checking if database update is needed..."
    
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    # Check if database exists
    if [ ! -f "$database_path" ]; then
        log "INFO" "Database not found, update needed"
        return 0
    fi
    
    # Check database age (update if older than 7 days)
    local file_age_days=$(( ($(date +%s) - $(stat -f%m "$database_path" 2>/dev/null || stat -c%Y "$database_path" 2>/dev/null)) / 86400 ))
    
    if [ $file_age_days -gt 7 ]; then
        log "INFO" "Database is $file_age_days days old, update needed"
        return 0
    else
        log "INFO" "Database is $file_age_days days old, update not needed"
        return 1
    fi
}

# Validate current database
validate_current_database() {
    log "INFO" "Validating current database..."
    
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    if [ -f "$database_path" ]; then
        # Use Python verification script
        if python3 scripts/verify_geoip.py; then
            log "INFO" "Current database validation successful"
            return 0
        else
            log "WARN" "Current database validation failed"
            return 1
        fi
    else
        log "INFO" "No current database found"
        return 1
    fi
}

# Create backup of current database
create_backup() {
    log "INFO" "Creating backup of current database..."
    
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    
    if [ -f "$database_path" ]; then
        local backup_filename="GeoLite2-Country_update_backup_$(date +%Y%m%d_%H%M%S).mmdb"
        local backup_path="$BACKUP_DIR/$backup_filename"
        
        # Create backup directory if it doesn't exist
        mkdir -p "$BACKUP_DIR"
        
        # Copy database to backup
        cp "$database_path" "$backup_path"
        
        # Verify backup
        if [ -f "$backup_path" ]; then
            log "INFO" "Backup created: $backup_path"
            echo "$backup_path" > "$DATA_DIR/.last_backup"
        else
            log "ERROR" "Failed to create backup"
            exit 1
        fi
    else
        log "INFO" "No current database to backup"
    fi
}

# Download new database
download_new_database() {
    log "INFO" "Downloading new MaxMind GeoLite2-Country database..."
    
    # Validate license key
    if [ -z "${MAXMIND_LICENSE_KEY:-}" ]; then
        log "ERROR" "MAXMIND_LICENSE_KEY environment variable not set"
        exit 1
    fi
    
    local download_url="${DOWNLOAD_URL_BASE}?edition_id=GeoLite2-Country&license_key=${MAXMIND_LICENSE_KEY}&suffix=tar.gz"
    local temp_file="$DATA_DIR/GeoLite2-Country_new.tar.gz"
    local temp_database="$DATA_DIR/$TEMP_DATABASE_FILE"
    
    # Download with retry logic
    local retry_count=0
    
    while [ $retry_count -lt $MAX_RETRIES ]; do
        log "INFO" "Download attempt $((retry_count + 1)) of $MAX_RETRIES..."
        
        if curl -L \
            --connect-timeout 30 \
            --max-time 300 \
            --retry 2 \
            --retry-delay 5 \
            --fail \
            --show-error \
            --silent \
            -o "$temp_file" \
            "$download_url"; then
            log "INFO" "New database downloaded successfully"
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $MAX_RETRIES ]; then
                log "WARN" "Download failed, retrying in $RETRY_DELAY seconds..."
                sleep $RETRY_DELAY
            else
                log "ERROR" "Failed to download new database after $MAX_RETRIES attempts"
                exit 1
            fi
        fi
    done
    
    # Extract new database
    log "INFO" "Extracting new database..."
    cd "$DATA_DIR"
    
    if tar -xzf "GeoLite2-Country_new.tar.gz" --strip-components=1 --wildcards "*/$GEOIP_DATABASE_FILE"; then
        # Move extracted file to temporary name
        mv "$GEOIP_DATABASE_FILE" "$TEMP_DATABASE_FILE"
        log "INFO" "New database extracted successfully"
    else
        log "ERROR" "Failed to extract new database"
        exit 1
    fi
    
    # Clean up download file
    rm -f "GeoLite2-Country_new.tar.gz"
    
    cd - > /dev/null
}

# Verify new database
verify_new_database() {
    log "INFO" "Verifying new database..."
    
    local temp_database="$DATA_DIR/$TEMP_DATABASE_FILE"
    
    # Check if file exists and has content
    if [ ! -f "$temp_database" ] || [ ! -s "$temp_database" ]; then
        log "ERROR" "New database file is missing or empty"
        exit 1
    fi
    
    # Temporarily move new database to expected location for verification
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    local original_database="$DATA_DIR/${GEOIP_DATABASE_FILE}.original"
    
    # Backup original if it exists
    if [ -f "$database_path" ]; then
        mv "$database_path" "$original_database"
    fi
    
    # Move new database for verification
    mv "$temp_database" "$database_path"
    
    # Verify using Python script
    if python3 scripts/verify_geoip.py; then
        log "INFO" "New database verification successful"
        
        # Remove original backup
        rm -f "$original_database"
    else
        log "ERROR" "New database verification failed"
        
        # Restore original database
        if [ -f "$original_database" ]; then
            mv "$original_database" "$database_path"
        fi
        
        exit 1
    fi
}

# Rollback database
rollback_database() {
    log "WARN" "Attempting database rollback..."
    
    local backup_file=""
    if [ -f "$DATA_DIR/.last_backup" ]; then
        backup_file=$(cat "$DATA_DIR/.last_backup")
    fi
    
    if [ -n "$backup_file" ] && [ -f "$backup_file" ]; then
        local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
        
        cp "$backup_file" "$database_path"
        
        if python3 scripts/verify_geoip.py; then
            log "SUCCESS" "Database rollback successful"
        else
            log "ERROR" "Database rollback failed - manual intervention required"
        fi
    else
        log "ERROR" "No backup available for rollback"
    fi
}

# Perform health check
perform_health_check() {
    log "INFO" "Performing health check..."
    
    if command -v curl &> /dev/null; then
        if curl -f -s "$HEALTH_CHECK_URL" > /dev/null; then
            log "INFO" "Health check passed"
            return 0
        else
            log "WARN" "Health check failed"
            return 1
        fi
    else
        log "WARN" "curl not available, skipping health check"
        return 0
    fi
}

# Clean up old backups
cleanup_old_backups() {
    log "INFO" "Cleaning up old backups..."
    
    if [ -d "$BACKUP_DIR" ]; then
        # Keep last 10 backups
        cd "$BACKUP_DIR"
        ls -t GeoLite2-Country_*_backup_*.mmdb 2>/dev/null | tail -n +11 | xargs -r rm -f
        cd - > /dev/null
        
        log "INFO" "Old backup cleanup completed"
    fi
}

# Main execution function
main() {
    print_banner
    
    log "INFO" "Starting MaxMind GeoIP database update..."
    log "INFO" "Script: $SCRIPT_NAME v$SCRIPT_VERSION"
    
    # Check if update is needed
    if ! check_update_needed; then
        log "INFO" "Database update not needed, exiting"
        exit 0
    fi
    
    # Validate current database
    validate_current_database
    
    # Create backup
    create_backup
    
    # Download and verify new database
    download_new_database
    verify_new_database
    
    # Perform health check
    if perform_health_check; then
        log "SUCCESS" "Database update completed successfully!"
        send_notification "SUCCESS" "GeoIP database updated successfully"
    else
        log "WARN" "Health check failed after update"
        send_notification "WARNING" "GeoIP database updated but health check failed"
    fi
    
    # Clean up old backups
    cleanup_old_backups
    
    # Display update info
    local database_path="$DATA_DIR/$GEOIP_DATABASE_FILE"
    if [ -f "$database_path" ]; then
        local file_size=$(stat -f%z "$database_path" 2>/dev/null || stat -c%s "$database_path" 2>/dev/null)
        local file_date=$(stat -f%Sm "$database_path" 2>/dev/null || stat -c%y "$database_path" 2>/dev/null)
        
        echo -e "${GREEN}"
        echo "=================================================================="
        echo "  Update Complete!"
        echo "  Database: $database_path"
        echo "  Size: $file_size bytes"
        echo "  Updated: $file_date"
        echo "=================================================================="
        echo -e "${NC}"
    fi
}

# Execute main function
main "$@"
