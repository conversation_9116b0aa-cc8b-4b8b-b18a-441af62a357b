"""
API endpoint tests for enhanced WebSocket functionality.

This module provides comprehensive API endpoint tests for enhanced WebSocket infrastructure including:
- WebSocket room management endpoints with HTTP response validation
- WebSocket participant management endpoints with authentication testing
- Real-time broadcasting endpoints with performance validation
- Enhanced connection management endpoints with room integration
- Security testing for JWT authentication and RBAC validation

Implements Task 6.1.1 Phase 5 requirements with comprehensive endpoint testing and performance validation.
"""

import pytest
import json
import time
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import FastAPI, status
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.api.v1.endpoints.websocket_endpoints import router
from app.services.websocket_services import (
    WebSocketRoomService, WebSocketRoomParticipantService,
    WebSocketConnectionService
)
from app.schemas.websocket_schemas import (
    WebSocketRoomCreate, WebSocketRoomResponse,
    WebSocketRoomParticipantResponse, WebSocketConnectionResponse
)
from app.models.user import User
from app.core.security import create_access_token


@pytest.mark.api
class TestWebSocketRoomEndpoints:
    """API endpoint tests for WebSocket room management."""

    @pytest.fixture
    def app_with_websocket_routes(self):
        """Create FastAPI app with WebSocket routes for testing."""
        app = FastAPI()
        app.include_router(router, prefix="/api/v1/websocket")
        return app

    @pytest.fixture
    def client(self, app_with_websocket_routes):
        """Create test client for API testing."""
        return TestClient(app_with_websocket_routes)

    @pytest.fixture
    def auth_headers(self, mock_user):
        """Create authentication headers for testing."""
        token = create_access_token(data={"sub": str(mock_user.id)})
        return {"Authorization": f"Bearer {token}"}

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        return user

    @pytest.fixture
    def sample_room_data(self):
        """Sample room creation data."""
        return {
            "room_id": "test_room_123",
            "room_name": "Test Room",
            "room_type": "booking",
            "is_private": True,
            "max_participants": 10,
            "booking_id": 123,
            "owner_id": 1
        }

    @pytest.mark.asyncio
    async def test_create_room_endpoint_performance(
        self,
        client: TestClient,
        auth_headers: dict,
        sample_room_data: dict,
        mock_user: User,
        performance_timer
    ):
        """Test room creation endpoint with performance validation."""
        timer = performance_timer()
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_room_response = WebSocketRoomResponse(
                        id=1,
                        room_id=sample_room_data["room_id"],
                        room_name=sample_room_data["room_name"],
                        room_type=sample_room_data["room_type"],
                        is_private=sample_room_data["is_private"],
                        max_participants=sample_room_data["max_participants"],
                        booking_id=sample_room_data["booking_id"],
                        owner_id=sample_room_data["owner_id"],
                        is_active=True,
                        participant_count=0,
                        total_messages=0,
                        created_at=datetime.now(timezone.utc),
                        last_activity_at=datetime.now(timezone.utc),
                        is_full=False,
                        is_open=True
                    )
                    mock_service.create_room.return_value = mock_room_response
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.post(
                        "/api/v1/websocket/rooms",
                        json=sample_room_data,
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <500ms for room creation
                    timer.assert_under_ms(500, "Room creation endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_201_CREATED
                    response_data = response.json()
                    assert response_data["room_id"] == sample_room_data["room_id"]
                    assert response_data["room_name"] == sample_room_data["room_name"]

    @pytest.mark.asyncio
    async def test_get_room_endpoint_performance(
        self,
        client: TestClient,
        auth_headers: dict,
        sample_room_data: dict,
        mock_user: User,
        performance_timer
    ):
        """Test room retrieval endpoint with performance validation."""
        timer = performance_timer()
        room_id = sample_room_data["room_id"]
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_room_response = WebSocketRoomResponse(
                        id=1,
                        room_id=room_id,
                        room_name=sample_room_data["room_name"],
                        room_type=sample_room_data["room_type"],
                        is_private=sample_room_data["is_private"],
                        max_participants=sample_room_data["max_participants"],
                        booking_id=sample_room_data["booking_id"],
                        owner_id=sample_room_data["owner_id"],
                        is_active=True,
                        participant_count=0,
                        total_messages=0,
                        created_at=datetime.now(timezone.utc),
                        last_activity_at=datetime.now(timezone.utc),
                        is_full=False,
                        is_open=True
                    )
                    mock_service.get_room_by_id.return_value = mock_room_response
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.get(
                        f"/api/v1/websocket/rooms/{room_id}",
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <200ms for room retrieval
                    timer.assert_under_ms(200, "Room retrieval endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_200_OK
                    response_data = response.json()
                    assert response_data["room_id"] == room_id

    @pytest.mark.asyncio
    async def test_close_room_endpoint(
        self,
        client: TestClient,
        auth_headers: dict,
        sample_room_data: dict,
        mock_user: User
    ):
        """Test room closure endpoint."""
        room_id = sample_room_data["room_id"]
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_service.close_room.return_value = True
                    mock_service_class.return_value = mock_service
                    
                    response = client.delete(
                        f"/api/v1/websocket/rooms/{room_id}",
                        headers=auth_headers
                    )
                    
                    # Verify response
                    assert response.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.asyncio
    async def test_room_not_found_error(
        self,
        client: TestClient,
        auth_headers: dict,
        mock_user: User
    ):
        """Test room not found error handling."""
        room_id = "nonexistent_room"
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service returning None
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_service.get_room_by_id.return_value = None
                    mock_service_class.return_value = mock_service
                    
                    response = client.get(
                        f"/api/v1/websocket/rooms/{room_id}",
                        headers=auth_headers
                    )
                    
                    # Verify 404 response
                    assert response.status_code == status.HTTP_404_NOT_FOUND
                    response_data = response.json()
                    assert "not found" in response_data["detail"].lower()

    @pytest.mark.asyncio
    async def test_get_rooms_by_booking_endpoint(
        self,
        client: TestClient,
        auth_headers: dict,
        sample_room_data: dict,
        mock_user: User,
        performance_timer
    ):
        """Test booking rooms retrieval endpoint."""
        timer = performance_timer()
        booking_id = sample_room_data["booking_id"]
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_room_response = WebSocketRoomResponse(
                        id=1,
                        room_id=sample_room_data["room_id"],
                        room_name=sample_room_data["room_name"],
                        room_type=sample_room_data["room_type"],
                        is_private=sample_room_data["is_private"],
                        max_participants=sample_room_data["max_participants"],
                        booking_id=booking_id,
                        owner_id=sample_room_data["owner_id"],
                        is_active=True,
                        participant_count=0,
                        total_messages=0,
                        created_at=datetime.now(timezone.utc),
                        last_activity_at=datetime.now(timezone.utc),
                        is_full=False,
                        is_open=True
                    )
                    mock_service.get_rooms_by_booking.return_value = [mock_room_response]
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.get(
                        f"/api/v1/websocket/rooms/booking/{booking_id}",
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <200ms for booking room queries
                    timer.assert_under_ms(200, "Booking rooms retrieval endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_200_OK
                    response_data = response.json()
                    assert len(response_data) == 1
                    assert response_data[0]["booking_id"] == booking_id


@pytest.mark.api
class TestWebSocketParticipantEndpoints:
    """API endpoint tests for WebSocket participant management."""

    @pytest.fixture
    def app_with_websocket_routes(self):
        """Create FastAPI app with WebSocket routes for testing."""
        app = FastAPI()
        app.include_router(router, prefix="/api/v1/websocket")
        return app

    @pytest.fixture
    def client(self, app_with_websocket_routes):
        """Create test client for API testing."""
        return TestClient(app_with_websocket_routes)

    @pytest.fixture
    def auth_headers(self, mock_user):
        """Create authentication headers for testing."""
        token = create_access_token(data={"sub": str(mock_user.id)})
        return {"Authorization": f"Bearer {token}"}

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_add_participant_endpoint_performance(
        self,
        client: TestClient,
        auth_headers: dict,
        mock_user: User,
        performance_timer
    ):
        """Test participant addition endpoint with performance validation."""
        timer = performance_timer()
        room_id = 1
        participant_data = {
            "user_id": 123,
            "role": "participant"
        }
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomParticipantService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_participant_response = WebSocketRoomParticipantResponse(
                        id=1,
                        room_id=room_id,
                        user_id=participant_data["user_id"],
                        role=participant_data["role"],
                        is_active=True,
                        joined_at=datetime.now(timezone.utc),
                        last_seen_at=datetime.now(timezone.utc)
                    )
                    mock_service.add_participant.return_value = mock_participant_response
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.post(
                        f"/api/v1/websocket/rooms/{room_id}/participants",
                        json=participant_data,
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <500ms for participant addition
                    timer.assert_under_ms(500, "Participant addition endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_201_CREATED
                    response_data = response.json()
                    assert response_data["user_id"] == participant_data["user_id"]
                    assert response_data["role"] == participant_data["role"]

    @pytest.mark.asyncio
    async def test_list_participants_endpoint_performance(
        self,
        client: TestClient,
        auth_headers: dict,
        mock_user: User,
        performance_timer
    ):
        """Test participant listing endpoint with performance validation."""
        timer = performance_timer()
        room_id = 1
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomParticipantService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_participants = [
                        WebSocketRoomParticipantResponse(
                            id=1,
                            room_id=room_id,
                            user_id=123,
                            role="participant",
                            is_active=True,
                            joined_at=datetime.now(timezone.utc),
                            last_seen_at=datetime.now(timezone.utc)
                        ),
                        WebSocketRoomParticipantResponse(
                            id=2,
                            room_id=room_id,
                            user_id=456,
                            role="moderator",
                            is_active=True,
                            joined_at=datetime.now(timezone.utc),
                            last_seen_at=datetime.now(timezone.utc)
                        )
                    ]
                    mock_service.get_room_participants.return_value = mock_participants
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.get(
                        f"/api/v1/websocket/rooms/{room_id}/participants",
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <200ms for participant queries
                    timer.assert_under_ms(200, "Participant listing endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_200_OK
                    response_data = response.json()
                    assert len(response_data) == 2
                    assert response_data[0]["user_id"] == 123
                    assert response_data[1]["user_id"] == 456

    @pytest.mark.asyncio
    async def test_bulk_remove_participants_endpoint(
        self,
        client: TestClient,
        auth_headers: dict,
        mock_user: User,
        performance_timer
    ):
        """Test bulk participant removal endpoint."""
        timer = performance_timer()
        room_id = 1
        user_ids = [123, 456, 789]
        
        # Mock authentication
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_user
            
            # Mock database session
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Mock service
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketRoomParticipantService') as mock_service_class:
                    mock_service = AsyncMock()
                    mock_service.bulk_remove_participants.return_value = 3
                    mock_service_class.return_value = mock_service
                    
                    timer.start()
                    response = client.delete(
                        f"/api/v1/websocket/rooms/{room_id}/participants/bulk",
                        params={"user_ids": user_ids},
                        headers=auth_headers
                    )
                    timer.stop()
                    
                    # Performance validation: <500ms for bulk operations
                    timer.assert_under_ms(500, "Bulk participant removal endpoint")
                    
                    # Verify response
                    assert response.status_code == status.HTTP_200_OK
                    response_data = response.json()
                    assert response_data["removed_count"] == 3
                    assert response_data["total_requested"] == 3
                    assert response_data["room_id"] == room_id
