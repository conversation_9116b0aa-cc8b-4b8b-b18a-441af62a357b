"""
Unit tests for enhanced workflow schemas (Task 6.2.2 Phase 3).

Tests for advanced scheduling system schemas including:
- Enhanced JobSchedule schemas with validation
- Advanced scheduling configuration validation
- Cron expression validation
- Timezone validation
- Schedule conflict checking schemas
- Performance metrics schemas
"""

import pytest
from datetime import datetime, timezone, timedelta
from uuid import uuid4

from pydantic import ValidationError

from app.schemas.workflow_schemas import (
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse,
    ScheduleConflictCheck, ScheduleConflictResult,
    ScheduleCalculation, ScheduleCalculationResult,
    SchedulePerformanceMetrics, BulkScheduleOperation, BulkScheduleResult
)
from app.models.workflow_models import (
    ScheduleType, NamedSchedule, HolidayCalendar
)


class TestJobScheduleCreateSchema:
    """Test JobScheduleCreate schema validation."""

    def test_valid_cron_schedule_creation(self):
        """Test creating valid cron schedule."""
        data = {
            "name": "test-cron-schedule",
            "description": "Test cron schedule",
            "cron_expression": "0 0 * * *",
            "timezone": "UTC",
            "schedule_type": ScheduleType.CRON,
            "priority": 5
        }

        schema = JobScheduleCreate(**data)

        assert schema.name == "test-cron-schedule"
        assert schema.cron_expression == "0 0 * * *"
        assert schema.schedule_type == ScheduleType.CRON
        assert schema.timezone == "UTC"
        assert schema.priority == 5

    def test_valid_named_schedule_creation(self):
        """Test creating valid named schedule."""
        data = {
            "name": "test-named-schedule",
            "description": "Test named schedule",
            "named_schedule": NamedSchedule.DAILY,
            "timezone": "America/New_York",
            "schedule_type": ScheduleType.NAMED,
            "business_days_only": True,
            "exclude_holidays": True,
            "holiday_calendar": HolidayCalendar.US
        }

        schema = JobScheduleCreate(**data)

        assert schema.named_schedule == NamedSchedule.DAILY
        assert schema.schedule_type == ScheduleType.NAMED
        assert schema.business_days_only is True
        assert schema.exclude_holidays is True
        assert schema.holiday_calendar == HolidayCalendar.US

    def test_valid_interval_schedule_creation(self):
        """Test creating valid interval schedule."""
        data = {
            "name": "test-interval-schedule",
            "description": "Test interval schedule",
            "interval_seconds": 3600,
            "timezone": "UTC",
            "schedule_type": ScheduleType.INTERVAL,
            "allow_overlap": True,
            "max_concurrent_runs": 3
        }

        schema = JobScheduleCreate(**data)

        assert schema.interval_seconds == 3600
        assert schema.schedule_type == ScheduleType.INTERVAL
        assert schema.allow_overlap is True
        assert schema.max_concurrent_runs == 3

    def test_cron_expression_validation(self):
        """Test cron expression validation."""
        # Valid 5-part cron expression
        data = {
            "name": "test-cron-5-parts",
            "cron_expression": "0 0 * * *",
            "schedule_type": ScheduleType.CRON
        }
        schema = JobScheduleCreate(**data)
        assert schema.cron_expression == "0 0 * * *"

        # Valid 6-part cron expression (with seconds)
        data = {
            "name": "test-cron-6-parts",
            "cron_expression": "0 0 0 * * *",
            "schedule_type": ScheduleType.CRON
        }
        schema = JobScheduleCreate(**data)
        assert schema.cron_expression == "0 0 0 * * *"

        # Invalid cron expression (too few parts)
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-invalid-cron",
                cron_expression="0 0",
                schedule_type=ScheduleType.CRON
            )
        assert "String should have at least 9 characters" in str(exc_info.value)

    def test_timezone_validation(self):
        """Test timezone validation."""
        # Valid timezone
        data = {
            "name": "test-timezone",
            "cron_expression": "0 0 * * *",
            "timezone": "America/New_York",
            "schedule_type": ScheduleType.CRON
        }
        schema = JobScheduleCreate(**data)
        assert schema.timezone == "America/New_York"

        # Invalid timezone
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-invalid-timezone",
                cron_expression="0 0 * * *",
                timezone="Invalid/Timezone",
                schedule_type=ScheduleType.CRON
            )
        assert "Invalid timezone" in str(exc_info.value)

    def test_priority_validation(self):
        """Test priority validation."""
        # Valid priority
        data = {
            "name": "test-priority",
            "cron_expression": "0 0 * * *",
            "schedule_type": ScheduleType.CRON,
            "priority": 1
        }
        schema = JobScheduleCreate(**data)
        assert schema.priority == 1

        # Invalid priority (too low)
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-invalid-priority",
                cron_expression="0 0 * * *",
                schedule_type=ScheduleType.CRON,
                priority=0
            )
        assert "Input should be greater than or equal to 1" in str(exc_info.value)

    def test_schedule_configuration_validation(self):
        """Test schedule configuration validation based on type."""
        # Cron type requires cron expression or named schedule
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-cron-missing",
                schedule_type=ScheduleType.CRON
            )
        assert "Cron expression or named schedule required" in str(exc_info.value)

        # Interval type requires interval seconds
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-interval-missing",
                schedule_type=ScheduleType.INTERVAL
            )
        assert "Interval seconds required" in str(exc_info.value)

        # Named type requires named schedule
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-named-missing",
                schedule_type=ScheduleType.NAMED
            )
        assert "Named schedule required" in str(exc_info.value)

    def test_holiday_calendar_validation(self):
        """Test holiday calendar validation."""
        # Exclude holidays requires holiday calendar
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-holidays-missing-calendar",
                cron_expression="0 0 * * *",
                schedule_type=ScheduleType.CRON,
                exclude_holidays=True
            )
        assert "Holiday calendar required" in str(exc_info.value)

    def test_date_constraints_validation(self):
        """Test date constraints validation."""
        now = datetime.now(timezone.utc)

        # Valid date range
        data = {
            "name": "test-valid-dates",
            "cron_expression": "0 0 * * *",
            "schedule_type": ScheduleType.CRON,
            "start_date": now,
            "end_date": now + timedelta(days=30)
        }
        schema = JobScheduleCreate(**data)
        assert schema.start_date < schema.end_date

        # Invalid date range
        with pytest.raises(ValidationError) as exc_info:
            JobScheduleCreate(
                name="test-invalid-dates",
                cron_expression="0 0 * * *",
                schedule_type=ScheduleType.CRON,
                start_date=now,
                end_date=now - timedelta(days=1)
            )
        assert "End date must be after start date" in str(exc_info.value)


class TestJobScheduleUpdateSchema:
    """Test JobScheduleUpdate schema validation."""

    def test_partial_update(self):
        """Test partial schedule update."""
        data = {
            "description": "Updated description",
            "priority": 8,
            "is_active": False
        }

        schema = JobScheduleUpdate(**data)

        assert schema.description == "Updated description"
        assert schema.priority == 8
        assert schema.is_active is False
        assert schema.name is None  # Not updated


class TestJobScheduleResponseSchema:
    """Test JobScheduleResponse schema."""

    def test_complete_response_schema(self):
        """Test complete schedule response schema."""
        now = datetime.now(timezone.utc)

        data = {
            "id": uuid4(),
            "workflow_definition_id": uuid4(),
            "name": "test-schedule",
            "description": "Test schedule",
            "cron_expression": "0 0 * * *",
            "named_schedule": None,
            "timezone": "UTC",
            "interval_seconds": None,
            "schedule_type": ScheduleType.CRON,
            "business_days_only": False,
            "exclude_holidays": False,
            "holiday_calendar": None,
            "start_date": None,
            "end_date": None,
            "max_runs": None,
            "is_active": True,
            "last_run_at": now,
            "next_run_at": now + timedelta(days=1),
            "run_count": 5,
            "success_count": 4,
            "failure_count": 1,
            "last_success_at": now,
            "last_failure_at": now - timedelta(hours=1),
            "priority": 5,
            "allow_overlap": False,
            "max_concurrent_runs": 1,
            "catchup_missed_runs": False,
            "max_catchup_runs": 3,
            "average_duration_seconds": 125.5,
            "last_duration_seconds": 130.2,
            "schedule_metadata": {"key": "value"},
            "created_at": now,
            "updated_at": now,
            "created_by": uuid4(),
            "updated_by": uuid4()
        }

        schema = JobScheduleResponse(**data)

        assert schema.name == "test-schedule"
        assert schema.schedule_type == ScheduleType.CRON
        assert schema.run_count == 5
        assert schema.success_count == 4
        assert schema.failure_count == 1


class TestAdvancedSchedulingSchemas:
    """Test advanced scheduling schemas."""

    def test_schedule_conflict_check_schema(self):
        """Test schedule conflict check schema."""
        data = {
            "cron_expression": "0 0 * * *",
            "timezone": "UTC",
            "max_concurrent_runs": 1
        }

        schema = ScheduleConflictCheck(**data)

        assert schema.cron_expression == "0 0 * * *"
        assert schema.timezone == "UTC"
        assert schema.max_concurrent_runs == 1

    def test_schedule_conflict_result_schema(self):
        """Test schedule conflict result schema."""
        data = {
            "has_conflicts": True,
            "conflicting_schedules": [uuid4(), uuid4()],
            "conflict_details": [
                {"schedule_id": str(uuid4()), "conflict_type": "overlap"}
            ],
            "recommendations": ["Adjust schedule timing", "Increase max concurrent runs"]
        }

        schema = ScheduleConflictResult(**data)

        assert schema.has_conflicts is True
        assert len(schema.conflicting_schedules) == 2
        assert len(schema.recommendations) == 2

    def test_schedule_calculation_schema(self):
        """Test schedule calculation schema."""
        data = {
            "cron_expression": "0 0 * * *",
            "timezone": "UTC",
            "business_days_only": True,
            "exclude_holidays": True,
            "holiday_calendar": HolidayCalendar.US,
            "max_results": 5
        }

        schema = ScheduleCalculation(**data)

        assert schema.cron_expression == "0 0 * * *"
        assert schema.business_days_only is True
        assert schema.holiday_calendar == HolidayCalendar.US
        assert schema.max_results == 5

    def test_schedule_calculation_result_schema(self):
        """Test schedule calculation result schema."""
        now = datetime.now(timezone.utc)

        data = {
            "next_run_times": [
                now + timedelta(days=1),
                now + timedelta(days=2),
                now + timedelta(days=3)
            ],
            "calculation_time_ms": 25.5,
            "is_valid": True,
            "validation_errors": [],
            "schedule_summary": {
                "frequency": "daily",
                "next_execution": "tomorrow at midnight"
            }
        }

        schema = ScheduleCalculationResult(**data)

        assert len(schema.next_run_times) == 3
        assert schema.calculation_time_ms == 25.5
        assert schema.is_valid is True

    def test_schedule_performance_metrics_schema(self):
        """Test schedule performance metrics schema."""
        data = {
            "schedule_id": uuid4(),
            "total_runs": 100,
            "successful_runs": 95,
            "failed_runs": 5,
            "average_duration_seconds": 125.5,
            "min_duration_seconds": 90.0,
            "max_duration_seconds": 200.0,
            "success_rate": 0.95,
            "last_run_at": datetime.now(timezone.utc),
            "next_run_at": datetime.now(timezone.utc) + timedelta(days=1),
            "performance_trend": "stable",
            "reliability_score": 0.95
        }

        schema = SchedulePerformanceMetrics(**data)

        assert schema.total_runs == 100
        assert schema.success_rate == 0.95
        assert schema.performance_trend == "stable"
        assert schema.reliability_score == 0.95

    def test_bulk_schedule_operation_schema(self):
        """Test bulk schedule operation schema."""
        data = {
            "operation": "enable",
            "schedule_ids": [uuid4(), uuid4(), uuid4()],
            "force": False
        }

        schema = BulkScheduleOperation(**data)

        assert schema.operation == "enable"
        assert len(schema.schedule_ids) == 3
        assert schema.force is False

    def test_bulk_schedule_result_schema(self):
        """Test bulk schedule result schema."""
        data = {
            "operation": "enable",
            "total_schedules": 3,
            "successful_operations": 2,
            "failed_operations": 1,
            "success_details": [
                {"schedule_id": str(uuid4()), "status": "enabled"}
            ],
            "failure_details": [
                {"schedule_id": str(uuid4()), "error": "Schedule not found"}
            ],
            "warnings": ["One schedule had conflicts"]
        }

        schema = BulkScheduleResult(**data)

        assert schema.operation == "enable"
        assert schema.total_schedules == 3
        assert schema.successful_operations == 2
        assert schema.failed_operations == 1
