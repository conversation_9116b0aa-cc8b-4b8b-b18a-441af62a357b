"""
User Profile Management endpoints for Culture Connect Backend API.

This module provides comprehensive user profile and account management endpoints:
- User profile CRUD operations with validation
- Profile image upload and optimization
- User preferences management
- Account settings configuration
- Privacy controls implementation

Implements Task 2.2.2 requirements for complete user profile management
with production-grade error handling and security features.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.user_profile_service import UserProfileService
from app.schemas.auth import UserResponse, MessageResponse
from app.schemas.user_profile import (
    UserProfileUpdate, UserPreferences, UserPreferencesUpdate,
    ProfileImageResponse, AccountSettings, AccountSettingsUpdate
)
from app.api.v1.auth import get_current_user
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.get(
    "/me",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Get current user profile",
    description="Retrieve complete user profile information for authenticated user"
)
async def get_current_user_profile(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Get current authenticated user profile.

    Args:
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        UserResponse: Complete user profile information

    Raises:
        HTTPException: If user not found or access denied
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Get user profile
        user_profile = await profile_service.get_user_profile(current_user["id"])

        logger.info(
            f"User profile retrieved: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"]
            }
        )

        return user_profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Get user profile failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


@router.put(
    "/me",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Update user profile",
    description="Update user profile information for authenticated user"
)
async def update_current_user_profile(
    profile_data: UserProfileUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserResponse:
    """
    Update current authenticated user profile.

    Args:
        profile_data: Profile update data
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        UserResponse: Updated user profile information

    Raises:
        HTTPException: If user not found or validation fails
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Update user profile
        updated_profile = await profile_service.update_user_profile(
            current_user["id"], profile_data
        )

        logger.info(
            f"User profile updated: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "updated_fields": list(profile_data.dict(exclude_unset=True).keys())
            }
        )

        return updated_profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Update user profile failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.post(
    "/me/upload-avatar",
    response_model=ProfileImageResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Upload profile image",
    description="Upload and process user profile image with thumbnail generation"
)
async def upload_avatar(
    file: UploadFile = File(..., description="Profile image file"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ProfileImageResponse:
    """
    Upload and process user profile image.

    Args:
        file: Uploaded image file
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        ProfileImageResponse: Uploaded image information with URLs

    Raises:
        HTTPException: If upload fails or validation errors
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Upload and process profile image
        image_response = await profile_service.upload_profile_image(
            current_user["id"], file
        )

        logger.info(
            f"Profile image uploaded: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "filename": file.filename,
                "file_size": file.size
            }
        )

        return image_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Profile image upload failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "filename": file.filename,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload profile image"
        )


@router.get(
    "/me/preferences",
    response_model=UserPreferences,
    status_code=status.HTTP_200_OK,
    summary="Get user preferences",
    description="Retrieve user preferences and notification settings"
)
async def get_user_preferences(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserPreferences:
    """
    Get user preferences and notification settings.

    Args:
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        UserPreferences: User preferences and notification settings

    Raises:
        HTTPException: If user not found or access denied
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Get user preferences
        preferences = await profile_service.get_user_preferences(current_user["id"])

        logger.info(
            f"User preferences retrieved: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"]
            }
        )

        return preferences

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Get user preferences failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user preferences"
        )


@router.put(
    "/me/preferences",
    response_model=UserPreferences,
    status_code=status.HTTP_200_OK,
    summary="Update user preferences",
    description="Update user preferences and notification settings"
)
async def update_user_preferences(
    preferences_data: UserPreferencesUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserPreferences:
    """
    Update user preferences and notification settings.

    Args:
        preferences_data: Preferences update data
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        UserPreferences: Updated user preferences

    Raises:
        HTTPException: If user not found or validation fails
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Update user preferences
        updated_preferences = await profile_service.update_user_preferences(
            current_user["id"], preferences_data
        )

        logger.info(
            f"User preferences updated: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "updated_preferences": list(preferences_data.dict(exclude_unset=True).keys())
            }
        )

        return updated_preferences

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Update user preferences failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user preferences"
        )


@router.get(
    "/me/settings",
    response_model=AccountSettings,
    status_code=status.HTTP_200_OK,
    summary="Get account settings",
    description="Retrieve user account settings and security preferences"
)
async def get_account_settings(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountSettings:
    """
    Get user account settings and security preferences.

    Args:
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        AccountSettings: User account settings and security preferences

    Raises:
        HTTPException: If user not found or access denied
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Get account settings
        settings_data = await profile_service.get_account_settings(current_user["id"])

        logger.info(
            f"Account settings retrieved: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"]
            }
        )

        return settings_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Get account settings failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve account settings"
        )


@router.put(
    "/me/settings",
    response_model=AccountSettings,
    status_code=status.HTTP_200_OK,
    summary="Update account settings",
    description="Update user account settings and security preferences"
)
async def update_account_settings(
    settings_data: AccountSettingsUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountSettings:
    """
    Update user account settings and security preferences.

    Args:
        settings_data: Account settings update data
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        AccountSettings: Updated account settings

    Raises:
        HTTPException: If user not found or validation fails
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Update account settings
        updated_settings = await profile_service.update_account_settings(
            current_user["id"], settings_data
        )

        logger.info(
            f"Account settings updated: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "updated_settings": list(settings_data.dict(exclude_unset=True).keys())
            }
        )

        return updated_settings

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Update account settings failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update account settings"
        )


@router.delete(
    "/me",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Deactivate user account",
    description="Soft delete user account (deactivate)"
)
async def deactivate_account(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Deactivate user account (soft delete).

    Args:
        current_user: Current authenticated user from JWT token
        db: Database session dependency

    Returns:
        MessageResponse: Account deactivation confirmation

    Raises:
        HTTPException: If user not found or deactivation fails
    """
    try:
        # Initialize user profile service
        profile_service = UserProfileService(db)

        # Deactivate user account
        result = await profile_service.delete_user_profile(current_user["id"])

        logger.info(
            f"User account deactivated: {current_user['email']}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"]
            }
        )

        return MessageResponse(message=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Account deactivation failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate account"
        )
