"""
WebSocket Infrastructure services for Culture Connect Backend API.

This module provides comprehensive WebSocket business logic orchestration including:
- WebSocketConnectionService: Connection lifecycle management with authentication
- WebSocketEventService: Event creation, delivery management, and content moderation
- UserPresenceService: Real-time status management and activity tracking
- WebSocketMetricsService: Analytics aggregation and performance monitoring

Implements Task 6.1.1 Phase 3 requirements with production-grade business logic following
established BaseService patterns with RBAC integration, transaction management, and
comprehensive error handling.

Performance targets: <500ms creation operations, <200ms queries, <100ms status updates.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.websocket_repositories import (
    WebSocketConnectionRepository, WebSocketEventRepository,
    UserPresenceRepository, WebSocketMetricsRepository,
    WebSocketRoomRepository, WebSocketRoomParticipantRepository
)
from app.repositories.auth_repository import UserRepository
from app.models.websocket_models import (
    WebSocketConnection, WebSocketEvent, UserPresence, WebSocketConnectionMetrics,
    WebSocketRoom, WebSocketRoomParticipant,
    ConnectionStatus, EventType, EventPriority
)
from app.models.user import User
from app.schemas.websocket_schemas import (
    WebSocketConnectionCreate, WebSocketConnectionResponse,
    WebSocketEventCreate, WebSocketEventResponse,
    UserPresenceResponse,
    WebSocketRoomCreate, WebSocketRoomResponse,
    WebSocketRoomParticipantCreate, WebSocketRoomParticipantResponse,
    ConversationEventData, RealTimeNotificationData
)
from app.repositories.base import PaginationParams, QueryResult
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.services.rbac_service import RBACService
from app.core.cache import CacheManager, get_cache_manager
from app.core.security import UserRole, Permission, has_permission
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.config import settings

logger = logging.getLogger(__name__)


class WebSocketConnectionService(BaseService[WebSocketConnection, WebSocketConnectionRepository]):
    """
    WebSocket connection service for connection lifecycle management.

    Provides comprehensive connection management including:
    - Connection creation with authentication validation
    - Session management with user association
    - Connection status tracking and updates
    - Performance monitoring and metrics collection
    - RBAC integration for connection permissions
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        """Initialize WebSocket connection service with dependencies."""
        super().__init__(
            repository_class=WebSocketConnectionRepository,
            model_class=WebSocketConnection,
            db_session=db
        )

        # Initialize dependent services
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)
        self.rbac_service = RBACService(db)

        # Initialize repositories
        self.user_repository = UserRepository(db)
        self.cache_manager = cache_manager

        # Initialize repository with cache
        self._repository_instance = None

    async def _get_repository(self) -> WebSocketConnectionRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = WebSocketConnectionRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate WebSocket connection creation data.

        Args:
            data: Connection creation data

        Returns:
            Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['connection_id', 'user_id']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError(f"Missing required field: {field}")

        # Validate user exists
        user = await self.user_repository.get(data['user_id'])
        if not user:
            raise ValidationError(f"User {data['user_id']} not found")

        # Validate connection_id uniqueness
        repository = await self._get_repository()
        existing_connection = await repository.get_by_connection_id(data['connection_id'])
        if existing_connection and existing_connection.status in [
            ConnectionStatus.CONNECTED, ConnectionStatus.AUTHENTICATED
        ]:
            raise ConflictError(f"Connection {data['connection_id']} already active")

        # Set default values
        data.setdefault('status', ConnectionStatus.CONNECTING)
        data.setdefault('connected_at', datetime.now(timezone.utc))
        data.setdefault('last_activity_at', datetime.now(timezone.utc))
        data.setdefault('message_count', 0)
        data.setdefault('bytes_sent', 0)
        data.setdefault('bytes_received', 0)
        data.setdefault('error_count', 0)

        return data

    async def create_connection(
        self,
        connection_data: WebSocketConnectionCreate,
        current_user: Optional[User] = None
    ) -> WebSocketConnectionResponse:
        """
        Create a new WebSocket connection with validation and authentication.

        Performance target: <500ms for connection creation.

        Args:
            connection_data: Connection creation data
            current_user: Current authenticated user (for permission checking)

        Returns:
            Created WebSocket connection

        Raises:
            ValidationError: If validation fails
            ConflictError: If connection already exists
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start("create_connection",
                                             connection_id=connection_data.connection_id)

        try:
            # Check permissions if current_user provided
            if current_user:
                # Users can only create connections for themselves unless admin
                if (connection_data.user_id != current_user.id and
                    not has_permission(current_user.role, Permission.MANAGE_SYSTEM)):
                    raise ValidationError("Cannot create connection for another user")

            # Prepare connection data
            connection_dict = connection_data.model_dump()

            # Create connection
            async with self.get_transaction_context() as session:
                repository = WebSocketConnectionRepository(session, self.cache_manager)
                connection = await repository.create_connection(connection_dict)

                # Send connection established notification
                await self._send_connection_notifications(connection, "established")

                self.log_operation_success(
                    correlation,
                    f"Created WebSocket connection {connection.connection_id}"
                )

                return WebSocketConnectionResponse.model_validate(connection)

        except Exception as e:
            await self.handle_service_error(e, "create_connection",
                                          {"connection_id": connection_data.connection_id})

    async def authenticate_connection(
        self,
        connection_id: str,
        auth_token: str,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Authenticate a WebSocket connection.

        Performance target: <200ms for authentication.

        Args:
            connection_id: Connection identifier
            auth_token: Authentication token
            current_user: Current authenticated user

        Returns:
            True if authentication successful

        Raises:
            NotFoundError: If connection not found
            ValidationError: If authentication fails
        """
        correlation = self.log_operation_start("authenticate_connection",
                                             connection_id=connection_id)

        try:
            repository = await self._get_repository()

            # Get connection
            connection = await repository.get_by_connection_id(connection_id)
            if not connection:
                raise NotFoundError("WebSocket connection", connection_id)

            # Validate authentication token (simplified - in production, verify JWT)
            if not auth_token or len(auth_token) < 10:
                raise ValidationError("Invalid authentication token")

            # Update connection status
            success = await repository.update_connection_status(
                connection_id=connection_id,
                status=ConnectionStatus.AUTHENTICATED
            )

            if success:
                # Send authentication success notification
                await self._send_connection_notifications(connection, "authenticated")

                self.log_operation_success(
                    correlation,
                    f"Authenticated WebSocket connection {connection_id}"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "authenticate_connection",
                                          {"connection_id": connection_id})

    async def update_connection_activity(
        self,
        connection_id: str,
        activity_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update connection activity timestamp and metrics.

        Performance target: <100ms for activity updates.

        Args:
            connection_id: Connection identifier
            activity_data: Optional activity data (message count, bytes, etc.)

        Returns:
            True if update successful
        """
        correlation = self.log_operation_start("update_connection_activity",
                                             connection_id=connection_id)

        try:
            repository = await self._get_repository()

            # Get connection
            connection = await repository.get_by_connection_id(connection_id)
            if not connection:
                return False

            # Update activity timestamp
            success = await repository.update_connection_status(
                connection_id=connection_id,
                status=connection.status  # Keep current status
            )

            # Update metrics if provided
            if activity_data and success:
                # This would update message counts, bytes transferred, etc.
                # Implementation depends on specific metrics tracking needs
                pass

            if success:
                self.log_operation_success(
                    correlation,
                    f"Updated activity for connection {connection_id}"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "update_connection_activity",
                                          {"connection_id": connection_id})

    async def disconnect_connection(
        self,
        connection_id: str,
        reason: Optional[str] = None
    ) -> bool:
        """
        Disconnect a WebSocket connection.

        Performance target: <200ms for disconnection.

        Args:
            connection_id: Connection identifier
            reason: Optional disconnection reason

        Returns:
            True if disconnection successful
        """
        correlation = self.log_operation_start("disconnect_connection",
                                             connection_id=connection_id)

        try:
            repository = await self._get_repository()

            # Get connection
            connection = await repository.get_by_connection_id(connection_id)
            if not connection:
                return False

            # Update connection status to disconnected
            success = await repository.update_connection_status(
                connection_id=connection_id,
                status=ConnectionStatus.DISCONNECTED,
                error_message=reason
            )

            if success:
                # Send disconnection notification
                await self._send_connection_notifications(connection, "disconnected", reason)

                self.log_operation_success(
                    correlation,
                    f"Disconnected WebSocket connection {connection_id}"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "disconnect_connection",
                                          {"connection_id": connection_id})

    async def get_user_connections(
        self,
        user_id: int,
        current_user: Optional[User] = None
    ) -> List[WebSocketConnectionResponse]:
        """
        Get active connections for a user.

        Performance target: <200ms for connection retrieval.

        Args:
            user_id: User ID
            current_user: Current authenticated user (for permission checking)

        Returns:
            List of active WebSocket connections

        Raises:
            ValidationError: If permission denied
        """
        correlation = self.log_operation_start("get_user_connections", user_id=user_id)

        try:
            # Check permissions
            if current_user:
                if (user_id != current_user.id and
                    not has_permission(current_user.role, Permission.VIEW_LOGS)):
                    raise ValidationError("Cannot view connections for another user")

            repository = await self._get_repository()
            connections = await repository.get_active_connections_by_user(user_id)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(connections)} connections for user {user_id}"
            )

            return [WebSocketConnectionResponse.model_validate(conn) for conn in connections]

        except Exception as e:
            await self.handle_service_error(e, "get_user_connections", {"user_id": user_id})

    async def _send_connection_notifications(
        self,
        connection: WebSocketConnection,
        event_type: str,
        reason: Optional[str] = None
    ) -> None:
        """Send notifications for connection events."""
        try:
            # Get user for notifications
            user = await self.user_repository.get(connection.user_id)
            if not user:
                return

            # Send email notification for important events
            if event_type in ["established", "disconnected"]:
                await self.email_service.send_system_notification(
                    user_id=user.id,
                    subject=f"WebSocket Connection {event_type.title()}",
                    template_data={
                        "connection_id": connection.connection_id,
                        "event_type": event_type,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "reason": reason
                    }
                )

            # Send push notification for connection issues
            if event_type == "disconnected" and reason:
                await self.push_service.send_system_notification(
                    user_id=user.id,
                    title="Connection Disconnected",
                    body=f"Your WebSocket connection was disconnected: {reason}",
                    data={
                        "connection_id": connection.connection_id,
                        "event_type": event_type,
                        "reason": reason
                    }
                )

        except Exception as e:
            logger.warning(f"Failed to send connection notifications: {str(e)}")

    async def cleanup_stale_connections(self, timeout_minutes: int = 30) -> int:
        """
        Clean up stale connections (admin operation).

        Args:
            timeout_minutes: Minutes of inactivity before considering stale

        Returns:
            Number of connections cleaned up
        """
        correlation = self.log_operation_start("cleanup_stale_connections",
                                             timeout_minutes=timeout_minutes)

        try:
            repository = await self._get_repository()
            cleaned_count = await repository.cleanup_stale_connections(timeout_minutes)

            self.log_operation_success(
                correlation,
                f"Cleaned up {cleaned_count} stale connections"
            )

            return cleaned_count

        except Exception as e:
            await self.handle_service_error(e, "cleanup_stale_connections",
                                          {"timeout_minutes": timeout_minutes})

    # ============================================================================
    # Enhanced Real-time Communication Methods for Task 6.1.1 Phase 3
    # ============================================================================

    async def get_connections_in_room(
        self,
        room_id: int,
        current_user: Optional[User] = None
    ) -> List[WebSocketConnectionResponse]:
        """
        Get all active WebSocket connections for participants in a room.

        Performance Metrics:
        - Target response time: <150ms for room connection queries
        - Cache integration for frequently accessed rooms
        - Database query optimization with room participant joins

        Args:
            room_id: Room ID to get connections for
            current_user: Current authenticated user

        Returns:
            List of active WebSocket connection responses in the room

        Raises:
            ValidationError: If user lacks access to room
            ServiceError: If retrieval operation fails
        """
        correlation = self.log_operation_start("get_connections_in_room", room_id=room_id)

        try:
            # Validate room access permissions
            if current_user:
                await self._validate_room_access_permissions(current_user, room_id)

            repository = await self._get_repository()
            connections = await repository.get_connections_in_room(room_id)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(connections)} connections for room {room_id}"
            )

            return [WebSocketConnectionResponse.model_validate(conn) for conn in connections]

        except Exception as e:
            await self.handle_service_error(e, "get_connections_in_room", {"room_id": room_id})

    async def broadcast_to_room(
        self,
        room_id: int,
        event_data: Dict[str, Any],
        current_user: Optional[User] = None
    ) -> int:
        """
        Prepare broadcast to all participants in a room.

        Performance Metrics:
        - Target response time: <100ms for broadcast preparation
        - Optimized query for connection ID retrieval
        - Cache integration for active room connections

        Args:
            room_id: Room ID to broadcast to
            event_data: Event data for broadcasting
            current_user: Current authenticated user

        Returns:
            Number of connections that can receive the broadcast

        Raises:
            ValidationError: If user lacks broadcast permissions
            ServiceError: If broadcast preparation fails

        Note:
            This method prepares the connection list for broadcasting.
            Actual WebSocket message delivery is handled by the WebSocket infrastructure.
        """
        correlation = self.log_operation_start("broadcast_to_room",
                                             room_id=room_id,
                                             event_type=event_data.get("type", "unknown"))

        try:
            # Validate broadcast permissions
            if current_user:
                await self._validate_room_broadcast_permissions(current_user, room_id)

            repository = await self._get_repository()
            connection_count = await repository.broadcast_to_room_connections(room_id, event_data)

            # Create WebSocket event for the broadcast
            event_service = WebSocketEventService(self.db, self.cache_manager)
            await event_service.create_event(
                WebSocketEventCreate(
                    event_type="room_broadcast",
                    payload=event_data,
                    priority="standard",
                    target_room_id=room_id
                ),
                current_user
            )

            self.log_operation_success(
                correlation,
                f"Prepared broadcast to {connection_count} connections in room {room_id}"
            )

            return connection_count

        except Exception as e:
            await self.handle_service_error(e, "broadcast_to_room", {
                "room_id": room_id,
                "event_type": event_data.get("type", "unknown")
            })

    # Helper methods for enhanced WebSocket connection service

    async def _validate_room_access_permissions(self, user: User, room_id: int) -> None:
        """Validate user permissions for room access."""
        # Check if user is a participant in the room
        participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
        participants = await participant_service.get_room_participants(room_id)
        user_participant = next((p for p in participants if p.user_id == user.id), None)

        if user_participant:
            return

        # Admins can access any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Access denied to room connections")

    async def _validate_room_broadcast_permissions(self, user: User, room_id: int) -> None:
        """Validate user permissions for room broadcasting."""
        # Check if user is a participant with broadcast permissions
        participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
        participants = await participant_service.get_room_participants(room_id)
        user_participant = next((p for p in participants if p.user_id == user.id), None)

        if user_participant and user_participant.role in ["owner", "moderator"]:
            return

        # Admins can broadcast to any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Insufficient permissions for room broadcasting")


class WebSocketEventService(BaseService[WebSocketEvent, WebSocketEventRepository]):
    """
    WebSocket event service for event delivery and content moderation.

    Provides comprehensive event management including:
    - Event creation with content validation and moderation
    - Priority-based delivery management
    - Retry logic with exponential backoff
    - Content moderation via AI/ML integration
    - Performance monitoring and delivery tracking
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        """Initialize WebSocket event service with dependencies."""
        super().__init__(
            repository_class=WebSocketEventRepository,
            model_class=WebSocketEvent,
            db_session=db
        )

        # Initialize dependent services
        self.rbac_service = RBACService(db)
        self.cache_manager = cache_manager

        # Initialize repositories
        self.connection_repository = WebSocketConnectionRepository(db, cache_manager)

        # Initialize repository with cache
        self._repository_instance = None

    async def _get_repository(self) -> WebSocketEventRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = WebSocketEventRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate WebSocket event creation data.

        Args:
            data: Event creation data

        Returns:
            Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['connection_id', 'event_type', 'payload']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"Missing required field: {field}")

        # Validate connection exists
        connection = await self.connection_repository.get(data['connection_id'])
        if not connection:
            raise ValidationError(f"Connection {data['connection_id']} not found")

        # Validate connection is active
        if connection.status not in [ConnectionStatus.CONNECTED, ConnectionStatus.AUTHENTICATED]:
            raise ValidationError(f"Connection {data['connection_id']} is not active")

        # Validate event type
        try:
            EventType(data['event_type'])
        except ValueError:
            raise ValidationError(f"Invalid event type: {data['event_type']}")

        # Validate priority
        if 'priority' in data:
            try:
                EventPriority(data['priority'])
            except ValueError:
                raise ValidationError(f"Invalid priority: {data['priority']}")

        # Set default values
        data.setdefault('priority', EventPriority.NORMAL)
        data.setdefault('max_retries', 3)
        data.setdefault('event_id', f"evt_{int(time.time() * 1000)}")

        return data

    async def create_event(
        self,
        event_data: WebSocketEventCreate,
        current_user: Optional[User] = None,
        moderate_content: bool = True
    ) -> WebSocketEventResponse:
        """
        Create a new WebSocket event with content moderation.

        Performance target: <500ms for event creation.

        Args:
            event_data: Event creation data
            current_user: Current authenticated user (for permission checking)
            moderate_content: Whether to apply content moderation

        Returns:
            Created WebSocket event

        Raises:
            ValidationError: If validation fails
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start("create_event",
                                             event_type=event_data.event_type)

        try:
            # Prepare event data
            event_dict = event_data.model_dump()

            # Apply content moderation if enabled
            if moderate_content and 'payload' in event_dict:
                moderated_payload = await self._moderate_event_content(
                    event_dict['payload'],
                    event_dict['event_type']
                )
                event_dict['payload'] = moderated_payload

            # Create event
            async with self.get_transaction_context() as session:
                repository = WebSocketEventRepository(session, self.cache_manager)
                event = await repository.create_event(event_dict)

                self.log_operation_success(
                    correlation,
                    f"Created WebSocket event {event.event_id}"
                )

                return WebSocketEventResponse.model_validate(event)

        except Exception as e:
            await self.handle_service_error(e, "create_event",
                                          {"event_type": event_data.event_type})

    async def get_pending_events(
        self,
        priority: Optional[EventPriority] = None,
        limit: int = 100,
        current_user: Optional[User] = None
    ) -> List[WebSocketEventResponse]:
        """
        Get pending events for delivery (admin operation).

        Performance target: <200ms for event retrieval.

        Args:
            priority: Optional priority filter
            limit: Maximum number of events to return
            current_user: Current authenticated user (for permission checking)

        Returns:
            List of pending events

        Raises:
            ValidationError: If permission denied
        """
        correlation = self.log_operation_start("get_pending_events",
                                             priority=priority, limit=limit)

        try:
            # Check permissions - only admins can view pending events
            if current_user and not has_permission(current_user.role, Permission.VIEW_LOGS):
                raise ValidationError("Insufficient permissions to view pending events")

            repository = await self._get_repository()
            events = await repository.get_pending_events(priority, limit)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(events)} pending events"
            )

            return [WebSocketEventResponse.model_validate(event) for event in events]

        except Exception as e:
            await self.handle_service_error(e, "get_pending_events",
                                          {"priority": priority, "limit": limit})

    async def mark_event_sent(
        self,
        event_id: str,
        processing_time_ms: Optional[float] = None
    ) -> bool:
        """
        Mark event as sent with processing time tracking.

        Performance target: <100ms for status updates.

        Args:
            event_id: Event identifier
            processing_time_ms: Processing time in milliseconds

        Returns:
            True if update successful
        """
        correlation = self.log_operation_start("mark_event_sent", event_id=event_id)

        try:
            repository = await self._get_repository()
            success = await repository.mark_event_sent(event_id, processing_time_ms)

            if success:
                self.log_operation_success(
                    correlation,
                    f"Marked event {event_id} as sent"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "mark_event_sent", {"event_id": event_id})

    async def mark_event_delivered(self, event_id: str) -> bool:
        """
        Mark event as delivered.

        Performance target: <100ms for status updates.

        Args:
            event_id: Event identifier

        Returns:
            True if update successful
        """
        correlation = self.log_operation_start("mark_event_delivered", event_id=event_id)

        try:
            repository = await self._get_repository()
            success = await repository.mark_event_delivered(event_id)

            if success:
                self.log_operation_success(
                    correlation,
                    f"Marked event {event_id} as delivered"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "mark_event_delivered", {"event_id": event_id})

    async def mark_event_acknowledged(self, event_id: str) -> bool:
        """
        Mark event as acknowledged by client.

        Performance target: <100ms for status updates.

        Args:
            event_id: Event identifier

        Returns:
            True if update successful
        """
        correlation = self.log_operation_start("mark_event_acknowledged", event_id=event_id)

        try:
            repository = await self._get_repository()
            success = await repository.mark_event_acknowledged(event_id)

            if success:
                self.log_operation_success(
                    correlation,
                    f"Marked event {event_id} as acknowledged"
                )

            return success

        except Exception as e:
            await self.handle_service_error(e, "mark_event_acknowledged", {"event_id": event_id})

    async def handle_event_failure(
        self,
        event_id: str,
        error_message: str,
        error_code: Optional[str] = None,
        retry_delay_seconds: int = 60
    ) -> bool:
        """
        Handle event delivery failure with retry logic.

        Args:
            event_id: Event identifier
            error_message: Error description
            error_code: Optional error code
            retry_delay_seconds: Delay before next retry

        Returns:
            True if handled successfully
        """
        correlation = self.log_operation_start("handle_event_failure", event_id=event_id)

        try:
            repository = await self._get_repository()

            # Get current event to check retry count
            events = await repository.get_events_by_connection(0, limit=1)  # This needs improvement
            # In a real implementation, we'd have a get_by_event_id method

            # For now, increment retry count
            retry_success = await repository.increment_retry_count(event_id, retry_delay_seconds)

            if not retry_success:
                # Mark as failed if retry increment failed
                await repository.mark_event_failed(event_id, error_message, error_code)

            self.log_operation_success(
                correlation,
                f"Handled failure for event {event_id}"
            )

            return True

        except Exception as e:
            await self.handle_service_error(e, "handle_event_failure", {"event_id": event_id})

    async def _moderate_event_content(
        self,
        payload: Dict[str, Any],
        event_type: str
    ) -> Dict[str, Any]:
        """
        Moderate event content using AI/ML integration.

        Args:
            payload: Event payload to moderate
            event_type: Type of event

        Returns:
            Moderated payload
        """
        try:
            # TODO: Integrate with aimlapi.com for content moderation
            # For now, implement basic content filtering

            moderated_payload = payload.copy()

            # Check for message content in payload
            if 'content' in moderated_payload:
                content = str(moderated_payload['content'])

                # Basic profanity filter (in production, use AI service)
                prohibited_words = ['spam', 'abuse', 'inappropriate']
                for word in prohibited_words:
                    if word.lower() in content.lower():
                        moderated_payload['content'] = content.replace(word, '***')
                        moderated_payload['moderated'] = True
                        moderated_payload['moderation_reason'] = f"Filtered word: {word}"

            return moderated_payload

        except Exception as e:
            logger.warning(f"Content moderation failed: {str(e)}")
            return payload  # Return original payload if moderation fails


class UserPresenceService(BaseService[UserPresence, UserPresenceRepository]):
    """
    User presence service for real-time status management.

    Provides comprehensive presence management including:
    - Real-time status updates with <100ms performance
    - Activity tracking and auto-away functionality
    - Connection count management
    - Online users tracking with caching
    - RBAC integration for presence visibility
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        """Initialize user presence service with dependencies."""
        super().__init__(
            repository_class=UserPresenceRepository,
            model_class=UserPresence,
            db_session=db
        )

        # Initialize dependent services
        self.rbac_service = RBACService(db)
        self.cache_manager = cache_manager

        # Initialize repositories
        self.user_repository = UserRepository(db)

        # Initialize repository with cache
        self._repository_instance = None

    async def _get_repository(self) -> UserPresenceRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = UserPresenceRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate user presence creation data.

        Args:
            data: Presence creation data

        Returns:
            Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        if 'user_id' not in data or not data['user_id']:
            raise ValidationError("Missing required field: user_id")

        # Validate user exists
        user = await self.user_repository.get(data['user_id'])
        if not user:
            raise ValidationError(f"User {data['user_id']} not found")

        # Set default values
        data.setdefault('status', 'offline')
        data.setdefault('active_connections_count', 0)
        data.setdefault('auto_away_enabled', True)

        return data

    async def update_user_status(
        self,
        user_id: int,
        status: str,
        activity_type: Optional[str] = None,
        current_user: Optional[User] = None
    ) -> UserPresenceResponse:
        """
        Update user presence status.

        Performance target: <100ms for status updates.

        Args:
            user_id: User ID
            status: New presence status (online, away, offline)
            activity_type: Optional activity type
            current_user: Current authenticated user (for permission checking)

        Returns:
            Updated user presence

        Raises:
            ValidationError: If validation fails or permission denied
        """
        correlation = self.log_operation_start("update_user_status",
                                             user_id=user_id, status=status)

        try:
            # Check permissions - users can only update their own status unless admin
            if current_user:
                if (user_id != current_user.id and
                    not has_permission(current_user.role, Permission.MANAGE_SYSTEM)):
                    raise ValidationError("Cannot update status for another user")

            # Validate status
            valid_statuses = ['online', 'away', 'offline']
            if status not in valid_statuses:
                raise ValidationError(f"Invalid status: {status}. Must be one of {valid_statuses}")

            repository = await self._get_repository()

            # Update status
            success = await repository.update_user_status(user_id, status, activity_type)
            if not success:
                raise ServiceError("Failed to update user status")

            # Get updated presence
            presence = await repository.get_user_presence(user_id)
            if not presence:
                raise NotFoundError("User presence", user_id)

            self.log_operation_success(
                correlation,
                f"Updated status for user {user_id} to {status}"
            )

            return UserPresenceResponse.model_validate(presence)

        except Exception as e:
            await self.handle_service_error(e, "update_user_status",
                                          {"user_id": user_id, "status": status})

    async def increment_user_connections(
        self,
        user_id: int,
        current_user: Optional[User] = None
    ) -> UserPresenceResponse:
        """
        Increment user's active connection count.

        Performance target: <100ms for connection updates.

        Args:
            user_id: User ID
            current_user: Current authenticated user (for permission checking)

        Returns:
            Updated user presence
        """
        correlation = self.log_operation_start("increment_user_connections", user_id=user_id)

        try:
            # Check permissions
            if current_user:
                if (user_id != current_user.id and
                    not has_permission(current_user.role, Permission.MANAGE_SYSTEM)):
                    raise ValidationError("Cannot modify connections for another user")

            repository = await self._get_repository()

            # Increment connections
            success = await repository.increment_connections(user_id)
            if not success:
                raise ServiceError("Failed to increment user connections")

            # Get updated presence
            presence = await repository.get_user_presence(user_id)
            if not presence:
                raise NotFoundError("User presence", user_id)

            self.log_operation_success(
                correlation,
                f"Incremented connections for user {user_id}"
            )

            return UserPresenceResponse.model_validate(presence)

        except Exception as e:
            await self.handle_service_error(e, "increment_user_connections", {"user_id": user_id})

    async def decrement_user_connections(
        self,
        user_id: int,
        current_user: Optional[User] = None
    ) -> UserPresenceResponse:
        """
        Decrement user's active connection count.

        Performance target: <100ms for connection updates.

        Args:
            user_id: User ID
            current_user: Current authenticated user (for permission checking)

        Returns:
            Updated user presence
        """
        correlation = self.log_operation_start("decrement_user_connections", user_id=user_id)

        try:
            # Check permissions
            if current_user:
                if (user_id != current_user.id and
                    not has_permission(current_user.role, Permission.MANAGE_SYSTEM)):
                    raise ValidationError("Cannot modify connections for another user")

            repository = await self._get_repository()

            # Decrement connections
            success = await repository.decrement_connections(user_id)
            if not success:
                raise ServiceError("Failed to decrement user connections")

            # Get updated presence
            presence = await repository.get_user_presence(user_id)
            if not presence:
                raise NotFoundError("User presence", user_id)

            self.log_operation_success(
                correlation,
                f"Decremented connections for user {user_id}"
            )

            return UserPresenceResponse.model_validate(presence)

        except Exception as e:
            await self.handle_service_error(e, "decrement_user_connections", {"user_id": user_id})

    async def get_user_presence(
        self,
        user_id: int,
        current_user: Optional[User] = None
    ) -> Optional[UserPresenceResponse]:
        """
        Get user presence information.

        Performance target: <200ms for presence retrieval.

        Args:
            user_id: User ID
            current_user: Current authenticated user (for permission checking)

        Returns:
            User presence if found and accessible
        """
        correlation = self.log_operation_start("get_user_presence", user_id=user_id)

        try:
            # Check permissions - users can view their own presence or admins can view any
            if current_user:
                if (user_id != current_user.id and
                    not has_permission(current_user.role, Permission.READ_USER)):
                    raise ValidationError("Cannot view presence for another user")

            repository = await self._get_repository()
            presence = await repository.get_user_presence(user_id)

            if presence:
                self.log_operation_success(
                    correlation,
                    f"Retrieved presence for user {user_id}"
                )
                return UserPresenceResponse.model_validate(presence)

            return None

        except Exception as e:
            await self.handle_service_error(e, "get_user_presence", {"user_id": user_id})

    async def get_online_users(
        self,
        limit: int = 100,
        current_user: Optional[User] = None
    ) -> List[UserPresenceResponse]:
        """
        Get list of online users.

        Performance target: <200ms for online users retrieval.

        Args:
            limit: Maximum number of users to return
            current_user: Current authenticated user (for permission checking)

        Returns:
            List of online user presence records
        """
        correlation = self.log_operation_start("get_online_users", limit=limit)

        try:
            # Check permissions - only authenticated users can view online users
            if current_user and not has_permission(current_user.role, Permission.READ_USER):
                raise ValidationError("Insufficient permissions to view online users")

            repository = await self._get_repository()
            users = await repository.get_online_users(limit)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(users)} online users"
            )

            return [UserPresenceResponse.model_validate(user) for user in users]

        except Exception as e:
            await self.handle_service_error(e, "get_online_users", {"limit": limit})

    async def set_auto_away_users(
        self,
        minutes_inactive: int = 15,
        current_user: Optional[User] = None
    ) -> int:
        """
        Set users to away status if they've been inactive (admin operation).

        Args:
            minutes_inactive: Minutes of inactivity before setting away
            current_user: Current authenticated user (for permission checking)

        Returns:
            Number of users set to away
        """
        correlation = self.log_operation_start("set_auto_away_users",
                                             minutes_inactive=minutes_inactive)

        try:
            # Check permissions - only admins can run auto-away
            if current_user and not has_permission(current_user.role, Permission.MANAGE_SYSTEM):
                raise ValidationError("Insufficient permissions for auto-away operation")

            repository = await self._get_repository()
            away_count = await repository.set_auto_away_users(minutes_inactive)

            self.log_operation_success(
                correlation,
                f"Set {away_count} users to away status"
            )

            return away_count

        except Exception as e:
            await self.handle_service_error(e, "set_auto_away_users",
                                          {"minutes_inactive": minutes_inactive})


class WebSocketMetricsService(BaseService[WebSocketConnectionMetrics, WebSocketMetricsRepository]):
    """
    WebSocket metrics service for analytics and performance monitoring.

    Provides comprehensive metrics management including:
    - Metrics aggregation with hourly and daily summaries
    - Performance monitoring and reporting
    - Bulk operations for high-throughput scenarios
    - Admin-level analytics access with RBAC integration
    - Cache-optimized queries for dashboard performance
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        """Initialize WebSocket metrics service with dependencies."""
        super().__init__(
            repository_class=WebSocketMetricsRepository,
            model_class=WebSocketConnectionMetrics,
            db_session=db
        )

        # Initialize dependent services
        self.rbac_service = RBACService(db)
        self.cache_manager = cache_manager

        # Initialize repository with cache
        self._repository_instance = None

    async def _get_repository(self) -> WebSocketMetricsRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = WebSocketMetricsRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate WebSocket metrics creation data.

        Args:
            data: Metrics creation data

        Returns:
            Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['metric_date', 'metric_hour']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"Missing required field: {field}")

        # Validate metric_hour range
        if not (0 <= data['metric_hour'] <= 23):
            raise ValidationError("metric_hour must be between 0 and 23")

        # Set default values for metrics
        data.setdefault('total_connections', 0)
        data.setdefault('active_connections', 0)
        data.setdefault('authenticated_connections', 0)
        data.setdefault('failed_connections', 0)
        data.setdefault('total_messages_sent', 0)
        data.setdefault('total_bytes_transferred', 0)
        data.setdefault('total_errors', 0)

        return data

    async def create_or_update_hourly_metrics(
        self,
        metric_date: datetime,
        metric_hour: int,
        metrics_data: Dict[str, Any],
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Create or update hourly metrics record.

        Performance target: <500ms for metrics creation.

        Args:
            metric_date: Date for metrics
            metric_hour: Hour of the day (0-23)
            metrics_data: Metrics data to update
            current_user: Current authenticated user (for permission checking)

        Returns:
            Created or updated metrics record as dict

        Raises:
            ValidationError: If validation fails or permission denied
        """
        correlation = self.log_operation_start("create_or_update_hourly_metrics",
                                             metric_date=metric_date.date(),
                                             metric_hour=metric_hour)

        try:
            # Check permissions - only admins can create/update metrics
            if current_user and not has_permission(current_user.role, Permission.VIEW_ANALYTICS):
                raise ValidationError("Insufficient permissions to manage metrics")

            repository = await self._get_repository()

            # Create or update metrics
            metrics = await repository.create_or_update_hourly_metrics(
                metric_date, metric_hour, metrics_data
            )

            self.log_operation_success(
                correlation,
                f"Created/updated hourly metrics for {metric_date.date()} hour {metric_hour}"
            )

            # Convert to dict for response
            return {
                "id": metrics.id,
                "metric_date": metrics.metric_date.isoformat(),
                "metric_hour": metrics.metric_hour,
                "total_connections": metrics.total_connections,
                "active_connections": metrics.active_connections,
                "authenticated_connections": metrics.authenticated_connections,
                "failed_connections": metrics.failed_connections,
                "total_messages_sent": metrics.total_messages_sent,
                "total_bytes_transferred": metrics.total_bytes_transferred,
                "total_errors": metrics.total_errors
            }

        except Exception as e:
            await self.handle_service_error(e, "create_or_update_hourly_metrics",
                                          {"metric_date": metric_date, "metric_hour": metric_hour})

    async def get_daily_aggregates(
        self,
        date: datetime,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Get daily aggregated metrics.

        Performance target: <200ms for daily aggregates.

        Args:
            date: Date for aggregation
            current_user: Current authenticated user (for permission checking)

        Returns:
            Dictionary with daily aggregated metrics
        """
        correlation = self.log_operation_start("get_daily_aggregates", date=date.date())

        try:
            # Check permissions - only users with analytics access
            if current_user and not has_permission(current_user.role, Permission.VIEW_ANALYTICS):
                raise ValidationError("Insufficient permissions to view metrics")

            repository = await self._get_repository()
            aggregates = await repository.get_daily_aggregates(date)

            self.log_operation_success(
                correlation,
                f"Retrieved daily aggregates for {date.date()}"
            )

            return aggregates

        except Exception as e:
            await self.handle_service_error(e, "get_daily_aggregates", {"date": date})

    async def get_metrics_range(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 1000,
        current_user: Optional[User] = None
    ) -> List[Dict[str, Any]]:
        """
        Get metrics for a date range.

        Performance target: <200ms for range queries.

        Args:
            start_date: Start date for range
            end_date: End date for range
            limit: Maximum number of records to return
            current_user: Current authenticated user (for permission checking)

        Returns:
            List of metrics records as dictionaries
        """
        correlation = self.log_operation_start("get_metrics_range",
                                             start_date=start_date.date(),
                                             end_date=end_date.date())

        try:
            # Check permissions
            if current_user and not has_permission(current_user.role, Permission.VIEW_ANALYTICS):
                raise ValidationError("Insufficient permissions to view metrics")

            repository = await self._get_repository()
            metrics = await repository.get_metrics_range(start_date, end_date, limit)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(metrics)} metrics records for date range"
            )

            # Convert to dictionaries
            return [
                {
                    "id": m.id,
                    "metric_date": m.metric_date.isoformat(),
                    "metric_hour": m.metric_hour,
                    "total_connections": m.total_connections,
                    "active_connections": m.active_connections,
                    "authenticated_connections": m.authenticated_connections,
                    "failed_connections": m.failed_connections,
                    "total_messages_sent": m.total_messages_sent,
                    "total_bytes_transferred": m.total_bytes_transferred,
                    "total_errors": m.total_errors
                }
                for m in metrics
            ]

        except Exception as e:
            await self.handle_service_error(e, "get_metrics_range",
                                          {"start_date": start_date, "end_date": end_date})

    async def get_performance_summary(
        self,
        hours: int = 24,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.

        Performance target: <200ms for performance summaries.

        Args:
            hours: Number of hours to analyze
            current_user: Current authenticated user (for permission checking)

        Returns:
            Dictionary with performance summary
        """
        correlation = self.log_operation_start("get_performance_summary", hours=hours)

        try:
            # Check permissions
            if current_user and not has_permission(current_user.role, Permission.VIEW_ANALYTICS):
                raise ValidationError("Insufficient permissions to view performance summary")

            repository = await self._get_repository()
            summary = await repository.get_performance_summary(hours)

            self.log_operation_success(
                correlation,
                f"Retrieved performance summary for {hours} hours"
            )

            return summary

        except Exception as e:
            await self.handle_service_error(e, "get_performance_summary", {"hours": hours})

    async def bulk_create_metrics(
        self,
        metrics_data: List[Dict[str, Any]],
        current_user: Optional[User] = None
    ) -> int:
        """
        Bulk create metrics records for high-throughput scenarios.

        Performance target: >1000 records/second processing.

        Args:
            metrics_data: List of metrics data dictionaries
            current_user: Current authenticated user (for permission checking)

        Returns:
            Number of records created
        """
        correlation = self.log_operation_start("bulk_create_metrics",
                                             count=len(metrics_data))

        try:
            # Check permissions - only admins can bulk create metrics
            if current_user and not has_permission(current_user.role, Permission.MANAGE_SYSTEM):
                raise ValidationError("Insufficient permissions for bulk metrics creation")

            repository = await self._get_repository()
            created_count = await repository.bulk_create_metrics(metrics_data)

            self.log_operation_success(
                correlation,
                f"Bulk created {created_count} metrics records"
            )

            return created_count

        except Exception as e:
            await self.handle_service_error(e, "bulk_create_metrics",
                                          {"count": len(metrics_data)})

    async def cleanup_old_metrics(
        self,
        days: int = 90,
        current_user: Optional[User] = None
    ) -> int:
        """
        Clean up old metrics records (admin operation).

        Args:
            days: Number of days to keep metrics
            current_user: Current authenticated user (for permission checking)

        Returns:
            Number of records cleaned up
        """
        correlation = self.log_operation_start("cleanup_old_metrics", days=days)

        try:
            # Check permissions - only admins can cleanup metrics
            if current_user and not has_permission(current_user.role, Permission.MANAGE_SYSTEM):
                raise ValidationError("Insufficient permissions for metrics cleanup")

            repository = await self._get_repository()
            cleaned_count = await repository.cleanup_old_metrics(days)

            self.log_operation_success(
                correlation,
                f"Cleaned up {cleaned_count} old metrics records"
            )

            return cleaned_count

        except Exception as e:
            await self.handle_service_error(e, "cleanup_old_metrics", {"days": days})


# ============================================================================
# Enhanced Real-time Communication Services for Task 6.1.1 Phase 3
# ============================================================================

class WebSocketRoomService(BaseService[WebSocketRoom, WebSocketRoomRepository]):
    """
    WebSocket room service for real-time conversation management.

    Provides comprehensive room management including:
    - Room lifecycle management with booking integration
    - Real-time participant tracking and capacity management
    - Activity monitoring and message count tracking
    - Performance-optimized operations with <500ms response times
    - Integration with booking communication system
    - Circuit breaker patterns for external service reliability
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketRoom, WebSocketRoomRepository, db)
        self.cache_manager = cache_manager
        self._repository_instance: Optional[WebSocketRoomRepository] = None

        # Initialize external service dependencies
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)
        self.rbac_service = RBACService(db)

        # Circuit breaker for external services
        from app.core.circuit_breaker import CircuitBreaker, CircuitBreakerConfig
        self._email_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(
                name="websocket_room_email",
                failure_threshold=5,
                timeout_seconds=30,
                recovery_timeout=60
            )
        )

    async def create_room(
        self,
        room_data: WebSocketRoomCreate,
        current_user: Optional[User] = None
    ) -> WebSocketRoomResponse:
        """
        Create a new WebSocket room with automatic configuration.

        Performance Metrics:
        - Target response time: <200ms for room creation
        - Database transaction time: <100ms with optimized inserts
        - Cache update time: <50ms for room indexing
        - Notification delivery: <100ms for participant alerts

        Args:
            room_data: Room creation data
            current_user: Current authenticated user

        Returns:
            Created WebSocket room response

        Raises:
            ValidationError: If room creation data is invalid
            ConflictError: If room ID already exists
            ServiceError: If room creation fails
        """
        correlation = self.log_operation_start("create_room",
                                             room_id=room_data.room_id,
                                             room_type=room_data.room_type)

        try:
            # Validate permissions for room creation
            if current_user:
                await self._validate_room_creation_permissions(current_user, room_data)

            # Validate room data
            validated_data = await self.validate_create_data(room_data.model_dump())

            # Create room using repository
            repository = await self._get_repository()
            room = await repository.create_room(validated_data)

            # Initialize room owner as participant if specified
            if room.owner_id:
                participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
                await participant_service.add_participant(
                    room_id=room.id,
                    user_id=room.owner_id,
                    role="owner",
                    current_user=current_user
                )

            # Send room creation notifications
            await self._send_room_creation_notifications(room, current_user)

            self.log_operation_success(
                correlation,
                f"Created WebSocket room: {room.room_id} (type: {room.room_type})"
            )

            return WebSocketRoomResponse.model_validate(room)

        except Exception as e:
            await self.handle_service_error(e, "create_room", {"room_data": room_data.model_dump()})

    async def get_room_by_id(
        self,
        room_id: str,
        current_user: Optional[User] = None
    ) -> Optional[WebSocketRoomResponse]:
        """
        Get room by room ID with access control validation.

        Performance Metrics:
        - Target response time: <100ms for room retrieval
        - Cache hit rate: >90% for active rooms
        - Database query time: <50ms for uncached lookups

        Args:
            room_id: Unique room identifier
            current_user: Current authenticated user

        Returns:
            WebSocket room response if found and accessible, None otherwise

        Raises:
            ValidationError: If user lacks access permissions
            ServiceError: If retrieval operation fails
        """
        correlation = self.log_operation_start("get_room_by_id", room_id=room_id)

        try:
            repository = await self._get_repository()
            room = await repository.get_room_by_id(room_id)

            if not room:
                self.log_operation_success(correlation, f"Room not found: {room_id}")
                return None

            # Validate access permissions
            if current_user:
                await self._validate_room_access_permissions(current_user, room)

            self.log_operation_success(correlation, f"Retrieved room: {room_id}")

            # Enhance room response with computed properties
            room_response = WebSocketRoomResponse.model_validate(room)
            room_response.is_full = room.is_full
            room_response.is_open = room.is_open

            return room_response

        except Exception as e:
            await self.handle_service_error(e, "get_room_by_id", {"room_id": room_id})

    async def get_rooms_by_booking(
        self,
        booking_id: int,
        current_user: Optional[User] = None
    ) -> List[WebSocketRoomResponse]:
        """
        Get all rooms associated with a booking.

        Performance Metrics:
        - Target response time: <200ms for booking room queries
        - Cache integration for frequently accessed bookings
        - Database query optimization with booking index

        Args:
            booking_id: Booking ID to search for
            current_user: Current authenticated user

        Returns:
            List of WebSocket room responses for the booking

        Raises:
            ValidationError: If user lacks access to booking
            ServiceError: If retrieval operation fails
        """
        correlation = self.log_operation_start("get_rooms_by_booking", booking_id=booking_id)

        try:
            # Validate booking access permissions
            if current_user:
                await self._validate_booking_access_permissions(current_user, booking_id)

            repository = await self._get_repository()
            rooms = await repository.get_rooms_by_booking(booking_id)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(rooms)} rooms for booking: {booking_id}"
            )

            # Convert to response objects with computed properties
            room_responses = []
            for room in rooms:
                room_response = WebSocketRoomResponse.model_validate(room)
                room_response.is_full = room.is_full
                room_response.is_open = room.is_open
                room_responses.append(room_response)

            return room_responses

        except Exception as e:
            await self.handle_service_error(e, "get_rooms_by_booking", {"booking_id": booking_id})

    async def close_room(
        self,
        room_id: str,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Close a WebSocket room and notify participants.

        Performance Metrics:
        - Target response time: <300ms for room closure
        - Participant notification: <100ms per participant
        - Database transaction time: <100ms for status updates

        Args:
            room_id: Room ID to close
            current_user: Current authenticated user

        Returns:
            True if room was closed successfully, False if not found

        Raises:
            ValidationError: If user lacks permissions to close room
            ServiceError: If closure operation fails
        """
        correlation = self.log_operation_start("close_room", room_id=room_id)

        try:
            repository = await self._get_repository()
            room = await repository.get_room_by_id(room_id)

            if not room:
                self.log_operation_success(correlation, f"Room not found for closure: {room_id}")
                return False

            # Validate closure permissions
            if current_user:
                await self._validate_room_closure_permissions(current_user, room)

            # Update room status
            room.is_active = False
            room.closed_at = datetime.now(timezone.utc)
            await repository.update(room.id, {
                "is_active": False,
                "closed_at": room.closed_at
            })

            # Notify all participants about room closure
            await self._notify_room_closure(room, current_user)

            # Remove all participants
            participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
            participants = await participant_service.get_room_participants(room.id)
            if participants:
                user_ids = [p.user_id for p in participants]
                await participant_service.bulk_remove_participants(room.id, user_ids, current_user)

            self.log_operation_success(correlation, f"Closed room: {room_id}")

            return True

        except Exception as e:
            await self.handle_service_error(e, "close_room", {"room_id": room_id})

    async def update_room_activity(
        self,
        room_id: str,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Update room last activity timestamp.

        Performance Metrics:
        - Target response time: <50ms for activity updates
        - Optimized single-field update query
        - Cache invalidation for real-time consistency

        Args:
            room_id: Room ID to update
            current_user: Current authenticated user

        Returns:
            True if update successful, False otherwise

        Raises:
            ServiceError: If update operation fails
        """
        correlation = self.log_operation_start("update_room_activity", room_id=room_id)

        try:
            repository = await self._get_repository()
            success = await repository.update_room_activity(room_id)

            if success:
                self.log_operation_success(correlation, f"Updated room activity: {room_id}")
            else:
                logger.warning(f"Failed to update room activity: {room_id}")

            return success

        except Exception as e:
            await self.handle_service_error(e, "update_room_activity", {"room_id": room_id})

    async def increment_message_count(
        self,
        room_id: str,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Increment room message count and update activity.

        Performance Metrics:
        - Target response time: <50ms for counter updates
        - Atomic increment operation
        - Cache invalidation for consistency

        Args:
            room_id: Room ID to update
            current_user: Current authenticated user

        Returns:
            True if update successful, False otherwise

        Raises:
            ServiceError: If increment operation fails
        """
        correlation = self.log_operation_start("increment_message_count", room_id=room_id)

        try:
            repository = await self._get_repository()
            success = await repository.increment_message_count(room_id)

            if success:
                self.log_operation_success(correlation, f"Incremented message count: {room_id}")
            else:
                logger.warning(f"Failed to increment message count: {room_id}")

            return success

        except Exception as e:
            await self.handle_service_error(e, "increment_message_count", {"room_id": room_id})

    # Helper methods for WebSocketRoomService

    async def _get_repository(self) -> WebSocketRoomRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = WebSocketRoomRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate WebSocket room creation data.

        Args:
            data: Room creation data

        Returns:
            Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['room_id', 'room_name', 'room_type']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError(f"Missing required field: {field}")

        # Validate room_id uniqueness
        repository = await self._get_repository()
        existing_room = await repository.get_room_by_id(data['room_id'])
        if existing_room:
            raise ConflictError(f"Room with ID '{data['room_id']}' already exists")

        # Validate room_type
        valid_types = ['booking', 'group', 'broadcast', 'system']
        if data['room_type'] not in valid_types:
            raise ValidationError(f"Invalid room_type. Must be one of: {valid_types}")

        # Set default values
        data.setdefault('is_active', True)
        data.setdefault('is_private', True)
        data.setdefault('max_participants', 100)
        data.setdefault('participant_count', 0)
        data.setdefault('total_messages', 0)

        return data

    async def _validate_room_creation_permissions(self, user: User, room_data: WebSocketRoomCreate) -> None:
        """Validate user permissions for room creation."""
        # Basic permission check - users can create rooms they own
        if room_data.owner_id and room_data.owner_id != user.id:
            # Check if user has admin permissions to create rooms for others
            if not has_permission(user.role, Permission.MANAGE_SYSTEM):
                raise ValidationError("Insufficient permissions to create room for another user")

        # Booking rooms require booking access
        if room_data.room_type == "booking" and room_data.booking_id:
            # This would integrate with booking service to validate access
            # For now, we'll assume basic validation
            pass

    async def _validate_room_access_permissions(self, user: User, room: WebSocketRoom) -> None:
        """Validate user permissions for room access."""
        # Public rooms are accessible to all authenticated users
        if not room.is_private:
            return

        # Private rooms require specific access
        if room.owner_id == user.id:
            return

        # Check if user is a participant
        participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
        participants = await participant_service.get_room_participants(room.id)
        user_participant = next((p for p in participants if p.user_id == user.id), None)

        if not user_participant:
            raise ValidationError("Access denied to private room")

    async def _validate_booking_access_permissions(self, user: User, booking_id: int) -> None:
        """Validate user permissions for booking access."""
        # This would integrate with booking service to validate access
        # For now, we'll implement basic validation
        # In a real implementation, this would check if user is customer or vendor for the booking
        pass

    async def _validate_room_closure_permissions(self, user: User, room: WebSocketRoom) -> None:
        """Validate user permissions for room closure."""
        # Room owner can close room
        if room.owner_id == user.id:
            return

        # Admins can close any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Insufficient permissions to close room")

    async def _send_room_creation_notifications(self, room: WebSocketRoom, current_user: Optional[User]) -> None:
        """Send notifications about room creation."""
        try:
            # Send email notification with circuit breaker
            await self._email_circuit_breaker.call(
                self._send_room_creation_email,
                room, current_user
            )
        except Exception as e:
            logger.warning(f"Failed to send room creation notifications: {str(e)}")

    async def _send_room_creation_email(self, room: WebSocketRoom, current_user: Optional[User]) -> None:
        """Send email notification about room creation."""
        if not room.owner_id or not current_user:
            return

        # This would integrate with email service
        # For now, we'll log the notification
        logger.info(f"Room creation email notification for room: {room.room_id}")

    async def _notify_room_closure(self, room: WebSocketRoom, current_user: Optional[User]) -> None:
        """Notify participants about room closure."""
        try:
            # Get all participants
            participant_service = WebSocketRoomParticipantService(self.db, self.cache_manager)
            participants = await participant_service.get_room_participants(room.id)

            # Send closure notifications
            for participant in participants:
                await self._send_room_closure_notification(room, participant.user_id)

        except Exception as e:
            logger.warning(f"Failed to notify room closure: {str(e)}")

    async def _send_room_closure_notification(self, room: WebSocketRoom, user_id: int) -> None:
        """Send room closure notification to a specific user."""
        # This would integrate with push notification service
        # For now, we'll log the notification
        logger.info(f"Room closure notification for user {user_id}, room: {room.room_id}")


class WebSocketRoomParticipantService(BaseService[WebSocketRoomParticipant, WebSocketRoomParticipantRepository]):
    """
    WebSocket room participant service for real-time participant management.

    Provides comprehensive participant management including:
    - Join/leave operations with role management and capacity checking
    - Activity monitoring and presence tracking
    - Bulk participant operations for scalability
    - Performance-optimized operations with <500ms response times
    - Integration with room management and notification systems
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketRoomParticipant, WebSocketRoomParticipantRepository, db)
        self.cache_manager = cache_manager
        self._repository_instance: Optional[WebSocketRoomParticipantRepository] = None

        # Initialize external service dependencies
        self.push_service = PushNotificationService(db)
        self.rbac_service = RBACService(db)

    async def add_participant(
        self,
        room_id: int,
        user_id: int,
        connection_id: Optional[int] = None,
        role: str = "participant",
        current_user: Optional[User] = None
    ) -> WebSocketRoomParticipantResponse:
        """
        Add a participant to a WebSocket room with capacity checking.

        Performance Metrics:
        - Target response time: <200ms for participant addition
        - Database transaction time: <100ms with optimized inserts
        - Capacity check time: <50ms with room cache lookup
        - Notification delivery: <100ms for participant alerts

        Args:
            room_id: Room ID to join
            user_id: User ID of participant
            connection_id: Optional WebSocket connection ID
            role: Participant role (owner, moderator, participant)
            current_user: Current authenticated user

        Returns:
            Created participant response

        Raises:
            ValidationError: If participant addition fails or room is full
            ConflictError: If participant already exists
            ServiceError: If addition operation fails
        """
        correlation = self.log_operation_start("add_participant",
                                             room_id=room_id,
                                             user_id=user_id,
                                             role=role)

        try:
            # Validate permissions for participant addition
            if current_user:
                await self._validate_participant_addition_permissions(current_user, room_id, user_id, role)

            # Add participant using repository
            repository = await self._get_repository()
            participant = await repository.add_participant(room_id, user_id, connection_id, role)

            # Send participant addition notifications
            await self._send_participant_addition_notifications(participant, current_user)

            self.log_operation_success(
                correlation,
                f"Added participant {user_id} to room {room_id} with role {role}"
            )

            return WebSocketRoomParticipantResponse.model_validate(participant)

        except Exception as e:
            await self.handle_service_error(e, "add_participant", {
                "room_id": room_id,
                "user_id": user_id,
                "role": role
            })

    async def remove_participant(
        self,
        room_id: int,
        user_id: int,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Remove a participant from a WebSocket room.

        Performance Metrics:
        - Target response time: <200ms for participant removal
        - Database transaction time: <100ms with optimized updates
        - Notification delivery: <100ms for participant alerts

        Args:
            room_id: Room ID to leave
            user_id: User ID of participant
            current_user: Current authenticated user

        Returns:
            True if participant was removed, False if not found

        Raises:
            ValidationError: If user lacks permissions to remove participant
            ServiceError: If removal operation fails
        """
        correlation = self.log_operation_start("remove_participant",
                                             room_id=room_id,
                                             user_id=user_id)

        try:
            # Validate permissions for participant removal
            if current_user:
                await self._validate_participant_removal_permissions(current_user, room_id, user_id)

            # Remove participant using repository
            repository = await self._get_repository()
            success = await repository.remove_participant(room_id, user_id)

            if success:
                # Send participant removal notifications
                await self._send_participant_removal_notifications(room_id, user_id, current_user)

                self.log_operation_success(
                    correlation,
                    f"Removed participant {user_id} from room {room_id}"
                )
            else:
                logger.warning(f"Participant {user_id} not found in room {room_id}")

            return success

        except Exception as e:
            await self.handle_service_error(e, "remove_participant", {
                "room_id": room_id,
                "user_id": user_id
            })

    async def get_room_participants(
        self,
        room_id: int,
        active_only: bool = True,
        current_user: Optional[User] = None
    ) -> List[WebSocketRoomParticipantResponse]:
        """
        Get all participants in a room with access control validation.

        Performance Metrics:
        - Target response time: <150ms for participant queries
        - Cache hit rate: >85% for active rooms
        - Database query optimization with room index

        Args:
            room_id: Room ID to get participants for
            active_only: Whether to return only active participants
            current_user: Current authenticated user

        Returns:
            List of room participant responses

        Raises:
            ValidationError: If user lacks access to room
            ServiceError: If retrieval operation fails
        """
        correlation = self.log_operation_start("get_room_participants",
                                             room_id=room_id,
                                             active_only=active_only)

        try:
            # Validate permissions for participant access
            if current_user:
                await self._validate_room_access_permissions(current_user, room_id)

            repository = await self._get_repository()
            participants = await repository.get_room_participants(room_id, active_only)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(participants)} participants for room {room_id}"
            )

            return [WebSocketRoomParticipantResponse.model_validate(p) for p in participants]

        except Exception as e:
            await self.handle_service_error(e, "get_room_participants", {
                "room_id": room_id,
                "active_only": active_only
            })

    async def get_user_rooms(
        self,
        user_id: int,
        active_only: bool = True,
        current_user: Optional[User] = None
    ) -> List[WebSocketRoomParticipantResponse]:
        """
        Get all rooms a user is participating in.

        Performance Metrics:
        - Target response time: <200ms for user room queries
        - Cache integration for frequently accessed users
        - Database query optimization with user index

        Args:
            user_id: User ID to get rooms for
            active_only: Whether to return only active participations
            current_user: Current authenticated user

        Returns:
            List of user's room participation responses

        Raises:
            ValidationError: If user lacks access to user data
            ServiceError: If retrieval operation fails
        """
        correlation = self.log_operation_start("get_user_rooms",
                                             user_id=user_id,
                                             active_only=active_only)

        try:
            # Validate permissions for user room access
            if current_user:
                await self._validate_user_room_access_permissions(current_user, user_id)

            repository = await self._get_repository()
            participations = await repository.get_user_rooms(user_id, active_only)

            self.log_operation_success(
                correlation,
                f"Retrieved {len(participations)} room participations for user {user_id}"
            )

            return [WebSocketRoomParticipantResponse.model_validate(p) for p in participations]

        except Exception as e:
            await self.handle_service_error(e, "get_user_rooms", {
                "user_id": user_id,
                "active_only": active_only
            })

    async def update_participant_activity(
        self,
        room_id: int,
        user_id: int,
        current_user: Optional[User] = None
    ) -> bool:
        """
        Update participant last seen timestamp.

        Performance Metrics:
        - Target response time: <100ms for activity updates
        - Optimized single-field update query
        - Cache invalidation for real-time consistency

        Args:
            room_id: Room ID
            user_id: User ID
            current_user: Current authenticated user

        Returns:
            True if update successful, False otherwise

        Raises:
            ServiceError: If update operation fails
        """
        correlation = self.log_operation_start("update_participant_activity",
                                             room_id=room_id,
                                             user_id=user_id)

        try:
            repository = await self._get_repository()
            success = await repository.update_participant_activity(room_id, user_id)

            if success:
                self.log_operation_success(
                    correlation,
                    f"Updated participant activity: room {room_id}, user {user_id}"
                )
            else:
                logger.warning(f"Failed to update participant activity: room {room_id}, user {user_id}")

            return success

        except Exception as e:
            await self.handle_service_error(e, "update_participant_activity", {
                "room_id": room_id,
                "user_id": user_id
            })

    async def bulk_remove_participants(
        self,
        room_id: int,
        user_ids: List[int],
        current_user: Optional[User] = None
    ) -> int:
        """
        Remove multiple participants from a room in bulk.

        Performance Metrics:
        - Target response time: <300ms for bulk operations
        - Batch processing for >100 participants
        - Atomic transaction for consistency

        Args:
            room_id: Room ID
            user_ids: List of user IDs to remove
            current_user: Current authenticated user

        Returns:
            Number of participants removed

        Raises:
            ValidationError: If user lacks permissions for bulk removal
            ServiceError: If bulk removal fails
        """
        correlation = self.log_operation_start("bulk_remove_participants",
                                             room_id=room_id,
                                             user_count=len(user_ids))

        try:
            # Validate permissions for bulk participant removal
            if current_user:
                await self._validate_bulk_participant_removal_permissions(current_user, room_id)

            repository = await self._get_repository()
            removed_count = await repository.bulk_remove_participants(room_id, user_ids)

            # Send bulk removal notifications
            if removed_count > 0:
                await self._send_bulk_participant_removal_notifications(room_id, user_ids, current_user)

            self.log_operation_success(
                correlation,
                f"Bulk removed {removed_count} participants from room {room_id}"
            )

            return removed_count

        except Exception as e:
            await self.handle_service_error(e, "bulk_remove_participants", {
                "room_id": room_id,
                "user_count": len(user_ids)
            })

    # Helper methods for WebSocketRoomParticipantService

    async def _get_repository(self) -> WebSocketRoomParticipantRepository:
        """Get repository instance with cache manager."""
        if not self._repository_instance:
            if not self.cache_manager:
                self.cache_manager = await get_cache_manager()
            self._repository_instance = WebSocketRoomParticipantRepository(self.db, self.cache_manager)
        return self._repository_instance

    async def _validate_participant_addition_permissions(
        self,
        user: User,
        room_id: int,
        target_user_id: int,
        role: str
    ) -> None:
        """Validate user permissions for participant addition."""
        # Users can add themselves to public rooms
        if user.id == target_user_id:
            return

        # Room owners and moderators can add participants
        repository = await self._get_repository()
        user_participations = await repository.get_user_rooms(user.id)
        room_participation = next((p for p in user_participations if p.room_id == room_id), None)

        if room_participation and room_participation.role in ["owner", "moderator"]:
            return

        # Admins can add participants to any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Insufficient permissions to add participant")

    async def _validate_participant_removal_permissions(
        self,
        user: User,
        room_id: int,
        target_user_id: int
    ) -> None:
        """Validate user permissions for participant removal."""
        # Users can remove themselves
        if user.id == target_user_id:
            return

        # Room owners and moderators can remove participants
        repository = await self._get_repository()
        user_participations = await repository.get_user_rooms(user.id)
        room_participation = next((p for p in user_participations if p.room_id == room_id), None)

        if room_participation and room_participation.role in ["owner", "moderator"]:
            return

        # Admins can remove participants from any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Insufficient permissions to remove participant")

    async def _validate_room_access_permissions(self, user: User, room_id: int) -> None:
        """Validate user permissions for room access."""
        # Check if user is a participant in the room
        repository = await self._get_repository()
        participants = await repository.get_room_participants(room_id)
        user_participant = next((p for p in participants if p.user_id == user.id), None)

        if user_participant:
            return

        # Admins can access any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Access denied to room participants")

    async def _validate_user_room_access_permissions(self, user: User, target_user_id: int) -> None:
        """Validate user permissions for user room access."""
        # Users can access their own room list
        if user.id == target_user_id:
            return

        # Admins can access any user's room list
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Access denied to user room data")

    async def _validate_bulk_participant_removal_permissions(self, user: User, room_id: int) -> None:
        """Validate user permissions for bulk participant removal."""
        # Room owners can bulk remove participants
        repository = await self._get_repository()
        user_participations = await repository.get_user_rooms(user.id)
        room_participation = next((p for p in user_participations if p.room_id == room_id), None)

        if room_participation and room_participation.role == "owner":
            return

        # Admins can bulk remove participants from any room
        if has_permission(user.role, Permission.MANAGE_SYSTEM):
            return

        raise ValidationError("Insufficient permissions for bulk participant removal")

    async def _send_participant_addition_notifications(
        self,
        participant: WebSocketRoomParticipant,
        current_user: Optional[User]
    ) -> None:
        """Send notifications about participant addition."""
        try:
            # Send push notification to the added participant
            await self._send_participant_addition_push_notification(participant)

            # Notify other room participants about the new participant
            await self._notify_room_participants_about_addition(participant)

        except Exception as e:
            logger.warning(f"Failed to send participant addition notifications: {str(e)}")

    async def _send_participant_removal_notifications(
        self,
        room_id: int,
        user_id: int,
        current_user: Optional[User]
    ) -> None:
        """Send notifications about participant removal."""
        try:
            # Send push notification to the removed participant
            await self._send_participant_removal_push_notification(room_id, user_id)

            # Notify other room participants about the removal
            await self._notify_room_participants_about_removal(room_id, user_id)

        except Exception as e:
            logger.warning(f"Failed to send participant removal notifications: {str(e)}")

    async def _send_bulk_participant_removal_notifications(
        self,
        room_id: int,
        user_ids: List[int],
        current_user: Optional[User]
    ) -> None:
        """Send notifications about bulk participant removal."""
        try:
            # Send individual notifications for each removed participant
            for user_id in user_ids:
                await self._send_participant_removal_push_notification(room_id, user_id)

        except Exception as e:
            logger.warning(f"Failed to send bulk participant removal notifications: {str(e)}")

    async def _send_participant_addition_push_notification(self, participant: WebSocketRoomParticipant) -> None:
        """Send push notification about participant addition."""
        # This would integrate with push notification service
        # For now, we'll log the notification
        logger.info(f"Participant addition notification for user {participant.user_id}, room {participant.room_id}")

    async def _send_participant_removal_push_notification(self, room_id: int, user_id: int) -> None:
        """Send push notification about participant removal."""
        # This would integrate with push notification service
        # For now, we'll log the notification
        logger.info(f"Participant removal notification for user {user_id}, room {room_id}")

    async def _notify_room_participants_about_addition(self, participant: WebSocketRoomParticipant) -> None:
        """Notify other room participants about new participant addition."""
        # This would integrate with WebSocket broadcasting
        # For now, we'll log the notification
        logger.info(f"Room participant addition broadcast for room {participant.room_id}")

    async def _notify_room_participants_about_removal(self, room_id: int, user_id: int) -> None:
        """Notify other room participants about participant removal."""
        # This would integrate with WebSocket broadcasting
        # For now, we'll log the notification
        logger.info(f"Room participant removal broadcast for room {room_id}")
