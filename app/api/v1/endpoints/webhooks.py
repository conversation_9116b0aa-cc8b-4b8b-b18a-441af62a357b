"""
Webhook API endpoints for Culture Connect Backend API.

This module provides webhook handlers for payment provider notifications including:
- Paystack webhook processing with signature validation
- Payment status updates from provider events
- Transaction reconciliation and audit logging

Implements Task 4.3.1 Phase 3 requirements for webhook handling with
production-grade security and comprehensive event processing.
"""

import logging
from typing import Optional
from fastapi import APIRouter, Request, HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db_session as get_db
from app.services.payment.paystack_service import PaystackService, PaystackError
from app.services.payment.stripe_service import StripeService, StripeError
from app.services.payment.busha_service import BushaService, BushaError
from app.services.payment_service import PaymentService
from app.schemas.payment_schemas import WebhookProcessingResult
from app.models.payment import PaymentStatus
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/paystack", response_model=WebhookProcessingResult, status_code=status.HTTP_200_OK)
async def paystack_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Paystack webhook events.

    This endpoint processes payment status updates from Paystack including:
    - charge.success: Payment completed successfully
    - charge.failed: Payment failed
    - transfer.success: Payout completed
    - transfer.failed: Payout failed

    **Security**: Validates webhook signature for authenticity
    """
    correlation = correlation_id.get()

    try:
        # Get raw payload and signature
        raw_payload = await request.body()
        signature = request.headers.get("x-paystack-signature", "")

        if not signature:
            logger.warning(
                f"Paystack webhook received without signature",
                extra={"correlation_id": correlation}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing webhook signature"
            )

        # Parse JSON payload
        try:
            event_data = await request.json()
        except Exception as e:
            logger.error(
                f"Invalid JSON in Paystack webhook",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid JSON payload"
            )

        logger.info(
            f"Received Paystack webhook",
            extra={
                "correlation_id": correlation,
                "event_type": event_data.get("event"),
                "reference": event_data.get("data", {}).get("reference")
            }
        )

        # Initialize Paystack service
        paystack_service = PaystackService()

        try:
            # Process webhook event
            result = await paystack_service.process_webhook_event(
                event_data=event_data,
                signature=signature,
                raw_payload=raw_payload
            )

            # Handle payment status updates
            if result.processed and result.event_type in ["charge.success", "charge.failed"]:
                await _update_payment_from_webhook(
                    event_data=event_data,
                    db=db,
                    correlation=correlation
                )

            await paystack_service.close()

            # Track metrics
            metrics_collector.increment_counter(
                "webhook_processed",
                tags={
                    "provider": "paystack",
                    "event_type": result.event_type,
                    "status": "success" if result.processed else "failed"
                }
            )

            logger.info(
                f"Paystack webhook processed successfully",
                extra={
                    "correlation_id": correlation,
                    "event_id": result.event_id,
                    "processed": result.processed
                }
            )

            return result

        except PaystackError as e:
            await paystack_service.close()
            logger.error(
                f"Paystack webhook processing error",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )

            metrics_collector.increment_counter(
                "webhook_processed",
                tags={
                    "provider": "paystack",
                    "event_type": event_data.get("event", "unknown"),
                    "status": "error"
                }
            )

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Webhook processing failed: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error processing Paystack webhook",
            extra={
                "correlation_id": correlation,
                "error": str(e)
            }
        )

        metrics_collector.increment_counter(
            "webhook_processed",
            tags={
                "provider": "paystack",
                "event_type": "unknown",
                "status": "error"
            }
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


async def _update_payment_from_webhook(
    event_data: dict,
    db: AsyncSession,
    correlation: str
) -> None:
    """
    Update payment status based on webhook event data.

    Args:
        event_data: Webhook event data
        db: Database session
        correlation: Correlation ID for logging
    """
    try:
        event_type = event_data.get("event")
        data = event_data.get("data", {})
        reference = data.get("reference")

        if not reference:
            logger.warning(
                f"Webhook event missing reference",
                extra={
                    "correlation_id": correlation,
                    "event_type": event_type
                }
            )
            return

        # Initialize payment service
        payment_service = PaymentService(db)

        # Find payment by reference
        payment = await payment_service.get_payment_by_reference(reference)

        if not payment:
            logger.warning(
                f"Payment not found for webhook reference",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "event_type": event_type
                }
            )
            return

        # Update payment status based on event
        if event_type == "charge.success":
            # Payment successful
            gateway_response = data.get("gateway_response", "Successful")

            if gateway_response == "Successful":
                await payment_service.update_payment_status(
                    payment_id=payment.id,
                    status=PaymentStatus.COMPLETED,
                    provider_reference=data.get("id"),
                    paid_at=data.get("paid_at")
                )

                logger.info(
                    f"Payment marked as completed from webhook",
                    extra={
                        "correlation_id": correlation,
                        "payment_id": payment.id,
                        "reference": reference
                    }
                )
            else:
                await payment_service.update_payment_status(
                    payment_id=payment.id,
                    status=PaymentStatus.FAILED,
                    failure_reason=gateway_response
                )

                logger.info(
                    f"Payment marked as failed from webhook",
                    extra={
                        "correlation_id": correlation,
                        "payment_id": payment.id,
                        "reference": reference,
                        "reason": gateway_response
                    }
                )

        elif event_type == "charge.failed":
            # Payment failed
            failure_reason = data.get("gateway_response", "Payment failed")

            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.FAILED,
                failure_reason=failure_reason
            )

            logger.info(
                f"Payment marked as failed from webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "reference": reference,
                    "reason": failure_reason
                }
            )

    except Exception as e:
        logger.error(
            f"Error updating payment from webhook",
            extra={
                "correlation_id": correlation,
                "reference": reference,
                "error": str(e)
            }
        )
        # Don't raise exception to avoid webhook retry loops


@router.post("/stripe", response_model=WebhookProcessingResult, status_code=status.HTTP_200_OK)
async def stripe_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Stripe webhook events.

    This endpoint processes payment status updates from Stripe including:
    - payment_intent.succeeded: Payment completed successfully
    - payment_intent.payment_failed: Payment failed
    - payment_intent.requires_action: Payment requires additional authentication
    - charge.dispute.created: Dispute created for payment

    **Security**: Validates webhook signature for authenticity
    """
    correlation = correlation_id.get()

    try:
        # Get raw payload and signature
        raw_payload = await request.body()
        signature = request.headers.get("stripe-signature", "")

        if not signature:
            logger.warning(
                f"Stripe webhook received without signature",
                extra={"correlation_id": correlation}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing webhook signature"
            )

        logger.info(
            f"Received Stripe webhook",
            extra={
                "correlation_id": correlation,
                "signature_present": bool(signature)
            }
        )

        # Initialize Stripe service
        stripe_service = StripeService()

        try:
            # Process webhook event
            result = await stripe_service.process_webhook_event(
                payload=raw_payload,
                signature=signature
            )

            # Handle payment status updates
            if result.processed and result.event_type.startswith("payment_intent."):
                await _update_payment_from_stripe_webhook(
                    event_type=result.event_type,
                    payment_intent_id=result.payment_id,  # This will be extracted from event data
                    db=db,
                    correlation=correlation
                )

            # Track metrics
            metrics_collector.increment_counter(
                "webhook_processed",
                tags={
                    "provider": "stripe",
                    "event_type": result.event_type,
                    "status": "success" if result.processed else "failed"
                }
            )

            logger.info(
                f"Stripe webhook processed successfully",
                extra={
                    "correlation_id": correlation,
                    "event_id": result.event_id,
                    "processed": result.processed
                }
            )

            return result

        except StripeError as e:
            logger.error(
                f"Stripe webhook processing error",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )

            metrics_collector.increment_counter(
                "webhook_processed",
                tags={
                    "provider": "stripe",
                    "event_type": "unknown",
                    "status": "error"
                }
            )

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Webhook processing failed: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error processing Stripe webhook",
            extra={
                "correlation_id": correlation,
                "error": str(e)
            }
        )

        metrics_collector.increment_counter(
            "webhook_processed",
            tags={
                "provider": "stripe",
                "event_type": "unknown",
                "status": "error"
            }
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


async def _update_payment_from_stripe_webhook(
    event_type: str,
    payment_intent_id: Optional[str],
    db: AsyncSession,
    correlation: str
) -> None:
    """
    Update payment status based on Stripe webhook event data.

    Args:
        event_type: Stripe webhook event type
        payment_intent_id: Stripe Payment Intent ID
        db: Database session
        correlation: Correlation ID for logging
    """
    try:
        if not payment_intent_id:
            logger.warning(
                f"Stripe webhook event missing payment_intent_id",
                extra={
                    "correlation_id": correlation,
                    "event_type": event_type
                }
            )
            return

        # Initialize payment service
        payment_service = PaymentService(db)

        # Find payment by provider reference (Payment Intent ID)
        payment = await payment_service.get_payment_by_provider_reference(payment_intent_id)

        if not payment:
            logger.warning(
                f"Payment not found for Stripe webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_intent_id": payment_intent_id,
                    "event_type": event_type
                }
            )
            return

        # Update payment status based on event
        if event_type == "payment_intent.succeeded":
            # Payment successful
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.COMPLETED,
                provider_reference=payment_intent_id
            )

            logger.info(
                f"Payment marked as completed from Stripe webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_intent_id": payment_intent_id
                }
            )

        elif event_type == "payment_intent.payment_failed":
            # Payment failed
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.FAILED,
                failure_reason="Payment failed (Stripe webhook)"
            )

            logger.info(
                f"Payment marked as failed from Stripe webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_intent_id": payment_intent_id
                }
            )

        elif event_type == "payment_intent.requires_action":
            # Payment requires additional action
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.PROCESSING
            )

            logger.info(
                f"Payment marked as requiring action from Stripe webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_intent_id": payment_intent_id
                }
            )

    except Exception as e:
        logger.error(
            f"Error updating payment from Stripe webhook",
            extra={
                "correlation_id": correlation,
                "payment_intent_id": payment_intent_id,
                "error": str(e)
            }
        )
        # Don't raise exception to avoid webhook retry loops


@router.post("/busha", response_model=WebhookProcessingResult, status_code=status.HTTP_200_OK)
async def busha_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Busha cryptocurrency webhook events.

    This endpoint processes crypto payment status updates from Busha including:
    - payment.confirmed: Crypto payment confirmed on blockchain
    - payment.completed: Crypto payment completed successfully
    - payment.failed: Crypto payment failed
    - payment.expired: Crypto payment expired

    **Security**: Validates webhook signature for authenticity
    """
    correlation = correlation_id.get()

    try:
        # Get raw payload and signature
        raw_payload = await request.body()
        signature = request.headers.get("busha-signature", "")

        if not signature:
            logger.warning(
                f"Busha webhook received without signature",
                extra={"correlation_id": correlation}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing webhook signature"
            )

        logger.info(
            f"Received Busha webhook",
            extra={
                "correlation_id": correlation,
                "signature_present": bool(signature)
            }
        )

        # Initialize Busha service
        busha_service = BushaService()

        try:
            # Process webhook event
            result = await busha_service.process_webhook_event(
                payload=raw_payload,
                signature=signature
            )

            # Handle payment status updates
            if result.processed and result.event_type.startswith("payment."):
                await _update_payment_from_busha_webhook(
                    event_type=result.event_type,
                    payment_reference=result.payment_id,  # This will be extracted from event data
                    db=db,
                    correlation=correlation
                )

            # Track metrics
            metrics_collector.record_business_event(
                f"webhook_processed_busha_{result.event_type}_{'success' if result.processed else 'failed'}"
            )

            logger.info(
                f"Busha webhook processed successfully",
                extra={
                    "correlation_id": correlation,
                    "event_id": result.event_id,
                    "processed": result.processed
                }
            )

            return result

        except BushaError as e:
            logger.error(
                f"Busha webhook processing error",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                "webhook_processed_busha_unknown_error"
            )

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Webhook processing failed: {str(e)}"
            )

        finally:
            await busha_service.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error processing Busha webhook",
            extra={
                "correlation_id": correlation,
                "error": str(e)
            }
        )

        metrics_collector.record_business_event(
            "webhook_processed_busha_unknown_error"
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


async def _update_payment_from_busha_webhook(
    event_type: str,
    payment_reference: Optional[str],
    db: AsyncSession,
    correlation: str
) -> None:
    """
    Update payment status based on Busha webhook event data.

    Args:
        event_type: Busha webhook event type
        payment_reference: Payment reference from Busha
        db: Database session
        correlation: Correlation ID for logging
    """
    try:
        if not payment_reference:
            logger.warning(
                f"Busha webhook event missing payment_reference",
                extra={
                    "correlation_id": correlation,
                    "event_type": event_type
                }
            )
            return

        # Initialize payment service
        payment_service = PaymentService(db)

        # Find payment by transaction reference
        payment = await payment_service.get_payment_by_reference(payment_reference)

        if not payment:
            logger.warning(
                f"Payment not found for Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_reference": payment_reference,
                    "event_type": event_type
                }
            )
            return

        # Update payment status based on event
        if event_type == "payment.confirmed":
            # Crypto payment confirmed on blockchain
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.PROCESSING
            )

            logger.info(
                f"Crypto payment marked as processing from Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_reference": payment_reference
                }
            )

        elif event_type == "payment.completed":
            # Crypto payment completed successfully
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.COMPLETED,
                provider_reference=payment_reference
            )

            logger.info(
                f"Crypto payment marked as completed from Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_reference": payment_reference
                }
            )

        elif event_type == "payment.failed":
            # Crypto payment failed
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.FAILED,
                failure_reason="Crypto payment failed (Busha webhook)"
            )

            logger.info(
                f"Crypto payment marked as failed from Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_reference": payment_reference
                }
            )

        elif event_type == "payment.expired":
            # Crypto payment expired
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.EXPIRED
            )

            logger.info(
                f"Crypto payment marked as expired from Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "payment_reference": payment_reference
                }
            )

    except Exception as e:
        logger.error(
            f"Error updating payment from Busha webhook",
            extra={
                "correlation_id": correlation,
                "payment_reference": payment_reference,
                "error": str(e)
            }
        )
        # Don't raise exception to avoid webhook retry loops