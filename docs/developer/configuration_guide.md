# Configuration Guide

## ⚙️ Environment Setup and Configuration

This guide provides comprehensive instructions for configuring Culture Connect's geolocation-enhanced payment system across different environments (development, staging, production).

## Environment Configuration

### **1. Core Environment Variables**

#### **Application Configuration**
```bash
# Application Settings
APP_NAME="Culture Connect Backend API"
APP_VERSION="1.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true
SECRET_KEY="your-super-secret-key-here"

# API Configuration
API_V1_PREFIX="/api/v1"
ALLOWED_HOSTS="localhost,127.0.0.1,api.cultureconnect.ng"
CORS_ORIGINS="http://localhost:3000,https://app.cultureconnect.ng"
```

#### **Database Configuration**
```bash
# PostgreSQL Database
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/cultureconnect"
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Database Migration
ALEMBIC_CONFIG="alembic.ini"
AUTO_MIGRATE=false  # Set to true only in development
```

#### **Redis Configuration**
```bash
# Redis Cache and Session Store
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""
REDIS_SSL=false
REDIS_TIMEOUT=5

# Geolocation-specific Redis databases
REDIS_GEOLOCATION_DB=1
REDIS_VPN_DETECTION_DB=2
REDIS_ANALYTICS_DB=3
REDIS_SESSION_DB=4
```

### **2. Geolocation Service Configuration**

#### **MaxMind GeoIP Configuration**
```bash
# MaxMind GeoIP Database
MAXMIND_LICENSE_KEY="your_maxmind_license_key"
MAXMIND_DATABASE_PATH="/app/data/GeoLite2-Country.mmdb"
MAXMIND_DATABASE_URL="https://download.maxmind.com/app/geoip_download"
MAXMIND_AUTO_UPDATE=true
MAXMIND_UPDATE_INTERVAL_HOURS=24

# Geolocation Service Settings
GEOLOCATION_ENABLED=true
GEOLOCATION_CACHE_TTL=3600  # 1 hour
GEOLOCATION_TIMEOUT_SECONDS=5.0
GEOLOCATION_FALLBACK_COUNTRY="US"
GEOLOCATION_CONFIDENCE_THRESHOLD=0.7
```

#### **VPN Detection Configuration**
```bash
# VPN Detection Service
VPN_DETECTION_ENABLED=true
VPN_CONFIDENCE_THRESHOLD=0.7
VPN_CACHE_TTL=1800  # 30 minutes
VPN_TIMEOUT_SECONDS=3.0

# VPN Detection Methods
VPN_ENABLE_ASN_ANALYSIS=true
VPN_ENABLE_HOSTING_DETECTION=true
VPN_ENABLE_IP_REPUTATION=true

# External VPN Detection APIs
IP_REPUTATION_API_KEY="your_ip_reputation_api_key"
IP_REPUTATION_API_URL="https://api.abuseipdb.com/api/v2/check"
VPN_DETECTION_API_KEY="your_vpn_detection_api_key"
```

### **3. Payment Provider Configuration**

#### **Paystack Configuration**
```bash
# Paystack Settings
# TODO-PAYSTACK-API-URL=https://api.paystack.co
PAYSTACK_SECRET_KEY="sk_test_your_paystack_secret_key"
PAYSTACK_PUBLIC_KEY="pk_test_your_paystack_public_key"
PAYSTACK_WEBHOOK_SECRET="your_paystack_webhook_secret"
PAYSTACK_TIMEOUT_SECONDS=30
PAYSTACK_RETRY_ATTEMPTS=3

# Paystack Supported Countries
PAYSTACK_SUPPORTED_COUNTRIES="NG,GH,KE,ZA,UG,TZ,RW,SN,CI,BF,ML,NE"
PAYSTACK_DEFAULT_CURRENCY="NGN"
```

#### **Stripe Configuration**
```bash
# Stripe Settings
# TODO-STRIPE-API-URL=https://api.stripe.com
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"
STRIPE_TIMEOUT_SECONDS=30
STRIPE_RETRY_ATTEMPTS=3

# Stripe Supported Countries
STRIPE_SUPPORTED_COUNTRIES="US,UK,CA,AU,DE,FR,IT,ES,NL,BE,SE,NO,DK,FI,AT,CH"
STRIPE_DEFAULT_CURRENCY="USD"
```

#### **Busha Cryptocurrency Configuration**
```bash
# Busha Settings
# TODO-BUSHA-API-URL=https://api.busha.co
BUSHA_API_KEY="your_busha_api_key"
BUSHA_API_SECRET="your_busha_api_secret"
BUSHA_WEBHOOK_SECRET="your_busha_webhook_secret"
BUSHA_TIMEOUT_SECONDS=45
BUSHA_RETRY_ATTEMPTS=3

# Supported Cryptocurrencies
BUSHA_SUPPORTED_CRYPTO="BTC,ETH,USDT,USDC"
BUSHA_DEFAULT_CRYPTO="BTC"
BUSHA_CONFIRMATION_BLOCKS=3
```

### **4. Analytics and Monitoring Configuration**

#### **Analytics Configuration**
```bash
# Analytics Settings
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_FLUSH_INTERVAL_SECONDS=60

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_METRICS_INTERVAL=30
PERFORMANCE_ALERT_THRESHOLD_MS=1000

# A/B Testing
AB_TESTING_ENABLED=true
AB_TEST_DEFAULT_SPLIT=0.5
AB_TEST_MIN_SAMPLE_SIZE=100
AB_TEST_CONFIDENCE_LEVEL=0.95
```

#### **Logging Configuration**
```bash
# Logging Settings
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT="json"  # json, text
LOG_FILE_PATH="/app/logs/app.log"
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Structured Logging
ENABLE_CORRELATION_ID=true
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_LOGGING=true
LOG_SENSITIVE_DATA=false
```

#### **Sentry Configuration**
```bash
# Error Tracking
SENTRY_DSN="https://<EMAIL>/project-id"
SENTRY_ENVIRONMENT="development"
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1
SENTRY_RELEASE="culture-connect-backend@1.0.0"
```

## Environment-Specific Configurations

### **5. Development Environment**

#### **.env.development**
```bash
# Development-specific settings
ENVIRONMENT="development"
DEBUG=true
LOG_LEVEL="DEBUG"

# Local services
DATABASE_URL="postgresql+asyncpg://postgres:password@localhost:5432/cultureconnect_dev"
REDIS_URL="redis://localhost:6379/0"

# MaxMind (use free GeoLite2)
MAXMIND_LICENSE_KEY="your_free_license_key"
MAXMIND_DATABASE_PATH="./data/GeoLite2-Country.mmdb"

# Test API keys (use test/sandbox keys)
PAYSTACK_SECRET_KEY="sk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
BUSHA_API_KEY="test_api_key"

# Relaxed security for development
VPN_DETECTION_ENABLED=false
CORS_ORIGINS="*"
ALLOWED_HOSTS="*"

# Fast cache expiry for testing
GEOLOCATION_CACHE_TTL=300  # 5 minutes
VPN_CACHE_TTL=300  # 5 minutes
```

#### **Docker Compose for Development**
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/cultureconnect_dev
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - .:/app
      - ./data:/app/data
    depends_on:
      - db
      - redis

  db:
    image: postgis/postgis:13-3.1
    environment:
      - POSTGRES_DB=cultureconnect_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### **6. Staging Environment**

#### **.env.staging**
```bash
# Staging-specific settings
ENVIRONMENT="staging"
DEBUG=false
LOG_LEVEL="INFO"

# Staging database
DATABASE_URL="postgresql+asyncpg://user:<EMAIL>:5432/cultureconnect_staging"
REDIS_URL="redis://staging-redis.cultureconnect.ng:6379/0"

# Production-like MaxMind setup
MAXMIND_LICENSE_KEY="your_paid_license_key"
MAXMIND_DATABASE_PATH="/app/data/GeoIP2-Country.mmdb"
MAXMIND_AUTO_UPDATE=true

# Staging API keys
PAYSTACK_SECRET_KEY="sk_test_staging_..."
STRIPE_SECRET_KEY="sk_test_staging_..."
BUSHA_API_KEY="staging_api_key"

# Enable all features for testing
VPN_DETECTION_ENABLED=true
ANALYTICS_ENABLED=true
AB_TESTING_ENABLED=true

# Production-like cache settings
GEOLOCATION_CACHE_TTL=3600  # 1 hour
VPN_CACHE_TTL=1800  # 30 minutes

# Staging-specific hosts
ALLOWED_HOSTS="staging-api.cultureconnect.ng"
CORS_ORIGINS="https://staging-app.cultureconnect.ng"
```

### **7. Production Environment**

#### **.env.production**
```bash
# Production settings
ENVIRONMENT="production"
DEBUG=false
LOG_LEVEL="WARNING"

# Production database with connection pooling
DATABASE_URL="postgresql+asyncpg://user:<EMAIL>:5432/cultureconnect"
DATABASE_POOL_SIZE=50
DATABASE_MAX_OVERFLOW=100

# Production Redis cluster
REDIS_URL="redis://prod-redis.cultureconnect.ng:6379/0"
REDIS_PASSWORD="secure_redis_password"
REDIS_SSL=true

# Production MaxMind
MAXMIND_LICENSE_KEY="your_production_license_key"
MAXMIND_DATABASE_PATH="/app/data/GeoIP2-Country.mmdb"
MAXMIND_AUTO_UPDATE=true

# Production API keys
PAYSTACK_SECRET_KEY="sk_live_..."
STRIPE_SECRET_KEY="sk_live_..."
BUSHA_API_KEY="production_api_key"

# Full feature enablement
VPN_DETECTION_ENABLED=true
ANALYTICS_ENABLED=true
AB_TESTING_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Production cache settings
GEOLOCATION_CACHE_TTL=7200  # 2 hours
VPN_CACHE_TTL=3600  # 1 hour

# Strict security
ALLOWED_HOSTS="api.cultureconnect.ng"
CORS_ORIGINS="https://app.cultureconnect.ng,https://admin.cultureconnect.ng"

# Production monitoring
SENTRY_DSN="https://<EMAIL>/project-id"
SENTRY_ENVIRONMENT="production"
SENTRY_TRACES_SAMPLE_RATE=0.01  # Lower sampling in production
```

## Service Configuration

### **8. MaxMind GeoIP Setup**

#### **Database Download Script**
```bash
#!/bin/bash
# scripts/download_maxmind_db.sh

set -e

MAXMIND_LICENSE_KEY="${MAXMIND_LICENSE_KEY}"
DATABASE_PATH="${MAXMIND_DATABASE_PATH:-./data/GeoLite2-Country.mmdb}"
DATABASE_DIR=$(dirname "$DATABASE_PATH")

if [ -z "$MAXMIND_LICENSE_KEY" ]; then
    echo "Error: MAXMIND_LICENSE_KEY environment variable is required"
    exit 1
fi

# Create data directory
mkdir -p "$DATABASE_DIR"

# Download GeoLite2 Country database
echo "Downloading MaxMind GeoLite2 Country database..."
curl -L "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=${MAXMIND_LICENSE_KEY}&suffix=tar.gz" \
    -o "${DATABASE_DIR}/GeoLite2-Country.tar.gz"

# Extract database
cd "$DATABASE_DIR"
tar -xzf GeoLite2-Country.tar.gz
mv GeoLite2-Country_*/GeoLite2-Country.mmdb .
rm -rf GeoLite2-Country_* GeoLite2-Country.tar.gz

echo "MaxMind database downloaded to: $DATABASE_PATH"
```

#### **Automatic Update Configuration**
```python
# app/core/geolocation/maxmind_updater.py
import asyncio
import aiohttp
import tarfile
import os
from datetime import datetime, timedelta
from app.core.config import settings

class MaxMindUpdater:
    """Automatic MaxMind database updater."""

    def __init__(self):
        self.license_key = settings.MAXMIND_LICENSE_KEY
        self.database_path = settings.MAXMIND_DATABASE_PATH
        self.update_interval = timedelta(hours=settings.MAXMIND_UPDATE_INTERVAL_HOURS)

    async def check_and_update(self):
        """Check if database needs updating and update if necessary."""
        if not self.needs_update():
            return False

        try:
            await self.download_database()
            return True
        except Exception as e:
            logger.error(f"MaxMind database update failed: {e}")
            return False

    def needs_update(self) -> bool:
        """Check if database needs updating."""
        if not os.path.exists(self.database_path):
            return True

        file_age = datetime.now() - datetime.fromtimestamp(
            os.path.getmtime(self.database_path)
        )

        return file_age > self.update_interval

    async def download_database(self):
        """Download and extract MaxMind database."""
        url = (
            f"https://download.maxmind.com/app/geoip_download"
            f"?edition_id=GeoLite2-Country"
            f"&license_key={self.license_key}"
            f"&suffix=tar.gz"
        )

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    await self.extract_database(content)
                else:
                    raise Exception(f"Download failed: {response.status}")

    async def extract_database(self, content: bytes):
        """Extract database from downloaded tar.gz."""
        import tempfile

        with tempfile.NamedTemporaryFile(suffix='.tar.gz') as tmp_file:
            tmp_file.write(content)
            tmp_file.flush()

            with tarfile.open(tmp_file.name, 'r:gz') as tar:
                # Find the .mmdb file in the archive
                for member in tar.getmembers():
                    if member.name.endswith('.mmdb'):
                        # Extract to temporary location first
                        tar.extract(member, path=tempfile.gettempdir())

                        # Move to final location
                        temp_path = os.path.join(tempfile.gettempdir(), member.name)
                        os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
                        os.rename(temp_path, self.database_path)
                        break
```

### **9. Redis Configuration**

#### **Redis Cluster Setup**
```bash
# redis.conf for production
bind 0.0.0.0
port 6379
requirepass your_secure_redis_password

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log
```

#### **Redis Connection Pool Configuration**
```python
# app/core/cache.py
import redis.asyncio as redis
from app.core.config import settings

class RedisManager:
    """Redis connection manager with multiple databases."""

    def __init__(self):
        self.pools = {}

    def get_pool(self, db: int = 0) -> redis.ConnectionPool:
        """Get Redis connection pool for specific database."""
        if db not in self.pools:
            self.pools[db] = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                db=db,
                password=settings.REDIS_PASSWORD,
                ssl=settings.REDIS_SSL,
                max_connections=20,
                retry_on_timeout=True,
                socket_timeout=settings.REDIS_TIMEOUT
            )
        return self.pools[db]

    def get_client(self, db: int = 0) -> redis.Redis:
        """Get Redis client for specific database."""
        return redis.Redis(connection_pool=self.get_pool(db))

# Global Redis manager
redis_manager = RedisManager()

# Convenience functions
def get_redis_client(db: int = 0) -> redis.Redis:
    """Get Redis client for specific use case."""
    return redis_manager.get_client(db)

def get_geolocation_cache() -> redis.Redis:
    """Get Redis client for geolocation caching."""
    return get_redis_client(settings.REDIS_GEOLOCATION_DB)

def get_vpn_cache() -> redis.Redis:
    """Get Redis client for VPN detection caching."""
    return get_redis_client(settings.REDIS_VPN_DETECTION_DB)
```

## Deployment Configuration

### **10. Kubernetes Configuration**

#### **ConfigMap for Environment Variables**
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: culture-connect-config
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"

  # Database
  DATABASE_POOL_SIZE: "50"
  DATABASE_MAX_OVERFLOW: "100"

  # Geolocation
  GEOLOCATION_ENABLED: "true"
  GEOLOCATION_CACHE_TTL: "7200"
  GEOLOCATION_TIMEOUT_SECONDS: "5.0"

  # VPN Detection
  VPN_DETECTION_ENABLED: "true"
  VPN_CONFIDENCE_THRESHOLD: "0.7"
  VPN_CACHE_TTL: "3600"

  # Analytics
  ANALYTICS_ENABLED: "true"
  ANALYTICS_RETENTION_DAYS: "90"
  PERFORMANCE_MONITORING_ENABLED: "true"
```

#### **Secret for Sensitive Configuration**
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: culture-connect-secrets
type: Opaque
stringData:
  DATABASE_URL: "postgresql+asyncpg://user:password@db:5432/cultureconnect"
  REDIS_URL: "redis://redis:6379/0"
  SECRET_KEY: "your-super-secret-key"

  # MaxMind
  MAXMIND_LICENSE_KEY: "your_maxmind_license_key"

  # Payment Providers
  PAYSTACK_SECRET_KEY: "sk_live_..."
  STRIPE_SECRET_KEY: "sk_live_..."
  BUSHA_API_KEY: "production_api_key"

  # Monitoring
  SENTRY_DSN: "https://<EMAIL>/project"
```

### **11. Health Checks and Monitoring**

#### **Health Check Configuration**
```python
# app/core/health.py
from app.services.geolocation_service import get_geolocation_service
from app.core.cache import get_redis_client

async def check_geolocation_health() -> dict:
    """Check geolocation service health."""
    try:
        # Test MaxMind database
        service = get_geolocation_service()
        result = await service.detect_country_from_ip("*******")

        return {
            "status": "healthy",
            "database_accessible": True,
            "test_detection_time_ms": result.detection_time_ms
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "database_accessible": False
        }

async def check_redis_health() -> dict:
    """Check Redis connectivity."""
    try:
        redis_client = get_redis_client()
        await redis_client.ping()

        return {
            "status": "healthy",
            "redis_accessible": True
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "redis_accessible": False
        }
```

---

## 📚 Related Developer Guides

- **[Integration Guide](geolocation_integration.md)** - Complete integration walkthrough with code examples
- **[API Examples](api_examples.md)** - Comprehensive code examples and testing strategies
- **[User Guides](../user_guide/geolocation_features.md)** - User-facing documentation
- **[API Documentation](../README.md)** - Complete API reference

---

**Last Updated**: January 15, 2024
**Version**: 2.4.3
**Part of**: Culture Connect Backend API Documentation v2.4.3
