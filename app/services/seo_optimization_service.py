"""
SEO Optimization Service for Culture Connect Backend API.

This module provides comprehensive SEO analysis and optimization functionality including:
- Content analysis algorithms and keyword optimization
- SEO scoring calculations with configurable weights
- Meta tag validation and optimization recommendations
- Content quality assessment and readability analysis
- Keyword density analysis and competitor keyword research

Implements Task 3.2.2 requirements for SEO optimization tools with
production-grade algorithms, comprehensive analysis, and actionable insights.
"""

import logging
import re
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple, Set
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, NotFoundError, ValidationError
from app.models.marketplace_optimization import SEOAnalysis
from app.models.service import Service
from app.repositories.seo_analysis_repository import SEOAnalysisRepository
from app.repositories.service_repository import ServiceRepository
from app.schemas.marketplace_optimization import SEOAnalysisCreate, SEOAnalysisUpdate


logger = logging.getLogger(__name__)


class SEOOptimizationService(BaseService[SEOAnalysis, SEOAnalysisRepository]):
    """
    SEO optimization service for comprehensive content analysis and optimization.

    Provides production-grade algorithms for:
    - Content analysis and keyword optimization
    - SEO scoring with configurable weights and thresholds
    - Meta tag validation and optimization
    - Content quality assessment and readability analysis
    - Competitive keyword research and gap analysis
    """

    def __init__(self, db: AsyncSession):
        """Initialize SEO optimization service."""
        super().__init__(SEOAnalysisRepository, SEOAnalysis, db_session=db)
        self.service_repository = ServiceRepository(db)

        # SEO scoring weights (configurable)
        self.scoring_weights = {
            "title_optimization": 0.20,
            "description_quality": 0.18,
            "keyword_optimization": 0.22,
            "content_completeness": 0.15,
            "media_quality": 0.15,
            "readability": 0.10
        }

        # Common stop words for keyword analysis
        self.stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'you', 'your', 'this', 'these', 'those'
        }

    async def analyze_service_seo(
        self,
        service_id: int,
        force_refresh: bool = False
    ) -> SEOAnalysis:
        """
        Perform comprehensive SEO analysis for a service.

        Args:
            service_id: Service ID to analyze
            force_refresh: Whether to force new analysis even if recent one exists

        Returns:
            SEOAnalysis: Complete SEO analysis results

        Raises:
            NotFoundError: If service not found
            ServiceError: If analysis fails
        """
        correlation = self.log_operation_start(
            "analyze_service_seo",
            service_id=service_id,
            force_refresh=force_refresh
        )

        try:
            # Get service data
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Check for existing recent analysis
            if not force_refresh:
                existing_analysis = await self.repository.get_latest_by_service(service_id)
                if existing_analysis and self._is_analysis_recent(existing_analysis):
                    self.log_operation_success(correlation, "Returning existing recent SEO analysis")
                    return existing_analysis

            # Perform comprehensive SEO analysis
            analysis_data = await self._perform_seo_analysis(service)

            # Create or update SEO analysis record
            seo_analysis = await self._save_seo_analysis(service_id, service.vendor_id, analysis_data)

            self.log_operation_success(
                correlation,
                f"Completed SEO analysis for service {service_id} with score {seo_analysis.overall_seo_score}"
            )

            return seo_analysis

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "analyze_service_seo",
                {"service_id": service_id, "force_refresh": force_refresh}
            )

    async def get_keyword_recommendations(
        self,
        service_id: int,
        target_keywords: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get keyword optimization recommendations for a service.

        Args:
            service_id: Service ID to analyze
            target_keywords: Optional list of target keywords to analyze

        Returns:
            Dict[str, Any]: Keyword recommendations and analysis

        Raises:
            NotFoundError: If service not found
            ServiceError: If analysis fails
        """
        correlation = self.log_operation_start(
            "get_keyword_recommendations",
            service_id=service_id,
            target_keywords_count=len(target_keywords) if target_keywords else 0
        )

        try:
            # Get service data
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Get latest SEO analysis
            seo_analysis = await self.repository.get_latest_by_service(service_id)

            # Extract content for analysis
            content = self._extract_service_content(service)

            # Analyze current keywords
            current_keywords = self._extract_keywords(content)
            keyword_density = self._calculate_keyword_density(content, current_keywords)

            # Generate keyword recommendations
            recommendations = await self._generate_keyword_recommendations(
                service, current_keywords, keyword_density, target_keywords
            )

            # Analyze keyword gaps
            keyword_gaps = self._identify_keyword_gaps(
                current_keywords, recommendations.get("suggested_keywords", [])
            )

            result = {
                "service_id": service_id,
                "current_keywords": {
                    "primary_keywords": current_keywords[:10],
                    "keyword_density": keyword_density,
                    "total_keywords": len(current_keywords)
                },
                "recommendations": recommendations,
                "keyword_gaps": keyword_gaps,
                "optimization_opportunities": self._identify_optimization_opportunities(
                    seo_analysis, current_keywords, keyword_density
                ),
                "analysis_date": datetime.now(timezone.utc).isoformat()
            }

            self.log_operation_success(
                correlation,
                f"Generated keyword recommendations for service {service_id}"
            )

            return result

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_keyword_recommendations",
                {"service_id": service_id, "target_keywords": target_keywords}
            )

    async def get_content_optimization_suggestions(
        self,
        service_id: int
    ) -> Dict[str, Any]:
        """
        Get content optimization suggestions for a service.

        Args:
            service_id: Service ID to analyze

        Returns:
            Dict[str, Any]: Content optimization suggestions

        Raises:
            NotFoundError: If service not found
            ServiceError: If analysis fails
        """
        correlation = self.log_operation_start(
            "get_content_optimization_suggestions",
            service_id=service_id
        )

        try:
            # Get service data
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Get latest SEO analysis
            seo_analysis = await self.repository.get_latest_by_service(service_id)

            # Analyze content quality
            content_analysis = self._analyze_content_quality(service)

            # Generate optimization suggestions
            suggestions = {
                "title_optimization": self._get_title_suggestions(service),
                "description_optimization": self._get_description_suggestions(service),
                "content_structure": self._get_content_structure_suggestions(service),
                "readability_improvements": self._get_readability_suggestions(service),
                "meta_tag_optimization": self._get_meta_tag_suggestions(service),
                "media_optimization": self._get_media_suggestions(service)
            }

            # Calculate priority scores for each suggestion category
            priority_scores = self._calculate_suggestion_priorities(seo_analysis, suggestions)

            result = {
                "service_id": service_id,
                "content_analysis": content_analysis,
                "optimization_suggestions": suggestions,
                "priority_scores": priority_scores,
                "implementation_order": sorted(
                    priority_scores.items(),
                    key=lambda x: x[1],
                    reverse=True
                ),
                "analysis_date": datetime.now(timezone.utc).isoformat()
            }

            self.log_operation_success(
                correlation,
                f"Generated content optimization suggestions for service {service_id}"
            )

            return result

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_content_optimization_suggestions",
                {"service_id": service_id}
            )

    async def _perform_seo_analysis(self, service: Service) -> Dict[str, Any]:
        """Perform comprehensive SEO analysis on service content."""
        # Extract content for analysis
        content = self._extract_service_content(service)

        # Analyze title optimization
        title_score = self._analyze_title_optimization(service.title)

        # Analyze description quality
        description_score = self._analyze_description_quality(service.description)

        # Analyze keyword optimization
        keywords = self._extract_keywords(content)
        keyword_density = self._calculate_keyword_density(content, keywords)
        keyword_score = self._calculate_keyword_score(keywords, keyword_density)

        # Analyze content completeness
        completeness_score = self._analyze_content_completeness(service)

        # Analyze media quality
        media_score = self._analyze_media_quality(service)

        # Analyze readability
        readability_score = self._calculate_readability_score(content)

        # Calculate overall SEO score
        overall_score = self._calculate_overall_seo_score({
            "title_score": title_score,
            "description_score": description_score,
            "keyword_score": keyword_score,
            "completeness_score": completeness_score,
            "media_score": media_score,
            "readability_score": readability_score
        })

        return {
            "overall_seo_score": overall_score,
            "title_score": title_score,
            "description_score": description_score,
            "media_score": media_score,
            "keyword_score": keyword_score,
            "completeness_score": completeness_score,
            "primary_keywords": keywords[:10],
            "keyword_density": keyword_density,
            "missing_keywords": self._identify_missing_keywords(service, keywords),
            "content_quality_metrics": self._get_content_quality_metrics(content),
            "readability_score": readability_score,
            "meta_title_analysis": self._analyze_meta_title(service.title),
            "meta_description_analysis": self._analyze_meta_description(service.description),
            "analysis_version": "1.0"
        }

    def _extract_service_content(self, service: Service) -> str:
        """Extract all textual content from service for analysis."""
        content_parts = []

        if service.title:
            content_parts.append(service.title)
        if service.description:
            content_parts.append(service.description)
        if service.highlights:
            content_parts.extend(service.highlights)
        if service.inclusions:
            content_parts.extend(service.inclusions)
        if service.exclusions:
            content_parts.extend(service.exclusions)
        if service.requirements:
            content_parts.extend(service.requirements)

        return " ".join(content_parts).lower()

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from content using NLP techniques."""
        # Clean and tokenize content
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())

        # Remove stop words
        keywords = [word for word in words if word not in self.stop_words]

        # Count frequency and return top keywords
        word_freq = {}
        for word in keywords:
            word_freq[word] = word_freq.get(word, 0) + 1

        # Sort by frequency and return top keywords
        sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_keywords if freq >= 2]

    def _calculate_keyword_density(self, content: str, keywords: List[str]) -> Dict[str, float]:
        """Calculate keyword density for top keywords."""
        total_words = len(content.split())
        keyword_density = {}

        for keyword in keywords[:20]:  # Top 20 keywords
            count = content.lower().count(keyword.lower())
            density = (count / total_words) * 100 if total_words > 0 else 0
            keyword_density[keyword] = round(density, 2)

        return keyword_density

    def _analyze_title_optimization(self, title: str) -> Decimal:
        """Analyze title optimization and return score (0-100)."""
        if not title:
            return Decimal("0.00")

        score = 0

        # Length optimization (50-60 characters is ideal)
        title_length = len(title)
        if 50 <= title_length <= 60:
            score += 30
        elif 40 <= title_length <= 70:
            score += 20
        elif title_length > 0:
            score += 10

        # Keyword presence (check if title contains relevant keywords)
        title_lower = title.lower()
        cultural_keywords = ['cultural', 'culture', 'heritage', 'traditional', 'authentic', 'local']
        for keyword in cultural_keywords:
            if keyword in title_lower:
                score += 15
                break

        # Uniqueness and appeal
        if any(word in title_lower for word in ['experience', 'tour', 'journey', 'adventure']):
            score += 15

        # Avoid keyword stuffing
        words = title_lower.split()
        unique_words = set(words)
        if len(words) > 0 and len(unique_words) / len(words) > 0.7:
            score += 20

        # Call to action or emotional appeal
        if any(word in title_lower for word in ['discover', 'explore', 'immerse', 'authentic']):
            score += 20

        return Decimal(str(min(100, score)))

    def _analyze_description_quality(self, description: str) -> Decimal:
        """Analyze description quality and return score (0-100)."""
        if not description:
            return Decimal("0.00")

        score = 0

        # Length optimization (150-300 words is ideal)
        word_count = len(description.split())
        if 150 <= word_count <= 300:
            score += 25
        elif 100 <= word_count <= 400:
            score += 15
        elif word_count > 0:
            score += 5

        # Keyword presence and density
        description_lower = description.lower()
        cultural_keywords = ['cultural', 'culture', 'heritage', 'traditional', 'authentic', 'local', 'experience']
        keyword_count = sum(1 for keyword in cultural_keywords if keyword in description_lower)
        if keyword_count >= 3:
            score += 20
        elif keyword_count >= 1:
            score += 10

        # Readability and structure
        sentences = description.split('.')
        if len(sentences) >= 3:
            score += 15

        # Call to action
        cta_words = ['book', 'reserve', 'join', 'discover', 'explore', 'experience']
        if any(word in description_lower for word in cta_words):
            score += 15

        # Unique selling proposition
        usp_words = ['unique', 'exclusive', 'authentic', 'genuine', 'original', 'special']
        if any(word in description_lower for word in usp_words):
            score += 15

        # Avoid excessive repetition
        words = description_lower.split()
        unique_words = set(words)
        if len(words) > 0 and len(unique_words) / len(words) > 0.6:
            score += 10

        return Decimal(str(min(100, score)))

    def _calculate_keyword_score(self, keywords: List[str], keyword_density: Dict[str, float]) -> Decimal:
        """Calculate keyword optimization score."""
        if not keywords:
            return Decimal("0.00")

        score = 0

        # Keyword diversity
        if len(keywords) >= 10:
            score += 25
        elif len(keywords) >= 5:
            score += 15
        elif len(keywords) >= 1:
            score += 5

        # Optimal keyword density (1-3% is ideal)
        optimal_density_count = 0
        for keyword, density in keyword_density.items():
            if 1.0 <= density <= 3.0:
                optimal_density_count += 1

        if optimal_density_count >= 5:
            score += 30
        elif optimal_density_count >= 3:
            score += 20
        elif optimal_density_count >= 1:
            score += 10

        # Long-tail keywords (3+ words)
        long_tail_count = sum(1 for keyword in keywords if len(keyword.split()) >= 3)
        if long_tail_count >= 3:
            score += 25
        elif long_tail_count >= 1:
            score += 15

        # Relevant cultural keywords
        cultural_keywords = ['cultural', 'heritage', 'traditional', 'authentic', 'local']
        cultural_count = sum(1 for keyword in keywords if any(ck in keyword for ck in cultural_keywords))
        if cultural_count >= 3:
            score += 20
        elif cultural_count >= 1:
            score += 10

        return Decimal(str(min(100, score)))

    def _analyze_content_completeness(self, service: Service) -> Decimal:
        """Analyze content completeness and return score."""
        score = 0

        # Required fields
        if service.title:
            score += 15
        if service.description:
            score += 20
        if service.location:
            score += 10
        if service.base_price:
            score += 10

        # Optional but valuable fields
        if service.highlights:
            score += 10
        if service.inclusions:
            score += 10
        if service.requirements:
            score += 5
        if service.duration_hours:
            score += 5
        if service.max_participants:
            score += 5
        if service.languages:
            score += 10

        return Decimal(str(min(100, score)))

    def _analyze_media_quality(self, service: Service) -> Decimal:
        """Analyze media quality and return score."""
        score = 0

        # Check if service has images (through relationship)
        # This would need to be enhanced with actual image analysis
        if hasattr(service, 'images') and service.images:
            image_count = len(service.images)
            if image_count >= 5:
                score += 40
            elif image_count >= 3:
                score += 30
            elif image_count >= 1:
                score += 20

            # Check for primary image
            has_primary = any(img.is_primary for img in service.images)
            if has_primary:
                score += 20

            # Image optimization (would need actual image analysis)
            score += 20  # Placeholder for image quality analysis

        # Alt text and descriptions (placeholder)
        score += 20  # Would analyze image alt texts and descriptions

        return Decimal(str(min(100, score)))

    def _calculate_readability_score(self, content: str) -> Decimal:
        """Calculate content readability score using simplified metrics."""
        if not content:
            return Decimal("0.00")

        sentences = content.split('.')
        words = content.split()

        if not sentences or not words:
            return Decimal("0.00")

        # Average words per sentence
        avg_words_per_sentence = len(words) / len(sentences)

        # Average syllables per word (simplified estimation)
        total_syllables = sum(self._count_syllables(word) for word in words)
        avg_syllables_per_word = total_syllables / len(words) if words else 0

        # Simplified Flesch Reading Ease formula
        flesch_score = 206.835 - (1.015 * avg_words_per_sentence) - (84.6 * avg_syllables_per_word)

        # Convert to 0-100 scale
        readability_score = max(0, min(100, flesch_score))

        return Decimal(str(round(readability_score, 2)))

    def _count_syllables(self, word: str) -> int:
        """Estimate syllable count for a word."""
        word = word.lower()
        vowels = 'aeiouy'
        syllable_count = 0
        prev_was_vowel = False

        for char in word:
            if char in vowels:
                if not prev_was_vowel:
                    syllable_count += 1
                prev_was_vowel = True
            else:
                prev_was_vowel = False

        # Handle silent 'e'
        if word.endswith('e'):
            syllable_count -= 1

        # Ensure at least one syllable
        return max(1, syllable_count)

    def _calculate_overall_seo_score(self, scores: Dict[str, Decimal]) -> Decimal:
        """Calculate weighted overall SEO score."""
        weighted_sum = Decimal("0.00")
        total_weight = Decimal("0.00")

        for category, weight in self.scoring_weights.items():
            score_key = f"{category.replace('_optimization', '_score')}"
            if score_key in scores:
                weighted_sum += scores[score_key] * Decimal(str(weight))
                total_weight += Decimal(str(weight))

        if total_weight > 0:
            overall_score = weighted_sum / total_weight
        else:
            overall_score = Decimal("0.00")

        return Decimal(str(round(float(overall_score), 2)))

    async def _save_seo_analysis(self, service_id: int, vendor_id: int, analysis_data: Dict[str, Any]) -> SEOAnalysis:
        """Save SEO analysis to database."""
        create_data = SEOAnalysisCreate(
            service_id=service_id,
            vendor_id=vendor_id,
            **analysis_data
        )

        return await self.create(create_data.model_dump())

    def _is_analysis_recent(self, analysis: SEOAnalysis, hours: int = 24) -> bool:
        """Check if analysis is recent enough to avoid re-analysis."""
        if not analysis.analysis_date:
            return False

        time_diff = datetime.now(timezone.utc) - analysis.analysis_date.replace(tzinfo=timezone.utc)
        return time_diff.total_seconds() < (hours * 3600)

    def _identify_missing_keywords(self, service: Service, current_keywords: List[str]) -> List[str]:
        """Identify missing keywords that should be included."""
        # Industry-specific keywords for cultural services
        suggested_keywords = [
            'cultural experience', 'heritage tour', 'traditional culture',
            'authentic local', 'cultural immersion', 'historical site',
            'local guide', 'cultural learning', 'traditional art',
            'cultural workshop', 'heritage walk', 'local customs'
        ]

        # Filter out keywords already present
        current_keywords_lower = [k.lower() for k in current_keywords]
        missing_keywords = []

        for keyword in suggested_keywords:
            if not any(k in keyword.lower() for k in current_keywords_lower):
                missing_keywords.append(keyword)

        return missing_keywords[:10]  # Return top 10 missing keywords

    def _get_content_quality_metrics(self, content: str) -> Dict[str, Any]:
        """Get detailed content quality metrics."""
        words = content.split()
        sentences = content.split('.')

        return {
            "word_count": len(words),
            "sentence_count": len(sentences),
            "avg_words_per_sentence": round(len(words) / len(sentences), 2) if sentences else 0,
            "character_count": len(content),
            "paragraph_count": len(content.split('\n\n')) if content else 0
        }

    def _analyze_meta_title(self, title: str) -> Dict[str, Any]:
        """Analyze meta title optimization."""
        return {
            "length": len(title) if title else 0,
            "optimal_length": 50 <= len(title) <= 60 if title else False,
            "keyword_present": any(k in title.lower() for k in ['cultural', 'heritage', 'traditional']) if title else False,
            "unique": True,  # Would check against database for uniqueness
            "compelling": any(k in title.lower() for k in ['discover', 'explore', 'authentic']) if title else False
        }

    def _analyze_meta_description(self, description: str) -> Dict[str, Any]:
        """Analyze meta description optimization."""
        return {
            "length": len(description) if description else 0,
            "optimal_length": 120 <= len(description) <= 160 if description else False,
            "keyword_present": any(k in description.lower() for k in ['cultural', 'heritage', 'experience']) if description else False,
            "call_to_action": any(k in description.lower() for k in ['book', 'reserve', 'discover']) if description else False,
            "unique": True  # Would check against database for uniqueness
        }
