"""
Email Notification Service for Culture Connect Backend API.

This module provides comprehensive email notification functionality including:
- Authentication workflow email notifications (verification, password reset)
- User management email notifications (profile updates, security alerts)
- Integration with existing authentication services
- Template-based notification composition
- Preference-aware email sending

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
seamless integration with existing authentication infrastructure and user management.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError
from app.services.email_template_service import EmailTemplateService
from app.services.email_delivery_service import EmailDeliveryService
from app.services.email_preference_service import EmailPreferenceService
from app.services.email_queue_service import EmailQueueService
from app.models.email_models import EmailTemplateCategory
from app.schemas.email_schemas import (
    EmailVerificationRequest, EmailVerificationResponse,
    PasswordResetEmailRequest, PasswordResetEmailResponse,
    EmailSendRequest
)
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class EmailNotificationService:
    """
    Email notification service for authentication and user management workflows.
    
    Provides high-level email notification functionality with template integration,
    preference checking, and seamless integration with authentication services.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email notification service."""
        self.db = db
        self.template_service = EmailTemplateService(db)
        self.delivery_service = EmailDeliveryService(db)
        self.preference_service = EmailPreferenceService(db)
        self.queue_service = EmailQueueService(db)

    async def send_verification_email(
        self,
        request: EmailVerificationRequest
    ) -> EmailVerificationResponse:
        """
        Send email verification notification.

        Args:
            request: Email verification request data

        Returns:
            EmailVerificationResponse: Verification email response

        Raises:
            ValidationError: If request data is invalid
            ServiceError: If email sending fails
        """
        try:
            # Check user email preferences
            can_send = await self.preference_service.can_send_email(
                user_id=request.user_id,
                email_category=EmailTemplateCategory.VERIFICATION
            )
            
            if not can_send:
                logger.warning(
                    f"User has opted out of verification emails",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": request.user_id,
                        "email": request.email
                    }
                )
                # Still send verification emails as they are essential
                # but log the preference conflict

            # Get verification email template
            template = await self._get_template_by_category(EmailTemplateCategory.VERIFICATION)
            
            # Prepare template variables
            verification_url = f"{settings.FRONTEND_URL}/verify-email?token={request.verification_token}"
            template_vars = {
                "user_name": request.user_name,
                "verification_url": verification_url,
                "verification_token": request.verification_token,
                "expires_at": (datetime.utcnow() + timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S UTC")
            }

            # Render template
            rendered = await self.template_service.render_template(
                template_id=template.id,
                variables=template_vars
            )

            # Send email
            email_request = EmailSendRequest(
                recipient_email=request.email,
                subject=rendered["subject"],
                body=rendered["body"],
                html_body=rendered["html_body"]
            )

            send_response = await self.delivery_service.send_email(
                email_request=email_request,
                user_id=request.user_id,
                template_id=template.id
            )

            logger.info(
                f"Verification email sent successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": request.user_id,
                    "email": request.email,
                    "delivery_id": str(send_response.delivery_id)
                }
            )

            return EmailVerificationResponse(
                user_id=request.user_id,
                email=request.email,
                delivery_id=send_response.delivery_id,
                status=send_response.status,
                message="Verification email sent successfully"
            )

        except (ValidationError, ServiceError):
            raise
        except Exception as e:
            logger.error(
                f"Error sending verification email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": request.user_id,
                    "email": request.email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to send verification email: {str(e)}")

    async def send_password_reset_email(
        self,
        request: PasswordResetEmailRequest
    ) -> PasswordResetEmailResponse:
        """
        Send password reset notification.

        Args:
            request: Password reset email request data

        Returns:
            PasswordResetEmailResponse: Password reset email response

        Raises:
            ValidationError: If request data is invalid
            ServiceError: If email sending fails
        """
        try:
            # Check user email preferences
            can_send = await self.preference_service.can_send_email(
                user_id=request.user_id,
                email_category=EmailTemplateCategory.PASSWORD_RESET
            )
            
            if not can_send:
                logger.warning(
                    f"User has opted out of password reset emails",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": request.user_id,
                        "email": request.email
                    }
                )
                # Still send password reset emails as they are security-related

            # Get password reset email template
            template = await self._get_template_by_category(EmailTemplateCategory.PASSWORD_RESET)
            
            # Prepare template variables
            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={request.reset_token}"
            template_vars = {
                "user_name": request.user_name,
                "reset_url": reset_url,
                "reset_token": request.reset_token,
                "expires_at": (datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S UTC"),
                "ip_address": request.ip_address or "Unknown",
                "user_agent": request.user_agent or "Unknown"
            }

            # Render template
            rendered = await self.template_service.render_template(
                template_id=template.id,
                variables=template_vars
            )

            # Send email
            email_request = EmailSendRequest(
                recipient_email=request.email,
                subject=rendered["subject"],
                body=rendered["body"],
                html_body=rendered["html_body"]
            )

            send_response = await self.delivery_service.send_email(
                email_request=email_request,
                user_id=request.user_id,
                template_id=template.id
            )

            logger.info(
                f"Password reset email sent successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": request.user_id,
                    "email": request.email,
                    "delivery_id": str(send_response.delivery_id),
                    "ip_address": request.ip_address
                }
            )

            return PasswordResetEmailResponse(
                user_id=request.user_id,
                email=request.email,
                delivery_id=send_response.delivery_id,
                status=send_response.status,
                message="Password reset email sent successfully"
            )

        except (ValidationError, ServiceError):
            raise
        except Exception as e:
            logger.error(
                f"Error sending password reset email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": request.user_id,
                    "email": request.email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to send password reset email: {str(e)}")

    async def send_security_alert(
        self,
        user_id: int,
        email: str,
        user_name: str,
        alert_type: str,
        alert_details: Dict[str, Any]
    ) -> bool:
        """
        Send security alert notification.

        Args:
            user_id: User ID
            email: User email address
            user_name: User name
            alert_type: Type of security alert
            alert_details: Alert details and context

        Returns:
            bool: True if email sent successfully

        Raises:
            ServiceError: If email sending fails
        """
        try:
            # Check user email preferences
            can_send = await self.preference_service.can_send_email(
                user_id=user_id,
                email_category=EmailTemplateCategory.SECURITY
            )
            
            if not can_send:
                logger.warning(
                    f"User has opted out of security emails",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "email": email,
                        "alert_type": alert_type
                    }
                )
                # Still send security emails as they are essential

            # Get security email template
            template = await self._get_template_by_category(EmailTemplateCategory.SECURITY)
            
            # Prepare template variables
            template_vars = {
                "user_name": user_name,
                "alert_type": alert_type,
                "alert_details": alert_details,
                "timestamp": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
                "support_url": f"{settings.FRONTEND_URL}/support",
                "account_url": f"{settings.FRONTEND_URL}/account/security"
            }

            # Render template
            rendered = await self.template_service.render_template(
                template_id=template.id,
                variables=template_vars
            )

            # Send email with high priority
            await self.queue_service.enqueue_email(
                user_id=user_id,
                template_id=template.id,
                recipient_email=email,
                subject=rendered["subject"],
                body=rendered["body"],
                html_body=rendered["html_body"],
                priority=1,  # High priority for security alerts
                metadata={"alert_type": alert_type, "alert_details": alert_details}
            )

            logger.info(
                f"Security alert email queued successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "email": email,
                    "alert_type": alert_type
                }
            )

            return True

        except Exception as e:
            logger.error(
                f"Error sending security alert email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "email": email,
                    "alert_type": alert_type,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to send security alert email: {str(e)}")

    async def send_welcome_email(
        self,
        user_id: int,
        email: str,
        user_name: str
    ) -> bool:
        """
        Send welcome email to new users.

        Args:
            user_id: User ID
            email: User email address
            user_name: User name

        Returns:
            bool: True if email sent successfully

        Raises:
            ServiceError: If email sending fails
        """
        try:
            # Check user email preferences
            can_send = await self.preference_service.can_send_email(
                user_id=user_id,
                email_category=EmailTemplateCategory.NOTIFICATION
            )
            
            if not can_send:
                logger.info(
                    f"User has opted out of notification emails, skipping welcome email",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "email": email
                    }
                )
                return False

            # Get notification email template (welcome)
            template = await self._get_template_by_category(EmailTemplateCategory.NOTIFICATION)
            
            # Prepare template variables
            template_vars = {
                "user_name": user_name,
                "app_name": "Culture Connect",
                "dashboard_url": f"{settings.FRONTEND_URL}/dashboard",
                "support_url": f"{settings.FRONTEND_URL}/support",
                "preferences_url": f"{settings.FRONTEND_URL}/account/preferences"
            }

            # Render template
            rendered = await self.template_service.render_template(
                template_id=template.id,
                variables=template_vars
            )

            # Send email with normal priority
            await self.queue_service.enqueue_email(
                user_id=user_id,
                template_id=template.id,
                recipient_email=email,
                subject=rendered["subject"],
                body=rendered["body"],
                html_body=rendered["html_body"],
                priority=3,  # Normal priority
                metadata={"email_type": "welcome"}
            )

            logger.info(
                f"Welcome email queued successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "email": email
                }
            )

            return True

        except Exception as e:
            logger.error(
                f"Error sending welcome email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "email": email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to send welcome email: {str(e)}")

    async def _get_template_by_category(self, category: EmailTemplateCategory):
        """
        Get active email template by category.

        Args:
            category: Email template category

        Returns:
            EmailTemplate: Active template for the category

        Raises:
            ServiceError: If no active template found
        """
        templates_response = await self.template_service.list_templates(
            category=category,
            is_active=True,
            limit=1
        )
        
        if not templates_response.templates:
            raise ServiceError(f"No active email template found for category: {category.value}")
        
        return templates_response.templates[0]
