"""
Role-Based Access Control (RBAC) schemas for Culture Connect Backend API.

This module provides comprehensive schemas for RBAC management including:
- Role and permission management schemas
- Access control request and response schemas
- Audit logging and security event schemas
- Resource-level permission schemas

Implements Task 2.2.3 requirements for complete role-based access control
with comprehensive validation and type safety.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin
from app.core.security import UserR<PERSON>, Permission


class AccessDecision(str, Enum):
    """Access control decision types."""
    GRANTED = "granted"
    DENIED = "denied"
    ERROR = "error"


class ResourceType(str, Enum):
    """Resource types for permission checking."""
    USER = "user"
    VENDOR = "vendor"
    SERVICE = "service"
    BOOKING = "booking"
    PAYMENT = "payment"
    CAMPAIGN = "campaign"
    ANALYTICS = "analytics"
    SYSTEM = "system"


class PermissionCheck(BaseSchema):
    """
    Schema for permission checking requests.
    
    Used to validate permission check requests with optional
    resource-specific access control.
    """
    
    permission: str = Field(..., description="Permission to check")
    resource_type: Optional[ResourceType] = Field(None, description="Resource type for resource-specific permissions")
    resource_id: Optional[str] = Field(None, description="Resource ID for resource-specific permissions")
    
    @validator('permission')
    def validate_permission(cls, v):
        """Validate permission exists in Permission enum."""
        valid_permissions = [perm.value for perm in Permission]
        if v not in valid_permissions:
            raise ValueError(f'Invalid permission. Must be one of: {valid_permissions}')
        return v


class PermissionCheckResponse(BaseSchema):
    """
    Schema for permission checking responses.
    
    Provides detailed information about permission check results
    including decision reasoning and metadata.
    """
    
    has_permission: bool = Field(..., description="Whether user has the requested permission")
    decision: AccessDecision = Field(..., description="Access control decision")
    reason: Optional[str] = Field(None, description="Reason for the decision")
    user_role: str = Field(..., description="User's current role")
    checked_permission: str = Field(..., description="Permission that was checked")
    resource_type: Optional[str] = Field(None, description="Resource type checked")
    resource_id: Optional[str] = Field(None, description="Resource ID checked")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")


class RolePermissions(BaseSchema):
    """
    Schema for role permission mappings.
    
    Provides comprehensive role permission information
    including inherited permissions and role hierarchy.
    """
    
    role: str = Field(..., description="Role name")
    permissions: List[str] = Field(..., description="List of permissions for the role")
    inherited_permissions: List[str] = Field(default=[], description="Permissions inherited from parent roles")
    all_permissions: List[str] = Field(..., description="All permissions (direct + inherited)")
    parent_roles: List[str] = Field(default=[], description="Parent roles in hierarchy")
    child_roles: List[str] = Field(default=[], description="Child roles in hierarchy")
    
    @validator('role')
    def validate_role(cls, v):
        """Validate role exists in UserRole enum."""
        valid_roles = [role for role in dir(UserRole) if not role.startswith('_')]
        role_values = [getattr(UserRole, role) for role in valid_roles]
        if v not in role_values:
            raise ValueError(f'Invalid role. Must be one of: {role_values}')
        return v


class PermissionGrantCreate(BaseSchema):
    """
    Schema for creating permission grants.
    
    Used for granting specific permissions to users or roles
    with optional resource-level access control.
    """
    
    user_id: Optional[str] = Field(None, description="User ID for user-specific permissions")
    role: Optional[str] = Field(None, description="Role for role-based permissions")
    permission: str = Field(..., description="Permission to grant")
    resource_type: Optional[ResourceType] = Field(None, description="Resource type for resource-specific permissions")
    resource_id: Optional[str] = Field(None, description="Resource ID for resource-specific permissions")
    expires_at: Optional[datetime] = Field(None, description="When permission expires (null = never)")
    reason: Optional[str] = Field(None, max_length=500, description="Reason for granting permission")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @validator('permission')
    def validate_permission(cls, v):
        """Validate permission exists in Permission enum."""
        valid_permissions = [perm.value for perm in Permission]
        if v not in valid_permissions:
            raise ValueError(f'Invalid permission. Must be one of: {valid_permissions}')
        return v
    
    @validator('role')
    def validate_role(cls, v):
        """Validate role exists in UserRole enum."""
        if v is not None:
            valid_roles = [role for role in dir(UserRole) if not role.startswith('_')]
            role_values = [getattr(UserRole, role) for role in valid_roles]
            if v not in role_values:
                raise ValueError(f'Invalid role. Must be one of: {role_values}')
        return v
    
    @validator('expires_at')
    def validate_expires_at(cls, v):
        """Validate expiration date is in the future."""
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v


class PermissionGrantResponse(BaseSchema, TimestampMixin):
    """
    Schema for permission grant responses.
    
    Provides complete information about permission grants
    including grant details and metadata.
    """
    
    id: str = Field(..., description="Permission grant ID")
    user_id: Optional[str] = Field(None, description="User ID for user-specific permissions")
    role: Optional[str] = Field(None, description="Role for role-based permissions")
    permission: str = Field(..., description="Granted permission")
    resource_type: Optional[str] = Field(None, description="Resource type")
    resource_id: Optional[str] = Field(None, description="Resource ID")
    granted_by: Optional[str] = Field(None, description="User who granted the permission")
    granted_at: datetime = Field(..., description="When permission was granted")
    expires_at: Optional[datetime] = Field(None, description="When permission expires")
    is_active: bool = Field(..., description="Permission grant active status")
    reason: Optional[str] = Field(None, description="Reason for granting permission")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class PermissionGrantUpdate(BaseSchema):
    """
    Schema for updating permission grants.
    
    Allows updating permission grant details including
    expiration and active status.
    """
    
    expires_at: Optional[datetime] = Field(None, description="When permission expires")
    is_active: Optional[bool] = Field(None, description="Permission grant active status")
    reason: Optional[str] = Field(None, max_length=500, description="Updated reason")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Updated metadata")
    
    @validator('expires_at')
    def validate_expires_at(cls, v):
        """Validate expiration date is in the future."""
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v


class AccessControlLogResponse(BaseSchema, TimestampMixin):
    """
    Schema for access control log responses.
    
    Provides comprehensive audit log information for
    access control decisions and security monitoring.
    """
    
    id: str = Field(..., description="Log entry ID")
    user_id: Optional[str] = Field(None, description="User who made the request")
    user_role: Optional[str] = Field(None, description="User role at time of request")
    endpoint: str = Field(..., description="API endpoint accessed")
    method: str = Field(..., description="HTTP method used")
    required_permission: Optional[str] = Field(None, description="Required permission")
    resource_type: Optional[str] = Field(None, description="Resource type accessed")
    resource_id: Optional[str] = Field(None, description="Resource ID accessed")
    decision: AccessDecision = Field(..., description="Access control decision")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")
    request_timestamp: datetime = Field(..., description="When request was made")
    response_time_ms: Optional[int] = Field(None, description="Response time in milliseconds")
    reason: Optional[str] = Field(None, description="Reason for access decision")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class RoleHierarchyResponse(BaseSchema, TimestampMixin):
    """
    Schema for role hierarchy responses.
    
    Provides information about role hierarchical relationships
    and permission inheritance.
    """
    
    id: str = Field(..., description="Hierarchy relationship ID")
    parent_role: str = Field(..., description="Parent role in hierarchy")
    child_role: str = Field(..., description="Child role that inherits permissions")
    level: int = Field(..., description="Hierarchy level (0 = highest)")
    is_active: bool = Field(..., description="Hierarchy relationship active status")
    description: Optional[str] = Field(None, description="Description of relationship")


class UserPermissionSummary(BaseSchema):
    """
    Schema for user permission summary.
    
    Provides comprehensive overview of user's permissions
    including role-based and granted permissions.
    """
    
    user_id: str = Field(..., description="User ID")
    user_role: str = Field(..., description="User's primary role")
    role_permissions: List[str] = Field(..., description="Permissions from role")
    granted_permissions: List[PermissionGrantResponse] = Field(..., description="Additional granted permissions")
    all_permissions: List[str] = Field(..., description="All effective permissions")
    resource_permissions: Dict[str, List[str]] = Field(default={}, description="Resource-specific permissions")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last permission update")


class AccessControlStats(BaseSchema):
    """
    Schema for access control statistics.
    
    Provides analytics and monitoring data for
    access control system performance and security.
    """
    
    total_requests: int = Field(..., description="Total access control requests")
    granted_requests: int = Field(..., description="Number of granted requests")
    denied_requests: int = Field(..., description="Number of denied requests")
    error_requests: int = Field(..., description="Number of error requests")
    success_rate: float = Field(..., description="Success rate percentage")
    avg_response_time_ms: float = Field(..., description="Average response time")
    top_endpoints: List[Dict[str, Any]] = Field(..., description="Most accessed endpoints")
    top_permissions: List[Dict[str, Any]] = Field(..., description="Most checked permissions")
    recent_denials: List[AccessControlLogResponse] = Field(..., description="Recent access denials")
    period_start: datetime = Field(..., description="Statistics period start")
    period_end: datetime = Field(..., description="Statistics period end")


# Export all schemas
__all__ = [
    "AccessDecision",
    "ResourceType",
    "PermissionCheck",
    "PermissionCheckResponse",
    "RolePermissions",
    "PermissionGrantCreate",
    "PermissionGrantResponse",
    "PermissionGrantUpdate",
    "AccessControlLogResponse",
    "RoleHierarchyResponse",
    "UserPermissionSummary",
    "AccessControlStats",
]
