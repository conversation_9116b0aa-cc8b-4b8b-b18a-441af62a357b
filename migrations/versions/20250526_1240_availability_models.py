"""Add availability management models for Task 4.1.2

Revision ID: availability_models
Revises: 635f9ded4592
Create Date: 2025-01-26 12:40:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'availability_models'
down_revision = '635f9ded4592'
branch_labels = None
depends_on = None


def upgrade():
    """Create availability management tables."""
    
    # Create vendor_availability table
    op.create_table('vendor_availability',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('service_id', sa.Integer(), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=False),
        sa.Column('advance_booking_days', sa.Integer(), nullable=False),
        sa.Column('min_booking_notice_hours', sa.Integer(), nullable=False),
        sa.Column('max_booking_notice_days', sa.Integer(), nullable=False),
        sa.Column('default_slot_duration_minutes', sa.Integer(), nullable=False),
        sa.Column('buffer_time_minutes', sa.Integer(), nullable=False),
        sa.Column('max_daily_bookings', sa.Integer(), nullable=True),
        sa.Column('earliest_booking_time', sa.Time(), nullable=False),
        sa.Column('latest_booking_time', sa.Time(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.CheckConstraint('advance_booking_days > 0', name='check_advance_booking_positive'),
        sa.CheckConstraint('min_booking_notice_hours >= 0', name='check_min_notice_non_negative'),
        sa.CheckConstraint('max_booking_notice_days > 0', name='check_max_notice_positive'),
        sa.CheckConstraint('default_slot_duration_minutes > 0', name='check_slot_duration_positive'),
        sa.CheckConstraint('buffer_time_minutes >= 0', name='check_buffer_time_non_negative'),
        sa.CheckConstraint('max_daily_bookings > 0', name='check_daily_bookings_positive'),
        sa.CheckConstraint('earliest_booking_time < latest_booking_time', name='check_booking_time_order'),
        sa.ForeignKeyConstraint(['vendor_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('vendor_id', 'service_id', name='unique_vendor_service_availability'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for vendor_availability
    op.create_index('idx_vendor_availability_vendor_service', 'vendor_availability', ['vendor_id', 'service_id'])
    op.create_index('idx_vendor_availability_active', 'vendor_availability', ['is_active'])
    op.create_index(op.f('ix_vendor_availability_id'), 'vendor_availability', ['id'])
    op.create_index(op.f('ix_vendor_availability_vendor_id'), 'vendor_availability', ['vendor_id'])
    op.create_index(op.f('ix_vendor_availability_service_id'), 'vendor_availability', ['service_id'])
    
    # Create recurring_availability table
    op.create_table('recurring_availability',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vendor_availability_id', sa.Integer(), nullable=False),
        sa.Column('pattern_type', sa.String(length=20), nullable=False),
        sa.Column('pattern_name', sa.String(length=100), nullable=True),
        sa.Column('day_of_week', sa.Integer(), nullable=True),
        sa.Column('day_of_month', sa.Integer(), nullable=True),
        sa.Column('start_time', sa.Time(), nullable=False),
        sa.Column('end_time', sa.Time(), nullable=False),
        sa.Column('valid_from', sa.Date(), nullable=False),
        sa.Column('valid_until', sa.Date(), nullable=True),
        sa.Column('slot_duration_minutes', sa.Integer(), nullable=False),
        sa.Column('max_bookings_per_slot', sa.Integer(), nullable=False),
        sa.Column('buffer_time_minutes', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('auto_generate', sa.Boolean(), nullable=False),
        sa.Column('generation_advance_days', sa.Integer(), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_generated_date', sa.Date(), nullable=True),
        sa.CheckConstraint('start_time < end_time', name='check_recurring_time_order'),
        sa.CheckConstraint('slot_duration_minutes > 0', name='check_slot_duration_positive'),
        sa.CheckConstraint('max_bookings_per_slot > 0', name='check_max_bookings_positive'),
        sa.CheckConstraint('buffer_time_minutes >= 0', name='check_buffer_time_non_negative'),
        sa.CheckConstraint('generation_advance_days > 0', name='check_advance_days_positive'),
        sa.CheckConstraint("pattern_type IN ('daily', 'weekly', 'monthly')", name='check_valid_pattern_type'),
        sa.CheckConstraint('day_of_week BETWEEN 0 AND 6', name='check_valid_day_of_week'),
        sa.CheckConstraint('day_of_month BETWEEN 1 AND 31', name='check_valid_day_of_month'),
        sa.ForeignKeyConstraint(['vendor_availability_id'], ['vendor_availability.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for recurring_availability
    op.create_index('idx_recurring_availability_pattern', 'recurring_availability', ['pattern_type', 'is_active'])
    op.create_index('idx_recurring_availability_validity', 'recurring_availability', ['valid_from', 'valid_until'])
    op.create_index('idx_recurring_availability_generation', 'recurring_availability', ['auto_generate', 'last_generated_date'])
    op.create_index(op.f('ix_recurring_availability_id'), 'recurring_availability', ['id'])
    op.create_index(op.f('ix_recurring_availability_vendor_availability_id'), 'recurring_availability', ['vendor_availability_id'])
    
    # Create availability_slots table
    op.create_table('availability_slots',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vendor_availability_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('start_time', sa.Time(), nullable=False),
        sa.Column('end_time', sa.Time(), nullable=False),
        sa.Column('is_available', sa.Boolean(), nullable=False),
        sa.Column('max_bookings', sa.Integer(), nullable=False),
        sa.Column('current_bookings', sa.Integer(), nullable=False),
        sa.Column('slot_type', sa.String(length=20), nullable=False),
        sa.Column('recurring_availability_id', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('special_pricing', sa.String(length=100), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.CheckConstraint('start_time < end_time', name='check_slot_time_order'),
        sa.CheckConstraint('max_bookings > 0', name='check_max_bookings_positive'),
        sa.CheckConstraint('current_bookings >= 0', name='check_current_bookings_non_negative'),
        sa.CheckConstraint('current_bookings <= max_bookings', name='check_bookings_within_capacity'),
        sa.CheckConstraint("slot_type IN ('regular', 'exception', 'recurring', 'custom')", name='check_valid_slot_type'),
        sa.ForeignKeyConstraint(['recurring_availability_id'], ['recurring_availability.id'], ),
        sa.ForeignKeyConstraint(['vendor_availability_id'], ['vendor_availability.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('vendor_availability_id', 'date', 'start_time', name='unique_vendor_slot_time'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for availability_slots
    op.create_index('idx_availability_slots_date_time', 'availability_slots', ['date', 'start_time'])
    op.create_index('idx_availability_slots_available', 'availability_slots', ['is_available'])
    op.create_index('idx_availability_slots_capacity', 'availability_slots', ['current_bookings', 'max_bookings'])
    op.create_index(op.f('ix_availability_slots_id'), 'availability_slots', ['id'])
    op.create_index(op.f('ix_availability_slots_vendor_availability_id'), 'availability_slots', ['vendor_availability_id'])
    op.create_index(op.f('ix_availability_slots_date'), 'availability_slots', ['date'])
    op.create_index(op.f('ix_availability_slots_recurring_availability_id'), 'availability_slots', ['recurring_availability_id'])
    
    # Create availability_exceptions table
    op.create_table('availability_exceptions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vendor_availability_id', sa.Integer(), nullable=False),
        sa.Column('recurring_availability_id', sa.Integer(), nullable=True),
        sa.Column('exception_date', sa.Date(), nullable=False),
        sa.Column('exception_type', sa.String(length=20), nullable=False),
        sa.Column('modified_start_time', sa.Time(), nullable=True),
        sa.Column('modified_end_time', sa.Time(), nullable=True),
        sa.Column('modified_max_bookings', sa.Integer(), nullable=True),
        sa.Column('reason', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.CheckConstraint("exception_type IN ('unavailable', 'modified', 'custom')", name='check_valid_exception_type'),
        sa.CheckConstraint('modified_max_bookings > 0', name='check_modified_bookings_positive'),
        sa.CheckConstraint(
            "(exception_type = 'unavailable') OR "
            "(exception_type IN ('modified', 'custom') AND modified_start_time IS NOT NULL AND modified_end_time IS NOT NULL)",
            name='check_modified_time_required'
        ),
        sa.CheckConstraint(
            "(modified_start_time IS NULL AND modified_end_time IS NULL) OR "
            "(modified_start_time < modified_end_time)",
            name='check_modified_time_order'
        ),
        sa.ForeignKeyConstraint(['recurring_availability_id'], ['recurring_availability.id'], ),
        sa.ForeignKeyConstraint(['vendor_availability_id'], ['vendor_availability.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('vendor_availability_id', 'exception_date', 'recurring_availability_id', 
                           name='unique_exception_per_date'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for availability_exceptions
    op.create_index('idx_availability_exceptions_date_type', 'availability_exceptions', ['exception_date', 'exception_type'])
    op.create_index('idx_availability_exceptions_active', 'availability_exceptions', ['is_active'])
    op.create_index(op.f('ix_availability_exceptions_id'), 'availability_exceptions', ['id'])
    op.create_index(op.f('ix_availability_exceptions_vendor_availability_id'), 'availability_exceptions', ['vendor_availability_id'])
    op.create_index(op.f('ix_availability_exceptions_recurring_availability_id'), 'availability_exceptions', ['recurring_availability_id'])
    op.create_index(op.f('ix_availability_exceptions_exception_date'), 'availability_exceptions', ['exception_date'])


def downgrade():
    """Drop availability management tables."""
    
    # Drop tables in reverse order
    op.drop_table('availability_exceptions')
    op.drop_table('availability_slots')
    op.drop_table('recurring_availability')
    op.drop_table('vendor_availability')
