# Culture Connect Backend - Development Environment Configuration
# Geolocation Enhancement System - Development Settings
#
# This file contains development-specific configuration for the MaxMind GeoIP
# geolocation enhancement system. Settings are optimized for development
# productivity, debugging, and testing.
#
# Development Notice:
# - This file is safe to commit to version control
# - Contains no production secrets or sensitive data
# - Uses relaxed security settings for development ease
# - Includes enhanced debugging and logging features

# =============================================================================
# MAXMIND GEOIP CONFIGURATION
# =============================================================================

# MaxMind Database Configuration (Development)
CC_GEOIP_DATABASE_PATH=./data/GeoLite2-Country.mmdb
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_GEOLOCATION_FALLBACK_COUNTRY=NG

# MaxMind License Configuration (Development)
# TODO-MAXMIND-LICENSE: Set your development MaxMind license key
MAXMIND_LICENSE_KEY=your_development_maxmind_license_key_here

# Database Update Configuration (Relaxed for Development)
CC_GEOIP_AUTO_UPDATE_ENABLED=false  # Disabled for development
CC_GEOIP_UPDATE_SCHEDULE="0 6 * * 0"  # Weekly on Sunday at 6 AM (if enabled)
CC_GEOIP_UPDATE_WEBHOOK_URL=http://localhost:8080/webhooks/geoip

# =============================================================================
# GEOLOCATION PERFORMANCE CONFIGURATION
# =============================================================================

# Performance Thresholds (Relaxed for Development)
CC_GEOLOCATION_TIMEOUT_MS=10000  # 10 seconds (relaxed)
CC_GEOLOCATION_MAX_RETRIES=1     # Fewer retries for faster feedback
CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD=5
CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT=30

# Cache Configuration (Development Optimized)
CC_GEOLOCATION_CACHE_TTL_HOURS=1  # Short TTL for development
CC_GEOLOCATION_CACHE_MAX_SIZE=1000
CC_GEOLOCATION_CACHE_COMPRESSION=false  # Disabled for debugging

# =============================================================================
# REDIS GEOLOCATION CACHING
# =============================================================================

# Redis Cache Configuration (Development)
CC_GEOLOCATION_REDIS_PREFIX=geo:dev:
CC_GEOLOCATION_REDIS_TTL=3600  # 1 hour in seconds
CC_GEOLOCATION_REDIS_COMPRESSION=false  # Disabled for debugging
CC_GEOLOCATION_REDIS_MAX_CONNECTIONS=5

# Redis Cluster Configuration (Disabled for Development)
CC_GEOLOCATION_REDIS_CLUSTER_ENABLED=false
CC_GEOLOCATION_REDIS_CLUSTER_NODES=""

# =============================================================================
# MONITORING AND METRICS
# =============================================================================

# Monitoring Configuration (Enhanced for Development)
CC_GEOLOCATION_METRICS_ENABLED=true
CC_GEOLOCATION_DETAILED_LOGGING=true   # Enabled for debugging
CC_GEOLOCATION_PERFORMANCE_TRACKING=true
CC_GEOLOCATION_HEALTH_CHECK_ENABLED=true

# Metrics Collection (Development)
CC_GEOLOCATION_METRICS_INTERVAL=30  # seconds (more frequent)
CC_GEOLOCATION_METRICS_RETENTION_DAYS=7  # Shorter retention
CC_GEOLOCATION_METRICS_EXPORT_ENABLED=true

# Alerting Configuration (Relaxed)
CC_GEOLOCATION_ALERT_THRESHOLD_ERROR_RATE=0.20  # 20% error rate (relaxed)
CC_GEOLOCATION_ALERT_THRESHOLD_LATENCY_MS=1000  # 1000ms latency (relaxed)
CC_GEOLOCATION_ALERT_THRESHOLD_SUCCESS_RATE=0.80 # 80% success rate (relaxed)

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Settings (Relaxed for Development)
CC_GEOLOCATION_SECURE_HEADERS=false  # Disabled for development
CC_GEOLOCATION_RATE_LIMITING=false   # Disabled for development
CC_GEOLOCATION_RATE_LIMIT_PER_MINUTE=10000  # High limit
CC_GEOLOCATION_IP_WHITELIST=""  # No restrictions

# Encryption Configuration (Simplified)
CC_GEOLOCATION_ENCRYPT_CACHE=false  # Disabled for debugging
CC_GEOLOCATION_ENCRYPTION_KEY_ROTATION_DAYS=365  # Longer rotation

# =============================================================================
# VPN/PROXY DETECTION CONFIGURATION
# =============================================================================

# VPN Detection Settings (Development)
CC_VPN_DETECTION_ENABLED=true
CC_VPN_DETECTION_API_KEY=development_vpn_detection_key
CC_VPN_DETECTION_CONFIDENCE_THRESHOLD=0.5  # Lower threshold
CC_VPN_DETECTION_TIMEOUT_MS=5000

# ASN Analysis Configuration
CC_ENABLE_ASN_ANALYSIS=true
CC_ASN_ANALYSIS_CACHE_TTL_HOURS=24  # Shorter cache

# Hosting Provider Detection
CC_ENABLE_HOSTING_DETECTION=true
CC_HOSTING_DETECTION_CACHE_TTL_HOURS=12  # Shorter cache

# =============================================================================
# PAYMENT PROVIDER ROUTING
# =============================================================================

# Geolocation-Based Payment Routing (Development)
CC_PAYMENT_ROUTING_ENABLED=true
CC_PAYMENT_ROUTING_FALLBACK_PROVIDER=paystack
CC_PAYMENT_ROUTING_CONFIDENCE_THRESHOLD=0.6  # Lower threshold

# Provider Selection Rules
CC_AFRICAN_COUNTRIES_PRIMARY_PROVIDER=paystack
CC_DIASPORA_COUNTRIES_PRIMARY_PROVIDER=stripe
CC_CRYPTO_PAYMENTS_PRIMARY_PROVIDER=busha

# Routing Performance (Development)
CC_PAYMENT_ROUTING_CACHE_TTL_MINUTES=10  # Shorter cache
CC_PAYMENT_ROUTING_MAX_RETRIES=1

# =============================================================================
# A/B TESTING CONFIGURATION
# =============================================================================

# A/B Testing Framework (Development)
CC_AB_TESTING_ENABLED=true
CC_AB_TESTING_DEFAULT_SPLIT=0.5  # 50/50 split
CC_AB_TESTING_MIN_SAMPLE_SIZE=100  # Lower sample size
CC_AB_TESTING_CONFIDENCE_LEVEL=0.90  # Lower confidence

# Test Configuration (Development)
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_ENABLED=true
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_SPLIT=0.5
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_DURATION_DAYS=7  # Shorter duration

# =============================================================================
# ANALYTICS AND REPORTING
# =============================================================================

# Analytics Configuration (Development)
CC_GEOLOCATION_ANALYTICS_ENABLED=true
CC_GEOLOCATION_ANALYTICS_RETENTION_DAYS=30  # Shorter retention
CC_GEOLOCATION_ANALYTICS_AGGREGATION_INTERVAL=1800  # 30 minutes

# Reporting Configuration (Development)
CC_GEOLOCATION_REPORTING_ENABLED=true
CC_GEOLOCATION_DAILY_REPORTS=true
CC_GEOLOCATION_WEEKLY_REPORTS=false  # Disabled for development
CC_GEOLOCATION_MONTHLY_REPORTS=false  # Disabled for development

# Dashboard Configuration (Development)
CC_GEOLOCATION_DASHBOARD_ENABLED=true
CC_GEOLOCATION_DASHBOARD_REFRESH_INTERVAL=60  # 1 minute (more frequent)
CC_GEOLOCATION_DASHBOARD_RETENTION_DAYS=7

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================

# Backup Configuration (Simplified for Development)
CC_GEOIP_BACKUP_ENABLED=true
CC_GEOIP_BACKUP_RETENTION_DAYS=30  # Shorter retention
CC_GEOIP_BACKUP_COMPRESSION=false  # Disabled for debugging
CC_GEOIP_BACKUP_ENCRYPTION=false   # Disabled for development

# Disaster Recovery (Disabled for Development)
CC_GEOIP_DR_ENABLED=false
CC_GEOIP_DR_BACKUP_LOCATION=./backups/geoip/
CC_GEOIP_DR_REPLICATION_ENABLED=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Levels (Development - Verbose)
CC_GEOLOCATION_LOG_LEVEL=DEBUG
CC_GEOLOCATION_LOG_FORMAT=text  # Human-readable format
CC_GEOLOCATION_LOG_ROTATION=daily
CC_GEOLOCATION_LOG_RETENTION_DAYS=7

# Log Destinations (Development)
CC_GEOLOCATION_LOG_FILE=./logs/geolocation_dev.log
CC_GEOLOCATION_LOG_SYSLOG=false  # Disabled for development
CC_GEOLOCATION_LOG_REMOTE_ENDPOINT=""

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# External Service Integration (Development)
CC_GEOLOCATION_EXTERNAL_APIS_ENABLED=true
CC_GEOLOCATION_EXTERNAL_API_TIMEOUT=15000  # 15 seconds (relaxed)
CC_GEOLOCATION_EXTERNAL_API_RETRIES=1

# Webhook Configuration (Development)
CC_GEOLOCATION_WEBHOOKS_ENABLED=true
CC_GEOLOCATION_WEBHOOK_TIMEOUT=10000  # 10 seconds (relaxed)
CC_GEOLOCATION_WEBHOOK_RETRIES=1

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Environment Identification
ENVIRONMENT=development
CC_GEOLOCATION_ENVIRONMENT=development
CC_GEOLOCATION_VERSION=1.0.0-dev
CC_GEOLOCATION_BUILD_ID=dev-build-local

# Resource Limits (Relaxed for Development)
CC_GEOLOCATION_MAX_MEMORY_MB=1024  # Higher limit
CC_GEOLOCATION_MAX_CPU_PERCENT=90  # Higher limit
CC_GEOLOCATION_MAX_CONNECTIONS=100

# Health Check Configuration (Development)
CC_GEOLOCATION_HEALTH_CHECK_INTERVAL=60  # seconds (less frequent)
CC_GEOLOCATION_HEALTH_CHECK_TIMEOUT=10   # seconds (relaxed)
CC_GEOLOCATION_HEALTH_CHECK_RETRIES=1

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles (All Enabled for Development)
CC_FEATURE_ENHANCED_GEOLOCATION=true
CC_FEATURE_VPN_DETECTION=true
CC_FEATURE_ANALYTICS_DASHBOARD=true
CC_FEATURE_AB_TESTING=true
CC_FEATURE_REAL_TIME_MONITORING=true

# Experimental Features (Enabled for Development)
CC_FEATURE_EXPERIMENTAL_ROUTING=true
CC_FEATURE_BETA_ANALYTICS=true
CC_FEATURE_DEBUG_MODE=true

# =============================================================================
# DEVELOPMENT-SPECIFIC CONFIGURATION
# =============================================================================

# Debug Configuration
CC_GEOLOCATION_DEBUG_MODE=true
CC_GEOLOCATION_DEBUG_VERBOSE=true
CC_GEOLOCATION_DEBUG_TRACE_REQUESTS=true
CC_GEOLOCATION_DEBUG_MOCK_RESPONSES=false

# Development Tools
CC_GEOLOCATION_DEV_TOOLS_ENABLED=true
CC_GEOLOCATION_DEV_API_DOCS=true
CC_GEOLOCATION_DEV_METRICS_UI=true
CC_GEOLOCATION_DEV_PROFILING=true

# Testing Configuration
CC_GEOLOCATION_TEST_MODE=false
CC_GEOLOCATION_TEST_DATA_ENABLED=true
CC_GEOLOCATION_TEST_FIXTURES_PATH=./tests/fixtures/geolocation/

# =============================================================================
# COMPLIANCE AND PRIVACY
# =============================================================================

# Data Privacy Configuration (Relaxed for Development)
CC_GEOLOCATION_GDPR_COMPLIANCE=false  # Disabled for development
CC_GEOLOCATION_DATA_RETENTION_DAYS=30  # Shorter retention
CC_GEOLOCATION_ANONYMIZE_IPS=false     # Disabled for debugging
CC_GEOLOCATION_CONSENT_REQUIRED=false  # Disabled for development

# Audit Configuration (Simplified)
CC_GEOLOCATION_AUDIT_ENABLED=true
CC_GEOLOCATION_AUDIT_RETENTION_DAYS=30  # Shorter retention
CC_GEOLOCATION_AUDIT_ENCRYPTION=false   # Disabled for development

# =============================================================================
# DEVELOPMENT SETUP NOTES
# =============================================================================

# 1. Quick Setup:
#    - Copy this file to .env for local development
#    - Set MAXMIND_LICENSE_KEY with your development key
#    - Run: ./scripts/setup_geoip.sh
#
# 2. Development Tools:
#    - Access API docs at http://localhost:8000/docs
#    - View metrics at http://localhost:8000/metrics
#    - Check health at http://localhost:8000/health/geolocation
#
# 3. Debugging:
#    - Enable verbose logging with CC_GEOLOCATION_DEBUG_VERBOSE=true
#    - Use CC_GEOLOCATION_DEBUG_TRACE_REQUESTS=true for request tracing
#    - Check logs in ./logs/geolocation_dev.log
#
# 4. Testing:
#    - Use shorter cache TTLs for faster iteration
#    - Enable test fixtures with CC_GEOLOCATION_TEST_DATA_ENABLED=true
#    - Run verification: python3 scripts/verify_geoip.py
#
# 5. Performance Testing:
#    - Use relaxed thresholds for development
#    - Monitor performance with CC_GEOLOCATION_PERFORMANCE_TRACKING=true
#    - Profile with CC_GEOLOCATION_DEV_PROFILING=true
