# Culture Connect Backend - Production Security Patch
# Enhanced security configurations for production environment

apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
spec:
  template:
    metadata:
      annotations:
        container.apparmor.security.beta.kubernetes.io/api: runtime/default
        container.apparmor.security.beta.kubernetes.io/nginx: runtime/default
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
        supplementalGroups: [1001]
      containers:
      - name: api
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE
        env:
        - name: CC_SECURITY_HEADERS_ENABLED
          value: "true"
        - name: CC_HSTS_MAX_AGE
          value: "31536000"
        - name: CC_CONTENT_SECURITY_POLICY
          value: "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'"
        - name: CC_SSL_ENABLED
          value: "true"
        - name: CC_RATE_LIMITING_ENABLED
          value: "true"
        - name: CC_RATE_LIMIT_REQUESTS
          value: "5000"
        - name: CC_RATE_LIMIT_WINDOW
          value: "3600"
      - name: nginx
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 101
          runAsGroup: 101
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-worker
spec:
  template:
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: celery-worker
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-beat
spec:
  template:
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: celery-beat
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
            - ALL
