"""
Cross-System Integration Tests for Culture Connect Backend API.

This module provides comprehensive cross-system integration validation testing including:
- Authentication ↔ All Systems Integration (JWT token validation, RBAC enforcement)
- Booking System Integration Hub (Payment, Communication, Analytics, WebSocket integration)
- VendorDashboardService Integration (vendor management, analytics aggregation, real-time updates)
- Analytics System Integration (data collection, metrics aggregation, performance monitoring)

Implements Phase 8.1: Integration Testing Implementation with >80% test coverage,
100% cross-system communication success rate, and comprehensive RBAC validation.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock

from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VendorStatus
from app.models.booking import Booking, BookingStatus
from app.models.payment_models import Payment, PaymentStatus, PaymentProvider
from app.models.booking_communication import BookingMessage, MessageType, MessageStatus
from app.models.analytics_models import UserAnalytics, VendorAnalytics, AnalyticsTimeframe
from app.services.auth_service import AuthService
from app.services.vendor_service import VendorService
from app.services.booking_service import BookingService
from app.services.payment_service import PaymentService
from app.services.booking_communication_service import BookingCommunicationService
from app.services.analytics_services import AnalyticsService
from app.services.vendor_dashboard_service import VendorDashboardService
from app.db.session import get_async_session_context
from app.core.security import create_token_pair


@pytest.mark.integration
class TestCrossSystemIntegration:
    """Comprehensive cross-system integration validation tests."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client with real app."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    async def db_session(self):
        """Create database session with transaction rollback."""
        async with get_async_session_context() as session:
            transaction = await session.begin()
            try:
                yield session
            finally:
                await transaction.rollback()

    @pytest.fixture
    async def test_customer_user(self, db_session: AsyncSession):
        """Create test customer user for cross-system testing."""
        user_data = {
            "email": f"customer_{uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "Customer",
            "role": UserRole.CUSTOMER,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor_user(self, db_session: AsyncSession):
        """Create test vendor user for cross-system testing."""
        user_data = {
            "email": f"vendor_{uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "Vendor",
            "role": UserRole.VENDOR,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_admin_user(self, db_session: AsyncSession):
        """Create test admin user for cross-system testing."""
        user_data = {
            "email": f"admin_{uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "Admin",
            "role": UserRole.ADMIN,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession, test_vendor_user: User):
        """Create test vendor for cross-system testing."""
        vendor_data = {
            "user_id": test_vendor_user.id,
            "business_name": "Cross-System Test Tours",
            "business_type": VendorType.TOUR_GUIDE,
            "status": VendorStatus.ACTIVE,
            "verified": True,
            "phone": "+************",
            "address": "Lagos, Nigeria"
        }

        vendor = Vendor(**vendor_data)
        db_session.add(vendor)
        await db_session.commit()
        await db_session.refresh(vendor)
        return vendor

    @pytest.fixture
    def customer_auth_headers(self, test_customer_user: User):
        """Create authentication headers for customer user."""
        token_data = {
            "sub": str(test_customer_user.id),
            "email": test_customer_user.email,
            "role": test_customer_user.role.value,
            "scopes": ["customer:read", "customer:write", "booking:create", "payment:create"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def vendor_auth_headers(self, test_vendor_user: User):
        """Create authentication headers for vendor user."""
        token_data = {
            "sub": str(test_vendor_user.id),
            "email": test_vendor_user.email,
            "role": test_vendor_user.role.value,
            "scopes": ["vendor:read", "vendor:write", "booking:manage", "dashboard:access"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def admin_auth_headers(self, test_admin_user: User):
        """Create authentication headers for admin user."""
        token_data = {
            "sub": str(test_admin_user.id),
            "email": test_admin_user.email,
            "role": test_admin_user.role.value,
            "scopes": ["admin:read", "admin:write", "analytics:access", "system:manage"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def performance_timer(self):
        """Performance timing utility for cross-system integration tests."""
        class PerformanceTimer:
            def __init__(self):
                self.start_time = None
                self.end_time = None

            def start(self):
                self.start_time = time.perf_counter()

            def stop(self):
                self.end_time = time.perf_counter()
                return (self.end_time - self.start_time) * 1000  # Return milliseconds

        return PerformanceTimer()

    @pytest.mark.asyncio
    async def test_authentication_all_systems_integration(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor_user: User,
        test_admin_user: User,
        customer_auth_headers: Dict[str, str],
        vendor_auth_headers: Dict[str, str],
        admin_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Authentication ↔ All Systems Integration with JWT token validation and RBAC enforcement.

        Validates: JWT propagation, RBAC permissions, session management across all systems
        """
        correlation_id = f"test_auth_integration_{uuid4().hex[:8]}"

        # Test 1: Customer Authentication Across Systems
        performance_timer.start()

        # Customer can access user profile
        profile_response = await async_client.get(
            "/api/v1/users/profile",
            headers=customer_auth_headers
        )
        assert profile_response.status_code == status.HTTP_200_OK

        # Customer can search vendors
        vendor_search_response = await async_client.get(
            "/api/v1/vendors/search",
            params={"location": "Lagos"},
            headers=customer_auth_headers
        )
        assert vendor_search_response.status_code == status.HTTP_200_OK

        # Customer cannot access vendor dashboard (RBAC enforcement)
        vendor_dashboard_response = await async_client.get(
            "/api/v1/vendors/dashboard/overview",
            headers=customer_auth_headers
        )
        assert vendor_dashboard_response.status_code == status.HTTP_403_FORBIDDEN

        customer_auth_time = performance_timer.stop()
        assert customer_auth_time < 600  # <600ms for multiple operations

        # Test 2: Vendor Authentication Across Systems
        performance_timer.start()

        # Vendor can access vendor profile
        vendor_profile_response = await async_client.get(
            "/api/v1/vendors/profile",
            headers=vendor_auth_headers
        )
        assert vendor_profile_response.status_code == status.HTTP_200_OK

        # Vendor can access dashboard
        dashboard_response = await async_client.get(
            "/api/v1/vendors/dashboard/overview",
            headers=vendor_auth_headers
        )
        assert dashboard_response.status_code == status.HTTP_200_OK

        # Vendor cannot access admin analytics (RBAC enforcement)
        admin_analytics_response = await async_client.get(
            "/api/v1/analytics/admin/system-metrics",
            headers=vendor_auth_headers
        )
        assert admin_analytics_response.status_code == status.HTTP_403_FORBIDDEN

        vendor_auth_time = performance_timer.stop()
        assert vendor_auth_time < 600  # <600ms for multiple operations

        # Test 3: Admin Authentication Across Systems
        performance_timer.start()

        # Admin can access system analytics
        system_analytics_response = await async_client.get(
            "/api/v1/analytics/admin/system-metrics",
            headers=admin_auth_headers
        )
        assert system_analytics_response.status_code == status.HTTP_200_OK

        # Admin can access all vendor data
        all_vendors_response = await async_client.get(
            "/api/v1/admin/vendors",
            headers=admin_auth_headers
        )
        assert all_vendors_response.status_code == status.HTTP_200_OK

        admin_auth_time = performance_timer.stop()
        assert admin_auth_time < 400  # <400ms for admin operations

    @pytest.mark.asyncio
    async def test_booking_system_integration_hub(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor: Vendor,
        customer_auth_headers: Dict[str, str],
        vendor_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Booking System Integration Hub with Payment, Communication, Analytics, WebSocket systems.

        Validates: Booking ↔ Payment ↔ Communication ↔ Analytics ↔ WebSocket integration
        """
        correlation_id = f"test_booking_hub_{uuid4().hex[:8]}"

        # Step 1: Create Booking (Booking System)
        booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=5)).isoformat(),
            "total_amount": 250.00,
            "number_of_guests": 2,
            "special_requests": "Integration test booking"
        }

        performance_timer.start()
        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=booking_data,
            headers=customer_auth_headers
        )
        booking_time = performance_timer.stop()

        assert booking_response.status_code == status.HTTP_201_CREATED
        assert booking_time < 500  # <500ms performance target
        booking_result = booking_response.json()
        booking_id = booking_result["id"]

        # Step 2: Payment System Integration
        payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("250.00"),
            "currency": "NGN",
            "payment_method": "card",
            "provider": PaymentProvider.PAYSTACK.value
        }

        performance_timer.start()
        with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
            mock_payment.return_value = {
                "status": "success",
                "data": {
                    "authorization_url": "https://checkout.paystack.com/test",
                    "reference": f"ref_{uuid4().hex[:8]}"
                }
            }

            payment_response = await async_client.post(
                "/api/v1/payments/initialize",
                json=payment_data,
                headers=customer_auth_headers
            )
        payment_time = performance_timer.stop()

        assert payment_response.status_code == status.HTTP_200_OK
        assert payment_time < 500  # <500ms performance target

        # Step 3: Communication System Integration
        message_data = {
            "booking_id": booking_id,
            "message_type": MessageType.CUSTOMER_MESSAGE.value,
            "content": "Integration test message for booking communication",
            "sender_type": "customer"
        }

        performance_timer.start()
        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=customer_auth_headers
        )
        message_time = performance_timer.stop()

        assert message_response.status_code == status.HTTP_201_CREATED
        assert message_time < 500  # <500ms performance target

        # Step 4: Analytics System Integration - Verify Data Collection
        performance_timer.start()
        analytics_response = await async_client.get(
            "/api/v1/analytics/user/dashboard",
            headers=customer_auth_headers
        )
        analytics_time = performance_timer.stop()

        assert analytics_response.status_code == status.HTTP_200_OK
        assert analytics_time < 200  # <200ms performance target

        # Step 5: WebSocket System Integration - Test Real-time Updates
        performance_timer.start()
        websocket_rooms_response = await async_client.get(
            "/api/v1/websocket/rooms/",
            headers=customer_auth_headers
        )
        websocket_time = performance_timer.stop()

        assert websocket_rooms_response.status_code == status.HTTP_200_OK
        assert websocket_time < 200  # <200ms performance target

        # Step 6: Cross-System Database Consistency Validation
        async with get_async_session_context() as session:
            # Verify booking exists and is properly linked
            booking = await session.get(Booking, booking_id)
            assert booking is not None
            assert booking.customer_id == test_customer_user.id
            assert booking.vendor_id == test_vendor.id

            # Verify message is linked to booking
            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None
            assert message.booking_id == booking_id

            # Verify foreign key relationships are intact
            assert booking.customer is not None
            assert booking.vendor is not None
            assert message.booking is not None

    @pytest.mark.asyncio
    async def test_vendor_dashboard_service_integration(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_vendor_user: User,
        test_vendor: Vendor,
        test_customer_user: User,
        vendor_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test VendorDashboardService Integration with vendor management, analytics aggregation, real-time updates.

        Validates: VendorDashboardService ↔ Vendor Management ↔ Analytics ↔ Real-time Updates
        """
        correlation_id = f"test_vendor_dashboard_{uuid4().hex[:8]}"

        # Step 1: Create Test Data for Dashboard Integration
        # Create a booking for analytics
        booking_data = {
            "customer_id": test_customer_user.id,
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": datetime.now() + timedelta(days=3),
            "status": BookingStatus.CONFIRMED,
            "total_amount": Decimal("300.00"),
            "number_of_guests": 3
        }

        booking = Booking(**booking_data)
        db_session.add(booking)
        await db_session.commit()
        await db_session.refresh(booking)

        # Step 2: Vendor Dashboard Overview Integration
        performance_timer.start()
        dashboard_overview_response = await async_client.get(
            "/api/v1/vendors/dashboard/overview",
            headers=vendor_auth_headers
        )
        dashboard_time = performance_timer.stop()

        assert dashboard_overview_response.status_code == status.HTTP_200_OK
        assert dashboard_time < 200  # <200ms performance target
        dashboard_data = dashboard_overview_response.json()

        # Verify dashboard contains integrated data
        assert "total_bookings" in dashboard_data
        assert "revenue_metrics" in dashboard_data
        assert "recent_activities" in dashboard_data

        # Step 3: Vendor Analytics Integration
        performance_timer.start()
        analytics_response = await async_client.get(
            "/api/v1/vendors/analytics/performance",
            params={"timeframe": "month"},
            headers=vendor_auth_headers
        )
        analytics_time = performance_timer.stop()

        assert analytics_response.status_code == status.HTTP_200_OK
        assert analytics_time < 200  # <200ms performance target

        # Step 4: Vendor Booking Management Integration
        performance_timer.start()
        bookings_response = await async_client.get(
            "/api/v1/vendors/bookings",
            params={"status": "confirmed"},
            headers=vendor_auth_headers
        )
        bookings_time = performance_timer.stop()

        assert bookings_response.status_code == status.HTTP_200_OK
        assert bookings_time < 200  # <200ms performance target

        # Step 5: Real-time Dashboard Updates Integration
        performance_timer.start()
        metrics_response = await async_client.get(
            "/api/v1/vendors/dashboard/metrics/real-time",
            headers=vendor_auth_headers
        )
        metrics_time = performance_timer.stop()

        assert metrics_response.status_code == status.HTTP_200_OK
        assert metrics_time < 200  # <200ms performance target

        # Step 6: Cross-Component Integration Validation
        async with get_async_session_context() as session:
            # Verify vendor dashboard can access all related data
            vendor = await session.get(Vendor, test_vendor.id)
            assert vendor is not None

            # Verify booking is accessible through vendor relationship
            vendor_bookings = await session.execute(
                f"SELECT * FROM bookings WHERE vendor_id = {test_vendor.id}"
            )
            bookings_list = vendor_bookings.fetchall()
            assert len(bookings_list) >= 1

            # Verify analytics data integration
            assert vendor.user_id == test_vendor_user.id

    @pytest.mark.asyncio
    async def test_analytics_system_integration(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor_user: User,
        test_vendor: Vendor,
        customer_auth_headers: Dict[str, str],
        vendor_auth_headers: Dict[str, str],
        admin_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Analytics System Integration with data collection, metrics aggregation, performance monitoring.

        Validates: Analytics ↔ All Business Logic Systems ↔ Real-time Metrics ↔ Performance Monitoring
        """
        correlation_id = f"test_analytics_integration_{uuid4().hex[:8]}"

        # Step 1: Create Test Data for Analytics Integration
        # Create booking for analytics data
        booking_data = {
            "customer_id": test_customer_user.id,
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": datetime.now() + timedelta(days=2),
            "status": BookingStatus.CONFIRMED,
            "total_amount": Decimal("400.00"),
            "number_of_guests": 4
        }

        booking = Booking(**booking_data)
        db_session.add(booking)

        # Create user analytics data
        user_analytics_data = {
            "user_id": test_customer_user.id,
            "timeframe": AnalyticsTimeframe.DAILY,
            "period_start": datetime.now().date(),
            "period_end": datetime.now().date(),
            "total_bookings": 1,
            "total_spent": Decimal("400.00"),
            "average_booking_value": Decimal("400.00"),
            "favorite_categories": ["cultural_tour"],
            "booking_frequency": 1.0
        }

        user_analytics = UserAnalytics(**user_analytics_data)
        db_session.add(user_analytics)

        # Create vendor analytics data
        vendor_analytics_data = {
            "vendor_id": test_vendor.id,
            "timeframe": AnalyticsTimeframe.DAILY,
            "period_start": datetime.now().date(),
            "period_end": datetime.now().date(),
            "total_bookings": 1,
            "total_revenue": Decimal("400.00"),
            "average_rating": Decimal("4.5"),
            "conversion_rate": Decimal("0.85"),
            "response_time_avg": 120,
            "customer_satisfaction": Decimal("4.2")
        }

        vendor_analytics = VendorAnalytics(**vendor_analytics_data)
        db_session.add(vendor_analytics)

        await db_session.commit()
        await db_session.refresh(booking)
        await db_session.refresh(user_analytics)
        await db_session.refresh(vendor_analytics)

        # Step 2: User Analytics Integration
        performance_timer.start()
        user_analytics_response = await async_client.get(
            "/api/v1/analytics/user/dashboard",
            headers=customer_auth_headers
        )
        user_analytics_time = performance_timer.stop()

        assert user_analytics_response.status_code == status.HTTP_200_OK
        assert user_analytics_time < 200  # <200ms performance target
        user_data = user_analytics_response.json()

        # Verify analytics data integration
        assert "total_bookings" in user_data
        assert "total_spent" in user_data

        # Step 3: Vendor Analytics Integration
        performance_timer.start()
        vendor_analytics_response = await async_client.get(
            "/api/v1/analytics/vendor/dashboard",
            headers=vendor_auth_headers
        )
        vendor_analytics_time = performance_timer.stop()

        assert vendor_analytics_response.status_code == status.HTTP_200_OK
        assert vendor_analytics_time < 200  # <200ms performance target
        vendor_data = vendor_analytics_response.json()

        # Verify vendor analytics integration
        assert "total_revenue" in vendor_data
        assert "conversion_rate" in vendor_data

        # Step 4: System-wide Analytics Integration (Admin)
        performance_timer.start()
        system_analytics_response = await async_client.get(
            "/api/v1/analytics/admin/system-metrics",
            headers=admin_auth_headers
        )
        system_analytics_time = performance_timer.stop()

        assert system_analytics_response.status_code == status.HTTP_200_OK
        assert system_analytics_time < 200  # <200ms performance target

        # Step 5: Real-time Metrics Integration
        performance_timer.start()
        realtime_metrics_response = await async_client.get(
            "/api/v1/analytics/metrics/real-time",
            headers=admin_auth_headers
        )
        realtime_time = performance_timer.stop()

        assert realtime_metrics_response.status_code == status.HTTP_200_OK
        assert realtime_time < 200  # <200ms performance target

        # Step 6: Performance Monitoring Integration
        performance_timer.start()
        performance_metrics_response = await async_client.get(
            "/api/v1/analytics/performance/monitoring",
            headers=admin_auth_headers
        )
        monitoring_time = performance_timer.stop()

        assert performance_metrics_response.status_code == status.HTTP_200_OK
        assert monitoring_time < 200  # <200ms performance target

        # Step 7: Cross-System Analytics Data Consistency
        async with get_async_session_context() as session:
            # Verify analytics data is properly linked to business entities
            user_analytics_record = await session.get(UserAnalytics, user_analytics.id)
            assert user_analytics_record is not None
            assert user_analytics_record.user_id == test_customer_user.id

            vendor_analytics_record = await session.get(VendorAnalytics, vendor_analytics.id)
            assert vendor_analytics_record is not None
            assert vendor_analytics_record.vendor_id == test_vendor.id

            # Verify booking data is accessible for analytics
            booking_record = await session.get(Booking, booking.id)
            assert booking_record is not None
            assert booking_record.customer_id == test_customer_user.id
            assert booking_record.vendor_id == test_vendor.id

            # Verify foreign key relationships for analytics
            assert booking_record.customer is not None
            assert booking_record.vendor is not None

        # Step 8: Analytics Performance Under Load Simulation
        performance_timer.start()

        # Simulate multiple concurrent analytics requests
        analytics_tasks = []
        for i in range(5):
            task = async_client.get(
                "/api/v1/analytics/user/dashboard",
                headers=customer_auth_headers
            )
            analytics_tasks.append(task)

        # Execute concurrent requests
        concurrent_responses = await asyncio.gather(*analytics_tasks)
        concurrent_time = performance_timer.stop()

        # Verify all requests succeeded
        for response in concurrent_responses:
            assert response.status_code == status.HTTP_200_OK

        # Verify performance under concurrent load
        assert concurrent_time < 1000  # <1000ms for 5 concurrent requests
        average_response_time = concurrent_time / 5
        assert average_response_time < 200  # <200ms average response time
