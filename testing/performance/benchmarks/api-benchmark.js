// Culture Connect Backend - API Performance Benchmarking
// Comprehensive performance validation for production readiness

const autocannon = require('autocannon');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  baseUrl: process.env.API_BASE_URL || 'https://api.cultureconnect.ng',
  duration: 60, // seconds
  connections: 100,
  pipelining: 10,
  timeout: 30, // seconds
  
  // Performance thresholds
  thresholds: {
    p95ResponseTime: 500, // ms
    p99ResponseTime: 1000, // ms
    medianResponseTime: 200, // ms
    minThroughput: 1000, // requests/second
    maxErrorRate: 0.01, // 1%
  }
};

// Test endpoints configuration
const ENDPOINTS = [
  {
    name: 'Health Check',
    method: 'GET',
    path: '/health',
    expectedStatus: 200,
    weight: 10,
    headers: {},
    body: null
  },
  {
    name: 'API Documentation',
    method: 'GET',
    path: '/docs',
    expectedStatus: 200,
    weight: 5,
    headers: {},
    body: null
  },
  {
    name: 'User Authentication',
    method: 'POST',
    path: '/api/v1/auth/login',
    expectedStatus: 200,
    weight: 20,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'LoadTest123!'
    })
  },
  {
    name: 'Vendor Search',
    method: 'GET',
    path: '/api/v1/vendors/search?location=Lagos&service_type=catering&limit=20',
    expectedStatus: 200,
    weight: 30,
    headers: {},
    body: null
  },
  {
    name: 'Vendor Details',
    method: 'GET',
    path: '/api/v1/vendors/1',
    expectedStatus: 200,
    weight: 25,
    headers: {},
    body: null
  },
  {
    name: 'Payment Initialization',
    method: 'POST',
    path: '/api/v1/payments/initialize',
    expectedStatus: 200,
    weight: 10,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test_token'
    },
    body: JSON.stringify({
      amount: 50000,
      currency: 'NGN',
      payment_method: 'paystack',
      reference: 'benchmark_test'
    })
  }
];

// Benchmark results storage
let benchmarkResults = {
  timestamp: new Date().toISOString(),
  config: CONFIG,
  endpoints: {},
  summary: {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    overallScore: 0
  }
};

// Main benchmarking function
async function runBenchmarks() {
  console.log('🚀 Starting API Performance Benchmarking...');
  console.log(`Target: ${CONFIG.baseUrl}`);
  console.log(`Duration: ${CONFIG.duration}s per endpoint`);
  console.log(`Connections: ${CONFIG.connections}`);
  console.log('='.repeat(60));
  
  for (const endpoint of ENDPOINTS) {
    console.log(`\n📊 Benchmarking: ${endpoint.name}`);
    console.log(`${endpoint.method} ${endpoint.path}`);
    
    try {
      const result = await benchmarkEndpoint(endpoint);
      benchmarkResults.endpoints[endpoint.name] = result;
      
      // Analyze results
      const analysis = analyzeResults(result, endpoint);
      console.log(`✅ Completed: ${endpoint.name}`);
      console.log(`   Throughput: ${result.requests.average} req/s`);
      console.log(`   P95 Latency: ${result.latency.p95}ms`);
      console.log(`   Error Rate: ${(result.errors / result.requests.total * 100).toFixed(2)}%`);
      console.log(`   Score: ${analysis.score}/100`);
      
      benchmarkResults.summary.totalTests++;
      if (analysis.passed) {
        benchmarkResults.summary.passedTests++;
      } else {
        benchmarkResults.summary.failedTests++;
      }
      
    } catch (error) {
      console.error(`❌ Failed: ${endpoint.name} - ${error.message}`);
      benchmarkResults.endpoints[endpoint.name] = {
        error: error.message,
        passed: false,
        score: 0
      };
      benchmarkResults.summary.totalTests++;
      benchmarkResults.summary.failedTests++;
    }
    
    // Wait between tests
    await sleep(5000);
  }
  
  // Generate final report
  generateReport();
}

// Benchmark individual endpoint
async function benchmarkEndpoint(endpoint) {
  const options = {
    url: `${CONFIG.baseUrl}${endpoint.path}`,
    method: endpoint.method,
    duration: CONFIG.duration,
    connections: CONFIG.connections,
    pipelining: CONFIG.pipelining,
    timeout: CONFIG.timeout,
    headers: endpoint.headers,
    body: endpoint.body,
    
    // Custom request generator for weighted load
    requests: generateRequests(endpoint)
  };
  
  return new Promise((resolve, reject) => {
    const instance = autocannon(options, (err, result) => {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
    
    // Track progress
    instance.on('response', (client, statusCode, resBytes, responseTime) => {
      if (statusCode !== endpoint.expectedStatus) {
        console.log(`⚠️  Unexpected status: ${statusCode} (expected ${endpoint.expectedStatus})`);
      }
    });
    
    instance.on('reqError', (error) => {
      console.log(`❌ Request error: ${error.message}`);
    });
  });
}

// Generate weighted requests
function generateRequests(endpoint) {
  const requests = [];
  const requestCount = Math.floor(CONFIG.connections * endpoint.weight / 100);
  
  for (let i = 0; i < requestCount; i++) {
    requests.push({
      method: endpoint.method,
      path: endpoint.path,
      headers: endpoint.headers,
      body: endpoint.body
    });
  }
  
  return requests;
}

// Analyze benchmark results
function analyzeResults(result, endpoint) {
  const analysis = {
    endpoint: endpoint.name,
    passed: true,
    score: 100,
    issues: [],
    metrics: {
      throughput: result.requests.average,
      p95Latency: result.latency.p95,
      p99Latency: result.latency.p99,
      medianLatency: result.latency.p50,
      errorRate: result.errors / result.requests.total,
      totalRequests: result.requests.total
    }
  };
  
  // Check P95 response time
  if (result.latency.p95 > CONFIG.thresholds.p95ResponseTime) {
    analysis.passed = false;
    analysis.score -= 25;
    analysis.issues.push(`P95 latency ${result.latency.p95}ms exceeds threshold ${CONFIG.thresholds.p95ResponseTime}ms`);
  }
  
  // Check P99 response time
  if (result.latency.p99 > CONFIG.thresholds.p99ResponseTime) {
    analysis.passed = false;
    analysis.score -= 20;
    analysis.issues.push(`P99 latency ${result.latency.p99}ms exceeds threshold ${CONFIG.thresholds.p99ResponseTime}ms`);
  }
  
  // Check median response time
  if (result.latency.p50 > CONFIG.thresholds.medianResponseTime) {
    analysis.passed = false;
    analysis.score -= 15;
    analysis.issues.push(`Median latency ${result.latency.p50}ms exceeds threshold ${CONFIG.thresholds.medianResponseTime}ms`);
  }
  
  // Check throughput
  if (result.requests.average < CONFIG.thresholds.minThroughput) {
    analysis.passed = false;
    analysis.score -= 20;
    analysis.issues.push(`Throughput ${result.requests.average} req/s below threshold ${CONFIG.thresholds.minThroughput} req/s`);
  }
  
  // Check error rate
  const errorRate = result.errors / result.requests.total;
  if (errorRate > CONFIG.thresholds.maxErrorRate) {
    analysis.passed = false;
    analysis.score -= 20;
    analysis.issues.push(`Error rate ${(errorRate * 100).toFixed(2)}% exceeds threshold ${(CONFIG.thresholds.maxErrorRate * 100).toFixed(2)}%`);
  }
  
  // Ensure score doesn't go below 0
  analysis.score = Math.max(0, analysis.score);
  
  return analysis;
}

// Generate comprehensive report
function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 PERFORMANCE BENCHMARK REPORT');
  console.log('='.repeat(60));
  
  // Calculate overall score
  const totalScore = Object.values(benchmarkResults.endpoints)
    .reduce((sum, result) => sum + (result.score || 0), 0);
  benchmarkResults.summary.overallScore = Math.round(totalScore / benchmarkResults.summary.totalTests);
  
  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Tests: ${benchmarkResults.summary.totalTests}`);
  console.log(`   Passed: ${benchmarkResults.summary.passedTests}`);
  console.log(`   Failed: ${benchmarkResults.summary.failedTests}`);
  console.log(`   Overall Score: ${benchmarkResults.summary.overallScore}/100`);
  
  // Detailed results
  console.log(`\n📋 DETAILED RESULTS:`);
  Object.entries(benchmarkResults.endpoints).forEach(([name, result]) => {
    if (result.error) {
      console.log(`   ❌ ${name}: ERROR - ${result.error}`);
    } else {
      const status = result.passed ? '✅' : '❌';
      console.log(`   ${status} ${name}: ${result.score}/100`);
      
      if (result.issues && result.issues.length > 0) {
        result.issues.forEach(issue => {
          console.log(`      ⚠️  ${issue}`);
        });
      }
    }
  });
  
  // Performance recommendations
  console.log(`\n💡 RECOMMENDATIONS:`);
  if (benchmarkResults.summary.overallScore >= 90) {
    console.log('   🎉 Excellent performance! System is production-ready.');
  } else if (benchmarkResults.summary.overallScore >= 75) {
    console.log('   ✅ Good performance with minor optimization opportunities.');
  } else if (benchmarkResults.summary.overallScore >= 60) {
    console.log('   ⚠️  Moderate performance issues detected. Optimization recommended.');
  } else {
    console.log('   ❌ Significant performance issues detected. Immediate optimization required.');
  }
  
  // Save results to file
  const reportPath = path.join(__dirname, '../reports', `benchmark-report-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(benchmarkResults, null, 2));
  console.log(`\n📄 Report saved to: ${reportPath}`);
  
  // Generate HTML report
  generateHtmlReport();
}

// Generate HTML report
function generateHtmlReport() {
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Culture Connect API Performance Benchmark Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .summary { background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .endpoint { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .passed { border-left: 5px solid #4CAF50; }
        .failed { border-left: 5px solid #f44336; }
        .metric { display: inline-block; margin: 5px 10px; }
        .score { font-size: 24px; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Culture Connect API Performance Benchmark Report</h1>
        <p><strong>Generated:</strong> ${benchmarkResults.timestamp}</p>
        <p><strong>Target:</strong> ${CONFIG.baseUrl}</p>
        <p><strong>Duration:</strong> ${CONFIG.duration}s per endpoint</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <div class="score">Overall Score: ${benchmarkResults.summary.overallScore}/100</div>
        <p>Tests: ${benchmarkResults.summary.totalTests} | Passed: ${benchmarkResults.summary.passedTests} | Failed: ${benchmarkResults.summary.failedTests}</p>
    </div>
    
    <h2>Endpoint Results</h2>
    ${Object.entries(benchmarkResults.endpoints).map(([name, result]) => `
        <div class="endpoint ${result.passed ? 'passed' : 'failed'}">
            <h3>${name} - ${result.score || 0}/100</h3>
            ${result.metrics ? `
                <div class="metric">Throughput: ${result.metrics.throughput.toFixed(2)} req/s</div>
                <div class="metric">P95: ${result.metrics.p95Latency}ms</div>
                <div class="metric">P99: ${result.metrics.p99Latency}ms</div>
                <div class="metric">Median: ${result.metrics.medianLatency}ms</div>
                <div class="metric">Error Rate: ${(result.metrics.errorRate * 100).toFixed(2)}%</div>
            ` : ''}
            ${result.issues ? result.issues.map(issue => `<p style="color: red;">⚠️ ${issue}</p>`).join('') : ''}
        </div>
    `).join('')}
</body>
</html>
  `;
  
  const htmlPath = path.join(__dirname, '../reports', `benchmark-report-${Date.now()}.html`);
  fs.writeFileSync(htmlPath, htmlContent);
  console.log(`📄 HTML report saved to: ${htmlPath}`);
}

// Utility function
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run benchmarks if called directly
if (require.main === module) {
  runBenchmarks().catch(console.error);
}

module.exports = {
  runBenchmarks,
  CONFIG,
  ENDPOINTS
};
