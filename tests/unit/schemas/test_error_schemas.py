"""
Tests for error response schemas and structured error handling.

This module tests the error handling system features:
- Standardized error response schemas
- Error code classification and HTTP status mapping
- Validation error formatting and localization
- Business logic error responses
- Error response factory functionality
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4

from pydantic import ValidationError
from fastapi import status

from app.schemas.errors import (
    ErrorCategory, ErrorSeverity, ErrorCode, ErrorDetail, ErrorResponse,
    ValidationErrorResponse, BusinessLogicErrorResponse, ErrorResponseFactory,
    StructuredHTTPException
)


class TestErrorCode:
    """Test error code enumeration and utilities."""
    
    def test_error_code_values(self):
        """Test error code values and format."""
        assert ErrorCode.VALIDATION_FAILED.value == "E1000"
        assert ErrorCode.INVALID_CREDENTIALS.value == "E2000"
        assert ErrorCode.INSUFFICIENT_PERMISSIONS.value == "E3000"
        assert ErrorCode.RESOURCE_NOT_FOUND.value == "E4000"
        assert ErrorCode.RESOURCE_ALREADY_EXISTS.value == "E5000"
        assert ErrorCode.RATE_LIMIT_EXCEEDED.value == "E6000"
        assert ErrorCode.PAYMENT_FAILED.value == "E7000"
        assert ErrorCode.EXTERNAL_API_ERROR.value == "E8000"
        assert ErrorCode.INTERNAL_SERVER_ERROR.value == "E9000"
    
    def test_error_category_mapping(self):
        """Test error code to category mapping."""
        assert ErrorCode.VALIDATION_FAILED.category == ErrorCategory.VALIDATION
        assert ErrorCode.INVALID_CREDENTIALS.category == ErrorCategory.AUTHENTICATION
        assert ErrorCode.INSUFFICIENT_PERMISSIONS.category == ErrorCategory.AUTHORIZATION
        assert ErrorCode.RESOURCE_NOT_FOUND.category == ErrorCategory.NOT_FOUND
        assert ErrorCode.RESOURCE_ALREADY_EXISTS.category == ErrorCategory.CONFLICT
        assert ErrorCode.RATE_LIMIT_EXCEEDED.category == ErrorCategory.RATE_LIMIT
        assert ErrorCode.PAYMENT_FAILED.category == ErrorCategory.PAYMENT
        assert ErrorCode.EXTERNAL_API_ERROR.category == ErrorCategory.EXTERNAL_SERVICE
        assert ErrorCode.INTERNAL_SERVER_ERROR.category == ErrorCategory.INTERNAL_SERVER
    
    def test_http_status_mapping(self):
        """Test error code to HTTP status mapping."""
        assert ErrorCode.VALIDATION_FAILED.http_status == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert ErrorCode.INVALID_CREDENTIALS.http_status == status.HTTP_401_UNAUTHORIZED
        assert ErrorCode.INSUFFICIENT_PERMISSIONS.http_status == status.HTTP_403_FORBIDDEN
        assert ErrorCode.RESOURCE_NOT_FOUND.http_status == status.HTTP_404_NOT_FOUND
        assert ErrorCode.RESOURCE_ALREADY_EXISTS.http_status == status.HTTP_409_CONFLICT
        assert ErrorCode.RATE_LIMIT_EXCEEDED.http_status == status.HTTP_429_TOO_MANY_REQUESTS
        assert ErrorCode.PAYMENT_FAILED.http_status == status.HTTP_402_PAYMENT_REQUIRED
        assert ErrorCode.EXTERNAL_API_ERROR.http_status == status.HTTP_502_BAD_GATEWAY
        assert ErrorCode.INTERNAL_SERVER_ERROR.http_status == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestErrorDetail:
    """Test error detail schema."""
    
    def test_error_detail_creation(self):
        """Test creating error detail."""
        detail = ErrorDetail(
            field="email",
            message="Invalid email format",
            code="invalid_email",
            value="invalid-email"
        )
        
        assert detail.field == "email"
        assert detail.message == "Invalid email format"
        assert detail.code == "invalid_email"
        assert detail.value == "invalid-email"
    
    def test_error_detail_minimal(self):
        """Test creating error detail with minimal information."""
        detail = ErrorDetail(message="Something went wrong")
        
        assert detail.field is None
        assert detail.message == "Something went wrong"
        assert detail.code is None
        assert detail.value is None
    
    def test_message_validation(self):
        """Test error message validation."""
        # Empty message should be replaced
        detail = ErrorDetail(message="")
        assert detail.message == "An error occurred"
        
        # Whitespace-only message should be replaced
        detail = ErrorDetail(message="   ")
        assert detail.message == "An error occurred"
        
        # Valid message should be stripped
        detail = ErrorDetail(message="  Valid message  ")
        assert detail.message == "Valid message"


class TestErrorResponse:
    """Test error response schema."""
    
    def test_error_response_creation(self):
        """Test creating error response."""
        details = [
            ErrorDetail(field="email", message="Invalid email", code="invalid_email"),
            ErrorDetail(field="password", message="Too weak", code="weak_password")
        ]
        
        response = ErrorResponse(
            error=ErrorCode.VALIDATION_FAILED,
            message="Validation failed",
            details=details,
            path="/api/v1/users",
            method="POST"
        )
        
        assert response.error == ErrorCode.VALIDATION_FAILED
        assert response.message == "Validation failed"
        assert len(response.details) == 2
        assert response.path == "/api/v1/users"
        assert response.method == "POST"
        assert response.category == ErrorCategory.VALIDATION  # Auto-assigned
        assert response.severity == ErrorSeverity.MEDIUM  # Default
        assert response.retryable is False  # Default
        assert isinstance(response.timestamp, datetime)
        assert isinstance(response.request_id, str)
    
    def test_automatic_category_assignment(self):
        """Test automatic category assignment from error code."""
        response = ErrorResponse(
            error=ErrorCode.INVALID_CREDENTIALS,
            message="Invalid credentials"
        )
        
        assert response.category == ErrorCategory.AUTHENTICATION
    
    def test_details_validation(self):
        """Test error details validation."""
        # Test empty details default
        response = ErrorResponse(
            error=ErrorCode.VALIDATION_FAILED,
            message="Test error"
        )
        assert response.details == []
        
        # Test details limit
        too_many_details = [
            ErrorDetail(message=f"Error {i}") for i in range(60)
        ]
        
        response = ErrorResponse(
            error=ErrorCode.VALIDATION_FAILED,
            message="Test error",
            details=too_many_details
        )
        
        assert len(response.details) == 50  # Should be limited to 50


class TestValidationErrorResponse:
    """Test validation error response."""
    
    def test_validation_error_defaults(self):
        """Test validation error response defaults."""
        response = ValidationErrorResponse(message="Validation failed")
        
        assert response.error == ErrorCode.VALIDATION_FAILED
        assert response.category == ErrorCategory.VALIDATION
        assert response.message == "Validation failed"
    
    def test_from_pydantic_error(self):
        """Test creating validation error from Pydantic error."""
        # Create a Pydantic validation error
        from pydantic import BaseModel, Field
        
        class TestModel(BaseModel):
            email: str = Field(..., min_length=5)
            age: int = Field(..., ge=0)
        
        try:
            TestModel(email="abc", age=-1)
        except ValidationError as exc:
            response = ValidationErrorResponse.from_pydantic_error(
                exc, path="/api/v1/test"
            )
            
            assert response.error == ErrorCode.VALIDATION_FAILED
            assert response.message == "Validation failed"
            assert response.path == "/api/v1/test"
            assert len(response.details) > 0
            
            # Check that field errors are captured
            field_names = [detail.field for detail in response.details if detail.field]
            assert any("email" in field for field in field_names)
            assert any("age" in field for field in field_names)


class TestBusinessLogicErrorResponse:
    """Test business logic error response."""
    
    def test_business_logic_error_defaults(self):
        """Test business logic error defaults."""
        response = BusinessLogicErrorResponse(
            error=ErrorCode.INSUFFICIENT_FUNDS,
            message="Not enough funds"
        )
        
        assert response.category == ErrorCategory.BUSINESS_LOGIC
        assert response.error == ErrorCode.INSUFFICIENT_FUNDS
        assert response.message == "Not enough funds"
    
    def test_insufficient_funds_factory(self):
        """Test insufficient funds error factory method."""
        response = BusinessLogicErrorResponse.insufficient_funds(
            required_amount=1000.0,
            available_amount=500.0
        )
        
        assert response.error == ErrorCode.INSUFFICIENT_FUNDS
        assert "1000.0" in response.message
        assert "500.0" in response.message
        assert response.severity == ErrorSeverity.MEDIUM
        assert response.retryable is True
    
    def test_campaign_budget_exceeded_factory(self):
        """Test campaign budget exceeded error factory method."""
        campaign_id = str(uuid4())
        response = BusinessLogicErrorResponse.campaign_budget_exceeded(campaign_id)
        
        assert response.error == ErrorCode.VALIDATION_FAILED
        assert campaign_id in response.message
        assert response.severity == ErrorSeverity.HIGH
        assert response.retryable is False


class TestErrorResponseFactory:
    """Test error response factory."""
    
    def test_create_error_response(self):
        """Test creating error response with factory."""
        details = [ErrorDetail(message="Test detail")]
        
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            message="Custom message",
            details=details,
            path="/api/v1/test",
            method="GET"
        )
        
        assert response.error == ErrorCode.RESOURCE_NOT_FOUND
        assert response.message == "Custom message"
        assert response.details == details
        assert response.path == "/api/v1/test"
        assert response.method == "GET"
        assert response.category == ErrorCategory.NOT_FOUND
    
    def test_default_message_assignment(self):
        """Test default message assignment."""
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.VALIDATION_FAILED
        )
        
        assert response.message == "Validation failed"
    
    def test_severity_assignment(self):
        """Test automatic severity assignment."""
        # High severity error
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR
        )
        assert response.severity == ErrorSeverity.HIGH
        
        # Low severity error
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.VALIDATION_FAILED
        )
        assert response.severity == ErrorSeverity.LOW
        
        # Medium severity error (default)
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.INSUFFICIENT_PERMISSIONS
        )
        assert response.severity == ErrorSeverity.MEDIUM
    
    def test_retryable_assignment(self):
        """Test automatic retryable assignment."""
        # Retryable error
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED
        )
        assert response.retryable is True
        
        # Non-retryable error
        response = ErrorResponseFactory.create_error_response(
            error_code=ErrorCode.VALIDATION_FAILED
        )
        assert response.retryable is False


class TestStructuredHTTPException:
    """Test structured HTTP exception."""
    
    def test_structured_http_exception_creation(self):
        """Test creating structured HTTP exception."""
        details = [ErrorDetail(field="email", message="Invalid email")]
        
        exception = StructuredHTTPException(
            error_code=ErrorCode.VALIDATION_FAILED,
            message="Custom validation error",
            details=details,
            path="/api/v1/users"
        )
        
        assert exception.status_code == ErrorCode.VALIDATION_FAILED.http_status
        assert isinstance(exception.detail, dict)
        
        # Check that error response is properly structured
        error_dict = exception.detail
        assert error_dict["error"] == ErrorCode.VALIDATION_FAILED.value
        assert error_dict["message"] == "Custom validation error"
        assert len(error_dict["details"]) == 1
        assert error_dict["path"] == "/api/v1/users"
    
    def test_default_message_handling(self):
        """Test default message handling in exception."""
        exception = StructuredHTTPException(
            error_code=ErrorCode.RESOURCE_NOT_FOUND
        )
        
        assert exception.status_code == status.HTTP_404_NOT_FOUND
        error_dict = exception.detail
        assert error_dict["message"] == "Resource not found"  # Default message
    
    def test_different_error_codes(self):
        """Test different error codes produce correct HTTP status."""
        test_cases = [
            (ErrorCode.VALIDATION_FAILED, status.HTTP_422_UNPROCESSABLE_ENTITY),
            (ErrorCode.INVALID_CREDENTIALS, status.HTTP_401_UNAUTHORIZED),
            (ErrorCode.INSUFFICIENT_PERMISSIONS, status.HTTP_403_FORBIDDEN),
            (ErrorCode.RESOURCE_NOT_FOUND, status.HTTP_404_NOT_FOUND),
            (ErrorCode.RATE_LIMIT_EXCEEDED, status.HTTP_429_TOO_MANY_REQUESTS),
            (ErrorCode.INTERNAL_SERVER_ERROR, status.HTTP_500_INTERNAL_SERVER_ERROR),
        ]
        
        for error_code, expected_status in test_cases:
            exception = StructuredHTTPException(error_code=error_code)
            assert exception.status_code == expected_status


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
