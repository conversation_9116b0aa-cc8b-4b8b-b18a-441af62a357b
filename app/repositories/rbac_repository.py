"""
Role-Based Access Control (RBAC) repository for Culture Connect Backend API.

This module provides comprehensive database operations for RBAC management including:
- Permission grant CRUD operations
- Access control audit logging
- Role hierarchy management
- Performance-optimized queries for permission checking

Implements Task 2.2.3 requirements for complete role-based access control
with production-grade database operations and audit logging.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func, desc, asc
from sqlalchemy.orm import selectinload

from app.models.rbac_models import (
    RoleHierarchy, PermissionGrant, AccessControlLog,
    AccessDecision, ResourceType
)
from app.models.user import User
from app.repositories.base import BaseRepository
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class RBACRepository(BaseRepository):
    """
    Repository for RBAC database operations.

    Provides comprehensive database operations for role-based access control
    including permission grants, audit logging, and role hierarchy management
    with performance optimization and proper error handling.
    """

    def __init__(self, db: AsyncSession):
        super().__init__(db)

    # Permission Grant Operations
    async def create_permission_grant(
        self,
        user_id: Optional[str] = None,
        role: Optional[str] = None,
        permission: str = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        granted_by: Optional[str] = None,
        expires_at: Optional[datetime] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PermissionGrant:
        """
        Create a new permission grant.

        Args:
            user_id: User ID for user-specific permissions
            role: Role for role-based permissions
            permission: Permission to grant
            resource_type: Resource type for resource-specific permissions
            resource_id: Resource ID for resource-specific permissions
            granted_by: User who granted the permission
            expires_at: When permission expires
            reason: Reason for granting permission
            metadata: Additional metadata

        Returns:
            PermissionGrant: Created permission grant

        Raises:
            Exception: If creation fails
        """
        try:
            permission_grant = PermissionGrant(
                user_id=user_id,
                role=role,
                permission=permission,
                resource_type=resource_type,
                resource_id=resource_id,
                granted_by=granted_by,
                granted_at=datetime.utcnow(),
                expires_at=expires_at,
                reason=reason,
                grant_metadata=metadata,
                is_active=True
            )

            self.db.add(permission_grant)
            await self.db.commit()
            await self.db.refresh(permission_grant)

            logger.info(
                f"Permission grant created: {permission} for {user_id or role}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "permission": permission,
                    "user_id": user_id,
                    "role": role,
                    "granted_by": granted_by
                }
            )

            return permission_grant

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to create permission grant: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "permission": permission,
                    "user_id": user_id,
                    "role": role,
                    "error": str(e)
                }
            )
            raise

    async def get_permission_grants(
        self,
        user_id: Optional[str] = None,
        role: Optional[str] = None,
        permission: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        is_active: bool = True,
        include_expired: bool = False
    ) -> List[PermissionGrant]:
        """
        Get permission grants based on criteria.

        Args:
            user_id: Filter by user ID
            role: Filter by role
            permission: Filter by permission
            resource_type: Filter by resource type
            resource_id: Filter by resource ID
            is_active: Filter by active status
            include_expired: Include expired permissions

        Returns:
            List[PermissionGrant]: Matching permission grants
        """
        try:
            query = select(PermissionGrant)

            # Apply filters
            conditions = []

            if user_id is not None:
                conditions.append(PermissionGrant.user_id == user_id)

            if role is not None:
                conditions.append(PermissionGrant.role == role)

            if permission is not None:
                conditions.append(PermissionGrant.permission == permission)

            if resource_type is not None:
                conditions.append(PermissionGrant.resource_type == resource_type)

            if resource_id is not None:
                conditions.append(PermissionGrant.resource_id == resource_id)

            if is_active is not None:
                conditions.append(PermissionGrant.is_active == is_active)

            if not include_expired:
                conditions.append(
                    or_(
                        PermissionGrant.expires_at.is_(None),
                        PermissionGrant.expires_at > datetime.utcnow()
                    )
                )

            if conditions:
                query = query.where(and_(*conditions))

            # Order by creation date
            query = query.order_by(desc(PermissionGrant.created_at))

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(
                f"Failed to get permission grants: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "role": role,
                    "permission": permission,
                    "error": str(e)
                }
            )
            raise

    async def update_permission_grant(
        self,
        grant_id: str,
        expires_at: Optional[datetime] = None,
        is_active: Optional[bool] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[PermissionGrant]:
        """
        Update a permission grant.

        Args:
            grant_id: Permission grant ID
            expires_at: New expiration date
            is_active: New active status
            reason: Updated reason
            metadata: Updated metadata

        Returns:
            PermissionGrant: Updated permission grant or None if not found
        """
        try:
            # Build update data
            update_data = {"updated_at": datetime.utcnow()}

            if expires_at is not None:
                update_data["expires_at"] = expires_at

            if is_active is not None:
                update_data["is_active"] = is_active

            if reason is not None:
                update_data["reason"] = reason

            if metadata is not None:
                update_data["grant_metadata"] = metadata

            # Update permission grant
            stmt = update(PermissionGrant).where(
                PermissionGrant.id == grant_id
            ).values(**update_data)

            result = await self.db.execute(stmt)

            if result.rowcount > 0:
                await self.db.commit()

                # Get updated grant
                query = select(PermissionGrant).where(PermissionGrant.id == grant_id)
                result = await self.db.execute(query)
                grant = result.scalar_one_or_none()

                logger.info(
                    f"Permission grant updated: {grant_id}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "grant_id": grant_id,
                        "updated_fields": list(update_data.keys())
                    }
                )

                return grant

            return None

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to update permission grant: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "grant_id": grant_id,
                    "error": str(e)
                }
            )
            raise

    async def delete_permission_grant(self, grant_id: str) -> bool:
        """
        Delete a permission grant.

        Args:
            grant_id: Permission grant ID

        Returns:
            bool: True if deleted, False if not found
        """
        try:
            stmt = delete(PermissionGrant).where(PermissionGrant.id == grant_id)
            result = await self.db.execute(stmt)

            if result.rowcount > 0:
                await self.db.commit()

                logger.info(
                    f"Permission grant deleted: {grant_id}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "grant_id": grant_id
                    }
                )

                return True

            return False

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to delete permission grant: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "grant_id": grant_id,
                    "error": str(e)
                }
            )
            raise

    # Access Control Logging
    async def log_access_control_decision(
        self,
        user_id: Optional[str],
        user_role: Optional[str],
        endpoint: str,
        method: str,
        required_permission: Optional[str],
        resource_type: Optional[str],
        resource_id: Optional[str],
        decision: AccessDecision,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        correlation_id_value: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AccessControlLog:
        """
        Log an access control decision for audit purposes.

        Args:
            user_id: User who made the request
            user_role: User role at time of request
            endpoint: API endpoint accessed
            method: HTTP method used
            required_permission: Required permission
            resource_type: Resource type accessed
            resource_id: Resource ID accessed
            decision: Access control decision
            ip_address: Client IP address
            user_agent: Client user agent
            correlation_id_value: Request correlation ID
            response_time_ms: Response time in milliseconds
            reason: Reason for access decision
            metadata: Additional metadata

        Returns:
            AccessControlLog: Created log entry
        """
        try:
            log_entry = AccessControlLog(
                user_id=user_id,
                user_role=user_role,
                endpoint=endpoint,
                method=method,
                required_permission=required_permission,
                resource_type=resource_type,
                resource_id=resource_id,
                decision=decision,
                ip_address=ip_address,
                user_agent=user_agent,
                correlation_id=correlation_id_value,
                request_timestamp=datetime.utcnow(),
                response_time_ms=response_time_ms,
                reason=reason,
                log_metadata=metadata
            )

            self.db.add(log_entry)
            await self.db.commit()
            await self.db.refresh(log_entry)

            return log_entry

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to log access control decision: {str(e)}",
                extra={
                    "correlation_id": correlation_id_value,
                    "user_id": user_id,
                    "endpoint": endpoint,
                    "decision": decision.value,
                    "error": str(e)
                }
            )
            raise

    async def get_access_control_logs(
        self,
        user_id: Optional[str] = None,
        endpoint: Optional[str] = None,
        decision: Optional[AccessDecision] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[AccessControlLog], int]:
        """
        Get access control logs with filtering and pagination.

        Args:
            user_id: Filter by user ID
            endpoint: Filter by endpoint
            decision: Filter by decision
            start_date: Filter by start date
            end_date: Filter by end date
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            Tuple[List[AccessControlLog], int]: Logs and total count
        """
        try:
            # Build base query
            query = select(AccessControlLog)
            count_query = select(func.count(AccessControlLog.id))

            # Apply filters
            conditions = []

            if user_id is not None:
                conditions.append(AccessControlLog.user_id == user_id)

            if endpoint is not None:
                conditions.append(AccessControlLog.endpoint.ilike(f"%{endpoint}%"))

            if decision is not None:
                conditions.append(AccessControlLog.decision == decision)

            if start_date is not None:
                conditions.append(AccessControlLog.request_timestamp >= start_date)

            if end_date is not None:
                conditions.append(AccessControlLog.request_timestamp <= end_date)

            if conditions:
                query = query.where(and_(*conditions))
                count_query = count_query.where(and_(*conditions))

            # Get total count
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()

            # Apply pagination and ordering
            query = query.order_by(desc(AccessControlLog.request_timestamp))
            query = query.offset(offset).limit(limit)

            # Execute query
            result = await self.db.execute(query)
            logs = result.scalars().all()

            return logs, total_count

        except Exception as e:
            logger.error(
                f"Failed to get access control logs: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "endpoint": endpoint,
                    "error": str(e)
                }
            )
            raise

    async def get_access_control_stats(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get access control statistics for monitoring and analytics.

        Args:
            start_date: Statistics start date
            end_date: Statistics end date

        Returns:
            Dict[str, Any]: Access control statistics
        """
        try:
            # Default to last 24 hours if no dates provided
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(hours=24)

            # Base conditions
            conditions = [
                AccessControlLog.request_timestamp >= start_date,
                AccessControlLog.request_timestamp <= end_date
            ]

            # Total requests
            total_query = select(func.count(AccessControlLog.id)).where(and_(*conditions))
            total_result = await self.db.execute(total_query)
            total_requests = total_result.scalar() or 0

            # Requests by decision
            decision_query = select(
                AccessControlLog.decision,
                func.count(AccessControlLog.id)
            ).where(and_(*conditions)).group_by(AccessControlLog.decision)

            decision_result = await self.db.execute(decision_query)
            decision_counts = {row[0].value: row[1] for row in decision_result}

            # Average response time
            avg_time_query = select(
                func.avg(AccessControlLog.response_time_ms)
            ).where(
                and_(*conditions, AccessControlLog.response_time_ms.is_not(None))
            )
            avg_time_result = await self.db.execute(avg_time_query)
            avg_response_time = avg_time_result.scalar() or 0

            # Top endpoints
            endpoint_query = select(
                AccessControlLog.endpoint,
                func.count(AccessControlLog.id).label('count')
            ).where(and_(*conditions)).group_by(
                AccessControlLog.endpoint
            ).order_by(desc('count')).limit(10)

            endpoint_result = await self.db.execute(endpoint_query)
            top_endpoints = [{"endpoint": row[0], "count": row[1]} for row in endpoint_result]

            return {
                "total_requests": total_requests,
                "granted_requests": decision_counts.get("granted", 0),
                "denied_requests": decision_counts.get("denied", 0),
                "error_requests": decision_counts.get("error", 0),
                "success_rate": (decision_counts.get("granted", 0) / max(total_requests, 1)) * 100,
                "avg_response_time_ms": float(avg_response_time),
                "top_endpoints": top_endpoints,
                "period_start": start_date,
                "period_end": end_date
            }

        except Exception as e:
            logger.error(
                f"Failed to get access control stats: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "start_date": start_date,
                    "end_date": end_date,
                    "error": str(e)
                }
            )
            raise