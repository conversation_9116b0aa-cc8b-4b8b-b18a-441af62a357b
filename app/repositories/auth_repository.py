"""
Authentication repository for Culture Connect Backend API.

This module provides data access layer for authentication-related operations
including user authentication, token blacklisting, security event logging,
and session management.

Implements Task 2.1.1 requirements for JWT authentication with comprehensive
repository patterns following the established Phase 1 foundation.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.models.user import User, TokenBlacklist, SecurityEvent, UserSession, APIKey
from app.repositories.base import BaseRepository
from app.core.security import UserRole

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository[User]):
    """
    Enhanced user repository for authentication operations.

    Provides comprehensive user data access methods with performance optimization
    and security features for authentication workflows.
    """

    def __init__(self, db):
        """Initialize user repository."""
        super().__init__(User, db)

    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address with optimized query.

        Args:
            email: User email address

        Returns:
            User instance if found, None otherwise
        """
        try:
            stmt = select(User).where(
                and_(
                    func.lower(User.email) == email.lower(),
                    User.is_active == True
                )
            )
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if user:
                logger.debug(f"User found by email: {email}")

            return user

        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {str(e)}")
            return None

    async def get_by_reset_token(self, token: str) -> Optional[User]:
        """
        Get user by valid password reset token.

        Args:
            token: Password reset token

        Returns:
            User instance if token is valid, None otherwise
        """
        try:
            stmt = select(User).where(
                and_(
                    User.password_reset_token == token,
                    User.password_reset_expires > datetime.utcnow(),
                    User.is_active == True
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get user by reset token: {str(e)}")
            return None

    async def get_with_sessions(self, user_id: int) -> Optional[User]:
        """
        Get user with active sessions loaded.

        Args:
            user_id: User ID

        Returns:
            User instance with sessions if found
        """
        try:
            stmt = select(User).options(
                selectinload(User.sessions).where(UserSession.is_active == True)
            ).where(User.id == user_id)

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to get user with sessions {user_id}: {str(e)}")
            return None

    async def update_login_info(
        self,
        user_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Update user login information.

        Args:
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent

        Returns:
            bool: True if update successful
        """
        try:
            user = await self.get(user_id)
            if not user:
                return False

            user.last_login = datetime.utcnow()
            user.failed_login_attempts = 0

            # Update additional login info if provided
            if ip_address:
                user.last_login_ip = ip_address
            if user_agent:
                user.last_login_user_agent = user_agent

            await self.db.commit()
            logger.info(f"Login info updated for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to update login info for user {user_id}: {str(e)}")
            await self.db.rollback()
            return False

    async def increment_failed_login(self, user_id: int) -> bool:
        """
        Increment failed login attempts counter.

        Args:
            user_id: User ID

        Returns:
            bool: True if update successful
        """
        try:
            user = await self.get(user_id)
            if not user:
                return False

            user.failed_login_attempts += 1

            # Lock account if too many failed attempts
            if user.failed_login_attempts >= 5:
                user.lock_account(duration_minutes=30)
                logger.warning(f"Account locked for user {user_id} due to failed login attempts")

            await self.db.commit()
            return True

        except Exception as e:
            logger.error(f"Failed to increment failed login for user {user_id}: {str(e)}")
            await self.db.rollback()
            return False

    async def get_active_users_count(self) -> int:
        """
        Get count of active users.

        Returns:
            int: Number of active users
        """
        try:
            stmt = select(func.count(User.id)).where(User.is_active == True)
            result = await self.db.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            logger.error(f"Failed to get active users count: {str(e)}")
            return 0


class TokenBlacklistRepository(BaseRepository[TokenBlacklist]):
    """
    Token blacklist repository for secure logout functionality.

    Manages blacklisted JWT tokens with automatic cleanup and performance optimization.
    """

    async def blacklist_token(
        self,
        jti: str,
        user_id: int,
        token_type: str,
        expires_at: datetime,
        reason: str = "logout",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> TokenBlacklist:
        """
        Add token to blacklist.

        Args:
            jti: JWT ID
            user_id: User ID
            token_type: Token type (access or refresh)
            expires_at: Token expiration time
            reason: Blacklist reason
            ip_address: Client IP address
            user_agent: Client user agent

        Returns:
            TokenBlacklist: Created blacklist entry
        """
        try:
            blacklist_entry = TokenBlacklist(
                jti=jti,
                user_id=user_id,
                token_type=token_type,
                expires_at=expires_at,
                reason=reason,
                ip_address=ip_address,
                user_agent=user_agent
            )

            self.db.add(blacklist_entry)
            await self.db.commit()

            logger.info(f"Token {jti} blacklisted for user {user_id}")
            return blacklist_entry

        except Exception as e:
            logger.error(f"Failed to blacklist token {jti}: {str(e)}")
            await self.db.rollback()
            raise

    async def is_token_blacklisted(self, jti: str) -> bool:
        """
        Check if token is blacklisted.

        Args:
            jti: JWT ID to check

        Returns:
            bool: True if token is blacklisted
        """
        try:
            stmt = select(TokenBlacklist).where(TokenBlacklist.jti == jti)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none() is not None

        except Exception as e:
            logger.error(f"Failed to check token blacklist for {jti}: {str(e)}")
            return False

    async def cleanup_expired_tokens(self) -> int:
        """
        Remove expired blacklisted tokens.

        Returns:
            int: Number of tokens cleaned up
        """
        try:
            # Get expired tokens
            stmt = select(TokenBlacklist).where(
                TokenBlacklist.expires_at < datetime.utcnow()
            )
            result = await self.db.execute(stmt)
            expired_tokens = result.scalars().all()

            # Delete expired tokens
            for token in expired_tokens:
                await self.db.delete(token)

            await self.db.commit()

            count = len(expired_tokens)
            logger.info(f"Cleaned up {count} expired blacklisted tokens")
            return count

        except Exception as e:
            logger.error(f"Failed to cleanup expired tokens: {str(e)}")
            await self.db.rollback()
            return 0

    async def get_user_blacklisted_tokens(
        self,
        user_id: int,
        limit: int = 50
    ) -> List[TokenBlacklist]:
        """
        Get user's blacklisted tokens.

        Args:
            user_id: User ID
            limit: Maximum number of tokens to return

        Returns:
            List[TokenBlacklist]: User's blacklisted tokens
        """
        try:
            stmt = select(TokenBlacklist).where(
                TokenBlacklist.user_id == user_id
            ).order_by(desc(TokenBlacklist.blacklisted_at)).limit(limit)

            result = await self.db.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Failed to get blacklisted tokens for user {user_id}: {str(e)}")
            return []


class SecurityEventRepository(BaseRepository[SecurityEvent]):
    """
    Security event repository for audit logging and monitoring.

    Provides comprehensive security event tracking with performance optimization
    and advanced querying capabilities.
    """

    async def log_event(
        self,
        event_type: str,
        description: str,
        user_id: Optional[int] = None,
        event_category: str = "authentication",
        severity: str = "info",
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> SecurityEvent:
        """
        Log a security event.

        Args:
            event_type: Type of security event
            description: Event description
            user_id: Associated user ID
            event_category: Event category
            severity: Event severity level
            details: Additional event details
            ip_address: Client IP address
            user_agent: Client user agent
            session_id: Session ID
            correlation_id: Correlation ID for tracking

        Returns:
            SecurityEvent: Created security event
        """
        try:
            event = SecurityEvent(
                user_id=user_id,
                event_type=event_type,
                event_category=event_category,
                severity=severity,
                description=description,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                correlation_id=correlation_id
            )

            self.db.add(event)
            await self.db.commit()

            logger.info(f"Security event logged: {event_type} for user {user_id}")
            return event

        except Exception as e:
            logger.error(f"Failed to log security event {event_type}: {str(e)}")
            await self.db.rollback()
            raise

    async def get_user_events(
        self,
        user_id: int,
        limit: int = 100,
        severity: Optional[str] = None,
        event_category: Optional[str] = None
    ) -> List[SecurityEvent]:
        """
        Get security events for a user.

        Args:
            user_id: User ID
            limit: Maximum number of events
            severity: Filter by severity level
            event_category: Filter by event category

        Returns:
            List[SecurityEvent]: User's security events
        """
        try:
            stmt = select(SecurityEvent).where(SecurityEvent.user_id == user_id)

            if severity:
                stmt = stmt.where(SecurityEvent.severity == severity)
            if event_category:
                stmt = stmt.where(SecurityEvent.event_category == event_category)

            stmt = stmt.order_by(desc(SecurityEvent.created_at)).limit(limit)

            result = await self.db.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Failed to get security events for user {user_id}: {str(e)}")
            return []

    async def get_unresolved_events(
        self,
        severity: Optional[str] = None,
        limit: int = 100
    ) -> List[SecurityEvent]:
        """
        Get unresolved security events.

        Args:
            severity: Filter by severity level
            limit: Maximum number of events

        Returns:
            List[SecurityEvent]: Unresolved security events
        """
        try:
            stmt = select(SecurityEvent).where(SecurityEvent.resolved == False)

            if severity:
                stmt = stmt.where(SecurityEvent.severity == severity)

            stmt = stmt.order_by(desc(SecurityEvent.created_at)).limit(limit)

            result = await self.db.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Failed to get unresolved security events: {str(e)}")
            return []

    async def resolve_event(self, event_id: int, resolved_by_user_id: int) -> bool:
        """
        Mark security event as resolved.

        Args:
            event_id: Security event ID
            resolved_by_user_id: User who resolved the event

        Returns:
            bool: True if resolution successful
        """
        try:
            event = await self.get(event_id)
            if not event:
                return False

            event.resolve(resolved_by_user_id)
            await self.db.commit()

            logger.info(f"Security event {event_id} resolved by user {resolved_by_user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to resolve security event {event_id}: {str(e)}")
            await self.db.rollback()
            return False


class UserSessionRepository(BaseRepository[UserSession]):
    """
    User session repository for session management and tracking.

    Provides session lifecycle management with security features and cleanup.
    """

    async def create_session(
        self,
        user_id: int,
        session_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> UserSession:
        """
        Create a new user session.

        Args:
            user_id: User ID
            session_id: Unique session identifier
            ip_address: Client IP address
            user_agent: Client user agent
            expires_at: Session expiration time

        Returns:
            UserSession: Created session
        """
        try:
            if not expires_at:
                expires_at = datetime.utcnow() + timedelta(days=7)  # Default 7 days

            session = UserSession(
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )

            self.db.add(session)
            await self.db.commit()

            logger.info(f"Session created for user {user_id}: {session_id}")
            return session

        except Exception as e:
            logger.error(f"Failed to create session for user {user_id}: {str(e)}")
            await self.db.rollback()
            raise

    async def get_active_sessions(self, user_id: int) -> List[UserSession]:
        """
        Get active sessions for a user.

        Args:
            user_id: User ID

        Returns:
            List[UserSession]: Active sessions
        """
        try:
            stmt = select(UserSession).where(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow()
                )
            ).order_by(desc(UserSession.last_activity))

            result = await self.db.execute(stmt)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Failed to get active sessions for user {user_id}: {str(e)}")
            return []

    async def deactivate_session(self, session_id: str) -> bool:
        """
        Deactivate a session.

        Args:
            session_id: Session ID to deactivate

        Returns:
            bool: True if deactivation successful
        """
        try:
            stmt = select(UserSession).where(UserSession.session_id == session_id)
            result = await self.db.execute(stmt)
            session = result.scalar_one_or_none()

            if not session:
                return False

            session.deactivate()
            await self.db.commit()

            logger.info(f"Session deactivated: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to deactivate session {session_id}: {str(e)}")
            await self.db.rollback()
            return False

    async def cleanup_expired_sessions(self) -> int:
        """
        Remove expired sessions.

        Returns:
            int: Number of sessions cleaned up
        """
        try:
            # Get expired sessions
            stmt = select(UserSession).where(
                UserSession.expires_at < datetime.utcnow()
            )
            result = await self.db.execute(stmt)
            expired_sessions = result.scalars().all()

            # Delete expired sessions
            for session in expired_sessions:
                await self.db.delete(session)

            await self.db.commit()

            count = len(expired_sessions)
            logger.info(f"Cleaned up {count} expired sessions")
            return count

        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {str(e)}")
            await self.db.rollback()
            return 0
