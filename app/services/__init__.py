"""
Services package for Culture Connect Backend API.

This package contains business logic services organized by functionality:
- Base service class with common patterns
- Authentication and user management services
- Vendor management services
- Booking and payment services
- Promotional and advertising services
- Communication and notification services
- Analytics and reporting services

Services implement the business logic layer between API endpoints and data repositories.
"""

import logging
from typing import Dict, Any

# Base service imports
from .base import (
    BaseService, ServiceError, service_registry
)

# Import existing services
from .auth_service import AuthService
from .email_service import EmailService
from .password_security_service import PasswordSecurityService

# Import push notification services (Task 2.3.2)
from .push_notification_services import (
    DeviceTokenService, NotificationTemplateService, NotificationDeliveryService,
    NotificationPreferenceService, NotificationQueueService, PushNotificationService
)

# Import vendor services (Task 3.1.1)
from .vendor_service import VendorService

# Import booking services (Task 4.1.1)
from .booking_service import BookingService

# Import booking communication services (Task 4.1.3)
from .booking_communication_service import (
    BookingMessageService, MessageAttachmentService,
    MessageTemplateService, MessageDeliveryService
)

# Import availability services (Task 4.1.2)
from .availability_service import AvailabilityService

# Import payment services (Step 6 - Payment & Transaction Management System)
from .payment_service import PaymentService, PaymentMethodService
from .transaction_service import TransactionService, TransactionEventService
from .payout_service import VendorPayoutService, EscrowAccountService
from .financial_service import RevenueRecordService, ReconciliationRecordService

# Import VPN detection services (Phase 2.2 - VPN Detection Service)
from .vpn_detection_service import VPNDetectionService

# Import geolocation analytics services (Phase 2.2 - Analytics Dashboard)
from .geolocation_analytics_service import GeolocationAnalyticsService

# Import A/B testing services (Phase 2.3 - A/B Testing Framework)
from .ab_testing_service import ABTestingService

# Review services (Task 4.4.1 Phase 4)
from .review_service import ReviewService
from .review_response_service import ReviewResponseService
from .review_moderation_service import ReviewModerationService
from .review_analytics_service import ReviewAnalyticsService

# WebSocket services (Task 6.1.1 Phase 3)
from .websocket_services import (
    WebSocketConnectionService, WebSocketEventService,
    UserPresenceService, WebSocketMetricsService,
    WebSocketRoomService, WebSocketRoomParticipantService
)

# Workflow orchestration services (Task 6.2.2 Phase 2 Step 2)
from .workflow_orchestration_service import WorkflowOrchestrationService

# Advanced scheduler services (Task 6.2.2 Phase 3.3)
from .advanced_scheduler_service import AdvancedSchedulerService
from .enhanced_monitoring_service import EnhancedMonitoringService

# Alerting system services (Task 6.2.2 Phase 4.4)
from .alerting_system_service import AlertingSystemService

# Analytics services (Phase 7.1 - Analytics & Performance System)
from .analytics_service import AnalyticsService
from .dashboard_service import DashboardService
from .reporting_service import ReportingService

# Performance monitoring services (Phase 7.2.3 - Performance Monitoring System)
from .performance_monitoring_service import PerformanceMonitoringService
from .system_health_service import SystemHealthService
from .load_testing_service import LoadTestingService

logger = logging.getLogger(__name__)


def register_core_services() -> None:
    """
    Register core services with the service registry.

    This function registers all core services and their dependencies
    for dependency injection and service discovery.
    """
    try:
        # Register email service (no dependencies)
        service_registry.register(
            service_class=EmailService,
            name="EmailService",
            dependencies=[]
        )

        # Register auth service (depends on email service)
        service_registry.register(
            service_class=AuthService,
            name="AuthService",
            dependencies=["EmailService"]
        )

        # Register password security service (no dependencies)
        service_registry.register(
            service_class=PasswordSecurityService,
            name="PasswordSecurityService",
            dependencies=[]
        )

        logger.info("Core services registered successfully")

    except Exception as e:
        logger.error(f"Failed to register core services: {str(e)}")
        raise ServiceError(
            "Service registration failed",
            error_code="SERVICE_REGISTRATION_FAILED",
            original_error=e
        )


def get_service(service_name: str, **kwargs) -> BaseService:
    """
    Get service instance from registry.

    Args:
        service_name: Name of the service to retrieve
        **kwargs: Additional arguments for service initialization

    Returns:
        BaseService: Service instance

    Raises:
        ServiceError: If service not found or creation fails
    """
    return service_registry.get_service(service_name, **kwargs)


def clear_service_cache() -> None:
    """Clear all cached service instances."""
    service_registry.clear_instances()
    logger.info("Service cache cleared")


def list_registered_services() -> list:
    """Get list of all registered service names."""
    return service_registry.list_services()


def setup_services() -> None:
    """
    Setup and initialize all services.

    This function should be called during application startup
    to register all services and prepare them for use.
    """
    logger.info("Setting up services...")

    # Register core services
    register_core_services()

    # TODO: Register additional services as they are implemented
    # register_vendor_services()
    # register_booking_services()
    # register_payment_services()
    # register_promotional_services()

    logger.info(f"Services setup complete. Registered services: {list_registered_services()}")


# Service lifecycle management
class ServiceLifecycleManager:
    """
    Manages service lifecycle including startup, shutdown, and health checks.
    """

    def __init__(self):
        self.initialized = False
        self.logger = logging.getLogger(f"{__name__}.ServiceLifecycleManager")

    async def startup(self) -> None:
        """Initialize all services during application startup."""
        if self.initialized:
            self.logger.warning("Services already initialized")
            return

        try:
            self.logger.info("Starting service initialization...")

            # Setup service registry
            setup_services()

            # TODO: Initialize any services that require async setup
            # await self._initialize_async_services()

            self.initialized = True
            self.logger.info("Service initialization completed successfully")

        except Exception as e:
            self.logger.error(f"Service initialization failed: {str(e)}")
            raise ServiceError(
                "Service startup failed",
                error_code="SERVICE_STARTUP_FAILED",
                original_error=e
            )

    async def shutdown(self) -> None:
        """Cleanup services during application shutdown."""
        if not self.initialized:
            self.logger.warning("Services not initialized")
            return

        try:
            self.logger.info("Starting service shutdown...")

            # Clear service cache
            clear_service_cache()

            # TODO: Cleanup any services that require async shutdown
            # await self._cleanup_async_services()

            self.initialized = False
            self.logger.info("Service shutdown completed successfully")

        except Exception as e:
            self.logger.error(f"Service shutdown failed: {str(e)}")

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all services.

        Returns:
            Dict[str, Any]: Health check results
        """
        if not self.initialized:
            return {
                "status": "unhealthy",
                "message": "Services not initialized",
                "services": {}
            }

        service_health = {}
        overall_healthy = True

        try:
            # Check each registered service
            for service_name in list_registered_services():
                try:
                    # Basic service instantiation check
                    service = get_service(service_name)
                    service_health[service_name] = {
                        "status": "healthy",
                        "service_id": getattr(service, 'service_id', 'unknown')
                    }
                except Exception as e:
                    service_health[service_name] = {
                        "status": "unhealthy",
                        "error": str(e)
                    }
                    overall_healthy = False

            return {
                "status": "healthy" if overall_healthy else "degraded",
                "message": "Service health check completed",
                "services": service_health,
                "total_services": len(service_health),
                "healthy_services": sum(1 for s in service_health.values() if s["status"] == "healthy")
            }

        except Exception as e:
            self.logger.error(f"Service health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "message": f"Health check failed: {str(e)}",
                "services": service_health
            }


# Global service lifecycle manager
service_lifecycle_manager = ServiceLifecycleManager()


# Export all public components
__all__ = [
    # Base service components
    "BaseService",
    "ServiceError",

    # Service registry and management
    "service_registry",
    "service_lifecycle_manager",

    # Service management functions
    "get_service",
    "setup_services",
    "clear_service_cache",
    "list_registered_services",
    "register_core_services",

    # Existing services
    "AuthService",
    "EmailService",
    "PasswordSecurityService",

    # Push notification services (Task 2.3.2)
    "DeviceTokenService",
    "NotificationTemplateService",
    "NotificationDeliveryService",
    "NotificationPreferenceService",
    "NotificationQueueService",
    "PushNotificationService",

    # Vendor services (Task 3.1.1)
    "VendorService",

    # Booking services (Task 4.1.1)
    "BookingService",

    # Booking communication services (Task 4.1.3)
    "BookingMessageService",
    "MessageAttachmentService",
    "MessageTemplateService",
    "MessageDeliveryService",

    # Availability services (Task 4.1.2)
    "AvailabilityService",

    # Payment services (Step 6 - Payment & Transaction Management System)
    "PaymentService",
    "PaymentMethodService",
    "TransactionService",
    "TransactionEventService",
    "VendorPayoutService",
    "EscrowAccountService",
    "RevenueRecordService",
    "ReconciliationRecordService",

    # VPN detection services (Phase 2.2 - VPN Detection Service)
    "VPNDetectionService",

    # Geolocation analytics services (Phase 2.2 - Analytics Dashboard)
    "GeolocationAnalyticsService",

    # A/B testing services (Phase 2.3 - A/B Testing Framework)
    "ABTestingService",

    # Review services (Task 4.4.1 Phase 4)
    "ReviewService",
    "ReviewResponseService",
    "ReviewModerationService",
    "ReviewAnalyticsService",

    # WebSocket services (Task 6.1.1 Phase 3)
    "WebSocketConnectionService",
    "WebSocketEventService",
    "UserPresenceService",
    "WebSocketMetricsService",
    "WebSocketRoomService",
    "WebSocketRoomParticipantService",

    # Workflow orchestration services (Task 6.2.2 Phase 2 Step 2)
    "WorkflowOrchestrationService",

    # Advanced scheduler services (Task 6.2.2 Phase 3.3)
    "AdvancedSchedulerService",

    # Enhanced monitoring services (Task 6.2.2 Phase 4.3)
    "EnhancedMonitoringService",

    # Alerting system services (Task 6.2.2 Phase 4.4)
    "AlertingSystemService",

    # Analytics services (Phase 7.1 - Analytics & Performance System)
    "AnalyticsService",
    "DashboardService",
    "ReportingService",

    # Performance monitoring services (Phase 7.2.3 - Performance Monitoring System)
    "PerformanceMonitoringService",
    "SystemHealthService",
    "LoadTestingService",
]