"""
Payment API endpoints for Culture Connect Backend API.

This module provides comprehensive payment management endpoints including:
- Payment creation and processing with Paystack integration
- Payment status management and verification
- Payment method management with encrypted storage
- Payment history and analytics

Implements Task 4.3.1 Phase 3 requirements for payment API endpoints with
production-grade FastAPI implementation and seamless integration with payment services.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db_session as get_db
from app.core.deps import get_current_user
from app.models.user import User
from app.models.payment import PaymentStatus
from app.core.payment.config import PaymentProviderType
from app.services.payment_service import PaymentService, PaymentMethodService
from app.services.payment.paystack_service import PaystackService, PaystackError
from app.services.payment.stripe_service import StripeService, StripeError
from app.services.payment.busha_service import BushaService, BushaError
from app.schemas.payment_schemas import (
    PaymentCreate, PaymentResponse, PaymentListResponse,
    PaymentMethodCreate, PaymentMethodResponse, PaymentMethodListResponse,
    PaymentProcessRequest, PaymentProcessResponse
)
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.services.geolocation_service import get_geolocation_service

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/payments", tags=["payments"])


@router.post(
    "/create",
    response_model=PaymentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create geolocation-enhanced payment",
    description="""
    **Create a new payment with intelligent geolocation-based provider routing.**

    This endpoint implements the 4-priority geolocation routing system for optimal payment processing:

    **🚀 4-Priority Routing System:**
    1. **Priority 1: Cryptocurrency** → Busha (BTC, ETH, USDT, USDC)
    2. **Priority 2: User Preference** → Validated against geo-compatibility
    3. **Priority 3: Geolocation-Based** → IP detection with country routing
    4. **Priority 4: Currency-Based Fallback** → Traditional routing

    **🌍 Geographic Provider Selection:**
    - **African Markets** (NG, GH, KE, etc.) → Paystack
    - **Diaspora Markets** (US, UK, CA, EU, etc.) → Stripe
    - **Cryptocurrency** (Global) → Busha

    **🔒 Advanced Features:**
    - **VPN Detection**: Multi-method analysis with risk scoring
    - **Performance Optimization**: <500ms payment creation with caching
    - **Fallback Mechanisms**: Graceful handling of detection failures
    - **Audit Logging**: Complete audit trail with correlation IDs

    **📊 Real-time Analytics:**
    - Provider performance tracking by geography
    - Conversion rate optimization insights
    - VPN impact assessment on routing decisions
    """,
    responses={
        201: {
            "description": "Payment created successfully with geolocation routing",
            "content": {
                "application/json": {
                    "examples": {
                        "nigerian_paystack_payment": {
                            "summary": "Nigerian IP → Paystack Routing",
                            "description": "Payment from Nigerian IP automatically routed to Paystack",
                            "value": {
                                "id": "pay_123456789",
                                "booking_id": 123,
                                "user_id": 456,
                                "vendor_id": 789,
                                "amount": 50000.00,
                                "currency": "USD",
                                "provider": "paystack",
                                "status": "pending",
                                "transaction_reference": "CC_PAY_20240115_123456",
                                "payment_url": "https://checkout.paystack.com/abc123",
                                "geolocation_metadata": {
                                    "detected_country": "NG",
                                    "country_name": "Nigeria",
                                    "continent": "AF",
                                    "routing_decision": "geolocation_based",
                                    "provider_selection_reason": "Nigerian IP detected, optimal for Paystack processing",
                                    "detection_confidence": 0.98,
                                    "detection_time_ms": 45,
                                    "vpn_detected": False,
                                    "risk_score": 0.1
                                },
                                "created_at": "2024-01-15T10:30:00Z",
                                "expires_at": "2024-01-15T11:30:00Z"
                            }
                        },
                        "us_stripe_payment": {
                            "summary": "US IP → Stripe Routing",
                            "description": "Payment from US IP automatically routed to Stripe",
                            "value": {
                                "id": "pay_987654321",
                                "booking_id": 124,
                                "user_id": 456,
                                "vendor_id": 789,
                                "amount": 250.00,
                                "currency": "USD",
                                "provider": "stripe",
                                "status": "pending",
                                "transaction_reference": "CC_PAY_20240115_987654",
                                "payment_url": "stripe://payment_intent/pi_abc123_secret_def456",
                                "geolocation_metadata": {
                                    "detected_country": "US",
                                    "country_name": "United States",
                                    "continent": "NA",
                                    "routing_decision": "geolocation_based",
                                    "provider_selection_reason": "US IP detected, optimal for Stripe processing",
                                    "detection_confidence": 0.96,
                                    "detection_time_ms": 38,
                                    "vpn_detected": False,
                                    "risk_score": 0.05
                                },
                                "created_at": "2024-01-15T10:35:00Z",
                                "expires_at": "2024-01-15T11:35:00Z"
                            }
                        },
                        "vpn_detected_payment": {
                            "summary": "VPN Detected → Adjusted Routing",
                            "description": "Payment with VPN detection showing adjusted routing and risk assessment",
                            "value": {
                                "id": "pay_555666777",
                                "booking_id": 125,
                                "user_id": 456,
                                "vendor_id": 789,
                                "amount": 75000.00,
                                "currency": "NGN",
                                "provider": "stripe",
                                "status": "pending",
                                "transaction_reference": "CC_PAY_20240115_555666",
                                "payment_url": "stripe://payment_intent/pi_vpn123_secret_xyz789",
                                "geolocation_metadata": {
                                    "detected_country": "US",
                                    "country_name": "United States",
                                    "continent": "NA",
                                    "routing_decision": "vpn_adjusted",
                                    "provider_selection_reason": "VPN detected from Nigerian user, routed to Stripe for security",
                                    "detection_confidence": 0.75,
                                    "detection_time_ms": 89,
                                    "vpn_detected": True,
                                    "vpn_provider": "ExpressVPN",
                                    "vpn_confidence": 0.92,
                                    "risk_score": 0.6,
                                    "risk_factors": ["vpn_usage", "currency_mismatch"]
                                },
                                "created_at": "2024-01-15T10:40:00Z",
                                "expires_at": "2024-01-15T11:40:00Z"
                            }
                        },
                        "cryptocurrency_payment": {
                            "summary": "Cryptocurrency → Busha Routing",
                            "description": "Cryptocurrency payment automatically routed to Busha",
                            "value": {
                                "id": "pay_crypto_888999",
                                "booking_id": 126,
                                "user_id": 456,
                                "vendor_id": 789,
                                "amount": 100.00,
                                "currency": "USD",
                                "crypto_currency": "BTC",
                                "provider": "busha",
                                "status": "pending",
                                "transaction_reference": "CC_CRYPTO_20240115_888999",
                                "payment_url": "busha://crypto/******************************************",
                                "geolocation_metadata": {
                                    "detected_country": "NG",
                                    "country_name": "Nigeria",
                                    "continent": "AF",
                                    "routing_decision": "cryptocurrency",
                                    "provider_selection_reason": "Cryptocurrency payment requested, routed to Busha",
                                    "detection_confidence": 0.95,
                                    "detection_time_ms": 32,
                                    "vpn_detected": False,
                                    "risk_score": 0.2
                                },
                                "crypto_metadata": {
                                    "wallet_address": "******************************************",
                                    "qr_code_url": "https://api.cultureconnect.ng/crypto/qr/pay_crypto_888999",
                                    "exchange_rate": 0.000002,
                                    "crypto_amount": "0.000200",
                                    "network_fee": "0.000005",
                                    "confirmations_required": 3
                                },
                                "created_at": "2024-01-15T10:45:00Z",
                                "expires_at": "2024-01-15T11:45:00Z"
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid payment data or geolocation routing error",
            "content": {
                "application/json": {
                    "examples": {
                        "invalid_currency": {
                            "summary": "Unsupported Currency",
                            "value": {
                                "error": "UNSUPPORTED_CURRENCY",
                                "message": "Currency not supported for detected location",
                                "details": {
                                    "currency": "JPY",
                                    "detected_country": "NG",
                                    "supported_currencies": ["NGN", "USD", "GBP", "EUR"]
                                },
                                "timestamp": "2024-01-15T10:30:00Z",
                                "request_id": "req_123456789"
                            }
                        },
                        "geolocation_failed": {
                            "summary": "Geolocation Detection Failed",
                            "value": {
                                "error": "GEOLOCATION_DETECTION_FAILED",
                                "message": "Unable to detect location, using fallback routing",
                                "details": {
                                    "fallback_provider": "stripe",
                                    "fallback_reason": "Default provider for undetected locations"
                                },
                                "timestamp": "2024-01-15T10:30:00Z",
                                "request_id": "req_123456789"
                            }
                        }
                    }
                }
            }
        },
        503: {
            "description": "Payment provider service unavailable",
            "content": {
                "application/json": {
                    "example": {
                        "error": "PAYMENT_PROVIDER_UNAVAILABLE",
                        "message": "Selected payment provider temporarily unavailable",
                        "details": {
                            "provider": "paystack",
                            "fallback_available": True,
                            "fallback_provider": "stripe",
                            "retry_after_seconds": 30
                        },
                        "timestamp": "2024-01-15T10:30:00Z",
                        "request_id": "req_123456789"
                    }
                }
            }
        }
    }
)
async def create_payment(
    payment_data: PaymentCreate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new payment with intelligent geolocation-based provider routing.

    **Performance Target**: <500ms with geolocation detection
    **Security**: VPN detection and risk assessment included
    **Fallback**: Graceful handling of geolocation detection failures
    """
    correlation = correlation_id.get()

    try:
        # Initialize services
        payment_service = PaymentService(db)
        geolocation_service = get_geolocation_service()

        # Detect user location for intelligent provider routing
        geolocation_result = None
        try:
            geolocation_result = await geolocation_service.get_geolocation_from_request(request)
            logger.info(
                f"Geolocation detected for payment creation",
                extra={
                    "correlation_id": correlation,
                    "country_code": geolocation_result.country_code,
                    "detection_method": geolocation_result.detection_method,
                    "confidence_score": geolocation_result.confidence_score
                }
            )
        except Exception as geo_error:
            logger.warning(
                f"Geolocation detection failed, proceeding with fallback: {str(geo_error)}",
                extra={"correlation_id": correlation}
            )
            # Continue without geolocation - fallback mechanisms will handle this

        logger.info(
            f"Creating payment for booking {payment_data.booking_id} with geolocation routing",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "booking_id": payment_data.booking_id,
                "amount": float(payment_data.amount),
                "currency": payment_data.currency,
                "country_code": geolocation_result.country_code if geolocation_result else None,
                "crypto_currency": payment_data.crypto_currency,
                "preferred_provider": payment_data.preferred_provider
            }
        )

        # Create payment with enhanced 4-priority geolocation routing
        payment = await payment_service.create_payment(
            booking_id=payment_data.booking_id,
            user_id=current_user.id,
            vendor_id=1,  # TODO: Get vendor_id from booking
            amount=payment_data.amount,
            currency=payment_data.currency,
            payment_method_id=payment_data.payment_method_id,
            crypto_currency=payment_data.crypto_currency,
            preferred_provider=payment_data.preferred_provider,
            geolocation_result=geolocation_result  # Pass geolocation data
        )

        # Initialize with appropriate payment provider based on currency
        try:
            if payment.provider == PaymentProviderType.PAYSTACK:
                # Initialize payment with Paystack (NGN currency)
                paystack_service = PaystackService()

                paystack_response = await paystack_service.initialize_payment(
                    email=current_user.email,
                    amount=payment_data.amount,
                    reference=payment.transaction_reference,
                    currency=payment_data.currency,
                    callback_url=str(payment_data.return_url) if payment_data.return_url else None,
                    metadata=payment_data.metadata
                )

                # Update payment with provider response
                if paystack_response.authorization_url:
                    await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.PENDING,
                        provider_reference=paystack_response.access_code
                    )

                    # Update payment URL
                    payment.payment_url = paystack_response.authorization_url

                await paystack_service.close()

            elif payment.provider == PaymentProviderType.STRIPE:
                # Initialize payment with Stripe (USD/EUR/GBP currencies)
                stripe_service = StripeService()

                stripe_response = await stripe_service.create_payment_intent(
                    amount=payment_data.amount,
                    currency=payment_data.currency,
                    customer_email=current_user.email,
                    metadata=payment_data.metadata,
                    return_url=str(payment_data.return_url) if payment_data.return_url else None
                )

                # Update payment with provider response
                if stripe_response.get("client_secret"):
                    await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.PENDING,
                        provider_reference=stripe_response["payment_intent_id"]
                    )

                    # Store client secret for frontend integration
                    payment.payment_url = f"stripe://payment_intent/{stripe_response['client_secret']}"

            elif payment.provider == PaymentProviderType.BUSHA:
                # Initialize payment with Busha (cryptocurrency)
                busha_service = BushaService()

                # Use crypto_currency from payment data or default to BTC
                crypto_currency = payment_data.crypto_currency or "BTC"

                busha_response = await busha_service.create_crypto_payment(
                    amount=payment_data.amount,
                    currency=payment_data.currency,
                    crypto_currency=crypto_currency,
                    customer_email=current_user.email,
                    reference=payment.transaction_reference,
                    metadata=payment_data.metadata,
                    callback_url=str(payment_data.return_url) if payment_data.return_url else None
                )

                # Update payment with provider response
                if busha_response.get("wallet_address"):
                    await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.PENDING,
                        provider_reference=busha_response["payment_id"]
                    )

                    # Store crypto payment details for frontend
                    payment.payment_url = f"busha://crypto/{busha_response['wallet_address']}"

                await busha_service.close()

            else:
                logger.warning(
                    f"Unsupported payment provider: {payment.provider}",
                    extra={
                        "correlation_id": correlation,
                        "payment_id": payment.id,
                        "provider": payment.provider.value
                    }
                )

        except (PaystackError, StripeError, BushaError) as e:
            logger.error(
                f"Payment provider initialization failed",
                extra={
                    "correlation_id": correlation,
                    "payment_id": payment.id,
                    "provider": payment.provider.value,
                    "error": str(e)
                }
            )
            # Continue with payment record created, but mark as failed
            await payment_service.update_payment_status(
                payment_id=payment.id,
                status=PaymentStatus.FAILED,
                failure_reason=f"Provider initialization failed: {str(e)}"
            )

        # Track metrics
        metrics_collector.increment_counter(
            "payment_created",
            tags={
                "currency": payment_data.currency,
                "provider": payment.provider.value,
                "status": "success"
            }
        )

        logger.info(
            f"Payment created successfully",
            extra={
                "correlation_id": correlation,
                "payment_id": payment.id,
                "reference": payment.transaction_reference
            }
        )

        return PaymentResponse.model_validate(payment)

    except Exception as e:
        logger.error(
            f"Error creating payment",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "error": str(e)
            }
        )

        metrics_collector.increment_counter(
            "payment_created",
            tags={
                "currency": payment_data.currency,
                "status": "error"
            }
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create payment"
        )


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get payment details by ID.

    **Performance Target**: <200ms
    """
    try:
        payment_service = PaymentService(db)
        payment = await payment_service.get_by_id(payment_id)

        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )

        # Check if user owns this payment
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        return PaymentResponse.model_validate(payment)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving payment {payment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment"
        )


@router.put("/{payment_id}/status", response_model=PaymentResponse)
async def update_payment_status(
    payment_id: int,
    status_update: PaymentStatus,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update payment status.

    **Performance Target**: <100ms
    """
    try:
        payment_service = PaymentService(db)

        # Get payment to verify ownership
        payment = await payment_service.get_by_id(payment_id)
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )

        # Check if user owns this payment
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Update status
        updated_payment = await payment_service.update_payment_status(
            payment_id=payment_id,
            status=status_update
        )

        return PaymentResponse.model_validate(updated_payment)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating payment status {payment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update payment status"
        )


@router.get("/user/{user_id}", response_model=PaymentListResponse)
async def get_user_payments(
    user_id: int,
    status_filter: Optional[PaymentStatus] = Query(None, alias="status"),
    provider_filter: Optional[PaymentProviderType] = Query(None, alias="provider"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user payment history with filtering and pagination.

    **Performance Target**: <200ms
    """
    try:
        # Check if user can access this data
        if user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        payment_service = PaymentService(db)
        result = await payment_service.get_user_payments(
            user_id=user_id,
            status=status_filter,
            provider=provider_filter,
            page=page,
            size=size
        )

        return PaymentListResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving user payments {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payments"
        )


@router.post("/process", response_model=PaymentProcessResponse)
async def process_payment(
    process_request: PaymentProcessRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Process a payment with the specified provider.

    This endpoint handles payment processing logic and provider integration.
    """
    try:
        payment_service = PaymentService(db)

        # Get payment
        payment = await payment_service.get_by_id(process_request.payment_id)
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )

        # Check ownership
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Process payment based on provider
        if payment.provider == PaymentProviderType.PAYSTACK:
            paystack_service = PaystackService()

            try:
                # Verify payment with Paystack
                verify_response = await paystack_service.verify_payment(
                    payment.transaction_reference
                )

                # Update payment status based on verification
                if verify_response.status and verify_response.gateway_response == "Successful":
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.COMPLETED,
                        provider_reference=verify_response.reference
                    )
                    message = "Payment completed successfully"
                else:
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.FAILED,
                        failure_reason=verify_response.gateway_response or "Payment verification failed"
                    )
                    message = "Payment verification failed"

                await paystack_service.close()

                return PaymentProcessResponse(
                    payment_id=payment.id,
                    status=updated_payment.status,
                    authorization_url=payment.payment_url,
                    message=message,
                    expires_at=payment.expires_at
                )

            except PaystackError as e:
                await paystack_service.close()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Paystack payment processing failed: {str(e)}"
                )

        elif payment.provider == PaymentProviderType.STRIPE:
            stripe_service = StripeService()

            try:
                # Retrieve payment intent from Stripe
                stripe_response = await stripe_service.retrieve_payment_intent(
                    payment.provider_reference or payment.payment_intent_id
                )

                # Update payment status based on Stripe status
                if stripe_response["status"] == "succeeded":
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.COMPLETED,
                        provider_reference=stripe_response["payment_intent_id"]
                    )
                    message = "Payment completed successfully"
                elif stripe_response["status"] == "requires_action":
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.PROCESSING
                    )
                    message = "Payment requires additional authentication"
                else:
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.FAILED,
                        failure_reason=f"Payment status: {stripe_response['status']}"
                    )
                    message = "Payment processing failed"

                return PaymentProcessResponse(
                    payment_id=payment.id,
                    status=updated_payment.status,
                    authorization_url=payment.payment_url,
                    message=message,
                    expires_at=payment.expires_at
                )

            except StripeError as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Stripe payment processing failed: {str(e)}"
                )

        elif payment.provider == PaymentProviderType.BUSHA:
            busha_service = BushaService()

            try:
                # Verify crypto payment with Busha
                verify_response = await busha_service.verify_crypto_payment(
                    payment.transaction_reference
                )

                # Update payment status based on verification
                if verify_response["status"] == "completed":
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.COMPLETED,
                        provider_reference=verify_response["transaction_hash"]
                    )
                    message = "Crypto payment completed successfully"
                elif verify_response["status"] == "confirmed":
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.PROCESSING
                    )
                    message = f"Crypto payment confirmed ({verify_response['confirmations']}/{verify_response['required_confirmations']} confirmations)"
                else:
                    updated_payment = await payment_service.update_payment_status(
                        payment_id=payment.id,
                        status=PaymentStatus.FAILED,
                        failure_reason=f"Crypto payment status: {verify_response['status']}"
                    )
                    message = "Crypto payment verification failed"

                await busha_service.close()

                return PaymentProcessResponse(
                    payment_id=payment.id,
                    status=updated_payment.status,
                    authorization_url=payment.payment_url,
                    message=message,
                    expires_at=payment.expires_at
                )

            except BushaError as e:
                await busha_service.close()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Busha crypto payment processing failed: {str(e)}"
                )

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Provider {payment.provider.value} not supported"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing payment {process_request.payment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process payment"
        )


# Payment Methods Endpoints
@router.post("/methods", response_model=PaymentMethodResponse, status_code=status.HTTP_201_CREATED)
async def create_payment_method(
    method_data: PaymentMethodCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new payment method for the user.
    """
    try:
        payment_method_service = PaymentMethodService(db)

        # Create payment method
        payment_method = await payment_method_service.create_payment_method(
            user_id=current_user.id,
            method_type=method_data.method_type,
            provider=method_data.provider,
            display_name=method_data.display_name,
            **method_data.model_dump(exclude={"method_type", "provider", "display_name"})
        )

        return PaymentMethodResponse.model_validate(payment_method)

    except Exception as e:
        logger.error(f"Error creating payment method for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create payment method"
        )


@router.get("/methods/user/{user_id}", response_model=PaymentMethodListResponse)
async def get_user_payment_methods(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's payment methods.
    """
    try:
        # Check access
        if user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        payment_method_service = PaymentMethodService(db)

        # Get user payment methods
        methods = await payment_method_service.get_user_payment_methods(user_id)

        return PaymentMethodListResponse(
            items=[PaymentMethodResponse.model_validate(method) for method in methods],
            total=len(methods)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving payment methods for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment methods"
        )
