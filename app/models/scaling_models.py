"""
Scaling models for Culture Connect Backend API.

This module provides comprehensive scaling models for horizontal scaling and load balancing:
- ScalingMetrics: Real-time scaling metrics and performance indicators
- AutoScalingPolicy: Auto-scaling configuration and policies
- LoadBalancerConfig: Load balancer configuration and health checks
- ContainerMetrics: Container performance and resource utilization
- ScalingEvent: Scaling event tracking and audit trail

Implements Phase 7.3.3 requirements with production-grade scaling infrastructure,
comprehensive monitoring, and seamless integration with Phase 7.2 performance
monitoring and Phase 7.3.2 caching systems.
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4

from sqlalchemy import (
    Column, String, Integer, DateTime, Boolean, Text, Numeric,
    ForeignKey, Index, CheckConstraint, JSON
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, JSONB
from sqlalchemy.orm import relationship

from app.db.base import Base


class ScalingTriggerType(str, Enum):
    """Scaling trigger types."""
    CPU_UTILIZATION = "cpu_utilization"
    MEMORY_UTILIZATION = "memory_utilization"
    REQUEST_RATE = "request_rate"
    RESPONSE_TIME = "response_time"
    QUEUE_LENGTH = "queue_length"
    CUSTOM_METRIC = "custom_metric"


class ScalingDirection(str, Enum):
    """Scaling direction."""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    NO_CHANGE = "no_change"


class LoadBalancerStrategy(str, Enum):
    """Load balancer strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    IP_HASH = "ip_hash"
    LEAST_RESPONSE_TIME = "least_response_time"


class ContainerStatus(str, Enum):
    """Container status."""
    PENDING = "pending"
    RUNNING = "running"
    TERMINATING = "terminating"
    FAILED = "failed"
    SUCCEEDED = "succeeded"


class ScalingEventType(str, Enum):
    """Scaling event types."""
    SCALE_UP_TRIGGERED = "scale_up_triggered"
    SCALE_DOWN_TRIGGERED = "scale_down_triggered"
    SCALING_COMPLETED = "scaling_completed"
    SCALING_FAILED = "scaling_failed"
    POLICY_UPDATED = "policy_updated"
    THRESHOLD_BREACHED = "threshold_breached"


class ScalingMetrics(Base):
    """
    Real-time scaling metrics and performance indicators.

    Tracks system performance metrics used for auto-scaling decisions
    and load balancing optimization.
    """
    __tablename__ = "scaling_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Metric identification
    metric_name = Column(String(100), nullable=False, index=True)
    metric_type = Column(String(50), nullable=False)
    component = Column(String(100), nullable=False, index=True)

    # Metric values
    current_value = Column(Numeric(10, 4), nullable=False)
    threshold_value = Column(Numeric(10, 4), nullable=True)
    target_value = Column(Numeric(10, 4), nullable=True)

    # Performance data
    cpu_utilization = Column(Numeric(5, 2), nullable=True)
    memory_utilization = Column(Numeric(5, 2), nullable=True)
    request_rate = Column(Integer, nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    error_rate = Column(Numeric(5, 4), nullable=True)

    # Metadata
    tags = Column(JSONB, nullable=True)
    metric_metadata = Column(JSONB, nullable=True)

    # Timestamps
    recorded_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_scaling_metrics_component_recorded', 'component', 'recorded_at'),
        Index('idx_scaling_metrics_type_name', 'metric_type', 'metric_name'),
        Index('idx_scaling_metrics_recorded_desc', 'recorded_at', postgresql_using='btree'),
        CheckConstraint('current_value >= 0', name='check_current_value_positive'),
        CheckConstraint('cpu_utilization >= 0 AND cpu_utilization <= 100', name='check_cpu_utilization_range'),
        CheckConstraint('memory_utilization >= 0 AND memory_utilization <= 100', name='check_memory_utilization_range'),
        CheckConstraint('error_rate >= 0 AND error_rate <= 1', name='check_error_rate_range'),
    )


class AutoScalingPolicy(Base):
    """
    Auto-scaling configuration and policies.

    Defines scaling policies, thresholds, and behavior for horizontal
    pod autoscaling and custom metrics scaling.
    """
    __tablename__ = "auto_scaling_policies"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Policy identification
    name = Column(String(200), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    component = Column(String(100), nullable=False, index=True)

    # Scaling configuration
    min_replicas = Column(Integer, nullable=False, default=1)
    max_replicas = Column(Integer, nullable=False, default=10)
    target_cpu_utilization = Column(Integer, nullable=True)
    target_memory_utilization = Column(Integer, nullable=True)

    # Scaling behavior
    scale_up_threshold = Column(Numeric(5, 2), nullable=False)
    scale_down_threshold = Column(Numeric(5, 2), nullable=False)
    scale_up_cooldown_seconds = Column(Integer, nullable=False, default=300)
    scale_down_cooldown_seconds = Column(Integer, nullable=False, default=600)

    # Custom metrics
    custom_metrics = Column(JSONB, nullable=True)
    scaling_triggers = Column(JSONB, nullable=True)

    # Policy status
    is_enabled = Column(Boolean, nullable=False, default=True)
    last_scaling_event = Column(DateTime(timezone=True), nullable=True)

    # Metadata
    configuration = Column(JSONB, nullable=True)
    tags = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Relationships
    scaling_events = relationship("ScalingEvent", back_populates="policy", cascade="all, delete-orphan")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_auto_scaling_policies_component', 'component'),
        Index('idx_auto_scaling_policies_enabled', 'is_enabled'),
        Index('idx_auto_scaling_policies_last_event', 'last_scaling_event'),
        CheckConstraint('min_replicas >= 1', name='check_min_replicas_positive'),
        CheckConstraint('max_replicas >= min_replicas', name='check_max_replicas_valid'),
        CheckConstraint('scale_up_threshold > scale_down_threshold', name='check_threshold_order'),
        CheckConstraint('scale_up_cooldown_seconds >= 60', name='check_scale_up_cooldown_minimum'),
        CheckConstraint('scale_down_cooldown_seconds >= 60', name='check_scale_down_cooldown_minimum'),
    )


class LoadBalancerConfig(Base):
    """
    Load balancer configuration and health checks.

    Manages load balancer settings, traffic distribution strategies,
    health check configurations, and SSL termination.
    """
    __tablename__ = "load_balancer_configs"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Configuration identification
    name = Column(String(200), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    service_name = Column(String(100), nullable=False, index=True)

    # Load balancing strategy
    strategy = Column(String(50), nullable=False, default=LoadBalancerStrategy.ROUND_ROBIN)
    session_affinity = Column(Boolean, nullable=False, default=False)
    sticky_session_timeout = Column(Integer, nullable=True)

    # Health check configuration
    health_check_path = Column(String(200), nullable=False, default="/health")
    health_check_interval_seconds = Column(Integer, nullable=False, default=30)
    health_check_timeout_seconds = Column(Integer, nullable=False, default=5)
    health_check_retries = Column(Integer, nullable=False, default=3)

    # SSL configuration
    ssl_enabled = Column(Boolean, nullable=False, default=True)
    ssl_certificate_path = Column(String(500), nullable=True)
    ssl_redirect = Column(Boolean, nullable=False, default=True)

    # Traffic configuration
    max_connections = Column(Integer, nullable=True)
    connection_timeout_seconds = Column(Integer, nullable=False, default=60)
    request_timeout_seconds = Column(Integer, nullable=False, default=30)

    # Configuration details
    upstream_servers = Column(JSONB, nullable=True)
    configuration = Column(JSONB, nullable=True)

    # Status
    is_enabled = Column(Boolean, nullable=False, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_load_balancer_configs_service', 'service_name'),
        Index('idx_load_balancer_configs_enabled', 'is_enabled'),
        Index('idx_load_balancer_configs_strategy', 'strategy'),
        CheckConstraint('health_check_interval_seconds >= 10', name='check_health_check_interval_minimum'),
        CheckConstraint('health_check_timeout_seconds >= 1', name='check_health_check_timeout_minimum'),
        CheckConstraint('health_check_retries >= 1', name='check_health_check_retries_minimum'),
        CheckConstraint('connection_timeout_seconds >= 10', name='check_connection_timeout_minimum'),
        CheckConstraint('request_timeout_seconds >= 5', name='check_request_timeout_minimum'),
    )


class ContainerMetrics(Base):
    """
    Container performance and resource utilization.

    Tracks individual container metrics for scaling decisions
    and performance optimization.
    """
    __tablename__ = "container_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Container identification
    container_id = Column(String(100), nullable=False, index=True)
    pod_name = Column(String(200), nullable=False, index=True)
    namespace = Column(String(100), nullable=False, index=True)
    node_name = Column(String(200), nullable=True)

    # Resource utilization
    cpu_usage_cores = Column(Numeric(8, 4), nullable=True)
    cpu_limit_cores = Column(Numeric(8, 4), nullable=True)
    memory_usage_bytes = Column(Integer, nullable=True)
    memory_limit_bytes = Column(Integer, nullable=True)

    # Performance metrics
    request_count = Column(Integer, nullable=True, default=0)
    error_count = Column(Integer, nullable=True, default=0)
    avg_response_time_ms = Column(Integer, nullable=True)

    # Container status
    status = Column(String(50), nullable=False, default=ContainerStatus.PENDING)
    restart_count = Column(Integer, nullable=False, default=0)

    # Network metrics
    network_rx_bytes = Column(Integer, nullable=True)
    network_tx_bytes = Column(Integer, nullable=True)

    # Metadata
    labels = Column(JSONB, nullable=True)
    annotations = Column(JSONB, nullable=True)

    # Timestamps
    recorded_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_container_metrics_pod_recorded', 'pod_name', 'recorded_at'),
        Index('idx_container_metrics_namespace_status', 'namespace', 'status'),
        Index('idx_container_metrics_recorded_desc', 'recorded_at', postgresql_using='btree'),
        CheckConstraint('cpu_usage_cores >= 0', name='check_cpu_usage_positive'),
        CheckConstraint('memory_usage_bytes >= 0', name='check_memory_usage_positive'),
        CheckConstraint('request_count >= 0', name='check_request_count_positive'),
        CheckConstraint('error_count >= 0', name='check_error_count_positive'),
        CheckConstraint('restart_count >= 0', name='check_restart_count_positive'),
    )


class ScalingEvent(Base):
    """
    Scaling event tracking and audit trail.

    Records all scaling events, decisions, and outcomes for
    audit trail and performance analysis.
    """
    __tablename__ = "scaling_events"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Event identification
    event_type = Column(String(50), nullable=False, index=True)
    component = Column(String(100), nullable=False, index=True)
    trigger_type = Column(String(50), nullable=False)

    # Scaling details
    scaling_direction = Column(String(20), nullable=True)
    previous_replicas = Column(Integer, nullable=True)
    target_replicas = Column(Integer, nullable=True)
    actual_replicas = Column(Integer, nullable=True)

    # Trigger information
    trigger_value = Column(Numeric(10, 4), nullable=True)
    threshold_value = Column(Numeric(10, 4), nullable=True)
    metric_name = Column(String(100), nullable=True)

    # Event outcome
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(Text, nullable=True)
    duration_seconds = Column(Integer, nullable=True)

    # Policy reference
    policy_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey('auto_scaling_policies.id'), nullable=True)

    # Metadata
    event_metadata = Column(JSONB, nullable=True)
    context = Column(JSONB, nullable=True)

    # Timestamps
    triggered_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Relationships
    policy = relationship("AutoScalingPolicy", back_populates="scaling_events")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_scaling_events_component_triggered', 'component', 'triggered_at'),
        Index('idx_scaling_events_type_success', 'event_type', 'success'),
        Index('idx_scaling_events_policy_triggered', 'policy_id', 'triggered_at'),
        Index('idx_scaling_events_triggered_desc', 'triggered_at', postgresql_using='btree'),
        CheckConstraint('previous_replicas >= 0', name='check_previous_replicas_positive'),
        CheckConstraint('target_replicas >= 0', name='check_target_replicas_positive'),
        CheckConstraint('actual_replicas >= 0', name='check_actual_replicas_positive'),
        CheckConstraint('duration_seconds >= 0', name='check_duration_positive'),
    )
