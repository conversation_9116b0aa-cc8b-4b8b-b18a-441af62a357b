"""
Optimization Recommendation Repository for Culture Connect Backend API.

This module provides data access layer for optimization recommendation operations including:
- Priority-based filtering and status tracking
- Impact measurement queries and implementation tracking
- AI-generated recommendation management
- Integration with service and vendor optimization workflows

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade async operations and comprehensive recommendation management.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from app.models.marketplace_optimization import (
    OptimizationRecommendation, RecommendationType, RecommendationPriority
)
from app.models.service import Service
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository, RepositoryError


logger = logging.getLogger(__name__)


class OptimizationRecommendationRepository(BaseRepository[OptimizationRecommendation]):
    """
    Repository for optimization recommendation data access operations.
    
    Provides specialized methods for:
    - Priority-based filtering and status tracking
    - Impact measurement queries and implementation tracking
    - AI-generated recommendation management
    - Service and vendor optimization workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize optimization recommendation repository."""
        super().__init__(OptimizationRecommendation, db)

    async def get_by_service(
        self,
        service_id: int,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        limit: int = 50
    ) -> List[OptimizationRecommendation]:
        """
        Get optimization recommendations for a service.
        
        Args:
            service_id: Service ID to get recommendations for
            status: Optional status filter (pending, in_progress, completed, dismissed)
            priority: Optional priority filter (critical, high, medium, low)
            limit: Maximum number of recommendations to return
            
        Returns:
            List of optimization recommendations
        """
        try:
            query = Select(OptimizationRecommendation).where(
                OptimizationRecommendation.service_id == service_id
            )
            
            if status:
                query = query.where(OptimizationRecommendation.status == status)
            
            if priority:
                query = query.where(OptimizationRecommendation.priority == priority)
            
            query = query.order_by(
                desc(OptimizationRecommendation.expected_impact_score),
                desc(OptimizationRecommendation.created_at)
            ).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get recommendations for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get service recommendations", e)

    async def get_by_vendor(
        self,
        vendor_id: int,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        recommendation_type: Optional[str] = None,
        limit: int = 100
    ) -> List[OptimizationRecommendation]:
        """
        Get optimization recommendations for a vendor.
        
        Args:
            vendor_id: Vendor ID to get recommendations for
            status: Optional status filter
            priority: Optional priority filter
            recommendation_type: Optional type filter
            limit: Maximum number of recommendations to return
            
        Returns:
            List of optimization recommendations with service data
        """
        try:
            query = Select(OptimizationRecommendation).where(
                OptimizationRecommendation.vendor_id == vendor_id
            ).options(
                joinedload(OptimizationRecommendation.service),
                joinedload(OptimizationRecommendation.vendor)
            )
            
            if status:
                query = query.where(OptimizationRecommendation.status == status)
            
            if priority:
                query = query.where(OptimizationRecommendation.priority == priority)
            
            if recommendation_type:
                query = query.where(OptimizationRecommendation.recommendation_type == recommendation_type)
            
            query = query.order_by(
                case(
                    (OptimizationRecommendation.priority == "critical", 1),
                    (OptimizationRecommendation.priority == "high", 2),
                    (OptimizationRecommendation.priority == "medium", 3),
                    else_=4
                ),
                desc(OptimizationRecommendation.expected_impact_score),
                desc(OptimizationRecommendation.created_at)
            ).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get recommendations for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get vendor recommendations", e)

    async def get_high_impact_recommendations(
        self,
        vendor_id: Optional[int] = None,
        min_impact_score: float = 75.0,
        status: str = "pending",
        limit: int = 20
    ) -> List[OptimizationRecommendation]:
        """
        Get high-impact optimization recommendations.
        
        Args:
            vendor_id: Optional vendor filter
            min_impact_score: Minimum expected impact score
            status: Status filter (default: pending)
            limit: Maximum number of recommendations
            
        Returns:
            List of high-impact recommendations
        """
        try:
            query = Select(OptimizationRecommendation).where(
                and_(
                    OptimizationRecommendation.expected_impact_score >= min_impact_score,
                    OptimizationRecommendation.status == status
                )
            ).options(
                joinedload(OptimizationRecommendation.service),
                joinedload(OptimizationRecommendation.vendor)
            )
            
            if vendor_id:
                query = query.where(OptimizationRecommendation.vendor_id == vendor_id)
            
            query = query.order_by(
                desc(OptimizationRecommendation.expected_impact_score),
                case(
                    (OptimizationRecommendation.priority == "critical", 1),
                    (OptimizationRecommendation.priority == "high", 2),
                    else_=3
                )
            ).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get high-impact recommendations: {str(e)}")
            raise RepositoryError(f"Failed to get high-impact recommendations", e)

    async def get_implementation_statistics(
        self,
        vendor_id: Optional[int] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get implementation statistics for recommendations.
        
        Args:
            vendor_id: Optional vendor filter
            days: Number of days to analyze
            
        Returns:
            Implementation statistics and success metrics
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            query = Select(
                func.count(OptimizationRecommendation.id).label('total_recommendations'),
                func.sum(
                    case(
                        (OptimizationRecommendation.status == "completed", 1),
                        else_=0
                    )
                ).label('completed_count'),
                func.sum(
                    case(
                        (OptimizationRecommendation.status == "in_progress", 1),
                        else_=0
                    )
                ).label('in_progress_count'),
                func.sum(
                    case(
                        (OptimizationRecommendation.status == "dismissed", 1),
                        else_=0
                    )
                ).label('dismissed_count'),
                func.avg(OptimizationRecommendation.expected_impact_score).label('avg_expected_impact'),
                func.avg(
                    case(
                        (OptimizationRecommendation.actual_impact_score.isnot(None),
                         OptimizationRecommendation.actual_impact_score),
                        else_=None
                    )
                ).label('avg_actual_impact'),
                func.avg(OptimizationRecommendation.estimated_time_hours).label('avg_estimated_time')
            ).where(OptimizationRecommendation.created_at >= cutoff_date)
            
            if vendor_id:
                query = query.where(OptimizationRecommendation.vendor_id == vendor_id)
            
            result = await self.db.execute(query)
            stats = result.first()
            
            # Calculate completion rate and impact accuracy
            total = stats.total_recommendations or 0
            completed = stats.completed_count or 0
            completion_rate = (completed / total * 100) if total > 0 else 0
            
            avg_expected = float(stats.avg_expected_impact or 0)
            avg_actual = float(stats.avg_actual_impact or 0)
            impact_accuracy = (avg_actual / avg_expected * 100) if avg_expected > 0 else 0
            
            return {
                "period": {
                    "start_date": cutoff_date.date().isoformat(),
                    "end_date": datetime.now(timezone.utc).date().isoformat(),
                    "days": days
                },
                "totals": {
                    "total_recommendations": total,
                    "completed": completed,
                    "in_progress": stats.in_progress_count or 0,
                    "dismissed": stats.dismissed_count or 0,
                    "pending": total - completed - (stats.in_progress_count or 0) - (stats.dismissed_count or 0)
                },
                "rates": {
                    "completion_rate": round(completion_rate, 2),
                    "dismissal_rate": round((stats.dismissed_count or 0) / total * 100, 2) if total > 0 else 0
                },
                "impact_analysis": {
                    "avg_expected_impact": round(avg_expected, 2),
                    "avg_actual_impact": round(avg_actual, 2),
                    "impact_accuracy": round(impact_accuracy, 2)
                },
                "effort_analysis": {
                    "avg_estimated_time_hours": round(float(stats.avg_estimated_time or 0), 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get implementation statistics: {str(e)}")
            raise RepositoryError(f"Failed to get implementation statistics", e)

    async def get_recommendations_by_type(
        self,
        recommendation_type: str,
        vendor_id: Optional[int] = None,
        status: Optional[str] = None,
        limit: int = 50
    ) -> List[OptimizationRecommendation]:
        """
        Get recommendations by type with optional filters.
        
        Args:
            recommendation_type: Type of recommendation to filter by
            vendor_id: Optional vendor filter
            status: Optional status filter
            limit: Maximum number of recommendations
            
        Returns:
            List of recommendations of specified type
        """
        try:
            query = Select(OptimizationRecommendation).where(
                OptimizationRecommendation.recommendation_type == recommendation_type
            ).options(
                joinedload(OptimizationRecommendation.service),
                joinedload(OptimizationRecommendation.vendor)
            )
            
            if vendor_id:
                query = query.where(OptimizationRecommendation.vendor_id == vendor_id)
            
            if status:
                query = query.where(OptimizationRecommendation.status == status)
            
            query = query.order_by(
                desc(OptimizationRecommendation.expected_impact_score),
                desc(OptimizationRecommendation.created_at)
            ).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get recommendations by type {recommendation_type}: {str(e)}")
            raise RepositoryError(f"Failed to get recommendations by type", e)

    async def get_impact_analysis(
        self,
        vendor_id: Optional[int] = None,
        recommendation_type: Optional[str] = None,
        days: int = 90
    ) -> Dict[str, Any]:
        """
        Get impact analysis for completed recommendations.
        
        Args:
            vendor_id: Optional vendor filter
            recommendation_type: Optional type filter
            days: Number of days to analyze
            
        Returns:
            Impact analysis with success metrics
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            query = Select(
                OptimizationRecommendation.recommendation_type,
                OptimizationRecommendation.expected_impact_score,
                OptimizationRecommendation.actual_impact_score,
                OptimizationRecommendation.estimated_time_hours,
                OptimizationRecommendation.implementation_date,
                OptimizationRecommendation.completion_date
            ).where(
                and_(
                    OptimizationRecommendation.status == "completed",
                    OptimizationRecommendation.completion_date >= cutoff_date,
                    OptimizationRecommendation.actual_impact_score.isnot(None)
                )
            )
            
            if vendor_id:
                query = query.where(OptimizationRecommendation.vendor_id == vendor_id)
            
            if recommendation_type:
                query = query.where(OptimizationRecommendation.recommendation_type == recommendation_type)
            
            result = await self.db.execute(query)
            recommendations = result.all()
            
            if not recommendations:
                return {"message": "No completed recommendations found for analysis"}
            
            # Analyze impact by type
            type_analysis = {}
            total_expected = 0
            total_actual = 0
            total_time = 0
            
            for rec in recommendations:
                rec_type = rec.recommendation_type
                expected = float(rec.expected_impact_score)
                actual = float(rec.actual_impact_score)
                time_hours = float(rec.estimated_time_hours)
                
                if rec_type not in type_analysis:
                    type_analysis[rec_type] = {
                        "count": 0,
                        "total_expected": 0,
                        "total_actual": 0,
                        "total_time": 0,
                        "success_rate": 0
                    }
                
                type_analysis[rec_type]["count"] += 1
                type_analysis[rec_type]["total_expected"] += expected
                type_analysis[rec_type]["total_actual"] += actual
                type_analysis[rec_type]["total_time"] += time_hours
                
                total_expected += expected
                total_actual += actual
                total_time += time_hours
            
            # Calculate success rates and averages
            for rec_type, data in type_analysis.items():
                count = data["count"]
                data["avg_expected"] = data["total_expected"] / count
                data["avg_actual"] = data["total_actual"] / count
                data["avg_time"] = data["total_time"] / count
                data["success_rate"] = (data["avg_actual"] / data["avg_expected"] * 100) if data["avg_expected"] > 0 else 0
                data["impact_efficiency"] = data["avg_actual"] / data["avg_time"] if data["avg_time"] > 0 else 0
            
            overall_success_rate = (total_actual / total_expected * 100) if total_expected > 0 else 0
            
            return {
                "analysis_period": {
                    "start_date": cutoff_date.date().isoformat(),
                    "end_date": datetime.now(timezone.utc).date().isoformat(),
                    "days": days
                },
                "overall_metrics": {
                    "total_completed": len(recommendations),
                    "avg_expected_impact": round(total_expected / len(recommendations), 2),
                    "avg_actual_impact": round(total_actual / len(recommendations), 2),
                    "overall_success_rate": round(overall_success_rate, 2),
                    "avg_implementation_time": round(total_time / len(recommendations), 2)
                },
                "by_recommendation_type": {
                    rec_type: {
                        "count": data["count"],
                        "avg_expected_impact": round(data["avg_expected"], 2),
                        "avg_actual_impact": round(data["avg_actual"], 2),
                        "success_rate": round(data["success_rate"], 2),
                        "avg_time_hours": round(data["avg_time"], 2),
                        "impact_efficiency": round(data["impact_efficiency"], 2)
                    }
                    for rec_type, data in type_analysis.items()
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get impact analysis: {str(e)}")
            raise RepositoryError(f"Failed to get impact analysis", e)

    async def update_implementation_status(
        self,
        recommendation_id: int,
        status: str,
        implementation_notes: Optional[str] = None,
        actual_impact_score: Optional[float] = None
    ) -> OptimizationRecommendation:
        """
        Update implementation status of a recommendation.
        
        Args:
            recommendation_id: Recommendation ID to update
            status: New status (in_progress, completed, dismissed)
            implementation_notes: Optional implementation notes
            actual_impact_score: Optional actual impact achieved
            
        Returns:
            Updated recommendation
        """
        try:
            recommendation = await self.get_by_id(recommendation_id)
            if not recommendation:
                raise RepositoryError(f"Recommendation {recommendation_id} not found")
            
            # Update status and timestamps
            recommendation.status = status
            
            if status == "in_progress" and not recommendation.implementation_date:
                recommendation.implementation_date = datetime.now(timezone.utc)
            elif status == "completed":
                recommendation.completion_date = datetime.now(timezone.utc)
                if not recommendation.implementation_date:
                    recommendation.implementation_date = datetime.now(timezone.utc)
            
            if implementation_notes:
                recommendation.implementation_notes = implementation_notes
            
            if actual_impact_score is not None:
                recommendation.actual_impact_score = Decimal(str(actual_impact_score))
            
            await self.db.commit()
            await self.db.refresh(recommendation)
            
            return recommendation
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update recommendation status: {str(e)}")
            raise RepositoryError(f"Failed to update recommendation status", e)
