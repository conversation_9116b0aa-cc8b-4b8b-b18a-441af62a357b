"""
Analytics models for Culture Connect Backend API.

This module defines comprehensive analytics and performance monitoring models including:
- UserAnalytics: User behavior tracking and engagement metrics
- VendorAnalytics: Vendor performance metrics and business intelligence
- BookingAnalytics: Booking conversion and revenue analytics
- SystemMetrics: Application performance and system health monitoring
- DashboardWidget: Configurable dashboard components and KPI definitions
- PerformanceMetrics: APM integration and system performance tracking (Phase 7.2)
- SystemHealth: Real-time system health monitoring and alerting (Phase 7.2)

Implements Phase 7.1 & 7.2 requirements for analytics dashboard system and performance monitoring
with production-grade PostgreSQL optimization and real-time data aggregation.
"""

import enum
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Numeric, JSON, Index, CheckConstraint, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.db.base import BaseModelWithAudit


class AnalyticsTimeframe(str, enum.Enum):
    """Time frame options for analytics aggregation."""
    REAL_TIME = "real_time"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class MetricType(str, enum.Enum):
    """Types of metrics for analytics tracking."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    RATE = "rate"
    PERCENTAGE = "percentage"


class DashboardWidgetType(str, enum.Enum):
    """Types of dashboard widgets."""
    LINE_CHART = "line_chart"
    BAR_CHART = "bar_chart"
    PIE_CHART = "pie_chart"
    METRIC_CARD = "metric_card"
    TABLE = "table"
    HEATMAP = "heatmap"
    GAUGE = "gauge"


class UserAnalytics(BaseModelWithAudit):
    """
    User analytics model for tracking user behavior and engagement metrics.

    Stores aggregated user activity data for business intelligence and
    user experience optimization.
    """

    __tablename__ = "user_analytics"

    # User reference
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.uuid", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the user"
    )

    # Time aggregation
    timeframe = Column(
        SQLEnum(AnalyticsTimeframe),
        nullable=False,
        index=True,
        doc="Time aggregation period"
    )

    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Start of the analytics period"
    )

    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="End of the analytics period"
    )

    # Engagement metrics
    session_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of user sessions"
    )

    total_session_duration = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total session duration in seconds"
    )

    page_views = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Total page views"
    )

    unique_pages_visited = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of unique pages visited"
    )

    # Booking behavior
    bookings_viewed = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings viewed"
    )

    bookings_created = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings created"
    )

    bookings_completed = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings completed"
    )

    # Financial metrics
    total_spent = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total amount spent"
    )

    average_order_value = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Average order value"
    )

    # Device and location
    device_type = Column(
        String(50),
        nullable=True,
        doc="Primary device type used"
    )

    location_country = Column(
        String(2),
        nullable=True,
        doc="User's country code"
    )

    location_city = Column(
        String(100),
        nullable=True,
        doc="User's city"
    )

    # Additional metrics
    custom_metrics = Column(
        JSONB,
        nullable=True,
        doc="Additional custom metrics in JSON format"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="analytics",
        foreign_keys=[user_id]
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('session_count >= 0', name='positive_session_count'),
        CheckConstraint('total_session_duration >= 0', name='positive_session_duration'),
        CheckConstraint('page_views >= 0', name='positive_page_views'),
        CheckConstraint('total_spent >= 0', name='positive_total_spent'),
        Index('idx_user_analytics_user_timeframe', 'user_id', 'timeframe'),
        Index('idx_user_analytics_period', 'period_start', 'period_end'),
        Index('idx_user_analytics_location', 'location_country', 'location_city'),
        Index('idx_user_analytics_device', 'device_type'),
    )


class VendorAnalytics(BaseModelWithAudit):
    """
    Vendor analytics model for tracking vendor performance and business metrics.

    Stores aggregated vendor activity data for business intelligence and
    vendor performance optimization.
    """

    __tablename__ = "vendor_analytics"

    # Vendor reference
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("vendors.uuid", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor"
    )

    # Time aggregation
    timeframe = Column(
        SQLEnum(AnalyticsTimeframe),
        nullable=False,
        index=True,
        doc="Time aggregation period"
    )

    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Start of the analytics period"
    )

    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="End of the analytics period"
    )

    # Profile metrics
    profile_views = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of profile views"
    )

    service_views = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of service views"
    )

    # Booking metrics
    booking_requests = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of booking requests received"
    )

    bookings_accepted = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings accepted"
    )

    bookings_completed = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings completed"
    )

    bookings_cancelled = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of bookings cancelled"
    )

    # Financial metrics
    total_revenue = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total revenue generated"
    )

    commission_paid = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total commission paid to platform"
    )

    net_revenue = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Net revenue after commission"
    )

    average_booking_value = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Average booking value"
    )

    # Performance metrics
    response_time_avg = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Average response time to bookings in minutes"
    )

    customer_rating_avg = Column(
        Numeric(3, 2),
        default=0,
        nullable=False,
        doc="Average customer rating"
    )

    repeat_customer_rate = Column(
        Numeric(5, 2),
        default=0,
        nullable=False,
        doc="Repeat customer rate as percentage"
    )

    # Additional metrics
    custom_metrics = Column(
        JSONB,
        nullable=True,
        doc="Additional custom metrics in JSON format"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="analytics",
        foreign_keys=[vendor_id]
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('profile_views >= 0', name='positive_profile_views'),
        CheckConstraint('booking_requests >= 0', name='positive_booking_requests'),
        CheckConstraint('total_revenue >= 0', name='positive_total_revenue'),
        CheckConstraint('customer_rating_avg >= 0 AND customer_rating_avg <= 5', name='valid_rating_range'),
        CheckConstraint('repeat_customer_rate >= 0 AND repeat_customer_rate <= 100', name='valid_percentage_range'),
        Index('idx_vendor_analytics_vendor_timeframe', 'vendor_id', 'timeframe'),
        Index('idx_vendor_analytics_period', 'period_start', 'period_end'),
        Index('idx_vendor_analytics_revenue', 'total_revenue', 'period_start'),
        Index('idx_vendor_analytics_performance', 'customer_rating_avg', 'response_time_avg'),
    )


class BookingAnalytics(BaseModelWithAudit):
    """
    Booking analytics model for tracking booking conversion and revenue metrics.

    Stores aggregated booking data for business intelligence and
    conversion optimization.
    """

    __tablename__ = "booking_analytics"

    # Time aggregation
    timeframe = Column(
        SQLEnum(AnalyticsTimeframe),
        nullable=False,
        index=True,
        doc="Time aggregation period"
    )

    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Start of the analytics period"
    )

    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="End of the analytics period"
    )

    # Booking funnel metrics
    booking_views = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of booking page views"
    )

    booking_starts = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of booking processes started"
    )

    booking_submissions = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of booking submissions"
    )

    booking_completions = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of completed bookings"
    )

    # Revenue metrics
    total_booking_value = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total value of all bookings"
    )

    completed_booking_value = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total value of completed bookings"
    )

    average_booking_value = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Average booking value"
    )

    commission_revenue = Column(
        Numeric(12, 2),
        default=0,
        nullable=False,
        doc="Total commission revenue"
    )

    # Conversion metrics
    view_to_start_rate = Column(
        Numeric(5, 2),
        default=0,
        nullable=False,
        doc="View to start conversion rate as percentage"
    )

    start_to_completion_rate = Column(
        Numeric(5, 2),
        default=0,
        nullable=False,
        doc="Start to completion conversion rate as percentage"
    )

    overall_conversion_rate = Column(
        Numeric(5, 2),
        default=0,
        nullable=False,
        doc="Overall conversion rate as percentage"
    )

    # Geographic and demographic data
    top_countries = Column(
        JSONB,
        nullable=True,
        doc="Top countries by booking volume"
    )

    top_cities = Column(
        JSONB,
        nullable=True,
        doc="Top cities by booking volume"
    )

    device_breakdown = Column(
        JSONB,
        nullable=True,
        doc="Booking breakdown by device type"
    )

    # Payment method analytics
    payment_method_breakdown = Column(
        JSONB,
        nullable=True,
        doc="Booking breakdown by payment method"
    )

    # Additional metrics
    custom_metrics = Column(
        JSONB,
        nullable=True,
        doc="Additional custom metrics in JSON format"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('booking_views >= 0', name='positive_booking_views'),
        CheckConstraint('booking_starts >= 0', name='positive_booking_starts'),
        CheckConstraint('total_booking_value >= 0', name='positive_total_booking_value'),
        CheckConstraint('view_to_start_rate >= 0 AND view_to_start_rate <= 100', name='valid_view_start_rate'),
        CheckConstraint('start_to_completion_rate >= 0 AND start_to_completion_rate <= 100', name='valid_start_completion_rate'),
        CheckConstraint('overall_conversion_rate >= 0 AND overall_conversion_rate <= 100', name='valid_overall_conversion_rate'),
        Index('idx_booking_analytics_timeframe_period', 'timeframe', 'period_start'),
        Index('idx_booking_analytics_revenue', 'total_booking_value', 'period_start'),
        Index('idx_booking_analytics_conversion', 'overall_conversion_rate', 'period_start'),
    )


class SystemMetrics(BaseModelWithAudit):
    """
    System metrics model for tracking application performance and health.

    Stores system-level metrics for performance monitoring and
    infrastructure optimization.
    """

    __tablename__ = "system_metrics"

    # Metric identification
    metric_name = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Name of the metric"
    )

    metric_type = Column(
        SQLEnum(MetricType),
        nullable=False,
        index=True,
        doc="Type of metric"
    )

    # Time aggregation
    timeframe = Column(
        SQLEnum(AnalyticsTimeframe),
        nullable=False,
        index=True,
        doc="Time aggregation period"
    )

    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Start of the metrics period"
    )

    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="End of the metrics period"
    )

    # Metric values
    value = Column(
        Numeric(15, 4),
        nullable=False,
        doc="Primary metric value"
    )

    min_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Minimum value in the period"
    )

    max_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Maximum value in the period"
    )

    avg_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Average value in the period"
    )

    count = Column(
        Integer,
        default=1,
        nullable=False,
        doc="Number of data points"
    )

    # Additional context
    tags = Column(
        JSONB,
        nullable=True,
        doc="Metric tags for filtering and grouping"
    )

    metric_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional metric metadata"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('count > 0', name='positive_count'),
        Index('idx_system_metrics_name_type', 'metric_name', 'metric_type'),
        Index('idx_system_metrics_timeframe_period', 'timeframe', 'period_start'),
        Index('idx_system_metrics_value', 'value', 'period_start'),
        Index('idx_system_metrics_tags', 'tags'),
    )


class DashboardWidget(BaseModelWithAudit):
    """
    Dashboard widget model for configurable dashboard components.

    Stores dashboard widget configurations and KPI definitions for
    customizable analytics dashboards.
    """

    __tablename__ = "dashboard_widgets"

    # Widget identification
    name = Column(
        String(100),
        nullable=False,
        doc="Widget name"
    )

    title = Column(
        String(200),
        nullable=False,
        doc="Widget display title"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Widget description"
    )

    widget_type = Column(
        SQLEnum(DashboardWidgetType),
        nullable=False,
        index=True,
        doc="Type of dashboard widget"
    )

    # Widget configuration
    data_source = Column(
        String(100),
        nullable=False,
        doc="Data source for the widget"
    )

    query_config = Column(
        JSONB,
        nullable=False,
        doc="Query configuration for data retrieval"
    )

    display_config = Column(
        JSONB,
        nullable=True,
        doc="Display configuration (colors, formatting, etc.)"
    )

    refresh_interval = Column(
        Integer,
        default=300,
        nullable=False,
        doc="Refresh interval in seconds"
    )

    # Access control
    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether widget is publicly accessible"
    )

    allowed_roles = Column(
        JSONB,
        nullable=True,
        doc="Roles allowed to view this widget"
    )

    # Layout and positioning
    dashboard_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True,
        doc="Dashboard this widget belongs to"
    )

    position_x = Column(
        Integer,
        default=0,
        nullable=False,
        doc="X position on dashboard grid"
    )

    position_y = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Y position on dashboard grid"
    )

    width = Column(
        Integer,
        default=4,
        nullable=False,
        doc="Widget width in grid units"
    )

    height = Column(
        Integer,
        default=3,
        nullable=False,
        doc="Widget height in grid units"
    )

    # Status and metadata
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether widget is active"
    )

    last_updated = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last time widget data was updated"
    )

    cache_duration = Column(
        Integer,
        default=300,
        nullable=False,
        doc="Cache duration in seconds"
    )

    # Additional configuration
    custom_config = Column(
        JSONB,
        nullable=True,
        doc="Additional custom configuration"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('refresh_interval > 0', name='positive_refresh_interval'),
        CheckConstraint('position_x >= 0', name='non_negative_position_x'),
        CheckConstraint('position_y >= 0', name='non_negative_position_y'),
        CheckConstraint('width > 0', name='positive_width'),
        CheckConstraint('height > 0', name='positive_height'),
        CheckConstraint('cache_duration >= 0', name='non_negative_cache_duration'),
        Index('idx_dashboard_widgets_type_active', 'widget_type', 'is_active'),
        Index('idx_dashboard_widgets_dashboard', 'dashboard_id', 'is_active'),
        Index('idx_dashboard_widgets_position', 'position_x', 'position_y'),
        Index('idx_dashboard_widgets_data_source', 'data_source'),
    )


class KPIDefinition(BaseModelWithAudit):
    """
    KPI definition model for defining key performance indicators.

    Stores KPI definitions and calculation rules for business metrics.
    """

    __tablename__ = "kpi_definitions"

    # KPI identification
    name = Column(
        String(100),
        nullable=False,
        unique=True,
        doc="KPI name (unique identifier)"
    )

    display_name = Column(
        String(200),
        nullable=False,
        doc="KPI display name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="KPI description"
    )

    category = Column(
        String(50),
        nullable=False,
        index=True,
        doc="KPI category (revenue, engagement, performance, etc.)"
    )

    # Calculation configuration
    calculation_method = Column(
        String(50),
        nullable=False,
        doc="Calculation method (sum, avg, count, rate, etc.)"
    )

    data_source = Column(
        String(100),
        nullable=False,
        doc="Primary data source for calculation"
    )

    calculation_config = Column(
        JSONB,
        nullable=False,
        doc="Detailed calculation configuration"
    )

    # Display configuration
    unit = Column(
        String(20),
        nullable=True,
        doc="Unit of measurement (%, $, count, etc.)"
    )

    format_config = Column(
        JSONB,
        nullable=True,
        doc="Number formatting configuration"
    )

    # Targets and thresholds
    target_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Target value for this KPI"
    )

    warning_threshold = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Warning threshold value"
    )

    critical_threshold = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Critical threshold value"
    )

    # Status and metadata
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether KPI is active"
    )

    update_frequency = Column(
        String(20),
        default="daily",
        nullable=False,
        doc="How often KPI should be updated"
    )

    # Additional configuration
    custom_config = Column(
        JSONB,
        nullable=True,
        doc="Additional custom configuration"
    )

    # Constraints and indexes
    __table_args__ = (
        Index('idx_kpi_definitions_category_active', 'category', 'is_active'),
        Index('idx_kpi_definitions_data_source', 'data_source'),
        Index('idx_kpi_definitions_update_frequency', 'update_frequency'),
    )


# Phase 7.2 Performance Monitoring Models

class PerformanceMetricType(enum.Enum):
    """Performance metric types for APM integration."""
    API_RESPONSE_TIME = "api_response_time"
    DATABASE_QUERY_TIME = "database_query_time"
    CACHE_HIT_RATE = "cache_hit_rate"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    DISK_USAGE = "disk_usage"
    NETWORK_LATENCY = "network_latency"
    ERROR_RATE = "error_rate"
    THROUGHPUT = "throughput"
    CONCURRENT_CONNECTIONS = "concurrent_connections"


class PerformanceMetrics(BaseModelWithAudit):
    """
    Performance metrics model for APM integration and system performance tracking.

    Stores detailed performance metrics collected from various system components
    including API endpoints, database queries, cache operations, and system resources.

    Implements Phase 7.2 requirements for performance monitoring with:
    - APM integration with Sentry and custom metrics collection
    - Time-series performance data with PostgreSQL optimization
    - Real-time performance tracking and alerting
    - System resource monitoring and optimization insights
    """

    __tablename__ = "performance_metrics"

    # Metric identification
    metric_type = Column(
        SQLEnum(PerformanceMetricType),
        nullable=False,
        index=True,
        doc="Type of performance metric"
    )

    metric_name = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Specific metric name or identifier"
    )

    component = Column(
        String(50),
        nullable=False,
        index=True,
        doc="System component (api, database, cache, etc.)"
    )

    # Time aggregation
    timestamp = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Metric collection timestamp"
    )

    timeframe = Column(
        SQLEnum(AnalyticsTimeframe),
        nullable=False,
        index=True,
        doc="Time aggregation period"
    )

    # Performance values
    value = Column(
        Numeric(15, 4),
        nullable=False,
        doc="Primary metric value"
    )

    min_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Minimum value in aggregation period"
    )

    max_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Maximum value in aggregation period"
    )

    avg_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Average value in aggregation period"
    )

    percentile_95 = Column(
        Numeric(15, 4),
        nullable=True,
        doc="95th percentile value"
    )

    percentile_99 = Column(
        Numeric(15, 4),
        nullable=True,
        doc="99th percentile value"
    )

    # Sample and error tracking
    sample_count = Column(
        Integer,
        default=1,
        nullable=False,
        doc="Number of samples in aggregation"
    )

    error_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of errors in aggregation period"
    )

    # Additional context
    tags = Column(
        JSONB,
        nullable=True,
        doc="Additional metric tags and labels"
    )

    metric_metadata = Column(
        JSONB,
        nullable=True,
        doc="Additional metric metadata"
    )

    # Constraints and indexes for performance optimization
    __table_args__ = (
        CheckConstraint('value >= 0', name='non_negative_value'),
        CheckConstraint('sample_count > 0', name='positive_sample_count'),
        CheckConstraint('error_count >= 0', name='non_negative_error_count'),
        Index('idx_performance_metrics_type_component', 'metric_type', 'component'),
        Index('idx_performance_metrics_timestamp', 'timestamp'),
        Index('idx_performance_metrics_timeframe_timestamp', 'timeframe', 'timestamp'),
        Index('idx_performance_metrics_component_timestamp', 'component', 'timestamp'),
        Index('idx_performance_metrics_name_timestamp', 'metric_name', 'timestamp'),
    )


class SystemHealthStatus(enum.Enum):
    """System health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DOWN = "down"
    MAINTENANCE = "maintenance"


class SystemHealth(BaseModelWithAudit):
    """
    System health model for real-time system health monitoring and alerting.

    Stores comprehensive system health information including component status,
    performance indicators, and alert configurations for proactive monitoring.

    Implements Phase 7.2 requirements for system health monitoring with:
    - Real-time health status tracking and alerting
    - Component-level health monitoring and dependency tracking
    - Performance threshold monitoring and automated alerting
    - Integration with existing monitoring infrastructure
    """

    __tablename__ = "system_health"

    # Health identification
    component = Column(
        String(50),
        nullable=False,
        index=True,
        doc="System component name"
    )

    service_name = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Service or subsystem name"
    )

    # Health status
    status = Column(
        SQLEnum(SystemHealthStatus),
        nullable=False,
        index=True,
        doc="Current health status"
    )

    previous_status = Column(
        SQLEnum(SystemHealthStatus),
        nullable=True,
        doc="Previous health status for change tracking"
    )

    status_changed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When status last changed"
    )

    # Health metrics
    response_time = Column(
        Numeric(10, 4),
        nullable=True,
        doc="Component response time in milliseconds"
    )

    uptime_percentage = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Uptime percentage over monitoring period"
    )

    error_rate = Column(
        Numeric(5, 4),
        nullable=True,
        doc="Error rate as decimal (0.0 to 1.0)"
    )

    # Resource utilization
    cpu_usage = Column(
        Numeric(5, 2),
        nullable=True,
        doc="CPU usage percentage"
    )

    memory_usage = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Memory usage percentage"
    )

    disk_usage = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Disk usage percentage"
    )

    # Connectivity and dependencies
    dependencies_healthy = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether all dependencies are healthy"
    )

    last_check_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Last health check timestamp"
    )

    next_check_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Next scheduled health check"
    )

    # Alert configuration
    alert_threshold = Column(
        JSONB,
        nullable=True,
        doc="Alert threshold configuration"
    )

    alert_sent = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether alert has been sent for current status"
    )

    alert_sent_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When alert was last sent"
    )

    # Additional context
    health_details = Column(
        JSONB,
        nullable=True,
        doc="Detailed health information and metrics"
    )

    error_details = Column(
        JSONB,
        nullable=True,
        doc="Error details if status is not healthy"
    )

    # Constraints and indexes for performance optimization
    __table_args__ = (
        CheckConstraint('uptime_percentage >= 0 AND uptime_percentage <= 100', name='valid_uptime_percentage'),
        CheckConstraint('error_rate >= 0 AND error_rate <= 1', name='valid_error_rate'),
        CheckConstraint('cpu_usage >= 0 AND cpu_usage <= 100', name='valid_cpu_usage'),
        CheckConstraint('memory_usage >= 0 AND memory_usage <= 100', name='valid_memory_usage'),
        CheckConstraint('disk_usage >= 0 AND disk_usage <= 100', name='valid_disk_usage'),
        CheckConstraint('response_time >= 0', name='non_negative_response_time'),
        Index('idx_system_health_component_status', 'component', 'status'),
        Index('idx_system_health_service_status', 'service_name', 'status'),
        Index('idx_system_health_last_check', 'last_check_at'),
        Index('idx_system_health_next_check', 'next_check_at'),
        Index('idx_system_health_status_changed', 'status_changed_at'),
        Index('idx_system_health_alert_status', 'alert_sent', 'status'),
    )
