"""
Unit tests for infrastructure optimization models.

Tests for Task 3.2.3 infrastructure optimization models including:
- CacheConfiguration model validation and constraints
- BackgroundTask model lifecycle and status management
- PerformanceMonitoring model query tracking
- CacheMetrics and TaskMetrics model analytics
- Model relationships and database constraints

Implements comprehensive test coverage with >80% requirement for
production-grade infrastructure optimization functionality.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from app.models.infrastructure_optimization import (
    CacheConfiguration, BackgroundTask, PerformanceMonitoring,
    CacheMetrics, TaskMetrics, CacheType, TaskStatus, TaskPriority
)


class TestCacheConfiguration:
    """Test CacheConfiguration model."""

    def test_cache_configuration_creation(self):
        """Test creating cache configuration."""
        config = CacheConfiguration(
            cache_type=CacheType.OPTIMIZATION_SCORE,
            cache_key_pattern="opt:{service_id}",
            ttl_seconds=86400,
            max_size_mb=100,
            compression_enabled=True,
            invalidation_rules={"on_update": True}
        )
        
        assert config.cache_type == CacheType.OPTIMIZATION_SCORE
        assert config.cache_key_pattern == "opt:{service_id}"
        assert config.ttl_seconds == 86400
        assert config.max_size_mb == 100
        assert config.compression_enabled is True
        assert config.invalidation_rules == {"on_update": True}
        assert config.is_active is True

    def test_cache_configuration_defaults(self):
        """Test cache configuration default values."""
        config = CacheConfiguration(
            cache_type=CacheType.MARKET_INTELLIGENCE,
            cache_key_pattern="market:{category}",
            ttl_seconds=3600
        )
        
        assert config.max_size_mb == 100
        assert config.compression_enabled is True
        assert config.invalidation_rules == {}
        assert config.is_active is True

    def test_cache_configuration_enum_values(self):
        """Test cache type enum values."""
        assert CacheType.OPTIMIZATION_SCORE == "optimization_score"
        assert CacheType.MARKET_INTELLIGENCE == "market_intelligence"
        assert CacheType.VENDOR_SUMMARY == "vendor_summary"
        assert CacheType.PERFORMANCE_ANALYTICS == "performance_analytics"
        assert CacheType.COMPETITIVE_ANALYSIS == "competitive_analysis"
        assert CacheType.SEO_ANALYSIS == "seo_analysis"
        assert CacheType.MOBILE_OPTIMIZATION == "mobile_optimization"


class TestBackgroundTask:
    """Test BackgroundTask model."""

    def test_background_task_creation(self):
        """Test creating background task."""
        task = BackgroundTask(
            task_id="celery-task-123",
            task_name="optimization.calculate_score",
            task_type="optimization",
            priority=TaskPriority.HIGH,
            status=TaskStatus.PENDING,
            queue_name="high_priority",
            max_retries=3,
            task_args={"service_id": "123"},
            task_kwargs={"include_competitive": True}
        )
        
        assert task.task_id == "celery-task-123"
        assert task.task_name == "optimization.calculate_score"
        assert task.task_type == "optimization"
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.PENDING
        assert task.queue_name == "high_priority"
        assert task.max_retries == 3
        assert task.retry_count == 0
        assert task.task_args == {"service_id": "123"}
        assert task.task_kwargs == {"include_competitive": True}

    def test_background_task_defaults(self):
        """Test background task default values."""
        task = BackgroundTask(
            task_id="celery-task-456",
            task_name="test.task",
            task_type="test"
        )
        
        assert task.priority == TaskPriority.NORMAL
        assert task.status == TaskStatus.PENDING
        assert task.queue_name == "default"
        assert task.retry_count == 0
        assert task.max_retries == 3
        assert task.task_args == {}
        assert task.task_kwargs == {}

    def test_task_status_enum_values(self):
        """Test task status enum values."""
        assert TaskStatus.PENDING == "pending"
        assert TaskStatus.RUNNING == "running"
        assert TaskStatus.SUCCESS == "success"
        assert TaskStatus.FAILURE == "failure"
        assert TaskStatus.RETRY == "retry"
        assert TaskStatus.REVOKED == "revoked"

    def test_task_priority_enum_values(self):
        """Test task priority enum values."""
        assert TaskPriority.LOW == "low"
        assert TaskPriority.NORMAL == "normal"
        assert TaskPriority.HIGH == "high"
        assert TaskPriority.CRITICAL == "critical"


class TestPerformanceMonitoring:
    """Test PerformanceMonitoring model."""

    def test_performance_monitoring_creation(self):
        """Test creating performance monitoring record."""
        monitoring = PerformanceMonitoring(
            query_type="optimization_dashboard",
            query_hash="abc123def456",
            table_names=["services", "seo_analysis", "performance_metrics"],
            execution_time_ms=250.5,
            rows_examined=1000,
            rows_returned=50,
            memory_usage_mb=15.2,
            cpu_usage_percent=25.0,
            endpoint_path="/api/v1/optimization/dashboard",
            query_plan={"plan": "index_scan"},
            index_usage={"services_idx": "used"},
            optimization_suggestions=["Add composite index"],
            is_slow_query=False
        )
        
        assert monitoring.query_type == "optimization_dashboard"
        assert monitoring.query_hash == "abc123def456"
        assert monitoring.table_names == ["services", "seo_analysis", "performance_metrics"]
        assert monitoring.execution_time_ms == 250.5
        assert monitoring.rows_examined == 1000
        assert monitoring.rows_returned == 50
        assert monitoring.memory_usage_mb == 15.2
        assert monitoring.cpu_usage_percent == 25.0
        assert monitoring.endpoint_path == "/api/v1/optimization/dashboard"
        assert monitoring.query_plan == {"plan": "index_scan"}
        assert monitoring.index_usage == {"services_idx": "used"}
        assert monitoring.optimization_suggestions == ["Add composite index"]
        assert monitoring.is_slow_query is False

    def test_performance_monitoring_defaults(self):
        """Test performance monitoring default values."""
        monitoring = PerformanceMonitoring(
            query_type="test_query",
            query_hash="test123",
            execution_time_ms=100.0
        )
        
        assert monitoring.table_names == []
        assert monitoring.rows_examined == 0
        assert monitoring.rows_returned == 0
        assert monitoring.memory_usage_mb is None
        assert monitoring.cpu_usage_percent is None
        assert monitoring.endpoint_path is None
        assert monitoring.query_plan is None
        assert monitoring.index_usage == {}
        assert monitoring.optimization_suggestions == []
        assert monitoring.is_slow_query is False


class TestCacheMetrics:
    """Test CacheMetrics model."""

    def test_cache_metrics_creation(self):
        """Test creating cache metrics record."""
        config_id = uuid4()
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=1)
        
        metrics = CacheMetrics(
            configuration_id=config_id,
            cache_hits=850,
            cache_misses=150,
            memory_usage_mb=256.5,
            key_count=1000,
            expired_keys=50,
            evicted_keys=10,
            avg_response_time_ms=2.5,
            max_response_time_ms=15.0,
            operations_per_second=100.0,
            period_start=start_time,
            period_end=end_time
        )
        
        assert metrics.configuration_id == config_id
        assert metrics.cache_hits == 850
        assert metrics.cache_misses == 150
        assert metrics.memory_usage_mb == 256.5
        assert metrics.key_count == 1000
        assert metrics.expired_keys == 50
        assert metrics.evicted_keys == 10
        assert metrics.avg_response_time_ms == 2.5
        assert metrics.max_response_time_ms == 15.0
        assert metrics.operations_per_second == 100.0
        assert metrics.period_start == start_time
        assert metrics.period_end == end_time

    def test_cache_metrics_defaults(self):
        """Test cache metrics default values."""
        config_id = uuid4()
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=1)
        
        metrics = CacheMetrics(
            configuration_id=config_id,
            period_start=start_time,
            period_end=end_time
        )
        
        assert metrics.cache_hits == 0
        assert metrics.cache_misses == 0
        assert metrics.memory_usage_mb == 0.0
        assert metrics.key_count == 0
        assert metrics.expired_keys == 0
        assert metrics.evicted_keys == 0
        assert metrics.avg_response_time_ms == 0.0
        assert metrics.max_response_time_ms == 0.0
        assert metrics.operations_per_second == 0.0


class TestTaskMetrics:
    """Test TaskMetrics model."""

    def test_task_metrics_creation(self):
        """Test creating task metrics record."""
        task_id = uuid4()
        
        metrics = TaskMetrics(
            task_id=task_id,
            queue_wait_time_seconds=5.2,
            execution_time_seconds=120.5,
            memory_peak_mb=512.0,
            cpu_usage_percent=75.0,
            database_queries_count=25,
            cache_operations_count=10,
            external_api_calls=3,
            throughput_items_per_second=50.0,
            error_rate_percent=2.5,
            success_rate_percent=97.5,
            worker_node="worker-01"
        )
        
        assert metrics.task_id == task_id
        assert metrics.queue_wait_time_seconds == 5.2
        assert metrics.execution_time_seconds == 120.5
        assert metrics.memory_peak_mb == 512.0
        assert metrics.cpu_usage_percent == 75.0
        assert metrics.database_queries_count == 25
        assert metrics.cache_operations_count == 10
        assert metrics.external_api_calls == 3
        assert metrics.throughput_items_per_second == 50.0
        assert metrics.error_rate_percent == 2.5
        assert metrics.success_rate_percent == 97.5
        assert metrics.worker_node == "worker-01"

    def test_task_metrics_defaults(self):
        """Test task metrics default values."""
        task_id = uuid4()
        
        metrics = TaskMetrics(task_id=task_id)
        
        assert metrics.queue_wait_time_seconds == 0.0
        assert metrics.execution_time_seconds == 0.0
        assert metrics.memory_peak_mb == 0.0
        assert metrics.cpu_usage_percent == 0.0
        assert metrics.database_queries_count == 0
        assert metrics.cache_operations_count == 0
        assert metrics.external_api_calls == 0
        assert metrics.throughput_items_per_second == 0.0
        assert metrics.error_rate_percent == 0.0
        assert metrics.success_rate_percent == 100.0
        assert metrics.worker_node is None


class TestModelRelationships:
    """Test model relationships and constraints."""

    def test_cache_configuration_metrics_relationship(self):
        """Test relationship between CacheConfiguration and CacheMetrics."""
        config = CacheConfiguration(
            cache_type=CacheType.OPTIMIZATION_SCORE,
            cache_key_pattern="opt:{service_id}",
            ttl_seconds=86400
        )
        
        # Test that cache_metrics relationship exists
        assert hasattr(config, 'cache_metrics')
        assert config.cache_metrics == []

    def test_background_task_metrics_relationship(self):
        """Test relationship between BackgroundTask and TaskMetrics."""
        task = BackgroundTask(
            task_id="celery-task-123",
            task_name="test.task",
            task_type="test"
        )
        
        # Test that task_metrics relationship exists
        assert hasattr(task, 'task_metrics')
        assert task.task_metrics == []

    def test_model_uuid_generation(self):
        """Test that models generate UUIDs correctly."""
        config = CacheConfiguration(
            cache_type=CacheType.OPTIMIZATION_SCORE,
            cache_key_pattern="opt:{service_id}",
            ttl_seconds=86400
        )
        
        task = BackgroundTask(
            task_id="celery-task-123",
            task_name="test.task",
            task_type="test"
        )
        
        monitoring = PerformanceMonitoring(
            query_type="test_query",
            query_hash="test123",
            execution_time_ms=100.0
        )
        
        # UUIDs should be generated automatically
        assert config.id is not None
        assert task.id is not None
        assert monitoring.id is not None
        
        # UUIDs should be different
        assert config.id != task.id
        assert task.id != monitoring.id
        assert config.id != monitoring.id
