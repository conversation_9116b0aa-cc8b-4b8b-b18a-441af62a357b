"""
Unit tests for workflow repositories.

Tests for Task 6.2.2 Phase 2: Job Orchestration Engine
- WorkflowRepository tests
- JobDependencyRepository tests
- WorkflowStepRepository tests
- JobScheduleRepository tests
- WorkflowAlertRepository tests
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.repositories.workflow_repositories import (
    WorkflowRepository, JobDependencyRepository, WorkflowStepRepository,
    JobScheduleRepository, WorkflowAlertRepository
)
from app.models.workflow_models import (
    WorkflowDefinition, WorkflowExecution, JobDependency, WorkflowStep,
    WorkflowStepExecution, JobSchedule, WorkflowAlert,
    WorkflowStatus, ExecutionStatus, DependencyType, StepStatus,
    Alert<PERSON>everity, AlertChannel
)
from app.schemas.workflow_schemas import (
    WorkflowDefinitionCreate, JobDependencyCreate, WorkflowStepCreate,
    JobScheduleCreate, WorkflowAlertCreate
)
from app.repositories.workflow_repositories import NotFoundError, ValidationError, ConflictError


class TestWorkflowRepository:
    """Test cases for WorkflowRepository."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def workflow_repository(self, mock_session):
        """Create WorkflowRepository instance."""
        return WorkflowRepository(mock_session)

    @pytest.fixture
    def sample_workflow_data(self):
        """Create sample workflow creation data."""
        return WorkflowDefinitionCreate(
            name="Test Workflow",
            description="Test workflow description",
            version="1.0.0",
            status=WorkflowStatus.DRAFT,
            configuration={"test": "config"},
            tags=["test", "workflow"],
            workflow_metadata={"meta": "data"},
            team_id=uuid4(),
            max_execution_time=3600,
            max_retries=3,
            retry_delay=60,
            is_active=True,
            priority=5
        )

    @pytest.mark.asyncio
    async def test_create_workflow_definition_success(
        self, workflow_repository, sample_workflow_data, mock_session
    ):
        """Test successful workflow definition creation."""
        # Mock existing workflow check
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        # Mock workflow creation
        created_workflow = WorkflowDefinition(
            id=uuid4(),
            name=sample_workflow_data.name,
            description=sample_workflow_data.description,
            version=sample_workflow_data.version,
            status=sample_workflow_data.status,
            configuration=sample_workflow_data.configuration,
            tags=sample_workflow_data.tags,
            workflow_metadata=sample_workflow_data.workflow_metadata,
            team_id=sample_workflow_data.team_id,
            max_execution_time=sample_workflow_data.max_execution_time,
            max_retries=sample_workflow_data.max_retries,
            retry_delay=sample_workflow_data.retry_delay,
            is_active=sample_workflow_data.is_active,
            priority=sample_workflow_data.priority,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        with patch.object(workflow_repository, 'create', return_value=created_workflow):
            result = await workflow_repository.create_workflow_definition(
                sample_workflow_data,
                created_by=uuid4(),
                correlation_id="test-correlation-id"
            )

        assert result.name == sample_workflow_data.name
        assert result.description == sample_workflow_data.description
        assert result.version == sample_workflow_data.version
        assert result.status == sample_workflow_data.status

    @pytest.mark.asyncio
    async def test_create_workflow_definition_duplicate_name(
        self, workflow_repository, sample_workflow_data, mock_session
    ):
        """Test workflow creation with duplicate name."""
        # Mock existing workflow
        existing_workflow = WorkflowDefinition(
            id=uuid4(),
            name=sample_workflow_data.name,
            description="Existing workflow",
            version="1.0.0",
            status=WorkflowStatus.ACTIVE,
            configuration={},
            tags=[],
            workflow_metadata={},
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        mock_session.execute.return_value.scalar_one_or_none.return_value = existing_workflow

        with pytest.raises(ConflictError) as exc_info:
            await workflow_repository.create_workflow_definition(
                sample_workflow_data,
                created_by=uuid4(),
                correlation_id="test-correlation-id"
            )

        assert "already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_workflow_with_steps_success(
        self, workflow_repository, mock_session
    ):
        """Test successful workflow retrieval with steps."""
        workflow_id = uuid4()

        # Mock workflow with steps
        mock_workflow = WorkflowDefinition(
            id=workflow_id,
            name="Test Workflow",
            description="Test description",
            version="1.0.0",
            status=WorkflowStatus.ACTIVE,
            configuration={},
            tags=[],
            workflow_metadata={},
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        mock_workflow.steps = []
        mock_workflow.executions = []
        mock_workflow.schedules = []
        mock_workflow.alerts = []

        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_workflow

        result = await workflow_repository.get_workflow_with_steps(
            workflow_id,
            correlation_id="test-correlation-id"
        )

        assert result is not None
        assert result.id == workflow_id
        assert result.name == "Test Workflow"

    @pytest.mark.asyncio
    async def test_get_workflow_with_steps_not_found(
        self, workflow_repository, mock_session
    ):
        """Test workflow retrieval when workflow not found."""
        workflow_id = uuid4()
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        result = await workflow_repository.get_workflow_with_steps(
            workflow_id,
            correlation_id="test-correlation-id"
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_get_active_workflows_success(
        self, workflow_repository, mock_session
    ):
        """Test successful active workflows retrieval."""
        # Mock active workflows
        mock_workflows = [
            WorkflowDefinition(
                id=uuid4(),
                name=f"Workflow {i}",
                description=f"Description {i}",
                version="1.0.0",
                status=WorkflowStatus.ACTIVE,
                configuration={},
                tags=[],
                workflow_metadata={},
                is_active=True,
                priority=i,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            for i in range(3)
        ]

        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_workflows
        mock_session.execute.return_value.scalar.return_value = len(mock_workflows)

        result = await workflow_repository.get_active_workflows(
            correlation_id="test-correlation-id"
        )

        assert result.total_count == 3
        assert len(result.items) == 3
        assert all(workflow.is_active for workflow in result.items)
        assert all(workflow.status == WorkflowStatus.ACTIVE for workflow in result.items)


class TestJobDependencyRepository:
    """Test cases for JobDependencyRepository."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def dependency_repository(self, mock_session):
        """Create JobDependencyRepository instance."""
        return JobDependencyRepository(mock_session)

    @pytest.fixture
    def sample_dependency_data(self):
        """Create sample dependency creation data."""
        return JobDependencyCreate(
            dependent_job_id=uuid4(),
            prerequisite_job_id=uuid4(),
            dependency_type=DependencyType.FINISH_TO_START,
            condition_expression="status == 'completed'",
            is_optional=False,
            timeout_seconds=3600,
            dependency_metadata={"meta": "data"}
        )

    @pytest.mark.asyncio
    async def test_create_dependency_success(
        self, dependency_repository, sample_dependency_data, mock_session
    ):
        """Test successful dependency creation."""
        # Mock no circular dependency
        with patch.object(dependency_repository, '_would_create_cycle', return_value=False):
            created_dependency = JobDependency(
                id=uuid4(),
                dependent_job_id=sample_dependency_data.dependent_job_id,
                prerequisite_job_id=sample_dependency_data.prerequisite_job_id,
                dependency_type=sample_dependency_data.dependency_type,
                condition_expression=sample_dependency_data.condition_expression,
                is_optional=sample_dependency_data.is_optional,
                timeout_seconds=sample_dependency_data.timeout_seconds,
                dependency_metadata=sample_dependency_data.dependency_metadata,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            with patch.object(dependency_repository, 'create', return_value=created_dependency):
                result = await dependency_repository.create_dependency(
                    sample_dependency_data,
                    correlation_id="test-correlation-id"
                )

        assert result.dependent_job_id == sample_dependency_data.dependent_job_id
        assert result.prerequisite_job_id == sample_dependency_data.prerequisite_job_id
        assert result.dependency_type == sample_dependency_data.dependency_type

    @pytest.mark.asyncio
    async def test_create_dependency_circular_dependency(
        self, dependency_repository, sample_dependency_data, mock_session
    ):
        """Test dependency creation with circular dependency."""
        # Mock circular dependency detection
        with patch.object(dependency_repository, '_would_create_cycle', return_value=True):
            with pytest.raises(ConflictError) as exc_info:
                await dependency_repository.create_dependency(
                    sample_dependency_data,
                    correlation_id="test-correlation-id"
                )

        assert "circular dependency" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_job_dependencies_success(
        self, dependency_repository, mock_session
    ):
        """Test successful job dependencies retrieval."""
        job_id = uuid4()

        # Mock dependencies
        mock_dependencies = [
            JobDependency(
                id=uuid4(),
                dependent_job_id=job_id,
                prerequisite_job_id=uuid4(),
                dependency_type=DependencyType.FINISH_TO_START,
                condition_expression=None,
                is_optional=False,
                timeout_seconds=3600,
                dependency_metadata={},
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            for _ in range(2)
        ]

        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_dependencies

        result = await dependency_repository.get_job_dependencies(
            job_id,
            correlation_id="test-correlation-id"
        )

        assert len(result) == 2
        assert all(dep.dependent_job_id == job_id for dep in result)

    @pytest.mark.asyncio
    async def test_resolve_execution_order_success(
        self, dependency_repository, mock_session
    ):
        """Test successful execution order resolution."""
        workflow_id = uuid4()

        # Mock workflow steps
        step1_id = uuid4()
        step2_id = uuid4()
        step3_id = uuid4()

        mock_steps = [
            WorkflowStep(
                id=step1_id,
                workflow_definition_id=workflow_id,
                name="Step 1",
                step_order=1,
                task_name="task1",
                task_configuration={},
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            ),
            WorkflowStep(
                id=step2_id,
                workflow_definition_id=workflow_id,
                name="Step 2",
                step_order=2,
                task_name="task2",
                task_configuration={},
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            ),
            WorkflowStep(
                id=step3_id,
                workflow_definition_id=workflow_id,
                name="Step 3",
                step_order=3,
                task_name="task3",
                task_configuration={},
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        ]

        # Mock dependencies (step2 depends on step1, step3 depends on step2)
        mock_dependencies = [
            JobDependency(
                id=uuid4(),
                dependent_job_id=step2_id,
                prerequisite_job_id=step1_id,
                dependency_type=DependencyType.FINISH_TO_START,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            ),
            JobDependency(
                id=uuid4(),
                dependent_job_id=step3_id,
                prerequisite_job_id=step2_id,
                dependency_type=DependencyType.FINISH_TO_START,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        ]

        # Mock database calls
        mock_session.execute.side_effect = [
            # First call for steps
            MagicMock(scalars=MagicMock(return_value=MagicMock(all=MagicMock(return_value=mock_steps)))),
            # Second call for dependencies
            MagicMock(scalars=MagicMock(return_value=MagicMock(all=MagicMock(return_value=mock_dependencies))))
        ]

        result = await dependency_repository.resolve_execution_order(
            workflow_id,
            correlation_id="test-correlation-id"
        )

        assert len(result) == 3
        # Should be in topological order: step1, step2, step3
        assert result[0] == step1_id
        assert result[1] == step2_id
        assert result[2] == step3_id
