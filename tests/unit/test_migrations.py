"""
Unit tests for database migration functionality.

This module tests the migration infrastructure including:
- Migration manager functionality
- Migration validation and rollback
- Database schema comparison
- Migration safety checks
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock, AsyncMock
from pathlib import Path

from alembic.config import Config
from alembic.script import ScriptDirectory
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String
from sqlalchemy.ext.asyncio import create_async_engine

from app.db.migrations import (
    MigrationManager,
    MigrationInfo,
    MigrationValidationResult,
    validate_migration,
    create_backup,
    restore_backup
)
from app.core.config import settings


class TestMigrationManager:
    """Test cases for MigrationManager class."""
    
    @pytest.fixture
    def temp_alembic_config(self):
        """Create temporary alembic configuration for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "alembic.ini"
            
            # Create minimal alembic.ini
            config_content = f"""
[alembic]
script_location = {temp_dir}/migrations
sqlalchemy.url = sqlite:///:memory:

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
"""
            
            with open(config_path, 'w') as f:
                f.write(config_content)
            
            # Create migrations directory
            migrations_dir = Path(temp_dir) / "migrations"
            migrations_dir.mkdir()
            
            yield str(config_path)
    
    def test_migration_manager_initialization(self, temp_alembic_config):
        """Test MigrationManager initialization."""
        manager = MigrationManager(temp_alembic_config)
        
        assert manager.alembic_config_path == temp_alembic_config
        assert isinstance(manager.alembic_config, Config)
        assert isinstance(manager.script_directory, ScriptDirectory)
    
    @pytest.mark.asyncio
    async def test_get_migration_history(self, temp_alembic_config):
        """Test getting migration history."""
        manager = MigrationManager(temp_alembic_config)
        
        with patch.object(manager.script_directory, 'walk_revisions') as mock_walk:
            # Mock revision data
            mock_revision = MagicMock()
            mock_revision.revision = "001"
            mock_revision.doc = "Initial migration"
            mock_revision.branch_labels = None
            mock_revision.depends_on = None
            mock_revision.create_date = "2024-01-01"
            
            mock_walk.return_value = [mock_revision]
            
            history = manager.get_migration_history()
            
            assert len(history) == 1
            assert history[0].revision == "001"
            assert history[0].description == "Initial migration"
    
    @pytest.mark.asyncio
    async def test_validate_migration_schema(self, temp_alembic_config):
        """Test migration schema validation."""
        manager = MigrationManager(temp_alembic_config)
        
        with patch.object(manager, '_compare_schemas') as mock_compare:
            mock_compare.return_value = []
            
            result = await manager.validate_migration_schema("001")
            
            assert isinstance(result, MigrationValidationResult)
            assert result.is_valid
            assert len(result.issues) == 0
    
    @pytest.mark.asyncio
    async def test_create_migration_backup(self, temp_alembic_config):
        """Test creating migration backup."""
        manager = MigrationManager(temp_alembic_config)
        
        with patch('app.db.migrations.create_backup') as mock_backup:
            mock_backup.return_value = "/tmp/backup_001.sql"
            
            backup_path = await manager.create_migration_backup("001")
            
            assert backup_path == "/tmp/backup_001.sql"
            mock_backup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rollback_migration(self, temp_alembic_config):
        """Test migration rollback functionality."""
        manager = MigrationManager(temp_alembic_config)
        
        with patch('alembic.command.downgrade') as mock_downgrade:
            await manager.rollback_migration("001")
            
            mock_downgrade.assert_called_once()


class TestMigrationValidation:
    """Test cases for migration validation functions."""
    
    @pytest.mark.asyncio
    async def test_validate_migration_success(self):
        """Test successful migration validation."""
        with patch('app.db.migrations.MigrationManager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.validate_migration_schema = AsyncMock()
            mock_instance.validate_migration_schema.return_value = MigrationValidationResult(
                revision="001",
                is_valid=True,
                issues=[],
                warnings=[],
                validation_time=0.5
            )
            
            result = await validate_migration("001")
            
            assert result.is_valid
            assert len(result.issues) == 0
    
    @pytest.mark.asyncio
    async def test_validate_migration_with_issues(self):
        """Test migration validation with issues."""
        with patch('app.db.migrations.MigrationManager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.validate_migration_schema = AsyncMock()
            mock_instance.validate_migration_schema.return_value = MigrationValidationResult(
                revision="001",
                is_valid=False,
                issues=["Missing foreign key constraint"],
                warnings=["Index may impact performance"],
                validation_time=0.8
            )
            
            result = await validate_migration("001")
            
            assert not result.is_valid
            assert len(result.issues) == 1
            assert len(result.warnings) == 1


class TestMigrationBackup:
    """Test cases for migration backup and restore functionality."""
    
    @pytest.mark.asyncio
    async def test_create_backup_success(self):
        """Test successful backup creation."""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Backup created successfully"
            
            backup_path = await create_backup("test_revision")
            
            assert backup_path is not None
            assert "test_revision" in backup_path
            mock_run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_backup_failure(self):
        """Test backup creation failure."""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1
            mock_run.return_value.stderr = "Backup failed"
            
            with pytest.raises(Exception):
                await create_backup("test_revision")
    
    @pytest.mark.asyncio
    async def test_restore_backup_success(self):
        """Test successful backup restoration."""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Backup restored successfully"
            
            result = await restore_backup("/tmp/backup.sql")
            
            assert result is True
            mock_run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_restore_backup_failure(self):
        """Test backup restoration failure."""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1
            mock_run.return_value.stderr = "Restore failed"
            
            with pytest.raises(Exception):
                await restore_backup("/tmp/backup.sql")


class TestMigrationSafety:
    """Test cases for migration safety checks."""
    
    def test_migration_info_creation(self):
        """Test MigrationInfo dataclass creation."""
        info = MigrationInfo(
            revision="001",
            description="Initial migration",
            branch_labels=None,
            depends_on=None,
            created_at="2024-01-01",
            is_head=True,
            is_current=False
        )
        
        assert info.revision == "001"
        assert info.description == "Initial migration"
        assert info.is_head is True
        assert info.is_current is False
    
    def test_migration_validation_result_creation(self):
        """Test MigrationValidationResult dataclass creation."""
        result = MigrationValidationResult(
            revision="001",
            is_valid=True,
            issues=[],
            warnings=["Performance warning"],
            validation_time=1.2
        )
        
        assert result.revision == "001"
        assert result.is_valid is True
        assert len(result.warnings) == 1
        assert result.validation_time == 1.2
    
    @pytest.mark.asyncio
    async def test_migration_safety_checks(self):
        """Test migration safety validation."""
        with patch('app.db.migrations.MigrationManager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.check_migration_safety = AsyncMock()
            mock_instance.check_migration_safety.return_value = {
                "safe": True,
                "warnings": [],
                "blocking_issues": []
            }
            
            # This would be part of the migration safety check
            safety_result = await mock_instance.check_migration_safety("001")
            
            assert safety_result["safe"] is True
            assert len(safety_result["warnings"]) == 0


class TestMigrationIntegration:
    """Integration tests for migration functionality."""
    
    @pytest.mark.asyncio
    async def test_migration_workflow(self, temp_alembic_config):
        """Test complete migration workflow."""
        manager = MigrationManager(temp_alembic_config)
        
        with patch.multiple(
            manager,
            validate_migration_schema=AsyncMock(),
            create_migration_backup=AsyncMock(),
            apply_migration=AsyncMock(),
            verify_migration=AsyncMock()
        ):
            # Mock successful validation
            manager.validate_migration_schema.return_value = MigrationValidationResult(
                revision="001",
                is_valid=True,
                issues=[],
                warnings=[],
                validation_time=0.5
            )
            
            # Mock successful backup
            manager.create_migration_backup.return_value = "/tmp/backup_001.sql"
            
            # Mock successful application
            manager.apply_migration.return_value = True
            
            # Mock successful verification
            manager.verify_migration.return_value = True
            
            # Execute workflow
            validation = await manager.validate_migration_schema("001")
            backup_path = await manager.create_migration_backup("001")
            applied = await manager.apply_migration("001")
            verified = await manager.verify_migration("001")
            
            assert validation.is_valid
            assert backup_path is not None
            assert applied is True
            assert verified is True
