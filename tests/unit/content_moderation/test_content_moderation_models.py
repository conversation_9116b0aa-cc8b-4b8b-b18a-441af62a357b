"""
Tests for Content Moderation models.

This module tests the comprehensive content moderation models including:
- ContentModerationWorkflow model validation and relationships
- ContentQualityScore model validation and constraints
- PlagiarismCheck model validation and business logic
- ContentModerationRule model validation and configuration
- ContentApprovalHistory model validation and audit trail

Implements Task 3.2.4 testing requirements with >80% coverage.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from sqlalchemy.exc import IntegrityError

from app.models.content_moderation import (
    ContentModerationWorkflow, ContentQualityScore, PlagiarismCheck,
    ContentModerationRule, ContentApprovalHistory,
    ContentStatus, ContentType, ModerationAction, PlagiarismStatus
)


class TestContentModerationWorkflow:
    """Test ContentModerationWorkflow model."""

    def test_create_workflow_with_required_fields(self, db_session):
        """Test creating workflow with required fields."""
        vendor_id = uuid4()
        content_id = uuid4()
        
        workflow = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=content_id,
            vendor_id=vendor_id,
            content_title="Test Service",
            content_summary="Test service description"
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.id is not None
        assert workflow.content_type == ContentType.SERVICE_LISTING
        assert workflow.content_id == content_id
        assert workflow.vendor_id == vendor_id
        assert workflow.status == ContentStatus.PENDING
        assert workflow.priority == 5
        assert workflow.moderation_score == 0.0
        assert workflow.quality_score == 0.0
        assert workflow.plagiarism_score == 0.0
        assert workflow.created_at is not None
        assert workflow.submitted_at is not None

    def test_workflow_status_transitions(self, db_session):
        """Test workflow status transitions."""
        workflow = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4()
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        # Test status transitions
        workflow.status = ContentStatus.UNDER_REVIEW
        workflow.review_started_at = datetime.utcnow()
        db_session.commit()
        
        assert workflow.status == ContentStatus.UNDER_REVIEW
        assert workflow.review_started_at is not None
        
        workflow.status = ContentStatus.APPROVED
        workflow.review_completed_at = datetime.utcnow()
        db_session.commit()
        
        assert workflow.status == ContentStatus.APPROVED
        assert workflow.review_completed_at is not None

    def test_workflow_priority_constraints(self, db_session):
        """Test workflow priority constraints."""
        workflow = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4(),
            priority=10  # Maximum priority
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.priority == 10
        
        # Test invalid priority (should be handled by application logic)
        workflow.priority = 15
        with pytest.raises(Exception):  # Database constraint violation
            db_session.commit()

    def test_workflow_score_constraints(self, db_session):
        """Test workflow score constraints."""
        workflow = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4(),
            moderation_score=85.5,
            quality_score=92.3,
            plagiarism_score=12.7
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.moderation_score == 85.5
        assert workflow.quality_score == 92.3
        assert workflow.plagiarism_score == 12.7

    def test_workflow_metadata_storage(self, db_session):
        """Test workflow metadata storage."""
        metadata = {
            "source": "api",
            "version": "1.0",
            "tags": ["urgent", "review"],
            "custom_fields": {"priority_reason": "new_vendor"}
        }
        
        workflow = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4(),
            content_metadata=metadata
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.content_metadata == metadata
        assert workflow.content_metadata["source"] == "api"
        assert "urgent" in workflow.content_metadata["tags"]

    def test_workflow_unique_content_constraint(self, db_session):
        """Test unique constraint on content_type and content_id."""
        content_id = uuid4()
        
        workflow1 = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=content_id,
            vendor_id=uuid4()
        )
        
        db_session.add(workflow1)
        db_session.commit()
        
        # Try to create duplicate workflow for same content
        workflow2 = ContentModerationWorkflow(
            content_type=ContentType.SERVICE_LISTING,
            content_id=content_id,
            vendor_id=uuid4()
        )
        
        db_session.add(workflow2)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestContentQualityScore:
    """Test ContentQualityScore model."""

    def test_create_quality_score(self, db_session):
        """Test creating quality score with all fields."""
        workflow_id = uuid4()
        
        quality_score = ContentQualityScore(
            workflow_id=workflow_id,
            overall_score=87.5,
            weighted_score=89.2,
            quality_grade="B+",
            content_quality_score=85.0,
            image_quality_score=90.0,
            metadata_completeness_score=88.0,
            seo_optimization_score=92.0,
            mobile_friendliness_score=85.0,
            cultural_appropriateness_score=95.0
        )
        
        db_session.add(quality_score)
        db_session.commit()
        
        assert quality_score.id is not None
        assert quality_score.workflow_id == workflow_id
        assert quality_score.overall_score == 87.5
        assert quality_score.weighted_score == 89.2
        assert quality_score.quality_grade == "B+"
        assert quality_score.calculated_at is not None

    def test_quality_score_constraints(self, db_session):
        """Test quality score constraints."""
        quality_score = ContentQualityScore(
            workflow_id=uuid4(),
            overall_score=100.0,  # Maximum score
            content_quality_score=0.0,  # Minimum score
            image_quality_score=50.0
        )
        
        db_session.add(quality_score)
        db_session.commit()
        
        assert quality_score.overall_score == 100.0
        assert quality_score.content_quality_score == 0.0

    def test_quality_analysis_storage(self, db_session):
        """Test quality analysis data storage."""
        content_analysis = {
            "word_count": 250,
            "sentence_count": 15,
            "readability_score": 75.5,
            "grammar_issues": ["missing_comma", "run_on_sentence"]
        }
        
        image_analysis = {
            "image_count": 3,
            "total_size": 1500000,
            "formats": ["jpg", "png"],
            "resolution_quality": "high"
        }
        
        quality_score = ContentQualityScore(
            workflow_id=uuid4(),
            overall_score=85.0,
            content_analysis=content_analysis,
            image_analysis=image_analysis
        )
        
        db_session.add(quality_score)
        db_session.commit()
        
        assert quality_score.content_analysis == content_analysis
        assert quality_score.image_analysis == image_analysis
        assert quality_score.content_analysis["word_count"] == 250

    def test_quality_recommendations(self, db_session):
        """Test quality recommendations storage."""
        recommendations = [
            "Improve image quality by using higher resolution",
            "Add more detailed service descriptions",
            "Optimize SEO meta tags"
        ]
        
        priority_improvements = [
            "Fix grammar issues in description",
            "Add missing metadata fields"
        ]
        
        quality_score = ContentQualityScore(
            workflow_id=uuid4(),
            overall_score=75.0,
            recommendations=recommendations,
            priority_improvements=priority_improvements,
            estimated_improvement_impact=15.5
        )
        
        db_session.add(quality_score)
        db_session.commit()
        
        assert quality_score.recommendations == recommendations
        assert quality_score.priority_improvements == priority_improvements
        assert quality_score.estimated_improvement_impact == 15.5


class TestPlagiarismCheck:
    """Test PlagiarismCheck model."""

    def test_create_plagiarism_check(self, db_session):
        """Test creating plagiarism check."""
        workflow_id = uuid4()
        content_hash = "abc123def456"
        
        plagiarism_check = PlagiarismCheck(
            workflow_id=workflow_id,
            status=PlagiarismStatus.SIMILAR_FOUND,
            similarity_percentage=25.5,
            originality_score=74.5,
            confidence_level=85.0,
            similar_sources_found=3,
            exact_matches_found=1,
            paraphrased_matches_found=2,
            content_hash=content_hash,
            content_length=1500,
            words_checked=250
        )
        
        db_session.add(plagiarism_check)
        db_session.commit()
        
        assert plagiarism_check.id is not None
        assert plagiarism_check.workflow_id == workflow_id
        assert plagiarism_check.status == PlagiarismStatus.SIMILAR_FOUND
        assert plagiarism_check.similarity_percentage == 25.5
        assert plagiarism_check.originality_score == 74.5
        assert plagiarism_check.content_hash == content_hash
        assert plagiarism_check.checked_at is not None

    def test_plagiarism_similar_sources_storage(self, db_session):
        """Test similar sources storage."""
        similar_sources = [
            {
                "url": "https://example.com/similar-content",
                "title": "Similar Service Description",
                "similarity_score": 85.5,
                "source_type": "marketplace"
            },
            {
                "url": "https://competitor.com/service",
                "title": "Competitor Service",
                "similarity_score": 65.2,
                "source_type": "competitor"
            }
        ]
        
        matching_segments = [
            {
                "original_text": "Professional photography services",
                "source_url": "https://example.com/similar-content",
                "match_type": "exact",
                "similarity_score": 100.0
            }
        ]
        
        plagiarism_check = PlagiarismCheck(
            workflow_id=uuid4(),
            status=PlagiarismStatus.SIMILAR_FOUND,
            similarity_percentage=75.0,
            similar_sources=similar_sources,
            matching_segments=matching_segments,
            content_hash="test_hash_123"
        )
        
        db_session.add(plagiarism_check)
        db_session.commit()
        
        assert plagiarism_check.similar_sources == similar_sources
        assert plagiarism_check.matching_segments == matching_segments
        assert len(plagiarism_check.similar_sources) == 2

    def test_plagiarism_check_parameters(self, db_session):
        """Test plagiarism check parameters storage."""
        check_parameters = {
            "sensitivity_level": "high",
            "excluded_domains": ["example.com"],
            "language": "en",
            "check_method": "comprehensive"
        }
        
        source_analysis = {
            "total_sources": 5,
            "credible_sources": 3,
            "source_types": {"marketplace": 2, "competitor": 3},
            "average_similarity": 45.5
        }
        
        plagiarism_check = PlagiarismCheck(
            workflow_id=uuid4(),
            status=PlagiarismStatus.ORIGINAL,
            similarity_percentage=5.0,
            check_parameters=check_parameters,
            source_analysis=source_analysis,
            sensitivity_level="high",
            content_hash="original_content_hash"
        )
        
        db_session.add(plagiarism_check)
        db_session.commit()
        
        assert plagiarism_check.check_parameters == check_parameters
        assert plagiarism_check.source_analysis == source_analysis
        assert plagiarism_check.sensitivity_level == "high"


class TestContentModerationRule:
    """Test ContentModerationRule model."""

    def test_create_moderation_rule(self, db_session):
        """Test creating moderation rule."""
        detection_criteria = {
            "keywords": ["spam", "inappropriate"],
            "patterns": ["excessive_caps", "repeated_chars"],
            "thresholds": {"keyword_density": 0.1}
        }
        
        rule_logic = {
            "conditions": [
                {"field": "content", "operator": "contains", "value": "spam"},
                {"field": "keyword_density", "operator": "gt", "value": 0.1}
            ],
            "operator": "OR"
        }
        
        rule = ContentModerationRule(
            rule_name="Spam Detection Rule",
            rule_description="Detects spam content in service listings",
            content_types=[ContentType.SERVICE_LISTING, ContentType.SERVICE_DESCRIPTION],
            rule_category="spam_detection",
            rule_severity="high",
            detection_criteria=detection_criteria,
            action_on_trigger=ModerationAction.FLAG,
            rule_logic=rule_logic
        )
        
        db_session.add(rule)
        db_session.commit()
        
        assert rule.id is not None
        assert rule.rule_name == "Spam Detection Rule"
        assert rule.rule_category == "spam_detection"
        assert rule.rule_severity == "high"
        assert rule.is_active is True
        assert rule.auto_apply is True
        assert rule.created_at is not None

    def test_rule_performance_tracking(self, db_session):
        """Test rule performance tracking."""
        rule = ContentModerationRule(
            rule_name="Test Performance Rule",
            rule_category="test",
            detection_criteria={"test": True},
            action_on_trigger=ModerationAction.APPROVE,
            rule_logic={"conditions": [], "operator": "AND"},
            total_applications=100,
            successful_detections=85,
            false_positives=5,
            accuracy_rate=85.0
        )
        
        db_session.add(rule)
        db_session.commit()
        
        assert rule.total_applications == 100
        assert rule.successful_detections == 85
        assert rule.false_positives == 5
        assert rule.accuracy_rate == 85.0

    def test_rule_keywords_and_patterns(self, db_session):
        """Test rule keywords and patterns storage."""
        custom_keywords = ["inappropriate", "offensive", "spam"]
        excluded_keywords = ["legitimate", "professional"]
        regex_patterns = [r"\b[A-Z]{5,}\b", r"(.)\1{3,}"]
        
        rule = ContentModerationRule(
            rule_name="Content Filter Rule",
            rule_category="content_filter",
            detection_criteria={"type": "keyword_filter"},
            action_on_trigger=ModerationAction.REJECT,
            rule_logic={"conditions": [], "operator": "AND"},
            custom_keywords=custom_keywords,
            excluded_keywords=excluded_keywords,
            regex_patterns=regex_patterns
        )
        
        db_session.add(rule)
        db_session.commit()
        
        assert rule.custom_keywords == custom_keywords
        assert rule.excluded_keywords == excluded_keywords
        assert rule.regex_patterns == regex_patterns


class TestContentApprovalHistory:
    """Test ContentApprovalHistory model."""

    def test_create_approval_history(self, db_session):
        """Test creating approval history entry."""
        workflow_id = uuid4()
        reviewer_id = uuid4()
        
        history = ContentApprovalHistory(
            workflow_id=workflow_id,
            action_taken=ModerationAction.APPROVE,
            previous_status=ContentStatus.UNDER_REVIEW,
            new_status=ContentStatus.APPROVED,
            reviewer_id=reviewer_id,
            reviewer_role="content_moderator",
            decision_reason="Content meets quality standards",
            confidence_score=95.0,
            time_spent_minutes=15
        )
        
        db_session.add(history)
        db_session.commit()
        
        assert history.id is not None
        assert history.workflow_id == workflow_id
        assert history.action_taken == ModerationAction.APPROVE
        assert history.reviewer_id == reviewer_id
        assert history.confidence_score == 95.0
        assert history.decision_made_at is not None

    def test_approval_history_with_scores(self, db_session):
        """Test approval history with quality scores."""
        history = ContentApprovalHistory(
            workflow_id=uuid4(),
            action_taken=ModerationAction.REQUEST_REVISION,
            previous_status=ContentStatus.UNDER_REVIEW,
            new_status=ContentStatus.REQUIRES_REVISION,
            reviewer_id=uuid4(),
            quality_score_at_decision=65.5,
            moderation_score_at_decision=70.0,
            plagiarism_score_at_decision=15.5,
            revision_requirements=["Improve image quality", "Add more details"],
            estimated_revision_time=30
        )
        
        db_session.add(history)
        db_session.commit()
        
        assert history.quality_score_at_decision == 65.5
        assert history.moderation_score_at_decision == 70.0
        assert history.plagiarism_score_at_decision == 15.5
        assert "Improve image quality" in history.revision_requirements

    def test_approval_history_escalation(self, db_session):
        """Test approval history with escalation."""
        escalated_to = uuid4()
        
        history = ContentApprovalHistory(
            workflow_id=uuid4(),
            action_taken=ModerationAction.ESCALATE,
            previous_status=ContentStatus.UNDER_REVIEW,
            new_status=ContentStatus.FLAGGED,
            reviewer_id=uuid4(),
            escalated_to=escalated_to,
            escalation_reason="Complex content requires senior review",
            escalation_urgency="high",
            decision_factors=["cultural_sensitivity", "legal_compliance"]
        )
        
        db_session.add(history)
        db_session.commit()
        
        assert history.escalated_to == escalated_to
        assert history.escalation_reason == "Complex content requires senior review"
        assert history.escalation_urgency == "high"
        assert "cultural_sensitivity" in history.decision_factors

    def test_automated_approval_history(self, db_session):
        """Test automated approval history entry."""
        history = ContentApprovalHistory(
            workflow_id=uuid4(),
            action_taken=ModerationAction.AUTO_APPROVE,
            previous_status=ContentStatus.PENDING,
            new_status=ContentStatus.AUTO_APPROVED,
            reviewer_id=uuid4(),  # System user ID
            is_automated=True,
            confidence_score=98.5,
            automated_flags_considered=["high_quality_score", "trusted_vendor"],
            review_duration_minutes=0
        )
        
        db_session.add(history)
        db_session.commit()
        
        assert history.is_automated is True
        assert history.action_taken == ModerationAction.AUTO_APPROVE
        assert history.confidence_score == 98.5
        assert "high_quality_score" in history.automated_flags_considered
