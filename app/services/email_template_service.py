"""
Email Template Service for Culture Connect Backend API.

This module provides comprehensive email template management including:
- Template CRUD operations with versioning support
- Template validation and variable substitution
- Category-based template organization
- Template activation/deactivation management
- Integration with Jinja2 for template rendering

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
production-grade error handling, structured logging, and seamless integration
with existing authentication infrastructure.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from jinja2 import Template, TemplateError, Environment, BaseLoader
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.models.email_models import EmailTemplate, EmailTemplateCategory
from app.repositories.email_repository import EmailTemplateRepository
from app.schemas.email_schemas import (
    EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailTemplateListResponse
)
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class EmailTemplateService(BaseService[EmailTemplate, EmailTemplateRepository]):
    """
    Email template service for comprehensive template management.

    Provides template CRUD operations, versioning, validation, and rendering
    with production-grade error handling and performance optimization.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email template service."""
        super().__init__(EmailTemplateRepository, EmailTemplate, db_session=db)
        self.jinja_env = Environment(loader=BaseLoader())

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for email template creation."""
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for email template updates."""
        return data

    async def create_template(
        self,
        template_data: EmailTemplateCreate,
        created_by: int
    ) -> EmailTemplateResponse:
        """
        Create a new email template with validation.

        Args:
            template_data: Template creation data
            created_by: ID of user creating the template

        Returns:
            EmailTemplateResponse: Created template data

        Raises:
            ValidationError: If template data is invalid
            ConflictError: If template name/version already exists
        """
        try:
            # Validate template syntax
            await self._validate_template_syntax(
                template_data.subject_template,
                template_data.body_template,
                template_data.html_template,
                template_data.variables
            )

            # Check for existing template with same name/version
            existing = await self.repository.get_template_by_name_and_version(
                template_data.name,
                template_data.version or 1
            )
            if existing:
                raise ConflictError(
                    f"Template '{template_data.name}' version {template_data.version or 1} already exists"
                )

            # Create template
            template = await self.repository.create_template(
                template_data=template_data,
                created_by=created_by
            )

            logger.info(
                f"Email template created successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template.id),
                    "template_name": template.name,
                    "template_version": template.version,
                    "created_by": created_by
                }
            )

            return EmailTemplateResponse.model_validate(template)

        except (ValidationError, ConflictError):
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error creating email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_name": template_data.name,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to create email template: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error creating email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_name": template_data.name,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error creating email template: {str(e)}")

    async def get_template(self, template_id: UUID) -> EmailTemplateResponse:
        """
        Get email template by ID.

        Args:
            template_id: Template UUID

        Returns:
            EmailTemplateResponse: Template data

        Raises:
            NotFoundError: If template not found
        """
        try:
            template = await self.repository.get(template_id)
            if not template:
                raise NotFoundError(f"Email template {template_id} not found")

            return EmailTemplateResponse.model_validate(template)

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error retrieving email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve email template: {str(e)}")

    async def update_template(
        self,
        template_id: UUID,
        template_data: EmailTemplateUpdate,
        updated_by: int
    ) -> EmailTemplateResponse:
        """
        Update email template.

        Args:
            template_id: Template UUID
            template_data: Template update data
            updated_by: ID of user updating the template

        Returns:
            EmailTemplateResponse: Updated template data

        Raises:
            NotFoundError: If template not found
            ValidationError: If template data is invalid
        """
        try:
            # Get existing template
            template = self.repository.get(template_id)
            if not template:
                raise NotFoundError(f"Email template {template_id} not found")

            # Validate template syntax if templates are being updated
            if any([
                template_data.subject_template,
                template_data.body_template,
                template_data.html_template is not None
            ]):
                await self._validate_template_syntax(
                    template_data.subject_template or template.subject_template,
                    template_data.body_template or template.body_template,
                    template_data.html_template if template_data.html_template is not None else template.html_template,
                    template_data.variables or template.variables
                )

            # Update template using inherited update method
            update_data = template_data.model_dump(exclude_unset=True)
            updated_template = self.repository.update(template_id, update_data)

            logger.info(
                f"Email template updated successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id),
                    "template_name": updated_template.name,
                    "updated_by": updated_by
                }
            )

            return EmailTemplateResponse.model_validate(updated_template)

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(
                f"Error updating email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to update email template: {str(e)}")

    async def delete_template(self, template_id: UUID) -> bool:
        """
        Delete email template.

        Args:
            template_id: Template UUID

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If template not found
        """
        try:
            success = self.repository.delete(template_id)
            if not success:
                raise NotFoundError(f"Email template {template_id} not found")

            logger.info(
                f"Email template deleted successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id)
                }
            )

            return True

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error deleting email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to delete email template: {str(e)}")

    async def list_templates(
        self,
        category: Optional[EmailTemplateCategory] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> EmailTemplateListResponse:
        """
        List email templates with filtering.

        Args:
            category: Optional category filter
            is_active: Optional active status filter
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            EmailTemplateListResponse: List of templates with metadata
        """
        try:
            # Use inherited list method with filters
            templates = self.repository.list(
                skip=skip,
                limit=limit
            )
            # For now, return all templates - filtering can be added later
            total = len(templates)

            template_responses = [
                EmailTemplateResponse.model_validate(template)
                for template in templates
            ]

            return EmailTemplateListResponse(
                templates=template_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(
                f"Error listing email templates: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "category": category.value if category else None,
                    "is_active": is_active,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to list email templates: {str(e)}")

    async def render_template(
        self,
        template_id: UUID,
        variables: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Render email template with provided variables.

        Args:
            template_id: Template UUID
            variables: Variables for template substitution

        Returns:
            Dict[str, str]: Rendered subject, body, and html_body

        Raises:
            NotFoundError: If template not found
            ValidationError: If template rendering fails
        """
        try:
            template = self.repository.get(template_id)
            if not template:
                raise NotFoundError(f"Email template {template_id} not found")

            if not template.is_active:
                raise ValidationError(f"Email template {template_id} is not active")

            # Render templates
            subject = self._render_template_string(template.subject_template, variables)
            body = self._render_template_string(template.body_template, variables)
            html_body = None

            if template.html_template:
                html_body = self._render_template_string(template.html_template, variables)

            return {
                "subject": subject,
                "body": body,
                "html_body": html_body
            }

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(
                f"Error rendering email template: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "template_id": str(template_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to render email template: {str(e)}")

    async def _validate_template_syntax(
        self,
        subject_template: str,
        body_template: str,
        html_template: Optional[str],
        variables: Dict[str, Any]
    ) -> None:
        """
        Validate Jinja2 template syntax.

        Args:
            subject_template: Subject template string
            body_template: Body template string
            html_template: Optional HTML template string
            variables: Template variables for validation

        Raises:
            ValidationError: If template syntax is invalid
        """
        try:
            # Test render with sample variables
            test_vars = {key: f"test_{key}" for key in variables.keys()}

            self._render_template_string(subject_template, test_vars)
            self._render_template_string(body_template, test_vars)

            if html_template:
                self._render_template_string(html_template, test_vars)

        except TemplateError as e:
            raise ValidationError(f"Invalid template syntax: {str(e)}")

    def _render_template_string(self, template_string: str, variables: Dict[str, Any]) -> str:
        """
        Render a template string with variables.

        Args:
            template_string: Template string to render
            variables: Variables for substitution

        Returns:
            str: Rendered template

        Raises:
            TemplateError: If rendering fails
        """
        template = self.jinja_env.from_string(template_string)
        return template.render(**variables)
