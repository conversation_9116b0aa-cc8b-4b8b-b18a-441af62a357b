"""
Performance Monitoring Service for Culture Connect Backend API.

This module provides comprehensive performance monitoring services including:
- PerformanceMonitoringService: APM integration and system metrics collection
- Real-time performance tracking with Sentry integration
- Custom metrics collection and aggregation
- Performance optimization insights and recommendations
- Circuit breaker patterns for external service reliability
- Integration with PerformanceMetricsRepository for data persistence

Implements Phase 7.2.3 requirements for performance monitoring service layer with:
- Performance optimization targeting <500ms for creation operations, <200ms for queries
- APM integration with Sentry and custom metrics collection
- Circuit breaker patterns and transaction management with rollback capabilities
- Comprehensive error handling with correlation IDs and structured logging

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.services.base import BaseService, ServiceError, ValidationError
from app.repositories.performance_monitoring_repositories import PerformanceMetricsRepository
from app.models.analytics_models import PerformanceMetrics, PerformanceMetricType, AnalyticsTimeframe
from app.core.circuit_breaker import get_circuit_breaker, CircuitBreakerConfig
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.db.session import get_db_session

try:
    import sentry_sdk
    from sentry_sdk import capture_exception, capture_message, set_tag, set_context
    SENTRY_AVAILABLE = True
except ImportError:
    SENTRY_AVAILABLE = False


class PerformanceMonitoringService(BaseService[PerformanceMetrics, PerformanceMetricsRepository]):
    """
    Performance monitoring service for APM integration and system metrics collection.

    Provides comprehensive performance monitoring capabilities including:
    - Real-time performance metrics collection and storage
    - APM integration with Sentry for custom performance tracking
    - System metrics aggregation and analysis
    - Performance optimization insights and recommendations
    - Circuit breaker patterns for external service reliability
    """

    def __init__(self):
        """Initialize performance monitoring service."""
        super().__init__(PerformanceMetrics, PerformanceMetricsRepository)
        self.logger = logging.getLogger(f"{__name__}.PerformanceMonitoringService")
        self._circuit_breaker = get_circuit_breaker(
            "performance_monitoring_service",
            CircuitBreakerConfig(failure_threshold=5, timeout=30)
        )

    async def record_performance_metric(
        self,
        metric_type: PerformanceMetricType,
        metric_name: str,
        component: str,
        value: Decimal,
        timeframe: AnalyticsTimeframe = AnalyticsTimeframe.REAL_TIME,
        tags: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> PerformanceMetrics:
        """
        Record a performance metric with APM integration.

        Performance Metrics:
        - Target response time: <500ms for metric creation
        - Validation: <100ms using optimized patterns
        - Database insertion: <300ms with bulk optimization support
        - Sentry integration: <50ms for APM tracking

        Args:
            metric_type: Type of performance metric
            metric_name: Specific metric name or identifier
            component: System component (api, database, cache, etc.)
            value: Primary metric value
            timeframe: Time aggregation period
            tags: Additional metric tags and labels
            metadata: Additional metric metadata
            **kwargs: Additional metric fields

        Returns:
            PerformanceMetrics: Created performance metric

        Raises:
            ValidationError: If metric data validation fails
            ServiceError: If metric recording fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            # Validate input data
            if value < 0:
                raise ValidationError("Metric value cannot be negative")

            if len(metric_name) > 100:
                raise ValidationError("Metric name too long (max 100 characters)")

            if len(component) > 50:
                raise ValidationError("Component name too long (max 50 characters)")

            # Prepare metric data
            metric_data = {
                "metric_type": metric_type,
                "metric_name": metric_name,
                "component": component,
                "value": value,
                "timeframe": timeframe,
                "timestamp": datetime.utcnow(),
                "tags": tags,
                "metric_metadata": metadata,
                **kwargs
            }

            # Record metric using repository
            async with self.get_session_context() as session:
                repository = PerformanceMetricsRepository(session)
                metric = await repository.create_metric(
                    metric_type=metric_type,
                    metric_name=metric_name,
                    component=component,
                    value=value,
                    timeframe=timeframe,
                    tags=tags,
                    metric_metadata=metadata,
                    **kwargs
                )

                # Integrate with Sentry APM
                if SENTRY_AVAILABLE:
                    await self._record_sentry_metric(metric)

                # Record internal metrics
                metrics_collector.record_histogram(
                    "performance_metric_recorded",
                    time.time() - start_time,
                    tags={
                        "metric_type": metric_type.value,
                        "component": component,
                        "timeframe": timeframe.value
                    }
                )

                operation_time = time.time() - start_time
                self.logger.info(
                    f"Performance metric recorded successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "metric_type": metric_type.value,
                        "component": component,
                        "metric_name": metric_name,
                        "operation_time": operation_time,
                        "metric_id": metric.id
                    }
                )

                return metric

        except Exception as e:
            operation_time = time.time() - start_time
            self.logger.error(
                f"Failed to record performance metric: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "metric_type": metric_type.value if metric_type else None,
                    "component": component,
                    "error": str(e),
                    "operation_time": operation_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            await self.handle_service_error(e, "record_performance_metric", {
                "metric_type": metric_type.value if metric_type else None,
                "component": component,
                "metric_name": metric_name
            })

    async def get_performance_metrics(
        self,
        component: Optional[str] = None,
        metric_type: Optional[PerformanceMetricType] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        timeframe: Optional[AnalyticsTimeframe] = None,
        limit: int = 1000
    ) -> List[PerformanceMetrics]:
        """
        Get performance metrics with filtering and optimization.

        Performance Metrics:
        - Target response time: <200ms for metric queries
        - Cache optimization: >90% cache hit rate for frequently accessed metrics
        - Query optimization: Composite indexes for component + timestamp queries

        Args:
            component: Optional component filter
            metric_type: Optional metric type filter
            start_time: Optional start time filter
            end_time: Optional end time filter
            timeframe: Optional timeframe filter
            limit: Maximum number of records to return

        Returns:
            List[PerformanceMetrics]: List of performance metrics

        Raises:
            ServiceError: If query fails
        """
        start_query_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            async with self.get_session_context() as session:
                repository = PerformanceMetricsRepository(session)
                metrics = await repository.get_metrics_by_component(
                    component=component,
                    metric_type=metric_type,
                    start_time=start_time,
                    end_time=end_time,
                    timeframe=timeframe,
                    limit=limit
                )

                query_time = time.time() - start_query_time
                self.logger.info(
                    f"Performance metrics retrieved successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "component": component,
                        "metric_type": metric_type.value if metric_type else None,
                        "query_time": query_time,
                        "result_count": len(metrics)
                    }
                )

                return metrics

        except Exception as e:
            query_time = time.time() - start_query_time
            self.logger.error(
                f"Failed to get performance metrics: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "component": component,
                    "error": str(e),
                    "query_time": query_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            await self.handle_service_error(e, "get_performance_metrics", {
                "component": component,
                "metric_type": metric_type.value if metric_type else None
            })

    async def get_aggregated_performance_metrics(
        self,
        component: Optional[str] = None,
        metric_type: Optional[PerformanceMetricType] = None,
        timeframe: AnalyticsTimeframe = AnalyticsTimeframe.HOURLY,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get aggregated performance metrics with statistical analysis.

        Performance Metrics:
        - Target response time: <200ms for aggregation queries
        - Cache optimization: 30-minute TTL for aggregated data
        - Statistical calculations: min, max, avg, percentiles

        Args:
            component: Optional component filter
            metric_type: Optional metric type filter
            timeframe: Time aggregation period
            start_time: Optional start time filter
            end_time: Optional end time filter

        Returns:
            Dict[str, Any]: Aggregated metrics with statistics

        Raises:
            ServiceError: If aggregation fails
        """
        start_query_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            async with self.get_session_context() as session:
                repository = PerformanceMetricsRepository(session)
                aggregated_data = await repository.get_aggregated_metrics(
                    component=component,
                    metric_type=metric_type,
                    timeframe=timeframe,
                    start_time=start_time,
                    end_time=end_time
                )

                query_time = time.time() - start_query_time
                self.logger.info(
                    f"Performance metrics aggregated successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "component": component,
                        "metric_type": metric_type.value if metric_type else None,
                        "query_time": query_time,
                        "total_count": aggregated_data.get("total_count", 0)
                    }
                )

                return aggregated_data

        except Exception as e:
            query_time = time.time() - start_query_time
            self.logger.error(
                f"Failed to aggregate performance metrics: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "component": component,
                    "error": str(e),
                    "query_time": query_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            await self.handle_service_error(e, "get_aggregated_performance_metrics", {
                "component": component,
                "metric_type": metric_type.value if metric_type else None
            })

    async def _record_sentry_metric(self, metric: PerformanceMetrics) -> None:
        """
        Record performance metric in Sentry APM.

        Args:
            metric: Performance metric to record in Sentry
        """
        if not SENTRY_AVAILABLE:
            return

        try:
            # Set Sentry context
            set_context("performance_metric", {
                "metric_type": metric.metric_type.value,
                "metric_name": metric.metric_name,
                "component": metric.component,
                "value": float(metric.value),
                "timeframe": metric.timeframe.value,
                "timestamp": metric.timestamp.isoformat()
            })

            # Set tags for filtering
            set_tag("metric_type", metric.metric_type.value)
            set_tag("component", metric.component)
            set_tag("timeframe", metric.timeframe.value)

            # Record custom metric in Sentry
            sentry_sdk.set_measurement(
                f"{metric.component}_{metric.metric_name}",
                float(metric.value),
                unit="millisecond" if "time" in metric.metric_name.lower() else "count"
            )

        except Exception as e:
            self.logger.warning(f"Failed to record Sentry metric: {str(e)}")

    async def get_performance_insights(
        self,
        component: Optional[str] = None,
        hours_back: int = 24
    ) -> Dict[str, Any]:
        """
        Get performance optimization insights and recommendations.

        Args:
            component: Optional component filter
            hours_back: Number of hours to analyze

        Returns:
            Dict[str, Any]: Performance insights and recommendations
        """
        start_time = datetime.utcnow() - timedelta(hours=hours_back)
        correlation_id_val = correlation_id.get('')

        try:
            # Get aggregated metrics for analysis
            aggregated_data = await self.get_aggregated_performance_metrics(
                component=component,
                timeframe=AnalyticsTimeframe.HOURLY,
                start_time=start_time
            )

            insights = {
                "analysis_period": {
                    "start_time": start_time.isoformat(),
                    "end_time": datetime.utcnow().isoformat(),
                    "hours_analyzed": hours_back
                },
                "performance_summary": {
                    "total_metrics": aggregated_data.get("total_count", 0),
                    "avg_value": aggregated_data.get("avg_value", 0.0),
                    "p95_value": aggregated_data.get("p95_value", 0.0),
                    "p99_value": aggregated_data.get("p99_value", 0.0),
                    "error_rate": aggregated_data.get("error_rate", 0.0)
                },
                "recommendations": [],
                "alerts": []
            }

            # Generate recommendations based on metrics
            if aggregated_data.get("p95_value", 0) > 500:  # 500ms threshold
                insights["recommendations"].append({
                    "type": "performance",
                    "priority": "high",
                    "message": "95th percentile response time exceeds 500ms threshold",
                    "suggested_actions": [
                        "Review database query performance",
                        "Check for N+1 query patterns",
                        "Consider implementing caching",
                        "Optimize API endpoint logic"
                    ]
                })

            if aggregated_data.get("error_rate", 0) > 0.05:  # 5% error rate
                insights["alerts"].append({
                    "type": "error_rate",
                    "severity": "critical",
                    "message": f"Error rate ({aggregated_data.get('error_rate', 0):.2%}) exceeds 5% threshold",
                    "immediate_actions": [
                        "Check application logs for error patterns",
                        "Review recent deployments",
                        "Monitor system resources",
                        "Consider circuit breaker activation"
                    ]
                })

            self.logger.info(
                f"Performance insights generated successfully",
                extra={
                    "correlation_id": correlation_id_val,
                    "component": component,
                    "recommendations_count": len(insights["recommendations"]),
                    "alerts_count": len(insights["alerts"])
                }
            )

            return insights

        except Exception as e:
            self.logger.error(
                f"Failed to generate performance insights: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "component": component,
                    "error": str(e)
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            await self.handle_service_error(e, "get_performance_insights", {
                "component": component,
                "hours_back": hours_back
            })

    async def bulk_record_metrics(
        self,
        metrics_data: List[Dict[str, Any]]
    ) -> List[PerformanceMetrics]:
        """
        Record multiple performance metrics in bulk for high-throughput scenarios.

        Performance Metrics:
        - Target throughput: >1000 records/second
        - Batch processing: Optimized for bulk operations
        - Transaction management: Rollback on any failure

        Args:
            metrics_data: List of metric data dictionaries

        Returns:
            List[PerformanceMetrics]: Created performance metrics

        Raises:
            ValidationError: If any metric data validation fails
            ServiceError: If bulk operation fails
        """
        start_time = time.time()
        correlation_id_val = correlation_id.get('')

        try:
            if not metrics_data:
                raise ValidationError("No metrics data provided")

            if len(metrics_data) > 1000:
                raise ValidationError("Bulk operation limited to 1000 metrics per batch")

            created_metrics = []

            async with self.get_session_context() as session:
                repository = PerformanceMetricsRepository(session)

                for metric_data in metrics_data:
                    # Validate each metric
                    if metric_data.get("value", 0) < 0:
                        raise ValidationError(f"Metric value cannot be negative: {metric_data}")

                    # Create metric
                    metric = await repository.create_metric(
                        metric_type=metric_data["metric_type"],
                        metric_name=metric_data["metric_name"],
                        component=metric_data["component"],
                        value=metric_data["value"],
                        timeframe=metric_data.get("timeframe", AnalyticsTimeframe.REAL_TIME),
                        tags=metric_data.get("tags"),
                        metric_metadata=metric_data.get("metric_metadata"),
                        **{k: v for k, v in metric_data.items() if k not in [
                            "metric_type", "metric_name", "component", "value",
                            "timeframe", "tags", "metric_metadata"
                        ]}
                    )
                    created_metrics.append(metric)

                operation_time = time.time() - start_time
                throughput = len(created_metrics) / operation_time if operation_time > 0 else 0

                self.logger.info(
                    f"Bulk metrics recorded successfully",
                    extra={
                        "correlation_id": correlation_id_val,
                        "metrics_count": len(created_metrics),
                        "operation_time": operation_time,
                        "throughput_per_second": throughput
                    }
                )

                return created_metrics

        except Exception as e:
            operation_time = time.time() - start_time
            self.logger.error(
                f"Failed to bulk record metrics: {str(e)}",
                extra={
                    "correlation_id": correlation_id_val,
                    "metrics_count": len(metrics_data) if metrics_data else 0,
                    "error": str(e),
                    "operation_time": operation_time
                }
            )

            if SENTRY_AVAILABLE:
                capture_exception(e)

            await self.handle_service_error(e, "bulk_record_metrics", {
                "metrics_count": len(metrics_data) if metrics_data else 0
            })

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for performance metrics creation.

        Args:
            data: Raw input data for validation

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Basic validation for required fields
        required_fields = ['metric_type', 'metric_name', 'component', 'value', 'timeframe']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate metric_type
        if not isinstance(data.get('metric_type'), PerformanceMetricType):
            raise ValidationError("Invalid metric_type")

        # Validate timeframe
        if not isinstance(data.get('timeframe'), AnalyticsTimeframe):
            raise ValidationError("Invalid timeframe")

        # Validate value is numeric
        try:
            Decimal(str(data['value']))
        except (ValueError, TypeError):
            raise ValidationError("Value must be numeric")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate data for performance metrics updates.

        Args:
            data: Raw input data for validation
            existing_id: ID of existing record being updated

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate value if provided
        if 'value' in data:
            try:
                Decimal(str(data['value']))
            except (ValueError, TypeError):
                raise ValidationError("Value must be numeric")

        # Validate metric_type if provided
        if 'metric_type' in data and not isinstance(data.get('metric_type'), PerformanceMetricType):
            raise ValidationError("Invalid metric_type")

        # Validate timeframe if provided
        if 'timeframe' in data and not isinstance(data.get('timeframe'), AnalyticsTimeframe):
            raise ValidationError("Invalid timeframe")

        return data
