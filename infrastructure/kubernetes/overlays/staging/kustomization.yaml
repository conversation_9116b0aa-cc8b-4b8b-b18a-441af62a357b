# Culture Connect Backend - Staging Kubernetes Overlay
# Kustomize configuration for staging environment deployment

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Base resources to customize
resources:
  - ../../base

# Namespace for staging
namespace: culture-connect-staging

# Common labels for all resources
commonLabels:
  environment: staging
  deployment-tier: staging
  app.kubernetes.io/environment: staging

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/environment: staging
  config.kubernetes.io/local-config: "false"

# Name prefix for all resources
namePrefix: staging-

# Images to use (staging tags)
images:
  - name: ghcr.io/culture-connect/backend
    newTag: staging-latest

# ConfigMap generator for staging-specific config
configMapGenerator:
  - name: culture-connect-config
    behavior: merge
    literals:
      - CC_ENVIRONMENT=staging
      - CC_DEBUG=false
      - CC_LOG_LEVEL=INFO
      - CC_WORKERS=2
      - CC_RELOAD=false
      - CC_ENABLE_DOCS=true
      - CC_ENABLE_REDOC=true
      - CC_ENABLE_PROFILER=true
      - CC_ENABLE_DEBUG_TOOLBAR=false
      - CC_MOCK_EXTERNAL_SERVICES=false
      - CC_SKIP_EMAIL_VERIFICATION=false
      - CC_BYPASS_RATE_LIMITING=false
      - CC_ENABLE_SQL_LOGGING=false
      - CC_ENABLE_LOAD_TESTING=true
      - CC_LOAD_TEST_MAX_USERS=1000
      - CC_LOAD_TEST_DURATION=300

# Secret generator for staging secrets
secretGenerator:
  - name: culture-connect-secrets
    behavior: merge
    literals:
      - CC_SECRET_KEY=staging_secret_key_2024_change_in_production_abcdef123456
      - CC_DATABASE_URL=postgresql+asyncpg://culture_connect_staging:staging_secure_password_2024@postgres:5432/culture_connect_staging_db
      - CC_REDIS_URL=redis://:staging_redis_password_2024@redis:6379/0
      - CC_PAYSTACK_SECRET_KEY=sk_test_staging_paystack_secret_key
      - CC_STRIPE_SECRET_KEY=sk_test_staging_stripe_secret_key
      - CC_BUSHA_API_KEY=staging_busha_api_key
      - CC_AIML_API_KEY=staging_aiml_api_key
      - CC_SENTRY_DSN=https://<EMAIL>/staging_project_id

# Patches for staging-specific modifications
patches:
  # Staging resource requirements (production-like but smaller)
  - target:
      kind: Deployment
      name: culture-connect-api
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 2
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "384Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "200m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "768Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "400m"

  # Staging Celery worker resources
  - target:
      kind: Deployment
      name: culture-connect-celery-worker
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 2
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "192Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "100m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "384Mi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "200m"

  # Staging HPA configuration
  - target:
      kind: HorizontalPodAutoscaler
      name: culture-connect-api-hpa
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 2
      - op: replace
        path: /spec/maxReplicas
        value: 5
      - op: replace
        path: /spec/metrics/0/resource/target/averageUtilization
        value: 70

  # Staging ingress configuration
  - target:
      kind: Ingress
      name: culture-connect-ingress
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: staging-api.cultureconnect.ng
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1rate-limit
        value: "500"
      - op: add
        path: /metadata/annotations/nginx.ingress.kubernetes.io~1rate-limit-window
        value: "1m"

# Staging-specific resource configurations
replicas:
  - name: culture-connect-api
    count: 2
  - name: culture-connect-celery-worker
    count: 2
  - name: culture-connect-celery-beat
    count: 1

# Additional staging resources
patchesStrategicMerge:
  - staging-monitoring-patch.yaml
