"""
Vendor schemas for Culture Connect Backend API.

This module provides Pydantic schemas for vendor-related operations
including vendor registration, profile management, and document verification.
"""

from datetime import datetime, time
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union, Literal
from pydantic import BaseModel, EmailStr, Field, field_validator, HttpUrl

from app.models.vendor import (
    VendorType, VerificationStatus, MarketplaceStatus,
    DocumentType, DocumentStatus
)


class VendorBase(BaseModel):
    """Base vendor schema with common fields."""

    business_name: str = Field(..., min_length=1, max_length=255, description="Official business name")
    business_type: VendorType = Field(..., description="Type of business/service provider")
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Official business registration number"
    )
    tax_id: Optional[str] = Field(None, max_length=50, description="Tax identification number")


class VendorCreate(VendorBase):
    """Schema for vendor registration."""

    # Profile information
    description: Optional[str] = Field(None, description="Business description and story")
    short_description: Optional[str] = Field(
        None, max_length=500, description="Short business description for listings"
    )
    tagline: Optional[str] = Field(None, max_length=255, description="Business tagline or slogan")

    # Contact information
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone number")
    contact_email: Optional[EmailStr] = Field(None, description="Primary contact email address")
    website_url: Optional[HttpUrl] = Field(None, description="Business website URL")

    # Business address
    business_address: Optional[str] = Field(None, description="Complete business address")
    city: Optional[str] = Field(None, max_length=100, description="Business city")
    state: Optional[str] = Field(None, max_length=100, description="Business state/region")
    country: Optional[str] = Field(None, max_length=100, description="Business country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Business postal/zip code")

    # Operating details
    languages_spoken: Optional[List[str]] = Field(None, description="Languages spoken by business")
    specializations: Optional[List[str]] = Field(None, description="Business specializations")
    max_group_size: Optional[int] = Field(None, gt=0, description="Maximum group size")

    @field_validator('contact_phone')
    @classmethod
    def validate_phone_number(cls, v):
        """Validate phone number format."""
        if v is not None:
            # Remove spaces and common separators
            cleaned = ''.join(c for c in v if c.isdigit() or c == '+')
            if not cleaned.startswith('+'):
                cleaned = '+' + cleaned
            # Basic validation for international format
            if len(cleaned) < 8 or len(cleaned) > 16:
                raise ValueError('Phone number must be between 8 and 16 digits')
        return v


class VendorUpdate(BaseModel):
    """Schema for vendor profile updates."""

    business_name: Optional[str] = Field(None, min_length=1, max_length=255)
    business_registration_number: Optional[str] = Field(None, max_length=100)
    tax_id: Optional[str] = Field(None, max_length=50)


class VendorProfileBase(BaseModel):
    """Base vendor profile schema."""

    description: Optional[str] = Field(None, description="Business description and story")
    short_description: Optional[str] = Field(
        None, max_length=500, description="Short business description for listings"
    )
    tagline: Optional[str] = Field(None, max_length=255, description="Business tagline or slogan")

    # Contact information
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone number")
    contact_email: Optional[EmailStr] = Field(None, description="Primary contact email address")
    website_url: Optional[HttpUrl] = Field(None, description="Business website URL")

    # Business address
    business_address: Optional[str] = Field(None, description="Complete business address")
    city: Optional[str] = Field(None, max_length=100, description="Business city")
    state: Optional[str] = Field(None, max_length=100, description="Business state/region")
    country: Optional[str] = Field(None, max_length=100, description="Business country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Business postal/zip code")

    # Location coordinates
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90, description="Business location latitude")
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180, description="Business location longitude")

    # Operating details
    operating_hours: Optional[Dict[str, Any]] = Field(None, description="Operating hours by day of week")
    timezone: Optional[str] = Field(None, max_length=50, description="Business timezone")

    # Social media and online presence
    social_media_links: Optional[Dict[str, str]] = Field(None, description="Social media profile links")

    # Business capabilities
    languages_spoken: Optional[List[str]] = Field(None, description="Languages spoken by business")
    specializations: Optional[List[str]] = Field(None, description="Business specializations")
    certifications: Optional[List[str]] = Field(None, description="Professional certifications")

    # Capacity and logistics
    max_group_size: Optional[int] = Field(None, gt=0, description="Maximum group size")
    min_advance_booking: Optional[int] = Field(
        None, ge=0, description="Minimum advance booking hours required"
    )
    cancellation_policy: Optional[str] = Field(None, description="Cancellation policy details")

    # Media
    logo_url: Optional[HttpUrl] = Field(None, description="Business logo image URL")
    cover_image_url: Optional[HttpUrl] = Field(None, description="Cover image URL")
    gallery_images: Optional[List[HttpUrl]] = Field(None, description="Gallery image URLs")

    # Business verification details
    years_in_business: Optional[int] = Field(None, ge=0, description="Number of years in business")
    business_license_number: Optional[str] = Field(None, max_length=100, description="Business license number")
    insurance_details: Optional[Dict[str, Any]] = Field(None, description="Business insurance information")

    # Marketplace preferences
    auto_accept_bookings: Optional[bool] = Field(None, description="Whether to automatically accept bookings")
    instant_booking_enabled: Optional[bool] = Field(None, description="Whether instant booking is enabled")

    # Additional metadata
    additional_info: Optional[Dict[str, Any]] = Field(None, description="Additional business information")


class VendorProfileCreate(VendorProfileBase):
    """Schema for creating vendor profile."""
    pass


class VendorProfileUpdate(VendorProfileBase):
    """Schema for updating vendor profile."""
    pass


class VendorProfileResponse(VendorProfileBase):
    """Schema for vendor profile response."""

    id: int = Field(..., description="Profile ID")
    vendor_id: int = Field(..., description="Associated vendor ID")
    created_at: datetime = Field(..., description="Profile creation timestamp")
    updated_at: datetime = Field(..., description="Profile last update timestamp")

    class Config:
        from_attributes = True


class VendorDocumentBase(BaseModel):
    """Base vendor document schema."""

    document_type: DocumentType = Field(..., description="Type of document")
    document_name: str = Field(..., min_length=1, max_length=255, description="Original document filename")
    document_number: Optional[str] = Field(None, max_length=100, description="Document number or identifier")
    issuing_authority: Optional[str] = Field(None, max_length=255, description="Authority that issued the document")
    expiry_date: Optional[datetime] = Field(None, description="Document expiry date (if applicable)")
    is_primary: bool = Field(default=False, description="Whether this is the primary document of its type")


class VendorDocumentCreate(VendorDocumentBase):
    """Schema for creating vendor document."""

    file_size: int = Field(..., gt=0, description="Document file size in bytes")
    file_type: str = Field(..., max_length=50, description="Document MIME type")
    verification_data: Optional[Dict[str, Any]] = Field(None, description="Additional verification data")


class VendorDocumentUpdate(BaseModel):
    """Schema for updating vendor document."""

    document_number: Optional[str] = Field(None, max_length=100)
    issuing_authority: Optional[str] = Field(None, max_length=255)
    expiry_date: Optional[datetime] = Field(None)
    is_primary: Optional[bool] = Field(None)
    verification_data: Optional[Dict[str, Any]] = Field(None)


class VendorDocumentResponse(VendorDocumentBase):
    """Schema for vendor document response."""

    id: int = Field(..., description="Document ID")
    vendor_id: int = Field(..., description="Associated vendor ID")
    document_url: str = Field(..., description="Document file URL")
    file_size: int = Field(..., description="Document file size in bytes")
    file_type: str = Field(..., description="Document MIME type")
    status: DocumentStatus = Field(..., description="Document verification status")
    reviewed_at: Optional[datetime] = Field(None, description="Timestamp when document was reviewed")
    reviewed_by: Optional[int] = Field(None, description="ID of admin who reviewed the document")
    review_notes: Optional[str] = Field(None, description="Review notes and feedback")
    verification_data: Optional[Dict[str, Any]] = Field(None, description="Additional verification data")
    created_at: datetime = Field(..., description="Document upload timestamp")
    updated_at: datetime = Field(..., description="Document last update timestamp")

    # Computed properties
    is_expired: bool = Field(..., description="Whether document is expired")
    is_approved: bool = Field(..., description="Whether document is approved")
    days_until_expiry: Optional[int] = Field(None, description="Days until document expires")

    class Config:
        from_attributes = True


class VendorResponse(VendorBase):
    """Schema for vendor response data."""

    id: int = Field(..., description="Vendor ID")
    user_id: int = Field(..., description="Associated user account ID")
    verification_status: VerificationStatus = Field(..., description="Current verification status")
    marketplace_status: MarketplaceStatus = Field(..., description="Current marketplace status")
    onboarding_completed: bool = Field(..., description="Whether onboarding process is completed")
    onboarding_step: int = Field(..., description="Current step in onboarding process")

    # Performance metrics
    commission_rate: Decimal = Field(..., description="Platform commission rate")
    total_earnings: Decimal = Field(..., description="Total earnings from marketplace")
    total_bookings: int = Field(..., description="Total number of bookings received")
    average_rating: Decimal = Field(..., description="Average customer rating")
    total_reviews: int = Field(..., description="Total number of customer reviews")
    response_rate: Decimal = Field(..., description="Response rate percentage")
    response_time_hours: Decimal = Field(..., description="Average response time in hours")

    # SEO and optimization
    seo_score: Decimal = Field(..., description="SEO optimization score")
    marketplace_ranking: int = Field(..., description="Current marketplace ranking position")
    listing_quality_score: Decimal = Field(..., description="Listing quality score")

    # Verification and compliance
    verified_at: Optional[datetime] = Field(None, description="Timestamp when vendor was verified")
    verification_notes: Optional[str] = Field(None, description="Notes from verification process")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity timestamp")

    # Timestamps
    created_at: datetime = Field(..., description="Vendor registration timestamp")
    updated_at: datetime = Field(..., description="Vendor last update timestamp")

    # Computed properties
    is_verified: bool = Field(..., description="Whether vendor is verified")
    is_active: bool = Field(..., description="Whether vendor is active in marketplace")
    completion_percentage: float = Field(..., description="Profile completion percentage")

    # Related data
    profile: Optional[VendorProfileResponse] = Field(None, description="Vendor profile information")
    documents: Optional[List[VendorDocumentResponse]] = Field(None, description="Verification documents")

    class Config:
        from_attributes = True


# Vendor Registration Schemas
class VendorRegistrationRequest(BaseModel):
    """Schema for vendor registration request."""

    business_name: str = Field(..., min_length=2, max_length=255, description="Official business name")
    business_type: VendorType = Field(..., description="Type of business/service provider")
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Official business registration number"
    )
    tax_id: Optional[str] = Field(None, max_length=50, description="Tax identification number")

    # Optional profile data
    description: Optional[str] = Field(None, max_length=1000, description="Business description")
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone")
    contact_email: Optional[EmailStr] = Field(None, description="Primary contact email")
    website_url: Optional[HttpUrl] = Field(None, description="Business website URL")

    # Address information
    business_address: Optional[str] = Field(None, description="Complete business address")
    city: Optional[str] = Field(None, max_length=100, description="Business city")
    state: Optional[str] = Field(None, max_length=100, description="Business state/region")
    country: Optional[str] = Field(None, max_length=100, description="Business country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Business postal/zip code")


class VendorRegistrationResponse(BaseModel):
    """Schema for vendor registration response."""

    vendor: VendorResponse = Field(..., description="Created vendor information")
    onboarding_steps: List[Dict[str, Any]] = Field(..., description="Onboarding steps to complete")
    verification_required: bool = Field(..., description="Whether verification documents are required")
    message: str = Field(..., description="Registration success message")


# Onboarding Management Schemas
class OnboardingStepUpdate(BaseModel):
    """Schema for updating onboarding step."""

    step: int = Field(..., ge=1, le=6, description="Onboarding step number (1-6)")
    completed_data: Optional[Dict[str, Any]] = Field(None, description="Data completed in this step")
    notes: Optional[str] = Field(None, max_length=500, description="Optional notes for this step")


class OnboardingStatusResponse(BaseModel):
    """Schema for onboarding status response."""

    current_step: int = Field(..., description="Current onboarding step")
    total_steps: int = Field(..., description="Total number of onboarding steps")
    completed: bool = Field(..., description="Whether onboarding is completed")
    completion_percentage: float = Field(..., description="Completion percentage (0-100)")
    steps: List[Dict[str, Any]] = Field(..., description="Detailed step information")
    next_step_title: Optional[str] = Field(None, description="Title of next step")
    next_step_description: Optional[str] = Field(None, description="Description of next step")


# Task 3.1.3: Enhanced Vendor Profile Management Schemas

class OperatingHoursDay(BaseModel):
    """Schema for operating hours for a single day."""

    is_open: bool = Field(..., description="Whether business is open on this day")
    open_time: Optional[str] = Field(None, description="Opening time in HH:MM format")
    close_time: Optional[str] = Field(None, description="Closing time in HH:MM format")
    is_24_hours: bool = Field(default=False, description="Whether business operates 24 hours")
    break_start: Optional[str] = Field(None, description="Break start time in HH:MM format")
    break_end: Optional[str] = Field(None, description="Break end time in HH:MM format")

    @field_validator('open_time', 'close_time', 'break_start', 'break_end')
    @classmethod
    def validate_time_format(cls, v):
        """Validate time format HH:MM."""
        if v is not None:
            try:
                time_parts = v.split(':')
                if len(time_parts) != 2:
                    raise ValueError('Time must be in HH:MM format')
                hours, minutes = int(time_parts[0]), int(time_parts[1])
                if not (0 <= hours <= 23) or not (0 <= minutes <= 59):
                    raise ValueError('Invalid time values')
            except (ValueError, IndexError):
                raise ValueError('Time must be in HH:MM format (e.g., 09:00)')
        return v


class OperatingHours(BaseModel):
    """Schema for complete operating hours configuration."""

    monday: Optional[OperatingHoursDay] = Field(default=None)
    tuesday: Optional[OperatingHoursDay] = Field(default=None)
    wednesday: Optional[OperatingHoursDay] = Field(default=None)
    thursday: Optional[OperatingHoursDay] = Field(default=None)
    friday: Optional[OperatingHoursDay] = Field(default=None)
    saturday: Optional[OperatingHoursDay] = Field(default=None)
    sunday: Optional[OperatingHoursDay] = Field(default=None)
    timezone: str = Field(default="UTC", description="Business timezone")
    special_hours: Optional[Dict[str, OperatingHoursDay]] = Field(
        None, description="Special hours for holidays/events (date as key)"
    )


class MediaItem(BaseModel):
    """Schema for media item (image/video)."""

    url: HttpUrl = Field(..., description="Media file URL")
    type: str = Field(..., description="Media type (image/video)")
    title: Optional[str] = Field(None, max_length=255, description="Media title")
    description: Optional[str] = Field(None, max_length=500, description="Media description")
    alt_text: Optional[str] = Field(None, max_length=255, description="Alt text for accessibility")
    order: int = Field(default=0, description="Display order")
    is_featured: bool = Field(default=False, description="Whether this is a featured image")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    dimensions: Optional[Dict[str, int]] = Field(None, description="Image dimensions (width, height)")


class VendorMediaManagement(BaseModel):
    """Schema for vendor media management."""

    logo: Optional[MediaItem] = Field(None, description="Business logo")
    cover_image: Optional[MediaItem] = Field(None, description="Cover image")
    gallery: List[MediaItem] = Field(default_factory=list, description="Gallery images")
    videos: List[MediaItem] = Field(default_factory=list, description="Business videos")
    documents: List[MediaItem] = Field(default_factory=list, description="Public documents (menus, brochures)")


class BusinessVerificationInfo(BaseModel):
    """Schema for business verification information."""

    business_license_number: Optional[str] = Field(None, max_length=100, description="Business license number")
    business_license_expiry: Optional[datetime] = Field(None, description="License expiry date")
    tax_registration_number: Optional[str] = Field(None, max_length=100, description="Tax registration number")
    years_in_business: Optional[int] = Field(None, ge=0, le=100, description="Years in business")
    insurance_provider: Optional[str] = Field(None, max_length=255, description="Insurance provider name")
    insurance_policy_number: Optional[str] = Field(None, max_length=100, description="Insurance policy number")
    insurance_coverage_amount: Optional[Decimal] = Field(None, ge=0, description="Insurance coverage amount")
    insurance_expiry: Optional[datetime] = Field(None, description="Insurance expiry date")
    professional_certifications: List[str] = Field(default_factory=list, description="Professional certifications")
    industry_memberships: List[str] = Field(default_factory=list, description="Industry association memberships")


class ProfileCompletionStatus(BaseModel):
    """Schema for profile completion tracking."""

    overall_percentage: float = Field(..., ge=0, le=100, description="Overall completion percentage")
    basic_info_complete: bool = Field(..., description="Basic business information complete")
    contact_info_complete: bool = Field(..., description="Contact information complete")
    address_complete: bool = Field(..., description="Business address complete")
    operating_hours_complete: bool = Field(..., description="Operating hours configured")
    media_complete: bool = Field(..., description="Media (logo, images) uploaded")
    verification_complete: bool = Field(..., description="Business verification complete")
    services_complete: bool = Field(..., description="Services/offerings configured")

    missing_fields: List[str] = Field(default_factory=list, description="List of missing required fields")
    recommendations: List[str] = Field(default_factory=list, description="Recommendations to improve profile")
    next_steps: List[str] = Field(default_factory=list, description="Suggested next steps")


class VendorProfileManagementRequest(BaseModel):
    """Schema for comprehensive vendor profile management request."""

    # Basic business information
    description: Optional[str] = Field(None, description="Business description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short description")
    tagline: Optional[str] = Field(None, max_length=255, description="Business tagline")

    # Contact information
    contact_phone: Optional[str] = Field(None, max_length=20, description="Primary contact phone")
    contact_email: Optional[EmailStr] = Field(None, description="Primary contact email")
    website_url: Optional[HttpUrl] = Field(None, description="Business website")

    # Address information
    business_address: Optional[str] = Field(None, description="Complete business address")
    city: Optional[str] = Field(None, max_length=100, description="Business city")
    state: Optional[str] = Field(None, max_length=100, description="Business state")
    country: Optional[str] = Field(None, max_length=100, description="Business country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90, description="Latitude")
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180, description="Longitude")

    # Operating details
    operating_hours: Optional[OperatingHours] = Field(None, description="Operating hours configuration")
    languages_spoken: Optional[List[str]] = Field(None, description="Languages spoken")
    specializations: Optional[List[str]] = Field(None, description="Business specializations")
    max_group_size: Optional[int] = Field(None, gt=0, description="Maximum group size")
    min_advance_booking: Optional[int] = Field(None, ge=0, description="Minimum advance booking hours")
    cancellation_policy: Optional[str] = Field(None, description="Cancellation policy")

    # Media management
    media: Optional[VendorMediaManagement] = Field(None, description="Media management")

    # Business verification
    verification_info: Optional[BusinessVerificationInfo] = Field(None, description="Verification information")

    # Marketplace preferences
    auto_accept_bookings: Optional[bool] = Field(None, description="Auto-accept bookings")
    instant_booking_enabled: Optional[bool] = Field(None, description="Enable instant booking")

    # Social media
    social_media_links: Optional[Dict[str, str]] = Field(None, description="Social media links")

    # Additional information
    additional_info: Optional[Dict[str, Any]] = Field(None, description="Additional information")


class VendorProfileManagementResponse(BaseModel):
    """Schema for vendor profile management response."""

    profile: VendorProfileResponse = Field(..., description="Updated profile information")
    completion_status: ProfileCompletionStatus = Field(..., description="Profile completion status")
    verification_status: VerificationStatus = Field(..., description="Current verification status")
    marketplace_eligibility: bool = Field(..., description="Whether eligible for marketplace")
    recommendations: List[str] = Field(default_factory=list, description="Profile improvement recommendations")

    class Config:
        from_attributes = True


class OperatingHoursUpdateRequest(BaseModel):
    """Schema for updating operating hours."""

    operating_hours: OperatingHours = Field(..., description="Complete operating hours configuration")
    timezone: Optional[str] = Field(None, description="Business timezone override")


class MediaUploadRequest(BaseModel):
    """Schema for media upload request."""

    media_type: str = Field(..., description="Type of media (logo, cover, gallery, video)")
    title: Optional[str] = Field(None, max_length=255, description="Media title")
    description: Optional[str] = Field(None, max_length=500, description="Media description")
    alt_text: Optional[str] = Field(None, max_length=255, description="Alt text")
    is_featured: bool = Field(default=False, description="Whether this is featured media")
    order: Optional[int] = Field(None, description="Display order")


class MediaUploadResponse(BaseModel):
    """Schema for media upload response."""

    media_item: MediaItem = Field(..., description="Uploaded media information")
    upload_url: str = Field(..., description="URL for file upload")
    success: bool = Field(..., description="Whether upload was successful")
    message: str = Field(..., description="Upload status message")


# Export all schemas
__all__ = [
    # Base schemas
    "VendorBase",
    "VendorCreate",
    "VendorUpdate",
    "VendorResponse",

    # Profile schemas
    "VendorProfileBase",
    "VendorProfileCreate",
    "VendorProfileUpdate",
    "VendorProfileResponse",

    # Document schemas
    "VendorDocumentBase",
    "VendorDocumentCreate",
    "VendorDocumentUpdate",
    "VendorDocumentResponse",

    # Registration schemas
    "VendorRegistrationRequest",
    "VendorRegistrationResponse",

    # Onboarding schemas
    "OnboardingStepUpdate",
    "OnboardingStatusResponse",

    # Task 3.1.3: Enhanced Profile Management schemas
    "OperatingHoursDay",
    "OperatingHours",
    "MediaItem",
    "VendorMediaManagement",
    "BusinessVerificationInfo",
    "ProfileCompletionStatus",
    "VendorProfileManagementRequest",
    "VendorProfileManagementResponse",
    "OperatingHoursUpdateRequest",
    "MediaUploadRequest",
    "MediaUploadResponse",
]
