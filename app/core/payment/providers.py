"""
Payment provider management and routing for Culture Connect Backend API.

This module provides intelligent payment provider selection and management including:
- Geo-location based payment routing
- Provider switching and fallback logic
- Load balancing and performance optimization
- Provider health monitoring and circuit breaker patterns

Features:
- Automatic provider selection based on user location
- Fallback mechanisms for provider failures
- Performance monitoring and optimization
- Circuit breaker patterns for resilience

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from app.core.payment.config import PaymentProviderType, PaymentProviderSettings
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


class ProviderSwitchingStrategy(str, Enum):
    """Payment provider switching strategies."""
    GEO_LOCATION = "geo_location"
    LOAD_BALANCING = "load_balancing"
    COST_OPTIMIZATION = "cost_optimization"
    PERFORMANCE = "performance"
    FALLBACK = "fallback"


@dataclass
class ProviderHealthStatus:
    """Payment provider health status tracking."""
    provider: PaymentProviderType
    is_healthy: bool
    last_check: datetime
    response_time_ms: float
    error_rate: float
    success_rate: float
    consecutive_failures: int


@dataclass
class PaymentRoutingDecision:
    """Payment routing decision with reasoning."""
    primary_provider: PaymentProviderType
    fallback_providers: List[PaymentProviderType]
    strategy_used: ProviderSwitchingStrategy
    reasoning: str
    confidence_score: float
    estimated_cost: Optional[float] = None
    estimated_processing_time: Optional[int] = None


class PaymentProviderSelector(ABC):
    """Abstract base class for payment provider selection strategies."""
    
    @abstractmethod
    async def select_provider(
        self,
        country_code: str,
        currency: str,
        amount: float,
        payment_method: str,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> PaymentRoutingDecision:
        """Select optimal payment provider based on criteria."""
        pass


class GeoLocationBasedSelector(PaymentProviderSelector):
    """
    Geo-location based payment provider selector.
    
    Routes payments based on user location and regional provider optimization:
    - African markets: Paystack (primary), Stripe (fallback)
    - Diaspora markets: Stripe (primary), Paystack (fallback)
    - Global crypto: Busha (primary), others (fallback)
    """
    
    # TODO-GEO-ROUTING-CONFIG: Configure geo-location routing rules for production
    AFRICAN_COUNTRIES = {
        "NG", "GH", "KE", "ZA", "EG", "MA", "TN", "UG", "TZ", "RW",
        "SN", "CI", "BF", "ML", "NE", "TD", "CM", "CF", "GA", "CG",
        "CD", "AO", "ZM", "ZW", "BW", "NA", "SZ", "LS", "MW", "MZ",
        "MG", "MU", "SC", "KM", "DJ", "SO", "ET", "ER", "SD", "SS"
    }
    
    # TODO-DIASPORA-ROUTING-CONFIG: Configure diaspora market routing for production
    DIASPORA_COUNTRIES = {
        "US", "GB", "CA", "AU", "DE", "FR", "IT", "ES", "NL", "BE",
        "SE", "NO", "DK", "FI", "CH", "AT", "IE", "PT", "GR", "PL",
        "CZ", "HU", "SK", "SI", "HR", "BG", "RO", "LT", "LV", "EE"
    }
    
    def __init__(self):
        """Initialize geo-location based selector."""
        self.provider_settings = PaymentProviderSettings()
        logger.info("Initialized geo-location based payment provider selector")
    
    async def select_provider(
        self,
        country_code: str,
        currency: str,
        amount: float,
        payment_method: str,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> PaymentRoutingDecision:
        """
        Select payment provider based on geo-location.
        
        Args:
            country_code: ISO 3166-1 alpha-2 country code
            currency: Payment currency code
            amount: Payment amount
            payment_method: Payment method type
            user_preferences: Optional user payment preferences
            
        Returns:
            PaymentRoutingDecision: Provider selection with reasoning
        """
        correlation = correlation_id.get()
        logger.info(
            f"Selecting payment provider for country: {country_code}, "
            f"currency: {currency}, amount: {amount}",
            extra={"correlation_id": correlation}
        )
        
        # Cryptocurrency preference check
        if payment_method == "crypto" or currency in ["BTC", "ETH", "USDC", "USDT"]:
            if self.provider_settings.is_provider_enabled(PaymentProviderType.BUSHA):
                return PaymentRoutingDecision(
                    primary_provider=PaymentProviderType.BUSHA,
                    fallback_providers=[PaymentProviderType.STRIPE, PaymentProviderType.PAYSTACK],
                    strategy_used=ProviderSwitchingStrategy.GEO_LOCATION,
                    reasoning="Cryptocurrency payment method selected",
                    confidence_score=0.95
                )
        
        # African markets routing
        if country_code in self.AFRICAN_COUNTRIES:
            if self.provider_settings.is_provider_enabled(PaymentProviderType.PAYSTACK):
                return PaymentRoutingDecision(
                    primary_provider=PaymentProviderType.PAYSTACK,
                    fallback_providers=[PaymentProviderType.STRIPE, PaymentProviderType.BUSHA],
                    strategy_used=ProviderSwitchingStrategy.GEO_LOCATION,
                    reasoning=f"African market detected: {country_code}",
                    confidence_score=0.90
                )
        
        # Diaspora markets routing
        if country_code in self.DIASPORA_COUNTRIES:
            if self.provider_settings.is_provider_enabled(PaymentProviderType.STRIPE):
                return PaymentRoutingDecision(
                    primary_provider=PaymentProviderType.STRIPE,
                    fallback_providers=[PaymentProviderType.PAYSTACK, PaymentProviderType.BUSHA],
                    strategy_used=ProviderSwitchingStrategy.GEO_LOCATION,
                    reasoning=f"Diaspora market detected: {country_code}",
                    confidence_score=0.90
                )
        
        # Default fallback routing
        enabled_providers = []
        if self.provider_settings.is_provider_enabled(PaymentProviderType.STRIPE):
            enabled_providers.append(PaymentProviderType.STRIPE)
        if self.provider_settings.is_provider_enabled(PaymentProviderType.PAYSTACK):
            enabled_providers.append(PaymentProviderType.PAYSTACK)
        if self.provider_settings.is_provider_enabled(PaymentProviderType.BUSHA):
            enabled_providers.append(PaymentProviderType.BUSHA)
        
        if not enabled_providers:
            raise ValueError("No payment providers are enabled")
        
        primary = enabled_providers[0]
        fallbacks = enabled_providers[1:] if len(enabled_providers) > 1 else []
        
        return PaymentRoutingDecision(
            primary_provider=primary,
            fallback_providers=fallbacks,
            strategy_used=ProviderSwitchingStrategy.FALLBACK,
            reasoning=f"Unknown country {country_code}, using default provider",
            confidence_score=0.60
        )


class FallbackProviderManager:
    """
    Fallback provider management with circuit breaker patterns.
    
    Manages provider health monitoring and automatic failover to
    backup providers when primary providers experience issues.
    """
    
    def __init__(self):
        """Initialize fallback provider manager."""
        self.provider_health: Dict[PaymentProviderType, ProviderHealthStatus] = {}
        self.circuit_breaker_threshold = 5  # Consecutive failures before circuit break
        self.health_check_interval = timedelta(minutes=5)
        logger.info("Initialized fallback provider manager")
    
    async def get_healthy_provider(
        self,
        routing_decision: PaymentRoutingDecision
    ) -> PaymentProviderType:
        """
        Get healthy provider from routing decision.
        
        Args:
            routing_decision: Initial provider routing decision
            
        Returns:
            PaymentProviderType: Healthy provider to use
            
        Raises:
            ValueError: If no healthy providers are available
        """
        correlation = correlation_id.get()
        
        # Check primary provider health
        if await self._is_provider_healthy(routing_decision.primary_provider):
            logger.info(
                f"Using primary provider: {routing_decision.primary_provider}",
                extra={"correlation_id": correlation}
            )
            return routing_decision.primary_provider
        
        # Check fallback providers
        for fallback_provider in routing_decision.fallback_providers:
            if await self._is_provider_healthy(fallback_provider):
                logger.warning(
                    f"Primary provider {routing_decision.primary_provider} unhealthy, "
                    f"using fallback: {fallback_provider}",
                    extra={"correlation_id": correlation}
                )
                return fallback_provider
        
        # No healthy providers available
        logger.error(
            "No healthy payment providers available",
            extra={"correlation_id": correlation}
        )
        raise ValueError("No healthy payment providers available")
    
    async def _is_provider_healthy(self, provider: PaymentProviderType) -> bool:
        """
        Check if payment provider is healthy.
        
        Args:
            provider: Payment provider to check
            
        Returns:
            bool: True if provider is healthy
        """
        health_status = self.provider_health.get(provider)
        
        if not health_status:
            # No health data, assume healthy for first use
            return True
        
        # Check circuit breaker
        if health_status.consecutive_failures >= self.circuit_breaker_threshold:
            # Check if enough time has passed to retry
            time_since_check = datetime.utcnow() - health_status.last_check
            if time_since_check < self.health_check_interval:
                return False
        
        return health_status.is_healthy
    
    async def record_provider_result(
        self,
        provider: PaymentProviderType,
        success: bool,
        response_time_ms: float
    ) -> None:
        """
        Record provider operation result for health monitoring.
        
        Args:
            provider: Payment provider
            success: Whether operation was successful
            response_time_ms: Response time in milliseconds
        """
        current_time = datetime.utcnow()
        
        if provider not in self.provider_health:
            self.provider_health[provider] = ProviderHealthStatus(
                provider=provider,
                is_healthy=True,
                last_check=current_time,
                response_time_ms=response_time_ms,
                error_rate=0.0,
                success_rate=100.0,
                consecutive_failures=0
            )
        
        health_status = self.provider_health[provider]
        
        if success:
            health_status.consecutive_failures = 0
            health_status.is_healthy = True
        else:
            health_status.consecutive_failures += 1
            if health_status.consecutive_failures >= self.circuit_breaker_threshold:
                health_status.is_healthy = False
        
        health_status.last_check = current_time
        health_status.response_time_ms = response_time_ms
        
        logger.info(
            f"Recorded provider result: {provider}, success: {success}, "
            f"response_time: {response_time_ms}ms, consecutive_failures: {health_status.consecutive_failures}"
        )


class PaymentProviderManager:
    """
    Comprehensive payment provider management system.
    
    Coordinates payment provider selection, health monitoring, and
    fallback management for optimal payment processing.
    """
    
    def __init__(self):
        """Initialize payment provider manager."""
        self.selector = GeoLocationBasedSelector()
        self.fallback_manager = FallbackProviderManager()
        logger.info("Initialized payment provider manager")
    
    async def get_optimal_provider(
        self,
        country_code: str,
        currency: str,
        amount: float,
        payment_method: str,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Tuple[PaymentProviderType, PaymentRoutingDecision]:
        """
        Get optimal payment provider with fallback support.
        
        Args:
            country_code: ISO 3166-1 alpha-2 country code
            currency: Payment currency code
            amount: Payment amount
            payment_method: Payment method type
            user_preferences: Optional user payment preferences
            
        Returns:
            Tuple[PaymentProviderType, PaymentRoutingDecision]: Selected provider and routing decision
        """
        correlation = correlation_id.get()
        
        try:
            # Get initial routing decision
            routing_decision = await self.selector.select_provider(
                country_code=country_code,
                currency=currency,
                amount=amount,
                payment_method=payment_method,
                user_preferences=user_preferences
            )
            
            # Get healthy provider with fallback support
            selected_provider = await self.fallback_manager.get_healthy_provider(routing_decision)
            
            logger.info(
                f"Selected payment provider: {selected_provider} for {country_code}",
                extra={"correlation_id": correlation}
            )
            
            return selected_provider, routing_decision
            
        except Exception as e:
            logger.error(
                f"Failed to select payment provider: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise
    
    async def record_payment_result(
        self,
        provider: PaymentProviderType,
        success: bool,
        response_time_ms: float
    ) -> None:
        """
        Record payment operation result for provider health monitoring.
        
        Args:
            provider: Payment provider used
            success: Whether payment was successful
            response_time_ms: Response time in milliseconds
        """
        await self.fallback_manager.record_provider_result(
            provider=provider,
            success=success,
            response_time_ms=response_time_ms
        )


def get_payment_provider_manager() -> PaymentProviderManager:
    """
    Get payment provider manager instance.
    
    Returns:
        PaymentProviderManager: Configured payment provider manager
    """
    return PaymentProviderManager()
