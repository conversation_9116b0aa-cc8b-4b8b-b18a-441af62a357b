"""
Comprehensive Error Response System for Culture Connect Backend API.

This module provides structured error handling with:
- Standardized error response schemas
- Validation error formatting and localization
- Error code classification and documentation
- Integration with monitoring and logging systems
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator
from fastapi import HTTPException, status

from app.schemas.base import BaseSchema

# Logger for error handling
logger = logging.getLogger(__name__)


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NOT_FOUND = "not_found"
    CONFLICT = "conflict"
    RATE_LIMIT = "rate_limit"
    PAYMENT = "payment"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL_SERVER = "internal_server"
    BUSINESS_LOGIC = "business_logic"


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCode(str, Enum):
    """Standardized error codes."""
    
    # Validation errors (1000-1999)
    VALIDATION_FAILED = "E1000"
    REQUIRED_FIELD_MISSING = "E1001"
    INVALID_FORMAT = "E1002"
    VALUE_OUT_OF_RANGE = "E1003"
    INVALID_ENUM_VALUE = "E1004"
    DUPLICATE_VALUE = "E1005"
    
    # Authentication errors (2000-2999)
    INVALID_CREDENTIALS = "E2000"
    TOKEN_EXPIRED = "E2001"
    TOKEN_INVALID = "E2002"
    TOKEN_MISSING = "E2003"
    ACCOUNT_LOCKED = "E2004"
    ACCOUNT_DISABLED = "E2005"
    
    # Authorization errors (3000-3999)
    INSUFFICIENT_PERMISSIONS = "E3000"
    RESOURCE_ACCESS_DENIED = "E3001"
    ROLE_REQUIRED = "E3002"
    SUBSCRIPTION_REQUIRED = "E3003"
    
    # Resource errors (4000-4999)
    RESOURCE_NOT_FOUND = "E4000"
    VENDOR_NOT_FOUND = "E4001"
    CAMPAIGN_NOT_FOUND = "E4002"
    USER_NOT_FOUND = "E4003"
    
    # Conflict errors (5000-5999)
    RESOURCE_ALREADY_EXISTS = "E5000"
    VENDOR_ALREADY_REGISTERED = "E5001"
    EMAIL_ALREADY_TAKEN = "E5002"
    CAMPAIGN_ALREADY_ACTIVE = "E5003"
    
    # Rate limiting errors (6000-6999)
    RATE_LIMIT_EXCEEDED = "E6000"
    API_QUOTA_EXCEEDED = "E6001"
    CONCURRENT_REQUESTS_LIMIT = "E6002"
    
    # Payment errors (7000-7999)
    PAYMENT_FAILED = "E7000"
    INSUFFICIENT_FUNDS = "E7001"
    PAYMENT_METHOD_INVALID = "E7002"
    PAYMENT_PROVIDER_ERROR = "E7003"
    CURRENCY_NOT_SUPPORTED = "E7004"
    
    # External service errors (8000-8999)
    EXTERNAL_API_ERROR = "E8000"
    PAYSTACK_ERROR = "E8001"
    STRIPE_ERROR = "E8002"
    EMAIL_SERVICE_ERROR = "E8003"
    AI_SERVICE_ERROR = "E8004"
    
    # Internal server errors (9000-9999)
    INTERNAL_SERVER_ERROR = "E9000"
    DATABASE_ERROR = "E9001"
    CACHE_ERROR = "E9002"
    QUEUE_ERROR = "E9003"
    
    @property
    def category(self) -> ErrorCategory:
        """Get error category based on code."""
        code_num = int(self.value[1:])
        
        if 1000 <= code_num < 2000:
            return ErrorCategory.VALIDATION
        elif 2000 <= code_num < 3000:
            return ErrorCategory.AUTHENTICATION
        elif 3000 <= code_num < 4000:
            return ErrorCategory.AUTHORIZATION
        elif 4000 <= code_num < 5000:
            return ErrorCategory.NOT_FOUND
        elif 5000 <= code_num < 6000:
            return ErrorCategory.CONFLICT
        elif 6000 <= code_num < 7000:
            return ErrorCategory.RATE_LIMIT
        elif 7000 <= code_num < 8000:
            return ErrorCategory.PAYMENT
        elif 8000 <= code_num < 9000:
            return ErrorCategory.EXTERNAL_SERVICE
        else:
            return ErrorCategory.INTERNAL_SERVER
    
    @property
    def http_status(self) -> int:
        """Get appropriate HTTP status code."""
        category = self.category
        
        status_map = {
            ErrorCategory.VALIDATION: status.HTTP_422_UNPROCESSABLE_ENTITY,
            ErrorCategory.AUTHENTICATION: status.HTTP_401_UNAUTHORIZED,
            ErrorCategory.AUTHORIZATION: status.HTTP_403_FORBIDDEN,
            ErrorCategory.NOT_FOUND: status.HTTP_404_NOT_FOUND,
            ErrorCategory.CONFLICT: status.HTTP_409_CONFLICT,
            ErrorCategory.RATE_LIMIT: status.HTTP_429_TOO_MANY_REQUESTS,
            ErrorCategory.PAYMENT: status.HTTP_402_PAYMENT_REQUIRED,
            ErrorCategory.EXTERNAL_SERVICE: status.HTTP_502_BAD_GATEWAY,
            ErrorCategory.BUSINESS_LOGIC: status.HTTP_400_BAD_REQUEST,
            ErrorCategory.INTERNAL_SERVER: status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        
        return status_map.get(category, status.HTTP_500_INTERNAL_SERVER_ERROR)


class ErrorDetail(BaseSchema):
    """Detailed error information."""
    
    field: Optional[str] = Field(
        default=None,
        description="Field name that caused the error"
    )
    message: str = Field(
        ...,
        description="Human-readable error message"
    )
    code: Optional[str] = Field(
        default=None,
        description="Specific error code for this detail"
    )
    value: Optional[Any] = Field(
        default=None,
        description="Invalid value that caused the error"
    )
    
    @field_validator('message')
    @classmethod
    def validate_message(cls, v):
        """Ensure error message is not empty."""
        if not v or not v.strip():
            return "An error occurred"
        return v.strip()


class ErrorResponse(BaseSchema):
    """Standardized error response schema."""
    
    error: ErrorCode = Field(
        ...,
        description="Standardized error code"
    )
    message: str = Field(
        ...,
        description="Human-readable error message"
    )
    details: Optional[List[ErrorDetail]] = Field(
        default=None,
        description="Detailed error information"
    )
    timestamp: datetime = Field(
        default=None,
        description="Error occurrence timestamp"
    )
    request_id: str = Field(
        default=None,
        description="Unique request identifier for tracking"
    )
    path: Optional[str] = Field(
        default=None,
        description="API endpoint path where error occurred"
    )
    method: Optional[str] = Field(
        default=None,
        description="HTTP method used"
    )
    
    # Additional metadata
    category: ErrorCategory = Field(
        ...,
        description="Error category"
    )
    severity: ErrorSeverity = Field(
        default=ErrorSeverity.MEDIUM,
        description="Error severity level"
    )
    retryable: bool = Field(
        default=False,
        description="Whether the operation can be retried"
    )
    
    def __init__(self, **data):
        """Initialize error response with automatic category assignment."""
        if 'error' in data and 'category' not in data:
            data['category'] = data['error'].category
        
        super().__init__(**data)
    
    @field_validator('details')
    @classmethod
    def validate_details(cls, v):
        """Validate error details list."""
        if v is None:
            return []

        # Limit number of details to prevent response bloat
        if len(v) > 50:
            return v[:50]

        return v


class ValidationErrorResponse(ErrorResponse):
    """Specialized error response for validation errors."""
    
    error: ErrorCode = Field(
        default=ErrorCode.VALIDATION_FAILED,
        description="Validation error code"
    )
    category: ErrorCategory = Field(
        default=ErrorCategory.VALIDATION,
        description="Error category"
    )
    
    @classmethod
    def from_pydantic_error(cls, exc: Exception, path: str = None) -> "ValidationErrorResponse":
        """Create validation error response from Pydantic validation error."""
        details = []
        
        if hasattr(exc, 'errors'):
            for error in exc.errors():
                field_path = '.'.join(str(loc) for loc in error.get('loc', []))
                
                detail = ErrorDetail(
                    field=field_path or None,
                    message=error.get('msg', 'Validation error'),
                    code=error.get('type', 'validation_error'),
                    value=error.get('input')
                )
                details.append(detail)
        
        return cls(
            message="Validation failed",
            details=details,
            path=path,
            severity=ErrorSeverity.LOW
        )


class BusinessLogicErrorResponse(ErrorResponse):
    """Error response for business logic violations."""
    
    category: ErrorCategory = Field(
        default=ErrorCategory.BUSINESS_LOGIC,
        description="Error category"
    )
    
    @classmethod
    def insufficient_funds(cls, required_amount: float, available_amount: float) -> "BusinessLogicErrorResponse":
        """Create insufficient funds error."""
        return cls(
            error=ErrorCode.INSUFFICIENT_FUNDS,
            message=f"Insufficient funds. Required: {required_amount}, Available: {available_amount}",
            severity=ErrorSeverity.MEDIUM,
            retryable=True
        )
    
    @classmethod
    def campaign_budget_exceeded(cls, campaign_id: str) -> "BusinessLogicErrorResponse":
        """Create campaign budget exceeded error."""
        return cls(
            error=ErrorCode.VALIDATION_FAILED,
            message=f"Campaign {campaign_id} has exceeded its budget limit",
            severity=ErrorSeverity.HIGH,
            retryable=False
        )


class ErrorResponseFactory:
    """Factory for creating standardized error responses."""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        message: str = None,
        details: List[ErrorDetail] = None,
        path: str = None,
        method: str = None,
        **kwargs
    ) -> ErrorResponse:
        """Create a standardized error response."""
        
        # Use default message if not provided
        if message is None:
            message = ErrorResponseFactory._get_default_message(error_code)
        
        # Determine severity and retryability
        severity = ErrorResponseFactory._get_severity(error_code)
        retryable = ErrorResponseFactory._is_retryable(error_code)
        
        return ErrorResponse(
            error=error_code,
            message=message,
            details=details or [],
            path=path,
            method=method,
            severity=severity,
            retryable=retryable,
            **kwargs
        )
    
    @staticmethod
    def _get_default_message(error_code: ErrorCode) -> str:
        """Get default message for error code."""
        messages = {
            ErrorCode.VALIDATION_FAILED: "Validation failed",
            ErrorCode.INVALID_CREDENTIALS: "Invalid credentials provided",
            ErrorCode.TOKEN_EXPIRED: "Authentication token has expired",
            ErrorCode.INSUFFICIENT_PERMISSIONS: "Insufficient permissions",
            ErrorCode.RESOURCE_NOT_FOUND: "Resource not found",
            ErrorCode.RATE_LIMIT_EXCEEDED: "Rate limit exceeded",
            ErrorCode.PAYMENT_FAILED: "Payment processing failed",
            ErrorCode.INTERNAL_SERVER_ERROR: "Internal server error occurred",
        }
        
        return messages.get(error_code, "An error occurred")
    
    @staticmethod
    def _get_severity(error_code: ErrorCode) -> ErrorSeverity:
        """Determine error severity based on code."""
        high_severity_codes = [
            ErrorCode.INTERNAL_SERVER_ERROR,
            ErrorCode.DATABASE_ERROR,
            ErrorCode.PAYMENT_FAILED
        ]
        
        low_severity_codes = [
            ErrorCode.VALIDATION_FAILED,
            ErrorCode.INVALID_FORMAT,
            ErrorCode.RESOURCE_NOT_FOUND
        ]
        
        if error_code in high_severity_codes:
            return ErrorSeverity.HIGH
        elif error_code in low_severity_codes:
            return ErrorSeverity.LOW
        else:
            return ErrorSeverity.MEDIUM
    
    @staticmethod
    def _is_retryable(error_code: ErrorCode) -> bool:
        """Determine if error is retryable."""
        retryable_codes = [
            ErrorCode.RATE_LIMIT_EXCEEDED,
            ErrorCode.EXTERNAL_API_ERROR,
            ErrorCode.INTERNAL_SERVER_ERROR,
            ErrorCode.DATABASE_ERROR
        ]
        
        return error_code in retryable_codes


# Custom HTTP exception with structured error response
class StructuredHTTPException(HTTPException):
    """HTTP exception with structured error response."""
    
    def __init__(
        self,
        error_code: ErrorCode,
        message: str = None,
        details: List[ErrorDetail] = None,
        **kwargs
    ):
        self.error_response = ErrorResponseFactory.create_error_response(
            error_code=error_code,
            message=message,
            details=details,
            **kwargs
        )
        
        super().__init__(
            status_code=error_code.http_status,
            detail=self.error_response.dict()
        )


# Export error handling utilities
__all__ = [
    'ErrorCategory',
    'ErrorSeverity',
    'ErrorCode',
    'ErrorDetail',
    'ErrorResponse',
    'ValidationErrorResponse',
    'BusinessLogicErrorResponse',
    'ErrorResponseFactory',
    'StructuredHTTPException',
]
