"""
Booking Communication schemas for Culture Connect Backend API.

This module defines comprehensive booking communication management schemas including:
- BookingMessage schemas: Message creation, response, and threading with validation
- MessageAttachment schemas: File attachment handling with security validation
- MessageTemplate schemas: Template management with variable substitution
- WebSocket Event schemas: Real-time messaging event structure
- Analytics schemas: Message analytics and conversation summaries

Implements Task 4.1.3 Phase 2 requirements for booking communication system with
production-grade Pydantic V2 validation and seamless integration with Phase 1 models.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from pydantic import Field, field_validator, ConfigDict

from app.schemas.base import BaseSchema, TimestampMixin
from app.models.booking_communication import (
    MessageType, MessageStatus, DeliveryMethod, DeliveryStatus, TemplateCategory
)


class BookingCommunicationBaseSchema(BaseSchema):
    """Base schema for booking communication with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-26T10:00:00Z",
                "updated_at": "2025-01-26T10:00:00Z"
            }
        }
    )


# ============================================================================
# Message Management Schemas
# ============================================================================

class BookingMessageBase(BookingCommunicationBaseSchema):
    """Base schema for booking message with common fields."""

    booking_id: int = Field(
        ...,
        gt=0,
        description="Associated booking ID",
        example=123
    )

    message_type: MessageType = Field(
        MessageType.USER_MESSAGE,
        description="Type of message for categorization",
        example=MessageType.USER_MESSAGE
    )

    subject: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="Message subject line",
        example="Booking inquiry about venue availability"
    )

    content: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Message content body",
        example="Hello, I would like to confirm the booking details for next week."
    )

    is_automated: bool = Field(
        False,
        description="Whether message was automatically generated"
    )

    @field_validator('content')
    @classmethod
    def validate_content_security(cls, v):
        """Validate message content for security."""
        if not v:
            raise ValueError("Message content cannot be empty")

        # Basic security check for HTML/script injection
        dangerous_patterns = ['<script', '<iframe', 'javascript:', 'data:text/html']
        content_lower = v.lower()

        for pattern in dangerous_patterns:
            if pattern in content_lower:
                raise ValueError(f"Message content contains potentially dangerous content: {pattern}")

        return v.strip()


class BookingMessageCreate(BookingMessageBase):
    """Schema for creating a new booking message."""

    recipient_id: Optional[int] = Field(
        None,
        gt=0,
        description="User who should receive the message (auto-determined if not specified)",
        example=456
    )

    parent_message_id: Optional[int] = Field(
        None,
        gt=0,
        description="Parent message ID for reply threading",
        example=789
    )

    template_id: Optional[int] = Field(
        None,
        gt=0,
        description="Template ID for automated messages",
        example=101
    )

    template_variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Variables for template substitution",
        example={"customer_name": "John Doe", "booking_date": "2025-02-01"}
    )

    @field_validator('template_variables')
    @classmethod
    def validate_template_variables(cls, v):
        """Validate template variables format."""
        if not v:
            return {}

        # Limit the size of template variables
        if len(str(v)) > 5000:
            raise ValueError("Template variables too large (max 5KB)")

        # Ensure all keys are strings
        for key in v.keys():
            if not isinstance(key, str):
                raise ValueError("Template variable names must be strings")

        return v


class BookingMessageUpdate(BookingCommunicationBaseSchema):
    """Schema for updating a booking message."""

    subject: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="Updated message subject"
    )

    content: Optional[str] = Field(
        None,
        min_length=1,
        max_length=10000,
        description="Updated message content"
    )

    is_read: Optional[bool] = Field(
        None,
        description="Mark message as read/unread"
    )

    @field_validator('content')
    @classmethod
    def validate_content_security(cls, v):
        """Validate message content for security."""
        if v is None:
            return v

        # Apply same security validation as create
        dangerous_patterns = ['<script', '<iframe', 'javascript:', 'data:text/html']
        content_lower = v.lower()

        for pattern in dangerous_patterns:
            if pattern in content_lower:
                raise ValueError(f"Message content contains potentially dangerous content: {pattern}")

        return v.strip()


class BookingMessageResponse(BookingMessageBase, TimestampMixin):
    """Schema for booking message response."""

    id: int = Field(..., description="Message unique identifier")
    sender_id: Optional[int] = Field(None, description="Message sender ID")
    recipient_id: Optional[int] = Field(None, description="Message recipient ID")

    # Threading information
    thread_id: Optional[UUID] = Field(None, description="Thread identifier for conversation grouping")
    parent_message_id: Optional[int] = Field(None, description="Parent message for threading")
    message_order: int = Field(0, description="Message order within thread")

    # Status and delivery tracking
    status: MessageStatus = Field(MessageStatus.SENT, description="Current message status")
    is_read: bool = Field(False, description="Whether message has been read")
    read_at: Optional[datetime] = Field(None, description="Timestamp when message was read")

    # Template information
    template_id: Optional[int] = Field(None, description="Associated template ID")

    # Delivery tracking
    delivery_status: Optional[Dict[str, Any]] = Field(
        None,
        description="Detailed delivery status for multiple channels"
    )

    # Message metadata
    message_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional message context and metadata"
    )

    # Computed properties
    has_attachments: bool = Field(False, description="Whether message has file attachments")
    attachment_count: int = Field(0, description="Number of file attachments")

    @field_validator('delivery_status')
    @classmethod
    def validate_delivery_status(cls, v):
        """Validate delivery status structure."""
        if v is None:
            return v

        # Ensure it's a dictionary with valid channel names
        if not isinstance(v, dict):
            raise ValueError("Delivery status must be a dictionary")

        valid_channels = {'email', 'push', 'websocket', 'sms'}
        for channel in v.keys():
            if channel not in valid_channels:
                raise ValueError(f"Invalid delivery channel: {channel}")

        return v


class MessageThreadResponse(BookingCommunicationBaseSchema):
    """Schema for message thread response with conversation context."""

    thread_id: UUID = Field(..., description="Thread identifier")
    booking_id: int = Field(..., description="Associated booking ID")

    # Thread metadata
    message_count: int = Field(0, description="Total messages in thread")
    participant_count: int = Field(0, description="Number of participants")
    last_message_at: Optional[datetime] = Field(None, description="Timestamp of last message")

    # Thread participants
    participants: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Thread participants with basic info"
    )

    # Messages in thread (paginated)
    messages: List[BookingMessageResponse] = Field(
        default_factory=list,
        description="Messages in the thread"
    )

    # Thread status
    is_active: bool = Field(True, description="Whether thread is active")
    unread_count: int = Field(0, description="Number of unread messages for current user")

    @field_validator('participants')
    @classmethod
    def validate_participants(cls, v):
        """Validate participants list."""
        if not isinstance(v, list):
            raise ValueError("Participants must be a list")

        # Limit number of participants
        if len(v) > 100:
            raise ValueError("Too many participants (max 100)")

        return v


# ============================================================================
# File Attachment Schemas
# ============================================================================

class MessageAttachmentBase(BookingCommunicationBaseSchema):
    """Base schema for message attachments."""

    filename: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Original filename as uploaded by user",
        example="booking_contract.pdf"
    )

    file_size: int = Field(
        ...,
        gt=0,
        le=52428800,  # 50MB limit
        description="File size in bytes",
        example=1024000
    )

    mime_type: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="MIME type of the file",
        example="application/pdf"
    )

    @field_validator('filename')
    @classmethod
    def validate_filename_security(cls, v):
        """Validate filename for security."""
        if not v:
            raise ValueError("Filename cannot be empty")

        # Check for dangerous file extensions
        dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run'
        }

        filename_lower = v.lower()
        for ext in dangerous_extensions:
            if filename_lower.endswith(ext):
                raise ValueError(f"File type not allowed: {ext}")

        # Check for path traversal attempts
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError("Filename contains invalid characters")

        return v.strip()

    @field_validator('mime_type')
    @classmethod
    def validate_mime_type(cls, v):
        """Validate MIME type."""
        if not v:
            raise ValueError("MIME type cannot be empty")

        # List of allowed MIME types
        allowed_mime_types = {
            # Images
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
            # Documents
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv',
            # Archives
            'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
            # Audio/Video
            'audio/mpeg', 'audio/wav', 'video/mp4', 'video/avi'
        }

        if v not in allowed_mime_types:
            raise ValueError(f"MIME type not allowed: {v}")

        return v


class MessageAttachmentCreate(MessageAttachmentBase):
    """Schema for creating a message attachment."""

    message_id: int = Field(
        ...,
        gt=0,
        description="Associated message ID",
        example=123
    )

    file_content: Optional[str] = Field(
        None,
        description="Base64 encoded file content (for small files)",
        max_length=70000000  # ~50MB base64 encoded
    )

    storage_provider: str = Field(
        "local",
        description="Storage provider preference",
        example="local"
    )

    is_public: bool = Field(
        False,
        description="Whether file should be publicly accessible"
    )

    @field_validator('file_content')
    @classmethod
    def validate_file_content(cls, v):
        """Validate base64 file content."""
        if v is None:
            return v

        # Basic base64 validation
        import base64
        import binascii

        try:
            # Try to decode to validate format
            decoded = base64.b64decode(v, validate=True)
            if len(decoded) > 52428800:  # 50MB limit
                raise ValueError("File content too large (max 50MB)")
        except (binascii.Error, ValueError) as e:
            raise ValueError(f"Invalid base64 content: {str(e)}")

        return v


class MessageAttachmentResponse(MessageAttachmentBase, TimestampMixin):
    """Schema for message attachment response."""

    id: int = Field(..., description="Attachment unique identifier")
    message_id: int = Field(..., description="Associated message ID")

    # Storage information
    file_path: str = Field(..., description="Storage path or key for file retrieval")
    storage_provider: str = Field(..., description="Storage provider used")
    storage_url: Optional[str] = Field(None, description="Public URL for file access")
    thumbnail_url: Optional[str] = Field(None, description="Thumbnail URL for images")

    # Security and validation
    file_hash: Optional[str] = Field(None, description="SHA-256 hash for integrity verification")
    virus_scan_status: str = Field(..., description="Virus scan status")
    virus_scan_result: Optional[Dict[str, Any]] = Field(None, description="Detailed scan results")

    # File metadata
    is_public: bool = Field(..., description="Whether file is publicly accessible")
    upload_metadata: Optional[Dict[str, Any]] = Field(None, description="Upload metadata")

    # Computed properties
    file_size_mb: float = Field(..., description="File size in megabytes")
    is_image: bool = Field(..., description="Whether file is an image")
    is_document: bool = Field(..., description="Whether file is a document")
    is_safe: bool = Field(..., description="Whether file passed security validation")


# ============================================================================
# Template Management Schemas
# ============================================================================

class MessageTemplateBase(BookingCommunicationBaseSchema):
    """Base schema for message templates."""

    template_key: str = Field(
        ...,
        min_length=1,
        max_length=100,
        pattern=r'^[a-z0-9_]+$',
        description="Unique template identifier key",
        example="booking_confirmed"
    )

    name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="Human-readable template name",
        example="Booking Confirmation"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Template description and usage notes",
        example="Template for booking confirmation messages"
    )

    category: TemplateCategory = Field(
        ...,
        description="Template category for organization",
        example=TemplateCategory.BOOKING_LIFECYCLE
    )

    subject_template: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Subject line template with variable placeholders",
        example="Your booking #{booking_reference} has been confirmed"
    )

    content_template: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Message content template with variable placeholders",
        example="Dear {customer_name}, your booking for {service_name} on {booking_date} has been confirmed."
    )

    @field_validator('template_key')
    @classmethod
    def validate_template_key(cls, v):
        """Validate template key format."""
        if not v:
            raise ValueError("Template key cannot be empty")

        # Ensure lowercase with underscores only
        if not v.islower() or not all(c.isalnum() or c == '_' for c in v):
            raise ValueError("Template key must be lowercase alphanumeric with underscores only")

        return v


class MessageTemplateCreate(MessageTemplateBase):
    """Schema for creating a message template."""

    template_variables: Optional[Dict[str, str]] = Field(
        default_factory=dict,
        description="Available variables for template substitution",
        example={"customer_name": "string", "booking_date": "datetime", "service_name": "string"}
    )

    trigger_events: Optional[List[str]] = Field(
        default_factory=list,
        description="Events that trigger this template",
        example=["booking_confirmed", "payment_received"]
    )

    conditions: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Conditions for template activation",
        example={"booking_status": "confirmed", "payment_status": "paid"}
    )

    recipient_type: Optional[str] = Field(
        None,
        pattern=r'^(customer|vendor|both)$',
        description="Target recipient type",
        example="customer"
    )

    is_active: bool = Field(
        True,
        description="Whether template is active and available"
    )

    @field_validator('template_variables')
    @classmethod
    def validate_template_variables(cls, v):
        """Validate template variables."""
        if not v:
            return {}

        # Ensure all keys are valid variable names
        for key, value_type in v.items():
            if not key.isidentifier():
                raise ValueError(f"Invalid variable name: {key}")

            valid_types = {'string', 'number', 'boolean', 'datetime', 'decimal'}
            if value_type not in valid_types:
                raise ValueError(f"Invalid variable type: {value_type}")

        return v

    @field_validator('trigger_events')
    @classmethod
    def validate_trigger_events(cls, v):
        """Validate trigger events."""
        if not v:
            return []

        # Limit number of trigger events
        if len(v) > 20:
            raise ValueError("Too many trigger events (max 20)")

        return v


class MessageTemplateUpdate(BookingCommunicationBaseSchema):
    """Schema for updating a message template."""

    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="Updated template name"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Updated template description"
    )

    subject_template: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="Updated subject template"
    )

    content_template: Optional[str] = Field(
        None,
        min_length=1,
        max_length=10000,
        description="Updated content template"
    )

    template_variables: Optional[Dict[str, str]] = Field(
        None,
        description="Updated template variables"
    )

    trigger_events: Optional[List[str]] = Field(
        None,
        description="Updated trigger events"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Updated active status"
    )


class MessageTemplateResponse(MessageTemplateBase, TimestampMixin):
    """Schema for message template response."""

    id: int = Field(..., description="Template unique identifier")

    # Template content and variables
    template_variables: Optional[Dict[str, str]] = Field(None, description="Template variables")
    trigger_events: Optional[List[str]] = Field(None, description="Trigger events")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Activation conditions")

    # Template management
    recipient_type: Optional[str] = Field(None, description="Target recipient type")
    version: int = Field(..., description="Template version number")
    is_active: bool = Field(..., description="Whether template is active")
    is_system_template: bool = Field(..., description="Whether template is system-managed")

    # Template authorship
    created_by: Optional[int] = Field(None, description="User who created the template")

    # Computed properties
    variable_count: int = Field(..., description="Number of template variables")
    trigger_event_count: int = Field(..., description="Number of trigger events")
    usage_count: int = Field(0, description="Number of times template has been used")


# ============================================================================
# WebSocket Event Schemas
# ============================================================================

class MessageEventSchema(BookingCommunicationBaseSchema):
    """Schema for WebSocket message events."""

    event_type: str = Field(
        ...,
        pattern=r'^(message_sent|message_received|message_read|typing_start|typing_stop|user_online|user_offline)$',
        description="Type of message event",
        example="message_sent"
    )

    booking_id: int = Field(
        ...,
        gt=0,
        description="Associated booking ID",
        example=123
    )

    thread_id: Optional[UUID] = Field(
        None,
        description="Thread identifier for conversation grouping"
    )

    user_id: int = Field(
        ...,
        gt=0,
        description="User who triggered the event",
        example=456
    )

    message_id: Optional[int] = Field(
        None,
        gt=0,
        description="Associated message ID (for message events)",
        example=789
    )

    event_data: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional event data",
        example={"message_preview": "Hello, I have a question..."}
    )

    timestamp: datetime = Field(
        ...,
        description="Event timestamp",
        example="2025-01-26T10:00:00Z"
    )

    @field_validator('event_data')
    @classmethod
    def validate_event_data(cls, v):
        """Validate event data size."""
        if not v:
            return {}

        # Limit event data size
        if len(str(v)) > 10000:
            raise ValueError("Event data too large (max 10KB)")

        return v


class DeliveryStatusEventSchema(BookingCommunicationBaseSchema):
    """Schema for delivery status events."""

    message_id: int = Field(
        ...,
        gt=0,
        description="Associated message ID",
        example=123
    )

    delivery_method: DeliveryMethod = Field(
        ...,
        description="Delivery method",
        example=DeliveryMethod.EMAIL
    )

    delivery_status: DeliveryStatus = Field(
        ...,
        description="Current delivery status",
        example=DeliveryStatus.DELIVERED
    )

    delivery_provider: Optional[str] = Field(
        None,
        description="Delivery provider",
        example="smtp"
    )

    attempted_at: datetime = Field(
        ...,
        description="When delivery was attempted"
    )

    delivered_at: Optional[datetime] = Field(
        None,
        description="When delivery was confirmed"
    )

    error_code: Optional[str] = Field(
        None,
        description="Error code for failed deliveries"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message for failed deliveries"
    )

    retry_count: int = Field(
        0,
        ge=0,
        description="Number of retry attempts"
    )

    delivery_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional delivery metadata"
    )


# ============================================================================
# Analytics Schemas
# ============================================================================

class MessageAnalyticsResponse(BookingCommunicationBaseSchema):
    """Schema for message analytics response."""

    booking_id: int = Field(..., description="Associated booking ID")

    # Message statistics
    total_messages: int = Field(0, description="Total number of messages")
    user_messages: int = Field(0, description="Number of user messages")
    automated_messages: int = Field(0, description="Number of automated messages")
    system_messages: int = Field(0, description="Number of system messages")

    # Participant statistics
    total_participants: int = Field(0, description="Total number of participants")
    active_participants: int = Field(0, description="Number of active participants")

    # Thread statistics
    total_threads: int = Field(0, description="Total number of conversation threads")
    active_threads: int = Field(0, description="Number of active threads")

    # Read status statistics
    unread_messages: int = Field(0, description="Number of unread messages")
    read_rate: float = Field(0.0, description="Message read rate percentage")

    # Attachment statistics
    total_attachments: int = Field(0, description="Total number of attachments")
    attachment_size_mb: float = Field(0.0, description="Total attachment size in MB")

    # Delivery statistics
    delivery_success_rate: float = Field(0.0, description="Overall delivery success rate")
    email_delivery_rate: float = Field(0.0, description="Email delivery success rate")
    push_delivery_rate: float = Field(0.0, description="Push notification delivery rate")

    # Response time statistics
    avg_response_time_hours: float = Field(0.0, description="Average response time in hours")
    median_response_time_hours: float = Field(0.0, description="Median response time in hours")

    # Time period
    period_start: datetime = Field(..., description="Analytics period start")
    period_end: datetime = Field(..., description="Analytics period end")

    # Last activity
    last_message_at: Optional[datetime] = Field(None, description="Timestamp of last message")
    last_activity_at: Optional[datetime] = Field(None, description="Timestamp of last activity")


class ConversationSummaryResponse(BookingCommunicationBaseSchema):
    """Schema for conversation summary response."""

    booking_id: int = Field(..., description="Associated booking ID")
    thread_id: UUID = Field(..., description="Thread identifier")

    # Conversation metadata
    conversation_title: str = Field(..., description="Auto-generated conversation title")
    participant_names: List[str] = Field(..., description="Names of conversation participants")

    # Message summary
    message_count: int = Field(0, description="Total messages in conversation")
    first_message_at: Optional[datetime] = Field(None, description="First message timestamp")
    last_message_at: Optional[datetime] = Field(None, description="Last message timestamp")

    # Key topics and themes
    key_topics: List[str] = Field(
        default_factory=list,
        description="Key topics discussed in conversation"
    )

    sentiment_score: float = Field(
        0.0,
        ge=-1.0,
        le=1.0,
        description="Overall conversation sentiment (-1 to 1)"
    )

    # Action items and decisions
    action_items: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Identified action items from conversation"
    )

    decisions_made: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Decisions made during conversation"
    )

    # Urgency and priority
    urgency_level: str = Field(
        "normal",
        pattern=r'^(low|normal|high|urgent)$',
        description="Conversation urgency level"
    )

    requires_followup: bool = Field(False, description="Whether conversation requires follow-up")
    followup_deadline: Optional[datetime] = Field(None, description="Follow-up deadline")

    # Summary text
    summary_text: str = Field(..., description="AI-generated conversation summary")

    # Metadata
    summary_generated_at: datetime = Field(..., description="When summary was generated")
    summary_version: int = Field(1, description="Summary version number")

    @field_validator('key_topics')
    @classmethod
    def validate_key_topics(cls, v):
        """Validate key topics list."""
        if len(v) > 20:
            raise ValueError("Too many key topics (max 20)")
        return v

    @field_validator('action_items')
    @classmethod
    def validate_action_items(cls, v):
        """Validate action items list."""
        if len(v) > 50:
            raise ValueError("Too many action items (max 50)")
        return v


# ============================================================================
# List and Pagination Schemas
# ============================================================================

class BookingMessageListResponse(BookingCommunicationBaseSchema):
    """Schema for paginated booking message list response."""

    messages: List[BookingMessageResponse] = Field(
        default_factory=list,
        description="List of booking messages"
    )

    total: int = Field(0, description="Total number of messages")
    page: int = Field(1, description="Current page number")
    per_page: int = Field(20, description="Items per page")
    pages: int = Field(0, description="Total number of pages")
    has_next: bool = Field(False, description="Whether there are more pages")
    has_prev: bool = Field(False, description="Whether there are previous pages")

    # Additional metadata
    unread_count: int = Field(0, description="Number of unread messages")
    thread_count: int = Field(0, description="Number of conversation threads")


class MessageTemplateListResponse(BookingCommunicationBaseSchema):
    """Schema for paginated message template list response."""

    templates: List[MessageTemplateResponse] = Field(
        default_factory=list,
        description="List of message templates"
    )

    total: int = Field(0, description="Total number of templates")
    page: int = Field(1, description="Current page number")
    per_page: int = Field(20, description="Items per page")
    pages: int = Field(0, description="Total number of pages")
    has_next: bool = Field(False, description="Whether there are more pages")
    has_prev: bool = Field(False, description="Whether there are previous pages")

    # Category breakdown
    category_counts: Dict[str, int] = Field(
        default_factory=dict,
        description="Template count by category"
    )

    active_count: int = Field(0, description="Number of active templates")
    system_template_count: int = Field(0, description="Number of system templates")


# ============================================================================
# Legacy Schema Aliases for Backward Compatibility
# ============================================================================

# Alias for backward compatibility with existing tests
BookingCommunicationCreateSchema = BookingMessageCreate

# Alias for booking modification schema (placeholder)
class BookingModificationCreateSchema(BookingCommunicationBaseSchema):
    """Schema for creating booking modification requests."""

    booking_id: int = Field(..., gt=0, description="Associated booking ID")
    modification_type: str = Field(..., description="Type of modification requested")
    modification_reason: str = Field(..., description="Reason for modification")
    requested_changes: Dict[str, Any] = Field(..., description="Requested changes")

    @field_validator('modification_type')
    @classmethod
    def validate_modification_type(cls, v):
        """Validate modification type."""
        valid_types = ['DATE_CHANGE', 'TIME_CHANGE', 'PARTICIPANT_CHANGE', 'SERVICE_CHANGE', 'CANCELLATION']
        if v not in valid_types:
            raise ValueError(f"Invalid modification type. Must be one of: {valid_types}")
        return v