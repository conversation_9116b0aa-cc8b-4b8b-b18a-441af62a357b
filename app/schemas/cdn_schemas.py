"""
CDN Optimization schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic schemas for CDN optimization and asset delivery:
- CDN configuration schemas for provider settings and optimization policies
- Asset optimization schemas for tracking optimization processes and results
- Asset bundle schemas for bundled asset optimization and delivery
- CDN metrics schemas for performance monitoring and analytics
- Asset delivery schemas for tracking delivery requests and performance

Implements Phase 7.3.4 requirements with Pydantic V2 syntax, comprehensive validation,
and seamless integration with existing Phase 7.3.2 caching infrastructure.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator, field_validator
from pydantic.types import PositiveInt, PositiveFloat

from app.models.cdn_models import (
    AssetType, OptimizationType, DeliveryStatus, CDNProvider
)


# Base schemas
class CDNBaseSchema(BaseModel):
    """Base schema for CDN-related models."""
    model_config = ConfigDict(from_attributes=True)


# CDN Configuration schemas
class CDNConfigurationCreate(CDNBaseSchema):
    """Schema for creating CDN configuration."""
    name: str = Field(..., min_length=1, max_length=100, description="Configuration name")
    provider: CDNProvider = Field(..., description="CDN provider")
    base_url: str = Field(..., min_length=1, max_length=255, description="CDN base URL")
    api_key: Optional[str] = Field(None, description="CDN provider API key")
    
    # Configuration settings
    cache_ttl_seconds: PositiveInt = Field(86400, description="Cache TTL in seconds")
    compression_enabled: bool = Field(True, description="Enable compression")
    minification_enabled: bool = Field(True, description="Enable minification")
    bundling_enabled: bool = Field(True, description="Enable bundling")
    image_optimization_enabled: bool = Field(True, description="Enable image optimization")
    
    # Performance settings
    max_file_size_mb: PositiveFloat = Field(10.0, description="Maximum file size in MB")
    optimization_quality: int = Field(85, ge=1, le=100, description="Optimization quality (1-100)")
    supported_formats: List[str] = Field(default_factory=list, description="Supported file formats")
    cache_headers: Dict[str, str] = Field(default_factory=dict, description="Custom cache headers")
    
    # Geographic settings
    edge_locations: List[str] = Field(default_factory=list, description="Edge location codes")
    geo_restrictions: Dict[str, Any] = Field(default_factory=dict, description="Geographic restrictions")
    
    # Metadata
    is_default: bool = Field(False, description="Is default configuration")

    @field_validator('base_url')
    @classmethod
    def validate_base_url(cls, v: str) -> str:
        """Validate base URL format."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Base URL must start with http:// or https://')
        return v

    @field_validator('supported_formats')
    @classmethod
    def validate_supported_formats(cls, v: List[str]) -> List[str]:
        """Validate supported formats."""
        valid_formats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'css', 'js', 'html', 'json', 'svg', 'ico']
        for format_type in v:
            if format_type.lower() not in valid_formats:
                raise ValueError(f'Unsupported format: {format_type}')
        return [f.lower() for f in v]


class CDNConfigurationUpdate(CDNBaseSchema):
    """Schema for updating CDN configuration."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    base_url: Optional[str] = Field(None, min_length=1, max_length=255)
    api_key: Optional[str] = None
    cache_ttl_seconds: Optional[PositiveInt] = None
    compression_enabled: Optional[bool] = None
    minification_enabled: Optional[bool] = None
    bundling_enabled: Optional[bool] = None
    image_optimization_enabled: Optional[bool] = None
    max_file_size_mb: Optional[PositiveFloat] = None
    optimization_quality: Optional[int] = Field(None, ge=1, le=100)
    supported_formats: Optional[List[str]] = None
    cache_headers: Optional[Dict[str, str]] = None
    edge_locations: Optional[List[str]] = None
    geo_restrictions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class CDNConfigurationResponse(CDNBaseSchema):
    """Schema for CDN configuration response."""
    id: UUID
    name: str
    provider: CDNProvider
    base_url: str
    cache_ttl_seconds: int
    compression_enabled: bool
    minification_enabled: bool
    bundling_enabled: bool
    image_optimization_enabled: bool
    max_file_size_mb: Decimal
    optimization_quality: int
    supported_formats: List[str]
    cache_headers: Dict[str, str]
    edge_locations: List[str]
    geo_restrictions: Dict[str, Any]
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: Optional[datetime]


# Asset Optimization schemas
class AssetOptimizationCreate(CDNBaseSchema):
    """Schema for creating asset optimization."""
    cdn_configuration_id: UUID = Field(..., description="CDN configuration ID")
    original_path: str = Field(..., min_length=1, max_length=500, description="Original asset path")
    asset_type: AssetType = Field(..., description="Asset type")
    optimization_type: OptimizationType = Field(..., description="Optimization type")

    @field_validator('original_path')
    @classmethod
    def validate_original_path(cls, v: str) -> str:
        """Validate original path format."""
        if not v.strip():
            raise ValueError('Original path cannot be empty')
        return v.strip()


class AssetOptimizationUpdate(CDNBaseSchema):
    """Schema for updating asset optimization."""
    optimized_path: Optional[str] = Field(None, max_length=500)
    optimized_size_bytes: Optional[int] = Field(None, ge=0)
    compression_ratio: Optional[Decimal] = Field(None, ge=0)
    optimization_time_ms: Optional[int] = Field(None, ge=0)
    cdn_url: Optional[str] = Field(None, max_length=500)
    cache_headers: Optional[Dict[str, str]] = None
    delivery_status: Optional[DeliveryStatus] = None
    first_byte_time_ms: Optional[int] = Field(None, ge=0)
    total_download_time_ms: Optional[int] = Field(None, ge=0)
    error_message: Optional[str] = None
    expires_at: Optional[datetime] = None


class AssetOptimizationResponse(CDNBaseSchema):
    """Schema for asset optimization response."""
    id: UUID
    cdn_configuration_id: UUID
    original_path: str
    optimized_path: str
    asset_type: AssetType
    optimization_type: OptimizationType
    original_size_bytes: int
    optimized_size_bytes: int
    compression_ratio: Decimal
    optimization_time_ms: int
    cdn_url: Optional[str]
    cache_headers: Dict[str, str]
    delivery_status: DeliveryStatus
    first_byte_time_ms: Optional[int]
    total_download_time_ms: Optional[int]
    cache_hit_count: int
    cache_miss_count: int
    error_message: Optional[str]
    retry_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    expires_at: Optional[datetime]


# Asset Bundle schemas
class AssetBundleCreate(CDNBaseSchema):
    """Schema for creating asset bundle."""
    cdn_configuration_id: UUID = Field(..., description="CDN configuration ID")
    bundle_name: str = Field(..., min_length=1, max_length=100, description="Bundle name")
    bundle_type: str = Field(..., description="Bundle type (css, js, mixed)")
    asset_paths: List[str] = Field(..., min_items=1, description="List of asset paths to bundle")
    minification_enabled: bool = Field(True, description="Enable minification")
    source_map_enabled: bool = Field(False, description="Enable source maps")

    @field_validator('bundle_type')
    @classmethod
    def validate_bundle_type(cls, v: str) -> str:
        """Validate bundle type."""
        valid_types = ['css', 'js', 'mixed']
        if v.lower() not in valid_types:
            raise ValueError(f'Bundle type must be one of: {valid_types}')
        return v.lower()

    @field_validator('asset_paths')
    @classmethod
    def validate_asset_paths(cls, v: List[str]) -> List[str]:
        """Validate asset paths."""
        if not v:
            raise ValueError('Asset paths cannot be empty')
        for path in v:
            if not path.strip():
                raise ValueError('Asset path cannot be empty')
        return [path.strip() for path in v]


class AssetBundleUpdate(CDNBaseSchema):
    """Schema for updating asset bundle."""
    bundled_path: Optional[str] = Field(None, max_length=500)
    bundled_size_bytes: Optional[int] = Field(None, ge=0)
    compression_ratio: Optional[Decimal] = Field(None, ge=0)
    bundling_time_ms: Optional[int] = Field(None, ge=0)
    cdn_url: Optional[str] = Field(None, max_length=500)
    cache_headers: Optional[Dict[str, str]] = None
    delivery_status: Optional[DeliveryStatus] = None
    first_byte_time_ms: Optional[int] = Field(None, ge=0)
    total_download_time_ms: Optional[int] = Field(None, ge=0)
    bundle_version: Optional[str] = Field(None, max_length=50)
    expires_at: Optional[datetime] = None


class AssetBundleResponse(CDNBaseSchema):
    """Schema for asset bundle response."""
    id: UUID
    cdn_configuration_id: UUID
    bundle_name: str
    bundle_type: str
    asset_paths: List[str]
    bundled_path: str
    total_original_size_bytes: int
    bundled_size_bytes: int
    compression_ratio: Decimal
    bundling_time_ms: int
    cdn_url: Optional[str]
    cache_headers: Dict[str, str]
    delivery_status: DeliveryStatus
    first_byte_time_ms: Optional[int]
    total_download_time_ms: Optional[int]
    cache_hit_count: int
    cache_miss_count: int
    minification_enabled: bool
    source_map_enabled: bool
    bundle_version: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    expires_at: Optional[datetime]


# CDN Metrics schemas
class CDNMetricsCreate(CDNBaseSchema):
    """Schema for creating CDN metrics."""
    cdn_configuration_id: UUID = Field(..., description="CDN configuration ID")
    period_start: datetime = Field(..., description="Metrics period start")
    period_end: datetime = Field(..., description="Metrics period end")
    total_requests: int = Field(0, ge=0, description="Total requests")
    cache_hits: int = Field(0, ge=0, description="Cache hits")
    cache_misses: int = Field(0, ge=0, description="Cache misses")
    avg_response_time_ms: Decimal = Field(Decimal("0.0"), ge=0, description="Average response time")
    avg_first_byte_time_ms: Decimal = Field(Decimal("0.0"), ge=0, description="Average first byte time")
    total_bytes_served: int = Field(0, ge=0, description="Total bytes served")
    total_bytes_saved: int = Field(0, ge=0, description="Total bytes saved")
    error_count: int = Field(0, ge=0, description="Error count")

    @field_validator('period_end')
    @classmethod
    def validate_period_end(cls, v: datetime, info) -> datetime:
        """Validate period end is after period start."""
        if 'period_start' in info.data and v <= info.data['period_start']:
            raise ValueError('Period end must be after period start')
        return v


class CDNMetricsResponse(CDNBaseSchema):
    """Schema for CDN metrics response."""
    id: UUID
    cdn_configuration_id: UUID
    period_start: datetime
    period_end: datetime
    total_requests: int
    cache_hits: int
    cache_misses: int
    cache_hit_rate: Decimal
    avg_response_time_ms: Decimal
    avg_first_byte_time_ms: Decimal
    avg_download_time_ms: Decimal
    total_bytes_served: int
    total_bytes_saved: int
    bandwidth_savings_ratio: Decimal
    error_count: int
    error_rate: Decimal
    top_regions: List[Dict[str, Any]]
    edge_performance: Dict[str, Any]
    recorded_at: datetime


# Asset Delivery schemas
class AssetDeliveryCreate(CDNBaseSchema):
    """Schema for creating asset delivery record."""
    asset_optimization_id: UUID = Field(..., description="Asset optimization ID")
    request_ip: Optional[str] = Field(None, max_length=45, description="Request IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    referer: Optional[str] = Field(None, max_length=500, description="Referer URL")
    country_code: Optional[str] = Field(None, max_length=2, description="Country code")
    region_code: Optional[str] = Field(None, max_length=10, description="Region code")
    city: Optional[str] = Field(None, max_length=100, description="City")
    edge_location: Optional[str] = Field(None, max_length=50, description="Edge location")
    response_time_ms: int = Field(..., ge=0, description="Response time in milliseconds")
    first_byte_time_ms: int = Field(..., ge=0, description="First byte time in milliseconds")
    download_time_ms: int = Field(..., ge=0, description="Download time in milliseconds")
    bytes_transferred: int = Field(..., ge=0, description="Bytes transferred")
    cache_status: str = Field(..., description="Cache status (HIT, MISS, etc.)")
    http_status_code: int = Field(..., ge=100, lt=600, description="HTTP status code")


class AssetDeliveryResponse(CDNBaseSchema):
    """Schema for asset delivery response."""
    id: UUID
    asset_optimization_id: UUID
    request_ip: Optional[str]
    user_agent: Optional[str]
    referer: Optional[str]
    country_code: Optional[str]
    region_code: Optional[str]
    city: Optional[str]
    edge_location: Optional[str]
    response_time_ms: int
    first_byte_time_ms: int
    download_time_ms: int
    bytes_transferred: int
    cache_status: str
    cache_age_seconds: Optional[int]
    http_status_code: int
    error_message: Optional[str]
    delivered_at: datetime


# Dashboard and analytics schemas
class CDNDashboardData(CDNBaseSchema):
    """Schema for CDN dashboard data."""
    total_configurations: int
    active_configurations: int
    total_optimizations: int
    successful_optimizations: int
    total_bundles: int
    avg_compression_ratio: Decimal
    avg_optimization_time_ms: Decimal
    cache_hit_rate: Decimal
    bandwidth_savings_gb: Decimal
    top_performing_configs: List[Dict[str, Any]]
    recent_optimizations: List[AssetOptimizationResponse]


class OptimizationRecommendation(CDNBaseSchema):
    """Schema for optimization recommendations."""
    asset_path: str
    current_size_bytes: int
    recommended_optimizations: List[str]
    estimated_savings_bytes: int
    estimated_savings_percentage: Decimal
    priority: str  # high, medium, low
    reasoning: str
