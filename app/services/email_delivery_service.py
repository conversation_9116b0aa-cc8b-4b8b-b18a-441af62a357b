"""
Email Delivery Service for Culture Connect Backend API.

This module provides comprehensive email delivery functionality including:
- SMTP email sending with multiple provider support
- Email delivery tracking and status management
- Template-based email composition and rendering
- Delivery analytics and performance monitoring
- Integration with email queue for batch processing

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
production-grade SMTP configuration, error handling, and delivery tracking.
"""

import logging
import smtplib
import ssl
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.models.email_models import EmailDelivery, EmailDeliveryStatus
from app.repositories.email_repository import EmailDeliveryRepository
from app.schemas.email_schemas import (
    EmailSendRequest, EmailSendResponse, EmailBatchSendRequest, EmailBatchSendResponse,
    EmailDeliveryResponse, EmailDeliveryListResponse, EmailDeliveryStatusUpdate
)
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class EmailDeliveryService(BaseService[EmailDelivery, EmailDeliveryRepository]):
    """
    Email delivery service for sending and tracking emails.

    Provides comprehensive email delivery functionality with SMTP integration,
    delivery tracking, and performance monitoring.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email delivery service."""
        super().__init__(EmailDeliveryRepository, EmailDelivery, db_session=db)
        self._smtp_config = self._get_smtp_config()

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for email delivery creation."""
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for email delivery updates."""
        return data

    def _get_smtp_config(self) -> Dict[str, Any]:
        """Get SMTP configuration from settings."""
        return {
            "host": settings.SMTP_HOST,
            "port": settings.SMTP_PORT,
            "username": settings.SMTP_USERNAME,
            "password": settings.SMTP_PASSWORD,
            "use_tls": settings.SMTP_USE_TLS,
            "use_ssl": getattr(settings, 'SMTP_USE_SSL', False),
            "timeout": getattr(settings, 'SMTP_TIMEOUT', 30),
            "from_email": settings.FROM_EMAIL,
            "from_name": settings.FROM_NAME
        }

    async def send_email(
        self,
        email_request: EmailSendRequest,
        user_id: Optional[int] = None,
        template_id: Optional[UUID] = None
    ) -> EmailSendResponse:
        """
        Send a single email with delivery tracking.

        Args:
            email_request: Email sending request data
            user_id: Optional associated user ID
            template_id: Optional template ID used

        Returns:
            EmailSendResponse: Email sending response with delivery ID

        Raises:
            ValidationError: If email data is invalid
            ServiceError: If email sending fails
        """
        try:
            # Validate email request
            self._validate_email_request(email_request)

            # Create delivery record
            delivery = await self.repository.create_delivery(
                user_id=user_id,
                template_id=template_id,
                recipient_email=email_request.recipient_email,
                subject=email_request.subject,
                status=EmailDeliveryStatus.PENDING,
                correlation_id=correlation_id.get('')
            )

            try:
                # Send email via SMTP
                await self._send_smtp_email(
                    recipient_email=email_request.recipient_email,
                    subject=email_request.subject,
                    body=email_request.body,
                    html_body=email_request.html_body,
                    attachments=email_request.attachments
                )

                # Update delivery status to sent
                await self.repository.update_delivery_status(
                    delivery_id=delivery.id,
                    status=EmailDeliveryStatus.SENT,
                    sent_at=datetime.utcnow()
                )

                logger.info(
                    f"Email sent successfully",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "delivery_id": str(delivery.id),
                        "recipient": email_request.recipient_email,
                        "subject": email_request.subject
                    }
                )

                return EmailSendResponse(
                    delivery_id=delivery.id,
                    status=EmailDeliveryStatus.SENT,
                    message="Email sent successfully"
                )

            except Exception as smtp_error:
                # Update delivery status to failed
                await self.repository.update_delivery_status(
                    delivery_id=delivery.id,
                    status=EmailDeliveryStatus.FAILED,
                    error_message=str(smtp_error)
                )

                logger.error(
                    f"SMTP error sending email: {str(smtp_error)}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "delivery_id": str(delivery.id),
                        "recipient": email_request.recipient_email,
                        "error_type": type(smtp_error).__name__
                    }
                )

                raise ServiceError(f"Failed to send email: {str(smtp_error)}")

        except (ValidationError, ServiceError):
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error in email delivery: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "recipient": email_request.recipient_email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Database error in email delivery: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error in email delivery: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "recipient": email_request.recipient_email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error in email delivery: {str(e)}")

    async def send_batch_emails(
        self,
        batch_request: EmailBatchSendRequest
    ) -> EmailBatchSendResponse:
        """
        Send multiple emails in batch with delivery tracking.

        Args:
            batch_request: Batch email sending request

        Returns:
            EmailBatchSendResponse: Batch sending response with delivery IDs

        Raises:
            ValidationError: If batch data is invalid
        """
        try:
            if not batch_request.emails:
                raise ValidationError("No emails provided in batch request")

            if len(batch_request.emails) > 100:  # Limit batch size
                raise ValidationError("Batch size cannot exceed 100 emails")

            sent_emails = []
            failed_emails = []

            for email_request in batch_request.emails:
                try:
                    response = await self.send_email(email_request)
                    sent_emails.append(response)
                except Exception as e:
                    failed_emails.append({
                        "recipient": email_request.recipient_email,
                        "error": str(e)
                    })

            logger.info(
                f"Batch email sending completed",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "total_emails": len(batch_request.emails),
                    "sent_count": len(sent_emails),
                    "failed_count": len(failed_emails)
                }
            )

            return EmailBatchSendResponse(
                sent_emails=sent_emails,
                failed_emails=failed_emails,
                total_count=len(batch_request.emails),
                sent_count=len(sent_emails),
                failed_count=len(failed_emails)
            )

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                f"Error in batch email sending: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "batch_size": len(batch_request.emails) if batch_request.emails else 0,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Batch email sending failed: {str(e)}")

    async def get_delivery_status(self, delivery_id: UUID) -> EmailDeliveryResponse:
        """
        Get email delivery status and details.

        Args:
            delivery_id: Delivery UUID

        Returns:
            EmailDeliveryResponse: Delivery status and details

        Raises:
            NotFoundError: If delivery not found
        """
        try:
            delivery = await self.repository.get_by_id(delivery_id)
            if not delivery:
                raise NotFoundError(f"Email delivery {delivery_id} not found")

            return EmailDeliveryResponse.model_validate(delivery)

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error retrieving delivery status: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "delivery_id": str(delivery_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve delivery status: {str(e)}")

    async def update_delivery_status(
        self,
        delivery_id: UUID,
        status_update: EmailDeliveryStatusUpdate
    ) -> EmailDeliveryResponse:
        """
        Update email delivery status (for webhook callbacks).

        Args:
            delivery_id: Delivery UUID
            status_update: Status update data

        Returns:
            EmailDeliveryResponse: Updated delivery data

        Raises:
            NotFoundError: If delivery not found
        """
        try:
            delivery = await self.repository.get_by_id(delivery_id)
            if not delivery:
                raise NotFoundError(f"Email delivery {delivery_id} not found")

            # Update delivery status
            updated_delivery = await self.repository.update_delivery_status(
                delivery_id=delivery_id,
                status=status_update.status,
                error_message=status_update.error_message,
                delivered_at=status_update.delivered_at,
                opened_at=status_update.opened_at
            )

            logger.info(
                f"Delivery status updated",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "delivery_id": str(delivery_id),
                    "new_status": status_update.status.value,
                    "recipient": delivery.recipient_email
                }
            )

            return EmailDeliveryResponse.model_validate(updated_delivery)

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error updating delivery status: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "delivery_id": str(delivery_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to update delivery status: {str(e)}")

    async def list_deliveries(
        self,
        user_id: Optional[int] = None,
        status: Optional[EmailDeliveryStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> EmailDeliveryListResponse:
        """
        List email deliveries with filtering.

        Args:
            user_id: Optional user ID filter
            status: Optional status filter
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            EmailDeliveryListResponse: List of deliveries with metadata
        """
        try:
            deliveries, total = await self.repository.list_deliveries(
                user_id=user_id,
                status=status,
                skip=skip,
                limit=limit
            )

            delivery_responses = [
                EmailDeliveryResponse.model_validate(delivery)
                for delivery in deliveries
            ]

            return EmailDeliveryListResponse(
                deliveries=delivery_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(
                f"Error listing deliveries: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "status": status.value if status else None,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to list deliveries: {str(e)}")

    async def _send_smtp_email(
        self,
        recipient_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> None:
        """
        Send email via SMTP.

        Args:
            recipient_email: Recipient email address
            subject: Email subject
            body: Email body (plain text)
            html_body: Optional HTML body
            attachments: Optional list of attachments

        Raises:
            Exception: If SMTP sending fails
        """
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = f"{self._smtp_config['from_name']} <{self._smtp_config['from_email']}>"
        msg['To'] = recipient_email

        # Add text part
        text_part = MIMEText(body, 'plain', 'utf-8')
        msg.attach(text_part)

        # Add HTML part if provided
        if html_body:
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

        # Add attachments if provided
        if attachments:
            for attachment in attachments:
                self._add_attachment(msg, attachment)

        # Send email
        if self._smtp_config['use_ssl']:
            context = ssl.create_default_context()
            with smtplib.SMTP_SSL(
                self._smtp_config['host'],
                self._smtp_config['port'],
                context=context,
                timeout=self._smtp_config['timeout']
            ) as server:
                if self._smtp_config['username'] and self._smtp_config['password']:
                    server.login(self._smtp_config['username'], self._smtp_config['password'])
                server.send_message(msg)
        else:
            with smtplib.SMTP(
                self._smtp_config['host'],
                self._smtp_config['port'],
                timeout=self._smtp_config['timeout']
            ) as server:
                if self._smtp_config['use_tls']:
                    server.starttls()
                if self._smtp_config['username'] and self._smtp_config['password']:
                    server.login(self._smtp_config['username'], self._smtp_config['password'])
                server.send_message(msg)

    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]) -> None:
        """Add attachment to email message."""
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(attachment['content'])
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {attachment["filename"]}'
        )
        msg.attach(part)

    def _validate_email_request(self, email_request: EmailSendRequest) -> None:
        """Validate email request data."""
        if not email_request.recipient_email:
            raise ValidationError("Recipient email is required")
        if not email_request.subject:
            raise ValidationError("Email subject is required")
        if not email_request.body:
            raise ValidationError("Email body is required")
