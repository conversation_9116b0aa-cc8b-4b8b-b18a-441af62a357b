"""
Enhanced Base Schema Framework for Culture Connect Backend API.

This module provides production-grade base schema classes with comprehensive
validation, serialization optimization, and API versioning support.

Features:
- Advanced validation patterns with custom validators
- Serialization optimization for performance
- API versioning support with backward compatibility
- Structured error handling and response formatting
- Integration with repository layer and service architecture
"""

import re
import logging
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, ClassVar, Annotated
from uuid import UUID

from pydantic import (
    BaseModel, Field, field_validator, model_validator,
    EmailStr, ConfigDict
)

# Logger for schema operations
logger = logging.getLogger(__name__)


# Optimized configuration for Pydantic v2
optimized_config = ConfigDict(
    # Performance optimizations
    validate_assignment=True,
    use_enum_values=True,
    populate_by_name=True,

    # Validation settings
    str_strip_whitespace=True,
    str_min_length=0,
    str_max_length=10000,

    # Serialization settings
    arbitrary_types_allowed=False,
    extra='forbid'
)


class BaseSchema(BaseModel):
    """
    Enhanced base schema with comprehensive validation and optimization.

    Provides:
    - Common validation patterns
    - Serialization optimization
    - Error handling utilities
    - Performance monitoring
    """

    model_config = optimized_config

    # Schema metadata (using ClassVar to avoid field validation)
    schema_version: ClassVar[str] = "1.0"

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """
        Enhanced model dump method with optimization and filtering.

        Args:
            **kwargs: Additional arguments for model dump

        Returns:
            Optimized dictionary representation
        """
        # Default optimizations
        kwargs.setdefault('exclude_none', True)
        kwargs.setdefault('by_alias', True)

        # Performance tracking
        start_time = datetime.now(timezone.utc)
        result = super().model_dump(**kwargs)

        # Log slow serialization
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        if duration > 0.1:  # Log if serialization takes more than 100ms
            logger.warning(
                f"Slow schema serialization: {self.__class__.__name__} "
                f"took {duration:.3f}s"
            )

        return result

    def model_dump_json(self, **kwargs) -> str:
        """
        Enhanced JSON serialization with optimization.

        Args:
            **kwargs: Additional arguments for JSON serialization

        Returns:
            Optimized JSON string
        """
        # Set JSON serialization options
        if 'indent' not in kwargs:
            kwargs['indent'] = None  # Compact JSON

        return super().model_dump_json(**kwargs)

    # Backward compatibility methods
    def dict(self, **kwargs) -> Dict[str, Any]:
        """Backward compatibility method."""
        return self.model_dump(**kwargs)

    def json(self, **kwargs) -> str:
        """Backward compatibility method."""
        return self.model_dump_json(**kwargs)


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields with validation."""

    created_at: Optional[datetime] = Field(
        default=None,
        description="Record creation timestamp"
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        description="Record last update timestamp"
    )

    @field_validator('created_at', 'updated_at', mode='before')
    @classmethod
    def validate_timestamps(cls, v):
        """Validate timestamp fields."""
        if v is None:
            return v

        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError('Invalid timestamp format')

        if isinstance(v, datetime):
            # Ensure timezone awareness
            if v.tzinfo is None:
                v = v.replace(tzinfo=timezone.utc)
            return v

        raise ValueError('Timestamp must be datetime or ISO string')


class UUIDMixin(BaseModel):
    """Mixin for UUID fields with validation."""

    id: Optional[UUID] = Field(
        default=None,
        description="Unique identifier"
    )
    uuid: Optional[UUID] = Field(
        default=None,
        description="Alternative UUID field"
    )

    @field_validator('id', 'uuid', mode='before')
    @classmethod
    def validate_uuid_fields(cls, v):
        """Validate UUID fields."""
        if v is None:
            return v

        if isinstance(v, str):
            try:
                return UUID(v)
            except ValueError:
                raise ValueError('Invalid UUID format')

        if isinstance(v, UUID):
            return v

        raise ValueError('UUID must be string or UUID object')


class PaginationMixin(BaseModel):
    """Mixin for pagination parameters with validation."""

    page: int = Field(
        default=1,
        ge=1,
        le=10000,
        description="Page number (1-based)"
    )
    size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Items per page"
    )

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size

    @field_validator('size')
    @classmethod
    def validate_page_size(cls, v):
        """Validate page size limits."""
        if v > 100:
            raise ValueError('Page size cannot exceed 100 items')
        return v


class SearchMixin(BaseModel):
    """Mixin for search parameters with validation."""

    q: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=500,
        description="Search query"
    )
    sort_by: Optional[str] = Field(
        default=None,
        pattern=r'^[a-zA-Z_][a-zA-Z0-9_]*$',
        description="Sort field name"
    )
    sort_order: Optional[str] = Field(
        default="asc",
        pattern=r'^(asc|desc)$',
        description="Sort order (asc/desc)"
    )

    @field_validator('q')
    @classmethod
    def validate_search_query(cls, v):
        """Validate search query."""
        if v is None:
            return v

        # Remove excessive whitespace
        v = re.sub(r'\s+', ' ', v.strip())

        # Basic security check
        if any(char in v for char in ['<', '>', '"', "'"]):
            raise ValueError('Search query contains invalid characters')

        return v


class MetadataMixin(BaseModel):
    """Mixin for metadata fields."""

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    tags: Optional[List[str]] = Field(
        default_factory=list,
        description="Associated tags"
    )

    @field_validator('metadata')
    @classmethod
    def validate_metadata(cls, v):
        """Validate metadata structure."""
        if v is None:
            return {}

        # Limit metadata size
        if len(str(v)) > 10000:
            raise ValueError('Metadata too large (max 10KB)')

        return v

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Validate tags list."""
        if v is None:
            return []

        # Limit number of tags
        if len(v) > 50:
            raise ValueError('Too many tags (max 50)')

        # Validate tag format
        validated_tags = []
        for tag in v:
            if not isinstance(tag, str):
                continue

            tag = tag.strip().lower()
            if len(tag) > 50:
                continue

            if re.match(r'^[a-zA-Z0-9_-]+$', tag):
                validated_tags.append(tag)

        return validated_tags


# Common field types with validation (Pydantic v2 compatible)

PhoneNumber = Annotated[str, Field(pattern=r'^\+?[1-9]\d{1,14}$')]
CountryCode = Annotated[str, Field(pattern=r'^[A-Z]{2}$', min_length=2, max_length=2)]
CurrencyCode = Annotated[str, Field(pattern=r'^[A-Z]{3}$', min_length=3, max_length=3)]
LanguageCode = Annotated[str, Field(pattern=r'^[a-z]{2}(-[A-Z]{2})?$')]

# Numeric constraints
PositiveInt = Annotated[int, Field(gt=0)]
NonNegativeInt = Annotated[int, Field(ge=0)]
PositiveFloat = Annotated[float, Field(gt=0.0)]
NonNegativeFloat = Annotated[float, Field(ge=0.0)]
Percentage = Annotated[float, Field(ge=0.0, le=100.0)]

# String constraints
NonEmptyStr = Annotated[str, Field(min_length=1)]
ShortStr = Annotated[str, Field(max_length=255)]
MediumStr = Annotated[str, Field(max_length=1000)]
LongStr = Annotated[str, Field(max_length=10000)]


class ValidationError(Exception):
    """Custom validation error with structured details."""

    def __init__(self, message: str, field: str = None, code: str = None):
        super().__init__(message)
        self.message = message
        self.field = field
        self.code = code
        self.timestamp = datetime.now(timezone.utc)


# Export all base classes and utilities
__all__ = [
    'BaseSchema',
    'OptimizedConfig',
    'TimestampMixin',
    'UUIDMixin',
    'PaginationMixin',
    'SearchMixin',
    'MetadataMixin',
    'ValidationError',
    'PhoneNumber',
    'CountryCode',
    'CurrencyCode',
    'LanguageCode',
    'PositiveInt',
    'NonNegativeInt',
    'PositiveFloat',
    'NonNegativeFloat',
    'Percentage',
    'NonEmptyStr',
    'ShortStr',
    'MediumStr',
    'LongStr',
]
