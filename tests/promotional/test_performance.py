"""
Performance tests for promotional system.

This module provides comprehensive performance tests for promotional system:
- Database query performance validation (<200ms targets)
- Bulk operations performance (>1000 records/second)
- Concurrent access testing and load handling
- Cache performance and hit rate validation (>90%)
- Index effectiveness and query optimization
- Memory usage and resource consumption monitoring
- API endpoint response time validation
- Database connection pooling under load

Implements Phase 5: Comprehensive Testing Infrastructure with >85% test coverage.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch
from concurrent.futures import ThreadPoolExecutor

from app.repositories.promotional_repositories import (
    CampaignRepository, AdvertisementRepository, CampaignMetricsRepository,
    PromotionalListingRepository, AdSpendRepository
)
from app.services.promotional_services import (
    CampaignService, AdvertisementService, PromotionalAnalyticsService
)
from app.models.promotional import Campaign, Advertisement, CampaignMetrics


@pytest.mark.performance
class TestDatabasePerformance:
    """Test database performance and query optimization."""

    @pytest.mark.asyncio
    async def test_campaign_query_performance(self, mock_async_session, campaign_factory, performance_timer):
        """Test campaign query performance meets <200ms target."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock large dataset
        campaigns = [campaign_factory(id=i) for i in range(100)]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        repository.session.execute = AsyncMock(return_value=mock_result)

        performance_timer.start()
        result = await repository.get_by_vendor_id(1)
        performance_timer.stop()

        assert len(result) == 100
        assert performance_timer.elapsed < 0.2  # <200ms target
        repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_metrics_insert_performance(self, mock_async_session, campaign_metrics_factory, performance_timer):
        """Test bulk metrics insertion performance >1000 records/second."""
        repository = CampaignMetricsRepository(mock_async_session)
        
        # Create 1000 metrics records
        metrics_list = [campaign_metrics_factory(id=i) for i in range(1000)]
        repository.session.add_all = MagicMock()
        repository.session.commit = AsyncMock()

        performance_timer.start()
        result = await repository.bulk_insert(metrics_list)
        performance_timer.stop()

        assert result == 1000
        assert performance_timer.elapsed < 1.0  # Should complete in under 1 second
        repository.session.add_all.assert_called_once_with(metrics_list)

    @pytest.mark.asyncio
    async def test_complex_analytics_query_performance(self, mock_async_session, performance_timer):
        """Test complex analytics query performance."""
        repository = CampaignMetricsRepository(mock_async_session)
        
        # Mock complex aggregation result
        aggregated_data = {
            "total_impressions": 1000000,
            "total_clicks": 50000,
            "total_conversions": 5000,
            "total_spend": Decimal("250000.00"),
            "average_ctr": Decimal("5.00"),
            "average_conversion_rate": Decimal("10.00")
        }
        
        mock_result = MagicMock()
        mock_result.fetchone.return_value = aggregated_data
        repository.session.execute = AsyncMock(return_value=mock_result)

        performance_timer.start()
        result = await repository.get_aggregated_metrics(
            campaign_id=1,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )
        performance_timer.stop()

        assert result == aggregated_data
        assert performance_timer.elapsed < 0.2  # <200ms target for complex queries
        repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_pagination_performance_large_dataset(self, mock_async_session, campaign_factory, performance_timer):
        """Test pagination performance with large datasets."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock paginated result
        campaigns = [campaign_factory(id=i) for i in range(50)]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        repository.session.execute = AsyncMock(return_value=mock_result)

        performance_timer.start()
        # Test pagination at page 1000 (large offset)
        result = await repository.get_paginated_campaigns(page=1000, page_size=50)
        performance_timer.stop()

        assert len(result) == 50
        assert performance_timer.elapsed < 0.2  # Should handle large offsets efficiently
        repository.session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_index_effectiveness(self, mock_async_session, performance_timer):
        """Test database index effectiveness for promotional queries."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock query execution plan
        execution_plan = {
            "query_type": "Index Scan",
            "index_used": "idx_campaigns_vendor_status",
            "rows_examined": 10,
            "execution_time_ms": 5
        }
        
        mock_result = MagicMock()
        mock_result.fetchone.return_value = execution_plan
        repository.session.execute = AsyncMock(return_value=mock_result)

        performance_timer.start()
        result = await repository.explain_query_performance(
            vendor_id=1,
            status="active"
        )
        performance_timer.stop()

        assert result["query_type"] == "Index Scan"
        assert result["execution_time_ms"] < 10  # Should use indexes efficiently
        assert performance_timer.elapsed < 0.05  # Query analysis should be fast


@pytest.mark.performance
class TestConcurrencyPerformance:
    """Test concurrent access and load handling."""

    @pytest.mark.asyncio
    async def test_concurrent_campaign_creation(self, mock_async_session, campaign_factory):
        """Test concurrent campaign creation performance."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock successful creation
        repository.session.add = MagicMock()
        repository.session.commit = AsyncMock()
        repository.session.refresh = AsyncMock()

        async def create_campaign(campaign_id):
            campaign = campaign_factory(id=campaign_id)
            return await repository.create(campaign)

        # Test 50 concurrent campaign creations
        start_time = time.time()
        tasks = [create_campaign(i) for i in range(50)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()

        assert len(results) == 50
        total_time = end_time - start_time
        assert total_time < 2.0  # Should handle 50 concurrent operations in under 2 seconds

    @pytest.mark.asyncio
    async def test_concurrent_metrics_updates(self, mock_async_session, campaign_metrics_factory):
        """Test concurrent metrics updates performance."""
        repository = CampaignMetricsRepository(mock_async_session)
        
        repository.session.execute = AsyncMock()
        repository.session.commit = AsyncMock()

        async def update_metrics(metrics_id):
            metrics = campaign_metrics_factory(id=metrics_id)
            return await repository.update_performance_metrics(metrics_id, {
                "impressions": 1000,
                "clicks": 50,
                "conversions": 5
            })

        # Test 100 concurrent metrics updates
        start_time = time.time()
        tasks = [update_metrics(i) for i in range(100)]
        await asyncio.gather(*tasks)
        end_time = time.time()

        total_time = end_time - start_time
        assert total_time < 3.0  # Should handle 100 concurrent updates efficiently

    @pytest.mark.asyncio
    async def test_read_write_concurrency(self, mock_async_session, campaign_factory):
        """Test concurrent read/write operations."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock read operations
        campaigns = [campaign_factory(id=i) for i in range(10)]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        repository.session.execute = AsyncMock(return_value=mock_result)
        
        # Mock write operations
        repository.session.add = MagicMock()
        repository.session.commit = AsyncMock()

        async def read_campaigns():
            return await repository.get_by_vendor_id(1)

        async def write_campaign():
            campaign = campaign_factory()
            return await repository.create(campaign)

        # Mix of read and write operations
        start_time = time.time()
        read_tasks = [read_campaigns() for _ in range(20)]
        write_tasks = [write_campaign() for _ in range(10)]
        all_tasks = read_tasks + write_tasks
        
        results = await asyncio.gather(*all_tasks)
        end_time = time.time()

        assert len(results) == 30
        total_time = end_time - start_time
        assert total_time < 2.0  # Should handle mixed operations efficiently


@pytest.mark.performance
class TestCachePerformance:
    """Test cache performance and hit rates."""

    @pytest.mark.asyncio
    async def test_campaign_cache_hit_rate(self, mock_async_session, campaign_factory):
        """Test campaign cache hit rate >90% target."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock cache behavior
        cache_hits = 0
        cache_misses = 0
        
        async def get_campaign_with_cache(campaign_id):
            nonlocal cache_hits, cache_misses
            
            # Simulate 95% cache hit rate
            if campaign_id <= 95:  # First 95 requests are cache hits
                cache_hits += 1
                return campaign_factory(id=campaign_id)
            else:  # Last 5 requests are cache misses
                cache_misses += 1
                mock_result = MagicMock()
                mock_result.scalar_one_or_none.return_value = campaign_factory(id=campaign_id)
                repository.session.execute = AsyncMock(return_value=mock_result)
                return await repository.get_by_id(campaign_id)

        # Test 100 campaign retrievals
        tasks = [get_campaign_with_cache(i) for i in range(1, 101)]
        results = await asyncio.gather(*tasks)

        assert len(results) == 100
        cache_hit_rate = cache_hits / (cache_hits + cache_misses)
        assert cache_hit_rate >= 0.90  # >90% cache hit rate target

    @pytest.mark.asyncio
    async def test_metrics_aggregation_cache_performance(self, mock_async_session):
        """Test metrics aggregation cache performance."""
        repository = CampaignMetricsRepository(mock_async_session)
        
        # Mock cached aggregation result
        cached_result = {
            "total_impressions": 100000,
            "total_clicks": 5000,
            "cache_hit": True,
            "response_time_ms": 10
        }
        
        mock_result = MagicMock()
        mock_result.fetchone.return_value = cached_result
        repository.session.execute = AsyncMock(return_value=mock_result)

        start_time = time.time()
        result = await repository.get_cached_aggregated_metrics(
            campaign_id=1,
            start_date=date.today() - timedelta(days=7),
            end_date=date.today()
        )
        end_time = time.time()

        assert result["cache_hit"] is True
        response_time_ms = (end_time - start_time) * 1000
        assert response_time_ms < 50  # Cached queries should be very fast

    @pytest.mark.asyncio
    async def test_cache_invalidation_performance(self, mock_async_session):
        """Test cache invalidation performance."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock cache invalidation
        repository.session.execute = AsyncMock()
        repository.session.commit = AsyncMock()

        start_time = time.time()
        # Simulate cache invalidation for 100 campaigns
        for i in range(100):
            await repository.invalidate_campaign_cache(i)
        end_time = time.time()

        total_time = end_time - start_time
        assert total_time < 1.0  # Cache invalidation should be fast


@pytest.mark.performance
class TestServicePerformance:
    """Test service layer performance."""

    @pytest.mark.asyncio
    async def test_campaign_service_response_time(self, mock_promotional_repository, mock_payment_service, mock_rbac_service, performance_timer):
        """Test campaign service response time <200ms."""
        service = CampaignService(
            repository=mock_promotional_repository,
            payment_service=mock_payment_service,
            rbac_service=mock_rbac_service
        )
        
        # Mock dependencies
        mock_promotional_repository.get_by_vendor_id = AsyncMock(return_value=[])
        mock_rbac_service.check_permission = AsyncMock(return_value=True)

        performance_timer.start()
        result = await service.get_campaigns_by_vendor(vendor_id=1, current_user_id=1)
        performance_timer.stop()

        assert result == []
        assert performance_timer.elapsed < 0.2  # <200ms target

    @pytest.mark.asyncio
    async def test_analytics_service_complex_calculation_performance(self, mock_promotional_repository, performance_timer):
        """Test analytics service complex calculation performance."""
        service = PromotionalAnalyticsService(repository=mock_promotional_repository)
        
        # Mock complex analytics data
        analytics_data = {
            "campaign_performance": {"roas": Decimal("10.00")},
            "attribution_analysis": {"touchpoints": 5},
            "optimization_insights": {"recommendations": 3}
        }
        
        mock_promotional_repository.get_campaign_analytics = AsyncMock(return_value=analytics_data)

        performance_timer.start()
        result = await service.get_campaign_analytics(
            campaign_id=1,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )
        performance_timer.stop()

        assert result == analytics_data
        assert performance_timer.elapsed < 0.2  # Complex analytics should complete quickly

    @pytest.mark.asyncio
    async def test_bulk_advertisement_processing_performance(self, mock_promotional_repository, mock_rbac_service, advertisement_factory, performance_timer):
        """Test bulk advertisement processing performance."""
        service = AdvertisementService(
            repository=mock_promotional_repository,
            rbac_service=mock_rbac_service
        )
        
        # Mock bulk creation
        advertisements = [advertisement_factory(id=i) for i in range(100)]
        mock_promotional_repository.bulk_create = AsyncMock(return_value=advertisements)
        mock_rbac_service.check_permission = AsyncMock(return_value=True)

        performance_timer.start()
        result = await service.bulk_create_advertisements(
            advertisements_data=[{"name": f"Ad {i}"} for i in range(100)],
            current_user_id=1
        )
        performance_timer.stop()

        assert len(result) == 100
        assert performance_timer.elapsed < 1.0  # Bulk operations should be efficient


@pytest.mark.performance
class TestMemoryPerformance:
    """Test memory usage and resource consumption."""

    @pytest.mark.asyncio
    async def test_memory_usage_large_dataset(self, mock_async_session, campaign_factory):
        """Test memory usage with large datasets."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock large dataset (10,000 campaigns)
        campaigns = [campaign_factory(id=i) for i in range(10000)]
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = campaigns
        repository.session.execute = AsyncMock(return_value=mock_result)

        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss

        result = await repository.get_all_campaigns()

        memory_after = process.memory_info().rss
        memory_increase_mb = (memory_after - memory_before) / 1024 / 1024

        assert len(result) == 10000
        assert memory_increase_mb < 100  # Should not use excessive memory

    @pytest.mark.asyncio
    async def test_connection_pool_performance(self, mock_async_session):
        """Test database connection pool performance under load."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock connection pool behavior
        repository.session.execute = AsyncMock()

        async def execute_query(query_id):
            return await repository.get_by_id(query_id)

        # Test 200 concurrent database operations
        start_time = time.time()
        tasks = [execute_query(i) for i in range(200)]
        await asyncio.gather(*tasks)
        end_time = time.time()

        total_time = end_time - start_time
        assert total_time < 5.0  # Connection pool should handle load efficiently

    @pytest.mark.asyncio
    async def test_resource_cleanup_performance(self, mock_async_session):
        """Test resource cleanup performance."""
        repository = CampaignRepository(mock_async_session)
        
        # Mock resource cleanup
        repository.session.close = AsyncMock()

        start_time = time.time()
        # Simulate cleanup of 100 sessions
        for _ in range(100):
            await repository.cleanup_resources()
        end_time = time.time()

        total_time = end_time - start_time
        assert total_time < 1.0  # Resource cleanup should be fast
