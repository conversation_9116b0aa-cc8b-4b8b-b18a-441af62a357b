"""
Enhanced Email Service for Culture Connect Backend API.

This module provides comprehensive email functionality including:
- Template-based email composition and rendering
- Email delivery with tracking and analytics
- User preference management and opt-out compliance
- Queue-based email processing with priority handling
- Integration with authentication workflows
- SMTP configuration and provider management

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
production-grade functionality replacing the basic email service.
"""

import logging
from typing import Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.email_template_service import EmailTemplateService
from app.services.email_delivery_service import EmailDeliveryService
from app.services.email_preference_service import EmailPreferenceService
from app.services.email_queue_service import EmailQueueService
from app.services.email_notification_service import EmailNotificationService
from app.schemas.email_schemas import (
    EmailTemplateCreate, EmailTemplateResponse, EmailSendRequest, EmailSendResponse,
    EmailVerificationRequest, PasswordResetEmailRequest, EmailPreferenceUpdate
)
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


class EmailService:
    """
    Enhanced email service providing comprehensive email functionality.

    Integrates template management, delivery tracking, preference management,
    queue processing, and notification workflows for production-grade email operations.
    """

    def __init__(self, db: AsyncSession):
        """Initialize enhanced email service with all sub-services."""
        self.db = db
        self.template_service = EmailTemplateService(db)
        self.delivery_service = EmailDeliveryService(db)
        self.preference_service = EmailPreferenceService(db)
        self.queue_service = EmailQueueService(db)
        self.notification_service = EmailNotificationService(db)

    # Template Management Methods
    async def create_template(
        self,
        template_data: EmailTemplateCreate,
        created_by: int
    ) -> EmailTemplateResponse:
        """Create a new email template."""
        return await self.template_service.create_template(template_data, created_by)

    async def get_template(self, template_id: UUID) -> EmailTemplateResponse:
        """Get email template by ID."""
        return await self.template_service.get_template(template_id)

    async def render_template(
        self,
        template_id: UUID,
        variables: Dict[str, Any]
    ) -> Dict[str, str]:
        """Render email template with variables."""
        return await self.template_service.render_template(template_id, variables)

    # Email Delivery Methods
    async def send_email(
        self,
        email_request: EmailSendRequest,
        user_id: Optional[int] = None,
        template_id: Optional[UUID] = None
    ) -> EmailSendResponse:
        """Send a single email with delivery tracking."""
        return await self.delivery_service.send_email(email_request, user_id, template_id)

    async def get_delivery_status(self, delivery_id: UUID):
        """Get email delivery status."""
        return await self.delivery_service.get_delivery_status(delivery_id)

    # User Preference Methods
    async def get_user_preferences(self, user_id: int):
        """Get user email preferences."""
        return await self.preference_service.get_user_preferences(user_id)

    async def update_user_preferences(
        self,
        user_id: int,
        preference_update: EmailPreferenceUpdate
    ):
        """Update user email preferences."""
        return await self.preference_service.update_user_preferences(user_id, preference_update)

    async def can_send_email(self, user_id: int, email_category) -> bool:
        """Check if user can receive emails of a specific category."""
        return await self.preference_service.can_send_email(user_id, email_category)

    # Queue Management Methods
    async def enqueue_email(
        self,
        user_id: Optional[int],
        template_id: Optional[UUID],
        recipient_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        priority: int = 3,
        scheduled_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Add email to processing queue."""
        return await self.queue_service.enqueue_email(
            user_id, template_id, recipient_email, subject, body,
            html_body, priority, scheduled_at, metadata
        )

    async def get_next_batch(self, batch_size: int = 10, max_priority: int = 5):
        """Get next batch of emails to process."""
        return await self.queue_service.get_next_batch(batch_size, max_priority)

    # Notification Methods
    async def send_verification_email(
        self,
        request: EmailVerificationRequest
    ):
        """Send email verification notification."""
        return await self.notification_service.send_verification_email(request)

    async def send_password_reset_email(
        self,
        request: PasswordResetEmailRequest
    ):
        """Send password reset notification."""
        return await self.notification_service.send_password_reset_email(request)

    async def send_security_alert(
        self,
        user_id: int,
        email: str,
        user_name: str,
        alert_type: str,
        alert_details: Dict[str, Any]
    ) -> bool:
        """Send security alert notification."""
        return await self.notification_service.send_security_alert(
            user_id, email, user_name, alert_type, alert_details
        )

    async def send_welcome_email(
        self,
        user_id: int,
        email: str,
        user_name: str
    ) -> bool:
        """Send welcome email to new users."""
        return await self.notification_service.send_welcome_email(user_id, email, user_name)

    # Analytics and Monitoring Methods
    async def get_email_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive email analytics.

        Returns:
            Dict[str, Any]: Email analytics data
        """
        try:
            # Get delivery statistics
            delivery_stats = await self.delivery_service.list_deliveries(limit=0)

            # Get queue statistics
            queue_stats = await self.queue_service.get_queue_statistics()

            # Get preference statistics
            preference_stats = await self.preference_service.get_preference_statistics()

            analytics = {
                "delivery_stats": {
                    "total_deliveries": delivery_stats.total,
                },
                "queue_stats": queue_stats,
                "preference_stats": preference_stats,
                "timestamp": datetime.utcnow().isoformat()
            }

            logger.info(
                f"Retrieved email analytics",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "total_deliveries": delivery_stats.total,
                    "total_queued": queue_stats.get("total_queued", 0)
                }
            )

            return analytics

        except Exception as e:
            logger.error(
                f"Error retrieving email analytics: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__
                }
            )
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    # Health Check Methods
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on email service components.

        Returns:
            Dict[str, Any]: Health check results
        """
        try:
            health_status = {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "components": {
                    "template_service": "healthy",
                    "delivery_service": "healthy",
                    "preference_service": "healthy",
                    "queue_service": "healthy",
                    "notification_service": "healthy"
                }
            }

            # Test basic functionality
            try:
                # Test template service
                await self.template_service.list_templates(limit=1)

                # Test queue service
                await self.queue_service.get_queue_statistics()

                # Test preference service
                await self.preference_service.get_preference_statistics()

            except Exception as component_error:
                health_status["status"] = "degraded"
                health_status["error"] = str(component_error)
                logger.warning(
                    f"Email service component health check failed: {str(component_error)}",
                    extra={"correlation_id": correlation_id.get('')}
                )

            return health_status

        except Exception as e:
            logger.error(
                f"Email service health check failed: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__
                }
            )
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
