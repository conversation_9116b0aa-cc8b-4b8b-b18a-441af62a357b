"""
Unit tests for Repository Bulk Operations.

This test suite validates the bulk operations functionality including:
- Bulk create with conflict resolution
- Bulk update with optimization
- Bulk delete with batch processing
- Bulk upsert operations
- Transaction management
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List, Dict, Any

from sqlalchemy.exc import IntegrityError

from app.repositories.bulk_operations import FullRepository, BulkOperationsMixin
from app.repositories.base import RepositoryError
from tests.unit.repositories.test_base_repository import MockModel


@pytest.fixture
def mock_async_session():
    """Create mock async database session."""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.add_all = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.refresh = AsyncMock()
    return session


@pytest.fixture
def full_repository(mock_async_session):
    """Create full repository instance."""
    return FullRepository(MockModel, mock_async_session)


@pytest.mark.asyncio
class TestBulkCreate:
    """Test bulk create operations."""

    async def test_bulk_create_success(self, full_repository, mock_async_session):
        """Test successful bulk create operation."""
        # Test data
        objects = [
            {"name": "item_1", "value": 10},
            {"name": "item_2", "value": 20},
            {"name": "item_3", "value": 30}
        ]
        
        # Mock created objects
        created_objects = [MockModel(id=i, **obj) for i, obj in enumerate(objects, 1)]
        
        # Mock database operations
        mock_async_session.add_all = MagicMock()
        mock_async_session.flush = AsyncMock()
        mock_async_session.refresh = AsyncMock()
        mock_async_session.commit = AsyncMock()
        
        # Mock model instantiation
        with patch.object(MockModel, '__new__', side_effect=created_objects):
            result = await full_repository.bulk_create(objects)
        
        assert len(result) == 3
        assert all(isinstance(obj, MockModel) for obj in result)
        mock_async_session.add_all.assert_called_once()
        mock_async_session.commit.assert_called_once()

    async def test_bulk_create_with_batching(self, full_repository, mock_async_session):
        """Test bulk create with batch processing."""
        # Create more objects than batch size
        objects = [{"name": f"item_{i}", "value": i} for i in range(1, 26)]  # 25 objects
        
        # Mock created objects
        created_objects = [MockModel(id=i, **obj) for i, obj in enumerate(objects, 1)]
        
        # Mock database operations
        mock_async_session.add_all = MagicMock()
        mock_async_session.flush = AsyncMock()
        mock_async_session.refresh = AsyncMock()
        mock_async_session.commit = AsyncMock()
        
        # Mock model instantiation
        with patch.object(MockModel, '__new__', side_effect=created_objects):
            result = await full_repository.bulk_create(objects, batch_size=10)
        
        assert len(result) == 25
        # Should be called 3 times (10 + 10 + 5)
        assert mock_async_session.add_all.call_count == 3

    async def test_bulk_create_on_conflict_ignore(self, full_repository, mock_async_session):
        """Test bulk create with conflict ignore."""
        objects = [{"name": "item_1", "value": 10}]
        
        # Mock execute for INSERT ... ON CONFLICT DO NOTHING
        mock_result = AsyncMock()
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_create(objects, on_conflict="ignore")
        
        # Should return empty list since we're using ON CONFLICT DO NOTHING
        assert result == []
        mock_async_session.execute.assert_called_once()
        mock_async_session.commit.assert_called_once()

    async def test_bulk_create_on_conflict_update(self, full_repository, mock_async_session):
        """Test bulk create with conflict update."""
        objects = [{"name": "item_1", "value": 10}]
        
        # Mock execute for INSERT ... ON CONFLICT DO UPDATE
        mock_result = AsyncMock()
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_create(objects, on_conflict="update")
        
        # Should return empty list since we're using ON CONFLICT DO UPDATE
        assert result == []
        mock_async_session.execute.assert_called_once()
        mock_async_session.commit.assert_called_once()

    async def test_bulk_create_error(self, full_repository, mock_async_session):
        """Test bulk create with database error."""
        objects = [{"name": "item_1", "value": 10}]
        
        # Mock database error
        mock_async_session.commit.side_effect = IntegrityError("", "", "")
        
        with pytest.raises(RepositoryError) as exc_info:
            await full_repository.bulk_create(objects)
        
        assert "Failed to bulk create MockModel records" in str(exc_info.value)
        mock_async_session.rollback.assert_called_once()


@pytest.mark.asyncio
class TestBulkUpdate:
    """Test bulk update operations."""

    async def test_bulk_update_success(self, full_repository, mock_async_session):
        """Test successful bulk update operation."""
        updates = [
            {"id": 1, "name": "updated_item_1", "value": 100},
            {"id": 2, "name": "updated_item_2", "value": 200}
        ]
        
        # Mock execute results
        mock_result1 = AsyncMock()
        mock_result1.rowcount = 1
        mock_result2 = AsyncMock()
        mock_result2.rowcount = 1
        
        mock_async_session.execute.side_effect = [mock_result1, mock_result2]
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_update(updates)
        
        assert result == 2  # Total updated records
        assert mock_async_session.execute.call_count == 2
        mock_async_session.commit.assert_called_once()

    async def test_bulk_update_with_batching(self, full_repository, mock_async_session):
        """Test bulk update with batch processing."""
        # Create updates that will be grouped
        updates = [
            {"id": i, "name": "updated_item", "value": 100}
            for i in range(1, 26)  # 25 updates with same field values
        ]
        
        # Mock execute result
        mock_result = AsyncMock()
        mock_result.rowcount = 10  # Simulate 10 records updated per batch
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_update(updates, batch_size=10)
        
        # Should process in 3 batches (10 + 10 + 5)
        assert mock_async_session.execute.call_count == 3
        assert result == 30  # 3 batches * 10 records each

    async def test_bulk_update_grouping(self, full_repository):
        """Test update grouping by common fields."""
        updates = [
            {"id": 1, "name": "group1", "value": 100},
            {"id": 2, "name": "group1", "value": 100},  # Same fields as above
            {"id": 3, "name": "group2", "value": 200},  # Different fields
        ]
        
        groups = full_repository._group_updates_by_fields(updates, "id")
        
        # Should have 2 groups
        assert len(groups) == 2
        
        # Find the groups
        group1_key = {"name": "group1", "value": 100}
        group2_key = {"name": "group2", "value": 200}
        
        assert group1_key in groups
        assert group2_key in groups
        assert len(groups[group1_key]) == 2  # IDs 1 and 2
        assert len(groups[group2_key]) == 1  # ID 3

    async def test_bulk_update_error(self, full_repository, mock_async_session):
        """Test bulk update with database error."""
        updates = [{"id": 1, "name": "updated"}]
        
        # Mock database error
        mock_async_session.execute.side_effect = IntegrityError("", "", "")
        
        with pytest.raises(RepositoryError) as exc_info:
            await full_repository.bulk_update(updates)
        
        assert "Failed to bulk update MockModel records" in str(exc_info.value)
        mock_async_session.rollback.assert_called_once()


@pytest.mark.asyncio
class TestBulkDelete:
    """Test bulk delete operations."""

    async def test_bulk_delete_success(self, full_repository, mock_async_session):
        """Test successful bulk delete operation."""
        ids = [1, 2, 3, 4, 5]
        
        # Mock execute results for each batch
        mock_result1 = AsyncMock()
        mock_result1.rowcount = 3
        mock_result2 = AsyncMock()
        mock_result2.rowcount = 2
        
        mock_async_session.execute.side_effect = [mock_result1, mock_result2]
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_delete(ids, batch_size=3)
        
        assert result == 5  # Total deleted records
        assert mock_async_session.execute.call_count == 2  # 2 batches
        mock_async_session.commit.assert_called_once()

    async def test_bulk_delete_soft_delete(self, full_repository, mock_async_session):
        """Test bulk soft delete operation."""
        ids = [1, 2, 3]
        
        # Mock model to have deleted_at field
        with patch.object(MockModel, 'deleted_at', create=True):
            mock_result = AsyncMock()
            mock_result.rowcount = 3
            mock_async_session.execute.return_value = mock_result
            mock_async_session.commit = AsyncMock()
            
            result = await full_repository.bulk_delete(ids, soft_delete=True)
            
            assert result == 3
            mock_async_session.execute.assert_called_once()
            mock_async_session.commit.assert_called_once()

    async def test_bulk_delete_custom_id_field(self, full_repository, mock_async_session):
        """Test bulk delete with custom ID field."""
        uuids = ["uuid1", "uuid2", "uuid3"]
        
        mock_result = AsyncMock()
        mock_result.rowcount = 3
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_delete(uuids, id_field="uuid")
        
        assert result == 3
        mock_async_session.execute.assert_called_once()

    async def test_bulk_delete_error(self, full_repository, mock_async_session):
        """Test bulk delete with database error."""
        ids = [1, 2, 3]
        
        # Mock database error
        mock_async_session.execute.side_effect = IntegrityError("", "", "")
        
        with pytest.raises(RepositoryError) as exc_info:
            await full_repository.bulk_delete(ids)
        
        assert "Failed to bulk delete MockModel records" in str(exc_info.value)
        mock_async_session.rollback.assert_called_once()


@pytest.mark.asyncio
class TestBulkUpsert:
    """Test bulk upsert operations."""

    async def test_bulk_upsert_success(self, full_repository, mock_async_session):
        """Test successful bulk upsert operation."""
        objects = [
            {"email": "<EMAIL>", "name": "User 1"},
            {"email": "<EMAIL>", "name": "User 2"}
        ]
        conflict_columns = ["email"]
        
        # Mock execute results for each batch
        mock_result = AsyncMock()
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_upsert(
            objects, 
            conflict_columns=conflict_columns
        )
        
        assert result == 2  # Total processed records
        mock_async_session.execute.assert_called_once()
        mock_async_session.commit.assert_called_once()

    async def test_bulk_upsert_with_update_columns(self, full_repository, mock_async_session):
        """Test bulk upsert with specific update columns."""
        objects = [
            {"email": "<EMAIL>", "name": "User 1", "age": 25},
            {"email": "<EMAIL>", "name": "User 2", "age": 30}
        ]
        conflict_columns = ["email"]
        update_columns = ["name"]  # Only update name, not age
        
        mock_result = AsyncMock()
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_upsert(
            objects,
            conflict_columns=conflict_columns,
            update_columns=update_columns
        )
        
        assert result == 2
        mock_async_session.execute.assert_called_once()

    async def test_bulk_upsert_with_batching(self, full_repository, mock_async_session):
        """Test bulk upsert with batch processing."""
        objects = [
            {"email": f"user{i}@example.com", "name": f"User {i}"}
            for i in range(1, 26)  # 25 objects
        ]
        conflict_columns = ["email"]
        
        mock_result = AsyncMock()
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit = AsyncMock()
        
        result = await full_repository.bulk_upsert(
            objects,
            conflict_columns=conflict_columns,
            batch_size=10
        )
        
        assert result == 25
        # Should process in 3 batches (10 + 10 + 5)
        assert mock_async_session.execute.call_count == 3

    async def test_bulk_upsert_error(self, full_repository, mock_async_session):
        """Test bulk upsert with database error."""
        objects = [{"email": "<EMAIL>", "name": "User"}]
        conflict_columns = ["email"]
        
        # Mock database error
        mock_async_session.execute.side_effect = IntegrityError("", "", "")
        
        with pytest.raises(RepositoryError) as exc_info:
            await full_repository.bulk_upsert(objects, conflict_columns)
        
        assert "Failed to bulk upsert MockModel records" in str(exc_info.value)
        mock_async_session.rollback.assert_called_once()


@pytest.mark.asyncio
class TestBulkOperationsIntegration:
    """Test bulk operations integration scenarios."""

    async def test_mixed_bulk_operations_transaction(self, full_repository, mock_async_session):
        """Test multiple bulk operations in sequence."""
        # Test that all operations use the same session and transaction
        
        # Bulk create
        create_objects = [{"name": "item_1"}]
        mock_async_session.add_all = MagicMock()
        mock_async_session.commit = AsyncMock()
        
        with patch.object(MockModel, '__new__', return_value=MockModel(id=1)):
            await full_repository.bulk_create(create_objects)
        
        # Bulk update
        update_objects = [{"id": 1, "name": "updated_item_1"}]
        mock_result = AsyncMock()
        mock_result.rowcount = 1
        mock_async_session.execute.return_value = mock_result
        
        await full_repository.bulk_update(update_objects)
        
        # Verify session was used consistently
        assert mock_async_session.commit.call_count == 2

    async def test_bulk_operations_performance_tracking(self, full_repository):
        """Test that bulk operations track performance metrics."""
        # Mock successful operations
        full_repository.bulk_create = AsyncMock(return_value=[])
        full_repository.bulk_update = AsyncMock(return_value=5)
        full_repository.bulk_delete = AsyncMock(return_value=3)
        
        await full_repository.bulk_create([])
        await full_repository.bulk_update([])
        await full_repository.bulk_delete([])
        
        # Check that metrics were recorded (would be tracked by the actual implementation)
        # This is more of a structural test to ensure the pattern is followed
        assert hasattr(full_repository, '_query_metrics')

    async def test_bulk_operations_error_consistency(self, full_repository, mock_async_session):
        """Test consistent error handling across bulk operations."""
        # Mock database error for all operations
        mock_async_session.execute.side_effect = IntegrityError("", "", "")
        mock_async_session.commit.side_effect = IntegrityError("", "", "")
        
        # Test that all bulk operations raise RepositoryError
        with pytest.raises(RepositoryError):
            await full_repository.bulk_create([{"name": "test"}])
        
        with pytest.raises(RepositoryError):
            await full_repository.bulk_update([{"id": 1, "name": "test"}])
        
        with pytest.raises(RepositoryError):
            await full_repository.bulk_delete([1])
        
        with pytest.raises(RepositoryError):
            await full_repository.bulk_upsert([{"name": "test"}], ["name"])
        
        # Verify rollback was called for each operation
        assert mock_async_session.rollback.call_count >= 4
