"""
Geolocation Analytics Repository for Culture Connect Backend API.

This module provides comprehensive repository layer for geolocation analytics including:
- GeolocationAnalyticsRepository: Core analytics data aggregation and retrieval
- ProviderPerformanceRepository: Provider-specific performance analytics
- ConversionAnalyticsRepository: Conversion rate tracking and optimization
- RoutingMetricsRepository: Routing decision analytics and effectiveness

Implements Phase 2.2 Analytics Dashboard requirements with production-grade
PostgreSQL optimization, <200ms query performance, and comprehensive analytics
aggregation following established BaseRepository patterns.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from sqlalchemy import func, and_, or_, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository, RepositoryError
from app.models.geolocation_analytics import (
    GeolocationAnalytics, ProviderPerformanceMetrics, GeolocationRoutingMetrics,
    ConversionAnalytics, AnalyticsPeriodType, RoutingDecisionType
)
from app.models.payment import Payment, PaymentStatus
import logging

logger = logging.getLogger(__name__)

# Create exception classes for repository operations
class NotFoundError(RepositoryError):
    """Exception for resource not found errors."""
    pass

class ServiceError(RepositoryError):
    """Exception for service errors."""
    pass


class GeolocationAnalyticsRepository(BaseRepository[GeolocationAnalytics]):
    """
    Repository for geolocation analytics data aggregation and retrieval.

    Provides comprehensive analytics operations with <200ms performance targets,
    advanced aggregation capabilities, and optimization insights.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize geolocation analytics repository."""
        super().__init__(GeolocationAnalytics, db_session)

    async def get_analytics_by_period(
        self,
        start_date: datetime,
        end_date: datetime,
        period_type: AnalyticsPeriodType = AnalyticsPeriodType.DAILY,
        country_codes: Optional[List[str]] = None
    ) -> List[GeolocationAnalytics]:
        """
        Retrieve analytics data for specified time period.

        Args:
            start_date: Period start date
            end_date: Period end date
            period_type: Analytics aggregation period
            country_codes: Optional country code filter

        Returns:
            List[GeolocationAnalytics]: Analytics data for period

        Performance Target: <200ms for complex queries
        """
        try:
            query = self.db_session.query(GeolocationAnalytics).filter(
                and_(
                    GeolocationAnalytics.period_start >= start_date,
                    GeolocationAnalytics.period_end <= end_date,
                    GeolocationAnalytics.period_type == period_type
                )
            )

            if country_codes:
                query = query.filter(GeolocationAnalytics.country_code.in_(country_codes))

            # Order by analysis date for consistent results
            query = query.order_by(desc(GeolocationAnalytics.analysis_date))

            result = await query.all()

            logger.info(
                f"Retrieved {len(result)} analytics records for period {start_date} to {end_date}",
                extra={
                    "period_type": period_type.value,
                    "country_filter": country_codes,
                    "record_count": len(result)
                }
            )

            return result

        except Exception as e:
            logger.error(f"Error retrieving analytics by period: {str(e)}")
            raise ServiceError(f"Failed to retrieve analytics data: {str(e)}")

    async def get_top_performing_countries(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 10,
        metric: str = "conversion_rate"
    ) -> List[Dict[str, Any]]:
        """
        Get top performing countries by specified metric.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            limit: Number of top countries to return
            metric: Performance metric to rank by

        Returns:
            List[Dict[str, Any]]: Top performing countries data

        Performance Target: <150ms for ranking queries
        """
        try:
            # Map metric names to database columns
            metric_mapping = {
                "conversion_rate": GeolocationAnalytics.conversion_rate,
                "total_payments": GeolocationAnalytics.total_payments,
                "total_amount": GeolocationAnalytics.total_amount,
                "cache_hit_rate": GeolocationAnalytics.cache_hit_rate,
                "avg_processing_time_ms": GeolocationAnalytics.avg_processing_time_ms
            }

            if metric not in metric_mapping:
                raise ValueError(f"Invalid metric: {metric}")

            # Aggregate data by country
            query = self.db_session.query(
                GeolocationAnalytics.country_code,
                GeolocationAnalytics.country_name,
                func.avg(metric_mapping[metric]).label('avg_metric'),
                func.sum(GeolocationAnalytics.total_payments).label('total_payments'),
                func.sum(GeolocationAnalytics.successful_payments).label('successful_payments'),
                func.sum(GeolocationAnalytics.total_amount).label('total_amount')
            ).filter(
                and_(
                    GeolocationAnalytics.analysis_date >= start_date,
                    GeolocationAnalytics.analysis_date <= end_date,
                    GeolocationAnalytics.country_code.isnot(None)
                )
            ).group_by(
                GeolocationAnalytics.country_code,
                GeolocationAnalytics.country_name
            ).order_by(
                desc('avg_metric')
            ).limit(limit)

            result = await query.all()

            # Format results
            top_countries = []
            for row in result:
                top_countries.append({
                    "country_code": row.country_code,
                    "country_name": row.country_name,
                    "metric_value": float(row.avg_metric) if row.avg_metric else 0.0,
                    "total_payments": int(row.total_payments) if row.total_payments else 0,
                    "successful_payments": int(row.successful_payments) if row.successful_payments else 0,
                    "total_amount": float(row.total_amount) if row.total_amount else 0.0
                })

            logger.info(
                f"Retrieved top {len(top_countries)} countries by {metric}",
                extra={"metric": metric, "period": f"{start_date} to {end_date}"}
            )

            return top_countries

        except Exception as e:
            logger.error(f"Error retrieving top performing countries: {str(e)}")
            raise ServiceError(f"Failed to retrieve top countries: {str(e)}")

    async def get_provider_distribution(
        self,
        start_date: datetime,
        end_date: datetime,
        country_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get payment provider distribution analytics.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            country_code: Optional country filter

        Returns:
            Dict[str, Any]: Provider distribution data

        Performance Target: <100ms for aggregation queries
        """
        try:
            query = self.db_session.query(
                func.sum(GeolocationAnalytics.paystack_payments).label('paystack_total'),
                func.sum(GeolocationAnalytics.stripe_payments).label('stripe_total'),
                func.sum(GeolocationAnalytics.busha_payments).label('busha_total'),
                func.sum(GeolocationAnalytics.total_payments).label('total_payments'),
                func.avg(GeolocationAnalytics.conversion_rate).label('avg_conversion_rate')
            ).filter(
                and_(
                    GeolocationAnalytics.analysis_date >= start_date,
                    GeolocationAnalytics.analysis_date <= end_date
                )
            )

            if country_code:
                query = query.filter(GeolocationAnalytics.country_code == country_code)

            result = await query.first()

            if not result:
                return {
                    "paystack": {"count": 0, "percentage": 0.0},
                    "stripe": {"count": 0, "percentage": 0.0},
                    "busha": {"count": 0, "percentage": 0.0},
                    "total_payments": 0,
                    "avg_conversion_rate": 0.0
                }

            total_payments = int(result.total_payments) if result.total_payments else 0
            paystack_count = int(result.paystack_total) if result.paystack_total else 0
            stripe_count = int(result.stripe_total) if result.stripe_total else 0
            busha_count = int(result.busha_total) if result.busha_total else 0

            # Calculate percentages
            distribution = {
                "paystack": {
                    "count": paystack_count,
                    "percentage": (paystack_count / total_payments * 100) if total_payments > 0 else 0.0
                },
                "stripe": {
                    "count": stripe_count,
                    "percentage": (stripe_count / total_payments * 100) if total_payments > 0 else 0.0
                },
                "busha": {
                    "count": busha_count,
                    "percentage": (busha_count / total_payments * 100) if total_payments > 0 else 0.0
                },
                "total_payments": total_payments,
                "avg_conversion_rate": float(result.avg_conversion_rate) if result.avg_conversion_rate else 0.0
            }

            logger.info(
                f"Retrieved provider distribution for period {start_date} to {end_date}",
                extra={"country_code": country_code, "total_payments": total_payments}
            )

            return distribution

        except Exception as e:
            logger.error(f"Error retrieving provider distribution: {str(e)}")
            raise ServiceError(f"Failed to retrieve provider distribution: {str(e)}")

    async def get_vpn_impact_analytics(
        self,
        start_date: datetime,
        end_date: datetime,
        country_codes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get VPN/Proxy impact analytics on payment performance.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            country_codes: Optional country code filter

        Returns:
            Dict[str, Any]: VPN impact analytics data

        Performance Target: <150ms for VPN analytics queries
        """
        try:
            query = self.db_session.query(
                func.sum(GeolocationAnalytics.vpn_detected_payments).label('total_vpn_payments'),
                func.sum(GeolocationAnalytics.proxy_detected_payments).label('total_proxy_payments'),
                func.sum(GeolocationAnalytics.total_payments).label('total_payments'),
                func.avg(GeolocationAnalytics.vpn_impact_on_conversion).label('avg_vpn_impact'),
                func.avg(GeolocationAnalytics.conversion_rate).label('overall_conversion_rate')
            ).filter(
                and_(
                    GeolocationAnalytics.analysis_date >= start_date,
                    GeolocationAnalytics.analysis_date <= end_date
                )
            )

            if country_codes:
                query = query.filter(GeolocationAnalytics.country_code.in_(country_codes))

            result = await query.first()

            if not result:
                return {
                    "vpn_detection_rate": 0.0,
                    "proxy_detection_rate": 0.0,
                    "vpn_impact_on_conversion": 0.0,
                    "total_payments": 0,
                    "vpn_payments": 0,
                    "proxy_payments": 0
                }

            total_payments = int(result.total_payments) if result.total_payments else 0
            vpn_payments = int(result.total_vpn_payments) if result.total_vpn_payments else 0
            proxy_payments = int(result.total_proxy_payments) if result.total_proxy_payments else 0

            vpn_analytics = {
                "vpn_detection_rate": (vpn_payments / total_payments * 100) if total_payments > 0 else 0.0,
                "proxy_detection_rate": (proxy_payments / total_payments * 100) if total_payments > 0 else 0.0,
                "vpn_impact_on_conversion": float(result.avg_vpn_impact) if result.avg_vpn_impact else 0.0,
                "overall_conversion_rate": float(result.overall_conversion_rate) if result.overall_conversion_rate else 0.0,
                "total_payments": total_payments,
                "vpn_payments": vpn_payments,
                "proxy_payments": proxy_payments,
                "anonymization_rate": ((vpn_payments + proxy_payments) / total_payments * 100) if total_payments > 0 else 0.0
            }

            logger.info(
                f"Retrieved VPN impact analytics for period {start_date} to {end_date}",
                extra={
                    "vpn_detection_rate": vpn_analytics["vpn_detection_rate"],
                    "total_payments": total_payments
                }
            )

            return vpn_analytics

        except Exception as e:
            logger.error(f"Error retrieving VPN impact analytics: {str(e)}")
            raise ServiceError(f"Failed to retrieve VPN analytics: {str(e)}")

    async def create_analytics_aggregation(
        self,
        analysis_date: datetime,
        period_type: AnalyticsPeriodType,
        country_code: Optional[str] = None
    ) -> GeolocationAnalytics:
        """
        Create analytics aggregation for specified period and country.

        Args:
            analysis_date: Date for analytics aggregation
            period_type: Type of aggregation period
            country_code: Optional country code filter

        Returns:
            GeolocationAnalytics: Created analytics record

        Performance Target: <300ms for aggregation creation
        """
        try:
            # Calculate period boundaries
            if period_type == AnalyticsPeriodType.DAILY:
                period_start = analysis_date.replace(hour=0, minute=0, second=0, microsecond=0)
                period_end = period_start + timedelta(days=1) - timedelta(microseconds=1)
            elif period_type == AnalyticsPeriodType.WEEKLY:
                days_since_monday = analysis_date.weekday()
                period_start = (analysis_date - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
                period_end = period_start + timedelta(days=7) - timedelta(microseconds=1)
            elif period_type == AnalyticsPeriodType.MONTHLY:
                period_start = analysis_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                next_month = period_start.replace(month=period_start.month + 1) if period_start.month < 12 else period_start.replace(year=period_start.year + 1, month=1)
                period_end = next_month - timedelta(microseconds=1)
            else:
                raise ValueError(f"Unsupported period type: {period_type}")

            # TODO: Implement actual payment data aggregation
            # This would query the Payment table and aggregate metrics
            # For now, creating a placeholder record

            analytics_record = GeolocationAnalytics(
                analysis_date=analysis_date,
                period_type=period_type,
                period_start=period_start,
                period_end=period_end,
                country_code=country_code,
                total_payments=0,
                successful_payments=0,
                failed_payments=0,
                total_amount=Decimal("0.00"),
                successful_amount=Decimal("0.00"),
                conversion_rate=0.0,
                cache_hit_rate=0.0,
                paystack_payments=0,
                stripe_payments=0,
                busha_payments=0,
                geolocation_routing_count=0,
                currency_routing_count=0,
                user_preference_routing_count=0,
                fallback_routing_count=0,
                vpn_detected_payments=0,
                proxy_detected_payments=0,
                vpn_impact_on_conversion=0.0,
                unique_users=0,
                repeat_users=0,
                avg_transaction_value=Decimal("0.00")
            )

            created_record = await self.create(analytics_record)

            logger.info(
                f"Created analytics aggregation for {analysis_date} ({period_type.value})",
                extra={"country_code": country_code, "record_id": str(created_record.id)}
            )

            return created_record

        except Exception as e:
            logger.error(f"Error creating analytics aggregation: {str(e)}")
            raise ServiceError(f"Failed to create analytics aggregation: {str(e)}")


class ProviderPerformanceRepository(BaseRepository[ProviderPerformanceMetrics]):
    """
    Repository for provider performance metrics and analytics.

    Provides detailed provider performance tracking with geographic
    segmentation and optimization insights.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize provider performance repository."""
        super().__init__(ProviderPerformanceMetrics, db_session)

    async def get_provider_performance_by_country(
        self,
        start_date: datetime,
        end_date: datetime,
        provider: Optional[str] = None,
        country_codes: Optional[List[str]] = None
    ) -> List[ProviderPerformanceMetrics]:
        """
        Get provider performance metrics by country.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            provider: Optional provider filter
            country_codes: Optional country code filter

        Returns:
            List[ProviderPerformanceMetrics]: Provider performance data

        Performance Target: <200ms for provider analytics
        """
        try:
            query = self.db_session.query(ProviderPerformanceMetrics).filter(
                and_(
                    ProviderPerformanceMetrics.analysis_date >= start_date,
                    ProviderPerformanceMetrics.analysis_date <= end_date
                )
            )

            if provider:
                query = query.filter(ProviderPerformanceMetrics.provider == provider)

            if country_codes:
                query = query.filter(ProviderPerformanceMetrics.country_code.in_(country_codes))

            # Order by success rate descending
            query = query.order_by(desc(ProviderPerformanceMetrics.success_rate))

            result = await query.all()

            logger.info(
                f"Retrieved {len(result)} provider performance records",
                extra={
                    "provider": provider,
                    "country_filter": country_codes,
                    "period": f"{start_date} to {end_date}"
                }
            )

            return result

        except Exception as e:
            logger.error(f"Error retrieving provider performance: {str(e)}")
            raise ServiceError(f"Failed to retrieve provider performance: {str(e)}")

    async def get_best_provider_by_country(
        self,
        country_code: str,
        start_date: datetime,
        end_date: datetime,
        metric: str = "success_rate"
    ) -> Optional[ProviderPerformanceMetrics]:
        """
        Get best performing provider for specific country.

        Args:
            country_code: Country code to analyze
            start_date: Analysis start date
            end_date: Analysis end date
            metric: Performance metric to optimize for

        Returns:
            Optional[ProviderPerformanceMetrics]: Best provider or None

        Performance Target: <100ms for single country queries
        """
        try:
            # Map metric names to database columns
            metric_mapping = {
                "success_rate": ProviderPerformanceMetrics.success_rate,
                "cost_effectiveness_score": ProviderPerformanceMetrics.cost_effectiveness_score,
                "avg_processing_time_ms": ProviderPerformanceMetrics.avg_processing_time_ms,
                "completion_rate": ProviderPerformanceMetrics.completion_rate
            }

            if metric not in metric_mapping:
                raise ValueError(f"Invalid metric: {metric}")

            query = self.db_session.query(ProviderPerformanceMetrics).filter(
                and_(
                    ProviderPerformanceMetrics.country_code == country_code,
                    ProviderPerformanceMetrics.analysis_date >= start_date,
                    ProviderPerformanceMetrics.analysis_date <= end_date,
                    ProviderPerformanceMetrics.total_transactions > 0  # Ensure meaningful data
                )
            )

            # Order by metric (ascending for time-based metrics, descending for others)
            if metric == "avg_processing_time_ms":
                query = query.order_by(asc(metric_mapping[metric]))
            else:
                query = query.order_by(desc(metric_mapping[metric]))

            result = await query.first()

            if result:
                logger.info(
                    f"Found best provider for {country_code}: {result.provider}",
                    extra={
                        "country_code": country_code,
                        "provider": result.provider,
                        "metric": metric,
                        "metric_value": getattr(result, metric)
                    }
                )

            return result

        except Exception as e:
            logger.error(f"Error finding best provider for country: {str(e)}")
            raise ServiceError(f"Failed to find best provider: {str(e)}")


class ConversionAnalyticsRepository(BaseRepository[ConversionAnalytics]):
    """
    Repository for conversion rate analytics and optimization insights.

    Provides comprehensive conversion tracking with funnel analysis
    and optimization recommendations.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize conversion analytics repository."""
        super().__init__(ConversionAnalytics, db_session)

    async def get_conversion_funnel_data(
        self,
        start_date: datetime,
        end_date: datetime,
        country_codes: Optional[List[str]] = None,
        market_segment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get conversion funnel analytics data.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            country_codes: Optional country code filter
            market_segment: Optional market segment filter

        Returns:
            Dict[str, Any]: Conversion funnel analytics

        Performance Target: <150ms for funnel analytics
        """
        try:
            query = self.db_session.query(
                func.sum(ConversionAnalytics.total_payment_attempts).label('total_attempts'),
                func.sum(ConversionAnalytics.initiated_payments).label('total_initiated'),
                func.sum(ConversionAnalytics.authorized_payments).label('total_authorized'),
                func.sum(ConversionAnalytics.completed_payments).label('total_completed'),
                func.avg(ConversionAnalytics.overall_conversion_rate).label('avg_conversion_rate'),
                func.avg(ConversionAnalytics.abandonment_rate).label('avg_abandonment_rate')
            ).filter(
                and_(
                    ConversionAnalytics.analysis_date >= start_date,
                    ConversionAnalytics.analysis_date <= end_date
                )
            )

            if country_codes:
                query = query.filter(ConversionAnalytics.country_code.in_(country_codes))

            if market_segment:
                query = query.filter(ConversionAnalytics.market_segment == market_segment)

            result = await query.first()

            if not result:
                return {
                    "funnel_stages": {
                        "attempts": 0,
                        "initiated": 0,
                        "authorized": 0,
                        "completed": 0
                    },
                    "conversion_rates": {
                        "initiation_rate": 0.0,
                        "authorization_rate": 0.0,
                        "completion_rate": 0.0,
                        "overall_rate": 0.0
                    },
                    "abandonment_rate": 0.0
                }

            total_attempts = int(result.total_attempts) if result.total_attempts else 0
            total_initiated = int(result.total_initiated) if result.total_initiated else 0
            total_authorized = int(result.total_authorized) if result.total_authorized else 0
            total_completed = int(result.total_completed) if result.total_completed else 0

            funnel_data = {
                "funnel_stages": {
                    "attempts": total_attempts,
                    "initiated": total_initiated,
                    "authorized": total_authorized,
                    "completed": total_completed
                },
                "conversion_rates": {
                    "initiation_rate": (total_initiated / total_attempts * 100) if total_attempts > 0 else 0.0,
                    "authorization_rate": (total_authorized / total_initiated * 100) if total_initiated > 0 else 0.0,
                    "completion_rate": (total_completed / total_authorized * 100) if total_authorized > 0 else 0.0,
                    "overall_rate": float(result.avg_conversion_rate) if result.avg_conversion_rate else 0.0
                },
                "abandonment_rate": float(result.avg_abandonment_rate) if result.avg_abandonment_rate else 0.0
            }

            logger.info(
                f"Retrieved conversion funnel data for period {start_date} to {end_date}",
                extra={
                    "total_attempts": total_attempts,
                    "overall_conversion_rate": funnel_data["conversion_rates"]["overall_rate"]
                }
            )

            return funnel_data

        except Exception as e:
            logger.error(f"Error retrieving conversion funnel data: {str(e)}")
            raise ServiceError(f"Failed to retrieve conversion funnel: {str(e)}")


class RoutingMetricsRepository(BaseRepository[GeolocationRoutingMetrics]):
    """
    Repository for routing decision analytics and optimization insights.

    Provides routing effectiveness tracking and optimization recommendations.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize routing metrics repository."""
        super().__init__(GeolocationRoutingMetrics, db_session)

    async def get_routing_effectiveness(
        self,
        start_date: datetime,
        end_date: datetime,
        routing_decision: Optional[RoutingDecisionType] = None
    ) -> Dict[str, Any]:
        """
        Get routing decision effectiveness analytics.

        Args:
            start_date: Analysis start date
            end_date: Analysis end date
            routing_decision: Optional routing decision filter

        Returns:
            Dict[str, Any]: Routing effectiveness data

        Performance Target: <150ms for routing analytics
        """
        try:
            query = self.db_session.query(
                GeolocationRoutingMetrics.routing_decision,
                func.sum(GeolocationRoutingMetrics.total_decisions).label('total_decisions'),
                func.sum(GeolocationRoutingMetrics.successful_outcomes).label('successful_outcomes'),
                func.avg(GeolocationRoutingMetrics.decision_accuracy).label('avg_accuracy'),
                func.avg(GeolocationRoutingMetrics.avg_success_rate).label('avg_success_rate'),
                func.avg(GeolocationRoutingMetrics.cost_efficiency_score).label('avg_cost_efficiency')
            ).filter(
                and_(
                    GeolocationRoutingMetrics.analysis_date >= start_date,
                    GeolocationRoutingMetrics.analysis_date <= end_date
                )
            ).group_by(GeolocationRoutingMetrics.routing_decision)

            if routing_decision:
                query = query.filter(GeolocationRoutingMetrics.routing_decision == routing_decision)

            result = await query.all()

            routing_effectiveness = {}
            total_decisions_all = 0
            total_successful_all = 0

            for row in result:
                total_decisions = int(row.total_decisions) if row.total_decisions else 0
                successful_outcomes = int(row.successful_outcomes) if row.successful_outcomes else 0

                total_decisions_all += total_decisions
                total_successful_all += successful_outcomes

                routing_effectiveness[row.routing_decision] = {
                    "total_decisions": total_decisions,
                    "successful_outcomes": successful_outcomes,
                    "success_rate": (successful_outcomes / total_decisions * 100) if total_decisions > 0 else 0.0,
                    "avg_accuracy": float(row.avg_accuracy) if row.avg_accuracy else 0.0,
                    "avg_success_rate": float(row.avg_success_rate) if row.avg_success_rate else 0.0,
                    "avg_cost_efficiency": float(row.avg_cost_efficiency) if row.avg_cost_efficiency else 0.0
                }

            # Calculate overall effectiveness
            overall_effectiveness = {
                "routing_strategies": routing_effectiveness,
                "overall_metrics": {
                    "total_decisions": total_decisions_all,
                    "total_successful": total_successful_all,
                    "overall_success_rate": (total_successful_all / total_decisions_all * 100) if total_decisions_all > 0 else 0.0
                }
            }

            logger.info(
                f"Retrieved routing effectiveness for period {start_date} to {end_date}",
                extra={
                    "total_decisions": total_decisions_all,
                    "strategies_analyzed": len(routing_effectiveness)
                }
            )

            return overall_effectiveness

        except Exception as e:
            logger.error(f"Error retrieving routing effectiveness: {str(e)}")
            raise ServiceError(f"Failed to retrieve routing effectiveness: {str(e)}")
