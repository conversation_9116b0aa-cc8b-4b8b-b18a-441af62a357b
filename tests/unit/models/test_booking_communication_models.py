"""
Unit tests for booking communication models.

This module tests the booking communication models including:
- BookingMessage model with threading and delivery tracking
- MessageAttachment model with security validation
- MessageTemplate model with event triggers
- MessageDeliveryLog model with retry logic

Tests Task 4.1.3 Phase 1 implementation requirements.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal

from app.models.booking_communication import (
    BookingMessage, MessageAttachment, MessageTemplate, MessageDeliveryLog,
    MessageType, MessageStatus, DeliveryMethod, DeliveryStatus, TemplateCategory
)


class TestBookingMessage:
    """Test cases for BookingMessage model."""

    def test_booking_message_creation(self):
        """Test basic booking message creation."""
        message = BookingMessage(
            booking_id=1,
            sender_id=1,
            recipient_id=2,
            message_type=MessageType.USER_MESSAGE,
            subject="Test Message",
            content="This is a test message content.",
            status=MessageStatus.SENT,
            is_read=False,  # Explicitly set default value
            is_automated=False  # Explicitly set default value
        )

        assert message.booking_id == 1
        assert message.sender_id == 1
        assert message.recipient_id == 2
        assert message.message_type == MessageType.USER_MESSAGE
        assert message.subject == "Test Message"
        assert message.content == "This is a test message content."
        assert message.status == MessageStatus.SENT
        assert message.is_read is False
        assert message.is_automated is False

    def test_booking_message_properties(self):
        """Test BookingMessage computed properties."""
        # Test system message
        system_message = BookingMessage(
            booking_id=1,
            message_type=MessageType.SYSTEM,
            content="System notification",
            is_automated=True
        )
        assert system_message.is_system_message is True

        # Test user message
        user_message = BookingMessage(
            booking_id=1,
            message_type=MessageType.USER_MESSAGE,
            content="User message"
        )
        assert user_message.is_system_message is False

    def test_delivery_summary_property(self):
        """Test delivery summary property."""
        message = BookingMessage(
            booking_id=1,
            content="Test message",
            delivery_status={
                "email": {"status": "delivered", "timestamp": "2025-01-26T10:00:00Z"},
                "push": {"status": "failed", "error": "Device not found"},
                "websocket": {"status": "pending"}
            }
        )

        summary = message.delivery_summary
        assert summary["total_channels"] == 3
        assert summary["successful"] == 1
        assert summary["failed"] == 1
        assert summary["pending"] == 1

    def test_message_enums(self):
        """Test message enum values."""
        assert MessageType.USER_MESSAGE == "user_message"
        assert MessageType.AUTOMATED == "automated"
        assert MessageType.SYSTEM == "system"

        assert MessageStatus.SENT == "sent"
        assert MessageStatus.DELIVERED == "delivered"
        assert MessageStatus.READ == "read"


class TestMessageAttachment:
    """Test cases for MessageAttachment model."""

    def test_message_attachment_creation(self):
        """Test basic message attachment creation."""
        attachment = MessageAttachment(
            message_id=1,
            filename="test_document.pdf",
            original_filename="Test Document.pdf",
            file_size=1024000,  # 1MB
            mime_type="application/pdf",
            file_path="/uploads/messages/test_document.pdf",
            storage_provider="local",
            virus_scan_status="clean"
        )

        assert attachment.message_id == 1
        assert attachment.filename == "test_document.pdf"
        assert attachment.original_filename == "Test Document.pdf"
        assert attachment.file_size == 1024000
        assert attachment.mime_type == "application/pdf"
        assert attachment.storage_provider == "local"
        assert attachment.virus_scan_status == "clean"

    def test_attachment_properties(self):
        """Test MessageAttachment computed properties."""
        # Test image attachment
        image_attachment = MessageAttachment(
            message_id=1,
            filename="image.jpg",
            original_filename="image.jpg",
            file_size=500000,
            mime_type="image/jpeg",
            file_path="/uploads/images/image.jpg"
        )
        assert image_attachment.is_image is True
        assert image_attachment.is_document is False
        assert image_attachment.file_size_mb == 0.48

        # Test document attachment
        doc_attachment = MessageAttachment(
            message_id=1,
            filename="document.pdf",
            original_filename="document.pdf",
            file_size=2048000,  # 2MB
            mime_type="application/pdf",
            file_path="/uploads/docs/document.pdf"
        )
        assert doc_attachment.is_image is False
        assert doc_attachment.is_document is True
        assert doc_attachment.file_size_mb == 1.95

    def test_attachment_security_validation(self):
        """Test attachment security properties."""
        safe_attachment = MessageAttachment(
            message_id=1,
            filename="safe_file.pdf",
            original_filename="safe_file.pdf",
            file_size=1000000,
            mime_type="application/pdf",
            file_path="/uploads/safe_file.pdf",
            virus_scan_status="clean"
        )
        assert safe_attachment.is_safe is True

        unsafe_attachment = MessageAttachment(
            message_id=1,
            filename="unsafe_file.exe",
            original_filename="unsafe_file.exe",
            file_size=1000000,
            mime_type="application/octet-stream",
            file_path="/uploads/unsafe_file.exe",
            virus_scan_status="infected"
        )
        assert unsafe_attachment.is_safe is False


class TestMessageTemplate:
    """Test cases for MessageTemplate model."""

    def test_message_template_creation(self):
        """Test basic message template creation."""
        template = MessageTemplate(
            template_key="booking_confirmed",
            name="Booking Confirmation",
            description="Template for booking confirmation messages",
            category=TemplateCategory.BOOKING_LIFECYCLE,
            subject_template="Your booking #{booking_reference} has been confirmed",
            content_template="Dear {customer_name}, your booking for {service_name} on {booking_date} has been confirmed.",
            template_variables={
                "booking_reference": "string",
                "customer_name": "string",
                "service_name": "string",
                "booking_date": "datetime"
            },
            trigger_events=["booking_confirmed"],
            recipient_type="customer",
            is_active=True,
            is_system_template=True,
            version=1  # Explicitly set default value
        )

        assert template.template_key == "booking_confirmed"
        assert template.name == "Booking Confirmation"
        assert template.category == TemplateCategory.BOOKING_LIFECYCLE
        assert template.is_active is True
        assert template.is_system_template is True
        assert template.version == 1

    def test_template_properties(self):
        """Test MessageTemplate computed properties."""
        template = MessageTemplate(
            template_key="test_template",
            name="Test Template",
            category=TemplateCategory.COMMUNICATION,
            subject_template="Test Subject",
            content_template="Test Content",
            template_variables={
                "var1": "string",
                "var2": "number",
                "var3": "boolean"
            },
            trigger_events=["event1", "event2", "event3"]
        )

        assert template.variable_count == 3
        assert template.trigger_event_count == 3

    def test_template_enums(self):
        """Test template enum values."""
        assert TemplateCategory.BOOKING_LIFECYCLE == "booking_lifecycle"
        assert TemplateCategory.PAYMENT == "payment"
        assert TemplateCategory.COMMUNICATION == "communication"


class TestMessageDeliveryLog:
    """Test cases for MessageDeliveryLog model."""

    def test_delivery_log_creation(self):
        """Test basic delivery log creation."""
        delivery_log = MessageDeliveryLog(
            message_id=1,
            delivery_method=DeliveryMethod.EMAIL,
            delivery_status=DeliveryStatus.DELIVERED,
            delivery_provider="smtp",
            delivered_at=datetime.now(timezone.utc),
            delivery_metadata={
                "smtp_server": "smtp.example.com",
                "message_id": "<EMAIL>"
            },
            retry_count=0,  # Explicitly set default value
            max_retries=3   # Explicitly set default value
        )

        assert delivery_log.message_id == 1
        assert delivery_log.delivery_method == DeliveryMethod.EMAIL
        assert delivery_log.delivery_status == DeliveryStatus.DELIVERED
        assert delivery_log.delivery_provider == "smtp"
        assert delivery_log.retry_count == 0
        assert delivery_log.max_retries == 3

    def test_delivery_log_properties(self):
        """Test MessageDeliveryLog computed properties."""
        # Test successful delivery
        successful_log = MessageDeliveryLog(
            message_id=1,
            delivery_method=DeliveryMethod.PUSH,
            delivery_status=DeliveryStatus.DELIVERED,
            attempted_at=datetime.now(timezone.utc) - timedelta(seconds=30),
            delivered_at=datetime.now(timezone.utc)
        )
        assert successful_log.is_successful is True
        assert successful_log.is_failed is False
        assert successful_log.can_retry is False
        assert successful_log.delivery_duration is not None
        assert successful_log.delivery_duration > 0

        # Test failed delivery
        failed_log = MessageDeliveryLog(
            message_id=1,
            delivery_method=DeliveryMethod.SMS,
            delivery_status=DeliveryStatus.FAILED,
            retry_count=2,
            max_retries=3
        )
        assert failed_log.is_successful is False
        assert failed_log.is_failed is True
        assert failed_log.can_retry is True

    def test_retry_increment(self):
        """Test retry increment functionality."""
        delivery_log = MessageDeliveryLog(
            message_id=1,
            delivery_method=DeliveryMethod.WEBSOCKET,
            delivery_status=DeliveryStatus.FAILED,
            retry_count=0,
            max_retries=3
        )

        # First retry
        delivery_log.increment_retry(5)
        assert delivery_log.retry_count == 1
        assert delivery_log.delivery_status == DeliveryStatus.RETRY
        assert delivery_log.next_retry_at is not None

        # Second retry (exponential backoff)
        delivery_log.increment_retry(5)
        assert delivery_log.retry_count == 2
        assert delivery_log.delivery_status == DeliveryStatus.RETRY

        # Third retry (max retries reached)
        delivery_log.increment_retry(5)
        assert delivery_log.retry_count == 3
        assert delivery_log.delivery_status == DeliveryStatus.FAILED
        assert delivery_log.next_retry_at is None
        assert delivery_log.can_retry is False

    def test_delivery_enums(self):
        """Test delivery enum values."""
        assert DeliveryMethod.EMAIL == "email"
        assert DeliveryMethod.PUSH == "push"
        assert DeliveryMethod.WEBSOCKET == "websocket"

        assert DeliveryStatus.PENDING == "pending"
        assert DeliveryStatus.DELIVERED == "delivered"
        assert DeliveryStatus.FAILED == "failed"
