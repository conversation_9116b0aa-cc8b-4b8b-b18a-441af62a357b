"""
Integration tests for enhanced booking communication system - Task 4.1.3.

This module provides comprehensive integration testing for the enhanced booking communication
system including conversation management, real-time delivery, and performance validation.

Tests cover:
- Enhanced conversation management features
- Multi-channel delivery orchestration
- Bulk operations performance
- Real-time WebSocket integration
- Cross-service communication validation
- Performance targets validation (<500ms creation, <200ms retrieval, <100ms updates)
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from unittest.mock import AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.booking_communication_service import (
    BookingMessageService, MessageDeliveryService
)
from app.models.booking_communication import MessageType, DeliveryMethod, DeliveryStatus
from app.schemas.booking_communication_schemas import (
    BookingMessageCreate, MessageAttachmentCreate
)
from app.repositories.base import PaginationParams
from tests.conftest import TestClient


class TestEnhancedBookingCommunicationIntegration:
    """Integration tests for enhanced booking communication system."""

    @pytest.fixture
    async def message_service(self, db_session: AsyncSession) -> BookingMessageService:
        """Create booking message service instance."""
        return BookingMessageService(db_session)

    @pytest.fixture
    async def delivery_service(self, db_session: AsyncSession) -> MessageDeliveryService:
        """Create message delivery service instance."""
        return MessageDeliveryService(db_session)

    @pytest.fixture
    async def sample_booking_data(self, db_session: AsyncSession) -> Dict[str, Any]:
        """Create sample booking data for testing."""
        # This would create actual booking data in a real test
        return {
            "booking_id": 1,
            "customer_id": 1,
            "vendor_id": 2,
            "booking_reference": "BK-TEST-001"
        }

    @pytest.mark.asyncio
    async def test_enhanced_conversation_summary_performance(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test conversation summary generation with performance validation."""
        booking_id = sample_booking_data["booking_id"]
        user_id = sample_booking_data["customer_id"]

        # Performance test: Target <150ms for conversation summary
        start_time = time.time()
        
        summary = await message_service.get_conversation_summary(
            booking_id=booking_id,
            user_id=user_id
        )
        
        operation_time = time.time() - start_time
        
        # Validate performance target
        assert operation_time < 0.15, f"Conversation summary took {operation_time:.3f}s, target: <150ms"
        
        # Validate summary structure
        assert "booking_id" in summary
        assert "analytics" in summary
        assert "recent_messages" in summary
        assert "participants" in summary
        assert "conversation_status" in summary
        
        # Validate analytics data
        analytics = summary["analytics"]
        assert isinstance(analytics, dict)
        
        # Validate participants information
        participants = summary["participants"]
        assert isinstance(participants, list)

    @pytest.mark.asyncio
    async def test_bulk_conversation_read_marking_performance(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test bulk conversation read marking with performance validation."""
        booking_id = sample_booking_data["booking_id"]
        user_id = sample_booking_data["customer_id"]

        # Create multiple test messages first
        message_data = BookingMessageCreate(
            content="Test message for bulk read marking",
            message_type=MessageType.USER,
            recipient_id=user_id
        )

        # Create 10 test messages
        created_messages = []
        for i in range(10):
            message = await message_service.send_message(
                booking_id=booking_id,
                sender_id=sample_booking_data["vendor_id"],
                message_data=message_data
            )
            created_messages.append(message)

        # Performance test: Target <200ms for conversation read marking
        start_time = time.time()
        
        result = await message_service.mark_conversation_as_read(
            booking_id=booking_id,
            user_id=user_id
        )
        
        operation_time = time.time() - start_time
        
        # Validate performance target
        assert operation_time < 0.2, f"Conversation read marking took {operation_time:.3f}s, target: <200ms"
        
        # Validate result structure
        assert result["booking_id"] == booking_id
        assert result["user_id"] == user_id
        assert result["status"] == "success"
        assert result["messages_marked"] >= 0

    @pytest.mark.asyncio
    async def test_multi_channel_delivery_orchestration(
        self,
        delivery_service: MessageDeliveryService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test multi-channel delivery orchestration with performance validation."""
        # Create a test message first
        message_service = BookingMessageService(delivery_service.db)
        
        message_data = BookingMessageCreate(
            content="Test message for multi-channel delivery",
            message_type=MessageType.USER,
            recipient_id=sample_booking_data["customer_id"]
        )

        message = await message_service.send_message(
            booking_id=sample_booking_data["booking_id"],
            sender_id=sample_booking_data["vendor_id"],
            message_data=message_data
        )

        # Test multi-channel delivery orchestration
        delivery_channels = [DeliveryMethod.EMAIL, DeliveryMethod.PUSH, DeliveryMethod.WEBSOCKET]
        
        # Performance test: Target <100ms for delivery orchestration
        start_time = time.time()
        
        with patch.object(delivery_service, '_attempt_delivery_with_retry', new_callable=AsyncMock) as mock_delivery:
            mock_delivery.return_value = {
                "status": "delivered",
                "delivery_method": "email",
                "retry_count": 0,
                "delivered_at": datetime.now().isoformat()
            }
            
            result = await delivery_service.orchestrate_multi_channel_delivery(
                message_id=message.id,
                delivery_channels=delivery_channels
            )
        
        operation_time = time.time() - start_time
        
        # Validate performance target
        assert operation_time < 0.1, f"Delivery orchestration took {operation_time:.3f}s, target: <100ms"
        
        # Validate delivery results
        assert result["message_id"] == message.id
        assert "delivery_results" in result
        assert result["total_channels"] == len(delivery_channels)
        assert "operation_time" in result

    @pytest.mark.asyncio
    async def test_bulk_message_operations_performance(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test bulk message operations with performance validation."""
        booking_id = sample_booking_data["booking_id"]
        sender_id = sample_booking_data["vendor_id"]
        recipient_id = sample_booking_data["customer_id"]

        # Prepare bulk message data
        messages_data = []
        for i in range(50):  # Test with 50 messages
            messages_data.append({
                "booking_id": booking_id,
                "sender_id": sender_id,
                "recipient_id": recipient_id,
                "content": f"Bulk test message {i+1}",
                "message_type": MessageType.USER.value
            })

        # Performance test: Target <300ms for bulk message creation
        start_time = time.time()
        
        with patch.object(message_service.repository, 'bulk_create_messages', new_callable=AsyncMock) as mock_bulk:
            # Mock successful bulk creation
            mock_messages = []
            for i, data in enumerate(messages_data):
                mock_message = AsyncMock()
                mock_message.id = i + 1
                mock_message.booking_id = data["booking_id"]
                mock_message.content = data["content"]
                mock_message.message_type = MessageType.USER
                mock_message.created_at = datetime.now()
                mock_messages.append(mock_message)
            
            mock_bulk.return_value = mock_messages
            
            # Mock delivery initialization
            with patch.object(message_service, '_initialize_message_delivery', new_callable=AsyncMock):
                with patch.object(message_service, '_send_message_notifications', new_callable=AsyncMock):
                    result = await message_service.bulk_send_messages(messages_data)
        
        operation_time = time.time() - start_time
        
        # Validate performance target
        assert operation_time < 0.3, f"Bulk message creation took {operation_time:.3f}s, target: <300ms"
        
        # Validate bulk operation results
        assert len(result) == len(messages_data)

    @pytest.mark.asyncio
    async def test_message_retrieval_performance(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test message retrieval performance with pagination."""
        booking_id = sample_booking_data["booking_id"]
        user_id = sample_booking_data["customer_id"]

        # Performance test: Target <200ms for message retrieval
        start_time = time.time()
        
        pagination = PaginationParams(page=1, per_page=20)
        
        with patch.object(message_service.repository, 'get_booking_messages', new_callable=AsyncMock) as mock_get:
            # Mock successful message retrieval
            from app.repositories.base import QueryResult
            mock_messages = []
            for i in range(20):
                mock_message = AsyncMock()
                mock_message.id = i + 1
                mock_message.booking_id = booking_id
                mock_message.content = f"Test message {i+1}"
                mock_messages.append(mock_message)
            
            mock_result = QueryResult(
                items=mock_messages,
                total=100,
                page=1,
                size=20,
                has_next=True,
                has_previous=False
            )
            mock_get.return_value = mock_result
            
            result = await message_service.get_booking_messages(
                booking_id=booking_id,
                user_id=user_id,
                pagination=pagination
            )
        
        operation_time = time.time() - start_time
        
        # Validate performance target
        assert operation_time < 0.2, f"Message retrieval took {operation_time:.3f}s, target: <200ms"
        
        # Validate retrieval results
        assert len(result.items) == 20
        assert result.total == 100
        assert result.page == 1

    @pytest.mark.asyncio
    async def test_real_time_websocket_integration(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test real-time WebSocket integration for message delivery."""
        booking_id = sample_booking_data["booking_id"]
        sender_id = sample_booking_data["vendor_id"]
        recipient_id = sample_booking_data["customer_id"]

        message_data = BookingMessageCreate(
            content="Test WebSocket message",
            message_type=MessageType.USER,
            recipient_id=recipient_id
        )

        # Mock WebSocket service integration
        with patch('app.services.booking_communication_service.WebSocketService') as mock_ws_service:
            mock_ws_instance = AsyncMock()
            mock_ws_service.return_value = mock_ws_instance
            
            # Test message sending with WebSocket delivery
            message = await message_service.send_message(
                booking_id=booking_id,
                sender_id=sender_id,
                message_data=message_data
            )
            
            # Verify message was created
            assert message.id is not None
            assert message.content == "Test WebSocket message"
            assert message.booking_id == booking_id

    @pytest.mark.asyncio
    async def test_cross_service_integration_validation(
        self,
        message_service: BookingMessageService,
        sample_booking_data: Dict[str, Any]
    ):
        """Test cross-service integration with email and push notification services."""
        booking_id = sample_booking_data["booking_id"]
        sender_id = sample_booking_data["vendor_id"]
        recipient_id = sample_booking_data["customer_id"]

        message_data = BookingMessageCreate(
            content="Test cross-service integration",
            message_type=MessageType.USER,
            recipient_id=recipient_id
        )

        # Mock email and push notification services
        with patch.object(message_service.email_service, 'send_booking_message_notification', new_callable=AsyncMock) as mock_email:
            with patch.object(message_service.push_service, 'send_notification_to_user', new_callable=AsyncMock) as mock_push:
                
                message = await message_service.send_message(
                    booking_id=booking_id,
                    sender_id=sender_id,
                    message_data=message_data
                )
                
                # Verify message was created
                assert message.id is not None
                assert message.content == "Test cross-service integration"
                
                # Note: Email and push notifications are called asynchronously
                # In a real integration test, we would verify the actual calls
