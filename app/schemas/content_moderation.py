"""
Content Moderation schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic V2 schemas for content moderation including:
- Content workflow management and tracking
- Quality assessment and scoring
- Plagiarism detection and analysis
- Moderation rules and configuration
- Approval history and audit trail

Implements Task 3.2.4 requirements with production-grade validation,
computed fields, and seamless integration with existing marketplace systems.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, computed_field, field_validator, ConfigDict
from pydantic.types import PositiveInt, NonNegativeInt, PositiveFloat, NonNegativeFloat

from app.schemas.base import BaseSchema, TimestampMixin


class ContentStatus(str, Enum):
    """Content moderation status enumeration."""
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    FLAGGED = "flagged"
    REQUIRES_REVISION = "requires_revision"
    AUTO_APPROVED = "auto_approved"


class ContentType(str, Enum):
    """Content type enumeration for moderation."""
    SERVICE_LISTING = "service_listing"
    SERVICE_DESCRIPTION = "service_description"
    SERVICE_IMAGE = "service_image"
    VENDOR_PROFILE = "vendor_profile"
    VENDOR_DESCRIPTION = "vendor_description"
    REVIEW_CONTENT = "review_content"
    PROMOTIONAL_CONTENT = "promotional_content"


class ModerationAction(str, Enum):
    """Moderation action enumeration."""
    APPROVE = "approve"
    REJECT = "reject"
    FLAG = "flag"
    REQUEST_REVISION = "request_revision"
    ESCALATE = "escalate"
    AUTO_APPROVE = "auto_approve"
    AUTO_REJECT = "auto_reject"


class QualityCategory(str, Enum):
    """Quality assessment category enumeration."""
    CONTENT_QUALITY = "content_quality"
    IMAGE_QUALITY = "image_quality"
    METADATA_COMPLETENESS = "metadata_completeness"
    SEO_OPTIMIZATION = "seo_optimization"
    MOBILE_FRIENDLINESS = "mobile_friendliness"
    CULTURAL_APPROPRIATENESS = "cultural_appropriateness"


class PlagiarismStatus(str, Enum):
    """Plagiarism detection status enumeration."""
    NOT_CHECKED = "not_checked"
    CHECKING = "checking"
    ORIGINAL = "original"
    SIMILAR_FOUND = "similar_found"
    PLAGIARIZED = "plagiarized"
    INCONCLUSIVE = "inconclusive"


# Base schemas for content moderation workflow
class ContentModerationWorkflowBase(BaseSchema):
    """Base schema for content moderation workflow."""
    content_type: ContentType = Field(..., description="Type of content being moderated")
    content_id: UUID = Field(..., description="ID of the content being moderated")
    vendor_id: UUID = Field(..., description="ID of the vendor who owns the content")
    priority: int = Field(default=5, ge=1, le=10, description="Priority level (1-10, 10=highest)")
    content_title: Optional[str] = Field(None, max_length=255, description="Title of the content")
    content_summary: Optional[str] = Field(None, description="Summary of the content")
    content_url: Optional[str] = Field(None, max_length=500, description="URL to the content")
    content_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional content metadata")


class ContentModerationWorkflowCreate(ContentModerationWorkflowBase):
    """Schema for creating content moderation workflow."""

    @field_validator('content_metadata')
    @classmethod
    def validate_content_metadata(cls, v):
        """Validate content metadata structure."""
        if not isinstance(v, dict):
            raise ValueError("Content metadata must be a dictionary")
        return v


class ContentModerationWorkflowUpdate(BaseSchema):
    """Schema for updating content moderation workflow."""
    status: Optional[ContentStatus] = None
    priority: Optional[int] = Field(None, ge=1, le=10)
    assigned_reviewer_id: Optional[UUID] = None
    reviewer_notes: Optional[str] = None
    rejection_reason: Optional[str] = None
    revision_requirements: Optional[List[str]] = None
    escalation_reason: Optional[str] = None
    content_metadata: Optional[Dict[str, Any]] = None


class ContentModerationWorkflowResponse(ContentModerationWorkflowBase, TimestampMixin):
    """Schema for content moderation workflow response."""
    id: UUID
    status: ContentStatus
    assigned_reviewer_id: Optional[UUID] = None
    moderation_score: float = Field(ge=0, le=100)
    quality_score: float = Field(ge=0, le=100)
    plagiarism_score: float = Field(ge=0, le=100)
    automated_flags: List[str] = Field(default_factory=list)
    manual_flags: List[str] = Field(default_factory=list)
    reviewer_notes: Optional[str] = None
    rejection_reason: Optional[str] = None
    revision_requirements: List[str] = Field(default_factory=list)
    escalation_reason: Optional[str] = None
    submitted_at: datetime
    review_started_at: Optional[datetime] = None
    review_completed_at: Optional[datetime] = None
    auto_approval_deadline: Optional[datetime] = None

    @computed_field
    @property
    def overall_health_score(self) -> float:
        """Calculate overall content health score."""
        scores = [self.moderation_score, self.quality_score, (100 - self.plagiarism_score)]
        return sum(scores) / len(scores)

    @computed_field
    @property
    def review_duration_hours(self) -> Optional[float]:
        """Calculate review duration in hours."""
        if self.review_started_at and self.review_completed_at:
            duration = self.review_completed_at - self.review_started_at
            return duration.total_seconds() / 3600
        return None

    @computed_field
    @property
    def is_overdue(self) -> bool:
        """Check if workflow is overdue for review."""
        if self.auto_approval_deadline and self.status in [ContentStatus.PENDING, ContentStatus.UNDER_REVIEW]:
            return datetime.utcnow() > self.auto_approval_deadline.replace(tzinfo=None)
        return False

    model_config = ConfigDict(from_attributes=True)


# Quality score schemas
class ContentQualityScoreBase(BaseSchema):
    """Base schema for content quality score."""
    overall_score: float = Field(ge=0, le=100, description="Overall quality score")
    weighted_score: float = Field(ge=0, le=100, description="Weighted quality score")
    quality_grade: Optional[str] = Field(None, max_length=2, description="Quality grade (A+, A, B+, etc.)")
    content_quality_score: float = Field(default=0.0, ge=0, le=100)
    image_quality_score: float = Field(default=0.0, ge=0, le=100)
    metadata_completeness_score: float = Field(default=0.0, ge=0, le=100)
    seo_optimization_score: float = Field(default=0.0, ge=0, le=100)
    mobile_friendliness_score: float = Field(default=0.0, ge=0, le=100)
    cultural_appropriateness_score: float = Field(default=0.0, ge=0, le=100)


class ContentQualityScoreCreate(ContentQualityScoreBase):
    """Schema for creating content quality score."""
    workflow_id: UUID = Field(..., description="ID of the associated workflow")
    content_analysis: Dict[str, Any] = Field(default_factory=dict)
    image_analysis: Dict[str, Any] = Field(default_factory=dict)
    seo_analysis: Dict[str, Any] = Field(default_factory=dict)
    mobile_analysis: Dict[str, Any] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)
    priority_improvements: List[str] = Field(default_factory=list)
    estimated_improvement_impact: float = Field(default=0.0, ge=0, le=100)
    scoring_weights: Dict[str, float] = Field(default_factory=dict)
    scoring_version: str = Field(default="1.0", max_length=20)


class ContentQualityScoreUpdate(BaseSchema):
    """Schema for updating content quality score."""
    overall_score: Optional[float] = Field(None, ge=0, le=100)
    weighted_score: Optional[float] = Field(None, ge=0, le=100)
    quality_grade: Optional[str] = Field(None, max_length=2)
    recommendations: Optional[List[str]] = None
    priority_improvements: Optional[List[str]] = None
    estimated_improvement_impact: Optional[float] = Field(None, ge=0, le=100)


class ContentQualityScoreResponse(ContentQualityScoreBase):
    """Schema for content quality score response."""
    id: UUID
    workflow_id: UUID
    content_analysis: Dict[str, Any]
    image_analysis: Dict[str, Any]
    seo_analysis: Dict[str, Any]
    mobile_analysis: Dict[str, Any]
    recommendations: List[str]
    priority_improvements: List[str]
    estimated_improvement_impact: float
    scoring_weights: Dict[str, float]
    scoring_version: str
    calculated_at: datetime
    calculation_duration_ms: int

    @computed_field
    @property
    def category_scores(self) -> Dict[str, float]:
        """Get all category scores as a dictionary."""
        return {
            "content_quality": self.content_quality_score,
            "image_quality": self.image_quality_score,
            "metadata_completeness": self.metadata_completeness_score,
            "seo_optimization": self.seo_optimization_score,
            "mobile_friendliness": self.mobile_friendliness_score,
            "cultural_appropriateness": self.cultural_appropriateness_score,
        }

    @computed_field
    @property
    def improvement_potential(self) -> float:
        """Calculate improvement potential based on current scores."""
        scores = list(self.category_scores.values())
        if not scores:
            return 0.0
        min_score = min(scores)
        return 100 - min_score

    model_config = ConfigDict(from_attributes=True)


# Plagiarism check schemas
class PlagiarismCheckBase(BaseSchema):
    """Base schema for plagiarism check."""
    status: PlagiarismStatus = Field(default=PlagiarismStatus.NOT_CHECKED)
    similarity_percentage: float = Field(default=0.0, ge=0, le=100)
    originality_score: float = Field(default=100.0, ge=0, le=100)
    confidence_level: float = Field(default=0.0, ge=0, le=100)
    similar_sources_found: NonNegativeInt = Field(default=0)
    exact_matches_found: NonNegativeInt = Field(default=0)
    paraphrased_matches_found: NonNegativeInt = Field(default=0)


class PlagiarismCheckCreate(PlagiarismCheckBase):
    """Schema for creating plagiarism check."""
    workflow_id: UUID = Field(..., description="ID of the associated workflow")
    similar_sources: List[Dict[str, Any]] = Field(default_factory=list)
    matching_segments: List[Dict[str, Any]] = Field(default_factory=list)
    source_analysis: Dict[str, Any] = Field(default_factory=dict)
    check_parameters: Dict[str, Any] = Field(default_factory=dict)
    sensitivity_level: str = Field(default="medium", pattern="^(low|medium|high)$")
    excluded_sources: List[str] = Field(default_factory=list)
    content_hash: str = Field(..., max_length=64)
    content_length: NonNegativeInt = Field(default=0)
    words_checked: NonNegativeInt = Field(default=0)
    check_version: str = Field(default="1.0", max_length=20)


class PlagiarismCheckUpdate(BaseSchema):
    """Schema for updating plagiarism check."""
    status: Optional[PlagiarismStatus] = None
    similarity_percentage: Optional[float] = Field(None, ge=0, le=100)
    originality_score: Optional[float] = Field(None, ge=0, le=100)
    confidence_level: Optional[float] = Field(None, ge=0, le=100)
    similar_sources: Optional[List[Dict[str, Any]]] = None
    matching_segments: Optional[List[Dict[str, Any]]] = None


class PlagiarismCheckResponse(PlagiarismCheckBase):
    """Schema for plagiarism check response."""
    id: UUID
    workflow_id: UUID
    similar_sources: List[Dict[str, Any]]
    matching_segments: List[Dict[str, Any]]
    source_analysis: Dict[str, Any]
    check_parameters: Dict[str, Any]
    sensitivity_level: str
    excluded_sources: List[str]
    content_hash: str
    content_length: int
    words_checked: int
    processing_time_ms: int
    checked_at: datetime
    check_version: str

    @computed_field
    @property
    def plagiarism_risk_level(self) -> str:
        """Determine plagiarism risk level based on similarity percentage."""
        if self.similarity_percentage >= 80:
            return "critical"
        elif self.similarity_percentage >= 60:
            return "high"
        elif self.similarity_percentage >= 40:
            return "medium"
        elif self.similarity_percentage >= 20:
            return "low"
        else:
            return "minimal"

    @computed_field
    @property
    def total_matches_found(self) -> int:
        """Calculate total matches found."""
        return self.exact_matches_found + self.paraphrased_matches_found

    model_config = ConfigDict(from_attributes=True)


# Moderation rule schemas
class ContentModerationRuleBase(BaseSchema):
    """Base schema for content moderation rule."""
    rule_name: str = Field(..., max_length=100, description="Unique name for the rule")
    rule_description: Optional[str] = Field(None, description="Description of the rule")
    content_types: List[ContentType] = Field(default_factory=list, description="Applicable content types")
    rule_category: str = Field(..., max_length=50, description="Category of the rule")
    rule_severity: str = Field(default="medium", pattern="^(low|medium|high|critical)$")
    detection_criteria: Dict[str, Any] = Field(..., description="Criteria for rule detection")
    threshold_values: Dict[str, float] = Field(default_factory=dict)
    action_on_trigger: ModerationAction = Field(..., description="Action to take when rule triggers")
    escalation_threshold: float = Field(default=0.8, ge=0, le=1)


class ContentModerationRuleCreate(ContentModerationRuleBase):
    """Schema for creating content moderation rule."""
    rule_logic: Dict[str, Any] = Field(..., description="Rule logic conditions and operators")
    custom_keywords: List[str] = Field(default_factory=list)
    excluded_keywords: List[str] = Field(default_factory=list)
    regex_patterns: List[str] = Field(default_factory=list)
    is_active: bool = Field(default=True)
    auto_apply: bool = Field(default=True)
    requires_human_review: bool = Field(default=False)

    @field_validator('rule_logic')
    @classmethod
    def validate_rule_logic(cls, v):
        """Validate rule logic structure."""
        if not isinstance(v, dict):
            raise ValueError("Rule logic must be a dictionary")
        required_keys = ['conditions', 'operator']
        if not all(key in v for key in required_keys):
            raise ValueError(f"Rule logic must contain: {required_keys}")
        return v


class ContentModerationRuleUpdate(BaseSchema):
    """Schema for updating content moderation rule."""
    rule_description: Optional[str] = None
    content_types: Optional[List[ContentType]] = None
    rule_severity: Optional[str] = Field(None, pattern="^(low|medium|high|critical)$")
    detection_criteria: Optional[Dict[str, Any]] = None
    threshold_values: Optional[Dict[str, float]] = None
    action_on_trigger: Optional[ModerationAction] = None
    escalation_threshold: Optional[float] = Field(None, ge=0, le=1)
    rule_logic: Optional[Dict[str, Any]] = None
    custom_keywords: Optional[List[str]] = None
    excluded_keywords: Optional[List[str]] = None
    regex_patterns: Optional[List[str]] = None
    is_active: Optional[bool] = None
    auto_apply: Optional[bool] = None
    requires_human_review: Optional[bool] = None


class ContentModerationRuleResponse(ContentModerationRuleBase, TimestampMixin):
    """Schema for content moderation rule response."""
    id: UUID
    rule_logic: Dict[str, Any]
    custom_keywords: List[str]
    excluded_keywords: List[str]
    regex_patterns: List[str]
    is_active: bool
    auto_apply: bool
    requires_human_review: bool
    total_applications: int
    successful_detections: int
    false_positives: int
    accuracy_rate: float
    created_by: Optional[UUID] = None
    last_modified_by: Optional[UUID] = None

    @computed_field
    @property
    def effectiveness_score(self) -> float:
        """Calculate rule effectiveness score."""
        if self.total_applications == 0:
            return 0.0
        return (self.successful_detections / self.total_applications) * 100

    @computed_field
    @property
    def false_positive_rate(self) -> float:
        """Calculate false positive rate."""
        if self.total_applications == 0:
            return 0.0
        return (self.false_positives / self.total_applications) * 100

    model_config = ConfigDict(from_attributes=True)


# Approval history schemas
class ContentApprovalHistoryBase(BaseSchema):
    """Base schema for content approval history."""
    action_taken: ModerationAction = Field(..., description="Action taken by reviewer")
    previous_status: ContentStatus = Field(..., description="Previous content status")
    new_status: ContentStatus = Field(..., description="New content status")
    decision_reason: Optional[str] = Field(None, description="Reason for the decision")
    reviewer_notes: Optional[str] = Field(None, description="Additional reviewer notes")
    confidence_score: float = Field(default=0.0, ge=0, le=100)
    time_spent_minutes: NonNegativeInt = Field(default=0)


class ContentApprovalHistoryCreate(ContentApprovalHistoryBase):
    """Schema for creating content approval history."""
    workflow_id: UUID = Field(..., description="ID of the associated workflow")
    reviewer_id: UUID = Field(..., description="ID of the reviewer")
    reviewer_role: Optional[str] = Field(None, max_length=50)
    is_automated: bool = Field(default=False)
    quality_score_at_decision: float = Field(default=0.0, ge=0, le=100)
    moderation_score_at_decision: float = Field(default=0.0, ge=0, le=100)
    plagiarism_score_at_decision: float = Field(default=0.0, ge=0, le=100)
    revision_requirements: List[str] = Field(default_factory=list)
    estimated_revision_time: NonNegativeInt = Field(default=0)
    revision_priority: str = Field(default="medium", pattern="^(low|medium|high|urgent)$")
    escalated_to: Optional[UUID] = None
    escalation_reason: Optional[str] = None
    escalation_urgency: str = Field(default="normal", pattern="^(low|normal|high|urgent)$")
    decision_factors: List[str] = Field(default_factory=list)
    automated_flags_considered: List[str] = Field(default_factory=list)
    manual_flags_considered: List[str] = Field(default_factory=list)
    review_duration_minutes: NonNegativeInt = Field(default=0)


class ContentApprovalHistoryResponse(ContentApprovalHistoryBase):
    """Schema for content approval history response."""
    id: UUID
    workflow_id: UUID
    reviewer_id: UUID
    reviewer_role: Optional[str] = None
    is_automated: bool
    quality_score_at_decision: float
    moderation_score_at_decision: float
    plagiarism_score_at_decision: float
    revision_requirements: List[str]
    estimated_revision_time: int
    revision_priority: str
    escalated_to: Optional[UUID] = None
    escalation_reason: Optional[str] = None
    escalation_urgency: str
    decision_factors: List[str]
    automated_flags_considered: List[str]
    manual_flags_considered: List[str]
    decision_made_at: datetime
    review_duration_minutes: int

    @computed_field
    @property
    def decision_quality_indicator(self) -> str:
        """Indicate decision quality based on scores and time spent."""
        if self.confidence_score >= 90 and self.time_spent_minutes >= 5:
            return "high_quality"
        elif self.confidence_score >= 70 and self.time_spent_minutes >= 3:
            return "good_quality"
        elif self.confidence_score >= 50:
            return "acceptable_quality"
        else:
            return "needs_review"

    @computed_field
    @property
    def has_escalation(self) -> bool:
        """Check if decision involved escalation."""
        return self.escalated_to is not None

    model_config = ConfigDict(from_attributes=True)


# Comprehensive dashboard schema
class ContentModerationDashboard(BaseSchema):
    """Comprehensive content moderation dashboard schema."""
    total_workflows: int = Field(ge=0)
    pending_reviews: int = Field(ge=0)
    completed_reviews: int = Field(ge=0)
    auto_approved: int = Field(ge=0)
    rejected_content: int = Field(ge=0)
    flagged_content: int = Field(ge=0)
    average_review_time_hours: float = Field(ge=0)
    average_quality_score: float = Field(ge=0, le=100)
    average_plagiarism_score: float = Field(ge=0, le=100)

    # Performance metrics
    reviewer_performance: List[Dict[str, Any]] = Field(default_factory=list)
    content_type_statistics: Dict[str, int] = Field(default_factory=dict)
    quality_trends: List[Dict[str, Any]] = Field(default_factory=list)
    moderation_rule_effectiveness: List[Dict[str, Any]] = Field(default_factory=list)

    # Recent activity
    recent_approvals: List[ContentModerationWorkflowResponse] = Field(default_factory=list)
    recent_rejections: List[ContentModerationWorkflowResponse] = Field(default_factory=list)
    overdue_reviews: List[ContentModerationWorkflowResponse] = Field(default_factory=list)

    @computed_field
    @property
    def approval_rate(self) -> float:
        """Calculate overall approval rate."""
        total_completed = self.completed_reviews + self.auto_approved
        if total_completed == 0:
            return 0.0
        approved = self.completed_reviews + self.auto_approved - self.rejected_content
        return (approved / total_completed) * 100

    @computed_field
    @property
    def automation_rate(self) -> float:
        """Calculate automation rate."""
        total_processed = self.completed_reviews + self.auto_approved
        if total_processed == 0:
            return 0.0
        return (self.auto_approved / total_processed) * 100

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_workflows": 1250,
                "pending_reviews": 45,
                "completed_reviews": 1180,
                "auto_approved": 890,
                "rejected_content": 25,
                "flagged_content": 15,
                "average_review_time_hours": 2.5,
                "average_quality_score": 87.3,
                "average_plagiarism_score": 8.2,
                "approval_rate": 94.5,
                "automation_rate": 71.2
            }
        }
    )