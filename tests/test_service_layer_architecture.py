"""
Comprehensive tests for Task 1.3.1 - Service Layer Architecture

This test suite validates the BaseService implementation and service layer architecture
according to the requirements in ToDo.md Task 1.3.1.

Test Coverage:
- BaseService functionality and error handling
- Service registry and dependency injection
- Service lifecycle management
- Error propagation and logging
- Service discovery and caching
- Production-grade patterns validation
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, Optional, List

# Import the service layer components
from app.services.base import (
    BaseService, ServiceError, ValidationError, NotFoundError, ConflictError,
    ServiceRegistry, service_registry
)
from app.db.session import session_manager
from app.core.config import settings


class SimpleTestService:
    """Simple test service for testing service layer functionality"""

    def __init__(self, **kwargs):
        self.test_data = {}
        self.logger = logging.getLogger(f"{__name__}.SimpleTestService")

    async def create_test_item(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test method for creating items"""
        if not data.get("name"):
            raise ValidationError("Name is required")

        item_id = len(self.test_data) + 1
        item = {"id": item_id, **data}
        self.test_data[item_id] = item

        self.logger.info(f"Created test item: {item_id}")
        return item

    async def get_test_item(self, item_id: int) -> Dict[str, Any]:
        """Test method for retrieving items"""
        if item_id not in self.test_data:
            raise NotFoundError("TestItem", item_id)

        return self.test_data[item_id]

    async def update_test_item(self, item_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test method for updating items"""
        if item_id not in self.test_data:
            raise NotFoundError("TestItem", item_id)

        if data.get("name") == "conflict":
            raise ConflictError("Name conflicts with existing item")

        self.test_data[item_id].update(data)
        self.logger.info(f"Updated test item: {item_id}")
        return self.test_data[item_id]


class SimpleDependentService:
    """Simple dependent service for testing dependency injection"""

    def __init__(self, test_service: SimpleTestService = None, **kwargs):
        self.test_service = test_service
        self.logger = logging.getLogger(f"{__name__}.SimpleDependentService")

    async def create_dependent_item(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create item using dependent service"""
        if not self.test_service:
            raise ServiceError("TestService dependency not available")

        # Use the dependent service
        base_item = await self.test_service.create_test_item(data)

        # Add dependent-specific data
        dependent_item = {
            **base_item,
            "dependent_field": "dependent_value",
            "created_by": "dependent_service"
        }

        return dependent_item


@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    session = Mock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def test_service():
    """Create test service instance"""
    return SimpleTestService()


@pytest.fixture
def dependent_service(test_service):
    """Create dependent service instance"""
    return SimpleDependentService(test_service=test_service)


class TestServiceLayer:
    """Test service layer core functionality"""

    def test_service_initialization(self, test_service):
        """Test service initialization with proper attributes"""
        assert test_service.logger is not None
        assert hasattr(test_service, 'test_data')
        assert test_service.test_data == {}

    @pytest.mark.asyncio
    async def test_successful_operation(self, test_service):
        """Test successful service operation"""
        data = {"name": "test_item", "description": "Test description"}
        result = await test_service.create_test_item(data)

        assert result["id"] == 1
        assert result["name"] == "test_item"
        assert result["description"] == "Test description"

    @pytest.mark.asyncio
    async def test_validation_error(self, test_service):
        """Test validation error handling"""
        with pytest.raises(ValidationError) as exc_info:
            await test_service.create_test_item({})

        assert "Name is required" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_not_found_error(self, test_service):
        """Test not found error handling"""
        with pytest.raises(NotFoundError) as exc_info:
            await test_service.get_test_item(999)

        assert "TestItem not found: 999" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_conflict_error(self, test_service):
        """Test conflict error handling"""
        # Create an item first
        await test_service.create_test_item({"name": "test_item"})

        # Try to update with conflicting data
        with pytest.raises(ConflictError) as exc_info:
            await test_service.update_test_item(1, {"name": "conflict"})

        assert "Name conflicts with existing item" in str(exc_info.value)

    def test_logger_configuration(self, test_service):
        """Test logger is properly configured"""
        assert test_service.logger.level <= logging.INFO
        assert test_service.logger.name.startswith("test_service_layer_architecture")


class TestServiceErrors:
    """Test service error handling"""

    def test_service_error_creation(self):
        """Test ServiceError creation with proper attributes"""
        error = ServiceError(
            message="Test error",
            error_code="TEST_ERROR",
            status_code=400,
            details={"field": "test"}
        )

        assert error.message == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.status_code == 400
        assert error.details == {"field": "test"}

    def test_validation_error_creation(self):
        """Test ValidationError creation"""
        error = ValidationError("Invalid data", field="name")

        assert error.message == "Invalid data"
        assert error.error_code == "VALIDATION_ERROR"
        assert error.status_code == 422
        assert error.details["field"] == "name"

    def test_not_found_error_creation(self):
        """Test NotFoundError creation"""
        error = NotFoundError("User", 123)

        assert "User not found: 123" in error.message
        assert error.error_code == "NOT_FOUND"
        assert error.status_code == 404
        assert error.details["resource"] == "User"
        assert error.details["identifier"] == "123"

    def test_conflict_error_creation(self):
        """Test ConflictError creation"""
        error = ConflictError("Resource already exists")

        assert error.message == "Resource already exists"
        assert error.error_code == "CONFLICT"
        assert error.status_code == 409


@pytest.mark.asyncio
class TestServiceIntegration:
    """Test service integration and business logic"""

    async def test_simple_service_operation(self, test_service):
        """Test basic service operation"""
        data = {"name": "test_item", "description": "Test description"}
        result = await test_service.create_test_item(data)

        assert result["id"] == 1
        assert result["name"] == "test_item"
        assert result["description"] == "Test description"

    async def test_service_validation_error(self, test_service):
        """Test service validation error handling"""
        with pytest.raises(ValidationError) as exc_info:
            await test_service.create_test_item({})

        assert "Name is required" in str(exc_info.value)

    async def test_service_not_found_error(self, test_service):
        """Test service not found error handling"""
        with pytest.raises(NotFoundError) as exc_info:
            await test_service.get_test_item(999)

        assert "TestItem not found: 999" in str(exc_info.value)

    async def test_service_conflict_error(self, test_service):
        """Test service conflict error handling"""
        # Create an item first
        await test_service.create_test_item({"name": "test_item"})

        # Try to update with conflicting data
        with pytest.raises(ConflictError) as exc_info:
            await test_service.update_test_item(1, {"name": "conflict"})

        assert "Name conflicts with existing item" in str(exc_info.value)

    async def test_dependent_service_operation(self, dependent_service):
        """Test dependent service operation"""
        data = {"name": "dependent_item", "description": "Test dependent item"}
        result = await dependent_service.create_dependent_item(data)

        assert result["id"] == 1
        assert result["name"] == "dependent_item"
        assert result["dependent_field"] == "dependent_value"
        assert result["created_by"] == "dependent_service"

    async def test_service_error_propagation(self, dependent_service):
        """Test error propagation through service layers"""
        # Test validation error propagation
        with pytest.raises(ValidationError):
            await dependent_service.create_dependent_item({})


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
