# Culture Connect Backend - Kubernetes Services
# Service definitions for application components

apiVersion: v1
kind: Service
metadata:
  name: culture-connect-api
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: api
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-west-2:ACCOUNT_ID:certificate/CERT_ID"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: nginx-http
    port: 80
    targetPort: nginx-http
    protocol: TCP
  - name: nginx-https
    port: 443
    targetPort: nginx-https
    protocol: TCP
  selector:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: api
  sessionAffinity: None

---
apiVersion: v1
kind: Service
metadata:
  name: culture-connect-api-headless
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: api-headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  selector:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: api

---
apiVersion: v1
kind: Service
metadata:
  name: culture-connect-celery-worker
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-worker
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9540"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: metrics
    port: 9540
    targetPort: 9540
    protocol: TCP
  selector:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-worker

---
# External service for managed PostgreSQL (RDS)
apiVersion: v1
kind: Service
metadata:
  name: culture-connect-postgres
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: postgres
spec:
  type: ExternalName
  externalName: culture-connect-production-postgres.cluster-xyz.us-west-2.rds.amazonaws.com
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP

---
# External service for managed Redis (ElastiCache)
apiVersion: v1
kind: Service
metadata:
  name: culture-connect-redis
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: redis
spec:
  type: ExternalName
  externalName: culture-connect-production-redis.xyz.cache.amazonaws.com
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP

---
# Service Monitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: culture-connect-api
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: api
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    honorLabels: true
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_node_name]
      targetLabel: node
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: namespace

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: culture-connect-celery-worker
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: celery-worker
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
