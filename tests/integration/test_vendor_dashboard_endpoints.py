"""
Integration Tests for Vendor Dashboard API Endpoints.

This module provides comprehensive integration tests for vendor dashboard API endpoints including:
- Dashboard overview endpoint testing with authentication
- Metrics calculation endpoint testing
- Activity feed management endpoint testing
- Notification system endpoint testing
- Quick actions workflow endpoint testing
- Analytics reporting endpoint testing

Implements integration testing with database rollback mechanisms,
authentication validation, and performance testing with <200ms GET targets.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.core.deps import get_db
from app.models.user import User
from app.models.vendor import Vendor
from app.models.vendor_dashboard import (
    VendorDashboardMetrics,
    VendorActivityFeed,
    VendorNotification,
    VendorQuickAction
)
from tests.conftest import (
    TestingSessionLocal,
    create_test_user,
    create_test_vendor,
    get_test_token_headers
)


class TestVendorDashboardEndpoints:
    """Integration test suite for vendor dashboard API endpoints."""

    @pytest.fixture
    async def db_session(self):
        """Create test database session with rollback."""
        async with TestingSessionLocal() as session:
            yield session
            await session.rollback()

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Create test user."""
        return await create_test_user(
            db_session,
            email="<EMAIL>",
            password="testpass123",
            role="vendor"
        )

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession, test_user: User):
        """Create test vendor."""
        return await create_test_vendor(
            db_session,
            user_id=test_user.id,
            business_name="Test Cultural Vendor",
            business_type="photography"
        )

    @pytest.fixture
    async def auth_headers(self, test_user: User):
        """Get authentication headers for test user."""
        return await get_test_token_headers(test_user)

    @pytest.fixture
    async def sample_metrics(self, db_session: AsyncSession, test_vendor: Vendor):
        """Create sample metrics data."""
        metrics = [
            VendorDashboardMetrics(
                vendor_id=test_vendor.id,
                metric_type="revenue",
                metric_name="total_revenue",
                metric_value=Decimal("150000.00"),
                previous_value=Decimal("120000.00"),
                percentage_change=25.0,
                period_start=datetime.now(timezone.utc) - timedelta(days=30),
                period_end=datetime.now(timezone.utc),
                is_trending_up=True,
                trend_direction="up"
            ),
            VendorDashboardMetrics(
                vendor_id=test_vendor.id,
                metric_type="bookings",
                metric_name="total_bookings",
                metric_value=Decimal("45"),
                previous_value=Decimal("38"),
                percentage_change=18.4,
                period_start=datetime.now(timezone.utc) - timedelta(days=30),
                period_end=datetime.now(timezone.utc),
                is_trending_up=True,
                trend_direction="up"
            )
        ]
        
        for metric in metrics:
            db_session.add(metric)
        await db_session.commit()
        return metrics

    @pytest.mark.asyncio
    async def test_get_dashboard_overview_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict,
        sample_metrics: list
    ):
        """Test successful dashboard overview retrieval."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            start_time = datetime.now(timezone.utc)
            response = await client.get(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/overview",
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            # Performance validation: <200ms for GET operations
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            assert execution_time_ms < 200
            
            data = response.json()
            assert data["vendor_id"] == test_vendor.id
            assert "metrics" in data
            assert "recent_activities" in data
            assert "notifications" in data
            assert "quick_actions" in data
            assert len(data["metrics"]) == 2
            
            # Verify metrics data
            revenue_metric = next(m for m in data["metrics"] if m["metric_type"] == "revenue")
            assert revenue_metric["metric_value"] == "150000.00"
            assert revenue_metric["percentage_change"] == 25.0
            assert revenue_metric["is_trending_up"] is True
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_get_dashboard_overview_unauthorized(self, db_session: AsyncSession, test_vendor: Vendor):
        """Test dashboard overview access without authentication."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.get(f"/api/v1/vendor-dashboard/{test_vendor.id}/overview")
            
            # Assert
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_get_dashboard_overview_vendor_not_found(
        self,
        db_session: AsyncSession,
        auth_headers: dict
    ):
        """Test dashboard overview for non-existent vendor."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.get(
                "/api/v1/vendor-dashboard/999/overview",
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_404_NOT_FOUND
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_calculate_metrics_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful metrics calculation."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare request data
            request_data = {
                "period_start": (datetime.now(timezone.utc) - timedelta(days=30)).isoformat(),
                "period_end": datetime.now(timezone.utc).isoformat(),
                "metric_types": ["revenue", "bookings", "customers"]
            }
            
            # Act
            start_time = datetime.now(timezone.utc)
            response = await client.post(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/metrics/calculate",
                json=request_data,
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            # Performance validation: <500ms for POST operations
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            assert execution_time_ms < 500
            
            data = response.json()
            assert "metrics" in data
            assert "calculation_time" in data
            assert "period_start" in data
            assert "period_end" in data
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_create_activity_feed_entry_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful activity feed entry creation."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare request data
            request_data = {
                "activity_type": "booking_received",
                "title": "New booking received",
                "description": "Photography session booked for tomorrow",
                "entity_type": "booking",
                "entity_id": 123,
                "is_important": True
            }
            
            # Act
            response = await client.post(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/activities",
                json=request_data,
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_201_CREATED
            
            data = response.json()
            assert data["vendor_id"] == test_vendor.id
            assert data["activity_type"] == "booking_received"
            assert data["title"] == "New booking received"
            assert data["is_important"] is True
            assert "created_at" in data
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_send_notification_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful notification sending."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare request data
            request_data = {
                "notification_type": "booking_request",
                "priority": "high",
                "title": "New Booking Request",
                "message": "You have a new booking request for photography session",
                "action_url": "/bookings/123",
                "action_text": "View Booking"
            }
            
            # Act
            response = await client.post(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/notifications",
                json=request_data,
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_201_CREATED
            
            data = response.json()
            assert data["vendor_id"] == test_vendor.id
            assert data["notification_type"] == "booking_request"
            assert data["priority"] == "high"
            assert data["title"] == "New Booking Request"
            assert "created_at" in data
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_create_quick_action_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful quick action creation."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare request data
            request_data = {
                "action_type": "complete_profile",
                "title": "Complete Your Profile",
                "description": "Add more details to improve your visibility",
                "action_url": "/profile/edit",
                "priority_score": 8.5,
                "estimated_time_minutes": 15,
                "potential_impact": "high"
            }
            
            # Act
            response = await client.post(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/quick-actions",
                json=request_data,
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_201_CREATED
            
            data = response.json()
            assert data["vendor_id"] == test_vendor.id
            assert data["action_type"] == "complete_profile"
            assert data["priority_score"] == 8.5
            assert data["estimated_time_minutes"] == 15
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_mark_notification_as_read_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful notification marking as read."""
        # Create test notification
        notification = VendorNotification(
            vendor_id=test_vendor.id,
            notification_type="booking_request",
            priority="medium",
            title="Test Notification",
            message="Test message",
            is_read=False
        )
        db_session.add(notification)
        await db_session.commit()
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.patch(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/notifications/{notification.id}/read",
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "Notification marked as read"
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_complete_quick_action_success(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test successful quick action completion."""
        # Create test quick action
        quick_action = VendorQuickAction(
            vendor_id=test_vendor.id,
            action_type="complete_profile",
            title="Complete Profile",
            action_url="/profile/edit",
            priority_score=8.0,
            is_completed=False
        )
        db_session.add(quick_action)
        await db_session.commit()
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.patch(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/quick-actions/{quick_action.id}/complete",
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "Quick action completed"
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_endpoint_performance_targets(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        auth_headers: dict
    ):
        """Test that all endpoints meet performance targets."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test GET endpoint performance (<200ms)
            start_time = datetime.now(timezone.utc)
            response = await client.get(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/overview",
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            get_time_ms = (end_time - start_time).total_seconds() * 1000
            assert response.status_code == status.HTTP_200_OK
            assert get_time_ms < 200  # Target: <200ms for GET operations
            
            # Test POST endpoint performance (<500ms)
            request_data = {
                "activity_type": "booking_received",
                "title": "Performance test activity",
                "description": "Testing performance"
            }
            
            start_time = datetime.now(timezone.utc)
            response = await client.post(
                f"/api/v1/vendor-dashboard/{test_vendor.id}/activities",
                json=request_data,
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            post_time_ms = (end_time - start_time).total_seconds() * 1000
            assert response.status_code == status.HTTP_201_CREATED
            assert post_time_ms < 500  # Target: <500ms for POST operations
            
            # Clean up
            app.dependency_overrides.clear()
