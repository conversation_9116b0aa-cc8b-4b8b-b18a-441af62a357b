#!/usr/bin/env python3
"""
MaxMind GeoIP database verification script for Culture Connect Backend.

This script provides comprehensive verification of MaxMind GeoLite2-Country database
including:
- Database file integrity and format validation
- Functional testing with known IP addresses
- Performance benchmarking and validation
- Integration with existing GeolocationService

Features:
- Production-grade error handling and logging
- Performance validation (<100ms detection target)
- Comprehensive test coverage with known IP addresses
- Integration with Culture Connect Backend infrastructure
"""

import sys
import os
import time
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Add app directory to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    import geoip2.database
    import geoip2.errors
    from app.core.config import settings
    from app.services.geolocation_service import GeolocationService, GeolocationResult
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    print("Please ensure all dependencies are installed and app directory is accessible")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/geoip/verify_geoip.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)

@dataclass
class VerificationResult:
    """Result of database verification."""
    success: bool
    message: str
    details: Dict[str, any]
    performance_ms: Optional[float] = None

class GeoIPDatabaseVerifier:
    """
    Comprehensive MaxMind GeoIP database verifier.
    
    Provides thorough validation of database integrity, functionality,
    and performance for production deployment.
    """
    
    def __init__(self, database_path: Optional[str] = None):
        """Initialize verifier with database path."""
        self.database_path = database_path or getattr(settings, 'GEOIP_DATABASE_PATH', './data/GeoLite2-Country.mmdb')
        self.reader = None
        self.geolocation_service = None
        
        # Test IP addresses with known countries
        self.test_ips = {
            "*******": {"country": "US", "name": "United States"},  # Google DNS
            "*******": {"country": "US", "name": "United States"},  # Cloudflare DNS
            "**************": {"country": "US", "name": "United States"},  # OpenDNS
            "************": {"country": "NG", "name": "Nigeria"},  # Nigerian IP
            "***********": {"country": "ZA", "name": "South Africa"},  # South African IP
            "************": {"country": "EG", "name": "Egypt"},  # Egyptian IP
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "max_detection_time_ms": 100,  # Maximum detection time
            "avg_detection_time_ms": 50,   # Average detection time target
            "min_success_rate": 0.95       # Minimum success rate
        }
    
    def verify_file_integrity(self) -> VerificationResult:
        """Verify database file integrity and format."""
        logger.info(f"Verifying database file integrity: {self.database_path}")
        
        try:
            # Check if file exists
            if not os.path.exists(self.database_path):
                return VerificationResult(
                    success=False,
                    message=f"Database file not found: {self.database_path}",
                    details={"file_exists": False}
                )
            
            # Check file size (should be > 1MB for GeoLite2-Country)
            file_size = os.path.getsize(self.database_path)
            if file_size < 1024 * 1024:  # 1MB minimum
                return VerificationResult(
                    success=False,
                    message=f"Database file too small: {file_size} bytes",
                    details={"file_size": file_size, "min_size": 1024 * 1024}
                )
            
            # Try to open database
            try:
                with geoip2.database.Reader(self.database_path) as reader:
                    # Verify it's a Country database
                    metadata = reader.metadata()
                    if 'Country' not in metadata.database_type:
                        return VerificationResult(
                            success=False,
                            message=f"Invalid database type: {metadata.database_type}",
                            details={"database_type": metadata.database_type}
                        )
                    
                    return VerificationResult(
                        success=True,
                        message="Database file integrity verified",
                        details={
                            "file_size": file_size,
                            "database_type": metadata.database_type,
                            "build_epoch": metadata.build_epoch,
                            "description": metadata.description
                        }
                    )
            
            except geoip2.errors.InvalidDatabaseError as e:
                return VerificationResult(
                    success=False,
                    message=f"Invalid database format: {str(e)}",
                    details={"error": str(e)}
                )
        
        except Exception as e:
            return VerificationResult(
                success=False,
                message=f"File integrity check failed: {str(e)}",
                details={"error": str(e)}
            )
    
    def verify_functionality(self) -> VerificationResult:
        """Verify database functionality with known IP addresses."""
        logger.info("Verifying database functionality with test IP addresses")
        
        try:
            with geoip2.database.Reader(self.database_path) as reader:
                self.reader = reader
                
                results = {}
                successful_lookups = 0
                total_lookups = len(self.test_ips)
                
                for ip, expected in self.test_ips.items():
                    try:
                        start_time = time.perf_counter()
                        response = reader.country(ip)
                        end_time = time.perf_counter()
                        
                        detection_time_ms = (end_time - start_time) * 1000
                        
                        # Verify country code matches expected
                        country_match = response.country.iso_code == expected["country"]
                        if country_match:
                            successful_lookups += 1
                        
                        results[ip] = {
                            "expected_country": expected["country"],
                            "detected_country": response.country.iso_code,
                            "country_name": response.country.name,
                            "continent": response.continent.code,
                            "match": country_match,
                            "detection_time_ms": detection_time_ms
                        }
                        
                        logger.debug(f"IP {ip}: {response.country.iso_code} ({detection_time_ms:.2f}ms)")
                    
                    except Exception as e:
                        results[ip] = {
                            "expected_country": expected["country"],
                            "error": str(e),
                            "match": False,
                            "detection_time_ms": None
                        }
                        logger.warning(f"Failed to lookup IP {ip}: {str(e)}")
                
                # Calculate success rate
                success_rate = successful_lookups / total_lookups
                
                if success_rate >= self.performance_thresholds["min_success_rate"]:
                    return VerificationResult(
                        success=True,
                        message=f"Functionality verification passed ({successful_lookups}/{total_lookups} successful)",
                        details={
                            "success_rate": success_rate,
                            "successful_lookups": successful_lookups,
                            "total_lookups": total_lookups,
                            "results": results
                        }
                    )
                else:
                    return VerificationResult(
                        success=False,
                        message=f"Functionality verification failed (success rate: {success_rate:.2%})",
                        details={
                            "success_rate": success_rate,
                            "successful_lookups": successful_lookups,
                            "total_lookups": total_lookups,
                            "results": results
                        }
                    )
        
        except Exception as e:
            return VerificationResult(
                success=False,
                message=f"Functionality verification failed: {str(e)}",
                details={"error": str(e)}
            )
    
    def verify_performance(self) -> VerificationResult:
        """Verify database performance meets requirements."""
        logger.info("Verifying database performance")
        
        try:
            with geoip2.database.Reader(self.database_path) as reader:
                detection_times = []
                
                # Perform multiple lookups for performance testing
                test_cycles = 10
                for cycle in range(test_cycles):
                    for ip in self.test_ips.keys():
                        try:
                            start_time = time.perf_counter()
                            reader.country(ip)
                            end_time = time.perf_counter()
                            
                            detection_time_ms = (end_time - start_time) * 1000
                            detection_times.append(detection_time_ms)
                        
                        except Exception:
                            # Skip failed lookups for performance testing
                            continue
                
                if not detection_times:
                    return VerificationResult(
                        success=False,
                        message="No successful lookups for performance testing",
                        details={}
                    )
                
                # Calculate performance metrics
                avg_time = sum(detection_times) / len(detection_times)
                max_time = max(detection_times)
                min_time = min(detection_times)
                
                # Check performance thresholds
                avg_threshold_met = avg_time <= self.performance_thresholds["avg_detection_time_ms"]
                max_threshold_met = max_time <= self.performance_thresholds["max_detection_time_ms"]
                
                performance_passed = avg_threshold_met and max_threshold_met
                
                return VerificationResult(
                    success=performance_passed,
                    message=f"Performance verification {'passed' if performance_passed else 'failed'}",
                    details={
                        "avg_detection_time_ms": avg_time,
                        "max_detection_time_ms": max_time,
                        "min_detection_time_ms": min_time,
                        "total_tests": len(detection_times),
                        "avg_threshold_met": avg_threshold_met,
                        "max_threshold_met": max_threshold_met,
                        "thresholds": self.performance_thresholds
                    },
                    performance_ms=avg_time
                )
        
        except Exception as e:
            return VerificationResult(
                success=False,
                message=f"Performance verification failed: {str(e)}",
                details={"error": str(e)}
            )
    
    async def verify_service_integration(self) -> VerificationResult:
        """Verify integration with GeolocationService."""
        logger.info("Verifying GeolocationService integration")
        
        try:
            # Initialize GeolocationService
            self.geolocation_service = GeolocationService()
            
            # Test service functionality
            test_ip = "*******"
            start_time = time.perf_counter()
            result = await self.geolocation_service.detect_country_from_ip(test_ip)
            end_time = time.perf_counter()
            
            detection_time_ms = (end_time - start_time) * 1000
            
            if isinstance(result, GeolocationResult) and result.country_code:
                return VerificationResult(
                    success=True,
                    message="GeolocationService integration verified",
                    details={
                        "test_ip": test_ip,
                        "detected_country": result.country_code,
                        "detection_method": result.detection_method,
                        "confidence_score": result.confidence_score,
                        "detection_time_ms": detection_time_ms
                    },
                    performance_ms=detection_time_ms
                )
            else:
                return VerificationResult(
                    success=False,
                    message="GeolocationService returned invalid result",
                    details={"result": str(result)}
                )
        
        except Exception as e:
            return VerificationResult(
                success=False,
                message=f"Service integration verification failed: {str(e)}",
                details={"error": str(e)}
            )
    
    def run_verification(self) -> bool:
        """Run complete database verification."""
        logger.info("Starting comprehensive GeoIP database verification")
        
        verification_steps = [
            ("File Integrity", self.verify_file_integrity),
            ("Functionality", self.verify_functionality),
            ("Performance", self.verify_performance),
        ]
        
        all_passed = True
        results = {}
        
        # Run synchronous verifications
        for step_name, step_func in verification_steps:
            logger.info(f"Running {step_name} verification...")
            result = step_func()
            results[step_name] = result
            
            if result.success:
                logger.info(f"✅ {step_name}: {result.message}")
            else:
                logger.error(f"❌ {step_name}: {result.message}")
                all_passed = False
        
        # Run async service integration verification
        try:
            logger.info("Running Service Integration verification...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            service_result = loop.run_until_complete(self.verify_service_integration())
            loop.close()
            
            results["Service Integration"] = service_result
            
            if service_result.success:
                logger.info(f"✅ Service Integration: {service_result.message}")
            else:
                logger.error(f"❌ Service Integration: {service_result.message}")
                all_passed = False
        
        except Exception as e:
            logger.error(f"❌ Service Integration: Failed to run verification: {str(e)}")
            all_passed = False
        
        # Print summary
        print("\n" + "="*70)
        print("  MaxMind GeoIP Database Verification Summary")
        print("="*70)
        
        for step_name, result in results.items():
            status = "✅ PASS" if result.success else "❌ FAIL"
            print(f"{status} {step_name}: {result.message}")
            
            if result.performance_ms:
                print(f"     Performance: {result.performance_ms:.2f}ms")
        
        print("="*70)
        
        if all_passed:
            print("🎉 All verifications PASSED - Database is ready for production!")
            logger.info("Database verification completed successfully")
            return True
        else:
            print("⚠️  Some verifications FAILED - Please check the issues above")
            logger.error("Database verification failed")
            return False

def main():
    """Main execution function."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs/geoip', exist_ok=True)
    
    # Parse command line arguments
    database_path = sys.argv[1] if len(sys.argv) > 1 else None
    
    # Run verification
    verifier = GeoIPDatabaseVerifier(database_path)
    success = verifier.run_verification()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
