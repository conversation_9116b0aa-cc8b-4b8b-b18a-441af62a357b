"""
Enhanced Authentication schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic schemas for authentication-related operations
implementing Task 1.3.3 requirements:

Features:
- Advanced validation patterns with custom validators
- Serialization optimization for performance
- API versioning support with backward compatibility
- Structured error handling and response formatting
- Integration with repository layer and service architecture
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from uuid import UUID
import re

from pydantic import BaseModel, EmailStr, Field, field_validator, model_validator

from app.core.security import UserRole
from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin, MetadataMixin
from app.schemas.versioning import VersionedSchema, VersionCompatibilityMixin
from app.schemas.errors import <PERSON>rrorCode, ErrorDetail, ValidationErrorResponse


class UserBase(BaseSchema, TimestampMixin):
    """Base user schema with common fields."""

    email: EmailStr = Field(..., description="User email address")
    first_name: str = Field(..., min_length=1, max_length=50, description="User first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="User last name")
    phone_number: Optional[str] = Field(None, max_length=20, description="User phone number")

    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v):
        """Validate phone number format."""
        if v is not None:
            # Remove spaces and common separators
            cleaned = ''.join(c for c in v if c.isdigit() or c == '+')
            if not cleaned.startswith('+'):
                cleaned = '+' + cleaned
            # Basic validation for international format
            if len(cleaned) < 8 or len(cleaned) > 16:
                raise ValueError('Phone number must be between 8 and 16 digits')
        return v


class UserCreate(VersionedSchema, VersionCompatibilityMixin, UserBase):
    """
    Enhanced schema for user registration with comprehensive validation.

    Implements Task 1.3.3 requirements:
    - Advanced validation patterns with custom validators
    - Password strength validation
    - API versioning support
    - Structured error handling
    """

    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="User password (min 8 characters with complexity requirements)"
    )
    confirm_password: str = Field(..., description="Password confirmation")
    role: Optional[str] = Field(
        default=UserRole.CUSTOMER,
        description="User role"
    )
    terms_accepted: bool = Field(..., description="Terms and conditions acceptance")

    # Enhanced profile fields
    country_code: Optional[str] = Field(
        default=None,
        pattern=r'^[A-Z]{2}$',
        min_length=2,
        max_length=2,
        description="ISO 3166-1 alpha-2 country code"
    )
    preferred_currency: Optional[str] = Field(
        default="NGN",
        pattern=r'^[A-Z]{3}$',
        min_length=3,
        max_length=3,
        description="Preferred currency code"
    )
    date_of_birth: Optional[datetime] = Field(
        default=None,
        description="Date of birth (must be 13+ years old)"
    )
    marketing_consent: bool = Field(
        default=False,
        description="Marketing communications consent"
    )

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Enhanced email validation with disposable email detection."""
        v = v.lower().strip()

        # Check for common disposable email domains
        disposable_domains = [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email', 'temp-mail.org'
        ]
        domain = v.split('@')[1] if '@' in v else ''

        if domain in disposable_domains:
            raise ValueError('Disposable email addresses are not allowed')

        return v

    @field_validator('password')
    @classmethod
    def validate_password_strength(cls, v: str) -> str:
        """Comprehensive password strength validation."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')

        # Check for required character types
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v)

        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                'Password must contain at least one uppercase letter, '
                'one lowercase letter, one digit, and one special character'
            )

        # Check for common weak patterns
        weak_patterns = ['123456', 'password', 'qwerty', 'abc123']
        if any(pattern in v.lower() for pattern in weak_patterns):
            raise ValueError('Password contains common weak patterns')

        return v

    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v: Optional[str]) -> Optional[str]:
        """Enhanced phone number validation."""
        if v is None:
            return v

        # Remove spaces and common separators
        cleaned = ''.join(c for c in v if c.isdigit() or c == '+')

        # Ensure international format
        if not cleaned.startswith('+'):
            cleaned = '+' + cleaned

        # Validate length and format
        if len(cleaned) < 8 or len(cleaned) > 16:
            raise ValueError('Phone number must be between 8 and 16 digits')

        # Basic format validation
        if not cleaned[1:].isdigit():
            raise ValueError('Phone number must contain only digits after country code')

        return cleaned

    @field_validator('date_of_birth')
    @classmethod
    def validate_age(cls, v: Optional[datetime]) -> Optional[datetime]:
        """Validate minimum age requirement."""
        if v is None:
            return v

        today = datetime.now(timezone.utc).date()
        age = today.year - v.date().year - ((today.month, today.day) < (v.date().month, v.date().day))

        if age < 13:
            raise ValueError('Users must be at least 13 years old')

        if age > 120:
            raise ValueError('Invalid date of birth')

        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v: str) -> str:
        """Validate user role."""
        allowed_roles = [UserRole.CUSTOMER, UserRole.VENDOR]
        if v not in allowed_roles:
            raise ValueError(f'Role must be one of: {allowed_roles}')
        return v

    @model_validator(mode='after')
    def validate_passwords_match(self) -> 'UserCreate':
        """Validate that passwords match."""
        if self.password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self

    @model_validator(mode='after')
    def validate_terms_acceptance(self) -> 'UserCreate':
        """Validate terms acceptance."""
        if not self.terms_accepted:
            raise ValueError('Terms and conditions must be accepted')
        return self


class UserLogin(BaseModel):
    """Schema for user login."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    remember_me: bool = Field(default=False, description="Remember login session")


class UserResponse(VersionedSchema, UUIDMixin, TimestampMixin, MetadataMixin):
    """
    Enhanced schema for user response data with serialization optimization.

    Implements Task 1.3.3 requirements:
    - Optimized serialization for performance
    - API versioning support
    - Comprehensive user data representation
    """

    id: int = Field(..., description="User ID")
    email: EmailStr = Field(..., description="User email address")
    first_name: str = Field(..., description="User first name")
    last_name: str = Field(..., description="User last name")
    phone_number: Optional[str] = Field(None, description="User phone number")
    role: str = Field(..., description="User role")
    is_active: bool = Field(..., description="User active status")
    is_verified: bool = Field(..., description="User verification status")
    vendor_id: Optional[int] = Field(None, description="Associated vendor ID")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

    # Enhanced profile fields
    country_code: Optional[str] = Field(None, description="Country code")
    preferred_currency: Optional[str] = Field(None, description="Preferred currency")
    date_of_birth: Optional[datetime] = Field(None, description="Date of birth")
    marketing_consent: bool = Field(default=False, description="Marketing consent")

    # Computed fields
    full_name: str = Field(..., description="Full name")
    profile_completion: float = Field(..., ge=0.0, le=100.0, description="Profile completion percentage")

    # Statistics
    total_bookings: int = Field(default=0, ge=0, description="Total bookings count")
    total_spent: float = Field(default=0.0, ge=0.0, description="Total amount spent")

    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        },
        "populate_by_name": True,
        "validate_assignment": True
    }


class UserUpdate(BaseModel):
    """Schema for user profile updates."""

    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    phone_number: Optional[str] = Field(None, max_length=20)

    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v):
        """Validate phone number format."""
        if v is not None:
            # Remove spaces and common separators
            cleaned = ''.join(c for c in v if c.isdigit() or c == '+')
            if not cleaned.startswith('+'):
                cleaned = '+' + cleaned
            # Basic validation for international format
            if len(cleaned) < 8 or len(cleaned) > 16:
                raise ValueError('Phone number must be between 8 and 16 digits')
        return v


class TokenResponse(BaseModel):
    """Schema for authentication token response."""

    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenRefresh(BaseModel):
    """Schema for token refresh request."""

    refresh_token: str = Field(..., description="Valid refresh token")


class PasswordReset(BaseModel):
    """Schema for password reset."""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="New password"
    )
    confirm_password: str = Field(..., description="Password confirmation")

    @model_validator(mode='after')
    def passwords_match(self) -> 'PasswordReset':
        """Validate that passwords match."""
        if self.new_password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self


class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""

    email: EmailStr = Field(..., description="User email address")


class PasswordChange(BaseModel):
    """Schema for password change."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="New password"
    )
    confirm_password: str = Field(..., description="Password confirmation")

    @model_validator(mode='after')
    def passwords_match(self) -> 'PasswordChange':
        """Validate that passwords match."""
        if self.new_password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self


class EmailVerification(BaseModel):
    """Schema for email verification."""

    token: str = Field(..., description="Email verification token")


class LoginResponse(BaseModel):
    """Schema for login response."""

    user: UserResponse = Field(..., description="User information")
    tokens: TokenResponse = Field(..., description="Authentication tokens")
    message: str = Field(default="Login successful", description="Response message")


class LogoutRequest(BaseModel):
    """Schema for logout request."""

    refresh_token: Optional[str] = Field(None, description="Refresh token to invalidate")


class UserPermissions(BaseModel):
    """Schema for user permissions."""

    permissions: List[str] = Field(..., description="List of user permissions")
    role: str = Field(..., description="User role")


class AuthStatus(BaseModel):
    """Schema for authentication status."""

    is_authenticated: bool = Field(..., description="Authentication status")
    user: Optional[UserResponse] = Field(None, description="User information if authenticated")
    permissions: Optional[List[str]] = Field(None, description="User permissions")


class APIKeyCreate(BaseModel):
    """Schema for API key creation."""

    name: str = Field(..., min_length=1, max_length=100, description="API key name")
    description: Optional[str] = Field(None, max_length=500, description="API key description")
    scopes: List[str] = Field(default=[], description="API key scopes")
    expires_at: Optional[datetime] = Field(None, description="API key expiration")


class APIKeyResponse(BaseModel):
    """Schema for API key response."""

    id: int = Field(..., description="API key ID")
    name: str = Field(..., description="API key name")
    key: str = Field(..., description="API key value")
    description: Optional[str] = Field(None, description="API key description")
    scopes: List[str] = Field(..., description="API key scopes")
    is_active: bool = Field(..., description="API key active status")
    created_at: datetime = Field(..., description="Creation timestamp")
    expires_at: Optional[datetime] = Field(None, description="Expiration timestamp")
    last_used: Optional[datetime] = Field(None, description="Last used timestamp")

    class Config:
        from_attributes = True


class SessionInfo(BaseModel):
    """Schema for session information."""

    session_id: str = Field(..., description="Session identifier")
    user_id: int = Field(..., description="User ID")
    ip_address: str = Field(..., description="Client IP address")
    user_agent: str = Field(..., description="Client user agent")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    is_active: bool = Field(..., description="Session active status")


class SecurityEvent(BaseModel):
    """Schema for security events."""

    event_type: str = Field(..., description="Type of security event")
    user_id: Optional[int] = Field(None, description="Associated user ID")
    ip_address: str = Field(..., description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")
    details: Optional[dict] = Field(None, description="Additional event details")
    timestamp: datetime = Field(..., description="Event timestamp")
    severity: str = Field(..., description="Event severity level")


# Response models for API documentation
class MessageResponse(BaseModel):
    """Generic message response schema."""

    message: str = Field(..., description="Response message")
    success: bool = Field(default=True, description="Operation success status")


class ErrorResponse(BaseModel):
    """Error response schema."""

    detail: str = Field(..., description="Error detail message")
    error_code: Optional[str] = Field(None, description="Error code")
    timestamp: datetime = Field(default=None, description="Error timestamp")


# Export all schemas
__all__ = [
    "UserBase",
    "UserCreate",
    "UserLogin",
    "UserResponse",
    "UserUpdate",
    "TokenResponse",
    "TokenRefresh",
    "PasswordReset",
    "PasswordResetRequest",
    "PasswordChange",
    "EmailVerification",
    "LoginResponse",
    "LogoutRequest",
    "UserPermissions",
    "AuthStatus",
    "APIKeyCreate",
    "APIKeyResponse",
    "SessionInfo",
    "SecurityEvent",
    "MessageResponse",
    "ErrorResponse",
]
