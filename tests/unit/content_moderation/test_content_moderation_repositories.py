"""
Tests for Content Moderation repositories.

This module tests the comprehensive content moderation repository layer including:
- ContentModerationWorkflowRepository async operations and queries
- ContentQualityScoreRepository quality analytics and trends
- PlagiarismCheckRepository plagiarism detection and statistics
- ContentModerationRuleRepository rule management and effectiveness
- ContentApprovalHistoryRepository audit trail and performance metrics

Implements Task 3.2.4 testing requirements with >80% coverage.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import AsyncMock, patch

from app.repositories.content_moderation_repositories import (
    ContentModerationWorkflowRepository, ContentQualityScoreRepository,
    PlagiarismCheckRepository, ContentModerationRuleRepository,
    ContentApprovalHistoryRepository
)
from app.models.content_moderation import (
    ContentModerationWorkflow, ContentQualityScore, PlagiarismCheck,
    ContentModerationRule, ContentApprovalHistory,
    ContentStatus, ContentType, ModerationAction, PlagiarismStatus
)
from app.schemas.content_moderation import (
    ContentModerationWorkflowCreate, ContentQualityScoreCreate,
    PlagiarismCheckCreate, ContentModerationRuleCreate,
    ContentApprovalHistoryCreate
)


@pytest.fixture
async def workflow_repository(async_session):
    """Create ContentModerationWorkflowRepository fixture."""
    return ContentModerationWorkflowRepository(async_session)


@pytest.fixture
async def quality_repository(async_session):
    """Create ContentQualityScoreRepository fixture."""
    return ContentQualityScoreRepository(async_session)


@pytest.fixture
async def plagiarism_repository(async_session):
    """Create PlagiarismCheckRepository fixture."""
    return PlagiarismCheckRepository(async_session)


@pytest.fixture
async def rule_repository(async_session):
    """Create ContentModerationRuleRepository fixture."""
    return ContentModerationRuleRepository(async_session)


@pytest.fixture
async def history_repository(async_session):
    """Create ContentApprovalHistoryRepository fixture."""
    return ContentApprovalHistoryRepository(async_session)


class TestContentModerationWorkflowRepository:
    """Test ContentModerationWorkflowRepository."""

    async def test_create_workflow(self, workflow_repository):
        """Test creating a new workflow."""
        workflow_data = ContentModerationWorkflowCreate(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4(),
            priority=8,
            content_title="Test Service",
            content_summary="Test service description"
        )
        
        workflow = await workflow_repository.create(workflow_data)
        
        assert workflow.id is not None
        assert workflow.content_type == ContentType.SERVICE_LISTING
        assert workflow.priority == 8
        assert workflow.status == ContentStatus.PENDING

    async def test_get_by_content(self, workflow_repository):
        """Test getting workflow by content type and ID."""
        content_id = uuid4()
        workflow_data = ContentModerationWorkflowCreate(
            content_type=ContentType.SERVICE_LISTING,
            content_id=content_id,
            vendor_id=uuid4()
        )
        
        created_workflow = await workflow_repository.create(workflow_data)
        
        # Test retrieval
        retrieved_workflow = await workflow_repository.get_by_content(
            ContentType.SERVICE_LISTING, content_id
        )
        
        assert retrieved_workflow is not None
        assert retrieved_workflow.id == created_workflow.id
        assert retrieved_workflow.content_id == content_id

    async def test_get_by_content_not_found(self, workflow_repository):
        """Test getting workflow by content when not found."""
        result = await workflow_repository.get_by_content(
            ContentType.SERVICE_LISTING, uuid4()
        )
        
        assert result is None

    async def test_get_pending_reviews(self, workflow_repository):
        """Test getting pending reviews."""
        # Create test workflows
        workflows = []
        for i in range(3):
            workflow_data = ContentModerationWorkflowCreate(
                content_type=ContentType.SERVICE_LISTING,
                content_id=uuid4(),
                vendor_id=uuid4(),
                priority=i + 1
            )
            workflow = await workflow_repository.create(workflow_data)
            workflows.append(workflow)
        
        # Get pending reviews
        pending = await workflow_repository.get_pending_reviews(limit=10)
        
        assert len(pending) >= 3
        # Should be ordered by priority (descending) then submission time
        assert pending[0].priority >= pending[1].priority

    async def test_get_overdue_reviews(self, workflow_repository):
        """Test getting overdue reviews."""
        # Create overdue workflow
        workflow_data = ContentModerationWorkflowCreate(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4()
        )
        workflow = await workflow_repository.create(workflow_data)
        
        # Manually set deadline to past
        workflow.auto_approval_deadline = datetime.utcnow() - timedelta(hours=1)
        await workflow_repository.session.commit()
        
        # Get overdue reviews
        overdue = await workflow_repository.get_overdue_reviews(hours_overdue=0)
        
        assert len(overdue) >= 1
        assert any(w.id == workflow.id for w in overdue)

    async def test_get_by_vendor(self, workflow_repository):
        """Test getting workflows by vendor."""
        vendor_id = uuid4()
        
        # Create workflows for vendor
        for i in range(2):
            workflow_data = ContentModerationWorkflowCreate(
                content_type=ContentType.SERVICE_LISTING,
                content_id=uuid4(),
                vendor_id=vendor_id
            )
            await workflow_repository.create(workflow_data)
        
        # Get workflows by vendor
        vendor_workflows = await workflow_repository.get_by_vendor(vendor_id)
        
        assert len(vendor_workflows) >= 2
        assert all(w.vendor_id == vendor_id for w in vendor_workflows)

    async def test_assign_reviewer(self, workflow_repository):
        """Test assigning reviewer to workflow."""
        workflow_data = ContentModerationWorkflowCreate(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4()
        )
        workflow = await workflow_repository.create(workflow_data)
        reviewer_id = uuid4()
        
        # Assign reviewer
        success = await workflow_repository.assign_reviewer(workflow.id, reviewer_id)
        
        assert success is True
        
        # Verify assignment
        updated_workflow = await workflow_repository.get(workflow.id)
        assert updated_workflow.assigned_reviewer_id == reviewer_id
        assert updated_workflow.status == ContentStatus.UNDER_REVIEW
        assert updated_workflow.review_started_at is not None

    async def test_update_scores(self, workflow_repository):
        """Test updating workflow scores."""
        workflow_data = ContentModerationWorkflowCreate(
            content_type=ContentType.SERVICE_LISTING,
            content_id=uuid4(),
            vendor_id=uuid4()
        )
        workflow = await workflow_repository.create(workflow_data)
        
        # Update scores
        success = await workflow_repository.update_scores(
            workflow.id,
            moderation_score=85.5,
            quality_score=92.3,
            plagiarism_score=8.7
        )
        
        assert success is True
        
        # Verify scores
        updated_workflow = await workflow_repository.get(workflow.id)
        assert updated_workflow.moderation_score == 85.5
        assert updated_workflow.quality_score == 92.3
        assert updated_workflow.plagiarism_score == 8.7

    async def test_get_workflow_statistics(self, workflow_repository):
        """Test getting workflow statistics."""
        # Create test workflows
        for i in range(3):
            workflow_data = ContentModerationWorkflowCreate(
                content_type=ContentType.SERVICE_LISTING,
                content_id=uuid4(),
                vendor_id=uuid4()
            )
            await workflow_repository.create(workflow_data)
        
        # Get statistics
        stats = await workflow_repository.get_workflow_statistics(days=30)
        
        assert "total_workflows" in stats
        assert "status_distribution" in stats
        assert "average_moderation_score" in stats
        assert "period_days" in stats
        assert stats["period_days"] == 30


class TestContentQualityScoreRepository:
    """Test ContentQualityScoreRepository."""

    async def test_create_quality_score(self, quality_repository):
        """Test creating quality score."""
        quality_data = ContentQualityScoreCreate(
            workflow_id=uuid4(),
            overall_score=87.5,
            weighted_score=89.2,
            quality_grade="B+",
            content_quality_score=85.0,
            image_quality_score=90.0
        )
        
        quality_score = await quality_repository.create(quality_data)
        
        assert quality_score.id is not None
        assert quality_score.overall_score == 87.5
        assert quality_score.quality_grade == "B+"

    async def test_get_by_workflow(self, quality_repository):
        """Test getting quality score by workflow."""
        workflow_id = uuid4()
        quality_data = ContentQualityScoreCreate(
            workflow_id=workflow_id,
            overall_score=85.0
        )
        
        created_score = await quality_repository.create(quality_data)
        
        # Test retrieval
        retrieved_score = await quality_repository.get_by_workflow(workflow_id)
        
        assert retrieved_score is not None
        assert retrieved_score.id == created_score.id
        assert retrieved_score.workflow_id == workflow_id

    async def test_get_quality_trends(self, quality_repository):
        """Test getting quality trends."""
        # Create test quality scores
        for i in range(3):
            quality_data = ContentQualityScoreCreate(
                workflow_id=uuid4(),
                overall_score=80.0 + i * 5,
                content_quality_score=75.0 + i * 5
            )
            await quality_repository.create(quality_data)
        
        # Get trends
        trends = await quality_repository.get_quality_trends(days=30)
        
        assert isinstance(trends, list)
        if trends:  # May be empty if no data in time range
            assert "date" in trends[0]
            assert "average_overall_score" in trends[0]


class TestPlagiarismCheckRepository:
    """Test PlagiarismCheckRepository."""

    async def test_create_plagiarism_check(self, plagiarism_repository):
        """Test creating plagiarism check."""
        plagiarism_data = PlagiarismCheckCreate(
            workflow_id=uuid4(),
            status=PlagiarismStatus.SIMILAR_FOUND,
            similarity_percentage=25.5,
            originality_score=74.5,
            content_hash="abc123def456"
        )
        
        plagiarism_check = await plagiarism_repository.create(plagiarism_data)
        
        assert plagiarism_check.id is not None
        assert plagiarism_check.status == PlagiarismStatus.SIMILAR_FOUND
        assert plagiarism_check.similarity_percentage == 25.5

    async def test_get_by_workflow(self, plagiarism_repository):
        """Test getting plagiarism check by workflow."""
        workflow_id = uuid4()
        plagiarism_data = PlagiarismCheckCreate(
            workflow_id=workflow_id,
            status=PlagiarismStatus.ORIGINAL,
            content_hash="test_hash"
        )
        
        created_check = await plagiarism_repository.create(plagiarism_data)
        
        # Test retrieval
        retrieved_check = await plagiarism_repository.get_by_workflow(workflow_id)
        
        assert retrieved_check is not None
        assert retrieved_check.id == created_check.id
        assert retrieved_check.workflow_id == workflow_id

    async def test_get_by_content_hash(self, plagiarism_repository):
        """Test getting plagiarism checks by content hash."""
        content_hash = "unique_hash_123"
        
        # Create multiple checks with same hash
        for i in range(2):
            plagiarism_data = PlagiarismCheckCreate(
                workflow_id=uuid4(),
                status=PlagiarismStatus.ORIGINAL,
                content_hash=content_hash
            )
            await plagiarism_repository.create(plagiarism_data)
        
        # Get by hash
        checks = await plagiarism_repository.get_by_content_hash(content_hash)
        
        assert len(checks) >= 2
        assert all(check.content_hash == content_hash for check in checks)

    async def test_get_high_similarity_content(self, plagiarism_repository):
        """Test getting high similarity content."""
        # Create high similarity check
        plagiarism_data = PlagiarismCheckCreate(
            workflow_id=uuid4(),
            status=PlagiarismStatus.SIMILAR_FOUND,
            similarity_percentage=85.0,
            content_hash="high_similarity_hash"
        )
        
        await plagiarism_repository.create(plagiarism_data)
        
        # Get high similarity content
        high_similarity = await plagiarism_repository.get_high_similarity_content(threshold=80.0)
        
        assert len(high_similarity) >= 1
        assert all(check.similarity_percentage >= 80.0 for check in high_similarity)

    async def test_get_plagiarism_statistics(self, plagiarism_repository):
        """Test getting plagiarism statistics."""
        # Create test plagiarism checks
        for i in range(3):
            plagiarism_data = PlagiarismCheckCreate(
                workflow_id=uuid4(),
                status=PlagiarismStatus.ORIGINAL,
                similarity_percentage=i * 10,
                content_hash=f"hash_{i}"
            )
            await plagiarism_repository.create(plagiarism_data)
        
        # Get statistics
        stats = await plagiarism_repository.get_plagiarism_statistics(days=30)
        
        assert "total_checks" in stats
        assert "status_distribution" in stats
        assert "average_similarity_percentage" in stats
        assert "period_days" in stats


class TestContentModerationRuleRepository:
    """Test ContentModerationRuleRepository."""

    async def test_create_moderation_rule(self, rule_repository):
        """Test creating moderation rule."""
        rule_data = ContentModerationRuleCreate(
            rule_name="Test Spam Rule",
            rule_category="spam_detection",
            detection_criteria={"keywords": ["spam"]},
            action_on_trigger=ModerationAction.FLAG,
            rule_logic={"conditions": [], "operator": "AND"}
        )
        
        rule = await rule_repository.create(rule_data)
        
        assert rule.id is not None
        assert rule.rule_name == "Test Spam Rule"
        assert rule.rule_category == "spam_detection"
        assert rule.is_active is True

    async def test_get_active_rules(self, rule_repository):
        """Test getting active rules."""
        # Create active rule
        rule_data = ContentModerationRuleCreate(
            rule_name="Active Rule",
            rule_category="test",
            detection_criteria={"test": True},
            action_on_trigger=ModerationAction.APPROVE,
            rule_logic={"conditions": [], "operator": "AND"},
            is_active=True
        )
        
        await rule_repository.create(rule_data)
        
        # Get active rules
        active_rules = await rule_repository.get_active_rules()
        
        assert len(active_rules) >= 1
        assert all(rule.is_active for rule in active_rules)

    async def test_get_by_category(self, rule_repository):
        """Test getting rules by category."""
        category = "test_category"
        
        # Create rule in category
        rule_data = ContentModerationRuleCreate(
            rule_name="Category Rule",
            rule_category=category,
            detection_criteria={"test": True},
            action_on_trigger=ModerationAction.APPROVE,
            rule_logic={"conditions": [], "operator": "AND"}
        )
        
        await rule_repository.create(rule_data)
        
        # Get by category
        category_rules = await rule_repository.get_by_category(category)
        
        assert len(category_rules) >= 1
        assert all(rule.rule_category == category for rule in category_rules)

    async def test_update_rule_performance(self, rule_repository):
        """Test updating rule performance."""
        rule_data = ContentModerationRuleCreate(
            rule_name="Performance Rule",
            rule_category="test",
            detection_criteria={"test": True},
            action_on_trigger=ModerationAction.APPROVE,
            rule_logic={"conditions": [], "operator": "AND"}
        )
        
        rule = await rule_repository.create(rule_data)
        
        # Update performance (successful detection)
        success = await rule_repository.update_rule_performance(rule.id, successful=True)
        
        assert success is True
        
        # Verify update
        updated_rule = await rule_repository.get(rule.id)
        assert updated_rule.total_applications == 1
        assert updated_rule.successful_detections == 1

    async def test_get_rule_effectiveness_report(self, rule_repository):
        """Test getting rule effectiveness report."""
        # Create test rule
        rule_data = ContentModerationRuleCreate(
            rule_name="Effectiveness Rule",
            rule_category="test",
            detection_criteria={"test": True},
            action_on_trigger=ModerationAction.APPROVE,
            rule_logic={"conditions": [], "operator": "AND"}
        )
        
        await rule_repository.create(rule_data)
        
        # Get effectiveness report
        report = await rule_repository.get_rule_effectiveness_report(days=30)
        
        assert isinstance(report, list)
        if report:  # May be empty if no recent activity
            assert "rule_id" in report[0]
            assert "effectiveness_score" in report[0]


class TestContentApprovalHistoryRepository:
    """Test ContentApprovalHistoryRepository."""

    async def test_create_approval_history(self, history_repository):
        """Test creating approval history."""
        history_data = ContentApprovalHistoryCreate(
            workflow_id=uuid4(),
            action_taken=ModerationAction.APPROVE,
            previous_status=ContentStatus.UNDER_REVIEW,
            new_status=ContentStatus.APPROVED,
            reviewer_id=uuid4(),
            confidence_score=95.0
        )
        
        history = await history_repository.create(history_data)
        
        assert history.id is not None
        assert history.action_taken == ModerationAction.APPROVE
        assert history.confidence_score == 95.0

    async def test_get_by_workflow(self, history_repository):
        """Test getting approval history by workflow."""
        workflow_id = uuid4()
        
        # Create history entries
        for i in range(2):
            history_data = ContentApprovalHistoryCreate(
                workflow_id=workflow_id,
                action_taken=ModerationAction.APPROVE,
                previous_status=ContentStatus.PENDING,
                new_status=ContentStatus.APPROVED,
                reviewer_id=uuid4()
            )
            await history_repository.create(history_data)
        
        # Get by workflow
        workflow_history = await history_repository.get_by_workflow(workflow_id)
        
        assert len(workflow_history) >= 2
        assert all(h.workflow_id == workflow_id for h in workflow_history)

    async def test_get_by_reviewer(self, history_repository):
        """Test getting approval history by reviewer."""
        reviewer_id = uuid4()
        
        # Create history entries
        for i in range(2):
            history_data = ContentApprovalHistoryCreate(
                workflow_id=uuid4(),
                action_taken=ModerationAction.APPROVE,
                previous_status=ContentStatus.PENDING,
                new_status=ContentStatus.APPROVED,
                reviewer_id=reviewer_id
            )
            await history_repository.create(history_data)
        
        # Get by reviewer
        reviewer_history = await history_repository.get_by_reviewer(reviewer_id)
        
        assert len(reviewer_history) >= 2
        assert all(h.reviewer_id == reviewer_id for h in reviewer_history)

    async def test_get_reviewer_performance(self, history_repository):
        """Test getting reviewer performance metrics."""
        reviewer_id = uuid4()
        
        # Create history entries
        for i in range(3):
            history_data = ContentApprovalHistoryCreate(
                workflow_id=uuid4(),
                action_taken=ModerationAction.APPROVE,
                previous_status=ContentStatus.PENDING,
                new_status=ContentStatus.APPROVED,
                reviewer_id=reviewer_id,
                confidence_score=90.0 + i,
                time_spent_minutes=10 + i
            )
            await history_repository.create(history_data)
        
        # Get performance metrics
        performance = await history_repository.get_reviewer_performance(reviewer_id)
        
        assert "reviewer_id" in performance
        assert "total_decisions" in performance
        assert "average_confidence_score" in performance
        assert performance["reviewer_id"] == str(reviewer_id)
