"""
Widget and KPI Repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for dashboard widget and KPI
data access operations including CRUD operations, configuration management, and
dashboard data aggregation.

Implements Phase 7.1 requirements for widget repository layer with:
- Dashboard widget management with configuration support
- KPI definition management and calculation
- Dashboard data aggregation and caching
- Performance optimization with <200ms query targets
- Redis caching integration for >90% cache hit rate
- Bulk operations support for >1000 records/second throughput

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
import json
from datetime import datetime, date, timedelta, timezone
from typing import List, Optional, Dict, Any, Tuple, Union
from decimal import Decimal
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc, case
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from sqlalchemy.dialects.postgresql import insert

from app.repositories.enhanced_base import EnhancedBaseRepository
from app.repositories.bulk_operations import BulkOperationsMixin
from app.repositories.base import PaginationParams, QueryResult, RepositoryError
from app.models.analytics_models import (
    DashboardWidget, KPIDefinition, DashboardWidgetType
)
from app.core.cache import cache_manager
from app.core.logging import correlation_id


def get_correlation_id() -> str:
    """Get current correlation ID from context."""
    return correlation_id.get('')


class DashboardWidgetRepository(EnhancedBaseRepository[DashboardWidget], BulkOperationsMixin):
    """
    Repository for dashboard widget data access operations.

    Provides comprehensive dashboard widget data management including:
    - CRUD operations with performance optimization (<200ms targets)
    - Widget configuration management and validation
    - Dashboard layout and positioning support
    - Redis caching integration for >90% cache hit rate
    - Bulk operations for widget updates (>1000 records/second)
    """

    def __init__(self, db):
        """Initialize dashboard widget repository."""
        super().__init__(DashboardWidget, db)
        self.logger = logging.getLogger(f"{__name__}.DashboardWidgetRepository")

    async def create_widget(
        self,
        widget_data: Dict[str, Any]
    ) -> DashboardWidget:
        """
        Create a new dashboard widget with validation and caching.

        Performance Metrics:
        - Target response time: <200ms for widget creation
        - Configuration validation: <50ms
        - Widget creation: <100ms with optimized insert
        - Cache invalidation: <50ms for related cache keys

        Args:
            widget_data: Widget configuration data

        Returns:
            Created DashboardWidget instance

        Raises:
            RepositoryError: If widget creation fails
        """
        start_time = time.time()
        correlation_id = get_correlation_id()

        try:
            # Validate widget configuration
            await self._validate_widget_configuration(widget_data)

            # Create widget record
            widget = await self.create(widget_data)

            # Invalidate related cache keys
            await self._invalidate_widget_cache(widget.dashboard_id)

            query_time = time.time() - start_time
            self.logger.info(
                f"Dashboard widget created successfully",
                extra={
                    "correlation_id": correlation_id,
                    "widget_id": widget.uuid,
                    "widget_name": widget.name,
                    "widget_type": widget.widget_type.value,
                    "dashboard_id": str(widget.dashboard_id) if widget.dashboard_id else None,
                    "query_time": query_time
                }
            )

            return widget

        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to create dashboard widget",
                extra={
                    "correlation_id": correlation_id,
                    "widget_name": widget_data.get("name"),
                    "widget_type": widget_data.get("widget_type"),
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to create dashboard widget", e)

    async def get_widgets_by_dashboard(
        self,
        dashboard_id: Optional[UUID] = None,
        is_active: bool = True,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[DashboardWidget]:
        """
        Get dashboard widgets by dashboard ID with caching.

        Performance Metrics:
        - Target response time: <200ms for widget queries
        - Uses idx_dashboard_widgets_dashboard composite index
        - Optimized pagination with cursor support

        Args:
            dashboard_id: Dashboard UUID (None for global widgets)
            is_active: Filter by active status
            pagination: Pagination parameters

        Returns:
            Query result with dashboard widgets
        """
        start_time = time.time()
        correlation_id = get_correlation_id()

        try:
            # Check cache first
            cache_key = f"dashboard_widgets:{dashboard_id}:{is_active}"
            if pagination:
                cache_key += f":p{pagination.page}:s{pagination.size}"

            cached_result = await cache_manager.get(cache_key)
            if cached_result:
                self.logger.debug(
                    f"Dashboard widgets cache hit",
                    extra={
                        "correlation_id": correlation_id,
                        "cache_key": cache_key,
                        "dashboard_id": str(dashboard_id) if dashboard_id else None
                    }
                )
                return QueryResult(**cached_result)

            # Build query
            stmt = select(DashboardWidget).where(DashboardWidget.is_active == is_active)

            if dashboard_id:
                stmt = stmt.where(DashboardWidget.dashboard_id == dashboard_id)
            else:
                stmt = stmt.where(DashboardWidget.dashboard_id.is_(None))

            # Apply ordering by position
            stmt = stmt.order_by(DashboardWidget.position_y, DashboardWidget.position_x)

            result = await self._execute_paginated_query(stmt, pagination)

            # Cache result
            result_dict = {
                "items": [
                    {
                        "uuid": str(item.uuid),
                        "name": item.name,
                        "title": item.title,
                        "description": item.description,
                        "widget_type": item.widget_type.value,
                        "data_source": item.data_source,
                        "query_config": item.query_config,
                        "display_config": item.display_config,
                        "refresh_interval": item.refresh_interval,
                        "is_public": item.is_public,
                        "allowed_roles": item.allowed_roles,
                        "dashboard_id": str(item.dashboard_id) if item.dashboard_id else None,
                        "position_x": item.position_x,
                        "position_y": item.position_y,
                        "width": item.width,
                        "height": item.height,
                        "is_active": item.is_active,
                        "last_updated": item.last_updated.isoformat() if item.last_updated else None,
                        "cache_duration": item.cache_duration,
                        "custom_config": item.custom_config,
                        "created_at": item.created_at.isoformat()
                    } for item in result.items
                ],
                "total": result.total,
                "page": result.page,
                "size": result.size,
                "has_next": result.has_next,
                "has_previous": result.has_previous
            }
            await cache_manager.set(cache_key, result_dict, ttl=1800)  # 30 minutes cache

            query_time = time.time() - start_time
            self.logger.info(
                f"Dashboard widgets query completed",
                extra={
                    "correlation_id": correlation_id,
                    "dashboard_id": str(dashboard_id) if dashboard_id else None,
                    "total_widgets": result.total,
                    "query_time": query_time
                }
            )

            return result

        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to get dashboard widgets",
                extra={
                    "correlation_id": correlation_id,
                    "dashboard_id": str(dashboard_id) if dashboard_id else None,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to get dashboard widgets", e)

    async def update_widget_position(
        self,
        widget_id: UUID,
        position_x: int,
        position_y: int,
        width: Optional[int] = None,
        height: Optional[int] = None
    ) -> Optional[DashboardWidget]:
        """
        Update widget position and size with validation.

        Performance Metrics:
        - Target response time: <100ms for position updates
        - Optimized update with minimal data transfer

        Args:
            widget_id: Widget UUID
            position_x: X position on dashboard grid
            position_y: Y position on dashboard grid
            width: Optional widget width
            height: Optional widget height

        Returns:
            Updated DashboardWidget instance or None if not found

        Raises:
            RepositoryError: If position update fails
        """
        start_time = time.time()
        correlation_id = get_correlation_id()

        try:
            # Validate position values
            if position_x < 0 or position_y < 0:
                raise ValueError("Position coordinates must be non-negative")

            if width is not None and width <= 0:
                raise ValueError("Width must be positive")

            if height is not None and height <= 0:
                raise ValueError("Height must be positive")

            # Prepare update data
            update_data = {
                "position_x": position_x,
                "position_y": position_y
            }

            if width is not None:
                update_data["width"] = width

            if height is not None:
                update_data["height"] = height

            # Update widget
            widget = await self.update(widget_id, update_data)

            if widget:
                # Invalidate related cache keys
                await self._invalidate_widget_cache(widget.dashboard_id)

                query_time = time.time() - start_time
                self.logger.info(
                    f"Widget position updated successfully",
                    extra={
                        "correlation_id": correlation_id,
                        "widget_id": str(widget_id),
                        "position_x": position_x,
                        "position_y": position_y,
                        "width": width,
                        "height": height,
                        "query_time": query_time
                    }
                )

            return widget

        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to update widget position",
                extra={
                    "correlation_id": correlation_id,
                    "widget_id": str(widget_id),
                    "position_x": position_x,
                    "position_y": position_y,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to update widget position", e)

    async def get_widgets_by_type(
        self,
        widget_type: DashboardWidgetType,
        is_active: bool = True
    ) -> List[DashboardWidget]:
        """
        Get widgets by type with caching.

        Args:
            widget_type: Type of dashboard widget
            is_active: Filter by active status

        Returns:
            List of DashboardWidget instances
        """
        try:
            # Check cache first
            cache_key = f"widgets_by_type:{widget_type.value}:{is_active}"
            cached_result = await cache_manager.get(cache_key)
            if cached_result:
                return [DashboardWidget(**item) for item in cached_result]

            # Query database
            stmt = (
                select(DashboardWidget)
                .where(
                    and_(
                        DashboardWidget.widget_type == widget_type,
                        DashboardWidget.is_active == is_active
                    )
                )
                .order_by(DashboardWidget.created_at.desc())
            )

            result = await self.db.execute(stmt)
            widgets = result.scalars().all()

            # Cache result
            widgets_dict = [
                {
                    "uuid": str(widget.uuid),
                    "name": widget.name,
                    "title": widget.title,
                    "widget_type": widget.widget_type.value,
                    "data_source": widget.data_source,
                    "query_config": widget.query_config,
                    "display_config": widget.display_config,
                    "is_active": widget.is_active,
                    "created_at": widget.created_at.isoformat()
                } for widget in widgets
            ]
            await cache_manager.set(cache_key, widgets_dict, ttl=3600)  # 1 hour cache

            return widgets

        except Exception as e:
            self.logger.error(f"Failed to get widgets by type: {str(e)}")
            raise RepositoryError(f"Failed to get widgets by type", e)

    # Private helper methods

    async def _validate_widget_configuration(self, widget_data: Dict[str, Any]):
        """
        Validate widget configuration data.

        Args:
            widget_data: Widget configuration data

        Raises:
            ValueError: If configuration is invalid
        """
        required_fields = ["name", "title", "widget_type", "data_source", "query_config"]
        for field in required_fields:
            if field not in widget_data:
                raise ValueError(f"Required field '{field}' is missing")

        # Validate widget type
        if not isinstance(widget_data.get("widget_type"), DashboardWidgetType):
            raise ValueError("Invalid widget type")

        # Validate query configuration
        query_config = widget_data.get("query_config")
        if not isinstance(query_config, dict):
            raise ValueError("Query configuration must be a dictionary")

        # Validate position values
        position_x = widget_data.get("position_x", 0)
        position_y = widget_data.get("position_y", 0)
        if position_x < 0 or position_y < 0:
            raise ValueError("Position coordinates must be non-negative")

        # Validate size values
        width = widget_data.get("width", 4)
        height = widget_data.get("height", 3)
        if width <= 0 or height <= 0:
            raise ValueError("Width and height must be positive")

    async def _invalidate_widget_cache(self, dashboard_id: Optional[UUID]):
        """
        Invalidate related cache keys for dashboard widgets.

        Args:
            dashboard_id: Dashboard UUID
        """
        try:
            cache_patterns = [
                f"dashboard_widgets:{dashboard_id}:*",
                f"widgets_by_type:*"
            ]
            for pattern in cache_patterns:
                await cache_manager.delete_pattern(pattern)

        except Exception as e:
            self.logger.warning(f"Failed to invalidate widget cache: {str(e)}")


class KPIDefinitionRepository(EnhancedBaseRepository[KPIDefinition], BulkOperationsMixin):
    """
    Repository for KPI definition data access operations.

    Provides comprehensive KPI definition data management including:
    - CRUD operations with performance optimization (<200ms targets)
    - KPI calculation configuration management
    - Category-based filtering and organization
    - Redis caching integration for >90% cache hit rate
    - Bulk operations for KPI updates (>1000 records/second)
    """

    def __init__(self, db):
        """Initialize KPI definition repository."""
        super().__init__(KPIDefinition, db)
        self.logger = logging.getLogger(f"{__name__}.KPIDefinitionRepository")

    async def get_kpis_by_category(
        self,
        category: str,
        is_active: bool = True
    ) -> List[KPIDefinition]:
        """
        Get KPI definitions by category with caching.

        Args:
            category: KPI category
            is_active: Filter by active status

        Returns:
            List of KPIDefinition instances
        """
        try:
            # Check cache first
            cache_key = f"kpis_by_category:{category}:{is_active}"
            cached_result = await cache_manager.get(cache_key)
            if cached_result:
                return [KPIDefinition(**item) for item in cached_result]

            # Query database
            stmt = (
                select(KPIDefinition)
                .where(
                    and_(
                        KPIDefinition.category == category,
                        KPIDefinition.is_active == is_active
                    )
                )
                .order_by(KPIDefinition.display_name)
            )

            result = await self.db.execute(stmt)
            kpis = result.scalars().all()

            # Cache result
            kpis_dict = [
                {
                    "uuid": str(kpi.uuid),
                    "name": kpi.name,
                    "display_name": kpi.display_name,
                    "description": kpi.description,
                    "category": kpi.category,
                    "calculation_method": kpi.calculation_method,
                    "data_source": kpi.data_source,
                    "calculation_config": kpi.calculation_config,
                    "unit": kpi.unit,
                    "format_config": kpi.format_config,
                    "target_value": str(kpi.target_value) if kpi.target_value else None,
                    "warning_threshold": str(kpi.warning_threshold) if kpi.warning_threshold else None,
                    "critical_threshold": str(kpi.critical_threshold) if kpi.critical_threshold else None,
                    "is_active": kpi.is_active,
                    "update_frequency": kpi.update_frequency,
                    "custom_config": kpi.custom_config,
                    "created_at": kpi.created_at.isoformat()
                } for kpi in kpis
            ]
            await cache_manager.set(cache_key, kpis_dict, ttl=3600)  # 1 hour cache

            return kpis

        except Exception as e:
            self.logger.error(f"Failed to get KPIs by category: {str(e)}")
            raise RepositoryError(f"Failed to get KPIs by category", e)
