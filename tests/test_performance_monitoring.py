"""
Comprehensive tests for Performance Monitoring System (Phase 7.2.3).

This module provides comprehensive test coverage for:
- PerformanceMonitoringService: APM integration and metrics collection
- SystemHealthService: Real-time health monitoring and alerting
- LoadTestingService: Performance testing infrastructure
- Performance monitoring API endpoints
- Integration with repository layer and database operations

Implements >80% test coverage validation with pytest-asyncio patterns.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any, List
from unittest.mock import Mock, patch, AsyncMock

from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.services.system_health_service import SystemHealthService
from app.services.load_testing_service import LoadTestingService
from app.models.analytics_models import (
    PerformanceMetrics, SystemHealth, PerformanceMetricType, 
    AnalyticsTimeframe, SystemHealthStatus
)
from app.services.base import ServiceError, ValidationError


class TestPerformanceMonitoringService:
    """Test suite for PerformanceMonitoringService."""

    @pytest.fixture
    async def performance_service(self):
        """Create PerformanceMonitoringService instance for testing."""
        return PerformanceMonitoringService()

    @pytest.mark.asyncio
    async def test_record_performance_metric_success(self, performance_service):
        """Test successful performance metric recording."""
        with patch.object(performance_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.PerformanceMetricsRepository') as mock_repo:
                mock_metric = Mock()
                mock_metric.id = 1
                mock_metric.metric_type = PerformanceMetricType.API_RESPONSE_TIME
                mock_metric.component = "api"
                mock_repo.return_value.create_metric = AsyncMock(return_value=mock_metric)

                result = await performance_service.record_performance_metric(
                    metric_type=PerformanceMetricType.API_RESPONSE_TIME,
                    metric_name="test_endpoint_response_time",
                    component="api",
                    value=Decimal("150.5"),
                    timeframe=AnalyticsTimeframe.REAL_TIME
                )

                assert result == mock_metric
                mock_repo.return_value.create_metric.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_performance_metric_validation_error(self, performance_service):
        """Test performance metric recording with validation error."""
        with pytest.raises(ValidationError) as exc_info:
            await performance_service.record_performance_metric(
                metric_type=PerformanceMetricType.API_RESPONSE_TIME,
                metric_name="test_endpoint_response_time",
                component="api",
                value=Decimal("-10.0"),  # Negative value should fail
                timeframe=AnalyticsTimeframe.REAL_TIME
            )
        
        assert "cannot be negative" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_performance_metrics_success(self, performance_service):
        """Test successful performance metrics retrieval."""
        with patch.object(performance_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.PerformanceMetricsRepository') as mock_repo:
                mock_metrics = [Mock(), Mock()]
                mock_repo.return_value.get_metrics_by_component = AsyncMock(return_value=mock_metrics)

                result = await performance_service.get_performance_metrics(
                    component="api",
                    metric_type=PerformanceMetricType.API_RESPONSE_TIME,
                    limit=100
                )

                assert result == mock_metrics
                mock_repo.return_value.get_metrics_by_component.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_aggregated_performance_metrics_success(self, performance_service):
        """Test successful aggregated performance metrics retrieval."""
        with patch.object(performance_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.PerformanceMetricsRepository') as mock_repo:
                mock_aggregated = {
                    "total_count": 100,
                    "avg_value": 150.5,
                    "p95_value": 250.0,
                    "p99_value": 350.0,
                    "error_rate": 0.02
                }
                mock_repo.return_value.get_aggregated_metrics = AsyncMock(return_value=mock_aggregated)

                result = await performance_service.get_aggregated_performance_metrics(
                    component="api",
                    timeframe=AnalyticsTimeframe.HOURLY
                )

                assert result == mock_aggregated
                mock_repo.return_value.get_aggregated_metrics.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_performance_insights_success(self, performance_service):
        """Test successful performance insights generation."""
        with patch.object(performance_service, 'get_aggregated_performance_metrics') as mock_aggregated:
            mock_aggregated.return_value = {
                "total_count": 100,
                "avg_value": 150.5,
                "p95_value": 600.0,  # High value to trigger recommendation
                "p99_value": 800.0,
                "error_rate": 0.08  # High error rate to trigger alert
            }

            result = await performance_service.get_performance_insights(
                component="api",
                hours_back=24
            )

            assert "analysis_period" in result
            assert "performance_summary" in result
            assert "recommendations" in result
            assert "alerts" in result
            assert len(result["recommendations"]) > 0  # Should have recommendations
            assert len(result["alerts"]) > 0  # Should have alerts

    @pytest.mark.asyncio
    async def test_bulk_record_metrics_success(self, performance_service):
        """Test successful bulk metrics recording."""
        with patch.object(performance_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.PerformanceMetricsRepository') as mock_repo:
                mock_metric = Mock()
                mock_metric.id = 1
                mock_repo.return_value.create_metric = AsyncMock(return_value=mock_metric)

                metrics_data = [
                    {
                        "metric_type": PerformanceMetricType.API_RESPONSE_TIME,
                        "metric_name": "test_endpoint_1",
                        "component": "api",
                        "value": Decimal("100.0")
                    },
                    {
                        "metric_type": PerformanceMetricType.DATABASE_QUERY_TIME,
                        "metric_name": "test_query_1",
                        "component": "database",
                        "value": Decimal("50.0")
                    }
                ]

                result = await performance_service.bulk_record_metrics(metrics_data)

                assert len(result) == 2
                assert mock_repo.return_value.create_metric.call_count == 2

    @pytest.mark.asyncio
    async def test_bulk_record_metrics_validation_error(self, performance_service):
        """Test bulk metrics recording with validation error."""
        metrics_data = [
            {
                "metric_type": PerformanceMetricType.API_RESPONSE_TIME,
                "metric_name": "test_endpoint_1",
                "component": "api",
                "value": Decimal("-100.0")  # Negative value should fail
            }
        ]

        with pytest.raises(ValidationError) as exc_info:
            await performance_service.bulk_record_metrics(metrics_data)
        
        assert "cannot be negative" in str(exc_info.value)


class TestSystemHealthService:
    """Test suite for SystemHealthService."""

    @pytest.fixture
    async def health_service(self):
        """Create SystemHealthService instance for testing."""
        return SystemHealthService()

    @pytest.mark.asyncio
    async def test_record_health_status_success(self, health_service):
        """Test successful health status recording."""
        with patch.object(health_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.SystemHealthRepository') as mock_repo:
                mock_health = Mock()
                mock_health.id = 1
                mock_health.component = "api"
                mock_health.status = SystemHealthStatus.HEALTHY
                mock_repo.return_value.create_health_record = AsyncMock(return_value=mock_health)

                result = await health_service.record_health_status(
                    component="api",
                    service_name="user_service",
                    status=SystemHealthStatus.HEALTHY,
                    response_time=Decimal("50.0"),
                    cpu_usage=Decimal("25.5"),
                    memory_usage=Decimal("60.0")
                )

                assert result == mock_health
                mock_repo.return_value.create_health_record.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_health_status_validation_error(self, health_service):
        """Test health status recording with validation error."""
        with pytest.raises(ValidationError) as exc_info:
            await health_service.record_health_status(
                component="api",
                service_name="user_service",
                status=SystemHealthStatus.HEALTHY,
                cpu_usage=Decimal("150.0")  # Invalid CPU usage > 100%
            )
        
        assert "CPU usage must be between 0 and 100" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_system_health_overview_success(self, health_service):
        """Test successful system health overview retrieval."""
        with patch.object(health_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.SystemHealthRepository') as mock_repo:
                mock_overview = {
                    "total_components": 5,
                    "healthy_components": 4,
                    "warning_components": 1,
                    "critical_components": 0,
                    "overall_status": "warning"
                }
                mock_repo.return_value.get_health_overview = AsyncMock(return_value=mock_overview)

                result = await health_service.get_system_health_overview(include_details=True)

                assert result == mock_overview
                mock_repo.return_value.get_health_overview.assert_called_once_with(include_details=True)

    @pytest.mark.asyncio
    async def test_get_unhealthy_components_success(self, health_service):
        """Test successful unhealthy components retrieval."""
        with patch.object(health_service, 'get_session_context') as mock_session:
            mock_session.return_value.__aenter__.return_value = Mock()
            
            with patch('app.repositories.performance_monitoring_repositories.SystemHealthRepository') as mock_repo:
                mock_unhealthy = [Mock(), Mock()]
                mock_repo.return_value.get_unhealthy_components = AsyncMock(return_value=mock_unhealthy)

                result = await health_service.get_unhealthy_components(
                    status_filter=[SystemHealthStatus.WARNING, SystemHealthStatus.CRITICAL]
                )

                assert result == mock_unhealthy
                mock_repo.return_value.get_unhealthy_components.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_component_health_success(self, health_service):
        """Test successful component health check."""
        with patch.object(health_service, 'record_health_status') as mock_record:
            mock_health = Mock()
            mock_health.component = "api"
            mock_health.status = SystemHealthStatus.HEALTHY
            mock_record.return_value = mock_health

            result = await health_service.check_component_health(
                component="api",
                service_name="user_service"
            )

            assert result == mock_health
            mock_record.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_health_check_success(self, health_service):
        """Test successful bulk health check."""
        with patch.object(health_service, 'check_component_health') as mock_check:
            mock_health = Mock()
            mock_health.component = "api"
            mock_health.status = SystemHealthStatus.HEALTHY
            mock_check.return_value = mock_health

            components = [
                {"component": "api", "service_name": "user_service"},
                {"component": "database", "service_name": "postgres"}
            ]

            result = await health_service.bulk_health_check(components)

            assert len(result) == 2
            assert mock_check.call_count == 2


class TestLoadTestingService:
    """Test suite for LoadTestingService."""

    @pytest.fixture
    async def load_testing_service(self):
        """Create LoadTestingService instance for testing."""
        return LoadTestingService()

    @pytest.mark.asyncio
    async def test_create_load_test_configuration_success(self, load_testing_service):
        """Test successful load test configuration creation."""
        config = await load_testing_service.create_load_test_configuration(
            test_name="API Load Test",
            target_endpoint="/api/v1/users",
            concurrent_users=100,
            duration_seconds=300,
            ramp_up_seconds=60
        )

        assert config["test_name"] == "API Load Test"
        assert config["target_endpoint"] == "/api/v1/users"
        assert config["concurrent_users"] == 100
        assert config["duration_seconds"] == 300
        assert config["ramp_up_seconds"] == 60
        assert "test_id" in config
        assert config["status"] == "configured"

    @pytest.mark.asyncio
    async def test_create_load_test_configuration_validation_error(self, load_testing_service):
        """Test load test configuration creation with validation error."""
        with pytest.raises(ValidationError) as exc_info:
            await load_testing_service.create_load_test_configuration(
                test_name="",  # Empty name should fail
                target_endpoint="/api/v1/users",
                concurrent_users=100,
                duration_seconds=300
            )
        
        assert "Test name must be 1-100 characters" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_load_test_success(self, load_testing_service):
        """Test successful load test execution."""
        config = {
            "test_id": "test-123",
            "test_name": "API Load Test",
            "target_endpoint": "/api/v1/users",
            "concurrent_users": 10,
            "duration_seconds": 5,  # Short duration for testing
            "ramp_up_seconds": 1
        }

        with patch.object(load_testing_service, '_simulate_load_test') as mock_simulate:
            mock_simulate.return_value = None

            result = await load_testing_service.execute_load_test(config)

            assert result["test_id"] == "test-123"
            assert result["test_name"] == "API Load Test"
            assert "total_requests" in result
            assert "success_rate" in result
            assert "avg_response_time" in result
            assert "performance_grade" in result
            assert "recommendations" in result

    @pytest.mark.asyncio
    async def test_get_active_tests_success(self, load_testing_service):
        """Test successful active tests retrieval."""
        # Add a mock active test
        load_testing_service._active_tests["test-123"] = {
            "config": {"test_name": "Test 1"},
            "start_time": 1234567890,
            "status": "running",
            "metrics": {"requests_sent": 100, "requests_completed": 95, "requests_failed": 5}
        }

        result = await load_testing_service.get_active_tests()

        assert len(result) == 1
        assert result[0]["test_id"] == "test-123"
        assert result[0]["test_name"] == "Test 1"
        assert result[0]["status"] == "running"

    @pytest.mark.asyncio
    async def test_stop_load_test_success(self, load_testing_service):
        """Test successful load test stopping."""
        # Add a mock active test
        load_testing_service._active_tests["test-123"] = {
            "config": {"test_name": "Test 1"},
            "start_time": 1234567890,
            "status": "running",
            "metrics": {}
        }

        result = await load_testing_service.stop_load_test("test-123")

        assert result is True
        assert load_testing_service._active_tests["test-123"]["status"] == "stopped"

    @pytest.mark.asyncio
    async def test_stop_load_test_not_found(self, load_testing_service):
        """Test load test stopping when test not found."""
        result = await load_testing_service.stop_load_test("nonexistent-test")

        assert result is False
