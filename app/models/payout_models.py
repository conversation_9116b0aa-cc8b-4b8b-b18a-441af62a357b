"""
Payout and settlement models for Culture Connect Backend API.

This module defines comprehensive payout and escrow models for vendor payments,
settlement management, and fund holding for the marketplace.

Implements Phase 1 of Payment & Transaction Management System with:
- Vendor payout management with automated settlement
- Escrow account management for secure fund holding
- Comprehensive audit logging and compliance features
- Performance-optimized indexing for financial operations
- Integration with payment and reconciliation systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Numeric, CheckConstraint, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from app.db.base import BaseModelWithAudit
from app.core.payment.config import PaymentProviderType


class PayoutStatus(str, Enum):
    """Payout status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REVERSED = "reversed"
    ON_HOLD = "on_hold"


class EscrowStatus(str, Enum):
    """Escrow account status enumeration."""
    ACTIVE = "active"
    HELD = "held"
    RELEASED = "released"
    DISPUTED = "disputed"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class ReleaseCondition(str, Enum):
    """Escrow release condition enumeration."""
    SERVICE_COMPLETION = "service_completion"
    CUSTOMER_CONFIRMATION = "customer_confirmation"
    TIME_BASED = "time_based"
    MANUAL_RELEASE = "manual_release"
    DISPUTE_RESOLUTION = "dispute_resolution"


class VendorPayout(BaseModelWithAudit):
    """
    Vendor payout model for marketplace settlement management.
    
    This model manages vendor payouts including earnings calculation,
    fee deduction, and automated settlement processing.
    
    Features:
    - Automated payout calculation and processing
    - Multi-provider payout support
    - Comprehensive fee and commission tracking
    - Performance-optimized settlement queries
    - Integration with payment and reconciliation systems
    """
    
    __tablename__ = "vendor_payouts"
    
    # Core payout information
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )
    
    # Payout period
    period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Payout period start date"
    )
    
    period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Payout period end date"
    )
    
    # Financial details
    total_earnings = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Total earnings for the period"
    )
    
    platform_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform commission fee"
    )
    
    processing_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Payment processing fee"
    )
    
    adjustment_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Manual adjustments (positive or negative)"
    )
    
    net_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Net payout amount after fees"
    )
    
    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Payout currency (ISO 4217)"
    )
    
    # Payout method and provider
    payout_method_id = Column(
        Integer,
        ForeignKey("payment_methods.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Payout method (bank account, etc.)"
    )
    
    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=True,
        index=True,
        doc="Payout provider"
    )
    
    # Status and tracking
    status = Column(
        SQLEnum(PayoutStatus),
        nullable=False,
        default=PayoutStatus.PENDING,
        index=True,
        doc="Payout processing status"
    )
    
    # Provider information
    provider_payout_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider payout ID"
    )
    
    reference_id = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique payout reference"
    )
    
    # Timing information
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Payout processing completion timestamp"
    )
    
    scheduled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Scheduled payout date"
    )
    
    # Failure tracking
    failure_reason = Column(
        String(255),
        nullable=True,
        doc="Payout failure reason"
    )
    
    failure_code = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Provider failure code"
    )
    
    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of retry attempts"
    )
    
    # Metadata and additional information
    payout_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional payout metadata"
    )
    
    provider_response = Column(
        Text,
        nullable=True,
        doc="Provider response data (encrypted)"
    )
    
    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="payouts",
        doc="Associated vendor"
    )
    
    payout_method = relationship(
        "PaymentMethod",
        doc="Payout method used"
    )
    
    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "total_earnings >= 0",
            name="check_total_earnings_non_negative"
        ),
        CheckConstraint(
            "platform_fee >= 0",
            name="check_platform_fee_non_negative"
        ),
        CheckConstraint(
            "processing_fee >= 0",
            name="check_processing_fee_non_negative"
        ),
        CheckConstraint(
            "net_amount >= 0",
            name="check_net_amount_non_negative"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="check_retry_count_non_negative"
        ),
        CheckConstraint(
            "period_start < period_end",
            name="check_period_valid"
        ),
        
        # Performance indexes
        Index("idx_payout_vendor_status", "vendor_id", "status"),
        Index("idx_payout_vendor_period", "vendor_id", "period_start", "period_end"),
        Index("idx_payout_status_scheduled", "status", "scheduled_at"),
        Index("idx_payout_provider_status", "provider", "status"),
        Index("idx_payout_currency_amount", "currency", "net_amount"),
        Index("idx_payout_processed_date", "processed_at", "status"),
    )
    
    def __repr__(self) -> str:
        """String representation of the vendor payout."""
        return f"<VendorPayout(id={self.id}, vendor_id={self.vendor_id}, ref='{self.reference_id}', status='{self.status}')>"
    
    @property
    def is_successful(self) -> bool:
        """Check if payout is successful."""
        return self.status == PayoutStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if payout failed."""
        return self.status in {PayoutStatus.FAILED, PayoutStatus.CANCELLED}
    
    @property
    def total_fees(self) -> Decimal:
        """Calculate total fees."""
        return self.platform_fee + self.processing_fee


class EscrowAccount(BaseModelWithAudit):
    """
    Escrow account model for secure fund holding and dispute management.
    
    This model manages escrow accounts for holding funds until service
    completion or dispute resolution.
    
    Features:
    - Secure fund holding with automated release conditions
    - Dispute management and resolution tracking
    - Time-based and event-based release mechanisms
    - Comprehensive audit logging for compliance
    - Integration with booking and payment systems
    """
    
    __tablename__ = "escrow_accounts"
    
    # Core escrow information
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )
    
    payment_id = Column(
        Integer,
        ForeignKey("payments.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated payment ID"
    )
    
    # Financial details
    amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Escrow amount"
    )
    
    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Escrow currency (ISO 4217)"
    )
    
    # Status and conditions
    status = Column(
        SQLEnum(EscrowStatus),
        nullable=False,
        default=EscrowStatus.ACTIVE,
        index=True,
        doc="Escrow account status"
    )
    
    release_condition = Column(
        SQLEnum(ReleaseCondition),
        nullable=False,
        default=ReleaseCondition.SERVICE_COMPLETION,
        index=True,
        doc="Condition for fund release"
    )
    
    # Timing information
    hold_until = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Hold funds until this date"
    )
    
    released_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Fund release timestamp"
    )
    
    # Release tracking
    released_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who released the funds"
    )
    
    release_reason = Column(
        String(255),
        nullable=True,
        doc="Reason for fund release"
    )
    
    auto_release_enabled = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable automatic release based on conditions"
    )
    
    # Dispute information
    disputed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Dispute initiation timestamp"
    )
    
    disputed_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who initiated the dispute"
    )
    
    dispute_reason = Column(
        Text,
        nullable=True,
        doc="Dispute reason and details"
    )
    
    dispute_resolved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Dispute resolution timestamp"
    )
    
    # Metadata and additional information
    escrow_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional escrow metadata"
    )
    
    release_conditions_data = Column(
        JSON,
        nullable=True,
        doc="Detailed release conditions configuration"
    )
    
    # Relationships
    booking = relationship(
        "Booking",
        back_populates="escrow_accounts",
        doc="Associated booking"
    )
    
    payment = relationship(
        "Payment",
        doc="Associated payment"
    )
    
    released_by_user = relationship(
        "User",
        foreign_keys=[released_by],
        doc="User who released the funds"
    )
    
    disputed_by_user = relationship(
        "User",
        foreign_keys=[disputed_by],
        doc="User who initiated the dispute"
    )
    
    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "amount > 0",
            name="check_escrow_amount_positive"
        ),
        
        # Performance indexes
        Index("idx_escrow_booking_status", "booking_id", "status"),
        Index("idx_escrow_payment_status", "payment_id", "status"),
        Index("idx_escrow_status_hold_until", "status", "hold_until"),
        Index("idx_escrow_release_condition", "release_condition", "status"),
        Index("idx_escrow_disputed", "disputed_at", "status"),
        Index("idx_escrow_auto_release", "auto_release_enabled", "hold_until"),
    )
    
    def __repr__(self) -> str:
        """String representation of the escrow account."""
        return f"<EscrowAccount(id={self.id}, booking_id={self.booking_id}, amount={self.amount}, status='{self.status}')>"
    
    @property
    def is_active(self) -> bool:
        """Check if escrow is active."""
        return self.status == EscrowStatus.ACTIVE
    
    @property
    def is_released(self) -> bool:
        """Check if funds are released."""
        return self.status == EscrowStatus.RELEASED
    
    @property
    def is_disputed(self) -> bool:
        """Check if escrow is disputed."""
        return self.status == EscrowStatus.DISPUTED
    
    @property
    def is_ready_for_release(self) -> bool:
        """Check if escrow is ready for automatic release."""
        if not self.auto_release_enabled or self.status != EscrowStatus.ACTIVE:
            return False
        
        if self.hold_until and datetime.utcnow() < self.hold_until.replace(tzinfo=None):
            return False
        
        return True
