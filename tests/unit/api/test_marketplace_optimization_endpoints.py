"""
Unit tests for Marketplace Optimization API endpoints.

This module provides comprehensive unit tests for the marketplace optimization API endpoints
implementing Task 3.2.2 Phase 5 requirements with >80% test coverage.

Test Coverage:
- All 13 marketplace optimization endpoints
- Authentication and authorization
- Request/response validation
- Error handling scenarios
- Rate limiting validation
- RBAC integration
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, date, timedelta
from decimal import Decimal
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.schemas.marketplace_optimization import (
    MarketplaceOptimizationDashboard, OptimizationAnalysisRequest,
    SEOAnalysisResponse, PerformanceMetricsResponse, CompetitiveAnalysisResponse,
    MobileOptimizationResponse
)
from app.schemas.auth import UserResponse


class TestMarketplaceOptimizationEndpoints:
    """Test suite for Marketplace Optimization API endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_current_user(self):
        """Mock current user for authentication."""
        return UserResponse(
            id=1,
            email="<EMAIL>",
            username="testvendor",
            role="vendor",
            vendor_id=1,
            is_active=True,
            is_verified=True
        )

    @pytest.fixture
    def mock_admin_user(self):
        """Mock admin user for authentication."""
        return UserResponse(
            id=2,
            email="<EMAIL>",
            username="admin",
            role="admin",
            vendor_id=None,
            is_active=True,
            is_verified=True
        )

    @pytest.fixture
    def sample_optimization_dashboard(self):
        """Sample optimization dashboard response."""
        return MarketplaceOptimizationDashboard(
            service_id=1,
            vendor_id=1,
            overall_optimization_score=Decimal("78.50"),
            seo_analysis=MagicMock(),
            performance_metrics=MagicMock(),
            competitive_analysis=None,
            mobile_optimization=MagicMock(),
            recommendations=[],
            last_updated=datetime.now(),
            analysis_summary={"total_components": 3, "completed_components": 3}
        )

    @pytest.fixture
    def sample_seo_analysis(self):
        """Sample SEO analysis response."""
        return SEOAnalysisResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            overall_seo_score=Decimal("75.50"),
            title_score=Decimal("80.00"),
            description_score=Decimal("70.00"),
            media_score=Decimal("85.00"),
            keyword_score=Decimal("65.00"),
            completeness_score=Decimal("90.00"),
            primary_keywords=["cultural tour", "heritage"],
            keyword_density={"cultural": 2.5, "tour": 1.8},
            missing_keywords=["authentic", "traditional"],
            content_quality_metrics={"word_count": 250},
            readability_score=Decimal("78.00"),
            meta_title_analysis={"length": 55, "keyword_present": True},
            meta_description_analysis={"length": 145, "call_to_action": True},
            analysis_date=datetime.now(),
            analysis_version="1.0"
        )

    def test_get_optimization_dashboard_success(self, client, mock_current_user, sample_optimization_dashboard):
        """Test successful optimization dashboard retrieval."""
        service_id = 1

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.MarketplaceOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_optimization_dashboard.return_value = sample_optimization_dashboard

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/dashboard/{service_id}")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["service_id"] == service_id
            assert data["vendor_id"] == mock_current_user.vendor_id
            assert "overall_optimization_score" in data
            assert "seo_analysis" in data
            assert "performance_metrics" in data

            # Verify service method was called
            mock_service_instance.get_optimization_dashboard.assert_called_once_with(
                service_id=service_id,
                include_recommendations=True
            )

    def test_get_optimization_dashboard_unauthorized(self, client):
        """Test optimization dashboard access without authentication."""
        service_id = 1

        # Make request without authentication
        response = client.get(f"/api/v1/marketplace-optimization/dashboard/{service_id}")

        # Assertions
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_optimization_dashboard_service_not_found(self, client, mock_current_user):
        """Test optimization dashboard for non-existent service."""
        service_id = 999

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.MarketplaceOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service raises exception
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_optimization_dashboard.side_effect = Exception("Service not found")

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/dashboard/{service_id}")

            # Assertions
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_run_comprehensive_analysis_success(self, client, mock_current_user, sample_optimization_dashboard):
        """Test successful comprehensive analysis execution."""
        analysis_request = {
            "service_id": 1,
            "analysis_types": ["seo", "performance", "competitive", "mobile"],
            "include_recommendations": True,
            "analysis_depth": "comprehensive"
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.MarketplaceOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.run_comprehensive_analysis.return_value = sample_optimization_dashboard

            # Make request
            response = client.post("/api/v1/marketplace-optimization/analyze", json=analysis_request)

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["service_id"] == analysis_request["service_id"]
            assert "overall_optimization_score" in data

            # Verify service method was called
            mock_service_instance.run_comprehensive_analysis.assert_called_once()

    def test_run_comprehensive_analysis_invalid_request(self, client, mock_current_user):
        """Test comprehensive analysis with invalid request data."""
        invalid_request = {
            "service_id": "invalid",  # Should be integer
            "analysis_types": ["invalid_type"],  # Invalid analysis type
            "include_recommendations": "yes"  # Should be boolean
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth:
            # Mock authentication
            mock_auth.return_value = mock_current_user

            # Make request
            response = client.post("/api/v1/marketplace-optimization/analyze", json=invalid_request)

            # Assertions
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_vendor_optimization_overview_success(self, client, mock_current_user):
        """Test successful vendor optimization overview retrieval."""
        vendor_id = mock_current_user.vendor_id
        overview_data = {
            "vendor_id": vendor_id,
            "optimization_summary": {"total_services": 5, "average_score": 75.0},
            "pending_recommendations": {"total_count": 12, "high_priority": 3}
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.MarketplaceOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_vendor_optimization_overview.return_value = overview_data

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/vendor/{vendor_id}/overview")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["vendor_id"] == vendor_id
            assert "optimization_summary" in data
            assert "pending_recommendations" in data

    def test_get_vendor_optimization_overview_access_denied(self, client, mock_current_user):
        """Test vendor optimization overview access denied for other vendor."""
        other_vendor_id = 999  # Different from mock_current_user.vendor_id

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth:
            # Mock authentication
            mock_auth.return_value = mock_current_user

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/vendor/{other_vendor_id}/overview")

            # Assertions
            assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_vendor_optimization_overview_admin_access(self, client, mock_admin_user):
        """Test vendor optimization overview access by admin user."""
        vendor_id = 1
        overview_data = {
            "vendor_id": vendor_id,
            "optimization_summary": {"total_services": 5, "average_score": 75.0},
            "pending_recommendations": {"total_count": 12, "high_priority": 3}
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.MarketplaceOptimizationService') as mock_service:

            # Mock authentication (admin user)
            mock_auth.return_value = mock_admin_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_vendor_optimization_overview.return_value = overview_data

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/vendor/{vendor_id}/overview")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["vendor_id"] == vendor_id

    def test_get_seo_analysis_success(self, client, mock_current_user, sample_seo_analysis):
        """Test successful SEO analysis retrieval."""
        service_id = 1

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.SEOOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.analyze_service_seo.return_value = sample_seo_analysis

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/seo/analyze/{service_id}")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["service_id"] == service_id
            assert "overall_seo_score" in data
            assert "primary_keywords" in data

    def test_get_seo_analysis_force_refresh(self, client, mock_current_user, sample_seo_analysis):
        """Test SEO analysis with force refresh parameter."""
        service_id = 1

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.SEOOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.analyze_service_seo.return_value = sample_seo_analysis

            # Make request with force_refresh=true
            response = client.get(f"/api/v1/marketplace-optimization/seo/analyze/{service_id}?force_refresh=true")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            
            # Verify service method was called with force_refresh=True
            mock_service_instance.analyze_service_seo.assert_called_once_with(
                service_id=service_id,
                force_refresh=True
            )

    def test_get_keyword_recommendations_success(self, client, mock_current_user):
        """Test successful keyword recommendations retrieval."""
        service_id = 1
        keyword_data = {
            "current_keywords": {"extracted_keywords": ["cultural", "tour"], "total_keywords": 2},
            "recommendations": {"suggested_keywords": ["authentic", "heritage"], "priority_keywords": ["cultural"]},
            "keyword_gaps": ["traditional", "nigerian"],
            "optimization_opportunities": ["increase keyword density", "add long-tail keywords"]
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.SEOOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_keyword_recommendations.return_value = keyword_data

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/seo/{service_id}/keywords")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "current_keywords" in data
            assert "recommendations" in data
            assert "keyword_gaps" in data

    def test_get_keyword_recommendations_with_target_keywords(self, client, mock_current_user):
        """Test keyword recommendations with target keywords parameter."""
        service_id = 1
        target_keywords = ["cultural", "heritage", "authentic"]

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.SEOOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_keyword_recommendations.return_value = {}

            # Make request with target keywords
            params = "&".join([f"target_keywords={kw}" for kw in target_keywords])
            response = client.get(f"/api/v1/marketplace-optimization/seo/{service_id}/keywords?{params}")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            
            # Verify service method was called with target keywords
            mock_service_instance.get_keyword_recommendations.assert_called_once_with(
                service_id=service_id,
                target_keywords=target_keywords
            )

    def test_get_content_optimization_suggestions_success(self, client, mock_current_user):
        """Test successful content optimization suggestions retrieval."""
        service_id = 1
        content_data = {
            "content_analysis": {"title_analysis": {}, "description_analysis": {}},
            "optimization_suggestions": {"title_optimization": [], "description_optimization": []},
            "implementation_order": ["optimize_title", "improve_description"],
            "expected_impact": {"seo_score_improvement": 15.0}
        }

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth, \
             patch('app.api.v1.endpoints.marketplace_optimization.SEOOptimizationService') as mock_service:

            # Mock authentication
            mock_auth.return_value = mock_current_user
            
            # Mock service response
            mock_service_instance = mock_service.return_value
            mock_service_instance.get_content_optimization_suggestions.return_value = content_data

            # Make request
            response = client.get(f"/api/v1/marketplace-optimization/seo/{service_id}/content-optimization")

            # Assertions
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "content_analysis" in data
            assert "optimization_suggestions" in data
            assert "implementation_order" in data

    def test_endpoint_parameter_validation(self, client, mock_current_user):
        """Test endpoint parameter validation."""
        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth:
            # Mock authentication
            mock_auth.return_value = mock_current_user

            # Test invalid service_id (non-integer)
            response = client.get("/api/v1/marketplace-optimization/dashboard/invalid")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

            # Test invalid vendor_id (non-integer)
            response = client.get("/api/v1/marketplace-optimization/vendor/invalid/overview")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_endpoint_query_parameter_validation(self, client, mock_current_user):
        """Test endpoint query parameter validation."""
        service_id = 1

        with patch('app.api.v1.endpoints.marketplace_optimization.get_current_vendor_user') as mock_auth:
            # Mock authentication
            mock_auth.return_value = mock_current_user

            # Test invalid boolean parameter
            response = client.get(f"/api/v1/marketplace-optimization/dashboard/{service_id}?include_recommendations=invalid")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

            # Test invalid days parameter (out of range)
            response = client.get(f"/api/v1/marketplace-optimization/vendor/1/overview?days=500")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
