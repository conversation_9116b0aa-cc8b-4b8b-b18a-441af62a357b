# Culture Connect Backend - Production Backup Patch
# Backup and disaster recovery configurations for production environment

apiVersion: batch/v1
kind: CronJob
metadata:
  name: culture-connect-database-backup
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM UTC
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/name: culture-connect
            app.kubernetes.io/component: backup
        spec:
          restartPolicy: OnFailure
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
            runAsGroup: 1001
            fsGroup: 1001
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/sh
            - -c
            - |
              set -e
              BACKUP_FILE="/backups/culture_connect_$(date +%Y%m%d_%H%M%S).sql"
              echo "Starting database backup to $BACKUP_FILE"
              pg_dump $CC_DATABASE_URL > $BACKUP_FILE
              gzip $BACKUP_FILE
              echo "Backup completed: ${BACKUP_FILE}.gz"
              
              # Upload to S3 if configured
              if [ -n "$AWS_S3_BUCKET" ]; then
                aws s3 cp "${BACKUP_FILE}.gz" "s3://$AWS_S3_BUCKET/database-backups/"
                echo "Backup uploaded to S3"
              fi
              
              # Cleanup old local backups (keep last 7 days)
              find /backups -name "culture_connect_*.sql.gz" -mtime +7 -delete
            env:
            - name: CC_DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: culture-connect-secrets
                  key: CC_DATABASE_URL
            - name: AWS_S3_BUCKET
              value: "culture-connect-production-backups"
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
            resources:
              requests:
                memory: "128Mi"
                cpu: "100m"
              limits:
                memory: "256Mi"
                cpu: "200m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: culture-connect-backup-pvc

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: culture-connect-file-backup
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: backup
spec:
  schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM UTC
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/name: culture-connect
            app.kubernetes.io/component: backup
        spec:
          restartPolicy: OnFailure
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
            runAsGroup: 1001
            fsGroup: 1001
          containers:
          - name: file-backup
            image: alpine:latest
            command:
            - /bin/sh
            - -c
            - |
              set -e
              apk add --no-cache aws-cli tar gzip
              BACKUP_FILE="/backups/files_$(date +%Y%m%d_%H%M%S).tar.gz"
              echo "Starting file backup to $BACKUP_FILE"
              tar czf $BACKUP_FILE -C /app uploads logs
              echo "File backup completed: $BACKUP_FILE"
              
              # Upload to S3 if configured
              if [ -n "$AWS_S3_BUCKET" ]; then
                aws s3 cp "$BACKUP_FILE" "s3://$AWS_S3_BUCKET/file-backups/"
                echo "File backup uploaded to S3"
              fi
              
              # Cleanup old local backups (keep last 4 weeks)
              find /backups -name "files_*.tar.gz" -mtime +28 -delete
            env:
            - name: AWS_S3_BUCKET
              value: "culture-connect-production-backups"
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
            - name: app-uploads
              mountPath: /app/uploads
              readOnly: true
            - name: app-logs
              mountPath: /app/logs
              readOnly: true
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "200m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: culture-connect-backup-pvc
          - name: app-uploads
            persistentVolumeClaim:
              claimName: culture-connect-uploads-pvc
          - name: app-logs
            persistentVolumeClaim:
              claimName: culture-connect-logs-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: culture-connect-backup-pvc
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: backup
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: gp3
