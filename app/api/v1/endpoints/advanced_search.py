"""
Advanced Search API Endpoints for Culture Connect Backend API.

This module provides comprehensive REST API endpoints for advanced search operations including:
- Advanced service search with multiple criteria and filters
- Geospatial search with location-based filtering
- Search suggestions and autocomplete functionality
- Popular searches and trending content
- Search analytics and performance tracking

Implements Task 3.3 requirements for advanced search API endpoints with
FastAPI router patterns, dependency injection, authentication integration,
and comprehensive error handling with correlation IDs.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.deps import get_db, get_current_user
from app.core.logging import correlation_id
from app.repositories.search_repository import AdvancedSearchRepository
from app.schemas.search_schemas import (
    AdvancedSearchRequest,
    AdvancedSearchResponse,
    LocationSearchRequest,
    LocationSearchResponse,
    SearchSuggestionRequest,
    SearchSuggestionResponse,
    PopularSearchesResponse,
    SearchResultService,
    SearchMetadata,
    SearchAggregations
)
from app.schemas.auth import UserResponse
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/search",
    response_model=AdvancedSearchResponse,
    summary="Advanced Service Search",
    description="Perform advanced search with multiple criteria including text, location, price, rating, and availability filters"
)
async def advanced_search(
    search_request: AdvancedSearchRequest,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user)
) -> AdvancedSearchResponse:
    """
    Perform advanced service search with comprehensive filtering.

    - **query**: Text search query
    - **location**: Geographic location filter with radius
    - **category_ids**: Filter by service categories
    - **price_range**: Price range filtering
    - **min_rating**: Minimum rating filter
    - **availability_date**: Filter by availability
    - **sort_by**: Sort criteria (relevance, price, rating, distance)
    - **page**: Page number for pagination
    - **per_page**: Results per page

    Returns comprehensive search results with aggregations and metadata.
    """
    correlation = correlation_id.get()
    start_time = datetime.now(timezone.utc)

    try:
        logger.info(
            f"Advanced search request",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "query": search_request.query,
                "location": search_request.location.model_dump() if search_request.location else None,
                "page": search_request.page,
                "per_page": search_request.per_page
            }
        )

        # Initialize search repository
        search_repo = AdvancedSearchRepository(db)

        # Extract search parameters
        location_tuple = None
        radius_km = None
        if search_request.location:
            location_tuple = (search_request.location.latitude, search_request.location.longitude)
            radius_km = search_request.location.radius_km

        price_min = search_request.price_range.min_price if search_request.price_range else None
        price_max = search_request.price_range.max_price if search_request.price_range else None

        # Perform advanced search
        services, total_count, aggregations = await search_repo.advanced_search(
            query=search_request.query,
            location=location_tuple,
            radius_km=radius_km,
            category_ids=search_request.category_ids,
            price_min=price_min,
            price_max=price_max,
            min_rating=search_request.min_rating,
            availability_date=search_request.availability_date,
            tags=search_request.tags,
            sort_by=search_request.sort_by.value,
            page=search_request.page,
            per_page=search_request.per_page
        )

        # Calculate search time
        search_time_ms = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

        # Convert services to search result format
        search_results = []
        for service in services:
            # Calculate pricing information
            pricing_info = {
                "min_price": 0.0,
                "max_price": 0.0,
                "currency": "NGN",
                "price_display": "Contact for pricing"
            }

            if service.pricing_tiers:
                prices = [float(tier.price) for tier in service.pricing_tiers if tier.is_active]
                if prices:
                    pricing_info.update({
                        "min_price": min(prices),
                        "max_price": max(prices),
                        "price_display": f"₦{min(prices):,.0f} - ₦{max(prices):,.0f}"
                    })

            # Build search result
            search_result = SearchResultService(
                id=service.id,
                title=service.title,
                description=service.description,
                short_description=service.short_description,
                category={
                    "id": service.category.id if service.category else None,
                    "name": service.category.name if service.category else "Uncategorized",
                    "slug": service.category.slug if service.category else "uncategorized"
                },
                vendor={
                    "id": service.vendor.id,
                    "business_name": service.vendor.business_name,
                    "average_rating": float(service.vendor.average_rating or 0.0),
                    "total_reviews": service.vendor.total_reviews or 0,
                    "verification_status": service.vendor.verification_status
                },
                location={
                    "latitude": 6.5244,  # Default Lagos coordinates
                    "longitude": 3.3792,
                    "address": service.location,
                    "distance_km": None  # Would be calculated from search center
                } if service.location else None,
                pricing=pricing_info,
                average_rating=float(service.average_rating or 0.0),
                total_reviews=service.total_reviews or 0,
                duration_minutes=service.duration_minutes,
                max_participants=service.max_participants,
                tags=service.tags or [],
                has_availability=True,  # Would be calculated from availability data
                next_available_date=None,  # Would be calculated from availability data
                relevance_score=None,  # Would come from Elasticsearch
                boost_score=None,  # Would come from Elasticsearch
                featured_image_url=service.images[0].image_url if service.images else None,
                image_count=len(service.images) if service.images else 0
            )
            search_results.append(search_result)

        # Build metadata
        total_pages = (total_count + search_request.per_page - 1) // search_request.per_page
        metadata = SearchMetadata(
            total_results=total_count,
            page=search_request.page,
            per_page=search_request.per_page,
            total_pages=total_pages,
            search_time_ms=search_time_ms,
            has_more=search_request.page < total_pages,
            query=search_request.query,
            filters_applied={
                "location": search_request.location.model_dump() if search_request.location else None,
                "category_ids": search_request.category_ids,
                "price_range": search_request.price_range.model_dump() if search_request.price_range else None,
                "min_rating": search_request.min_rating,
                "availability_date": search_request.availability_date.isoformat() if search_request.availability_date else None,
                "tags": search_request.tags
            },
            sort_by=search_request.sort_by.value
        )

        # Build aggregations (simplified for now)
        search_aggregations = SearchAggregations(
            categories=aggregations.get("categories", {"buckets": []}),
            price_ranges=aggregations.get("price_ranges", {"buckets": []}),
            ratings=aggregations.get("ratings", {"buckets": []}),
            locations={"buckets": []}  # Would be populated with location aggregations
        )

        # Build response
        response = AdvancedSearchResponse(
            results=search_results,
            metadata=metadata,
            aggregations=search_aggregations,
            suggestions=[],  # Would be populated with search suggestions
            did_you_mean=None  # Would be populated with spell correction
        )

        logger.info(
            f"Advanced search completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "results_count": len(search_results),
                "total_count": total_count,
                "search_time_ms": search_time_ms
            }
        )

        return response

    except Exception as e:
        logger.error(
            f"Advanced search failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search operation failed: {str(e)}"
        )


@router.post(
    "/location",
    response_model=LocationSearchResponse,
    summary="Location-based Search",
    description="Search services by geographic location with radius filtering"
)
async def location_search(
    search_request: LocationSearchRequest,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user)
) -> LocationSearchResponse:
    """
    Search services by geographic location.

    - **latitude**: Search center latitude
    - **longitude**: Search center longitude
    - **radius_km**: Search radius in kilometers
    - **category_id**: Optional category filter
    - **limit**: Maximum results to return

    Returns services within the specified geographic area.
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Location search request",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "latitude": search_request.latitude,
                "longitude": search_request.longitude,
                "radius_km": search_request.radius_km
            }
        )

        # Initialize search repository
        search_repo = AdvancedSearchRepository(db)

        # Perform location search
        services = await search_repo.search_by_location(
            latitude=search_request.latitude,
            longitude=search_request.longitude,
            radius_km=search_request.radius_km,
            category_id=search_request.category_id,
            limit=search_request.limit
        )

        # Convert to search results (simplified)
        search_results = []
        for service in services:
            search_result = SearchResultService(
                id=service.id,
                title=service.title,
                description=service.description,
                short_description=service.short_description,
                category={
                    "id": service.category.id if service.category else None,
                    "name": service.category.name if service.category else "Uncategorized",
                    "slug": service.category.slug if service.category else "uncategorized"
                },
                vendor={
                    "id": service.vendor.id,
                    "business_name": service.vendor.business_name,
                    "average_rating": float(service.vendor.average_rating or 0.0),
                    "total_reviews": service.vendor.total_reviews or 0,
                    "verification_status": service.vendor.verification_status
                },
                pricing={
                    "min_price": 5000.0,  # Would be calculated from pricing tiers
                    "max_price": 15000.0,
                    "currency": "NGN",
                    "price_display": "₦5,000 - ₦15,000"
                },
                average_rating=float(service.average_rating or 0.0),
                total_reviews=service.total_reviews or 0,
                duration_minutes=service.duration_minutes,
                max_participants=service.max_participants,
                tags=service.tags or [],
                has_availability=True,
                featured_image_url=service.images[0].image_url if service.images else None,
                image_count=len(service.images) if service.images else 0
            )
            search_results.append(search_result)

        response = LocationSearchResponse(
            results=search_results,
            center={
                "latitude": search_request.latitude,
                "longitude": search_request.longitude,
                "radius_km": search_request.radius_km
            },
            total_found=len(search_results)
        )

        logger.info(
            f"Location search completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "results_count": len(search_results)
            }
        )

        return response

    except Exception as e:
        logger.error(
            f"Location search failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Location search failed: {str(e)}"
        )


@router.get(
    "/suggestions",
    response_model=SearchSuggestionResponse,
    summary="Search Suggestions",
    description="Get search suggestions and autocomplete for partial queries"
)
async def search_suggestions(
    query: str = Query(..., min_length=1, max_length=100, description="Partial search query"),
    limit: int = Query(default=10, ge=1, le=20, description="Maximum suggestions"),
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user)
) -> SearchSuggestionResponse:
    """
    Get search suggestions for autocomplete functionality.

    - **query**: Partial search query
    - **limit**: Maximum number of suggestions

    Returns list of suggested search terms.
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Search suggestions request",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "query": query,
                "limit": limit
            }
        )

        # For now, return simulated suggestions
        # In production, this would query Elasticsearch or a dedicated suggestions service
        suggestions = [
            f"{query} photography",
            f"{query} tours",
            f"{query} workshops",
            f"{query} events",
            f"{query} experiences",
            f"cultural {query}",
            f"traditional {query}",
            f"modern {query}",
            f"{query} in Lagos",
            f"{query} classes"
        ][:limit]

        response = SearchSuggestionResponse(
            suggestions=suggestions,
            query=query
        )

        logger.info(
            f"Search suggestions completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "suggestions_count": len(suggestions)
            }
        )

        return response

    except Exception as e:
        logger.error(
            f"Search suggestions failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search suggestions failed: {str(e)}"
        )


@router.get(
    "/popular",
    response_model=PopularSearchesResponse,
    summary="Popular Searches",
    description="Get popular search queries and trending content"
)
async def popular_searches(
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user)
) -> PopularSearchesResponse:
    """
    Get popular search queries and trending content.

    Returns popular search terms, trending categories, and featured services.
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Popular searches request",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None
            }
        )

        # For now, return simulated popular searches
        # In production, this would be based on actual search analytics
        popular_queries = [
            "photography services",
            "cultural tours",
            "traditional workshops",
            "wedding photography",
            "corporate events",
            "art classes",
            "music lessons",
            "dance workshops",
            "cooking classes",
            "language tutoring"
        ]

        trending_categories = [
            {"id": 1, "name": "Photography", "slug": "photography", "trend_score": 95},
            {"id": 2, "name": "Tours", "slug": "tours", "trend_score": 88},
            {"id": 3, "name": "Workshops", "slug": "workshops", "trend_score": 82},
            {"id": 4, "name": "Events", "slug": "events", "trend_score": 76},
            {"id": 5, "name": "Experiences", "slug": "experiences", "trend_score": 71}
        ]

        # Featured services would be fetched from database
        featured_services = []  # Simplified for now

        response = PopularSearchesResponse(
            popular_queries=popular_queries,
            trending_categories=trending_categories,
            featured_services=featured_services
        )

        logger.info(
            f"Popular searches completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "popular_queries_count": len(popular_queries),
                "trending_categories_count": len(trending_categories)
            }
        )

        return response

    except Exception as e:
        logger.error(
            f"Popular searches failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id if current_user else None,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Popular searches failed: {str(e)}"
        )


@router.get(
    "/health",
    summary="Search Service Health Check",
    description="Check the health of search services including Elasticsearch"
)
async def search_health_check(
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """
    Check the health of search services.

    Returns status of search infrastructure including Elasticsearch connectivity.
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Search health check request",
            extra={"correlation_id": correlation}
        )

        # Check database connectivity
        db_status = "healthy"
        try:
            await db.execute("SELECT 1")
        except Exception:
            db_status = "unhealthy"

        # Check Elasticsearch connectivity
        search_repo = AdvancedSearchRepository(db)
        es_status = "healthy" if await search_repo.elasticsearch_service.initialize_client() else "unhealthy"

        health_status = {
            "status": "healthy" if db_status == "healthy" else "degraded",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": {
                "database": db_status,
                "elasticsearch": es_status,
                "search_api": "healthy"
            },
            "version": "1.0.0"
        }

        status_code = 200 if health_status["status"] == "healthy" else 503

        logger.info(
            f"Search health check completed",
            extra={
                "correlation_id": correlation,
                "status": health_status["status"],
                "db_status": db_status,
                "es_status": es_status
            }
        )

        return JSONResponse(
            status_code=status_code,
            content=health_status
        )

    except Exception as e:
        logger.error(
            f"Search health check failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "error": str(e)
            }
        )
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }
        )
