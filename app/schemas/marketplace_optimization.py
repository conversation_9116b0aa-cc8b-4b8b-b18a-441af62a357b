"""
Marketplace Optimization schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic schemas for marketplace optimization including:
- SEO analysis request/response schemas with validation
- Performance metrics tracking and analytics schemas
- Competitive analysis insights and reporting schemas
- Optimization recommendations with implementation tracking
- Mobile optimization and preview generation schemas

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade Pydantic V2 validation and seamless API integration.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, ConfigDict, validator
from enum import Enum

from app.models.marketplace_optimization import (
    SEOScoreCategory, PerformanceMetricType, RecommendationType,
    RecommendationPriority, MobileOptimizationCategory
)


# Base schemas
class MarketplaceOptimizationBase(BaseModel):
    """Base schema for marketplace optimization models."""
    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1
        }
    })


# SEO Analysis Schemas
class SEOAnalysisCreate(MarketplaceOptimizationBase):
    """Schema for creating SEO analysis."""
    service_id: int = Field(..., description="Service ID for SEO analysis")
    vendor_id: int = Field(..., description="Vendor ID owning the service")
    analysis_version: str = Field(default="1.0", description="Analysis algorithm version")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1,
            "analysis_version": "1.0"
        }
    })


class SEOAnalysisUpdate(BaseModel):
    """Schema for updating SEO analysis."""
    overall_seo_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Overall SEO score")
    title_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Title optimization score")
    description_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Description quality score")
    media_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Media quality score")
    keyword_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Keyword optimization score")
    completeness_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Profile completeness score")
    primary_keywords: Optional[List[str]] = Field(None, description="Primary keywords identified")
    keyword_density: Optional[Dict[str, float]] = Field(None, description="Keyword density analysis")
    missing_keywords: Optional[List[str]] = Field(None, description="Recommended missing keywords")
    content_quality_metrics: Optional[Dict[str, Any]] = Field(None, description="Content quality metrics")
    readability_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Content readability score")
    meta_title_analysis: Optional[Dict[str, Any]] = Field(None, description="Meta title analysis")
    meta_description_analysis: Optional[Dict[str, Any]] = Field(None, description="Meta description analysis")

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "overall_seo_score": 75.50,
            "title_score": 80.00,
            "description_score": 70.00,
            "media_score": 85.00,
            "keyword_score": 65.00,
            "completeness_score": 90.00,
            "primary_keywords": ["cultural tour", "heritage experience", "local guide"],
            "keyword_density": {"cultural": 2.5, "tour": 1.8, "heritage": 1.2},
            "missing_keywords": ["authentic", "traditional", "immersive"],
            "readability_score": 78.00
        }
    })


class SEOAnalysisResponse(MarketplaceOptimizationBase):
    """Schema for SEO analysis response."""
    id: int
    service_id: int
    vendor_id: int
    overall_seo_score: Decimal
    title_score: Decimal
    description_score: Decimal
    media_score: Decimal
    keyword_score: Decimal
    completeness_score: Decimal
    primary_keywords: List[str]
    keyword_density: Dict[str, float]
    missing_keywords: List[str]
    content_quality_metrics: Dict[str, Any]
    readability_score: Decimal
    meta_title_analysis: Dict[str, Any]
    meta_description_analysis: Dict[str, Any]
    analysis_date: datetime
    analysis_version: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "id": 1,
            "service_id": 1,
            "vendor_id": 1,
            "overall_seo_score": 75.50,
            "title_score": 80.00,
            "description_score": 70.00,
            "media_score": 85.00,
            "keyword_score": 65.00,
            "completeness_score": 90.00,
            "primary_keywords": ["cultural tour", "heritage experience"],
            "keyword_density": {"cultural": 2.5, "tour": 1.8},
            "missing_keywords": ["authentic", "traditional"],
            "content_quality_metrics": {"word_count": 250, "sentence_length": 15},
            "readability_score": 78.00,
            "meta_title_analysis": {"length": 55, "keyword_present": True},
            "meta_description_analysis": {"length": 145, "call_to_action": True},
            "analysis_date": "2025-01-27T10:00:00Z",
            "analysis_version": "1.0"
        }
    })


# Performance Metrics Schemas
class PerformanceMetricsCreate(MarketplaceOptimizationBase):
    """Schema for creating performance metrics."""
    service_id: int = Field(..., description="Service ID for metrics tracking")
    vendor_id: int = Field(..., description="Vendor ID owning the service")
    metric_date: datetime = Field(..., description="Date for metrics recording")
    metric_period: str = Field(default="daily", description="Metric period type")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1,
            "metric_date": "2025-01-27T00:00:00Z",
            "metric_period": "daily"
        }
    })


class PerformanceMetricsUpdate(BaseModel):
    """Schema for updating performance metrics."""
    listing_views: Optional[int] = Field(None, ge=0, description="Number of listing views")
    unique_views: Optional[int] = Field(None, ge=0, description="Number of unique views")
    search_impressions: Optional[int] = Field(None, ge=0, description="Search result impressions")
    profile_visits: Optional[int] = Field(None, ge=0, description="Profile page visits")
    contact_clicks: Optional[int] = Field(None, ge=0, description="Contact button clicks")
    booking_inquiries: Optional[int] = Field(None, ge=0, description="Booking inquiries received")
    phone_clicks: Optional[int] = Field(None, ge=0, description="Phone number clicks")
    email_clicks: Optional[int] = Field(None, ge=0, description="Email clicks")
    conversion_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Conversion rate percentage")
    click_through_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Click-through rate")
    engagement_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Engagement rate")
    bounce_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Bounce rate percentage")
    average_time_on_page: Optional[Decimal] = Field(None, ge=0, description="Average time on page (seconds)")
    revenue_generated: Optional[Decimal] = Field(None, ge=0, description="Revenue generated")
    bookings_completed: Optional[int] = Field(None, ge=0, description="Completed bookings")
    traffic_sources: Optional[Dict[str, int]] = Field(None, description="Traffic source breakdown")
    device_breakdown: Optional[Dict[str, int]] = Field(None, description="Device type breakdown")
    geographic_data: Optional[Dict[str, Any]] = Field(None, description="Geographic distribution")

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "listing_views": 150,
            "unique_views": 120,
            "search_impressions": 500,
            "profile_visits": 80,
            "contact_clicks": 25,
            "booking_inquiries": 15,
            "conversion_rate": 10.00,
            "click_through_rate": 30.00,
            "engagement_rate": 65.00,
            "bounce_rate": 35.00,
            "average_time_on_page": 180.50,
            "revenue_generated": 2500.00,
            "bookings_completed": 12
        }
    })


class PerformanceMetricsResponse(MarketplaceOptimizationBase):
    """Schema for performance metrics response."""
    id: int
    service_id: int
    vendor_id: int
    metric_date: datetime
    metric_period: str
    listing_views: int
    unique_views: int
    search_impressions: int
    profile_visits: int
    contact_clicks: int
    booking_inquiries: int
    phone_clicks: int
    email_clicks: int
    conversion_rate: Decimal
    click_through_rate: Decimal
    engagement_rate: Decimal
    bounce_rate: Decimal
    average_time_on_page: Decimal
    revenue_generated: Decimal
    bookings_completed: int
    traffic_sources: Dict[str, int]
    device_breakdown: Dict[str, int]
    geographic_data: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "id": 1,
            "service_id": 1,
            "vendor_id": 1,
            "metric_date": "2025-01-27T00:00:00Z",
            "metric_period": "daily",
            "listing_views": 150,
            "unique_views": 120,
            "search_impressions": 500,
            "profile_visits": 80,
            "contact_clicks": 25,
            "booking_inquiries": 15,
            "phone_clicks": 8,
            "email_clicks": 5,
            "conversion_rate": 10.00,
            "click_through_rate": 30.00,
            "engagement_rate": 65.00,
            "bounce_rate": 35.00,
            "average_time_on_page": 180.50,
            "revenue_generated": 2500.00,
            "bookings_completed": 12,
            "traffic_sources": {"organic": 60, "direct": 30, "social": 10},
            "device_breakdown": {"mobile": 70, "desktop": 25, "tablet": 5},
            "geographic_data": {"lagos": 40, "abuja": 30, "kano": 20, "other": 10}
        }
    })


# Competitive Analysis Schemas
class CompetitiveAnalysisCreate(MarketplaceOptimizationBase):
    """Schema for creating competitive analysis."""
    service_id: int = Field(..., description="Service ID for competitive analysis")
    vendor_id: int = Field(..., description="Vendor ID owning the service")
    market_category: str = Field(..., description="Market category for analysis")
    geographic_scope: str = Field(default="local", description="Geographic scope of analysis")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1,
            "market_category": "cultural_tours",
            "geographic_scope": "local"
        }
    })


class CompetitiveAnalysisUpdate(BaseModel):
    """Schema for updating competitive analysis."""
    market_position_rank: Optional[int] = Field(None, ge=0, description="Market position ranking")
    total_competitors: Optional[int] = Field(None, ge=0, description="Total competitors analyzed")
    market_share_percentage: Optional[Decimal] = Field(None, ge=0, le=100, description="Market share percentage")
    price_position: Optional[str] = Field(None, description="Price position category")
    price_competitiveness_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Price competitiveness")
    average_competitor_price: Optional[Decimal] = Field(None, ge=0, description="Average competitor price")
    price_difference_percentage: Optional[Decimal] = Field(None, ge=-100, le=100, description="Price difference")
    quality_score_vs_competitors: Optional[Decimal] = Field(None, ge=0, le=100, description="Quality comparison")
    rating_vs_competitors: Optional[Decimal] = Field(None, ge=0, le=5, description="Rating comparison")
    review_count_vs_competitors: Optional[Decimal] = Field(None, description="Review count comparison")
    feature_comparison: Optional[Dict[str, Any]] = Field(None, description="Feature comparison data")
    unique_selling_points: Optional[List[str]] = Field(None, description="Unique selling points")
    competitive_gaps: Optional[List[str]] = Field(None, description="Competitive gaps identified")
    market_trends: Optional[Dict[str, Any]] = Field(None, description="Market trends analysis")
    competitor_strategies: Optional[Dict[str, Any]] = Field(None, description="Competitor strategies")
    market_opportunities: Optional[List[str]] = Field(None, description="Market opportunities")
    threat_analysis: Optional[Dict[str, Any]] = Field(None, description="Threat analysis")
    visibility_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Market visibility score")
    engagement_vs_competitors: Optional[Decimal] = Field(None, description="Engagement comparison")
    competitive_recommendations: Optional[List[str]] = Field(None, description="Strategic recommendations")

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "market_position_rank": 3,
            "total_competitors": 15,
            "market_share_percentage": 12.50,
            "price_position": "premium",
            "price_competitiveness_score": 75.00,
            "average_competitor_price": 15000.00,
            "price_difference_percentage": 20.00,
            "quality_score_vs_competitors": 85.00,
            "rating_vs_competitors": 4.2,
            "unique_selling_points": ["Authentic local guides", "Small group sizes"],
            "competitive_gaps": ["Limited online presence", "No mobile app"],
            "visibility_score": 70.00
        }
    })


class CompetitiveAnalysisResponse(MarketplaceOptimizationBase):
    """Schema for competitive analysis response."""
    id: int
    service_id: int
    vendor_id: int
    analysis_date: datetime
    market_category: str
    geographic_scope: str
    market_position_rank: int
    total_competitors: int
    market_share_percentage: Decimal
    price_position: str
    price_competitiveness_score: Decimal
    average_competitor_price: Decimal
    price_difference_percentage: Decimal
    quality_score_vs_competitors: Decimal
    rating_vs_competitors: Decimal
    review_count_vs_competitors: Decimal
    feature_comparison: Dict[str, Any]
    unique_selling_points: List[str]
    competitive_gaps: List[str]
    market_trends: Dict[str, Any]
    competitor_strategies: Dict[str, Any]
    market_opportunities: List[str]
    threat_analysis: Dict[str, Any]
    visibility_score: Decimal
    engagement_vs_competitors: Decimal
    competitive_recommendations: List[str]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "id": 1,
            "service_id": 1,
            "vendor_id": 1,
            "analysis_date": "2025-01-27T10:00:00Z",
            "market_category": "cultural_tours",
            "geographic_scope": "local",
            "market_position_rank": 3,
            "total_competitors": 15,
            "market_share_percentage": 12.50,
            "price_position": "premium",
            "price_competitiveness_score": 75.00,
            "average_competitor_price": 15000.00,
            "price_difference_percentage": 20.00,
            "quality_score_vs_competitors": 85.00,
            "rating_vs_competitors": 4.2,
            "review_count_vs_competitors": 150.00,
            "feature_comparison": {"guided_tours": True, "group_size": "small", "duration": "half_day"},
            "unique_selling_points": ["Authentic local guides", "Small group sizes"],
            "competitive_gaps": ["Limited online presence", "No mobile app"],
            "market_trends": {"growth_rate": 15.5, "seasonal_demand": "high"},
            "competitor_strategies": {"pricing": "competitive", "marketing": "social_media"},
            "market_opportunities": ["Digital marketing expansion", "Mobile app development"],
            "threat_analysis": {"new_entrants": "medium", "price_competition": "high"},
            "visibility_score": 70.00,
            "engagement_vs_competitors": 25.00,
            "competitive_recommendations": ["Improve online presence", "Develop mobile strategy"]
        }
    })


# Optimization Recommendation Schemas
class OptimizationRecommendationCreate(MarketplaceOptimizationBase):
    """Schema for creating optimization recommendations."""
    service_id: int = Field(..., description="Service ID for recommendation")
    vendor_id: int = Field(..., description="Vendor ID receiving recommendation")
    recommendation_type: str = Field(..., description="Type of recommendation")
    priority: str = Field(default="medium", description="Recommendation priority")
    title: str = Field(..., max_length=200, description="Recommendation title")
    description: str = Field(..., description="Detailed recommendation description")
    expected_impact_score: Decimal = Field(default=0, ge=0, le=100, description="Expected impact score")
    implementation_effort: str = Field(default="medium", description="Implementation effort level")
    estimated_time_hours: Decimal = Field(default=0, ge=0, description="Estimated time in hours")
    implementation_steps: List[str] = Field(default_factory=list, description="Implementation steps")
    required_resources: List[str] = Field(default_factory=list, description="Required resources")
    success_metrics: List[str] = Field(default_factory=list, description="Success measurement metrics")
    generated_by: str = Field(default="system", description="Recommendation source")
    algorithm_version: str = Field(default="1.0", description="Algorithm version")
    confidence_score: Decimal = Field(default=0, ge=0, le=100, description="Confidence score")
    related_analysis_data: Dict[str, Any] = Field(default_factory=dict, description="Related analysis data")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1,
            "recommendation_type": "seo_improvement",
            "priority": "high",
            "title": "Improve Service Title SEO",
            "description": "Optimize your service title to include primary keywords and improve search visibility",
            "expected_impact_score": 25.00,
            "implementation_effort": "low",
            "estimated_time_hours": 2.00,
            "implementation_steps": ["Research target keywords", "Rewrite title", "Update listing"],
            "required_resources": ["Keyword research tool", "Content writer"],
            "success_metrics": ["SEO score improvement", "Search ranking increase"],
            "generated_by": "ai",
            "confidence_score": 85.00
        }
    })


class OptimizationRecommendationUpdate(BaseModel):
    """Schema for updating optimization recommendations."""
    status: Optional[str] = Field(None, description="Implementation status")
    implementation_date: Optional[datetime] = Field(None, description="Implementation start date")
    completion_date: Optional[datetime] = Field(None, description="Implementation completion date")
    actual_impact_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Actual impact achieved")
    implementation_notes: Optional[str] = Field(None, description="Implementation notes")

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "status": "completed",
            "implementation_date": "2025-01-25T09:00:00Z",
            "completion_date": "2025-01-27T17:00:00Z",
            "actual_impact_score": 28.50,
            "implementation_notes": "Successfully implemented with better results than expected"
        }
    })


class OptimizationRecommendationResponse(MarketplaceOptimizationBase):
    """Schema for optimization recommendation response."""
    id: int
    service_id: int
    vendor_id: int
    recommendation_type: str
    priority: str
    title: str
    description: str
    expected_impact_score: Decimal
    implementation_effort: str
    estimated_time_hours: Decimal
    implementation_steps: List[str]
    required_resources: List[str]
    success_metrics: List[str]
    status: str
    implementation_date: Optional[datetime]
    completion_date: Optional[datetime]
    actual_impact_score: Optional[Decimal]
    implementation_notes: Optional[str]
    generated_by: str
    algorithm_version: str
    confidence_score: Decimal
    related_analysis_data: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "id": 1,
            "service_id": 1,
            "vendor_id": 1,
            "recommendation_type": "seo_improvement",
            "priority": "high",
            "title": "Improve Service Title SEO",
            "description": "Optimize your service title to include primary keywords",
            "expected_impact_score": 25.00,
            "implementation_effort": "low",
            "estimated_time_hours": 2.00,
            "implementation_steps": ["Research keywords", "Rewrite title", "Update listing"],
            "required_resources": ["Keyword tool", "Content writer"],
            "success_metrics": ["SEO score improvement", "Search ranking increase"],
            "status": "pending",
            "implementation_date": None,
            "completion_date": None,
            "actual_impact_score": None,
            "implementation_notes": None,
            "generated_by": "ai",
            "algorithm_version": "1.0",
            "confidence_score": 85.00,
            "related_analysis_data": {"current_seo_score": 65.0, "target_keywords": ["cultural tour"]}
        }
    })


# Mobile Optimization Schemas
class MobileOptimizationCreate(MarketplaceOptimizationBase):
    """Schema for creating mobile optimization analysis."""
    service_id: int = Field(..., description="Service ID for mobile optimization")
    vendor_id: int = Field(..., description="Vendor ID owning the service")
    device_type: str = Field(default="mobile", description="Device type for analysis")
    screen_size_category: str = Field(default="standard", description="Screen size category")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "vendor_id": 1,
            "device_type": "mobile",
            "screen_size_category": "standard"
        }
    })


class MobileOptimizationUpdate(BaseModel):
    """Schema for updating mobile optimization analysis."""
    overall_mobile_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Overall mobile score")
    responsive_design_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Responsive design score")
    loading_speed_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Loading speed score")
    page_load_time_ms: Optional[int] = Field(None, ge=0, description="Page load time in milliseconds")
    touch_interface_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Touch interface score")
    image_optimization_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Image optimization score")
    navigation_ux_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Navigation UX score")
    mobile_conversion_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Mobile conversion rate")
    mobile_bounce_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Mobile bounce rate")
    mobile_engagement_score: Optional[Decimal] = Field(None, ge=0, le=100, description="Mobile engagement score")
    mobile_recommendations: Optional[List[str]] = Field(None, description="Mobile optimization recommendations")

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "overall_mobile_score": 78.50,
            "responsive_design_score": 85.00,
            "loading_speed_score": 72.00,
            "page_load_time_ms": 2500,
            "touch_interface_score": 80.00,
            "image_optimization_score": 75.00,
            "navigation_ux_score": 82.00,
            "mobile_conversion_rate": 8.50,
            "mobile_bounce_rate": 42.00,
            "mobile_engagement_score": 68.00
        }
    })


class MobileOptimizationResponse(MarketplaceOptimizationBase):
    """Schema for mobile optimization response."""
    id: int
    service_id: int
    vendor_id: int
    analysis_date: datetime
    device_type: str
    screen_size_category: str
    overall_mobile_score: Decimal
    responsive_design_score: Decimal
    loading_speed_score: Decimal
    page_load_time_ms: int
    touch_interface_score: Decimal
    image_optimization_score: Decimal
    navigation_ux_score: Decimal
    mobile_conversion_rate: Decimal
    mobile_bounce_rate: Decimal
    mobile_engagement_score: Decimal
    mobile_recommendations: List[str]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "id": 1,
            "service_id": 1,
            "vendor_id": 1,
            "analysis_date": "2025-01-27T10:00:00Z",
            "device_type": "mobile",
            "screen_size_category": "standard",
            "overall_mobile_score": 78.50,
            "responsive_design_score": 85.00,
            "loading_speed_score": 72.00,
            "page_load_time_ms": 2500,
            "touch_interface_score": 80.00,
            "image_optimization_score": 75.00,
            "navigation_ux_score": 82.00,
            "mobile_conversion_rate": 8.50,
            "mobile_bounce_rate": 42.00,
            "mobile_engagement_score": 68.00,
            "mobile_recommendations": ["Improve loading speed", "Optimize images"]
        }
    })


# Request/Response Collections
class MarketplaceOptimizationDashboard(BaseModel):
    """Comprehensive marketplace optimization dashboard response."""
    seo_analysis: Optional[SEOAnalysisResponse] = None
    performance_metrics: Optional[PerformanceMetricsResponse] = None
    competitive_analysis: Optional[CompetitiveAnalysisResponse] = None
    recommendations: List[OptimizationRecommendationResponse] = Field(default_factory=list)
    mobile_optimization: Optional[MobileOptimizationResponse] = None
    overall_optimization_score: Decimal = Field(default=0, ge=0, le=100)

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "seo_analysis": {"overall_seo_score": 75.50},
            "performance_metrics": {"conversion_rate": 10.00},
            "competitive_analysis": {"market_position_rank": 3},
            "recommendations": [{"title": "Improve SEO", "priority": "high"}],
            "mobile_optimization": {"overall_mobile_score": 78.50},
            "overall_optimization_score": 76.25
        }
    })


class OptimizationAnalysisRequest(BaseModel):
    """Request schema for comprehensive optimization analysis."""
    service_id: int = Field(..., description="Service ID to analyze")
    analysis_types: List[str] = Field(
        default=["seo", "performance", "competitive", "mobile"],
        description="Types of analysis to perform"
    )
    include_recommendations: bool = Field(default=True, description="Include optimization recommendations")
    analysis_depth: str = Field(default="standard", description="Analysis depth: basic, standard, comprehensive")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "service_id": 1,
            "analysis_types": ["seo", "performance", "competitive", "mobile"],
            "include_recommendations": True,
            "analysis_depth": "comprehensive"
        }
    })
