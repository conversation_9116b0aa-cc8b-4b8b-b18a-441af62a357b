"""
Vendor models for Culture Connect Backend API.

This module defines vendor-related models for marketplace service providers,
including vendor registration, profile management, and document verification.
"""

import enum
from datetime import datetime, time
from decimal import Decimal
from typing import Optional, List, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Numeric, Enum, JSON, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit
from app.core.security import UserRole


class VendorType(str, enum.Enum):
    """Enumeration of vendor business types."""
    GUIDE = "guide"
    RESTAURANT = "restaurant"
    HOTEL = "hotel"
    TRANSPORT = "transport"
    ACTIVITY_PROVIDER = "activity_provider"
    PRIVATE_BEACH = "private_beach"
    APARTMENT_RENTAL = "apartment_rental"
    CAR_HIRE = "car_hire"
    SECURITY_SERVICE = "security_service"
    OTHER = "other"


class VerificationStatus(str, enum.Enum):
    """Enumeration of vendor verification statuses."""
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    VERIFIED = "verified"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    RESUBMISSION_REQUIRED = "resubmission_required"


class MarketplaceStatus(str, enum.Enum):
    """Enumeration of vendor marketplace statuses."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PAUSED = "paused"
    SUSPENDED = "suspended"
    BANNED = "banned"


class DocumentType(str, enum.Enum):
    """Enumeration of vendor document types."""
    BUSINESS_LICENSE = "business_license"
    TAX_CERTIFICATE = "tax_certificate"
    IDENTITY_DOCUMENT = "identity_document"
    PROFESSIONAL_CERTIFICATION = "professional_certification"
    INSURANCE_CERTIFICATE = "insurance_certificate"
    BANK_STATEMENT = "bank_statement"
    UTILITY_BILL = "utility_bill"
    OTHER = "other"


class DocumentStatus(str, enum.Enum):
    """Enumeration of document verification statuses."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


class Vendor(BaseModelWithAudit):
    """
    Vendor model for marketplace service providers.

    This model stores core vendor information including business details,
    verification status, and marketplace performance metrics.
    """

    __tablename__ = "vendors"

    # Core vendor information
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        doc="Associated user account ID"
    )

    business_name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Official business name"
    )

    business_type = Column(
        Enum(VendorType),
        nullable=False,
        index=True,
        doc="Type of business/service provider"
    )

    business_registration_number = Column(
        String(100),
        nullable=True,
        unique=True,
        index=True,
        doc="Official business registration number"
    )

    tax_id = Column(
        String(50),
        nullable=True,
        unique=True,
        index=True,
        doc="Tax identification number"
    )

    # Marketplace-specific fields
    verification_status = Column(
        Enum(VerificationStatus),
        nullable=False,
        default=VerificationStatus.PENDING,
        index=True,
        doc="Current verification status"
    )

    marketplace_status = Column(
        Enum(MarketplaceStatus),
        nullable=False,
        default=MarketplaceStatus.INACTIVE,
        index=True,
        doc="Current marketplace status"
    )

    onboarding_completed = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether onboarding process is completed"
    )

    onboarding_step = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Current step in onboarding process (1-6)"
    )

    # Performance metrics
    commission_rate = Column(
        Numeric(5, 4),
        nullable=False,
        default=Decimal("0.15"),
        doc="Platform commission rate (0.15 = 15%)"
    )

    total_earnings = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Total earnings from marketplace"
    )

    total_bookings = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of bookings received"
    )

    average_rating = Column(
        Numeric(3, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average customer rating (0.00-5.00)"
    )

    total_reviews = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of customer reviews"
    )

    response_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Response rate percentage (0.00-100.00)"
    )

    response_time_hours = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("24.00"),
        doc="Average response time in hours"
    )

    # SEO and optimization
    seo_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="SEO optimization score (0.00-100.00)"
    )

    marketplace_ranking = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Current marketplace ranking position"
    )

    listing_quality_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Listing quality score (0.00-100.00)"
    )

    # Verification and compliance
    verified_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp when vendor was verified"
    )

    verification_notes = Column(
        Text,
        nullable=True,
        doc="Notes from verification process"
    )

    last_activity_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Last activity timestamp"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="vendor",
        foreign_keys=[user_id],
        doc="Associated user account"
    )

    profile = relationship(
        "VendorProfile",
        back_populates="vendor",
        uselist=False,
        cascade="all, delete-orphan",
        doc="Vendor profile information"
    )

    documents = relationship(
        "VendorDocument",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Verification documents"
    )

    # Task 3.2.1: Service relationships
    services = relationship(
        "Service",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor services"
    )

    # Task 3.2.2: Marketplace optimization relationships (TODO: Implement these models)
    # seo_analyses = relationship(
    #     "SEOAnalysis",
    #     back_populates="vendor",
    #     cascade="all, delete-orphan",
    #     doc="SEO analysis records"
    # )

    # performance_metrics = relationship(
    #     "PerformanceMetrics",
    #     back_populates="vendor",
    #     cascade="all, delete-orphan",
    #     doc="Performance metrics records"
    # )

    # competitive_analyses = relationship(
    #     "CompetitiveAnalysis",
    #     back_populates="vendor",
    #     cascade="all, delete-orphan",
    #     doc="Competitive analysis records"
    # )

    # optimization_recommendations = relationship(
    #     "OptimizationRecommendation",
    #     back_populates="vendor",
    #     cascade="all, delete-orphan",
    #     doc="Optimization recommendations"
    # )

    # mobile_optimizations = relationship(
    #     "MobileOptimization",
    #     back_populates="vendor",
    #     cascade="all, delete-orphan",
    #     doc="Mobile optimization records"
    # )

    # Task 3.3.1: Vendor dashboard relationships
    dashboard_metrics = relationship(
        "VendorDashboardMetrics",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor dashboard metrics"
    )

    activity_feed = relationship(
        "VendorActivityFeed",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor activity feed"
    )

    notifications = relationship(
        "VendorNotification",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor notifications"
    )

    quick_actions = relationship(
        "VendorQuickAction",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor quick actions"
    )

    analytics = relationship(
        "VendorAnalytics",
        back_populates="vendor",
        foreign_keys="VendorAnalytics.vendor_id",
        cascade="all, delete-orphan",
        doc="Vendor analytics"
    )

    # Booking relationships (implemented in Task 4.1.1)
    bookings = relationship(
        "Booking",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Vendor bookings"
    )

    # Payment relationships (implemented in Phase 1 - Payment & Transaction Management System)
    payments = relationship(
        "Payment",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Payments received by this vendor"
    )

    payouts = relationship(
        "VendorPayout",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Payouts to this vendor"
    )

    # Review relationships (Task 4.4.1)
    reviews = relationship(
        "Review",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Reviews for this vendor"
    )

    # Promotional relationships (Phase 5 - Promotional & Advertising System)
    campaigns = relationship(
        "Campaign",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Promotional campaigns created by this vendor"
    )

    promotional_listings = relationship(
        "PromotionalListing",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Featured promotional listings for this vendor"
    )

    ad_spends = relationship(
        "AdSpend",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Advertisement spending records for this vendor"
    )

    review_responses = relationship(
        "ReviewResponse",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Responses to reviews by this vendor"
    )

    review_analytics = relationship(
        "ReviewAnalytics",
        back_populates="vendor",
        cascade="all, delete-orphan",
        doc="Review analytics for this vendor"
    )

    # TODO: Add relationships for campaigns, etc. in later phases
    # campaigns = relationship("Campaign", back_populates="vendor")

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "onboarding_step >= 1 AND onboarding_step <= 6",
            name="check_onboarding_step_range"
        ),
        CheckConstraint(
            "commission_rate >= 0.0 AND commission_rate <= 1.0",
            name="check_commission_rate_range"
        ),
        CheckConstraint(
            "average_rating >= 0.0 AND average_rating <= 5.0",
            name="check_average_rating_range"
        ),
        CheckConstraint(
            "response_rate >= 0.0 AND response_rate <= 100.0",
            name="check_response_rate_range"
        ),
        CheckConstraint(
            "seo_score >= 0.0 AND seo_score <= 100.0",
            name="check_seo_score_range"
        ),
        CheckConstraint(
            "listing_quality_score >= 0.0 AND listing_quality_score <= 100.0",
            name="check_listing_quality_score_range"
        ),

        # Performance indexes
        Index("idx_vendor_business_type_status", "business_type", "marketplace_status"),
        Index("idx_vendor_verification_status", "verification_status", "created_at"),
        Index("idx_vendor_marketplace_ranking", "marketplace_ranking", "business_type"),
        Index("idx_vendor_performance", "average_rating", "total_bookings"),
        Index("idx_vendor_activity", "last_activity_at", "marketplace_status"),
        Index("idx_vendor_onboarding", "onboarding_completed", "onboarding_step"),
    )

    def __repr__(self) -> str:
        """String representation of the vendor."""
        return f"<Vendor(id={self.id}, business_name='{self.business_name}', type='{self.business_type}')>"

    @property
    def is_verified(self) -> bool:
        """Check if vendor is verified."""
        return self.verification_status == VerificationStatus.VERIFIED

    @property
    def is_active(self) -> bool:
        """Check if vendor is active in marketplace."""
        return self.marketplace_status == MarketplaceStatus.ACTIVE

    @property
    def completion_percentage(self) -> float:
        """Calculate profile completion percentage."""
        if not self.profile:
            return 0.0

        # Basic completion factors
        factors = [
            bool(self.business_name),
            bool(self.business_type),
            bool(self.profile.description),
            bool(self.profile.contact_phone),
            bool(self.profile.business_address),
            self.onboarding_completed,
            self.is_verified,
        ]

        return (sum(factors) / len(factors)) * 100.0


class VendorProfile(BaseModelWithAudit):
    """
    Vendor profile model for detailed business information.

    This model stores comprehensive vendor profile data including
    business description, contact information, and operational details.
    """

    __tablename__ = "vendor_profiles"

    # Foreign key to vendor
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        doc="Associated vendor ID"
    )

    # Business description and story
    description = Column(
        Text,
        nullable=True,
        doc="Business description and story"
    )

    short_description = Column(
        String(500),
        nullable=True,
        doc="Short business description for listings"
    )

    tagline = Column(
        String(255),
        nullable=True,
        doc="Business tagline or slogan"
    )

    # Contact information
    contact_phone = Column(
        String(20),
        nullable=True,
        index=True,
        doc="Primary contact phone number"
    )

    contact_email = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Primary contact email address"
    )

    website_url = Column(
        String(255),
        nullable=True,
        doc="Business website URL"
    )

    # Business address
    business_address = Column(
        Text,
        nullable=True,
        doc="Complete business address"
    )

    city = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Business city"
    )

    state = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Business state/region"
    )

    country = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Business country"
    )

    postal_code = Column(
        String(20),
        nullable=True,
        doc="Business postal/zip code"
    )

    latitude = Column(
        Numeric(10, 8),
        nullable=True,
        doc="Business location latitude"
    )

    longitude = Column(
        Numeric(11, 8),
        nullable=True,
        doc="Business location longitude"
    )

    # Operating hours (stored as JSON)
    operating_hours = Column(
        JSON,
        nullable=True,
        doc="Operating hours by day of week"
    )

    timezone = Column(
        String(50),
        nullable=True,
        default="UTC",
        doc="Business timezone"
    )

    # Social media and online presence
    social_media_links = Column(
        JSON,
        nullable=True,
        doc="Social media profile links"
    )

    # Business capabilities and features
    languages_spoken = Column(
        JSON,
        nullable=True,
        doc="Languages spoken by business"
    )

    specializations = Column(
        JSON,
        nullable=True,
        doc="Business specializations and expertise"
    )

    certifications = Column(
        JSON,
        nullable=True,
        doc="Professional certifications"
    )

    # Capacity and logistics
    max_group_size = Column(
        Integer,
        nullable=True,
        doc="Maximum group size that can be accommodated"
    )

    min_advance_booking = Column(
        Integer,
        nullable=True,
        default=24,
        doc="Minimum advance booking hours required"
    )

    cancellation_policy = Column(
        Text,
        nullable=True,
        doc="Cancellation policy details"
    )

    # Media and content
    logo_url = Column(
        String(500),
        nullable=True,
        doc="Business logo image URL"
    )

    cover_image_url = Column(
        String(500),
        nullable=True,
        doc="Cover image URL"
    )

    gallery_images = Column(
        JSON,
        nullable=True,
        doc="Gallery image URLs"
    )

    # Business verification details
    years_in_business = Column(
        Integer,
        nullable=True,
        doc="Number of years in business"
    )

    business_license_number = Column(
        String(100),
        nullable=True,
        doc="Business license number"
    )

    insurance_details = Column(
        JSON,
        nullable=True,
        doc="Business insurance information"
    )

    # Marketplace preferences
    auto_accept_bookings = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether to automatically accept bookings"
    )

    instant_booking_enabled = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether instant booking is enabled"
    )

    # Additional metadata
    additional_info = Column(
        JSON,
        nullable=True,
        doc="Additional business information"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="profile",
        doc="Associated vendor"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "max_group_size > 0",
            name="check_max_group_size_positive"
        ),
        CheckConstraint(
            "min_advance_booking >= 0",
            name="check_min_advance_booking_non_negative"
        ),
        CheckConstraint(
            "years_in_business >= 0",
            name="check_years_in_business_non_negative"
        ),
        CheckConstraint(
            "latitude >= -90.0 AND latitude <= 90.0",
            name="check_latitude_range"
        ),
        CheckConstraint(
            "longitude >= -180.0 AND longitude <= 180.0",
            name="check_longitude_range"
        ),

        # Performance indexes
        Index("idx_vendor_profile_location", "city", "state", "country"),
        Index("idx_vendor_profile_coordinates", "latitude", "longitude"),
        Index("idx_vendor_profile_contact", "contact_phone", "contact_email"),
    )

    def __repr__(self) -> str:
        """String representation of the vendor profile."""
        return f"<VendorProfile(vendor_id={self.vendor_id}, city='{self.city}')>"


class VendorDocument(BaseModelWithAudit):
    """
    Vendor document model for verification documents.

    This model stores uploaded documents for vendor verification
    including document metadata, status, and review information.
    """

    __tablename__ = "vendor_documents"

    # Foreign key to vendor
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    # Document information
    document_type = Column(
        Enum(DocumentType),
        nullable=False,
        index=True,
        doc="Type of document"
    )

    document_name = Column(
        String(255),
        nullable=False,
        doc="Original document filename"
    )

    document_url = Column(
        String(500),
        nullable=False,
        doc="Document file URL"
    )

    file_size = Column(
        Integer,
        nullable=False,
        doc="Document file size in bytes"
    )

    file_type = Column(
        String(50),
        nullable=False,
        doc="Document MIME type"
    )

    # Document status and verification
    status = Column(
        Enum(DocumentStatus),
        nullable=False,
        default=DocumentStatus.PENDING,
        index=True,
        doc="Document verification status"
    )

    reviewed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp when document was reviewed"
    )

    reviewed_by = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        doc="ID of admin who reviewed the document"
    )

    review_notes = Column(
        Text,
        nullable=True,
        doc="Review notes and feedback"
    )

    # Document metadata
    expiry_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Document expiry date (if applicable)"
    )

    document_number = Column(
        String(100),
        nullable=True,
        doc="Document number or identifier"
    )

    issuing_authority = Column(
        String(255),
        nullable=True,
        doc="Authority that issued the document"
    )

    # Verification details
    verification_data = Column(
        JSON,
        nullable=True,
        doc="Additional verification data"
    )

    is_primary = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the primary document of its type"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="documents",
        doc="Associated vendor"
    )

    reviewer = relationship(
        "User",
        foreign_keys=[reviewed_by],
        doc="Admin who reviewed the document"
    )

    # Constraints and indexes
    __table_args__ = (
        # Unique constraint for primary documents
        UniqueConstraint(
            "vendor_id", "document_type", "is_primary",
            name="uq_vendor_primary_document"
        ),

        # Performance indexes
        Index("idx_vendor_document_type_status", "document_type", "status"),
        Index("idx_vendor_document_expiry", "expiry_date", "status"),
        Index("idx_vendor_document_review", "reviewed_at", "reviewed_by"),
    )

    def __repr__(self) -> str:
        """String representation of the vendor document."""
        return f"<VendorDocument(id={self.id}, vendor_id={self.vendor_id}, type='{self.document_type}')>"

    @property
    def is_expired(self) -> bool:
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        return datetime.utcnow() > self.expiry_date.replace(tzinfo=None)

    @property
    def is_approved(self) -> bool:
        """Check if document is approved."""
        return self.status == DocumentStatus.APPROVED

    @property
    def days_until_expiry(self) -> Optional[int]:
        """Calculate days until document expires."""
        if not self.expiry_date:
            return None

        delta = self.expiry_date.replace(tzinfo=None) - datetime.utcnow()
        return delta.days if delta.days > 0 else 0
