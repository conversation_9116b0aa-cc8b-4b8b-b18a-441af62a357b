"""
Email models for Culture Connect Backend API.

This module defines comprehensive email management models including:
- EmailTemplate: Reusable email templates with Jinja2 support and versioning
- EmailDelivery: Email delivery tracking with status and analytics
- EmailPreference: User email notification preferences and opt-out management
- EmailQueue: Queued email management for batch processing and scheduling

Implements Task 2.3.1 requirements for email service implementation with
production-grade PostgreSQL optimization and seamless integration with
existing authentication infrastructure.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum as PyEnum
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Enum, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import Base


class EmailTemplateCategory(PyEnum):
    """Email template categories for organization and access control."""
    VERIFICATION = "verification"
    PASSWORD_RESET = "password_reset"
    NOTIFICATION = "notification"
    MARKETING = "marketing"
    BOOKING = "booking"
    SECURITY = "security"
    SYSTEM = "system"


class EmailDeliveryStatus(PyEnum):
    """Email delivery status tracking."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    BOUNCED = "bounced"
    REJECTED = "rejected"


class EmailQueueStatus(PyEnum):
    """Email queue processing status."""
    QUEUED = "queued"
    PROCESSING = "processing"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY = "retry"


class EmailTemplate(Base):
    """
    Email template model for reusable email templates with Jinja2 support.

    Provides template management with versioning, categorization, and
    variable substitution for consistent email communications.
    """
    __tablename__ = "email_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    category = Column(Enum(EmailTemplateCategory), nullable=False, index=True)
    subject_template = Column(Text, nullable=False)
    body_template = Column(Text, nullable=False)
    html_template = Column(Text, nullable=True)
    variables = Column(JSON, nullable=False, default=dict)
    version = Column(Integer, nullable=False, default=1)
    is_active = Column(Boolean, nullable=False, default=True, index=True)

    # Audit fields
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    created_by_user = relationship("User", back_populates="created_email_templates")
    email_deliveries = relationship("EmailDelivery", back_populates="template")
    email_queue_items = relationship("EmailQueue", back_populates="template")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_email_template_category_active', 'category', 'is_active'),
        Index('idx_email_template_name_version', 'name', 'version'),
        UniqueConstraint('name', 'version', name='uq_email_template_name_version'),
    )

    def __repr__(self) -> str:
        return f"<EmailTemplate(id={self.id}, name='{self.name}', category='{self.category.value}', version={self.version})>"


class EmailDelivery(Base):
    """
    Email delivery tracking model for monitoring email status and analytics.

    Tracks email delivery lifecycle from sending to final status with
    comprehensive metadata for performance monitoring and debugging.
    """
    __tablename__ = "email_deliveries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("email_templates.id"), nullable=True, index=True)
    recipient_email = Column(String(255), nullable=False, index=True)
    subject = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)
    html_body = Column(Text, nullable=True)
    priority = Column(Integer, nullable=False, default=3, index=True)  # 1=highest, 5=lowest
    status = Column(Enum(EmailDeliveryStatus), nullable=False, default=EmailDeliveryStatus.PENDING, index=True)

    # Delivery tracking
    error_message = Column(Text, nullable=True)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    opened_at = Column(DateTime(timezone=True), nullable=True)  # Future email tracking

    # Metadata and correlation
    email_metadata = Column(JSON, nullable=False, default=dict)
    correlation_id = Column(String(255), nullable=True, index=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="email_deliveries")
    template = relationship("EmailTemplate", back_populates="email_deliveries")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_email_delivery_user_status', 'user_id', 'status'),
        Index('idx_email_delivery_created_status', 'created_at', 'status'),
        Index('idx_email_delivery_recipient_status', 'recipient_email', 'status'),
    )

    def __repr__(self) -> str:
        return f"<EmailDelivery(id={self.id}, recipient='{self.recipient_email}', status='{self.status.value}')>"


class EmailPreference(Base):
    """
    User email notification preferences and opt-out management.

    Provides granular control over email notifications with GDPR compliance
    and integration with user profile management.
    """
    __tablename__ = "email_preferences"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True, index=True)

    # Notification preferences
    verification_emails = Column(Boolean, nullable=False, default=True)
    security_emails = Column(Boolean, nullable=False, default=True)
    marketing_emails = Column(Boolean, nullable=False, default=False)
    booking_notifications = Column(Boolean, nullable=False, default=True)
    vendor_notifications = Column(Boolean, nullable=False, default=True)
    system_notifications = Column(Boolean, nullable=False, default=True)

    # Opt-out tracking
    opted_out = Column(Boolean, nullable=False, default=False)
    opt_out_reason = Column(Text, nullable=True)
    opt_out_date = Column(DateTime(timezone=True), nullable=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="email_preferences", uselist=False)

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_email_preference_user', 'user_id'),
    )

    def __repr__(self) -> str:
        return f"<EmailPreference(id={self.id}, user_id={self.user_id})>"


class EmailQueue(Base):
    """
    Email queue model for batch processing and scheduled email delivery.

    Manages email queue with priority-based processing, retry logic,
    and integration with Celery for background task processing.
    """
    __tablename__ = "email_queue"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("email_templates.id"), nullable=True, index=True)

    # Email content
    recipient_email = Column(String(255), nullable=False, index=True)
    subject = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)
    html_body = Column(Text, nullable=True)

    # Queue management
    priority = Column(Integer, nullable=False, default=3, index=True)  # 1=highest, 5=lowest
    scheduled_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, index=True)
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    status = Column(Enum(EmailQueueStatus), nullable=False, default=EmailQueueStatus.QUEUED, index=True)

    # Error tracking
    error_message = Column(Text, nullable=True)

    # Metadata and correlation
    queue_metadata = Column(JSON, nullable=False, default=dict)

    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="email_queue_items")
    template = relationship("EmailTemplate", back_populates="email_queue_items")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_email_queue_status_priority', 'status', 'priority'),
        Index('idx_email_queue_scheduled', 'scheduled_at', 'status'),
        Index('idx_email_queue_retries', 'retry_count', 'max_retries', 'status'),
    )

    def __repr__(self) -> str:
        return f"<EmailQueue(id={self.id}, recipient='{self.recipient_email}', status='{self.status.value}', priority={self.priority})>"
