"""
User Profile Management Service for Culture Connect Backend API.

This module provides comprehensive user profile management services including:
- Profile CRUD operations with validation
- Profile image upload and optimization
- User preferences management
- Privacy controls implementation
- Account settings management

Implements Task 2.2.2 requirements for complete user profile management
with production-grade error handling and security features.
"""

import logging
import os
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update
from fastapi import HTTPException, status, UploadFile
from PIL import Image
import aiofiles

from app.models.user import User
from app.repositories.auth_repository import UserRepository
from app.schemas.user_profile import (
    UserProfileUpdate, UserPreferences, UserPreferencesUpdate,
    ProfileImageUpload, ProfileImageResponse, AccountSettings, AccountSettingsUpdate
)
from app.schemas.auth import UserResponse
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class UserProfileService:
    """
    Comprehensive user profile management service.

    Provides production-grade user profile operations including CRUD operations,
    image management, preferences handling, and privacy controls with proper
    error handling and security validation.
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.user_repository = UserRepository(db)

    async def get_user_profile(self, user_id: int) -> UserResponse:
        """
        Get complete user profile information.

        Args:
            user_id: User ID to retrieve profile for

        Returns:
            UserResponse: Complete user profile data

        Raises:
            HTTPException: If user not found or access denied
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User account is deactivated"
                )

            logger.info(
                f"User profile retrieved: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id
                }
            )

            return UserResponse.from_orm(user)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to get user profile: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve user profile"
            )

    async def update_user_profile(
        self,
        user_id: int,
        profile_data: UserProfileUpdate
    ) -> UserResponse:
        """
        Update user profile information.

        Args:
            user_id: User ID to update
            profile_data: Profile update data

        Returns:
            UserResponse: Updated user profile

        Raises:
            HTTPException: If user not found or validation fails
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Update only provided fields
            update_data = profile_data.dict(exclude_unset=True)

            if update_data:
                # Add updated timestamp
                update_data['updated_at'] = datetime.utcnow()

                # Update user record
                stmt = update(User).where(User.id == user_id).values(**update_data)
                await self.db.execute(stmt)
                await self.db.commit()

                # Refresh user data
                await self.db.refresh(user)

                logger.info(
                    f"User profile updated: {user.email}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "updated_fields": list(update_data.keys())
                    }
                )

            return UserResponse.from_orm(user)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to update user profile: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user profile"
            )

    async def get_user_preferences(self, user_id: int) -> UserPreferences:
        """
        Get user preferences and notification settings.

        Args:
            user_id: User ID to get preferences for

        Returns:
            UserPreferences: User preferences data

        Raises:
            HTTPException: If user not found
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Build preferences from user model fields
            preferences = UserPreferences(
                email_notifications=user.email_notifications,
                sms_notifications=user.sms_notifications,
                push_notifications=user.push_notifications,
                marketing_emails=user.marketing_emails,
                booking_notifications=getattr(user, 'booking_notifications', 'immediate'),
                promotional_notifications=getattr(user, 'promotional_notifications', 'weekly'),
                preferred_currency=getattr(user, 'preferred_currency', 'NGN'),
                distance_unit=getattr(user, 'distance_unit', 'km'),
                show_online_status=getattr(user, 'show_online_status', True),
                allow_contact_from_vendors=getattr(user, 'allow_contact_from_vendors', True),
                data_sharing_consent=getattr(user, 'data_sharing_consent', False)
            )

            logger.info(
                f"User preferences retrieved: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id
                }
            )

            return preferences

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to get user preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve user preferences"
            )

    async def update_user_preferences(
        self,
        user_id: int,
        preferences_data: UserPreferencesUpdate
    ) -> UserPreferences:
        """
        Update user preferences and notification settings.

        Args:
            user_id: User ID to update preferences for
            preferences_data: Preferences update data

        Returns:
            UserPreferences: Updated preferences

        Raises:
            HTTPException: If user not found or validation fails
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Update only provided preference fields
            update_data = preferences_data.dict(exclude_unset=True)

            if update_data:
                # Add updated timestamp
                update_data['updated_at'] = datetime.utcnow()

                # Update user record with preferences
                stmt = update(User).where(User.id == user_id).values(**update_data)
                await self.db.execute(stmt)
                await self.db.commit()

                logger.info(
                    f"User preferences updated: {user.email}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "updated_preferences": list(update_data.keys())
                    }
                )

            # Return updated preferences
            return await self.get_user_preferences(user_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to update user preferences: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user preferences"
            )

    async def upload_profile_image(
        self,
        user_id: int,
        file: UploadFile
    ) -> ProfileImageResponse:
        """
        Upload and process user profile image.

        Args:
            user_id: User ID to upload image for
            file: Uploaded image file

        Returns:
            ProfileImageResponse: Uploaded image information

        Raises:
            HTTPException: If upload fails or validation errors
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Validate file type and size
            if file.content_type not in settings.ALLOWED_FILE_TYPES:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid file type. Only JPEG, PNG, and WebP images are allowed"
                )

            if file.size > settings.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File too large. Maximum size is {settings.MAX_FILE_SIZE} bytes"
                )

            # Generate unique filename
            file_extension = file.filename.split('.')[-1].lower()
            unique_filename = f"{user_id}_{uuid.uuid4().hex}.{file_extension}"

            # Create upload directory if it doesn't exist
            upload_dir = os.path.join(settings.UPLOAD_DIR, "profile_images")
            os.makedirs(upload_dir, exist_ok=True)

            file_path = os.path.join(upload_dir, unique_filename)
            thumbnail_path = os.path.join(upload_dir, f"thumb_{unique_filename}")

            # Save original file
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)

            # Create thumbnail
            await self._create_thumbnail(file_path, thumbnail_path)

            # Update user profile image
            image_url = f"/uploads/profile_images/{unique_filename}"
            thumbnail_url = f"/uploads/profile_images/thumb_{unique_filename}"

            stmt = update(User).where(User.id == user_id).values(
                profile_image=image_url,
                updated_at=datetime.utcnow()
            )
            await self.db.execute(stmt)
            await self.db.commit()

            logger.info(
                f"Profile image uploaded: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "filename": unique_filename
                }
            )

            return ProfileImageResponse(
                id=unique_filename.split('.')[0],
                url=image_url,
                thumbnail_url=thumbnail_url,
                filename=file.filename,
                content_type=file.content_type,
                size=file.size,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to upload profile image: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload profile image"
            )

    async def _create_thumbnail(self, original_path: str, thumbnail_path: str):
        """Create thumbnail image from original."""
        try:
            with Image.open(original_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Create thumbnail (150x150)
                img.thumbnail((150, 150), Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)

        except Exception as e:
            logger.warning(f"Failed to create thumbnail: {str(e)}")
            # If thumbnail creation fails, copy original as fallback
            import shutil
            shutil.copy2(original_path, thumbnail_path)

    async def get_account_settings(self, user_id: int) -> AccountSettings:
        """
        Get user account settings and security preferences.

        Args:
            user_id: User ID to get settings for

        Returns:
            AccountSettings: User account settings

        Raises:
            HTTPException: If user not found
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Build account settings from user model fields
            settings_data = AccountSettings(
                two_factor_enabled=getattr(user, 'two_factor_enabled', False),
                login_notifications=getattr(user, 'login_notifications', True),
                session_timeout=getattr(user, 'session_timeout', 30),
                auto_logout_inactive=getattr(user, 'auto_logout_inactive', True),
                remember_login_devices=getattr(user, 'remember_login_devices', True),
                activity_tracking=getattr(user, 'activity_tracking', True),
                analytics_consent=getattr(user, 'analytics_consent', False),
                account_updates_email=getattr(user, 'account_updates_email', True),
                security_alerts_email=getattr(user, 'security_alerts_email', True)
            )

            logger.info(
                f"Account settings retrieved: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id
                }
            )

            return settings_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to get account settings: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve account settings"
            )

    async def update_account_settings(
        self,
        user_id: int,
        settings_data: AccountSettingsUpdate
    ) -> AccountSettings:
        """
        Update user account settings and security preferences.

        Args:
            user_id: User ID to update settings for
            settings_data: Account settings update data

        Returns:
            AccountSettings: Updated account settings

        Raises:
            HTTPException: If user not found or validation fails
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Update only provided settings fields
            update_data = settings_data.dict(exclude_unset=True)

            if update_data:
                # Add updated timestamp
                update_data['updated_at'] = datetime.utcnow()

                # Update user record with settings
                stmt = update(User).where(User.id == user_id).values(**update_data)
                await self.db.execute(stmt)
                await self.db.commit()

                logger.info(
                    f"Account settings updated: {user.email}",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "user_id": user_id,
                        "updated_settings": list(update_data.keys())
                    }
                )

            # Return updated settings
            return await self.get_account_settings(user_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to update account settings: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update account settings"
            )

    async def delete_user_profile(self, user_id: int) -> Dict[str, Any]:
        """
        Soft delete user profile (deactivate account).

        Args:
            user_id: User ID to deactivate

        Returns:
            Dict: Deletion confirmation

        Raises:
            HTTPException: If user not found or deletion fails
        """
        try:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User account is already deactivated"
                )

            # Soft delete by deactivating account
            stmt = update(User).where(User.id == user_id).values(
                is_active=False,
                updated_at=datetime.utcnow()
            )
            await self.db.execute(stmt)
            await self.db.commit()

            logger.info(
                f"User account deactivated: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id
                }
            )

            return {
                "message": "User account has been deactivated successfully",
                "user_id": user_id,
                "deactivated_at": datetime.utcnow().isoformat()
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to deactivate user account: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to deactivate user account"
            )
