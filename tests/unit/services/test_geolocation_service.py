"""
Unit tests for IP-based geolocation service.

This module provides comprehensive tests for geolocation detection including:
- IP address extraction from FastAPI requests
- Country code detection using MaxMind GeoIP
- Fallback mechanisms and error handling
- Performance validation and caching
- Integration with payment provider routing

Implements geolocation enhancement testing for Task 4.3.3+ with
>80% test coverage and comprehensive validation scenarios.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import Request
import geoip2.errors

from app.services.geolocation_service import GeolocationService, get_geolocation_service
from app.core.geolocation_resilience import GeolocationResult
from app.services.base import ValidationError, ServiceError


class TestGeolocationService:
    """Test suite for GeolocationService."""

    @pytest.fixture
    def geolocation_service(self):
        """Create GeolocationService instance for testing."""
        with patch('app.services.geolocation_service.settings') as mock_settings:
            mock_settings.GEOIP_DATABASE_PATH = "./test_data/GeoLite2-Country.mmdb"
            mock_settings.ENVIRONMENT = "test"

            service = GeolocationService()
            return service

    @pytest.fixture
    def mock_request_with_forwarded_ip(self):
        """Mock FastAPI request with X-Forwarded-For header."""
        request = MagicMock(spec=Request)
        request.headers = {"X-Forwarded-For": "***********, ************"}
        request.client.host = "127.0.0.1"
        return request

    @pytest.fixture
    def mock_request_with_direct_ip(self):
        """Mock FastAPI request with direct client IP."""
        request = MagicMock(spec=Request)
        request.headers = {}
        request.client.host = "***********"
        return request

    @pytest.fixture
    def mock_request_with_private_ip(self):
        """Mock FastAPI request with private IP."""
        request = MagicMock(spec=Request)
        request.headers = {}
        request.client.host = "*************"
        return request

    @pytest.fixture
    def mock_geoip_response(self):
        """Mock GeoIP database response."""
        response = MagicMock()
        response.country.iso_code = "NG"
        response.country.name = "Nigeria"
        response.continent.code = "AF"
        return response

    @pytest.mark.asyncio
    async def test_extract_client_ip_from_forwarded_header(self, geolocation_service, mock_request_with_forwarded_ip):
        """Test IP extraction from X-Forwarded-For header."""
        client_ip = await geolocation_service.extract_client_ip(mock_request_with_forwarded_ip)

        # Should extract the first IP from the forwarded header
        assert client_ip == "***********"

    @pytest.mark.asyncio
    async def test_extract_client_ip_from_direct_connection(self, geolocation_service, mock_request_with_direct_ip):
        """Test IP extraction from direct client connection."""
        client_ip = await geolocation_service.extract_client_ip(mock_request_with_direct_ip)

        # Should use direct client IP
        assert client_ip == "***********"

    @pytest.mark.asyncio
    async def test_extract_client_ip_development_fallback(self, geolocation_service, mock_request_with_private_ip):
        """Test IP extraction fallback in development environment."""
        with patch('app.services.geolocation_service.settings') as mock_settings:
            mock_settings.ENVIRONMENT = "development"

            client_ip = await geolocation_service.extract_client_ip(mock_request_with_private_ip)

            # Should use default IP for development
            assert client_ip == "*******"

    def test_is_valid_ip_public_address(self, geolocation_service):
        """Test IP validation for public addresses."""
        # Valid public IP addresses
        assert geolocation_service._is_valid_ip("***********") is True
        assert geolocation_service._is_valid_ip("*******") is True
        assert geolocation_service._is_valid_ip("2001:db8::1") is True

    def test_is_valid_ip_private_address_production(self, geolocation_service):
        """Test IP validation rejects private addresses in production."""
        with patch('app.services.geolocation_service.settings') as mock_settings:
            mock_settings.ENVIRONMENT = "production"

            # Private IP addresses should be rejected in production
            assert geolocation_service._is_valid_ip("***********") is False
            assert geolocation_service._is_valid_ip("********") is False
            assert geolocation_service._is_valid_ip("127.0.0.1") is False

    def test_is_valid_ip_invalid_format(self, geolocation_service):
        """Test IP validation for invalid formats."""
        # Invalid IP formats
        assert geolocation_service._is_valid_ip("invalid.ip.address") is False
        assert geolocation_service._is_valid_ip("999.999.999.999") is False
        assert geolocation_service._is_valid_ip("") is False

    @pytest.mark.asyncio
    async def test_detect_country_from_ip_with_geoip(self, geolocation_service, mock_geoip_response):
        """Test country detection using MaxMind GeoIP database."""
        # Mock GeoIP reader
        geolocation_service.geoip_reader = MagicMock()
        geolocation_service.geoip_reader.country.return_value = mock_geoip_response

        result = await geolocation_service.detect_country_from_ip("***********")

        # Assertions
        assert isinstance(result, GeolocationResult)
        assert result.country_code == "NG"
        assert result.country_name == "Nigeria"
        assert result.continent_code == "AF"
        assert result.ip_address == "***********"
        assert result.detection_method == "maxmind_geoip"
        assert result.confidence_score == 0.95

    @pytest.mark.asyncio
    async def test_detect_country_from_ip_address_not_found(self, geolocation_service):
        """Test country detection when IP not found in database."""
        # Mock GeoIP reader to raise AddressNotFoundError
        geolocation_service.geoip_reader = MagicMock()
        geolocation_service.geoip_reader.country.side_effect = geoip2.errors.AddressNotFoundError("IP not found")

        result = await geolocation_service.detect_country_from_ip("***********")

        # Should fallback to unknown result
        assert result.country_code is None
        assert result.detection_method in ["unknown_public_ip", "fallback_failed"]
        assert result.confidence_score <= 0.1

    @pytest.mark.asyncio
    async def test_detect_country_from_ip_no_geoip_reader(self, geolocation_service):
        """Test country detection without GeoIP database."""
        # No GeoIP reader available
        geolocation_service.geoip_reader = None

        result = await geolocation_service.detect_country_from_ip("***********")

        # Should use fallback method
        assert isinstance(result, GeolocationResult)
        assert result.ip_address == "***********"
        assert result.detection_method in ["unknown_public_ip", "fallback_failed"]

    @pytest.mark.asyncio
    async def test_detect_country_caching(self, geolocation_service, mock_geoip_response):
        """Test geolocation result caching."""
        # Mock GeoIP reader
        geolocation_service.geoip_reader = MagicMock()
        geolocation_service.geoip_reader.country.return_value = mock_geoip_response

        ip_address = "***********"

        # First call
        result1 = await geolocation_service.detect_country_from_ip(ip_address)

        # Second call should use cache
        result2 = await geolocation_service.detect_country_from_ip(ip_address)

        # Should only call GeoIP once due to caching
        geolocation_service.geoip_reader.country.assert_called_once()

        # Results should be identical
        assert result1.country_code == result2.country_code
        assert result1.detection_method == result2.detection_method

    @pytest.mark.asyncio
    async def test_get_geolocation_from_request_success(self, geolocation_service, mock_request_with_forwarded_ip, mock_geoip_response):
        """Test complete geolocation detection from FastAPI request."""
        # Mock GeoIP reader
        geolocation_service.geoip_reader = MagicMock()
        geolocation_service.geoip_reader.country.return_value = mock_geoip_response

        result = await geolocation_service.get_geolocation_from_request(mock_request_with_forwarded_ip)

        # Assertions
        assert isinstance(result, GeolocationResult)
        assert result.country_code == "NG"
        assert result.ip_address == "***********"
        assert result.detection_method == "maxmind_geoip"

    @pytest.mark.asyncio
    async def test_get_geolocation_from_request_ip_extraction_failure(self, geolocation_service):
        """Test geolocation when IP extraction fails."""
        # Mock request with invalid IP
        request = MagicMock(spec=Request)
        request.headers = {}
        request.client.host = "invalid.ip"

        with patch('app.services.geolocation_service.settings') as mock_settings:
            mock_settings.ENVIRONMENT = "production"

            with pytest.raises(ServiceError) as exc_info:
                await geolocation_service.get_geolocation_from_request(request)

            assert "Geolocation detection failed" in str(exc_info.value)

    def test_get_fallback_country_private_ip(self, geolocation_service):
        """Test fallback country detection for private IP."""
        result = geolocation_service._get_fallback_country("***********")

        # Should default to Nigeria for development
        assert result.country_code == "NG"
        assert result.country_name == "Nigeria"
        assert result.continent_code == "AF"
        assert result.detection_method == "private_ip_fallback"
        assert result.confidence_score == 0.3

    def test_get_fallback_country_public_ip(self, geolocation_service):
        """Test fallback country detection for unknown public IP."""
        result = geolocation_service._get_fallback_country("***********")

        # Should return unknown for public IP without GeoIP data
        assert result.country_code is None
        assert result.country_name == "Unknown"
        assert result.detection_method == "unknown_public_ip"
        assert result.confidence_score == 0.1

    @pytest.mark.asyncio
    async def test_close_geoip_reader(self, geolocation_service):
        """Test GeoIP reader cleanup."""
        # Mock GeoIP reader
        mock_reader = MagicMock()
        geolocation_service.geoip_reader = mock_reader

        await geolocation_service.close()

        # Verify close was called
        mock_reader.close.assert_called_once()

    def test_geolocation_result_dataclass(self):
        """Test GeolocationResult dataclass functionality."""
        result = GeolocationResult(
            country_code="NG",
            country_name="Nigeria",
            continent_code="AF",
            ip_address="***********",
            detection_method="maxmind_geoip",
            confidence_score=0.95
        )

        # Verify all fields are set correctly
        assert result.country_code == "NG"
        assert result.country_name == "Nigeria"
        assert result.continent_code == "AF"
        assert result.ip_address == "***********"
        assert result.detection_method == "maxmind_geoip"
        assert result.confidence_score == 0.95
        assert result.is_vpn_detected is False
        assert result.is_proxy_detected is False
        assert result.detected_at is not None

    def test_get_geolocation_service_singleton(self):
        """Test geolocation service singleton pattern."""
        service1 = get_geolocation_service()
        service2 = get_geolocation_service()

        # Should return the same instance
        assert service1 is service2

    @pytest.mark.asyncio
    async def test_performance_geolocation_detection(self, geolocation_service, mock_request_with_forwarded_ip, mock_geoip_response):
        """Test geolocation detection performance."""
        import time

        # Mock GeoIP reader
        geolocation_service.geoip_reader = MagicMock()
        geolocation_service.geoip_reader.country.return_value = mock_geoip_response

        start_time = time.time()
        result = await geolocation_service.get_geolocation_from_request(mock_request_with_forwarded_ip)
        end_time = time.time()

        # Performance assertion - should complete within 100ms
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        assert execution_time < 100, f"Geolocation detection took {execution_time}ms, expected <100ms"

        # Verify successful detection
        assert result.country_code == "NG"
