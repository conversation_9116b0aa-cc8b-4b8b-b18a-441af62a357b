# Payment Routing User Guide

## 🚀 How Your Payments Are Automatically Optimized

Culture Connect's smart payment routing ensures you always get the best payment experience by automatically selecting the optimal payment provider based on your location, preferences, and payment method.

## Understanding Payment Routing

### **What is Payment Routing?**

Payment routing is like having a smart GPS for your money. Just as GPS finds the fastest route to your destination, our payment routing finds the fastest, most reliable path for your payment to reach the vendor.

**Traditional Payment Processing**:
- One-size-fits-all approach
- Same provider for everyone
- Higher failure rates
- More expensive fees

**Culture Connect Smart Routing**:
- Personalized for your location
- Best provider for your region
- Higher success rates (95%+)
- Optimized fees and processing times

## The Three Payment Highways

### **🌍 African Highway - Paystack Route**

**Who Uses This Route**:
- Users in Nigeria, Ghana, Kenya, South Africa, and other African countries
- Anyone paying with African currencies (NGN, GHS, KES, ZAR)
- Users preferring local payment methods

**Why It's Optimal**:
- **Local Expertise**: Paystack specializes in African markets
- **Local Payment Methods**: USSD, bank transfer, mobile money
- **Better Success Rates**: 94%+ success rate in African markets
- **Lower Fees**: Reduced international transaction costs
- **Faster Processing**: Local infrastructure means faster payments

**Real-World Example**:
```
Adunni in Lagos wants to book a cultural tour for ₦50,000

1. Adunni clicks "Pay Now"
2. System detects: Nigeria location
3. Route Selected: Paystack (African Highway)
4. Payment Options: Bank transfer, USSD, card, mobile money
5. Adunni pays via USSD: *737*000*50000#
6. Payment completes in 30 seconds
7. Booking confirmed instantly
```

**Available Payment Methods**:
- **Bank Transfer**: Direct bank-to-bank transfer
- **USSD Codes**: Pay with simple phone codes (*737#, *894#, etc.)
- **Mobile Money**: MTN Mobile Money, Airtel Money
- **Debit/Credit Cards**: Visa, Mastercard, Verve
- **QR Codes**: Scan and pay with banking apps

### **🌎 Diaspora Highway - Stripe Route**

**Who Uses This Route**:
- Users in United States, United Kingdom, Canada, Europe, Australia
- Anyone paying with international currencies (USD, EUR, GBP, CAD)
- Users preferring international payment methods

**Why It's Optimal**:
- **Global Reach**: Stripe excels in international markets
- **Advanced Features**: Apple Pay, Google Pay, digital wallets
- **Premium Security**: Advanced fraud protection
- **Multiple Currencies**: Support for 135+ currencies
- **Instant Processing**: Real-time payment confirmation

**Real-World Example**:
```
Marcus in New York wants to book a Nigerian cultural experience for $200

1. Marcus clicks "Pay Now"
2. System detects: United States location
3. Route Selected: Stripe (Diaspora Highway)
4. Payment Options: Card, Apple Pay, Google Pay, bank transfer
5. Marcus pays with Apple Pay (Face ID authentication)
6. Payment completes in 2 seconds
7. Booking confirmed with USD receipt
```

**Available Payment Methods**:
- **Credit/Debit Cards**: Visa, Mastercard, American Express, Discover
- **Digital Wallets**: Apple Pay, Google Pay, Samsung Pay
- **Bank Transfers**: ACH (US), SEPA (Europe), Faster Payments (UK)
- **Buy Now, Pay Later**: Klarna, Afterpay (where available)
- **Local Methods**: iDEAL (Netherlands), Bancontact (Belgium), etc.

### **🪙 Crypto Highway - Busha Route**

**Who Uses This Route**:
- Users choosing cryptocurrency payments
- Anyone wanting to pay with Bitcoin, Ethereum, USDT, or USDC
- Users preferring decentralized payment methods

**Why It's Optimal**:
- **Global Availability**: Works from anywhere in the world
- **Fast Transactions**: Blockchain-based instant transfers
- **Lower Fees**: Reduced intermediary costs
- **Privacy**: Enhanced transaction privacy
- **Future-Proof**: Cutting-edge payment technology

**Real-World Example**:
```
Sarah in London wants to pay with Bitcoin for a ₦75,000 experience

1. Sarah selects "Pay with Cryptocurrency"
2. System automatically routes to: Busha (Crypto Highway)
3. Crypto Options: Bitcoin (BTC), Ethereum (ETH), USDT, USDC
4. Sarah chooses Bitcoin
5. System shows: 0.00185 BTC (current exchange rate)
6. Sarah scans QR code with her crypto wallet
7. Payment confirmed after 1 blockchain confirmation
8. Booking confirmed with crypto receipt
```

**Available Cryptocurrencies**:
- **Bitcoin (BTC)**: The original cryptocurrency
- **Ethereum (ETH)**: Smart contract platform token
- **USDT (Tether)**: USD-pegged stablecoin
- **USDC (USD Coin)**: Regulated USD stablecoin

## How Route Selection Works

### **Step-by-Step Process**

1. **You Click "Pay Now"**
   - Payment process begins
   - System starts route analysis

2. **Instant Location Detection** (< 0.1 seconds)
   - Country identification
   - Regional optimization check
   - VPN/proxy detection

3. **Route Calculation** (< 0.1 seconds)
   - Check for cryptocurrency selection
   - Review your payment preferences
   - Analyze location-based optimization
   - Apply currency-based fallback if needed

4. **Optimal Route Selected**
   - Best provider chosen
   - Payment interface loaded
   - Regional payment methods displayed

5. **Payment Processing**
   - Secure payment processing
   - Real-time status updates
   - Instant confirmation

### **Decision Tree Example**

```
Payment Request Received
├── Cryptocurrency Selected?
│   ├── YES → Busha Route (Crypto Highway)
│   └── NO → Continue Analysis
├── User Has Preference?
│   ├── YES → Check if preference works in current location
│   │   ├── Compatible → Use Preferred Route
│   │   └── Not Compatible → Continue Analysis
│   └── NO → Continue Analysis
├── Location Detected?
│   ├── African Country → Paystack Route (African Highway)
│   ├── Diaspora Country → Stripe Route (Diaspora Highway)
│   └── Unknown → Continue to Currency Analysis
└── Currency-Based Fallback
    ├── NGN, GHS, KES → Paystack Route
    ├── USD, EUR, GBP → Stripe Route
    └── Crypto → Busha Route
```

## Regional Payment Experiences

### **🇳🇬 Nigeria Experience**

**Typical User Journey**:
- **Detection**: System identifies Nigeria
- **Route**: Paystack (African Highway)
- **Currency**: Nigerian Naira (NGN) or USD
- **Methods**: Bank transfer, USSD, cards, mobile money
- **Processing**: 30 seconds to 2 minutes
- **Success Rate**: 94%

**Popular Payment Flow**:
1. Select amount in NGN or USD
2. Choose USSD payment
3. Dial bank code (e.g., *737*000*amount#)
4. Enter PIN
5. Receive SMS confirmation
6. Booking confirmed

### **🇺🇸 United States Experience**

**Typical User Journey**:
- **Detection**: System identifies United States
- **Route**: Stripe (Diaspora Highway)
- **Currency**: US Dollars (USD)
- **Methods**: Cards, Apple Pay, Google Pay, ACH
- **Processing**: 2-5 seconds
- **Success Rate**: 96%

**Popular Payment Flow**:
1. Select amount in USD
2. Choose Apple Pay
3. Authenticate with Face ID/Touch ID
4. Payment processes instantly
5. Booking confirmed immediately

### **🇬🇧 United Kingdom Experience**

**Typical User Journey**:
- **Detection**: System identifies United Kingdom
- **Route**: Stripe (Diaspora Highway)
- **Currency**: British Pounds (GBP) or USD
- **Methods**: Cards, Apple Pay, Faster Payments
- **Processing**: 2-10 seconds
- **Success Rate**: 95%

**Popular Payment Flow**:
1. Select amount in GBP
2. Choose bank transfer (Faster Payments)
3. Authenticate with banking app
4. Payment processes in real-time
5. Booking confirmed instantly

### **🌍 Global Cryptocurrency Experience**

**Typical User Journey**:
- **Selection**: User chooses cryptocurrency
- **Route**: Busha (Crypto Highway)
- **Currency**: BTC, ETH, USDT, USDC
- **Methods**: Wallet transfer, QR code scan
- **Processing**: 1-10 minutes (depending on blockchain)
- **Success Rate**: 89%

**Popular Payment Flow**:
1. Select "Pay with Crypto"
2. Choose Bitcoin (BTC)
3. See amount: 0.00185 BTC
4. Scan QR code with wallet app
5. Confirm transaction in wallet
6. Wait for blockchain confirmation
7. Booking confirmed

## Troubleshooting Payment Routes

### **Common Scenarios**

#### **"Wrong payment provider selected"**

**Possible Causes**:
- VPN usage affecting location detection
- Traveling to a different country
- Outdated browser cache

**Solutions**:
1. Disable VPN temporarily
2. Clear browser cache and cookies
3. Contact support to manually set your region
4. Use payment preferences to override automatic selection

#### **"Payment method not available"**

**Possible Causes**:
- Regional restrictions on payment method
- Provider maintenance
- Account limitations

**Solutions**:
1. Try alternative payment method
2. Check if you're using VPN
3. Contact your bank/payment provider
4. Use different payment route (e.g., switch to crypto)

#### **"Payment keeps failing"**

**Possible Causes**:
- Insufficient funds
- Bank blocking international transactions
- Incorrect payment details

**Solutions**:
1. Verify account balance
2. Contact your bank about international payments
3. Double-check payment information
4. Try different payment method

### **Getting Route Information**

You can always see which route your payment is using:

1. **During Payment**: Look for provider logo (Paystack/Stripe/Busha)
2. **In Receipt**: Route information included in payment confirmation
3. **Account History**: View payment routes in transaction history
4. **Support**: Contact us for detailed routing information

## Optimizing Your Payment Experience

### **Setting Payment Preferences**

1. **Go to Account Settings**
2. **Select "Payment Preferences"**
3. **Choose Preferred Provider**:
   - Paystack (for African markets)
   - Stripe (for international markets)
   - Busha (for cryptocurrency)
4. **Save Preferences**

**Note**: System will use your preference when optimal for your location.

### **Managing Multiple Regions**

If you frequently travel or live in multiple countries:

1. **Set Primary Region**: Your main country of residence
2. **Add Secondary Regions**: Countries you frequently visit
3. **Enable Smart Switching**: Automatic optimization when traveling
4. **Set Currency Preferences**: Preferred currencies for each region

### **Cryptocurrency Setup**

For optimal crypto payment experience:

1. **Verify Wallet Compatibility**: Ensure your wallet supports QR codes
2. **Check Network Fees**: Bitcoin fees vary by network congestion
3. **Consider Stablecoins**: USDT/USDC for price stability
4. **Enable Notifications**: Get alerts for payment confirmations

## Payment Route Analytics

### **Your Payment Performance**

Track your payment success rates:

- **Success Rate**: Percentage of successful payments
- **Average Processing Time**: How fast your payments complete
- **Preferred Routes**: Which routes you use most
- **Cost Analysis**: Fees paid across different routes

### **Regional Insights**

Understand how your location affects payments:

- **Optimal Routes**: Best routes for your region
- **Alternative Options**: Backup payment methods
- **Performance Trends**: How success rates change over time
- **Cost Comparisons**: Fee differences between routes

---

## 📚 Related Guides

- **[Geolocation Features](geolocation_features.md)** - Understanding automatic location detection
- **[VPN Handling Guide](vpn_handling.md)** - Using VPNs safely with Culture Connect payments
- **[Developer Integration](../developer/geolocation_integration.md)** - Technical implementation details
- **[API Documentation](../README.md)** - Complete API reference

---

**Last Updated**: January 15, 2024
**Version**: 2.4.2
**Part of**: Culture Connect Backend API Documentation v2.4.2
