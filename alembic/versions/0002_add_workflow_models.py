"""Add workflow models for Task 6.2.2

Revision ID: 0002
Revises: 0001
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""

    # Create workflow status enum
    workflow_status_enum = postgresql.ENUM(
        'draft', 'active', 'paused', 'archived', 'deprecated',
        name='workflowstatus'
    )
    workflow_status_enum.create(op.get_bind())

    # Create execution status enum
    execution_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'cancelled', 'timeout', 'retrying',
        name='executionstatus'
    )
    execution_status_enum.create(op.get_bind())

    # Create dependency type enum
    dependency_type_enum = postgresql.ENUM(
        'success', 'completion', 'failure', 'conditional',
        name='dependencytype'
    )
    dependency_type_enum.create(op.get_bind())

    # Create step status enum
    step_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'skipped', 'cancelled',
        name='stepstatus'
    )
    step_status_enum.create(op.get_bind())

    # Create alert severity enum
    alert_severity_enum = postgresql.ENUM(
        'low', 'medium', 'high', 'critical',
        name='alertseverity'
    )
    alert_severity_enum.create(op.get_bind())

    # Create workflow_definitions table
    op.create_table(
        'workflow_definitions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=50), nullable=False, server_default='1.0.0'),
        sa.Column('status', workflow_status_enum, nullable=False, server_default='draft'),

        # Configuration and metadata
        sa.Column('configuration', sa.JSON(), nullable=False, server_default='{}'),
        sa.Column('tags', postgresql.ARRAY(sa.String(length=100)), nullable=True, server_default='{}'),
        sa.Column('workflow_metadata', sa.JSON(), nullable=True, server_default='{}'),

        # Ownership and permissions
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('team_id', postgresql.UUID(as_uuid=True), nullable=True),

        # Performance and limits
        sa.Column('max_execution_time', sa.Integer(), nullable=True),
        sa.Column('max_retries', sa.Integer(), nullable=False, server_default='3'),
        sa.Column('retry_delay', sa.Integer(), nullable=False, server_default='60'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Constraints
        sa.CheckConstraint('max_execution_time > 0', name='positive_max_execution_time'),
        sa.CheckConstraint('max_retries >= 0', name='non_negative_max_retries'),
        sa.CheckConstraint('retry_delay >= 0', name='non_negative_retry_delay'),
        sa.UniqueConstraint('name', 'version', name='unique_workflow_name_version'),
    )

    # Create workflow_executions table
    op.create_table(
        'workflow_executions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('workflow_definition_id', postgresql.UUID(as_uuid=True), nullable=False),

        # Execution state and tracking
        sa.Column('status', execution_status_enum, nullable=False, server_default='pending'),
        sa.Column('execution_context', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('input_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('output_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('error_details', sa.JSON(), nullable=True),

        # Timing and performance
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('duration_seconds', sa.Numeric(precision=10, scale=3), nullable=True),

        # Retry and recovery
        sa.Column('retry_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),

        # Execution metadata
        sa.Column('triggered_by', sa.String(length=100), nullable=True),
        sa.Column('trigger_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('correlation_id', sa.String(length=255), nullable=True),

        # Performance metrics
        sa.Column('steps_total', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('steps_completed', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('steps_failed', sa.Integer(), nullable=False, server_default='0'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_definition_id'], ['workflow_definitions.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('retry_count >= 0', name='non_negative_retry_count'),
        sa.CheckConstraint('steps_total >= 0', name='non_negative_steps_total'),
        sa.CheckConstraint('steps_completed >= 0', name='non_negative_steps_completed'),
        sa.CheckConstraint('steps_failed >= 0', name='non_negative_steps_failed'),
        sa.CheckConstraint('duration_seconds >= 0', name='non_negative_duration'),
    )

    # Create job_dependencies table
    op.create_table(
        'job_dependencies',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),

        # Dependency relationship
        sa.Column('parent_job_id', sa.String(length=255), nullable=False),
        sa.Column('child_job_id', sa.String(length=255), nullable=False),
        sa.Column('dependency_type', dependency_type_enum, nullable=False, server_default='success'),

        # Workflow context
        sa.Column('workflow_execution_id', postgresql.UUID(as_uuid=True), nullable=True),

        # Conditional logic
        sa.Column('condition_expression', sa.Text(), nullable=True),
        sa.Column('condition_data', sa.JSON(), nullable=True, server_default='{}'),

        # Timing and constraints
        sa.Column('timeout_seconds', sa.Integer(), nullable=True),
        sa.Column('max_wait_time', sa.Integer(), nullable=True),

        # Status tracking
        sa.Column('is_satisfied', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('satisfied_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('evaluation_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('last_evaluation_at', sa.DateTime(timezone=True), nullable=True),

        # Metadata
        sa.Column('dependency_metadata', sa.JSON(), nullable=True, server_default='{}'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_execution_id'], ['workflow_executions.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('timeout_seconds > 0', name='positive_timeout_seconds'),
        sa.CheckConstraint('max_wait_time > 0', name='positive_max_wait_time'),
        sa.CheckConstraint('evaluation_count >= 0', name='non_negative_evaluation_count'),
        sa.UniqueConstraint('parent_job_id', 'child_job_id', name='unique_job_dependency'),
    )

    # Create workflow_steps table
    op.create_table(
        'workflow_steps',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('workflow_definition_id', postgresql.UUID(as_uuid=True), nullable=False),

        # Step identification and ordering
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('step_order', sa.Integer(), nullable=False),

        # Task configuration
        sa.Column('task_name', sa.String(length=255), nullable=False),
        sa.Column('task_configuration', sa.JSON(), nullable=False, server_default='{}'),
        sa.Column('task_queue', sa.String(length=100), nullable=True),
        sa.Column('task_priority', sa.Integer(), nullable=False, server_default='5'),

        # Execution parameters
        sa.Column('timeout_seconds', sa.Integer(), nullable=True),
        sa.Column('max_retries', sa.Integer(), nullable=False, server_default='3'),
        sa.Column('retry_delay', sa.Integer(), nullable=False, server_default='60'),

        # Conditional execution
        sa.Column('condition_expression', sa.Text(), nullable=True),
        sa.Column('skip_on_failure', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('continue_on_failure', sa.Boolean(), nullable=False, server_default='false'),

        # Dependencies within workflow
        sa.Column('depends_on_steps', postgresql.ARRAY(sa.String(length=255)), nullable=True, server_default='{}'),

        # Metadata
        sa.Column('tags', postgresql.ARRAY(sa.String(length=100)), nullable=True, server_default='{}'),
        sa.Column('step_metadata', sa.JSON(), nullable=True, server_default='{}'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_definition_id'], ['workflow_definitions.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('step_order >= 0', name='non_negative_step_order'),
        sa.CheckConstraint('timeout_seconds > 0', name='positive_timeout_seconds'),
        sa.CheckConstraint('max_retries >= 0', name='non_negative_max_retries'),
        sa.CheckConstraint('retry_delay >= 0', name='non_negative_retry_delay'),
        sa.CheckConstraint('task_priority >= 0 AND task_priority <= 10', name='valid_task_priority'),
        sa.UniqueConstraint('workflow_definition_id', 'step_order', name='unique_workflow_step_order'),
        sa.UniqueConstraint('workflow_definition_id', 'name', name='unique_workflow_step_name'),
    )

    # Create workflow_step_executions table
    op.create_table(
        'workflow_step_executions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('workflow_execution_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('workflow_step_id', postgresql.UUID(as_uuid=True), nullable=False),

        # Execution tracking
        sa.Column('status', step_status_enum, nullable=False, server_default='pending'),
        sa.Column('celery_task_id', sa.String(length=255), nullable=True),

        # Timing
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('duration_seconds', sa.Numeric(precision=10, scale=3), nullable=True),

        # Data and results
        sa.Column('input_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('output_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('error_details', sa.JSON(), nullable=True),

        # Retry tracking
        sa.Column('retry_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_execution_id'], ['workflow_executions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['workflow_step_id'], ['workflow_steps.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('retry_count >= 0', name='non_negative_retry_count'),
        sa.CheckConstraint('duration_seconds >= 0', name='non_negative_duration'),
        sa.UniqueConstraint('workflow_execution_id', 'workflow_step_id', name='unique_step_execution'),
    )

    # Create job_schedules table
    op.create_table(
        'job_schedules',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('workflow_definition_id', postgresql.UUID(as_uuid=True), nullable=True),

        # Schedule identification
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),

        # Scheduling configuration
        sa.Column('cron_expression', sa.String(length=255), nullable=True),
        sa.Column('timezone', sa.String(length=100), nullable=False, server_default='UTC'),
        sa.Column('interval_seconds', sa.Integer(), nullable=True),

        # Schedule constraints
        sa.Column('start_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('max_runs', sa.Integer(), nullable=True),

        # Status and control
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('last_run_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_run_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('run_count', sa.Integer(), nullable=False, server_default='0'),

        # Configuration
        sa.Column('schedule_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('schedule_metadata', sa.JSON(), nullable=True, server_default='{}'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_definition_id'], ['workflow_definitions.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('interval_seconds > 0', name='positive_interval_seconds'),
        sa.CheckConstraint('max_runs > 0', name='positive_max_runs'),
        sa.CheckConstraint('run_count >= 0', name='non_negative_run_count'),
        sa.CheckConstraint(
            '(cron_expression IS NOT NULL) OR (interval_seconds IS NOT NULL)',
            name='schedule_expression_required'
        ),
    )

    # Create workflow_alerts table
    op.create_table(
        'workflow_alerts',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('workflow_definition_id', postgresql.UUID(as_uuid=True), nullable=True),

        # Alert identification
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('severity', alert_severity_enum, nullable=False, server_default='medium'),

        # Alert conditions
        sa.Column('condition_type', sa.String(length=100), nullable=False),
        sa.Column('condition_expression', sa.Text(), nullable=False),
        sa.Column('condition_data', sa.JSON(), nullable=True, server_default='{}'),

        # Notification configuration
        sa.Column('notification_channels', postgresql.ARRAY(sa.String(length=50)), nullable=False, server_default='{}'),
        sa.Column('notification_config', sa.JSON(), nullable=True, server_default='{}'),

        # Escalation rules
        sa.Column('escalation_delay_minutes', sa.Integer(), nullable=True),
        sa.Column('escalation_channels', postgresql.ARRAY(sa.String(length=50)), nullable=True, server_default='{}'),
        sa.Column('max_escalations', sa.Integer(), nullable=False, server_default='3'),

        # Status and control
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('is_muted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('muted_until', sa.DateTime(timezone=True), nullable=True),

        # Alert statistics
        sa.Column('trigger_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('last_triggered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_escalated_at', sa.DateTime(timezone=True), nullable=True),

        # Configuration
        sa.Column('alert_data', sa.JSON(), nullable=True, server_default='{}'),
        sa.Column('alert_metadata', sa.JSON(), nullable=True, server_default='{}'),

        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()'), nullable=True),

        # Foreign keys
        sa.ForeignKeyConstraint(['workflow_definition_id'], ['workflow_definitions.id'], ondelete='CASCADE'),

        # Constraints
        sa.CheckConstraint('escalation_delay_minutes > 0', name='positive_escalation_delay'),
        sa.CheckConstraint('max_escalations > 0', name='positive_max_escalations'),
        sa.CheckConstraint('trigger_count >= 0', name='non_negative_trigger_count'),
    )

    # Create optimized indexes for workflow_definitions
    op.create_index('idx_workflow_def_status', 'workflow_definitions', ['status'])
    op.create_index('idx_workflow_def_created_by', 'workflow_definitions', ['created_by'])
    op.create_index('idx_workflow_def_team', 'workflow_definitions', ['team_id'])
    op.create_index('idx_workflow_def_tags', 'workflow_definitions', ['tags'], postgresql_using='gin')
    op.create_index('idx_workflow_def_created_at', 'workflow_definitions', ['created_at'])

    # Create optimized indexes for workflow_executions
    op.create_index('idx_workflow_exec_definition', 'workflow_executions', ['workflow_definition_id'])
    op.create_index('idx_workflow_exec_status', 'workflow_executions', ['status'])
    op.create_index('idx_workflow_exec_started_at', 'workflow_executions', ['started_at'])
    op.create_index('idx_workflow_exec_correlation', 'workflow_executions', ['correlation_id'])
    op.create_index('idx_workflow_exec_triggered_by', 'workflow_executions', ['triggered_by'])
    op.create_index('idx_workflow_exec_created_at', 'workflow_executions', ['created_at'])

    # Create optimized indexes for job_dependencies
    op.create_index('idx_job_dep_parent', 'job_dependencies', ['parent_job_id'])
    op.create_index('idx_job_dep_child', 'job_dependencies', ['child_job_id'])
    op.create_index('idx_job_dep_workflow', 'job_dependencies', ['workflow_execution_id'])
    op.create_index('idx_job_dep_type', 'job_dependencies', ['dependency_type'])
    op.create_index('idx_job_dep_satisfied', 'job_dependencies', ['is_satisfied'])
    op.create_index('idx_job_dep_created_at', 'job_dependencies', ['created_at'])

    # Create optimized indexes for workflow_steps
    op.create_index('idx_workflow_step_definition', 'workflow_steps', ['workflow_definition_id'])
    op.create_index('idx_workflow_step_order', 'workflow_steps', ['step_order'])
    op.create_index('idx_workflow_step_task_name', 'workflow_steps', ['task_name'])
    op.create_index('idx_workflow_step_tags', 'workflow_steps', ['tags'], postgresql_using='gin')
    op.create_index('idx_workflow_step_created_at', 'workflow_steps', ['created_at'])

    # Create optimized indexes for workflow_step_executions
    op.create_index('idx_step_exec_workflow', 'workflow_step_executions', ['workflow_execution_id'])
    op.create_index('idx_step_exec_step', 'workflow_step_executions', ['workflow_step_id'])
    op.create_index('idx_step_exec_status', 'workflow_step_executions', ['status'])
    op.create_index('idx_step_exec_celery_task', 'workflow_step_executions', ['celery_task_id'])
    op.create_index('idx_step_exec_started_at', 'workflow_step_executions', ['started_at'])
    op.create_index('idx_step_exec_created_at', 'workflow_step_executions', ['created_at'])

    # Create optimized indexes for job_schedules
    op.create_index('idx_job_schedule_workflow', 'job_schedules', ['workflow_definition_id'])
    op.create_index('idx_job_schedule_active', 'job_schedules', ['is_active'])
    op.create_index('idx_job_schedule_next_run', 'job_schedules', ['next_run_at'])
    op.create_index('idx_job_schedule_cron', 'job_schedules', ['cron_expression'])
    op.create_index('idx_job_schedule_created_at', 'job_schedules', ['created_at'])

    # Create optimized indexes for workflow_alerts
    op.create_index('idx_workflow_alert_definition', 'workflow_alerts', ['workflow_definition_id'])
    op.create_index('idx_workflow_alert_severity', 'workflow_alerts', ['severity'])
    op.create_index('idx_workflow_alert_active', 'workflow_alerts', ['is_active'])
    op.create_index('idx_workflow_alert_condition_type', 'workflow_alerts', ['condition_type'])
    op.create_index('idx_workflow_alert_channels', 'workflow_alerts', ['notification_channels'], postgresql_using='gin')
    op.create_index('idx_workflow_alert_created_at', 'workflow_alerts', ['created_at'])


def downgrade() -> None:
    """Downgrade database schema."""

    # Drop tables in reverse order
    op.drop_table('workflow_alerts')
    op.drop_table('job_schedules')
    op.drop_table('workflow_step_executions')
    op.drop_table('workflow_steps')
    op.drop_table('job_dependencies')
    op.drop_table('workflow_executions')
    op.drop_table('workflow_definitions')

    # Drop enums
    op.execute('DROP TYPE alertseverity')
    op.execute('DROP TYPE stepstatus')
    op.execute('DROP TYPE dependencytype')
    op.execute('DROP TYPE executionstatus')
    op.execute('DROP TYPE workflowstatus')
