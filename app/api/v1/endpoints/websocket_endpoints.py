"""
WebSocket API endpoints for Culture Connect Backend API.

This module provides comprehensive WebSocket endpoints for real-time communication including:
- WebSocket connection management with authentication
- Real-time event handling and message routing
- User presence management and broadcasting
- Admin metrics and monitoring endpoints
- Health check and status endpoints

Implements Task 6.1.1 Phase 4 requirements with production-grade FastAPI WebSocket integration,
JWT authentication middleware, rate limiting, and comprehensive error handling following
established API patterns.

Performance targets: <200ms HTTP responses, <100ms WebSocket message delivery.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Set
from uuid import uuid4

from fastapi import (
    APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException,
    status, Query, Path, Request, BackgroundTasks
)
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from jose import JWTError

from app.core.deps import get_db_session, get_current_user
from app.core.security import verify_token, TokenData
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
# Simple rate limiter implementation
class RateLimiter:
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = []

    async def is_allowed(self) -> bool:
        import time
        current_time = time.time()
        # Remove old requests outside the window
        self.requests = [req_time for req_time in self.requests
                        if current_time - req_time < self.window_seconds]

        if len(self.requests) < self.max_requests:
            self.requests.append(current_time)
            return True
        return False
from app.models.user import User
from app.services.websocket_services import (
    WebSocketConnectionService, WebSocketEventService,
    UserPresenceService, WebSocketMetricsService,
    WebSocketRoomService, WebSocketRoomParticipantService
)
from app.schemas.websocket_schemas import (
    WebSocketConnectionCreate, WebSocketConnectionResponse,
    WebSocketEventCreate, WebSocketEventResponse,
    UserPresenceResponse, WebSocketMessage, WebSocketAuthMessage, WebSocketAckMessage,
    WebSocketRoomCreate, WebSocketRoomResponse, WebSocketRoomUpdate,
    WebSocketRoomParticipantCreate, WebSocketRoomParticipantResponse,
    ConversationEventData, RealTimeNotificationData,
    EventTypeEnum, EventPriorityEnum, PresenceStatusEnum
)
from app.repositories.base import PaginationParams
from app.services.enhanced_event_broadcasting_service import (
    EnhancedEventBroadcastingService,
    EventMessage,
    MessagePriority,
    EventCategory,
    SubscriptionTopic
)
from app.services.booking_realtime_integration_service import BookingRealtimeIntegrationService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Enhanced WebSocket connection manager for Phase 6
class EnhancedWebSocketConnectionManager:
    """
    Enhanced WebSocket connection manager for real-time communication.

    Provides comprehensive connection lifecycle management including:
    - Real-time connection establishment with JWT authentication
    - Room-based connection management and participant tracking
    - Message broadcasting with performance optimization
    - Connection health monitoring with heartbeat/ping-pong
    - Integration with existing WebSocket services and repositories

    Performance targets: <1000ms connection, <100ms message delivery, >99% reliability
    """

    def __init__(self):
        # Active connections by connection_id
        self.active_connections: Dict[str, WebSocket] = {}
        # User connections mapping
        self.user_connections: Dict[int, Set[str]] = {}
        # Room connections mapping for efficient broadcasting
        self.room_connections: Dict[int, Set[str]] = {}
        # Connection metadata with enhanced tracking
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        # Rate limiters per connection
        self.rate_limiters: Dict[str, RateLimiter] = {}
        # Connection health monitoring
        self.connection_health: Dict[str, Dict[str, Any]] = {}
        # Message queues for reliable delivery
        self.message_queues: Dict[str, asyncio.Queue] = {}
        # Background tasks for connection management
        self._background_tasks: Set[asyncio.Task] = set()
        # Performance metrics
        self._metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_failed": 0,
            "connection_errors": 0
        }

    async def connect(
        self,
        websocket: WebSocket,
        connection_id: str,
        user_id: int,
        db: AsyncSession,
        user: User
    ) -> bool:
        """
        Enhanced WebSocket connection establishment with authentication and tracking.

        Performance target: <1000ms for connection establishment.
        """
        start_time = time.time()

        try:
            # Accept WebSocket connection
            await websocket.accept()

            # Store connection
            self.active_connections[connection_id] = websocket

            # Track user connections
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(connection_id)

            # Initialize message queue for reliable delivery
            self.message_queues[connection_id] = asyncio.Queue(maxsize=1000)

            # Store enhanced metadata
            self.connection_metadata[connection_id] = {
                "user_id": user_id,
                "user": user,
                "connected_at": datetime.now(timezone.utc),
                "last_activity": datetime.now(timezone.utc),
                "message_count": 0,
                "bytes_sent": 0,
                "bytes_received": 0,
                "rooms": set(),
                "client_info": {
                    "user_agent": getattr(websocket.headers, 'user-agent', 'unknown'),
                    "ip_address": getattr(websocket.client, 'host', 'unknown') if websocket.client else 'unknown'
                }
            }

            # Initialize connection health monitoring
            self.connection_health[connection_id] = {
                "last_ping": datetime.now(timezone.utc),
                "last_pong": datetime.now(timezone.utc),
                "ping_count": 0,
                "missed_pings": 0,
                "is_healthy": True,
                "latency_ms": 0
            }

            # Initialize rate limiter
            self.rate_limiters[connection_id] = RateLimiter(max_requests=100, window_seconds=60)

            # Store connection in database
            await self._store_connection_in_db(connection_id, user_id, db)

            # Start background tasks for this connection
            await self._start_connection_tasks(connection_id)

            # Update metrics
            self._metrics["total_connections"] += 1
            self._metrics["active_connections"] = len(self.active_connections)

            connection_time = (time.time() - start_time) * 1000
            logger.info(f"WebSocket connection established for user {user_id} in {connection_time:.2f}ms")

            # Send welcome message
            await self._send_welcome_message(connection_id, user)

            return True

        except Exception as e:
            logger.error(f"Failed to establish WebSocket connection: {str(e)}")
            self._metrics["connection_errors"] += 1
            await self._cleanup_failed_connection(connection_id, user_id)
            return False

    async def disconnect(self, connection_id: str, db: AsyncSession, reason: str = "normal_closure") -> None:
        """
        Enhanced WebSocket disconnection with cleanup and database updates.

        Performance target: <200ms for disconnection cleanup.
        """
        if connection_id not in self.active_connections:
            return

        start_time = time.time()

        try:
            # Get metadata before cleanup
            metadata = self.connection_metadata.get(connection_id, {})
            user_id = metadata.get("user_id")
            user_rooms = metadata.get("rooms", set())

            # Close WebSocket connection
            websocket = self.active_connections[connection_id]
            try:
                await websocket.close(code=1000, reason=reason)
            except Exception:
                pass  # Connection might already be closed

            # Remove from active connections
            del self.active_connections[connection_id]

            # Clean up user connections mapping
            if user_id and user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]

            # Remove from room connections
            for room_id in user_rooms:
                if room_id in self.room_connections:
                    self.room_connections[room_id].discard(connection_id)
                    if not self.room_connections[room_id]:
                        del self.room_connections[room_id]

            # Clean up connection data
            self.connection_metadata.pop(connection_id, None)
            self.connection_health.pop(connection_id, None)
            self.rate_limiters.pop(connection_id, None)

            # Clean up message queue
            if connection_id in self.message_queues:
                queue = self.message_queues[connection_id]
                # Drain the queue
                while not queue.empty():
                    try:
                        queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
                del self.message_queues[connection_id]

            # Cancel background tasks
            await self._cancel_connection_tasks(connection_id)

            # Update database
            await self._update_connection_status_in_db(connection_id, "disconnected", db)

            # Update metrics
            self._metrics["active_connections"] = len(self.active_connections)

            disconnect_time = (time.time() - start_time) * 1000
            logger.info(f"WebSocket connection {connection_id} disconnected for user {user_id} in {disconnect_time:.2f}ms")

        except Exception as e:
            logger.error(f"Error during WebSocket disconnection: {str(e)}")

    async def join_room(self, connection_id: str, room_id: int, db: AsyncSession) -> bool:
        """
        Join a WebSocket connection to a room for broadcasting.

        Performance target: <500ms for room joining.
        """
        if connection_id not in self.active_connections:
            return False

        try:
            # Get connection metadata
            metadata = self.connection_metadata.get(connection_id, {})
            user = metadata.get("user")

            if not user:
                return False

            # Validate room access using room service
            room_service = WebSocketRoomService(db)
            room = await room_service.get_room_by_id(str(room_id), user)

            if not room:
                return False

            # Add connection to room
            if room_id not in self.room_connections:
                self.room_connections[room_id] = set()
            self.room_connections[room_id].add(connection_id)

            # Update connection metadata
            self.connection_metadata[connection_id]["rooms"].add(room_id)

            # Add participant to room using participant service
            participant_service = WebSocketRoomParticipantService(db)
            await participant_service.add_participant(
                room_id=room_id,
                user_id=user.id,
                connection_id=connection_id,
                role="participant",
                current_user=user
            )

            logger.info(f"Connection {connection_id} joined room {room_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to join room {room_id}: {str(e)}")
            return False

    async def leave_room(self, connection_id: str, room_id: int, db: AsyncSession) -> bool:
        """
        Remove a WebSocket connection from a room.

        Performance target: <500ms for room leaving.
        """
        if connection_id not in self.active_connections:
            return False

        try:
            # Remove from room connections
            if room_id in self.room_connections:
                self.room_connections[room_id].discard(connection_id)
                if not self.room_connections[room_id]:
                    del self.room_connections[room_id]

            # Update connection metadata
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["rooms"].discard(room_id)

            # Remove participant from room using participant service
            metadata = self.connection_metadata.get(connection_id, {})
            user = metadata.get("user")

            if user:
                participant_service = WebSocketRoomParticipantService(db)
                await participant_service.remove_participant(
                    room_id=room_id,
                    user_id=user.id,
                    current_user=user
                )

            logger.info(f"Connection {connection_id} left room {room_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to leave room {room_id}: {str(e)}")
            return False

    async def send_personal_message(self, message: str, connection_id: str) -> bool:
        """
        Send message to specific connection with reliable delivery.

        Performance target: <100ms for message delivery.
        """
        if connection_id not in self.active_connections:
            return False

        try:
            start_time = time.time()

            # Check rate limiting
            if not await self._check_rate_limit(connection_id):
                logger.warning(f"Rate limit exceeded for connection {connection_id}")
                return False

            # Get WebSocket connection
            websocket = self.active_connections[connection_id]

            # Send message
            await websocket.send_text(message)

            # Update metrics and metadata
            self._update_connection_activity(connection_id)
            self._metrics["messages_sent"] += 1

            delivery_time = (time.time() - start_time) * 1000
            logger.debug(f"Message delivered to {connection_id} in {delivery_time:.2f}ms")

            return True

        except Exception as e:
            logger.error(f"Failed to send message to {connection_id}: {str(e)}")
            self._metrics["messages_failed"] += 1
            # Connection might be stale, mark for cleanup
            await self._mark_connection_for_cleanup(connection_id)
            return False

    async def broadcast_to_room(self, room_id: int, message: str, exclude_connections: Set[str] = None) -> int:
        """
        Broadcast message to all connections in a room.

        Performance target: <100ms for message delivery to all room participants.
        """
        if room_id not in self.room_connections:
            return 0

        exclude_connections = exclude_connections or set()
        connections = self.room_connections[room_id] - exclude_connections

        if not connections:
            return 0

        start_time = time.time()
        successful_deliveries = 0

        # Send messages concurrently for better performance
        tasks = []
        for connection_id in connections:
            if connection_id in self.active_connections:
                task = self._send_message_with_retry(connection_id, message)
                tasks.append(task)

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_deliveries = sum(1 for result in results if result is True)

        broadcast_time = (time.time() - start_time) * 1000
        logger.info(f"Broadcast to room {room_id}: {successful_deliveries}/{len(connections)} delivered in {broadcast_time:.2f}ms")

        return successful_deliveries

    async def broadcast_to_user(self, user_id: int, message: str) -> int:
        """
        Broadcast message to all connections of a specific user.

        Performance target: <100ms for message delivery to all user connections.
        """
        if user_id not in self.user_connections:
            return 0

        connections = self.user_connections[user_id]
        successful_deliveries = 0

        # Send messages concurrently
        tasks = []
        for connection_id in connections:
            if connection_id in self.active_connections:
                task = self._send_message_with_retry(connection_id, message)
                tasks.append(task)

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_deliveries = sum(1 for result in results if result is True)

        logger.debug(f"Broadcast to user {user_id}: {successful_deliveries}/{len(connections)} delivered")
        return successful_deliveries

    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get comprehensive connection statistics."""
        return {
            "total_connections": self._metrics["total_connections"],
            "active_connections": self._metrics["active_connections"],
            "messages_sent": self._metrics["messages_sent"],
            "messages_failed": self._metrics["messages_failed"],
            "connection_errors": self._metrics["connection_errors"],
            "active_rooms": len(self.room_connections),
            "active_users": len(self.user_connections),
            "message_queue_sizes": {
                conn_id: queue.qsize()
                for conn_id, queue in self.message_queues.items()
            },
            "connection_health": {
                conn_id: health["is_healthy"]
                for conn_id, health in self.connection_health.items()
            }
        }

    # Helper methods for enhanced connection management

    async def _store_connection_in_db(self, connection_id: str, user_id: int, db: AsyncSession) -> None:
        """Store connection information in database."""
        try:
            connection_service = WebSocketConnectionService(db)
            connection_data = WebSocketConnectionCreate(
                connection_id=connection_id,
                user_id=user_id,
                status="connected",
                client_type="web",
                ip_address=self.connection_metadata[connection_id]["client_info"]["ip_address"]
            )
            await connection_service.create_connection(connection_data, None)
        except Exception as e:
            logger.error(f"Failed to store connection in database: {str(e)}")

    async def _update_connection_status_in_db(self, connection_id: str, status: str, db: AsyncSession) -> None:
        """Update connection status in database."""
        try:
            connection_service = WebSocketConnectionService(db)
            await connection_service.update_connection_status(connection_id, status, None)
        except Exception as e:
            logger.error(f"Failed to update connection status in database: {str(e)}")

    async def _send_welcome_message(self, connection_id: str, user: User) -> None:
        """Send welcome message to newly connected user."""
        welcome_message = {
            "type": "welcome",
            "message": f"Welcome {user.username}! You are now connected.",
            "connection_id": connection_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "server_info": {
                "version": "1.0.0",
                "features": ["rooms", "broadcasting", "presence"]
            }
        }
        await self.send_personal_message(json.dumps(welcome_message), connection_id)

    async def _send_message_with_retry(self, connection_id: str, message: str, max_retries: int = 2) -> bool:
        """Send message with retry logic for reliability."""
        for attempt in range(max_retries + 1):
            try:
                if connection_id not in self.active_connections:
                    return False

                websocket = self.active_connections[connection_id]
                await websocket.send_text(message)

                self._update_connection_activity(connection_id)
                return True

            except Exception as e:
                if attempt == max_retries:
                    logger.error(f"Failed to send message after {max_retries + 1} attempts: {str(e)}")
                    await self._mark_connection_for_cleanup(connection_id)
                    return False
                await asyncio.sleep(0.01 * (attempt + 1))  # Exponential backoff

        return False

    def _update_connection_activity(self, connection_id: str) -> None:
        """Update connection activity timestamp and metrics."""
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_activity"] = datetime.now(timezone.utc)
            self.connection_metadata[connection_id]["message_count"] += 1

    async def _check_rate_limit(self, connection_id: str) -> bool:
        """Check if connection is within rate limits."""
        if connection_id in self.rate_limiters:
            return await self.rate_limiters[connection_id].is_allowed()
        return True

    async def _mark_connection_for_cleanup(self, connection_id: str) -> None:
        """Mark connection for cleanup due to errors."""
        if connection_id in self.connection_health:
            self.connection_health[connection_id]["is_healthy"] = False
            self.connection_health[connection_id]["missed_pings"] += 1

    async def _cleanup_failed_connection(self, connection_id: str, user_id: int) -> None:
        """Clean up failed connection attempt."""
        self.active_connections.pop(connection_id, None)
        self.connection_metadata.pop(connection_id, None)
        self.connection_health.pop(connection_id, None)
        self.rate_limiters.pop(connection_id, None)
        self.message_queues.pop(connection_id, None)

        if user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

    async def _start_connection_tasks(self, connection_id: str) -> None:
        """Start background tasks for connection management."""
        # Start heartbeat task
        heartbeat_task = asyncio.create_task(self._heartbeat_task(connection_id))
        self._background_tasks.add(heartbeat_task)
        heartbeat_task.add_done_callback(self._background_tasks.discard)

    async def _cancel_connection_tasks(self, connection_id: str) -> None:
        """Cancel background tasks for a connection."""
        # Tasks are automatically cleaned up when they complete
        pass

    async def _heartbeat_task(self, connection_id: str) -> None:
        """Background task for connection health monitoring."""
        try:
            while connection_id in self.active_connections:
                await asyncio.sleep(30)  # Ping every 30 seconds

                if connection_id not in self.active_connections:
                    break

                try:
                    websocket = self.active_connections[connection_id]
                    await websocket.ping()

                    # Update health status
                    if connection_id in self.connection_health:
                        self.connection_health[connection_id]["last_ping"] = datetime.now(timezone.utc)
                        self.connection_health[connection_id]["ping_count"] += 1

                except Exception:
                    # Connection is unhealthy
                    if connection_id in self.connection_health:
                        self.connection_health[connection_id]["missed_pings"] += 1
                        if self.connection_health[connection_id]["missed_pings"] >= 3:
                            # Mark for cleanup after 3 missed pings
                            await self._mark_connection_for_cleanup(connection_id)
                            break

        except Exception as e:
            logger.error(f"Heartbeat task error for {connection_id}: {str(e)}")


# Global enhanced connection manager
connection_manager = EnhancedWebSocketConnectionManager()


async def authenticate_websocket(websocket: WebSocket, token: str) -> Optional[User]:
    """
    Authenticate WebSocket connection using JWT token.

    Args:
        websocket: WebSocket connection
        token: JWT authentication token

    Returns:
        User if authentication successful, None otherwise
    """
    try:
        # Verify token
        token_data = await verify_token(token, "access")
        if not token_data or not token_data.user_id:
            return None

        # Get user from database (we'll need to pass db session)
        # For now, create a mock user - in production, fetch from database
        from app.models.user import User
        user = User()
        user.id = token_data.user_id
        user.email = token_data.email or f"user_{token_data.user_id}@example.com"
        user.is_active = True

        return user

    except Exception as e:
        logger.error(f"WebSocket authentication failed: {str(e)}")
        return None


@router.websocket("/ws/connect")
async def websocket_connect_endpoint(
    websocket: WebSocket,
    token: str = Query(..., description="JWT authentication token"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    WebSocket connection endpoint with authentication.

    Establishes WebSocket connection with JWT authentication and
    registers connection for real-time communication.
    """
    connection_id = str(uuid4())

    try:
        # Authenticate user
        user = await authenticate_websocket(websocket, token)
        if not user:
            await websocket.close(code=4001, reason="Authentication failed")
            return

        # Connect to WebSocket manager
        success = await connection_manager.connect(websocket, connection_id, user.id, db, user)
        if not success:
            await websocket.close(code=4000, reason="Connection failed")
            return

        # Create connection record in database
        connection_service = WebSocketConnectionService(db)
        presence_service = UserPresenceService(db)

        try:
            # Create connection record
            connection_data = WebSocketConnectionCreate(
                connection_id=connection_id,
                user_id=user.id,
                client_type="web",
                ip_address=websocket.client.host if websocket.client else None
            )

            await connection_service.create_connection(connection_data, user)

            # Update user presence
            await presence_service.increment_user_connections(user.id, user)

            # Send connection confirmation
            welcome_message = {
                "type": "connection.established",
                "data": {
                    "connection_id": connection_id,
                    "user_id": user.id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "message": "WebSocket connection established successfully"
                }
            }

            await websocket.send_text(json.dumps(welcome_message))

            # Handle incoming messages
            while True:
                try:
                    # Check rate limit
                    if not await connection_manager.check_rate_limit(connection_id):
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "data": {
                                "code": "RATE_LIMIT_EXCEEDED",
                                "message": "Rate limit exceeded. Please slow down."
                            }
                        }))
                        continue

                    # Receive message
                    data = await websocket.receive_text()
                    message_data = json.loads(data)

                    # Process message based on type
                    await process_websocket_message(
                        websocket, connection_id, user, message_data, db
                    )

                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "data": {
                            "code": "INVALID_JSON",
                            "message": "Invalid JSON format"
                        }
                    }))
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {str(e)}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "data": {
                            "code": "MESSAGE_PROCESSING_ERROR",
                            "message": "Failed to process message"
                        }
                    }))

        finally:
            # Clean up connection
            await connection_manager.disconnect(connection_id, db, "Client disconnected")

            # Update database
            try:
                await connection_service.disconnect_connection(connection_id, "Client disconnected")
                await presence_service.decrement_user_connections(user.id, user)
            except Exception as e:
                logger.error(f"Error cleaning up connection: {str(e)}")

    except Exception as e:
        logger.error(f"WebSocket connection error: {str(e)}")
        try:
            await websocket.close(code=4000, reason="Internal server error")
        except:
            pass


async def process_websocket_message(
    websocket: WebSocket,
    connection_id: str,
    user: User,
    message_data: Dict[str, Any],
    db: AsyncSession
) -> None:
    """
    Process incoming WebSocket message based on type.

    Args:
        websocket: WebSocket connection
        connection_id: Connection identifier
        user: Authenticated user
        message_data: Message data
        db: Database session
    """
    message_type = message_data.get("type")

    try:
        if message_type == "ping":
            # Handle ping/pong for connection health
            await websocket.send_text(json.dumps({
                "type": "pong",
                "data": {
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }))

        elif message_type == "presence.update":
            # Handle presence status updates
            presence_service = UserPresenceService(db)
            status = message_data.get("data", {}).get("status", "online")

            await presence_service.update_user_status(user.id, status, "manual_update", user)

            # Broadcast presence update to other users
            presence_message = {
                "type": "presence.updated",
                "data": {
                    "user_id": user.id,
                    "status": status,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }

            await connection_manager.broadcast(
                json.dumps(presence_message),
                exclude_connections={connection_id}
            )

        elif message_type == "message.send":
            # Handle message sending (placeholder for chat functionality)
            event_service = WebSocketEventService(db)

            # Create event for message
            event_data = WebSocketEventCreate(
                connection_id=1,  # This would be the actual connection DB ID
                event_type=EventTypeEnum.MESSAGE_SENT,
                priority=EventPriorityEnum.NORMAL,
                payload=message_data.get("data", {})
            )

            await event_service.create_event(event_data, user)

            # Send acknowledgment
            await websocket.send_text(json.dumps({
                "type": "message.acknowledged",
                "data": {
                    "message_id": message_data.get("data", {}).get("message_id"),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            }))

        else:
            # Unknown message type
            await websocket.send_text(json.dumps({
                "type": "error",
                "data": {
                    "code": "UNKNOWN_MESSAGE_TYPE",
                    "message": f"Unknown message type: {message_type}"
                }
            }))

    except Exception as e:
        logger.error(f"Error processing message type {message_type}: {str(e)}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "data": {
                "code": "MESSAGE_PROCESSING_ERROR",
                "message": "Failed to process message"
            }
        }))


# HTTP API Endpoints for WebSocket management

@router.get("/connections", response_model=List[WebSocketConnectionResponse])
async def get_user_connections(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get active WebSocket connections for the current user.

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)
        connections = await connection_service.get_user_connections(current_user.id, current_user)

        # Add real-time connection status
        for connection in connections:
            connection_id = connection.connection_id
            is_active = connection_id in connection_manager.active_connections
            connection.is_active = is_active

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(connections)} connections in {response_time:.3f}s")

        return connections

    except Exception as e:
        logger.error(f"Failed to get user connections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connections"
        )


@router.post("/connections/{connection_id}/disconnect")
async def disconnect_connection(
    connection_id: str = Path(..., description="Connection ID to disconnect"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Disconnect a specific WebSocket connection.

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)

        # Disconnect from manager
        await connection_manager.disconnect(connection_id, db, "Manual disconnection")

        # Update database
        success = await connection_service.disconnect_connection(
            connection_id, "Manual disconnection"
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Connection not found"
            )

        response_time = time.time() - start_time
        logger.info(f"Disconnected connection {connection_id} in {response_time:.3f}s")

        return {"message": "Connection disconnected successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect connection"
        )


@router.get("/presence/me", response_model=UserPresenceResponse)
async def get_my_presence(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get current user's presence status.

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        presence_service = UserPresenceService(db)
        presence = await presence_service.get_user_presence(current_user.id, current_user)

        if not presence:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Presence not found"
            )

        # Add real-time connection count
        presence.active_connections_count = connection_manager.get_user_connection_count(current_user.id)

        response_time = time.time() - start_time
        logger.info(f"Retrieved presence for user {current_user.id} in {response_time:.3f}s")

        return presence

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user presence: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve presence"
        )


@router.put("/presence/me")
async def update_my_presence(
    status: PresenceStatusEnum = Query(..., description="New presence status"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Update current user's presence status.

    Performance target: <100ms response time.
    """
    start_time = time.time()

    try:
        presence_service = UserPresenceService(db)
        presence = await presence_service.update_user_status(
            current_user.id, status.value, "manual_update", current_user
        )

        # Broadcast presence update to other users
        presence_message = {
            "type": "presence.updated",
            "data": {
                "user_id": current_user.id,
                "status": status.value,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }

        # Get user's connections to exclude from broadcast
        user_connections = connection_manager.user_connections.get(current_user.id, set())
        await connection_manager.broadcast(
            json.dumps(presence_message),
            exclude_connections=user_connections
        )

        response_time = time.time() - start_time
        logger.info(f"Updated presence for user {current_user.id} in {response_time:.3f}s")

        return {"message": "Presence updated successfully", "status": status.value}

    except Exception as e:
        logger.error(f"Failed to update user presence: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update presence"
        )


@router.get("/presence/online", response_model=List[UserPresenceResponse])
async def get_online_users(
    limit: int = Query(100, ge=1, le=500, description="Maximum number of users to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get list of online users.

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        presence_service = UserPresenceService(db)
        online_users = await presence_service.get_online_users(limit, current_user)

        # Add real-time connection counts
        for user_presence in online_users:
            user_presence.active_connections_count = connection_manager.get_user_connection_count(
                user_presence.user_id
            )

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(online_users)} online users in {response_time:.3f}s")

        return online_users

    except Exception as e:
        logger.error(f"Failed to get online users: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve online users"
        )


@router.get("/events", response_model=List[WebSocketEventResponse])
async def get_pending_events(
    priority: Optional[EventPriorityEnum] = Query(None, description="Filter by event priority"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of events to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get pending WebSocket events (admin operation).

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        event_service = WebSocketEventService(db)
        events = await event_service.get_pending_events(priority, limit, current_user)

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(events)} pending events in {response_time:.3f}s")

        return events

    except Exception as e:
        logger.error(f"Failed to get pending events: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve events"
        )


@router.post("/events/{event_id}/acknowledge")
async def acknowledge_event(
    event_id: str = Path(..., description="Event ID to acknowledge"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Acknowledge a WebSocket event.

    Performance target: <100ms response time.
    """
    start_time = time.time()

    try:
        event_service = WebSocketEventService(db)
        success = await event_service.mark_event_acknowledged(event_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )

        response_time = time.time() - start_time
        logger.info(f"Acknowledged event {event_id} in {response_time:.3f}s")

        return {"message": "Event acknowledged successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to acknowledge event: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to acknowledge event"
        )


@router.get("/metrics/summary")
async def get_metrics_summary(
    hours: int = Query(24, ge=1, le=168, description="Number of hours to analyze"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get WebSocket performance metrics summary (admin operation).

    Performance target: <200ms response time.
    """
    start_time = time.time()

    try:
        metrics_service = WebSocketMetricsService(db)
        summary = await metrics_service.get_performance_summary(hours, current_user)

        # Add real-time metrics
        summary["real_time"] = {
            "active_connections": connection_manager.get_connection_count(),
            "online_users": len(connection_manager.get_online_users()),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        response_time = time.time() - start_time
        logger.info(f"Retrieved metrics summary in {response_time:.3f}s")

        return summary

    except Exception as e:
        logger.error(f"Failed to get metrics summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )


@router.get("/health")
async def websocket_health_check():
    """
    WebSocket service health check endpoint.

    Performance target: <100ms response time.
    """
    start_time = time.time()

    try:
        # Check connection manager health
        active_connections = connection_manager.get_connection_count()
        online_users = len(connection_manager.get_online_users())

        # Basic health metrics
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": {
                "active_connections": active_connections,
                "online_users": online_users,
                "connection_manager_healthy": True
            },
            "performance": {
                "response_time_ms": 0  # Will be set below
            }
        }

        response_time = time.time() - start_time
        health_data["performance"]["response_time_ms"] = round(response_time * 1000, 2)

        return health_data

    except Exception as e:
        logger.error(f"WebSocket health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": "Health check failed"
            }
        )


# Admin endpoints

@router.post("/admin/connections/cleanup")
async def cleanup_stale_connections(
    timeout_minutes: int = Query(30, ge=5, le=1440, description="Minutes of inactivity before cleanup"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Clean up stale WebSocket connections (admin operation).
    """
    try:
        connection_service = WebSocketConnectionService(db)
        cleaned_count = await connection_service.cleanup_stale_connections(timeout_minutes)

        return {
            "message": f"Cleaned up {cleaned_count} stale connections",
            "cleaned_count": cleaned_count,
            "timeout_minutes": timeout_minutes
        }

    except Exception as e:
        logger.error(f"Failed to cleanup stale connections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup connections"
        )


@router.get("/admin/metrics/daily")
async def get_daily_metrics(
    date: str = Query(..., description="Date in YYYY-MM-DD format"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get daily WebSocket metrics (admin operation).

    Performance target: <200ms response time.
    """
    try:
        from datetime import datetime
        metric_date = datetime.strptime(date, "%Y-%m-%d")

        metrics_service = WebSocketMetricsService(db)
        daily_metrics = await metrics_service.get_daily_aggregates(metric_date, current_user)

        return daily_metrics

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYY-MM-DD"
        )
    except Exception as e:
        logger.error(f"Failed to get daily metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve daily metrics"
        )


# ============================================================================
# Enhanced Real-time Communication Endpoints for Task 6.1.1 Phase 4
# ============================================================================

# WebSocket Room Management Endpoints

@router.post(
    "/rooms",
    response_model=WebSocketRoomResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create WebSocket room",
    description="Create a new WebSocket room for real-time communication"
)
async def create_websocket_room(
    room_data: WebSocketRoomCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> WebSocketRoomResponse:
    """
    Create a new WebSocket room with automatic configuration.

    Performance target: <500ms for room creation.
    """
    start_time = time.time()

    try:
        room_service = WebSocketRoomService(db)
        room = await room_service.create_room(room_data, current_user)

        response_time = time.time() - start_time
        logger.info(f"Created WebSocket room {room.room_id} in {response_time:.3f}s")

        return room

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to create WebSocket room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create WebSocket room"
        )


@router.get(
    "/rooms/{room_id}",
    response_model=WebSocketRoomResponse,
    summary="Get WebSocket room",
    description="Get WebSocket room details by room ID"
)
async def get_websocket_room(
    room_id: str = Path(..., description="Room ID to retrieve"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> WebSocketRoomResponse:
    """
    Get WebSocket room details with access control validation.

    Performance target: <200ms for room retrieval.
    """
    start_time = time.time()

    try:
        room_service = WebSocketRoomService(db)
        room = await room_service.get_room_by_id(room_id, current_user)

        if not room:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"WebSocket room {room_id} not found"
            )

        response_time = time.time() - start_time
        logger.info(f"Retrieved WebSocket room {room_id} in {response_time:.3f}s")

        return room

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to get WebSocket room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve WebSocket room"
        )


@router.put(
    "/rooms/{room_id}",
    response_model=WebSocketRoomResponse,
    summary="Update WebSocket room",
    description="Update WebSocket room configuration"
)
async def update_websocket_room(
    room_id: str = Path(..., description="Room ID to update"),
    room_update: WebSocketRoomUpdate = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> WebSocketRoomResponse:
    """
    Update WebSocket room configuration.

    Performance target: <500ms for room updates.
    """
    start_time = time.time()

    try:
        room_service = WebSocketRoomService(db)

        # Get existing room first
        existing_room = await room_service.get_room_by_id(room_id, current_user)
        if not existing_room:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"WebSocket room {room_id} not found"
            )

        # Update room activity
        await room_service.update_room_activity(room_id, current_user)

        response_time = time.time() - start_time
        logger.info(f"Updated WebSocket room {room_id} in {response_time:.3f}s")

        # Return updated room
        return await room_service.get_room_by_id(room_id, current_user)

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to update WebSocket room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update WebSocket room"
        )


@router.delete(
    "/rooms/{room_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Close WebSocket room",
    description="Close a WebSocket room and notify participants"
)
async def close_websocket_room(
    room_id: str = Path(..., description="Room ID to close"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Close a WebSocket room and notify participants.

    Performance target: <500ms for room closure.
    """
    start_time = time.time()

    try:
        room_service = WebSocketRoomService(db)
        success = await room_service.close_room(room_id, current_user)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"WebSocket room {room_id} not found"
            )

        response_time = time.time() - start_time
        logger.info(f"Closed WebSocket room {room_id} in {response_time:.3f}s")

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to close WebSocket room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to close WebSocket room"
        )


@router.get(
    "/rooms/booking/{booking_id}",
    response_model=List[WebSocketRoomResponse],
    summary="Get rooms by booking",
    description="Get all WebSocket rooms associated with a booking"
)
async def get_rooms_by_booking(
    booking_id: int = Path(..., description="Booking ID to get rooms for"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> List[WebSocketRoomResponse]:
    """
    Get all WebSocket rooms associated with a booking.

    Performance target: <200ms for booking room queries.
    """
    start_time = time.time()

    try:
        room_service = WebSocketRoomService(db)
        rooms = await room_service.get_rooms_by_booking(booking_id, current_user)

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(rooms)} rooms for booking {booking_id} in {response_time:.3f}s")

        return rooms

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to get booking rooms in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve booking rooms"
        )


# WebSocket Participant Management Endpoints

@router.post(
    "/rooms/{room_id}/participants",
    response_model=WebSocketRoomParticipantResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Add participant to room",
    description="Add a participant to a WebSocket room with role assignment"
)
async def add_room_participant(
    room_id: int = Path(..., description="Room ID to add participant to"),
    participant_data: WebSocketRoomParticipantCreate = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> WebSocketRoomParticipantResponse:
    """
    Add a participant to a WebSocket room with capacity checking.

    Performance target: <500ms for participant addition.
    """
    start_time = time.time()

    try:
        participant_service = WebSocketRoomParticipantService(db)

        # Use current user as participant if not specified
        user_id = participant_data.user_id if participant_data else current_user.id
        role = participant_data.role if participant_data else "participant"
        connection_id = participant_data.connection_id if participant_data else None

        participant = await participant_service.add_participant(
            room_id=room_id,
            user_id=user_id,
            connection_id=connection_id,
            role=role,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Added participant {user_id} to room {room_id} in {response_time:.3f}s")

        return participant

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to add participant in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add participant to room"
        )


@router.delete(
    "/rooms/{room_id}/participants/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove participant from room",
    description="Remove a participant from a WebSocket room"
)
async def remove_room_participant(
    room_id: int = Path(..., description="Room ID to remove participant from"),
    user_id: int = Path(..., description="User ID to remove"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Remove a participant from a WebSocket room.

    Performance target: <500ms for participant removal.
    """
    start_time = time.time()

    try:
        participant_service = WebSocketRoomParticipantService(db)
        success = await participant_service.remove_participant(
            room_id=room_id,
            user_id=user_id,
            current_user=current_user
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Participant {user_id} not found in room {room_id}"
            )

        response_time = time.time() - start_time
        logger.info(f"Removed participant {user_id} from room {room_id} in {response_time:.3f}s")

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to remove participant in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove participant from room"
        )


@router.get(
    "/rooms/{room_id}/participants",
    response_model=List[WebSocketRoomParticipantResponse],
    summary="List room participants",
    description="Get all participants in a WebSocket room"
)
async def list_room_participants(
    room_id: int = Path(..., description="Room ID to get participants for"),
    active_only: bool = Query(True, description="Show only active participants"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> List[WebSocketRoomParticipantResponse]:
    """
    Get all participants in a WebSocket room with access control validation.

    Performance target: <200ms for participant queries.
    """
    start_time = time.time()

    try:
        participant_service = WebSocketRoomParticipantService(db)
        participants = await participant_service.get_room_participants(
            room_id=room_id,
            active_only=active_only,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(participants)} participants for room {room_id} in {response_time:.3f}s")

        return participants

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to get room participants in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve room participants"
        )


@router.put(
    "/rooms/{room_id}/participants/{user_id}/activity",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Update participant activity",
    description="Update participant last seen timestamp"
)
async def update_participant_activity(
    room_id: int = Path(..., description="Room ID"),
    user_id: int = Path(..., description="User ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Update participant last seen timestamp.

    Performance target: <200ms for activity updates.
    """
    start_time = time.time()

    try:
        participant_service = WebSocketRoomParticipantService(db)
        success = await participant_service.update_participant_activity(
            room_id=room_id,
            user_id=user_id,
            current_user=current_user
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Participant {user_id} not found in room {room_id}"
            )

        response_time = time.time() - start_time
        logger.info(f"Updated participant activity for user {user_id} in room {room_id} in {response_time:.3f}s")

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to update participant activity in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update participant activity"
        )


@router.delete(
    "/rooms/{room_id}/participants/bulk",
    status_code=status.HTTP_200_OK,
    summary="Bulk remove participants",
    description="Remove multiple participants from a room in bulk"
)
async def bulk_remove_participants(
    room_id: int = Path(..., description="Room ID"),
    user_ids: List[int] = Query(..., description="List of user IDs to remove"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Remove multiple participants from a room in bulk.

    Performance target: <500ms for bulk operations.
    """
    start_time = time.time()

    try:
        participant_service = WebSocketRoomParticipantService(db)
        removed_count = await participant_service.bulk_remove_participants(
            room_id=room_id,
            user_ids=user_ids,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Bulk removed {removed_count} participants from room {room_id} in {response_time:.3f}s")

        return {
            "removed_count": removed_count,
            "total_requested": len(user_ids),
            "room_id": room_id
        }

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed bulk participant removal in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk remove participants"
        )


# Real-time Broadcasting Endpoints

@router.post(
    "/broadcast/room/{room_id}",
    status_code=status.HTTP_200_OK,
    summary="Broadcast to room",
    description="Broadcast a message to all participants in a WebSocket room"
)
async def broadcast_to_room(
    room_id: int = Path(..., description="Room ID to broadcast to"),
    event_data: ConversationEventData = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Broadcast a message to all participants in a WebSocket room.

    Performance target: <200ms for broadcast preparation.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)

        # Prepare broadcast data
        broadcast_data = event_data.model_dump() if event_data else {
            "type": "room_broadcast",
            "message": "System broadcast",
            "timestamp": time.time()
        }

        # Prepare broadcast to room connections
        connection_count = await connection_service.broadcast_to_room(
            room_id=room_id,
            event_data=broadcast_data,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Prepared broadcast to {connection_count} connections in room {room_id} in {response_time:.3f}s")

        return {
            "room_id": room_id,
            "connection_count": connection_count,
            "broadcast_type": broadcast_data.get("type", "unknown"),
            "timestamp": broadcast_data.get("timestamp", time.time())
        }

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to broadcast to room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to broadcast to room"
        )


@router.get(
    "/connections/room/{room_id}",
    response_model=List[WebSocketConnectionResponse],
    summary="Get room connections",
    description="Get all active WebSocket connections for participants in a room"
)
async def get_room_connections(
    room_id: int = Path(..., description="Room ID to get connections for"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> List[WebSocketConnectionResponse]:
    """
    Get all active WebSocket connections for participants in a room.

    Performance target: <200ms for connection queries.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)
        connections = await connection_service.get_connections_in_room(
            room_id=room_id,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(connections)} connections for room {room_id} in {response_time:.3f}s")

        return connections

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to get room connections in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve room connections"
        )


@router.post(
    "/connections/{connection_id}/join/{room_id}",
    status_code=status.HTTP_200_OK,
    summary="Join room",
    description="Join a WebSocket connection to a room"
)
async def join_websocket_room(
    connection_id: str = Path(..., description="WebSocket connection ID"),
    room_id: int = Path(..., description="Room ID to join"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Join a WebSocket connection to a room.

    Performance target: <500ms for room joining.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)
        success = await connection_service.join_room(
            connection_id=connection_id,
            room_id=room_id,
            current_user=current_user
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Connection {connection_id} or room {room_id} not found"
            )

        response_time = time.time() - start_time
        logger.info(f"Connection {connection_id} joined room {room_id} in {response_time:.3f}s")

        return {
            "connection_id": connection_id,
            "room_id": room_id,
            "status": "joined",
            "timestamp": time.time()
        }

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to join room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to join room"
        )


@router.delete(
    "/connections/{connection_id}/leave/{room_id}",
    status_code=status.HTTP_200_OK,
    summary="Leave room",
    description="Remove a WebSocket connection from a room"
)
async def leave_websocket_room(
    connection_id: str = Path(..., description="WebSocket connection ID"),
    room_id: int = Path(..., description="Room ID to leave"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Remove a WebSocket connection from a room.

    Performance target: <500ms for room leaving.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)
        success = await connection_service.leave_room(
            connection_id=connection_id,
            room_id=room_id,
            current_user=current_user
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Connection {connection_id} not found in room {room_id}"
            )

        response_time = time.time() - start_time
        logger.info(f"Connection {connection_id} left room {room_id} in {response_time:.3f}s")

        return {
            "connection_id": connection_id,
            "room_id": room_id,
            "status": "left",
            "timestamp": time.time()
        }

    except HTTPException:
        raise
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to leave room in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to leave room"
        )


# Enhanced Connection Management Endpoints

@router.get(
    "/connections/user/{user_id}/rooms",
    response_model=List[Dict[str, Any]],
    summary="Get user connections with rooms",
    description="Get user's active connections with associated room information"
)
async def get_user_connections_with_rooms(
    user_id: int = Path(..., description="User ID to get connections for"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> List[Dict[str, Any]]:
    """
    Get user's active connections with associated room information.

    Performance target: <200ms for user connection queries.
    """
    start_time = time.time()

    try:
        connection_service = WebSocketConnectionService(db)
        connections_data = await connection_service.get_user_active_connections_with_rooms(
            user_id=user_id,
            current_user=current_user
        )

        response_time = time.time() - start_time
        logger.info(f"Retrieved {len(connections_data)} connections with rooms for user {user_id} in {response_time:.3f}s")

        return connections_data

    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Failed to get user connections with rooms in {response_time:.3f}s: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user connections with rooms"
        )


# Enhanced Event Broadcasting Endpoints for Phase 7

@router.post("/events/broadcast")
async def broadcast_event(
    event_data: Dict[str, Any],
    priority: MessagePriority = MessagePriority.STANDARD,
    category: EventCategory = EventCategory.NOTIFICATION,
    topic: Optional[SubscriptionTopic] = None,
    target_user_ids: Optional[List[int]] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Broadcast event message with priority-based routing.

    Performance target: <50ms event processing.
    """
    start_time = time.time()

    try:
        # Initialize broadcasting service
        broadcasting_service = EnhancedEventBroadcastingService(db, connection_manager)

        # Create event message
        event_message = EventMessage(
            event_type=event_data.get("type", "custom_event"),
            category=category,
            priority=priority,
            topic=topic,
            payload=event_data,
            target_user_ids=set(target_user_ids) if target_user_ids else set(),
            requires_acknowledgment=event_data.get("requires_ack", False)
        )

        # Broadcast event
        result = await broadcasting_service.broadcast_event(event_message, current_user)

        response_time = (time.time() - start_time) * 1000
        logger.info(f"Event broadcasted in {response_time:.2f}ms")

        return {
            **result,
            "processing_time_ms": response_time,
            "status": "success"
        }

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to broadcast event in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Event broadcasting failed: {str(e)}"
        )


@router.post("/events/booking/{booking_id}/broadcast")
async def broadcast_booking_event(
    booking_id: int = Path(..., description="Booking ID"),
    event_type: str = Query(..., description="Event type"),
    event_data: Dict[str, Any] = {},
    priority: MessagePriority = MessagePriority.HIGH,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Broadcast booking-specific event to relevant participants.

    Performance target: <50ms event processing.
    """
    start_time = time.time()

    try:
        # Initialize booking integration service
        integration_service = BookingRealtimeIntegrationService(db)

        # Broadcast booking event
        result = await integration_service.broadcast_booking_status_update(
            booking_id=booking_id,
            status=event_type,
            event_data=event_data,
            current_user=current_user
        )

        response_time = (time.time() - start_time) * 1000
        logger.info(f"Booking event broadcasted in {response_time:.2f}ms")

        return {
            **result,
            "processing_time_ms": response_time,
            "status": "success"
        }

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to broadcast booking event in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Booking event broadcasting failed: {str(e)}"
        )


@router.post("/subscriptions/{topic}/subscribe")
async def subscribe_to_topic(
    topic: SubscriptionTopic = Path(..., description="Topic to subscribe to"),
    user_id: Optional[int] = Query(None, description="User ID (admin only)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Subscribe user to a specific topic for selective message delivery.
    """
    start_time = time.time()

    try:
        # Use current user ID if not specified or not admin
        target_user_id = user_id if user_id and hasattr(current_user, 'role') and current_user.role == 'admin' else current_user.id

        # Initialize broadcasting service
        broadcasting_service = EnhancedEventBroadcastingService(db, connection_manager)

        # Subscribe to topic
        success = await broadcasting_service.subscribe_user_to_topic(
            user_id=target_user_id,
            topic=topic,
            current_user=current_user
        )

        response_time = (time.time() - start_time) * 1000

        if success:
            logger.info(f"User {target_user_id} subscribed to topic {topic.value} in {response_time:.2f}ms")
            return {
                "status": "success",
                "user_id": target_user_id,
                "topic": topic.value,
                "processing_time_ms": response_time
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to subscribe to topic"
            )

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to subscribe to topic in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Topic subscription failed: {str(e)}"
        )


@router.delete("/subscriptions/{topic}/unsubscribe")
async def unsubscribe_from_topic(
    topic: SubscriptionTopic = Path(..., description="Topic to unsubscribe from"),
    user_id: Optional[int] = Query(None, description="User ID (admin only)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Unsubscribe user from a specific topic.
    """
    start_time = time.time()

    try:
        # Use current user ID if not specified or not admin
        target_user_id = user_id if user_id and hasattr(current_user, 'role') and current_user.role == 'admin' else current_user.id

        # Initialize broadcasting service
        broadcasting_service = EnhancedEventBroadcastingService(db, connection_manager)

        # Unsubscribe from topic
        success = await broadcasting_service.unsubscribe_user_from_topic(
            user_id=target_user_id,
            topic=topic,
            current_user=current_user
        )

        response_time = (time.time() - start_time) * 1000

        if success:
            logger.info(f"User {target_user_id} unsubscribed from topic {topic.value} in {response_time:.2f}ms")
            return {
                "status": "success",
                "user_id": target_user_id,
                "topic": topic.value,
                "processing_time_ms": response_time
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to unsubscribe from topic"
            )

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to unsubscribe from topic in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Topic unsubscription failed: {str(e)}"
        )


@router.get("/events/offline")
async def get_offline_messages(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get offline messages for user upon reconnection.
    """
    start_time = time.time()

    try:
        # Initialize broadcasting service
        broadcasting_service = EnhancedEventBroadcastingService(db, connection_manager)

        # Get offline messages
        messages = await broadcasting_service.get_offline_messages(
            user_id=current_user.id,
            current_user=current_user
        )

        response_time = (time.time() - start_time) * 1000
        logger.info(f"Retrieved {len(messages)} offline messages for user {current_user.id} in {response_time:.2f}ms")

        return {
            "messages": messages,
            "count": len(messages),
            "processing_time_ms": response_time,
            "status": "success"
        }

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to get offline messages in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve offline messages: {str(e)}"
        )


@router.get("/events/metrics")
async def get_broadcasting_metrics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get comprehensive broadcasting performance metrics.
    """
    start_time = time.time()

    try:
        # Initialize broadcasting service
        broadcasting_service = EnhancedEventBroadcastingService(db, connection_manager)

        # Get metrics
        metrics = await broadcasting_service.get_broadcasting_metrics()

        response_time = (time.time() - start_time) * 1000

        return {
            **metrics,
            "processing_time_ms": response_time,
            "status": "success"
        }

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to get broadcasting metrics in {response_time:.2f}ms: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve broadcasting metrics: {str(e)}"
        )
