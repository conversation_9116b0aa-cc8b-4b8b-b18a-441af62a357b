"""
Role-Based Access Control (RBAC) models for Culture Connect Backend API.

This module defines comprehensive RBAC models including:
- Role management with hierarchical permissions
- Permission tracking and audit logging
- Access control decisions and security events
- Resource-level permission management

Implements Task 2.2.3 requirements for complete role-based access control
with production-grade audit logging and security monitoring.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Index, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
import enum

from app.db.base import BaseModelWithAudit
from app.core.security import UserRole, Permission


class AccessDecision(str, enum.Enum):
    """Access control decision types."""
    GRANTED = "granted"
    DENIED = "denied"
    ERROR = "error"


class ResourceType(str, enum.Enum):
    """Resource types for permission checking."""
    USER = "user"
    VENDOR = "vendor"
    SERVICE = "service"
    BOOKING = "booking"
    PAYMENT = "payment"
    CAMPAIGN = "campaign"
    ANALYTICS = "analytics"
    SYSTEM = "system"


class RoleHierarchy(BaseModelWithAudit):
    """
    Role hierarchy model for managing role relationships and inheritance.

    This model defines hierarchical relationships between roles, allowing
    for permission inheritance and role-based access control with proper
    hierarchy management.
    """

    __tablename__ = "role_hierarchies"

    # Parent role (higher in hierarchy)
    parent_role = Column(
        String(20),
        nullable=False,
        index=True,
        doc="Parent role in hierarchy"
    )

    # Child role (inherits from parent)
    child_role = Column(
        String(20),
        nullable=False,
        index=True,
        doc="Child role that inherits permissions"
    )

    # Hierarchy level (0 = top level)
    level = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Hierarchy level (0 = highest)"
    )

    # Active status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Hierarchy relationship active status"
    )

    # Metadata
    description = Column(
        Text,
        nullable=True,
        doc="Description of hierarchy relationship"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_role_hierarchy_parent", "parent_role", "is_active"),
        Index("idx_role_hierarchy_child", "child_role", "is_active"),
        Index("idx_role_hierarchy_level", "level", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of role hierarchy."""
        return f"<RoleHierarchy(parent='{self.parent_role}', child='{self.child_role}', level={self.level})>"


class PermissionGrant(BaseModelWithAudit):
    """
    Permission grant model for tracking specific permission assignments.

    This model tracks individual permission grants to users or roles,
    allowing for fine-grained access control and audit logging.
    """

    __tablename__ = "permission_grants"

    # User ID (if permission granted to specific user)
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="User ID for user-specific permissions"
    )

    # Role (if permission granted to role)
    role = Column(
        String(20),
        nullable=True,
        index=True,
        doc="Role for role-based permissions"
    )

    # Permission
    permission = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Granted permission"
    )

    # Resource type and ID for resource-specific permissions
    resource_type = Column(
        String(20),
        nullable=True,
        index=True,
        doc="Resource type for resource-specific permissions"
    )

    resource_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Resource ID for resource-specific permissions"
    )

    # Grant details
    granted_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who granted the permission"
    )

    granted_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="When permission was granted"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When permission expires (null = never)"
    )

    # Active status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Permission grant active status"
    )

    # Metadata
    reason = Column(
        Text,
        nullable=True,
        doc="Reason for granting permission"
    )

    grant_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional metadata for permission grant"
    )

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="permission_grants")
    granted_by_user = relationship("User", foreign_keys=[granted_by])

    # Indexes for performance
    __table_args__ = (
        Index("idx_permission_grant_user", "user_id", "is_active"),
        Index("idx_permission_grant_role", "role", "is_active"),
        Index("idx_permission_grant_permission", "permission", "is_active"),
        Index("idx_permission_grant_resource", "resource_type", "resource_id", "is_active"),
        Index("idx_permission_grant_expires", "expires_at", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of permission grant."""
        target = f"user:{self.user_id}" if self.user_id else f"role:{self.role}"
        resource = f" on {self.resource_type}:{self.resource_id}" if self.resource_type else ""
        return f"<PermissionGrant({target} -> {self.permission}{resource})>"


class AccessControlLog(BaseModelWithAudit):
    """
    Access control audit log model for tracking all permission decisions.

    This model provides comprehensive audit logging for all access control
    decisions, enabling security monitoring and compliance reporting.
    """

    __tablename__ = "access_control_logs"

    # User and request details
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who made the request"
    )

    user_role = Column(
        String(20),
        nullable=True,
        index=True,
        doc="User role at time of request"
    )

    # Request details
    endpoint = Column(
        String(255),
        nullable=False,
        index=True,
        doc="API endpoint accessed"
    )

    method = Column(
        String(10),
        nullable=False,
        doc="HTTP method used"
    )

    # Permission details
    required_permission = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Required permission for access"
    )

    resource_type = Column(
        String(20),
        nullable=True,
        index=True,
        doc="Resource type accessed"
    )

    resource_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Resource ID accessed"
    )

    # Access decision
    decision = Column(
        Enum(AccessDecision),
        nullable=False,
        index=True,
        doc="Access control decision"
    )

    # Request metadata
    ip_address = Column(
        String(45),
        nullable=True,
        index=True,
        doc="Client IP address"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="Client user agent"
    )

    correlation_id = Column(
        String(36),
        nullable=True,
        index=True,
        doc="Request correlation ID"
    )

    # Timing
    request_timestamp = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="When request was made"
    )

    response_time_ms = Column(
        Integer,
        nullable=True,
        doc="Response time in milliseconds"
    )

    # Additional details
    reason = Column(
        Text,
        nullable=True,
        doc="Reason for access decision"
    )

    log_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional request metadata"
    )

    # Relationships
    user = relationship("User", back_populates="access_logs", foreign_keys=[user_id])

    # Indexes for performance and analytics
    __table_args__ = (
        Index("idx_access_log_user_timestamp", "user_id", "request_timestamp"),
        Index("idx_access_log_endpoint_timestamp", "endpoint", "request_timestamp"),
        Index("idx_access_log_decision_timestamp", "decision", "request_timestamp"),
        Index("idx_access_log_permission", "required_permission", "decision"),
        Index("idx_access_log_correlation", "correlation_id"),
        Index("idx_access_log_ip", "ip_address", "request_timestamp"),
    )

    def __repr__(self) -> str:
        """String representation of access control log."""
        return f"<AccessControlLog(user:{self.user_id}, {self.method} {self.endpoint}, {self.decision})>"


# Add relationships to User model (to be added via migration or model update)
# This would be added to the User model:
# permission_grants = relationship("PermissionGrant", foreign_keys="PermissionGrant.user_id", back_populates="user")
# access_logs = relationship("AccessControlLog", back_populates="user")
