# Culture Connect Backend - Prometheus Configuration
# Monitoring and metrics collection for production deployment

apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      scrape_timeout: 10s
      evaluation_interval: 15s
      external_labels:
        cluster: 'culture-connect-production'
        environment: 'production'
        region: 'us-west-2'

    rule_files:
      - "/etc/prometheus/rules/*.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
          scheme: http
          timeout: 10s
          api_version: v2

    scrape_configs:
      # Prometheus self-monitoring
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']
        scrape_interval: 30s
        metrics_path: /metrics

      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https

      # Kubernetes nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - target_label: __address__
            replacement: kubernetes.default.svc:443
          - source_labels: [__meta_kubernetes_node_name]
            regex: (.+)
            target_label: __metrics_path__
            replacement: /api/v1/nodes/${1}/proxy/metrics

      # Kubernetes pods
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # Culture Connect API
      - job_name: 'culture-connect-api'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - culture-connect
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: culture-connect-api
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        scrape_interval: 15s
        metrics_path: /metrics

      # Culture Connect Celery Workers
      - job_name: 'culture-connect-celery'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - culture-connect
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: culture-connect-celery-worker
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: metrics
        scrape_interval: 30s
        metrics_path: /metrics

      # Node Exporter
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_endpoints_name]
            action: keep
            regex: node-exporter
        scrape_interval: 30s

      # kube-state-metrics
      - job_name: 'kube-state-metrics'
        static_configs:
          - targets: ['kube-state-metrics:8080']
        scrape_interval: 30s

      # AWS Load Balancer Controller
      - job_name: 'aws-load-balancer-controller'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - kube-system
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: aws-load-balancer-webhook-service
        scrape_interval: 30s

      # External services monitoring
      - job_name: 'blackbox-http'
        metrics_path: /probe
        params:
          module: [http_2xx]
        static_configs:
          - targets:
            - https://api.cultureconnect.ng/health
            - https://cultureconnect.ng
            - https://app.cultureconnect.ng
        relabel_configs:
          - source_labels: [__address__]
            target_label: __param_target
          - source_labels: [__param_target]
            target_label: instance
          - target_label: __address__
            replacement: blackbox-exporter:9115

      # Database monitoring (if accessible)
      - job_name: 'postgres-exporter'
        static_configs:
          - targets: ['postgres-exporter:9187']
        scrape_interval: 30s

      # Redis monitoring (if accessible)
      - job_name: 'redis-exporter'
        static_configs:
          - targets: ['redis-exporter:9121']
        scrape_interval: 30s

  rules.yml: |
    groups:
    - name: culture-connect.rules
      rules:
      # API Performance Rules
      - record: culture_connect:api_request_rate
        expr: rate(http_requests_total{job="culture-connect-api"}[5m])
      
      - record: culture_connect:api_error_rate
        expr: rate(http_requests_total{job="culture-connect-api",status=~"5.."}[5m]) / rate(http_requests_total{job="culture-connect-api"}[5m])
      
      - record: culture_connect:api_response_time_p95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="culture-connect-api"}[5m]))
      
      - record: culture_connect:api_response_time_p99
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job="culture-connect-api"}[5m]))

      # Resource Utilization Rules
      - record: culture_connect:cpu_utilization
        expr: rate(container_cpu_usage_seconds_total{namespace="culture-connect"}[5m]) * 100
      
      - record: culture_connect:memory_utilization
        expr: (container_memory_usage_bytes{namespace="culture-connect"} / container_spec_memory_limit_bytes{namespace="culture-connect"}) * 100

      # Celery Rules
      - record: culture_connect:celery_queue_length
        expr: celery_queue_length{namespace="culture-connect"}
      
      - record: culture_connect:celery_task_rate
        expr: rate(celery_tasks_total{namespace="culture-connect"}[5m])

    - name: culture-connect.alerts
      rules:
      # Critical Alerts
      - alert: APIHighErrorRate
        expr: culture_connect:api_error_rate > 0.05
        for: 2m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "High API error rate detected"
          description: "API error rate is {{ $value | humanizePercentage }} for the last 2 minutes"
          runbook_url: "https://docs.cultureconnect.ng/runbooks/api-high-error-rate"

      - alert: APIHighResponseTime
        expr: culture_connect:api_response_time_p95 > 0.5
        for: 2m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API response time detected"
          description: "95th percentile response time is {{ $value }}s"
          runbook_url: "https://docs.cultureconnect.ng/runbooks/api-high-response-time"

      - alert: HighCPUUtilization
        expr: culture_connect:cpu_utilization > 85
        for: 5m
        labels:
          severity: warning
          component: infrastructure
        annotations:
          summary: "High CPU utilization detected"
          description: "CPU utilization is {{ $value }}% for {{ $labels.pod }}"

      - alert: HighMemoryUtilization
        expr: culture_connect:memory_utilization > 90
        for: 5m
        labels:
          severity: critical
          component: infrastructure
        annotations:
          summary: "High memory utilization detected"
          description: "Memory utilization is {{ $value }}% for {{ $labels.pod }}"

      - alert: CeleryQueueBacklog
        expr: culture_connect:celery_queue_length > 100
        for: 3m
        labels:
          severity: warning
          component: celery
        annotations:
          summary: "Celery queue backlog detected"
          description: "Queue length is {{ $value }} tasks"

      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total{namespace="culture-connect"}[15m]) > 0
        for: 5m
        labels:
          severity: critical
          component: infrastructure
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"

      - alert: ServiceDown
        expr: up{job="culture-connect-api"} == 0
        for: 1m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "Culture Connect API is down"
          description: "Culture Connect API service is not responding"
          runbook_url: "https://docs.cultureconnect.ng/runbooks/service-down"

      # Warning Alerts
      - alert: HighRequestRate
        expr: culture_connect:api_request_rate > 1000
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High request rate detected"
          description: "Request rate is {{ $value }} requests/second"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }}"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.85
        for: 5m
        labels:
          severity: warning
          component: redis
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"
