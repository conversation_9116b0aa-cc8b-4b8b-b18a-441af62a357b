"""
Integration test suite for password security functionality.

This module tests the integration between all password security components
including end-to-end workflows and cross-component interactions.

Tests cover Task 2.1.2 integration requirements.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.services.auth_service import AuthService
from app.schemas.auth import UserLogin, PasswordChange
from app.schemas.password_security import PasswordResetConfirm
from app.models.user import User
from app.core.security import get_password_hash


class TestPasswordSecurityIntegration:
    """Test password security integration scenarios."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        return PasswordSecurityService(mock_db_session)

    @pytest.fixture
    def auth_service(self, mock_db_session):
        """Create auth service instance."""
        return AuthService(mock_db_session)

    @pytest.fixture
    def sample_user(self):
        """Create sample user for testing."""
        user = Mock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.hashed_password = get_password_hash("CurrentP@ssw0rd!")
        user.is_active = True
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_complete_password_change_workflow(self, auth_service, sample_user):
        """Test complete password change workflow with all security checks."""
        user_id = 1
        password_data = PasswordChange(
            current_password="CurrentP@ssw0rd!",
            new_password="NewSecureP@ssw0rd!",
            confirm_password="NewSecureP@ssw0rd!"
        )
        
        # Mock all dependencies
        with patch.object(auth_service, '_get_user_by_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True, violations=[], suggestions=[])
                
                with patch.object(auth_service.password_security_service, 'check_password_history') as mock_history:
                    mock_history.return_value = False  # Not reused
                    
                    with patch.object(auth_service.password_security_service, 'add_password_to_history') as mock_add_history:
                        mock_add_history.return_value = True
                        
                        with patch.object(auth_service, 'db') as mock_db:
                            result = await auth_service.change_password(user_id, password_data)
                            
                            assert result["message"] == "Password changed successfully"
                            mock_validate.assert_called_once()
                            mock_history.assert_called_once()
                            mock_add_history.assert_called_once()

    @pytest.mark.asyncio
    async def test_login_with_account_lockout_protection(self, auth_service, sample_user):
        """Test login process with account lockout protection."""
        login_data = UserLogin(email="<EMAIL>", password="WrongPassword!")
        client_ip = "*************"
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'check_account_lockout') as mock_check_lockout:
                # Account not locked initially
                mock_check_lockout.return_value = Mock(is_locked=False)
                
                with patch.object(auth_service.password_security_service, 'record_failed_login') as mock_record_failed:
                    mock_record_failed.return_value = Mock(is_locked=False, failed_attempts=1)
                    
                    with pytest.raises(Exception):  # Should raise HTTPException for wrong password
                        await auth_service.authenticate_user(login_data, client_ip)
                    
                    mock_check_lockout.assert_called_once_with(sample_user.id)
                    mock_record_failed.assert_called_once_with(sample_user.id, client_ip)

    @pytest.mark.asyncio
    async def test_successful_login_resets_lockout(self, auth_service, sample_user):
        """Test that successful login resets account lockout."""
        login_data = UserLogin(email="<EMAIL>", password="CurrentP@ssw0rd!")
        client_ip = "*************"
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'check_account_lockout') as mock_check_lockout:
                mock_check_lockout.return_value = Mock(is_locked=False)
                
                with patch.object(auth_service.password_security_service, 'reset_lockout_on_success') as mock_reset_lockout:
                    with patch('app.core.security.create_token_pair') as mock_create_tokens:
                        mock_create_tokens.return_value = Mock()
                        
                        with patch.object(auth_service, 'db') as mock_db:
                            await auth_service.authenticate_user(login_data, client_ip)
                            
                            mock_reset_lockout.assert_called_once_with(sample_user.id)

    @pytest.mark.asyncio
    async def test_password_reset_with_history_check(self, password_service):
        """Test password reset with history validation."""
        token = "valid-reset-token"
        new_password = "NewSecureP@ssw0rd!"
        client_ip = "*************"
        
        # Mock valid token
        mock_token = Mock()
        mock_token.user_id = 1
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get_token:
            mock_get_token.return_value = mock_token
            
            with patch.object(password_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True, strength_score=85, strength_level="strong")
                
                with patch.object(password_service, 'check_password_history') as mock_history:
                    mock_history.return_value = False  # Not reused
                    
                    with patch.object(password_service, 'add_password_to_history') as mock_add_history:
                        with patch.object(password_service.reset_token_repo, 'mark_token_used') as mock_mark_used:
                            with patch.object(password_service, 'reset_lockout_on_success') as mock_reset_lockout:
                                
                                result = await password_service.reset_password_with_token(
                                    token, new_password, client_ip
                                )
                                
                                assert result.success is True
                                mock_history.assert_called_once_with(1, new_password)
                                mock_add_history.assert_called_once()
                                mock_reset_lockout.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_multiple_failed_logins_trigger_lockout(self, auth_service, sample_user):
        """Test that multiple failed logins trigger account lockout."""
        login_data = UserLogin(email="<EMAIL>", password="WrongPassword!")
        client_ip = "*************"
        
        with patch.object(auth_service, '_get_user_by_email') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            # Simulate progressive lockout
            lockout_statuses = [
                Mock(is_locked=False, failed_attempts=i) for i in range(1, 5)
            ] + [Mock(is_locked=True, failed_attempts=5, remaining_lockout_seconds=900)]
            
            with patch.object(auth_service.password_security_service, 'check_account_lockout') as mock_check_lockout:
                with patch.object(auth_service.password_security_service, 'record_failed_login') as mock_record_failed:
                    
                    for i, status in enumerate(lockout_statuses):
                        mock_check_lockout.return_value = Mock(is_locked=(i == 4))  # Locked on 5th attempt
                        mock_record_failed.return_value = status
                        
                        with pytest.raises(Exception):  # Should raise HTTPException
                            await auth_service.authenticate_user(login_data, client_ip)

    @pytest.mark.asyncio
    async def test_password_strength_integration_with_auth(self, auth_service, sample_user):
        """Test password strength validation integration with auth service."""
        user_id = 1
        weak_password_data = PasswordChange(
            current_password="CurrentP@ssw0rd!",
            new_password="weak",
            confirm_password="weak"
        )
        
        with patch.object(auth_service, '_get_user_by_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(
                    is_valid=False,
                    violations=["Password too short", "Missing uppercase"],
                    suggestions=["Add more characters", "Add uppercase letters"]
                )
                
                with pytest.raises(Exception):  # Should raise HTTPException for weak password
                    await auth_service.change_password(user_id, weak_password_data)

    @pytest.mark.asyncio
    async def test_password_history_prevents_reuse(self, auth_service, sample_user):
        """Test that password history prevents password reuse."""
        user_id = 1
        reused_password_data = PasswordChange(
            current_password="CurrentP@ssw0rd!",
            new_password="OldP@ssw0rd!",  # Previously used password
            confirm_password="OldP@ssw0rd!"
        )
        
        with patch.object(auth_service, '_get_user_by_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True)
                
                with patch.object(auth_service.password_security_service, 'check_password_history') as mock_history:
                    mock_history.return_value = True  # Password was used recently
                    
                    with pytest.raises(Exception):  # Should raise HTTPException for reused password
                        await auth_service.change_password(user_id, reused_password_data)

    @pytest.mark.asyncio
    async def test_security_event_logging_integration(self, password_service):
        """Test that security events are properly logged."""
        user_id = 1
        client_ip = "*************"
        
        with patch('app.core.security.log_password_security_event') as mock_log_event:
            with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
                mock_record.return_value = Mock(failed_attempts=1, is_locked=False)
                
                with patch.object(password_service, 'check_account_lockout') as mock_check:
                    mock_check.return_value = Mock(is_locked=False)
                    
                    await password_service.record_failed_login(user_id, client_ip)
                    
                    # Verify security event logging would be called
                    # (This depends on implementation details)

    @pytest.mark.asyncio
    async def test_concurrent_password_operations(self, password_service):
        """Test handling of concurrent password security operations."""
        user_id = 1
        
        # Simulate concurrent operations
        with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
            with patch.object(password_service.lockout_repo, 'reset_failed_attempts') as mock_reset:
                
                # Concurrent password change and lockout reset
                password_hash = get_password_hash("NewP@ssw0rd!")
                
                result1 = await password_service.add_password_to_history(user_id, password_hash)
                result2 = await password_service.reset_lockout_on_success(user_id)
                
                # Both operations should complete successfully
                mock_add.assert_called_once()
                mock_reset.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_operations_integration(self, password_service):
        """Test integration of cleanup operations."""
        with patch.object(password_service.reset_token_repo, 'cleanup_expired_tokens') as mock_cleanup_tokens:
            mock_cleanup_tokens.return_value = 5
            
            with patch.object(password_service.lockout_repo, 'cleanup_expired_lockouts') as mock_cleanup_lockouts:
                mock_cleanup_lockouts.return_value = 3
                
                # Run cleanup operations
                token_count = await password_service.cleanup_expired_tokens()
                lockout_count = await password_service.cleanup_expired_lockouts()
                
                assert token_count == 5
                assert lockout_count == 3
                mock_cleanup_tokens.assert_called_once()
                mock_cleanup_lockouts.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_recovery_in_integrated_workflow(self, auth_service, sample_user):
        """Test error recovery in integrated password security workflow."""
        user_id = 1
        password_data = PasswordChange(
            current_password="CurrentP@ssw0rd!",
            new_password="NewSecureP@ssw0rd!",
            confirm_password="NewSecureP@ssw0rd!"
        )
        
        with patch.object(auth_service, '_get_user_by_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            with patch.object(auth_service.password_security_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True)
                
                with patch.object(auth_service.password_security_service, 'check_password_history') as mock_history:
                    mock_history.return_value = False
                    
                    with patch.object(auth_service.password_security_service, 'add_password_to_history') as mock_add_history:
                        # Simulate error in password history addition
                        mock_add_history.side_effect = Exception("Database error")
                        
                        # The operation should still complete (password change is more important than history)
                        with patch.object(auth_service, 'db') as mock_db:
                            result = await auth_service.change_password(user_id, password_data)
                            
                            assert result["message"] == "Password changed successfully"

    @pytest.mark.asyncio
    async def test_configuration_integration(self, password_service):
        """Test that configuration settings are properly integrated."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.PASSWORD_HISTORY_COUNT = 10
            mock_settings.ACCOUNT_LOCKOUT_ATTEMPTS = 3
            mock_settings.ACCOUNT_LOCKOUT_DURATION_MINUTES = 30
            
            # Test that services use configured values
            user_id = 1
            
            with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
                mock_get_history.return_value = []
                
                await password_service.check_password_history(user_id, "TestP@ssw0rd!")
                
                # Should use configured history count
                mock_get_history.assert_called_once_with(user_id, 10)

    @pytest.mark.asyncio
    async def test_cross_service_data_consistency(self, auth_service, password_service, sample_user):
        """Test data consistency across password security services."""
        user_id = 1
        
        # Ensure both services work with the same user data
        with patch.object(auth_service, '_get_user_by_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            # Test that user ID is consistently used across services
            with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get_lockout:
                mock_get_lockout.return_value = None
                
                lockout_status = await password_service.check_account_lockout(user_id)
                
                assert lockout_status.is_locked is False
                mock_get_lockout.assert_called_once_with(user_id)
