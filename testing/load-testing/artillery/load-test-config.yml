# Culture Connect Backend - Artillery Load Testing Configuration
# Comprehensive load testing for >10,000 concurrent users

config:
  target: 'https://api.cultureconnect.ng'
  phases:
    # Warm-up phase
    - duration: 300  # 5 minutes
      arrivalRate: 10
      name: "Warm-up phase"
    
    # Gradual ramp-up
    - duration: 600  # 10 minutes
      arrivalRate: 50
      rampTo: 500
      name: "Gradual ramp-up"
    
    # Sustained load
    - duration: 1800  # 30 minutes
      arrivalRate: 1000
      name: "Sustained high load"
    
    # Peak load testing
    - duration: 900  # 15 minutes
      arrivalRate: 2000
      name: "Peak load testing"
    
    # Stress testing
    - duration: 600  # 10 minutes
      arrivalRate: 3000
      name: "Stress testing"
    
    # Cool-down
    - duration: 300  # 5 minutes
      arrivalRate: 100
      name: "Cool-down phase"

  defaults:
    headers:
      User-Agent: 'Artillery Load Test'
      Accept: 'application/json'
      Content-Type: 'application/json'
  
  http:
    timeout: 30
    pool: 50
    maxSockets: 1000
  
  processor: "./load-test-functions.js"
  
  variables:
    api_version: "v1"
    test_user_email: "<EMAIL>"
    test_user_password: "LoadTest123!"

  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
    publish-metrics:
      - type: prometheus
        pushgateway: 'http://prometheus-pushgateway:9091'
        prefix: 'artillery_'
        tags:
          - 'environment:production'
          - 'test_type:load_test'
    
    expect:
      outputFormat: json
      reportFailuresAsErrors: true

scenarios:
  # Health check scenario (10% of traffic)
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - hasProperty: "status"
            - equals:
              - "{{ status }}"
              - "healthy"
          capture:
            - json: "$.response_time"
              as: "health_response_time"
      
      - think: 1

  # User authentication scenario (20% of traffic)
  - name: "User Authentication"
    weight: 20
    flow:
      - post:
          url: "/api/{{ api_version }}/auth/login"
          json:
            email: "{{ test_user_email }}"
            password: "{{ test_user_password }}"
          expect:
            - statusCode: 200
            - hasProperty: "access_token"
          capture:
            - json: "$.access_token"
              as: "auth_token"
      
      - think: 2
      
      - get:
          url: "/api/{{ api_version }}/auth/me"
          headers:
            Authorization: "Bearer {{ auth_token }}"
          expect:
            - statusCode: 200
            - hasProperty: "user"

  # Vendor search scenario (30% of traffic)
  - name: "Vendor Search"
    weight: 30
    flow:
      - get:
          url: "/api/{{ api_version }}/vendors/search"
          qs:
            location: "Lagos"
            service_type: "catering"
            limit: 20
          expect:
            - statusCode: 200
            - hasProperty: "vendors"
          capture:
            - json: "$.vendors[0].id"
              as: "vendor_id"
      
      - think: 3
      
      - get:
          url: "/api/{{ api_version }}/vendors/{{ vendor_id }}"
          expect:
            - statusCode: 200
            - hasProperty: "vendor"

  # Booking creation scenario (25% of traffic)
  - name: "Booking Creation"
    weight: 25
    flow:
      # Authenticate first
      - post:
          url: "/api/{{ api_version }}/auth/login"
          json:
            email: "{{ test_user_email }}"
            password: "{{ test_user_password }}"
          capture:
            - json: "$.access_token"
              as: "auth_token"
      
      - think: 2
      
      # Get available vendors
      - get:
          url: "/api/{{ api_version }}/vendors/search"
          qs:
            location: "Lagos"
            service_type: "catering"
            limit: 5
          capture:
            - json: "$.vendors[0].id"
              as: "vendor_id"
      
      - think: 5
      
      # Create booking
      - post:
          url: "/api/{{ api_version }}/bookings"
          headers:
            Authorization: "Bearer {{ auth_token }}"
          json:
            vendor_id: "{{ vendor_id }}"
            event_date: "2024-12-25"
            event_type: "wedding"
            guest_count: 150
            budget: 500000
            location: "Lagos, Nigeria"
            description: "Load test booking"
          expect:
            - statusCode: 201
            - hasProperty: "booking"
          capture:
            - json: "$.booking.id"
              as: "booking_id"

  # Payment processing scenario (10% of traffic)
  - name: "Payment Processing"
    weight: 10
    flow:
      # Authenticate
      - post:
          url: "/api/{{ api_version }}/auth/login"
          json:
            email: "{{ test_user_email }}"
            password: "{{ test_user_password }}"
          capture:
            - json: "$.access_token"
              as: "auth_token"
      
      - think: 2
      
      # Initiate payment
      - post:
          url: "/api/{{ api_version }}/payments/initialize"
          headers:
            Authorization: "Bearer {{ auth_token }}"
          json:
            amount: 50000
            currency: "NGN"
            payment_method: "paystack"
            reference: "load_test_{{ $randomString() }}"
          expect:
            - statusCode: 200
            - hasProperty: "payment_url"

  # WebSocket connection scenario (5% of traffic)
  - name: "WebSocket Communication"
    weight: 5
    engine: ws
    flow:
      - connect:
          url: "wss://api.cultureconnect.ng/api/{{ api_version }}/websocket/rooms/general"
          headers:
            Authorization: "Bearer {{ auth_token }}"
      
      - send:
          payload: |
            {
              "type": "join_room",
              "room": "general",
              "user_id": "load_test_user"
            }
      
      - think: 10
      
      - send:
          payload: |
            {
              "type": "message",
              "room": "general",
              "message": "Load test message",
              "timestamp": "{{ $timestamp() }}"
            }
      
      - think: 5

# Performance thresholds
expect:
  # Response time thresholds
  - http.response_time.p95: 500  # 95th percentile < 500ms
  - http.response_time.p99: 1000  # 99th percentile < 1000ms
  - http.response_time.median: 200  # Median < 200ms
  
  # Error rate thresholds
  - http.request_rate: "> 1000"  # > 1000 requests/second
  - http.codes.200: "> 95%"  # > 95% success rate
  - http.codes.4xx: "< 2%"   # < 2% client errors
  - http.codes.5xx: "< 1%"   # < 1% server errors
  
  # Throughput thresholds
  - vusers.created_by_name.total: "> 10000"  # > 10,000 virtual users
  - vusers.completed: "> 95%"  # > 95% completion rate

# Reporting configuration
reporting:
  json: "./reports/load-test-results.json"
  html: "./reports/load-test-report.html"
  
  plugins:
    - name: "html-report"
      options:
        output: "./reports/detailed-report.html"
        title: "Culture Connect Load Test Report"
        description: "Comprehensive load testing for >10,000 concurrent users"
