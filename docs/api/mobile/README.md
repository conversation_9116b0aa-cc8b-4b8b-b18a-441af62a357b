# Culture Connect Mobile App API Documentation

**Target Audience**: Mobile app developers (<PERSON><PERSON><PERSON>, React Native, Native iOS/Android)  
**API Version**: v1  
**Base URL**: `https://api.cultureconnect.ng/api/v1`  
**Documentation Version**: 1.0.0  
**Last Updated**: 2025-02-01

## 📱 Overview

The Culture Connect Mobile API provides comprehensive endpoints for tourist-focused mobile applications, enabling cultural discovery, service booking, real-time updates, and seamless payment processing. This API is optimized for mobile consumption with efficient data structures, offline sync capabilities, and push notification support.

### Key Features
- **Cultural Discovery**: Explore local experiences, events, and services
- **Service Booking**: Book tours, accommodations, and cultural experiences
- **Real-time Updates**: Live notifications and status updates
- **Payment Processing**: Multi-currency payment support (Paystack, Stripe, Busha)
- **Geolocation Services**: Location-based recommendations and services
- **Offline Sync**: Robust offline capabilities with data synchronization
- **Push Notifications**: Real-time engagement and booking updates

## 🔐 Authentication

### Authentication Flow

The Culture Connect API uses JWT (JSON Web Tokens) for authentication with refresh token rotation for enhanced security.

#### 1. User Registration
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+1234567890",
  "country_code": "US",
  "preferred_language": "en",
  "date_of_birth": "1990-01-01",
  "marketing_consent": true
}
```

**Response (201 Created):**
```json
{
  "user": {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+1234567890",
    "country_code": "US",
    "preferred_language": "en",
    "is_verified": false,
    "created_at": "2025-02-01T10:00:00Z"
  },
  "message": "Registration successful. Please verify your email."
}
```

#### 2. Email Verification
```http
POST /auth/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verification_code": "123456"
}
```

**Response (200 OK):**
```json
{
  "message": "Email verified successfully",
  "user": {
    "id": "uuid-string",
    "is_verified": true
  }
}
```

#### 3. User Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "tourist",
    "permissions": ["booking_create", "booking_view", "payment_process"]
  }
}
```

#### 4. Token Refresh
```http
POST /auth/refresh
Content-Type: application/json
Authorization: Bearer {refresh_token}

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Flutter Integration Example

```dart
// auth_service.dart
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthService {
  final Dio _dio = Dio();
  final FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String baseUrl = 'https://api.cultureconnect.ng/api/v1';

  Future<AuthResponse> login(String email, String password) async {
    try {
      final response = await _dio.post(
        '$baseUrl/auth/login',
        data: {
          'email': email,
          'password': password,
        },
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      // Store tokens securely
      await _storage.write(key: 'access_token', value: authResponse.accessToken);
      await _storage.write(key: 'refresh_token', value: authResponse.refreshToken);
      
      return authResponse;
    } on DioException catch (e) {
      throw AuthException.fromDioError(e);
    }
  }

  Future<void> setupInterceptors() async {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _storage.read(key: 'access_token');
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token expired, try to refresh
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry original request
              final opts = error.requestOptions;
              final token = await _storage.read(key: 'access_token');
              opts.headers['Authorization'] = 'Bearer $token';
              final response = await _dio.fetch(opts);
              handler.resolve(response);
              return;
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storage.read(key: 'refresh_token');
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '$baseUrl/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      final authResponse = AuthResponse.fromJson(response.data);
      await _storage.write(key: 'access_token', value: authResponse.accessToken);
      await _storage.write(key: 'refresh_token', value: authResponse.refreshToken);
      
      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }

  Future<void> logout() async {
    await _storage.deleteAll();
  }
}

// Models
class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final User user;

  AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      tokenType: json['token_type'],
      expiresIn: json['expires_in'],
      user: User.fromJson(json['user']),
    );
  }
}

class User {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String role;
  final List<String> permissions;

  User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    required this.permissions,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      role: json['role'],
      permissions: List<String>.from(json['permissions']),
    );
  }
}

class AuthException implements Exception {
  final String message;
  final int? statusCode;

  AuthException(this.message, [this.statusCode]);

  factory AuthException.fromDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AuthException('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data['detail'] ?? 'Authentication failed';
        return AuthException(message, statusCode);
      default:
        return AuthException('Network error. Please try again.');
    }
  }
}
```

### Get Experience Details
```http
GET /experiences/{experience_id}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "id": "exp_123",
  "title": "Traditional Yoruba Cultural Tour",
  "description": "Immerse yourself in authentic Yoruba traditions with our expert local guides...",
  "long_description": "This comprehensive 3-hour cultural tour takes you through...",
  "category": "cultural_tour",
  "vendor": {
    "id": "vendor_456",
    "name": "Lagos Cultural Heritage",
    "description": "Authentic cultural experiences since 2015",
    "rating": 4.8,
    "review_count": 1250,
    "verified": true,
    "response_time": "within 2 hours",
    "languages": ["en", "yo", "ig"]
  },
  "location": {
    "latitude": 6.5244,
    "longitude": 3.3792,
    "address": "Lagos Island, Lagos, Nigeria",
    "landmark": "Near National Theatre",
    "accessibility": "wheelchair_accessible"
  },
  "pricing": {
    "base_price": 15000,
    "currency": "NGN",
    "price_breakdown": {
      "guide_fee": 8000,
      "transportation": 4000,
      "refreshments": 2000,
      "materials": 1000
    },
    "group_discounts": [
      {"min_size": 5, "discount_percent": 10},
      {"min_size": 10, "discount_percent": 20}
    ],
    "cancellation_policy": {
      "free_cancellation_hours": 24,
      "partial_refund_hours": 12,
      "no_refund_hours": 2
    }
  },
  "itinerary": [
    {
      "time": "09:00",
      "activity": "Meet at Lagos Island Cultural Center",
      "duration_minutes": 15,
      "description": "Introduction and welcome ceremony"
    },
    {
      "time": "09:15",
      "activity": "Traditional Yoruba History Presentation",
      "duration_minutes": 45,
      "description": "Learn about ancient Yoruba civilization"
    },
    {
      "time": "10:00",
      "activity": "Craft Workshop",
      "duration_minutes": 60,
      "description": "Hands-on traditional craft making"
    }
  ],
  "inclusions": [
    "Professional local guide",
    "Transportation within tour area",
    "Traditional refreshments",
    "Craft materials",
    "Certificate of participation"
  ],
  "exclusions": [
    "Hotel pickup/drop-off",
    "Personal expenses",
    "Gratuities"
  ],
  "requirements": [
    "Comfortable walking shoes",
    "Sun protection",
    "Respect for local customs"
  ],
  "reviews": {
    "average_rating": 4.7,
    "total_reviews": 234,
    "rating_distribution": {
      "5": 156,
      "4": 58,
      "3": 15,
      "2": 3,
      "1": 2
    },
    "recent_reviews": [
      {
        "id": "review_789",
        "user": {
          "name": "Sarah M.",
          "country": "US",
          "verified_booking": true
        },
        "rating": 5,
        "comment": "Absolutely amazing experience! The guide was knowledgeable...",
        "date": "2025-01-28T14:30:00Z",
        "helpful_votes": 12
      }
    ]
  }
}
```

## 📅 Booking Management

### Create Booking
```http
POST /bookings
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "experience_id": "exp_123",
  "booking_date": "2025-02-15",
  "time_slot": "09:00",
  "participants": [
    {
      "type": "adult",
      "first_name": "John",
      "last_name": "Doe",
      "age": 30,
      "dietary_restrictions": ["vegetarian"],
      "special_requirements": "Wheelchair accessible"
    },
    {
      "type": "child",
      "first_name": "Jane",
      "last_name": "Doe",
      "age": 8,
      "dietary_restrictions": [],
      "special_requirements": null
    }
  ],
  "contact_info": {
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "emergency_contact": {
      "name": "Mary Doe",
      "phone": "+1234567891",
      "relationship": "spouse"
    }
  },
  "special_requests": "Celebrating anniversary",
  "payment_method": "card",
  "promo_code": "WELCOME10"
}
```

**Response (201 Created):**
```json
{
  "booking": {
    "id": "booking_abc123",
    "reference_number": "CC-2025-001234",
    "status": "pending_payment",
    "experience": {
      "id": "exp_123",
      "title": "Traditional Yoruba Cultural Tour",
      "vendor_name": "Lagos Cultural Heritage"
    },
    "booking_details": {
      "date": "2025-02-15",
      "time_slot": "09:00",
      "duration_hours": 3,
      "participant_count": 2,
      "participants": [
        {
          "type": "adult",
          "name": "John Doe",
          "age": 30
        },
        {
          "type": "child",
          "name": "Jane Doe",
          "age": 8
        }
      ]
    },
    "pricing": {
      "subtotal": 30000,
      "discount": 3000,
      "tax": 2700,
      "total": 29700,
      "currency": "NGN",
      "breakdown": {
        "adult_price": 15000,
        "child_price": 15000,
        "promo_discount": -3000,
        "vat": 2700
      }
    },
    "payment": {
      "amount": 29700,
      "currency": "NGN",
      "status": "pending",
      "payment_url": "https://api.cultureconnect.ng/payments/booking_abc123/pay",
      "expires_at": "2025-02-01T11:00:00Z"
    },
    "contact_info": {
      "phone": "+1234567890",
      "email": "<EMAIL>"
    },
    "created_at": "2025-02-01T10:00:00Z",
    "expires_at": "2025-02-01T11:00:00Z"
  },
  "next_steps": [
    "Complete payment within 1 hour",
    "Check email for booking confirmation",
    "Arrive 15 minutes before start time"
  ]
}
```

### Get User Bookings
```http
GET /bookings?status={status}&page={page}&limit={limit}
Authorization: Bearer {access_token}
```

**Parameters:**
- `status` (optional): Filter by booking status (pending_payment, confirmed, completed, cancelled)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 20, max: 100)

**Response (200 OK):**
```json
{
  "bookings": [
    {
      "id": "booking_abc123",
      "reference_number": "CC-2025-001234",
      "status": "confirmed",
      "experience": {
        "id": "exp_123",
        "title": "Traditional Yoruba Cultural Tour",
        "featured_image": "https://cdn.cultureconnect.ng/experiences/exp_123/featured.jpg",
        "vendor": {
          "name": "Lagos Cultural Heritage",
          "phone": "+2341234567890"
        }
      },
      "booking_date": "2025-02-15",
      "time_slot": "09:00",
      "participant_count": 2,
      "total_amount": 29700,
      "currency": "NGN",
      "status_history": [
        {
          "status": "pending_payment",
          "timestamp": "2025-02-01T10:00:00Z"
        },
        {
          "status": "confirmed",
          "timestamp": "2025-02-01T10:15:00Z"
        }
      ],
      "can_cancel": true,
      "can_modify": true,
      "cancellation_deadline": "2025-02-14T09:00:00Z",
      "created_at": "2025-02-01T10:00:00Z"
    }
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "per_page": 20,
    "total_pages": 1
  },
  "summary": {
    "total_bookings": 15,
    "confirmed": 12,
    "pending": 1,
    "completed": 2,
    "cancelled": 0
  }
}
```

### Get Booking Details
```http
GET /bookings/{booking_id}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "id": "booking_abc123",
  "reference_number": "CC-2025-001234",
  "status": "confirmed",
  "experience": {
    "id": "exp_123",
    "title": "Traditional Yoruba Cultural Tour",
    "description": "Immerse yourself in authentic Yoruba traditions...",
    "vendor": {
      "id": "vendor_456",
      "name": "Lagos Cultural Heritage",
      "phone": "+2341234567890",
      "email": "<EMAIL>",
      "address": "Lagos Island, Lagos, Nigeria"
    },
    "meeting_point": {
      "name": "Lagos Island Cultural Center",
      "address": "Tafawa Balewa Square, Lagos Island",
      "latitude": 6.5244,
      "longitude": 3.3792,
      "instructions": "Meet at the main entrance near the fountain"
    }
  },
  "booking_details": {
    "date": "2025-02-15",
    "time_slot": "09:00",
    "duration_hours": 3,
    "estimated_end_time": "12:00",
    "participants": [
      {
        "type": "adult",
        "name": "John Doe",
        "age": 30,
        "dietary_restrictions": ["vegetarian"],
        "special_requirements": "Wheelchair accessible"
      },
      {
        "type": "child",
        "name": "Jane Doe",
        "age": 8,
        "dietary_restrictions": [],
        "special_requirements": null
      }
    ],
    "special_requests": "Celebrating anniversary"
  },
  "contact_info": {
    "primary": {
      "phone": "+1234567890",
      "email": "<EMAIL>"
    },
    "emergency": {
      "name": "Mary Doe",
      "phone": "+1234567891",
      "relationship": "spouse"
    }
  },
  "pricing": {
    "subtotal": 30000,
    "discount": 3000,
    "tax": 2700,
    "total": 29700,
    "currency": "NGN",
    "breakdown": {
      "adult_price": 15000,
      "child_price": 15000,
      "promo_discount": -3000,
      "vat": 2700
    }
  },
  "payment": {
    "status": "completed",
    "method": "card",
    "transaction_id": "txn_xyz789",
    "paid_at": "2025-02-01T10:15:00Z",
    "receipt_url": "https://api.cultureconnect.ng/receipts/booking_abc123.pdf"
  },
  "cancellation_policy": {
    "can_cancel": true,
    "free_cancellation_until": "2025-02-14T09:00:00Z",
    "partial_refund_until": "2025-02-15T07:00:00Z",
    "refund_percentage": 100
  },
  "status_history": [
    {
      "status": "pending_payment",
      "timestamp": "2025-02-01T10:00:00Z",
      "note": "Booking created, awaiting payment"
    },
    {
      "status": "confirmed",
      "timestamp": "2025-02-01T10:15:00Z",
      "note": "Payment successful, booking confirmed"
    }
  ],
  "qr_code": "https://api.cultureconnect.ng/bookings/booking_abc123/qr",
  "created_at": "2025-02-01T10:00:00Z",
  "updated_at": "2025-02-01T10:15:00Z"
}
```

## 💳 Payment Processing

### Initialize Payment
```http
POST /payments/initialize
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "booking_id": "booking_abc123",
  "payment_method": "card",
  "provider": "paystack",
  "currency": "NGN",
  "return_url": "cultureconnect://payment/success",
  "cancel_url": "cultureconnect://payment/cancel"
}
```

**Response (200 OK):**
```json
{
  "payment": {
    "id": "payment_xyz789",
    "booking_id": "booking_abc123",
    "amount": 29700,
    "currency": "NGN",
    "status": "pending",
    "provider": "paystack",
    "payment_url": "https://checkout.paystack.com/xyz789",
    "reference": "CC_2025_001234_xyz789",
    "expires_at": "2025-02-01T11:00:00Z"
  },
  "redirect_info": {
    "type": "web_redirect",
    "url": "https://checkout.paystack.com/xyz789",
    "method": "GET"
  }
}
```

### Verify Payment
```http
GET /payments/{payment_id}/verify
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "payment": {
    "id": "payment_xyz789",
    "booking_id": "booking_abc123",
    "status": "completed",
    "amount": 29700,
    "currency": "NGN",
    "provider": "paystack",
    "transaction_id": "txn_paystack_123456",
    "paid_at": "2025-02-01T10:15:00Z",
    "receipt_url": "https://api.cultureconnect.ng/receipts/payment_xyz789.pdf"
  },
  "booking": {
    "id": "booking_abc123",
    "status": "confirmed",
    "confirmation_number": "CC-2025-001234"
  }
}
```

### Multi-Currency Support
```http
GET /payments/exchange-rates?from={from_currency}&to={to_currency}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "exchange_rates": {
    "from": "USD",
    "to": "NGN",
    "rate": 1650.50,
    "updated_at": "2025-02-01T10:00:00Z"
  },
  "supported_currencies": [
    {
      "code": "NGN",
      "name": "Nigerian Naira",
      "symbol": "₦",
      "providers": ["paystack"]
    },
    {
      "code": "USD",
      "name": "US Dollar",
      "symbol": "$",
      "providers": ["stripe", "busha"]
    },
    {
      "code": "EUR",
      "name": "Euro",
      "symbol": "€",
      "providers": ["stripe"]
    }
  ]
}
```

## 🔔 Real-time Updates & WebSocket

### WebSocket Connection
```javascript
// Connect to WebSocket for real-time updates
const ws = new WebSocket('wss://api.cultureconnect.ng/api/v1/websocket/user/{user_id}');

ws.onopen = function(event) {
  console.log('Connected to Culture Connect WebSocket');

  // Authenticate WebSocket connection
  ws.send(JSON.stringify({
    type: 'authenticate',
    token: 'your_jwt_token_here'
  }));
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);

  switch(message.type) {
    case 'booking_confirmed':
      handleBookingConfirmed(message.data);
      break;
    case 'booking_reminder':
      handleBookingReminder(message.data);
      break;
    case 'payment_status':
      handlePaymentStatus(message.data);
      break;
    case 'experience_update':
      handleExperienceUpdate(message.data);
      break;
    default:
      console.log('Unknown message type:', message.type);
  }
};

function handleBookingConfirmed(data) {
  // Show notification to user
  showNotification({
    title: 'Booking Confirmed!',
    body: `Your booking for ${data.experience_title} is confirmed.`,
    data: data
  });
}
```

### Push Notification Registration
```http
POST /notifications/register-device
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "device_token": "fcm_token_or_apns_token",
  "platform": "ios",
  "app_version": "1.0.0",
  "device_info": {
    "model": "iPhone 14",
    "os_version": "17.0",
    "app_build": "100"
  },
  "notification_preferences": {
    "booking_updates": true,
    "payment_reminders": true,
    "experience_recommendations": true,
    "promotional_offers": false
  }
}
```

**Response (201 Created):**
```json
{
  "device": {
    "id": "device_123",
    "token": "fcm_token_or_apns_token",
    "platform": "ios",
    "registered_at": "2025-02-01T10:00:00Z",
    "last_active": "2025-02-01T10:00:00Z"
  },
  "preferences": {
    "booking_updates": true,
    "payment_reminders": true,
    "experience_recommendations": true,
    "promotional_offers": false
  }
}
```

## 🌍 Geolocation Services

### Get Location-based Recommendations
```http
POST /recommendations/location
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "latitude": 6.5244,
  "longitude": 3.3792,
  "radius_km": 25,
  "preferences": {
    "categories": ["cultural_tour", "food_experience"],
    "price_range": {"min": 5000, "max": 50000},
    "duration_hours": {"min": 2, "max": 6},
    "group_size": 2
  },
  "exclude_visited": true
}
```

**Response (200 OK):**
```json
{
  "recommendations": [
    {
      "id": "exp_456",
      "title": "Lagos Street Food Adventure",
      "category": "food_experience",
      "distance_km": 3.2,
      "rating": 4.9,
      "price": 12000,
      "currency": "NGN",
      "match_score": 0.95,
      "reasons": [
        "Popular in your area",
        "Matches your food preferences",
        "Highly rated by similar users"
      ],
      "featured_image": "https://cdn.cultureconnect.ng/experiences/exp_456/featured.jpg"
    }
  ],
  "location_info": {
    "city": "Lagos",
    "state": "Lagos State",
    "country": "Nigeria",
    "timezone": "Africa/Lagos",
    "local_time": "2025-02-01T11:00:00+01:00"
  }
}
```

### Nearby Services
```http
GET /services/nearby?lat={lat}&lng={lng}&type={service_type}&radius={radius}
Authorization: Bearer {access_token}
```

**Parameters:**
- `lat` (required): Latitude
- `lng` (required): Longitude
- `type` (optional): Service type (atm, hospital, police, embassy, transport)
- `radius` (optional): Search radius in km (default: 5, max: 25)

**Response (200 OK):**
```json
{
  "services": [
    {
      "id": "service_789",
      "name": "First Bank ATM",
      "type": "atm",
      "distance_km": 0.8,
      "location": {
        "latitude": 6.5280,
        "longitude": 3.3800,
        "address": "Victoria Island, Lagos"
      },
      "details": {
        "bank": "First Bank",
        "available_24h": true,
        "accepts_international_cards": true
      },
      "walking_time_minutes": 10
    }
  ],
  "emergency_contacts": {
    "police": "199",
    "medical": "199",
    "fire": "199",
    "tourist_helpline": "+234-1-234-5678"
  }
}
```

## ❌ Error Handling

### HTTP Status Codes

| Status Code | Description | Common Causes |
|-------------|-------------|---------------|
| 200 | OK | Successful request |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Invalid or expired token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource already exists |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |
| 503 | Service Unavailable | Maintenance mode |

### Error Response Format

All error responses follow a consistent format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request data is invalid",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format",
        "code": "INVALID_EMAIL"
      },
      {
        "field": "password",
        "message": "Password must be at least 8 characters",
        "code": "PASSWORD_TOO_SHORT"
      }
    ],
    "correlation_id": "req_123456789",
    "timestamp": "2025-02-01T10:00:00Z"
  }
}
```

### Common Error Codes

#### Authentication Errors
- `INVALID_CREDENTIALS`: Wrong email/password
- `TOKEN_EXPIRED`: JWT token has expired
- `TOKEN_INVALID`: Malformed or invalid token
- `ACCOUNT_LOCKED`: Account temporarily locked
- `EMAIL_NOT_VERIFIED`: Email verification required

#### Booking Errors
- `EXPERIENCE_NOT_AVAILABLE`: Experience not available for selected date/time
- `INSUFFICIENT_CAPACITY`: Not enough spots available
- `BOOKING_EXPIRED`: Booking session expired
- `PAYMENT_REQUIRED`: Payment needed to confirm booking
- `CANCELLATION_NOT_ALLOWED`: Booking cannot be cancelled

#### Payment Errors
- `PAYMENT_FAILED`: Payment processing failed
- `INSUFFICIENT_FUNDS`: Not enough funds in account
- `CARD_DECLINED`: Card was declined
- `CURRENCY_NOT_SUPPORTED`: Currency not supported for this provider
- `PAYMENT_TIMEOUT`: Payment session timed out

### Flutter Error Handling Example

```dart
// error_handler.dart
class ApiException implements Exception {
  final String code;
  final String message;
  final List<ValidationError>? details;
  final String? correlationId;
  final int? statusCode;

  ApiException({
    required this.code,
    required this.message,
    this.details,
    this.correlationId,
    this.statusCode,
  });

  factory ApiException.fromResponse(Response response) {
    final data = response.data;
    return ApiException(
      code: data['error']['code'],
      message: data['error']['message'],
      details: data['error']['details']?.map<ValidationError>(
        (detail) => ValidationError.fromJson(detail)
      ).toList(),
      correlationId: data['error']['correlation_id'],
      statusCode: response.statusCode,
    );
  }

  bool get isNetworkError => statusCode == null;
  bool get isAuthError => statusCode == 401;
  bool get isValidationError => statusCode == 422;
  bool get isRateLimitError => statusCode == 429;
}

class ValidationError {
  final String field;
  final String message;
  final String code;

  ValidationError({
    required this.field,
    required this.message,
    required this.code,
  });

  factory ValidationError.fromJson(Map<String, dynamic> json) {
    return ValidationError(
      field: json['field'],
      message: json['message'],
      code: json['code'],
    );
  }
}

// Usage in service
class BookingService {
  Future<Booking> createBooking(BookingRequest request) async {
    try {
      final response = await _dio.post('/bookings', data: request.toJson());
      return Booking.fromJson(response.data['booking']);
    } on DioException catch (e) {
      if (e.response != null) {
        throw ApiException.fromResponse(e.response!);
      } else {
        throw ApiException(
          code: 'NETWORK_ERROR',
          message: 'Network connection failed. Please check your internet connection.',
        );
      }
    }
  }
}

// Usage in UI
void _createBooking() async {
  try {
    setState(() => _isLoading = true);

    final booking = await _bookingService.createBooking(_bookingRequest);

    // Navigate to payment or confirmation
    Navigator.pushNamed(context, '/payment', arguments: booking);

  } on ApiException catch (e) {
    String userMessage;

    switch (e.code) {
      case 'EXPERIENCE_NOT_AVAILABLE':
        userMessage = 'Sorry, this experience is no longer available for your selected date.';
        break;
      case 'INSUFFICIENT_CAPACITY':
        userMessage = 'Not enough spots available. Please select fewer participants.';
        break;
      case 'VALIDATION_ERROR':
        userMessage = e.details?.first.message ?? e.message;
        break;
      default:
        userMessage = e.message;
    }

    _showErrorDialog(userMessage);

  } finally {
    setState(() => _isLoading = false);
  }
}
```

## 🚦 Rate Limiting & Performance

### Rate Limits

The Culture Connect API implements rate limiting to ensure fair usage and optimal performance:

| Endpoint Category | Rate Limit | Window | Burst Limit |
|------------------|------------|---------|-------------|
| Authentication | 10 requests | 1 minute | 20 requests |
| Search/Discovery | 100 requests | 1 minute | 150 requests |
| Booking Operations | 30 requests | 1 minute | 50 requests |
| Payment Processing | 20 requests | 1 minute | 30 requests |
| User Profile | 60 requests | 1 minute | 100 requests |
| WebSocket Connections | 5 connections | 1 minute | 10 connections |

### Rate Limit Headers

All API responses include rate limiting information in headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1643723400
X-RateLimit-Window: 60
```

### Rate Limit Exceeded Response

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests. Please try again later.",
    "details": {
      "limit": 100,
      "window_seconds": 60,
      "retry_after": 45
    },
    "correlation_id": "req_123456789",
    "timestamp": "2025-02-01T10:00:00Z"
  }
}
```

### Performance Optimization Tips

1. **Implement Request Caching**: Cache responses for static data (categories, locations)
2. **Use Pagination**: Always use pagination for list endpoints
3. **Batch Requests**: Combine multiple operations when possible
4. **Implement Retry Logic**: Use exponential backoff for failed requests
5. **Optimize Images**: Use appropriate image sizes for mobile displays

## 📱 Offline Sync Patterns

### Offline-First Architecture

The Culture Connect mobile app supports robust offline functionality:

#### 1. Data Synchronization Strategy

```dart
// sync_manager.dart
class SyncManager {
  final LocalDatabase _localDb;
  final ApiService _apiService;
  final ConnectivityService _connectivity;

  Future<void> syncData() async {
    if (!await _connectivity.isConnected()) {
      return; // Skip sync if offline
    }

    try {
      // Sync user bookings
      await _syncBookings();

      // Sync favorite experiences
      await _syncFavorites();

      // Sync user profile
      await _syncUserProfile();

      // Upload pending actions
      await _uploadPendingActions();

    } catch (e) {
      print('Sync failed: $e');
    }
  }

  Future<void> _syncBookings() async {
    final lastSync = await _localDb.getLastSyncTime('bookings');
    final response = await _apiService.getBookings(since: lastSync);

    for (final booking in response.bookings) {
      await _localDb.upsertBooking(booking);
    }

    await _localDb.setLastSyncTime('bookings', DateTime.now());
  }

  Future<void> _uploadPendingActions() async {
    final pendingActions = await _localDb.getPendingActions();

    for (final action in pendingActions) {
      try {
        switch (action.type) {
          case 'favorite_add':
            await _apiService.addFavorite(action.data['experience_id']);
            break;
          case 'favorite_remove':
            await _apiService.removeFavorite(action.data['experience_id']);
            break;
          case 'review_submit':
            await _apiService.submitReview(action.data);
            break;
        }

        await _localDb.removePendingAction(action.id);
      } catch (e) {
        print('Failed to upload action ${action.id}: $e');
      }
    }
  }
}
```

#### 2. Offline Data Storage

```dart
// local_database.dart
class LocalDatabase {
  late Database _database;

  Future<void> initialize() async {
    _database = await openDatabase(
      'culture_connect.db',
      version: 1,
      onCreate: _createTables,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Experiences table
    await db.execute('''
      CREATE TABLE experiences (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        category TEXT,
        vendor_data TEXT,
        location_data TEXT,
        pricing_data TEXT,
        media_data TEXT,
        cached_at INTEGER,
        expires_at INTEGER
      )
    ''');

    // Bookings table
    await db.execute('''
      CREATE TABLE bookings (
        id TEXT PRIMARY KEY,
        reference_number TEXT,
        status TEXT,
        experience_data TEXT,
        booking_details TEXT,
        contact_info TEXT,
        pricing_data TEXT,
        payment_data TEXT,
        created_at INTEGER,
        updated_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Pending actions table
    await db.execute('''
      CREATE TABLE pending_actions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at INTEGER,
        retry_count INTEGER DEFAULT 0
      )
    ''');
  }

  Future<List<Experience>> getCachedExperiences({String? category}) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    String query = '''
      SELECT * FROM experiences
      WHERE expires_at > ?
    ''';

    List<dynamic> args = [now];

    if (category != null) {
      query += ' AND category = ?';
      args.add(category);
    }

    final results = await _database.rawQuery(query, args);

    return results.map((row) => Experience.fromCachedJson(row)).toList();
  }

  Future<void> cacheExperience(Experience experience) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours

    await _database.insert(
      'experiences',
      {
        'id': experience.id,
        'title': experience.title,
        'description': experience.description,
        'category': experience.category,
        'vendor_data': jsonEncode(experience.vendor.toJson()),
        'location_data': jsonEncode(experience.location.toJson()),
        'pricing_data': jsonEncode(experience.pricing.toJson()),
        'media_data': jsonEncode(experience.media.toJson()),
        'cached_at': now,
        'expires_at': expiresAt,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
}
```

#### 3. Offline UI Patterns

```dart
// offline_aware_widget.dart
class OfflineAwareWidget extends StatefulWidget {
  final Widget child;
  final Widget? offlineChild;

  const OfflineAwareWidget({
    Key? key,
    required this.child,
    this.offlineChild,
  }) : super(key: key);

  @override
  _OfflineAwareWidgetState createState() => _OfflineAwareWidgetState();
}

class _OfflineAwareWidgetState extends State<OfflineAwareWidget> {
  bool _isOnline = true;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();

    Connectivity().onConnectivityChanged.listen((result) {
      setState(() {
        _isOnline = result != ConnectivityResult.none;
      });

      if (_isOnline) {
        // Trigger sync when coming back online
        context.read<SyncManager>().syncData();
      }
    });
  }

  Future<void> _checkConnectivity() async {
    final result = await Connectivity().checkConnectivity();
    setState(() {
      _isOnline = result != ConnectivityResult.none;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (!_isOnline)
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8),
            color: Colors.orange,
            child: Text(
              'You\'re offline. Some features may be limited.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white),
            ),
          ),
        Expanded(
          child: _isOnline
            ? widget.child
            : (widget.offlineChild ?? widget.child),
        ),
      ],
    );
  }
}
```

## 📊 Analytics & Tracking

### User Behavior Tracking

```http
POST /analytics/events
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "events": [
    {
      "event_type": "experience_viewed",
      "timestamp": "2025-02-01T10:00:00Z",
      "properties": {
        "experience_id": "exp_123",
        "category": "cultural_tour",
        "source": "search_results",
        "position": 1
      }
    },
    {
      "event_type": "booking_started",
      "timestamp": "2025-02-01T10:05:00Z",
      "properties": {
        "experience_id": "exp_123",
        "participant_count": 2,
        "selected_date": "2025-02-15"
      }
    }
  ],
  "session_id": "session_abc123",
  "device_info": {
    "platform": "ios",
    "app_version": "1.0.0",
    "device_model": "iPhone 14",
    "os_version": "17.0"
  }
}
```

### Performance Metrics

```http
POST /analytics/performance
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "metrics": [
    {
      "metric_type": "api_response_time",
      "endpoint": "/experiences/nearby",
      "response_time_ms": 245,
      "timestamp": "2025-02-01T10:00:00Z"
    },
    {
      "metric_type": "app_startup_time",
      "startup_time_ms": 1200,
      "cold_start": true,
      "timestamp": "2025-02-01T09:58:00Z"
    }
  ]
}
```

## 🔗 Integration Examples

### Complete Flutter Booking Flow

```dart
// booking_flow_screen.dart
class BookingFlowScreen extends StatefulWidget {
  final Experience experience;

  const BookingFlowScreen({Key? key, required this.experience}) : super(key: key);

  @override
  _BookingFlowScreenState createState() => _BookingFlowScreenState();
}

class _BookingFlowScreenState extends State<BookingFlowScreen> {
  final _bookingService = GetIt.instance<BookingService>();
  final _paymentService = GetIt.instance<PaymentService>();

  BookingRequest? _bookingRequest;
  Booking? _booking;
  Payment? _payment;

  bool _isLoading = false;
  String _currentStep = 'details'; // details, review, payment, confirmation

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Book Experience'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: _buildCurrentStep(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case 'details':
        return BookingDetailsStep(
          experience: widget.experience,
          onCompleted: (request) {
            setState(() {
              _bookingRequest = request;
              _currentStep = 'review';
            });
          },
        );
      case 'review':
        return BookingReviewStep(
          experience: widget.experience,
          request: _bookingRequest!,
          onConfirmed: _createBooking,
        );
      case 'payment':
        return PaymentStep(
          booking: _booking!,
          onPaymentCompleted: _handlePaymentCompleted,
        );
      case 'confirmation':
        return BookingConfirmationStep(
          booking: _booking!,
          payment: _payment!,
        );
      default:
        return Container();
    }
  }

  Future<void> _createBooking() async {
    setState(() => _isLoading = true);

    try {
      final booking = await _bookingService.createBooking(_bookingRequest!);

      setState(() {
        _booking = booking;
        _currentStep = 'payment';
      });

    } on ApiException catch (e) {
      _showErrorDialog(e.message);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handlePaymentCompleted(Payment payment) async {
    setState(() {
      _payment = payment;
      _currentStep = 'confirmation';
    });

    // Send confirmation analytics
    AnalyticsService.track('booking_completed', {
      'booking_id': _booking!.id,
      'experience_id': widget.experience.id,
      'amount': payment.amount,
      'currency': payment.currency,
    });
  }
}
```

## 📚 Additional Resources

### Postman Collection
- **Collection URL**: `https://api.cultureconnect.ng/docs/postman/mobile-api.json`
- **Environment**: Use the provided environment variables for different deployment targets

### SDK Downloads
- **Flutter SDK**: `https://pub.dev/packages/culture_connect_sdk`
- **React Native SDK**: `https://www.npmjs.com/package/@culture-connect/react-native-sdk`

### Sample Apps
- **Flutter Sample**: `https://github.com/culture-connect/flutter-sample-app`
- **React Native Sample**: `https://github.com/culture-connect/react-native-sample-app`

### Support & Documentation
- **API Status**: `https://status.cultureconnect.ng`
- **Developer Portal**: `https://developers.cultureconnect.ng`
- **Support Email**: `<EMAIL>`
- **Slack Community**: `#culture-connect-mobile-dev`

### Changelog
- **v1.0.0** (2025-02-01): Initial mobile API release
- **Breaking Changes**: None (initial release)
- **New Features**: Complete mobile API with booking, payments, and real-time updates

const BASE_URL = 'https://api.cultureconnect.ng/api/v1';

class AuthService {
  constructor() {
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor to add token
    axios.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          const refreshed = await this.refreshToken();
          if (refreshed) {
            const token = await AsyncStorage.getItem('access_token');
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axios(originalRequest);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  async login(email, password) {
    try {
      const response = await axios.post(`${BASE_URL}/auth/login`, {
        email,
        password,
      });

      const { access_token, refresh_token, user } = response.data;
      
      await AsyncStorage.multiSet([
        ['access_token', access_token],
        ['refresh_token', refresh_token],
        ['user', JSON.stringify(user)],
      ]);

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      if (!refreshToken) return false;

      const response = await axios.post(`${BASE_URL}/auth/refresh`, {
        refresh_token: refreshToken,
      });

      const { access_token, refresh_token: newRefreshToken } = response.data;
      
      await AsyncStorage.multiSet([
        ['access_token', access_token],
        ['refresh_token', newRefreshToken],
      ]);

      return true;
    } catch (error) {
      await this.logout();
      return false;
    }
  }

  async logout() {
    await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
  }

  handleError(error) {
    if (error.response) {
      return new Error(error.response.data.detail || 'Authentication failed');
    } else if (error.request) {
      return new Error('Network error. Please check your connection.');
    } else {
      return new Error('An unexpected error occurred.');
    }
  }
}

export default new AuthService();
```

## 🗺️ Cultural Discovery Endpoints

### Get Nearby Experiences
```http
GET /experiences/nearby?lat={latitude}&lng={longitude}&radius={radius_km}&category={category}&limit={limit}
Authorization: Bearer {access_token}
```

**Parameters:**
- `lat` (required): Latitude coordinate
- `lng` (required): Longitude coordinate  
- `radius` (optional): Search radius in kilometers (default: 10, max: 50)
- `category` (optional): Experience category filter
- `limit` (optional): Number of results (default: 20, max: 100)

**Response (200 OK):**
```json
{
  "experiences": [
    {
      "id": "exp_123",
      "title": "Traditional Yoruba Cultural Tour",
      "description": "Immerse yourself in authentic Yoruba traditions...",
      "category": "cultural_tour",
      "vendor": {
        "id": "vendor_456",
        "name": "Lagos Cultural Heritage",
        "rating": 4.8,
        "verified": true
      },
      "location": {
        "latitude": 6.5244,
        "longitude": 3.3792,
        "address": "Lagos Island, Lagos, Nigeria",
        "distance_km": 2.3
      },
      "pricing": {
        "base_price": 15000,
        "currency": "NGN",
        "price_per_person": true,
        "group_discounts": [
          {"min_size": 5, "discount_percent": 10},
          {"min_size": 10, "discount_percent": 20}
        ]
      },
      "availability": {
        "available_dates": ["2025-02-05", "2025-02-06", "2025-02-07"],
        "time_slots": ["09:00", "14:00"],
        "duration_hours": 3,
        "max_participants": 15
      },
      "media": {
        "featured_image": "https://cdn.cultureconnect.ng/experiences/exp_123/featured.jpg",
        "gallery": [
          "https://cdn.cultureconnect.ng/experiences/exp_123/gallery1.jpg",
          "https://cdn.cultureconnect.ng/experiences/exp_123/gallery2.jpg"
        ],
        "video_url": "https://cdn.cultureconnect.ng/experiences/exp_123/preview.mp4"
      },
      "features": ["guide_included", "transportation", "refreshments"],
      "rating": 4.7,
      "review_count": 234,
      "tags": ["traditional", "cultural", "educational", "family_friendly"],
      "created_at": "2025-01-15T10:00:00Z",
      "updated_at": "2025-02-01T08:30:00Z"
    }
  ],
  "pagination": {
    "total": 45,
    "page": 1,
    "per_page": 20,
    "total_pages": 3
  },
  "filters": {
    "categories": ["cultural_tour", "food_experience", "art_workshop", "music_event"],
    "price_ranges": [
      {"min": 0, "max": 10000, "count": 12},
      {"min": 10000, "max": 25000, "count": 23},
      {"min": 25000, "max": 50000, "count": 8},
      {"min": 50000, "max": null, "count": 2}
    ]
  }
}
```
