"""
Enhanced Schemas package for Culture Connect Backend API.

This package contains comprehensive Pydantic schemas implementing Task 1.3.3 requirements:
- Advanced validation patterns with custom validators
- Serialization optimization for performance
- API versioning support with backward compatibility
- Structured error handling and response formatting
- Integration with repository layer and service architecture

Schema Categories:
- Authentication and user management schemas
- Vendor management schemas
- Base schema framework with validation utilities
- API versioning and compatibility support
- Structured error response schemas
"""

# Base schema framework (Task 1.3.3)
from .base import (
    BaseSchema, TimestampMixin, UUIDMixin, PaginationMixin,
    SearchMixin, MetadataMixin, ValidationError,
    PhoneNumber, CountryCode, CurrencyCode, LanguageCode,
    PositiveInt, NonNegativeInt, PositiveFloat, NonNegativeFloat,
    Percentage, NonEmptyStr, ShortStr, MediumStr, LongStr
)

# API versioning support (Task 1.3.3)
from .versioning import (
    APIVersion, VersionInfo, VersionRegistry, VersionedSchema,
    VersionCompatibilityMixin, VersionNegotiator, VersionedResponse
)

# Structured error handling (Task 1.3.3)
from .errors import (
    ErrorCategory, ErrorSeverity, ErrorCode, ErrorDetail, ErrorResponse,
    ValidationErrorResponse, BusinessLogicErrorResponse, ErrorResponseFactory,
    StructuredHTTPException
)

# Enhanced authentication schemas (Task 1.3.3)
from .auth import (
    UserBase, UserLogin, UserCreate, UserResponse, UserUpdate,
    TokenResponse, TokenRefresh, PasswordReset, PasswordResetRequest,
    PasswordChange, EmailVerification, LoginResponse, LogoutRequest,
    UserPermissions, AuthStatus, APIKeyCreate, APIKeyResponse,
    SessionInfo, SecurityEvent, MessageResponse
)

# Password security schemas (Task 2.1.2)
from .password_security import (
    PasswordStrengthLevel, PasswordRequirements, PasswordValidationResult,
    PasswordStrengthCheck, PasswordResetRequest as PasswordResetRequestV2,
    PasswordResetConfirm, PasswordChangeRequest, AccountLockoutStatus,
    PasswordHistoryEntry, PasswordSecurityResponse
)

# OAuth schemas (Task 2.1.3)
from .oauth_schemas import (
    OAuthProviderName, OAuthTokenType, OAuthProviderBase, OAuthProviderCreate,
    OAuthProviderUpdate, OAuthProviderResponse, OAuthAccountBase, OAuthAccountCreate,
    OAuthAccountUpdate, OAuthAccountResponse, OAuthTokenBase, OAuthTokenCreate,
    OAuthTokenResponse, OAuthStateCreate, OAuthStateResponse,
    OAuthAuthorizationRequest, OAuthAuthorizationResponse, OAuthCallbackRequest,
    OAuthCallbackResponse, OAuthUserProfile, OAuthTokenRefreshRequest,
    OAuthTokenRefreshResponse, OAuthAccountLinkRequest, OAuthAccountUnlinkRequest,
    OAuthErrorResponse
)

# User profile management schemas (Task 2.2.2)
from .user_profile import (
    ProfileVisibility, NotificationFrequency, LanguageCode, TimezoneCode,
    UserProfileUpdate, UserPreferences, UserPreferencesUpdate,
    ProfileImageUpload, ProfileImageResponse, AccountSettings, AccountSettingsUpdate
)

# RBAC management schemas (Task 2.2.3)
from .rbac_schemas import (
    AccessDecision, ResourceType, PermissionCheck, PermissionCheckResponse,
    RolePermissions, PermissionGrantCreate, PermissionGrantResponse, PermissionGrantUpdate,
    AccessControlLogResponse, RoleHierarchyResponse, UserPermissionSummary, AccessControlStats
)

# Email service schemas (Task 2.3.1)
from .email_schemas import (
    EmailTemplateBase, EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailTemplateListResponse, EmailSendRequest, EmailSendResponse, EmailBatchSendRequest,
    EmailBatchSendResponse, EmailDeliveryBase, EmailDeliveryResponse, EmailDeliveryListResponse,
    EmailDeliveryStatusUpdate, EmailPreferenceBase, EmailPreferenceUpdate, EmailPreferenceResponse,
    EmailOptOutRequest, EmailOptOutResponse, EmailQueueResponse, EmailQueueListResponse,
    EmailAnalyticsResponse, EmailVerificationRequest, EmailVerificationResponse,
    PasswordResetEmailRequest, PasswordResetEmailResponse
)

# Push notification schemas (Task 2.3.2)
from .push_notification_schemas import (
    DeviceTokenBase, DeviceTokenCreate, DeviceTokenUpdate, DeviceTokenResponse, DeviceTokenListResponse,
    NotificationTemplateBase, NotificationTemplateCreate, NotificationTemplateUpdate,
    NotificationTemplateResponse, NotificationTemplateListResponse,
    NotificationSendRequest, NotificationSendResponse, NotificationBatchSendRequest, NotificationBatchSendResponse,
    NotificationDeliveryResponse, NotificationDeliveryListResponse, NotificationDeliveryStatusUpdate,
    NotificationPreferenceBase, NotificationPreferenceUpdate, NotificationPreferenceResponse,
    FCMMessageRequest, FCMMessageResponse, FCMBatchRequest, FCMBatchResponse
)

# Vendor schemas (implemented in Phase 1)
from .vendor import (
    VendorCreate,
    VendorUpdate,
    VendorResponse,
    VendorProfileCreate,
    VendorProfileUpdate,
    VendorProfileResponse,
    VendorDocumentCreate,
    VendorDocumentUpdate,
    VendorDocumentResponse,
    VendorRegistrationRequest,
    VendorRegistrationResponse,
    OnboardingStepUpdate,
    OnboardingStatusResponse,
)

# Service schemas (Task 3.2.1)
from .service import (
    ServiceCategoryBase, ServiceCategoryCreate, ServiceCategoryUpdate, ServiceCategoryResponse,
    ServiceImageBase, ServiceImageCreate, ServiceImageUpdate, ServiceImageResponse,
    ServicePricingBase, ServicePricingCreate, ServicePricingUpdate, ServicePricingResponse
)

from .service_extended import (
    ServiceAvailabilityBase, ServiceAvailabilityCreate, ServiceAvailabilityUpdate, ServiceAvailabilityResponse,
    ServiceBase, ServiceCreate, ServiceUpdate, ServiceResponse,
    ServiceListResponse, ServiceSearchRequest,
    ServiceBulkUpdateRequest, ServiceBulkStatusUpdateRequest, ServiceBulkDeleteRequest
)

# Booking schemas (Task 4.1.1)
from .booking_schemas import (
    BookingCreateSchema, BookingUpdateSchema, BookingStatusUpdateSchema,
    VendorResponseSchema, BookingResponseSchema, BookingListResponseSchema
)

# Booking communication schemas (Task 4.1.3)
from .booking_communication_schemas import (
    # Message management schemas
    BookingMessageBase, BookingMessageCreate, BookingMessageUpdate, BookingMessageResponse,
    MessageThreadResponse, BookingMessageListResponse,

    # File attachment schemas
    MessageAttachmentBase, MessageAttachmentCreate, MessageAttachmentResponse,

    # Template management schemas
    MessageTemplateBase, MessageTemplateCreate, MessageTemplateUpdate, MessageTemplateResponse,
    MessageTemplateListResponse,

    # WebSocket event schemas
    MessageEventSchema, DeliveryStatusEventSchema,

    # Analytics schemas
    MessageAnalyticsResponse, ConversationSummaryResponse
)

# Availability schemas (Task 4.1.2)
from .availability_schemas import (
    VendorAvailabilityCreateSchema, VendorAvailabilityUpdateSchema, VendorAvailabilityResponseSchema,
    RecurringAvailabilityCreateSchema, RecurringAvailabilityUpdateSchema, RecurringAvailabilityResponseSchema,
    AvailabilitySlotCreateSchema, AvailabilitySlotUpdateSchema, AvailabilitySlotResponseSchema,
    AvailabilityExceptionCreateSchema, AvailabilityExceptionUpdateSchema, AvailabilityExceptionResponseSchema,
    BulkSlotCreateSchema, BulkSlotResponseSchema, AvailabilityCheckResponseSchema,
    AvailabilitySlotListResponseSchema
)

# Payment schemas (Task 4.3.1)
from .payment_schemas import (
    PaymentCreate, PaymentUpdate, PaymentResponse, PaymentListResponse,
    PaymentMethodCreate, PaymentMethodUpdate, PaymentMethodResponse, PaymentMethodListResponse,
    PaystackInitializeRequest, PaystackInitializeResponse, PaystackVerifyResponse,
    PaystackWebhookEvent, WebhookProcessingResult,
    PaymentProcessRequest, PaymentProcessResponse
)

# A/B Testing schemas (implemented in Phase 2.3 - A/B Testing Framework)
from .ab_testing import (
    ABTestCreate, ABTestUpdate, ABTestResponse, ABTestAssignmentResponse,
    ABTestResultCreate, ABTestResultResponse, ABTestAnalysisResponse,
    ABTestDashboardResponse, ABTestFilterRequest, StatisticalAnalysisSchema,
    PerformanceComparisonSchema, ABTestSummarySchema
)

# Review schemas (Task 4.4.1 Phase 2)
from .review_schemas import (
    ReviewCreateSchema, ReviewUpdateSchema, ReviewResponseSchema,
    ReviewResponseCreateSchema, ReviewResponseUpdateSchema, VendorResponseSchema,
    ReviewModerationCreateSchema, ReviewModerationUpdateSchema, ReviewModerationSchema,
    ReviewAnalyticsSchema, ReviewListResponseSchema, VendorResponseListSchema,
    ReviewFilterSchema, ReviewSummarySchema
)

# Analytics schemas (Phase 7.1 - Analytics Dashboard System)
from .analytics_schemas import (
    UserAnalyticsCreate, UserAnalyticsUpdate, UserAnalyticsResponse,
    VendorAnalyticsCreate, VendorAnalyticsUpdate, VendorAnalyticsResponse,
    BookingAnalyticsCreate, BookingAnalyticsUpdate, BookingAnalyticsResponse,
    SystemMetricsCreate, SystemMetricsUpdate, SystemMetricsResponse,
    DashboardWidgetCreate, DashboardWidgetUpdate, DashboardWidgetResponse,
    KPIDefinitionCreate, KPIDefinitionUpdate, KPIDefinitionResponse,
    AnalyticsQueryParams, DashboardDataResponse
)

# TODO: Import other schemas as they are implemented
# from .user import UserCreate, UserUpdate, UserResponse
# from .promotion import CampaignCreate, CampaignUpdate, CampaignResponse

__all__ = [
    # Base schema framework (Task 1.3.3)
    "BaseSchema", "TimestampMixin", "UUIDMixin", "PaginationMixin",
    "SearchMixin", "MetadataMixin", "ValidationError",
    "PhoneNumber", "CountryCode", "CurrencyCode", "LanguageCode",
    "PositiveInt", "NonNegativeInt", "PositiveFloat", "NonNegativeFloat",
    "Percentage", "NonEmptyStr", "ShortStr", "MediumStr", "LongStr",

    # API versioning support (Task 1.3.3)
    "APIVersion", "VersionInfo", "VersionRegistry", "VersionedSchema",
    "VersionCompatibilityMixin", "VersionNegotiator", "VersionedResponse",

    # Structured error handling (Task 1.3.3)
    "ErrorCategory", "ErrorSeverity", "ErrorCode", "ErrorDetail", "ErrorResponse",
    "ValidationErrorResponse", "BusinessLogicErrorResponse", "ErrorResponseFactory",
    "StructuredHTTPException",

    # Enhanced authentication schemas (Task 1.3.3)
    "UserBase", "UserLogin", "UserCreate", "UserResponse", "UserUpdate",
    "TokenResponse", "TokenRefresh", "PasswordReset", "PasswordResetRequest",
    "PasswordChange", "EmailVerification", "LoginResponse", "LogoutRequest",
    "UserPermissions", "AuthStatus", "APIKeyCreate", "APIKeyResponse",
    "SessionInfo", "SecurityEvent", "MessageResponse",

    # Password security schemas (Task 2.1.2)
    "PasswordStrengthLevel", "PasswordRequirements", "PasswordValidationResult",
    "PasswordStrengthCheck", "PasswordResetRequestV2", "PasswordResetConfirm",
    "PasswordChangeRequest", "AccountLockoutStatus", "PasswordHistoryEntry",
    "PasswordSecurityResponse",

    # Email service schemas (Task 2.3.1)
    "EmailTemplateBase", "EmailTemplateCreate", "EmailTemplateUpdate", "EmailTemplateResponse",
    "EmailTemplateListResponse", "EmailSendRequest", "EmailSendResponse", "EmailBatchSendRequest",
    "EmailBatchSendResponse", "EmailDeliveryBase", "EmailDeliveryResponse", "EmailDeliveryListResponse",
    "EmailDeliveryStatusUpdate", "EmailPreferenceBase", "EmailPreferenceUpdate", "EmailPreferenceResponse",
    "EmailOptOutRequest", "EmailOptOutResponse", "EmailQueueResponse", "EmailQueueListResponse",
    "EmailAnalyticsResponse", "EmailVerificationRequest", "EmailVerificationResponse",
    "PasswordResetEmailRequest", "PasswordResetEmailResponse",

    # Push notification schemas (Task 2.3.2)
    "DeviceTokenBase", "DeviceTokenCreate", "DeviceTokenUpdate", "DeviceTokenResponse", "DeviceTokenListResponse",
    "NotificationTemplateBase", "NotificationTemplateCreate", "NotificationTemplateUpdate",
    "NotificationTemplateResponse", "NotificationTemplateListResponse",
    "NotificationSendRequest", "NotificationSendResponse", "NotificationBatchSendRequest", "NotificationBatchSendResponse",
    "NotificationDeliveryResponse", "NotificationDeliveryListResponse", "NotificationDeliveryStatusUpdate",
    "NotificationPreferenceBase", "NotificationPreferenceUpdate", "NotificationPreferenceResponse",
    "FCMMessageRequest", "FCMMessageResponse", "FCMBatchRequest", "FCMBatchResponse",

    # Vendor schemas (implemented in Phase 1)
    "VendorCreate", "VendorUpdate", "VendorResponse",
    "VendorProfileCreate", "VendorProfileUpdate", "VendorProfileResponse",
    "VendorDocumentCreate", "VendorDocumentUpdate", "VendorDocumentResponse",

    # Service schemas (Task 3.2.1)
    "ServiceCategoryBase", "ServiceCategoryCreate", "ServiceCategoryUpdate", "ServiceCategoryResponse",
    "ServiceImageBase", "ServiceImageCreate", "ServiceImageUpdate", "ServiceImageResponse",
    "ServicePricingBase", "ServicePricingCreate", "ServicePricingUpdate", "ServicePricingResponse",
    "ServiceAvailabilityBase", "ServiceAvailabilityCreate", "ServiceAvailabilityUpdate", "ServiceAvailabilityResponse",
    "ServiceBase", "ServiceCreate", "ServiceUpdate", "ServiceResponse",
    "ServiceListResponse", "ServiceSearchRequest",
    "ServiceBulkUpdateRequest", "ServiceBulkStatusUpdateRequest", "ServiceBulkDeleteRequest",

    # Booking schemas (Task 4.1.1)
    "BookingCreateSchema", "BookingUpdateSchema", "BookingStatusUpdateSchema",
    "VendorResponseSchema", "BookingResponseSchema", "BookingListResponseSchema",

    # Booking communication schemas (Task 4.1.3)
    "BookingMessageBase", "BookingMessageCreate", "BookingMessageUpdate", "BookingMessageResponse",
    "MessageThreadResponse", "BookingMessageListResponse",
    "MessageAttachmentBase", "MessageAttachmentCreate", "MessageAttachmentResponse",
    "MessageTemplateBase", "MessageTemplateCreate", "MessageTemplateUpdate", "MessageTemplateResponse",
    "MessageTemplateListResponse", "MessageEventSchema", "DeliveryStatusEventSchema",
    "MessageAnalyticsResponse", "ConversationSummaryResponse",

    # Availability schemas (Task 4.1.2)
    "VendorAvailabilityCreateSchema", "VendorAvailabilityUpdateSchema", "VendorAvailabilityResponseSchema",
    "RecurringAvailabilityCreateSchema", "RecurringAvailabilityUpdateSchema", "RecurringAvailabilityResponseSchema",
    "AvailabilitySlotCreateSchema", "AvailabilitySlotUpdateSchema", "AvailabilitySlotResponseSchema",
    "AvailabilityExceptionCreateSchema", "AvailabilityExceptionUpdateSchema", "AvailabilityExceptionResponseSchema",
    "BulkSlotCreateSchema", "BulkSlotResponseSchema", "AvailabilityCheckResponseSchema",
    "AvailabilitySlotListResponseSchema",

    # Payment schemas (Task 4.3.1)
    "PaymentCreate", "PaymentUpdate", "PaymentResponse", "PaymentListResponse",
    "PaymentMethodCreate", "PaymentMethodUpdate", "PaymentMethodResponse", "PaymentMethodListResponse",
    "PaystackInitializeRequest", "PaystackInitializeResponse", "PaystackVerifyResponse",
    "PaystackWebhookEvent", "WebhookProcessingResult",
    "PaymentProcessRequest", "PaymentProcessResponse",

    # A/B Testing schemas (implemented in Phase 2.3 - A/B Testing Framework)
    "ABTestCreate", "ABTestUpdate", "ABTestResponse", "ABTestAssignmentResponse",
    "ABTestResultCreate", "ABTestResultResponse", "ABTestAnalysisResponse",
    "ABTestDashboardResponse", "ABTestFilterRequest", "StatisticalAnalysisSchema",
    "PerformanceComparisonSchema", "ABTestSummarySchema",

    # Review schemas (Task 4.4.1 Phase 2)
    "ReviewCreateSchema", "ReviewUpdateSchema", "ReviewResponseSchema",
    "ReviewResponseCreateSchema", "ReviewResponseUpdateSchema", "VendorResponseSchema",
    "ReviewModerationCreateSchema", "ReviewModerationUpdateSchema", "ReviewModerationSchema",
    "ReviewAnalyticsSchema", "ReviewListResponseSchema", "VendorResponseListSchema",
    "ReviewFilterSchema", "ReviewSummarySchema",

    # Analytics schemas (Phase 7.1 - Analytics Dashboard System)
    "UserAnalyticsCreate", "UserAnalyticsUpdate", "UserAnalyticsResponse",
    "VendorAnalyticsCreate", "VendorAnalyticsUpdate", "VendorAnalyticsResponse",
    "BookingAnalyticsCreate", "BookingAnalyticsUpdate", "BookingAnalyticsResponse",
    "SystemMetricsCreate", "SystemMetricsUpdate", "SystemMetricsResponse",
    "DashboardWidgetCreate", "DashboardWidgetUpdate", "DashboardWidgetResponse",
    "KPIDefinitionCreate", "KPIDefinitionUpdate", "KPIDefinitionResponse",
    "AnalyticsQueryParams", "DashboardDataResponse",
]