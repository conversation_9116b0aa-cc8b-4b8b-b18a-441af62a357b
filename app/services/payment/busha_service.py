"""
Busha Cryptocurrency Payment Service for Culture Connect Backend API.

This module provides comprehensive Busha.co integration for Nigerian and Global cryptocurrency payments including:
- Bitcoin, Ethereum, and USDT payment processing
- Cryptocurrency wallet management and address generation
- Real-time exchange rate conversion (NGN ↔ Crypto)
- Webhook signature validation and event processing
- Multi-currency crypto support with automatic conversion

Implements Task 4.3.3 Phase 1 requirements for Busha API integration with
production-grade functionality and seamless integration with existing payment infrastructure.
"""

import logging
import hashlib
import hmac
import json
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any, Optional, List
from uuid import uuid4

import httpx

from app.core.payment.config import PaymentProviderSettings, PaymentProviderType
from app.models.payment import PaymentStatus
from app.schemas.payment_schemas import WebhookProcessingResult
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)


class BushaService:
    """
    Busha cryptocurrency payment service for Nigerian and Global markets.

    Provides comprehensive Busha.co operations including:
    - Bitcoin, Ethereum, and USDT payment processing
    - Cryptocurrency wallet address generation
    - Real-time exchange rate conversion (NGN ↔ Crypto)
    - Payment confirmation and blockchain verification
    - Webhook signature validation and event processing
    - Multi-currency crypto support with automatic conversion
    - Performance monitoring and metrics collection
    """

    def __init__(self):
        """Initialize Busha service with configuration."""
        self.config = PaymentProviderSettings.get_provider_config(PaymentProviderType.BUSHA)

        # TODO-BUSHA-API-KEY: Replace with actual Busha API key from environment
        self.api_key = self.config.get("api_key", "busha_test_api_key_placeholder")
        self.secret_key = self.config.get("secret_key", "busha_test_secret_placeholder")

        # TODO-BUSHA-API-URL: Replace with actual Busha API base URL
        self.base_url = self.config.get("base_url", "https://api.busha.co/v1")

        # TODO-BUSHA-WEBHOOK-SECRET: Replace with actual webhook secret
        self.webhook_secret = self.config.get("webhook_secret", "busha_webhook_secret_placeholder")

        # Supported cryptocurrencies
        self.supported_cryptos = ["BTC", "ETH", "USDT", "USDC"]

        # HTTP client for API requests
        self.http_client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "CultureConnect/1.0"
            }
        )

        self.logger = logging.getLogger(f"{__name__}.BushaService")

    async def get_exchange_rates(self, base_currency: str = "NGN") -> Dict[str, Any]:
        """
        Get current cryptocurrency exchange rates.

        Args:
            base_currency: Base currency for conversion (default: NGN)

        Returns:
            Dict containing exchange rates for supported cryptocurrencies

        Raises:
            BushaError: If exchange rate retrieval fails
        """
        correlation = correlation_id.get()

        try:
            self.logger.info(
                f"Fetching Busha exchange rates",
                extra={
                    "correlation_id": correlation,
                    "base_currency": base_currency
                }
            )

            # TODO-BUSHA-API-URL: Replace with actual Busha exchange rates endpoint
            response = await self.http_client.get(
                f"{self.base_url}/exchange-rates",
                params={"base": base_currency}
            )
            response.raise_for_status()

            rates_data = response.json()

            # Track metrics
            metrics_collector.record_business_event(
                f"busha_exchange_rates_fetched_{base_currency}_success"
            )

            self.logger.info(
                f"Busha exchange rates fetched successfully",
                extra={
                    "correlation_id": correlation,
                    "base_currency": base_currency,
                    "rates_count": len(rates_data.get("data", {}))
                }
            )

            return {
                "base_currency": base_currency,
                "rates": rates_data.get("data", {}),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "provider": "busha"
            }

        except httpx.HTTPStatusError as e:
            self.logger.error(
                f"Busha API error during exchange rates fetch",
                extra={
                    "correlation_id": correlation,
                    "status_code": e.response.status_code,
                    "response": e.response.text
                }
            )

            metrics_collector.record_business_event(
                f"busha_exchange_rates_fetched_{base_currency}_error"
            )

            raise BushaError(f"Exchange rates fetch failed: {e.response.text}")

        except Exception as e:
            self.logger.error(
                f"Unexpected error during Busha exchange rates fetch",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                f"busha_exchange_rates_fetched_{base_currency}_error"
            )

            raise BushaError(f"Exchange rates fetch failed: {str(e)}")

    async def create_crypto_payment(
        self,
        amount: Decimal,
        currency: str,
        crypto_currency: str,
        customer_email: str,
        reference: str,
        metadata: Optional[Dict[str, Any]] = None,
        callback_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a cryptocurrency payment request.

        Args:
            amount: Payment amount in fiat currency (NGN)
            currency: Fiat currency (NGN)
            crypto_currency: Cryptocurrency type (BTC, ETH, USDT, USDC)
            customer_email: Customer email address
            reference: Unique payment reference
            metadata: Additional payment metadata
            callback_url: Callback URL for payment completion

        Returns:
            Dict containing payment details and crypto wallet address

        Raises:
            BushaError: If payment creation fails
        """
        correlation = correlation_id.get()

        try:
            # Validate cryptocurrency
            if crypto_currency not in self.supported_cryptos:
                raise BushaError(f"Unsupported cryptocurrency: {crypto_currency}")

            self.logger.info(
                f"Creating Busha crypto payment",
                extra={
                    "correlation_id": correlation,
                    "amount": float(amount),
                    "currency": currency,
                    "crypto_currency": crypto_currency,
                    "customer_email": customer_email,
                    "reference": reference
                }
            )

            # Prepare payment request
            payment_data = {
                "amount": float(amount),
                "currency": currency,
                "crypto_currency": crypto_currency,
                "customer_email": customer_email,
                "reference": reference,
                "metadata": metadata or {},
                "callback_url": callback_url
            }

            # TODO-BUSHA-API-URL: Replace with actual Busha payment creation endpoint
            response = await self.http_client.post(
                f"{self.base_url}/payments/crypto",
                json=payment_data
            )
            response.raise_for_status()

            response_data = response.json()

            # Track metrics
            metrics_collector.record_business_event(
                f"busha_crypto_payment_created_{crypto_currency}_success"
            )

            self.logger.info(
                f"Busha crypto payment created successfully",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "crypto_currency": crypto_currency,
                    "wallet_address": response_data.get("data", {}).get("wallet_address", "")[:10] + "..."
                }
            )

            return {
                "payment_id": response_data.get("data", {}).get("id"),
                "reference": reference,
                "amount": amount,
                "currency": currency,
                "crypto_currency": crypto_currency,
                "crypto_amount": response_data.get("data", {}).get("crypto_amount"),
                "wallet_address": response_data.get("data", {}).get("wallet_address"),
                "qr_code": response_data.get("data", {}).get("qr_code"),
                "expires_at": response_data.get("data", {}).get("expires_at"),
                "status": "pending",
                "blockchain_network": response_data.get("data", {}).get("network", "mainnet")
            }

        except httpx.HTTPStatusError as e:
            self.logger.error(
                f"Busha API error during crypto payment creation",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "status_code": e.response.status_code,
                    "response": e.response.text
                }
            )

            metrics_collector.record_business_event(
                f"busha_crypto_payment_created_{crypto_currency}_error"
            )

            raise BushaError(f"Crypto payment creation failed: {e.response.text}")

        except Exception as e:
            self.logger.error(
                f"Unexpected error during Busha crypto payment creation",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                f"busha_crypto_payment_created_{crypto_currency}_error"
            )

            raise BushaError(f"Crypto payment creation failed: {str(e)}")

    async def verify_crypto_payment(self, reference: str) -> Dict[str, Any]:
        """
        Verify a cryptocurrency payment status.

        Args:
            reference: Payment reference to verify

        Returns:
            Dict containing payment verification details

        Raises:
            BushaError: If verification fails
        """
        correlation = correlation_id.get()

        try:
            self.logger.info(
                f"Verifying Busha crypto payment",
                extra={
                    "correlation_id": correlation,
                    "reference": reference
                }
            )

            # TODO-BUSHA-API-URL: Replace with actual Busha payment verification endpoint
            response = await self.http_client.get(
                f"{self.base_url}/payments/verify/{reference}"
            )
            response.raise_for_status()

            response_data = response.json()
            payment_data = response_data.get("data", {})

            # Track metrics
            metrics_collector.record_business_event(
                f"busha_crypto_payment_verified_success_{payment_data.get('status', 'unknown')}"
            )

            self.logger.info(
                f"Busha crypto payment verified successfully",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "status": payment_data.get("status"),
                    "confirmations": payment_data.get("confirmations", 0)
                }
            )

            return {
                "reference": reference,
                "status": payment_data.get("status"),
                "amount": payment_data.get("amount"),
                "currency": payment_data.get("currency"),
                "crypto_currency": payment_data.get("crypto_currency"),
                "crypto_amount": payment_data.get("crypto_amount"),
                "transaction_hash": payment_data.get("transaction_hash"),
                "confirmations": payment_data.get("confirmations", 0),
                "required_confirmations": payment_data.get("required_confirmations", 3),
                "verified_at": payment_data.get("verified_at"),
                "blockchain_network": payment_data.get("network", "mainnet")
            }

        except httpx.HTTPStatusError as e:
            self.logger.error(
                f"Busha API error during crypto payment verification",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "status_code": e.response.status_code,
                    "response": e.response.text
                }
            )

            metrics_collector.record_business_event(
                "busha_crypto_payment_verified_error"
            )

            raise BushaError(f"Crypto payment verification failed: {e.response.text}")

        except Exception as e:
            self.logger.error(
                f"Unexpected error during Busha crypto payment verification",
                extra={
                    "correlation_id": correlation,
                    "reference": reference,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                "busha_crypto_payment_verified_error"
            )

            raise BushaError(f"Crypto payment verification failed: {str(e)}")

    def validate_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """
        Validate Busha webhook signature for security.

        Args:
            payload: Raw webhook payload
            signature: Webhook signature from headers

        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # TODO-BUSHA-WEBHOOK-SECRET: Replace with actual webhook secret validation
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()

            # Busha uses SHA256 HMAC signature
            return hmac.compare_digest(f"sha256={expected_signature}", signature)

        except Exception as e:
            self.logger.error(
                f"Error validating Busha webhook signature: {str(e)}"
            )
            return False

    async def process_webhook_event(
        self,
        payload: bytes,
        signature: str
    ) -> WebhookProcessingResult:
        """
        Process Busha webhook events.

        Args:
            payload: Raw webhook payload
            signature: Webhook signature

        Returns:
            WebhookProcessingResult with processing status

        Raises:
            BushaError: If webhook processing fails
        """
        correlation = correlation_id.get()
        event_id = str(uuid4())

        try:
            # Validate webhook signature
            if not self.validate_webhook_signature(payload, signature):
                self.logger.warning(
                    f"Invalid Busha webhook signature",
                    extra={
                        "correlation_id": correlation,
                        "event_id": event_id
                    }
                )
                raise BushaError("Invalid webhook signature")

            # Parse webhook event
            try:
                event_data = json.loads(payload.decode('utf-8'))
            except json.JSONDecodeError as e:
                raise BushaError(f"Invalid webhook payload: {str(e)}")

            self.logger.info(
                f"Processing Busha webhook event",
                extra={
                    "correlation_id": correlation,
                    "event_id": event_id,
                    "event_type": event_data.get("event"),
                    "reference": event_data.get("data", {}).get("reference")
                }
            )

            # Process based on event type
            payment_id = None
            message = f"Processed {event_data.get('event')} event"
            event_type = event_data.get("event", "unknown")

            if event_type == "payment.confirmed":
                payment_id = await self._handle_payment_confirmed(event_data.get("data", {}))
                message = "Crypto payment confirmed on blockchain"

            elif event_type == "payment.completed":
                payment_id = await self._handle_payment_completed(event_data.get("data", {}))
                message = "Crypto payment completed successfully"

            elif event_type == "payment.failed":
                payment_id = await self._handle_payment_failed(event_data.get("data", {}))
                message = "Crypto payment failed"

            elif event_type == "payment.expired":
                payment_id = await self._handle_payment_expired(event_data.get("data", {}))
                message = "Crypto payment expired"

            else:
                self.logger.warning(
                    f"Unhandled Busha webhook event type: {event_type}",
                    extra={
                        "correlation_id": correlation,
                        "event_id": event_id
                    }
                )
                message = f"Unhandled event type: {event_type}"

            # Track metrics
            metrics_collector.record_business_event(
                f"busha_webhook_processed_{event_type}_success"
            )

            return WebhookProcessingResult(
                event_id=event_id,
                event_type=event_type,
                processed=True,
                payment_id=payment_id,
                message=message,
                timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            self.logger.error(
                f"Error processing Busha webhook",
                extra={
                    "correlation_id": correlation,
                    "event_id": event_id,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                "busha_webhook_processed_unknown_error"
            )

            return WebhookProcessingResult(
                event_id=event_id,
                event_type="unknown",
                processed=False,
                payment_id=None,
                message=f"Processing failed: {str(e)}",
                timestamp=datetime.now(timezone.utc)
            )

    async def _handle_payment_confirmed(self, data: Dict[str, Any]) -> Optional[int]:
        """Handle confirmed crypto payment webhook."""
        # TODO: Implement payment status update logic
        # This will be integrated with PaymentService in Phase 3
        reference = data.get("reference")
        confirmations = data.get("confirmations", 0)
        self.logger.info(f"Crypto payment confirmed: {reference} ({confirmations} confirmations)")
        return None

    async def _handle_payment_completed(self, data: Dict[str, Any]) -> Optional[int]:
        """Handle completed crypto payment webhook."""
        # TODO: Implement payment status update logic
        reference = data.get("reference")
        transaction_hash = data.get("transaction_hash")
        self.logger.info(f"Crypto payment completed: {reference} (tx: {transaction_hash})")
        return None

    async def _handle_payment_failed(self, data: Dict[str, Any]) -> Optional[int]:
        """Handle failed crypto payment webhook."""
        # TODO: Implement payment status update logic
        reference = data.get("reference")
        reason = data.get("failure_reason", "Unknown")
        self.logger.info(f"Crypto payment failed: {reference} (reason: {reason})")
        return None

    async def _handle_payment_expired(self, data: Dict[str, Any]) -> Optional[int]:
        """Handle expired crypto payment webhook."""
        # TODO: Implement payment status update logic
        reference = data.get("reference")
        self.logger.info(f"Crypto payment expired: {reference}")
        return None

    async def get_supported_cryptocurrencies(self) -> List[Dict[str, Any]]:
        """
        Get list of supported cryptocurrencies with current rates.

        Returns:
            List of supported cryptocurrencies with metadata

        Raises:
            BushaError: If retrieval fails
        """
        correlation = correlation_id.get()

        try:
            self.logger.info(
                f"Fetching supported cryptocurrencies",
                extra={"correlation_id": correlation}
            )

            # TODO-BUSHA-API-URL: Replace with actual Busha cryptocurrencies endpoint
            response = await self.http_client.get(f"{self.base_url}/cryptocurrencies")
            response.raise_for_status()

            response_data = response.json()

            # Track metrics
            metrics_collector.record_business_event(
                "busha_cryptocurrencies_fetched_success"
            )

            return response_data.get("data", [])

        except Exception as e:
            self.logger.error(
                f"Error fetching supported cryptocurrencies",
                extra={
                    "correlation_id": correlation,
                    "error": str(e)
                }
            )

            metrics_collector.record_business_event(
                "busha_cryptocurrencies_fetched_error"
            )

            # Return default supported cryptocurrencies
            return [
                {"symbol": "BTC", "name": "Bitcoin", "network": "bitcoin"},
                {"symbol": "ETH", "name": "Ethereum", "network": "ethereum"},
                {"symbol": "USDT", "name": "Tether", "network": "ethereum"},
                {"symbol": "USDC", "name": "USD Coin", "network": "ethereum"}
            ]

    async def close(self):
        """Close HTTP client connections."""
        await self.http_client.aclose()


class BushaError(Exception):
    """Custom exception for Busha-related errors."""
    pass
