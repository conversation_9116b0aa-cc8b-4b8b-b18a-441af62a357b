"""
Performance tests for scaling services and endpoints.

This module provides comprehensive performance tests for Phase 7.3.3 scaling components:
- API endpoint performance validation (<200ms GET, <500ms POST/PUT)
- Repository query performance validation (<200ms with Redis caching)
- Auto-scaling response time validation (<2 minutes from trigger to execution)
- Container startup performance validation (<30 seconds)
- Load testing for concurrent operations and throughput validation

Implements performance benchmarking with detailed metrics and reporting.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone
from decimal import Decimal
from uuid import uuid4
from httpx import AsyncClient
from concurrent.futures import ThreadPoolExecutor
import statistics

from app.services.scaling_services import (
    ScalingMetricsService, AutoScalingService, LoadBalancerService,
    ContainerOrchestrationService
)
from app.schemas.scaling_schemas import (
    ScalingMetricsCreate, AutoScalingPolicyCreate, ContainerMetricsCreate
)


class TestScalingPerformance:
    """Performance tests for scaling services."""

    @pytest.mark.asyncio
    async def test_scaling_metrics_service_performance(self, scaling_metrics_service: ScalingMetricsService):
        """Test ScalingMetricsService performance targets."""
        # Test metric recording performance (<500ms creation)
        metric_data = ScalingMetricsCreate(
            metric_name="cpu_utilization",
            metric_type="resource",
            component="booking-service",
            current_value=Decimal("75.5"),
            cpu_utilization=Decimal("75.5"),
            memory_utilization=Decimal("60.2")
        )
        
        start_time = time.time()
        await scaling_metrics_service.record_metric(metric_data, "perf-test-correlation-id")
        end_time = time.time()
        
        creation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        assert creation_time < 500, f"Metric creation took {creation_time:.2f}ms, expected <500ms"
        
        # Test metrics retrieval performance (<200ms queries)
        start_time = time.time()
        await scaling_metrics_service.get_component_metrics(
            component="booking-service",
            limit=100,
            correlation_id="perf-test-correlation-id"
        )
        end_time = time.time()
        
        query_time = (end_time - start_time) * 1000
        assert query_time < 200, f"Metrics query took {query_time:.2f}ms, expected <200ms"
        
        # Test utilization summary performance (<200ms)
        start_time = time.time()
        await scaling_metrics_service.get_utilization_summary(
            correlation_id="perf-test-correlation-id"
        )
        end_time = time.time()
        
        summary_time = (end_time - start_time) * 1000
        assert summary_time < 200, f"Utilization summary took {summary_time:.2f}ms, expected <200ms"

    @pytest.mark.asyncio
    async def test_auto_scaling_service_performance(self, auto_scaling_service: AutoScalingService):
        """Test AutoScalingService performance targets."""
        # Test policy creation performance (<500ms)
        policy_data = AutoScalingPolicyCreate(
            name="perf-test-policy",
            component="booking-service",
            min_replicas=2,
            max_replicas=10,
            scale_up_threshold=Decimal("80.0"),
            scale_down_threshold=Decimal("30.0")
        )
        
        start_time = time.time()
        await auto_scaling_service.create_scaling_policy(policy_data, "perf-test-correlation-id")
        end_time = time.time()
        
        creation_time = (end_time - start_time) * 1000
        assert creation_time < 500, f"Policy creation took {creation_time:.2f}ms, expected <500ms"
        
        # Test policy evaluation performance (<200ms)
        start_time = time.time()
        await auto_scaling_service.evaluate_scaling_policies("perf-test-correlation-id")
        end_time = time.time()
        
        evaluation_time = (end_time - start_time) * 1000
        assert evaluation_time < 200, f"Policy evaluation took {evaluation_time:.2f}ms, expected <200ms"

    @pytest.mark.asyncio
    async def test_container_orchestration_performance(self, container_orchestration_service: ContainerOrchestrationService):
        """Test ContainerOrchestrationService performance targets."""
        # Test container metrics recording performance (<500ms)
        metrics_data = ContainerMetricsCreate(
            container_id="perf-test-container",
            pod_name="perf-test-pod",
            namespace="culture-connect",
            cpu_usage_cores=Decimal("0.5"),
            memory_usage_bytes=512000000,
            request_count=150,
            error_count=2
        )
        
        start_time = time.time()
        await container_orchestration_service.record_container_metrics(
            metrics_data, "perf-test-correlation-id"
        )
        end_time = time.time()
        
        recording_time = (end_time - start_time) * 1000
        assert recording_time < 500, f"Container metrics recording took {recording_time:.2f}ms, expected <500ms"
        
        # Test namespace utilization performance (<200ms)
        start_time = time.time()
        await container_orchestration_service.get_namespace_utilization(
            namespace="culture-connect",
            correlation_id="perf-test-correlation-id"
        )
        end_time = time.time()
        
        utilization_time = (end_time - start_time) * 1000
        assert utilization_time < 200, f"Namespace utilization took {utilization_time:.2f}ms, expected <200ms"
        
        # Test Kubernetes manifest generation performance (<500ms)
        service_config = {
            "name": "perf-test-service",
            "namespace": "culture-connect",
            "image": "perf-test:v1.0.0",
            "port": 8000,
            "replicas": 3
        }
        
        start_time = time.time()
        await container_orchestration_service.generate_kubernetes_manifests(
            service_config, "perf-test-correlation-id"
        )
        end_time = time.time()
        
        manifest_time = (end_time - start_time) * 1000
        assert manifest_time < 500, f"Manifest generation took {manifest_time:.2f}ms, expected <500ms"


class TestAPIEndpointPerformance:
    """Performance tests for scaling API endpoints."""

    @pytest.mark.asyncio
    async def test_scaling_metrics_endpoints_performance(self, client: AsyncClient, auth_headers: dict):
        """Test scaling metrics API endpoints performance."""
        # Test POST /api/v1/scaling/metrics performance (<500ms)
        metric_data = {
            "metric_name": "cpu_utilization",
            "metric_type": "resource",
            "component": "booking-service",
            "current_value": 75.5,
            "cpu_utilization": 75.5,
            "memory_utilization": 60.2
        }
        
        start_time = time.time()
        response = await client.post(
            "/api/v1/scaling/metrics",
            json=metric_data,
            headers=auth_headers
        )
        end_time = time.time()
        
        post_time = (end_time - start_time) * 1000
        assert response.status_code == 201
        assert post_time < 500, f"POST /scaling/metrics took {post_time:.2f}ms, expected <500ms"
        
        # Test GET /api/v1/scaling/metrics performance (<200ms)
        start_time = time.time()
        response = await client.get(
            "/api/v1/scaling/metrics?component=booking-service",
            headers=auth_headers
        )
        end_time = time.time()
        
        get_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert get_time < 200, f"GET /scaling/metrics took {get_time:.2f}ms, expected <200ms"
        
        # Test GET /api/v1/scaling/metrics/utilization performance (<200ms)
        start_time = time.time()
        response = await client.get(
            "/api/v1/scaling/metrics/utilization",
            headers=auth_headers
        )
        end_time = time.time()
        
        utilization_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert utilization_time < 200, f"GET /scaling/metrics/utilization took {utilization_time:.2f}ms, expected <200ms"

    @pytest.mark.asyncio
    async def test_auto_scaling_policies_endpoints_performance(self, client: AsyncClient, auth_headers: dict):
        """Test auto-scaling policies API endpoints performance."""
        # Test POST /api/v1/scaling/policies performance (<500ms)
        policy_data = {
            "name": "perf-test-policy",
            "component": "booking-service",
            "min_replicas": 2,
            "max_replicas": 10,
            "scale_up_threshold": 80.0,
            "scale_down_threshold": 30.0
        }
        
        start_time = time.time()
        response = await client.post(
            "/api/v1/scaling/policies",
            json=policy_data,
            headers=auth_headers
        )
        end_time = time.time()
        
        post_time = (end_time - start_time) * 1000
        assert response.status_code == 201
        assert post_time < 500, f"POST /scaling/policies took {post_time:.2f}ms, expected <500ms"
        
        policy_id = response.json()["id"]
        
        # Test GET /api/v1/scaling/policies performance (<200ms)
        start_time = time.time()
        response = await client.get(
            "/api/v1/scaling/policies",
            headers=auth_headers
        )
        end_time = time.time()
        
        get_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert get_time < 200, f"GET /scaling/policies took {get_time:.2f}ms, expected <200ms"
        
        # Test PUT /api/v1/scaling/policies/{id} performance (<500ms)
        update_data = {"max_replicas": 15}
        
        start_time = time.time()
        response = await client.put(
            f"/api/v1/scaling/policies/{policy_id}",
            json=update_data,
            headers=auth_headers
        )
        end_time = time.time()
        
        put_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert put_time < 500, f"PUT /scaling/policies took {put_time:.2f}ms, expected <500ms"

    @pytest.mark.asyncio
    async def test_container_orchestration_endpoints_performance(self, client: AsyncClient, auth_headers: dict):
        """Test container orchestration API endpoints performance."""
        # Test POST /api/v1/scaling/containers/metrics performance (<500ms)
        metrics_data = {
            "container_id": "perf-test-container",
            "pod_name": "perf-test-pod",
            "namespace": "culture-connect",
            "cpu_usage_cores": 0.5,
            "memory_usage_bytes": 512000000,
            "request_count": 150,
            "error_count": 2,
            "status": "running"
        }
        
        start_time = time.time()
        response = await client.post(
            "/api/v1/scaling/containers/metrics",
            json=metrics_data,
            headers=auth_headers
        )
        end_time = time.time()
        
        post_time = (end_time - start_time) * 1000
        assert response.status_code == 201
        assert post_time < 500, f"POST /containers/metrics took {post_time:.2f}ms, expected <500ms"
        
        # Test GET /api/v1/scaling/containers/utilization/{namespace} performance (<200ms)
        start_time = time.time()
        response = await client.get(
            "/api/v1/scaling/containers/utilization/culture-connect",
            headers=auth_headers
        )
        end_time = time.time()
        
        get_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert get_time < 200, f"GET /containers/utilization took {get_time:.2f}ms, expected <200ms"
        
        # Test POST /api/v1/scaling/containers/manifests performance (<500ms)
        service_config = {
            "name": "perf-test-service",
            "namespace": "culture-connect",
            "image": "perf-test:v1.0.0",
            "port": 8000,
            "replicas": 3
        }
        
        start_time = time.time()
        response = await client.post(
            "/api/v1/scaling/containers/manifests",
            json=service_config,
            headers=auth_headers
        )
        end_time = time.time()
        
        manifest_time = (end_time - start_time) * 1000
        assert response.status_code == 200
        assert manifest_time < 500, f"POST /containers/manifests took {manifest_time:.2f}ms, expected <500ms"


class TestConcurrentOperationsPerformance:
    """Performance tests for concurrent scaling operations."""

    @pytest.mark.asyncio
    async def test_concurrent_metric_recording(self, client: AsyncClient, auth_headers: dict):
        """Test concurrent metric recording performance."""
        async def record_metric(metric_id: int):
            """Record a single metric."""
            metric_data = {
                "metric_name": f"test_metric_{metric_id}",
                "metric_type": "resource",
                "component": "booking-service",
                "current_value": 50.0 + (metric_id % 50),
                "cpu_utilization": 50.0 + (metric_id % 50)
            }
            
            start_time = time.time()
            response = await client.post(
                "/api/v1/scaling/metrics",
                json=metric_data,
                headers=auth_headers
            )
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test concurrent recording of 10 metrics
        concurrent_requests = 10
        tasks = [record_metric(i) for i in range(concurrent_requests)]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = (end_time - start_time) * 1000
        
        # Verify all requests succeeded
        success_count = sum(1 for result in results if result["status_code"] == 201)
        assert success_count == concurrent_requests, f"Only {success_count}/{concurrent_requests} requests succeeded"
        
        # Verify individual response times
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 500, f"Average response time {avg_response_time:.2f}ms exceeded 500ms"
        assert max_response_time < 1000, f"Max response time {max_response_time:.2f}ms exceeded 1000ms"
        
        # Calculate throughput (requests per second)
        throughput = (concurrent_requests / total_time) * 1000
        assert throughput > 10, f"Throughput {throughput:.2f} req/s is below minimum 10 req/s"

    @pytest.mark.asyncio
    async def test_scaling_decision_response_time(self, auto_scaling_service: AutoScalingService):
        """Test end-to-end scaling decision response time (<2 minutes)."""
        # Create a test policy
        policy_data = AutoScalingPolicyCreate(
            name="response-time-test-policy",
            component="booking-service",
            min_replicas=2,
            max_replicas=10,
            scale_up_threshold=Decimal("80.0"),
            scale_down_threshold=Decimal("30.0"),
            scale_up_cooldown_seconds=60,  # Reduced for testing
            scale_down_cooldown_seconds=120
        )
        
        # Measure time from policy evaluation to scaling decision
        start_time = time.time()
        
        # Create policy
        await auto_scaling_service.create_scaling_policy(policy_data, "response-time-test")
        
        # Evaluate policies (simulates trigger detection)
        decisions = await auto_scaling_service.evaluate_scaling_policies("response-time-test")
        
        # Execute scaling decision (simulates container scaling)
        if decisions:
            await auto_scaling_service.execute_scaling_decision(decisions[0], "response-time-test")
        
        end_time = time.time()
        
        total_response_time = end_time - start_time
        assert total_response_time < 120, f"Scaling response time {total_response_time:.2f}s exceeded 2 minutes"

    @pytest.mark.asyncio
    async def test_redis_caching_performance(self, scaling_metrics_service: ScalingMetricsService):
        """Test Redis caching performance and hit rate."""
        component = "cache-test-service"
        
        # First call (cache miss)
        start_time = time.time()
        await scaling_metrics_service.get_component_metrics(
            component=component,
            limit=100,
            correlation_id="cache-test-1"
        )
        first_call_time = (time.time() - start_time) * 1000
        
        # Second call (cache hit)
        start_time = time.time()
        await scaling_metrics_service.get_component_metrics(
            component=component,
            limit=100,
            correlation_id="cache-test-2"
        )
        second_call_time = (time.time() - start_time) * 1000
        
        # Cache hit should be significantly faster
        cache_improvement = (first_call_time - second_call_time) / first_call_time
        assert cache_improvement > 0.3, f"Cache improvement {cache_improvement:.2%} is below 30%"
        assert second_call_time < 50, f"Cached query took {second_call_time:.2f}ms, expected <50ms"
