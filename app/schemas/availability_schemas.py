"""
Pydantic V2 schemas for availability management system.

This module provides comprehensive validation schemas for vendor availability management,
including timezone validation, time range validation, recurring pattern validation,
and business rule enforcement.

Key Features:
- Pydantic V2 compliance with ConfigDict
- Comprehensive field validation with custom validators
- Business rule enforcement
- Timezone support with pytz validation
- Cross-field validation for complex business logic
- Proper error messages for validation failures

Production-grade implementation following established schema patterns.
"""

from datetime import date, time, datetime, timedelta
from typing import Optional, List, Literal
import pytz
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from pydantic.types import PositiveInt, NonNegativeInt


# ================================
# VendorAvailability Schemas
# ================================

class VendorAvailabilityCreateSchema(BaseModel):
    """Schema for creating vendor availability configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    # Basic configuration
    service_id: Optional[PositiveInt] = Field(
        None,
        description="Optional service ID for service-specific availability"
    )
    timezone: str = Field(
        default="UTC",
        description="Timezone for availability (e.g., 'America/New_York', 'UTC')",
        min_length=3,
        max_length=50
    )

    # Booking rules
    advance_booking_days: PositiveInt = Field(
        default=30,
        description="Maximum days in advance bookings can be made",
        le=365
    )
    min_booking_notice_hours: NonNegativeInt = Field(
        default=24,
        description="Minimum hours notice required for booking",
        le=168  # 1 week max
    )
    max_booking_notice_days: PositiveInt = Field(
        default=90,
        description="Maximum days in advance bookings can be made",
        le=365
    )

    # Slot configuration
    default_slot_duration_minutes: PositiveInt = Field(
        default=60,
        description="Default duration for availability slots in minutes",
        ge=15,  # Minimum 15 minutes
        le=480  # Maximum 8 hours
    )
    buffer_time_minutes: NonNegativeInt = Field(
        default=15,
        description="Buffer time between slots in minutes",
        le=120  # Maximum 2 hours buffer
    )
    max_daily_bookings: Optional[PositiveInt] = Field(
        None,
        description="Maximum bookings allowed per day (optional limit)",
        le=50
    )

    # Business hours
    earliest_booking_time: time = Field(
        default=time(9, 0),
        description="Earliest time of day for bookings"
    )
    latest_booking_time: time = Field(
        default=time(17, 0),
        description="Latest time of day for bookings"
    )

    # Additional settings
    notes: Optional[str] = Field(
        None,
        description="Additional notes about availability configuration",
        max_length=500
    )

    @field_validator('timezone')
    @classmethod
    def validate_timezone(cls, v: str) -> str:
        """Validate timezone string using pytz."""
        try:
            pytz.timezone(v)
            return v
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"Invalid timezone: {v}. Must be a valid pytz timezone.")

    @model_validator(mode='after')
    def validate_booking_rules(self):
        """Validate business rules and time constraints."""
        # Validate booking time order
        if self.earliest_booking_time >= self.latest_booking_time:
            raise ValueError("earliest_booking_time must be before latest_booking_time")

        # Validate booking notice periods
        if self.min_booking_notice_hours > (self.max_booking_notice_days * 24):
            raise ValueError("min_booking_notice_hours cannot exceed max_booking_notice_days")

        # Validate advance booking logic (advance_booking_days should be >= max_booking_notice_days)
        if self.advance_booking_days < self.max_booking_notice_days:
            # This is actually OK - advance_booking_days can be less than max_booking_notice_days
            # Let's just ensure they're both positive
            pass

        return self


class VendorAvailabilityUpdateSchema(BaseModel):
    """Schema for updating vendor availability configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    timezone: Optional[str] = Field(None, min_length=3, max_length=50)
    advance_booking_days: Optional[PositiveInt] = Field(None, le=365)
    min_booking_notice_hours: Optional[NonNegativeInt] = Field(None, le=168)
    max_booking_notice_days: Optional[PositiveInt] = Field(None, le=365)
    default_slot_duration_minutes: Optional[PositiveInt] = Field(None, ge=15, le=480)
    buffer_time_minutes: Optional[NonNegativeInt] = Field(None, le=120)
    max_daily_bookings: Optional[PositiveInt] = Field(None, le=50)
    earliest_booking_time: Optional[time] = None
    latest_booking_time: Optional[time] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = Field(None, max_length=500)

    @field_validator('timezone')
    @classmethod
    def validate_timezone(cls, v: Optional[str]) -> Optional[str]:
        """Validate timezone string using pytz."""
        if v is None:
            return v
        try:
            pytz.timezone(v)
            return v
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"Invalid timezone: {v}. Must be a valid pytz timezone.")

    @model_validator(mode='after')
    def validate_time_order(self):
        """Validate time order if both times are provided."""
        if (self.earliest_booking_time is not None and
            self.latest_booking_time is not None and
            self.earliest_booking_time >= self.latest_booking_time):
            raise ValueError("earliest_booking_time must be before latest_booking_time")
        return self


class VendorAvailabilityResponseSchema(BaseModel):
    """Schema for vendor availability response."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    vendor_id: int
    service_id: Optional[int]
    timezone: str
    advance_booking_days: int
    min_booking_notice_hours: int
    max_booking_notice_days: int
    default_slot_duration_minutes: int
    buffer_time_minutes: int
    max_daily_bookings: Optional[int]
    earliest_booking_time: time
    latest_booking_time: time
    is_active: bool
    notes: Optional[str]


# ================================
# RecurringAvailability Schemas
# ================================

class RecurringAvailabilityCreateSchema(BaseModel):
    """Schema for creating recurring availability patterns."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    pattern_type: Literal["daily", "weekly", "monthly"] = Field(
        description="Type of recurring pattern"
    )
    pattern_name: Optional[str] = Field(
        None,
        description="Descriptive name for the pattern",
        max_length=100
    )

    # Pattern-specific fields
    day_of_week: Optional[int] = Field(
        None,
        description="Day of week for weekly patterns (0=Monday, 6=Sunday)",
        ge=0,
        le=6
    )
    day_of_month: Optional[int] = Field(
        None,
        description="Day of month for monthly patterns (1-31)",
        ge=1,
        le=31
    )

    # Time configuration
    start_time: time = Field(description="Start time for the recurring pattern")
    end_time: time = Field(description="End time for the recurring pattern")

    # Validity period
    valid_from: date = Field(description="Date when pattern becomes valid")
    valid_until: Optional[date] = Field(
        None,
        description="Date when pattern expires (null for indefinite)"
    )

    # Slot configuration
    slot_duration_minutes: PositiveInt = Field(
        default=60,
        description="Duration of each generated slot in minutes",
        ge=15,
        le=480
    )
    max_bookings_per_slot: PositiveInt = Field(
        default=1,
        description="Maximum bookings allowed per generated slot",
        le=10
    )
    buffer_time_minutes: NonNegativeInt = Field(
        default=0,
        description="Buffer time between generated slots",
        le=120
    )

    # Generation settings
    auto_generate: bool = Field(
        default=True,
        description="Whether to automatically generate slots"
    )
    generation_advance_days: PositiveInt = Field(
        default=30,
        description="How many days in advance to generate slots",
        ge=1,
        le=365
    )

    notes: Optional[str] = Field(None, max_length=500)

    @model_validator(mode='after')
    def validate_pattern_configuration(self):
        """Validate pattern configuration consistency."""
        # Validate time order
        if self.start_time >= self.end_time:
            raise ValueError("start_time must be before end_time")

        # Validate pattern-specific requirements
        if self.pattern_type == "weekly" and self.day_of_week is None:
            raise ValueError("day_of_week is required for weekly patterns")
        elif self.pattern_type == "monthly" and self.day_of_month is None:
            raise ValueError("day_of_month is required for monthly patterns")
        elif self.pattern_type == "daily" and (self.day_of_week is not None or self.day_of_month is not None):
            raise ValueError("day_of_week and day_of_month should not be set for daily patterns")

        # Validate validity period
        if self.valid_until is not None and self.valid_until <= self.valid_from:
            raise ValueError("valid_until must be after valid_from")

        # Validate future start date
        if self.valid_from < date.today():
            raise ValueError("valid_from cannot be in the past")

        return self


class RecurringAvailabilityUpdateSchema(BaseModel):
    """Schema for updating recurring availability patterns."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    pattern_name: Optional[str] = Field(None, max_length=100)
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    valid_until: Optional[date] = None
    slot_duration_minutes: Optional[PositiveInt] = Field(None, ge=15, le=480)
    max_bookings_per_slot: Optional[PositiveInt] = Field(None, le=10)
    buffer_time_minutes: Optional[NonNegativeInt] = Field(None, le=120)
    auto_generate: Optional[bool] = None
    generation_advance_days: Optional[PositiveInt] = Field(None, ge=1, le=365)
    is_active: Optional[bool] = None
    notes: Optional[str] = Field(None, max_length=500)

    @model_validator(mode='after')
    def validate_time_order(self):
        """Validate time order if both times are provided."""
        if (self.start_time is not None and
            self.end_time is not None and
            self.start_time >= self.end_time):
            raise ValueError("start_time must be before end_time")
        return self


class RecurringAvailabilityResponseSchema(BaseModel):
    """Schema for recurring availability response."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    vendor_availability_id: int
    pattern_type: str
    pattern_name: Optional[str]
    day_of_week: Optional[int]
    day_of_month: Optional[int]
    start_time: time
    end_time: time
    valid_from: date
    valid_until: Optional[date]
    slot_duration_minutes: int
    max_bookings_per_slot: int
    buffer_time_minutes: int
    is_active: bool
    auto_generate: bool
    generation_advance_days: int
    notes: Optional[str]
    last_generated_date: Optional[date]


# ================================
# AvailabilitySlot Schemas
# ================================

class AvailabilitySlotCreateSchema(BaseModel):
    """Schema for creating individual availability slots."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    availability_date: date = Field(description="Date for the availability slot")
    start_time: time = Field(description="Start time of the slot")
    end_time: time = Field(description="End time of the slot")
    max_bookings: PositiveInt = Field(
        default=1,
        description="Maximum number of bookings for this slot",
        le=10
    )
    slot_type: Literal["regular", "exception", "recurring", "custom"] = Field(
        default="regular",
        description="Type of availability slot"
    )
    recurring_availability_id: Optional[PositiveInt] = Field(
        None,
        description="ID of recurring pattern that generated this slot"
    )
    notes: Optional[str] = Field(None, max_length=500)
    special_pricing: Optional[str] = Field(
        None,
        max_length=100,
        description="Special pricing information for this slot"
    )

    @model_validator(mode='after')
    def validate_slot_configuration(self):
        """Validate slot configuration and business rules."""
        # Validate time order
        if self.start_time >= self.end_time:
            raise ValueError("start_time must be before end_time")

        # Validate future date
        if self.availability_date < date.today():
            raise ValueError("Availability slots cannot be created for past dates")

        # Validate slot duration (minimum 15 minutes, maximum 8 hours)
        start_datetime = datetime.combine(self.availability_date, self.start_time)
        end_datetime = datetime.combine(self.availability_date, self.end_time)
        duration = end_datetime - start_datetime

        if duration < timedelta(minutes=15):
            raise ValueError("Slot duration must be at least 15 minutes")
        if duration > timedelta(hours=8):
            raise ValueError("Slot duration cannot exceed 8 hours")

        return self


class AvailabilitySlotUpdateSchema(BaseModel):
    """Schema for updating availability slots."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    start_time: Optional[time] = None
    end_time: Optional[time] = None
    is_available: Optional[bool] = None
    max_bookings: Optional[PositiveInt] = Field(None, le=10)
    notes: Optional[str] = Field(None, max_length=500)
    special_pricing: Optional[str] = Field(None, max_length=100)

    @model_validator(mode='after')
    def validate_time_order(self):
        """Validate time order if both times are provided."""
        if (self.start_time is not None and
            self.end_time is not None and
            self.start_time >= self.end_time):
            raise ValueError("start_time must be before end_time")
        return self


class AvailabilitySlotResponseSchema(BaseModel):
    """Schema for availability slot response."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    vendor_availability_id: int
    date: date
    start_time: time
    end_time: time
    is_available: bool
    max_bookings: int
    current_bookings: int
    slot_type: str
    recurring_availability_id: Optional[int]
    notes: Optional[str]
    special_pricing: Optional[str]

    # Computed properties
    is_fully_booked: bool = Field(description="Whether slot is fully booked")
    available_capacity: int = Field(description="Remaining booking capacity")


# ================================
# AvailabilityException Schemas
# ================================

class AvailabilityExceptionCreateSchema(BaseModel):
    """Schema for creating availability exceptions."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    recurring_availability_id: Optional[PositiveInt] = Field(
        None,
        description="ID of recurring pattern this exception applies to"
    )
    exception_date: date = Field(description="Date for the exception")
    exception_type: Literal["unavailable", "modified", "custom"] = Field(
        description="Type of exception"
    )

    # Modified availability (for 'modified' and 'custom' types)
    modified_start_time: Optional[time] = Field(
        None,
        description="Modified start time (required for modified/custom types)"
    )
    modified_end_time: Optional[time] = Field(
        None,
        description="Modified end time (required for modified/custom types)"
    )
    modified_max_bookings: Optional[PositiveInt] = Field(
        None,
        description="Modified max bookings (optional for modified/custom types)",
        le=10
    )

    reason: Optional[str] = Field(
        None,
        max_length=100,
        description="Reason for the exception"
    )
    notes: Optional[str] = Field(None, max_length=500)

    @model_validator(mode='after')
    def validate_exception_configuration(self):
        """Validate exception configuration based on type."""
        # Validate future date
        if self.exception_date < date.today():
            raise ValueError("Exceptions cannot be created for past dates")

        # Validate modified time requirements
        if self.exception_type in ["modified", "custom"]:
            if self.modified_start_time is None or self.modified_end_time is None:
                raise ValueError(
                    f"modified_start_time and modified_end_time are required for {self.exception_type} exceptions"
                )
            if self.modified_start_time >= self.modified_end_time:
                raise ValueError("modified_start_time must be before modified_end_time")
        elif self.exception_type == "unavailable":
            if (self.modified_start_time is not None or
                self.modified_end_time is not None or
                self.modified_max_bookings is not None):
                raise ValueError(
                    "modified times and bookings should not be set for unavailable exceptions"
                )

        return self


class AvailabilityExceptionUpdateSchema(BaseModel):
    """Schema for updating availability exceptions."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    exception_type: Optional[Literal["unavailable", "modified", "custom"]] = None
    modified_start_time: Optional[time] = None
    modified_end_time: Optional[time] = None
    modified_max_bookings: Optional[PositiveInt] = Field(None, le=10)
    reason: Optional[str] = Field(None, max_length=100)
    notes: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None

    @model_validator(mode='after')
    def validate_modified_times(self):
        """Validate modified time order if both are provided."""
        if (self.modified_start_time is not None and
            self.modified_end_time is not None and
            self.modified_start_time >= self.modified_end_time):
            raise ValueError("modified_start_time must be before modified_end_time")
        return self


class AvailabilityExceptionResponseSchema(BaseModel):
    """Schema for availability exception response."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    vendor_availability_id: int
    recurring_availability_id: Optional[int]
    exception_date: date
    exception_type: str
    modified_start_time: Optional[time]
    modified_end_time: Optional[time]
    modified_max_bookings: Optional[int]
    reason: Optional[str]
    notes: Optional[str]
    is_active: bool


# ================================
# Bulk and Utility Schemas
# ================================

class BulkSlotCreateSchema(BaseModel):
    """Schema for creating multiple availability slots in bulk."""

    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid"
    )

    start_date: date = Field(description="Start date for bulk slot creation")
    end_date: date = Field(description="End date for bulk slot creation")
    start_time: time = Field(description="Daily start time for slots")
    end_time: time = Field(description="Daily end time for slots")
    slot_duration_minutes: PositiveInt = Field(
        default=60,
        description="Duration of each slot in minutes",
        ge=15,
        le=480
    )
    buffer_time_minutes: NonNegativeInt = Field(
        default=15,
        description="Buffer time between slots",
        le=120
    )
    max_bookings_per_slot: PositiveInt = Field(
        default=1,
        description="Maximum bookings per slot",
        le=10
    )
    days_of_week: Optional[List[int]] = Field(
        None,
        description="Days of week to create slots (0=Monday, 6=Sunday). If None, all days included.",
        min_length=1,
        max_length=7
    )
    exclude_dates: Optional[List[date]] = Field(
        None,
        description="Specific dates to exclude from bulk creation"
    )

    @field_validator('days_of_week')
    @classmethod
    def validate_days_of_week(cls, v: Optional[List[int]]) -> Optional[List[int]]:
        """Validate days of week values."""
        if v is None:
            return v
        for day in v:
            if not (0 <= day <= 6):
                raise ValueError(f"Invalid day of week: {day}. Must be between 0-6.")
        return sorted(list(set(v)))  # Remove duplicates and sort

    @model_validator(mode='after')
    def validate_bulk_configuration(self):
        """Validate bulk slot creation configuration."""
        # Validate date order
        if self.end_date <= self.start_date:
            raise ValueError("end_date must be after start_date")

        # Validate time order
        if self.start_time >= self.end_time:
            raise ValueError("start_time must be before end_time")

        # Validate future dates
        if self.start_date < date.today():
            raise ValueError("start_date cannot be in the past")

        # Validate date range (max 90 days)
        if (self.end_date - self.start_date).days > 90:
            raise ValueError("Date range cannot exceed 90 days for bulk creation")

        # Validate slot configuration
        total_minutes = (datetime.combine(date.today(), self.end_time) -
                        datetime.combine(date.today(), self.start_time)).total_seconds() / 60
        slot_with_buffer = self.slot_duration_minutes + self.buffer_time_minutes

        if slot_with_buffer > total_minutes:
            raise ValueError("Slot duration plus buffer time exceeds available time window")

        return self


class BulkSlotResponseSchema(BaseModel):
    """Schema for bulk slot creation response."""

    model_config = ConfigDict(from_attributes=True)

    created_slots: int = Field(description="Number of slots created")
    skipped_slots: int = Field(description="Number of slots skipped (conflicts/exclusions)")
    total_requested: int = Field(description="Total slots requested")
    created_slot_ids: List[int] = Field(description="IDs of created slots")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")


class AvailabilityCheckResponseSchema(BaseModel):
    """Schema for availability checking response."""

    model_config = ConfigDict(from_attributes=True)

    available: bool = Field(description="Whether the requested time is available")
    vendor_id: int = Field(description="Vendor ID checked")
    service_id: Optional[int] = Field(description="Service ID checked (if specified)")
    start_datetime: datetime = Field(description="Requested start datetime")
    end_datetime: datetime = Field(description="Requested end datetime")
    available_slots: List[AvailabilitySlotResponseSchema] = Field(
        default_factory=list,
        description="Available slots that match the request"
    )
    conflicts: List[str] = Field(
        default_factory=list,
        description="Reasons why time is not available (if applicable)"
    )
    alternative_suggestions: List[datetime] = Field(
        default_factory=list,
        description="Alternative available times near the requested time"
    )


class AvailabilitySlotListResponseSchema(BaseModel):
    """Schema for paginated availability slot list response."""

    model_config = ConfigDict(from_attributes=True)

    slots: List[AvailabilitySlotResponseSchema] = Field(description="List of availability slots")
    total: int = Field(description="Total number of slots")
    start_date: date = Field(description="Start date of the range")
    end_date: date = Field(description="End date of the range")
    vendor_id: int = Field(description="Vendor ID")
    service_id: Optional[int] = Field(description="Service ID (if filtered)")
    timezone: str = Field(description="Timezone for the slots")
