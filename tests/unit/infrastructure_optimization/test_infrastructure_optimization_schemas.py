"""
Unit tests for infrastructure optimization schemas.

Tests for Task 3.2.3 infrastructure optimization schemas including:
- CacheConfiguration schema validation and serialization
- BackgroundTask schema lifecycle management
- PerformanceMonitoring schema query tracking
- CacheMetrics and TaskMetrics schema analytics
- Schema validation rules and error handling

Implements comprehensive test coverage with >80% requirement for
production-grade infrastructure optimization functionality.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from pydantic import ValidationError

from app.schemas.infrastructure_optimization import (
    CacheConfigurationCreate, CacheConfigurationUpdate, CacheConfigurationResponse,
    BackgroundTaskCreate, BackgroundTaskUpdate, BackgroundTaskResponse,
    PerformanceMonitoringCreate, PerformanceMonitoringResponse,
    CacheMetricsCreate, CacheMetricsResponse,
    TaskMetricsCreate, TaskMetricsResponse,
    InfrastructureOptimizationDashboard,
    CacheType, TaskStatus, TaskPriority
)


class TestCacheConfigurationSchemas:
    """Test cache configuration schemas."""

    def test_cache_configuration_create_valid(self):
        """Test valid cache configuration creation."""
        data = {
            "cache_type": CacheType.OPTIMIZATION_SCORE,
            "cache_key_pattern": "opt:{service_id}",
            "ttl_seconds": 86400,
            "max_size_mb": 100,
            "compression_enabled": True,
            "invalidation_rules": {"on_update": True},
            "is_active": True
        }
        
        schema = CacheConfigurationCreate(**data)
        
        assert schema.cache_type == CacheType.OPTIMIZATION_SCORE
        assert schema.cache_key_pattern == "opt:{service_id}"
        assert schema.ttl_seconds == 86400
        assert schema.max_size_mb == 100
        assert schema.compression_enabled is True
        assert schema.invalidation_rules == {"on_update": True}
        assert schema.is_active is True

    def test_cache_configuration_create_defaults(self):
        """Test cache configuration creation with defaults."""
        data = {
            "cache_type": CacheType.MARKET_INTELLIGENCE,
            "cache_key_pattern": "market:{category}",
            "ttl_seconds": 3600
        }
        
        schema = CacheConfigurationCreate(**data)
        
        assert schema.max_size_mb == 100
        assert schema.compression_enabled is True
        assert schema.invalidation_rules == {}
        assert schema.is_active is True

    def test_cache_configuration_ttl_validation(self):
        """Test TTL validation rules."""
        # TTL too low
        with pytest.raises(ValidationError) as exc_info:
            CacheConfigurationCreate(
                cache_type=CacheType.OPTIMIZATION_SCORE,
                cache_key_pattern="opt:{service_id}",
                ttl_seconds=30  # Below minimum of 60
            )
        assert "TTL must be at least 60 seconds" in str(exc_info.value)

        # TTL too high
        with pytest.raises(ValidationError) as exc_info:
            CacheConfigurationCreate(
                cache_type=CacheType.OPTIMIZATION_SCORE,
                cache_key_pattern="opt:{service_id}",
                ttl_seconds=86400 * 8  # Above maximum of 7 days
            )
        assert "TTL cannot exceed 7 days" in str(exc_info.value)

    def test_cache_key_pattern_validation(self):
        """Test cache key pattern validation."""
        # Empty pattern
        with pytest.raises(ValidationError) as exc_info:
            CacheConfigurationCreate(
                cache_type=CacheType.OPTIMIZATION_SCORE,
                cache_key_pattern="",
                ttl_seconds=3600
            )
        assert "Cache key pattern cannot be empty" in str(exc_info.value)

        # Invalid pattern format
        with pytest.raises(ValidationError) as exc_info:
            CacheConfigurationCreate(
                cache_type=CacheType.OPTIMIZATION_SCORE,
                cache_key_pattern="opt:{invalid",
                ttl_seconds=3600
            )
        assert "Invalid cache key pattern format" in str(exc_info.value)

    def test_cache_configuration_update(self):
        """Test cache configuration update schema."""
        data = {
            "ttl_seconds": 7200,
            "compression_enabled": False,
            "is_active": False
        }
        
        schema = CacheConfigurationUpdate(**data)
        
        assert schema.ttl_seconds == 7200
        assert schema.compression_enabled is False
        assert schema.is_active is False
        assert schema.cache_type is None  # Not updated
        assert schema.cache_key_pattern is None  # Not updated

    def test_cache_configuration_response(self):
        """Test cache configuration response schema."""
        data = {
            "id": uuid4(),
            "cache_type": CacheType.OPTIMIZATION_SCORE,
            "cache_key_pattern": "opt:{service_id}",
            "ttl_seconds": 86400,
            "max_size_mb": 100,
            "compression_enabled": True,
            "invalidation_rules": {"on_update": True},
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "created_by": uuid4()
        }
        
        schema = CacheConfigurationResponse(**data)
        
        assert schema.id == data["id"]
        assert schema.cache_type == CacheType.OPTIMIZATION_SCORE
        assert schema.created_by == data["created_by"]


class TestBackgroundTaskSchemas:
    """Test background task schemas."""

    def test_background_task_create_valid(self):
        """Test valid background task creation."""
        data = {
            "task_name": "optimization.calculate_score",
            "task_type": "optimization",
            "priority": TaskPriority.HIGH,
            "queue_name": "high_priority",
            "max_retries": 5,
            "task_args": {"service_id": "123"},
            "task_kwargs": {"include_competitive": True},
            "correlation_id": "corr-123"
        }
        
        schema = BackgroundTaskCreate(**data)
        
        assert schema.task_name == "optimization.calculate_score"
        assert schema.task_type == "optimization"
        assert schema.priority == TaskPriority.HIGH
        assert schema.queue_name == "high_priority"
        assert schema.max_retries == 5
        assert schema.task_args == {"service_id": "123"}
        assert schema.task_kwargs == {"include_competitive": True}
        assert schema.correlation_id == "corr-123"

    def test_background_task_create_defaults(self):
        """Test background task creation with defaults."""
        data = {
            "task_name": "test.task",
            "task_type": "test"
        }
        
        schema = BackgroundTaskCreate(**data)
        
        assert schema.priority == TaskPriority.NORMAL
        assert schema.queue_name == "default"
        assert schema.max_retries == 3
        assert schema.task_args == {}
        assert schema.task_kwargs == {}

    def test_background_task_max_retries_validation(self):
        """Test max retries validation."""
        # Negative retries
        with pytest.raises(ValidationError):
            BackgroundTaskCreate(
                task_name="test.task",
                task_type="test",
                max_retries=-1
            )

        # Too many retries
        with pytest.raises(ValidationError):
            BackgroundTaskCreate(
                task_name="test.task",
                task_type="test",
                max_retries=15
            )

    def test_background_task_update(self):
        """Test background task update schema."""
        data = {
            "status": TaskStatus.SUCCESS,
            "worker_name": "worker-01",
            "started_at": datetime.utcnow(),
            "completed_at": datetime.utcnow(),
            "execution_time_seconds": 120.5,
            "result_data": {"score": 85.5},
            "error_message": None
        }
        
        schema = BackgroundTaskUpdate(**data)
        
        assert schema.status == TaskStatus.SUCCESS
        assert schema.worker_name == "worker-01"
        assert schema.execution_time_seconds == 120.5
        assert schema.result_data == {"score": 85.5}

    def test_background_task_response_computed_fields(self):
        """Test background task response computed fields."""
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(seconds=120)
        
        data = {
            "id": uuid4(),
            "task_id": "celery-123",
            "task_name": "test.task",
            "task_type": "test",
            "priority": TaskPriority.NORMAL,
            "status": TaskStatus.SUCCESS,
            "retry_count": 0,
            "queue_name": "default",
            "max_retries": 3,
            "task_args": {},
            "task_kwargs": {},
            "queued_at": start_time,
            "started_at": start_time,
            "completed_at": end_time,
            "execution_time_seconds": 120.0
        }
        
        schema = BackgroundTaskResponse(**data)
        
        assert schema.is_completed is True
        assert schema.duration_seconds == 120.0

    def test_background_task_response_not_completed(self):
        """Test background task response for non-completed task."""
        data = {
            "id": uuid4(),
            "task_id": "celery-123",
            "task_name": "test.task",
            "task_type": "test",
            "priority": TaskPriority.NORMAL,
            "status": TaskStatus.RUNNING,
            "retry_count": 0,
            "queue_name": "default",
            "max_retries": 3,
            "task_args": {},
            "task_kwargs": {},
            "queued_at": datetime.utcnow()
        }
        
        schema = BackgroundTaskResponse(**data)
        
        assert schema.is_completed is False
        assert schema.duration_seconds is None


class TestPerformanceMonitoringSchemas:
    """Test performance monitoring schemas."""

    def test_performance_monitoring_create_valid(self):
        """Test valid performance monitoring creation."""
        data = {
            "query_type": "optimization_dashboard",
            "query_hash": "abc123def456",
            "table_names": ["services", "seo_analysis"],
            "execution_time_ms": 250.5,
            "rows_examined": 1000,
            "rows_returned": 50,
            "memory_usage_mb": 15.2,
            "cpu_usage_percent": 25.0,
            "endpoint_path": "/api/v1/optimization/dashboard",
            "query_plan": {"plan": "index_scan"},
            "index_usage": {"services_idx": "used"},
            "optimization_suggestions": ["Add composite index"],
            "correlation_id": "corr-123"
        }
        
        schema = PerformanceMonitoringCreate(**data)
        
        assert schema.query_type == "optimization_dashboard"
        assert schema.execution_time_ms == 250.5
        assert schema.rows_examined == 1000
        assert schema.rows_returned == 50
        assert schema.cpu_usage_percent == 25.0

    def test_performance_monitoring_execution_time_validation(self):
        """Test execution time validation."""
        # Unreasonably high execution time
        with pytest.raises(ValidationError) as exc_info:
            PerformanceMonitoringCreate(
                query_type="test_query",
                query_hash="test123",
                execution_time_ms=400000  # Over 5 minutes
            )
        assert "Execution time seems unreasonably high" in str(exc_info.value)

    def test_performance_monitoring_response_efficiency_score(self):
        """Test performance monitoring response efficiency score."""
        data = {
            "id": uuid4(),
            "query_type": "test_query",
            "query_hash": "test123",
            "table_names": [],
            "execution_time_ms": 100.0,
            "rows_examined": 1000,
            "rows_returned": 100,
            "is_slow_query": False,
            "executed_at": datetime.utcnow()
        }
        
        schema = PerformanceMonitoringResponse(**data)
        
        # Efficiency = (rows_returned / rows_examined) * 100 = (100 / 1000) * 100 = 10%
        assert schema.efficiency_score == 10.0

    def test_performance_monitoring_response_efficiency_score_edge_cases(self):
        """Test efficiency score edge cases."""
        # No rows examined
        data = {
            "id": uuid4(),
            "query_type": "test_query",
            "query_hash": "test123",
            "table_names": [],
            "execution_time_ms": 100.0,
            "rows_examined": 0,
            "rows_returned": 0,
            "is_slow_query": False,
            "executed_at": datetime.utcnow()
        }
        
        schema = PerformanceMonitoringResponse(**data)
        assert schema.efficiency_score == 100.0  # Perfect efficiency when no rows examined


class TestCacheMetricsSchemas:
    """Test cache metrics schemas."""

    def test_cache_metrics_create_valid(self):
        """Test valid cache metrics creation."""
        config_id = uuid4()
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=1)
        
        data = {
            "configuration_id": config_id,
            "cache_hits": 850,
            "cache_misses": 150,
            "memory_usage_mb": 256.5,
            "key_count": 1000,
            "expired_keys": 50,
            "evicted_keys": 10,
            "avg_response_time_ms": 2.5,
            "max_response_time_ms": 15.0,
            "operations_per_second": 100.0,
            "period_start": start_time,
            "period_end": end_time
        }
        
        schema = CacheMetricsCreate(**data)
        
        assert schema.configuration_id == config_id
        assert schema.cache_hits == 850
        assert schema.cache_misses == 150

    def test_cache_metrics_hit_rate_calculation(self):
        """Test cache hit rate calculation."""
        config_id = uuid4()
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=1)
        
        data = {
            "configuration_id": config_id,
            "cache_hits": 800,
            "cache_misses": 200,
            "period_start": start_time,
            "period_end": end_time
        }
        
        schema = CacheMetricsCreate(**data)
        
        # Hit rate = (800 / (800 + 200)) * 100 = 80%
        assert schema.cache_hit_rate == 80.0

    def test_cache_metrics_period_validation(self):
        """Test period validation."""
        config_id = uuid4()
        start_time = datetime.utcnow()
        end_time = start_time - timedelta(hours=1)  # End before start
        
        with pytest.raises(ValidationError) as exc_info:
            CacheMetricsCreate(
                configuration_id=config_id,
                period_start=start_time,
                period_end=end_time
            )
        assert "Period end must be after period start" in str(exc_info.value)


class TestTaskMetricsSchemas:
    """Test task metrics schemas."""

    def test_task_metrics_create_valid(self):
        """Test valid task metrics creation."""
        task_id = uuid4()
        
        data = {
            "task_id": task_id,
            "queue_wait_time_seconds": 5.2,
            "execution_time_seconds": 120.5,
            "memory_peak_mb": 512.0,
            "cpu_usage_percent": 75.0,
            "database_queries_count": 25,
            "cache_operations_count": 10,
            "external_api_calls": 3,
            "throughput_items_per_second": 50.0,
            "error_rate_percent": 2.5,
            "success_rate_percent": 97.5,
            "worker_node": "worker-01"
        }
        
        schema = TaskMetricsCreate(**data)
        
        assert schema.task_id == task_id
        assert schema.cpu_usage_percent == 75.0
        assert schema.success_rate_percent == 97.5

    def test_task_metrics_response_efficiency_score(self):
        """Test task metrics efficiency score calculation."""
        task_id = uuid4()
        
        data = {
            "id": uuid4(),
            "task_id": task_id,
            "cpu_usage_percent": 50.0,
            "memory_peak_mb": 500.0,
            "success_rate_percent": 95.0,
            "measured_at": datetime.utcnow()
        }
        
        schema = TaskMetricsResponse(**data)
        
        # Efficiency = success_rate * 0.7 + resource_efficiency * 0.3
        # Resource efficiency = 100 - (50 * 0.5 + min(500/1000, 1) * 50) = 100 - (25 + 25) = 50
        # Efficiency = 95 * 0.7 + 50 * 0.3 = 66.5 + 15 = 81.5
        assert abs(schema.efficiency_score - 81.5) < 0.1


class TestInfrastructureOptimizationDashboard:
    """Test infrastructure optimization dashboard schema."""

    def test_dashboard_schema_valid(self):
        """Test valid dashboard schema."""
        data = {
            "cache_performance": {
                "hit_rate": 85.5,
                "memory_usage_mb": 512.3,
                "operations_per_second": 1250.0
            },
            "task_performance": {
                "avg_execution_time": 2.5,
                "success_rate": 98.2,
                "queue_length": 15
            },
            "database_performance": {
                "avg_query_time": 45.2,
                "slow_queries_count": 3,
                "connection_pool_usage": 65.0
            },
            "optimization_recommendations": [
                "Increase cache TTL for optimization scores",
                "Add composite index for analytics queries"
            ],
            "system_health_score": 92.5
        }
        
        schema = InfrastructureOptimizationDashboard(**data)
        
        assert schema.system_health_score == 92.5
        assert len(schema.optimization_recommendations) == 2
        assert schema.cache_performance["hit_rate"] == 85.5

    def test_dashboard_system_health_score_validation(self):
        """Test system health score validation."""
        # Score too low
        with pytest.raises(ValidationError):
            InfrastructureOptimizationDashboard(
                cache_performance={},
                task_performance={},
                database_performance={},
                optimization_recommendations=[],
                system_health_score=-5.0
            )

        # Score too high
        with pytest.raises(ValidationError):
            InfrastructureOptimizationDashboard(
                cache_performance={},
                task_performance={},
                database_performance={},
                optimization_recommendations=[],
                system_health_score=105.0
            )
