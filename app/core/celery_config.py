"""
Production-grade Celery configuration for Culture Connect Backend API.

This module provides comprehensive Celery configuration including:
- Multi-queue setup with priority-based routing
- Redis broker configuration with failover
- Worker management and monitoring
- Task routing and retry policies
- Performance optimization settings

Implements Task 6.2.1 requirements for Celery task queue setup with
production-grade reliability and performance.
"""

import os
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any

from celery import Celery
from kombu import Queue, Exchange

from app.core.config import settings


class CeleryConfig:
    """Production Celery configuration."""
    
    # Broker Configuration
    broker_url = settings.REDIS_URL
    result_backend = settings.REDIS_URL
    
    # Broker Connection Settings
    broker_connection_retry_on_startup = True
    broker_connection_retry = True
    broker_connection_max_retries = 10
    broker_connection_retry_delay = 5.0
    
    # Redis Specific Settings
    redis_max_connections = 20
    redis_retry_on_timeout = True
    redis_socket_timeout = 30.0
    redis_socket_connect_timeout = 30.0
    
    # Task Serialization
    task_serializer = 'json'
    result_serializer = 'json'
    accept_content = ['json']
    
    # Task Execution Settings
    task_always_eager = False  # Set to True for testing
    task_eager_propagates = True
    task_ignore_result = False
    task_store_eager_result = True
    
    # Task Time Limits
    task_time_limit = 1800  # 30 minutes hard limit
    task_soft_time_limit = 1500  # 25 minutes soft limit
    
    # Worker Settings
    worker_prefetch_multiplier = 1  # Disable prefetching for fair distribution
    worker_max_tasks_per_child = 1000  # Restart worker after 1000 tasks
    worker_max_memory_per_child = 200000  # 200MB memory limit per worker
    worker_disable_rate_limits = False
    
    # Task Result Settings
    result_expires = 3600  # Results expire after 1 hour
    result_persistent = True
    result_compression = 'gzip'
    
    # Task Routing and Queues
    task_default_queue = 'default'
    task_default_exchange = 'default'
    task_default_exchange_type = 'direct'
    task_default_routing_key = 'default'
    
    # Define exchanges
    task_exchanges = (
        Exchange('default', type='direct'),
        Exchange('priority', type='direct'),
        Exchange('email', type='direct'),
        Exchange('notification', type='direct'),
        Exchange('report', type='direct'),
        Exchange('sync', type='direct'),
        Exchange('cleanup', type='direct'),
        Exchange('analytics', type='direct'),
    )
    
    # Define queues with priorities
    task_queues = (
        # Critical priority queue
        Queue('critical', Exchange('priority'), routing_key='critical',
              queue_arguments={'x-max-priority': 10}),
        
        # High priority queue
        Queue('high_priority', Exchange('priority'), routing_key='high',
              queue_arguments={'x-max-priority': 8}),
        
        # Standard/default queue
        Queue('default', Exchange('default'), routing_key='default',
              queue_arguments={'x-max-priority': 5}),
        
        # Low priority queue
        Queue('low_priority', Exchange('priority'), routing_key='low',
              queue_arguments={'x-max-priority': 2}),
        
        # Specialized queues
        Queue('email', Exchange('email'), routing_key='email',
              queue_arguments={'x-max-priority': 7}),
        
        Queue('notification', Exchange('notification'), routing_key='notification',
              queue_arguments={'x-max-priority': 6}),
        
        Queue('report', Exchange('report'), routing_key='report',
              queue_arguments={'x-max-priority': 4}),
        
        Queue('sync', Exchange('sync'), routing_key='sync',
              queue_arguments={'x-max-priority': 8}),
        
        Queue('cleanup', Exchange('cleanup'), routing_key='cleanup',
              queue_arguments={'x-max-priority': 1}),
        
        Queue('analytics', Exchange('analytics'), routing_key='analytics',
              queue_arguments={'x-max-priority': 3}),
    )
    
    # Task routing rules
    task_routes = {
        # Email tasks
        'email.send_verification_email': {'queue': 'email', 'routing_key': 'email'},
        'email.send_password_reset': {'queue': 'email', 'routing_key': 'email'},
        'email.send_booking_confirmation': {'queue': 'email', 'routing_key': 'email'},
        'email.send_notification_digest': {'queue': 'email', 'routing_key': 'email'},
        
        # Notification tasks
        'notification.send_push_notification': {'queue': 'notification', 'routing_key': 'notification'},
        'notification.send_sms_notification': {'queue': 'notification', 'routing_key': 'notification'},
        'notification.process_notification_batch': {'queue': 'notification', 'routing_key': 'notification'},
        
        # Report generation tasks
        'report.generate_vendor_report': {'queue': 'report', 'routing_key': 'report'},
        'report.generate_analytics_report': {'queue': 'report', 'routing_key': 'report'},
        'report.export_booking_data': {'queue': 'report', 'routing_key': 'report'},
        
        # Sync tasks
        'sync.process_data_sync': {'queue': 'sync', 'routing_key': 'sync'},
        'sync.resolve_sync_conflict': {'queue': 'sync', 'routing_key': 'sync'},
        'sync.batch_sync_operation': {'queue': 'sync', 'routing_key': 'sync'},
        
        # Cleanup tasks
        'cleanup.cleanup_expired_sessions': {'queue': 'cleanup', 'routing_key': 'cleanup'},
        'cleanup.cleanup_old_logs': {'queue': 'cleanup', 'routing_key': 'cleanup'},
        'cleanup.cleanup_temp_files': {'queue': 'cleanup', 'routing_key': 'cleanup'},
        
        # Analytics tasks
        'analytics.calculate_metrics': {'queue': 'analytics', 'routing_key': 'analytics'},
        'analytics.update_dashboards': {'queue': 'analytics', 'routing_key': 'analytics'},
        'analytics.process_events': {'queue': 'analytics', 'routing_key': 'analytics'},
        
        # Critical tasks (admin operations, payments, etc.)
        'payment.process_payment': {'queue': 'critical', 'routing_key': 'critical'},
        'admin.force_sync': {'queue': 'critical', 'routing_key': 'critical'},
        'security.fraud_detection': {'queue': 'critical', 'routing_key': 'critical'},
    }
    
    # Task retry policies
    task_annotations = {
        # Email tasks - retry with exponential backoff
        'email.*': {
            'rate_limit': '100/m',  # 100 emails per minute
            'max_retries': 5,
            'retry_backoff': True,
            'retry_backoff_max': 600,  # 10 minutes max
            'retry_jitter': True,
        },
        
        # Notification tasks
        'notification.*': {
            'rate_limit': '200/m',  # 200 notifications per minute
            'max_retries': 3,
            'retry_backoff': True,
            'retry_backoff_max': 300,  # 5 minutes max
        },
        
        # Report tasks - longer timeout, fewer retries
        'report.*': {
            'rate_limit': '10/m',  # 10 reports per minute
            'max_retries': 2,
            'time_limit': 3600,  # 1 hour
            'soft_time_limit': 3300,  # 55 minutes
        },
        
        # Sync tasks - high priority, quick retries
        'sync.*': {
            'rate_limit': '500/m',  # 500 sync operations per minute
            'max_retries': 5,
            'retry_backoff': True,
            'retry_backoff_max': 120,  # 2 minutes max
        },
        
        # Cleanup tasks - low priority, can be delayed
        'cleanup.*': {
            'rate_limit': '5/m',  # 5 cleanup tasks per minute
            'max_retries': 2,
            'retry_backoff': True,
        },
        
        # Analytics tasks
        'analytics.*': {
            'rate_limit': '50/m',  # 50 analytics tasks per minute
            'max_retries': 3,
            'time_limit': 1800,  # 30 minutes
        },
        
        # Critical tasks - immediate execution, minimal retries
        'payment.*': {
            'rate_limit': '1000/m',  # High throughput for payments
            'max_retries': 3,
            'retry_backoff': False,  # Immediate retries
        },
    }
    
    # Monitoring and Logging
    worker_send_task_events = True
    task_send_sent_event = True
    worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
    worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'
    
    # Security Settings
    worker_hijack_root_logger = False
    worker_log_color = False
    
    # Beat Schedule (for periodic tasks)
    beat_schedule = {
        # Cleanup tasks
        'cleanup-expired-sessions': {
            'task': 'cleanup.cleanup_expired_sessions',
            'schedule': timedelta(hours=1),  # Every hour
            'options': {'queue': 'cleanup'}
        },
        
        'cleanup-old-logs': {
            'task': 'cleanup.cleanup_old_logs',
            'schedule': timedelta(days=1),  # Daily
            'options': {'queue': 'cleanup'}
        },
        
        # Analytics tasks
        'update-analytics-dashboards': {
            'task': 'analytics.update_dashboards',
            'schedule': timedelta(minutes=15),  # Every 15 minutes
            'options': {'queue': 'analytics'}
        },
        
        'calculate-daily-metrics': {
            'task': 'analytics.calculate_metrics',
            'schedule': timedelta(hours=6),  # Every 6 hours
            'options': {'queue': 'analytics'}
        },
        
        # Sync verification tasks
        'verify-sync-integrity': {
            'task': 'sync.verify_sync_integrity',
            'schedule': timedelta(hours=2),  # Every 2 hours
            'options': {'queue': 'sync'}
        },
        
        # Email digest tasks
        'send-daily-digest': {
            'task': 'email.send_notification_digest',
            'schedule': timedelta(days=1),  # Daily
            'options': {'queue': 'email'}
        },
    }
    
    # Timezone
    timezone = 'UTC'
    enable_utc = True


def get_celery_app() -> Celery:
    """
    Create and configure Celery application.
    
    Returns:
        Configured Celery application instance
    """
    app = Celery('culture_connect_tasks')
    app.config_from_object(CeleryConfig)
    
    # Auto-discover tasks from all installed apps
    app.autodiscover_tasks([
        'app.tasks.email_tasks',
        'app.tasks.notification_tasks',
        'app.tasks.report_tasks',
        'app.tasks.sync_tasks',
        'app.tasks.cleanup_tasks',
        'app.tasks.analytics_tasks',
        'app.tasks.payment_tasks',
    ])
    
    return app


def get_celery_config() -> Dict[str, Any]:
    """
    Get Celery configuration as dictionary.
    
    Returns:
        Celery configuration dictionary
    """
    config = CeleryConfig()
    return {
        key: getattr(config, key)
        for key in dir(config)
        if not key.startswith('_')
    }


# Create global Celery app instance
celery_app = get_celery_app()


# Health check function
def check_celery_health() -> Dict[str, Any]:
    """
    Check Celery broker and worker health.
    
    Returns:
        Health status information
    """
    try:
        # Check broker connection
        inspect = celery_app.control.inspect()
        
        # Get worker stats
        stats = inspect.stats()
        active_workers = len(stats) if stats else 0
        
        # Get queue lengths
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        reserved_tasks = inspect.reserved()
        
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())
        total_reserved = sum(len(tasks) for tasks in (reserved_tasks or {}).values())
        
        return {
            "status": "healthy",
            "broker_connected": True,
            "active_workers": active_workers,
            "total_active_tasks": total_active,
            "total_scheduled_tasks": total_scheduled,
            "total_reserved_tasks": total_reserved,
            "queues": list(CeleryConfig.task_routes.keys())
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "broker_connected": False
        }
