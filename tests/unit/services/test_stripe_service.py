"""
Unit tests for Stripe service integration.

This module provides comprehensive tests for Stripe payment processing including:
- Payment Intent creation and confirmation
- Multi-currency support (USD/EUR/GBP)
- Webhook signature validation and event processing
- Error handling and retry logic
- Performance and security testing

Implements Task 4.3.2 Phase 4 requirements for comprehensive testing with
>80% test coverage and 100% test success rate validation.
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

import stripe

from app.services.payment.stripe_service import StripeService, StripeError
from app.schemas.payment_schemas import WebhookProcessingResult


class TestStripeService:
    """Test suite for StripeService."""

    @pytest.fixture
    def stripe_service(self):
        """Create StripeService instance for testing."""
        with patch('app.services.payment.stripe_service.PaymentProviderSettings') as mock_settings:
            mock_settings.get_provider_config.return_value = {
                "secret_key": "sk_test_123456789",
                "public_key": "pk_test_123456789",
                "webhook_secret": "whsec_test_secret"
            }
            
            service = StripeService()
            return service

    @pytest.fixture
    def sample_payment_data(self):
        """Sample payment data for testing."""
        return {
            "amount": Decimal("100.00"),
            "currency": "USD",
            "customer_email": "<EMAIL>",
            "metadata": {"booking_id": 123}
        }

    @pytest.fixture
    def sample_payment_intent(self):
        """Sample Stripe Payment Intent for testing."""
        return {
            "id": "pi_test_123456789",
            "client_secret": "pi_test_123456789_secret_test",
            "amount": 10000,  # Amount in cents
            "currency": "usd",
            "status": "requires_payment_method",
            "created": **********,
            "metadata": {"booking_id": "123"},
            "charges": {
                "data": [
                    {
                        "id": "ch_test_123456789",
                        "status": "succeeded",
                        "amount": 10000,
                        "payment_method": "pm_test_123456789"
                    }
                ]
            }
        }

    @pytest.fixture
    def sample_webhook_event(self):
        """Sample Stripe webhook event for testing."""
        return {
            "id": "evt_test_123456789",
            "type": "payment_intent.succeeded",
            "data": {
                "object": {
                    "id": "pi_test_123456789",
                    "status": "succeeded",
                    "amount": 10000,
                    "currency": "usd",
                    "metadata": {"booking_id": "123"}
                }
            }
        }

    @pytest.mark.asyncio
    async def test_create_payment_intent_success(self, stripe_service, sample_payment_data):
        """Test successful Payment Intent creation."""
        # Mock Stripe PaymentIntent.create
        mock_payment_intent = MagicMock()
        mock_payment_intent.id = "pi_test_123456789"
        mock_payment_intent.client_secret = "pi_test_123456789_secret_test"
        mock_payment_intent.amount = 10000
        mock_payment_intent.currency = "usd"
        mock_payment_intent.status = "requires_payment_method"
        mock_payment_intent.created = **********
        mock_payment_intent.metadata = {"booking_id": "123"}

        with patch('stripe.PaymentIntent.create', return_value=mock_payment_intent):
            result = await stripe_service.create_payment_intent(**sample_payment_data)

            # Assertions
            assert result["payment_intent_id"] == "pi_test_123456789"
            assert result["client_secret"] == "pi_test_123456789_secret_test"
            assert result["amount"] == 10000
            assert result["currency"] == "USD"
            assert result["status"] == "requires_payment_method"

            # Verify Stripe API call
            stripe.PaymentIntent.create.assert_called_once_with(
                amount=10000,  # Converted to cents
                currency="usd",
                payment_method_types=["card"],
                metadata={"booking_id": 123},
                receipt_email="<EMAIL>",
                automatic_payment_methods={
                    "enabled": True,
                    "allow_redirects": "never"
                }
            )

    @pytest.mark.asyncio
    async def test_create_payment_intent_stripe_error(self, stripe_service, sample_payment_data):
        """Test Payment Intent creation with Stripe error."""
        # Mock Stripe error
        stripe_error = stripe.error.InvalidRequestError(
            "Invalid currency", 
            param="currency"
        )
        
        with patch('stripe.PaymentIntent.create', side_effect=stripe_error):
            with pytest.raises(StripeError) as exc_info:
                await stripe_service.create_payment_intent(**sample_payment_data)

            assert "Payment Intent creation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_confirm_payment_intent_success(self, stripe_service):
        """Test successful Payment Intent confirmation."""
        # Mock Stripe PaymentIntent.confirm
        mock_payment_intent = MagicMock()
        mock_payment_intent.id = "pi_test_123456789"
        mock_payment_intent.status = "succeeded"
        mock_payment_intent.amount = 10000
        mock_payment_intent.currency = "usd"
        mock_payment_intent.charges.data = [
            MagicMock(
                id="ch_test_123456789",
                status="succeeded",
                amount=10000,
                payment_method="pm_test_123456789"
            )
        ]

        with patch('stripe.PaymentIntent.confirm', return_value=mock_payment_intent):
            result = await stripe_service.confirm_payment_intent(
                payment_intent_id="pi_test_123456789",
                payment_method_id="pm_test_123456789"
            )

            # Assertions
            assert result["payment_intent_id"] == "pi_test_123456789"
            assert result["status"] == "succeeded"
            assert result["amount"] == 10000
            assert result["currency"] == "USD"
            assert len(result["charges"]) == 1

            # Verify Stripe API call
            stripe.PaymentIntent.confirm.assert_called_once_with(
                "pi_test_123456789",
                payment_method="pm_test_123456789"
            )

    @pytest.mark.asyncio
    async def test_retrieve_payment_intent_success(self, stripe_service, sample_payment_intent):
        """Test successful Payment Intent retrieval."""
        # Mock Stripe PaymentIntent.retrieve
        mock_payment_intent = MagicMock()
        for key, value in sample_payment_intent.items():
            if key == "charges":
                mock_charges = MagicMock()
                mock_charges.data = [MagicMock(**charge) for charge in value["data"]]
                setattr(mock_payment_intent, key, mock_charges)
            else:
                setattr(mock_payment_intent, key, value)

        with patch('stripe.PaymentIntent.retrieve', return_value=mock_payment_intent):
            result = await stripe_service.retrieve_payment_intent("pi_test_123456789")

            # Assertions
            assert result["payment_intent_id"] == "pi_test_123456789"
            assert result["status"] == "requires_payment_method"
            assert result["amount"] == 10000
            assert result["currency"] == "USD"

            # Verify Stripe API call
            stripe.PaymentIntent.retrieve.assert_called_once_with("pi_test_123456789")

    def test_validate_webhook_signature_valid(self, stripe_service):
        """Test webhook signature validation with valid signature."""
        payload = b'{"type": "payment_intent.succeeded", "data": {"object": {"id": "pi_test"}}}'
        signature = "valid_signature"

        # Mock Stripe webhook validation
        with patch('stripe.Webhook.construct_event', return_value={"type": "payment_intent.succeeded"}):
            is_valid = stripe_service.validate_webhook_signature(payload, signature)
            assert is_valid is True

    def test_validate_webhook_signature_invalid(self, stripe_service):
        """Test webhook signature validation with invalid signature."""
        payload = b'{"type": "payment_intent.succeeded", "data": {"object": {"id": "pi_test"}}}'
        signature = "invalid_signature"

        # Mock Stripe signature verification error
        with patch('stripe.Webhook.construct_event', side_effect=stripe.error.SignatureVerificationError("Invalid signature", signature)):
            is_valid = stripe_service.validate_webhook_signature(payload, signature)
            assert is_valid is False

    @pytest.mark.asyncio
    async def test_process_webhook_event_payment_intent_succeeded(self, stripe_service, sample_webhook_event):
        """Test processing payment_intent.succeeded webhook event."""
        payload = json.dumps(sample_webhook_event).encode('utf-8')
        signature = "valid_signature"

        # Mock Stripe webhook validation
        with patch('stripe.Webhook.construct_event', return_value=sample_webhook_event):
            result = await stripe_service.process_webhook_event(payload, signature)

            # Assertions
            assert isinstance(result, WebhookProcessingResult)
            assert result.processed is True
            assert result.event_type == "payment_intent.succeeded"
            assert result.message == "Payment completed successfully"

    @pytest.mark.asyncio
    async def test_process_webhook_event_invalid_signature(self, stripe_service, sample_webhook_event):
        """Test webhook processing with invalid signature."""
        payload = json.dumps(sample_webhook_event).encode('utf-8')
        signature = "invalid_signature"

        # Mock Stripe signature verification error
        with patch('stripe.Webhook.construct_event', side_effect=stripe.error.SignatureVerificationError("Invalid signature", signature)):
            result = await stripe_service.process_webhook_event(payload, signature)

            # Assertions
            assert result.processed is False
            assert "Processing failed" in result.message

    @pytest.mark.asyncio
    async def test_create_refund_success(self, stripe_service):
        """Test successful refund creation."""
        # Mock Stripe Refund.create
        mock_refund = MagicMock()
        mock_refund.id = "re_test_123456789"
        mock_refund.amount = 5000  # Partial refund
        mock_refund.currency = "usd"
        mock_refund.status = "succeeded"
        mock_refund.reason = "requested_by_customer"
        mock_refund.created = **********
        mock_refund.metadata = {"reason": "customer_request"}

        with patch('stripe.Refund.create', return_value=mock_refund):
            result = await stripe_service.create_refund(
                payment_intent_id="pi_test_123456789",
                amount=Decimal("50.00"),
                reason="requested_by_customer",
                metadata={"reason": "customer_request"}
            )

            # Assertions
            assert result["refund_id"] == "re_test_123456789"
            assert result["amount"] == 5000
            assert result["currency"] == "USD"
            assert result["status"] == "succeeded"
            assert result["reason"] == "requested_by_customer"

            # Verify Stripe API call
            stripe.Refund.create.assert_called_once_with(
                payment_intent="pi_test_123456789",
                amount=5000,  # Converted to cents
                reason="requested_by_customer",
                metadata={"reason": "customer_request"}
            )

    @pytest.mark.asyncio
    async def test_multi_currency_support(self, stripe_service):
        """Test multi-currency payment support."""
        currencies = ["USD", "EUR", "GBP"]
        
        for currency in currencies:
            # Mock Stripe PaymentIntent.create
            mock_payment_intent = MagicMock()
            mock_payment_intent.id = f"pi_test_{currency.lower()}"
            mock_payment_intent.currency = currency.lower()
            mock_payment_intent.amount = 10000
            mock_payment_intent.client_secret = f"pi_test_{currency.lower()}_secret"
            mock_payment_intent.status = "requires_payment_method"
            mock_payment_intent.created = **********
            mock_payment_intent.metadata = {}

            with patch('stripe.PaymentIntent.create', return_value=mock_payment_intent):
                result = await stripe_service.create_payment_intent(
                    amount=Decimal("100.00"),
                    currency=currency,
                    customer_email="<EMAIL>"
                )

                # Assertions
                assert result["currency"] == currency
                assert result["payment_intent_id"] == f"pi_test_{currency.lower()}"

    def test_stripe_error_exception(self):
        """Test StripeError exception."""
        error_message = "Test Stripe error message"
        error = StripeError(error_message)
        assert str(error) == error_message
