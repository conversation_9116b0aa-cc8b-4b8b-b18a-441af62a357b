"""
Scaling repositories for Culture Connect Backend API.

This module provides comprehensive data access layer for scaling and load balancing:
- ScalingMetricsRepository: Real-time scaling metrics data access
- AutoScalingPolicyRepository: Auto-scaling policy management
- LoadBalancerConfigRepository: Load balancer configuration management
- ContainerMetricsRepository: Container performance data access
- ScalingEventRepository: Scaling event tracking and audit trail

Implements Phase 7.3.3 requirements with production-grade repository patterns,
comprehensive caching, and seamless integration with Phase 7.2 performance
monitoring and Phase 7.3.2 caching systems.
"""

from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, Optional, List
from uuid import UUID

from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.scaling_models import (
    ScalingMetrics, AutoScalingPolicy, LoadBalancerConfig,
    ContainerMetrics, ScalingEvent, ScalingTriggerType,
    ScalingDirection, LoadBalancerStrategy, ContainerStatus,
    ScalingEventType
)
from app.repositories.enhanced_base import EnhancedBaseRepository
import logging

logger = logging.getLogger(__name__)


class ScalingMetricsRepository(EnhancedBaseRepository[ScalingMetrics]):
    """
    Repository for scaling metrics data access.

    Provides high-performance data access for real-time scaling metrics
    with Redis caching and optimized queries for scaling decisions.
    """

    def __init__(self, db: AsyncSession, cache_manager=None):
        super().__init__(ScalingMetrics, db, cache_manager)
        self.cache_ttl = 300  # 5 minutes for metrics data

    async def create_metric(
        self,
        metric_name: str,
        metric_type: str,
        component: str,
        current_value: Decimal,
        **kwargs
    ) -> ScalingMetrics:
        """
        Create a new scaling metric record.

        Args:
            metric_name: Name of the metric
            metric_type: Type of metric (cpu, memory, etc.)
            component: Component being measured
            current_value: Current metric value
            **kwargs: Additional metric data

        Returns:
            Created scaling metric
        """
        try:
            metric_data = {
                "metric_name": metric_name,
                "metric_type": metric_type,
                "component": component,
                "current_value": current_value,
                "recorded_at": datetime.now(timezone.utc),
                **kwargs
            }

            metric = await self.create(metric_data)

            # Invalidate component metrics cache
            await self._invalidate_component_cache(component)

            logger.info(f"Created scaling metric: {metric_name} for {component}")
            return metric

        except Exception as e:
            logger.error(f"Error creating scaling metric: {str(e)}")
            raise

    async def get_latest_metrics_by_component(
        self,
        component: str,
        metric_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[ScalingMetrics]:
        """
        Get latest metrics for a component.

        Args:
            component: Component name
            metric_types: Optional list of metric types to filter
            limit: Maximum number of metrics to return

        Returns:
            List of latest scaling metrics
        """
        cache_key = f"scaling_metrics:component:{component}:latest"
        if metric_types:
            cache_key += f":types:{':'.join(sorted(metric_types))}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(ScalingMetrics).where(
                ScalingMetrics.component == component
            )

            if metric_types:
                query = query.where(ScalingMetrics.metric_type.in_(metric_types))

            query = query.order_by(desc(ScalingMetrics.recorded_at)).limit(limit)

            result = await self.db.execute(query)
            metrics = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, metrics, self.cache_ttl)

            return metrics

        except Exception as e:
            logger.error(f"Error getting latest metrics for component {component}: {str(e)}")
            raise

    async def get_metrics_time_series(
        self,
        component: str,
        metric_name: str,
        start_time: datetime,
        end_time: datetime,
        interval_minutes: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get time-series metrics data for scaling analysis.

        Args:
            component: Component name
            metric_name: Metric name
            start_time: Start time for data
            end_time: End time for data
            interval_minutes: Aggregation interval in minutes

        Returns:
            List of time-series data points
        """
        try:
            # Use PostgreSQL window functions for time-series aggregation
            query = select(
                func.date_trunc('minute', ScalingMetrics.recorded_at).label('time_bucket'),
                func.avg(ScalingMetrics.current_value).label('avg_value'),
                func.max(ScalingMetrics.current_value).label('max_value'),
                func.min(ScalingMetrics.current_value).label('min_value'),
                func.count().label('sample_count')
            ).where(
                and_(
                    ScalingMetrics.component == component,
                    ScalingMetrics.metric_name == metric_name,
                    ScalingMetrics.recorded_at >= start_time,
                    ScalingMetrics.recorded_at <= end_time
                )
            ).group_by(
                func.date_trunc('minute', ScalingMetrics.recorded_at)
            ).order_by(
                func.date_trunc('minute', ScalingMetrics.recorded_at)
            )

            result = await self.db.execute(query)
            rows = result.fetchall()

            return [
                {
                    "timestamp": row.time_bucket,
                    "avg_value": float(row.avg_value) if row.avg_value else 0,
                    "max_value": float(row.max_value) if row.max_value else 0,
                    "min_value": float(row.min_value) if row.min_value else 0,
                    "sample_count": row.sample_count
                }
                for row in rows
            ]

        except Exception as e:
            logger.error(f"Error getting time-series metrics: {str(e)}")
            raise

    async def get_current_utilization_summary(
        self,
        components: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get current utilization summary for scaling decisions.

        Args:
            components: Optional list of components to include

        Returns:
            Dictionary of component utilization data
        """
        cache_key = "scaling_metrics:utilization_summary"
        if components:
            cache_key += f":components:{':'.join(sorted(components))}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            # Get latest metrics for each component
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=10)

            query = select(
                ScalingMetrics.component,
                ScalingMetrics.metric_type,
                func.avg(ScalingMetrics.current_value).label('avg_value'),
                func.max(ScalingMetrics.current_value).label('max_value'),
                func.avg(ScalingMetrics.cpu_utilization).label('avg_cpu'),
                func.avg(ScalingMetrics.memory_utilization).label('avg_memory'),
                func.avg(ScalingMetrics.request_rate).label('avg_request_rate'),
                func.avg(ScalingMetrics.response_time_ms).label('avg_response_time'),
                func.avg(ScalingMetrics.error_rate).label('avg_error_rate')
            ).where(
                ScalingMetrics.recorded_at >= cutoff_time
            )

            if components:
                query = query.where(ScalingMetrics.component.in_(components))

            query = query.group_by(
                ScalingMetrics.component,
                ScalingMetrics.metric_type
            )

            result = await self.db.execute(query)
            rows = result.fetchall()

            # Organize data by component
            summary = {}
            for row in rows:
                component = row.component
                if component not in summary:
                    summary[component] = {
                        "cpu_utilization": 0,
                        "memory_utilization": 0,
                        "request_rate": 0,
                        "response_time_ms": 0,
                        "error_rate": 0,
                        "metrics": {}
                    }

                summary[component]["metrics"][row.metric_type] = {
                    "avg_value": float(row.avg_value) if row.avg_value else 0,
                    "max_value": float(row.max_value) if row.max_value else 0
                }

                # Update component-level averages
                if row.avg_cpu:
                    summary[component]["cpu_utilization"] = float(row.avg_cpu)
                if row.avg_memory:
                    summary[component]["memory_utilization"] = float(row.avg_memory)
                if row.avg_request_rate:
                    summary[component]["request_rate"] = int(row.avg_request_rate)
                if row.avg_response_time:
                    summary[component]["response_time_ms"] = int(row.avg_response_time)
                if row.avg_error_rate:
                    summary[component]["error_rate"] = float(row.avg_error_rate)

            # Cache the result
            await self._cache_result(cache_key, summary, 60)  # 1 minute cache

            return summary

        except Exception as e:
            logger.error(f"Error getting utilization summary: {str(e)}")
            raise

    async def cleanup_old_metrics(self, retention_days: int = 30) -> int:
        """
        Clean up old metrics data.

        Args:
            retention_days: Number of days to retain metrics

        Returns:
            Number of deleted records
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

            deleted_count = await self.delete_by_criteria({
                "recorded_at__lt": cutoff_date
            })

            logger.info(f"Cleaned up {deleted_count} old scaling metrics")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old metrics: {str(e)}")
            raise

    async def _invalidate_component_cache(self, component: str):
        """Invalidate cache for component metrics."""
        if self.cache_manager:
            cache_patterns = [
                f"scaling_metrics:component:{component}:*",
                "scaling_metrics:utilization_summary*"
            ]
            for pattern in cache_patterns:
                await self.cache_manager.delete_pattern(pattern)


class AutoScalingPolicyRepository(EnhancedBaseRepository[AutoScalingPolicy]):
    """
    Repository for auto-scaling policy management.

    Provides data access for auto-scaling policies with caching
    and optimized queries for scaling decisions.
    """

    def __init__(self, db: AsyncSession, cache_manager=None):
        super().__init__(AutoScalingPolicy, db, cache_manager)
        self.cache_ttl = 3600  # 1 hour for policy data

    async def get_active_policies(self) -> List[AutoScalingPolicy]:
        """
        Get all active auto-scaling policies.

        Returns:
            List of active auto-scaling policies
        """
        cache_key = "auto_scaling_policies:active"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(AutoScalingPolicy).where(
                AutoScalingPolicy.is_enabled == True
            ).options(
                selectinload(AutoScalingPolicy.scaling_events)
            ).order_by(AutoScalingPolicy.component)

            result = await self.db.execute(query)
            policies = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, policies, self.cache_ttl)

            return policies

        except Exception as e:
            logger.error(f"Error getting active policies: {str(e)}")
            raise

    async def get_policies_by_component(self, component: str) -> List[AutoScalingPolicy]:
        """
        Get auto-scaling policies for a specific component.

        Args:
            component: Component name

        Returns:
            List of auto-scaling policies for the component
        """
        cache_key = f"auto_scaling_policies:component:{component}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(AutoScalingPolicy).where(
                and_(
                    AutoScalingPolicy.component == component,
                    AutoScalingPolicy.is_enabled == True
                )
            ).options(
                selectinload(AutoScalingPolicy.scaling_events)
            )

            result = await self.db.execute(query)
            policies = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, policies, self.cache_ttl)

            return policies

        except Exception as e:
            logger.error(f"Error getting policies for component {component}: {str(e)}")
            raise

    async def update_last_scaling_event(
        self,
        policy_id: UUID,
        event_time: datetime
    ) -> AutoScalingPolicy:
        """
        Update the last scaling event time for a policy.

        Args:
            policy_id: Policy ID
            event_time: Time of the scaling event

        Returns:
            Updated auto-scaling policy
        """
        try:
            policy = await self.update(policy_id, {
                "last_scaling_event": event_time,
                "updated_at": datetime.now(timezone.utc)
            })

            # Invalidate cache
            await self._invalidate_policy_cache(policy.component)

            return policy

        except Exception as e:
            logger.error(f"Error updating last scaling event: {str(e)}")
            raise

    async def _invalidate_policy_cache(self, component: str):
        """Invalidate cache for policy data."""
        if self.cache_manager:
            cache_patterns = [
                "auto_scaling_policies:active",
                f"auto_scaling_policies:component:{component}"
            ]
            for pattern in cache_patterns:
                await self.cache_manager.delete_pattern(pattern)


class LoadBalancerConfigRepository(EnhancedBaseRepository[LoadBalancerConfig]):
    """
    Repository for load balancer configuration management.

    Provides data access for load balancer configurations with caching
    and optimized queries for traffic distribution.
    """

    def __init__(self, db: AsyncSession, cache_manager=None):
        super().__init__(LoadBalancerConfig, db, cache_manager)
        self.cache_ttl = 1800  # 30 minutes for config data

    async def get_active_configs(self) -> List[LoadBalancerConfig]:
        """
        Get all active load balancer configurations.

        Returns:
            List of active load balancer configurations
        """
        cache_key = "load_balancer_configs:active"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(LoadBalancerConfig).where(
                LoadBalancerConfig.is_enabled == True
            ).order_by(LoadBalancerConfig.service_name)

            result = await self.db.execute(query)
            configs = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, configs, self.cache_ttl)

            return configs

        except Exception as e:
            logger.error(f"Error getting active load balancer configs: {str(e)}")
            raise

    async def get_config_by_service(self, service_name: str) -> Optional[LoadBalancerConfig]:
        """
        Get load balancer configuration for a specific service.

        Args:
            service_name: Service name

        Returns:
            Load balancer configuration or None
        """
        cache_key = f"load_balancer_configs:service:{service_name}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(LoadBalancerConfig).where(
                and_(
                    LoadBalancerConfig.service_name == service_name,
                    LoadBalancerConfig.is_enabled == True
                )
            )

            result = await self.db.execute(query)
            config = result.scalar_one_or_none()

            # Cache the result
            await self._cache_result(cache_key, config, self.cache_ttl)

            return config

        except Exception as e:
            logger.error(f"Error getting config for service {service_name}: {str(e)}")
            raise

    async def update_upstream_servers(
        self,
        config_id: UUID,
        upstream_servers: Dict[str, Any]
    ) -> LoadBalancerConfig:
        """
        Update upstream servers for a load balancer configuration.

        Args:
            config_id: Configuration ID
            upstream_servers: Updated upstream server configuration

        Returns:
            Updated load balancer configuration
        """
        try:
            config = await self.update(config_id, {
                "upstream_servers": upstream_servers,
                "updated_at": datetime.now(timezone.utc)
            })

            # Invalidate cache
            await self._invalidate_config_cache(config.service_name)

            return config

        except Exception as e:
            logger.error(f"Error updating upstream servers: {str(e)}")
            raise

    async def _invalidate_config_cache(self, service_name: str):
        """Invalidate cache for configuration data."""
        if self.cache_manager:
            cache_patterns = [
                "load_balancer_configs:active",
                f"load_balancer_configs:service:{service_name}"
            ]
            for pattern in cache_patterns:
                await self.cache_manager.delete_pattern(pattern)


class ContainerMetricsRepository(EnhancedBaseRepository[ContainerMetrics]):
    """
    Repository for container metrics data access.

    Provides high-performance data access for container performance metrics
    with Redis caching and optimized queries for scaling decisions.
    """

    def __init__(self, db: AsyncSession, cache_manager=None):
        super().__init__(ContainerMetrics, db, cache_manager)
        self.cache_ttl = 300  # 5 minutes for metrics data

    async def create_container_metric(
        self,
        container_id: str,
        pod_name: str,
        namespace: str,
        **kwargs
    ) -> ContainerMetrics:
        """
        Create a new container metric record.

        Args:
            container_id: Container ID
            pod_name: Pod name
            namespace: Kubernetes namespace
            **kwargs: Additional metric data

        Returns:
            Created container metric
        """
        try:
            metric_data = {
                "container_id": container_id,
                "pod_name": pod_name,
                "namespace": namespace,
                "recorded_at": datetime.now(timezone.utc),
                **kwargs
            }

            metric = await self.create(metric_data)

            # Invalidate namespace metrics cache
            await self._invalidate_namespace_cache(namespace)

            logger.info(f"Created container metric for pod: {pod_name}")
            return metric

        except Exception as e:
            logger.error(f"Error creating container metric: {str(e)}")
            raise

    async def get_latest_metrics_by_namespace(
        self,
        namespace: str,
        limit: int = 100
    ) -> List[ContainerMetrics]:
        """
        Get latest container metrics for a namespace.

        Args:
            namespace: Kubernetes namespace
            limit: Maximum number of metrics to return

        Returns:
            List of latest container metrics
        """
        cache_key = f"container_metrics:namespace:{namespace}:latest"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(ContainerMetrics).where(
                ContainerMetrics.namespace == namespace
            ).order_by(desc(ContainerMetrics.recorded_at)).limit(limit)

            result = await self.db.execute(query)
            metrics = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, metrics, self.cache_ttl)

            return metrics

        except Exception as e:
            logger.error(f"Error getting latest metrics for namespace {namespace}: {str(e)}")
            raise

    async def get_resource_utilization_summary(
        self,
        namespace: str,
        time_window_minutes: int = 10
    ) -> Dict[str, Any]:
        """
        Get resource utilization summary for scaling decisions.

        Args:
            namespace: Kubernetes namespace
            time_window_minutes: Time window for metrics aggregation

        Returns:
            Resource utilization summary
        """
        cache_key = f"container_metrics:utilization:{namespace}:{time_window_minutes}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=time_window_minutes)

            query = select(
                func.count(ContainerMetrics.id).label('container_count'),
                func.avg(ContainerMetrics.cpu_usage_cores).label('avg_cpu_usage'),
                func.max(ContainerMetrics.cpu_usage_cores).label('max_cpu_usage'),
                func.avg(ContainerMetrics.memory_usage_bytes).label('avg_memory_usage'),
                func.max(ContainerMetrics.memory_usage_bytes).label('max_memory_usage'),
                func.sum(ContainerMetrics.request_count).label('total_requests'),
                func.sum(ContainerMetrics.error_count).label('total_errors'),
                func.avg(ContainerMetrics.avg_response_time_ms).label('avg_response_time'),
                func.count(
                    func.nullif(ContainerMetrics.status == ContainerStatus.RUNNING, False)
                ).label('running_containers')
            ).where(
                and_(
                    ContainerMetrics.namespace == namespace,
                    ContainerMetrics.recorded_at >= cutoff_time
                )
            )

            result = await self.db.execute(query)
            row = result.fetchone()

            summary = {
                "namespace": namespace,
                "time_window_minutes": time_window_minutes,
                "container_count": row.container_count or 0,
                "running_containers": row.running_containers or 0,
                "cpu_utilization": {
                    "avg_cores": float(row.avg_cpu_usage) if row.avg_cpu_usage else 0,
                    "max_cores": float(row.max_cpu_usage) if row.max_cpu_usage else 0
                },
                "memory_utilization": {
                    "avg_bytes": int(row.avg_memory_usage) if row.avg_memory_usage else 0,
                    "max_bytes": int(row.max_memory_usage) if row.max_memory_usage else 0
                },
                "performance": {
                    "total_requests": row.total_requests or 0,
                    "total_errors": row.total_errors or 0,
                    "error_rate": (row.total_errors / row.total_requests) if row.total_requests else 0,
                    "avg_response_time_ms": int(row.avg_response_time) if row.avg_response_time else 0
                }
            }

            # Cache the result
            await self._cache_result(cache_key, summary, 60)  # 1 minute cache

            return summary

        except Exception as e:
            logger.error(f"Error getting utilization summary: {str(e)}")
            raise

    async def cleanup_old_metrics(self, retention_days: int = 7) -> int:
        """
        Clean up old container metrics data.

        Args:
            retention_days: Number of days to retain metrics

        Returns:
            Number of deleted records
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

            deleted_count = await self.delete_by_criteria({
                "recorded_at__lt": cutoff_date
            })

            logger.info(f"Cleaned up {deleted_count} old container metrics")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old container metrics: {str(e)}")
            raise

    async def _invalidate_namespace_cache(self, namespace: str):
        """Invalidate cache for namespace metrics."""
        if self.cache_manager:
            cache_patterns = [
                f"container_metrics:namespace:{namespace}:*",
                f"container_metrics:utilization:{namespace}:*"
            ]
            for pattern in cache_patterns:
                await self.cache_manager.delete_pattern(pattern)


class ScalingEventRepository(EnhancedBaseRepository[ScalingEvent]):
    """
    Repository for scaling event tracking and audit trail.

    Provides data access for scaling events with caching and
    optimized queries for audit trail and analysis.
    """

    def __init__(self, db: AsyncSession, cache_manager=None):
        super().__init__(ScalingEvent, db, cache_manager)
        self.cache_ttl = 1800  # 30 minutes for event data

    async def create_scaling_event(
        self,
        event_type: ScalingEventType,
        component: str,
        trigger_type: ScalingTriggerType,
        **kwargs
    ) -> ScalingEvent:
        """
        Create a new scaling event record.

        Args:
            event_type: Type of scaling event
            component: Component being scaled
            trigger_type: What triggered the scaling
            **kwargs: Additional event data

        Returns:
            Created scaling event
        """
        try:
            event_data = {
                "event_type": event_type,
                "component": component,
                "trigger_type": trigger_type,
                "triggered_at": datetime.now(timezone.utc),
                **kwargs
            }

            event = await self.create(event_data)

            # Invalidate component events cache
            await self._invalidate_component_cache(component)

            logger.info(f"Created scaling event: {event_type} for {component}")
            return event

        except Exception as e:
            logger.error(f"Error creating scaling event: {str(e)}")
            raise

    async def get_recent_events_by_component(
        self,
        component: str,
        limit: int = 50
    ) -> List[ScalingEvent]:
        """
        Get recent scaling events for a component.

        Args:
            component: Component name
            limit: Maximum number of events to return

        Returns:
            List of recent scaling events
        """
        cache_key = f"scaling_events:component:{component}:recent"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            query = select(ScalingEvent).where(
                ScalingEvent.component == component
            ).options(
                selectinload(ScalingEvent.policy)
            ).order_by(desc(ScalingEvent.triggered_at)).limit(limit)

            result = await self.db.execute(query)
            events = result.scalars().all()

            # Cache the result
            await self._cache_result(cache_key, events, self.cache_ttl)

            return events

        except Exception as e:
            logger.error(f"Error getting recent events for component {component}: {str(e)}")
            raise

    async def get_events_by_policy(
        self,
        policy_id: UUID,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[ScalingEvent]:
        """
        Get scaling events for a specific policy.

        Args:
            policy_id: Policy ID
            start_time: Optional start time filter
            end_time: Optional end time filter
            limit: Maximum number of events to return

        Returns:
            List of scaling events for the policy
        """
        try:
            query = select(ScalingEvent).where(
                ScalingEvent.policy_id == policy_id
            )

            if start_time:
                query = query.where(ScalingEvent.triggered_at >= start_time)
            if end_time:
                query = query.where(ScalingEvent.triggered_at <= end_time)

            query = query.order_by(desc(ScalingEvent.triggered_at)).limit(limit)

            result = await self.db.execute(query)
            events = result.scalars().all()

            return events

        except Exception as e:
            logger.error(f"Error getting events for policy {policy_id}: {str(e)}")
            raise

    async def get_scaling_statistics(
        self,
        component: Optional[str] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Get scaling statistics for analysis.

        Args:
            component: Optional component filter
            days: Number of days to analyze

        Returns:
            Scaling statistics
        """
        cache_key = f"scaling_events:stats:{component or 'all'}:{days}"

        # Try cache first
        cached_result = await self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            start_time = datetime.now(timezone.utc) - timedelta(days=days)

            query = select(
                ScalingEvent.event_type,
                ScalingEvent.scaling_direction,
                ScalingEvent.success,
                func.count().label('event_count'),
                func.avg(ScalingEvent.duration_seconds).label('avg_duration'),
                func.max(ScalingEvent.duration_seconds).label('max_duration'),
                func.min(ScalingEvent.duration_seconds).label('min_duration')
            ).where(
                ScalingEvent.triggered_at >= start_time
            )

            if component:
                query = query.where(ScalingEvent.component == component)

            query = query.group_by(
                ScalingEvent.event_type,
                ScalingEvent.scaling_direction,
                ScalingEvent.success
            )

            result = await self.db.execute(query)
            rows = result.fetchall()

            # Organize statistics
            stats = {
                "period_days": days,
                "component": component,
                "total_events": 0,
                "successful_events": 0,
                "failed_events": 0,
                "scale_up_events": 0,
                "scale_down_events": 0,
                "avg_duration_seconds": 0,
                "event_breakdown": []
            }

            total_duration = 0
            duration_count = 0

            for row in rows:
                event_count = row.event_count
                stats["total_events"] += event_count

                if row.success:
                    stats["successful_events"] += event_count
                else:
                    stats["failed_events"] += event_count

                if row.scaling_direction == ScalingDirection.SCALE_UP:
                    stats["scale_up_events"] += event_count
                elif row.scaling_direction == ScalingDirection.SCALE_DOWN:
                    stats["scale_down_events"] += event_count

                if row.avg_duration:
                    total_duration += float(row.avg_duration) * event_count
                    duration_count += event_count

                stats["event_breakdown"].append({
                    "event_type": row.event_type,
                    "scaling_direction": row.scaling_direction,
                    "success": row.success,
                    "count": event_count,
                    "avg_duration_seconds": float(row.avg_duration) if row.avg_duration else 0,
                    "max_duration_seconds": float(row.max_duration) if row.max_duration else 0,
                    "min_duration_seconds": float(row.min_duration) if row.min_duration else 0
                })

            if duration_count > 0:
                stats["avg_duration_seconds"] = total_duration / duration_count

            # Cache the result
            await self._cache_result(cache_key, stats, 3600)  # 1 hour cache

            return stats

        except Exception as e:
            logger.error(f"Error getting scaling statistics: {str(e)}")
            raise

    async def update_event_completion(
        self,
        event_id: UUID,
        success: bool,
        actual_replicas: Optional[int] = None,
        error_message: Optional[str] = None,
        duration_seconds: Optional[int] = None
    ) -> ScalingEvent:
        """
        Update scaling event with completion details.

        Args:
            event_id: Event ID
            success: Whether the scaling was successful
            actual_replicas: Actual number of replicas after scaling
            error_message: Error message if failed
            duration_seconds: Duration of scaling operation

        Returns:
            Updated scaling event
        """
        try:
            update_data = {
                "success": success,
                "completed_at": datetime.now(timezone.utc)
            }

            if actual_replicas is not None:
                update_data["actual_replicas"] = actual_replicas
            if error_message:
                update_data["error_message"] = error_message
            if duration_seconds is not None:
                update_data["duration_seconds"] = duration_seconds

            event = await self.update(event_id, update_data)

            # Invalidate component cache
            await self._invalidate_component_cache(event.component)

            return event

        except Exception as e:
            logger.error(f"Error updating event completion: {str(e)}")
            raise

    async def cleanup_old_events(self, retention_days: int = 90) -> int:
        """
        Clean up old scaling events.

        Args:
            retention_days: Number of days to retain events

        Returns:
            Number of deleted records
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

            deleted_count = await self.delete_by_criteria({
                "triggered_at__lt": cutoff_date
            })

            logger.info(f"Cleaned up {deleted_count} old scaling events")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old events: {str(e)}")
            raise

    async def _invalidate_component_cache(self, component: str):
        """Invalidate cache for component events."""
        if self.cache_manager:
            cache_patterns = [
                f"scaling_events:component:{component}:*",
                f"scaling_events:stats:{component}:*",
                "scaling_events:stats:all:*"
            ]
            for pattern in cache_patterns:
                await self.cache_manager.delete_pattern(pattern)
