"""
Geolocation API endpoints for Culture Connect Backend API.

This module provides comprehensive geolocation service endpoints including:
- IP-based location detection with MaxMind GeoIP database
- Payment provider selection based on geographic location
- VPN/Proxy detection with risk assessment
- Geolocation analytics and performance metrics
- A/B testing for routing strategies

Implements Phase 2.4.1 Enhanced OpenAPI Documentation requirements with
comprehensive examples and real-world geolocation routing scenarios.
"""

import logging
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db_session as get_db
from app.core.logging import correlation_id
from app.core.deps import get_current_user, get_current_user_optional
from app.models.user import User
from app.services.geolocation_service import get_geolocation_service
from app.services.vpn_detection_service import VPNDetectionService
from app.services.geolocation_analytics_service import GeolocationAnalyticsService
from app.services.ab_testing_service import ABTestingService
from app.schemas.geolocation_schemas import (
    GeolocationDetectionRequest,
    GeolocationDetectionResponse,
    PaymentProviderRecommendationResponse,
    GeolocationAnalyticsResponse,
    SupportedCountriesResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/detect",
    response_model=GeolocationDetectionResponse,
    summary="Detect location from IP address",
    description="""
    **Detect geographic location from IP address with comprehensive metadata.**

    This endpoint provides high-accuracy IP-based geolocation detection using the MaxMind GeoIP database
    with VPN/proxy detection, risk assessment, and payment provider recommendations.

    **Features:**
    - **MaxMind GeoIP Database**: >95% accuracy for country detection
    - **VPN/Proxy Detection**: Multi-method analysis with confidence scoring
    - **Risk Assessment**: Comprehensive risk scoring for anonymized traffic
    - **Provider Recommendations**: Intelligent payment provider selection
    - **Performance Optimized**: <100ms response time with Redis caching

    **Use Cases:**
    - Payment provider selection for checkout flows
    - Fraud detection and risk assessment
    - Geographic analytics and user segmentation
    - A/B testing for routing strategies

    **Performance Targets:**
    - Detection Time: <100ms (95th percentile)
    - Cache Hit Rate: >90% for repeated requests
    - Accuracy: >95% for country-level detection
    """,
    responses={
        200: {
            "description": "Location detected successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "nigerian_ip": {
                            "summary": "Nigerian IP Detection",
                            "description": "Location detection for Nigerian IP address with Paystack recommendation",
                            "value": {
                                "ip_address": "************",
                                "country_code": "NG",
                                "country_name": "Nigeria",
                                "continent_code": "AF",
                                "continent_name": "Africa",
                                "detection_method": "maxmind_geoip",
                                "confidence_score": 0.98,
                                "detection_time_ms": 45,
                                "vpn_detected": False,
                                "proxy_detected": False,
                                "risk_score": 0.1,
                                "recommended_provider": "paystack",
                                "provider_selection_reason": "Nigerian IP detected, optimal for Paystack processing",
                                "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                                "detected_at": "2024-01-15T10:30:00Z"
                            }
                        },
                        "us_ip_with_vpn": {
                            "summary": "US IP with VPN Detection",
                            "description": "Location detection for US IP with VPN usage detected",
                            "value": {
                                "ip_address": "*******",
                                "country_code": "US",
                                "country_name": "United States",
                                "continent_code": "NA",
                                "continent_name": "North America",
                                "detection_method": "maxmind_geoip",
                                "confidence_score": 0.75,
                                "detection_time_ms": 89,
                                "vpn_detected": True,
                                "vpn_provider": "ExpressVPN",
                                "vpn_confidence": 0.92,
                                "proxy_detected": False,
                                "risk_score": 0.6,
                                "risk_factors": ["vpn_usage", "datacenter_ip"],
                                "recommended_provider": "stripe",
                                "provider_selection_reason": "US IP with VPN detected, Stripe recommended for security",
                                "supported_currencies": ["USD", "EUR", "GBP", "CAD"],
                                "detected_at": "2024-01-15T10:35:00Z"
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid IP address format",
            "content": {
                "application/json": {
                    "example": {
                        "error": "INVALID_IP_ADDRESS",
                        "message": "Invalid IP address format provided",
                        "details": {
                            "ip_address": "invalid_ip",
                            "expected_format": "IPv4 or IPv6 address"
                        },
                        "timestamp": "2024-01-15T10:30:00Z",
                        "request_id": "req_123456789"
                    }
                }
            }
        },
        503: {
            "description": "Geolocation service unavailable",
            "content": {
                "application/json": {
                    "example": {
                        "error": "GEOLOCATION_SERVICE_UNAVAILABLE",
                        "message": "MaxMind GeoIP database temporarily unavailable",
                        "details": {
                            "service_status": "degraded",
                            "fallback_available": True,
                            "retry_after_seconds": 30
                        },
                        "timestamp": "2024-01-15T10:30:00Z",
                        "request_id": "req_123456789"
                    }
                }
            }
        }
    }
)
async def detect_location(
    request_data: GeolocationDetectionRequest,
    request: Request,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Detect geographic location from IP address with VPN detection and provider recommendations.

    **Performance Target**: <100ms response time
    """
    correlation = correlation_id.get()

    try:
        # Get client IP if not provided
        ip_address = request_data.ip_address
        if not ip_address:
            ip_address = request.client.host

        logger.info(
            f"Processing geolocation detection request for IP: {ip_address}",
            extra={
                "correlation_id": correlation,
                "ip_address": ip_address,
                "user_id": current_user.id if current_user else None,
                "include_vpn_detection": request_data.include_vpn_detection,
                "include_provider_recommendation": request_data.include_provider_recommendation
            }
        )

        # Initialize services
        geolocation_service = get_geolocation_service()

        # Perform basic geolocation detection
        geolocation_result = await geolocation_service.detect_country_from_ip(ip_address)

        if not geolocation_result:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "error": "GEOLOCATION_DETECTION_FAILED",
                    "message": "Unable to detect location for provided IP address",
                    "details": {
                        "ip_address": ip_address,
                        "service_status": "degraded"
                    }
                }
            )

        # Enhanced detection with VPN analysis if requested
        enhanced_result = geolocation_result
        if request_data.include_vpn_detection:
            vpn_service = VPNDetectionService()
            enhanced_result = await vpn_service.detect_vpn_proxy(ip_address, geolocation_result)

        # Provider recommendation if requested
        recommended_provider = None
        provider_selection_reason = None
        supported_currencies = []

        if request_data.include_provider_recommendation:
            from app.services.payment_service import PaymentService
            payment_service = PaymentService(db)

            # Get provider recommendation based on geolocation
            provider_info = await payment_service.get_recommended_provider(
                country_code=enhanced_result.country_code,
                currency="USD",  # Default currency for recommendation
                user_preferences=None,
                geolocation_result=enhanced_result
            )

            recommended_provider = provider_info.get("provider")
            provider_selection_reason = provider_info.get("reason")
            supported_currencies = provider_info.get("supported_currencies", [])

        # Build comprehensive response
        response = GeolocationDetectionResponse(
            ip_address=ip_address,
            country_code=enhanced_result.country_code,
            country_name=enhanced_result.country_name,
            continent_code=enhanced_result.continent_code,
            continent_name=enhanced_result.continent_name,
            detection_method=enhanced_result.detection_method,
            confidence_score=enhanced_result.confidence_score,
            detection_time_ms=enhanced_result.detection_time_ms,
            vpn_detected=getattr(enhanced_result, 'is_vpn_detected', False),
            vpn_provider=getattr(enhanced_result, 'vpn_provider', None),
            vpn_confidence=getattr(enhanced_result, 'vpn_confidence_score', 0.0),
            proxy_detected=getattr(enhanced_result, 'is_proxy_detected', False),
            risk_score=getattr(enhanced_result, 'risk_score', 0.0),
            risk_factors=getattr(enhanced_result, 'risk_factors', []),
            recommended_provider=recommended_provider,
            provider_selection_reason=provider_selection_reason,
            supported_currencies=supported_currencies,
            detected_at=enhanced_result.detected_at
        )

        logger.info(
            f"Geolocation detection completed successfully",
            extra={
                "correlation_id": correlation,
                "ip_address": ip_address,
                "country_code": enhanced_result.country_code,
                "detection_time_ms": enhanced_result.detection_time_ms,
                "vpn_detected": getattr(enhanced_result, 'is_vpn_detected', False),
                "recommended_provider": recommended_provider
            }
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Geolocation detection failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "ip_address": request_data.ip_address,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "GEOLOCATION_DETECTION_ERROR",
                "message": "Internal error during geolocation detection",
                "request_id": correlation
            }
        )


@router.get(
    "/provider-recommendation/{country_code}",
    response_model=PaymentProviderRecommendationResponse,
    summary="Get payment provider recommendation for country",
    description="""
    **Get intelligent payment provider recommendation based on country code.**

    This endpoint provides optimized payment provider selection based on geographic location,
    currency preferences, and performance analytics.

    **Routing Logic:**
    1. **Priority 1**: Cryptocurrency → Busha (if crypto_currency specified)
    2. **Priority 2**: User Preference → Validated against geo-compatibility
    3. **Priority 3**: Geolocation-Based → Country-specific optimization
    4. **Priority 4**: Currency-Based Fallback → Traditional routing

    **Provider Coverage:**
    - **Paystack**: African markets (NG, GH, KE, ZA, etc.)
    - **Stripe**: Diaspora markets (US, UK, CA, EU, etc.)
    - **Busha**: Cryptocurrency payments (BTC, ETH, USDT, USDC)
    """,
    responses={
        200: {
            "description": "Provider recommendation generated successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "nigeria_recommendation": {
                            "summary": "Nigeria - Paystack Recommendation",
                            "value": {
                                "country_code": "NG",
                                "country_name": "Nigeria",
                                "recommended_provider": "paystack",
                                "selection_reason": "Optimal provider for Nigerian market with local payment methods",
                                "confidence_score": 0.95,
                                "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                                "supported_payment_methods": ["card", "bank_transfer", "ussd", "mobile_money"],
                                "estimated_success_rate": 0.94,
                                "average_processing_time_ms": 2500,
                                "transaction_fees": {
                                    "percentage": 1.5,
                                    "fixed_fee_ngn": 0,
                                    "cap_ngn": 2000
                                }
                            }
                        },
                        "us_recommendation": {
                            "summary": "United States - Stripe Recommendation",
                            "value": {
                                "country_code": "US",
                                "country_name": "United States",
                                "recommended_provider": "stripe",
                                "selection_reason": "Optimal provider for US market with comprehensive payment method support",
                                "confidence_score": 0.98,
                                "supported_currencies": ["USD", "EUR", "GBP", "CAD"],
                                "supported_payment_methods": ["card", "bank_transfer", "apple_pay", "google_pay"],
                                "estimated_success_rate": 0.96,
                                "average_processing_time_ms": 1800,
                                "transaction_fees": {
                                    "percentage": 2.9,
                                    "fixed_fee_cents": 30
                                }
                            }
                        }
                    }
                }
            }
        }
    }
)
async def get_provider_recommendation(
    country_code: str,
    currency: Optional[str] = Query(None, description="Preferred currency for payment"),
    crypto_currency: Optional[str] = Query(None, description="Cryptocurrency for payment (BTC, ETH, USDT, USDC)"),
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Get intelligent payment provider recommendation based on country and preferences.

    **Performance Target**: <50ms response time
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Processing provider recommendation request",
            extra={
                "correlation_id": correlation,
                "country_code": country_code,
                "currency": currency,
                "crypto_currency": crypto_currency,
                "user_id": current_user.id if current_user else None
            }
        )

        # Initialize payment service
        from app.services.payment_service import PaymentService
        payment_service = PaymentService(db)

        # Get user preferences if authenticated
        user_preferences = None
        if current_user:
            # TODO: Get user payment preferences from user profile
            pass

        # Get provider recommendation
        recommendation = await payment_service.get_provider_recommendation(
            country_code=country_code.upper(),
            currency=currency or "USD",
            crypto_currency=crypto_currency,
            user_preferences=user_preferences
        )

        logger.info(
            f"Provider recommendation generated successfully",
            extra={
                "correlation_id": correlation,
                "country_code": country_code,
                "recommended_provider": recommendation.get("provider"),
                "confidence_score": recommendation.get("confidence_score")
            }
        )

        return PaymentProviderRecommendationResponse(**recommendation)

    except Exception as e:
        logger.error(
            f"Provider recommendation failed: {str(e)}",
            extra={
                "correlation_id": correlation,
                "country_code": country_code,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "PROVIDER_RECOMMENDATION_ERROR",
                "message": "Failed to generate provider recommendation",
                "request_id": correlation
            }
        )


@router.get(
    "/supported-countries",
    response_model=SupportedCountriesResponse,
    summary="Get supported countries for geolocation routing",
    description="""
    **Get comprehensive list of supported countries with provider mappings.**

    This endpoint provides the complete list of countries supported by the geolocation
    routing system with provider mappings, currency support, and performance metrics.

    **Country Categories:**
    - **African Markets**: Paystack-optimized countries (NG, GH, KE, ZA, etc.)
    - **Diaspora Markets**: Stripe-optimized countries (US, UK, CA, EU, etc.)
    - **Cryptocurrency Markets**: Busha-supported regions for crypto payments
    - **Hybrid Markets**: Countries with multiple provider options
    """,
    responses={
        200: {
            "description": "Supported countries retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "total_countries": 195,
                        "paystack_countries": 54,
                        "stripe_countries": 120,
                        "busha_countries": 180,
                        "countries": [
                            {
                                "country_code": "NG",
                                "country_name": "Nigeria",
                                "continent": "Africa",
                                "primary_provider": "paystack",
                                "supported_providers": ["paystack", "busha"],
                                "supported_currencies": ["NGN", "USD", "GBP", "EUR"],
                                "crypto_supported": True,
                                "success_rate": 0.94,
                                "avg_processing_time_ms": 2500
                            },
                            {
                                "country_code": "US",
                                "country_name": "United States",
                                "continent": "North America",
                                "primary_provider": "stripe",
                                "supported_providers": ["stripe", "busha"],
                                "supported_currencies": ["USD", "EUR", "GBP", "CAD"],
                                "crypto_supported": True,
                                "success_rate": 0.96,
                                "avg_processing_time_ms": 1800
                            }
                        ],
                        "last_updated": "2024-01-15T10:30:00Z"
                    }
                }
            }
        }
    }
)
async def get_supported_countries(
    provider: Optional[str] = Query(None, description="Filter by payment provider (paystack, stripe, busha)"),
    continent: Optional[str] = Query(None, description="Filter by continent code (AF, NA, EU, AS, SA, OC)"),
    crypto_supported: Optional[bool] = Query(None, description="Filter by cryptocurrency support"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive list of supported countries with provider mappings.

    **Performance Target**: <100ms response time
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Processing supported countries request",
            extra={
                "correlation_id": correlation,
                "provider_filter": provider,
                "continent_filter": continent,
                "crypto_filter": crypto_supported
            }
        )

        # Initialize geolocation analytics service
        analytics_service = GeolocationAnalyticsService(db)

        # Get supported countries with filters
        countries_data = await analytics_service.get_supported_countries(
            provider_filter=provider,
            continent_filter=continent,
            crypto_supported_filter=crypto_supported
        )

        logger.info(
            f"Supported countries retrieved successfully",
            extra={
                "correlation_id": correlation,
                "total_countries": len(countries_data.get("countries", [])),
                "provider_filter": provider
            }
        )

        return SupportedCountriesResponse(**countries_data)

    except Exception as e:
        logger.error(
            f"Failed to retrieve supported countries: {str(e)}",
            extra={
                "correlation_id": correlation,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "SUPPORTED_COUNTRIES_ERROR",
                "message": "Failed to retrieve supported countries",
                "request_id": correlation
            }
        )


@router.get(
    "/analytics/performance",
    response_model=GeolocationAnalyticsResponse,
    summary="Get geolocation routing performance analytics",
    description="""
    **Get comprehensive geolocation routing performance analytics and insights.**

    This endpoint provides detailed analytics on geolocation routing performance including:
    - Provider performance by geographic regions
    - Conversion rate optimization insights
    - VPN detection impact analysis
    - A/B testing results and recommendations

    **Analytics Categories:**
    - **Geographic Performance**: Success rates and processing times by country/continent
    - **Provider Effectiveness**: Comparative analysis of Paystack, Stripe, and Busha
    - **VPN Impact Assessment**: How VPN detection affects routing decisions and success rates
    - **Optimization Opportunities**: Data-driven recommendations for routing improvements
    """,
    dependencies=[Depends(get_current_user)],
    responses={
        200: {
            "description": "Analytics retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "summary": {
                            "total_payments": 15420,
                            "total_countries": 45,
                            "avg_success_rate": 0.94,
                            "avg_processing_time_ms": 2100,
                            "vpn_detection_rate": 0.12,
                            "top_performing_provider": "stripe"
                        },
                        "provider_performance": [
                            {
                                "provider": "paystack",
                                "total_payments": 8500,
                                "success_rate": 0.94,
                                "avg_processing_time_ms": 2500,
                                "top_countries": ["NG", "GH", "KE"],
                                "revenue_share": 0.55
                            },
                            {
                                "provider": "stripe",
                                "total_payments": 6200,
                                "success_rate": 0.96,
                                "avg_processing_time_ms": 1800,
                                "top_countries": ["US", "UK", "CA"],
                                "revenue_share": 0.40
                            },
                            {
                                "provider": "busha",
                                "total_payments": 720,
                                "success_rate": 0.89,
                                "avg_processing_time_ms": 3200,
                                "top_currencies": ["BTC", "ETH", "USDT"],
                                "revenue_share": 0.05
                            }
                        ],
                        "geographic_insights": [
                            {
                                "country_code": "NG",
                                "country_name": "Nigeria",
                                "total_payments": 4200,
                                "success_rate": 0.94,
                                "primary_provider": "paystack",
                                "vpn_detection_rate": 0.08
                            }
                        ],
                        "optimization_recommendations": [
                            {
                                "type": "provider_routing",
                                "description": "Consider routing UK payments to Paystack for improved success rates",
                                "potential_improvement": 0.03,
                                "confidence": 0.85
                            }
                        ],
                        "generated_at": "2024-01-15T10:30:00Z"
                    }
                }
            }
        }
    }
)
async def get_geolocation_analytics(
    start_date: Optional[str] = Query(None, description="Start date for analytics (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date for analytics (YYYY-MM-DD)"),
    country_codes: Optional[str] = Query(None, description="Comma-separated country codes to filter"),
    provider: Optional[str] = Query(None, description="Filter by payment provider"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive geolocation routing performance analytics.

    **Performance Target**: <200ms response time
    **Authorization**: Requires authenticated user with analytics access
    """
    correlation = correlation_id.get()

    try:
        logger.info(
            f"Processing geolocation analytics request",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "start_date": start_date,
                "end_date": end_date,
                "country_filter": country_codes,
                "provider_filter": provider
            }
        )

        # Initialize analytics service
        analytics_service = GeolocationAnalyticsService(db)

        # Parse date filters
        from datetime import datetime, timedelta, timezone
        if start_date:
            start_dt = datetime.fromisoformat(start_date)
        else:
            start_dt = datetime.now(timezone.utc) - timedelta(days=30)

        if end_date:
            end_dt = datetime.fromisoformat(end_date)
        else:
            end_dt = datetime.now(timezone.utc)

        # Parse country codes filter
        country_list = None
        if country_codes:
            country_list = [code.strip().upper() for code in country_codes.split(",")]

        # Get comprehensive analytics
        analytics_data = await analytics_service.get_comprehensive_analytics(
            start_date=start_dt,
            end_date=end_dt,
            country_codes=country_list,
            provider_filter=provider
        )

        logger.info(
            f"Geolocation analytics retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "total_payments": analytics_data.get("summary", {}).get("total_payments", 0),
                "date_range_days": (end_dt - start_dt).days
            }
        )

        return GeolocationAnalyticsResponse(**analytics_data)

    except Exception as e:
        logger.error(
            f"Failed to retrieve geolocation analytics: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "GEOLOCATION_ANALYTICS_ERROR",
                "message": "Failed to retrieve geolocation analytics",
                "request_id": correlation
            }
        )
