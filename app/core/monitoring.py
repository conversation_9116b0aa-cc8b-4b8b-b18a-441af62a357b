"""
Comprehensive monitoring infrastructure for Culture Connect Backend API.

This module provides:
- Advanced health check endpoints with detailed system status
- Performance metrics collection and export
- Sentry integration for error tracking and performance monitoring
- System resource monitoring (CPU, memory, disk)
- Custom business metrics for promotional system and payments
- Prometheus-compatible metrics export
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

# Optional imports with fallbacks
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    import sentry_sdk
    from sentry_sdk.integrations.fastapi import FastApiIntegration
    from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
    from sentry_sdk.integrations.redis import RedisIntegration
    SENTRY_AVAILABLE = True
except ImportError:
    SENTRY_AVAILABLE = False
    sentry_sdk = None

from fastapi import APIRouter, HTTPException, status

from app.core.config import settings

# Optional database import with fallback
try:
    from sqlalchemy import text
    from app.db.database import get_async_session_context
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    text = None
    get_async_session_context = None


logger = logging.getLogger(__name__)


@dataclass
class HealthCheckResult:
    """Health check result data structure."""
    service: str
    status: str  # 'healthy', 'unhealthy', 'degraded'
    response_time: float
    details: Dict[str, Any]
    timestamp: datetime


@dataclass
class SystemMetrics:
    """System resource metrics data structure."""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    load_average: List[float]
    timestamp: datetime


@dataclass
class BusinessMetrics:
    """Business-specific metrics data structure."""
    active_vendors: int
    active_campaigns: int
    total_transactions_today: int
    total_revenue_today: float
    average_response_time: float
    error_rate: float
    timestamp: datetime


class MetricsCollector:
    """Collects and stores application metrics."""

    def __init__(self):
        self.request_counts = defaultdict(int)
        self.response_times = defaultdict(deque)
        self.error_counts = defaultdict(int)
        self.business_events = defaultdict(int)
        self.start_time = time.time()

    def record_request(self, method: str, path: str, status_code: int, response_time: float):
        """Record request metrics."""
        endpoint = f"{method} {path}"
        self.request_counts[endpoint] += 1

        # Keep last 1000 response times for each endpoint
        if len(self.response_times[endpoint]) >= 1000:
            self.response_times[endpoint].popleft()
        self.response_times[endpoint].append(response_time)

        if status_code >= 400:
            self.error_counts[endpoint] += 1

    def record_business_event(self, event_type: str, count: int = 1):
        """Record business event metrics."""
        self.business_events[event_type] += count

    def record_database_query(self, table: str, operation: str, duration: float, row_count: int = 0):
        """Record database query metrics."""
        query_key = f"db_{table}_{operation}"
        self.business_events[query_key] += 1

        # Track query duration
        duration_key = f"db_duration_{table}_{operation}"
        if len(self.response_times[duration_key]) >= 1000:
            self.response_times[duration_key].popleft()
        self.response_times[duration_key].append(duration)

        # Track row counts
        row_key = f"db_rows_{table}_{operation}"
        if len(self.response_times[row_key]) >= 1000:
            self.response_times[row_key].popleft()
        self.response_times[row_key].append(row_count)

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        uptime = time.time() - self.start_time

        # Calculate average response times
        avg_response_times = {}
        for endpoint, times in self.response_times.items():
            if times:
                avg_response_times[endpoint] = sum(times) / len(times)

        # Calculate error rates
        error_rates = {}
        for endpoint in self.request_counts:
            total_requests = self.request_counts[endpoint]
            errors = self.error_counts.get(endpoint, 0)
            error_rates[endpoint] = (errors / total_requests) * 100 if total_requests > 0 else 0

        return {
            'uptime_seconds': uptime,
            'total_requests': sum(self.request_counts.values()),
            'total_errors': sum(self.error_counts.values()),
            'request_counts': dict(self.request_counts),
            'average_response_times': avg_response_times,
            'error_rates': error_rates,
            'business_events': dict(self.business_events)
        }

    def reset_metrics(self):
        """Reset all metrics (useful for testing)."""
        self.request_counts.clear()
        self.response_times.clear()
        self.error_counts.clear()
        self.business_events.clear()
        self.start_time = time.time()


# Global metrics collector instance
metrics_collector = MetricsCollector()


class HealthChecker:
    """Performs comprehensive health checks on system components."""

    async def check_database(self) -> HealthCheckResult:
        """Check database connectivity and performance."""
        start_time = time.time()

        if not DATABASE_AVAILABLE:
            return HealthCheckResult(
                service="database",
                status="unhealthy",
                response_time=0.0,
                details={"error": "Database libraries not available"},
                timestamp=datetime.utcnow()
            )

        try:
            from app.db.session import get_database_info, session_manager

            async with get_async_session_context() as session:
                # Test basic connectivity
                await session.execute(text("SELECT 1"))

                # Test write capability
                await session.execute(text("CREATE TEMP TABLE health_check_test (id INTEGER)"))
                await session.execute(text("INSERT INTO health_check_test (id) VALUES (1)"))
                result = await session.execute(text("SELECT id FROM health_check_test WHERE id = 1"))

                if result.scalar() != 1:
                    raise Exception("Database write/read test failed")

                response_time = time.time() - start_time

                # Get advanced database information
                db_info = await get_database_info()

                # Get connection pool statistics
                pool_stats = session_manager.get_connection_stats()

                # Calculate connection pool health
                pool_health = "healthy"
                if pool_stats.get("failed_connections", 0) > 0:
                    failure_rate = pool_stats["failed_connections"] / max(pool_stats["total_connections"], 1)
                    if failure_rate > 0.1:  # More than 10% failure rate
                        pool_health = "degraded"
                    if failure_rate > 0.3:  # More than 30% failure rate
                        pool_health = "unhealthy"

                # Determine overall database status
                overall_status = "healthy"
                if pool_health == "unhealthy" or response_time > 5.0:
                    overall_status = "unhealthy"
                elif pool_health == "degraded" or response_time > 2.0:
                    overall_status = "degraded"

                details = {
                    "connection_pool_size": settings.DATABASE_POOL_SIZE,
                    "max_overflow": settings.DATABASE_MAX_OVERFLOW,
                    "test_query_time": response_time,
                    "pool_health": pool_health,
                    "connection_stats": pool_stats,
                    "database_info": db_info if "error" not in db_info else {"error": "Failed to get database info"}
                }

                return HealthCheckResult(
                    service="database",
                    status=overall_status,
                    response_time=response_time,
                    details=details,
                    timestamp=datetime.utcnow()
                )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Database health check failed: {str(e)}")

            return HealthCheckResult(
                service="database",
                status="unhealthy",
                response_time=response_time,
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )

    async def check_redis(self) -> HealthCheckResult:
        """Check Redis connectivity and performance."""
        start_time = time.time()

        if not REDIS_AVAILABLE:
            return HealthCheckResult(
                service="redis",
                status="unhealthy",
                response_time=0.0,
                details={"error": "Redis library not available"},
                timestamp=datetime.utcnow()
            )

        try:
            # Create Redis connection
            redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)

            # Test basic connectivity
            await asyncio.get_event_loop().run_in_executor(None, redis_client.ping)

            # Test write/read
            test_key = "health_check_test"
            test_value = "test_value"

            await asyncio.get_event_loop().run_in_executor(
                None, redis_client.set, test_key, test_value, 60
            )

            stored_value = await asyncio.get_event_loop().run_in_executor(
                None, redis_client.get, test_key
            )

            if stored_value != test_value:
                raise Exception("Redis write/read test failed")

            # Clean up
            await asyncio.get_event_loop().run_in_executor(None, redis_client.delete, test_key)

            response_time = time.time() - start_time

            # Get Redis info
            info = await asyncio.get_event_loop().run_in_executor(None, redis_client.info)

            redis_client.close()

            return HealthCheckResult(
                service="redis",
                status="healthy",
                response_time=response_time,
                details={
                    "version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory_human": info.get("used_memory_human"),
                    "test_operation_time": response_time
                },
                timestamp=datetime.utcnow()
            )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Redis health check failed: {str(e)}")

            return HealthCheckResult(
                service="redis",
                status="unhealthy",
                response_time=response_time,
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )

    async def check_database_performance(self) -> Dict[str, Any]:
        """
        Perform comprehensive database performance diagnostics.

        Returns:
            Dict[str, Any]: Detailed database performance metrics
        """
        if not DATABASE_AVAILABLE:
            return {"error": "Database libraries not available"}

        try:
            from app.db.session import get_database_info, session_manager, test_database_connection

            performance_metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "connection_test": False,
                "query_performance": {},
                "pool_metrics": {},
                "database_info": {},
                "health_status": "unknown"
            }

            # Test basic connection
            performance_metrics["connection_test"] = await test_database_connection()

            if performance_metrics["connection_test"]:
                # Get connection pool metrics
                performance_metrics["pool_metrics"] = session_manager.get_connection_stats()

                # Get database information
                db_info = await get_database_info()
                performance_metrics["database_info"] = db_info

                # Perform query performance tests
                async with get_async_session_context() as session:
                    # Test simple query performance
                    start_time = time.time()
                    await session.execute(text("SELECT 1"))
                    simple_query_time = time.time() - start_time

                    # Test table creation performance
                    start_time = time.time()
                    await session.execute(text("CREATE TEMP TABLE perf_test (id INTEGER, data TEXT)"))
                    create_table_time = time.time() - start_time

                    # Test insert performance
                    start_time = time.time()
                    for i in range(10):
                        await session.execute(
                            text("INSERT INTO perf_test (id, data) VALUES (:id, :data)"),
                            {"id": i, "data": f"test_data_{i}"}
                        )
                    insert_time = time.time() - start_time

                    # Test select performance
                    start_time = time.time()
                    result = await session.execute(text("SELECT COUNT(*) FROM perf_test"))
                    count = result.scalar()
                    select_time = time.time() - start_time

                    performance_metrics["query_performance"] = {
                        "simple_query_ms": round(simple_query_time * 1000, 2),
                        "create_table_ms": round(create_table_time * 1000, 2),
                        "insert_10_records_ms": round(insert_time * 1000, 2),
                        "select_count_ms": round(select_time * 1000, 2),
                        "records_inserted": count
                    }

                # Determine health status based on performance
                total_time = (simple_query_time + create_table_time + insert_time + select_time)
                if total_time < 0.1:  # Less than 100ms total
                    performance_metrics["health_status"] = "excellent"
                elif total_time < 0.5:  # Less than 500ms total
                    performance_metrics["health_status"] = "good"
                elif total_time < 2.0:  # Less than 2s total
                    performance_metrics["health_status"] = "acceptable"
                else:
                    performance_metrics["health_status"] = "poor"

            return performance_metrics

        except Exception as e:
            logger.error(f"Database performance check failed: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "health_status": "error"
            }

    async def check_external_services(self) -> List[HealthCheckResult]:
        """Check external service connectivity."""
        results = []

        # Check AI/ML API
        if settings.ENABLE_AI_OPTIMIZATION:
            results.append(await self._check_aiml_api())

        # Add other external service checks as needed
        return results

    async def _check_aiml_api(self) -> HealthCheckResult:
        """Check AI/ML API connectivity."""
        start_time = time.time()

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{settings.AIML_BASE_URL}/health",
                    headers={"Authorization": f"Bearer {settings.AIML_API_KEY}"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    response_time = time.time() - start_time

                    if response.status == 200:
                        return HealthCheckResult(
                            service="aiml_api",
                            status="healthy",
                            response_time=response_time,
                            details={"status_code": response.status},
                            timestamp=datetime.utcnow()
                        )
                    else:
                        return HealthCheckResult(
                            service="aiml_api",
                            status="degraded",
                            response_time=response_time,
                            details={"status_code": response.status},
                            timestamp=datetime.utcnow()
                        )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"AI/ML API health check failed: {str(e)}")

            return HealthCheckResult(
                service="aiml_api",
                status="unhealthy",
                response_time=response_time,
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )

    def get_system_metrics(self) -> SystemMetrics:
        """Get current system resource metrics."""
        if not PSUTIL_AVAILABLE:
            # Return default metrics when psutil is not available
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_total_mb=0.0,
                disk_percent=0.0,
                disk_used_gb=0.0,
                disk_total_gb=0.0,
                load_average=[0.0, 0.0, 0.0],
                timestamp=datetime.utcnow()
            )

        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory metrics
            memory = psutil.virtual_memory()

            # Disk metrics
            disk = psutil.disk_usage('/')

            # Load average (Unix-like systems)
            try:
                load_avg = list(psutil.getloadavg())
            except AttributeError:
                # Windows doesn't have load average
                load_avg = [0.0, 0.0, 0.0]

            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                memory_total_mb=memory.total / (1024 * 1024),
                disk_percent=disk.percent,
                disk_used_gb=disk.used / (1024 * 1024 * 1024),
                disk_total_gb=disk.total / (1024 * 1024 * 1024),
                load_average=load_avg,
                timestamp=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Failed to get system metrics: {str(e)}")
            # Return default metrics on error
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_total_mb=0.0,
                disk_percent=0.0,
                disk_used_gb=0.0,
                disk_total_gb=0.0,
                load_average=[0.0, 0.0, 0.0],
                timestamp=datetime.utcnow()
            )

    async def get_business_metrics(self) -> BusinessMetrics:
        """Get business-specific metrics."""
        if not DATABASE_AVAILABLE:
            # Return default metrics when database is not available
            return BusinessMetrics(
                active_vendors=0,
                active_campaigns=0,
                total_transactions_today=0,
                total_revenue_today=0.0,
                average_response_time=0.0,
                error_rate=0.0,
                timestamp=datetime.utcnow()
            )

        try:
            async with get_async_session_context() as session:
                # Get active vendors count
                vendor_result = await session.execute(
                    text("SELECT COUNT(*) FROM vendors WHERE is_active = true")
                )
                active_vendors = vendor_result.scalar() or 0

                # Get active campaigns count (when promotional tables exist)
                try:
                    campaign_result = await session.execute(
                        text("SELECT COUNT(*) FROM campaigns WHERE status = 'active'")
                    )
                    active_campaigns = campaign_result.scalar() or 0
                except:
                    active_campaigns = 0

                # Get today's transactions (when payment tables exist)
                try:
                    today = datetime.utcnow().date()
                    transaction_result = await session.execute(
                        text("""
                            SELECT COUNT(*), COALESCE(SUM(amount), 0)
                            FROM payments
                            WHERE DATE(created_at) = :today AND status = 'completed'
                        """),
                        {"today": today}
                    )
                    transaction_row = transaction_result.fetchone()
                    total_transactions_today = transaction_row[0] if transaction_row else 0
                    total_revenue_today = float(transaction_row[1]) if transaction_row else 0.0
                except:
                    total_transactions_today = 0
                    total_revenue_today = 0.0

                # Calculate average response time from metrics
                metrics_summary = metrics_collector.get_metrics_summary()
                avg_response_times = metrics_summary.get('average_response_times', {})
                average_response_time = (
                    sum(avg_response_times.values()) / len(avg_response_times)
                    if avg_response_times else 0.0
                )

                # Calculate error rate
                total_requests = metrics_summary.get('total_requests', 0)
                total_errors = metrics_summary.get('total_errors', 0)
                error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0.0

                return BusinessMetrics(
                    active_vendors=active_vendors,
                    active_campaigns=active_campaigns,
                    total_transactions_today=total_transactions_today,
                    total_revenue_today=total_revenue_today,
                    average_response_time=average_response_time,
                    error_rate=error_rate,
                    timestamp=datetime.utcnow()
                )

        except Exception as e:
            logger.error(f"Failed to get business metrics: {str(e)}")
            return BusinessMetrics(
                active_vendors=0,
                active_campaigns=0,
                total_transactions_today=0,
                total_revenue_today=0.0,
                average_response_time=0.0,
                error_rate=0.0,
                timestamp=datetime.utcnow()
            )


# Global health checker instance
health_checker = HealthChecker()


def setup_sentry():
    """Configure Sentry for error tracking and performance monitoring."""
    if not SENTRY_AVAILABLE:
        logger.info("Sentry SDK not available, skipping initialization")
        return

    sentry_config = settings.get_sentry_config()

    if not sentry_config:
        logger.info("Sentry not configured, skipping initialization")
        return

    try:
        sentry_sdk.init(
            dsn=sentry_config["dsn"],
            environment=sentry_config["environment"],
            traces_sample_rate=sentry_config["traces_sample_rate"],
            debug=sentry_config["debug"],
            integrations=[
                FastApiIntegration(auto_enabling_integrations=False),
                SqlalchemyIntegration(),
                RedisIntegration(),
            ],
            # Additional Sentry configuration
            attach_stacktrace=True,
            send_default_pii=False,  # Don't send personally identifiable information
            max_breadcrumbs=50,
            before_send=_filter_sentry_events,
        )

        logger.info(f"Sentry initialized for environment: {sentry_config['environment']}")

    except Exception as e:
        logger.error(f"Failed to initialize Sentry: {str(e)}")


def _filter_sentry_events(event, hint):
    """Filter Sentry events to reduce noise."""
    # Don't send health check errors
    if 'request' in event and event['request'].get('url', '').endswith('/health'):
        return None

    # Don't send certain HTTP errors
    if 'exception' in event:
        for exception in event['exception']['values']:
            if exception.get('type') in ['HTTPException', 'RequestValidationError']:
                return None

    return event


def create_health_router() -> APIRouter:
    """Create health check router with comprehensive endpoints."""
    router = APIRouter(prefix="/health", tags=["Health"])

    @router.get("/")
    async def basic_health_check():
        """Basic health check endpoint."""
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT
        }

    @router.get("/detailed")
    async def detailed_health_check():
        """Detailed health check with all system components."""
        try:
            # Run all health checks
            db_check = await health_checker.check_database()
            redis_check = await health_checker.check_redis()
            external_checks = await health_checker.check_external_services()

            # Collect all results
            all_checks = [db_check, redis_check] + external_checks

            # Determine overall status
            overall_status = "healthy"
            if any(check.status == "unhealthy" for check in all_checks):
                overall_status = "unhealthy"
            elif any(check.status == "degraded" for check in all_checks):
                overall_status = "degraded"

            # Build response
            response = {
                "status": overall_status,
                "timestamp": datetime.utcnow().isoformat(),
                "version": settings.APP_VERSION,
                "environment": settings.ENVIRONMENT,
                "checks": {check.service: asdict(check) for check in all_checks}
            }

            status_code = 200 if overall_status == "healthy" else 503
            return response, status_code

        except Exception as e:
            logger.error(f"Detailed health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e) if settings.is_development else "Health check failed"
            }, 503

    @router.get("/database")
    async def database_performance_check():
        """Get detailed database performance metrics."""
        try:
            performance_data = await health_checker.check_database_performance()

            # Determine HTTP status code based on health status
            health_status = performance_data.get("health_status", "unknown")
            if health_status in ["excellent", "good"]:
                status_code = 200
            elif health_status == "acceptable":
                status_code = 200  # Still OK but with warnings
            elif health_status in ["poor", "error"]:
                status_code = 503  # Service unavailable
            else:
                status_code = 200  # Default to OK for unknown status

            return performance_data, status_code

        except Exception as e:
            logger.error(f"Database performance check failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to check database performance"
            )

    @router.get("/metrics")
    async def get_metrics():
        """Get application metrics."""
        try:
            system_metrics = health_checker.get_system_metrics()
            business_metrics = await health_checker.get_business_metrics()
            app_metrics = metrics_collector.get_metrics_summary()

            return {
                "timestamp": datetime.utcnow().isoformat(),
                "system": asdict(system_metrics),
                "business": asdict(business_metrics),
                "application": app_metrics
            }

        except Exception as e:
            logger.error(f"Metrics collection failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to collect metrics"
            )

    @router.get("/prometheus")
    async def prometheus_metrics():
        """Prometheus-compatible metrics endpoint."""
        try:
            system_metrics = health_checker.get_system_metrics()
            business_metrics = await health_checker.get_business_metrics()
            app_metrics = metrics_collector.get_metrics_summary()

            # Generate Prometheus format
            prometheus_output = []

            # System metrics
            prometheus_output.extend([
                f"# HELP system_cpu_percent CPU usage percentage",
                f"# TYPE system_cpu_percent gauge",
                f"system_cpu_percent {system_metrics.cpu_percent}",
                f"# HELP system_memory_percent Memory usage percentage",
                f"# TYPE system_memory_percent gauge",
                f"system_memory_percent {system_metrics.memory_percent}",
                f"# HELP system_disk_percent Disk usage percentage",
                f"# TYPE system_disk_percent gauge",
                f"system_disk_percent {system_metrics.disk_percent}",
            ])

            # Application metrics
            prometheus_output.extend([
                f"# HELP app_uptime_seconds Application uptime in seconds",
                f"# TYPE app_uptime_seconds counter",
                f"app_uptime_seconds {app_metrics['uptime_seconds']}",
                f"# HELP app_requests_total Total number of requests",
                f"# TYPE app_requests_total counter",
                f"app_requests_total {app_metrics['total_requests']}",
                f"# HELP app_errors_total Total number of errors",
                f"# TYPE app_errors_total counter",
                f"app_errors_total {app_metrics['total_errors']}",
            ])

            # Business metrics
            prometheus_output.extend([
                f"# HELP business_active_vendors Number of active vendors",
                f"# TYPE business_active_vendors gauge",
                f"business_active_vendors {business_metrics.active_vendors}",
                f"# HELP business_active_campaigns Number of active campaigns",
                f"# TYPE business_active_campaigns gauge",
                f"business_active_campaigns {business_metrics.active_campaigns}",
                f"# HELP business_revenue_today_total Today's total revenue",
                f"# TYPE business_revenue_today_total gauge",
                f"business_revenue_today_total {business_metrics.total_revenue_today}",
            ])

            return "\n".join(prometheus_output), {"Content-Type": "text/plain"}

        except Exception as e:
            logger.error(f"Prometheus metrics generation failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate metrics"
            )

    return router


# Export commonly used items
__all__ = [
    'HealthCheckResult',
    'SystemMetrics',
    'BusinessMetrics',
    'MetricsCollector',
    'HealthChecker',
    'metrics_collector',
    'health_checker',
    'setup_sentry',
    'create_health_router',
    'PSUTIL_AVAILABLE',
    'REDIS_AVAILABLE',
    'SENTRY_AVAILABLE',
    'DATABASE_AVAILABLE'
]
