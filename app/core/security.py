"""
Enhanced Security utilities for Culture Connect Backend API.

This module provides comprehensive JWT token management, password hashing, and authentication
utilities following security best practices for Task 2.1.1 implementation.

Features:
- JWT token generation with role-based claims
- Refresh token mechanism with rotation
- Token blacklisting for secure logout
- Role-based access control with permissions
- Security event logging and audit trails
- Production-grade token validation and expiration handling
"""

import secrets
import uuid
import logging
import base64
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, Union, Set, List
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi import HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel
import redis
from contextlib import asynccontextmanager
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.core.config import settings

# Password security configuration
BCRYPT_ROUNDS = getattr(settings, 'BCRYPT_ROUNDS', 12)
PASSWORD_MIN_LENGTH = getattr(settings, 'PASSWORD_MIN_LENGTH', 8)
PASSWORD_HISTORY_COUNT = getattr(settings, 'PASSWORD_HISTORY_COUNT', 5)
ACCOUNT_LOCKOUT_ATTEMPTS = getattr(settings, 'ACCOUNT_LOCKOUT_ATTEMPTS', 5)
ACCOUNT_LOCKOUT_DURATION_MINUTES = getattr(settings, 'ACCOUNT_LOCKOUT_DURATION_MINUTES', 15)
PASSWORD_RESET_TOKEN_EXPIRE_HOURS = getattr(settings, 'PASSWORD_RESET_TOKEN_EXPIRE_HOURS', 24)

# Configure logging
logger = logging.getLogger(__name__)


# Enhanced password hashing context with configurable rounds
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=BCRYPT_ROUNDS
)

# JWT Bearer token scheme
security = HTTPBearer()


class TokenData(BaseModel):
    """Enhanced token data model for JWT payload with role-based claims."""
    user_id: Optional[int] = None
    email: Optional[str] = None
    role: Optional[str] = None
    vendor_id: Optional[int] = None
    scopes: List[str] = []
    jti: Optional[str] = None  # JWT ID for token blacklisting
    iat: Optional[datetime] = None  # Issued at
    exp: Optional[datetime] = None  # Expires at
    token_type: Optional[str] = None  # access or refresh


class Token(BaseModel):
    """Enhanced token response model with security metadata."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_expires_in: int
    scope: str = ""
    jti: str  # JWT ID for tracking


class TokenBlacklistManager:
    """
    Token blacklist manager for secure logout functionality.

    Manages blacklisted tokens using Redis for production or in-memory for development.
    Implements automatic cleanup of expired tokens to prevent memory leaks.
    """

    def __init__(self):
        """Initialize token blacklist manager."""
        self._blacklisted_tokens: Set[str] = set()
        self._redis_client: Optional[Any] = None
        self._use_redis = hasattr(settings, 'REDIS_URL') and settings.REDIS_URL

        if self._use_redis:
            try:
                import redis
                self._redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
                logger.info("Token blacklist using Redis backend")
            except ImportError:
                logger.warning("Redis not available, using in-memory token blacklist")
                self._use_redis = False
        else:
            logger.info("Token blacklist using in-memory backend")

    async def blacklist_token(self, token: str, expires_at: Optional[datetime] = None) -> bool:
        """
        Add token to blacklist.

        Args:
            token: JWT token to blacklist
            expires_at: Token expiration time for automatic cleanup

        Returns:
            bool: True if successfully blacklisted
        """
        try:
            # Extract JWT ID from token
            decoded = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
            )
            jti = decoded.get("jti")

            if not jti:
                logger.warning("Token missing JTI claim, cannot blacklist")
                return False

            if self._use_redis and self._redis_client:
                # Use Redis with expiration
                ttl = None
                if expires_at:
                    ttl = int((expires_at - datetime.utcnow()).total_seconds())
                    if ttl <= 0:
                        return True  # Token already expired

                self._redis_client.set(f"blacklist:{jti}", "1", ex=ttl)
                logger.info(f"Token {jti} blacklisted in Redis")
            else:
                # Use in-memory storage
                self._blacklisted_tokens.add(jti)
                logger.info(f"Token {jti} blacklisted in memory")

            return True

        except Exception as e:
            logger.error(f"Failed to blacklist token: {str(e)}")
            return False

    async def is_blacklisted(self, token: str) -> bool:
        """
        Check if token is blacklisted.

        Args:
            token: JWT token to check

        Returns:
            bool: True if token is blacklisted
        """
        try:
            # Extract JWT ID from token
            decoded = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
            )
            jti = decoded.get("jti")

            if not jti:
                return False

            if self._use_redis and self._redis_client:
                # Check Redis
                return bool(self._redis_client.get(f"blacklist:{jti}"))
            else:
                # Check in-memory storage
                return jti in self._blacklisted_tokens

        except Exception as e:
            logger.error(f"Failed to check token blacklist: {str(e)}")
            return False

    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens from blacklist.

        Returns:
            int: Number of tokens cleaned up
        """
        if self._use_redis:
            # Redis handles expiration automatically
            return 0

        # For in-memory storage, we can't easily determine expiration
        # This would require storing expiration times separately
        # For now, we'll rely on periodic full cleanup
        cleaned = 0
        current_time = datetime.utcnow()

        # Note: This is a simplified cleanup for in-memory storage
        # In production, Redis should be used for automatic expiration
        logger.info(f"Cleaned up {cleaned} expired tokens from blacklist")
        return cleaned


# Global token blacklist manager
token_blacklist = TokenBlacklistManager()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against its hash.

    Args:
        plain_password: The plain text password
        hashed_password: The hashed password to verify against

    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str, validate_strength: bool = True) -> str:
    """
    Generate enhanced password hash with optional strength validation.

    Args:
        password: Plain text password to hash
        validate_strength: Whether to validate password strength (default: True)

    Returns:
        str: Hashed password using bcrypt with configured rounds

    Raises:
        ValueError: If password doesn't meet strength requirements
    """
    if validate_strength:
        # Basic strength validation (detailed validation should use PasswordSecurityService)
        if len(password) < PASSWORD_MIN_LENGTH:
            raise ValueError(f"Password must be at least {PASSWORD_MIN_LENGTH} characters long")

        # Check for basic character requirements
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError("Password must contain uppercase, lowercase, numbers, and special characters")

    # Log password hashing for security audit
    logger.debug(f"Hashing password with {BCRYPT_ROUNDS} rounds")

    return pwd_context.hash(password)


def generate_password_reset_token() -> str:
    """
    Generate a cryptographically secure password reset token.

    This function generates a URL-safe token for password reset functionality.
    For database-backed token management with expiration, use PasswordSecurityService.

    Returns:
        str: Secure random token for password reset (32 bytes, URL-safe)
    """
    token = secrets.token_urlsafe(32)
    logger.debug("Generated secure password reset token")
    return token


def generate_secure_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure random token.

    Args:
        length: Token length in bytes (default: 32)

    Returns:
        str: URL-safe random token
    """
    return secrets.token_urlsafe(length)


def hash_token(token: str) -> str:
    """
    Hash a token for secure storage.

    Args:
        token: Plain text token to hash

    Returns:
        str: SHA-256 hash of the token
    """
    import hashlib
    return hashlib.sha256(token.encode()).hexdigest()


# Data encryption for OAuth tokens and sensitive data
class DataEncryption:
    """
    Data encryption utility for OAuth tokens and sensitive data.

    Uses Fernet symmetric encryption with PBKDF2 key derivation for secure
    encryption of OAuth tokens and other sensitive data.
    """

    def __init__(self, master_key: Optional[str] = None):
        """
        Initialize encryption with master key.

        Args:
            master_key: Master encryption key (uses SECRET_KEY if not provided)
        """
        self.master_key = (master_key or settings.SECRET_KEY).encode()
        self.fernet = self._create_fernet()

    def _create_fernet(self) -> Fernet:
        """Create Fernet instance with derived key."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'culture_connect_oauth_salt',  # Static salt for consistency
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key))
        return Fernet(key)

    def encrypt_field(self, data: str) -> str:
        """
        Encrypt sensitive field data.

        Args:
            data: Plain text data to encrypt

        Returns:
            str: Base64-encoded encrypted data
        """
        if not data:
            return data

        encrypted = self.fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted).decode()

    def decrypt_field(self, encrypted_data: str) -> str:
        """
        Decrypt sensitive field data.

        Args:
            encrypted_data: Base64-encoded encrypted data

        Returns:
            str: Decrypted plain text data
        """
        if not encrypted_data:
            return encrypted_data

        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt data: {str(e)}")
            raise ValueError("Failed to decrypt data")


# Global encryption instance
_encryption_instance = None


def get_encryption_instance() -> DataEncryption:
    """Get global encryption instance."""
    global _encryption_instance
    if _encryption_instance is None:
        _encryption_instance = DataEncryption()
    return _encryption_instance


def encrypt_data(data: str) -> str:
    """
    Encrypt sensitive data using global encryption instance.

    Args:
        data: Plain text data to encrypt

    Returns:
        str: Encrypted data
    """
    return get_encryption_instance().encrypt_field(data)


def decrypt_data(encrypted_data: str) -> str:
    """
    Decrypt sensitive data using global encryption instance.

    Args:
        encrypted_data: Encrypted data to decrypt

    Returns:
        str: Decrypted plain text data
    """
    return get_encryption_instance().decrypt_field(encrypted_data)


def create_access_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create enhanced JWT access token with role-based claims and JTI.

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time delta

    Returns:
        str: Encoded JWT token with JTI for blacklisting support
    """
    to_encode = data.copy()
    now = datetime.utcnow()

    if expires_delta:
        expire = now + expires_delta
    else:
        expire = now + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Add enhanced JWT claims
    jti = str(uuid.uuid4())  # Unique token ID for blacklisting
    to_encode.update({
        "exp": expire,  # Keep as datetime object - jose handles conversion
        "iat": now,     # Keep as datetime object - jose handles conversion
        "type": "access",
        "jti": jti,
        "iss": getattr(settings, 'JWT_ISSUER', 'culture-connect-api'),
        "aud": getattr(settings, 'JWT_AUDIENCE', 'culture-connect-clients')
    })

    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    logger.debug(f"Created access token with JTI: {jti}")
    return encoded_jwt


def create_refresh_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create enhanced JWT refresh token with role-based claims and JTI.

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time delta

    Returns:
        str: Encoded JWT refresh token with JTI for blacklisting support
    """
    to_encode = data.copy()
    now = datetime.utcnow()

    if expires_delta:
        expire = now + expires_delta
    else:
        expire = now + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    # Add enhanced JWT claims
    jti = str(uuid.uuid4())  # Unique token ID for blacklisting
    to_encode.update({
        "exp": expire,  # Keep as datetime object - jose handles conversion
        "iat": now,     # Keep as datetime object - jose handles conversion
        "type": "refresh",
        "jti": jti,
        "iss": getattr(settings, 'JWT_ISSUER', 'culture-connect-api'),
        "aud": getattr(settings, 'JWT_AUDIENCE', 'culture-connect-clients')
    })

    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    logger.debug(f"Created refresh token with JTI: {jti}")
    return encoded_jwt


async def verify_token(token: str, token_type: str = "access") -> Optional[TokenData]:
    """
    Enhanced JWT token verification with blacklist checking and role-based claims.

    Args:
        token: JWT token to verify
        token_type: Expected token type ("access" or "refresh")

    Returns:
        TokenData: Decoded token data if valid, None otherwise

    Raises:
        HTTPException: If token is invalid, expired, or blacklisted
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # First check if token is blacklisted
        if await token_blacklist.is_blacklisted(token):
            logger.warning("Attempted use of blacklisted token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been revoked",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Decode and verify token
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            issuer=getattr(settings, 'JWT_ISSUER', 'culture-connect-api'),
            audience=getattr(settings, 'JWT_AUDIENCE', 'culture-connect-clients')
        )

        # Verify token type
        if payload.get("type") != token_type:
            logger.warning(f"Token type mismatch: expected {token_type}, got {payload.get('type')}")
            raise credentials_exception

        # Extract claims
        user_id_str: str = payload.get("sub")
        email: str = payload.get("email")
        role: str = payload.get("role")
        vendor_id: int = payload.get("vendor_id")
        scopes: List[str] = payload.get("scopes", [])
        jti: str = payload.get("jti")
        iat: datetime = datetime.fromtimestamp(payload.get("iat", 0)) if payload.get("iat") else None
        exp: datetime = datetime.fromtimestamp(payload.get("exp", 0)) if payload.get("exp") else None

        if user_id_str is None:
            logger.warning("Token missing user ID (sub claim)")
            raise credentials_exception

        try:
            user_id = int(user_id_str)  # Convert string back to int
        except (ValueError, TypeError):
            logger.warning(f"Invalid user ID format: {user_id_str}")
            raise credentials_exception

        token_data = TokenData(
            user_id=user_id,
            email=email,
            role=role,
            vendor_id=vendor_id,
            scopes=scopes,
            jti=jti,
            iat=iat,
            exp=exp,
            token_type=token_type
        )

        logger.debug(f"Token verified successfully for user {user_id} with role {role}")
        return token_data

    except JWTError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        raise credentials_exception
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {str(e)}")
        raise credentials_exception


def create_token_pair(user_data: Dict[str, Any]) -> Token:
    """
    Create enhanced access and refresh token pair with role-based claims.

    Args:
        user_data: User data to encode in tokens

    Returns:
        Token: Enhanced token pair with access and refresh tokens including JTI
    """
    access_token = create_access_token(data=user_data)
    refresh_token = create_refresh_token(data=user_data)

    # Extract JTI from access token for tracking
    try:
        access_payload = jwt.decode(
            access_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        jti = access_payload.get("jti", "")
    except Exception:
        jti = ""

    # Calculate scope string from user scopes
    scopes = user_data.get("scopes", [])
    scope_string = " ".join(scopes) if scopes else ""

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        refresh_expires_in=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
        scope=scope_string,
        jti=jti
    )


async def refresh_token_pair(refresh_token: str) -> Token:
    """
    Refresh token pair using valid refresh token.

    Args:
        refresh_token: Valid refresh token

    Returns:
        Token: New token pair

    Raises:
        HTTPException: If refresh token is invalid or blacklisted
    """
    # Verify refresh token
    token_data = await verify_token(refresh_token, token_type="refresh")

    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

    # Blacklist the old refresh token
    await token_blacklist.blacklist_token(refresh_token, token_data.exp)

    # Create new token pair
    user_data = {
        "sub": str(token_data.user_id),
        "email": token_data.email,
        "role": token_data.role,
        "vendor_id": token_data.vendor_id,
        "scopes": token_data.scopes
    }

    new_tokens = create_token_pair(user_data)
    logger.info(f"Token pair refreshed for user {token_data.user_id}")

    return new_tokens


async def logout_user(access_token: str, refresh_token: Optional[str] = None) -> bool:
    """
    Logout user by blacklisting tokens.

    Args:
        access_token: User's access token
        refresh_token: User's refresh token (optional)

    Returns:
        bool: True if logout successful
    """
    try:
        # Blacklist access token
        access_token_data = await verify_token(access_token, token_type="access")
        if access_token_data:
            await token_blacklist.blacklist_token(access_token, access_token_data.exp)
            logger.info(f"Access token blacklisted for user {access_token_data.user_id}")

        # Blacklist refresh token if provided
        if refresh_token:
            refresh_token_data = await verify_token(refresh_token, token_type="refresh")
            if refresh_token_data:
                await token_blacklist.blacklist_token(refresh_token, refresh_token_data.exp)
                logger.info(f"Refresh token blacklisted for user {refresh_token_data.user_id}")

        return True

    except Exception as e:
        logger.error(f"Logout failed: {str(e)}")
        return False


def generate_api_key() -> str:
    """
    Generate a secure API key for vendor integrations.

    Returns:
        str: Secure API key
    """
    return f"cc_{secrets.token_urlsafe(32)}"


def validate_api_key_format(api_key: str) -> bool:
    """
    Validate API key format.

    Args:
        api_key: API key to validate

    Returns:
        bool: True if format is valid, False otherwise
    """
    return api_key.startswith("cc_") and len(api_key) == 46


def check_password_strength(password: str) -> Dict[str, Union[bool, str]]:
    """
    Check password strength and return validation results.

    Args:
        password: Password to validate

    Returns:
        Dict: Validation results with strength indicators
    """
    result = {
        "is_valid": True,
        "errors": [],
        "strength": "weak"
    }

    # Minimum length check
    if len(password) < 8:
        result["is_valid"] = False
        result["errors"].append("Password must be at least 8 characters long")

    # Character type checks
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

    if not has_upper:
        result["is_valid"] = False
        result["errors"].append("Password must contain at least one uppercase letter")

    if not has_lower:
        result["is_valid"] = False
        result["errors"].append("Password must contain at least one lowercase letter")

    if not has_digit:
        result["is_valid"] = False
        result["errors"].append("Password must contain at least one digit")

    if not has_special:
        result["is_valid"] = False
        result["errors"].append("Password must contain at least one special character")

    # Determine strength
    if result["is_valid"]:
        strength_score = sum([has_upper, has_lower, has_digit, has_special])
        if len(password) >= 12 and strength_score == 4:
            result["strength"] = "strong"
        elif len(password) >= 10 and strength_score >= 3:
            result["strength"] = "medium"
        else:
            result["strength"] = "weak"

    return result


def generate_secure_filename(original_filename: str) -> str:
    """
    Generate a secure filename to prevent directory traversal attacks.

    Args:
        original_filename: Original filename from upload

    Returns:
        str: Secure filename with timestamp prefix
    """
    import os
    import time

    # Extract file extension
    _, ext = os.path.splitext(original_filename)

    # Generate secure filename with timestamp
    timestamp = int(time.time())
    random_suffix = secrets.token_hex(8)

    return f"{timestamp}_{random_suffix}{ext}"


def sanitize_input(input_string: str, max_length: int = 1000) -> str:
    """
    Sanitize user input to prevent injection attacks.

    Args:
        input_string: Input string to sanitize
        max_length: Maximum allowed length

    Returns:
        str: Sanitized input string
    """
    if not input_string:
        return ""

    # Truncate to max length
    sanitized = input_string[:max_length]

    # Remove null bytes and control characters
    sanitized = "".join(char for char in sanitized if ord(char) >= 32 or char in "\n\r\t")

    # Strip whitespace
    sanitized = sanitized.strip()

    return sanitized


# Role-based access control constants
class UserRole:
    """User role constants."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    VENDOR = "vendor"
    CUSTOMER = "customer"
    MODERATOR = "moderator"


class Permission:
    """Permission constants."""
    # User management
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"

    # Vendor management
    CREATE_VENDOR = "create_vendor"
    READ_VENDOR = "read_vendor"
    UPDATE_VENDOR = "update_vendor"
    DELETE_VENDOR = "delete_vendor"

    # Service management
    CREATE_SERVICE = "create_service"
    READ_SERVICE = "read_service"
    UPDATE_SERVICE = "update_service"
    DELETE_SERVICE = "delete_service"

    # Booking management
    CREATE_BOOKING = "create_booking"
    READ_BOOKING = "read_booking"
    UPDATE_BOOKING = "update_booking"
    DELETE_BOOKING = "delete_booking"

    # Payment management
    PROCESS_PAYMENT = "process_payment"
    REFUND_PAYMENT = "refund_payment"
    VIEW_FINANCIAL_DATA = "view_financial_data"

    # Promotional system
    CREATE_CAMPAIGN = "create_campaign"
    MANAGE_CAMPAIGN = "manage_campaign"
    APPROVE_CAMPAIGN = "approve_campaign"
    CREATE_ADVERTISEMENT = "create_advertisement"
    MANAGE_ADVERTISEMENT = "manage_advertisement"
    APPROVE_ADVERTISEMENT = "approve_advertisement"
    VIEW_METRICS = "view_metrics"
    MANAGE_SPEND = "manage_spend"
    VIEW_ANALYTICS = "view_analytics"

    # System administration
    MANAGE_SYSTEM = "manage_system"
    VIEW_LOGS = "view_logs"


# Role-permission mapping
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: [
        # All permissions
        Permission.CREATE_USER, Permission.READ_USER, Permission.UPDATE_USER, Permission.DELETE_USER,
        Permission.CREATE_VENDOR, Permission.READ_VENDOR, Permission.UPDATE_VENDOR, Permission.DELETE_VENDOR,
        Permission.CREATE_SERVICE, Permission.READ_SERVICE, Permission.UPDATE_SERVICE, Permission.DELETE_SERVICE,
        Permission.CREATE_BOOKING, Permission.READ_BOOKING, Permission.UPDATE_BOOKING, Permission.DELETE_BOOKING,
        Permission.PROCESS_PAYMENT, Permission.REFUND_PAYMENT, Permission.VIEW_FINANCIAL_DATA,
        Permission.CREATE_CAMPAIGN, Permission.MANAGE_CAMPAIGN, Permission.APPROVE_CAMPAIGN,
        Permission.CREATE_ADVERTISEMENT, Permission.MANAGE_ADVERTISEMENT, Permission.APPROVE_ADVERTISEMENT,
        Permission.VIEW_METRICS, Permission.MANAGE_SPEND, Permission.VIEW_ANALYTICS,
        Permission.MANAGE_SYSTEM, Permission.VIEW_LOGS,
    ],
    UserRole.ADMIN: [
        Permission.READ_USER, Permission.UPDATE_USER,
        Permission.READ_VENDOR, Permission.UPDATE_VENDOR,
        Permission.READ_SERVICE, Permission.UPDATE_SERVICE,
        Permission.READ_BOOKING, Permission.UPDATE_BOOKING,
        Permission.VIEW_FINANCIAL_DATA,
        Permission.MANAGE_CAMPAIGN, Permission.APPROVE_CAMPAIGN, Permission.APPROVE_ADVERTISEMENT,
        Permission.VIEW_METRICS, Permission.MANAGE_SPEND, Permission.VIEW_ANALYTICS,
        Permission.VIEW_LOGS,
    ],
    UserRole.VENDOR: [
        Permission.READ_USER,
        Permission.READ_VENDOR, Permission.UPDATE_VENDOR,
        Permission.CREATE_SERVICE, Permission.READ_SERVICE, Permission.UPDATE_SERVICE, Permission.DELETE_SERVICE,
        Permission.READ_BOOKING, Permission.UPDATE_BOOKING,
        Permission.CREATE_CAMPAIGN, Permission.MANAGE_CAMPAIGN, Permission.CREATE_ADVERTISEMENT,
        Permission.MANAGE_ADVERTISEMENT, Permission.VIEW_METRICS, Permission.MANAGE_SPEND, Permission.VIEW_ANALYTICS,
    ],
    UserRole.CUSTOMER: [
        Permission.READ_USER, Permission.UPDATE_USER,
        Permission.READ_VENDOR,
        Permission.READ_SERVICE,
        Permission.CREATE_BOOKING, Permission.READ_BOOKING, Permission.UPDATE_BOOKING,
    ],
    UserRole.MODERATOR: [
        Permission.READ_USER,
        Permission.READ_VENDOR, Permission.UPDATE_VENDOR,
        Permission.READ_SERVICE, Permission.UPDATE_SERVICE,
        Permission.READ_BOOKING,
        Permission.VIEW_ANALYTICS,
    ],
}


def has_permission(user_role: str, permission: str) -> bool:
    """
    Check if a user role has a specific permission.

    Args:
        user_role: User's role
        permission: Permission to check

    Returns:
        bool: True if user has permission, False otherwise
    """
    return permission in ROLE_PERMISSIONS.get(user_role, [])


def get_user_permissions(user_role: str) -> list[str]:
    """
    Get all permissions for a user role.

    Args:
        user_role: User's role

    Returns:
        list: List of permissions for the role
    """
    return ROLE_PERMISSIONS.get(user_role, [])


# Password Security Event Logging

class PasswordSecurityEvent:
    """Password security event types for audit logging."""
    PASSWORD_CHANGED = "password_changed"
    PASSWORD_RESET_REQUESTED = "password_reset_requested"
    PASSWORD_RESET_COMPLETED = "password_reset_completed"
    PASSWORD_STRENGTH_VALIDATION_FAILED = "password_strength_validation_failed"
    PASSWORD_HISTORY_VIOLATION = "password_history_violation"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    FAILED_LOGIN_ATTEMPT = "failed_login_attempt"
    PASSWORD_RESET_TOKEN_EXPIRED = "password_reset_token_expired"
    PASSWORD_RESET_TOKEN_INVALID = "password_reset_token_invalid"


def log_password_security_event(
    event_type: str,
    user_id: Optional[int] = None,
    client_ip: Optional[str] = None,
    user_agent: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log password security events for audit and monitoring.

    Args:
        event_type: Type of security event
        user_id: User ID associated with the event
        client_ip: Client IP address
        user_agent: Client user agent
        details: Additional event details
    """
    log_data = {
        "event_type": event_type,
        "user_id": user_id,
        "client_ip": client_ip,
        "user_agent": user_agent,
        "timestamp": datetime.utcnow().isoformat(),
        "details": details or {}
    }

    logger.info(
        f"Password security event: {event_type}",
        extra={
            "security_event": True,
            "event_data": log_data
        }
    )


def validate_password_complexity(password: str) -> Dict[str, Any]:
    """
    Validate password complexity and return detailed results.

    Args:
        password: Password to validate

    Returns:
        Dict[str, Any]: Validation results with details
    """
    results = {
        "is_valid": True,
        "violations": [],
        "score": 0
    }

    # Length check
    if len(password) < PASSWORD_MIN_LENGTH:
        results["is_valid"] = False
        results["violations"].append(f"Password must be at least {PASSWORD_MIN_LENGTH} characters")
    else:
        results["score"] += 25

    # Character type checks
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

    if not has_upper:
        results["is_valid"] = False
        results["violations"].append("Password must contain uppercase letters")
    else:
        results["score"] += 20

    if not has_lower:
        results["is_valid"] = False
        results["violations"].append("Password must contain lowercase letters")
    else:
        results["score"] += 20

    if not has_digit:
        results["is_valid"] = False
        results["violations"].append("Password must contain numbers")
    else:
        results["score"] += 20

    if not has_special:
        results["is_valid"] = False
        results["violations"].append("Password must contain special characters")
    else:
        results["score"] += 15

    return results


def is_account_lockout_enabled() -> bool:
    """
    Check if account lockout is enabled.

    Returns:
        bool: True if account lockout is enabled
    """
    return ACCOUNT_LOCKOUT_ATTEMPTS > 0


def get_password_security_config() -> Dict[str, Any]:
    """
    Get current password security configuration.

    Returns:
        Dict[str, Any]: Password security configuration
    """
    return {
        "bcrypt_rounds": BCRYPT_ROUNDS,
        "min_length": PASSWORD_MIN_LENGTH,
        "history_count": PASSWORD_HISTORY_COUNT,
        "lockout_attempts": ACCOUNT_LOCKOUT_ATTEMPTS,
        "lockout_duration_minutes": ACCOUNT_LOCKOUT_DURATION_MINUTES,
        "reset_token_expire_hours": PASSWORD_RESET_TOKEN_EXPIRE_HOURS,
        "lockout_enabled": is_account_lockout_enabled()
    }
