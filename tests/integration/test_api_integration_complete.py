"""
Complete API Integration Tests for Culture Connect Backend API.

This module provides comprehensive API integration validation testing including:
- Multi-Endpoint Workflow Testing (complete API endpoint combinations with authentication flow)
- Error Propagation and Rollback Testing (cross-service error handling with database consistency)
- Performance Integration Validation (response time validation during integrated operations)

Implements Phase 8.1: Integration Testing Implementation with >80% test coverage,
100% API workflow success rate, and comprehensive error handling validation.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock

from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.main import app
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VendorStatus
from app.models.booking import Booking, BookingStatus
from app.models.payment_models import Payment, PaymentStatus, PaymentProvider
from app.models.booking_communication import BookingMessage, MessageType, MessageStatus
from app.services.auth_service import AuthService
from app.services.vendor_service import VendorService
from app.services.booking_service import BookingService
from app.services.payment_service import PaymentService
from app.services.booking_communication_service import BookingCommunicationService
from app.db.session import get_async_session_context
from app.core.security import create_token_pair
from app.core.logging import get_logger

logger = get_logger(__name__)


@pytest.mark.integration
class TestAPIIntegrationComplete:
    """Comprehensive API integration validation tests."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client with real app."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    async def db_session(self):
        """Create database session with transaction rollback."""
        async with get_async_session_context() as session:
            transaction = await session.begin()
            try:
                yield session
            finally:
                await transaction.rollback()

    @pytest.fixture
    async def test_customer_user(self, db_session: AsyncSession):
        """Create test customer user for API integration testing."""
        user_data = {
            "email": f"api_customer_{uuid4().hex[:8]}@test.com",
            "first_name": "API",
            "last_name": "Customer",
            "role": UserRole.CUSTOMER,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor_user(self, db_session: AsyncSession):
        """Create test vendor user for API integration testing."""
        user_data = {
            "email": f"api_vendor_{uuid4().hex[:8]}@test.com",
            "first_name": "API",
            "last_name": "Vendor",
            "role": UserRole.VENDOR,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession, test_vendor_user: User):
        """Create test vendor for API integration testing."""
        vendor_data = {
            "user_id": test_vendor_user.id,
            "business_name": "API Integration Tours",
            "business_type": VendorType.TOUR_GUIDE,
            "status": VendorStatus.ACTIVE,
            "verified": True,
            "phone": "+************",
            "address": "Lagos, Nigeria"
        }

        vendor = Vendor(**vendor_data)
        db_session.add(vendor)
        await db_session.commit()
        await db_session.refresh(vendor)
        return vendor

    @pytest.fixture
    def customer_auth_headers(self, test_customer_user: User):
        """Create authentication headers for customer user."""
        token_data = {
            "sub": str(test_customer_user.id),
            "email": test_customer_user.email,
            "role": test_customer_user.role.value,
            "scopes": ["customer:read", "customer:write", "booking:create", "payment:create"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def vendor_auth_headers(self, test_vendor_user: User):
        """Create authentication headers for vendor user."""
        token_data = {
            "sub": str(test_vendor_user.id),
            "email": test_vendor_user.email,
            "role": test_vendor_user.role.value,
            "scopes": ["vendor:read", "vendor:write", "booking:manage", "dashboard:access"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def performance_timer(self):
        """Performance timing utility for API integration tests."""
        class PerformanceTimer:
            def __init__(self):
                self.start_time = None
                self.end_time = None

            def start(self):
                self.start_time = time.perf_counter()

            def stop(self):
                self.end_time = time.perf_counter()
                return (self.end_time - self.start_time) * 1000  # Return milliseconds

        return PerformanceTimer()

    @pytest.mark.asyncio
    async def test_multi_endpoint_workflow_testing(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor: Vendor,
        customer_auth_headers: Dict[str, str],
        vendor_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Multi-Endpoint Workflow with complete API endpoint combinations and authentication flow.

        Validates: Authentication → Vendor Management → Booking → Payment → Communication API workflow
        """
        correlation_id = f"api_workflow_{uuid4().hex[:8]}"

        # Add correlation ID to headers
        customer_headers = {**customer_auth_headers, "X-Correlation-ID": correlation_id}
        vendor_headers = {**vendor_auth_headers, "X-Correlation-ID": correlation_id}

        logger.info(f"Starting multi-endpoint workflow test with correlation_id: {correlation_id}")

        # Step 1: Authentication Flow Integration - Customer Profile Access
        performance_timer.start()
        profile_response = await async_client.get(
            "/api/v1/users/profile",
            headers=customer_headers
        )
        profile_time = performance_timer.stop()

        assert profile_response.status_code == status.HTTP_200_OK
        assert profile_time < 200  # <200ms performance target
        profile_data = profile_response.json()
        assert profile_data["email"] == test_customer_user.email

        # Step 2: Vendor Management API Integration - Vendor Search
        performance_timer.start()
        vendor_search_response = await async_client.get(
            "/api/v1/vendors/search",
            params={"location": "Lagos", "service_type": "cultural_tour"},
            headers=customer_headers
        )
        vendor_search_time = performance_timer.stop()

        assert vendor_search_response.status_code == status.HTTP_200_OK
        assert vendor_search_time < 200  # <200ms performance target

        # Step 3: Vendor Details API Integration
        performance_timer.start()
        vendor_details_response = await async_client.get(
            f"/api/v1/vendors/{test_vendor.id}",
            headers=customer_headers
        )
        vendor_details_time = performance_timer.stop()

        assert vendor_details_response.status_code == status.HTTP_200_OK
        assert vendor_details_time < 200  # <200ms performance target
        vendor_data = vendor_details_response.json()
        assert vendor_data["id"] == test_vendor.id

        # Step 4: Booking API Integration - Create Booking
        booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "total_amount": 350.00,
            "number_of_guests": 3,
            "special_requests": "API integration test booking"
        }

        performance_timer.start()
        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=booking_data,
            headers=customer_headers
        )
        booking_time = performance_timer.stop()

        assert booking_response.status_code == status.HTTP_201_CREATED
        assert booking_time < 500  # <500ms performance target
        booking_result = booking_response.json()
        booking_id = booking_result["id"]

        # Step 5: Payment API Integration - Initialize Payment
        payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("350.00"),
            "currency": "NGN",
            "payment_method": "card",
            "provider": PaymentProvider.PAYSTACK.value
        }

        performance_timer.start()
        with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
            mock_payment.return_value = {
                "status": "success",
                "data": {
                    "authorization_url": "https://checkout.paystack.com/test",
                    "reference": f"ref_{correlation_id}"
                }
            }

            payment_response = await async_client.post(
                "/api/v1/payments/initialize",
                json=payment_data,
                headers=customer_headers
            )
        payment_time = performance_timer.stop()

        assert payment_response.status_code == status.HTTP_200_OK
        assert payment_time < 500  # <500ms performance target

        # Step 6: Communication API Integration - Send Message
        message_data = {
            "booking_id": booking_id,
            "message_type": MessageType.CUSTOMER_MESSAGE.value,
            "content": "API integration test message - looking forward to the tour!",
            "sender_type": "customer"
        }

        performance_timer.start()
        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=customer_headers
        )
        message_time = performance_timer.stop()

        assert message_response.status_code == status.HTTP_201_CREATED
        assert message_time < 500  # <500ms performance target

        # Step 7: Analytics API Integration - User Dashboard
        performance_timer.start()
        analytics_response = await async_client.get(
            "/api/v1/analytics/user/dashboard",
            headers=customer_headers
        )
        analytics_time = performance_timer.stop()

        assert analytics_response.status_code == status.HTTP_200_OK
        assert analytics_time < 200  # <200ms performance target

        # Step 8: Vendor API Integration - Vendor Dashboard
        performance_timer.start()
        vendor_dashboard_response = await async_client.get(
            "/api/v1/vendors/dashboard/overview",
            headers=vendor_headers
        )
        dashboard_time = performance_timer.stop()

        assert vendor_dashboard_response.status_code == status.HTTP_200_OK
        assert dashboard_time < 200  # <200ms performance target

        # Step 9: Database Consistency Validation Across All APIs
        async with get_async_session_context() as session:
            # Verify booking created by API workflow
            booking = await session.get(Booking, booking_id)
            assert booking is not None
            assert booking.customer_id == test_customer_user.id
            assert booking.vendor_id == test_vendor.id

            # Verify message created by API workflow
            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None
            assert message.booking_id == booking_id

            # Verify all foreign key relationships are intact
            assert booking.customer is not None
            assert booking.vendor is not None
            assert message.booking is not None

        logger.info(f"Multi-endpoint workflow test completed successfully with correlation_id: {correlation_id}")

    @pytest.mark.asyncio
    async def test_error_propagation_and_rollback_testing(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor: Vendor,
        customer_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Error Propagation and Rollback with cross-service error handling and database consistency.

        Validates: Error handling across service boundaries with proper database rollback
        """
        correlation_id = f"api_error_test_{uuid4().hex[:8]}"
        customer_headers = {**customer_auth_headers, "X-Correlation-ID": correlation_id}

        logger.info(f"Starting error propagation test with correlation_id: {correlation_id}")

        # Step 1: Test Invalid Booking Creation (Non-existent Vendor)
        invalid_booking_data = {
            "vendor_id": 99999,  # Non-existent vendor
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "total_amount": 200.00,
            "number_of_guests": 2
        }

        performance_timer.start()
        invalid_booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=invalid_booking_data,
            headers=customer_headers
        )
        error_time = performance_timer.stop()

        assert invalid_booking_response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
        assert error_time < 500  # <500ms performance target for errors

        # Step 2: Verify Database State Consistency After Error
        async with get_async_session_context() as session:
            # Verify no booking was created
            result = await session.execute(
                text("SELECT COUNT(*) FROM bookings WHERE vendor_id = 99999")
            )
            count = result.scalar()
            assert count == 0

        # Step 3: Test Payment Error Propagation
        # First create a valid booking
        valid_booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=5)).isoformat(),
            "total_amount": 300.00,
            "number_of_guests": 2
        }

        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=valid_booking_data,
            headers=customer_headers
        )
        assert booking_response.status_code == status.HTTP_201_CREATED
        booking_result = booking_response.json()
        booking_id = booking_result["id"]

        # Test payment error with invalid data
        invalid_payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("-100.00"),  # Invalid negative amount
            "currency": "INVALID",  # Invalid currency
            "payment_method": "invalid_method"
        }

        performance_timer.start()
        payment_error_response = await async_client.post(
            "/api/v1/payments/initialize",
            json=invalid_payment_data,
            headers=customer_headers
        )
        payment_error_time = performance_timer.stop()

        assert payment_error_response.status_code == status.HTTP_400_BAD_REQUEST
        assert payment_error_time < 500  # <500ms performance target

        # Step 4: Test Communication Error Propagation
        invalid_message_data = {
            "booking_id": 99999,  # Non-existent booking
            "message_type": "INVALID_TYPE",
            "content": "",  # Empty content
            "sender_type": "invalid_sender"
        }

        performance_timer.start()
        message_error_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=invalid_message_data,
            headers=customer_headers
        )
        message_error_time = performance_timer.stop()

        assert message_error_response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
        assert message_error_time < 500  # <500ms performance target

        # Step 5: Test Authentication Error Propagation
        invalid_headers = {"Authorization": "Bearer invalid_token"}

        performance_timer.start()
        auth_error_response = await async_client.get(
            "/api/v1/users/profile",
            headers=invalid_headers
        )
        auth_error_time = performance_timer.stop()

        assert auth_error_response.status_code == status.HTTP_401_UNAUTHORIZED
        assert auth_error_time < 200  # <200ms performance target

        # Step 6: Verify Database Consistency After All Errors
        async with get_async_session_context() as session:
            # Verify valid booking still exists
            booking = await session.get(Booking, booking_id)
            assert booking is not None
            assert booking.customer_id == test_customer_user.id

            # Verify no invalid payments were created
            result = await session.execute(
                text("SELECT COUNT(*) FROM payments WHERE amount < 0")
            )
            invalid_payments = result.scalar()
            assert invalid_payments == 0

            # Verify no invalid messages were created
            result = await session.execute(
                text("SELECT COUNT(*) FROM booking_messages WHERE booking_id = 99999")
            )
            invalid_messages = result.scalar()
            assert invalid_messages == 0

        logger.info(f"Error propagation test completed successfully with correlation_id: {correlation_id}")

    @pytest.mark.asyncio
    async def test_performance_integration_validation(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_customer_user: User,
        test_vendor: Vendor,
        customer_auth_headers: Dict[str, str],
        vendor_auth_headers: Dict[str, str],
        performance_timer
    ):
        """
        Test Performance Integration Validation with response time validation during integrated operations.

        Validates: Performance targets maintained during complex API integration scenarios
        """
        correlation_id = f"api_performance_{uuid4().hex[:8]}"
        customer_headers = {**customer_auth_headers, "X-Correlation-ID": correlation_id}
        vendor_headers = {**vendor_auth_headers, "X-Correlation-ID": correlation_id}

        logger.info(f"Starting performance integration test with correlation_id: {correlation_id}")

        # Step 1: Concurrent API Requests Performance Testing
        performance_timer.start()

        # Create multiple concurrent API requests
        concurrent_tasks = [
            async_client.get("/api/v1/users/profile", headers=customer_headers),
            async_client.get("/api/v1/vendors/search", params={"location": "Lagos"}, headers=customer_headers),
            async_client.get(f"/api/v1/vendors/{test_vendor.id}", headers=customer_headers),
            async_client.get("/api/v1/vendors/dashboard/overview", headers=vendor_headers),
            async_client.get("/api/v1/analytics/user/dashboard", headers=customer_headers)
        ]

        # Execute concurrent requests
        concurrent_responses = await asyncio.gather(*concurrent_tasks)
        concurrent_time = performance_timer.stop()

        # Verify all requests succeeded
        for response in concurrent_responses:
            assert response.status_code == status.HTTP_200_OK

        # Verify concurrent performance
        assert concurrent_time < 1000  # <1000ms for 5 concurrent requests
        average_response_time = concurrent_time / 5
        assert average_response_time < 200  # <200ms average response time

        # Step 2: Complex Workflow Performance Testing
        workflow_start_time = time.perf_counter()

        # Create booking
        booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=4)).isoformat(),
            "total_amount": 450.00,
            "number_of_guests": 4
        }

        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=booking_data,
            headers=customer_headers
        )
        assert booking_response.status_code == status.HTTP_201_CREATED
        booking_id = booking_response.json()["id"]

        # Initialize payment
        payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("450.00"),
            "currency": "NGN",
            "payment_method": "card"
        }

        with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
            mock_payment.return_value = {
                "status": "success",
                "data": {"reference": f"ref_{correlation_id}"}
            }

            payment_response = await async_client.post(
                "/api/v1/payments/initialize",
                json=payment_data,
                headers=customer_headers
            )
        assert payment_response.status_code == status.HTTP_200_OK

        # Send message
        message_data = {
            "booking_id": booking_id,
            "message_type": MessageType.CUSTOMER_MESSAGE.value,
            "content": "Performance test message",
            "sender_type": "customer"
        }

        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=customer_headers
        )
        assert message_response.status_code == status.HTTP_201_CREATED

        workflow_end_time = time.perf_counter()
        total_workflow_time = (workflow_end_time - workflow_start_time) * 1000

        # Verify complex workflow performance
        assert total_workflow_time < 2000  # <2000ms for complete workflow

        # Step 3: Database Performance Under Load
        performance_timer.start()

        # Multiple database queries
        db_tasks = [
            async_client.get("/api/v1/vendors/bookings", headers=vendor_headers),
            async_client.get("/api/v1/booking-communication/messages", params={"booking_id": booking_id}, headers=customer_headers),
            async_client.get("/api/v1/analytics/vendor/dashboard", headers=vendor_headers)
        ]

        db_responses = await asyncio.gather(*db_tasks)
        db_time = performance_timer.stop()

        # Verify all database queries succeeded
        for response in db_responses:
            assert response.status_code == status.HTTP_200_OK

        # Verify database performance
        assert db_time < 600  # <600ms for multiple database operations

        # Step 4: Memory and Resource Usage Validation
        # Verify no memory leaks or resource issues during integration
        async with get_async_session_context() as session:
            # Verify all created records exist
            booking = await session.get(Booking, booking_id)
            assert booking is not None

            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None

            # Verify relationships are properly loaded
            assert booking.customer is not None
            assert booking.vendor is not None
            assert message.booking is not None

        logger.info(f"Performance integration test completed successfully with correlation_id: {correlation_id}")
        logger.info(f"Performance metrics - Concurrent: {concurrent_time:.2f}ms, Workflow: {total_workflow_time:.2f}ms, DB: {db_time:.2f}ms")
