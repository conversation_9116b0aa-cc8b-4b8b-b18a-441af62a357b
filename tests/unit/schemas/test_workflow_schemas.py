"""
Unit tests for workflow schemas.

Tests for Task 6.2.2: Background Job Processing Implementation
- WorkflowDefinition schema tests
- WorkflowExecution schema tests
- JobDependency schema tests
- WorkflowStep schema tests
- JobSchedule schema tests
- WorkflowAlert schema tests
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from uuid import uuid4

from pydantic import ValidationError

from app.schemas.workflow_schemas import (
    WorkflowDefinitionCreate, WorkflowDefinitionUpdate, WorkflowDefinitionResponse,
    WorkflowExecutionCreate, WorkflowExecutionUpdate, WorkflowExecutionResponse,
    JobDependencyCreate, JobDependencyResponse,
    WorkflowStepCreate, WorkflowStepUpdate, WorkflowStepResponse,
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse,
    WorkflowAlertCreate, WorkflowAlertUpdate, WorkflowAlertResponse,
    WorkflowMetrics, WorkflowExecutionMetrics, JobDependencyStatus,
    WorkflowError, ValidationError as WorkflowValidationError
)
from app.models.workflow_models import (
    WorkflowStatus, ExecutionStatus, DependencyType, StepStatus, AlertSeverity
)


class TestWorkflowDefinitionSchemas:
    """Test WorkflowDefinition schemas."""

    def test_workflow_definition_create_valid(self):
        """Test valid workflow definition creation."""
        data = {
            "name": "Test Workflow",
            "description": "A test workflow",
            "version": "1.0.0",
            "status": WorkflowStatus.DRAFT,
            "configuration": {"key": "value"},
            "tags": ["test", "workflow"],
            "workflow_metadata": {"author": "test"},
            "max_execution_time": 3600,
            "max_retries": 3,
            "retry_delay": 60
        }

        schema = WorkflowDefinitionCreate(**data)

        assert schema.name == "Test Workflow"
        assert schema.description == "A test workflow"
        assert schema.version == "1.0.0"
        assert schema.status == WorkflowStatus.DRAFT
        assert schema.configuration == {"key": "value"}
        assert schema.tags == ["test", "workflow"]
        assert schema.workflow_metadata == {"author": "test"}
        assert schema.max_execution_time == 3600
        assert schema.max_retries == 3
        assert schema.retry_delay == 60

    def test_workflow_definition_create_defaults(self):
        """Test workflow definition creation with defaults."""
        data = {"name": "Test Workflow"}

        schema = WorkflowDefinitionCreate(**data)

        assert schema.name == "Test Workflow"
        assert schema.version == "1.0.0"
        assert schema.status == WorkflowStatus.DRAFT
        assert schema.configuration == {}
        assert schema.tags == []
        assert schema.workflow_metadata == {}
        assert schema.max_retries == 3
        assert schema.retry_delay == 60

    def test_workflow_definition_create_validation(self):
        """Test workflow definition validation."""
        # Test empty name
        with pytest.raises(ValidationError) as exc_info:
            WorkflowDefinitionCreate(name="")
        assert "String should have at least 1 character" in str(exc_info.value)

        # Test negative max_retries
        with pytest.raises(ValidationError) as exc_info:
            WorkflowDefinitionCreate(name="Test", max_retries=-1)
        assert "Input should be greater than or equal to 0" in str(exc_info.value)

        # Test negative max_execution_time
        with pytest.raises(ValidationError) as exc_info:
            WorkflowDefinitionCreate(name="Test", max_execution_time=-1)
        assert "Input should be greater than 0" in str(exc_info.value)

    def test_workflow_definition_tags_validation(self):
        """Test tags validation."""
        # Test too many tags
        tags = [f"tag{i}" for i in range(25)]  # More than 20 tags
        with pytest.raises(ValidationError) as exc_info:
            WorkflowDefinitionCreate(name="Test", tags=tags)
        assert "Maximum 20 tags allowed" in str(exc_info.value)

        # Test tag normalization
        schema = WorkflowDefinitionCreate(
            name="Test",
            tags=["  Test  ", "WORKFLOW ", " api"]
        )
        assert schema.tags == ["test", "workflow", "api"]

    def test_workflow_definition_update_partial(self):
        """Test partial workflow definition update."""
        data = {
            "name": "Updated Workflow",
            "status": WorkflowStatus.ACTIVE
        }

        schema = WorkflowDefinitionUpdate(**data)

        assert schema.name == "Updated Workflow"
        assert schema.status == WorkflowStatus.ACTIVE
        assert schema.description is None
        assert schema.configuration is None


class TestWorkflowExecutionSchemas:
    """Test WorkflowExecution schemas."""

    def test_workflow_execution_create_valid(self):
        """Test valid workflow execution creation."""
        workflow_id = uuid4()
        data = {
            "workflow_definition_id": workflow_id,
            "execution_context": {"context": "test"},
            "input_data": {"input": "data"},
            "triggered_by": "manual",
            "trigger_data": {"user": "test"},
            "correlation_id": "test-correlation-123"
        }

        schema = WorkflowExecutionCreate(**data)

        assert schema.workflow_definition_id == workflow_id
        assert schema.execution_context == {"context": "test"}
        assert schema.input_data == {"input": "data"}
        assert schema.triggered_by == "manual"
        assert schema.trigger_data == {"user": "test"}
        assert schema.correlation_id == "test-correlation-123"

    def test_workflow_execution_create_defaults(self):
        """Test workflow execution creation with defaults."""
        workflow_id = uuid4()
        data = {"workflow_definition_id": workflow_id}

        schema = WorkflowExecutionCreate(**data)

        assert schema.workflow_definition_id == workflow_id
        assert schema.execution_context == {}
        assert schema.input_data == {}
        assert schema.trigger_data == {}

    def test_workflow_execution_update_timing(self):
        """Test workflow execution update with timing."""
        start_time = datetime.now(timezone.utc)
        end_time = datetime.now(timezone.utc)
        duration = Decimal("123.456")

        data = {
            "status": ExecutionStatus.COMPLETED,
            "started_at": start_time,
            "completed_at": end_time,
            "duration_seconds": duration,
            "output_data": {"result": "success"}
        }

        schema = WorkflowExecutionUpdate(**data)

        assert schema.status == ExecutionStatus.COMPLETED
        assert schema.started_at == start_time
        assert schema.completed_at == end_time
        assert schema.duration_seconds == duration
        assert schema.output_data == {"result": "success"}


class TestJobDependencySchemas:
    """Test JobDependency schemas."""

    def test_job_dependency_create_valid(self):
        """Test valid job dependency creation."""
        data = {
            "parent_job_id": "parent-job-123",
            "child_job_id": "child-job-456",
            "dependency_type": DependencyType.SUCCESS,
            "condition_expression": "result.status == 'success'",
            "condition_data": {"threshold": 100},
            "timeout_seconds": 300,
            "max_wait_time": 600,
            "dependency_metadata": {"priority": "high"}
        }

        schema = JobDependencyCreate(**data)

        assert schema.parent_job_id == "parent-job-123"
        assert schema.child_job_id == "child-job-456"
        assert schema.dependency_type == DependencyType.SUCCESS
        assert schema.condition_expression == "result.status == 'success'"
        assert schema.condition_data == {"threshold": 100}
        assert schema.timeout_seconds == 300
        assert schema.max_wait_time == 600
        assert schema.dependency_metadata == {"priority": "high"}

    def test_job_dependency_create_defaults(self):
        """Test job dependency creation with defaults."""
        data = {
            "parent_job_id": "parent-job-123",
            "child_job_id": "child-job-456"
        }

        schema = JobDependencyCreate(**data)

        assert schema.parent_job_id == "parent-job-123"
        assert schema.child_job_id == "child-job-456"
        assert schema.dependency_type == DependencyType.SUCCESS
        assert schema.condition_data == {}
        assert schema.dependency_metadata == {}

    def test_job_dependency_conditional_validation(self):
        """Test conditional dependency validation."""
        # Test conditional dependency without expression
        with pytest.raises(ValidationError) as exc_info:
            JobDependencyCreate(
                parent_job_id="parent",
                child_job_id="child",
                dependency_type=DependencyType.CONDITIONAL
            )
        assert "Condition expression required for conditional dependencies" in str(exc_info.value)

        # Test conditional dependency with expression
        schema = JobDependencyCreate(
            parent_job_id="parent",
            child_job_id="child",
            dependency_type=DependencyType.CONDITIONAL,
            condition_expression="result.value > 100"
        )
        assert schema.condition_expression == "result.value > 100"

    def test_job_dependency_validation(self):
        """Test job dependency validation."""
        # Test empty parent_job_id
        with pytest.raises(ValidationError) as exc_info:
            JobDependencyCreate(parent_job_id="", child_job_id="child")
        assert "String should have at least 1 character" in str(exc_info.value)

        # Test negative timeout
        with pytest.raises(ValidationError) as exc_info:
            JobDependencyCreate(
                parent_job_id="parent",
                child_job_id="child",
                timeout_seconds=-1
            )
        assert "Input should be greater than 0" in str(exc_info.value)


class TestWorkflowStepSchemas:
    """Test WorkflowStep schemas."""

    def test_workflow_step_create_valid(self):
        """Test valid workflow step creation."""
        workflow_id = uuid4()
        data = {
            "workflow_definition_id": workflow_id,
            "name": "Test Step",
            "description": "A test step",
            "step_order": 1,
            "task_name": "test_task",
            "task_configuration": {"param": "value"},
            "task_queue": "default",
            "task_priority": 5,
            "timeout_seconds": 300,
            "max_retries": 3,
            "retry_delay": 60,
            "condition_expression": "input.ready == true",
            "skip_on_failure": False,
            "continue_on_failure": True,
            "depends_on_steps": ["step1", "step2"],
            "tags": ["test", "step"],
            "step_metadata": {"category": "processing"}
        }

        schema = WorkflowStepCreate(**data)

        assert schema.workflow_definition_id == workflow_id
        assert schema.name == "Test Step"
        assert schema.description == "A test step"
        assert schema.step_order == 1
        assert schema.task_name == "test_task"
        assert schema.task_configuration == {"param": "value"}
        assert schema.task_queue == "default"
        assert schema.task_priority == 5
        assert schema.timeout_seconds == 300
        assert schema.max_retries == 3
        assert schema.retry_delay == 60
        assert schema.condition_expression == "input.ready == true"
        assert schema.skip_on_failure is False
        assert schema.continue_on_failure is True
        assert schema.depends_on_steps == ["step1", "step2"]
        assert schema.tags == ["test", "step"]
        assert schema.step_metadata == {"category": "processing"}

    def test_workflow_step_create_defaults(self):
        """Test workflow step creation with defaults."""
        workflow_id = uuid4()
        data = {
            "workflow_definition_id": workflow_id,
            "name": "Test Step",
            "step_order": 1,
            "task_name": "test_task"
        }

        schema = WorkflowStepCreate(**data)

        assert schema.workflow_definition_id == workflow_id
        assert schema.name == "Test Step"
        assert schema.step_order == 1
        assert schema.task_name == "test_task"
        assert schema.task_configuration == {}
        assert schema.task_priority == 5
        assert schema.max_retries == 3
        assert schema.retry_delay == 60
        assert schema.skip_on_failure is False
        assert schema.continue_on_failure is False
        assert schema.depends_on_steps == []
        assert schema.tags == []
        assert schema.step_metadata == {}

    def test_workflow_step_validation(self):
        """Test workflow step validation."""
        workflow_id = uuid4()

        # Test invalid task priority
        with pytest.raises(ValidationError) as exc_info:
            WorkflowStepCreate(
                workflow_definition_id=workflow_id,
                name="Test Step",
                step_order=1,
                task_name="test_task",
                task_priority=15  # Invalid priority > 10
            )
        assert "Input should be less than or equal to 10" in str(exc_info.value)

        # Test negative step_order
        with pytest.raises(ValidationError) as exc_info:
            WorkflowStepCreate(
                workflow_definition_id=workflow_id,
                name="Test Step",
                step_order=-1,
                task_name="test_task"
            )
        assert "Input should be greater than or equal to 0" in str(exc_info.value)
