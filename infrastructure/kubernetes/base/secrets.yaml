# Culture Connect Backend - Kubernetes Secrets
# Sensitive configuration for production deployment
# Note: In production, these should be managed by external secret management systems

apiVersion: v1
kind: Secret
metadata:
  name: culture-connect-secrets
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "Application secrets for Culture Connect Backend"
    secret-management: "external-secrets-operator"
type: Opaque
stringData:
  # Application Secrets (Base64 encoded in production)
  CC_SECRET_KEY: "REPLACE_WITH_ACTUAL_SECRET_KEY"
  CC_JWT_SECRET_KEY: "REPLACE_WITH_ACTUAL_JWT_SECRET_KEY"
  
  # Database Configuration
  CC_DATABASE_URL: "postgresql+asyncpg://culture_connect:REPLACE_WITH_DB_PASSWORD@culture-connect-postgres:5432/culture_connect_db"
  CC_POSTGRES_PASSWORD: "REPLACE_WITH_DB_PASSWORD"
  
  # Redis Configuration
  CC_REDIS_URL: "redis://:REPLACE_WITH_REDIS_PASSWORD@culture-connect-redis:6379/0"
  CC_REDIS_PASSWORD: "REPLACE_WITH_REDIS_PASSWORD"
  
  # Celery Configuration
  CC_CELERY_BROKER_URL: "redis://:REPLACE_WITH_REDIS_PASSWORD@culture-connect-redis:6379/1"
  CC_CELERY_RESULT_BACKEND: "redis://:REPLACE_WITH_REDIS_PASSWORD@culture-connect-redis:6379/2"
  
  # Payment Provider Secrets
  CC_PAYSTACK_SECRET_KEY: "REPLACE_WITH_PAYSTACK_SECRET_KEY"
  CC_PAYSTACK_PUBLIC_KEY: "REPLACE_WITH_PAYSTACK_PUBLIC_KEY"
  CC_PAYSTACK_WEBHOOK_SECRET: "REPLACE_WITH_PAYSTACK_WEBHOOK_SECRET"
  
  CC_STRIPE_SECRET_KEY: "REPLACE_WITH_STRIPE_SECRET_KEY"
  CC_STRIPE_PUBLIC_KEY: "REPLACE_WITH_STRIPE_PUBLIC_KEY"
  CC_STRIPE_WEBHOOK_SECRET: "REPLACE_WITH_STRIPE_WEBHOOK_SECRET"
  
  CC_BUSHA_API_KEY: "REPLACE_WITH_BUSHA_API_KEY"
  CC_BUSHA_SECRET_KEY: "REPLACE_WITH_BUSHA_SECRET_KEY"
  CC_BUSHA_WEBHOOK_SECRET: "REPLACE_WITH_BUSHA_WEBHOOK_SECRET"
  
  # AI/ML API Secrets
  CC_AIML_API_KEY: "REPLACE_WITH_AIML_API_KEY"
  
  # OAuth Secrets
  CC_GOOGLE_CLIENT_ID: "REPLACE_WITH_GOOGLE_CLIENT_ID"
  CC_GOOGLE_CLIENT_SECRET: "REPLACE_WITH_GOOGLE_CLIENT_SECRET"
  CC_FACEBOOK_APP_ID: "REPLACE_WITH_FACEBOOK_APP_ID"
  CC_FACEBOOK_APP_SECRET: "REPLACE_WITH_FACEBOOK_APP_SECRET"
  
  # External Service Secrets
  CC_SENTRY_DSN: "REPLACE_WITH_SENTRY_DSN"
  MAXMIND_LICENSE_KEY: "REPLACE_WITH_MAXMIND_LICENSE_KEY"
  
  # SMTP Configuration
  CC_SMTP_HOST: "REPLACE_WITH_SMTP_HOST"
  CC_SMTP_USERNAME: "REPLACE_WITH_SMTP_USERNAME"
  CC_SMTP_PASSWORD: "REPLACE_WITH_SMTP_PASSWORD"
  
  # Backup Encryption
  CC_BACKUP_ENCRYPTION_KEY: "REPLACE_WITH_BACKUP_ENCRYPTION_KEY"

---
apiVersion: v1
kind: Secret
metadata:
  name: culture-connect-tls
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: tls-certificate
  annotations:
    cert-manager.io/issuer: "letsencrypt-prod"
type: kubernetes.io/tls
data:
  # TLS certificate and key will be populated by cert-manager
  tls.crt: ""
  tls.key: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: culture-connect-registry
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: registry-credentials
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: |
    {
      "auths": {
        "ghcr.io": {
          "username": "REPLACE_WITH_GITHUB_USERNAME",
          "password": "REPLACE_WITH_GITHUB_TOKEN",
          "auth": "REPLACE_WITH_BASE64_ENCODED_CREDENTIALS"
        }
      }
    }

---
# External Secrets Operator Configuration (if using)
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-manager
  namespace: culture-connect
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-west-2
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets-sa

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: culture-connect-external-secrets
  namespace: culture-connect
spec:
  refreshInterval: 15s
  secretStoreRef:
    name: aws-secrets-manager
    kind: SecretStore
  target:
    name: culture-connect-secrets
    creationPolicy: Owner
  data:
  - secretKey: CC_SECRET_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_SECRET_KEY
  - secretKey: CC_JWT_SECRET_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_JWT_SECRET_KEY
  - secretKey: CC_DATABASE_URL
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_DATABASE_URL
  - secretKey: CC_REDIS_URL
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_REDIS_URL
  - secretKey: CC_PAYSTACK_SECRET_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_PAYSTACK_SECRET_KEY
  - secretKey: CC_STRIPE_SECRET_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_STRIPE_SECRET_KEY
  - secretKey: CC_BUSHA_API_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_BUSHA_API_KEY
  - secretKey: CC_AIML_API_KEY
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_AIML_API_KEY
  - secretKey: CC_SENTRY_DSN
    remoteRef:
      key: culture-connect-production-secrets
      property: CC_SENTRY_DSN
