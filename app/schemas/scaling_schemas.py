"""
Scaling schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic schemas for scaling and load balancing:
- ScalingMetrics schemas for real-time performance metrics
- AutoScalingPolicy schemas for scaling configuration
- LoadBalancerConfig schemas for load balancer management
- ContainerMetrics schemas for container performance tracking
- ScalingEvent schemas for scaling event management

Implements Phase 7.3.3 requirements with Pydantic V2 syntax, comprehensive validation,
and seamless integration with Phase 7.2 performance monitoring.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, Optional, List, Union
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator
from pydantic.types import PositiveInt, NonNegativeInt

from app.models.scaling_models import (
    ScalingTriggerType, ScalingDirection, LoadBalancerStrategy,
    ContainerStatus, ScalingEventType
)


# Base schemas
class ScalingBaseSchema(BaseModel):
    """Base schema for scaling-related models."""
    model_config = ConfigDict(from_attributes=True)


# Scaling Metrics Schemas
class ScalingMetricsBase(ScalingBaseSchema):
    """Base schema for scaling metrics."""
    metric_name: str = Field(..., min_length=1, max_length=100)
    metric_type: str = Field(..., min_length=1, max_length=50)
    component: str = Field(..., min_length=1, max_length=100)
    current_value: Decimal = Field(..., ge=0)
    threshold_value: Optional[Decimal] = Field(None, ge=0)
    target_value: Optional[Decimal] = Field(None, ge=0)
    cpu_utilization: Optional[Decimal] = Field(None, ge=0, le=100)
    memory_utilization: Optional[Decimal] = Field(None, ge=0, le=100)
    request_rate: Optional[NonNegativeInt] = None
    response_time_ms: Optional[NonNegativeInt] = None
    error_rate: Optional[Decimal] = Field(None, ge=0, le=1)
    tags: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class ScalingMetricsCreate(ScalingMetricsBase):
    """Schema for creating scaling metrics."""
    recorded_at: Optional[datetime] = None


class ScalingMetricsUpdate(ScalingBaseSchema):
    """Schema for updating scaling metrics."""
    current_value: Optional[Decimal] = Field(None, ge=0)
    threshold_value: Optional[Decimal] = Field(None, ge=0)
    target_value: Optional[Decimal] = Field(None, ge=0)
    cpu_utilization: Optional[Decimal] = Field(None, ge=0, le=100)
    memory_utilization: Optional[Decimal] = Field(None, ge=0, le=100)
    request_rate: Optional[NonNegativeInt] = None
    response_time_ms: Optional[NonNegativeInt] = None
    error_rate: Optional[Decimal] = Field(None, ge=0, le=1)
    tags: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class ScalingMetricsResponse(ScalingMetricsBase):
    """Schema for scaling metrics response."""
    id: UUID
    recorded_at: datetime
    created_at: datetime


# Auto Scaling Policy Schemas
class AutoScalingPolicyBase(ScalingBaseSchema):
    """Base schema for auto scaling policies."""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    component: str = Field(..., min_length=1, max_length=100)
    min_replicas: PositiveInt = Field(default=1, ge=1)
    max_replicas: PositiveInt = Field(default=10, ge=1)
    target_cpu_utilization: Optional[int] = Field(None, ge=1, le=100)
    target_memory_utilization: Optional[int] = Field(None, ge=1, le=100)
    scale_up_threshold: Decimal = Field(..., gt=0)
    scale_down_threshold: Decimal = Field(..., gt=0)
    scale_up_cooldown_seconds: int = Field(default=300, ge=60)
    scale_down_cooldown_seconds: int = Field(default=600, ge=60)
    custom_metrics: Optional[Dict[str, Any]] = None
    scaling_triggers: Optional[Dict[str, Any]] = None
    is_enabled: bool = True
    configuration: Optional[Dict[str, Any]] = None
    tags: Optional[Dict[str, Any]] = None

    @validator('max_replicas')
    def validate_max_replicas(cls, v, values):
        """Validate max_replicas is greater than min_replicas."""
        if 'min_replicas' in values and v < values['min_replicas']:
            raise ValueError('max_replicas must be greater than or equal to min_replicas')
        return v

    @validator('scale_up_threshold')
    def validate_scale_up_threshold(cls, v, values):
        """Validate scale_up_threshold is greater than scale_down_threshold."""
        if 'scale_down_threshold' in values and v <= values['scale_down_threshold']:
            raise ValueError('scale_up_threshold must be greater than scale_down_threshold')
        return v


class AutoScalingPolicyCreate(AutoScalingPolicyBase):
    """Schema for creating auto scaling policies."""
    pass


class AutoScalingPolicyUpdate(ScalingBaseSchema):
    """Schema for updating auto scaling policies."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    min_replicas: Optional[PositiveInt] = Field(None, ge=1)
    max_replicas: Optional[PositiveInt] = Field(None, ge=1)
    target_cpu_utilization: Optional[int] = Field(None, ge=1, le=100)
    target_memory_utilization: Optional[int] = Field(None, ge=1, le=100)
    scale_up_threshold: Optional[Decimal] = Field(None, gt=0)
    scale_down_threshold: Optional[Decimal] = Field(None, gt=0)
    scale_up_cooldown_seconds: Optional[int] = Field(None, ge=60)
    scale_down_cooldown_seconds: Optional[int] = Field(None, ge=60)
    custom_metrics: Optional[Dict[str, Any]] = None
    scaling_triggers: Optional[Dict[str, Any]] = None
    is_enabled: Optional[bool] = None
    configuration: Optional[Dict[str, Any]] = None
    tags: Optional[Dict[str, Any]] = None


class AutoScalingPolicyResponse(AutoScalingPolicyBase):
    """Schema for auto scaling policy response."""
    id: UUID
    last_scaling_event: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


# Load Balancer Config Schemas
class LoadBalancerConfigBase(ScalingBaseSchema):
    """Base schema for load balancer configuration."""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    service_name: str = Field(..., min_length=1, max_length=100)
    strategy: LoadBalancerStrategy = LoadBalancerStrategy.ROUND_ROBIN
    session_affinity: bool = False
    sticky_session_timeout: Optional[PositiveInt] = None
    health_check_path: str = Field(default="/health", max_length=200)
    health_check_interval_seconds: int = Field(default=30, ge=10)
    health_check_timeout_seconds: int = Field(default=5, ge=1)
    health_check_retries: int = Field(default=3, ge=1)
    ssl_enabled: bool = True
    ssl_certificate_path: Optional[str] = Field(None, max_length=500)
    ssl_redirect: bool = True
    max_connections: Optional[PositiveInt] = None
    connection_timeout_seconds: int = Field(default=60, ge=10)
    request_timeout_seconds: int = Field(default=30, ge=5)
    upstream_servers: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None
    is_enabled: bool = True


class LoadBalancerConfigCreate(LoadBalancerConfigBase):
    """Schema for creating load balancer configuration."""
    pass


class LoadBalancerConfigUpdate(ScalingBaseSchema):
    """Schema for updating load balancer configuration."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    strategy: Optional[LoadBalancerStrategy] = None
    session_affinity: Optional[bool] = None
    sticky_session_timeout: Optional[PositiveInt] = None
    health_check_path: Optional[str] = Field(None, max_length=200)
    health_check_interval_seconds: Optional[int] = Field(None, ge=10)
    health_check_timeout_seconds: Optional[int] = Field(None, ge=1)
    health_check_retries: Optional[int] = Field(None, ge=1)
    ssl_enabled: Optional[bool] = None
    ssl_certificate_path: Optional[str] = Field(None, max_length=500)
    ssl_redirect: Optional[bool] = None
    max_connections: Optional[PositiveInt] = None
    connection_timeout_seconds: Optional[int] = Field(None, ge=10)
    request_timeout_seconds: Optional[int] = Field(None, ge=5)
    upstream_servers: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None
    is_enabled: Optional[bool] = None


class LoadBalancerConfigResponse(LoadBalancerConfigBase):
    """Schema for load balancer configuration response."""
    id: UUID
    created_at: datetime
    updated_at: datetime


# Container Metrics Schemas
class ContainerMetricsBase(ScalingBaseSchema):
    """Base schema for container metrics."""
    container_id: str = Field(..., min_length=1, max_length=100)
    pod_name: str = Field(..., min_length=1, max_length=200)
    namespace: str = Field(..., min_length=1, max_length=100)
    node_name: Optional[str] = Field(None, max_length=200)
    cpu_usage_cores: Optional[Decimal] = Field(None, ge=0)
    cpu_limit_cores: Optional[Decimal] = Field(None, ge=0)
    memory_usage_bytes: Optional[NonNegativeInt] = None
    memory_limit_bytes: Optional[NonNegativeInt] = None
    request_count: NonNegativeInt = 0
    error_count: NonNegativeInt = 0
    avg_response_time_ms: Optional[NonNegativeInt] = None
    status: ContainerStatus = ContainerStatus.PENDING
    restart_count: NonNegativeInt = 0
    network_rx_bytes: Optional[NonNegativeInt] = None
    network_tx_bytes: Optional[NonNegativeInt] = None
    labels: Optional[Dict[str, Any]] = None
    annotations: Optional[Dict[str, Any]] = None


class ContainerMetricsCreate(ContainerMetricsBase):
    """Schema for creating container metrics."""
    recorded_at: Optional[datetime] = None


class ContainerMetricsUpdate(ScalingBaseSchema):
    """Schema for updating container metrics."""
    cpu_usage_cores: Optional[Decimal] = Field(None, ge=0)
    cpu_limit_cores: Optional[Decimal] = Field(None, ge=0)
    memory_usage_bytes: Optional[NonNegativeInt] = None
    memory_limit_bytes: Optional[NonNegativeInt] = None
    request_count: Optional[NonNegativeInt] = None
    error_count: Optional[NonNegativeInt] = None
    avg_response_time_ms: Optional[NonNegativeInt] = None
    status: Optional[ContainerStatus] = None
    restart_count: Optional[NonNegativeInt] = None
    network_rx_bytes: Optional[NonNegativeInt] = None
    network_tx_bytes: Optional[NonNegativeInt] = None
    labels: Optional[Dict[str, Any]] = None
    annotations: Optional[Dict[str, Any]] = None


class ContainerMetricsResponse(ContainerMetricsBase):
    """Schema for container metrics response."""
    id: UUID
    recorded_at: datetime
    created_at: datetime


# Scaling Event Schemas
class ScalingEventBase(ScalingBaseSchema):
    """Base schema for scaling events."""
    event_type: ScalingEventType
    component: str = Field(..., min_length=1, max_length=100)
    trigger_type: ScalingTriggerType
    scaling_direction: Optional[ScalingDirection] = None
    previous_replicas: Optional[NonNegativeInt] = None
    target_replicas: Optional[NonNegativeInt] = None
    actual_replicas: Optional[NonNegativeInt] = None
    trigger_value: Optional[Decimal] = None
    threshold_value: Optional[Decimal] = None
    metric_name: Optional[str] = Field(None, max_length=100)
    success: bool = True
    error_message: Optional[str] = None
    duration_seconds: Optional[NonNegativeInt] = None
    policy_id: Optional[UUID] = None
    metadata: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None


class ScalingEventCreate(ScalingEventBase):
    """Schema for creating scaling events."""
    triggered_at: Optional[datetime] = None


class ScalingEventUpdate(ScalingBaseSchema):
    """Schema for updating scaling events."""
    actual_replicas: Optional[NonNegativeInt] = None
    success: Optional[bool] = None
    error_message: Optional[str] = None
    duration_seconds: Optional[NonNegativeInt] = None
    completed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class ScalingEventResponse(ScalingEventBase):
    """Schema for scaling event response."""
    id: UUID
    triggered_at: datetime
    completed_at: Optional[datetime] = None
    created_at: datetime


# Query and Filter Schemas
class ScalingMetricsQuery(ScalingBaseSchema):
    """Schema for scaling metrics query parameters."""
    component: Optional[str] = None
    metric_type: Optional[str] = None
    metric_name: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class ScalingEventQuery(ScalingBaseSchema):
    """Schema for scaling event query parameters."""
    component: Optional[str] = None
    event_type: Optional[ScalingEventType] = None
    success: Optional[bool] = None
    policy_id: Optional[UUID] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


# Dashboard and Analytics Schemas
class ScalingDashboardData(ScalingBaseSchema):
    """Schema for scaling dashboard data."""
    current_replicas: Dict[str, int]
    target_replicas: Dict[str, int]
    cpu_utilization: Dict[str, float]
    memory_utilization: Dict[str, float]
    request_rate: Dict[str, int]
    error_rate: Dict[str, float]
    recent_events: List[ScalingEventResponse]
    active_policies: List[AutoScalingPolicyResponse]


class ScalingRecommendation(ScalingBaseSchema):
    """Schema for scaling recommendations."""
    component: str
    recommendation_type: str
    current_state: Dict[str, Any]
    recommended_action: str
    expected_impact: str
    confidence_score: float = Field(..., ge=0, le=1)
    priority: str
    metadata: Optional[Dict[str, Any]] = None
