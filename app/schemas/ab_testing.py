"""
A/B Testing Schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic V2 schemas for A/B testing framework including:
- ABTestCreate/Update: Test configuration and management schemas
- ABTestAssignmentResponse: User assignment tracking schemas
- ABTestResultCreate: Test result submission schemas
- ABTestAnalysisResponse: Statistical analysis and reporting schemas
- ABTestDashboardResponse: Dashboard summary and insights

Implements Phase 2.3 A/B Testing Framework requirements with Pydantic V2 compliance,
comprehensive validation, statistical significance validation, and seamless integration
with existing geolocation analytics and VPN detection systems.
"""

from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from enum import Enum
import uuid

from app.schemas.base import BaseSchema, TimestampMixin
from app.models.ab_testing import RoutingStrategy, ABTestStatus, ABTestType, StatisticalSignificance


class ABTestConfigurationSchema(BaseSchema):
    """Base schema for A/B test configuration."""

    test_name: str = Field(
        ...,
        min_length=3,
        max_length=100,
        description="Unique test name identifier"
    )
    test_description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Detailed test description and objectives"
    )
    test_type: ABTestType = Field(
        default=ABTestType.ROUTING_STRATEGY,
        description="Type of A/B test being conducted"
    )

    # Test strategies
    strategy_a: RoutingStrategy = Field(
        ...,
        description="Control group routing strategy"
    )
    strategy_b: RoutingStrategy = Field(
        ...,
        description="Treatment group routing strategy"
    )
    strategy_a_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Human-readable name for strategy A"
    )
    strategy_b_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Human-readable name for strategy B"
    )

    # Traffic allocation
    traffic_split: float = Field(
        default=0.5,
        ge=0.1,
        le=0.9,
        description="Traffic split ratio (0.1 to 0.9)"
    )
    traffic_allocation_percentage: float = Field(
        default=100.0,
        ge=1.0,
        le=100.0,
        description="Percentage of total traffic to include in test"
    )

    # Statistical parameters
    confidence_level: float = Field(
        default=0.95,
        ge=0.8,
        le=0.99,
        description="Statistical confidence level (0.8 to 0.99)"
    )
    min_sample_size: int = Field(
        default=1000,
        ge=100,
        le=100000,
        description="Minimum sample size per group"
    )
    min_effect_size: float = Field(
        default=0.02,
        ge=0.005,
        le=0.5,
        description="Minimum detectable effect size"
    )
    statistical_power: float = Field(
        default=0.8,
        ge=0.7,
        le=0.95,
        description="Statistical power (0.7 to 0.95)"
    )

    @field_validator('strategy_a', 'strategy_b')
    @classmethod
    def validate_strategies(cls, v):
        """Validate routing strategies."""
        if not isinstance(v, RoutingStrategy):
            raise ValueError("Invalid routing strategy")
        return v

    @field_validator('traffic_split')
    @classmethod
    def validate_traffic_split(cls, v):
        """Validate traffic split is reasonable."""
        if not 0.1 <= v <= 0.9:
            raise ValueError("Traffic split must be between 0.1 and 0.9")
        return v

    model_config = ConfigDict(from_attributes=True)


class ABTestCreate(ABTestConfigurationSchema):
    """Schema for creating new A/B tests."""

    # Test lifecycle
    start_date: Optional[datetime] = Field(
        None,
        description="Test start date (defaults to now if not specified)"
    )
    end_date: Optional[datetime] = Field(
        None,
        description="Test end date (optional)"
    )
    planned_duration_days: Optional[int] = Field(
        None,
        ge=1,
        le=365,
        description="Planned test duration in days"
    )

    # Targeting and filtering
    target_countries: Optional[List[str]] = Field(
        None,
        description="List of target country codes (ISO 2-letter)"
    )
    exclude_countries: Optional[List[str]] = Field(
        None,
        description="List of excluded country codes"
    )
    target_user_segments: Optional[Dict[str, Any]] = Field(
        None,
        description="User segment targeting criteria"
    )
    exclude_vpn_users: bool = Field(
        default=False,
        description="Whether to exclude VPN users from the test"
    )

    # Success metrics
    primary_metric: str = Field(
        default="conversion_rate",
        description="Primary success metric for the test"
    )
    secondary_metrics: Optional[List[str]] = Field(
        None,
        description="List of secondary metrics to track"
    )
    success_criteria: Optional[Dict[str, Any]] = Field(
        None,
        description="Success criteria definition"
    )

    @field_validator('target_countries', 'exclude_countries')
    @classmethod
    def validate_country_codes(cls, v):
        """Validate country codes are 2-letter ISO codes."""
        if v is not None:
            for code in v:
                if not isinstance(code, str) or len(code) != 2:
                    raise ValueError("Country codes must be 2-letter ISO codes")
        return v

    @field_validator('planned_duration_days')
    @classmethod
    def validate_duration(cls, v):
        """Validate test duration is reasonable."""
        if v is not None and (v < 1 or v > 365):
            raise ValueError("Test duration must be between 1 and 365 days")
        return v


class ABTestUpdate(BaseSchema):
    """Schema for updating existing A/B tests."""

    test_description: Optional[str] = Field(None, max_length=1000)
    status: Optional[ABTestStatus] = Field(None)
    end_date: Optional[datetime] = Field(None)
    traffic_allocation_percentage: Optional[float] = Field(None, ge=1.0, le=100.0)
    exclude_vpn_users: Optional[bool] = Field(None)
    success_criteria: Optional[Dict[str, Any]] = Field(None)

    model_config = ConfigDict(from_attributes=True)


class ABTestResponse(ABTestConfigurationSchema, TimestampMixin):
    """Schema for A/B test response data."""

    id: uuid.UUID = Field(..., description="Test unique identifier")
    status: ABTestStatus = Field(..., description="Current test status")

    # Test lifecycle
    start_date: Optional[datetime] = Field(None)
    end_date: Optional[datetime] = Field(None)
    planned_duration_days: Optional[int] = Field(None)

    # Targeting and filtering
    target_countries: Optional[List[str]] = Field(None)
    exclude_countries: Optional[List[str]] = Field(None)
    target_user_segments: Optional[Dict[str, Any]] = Field(None)
    exclude_vpn_users: bool = Field(default=False)

    # Success metrics
    primary_metric: str = Field(default="conversion_rate")
    secondary_metrics: Optional[List[str]] = Field(None)
    success_criteria: Optional[Dict[str, Any]] = Field(None)

    # Performance tracking
    current_sample_size_a: int = Field(default=0)
    current_sample_size_b: int = Field(default=0)
    current_conversion_rate_a: float = Field(default=0.0)
    current_conversion_rate_b: float = Field(default=0.0)

    # Analysis results
    last_analysis_date: Optional[datetime] = Field(None)
    current_p_value: Optional[float] = Field(None)
    current_significance: Optional[StatisticalSignificance] = Field(None)
    is_statistically_significant: bool = Field(default=False)

    model_config = ConfigDict(from_attributes=True)


class ABTestAssignmentResponse(BaseSchema):
    """Schema for A/B test assignment response."""

    test_id: uuid.UUID = Field(..., description="Test identifier")
    user_id: Optional[int] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier for anonymous users")

    assigned_group: str = Field(..., description="Assigned test group (A or B)")
    assigned_strategy: RoutingStrategy = Field(..., description="Assigned routing strategy")
    assignment_date: datetime = Field(..., description="Assignment timestamp")

    # Assignment context
    country_code: Optional[str] = Field(None, description="User's country code")
    is_vpn_detected: bool = Field(default=False, description="Whether VPN was detected")

    # Assignment metadata
    assignment_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional assignment context")

    model_config = ConfigDict(from_attributes=True)


class ABTestResultCreate(BaseSchema):
    """Schema for creating A/B test results."""

    test_id: uuid.UUID = Field(..., description="Test identifier")
    user_id: Optional[int] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")

    # Result identification
    payment_id: Optional[int] = Field(None, description="Associated payment ID")
    transaction_id: Optional[str] = Field(None, max_length=100, description="Transaction identifier")

    # Test outcome
    is_conversion: bool = Field(..., description="Whether this result represents a conversion")
    conversion_value: Decimal = Field(default=Decimal("0.00"), description="Conversion value")
    conversion_currency: Optional[str] = Field(None, max_length=3, description="Conversion currency")

    # Performance metrics
    processing_time_ms: Optional[float] = Field(None, ge=0, description="Processing time in milliseconds")
    response_time_ms: Optional[float] = Field(None, ge=0, description="Response time in milliseconds")

    # Provider and routing information
    selected_provider: Optional[str] = Field(None, max_length=20, description="Selected payment provider")
    routing_decision: Optional[str] = Field(None, max_length=30, description="Routing decision made")
    routing_confidence: Optional[float] = Field(None, ge=0, le=1, description="Routing confidence score")

    # Geographic and VPN context
    country_code: Optional[str] = Field(None, max_length=2, description="Country code")
    is_vpn_detected: bool = Field(default=False, description="Whether VPN was detected")
    vpn_confidence_score: Optional[float] = Field(None, ge=0, le=1, description="VPN detection confidence")

    # Additional metrics
    error_code: Optional[str] = Field(None, max_length=20, description="Error code if applicable")
    error_message: Optional[str] = Field(None, max_length=500, description="Error message if applicable")
    additional_metrics: Optional[Dict[str, Any]] = Field(None, description="Additional metrics")

    @field_validator('conversion_currency')
    @classmethod
    def validate_currency(cls, v):
        """Validate currency code."""
        if v is not None and len(v) != 3:
            raise ValueError("Currency code must be 3 characters")
        return v

    @field_validator('country_code')
    @classmethod
    def validate_country_code(cls, v):
        """Validate country code."""
        if v is not None and len(v) != 2:
            raise ValueError("Country code must be 2 characters")
        return v

    model_config = ConfigDict(from_attributes=True)


class ABTestResultResponse(BaseSchema):
    """Schema for A/B test result response."""

    id: uuid.UUID = Field(..., description="Result unique identifier")
    test_id: uuid.UUID = Field(..., description="Test identifier")
    assignment_id: uuid.UUID = Field(..., description="Assignment identifier")

    # Result identification
    payment_id: Optional[int] = Field(None, description="Associated payment ID")
    transaction_id: Optional[str] = Field(None, description="Transaction identifier")

    # Test outcome
    is_conversion: bool = Field(..., description="Whether this result represents a conversion")
    conversion_value: Decimal = Field(default=Decimal("0.00"), description="Conversion value")
    conversion_currency: Optional[str] = Field(None, description="Conversion currency")

    # Performance metrics
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    response_time_ms: Optional[float] = Field(None, description="Response time in milliseconds")

    # Provider and routing information
    selected_provider: Optional[str] = Field(None, description="Selected payment provider")
    routing_decision: Optional[str] = Field(None, description="Routing decision made")
    routing_confidence: Optional[float] = Field(None, description="Routing confidence score")

    # Geographic and VPN context
    country_code: Optional[str] = Field(None, description="Country code")
    is_vpn_detected: bool = Field(default=False, description="Whether VPN was detected")
    vpn_confidence_score: Optional[float] = Field(None, description="VPN detection confidence")

    # Timing information
    result_timestamp: datetime = Field(..., description="Result timestamp")
    test_exposure_time_seconds: Optional[float] = Field(None, description="Time from assignment to result")

    model_config = ConfigDict(from_attributes=True)


class StatisticalAnalysisSchema(BaseSchema):
    """Schema for statistical analysis results."""

    # Sample sizes
    sample_size_a: int = Field(..., description="Sample size for group A")
    sample_size_b: int = Field(..., description="Sample size for group B")
    total_sample_size: int = Field(..., description="Total sample size")

    # Conversion metrics
    conversions_a: int = Field(..., description="Conversions in group A")
    conversions_b: int = Field(..., description="Conversions in group B")
    conversion_rate_a: float = Field(..., description="Conversion rate for group A")
    conversion_rate_b: float = Field(..., description="Conversion rate for group B")

    # Statistical analysis results
    effect_size: Optional[float] = Field(None, description="Difference in conversion rates")
    relative_effect_size: Optional[float] = Field(None, description="Percentage improvement")
    p_value: Optional[float] = Field(None, description="Statistical p-value")
    confidence_level: float = Field(default=0.95, description="Confidence level")
    confidence_interval_lower: Optional[float] = Field(None, description="Lower confidence interval")
    confidence_interval_upper: Optional[float] = Field(None, description="Upper confidence interval")

    # Statistical significance
    is_statistically_significant: bool = Field(default=False, description="Whether results are statistically significant")
    significance_level: Optional[StatisticalSignificance] = Field(None, description="Significance level")
    statistical_power: Optional[float] = Field(None, description="Statistical power")

    model_config = ConfigDict(from_attributes=True)


class PerformanceComparisonSchema(BaseSchema):
    """Schema for performance comparison metrics."""

    # Processing time comparison
    avg_processing_time_a: Optional[float] = Field(None, description="Average processing time for group A (ms)")
    avg_processing_time_b: Optional[float] = Field(None, description="Average processing time for group B (ms)")
    processing_time_improvement: Optional[float] = Field(None, description="Processing time improvement (%)")

    # Revenue and business impact
    total_revenue_a: Decimal = Field(default=Decimal("0.00"), description="Total revenue for group A")
    total_revenue_b: Decimal = Field(default=Decimal("0.00"), description="Total revenue for group B")
    revenue_per_user_a: Decimal = Field(default=Decimal("0.00"), description="Revenue per user for group A")
    revenue_per_user_b: Decimal = Field(default=Decimal("0.00"), description="Revenue per user for group B")
    projected_revenue_impact: Optional[Decimal] = Field(None, description="Projected revenue impact")

    # Success rates by provider
    provider_performance_a: Optional[Dict[str, float]] = Field(None, description="Provider performance for group A")
    provider_performance_b: Optional[Dict[str, float]] = Field(None, description="Provider performance for group B")

    model_config = ConfigDict(from_attributes=True)


class ABTestAnalysisResponse(BaseSchema):
    """Schema for comprehensive A/B test analysis response."""

    id: uuid.UUID = Field(..., description="Analysis unique identifier")
    test_id: uuid.UUID = Field(..., description="Test identifier")

    # Analysis metadata
    analysis_date: datetime = Field(..., description="Analysis timestamp")
    analysis_type: str = Field(default="statistical_significance", description="Type of analysis performed")
    analysis_period_start: datetime = Field(..., description="Analysis period start")
    analysis_period_end: datetime = Field(..., description="Analysis period end")

    # Statistical analysis
    statistical_analysis: StatisticalAnalysisSchema = Field(..., description="Statistical analysis results")

    # Performance comparison
    performance_comparison: PerformanceComparisonSchema = Field(..., description="Performance comparison metrics")

    # Test recommendations
    recommendation: Optional[str] = Field(None, description="Test recommendation")
    recommendation_confidence: Optional[str] = Field(None, description="Recommendation confidence level")
    recommendation_reason: Optional[str] = Field(None, description="Reason for recommendation")

    # Geographic and segment analysis
    geographic_breakdown: Optional[Dict[str, Any]] = Field(None, description="Country-specific results")
    vpn_impact_analysis: Optional[Dict[str, Any]] = Field(None, description="VPN vs non-VPN performance")
    segment_analysis: Optional[Dict[str, Any]] = Field(None, description="User segment breakdown")

    # Quality metrics
    data_quality_score: float = Field(default=1.0, description="Data quality score")
    outlier_detection_results: Optional[Dict[str, Any]] = Field(None, description="Outlier detection results")
    bias_detection_results: Optional[Dict[str, Any]] = Field(None, description="Bias detection results")

    # Additional analysis metadata
    analysis_methodology: str = Field(default="two_proportion_z_test", description="Analysis methodology used")
    analysis_parameters: Optional[Dict[str, Any]] = Field(None, description="Analysis parameters")
    analysis_notes: Optional[str] = Field(None, description="Additional analysis notes")

    model_config = ConfigDict(from_attributes=True)


class ABTestSummarySchema(BaseSchema):
    """Schema for A/B test summary information."""

    test_id: uuid.UUID = Field(..., description="Test identifier")
    test_name: str = Field(..., description="Test name")
    test_status: ABTestStatus = Field(..., description="Test status")

    # Test configuration
    strategy_a: RoutingStrategy = Field(..., description="Strategy A")
    strategy_b: RoutingStrategy = Field(..., description="Strategy B")
    strategy_a_name: Optional[str] = Field(None, description="Strategy A name")
    strategy_b_name: Optional[str] = Field(None, description="Strategy B name")

    # Current performance
    current_sample_size_a: int = Field(default=0, description="Current sample size A")
    current_sample_size_b: int = Field(default=0, description="Current sample size B")
    current_conversion_rate_a: float = Field(default=0.0, description="Current conversion rate A")
    current_conversion_rate_b: float = Field(default=0.0, description="Current conversion rate B")

    # Statistical significance
    is_statistically_significant: bool = Field(default=False, description="Statistical significance")
    current_p_value: Optional[float] = Field(None, description="Current p-value")

    # Test timeline
    start_date: Optional[datetime] = Field(None, description="Test start date")
    end_date: Optional[datetime] = Field(None, description="Test end date")
    days_running: Optional[int] = Field(None, description="Days test has been running")

    # Quick insights
    winning_strategy: Optional[RoutingStrategy] = Field(None, description="Currently winning strategy")
    confidence_level: Optional[str] = Field(None, description="Confidence in results")
    projected_improvement: Optional[str] = Field(None, description="Projected improvement")

    model_config = ConfigDict(from_attributes=True)


class ABTestDashboardResponse(BaseSchema):
    """Schema for A/B testing dashboard response."""

    # Dashboard metadata
    dashboard_generated_at: datetime = Field(..., description="Dashboard generation timestamp")
    total_active_tests: int = Field(default=0, description="Total number of active tests")
    total_completed_tests: int = Field(default=0, description="Total number of completed tests")

    # Active tests summary
    active_tests: List[ABTestSummarySchema] = Field(default=[], description="List of active tests")

    # Overall performance metrics
    overall_metrics: Dict[str, Any] = Field(
        default={},
        description="Overall A/B testing performance metrics"
    )

    # Recent insights
    recent_insights: List[str] = Field(default=[], description="Recent insights from A/B tests")

    # Recommendations
    optimization_recommendations: List[Dict[str, Any]] = Field(
        default=[],
        description="Optimization recommendations based on test results"
    )

    # Performance trends
    performance_trends: Dict[str, Any] = Field(
        default={},
        description="Performance trends across all tests"
    )

    # Geographic insights
    geographic_performance: Dict[str, Any] = Field(
        default={},
        description="Geographic performance breakdown"
    )

    # VPN impact summary
    vpn_impact_summary: Dict[str, Any] = Field(
        default={},
        description="Summary of VPN impact on test results"
    )

    model_config = ConfigDict(from_attributes=True)


class ABTestFilterRequest(BaseSchema):
    """Schema for filtering A/B test requests."""

    # Test filters
    test_status: Optional[List[ABTestStatus]] = Field(None, description="Filter by test status")
    test_type: Optional[List[ABTestType]] = Field(None, description="Filter by test type")
    strategy: Optional[List[RoutingStrategy]] = Field(None, description="Filter by routing strategy")

    # Date filters
    start_date_from: Optional[datetime] = Field(None, description="Filter tests starting from this date")
    start_date_to: Optional[datetime] = Field(None, description="Filter tests starting before this date")

    # Geographic filters
    country_codes: Optional[List[str]] = Field(None, description="Filter by country codes")
    include_vpn_tests: Optional[bool] = Field(None, description="Include tests with VPN users")

    # Statistical filters
    min_sample_size: Optional[int] = Field(None, ge=0, description="Minimum sample size")
    significance_only: Optional[bool] = Field(None, description="Only statistically significant tests")

    # Pagination
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=20, ge=1, le=100, description="Page size")

    model_config = ConfigDict(from_attributes=True)