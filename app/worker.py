"""
Celery worker configuration for Culture Connect Backend API.

This module provides the main Celery worker entry point with:
- Worker configuration and initialization
- Task auto-discovery and registration
- Signal handlers for monitoring
- Health checks and metrics collection
- Production-grade worker management

Implements Task 6.2.1 requirements for Celery worker setup with
production-grade reliability and monitoring.
"""

import logging
import os
import signal
import sys
from datetime import datetime, timezone

from celery import Celery
from celery.signals import (
    worker_init, worker_ready, worker_shutdown,
    task_prerun, task_postrun, task_failure, task_success,
    worker_process_init
)

from app.core.celery_config import celery_app, CeleryConfig
from app.core.logging import setup_logging, correlation_id
from app.core.config import settings

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@worker_init.connect
def worker_init_handler(sender=None, **kwargs):
    """Initialize worker on startup."""
    logger.info("Celery worker initializing", extra={
        "worker_name": sender.hostname if sender else "unknown",
        "pid": os.getpid(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Worker is ready to accept tasks."""
    logger.info("Celery worker ready", extra={
        "worker_name": sender.hostname if sender else "unknown",
        "pid": os.getpid(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


@worker_shutdown.connect
def worker_shutdown_handler(sender=None, **kwargs):
    """Worker is shutting down."""
    logger.info("Celery worker shutting down", extra={
        "worker_name": sender.hostname if sender else "unknown",
        "pid": os.getpid(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


@worker_process_init.connect
def worker_process_init_handler(sender=None, **kwargs):
    """Initialize worker process."""
    logger.info("Celery worker process initialized", extra={
        "worker_name": sender.hostname if sender else "unknown",
        "pid": os.getpid(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    })


@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Handle task prerun signal."""
    # Set correlation ID for task execution
    correlation_id.set(task_id)
    
    logger.info(f"Task starting: {task.name}", extra={
        "task_id": task_id,
        "task_name": task.name,
        "args_count": len(args) if args else 0,
        "kwargs_count": len(kwargs) if kwargs else 0,
        "correlation_id": task_id
    })


@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, 
                        retval=None, state=None, **kwds):
    """Handle task postrun signal."""
    logger.info(f"Task completed: {task.name}", extra={
        "task_id": task_id,
        "task_name": task.name,
        "state": state,
        "correlation_id": task_id
    })
    
    # Clear correlation ID
    correlation_id.set(None)


@task_success.connect
def task_success_handler(sender=None, result=None, **kwargs):
    """Handle task success signal."""
    logger.info(f"Task succeeded: {sender.name}", extra={
        "task_name": sender.name,
        "result_type": type(result).__name__ if result else None,
        "correlation_id": correlation_id.get()
    })


@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwargs):
    """Handle task failure signal."""
    logger.error(f"Task failed: {sender.name}", extra={
        "task_id": task_id,
        "task_name": sender.name,
        "exception": str(exception),
        "exception_type": type(exception).__name__,
        "correlation_id": task_id
    })


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)


def configure_worker():
    """Configure worker with production settings."""
    # Set worker configuration
    celery_app.conf.update(
        # Worker settings
        worker_prefetch_multiplier=CeleryConfig.worker_prefetch_multiplier,
        worker_max_tasks_per_child=CeleryConfig.worker_max_tasks_per_child,
        worker_max_memory_per_child=CeleryConfig.worker_max_memory_per_child,
        
        # Task settings
        task_time_limit=CeleryConfig.task_time_limit,
        task_soft_time_limit=CeleryConfig.task_soft_time_limit,
        
        # Monitoring
        worker_send_task_events=CeleryConfig.worker_send_task_events,
        task_send_sent_event=CeleryConfig.task_send_sent_event,
        
        # Logging
        worker_log_format=CeleryConfig.worker_log_format,
        worker_task_log_format=CeleryConfig.worker_task_log_format,
    )
    
    # Auto-discover tasks
    celery_app.autodiscover_tasks([
        'app.tasks.email_tasks',
        'app.tasks.notification_tasks',
        'app.tasks.report_tasks',
        'app.tasks.sync_tasks',
        'app.tasks.cleanup_tasks',
        'app.tasks.analytics_tasks',
        'app.tasks.payment_tasks',
    ])
    
    logger.info("Celery worker configured", extra={
        "prefetch_multiplier": CeleryConfig.worker_prefetch_multiplier,
        "max_tasks_per_child": CeleryConfig.worker_max_tasks_per_child,
        "task_time_limit": CeleryConfig.task_time_limit,
        "broker_url": settings.CELERY_BROKER_URL,
        "result_backend": settings.CELERY_RESULT_BACKEND
    })


def health_check():
    """Perform worker health check."""
    try:
        # Check broker connection
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            logger.info("Worker health check passed", extra={
                "active_workers": len(stats),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            return True
        else:
            logger.warning("Worker health check failed: No active workers")
            return False
            
    except Exception as e:
        logger.error(f"Worker health check failed: {str(e)}")
        return False


def main():
    """Main worker entry point."""
    try:
        # Setup signal handlers
        setup_signal_handlers()
        
        # Configure worker
        configure_worker()
        
        # Perform initial health check
        if not health_check():
            logger.error("Initial health check failed, exiting")
            sys.exit(1)
        
        logger.info("Starting Celery worker", extra={
            "environment": settings.ENVIRONMENT,
            "worker_version": "1.0.0",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        # Start worker
        celery_app.start()
        
    except KeyboardInterrupt:
        logger.info("Worker interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Worker startup failed: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()


# Export the Celery app for use with celery command
app = celery_app
