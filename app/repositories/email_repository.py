"""
Email repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for email management including:
- EmailTemplateRepository: Template management with versioning and categorization
- EmailDeliveryRepository: Delivery tracking with analytics and performance monitoring
- EmailPreferenceRepository: User notification preferences and opt-out management
- EmailQueueRepository: Queue management with priority handling and batch processing

Implements Task 2.3.1 Phase 3 requirements for email service implementation with
production-grade PostgreSQL optimization, comprehensive error handling, and
seamless integration with Phase 1 models and Phase 2 schemas.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from sqlalchemy import and_, or_, func, desc, asc, text, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.repositories.base import BaseRepository
from app.models.email_models import (
    EmailTemplate, EmailDelivery, EmailPreference, EmailQueue,
    EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
)
from app.schemas.email_schemas import (
    EmailTemplateCreate, EmailTemplateUpdate,
    EmailDeliveryStatusUpdate, EmailPreferenceUpdate
)
from app.repositories.base import RepositoryError

# Create a DatabaseError alias for consistency
DatabaseError = RepositoryError

# Define exception classes for repository operations
class NotFoundError(RepositoryError):
    """Exception for resource not found errors."""
    pass

class ValidationError(RepositoryError):
    """Exception for validation errors."""
    pass

class ConflictError(RepositoryError):
    """Exception for resource conflict errors."""
    pass

# Logger for repository operations
logger = logging.getLogger(__name__)


class EmailTemplateRepository(BaseRepository[EmailTemplate]):
    """
    Repository for email template management with versioning and categorization.

    Provides comprehensive CRUD operations, template versioning, category filtering,
    and template variable validation with production-grade error handling.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email template repository."""
        super().__init__(EmailTemplate, db)

    async def create_template(
        self,
        template_data: EmailTemplateCreate,
        created_by: int,
        correlation_id: Optional[str] = None
    ) -> EmailTemplate:
        """
        Create a new email template with automatic versioning.

        Args:
            template_data: Template creation data
            created_by: ID of user creating the template
            correlation_id: Optional correlation ID for tracking

        Returns:
            Created email template

        Raises:
            ConflictError: If template name/version combination already exists
            ValidationError: If template data is invalid
            DatabaseError: If database operation fails
        """
        try:
            # Check if template name already exists and get next version
            stmt = select(EmailTemplate).filter(
                EmailTemplate.name == template_data.name
            ).order_by(desc(EmailTemplate.version))
            result = await self.db.execute(stmt)
            existing_template = result.scalar_one_or_none()

            if existing_template:
                next_version = existing_template.version + 1
            else:
                next_version = 1

            # Create template with proper version
            template = EmailTemplate(
                name=template_data.name,
                category=template_data.category,
                subject_template=template_data.subject_template,
                body_template=template_data.body_template,
                html_template=template_data.html_template,
                variables=template_data.variables,
                version=next_version,
                is_active=template_data.is_active,
                created_by=created_by
            )

            self.db.add(template)
            await self.db.commit()
            await self.db.refresh(template)

            logger.info(
                f"Created email template: {template.name} v{template.version}",
                extra={
                    "template_id": str(template.id),
                    "template_name": template.name,
                    "template_version": template.version,
                    "created_by": created_by,
                    "correlation_id": correlation_id
                }
            )

            return template

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Template creation failed - integrity error: {str(e)}")
            raise ConflictError(f"Template with name '{template_data.name}' and version already exists")
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error creating template: {str(e)}")
            raise DatabaseError(f"Failed to create email template: {str(e)}")

    async def get_template_by_name_and_version(
        self,
        name: str,
        version: Optional[int] = None
    ) -> Optional[EmailTemplate]:
        """
        Get email template by name and version.

        Args:
            name: Template name
            version: Template version (latest active if None)

        Returns:
            Email template or None if not found
        """
        try:
            stmt = select(EmailTemplate).filter(EmailTemplate.name == name)

            if version is not None:
                stmt = stmt.filter(EmailTemplate.version == version)
            else:
                # Get latest active version
                stmt = stmt.filter(EmailTemplate.is_active == True).order_by(desc(EmailTemplate.version))

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except SQLAlchemyError as e:
            logger.error(f"Error retrieving template {name} v{version}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve email template: {str(e)}")

    def get_templates_by_category(
        self,
        category: EmailTemplateCategory,
        active_only: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[EmailTemplate], int]:
        """
        Get email templates by category with pagination.

        Args:
            category: Template category
            active_only: Whether to return only active templates
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (templates list, total count)
        """
        try:
            query = self.db.query(EmailTemplate).filter(EmailTemplate.category == category)

            if active_only:
                query = query.filter(EmailTemplate.is_active == True)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            templates = query.order_by(
                desc(EmailTemplate.created_at)
            ).offset(skip).limit(limit).all()

            return templates, total

        except SQLAlchemyError as e:
            logger.error(f"Error retrieving templates by category {category}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve templates by category: {str(e)}")

    def update_template_status(
        self,
        template_id: UUID,
        is_active: bool,
        correlation_id: Optional[str] = None
    ) -> EmailTemplate:
        """
        Update template active status.

        Args:
            template_id: Template ID
            is_active: New active status
            correlation_id: Optional correlation ID for tracking

        Returns:
            Updated template

        Raises:
            NotFoundError: If template not found
            DatabaseError: If database operation fails
        """
        try:
            template = self.get(template_id)
            if not template:
                raise NotFoundError(f"Email template with ID {template_id} not found")

            template.is_active = is_active
            template.updated_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(template)

            logger.info(
                f"Updated template status: {template.name} v{template.version} -> active={is_active}",
                extra={
                    "template_id": str(template_id),
                    "is_active": is_active,
                    "correlation_id": correlation_id
                }
            )

            return template

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error updating template status: {str(e)}")
            raise DatabaseError(f"Failed to update template status: {str(e)}")

    def search_templates(
        self,
        search_term: str,
        category: Optional[EmailTemplateCategory] = None,
        active_only: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[EmailTemplate], int]:
        """
        Search email templates by name or content.

        Args:
            search_term: Search term for name or content
            category: Optional category filter
            active_only: Whether to return only active templates
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (templates list, total count)
        """
        try:
            # Build search query
            search_filter = or_(
                EmailTemplate.name.ilike(f"%{search_term}%"),
                EmailTemplate.subject_template.ilike(f"%{search_term}%"),
                EmailTemplate.body_template.ilike(f"%{search_term}%")
            )

            query = self.db.query(EmailTemplate).filter(search_filter)

            if category:
                query = query.filter(EmailTemplate.category == category)

            if active_only:
                query = query.filter(EmailTemplate.is_active == True)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            templates = query.order_by(
                desc(EmailTemplate.updated_at)
            ).offset(skip).limit(limit).all()

            return templates, total

        except SQLAlchemyError as e:
            logger.error(f"Error searching templates: {str(e)}")
            raise DatabaseError(f"Failed to search email templates: {str(e)}")

    def get_template_usage_stats(self, template_id: UUID) -> Dict[str, Any]:
        """
        Get usage statistics for a template.

        Args:
            template_id: Template ID

        Returns:
            Dictionary with usage statistics
        """
        try:
            # Get delivery statistics
            delivery_stats = self.db.query(
                func.count(EmailDelivery.id).label('total_sent'),
                func.sum(
                    func.case(
                        (EmailDelivery.status == EmailDeliveryStatus.DELIVERED, 1),
                        else_=0
                    )
                ).label('delivered'),
                func.sum(
                    func.case(
                        (EmailDelivery.status == EmailDeliveryStatus.FAILED, 1),
                        else_=0
                    )
                ).label('failed')
            ).filter(EmailDelivery.template_id == template_id).first()

            # Get queue statistics
            queue_stats = self.db.query(
                func.count(EmailQueue.id).label('queued')
            ).filter(
                and_(
                    EmailQueue.template_id == template_id,
                    EmailQueue.status.in_([EmailQueueStatus.QUEUED, EmailQueueStatus.PROCESSING])
                )
            ).first()

            return {
                "total_sent": delivery_stats.total_sent or 0,
                "delivered": delivery_stats.delivered or 0,
                "failed": delivery_stats.failed or 0,
                "queued": queue_stats.queued or 0,
                "delivery_rate": (
                    (delivery_stats.delivered / delivery_stats.total_sent * 100)
                    if delivery_stats.total_sent and delivery_stats.total_sent > 0
                    else 0.0
                )
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting template usage stats: {str(e)}")
            raise DatabaseError(f"Failed to get template usage statistics: {str(e)}")


class EmailDeliveryRepository(BaseRepository[EmailDelivery]):
    """
    Repository for email delivery tracking with analytics and performance monitoring.

    Provides comprehensive delivery tracking, status updates, analytics queries,
    and performance monitoring with production-grade error handling.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email delivery repository."""
        super().__init__(EmailDelivery, db)

    def create_delivery_record(
        self,
        user_id: Optional[int],
        template_id: Optional[UUID],
        recipient_email: str,
        subject: str,
        correlation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EmailDelivery:
        """
        Create a new email delivery record.

        Args:
            user_id: Optional associated user ID
            template_id: Optional template ID
            recipient_email: Recipient email address
            subject: Email subject
            correlation_id: Optional correlation ID for tracking
            metadata: Optional additional metadata

        Returns:
            Created delivery record
        """
        try:
            delivery = EmailDelivery(
                user_id=user_id,
                template_id=template_id,
                recipient_email=recipient_email,
                subject=subject,
                status=EmailDeliveryStatus.PENDING,
                email_metadata=metadata or {},
                correlation_id=correlation_id
            )

            self.db.add(delivery)
            self.db.commit()
            self.db.refresh(delivery)

            logger.info(
                f"Created delivery record for {recipient_email}",
                extra={
                    "delivery_id": str(delivery.id),
                    "recipient": recipient_email,
                    "user_id": user_id,
                    "template_id": str(template_id) if template_id else None,
                    "correlation_id": correlation_id
                }
            )

            return delivery

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error creating delivery record: {str(e)}")
            raise DatabaseError(f"Failed to create delivery record: {str(e)}")

    def update_delivery_status(
        self,
        delivery_id: UUID,
        status: EmailDeliveryStatus,
        error_message: Optional[str] = None,
        delivered_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EmailDelivery:
        """
        Update email delivery status.

        Args:
            delivery_id: Delivery record ID
            status: New delivery status
            error_message: Optional error message
            delivered_at: Optional delivery timestamp
            metadata: Optional additional metadata

        Returns:
            Updated delivery record
        """
        try:
            delivery = self.get(delivery_id)
            if not delivery:
                raise NotFoundError(f"Delivery record with ID {delivery_id} not found")

            delivery.status = status
            if error_message:
                delivery.error_message = error_message
            if delivered_at:
                delivery.delivered_at = delivered_at
            if status == EmailDeliveryStatus.SENT and not delivery.sent_at:
                delivery.sent_at = datetime.utcnow()
            if metadata:
                delivery.email_metadata.update(metadata)

            self.db.commit()
            self.db.refresh(delivery)

            logger.info(
                f"Updated delivery status: {delivery.recipient_email} -> {status.value}",
                extra={
                    "delivery_id": str(delivery_id),
                    "status": status.value,
                    "error_message": error_message
                }
            )

            return delivery

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error updating delivery status: {str(e)}")
            raise DatabaseError(f"Failed to update delivery status: {str(e)}")

    def get_user_delivery_history(
        self,
        user_id: int,
        status_filter: Optional[EmailDeliveryStatus] = None,
        days_back: int = 30,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[EmailDelivery], int]:
        """
        Get delivery history for a user.

        Args:
            user_id: User ID
            status_filter: Optional status filter
            days_back: Number of days to look back
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (deliveries list, total count)
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)

            query = self.db.query(EmailDelivery).filter(
                and_(
                    EmailDelivery.user_id == user_id,
                    EmailDelivery.created_at >= cutoff_date
                )
            )

            if status_filter:
                query = query.filter(EmailDelivery.status == status_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            deliveries = query.order_by(
                desc(EmailDelivery.created_at)
            ).offset(skip).limit(limit).all()

            return deliveries, total

        except SQLAlchemyError as e:
            logger.error(f"Error getting user delivery history: {str(e)}")
            raise DatabaseError(f"Failed to get delivery history: {str(e)}")

    def get_delivery_analytics(
        self,
        start_date: datetime,
        end_date: datetime,
        category: Optional[EmailTemplateCategory] = None
    ) -> Dict[str, Any]:
        """
        Get email delivery analytics for a date range.

        Args:
            start_date: Start date for analytics
            end_date: End date for analytics
            category: Optional template category filter

        Returns:
            Dictionary with analytics data
        """
        try:
            # Base query
            query = self.db.query(EmailDelivery).filter(
                and_(
                    EmailDelivery.created_at >= start_date,
                    EmailDelivery.created_at <= end_date
                )
            )

            # Add category filter if specified
            if category:
                query = query.join(EmailTemplate).filter(
                    EmailTemplate.category == category
                )

            # Get overall statistics
            total_sent = query.count()

            # Status breakdown
            status_stats = self.db.query(
                EmailDelivery.status,
                func.count(EmailDelivery.id).label('count')
            ).filter(
                and_(
                    EmailDelivery.created_at >= start_date,
                    EmailDelivery.created_at <= end_date
                )
            ).group_by(EmailDelivery.status).all()

            # Category breakdown (if not filtered by category)
            category_stats = []
            if not category:
                category_stats = self.db.query(
                    EmailTemplate.category,
                    func.count(EmailDelivery.id).label('count')
                ).join(EmailTemplate).filter(
                    and_(
                        EmailDelivery.created_at >= start_date,
                        EmailDelivery.created_at <= end_date
                    )
                ).group_by(EmailTemplate.category).all()

            # Calculate rates
            delivered_count = sum(stat.count for stat in status_stats if stat.status == EmailDeliveryStatus.DELIVERED)
            failed_count = sum(stat.count for stat in status_stats if stat.status == EmailDeliveryStatus.FAILED)

            return {
                "total_sent": total_sent,
                "delivered": delivered_count,
                "failed": failed_count,
                "delivery_rate": (delivered_count / total_sent * 100) if total_sent > 0 else 0.0,
                "failure_rate": (failed_count / total_sent * 100) if total_sent > 0 else 0.0,
                "by_status": {stat.status.value: stat.count for stat in status_stats},
                "by_category": {stat.category.value: stat.count for stat in category_stats},
                "period_start": start_date,
                "period_end": end_date
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting delivery analytics: {str(e)}")
            raise DatabaseError(f"Failed to get delivery analytics: {str(e)}")

    def get_failed_deliveries(
        self,
        hours_back: int = 24,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[EmailDelivery], int]:
        """
        Get failed email deliveries for retry processing.

        Args:
            hours_back: Number of hours to look back
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (failed deliveries list, total count)
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(hours=hours_back)

            query = self.db.query(EmailDelivery).filter(
                and_(
                    EmailDelivery.status == EmailDeliveryStatus.FAILED,
                    EmailDelivery.created_at >= cutoff_date
                )
            )

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            deliveries = query.order_by(
                desc(EmailDelivery.created_at)
            ).offset(skip).limit(limit).all()

            return deliveries, total

        except SQLAlchemyError as e:
            logger.error(f"Error getting failed deliveries: {str(e)}")
            raise DatabaseError(f"Failed to get failed deliveries: {str(e)}")


class EmailPreferenceRepository(BaseRepository[EmailPreference]):
    """
    Repository for user email notification preferences and opt-out management.

    Provides comprehensive preference management, opt-out tracking, and
    GDPR compliance with production-grade error handling.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email preference repository."""
        super().__init__(EmailPreference, db)

    def get_user_preferences(self, user_id: int) -> Optional[EmailPreference]:
        """
        Get email preferences for a user.

        Args:
            user_id: User ID

        Returns:
            User email preferences or None if not found
        """
        try:
            return self.db.query(EmailPreference).filter(
                EmailPreference.user_id == user_id
            ).first()

        except SQLAlchemyError as e:
            logger.error(f"Error getting user preferences: {str(e)}")
            raise DatabaseError(f"Failed to get user preferences: {str(e)}")

    def create_default_preferences(self, user_id: int) -> EmailPreference:
        """
        Create default email preferences for a new user.

        Args:
            user_id: User ID

        Returns:
            Created email preferences
        """
        try:
            preferences = EmailPreference(
                user_id=user_id,
                verification_emails=True,
                security_emails=True,
                marketing_emails=False,
                booking_notifications=True,
                vendor_notifications=True,
                system_notifications=True
            )

            self.db.add(preferences)
            self.db.commit()
            self.db.refresh(preferences)

            logger.info(
                f"Created default email preferences for user {user_id}",
                extra={"user_id": user_id, "preferences_id": str(preferences.id)}
            )

            return preferences

        except IntegrityError:
            self.db.rollback()
            # Preferences already exist, return existing
            return self.get_user_preferences(user_id)
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error creating default preferences: {str(e)}")
            raise DatabaseError(f"Failed to create default preferences: {str(e)}")

    def update_user_preferences(
        self,
        user_id: int,
        preferences_data: EmailPreferenceUpdate,
        correlation_id: Optional[str] = None
    ) -> EmailPreference:
        """
        Update user email preferences.

        Args:
            user_id: User ID
            preferences_data: Preference update data
            correlation_id: Optional correlation ID for tracking

        Returns:
            Updated email preferences
        """
        try:
            preferences = self.get_user_preferences(user_id)
            if not preferences:
                # Create default preferences first
                preferences = self.create_default_preferences(user_id)

            # Update preferences with provided data
            update_data = preferences_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(preferences, field):
                    setattr(preferences, field, value)

            preferences.updated_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(preferences)

            logger.info(
                f"Updated email preferences for user {user_id}",
                extra={
                    "user_id": user_id,
                    "preferences_id": str(preferences.id),
                    "updated_fields": list(update_data.keys()),
                    "correlation_id": correlation_id
                }
            )

            return preferences

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error updating user preferences: {str(e)}")
            raise DatabaseError(f"Failed to update user preferences: {str(e)}")

    def opt_out_user(self, user_id: int, correlation_id: Optional[str] = None) -> EmailPreference:
        """
        Opt out user from all email communications.

        Args:
            user_id: User ID
            correlation_id: Optional correlation ID for tracking

        Returns:
            Updated email preferences
        """
        try:
            preferences = self.get_user_preferences(user_id)
            if not preferences:
                preferences = self.create_default_preferences(user_id)

            # Disable all email types except critical security emails
            preferences.verification_emails = False
            preferences.marketing_emails = False
            preferences.booking_notifications = False
            preferences.vendor_notifications = False
            preferences.system_notifications = False
            preferences.opted_out_at = datetime.utcnow()
            preferences.updated_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(preferences)

            logger.info(
                f"User {user_id} opted out of email communications",
                extra={
                    "user_id": user_id,
                    "preferences_id": str(preferences.id),
                    "correlation_id": correlation_id
                }
            )

            return preferences

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error opting out user: {str(e)}")
            raise DatabaseError(f"Failed to opt out user: {str(e)}")

    def check_email_permission(
        self,
        user_id: int,
        email_category: EmailTemplateCategory
    ) -> bool:
        """
        Check if user has permission to receive emails of a specific category.

        Args:
            user_id: User ID
            email_category: Email category to check

        Returns:
            True if user can receive emails of this category
        """
        try:
            preferences = self.get_user_preferences(user_id)
            if not preferences:
                # Create default preferences
                preferences = self.create_default_preferences(user_id)

            # Map categories to preference fields
            category_mapping = {
                EmailTemplateCategory.VERIFICATION: preferences.verification_emails,
                EmailTemplateCategory.PASSWORD_RESET: preferences.security_emails,  # Password reset is security-related
                EmailTemplateCategory.SECURITY: preferences.security_emails,
                EmailTemplateCategory.MARKETING: preferences.marketing_emails,
                EmailTemplateCategory.BOOKING: preferences.booking_notifications,
                EmailTemplateCategory.NOTIFICATION: preferences.system_notifications,
                EmailTemplateCategory.SYSTEM: preferences.system_notifications
            }

            return category_mapping.get(email_category, True)

        except SQLAlchemyError as e:
            logger.error(f"Error checking email permission: {str(e)}")
            # Default to allowing email in case of error
            return True


class EmailQueueRepository(BaseRepository[EmailQueue]):
    """
    Repository for email queue management with priority handling and batch processing.

    Provides comprehensive queue management, priority handling, retry logic,
    and batch processing support with production-grade error handling.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email queue repository."""
        super().__init__(EmailQueue, db)

    def enqueue_email(
        self,
        user_id: Optional[int],
        template_id: Optional[UUID],
        recipient_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        priority: int = 3,
        scheduled_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EmailQueue:
        """
        Add email to the processing queue.

        Args:
            user_id: Optional associated user ID
            template_id: Optional template ID
            recipient_email: Recipient email address
            subject: Email subject
            body: Email body
            html_body: Optional HTML body
            priority: Email priority (1=highest, 5=lowest)
            scheduled_at: Optional scheduled delivery time
            metadata: Optional additional metadata

        Returns:
            Created queue item
        """
        try:
            queue_item = EmailQueue(
                user_id=user_id,
                template_id=template_id,
                recipient_email=recipient_email,
                subject=subject,
                body=body,
                html_body=html_body,
                priority=priority,
                scheduled_at=scheduled_at or datetime.utcnow(),
                status=EmailQueueStatus.QUEUED,
                queue_metadata=metadata or {}
            )

            self.db.add(queue_item)
            self.db.commit()
            self.db.refresh(queue_item)

            logger.info(
                f"Enqueued email for {recipient_email}",
                extra={
                    "queue_id": str(queue_item.id),
                    "recipient": recipient_email,
                    "priority": priority,
                    "scheduled_at": scheduled_at
                }
            )

            return queue_item

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error enqueuing email: {str(e)}")
            raise DatabaseError(f"Failed to enqueue email: {str(e)}")

    def get_pending_emails(
        self,
        limit: int = 100,
        priority_order: bool = True
    ) -> List[EmailQueue]:
        """
        Get pending emails from the queue for processing.

        Args:
            limit: Maximum number of emails to retrieve
            priority_order: Whether to order by priority

        Returns:
            List of pending queue items
        """
        try:
            query = self.db.query(EmailQueue).filter(
                and_(
                    EmailQueue.status == EmailQueueStatus.QUEUED,
                    EmailQueue.scheduled_at <= datetime.utcnow(),
                    EmailQueue.attempts < EmailQueue.max_attempts
                )
            )

            if priority_order:
                query = query.order_by(asc(EmailQueue.priority), asc(EmailQueue.scheduled_at))
            else:
                query = query.order_by(asc(EmailQueue.scheduled_at))

            return query.limit(limit).all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting pending emails: {str(e)}")
            raise DatabaseError(f"Failed to get pending emails: {str(e)}")

    def update_queue_status(
        self,
        queue_id: UUID,
        status: EmailQueueStatus,
        error_message: Optional[str] = None,
        increment_attempts: bool = False
    ) -> EmailQueue:
        """
        Update queue item status.

        Args:
            queue_id: Queue item ID
            status: New status
            error_message: Optional error message
            increment_attempts: Whether to increment attempt count

        Returns:
            Updated queue item
        """
        try:
            queue_item = self.get(queue_id)
            if not queue_item:
                raise NotFoundError(f"Queue item with ID {queue_id} not found")

            queue_item.status = status
            if error_message:
                queue_item.error_message = error_message
            if increment_attempts:
                queue_item.attempts += 1
            if status in [EmailQueueStatus.SENT, EmailQueueStatus.FAILED, EmailQueueStatus.CANCELLED]:
                queue_item.processed_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(queue_item)

            logger.info(
                f"Updated queue status: {queue_item.recipient_email} -> {status.value}",
                extra={
                    "queue_id": str(queue_id),
                    "status": status.value,
                    "attempts": queue_item.attempts
                }
            )

            return queue_item

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error updating queue status: {str(e)}")
            raise DatabaseError(f"Failed to update queue status: {str(e)}")

    def get_failed_queue_items(
        self,
        hours_back: int = 24,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[EmailQueue], int]:
        """
        Get failed queue items for retry processing.

        Args:
            hours_back: Number of hours to look back
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (failed queue items list, total count)
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(hours=hours_back)

            query = self.db.query(EmailQueue).filter(
                and_(
                    EmailQueue.status == EmailQueueStatus.FAILED,
                    EmailQueue.created_at >= cutoff_date,
                    EmailQueue.attempts < EmailQueue.max_attempts
                )
            )

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            queue_items = query.order_by(
                asc(EmailQueue.priority),
                desc(EmailQueue.created_at)
            ).offset(skip).limit(limit).all()

            return queue_items, total

        except SQLAlchemyError as e:
            logger.error(f"Error getting failed queue items: {str(e)}")
            raise DatabaseError(f"Failed to get failed queue items: {str(e)}")

    def cleanup_processed_items(self, days_old: int = 7) -> int:
        """
        Clean up old processed queue items.

        Args:
            days_old: Number of days old items to clean up

        Returns:
            Number of items cleaned up
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)

            deleted_count = self.db.query(EmailQueue).filter(
                and_(
                    EmailQueue.status.in_([EmailQueueStatus.SENT, EmailQueueStatus.CANCELLED]),
                    EmailQueue.processed_at < cutoff_date
                )
            ).delete(synchronize_session=False)

            self.db.commit()

            logger.info(f"Cleaned up {deleted_count} old queue items")

            return deleted_count

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error cleaning up queue items: {str(e)}")
            raise DatabaseError(f"Failed to clean up queue items: {str(e)}")
