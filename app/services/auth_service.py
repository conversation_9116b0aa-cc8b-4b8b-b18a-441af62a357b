"""
Enhanced Authentication service for Culture Connect Backend API.

This module provides comprehensive authentication and authorization services including:
- User registration, login, and profile management
- JWT token management with blacklisting support
- Role-based access control and permissions
- Security event logging and audit trails
- Session management and tracking

Implements Task 2.1.1 requirements for JWT authentication with token blacklisting,
refresh token mechanism, and comprehensive security features.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from fastapi import HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials

from app.core.security import (
    verify_password, get_password_hash, create_token_pair, verify_token,
    TokenData, Token, generate_password_reset_token, check_password_strength,
    has_permission, UserRole, refresh_token_pair, logout_user, token_blacklist
)
from app.core.config import settings
from app.models.user import User, TokenBlacklist, SecurityEvent, UserSession
from app.schemas.auth import (
    UserCreate, UserLogin, UserResponse, PasswordReset, PasswordChange,
    TokenResponse, TokenRefresh, LoginResponse, LogoutRequest, SessionInfo
)
from app.services.email_service import EmailService
from app.services.base import BaseService, ValidationError, NotFoundError, ConflictError
from app.repositories.auth_repository import (
    UserRepository, TokenBlacklistRepository, SecurityEventRepository, UserSessionRepository
)
from app.services.password_security_service import PasswordSecurityService

logger = logging.getLogger(__name__)





class AuthService(BaseService[User, UserRepository]):
    """
    Enhanced authentication service class with JWT token blacklisting support.

    Provides comprehensive authentication and authorization methods including:
    - User registration, login, and profile management
    - JWT token management with blacklisting and refresh
    - Role-based access control and permissions
    - Security event logging and audit trails
    - Session management and tracking
    """

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """
        Initialize enhanced authentication service.

        Args:
            db_session: Optional database session for operations
        """
        super().__init__(
            repository_class=UserRepository,
            model_class=User,
            db_session=db_session
        )
        self.email_service = EmailService()

        # Initialize additional repositories
        self.token_blacklist_repo = TokenBlacklistRepository(TokenBlacklist, db_session)
        self.security_event_repo = SecurityEventRepository(SecurityEvent, db_session)
        self.session_repo = UserSessionRepository(UserSession, db_session)

        # Initialize password security service
        self.password_security_service = PasswordSecurityService(db_session)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for user creation.

        Args:
            data: User creation data

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Check if user already exists
        if 'email' in data:
            async with self.get_session_context() as session:
                repository = UserRepository(User, session)
                existing_user = await repository.get_by_email(data['email'])
                if existing_user:
                    raise ConflictError("User with this email already exists")

        # Validate password strength if provided
        if 'password' in data:
            password_validation = check_password_strength(data['password'])
            if not password_validation["is_valid"]:
                raise ValidationError(
                    "Password does not meet requirements",
                    field="password",
                    details={"errors": password_validation["errors"]}
                )

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate data for user updates.

        Args:
            data: User update data
            existing_id: ID of existing user

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Check email uniqueness if email is being updated
        if 'email' in data:
            async with self.get_session_context() as session:
                repository = UserRepository(User, session)
                existing_user = await repository.get_by_email(data['email'])
                if existing_user and existing_user.id != existing_id:
                    raise ConflictError("Email already in use by another user")

        # Validate password strength if provided
        if 'password' in data:
            password_validation = check_password_strength(data['password'])
            if not password_validation["is_valid"]:
                raise ValidationError(
                    "Password does not meet requirements",
                    field="password",
                    details={"errors": password_validation["errors"]}
                )

        return data

    async def register_user(self, user_data: UserCreate) -> UserResponse:
        """
        Register a new user account.

        Args:
            user_data: User registration data

        Returns:
            UserResponse: Created user information

        Raises:
            ValidationError: If validation fails
            ConflictError: If user already exists
            ServiceError: If registration fails
        """
        correlation = self.log_operation_start("register_user", email=user_data.email)

        try:
            # Prepare user data for creation
            user_dict = {
                "email": user_data.email,
                "hashed_password": get_password_hash(user_data.password),
                "first_name": user_data.first_name,
                "last_name": user_data.last_name,
                "phone_number": user_data.phone_number,
                "role": user_data.role or UserRole.CUSTOMER,
                "is_active": True,
                "is_verified": False,
                "password": user_data.password  # For validation only
            }

            # Use base service create method (includes validation)
            new_user = await self.create(user_dict)

            # Send verification email
            await self._send_verification_email(new_user)

            self.log_operation_success(correlation, f"User registered: {new_user.email}")

            return UserResponse.model_validate(new_user)

        except Exception as e:
            await self.handle_service_error(e, "register_user", {"email": user_data.email})

    async def authenticate_user(self, login_data: UserLogin, client_ip: str = None) -> Tuple[UserResponse, Token]:
        """
        Authenticate user and return user data with tokens with enhanced security.

        Args:
            login_data: User login credentials
            client_ip: Client IP address for security tracking

        Returns:
            Tuple[UserResponse, Token]: User data and authentication tokens

        Raises:
            HTTPException: If authentication fails
        """
        try:
            # Get user by email
            user = await self._get_user_by_email(login_data.email)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )

            # Check account lockout status first
            lockout_status = await self.password_security_service.check_account_lockout(user.id)
            if lockout_status.is_locked:
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail=f"Account is locked. Try again in {lockout_status.remaining_lockout_seconds} seconds."
                )

            # Verify password
            if not verify_password(login_data.password, user.hashed_password):
                # Record failed login attempt
                await self.password_security_service.record_failed_login(user.id, client_ip or "unknown")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )

            # Check if user is active
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account is deactivated"
                )

            # Create token data
            token_data = {
                "sub": str(user.id),  # JWT sub claim must be a string
                "email": user.email,
                "role": user.role,
                "vendor_id": getattr(user, 'vendor_id', None),  # Optional for Phase 1
                "scopes": self._get_user_scopes(user.role)
            }

            # Generate tokens
            tokens = create_token_pair(token_data)

            # Reset account lockout on successful login
            await self.password_security_service.reset_lockout_on_success(user.id)

            # Update last login
            user.last_login = datetime.utcnow()
            await self.db.commit()

            logger.info(f"User authenticated successfully: {user.email}")

            return UserResponse.from_orm(user), tokens

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication failed"
            )

    async def refresh_token(self, refresh_token: str) -> Token:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token

        Returns:
            Token: New token pair

        Raises:
            HTTPException: If refresh token is invalid
        """
        try:
            # Verify refresh token
            token_data = verify_token(refresh_token, token_type="refresh")

            # Get user to ensure they still exist and are active
            user = await self._get_user_by_id(token_data.user_id)
            if not user or not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )

            # Create new token data
            new_token_data = {
                "sub": str(user.id),  # JWT sub claim must be a string
                "email": user.email,
                "role": user.role,
                "vendor_id": getattr(user, 'vendor_id', None),  # Optional for Phase 1
                "scopes": self._get_user_scopes(user.role)
            }

            # Generate new tokens
            tokens = create_token_pair(new_token_data)

            logger.info(f"Token refreshed for user: {user.email}")

            return tokens

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token refresh failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

    async def get_current_user(self, credentials: HTTPAuthorizationCredentials) -> UserResponse:
        """
        Get current user from authorization credentials.

        Args:
            credentials: HTTP authorization credentials

        Returns:
            UserResponse: Current user data

        Raises:
            HTTPException: If token is invalid or user not found
        """
        try:
            # Verify access token
            token_data = verify_token(credentials.credentials, token_type="access")

            # Get user
            user = await self._get_user_by_id(token_data.user_id)
            if not user or not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive"
                )

            return UserResponse.from_orm(user)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Get current user failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

    async def request_password_reset(self, email: str) -> Dict[str, str]:
        """
        Request password reset for user.

        Args:
            email: User email address

        Returns:
            Dict[str, str]: Success message
        """
        try:
            user = await self._get_user_by_email(email)
            if not user:
                # Don't reveal if email exists or not
                return {"message": "If the email exists, a reset link has been sent"}

            # Generate reset token
            reset_token = generate_password_reset_token()
            reset_expires = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry

            # Save reset token
            user.password_reset_token = reset_token
            user.password_reset_expires = reset_expires
            await self.db.commit()

            # Send reset email
            await self._send_password_reset_email(user, reset_token)

            logger.info(f"Password reset requested for: {email}")

            return {"message": "If the email exists, a reset link has been sent"}

        except Exception as e:
            logger.error(f"Password reset request failed: {str(e)}")
            return {"message": "If the email exists, a reset link has been sent"}

    async def reset_password(self, reset_data: PasswordReset) -> Dict[str, str]:
        """
        Reset user password using reset token.

        Args:
            reset_data: Password reset data

        Returns:
            Dict[str, str]: Success message

        Raises:
            HTTPException: If reset token is invalid or expired
        """
        try:
            # Find user by reset token
            query = select(User).where(
                and_(
                    User.password_reset_token == reset_data.token,
                    User.password_reset_expires > datetime.utcnow()
                )
            )
            result = await self.db.execute(query)
            user = result.scalar_one_or_none()

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired reset token"
                )

            # Validate new password
            password_validation = check_password_strength(reset_data.new_password)
            if not password_validation["is_valid"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": "Password does not meet requirements",
                        "errors": password_validation["errors"]
                    }
                )

            # Update password and clear reset token
            user.hashed_password = get_password_hash(reset_data.new_password)
            user.password_reset_token = None
            user.password_reset_expires = None
            user.updated_at = datetime.utcnow()

            await self.db.commit()

            logger.info(f"Password reset completed for user: {user.email}")

            return {"message": "Password reset successfully"}

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Password reset failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed"
            )

    async def change_password(self, user_id: int, password_data: PasswordChange) -> Dict[str, str]:
        """
        Change user password.

        Args:
            user_id: User ID
            password_data: Password change data

        Returns:
            Dict[str, str]: Success message

        Raises:
            HTTPException: If current password is incorrect or new password is invalid
        """
        try:
            user = await self._get_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Verify current password
            if not verify_password(password_data.current_password, user.hashed_password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )

            # Validate password strength using password security service
            validation_result = await self.password_security_service.validate_password_strength(password_data.new_password)
            if not validation_result.is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": "Password does not meet requirements",
                        "violations": validation_result.violations,
                        "suggestions": validation_result.suggestions
                    }
                )

            # Check password history
            if await self.password_security_service.check_password_history(user.id, password_data.new_password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Password has been used recently. Please choose a different password."
                )

            # Update password
            new_password_hash = get_password_hash(password_data.new_password, validate_strength=False)
            user.hashed_password = new_password_hash
            user.updated_at = datetime.utcnow()

            # Add to password history
            await self.password_security_service.add_password_to_history(user.id, new_password_hash)

            await self.db.commit()

            logger.info(f"Password changed for user: {user.email}")

            return {"message": "Password changed successfully"}

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Password change failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password change failed"
            )

    async def refresh_tokens(self, refresh_token: str) -> Token:
        """
        Refresh JWT token pair using valid refresh token.

        Args:
            refresh_token: Valid refresh token

        Returns:
            Token: New token pair

        Raises:
            HTTPException: If refresh token is invalid or blacklisted
        """
        correlation = self.log_operation_start("refresh_tokens")

        try:
            # Use the enhanced refresh_token_pair function from security module
            new_tokens = await refresh_token_pair(refresh_token)

            # Log security event
            token_data = await verify_token(refresh_token, token_type="refresh")
            if token_data:
                await self.security_event_repo.log_event(
                    event_type="token_refresh",
                    description="JWT tokens refreshed successfully",
                    user_id=token_data.user_id,
                    event_category="authentication",
                    severity="info"
                )

            self.log_operation_success(correlation, "Tokens refreshed successfully")
            return new_tokens

        except Exception as e:
            await self.handle_service_error(e, "refresh_tokens", {"refresh_token": "***"})

    async def logout_user_tokens(
        self,
        access_token: str,
        refresh_token: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Logout user by blacklisting their tokens.

        Args:
            access_token: User's access token
            refresh_token: User's refresh token (optional)
            ip_address: Client IP address
            user_agent: Client user agent

        Returns:
            bool: True if logout successful
        """
        correlation = self.log_operation_start("logout_user_tokens")

        try:
            # Verify access token to get user info
            token_data = await verify_token(access_token, token_type="access")
            if not token_data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid access token"
                )

            user_id = token_data.user_id

            # Blacklist access token
            if token_data.jti and token_data.exp:
                await self.token_blacklist_repo.blacklist_token(
                    jti=token_data.jti,
                    user_id=user_id,
                    token_type="access",
                    expires_at=token_data.exp,
                    reason="logout",
                    ip_address=ip_address,
                    user_agent=user_agent
                )

            # Blacklist refresh token if provided
            if refresh_token:
                refresh_token_data = await verify_token(refresh_token, token_type="refresh")
                if refresh_token_data and refresh_token_data.jti and refresh_token_data.exp:
                    await self.token_blacklist_repo.blacklist_token(
                        jti=refresh_token_data.jti,
                        user_id=user_id,
                        token_type="refresh",
                        expires_at=refresh_token_data.exp,
                        reason="logout",
                        ip_address=ip_address,
                        user_agent=user_agent
                    )

            # Log security event
            await self.security_event_repo.log_event(
                event_type="user_logout",
                description="User logged out successfully",
                user_id=user_id,
                event_category="authentication",
                severity="info",
                ip_address=ip_address,
                user_agent=user_agent
            )

            self.log_operation_success(correlation, f"User {user_id} logged out successfully")
            return True

        except Exception as e:
            await self.handle_service_error(e, "logout_user_tokens", {"user_id": "***"})

    async def verify_user_token(self, token: str, token_type: str = "access") -> Optional[TokenData]:
        """
        Verify JWT token with blacklist checking.

        Args:
            token: JWT token to verify
            token_type: Token type (access or refresh)

        Returns:
            TokenData: Token data if valid, None otherwise
        """
        try:
            # Use enhanced verify_token function with blacklist checking
            token_data = await verify_token(token, token_type)
            return token_data

        except HTTPException:
            # Token is invalid or blacklisted
            return None
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            return None

    def check_permission(self, user_role: str, required_permission: str) -> bool:
        """
        Check if user has required permission.

        Args:
            user_role: User's role
            required_permission: Required permission

        Returns:
            bool: True if user has permission, False otherwise
        """
        return has_permission(user_role, required_permission)

    async def _get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        query = select(User).where(User.email == email)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def _get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        query = select(User).where(User.id == user_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    def _get_user_scopes(self, role: str) -> list[str]:
        """Get user scopes based on role."""
        # This can be expanded to include more granular scopes
        scope_mapping = {
            UserRole.SUPER_ADMIN: ["admin", "vendor", "customer"],
            UserRole.ADMIN: ["admin", "vendor", "customer"],
            UserRole.VENDOR: ["vendor"],
            UserRole.CUSTOMER: ["customer"],
            UserRole.MODERATOR: ["moderator"],
        }
        return scope_mapping.get(role, ["customer"])

    async def _send_verification_email(self, user: User):
        """Send email verification to user."""
        # Implementation depends on email service
        pass

    async def _send_password_reset_email(self, user: User, reset_token: str):
        """Send password reset email to user."""
        # Implementation depends on email service
        pass

    async def _log_failed_login(self, user_id: int, email: str):
        """Log failed login attempt."""
        logger.warning(f"Failed login attempt for user {email} (ID: {user_id})")
        # Additional security logging can be implemented here
