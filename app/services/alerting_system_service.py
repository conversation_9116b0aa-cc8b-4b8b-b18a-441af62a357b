"""
Alerting System Service for Culture Connect Backend API.

This module provides comprehensive alerting system services including:
- AlertingSystemService: Main alerting service with alert lifecycle management
- Alert escalation logic with configurable delay and channel management
- Notification delivery management across multiple channels (email, SMS, push, webhook)
- Alert resolution and acknowledgment workflows
- Alert template management and dynamic message generation
- Integration with EnhancedMonitoringService and external notification services
- Circuit breaker patterns for notification service reliability
- Performance targets: >99% alert delivery reliability, <50ms alert generation, <100ms notification processing

Implements Task 6.2.2 Phase 4.4 requirements for comprehensive alerting system
with production-grade error handling, structured logging, and correlation IDs.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4

from app.services.base import BaseService, ServiceError, NotFoundError, ValidationError
from app.repositories.workflow_repositories import (
    WorkflowAlertRepository, AlertTriggerEventRepository, AlertDeliveryRecordRepository
)
from app.models.workflow_models import (
    WorkflowAlert, AlertTriggerEvent, AlertDeliveryRecord,
    AlertSeverity, AlertChannel, AlertDeliveryStatus
)
from app.schemas.workflow_schemas import (
    WorkflowAlertCreate, WorkflowAlertUpdate, WorkflowAlertResponse,
    AlertTriggerEvent as AlertTriggerEventSchema,
    AlertDeliveryStatus as AlertDeliveryStatusSchema
)
import logging
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class AlertingCircuitBreaker:
    """
    Circuit breaker for external alerting services.

    Provides reliability patterns for notification delivery services
    with configurable failure thresholds and recovery timeouts.
    """

    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        timeout: int = 30
    ):
        """Initialize circuit breaker for alerting service."""
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def is_call_allowed(self) -> bool:
        """Check if calls are allowed through the circuit breaker."""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if self.last_failure_time and \
               (datetime.now(timezone.utc) - self.last_failure_time).total_seconds() > self.recovery_timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True

    def record_success(self):
        """Record successful call."""
        self.failure_count = 0
        self.state = "CLOSED"
        self.last_failure_time = None

    def record_failure(self):
        """Record failed call."""
        self.failure_count += 1
        self.last_failure_time = datetime.now(timezone.utc)

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class AlertingSystemService(BaseService[WorkflowAlert, WorkflowAlertRepository]):
    """
    Alerting system service for comprehensive alert lifecycle management.

    Provides end-to-end alerting capabilities including:
    - Alert configuration and management with severity levels and channel configuration
    - Alert escalation logic with configurable delay and channel management
    - Notification delivery management across multiple channels
    - Alert resolution and acknowledgment workflows
    - Alert template management and dynamic message generation
    - Integration with monitoring services and external notification providers
    - Circuit breaker patterns for notification service reliability
    """

    def __init__(
        self,
        repository: WorkflowAlertRepository,
        trigger_repo: AlertTriggerEventRepository,
        delivery_repo: AlertDeliveryRecordRepository,
        correlation_id: Optional[str] = None
    ):
        """Initialize alerting system service with repositories."""
        super().__init__(repository, correlation_id)
        self.trigger_repo = trigger_repo
        self.delivery_repo = delivery_repo

        # Alert management state
        self._active_alerts: Dict[UUID, WorkflowAlert] = {}
        self._alert_templates: Dict[str, str] = {}
        self._escalation_timers: Dict[UUID, asyncio.Task] = {}

        # Circuit breakers for notification services
        self._circuit_breakers: Dict[str, AlertingCircuitBreaker] = {
            "email_service": AlertingCircuitBreaker("email_service"),
            "sms_service": AlertingCircuitBreaker("sms_service"),
            "push_service": AlertingCircuitBreaker("push_service"),
            "webhook_service": AlertingCircuitBreaker("webhook_service"),
            "slack_service": AlertingCircuitBreaker("slack_service")
        }

        # Performance metrics
        self._delivery_metrics: List[Dict[str, Any]] = []
        self._escalation_metrics: List[Dict[str, Any]] = []

        # Configuration
        self.max_delivery_attempts = 3
        self.delivery_retry_delay = 30  # seconds
        self.escalation_enabled = True

        logger.info(
            "AlertingSystemService initialized",
            extra={
                "max_delivery_attempts": self.max_delivery_attempts,
                "delivery_retry_delay": self.delivery_retry_delay,
                "escalation_enabled": self.escalation_enabled,
                "correlation_id": getattr(self, 'correlation_id', correlation_id)
            }
        )

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate alert creation data.

        Args:
            data: Alert creation data

        Returns:
            Validated data dictionary

        Raises:
            ValidationError: If validation fails
        """
        try:
            # Required fields validation
            required_fields = ["name", "condition_type", "condition_expression"]
            for field in required_fields:
                if field not in data:
                    raise ValidationError(f"Missing required field: {field}")

            # Validate alert name
            if not data["name"] or len(data["name"]) < 3:
                raise ValidationError("Alert name must be at least 3 characters long")

            # Validate condition type
            valid_condition_types = [
                "execution_failure", "execution_timeout", "performance_degradation",
                "resource_exhaustion", "dependency_failure", "custom"
            ]
            if data["condition_type"] not in valid_condition_types:
                raise ValidationError(f"Invalid condition type. Must be one of: {valid_condition_types}")

            # Validate notification channels
            if "notification_channels" in data:
                valid_channels = [channel.value for channel in AlertChannel]
                for channel in data["notification_channels"]:
                    if channel not in valid_channels:
                        raise ValidationError(f"Invalid notification channel: {channel}")

            # Validate escalation configuration
            if "escalation_delay_minutes" in data:
                if data["escalation_delay_minutes"] <= 0:
                    raise ValidationError("Escalation delay must be positive")

            if "max_escalations" in data:
                if data["max_escalations"] <= 0:
                    raise ValidationError("Max escalations must be positive")

            # Set defaults
            validated_data = data.copy()
            validated_data.setdefault("severity", AlertSeverity.MEDIUM)
            validated_data.setdefault("notification_channels", [AlertChannel.EMAIL.value])
            validated_data.setdefault("escalation_delay_minutes", 30)
            validated_data.setdefault("max_escalations", 3)
            validated_data.setdefault("is_active", True)
            validated_data.setdefault("is_muted", False)
            validated_data.setdefault("condition_data", {})
            validated_data.setdefault("notification_config", {})
            validated_data.setdefault("escalation_channels", [])
            validated_data.setdefault("alert_data", {})
            validated_data.setdefault("alert_metadata", {})

            return validated_data

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                "Alert validation failed",
                extra={
                    "error": str(e),
                    "data": data,
                    "correlation_id": self.correlation_id
                }
            )
            raise ValidationError(f"Alert validation failed: {str(e)}")

    async def validate_update_data(self, data: Dict[str, Any], alert_id: UUID) -> Dict[str, Any]:
        """
        Validate alert update data.

        Args:
            data: Alert update data
            alert_id: Alert ID being updated

        Returns:
            Validated data dictionary

        Raises:
            ValidationError: If validation fails
        """
        try:
            # Validate notification channels if provided
            if "notification_channels" in data and data["notification_channels"]:
                valid_channels = [channel.value for channel in AlertChannel]
                for channel in data["notification_channels"]:
                    if channel not in valid_channels:
                        raise ValidationError(f"Invalid notification channel: {channel}")

            # Validate escalation configuration if provided
            if "escalation_delay_minutes" in data and data["escalation_delay_minutes"] is not None:
                if data["escalation_delay_minutes"] <= 0:
                    raise ValidationError("Escalation delay must be positive")

            if "max_escalations" in data and data["max_escalations"] is not None:
                if data["max_escalations"] <= 0:
                    raise ValidationError("Max escalations must be positive")

            # Validate mute configuration
            if "muted_until" in data and data["muted_until"]:
                if data["muted_until"] <= datetime.now(timezone.utc):
                    raise ValidationError("Mute expiration must be in the future")

            return data

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                "Alert update validation failed",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "data": data,
                    "correlation_id": self.correlation_id
                }
            )
            raise ValidationError(f"Alert update validation failed: {str(e)}")

    # Alert Configuration Management

    async def create_alert_configuration(
        self,
        alert_data: WorkflowAlertCreate,
        load_to_cache: bool = True
    ) -> WorkflowAlert:
        """
        Create a new alert configuration.

        Args:
            alert_data: Alert configuration data
            load_to_cache: Whether to load alert to active cache

        Returns:
            Created alert configuration

        Raises:
            ValidationError: If alert data is invalid
            ServiceError: If creation fails
        """
        start_time = time.time()

        try:
            # Validate alert data
            validated_data = await self.validate_create_data(alert_data.model_dump())

            # Create alert configuration
            alert = await self.repository.create_alert(alert_data, self.correlation_id)

            # Load to active cache if requested and alert is active
            if load_to_cache and alert.is_active:
                self._active_alerts[alert.id] = alert

            # Record performance metrics
            creation_time = (time.time() - start_time) * 1000
            logger.info(
                "Alert configuration created",
                extra={
                    "alert_id": str(alert.id),
                    "alert_name": alert.name,
                    "severity": alert.severity.value,
                    "condition_type": alert.condition_type,
                    "creation_time_ms": creation_time,
                    "correlation_id": self.correlation_id
                }
            )

            return alert

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                "Failed to create alert configuration",
                extra={
                    "error": str(e),
                    "alert_data": alert_data.model_dump() if alert_data else None,
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to create alert configuration: {str(e)}")

    async def update_alert_configuration(
        self,
        alert_id: UUID,
        update_data: WorkflowAlertUpdate
    ) -> WorkflowAlert:
        """
        Update an existing alert configuration.

        Args:
            alert_id: Alert ID to update
            update_data: Alert update data

        Returns:
            Updated alert configuration

        Raises:
            NotFoundError: If alert not found
            ValidationError: If update data is invalid
            ServiceError: If update fails
        """
        try:
            # Validate update data
            validated_data = await self.validate_update_data(update_data.model_dump(exclude_unset=True), alert_id)

            # Update alert configuration
            alert = await self.repository.update_alert(alert_id, update_data, self.correlation_id)

            # Update active cache
            if alert.is_active:
                self._active_alerts[alert.id] = alert
            elif alert.id in self._active_alerts:
                del self._active_alerts[alert.id]

            logger.info(
                "Alert configuration updated",
                extra={
                    "alert_id": str(alert_id),
                    "alert_name": alert.name,
                    "is_active": alert.is_active,
                    "correlation_id": self.correlation_id
                }
            )

            return alert

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(
                "Failed to update alert configuration",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to update alert configuration: {str(e)}")

    async def delete_alert_configuration(self, alert_id: UUID) -> bool:
        """
        Delete an alert configuration.

        Args:
            alert_id: Alert ID to delete

        Returns:
            True if deleted successfully

        Raises:
            NotFoundError: If alert not found
            ServiceError: If deletion fails
        """
        try:
            # Remove from active cache
            if alert_id in self._active_alerts:
                del self._active_alerts[alert_id]

            # Cancel any active escalation timers
            if alert_id in self._escalation_timers:
                self._escalation_timers[alert_id].cancel()
                del self._escalation_timers[alert_id]

            # Delete alert configuration
            success = await self.repository.delete_alert(alert_id, self.correlation_id)

            logger.info(
                "Alert configuration deleted",
                extra={
                    "alert_id": str(alert_id),
                    "success": success,
                    "correlation_id": self.correlation_id
                }
            )

            return success

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to delete alert configuration",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to delete alert configuration: {str(e)}")

    async def get_alert_configuration(self, alert_id: UUID) -> WorkflowAlert:
        """
        Get alert configuration by ID.

        Args:
            alert_id: Alert ID to retrieve

        Returns:
            Alert configuration

        Raises:
            NotFoundError: If alert not found
        """
        try:
            # Check active cache first
            if alert_id in self._active_alerts:
                return self._active_alerts[alert_id]

            # Retrieve from repository
            alert = await self.repository.get_alert_by_id(alert_id, self.correlation_id)

            # Cache if active
            if alert.is_active:
                self._active_alerts[alert_id] = alert

            return alert

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to get alert configuration",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to get alert configuration: {str(e)}")

    async def list_alert_configurations(
        self,
        workflow_definition_id: Optional[UUID] = None,
        severity: Optional[AlertSeverity] = None,
        is_active: Optional[bool] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[WorkflowAlert]:
        """
        List alert configurations with optional filtering.

        Args:
            workflow_definition_id: Filter by workflow definition
            severity: Filter by alert severity
            is_active: Filter by active status
            limit: Maximum number of results
            offset: Results offset

        Returns:
            List of alert configurations
        """
        try:
            alerts = await self.repository.list_alerts(
                workflow_definition_id=workflow_definition_id,
                severity=severity,
                is_active=is_active,
                limit=limit,
                offset=offset,
                correlation_id=self.correlation_id
            )

            # Update active cache for active alerts
            for alert in alerts:
                if alert.is_active:
                    self._active_alerts[alert.id] = alert

            return alerts

        except Exception as e:
            logger.error(
                "Failed to list alert configurations",
                extra={
                    "workflow_definition_id": str(workflow_definition_id) if workflow_definition_id else None,
                    "severity": severity.value if severity else None,
                    "is_active": is_active,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to list alert configurations: {str(e)}")

    # Alert Triggering and Notification Management

    async def trigger_alert(
        self,
        alert_id: UUID,
        trigger_condition: str,
        alert_message: str,
        severity: AlertSeverity,
        workflow_execution_id: Optional[UUID] = None,
        trigger_value: Optional[str] = None,
        threshold_value: Optional[str] = None,
        alert_context: Optional[Dict[str, Any]] = None
    ) -> AlertTriggerEvent:
        """
        Trigger an alert and initiate notification delivery.

        Args:
            alert_id: Alert configuration ID
            trigger_condition: Condition that triggered the alert
            alert_message: Alert message content
            severity: Alert severity level
            workflow_execution_id: Related workflow execution ID
            trigger_value: Value that triggered the alert
            threshold_value: Configured threshold value
            alert_context: Additional alert context data

        Returns:
            Created alert trigger event

        Raises:
            NotFoundError: If alert configuration not found
            ServiceError: If alert triggering fails
        """
        start_time = time.time()

        try:
            # Get alert configuration
            alert_config = await self.get_alert_configuration(alert_id)

            # Check if alert is active and not muted
            if not alert_config.is_active:
                logger.warning(
                    "Attempted to trigger inactive alert",
                    extra={
                        "alert_id": str(alert_id),
                        "alert_name": alert_config.name,
                        "correlation_id": self.correlation_id
                    }
                )
                raise ValidationError("Cannot trigger inactive alert")

            if alert_config.is_muted:
                # Check if mute period has expired
                if alert_config.muted_until and alert_config.muted_until > datetime.now(timezone.utc):
                    logger.info(
                        "Alert is muted, skipping trigger",
                        extra={
                            "alert_id": str(alert_id),
                            "muted_until": alert_config.muted_until.isoformat(),
                            "correlation_id": self.correlation_id
                        }
                    )
                    raise ValidationError("Alert is currently muted")

            # Create alert trigger event
            trigger_event = await self.trigger_repo.create_trigger_event(
                alert_id=alert_id,
                workflow_execution_id=workflow_execution_id,
                trigger_condition=trigger_condition,
                trigger_value=trigger_value,
                threshold_value=threshold_value,
                severity=severity,
                alert_message=alert_message,
                alert_context=alert_context or {},
                correlation_id=self.correlation_id
            )

            # Start notification delivery process
            asyncio.create_task(self._deliver_notifications(trigger_event, alert_config))

            # Start escalation timer if configured
            if self.escalation_enabled and alert_config.escalation_delay_minutes:
                escalation_task = asyncio.create_task(
                    self._schedule_escalation(trigger_event, alert_config)
                )
                self._escalation_timers[trigger_event.id] = escalation_task

            # Record performance metrics
            trigger_time = (time.time() - start_time) * 1000
            logger.info(
                "Alert triggered successfully",
                extra={
                    "alert_id": str(alert_id),
                    "trigger_event_id": str(trigger_event.id),
                    "severity": severity.value,
                    "trigger_condition": trigger_condition,
                    "trigger_time_ms": trigger_time,
                    "correlation_id": self.correlation_id
                }
            )

            return trigger_event

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(
                "Failed to trigger alert",
                extra={
                    "alert_id": str(alert_id),
                    "trigger_condition": trigger_condition,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to trigger alert: {str(e)}")

    async def acknowledge_alert(
        self,
        trigger_event_id: UUID,
        acknowledged_by: str,
        acknowledgment_message: Optional[str] = None
    ) -> AlertTriggerEvent:
        """
        Acknowledge an alert trigger event.

        Args:
            trigger_event_id: Alert trigger event ID
            acknowledged_by: User who acknowledged the alert
            acknowledgment_message: Optional acknowledgment message

        Returns:
            Updated alert trigger event

        Raises:
            NotFoundError: If trigger event not found
            ServiceError: If acknowledgment fails
        """
        try:
            # Acknowledge the trigger event
            trigger_event = await self.trigger_repo.acknowledge_trigger_event(
                trigger_event_id=trigger_event_id,
                acknowledged_by=acknowledged_by,
                acknowledgment_message=acknowledgment_message,
                correlation_id=self.correlation_id
            )

            # Cancel escalation timer if active
            if trigger_event_id in self._escalation_timers:
                self._escalation_timers[trigger_event_id].cancel()
                del self._escalation_timers[trigger_event_id]

            logger.info(
                "Alert acknowledged",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "acknowledged_by": acknowledged_by,
                    "acknowledgment_message": acknowledgment_message,
                    "correlation_id": self.correlation_id
                }
            )

            return trigger_event

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to acknowledge alert",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "acknowledged_by": acknowledged_by,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to acknowledge alert: {str(e)}")

    async def resolve_alert(
        self,
        trigger_event_id: UUID,
        resolved_by: str,
        resolution_message: Optional[str] = None
    ) -> AlertTriggerEvent:
        """
        Resolve an alert trigger event.

        Args:
            trigger_event_id: Alert trigger event ID
            resolved_by: User who resolved the alert
            resolution_message: Optional resolution message

        Returns:
            Updated alert trigger event

        Raises:
            NotFoundError: If trigger event not found
            ServiceError: If resolution fails
        """
        try:
            # Resolve the trigger event
            trigger_event = await self.trigger_repo.resolve_trigger_event(
                trigger_event_id=trigger_event_id,
                resolved_by=resolved_by,
                resolution_message=resolution_message,
                correlation_id=self.correlation_id
            )

            # Cancel escalation timer if active
            if trigger_event_id in self._escalation_timers:
                self._escalation_timers[trigger_event_id].cancel()
                del self._escalation_timers[trigger_event_id]

            logger.info(
                "Alert resolved",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "resolved_by": resolved_by,
                    "resolution_message": resolution_message,
                    "correlation_id": self.correlation_id
                }
            )

            return trigger_event

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to resolve alert",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "resolved_by": resolved_by,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to resolve alert: {str(e)}")

    # Private Helper Methods

    async def _deliver_notifications(
        self,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert
    ) -> None:
        """
        Deliver notifications for an alert trigger event.

        Args:
            trigger_event: Alert trigger event
            alert_config: Alert configuration
        """
        try:
            # Get notification channels
            channels = alert_config.notification_channels or [AlertChannel.EMAIL.value]

            # Deliver to each channel
            for channel in channels:
                await self._deliver_to_channel(trigger_event, alert_config, channel)

            logger.info(
                "Notifications delivered",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "channels": channels,
                    "correlation_id": self.correlation_id
                }
            )

        except Exception as e:
            logger.error(
                "Failed to deliver notifications",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )

    async def _deliver_to_channel(
        self,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert,
        channel: str
    ) -> None:
        """
        Deliver notification to a specific channel.

        Args:
            trigger_event: Alert trigger event
            alert_config: Alert configuration
            channel: Notification channel
        """
        start_time = time.time()
        delivery_record = None

        try:
            # Create delivery record
            delivery_record = await self.delivery_repo.create_delivery_record(
                trigger_event_id=trigger_event.id,
                channel=channel,
                correlation_id=self.correlation_id
            )

            # Check circuit breaker
            service_name = f"{channel}_service"
            circuit_breaker = self._circuit_breakers.get(service_name)

            if circuit_breaker and not circuit_breaker.is_call_allowed():
                logger.warning(
                    "Circuit breaker open, skipping delivery",
                    extra={
                        "channel": channel,
                        "service_name": service_name,
                        "correlation_id": self.correlation_id
                    }
                )
                await self.delivery_repo.update_delivery_status(
                    delivery_record.id,
                    AlertDeliveryStatus.FAILED,
                    error_message="Circuit breaker open",
                    correlation_id=self.correlation_id
                )
                return

            # Prepare notification content
            notification_content = await self._prepare_notification_content(
                trigger_event, alert_config, channel
            )

            # Deliver notification (mock implementation for now)
            success = await self._send_notification(channel, notification_content)

            if success:
                # Record success
                if circuit_breaker:
                    circuit_breaker.record_success()

                await self.delivery_repo.update_delivery_status(
                    delivery_record.id,
                    AlertDeliveryStatus.SENT,
                    correlation_id=self.correlation_id
                )

                # Record performance metrics
                delivery_time = (time.time() - start_time) * 1000
                self._delivery_metrics.append({
                    "channel": channel,
                    "delivery_time_ms": delivery_time,
                    "success": True,
                    "timestamp": datetime.now(timezone.utc)
                })

            else:
                # Record failure
                if circuit_breaker:
                    circuit_breaker.record_failure()

                await self.delivery_repo.update_delivery_status(
                    delivery_record.id,
                    AlertDeliveryStatus.FAILED,
                    error_message="Delivery failed",
                    correlation_id=self.correlation_id
                )

                # Schedule retry if attempts remaining
                if delivery_record.attempt_count < self.max_delivery_attempts:
                    asyncio.create_task(
                        self._schedule_retry(delivery_record, trigger_event, alert_config, channel)
                    )

        except Exception as e:
            logger.error(
                "Failed to deliver to channel",
                extra={
                    "channel": channel,
                    "trigger_event_id": str(trigger_event.id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )

            # Record failure in delivery record if created
            if delivery_record:
                try:
                    await self.delivery_repo.update_delivery_status(
                        delivery_record.id,
                        AlertDeliveryStatus.FAILED,
                        error_message=str(e),
                        correlation_id=self.correlation_id
                    )
                except Exception:
                    pass  # Avoid cascading failures

    async def _prepare_notification_content(
        self,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert,
        channel: str
    ) -> Dict[str, Any]:
        """
        Prepare notification content for a specific channel.

        Args:
            trigger_event: Alert trigger event
            alert_config: Alert configuration
            channel: Notification channel

        Returns:
            Notification content dictionary
        """
        # Base content
        content = {
            "alert_name": alert_config.name,
            "severity": trigger_event.severity.value,
            "message": trigger_event.alert_message,
            "trigger_condition": trigger_event.trigger_condition,
            "triggered_at": trigger_event.triggered_at.isoformat(),
            "alert_id": str(alert_config.id),
            "trigger_event_id": str(trigger_event.id)
        }

        # Add trigger values if available
        if trigger_event.trigger_value:
            content["trigger_value"] = trigger_event.trigger_value
        if trigger_event.threshold_value:
            content["threshold_value"] = trigger_event.threshold_value

        # Add workflow context if available
        if trigger_event.workflow_execution_id:
            content["workflow_execution_id"] = str(trigger_event.workflow_execution_id)

        # Channel-specific formatting
        if channel == AlertChannel.EMAIL.value:
            content["subject"] = f"[{trigger_event.severity.value.upper()}] {alert_config.name}"
            content["html_body"] = self._format_email_body(content)
        elif channel == AlertChannel.SLACK.value:
            content["slack_blocks"] = self._format_slack_blocks(content)
        elif channel == AlertChannel.SMS.value:
            content["sms_text"] = self._format_sms_text(content)
        elif channel == AlertChannel.WEBHOOK.value:
            content["webhook_payload"] = content  # Use full content as payload

        return content

    def _format_email_body(self, content: Dict[str, Any]) -> str:
        """Format email body content."""
        return f"""
        <h2>Alert: {content['alert_name']}</h2>
        <p><strong>Severity:</strong> {content['severity'].upper()}</p>
        <p><strong>Message:</strong> {content['message']}</p>
        <p><strong>Trigger Condition:</strong> {content['trigger_condition']}</p>
        <p><strong>Triggered At:</strong> {content['triggered_at']}</p>
        {f"<p><strong>Trigger Value:</strong> {content.get('trigger_value', 'N/A')}</p>" if content.get('trigger_value') else ""}
        {f"<p><strong>Threshold Value:</strong> {content.get('threshold_value', 'N/A')}</p>" if content.get('threshold_value') else ""}
        """

    def _format_slack_blocks(self, content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format Slack blocks content."""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"🚨 {content['alert_name']}"
                }
            },
            {
                "type": "section",
                "fields": [
                    {"type": "mrkdwn", "text": f"*Severity:* {content['severity'].upper()}"},
                    {"type": "mrkdwn", "text": f"*Condition:* {content['trigger_condition']}"},
                    {"type": "mrkdwn", "text": f"*Message:* {content['message']}"},
                    {"type": "mrkdwn", "text": f"*Time:* {content['triggered_at']}"}
                ]
            }
        ]

    def _format_sms_text(self, content: Dict[str, Any]) -> str:
        """Format SMS text content."""
        return f"ALERT [{content['severity'].upper()}]: {content['alert_name']} - {content['message']}"

    async def _send_notification(self, channel: str, content: Dict[str, Any]) -> bool:
        """
        Send notification to external service (mock implementation).

        Args:
            channel: Notification channel
            content: Notification content

        Returns:
            True if successful, False otherwise
        """
        # Mock implementation - in production, this would integrate with actual services
        try:
            # Simulate network delay
            await asyncio.sleep(0.1)

            # Mock success rate (95% success for testing)
            import random
            return random.random() < 0.95

        except Exception:
            return False

    async def _schedule_retry(
        self,
        delivery_record: AlertDeliveryRecord,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert,
        channel: str
    ) -> None:
        """
        Schedule retry for failed notification delivery.

        Args:
            delivery_record: Failed delivery record
            trigger_event: Alert trigger event
            alert_config: Alert configuration
            channel: Notification channel
        """
        try:
            # Wait for retry delay
            await asyncio.sleep(self.delivery_retry_delay)

            # Retry delivery
            await self._deliver_to_channel(trigger_event, alert_config, channel)

        except Exception as e:
            logger.error(
                "Failed to retry notification delivery",
                extra={
                    "delivery_record_id": str(delivery_record.id),
                    "channel": channel,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )

    async def _schedule_escalation(
        self,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert
    ) -> None:
        """
        Schedule alert escalation.

        Args:
            trigger_event: Alert trigger event
            alert_config: Alert configuration
        """
        try:
            # Wait for escalation delay
            delay_seconds = alert_config.escalation_delay_minutes * 60
            await asyncio.sleep(delay_seconds)

            # Check if alert is still unresolved
            current_trigger = await self.trigger_repo.get_trigger_event_by_id(
                trigger_event.id, self.correlation_id
            )

            if not current_trigger.is_resolved and not current_trigger.is_acknowledged:
                # Escalate alert
                await self._escalate_alert(current_trigger, alert_config)

        except Exception as e:
            logger.error(
                "Failed to schedule escalation",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )

    async def _escalate_alert(
        self,
        trigger_event: AlertTriggerEvent,
        alert_config: WorkflowAlert
    ) -> None:
        """
        Escalate an unresolved alert.

        Args:
            trigger_event: Alert trigger event
            alert_config: Alert configuration
        """
        try:
            # Check escalation limits
            if trigger_event.escalation_level >= alert_config.max_escalations:
                logger.warning(
                    "Maximum escalation level reached",
                    extra={
                        "trigger_event_id": str(trigger_event.id),
                        "escalation_level": trigger_event.escalation_level,
                        "max_escalations": alert_config.max_escalations,
                        "correlation_id": self.correlation_id
                    }
                )
                return

            # Escalate the trigger event
            escalated_trigger = await self.trigger_repo.escalate_trigger_event(
                trigger_event.id, self.correlation_id
            )

            # Send escalation notifications
            escalation_channels = alert_config.escalation_channels or alert_config.notification_channels
            for channel in escalation_channels:
                await self._deliver_to_channel(escalated_trigger, alert_config, channel)

            # Record escalation metrics
            self._escalation_metrics.append({
                "trigger_event_id": str(trigger_event.id),
                "escalation_level": escalated_trigger.escalation_level,
                "timestamp": datetime.now(timezone.utc)
            })

            # Schedule next escalation if not at max level
            if escalated_trigger.escalation_level < alert_config.max_escalations:
                escalation_task = asyncio.create_task(
                    self._schedule_escalation(escalated_trigger, alert_config)
                )
                self._escalation_timers[escalated_trigger.id] = escalation_task

            logger.info(
                "Alert escalated",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "escalation_level": escalated_trigger.escalation_level,
                    "correlation_id": self.correlation_id
                }
            )

        except Exception as e:
            logger.error(
                "Failed to escalate alert",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )

    # Alert Analytics and Reporting

    async def get_alert_statistics(
        self,
        time_range_hours: int = 24,
        workflow_definition_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """
        Get alert statistics for the specified time range.

        Args:
            time_range_hours: Time range in hours
            workflow_definition_id: Optional workflow definition filter

        Returns:
            Alert statistics dictionary
        """
        try:
            start_time = datetime.now(timezone.utc) - timedelta(hours=time_range_hours)

            # Get alert statistics from repository
            stats = await self.trigger_repo.get_alert_statistics(
                start_time=start_time,
                workflow_definition_id=workflow_definition_id,
                correlation_id=self.correlation_id
            )

            # Add delivery metrics
            delivery_stats = self._calculate_delivery_statistics(start_time)
            stats.update(delivery_stats)

            # Add escalation metrics
            escalation_stats = self._calculate_escalation_statistics(start_time)
            stats.update(escalation_stats)

            return stats

        except Exception as e:
            logger.error(
                "Failed to get alert statistics",
                extra={
                    "time_range_hours": time_range_hours,
                    "error": str(e),
                    "correlation_id": self.correlation_id
                }
            )
            raise ServiceError(f"Failed to get alert statistics: {str(e)}")

    def _calculate_delivery_statistics(self, start_time: datetime) -> Dict[str, Any]:
        """Calculate delivery performance statistics."""
        recent_metrics = [
            m for m in self._delivery_metrics
            if m["timestamp"] >= start_time
        ]

        if not recent_metrics:
            return {
                "delivery_success_rate": 0.0,
                "average_delivery_time_ms": 0.0,
                "total_deliveries": 0
            }

        successful_deliveries = [m for m in recent_metrics if m["success"]]
        success_rate = len(successful_deliveries) / len(recent_metrics) * 100

        avg_delivery_time = sum(m["delivery_time_ms"] for m in successful_deliveries) / len(successful_deliveries) if successful_deliveries else 0

        return {
            "delivery_success_rate": round(success_rate, 2),
            "average_delivery_time_ms": round(avg_delivery_time, 2),
            "total_deliveries": len(recent_metrics)
        }

    def _calculate_escalation_statistics(self, start_time: datetime) -> Dict[str, Any]:
        """Calculate escalation statistics."""
        recent_escalations = [
            m for m in self._escalation_metrics
            if m["timestamp"] >= start_time
        ]

        return {
            "total_escalations": len(recent_escalations),
            "escalation_levels": {
                level: len([m for m in recent_escalations if m["escalation_level"] == level])
                for level in range(1, 4)  # Levels 1-3
            }
        }
