"""
Performance Metrics Repository for Culture Connect Backend API.

This module provides data access layer for performance metrics operations including:
- Time-series data queries and aggregation methods
- Trend analysis and comparative metrics
- Performance benchmarking and analytics
- Integration with service and vendor performance tracking

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade async operations and comprehensive analytics capabilities.
"""

import logging
from datetime import datetime, timezone, timedelta, date
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from app.models.marketplace_optimization import PerformanceMetrics
from app.models.service import Service
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository, RepositoryError


logger = logging.getLogger(__name__)


class PerformanceMetricsRepository(BaseRepository[PerformanceMetrics]):
    """
    Repository for performance metrics data access operations.
    
    Provides specialized methods for:
    - Time-series data queries and aggregation
    - Trend analysis and comparative metrics
    - Performance benchmarking and analytics
    - Service and vendor performance tracking workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize performance metrics repository."""
        super().__init__(PerformanceMetrics, db)

    async def get_latest_by_service(self, service_id: int) -> Optional[PerformanceMetrics]:
        """
        Get the latest performance metrics for a service.
        
        Args:
            service_id: Service ID to get metrics for
            
        Returns:
            Latest performance metrics or None if not found
        """
        try:
            result = await self.db.execute(
                Select(PerformanceMetrics)
                .where(PerformanceMetrics.service_id == service_id)
                .order_by(desc(PerformanceMetrics.metric_date))
                .limit(1)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get latest performance metrics for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get latest performance metrics", e)

    async def get_time_series_data(
        self,
        service_id: int,
        start_date: date,
        end_date: date,
        metric_period: str = "daily"
    ) -> List[PerformanceMetrics]:
        """
        Get time-series performance data for a service.
        
        Args:
            service_id: Service ID to get data for
            start_date: Start date for data range
            end_date: End date for data range
            metric_period: Period type (daily, weekly, monthly)
            
        Returns:
            List of performance metrics ordered by date
        """
        try:
            result = await self.db.execute(
                Select(PerformanceMetrics)
                .where(
                    and_(
                        PerformanceMetrics.service_id == service_id,
                        PerformanceMetrics.metric_date >= start_date,
                        PerformanceMetrics.metric_date <= end_date,
                        PerformanceMetrics.metric_period == metric_period
                    )
                )
                .order_by(asc(PerformanceMetrics.metric_date))
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get time series data for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get time series data", e)

    async def get_aggregated_metrics(
        self,
        service_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """
        Get aggregated performance metrics for a service over a date range.
        
        Args:
            service_id: Service ID to aggregate for
            start_date: Start date for aggregation
            end_date: End date for aggregation
            
        Returns:
            Aggregated metrics including totals, averages, and trends
        """
        try:
            result = await self.db.execute(
                Select(
                    func.sum(PerformanceMetrics.listing_views).label('total_views'),
                    func.sum(PerformanceMetrics.unique_views).label('total_unique_views'),
                    func.sum(PerformanceMetrics.search_impressions).label('total_impressions'),
                    func.sum(PerformanceMetrics.contact_clicks).label('total_contacts'),
                    func.sum(PerformanceMetrics.booking_inquiries).label('total_inquiries'),
                    func.sum(PerformanceMetrics.bookings_completed).label('total_bookings'),
                    func.sum(PerformanceMetrics.revenue_generated).label('total_revenue'),
                    func.avg(PerformanceMetrics.conversion_rate).label('avg_conversion_rate'),
                    func.avg(PerformanceMetrics.click_through_rate).label('avg_ctr'),
                    func.avg(PerformanceMetrics.engagement_rate).label('avg_engagement'),
                    func.avg(PerformanceMetrics.bounce_rate).label('avg_bounce_rate'),
                    func.avg(PerformanceMetrics.average_time_on_page).label('avg_time_on_page'),
                    func.count(PerformanceMetrics.id).label('total_records')
                )
                .where(
                    and_(
                        PerformanceMetrics.service_id == service_id,
                        PerformanceMetrics.metric_date >= start_date,
                        PerformanceMetrics.metric_date <= end_date
                    )
                )
            )
            row = result.first()
            
            return {
                "totals": {
                    "views": row.total_views or 0,
                    "unique_views": row.total_unique_views or 0,
                    "impressions": row.total_impressions or 0,
                    "contacts": row.total_contacts or 0,
                    "inquiries": row.total_inquiries or 0,
                    "bookings": row.total_bookings or 0,
                    "revenue": float(row.total_revenue or 0)
                },
                "averages": {
                    "conversion_rate": float(row.avg_conversion_rate or 0),
                    "click_through_rate": float(row.avg_ctr or 0),
                    "engagement_rate": float(row.avg_engagement or 0),
                    "bounce_rate": float(row.avg_bounce_rate or 0),
                    "time_on_page": float(row.avg_time_on_page or 0)
                },
                "period_info": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "total_records": row.total_records
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get aggregated metrics for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get aggregated metrics", e)

    async def get_vendor_performance_summary(
        self,
        vendor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """
        Get performance summary for all vendor services.
        
        Args:
            vendor_id: Vendor ID to summarize for
            start_date: Start date for summary
            end_date: End date for summary
            
        Returns:
            Vendor performance summary across all services
        """
        try:
            result = await self.db.execute(
                Select(
                    func.sum(PerformanceMetrics.listing_views).label('total_views'),
                    func.sum(PerformanceMetrics.unique_views).label('total_unique_views'),
                    func.sum(PerformanceMetrics.contact_clicks).label('total_contacts'),
                    func.sum(PerformanceMetrics.booking_inquiries).label('total_inquiries'),
                    func.sum(PerformanceMetrics.bookings_completed).label('total_bookings'),
                    func.sum(PerformanceMetrics.revenue_generated).label('total_revenue'),
                    func.avg(PerformanceMetrics.conversion_rate).label('avg_conversion_rate'),
                    func.count(func.distinct(PerformanceMetrics.service_id)).label('active_services')
                )
                .where(
                    and_(
                        PerformanceMetrics.vendor_id == vendor_id,
                        PerformanceMetrics.metric_date >= start_date,
                        PerformanceMetrics.metric_date <= end_date
                    )
                )
            )
            row = result.first()
            
            return {
                "vendor_id": vendor_id,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "totals": {
                    "views": row.total_views or 0,
                    "unique_views": row.total_unique_views or 0,
                    "contacts": row.total_contacts or 0,
                    "inquiries": row.total_inquiries or 0,
                    "bookings": row.total_bookings or 0,
                    "revenue": float(row.total_revenue or 0)
                },
                "averages": {
                    "conversion_rate": float(row.avg_conversion_rate or 0)
                },
                "active_services": row.active_services or 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get vendor performance summary for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get vendor performance summary", e)

    async def get_top_performing_services(
        self,
        metric: str = "conversion_rate",
        limit: int = 10,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        Get top performing services by specified metric.
        
        Args:
            metric: Metric to rank by (conversion_rate, revenue_generated, etc.)
            limit: Number of top services to return
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of top performing services with metrics
        """
        try:
            # Map metric names to database columns
            metric_column_map = {
                "conversion_rate": PerformanceMetrics.conversion_rate,
                "revenue_generated": PerformanceMetrics.revenue_generated,
                "listing_views": PerformanceMetrics.listing_views,
                "booking_inquiries": PerformanceMetrics.booking_inquiries,
                "engagement_rate": PerformanceMetrics.engagement_rate
            }
            
            if metric not in metric_column_map:
                raise ValueError(f"Invalid metric: {metric}")
            
            metric_column = metric_column_map[metric]
            
            # Build query with aggregation by service
            query = (
                Select(
                    PerformanceMetrics.service_id,
                    func.avg(metric_column).label('avg_metric'),
                    func.sum(PerformanceMetrics.listing_views).label('total_views'),
                    func.sum(PerformanceMetrics.revenue_generated).label('total_revenue')
                )
                .group_by(PerformanceMetrics.service_id)
                .order_by(desc('avg_metric'))
                .limit(limit)
            )
            
            # Apply date filters if provided
            conditions = []
            if start_date:
                conditions.append(PerformanceMetrics.metric_date >= start_date)
            if end_date:
                conditions.append(PerformanceMetrics.metric_date <= end_date)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            result = await self.db.execute(query)
            rows = result.all()
            
            # Get service details for each result
            service_ids = [row.service_id for row in rows]
            if service_ids:
                services_result = await self.db.execute(
                    Select(Service)
                    .where(Service.id.in_(service_ids))
                    .options(joinedload(Service.vendor))
                )
                services = {s.id: s for s in services_result.scalars().all()}
            else:
                services = {}
            
            return [
                {
                    "service_id": row.service_id,
                    "service": services.get(row.service_id),
                    "metric_value": float(row.avg_metric),
                    "total_views": row.total_views,
                    "total_revenue": float(row.total_revenue)
                }
                for row in rows
            ]
            
        except Exception as e:
            logger.error(f"Failed to get top performing services by {metric}: {str(e)}")
            raise RepositoryError(f"Failed to get top performing services", e)

    async def get_performance_trends(
        self,
        service_id: int,
        days: int = 30
    ) -> Dict[str, List[Tuple[date, float]]]:
        """
        Get performance trends for key metrics over time.
        
        Args:
            service_id: Service ID to analyze
            days: Number of days to look back
            
        Returns:
            Dictionary of metric trends with (date, value) tuples
        """
        try:
            cutoff_date = datetime.now(timezone.utc).date() - timedelta(days=days)
            
            result = await self.db.execute(
                Select(
                    PerformanceMetrics.metric_date,
                    PerformanceMetrics.listing_views,
                    PerformanceMetrics.conversion_rate,
                    PerformanceMetrics.engagement_rate,
                    PerformanceMetrics.revenue_generated
                )
                .where(
                    and_(
                        PerformanceMetrics.service_id == service_id,
                        PerformanceMetrics.metric_date >= cutoff_date
                    )
                )
                .order_by(asc(PerformanceMetrics.metric_date))
            )
            rows = result.all()
            
            return {
                "views": [(row.metric_date, float(row.listing_views)) for row in rows],
                "conversion_rate": [(row.metric_date, float(row.conversion_rate)) for row in rows],
                "engagement_rate": [(row.metric_date, float(row.engagement_rate)) for row in rows],
                "revenue": [(row.metric_date, float(row.revenue_generated)) for row in rows]
            }
            
        except Exception as e:
            logger.error(f"Failed to get performance trends for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get performance trends", e)

    async def get_comparative_metrics(
        self,
        service_id: int,
        category_id: Optional[int] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get comparative performance metrics against category or market averages.
        
        Args:
            service_id: Service ID to compare
            category_id: Optional category for comparison
            days: Number of days to analyze
            
        Returns:
            Comparative metrics with percentiles and rankings
        """
        try:
            cutoff_date = datetime.now(timezone.utc).date() - timedelta(days=days)
            
            # Get service metrics
            service_metrics = await self.get_aggregated_metrics(
                service_id, cutoff_date, datetime.now(timezone.utc).date()
            )
            
            # Build comparison query
            query = Select(
                func.avg(PerformanceMetrics.conversion_rate).label('avg_conversion'),
                func.avg(PerformanceMetrics.engagement_rate).label('avg_engagement'),
                func.avg(PerformanceMetrics.bounce_rate).label('avg_bounce'),
                func.percentile_cont(0.5).within_group(PerformanceMetrics.conversion_rate).label('median_conversion'),
                func.percentile_cont(0.75).within_group(PerformanceMetrics.conversion_rate).label('p75_conversion'),
                func.percentile_cont(0.90).within_group(PerformanceMetrics.conversion_rate).label('p90_conversion')
            ).where(PerformanceMetrics.metric_date >= cutoff_date)
            
            if category_id:
                query = query.join(Service, PerformanceMetrics.service_id == Service.id)
                query = query.where(Service.category_id == category_id)
            
            result = await self.db.execute(query)
            comparison = result.first()
            
            service_conversion = service_metrics["averages"]["conversion_rate"]
            
            return {
                "service_metrics": service_metrics,
                "market_averages": {
                    "conversion_rate": float(comparison.avg_conversion or 0),
                    "engagement_rate": float(comparison.avg_engagement or 0),
                    "bounce_rate": float(comparison.avg_bounce or 0)
                },
                "percentiles": {
                    "median_conversion": float(comparison.median_conversion or 0),
                    "p75_conversion": float(comparison.p75_conversion or 0),
                    "p90_conversion": float(comparison.p90_conversion or 0)
                },
                "performance_vs_market": {
                    "conversion_rate_vs_avg": service_conversion - float(comparison.avg_conversion or 0),
                    "percentile_rank": self._calculate_percentile_rank(
                        service_conversion,
                        float(comparison.median_conversion or 0),
                        float(comparison.p75_conversion or 0),
                        float(comparison.p90_conversion or 0)
                    )
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get comparative metrics for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get comparative metrics", e)

    def _calculate_percentile_rank(
        self,
        value: float,
        median: float,
        p75: float,
        p90: float
    ) -> str:
        """Calculate percentile rank for a value."""
        if value >= p90:
            return "top_10_percent"
        elif value >= p75:
            return "top_25_percent"
        elif value >= median:
            return "above_median"
        else:
            return "below_median"
