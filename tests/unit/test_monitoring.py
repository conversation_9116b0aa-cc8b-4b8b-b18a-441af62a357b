"""
Unit tests for monitoring infrastructure.

This module tests the comprehensive monitoring system including:
- Health check endpoints and functionality
- Performance metrics collection and export
- Sentry integration for error tracking
- System resource monitoring
- Custom business metrics
- Prometheus-compatible metrics export
"""

import asyncio
import time
from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from fastapi.testclient import TestClient

from app.core.monitoring import (
    HealthCheckResult,
    SystemMetrics,
    BusinessMetrics,
    MetricsCollector,
    HealthChecker,
    setup_sentry,
    create_health_router,
    metrics_collector,
    health_checker
)


class TestHealthCheckResult:
    """Test cases for HealthCheckResult data structure."""

    def test_health_check_result_creation(self):
        """Test creating HealthCheckResult instance."""
        timestamp = datetime.utcnow()
        result = HealthCheckResult(
            service="database",
            status="healthy",
            response_time=0.123,
            details={"connection_pool": "active"},
            timestamp=timestamp
        )
        
        assert result.service == "database"
        assert result.status == "healthy"
        assert result.response_time == 0.123
        assert result.details["connection_pool"] == "active"
        assert result.timestamp == timestamp


class TestSystemMetrics:
    """Test cases for SystemMetrics data structure."""

    def test_system_metrics_creation(self):
        """Test creating SystemMetrics instance."""
        timestamp = datetime.utcnow()
        metrics = SystemMetrics(
            cpu_percent=45.2,
            memory_percent=67.8,
            memory_used_mb=1024.0,
            memory_total_mb=2048.0,
            disk_percent=23.4,
            disk_used_gb=50.0,
            disk_total_gb=100.0,
            load_average=[1.2, 1.5, 1.8],
            timestamp=timestamp
        )
        
        assert metrics.cpu_percent == 45.2
        assert metrics.memory_percent == 67.8
        assert metrics.memory_used_mb == 1024.0
        assert metrics.disk_percent == 23.4
        assert metrics.load_average == [1.2, 1.5, 1.8]
        assert metrics.timestamp == timestamp


class TestBusinessMetrics:
    """Test cases for BusinessMetrics data structure."""

    def test_business_metrics_creation(self):
        """Test creating BusinessMetrics instance."""
        timestamp = datetime.utcnow()
        metrics = BusinessMetrics(
            active_vendors=150,
            active_campaigns=25,
            total_transactions_today=500,
            total_revenue_today=12500.75,
            average_response_time=0.234,
            error_rate=2.5,
            timestamp=timestamp
        )
        
        assert metrics.active_vendors == 150
        assert metrics.active_campaigns == 25
        assert metrics.total_transactions_today == 500
        assert metrics.total_revenue_today == 12500.75
        assert metrics.average_response_time == 0.234
        assert metrics.error_rate == 2.5
        assert metrics.timestamp == timestamp


class TestMetricsCollector:
    """Test cases for MetricsCollector."""

    def test_record_request(self):
        """Test recording request metrics."""
        collector = MetricsCollector()
        
        collector.record_request(
            method="GET",
            path="/api/v1/vendors",
            status_code=200,
            response_time=0.123
        )
        
        endpoint = "GET /api/v1/vendors"
        assert collector.request_counts[endpoint] == 1
        assert len(collector.response_times[endpoint]) == 1
        assert collector.response_times[endpoint][0] == 0.123
        assert collector.error_counts[endpoint] == 0

    def test_record_request_with_error(self):
        """Test recording request metrics with error status."""
        collector = MetricsCollector()
        
        collector.record_request(
            method="POST",
            path="/api/v1/vendors",
            status_code=400,
            response_time=0.056
        )
        
        endpoint = "POST /api/v1/vendors"
        assert collector.request_counts[endpoint] == 1
        assert collector.error_counts[endpoint] == 1

    def test_record_business_event(self):
        """Test recording business event metrics."""
        collector = MetricsCollector()
        
        collector.record_business_event("vendor_registration", 1)
        collector.record_business_event("payment_completed", 3)
        
        assert collector.business_events["vendor_registration"] == 1
        assert collector.business_events["payment_completed"] == 3

    def test_get_metrics_summary(self):
        """Test getting comprehensive metrics summary."""
        collector = MetricsCollector()
        
        # Record some test data
        collector.record_request("GET", "/api/v1/vendors", 200, 0.1)
        collector.record_request("GET", "/api/v1/vendors", 200, 0.2)
        collector.record_request("POST", "/api/v1/vendors", 400, 0.05)
        collector.record_business_event("test_event", 5)
        
        summary = collector.get_metrics_summary()
        
        assert summary["total_requests"] == 3
        assert summary["total_errors"] == 1
        assert "GET /api/v1/vendors" in summary["request_counts"]
        assert summary["request_counts"]["GET /api/v1/vendors"] == 2
        assert "GET /api/v1/vendors" in summary["average_response_times"]
        assert summary["average_response_times"]["GET /api/v1/vendors"] == 0.15
        assert summary["business_events"]["test_event"] == 5

    def test_reset_metrics(self):
        """Test resetting all metrics."""
        collector = MetricsCollector()
        
        # Record some data
        collector.record_request("GET", "/test", 200, 0.1)
        collector.record_business_event("test", 1)
        
        # Reset
        collector.reset_metrics()
        
        assert len(collector.request_counts) == 0
        assert len(collector.response_times) == 0
        assert len(collector.error_counts) == 0
        assert len(collector.business_events) == 0

    def test_response_times_limit(self):
        """Test that response times are limited to 1000 entries."""
        collector = MetricsCollector()
        
        # Record more than 1000 response times
        for i in range(1200):
            collector.record_request("GET", "/test", 200, 0.1)
        
        assert len(collector.response_times["GET /test"]) == 1000


class TestHealthChecker:
    """Test cases for HealthChecker."""

    @pytest.mark.asyncio
    async def test_check_database_healthy(self):
        """Test database health check when healthy."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.get_async_session_context') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.__aenter__.return_value = mock_session_instance
            mock_session_instance.execute = AsyncMock()
            
            # Mock successful database operations
            mock_result = MagicMock()
            mock_result.scalar.return_value = 1
            mock_session_instance.execute.return_value = mock_result
            
            result = await checker.check_database()
            
            assert result.service == "database"
            assert result.status == "healthy"
            assert result.response_time > 0
            assert "connection_pool_size" in result.details

    @pytest.mark.asyncio
    async def test_check_database_unhealthy(self):
        """Test database health check when unhealthy."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.get_async_session_context') as mock_session:
            mock_session.__aenter__.side_effect = Exception("Database connection failed")
            
            result = await checker.check_database()
            
            assert result.service == "database"
            assert result.status == "unhealthy"
            assert "error" in result.details

    @pytest.mark.asyncio
    async def test_check_redis_healthy(self):
        """Test Redis health check when healthy."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.redis.from_url') as mock_redis:
            mock_client = MagicMock()
            mock_redis.return_value = mock_client
            
            # Mock successful Redis operations
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock()
                mock_loop.return_value.run_in_executor.side_effect = [
                    True,  # ping
                    None,  # set
                    "test_value",  # get
                    None,  # delete
                    {"redis_version": "6.0", "connected_clients": 5}  # info
                ]
                
                result = await checker.check_redis()
                
                assert result.service == "redis"
                assert result.status == "healthy"
                assert result.response_time > 0
                assert "version" in result.details

    @pytest.mark.asyncio
    async def test_check_redis_unhealthy(self):
        """Test Redis health check when unhealthy."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.redis.from_url') as mock_redis:
            mock_redis.side_effect = Exception("Redis connection failed")
            
            result = await checker.check_redis()
            
            assert result.service == "redis"
            assert result.status == "unhealthy"
            assert "error" in result.details

    @pytest.mark.asyncio
    async def test_check_external_services(self):
        """Test external services health check."""
        checker = HealthChecker()
        
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.ENABLE_AI_OPTIMIZATION = True
            
            with patch.object(checker, '_check_aiml_api') as mock_check_aiml:
                mock_check_aiml.return_value = HealthCheckResult(
                    service="aiml_api",
                    status="healthy",
                    response_time=0.5,
                    details={"status_code": 200},
                    timestamp=datetime.utcnow()
                )
                
                results = await checker.check_external_services()
                
                assert len(results) == 1
                assert results[0].service == "aiml_api"
                assert results[0].status == "healthy"

    def test_get_system_metrics(self):
        """Test getting system resource metrics."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.psutil') as mock_psutil:
            # Mock psutil functions
            mock_psutil.cpu_percent.return_value = 45.2
            
            mock_memory = MagicMock()
            mock_memory.percent = 67.8
            mock_memory.used = 1024 * 1024 * 1024  # 1GB
            mock_memory.total = 2048 * 1024 * 1024  # 2GB
            mock_psutil.virtual_memory.return_value = mock_memory
            
            mock_disk = MagicMock()
            mock_disk.percent = 23.4
            mock_disk.used = 50 * 1024 * 1024 * 1024  # 50GB
            mock_disk.total = 100 * 1024 * 1024 * 1024  # 100GB
            mock_psutil.disk_usage.return_value = mock_disk
            
            mock_psutil.getloadavg.return_value = (1.2, 1.5, 1.8)
            
            metrics = checker.get_system_metrics()
            
            assert metrics.cpu_percent == 45.2
            assert metrics.memory_percent == 67.8
            assert metrics.memory_used_mb == 1024.0
            assert metrics.disk_percent == 23.4
            assert metrics.load_average == [1.2, 1.5, 1.8]

    def test_get_system_metrics_error_handling(self):
        """Test system metrics error handling."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.psutil') as mock_psutil:
            mock_psutil.cpu_percent.side_effect = Exception("CPU monitoring failed")
            
            metrics = checker.get_system_metrics()
            
            # Should return default metrics on error
            assert metrics.cpu_percent == 0.0
            assert metrics.memory_percent == 0.0

    @pytest.mark.asyncio
    async def test_get_business_metrics(self):
        """Test getting business-specific metrics."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.get_async_session_context') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.__aenter__.return_value = mock_session_instance
            
            # Mock database queries
            mock_vendor_result = MagicMock()
            mock_vendor_result.scalar.return_value = 150
            
            mock_session_instance.execute.return_value = mock_vendor_result
            
            with patch('app.core.monitoring.metrics_collector') as mock_collector:
                mock_collector.get_metrics_summary.return_value = {
                    'average_response_times': {'GET /api': 0.1, 'POST /api': 0.2},
                    'total_requests': 1000,
                    'total_errors': 25
                }
                
                metrics = await checker.get_business_metrics()
                
                assert metrics.active_vendors == 150
                assert metrics.average_response_time == 0.15
                assert metrics.error_rate == 2.5

    @pytest.mark.asyncio
    async def test_get_business_metrics_error_handling(self):
        """Test business metrics error handling."""
        checker = HealthChecker()
        
        with patch('app.core.monitoring.get_async_session_context') as mock_session:
            mock_session.__aenter__.side_effect = Exception("Database error")
            
            metrics = await checker.get_business_metrics()
            
            # Should return default metrics on error
            assert metrics.active_vendors == 0
            assert metrics.active_campaigns == 0
            assert metrics.total_transactions_today == 0


class TestSetupSentry:
    """Test cases for Sentry setup."""

    def test_setup_sentry_with_config(self):
        """Test Sentry setup with valid configuration."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.get_sentry_config.return_value = {
                "dsn": "https://<EMAIL>/123",
                "environment": "test",
                "traces_sample_rate": 0.1,
                "debug": True
            }
            
            with patch('app.core.monitoring.sentry_sdk') as mock_sentry:
                setup_sentry()
                
                mock_sentry.init.assert_called_once()
                call_args = mock_sentry.init.call_args[1]
                assert call_args["dsn"] == "https://<EMAIL>/123"
                assert call_args["environment"] == "test"
                assert call_args["traces_sample_rate"] == 0.1

    def test_setup_sentry_without_config(self):
        """Test Sentry setup without configuration."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.get_sentry_config.return_value = {}
            
            with patch('app.core.monitoring.sentry_sdk') as mock_sentry:
                setup_sentry()
                
                mock_sentry.init.assert_not_called()

    def test_setup_sentry_error_handling(self):
        """Test Sentry setup error handling."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.get_sentry_config.return_value = {
                "dsn": "https://<EMAIL>/123",
                "environment": "test",
                "traces_sample_rate": 0.1,
                "debug": True
            }
            
            with patch('app.core.monitoring.sentry_sdk') as mock_sentry:
                mock_sentry.init.side_effect = Exception("Sentry init failed")
                
                # Should not raise exception
                setup_sentry()


class TestHealthRouter:
    """Test cases for health check router endpoints."""

    def test_create_health_router(self):
        """Test creating health check router."""
        router = create_health_router()
        
        assert router.prefix == "/health"
        assert "Health" in router.tags

    def test_basic_health_check_endpoint(self):
        """Test basic health check endpoint."""
        from fastapi import FastAPI
        
        app = FastAPI()
        health_router = create_health_router()
        app.include_router(health_router)
        
        client = TestClient(app)
        response = client.get("/health/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
        assert "environment" in data

    def test_detailed_health_check_endpoint(self):
        """Test detailed health check endpoint."""
        from fastapi import FastAPI
        
        app = FastAPI()
        health_router = create_health_router()
        app.include_router(health_router)
        
        with patch('app.core.monitoring.health_checker') as mock_checker:
            # Mock healthy checks
            mock_db_check = HealthCheckResult(
                service="database",
                status="healthy",
                response_time=0.1,
                details={},
                timestamp=datetime.utcnow()
            )
            mock_redis_check = HealthCheckResult(
                service="redis",
                status="healthy",
                response_time=0.05,
                details={},
                timestamp=datetime.utcnow()
            )
            
            mock_checker.check_database = AsyncMock(return_value=mock_db_check)
            mock_checker.check_redis = AsyncMock(return_value=mock_redis_check)
            mock_checker.check_external_services = AsyncMock(return_value=[])
            
            client = TestClient(app)
            response = client.get("/health/detailed")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "checks" in data
            assert "database" in data["checks"]
            assert "redis" in data["checks"]

    def test_metrics_endpoint(self):
        """Test metrics endpoint."""
        from fastapi import FastAPI
        
        app = FastAPI()
        health_router = create_health_router()
        app.include_router(health_router)
        
        with patch('app.core.monitoring.health_checker') as mock_checker:
            mock_system_metrics = SystemMetrics(
                cpu_percent=45.0,
                memory_percent=60.0,
                memory_used_mb=1024.0,
                memory_total_mb=2048.0,
                disk_percent=30.0,
                disk_used_gb=50.0,
                disk_total_gb=100.0,
                load_average=[1.0, 1.1, 1.2],
                timestamp=datetime.utcnow()
            )
            
            mock_business_metrics = BusinessMetrics(
                active_vendors=100,
                active_campaigns=20,
                total_transactions_today=500,
                total_revenue_today=10000.0,
                average_response_time=0.2,
                error_rate=1.5,
                timestamp=datetime.utcnow()
            )
            
            mock_checker.get_system_metrics.return_value = mock_system_metrics
            mock_checker.get_business_metrics = AsyncMock(return_value=mock_business_metrics)
            
            with patch('app.core.monitoring.metrics_collector') as mock_collector:
                mock_collector.get_metrics_summary.return_value = {
                    'uptime_seconds': 3600,
                    'total_requests': 1000,
                    'total_errors': 15
                }
                
                client = TestClient(app)
                response = client.get("/health/metrics")
                
                assert response.status_code == 200
                data = response.json()
                assert "system" in data
                assert "business" in data
                assert "application" in data
                assert data["system"]["cpu_percent"] == 45.0
                assert data["business"]["active_vendors"] == 100

    def test_prometheus_metrics_endpoint(self):
        """Test Prometheus metrics endpoint."""
        from fastapi import FastAPI
        
        app = FastAPI()
        health_router = create_health_router()
        app.include_router(health_router)
        
        with patch('app.core.monitoring.health_checker') as mock_checker:
            mock_system_metrics = SystemMetrics(
                cpu_percent=45.0,
                memory_percent=60.0,
                memory_used_mb=1024.0,
                memory_total_mb=2048.0,
                disk_percent=30.0,
                disk_used_gb=50.0,
                disk_total_gb=100.0,
                load_average=[1.0, 1.1, 1.2],
                timestamp=datetime.utcnow()
            )
            
            mock_business_metrics = BusinessMetrics(
                active_vendors=100,
                active_campaigns=20,
                total_transactions_today=500,
                total_revenue_today=10000.0,
                average_response_time=0.2,
                error_rate=1.5,
                timestamp=datetime.utcnow()
            )
            
            mock_checker.get_system_metrics.return_value = mock_system_metrics
            mock_checker.get_business_metrics = AsyncMock(return_value=mock_business_metrics)
            
            with patch('app.core.monitoring.metrics_collector') as mock_collector:
                mock_collector.get_metrics_summary.return_value = {
                    'uptime_seconds': 3600,
                    'total_requests': 1000,
                    'total_errors': 15
                }
                
                client = TestClient(app)
                response = client.get("/health/prometheus")
                
                assert response.status_code == 200
                content = response.content.decode()
                
                # Check Prometheus format
                assert "system_cpu_percent 45.0" in content
                assert "system_memory_percent 60.0" in content
                assert "business_active_vendors 100" in content
                assert "app_uptime_seconds 3600" in content
