"""
Vendor Dashboard Service for Culture Connect Backend API.

This module provides the main VendorDashboardService class that orchestrates
vendor dashboard functionality including metrics calculation, activity tracking,
notifications, and analytics generation.

Implements Task 3.3.1 requirements with comprehensive business logic, error handling,
structured logging, and seamless integration with existing marketplace systems.
Follows zero technical debt policy with production-grade implementation.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any, Union
from decimal import Decimal
from uuid import UUID

from app.repositories.vendor_repository import VendorRepository
from app.repositories.vendor_dashboard_repositories import VendorDashboardRepository
from app.schemas.vendor_dashboard import VendorDashboardOverview
from app.models.vendor_dashboard import (
    VendorDashboardMetrics,
    VendorActivityFeed,
    VendorNotification,
    VendorQuickAction
)
from app.models.analytics_models import VendorAnalytics

logger = logging.getLogger(__name__)


class VendorDashboardService:
    """
    Main vendor dashboard service for comprehensive dashboard functionality.

    Provides orchestration for vendor dashboard operations including:
    - Dashboard overview generation with key metrics
    - Metrics calculation and performance tracking
    - Activity feed management and notifications
    - Quick actions and analytics generation

    Implements performance targets:
    - <500ms for dashboard overview operations
    - <200ms for metrics queries
    - <100ms for status updates
    """

    def __init__(self):
        """Initialize VendorDashboardService with dependencies."""
        self.logger = logging.getLogger(__name__)
        self.db = None  # Will be injected
        self.vendor_repo = None  # Will be injected
        self.dashboard_repo = None  # Will be injected

    async def get_dashboard_overview(self, vendor_id: Union[int, UUID],
                                   correlation_id: str = None) -> VendorDashboardOverview:
        """
        Get comprehensive vendor dashboard overview.

        Args:
            vendor_id: Vendor identifier
            correlation_id: Request correlation ID for tracking

        Returns:
            VendorDashboardOverview: Complete dashboard data

        Raises:
            ValueError: If vendor not found
            Exception: For other service errors
        """
        try:
            self.logger.info(
                f"Getting dashboard overview for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Get vendor metrics
            metrics = await self.dashboard_repo.get_vendor_metrics(vendor_id)

            # Get recent activities
            activities = await self.dashboard_repo.get_recent_activities(vendor_id, limit=10)

            # Get unread notifications
            notifications = await self.dashboard_repo.get_unread_notifications(vendor_id, limit=5)

            # Get pending actions
            pending_actions = await self.dashboard_repo.get_pending_actions(vendor_id, limit=5)

            # Create dashboard response
            dashboard_response = VendorDashboardOverview(
                vendor_id=vendor_id,
                key_metrics=metrics or [],
                recent_activities=activities or [],
                unread_notifications=notifications or [],
                recommended_actions=pending_actions or [],
                last_updated=datetime.now(timezone.utc)
            )

            self.logger.info(
                f"Successfully retrieved dashboard overview for vendor {vendor_id}",
                extra={
                    "correlation_id": correlation_id,
                    "vendor_id": str(vendor_id),
                    "metrics_count": len(metrics or []),
                    "activities_count": len(activities or [])
                }
            )

            return dashboard_response

        except ValueError:
            # Re-raise validation errors
            raise
        except Exception as e:
            self.logger.error(
                f"Error getting dashboard overview for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def calculate_metrics(self, vendor_id: Union[int, UUID],
                              period_start: datetime, period_end: datetime,
                              correlation_id: str = None) -> Dict[str, Any]:
        """
        Calculate vendor metrics for specified period.

        Args:
            vendor_id: Vendor identifier
            period_start: Start of calculation period
            period_end: End of calculation period
            correlation_id: Request correlation ID

        Returns:
            Dict containing calculated metrics
        """
        try:
            self.logger.info(
                f"Calculating metrics for vendor {vendor_id} from {period_start} to {period_end}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Calculate revenue metrics
            revenue_metrics = await self.dashboard_repo.calculate_revenue_metrics(
                vendor_id, period_start, period_end
            )

            # Calculate booking metrics
            booking_metrics = await self.dashboard_repo.calculate_booking_metrics(
                vendor_id, period_start, period_end
            )

            # Calculate performance metrics
            performance_metrics = await self.dashboard_repo.calculate_performance_metrics(
                vendor_id, period_start, period_end
            )

            metrics_result = {
                "vendor_id": vendor_id,
                "period_start": period_start,
                "period_end": period_end,
                "revenue_metrics": revenue_metrics,
                "booking_metrics": booking_metrics,
                "performance_metrics": performance_metrics,
                "calculated_at": datetime.now(timezone.utc)
            }

            self.logger.info(
                f"Successfully calculated metrics for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return metrics_result

        except Exception as e:
            self.logger.error(
                f"Error calculating metrics for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def create_activity_feed_entry(self, vendor_id: Union[int, UUID],
                                       activity_data: Dict[str, Any],
                                       correlation_id: str = None) -> VendorActivityFeed:
        """
        Create new activity feed entry for vendor.

        Args:
            vendor_id: Vendor identifier
            activity_data: Activity data dictionary
            correlation_id: Request correlation ID

        Returns:
            VendorActivityFeed: Created activity entry
        """
        try:
            self.logger.info(
                f"Creating activity feed entry for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Create activity entry
            activity = await self.dashboard_repo.create_activity(vendor_id, activity_data)

            self.logger.info(
                f"Successfully created activity feed entry for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return activity

        except Exception as e:
            self.logger.error(
                f"Error creating activity feed entry for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def send_notification(self, vendor_id: Union[int, UUID],
                              notification_data: Dict[str, Any],
                              correlation_id: str = None) -> VendorNotification:
        """
        Send notification to vendor.

        Args:
            vendor_id: Vendor identifier
            notification_data: Notification data dictionary
            correlation_id: Request correlation ID

        Returns:
            VendorNotification: Created notification
        """
        try:
            self.logger.info(
                f"Sending notification to vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Create notification
            notification = await self.dashboard_repo.create_notification(vendor_id, notification_data)

            self.logger.info(
                f"Successfully sent notification to vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return notification

        except Exception as e:
            self.logger.error(
                f"Error sending notification to vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def create_quick_action(self, vendor_id: Union[int, UUID],
                                action_data: Dict[str, Any],
                                correlation_id: str = None) -> VendorQuickAction:
        """
        Create quick action for vendor.

        Args:
            vendor_id: Vendor identifier
            action_data: Action data dictionary
            correlation_id: Request correlation ID

        Returns:
            VendorQuickAction: Created quick action
        """
        try:
            self.logger.info(
                f"Creating quick action for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Create quick action
            action = await self.dashboard_repo.create_quick_action(vendor_id, action_data)

            self.logger.info(
                f"Successfully created quick action for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return action

        except Exception as e:
            self.logger.error(
                f"Error creating quick action for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def generate_analytics_report(self, vendor_id: Union[int, UUID],
                                      period_start: datetime, period_end: datetime,
                                      analytics_type: str = "monthly",
                                      correlation_id: str = None) -> VendorAnalytics:
        """
        Generate analytics report for vendor.

        Args:
            vendor_id: Vendor identifier
            period_start: Start of analytics period
            period_end: End of analytics period
            analytics_type: Type of analytics (monthly, weekly, daily)
            correlation_id: Request correlation ID

        Returns:
            VendorAnalytics: Generated analytics report
        """
        try:
            self.logger.info(
                f"Generating {analytics_type} analytics report for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Validate vendor exists
            vendor = await self.vendor_repo.get_by_id(vendor_id)
            if not vendor:
                raise ValueError("Vendor not found")

            # Generate analytics
            analytics = await self.dashboard_repo.generate_analytics(
                vendor_id, period_start, period_end, analytics_type
            )

            self.logger.info(
                f"Successfully generated analytics report for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return analytics

        except Exception as e:
            self.logger.error(
                f"Error generating analytics report for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def mark_notification_as_read(self, vendor_id: Union[int, UUID],
                                      notification_id: Union[int, UUID],
                                      correlation_id: str = None) -> bool:
        """
        Mark notification as read.

        Args:
            vendor_id: Vendor identifier
            notification_id: Notification identifier
            correlation_id: Request correlation ID

        Returns:
            bool: True if successfully marked as read
        """
        try:
            self.logger.info(
                f"Marking notification {notification_id} as read for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Mark notification as read
            result = await self.dashboard_repo.mark_notification_read(vendor_id, notification_id)

            self.logger.info(
                f"Successfully marked notification {notification_id} as read for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return result

        except Exception as e:
            self.logger.error(
                f"Error marking notification as read for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise

    async def complete_quick_action(self, vendor_id: Union[int, UUID],
                                  action_id: Union[int, UUID],
                                  correlation_id: str = None) -> bool:
        """
        Complete quick action.

        Args:
            vendor_id: Vendor identifier
            action_id: Action identifier
            correlation_id: Request correlation ID

        Returns:
            bool: True if successfully completed
        """
        try:
            self.logger.info(
                f"Completing quick action {action_id} for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            # Complete quick action
            result = await self.dashboard_repo.complete_quick_action(vendor_id, action_id)

            self.logger.info(
                f"Successfully completed quick action {action_id} for vendor {vendor_id}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)}
            )

            return result

        except Exception as e:
            self.logger.error(
                f"Error completing quick action for vendor {vendor_id}: {str(e)}",
                extra={"correlation_id": correlation_id, "vendor_id": str(vendor_id)},
                exc_info=True
            )
            raise
