"""
Unit tests for SEOOptimizationService.

This module provides comprehensive unit tests for the SEO optimization service
implementing Task 3.2.2 Phase 5 requirements with >80% test coverage.

Test Coverage:
- SEO analysis and scoring
- Keyword analysis and recommendations
- Content optimization suggestions
- Meta tag optimization
- Error handling and edge cases
- Service method validation
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, date
from decimal import Decimal
from typing import List, Dict, Any

from app.services.seo_optimization_service import SEOOptimizationService
from app.schemas.marketplace_optimization import SEOAnalysisResponse


class TestableSEOOptimizationService(SEOOptimizationService):
    """Testable version of SEOOptimizationService with implemented abstract methods."""

    async def validate_create_data(self, data: dict) -> dict:
        """Mock implementation of abstract method."""
        return data

    async def validate_update_data(self, data: dict, record_id: int = None) -> dict:
        """Mock implementation of abstract method."""
        return data


class TestSEOOptimizationService:
    """Test suite for SEOOptimizationService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_db_session):
        """Create SEOOptimizationService instance with mocked dependencies."""
        return TestableSEOOptimizationService(mock_db_session)

    @pytest.fixture
    def sample_service_data(self):
        """Sample service data for testing."""
        return MagicMock(
            id=1,
            title="Cultural Heritage Tour in Lagos",
            description="Experience authentic Nigerian culture with our guided heritage tour through historic Lagos. Discover traditional markets, ancient architecture, and local customs.",
            vendor_id=1,
            category_id=1,
            media_urls=["image1.jpg", "image2.jpg"],
            tags=["cultural", "heritage", "tour", "lagos", "nigeria"]
        )

    @pytest.fixture
    def sample_seo_analysis(self):
        """Sample SEO analysis response."""
        return SEOAnalysisResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            overall_seo_score=Decimal("75.50"),
            title_score=Decimal("80.00"),
            description_score=Decimal("70.00"),
            media_score=Decimal("85.00"),
            keyword_score=Decimal("65.00"),
            completeness_score=Decimal("90.00"),
            primary_keywords=["cultural tour", "heritage experience", "lagos"],
            keyword_density={"cultural": 2.5, "tour": 1.8, "heritage": 1.2},
            missing_keywords=["authentic", "traditional", "nigerian"],
            content_quality_metrics={"word_count": 250, "sentence_length": 15, "readability": 78},
            readability_score=Decimal("78.00"),
            meta_title_analysis={"length": 55, "keyword_present": True, "optimal_length": True},
            meta_description_analysis={"length": 145, "call_to_action": True, "keyword_present": True},
            analysis_date=datetime.now(),
            analysis_version="1.0"
        )

    @pytest.mark.asyncio
    async def test_analyze_service_seo_success(self, service, sample_service_data):
        """Test successful SEO analysis for a service."""
        service_id = 1
        force_refresh = False

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_seo_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data

            # Mock no existing analysis (force new analysis)
            mock_seo_repo.get_latest_by_service.return_value = None

            # Execute method
            result = await service.analyze_service_seo(service_id, force_refresh)

            # Assertions
            assert isinstance(result, SEOAnalysisResponse)
            assert result.service_id == service_id
            assert result.vendor_id == sample_service_data.vendor_id
            assert 0 <= result.overall_seo_score <= 100
            assert isinstance(result.primary_keywords, list)
            assert isinstance(result.keyword_density, dict)
            assert isinstance(result.missing_keywords, list)

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)
            mock_seo_repo.get_latest_by_service.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_analyze_service_seo_existing_analysis_no_refresh(self, service, sample_seo_analysis):
        """Test SEO analysis returns existing analysis when not forcing refresh."""
        service_id = 1
        force_refresh = False

        with patch.object(service, 'repository') as mock_seo_repo:
            # Mock existing recent analysis
            mock_seo_repo.get_latest_by_service.return_value = sample_seo_analysis

            # Execute method
            result = await service.analyze_service_seo(service_id, force_refresh)

            # Assertions
            assert result == sample_seo_analysis
            mock_seo_repo.get_latest_by_service.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_analyze_service_seo_force_refresh(self, service, sample_service_data, sample_seo_analysis):
        """Test SEO analysis with force refresh creates new analysis."""
        service_id = 1
        force_refresh = True

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_seo_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data

            # Mock existing analysis (should be ignored due to force_refresh)
            mock_seo_repo.get_latest_by_service.return_value = sample_seo_analysis

            # Execute method
            result = await service.analyze_service_seo(service_id, force_refresh)

            # Assertions
            assert isinstance(result, SEOAnalysisResponse)
            assert result.service_id == service_id
            # Should create new analysis, not return existing one
            assert result.analysis_date != sample_seo_analysis.analysis_date

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_analyze_service_seo_service_not_found(self, service):
        """Test SEO analysis when service not found."""
        service_id = 999

        with patch.object(service, 'service_repository') as mock_service_repo:
            mock_service_repo.get_by_id.return_value = None

            with pytest.raises(Exception) as exc_info:
                await service.analyze_service_seo(service_id, False)

            assert "not found" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_get_keyword_recommendations_success(self, service, sample_service_data):
        """Test successful keyword recommendations generation."""
        service_id = 1
        target_keywords = ["cultural", "heritage", "authentic"]

        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data

            # Execute method
            result = await service.get_keyword_recommendations(service_id, target_keywords)

            # Assertions
            assert isinstance(result, dict)
            assert "current_keywords" in result
            assert "recommendations" in result
            assert "keyword_gaps" in result
            assert "optimization_opportunities" in result

            # Verify current keywords analysis
            current_keywords = result["current_keywords"]
            assert "extracted_keywords" in current_keywords
            assert "keyword_density" in current_keywords
            assert "total_keywords" in current_keywords

            # Verify recommendations
            recommendations = result["recommendations"]
            assert "suggested_keywords" in recommendations
            assert "priority_keywords" in recommendations
            assert "long_tail_opportunities" in recommendations

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_get_keyword_recommendations_no_target_keywords(self, service, sample_service_data):
        """Test keyword recommendations without target keywords."""
        service_id = 1
        target_keywords = None

        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data

            # Execute method
            result = await service.get_keyword_recommendations(service_id, target_keywords)

            # Assertions
            assert isinstance(result, dict)
            assert "current_keywords" in result
            assert "recommendations" in result
            # Should still provide general recommendations
            assert len(result["recommendations"]["suggested_keywords"]) > 0

    @pytest.mark.asyncio
    async def test_get_content_optimization_suggestions_success(self, service, sample_service_data):
        """Test successful content optimization suggestions generation."""
        service_id = 1

        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data

            # Execute method
            result = await service.get_content_optimization_suggestions(service_id)

            # Assertions
            assert isinstance(result, dict)
            assert "content_analysis" in result
            assert "optimization_suggestions" in result
            assert "implementation_order" in result
            assert "expected_impact" in result

            # Verify content analysis
            content_analysis = result["content_analysis"]
            assert "title_analysis" in content_analysis
            assert "description_analysis" in content_analysis
            assert "content_structure" in content_analysis
            assert "readability_metrics" in content_analysis

            # Verify optimization suggestions
            optimization_suggestions = result["optimization_suggestions"]
            assert "title_optimization" in optimization_suggestions
            assert "description_optimization" in optimization_suggestions
            assert "keyword_optimization" in optimization_suggestions
            assert "media_optimization" in optimization_suggestions

            # Verify implementation order
            implementation_order = result["implementation_order"]
            assert isinstance(implementation_order, list)
            assert len(implementation_order) > 0

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_calculate_seo_score_comprehensive(self, service):
        """Test comprehensive SEO score calculation."""
        content_data = {
            "title": "Cultural Heritage Tour in Lagos - Authentic Nigerian Experience",
            "description": "Experience authentic Nigerian culture with our guided heritage tour through historic Lagos. Discover traditional markets, ancient architecture, and local customs with expert guides.",
            "media_count": 5,
            "tags": ["cultural", "heritage", "tour", "lagos", "nigeria", "authentic"],
            "completeness": 0.9
        }

        # Execute method (assuming this is a testable method)
        scores = service._calculate_seo_scores(content_data)

        # Assertions
        assert isinstance(scores, dict)
        assert "title_score" in scores
        assert "description_score" in scores
        assert "media_score" in scores
        assert "keyword_score" in scores
        assert "completeness_score" in scores
        assert "overall_seo_score" in scores

        # Verify score ranges
        for score_key, score_value in scores.items():
            assert 0 <= score_value <= 100

    @pytest.mark.asyncio
    async def test_extract_keywords_from_content(self, service):
        """Test keyword extraction from content."""
        content = "Experience authentic Nigerian cultural heritage tour through historic Lagos markets and traditional architecture"

        # Execute method (assuming this is a testable method)
        keywords = service._extract_keywords(content)

        # Assertions
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        # Should extract meaningful keywords
        expected_keywords = ["nigerian", "cultural", "heritage", "tour", "lagos", "markets", "traditional", "architecture"]
        for keyword in expected_keywords:
            assert any(keyword.lower() in extracted.lower() for extracted in keywords)

    @pytest.mark.asyncio
    async def test_calculate_keyword_density(self, service):
        """Test keyword density calculation."""
        content = "Cultural heritage tour in Lagos. This cultural tour showcases heritage sites and cultural traditions."
        keywords = ["cultural", "heritage", "tour", "lagos"]

        # Execute method (assuming this is a testable method)
        density = service._calculate_keyword_density(content, keywords)

        # Assertions
        assert isinstance(density, dict)
        assert "cultural" in density
        assert "heritage" in density
        assert "tour" in density
        assert "lagos" in density

        # Verify density calculations
        assert density["cultural"] > 0  # Should appear multiple times
        assert density["heritage"] > 0
        assert density["tour"] > 0
        assert density["lagos"] > 0

    @pytest.mark.asyncio
    async def test_analyze_title_optimization(self, service):
        """Test title optimization analysis."""
        title = "Cultural Heritage Tour in Lagos"

        # Execute method (assuming this is a testable method)
        analysis = service._analyze_title(title)

        # Assertions
        assert isinstance(analysis, dict)
        assert "length" in analysis
        assert "keyword_present" in analysis
        assert "optimal_length" in analysis
        assert "score" in analysis
        assert "recommendations" in analysis

        # Verify analysis results
        assert analysis["length"] == len(title)
        assert isinstance(analysis["keyword_present"], bool)
        assert isinstance(analysis["optimal_length"], bool)
        assert 0 <= analysis["score"] <= 100

    @pytest.mark.asyncio
    async def test_analyze_description_optimization(self, service):
        """Test description optimization analysis."""
        description = "Experience authentic Nigerian culture with our guided heritage tour through historic Lagos. Discover traditional markets, ancient architecture, and local customs."

        # Execute method (assuming this is a testable method)
        analysis = service._analyze_description(description)

        # Assertions
        assert isinstance(analysis, dict)
        assert "length" in analysis
        assert "keyword_present" in analysis
        assert "call_to_action" in analysis
        assert "readability" in analysis
        assert "score" in analysis
        assert "recommendations" in analysis

        # Verify analysis results
        assert analysis["length"] == len(description)
        assert isinstance(analysis["keyword_present"], bool)
        assert isinstance(analysis["call_to_action"], bool)
        assert 0 <= analysis["score"] <= 100

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db_session):
        """Test service initialization with dependencies."""
        service = TestableSEOOptimizationService(mock_db_session)

        # Verify service is properly initialized
        assert service._db_session == mock_db_session
        assert hasattr(service, 'service_repository')
        assert hasattr(service, 'repository')

    @pytest.mark.asyncio
    async def test_error_handling_database_error(self, service):
        """Test error handling for database errors."""
        service_id = 1

        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock database error
            mock_service_repo.get_by_id.side_effect = Exception("Database connection error")

            with pytest.raises(Exception) as exc_info:
                await service.analyze_service_seo(service_id, False)

            assert "Database connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_seo_analysis_with_minimal_content(self, service):
        """Test SEO analysis with minimal content."""
        minimal_service = MagicMock(
            id=1,
            title="Tour",
            description="Short description",
            vendor_id=1,
            category_id=1,
            media_urls=[],
            tags=[]
        )

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_seo_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = minimal_service
            mock_seo_repo.get_latest_by_service.return_value = None

            # Execute method
            result = await service.analyze_service_seo(1, False)

            # Assertions
            assert isinstance(result, SEOAnalysisResponse)
            # Should handle minimal content gracefully
            assert result.overall_seo_score < 50  # Should score low due to minimal content
            assert len(result.missing_keywords) > 0  # Should identify missing keywords
