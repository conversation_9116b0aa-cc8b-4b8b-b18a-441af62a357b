"""
Task management service for Culture Connect Backend API.

This module provides comprehensive task management services including:
- Task submission and execution management
- Queue monitoring and administration
- Task scheduling and periodic job management
- Performance metrics and analytics
- Error handling and retry management

Implements Task 6.2.1 requirements for Celery task queue setup with
production-grade task orchestration and monitoring.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Union
from uuid import UUID, uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from celery.result import AsyncResult

from app.core.celery_config import celery_app, check_celery_health
from app.core.logging import correlation_id
from app.models.task_models import (
    TaskExecution, TaskQueue, TaskSchedule, TaskMetrics, TaskFailure,
    TaskStatus, TaskPriority, QueueType
)
from app.schemas.task_schemas import (
    TaskSubmissionRequest, TaskSubmissionResponse, TaskStatusResponse,
    TaskCancellationRequest, TaskCancellationResponse, QueueStatusResponse,
    TaskScheduleRequest, TaskScheduleResponse, TaskMetricsResponse,
    SystemMetricsResponse, BulkTaskSubmissionRequest, BulkTaskSubmissionResponse
)
from app.services.base import BaseService

logger = logging.getLogger(__name__)


class TaskService:
    """Service for task management operations."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.celery_app = celery_app

    async def submit_task(
        self,
        request: TaskSubmissionRequest,
        user_id: Optional[UUID] = None
    ) -> TaskSubmissionResponse:
        """
        Submit a task for background execution.

        Args:
            request: Task submission request
            user_id: User submitting the task

        Returns:
            Task submission response
        """
        try:
            start_time = datetime.now(timezone.utc)

            # Determine queue if not specified
            queue_name = request.queue_name or self._get_default_queue(request.priority)

            # Submit task to Celery
            celery_task = self.celery_app.send_task(
                request.task_name,
                args=request.args or [],
                kwargs=request.kwargs or {},
                queue=queue_name,
                eta=request.eta,
                countdown=request.countdown,
                retry=True,
                retry_policy={
                    'max_retries': request.max_retries,
                    'interval_start': 0,
                    'interval_step': 0.2,
                    'interval_max': 2.0,
                }
            )

            # Create task execution record
            task_execution = TaskExecution(
                task_id=celery_task.id,
                task_name=request.task_name,
                queue_name=queue_name,
                priority=request.priority.value,
                status=TaskStatus.PENDING.value,
                args=request.args,
                kwargs=request.kwargs,
                eta=request.eta,
                max_retries=request.max_retries,
                submitted_at=start_time
            )

            self.session.add(task_execution)
            await self.session.commit()

            # Log task submission
            logger.info(f"Task submitted: {request.task_name}", extra={
                "task_id": celery_task.id,
                "task_name": request.task_name,
                "queue": queue_name,
                "priority": request.priority.value,
                "user_id": str(user_id) if user_id else None,
                "correlation_id": correlation_id.get()
            })

            return TaskSubmissionResponse(
                id=task_execution.id,
                task_id=celery_task.id,
                task_name=request.task_name,
                queue_name=queue_name,
                priority=request.priority,
                status=TaskStatus.PENDING,
                eta=request.eta,
                submitted_at=start_time,
                created_at=start_time,
                updated_at=start_time
            )

        except Exception as e:
            logger.error(f"Failed to submit task {request.task_name}: {str(e)}", extra={
                "task_name": request.task_name,
                "user_id": str(user_id) if user_id else None,
                "correlation_id": correlation_id.get()
            })
            raise

    async def get_task_status(
        self,
        task_id: str,
        include_result: bool = False,
        include_traceback: bool = False
    ) -> TaskStatusResponse:
        """
        Get task execution status.

        Args:
            task_id: Task identifier
            include_result: Include task result in response
            include_traceback: Include error traceback if failed

        Returns:
            Task status response
        """
        try:
            # Get task execution record
            stmt = select(TaskExecution).where(TaskExecution.task_id == task_id)
            result = await self.session.execute(stmt)
            task_execution = result.scalar_one_or_none()

            if not task_execution:
                raise ValueError(f"Task not found: {task_id}")

            # Get Celery task result
            celery_result = AsyncResult(task_id, app=self.celery_app)

            # Update task execution status if needed
            if celery_result.status != task_execution.status:
                task_execution.status = celery_result.status
                if celery_result.status == TaskStatus.SUCCESS.value:
                    task_execution.completed_at = datetime.now(timezone.utc)
                elif celery_result.status == TaskStatus.FAILURE.value:
                    task_execution.error_message = str(celery_result.result)
                    task_execution.error_traceback = celery_result.traceback

                await self.session.commit()

            # Prepare response
            response_data = {
                "task_id": task_id,
                "task_name": task_execution.task_name,
                "status": TaskStatus(task_execution.status),
                "submitted_at": task_execution.submitted_at,
                "started_at": task_execution.started_at,
                "completed_at": task_execution.completed_at,
                "execution_time_ms": task_execution.execution_time_ms,
                "retry_count": task_execution.retry_count,
                "worker_name": task_execution.worker_name
            }

            # Add result if requested and available
            if include_result and celery_result.successful():
                response_data["result"] = celery_result.result

            # Add error details if requested and failed
            if include_traceback and celery_result.failed():
                response_data["error_message"] = task_execution.error_message
                response_data["error_traceback"] = task_execution.error_traceback

            return TaskStatusResponse(**response_data)

        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {str(e)}", extra={
                "task_id": task_id,
                "correlation_id": correlation_id.get()
            })
            raise

    async def cancel_task(
        self,
        request: TaskCancellationRequest,
        user_id: Optional[UUID] = None
    ) -> TaskCancellationResponse:
        """
        Cancel a running or pending task.

        Args:
            request: Task cancellation request
            user_id: User cancelling the task

        Returns:
            Task cancellation response
        """
        try:
            # Revoke task in Celery
            self.celery_app.control.revoke(
                request.task_id,
                terminate=request.terminate,
                signal='SIGKILL' if request.terminate else 'SIGTERM'
            )

            # Update task execution record
            stmt = select(TaskExecution).where(TaskExecution.task_id == request.task_id)
            result = await self.session.execute(stmt)
            task_execution = result.scalar_one_or_none()

            if task_execution:
                task_execution.status = TaskStatus.REVOKED.value
                task_execution.completed_at = datetime.now(timezone.utc)
                await self.session.commit()

            logger.info(f"Task cancelled: {request.task_id}", extra={
                "task_id": request.task_id,
                "terminate": request.terminate,
                "reason": request.reason,
                "user_id": str(user_id) if user_id else None,
                "correlation_id": correlation_id.get()
            })

            return TaskCancellationResponse(
                task_id=request.task_id,
                status="cancelled",
                message=f"Task cancelled successfully. Reason: {request.reason or 'No reason provided'}",
                cancelled_at=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Failed to cancel task {request.task_id}: {str(e)}", extra={
                "task_id": request.task_id,
                "correlation_id": correlation_id.get()
            })
            raise

    async def submit_bulk_tasks(
        self,
        request: BulkTaskSubmissionRequest,
        user_id: Optional[UUID] = None
    ) -> BulkTaskSubmissionResponse:
        """
        Submit multiple tasks in bulk.

        Args:
            request: Bulk task submission request
            user_id: User submitting the tasks

        Returns:
            Bulk task submission response
        """
        try:
            batch_id = uuid4()
            task_ids = []
            errors = []
            successful = 0
            failed = 0

            for i, task_request in enumerate(request.tasks):
                try:
                    response = await self.submit_task(task_request, user_id)
                    task_ids.append(response.task_id)
                    successful += 1

                except Exception as e:
                    errors.append({
                        "index": i,
                        "task_name": task_request.task_name,
                        "error": str(e)
                    })
                    failed += 1

                    if request.fail_fast:
                        break

            logger.info(f"Bulk task submission completed", extra={
                "batch_id": str(batch_id),
                "total_tasks": len(request.tasks),
                "successful": successful,
                "failed": failed,
                "user_id": str(user_id) if user_id else None,
                "correlation_id": correlation_id.get()
            })

            return BulkTaskSubmissionResponse(
                batch_id=batch_id,
                total_tasks=len(request.tasks),
                successful_submissions=successful,
                failed_submissions=failed,
                task_ids=task_ids,
                errors=errors,
                submitted_at=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Failed to submit bulk tasks: {str(e)}", extra={
                "batch_name": request.batch_name,
                "task_count": len(request.tasks),
                "correlation_id": correlation_id.get()
            })
            raise

    async def get_system_metrics(self) -> SystemMetricsResponse:
        """
        Get system-wide task metrics.

        Returns:
            System metrics response
        """
        try:
            # Get Celery health status
            health_status = check_celery_health()

            # Calculate time periods
            now = datetime.now(timezone.utc)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

            # Get task statistics for today
            today_stats = await self.session.execute(
                select(
                    func.count(TaskExecution.id).label('total'),
                    func.count(TaskExecution.id).filter(
                        TaskExecution.status == TaskStatus.SUCCESS.value
                    ).label('successful'),
                    func.count(TaskExecution.id).filter(
                        TaskExecution.status == TaskStatus.FAILURE.value
                    ).label('failed'),
                    func.avg(TaskExecution.execution_time_ms).label('avg_execution_time')
                ).where(TaskExecution.submitted_at >= today_start)
            )
            stats = today_stats.first()

            # Calculate success rate
            total_today = stats.total or 0
            successful_today = stats.successful or 0
            failed_today = stats.failed or 0
            success_rate = (successful_today / max(total_today, 1)) * 100

            # Get queue count
            queue_count = await self.session.execute(
                select(func.count(TaskQueue.id))
            )
            total_queues = queue_count.scalar() or 0

            # Calculate health score
            health_score = self._calculate_health_score(
                success_rate=success_rate,
                broker_connected=health_status.get("broker_connected", False),
                active_workers=health_status.get("active_workers", 0)
            )

            return SystemMetricsResponse(
                total_queues=total_queues,
                active_queues=health_status.get("active_workers", 0),
                total_workers=health_status.get("active_workers", 0),
                active_workers=health_status.get("active_workers", 0),
                total_tasks_today=total_today,
                successful_tasks_today=successful_today,
                failed_tasks_today=failed_today,
                overall_success_rate=success_rate,
                average_execution_time_ms=float(stats.avg_execution_time or 0),
                peak_throughput_per_minute=0.0,  # Would be calculated from metrics
                total_memory_usage_mb=0.0,  # Would be collected from workers
                cpu_utilization_percent=0.0,  # Would be collected from workers
                health_score=health_score,
                uptime_hours=0.0,  # Would be calculated from system start
                recorded_at=now
            )

        except Exception as e:
            logger.error(f"Failed to get system metrics: {str(e)}", extra={
                "correlation_id": correlation_id.get()
            })
            raise

    def _get_default_queue(self, priority: TaskPriority) -> str:
        """Get default queue name based on priority."""
        queue_map = {
            TaskPriority.CRITICAL: "critical",
            TaskPriority.HIGH: "high_priority",
            TaskPriority.STANDARD: "default",
            TaskPriority.LOW: "low_priority"
        }
        return queue_map.get(priority, "default")

    def _calculate_health_score(
        self,
        success_rate: float,
        broker_connected: bool,
        active_workers: int
    ) -> float:
        """Calculate system health score."""
        score = 0.0

        # Success rate component (40%)
        score += (success_rate / 100) * 40

        # Broker connection component (30%)
        if broker_connected:
            score += 30

        # Worker availability component (30%)
        if active_workers > 0:
            score += min(active_workers * 5, 30)

        return min(score, 100.0)
