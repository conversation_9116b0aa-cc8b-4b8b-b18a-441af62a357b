"""
Marketplace Optimization API endpoints for Culture Connect Backend API.

This module provides comprehensive marketplace optimization endpoints including:
- Marketplace optimization dashboard with cross-component insights
- SEO analysis and optimization recommendations
- Performance analytics with trend analysis and forecasting
- Competitive analysis with market intelligence and positioning
- Mobile optimization with device-specific analysis and preview generation
- Vendor optimization overview with strategic planning

Implements Task 3.2.2 Phase 4 requirements for marketplace optimization API endpoints with
production-grade FastAPI implementation, authentication integration, and comprehensive validation.
"""

import logging
from datetime import date, datetime, timezone
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.marketplace_optimization import MarketplaceOptimizationService
from app.services.seo_optimization_service import SEOOptimizationService
from app.services.performance_analytics_service import PerformanceAnalyticsService
from app.services.competitive_analysis_service import CompetitiveAnalysisService
from app.services.mobile_preview_service import MobilePreviewService
from app.schemas.marketplace_optimization import (
    MarketplaceOptimizationDashboard, OptimizationAnalysisRequest,
    SEOAnalysisResponse, PerformanceMetricsResponse, CompetitiveAnalysisResponse,
    MobileOptimizationResponse, OptimizationRecommendationResponse
)
from app.schemas.auth import UserResponse, MessageResponse
from app.core.deps import get_current_user, get_current_vendor_user
from app.core.logging import correlation_id
# Note: Using standard HTTPException for error handling following existing patterns

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.get(
    "/dashboard/{service_id}",
    response_model=MarketplaceOptimizationDashboard,
    status_code=status.HTTP_200_OK,
    summary="Get marketplace optimization dashboard",
    description="Retrieve comprehensive optimization dashboard with cross-component insights for a service"
)
async def get_optimization_dashboard(
    service_id: int = Path(..., description="Service ID to get optimization dashboard for"),
    include_recommendations: bool = Query(True, description="Include optimization recommendations"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> MarketplaceOptimizationDashboard:
    """
    Get comprehensive marketplace optimization dashboard for a service.

    Provides complete optimization overview including:
    - SEO analysis with keyword optimization and content quality
    - Performance metrics with trend analysis and benchmarking
    - Competitive analysis with market positioning and insights
    - Mobile optimization with device-specific recommendations
    - Cross-component optimization recommendations with priority scoring

    **Authentication Required**: Vendor access
    **Rate Limiting**: 100 requests per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Optimization dashboard request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "include_recommendations": include_recommendations
        }
    )

    try:
        # Initialize marketplace optimization service
        optimization_service = MarketplaceOptimizationService(db)

        # Get optimization dashboard
        dashboard = await optimization_service.get_optimization_dashboard(
            service_id=service_id,
            include_recommendations=include_recommendations
        )

        logger.info(
            f"Optimization dashboard generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "overall_score": float(dashboard.overall_optimization_score),
                "recommendations_count": len(dashboard.recommendations)
            }
        )

        return dashboard

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate optimization dashboard: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate optimization dashboard"
        )


@router.post(
    "/analyze",
    response_model=MarketplaceOptimizationDashboard,
    status_code=status.HTTP_200_OK,
    summary="Run comprehensive optimization analysis",
    description="Execute comprehensive multi-component optimization analysis workflow"
)
async def run_comprehensive_analysis(
    analysis_request: OptimizationAnalysisRequest,
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> MarketplaceOptimizationDashboard:
    """
    Run comprehensive optimization analysis for a service.

    Executes multi-component analysis workflow including:
    - SEO analysis with content optimization and keyword recommendations
    - Performance analytics with trend forecasting and benchmarking
    - Competitive analysis with market positioning and strategic insights
    - Mobile optimization with device-specific analysis and recommendations
    - Cross-component optimization recommendations with implementation guidance

    **Authentication Required**: Vendor access
    **Rate Limiting**: 20 comprehensive analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Comprehensive analysis request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": analysis_request.service_id,
            "analysis_types": analysis_request.analysis_types,
            "analysis_depth": analysis_request.analysis_depth
        }
    )

    try:
        # Initialize marketplace optimization service
        optimization_service = MarketplaceOptimizationService(db)

        # Run comprehensive analysis
        analysis_results = await optimization_service.run_comprehensive_analysis(
            request=analysis_request
        )

        logger.info(
            f"Comprehensive analysis completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": analysis_request.service_id,
                "overall_score": float(analysis_results.overall_optimization_score),
                "analysis_types": analysis_request.analysis_types,
                "recommendations_count": len(analysis_results.recommendations)
            }
        )

        return analysis_results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to run comprehensive analysis: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": analysis_request.service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run comprehensive analysis"
        )


@router.get(
    "/vendor/{vendor_id}/overview",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get vendor optimization overview",
    description="Retrieve comprehensive optimization overview for all vendor services with strategic insights"
)
async def get_vendor_optimization_overview(
    vendor_id: int = Path(..., description="Vendor ID to get optimization overview for"),
    include_services: bool = Query(True, description="Include individual service optimization data"),
    days: int = Query(30, ge=7, le=365, description="Number of days for trend analysis"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get comprehensive optimization overview for a vendor.

    Provides vendor-level optimization insights including:
    - Optimization summary across all vendor services
    - Performance trends and improvement tracking
    - Competitive positioning in the marketplace
    - Priority optimization recommendations with strategic guidance
    - Optimization opportunities and implementation roadmap

    **Authentication Required**: Vendor access (own data) or Admin access
    **Rate Limiting**: 50 requests per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Vendor optimization overview request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "vendor_id": vendor_id,
            "include_services": include_services,
            "days": days
        }
    )

    try:
        # Verify vendor access (users can only access their own vendor data unless admin)
        if current_user.role not in ["admin", "super_admin"] and current_user.vendor_id != vendor_id:
            logger.warning(
                f"Unauthorized vendor access attempt",
                extra={
                    "correlation_id": correlation,
                    "user_id": current_user.id,
                    "requested_vendor_id": vendor_id,
                    "user_vendor_id": getattr(current_user, 'vendor_id', None)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: Can only view own vendor optimization data"
            )

        # Initialize marketplace optimization service
        optimization_service = MarketplaceOptimizationService(db)

        # Get vendor optimization overview
        overview = await optimization_service.get_vendor_optimization_overview(
            vendor_id=vendor_id,
            include_services=include_services,
            days=days
        )

        logger.info(
            f"Vendor optimization overview generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor_id,
                "services_count": len(overview.get("optimization_summary", {}).get("services", [])),
                "pending_recommendations": overview.get("pending_recommendations", {}).get("total_count", 0)
            }
        )

        return overview

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate vendor optimization overview: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate vendor optimization overview"
        )


# SEO Optimization Endpoints
@router.get(
    "/seo/analyze/{service_id}",
    response_model=SEOAnalysisResponse,
    status_code=status.HTTP_200_OK,
    summary="Get SEO analysis for service",
    description="Retrieve comprehensive SEO analysis with keyword optimization and content recommendations"
)
async def get_seo_analysis(
    service_id: int = Path(..., description="Service ID to analyze"),
    force_refresh: bool = Query(False, description="Force new analysis even if recent one exists"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> SEOAnalysisResponse:
    """
    Get comprehensive SEO analysis for a service.

    Provides detailed SEO insights including:
    - Overall SEO score with component breakdown
    - Keyword analysis with density and optimization recommendations
    - Content quality assessment with readability scoring
    - Meta tag optimization suggestions
    - Title and description optimization recommendations
    - Missing keyword identification and suggestions

    **Authentication Required**: Vendor access
    **Rate Limiting**: 100 SEO analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"SEO analysis request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "force_refresh": force_refresh
        }
    )

    try:
        # Initialize SEO optimization service
        seo_service = SEOOptimizationService(db)

        # Perform SEO analysis
        seo_analysis = await seo_service.analyze_service_seo(
            service_id=service_id,
            force_refresh=force_refresh
        )

        logger.info(
            f"SEO analysis completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "overall_seo_score": float(seo_analysis.overall_seo_score),
                "keyword_count": len(seo_analysis.primary_keywords) if seo_analysis.primary_keywords else 0
            }
        )

        return seo_analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to perform SEO analysis: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform SEO analysis"
        )


@router.get(
    "/seo/{service_id}/keywords",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get keyword recommendations",
    description="Get keyword optimization recommendations and analysis for a service"
)
async def get_keyword_recommendations(
    service_id: int = Path(..., description="Service ID to analyze"),
    target_keywords: Optional[List[str]] = Query(None, description="Optional target keywords to analyze"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get keyword optimization recommendations for a service.

    Provides comprehensive keyword analysis including:
    - Current keyword extraction and density analysis
    - Keyword optimization recommendations with priority scoring
    - Target keyword analysis and gap identification
    - Industry-specific keyword suggestions
    - Keyword density optimization recommendations
    - Long-tail keyword opportunities

    **Authentication Required**: Vendor access
    **Rate Limiting**: 50 keyword analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Keyword recommendations request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "target_keywords_count": len(target_keywords) if target_keywords else 0
        }
    )

    try:
        # Initialize SEO optimization service
        seo_service = SEOOptimizationService(db)

        # Get keyword recommendations
        keyword_recommendations = await seo_service.get_keyword_recommendations(
            service_id=service_id,
            target_keywords=target_keywords
        )

        logger.info(
            f"Keyword recommendations generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "current_keywords_count": keyword_recommendations.get("current_keywords", {}).get("total_keywords", 0),
                "recommendations_count": len(keyword_recommendations.get("recommendations", {}).get("suggested_keywords", []))
            }
        )

        return keyword_recommendations

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate keyword recommendations: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate keyword recommendations"
        )


@router.get(
    "/seo/{service_id}/content-optimization",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get content optimization suggestions",
    description="Get content optimization suggestions and improvement recommendations for a service"
)
async def get_content_optimization_suggestions(
    service_id: int = Path(..., description="Service ID to analyze"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get content optimization suggestions for a service.

    Provides comprehensive content analysis including:
    - Title optimization recommendations with length and keyword analysis
    - Description quality assessment with readability improvements
    - Content structure optimization suggestions
    - Meta tag optimization recommendations
    - Media optimization suggestions for better SEO
    - Content hierarchy and readability improvements

    **Authentication Required**: Vendor access
    **Rate Limiting**: 50 content analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Content optimization suggestions request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id
        }
    )

    try:
        # Initialize SEO optimization service
        seo_service = SEOOptimizationService(db)

        # Get content optimization suggestions
        content_suggestions = await seo_service.get_content_optimization_suggestions(
            service_id=service_id
        )

        logger.info(
            f"Content optimization suggestions generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "suggestions_categories": len(content_suggestions.get("optimization_suggestions", {})),
                "priority_actions": len(content_suggestions.get("implementation_order", []))
            }
        )

        return content_suggestions

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate content optimization suggestions: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate content optimization suggestions"
        )


# ============================================================================
# PERFORMANCE ANALYTICS ENDPOINTS
# ============================================================================

@router.get(
    "/performance/{service_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get performance analytics",
    description="Retrieve comprehensive performance analytics with trend analysis and forecasting"
)
async def get_performance_analytics(
    service_id: int = Path(..., description="Service ID to analyze"),
    start_date: date = Query(..., description="Start date for analysis"),
    end_date: date = Query(..., description="End date for analysis"),
    metric_period: str = Query("daily", regex="^(daily|weekly|monthly)$", description="Metric aggregation period"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get comprehensive performance analytics for a service.

    Provides detailed performance insights including:
    - Time-series performance data with trend analysis
    - Conversion funnel analysis with optimization opportunities
    - Performance benchmarking against market averages
    - Predictive analytics with forecasting capabilities
    - Statistical analysis with correlation coefficients
    - Performance scoring with improvement recommendations

    **Authentication Required**: Vendor access
    **Rate Limiting**: 50 performance analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Performance analytics request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "metric_period": metric_period
        }
    )

    try:
        # Validate date range
        if start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Start date must be before end date"
            )

        if (end_date - start_date).days > 365:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Date range cannot exceed 365 days"
            )

        # Initialize performance analytics service
        performance_service = PerformanceAnalyticsService(db)

        # Calculate service performance
        performance_analytics = await performance_service.calculate_service_performance(
            service_id=service_id,
            start_date=start_date,
            end_date=end_date,
            metric_period=metric_period
        )

        logger.info(
            f"Performance analytics generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "data_points": len(performance_analytics.get("time_series_data", [])),
                "overall_score": performance_analytics.get("performance_scores", {}).get("overall_performance_score", 0)
            }
        )

        return performance_analytics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate performance analytics: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate performance analytics"
        )


@router.get(
    "/performance/{service_id}/benchmarks",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get performance benchmarks",
    description="Get performance benchmarks and market comparison for a service"
)
async def get_performance_benchmarks(
    service_id: int = Path(..., description="Service ID to benchmark"),
    category_id: Optional[int] = Query(None, description="Category ID for comparison"),
    days: int = Query(30, ge=7, le=365, description="Number of days for benchmark analysis"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get performance benchmarks for a service against market averages.

    Provides comprehensive benchmarking including:
    - Performance comparison against market averages
    - Percentile rankings in category and overall market
    - Top performer analysis and comparison
    - Improvement opportunities with specific recommendations
    - Competitive positioning insights
    - Performance gap analysis with actionable insights

    **Authentication Required**: Vendor access
    **Rate Limiting**: 30 benchmark analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Performance benchmarks request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "category_id": category_id,
            "days": days
        }
    )

    try:
        # Initialize performance analytics service
        performance_service = PerformanceAnalyticsService(db)

        # Get performance benchmarks
        benchmarks = await performance_service.get_performance_benchmarks(
            service_id=service_id,
            category_id=category_id,
            days=days
        )

        logger.info(
            f"Performance benchmarks generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "benchmark_score": benchmarks.get("benchmark_scores", {}).get("overall_benchmark_score", 0),
                "improvement_opportunities": len(benchmarks.get("improvement_opportunities", []))
            }
        )

        return benchmarks

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate performance benchmarks: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate performance benchmarks"
        )


@router.get(
    "/performance/{service_id}/predictions",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get performance predictions",
    description="Get performance trend predictions and forecasting for a service"
)
async def get_performance_predictions(
    service_id: int = Path(..., description="Service ID to predict for"),
    forecast_days: int = Query(30, ge=7, le=90, description="Number of days to forecast"),
    historical_days: int = Query(90, ge=30, le=365, description="Number of historical days to analyze"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get performance trend predictions using historical data analysis.

    Provides predictive analytics including:
    - Performance trend forecasting with confidence intervals
    - Seasonal pattern identification and analysis
    - Statistical trend analysis with correlation coefficients
    - Forecast insights with actionable recommendations
    - Performance prediction accuracy metrics
    - Strategic planning recommendations based on trends

    **Authentication Required**: Vendor access
    **Rate Limiting**: 20 prediction analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Performance predictions request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "forecast_days": forecast_days,
            "historical_days": historical_days
        }
    )

    try:
        # Initialize performance analytics service
        performance_service = PerformanceAnalyticsService(db)

        # Get performance predictions
        predictions = await performance_service.predict_performance_trends(
            service_id=service_id,
            forecast_days=forecast_days,
            historical_days=historical_days
        )

        logger.info(
            f"Performance predictions generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "forecast_points": len(predictions.get("predictions", {}).get("conversion_rate", [])),
                "seasonal_patterns": predictions.get("seasonal_patterns", {}).get("has_seasonal_pattern", False)
            }
        )

        return predictions

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate performance predictions: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate performance predictions"
        )


# ============================================================================
# COMPETITIVE ANALYSIS ENDPOINTS
# ============================================================================

@router.get(
    "/competitive/analyze/{service_id}",
    response_model=CompetitiveAnalysisResponse,
    status_code=status.HTTP_200_OK,
    summary="Get competitive analysis",
    description="Retrieve comprehensive competitive analysis with market positioning and strategic insights"
)
async def get_competitive_analysis(
    service_id: int = Path(..., description="Service ID to analyze"),
    market_category: str = Query(..., description="Market category for analysis"),
    geographic_scope: str = Query("local", regex="^(local|regional|national)$", description="Geographic scope"),
    force_refresh: bool = Query(False, description="Force new analysis even if recent one exists"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> CompetitiveAnalysisResponse:
    """
    Get comprehensive competitive analysis for a service.

    Provides detailed competitive insights including:
    - Market positioning and ranking analysis
    - Pricing competitiveness assessment
    - Quality positioning against competitors
    - Unique selling points identification
    - Competitive gaps and improvement opportunities
    - Strategic recommendations for market advantage

    **Authentication Required**: Vendor access
    **Rate Limiting**: 30 competitive analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Competitive analysis request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "market_category": market_category,
            "geographic_scope": geographic_scope,
            "force_refresh": force_refresh
        }
    )

    try:
        # Initialize competitive analysis service
        competitive_service = CompetitiveAnalysisService(db)

        # Analyze market position
        competitive_analysis = await competitive_service.analyze_market_position(
            service_id=service_id,
            market_category=market_category,
            geographic_scope=geographic_scope,
            force_refresh=force_refresh
        )

        logger.info(
            f"Competitive analysis completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "market_position_rank": competitive_analysis.market_position_rank,
                "market_share": float(competitive_analysis.market_share_percentage),
                "total_competitors": competitive_analysis.total_competitors
            }
        )

        return competitive_analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to perform competitive analysis: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform competitive analysis"
        )


@router.get(
    "/competitive/market-intelligence",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get market intelligence",
    description="Get comprehensive market intelligence for a category and geographic scope"
)
async def get_market_intelligence(
    market_category: str = Query(..., description="Market category to analyze"),
    geographic_scope: str = Query("local", regex="^(local|regional|national)$", description="Geographic scope"),
    days: int = Query(90, ge=30, le=365, description="Number of days for trend analysis"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get comprehensive market intelligence for a category and scope.

    Provides market insights including:
    - Market statistics and competitive landscape overview
    - Market trends and evolution analysis
    - Market segmentation (leaders, challengers, followers, niche players)
    - Top performer analysis and success factors
    - Market opportunities and strategic insights
    - Competitive dynamics and threat assessment

    **Authentication Required**: Vendor access
    **Rate Limiting**: 20 market intelligence requests per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Market intelligence request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "market_category": market_category,
            "geographic_scope": geographic_scope,
            "days": days
        }
    )

    try:
        # Initialize competitive analysis service
        competitive_service = CompetitiveAnalysisService(db)

        # Get market intelligence
        market_intelligence = await competitive_service.get_market_intelligence(
            market_category=market_category,
            geographic_scope=geographic_scope,
            days=days
        )

        logger.info(
            f"Market intelligence generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "market_category": market_category,
                "total_competitors": market_intelligence.get("market_statistics", {}).get("total_competitors", 0),
                "top_performers": len(market_intelligence.get("top_performers", []))
            }
        )

        return market_intelligence

    except Exception as e:
        logger.error(
            f"Failed to generate market intelligence: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "market_category": market_category,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate market intelligence"
        )


# ============================================================================
# MOBILE OPTIMIZATION ENDPOINTS
# ============================================================================

@router.get(
    "/mobile/analyze/{service_id}",
    response_model=MobileOptimizationResponse,
    status_code=status.HTTP_200_OK,
    summary="Get mobile optimization analysis",
    description="Retrieve comprehensive mobile optimization analysis with device-specific insights"
)
async def get_mobile_optimization_analysis(
    service_id: int = Path(..., description="Service ID to analyze"),
    device_type: str = Query("mobile", regex="^(mobile|tablet|desktop)$", description="Device type for analysis"),
    screen_size_category: str = Query("standard", regex="^(small|standard|large)$", description="Screen size category"),
    force_refresh: bool = Query(False, description="Force new analysis even if recent one exists"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> MobileOptimizationResponse:
    """
    Get comprehensive mobile optimization analysis for a service.

    Provides detailed mobile insights including:
    - Overall mobile optimization score with component breakdown
    - Responsive design validation and scoring
    - Loading speed analysis with Core Web Vitals simulation
    - Touch interface optimization assessment
    - Image optimization recommendations
    - Navigation UX evaluation and improvements

    **Authentication Required**: Vendor access
    **Rate Limiting**: 50 mobile analyses per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Mobile optimization analysis request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "device_type": device_type,
            "screen_size_category": screen_size_category,
            "force_refresh": force_refresh
        }
    )

    try:
        # Initialize mobile preview service
        mobile_service = MobilePreviewService(db)

        # Perform mobile optimization analysis
        mobile_analysis = await mobile_service.analyze_mobile_optimization(
            service_id=service_id,
            device_type=device_type,
            screen_size_category=screen_size_category,
            force_refresh=force_refresh
        )

        logger.info(
            f"Mobile optimization analysis completed successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "device_type": device_type,
                "overall_mobile_score": float(mobile_analysis.overall_mobile_score),
                "loading_speed_score": float(mobile_analysis.loading_speed_score)
            }
        )

        return mobile_analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to perform mobile optimization analysis: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform mobile optimization analysis"
        )


@router.post(
    "/mobile/preview/{service_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Generate mobile app preview",
    description="Generate mobile app preview for multiple device types with optimization insights"
)
async def generate_mobile_preview(
    service_id: int = Path(..., description="Service ID to generate preview for"),
    device_types: List[str] = Query(["mobile", "tablet"], description="List of device types for preview generation"),
    current_user: UserResponse = Depends(get_current_vendor_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Generate mobile app preview for multiple device types.

    Provides comprehensive mobile preview including:
    - Device-specific preview generation and optimization analysis
    - Cross-device performance comparison and benchmarking
    - Mobile optimization recommendations with implementation guidance
    - Mobile readiness score and improvement opportunities
    - Preview image generation and device-specific layouts
    - Mobile conversion and engagement optimization insights

    **Authentication Required**: Vendor access
    **Rate Limiting**: 20 preview generations per hour per vendor
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Mobile preview generation request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "service_id": service_id,
            "device_types": device_types
        }
    )

    try:
        # Validate device types
        valid_device_types = {"mobile", "tablet", "desktop"}
        invalid_types = [dt for dt in device_types if dt not in valid_device_types]
        if invalid_types:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Invalid device types: {invalid_types}. Valid types: {list(valid_device_types)}"
            )

        # Initialize mobile preview service
        mobile_service = MobilePreviewService(db)

        # Generate mobile preview
        mobile_preview = await mobile_service.generate_mobile_preview(
            service_id=service_id,
            device_types=device_types
        )

        logger.info(
            f"Mobile preview generated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "device_types": device_types,
                "mobile_readiness_score": mobile_preview.get("mobile_readiness_score", 0),
                "optimization_recommendations": len(mobile_preview.get("optimization_recommendations", []))
            }
        )

        return mobile_preview

    except HTTPException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate mobile preview: {str(e)}",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "service_id": service_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate mobile preview"
        )
