"""PostgreSQL optimized schema with production-grade features

Revision ID: postgresql_optimized
Revises: 4a9bc5c64e13
Create Date: 2025-05-24 01:00:00.000000

This migration creates a PostgreSQL-optimized schema with:
- UUID primary keys using PostgreSQL UUID extension
- JSONB columns for better performance
- PostgreSQL-specific indexes (GIN, GIST)
- Proper constraints and triggers
- Production-grade performance optimizations
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'postgresql_optimized'
down_revision = '4a9bc5c64e13'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade to PostgreSQL-optimized schema."""

    # Enable required PostgreSQL extensions
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    op.execute('CREATE EXTENSION IF NOT EXISTS "pg_trgm"')
    op.execute('CREATE EXTENSION IF NOT EXISTS "btree_gin"')

    # Create enum types for better type safety
    op.execute("""
        CREATE TYPE user_role AS ENUM (
            'admin', 'customer', 'vendor', 'moderator'
        )
    """)

    op.execute("""
        CREATE TYPE verification_status AS ENUM (
            'pending', 'under_review', 'approved', 'rejected', 'suspended'
        )
    """)

    op.execute("""
        CREATE TYPE marketplace_status AS ENUM (
            'active', 'inactive', 'suspended', 'pending'
        )
    """)

    op.execute("""
        CREATE TYPE document_status AS ENUM (
            'pending', 'approved', 'rejected', 'expired'
        )
    """)

    op.execute("""
        CREATE TYPE document_type AS ENUM (
            'business_license', 'tax_certificate', 'insurance_certificate',
            'professional_certification', 'identity_document', 'utility_bill'
        )
    """)

    # Add PostgreSQL-specific optimizations to existing tables

    # Update users table with PostgreSQL optimizations
    with op.batch_alter_table('users', schema=None) as batch_op:
        # Add full-text search column
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR, nullable=True))

        # Create GIN index for full-text search
        batch_op.create_index(
            'idx_users_search_vector_gin',
            ['search_vector'],
            postgresql_using='gin'
        )

        # Create trigram indexes for fuzzy search
        batch_op.create_index(
            'idx_users_name_trgm',
            [sa.text("(first_name || ' ' || last_name)")],
            postgresql_using='gin',
            postgresql_ops={'first_name || \' \' || last_name': 'gin_trgm_ops'}
        )

        batch_op.create_index(
            'idx_users_email_trgm',
            ['email'],
            postgresql_using='gin',
            postgresql_ops={'email': 'gin_trgm_ops'}
        )

    # Update vendor_profiles table with PostgreSQL optimizations
    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        # Convert JSON columns to JSONB for better performance
        batch_op.alter_column(
            'operating_hours',
            type_=postgresql.JSONB(),
            postgresql_using='operating_hours::jsonb'
        )
        batch_op.alter_column(
            'social_media_links',
            type_=postgresql.JSONB(),
            postgresql_using='social_media_links::jsonb'
        )
        batch_op.alter_column(
            'languages_spoken',
            type_=postgresql.JSONB(),
            postgresql_using='languages_spoken::jsonb'
        )
        batch_op.alter_column(
            'specializations',
            type_=postgresql.JSONB(),
            postgresql_using='specializations::jsonb'
        )
        batch_op.alter_column(
            'certifications',
            type_=postgresql.JSONB(),
            postgresql_using='certifications::jsonb'
        )
        batch_op.alter_column(
            'gallery_images',
            type_=postgresql.JSONB(),
            postgresql_using='gallery_images::jsonb'
        )
        batch_op.alter_column(
            'insurance_details',
            type_=postgresql.JSONB(),
            postgresql_using='insurance_details::jsonb'
        )
        batch_op.alter_column(
            'additional_info',
            type_=postgresql.JSONB(),
            postgresql_using='additional_info::jsonb'
        )

        # Add GIN indexes for JSONB columns
        batch_op.create_index(
            'idx_vendor_profiles_languages_gin',
            ['languages_spoken'],
            postgresql_using='gin'
        )
        batch_op.create_index(
            'idx_vendor_profiles_specializations_gin',
            ['specializations'],
            postgresql_using='gin'
        )

        # Add spatial indexes for coordinates (separate indexes for better compatibility)
        batch_op.create_index(
            'idx_vendor_profiles_latitude',
            ['latitude'],
            postgresql_using='btree'
        )
        batch_op.create_index(
            'idx_vendor_profiles_longitude',
            ['longitude'],
            postgresql_using='btree'
        )

        # Add full-text search
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR, nullable=True))
        batch_op.create_index(
            'idx_vendor_profiles_search_vector_gin',
            ['search_vector'],
            postgresql_using='gin'
        )

    # Update vendor_documents table
    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.alter_column(
            'verification_data',
            type_=postgresql.JSONB(),
            postgresql_using='verification_data::jsonb'
        )

        batch_op.create_index(
            'idx_vendor_documents_verification_data_gin',
            ['verification_data'],
            postgresql_using='gin'
        )

    # Create triggers for automatic search vector updates
    op.execute("""
        CREATE OR REPLACE FUNCTION update_user_search_vector() RETURNS trigger AS $$
        BEGIN
            NEW.search_vector :=
                setweight(to_tsvector('english', COALESCE(NEW.first_name, '')), 'A') ||
                setweight(to_tsvector('english', COALESCE(NEW.last_name, '')), 'A') ||
                setweight(to_tsvector('english', COALESCE(NEW.email, '')), 'B') ||
                setweight(to_tsvector('english', COALESCE(NEW.bio, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER users_search_vector_update
            BEFORE INSERT OR UPDATE ON users
            FOR EACH ROW EXECUTE FUNCTION update_user_search_vector();
    """)

    op.execute("""
        CREATE OR REPLACE FUNCTION update_vendor_profile_search_vector() RETURNS trigger AS $$
        BEGIN
            NEW.search_vector :=
                setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'A') ||
                setweight(to_tsvector('english', COALESCE(NEW.short_description, '')), 'B') ||
                setweight(to_tsvector('english', COALESCE(NEW.tagline, '')), 'B') ||
                setweight(to_tsvector('english', COALESCE(NEW.city, '')), 'C') ||
                setweight(to_tsvector('english', COALESCE(NEW.state, '')), 'C') ||
                setweight(to_tsvector('english', COALESCE(NEW.country, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER vendor_profiles_search_vector_update
            BEFORE INSERT OR UPDATE ON vendor_profiles
            FOR EACH ROW EXECUTE FUNCTION update_vendor_profile_search_vector();
    """)

    # Update existing records with search vectors
    op.execute("UPDATE users SET search_vector = NULL WHERE search_vector IS NULL")
    op.execute("UPDATE vendor_profiles SET search_vector = NULL WHERE search_vector IS NULL")

    # Create additional performance indexes
    op.create_index(
        'idx_users_created_at_btree',
        'users',
        ['created_at'],
        postgresql_using='btree'
    )

    op.create_index(
        'idx_vendors_performance_composite',
        'vendors',
        ['average_rating', 'total_bookings', 'marketplace_status'],
        postgresql_using='btree'
    )

    # Add check constraints for data integrity
    op.create_check_constraint(
        'ck_users_email_format',
        'users',
        "email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'"
    )

    op.create_check_constraint(
        'ck_vendor_profiles_coordinates_valid',
        'vendor_profiles',
        "(latitude IS NULL AND longitude IS NULL) OR (latitude IS NOT NULL AND longitude IS NOT NULL)"
    )


def downgrade() -> None:
    """Downgrade from PostgreSQL-optimized schema."""

    # Drop triggers
    op.execute("DROP TRIGGER IF EXISTS users_search_vector_update ON users")
    op.execute("DROP TRIGGER IF EXISTS vendor_profiles_search_vector_update ON vendor_profiles")
    op.execute("DROP FUNCTION IF EXISTS update_user_search_vector()")
    op.execute("DROP FUNCTION IF EXISTS update_vendor_profile_search_vector()")

    # Drop check constraints
    op.drop_constraint('ck_users_email_format', 'users')
    op.drop_constraint('ck_vendor_profiles_coordinates_valid', 'vendor_profiles')

    # Drop PostgreSQL-specific indexes
    op.drop_index('idx_users_search_vector_gin', 'users')
    op.drop_index('idx_users_name_trgm', 'users')
    op.drop_index('idx_users_email_trgm', 'users')
    op.drop_index('idx_users_created_at_btree', 'users')
    op.drop_index('idx_vendors_performance_composite', 'vendors')

    # Drop vendor_profiles optimizations
    op.drop_index('idx_vendor_profiles_languages_gin', 'vendor_profiles')
    op.drop_index('idx_vendor_profiles_specializations_gin', 'vendor_profiles')
    op.drop_index('idx_vendor_profiles_latitude', 'vendor_profiles')
    op.drop_index('idx_vendor_profiles_longitude', 'vendor_profiles')
    op.drop_index('idx_vendor_profiles_search_vector_gin', 'vendor_profiles')
    op.drop_index('idx_vendor_documents_verification_data_gin', 'vendor_documents')

    # Remove search vector columns
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('search_vector')

    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.drop_column('search_vector')

    # Convert JSONB back to JSON
    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.alter_column('operating_hours', type_=sa.JSON())
        batch_op.alter_column('social_media_links', type_=sa.JSON())
        batch_op.alter_column('languages_spoken', type_=sa.JSON())
        batch_op.alter_column('specializations', type_=sa.JSON())
        batch_op.alter_column('certifications', type_=sa.JSON())
        batch_op.alter_column('gallery_images', type_=sa.JSON())
        batch_op.alter_column('insurance_details', type_=sa.JSON())
        batch_op.alter_column('additional_info', type_=sa.JSON())

    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.alter_column('verification_data', type_=sa.JSON())

    # Drop enum types
    op.execute("DROP TYPE IF EXISTS document_type")
    op.execute("DROP TYPE IF EXISTS document_status")
    op.execute("DROP TYPE IF EXISTS marketplace_status")
    op.execute("DROP TYPE IF EXISTS verification_status")
    op.execute("DROP TYPE IF EXISTS user_role")
