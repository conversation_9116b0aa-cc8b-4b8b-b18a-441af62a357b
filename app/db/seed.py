"""
Database seeding infrastructure for Culture Connect Backend API.

This module provides comprehensive database seeding functionality for
development and testing environments with sample data generation.
"""

import asyncio
import json
import logging
import random
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from faker import Faker

from app.core.config import settings
from app.core.security import get_password_hash, UserRole
from app.db.session import get_async_session_context
from app.models.user import User, APIKey, UserSession
from app.models.vendor import Vendor, VendorProfile, VendorDocument, VendorType, VerificationStatus, MarketplaceStatus, DocumentType, DocumentStatus

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Faker for generating realistic test data
fake = Faker()


class DatabaseSeeder:
    """
    Comprehensive database seeding with realistic test data.

    Provides methods to seed the database with sample users, vendors,
    and related data for development and testing purposes.
    """

    def __init__(self):
        self.fake = Faker()
        self.created_users: List[User] = []
        self.created_vendors: List[Vendor] = []

    async def seed_all(self, session: AsyncSession, environment: str = "development") -> Dict[str, int]:
        """
        Seed all tables with sample data.

        Args:
            session: Database session
            environment: Environment type (development, staging, testing)

        Returns:
            Dict[str, int]: Count of created records by type
        """
        logger.info(f"Starting database seeding for {environment} environment")

        counts = {
            "users": 0,
            "vendors": 0,
            "vendor_profiles": 0,
            "vendor_documents": 0,
            "api_keys": 0,
            "user_sessions": 0
        }

        try:
            # Clear existing data if in development
            if environment == "development":
                await self._clear_existing_data(session)

            # Seed users
            users = await self._seed_users(session, count=50)
            counts["users"] = len(users)

            # Seed vendors
            vendors = await self._seed_vendors(session, users[:30])  # 30 vendors from first 30 users
            counts["vendors"] = len(vendors)

            # Seed vendor profiles
            profiles = await self._seed_vendor_profiles(session, vendors)
            counts["vendor_profiles"] = len(profiles)

            # Seed vendor documents
            documents = await self._seed_vendor_documents(session, vendors)
            counts["vendor_documents"] = len(documents)

            # Seed API keys
            api_keys = await self._seed_api_keys(session, users[:10])  # API keys for first 10 users
            counts["api_keys"] = len(api_keys)

            # Seed user sessions
            sessions = await self._seed_user_sessions(session, users[:20])  # Sessions for first 20 users
            counts["user_sessions"] = len(sessions)

            await session.commit()
            logger.info(f"Database seeding completed successfully: {counts}")

        except Exception as e:
            await session.rollback()
            logger.error(f"Database seeding failed: {str(e)}")
            raise

        return counts

    async def _clear_existing_data(self, session: AsyncSession) -> None:
        """Clear existing data from all tables."""
        logger.info("Clearing existing data...")

        # Delete in reverse dependency order
        await session.execute(delete(UserSession))
        await session.execute(delete(APIKey))
        await session.execute(delete(VendorDocument))
        await session.execute(delete(VendorProfile))
        await session.execute(delete(Vendor))
        await session.execute(delete(User))

        await session.commit()
        logger.info("Existing data cleared")

    async def _seed_users(self, session: AsyncSession, count: int = 50) -> List[User]:
        """Seed users table with sample data."""
        logger.info(f"Seeding {count} users...")

        users = []

        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            first_name="Admin",
            last_name="User",
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            phone_number="+234" + self.fake.numerify("##########"),
            bio="System administrator",
            location="Lagos, Nigeria",
            terms_accepted_at=datetime.utcnow(),
            privacy_policy_accepted_at=datetime.utcnow()
        )
        session.add(admin_user)
        users.append(admin_user)

        # Create sample users
        for i in range(count - 1):
            role = random.choice([UserRole.CUSTOMER, UserRole.VENDOR])

            user = User(
                email=self.fake.unique.email(),
                hashed_password=get_password_hash("password123"),
                first_name=self.fake.first_name(),
                last_name=self.fake.last_name(),
                role=role,
                is_active=random.choice([True, True, True, False]),  # 75% active
                is_verified=random.choice([True, True, False]),  # 66% verified
                phone_number="+234" + self.fake.numerify("##########"),
                bio=self.fake.text(max_nb_chars=200),
                location=random.choice([
                    "Lagos, Nigeria", "Abuja, Nigeria", "Port Harcourt, Nigeria",
                    "Kano, Nigeria", "Ibadan, Nigeria", "Benin City, Nigeria"
                ]),
                timezone="Africa/Lagos",
                language=random.choice(["en", "yo", "ig", "ha"]),
                last_login=self.fake.date_time_between(start_date="-30d", end_date="now"),
                terms_accepted_at=self.fake.date_time_between(start_date="-365d", end_date="now"),
                privacy_policy_accepted_at=self.fake.date_time_between(start_date="-365d", end_date="now")
            )
            session.add(user)
            users.append(user)

        await session.flush()  # Get IDs
        self.created_users = users
        logger.info(f"Created {len(users)} users")
        return users

    async def _seed_vendors(self, session: AsyncSession, users: List[User]) -> List[Vendor]:
        """Seed vendors table with sample data."""
        logger.info(f"Seeding vendors for {len(users)} users...")

        vendors = []
        business_names = [
            "Lagos Cultural Tours", "Abuja Heritage Walks", "Niger Delta Adventures",
            "Yoruba Cultural Experience", "Igbo Traditional Tours", "Hausa Heritage Guide",
            "Nigerian Cuisine Tours", "Afrobeat Music Experience", "Art & Craft Workshop",
            "Traditional Dance Academy", "Nigerian History Tours", "Wildlife Safari Nigeria",
            "Beach Resort Lagos", "Mountain Hiking Plateau", "River Cruise Niger",
            "Cultural Festival Guide", "Traditional Wedding Tours", "Market Experience Lagos",
            "Nollywood Behind Scenes", "Traditional Medicine Tour", "Textile & Fashion Tour",
            "Music Production Studio", "Photography Workshop", "Cooking Class Nigerian",
            "Language Learning Center", "Traditional Games Academy", "Storytelling Sessions",
            "Drumming Workshop", "Craft Making Class", "Cultural Exchange Program"
        ]

        for i, user in enumerate(users):
            if user.role != UserRole.VENDOR:
                continue

            vendor = Vendor(
                user_id=user.id,
                business_name=business_names[i % len(business_names)],
                business_type=random.choice(list(VendorType)),
                business_registration_number=f"RC{self.fake.numerify('######')}",
                tax_id=f"TIN{self.fake.numerify('#########')}",
                verification_status=random.choice(list(VerificationStatus)),
                marketplace_status=random.choice(list(MarketplaceStatus)),
                onboarding_completed=random.choice([True, False]),
                onboarding_step=random.randint(1, 6),
                commission_rate=Decimal(str(random.uniform(0.10, 0.20))),
                total_earnings=Decimal(str(random.uniform(0, 50000))),
                total_bookings=random.randint(0, 100),
                average_rating=Decimal(str(random.uniform(3.0, 5.0))),
                total_reviews=random.randint(0, 50),
                response_rate=Decimal(str(random.uniform(70, 100))),
                response_time_hours=Decimal(str(random.uniform(1, 48))),
                seo_score=Decimal(str(random.uniform(40, 95))),
                marketplace_ranking=random.randint(1, 1000),
                listing_quality_score=Decimal(str(random.uniform(60, 100))),
                last_activity_at=self.fake.date_time_between(start_date="-7d", end_date="now")
            )
            session.add(vendor)
            vendors.append(vendor)

        await session.flush()  # Get IDs

        self.created_vendors = vendors
        logger.info(f"Created {len(vendors)} vendors")
        return vendors

    async def _seed_vendor_profiles(self, session: AsyncSession, vendors: List[Vendor]) -> List[VendorProfile]:
        """Seed vendor profiles table with sample data."""
        logger.info(f"Seeding vendor profiles for {len(vendors)} vendors...")

        profiles = []

        for vendor in vendors:
            profile = VendorProfile(
                vendor_id=vendor.id,
                description=self.fake.text(max_nb_chars=500),
                business_address=self.fake.address(),
                city=random.choice(["Lagos", "Abuja", "Port Harcourt", "Kano", "Ibadan", "Benin City"]),
                state=random.choice(["Lagos", "FCT", "Rivers", "Kano", "Oyo", "Edo"]),
                country="Nigeria",
                postal_code=self.fake.postcode(),
                latitude=Decimal(str(random.uniform(4.0, 14.0))),  # Nigeria latitude range
                longitude=Decimal(str(random.uniform(3.0, 15.0))),  # Nigeria longitude range
                contact_phone="+234" + self.fake.numerify("##########"),
                contact_email=self.fake.email(),
                website_url=self.fake.url(),
                operating_hours={
                    "monday": {"open": "09:00", "close": "17:00"},
                    "tuesday": {"open": "09:00", "close": "17:00"},
                    "wednesday": {"open": "09:00", "close": "17:00"},
                    "thursday": {"open": "09:00", "close": "17:00"},
                    "friday": {"open": "09:00", "close": "17:00"},
                    "saturday": {"open": "10:00", "close": "16:00"},
                    "sunday": {"closed": True}
                },
                timezone="Africa/Lagos",
                social_media_links={
                    "facebook": f"https://facebook.com/{self.fake.user_name()}",
                    "instagram": f"https://instagram.com/{self.fake.user_name()}",
                    "twitter": f"https://twitter.com/{self.fake.user_name()}"
                },
                languages_spoken=json.dumps(random.sample(["English", "Yoruba", "Igbo", "Hausa", "Pidgin"], k=random.randint(2, 4))),
                specializations=json.dumps(random.sample([
                    "Cultural Tours", "Historical Sites", "Traditional Cuisine", "Art & Crafts",
                    "Music & Dance", "Religious Sites", "Nature Tours", "Adventure Sports"
                ], k=random.randint(2, 5))),
                certifications=json.dumps([
                    f"Tourism Certification {self.fake.year()}",
                    f"Cultural Guide License {self.fake.year()}"
                ]),
                max_group_size=random.randint(5, 50),
                min_advance_booking=random.randint(1, 72),
                cancellation_policy=self.fake.text(max_nb_chars=300),
                logo_url=f"https://example.com/logos/{self.fake.uuid4()}.jpg",
                cover_image_url=f"https://example.com/covers/{self.fake.uuid4()}.jpg",
                gallery_images=json.dumps([
                    f"https://example.com/gallery/{self.fake.uuid4()}.jpg" for _ in range(random.randint(3, 8))
                ]),
                years_in_business=random.randint(1, 20),
                business_license_number=f"BL{self.fake.numerify('######')}",
                insurance_details={
                    "provider": "Nigerian Insurance Company",
                    "policy_number": self.fake.numerify("POL#########"),
                    "coverage_amount": random.randint(100000, 1000000)
                },
                auto_accept_bookings=random.choice([True, False]),
                instant_booking_enabled=random.choice([True, False]),
                additional_info={
                    "payment_methods": ["Cash", "Bank Transfer", "Mobile Money"],
                    "special_offers": random.choice([True, False]),
                    "group_discounts": random.choice([True, False])
                }
            )
            session.add(profile)
            profiles.append(profile)

        await session.flush()
        logger.info(f"Created {len(profiles)} vendor profiles")
        return profiles

    async def _seed_vendor_documents(self, session: AsyncSession, vendors: List[Vendor]) -> List[VendorDocument]:
        """Seed vendor documents table with sample data."""
        logger.info(f"Seeding vendor documents for {len(vendors)} vendors...")

        documents = []

        for vendor in vendors:
            # Each vendor gets 2-4 documents
            doc_count = random.randint(2, 4)
            doc_types = random.sample(list(DocumentType), k=doc_count)

            for doc_type in doc_types:
                document = VendorDocument(
                    vendor_id=vendor.id,
                    document_type=doc_type,
                    document_name=f"{doc_type.value.replace('_', ' ').title()} - {vendor.business_name}",
                    document_url=f"https://example.com/documents/{self.fake.uuid4()}.pdf",
                    file_size=random.randint(100000, 5000000),  # 100KB to 5MB
                    file_type="application/pdf",
                    status=random.choice(list(DocumentStatus)),
                    reviewed_at=self.fake.date_time_between(start_date="-30d", end_date="now") if random.choice([True, False]) else None,
                    reviewed_by=1 if random.choice([True, False]) else None,  # Admin user ID
                    review_notes=self.fake.text(max_nb_chars=200) if random.choice([True, False, False, False]) else None,
                    expiry_date=self.fake.date_between(start_date="+1y", end_date="+5y") if random.choice([True, False]) else None,
                    document_number=self.fake.numerify("DOC#########") if random.choice([True, False]) else None,
                    issuing_authority=random.choice(["CAC", "NIMC", "FIRS", "State Government"]) if random.choice([True, False]) else None,
                    verification_data={
                        "upload_source": "web_portal",
                        "file_hash": self.fake.sha256(),
                        "scan_status": "clean"
                    },
                    is_primary=random.choice([True, False])
                )
                session.add(document)
                documents.append(document)

        await session.flush()
        logger.info(f"Created {len(documents)} vendor documents")
        return documents

    async def _seed_api_keys(self, session: AsyncSession, users: List[User]) -> List[APIKey]:
        """Seed API keys table with sample data."""
        logger.info(f"Seeding API keys for {len(users)} users...")

        api_keys = []

        for user in users:
            # Some users get API keys
            if random.choice([True, False, False]):  # 33% chance
                api_key = APIKey(
                    user_id=user.id,
                    name=f"{user.first_name}'s API Key",
                    key_hash=self.fake.sha256(),
                    scopes=json.dumps(random.sample(["read", "write", "admin", "vendor"], k=random.randint(1, 3))),
                    is_active=random.choice([True, True, False]),  # 66% active
                    expires_at=self.fake.date_time_between(start_date="+30d", end_date="+365d"),
                    last_used=self.fake.date_time_between(start_date="-7d", end_date="now") if random.choice([True, False]) else None,
                    usage_count=random.randint(0, 1000)
                )
                session.add(api_key)
                api_keys.append(api_key)

        await session.flush()
        logger.info(f"Created {len(api_keys)} API keys")
        return api_keys

    async def _seed_user_sessions(self, session: AsyncSession, users: List[User]) -> List[UserSession]:
        """Seed user sessions table with sample data."""
        logger.info(f"Seeding user sessions for {len(users)} users...")

        sessions = []

        for user in users:
            # Active users get 1-3 sessions
            if user.is_active:
                session_count = random.randint(1, 3)

                for _ in range(session_count):
                    user_session = UserSession(
                        user_id=user.id,
                        session_id=self.fake.uuid4(),
                        ip_address=self.fake.ipv4(),
                        user_agent=self.fake.user_agent(),
                        is_active=random.choice([True, True, False]),  # 66% active
                        expires_at=self.fake.date_time_between(start_date="+1d", end_date="+30d"),
                        last_activity=self.fake.date_time_between(start_date="-1d", end_date="now")
                    )
                    session.add(user_session)
                    sessions.append(user_session)

        await session.flush()
        logger.info(f"Created {len(sessions)} user sessions")
        return sessions


# Seeding utility functions

async def seed_database(environment: str = "development") -> Dict[str, int]:
    """
    Seed the database with sample data.

    Args:
        environment: Environment type (development, staging, testing)

    Returns:
        Dict[str, int]: Count of created records by type
    """
    seeder = DatabaseSeeder()

    async with get_async_session_context() as session:
        return await seeder.seed_all(session, environment)


async def clear_database() -> None:
    """Clear all data from the database."""
    logger.info("Clearing database...")

    async with get_async_session_context() as session:
        seeder = DatabaseSeeder()
        await seeder._clear_existing_data(session)

    logger.info("Database cleared successfully")


# CLI interface for seeding
if __name__ == "__main__":
    import sys

    async def main():
        if len(sys.argv) > 1:
            command = sys.argv[1]

            if command == "seed":
                environment = sys.argv[2] if len(sys.argv) > 2 else "development"
                counts = await seed_database(environment)
                print(f"Database seeded successfully: {counts}")

            elif command == "clear":
                await clear_database()
                print("Database cleared successfully")

            else:
                print("Usage: python -m app.db.seed [seed|clear] [environment]")
        else:
            print("Usage: python -m app.db.seed [seed|clear] [environment]")

    asyncio.run(main())
