"""
Load testing scenarios for geolocation functionality.

This module provides comprehensive load testing for:
- Concurrent geolocation detection stress testing
- Database update process impact testing
- Circuit breaker behavior under high failure rates
- Cache performance under heavy load
- System stability under extreme conditions

Follows Culture Connect Backend testing standards with load testing benchmarks.
"""

import pytest
import asyncio
import time
import random
import statistics
from typing import Dict, Any, List, Tuple
from unittest.mock import patch, AsyncMock
from concurrent.futures import ThreadPoolExecutor
import psutil

from app.services.geolocation_service import get_geolocation_service
from app.core.circuit_breaker import get_geolocation_circuit_breaker
from app.core.geolocation_resilience import get_geolocation_resilience_manager
from tests.fixtures.geolocation_fixtures import (
    GeolocationTestData,
    GeolocationTestHelper,
    TEST_CONFIG
)


class TestGeolocationLoad:
    """Load tests for geolocation functionality."""
    
    @pytest.mark.asyncio
    async def test_high_concurrency_stress(self, mock_geolocation_service):
        """Test system under high concurrency stress."""
        concurrent_users = 200
        requests_per_user = 10
        total_requests = concurrent_users * requests_per_user
        
        ip_pool = [
            "*******", "*******", "************", "***********", 
            "************", "**************", "*******"
        ]
        
        # Track performance metrics
        start_time = time.perf_counter()
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        async def user_simulation(user_id: int):
            """Simulate a user making multiple requests."""
            nonlocal successful_requests, failed_requests
            user_response_times = []
            
            for request_num in range(requests_per_user):
                ip = random.choice(ip_pool)
                
                try:
                    request_start = time.perf_counter()
                    await mock_geolocation_service.detect_country_from_ip(ip)
                    request_time = (time.perf_counter() - request_start) * 1000
                    
                    user_response_times.append(request_time)
                    successful_requests += 1
                    
                    # Random delay between requests (0-100ms)
                    await asyncio.sleep(random.uniform(0, 0.1))
                    
                except Exception:
                    failed_requests += 1
            
            return user_response_times
        
        # Execute concurrent user simulations
        tasks = [user_simulation(i) for i in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect all response times
        for user_times in user_results:
            if isinstance(user_times, list):
                response_times.extend(user_times)
        
        total_time = time.perf_counter() - start_time
        
        # Calculate performance metrics
        success_rate = successful_requests / total_requests
        avg_response_time = statistics.mean(response_times) if response_times else 0
        p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0
        throughput = successful_requests / total_time
        
        # Validate stress test results
        assert success_rate >= 0.90, f"Success rate {success_rate:.2%} below 90% under stress"
        assert avg_response_time < 500, f"Average response time {avg_response_time:.2f}ms too high under stress"
        assert throughput >= 100, f"Throughput {throughput:.1f} RPS too low under stress"
        
        print(f"High concurrency stress: {success_rate:.2%} success, {avg_response_time:.2f}ms avg, "
              f"{p95_response_time:.2f}ms p95, {throughput:.1f} RPS")
    
    @pytest.mark.asyncio
    async def test_database_update_impact(self, mock_geolocation_service):
        """Test impact of database updates on ongoing operations."""
        # Simulate ongoing geolocation requests
        ip_addresses = ["*******", "*******", "************"]
        update_duration = 5  # Simulate 5-second database update
        
        performance_before = []
        performance_during = []
        performance_after = []
        
        async def continuous_requests(phase_name: str, duration: int, results_list: List[float]):
            """Generate continuous requests during a phase."""
            start_time = time.time()
            
            while time.time() - start_time < duration:
                ip = random.choice(ip_addresses)
                
                try:
                    request_start = time.perf_counter()
                    await mock_geolocation_service.detect_country_from_ip(ip)
                    request_time = (time.perf_counter() - request_start) * 1000
                    results_list.append(request_time)
                except Exception:
                    results_list.append(float('inf'))  # Mark failures
                
                await asyncio.sleep(0.1)  # 10 RPS
        
        # Phase 1: Normal operation (before update)
        await continuous_requests("before", 3, performance_before)
        
        # Phase 2: During database update (simulate degraded performance)
        with patch.object(mock_geolocation_service, 'detect_country_from_ip') as mock_detect:
            # Simulate slower responses during update
            async def slow_detect(ip_address: str):
                await asyncio.sleep(0.05)  # 50ms additional delay
                return GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
            
            mock_detect.side_effect = slow_detect
            await continuous_requests("during", update_duration, performance_during)
        
        # Phase 3: After update (should return to normal)
        await continuous_requests("after", 3, performance_after)
        
        # Analyze impact
        def calculate_stats(times: List[float]) -> Dict[str, float]:
            valid_times = [t for t in times if t != float('inf')]
            if not valid_times:
                return {"avg": 0, "p95": 0, "success_rate": 0}
            
            return {
                "avg": statistics.mean(valid_times),
                "p95": statistics.quantiles(valid_times, n=20)[18] if len(valid_times) >= 20 else max(valid_times),
                "success_rate": len(valid_times) / len(times)
            }
        
        before_stats = calculate_stats(performance_before)
        during_stats = calculate_stats(performance_during)
        after_stats = calculate_stats(performance_after)
        
        # Validate database update impact
        assert during_stats["success_rate"] >= 0.80, "Success rate during update too low"
        assert after_stats["avg"] <= before_stats["avg"] * 1.2, "Performance didn't recover after update"
        
        print(f"Database update impact: Before={before_stats['avg']:.2f}ms, "
              f"During={during_stats['avg']:.2f}ms, After={after_stats['avg']:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_under_load(self, test_circuit_breaker, mock_geolocation_service):
        """Test circuit breaker behavior under high failure rates."""
        failure_rate = 0.7  # 70% failure rate
        request_count = 100
        
        call_count = 0
        circuit_states = []
        response_times = []
        
        async def failing_detect_country(ip_address: str):
            nonlocal call_count
            call_count += 1
            
            # Simulate 70% failure rate
            if random.random() < failure_rate:
                await asyncio.sleep(0.1)  # Slow failure
                raise Exception("Simulated database failure")
            
            return GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
        
        mock_geolocation_service.detect_country_from_ip.side_effect = failing_detect_country
        
        # Generate load with high failure rate
        for i in range(request_count):
            try:
                start_time = time.perf_counter()
                await test_circuit_breaker.detect_country_with_protection(
                    mock_geolocation_service, f"8.8.8.{i % 255}"
                )
                response_time = (time.perf_counter() - start_time) * 1000
                response_times.append(response_time)
            except Exception:
                response_time = (time.perf_counter() - start_time) * 1000
                response_times.append(response_time)
            
            # Record circuit breaker state
            stats = test_circuit_breaker.get_stats()
            circuit_states.append(stats["state"])
            
            await asyncio.sleep(0.01)  # Small delay between requests
        
        # Analyze circuit breaker behavior
        state_changes = len(set(circuit_states))
        final_state = circuit_states[-1]
        avg_response_time = statistics.mean(response_times)
        
        # Validate circuit breaker behavior under load
        assert state_changes >= 2, "Circuit breaker should change states under high failure rate"
        assert final_state == "open", "Circuit breaker should be open after high failure rate"
        assert avg_response_time < 200, f"Average response time {avg_response_time:.2f}ms too high"
        
        print(f"Circuit breaker under load: {state_changes} state changes, final state={final_state}, "
              f"avg response={avg_response_time:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_cache_performance_under_load(self, redis_test_client, mock_geolocation_service):
        """Test cache performance under heavy load."""
        cache_size = 1000  # 1000 unique IPs
        request_multiplier = 5  # Each IP requested 5 times
        total_requests = cache_size * request_multiplier
        
        # Generate IP addresses for testing
        ip_addresses = [f"192.0.2.{i % 255}" for i in range(cache_size)]
        
        cache_hits = 0
        cache_misses = 0
        response_times = []
        
        # Simulate cache behavior
        cache_data = {}
        
        async def cached_detect_country(ip_address: str):
            nonlocal cache_hits, cache_misses
            
            # Check cache
            if ip_address in cache_data:
                cache_hits += 1
                await asyncio.sleep(0.001)  # 1ms cache hit time
                return cache_data[ip_address]
            else:
                cache_misses += 1
                await asyncio.sleep(0.05)  # 50ms database lookup time
                result = GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
                cache_data[ip_address] = result
                return result
        
        mock_geolocation_service.detect_country_from_ip.side_effect = cached_detect_country
        
        # Generate mixed cache hit/miss load
        requests = []
        for _ in range(request_multiplier):
            requests.extend(random.sample(ip_addresses, len(ip_addresses)))
        
        random.shuffle(requests)
        
        # Execute requests
        start_time = time.perf_counter()
        
        for ip in requests:
            request_start = time.perf_counter()
            await mock_geolocation_service.detect_country_from_ip(ip)
            response_time = (time.perf_counter() - request_start) * 1000
            response_times.append(response_time)
        
        total_time = time.perf_counter() - start_time
        
        # Calculate cache performance metrics
        cache_hit_rate = cache_hits / total_requests
        avg_response_time = statistics.mean(response_times)
        throughput = total_requests / total_time
        
        # Validate cache performance under load
        assert cache_hit_rate >= 0.75, f"Cache hit rate {cache_hit_rate:.2%} below 75% under load"
        assert avg_response_time < 30, f"Average response time {avg_response_time:.2f}ms too high with cache"
        assert throughput >= 200, f"Throughput {throughput:.1f} RPS too low with cache"
        
        print(f"Cache under load: {cache_hit_rate:.2%} hit rate, {avg_response_time:.2f}ms avg, "
              f"{throughput:.1f} RPS")
    
    @pytest.mark.asyncio
    async def test_memory_pressure_load(self, mock_geolocation_service):
        """Test system behavior under memory pressure."""
        large_request_count = 5000
        ip_pool = [f"10.0.{i//255}.{i%255}" for i in range(1000)]  # 1000 unique IPs
        
        # Monitor memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_samples = []
        
        successful_requests = 0
        failed_requests = 0
        
        # Generate large number of requests
        for i in range(large_request_count):
            ip = random.choice(ip_pool)
            
            try:
                await mock_geolocation_service.detect_country_from_ip(ip)
                successful_requests += 1
            except Exception:
                failed_requests += 1
            
            # Sample memory usage every 100 requests
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
        
        final_memory = process.memory_info().rss / 1024 / 1024
        max_memory = max(memory_samples) if memory_samples else final_memory
        memory_growth = final_memory - initial_memory
        
        # Calculate success rate
        success_rate = successful_requests / large_request_count
        
        # Validate memory behavior under load
        assert success_rate >= 0.95, f"Success rate {success_rate:.2%} too low under memory pressure"
        assert memory_growth < 200, f"Memory growth {memory_growth:.2f}MB too high"
        assert max_memory < initial_memory + 300, f"Peak memory {max_memory:.2f}MB too high"
        
        print(f"Memory pressure load: {success_rate:.2%} success, {memory_growth:.2f}MB growth, "
              f"{max_memory:.2f}MB peak")
    
    @pytest.mark.asyncio
    async def test_sustained_peak_load(self, mock_geolocation_service):
        """Test sustained peak load performance."""
        duration_minutes = 2  # 2-minute sustained test
        target_rps = 100  # 100 requests per second
        
        start_time = time.time()
        total_requests = 0
        successful_requests = 0
        response_times = []
        error_counts = {"timeout": 0, "failure": 0, "other": 0}
        
        ip_pool = ["*******", "*******", "************", "***********"]
        
        # Sustained load generation
        while time.time() - start_time < duration_minutes * 60:
            batch_start = time.time()
            
            # Generate batch of requests
            tasks = []
            for i in range(target_rps):
                ip = random.choice(ip_pool)
                task = asyncio.create_task(mock_geolocation_service.detect_country_from_ip(ip))
                tasks.append(task)
            
            # Execute batch
            batch_response_start = time.perf_counter()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            batch_response_time = (time.perf_counter() - batch_response_start) * 1000
            
            response_times.append(batch_response_time)
            total_requests += len(tasks)
            
            # Analyze results
            for result in results:
                if isinstance(result, Exception):
                    error_type = type(result).__name__.lower()
                    if "timeout" in error_type:
                        error_counts["timeout"] += 1
                    elif "failure" in error_type:
                        error_counts["failure"] += 1
                    else:
                        error_counts["other"] += 1
                else:
                    successful_requests += 1
            
            # Wait for next second
            elapsed = time.time() - batch_start
            if elapsed < 1.0:
                await asyncio.sleep(1.0 - elapsed)
        
        # Calculate sustained performance metrics
        actual_duration = time.time() - start_time
        actual_rps = total_requests / actual_duration
        success_rate = successful_requests / total_requests
        avg_batch_response = statistics.mean(response_times)
        total_errors = sum(error_counts.values())
        
        # Validate sustained peak performance
        assert actual_rps >= target_rps * 0.9, f"Sustained RPS {actual_rps:.1f} below target {target_rps}"
        assert success_rate >= 0.95, f"Success rate {success_rate:.2%} too low during sustained load"
        assert avg_batch_response < 2000, f"Average batch response {avg_batch_response:.2f}ms too high"
        
        print(f"Sustained peak load: {actual_rps:.1f} RPS, {success_rate:.2%} success, "
              f"{avg_batch_response:.2f}ms batch response, {total_errors} total errors")
    
    @pytest.mark.asyncio
    async def test_burst_load_handling(self, mock_geolocation_service):
        """Test handling of sudden burst loads."""
        normal_rps = 20
        burst_rps = 200
        burst_duration = 10  # 10 seconds
        
        performance_metrics = {"normal": [], "burst": [], "recovery": []}
        
        async def load_phase(phase_name: str, rps: int, duration: int):
            """Generate load for a specific phase."""
            phase_start = time.time()
            phase_requests = 0
            phase_successes = 0
            
            while time.time() - phase_start < duration:
                batch_start = time.time()
                
                # Generate requests for this second
                tasks = []
                for i in range(rps):
                    ip = f"8.8.8.{(phase_requests + i) % 255}"
                    task = asyncio.create_task(mock_geolocation_service.detect_country_from_ip(ip))
                    tasks.append(task)
                
                # Measure batch performance
                batch_perf_start = time.perf_counter()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                batch_time = (time.perf_counter() - batch_perf_start) * 1000
                
                phase_requests += len(tasks)
                phase_successes += sum(1 for r in results if not isinstance(r, Exception))
                
                performance_metrics[phase_name].append({
                    "batch_time_ms": batch_time,
                    "success_rate": phase_successes / phase_requests,
                    "rps": len(tasks)
                })
                
                # Wait for next second
                elapsed = time.time() - batch_start
                if elapsed < 1.0:
                    await asyncio.sleep(1.0 - elapsed)
        
        # Execute load phases
        await load_phase("normal", normal_rps, 5)  # 5 seconds normal
        await load_phase("burst", burst_rps, burst_duration)  # 10 seconds burst
        await load_phase("recovery", normal_rps, 5)  # 5 seconds recovery
        
        # Analyze burst handling
        normal_avg_time = statistics.mean([m["batch_time_ms"] for m in performance_metrics["normal"]])
        burst_avg_time = statistics.mean([m["batch_time_ms"] for m in performance_metrics["burst"]])
        recovery_avg_time = statistics.mean([m["batch_time_ms"] for m in performance_metrics["recovery"]])
        
        burst_success_rate = statistics.mean([m["success_rate"] for m in performance_metrics["burst"]])
        
        # Validate burst handling
        assert burst_success_rate >= 0.90, f"Burst success rate {burst_success_rate:.2%} too low"
        assert burst_avg_time < normal_avg_time * 3, f"Burst response time {burst_avg_time:.2f}ms too high"
        assert recovery_avg_time <= normal_avg_time * 1.1, "System didn't recover after burst"
        
        print(f"Burst load handling: Normal={normal_avg_time:.2f}ms, Burst={burst_avg_time:.2f}ms, "
              f"Recovery={recovery_avg_time:.2f}ms, Burst success={burst_success_rate:.2%}")
