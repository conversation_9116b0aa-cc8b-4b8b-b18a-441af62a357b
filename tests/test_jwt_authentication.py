"""
Comprehensive test suite for JWT authentication implementation.

This module tests Task 2.1.1 requirements including:
- JWT token generation and validation with role-based claims
- Refresh token mechanism with rotation
- Token blacklisting for secure logout
- Security event logging and audit trails
- Session management and tracking

Tests ensure >80% coverage for all JWT authentication components.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON>Ex<PERSON>, status
from jose import jwt

from app.core.security import (
    create_access_token, create_refresh_token, verify_token, create_token_pair,
    refresh_token_pair, logout_user, token_blacklist, TokenData, Token,
    TokenBlacklistManager
)
from app.core.config import settings
from app.models.user import User, TokenBlacklist, SecurityEvent, UserSession
from app.services.auth_service import AuthService
from app.repositories.auth_repository import (
    UserRepository, TokenBlacklistRepository, SecurityEventRepository, UserSessionRepository
)
from app.schemas.auth import UserLogin, UserCreate


class TestJWTTokenGeneration:
    """Test JWT token generation with role-based claims."""

    def test_create_access_token_basic(self):
        """Test basic access token creation."""
        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "customer"
        }

        token = create_access_token(user_data)

        # Verify token structure
        assert isinstance(token, str)
        assert len(token.split('.')) == 3  # JWT has 3 parts

        # Decode without verification to check claims
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        assert decoded["sub"] == "123"
        assert decoded["email"] == "<EMAIL>"
        assert decoded["role"] == "customer"
        assert decoded["type"] == "access"
        assert "jti" in decoded
        assert "iat" in decoded
        assert "exp" in decoded
        assert "iss" in decoded
        assert "aud" in decoded

    def test_create_access_token_with_custom_expiry(self):
        """Test access token creation with custom expiration."""
        user_data = {"sub": "123", "email": "<EMAIL>"}
        custom_expiry = timedelta(minutes=30)

        token = create_access_token(user_data, expires_delta=custom_expiry)

        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )

        # Check that token has expiration and it's a reasonable timestamp
        assert "exp" in decoded
        assert isinstance(decoded["exp"], int)

        # Check that expiration is in the future (basic sanity check)
        import time
        current_timestamp = int(time.time())
        assert decoded["exp"] > current_timestamp

        # Check that expiration is not too far in the future (should be around 30 minutes)
        # Allow for some flexibility in timing (between 25-35 minutes)
        expected_min = current_timestamp + (25 * 60)  # 25 minutes
        expected_max = current_timestamp + (35 * 60)  # 35 minutes
        assert expected_min <= decoded["exp"] <= expected_max

    def test_create_refresh_token_basic(self):
        """Test basic refresh token creation."""
        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "vendor"
        }

        token = create_refresh_token(user_data)

        # Verify token structure
        assert isinstance(token, str)
        assert len(token.split('.')) == 3

        # Decode without verification to check claims
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        assert decoded["sub"] == "123"
        assert decoded["email"] == "<EMAIL>"
        assert decoded["role"] == "vendor"
        assert decoded["type"] == "refresh"
        assert "jti" in decoded

    def test_create_token_pair(self):
        """Test creation of access and refresh token pair."""
        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "admin",
            "scopes": ["admin", "vendor", "customer"]
        }

        token_pair = create_token_pair(user_data)

        # Verify token pair structure
        assert isinstance(token_pair, Token)
        assert token_pair.access_token
        assert token_pair.refresh_token
        assert token_pair.token_type == "bearer"
        assert token_pair.expires_in > 0
        assert token_pair.refresh_expires_in > 0
        assert token_pair.jti
        assert token_pair.scope == "admin vendor customer"

    def test_token_jti_uniqueness(self):
        """Test that each token gets a unique JTI."""
        user_data = {"sub": "123", "email": "<EMAIL>"}

        token1 = create_access_token(user_data)
        token2 = create_access_token(user_data)

        decoded1 = jwt.decode(
            token1,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        decoded2 = jwt.decode(
            token2,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )

        assert decoded1["jti"] != decoded2["jti"]


class TestJWTTokenValidation:
    """Test JWT token validation with blacklist checking."""

    @pytest.mark.asyncio
    async def test_verify_valid_access_token(self):
        """Test verification of valid access token."""
        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "customer",
            "scopes": ["customer"]
        }

        token = create_access_token(user_data)
        token_data = await verify_token(token, "access")

        assert token_data is not None
        assert token_data.user_id == 123
        assert token_data.email == "<EMAIL>"
        assert token_data.role == "customer"
        assert token_data.scopes == ["customer"]
        assert token_data.token_type == "access"
        assert token_data.jti is not None

    @pytest.mark.asyncio
    async def test_verify_valid_refresh_token(self):
        """Test verification of valid refresh token."""
        user_data = {
            "sub": "456",
            "email": "<EMAIL>",
            "role": "vendor"
        }

        token = create_refresh_token(user_data)
        token_data = await verify_token(token, "refresh")

        assert token_data is not None
        assert token_data.user_id == 456
        assert token_data.email == "<EMAIL>"
        assert token_data.role == "vendor"
        assert token_data.token_type == "refresh"

    @pytest.mark.asyncio
    async def test_verify_expired_token(self):
        """Test verification of expired token."""
        user_data = {"sub": "123", "email": "<EMAIL>"}

        # Create token with very short expiry
        expired_token = create_access_token(user_data, timedelta(seconds=-1))

        with pytest.raises(HTTPException) as exc_info:
            await verify_token(expired_token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_verify_invalid_token_type(self):
        """Test verification with wrong token type."""
        user_data = {"sub": "123", "email": "<EMAIL>"}

        access_token = create_access_token(user_data)

        with pytest.raises(HTTPException) as exc_info:
            await verify_token(access_token, "refresh")  # Wrong type

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_verify_malformed_token(self):
        """Test verification of malformed token."""
        malformed_token = "invalid.token.here"

        with pytest.raises(HTTPException) as exc_info:
            await verify_token(malformed_token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    @patch('app.core.security.token_blacklist.is_blacklisted')
    async def test_verify_blacklisted_token(self, mock_is_blacklisted):
        """Test verification of blacklisted token."""
        mock_is_blacklisted.return_value = True

        user_data = {"sub": "123", "email": "<EMAIL>"}
        token = create_access_token(user_data)

        with pytest.raises(HTTPException) as exc_info:
            await verify_token(token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED


class TestTokenBlacklistManager:
    """Test token blacklist manager functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.blacklist_manager = TokenBlacklistManager()

    @pytest.mark.asyncio
    async def test_blacklist_token_success(self):
        """Test successful token blacklisting."""
        user_data = {"sub": "123", "email": "<EMAIL>"}
        token = create_access_token(user_data)

        # Extract expiration for blacklisting
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        expires_at = datetime.fromtimestamp(decoded["exp"])

        success = await self.blacklist_manager.blacklist_token(token, expires_at)
        assert success is True

    @pytest.mark.asyncio
    async def test_blacklist_token_without_jti(self):
        """Test blacklisting token without JTI claim."""
        # Create a token without JTI (manually)
        payload = {
            "sub": "123",
            "exp": datetime.utcnow() + timedelta(minutes=15)
        }
        token_without_jti = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

        success = await self.blacklist_manager.blacklist_token(token_without_jti)
        assert success is False

    @pytest.mark.asyncio
    async def test_is_blacklisted_check(self):
        """Test checking if token is blacklisted."""
        user_data = {"sub": "123", "email": "<EMAIL>"}
        token = create_access_token(user_data)

        # Initially not blacklisted
        is_blacklisted = await self.blacklist_manager.is_blacklisted(token)
        assert is_blacklisted is False

        # Blacklist the token
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        expires_at = datetime.fromtimestamp(decoded["exp"])
        await self.blacklist_manager.blacklist_token(token, expires_at)

        # Now should be blacklisted
        is_blacklisted = await self.blacklist_manager.is_blacklisted(token)
        assert is_blacklisted is True

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self):
        """Test cleanup of expired tokens."""
        # For in-memory storage, this should return 0
        # In production with Redis, this would be handled automatically
        cleaned_count = await self.blacklist_manager.cleanup_expired_tokens()
        assert isinstance(cleaned_count, int)
        assert cleaned_count >= 0


class TestRefreshTokenMechanism:
    """Test refresh token mechanism with rotation."""

    @pytest.mark.asyncio
    @patch('app.core.security.token_blacklist.blacklist_token')
    async def test_refresh_token_pair_success(self, mock_blacklist):
        """Test successful token pair refresh."""
        mock_blacklist.return_value = True

        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "customer",
            "scopes": ["customer"]
        }

        # Create initial refresh token
        refresh_token = create_refresh_token(user_data)

        # Refresh the token pair
        new_tokens = await refresh_token_pair(refresh_token)

        assert isinstance(new_tokens, Token)
        assert new_tokens.access_token
        assert new_tokens.refresh_token
        assert new_tokens.access_token != refresh_token  # Should be different

        # Verify old token was blacklisted
        mock_blacklist.assert_called_once()

    @pytest.mark.asyncio
    async def test_refresh_with_invalid_token(self):
        """Test refresh with invalid refresh token."""
        invalid_token = "invalid.refresh.token"

        with pytest.raises(HTTPException) as exc_info:
            await refresh_token_pair(invalid_token)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_refresh_with_access_token(self):
        """Test refresh using access token instead of refresh token."""
        user_data = {"sub": "123", "email": "<EMAIL>"}
        access_token = create_access_token(user_data)

        with pytest.raises(HTTPException) as exc_info:
            await refresh_token_pair(access_token)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    @patch('app.core.security.token_blacklist.is_blacklisted')
    async def test_refresh_with_blacklisted_token(self, mock_is_blacklisted):
        """Test refresh with blacklisted refresh token."""
        mock_is_blacklisted.return_value = True

        user_data = {"sub": "123", "email": "<EMAIL>"}
        refresh_token = create_refresh_token(user_data)

        with pytest.raises(HTTPException) as exc_info:
            await refresh_token_pair(refresh_token)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED


class TestLogoutFunctionality:
    """Test logout functionality with token blacklisting."""

    @pytest.mark.asyncio
    @patch('app.core.security.token_blacklist.blacklist_token')
    async def test_logout_with_both_tokens(self, mock_blacklist):
        """Test logout with both access and refresh tokens."""
        mock_blacklist.return_value = True

        user_data = {"sub": "123", "email": "<EMAIL>"}
        access_token = create_access_token(user_data)
        refresh_token = create_refresh_token(user_data)

        success = await logout_user(access_token, refresh_token)

        assert success is True
        assert mock_blacklist.call_count == 2  # Both tokens blacklisted

    @pytest.mark.asyncio
    @patch('app.core.security.token_blacklist.blacklist_token')
    async def test_logout_with_access_token_only(self, mock_blacklist):
        """Test logout with only access token."""
        mock_blacklist.return_value = True

        user_data = {"sub": "123", "email": "<EMAIL>"}
        access_token = create_access_token(user_data)

        success = await logout_user(access_token)

        assert success is True
        assert mock_blacklist.call_count == 1  # Only access token blacklisted

    @pytest.mark.asyncio
    async def test_logout_with_invalid_token(self):
        """Test logout with invalid access token."""
        invalid_token = "invalid.access.token"

        success = await logout_user(invalid_token)

        assert success is False


class TestAuthServiceJWTMethods:
    """Test AuthService JWT-related methods."""

    def setup_method(self):
        """Set up test environment."""
        self.mock_db = AsyncMock()
        self.auth_service = AuthService(self.mock_db)

    @pytest.mark.asyncio
    @patch('app.services.auth_service.refresh_token_pair')
    @patch('app.services.auth_service.verify_token')
    async def test_auth_service_refresh_tokens(self, mock_verify, mock_refresh):
        """Test AuthService refresh_tokens method."""
        # Mock token data
        mock_token_data = TokenData(
            user_id=123,
            email="<EMAIL>",
            role="customer",
            jti="test-jti"
        )
        mock_verify.return_value = mock_token_data

        # Mock new tokens
        mock_new_tokens = Token(
            access_token="new_access_token",
            refresh_token="new_refresh_token",
            expires_in=3600,
            refresh_expires_in=604800,
            jti="new-jti"
        )
        mock_refresh.return_value = mock_new_tokens

        # Mock security event logging
        self.auth_service.security_event_repo.log_event = AsyncMock()

        refresh_token = "valid_refresh_token"
        result = await self.auth_service.refresh_tokens(refresh_token)

        assert result == mock_new_tokens
        mock_refresh.assert_called_once_with(refresh_token)
        self.auth_service.security_event_repo.log_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.services.auth_service.verify_token')
    async def test_auth_service_logout_user_tokens(self, mock_verify):
        """Test AuthService logout_user_tokens method."""
        # Mock token data
        mock_token_data = TokenData(
            user_id=123,
            email="<EMAIL>",
            role="customer",
            jti="test-jti",
            exp=datetime.utcnow() + timedelta(hours=1)
        )
        mock_verify.return_value = mock_token_data

        # Mock repository methods
        self.auth_service.token_blacklist_repo.blacklist_token = AsyncMock()
        self.auth_service.security_event_repo.log_event = AsyncMock()

        access_token = "valid_access_token"
        refresh_token = "valid_refresh_token"

        result = await self.auth_service.logout_user_tokens(
            access_token,
            refresh_token,
            ip_address="***********",
            user_agent="Test Agent"
        )

        assert result is True
        assert self.auth_service.token_blacklist_repo.blacklist_token.call_count == 2
        self.auth_service.security_event_repo.log_event.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.services.auth_service.verify_token')
    async def test_auth_service_verify_user_token(self, mock_verify):
        """Test AuthService verify_user_token method."""
        # Mock token data
        mock_token_data = TokenData(
            user_id=123,
            email="<EMAIL>",
            role="customer"
        )
        mock_verify.return_value = mock_token_data

        token = "valid_token"
        result = await self.auth_service.verify_user_token(token, "access")

        assert result == mock_token_data
        mock_verify.assert_called_once_with(token, "access")

    @pytest.mark.asyncio
    @patch('app.services.auth_service.verify_token')
    async def test_auth_service_verify_invalid_token(self, mock_verify):
        """Test AuthService verify_user_token with invalid token."""
        mock_verify.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

        token = "invalid_token"
        result = await self.auth_service.verify_user_token(token, "access")

        assert result is None


class TestTokenBlacklistRepository:
    """Test TokenBlacklistRepository functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.mock_db = AsyncMock()
        self.repo = TokenBlacklistRepository(TokenBlacklist, self.mock_db)

    @pytest.mark.asyncio
    async def test_blacklist_token_success(self):
        """Test successful token blacklisting in repository."""
        jti = "test-jti-123"
        user_id = 123
        token_type = "access"
        expires_at = datetime.utcnow() + timedelta(hours=1)

        # Mock database operations
        self.mock_db.add = MagicMock()
        self.mock_db.commit = AsyncMock()

        result = await self.repo.blacklist_token(
            jti=jti,
            user_id=user_id,
            token_type=token_type,
            expires_at=expires_at,
            reason="logout",
            ip_address="***********"
        )

        assert isinstance(result, TokenBlacklist)
        assert result.jti == jti
        assert result.user_id == user_id
        assert result.token_type == token_type
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_is_token_blacklisted_true(self):
        """Test checking blacklisted token returns True."""
        jti = "blacklisted-jti"

        # Mock database query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = TokenBlacklist(jti=jti)
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        is_blacklisted = await self.repo.is_token_blacklisted(jti)

        assert is_blacklisted is True
        self.mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_is_token_blacklisted_false(self):
        """Test checking non-blacklisted token returns False."""
        jti = "valid-jti"

        # Mock database query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        is_blacklisted = await self.repo.is_token_blacklisted(jti)

        assert is_blacklisted is False
        self.mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self):
        """Test cleanup of expired tokens."""
        # Mock expired tokens
        expired_token1 = TokenBlacklist(jti="expired1", expires_at=datetime.utcnow() - timedelta(hours=1))
        expired_token2 = TokenBlacklist(jti="expired2", expires_at=datetime.utcnow() - timedelta(hours=2))

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [expired_token1, expired_token2]
        self.mock_db.execute = AsyncMock(return_value=mock_result)
        self.mock_db.delete = AsyncMock()
        self.mock_db.commit = AsyncMock()

        cleaned_count = await self.repo.cleanup_expired_tokens()

        assert cleaned_count == 2
        assert self.mock_db.delete.call_count == 2
        self.mock_db.commit.assert_called_once()


class TestSecurityEventRepository:
    """Test SecurityEventRepository functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.mock_db = AsyncMock()
        self.repo = SecurityEventRepository(SecurityEvent, self.mock_db)

    @pytest.mark.asyncio
    async def test_log_event_success(self):
        """Test successful security event logging."""
        event_type = "user_login"
        description = "User logged in successfully"
        user_id = 123

        # Mock database operations
        self.mock_db.add = MagicMock()
        self.mock_db.commit = AsyncMock()

        result = await self.repo.log_event(
            event_type=event_type,
            description=description,
            user_id=user_id,
            event_category="authentication",
            severity="info",
            ip_address="***********",
            user_agent="Test Agent"
        )

        assert isinstance(result, SecurityEvent)
        assert result.event_type == event_type
        assert result.description == description
        assert result.user_id == user_id
        assert result.event_category == "authentication"
        assert result.severity == "info"
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_events(self):
        """Test getting security events for a user."""
        user_id = 123

        # Mock events
        event1 = SecurityEvent(event_type="login", user_id=user_id)
        event2 = SecurityEvent(event_type="logout", user_id=user_id)

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [event1, event2]
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        events = await self.repo.get_user_events(user_id, limit=10)

        assert len(events) == 2
        assert all(event.user_id == user_id for event in events)
        self.mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_unresolved_events(self):
        """Test getting unresolved security events."""
        # Mock unresolved events
        event1 = SecurityEvent(event_type="failed_login", resolved=False, severity="warning")
        event2 = SecurityEvent(event_type="suspicious_activity", resolved=False, severity="error")

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [event1, event2]
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        events = await self.repo.get_unresolved_events(severity="warning")

        assert len(events) == 2
        assert all(not event.resolved for event in events)
        self.mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_event_success(self):
        """Test successful event resolution."""
        event_id = 1
        resolved_by_user_id = 456

        # Mock event
        mock_event = SecurityEvent(id=event_id, resolved=False)
        mock_event.resolve = MagicMock()

        # Mock repository get method
        self.repo.get = AsyncMock(return_value=mock_event)
        self.mock_db.commit = AsyncMock()

        success = await self.repo.resolve_event(event_id, resolved_by_user_id)

        assert success is True
        mock_event.resolve.assert_called_once_with(resolved_by_user_id)
        self.mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_event_not_found(self):
        """Test event resolution when event not found."""
        event_id = 999
        resolved_by_user_id = 456

        # Mock repository get method returning None
        self.repo.get = AsyncMock(return_value=None)

        success = await self.repo.resolve_event(event_id, resolved_by_user_id)

        assert success is False


class TestUserSessionRepository:
    """Test UserSessionRepository functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.mock_db = AsyncMock()
        self.repo = UserSessionRepository(UserSession, self.mock_db)

    @pytest.mark.asyncio
    async def test_create_session_success(self):
        """Test successful session creation."""
        user_id = 123
        session_id = "session-123"
        ip_address = "***********"
        user_agent = "Test Agent"

        # Mock database operations
        self.mock_db.add = MagicMock()
        self.mock_db.commit = AsyncMock()

        result = await self.repo.create_session(
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent
        )

        assert isinstance(result, UserSession)
        assert result.user_id == user_id
        assert result.session_id == session_id
        assert result.ip_address == ip_address
        assert result.user_agent == user_agent
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_sessions(self):
        """Test getting active sessions for a user."""
        user_id = 123

        # Mock active sessions
        session1 = UserSession(user_id=user_id, session_id="session1", is_active=True)
        session2 = UserSession(user_id=user_id, session_id="session2", is_active=True)

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [session1, session2]
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        sessions = await self.repo.get_active_sessions(user_id)

        assert len(sessions) == 2
        assert all(session.user_id == user_id for session in sessions)
        assert all(session.is_active for session in sessions)
        self.mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_deactivate_session_success(self):
        """Test successful session deactivation."""
        session_id = "session-123"

        # Mock session
        mock_session = UserSession(session_id=session_id, is_active=True)
        mock_session.deactivate = MagicMock()

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_session
        self.mock_db.execute = AsyncMock(return_value=mock_result)
        self.mock_db.commit = AsyncMock()

        success = await self.repo.deactivate_session(session_id)

        assert success is True
        mock_session.deactivate.assert_called_once()
        self.mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_deactivate_session_not_found(self):
        """Test session deactivation when session not found."""
        session_id = "nonexistent-session"

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        self.mock_db.execute = AsyncMock(return_value=mock_result)

        success = await self.repo.deactivate_session(session_id)

        assert success is False


class TestRoleBasedClaims:
    """Test role-based claims in JWT tokens."""

    def test_admin_role_claims(self):
        """Test JWT token with admin role claims."""
        user_data = {
            "sub": "1",
            "email": "<EMAIL>",
            "role": "admin",
            "scopes": ["admin", "vendor", "customer"],
            "vendor_id": None
        }

        token_pair = create_token_pair(user_data)

        # Decode access token to verify claims
        decoded = jwt.decode(
            token_pair.access_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        assert decoded["role"] == "admin"
        assert decoded["scopes"] == ["admin", "vendor", "customer"]
        assert token_pair.scope == "admin vendor customer"

    def test_vendor_role_claims(self):
        """Test JWT token with vendor role claims."""
        user_data = {
            "sub": "2",
            "email": "<EMAIL>",
            "role": "vendor",
            "scopes": ["vendor", "customer"],
            "vendor_id": 123
        }

        token_pair = create_token_pair(user_data)

        # Decode access token to verify claims
        decoded = jwt.decode(
            token_pair.access_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        assert decoded["role"] == "vendor"
        assert decoded["vendor_id"] == 123
        assert decoded["scopes"] == ["vendor", "customer"]

    def test_customer_role_claims(self):
        """Test JWT token with customer role claims."""
        user_data = {
            "sub": "3",
            "email": "<EMAIL>",
            "role": "customer",
            "scopes": ["customer"]
        }

        token_pair = create_token_pair(user_data)

        # Decode access token to verify claims
        decoded = jwt.decode(
            token_pair.access_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_signature": False, "verify_exp": False, "verify_aud": False, "verify_iss": False}
        )
        assert decoded["role"] == "customer"
        assert decoded["scopes"] == ["customer"]
        assert token_pair.scope == "customer"


# Integration test to verify >80% coverage requirement
class TestJWTAuthenticationIntegration:
    """Integration tests for complete JWT authentication flow."""

    @pytest.mark.asyncio
    async def test_complete_authentication_flow(self):
        """Test complete authentication flow from login to logout."""
        # 1. Create token pair (simulating login)
        user_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "role": "customer",
            "scopes": ["customer"]
        }

        tokens = create_token_pair(user_data)
        assert tokens.access_token
        assert tokens.refresh_token

        # 2. Verify access token
        token_data = await verify_token(tokens.access_token, "access")
        assert token_data.user_id == 123
        assert token_data.email == "<EMAIL>"

        # 3. Refresh tokens
        with patch('app.core.security.token_blacklist.blacklist_token') as mock_blacklist:
            mock_blacklist.return_value = True
            new_tokens = await refresh_token_pair(tokens.refresh_token)
            assert new_tokens.access_token != tokens.access_token
            mock_blacklist.assert_called_once()

        # 4. Logout (blacklist tokens)
        with patch('app.core.security.token_blacklist.blacklist_token') as mock_blacklist:
            mock_blacklist.return_value = True
            success = await logout_user(new_tokens.access_token, new_tokens.refresh_token)
            assert success is True
            assert mock_blacklist.call_count == 2

    @pytest.mark.asyncio
    async def test_security_event_logging_flow(self):
        """Test security event logging throughout authentication flow."""
        mock_db = AsyncMock()
        security_repo = SecurityEventRepository(SecurityEvent, mock_db)

        # Mock database operations
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()

        # Log various security events
        await security_repo.log_event("user_login", "User logged in", user_id=123)
        await security_repo.log_event("token_refresh", "Tokens refreshed", user_id=123)
        await security_repo.log_event("user_logout", "User logged out", user_id=123)

        assert mock_db.add.call_count == 3
        assert mock_db.commit.call_count == 3
