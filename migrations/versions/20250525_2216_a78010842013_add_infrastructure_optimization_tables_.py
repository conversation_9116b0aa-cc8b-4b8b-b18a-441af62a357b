"""Add infrastructure optimization tables for Task 3.2.3

Revision ID: a78010842013
Revises: 20250524_0200
Create Date: 2025-05-25 22:16:01.161860+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a78010842013'
down_revision = '20250524_0200'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('role_hierarchies',
    sa.Column('parent_role', sa.String(length=20), nullable=False),
    sa.Column('child_role', sa.String(length=20), nullable=False),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_role_hierarchies'))
    )
    with op.batch_alter_table('role_hierarchies', schema=None) as batch_op:
        batch_op.create_index('idx_role_hierarchy_child', ['child_role', 'is_active'], unique=False)
        batch_op.create_index('idx_role_hierarchy_level', ['level', 'is_active'], unique=False)
        batch_op.create_index('idx_role_hierarchy_parent', ['parent_role', 'is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_child_role'), ['child_role'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_parent_role'), ['parent_role'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_role_hierarchies_uuid'), ['uuid'], unique=True)

    op.create_table('access_control_logs',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('user_role', sa.String(length=20), nullable=True),
    sa.Column('endpoint', sa.String(length=255), nullable=False),
    sa.Column('method', sa.String(length=10), nullable=False),
    sa.Column('required_permission', sa.String(length=50), nullable=True),
    sa.Column('resource_type', sa.String(length=20), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('decision', sa.Enum('GRANTED', 'DENIED', 'ERROR', name='accessdecision'), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('correlation_id', sa.String(length=36), nullable=True),
    sa.Column('request_timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('response_time_ms', sa.Integer(), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('log_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_access_control_logs_user_id_users'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_access_control_logs'))
    )
    with op.batch_alter_table('access_control_logs', schema=None) as batch_op:
        batch_op.create_index('idx_access_log_correlation', ['correlation_id'], unique=False)
        batch_op.create_index('idx_access_log_decision_timestamp', ['decision', 'request_timestamp'], unique=False)
        batch_op.create_index('idx_access_log_endpoint_timestamp', ['endpoint', 'request_timestamp'], unique=False)
        batch_op.create_index('idx_access_log_ip', ['ip_address', 'request_timestamp'], unique=False)
        batch_op.create_index('idx_access_log_permission', ['required_permission', 'decision'], unique=False)
        batch_op.create_index('idx_access_log_user_timestamp', ['user_id', 'request_timestamp'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_decision'), ['decision'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_endpoint'), ['endpoint'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_ip_address'), ['ip_address'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_request_timestamp'), ['request_timestamp'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_required_permission'), ['required_permission'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_resource_id'), ['resource_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_resource_type'), ['resource_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_user_role'), ['user_role'], unique=False)
        batch_op.create_index(batch_op.f('ix_access_control_logs_uuid'), ['uuid'], unique=True)

    op.create_table('automated_validation_rules',
    sa.Column('rule_name', sa.String(length=255), nullable=False),
    sa.Column('rule_description', sa.Text(), nullable=True),
    sa.Column('document_types', sa.JSON(), nullable=False),
    sa.Column('vendor_types', sa.JSON(), nullable=True),
    sa.Column('validation_type', sa.String(length=50), nullable=False),
    sa.Column('rule_config', sa.JSON(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('success_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('total_executions', sa.Integer(), nullable=False),
    sa.Column('successful_executions', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('last_executed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.CheckConstraint('success_rate >= 0.0 AND success_rate <= 100.0', name=op.f('ck_automated_validation_rules_check_success_rate_range')),
    sa.CheckConstraint('successful_executions <= total_executions', name=op.f('ck_automated_validation_rules_check_execution_counts')),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_automated_validation_rules_created_by_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_automated_validation_rules'))
    )
    with op.batch_alter_table('automated_validation_rules', schema=None) as batch_op:
        batch_op.create_index('idx_rule_performance', ['success_rate', 'total_executions'], unique=False)
        batch_op.create_index('idx_rule_priority', ['priority', 'is_active'], unique=False)
        batch_op.create_index('idx_rule_type_active', ['validation_type', 'is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_last_executed_at'), ['last_executed_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_rule_name'), ['rule_name'], unique=True)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_automated_validation_rules_validation_type'), ['validation_type'], unique=False)

    op.create_table('permission_grants',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=True),
    sa.Column('permission', sa.String(length=50), nullable=False),
    sa.Column('resource_type', sa.String(length=20), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('granted_by', sa.Integer(), nullable=True),
    sa.Column('granted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('grant_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['granted_by'], ['users.id'], name=op.f('fk_permission_grants_granted_by_users'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_permission_grants_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_permission_grants'))
    )
    with op.batch_alter_table('permission_grants', schema=None) as batch_op:
        batch_op.create_index('idx_permission_grant_expires', ['expires_at', 'is_active'], unique=False)
        batch_op.create_index('idx_permission_grant_permission', ['permission', 'is_active'], unique=False)
        batch_op.create_index('idx_permission_grant_resource', ['resource_type', 'resource_id', 'is_active'], unique=False)
        batch_op.create_index('idx_permission_grant_role', ['role', 'is_active'], unique=False)
        batch_op.create_index('idx_permission_grant_user', ['user_id', 'is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_permission'), ['permission'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_resource_id'), ['resource_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_resource_type'), ['resource_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_role'), ['role'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_permission_grants_uuid'), ['uuid'], unique=True)

    op.create_table('document_verification_workflows',
    sa.Column('vendor_id', sa.Integer(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('workflow_reference', sa.String(length=50), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'RESUBMISSION_REQUIRED', 'ESCALATED', 'COMPLETED', name='verificationworkflowstatus'), nullable=False),
    sa.Column('priority', sa.Enum('LOW', 'NORMAL', 'HIGH', 'URGENT', name='verificationpriority'), nullable=False),
    sa.Column('assigned_to', sa.Integer(), nullable=True),
    sa.Column('assigned_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('reviewed_by', sa.Integer(), nullable=True),
    sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('review_notes', sa.Text(), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('escalation_reason', sa.Text(), nullable=True),
    sa.Column('automated_validation_status', sa.Enum('PENDING', 'PASSED', 'FAILED', 'ERROR', 'SKIPPED', name='automatedvalidationstatus'), nullable=False),
    sa.Column('automated_validation_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('automated_validation_results', sa.JSON(), nullable=True),
    sa.Column('workflow_data', sa.JSON(), nullable=True),
    sa.Column('requires_manual_review', sa.Boolean(), nullable=False),
    sa.Column('is_expedited', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.CheckConstraint('automated_validation_score >= 0.0 AND automated_validation_score <= 100.0', name=op.f('ck_document_verification_workflows_check_validation_score_range')),
    sa.ForeignKeyConstraint(['assigned_to'], ['users.id'], name=op.f('fk_document_verification_workflows_assigned_to_users')),
    sa.ForeignKeyConstraint(['document_id'], ['vendor_documents.id'], name=op.f('fk_document_verification_workflows_document_id_vendor_documents'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], name=op.f('fk_document_verification_workflows_reviewed_by_users')),
    sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], name=op.f('fk_document_verification_workflows_vendor_id_vendors'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_document_verification_workflows'))
    )
    with op.batch_alter_table('document_verification_workflows', schema=None) as batch_op:
        batch_op.create_index('idx_workflow_assigned', ['assigned_to', 'assigned_at'], unique=False)
        batch_op.create_index('idx_workflow_automation', ['automated_validation_status', 'requires_manual_review'], unique=False)
        batch_op.create_index('idx_workflow_due_date', ['due_date', 'status'], unique=False)
        batch_op.create_index('idx_workflow_status_priority', ['status', 'priority'], unique=False)
        batch_op.create_index('idx_workflow_vendor_status', ['vendor_id', 'status'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_assigned_to'), ['assigned_to'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_automated_validation_status'), ['automated_validation_status'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_completed_at'), ['completed_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_document_id'), ['document_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_due_date'), ['due_date'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_priority'), ['priority'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_reviewed_at'), ['reviewed_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_submitted_at'), ['submitted_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_vendor_id'), ['vendor_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_document_verification_workflows_workflow_reference'), ['workflow_reference'], unique=True)

    op.create_table('verification_history',
    sa.Column('workflow_id', sa.Integer(), nullable=False),
    sa.Column('action_type', sa.Enum('SUBMITTED', 'ASSIGNED', 'REVIEWED', 'APPROVED', 'REJECTED', 'RESUBMISSION_REQUESTED', 'ESCALATED', 'COMMENT_ADDED', 'STATUS_CHANGED', name='verificationactiontype'), nullable=False),
    sa.Column('performed_by', sa.Integer(), nullable=True),
    sa.Column('performed_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('action_description', sa.Text(), nullable=True),
    sa.Column('previous_status', sa.String(length=50), nullable=True),
    sa.Column('new_status', sa.String(length=50), nullable=True),
    sa.Column('action_data', sa.JSON(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['performed_by'], ['users.id'], name=op.f('fk_verification_history_performed_by_users')),
    sa.ForeignKeyConstraint(['workflow_id'], ['document_verification_workflows.id'], name=op.f('fk_verification_history_workflow_id_document_verification_workflows'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_verification_history'))
    )
    with op.batch_alter_table('verification_history', schema=None) as batch_op:
        batch_op.create_index('idx_history_action_time', ['action_type', 'performed_at'], unique=False)
        batch_op.create_index('idx_history_user_action', ['performed_by', 'performed_at'], unique=False)
        batch_op.create_index('idx_history_workflow_action', ['workflow_id', 'action_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_action_type'), ['action_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_performed_at'), ['performed_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_performed_by'), ['performed_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_verification_history_uuid'), ['uuid'], unique=True)
        batch_op.create_index(batch_op.f('ix_verification_history_workflow_id'), ['workflow_id'], unique=False)

    with op.batch_alter_table('account_lockouts', schema=None) as batch_op:
        batch_op.drop_constraint('uq_account_lockouts_user_id', type_='unique')
        batch_op.create_index(batch_op.f('ix_account_lockouts_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_locked_until'), ['locked_until'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_account_lockouts_user_id'), ['user_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_account_lockouts_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)

    with op.batch_alter_table('email_deliveries', schema=None) as batch_op:
        batch_op.add_column(sa.Column('body', sa.Text(), nullable=False))
        batch_op.add_column(sa.Column('html_body', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('priority', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.drop_index('idx_email_delivery_correlation_id')
        batch_op.drop_index('idx_email_delivery_created_at')
        batch_op.drop_index('idx_email_delivery_status')
        batch_op.drop_index('idx_email_delivery_template_id')
        batch_op.drop_index('idx_email_delivery_user_id')
        batch_op.create_index(batch_op.f('ix_email_deliveries_correlation_id'), ['correlation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_priority'), ['priority'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_recipient_email'), ['recipient_email'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_template_id'), ['template_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_deliveries_user_id'), ['user_id'], unique=False)

    with op.batch_alter_table('email_preferences', schema=None) as batch_op:
        batch_op.add_column(sa.Column('opted_out', sa.Boolean(), nullable=False))
        batch_op.add_column(sa.Column('opt_out_reason', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('opt_out_date', sa.DateTime(timezone=True), nullable=True))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.drop_constraint('uq_email_preferences_user_id', type_='unique')
        batch_op.create_index(batch_op.f('ix_email_preferences_user_id'), ['user_id'], unique=True)
        batch_op.drop_column('opted_out_at')

    with op.batch_alter_table('email_queue', schema=None) as batch_op:
        batch_op.add_column(sa.Column('retry_count', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('max_retries', sa.Integer(), nullable=False))
        batch_op.alter_column('scheduled_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.drop_index('idx_email_queue_attempts')
        batch_op.drop_index('idx_email_queue_priority')
        batch_op.drop_index('idx_email_queue_scheduled_at')
        batch_op.drop_index('idx_email_queue_status')
        batch_op.drop_index('idx_email_queue_template_id')
        batch_op.drop_index('idx_email_queue_user_id')
        batch_op.create_index('idx_email_queue_retries', ['retry_count', 'max_retries', 'status'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_priority'), ['priority'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_recipient_email'), ['recipient_email'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_scheduled_at'), ['scheduled_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_template_id'), ['template_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_queue_user_id'), ['user_id'], unique=False)
        batch_op.drop_column('attempts')
        batch_op.drop_column('max_attempts')

    with op.batch_alter_table('email_templates', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=None,
               existing_nullable=False)
        batch_op.drop_index('idx_email_template_category')
        batch_op.drop_index('idx_email_template_is_active')
        batch_op.drop_index('idx_email_template_name')
        batch_op.create_index(batch_op.f('ix_email_templates_category'), ['category'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_templates_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_email_templates_name'), ['name'], unique=False)

    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_constraint('uq_oauth_accounts_provider_user', type_='unique')
        batch_op.drop_constraint('uq_oauth_accounts_uuid', type_='unique')
        batch_op.create_index('idx_oauth_account_unique', ['provider_id', 'provider_user_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_provider_email'), ['provider_email'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_provider_id'), ['provider_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_provider_user_id'), ['provider_user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('oauth_providers', schema=None) as batch_op:
        batch_op.drop_constraint('uq_oauth_providers_name', type_='unique')
        batch_op.drop_constraint('uq_oauth_providers_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_oauth_providers_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_name'), ['name'], unique=True)
        batch_op.create_index(batch_op.f('ix_oauth_providers_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_providers_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('oauth_states', schema=None) as batch_op:
        batch_op.drop_constraint('uq_oauth_states_state_token', type_='unique')
        batch_op.drop_constraint('uq_oauth_states_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_oauth_states_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_expires_at'), ['expires_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_is_used'), ['is_used'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_provider_id'), ['provider_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_state_token'), ['state_token'], unique=True)
        batch_op.create_index(batch_op.f('ix_oauth_states_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_states_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('oauth_tokens', schema=None) as batch_op:
        batch_op.drop_constraint('uq_oauth_tokens_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_oauth_tokens_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_expires_at'), ['expires_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_is_revoked'), ['is_revoked'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_oauth_account_id'), ['oauth_account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_oauth_tokens_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('password_history', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_password_history_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_history_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('password_reset_tokens', schema=None) as batch_op:
        batch_op.drop_constraint('uq_password_reset_tokens_token_hash', type_='unique')
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_expires_at'), ['expires_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_token_hash'), ['token_hash'], unique=True)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_user_id'), ['user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_password_reset_tokens_uuid'), ['uuid'], unique=True)

    with op.batch_alter_table('security_events', schema=None) as batch_op:
        batch_op.add_column(sa.Column('details', sa.JSON(), nullable=True))
        batch_op.add_column(sa.Column('session_id', sa.String(length=255), nullable=True))
        batch_op.alter_column('resolved_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               server_default=sa.text('now()'),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               server_default=sa.text('now()'),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
        batch_op.drop_index('idx_security_events_unresolved', postgresql_where='(resolved = false)')
        batch_op.create_index('idx_security_events_unresolved', ['resolved', 'severity'], unique=False)
        batch_op.create_foreign_key(batch_op.f('fk_security_events_user_id_users'), 'users', ['user_id'], ['id'], ondelete='SET NULL')
        batch_op.create_foreign_key(batch_op.f('fk_security_events_resolved_by_users'), 'users', ['resolved_by'], ['id'], ondelete='SET NULL')
        batch_op.drop_column('additional_data')
        batch_op.drop_column('resolution_notes')

    with op.batch_alter_table('service_availability', schema=None) as batch_op:
        batch_op.alter_column('max_bookings',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('current_bookings',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_available',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_recurring',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_constraint('uq_service_availability_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_service_availability_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_availability_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_availability_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_availability_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_availability_uuid'), ['uuid'], unique=True)
        batch_op.drop_constraint('fk_service_availability_updated_by_users', type_='foreignkey')
        batch_op.drop_constraint('fk_service_availability_created_by_users', type_='foreignkey')

    with op.batch_alter_table('service_categories', schema=None) as batch_op:
        batch_op.alter_column('display_order',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_constraint('uq_service_categories_slug', type_='unique')
        batch_op.drop_constraint('uq_service_categories_uuid', type_='unique')
        batch_op.drop_index('ix_service_categories_slug')
        batch_op.create_index(batch_op.f('ix_service_categories_slug'), ['slug'], unique=True)
        batch_op.create_index(batch_op.f('ix_service_categories_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_categories_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_categories_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_categories_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_categories_uuid'), ['uuid'], unique=True)
        batch_op.drop_constraint('fk_service_categories_updated_by_users', type_='foreignkey')
        batch_op.drop_constraint('fk_service_categories_created_by_users', type_='foreignkey')

    with op.batch_alter_table('service_images', schema=None) as batch_op:
        batch_op.alter_column('media_type',
               existing_type=sa.VARCHAR(length=50),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('display_order',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_primary',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_featured',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_constraint('uq_service_images_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_service_images_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_images_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_images_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_images_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_images_uuid'), ['uuid'], unique=True)
        batch_op.drop_constraint('fk_service_images_created_by_users', type_='foreignkey')
        batch_op.drop_constraint('fk_service_images_updated_by_users', type_='foreignkey')

    with op.batch_alter_table('service_pricing', schema=None) as batch_op:
        batch_op.alter_column('currency',
               existing_type=sa.VARCHAR(length=3),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('min_participants',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_constraint('uq_service_pricing_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_service_pricing_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_pricing_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_pricing_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_pricing_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_service_pricing_uuid'), ['uuid'], unique=True)
        batch_op.drop_constraint('fk_service_pricing_created_by_users', type_='foreignkey')
        batch_op.drop_constraint('fk_service_pricing_updated_by_users', type_='foreignkey')

    with op.batch_alter_table('services', schema=None) as batch_op:
        batch_op.alter_column('min_participants',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('pricing_type',
               existing_type=postgresql.ENUM('fixed', 'hourly', 'daily', 'package', 'custom', name='pricingtype'),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('currency',
               existing_type=sa.VARCHAR(length=3),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('availability_type',
               existing_type=postgresql.ENUM('always', 'scheduled', 'on_demand', name='availabilitytype'),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('advance_booking_hours',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('cancellation_hours',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('status',
               existing_type=postgresql.ENUM('draft', 'active', 'paused', 'archived', name='servicestatus'),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_featured',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_instant_booking',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('total_bookings',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('average_rating',
               existing_type=sa.NUMERIC(precision=3, scale=2),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('total_reviews',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_constraint('uq_services_uuid', type_='unique')
        batch_op.create_index(batch_op.f('ix_services_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_services_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_services_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_services_updated_by'), ['updated_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_services_uuid'), ['uuid'], unique=True)
        batch_op.drop_constraint('fk_services_created_by_users', type_='foreignkey')
        batch_op.drop_constraint('fk_services_updated_by_users', type_='foreignkey')

    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.add_column(sa.Column('blacklisted_at', sa.DateTime(timezone=True), nullable=False))
        batch_op.alter_column('expires_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               server_default=sa.text('now()'),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               server_default=sa.text('now()'),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
        batch_op.drop_index('idx_token_blacklist_jti')
        batch_op.create_index('idx_token_blacklist_jti', ['jti'], unique=False)
        batch_op.create_foreign_key(batch_op.f('fk_token_blacklist_user_id_users'), 'users', ['user_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('role',
               existing_type=postgresql.ENUM('admin', 'customer', 'vendor', 'moderator', name='user_role'),
               type_=sa.String(length=20),
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_index('idx_users_created_at_btree')
        batch_op.drop_index('idx_users_email_trgm', postgresql_using='gin')
        batch_op.drop_index('idx_users_name_trgm', postgresql_using='gin')
        batch_op.drop_index('idx_users_search_vector_gin', postgresql_using='gin')
        batch_op.drop_column('search_vector')

    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.alter_column('document_type',
               existing_type=postgresql.ENUM('business_license', 'tax_certificate', 'insurance_certificate', 'professional_certification', 'identity_document', 'utility_bill', name='document_type'),
               type_=sa.Enum('BUSINESS_LICENSE', 'TAX_CERTIFICATE', 'IDENTITY_DOCUMENT', 'PROFESSIONAL_CERTIFICATION', 'INSURANCE_CERTIFICATE', 'BANK_STATEMENT', 'UTILITY_BILL', 'OTHER', name='documenttype'),
               existing_nullable=False)
        batch_op.alter_column('status',
               existing_type=postgresql.ENUM('pending', 'approved', 'rejected', 'expired', name='document_status'),
               type_=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED', name='documentstatus'),
               existing_nullable=False)
        batch_op.alter_column('verification_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_index('idx_vendor_documents_verification_data_gin', postgresql_using='gin')

    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.alter_column('operating_hours',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('social_media_links',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('languages_spoken',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('specializations',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('certifications',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('gallery_images',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('insurance_details',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('additional_info',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_index('idx_vendor_profiles_languages_gin', postgresql_using='gin')
        batch_op.drop_index('idx_vendor_profiles_latitude')
        batch_op.drop_index('idx_vendor_profiles_longitude')
        batch_op.drop_index('idx_vendor_profiles_search_vector_gin', postgresql_using='gin')
        batch_op.drop_index('idx_vendor_profiles_specializations_gin', postgresql_using='gin')
        batch_op.drop_column('search_vector')

    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.alter_column('business_type',
               existing_type=postgresql.ENUM('guide', 'restaurant', 'hotel', 'transport', 'activity_provider', 'private_beach', 'apartment_rental', 'car_hire', 'security_service', 'other', name='vendor_type'),
               type_=sa.Enum('GUIDE', 'RESTAURANT', 'HOTEL', 'TRANSPORT', 'ACTIVITY_PROVIDER', 'PRIVATE_BEACH', 'APARTMENT_RENTAL', 'CAR_HIRE', 'SECURITY_SERVICE', 'OTHER', name='vendortype'),
               existing_nullable=False)
        batch_op.alter_column('verification_status',
               existing_type=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'suspended', name='verification_status'),
               type_=sa.Enum('PENDING', 'UNDER_REVIEW', 'VERIFIED', 'REJECTED', 'SUSPENDED', 'RESUBMISSION_REQUIRED', name='verificationstatus'),
               existing_nullable=False)
        batch_op.alter_column('marketplace_status',
               existing_type=postgresql.ENUM('active', 'inactive', 'suspended', 'pending', name='marketplace_status'),
               type_=sa.Enum('ACTIVE', 'INACTIVE', 'PAUSED', 'SUSPENDED', 'BANNED', name='marketplacestatus'),
               existing_nullable=False)
        batch_op.alter_column('uuid',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
        batch_op.drop_index('idx_vendors_performance_composite')

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vendors', schema=None) as batch_op:
        batch_op.create_index('idx_vendors_performance_composite', ['average_rating', 'total_bookings', 'marketplace_status'], unique=False)
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('marketplace_status',
               existing_type=sa.Enum('ACTIVE', 'INACTIVE', 'PAUSED', 'SUSPENDED', 'BANNED', name='marketplacestatus'),
               type_=postgresql.ENUM('active', 'inactive', 'suspended', 'pending', name='marketplace_status'),
               existing_nullable=False)
        batch_op.alter_column('verification_status',
               existing_type=sa.Enum('PENDING', 'UNDER_REVIEW', 'VERIFIED', 'REJECTED', 'SUSPENDED', 'RESUBMISSION_REQUIRED', name='verificationstatus'),
               type_=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'suspended', name='verification_status'),
               existing_nullable=False)
        batch_op.alter_column('business_type',
               existing_type=sa.Enum('GUIDE', 'RESTAURANT', 'HOTEL', 'TRANSPORT', 'ACTIVITY_PROVIDER', 'PRIVATE_BEACH', 'APARTMENT_RENTAL', 'CAR_HIRE', 'SECURITY_SERVICE', 'OTHER', name='vendortype'),
               type_=postgresql.ENUM('guide', 'restaurant', 'hotel', 'transport', 'activity_provider', 'private_beach', 'apartment_rental', 'car_hire', 'security_service', 'other', name='vendor_type'),
               existing_nullable=False)

    with op.batch_alter_table('vendor_profiles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True))
        batch_op.create_index('idx_vendor_profiles_specializations_gin', ['specializations'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_vendor_profiles_search_vector_gin', ['search_vector'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_vendor_profiles_longitude', ['longitude'], unique=False)
        batch_op.create_index('idx_vendor_profiles_latitude', ['latitude'], unique=False)
        batch_op.create_index('idx_vendor_profiles_languages_gin', ['languages_spoken'], unique=False, postgresql_using='gin')
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('additional_info',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('insurance_details',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('gallery_images',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('certifications',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('specializations',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('languages_spoken',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('social_media_links',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('operating_hours',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)

    with op.batch_alter_table('vendor_documents', schema=None) as batch_op:
        batch_op.create_index('idx_vendor_documents_verification_data_gin', ['verification_data'], unique=False, postgresql_using='gin')
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('verification_data',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED', name='documentstatus'),
               type_=postgresql.ENUM('pending', 'approved', 'rejected', 'expired', name='document_status'),
               existing_nullable=False)
        batch_op.alter_column('document_type',
               existing_type=sa.Enum('BUSINESS_LICENSE', 'TAX_CERTIFICATE', 'IDENTITY_DOCUMENT', 'PROFESSIONAL_CERTIFICATION', 'INSURANCE_CERTIFICATE', 'BANK_STATEMENT', 'UTILITY_BILL', 'OTHER', name='documenttype'),
               type_=postgresql.ENUM('business_license', 'tax_certificate', 'insurance_certificate', 'professional_certification', 'identity_document', 'utility_bill', name='document_type'),
               existing_nullable=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('search_vector', postgresql.TSVECTOR(), autoincrement=False, nullable=True))
        batch_op.create_index('idx_users_search_vector_gin', ['search_vector'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_name_trgm', [sa.text("((first_name::text || ' '::text) || last_name::text)")], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_email_trgm', ['email'], unique=False, postgresql_using='gin')
        batch_op.create_index('idx_users_created_at_btree', ['created_at'], unique=False)
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('role',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('admin', 'customer', 'vendor', 'moderator', name='user_role'),
               existing_nullable=False)

    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)

    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('fk_token_blacklist_user_id_users'), type_='foreignkey')
        batch_op.drop_index('idx_token_blacklist_jti')
        batch_op.create_index('idx_token_blacklist_jti', ['jti'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               server_default=None,
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               server_default=None,
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.alter_column('expires_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.drop_column('blacklisted_at')

    with op.batch_alter_table('services', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_services_updated_by_users', 'users', ['updated_by'], ['id'])
        batch_op.create_foreign_key('fk_services_created_by_users', 'users', ['created_by'], ['id'])
        batch_op.drop_index(batch_op.f('ix_services_uuid'))
        batch_op.drop_index(batch_op.f('ix_services_updated_by'))
        batch_op.drop_index(batch_op.f('ix_services_updated_at'))
        batch_op.drop_index(batch_op.f('ix_services_created_by'))
        batch_op.drop_index(batch_op.f('ix_services_created_at'))
        batch_op.create_unique_constraint('uq_services_uuid', ['uuid'])
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('total_reviews',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)
        batch_op.alter_column('average_rating',
               existing_type=sa.NUMERIC(precision=3, scale=2),
               server_default=sa.text('0.00'),
               existing_nullable=False)
        batch_op.alter_column('total_bookings',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)
        batch_op.alter_column('is_instant_booking',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
        batch_op.alter_column('is_featured',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
        batch_op.alter_column('status',
               existing_type=postgresql.ENUM('draft', 'active', 'paused', 'archived', name='servicestatus'),
               server_default=sa.text("'draft'::servicestatus"),
               existing_nullable=False)
        batch_op.alter_column('cancellation_hours',
               existing_type=sa.INTEGER(),
               server_default=sa.text('24'),
               existing_nullable=False)
        batch_op.alter_column('advance_booking_hours',
               existing_type=sa.INTEGER(),
               server_default=sa.text('24'),
               existing_nullable=False)
        batch_op.alter_column('availability_type',
               existing_type=postgresql.ENUM('always', 'scheduled', 'on_demand', name='availabilitytype'),
               server_default=sa.text("'scheduled'::availabilitytype"),
               existing_nullable=False)
        batch_op.alter_column('currency',
               existing_type=sa.VARCHAR(length=3),
               server_default=sa.text("'USD'::character varying"),
               existing_nullable=False)
        batch_op.alter_column('pricing_type',
               existing_type=postgresql.ENUM('fixed', 'hourly', 'daily', 'package', 'custom', name='pricingtype'),
               server_default=sa.text("'fixed'::pricingtype"),
               existing_nullable=False)
        batch_op.alter_column('min_participants',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               existing_nullable=False)

    with op.batch_alter_table('service_pricing', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_service_pricing_updated_by_users', 'users', ['updated_by'], ['id'])
        batch_op.create_foreign_key('fk_service_pricing_created_by_users', 'users', ['created_by'], ['id'])
        batch_op.drop_index(batch_op.f('ix_service_pricing_uuid'))
        batch_op.drop_index(batch_op.f('ix_service_pricing_updated_by'))
        batch_op.drop_index(batch_op.f('ix_service_pricing_updated_at'))
        batch_op.drop_index(batch_op.f('ix_service_pricing_created_by'))
        batch_op.drop_index(batch_op.f('ix_service_pricing_created_at'))
        batch_op.create_unique_constraint('uq_service_pricing_uuid', ['uuid'])
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('true'),
               existing_nullable=False)
        batch_op.alter_column('min_participants',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               existing_nullable=False)
        batch_op.alter_column('currency',
               existing_type=sa.VARCHAR(length=3),
               server_default=sa.text("'USD'::character varying"),
               existing_nullable=False)

    with op.batch_alter_table('service_images', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_service_images_updated_by_users', 'users', ['updated_by'], ['id'])
        batch_op.create_foreign_key('fk_service_images_created_by_users', 'users', ['created_by'], ['id'])
        batch_op.drop_index(batch_op.f('ix_service_images_uuid'))
        batch_op.drop_index(batch_op.f('ix_service_images_updated_by'))
        batch_op.drop_index(batch_op.f('ix_service_images_updated_at'))
        batch_op.drop_index(batch_op.f('ix_service_images_created_by'))
        batch_op.drop_index(batch_op.f('ix_service_images_created_at'))
        batch_op.create_unique_constraint('uq_service_images_uuid', ['uuid'])
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('is_featured',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
        batch_op.alter_column('is_primary',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
        batch_op.alter_column('display_order',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)
        batch_op.alter_column('media_type',
               existing_type=sa.VARCHAR(length=50),
               server_default=sa.text("'image'::character varying"),
               existing_nullable=False)

    with op.batch_alter_table('service_categories', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_service_categories_created_by_users', 'users', ['created_by'], ['id'])
        batch_op.create_foreign_key('fk_service_categories_updated_by_users', 'users', ['updated_by'], ['id'])
        batch_op.drop_index(batch_op.f('ix_service_categories_uuid'))
        batch_op.drop_index(batch_op.f('ix_service_categories_updated_by'))
        batch_op.drop_index(batch_op.f('ix_service_categories_updated_at'))
        batch_op.drop_index(batch_op.f('ix_service_categories_created_by'))
        batch_op.drop_index(batch_op.f('ix_service_categories_created_at'))
        batch_op.drop_index(batch_op.f('ix_service_categories_slug'))
        batch_op.create_index('ix_service_categories_slug', ['slug'], unique=False)
        batch_op.create_unique_constraint('uq_service_categories_uuid', ['uuid'])
        batch_op.create_unique_constraint('uq_service_categories_slug', ['slug'])
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('true'),
               existing_nullable=False)
        batch_op.alter_column('display_order',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)

    with op.batch_alter_table('service_availability', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_service_availability_created_by_users', 'users', ['created_by'], ['id'])
        batch_op.create_foreign_key('fk_service_availability_updated_by_users', 'users', ['updated_by'], ['id'])
        batch_op.drop_index(batch_op.f('ix_service_availability_uuid'))
        batch_op.drop_index(batch_op.f('ix_service_availability_updated_by'))
        batch_op.drop_index(batch_op.f('ix_service_availability_updated_at'))
        batch_op.drop_index(batch_op.f('ix_service_availability_created_by'))
        batch_op.drop_index(batch_op.f('ix_service_availability_created_at'))
        batch_op.create_unique_constraint('uq_service_availability_uuid', ['uuid'])
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
        batch_op.alter_column('is_recurring',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
        batch_op.alter_column('is_available',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('true'),
               existing_nullable=False)
        batch_op.alter_column('current_bookings',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)
        batch_op.alter_column('max_bookings',
               existing_type=sa.INTEGER(),
               server_default=sa.text('1'),
               existing_nullable=False)

    with op.batch_alter_table('security_events', schema=None) as batch_op:
        batch_op.add_column(sa.Column('resolution_notes', sa.TEXT(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('additional_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
        batch_op.drop_constraint(batch_op.f('fk_security_events_resolved_by_users'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_security_events_user_id_users'), type_='foreignkey')
        batch_op.drop_index('idx_security_events_unresolved')
        batch_op.create_index('idx_security_events_unresolved', ['resolved'], unique=False, postgresql_where='(resolved = false)')
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               server_default=None,
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               server_default=None,
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
        batch_op.alter_column('resolved_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
        batch_op.drop_column('session_id')
        batch_op.drop_column('details')

    with op.batch_alter_table('password_reset_tokens', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_uuid'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_user_id'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_updated_by'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_updated_at'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_token_hash'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_is_active'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_expires_at'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_created_by'))
        batch_op.drop_index(batch_op.f('ix_password_reset_tokens_created_at'))
        batch_op.create_unique_constraint('uq_password_reset_tokens_token_hash', ['token_hash'])

    with op.batch_alter_table('password_history', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_password_history_uuid'))
        batch_op.drop_index(batch_op.f('ix_password_history_user_id'))
        batch_op.drop_index(batch_op.f('ix_password_history_updated_by'))
        batch_op.drop_index(batch_op.f('ix_password_history_updated_at'))
        batch_op.drop_index(batch_op.f('ix_password_history_id'))
        batch_op.drop_index(batch_op.f('ix_password_history_created_by'))
        batch_op.drop_index(batch_op.f('ix_password_history_created_at'))

    with op.batch_alter_table('oauth_tokens', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_uuid'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_updated_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_updated_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_oauth_account_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_is_revoked'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_is_active'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_expires_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_created_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_tokens_created_at'))
        batch_op.create_unique_constraint('uq_oauth_tokens_uuid', ['uuid'])

    with op.batch_alter_table('oauth_states', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_states_uuid'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_updated_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_updated_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_state_token'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_provider_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_is_used'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_expires_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_created_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_states_created_at'))
        batch_op.create_unique_constraint('uq_oauth_states_uuid', ['uuid'])
        batch_op.create_unique_constraint('uq_oauth_states_state_token', ['state_token'])

    with op.batch_alter_table('oauth_providers', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_providers_uuid'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_updated_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_updated_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_name'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_is_active'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_created_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_providers_created_at'))
        batch_op.create_unique_constraint('uq_oauth_providers_uuid', ['uuid'])
        batch_op.create_unique_constraint('uq_oauth_providers_name', ['name'])

    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_uuid'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_user_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_updated_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_updated_at'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_provider_user_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_provider_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_provider_email'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_is_active'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_id'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_created_by'))
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_created_at'))
        batch_op.drop_index('idx_oauth_account_unique')
        batch_op.create_unique_constraint('uq_oauth_accounts_uuid', ['uuid'])
        batch_op.create_unique_constraint('uq_oauth_accounts_provider_user', ['provider_id', 'provider_user_id'])

    with op.batch_alter_table('email_templates', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_email_templates_name'))
        batch_op.drop_index(batch_op.f('ix_email_templates_is_active'))
        batch_op.drop_index(batch_op.f('ix_email_templates_category'))
        batch_op.create_index('idx_email_template_name', ['name'], unique=False)
        batch_op.create_index('idx_email_template_is_active', ['is_active'], unique=False)
        batch_op.create_index('idx_email_template_category', ['category'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)

    with op.batch_alter_table('email_queue', schema=None) as batch_op:
        batch_op.add_column(sa.Column('max_attempts', sa.INTEGER(), autoincrement=False, nullable=False))
        batch_op.add_column(sa.Column('attempts', sa.INTEGER(), autoincrement=False, nullable=False))
        batch_op.drop_index(batch_op.f('ix_email_queue_user_id'))
        batch_op.drop_index(batch_op.f('ix_email_queue_template_id'))
        batch_op.drop_index(batch_op.f('ix_email_queue_status'))
        batch_op.drop_index(batch_op.f('ix_email_queue_scheduled_at'))
        batch_op.drop_index(batch_op.f('ix_email_queue_recipient_email'))
        batch_op.drop_index(batch_op.f('ix_email_queue_priority'))
        batch_op.drop_index('idx_email_queue_retries')
        batch_op.create_index('idx_email_queue_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_email_queue_template_id', ['template_id'], unique=False)
        batch_op.create_index('idx_email_queue_status', ['status'], unique=False)
        batch_op.create_index('idx_email_queue_scheduled_at', ['scheduled_at'], unique=False)
        batch_op.create_index('idx_email_queue_priority', ['priority'], unique=False)
        batch_op.create_index('idx_email_queue_attempts', ['attempts', 'max_attempts', 'status'], unique=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.alter_column('scheduled_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.drop_column('max_retries')
        batch_op.drop_column('retry_count')

    with op.batch_alter_table('email_preferences', schema=None) as batch_op:
        batch_op.add_column(sa.Column('opted_out_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
        batch_op.drop_index(batch_op.f('ix_email_preferences_user_id'))
        batch_op.create_unique_constraint('uq_email_preferences_user_id', ['user_id'])
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.drop_column('opt_out_date')
        batch_op.drop_column('opt_out_reason')
        batch_op.drop_column('opted_out')

    with op.batch_alter_table('email_deliveries', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_email_deliveries_user_id'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_template_id'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_status'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_recipient_email'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_priority'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_created_at'))
        batch_op.drop_index(batch_op.f('ix_email_deliveries_correlation_id'))
        batch_op.create_index('idx_email_delivery_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_email_delivery_template_id', ['template_id'], unique=False)
        batch_op.create_index('idx_email_delivery_status', ['status'], unique=False)
        batch_op.create_index('idx_email_delivery_created_at', ['created_at'], unique=False)
        batch_op.create_index('idx_email_delivery_correlation_id', ['correlation_id'], unique=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               server_default=sa.text('now()'),
               existing_nullable=False)
        batch_op.drop_column('updated_at')
        batch_op.drop_column('priority')
        batch_op.drop_column('html_body')
        batch_op.drop_column('body')

    with op.batch_alter_table('api_keys', schema=None) as batch_op:
        batch_op.alter_column('uuid',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)

    with op.batch_alter_table('account_lockouts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_account_lockouts_uuid'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_user_id'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_updated_by'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_updated_at'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_locked_until'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_id'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_created_by'))
        batch_op.drop_index(batch_op.f('ix_account_lockouts_created_at'))
        batch_op.create_unique_constraint('uq_account_lockouts_user_id', ['user_id'])

    with op.batch_alter_table('verification_history', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_verification_history_workflow_id'))
        batch_op.drop_index(batch_op.f('ix_verification_history_uuid'))
        batch_op.drop_index(batch_op.f('ix_verification_history_updated_by'))
        batch_op.drop_index(batch_op.f('ix_verification_history_updated_at'))
        batch_op.drop_index(batch_op.f('ix_verification_history_performed_by'))
        batch_op.drop_index(batch_op.f('ix_verification_history_performed_at'))
        batch_op.drop_index(batch_op.f('ix_verification_history_id'))
        batch_op.drop_index(batch_op.f('ix_verification_history_created_by'))
        batch_op.drop_index(batch_op.f('ix_verification_history_created_at'))
        batch_op.drop_index(batch_op.f('ix_verification_history_action_type'))
        batch_op.drop_index('idx_history_workflow_action')
        batch_op.drop_index('idx_history_user_action')
        batch_op.drop_index('idx_history_action_time')

    op.drop_table('verification_history')
    with op.batch_alter_table('document_verification_workflows', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_workflow_reference'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_vendor_id'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_uuid'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_updated_by'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_updated_at'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_submitted_at'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_status'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_reviewed_at'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_priority'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_id'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_due_date'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_document_id'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_created_by'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_created_at'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_completed_at'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_automated_validation_status'))
        batch_op.drop_index(batch_op.f('ix_document_verification_workflows_assigned_to'))
        batch_op.drop_index('idx_workflow_vendor_status')
        batch_op.drop_index('idx_workflow_status_priority')
        batch_op.drop_index('idx_workflow_due_date')
        batch_op.drop_index('idx_workflow_automation')
        batch_op.drop_index('idx_workflow_assigned')

    op.drop_table('document_verification_workflows')
    with op.batch_alter_table('permission_grants', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_permission_grants_uuid'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_user_id'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_updated_by'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_updated_at'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_role'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_resource_type'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_resource_id'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_permission'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_id'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_created_by'))
        batch_op.drop_index(batch_op.f('ix_permission_grants_created_at'))
        batch_op.drop_index('idx_permission_grant_user')
        batch_op.drop_index('idx_permission_grant_role')
        batch_op.drop_index('idx_permission_grant_resource')
        batch_op.drop_index('idx_permission_grant_permission')
        batch_op.drop_index('idx_permission_grant_expires')

    op.drop_table('permission_grants')
    with op.batch_alter_table('automated_validation_rules', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_validation_type'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_uuid'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_updated_by'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_updated_at'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_rule_name'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_last_executed_at'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_is_active'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_id'))
        batch_op.drop_index(batch_op.f('ix_automated_validation_rules_created_at'))
        batch_op.drop_index('idx_rule_type_active')
        batch_op.drop_index('idx_rule_priority')
        batch_op.drop_index('idx_rule_performance')

    op.drop_table('automated_validation_rules')
    with op.batch_alter_table('access_control_logs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_access_control_logs_uuid'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_user_role'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_user_id'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_updated_by'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_updated_at'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_resource_type'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_resource_id'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_required_permission'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_request_timestamp'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_ip_address'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_id'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_endpoint'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_decision'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_created_by'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_created_at'))
        batch_op.drop_index(batch_op.f('ix_access_control_logs_correlation_id'))
        batch_op.drop_index('idx_access_log_user_timestamp')
        batch_op.drop_index('idx_access_log_permission')
        batch_op.drop_index('idx_access_log_ip')
        batch_op.drop_index('idx_access_log_endpoint_timestamp')
        batch_op.drop_index('idx_access_log_decision_timestamp')
        batch_op.drop_index('idx_access_log_correlation')

    op.drop_table('access_control_logs')
    with op.batch_alter_table('role_hierarchies', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_uuid'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_updated_by'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_updated_at'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_parent_role'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_id'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_created_by'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_created_at'))
        batch_op.drop_index(batch_op.f('ix_role_hierarchies_child_role'))
        batch_op.drop_index('idx_role_hierarchy_parent')
        batch_op.drop_index('idx_role_hierarchy_level')
        batch_op.drop_index('idx_role_hierarchy_child')

    op.drop_table('role_hierarchies')
    # ### end Alembic commands ###
