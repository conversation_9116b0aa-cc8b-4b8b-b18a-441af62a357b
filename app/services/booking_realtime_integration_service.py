"""
Booking Real-time Integration Service for Culture Connect Backend API.

This module provides seamless integration between the booking communication system
and the enhanced event broadcasting infrastructure for real-time updates.

Implements Task 6.1.1 Phase 7 requirements for booking communication integration including:
- Live booking status updates broadcast to relevant participants
- Real-time vendor response notifications to customers
- Booking conversation message broadcasting through WebSocket infrastructure
- Integration with existing BookingCommunicationService for seamless real-time updates

Performance targets: <50ms event processing, <100ms message broadcasting, >99.9% delivery reliability
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Set
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.services.base import BaseService, ValidationError, ServiceError, NotFoundError
from app.core.logging import correlation_id
from app.models.user import User
from app.models.booking import Booking
from app.models.booking_communication import BookingMessage
from app.services.enhanced_event_broadcasting_service import (
    EnhancedEventBroadcastingService, EventMessage, MessagePriority,
    EventCategory, SubscriptionTopic
)
from app.services.booking_communication_service import BookingMessageService

logger = logging.getLogger(__name__)


class BookingEventType(str, Enum):
    """Booking-specific event types for real-time broadcasting."""
    BOOKING_CREATED = "booking.created"
    BOOKING_CONFIRMED = "booking.confirmed"
    BOOKING_CANCELLED = "booking.cancelled"
    BOOKING_MODIFIED = "booking.modified"
    BOOKING_COMPLETED = "booking.completed"
    VENDOR_RESPONSE = "booking.vendor_response"
    PAYMENT_CONFIRMED = "booking.payment_confirmed"
    MESSAGE_RECEIVED = "booking.message_received"
    MESSAGE_READ = "booking.message_read"
    TYPING_INDICATOR = "booking.typing"


class BookingRealtimeIntegrationService(BaseService):
    """
    Booking real-time integration service for seamless WebSocket communication.

    Provides comprehensive integration between booking system and real-time broadcasting including:
    - Live booking status updates with participant targeting
    - Real-time message broadcasting for booking conversations
    - Vendor response notifications with priority handling
    - Multi-channel delivery coordination with email and push notifications
    """

    def __init__(self, db: AsyncSession, broadcasting_service: Optional[EnhancedEventBroadcastingService] = None):
        super().__init__(db)
        self.broadcasting_service = broadcasting_service or EnhancedEventBroadcastingService(db)
        self.booking_message_service = BookingMessageService(db)

        # Performance metrics
        self.metrics = {
            "booking_events_processed": 0,
            "messages_broadcasted": 0,
            "notifications_sent": 0,
            "average_processing_time": 0,
            "failed_broadcasts": 0
        }

    async def broadcast_booking_status_update(
        self,
        booking_id: int,
        status: str,
        event_data: Dict[str, Any],
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast booking status update to relevant participants.

        Performance target: <50ms event processing.
        """
        start_time = time.time()

        try:
            # Get booking details
            booking = await self._get_booking_details(booking_id)
            if not booking:
                raise NotFoundError(f"Booking {booking_id} not found")

            # Determine event type based on status
            event_type = self._map_status_to_event_type(status)

            # Determine message priority based on event type
            priority = self._get_event_priority(event_type)

            # Create event message
            event_message = EventMessage(
                event_type=event_type.value,
                category=EventCategory.BOOKING,
                priority=priority,
                topic=SubscriptionTopic.BOOKING_UPDATES,
                payload={
                    "booking_id": booking_id,
                    "status": status,
                    "booking_reference": booking.booking_reference,
                    "customer_id": booking.user_id,
                    "vendor_id": booking.vendor_id,
                    "service_name": getattr(booking, 'service_name', 'Unknown Service'),
                    "event_data": event_data,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                target_user_ids={booking.user_id, booking.vendor_id},
                booking_id=booking_id,
                requires_acknowledgment=True
            )

            # Broadcast event
            result = await self.broadcasting_service.broadcast_event(event_message, current_user)

            # Update metrics
            self.metrics["booking_events_processed"] += 1
            processing_time = (time.time() - start_time) * 1000
            self._update_processing_metrics(processing_time)

            logger.info(f"Booking status update broadcasted for booking {booking_id} in {processing_time:.2f}ms")

            return {
                **result,
                "booking_id": booking_id,
                "status": status,
                "event_type": event_type.value,
                "participants_notified": 2  # Customer and vendor
            }

        except Exception as e:
            logger.error(f"Failed to broadcast booking status update: {str(e)}")
            self.metrics["failed_broadcasts"] += 1
            raise ServiceError(f"Booking status broadcast failed: {str(e)}")

    async def broadcast_vendor_response(
        self,
        booking_id: int,
        vendor_id: int,
        response_type: str,
        response_data: Dict[str, Any],
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast vendor response to customer with high priority.

        Performance target: <25ms event processing (high priority).
        """
        start_time = time.time()

        try:
            # Get booking details
            booking = await self._get_booking_details(booking_id)
            if not booking:
                raise NotFoundError(f"Booking {booking_id} not found")

            # Validate vendor
            if booking.vendor_id != vendor_id:
                raise ValidationError("Vendor ID does not match booking")

            # Create high-priority event message
            event_message = EventMessage(
                event_type=BookingEventType.VENDOR_RESPONSE.value,
                category=EventCategory.BOOKING,
                priority=MessagePriority.HIGH,
                topic=SubscriptionTopic.BOOKING_UPDATES,
                payload={
                    "booking_id": booking_id,
                    "vendor_id": vendor_id,
                    "response_type": response_type,
                    "response_data": response_data,
                    "booking_reference": booking.booking_reference,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                target_user_ids={booking.user_id},  # Notify customer only
                booking_id=booking_id,
                requires_acknowledgment=True
            )

            # Broadcast event
            result = await self.broadcasting_service.broadcast_event(event_message, current_user)

            # Update metrics
            self.metrics["notifications_sent"] += 1
            processing_time = (time.time() - start_time) * 1000

            logger.info(f"Vendor response broadcasted for booking {booking_id} in {processing_time:.2f}ms")

            return {
                **result,
                "booking_id": booking_id,
                "vendor_id": vendor_id,
                "response_type": response_type,
                "customer_notified": True
            }

        except Exception as e:
            logger.error(f"Failed to broadcast vendor response: {str(e)}")
            self.metrics["failed_broadcasts"] += 1
            raise ServiceError(f"Vendor response broadcast failed: {str(e)}")

    async def broadcast_booking_message(
        self,
        message: BookingMessage,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast booking conversation message through WebSocket infrastructure.

        Performance target: <50ms event processing.
        """
        start_time = time.time()

        try:
            # Get booking details
            booking = await self._get_booking_details(message.booking_id)
            if not booking:
                raise NotFoundError(f"Booking {message.booking_id} not found")

            # Determine target user (recipient)
            target_user_id = message.recipient_id
            if not target_user_id:
                # If no specific recipient, broadcast to all participants except sender
                target_users = {booking.user_id, booking.vendor_id}
                target_users.discard(message.sender_id)
            else:
                target_users = {target_user_id}

            # Create event message
            event_message = EventMessage(
                event_type=BookingEventType.MESSAGE_RECEIVED.value,
                category=EventCategory.CHAT,
                priority=MessagePriority.STANDARD,
                topic=SubscriptionTopic.BOOKING_MESSAGES,
                payload={
                    "message_id": message.id,
                    "booking_id": message.booking_id,
                    "thread_id": str(message.thread_id),
                    "sender_id": message.sender_id,
                    "content": message.content,
                    "message_type": message.message_type.value,
                    "created_at": message.created_at.isoformat(),
                    "booking_reference": booking.booking_reference,
                    "has_attachments": bool(message.attachments)
                },
                target_user_ids=target_users,
                booking_id=message.booking_id,
                requires_acknowledgment=False  # Messages don't require acknowledgment
            )

            # Broadcast event
            result = await self.broadcasting_service.broadcast_event(event_message, current_user)

            # Update metrics
            self.metrics["messages_broadcasted"] += 1
            processing_time = (time.time() - start_time) * 1000

            logger.info(f"Booking message broadcasted for booking {message.booking_id} in {processing_time:.2f}ms")

            return {
                **result,
                "message_id": message.id,
                "booking_id": message.booking_id,
                "recipients_notified": len(target_users)
            }

        except Exception as e:
            logger.error(f"Failed to broadcast booking message: {str(e)}")
            self.metrics["failed_broadcasts"] += 1
            raise ServiceError(f"Booking message broadcast failed: {str(e)}")

    async def broadcast_typing_indicator(
        self,
        booking_id: int,
        user_id: int,
        is_typing: bool,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast typing indicator for real-time conversation feedback.

        Performance target: <25ms event processing (immediate priority).
        """
        try:
            # Get booking details
            booking = await self._get_booking_details(booking_id)
            if not booking:
                raise NotFoundError(f"Booking {booking_id} not found")

            # Determine target users (all participants except the typer)
            target_users = {booking.user_id, booking.vendor_id}
            target_users.discard(user_id)

            # Create immediate priority event message
            event_message = EventMessage(
                event_type=BookingEventType.TYPING_INDICATOR.value,
                category=EventCategory.CHAT,
                priority=MessagePriority.IMMEDIATE,
                topic=SubscriptionTopic.BOOKING_MESSAGES,
                payload={
                    "booking_id": booking_id,
                    "user_id": user_id,
                    "is_typing": is_typing,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                target_user_ids=target_users,
                booking_id=booking_id,
                requires_acknowledgment=False
            )

            # Broadcast event
            result = await self.broadcasting_service.broadcast_event(event_message, current_user)

            return {
                **result,
                "booking_id": booking_id,
                "user_id": user_id,
                "is_typing": is_typing
            }

        except Exception as e:
            logger.error(f"Failed to broadcast typing indicator: {str(e)}")
            raise ServiceError(f"Typing indicator broadcast failed: {str(e)}")

    async def get_integration_metrics(self) -> Dict[str, Any]:
        """Get comprehensive integration performance metrics."""
        broadcasting_metrics = await self.broadcasting_service.get_broadcasting_metrics()

        return {
            **self.metrics,
            "broadcasting_service_metrics": broadcasting_metrics,
            "total_events_in_queues": sum(broadcasting_metrics["queue_sizes"].values()),
            "integration_health": "healthy" if self.metrics["failed_broadcasts"] < 10 else "degraded"
        }

    # Helper methods

    def _map_status_to_event_type(self, status: str) -> BookingEventType:
        """Map booking status to appropriate event type."""
        status_mapping = {
            "confirmed": BookingEventType.BOOKING_CONFIRMED,
            "cancelled": BookingEventType.BOOKING_CANCELLED,
            "modified": BookingEventType.BOOKING_MODIFIED,
            "completed": BookingEventType.BOOKING_COMPLETED,
            "pending": BookingEventType.BOOKING_CREATED
        }

        return status_mapping.get(status.lower(), BookingEventType.BOOKING_MODIFIED)

    def _get_event_priority(self, event_type: BookingEventType) -> MessagePriority:
        """Determine message priority based on event type."""
        high_priority_events = {
            BookingEventType.BOOKING_CONFIRMED,
            BookingEventType.BOOKING_CANCELLED,
            BookingEventType.VENDOR_RESPONSE,
            BookingEventType.PAYMENT_CONFIRMED
        }

        immediate_priority_events = {
            BookingEventType.TYPING_INDICATOR
        }

        if event_type in immediate_priority_events:
            return MessagePriority.IMMEDIATE
        elif event_type in high_priority_events:
            return MessagePriority.HIGH
        else:
            return MessagePriority.STANDARD

    async def _get_booking_details(self, booking_id: int) -> Optional[Booking]:
        """Get booking details for event targeting."""
        try:
            # This would typically use a booking repository
            # For now, we'll create a mock implementation
            from app.repositories.booking_repository import BookingRepository
            booking_repo = BookingRepository(self.db)
            return await booking_repo.get_by_id(booking_id)
        except Exception as e:
            logger.error(f"Failed to get booking details for {booking_id}: {str(e)}")
            return None

    def _update_processing_metrics(self, processing_time: float) -> None:
        """Update processing time metrics."""
        current_avg = self.metrics["average_processing_time"]
        processed_count = self.metrics["booking_events_processed"]

        if processed_count > 0:
            # Calculate new average
            new_avg = ((current_avg * (processed_count - 1)) + processing_time) / processed_count
            self.metrics["average_processing_time"] = new_avg
        else:
            self.metrics["average_processing_time"] = processing_time
