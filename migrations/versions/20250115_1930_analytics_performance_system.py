"""Add Analytics & Performance System tables for Phase 7.1

Revision ID: analytics_performance_system
Revises: 3be6aa798753
Create Date: 2025-01-15 19:30:00.000000

This migration creates comprehensive analytics and performance system tables including:
- UserAnalytics for user behavior tracking and engagement metrics
- VendorAnalytics for vendor performance metrics and business intelligence
- BookingAnalytics for booking conversion and revenue analytics
- SystemMetrics for application performance and system health monitoring
- DashboardWidget for configurable dashboard components and KPI definitions
- KPIDefinition for business intelligence and performance tracking
- Comprehensive indexes for performance optimization (<200ms query targets)
- Foreign key constraints with proper CASCADE/SET NULL behaviors
- Check constraints for data validation and integrity
- JSONB indexes for analytics data fields with GIN indexing
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'analytics_performance_system'
down_revision = '3be6aa798753'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema with analytics and performance system tables."""

    # Create UserAnalytics table
    op.create_table('user_analytics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('timeframe', sa.Enum('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', name='analyticstimeframe'), nullable=False),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
        sa.Column('page_views', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('session_duration_minutes', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'),
        sa.Column('actions_taken', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('bookings_made', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('searches_performed', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('profile_updates', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('engagement_score', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('conversion_events', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('revenue_generated', sa.DECIMAL(precision=15, scale=2), nullable=False, server_default='0.00'),
        sa.Column('device_type', sa.String(length=50), nullable=True),
        sa.Column('browser_type', sa.String(length=50), nullable=True),
        sa.Column('location_country', sa.String(length=2), nullable=True),
        sa.Column('location_city', sa.String(length=100), nullable=True),
        sa.Column('referral_source', sa.String(length=255), nullable=True),
        sa.Column('custom_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.CheckConstraint('page_views >= 0', name='ck_user_analytics_page_views_positive'),
        sa.CheckConstraint('session_duration_minutes >= 0', name='ck_user_analytics_session_duration_positive'),
        sa.CheckConstraint('actions_taken >= 0', name='ck_user_analytics_actions_positive'),
        sa.CheckConstraint('engagement_score >= 0 AND engagement_score <= 100', name='ck_user_analytics_engagement_score_range'),
        sa.CheckConstraint('revenue_generated >= 0', name='ck_user_analytics_revenue_positive'),
        sa.CheckConstraint('period_start < period_end', name='ck_user_analytics_period_valid')
    )

    # Create VendorAnalytics table
    op.create_table('vendor_analytics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('timeframe', sa.Enum('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', name='analyticstimeframe'), nullable=False),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
        sa.Column('profile_views', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('service_views', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('booking_requests', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('bookings_completed', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('revenue_earned', sa.DECIMAL(precision=15, scale=2), nullable=False, server_default='0.00'),
        sa.Column('average_rating', sa.DECIMAL(precision=3, scale=2), nullable=True),
        sa.Column('response_time_hours', sa.DECIMAL(precision=8, scale=2), nullable=True),
        sa.Column('cancellation_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('repeat_customer_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('efficiency_score', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('growth_rate', sa.DECIMAL(precision=8, scale=4), nullable=False, server_default='0.0000'),
        sa.Column('market_share', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('customer_acquisition_cost', sa.DECIMAL(precision=10, scale=2), nullable=True),
        sa.Column('lifetime_value', sa.DECIMAL(precision=15, scale=2), nullable=True),
        sa.Column('top_services', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('performance_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('custom_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.CheckConstraint('profile_views >= 0', name='ck_vendor_analytics_profile_views_positive'),
        sa.CheckConstraint('service_views >= 0', name='ck_vendor_analytics_service_views_positive'),
        sa.CheckConstraint('booking_requests >= 0', name='ck_vendor_analytics_booking_requests_positive'),
        sa.CheckConstraint('bookings_completed >= 0', name='ck_vendor_analytics_bookings_completed_positive'),
        sa.CheckConstraint('revenue_earned >= 0', name='ck_vendor_analytics_revenue_positive'),
        sa.CheckConstraint('average_rating >= 0 AND average_rating <= 5', name='ck_vendor_analytics_rating_range'),
        sa.CheckConstraint('cancellation_rate >= 0 AND cancellation_rate <= 100', name='ck_vendor_analytics_cancellation_rate_range'),
        sa.CheckConstraint('repeat_customer_rate >= 0 AND repeat_customer_rate <= 100', name='ck_vendor_analytics_repeat_rate_range'),
        sa.CheckConstraint('efficiency_score >= 0 AND efficiency_score <= 100', name='ck_vendor_analytics_efficiency_range'),
        sa.CheckConstraint('market_share >= 0 AND market_share <= 100', name='ck_vendor_analytics_market_share_range'),
        sa.CheckConstraint('period_start < period_end', name='ck_vendor_analytics_period_valid')
    )

    # Create BookingAnalytics table
    op.create_table('booking_analytics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('timeframe', sa.Enum('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', name='analyticstimeframe'), nullable=False),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
        sa.Column('total_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('completed_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('cancelled_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('pending_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('total_revenue', sa.DECIMAL(precision=15, scale=2), nullable=False, server_default='0.00'),
        sa.Column('average_booking_value', sa.DECIMAL(precision=10, scale=2), nullable=False, server_default='0.00'),
        sa.Column('conversion_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('overall_conversion_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, server_default='0.00'),
        sa.Column('top_countries', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('top_cities', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('device_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('payment_method_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('custom_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.CheckConstraint('total_bookings >= 0', name='ck_booking_analytics_total_positive'),
        sa.CheckConstraint('completed_bookings >= 0', name='ck_booking_analytics_completed_positive'),
        sa.CheckConstraint('cancelled_bookings >= 0', name='ck_booking_analytics_cancelled_positive'),
        sa.CheckConstraint('pending_bookings >= 0', name='ck_booking_analytics_pending_positive'),
        sa.CheckConstraint('total_revenue >= 0', name='ck_booking_analytics_revenue_positive'),
        sa.CheckConstraint('average_booking_value >= 0', name='ck_booking_analytics_avg_value_positive'),
        sa.CheckConstraint('conversion_rate >= 0 AND conversion_rate <= 100', name='ck_booking_analytics_conversion_range'),
        sa.CheckConstraint('overall_conversion_rate >= 0 AND overall_conversion_rate <= 100', name='ck_booking_analytics_overall_conversion_range'),
        sa.CheckConstraint('period_start < period_end', name='ck_booking_analytics_period_valid')
    )

    # Create SystemMetrics table
    op.create_table('system_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('metric_name', sa.String(length=255), nullable=False),
        sa.Column('metric_type', sa.Enum('COUNTER', 'GAUGE', 'HISTOGRAM', 'TIMER', name='metrictype'), nullable=False),
        sa.Column('value', sa.DECIMAL(precision=20, scale=6), nullable=False),
        sa.Column('timeframe', sa.Enum('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', name='analyticstimeframe'), nullable=False),
        sa.Column('recorded_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metric_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.CheckConstraint("metric_name != ''", name='ck_system_metrics_name_not_empty')
    )

    # Create DashboardWidget table
    op.create_table('dashboard_widgets',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('widget_type', sa.String(length=100), nullable=False),
        sa.Column('data_source', sa.String(length=255), nullable=False),
        sa.Column('configuration', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('position_x', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('position_y', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('width', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('height', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('refresh_interval_seconds', sa.Integer(), nullable=False, server_default='300'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.CheckConstraint("name != ''", name='ck_dashboard_widgets_name_not_empty'),
        sa.CheckConstraint("widget_type != ''", name='ck_dashboard_widgets_type_not_empty'),
        sa.CheckConstraint("data_source != ''", name='ck_dashboard_widgets_source_not_empty'),
        sa.CheckConstraint('position_x >= 0', name='ck_dashboard_widgets_position_x_positive'),
        sa.CheckConstraint('position_y >= 0', name='ck_dashboard_widgets_position_y_positive'),
        sa.CheckConstraint('width > 0', name='ck_dashboard_widgets_width_positive'),
        sa.CheckConstraint('height > 0', name='ck_dashboard_widgets_height_positive'),
        sa.CheckConstraint('refresh_interval_seconds > 0', name='ck_dashboard_widgets_refresh_positive')
    )

    # Create KPIDefinition table
    op.create_table('kpi_definitions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('category', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('calculation_method', sa.String(length=100), nullable=False),
        sa.Column('data_source', sa.String(length=255), nullable=False),
        sa.Column('target_value', sa.DECIMAL(precision=15, scale=4), nullable=True),
        sa.Column('warning_threshold', sa.DECIMAL(precision=15, scale=4), nullable=True),
        sa.Column('critical_threshold', sa.DECIMAL(precision=15, scale=4), nullable=True),
        sa.Column('unit_of_measurement', sa.String(length=50), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('configuration', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('name', 'category', name='uq_kpi_definitions_name_category'),
        sa.CheckConstraint("name != ''", name='ck_kpi_definitions_name_not_empty'),
        sa.CheckConstraint("category != ''", name='ck_kpi_definitions_category_not_empty'),
        sa.CheckConstraint("calculation_method != ''", name='ck_kpi_definitions_method_not_empty'),
        sa.CheckConstraint("data_source != ''", name='ck_kpi_definitions_source_not_empty')
    )

    # Create performance-optimized indexes for <200ms query targets

    # UserAnalytics indexes
    op.create_index('ix_user_analytics_user_id', 'user_analytics', ['user_id'])
    op.create_index('ix_user_analytics_timeframe', 'user_analytics', ['timeframe'])
    op.create_index('ix_user_analytics_period_start', 'user_analytics', ['period_start'])
    op.create_index('ix_user_analytics_period_end', 'user_analytics', ['period_end'])
    op.create_index('ix_user_analytics_user_timeframe', 'user_analytics', ['user_id', 'timeframe'])
    op.create_index('ix_user_analytics_user_period', 'user_analytics', ['user_id', 'period_start', 'period_end'])
    op.create_index('ix_user_analytics_engagement_score', 'user_analytics', ['engagement_score'])
    op.create_index('ix_user_analytics_revenue_generated', 'user_analytics', ['revenue_generated'])
    op.create_index('ix_user_analytics_location_country', 'user_analytics', ['location_country'])
    op.create_index('ix_user_analytics_device_type', 'user_analytics', ['device_type'])
    # GIN index for JSONB custom_metrics
    op.create_index('ix_user_analytics_custom_metrics_gin', 'user_analytics', ['custom_metrics'], postgresql_using='gin')

    # VendorAnalytics indexes
    op.create_index('ix_vendor_analytics_vendor_id', 'vendor_analytics', ['vendor_id'])
    op.create_index('ix_vendor_analytics_timeframe', 'vendor_analytics', ['timeframe'])
    op.create_index('ix_vendor_analytics_period_start', 'vendor_analytics', ['period_start'])
    op.create_index('ix_vendor_analytics_period_end', 'vendor_analytics', ['period_end'])
    op.create_index('ix_vendor_analytics_vendor_timeframe', 'vendor_analytics', ['vendor_id', 'timeframe'])
    op.create_index('ix_vendor_analytics_vendor_period', 'vendor_analytics', ['vendor_id', 'period_start', 'period_end'])
    op.create_index('ix_vendor_analytics_revenue_earned', 'vendor_analytics', ['revenue_earned'])
    op.create_index('ix_vendor_analytics_efficiency_score', 'vendor_analytics', ['efficiency_score'])
    op.create_index('ix_vendor_analytics_average_rating', 'vendor_analytics', ['average_rating'])
    op.create_index('ix_vendor_analytics_growth_rate', 'vendor_analytics', ['growth_rate'])
    # GIN indexes for JSONB fields
    op.create_index('ix_vendor_analytics_top_services_gin', 'vendor_analytics', ['top_services'], postgresql_using='gin')
    op.create_index('ix_vendor_analytics_performance_metrics_gin', 'vendor_analytics', ['performance_metrics'], postgresql_using='gin')
    op.create_index('ix_vendor_analytics_custom_metrics_gin', 'vendor_analytics', ['custom_metrics'], postgresql_using='gin')

    # BookingAnalytics indexes
    op.create_index('ix_booking_analytics_timeframe', 'booking_analytics', ['timeframe'])
    op.create_index('ix_booking_analytics_period_start', 'booking_analytics', ['period_start'])
    op.create_index('ix_booking_analytics_period_end', 'booking_analytics', ['period_end'])
    op.create_index('ix_booking_analytics_timeframe_period', 'booking_analytics', ['timeframe', 'period_start', 'period_end'])
    op.create_index('ix_booking_analytics_total_revenue', 'booking_analytics', ['total_revenue'])
    op.create_index('ix_booking_analytics_conversion_rate', 'booking_analytics', ['conversion_rate'])
    op.create_index('ix_booking_analytics_overall_conversion_rate', 'booking_analytics', ['overall_conversion_rate'])
    op.create_index('ix_booking_analytics_total_bookings', 'booking_analytics', ['total_bookings'])
    # GIN indexes for JSONB fields
    op.create_index('ix_booking_analytics_top_countries_gin', 'booking_analytics', ['top_countries'], postgresql_using='gin')
    op.create_index('ix_booking_analytics_top_cities_gin', 'booking_analytics', ['top_cities'], postgresql_using='gin')
    op.create_index('ix_booking_analytics_device_breakdown_gin', 'booking_analytics', ['device_breakdown'], postgresql_using='gin')
    op.create_index('ix_booking_analytics_payment_breakdown_gin', 'booking_analytics', ['payment_method_breakdown'], postgresql_using='gin')
    op.create_index('ix_booking_analytics_custom_metrics_gin', 'booking_analytics', ['custom_metrics'], postgresql_using='gin')

    # SystemMetrics indexes
    op.create_index('ix_system_metrics_metric_name', 'system_metrics', ['metric_name'])
    op.create_index('ix_system_metrics_metric_type', 'system_metrics', ['metric_type'])
    op.create_index('ix_system_metrics_timeframe', 'system_metrics', ['timeframe'])
    op.create_index('ix_system_metrics_recorded_at', 'system_metrics', ['recorded_at'])
    op.create_index('ix_system_metrics_name_timeframe', 'system_metrics', ['metric_name', 'timeframe'])
    op.create_index('ix_system_metrics_name_recorded', 'system_metrics', ['metric_name', 'recorded_at'])
    op.create_index('ix_system_metrics_type_recorded', 'system_metrics', ['metric_type', 'recorded_at'])
    op.create_index('ix_system_metrics_value', 'system_metrics', ['value'])
    # GIN indexes for JSONB fields
    op.create_index('ix_system_metrics_tags_gin', 'system_metrics', ['tags'], postgresql_using='gin')
    op.create_index('ix_system_metrics_metadata_gin', 'system_metrics', ['metric_metadata'], postgresql_using='gin')

    # DashboardWidget indexes
    op.create_index('ix_dashboard_widgets_name', 'dashboard_widgets', ['name'])
    op.create_index('ix_dashboard_widgets_widget_type', 'dashboard_widgets', ['widget_type'])
    op.create_index('ix_dashboard_widgets_data_source', 'dashboard_widgets', ['data_source'])
    op.create_index('ix_dashboard_widgets_is_active', 'dashboard_widgets', ['is_active'])
    op.create_index('ix_dashboard_widgets_position', 'dashboard_widgets', ['position_x', 'position_y'])
    op.create_index('ix_dashboard_widgets_type_active', 'dashboard_widgets', ['widget_type', 'is_active'])
    # GIN index for JSONB configuration
    op.create_index('ix_dashboard_widgets_configuration_gin', 'dashboard_widgets', ['configuration'], postgresql_using='gin')

    # KPIDefinition indexes
    op.create_index('ix_kpi_definitions_name', 'kpi_definitions', ['name'])
    op.create_index('ix_kpi_definitions_category', 'kpi_definitions', ['category'])
    op.create_index('ix_kpi_definitions_calculation_method', 'kpi_definitions', ['calculation_method'])
    op.create_index('ix_kpi_definitions_data_source', 'kpi_definitions', ['data_source'])
    op.create_index('ix_kpi_definitions_is_active', 'kpi_definitions', ['is_active'])
    op.create_index('ix_kpi_definitions_category_active', 'kpi_definitions', ['category', 'is_active'])
    op.create_index('ix_kpi_definitions_target_value', 'kpi_definitions', ['target_value'])
    # GIN index for JSONB configuration
    op.create_index('ix_kpi_definitions_configuration_gin', 'kpi_definitions', ['configuration'], postgresql_using='gin')


def downgrade() -> None:
    """Downgrade database schema by removing analytics and performance system tables."""

    # Drop indexes first (PostgreSQL automatically drops them with tables, but explicit for clarity)

    # Drop KPIDefinition indexes
    op.drop_index('ix_kpi_definitions_configuration_gin', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_target_value', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_category_active', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_is_active', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_data_source', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_calculation_method', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_category', table_name='kpi_definitions')
    op.drop_index('ix_kpi_definitions_name', table_name='kpi_definitions')

    # Drop DashboardWidget indexes
    op.drop_index('ix_dashboard_widgets_configuration_gin', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_type_active', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_position', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_is_active', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_data_source', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_widget_type', table_name='dashboard_widgets')
    op.drop_index('ix_dashboard_widgets_name', table_name='dashboard_widgets')

    # Drop SystemMetrics indexes
    op.drop_index('ix_system_metrics_metadata_gin', table_name='system_metrics')
    op.drop_index('ix_system_metrics_tags_gin', table_name='system_metrics')
    op.drop_index('ix_system_metrics_value', table_name='system_metrics')
    op.drop_index('ix_system_metrics_type_recorded', table_name='system_metrics')
    op.drop_index('ix_system_metrics_name_recorded', table_name='system_metrics')
    op.drop_index('ix_system_metrics_name_timeframe', table_name='system_metrics')
    op.drop_index('ix_system_metrics_recorded_at', table_name='system_metrics')
    op.drop_index('ix_system_metrics_timeframe', table_name='system_metrics')
    op.drop_index('ix_system_metrics_metric_type', table_name='system_metrics')
    op.drop_index('ix_system_metrics_metric_name', table_name='system_metrics')

    # Drop BookingAnalytics indexes
    op.drop_index('ix_booking_analytics_custom_metrics_gin', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_payment_breakdown_gin', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_device_breakdown_gin', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_top_cities_gin', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_top_countries_gin', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_total_bookings', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_overall_conversion_rate', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_conversion_rate', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_total_revenue', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_timeframe_period', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_period_end', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_period_start', table_name='booking_analytics')
    op.drop_index('ix_booking_analytics_timeframe', table_name='booking_analytics')

    # Drop VendorAnalytics indexes
    op.drop_index('ix_vendor_analytics_custom_metrics_gin', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_performance_metrics_gin', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_top_services_gin', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_growth_rate', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_average_rating', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_efficiency_score', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_revenue_earned', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_vendor_period', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_vendor_timeframe', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_period_end', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_period_start', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_timeframe', table_name='vendor_analytics')
    op.drop_index('ix_vendor_analytics_vendor_id', table_name='vendor_analytics')

    # Drop UserAnalytics indexes
    op.drop_index('ix_user_analytics_custom_metrics_gin', table_name='user_analytics')
    op.drop_index('ix_user_analytics_device_type', table_name='user_analytics')
    op.drop_index('ix_user_analytics_location_country', table_name='user_analytics')
    op.drop_index('ix_user_analytics_revenue_generated', table_name='user_analytics')
    op.drop_index('ix_user_analytics_engagement_score', table_name='user_analytics')
    op.drop_index('ix_user_analytics_user_period', table_name='user_analytics')
    op.drop_index('ix_user_analytics_user_timeframe', table_name='user_analytics')
    op.drop_index('ix_user_analytics_period_end', table_name='user_analytics')
    op.drop_index('ix_user_analytics_period_start', table_name='user_analytics')
    op.drop_index('ix_user_analytics_timeframe', table_name='user_analytics')
    op.drop_index('ix_user_analytics_user_id', table_name='user_analytics')

    # Drop tables in reverse order of creation (to handle dependencies)
    op.drop_table('kpi_definitions')
    op.drop_table('dashboard_widgets')
    op.drop_table('system_metrics')
    op.drop_table('booking_analytics')
    op.drop_table('vendor_analytics')
    op.drop_table('user_analytics')

    # Drop custom enum types
    op.execute('DROP TYPE IF EXISTS metrictype')
    op.execute('DROP TYPE IF EXISTS analyticstimeframe')
