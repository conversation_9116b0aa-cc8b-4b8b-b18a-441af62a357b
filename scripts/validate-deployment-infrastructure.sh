#!/bin/bash

# Culture Connect Backend - Deployment Infrastructure Validation Script
# This script validates that all deployment infrastructure components are properly configured

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation counters
CHECKS_PASSED=0
CHECKS_FAILED=0
CHECKS_WARNING=0

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    CHECKS_WARNING=$((CHECKS_WARNING + 1))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    CHECKS_FAILED=$((CHECKS_FAILED + 1))
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

validate_environment_files() {
    log_info "Validating environment configuration files..."
    
    local environments=("development" "staging" "production")
    
    for env in "${environments[@]}"; do
        local env_file="$PROJECT_ROOT/environments/$env/.env.$env"
        
        if [[ -f "$env_file" ]]; then
            log_success "Environment file exists: $env"
            
            # Check for required variables
            local required_vars=("CC_ENVIRONMENT" "CC_DATABASE_URL" "CC_REDIS_URL" "CC_SECRET_KEY")
            local missing_vars=()
            
            for var in "${required_vars[@]}"; do
                if ! grep -q "^$var=" "$env_file"; then
                    missing_vars+=("$var")
                fi
            done
            
            if [[ ${#missing_vars[@]} -eq 0 ]]; then
                log_success "All required variables present in $env environment"
            else
                log_error "Missing variables in $env: ${missing_vars[*]}"
            fi
        else
            log_error "Environment file missing: $env"
        fi
    done
}

validate_docker_configurations() {
    log_info "Validating Docker configurations..."
    
    # Check development Docker Compose
    local dev_compose="$PROJECT_ROOT/docker/development/docker-compose.yml"
    if [[ -f "$dev_compose" ]]; then
        log_success "Development Docker Compose exists"
        
        # Validate Docker Compose syntax
        if docker-compose -f "$dev_compose" config > /dev/null 2>&1; then
            log_success "Development Docker Compose syntax is valid"
        else
            log_error "Development Docker Compose syntax is invalid"
        fi
    else
        log_error "Development Docker Compose missing"
    fi
    
    # Check staging Docker Compose
    local staging_compose="$PROJECT_ROOT/docker/staging/docker-compose.swarm.yml"
    if [[ -f "$staging_compose" ]]; then
        log_success "Staging Docker Compose exists"
    else
        log_error "Staging Docker Compose missing"
    fi
    
    # Check production Docker Compose
    local prod_compose="$PROJECT_ROOT/docker/production/docker-compose.prod.yml"
    if [[ -f "$prod_compose" ]]; then
        log_success "Production Docker Compose exists"
    else
        log_error "Production Docker Compose missing"
    fi
}

validate_deployment_scripts() {
    log_info "Validating deployment scripts..."
    
    local scripts=(
        "scripts/deployment/development/deploy-development.sh"
        "scripts/deployment/staging/deploy-staging.sh"
        "scripts/deployment/production/deploy-production.sh"
        "scripts/deployment/health-check.sh"
    )
    
    for script in "${scripts[@]}"; do
        local script_path="$PROJECT_ROOT/$script"
        
        if [[ -f "$script_path" ]]; then
            if [[ -x "$script_path" ]]; then
                log_success "Deployment script exists and is executable: $(basename "$script")"
            else
                log_warning "Deployment script exists but is not executable: $(basename "$script")"
            fi
        else
            log_error "Deployment script missing: $(basename "$script")"
        fi
    done
}

validate_kubernetes_manifests() {
    log_info "Validating Kubernetes manifests..."
    
    local k8s_dir="$PROJECT_ROOT/infrastructure/kubernetes"
    
    if [[ -d "$k8s_dir" ]]; then
        log_success "Kubernetes directory exists"
        
        # Check for base manifests
        local base_manifests=(
            "base/deployment.yaml"
            "base/service.yaml"
            "base/configmap.yaml"
            "base/secret.yaml"
        )
        
        for manifest in "${base_manifests[@]}"; do
            if [[ -f "$k8s_dir/$manifest" ]]; then
                log_success "Kubernetes manifest exists: $manifest"
            else
                log_warning "Kubernetes manifest missing: $manifest"
            fi
        done
    else
        log_error "Kubernetes directory missing"
    fi
}

validate_terraform_configurations() {
    log_info "Validating Terraform configurations..."
    
    local terraform_dir="$PROJECT_ROOT/infrastructure/terraform"
    
    if [[ -d "$terraform_dir" ]]; then
        log_success "Terraform directory exists"
        
        # Check for provider configurations
        local providers=("aws" "gcp" "azure" "hetzner")
        
        for provider in "${providers[@]}"; do
            if [[ -f "$terraform_dir/$provider/main.tf" ]]; then
                log_success "Terraform configuration exists: $provider"
            else
                log_warning "Terraform configuration missing: $provider"
            fi
        done
    else
        log_error "Terraform directory missing"
    fi
}

validate_monitoring_configurations() {
    log_info "Validating monitoring configurations..."
    
    local monitoring_dir="$PROJECT_ROOT/infrastructure/monitoring"
    
    if [[ -d "$monitoring_dir" ]]; then
        log_success "Monitoring directory exists"
        
        # Check for monitoring components
        local components=("prometheus" "grafana" "alertmanager")
        
        for component in "${components[@]}"; do
            if [[ -d "$monitoring_dir/$component" ]]; then
                log_success "Monitoring component exists: $component"
            else
                log_warning "Monitoring component missing: $component"
            fi
        done
    else
        log_error "Monitoring directory missing"
    fi
}

validate_cicd_pipelines() {
    log_info "Validating CI/CD pipelines..."
    
    local github_dir="$PROJECT_ROOT/.github/workflows"
    
    if [[ -d "$github_dir" ]]; then
        log_success "GitHub workflows directory exists"
        
        # Check for workflow files
        local workflows=(
            "ci-cd-pipeline.yml"
            "deploy-development.yml"
            "deploy-staging.yml"
            "deploy-production.yml"
            "security-scan.yml"
        )
        
        for workflow in "${workflows[@]}"; do
            if [[ -f "$github_dir/$workflow" ]]; then
                log_success "GitHub workflow exists: $workflow"
            else
                log_warning "GitHub workflow missing: $workflow"
            fi
        done
    else
        log_error "GitHub workflows directory missing"
    fi
}

validate_database_scripts() {
    log_info "Validating database scripts..."
    
    local db_scripts=(
        "scripts/init-db.sql"
        "scripts/postgres-prod-config.sql"
    )
    
    for script in "${db_scripts[@]}"; do
        if [[ -f "$PROJECT_ROOT/$script" ]]; then
            log_success "Database script exists: $(basename "$script")"
        else
            log_warning "Database script missing: $(basename "$script")"
        fi
    done
}

validate_configuration_files() {
    log_info "Validating configuration files..."
    
    local config_files=(
        "config/redis/redis-dev.conf"
        "Dockerfile"
        "requirements.txt"
        "alembic.ini"
    )
    
    for config in "${config_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$config" ]]; then
            log_success "Configuration file exists: $(basename "$config")"
        else
            log_error "Configuration file missing: $(basename "$config")"
        fi
    done
}

validate_documentation() {
    log_info "Validating deployment documentation..."
    
    local docs=(
        "docs/deployment/deployment-guide.md"
        "docs/production-deployment.md"
        "README.md"
    )
    
    for doc in "${docs[@]}"; do
        if [[ -f "$PROJECT_ROOT/$doc" ]]; then
            log_success "Documentation exists: $(basename "$doc")"
        else
            log_warning "Documentation missing: $(basename "$doc")"
        fi
    done
}

# =============================================================================
# MAIN VALIDATION FUNCTION
# =============================================================================

main() {
    log_info "Starting Culture Connect Backend deployment infrastructure validation"
    log_info "Project root: $PROJECT_ROOT"
    echo
    
    # Run all validations
    validate_environment_files
    echo
    validate_docker_configurations
    echo
    validate_deployment_scripts
    echo
    validate_kubernetes_manifests
    echo
    validate_terraform_configurations
    echo
    validate_monitoring_configurations
    echo
    validate_cicd_pipelines
    echo
    validate_database_scripts
    echo
    validate_configuration_files
    echo
    validate_documentation
    echo
    
    # Summary
    log_info "=== Validation Summary ==="
    log_info "Checks Passed: $CHECKS_PASSED"
    log_info "Checks Failed: $CHECKS_FAILED"
    log_info "Warnings: $CHECKS_WARNING"
    log_info "Total Checks: $((CHECKS_PASSED + CHECKS_FAILED + CHECKS_WARNING))"
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        log_success "All critical validations passed! Deployment infrastructure is ready."
        if [[ $CHECKS_WARNING -gt 0 ]]; then
            log_warning "$CHECKS_WARNING warning(s) found. Review and address if needed."
        fi
        return 0
    else
        log_error "$CHECKS_FAILED critical validation(s) failed. Address these issues before deployment."
        return 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
