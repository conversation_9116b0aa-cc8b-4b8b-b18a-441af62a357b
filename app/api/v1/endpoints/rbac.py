"""
RBAC management endpoints for Culture Connect Backend API.

This module provides comprehensive RBAC administration endpoints:
- Role and permission management
- Permission grant administration
- Access control audit logs and analytics
- User permission summaries

Implements Task 2.2.3 requirements for complete role-based access control
with production-grade admin endpoints and security features.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.rbac_service import RBACService
from app.schemas.rbac_schemas import (
    PermissionCheck, PermissionCheckResponse, RolePermissions,
    PermissionGrantCreate, PermissionGrantResponse, PermissionGrantUpdate,
    AccessControlLogResponse, UserPermissionSummary, AccessControlStats
)
from app.schemas.auth import UserResponse, MessageResponse
from app.core.permissions import (
    require_permission, require_admin, get_current_admin_user
)
from app.api.v1.auth import get_current_user
from app.core.security import Permission, UserRole
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.post(
    "/check-permission",
    response_model=PermissionCheckResponse,
    status_code=status.HTTP_200_OK,
    summary="Check user permission",
    description="Check if current user has specific permission with audit logging"
)
async def check_user_permission(
    permission_check: PermissionCheck,
    request: Request,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> PermissionCheckResponse:
    """
    Check if current user has specific permission.

    Args:
        permission_check: Permission check request
        request: HTTP request for audit logging
        current_user: Current authenticated user
        db: Database session

    Returns:
        PermissionCheckResponse: Permission check result with audit information

    Raises:
        HTTPException: If permission check fails
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Get request details for audit logging
        endpoint = str(request.url.path)
        method = request.method
        ip_address = request.client.host if hasattr(request, 'client') else None
        user_agent = request.headers.get('user-agent')

        # Check permission with comprehensive audit logging
        permission_result = await rbac_service.check_permission(
            user_id=str(current_user.id),
            user_role=current_user.role,
            permission=permission_check.permission,
            resource_type=permission_check.resource_type,
            resource_id=permission_check.resource_id,
            endpoint=endpoint,
            method=method,
            ip_address=ip_address,
            user_agent=user_agent
        )

        logger.info(
            f"Permission check completed: {current_user.email} -> {permission_check.permission}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "permission": permission_check.permission,
                "result": permission_result.has_permission
            }
        )

        return permission_result

    except Exception as e:
        logger.error(
            f"Permission check failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "permission": permission_check.permission,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check permission"
        )


@router.get(
    "/roles/{role}/permissions",
    response_model=RolePermissions,
    status_code=status.HTTP_200_OK,
    summary="Get role permissions",
    description="Get comprehensive permissions for a specific role"
)
@require_permission(Permission.VIEW_ANALYTICS)
async def get_role_permissions(
    role: str,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> RolePermissions:
    """
    Get comprehensive permissions for a specific role.

    Args:
        role: Role to get permissions for
        current_user: Current authenticated user
        db: Database session

    Returns:
        RolePermissions: Role permissions with hierarchy information

    Raises:
        HTTPException: If role is invalid or retrieval fails
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Get role permissions
        role_permissions = await rbac_service.get_role_permissions(role)

        logger.info(
            f"Role permissions retrieved: {role}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "role": role,
                "permission_count": len(role_permissions.all_permissions)
            }
        )

        return role_permissions

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get role permissions: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "role": role,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve role permissions"
        )


@router.post(
    "/permissions/grant",
    response_model=PermissionGrantResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Grant permission",
    description="Grant specific permission to user or role"
)
@require_admin()
async def grant_permission(
    grant_data: PermissionGrantCreate,
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> PermissionGrantResponse:
    """
    Grant specific permission to user or role.

    Args:
        grant_data: Permission grant data
        current_user: Current admin user
        db: Database session

    Returns:
        PermissionGrantResponse: Created permission grant

    Raises:
        HTTPException: If grant creation fails or validation errors
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Grant permission
        permission_grant = await rbac_service.grant_permission(
            grant_data=grant_data,
            granted_by=str(current_user.id)
        )

        logger.info(
            f"Permission granted by admin: {grant_data.permission} to {grant_data.user_id or grant_data.role}",
            extra={
                "correlation_id": correlation_id.get(''),
                "admin_user_id": current_user.id,
                "permission": grant_data.permission,
                "target_user_id": grant_data.user_id,
                "target_role": grant_data.role
            }
        )

        return permission_grant

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to grant permission: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "admin_user_id": current_user.id,
                "permission": grant_data.permission,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to grant permission"
        )


@router.get(
    "/users/{user_id}/permissions",
    response_model=UserPermissionSummary,
    status_code=status.HTTP_200_OK,
    summary="Get user permission summary",
    description="Get comprehensive permission summary for a user"
)
@require_permission(Permission.READ_USER)
async def get_user_permission_summary(
    user_id: str,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserPermissionSummary:
    """
    Get comprehensive permission summary for a user.

    Args:
        user_id: User ID to get permissions for
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserPermissionSummary: Complete user permission information

    Raises:
        HTTPException: If user not found or access denied
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Check if user can access this information
        # Users can view their own permissions, admins can view any user's permissions
        if str(current_user.id) != user_id and current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot access other user's permissions"
            )

        # Get user permission summary
        permission_summary = await rbac_service.get_user_permission_summary(user_id)

        logger.info(
            f"User permission summary retrieved: {user_id}",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "target_user_id": user_id,
                "total_permissions": len(permission_summary.all_permissions)
            }
        )

        return permission_summary

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get user permission summary: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "target_user_id": user_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user permission summary"
        )


@router.get(
    "/access-logs",
    response_model=List[AccessControlLogResponse],
    status_code=status.HTTP_200_OK,
    summary="Get access control logs",
    description="Get access control audit logs with filtering"
)
@require_permission(Permission.VIEW_LOGS)
async def get_access_control_logs(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    endpoint: Optional[str] = Query(None, description="Filter by endpoint"),
    decision: Optional[str] = Query(None, description="Filter by decision (granted/denied/error)"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[AccessControlLogResponse]:
    """
    Get access control audit logs with filtering.

    Args:
        user_id: Filter by user ID
        endpoint: Filter by endpoint
        decision: Filter by decision
        start_date: Filter by start date
        end_date: Filter by end date
        limit: Maximum number of results
        offset: Number of results to skip
        current_user: Current authenticated user
        db: Database session

    Returns:
        List[AccessControlLogResponse]: Access control logs

    Raises:
        HTTPException: If log retrieval fails
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Convert decision string to enum if provided
        decision_enum = None
        if decision:
            from app.models.rbac_models import AccessDecision
            try:
                decision_enum = AccessDecision(decision.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid decision value. Must be one of: granted, denied, error"
                )

        # Get access control logs
        logs, total_count = await rbac_service.rbac_repository.get_access_control_logs(
            user_id=user_id,
            endpoint=endpoint,
            decision=decision_enum,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )

        # Convert to response format
        log_responses = []
        for log in logs:
            log_responses.append(AccessControlLogResponse(
                id=str(log.id),
                user_id=str(log.user_id) if log.user_id else None,
                user_role=log.user_role,
                endpoint=log.endpoint,
                method=log.method,
                required_permission=log.required_permission,
                resource_type=log.resource_type,
                resource_id=log.resource_id,
                decision=log.decision,
                ip_address=log.ip_address,
                user_agent=log.user_agent,
                correlation_id=log.correlation_id,
                request_timestamp=log.request_timestamp,
                response_time_ms=log.response_time_ms,
                reason=log.reason,
                metadata=log.metadata,
                created_at=log.created_at,
                updated_at=log.updated_at
            ))

        logger.info(
            f"Access control logs retrieved: {len(log_responses)} of {total_count}",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "total_count": total_count,
                "returned_count": len(log_responses)
            }
        )

        return log_responses

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get access control logs: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve access control logs"
        )


@router.get(
    "/stats",
    response_model=AccessControlStats,
    status_code=status.HTTP_200_OK,
    summary="Get access control statistics",
    description="Get access control statistics for monitoring and analytics"
)
@require_permission(Permission.VIEW_ANALYTICS)
async def get_access_control_stats(
    start_date: Optional[datetime] = Query(None, description="Statistics start date"),
    end_date: Optional[datetime] = Query(None, description="Statistics end date"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccessControlStats:
    """
    Get access control statistics for monitoring and analytics.

    Args:
        start_date: Statistics start date (defaults to 24 hours ago)
        end_date: Statistics end date (defaults to now)
        current_user: Current authenticated user
        db: Database session

    Returns:
        AccessControlStats: Access control statistics

    Raises:
        HTTPException: If statistics retrieval fails
    """
    try:
        # Initialize RBAC service
        rbac_service = RBACService(db)

        # Get access control statistics
        stats = await rbac_service.get_access_control_stats(
            start_date=start_date,
            end_date=end_date
        )

        logger.info(
            f"Access control stats retrieved",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "period_start": stats.period_start,
                "period_end": stats.period_end,
                "total_requests": stats.total_requests
            }
        )

        return stats

    except Exception as e:
        logger.error(
            f"Failed to get access control stats: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "requesting_user_id": current_user.id,
                "start_date": start_date,
                "end_date": end_date,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve access control statistics"
        )
