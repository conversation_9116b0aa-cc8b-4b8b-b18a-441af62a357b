"""
Availability management models for vendor scheduling and booking conflict prevention.

This module provides comprehensive availability management with support for:
- Vendor-specific availability configurations
- Individual time slots with booking capacity
- Recurring availability patterns (daily, weekly, monthly)
- Exception handling for recurring patterns
- Timezone support for global vendors
- Integration with booking system for conflict prevention

Production-grade implementation following established model patterns.
"""

from datetime import date, datetime, time
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Date, Time, Text, 
    ForeignKey, Index, CheckConstraint, UniqueConstraint, func
)
from sqlalchemy.orm import relationship, validates
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.database import Base


class VendorAvailability(Base):
    """
    Vendor's general availability configuration and settings.
    
    This model stores the overall availability configuration for a vendor,
    including timezone settings, booking rules, and slot configurations.
    Can be service-specific or apply to all vendor services.
    """
    __tablename__ = "vendor_availability"
    
    # Primary identification
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    vendor_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    service_id = Column(Integer, nullable=True, index=True)  # Optional: service-specific availability
    
    # Timezone and booking configuration
    timezone = Column(String(50), nullable=False, default="UTC")
    advance_booking_days = Column(Integer, nullable=False, default=30)
    min_booking_notice_hours = Column(Integer, nullable=False, default=24)
    max_booking_notice_days = Column(Integer, nullable=False, default=90)
    
    # Slot configuration
    default_slot_duration_minutes = Column(Integer, nullable=False, default=60)
    buffer_time_minutes = Column(Integer, nullable=False, default=15)
    max_daily_bookings = Column(Integer, nullable=True)  # Optional daily limit
    
    # Availability window
    earliest_booking_time = Column(Time, nullable=False, default=time(9, 0))  # 9:00 AM
    latest_booking_time = Column(Time, nullable=False, default=time(17, 0))   # 5:00 PM
    
    # Status and metadata
    is_active = Column(Boolean, nullable=False, default=True)
    notes = Column(Text, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    
    # Relationships
    availability_slots = relationship("AvailabilitySlot", back_populates="vendor_availability", cascade="all, delete-orphan")
    recurring_patterns = relationship("RecurringAvailability", back_populates="vendor_availability", cascade="all, delete-orphan")
    exceptions = relationship("AvailabilityException", back_populates="vendor_availability", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('advance_booking_days > 0', name='check_advance_booking_positive'),
        CheckConstraint('min_booking_notice_hours >= 0', name='check_min_notice_non_negative'),
        CheckConstraint('max_booking_notice_days > 0', name='check_max_notice_positive'),
        CheckConstraint('default_slot_duration_minutes > 0', name='check_slot_duration_positive'),
        CheckConstraint('buffer_time_minutes >= 0', name='check_buffer_time_non_negative'),
        CheckConstraint('max_daily_bookings > 0', name='check_daily_bookings_positive'),
        CheckConstraint('earliest_booking_time < latest_booking_time', name='check_booking_time_order'),
        UniqueConstraint('vendor_id', 'service_id', name='unique_vendor_service_availability'),
        Index('idx_vendor_availability_vendor_service', 'vendor_id', 'service_id'),
        Index('idx_vendor_availability_active', 'is_active'),
    )
    
    @validates('timezone')
    def validate_timezone(self, key, timezone):
        """Validate timezone string format."""
        import pytz
        try:
            pytz.timezone(timezone)
            return timezone
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"Invalid timezone: {timezone}")
    
    def __repr__(self):
        return f"<VendorAvailability(id={self.id}, vendor_id={self.vendor_id}, service_id={self.service_id})>"


class AvailabilitySlot(Base):
    """
    Individual availability time slots for specific dates and times.
    
    This model represents specific time slots when a vendor is available
    for bookings. Supports multiple bookings per slot and tracks current
    booking count for capacity management.
    """
    __tablename__ = "availability_slots"
    
    # Primary identification
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    vendor_availability_id = Column(Integer, ForeignKey("vendor_availability.id"), nullable=False, index=True)
    
    # Time details
    date = Column(Date, nullable=False, index=True)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    
    # Capacity management
    is_available = Column(Boolean, nullable=False, default=True)
    max_bookings = Column(Integer, nullable=False, default=1)
    current_bookings = Column(Integer, nullable=False, default=0)
    
    # Slot metadata
    slot_type = Column(String(20), nullable=False, default="regular")  # regular, exception, recurring, custom
    recurring_availability_id = Column(Integer, ForeignKey("recurring_availability.id"), nullable=True, index=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    special_pricing = Column(String(100), nullable=True)  # Optional special pricing info
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    
    # Relationships
    vendor_availability = relationship("VendorAvailability", back_populates="availability_slots")
    recurring_availability = relationship("RecurringAvailability", back_populates="generated_slots")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('start_time < end_time', name='check_slot_time_order'),
        CheckConstraint('max_bookings > 0', name='check_max_bookings_positive'),
        CheckConstraint('current_bookings >= 0', name='check_current_bookings_non_negative'),
        CheckConstraint('current_bookings <= max_bookings', name='check_bookings_within_capacity'),
        CheckConstraint("slot_type IN ('regular', 'exception', 'recurring', 'custom')", name='check_valid_slot_type'),
        UniqueConstraint('vendor_availability_id', 'date', 'start_time', name='unique_vendor_slot_time'),
        Index('idx_availability_slots_date_time', 'date', 'start_time'),
        Index('idx_availability_slots_available', 'is_available'),
        Index('idx_availability_slots_capacity', 'current_bookings', 'max_bookings'),
    )
    
    @property
    def is_fully_booked(self) -> bool:
        """Check if slot is fully booked."""
        return self.current_bookings >= self.max_bookings
    
    @property
    def available_capacity(self) -> int:
        """Get remaining booking capacity."""
        return max(0, self.max_bookings - self.current_bookings)
    
    def __repr__(self):
        return f"<AvailabilitySlot(id={self.id}, date={self.date}, time={self.start_time}-{self.end_time})>"


class RecurringAvailability(Base):
    """
    Recurring availability patterns for automated slot generation.
    
    This model defines recurring patterns (daily, weekly, monthly) that
    automatically generate availability slots. Supports complex patterns
    with validity periods and capacity configuration.
    """
    __tablename__ = "recurring_availability"
    
    # Primary identification
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    vendor_availability_id = Column(Integer, ForeignKey("vendor_availability.id"), nullable=False, index=True)
    
    # Pattern configuration
    pattern_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    pattern_name = Column(String(100), nullable=True)  # Optional descriptive name
    
    # Weekly pattern (day_of_week: 0=Monday, 6=Sunday)
    day_of_week = Column(Integer, nullable=True)
    
    # Monthly pattern (day_of_month: 1-31)
    day_of_month = Column(Integer, nullable=True)
    
    # Time configuration
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    
    # Validity period
    valid_from = Column(Date, nullable=False)
    valid_until = Column(Date, nullable=True)  # NULL means indefinite
    
    # Slot configuration
    slot_duration_minutes = Column(Integer, nullable=False, default=60)
    max_bookings_per_slot = Column(Integer, nullable=False, default=1)
    buffer_time_minutes = Column(Integer, nullable=False, default=0)
    
    # Status and metadata
    is_active = Column(Boolean, nullable=False, default=True)
    auto_generate = Column(Boolean, nullable=False, default=True)
    generation_advance_days = Column(Integer, nullable=False, default=30)  # How far ahead to generate
    
    # Additional configuration
    notes = Column(Text, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    last_generated_date = Column(Date, nullable=True)  # Track last generation
    
    # Relationships
    vendor_availability = relationship("VendorAvailability", back_populates="recurring_patterns")
    generated_slots = relationship("AvailabilitySlot", back_populates="recurring_availability")
    exceptions = relationship("AvailabilityException", back_populates="recurring_availability")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('start_time < end_time', name='check_recurring_time_order'),
        CheckConstraint('slot_duration_minutes > 0', name='check_slot_duration_positive'),
        CheckConstraint('max_bookings_per_slot > 0', name='check_max_bookings_positive'),
        CheckConstraint('buffer_time_minutes >= 0', name='check_buffer_time_non_negative'),
        CheckConstraint('generation_advance_days > 0', name='check_advance_days_positive'),
        CheckConstraint("pattern_type IN ('daily', 'weekly', 'monthly')", name='check_valid_pattern_type'),
        CheckConstraint('day_of_week BETWEEN 0 AND 6', name='check_valid_day_of_week'),
        CheckConstraint('day_of_month BETWEEN 1 AND 31', name='check_valid_day_of_month'),
        Index('idx_recurring_availability_pattern', 'pattern_type', 'is_active'),
        Index('idx_recurring_availability_validity', 'valid_from', 'valid_until'),
        Index('idx_recurring_availability_generation', 'auto_generate', 'last_generated_date'),
    )
    
    @validates('pattern_type', 'day_of_week', 'day_of_month')
    def validate_pattern_configuration(self, key, value):
        """Validate pattern configuration consistency."""
        if key == 'pattern_type':
            if value not in ['daily', 'weekly', 'monthly']:
                raise ValueError(f"Invalid pattern_type: {value}")
        elif key == 'day_of_week' and value is not None:
            if not (0 <= value <= 6):
                raise ValueError(f"day_of_week must be between 0-6, got: {value}")
        elif key == 'day_of_month' and value is not None:
            if not (1 <= value <= 31):
                raise ValueError(f"day_of_month must be between 1-31, got: {value}")
        return value
    
    def __repr__(self):
        return f"<RecurringAvailability(id={self.id}, pattern={self.pattern_type}, active={self.is_active})>"


class AvailabilityException(Base):
    """
    Exceptions to recurring availability patterns.
    
    This model handles exceptions to recurring patterns, such as:
    - Unavailable dates (holidays, vacation)
    - Modified availability (different hours)
    - Custom availability for specific dates
    """
    __tablename__ = "availability_exceptions"
    
    # Primary identification
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    vendor_availability_id = Column(Integer, ForeignKey("vendor_availability.id"), nullable=False, index=True)
    recurring_availability_id = Column(Integer, ForeignKey("recurring_availability.id"), nullable=True, index=True)
    
    # Exception details
    exception_date = Column(Date, nullable=False, index=True)
    exception_type = Column(String(20), nullable=False)  # unavailable, modified, custom
    
    # Modified availability (for exception_type = 'modified' or 'custom')
    modified_start_time = Column(Time, nullable=True)
    modified_end_time = Column(Time, nullable=True)
    modified_max_bookings = Column(Integer, nullable=True)
    
    # Reason and metadata
    reason = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    
    # Relationships
    vendor_availability = relationship("VendorAvailability", back_populates="exceptions")
    recurring_availability = relationship("RecurringAvailability", back_populates="exceptions")
    
    # Constraints
    __table_args__ = (
        CheckConstraint("exception_type IN ('unavailable', 'modified', 'custom')", name='check_valid_exception_type'),
        CheckConstraint('modified_max_bookings > 0', name='check_modified_bookings_positive'),
        CheckConstraint(
            "(exception_type = 'unavailable') OR "
            "(exception_type IN ('modified', 'custom') AND modified_start_time IS NOT NULL AND modified_end_time IS NOT NULL)",
            name='check_modified_time_required'
        ),
        CheckConstraint(
            "(modified_start_time IS NULL AND modified_end_time IS NULL) OR "
            "(modified_start_time < modified_end_time)",
            name='check_modified_time_order'
        ),
        UniqueConstraint('vendor_availability_id', 'exception_date', 'recurring_availability_id', 
                        name='unique_exception_per_date'),
        Index('idx_availability_exceptions_date_type', 'exception_date', 'exception_type'),
        Index('idx_availability_exceptions_active', 'is_active'),
    )
    
    def __repr__(self):
        return f"<AvailabilityException(id={self.id}, date={self.exception_date}, type={self.exception_type})>"
