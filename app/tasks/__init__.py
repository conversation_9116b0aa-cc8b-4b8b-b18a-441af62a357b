"""
Task modules for Culture Connect Backend API.

This package provides comprehensive background task processing including:
- Email tasks: Verification, notifications, digests
- Notification tasks: Push notifications, SMS, batch processing
- Report tasks: Analytics reports, data exports
- Sync tasks: Data synchronization, conflict resolution
- Cleanup tasks: Session cleanup, log rotation, temp file cleanup
- Analytics tasks: Metrics calculation, dashboard updates
- Payment tasks: Payment processing, fraud detection

Implements Task 6.2.1 requirements for Celery task queue setup with
production-grade task organization and management.
"""

from app.core.celery_config import celery_app

# Import all task modules to register them with Celery
from . import (
    email_tasks,
    notification_tasks,
    report_tasks,
    sync_tasks,
    cleanup_tasks,
    analytics_tasks,
    payment_tasks,
)

__all__ = [
    'celery_app',
    'email_tasks',
    'notification_tasks', 
    'report_tasks',
    'sync_tasks',
    'cleanup_tasks',
    'analytics_tasks',
    'payment_tasks',
]
