"""
End-to-End Workflow Integration Tests for Culture Connect Backend API.

This module provides comprehensive end-to-end workflow validation testing including:
- Complete tourist journey validation (registration → booking → payment → communication)
- Complete vendor journey validation (onboarding → service management → booking handling)
- Cross-system data flow validation with real database transactions
- Authentication flow integration across all major systems
- Performance validation during integrated operations

Implements Phase 8.1: Integration Testing Implementation with >80% test coverage,
100% workflow success rate, and database consistency validation.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import uuid4
from unittest.mock import AsyncMock, patch

from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.user import User, UserRole
from app.models.vendor import Vendor, VendorType, VendorStatus
from app.models.booking import Booking, BookingStatus
from app.models.payment_models import Payment, PaymentStatus, PaymentProvider
from app.models.booking_communication import BookingMessage, MessageType, MessageStatus
from app.services.auth_service import AuthService
from app.services.vendor_service import VendorService
from app.services.booking_service import BookingService
from app.services.payment_service import PaymentService
from app.services.booking_communication_service import BookingCommunicationService
from app.db.session import get_async_session_context
from app.core.security import create_token_pair


@pytest.mark.integration
class TestEndToEndWorkflows:
    """Comprehensive end-to-end workflow integration tests."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client with real app."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    async def db_session(self):
        """Create database session with transaction rollback."""
        async with get_async_session_context() as session:
            # Start transaction
            transaction = await session.begin()
            try:
                yield session
            finally:
                # Rollback transaction to clean state
                await transaction.rollback()

    @pytest.fixture
    async def test_tourist_user(self, db_session: AsyncSession):
        """Create test tourist user for workflow testing."""
        user_data = {
            "email": f"tourist_{uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "Tourist",
            "role": UserRole.CUSTOMER,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor_user(self, db_session: AsyncSession):
        """Create test vendor user for workflow testing."""
        user_data = {
            "email": f"vendor_{uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "Vendor",
            "role": UserRole.VENDOR,
            "is_active": True,
            "is_verified": True,
            "hashed_password": "$2b$12$test_hash"
        }

        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession, test_vendor_user: User):
        """Create test vendor for workflow testing."""
        vendor_data = {
            "user_id": test_vendor_user.id,
            "business_name": "Test Cultural Tours",
            "business_type": VendorType.TOUR_GUIDE,
            "status": VendorStatus.ACTIVE,
            "verified": True,
            "phone": "+************",
            "address": "Lagos, Nigeria"
        }

        vendor = Vendor(**vendor_data)
        db_session.add(vendor)
        await db_session.commit()
        await db_session.refresh(vendor)
        return vendor

    @pytest.fixture
    def auth_headers_tourist(self, test_tourist_user: User):
        """Create authentication headers for tourist user."""
        token_data = {
            "sub": str(test_tourist_user.id),
            "email": test_tourist_user.email,
            "role": test_tourist_user.role.value,
            "scopes": ["customer:read", "customer:write"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def auth_headers_vendor(self, test_vendor_user: User):
        """Create authentication headers for vendor user."""
        token_data = {
            "sub": str(test_vendor_user.id),
            "email": test_vendor_user.email,
            "role": test_vendor_user.role.value,
            "scopes": ["vendor:read", "vendor:write"]
        }
        tokens = create_token_pair(token_data)
        return {"Authorization": f"Bearer {tokens['access_token']}"}

    @pytest.fixture
    def performance_timer(self):
        """Performance timing utility for integration tests."""
        class PerformanceTimer:
            def __init__(self):
                self.start_time = None
                self.end_time = None

            def start(self):
                self.start_time = time.perf_counter()

            def stop(self):
                self.end_time = time.perf_counter()
                return (self.end_time - self.start_time) * 1000  # Return milliseconds

        return PerformanceTimer()

    @pytest.mark.asyncio
    async def test_tourist_complete_journey_workflow(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_tourist_user: User,
        test_vendor: Vendor,
        auth_headers_tourist: Dict[str, str],
        performance_timer
    ):
        """
        Test complete tourist journey workflow from registration to booking completion.

        Workflow: Registration → Service Discovery → Booking Creation → Payment → Communication
        """
        correlation_id = f"test_tourist_journey_{uuid4().hex[:8]}"

        # Step 1: User Profile Setup (simulated - user already created)
        performance_timer.start()
        profile_response = await async_client.get(
            "/api/v1/users/profile",
            headers=auth_headers_tourist
        )
        profile_time = performance_timer.stop()

        assert profile_response.status_code == status.HTTP_200_OK
        assert profile_time < 200  # <200ms performance target

        # Step 2: Service Discovery - Find Available Vendors
        performance_timer.start()
        vendors_response = await async_client.get(
            "/api/v1/vendors/search",
            params={"location": "Lagos", "service_type": "cultural_tour"},
            headers=auth_headers_tourist
        )
        discovery_time = performance_timer.stop()

        assert vendors_response.status_code == status.HTTP_200_OK
        assert discovery_time < 200  # <200ms performance target

        # Step 3: Vendor Selection and Availability Checking
        performance_timer.start()
        availability_response = await async_client.get(
            f"/api/v1/vendors/{test_vendor.id}/availability",
            params={
                "start_date": (datetime.now() + timedelta(days=7)).date().isoformat(),
                "end_date": (datetime.now() + timedelta(days=14)).date().isoformat()
            },
            headers=auth_headers_tourist
        )
        availability_time = performance_timer.stop()

        assert availability_response.status_code == status.HTTP_200_OK
        assert availability_time < 200  # <200ms performance target

        # Step 4: Booking Creation
        booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,  # Mock service ID
            "booking_date": (datetime.now() + timedelta(days=10)).isoformat(),
            "duration_hours": 4,
            "number_of_guests": 2,
            "special_requests": "Cultural tour with traditional music",
            "total_amount": 150.00
        }

        performance_timer.start()
        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=booking_data,
            headers=auth_headers_tourist
        )
        booking_time = performance_timer.stop()

        assert booking_response.status_code == status.HTTP_201_CREATED
        assert booking_time < 500  # <500ms performance target for creation
        booking_result = booking_response.json()
        booking_id = booking_result["id"]

        # Step 5: Payment Processing
        payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("150.00"),
            "currency": "NGN",
            "payment_method": "card",
            "provider": PaymentProvider.PAYSTACK.value
        }

        performance_timer.start()
        with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
            mock_payment.return_value = {
                "status": "success",
                "data": {
                    "authorization_url": "https://checkout.paystack.com/test",
                    "access_code": "test_access_code",
                    "reference": f"ref_{uuid4().hex[:8]}"
                }
            }

            payment_response = await async_client.post(
                "/api/v1/payments/initialize",
                json=payment_data,
                headers=auth_headers_tourist
            )
        payment_time = performance_timer.stop()

        assert payment_response.status_code == status.HTTP_200_OK
        assert payment_time < 500  # <500ms performance target

        # Step 6: Communication Initiation
        message_data = {
            "booking_id": booking_id,
            "message_type": MessageType.CUSTOMER_MESSAGE.value,
            "content": "Looking forward to the cultural tour! Any specific preparations needed?",
            "sender_type": "customer"
        }

        performance_timer.start()
        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=auth_headers_tourist
        )
        message_time = performance_timer.stop()

        assert message_response.status_code == status.HTTP_201_CREATED
        assert message_time < 500  # <500ms performance target

        # Validate database consistency
        async with get_async_session_context() as session:
            # Verify booking exists
            booking = await session.get(Booking, booking_id)
            assert booking is not None
            assert booking.customer_id == test_tourist_user.id
            assert booking.vendor_id == test_vendor.id

            # Verify message exists
            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None
            assert message.booking_id == booking_id

    @pytest.mark.asyncio
    async def test_vendor_complete_journey_workflow(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_vendor_user: User,
        test_vendor: Vendor,
        test_tourist_user: User,
        auth_headers_vendor: Dict[str, str],
        performance_timer
    ):
        """
        Test complete vendor journey workflow from onboarding to booking management.

        Workflow: Registration → Service Creation → Booking Management → Payment Receipt
        """
        correlation_id = f"test_vendor_journey_{uuid4().hex[:8]}"

        # Step 1: Vendor Profile Setup (simulated - vendor already created)
        performance_timer.start()
        profile_response = await async_client.get(
            "/api/v1/vendors/profile",
            headers=auth_headers_vendor
        )
        profile_time = performance_timer.stop()

        assert profile_response.status_code == status.HTTP_200_OK
        assert profile_time < 200  # <200ms performance target

        # Step 2: Service Creation
        service_data = {
            "name": "Traditional Lagos Cultural Tour",
            "description": "Authentic cultural experience in Lagos",
            "category": "cultural_tour",
            "duration_hours": 4,
            "max_participants": 8,
            "base_price": 75.00,
            "currency": "NGN"
        }

        performance_timer.start()
        service_response = await async_client.post(
            "/api/v1/vendors/services",
            json=service_data,
            headers=auth_headers_vendor
        )
        service_time = performance_timer.stop()

        assert service_response.status_code == status.HTTP_201_CREATED
        assert service_time < 500  # <500ms performance target
        service_result = service_response.json()
        service_id = service_result["id"]

        # Step 3: Create Mock Booking for Management Testing
        booking_data = {
            "customer_id": test_tourist_user.id,
            "vendor_id": test_vendor.id,
            "service_id": service_id,
            "booking_date": datetime.now() + timedelta(days=5),
            "status": BookingStatus.PENDING_VENDOR_APPROVAL,
            "total_amount": Decimal("150.00"),
            "number_of_guests": 2
        }

        booking = Booking(**booking_data)
        db_session.add(booking)
        await db_session.commit()
        await db_session.refresh(booking)

        # Step 4: Vendor Booking Management - View Pending Bookings
        performance_timer.start()
        bookings_response = await async_client.get(
            "/api/v1/vendors/bookings",
            params={"status": "pending_vendor_approval"},
            headers=auth_headers_vendor
        )
        bookings_time = performance_timer.stop()

        assert bookings_response.status_code == status.HTTP_200_OK
        assert bookings_time < 200  # <200ms performance target

        # Step 5: Vendor Responds to Booking
        response_data = {
            "status": BookingStatus.CONFIRMED.value,
            "vendor_message": "Booking confirmed! Looking forward to hosting you."
        }

        performance_timer.start()
        booking_response = await async_client.put(
            f"/api/v1/vendors/bookings/{booking.id}/respond",
            json=response_data,
            headers=auth_headers_vendor
        )
        response_time = performance_timer.stop()

        assert booking_response.status_code == status.HTTP_200_OK
        assert response_time < 500  # <500ms performance target

        # Step 6: Customer Communication Response
        message_data = {
            "booking_id": booking.id,
            "message_type": MessageType.VENDOR_MESSAGE.value,
            "content": "Thank you for booking! Please bring comfortable walking shoes.",
            "sender_type": "vendor"
        }

        performance_timer.start()
        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=auth_headers_vendor
        )
        message_time = performance_timer.stop()

        assert message_response.status_code == status.HTTP_201_CREATED
        assert message_time < 500  # <500ms performance target

        # Step 7: Dashboard Analytics Access
        performance_timer.start()
        dashboard_response = await async_client.get(
            "/api/v1/vendors/dashboard/overview",
            headers=auth_headers_vendor
        )
        dashboard_time = performance_timer.stop()

        assert dashboard_response.status_code == status.HTTP_200_OK
        assert dashboard_time < 200  # <200ms performance target

        # Validate database consistency
        async with get_async_session_context() as session:
            # Verify booking status updated
            updated_booking = await session.get(Booking, booking.id)
            assert updated_booking is not None
            assert updated_booking.status == BookingStatus.CONFIRMED

            # Verify vendor message exists
            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None
            assert message.booking_id == booking.id

    @pytest.mark.asyncio
    async def test_cross_system_data_flow_validation(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        test_tourist_user: User,
        test_vendor: Vendor,
        auth_headers_tourist: Dict[str, str],
        auth_headers_vendor: Dict[str, str],
        performance_timer
    ):
        """
        Test cross-system data flow validation with real database transactions.

        Validates: Authentication → Booking → Payment → Communication → Analytics integration
        """
        correlation_id = f"test_cross_system_{uuid4().hex[:8]}"

        # Step 1: Authentication Flow Integration - Tourist Login
        login_data = {
            "email": test_tourist_user.email,
            "password": "test_password"
        }

        performance_timer.start()
        with patch('app.services.auth_service.verify_password', return_value=True):
            auth_response = await async_client.post(
                "/api/v1/auth/login",
                json=login_data
            )
        auth_time = performance_timer.stop()

        assert auth_response.status_code == status.HTTP_200_OK
        assert auth_time < 200  # <200ms performance target
        auth_result = auth_response.json()
        tourist_token = auth_result["access_token"]

        # Step 2: Cross-System Booking Creation with Authentication
        booking_data = {
            "vendor_id": test_vendor.id,
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "total_amount": 200.00,
            "number_of_guests": 3
        }

        headers_with_token = {"Authorization": f"Bearer {tourist_token}"}

        performance_timer.start()
        booking_response = await async_client.post(
            "/api/v1/bookings/",
            json=booking_data,
            headers=headers_with_token
        )
        booking_time = performance_timer.stop()

        assert booking_response.status_code == status.HTTP_201_CREATED
        assert booking_time < 500  # <500ms performance target
        booking_result = booking_response.json()
        booking_id = booking_result["id"]

        # Step 3: Payment System Integration
        payment_data = {
            "booking_id": booking_id,
            "amount": Decimal("200.00"),
            "currency": "NGN",
            "payment_method": "card"
        }

        performance_timer.start()
        with patch('app.services.payment.paystack_service.PaystackService.initialize_payment') as mock_payment:
            mock_payment.return_value = {
                "status": "success",
                "data": {"reference": f"ref_{uuid4().hex[:8]}"}
            }

            payment_response = await async_client.post(
                "/api/v1/payments/initialize",
                json=payment_data,
                headers=headers_with_token
            )
        payment_time = performance_timer.stop()

        assert payment_response.status_code == status.HTTP_200_OK
        assert payment_time < 500  # <500ms performance target

        # Step 4: Communication System Integration
        message_data = {
            "booking_id": booking_id,
            "message_type": MessageType.CUSTOMER_MESSAGE.value,
            "content": "Excited for this cultural experience!",
            "sender_type": "customer"
        }

        performance_timer.start()
        message_response = await async_client.post(
            "/api/v1/booking-communication/messages",
            json=message_data,
            headers=headers_with_token
        )
        message_time = performance_timer.stop()

        assert message_response.status_code == status.HTTP_201_CREATED
        assert message_time < 500  # <500ms performance target

        # Step 5: Analytics System Integration
        performance_timer.start()
        analytics_response = await async_client.get(
            "/api/v1/analytics/user/dashboard",
            headers=headers_with_token
        )
        analytics_time = performance_timer.stop()

        assert analytics_response.status_code == status.HTTP_200_OK
        assert analytics_time < 200  # <200ms performance target

        # Step 6: Cross-System Database Consistency Validation
        async with get_async_session_context() as session:
            # Verify all related records exist and are properly linked
            booking = await session.get(Booking, booking_id)
            assert booking is not None
            assert booking.customer_id == test_tourist_user.id
            assert booking.vendor_id == test_vendor.id

            # Verify foreign key relationships
            assert booking.customer is not None
            assert booking.vendor is not None

            # Verify message linked to booking
            message_result = message_response.json()
            message = await session.get(BookingMessage, message_result["id"])
            assert message is not None
            assert message.booking_id == booking_id
            assert message.booking is not None

        # Step 7: Error Handling and Rollback Validation
        invalid_booking_data = {
            "vendor_id": 99999,  # Non-existent vendor
            "service_id": 1,
            "booking_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "total_amount": 200.00
        }

        performance_timer.start()
        error_response = await async_client.post(
            "/api/v1/bookings/",
            json=invalid_booking_data,
            headers=headers_with_token
        )
        error_time = performance_timer.stop()

        assert error_response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
        assert error_time < 500  # <500ms performance target

        # Verify database state remains consistent after error
        async with get_async_session_context() as session:
            # Original booking should still exist
            original_booking = await session.get(Booking, booking_id)
            assert original_booking is not None
            assert original_booking.status in [BookingStatus.PENDING_VENDOR_APPROVAL, BookingStatus.CONFIRMED]
