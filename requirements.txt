# Core FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
starlette==0.27.0

# Database and ORM
sqlalchemy==2.0.23
asyncpg==0.29.0
psycopg2-binary==2.9.9
alembic==1.13.0

# Data Validation and Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Redis and Caching
redis==5.0.1
hiredis==2.2.3

# Task Queue
celery==5.3.4

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# HTTP Clients - Fixed async-timeout compatibility
httpx==0.25.2
aiohttp>=3.10.11
requests==2.31.0

# Payment Processing - Updated Paystack SDK
stripe==7.8.0
paystack-sdk==1.0.1

# Cryptocurrency Integration - Fixed web3 compatibility
web3>=6.15.0
qrcode==7.4.2
ccxt==4.4.85

# Monitoring and Logging
sentry-sdk[fastapi]==1.38.0
psutil==5.9.6

# AWS Integration - Fixed botocore compatibility
boto3>=1.29.7
botocore>=1.32.7

# Geo-location Services
geoip2==4.7.0
maxminddb>=2.3.0
geopy==2.4.0
pycountry==23.12.11

# AI/ML Integration
openai==1.3.0
tenacity==8.2.3

# WebSockets
websockets==12.0

# Email
aiosmtplib==3.0.1

# File Processing
pillow==10.1.0
python-magic==0.4.27
aiofiles==23.2.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist>=3.5.0
pytest-forked>=1.6.0

# Code Quality
black==23.11.0
flake8==6.1.0
mypy==1.7.1
isort==5.12.0

# Security
bandit==1.7.5
safety>=3.0.0