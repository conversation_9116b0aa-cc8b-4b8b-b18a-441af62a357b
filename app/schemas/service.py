"""
Service schemas for Culture Connect Backend API.

This module defines Pydantic V2 schemas for service management including
service listings, categories, pricing, availability, and media management.

Implements Task 3.2.1 requirements for comprehensive service listing system
with hierarchical categorization, flexible pricing models, and availability management.
"""

from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict, HttpUrl

from app.models.service import ServiceStatus, PricingType, AvailabilityType


# Base schemas
class ServiceCategoryBase(BaseModel):
    """Base schema for service categories."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Category name"
    )

    slug: str = Field(
        ...,
        min_length=1,
        max_length=255,
        pattern=r'^[a-z0-9\-]+$',
        description="URL-friendly category identifier"
    )

    description: Optional[str] = Field(
        None,
        description="Category description"
    )

    parent_id: Optional[int] = Field(
        None,
        description="Parent category ID for hierarchical structure"
    )

    display_order: int = Field(
        default=0,
        ge=0,
        description="Display order within parent category"
    )

    is_active: bool = Field(
        default=True,
        description="Whether category is active"
    )

    icon_url: Optional[HttpUrl] = Field(
        None,
        description="Category icon URL"
    )

    image_url: Optional[HttpUrl] = Field(
        None,
        description="Category image URL"
    )

    extra_data: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional category metadata"
    )


class ServiceCategoryCreate(ServiceCategoryBase):
    """Schema for creating service categories."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Cultural Workshops",
                "slug": "cultural-workshops",
                "description": "Traditional and contemporary cultural workshops",
                "parent_id": None,
                "display_order": 1,
                "is_active": True,
                "icon_url": "https://example.com/icons/workshop.svg",
                "image_url": "https://example.com/images/workshop.jpg",
                "extra_data": {
                    "featured": True,
                    "color": "#FF6B35"
                }
            }
        }
    )


class ServiceCategoryUpdate(BaseModel):
    """Schema for updating service categories."""

    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="Category name"
    )

    slug: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        pattern=r'^[a-z0-9\-]+$',
        description="URL-friendly category identifier"
    )

    description: Optional[str] = Field(
        None,
        description="Category description"
    )

    parent_id: Optional[int] = Field(
        None,
        description="Parent category ID"
    )

    display_order: Optional[int] = Field(
        None,
        ge=0,
        description="Display order"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Whether category is active"
    )

    icon_url: Optional[HttpUrl] = Field(
        None,
        description="Category icon URL"
    )

    image_url: Optional[HttpUrl] = Field(
        None,
        description="Category image URL"
    )

    extra_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional category metadata"
    )


class ServiceCategoryResponse(ServiceCategoryBase):
    """Schema for service category responses."""

    id: int = Field(..., description="Category ID")
    uuid: UUID = Field(..., description="Category UUID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    full_path: Optional[str] = Field(None, description="Full category path")

    # Relationships
    children: Optional[List['ServiceCategoryResponse']] = Field(
        default_factory=list,
        description="Child categories"
    )

    service_count: Optional[int] = Field(
        None,
        description="Number of services in this category"
    )

    model_config = ConfigDict(from_attributes=True)


# Service Image schemas
class ServiceImageBase(BaseModel):
    """Base schema for service images."""

    url: HttpUrl = Field(..., description="Image URL")

    alt_text: Optional[str] = Field(
        None,
        max_length=255,
        description="Alt text for accessibility"
    )

    caption: Optional[str] = Field(
        None,
        max_length=500,
        description="Image caption"
    )

    media_type: str = Field(
        default="image",
        max_length=50,
        description="Media type (image, video, document)"
    )

    file_size: Optional[int] = Field(
        None,
        ge=0,
        description="File size in bytes"
    )

    dimensions: Optional[Dict[str, int]] = Field(
        None,
        description="Image dimensions (width, height)"
    )

    display_order: int = Field(
        default=0,
        ge=0,
        description="Display order"
    )

    is_primary: bool = Field(
        default=False,
        description="Whether this is the primary image"
    )

    is_featured: bool = Field(
        default=False,
        description="Whether image is featured"
    )

    extra_data: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional image metadata"
    )


class ServiceImageCreate(ServiceImageBase):
    """Schema for creating service images."""

    service_id: int = Field(..., description="Associated service ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "service_id": 1,
                "url": "https://example.com/images/service1.jpg",
                "alt_text": "Traditional dance workshop",
                "caption": "Learn traditional Nigerian dance moves",
                "media_type": "image",
                "file_size": 1024000,
                "dimensions": {"width": 1920, "height": 1080},
                "display_order": 1,
                "is_primary": True,
                "is_featured": False,
                "extra_data": {
                    "photographer": "John Doe",
                    "location": "Lagos, Nigeria"
                }
            }
        }
    )


class ServiceImageUpdate(BaseModel):
    """Schema for updating service images."""

    url: Optional[HttpUrl] = Field(None, description="Image URL")
    alt_text: Optional[str] = Field(None, max_length=255, description="Alt text")
    caption: Optional[str] = Field(None, max_length=500, description="Caption")
    media_type: Optional[str] = Field(None, max_length=50, description="Media type")
    file_size: Optional[int] = Field(None, ge=0, description="File size")
    dimensions: Optional[Dict[str, int]] = Field(None, description="Dimensions")
    display_order: Optional[int] = Field(None, ge=0, description="Display order")
    is_primary: Optional[bool] = Field(None, description="Is primary image")
    is_featured: Optional[bool] = Field(None, description="Is featured image")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class ServiceImageResponse(ServiceImageBase):
    """Schema for service image responses."""

    id: int = Field(..., description="Image ID")
    uuid: UUID = Field(..., description="Image UUID")
    service_id: int = Field(..., description="Associated service ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


# Service Pricing schemas
class ServicePricingBase(BaseModel):
    """Base schema for service pricing."""

    tier_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Pricing tier name"
    )

    description: Optional[str] = Field(
        None,
        description="Pricing tier description"
    )

    price: Decimal = Field(
        ...,
        ge=0,
        description="Price for this tier"
    )

    currency: str = Field(
        default="USD",
        min_length=3,
        max_length=3,
        pattern=r'^[A-Z]{3}$',
        description="Currency code (ISO 4217)"
    )

    min_participants: int = Field(
        default=1,
        ge=1,
        description="Minimum participants for this tier"
    )

    max_participants: Optional[int] = Field(
        None,
        ge=1,
        description="Maximum participants for this tier"
    )

    valid_from: Optional[date] = Field(
        None,
        description="Pricing valid from date"
    )

    valid_until: Optional[date] = Field(
        None,
        description="Pricing valid until date"
    )

    conditions: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Special conditions for this pricing tier"
    )

    is_active: bool = Field(
        default=True,
        description="Whether pricing tier is active"
    )

    @field_validator('max_participants')
    @classmethod
    def validate_max_participants(cls, v, info):
        """Validate max_participants is greater than min_participants."""
        if v is not None and hasattr(info, 'data') and 'min_participants' in info.data:
            min_participants = info.data['min_participants']
            if v < min_participants:
                raise ValueError('max_participants must be greater than or equal to min_participants')
        return v

    @field_validator('valid_until')
    @classmethod
    def validate_valid_until(cls, v, info):
        """Validate valid_until is after valid_from."""
        if v is not None and hasattr(info, 'data') and 'valid_from' in info.data and info.data['valid_from'] is not None:
            if v <= info.data['valid_from']:
                raise ValueError('valid_until must be after valid_from')
        return v


class ServicePricingCreate(ServicePricingBase):
    """Schema for creating service pricing."""

    service_id: int = Field(..., description="Associated service ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "service_id": 1,
                "tier_name": "Standard Group",
                "description": "Standard pricing for groups of 5-10 people",
                "price": "150.00",
                "currency": "USD",
                "min_participants": 5,
                "max_participants": 10,
                "valid_from": "2025-01-01",
                "valid_until": "2025-12-31",
                "conditions": {
                    "advance_booking_required": True,
                    "cancellation_policy": "24 hours"
                },
                "is_active": True
            }
        }
    )


class ServicePricingUpdate(BaseModel):
    """Schema for updating service pricing."""

    tier_name: Optional[str] = Field(None, min_length=1, max_length=255, description="Tier name")
    description: Optional[str] = Field(None, description="Description")
    price: Optional[Decimal] = Field(None, ge=0, description="Price")
    currency: Optional[str] = Field(None, min_length=3, max_length=3, pattern=r'^[A-Z]{3}$', description="Currency")
    min_participants: Optional[int] = Field(None, ge=1, description="Min participants")
    max_participants: Optional[int] = Field(None, ge=1, description="Max participants")
    valid_from: Optional[date] = Field(None, description="Valid from date")
    valid_until: Optional[date] = Field(None, description="Valid until date")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Conditions")
    is_active: Optional[bool] = Field(None, description="Is active")


class ServicePricingResponse(ServicePricingBase):
    """Schema for service pricing responses."""

    id: int = Field(..., description="Pricing ID")
    uuid: UUID = Field(..., description="Pricing UUID")
    service_id: int = Field(..., description="Associated service ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)