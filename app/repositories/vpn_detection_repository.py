"""
VPN Detection Repository for Culture Connect Backend API.

This module provides comprehensive repository layer for VPN, proxy, and anonymization detection including:
- VPNDetectionResultRepository: VPN detection result CRUD operations with analytics
- ProxyDetectionResultRepository: Proxy detection with type classification and performance tracking
- AnonymizationDetectionResultRepository: Anonymization service detection and monitoring
- VPNDetectionAnalyticsRepository: Analytics aggregation for detection dashboard

Implements Phase 2.2 VPN Detection Repository following established repository patterns
with async/await, PostgreSQL optimization, and seamless BaseRepository integration.
"""

import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from uuid import UUID
from sqlalchemy import and_, or_, func, desc, asc, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import SQLAlchemyError

from app.repositories.base import BaseRepository, PaginationParams, QueryResult
from app.models.vpn_detection import (
    VPNDetectionResult, ProxyDetectionResult, AnonymizationDetectionResult,
    VPNDetectionAnalytics, VPNDetectionMethod, ProxyType, DetectionConfidence
)
from app.schemas.vpn_detection import (
    VPNDetectionResultCreate, VPNDetectionResultUpdate, VPNDetectionResultResponse,
    ProxyDetectionResultCreate, ProxyDetectionResultUpdate, ProxyDetectionResultResponse,
    AnonymizationDetectionResultCreate, AnonymizationDetectionResultUpdate,
    VPNDetectionAnalyticsResponse, VPNDetectionStatsResponse
)
from app.core.monitoring import metrics_collector
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


class VPNDetectionResultRepository(BaseRepository[VPNDetectionResult]):
    """
    Repository for VPN detection results with comprehensive analytics and performance optimization.

    Provides CRUD operations, analytics aggregation, and optimized queries for VPN detection
    results with <200ms performance targets using composite indexes.
    """

    def __init__(self, db: AsyncSession):
        """Initialize VPN detection result repository."""
        super().__init__(VPNDetectionResult, db)

    async def create_detection_result(
        self,
        ip_address: str,
        is_vpn_detected: bool,
        confidence_score: float,
        detection_method: VPNDetectionMethod,
        user_id: Optional[UUID] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> VPNDetectionResult:
        """
        Create VPN detection result with comprehensive metadata.

        Args:
            ip_address: IP address analyzed
            is_vpn_detected: Whether VPN was detected
            confidence_score: Detection confidence (0.0-1.0)
            detection_method: Method used for detection
            user_id: Optional user ID for tracking
            additional_metadata: Optional additional detection data

        Returns:
            Created VPN detection result

        Raises:
            RepositoryError: If creation fails
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            # Determine confidence level from score
            confidence_level = self._calculate_confidence_level(confidence_score)

            # Create detection result
            detection_data = VPNDetectionResultCreate(
                ip_address=ip_address,
                is_vpn_detected=is_vpn_detected,
                confidence_score=confidence_score,
                confidence_level=confidence_level,
                detection_method=detection_method,
                user_id=user_id,
                additional_metadata=additional_metadata or {}
            )

            result = await self.create(detection_data)

            # Record performance metrics
            creation_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_detection_result_creation_time_ms", creation_time_ms)
            metrics_collector.increment_counter("vpn_detection_result_created", tags={
                "detected": str(is_vpn_detected),
                "method": detection_method.value,
                "confidence_level": confidence_level.value
            })

            logger.info(
                f"VPN detection result created for {ip_address}: detected={is_vpn_detected}, "
                f"confidence={confidence_score:.3f}, method={detection_method.value}",
                extra={
                    "correlation_id": correlation,
                    "creation_time_ms": creation_time_ms,
                    "result_id": str(result.id)
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to create VPN detection result for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    async def get_recent_detection_by_ip(
        self,
        ip_address: str,
        max_age_hours: int = 24
    ) -> Optional[VPNDetectionResult]:
        """
        Get most recent VPN detection result for IP address within time window.

        Args:
            ip_address: IP address to lookup
            max_age_hours: Maximum age of result in hours

        Returns:
            Most recent detection result or None
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)

            stmt = select(VPNDetectionResult).where(
                and_(
                    VPNDetectionResult.ip_address == ip_address,
                    VPNDetectionResult.detection_timestamp >= cutoff_time
                )
            ).order_by(desc(VPNDetectionResult.detection_timestamp)).limit(1)

            result = await self.db.execute(stmt)
            detection_result = result.scalar_one_or_none()

            # Record performance metrics
            query_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_detection_lookup_time_ms", query_time_ms)

            if detection_result:
                logger.debug(
                    f"Found recent VPN detection for {ip_address}: "
                    f"detected={detection_result.is_vpn_detected}, age={max_age_hours}h",
                    extra={"correlation_id": correlation, "query_time_ms": query_time_ms}
                )
            else:
                logger.debug(
                    f"No recent VPN detection found for {ip_address} within {max_age_hours}h",
                    extra={"correlation_id": correlation, "query_time_ms": query_time_ms}
                )

            return detection_result

        except Exception as e:
            logger.error(
                f"Failed to lookup recent VPN detection for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    async def get_detection_statistics(
        self,
        start_date: datetime,
        end_date: datetime,
        group_by_method: bool = True
    ) -> Dict[str, Any]:
        """
        Get VPN detection statistics for analytics dashboard.

        Args:
            start_date: Start date for statistics
            end_date: End date for statistics
            group_by_method: Whether to group by detection method

        Returns:
            Detection statistics with performance metrics
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            # Base query for time range
            base_query = select(VPNDetectionResult).where(
                and_(
                    VPNDetectionResult.detection_timestamp >= start_date,
                    VPNDetectionResult.detection_timestamp <= end_date
                )
            )

            # Overall statistics
            total_detections = await self.db.scalar(
                select(func.count(VPNDetectionResult.id)).where(
                    and_(
                        VPNDetectionResult.detection_timestamp >= start_date,
                        VPNDetectionResult.detection_timestamp <= end_date
                    )
                )
            )

            vpn_detections = await self.db.scalar(
                select(func.count(VPNDetectionResult.id)).where(
                    and_(
                        VPNDetectionResult.detection_timestamp >= start_date,
                        VPNDetectionResult.detection_timestamp <= end_date,
                        VPNDetectionResult.is_vpn_detected == True
                    )
                )
            )

            avg_confidence = await self.db.scalar(
                select(func.avg(VPNDetectionResult.confidence_score)).where(
                    and_(
                        VPNDetectionResult.detection_timestamp >= start_date,
                        VPNDetectionResult.detection_timestamp <= end_date
                    )
                )
            )

            avg_detection_time = await self.db.scalar(
                select(func.avg(VPNDetectionResult.detection_duration_ms)).where(
                    and_(
                        VPNDetectionResult.detection_timestamp >= start_date,
                        VPNDetectionResult.detection_timestamp <= end_date,
                        VPNDetectionResult.detection_duration_ms.isnot(None)
                    )
                )
            )

            statistics = {
                "total_detections": total_detections or 0,
                "vpn_detections": vpn_detections or 0,
                "vpn_detection_rate": (vpn_detections / total_detections * 100) if total_detections > 0 else 0.0,
                "average_confidence_score": float(avg_confidence or 0.0),
                "average_detection_time_ms": float(avg_detection_time or 0.0)
            }

            # Method-specific statistics
            if group_by_method:
                method_stats = await self.db.execute(
                    select(
                        VPNDetectionResult.detection_method,
                        func.count(VPNDetectionResult.id).label('total'),
                        func.sum(func.cast(VPNDetectionResult.is_vpn_detected, func.Integer)).label('vpn_detected'),
                        func.avg(VPNDetectionResult.confidence_score).label('avg_confidence')
                    ).where(
                        and_(
                            VPNDetectionResult.detection_timestamp >= start_date,
                            VPNDetectionResult.detection_timestamp <= end_date
                        )
                    ).group_by(VPNDetectionResult.detection_method)
                )

                method_breakdown = {}
                for row in method_stats:
                    method_breakdown[row.detection_method] = {
                        "total_detections": row.total,
                        "vpn_detections": row.vpn_detected or 0,
                        "detection_rate": (row.vpn_detected / row.total * 100) if row.total > 0 else 0.0,
                        "average_confidence": float(row.avg_confidence or 0.0)
                    }

                statistics["method_breakdown"] = method_breakdown

            # Record performance metrics
            query_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_detection_statistics_time_ms", query_time_ms)

            logger.info(
                f"VPN detection statistics calculated: {total_detections} total, "
                f"{vpn_detections} VPN detected ({statistics['vpn_detection_rate']:.1f}%)",
                extra={
                    "correlation_id": correlation,
                    "query_time_ms": query_time_ms,
                    "date_range_days": (end_date - start_date).days
                }
            )

            return statistics

        except Exception as e:
            logger.error(
                f"Failed to calculate VPN detection statistics: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    async def get_high_risk_detections(
        self,
        risk_threshold: float = 0.7,
        limit: int = 100
    ) -> List[VPNDetectionResult]:
        """
        Get high-risk VPN detections for security monitoring.

        Args:
            risk_threshold: Minimum risk score threshold
            limit: Maximum number of results

        Returns:
            List of high-risk VPN detection results
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            stmt = select(VPNDetectionResult).where(
                and_(
                    VPNDetectionResult.risk_score >= risk_threshold,
                    VPNDetectionResult.is_vpn_detected == True
                )
            ).options(
                selectinload(VPNDetectionResult.user)
            ).order_by(
                desc(VPNDetectionResult.risk_score),
                desc(VPNDetectionResult.detection_timestamp)
            ).limit(limit)

            result = await self.db.execute(stmt)
            high_risk_detections = result.scalars().all()

            # Record performance metrics
            query_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_high_risk_query_time_ms", query_time_ms)
            metrics_collector.record_gauge("vpn_high_risk_detections_count", len(high_risk_detections))

            logger.info(
                f"Retrieved {len(high_risk_detections)} high-risk VPN detections "
                f"(threshold: {risk_threshold})",
                extra={
                    "correlation_id": correlation,
                    "query_time_ms": query_time_ms
                }
            )

            return high_risk_detections

        except Exception as e:
            logger.error(
                f"Failed to retrieve high-risk VPN detections: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    def _calculate_confidence_level(self, confidence_score: float) -> DetectionConfidence:
        """
        Calculate confidence level from numeric score.

        Args:
            confidence_score: Numeric confidence score (0.0-1.0)

        Returns:
            DetectionConfidence enum value
        """
        if confidence_score >= 0.8:
            return DetectionConfidence.VERY_HIGH
        elif confidence_score >= 0.6:
            return DetectionConfidence.HIGH
        elif confidence_score >= 0.4:
            return DetectionConfidence.MEDIUM
        elif confidence_score >= 0.2:
            return DetectionConfidence.LOW
        else:
            return DetectionConfidence.VERY_LOW


class ProxyDetectionResultRepository(BaseRepository[ProxyDetectionResult]):
    """
    Repository for proxy detection results with type classification and performance tracking.

    Provides CRUD operations and analytics for proxy detection with optimized queries
    and comprehensive performance monitoring.
    """

    def __init__(self, db: AsyncSession):
        """Initialize proxy detection result repository."""
        super().__init__(ProxyDetectionResult, db)

    async def create_proxy_detection(
        self,
        ip_address: str,
        is_proxy_detected: bool,
        proxy_type: ProxyType,
        confidence_score: float,
        detection_method: str,
        user_id: Optional[UUID] = None,
        performance_metadata: Optional[Dict[str, Any]] = None
    ) -> ProxyDetectionResult:
        """
        Create proxy detection result with performance characteristics.

        Args:
            ip_address: IP address analyzed
            is_proxy_detected: Whether proxy was detected
            proxy_type: Type of proxy detected
            confidence_score: Detection confidence (0.0-1.0)
            detection_method: Method used for detection
            user_id: Optional user ID for tracking
            performance_metadata: Optional performance data

        Returns:
            Created proxy detection result
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            detection_data = ProxyDetectionResultCreate(
                ip_address=ip_address,
                is_proxy_detected=is_proxy_detected,
                proxy_type=proxy_type,
                confidence_score=confidence_score,
                detection_method=detection_method,
                user_id=user_id,
                additional_metadata=performance_metadata or {}
            )

            result = await self.create(detection_data)

            # Record performance metrics
            creation_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("proxy_detection_result_creation_time_ms", creation_time_ms)
            metrics_collector.increment_counter("proxy_detection_result_created", tags={
                "detected": str(is_proxy_detected),
                "proxy_type": proxy_type.value,
                "method": detection_method
            })

            logger.info(
                f"Proxy detection result created for {ip_address}: detected={is_proxy_detected}, "
                f"type={proxy_type.value}, confidence={confidence_score:.3f}",
                extra={
                    "correlation_id": correlation,
                    "creation_time_ms": creation_time_ms,
                    "result_id": str(result.id)
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to create proxy detection result for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    async def get_proxy_type_distribution(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, int]:
        """
        Get distribution of proxy types for analytics.

        Args:
            start_date: Start date for analysis
            end_date: End date for analysis

        Returns:
            Dictionary mapping proxy types to counts
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            result = await self.db.execute(
                select(
                    ProxyDetectionResult.proxy_type,
                    func.count(ProxyDetectionResult.id).label('count')
                ).where(
                    and_(
                        ProxyDetectionResult.detection_timestamp >= start_date,
                        ProxyDetectionResult.detection_timestamp <= end_date,
                        ProxyDetectionResult.is_proxy_detected == True
                    )
                ).group_by(ProxyDetectionResult.proxy_type)
            )

            distribution = {row.proxy_type: row.count for row in result}

            # Record performance metrics
            query_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("proxy_type_distribution_query_time_ms", query_time_ms)

            logger.debug(
                f"Proxy type distribution calculated: {len(distribution)} types",
                extra={
                    "correlation_id": correlation,
                    "query_time_ms": query_time_ms
                }
            )

            return distribution

        except Exception as e:
            logger.error(
                f"Failed to calculate proxy type distribution: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise


class AnonymizationDetectionResultRepository(BaseRepository[AnonymizationDetectionResult]):
    """
    Repository for anonymization service detection results.

    Provides CRUD operations and analytics for anonymization detection including
    Tor network detection and other anonymization services.
    """

    def __init__(self, db: AsyncSession):
        """Initialize anonymization detection result repository."""
        super().__init__(AnonymizationDetectionResult, db)

    async def create_anonymization_detection(
        self,
        ip_address: str,
        is_anonymized: bool,
        anonymization_service: str,
        confidence_score: float,
        user_id: Optional[UUID] = None
    ) -> AnonymizationDetectionResult:
        """
        Create anonymization detection result.

        Args:
            ip_address: IP address analyzed
            is_anonymized: Whether anonymization was detected
            anonymization_service: Service providing anonymization
            confidence_score: Detection confidence (0.0-1.0)
            user_id: Optional user ID for tracking

        Returns:
            Created anonymization detection result
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            detection_data = AnonymizationDetectionResultCreate(
                ip_address=ip_address,
                is_anonymized=is_anonymized,
                anonymization_service=anonymization_service,
                confidence_score=confidence_score,
                user_id=user_id
            )

            result = await self.create(detection_data)

            # Record performance metrics
            creation_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.increment_counter("anonymization_detection_result_created", tags={
                "detected": str(is_anonymized),
                "service": anonymization_service
            })

            logger.info(
                f"Anonymization detection result created for {ip_address}: "
                f"detected={is_anonymized}, service={anonymization_service}",
                extra={
                    "correlation_id": correlation,
                    "creation_time_ms": creation_time_ms,
                    "result_id": str(result.id)
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to create anonymization detection result for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise


class VPNDetectionAnalyticsRepository(BaseRepository[VPNDetectionAnalytics]):
    """
    Repository for VPN detection analytics aggregation.

    Provides comprehensive analytics aggregation methods for the detection dashboard
    with optimized queries and performance monitoring.
    """

    def __init__(self, db: AsyncSession):
        """Initialize VPN detection analytics repository."""
        super().__init__(VPNDetectionAnalytics, db)

    async def aggregate_daily_statistics(
        self,
        target_date: datetime
    ) -> VPNDetectionAnalytics:
        """
        Aggregate daily VPN detection statistics.

        Args:
            target_date: Date to aggregate statistics for

        Returns:
            VPN detection analytics record
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_day = start_of_day + timedelta(days=1)

            # Aggregate VPN detection statistics
            vpn_stats = await self.db.execute(
                select(
                    func.count(VPNDetectionResult.id).label('total_detections'),
                    func.sum(func.cast(VPNDetectionResult.is_vpn_detected, func.Integer)).label('vpn_detections'),
                    func.avg(VPNDetectionResult.detection_duration_ms).label('avg_detection_time'),
                    func.avg(VPNDetectionResult.risk_score).label('avg_risk_score')
                ).where(
                    and_(
                        VPNDetectionResult.detection_timestamp >= start_of_day,
                        VPNDetectionResult.detection_timestamp < end_of_day
                    )
                )
            )

            vpn_row = vpn_stats.first()

            # Aggregate proxy detection statistics
            proxy_stats = await self.db.execute(
                select(
                    func.count(ProxyDetectionResult.id).label('total_detections'),
                    func.sum(func.cast(ProxyDetectionResult.is_proxy_detected, func.Integer)).label('proxy_detections')
                ).where(
                    and_(
                        ProxyDetectionResult.detection_timestamp >= start_of_day,
                        ProxyDetectionResult.detection_timestamp < end_of_day
                    )
                )
            )

            proxy_row = proxy_stats.first()

            # Aggregate anonymization detection statistics
            anon_stats = await self.db.execute(
                select(
                    func.count(AnonymizationDetectionResult.id).label('total_detections'),
                    func.sum(func.cast(AnonymizationDetectionResult.is_anonymized, func.Integer)).label('anon_detections')
                ).where(
                    and_(
                        AnonymizationDetectionResult.detection_timestamp >= start_of_day,
                        AnonymizationDetectionResult.detection_timestamp < end_of_day
                    )
                )
            )

            anon_row = anon_stats.first()

            # Create or update analytics record
            analytics_data = {
                "analysis_date": start_of_day,
                "total_detections": vpn_row.total_detections or 0,
                "vpn_detections": vpn_row.vpn_detections or 0,
                "proxy_detections": proxy_row.proxy_detections or 0,
                "anonymization_detections": anon_row.anon_detections or 0,
                "avg_detection_time_ms": float(vpn_row.avg_detection_time or 0.0),
                "avg_risk_score": float(vpn_row.avg_risk_score or 0.0)
            }

            # Check if record exists for this date
            existing_record = await self.db.scalar(
                select(VPNDetectionAnalytics).where(
                    VPNDetectionAnalytics.analysis_date == start_of_day
                )
            )

            if existing_record:
                # Update existing record
                for key, value in analytics_data.items():
                    if key != "analysis_date":
                        setattr(existing_record, key, value)
                await self.db.commit()
                result = existing_record
            else:
                # Create new record
                result = VPNDetectionAnalytics(**analytics_data)
                self.db.add(result)
                await self.db.commit()
                await self.db.refresh(result)

            # Record performance metrics
            aggregation_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_analytics_aggregation_time_ms", aggregation_time_ms)

            logger.info(
                f"VPN detection analytics aggregated for {target_date.date()}: "
                f"{analytics_data['total_detections']} total, {analytics_data['vpn_detections']} VPN",
                extra={
                    "correlation_id": correlation,
                    "aggregation_time_ms": aggregation_time_ms
                }
            )

            return result

        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to aggregate VPN detection analytics for {target_date.date()}: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise

    async def get_analytics_trends(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[VPNDetectionAnalytics]:
        """
        Get VPN detection analytics trends over time period.

        Args:
            start_date: Start date for trends
            end_date: End date for trends

        Returns:
            List of analytics records ordered by date
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        try:
            stmt = select(VPNDetectionAnalytics).where(
                and_(
                    VPNDetectionAnalytics.analysis_date >= start_date,
                    VPNDetectionAnalytics.analysis_date <= end_date
                )
            ).order_by(asc(VPNDetectionAnalytics.analysis_date))

            result = await self.db.execute(stmt)
            trends = result.scalars().all()

            # Record performance metrics
            query_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("vpn_analytics_trends_query_time_ms", query_time_ms)

            logger.debug(
                f"Retrieved {len(trends)} analytics trend records",
                extra={
                    "correlation_id": correlation,
                    "query_time_ms": query_time_ms,
                    "date_range_days": (end_date - start_date).days
                }
            )

            return trends

        except Exception as e:
            logger.error(
                f"Failed to retrieve VPN detection analytics trends: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise
