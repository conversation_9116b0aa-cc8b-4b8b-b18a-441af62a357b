# Culture Connect Backend API Documentation

## 🌍 Geolocation-Enhanced Payment Processing

Welcome to the Culture Connect Backend API documentation. This API provides intelligent, location-aware payment processing with automatic provider selection, VPN detection, and performance optimization.

## 📚 Documentation Structure

### **API Documentation**
- **[OpenAPI/Swagger UI](http://localhost:8000/docs)** - Interactive API documentation
- **[ReDoc](http://localhost:8000/redoc)** - Alternative API documentation format

### **User Guides**
- **[Geolocation Features](user_guide/geolocation_features.md)** - Complete guide to automatic location detection and smart payment optimization
- **[Payment Routing](user_guide/payment_routing.md)** - Understanding the 4-priority routing system with real-world examples
- **[VPN Handling](user_guide/vpn_handling.md)** - Using VPNs safely with Culture Connect payments and security features

### **Developer Integration**
- **[Integration Guide](developer/geolocation_integration.md)** - Complete integration walkthrough with code examples
- **[API Examples](developer/api_examples.md)** - Comprehensive code examples and testing strategies
- **[Configuration Guide](developer/configuration_guide.md)** - Environment setup and production deployment configuration

## 🚀 Quick Start

### **1. Basic Payment Creation**
```bash
curl -X POST "http://localhost:8000/api/v1/payments/create" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": 123,
    "amount": 50000.00,
    "currency": "USD",
    "return_url": "https://app.cultureconnect.ng/payment/return"
  }'
```

### **2. Geolocation Detection**
```bash
curl -X POST "http://localhost:8000/api/v1/geolocation/detect" \
  -H "Content-Type: application/json" \
  -d '{
    "ip_address": "************",
    "include_vpn_detection": true,
    "include_provider_recommendation": true
  }'
```

### **3. Provider Recommendation**
```bash
curl -X GET "http://localhost:8000/api/v1/geolocation/provider-recommendation/NG?currency=USD"
```

## 🌟 Key Features

### **4-Priority Routing System**
1. **Priority 1: Cryptocurrency** → Busha (BTC, ETH, USDT, USDC)
2. **Priority 2: User Preference** → Validated against geo-compatibility
3. **Priority 3: Geolocation-Based** → IP detection with country routing
4. **Priority 4: Currency-Based Fallback** → Traditional routing

### **Payment Providers**
- **Paystack**: African markets (NG, GH, KE, ZA, etc.)
- **Stripe**: Diaspora markets (US, UK, CA, EU, etc.)
- **Busha**: Cryptocurrency payments (BTC, ETH, USDT, USDC)

### **Advanced Capabilities**
- **VPN/Proxy Detection**: Multi-method detection with risk scoring
- **Analytics Dashboard**: Performance tracking and optimization insights
- **A/B Testing Framework**: Statistical significance validation for routing strategies
- **Real-time Monitoring**: Circuit breaker patterns and health checks

## 📊 Performance Targets

| Feature | Target | Description |
|---------|--------|-------------|
| Payment Creation | <500ms | Complete payment creation with geolocation routing |
| Location Detection | <100ms | IP-based geolocation with >95% accuracy |
| VPN Detection | <100ms | Multi-method VPN analysis with >95% accuracy |
| Analytics Queries | <200ms | Complex aggregations and performance metrics |
| Cache Hit Rate | >90% | Redis caching for repeated geolocation requests |

## 🔒 Security Features

- **VPN Detection**: Multi-method analysis with confidence scoring
- **Risk Assessment**: Comprehensive risk scoring for anonymized traffic
- **Audit Logging**: Complete audit trail with correlation IDs
- **Circuit Breakers**: Resilient fallback mechanisms
- **Rate Limiting**: Protection against abuse and DDoS attacks

## 🧪 Testing & Development

### **Environment Setup**
```bash
# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env

# Run database migrations
alembic upgrade head

# Start development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **Running Tests**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/test_geolocation/ -v
pytest tests/test_payments/ -v
```

## 📞 Support

- **API Support**: <EMAIL>
- **Documentation Issues**: <EMAIL>
- **General Support**: https://cultureconnect.ng/support

## 📄 License

This API is proprietary software. See [License](https://cultureconnect.ng/license) for details.

---

## 📋 Documentation Status

### **Phase 2.4: API Documentation Updates** ✅ **COMPLETE**

- ✅ **Phase 2.4.1**: Enhanced OpenAPI Documentation
- ✅ **Phase 2.4.2**: User Guide Documentation
- ✅ **Phase 2.4.3**: Developer Integration Documentation

### **Coverage Metrics**
- **API Endpoints**: 100% documented with examples
- **User Guides**: 3 comprehensive guides covering all features
- **Developer Integration**: Complete integration walkthrough with code examples
- **Configuration**: Production-ready setup guides for all environments

---

**Last Updated**: January 15, 2024
**API Version**: v1.0.0
**Documentation Version**: 2.4.3 (Complete)
