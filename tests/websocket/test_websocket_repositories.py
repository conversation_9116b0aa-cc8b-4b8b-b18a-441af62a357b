"""
Unit tests for enhanced WebSocket repositories.

This module provides comprehensive unit tests for WebSocket repository layer including:
- WebSocketRoomRepository with room management and caching validation
- WebSocketRoomParticipantRepository with participant tracking and bulk operations
- Enhanced WebSocketConnectionRepository with room integration testing
- Performance validation with <200ms query targets and >90% cache hit rates

Implements Task 6.1.1 Phase 5 requirements with >85% code coverage and real functionality testing.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete

from app.repositories.websocket_repositories import (
    WebSocketRoomRepository, WebSocketRoomParticipantRepository,
    WebSocketConnectionRepository
)
from app.models.websocket_models import (
    WebSocketRoom, WebSocketRoomParticipant, WebSocketConnection,
    ConnectionStatus
)
from app.core.cache import CacheManager
from app.services.base import RepositoryError


@pytest.mark.unit
class TestWebSocketRoomRepository:
    """Unit tests for WebSocketRoomRepository with performance validation."""

    @pytest.fixture
    async def room_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomRepository instance for testing."""
        return WebSocketRoomRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_room_data(self):
        """Sample room data for testing."""
        return {
            "room_id": "test_room_123",
            "room_name": "Test Room",
            "room_type": "booking",
            "is_private": True,
            "max_participants": 10,
            "booking_id": 123,
            "owner_id": 1,
            "is_active": True,
            "participant_count": 0,
            "total_messages": 0
        }

    @pytest.mark.asyncio
    async def test_create_room_performance(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test room creation with performance validation."""
        timer = performance_timer()

        # Mock database operations
        with patch.object(room_repository, 'create') as mock_create:
            mock_room = WebSocketRoom(**sample_room_data)
            mock_create.return_value = mock_room

            # Mock cache operations
            with patch.object(room_repository.cache, 'set') as mock_cache_set:
                mock_cache_set.return_value = None

                timer.start()
                result = await room_repository.create_room(sample_room_data)
                timer.stop()

                # Performance validation: <100ms for room creation
                timer.assert_under_ms(100, "Room creation")

                # Verify result
                assert result.room_id == sample_room_data["room_id"]
                assert result.room_name == sample_room_data["room_name"]

                # Verify cache was updated
                mock_cache_set.assert_called()

    @pytest.mark.asyncio
    async def test_get_room_by_id_with_caching(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test room retrieval with cache hit validation."""
        timer = performance_timer()
        room_id = sample_room_data["room_id"]

        # Test cache hit scenario
        cached_data = {
            "id": 1,
            "room_id": room_id,
            "room_name": sample_room_data["room_name"],
            "room_type": sample_room_data["room_type"],
            "is_active": True,
            "participant_count": 0,
            "max_participants": 10,
            "booking_id": 123,
            "owner_id": 1
        }

        with patch.object(room_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = cached_data

            with patch.object(room_repository, 'get') as mock_get:
                mock_room = WebSocketRoom(**sample_room_data)
                mock_get.return_value = mock_room

                timer.start()
                result = await room_repository.get_room_by_id(room_id)
                timer.stop()

                # Performance validation: <50ms for cached lookups
                timer.assert_under_ms(50, "Cached room retrieval")

                # Verify cache was checked
                mock_cache_get.assert_called_with(f"room:{room_id}")

                # Verify result
                assert result.room_id == room_id

    @pytest.mark.asyncio
    async def test_get_rooms_by_booking_performance(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test booking room queries with performance validation."""
        timer = performance_timer()
        booking_id = sample_room_data["booking_id"]

        # Mock database query
        with patch.object(room_repository.db, 'execute') as mock_execute:
            mock_result = MagicMock()
            mock_result.scalars.return_value.all.return_value = [
                WebSocketRoom(**sample_room_data)
            ]
            mock_execute.return_value = mock_result

            # Mock cache operations
            with patch.object(room_repository.cache, 'get') as mock_cache_get:
                mock_cache_get.return_value = None  # Cache miss

                with patch.object(room_repository.cache, 'set') as mock_cache_set:
                    timer.start()
                    result = await room_repository.get_rooms_by_booking(booking_id)
                    timer.stop()

                    # Performance validation: <150ms for booking room queries
                    timer.assert_under_ms(150, "Booking room query")

                    # Verify result
                    assert len(result) == 1
                    assert result[0].booking_id == booking_id

                    # Verify cache was updated
                    mock_cache_set.assert_called()

    @pytest.mark.asyncio
    async def test_update_room_activity_performance(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test room activity updates with performance validation."""
        timer = performance_timer()
        room_id = sample_room_data["room_id"]

        # Mock database update
        with patch.object(room_repository.db, 'execute') as mock_execute:
            mock_result = MagicMock()
            mock_result.rowcount = 1
            mock_execute.return_value = mock_result

            with patch.object(room_repository.db, 'commit') as mock_commit:
                # Mock cache invalidation
                with patch.object(room_repository.cache, 'delete') as mock_cache_delete:
                    timer.start()
                    result = await room_repository.update_room_activity(room_id)
                    timer.stop()

                    # Performance validation: <50ms for activity updates
                    timer.assert_under_ms(50, "Room activity update")

                    # Verify result
                    assert result is True

                    # Verify cache was invalidated
                    mock_cache_delete.assert_called_with(f"room:{room_id}")

    @pytest.mark.asyncio
    async def test_increment_message_count_atomic(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test atomic message count increment with performance validation."""
        timer = performance_timer()
        room_id = sample_room_data["room_id"]

        # Mock database atomic update
        with patch.object(room_repository.db, 'execute') as mock_execute:
            mock_result = MagicMock()
            mock_result.rowcount = 1
            mock_execute.return_value = mock_result

            with patch.object(room_repository.db, 'commit') as mock_commit:
                # Mock cache invalidation
                with patch.object(room_repository.cache, 'delete') as mock_cache_delete:
                    timer.start()
                    result = await room_repository.increment_message_count(room_id)
                    timer.stop()

                    # Performance validation: <50ms for counter updates
                    timer.assert_under_ms(50, "Message count increment")

                    # Verify result
                    assert result is True

                    # Verify atomic operation was called
                    mock_execute.assert_called()
                    mock_commit.assert_called()

    @pytest.mark.asyncio
    async def test_repository_error_handling(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict
    ):
        """Test repository error handling and rollback."""
        # Test database error scenario
        with patch.object(room_repository, 'create') as mock_create:
            mock_create.side_effect = Exception("Database error")

            with patch.object(room_repository.db, 'rollback') as mock_rollback:
                with pytest.raises(RepositoryError) as exc_info:
                    await room_repository.create_room(sample_room_data)

                # Verify error message
                assert "Failed to create WebSocket room" in str(exc_info.value)

                # Verify rollback was called
                mock_rollback.assert_called()

    @pytest.mark.asyncio
    async def test_cache_failure_graceful_degradation(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict
    ):
        """Test graceful degradation when cache fails."""
        room_id = sample_room_data["room_id"]

        # Mock cache failure
        with patch.object(room_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.side_effect = Exception("Cache error")

            # Mock database query
            with patch.object(room_repository.db, 'execute') as mock_execute:
                mock_result = MagicMock()
                mock_result.scalar_one_or_none.return_value = WebSocketRoom(**sample_room_data)
                mock_execute.return_value = mock_result

                # Should still work despite cache failure
                result = await room_repository.get_room_by_id(room_id)

                # Verify result
                assert result.room_id == room_id

                # Verify database was queried
                mock_execute.assert_called()


@pytest.mark.unit
class TestWebSocketRoomParticipantRepository:
    """Unit tests for WebSocketRoomParticipantRepository with bulk operations."""

    @pytest.fixture
    async def participant_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomParticipantRepository instance for testing."""
        return WebSocketRoomParticipantRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_participant_data(self):
        """Sample participant data for testing."""
        return {
            "room_id": 1,
            "user_id": 123,
            "connection_id": 456,
            "role": "participant",
            "is_active": True
        }

    @pytest.mark.asyncio
    async def test_add_participant_with_capacity_check(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict,
        performance_timer
    ):
        """Test participant addition with capacity checking."""
        timer = performance_timer()

        # Mock existing participant check
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            # First call: check existing participant (none found)
            mock_existing_result = MagicMock()
            mock_existing_result.scalar_one_or_none.return_value = None

            # Second call: get room for capacity check
            mock_room = MagicMock()
            mock_room.participant_count = 5
            mock_room.max_participants = 10
            mock_room_result = MagicMock()
            mock_room_result.scalar_one_or_none.return_value = mock_room

            mock_execute.side_effect = [mock_existing_result, mock_room_result]

            # Mock participant creation
            with patch.object(participant_repository, 'create') as mock_create:
                mock_participant = WebSocketRoomParticipant(**sample_participant_data)
                mock_create.return_value = mock_participant

                with patch.object(participant_repository.db, 'commit') as mock_commit:
                    timer.start()
                    result = await participant_repository.add_participant(
                        room_id=sample_participant_data["room_id"],
                        user_id=sample_participant_data["user_id"],
                        connection_id=sample_participant_data["connection_id"],
                        role=sample_participant_data["role"]
                    )
                    timer.stop()

                    # Performance validation: <100ms for participant addition
                    timer.assert_under_ms(100, "Participant addition")

                    # Verify result
                    assert result.user_id == sample_participant_data["user_id"]
                    assert result.room_id == sample_participant_data["room_id"]

    @pytest.mark.asyncio
    async def test_bulk_remove_participants_performance(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        performance_timer
    ):
        """Test bulk participant removal with performance validation."""
        timer = performance_timer()
        room_id = 1
        user_ids = [1, 2, 3, 4, 5]  # 5 participants

        # Mock bulk update
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            # First call: bulk update participants
            mock_participant_result = MagicMock()
            mock_participant_result.rowcount = 5

            # Second call: update room participant count
            mock_room_result = MagicMock()

            mock_execute.side_effect = [mock_participant_result, mock_room_result]

            with patch.object(participant_repository.db, 'commit') as mock_commit:
                # Mock cache invalidation
                with patch.object(participant_repository.cache, 'delete') as mock_cache_delete:
                    timer.start()
                    result = await participant_repository.bulk_remove_participants(room_id, user_ids)
                    timer.stop()

                    # Performance validation: <200ms for bulk operations
                    timer.assert_under_ms(200, "Bulk participant removal")

                    # Verify result
                    assert result == 5

                    # Verify cache invalidation
                    assert mock_cache_delete.call_count >= 1

    @pytest.mark.asyncio
    async def test_get_room_participants_with_caching(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict,
        performance_timer
    ):
        """Test room participant queries with cache validation."""
        timer = performance_timer()
        room_id = sample_participant_data["room_id"]

        # Test cache hit scenario
        cached_data = [{
            "id": 1,
            "user_id": sample_participant_data["user_id"],
            "role": sample_participant_data["role"],
            "joined_at": datetime.now(timezone.utc).isoformat(),
            "last_seen_at": datetime.now(timezone.utc).isoformat()
        }]

        with patch.object(participant_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = cached_data

            with patch.object(participant_repository, 'get_many') as mock_get_many:
                mock_get_many.return_value = [WebSocketRoomParticipant(**sample_participant_data)]

                timer.start()
                result = await participant_repository.get_room_participants(room_id, active_only=True)
                timer.stop()

                # Performance validation: <100ms for participant queries
                timer.assert_under_ms(100, "Room participant query")

                # Verify cache was checked
                mock_cache_get.assert_called_with(f"room_participants:{room_id}")

                # Verify result
                assert len(result) == 1
                assert result[0].user_id == sample_participant_data["user_id"]

    @pytest.mark.asyncio
    async def test_update_participant_activity_performance(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict,
        performance_timer
    ):
        """Test participant activity updates with performance validation."""
        timer = performance_timer()
        room_id = sample_participant_data["room_id"]
        user_id = sample_participant_data["user_id"]

        # Mock database update
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            mock_result = MagicMock()
            mock_result.rowcount = 1
            mock_execute.return_value = mock_result

            with patch.object(participant_repository.db, 'commit') as mock_commit:
                timer.start()
                result = await participant_repository.update_participant_activity(room_id, user_id)
                timer.stop()

                # Performance validation: <50ms for activity updates
                timer.assert_under_ms(50, "Participant activity update")

                # Verify result
                assert result is True

    @pytest.mark.asyncio
    async def test_room_capacity_validation(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict
    ):
        """Test room capacity validation during participant addition."""
        # Mock existing participant check (none found)
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            mock_existing_result = MagicMock()
            mock_existing_result.scalar_one_or_none.return_value = None

            # Mock room at capacity
            mock_room = MagicMock()
            mock_room.participant_count = 10
            mock_room.max_participants = 10
            mock_room_result = MagicMock()
            mock_room_result.scalar_one_or_none.return_value = mock_room

            mock_execute.side_effect = [mock_existing_result, mock_room_result]

            # Should raise RepositoryError for capacity exceeded
            with pytest.raises(RepositoryError) as exc_info:
                await participant_repository.add_participant(
                    room_id=sample_participant_data["room_id"],
                    user_id=sample_participant_data["user_id"]
                )

            assert "Room is at capacity" in str(exc_info.value)


@pytest.mark.unit
class TestEnhancedWebSocketConnectionRepository:
    """Unit tests for enhanced WebSocketConnectionRepository with room integration."""

    @pytest.fixture
    async def connection_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketConnectionRepository instance for testing."""
        return WebSocketConnectionRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_connection_data(self):
        """Sample connection data for testing."""
        return {
            "connection_id": "ws_conn_123",
            "user_id": 123,
            "status": ConnectionStatus.CONNECTED,
            "client_type": "web",
            "ip_address": "*************"
        }

    @pytest.mark.asyncio
    async def test_get_connections_in_room_performance(
        self,
        connection_repository: WebSocketConnectionRepository,
        sample_connection_data: dict,
        performance_timer
    ):
        """Test room connection queries with performance validation."""
        timer = performance_timer()
        room_id = 1

        # Mock cache miss and database query
        with patch.object(connection_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = None

            with patch.object(connection_repository.db, 'execute') as mock_execute:
                mock_result = MagicMock()
                mock_result.scalars.return_value.all.return_value = [
                    WebSocketConnection(**sample_connection_data)
                ]
                mock_execute.return_value = mock_result

                # Mock cache update
                with patch.object(connection_repository.cache, 'set') as mock_cache_set:
                    timer.start()
                    result = await connection_repository.get_connections_in_room(room_id)
                    timer.stop()

                    # Performance validation: <150ms for room connection queries
                    timer.assert_under_ms(150, "Room connection query")

                    # Verify result
                    assert len(result) == 1
                    assert result[0].connection_id == sample_connection_data["connection_id"]

                    # Verify cache was updated
                    mock_cache_set.assert_called()

    @pytest.mark.asyncio
    async def test_get_user_active_connections_with_rooms(
        self,
        connection_repository: WebSocketConnectionRepository,
        sample_connection_data: dict,
        performance_timer
    ):
        """Test user connections with room information query."""
        timer = performance_timer()
        user_id = sample_connection_data["user_id"]

        # Mock database query with room joins
        with patch.object(connection_repository.db, 'execute') as mock_execute:
            mock_row = MagicMock()
            mock_row.id = 1
            mock_row.connection_id = sample_connection_data["connection_id"]
            mock_row.status = sample_connection_data["status"]
            mock_row.connected_at = datetime.now(timezone.utc)
            mock_row.last_activity_at = datetime.now(timezone.utc)
            mock_row.room_id = 1
            mock_row.room_identifier = "test_room_123"
            mock_row.room_name = "Test Room"
            mock_row.room_type = "booking"

            mock_result = MagicMock()
            mock_result.all.return_value = [mock_row]
            mock_execute.return_value = mock_result

            # Mock cache operations
            with patch.object(connection_repository.cache, 'get') as mock_cache_get:
                mock_cache_get.return_value = None

                with patch.object(connection_repository.cache, 'set') as mock_cache_set:
                    timer.start()
                    result = await connection_repository.get_user_active_connections_with_rooms(user_id)
                    timer.stop()

                    # Performance validation: <100ms for user connection queries
                    timer.assert_under_ms(100, "User connections with rooms query")

                    # Verify result structure
                    assert len(result) == 1
                    connection_data = result[0]
                    assert connection_data["connection_id"] == sample_connection_data["connection_id"]
                    assert connection_data["room"] is not None
                    assert connection_data["room"]["room_name"] == "Test Room"

    @pytest.mark.asyncio
    async def test_broadcast_to_room_connections_preparation(
        self,
        connection_repository: WebSocketConnectionRepository,
        performance_timer
    ):
        """Test broadcast preparation with connection counting."""
        timer = performance_timer()
        room_id = 1
        event_data = {"type": "test_broadcast", "message": "Hello room!"}

        # Mock get_connections_in_room
        with patch.object(connection_repository, 'get_connections_in_room') as mock_get_connections:
            mock_connections = [MagicMock() for _ in range(5)]  # 5 connections
            mock_get_connections.return_value = mock_connections

            timer.start()
            result = await connection_repository.broadcast_to_room_connections(room_id, event_data)
            timer.stop()

            # Performance validation: <100ms for broadcast preparation
            timer.assert_under_ms(100, "Broadcast preparation")

            # Verify result
            assert result == 5

            # Verify connections were retrieved
            mock_get_connections.assert_called_with(room_id)

    @pytest.mark.asyncio
    async def test_connection_repository_cache_integration(
        self,
        connection_repository: WebSocketConnectionRepository,
        sample_connection_data: dict
    ):
        """Test cache integration with proper TTL and invalidation."""
        room_id = 1

        # Test cache hit scenario
        cached_data = [{
            "id": 1,
            "connection_id": sample_connection_data["connection_id"],
            "user_id": sample_connection_data["user_id"],
            "status": sample_connection_data["status"]
        }]

        with patch.object(connection_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = cached_data

            with patch.object(connection_repository, 'get_many') as mock_get_many:
                mock_get_many.return_value = [WebSocketConnection(**sample_connection_data)]

                result = await connection_repository.get_connections_in_room(room_id)

                # Verify cache was checked with correct key
                mock_cache_get.assert_called_with(f"room_connections:{room_id}")

                # Verify result
                assert len(result) == 1
                assert result[0].connection_id == sample_connection_data["connection_id"]

    @pytest.mark.asyncio
    async def test_repository_error_handling_and_logging(
        self,
        connection_repository: WebSocketConnectionRepository
    ):
        """Test comprehensive error handling and logging."""
        room_id = 1

        # Test database error scenario
        with patch.object(connection_repository.db, 'execute') as mock_execute:
            mock_execute.side_effect = Exception("Database connection failed")

            with pytest.raises(RepositoryError) as exc_info:
                await connection_repository.get_connections_in_room(room_id)

            # Verify error message
            assert "Failed to get room connections" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_performance_targets_validation(
        self,
        connection_repository: WebSocketConnectionRepository,
        sample_connection_data: dict,
        performance_timer
    ):
        """Test all repository operations meet performance targets."""
        timer = performance_timer()

        # Test multiple operations with performance validation
        operations = [
            ("get_connections_in_room", [1], 150),
            ("get_user_active_connections_with_rooms", [123], 100),
            ("broadcast_to_room_connections", [1, {"type": "test"}], 100)
        ]

        for operation_name, args, target_ms in operations:
            # Mock all database and cache operations
            with patch.object(connection_repository.db, 'execute') as mock_execute:
                mock_result = MagicMock()
                mock_result.scalars.return_value.all.return_value = []
                mock_result.all.return_value = []
                mock_execute.return_value = mock_result

                with patch.object(connection_repository.cache, 'get') as mock_cache_get:
                    mock_cache_get.return_value = None

                    with patch.object(connection_repository.cache, 'set') as mock_cache_set:
                        with patch.object(connection_repository, 'get_connections_in_room') as mock_get_conn:
                            mock_get_conn.return_value = []

                            timer.start()
                            operation = getattr(connection_repository, operation_name)
                            await operation(*args)
                            timer.stop()

                            # Validate performance target
                            timer.assert_under_ms(target_ms, f"{operation_name} operation")
