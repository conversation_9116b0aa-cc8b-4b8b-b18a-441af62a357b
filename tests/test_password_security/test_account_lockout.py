"""
Test suite for account lockout functionality.

This module tests account lockout protection against brute force attacks
including failed attempt tracking, lockout duration, and automatic unlocking.

Tests cover Task 2.1.2 account lockout requirements.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.repositories.password_security_repository import AccountLockoutRepository
from app.models.password_security import AccountLockout
from app.schemas.password_security import AccountLockoutStatus


class TestAccountLockout:
    """Test account lockout functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_lockout_repo(self):
        """Create mock account lockout repository."""
        return Mock(spec=AccountLockoutRepository)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        return PasswordSecurityService(mock_db_session)

    @pytest.fixture
    def unlocked_account(self):
        """Create an unlocked account mock."""
        lockout = Mock(spec=AccountLockout)
        lockout.user_id = 1
        lockout.failed_attempts = 2
        lockout.locked_until = None
        lockout.last_attempt_at = datetime.utcnow()
        lockout.last_attempt_ip = "*************"
        lockout.lockout_reason = None
        lockout.is_locked = False
        lockout.remaining_lockout_seconds = None
        return lockout

    @pytest.fixture
    def locked_account(self):
        """Create a locked account mock."""
        lockout = Mock(spec=AccountLockout)
        lockout.user_id = 1
        lockout.failed_attempts = 5
        lockout.locked_until = datetime.utcnow() + timedelta(minutes=15)
        lockout.last_attempt_at = datetime.utcnow()
        lockout.last_attempt_ip = "*************"
        lockout.lockout_reason = "too_many_attempts"
        lockout.is_locked = True
        lockout.remaining_lockout_seconds = 900  # 15 minutes
        return lockout

    @pytest.mark.asyncio
    async def test_check_account_lockout_no_record(self, password_service):
        """Test checking lockout status for user with no lockout record."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.return_value = None
            
            status = await password_service.check_account_lockout(user_id)
            
            assert status.is_locked is False
            assert status.failed_attempts == 0
            assert status.max_attempts == 5  # Default value
            assert status.locked_until is None
            assert status.remaining_lockout_seconds is None

    @pytest.mark.asyncio
    async def test_check_account_lockout_unlocked(self, password_service, unlocked_account):
        """Test checking lockout status for unlocked account."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.return_value = unlocked_account
            
            status = await password_service.check_account_lockout(user_id)
            
            assert status.is_locked is False
            assert status.failed_attempts == 2
            assert status.max_attempts == 5
            assert status.locked_until is None
            assert status.remaining_lockout_seconds is None

    @pytest.mark.asyncio
    async def test_check_account_lockout_locked(self, password_service, locked_account):
        """Test checking lockout status for locked account."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.return_value = locked_account
            
            status = await password_service.check_account_lockout(user_id)
            
            assert status.is_locked is True
            assert status.failed_attempts == 5
            assert status.max_attempts == 5
            assert status.locked_until is not None
            assert status.remaining_lockout_seconds == 900

    @pytest.mark.asyncio
    async def test_record_failed_login_first_attempt(self, password_service):
        """Test recording first failed login attempt."""
        user_id = 1
        client_ip = "*************"
        
        mock_lockout = Mock(spec=AccountLockout)
        mock_lockout.failed_attempts = 1
        mock_lockout.is_locked = False
        
        with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
            mock_record.return_value = mock_lockout
            
            with patch.object(password_service, 'check_account_lockout') as mock_check:
                mock_status = Mock(spec=AccountLockoutStatus)
                mock_status.is_locked = False
                mock_status.failed_attempts = 1
                mock_check.return_value = mock_status
                
                status = await password_service.record_failed_login(user_id, client_ip)
                
                assert status.failed_attempts == 1
                assert status.is_locked is False
                mock_record.assert_called_once_with(user_id, client_ip)

    @pytest.mark.asyncio
    async def test_record_failed_login_triggers_lockout(self, password_service):
        """Test that 5th failed attempt triggers account lockout."""
        user_id = 1
        client_ip = "*************"
        
        mock_lockout = Mock(spec=AccountLockout)
        mock_lockout.failed_attempts = 5
        mock_lockout.is_locked = True
        
        with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
            mock_record.return_value = mock_lockout
            
            with patch.object(password_service, 'check_account_lockout') as mock_check:
                mock_status = Mock(spec=AccountLockoutStatus)
                mock_status.is_locked = True
                mock_status.failed_attempts = 5
                mock_check.return_value = mock_status
                
                status = await password_service.record_failed_login(user_id, client_ip)
                
                assert status.failed_attempts == 5
                assert status.is_locked is True
                mock_record.assert_called_once_with(user_id, client_ip)

    @pytest.mark.asyncio
    async def test_reset_lockout_on_success(self, password_service):
        """Test resetting lockout on successful login."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'reset_failed_attempts') as mock_reset:
            mock_reset.return_value = True
            
            result = await password_service.reset_lockout_on_success(user_id)
            
            assert result is True
            mock_reset.assert_called_once_with(user_id)

    @pytest.mark.asyncio
    async def test_reset_lockout_on_success_no_record(self, password_service):
        """Test resetting lockout when no lockout record exists."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'reset_failed_attempts') as mock_reset:
            mock_reset.return_value = False
            
            result = await password_service.reset_lockout_on_success(user_id)
            
            assert result is False
            mock_reset.assert_called_once_with(user_id)

    @pytest.mark.asyncio
    async def test_lockout_duration_configuration(self, password_service):
        """Test that lockout duration respects configuration."""
        user_id = 1
        client_ip = "*************"
        
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.ACCOUNT_LOCKOUT_DURATION_MINUTES = 30
            mock_settings.ACCOUNT_LOCKOUT_ATTEMPTS = 3
            
            mock_lockout = Mock(spec=AccountLockout)
            mock_lockout.failed_attempts = 3
            mock_lockout.is_locked = True
            
            with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
                mock_record.return_value = mock_lockout
                
                with patch.object(password_service, 'check_account_lockout') as mock_check:
                    mock_status = Mock(spec=AccountLockoutStatus)
                    mock_status.max_attempts = 3
                    mock_check.return_value = mock_status
                    
                    await password_service.record_failed_login(user_id, client_ip)
                    
                    # Verify configuration was used
                    mock_check.assert_called_once_with(user_id)

    @pytest.mark.asyncio
    async def test_lockout_attempts_configuration(self, password_service):
        """Test that lockout attempts threshold respects configuration."""
        user_id = 1
        
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.ACCOUNT_LOCKOUT_ATTEMPTS = 3
            
            with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
                mock_get.return_value = None
                
                status = await password_service.check_account_lockout(user_id)
                
                assert status.max_attempts == 3

    @pytest.mark.asyncio
    async def test_lockout_ip_tracking(self, password_service):
        """Test that failed attempts track IP addresses."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
            mock_record.return_value = Mock(spec=AccountLockout)
            
            with patch.object(password_service, 'check_account_lockout') as mock_check:
                mock_check.return_value = Mock(spec=AccountLockoutStatus)
                
                await password_service.record_failed_login(user_id, client_ip)
                
                mock_record.assert_called_once_with(user_id, client_ip)

    @pytest.mark.asyncio
    async def test_lockout_error_handling(self, password_service):
        """Test error handling in lockout operations."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.side_effect = Exception("Database error")
            
            # Should return safe default status
            status = await password_service.check_account_lockout(user_id)
            
            assert status.is_locked is False
            assert status.failed_attempts == 0

    @pytest.mark.asyncio
    async def test_reset_lockout_error_handling(self, password_service):
        """Test error handling in lockout reset."""
        user_id = 1
        
        with patch.object(password_service.lockout_repo, 'reset_failed_attempts') as mock_reset:
            mock_reset.side_effect = Exception("Database error")
            
            result = await password_service.reset_lockout_on_success(user_id)
            
            assert result is False

    @pytest.mark.asyncio
    async def test_record_failed_login_error_handling(self, password_service):
        """Test error handling when recording failed login."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
            mock_record.side_effect = Exception("Database error")
            
            with pytest.raises(Exception):
                await password_service.record_failed_login(user_id, client_ip)

    @pytest.mark.asyncio
    async def test_cleanup_expired_lockouts(self, password_service):
        """Test cleanup of expired lockouts."""
        with patch.object(password_service.lockout_repo, 'cleanup_expired_lockouts') as mock_cleanup:
            mock_cleanup.return_value = 3
            
            count = await password_service.cleanup_expired_lockouts()
            
            assert count == 3
            mock_cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_expired_lockouts_error_handling(self, password_service):
        """Test error handling in lockout cleanup."""
        with patch.object(password_service.lockout_repo, 'cleanup_expired_lockouts') as mock_cleanup:
            mock_cleanup.side_effect = Exception("Database error")
            
            count = await password_service.cleanup_expired_lockouts()
            
            assert count == 0

    @pytest.mark.asyncio
    async def test_lockout_isolation_between_users(self, password_service):
        """Test that lockouts are isolated between different users."""
        user1_id = 1
        user2_id = 2
        client_ip = "*************"
        
        # User 1 gets locked
        locked_user1 = Mock(spec=AccountLockout)
        locked_user1.failed_attempts = 5
        locked_user1.is_locked = True
        
        # User 2 has no lockout
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            # First call for user 1
            mock_get.return_value = locked_user1
            status1 = await password_service.check_account_lockout(user1_id)
            assert status1.is_locked is True
            
            # Second call for user 2
            mock_get.return_value = None
            status2 = await password_service.check_account_lockout(user2_id)
            assert status2.is_locked is False

    @pytest.mark.asyncio
    async def test_lockout_time_calculation(self, password_service, locked_account):
        """Test accurate lockout time calculation."""
        user_id = 1
        
        # Set specific lockout time
        future_time = datetime.utcnow() + timedelta(minutes=10)
        locked_account.locked_until = future_time
        locked_account.remaining_lockout_seconds = 600  # 10 minutes
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.return_value = locked_account
            
            status = await password_service.check_account_lockout(user_id)
            
            assert status.remaining_lockout_seconds == 600
            assert status.locked_until == future_time

    @pytest.mark.asyncio
    async def test_progressive_lockout_attempts(self, password_service):
        """Test progressive failed attempt counting."""
        user_id = 1
        client_ip = "*************"
        
        # Simulate progressive failed attempts
        for attempt in range(1, 6):
            mock_lockout = Mock(spec=AccountLockout)
            mock_lockout.failed_attempts = attempt
            mock_lockout.is_locked = (attempt >= 5)
            
            with patch.object(password_service.lockout_repo, 'record_failed_attempt') as mock_record:
                mock_record.return_value = mock_lockout
                
                with patch.object(password_service, 'check_account_lockout') as mock_check:
                    mock_status = Mock(spec=AccountLockoutStatus)
                    mock_status.failed_attempts = attempt
                    mock_status.is_locked = (attempt >= 5)
                    mock_check.return_value = mock_status
                    
                    status = await password_service.record_failed_login(user_id, client_ip)
                    
                    assert status.failed_attempts == attempt
                    if attempt >= 5:
                        assert status.is_locked is True
                    else:
                        assert status.is_locked is False

    @pytest.mark.asyncio
    async def test_lockout_reason_tracking(self, password_service, locked_account):
        """Test that lockout reason is properly tracked."""
        user_id = 1
        
        locked_account.lockout_reason = "too_many_attempts"
        
        with patch.object(password_service.lockout_repo, 'get_lockout_status') as mock_get:
            mock_get.return_value = locked_account
            
            status = await password_service.check_account_lockout(user_id)
            
            assert status.lockout_reason == "too_many_attempts"
