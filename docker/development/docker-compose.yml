version: '3.8'

services:
  # Culture Connect Backend API - Development Configuration
  api:
    build:
      context: ../../
      dockerfile: Dockerfile
      target: development
    container_name: culture_connect_api_dev
    ports:
      - "8000:8000"
    env_file:
      - ../../environments/development/.env.development
    environment:
      - CC_DATABASE_URL=postgresql+asyncpg://culture_connect_dev:dev_password_123@db:5432/culture_connect_dev_db
      - CC_REDIS_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ../../:/app
      - app_logs:/app/logs
      - app_uploads:/app/uploads
      - geoip_data:/app/data
    networks:
      - culture_connect_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-dev.rule=Host(`api-dev.localhost`)"
      - "traefik.http.services.api-dev.loadbalancer.server.port=8000"

  # PostgreSQL Database - Development
  db:
    image: postgres:15-alpine
    container_name: culture_connect_db_dev
    environment:
      - POSTGRES_DB=culture_connect_dev_db
      - POSTGRES_USER=culture_connect_dev
      - POSTGRES_PASSWORD=dev_password_123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      # Development performance settings
      - POSTGRES_SHARED_BUFFERS=64MB
      - POSTGRES_EFFECTIVE_CACHE_SIZE=256MB
      - POSTGRES_MAINTENANCE_WORK_MEM=16MB
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - postgres_backups_dev:/backups
      - ../../scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - culture_connect_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U culture_connect_dev -d culture_connect_dev_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Cache - Development
  redis:
    image: redis:7-alpine
    container_name: culture_connect_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
      - ../../config/redis/redis-dev.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - culture_connect_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]

  # Celery Worker - Development
  celery-worker:
    build:
      context: ../../
      dockerfile: Dockerfile
      target: development
    container_name: culture_connect_celery_dev
    env_file:
      - ../../environments/development/.env.development
    environment:
      - CC_DATABASE_URL=postgresql+asyncpg://culture_connect_dev:dev_password_123@db:5432/culture_connect_dev_db
      - CC_REDIS_URL=redis://redis:6379/0
      - CC_CELERY_BROKER_URL=redis://redis:6379/1
      - CC_CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ../../:/app
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - culture_connect_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_config", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s
    labels:
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: ["celery", "-A", "app.core.celery_config", "worker", "--loglevel=debug", "--concurrency=1"]

  # Development tools (optional)
  adminer:
    image: adminer:latest
    container_name: culture_connect_adminer_dev
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=db
      - ADMINER_DESIGN=pepa-linha
    depends_on:
      - db
    networks:
      - culture_connect_dev_network
    profiles:
      - dev
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.adminer-dev.rule=Host(`adminer-dev.localhost`)"

  # MailHog for email testing (optional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: culture_connect_mailhog_dev
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - culture_connect_dev_network
    profiles:
      - dev
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mailhog-dev.rule=Host(`mailhog-dev.localhost`)"

volumes:
  postgres_data_dev:
    driver: local
  postgres_backups_dev:
    driver: local
  redis_data_dev:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  geoip_data:
    driver: local

networks:
  culture_connect_dev_network:
    driver: bridge
    labels:
      - "environment=development"