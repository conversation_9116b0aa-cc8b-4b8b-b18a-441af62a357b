"""
IP-based geolocation service for Culture Connect Backend API.

This module provides comprehensive geolocation detection for intelligent payment
provider routing including:
- IP address extraction and validation
- Country code detection using MaxMind GeoIP
- Geographic routing with fallback mechanisms
- Performance optimization and caching
- Circuit breaker patterns for resilience
- Enhanced error handling and monitoring

Implements Phase 1 geolocation enhancement with MaxMind database setup,
circuit breaker protection, and comprehensive resilience patterns following
production-grade FastAPI patterns with >80% test coverage.
"""

import logging
import ipaddress
import time
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
import aiofiles
import geoip2.database
import geoip2.errors
from fastapi import Request

from app.core.config import settings
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.services.base import ServiceError, ValidationError
from app.core.circuit_breaker import get_geolocation_circuit_breaker, CircuitBreakerError
from app.core.geolocation_resilience import get_geolocation_resilience_manager

logger = logging.getLogger(__name__)


# Import GeolocationResult from resilience manager to avoid circular imports
from app.core.geolocation_resilience import GeolocationResult


class GeolocationService:
    """
    IP-based geolocation service for payment provider routing.

    Provides comprehensive geolocation detection with multiple fallback
    mechanisms and performance optimization for payment routing decisions.
    """

    def __init__(self):
        """Initialize geolocation service with enhanced resilience patterns."""
        self.geoip_database_path = getattr(settings, 'GEOIP_DATABASE_PATH', './data/GeoLite2-Country.mmdb')
        self.geoip_reader = None
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = timedelta(hours=getattr(settings, 'GEOLOCATION_CACHE_TTL_HOURS', 1))

        # Initialize circuit breaker and resilience manager
        self.circuit_breaker = get_geolocation_circuit_breaker()
        self.resilience_manager = get_geolocation_resilience_manager()

        # Performance tracking
        self.performance_metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "circuit_breaker_trips": 0,
            "fallback_activations": 0
        }

        # Initialize GeoIP database
        asyncio.create_task(self._initialize_geoip_database())

    async def _initialize_geoip_database(self) -> None:
        """Initialize MaxMind GeoIP database reader."""
        try:
            # Check if GeoIP database file exists
            import os
            if not os.path.exists(self.geoip_database_path):
                logger.warning(
                    f"GeoIP database not found at {self.geoip_database_path}. "
                    "Geolocation will use fallback methods only."
                )
                return

            # Initialize GeoIP reader
            self.geoip_reader = geoip2.database.Reader(self.geoip_database_path)
            logger.info(f"GeoIP database initialized: {self.geoip_database_path}")

        except Exception as e:
            logger.error(f"Failed to initialize GeoIP database: {str(e)}")
            self.geoip_reader = None

    async def extract_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request with proxy support.

        Args:
            request: FastAPI request object

        Returns:
            Client IP address string

        Raises:
            ValidationError: If IP extraction fails
        """
        correlation = correlation_id.get()

        try:
            # Check for forwarded headers (proxy/load balancer support)
            forwarded_headers = [
                "X-Forwarded-For",
                "X-Real-IP",
                "CF-Connecting-IP",  # Cloudflare
                "X-Client-IP",
                "X-Forwarded"
            ]

            for header in forwarded_headers:
                if header in request.headers:
                    ip_list = request.headers[header].split(',')
                    # Take the first IP (original client)
                    client_ip = ip_list[0].strip()

                    # Validate IP address
                    if self._is_valid_ip(client_ip):
                        logger.info(
                            f"Client IP extracted from {header}: {client_ip}",
                            extra={"correlation_id": correlation}
                        )
                        return client_ip

            # Fallback to direct client IP
            client_ip = request.client.host
            if self._is_valid_ip(client_ip):
                logger.info(
                    f"Client IP extracted from direct connection: {client_ip}",
                    extra={"correlation_id": correlation}
                )
                return client_ip

            # If all else fails, use a default (for development)
            if settings.ENVIRONMENT == "development":
                default_ip = "*******"  # Google DNS for testing
                logger.warning(
                    f"Using default IP for development: {default_ip}",
                    extra={"correlation_id": correlation}
                )
                return default_ip

            raise ValidationError("Unable to extract valid client IP address")

        except Exception as e:
            logger.error(
                f"IP extraction failed: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise ValidationError(f"IP extraction failed: {str(e)}")

    def _is_valid_ip(self, ip_string: str) -> bool:
        """
        Validate IP address format.

        Args:
            ip_string: IP address string to validate

        Returns:
            True if valid IP address
        """
        try:
            # Parse IP address
            ip = ipaddress.ip_address(ip_string)

            # Reject private/local addresses in production
            if settings.ENVIRONMENT == "production":
                if ip.is_private or ip.is_loopback or ip.is_link_local:
                    return False

            return True

        except ValueError:
            return False

    async def detect_country_from_ip(self, ip_address: str, user_preferences: Optional[Dict[str, Any]] = None) -> GeolocationResult:
        """
        Detect country from IP address using enhanced resilience patterns.

        Args:
            ip_address: IP address to geolocate
            user_preferences: Optional user preferences for fallback

        Returns:
            GeolocationResult with country detection data
        """
        correlation = correlation_id.get()
        start_time = time.perf_counter()

        # Update performance metrics
        self.performance_metrics["total_requests"] += 1

        # Check cache first
        cache_key = f"geo_{ip_address}"
        if cache_key in self.cache:
            cached_result, cached_time = self.cache[cache_key]
            if time.time() - cached_time < self.cache_ttl.total_seconds():
                self.performance_metrics["cache_hits"] += 1

                # Record cache hit metrics
                detection_time_ms = (time.perf_counter() - start_time) * 1000
                metrics_collector.record_histogram("geolocation_detection_time_ms", detection_time_ms)
                metrics_collector.record_gauge("geolocation_cache_hit_rate",
                    self.performance_metrics["cache_hits"] / self.performance_metrics["total_requests"])

                logger.info(
                    f"Returning cached geolocation for {ip_address} ({detection_time_ms:.2f}ms)",
                    extra={"correlation_id": correlation}
                )
                return cached_result

        self.performance_metrics["cache_misses"] += 1

        try:
            # Use resilience manager for enhanced detection with fallback
            result = await self.resilience_manager.detect_country_with_resilience(
                self, ip_address, user_preferences
            )

            # Cache successful result
            if result.confidence_score > 0.5:  # Only cache high-confidence results
                self.cache[cache_key] = (result, time.time())

            # Record performance metrics
            detection_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("geolocation_detection_time_ms", detection_time_ms)
            metrics_collector.record_gauge("geolocation_cache_hit_rate",
                self.performance_metrics["cache_hits"] / self.performance_metrics["total_requests"])

            # Track detection method metrics
            metrics_collector.increment_counter(
                "geolocation_detection_method",
                tags={"method": result.detection_method, "country": result.country_code or "unknown"}
            )

            logger.info(
                f"Country detected: {result.country_code} for {ip_address} "
                f"via {result.detection_method} ({detection_time_ms:.2f}ms, confidence: {result.confidence_score:.2f})",
                extra={"correlation_id": correlation}
            )

            return result

        except CircuitBreakerError as e:
            self.performance_metrics["circuit_breaker_trips"] += 1

            logger.warning(
                f"Circuit breaker activated for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )

            # Use direct fallback when circuit breaker is open
            fallback_result = self._get_enhanced_fallback_country(ip_address, user_preferences)
            self.performance_metrics["fallback_activations"] += 1

            # Record metrics
            detection_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("geolocation_detection_time_ms", detection_time_ms)
            metrics_collector.increment_counter("geolocation_circuit_breaker_trips")

            return fallback_result

        except Exception as e:
            logger.error(
                f"Geolocation detection failed for {ip_address}: {str(e)}",
                extra={"correlation_id": correlation}
            )

            # Record failure metrics
            detection_time_ms = (time.perf_counter() - start_time) * 1000
            metrics_collector.record_histogram("geolocation_detection_time_ms", detection_time_ms)
            metrics_collector.increment_counter("geolocation_detection_failures")

            # Return enhanced fallback result
            fallback_result = self._get_enhanced_fallback_country(ip_address, user_preferences)
            self.performance_metrics["fallback_activations"] += 1

            return fallback_result

    async def _detect_country_maxmind_only(self, ip_address: str) -> GeolocationResult:
        """
        Direct MaxMind detection method for circuit breaker integration.

        Args:
            ip_address: IP address to geolocate

        Returns:
            GeolocationResult with MaxMind detection data

        Raises:
            Exception: If MaxMind detection fails
        """
        if not self.geoip_reader:
            raise Exception("MaxMind GeoIP database not available")

        try:
            response = self.geoip_reader.country(ip_address)
            return GeolocationResult(
                country_code=response.country.iso_code,
                country_name=response.country.name,
                continent_code=response.continent.code,
                ip_address=ip_address,
                detection_method="maxmind_geoip",
                confidence_score=0.95,
                detected_at=time.time()
            )

        except geoip2.errors.AddressNotFoundError:
            raise Exception(f"IP address not found in GeoIP database: {ip_address}")
        except Exception as e:
            raise Exception(f"MaxMind GeoIP lookup failed: {str(e)}")

    def _get_enhanced_fallback_country(self, ip_address: str, user_preferences: Optional[Dict[str, Any]] = None) -> GeolocationResult:
        """
        Enhanced fallback country detection with user preferences and IP range analysis.

        Args:
            ip_address: IP address to analyze
            user_preferences: Optional user preferences for fallback

        Returns:
            GeolocationResult with enhanced fallback estimation
        """
        try:
            # Check user preferences first
            if user_preferences and "preferred_country" in user_preferences:
                return GeolocationResult(
                    country_code=user_preferences["preferred_country"],
                    country_name=self._get_country_name(user_preferences["preferred_country"]),
                    continent_code=self._get_continent_code(user_preferences["preferred_country"]),
                    ip_address=ip_address,
                    detection_method="user_preference_fallback",
                    confidence_score=0.7,
                    detected_at=time.time()
                )

            ip = ipaddress.ip_address(ip_address)

            # Enhanced IP range mapping
            ip_range_mappings = {
                "197.210.": ("NG", "Nigeria", "AF"),
                "41.203.": ("ZA", "South Africa", "AF"),
                "105.112.": ("EG", "Egypt", "AF"),
                "8.8.": ("US", "United States", "NA"),
                "1.1.": ("US", "United States", "NA"),
                "208.67.": ("US", "United States", "NA"),
            }

            # Check IP range mappings
            for ip_prefix, (country_code, country_name, continent_code) in ip_range_mappings.items():
                if ip_address.startswith(ip_prefix):
                    return GeolocationResult(
                        country_code=country_code,
                        country_name=country_name,
                        continent_code=continent_code,
                        ip_address=ip_address,
                        detection_method="ip_range_fallback",
                        confidence_score=0.6,
                        detected_at=time.time()
                    )

            # Private IP handling
            if ip.is_private:
                fallback_country = getattr(settings, 'GEOLOCATION_FALLBACK_COUNTRY', 'NG')
                return GeolocationResult(
                    country_code=fallback_country,
                    country_name=self._get_country_name(fallback_country),
                    continent_code=self._get_continent_code(fallback_country),
                    ip_address=ip_address,
                    detection_method="private_ip_fallback",
                    confidence_score=0.3,
                    detected_at=time.time()
                )

            # Default fallback for unknown public IPs
            fallback_country = getattr(settings, 'GEOLOCATION_FALLBACK_COUNTRY', 'NG')
            return GeolocationResult(
                country_code=fallback_country,
                country_name=self._get_country_name(fallback_country),
                continent_code=self._get_continent_code(fallback_country),
                ip_address=ip_address,
                detection_method="default_country_fallback",
                confidence_score=0.3,
                detected_at=time.time()
            )

        except Exception as e:
            logger.warning(f"Enhanced fallback failed for {ip_address}: {str(e)}")
            return GeolocationResult(
                country_code=None,
                country_name="Unknown",
                continent_code=None,
                ip_address=ip_address,
                detection_method="fallback_failed",
                confidence_score=0.0,
                detected_at=time.time()
            )

    def _get_country_name(self, country_code: str) -> str:
        """Get country name from country code."""
        country_names = {
            "NG": "Nigeria",
            "US": "United States",
            "GB": "United Kingdom",
            "ZA": "South Africa",
            "EG": "Egypt",
            "GH": "Ghana",
            "KE": "Kenya",
            "CA": "Canada",
            "AU": "Australia",
            "DE": "Germany",
            "FR": "France",
        }
        return country_names.get(country_code, "Unknown")

    def _get_continent_code(self, country_code: str) -> str:
        """Get continent code from country code."""
        continent_mapping = {
            "NG": "AF", "ZA": "AF", "EG": "AF", "GH": "AF", "KE": "AF",
            "US": "NA", "CA": "NA",
            "GB": "EU", "DE": "EU", "FR": "EU",
            "AU": "OC",
        }
        return continent_mapping.get(country_code, "UN")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics for monitoring.

        Returns:
            Dictionary containing performance metrics
        """
        total_requests = self.performance_metrics["total_requests"]
        cache_hit_rate = (
            self.performance_metrics["cache_hits"] / total_requests
            if total_requests > 0 else 0
        )

        circuit_breaker_stats = self.circuit_breaker.get_stats()
        resilience_stats = self.resilience_manager.get_resilience_stats()

        return {
            "service_metrics": {
                "total_requests": total_requests,
                "cache_hits": self.performance_metrics["cache_hits"],
                "cache_misses": self.performance_metrics["cache_misses"],
                "cache_hit_rate": cache_hit_rate,
                "circuit_breaker_trips": self.performance_metrics["circuit_breaker_trips"],
                "fallback_activations": self.performance_metrics["fallback_activations"],
                "cache_size": len(self.cache)
            },
            "circuit_breaker": circuit_breaker_stats,
            "resilience_manager": resilience_stats,
            "database_status": {
                "database_path": self.geoip_database_path,
                "database_available": self.geoip_reader is not None,
                "cache_ttl_hours": self.cache_ttl.total_seconds() / 3600
            }
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        Comprehensive health check for geolocation service.

        Returns:
            Health check results
        """
        health_status = {
            "service": "geolocation",
            "status": "healthy",
            "checks": {},
            "performance": {}
        }

        try:
            # Test database connectivity
            if self.geoip_reader:
                try:
                    # Test with a known IP
                    test_result = await self._detect_country_maxmind_only("*******")
                    health_status["checks"]["database"] = {
                        "status": "healthy",
                        "test_result": test_result.country_code
                    }
                except Exception as e:
                    health_status["checks"]["database"] = {
                        "status": "unhealthy",
                        "error": str(e)
                    }
                    health_status["status"] = "degraded"
            else:
                health_status["checks"]["database"] = {
                    "status": "unavailable",
                    "message": "GeoIP database not loaded"
                }
                health_status["status"] = "degraded"

            # Check circuit breaker status
            cb_stats = self.circuit_breaker.get_stats()
            health_status["checks"]["circuit_breaker"] = {
                "status": "healthy" if cb_stats["state"] == "closed" else "degraded",
                "state": cb_stats["state"],
                "failure_rate": 1 - cb_stats.get("success_rate", 1)
            }

            # Performance metrics
            metrics = self.get_performance_metrics()
            health_status["performance"] = {
                "cache_hit_rate": metrics["service_metrics"]["cache_hit_rate"],
                "total_requests": metrics["service_metrics"]["total_requests"],
                "fallback_rate": (
                    metrics["service_metrics"]["fallback_activations"] /
                    max(metrics["service_metrics"]["total_requests"], 1)
                )
            }

        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)

        return health_status

    async def get_geolocation_from_request(self, request: Request) -> GeolocationResult:
        """
        Complete geolocation detection from FastAPI request.

        Args:
            request: FastAPI request object

        Returns:
            GeolocationResult with complete detection data
        """
        correlation = correlation_id.get()

        try:
            # Extract client IP
            client_ip = await self.extract_client_ip(request)

            # Detect country from IP
            geolocation_result = await self.detect_country_from_ip(client_ip)

            logger.info(
                f"Complete geolocation detection: {geolocation_result.country_code} "
                f"for IP {client_ip} via {geolocation_result.detection_method}",
                extra={"correlation_id": correlation}
            )

            return geolocation_result

        except Exception as e:
            logger.error(
                f"Complete geolocation detection failed: {str(e)}",
                extra={"correlation_id": correlation}
            )
            raise ServiceError(f"Geolocation detection failed: {str(e)}")

    async def close(self) -> None:
        """Close GeoIP database reader."""
        if self.geoip_reader:
            self.geoip_reader.close()
            logger.info("GeoIP database reader closed")


# Global geolocation service instance
_geolocation_service: Optional[GeolocationService] = None


def get_geolocation_service() -> GeolocationService:
    """
    Get geolocation service instance (singleton pattern).

    Returns:
        GeolocationService: Configured geolocation service
    """
    global _geolocation_service
    if _geolocation_service is None:
        _geolocation_service = GeolocationService()
    return _geolocation_service
