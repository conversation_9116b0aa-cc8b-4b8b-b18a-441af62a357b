"""
Performance Tests for Service Management System.

This module provides comprehensive performance tests for service management operations including:
- Service creation and update performance validation
- Search query performance testing with large datasets
- Database query optimization validation
- Concurrent user load testing
- Memory usage and resource consumption testing

Implements performance testing with targets:
- <500ms for service creation operations
- <200ms for search queries
- <100ms for status updates
- >1000 concurrent users support
"""

import pytest
import asyncio
import time
import psutil
import statistics
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
from concurrent.futures import ThreadPoolExecutor

from app.main import app
from app.core.deps import get_db
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service, ServiceCategory
from tests.conftest import (
    TestingSessionLocal,
    create_test_user,
    create_test_vendor,
    create_test_service,
    get_test_token_headers
)


class TestServiceManagementPerformance:
    """Performance test suite for service management operations."""

    @pytest.fixture
    async def db_session(self):
        """Create test database session with rollback."""
        async with TestingSessionLocal() as session:
            yield session
            await session.rollback()

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession):
        """Create test vendor for performance testing."""
        vendor_user = await create_test_user(
            db_session,
            email="<EMAIL>",
            password="testpass123",
            role="vendor"
        )
        return await create_test_vendor(
            db_session,
            user_id=vendor_user.id,
            business_name="Performance Test Vendor",
            business_type="photography"
        )

    @pytest.fixture
    async def test_category(self, db_session: AsyncSession):
        """Create test service category."""
        category = ServiceCategory(
            name="Performance Testing",
            slug="performance-testing",
            description="Category for performance testing",
            is_active=True
        )
        db_session.add(category)
        await db_session.commit()
        return category

    @pytest.fixture
    async def auth_headers(self, test_vendor: Vendor, db_session: AsyncSession):
        """Get authentication headers for test vendor."""
        # Get the vendor's user
        vendor_user = await db_session.get(User, test_vendor.user_id)
        return await get_test_token_headers(vendor_user)

    @pytest.fixture
    async def large_dataset(
        self, 
        db_session: AsyncSession, 
        test_vendor: Vendor, 
        test_category: ServiceCategory
    ):
        """Create large dataset for performance testing."""
        services = []
        batch_size = 50
        total_services = 200
        
        for i in range(0, total_services, batch_size):
            batch_services = []
            for j in range(batch_size):
                if i + j >= total_services:
                    break
                    
                service = Service(
                    vendor_id=test_vendor.id,
                    category_id=test_category.id,
                    title=f"Performance Test Service {i + j + 1}",
                    description=f"Description for performance test service {i + j + 1}",
                    short_description=f"Short description {i + j + 1}",
                    location="Lagos, Nigeria",
                    duration_minutes=60,
                    max_participants=10,
                    status="active",
                    tags=["performance", "testing", f"service{i + j + 1}"],
                    average_rating=Decimal("4.0") + Decimal(str((i + j) % 10)) / 10,
                    total_reviews=(i + j) % 50 + 1,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                batch_services.append(service)
                services.append(service)
            
            # Add batch to database
            db_session.add_all(batch_services)
            await db_session.commit()
        
        return services

    @pytest.mark.asyncio
    async def test_service_creation_performance(
        self,
        db_session: AsyncSession,
        test_vendor: Vendor,
        test_category: ServiceCategory,
        auth_headers: dict
    ):
        """Test service creation performance - Target: <500ms."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare service creation data
            service_data = {
                "title": "Performance Test Service",
                "description": "A service created for performance testing",
                "short_description": "Performance test service",
                "category_id": test_category.id,
                "location": "Lagos, Nigeria",
                "duration_minutes": 120,
                "max_participants": 15,
                "tags": ["performance", "testing"],
                "pricing_tiers": [
                    {
                        "tier_name": "Standard",
                        "price": 25000.00,
                        "currency": "NGN",
                        "description": "Standard package"
                    }
                ]
            }
            
            # Measure creation time
            start_time = time.perf_counter()
            response = await client.post(
                "/api/v1/services/",
                json=service_data,
                headers=auth_headers
            )
            end_time = time.perf_counter()
            
            # Assert
            execution_time_ms = (end_time - start_time) * 1000
            assert response.status_code == status.HTTP_201_CREATED
            assert execution_time_ms < 500  # Target: <500ms for creation operations
            
            print(f"Service creation time: {execution_time_ms:.2f}ms")
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_query_performance(
        self,
        db_session: AsyncSession,
        large_dataset: list,
        auth_headers: dict
    ):
        """Test search query performance with large dataset - Target: <200ms."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test different search scenarios
            search_scenarios = [
                {"query": "performance", "per_page": 20},
                {"query": "testing", "per_page": 50},
                {"category_ids": [large_dataset[0].category_id], "per_page": 30},
                {"min_rating": 4.0, "per_page": 25},
                {"tags": ["performance"], "per_page": 40}
            ]
            
            execution_times = []
            
            for scenario in search_scenarios:
                start_time = time.perf_counter()
                response = await client.post(
                    "/api/v1/search/search",
                    json=scenario,
                    headers=auth_headers
                )
                end_time = time.perf_counter()
                
                execution_time_ms = (end_time - start_time) * 1000
                execution_times.append(execution_time_ms)
                
                assert response.status_code == status.HTTP_200_OK
                assert execution_time_ms < 200  # Target: <200ms for queries
                
                print(f"Search scenario {scenario}: {execution_time_ms:.2f}ms")
            
            # Calculate statistics
            avg_time = statistics.mean(execution_times)
            max_time = max(execution_times)
            min_time = min(execution_times)
            
            print(f"Search performance - Avg: {avg_time:.2f}ms, Max: {max_time:.2f}ms, Min: {min_time:.2f}ms")
            
            assert avg_time < 150  # Average should be well below target
            assert max_time < 200  # No query should exceed target
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_service_update_performance(
        self,
        db_session: AsyncSession,
        large_dataset: list,
        auth_headers: dict
    ):
        """Test service update performance - Target: <100ms for status updates."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test simple status update
            service = large_dataset[0]
            update_data = {"status": "inactive"}
            
            start_time = time.perf_counter()
            response = await client.patch(
                f"/api/v1/services/{service.id}",
                json=update_data,
                headers=auth_headers
            )
            end_time = time.perf_counter()
            
            execution_time_ms = (end_time - start_time) * 1000
            
            assert response.status_code == status.HTTP_200_OK
            assert execution_time_ms < 100  # Target: <100ms for status updates
            
            print(f"Service status update time: {execution_time_ms:.2f}ms")
            
            # Test full service update
            full_update_data = {
                "title": "Updated Performance Test Service",
                "description": "Updated description for performance testing",
                "duration_minutes": 90,
                "max_participants": 20
            }
            
            start_time = time.perf_counter()
            response = await client.patch(
                f"/api/v1/services/{service.id}",
                json=full_update_data,
                headers=auth_headers
            )
            end_time = time.perf_counter()
            
            execution_time_ms = (end_time - start_time) * 1000
            
            assert response.status_code == status.HTTP_200_OK
            assert execution_time_ms < 300  # Target: <300ms for full updates
            
            print(f"Service full update time: {execution_time_ms:.2f}ms")
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_concurrent_search_performance(
        self,
        db_session: AsyncSession,
        large_dataset: list,
        auth_headers: dict
    ):
        """Test concurrent search performance - Target: >100 concurrent users."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare concurrent search requests
            concurrent_requests = 50  # Test with 50 concurrent requests
            search_request = {
                "query": "performance testing",
                "per_page": 20,
                "sort_by": "relevance"
            }
            
            async def perform_search():
                """Perform a single search request."""
                start_time = time.perf_counter()
                response = await client.post(
                    "/api/v1/search/search",
                    json=search_request,
                    headers=auth_headers
                )
                end_time = time.perf_counter()
                return response.status_code, (end_time - start_time) * 1000
            
            # Execute concurrent searches
            start_time = time.perf_counter()
            tasks = [perform_search() for _ in range(concurrent_requests)]
            results = await asyncio.gather(*tasks)
            end_time = time.perf_counter()
            
            # Analyze results
            total_time_ms = (end_time - start_time) * 1000
            successful_requests = sum(1 for status_code, _ in results if status_code == 200)
            execution_times = [exec_time for _, exec_time in results]
            
            success_rate = (successful_requests / concurrent_requests) * 100
            avg_response_time = statistics.mean(execution_times)
            max_response_time = max(execution_times)
            throughput = concurrent_requests / (total_time_ms / 1000)  # requests per second
            
            print(f"Concurrent search performance:")
            print(f"  - Concurrent requests: {concurrent_requests}")
            print(f"  - Success rate: {success_rate:.1f}%")
            print(f"  - Average response time: {avg_response_time:.2f}ms")
            print(f"  - Max response time: {max_response_time:.2f}ms")
            print(f"  - Throughput: {throughput:.1f} requests/second")
            
            # Assert performance targets
            assert success_rate >= 95  # At least 95% success rate
            assert avg_response_time < 300  # Average response time under load
            assert max_response_time < 500  # No request should take too long
            assert throughput > 20  # Minimum throughput requirement
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_database_query_optimization(
        self,
        db_session: AsyncSession,
        large_dataset: list
    ):
        """Test database query optimization with large dataset."""
        from app.repositories.service_repository import ServiceRepository
        from app.repositories.search_repository import AdvancedSearchRepository
        
        # Test service repository performance
        service_repo = ServiceRepository(db_session)
        search_repo = AdvancedSearchRepository(db_session)
        
        # Test get_by_vendor query performance
        vendor_id = large_dataset[0].vendor_id
        
        start_time = time.perf_counter()
        services = await service_repo.get_by_vendor(vendor_id, limit=50)
        end_time = time.perf_counter()
        
        query_time_ms = (end_time - start_time) * 1000
        assert query_time_ms < 100  # Target: <100ms for vendor services query
        assert len(services) > 0
        
        print(f"Get services by vendor query time: {query_time_ms:.2f}ms")
        
        # Test search repository performance
        start_time = time.perf_counter()
        search_results, total_count, aggregations = await search_repo.advanced_search(
            query="performance",
            page=1,
            per_page=20
        )
        end_time = time.perf_counter()
        
        search_time_ms = (end_time - start_time) * 1000
        assert search_time_ms < 200  # Target: <200ms for advanced search
        
        print(f"Advanced search query time: {search_time_ms:.2f}ms")
        print(f"Search results count: {len(search_results)}, Total: {total_count}")

    @pytest.mark.asyncio
    async def test_memory_usage_monitoring(
        self,
        db_session: AsyncSession,
        large_dataset: list,
        auth_headers: dict
    ):
        """Test memory usage during intensive operations."""
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Perform intensive search operations
            for i in range(20):
                search_request = {
                    "query": f"performance test {i}",
                    "per_page": 50,
                    "page": (i % 4) + 1
                }
                
                response = await client.post(
                    "/api/v1/search/search",
                    json=search_request,
                    headers=auth_headers
                )
                assert response.status_code == status.HTTP_200_OK
                
                # Check memory every 5 iterations
                if i % 5 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_increase = current_memory - initial_memory
                    print(f"Iteration {i}: Memory usage: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                    
                    # Memory should not increase excessively
                    assert memory_increase < 100  # Should not increase by more than 100MB
            
            # Force garbage collection
            gc.collect()
            
            # Final memory check
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            total_increase = final_memory - initial_memory
            
            print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB")
            print(f"Total memory increase: {total_increase:.1f}MB")
            
            # Memory increase should be reasonable
            assert total_increase < 50  # Should not increase by more than 50MB after GC
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_pagination_performance(
        self,
        db_session: AsyncSession,
        large_dataset: list,
        auth_headers: dict
    ):
        """Test pagination performance with large datasets."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test different page sizes and positions
            test_scenarios = [
                {"page": 1, "per_page": 10},
                {"page": 5, "per_page": 20},
                {"page": 10, "per_page": 15},
                {"page": 1, "per_page": 50},
                {"page": 20, "per_page": 5}
            ]
            
            for scenario in test_scenarios:
                search_request = {
                    "query": "performance",
                    **scenario
                }
                
                start_time = time.perf_counter()
                response = await client.post(
                    "/api/v1/search/search",
                    json=search_request,
                    headers=auth_headers
                )
                end_time = time.perf_counter()
                
                execution_time_ms = (end_time - start_time) * 1000
                
                assert response.status_code == status.HTTP_200_OK
                assert execution_time_ms < 200  # Pagination should not significantly impact performance
                
                data = response.json()
                assert len(data["results"]) <= scenario["per_page"]
                assert data["metadata"]["page"] == scenario["page"]
                assert data["metadata"]["per_page"] == scenario["per_page"]
                
                print(f"Pagination {scenario}: {execution_time_ms:.2f}ms")
            
            # Clean up
            app.dependency_overrides.clear()
