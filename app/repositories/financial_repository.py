"""
Financial Repository for Culture Connect Backend API.

This module provides comprehensive repository classes for financial-related database operations:
- RevenueRecordRepository: Revenue tracking and analytics with multi-currency support
- ReconciliationRecordRepository: Financial reconciliation and variance detection
- Advanced financial analytics and reporting capabilities
- Performance optimization with composite index utilization
- Compliance and audit trail management

Implements Phase 1 Payment & Transaction Management System financial layer.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, case, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.financial_models import RevenueRecord, ReconciliationRecord, RevenueCategory, ReconciliationStatus
from app.core.payment.config import PaymentProviderType
from app.repositories.base import BaseRepository, PaginationParams, FilterParams, QueryResult, RepositoryError
import logging

logger = logging.getLogger(__name__)


class RevenueRecordRepository(BaseRepository[RevenueRecord]):
    """
    Repository for revenue record management and analytics.

    Provides optimized database operations for revenue tracking with:
    - Multi-currency revenue tracking and analytics
    - Performance-optimized queries using composite indexes
    - Revenue categorization and settlement tracking
    - Tax compliance and financial reporting
    - Comprehensive audit logging and compliance features

    Performance targets:
    - Revenue creation: <200ms
    - Analytics queries: <200ms
    - Financial reports: <500ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(RevenueRecord, db)
        self.logger = logging.getLogger(f"{__name__}.RevenueRecordRepository")

    async def create_revenue_record(
        self,
        category: RevenueCategory,
        gross_amount: Decimal,
        platform_fee: Decimal,
        processing_fee: Decimal,
        net_vendor_amount: Decimal,
        net_platform_amount: Decimal,
        transaction_date: datetime,
        reference_id: str,
        booking_id: Optional[int] = None,
        payment_id: Optional[int] = None,
        vendor_id: Optional[int] = None,
        user_id: Optional[int] = None,
        currency: str = "NGN",
        **kwargs
    ) -> RevenueRecord:
        """
        Create a new revenue record with comprehensive tracking.

        Args:
            category: Revenue category
            gross_amount: Gross revenue amount
            platform_fee: Platform commission fee
            processing_fee: Payment processing fee
            net_vendor_amount: Net amount for vendor
            net_platform_amount: Net amount for platform
            transaction_date: Transaction date
            reference_id: Unique reference ID
            booking_id: Optional associated booking ID
            payment_id: Optional associated payment ID
            vendor_id: Optional associated vendor ID
            user_id: Optional associated user ID
            currency: Revenue currency
            **kwargs: Additional revenue fields

        Returns:
            Created revenue record instance
        """
        try:
            revenue_data = {
                "category": category,
                "gross_amount": gross_amount,
                "platform_fee": platform_fee,
                "processing_fee": processing_fee,
                "net_vendor_amount": net_vendor_amount,
                "net_platform_amount": net_platform_amount,
                "transaction_date": transaction_date,
                "reference_id": reference_id,
                "booking_id": booking_id,
                "payment_id": payment_id,
                "vendor_id": vendor_id,
                "user_id": user_id,
                "currency": currency,
                **kwargs
            }

            revenue = await self.create(revenue_data)

            self.logger.info(
                "Revenue record created successfully",
                extra={
                    "revenue_id": revenue.id,
                    "reference_id": reference_id,
                    "category": category.value,
                    "gross_amount": float(gross_amount),
                    "currency": currency,
                    "vendor_id": vendor_id,
                    "user_id": user_id
                }
            )

            return revenue

        except Exception as e:
            self.logger.error(
                "Failed to create revenue record",
                extra={
                    "reference_id": reference_id,
                    "category": category.value,
                    "gross_amount": float(gross_amount),
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create revenue record: {str(e)}", e)

    async def get_revenue_by_category(
        self,
        category: RevenueCategory,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        vendor_id: Optional[int] = None,
        currency: str = "NGN",
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[RevenueRecord]:
        """
        Get revenue records by category with filtering.

        Uses idx_revenue_category_date composite index.

        Args:
            category: Revenue category to filter by
            date_from: Start date filter
            date_to: End date filter
            vendor_id: Optional vendor ID filter
            currency: Currency filter
            pagination: Pagination parameters

        Returns:
            Query result with revenue records and metadata
        """
        try:
            query = select(RevenueRecord).where(
                and_(
                    RevenueRecord.category == category,
                    RevenueRecord.currency == currency
                )
            )

            if date_from:
                query = query.where(RevenueRecord.transaction_date >= date_from)

            if date_to:
                query = query.where(RevenueRecord.transaction_date <= date_to)

            if vendor_id:
                query = query.where(RevenueRecord.vendor_id == vendor_id)

            # Order by transaction date (newest first)
            query = query.order_by(desc(RevenueRecord.transaction_date))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get revenue by category",
                extra={"category": category.value, "vendor_id": vendor_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get revenue by category: {str(e)}", e)

    async def get_vendor_revenue_summary(
        self,
        vendor_id: int,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get vendor revenue summary with aggregated metrics.

        Uses idx_revenue_vendor_date composite index.

        Args:
            vendor_id: Vendor ID
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Vendor revenue summary dictionary
        """
        try:
            base_query = select(RevenueRecord).where(
                and_(
                    RevenueRecord.vendor_id == vendor_id,
                    RevenueRecord.currency == currency
                )
            )

            if date_from:
                base_query = base_query.where(RevenueRecord.transaction_date >= date_from)

            if date_to:
                base_query = base_query.where(RevenueRecord.transaction_date <= date_to)

            # Summary query
            summary_query = select(
                func.count(RevenueRecord.id).label('total_records'),
                func.sum(RevenueRecord.gross_amount).label('total_gross_amount'),
                func.sum(RevenueRecord.platform_fee).label('total_platform_fees'),
                func.sum(RevenueRecord.processing_fee).label('total_processing_fees'),
                func.sum(RevenueRecord.net_vendor_amount).label('total_net_vendor_amount'),
                func.sum(RevenueRecord.net_platform_amount).label('total_net_platform_amount'),
                func.sum(RevenueRecord.tax_amount).label('total_tax_amount'),
                func.count(case((RevenueRecord.category == RevenueCategory.SERVICE_BOOKING, 1))).label('service_bookings'),
                func.count(case((RevenueRecord.category == RevenueCategory.PLATFORM_FEE, 1))).label('platform_fees'),
                func.count(case((RevenueRecord.category == RevenueCategory.PAYMENT_PROCESSING, 1))).label('processing_fees')
            ).select_from(base_query.subquery())

            result = await self.db.execute(summary_query)
            row = result.first()

            return {
                "vendor_id": vendor_id,
                "currency": currency,
                "period": {
                    "from": date_from.isoformat() if date_from else None,
                    "to": date_to.isoformat() if date_to else None
                },
                "total_records": row.total_records or 0,
                "total_gross_amount": float(row.total_gross_amount or 0),
                "total_platform_fees": float(row.total_platform_fees or 0),
                "total_processing_fees": float(row.total_processing_fees or 0),
                "total_net_vendor_amount": float(row.total_net_vendor_amount or 0),
                "total_net_platform_amount": float(row.total_net_platform_amount or 0),
                "total_tax_amount": float(row.total_tax_amount or 0),
                "breakdown": {
                    "service_bookings": row.service_bookings or 0,
                    "platform_fees": row.platform_fees or 0,
                    "processing_fees": row.processing_fees or 0
                }
            }

        except Exception as e:
            self.logger.error(
                "Failed to get vendor revenue summary",
                extra={"vendor_id": vendor_id, "currency": currency, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get vendor revenue summary: {str(e)}", e)

    async def get_platform_revenue_analytics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN",
        provider: Optional[PaymentProviderType] = None
    ) -> Dict[str, Any]:
        """
        Get platform-wide revenue analytics.

        Uses idx_revenue_provider_date composite index.

        Args:
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter
            provider: Optional provider filter

        Returns:
            Platform revenue analytics dictionary
        """
        try:
            base_query = select(RevenueRecord).where(RevenueRecord.currency == currency)

            if date_from:
                base_query = base_query.where(RevenueRecord.transaction_date >= date_from)

            if date_to:
                base_query = base_query.where(RevenueRecord.transaction_date <= date_to)

            if provider:
                base_query = base_query.where(RevenueRecord.provider == provider)

            # Analytics query
            analytics_query = select(
                func.count(RevenueRecord.id).label('total_records'),
                func.sum(RevenueRecord.gross_amount).label('total_gross_revenue'),
                func.sum(RevenueRecord.net_platform_amount).label('total_platform_revenue'),
                func.sum(RevenueRecord.net_vendor_amount).label('total_vendor_revenue'),
                func.sum(RevenueRecord.tax_amount).label('total_tax_collected'),
                func.count(func.distinct(RevenueRecord.vendor_id)).label('active_vendors'),
                func.count(func.distinct(RevenueRecord.user_id)).label('active_users'),
                func.avg(RevenueRecord.gross_amount).label('avg_transaction_amount'),
                func.max(RevenueRecord.gross_amount).label('max_transaction_amount'),
                func.min(RevenueRecord.gross_amount).label('min_transaction_amount')
            ).select_from(base_query.subquery())

            result = await self.db.execute(analytics_query)
            row = result.first()

            return {
                "currency": currency,
                "provider": provider.value if provider else "all",
                "period": {
                    "from": date_from.isoformat() if date_from else None,
                    "to": date_to.isoformat() if date_to else None
                },
                "totals": {
                    "records": row.total_records or 0,
                    "gross_revenue": float(row.total_gross_revenue or 0),
                    "platform_revenue": float(row.total_platform_revenue or 0),
                    "vendor_revenue": float(row.total_vendor_revenue or 0),
                    "tax_collected": float(row.total_tax_collected or 0)
                },
                "metrics": {
                    "active_vendors": row.active_vendors or 0,
                    "active_users": row.active_users or 0,
                    "avg_transaction_amount": float(row.avg_transaction_amount or 0),
                    "max_transaction_amount": float(row.max_transaction_amount or 0),
                    "min_transaction_amount": float(row.min_transaction_amount or 0),
                    "platform_take_rate": (row.total_platform_revenue / row.total_gross_revenue * 100) if row.total_gross_revenue > 0 else 0
                }
            }

        except Exception as e:
            self.logger.error(
                "Failed to get platform revenue analytics",
                extra={"currency": currency, "provider": provider.value if provider else None, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get platform revenue analytics: {str(e)}", e)


class ReconciliationRecordRepository(BaseRepository[ReconciliationRecord]):
    """
    Repository for reconciliation record management and variance detection.

    Provides database operations for financial reconciliation with:
    - Automated reconciliation with provider statements
    - Discrepancy detection and resolution tracking
    - Performance-optimized reconciliation queries
    - Compliance and audit trail management
    - Integration with payment and revenue systems

    Performance targets:
    - Reconciliation creation: <300ms
    - Variance detection: <200ms
    - Reconciliation queries: <200ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ReconciliationRecord, db)
        self.logger = logging.getLogger(f"{__name__}.ReconciliationRecordRepository")

    async def create_reconciliation_record(
        self,
        provider: PaymentProviderType,
        period_start: datetime,
        period_end: datetime,
        expected_amount: Decimal,
        actual_amount: Decimal,
        expected_transaction_count: int,
        actual_transaction_count: int,
        reference_id: str,
        currency: str = "NGN",
        provider_statement_id: Optional[str] = None,
        **kwargs
    ) -> ReconciliationRecord:
        """
        Create a new reconciliation record with variance calculation.

        Args:
            provider: Payment provider being reconciled
            period_start: Reconciliation period start
            period_end: Reconciliation period end
            expected_amount: Expected amount from internal records
            actual_amount: Actual amount from provider statement
            expected_transaction_count: Expected transaction count
            actual_transaction_count: Actual transaction count
            reference_id: Unique reconciliation reference
            currency: Reconciliation currency
            provider_statement_id: Provider statement ID
            **kwargs: Additional reconciliation fields

        Returns:
            Created reconciliation record instance
        """
        try:
            # Calculate variance
            variance = actual_amount - expected_amount

            reconciliation_data = {
                "provider": provider,
                "period_start": period_start,
                "period_end": period_end,
                "expected_amount": expected_amount,
                "actual_amount": actual_amount,
                "variance": variance,
                "expected_transaction_count": expected_transaction_count,
                "actual_transaction_count": actual_transaction_count,
                "reference_id": reference_id,
                "currency": currency,
                "provider_statement_id": provider_statement_id,
                "status": ReconciliationStatus.PENDING,
                **kwargs
            }

            reconciliation = await self.create(reconciliation_data)

            self.logger.info(
                "Reconciliation record created successfully",
                extra={
                    "reconciliation_id": reconciliation.id,
                    "reference_id": reference_id,
                    "provider": provider.value,
                    "expected_amount": float(expected_amount),
                    "actual_amount": float(actual_amount),
                    "variance": float(variance),
                    "currency": currency,
                    "period": f"{period_start.date()} to {period_end.date()}"
                }
            )

            return reconciliation

        except Exception as e:
            self.logger.error(
                "Failed to create reconciliation record",
                extra={
                    "reference_id": reference_id,
                    "provider": provider.value,
                    "expected_amount": float(expected_amount),
                    "actual_amount": float(actual_amount),
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create reconciliation record: {str(e)}", e)

    async def update_reconciliation_status(
        self,
        reconciliation_id: int,
        status: ReconciliationStatus,
        reconciled_by: Optional[int] = None,
        resolution_notes: Optional[str] = None,
        **kwargs
    ) -> Optional[ReconciliationRecord]:
        """
        Update reconciliation status with tracking.

        Args:
            reconciliation_id: Reconciliation ID to update
            status: New reconciliation status
            reconciled_by: User ID who performed reconciliation
            resolution_notes: Resolution notes
            **kwargs: Additional fields to update

        Returns:
            Updated reconciliation record or None if not found
        """
        try:
            update_data = {
                "status": status,
                **kwargs
            }

            if status == ReconciliationStatus.COMPLETED:
                update_data.update({
                    "reconciled_at": datetime.now(timezone.utc),
                    "reconciled_by": reconciled_by
                })

            if resolution_notes:
                update_data["resolution_notes"] = resolution_notes

            reconciliation = await self.update(reconciliation_id, update_data)

            if reconciliation:
                self.logger.info(
                    "Reconciliation status updated",
                    extra={
                        "reconciliation_id": reconciliation_id,
                        "new_status": status.value,
                        "reconciled_by": reconciled_by
                    }
                )

            return reconciliation

        except Exception as e:
            self.logger.error(
                "Failed to update reconciliation status",
                extra={
                    "reconciliation_id": reconciliation_id,
                    "status": status.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update reconciliation status: {str(e)}", e)

    async def get_reconciliations_by_provider(
        self,
        provider: PaymentProviderType,
        status: Optional[ReconciliationStatus] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReconciliationRecord]:
        """
        Get reconciliation records by provider with filtering.

        Uses idx_reconciliation_provider_period composite index.

        Args:
            provider: Payment provider to filter by
            status: Optional reconciliation status filter
            date_from: Start date filter (period_start)
            date_to: End date filter (period_end)
            pagination: Pagination parameters

        Returns:
            Query result with reconciliation records and metadata
        """
        try:
            query = select(ReconciliationRecord).where(ReconciliationRecord.provider == provider)

            if status:
                query = query.where(ReconciliationRecord.status == status)

            if date_from:
                query = query.where(ReconciliationRecord.period_start >= date_from)

            if date_to:
                query = query.where(ReconciliationRecord.period_end <= date_to)

            # Order by period start date (newest first)
            query = query.order_by(desc(ReconciliationRecord.period_start))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get reconciliations by provider",
                extra={"provider": provider.value, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get reconciliations by provider: {str(e)}", e)

    async def get_discrepancy_reconciliations(
        self,
        variance_threshold: Decimal = Decimal("0.01"),
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReconciliationRecord]:
        """
        Get reconciliation records with discrepancies.

        Uses idx_reconciliation_variance composite index.

        Args:
            variance_threshold: Minimum variance to consider as discrepancy
            date_from: Start date filter
            date_to: End date filter
            pagination: Pagination parameters

        Returns:
            Query result with discrepancy reconciliations and metadata
        """
        try:
            query = select(ReconciliationRecord).where(
                or_(
                    ReconciliationRecord.variance > variance_threshold,
                    ReconciliationRecord.variance < -variance_threshold
                )
            )

            if date_from:
                query = query.where(ReconciliationRecord.period_start >= date_from)

            if date_to:
                query = query.where(ReconciliationRecord.period_end <= date_to)

            # Order by variance magnitude (largest discrepancies first)
            query = query.order_by(desc(func.abs(ReconciliationRecord.variance)))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get discrepancy reconciliations",
                extra={"variance_threshold": float(variance_threshold), "error": str(e)}
            )
            raise RepositoryError(f"Failed to get discrepancy reconciliations: {str(e)}", e)

    async def get_reconciliation_analytics(
        self,
        provider: Optional[PaymentProviderType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get reconciliation analytics and metrics.

        Args:
            provider: Optional provider filter
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Reconciliation analytics dictionary
        """
        try:
            base_query = select(ReconciliationRecord).where(ReconciliationRecord.currency == currency)

            if provider:
                base_query = base_query.where(ReconciliationRecord.provider == provider)

            if date_from:
                base_query = base_query.where(ReconciliationRecord.period_start >= date_from)

            if date_to:
                base_query = base_query.where(ReconciliationRecord.period_end <= date_to)

            # Analytics query
            analytics_query = select(
                func.count(ReconciliationRecord.id).label('total_reconciliations'),
                func.sum(ReconciliationRecord.expected_amount).label('total_expected_amount'),
                func.sum(ReconciliationRecord.actual_amount).label('total_actual_amount'),
                func.sum(ReconciliationRecord.variance).label('total_variance'),
                func.sum(func.abs(ReconciliationRecord.variance)).label('total_absolute_variance'),
                func.count(case((ReconciliationRecord.status == ReconciliationStatus.COMPLETED, 1))).label('completed_reconciliations'),
                func.count(case((ReconciliationRecord.status == ReconciliationStatus.DISCREPANCY, 1))).label('discrepancy_reconciliations'),
                func.count(case((func.abs(ReconciliationRecord.variance) > 0.01, 1))).label('variance_reconciliations'),
                func.avg(func.abs(ReconciliationRecord.variance)).label('avg_absolute_variance'),
                func.max(func.abs(ReconciliationRecord.variance)).label('max_absolute_variance')
            ).select_from(base_query.subquery())

            result = await self.db.execute(analytics_query)
            row = result.first()

            return {
                "currency": currency,
                "provider": provider.value if provider else "all",
                "period": {
                    "from": date_from.isoformat() if date_from else None,
                    "to": date_to.isoformat() if date_to else None
                },
                "totals": {
                    "reconciliations": row.total_reconciliations or 0,
                    "expected_amount": float(row.total_expected_amount or 0),
                    "actual_amount": float(row.total_actual_amount or 0),
                    "variance": float(row.total_variance or 0),
                    "absolute_variance": float(row.total_absolute_variance or 0)
                },
                "status_breakdown": {
                    "completed": row.completed_reconciliations or 0,
                    "discrepancies": row.discrepancy_reconciliations or 0,
                    "with_variance": row.variance_reconciliations or 0
                },
                "metrics": {
                    "completion_rate": (row.completed_reconciliations / row.total_reconciliations * 100) if row.total_reconciliations > 0 else 0,
                    "discrepancy_rate": (row.discrepancy_reconciliations / row.total_reconciliations * 100) if row.total_reconciliations > 0 else 0,
                    "variance_rate": (row.variance_reconciliations / row.total_reconciliations * 100) if row.total_reconciliations > 0 else 0,
                    "avg_absolute_variance": float(row.avg_absolute_variance or 0),
                    "max_absolute_variance": float(row.max_absolute_variance or 0),
                    "accuracy_percentage": ((row.total_expected_amount - row.total_absolute_variance) / row.total_expected_amount * 100) if row.total_expected_amount > 0 else 0
                }
            }

        except Exception as e:
            self.logger.error(
                "Failed to get reconciliation analytics",
                extra={"provider": provider.value if provider else None, "currency": currency, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get reconciliation analytics: {str(e)}", e)
