# Availability Management API Documentation

## Overview

The Availability Management System provides comprehensive vendor availability configuration, real-time availability checking, and slot management capabilities for the CultureConnect marketplace platform.

## API Endpoints

### Base URL
```
/api/v1/availability
```

### Authentication
All endpoints require authentication. Vendor-specific endpoints require vendor role authorization.

## Vendor Availability Configuration

### Create Vendor Availability Configuration
```http
POST /vendor-availability
```

**Description**: Create availability configuration for a vendor.

**Authorization**: Vendor role required

**Request Body**:
```json
{
  "timezone": "America/New_York",
  "advance_booking_days": 90,
  "min_booking_notice_hours": 24,
  "max_booking_notice_days": 30,
  "default_slot_duration_minutes": 60,
  "buffer_time_minutes": 15,
  "earliest_booking_time": "09:00:00",
  "latest_booking_time": "17:00:00",
  "service_id": null,
  "max_daily_bookings": null,
  "notes": "Standard business hours"
}
```

**Response** (201 Created):
```json
{
  "id": 1,
  "vendor_id": 123,
  "service_id": null,
  "timezone": "America/New_York",
  "advance_booking_days": 90,
  "min_booking_notice_hours": 24,
  "max_booking_notice_days": 30,
  "default_slot_duration_minutes": 60,
  "buffer_time_minutes": 15,
  "earliest_booking_time": "09:00:00",
  "latest_booking_time": "17:00:00",
  "max_daily_bookings": null,
  "notes": "Standard business hours",
  "is_active": true
}
```

**Performance Target**: <100ms response time

### Get Vendor Availability Configuration
```http
GET /vendor-availability?service_id={service_id}
```

**Description**: Retrieve vendor's availability configuration.

**Authorization**: Vendor role required

**Query Parameters**:
- `service_id` (optional): Filter by specific service

**Response** (200 OK):
```json
{
  "id": 1,
  "vendor_id": 123,
  "service_id": null,
  "timezone": "America/New_York",
  "advance_booking_days": 90,
  "min_booking_notice_hours": 24,
  "max_booking_notice_days": 30,
  "default_slot_duration_minutes": 60,
  "buffer_time_minutes": 15,
  "earliest_booking_time": "09:00:00",
  "latest_booking_time": "17:00:00",
  "max_daily_bookings": null,
  "notes": "Standard business hours",
  "is_active": true
}
```

**Performance Target**: <50ms response time (cached)

## Real-time Availability Checking

### Check Availability
```http
POST /check-availability
```

**Description**: Real-time availability checking for booking requests.

**Authorization**: Any authenticated user

**Request Body**:
```json
{
  "vendor_id": 123,
  "service_id": null,
  "start_datetime": "2025-01-15T10:00:00Z",
  "end_datetime": "2025-01-15T11:00:00Z",
  "required_capacity": 1
}
```

**Response** (200 OK):
```json
{
  "available": true,
  "vendor_id": 123,
  "service_id": null,
  "start_datetime": "2025-01-15T10:00:00Z",
  "end_datetime": "2025-01-15T11:00:00Z",
  "available_slots": [
    {
      "id": 456,
      "date": "2025-01-15",
      "start_time": "10:00:00",
      "end_time": "11:00:00",
      "max_bookings": 2,
      "current_bookings": 0,
      "is_available": true
    }
  ],
  "conflicts": [],
  "alternative_suggestions": []
}
```

**Performance Target**: <50ms response time (critical for UX)

## Slot Generation

### Generate Slots from Patterns
```http
POST /vendor-availability/{availability_id}/generate-slots
```

**Description**: Generate availability slots from recurring patterns.

**Authorization**: Vendor role required (owner verification)

**Path Parameters**:
- `availability_id`: Vendor availability configuration ID

**Request Body**:
```json
{
  "start_date": "2025-01-15",
  "end_date": "2025-02-15",
  "auto_commit": true,
  "pattern_ids": [1, 2]
}
```

**Response** (200 OK):
```json
{
  "created_slots": 180,
  "skipped_slots": 5,
  "total_requested": 185,
  "created_slot_ids": [1, 2, 3, "..."],
  "errors": []
}
```

**Performance Target**: <200ms for 30-day periods

## Recurring Pattern Management

### Create Recurring Pattern
```http
POST /vendor-availability/{availability_id}/recurring-patterns
```

**Description**: Create recurring availability pattern.

**Authorization**: Vendor role required (owner verification)

**Request Body**:
```json
{
  "pattern_type": "weekly",
  "day_of_week": 1,
  "start_time": "10:00:00",
  "end_time": "16:00:00",
  "slot_duration_minutes": 60,
  "max_bookings_per_slot": 2,
  "valid_from": "2025-01-15",
  "valid_until": null,
  "auto_generate": true
}
```

**Response** (201 Created):
```json
{
  "id": 1,
  "vendor_availability_id": 1,
  "pattern_type": "weekly",
  "day_of_week": 1,
  "start_time": "10:00:00",
  "end_time": "16:00:00",
  "slot_duration_minutes": 60,
  "max_bookings_per_slot": 2,
  "valid_from": "2025-01-15",
  "valid_until": null,
  "auto_generate": true,
  "is_active": true
}
```

### List Recurring Patterns
```http
GET /vendor-availability/{availability_id}/recurring-patterns
```

**Description**: List all recurring patterns for vendor availability.

**Authorization**: Vendor role required (owner verification)

**Response** (200 OK):
```json
[
  {
    "id": 1,
    "vendor_availability_id": 1,
    "pattern_type": "weekly",
    "day_of_week": 1,
    "start_time": "10:00:00",
    "end_time": "16:00:00",
    "slot_duration_minutes": 60,
    "max_bookings_per_slot": 2,
    "valid_from": "2025-01-15",
    "valid_until": null,
    "auto_generate": true,
    "is_active": true
  }
]
```

## Availability Slot Management

### List Availability Slots
```http
GET /vendor-availability/{availability_id}/slots
```

**Description**: List availability slots with pagination and filtering.

**Authorization**: Vendor role required (owner verification)

**Query Parameters**:
- `start_date`: Filter start date (ISO format)
- `end_date`: Filter end date (ISO format)
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 50, max: 100)
- `is_available`: Filter by availability status

**Response** (200 OK):
```json
{
  "slots": [
    {
      "id": 1,
      "vendor_availability_id": 1,
      "date": "2025-01-15",
      "start_time": "10:00:00",
      "end_time": "11:00:00",
      "max_bookings": 2,
      "current_bookings": 0,
      "is_available": true
    }
  ],
  "total_count": 180,
  "page": 1,
  "per_page": 50,
  "total_pages": 4
}
```

### Create Individual Slot
```http
POST /vendor-availability/{availability_id}/slots
```

**Description**: Create individual availability slot.

**Authorization**: Vendor role required (owner verification)

**Request Body**:
```json
{
  "availability_date": "2025-01-15",
  "start_time": "10:00:00",
  "end_time": "11:00:00",
  "max_bookings": 2
}
```

**Response** (201 Created):
```json
{
  "id": 1,
  "vendor_availability_id": 1,
  "date": "2025-01-15",
  "start_time": "10:00:00",
  "end_time": "11:00:00",
  "max_bookings": 2,
  "current_bookings": 0,
  "is_available": true
}
```

## Exception Management

### Create Availability Exception
```http
POST /vendor-availability/{availability_id}/exceptions
```

**Description**: Create availability exception (override patterns).

**Authorization**: Vendor role required (owner verification)

**Request Body**:
```json
{
  "exception_date": "2025-01-20",
  "exception_type": "unavailable",
  "reason": "Vendor holiday",
  "recurring_availability_id": 1
}
```

**Response** (201 Created):
```json
{
  "id": 1,
  "vendor_availability_id": 1,
  "recurring_availability_id": 1,
  "exception_date": "2025-01-20",
  "exception_type": "unavailable",
  "reason": "Vendor holiday",
  "is_active": true
}
```

## Error Responses

### Standard Error Format
```json
{
  "detail": "Error message",
  "error_code": "AVAILABILITY_ERROR",
  "correlation_id": "req_123456789",
  "timestamp": "2025-01-14T10:00:00Z"
}
```

### Common Error Codes
- `VENDOR_NOT_FOUND`: Vendor not found
- `AVAILABILITY_CONFIG_NOT_FOUND`: Availability configuration not found
- `INVALID_TIME_RANGE`: Invalid time range specified
- `BOOKING_NOTICE_VIOLATION`: Minimum booking notice period violated
- `SLOT_CONFLICT`: Slot conflicts with existing bookings
- `PATTERN_VALIDATION_ERROR`: Recurring pattern validation failed
- `UNAUTHORIZED_ACCESS`: Insufficient permissions

## Rate Limiting

- **Availability Checking**: 100 requests/minute per user
- **Configuration Management**: 20 requests/minute per vendor
- **Slot Generation**: 5 requests/minute per vendor
- **Pattern Management**: 10 requests/minute per vendor

## Performance Specifications

- **Real-time Availability Checking**: <50ms response time
- **Vendor Configuration Retrieval**: <100ms response time
- **Slot Generation**: <200ms for 30-day periods
- **Pattern Management**: <100ms response time
- **Slot Listing**: <150ms with pagination

## Caching Strategy

- **Vendor Configurations**: 15-minute cache TTL
- **Availability Patterns**: 5-minute cache TTL
- **Real-time Checking**: No caching (always fresh)
- **Slot Lists**: 2-minute cache TTL

## Integration Points

- **Booking System**: Automatic slot reservation/release
- **Notification System**: Availability change notifications
- **Analytics**: Performance metrics and usage tracking
- **Monitoring**: Health checks and error tracking
