"""
Enhanced User and Authentication models for Culture Connect Backend API.

This module defines comprehensive user and authentication models including:
- User model with authentication and profile management
- TokenBlacklist model for secure logout functionality
- SecurityEvent model for audit logging and security monitoring
- Enhanced session tracking and API key management

Implements Task 2.1.1 requirements for JWT authentication with token blacklisting.
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Index, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit
from app.core.security import UserRole


class User(BaseModelWithAudit):
    """
    User model for authentication and profile management.

    This model stores user account information including authentication
    credentials, profile data, and role-based access control.
    """

    __tablename__ = "users"

    # Authentication fields
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="User email address (unique identifier)"
    )

    hashed_password = Column(
        String(255),
        nullable=False,
        doc="Hashed password for authentication"
    )

    # Profile fields
    first_name = Column(
        String(50),
        nullable=False,
        doc="User first name"
    )

    last_name = Column(
        String(50),
        nullable=False,
        doc="User last name"
    )

    phone_number = Column(
        String(20),
        nullable=True,
        index=True,
        doc="User phone number"
    )

    # Role and permissions
    role = Column(
        String(20),
        nullable=False,
        default=UserRole.CUSTOMER,
        index=True,
        doc="User role for access control"
    )

    # Account status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Account active status"
    )

    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Email verification status"
    )

    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Superuser status"
    )

    # Authentication tracking
    last_login = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last login timestamp"
    )

    failed_login_attempts = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of failed login attempts"
    )

    account_locked_until = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Account lock expiration timestamp"
    )

    # Password reset
    password_reset_token = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Password reset token"
    )

    password_reset_expires = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Password reset token expiration"
    )

    # Email verification
    email_verification_token = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Email verification token"
    )

    email_verification_expires = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Email verification token expiration"
    )

    # Profile information
    avatar_url = Column(
        String(500),
        nullable=True,
        doc="User avatar image URL"
    )

    bio = Column(
        Text,
        nullable=True,
        doc="User biography/description"
    )

    location = Column(
        String(100),
        nullable=True,
        doc="User location/city"
    )

    timezone = Column(
        String(50),
        nullable=True,
        default="UTC",
        doc="User timezone preference"
    )

    language = Column(
        String(10),
        nullable=True,
        default="en",
        doc="User language preference"
    )

    # Note: Vendor association is handled via Vendor.user_id relationship
    # No vendor_id needed here to avoid circular reference

    # Privacy settings
    profile_visibility = Column(
        String(20),
        default="public",
        nullable=False,
        doc="Profile visibility setting (public, private, friends)"
    )

    email_notifications = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Email notification preference"
    )

    sms_notifications = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="SMS notification preference"
    )

    marketing_emails = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Marketing email preference"
    )

    # Terms and privacy
    terms_accepted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Terms of service acceptance timestamp"
    )

    privacy_policy_accepted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Privacy policy acceptance timestamp"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="user",
        foreign_keys="Vendor.user_id",
        uselist=False
    )

    # Booking relationships (implemented in Task 4.1.1)
    customer_bookings = relationship(
        "Booking",
        back_populates="customer",
        foreign_keys="Booking.customer_id",
        cascade="all, delete-orphan",
        doc="Bookings made by this customer"
    )

    # Payment relationships (implemented in Phase 1 - Payment & Transaction Management System)
    payments = relationship(
        "Payment",
        back_populates="user",
        foreign_keys="Payment.user_id",
        cascade="all, delete-orphan",
        doc="Payments made by this user"
    )

    payment_methods = relationship(
        "PaymentMethod",
        back_populates="user",
        foreign_keys="PaymentMethod.user_id",
        cascade="all, delete-orphan",
        doc="Payment methods for this user"
    )

    # Review relationships (Task 4.4.1)
    customer_reviews = relationship(
        "Review",
        foreign_keys="Review.customer_id",
        back_populates="customer",
        cascade="all, delete-orphan",
        doc="Reviews written by this customer"
    )

    # Promotional relationships (Phase 5 - Promotional & Advertising System)
    created_campaigns = relationship(
        "Campaign",
        foreign_keys="Campaign.created_by",
        back_populates="created_by_user",
        doc="Campaigns created by this user"
    )

    approved_campaigns = relationship(
        "Campaign",
        foreign_keys="Campaign.approved_by",
        back_populates="approved_by_user",
        doc="Campaigns approved by this user"
    )

    created_advertisements = relationship(
        "Advertisement",
        foreign_keys="Advertisement.created_by",
        back_populates="created_by_user",
        doc="Advertisements created by this user"
    )

    approved_advertisements = relationship(
        "Advertisement",
        foreign_keys="Advertisement.approved_by",
        back_populates="approved_by_user",
        doc="Advertisements approved by this user"
    )

    api_keys = relationship(
        "APIKey",
        back_populates="user",
        foreign_keys="APIKey.user_id",
        cascade="all, delete-orphan"
    )

    sessions = relationship(
        "UserSession",
        back_populates="user",
        foreign_keys="UserSession.user_id",
        cascade="all, delete-orphan"
    )

    # Password security relationships (Task 2.1.2)
    password_history = relationship(
        "PasswordHistory",
        back_populates="user",
        foreign_keys="PasswordHistory.user_id",
        cascade="all, delete-orphan",
        order_by="PasswordHistory.created_at.desc()"
    )

    password_reset_tokens = relationship(
        "PasswordResetToken",
        back_populates="user",
        foreign_keys="PasswordResetToken.user_id",
        cascade="all, delete-orphan"
    )

    account_lockout = relationship(
        "AccountLockout",
        back_populates="user",
        foreign_keys="AccountLockout.user_id",
        uselist=False,
        cascade="all, delete-orphan"
    )

    # OAuth relationships (Task 2.1.3)
    oauth_accounts = relationship(
        "OAuthAccount",
        back_populates="user",
        foreign_keys="OAuthAccount.user_id",
        cascade="all, delete-orphan"
    )

    # Email relationships (Task 2.3.1)
    created_email_templates = relationship(
        "EmailTemplate",
        back_populates="created_by_user",
        foreign_keys="EmailTemplate.created_by"
    )

    email_deliveries = relationship(
        "EmailDelivery",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    email_preferences = relationship(
        "EmailPreference",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )

    email_queue_items = relationship(
        "EmailQueue",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    # RBAC relationships (Task 2.2.3)
    permission_grants = relationship(
        "PermissionGrant",
        foreign_keys="PermissionGrant.user_id",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    access_logs = relationship(
        "AccessControlLog",
        back_populates="user",
        foreign_keys="AccessControlLog.user_id",
        cascade="all, delete-orphan"
    )

    # Push notification relationships (Task 2.3.2)
    device_tokens = relationship(
        "DeviceToken",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    notification_deliveries = relationship(
        "NotificationDelivery",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    notification_preferences = relationship(
        "NotificationPreference",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )

    notification_queue_items = relationship(
        "NotificationQueue",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    # VPN Detection relationships (Phase 2.1)
    vpn_detections = relationship(
        "VPNDetectionResult",
        back_populates="user",
        foreign_keys="VPNDetectionResult.user_id",
        cascade="all, delete-orphan",
        doc="VPN detection results for this user"
    )

    proxy_detections = relationship(
        "ProxyDetectionResult",
        back_populates="user",
        foreign_keys="ProxyDetectionResult.user_id",
        cascade="all, delete-orphan",
        doc="Proxy detection results for this user"
    )

    anonymization_detections = relationship(
        "AnonymizationDetectionResult",
        back_populates="user",
        foreign_keys="AnonymizationDetectionResult.user_id",
        cascade="all, delete-orphan",
        doc="Anonymization detection results for this user"
    )

    # WebSocket relationships (Task 6.1.1)
    websocket_connections = relationship(
        "WebSocketConnection",
        back_populates="user",
        foreign_keys="WebSocketConnection.user_id",
        cascade="all, delete-orphan",
        doc="WebSocket connections for this user"
    )

    presence = relationship(
        "UserPresence",
        back_populates="user",
        foreign_keys="UserPresence.user_id",
        uselist=False,
        cascade="all, delete-orphan",
        doc="User presence status"
    )

    # Analytics relationships (Task 7.1.1)
    analytics = relationship(
        "UserAnalytics",
        back_populates="user",
        foreign_keys="UserAnalytics.user_id",
        cascade="all, delete-orphan",
        doc="User analytics data"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_user_email_active", "email", "is_active"),
        Index("idx_user_role_active", "role", "is_active"),
        Index("idx_user_last_login", "last_login"),
        Index("idx_user_created_role", "created_at", "role"),
    )

    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(id={self.id}, email='{self.email}', role='{self.role}')>"

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]

    @property
    def is_vendor(self) -> bool:
        """Check if user is a vendor."""
        return self.role == UserRole.VENDOR

    @property
    def is_customer(self) -> bool:
        """Check if user is a customer."""
        return self.role == UserRole.CUSTOMER

    @property
    def is_account_locked(self) -> bool:
        """Check if account is currently locked."""
        if self.account_locked_until is None:
            return False
        return datetime.utcnow() < self.account_locked_until

    def lock_account(self, duration_minutes: int = 30):
        """
        Lock user account for specified duration.

        Args:
            duration_minutes: Lock duration in minutes
        """
        from datetime import timedelta
        self.account_locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0

    def unlock_account(self):
        """Unlock user account."""
        self.account_locked_until = None
        self.failed_login_attempts = 0

    def increment_failed_login(self):
        """Increment failed login attempts."""
        self.failed_login_attempts += 1

        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.lock_account(30)  # Lock for 30 minutes

    def reset_failed_login(self):
        """Reset failed login attempts."""
        self.failed_login_attempts = 0

    def update_last_login(self):
        """Update last login timestamp."""
        self.last_login = datetime.utcnow()
        self.reset_failed_login()

    def can_access_vendor_features(self) -> bool:
        """Check if user can access vendor features."""
        return self.role in [UserRole.VENDOR, UserRole.ADMIN, UserRole.SUPER_ADMIN]

    def can_access_admin_features(self) -> bool:
        """Check if user can access admin features."""
        return self.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]

    def has_vendor_association(self) -> bool:
        """Check if user has vendor association."""
        # return self.vendor_id is not None  # Will be implemented in Phase 2
        return False

    def to_dict(self, include_sensitive: bool = False) -> dict:
        """
        Convert user to dictionary.

        Args:
            include_sensitive: Whether to include sensitive fields

        Returns:
            dict: User data dictionary
        """
        data = super().to_dict(exclude=["hashed_password", "password_reset_token",
                                       "email_verification_token"])

        # Add computed properties
        data["full_name"] = self.full_name
        data["is_admin"] = self.is_admin
        data["is_vendor"] = self.is_vendor
        data["is_customer"] = self.is_customer
        data["is_account_locked"] = self.is_account_locked

        if include_sensitive:
            data["failed_login_attempts"] = self.failed_login_attempts
            data["account_locked_until"] = self.account_locked_until

        return data


class APIKey(BaseModelWithAudit):
    """
    API key model for programmatic access.

    This model stores API keys for users to access the API programmatically.
    """

    __tablename__ = "api_keys"

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated user ID"
    )

    name = Column(
        String(100),
        nullable=False,
        doc="API key name/description"
    )

    key_hash = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Hashed API key"
    )

    scopes = Column(
        Text,
        nullable=True,
        doc="JSON array of API scopes"
    )

    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="API key active status"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="API key expiration timestamp"
    )

    last_used = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last usage timestamp"
    )

    usage_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times key has been used"
    )

    # Relationships
    user = relationship("User", back_populates="api_keys", foreign_keys=[user_id])

    def __repr__(self) -> str:
        """String representation of the API key."""
        return f"<APIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"

    @property
    def is_expired(self) -> bool:
        """Check if API key is expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    def update_usage(self):
        """Update API key usage statistics."""
        self.last_used = datetime.utcnow()
        self.usage_count += 1


class UserSession(BaseModelWithAudit):
    """
    User session model for tracking active sessions.

    This model stores user session information for security and monitoring.
    """

    __tablename__ = "user_sessions"

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated user ID"
    )

    session_id = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique session identifier"
    )

    ip_address = Column(
        String(45),
        nullable=True,
        doc="Client IP address"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="Client user agent string"
    )

    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Session active status"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        doc="Session expiration timestamp"
    )

    last_activity = Column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        doc="Last activity timestamp"
    )

    # Relationships
    user = relationship("User", back_populates="sessions", foreign_keys=[user_id])

    def __repr__(self) -> str:
        """String representation of the session."""
        return f"<UserSession(id={self.id}, user_id={self.user_id}, session_id='{self.session_id}')>"

    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at

    def update_activity(self):
        """Update session activity timestamp."""
        self.last_activity = datetime.utcnow()

    def deactivate(self):
        """Deactivate the session."""
        self.is_active = False


class TokenBlacklist(BaseModelWithAudit):
    """
    Token blacklist model for secure logout functionality.

    This model stores blacklisted JWT tokens to prevent their reuse after logout.
    Implements Task 2.1.1 requirement for token blacklisting.
    """

    __tablename__ = "token_blacklist"

    jti = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="JWT ID (jti claim) of the blacklisted token"
    )

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owned the token"
    )

    token_type = Column(
        String(20),
        nullable=False,
        doc="Type of token (access or refresh)"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Original token expiration time"
    )

    blacklisted_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        doc="When the token was blacklisted"
    )

    reason = Column(
        String(100),
        nullable=True,
        doc="Reason for blacklisting (logout, security, etc.)"
    )

    ip_address = Column(
        String(45),
        nullable=True,
        doc="IP address from which logout was initiated"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent from which logout was initiated"
    )

    # Relationships
    user = relationship("User", backref="blacklisted_tokens", foreign_keys=[user_id])

    # Indexes for performance
    __table_args__ = (
        Index('idx_token_blacklist_jti', 'jti'),
        Index('idx_token_blacklist_user_id', 'user_id'),
        Index('idx_token_blacklist_expires_at', 'expires_at'),
        Index('idx_token_blacklist_user_type', 'user_id', 'token_type'),
    )

    def __repr__(self) -> str:
        """String representation of the blacklisted token."""
        return f"<TokenBlacklist(jti='{self.jti}', user_id={self.user_id}, type='{self.token_type}')>"

    @property
    def is_expired(self) -> bool:
        """Check if the original token would have expired naturally."""
        return datetime.utcnow() > self.expires_at

    @classmethod
    def cleanup_expired(cls, db_session):
        """Remove expired blacklisted tokens to save space."""
        expired_tokens = db_session.query(cls).filter(
            cls.expires_at < datetime.utcnow()
        ).all()

        for token in expired_tokens:
            db_session.delete(token)

        return len(expired_tokens)


class SecurityEvent(BaseModelWithAudit):
    """
    Security event model for audit logging and security monitoring.

    This model tracks security-related events for compliance and monitoring.
    Implements Task 2.1.1 requirement for security event logging.
    """

    __tablename__ = "security_events"

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User associated with the event (nullable for system events)"
    )

    event_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of security event"
    )

    event_category = Column(
        String(30),
        nullable=False,
        index=True,
        doc="Category of event (authentication, authorization, etc.)"
    )

    severity = Column(
        String(20),
        nullable=False,
        default="info",
        doc="Event severity level (info, warning, error, critical)"
    )

    description = Column(
        Text,
        nullable=False,
        doc="Human-readable description of the event"
    )

    details = Column(
        JSON,
        nullable=True,
        doc="Additional event details in JSON format"
    )

    ip_address = Column(
        String(45),
        nullable=True,
        index=True,
        doc="IP address associated with the event"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string associated with the event"
    )

    session_id = Column(
        String(255),
        nullable=True,
        doc="Session ID associated with the event"
    )

    correlation_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Correlation ID for tracking related events"
    )

    resolved = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the security event has been resolved"
    )

    resolved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the event was resolved"
    )

    resolved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who resolved the event"
    )

    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="security_events")
    resolver = relationship("User", foreign_keys=[resolved_by])

    # Indexes for performance and querying
    __table_args__ = (
        Index('idx_security_events_user_id', 'user_id'),
        Index('idx_security_events_type', 'event_type'),
        Index('idx_security_events_category', 'event_category'),
        Index('idx_security_events_severity', 'severity'),
        Index('idx_security_events_ip', 'ip_address'),
        Index('idx_security_events_correlation', 'correlation_id'),
        Index('idx_security_events_created', 'created_at'),
        Index('idx_security_events_user_type', 'user_id', 'event_type'),
        Index('idx_security_events_unresolved', 'resolved', 'severity'),
    )

    def __repr__(self) -> str:
        """String representation of the security event."""
        return f"<SecurityEvent(type='{self.event_type}', user_id={self.user_id}, severity='{self.severity}')>"

    def resolve(self, resolved_by_user_id: int):
        """Mark the security event as resolved."""
        self.resolved = True
        self.resolved_at = datetime.utcnow()
        self.resolved_by = resolved_by_user_id

    @classmethod
    def log_event(
        cls,
        db_session,
        event_type: str,
        description: str,
        user_id: Optional[int] = None,
        event_category: str = "authentication",
        severity: str = "info",
        details: Optional[dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        """
        Log a security event.

        Args:
            db_session: Database session
            event_type: Type of event
            description: Event description
            user_id: Associated user ID
            event_category: Event category
            severity: Event severity
            details: Additional details
            ip_address: Client IP address
            user_agent: Client user agent
            session_id: Session ID
            correlation_id: Correlation ID

        Returns:
            SecurityEvent: Created security event
        """
        event = cls(
            user_id=user_id,
            event_type=event_type,
            event_category=event_category,
            severity=severity,
            description=description,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            correlation_id=correlation_id
        )

        db_session.add(event)
        return event
