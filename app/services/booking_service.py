"""
Booking Service for Culture Connect Backend API.

This module provides comprehensive booking workflow management including:
- Complete booking lifecycle management (creation, validation, status workflow)
- Real-time availability checking and conflict prevention
- Vendor approval system with automated notifications
- Customer notification integration with existing email/push services
- Booking communication system for vendor-customer messaging
- Modification request workflows and approval management

Implements Task 4.1.1 requirements for booking workflow implementation with:
- Production-grade business logic following monolithic FastAPI architecture
- Seamless integration with existing authentication, RBAC, and communication services
- Comprehensive error handling with structured logging and correlation IDs
- >80% test coverage readiness with comprehensive validation

Production-grade implementation following established BaseService patterns.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ValidationError, NotFoundError, ConflictError, ServiceError
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.models.booking import (
    Booking, BookingStatus, VendorResponseType, BookingPriority,
    CommunicationType, ModificationType
)
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service, ServiceAvailability
from app.repositories.booking_repository import BookingRepository
# Note: BookingCommunicationRepository and BookingModificationRepository
# have been replaced by the new Task 4.1.3 service classes:
# BookingMessageService, MessageAttachmentService, etc.
from app.repositories.auth_repository import UserRepository
from app.repositories.vendor_repository import VendorRepository
from app.schemas.booking_schemas import (
    BookingCreateSchema, BookingUpdateSchema, BookingStatusUpdateSchema,
    VendorResponseSchema, BookingResponseSchema
)
from app.core.logging import correlation_id
from app.core.config import settings

logger = logging.getLogger(__name__)


class BookingService(BaseService[Booking, BookingRepository]):
    """
    Comprehensive booking service for workflow management.

    Provides complete booking lifecycle management including:
    - Booking creation with availability validation
    - Vendor approval workflow automation
    - Customer notification integration
    - Status management and audit tracking
    - Communication and modification workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize booking service with dependencies."""
        super().__init__(
            repository_class=BookingRepository,
            model_class=Booking,
            db_session=db
        )

        # Initialize dependent services
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)

        # Initialize repositories
        self.user_repository = UserRepository(db)
        self.vendor_repository = VendorRepository(db)
        # Note: Communication and modification functionality now handled by
        # BookingMessageService and related services from Task 4.1.3

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate booking creation data.

        Args:
            data: Booking creation data

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        try:
            # Validate required fields
            required_fields = ['customer_id', 'vendor_id', 'service_id', 'booking_date']
            for field in required_fields:
                if field not in data:
                    raise ValidationError(f"Missing required field: {field}")

            # Validate customer exists
            customer = await self.user_repository.get(data['customer_id'])
            if not customer:
                raise ValidationError("Customer not found")

            # Validate vendor exists and is active
            vendor = await self.vendor_repository.get(data['vendor_id'])
            if not vendor:
                raise ValidationError("Vendor not found")
            if not vendor.is_active:
                raise ValidationError("Vendor is not active")

            # Validate service exists and belongs to vendor
            from app.repositories.service_repository import ServiceRepository
            service_repo = ServiceRepository(self._db_session)
            service = await service_repo.get(data['service_id'])
            if not service:
                raise ValidationError("Service not found")
            if service.vendor_id != data['vendor_id']:
                raise ValidationError("Service does not belong to the specified vendor")

            # Validate booking date is not in the past
            booking_date = data['booking_date']
            if isinstance(booking_date, str):
                booking_date = datetime.strptime(booking_date, '%Y-%m-%d').date()
            if booking_date < date.today():
                raise ValidationError("Booking date cannot be in the past")

            # Calculate pricing
            pricing_data = await self._calculate_booking_pricing(
                service,
                data.get('participant_count', 1),
                data.get('duration_hours'),
                data.get('special_requirements')
            )
            data.update(pricing_data)

            # Set vendor response deadline (24 hours from creation)
            data['vendor_response_deadline'] = datetime.utcnow() + timedelta(hours=24)

            return data

        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logger.error(f"Validation error in booking creation: {str(e)}")
            raise ValidationError(f"Booking validation failed: {str(e)}")

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate booking update data.

        Args:
            data: Update data
            existing_id: Existing booking ID

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        try:
            # Get existing booking
            existing_booking = await self.get(existing_id)
            if not existing_booking:
                raise ValidationError("Booking not found")

            # Check if booking can be modified
            if not existing_booking.can_be_modified:
                raise ValidationError("Booking cannot be modified in current status")

            # Validate booking date if provided
            if 'booking_date' in data:
                booking_date = data['booking_date']
                if isinstance(booking_date, str):
                    booking_date = datetime.strptime(booking_date, '%Y-%m-%d').date()
                if booking_date < date.today():
                    raise ValidationError("Booking date cannot be in the past")

            # Recalculate pricing if relevant fields changed
            pricing_fields = ['participant_count', 'duration_hours', 'special_requirements']
            if any(field in data for field in pricing_fields):
                from app.repositories.service_repository import ServiceRepository
                service_repo = ServiceRepository(self._db_session)
                service = await service_repo.get(existing_booking.service_id)

                pricing_data = await self._calculate_booking_pricing(
                    service,
                    data.get('participant_count', existing_booking.participant_count),
                    data.get('duration_hours', existing_booking.duration_hours),
                    data.get('special_requirements', existing_booking.special_requirements)
                )
                data.update(pricing_data)

            return data

        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logger.error(f"Validation error in booking update: {str(e)}")
            raise ValidationError(f"Booking update validation failed: {str(e)}")

    async def create_booking(
        self,
        customer_id: int,
        booking_data: BookingCreateSchema,
        check_availability: bool = True
    ) -> BookingResponseSchema:
        """
        Create a new booking with comprehensive validation.

        Args:
            customer_id: Customer ID creating the booking
            booking_data: Booking creation data
            check_availability: Whether to check availability conflicts

        Returns:
            BookingResponseSchema: Created booking data

        Raises:
            ValidationError: If validation fails
            ConflictError: If availability conflict exists
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start("create_booking", customer_id=customer_id)

        try:
            # Prepare booking data
            booking_dict = booking_data.model_dump()
            booking_dict['customer_id'] = customer_id

            # Check availability conflicts if requested
            if check_availability:
                conflict_exists = await self.repository.check_availability_conflict(
                    service_id=booking_dict['service_id'],
                    booking_date=booking_dict['booking_date'],
                    booking_time=booking_dict.get('booking_time'),
                    duration_hours=booking_dict.get('duration_hours')
                )
                if conflict_exists:
                    raise ConflictError("Booking conflicts with existing reservation")

            # Create booking
            async with self.get_transaction_context() as session:
                repository = BookingRepository(session)
                booking = await repository.create_booking(
                    customer_id=customer_id,
                    vendor_id=booking_dict['vendor_id'],
                    service_id=booking_dict['service_id'],
                    booking_data=booking_dict
                )

                # Send notifications
                await self._send_booking_created_notifications(booking)

                self.log_operation_success(
                    correlation,
                    f"Created booking {booking.booking_reference}"
                )

                return BookingResponseSchema.model_validate(booking)

        except Exception as e:
            await self.handle_service_error(e, "create_booking", {"customer_id": customer_id})

    async def update_booking_status(
        self,
        booking_id: int,
        status_update: BookingStatusUpdateSchema,
        updated_by: int
    ) -> Optional[BookingResponseSchema]:
        """
        Update booking status with notifications.

        Args:
            booking_id: Booking ID
            status_update: Status update data
            updated_by: User making the update

        Returns:
            Optional[BookingResponseSchema]: Updated booking if found

        Raises:
            NotFoundError: If booking not found
            ValidationError: If status transition invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("update_booking_status", booking_id=booking_id)

        try:
            async with self.get_transaction_context() as session:
                repository = BookingRepository(session)

                # Validate status transition
                booking = await repository.get(booking_id)
                if not booking:
                    raise NotFoundError("Booking not found")

                await self._validate_status_transition(booking.status, status_update.status)

                # Update status
                updated_booking = await repository.update_booking_status(
                    booking_id=booking_id,
                    new_status=status_update.status,
                    changed_by=updated_by,
                    change_reason=status_update.change_reason,
                    change_notes=status_update.change_notes
                )

                # Send notifications if requested
                if status_update.notify_customer or status_update.notify_vendor:
                    await self._send_status_update_notifications(
                        updated_booking,
                        status_update.notify_customer,
                        status_update.notify_vendor
                    )

                self.log_operation_success(
                    correlation,
                    f"Updated booking {booking_id} status to {status_update.status}"
                )

                return BookingResponseSchema.model_validate(updated_booking)

        except Exception as e:
            await self.handle_service_error(e, "update_booking_status", {"booking_id": booking_id})

    async def process_vendor_response(
        self,
        booking_id: int,
        vendor_id: int,
        response_data: VendorResponseSchema
    ) -> Optional[BookingResponseSchema]:
        """
        Process vendor response to booking request.

        Args:
            booking_id: Booking ID
            vendor_id: Vendor ID responding
            response_data: Vendor response data

        Returns:
            Optional[BookingResponseSchema]: Updated booking if found

        Raises:
            NotFoundError: If booking not found
            ValidationError: If vendor not authorized
            ServiceError: If processing fails
        """
        correlation = self.log_operation_start("process_vendor_response", booking_id=booking_id)

        try:
            async with self.get_transaction_context() as session:
                repository = BookingRepository(session)

                # Update vendor response
                updated_booking = await repository.update_vendor_response(
                    booking_id=booking_id,
                    response_type=response_data.response_type,
                    vendor_notes=response_data.vendor_notes,
                    vendor_id=vendor_id
                )

                if not updated_booking:
                    raise NotFoundError("Booking not found or vendor not authorized")

                # Send customer notification
                await self._send_vendor_response_notifications(updated_booking, response_data)

                self.log_operation_success(
                    correlation,
                    f"Processed vendor response for booking {booking_id}: {response_data.response_type}"
                )

                return BookingResponseSchema.model_validate(updated_booking)

        except Exception as e:
            await self.handle_service_error(e, "process_vendor_response", {"booking_id": booking_id})

    async def check_availability(
        self,
        service_id: int,
        booking_date: date,
        booking_time: Optional[datetime] = None,
        duration_hours: Optional[Decimal] = None,
        exclude_booking_id: Optional[int] = None
    ) -> bool:
        """
        Check service availability for booking.

        Args:
            service_id: Service ID
            booking_date: Booking date
            booking_time: Optional booking time
            duration_hours: Optional duration
            exclude_booking_id: Optional booking to exclude

        Returns:
            bool: True if available, False if conflict exists

        Raises:
            ServiceError: If check fails
        """
        try:
            return not await self.repository.check_availability_conflict(
                service_id=service_id,
                booking_date=booking_date,
                booking_time=booking_time,
                duration_hours=duration_hours,
                exclude_booking_id=exclude_booking_id
            )

        except Exception as e:
            await self.handle_service_error(e, "check_availability", {"service_id": service_id})

    async def get_customer_bookings(
        self,
        customer_id: int,
        status_filter: Optional[List[BookingStatus]] = None,
        page: int = 1,
        per_page: int = 20
    ) -> Dict[str, Any]:
        """
        Get bookings for a customer with pagination.

        Args:
            customer_id: Customer ID
            status_filter: Optional status filter
            page: Page number
            per_page: Items per page

        Returns:
            Dict[str, Any]: Paginated booking results

        Raises:
            ServiceError: If retrieval fails
        """
        try:
            from app.repositories.base import PaginationParams

            pagination = PaginationParams(page=page, per_page=per_page)
            result = await self.repository.get_bookings_by_customer(
                customer_id=customer_id,
                status_filter=status_filter,
                pagination=pagination
            )

            return {
                "bookings": [BookingResponseSchema.model_validate(booking) for booking in result.items],
                "total": result.total,
                "page": result.page,
                "per_page": result.per_page,
                "pages": result.pages,
                "has_next": result.has_next,
                "has_prev": result.has_prev
            }

        except Exception as e:
            await self.handle_service_error(e, "get_customer_bookings", {"customer_id": customer_id})

    async def get_vendor_bookings(
        self,
        vendor_id: int,
        status_filter: Optional[List[BookingStatus]] = None,
        date_range: Optional[Tuple[date, date]] = None,
        page: int = 1,
        per_page: int = 20
    ) -> Dict[str, Any]:
        """
        Get bookings for a vendor with pagination.

        Args:
            vendor_id: Vendor ID
            status_filter: Optional status filter
            date_range: Optional date range filter
            page: Page number
            per_page: Items per page

        Returns:
            Dict[str, Any]: Paginated booking results

        Raises:
            ServiceError: If retrieval fails
        """
        try:
            from app.repositories.base import PaginationParams

            pagination = PaginationParams(page=page, per_page=per_page)
            result = await self.repository.get_bookings_by_vendor(
                vendor_id=vendor_id,
                status_filter=status_filter,
                date_range=date_range,
                pagination=pagination
            )

            return {
                "bookings": [BookingResponseSchema.model_validate(booking) for booking in result.items],
                "total": result.total,
                "page": result.page,
                "per_page": result.per_page,
                "pages": result.pages,
                "has_next": result.has_next,
                "has_prev": result.has_prev
            }

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_bookings", {"vendor_id": vendor_id})

    async def get_booking_analytics(
        self,
        vendor_id: Optional[int] = None,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get booking analytics data.

        Args:
            vendor_id: Optional vendor ID filter
            date_range: Optional date range filter

        Returns:
            Dict[str, Any]: Analytics data

        Raises:
            ServiceError: If retrieval fails
        """
        try:
            return await self.repository.get_booking_analytics(
                vendor_id=vendor_id,
                date_range=date_range
            )

        except Exception as e:
            await self.handle_service_error(e, "get_booking_analytics", {"vendor_id": vendor_id})

    # Helper methods
    async def _calculate_booking_pricing(
        self,
        service: Service,
        participant_count: int,
        duration_hours: Optional[Decimal] = None,
        special_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """Calculate booking pricing based on service and requirements."""
        try:
            base_price = service.base_price

            # Apply participant count multiplier if applicable
            if service.pricing_model == "per_person":
                base_price = base_price * participant_count

            # Apply duration multiplier if applicable
            if duration_hours and service.pricing_model == "per_hour":
                base_price = base_price * duration_hours

            # Calculate additional fees (placeholder logic)
            additional_fees = Decimal("0.00")
            if special_requirements:
                additional_fees = base_price * Decimal("0.1")  # 10% for special requirements

            # Calculate totals
            subtotal = base_price + additional_fees
            commission_rate = Decimal("0.15")  # 15% platform commission
            commission_amount = subtotal * commission_rate
            total_amount = subtotal
            vendor_payout = subtotal - commission_amount

            return {
                "base_price": base_price,
                "additional_fees": additional_fees,
                "discount_amount": Decimal("0.00"),
                "total_amount": total_amount,
                "commission_rate": commission_rate,
                "commission_amount": commission_amount,
                "vendor_payout": vendor_payout,
                "currency": "NGN"
            }

        except Exception as e:
            logger.error(f"Error calculating booking pricing: {str(e)}")
            raise ValidationError("Failed to calculate booking pricing")

    async def _validate_status_transition(
        self,
        current_status: BookingStatus,
        new_status: BookingStatus
    ) -> None:
        """Validate booking status transition."""
        # Define valid status transitions
        valid_transitions = {
            BookingStatus.PENDING: [
                BookingStatus.VENDOR_REVIEW, BookingStatus.CONFIRMED,
                BookingStatus.REJECTED, BookingStatus.CANCELLED_BY_CUSTOMER
            ],
            BookingStatus.VENDOR_REVIEW: [
                BookingStatus.CONFIRMED, BookingStatus.REJECTED,
                BookingStatus.CANCELLED_BY_CUSTOMER, BookingStatus.CANCELLED_BY_VENDOR
            ],
            BookingStatus.CONFIRMED: [
                BookingStatus.PAYMENT_PENDING, BookingStatus.PAID,
                BookingStatus.CANCELLED_BY_CUSTOMER, BookingStatus.CANCELLED_BY_VENDOR
            ],
            BookingStatus.PAYMENT_PENDING: [
                BookingStatus.PAID, BookingStatus.CANCELLED_BY_CUSTOMER,
                BookingStatus.CANCELLED_BY_VENDOR
            ],
            BookingStatus.PAID: [
                BookingStatus.IN_PROGRESS, BookingStatus.NO_SHOW,
                BookingStatus.CANCELLED_BY_CUSTOMER, BookingStatus.CANCELLED_BY_VENDOR
            ],
            BookingStatus.IN_PROGRESS: [
                BookingStatus.COMPLETED, BookingStatus.DISPUTED
            ],
            BookingStatus.COMPLETED: [
                BookingStatus.DISPUTED
            ]
        }

        allowed_statuses = valid_transitions.get(current_status, [])
        if new_status not in allowed_statuses:
            raise ValidationError(
                f"Invalid status transition from {current_status} to {new_status}"
            )

    async def _send_booking_created_notifications(self, booking: Booking) -> None:
        """Send notifications when booking is created."""
        try:
            # Send email to customer
            await self.email_service.send_booking_confirmation_email(
                user_id=booking.customer_id,
                booking_reference=booking.booking_reference,
                booking_details={
                    "service_name": booking.service.name if booking.service else "Service",
                    "booking_date": booking.booking_date.isoformat(),
                    "total_amount": str(booking.total_amount)
                }
            )

            # Send notification to vendor
            await self.email_service.send_booking_notification_email(
                user_id=booking.vendor.user_id if booking.vendor else None,
                booking_reference=booking.booking_reference,
                booking_details={
                    "customer_name": f"{booking.customer.first_name} {booking.customer.last_name}",
                    "service_name": booking.service.name if booking.service else "Service",
                    "booking_date": booking.booking_date.isoformat()
                }
            )

            # Send push notifications
            await self.push_service.send_notification_to_user(
                user_id=booking.vendor.user_id if booking.vendor else None,
                title="New Booking Request",
                body=f"You have a new booking request for {booking.booking_date}",
                payload={"booking_id": booking.id, "type": "new_booking"}
            )

        except Exception as e:
            logger.error(f"Failed to send booking created notifications: {str(e)}")

    async def _send_status_update_notifications(
        self,
        booking: Booking,
        notify_customer: bool,
        notify_vendor: bool
    ) -> None:
        """Send status update notifications."""
        try:
            if notify_customer:
                await self.email_service.send_booking_status_update_email(
                    user_id=booking.customer_id,
                    booking_reference=booking.booking_reference,
                    new_status=booking.status.value,
                    status_message=self._get_status_message(booking.status)
                )

            if notify_vendor:
                await self.email_service.send_booking_status_update_email(
                    user_id=booking.vendor.user_id if booking.vendor else None,
                    booking_reference=booking.booking_reference,
                    new_status=booking.status.value,
                    status_message=self._get_status_message(booking.status)
                )

        except Exception as e:
            logger.error(f"Failed to send status update notifications: {str(e)}")

    async def _send_vendor_response_notifications(
        self,
        booking: Booking,
        response_data: VendorResponseSchema
    ) -> None:
        """Send vendor response notifications to customer."""
        try:
            await self.email_service.send_vendor_response_email(
                user_id=booking.customer_id,
                booking_reference=booking.booking_reference,
                response_type=response_data.response_type.value,
                vendor_notes=response_data.vendor_notes
            )

            # Send push notification
            await self.push_service.send_notification_to_user(
                user_id=booking.customer_id,
                title="Booking Update",
                body=f"Your booking request has been {response_data.response_type.value}",
                payload={"booking_id": booking.id, "type": "vendor_response"}
            )

        except Exception as e:
            logger.error(f"Failed to send vendor response notifications: {str(e)}")

    def _get_status_message(self, status: BookingStatus) -> str:
        """Get user-friendly status message."""
        status_messages = {
            BookingStatus.PENDING: "Your booking request is being processed",
            BookingStatus.VENDOR_REVIEW: "Your booking is under vendor review",
            BookingStatus.CONFIRMED: "Your booking has been confirmed",
            BookingStatus.PAYMENT_PENDING: "Payment is required to complete your booking",
            BookingStatus.PAID: "Payment received, booking confirmed",
            BookingStatus.IN_PROGRESS: "Your service is currently in progress",
            BookingStatus.COMPLETED: "Your booking has been completed",
            BookingStatus.CANCELLED_BY_CUSTOMER: "Booking cancelled by customer",
            BookingStatus.CANCELLED_BY_VENDOR: "Booking cancelled by vendor",
            BookingStatus.REJECTED: "Booking request was rejected",
            BookingStatus.REFUNDED: "Booking refund has been processed",
            BookingStatus.DISPUTED: "Booking is under dispute review",
            BookingStatus.NO_SHOW: "Customer did not show up for the service"
        }
        return status_messages.get(status, "Booking status updated")
