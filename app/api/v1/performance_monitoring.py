"""
Performance Monitoring API endpoints for Culture Connect Backend API.

This module provides comprehensive performance monitoring API endpoints including:
- Performance metrics recording and retrieval
- System health monitoring and alerting
- Load testing configuration and execution
- Performance insights and recommendations
- Real-time monitoring dashboards

Implements Phase 7.2.3 requirements for performance monitoring API with:
- FastAPI router patterns with dependency injection
- Authentication and authorization using established patterns
- Performance targets: <200ms GET, <500ms POST/PUT operations
- Comprehensive error handling with correlation IDs
- Integration with PerformanceMonitoringService and SystemHealthService

Production-grade implementation following Culture Connect Backend architecture.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from fastapi.responses import JSONResponse

from app.core.deps import (
    get_current_user,
    require_permission_dependency,
    get_correlation_id
)
from app.models.user import User
from app.models.analytics_models import PerformanceMetricType, AnalyticsTimeframe, SystemHealthStatus
from app.schemas.performance_schemas import (
    PerformanceMetricsCreate,
    PerformanceMetricsResponse,
    SystemHealthBase as SystemHealth<PERSON><PERSON>,
    SystemHealthResponse,
    LoadTestConfiguration as LoadTestConfigCreate,
    LoadTestResult as LoadTestResultsResponse
)
from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.services.system_health_service import SystemHealthService
from app.services.load_testing_service import LoadTestingService
from app.core.logging import correlation_id

# Initialize router
router = APIRouter(prefix="/performance", tags=["performance-monitoring"])
logger = logging.getLogger(__name__)

# Initialize services
performance_service = PerformanceMonitoringService()
health_service = SystemHealthService()
load_testing_service = LoadTestingService(performance_service)


@router.post(
    "/metrics",
    response_model=PerformanceMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record Performance Metric",
    description="Record a new performance metric with APM integration"
)
async def record_performance_metric(
    metric_data: PerformanceMetricsCreate,
    current_user: User = Depends(require_permission_dependency("performance_monitoring_create")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> PerformanceMetricsResponse:
    """
    Record a new performance metric with validation and APM integration.

    Performance Metrics:
    - Target response time: <500ms for metric creation
    - Validation: <100ms using optimized patterns
    - Database insertion: <300ms with bulk optimization support
    """
    try:
        correlation_id.set(correlation_id_val)

        # Record performance metric
        metric = await performance_service.record_performance_metric(
            metric_type=metric_data.metric_type,
            metric_name=metric_data.metric_name,
            component=metric_data.component,
            value=metric_data.value,
            timeframe=metric_data.timeframe,
            tags=metric_data.tags,
            metadata=metric_data.metadata
        )

        logger.info(
            f"Performance metric recorded via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "metric_id": metric.id,
                "metric_type": metric_data.metric_type.value,
                "component": metric_data.component
            }
        )

        return PerformanceMetricsResponse.model_validate(metric)

    except Exception as e:
        logger.error(
            f"Failed to record performance metric via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to record performance metric: {str(e)}"
        )


@router.get(
    "/metrics",
    response_model=List[PerformanceMetricsResponse],
    summary="Get Performance Metrics",
    description="Retrieve performance metrics with filtering and optimization"
)
async def get_performance_metrics(
    component: Optional[str] = Query(None, description="Filter by component"),
    metric_type: Optional[PerformanceMetricType] = Query(None, description="Filter by metric type"),
    start_time: Optional[datetime] = Query(None, description="Start time filter"),
    end_time: Optional[datetime] = Query(None, description="End time filter"),
    timeframe: Optional[AnalyticsTimeframe] = Query(None, description="Timeframe filter"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: User = Depends(require_permission_dependency("performance_monitoring_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> List[PerformanceMetricsResponse]:
    """
    Retrieve performance metrics with filtering and optimization.

    Performance Metrics:
    - Target response time: <200ms for metric queries
    - Cache optimization: >90% cache hit rate for frequently accessed metrics
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get performance metrics
        metrics = await performance_service.get_performance_metrics(
            component=component,
            metric_type=metric_type,
            start_time=start_time,
            end_time=end_time,
            timeframe=timeframe,
            limit=limit
        )

        logger.info(
            f"Performance metrics retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "component": component,
                "result_count": len(metrics)
            }
        )

        return [PerformanceMetricsResponse.model_validate(metric) for metric in metrics]

    except Exception as e:
        logger.error(
            f"Failed to get performance metrics via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve performance metrics: {str(e)}"
        )


@router.get(
    "/metrics/aggregated",
    response_model=Dict[str, Any],
    summary="Get Aggregated Performance Metrics",
    description="Get aggregated performance metrics with statistical analysis"
)
async def get_aggregated_performance_metrics(
    component: Optional[str] = Query(None, description="Filter by component"),
    metric_type: Optional[PerformanceMetricType] = Query(None, description="Filter by metric type"),
    timeframe: AnalyticsTimeframe = Query(AnalyticsTimeframe.HOURLY, description="Time aggregation period"),
    start_time: Optional[datetime] = Query(None, description="Start time filter"),
    end_time: Optional[datetime] = Query(None, description="End time filter"),
    current_user: User = Depends(require_permission_dependency("performance_monitoring_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> Dict[str, Any]:
    """
    Get aggregated performance metrics with statistical analysis.

    Performance Metrics:
    - Target response time: <200ms for aggregation queries
    - Cache optimization: 30-minute TTL for aggregated data
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get aggregated metrics
        aggregated_data = await performance_service.get_aggregated_performance_metrics(
            component=component,
            metric_type=metric_type,
            timeframe=timeframe,
            start_time=start_time,
            end_time=end_time
        )

        logger.info(
            f"Aggregated performance metrics retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "component": component,
                "timeframe": timeframe.value
            }
        )

        return aggregated_data

    except Exception as e:
        logger.error(
            f"Failed to get aggregated performance metrics via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve aggregated performance metrics: {str(e)}"
        )


@router.get(
    "/insights",
    response_model=Dict[str, Any],
    summary="Get Performance Insights",
    description="Get performance optimization insights and recommendations"
)
async def get_performance_insights(
    component: Optional[str] = Query(None, description="Filter by component"),
    hours_back: int = Query(24, ge=1, le=168, description="Number of hours to analyze"),
    current_user: User = Depends(require_permission_dependency("performance_monitoring_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> Dict[str, Any]:
    """
    Get performance optimization insights and recommendations.
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get performance insights
        insights = await performance_service.get_performance_insights(
            component=component,
            hours_back=hours_back
        )

        logger.info(
            f"Performance insights retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "component": component,
                "hours_back": hours_back
            }
        )

        return insights

    except Exception as e:
        logger.error(
            f"Failed to get performance insights via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve performance insights: {str(e)}"
        )


@router.post(
    "/health",
    response_model=SystemHealthResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record System Health Status",
    description="Record system health status with validation and alerting"
)
async def record_system_health(
    health_data: SystemHealthCreate,
    current_user: User = Depends(require_permission_dependency("system_health_create")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> SystemHealthResponse:
    """
    Record system health status with validation and alerting.

    Performance Metrics:
    - Target response time: <100ms for health status updates
    - Validation: <50ms using optimized patterns
    """
    try:
        correlation_id.set(correlation_id_val)

        # Record health status
        health_record = await health_service.record_health_status(
            component=health_data.component,
            service_name=health_data.service_name,
            status=health_data.status,
            response_time=health_data.response_time,
            cpu_usage=health_data.cpu_usage,
            memory_usage=health_data.memory_usage,
            disk_usage=health_data.disk_usage,
            dependencies_healthy=health_data.dependencies_healthy,
            health_details=health_data.health_details,
            error_details=health_data.error_details
        )

        logger.info(
            f"System health status recorded via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "health_id": health_record.id,
                "component": health_data.component,
                "status": health_data.status.value
            }
        )

        return SystemHealthResponse.model_validate(health_record)

    except Exception as e:
        logger.error(
            f"Failed to record system health via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to record system health: {str(e)}"
        )


@router.get(
    "/health/overview",
    response_model=Dict[str, Any],
    summary="Get System Health Overview",
    description="Get comprehensive system health overview with status summary"
)
async def get_system_health_overview(
    include_details: bool = Query(False, description="Include detailed health information"),
    current_user: User = Depends(require_permission_dependency("system_health_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> Dict[str, Any]:
    """
    Get comprehensive system health overview with status summary.

    Performance Metrics:
    - Target response time: <200ms for health overview queries
    - Cache optimization: 5-minute TTL for overview data
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get health overview
        overview = await health_service.get_system_health_overview(
            include_details=include_details
        )

        logger.info(
            f"System health overview retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "include_details": include_details
            }
        )

        return overview

    except Exception as e:
        logger.error(
            f"Failed to get system health overview via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve system health overview: {str(e)}"
        )


@router.get(
    "/health/unhealthy",
    response_model=List[SystemHealthResponse],
    summary="Get Unhealthy Components",
    description="Get components with unhealthy status for alerting and monitoring"
)
async def get_unhealthy_components(
    status_filter: Optional[List[SystemHealthStatus]] = Query(None, description="Filter by status"),
    current_user: User = Depends(require_permission_dependency("system_health_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> List[SystemHealthResponse]:
    """
    Get components with unhealthy status for alerting and monitoring.

    Performance Metrics:
    - Target response time: <100ms for unhealthy component queries
    - Cache optimization: 1-minute TTL for unhealthy status data
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get unhealthy components
        unhealthy_records = await health_service.get_unhealthy_components(
            status_filter=status_filter
        )

        logger.info(
            f"Unhealthy components retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "result_count": len(unhealthy_records)
            }
        )

        return [SystemHealthResponse.model_validate(record) for record in unhealthy_records]

    except Exception as e:
        logger.error(
            f"Failed to get unhealthy components via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve unhealthy components: {str(e)}"
        )


@router.post(
    "/load-test/config",
    response_model=Dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Create Load Test Configuration",
    description="Create load test configuration with validation"
)
async def create_load_test_config(
    config_data: LoadTestConfigCreate,
    current_user: User = Depends(require_permission_dependency("load_testing_create")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> Dict[str, Any]:
    """
    Create load test configuration with validation.
    """
    try:
        correlation_id.set(correlation_id_val)

        # Create load test configuration
        config = await load_testing_service.create_load_test_configuration(
            test_name=config_data.test_name,
            target_endpoint=config_data.target_endpoint,
            concurrent_users=config_data.concurrent_users,
            duration_seconds=config_data.duration_seconds,
            ramp_up_seconds=config_data.ramp_up_seconds,
            request_rate=config_data.request_rate,
            test_data=config_data.test_data,
            headers=config_data.headers
        )

        logger.info(
            f"Load test configuration created via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "test_id": config["test_id"],
                "test_name": config_data.test_name
            }
        )

        return config

    except Exception as e:
        logger.error(
            f"Failed to create load test configuration via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create load test configuration: {str(e)}"
        )


@router.post(
    "/load-test/execute",
    response_model=LoadTestResultsResponse,
    summary="Execute Load Test",
    description="Execute load test with real-time monitoring"
)
async def execute_load_test(
    config: Dict[str, Any] = Body(..., description="Load test configuration"),
    current_user: User = Depends(require_permission_dependency("load_testing_execute")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> LoadTestResultsResponse:
    """
    Execute load test with real-time monitoring.
    """
    try:
        correlation_id.set(correlation_id_val)

        # Execute load test
        results = await load_testing_service.execute_load_test(config)

        logger.info(
            f"Load test executed via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "test_id": results["test_id"],
                "test_name": results["test_name"],
                "total_requests": results["total_requests"]
            }
        )

        return LoadTestResultsResponse.model_validate(results)

    except Exception as e:
        logger.error(
            f"Failed to execute load test via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute load test: {str(e)}"
        )


@router.get(
    "/load-test/active",
    response_model=List[Dict[str, Any]],
    summary="Get Active Load Tests",
    description="Get currently active load tests"
)
async def get_active_load_tests(
    current_user: User = Depends(require_permission_dependency("load_testing_read")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> List[Dict[str, Any]]:
    """
    Get currently active load tests.
    """
    try:
        correlation_id.set(correlation_id_val)

        # Get active tests
        active_tests = await load_testing_service.get_active_tests()

        logger.info(
            f"Active load tests retrieved via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "active_tests_count": len(active_tests)
            }
        )

        return active_tests

    except Exception as e:
        logger.error(
            f"Failed to get active load tests via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve active load tests: {str(e)}"
        )


@router.post(
    "/load-test/{test_id}/stop",
    response_model=Dict[str, Any],
    summary="Stop Load Test",
    description="Stop an active load test"
)
async def stop_load_test(
    test_id: str,
    current_user: User = Depends(require_permission_dependency("load_testing_execute")),
    correlation_id_val: str = Depends(get_correlation_id)
) -> Dict[str, Any]:
    """
    Stop an active load test.
    """
    try:
        correlation_id.set(correlation_id_val)

        # Stop load test
        stopped = await load_testing_service.stop_load_test(test_id)

        if not stopped:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Load test not found: {test_id}"
            )

        logger.info(
            f"Load test stopped via API",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "test_id": test_id
            }
        )

        return {"test_id": test_id, "status": "stopped"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to stop load test via API: {str(e)}",
            extra={
                "correlation_id": correlation_id_val,
                "user_id": current_user.id,
                "test_id": test_id,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop load test: {str(e)}"
        )
