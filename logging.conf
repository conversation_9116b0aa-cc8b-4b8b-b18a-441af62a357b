# Culture Connect Backend - Production Logging Configuration
# Structured logging configuration for production deployment with correlation IDs

[loggers]
keys=root,uvicorn,uvicorn.error,uvicorn.access,app,sqlalchemy,celery,redis

[handlers]
keys=console,file,error_file,access_file,sentry

[formatters]
keys=json,detailed,access

[logger_root]
level=INFO
handlers=console,file,sentry
qualname=root

[logger_uvicorn]
level=INFO
handlers=console,file
qualname=uvicorn
propagate=0

[logger_uvicorn.error]
level=ERROR
handlers=console,error_file,sentry
qualname=uvicorn.error
propagate=0

[logger_uvicorn.access]
level=INFO
handlers=access_file
qualname=uvicorn.access
propagate=0

[logger_app]
level=INFO
handlers=console,file,sentry
qualname=app
propagate=0

[logger_sqlalchemy]
level=WARNING
handlers=console,file
qualname=sqlalchemy
propagate=0

[logger_celery]
level=INFO
handlers=console,file
qualname=celery
propagate=0

[logger_redis]
level=WARNING
handlers=console,file
qualname=redis
propagate=0

[handler_console]
class=StreamHandler
level=INFO
formatter=json
args=(sys.stdout,)

[handler_file]
class=logging.handlers.RotatingFileHandler
level=INFO
formatter=json
args=('/app/logs/culture_connect.log', 'a', 104857600, 5, 'utf-8')

[handler_error_file]
class=logging.handlers.RotatingFileHandler
level=ERROR
formatter=detailed
args=('/app/logs/culture_connect_error.log', 'a', 104857600, 5, 'utf-8')

[handler_access_file]
class=logging.handlers.RotatingFileHandler
level=INFO
formatter=access
args=('/app/logs/culture_connect_access.log', 'a', 104857600, 5, 'utf-8')

[handler_sentry]
class=sentry_sdk.integrations.logging.SentryHandler
level=ERROR
formatter=detailed

[formatter_json]
format={"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "process": %(process)d, "thread": %(thread)d}
datefmt=%Y-%m-%d %H:%M:%S

[formatter_detailed]
format=%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_access]
format=%(asctime)s - %(levelname)s - %(message)s
datefmt=%Y-%m-%d %H:%M:%S
