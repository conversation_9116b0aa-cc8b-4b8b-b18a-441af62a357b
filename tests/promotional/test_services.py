"""
Unit tests for promotional services.

This module provides comprehensive unit tests for promotional system services:
- CampaignService: Campaign management with business logic and validation
- AdvertisementService: Advertisement management with A/B testing support
- PromotionalListingService: Placement management with availability checking
- PromotionalAnalyticsService: Performance analytics and optimization insights
- PromotionalBillingService: Financial tracking and payment integration
- Business logic validation and error handling
- Integration with RBAC, payment, and notification systems
- Performance testing and optimization validation

Implements Phase 5: Comprehensive Testing Infrastructure with >85% test coverage.
"""

import pytest
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.promotional_services import (
    CampaignService, AdvertisementService, PromotionalListingService,
    PromotionalAnalyticsService, PromotionalBillingService
)
from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend,
    CampaignStatus, AdStatus, PlacementStatus
)
from app.core.exceptions import ValidationError, NotFoundError, PermissionError
from app.core.payment.config import PaymentProviderType


class TestCampaignService:
    """Test CampaignService functionality."""

    @pytest.fixture
    def campaign_service(self, mock_promotional_repository, mock_payment_service, mock_rbac_service):
        """Create campaign service with mocked dependencies."""
        return CampaignService(
            repository=mock_promotional_repository,
            payment_service=mock_payment_service,
            rbac_service=mock_rbac_service
        )

    @pytest.mark.asyncio
    async def test_create_campaign_success(self, campaign_service, sample_campaign_data, campaign_factory):
        """Test successful campaign creation."""
        created_campaign = campaign_factory()
        campaign_service.repository.create = AsyncMock(return_value=created_campaign)
        campaign_service.payment_service.validate_payment_method = AsyncMock(return_value=True)
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await campaign_service.create_campaign(
            vendor_id=1,
            campaign_data=sample_campaign_data,
            current_user_id=1
        )

        assert result == created_campaign
        campaign_service.repository.create.assert_called_once()
        campaign_service.payment_service.validate_payment_method.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_campaign_permission_denied(self, campaign_service, sample_campaign_data):
        """Test campaign creation with insufficient permissions."""
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=False)

        with pytest.raises(PermissionError) as exc_info:
            await campaign_service.create_campaign(
                vendor_id=1,
                campaign_data=sample_campaign_data,
                current_user_id=2  # Different user
            )

        assert "Insufficient permissions" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_campaign_budget_validation(self, campaign_service, sample_campaign_data):
        """Test campaign creation with invalid budget."""
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=True)

        # Test budget below minimum
        invalid_data = sample_campaign_data.copy()
        invalid_data["total_budget"] = 500.00  # Below ₦1,000 minimum

        with pytest.raises(ValidationError) as exc_info:
            await campaign_service.create_campaign(
                vendor_id=1,
                campaign_data=invalid_data,
                current_user_id=1
            )

        assert "Total budget must be at least ₦1,000" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_campaign_status(self, campaign_service, campaign_factory):
        """Test campaign status update."""
        campaign = campaign_factory(status=CampaignStatus.DRAFT)
        campaign_service.repository.get_by_id = AsyncMock(return_value=campaign)
        campaign_service.repository.update = AsyncMock(return_value=campaign)
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await campaign_service.update_campaign_status(
            campaign_id=1,
            new_status=CampaignStatus.ACTIVE,
            current_user_id=1
        )

        assert result.status == CampaignStatus.ACTIVE
        campaign_service.repository.update.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaign_performance(self, campaign_service, campaign_factory):
        """Test getting campaign performance metrics."""
        campaign = campaign_factory()
        performance_data = {
            "impressions": 10000,
            "clicks": 500,
            "conversions": 50,
            "spend": Decimal("2500.00"),
            "revenue": Decimal("25000.00"),
            "roas": Decimal("10.00")
        }

        campaign_service.repository.get_by_id = AsyncMock(return_value=campaign)
        campaign_service.repository.get_performance_metrics = AsyncMock(return_value=performance_data)

        result = await campaign_service.get_campaign_performance(
            campaign_id=1,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )

        assert result == performance_data
        assert result["roas"] == Decimal("10.00")

    @pytest.mark.asyncio
    async def test_pause_campaign(self, campaign_service, campaign_factory):
        """Test pausing an active campaign."""
        campaign = campaign_factory(status=CampaignStatus.ACTIVE)
        campaign_service.repository.get_by_id = AsyncMock(return_value=campaign)
        campaign_service.repository.update = AsyncMock(return_value=campaign)
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await campaign_service.pause_campaign(campaign_id=1, current_user_id=1)

        assert result.status == CampaignStatus.PAUSED
        campaign_service.repository.update.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_campaigns_by_vendor(self, campaign_service, campaign_factory):
        """Test getting campaigns by vendor with filtering."""
        campaigns = [campaign_factory(id=1), campaign_factory(id=2)]
        campaign_service.repository.get_by_vendor_id = AsyncMock(return_value=campaigns)
        campaign_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await campaign_service.get_campaigns_by_vendor(
            vendor_id=1,
            status_filter=CampaignStatus.ACTIVE,
            current_user_id=1
        )

        assert result == campaigns
        assert len(result) == 2
        campaign_service.repository.get_by_vendor_id.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_campaign_budget_allocation(self, campaign_service):
        """Test campaign budget allocation validation."""
        # Test valid budget allocation
        result = await campaign_service.validate_budget_allocation(
            total_budget=Decimal("10000.00"),
            daily_budget=Decimal("500.00"),
            campaign_duration_days=30
        )
        assert result is True

        # Test invalid budget allocation (daily budget too high)
        with pytest.raises(ValidationError) as exc_info:
            await campaign_service.validate_budget_allocation(
                total_budget=Decimal("10000.00"),
                daily_budget=Decimal("1000.00"),  # Would exceed total budget
                campaign_duration_days=15
            )
        assert "Daily budget allocation exceeds total budget" in str(exc_info.value)


class TestAdvertisementService:
    """Test AdvertisementService functionality."""

    @pytest.fixture
    def advertisement_service(self, mock_promotional_repository, mock_rbac_service):
        """Create advertisement service with mocked dependencies."""
        return AdvertisementService(
            repository=mock_promotional_repository,
            rbac_service=mock_rbac_service
        )

    @pytest.mark.asyncio
    async def test_create_advertisement(self, advertisement_service, sample_advertisement_data, advertisement_factory):
        """Test advertisement creation."""
        created_ad = advertisement_factory()
        advertisement_service.repository.create = AsyncMock(return_value=created_ad)
        advertisement_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await advertisement_service.create_advertisement(
            advertisement_data=sample_advertisement_data,
            current_user_id=1
        )

        assert result == created_ad
        advertisement_service.repository.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_ab_test_variants(self, advertisement_service, advertisement_factory):
        """Test creating A/B test variants."""
        variant_a = advertisement_factory(id=1, variant_name="variant_a")
        variant_b = advertisement_factory(id=2, variant_name="variant_b")

        advertisement_service.repository.create = AsyncMock(side_effect=[variant_a, variant_b])
        advertisement_service.rbac_service.check_permission = AsyncMock(return_value=True)

        variants_data = [
            {
                "campaign_id": 1,
                "name": "Test Ad Variant A",
                "headline": "Headline A",
                "variant_group": "test_group",
                "variant_name": "variant_a",
                "test_percentage": 50.0
            },
            {
                "campaign_id": 1,
                "name": "Test Ad Variant B",
                "headline": "Headline B",
                "variant_group": "test_group",
                "variant_name": "variant_b",
                "test_percentage": 50.0
            }
        ]

        result = await advertisement_service.create_ab_test_variants(
            variants_data=variants_data,
            current_user_id=1
        )

        assert len(result) == 2
        assert result[0].variant_name == "variant_a"
        assert result[1].variant_name == "variant_b"

    @pytest.mark.asyncio
    async def test_update_advertisement_performance(self, advertisement_service, advertisement_factory):
        """Test updating advertisement performance metrics."""
        ad = advertisement_factory()
        performance_data = {
            "impressions": 1000,
            "clicks": 50,
            "conversions": 5,
            "spend_amount": Decimal("250.00")
        }

        advertisement_service.repository.get_by_id = AsyncMock(return_value=ad)
        advertisement_service.repository.update_performance = AsyncMock(return_value=ad)

        result = await advertisement_service.update_performance_metrics(
            advertisement_id=1,
            performance_data=performance_data
        )

        assert result == ad
        advertisement_service.repository.update_performance.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_advertisement_variants_performance(self, advertisement_service, advertisement_factory):
        """Test getting A/B test variant performance comparison."""
        variants = [
            advertisement_factory(id=1, variant_name="variant_a", clicks=30, conversions=3),
            advertisement_factory(id=2, variant_name="variant_b", clicks=20, conversions=4)
        ]

        advertisement_service.repository.get_variants = AsyncMock(return_value=variants)

        result = await advertisement_service.get_variant_performance("test_group")

        assert len(result) == 2
        # Variant B should have better conversion rate (20% vs 10%)
        assert result[1]["conversion_rate"] > result[0]["conversion_rate"]

    @pytest.mark.asyncio
    async def test_validate_advertisement_content(self, advertisement_service):
        """Test advertisement content validation."""
        # Test valid content
        valid_content = {
            "headline": "Valid Advertisement Headline",
            "description_text": "Valid description without malicious content",
            "call_to_action": "Learn More"
        }

        result = await advertisement_service.validate_content(valid_content)
        assert result is True

        # Test XSS prevention
        malicious_content = {
            "headline": "<script>alert('xss')</script>Malicious Headline",
            "description_text": "Valid description",
            "call_to_action": "Click"
        }

        with pytest.raises(ValidationError) as exc_info:
            await advertisement_service.validate_content(malicious_content)
        assert "Invalid characters detected" in str(exc_info.value)


class TestPromotionalListingService:
    """Test PromotionalListingService functionality."""

    @pytest.fixture
    def listing_service(self, mock_promotional_repository, mock_rbac_service):
        """Create listing service with mocked dependencies."""
        return PromotionalListingService(
            repository=mock_promotional_repository,
            rbac_service=mock_rbac_service
        )

    @pytest.mark.asyncio
    async def test_check_placement_availability(self, listing_service):
        """Test checking placement availability."""
        listing_service.repository.check_availability = AsyncMock(return_value=3)

        result = await listing_service.check_placement_availability(
            placement_type="homepage_hero",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=30)
        )

        assert result == 3
        listing_service.repository.check_availability.assert_called_once()

    @pytest.mark.asyncio
    async def test_reserve_placement(self, listing_service, promotional_listing_factory):
        """Test reserving a placement."""
        listing = promotional_listing_factory(placement_status=PlacementStatus.AVAILABLE)
        listing_service.repository.get_by_id = AsyncMock(return_value=listing)
        listing_service.repository.reserve_placement = AsyncMock(return_value=listing)
        listing_service.rbac_service.check_permission = AsyncMock(return_value=True)

        result = await listing_service.reserve_placement(
            listing_id=1,
            current_user_id=1
        )

        assert result.placement_status == PlacementStatus.RESERVED
        listing_service.repository.reserve_placement.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_optimal_placement_recommendations(self, listing_service):
        """Test getting optimal placement recommendations."""
        recommendations = [
            {
                "placement_type": "homepage_hero",
                "priority_level": 10,
                "estimated_impressions": 50000,
                "estimated_ctr": 5.5,
                "recommended_bid": Decimal("30.00")
            },
            {
                "placement_type": "category_top",
                "priority_level": 8,
                "estimated_impressions": 25000,
                "estimated_ctr": 4.2,
                "recommended_bid": Decimal("20.00")
            }
        ]

        listing_service.repository.get_placement_recommendations = AsyncMock(return_value=recommendations)

        result = await listing_service.get_optimal_placements(
            service_id=1,
            budget=Decimal("5000.00"),
            target_audience="travel_enthusiasts"
        )

        assert len(result) == 2
        assert result[0]["placement_type"] == "homepage_hero"
        assert result[0]["priority_level"] == 10


class TestPromotionalAnalyticsService:
    """Test PromotionalAnalyticsService functionality."""

    @pytest.fixture
    def analytics_service(self, mock_promotional_repository):
        """Create analytics service with mocked dependencies."""
        return PromotionalAnalyticsService(repository=mock_promotional_repository)

    @pytest.mark.asyncio
    async def test_get_campaign_analytics(self, analytics_service):
        """Test getting comprehensive campaign analytics."""
        analytics_data = {
            "campaign_id": 1,
            "time_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "performance_metrics": {
                "total_impressions": 100000,
                "total_clicks": 5000,
                "total_conversions": 500,
                "total_spend": Decimal("25000.00"),
                "total_revenue": Decimal("250000.00"),
                "average_ctr": Decimal("5.00"),
                "average_conversion_rate": Decimal("10.00"),
                "roas": Decimal("10.00")
            },
            "trend_analysis": {
                "impressions_trend": "increasing",
                "ctr_trend": "stable",
                "conversion_trend": "improving",
                "spend_efficiency_trend": "optimizing"
            },
            "optimization_insights": [
                {
                    "type": "budget_optimization",
                    "priority": "high",
                    "description": "Increase daily budget by 20%",
                    "expected_impact": "15% increase in conversions"
                }
            ]
        }

        analytics_service.repository.get_campaign_analytics = AsyncMock(return_value=analytics_data)

        result = await analytics_service.get_campaign_analytics(
            campaign_id=1,
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 31)
        )

        assert result == analytics_data
        assert result["performance_metrics"]["roas"] == Decimal("10.00")
        assert result["trend_analysis"]["conversion_trend"] == "improving"

    @pytest.mark.asyncio
    async def test_generate_optimization_recommendations(self, analytics_service):
        """Test generating optimization recommendations."""
        campaign_data = {
            "campaign_id": 1,
            "current_performance": {
                "ctr": Decimal("3.5"),
                "conversion_rate": Decimal("8.0"),
                "roas": Decimal("6.5"),
                "budget_utilization": Decimal("85.0")
            },
            "industry_benchmarks": {
                "average_ctr": Decimal("4.2"),
                "average_conversion_rate": Decimal("10.0"),
                "average_roas": Decimal("8.0")
            }
        }

        analytics_service.repository.get_campaign_performance = AsyncMock(return_value=campaign_data)

        result = await analytics_service.generate_optimization_recommendations(campaign_id=1)

        # Should identify areas for improvement
        assert len(result) > 0
        # Should recommend CTR improvement since it's below benchmark
        ctr_recommendations = [r for r in result if "ctr" in r["description"].lower()]
        assert len(ctr_recommendations) > 0

    @pytest.mark.asyncio
    async def test_calculate_attribution_metrics(self, analytics_service):
        """Test calculating attribution metrics across touchpoints."""
        attribution_data = {
            "campaign_id": 1,
            "attribution_model": "last_click",
            "touchpoint_analysis": {
                "homepage_hero": {
                    "impressions": 50000,
                    "clicks": 2500,
                    "assisted_conversions": 150,
                    "direct_conversions": 200,
                    "attribution_weight": Decimal("0.70")
                },
                "category_top": {
                    "impressions": 30000,
                    "clicks": 1200,
                    "assisted_conversions": 80,
                    "direct_conversions": 100,
                    "attribution_weight": Decimal("0.30")
                }
            },
            "conversion_path_analysis": {
                "single_touch": 180,
                "multi_touch": 120,
                "average_path_length": 2.3
            }
        }

        analytics_service.repository.get_attribution_data = AsyncMock(return_value=attribution_data)

        result = await analytics_service.calculate_attribution_metrics(
            campaign_id=1,
            attribution_model="last_click"
        )

        assert result == attribution_data
        assert result["touchpoint_analysis"]["homepage_hero"]["attribution_weight"] == Decimal("0.70")

    @pytest.mark.asyncio
    async def test_get_competitive_analysis(self, analytics_service):
        """Test getting competitive analysis insights."""
        competitive_data = {
            "market_position": {
                "rank": 3,
                "market_share": Decimal("15.5"),
                "growth_rate": Decimal("12.3")
            },
            "competitor_insights": [
                {
                    "competitor": "Competitor A",
                    "estimated_spend": Decimal("50000.00"),
                    "estimated_impressions": 200000,
                    "placement_overlap": Decimal("35.0")
                },
                {
                    "competitor": "Competitor B",
                    "estimated_spend": Decimal("30000.00"),
                    "estimated_impressions": 150000,
                    "placement_overlap": Decimal("25.0")
                }
            ],
            "opportunity_analysis": {
                "underutilized_placements": ["email_newsletter", "mobile_banner"],
                "optimal_bid_adjustments": {
                    "homepage_hero": "+15%",
                    "category_top": "+5%"
                }
            }
        }

        analytics_service.repository.get_competitive_analysis = AsyncMock(return_value=competitive_data)

        result = await analytics_service.get_competitive_analysis(
            vendor_id=1,
            category="tourism",
            time_period=30
        )

        assert result == competitive_data
        assert result["market_position"]["rank"] == 3
        assert len(result["competitor_insights"]) == 2


class TestPromotionalBillingService:
    """Test PromotionalBillingService functionality."""

    @pytest.fixture
    def billing_service(self, mock_promotional_repository, mock_payment_service):
        """Create billing service with mocked dependencies."""
        return PromotionalBillingService(
            repository=mock_promotional_repository,
            payment_service=mock_payment_service
        )

    @pytest.mark.asyncio
    async def test_process_campaign_billing(self, billing_service, ad_spend_factory):
        """Test processing campaign billing."""
        spend_records = [
            ad_spend_factory(id=1, amount=Decimal("1000.00")),
            ad_spend_factory(id=2, amount=Decimal("1500.00"))
        ]

        billing_service.repository.get_pending_billing = AsyncMock(return_value=spend_records)
        billing_service.payment_service.process_payment = AsyncMock(return_value={"status": "completed"})
        billing_service.repository.update_billing_status = AsyncMock()

        result = await billing_service.process_campaign_billing(
            vendor_id=1,
            billing_period_start=datetime.now(timezone.utc) - timedelta(days=1),
            billing_period_end=datetime.now(timezone.utc)
        )

        assert result["total_amount"] == Decimal("2500.00")
        assert result["status"] == "completed"
        billing_service.payment_service.process_payment.assert_called_once()

    @pytest.mark.asyncio
    async def test_calculate_platform_fees(self, billing_service):
        """Test calculating platform fees."""
        spend_amount = Decimal("10000.00")

        result = await billing_service.calculate_platform_fees(
            spend_amount=spend_amount,
            vendor_tier="premium",
            payment_provider=PaymentProviderType.PAYSTACK
        )

        # Premium tier should have lower fees
        assert result["platform_fee_rate"] == Decimal("0.025")  # 2.5%
        assert result["platform_fee"] == Decimal("250.00")
        assert result["payment_provider_fee"] == Decimal("150.00")  # 1.5% for Paystack
        assert result["total_fees"] == Decimal("400.00")

    @pytest.mark.asyncio
    async def test_generate_billing_invoice(self, billing_service):
        """Test generating billing invoice."""
        billing_data = {
            "vendor_id": 1,
            "billing_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "total_spend": Decimal("15000.00"),
            "platform_fees": Decimal("750.00"),
            "tax_amount": Decimal("1125.00"),
            "total_amount": Decimal("16875.00")
        }

        billing_service.repository.get_billing_summary = AsyncMock(return_value=billing_data)

        result = await billing_service.generate_invoice(
            vendor_id=1,
            billing_period_start=date(2025, 1, 1),
            billing_period_end=date(2025, 1, 31)
        )

        assert result["total_amount"] == Decimal("16875.00")
        assert result["invoice_number"] is not None
        assert result["due_date"] is not None

    @pytest.mark.asyncio
    async def test_reconcile_payments(self, billing_service):
        """Test payment reconciliation process."""
        unreconciled_records = [
            {"id": 1, "amount": Decimal("1000.00"), "payment_reference": "PAY-001"},
            {"id": 2, "amount": Decimal("1500.00"), "payment_reference": "PAY-002"}
        ]

        billing_service.repository.get_unreconciled_payments = AsyncMock(return_value=unreconciled_records)
        billing_service.payment_service.verify_payment_status = AsyncMock(return_value="completed")
        billing_service.repository.mark_as_reconciled = AsyncMock(return_value=2)

        result = await billing_service.reconcile_payments(
            reconciliation_date=date.today()
        )

        assert result["reconciled_count"] == 2
        assert result["total_reconciled_amount"] == Decimal("2500.00")
        billing_service.repository.mark_as_reconciled.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_payment_failure(self, billing_service):
        """Test handling payment failures."""
        failed_payment_data = {
            "vendor_id": 1,
            "amount": Decimal("5000.00"),
            "failure_reason": "Insufficient funds",
            "retry_count": 1
        }

        billing_service.repository.log_payment_failure = AsyncMock()
        billing_service.payment_service.schedule_retry = AsyncMock()

        result = await billing_service.handle_payment_failure(failed_payment_data)

        assert result["action"] == "retry_scheduled"
        assert result["next_retry_date"] is not None
        billing_service.repository.log_payment_failure.assert_called_once()


class TestServiceIntegration:
    """Test service integration and cross-service functionality."""

    @pytest.mark.asyncio
    async def test_campaign_to_billing_integration(self, mock_promotional_repository, mock_payment_service):
        """Test integration between campaign and billing services."""
        campaign_service = CampaignService(
            repository=mock_promotional_repository,
            payment_service=mock_payment_service,
            rbac_service=AsyncMock()
        )
        billing_service = PromotionalBillingService(
            repository=mock_promotional_repository,
            payment_service=mock_payment_service
        )

        # Mock campaign completion triggering billing
        campaign_service.repository.get_by_id = AsyncMock(return_value=MagicMock(id=1, vendor_id=1))
        billing_service.repository.get_campaign_spend = AsyncMock(return_value=Decimal("5000.00"))
        billing_service.payment_service.process_payment = AsyncMock(return_value={"status": "completed"})

        # Complete campaign and trigger billing
        await campaign_service.complete_campaign(campaign_id=1)
        billing_result = await billing_service.process_campaign_billing(vendor_id=1)

        assert billing_result["status"] == "completed"

    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, mock_promotional_repository):
        """Test performance monitoring across services."""
        analytics_service = PromotionalAnalyticsService(repository=mock_promotional_repository)

        # Mock performance data
        performance_data = {
            "response_time_ms": 150,
            "query_count": 5,
            "cache_hit_rate": 0.95,
            "error_rate": 0.01
        }

        analytics_service.repository.get_performance_metrics = AsyncMock(return_value=performance_data)

        result = await analytics_service.get_service_performance_metrics()

        # Should meet performance targets
        assert result["response_time_ms"] < 200  # <200ms target
        assert result["cache_hit_rate"] > 0.90   # >90% cache hit rate
        assert result["error_rate"] < 0.05       # <5% error rate
