"""
Extended service schemas for Culture Connect Backend API.

This module contains the remaining service schemas including availability,
main service schemas, and comprehensive response models.

Continuation of app/schemas/service.py for Task 3.2.1 implementation.
"""

from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union
from uuid import UUID

from pydantic import BaseModel, Field, validator, root_validator, ConfigDict, HttpUrl

from app.models.service import ServiceStatus, PricingType, AvailabilityType
from app.schemas.service import (
    ServiceCategoryResponse, ServiceImageResponse,
    ServicePricingResponse
)


# Service Availability schemas
class ServiceAvailabilityBase(BaseModel):
    """Base schema for service availability."""

    available_date: date = Field(..., description="Available date")

    start_time: Optional[time] = Field(
        None,
        description="Start time (null for all-day availability)"
    )

    end_time: Optional[time] = Field(
        None,
        description="End time (null for all-day availability)"
    )

    max_bookings: int = Field(
        default=1,
        ge=1,
        description="Maximum number of bookings for this slot"
    )

    current_bookings: int = Field(
        default=0,
        ge=0,
        description="Current number of bookings"
    )

    price_override: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Price override for this specific slot"
    )

    is_available: bool = Field(
        default=True,
        description="Whether slot is available for booking"
    )

    notes: Optional[str] = Field(
        None,
        description="Special notes for this availability slot"
    )

    is_recurring: bool = Field(
        default=False,
        description="Whether this is a recurring availability"
    )

    recurrence_pattern: Optional[Dict[str, Any]] = Field(
        None,
        description="Recurrence pattern details"
    )

    recurrence_end_date: Optional[date] = Field(
        None,
        description="End date for recurring availability"
    )

    @validator('current_bookings')
    def validate_current_bookings(cls, v, values):
        """Validate current_bookings doesn't exceed max_bookings."""
        if 'max_bookings' in values and v > values['max_bookings']:
            raise ValueError('current_bookings cannot exceed max_bookings')
        return v

    @validator('end_time')
    def validate_end_time(cls, v, values):
        """Validate end_time is after start_time."""
        if v is not None and 'start_time' in values and values['start_time'] is not None:
            if v <= values['start_time']:
                raise ValueError('end_time must be after start_time')
        return v


class ServiceAvailabilityCreate(ServiceAvailabilityBase):
    """Schema for creating service availability."""

    service_id: int = Field(..., description="Associated service ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "service_id": 1,
                "available_date": "2025-02-01",
                "start_time": "10:00:00",
                "end_time": "12:00:00",
                "max_bookings": 2,
                "current_bookings": 0,
                "price_override": None,
                "is_available": True,
                "notes": "Morning session with traditional instruments",
                "is_recurring": False,
                "recurrence_pattern": None,
                "recurrence_end_date": None
            }
        }
    )


class ServiceAvailabilityUpdate(BaseModel):
    """Schema for updating service availability."""

    available_date: Optional[date] = Field(None, description="Available date")
    start_time: Optional[time] = Field(None, description="Start time")
    end_time: Optional[time] = Field(None, description="End time")
    max_bookings: Optional[int] = Field(None, ge=1, description="Max bookings")
    current_bookings: Optional[int] = Field(None, ge=0, description="Current bookings")
    price_override: Optional[Decimal] = Field(None, ge=0, description="Price override")
    is_available: Optional[bool] = Field(None, description="Is available")
    notes: Optional[str] = Field(None, description="Notes")
    is_recurring: Optional[bool] = Field(None, description="Is recurring")
    recurrence_pattern: Optional[Dict[str, Any]] = Field(None, description="Recurrence pattern")
    recurrence_end_date: Optional[date] = Field(None, description="Recurrence end date")


class ServiceAvailabilityResponse(ServiceAvailabilityBase):
    """Schema for service availability responses."""

    id: int = Field(..., description="Availability ID")
    uuid: UUID = Field(..., description="Availability UUID")
    service_id: int = Field(..., description="Associated service ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Computed properties
    available_spots: int = Field(..., description="Available spots for booking")
    is_fully_booked: bool = Field(..., description="Whether slot is fully booked")

    model_config = ConfigDict(from_attributes=True)


# Main Service schemas
class ServiceBase(BaseModel):
    """Base schema for services."""

    title: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Service title"
    )

    slug: str = Field(
        ...,
        min_length=1,
        max_length=255,
        pattern=r'^[a-z0-9\-]+$',
        description="URL-friendly service identifier"
    )

    description: str = Field(
        ...,
        min_length=10,
        description="Detailed service description"
    )

    short_description: Optional[str] = Field(
        None,
        max_length=500,
        description="Short service description for listings"
    )

    category_id: Optional[int] = Field(
        None,
        description="Service category ID"
    )

    duration_minutes: Optional[int] = Field(
        None,
        ge=1,
        description="Service duration in minutes"
    )

    max_participants: Optional[int] = Field(
        None,
        ge=1,
        description="Maximum number of participants"
    )

    min_participants: int = Field(
        default=1,
        ge=1,
        description="Minimum number of participants"
    )

    location: Optional[str] = Field(
        None,
        max_length=500,
        description="Service location or meeting point"
    )

    includes: Optional[List[str]] = Field(
        default_factory=list,
        description="What's included in the service"
    )

    excludes: Optional[List[str]] = Field(
        default_factory=list,
        description="What's not included in the service"
    )

    requirements: Optional[List[str]] = Field(
        default_factory=list,
        description="Service requirements and restrictions"
    )

    pricing_type: PricingType = Field(
        default=PricingType.FIXED,
        description="Type of pricing model"
    )

    base_price: Decimal = Field(
        ...,
        ge=0,
        description="Base price for the service"
    )

    currency: str = Field(
        default="USD",
        min_length=3,
        max_length=3,
        pattern=r'^[A-Z]{3}$',
        description="Currency code (ISO 4217)"
    )

    pricing_details: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Detailed pricing information"
    )

    availability_type: AvailabilityType = Field(
        default=AvailabilityType.SCHEDULED,
        description="Type of availability"
    )

    advance_booking_hours: int = Field(
        default=24,
        ge=0,
        description="Minimum advance booking hours"
    )

    cancellation_hours: int = Field(
        default=24,
        ge=0,
        description="Cancellation deadline in hours"
    )

    status: ServiceStatus = Field(
        default=ServiceStatus.DRAFT,
        description="Service status"
    )

    is_featured: bool = Field(
        default=False,
        description="Whether service is featured"
    )

    is_instant_booking: bool = Field(
        default=False,
        description="Whether instant booking is enabled"
    )

    meta_title: Optional[str] = Field(
        None,
        max_length=255,
        description="SEO meta title"
    )

    meta_description: Optional[str] = Field(
        None,
        max_length=500,
        description="SEO meta description"
    )

    tags: Optional[List[str]] = Field(
        default_factory=list,
        description="Service tags for search and filtering"
    )

    extra_data: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional service metadata"
    )

    @validator('max_participants')
    def validate_max_participants(cls, v, values):
        """Validate max_participants is greater than min_participants."""
        if v is not None and 'min_participants' in values:
            min_participants = values['min_participants']
            if v < min_participants:
                raise ValueError('max_participants must be greater than or equal to min_participants')
        return v


class ServiceCreate(ServiceBase):
    """Schema for creating services."""

    vendor_id: int = Field(..., description="Associated vendor ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vendor_id": 1,
                "title": "Traditional Nigerian Dance Workshop",
                "slug": "traditional-nigerian-dance-workshop",
                "description": "Learn authentic traditional Nigerian dance moves from experienced instructors. This immersive workshop covers various regional dance styles including Igbo, Yoruba, and Hausa traditional dances.",
                "short_description": "Learn authentic traditional Nigerian dance moves",
                "category_id": 1,
                "duration_minutes": 120,
                "max_participants": 15,
                "min_participants": 5,
                "location": "Cultural Center, Lagos",
                "includes": ["Traditional costumes", "Live drumming", "Refreshments"],
                "excludes": ["Transportation", "Personal items"],
                "requirements": ["Comfortable clothing", "Basic fitness level"],
                "pricing_type": "fixed",
                "base_price": "75.00",
                "currency": "USD",
                "pricing_details": {"group_discount": "10% for 10+ people"},
                "availability_type": "scheduled",
                "advance_booking_hours": 48,
                "cancellation_hours": 24,
                "status": "draft",
                "is_featured": False,
                "is_instant_booking": False,
                "meta_title": "Traditional Nigerian Dance Workshop - Culture Connect",
                "meta_description": "Experience authentic Nigerian culture through traditional dance",
                "tags": ["dance", "culture", "traditional", "nigeria", "workshop"],
                "extra_data": {"difficulty_level": "beginner", "age_range": "12+"}
            }
        }
    )


class ServiceUpdate(BaseModel):
    """Schema for updating services."""

    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Service title")
    slug: Optional[str] = Field(None, min_length=1, max_length=255, pattern=r'^[a-z0-9\-]+$', description="Service slug")
    description: Optional[str] = Field(None, min_length=10, description="Service description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short description")
    category_id: Optional[int] = Field(None, description="Category ID")
    duration_minutes: Optional[int] = Field(None, ge=1, description="Duration in minutes")
    max_participants: Optional[int] = Field(None, ge=1, description="Max participants")
    min_participants: Optional[int] = Field(None, ge=1, description="Min participants")
    location: Optional[str] = Field(None, max_length=500, description="Location")
    includes: Optional[List[str]] = Field(None, description="What's included")
    excludes: Optional[List[str]] = Field(None, description="What's excluded")
    requirements: Optional[List[str]] = Field(None, description="Requirements")
    pricing_type: Optional[PricingType] = Field(None, description="Pricing type")
    base_price: Optional[Decimal] = Field(None, ge=0, description="Base price")
    currency: Optional[str] = Field(None, min_length=3, max_length=3, pattern=r'^[A-Z]{3}$', description="Currency")
    pricing_details: Optional[Dict[str, Any]] = Field(None, description="Pricing details")
    availability_type: Optional[AvailabilityType] = Field(None, description="Availability type")
    advance_booking_hours: Optional[int] = Field(None, ge=0, description="Advance booking hours")
    cancellation_hours: Optional[int] = Field(None, ge=0, description="Cancellation hours")
    status: Optional[ServiceStatus] = Field(None, description="Service status")
    is_featured: Optional[bool] = Field(None, description="Is featured")
    is_instant_booking: Optional[bool] = Field(None, description="Is instant booking")
    meta_title: Optional[str] = Field(None, max_length=255, description="Meta title")
    meta_description: Optional[str] = Field(None, max_length=500, description="Meta description")
    tags: Optional[List[str]] = Field(None, description="Tags")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="Extra data")


class ServiceResponse(ServiceBase):
    """Schema for service responses."""

    id: int = Field(..., description="Service ID")
    uuid: UUID = Field(..., description="Service UUID")
    vendor_id: int = Field(..., description="Associated vendor ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # Performance metrics
    total_bookings: int = Field(..., description="Total number of bookings")
    average_rating: Decimal = Field(..., description="Average customer rating")
    total_reviews: int = Field(..., description="Total number of reviews")

    # Computed properties
    is_available: bool = Field(..., description="Whether service is currently available")

    # Relationships
    category: Optional[ServiceCategoryResponse] = Field(None, description="Service category")
    images: List[ServiceImageResponse] = Field(default_factory=list, description="Service images")
    pricing_tiers: List[ServicePricingResponse] = Field(default_factory=list, description="Pricing tiers")
    availability_slots: List[ServiceAvailabilityResponse] = Field(default_factory=list, description="Availability slots")

    model_config = ConfigDict(from_attributes=True)


class ServiceListResponse(BaseModel):
    """Schema for service list responses with pagination."""

    services: List[ServiceResponse] = Field(..., description="List of services")
    total: int = Field(..., description="Total number of services")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_prev: bool = Field(..., description="Whether there are previous pages")


class ServiceSearchRequest(BaseModel):
    """Schema for service search requests."""

    query: Optional[str] = Field(None, description="Search query")
    category_id: Optional[int] = Field(None, description="Filter by category")
    vendor_id: Optional[int] = Field(None, description="Filter by vendor")
    location: Optional[str] = Field(None, description="Filter by location")
    min_price: Optional[Decimal] = Field(None, ge=0, description="Minimum price")
    max_price: Optional[Decimal] = Field(None, ge=0, description="Maximum price")
    currency: Optional[str] = Field(None, min_length=3, max_length=3, description="Currency filter")
    pricing_type: Optional[PricingType] = Field(None, description="Pricing type filter")
    availability_type: Optional[AvailabilityType] = Field(None, description="Availability type filter")
    status: Optional[ServiceStatus] = Field(None, description="Status filter")
    is_featured: Optional[bool] = Field(None, description="Featured services only")
    is_instant_booking: Optional[bool] = Field(None, description="Instant booking only")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    date_from: Optional[date] = Field(None, description="Available from date")
    date_to: Optional[date] = Field(None, description="Available to date")
    sort_by: Optional[str] = Field(
        default="created_at",
        description="Sort field (created_at, title, price, rating)"
    )
    sort_order: Optional[str] = Field(
        default="desc",
        pattern=r'^(asc|desc)$',
        description="Sort order (asc, desc)"
    )
    page: int = Field(default=1, ge=1, description="Page number")
    per_page: int = Field(default=20, ge=1, le=100, description="Items per page")


# Bulk operation schemas
class ServiceBulkUpdateRequest(BaseModel):
    """Schema for bulk service updates."""

    service_ids: List[int] = Field(..., min_items=1, description="List of service IDs to update")
    updates: ServiceUpdate = Field(..., description="Updates to apply")


class ServiceBulkStatusUpdateRequest(BaseModel):
    """Schema for bulk service status updates."""

    service_ids: List[int] = Field(..., min_items=1, description="List of service IDs to update")
    status: ServiceStatus = Field(..., description="New status to apply")


class ServiceBulkDeleteRequest(BaseModel):
    """Schema for bulk service deletion."""

    service_ids: List[int] = Field(..., min_items=1, description="List of service IDs to delete")
    confirm: bool = Field(..., description="Confirmation flag for deletion")