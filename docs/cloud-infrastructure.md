# Culture Connect Backend - Cloud Infrastructure Documentation

## Overview

The Culture Connect Backend supports three deployment strategies to accommodate different scales, budgets, and requirements:

1. **AWS EKS (Enterprise Production)** - Fully managed Kubernetes for high-scale production
2. **Docker Swarm (Staging/Development)** - Mid-scale deployment with service orchestration
3. **Hetzner Cloud + k3s (Cost-Effective)** - Budget-friendly self-hosting for startups

## Deployment Strategy Comparison

| Feature | AWS EKS | Docker Swarm | Hetzner + k3s |
|---------|---------|--------------|---------------|
| **Target Users** | >10,000 concurrent | 1,000-10,000 | <5,000 concurrent |
| **Monthly Cost** | $500-2000+ | $200-800 | $100-300 |
| **Complexity** | Medium (managed) | Low-Medium | Medium |
| **Auto-scaling** | Full HPA/VPA | Manual scaling | Manual/basic HPA |
| **High Availability** | Multi-AZ | Multi-node | Multi-node |
| **Managed Services** | RDS, ElastiCache | External services | Self-hosted |
| **Best For** | Enterprise, high-scale | Development, staging | Startups, cost-conscious |

## AWS EKS Deployment (Primary Production)

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                        AWS EKS Cluster                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Master    │  │   Master    │  │   Master    │        │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Worker    │  │   Worker    │  │   Worker    │        │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                    Managed Services                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     RDS     │  │ ElastiCache │  │     S3      │        │
│  │ PostgreSQL  │  │    Redis    │  │   Storage   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### Key Features

- **Managed Kubernetes**: AWS handles control plane management
- **Auto-scaling**: HPA for pods, Cluster Autoscaler for nodes
- **High Availability**: Multi-AZ deployment with automatic failover
- **Managed Databases**: RDS PostgreSQL with automated backups
- **Security**: IAM integration, VPC isolation, encryption at rest/transit
- **Monitoring**: CloudWatch, Prometheus, Grafana integration
- **Load Balancing**: AWS ALB with SSL termination

### Resource Configuration

```yaml
# EKS Cluster
- Control Plane: Managed by AWS
- Node Groups: 3-50 nodes (t3.large to c5.4xlarge)
- Networking: VPC with public/private subnets
- Storage: EBS volumes with encryption

# Managed Services
- RDS PostgreSQL: db.t3.medium to db.r5.2xlarge
- ElastiCache Redis: cache.t3.medium to cache.r5.xlarge
- S3: Application storage and backups
- ALB: Application Load Balancer with WAF
```

### Deployment Commands

```bash
# Deploy AWS infrastructure
cd infrastructure/terraform/aws
terraform init
terraform plan -var-file="production.tfvars"
terraform apply

# Deploy Kubernetes applications
kubectl apply -f infrastructure/kubernetes/base/
kubectl apply -f infrastructure/monitoring/

# Verify deployment
kubectl get nodes
kubectl get pods -n culture-connect
kubectl get ingress -n culture-connect
```

## Hetzner Cloud + k3s Deployment (Cost-Effective)

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Hetzner Cloud k3s                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ k3s Master  │  │ k3s Master  │  │ k3s Master  │        │
│  │   CPX21     │  │   CPX21     │  │   CPX21     │        │
│  │ 3CPU/8GB    │  │ 3CPU/8GB    │  │ 3CPU/8GB    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ k3s Worker  │  │ k3s Worker  │  │ k3s Worker  │        │
│  │   CPX31     │  │   CPX31     │  │   CPX31     │        │
│  │ 4CPU/16GB   │  │ 4CPU/16GB   │  │ 4CPU/16GB   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                   Self-Hosted Services                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │    Redis    │  │Load Balancer│        │
│  │   CPX21     │  │   CPX11     │  │    LB11     │        │
│  │ 3CPU/8GB    │  │ 2CPU/4GB    │  │  5K conn    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### Cost Breakdown (Monthly EUR)

| Component | Type | Count | Unit Cost | Total |
|-----------|------|-------|-----------|-------|
| k3s Masters | CPX21 | 3 | €8.21 | €24.63 |
| k3s Workers | CPX31 | 3 | €16.41 | €49.23 |
| PostgreSQL | CPX21 | 1 | €8.21 | €8.21 |
| Redis | CPX11 | 1 | €4.51 | €4.51 |
| Load Balancer | LB11 | 1 | €5.39 | €5.39 |
| Storage | 120GB | 1 | €0.10/GB | €12.00 |
| Floating IP | IPv4 | 1 | €1.19 | €1.19 |
| **Total** | | | | **€105.16** |

### Key Features

- **Lightweight Kubernetes**: k3s with minimal resource overhead
- **Cost Optimization**: 60-80% cost savings compared to AWS
- **High Availability**: Multi-master setup with load balancer
- **Private Networking**: Isolated private network for security
- **Persistent Storage**: Dedicated volumes for data persistence
- **SSL Termination**: Load balancer with SSL/TLS support

### Deployment Commands

```bash
# Deploy Hetzner infrastructure
cd infrastructure/terraform/hetzner
terraform init
terraform plan -var-file="production.tfvars"
terraform apply

# Get kubeconfig
scp root@MASTER_IP:/etc/rancher/k3s/k3s.yaml ./kubeconfig
sed -i 's/127.0.0.1/LOAD_BALANCER_IP/g' ./kubeconfig
export KUBECONFIG=./kubeconfig

# Deploy applications
kubectl apply -f infrastructure/kubernetes/base/
kubectl apply -f infrastructure/monitoring/prometheus/
kubectl apply -f infrastructure/monitoring/grafana/

# Verify deployment
kubectl get nodes
kubectl get pods -A
```

## Monitoring and Observability

### Prometheus Configuration

- **Metrics Collection**: Application, system, and custom metrics
- **Retention**: 30 days (AWS) / 15 days (Hetzner)
- **Storage**: 50GB persistent volume
- **Scrape Interval**: 15 seconds
- **Alert Rules**: Performance, error rate, resource utilization

### Grafana Dashboards

- **API Performance**: Request rate, response time, error rate
- **Infrastructure**: CPU, memory, disk, network utilization
- **Database**: Connection pool, query performance, replication lag
- **Business Metrics**: User activity, payment processing, feature usage

### Alert Configuration

```yaml
# Critical Alerts
- API Error Rate > 5%
- Response Time P95 > 500ms
- Memory Utilization > 90%
- Database Connections > 80%

# Warning Alerts
- CPU Utilization > 80%
- Disk Usage > 85%
- Queue Length > 100
- SSL Certificate Expiry < 30 days
```

## Auto-scaling Configuration

### Horizontal Pod Autoscaler (HPA)

```yaml
# API Service Scaling
minReplicas: 3
maxReplicas: 50
targetCPUUtilization: 70%
targetMemoryUtilization: 80%
scaleUpPolicy: 50% increase, max 5 pods/minute
scaleDownPolicy: 10% decrease, max 2 pods/minute
```

### Vertical Pod Autoscaler (VPA)

```yaml
# Resource Optimization
updateMode: Auto
minAllowed: 100m CPU, 256Mi memory
maxAllowed: 2 CPU, 4Gi memory
controlledResources: [cpu, memory]
```

### Cluster Autoscaler (AWS only)

```yaml
# Node Scaling
minNodes: 3
maxNodes: 20
scaleDownDelay: 10m
scaleDownUnneededTime: 10m
scaleDownUtilizationThreshold: 50%
```

## Security Configuration

### Network Security

- **VPC/Private Network**: Isolated network with private subnets
- **Security Groups/Firewall**: Restrictive ingress rules
- **Network Policies**: Pod-to-pod communication control
- **SSL/TLS**: End-to-end encryption with cert-manager

### Access Control

- **RBAC**: Kubernetes role-based access control
- **IAM Integration**: AWS IAM for service accounts (AWS only)
- **SSH Access**: Key-based authentication only
- **API Access**: Token-based authentication with rotation

### Data Protection

- **Encryption at Rest**: Database, storage volumes, secrets
- **Encryption in Transit**: All network communication
- **Backup Encryption**: Encrypted backup storage
- **Secret Management**: Kubernetes secrets / AWS Secrets Manager

## Backup and Disaster Recovery

### Automated Backups

```bash
# Database Backup (Daily)
kubectl create cronjob postgres-backup \
  --image=postgres:15 \
  --schedule="0 2 * * *" \
  -- pg_dump -h postgres -U user database

# Application Data Backup
kubectl create cronjob app-backup \
  --image=culture-connect:latest \
  --schedule="0 3 * * *" \
  -- python backup_script.py

# Cluster State Backup (k3s)
k3s etcd-snapshot save --name backup-$(date +%Y%m%d)
```

### Recovery Procedures

```bash
# Database Recovery
kubectl exec -i postgres-pod -- psql -U user database < backup.sql

# Application Recovery
kubectl apply -f backup/kubernetes-manifests/

# Cluster Recovery (k3s)
k3s server --cluster-init --cluster-reset \
  --cluster-reset-restore-path=backup.db
```

## Performance Optimization

### Database Optimization

- **Connection Pooling**: PgBouncer with 200 max connections
- **Query Optimization**: Indexes, query analysis, slow query monitoring
- **Read Replicas**: For read-heavy workloads (AWS only)
- **Caching**: Redis for session storage and application caching

### Application Optimization

- **Resource Limits**: CPU and memory limits for all containers
- **Health Checks**: Liveness, readiness, and startup probes
- **Image Optimization**: Multi-stage builds, minimal base images
- **Caching Strategy**: Multi-layer caching (Redis, CDN, browser)

### Network Optimization

- **Load Balancing**: Round-robin with health checks
- **SSL Optimization**: HTTP/2, OCSP stapling, session resumption
- **Compression**: Gzip/Brotli compression for responses
- **CDN Integration**: CloudFront (AWS) or external CDN

## Troubleshooting Guide

### Common Issues

1. **Pod Startup Failures**
   ```bash
   kubectl describe pod POD_NAME
   kubectl logs POD_NAME --previous
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it api-pod -- nc -zv postgres-service 5432
   kubectl get endpoints postgres-service
   ```

3. **SSL Certificate Issues**
   ```bash
   kubectl describe certificate culture-connect-tls
   kubectl logs -n cert-manager deployment/cert-manager
   ```

4. **Load Balancer Issues**
   ```bash
   kubectl describe ingress culture-connect-api
   kubectl get events --sort-by=.metadata.creationTimestamp
   ```

### Performance Issues

1. **High Response Times**
   - Check HPA scaling status
   - Review database query performance
   - Analyze application metrics in Grafana

2. **Memory Issues**
   - Check VPA recommendations
   - Review memory usage patterns
   - Adjust resource limits if needed

3. **Database Performance**
   - Check connection pool utilization
   - Review slow query logs
   - Analyze database metrics

## Cost Optimization

### AWS Cost Optimization

- **Reserved Instances**: 1-3 year commitments for 30-60% savings
- **Spot Instances**: For non-critical workloads (70-90% savings)
- **Right-sizing**: Regular review of instance types and sizes
- **Storage Optimization**: Lifecycle policies for S3, EBS optimization

### Hetzner Cost Optimization

- **Resource Monitoring**: Regular review of resource utilization
- **Auto-shutdown**: Development environments during off-hours
- **Storage Cleanup**: Regular cleanup of logs and temporary files
- **Load Balancer Optimization**: Combine multiple services on single LB

## Migration Guide

### AWS to Hetzner Migration

1. **Data Export**: Export database and application data
2. **Infrastructure Setup**: Deploy Hetzner infrastructure
3. **Application Deployment**: Deploy applications to new cluster
4. **Data Import**: Import data to new environment
5. **DNS Cutover**: Update DNS to point to new load balancer
6. **Verification**: Verify all functionality works correctly

### Hetzner to AWS Migration

1. **AWS Setup**: Deploy AWS infrastructure with Terraform
2. **Data Migration**: Use AWS DMS for database migration
3. **Application Deployment**: Deploy to EKS cluster
4. **Load Testing**: Verify performance under load
5. **DNS Cutover**: Update DNS to point to AWS ALB
6. **Monitoring**: Monitor for any issues post-migration

## Support and Maintenance

### Regular Maintenance Tasks

- **Weekly**: Review monitoring alerts, check resource utilization
- **Monthly**: Update applications, review security patches
- **Quarterly**: Review costs, optimize resources, update documentation

### Emergency Procedures

- **Incident Response**: Defined escalation procedures
- **Rollback Procedures**: Automated rollback for failed deployments
- **Communication**: Status page updates, stakeholder notifications
- **Post-Incident**: Root cause analysis and improvement plans
