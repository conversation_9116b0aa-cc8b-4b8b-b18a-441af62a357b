#!/usr/bin/env python3
"""
Production Optimization Tests for Task 6.1.1 Phase 8.

This module tests the production optimization implementation including:
- Advanced connection pooling with resource optimization and health monitoring
- Redis-backed message queuing with persistence and scalability enhancements
- Horizontal scaling support and production readiness features
- Performance validation meeting established targets

Performance targets: >10,000 concurrent connections, >2000 messages/second, <100ms delivery under load
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from typing import Dict, Any

from app.core.websocket_connection_pool import (
    AdvancedWebSocketConnectionPool,
    PoolConfiguration,
    ConnectionState,
    PoolHealthStatus
)
from app.services.redis_message_queue_service import (
    RedisMessageQueueService,
    MessagePriority,
    QueueMessage
)
from app.services.websocket_production_optimization_service import (
    WebSocketProductionOptimizationService,
    OptimizationMode,
    ProductionMetrics
)


class TestAdvancedConnectionPool:
    """Test suite for advanced connection pool functionality."""

    @pytest.fixture
    def pool_config(self):
        """Create test pool configuration."""
        return PoolConfiguration(
            max_connections=100,
            max_connections_per_user=3,
            connection_timeout=60,
            idle_timeout=120,
            stale_connection_threshold=180,
            cleanup_interval=10,
            health_check_interval=5,
            ping_interval=10,
            max_message_queue_size=50,
            memory_threshold_mb=128,
            enable_load_balancing=True,
            enable_auto_scaling=True
        )

    @pytest.fixture
    def connection_pool(self, pool_config):
        """Create connection pool instance."""
        return AdvancedWebSocketConnectionPool(pool_config)

    @pytest.fixture
    def mock_websocket(self):
        """Create mock WebSocket connection."""
        websocket = AsyncMock()
        websocket.close = AsyncMock()
        websocket.send_json = AsyncMock()
        return websocket

    @pytest.fixture
    def mock_user(self):
        """Create mock user."""
        user = MagicMock()
        user.id = 1
        user.email = "<EMAIL>"
        return user

    @pytest.mark.asyncio
    async def test_add_connection_success(self, connection_pool, mock_websocket, mock_user):
        """Test successful connection addition to pool."""
        # Arrange
        connection_id = "test_connection_1"
        user_id = 1

        # Act
        success = await connection_pool.add_connection(connection_id, mock_websocket, user_id, mock_user)

        # Assert
        assert success is True
        assert connection_id in connection_pool.connections
        assert connection_id in connection_pool.user_connections[user_id]
        assert connection_id in connection_pool.connection_metrics
        assert connection_pool.pool_metrics["total_connections"] == 1
        assert connection_pool.pool_metrics["active_connections"] == 1

    @pytest.mark.asyncio
    async def test_connection_pool_capacity_limit(self, connection_pool, mock_websocket, mock_user):
        """Test connection pool capacity enforcement."""
        # Arrange - Fill pool to capacity with different users to avoid per-user limit
        for i in range(connection_pool.config.max_connections):
            await connection_pool.add_connection(f"conn_{i}", mock_websocket, i, mock_user)

        # Act - Try to add one more connection
        success = await connection_pool.add_connection("overflow_conn", mock_websocket, 999, mock_user)

        # Assert - Should reject when at capacity
        assert success is False
        assert "overflow_conn" not in connection_pool.connections
        assert len(connection_pool.connections) == connection_pool.config.max_connections

    @pytest.mark.asyncio
    async def test_per_user_connection_limit(self, connection_pool, mock_websocket, mock_user):
        """Test per-user connection limit enforcement."""
        # Arrange - Add connections up to user limit
        user_id = 1
        for i in range(connection_pool.config.max_connections_per_user):
            await connection_pool.add_connection(f"user_conn_{i}", mock_websocket, user_id, mock_user)

        # Act - Try to add one more connection for same user
        await connection_pool.add_connection("user_overflow", mock_websocket, user_id, mock_user)

        # Assert - Should have removed oldest connection
        assert len(connection_pool.user_connections[user_id]) == connection_pool.config.max_connections_per_user
        assert "user_overflow" in connection_pool.user_connections[user_id]

    @pytest.mark.asyncio
    async def test_load_balanced_connection_selection(self, connection_pool, mock_websocket, mock_user):
        """Test load-balanced connection selection."""
        # Arrange - Add multiple connections for user
        user_id = 1
        connection_ids = []
        for i in range(3):
            conn_id = f"lb_conn_{i}"
            await connection_pool.add_connection(conn_id, mock_websocket, user_id, mock_user)
            connection_ids.append(conn_id)

        # Act - Get load-balanced connections
        selected_connections = []
        for _ in range(6):  # More than available connections
            conn_id = await connection_pool.get_load_balanced_connection(user_id)
            selected_connections.append(conn_id)

        # Assert - Should rotate through connections
        assert len(set(selected_connections)) == 3  # All connections used
        assert selected_connections[0] != selected_connections[1]  # Different connections

    @pytest.mark.asyncio
    async def test_connection_removal(self, connection_pool, mock_websocket, mock_user):
        """Test connection removal and cleanup."""
        # Arrange
        connection_id = "test_removal"
        user_id = 1
        await connection_pool.add_connection(connection_id, mock_websocket, user_id, mock_user)

        # Act
        success = await connection_pool.remove_connection(connection_id)

        # Assert
        assert success is True
        assert connection_id not in connection_pool.connections
        assert connection_id not in connection_pool.user_connections[user_id]
        assert connection_id not in connection_pool.connection_metrics

    @pytest.mark.asyncio
    async def test_pool_health_status_determination(self, connection_pool):
        """Test pool health status determination."""
        # Test healthy status
        health_status = connection_pool._determine_health_status()
        assert health_status == PoolHealthStatus.HEALTHY

        # Test warning status - simulate high connection count by setting metrics directly
        warning_threshold = int(connection_pool.config.max_connections * 0.85)  # 85 connections

        # Manually set the metrics to simulate warning condition
        connection_pool.pool_metrics["active_connections"] = warning_threshold

        health_status = connection_pool._determine_health_status()
        assert health_status == PoolHealthStatus.WARNING

        # Test critical status
        critical_threshold = int(connection_pool.config.max_connections * 0.96)  # 96 connections
        connection_pool.pool_metrics["active_connections"] = critical_threshold

        health_status = connection_pool._determine_health_status()
        assert health_status == PoolHealthStatus.CRITICAL

    @pytest.mark.asyncio
    async def test_pool_metrics_collection(self, connection_pool, mock_websocket, mock_user):
        """Test pool metrics collection and accuracy."""
        # Arrange - Add some connections
        for i in range(5):
            await connection_pool.add_connection(f"metrics_conn_{i}", mock_websocket, i, mock_user)

        # Act
        pool_status = await connection_pool.get_pool_status()

        # Assert
        assert pool_status["metrics"]["active_connections"] == 5
        assert pool_status["health_status"] == "healthy"
        assert "configuration" in pool_status
        assert "connection_states" in pool_status


class TestRedisMessageQueue:
    """Test suite for Redis message queue functionality."""

    @pytest.fixture
    def mock_redis(self):
        """Create mock Redis client."""
        redis_mock = AsyncMock()
        redis_mock.ping = AsyncMock(return_value=True)
        redis_mock.zadd = AsyncMock(return_value=1)
        redis_mock.zpopmax = AsyncMock(return_value=[])
        redis_mock.zcard = AsyncMock(return_value=0)
        redis_mock.publish = AsyncMock(return_value=1)
        return redis_mock

    @pytest.fixture
    def message_queue(self, mock_redis):
        """Create message queue service instance."""
        queue = RedisMessageQueueService(redis_client=mock_redis)
        return queue

    @pytest.mark.asyncio
    async def test_message_enqueue_success(self, message_queue):
        """Test successful message enqueuing."""
        # Arrange
        content = {"type": "test_message", "data": "test_data"}
        priority = MessagePriority.HIGH

        # Act
        message_id = await message_queue.enqueue_message(
            content=content,
            priority=priority,
            correlation_id="test_correlation"
        )

        # Assert
        assert message_id is not None
        assert len(message_id) > 0
        message_queue.redis.zadd.assert_called_once()

    @pytest.mark.asyncio
    async def test_message_priority_scoring(self, message_queue):
        """Test message priority scoring system."""
        # Test different priority scores
        immediate_score = message_queue._get_priority_score(MessagePriority.IMMEDIATE)
        high_score = message_queue._get_priority_score(MessagePriority.HIGH)
        standard_score = message_queue._get_priority_score(MessagePriority.STANDARD)
        low_score = message_queue._get_priority_score(MessagePriority.LOW)

        # Assert priority ordering
        assert immediate_score > high_score
        assert high_score > standard_score
        assert standard_score > low_score

    @pytest.mark.asyncio
    async def test_message_serialization_compression(self, message_queue):
        """Test message serialization and compression."""
        # Arrange - Large message that should be compressed
        large_content = {"data": "x" * 2000}  # Larger than compression threshold
        message = QueueMessage(
            id="test_id",
            content=large_content,
            priority=MessagePriority.STANDARD,
            created_at=datetime.now(timezone.utc)
        )

        # Act
        serialized = await message_queue._serialize_message(message)
        deserialized = await message_queue._deserialize_message(serialized)

        # Assert
        assert serialized.startswith(b'COMPRESSED:')  # Should be compressed
        assert deserialized.content == large_content
        assert deserialized.priority == MessagePriority.STANDARD

    @pytest.mark.asyncio
    async def test_partition_selection_consistency(self, message_queue):
        """Test consistent partition selection for same message ID."""
        # Test same message ID always goes to same partition
        message_id = "test_message_123"
        partition1 = message_queue._select_partition(message_id)
        partition2 = message_queue._select_partition(message_id)

        assert partition1 == partition2
        assert 0 <= partition1 < message_queue.partition_count

    @pytest.mark.asyncio
    async def test_queue_status_reporting(self, message_queue):
        """Test queue status and metrics reporting."""
        # Act
        status = await message_queue.get_queue_status()

        # Assert
        assert "queues" in status
        assert "configuration" in status
        assert "processing_stats" in status
        assert status["configuration"]["max_queue_size"] > 0


class TestProductionOptimizationService:
    """Test suite for production optimization service."""

    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        db_mock = AsyncMock()
        db_mock.execute = AsyncMock()
        return db_mock

    @pytest.fixture
    def mock_cache_manager(self):
        """Create mock cache manager."""
        cache_mock = MagicMock()
        cache_mock.redis = AsyncMock()
        cache_mock.redis.ping = AsyncMock(return_value=True)
        return cache_mock

    @pytest.fixture
    def optimization_service_factory(self, mock_db, mock_cache_manager):
        """Create production optimization service factory."""
        async def _create_service():
            service = WebSocketProductionOptimizationService(
                db=mock_db,
                cache_manager=mock_cache_manager,
                optimization_mode=OptimizationMode.DEVELOPMENT
            )
            await service.initialize()
            return service
        return _create_service

    @pytest.mark.asyncio
    async def test_service_initialization(self, optimization_service_factory):
        """Test service initialization and component setup."""
        # Arrange
        optimization_service = await optimization_service_factory()

        # Assert
        assert optimization_service.connection_pool is not None
        assert optimization_service.message_queue is not None
        assert optimization_service.optimization_mode == OptimizationMode.DEVELOPMENT
        assert optimization_service.instance_id is not None

    @pytest.mark.asyncio
    async def test_production_status_reporting(self, optimization_service_factory):
        """Test production status and metrics reporting."""
        # Arrange
        optimization_service = await optimization_service_factory()

        # Act
        status = await optimization_service.get_production_status()

        # Assert
        assert "service_info" in status
        assert "performance_metrics" in status
        assert "connection_pool" in status
        assert "message_queue" in status
        assert "health_status" in status
        assert status["service_info"]["optimization_mode"] == "development"

    @pytest.mark.asyncio
    async def test_health_check_comprehensive(self, optimization_service_factory):
        """Test comprehensive health check functionality."""
        # Arrange
        optimization_service = await optimization_service_factory()

        # Act
        health_results = await optimization_service.perform_health_check()

        # Assert
        assert "timestamp" in health_results
        assert "overall_status" in health_results
        assert "checks" in health_results
        assert "connection_pool" in health_results["checks"]
        assert "message_queue" in health_results["checks"]
        assert "redis" in health_results["checks"]
        assert "database" in health_results["checks"]

    @pytest.mark.asyncio
    async def test_high_traffic_optimization(self, optimization_service_factory):
        """Test high-traffic optimization configuration."""
        # Arrange
        optimization_service = await optimization_service_factory()
        initial_max_connections = optimization_service.connection_pool.config.max_connections

        # Act
        await optimization_service.optimize_for_high_traffic()

        # Assert
        assert optimization_service.connection_pool.config.max_connections > initial_max_connections
        assert optimization_service.scaling_config["auto_scaling_enabled"] is True
        assert optimization_service.scaling_config["scale_up_threshold"] < 0.8

    @pytest.mark.asyncio
    async def test_performance_report_generation(self, optimization_service_factory):
        """Test performance report generation."""
        # Arrange
        optimization_service = await optimization_service_factory()

        # Act
        report = await optimization_service.get_performance_report()

        # Assert
        assert "report_timestamp" in report
        assert "service_uptime" in report
        assert "performance_summary" in report
        assert "queue_performance" in report
        assert "optimization_recommendations" in report
        assert isinstance(report["optimization_recommendations"], list)

    @pytest.mark.asyncio
    async def test_graceful_shutdown(self, optimization_service_factory):
        """Test graceful shutdown process."""
        # Arrange
        optimization_service = await optimization_service_factory()

        # Act
        await optimization_service.graceful_shutdown(timeout=5)

        # Assert - Should complete without errors
        # Background tasks should be cancelled
        assert len(optimization_service._background_tasks) > 0  # Tasks were created
        # Note: In real implementation, tasks would be cancelled


class TestPerformanceTargets:
    """Test suite for validating performance targets."""

    @pytest.mark.asyncio
    async def test_connection_establishment_performance(self):
        """Test connection establishment meets <100ms target."""
        # Arrange
        pool = AdvancedWebSocketConnectionPool()
        mock_websocket = AsyncMock()
        mock_user = MagicMock()
        mock_user.id = 1

        # Act - Measure connection establishment time
        start_time = time.time()
        success = await pool.add_connection("perf_test", mock_websocket, 1, mock_user)
        establishment_time = (time.time() - start_time) * 1000  # Convert to ms

        # Assert
        assert success is True
        assert establishment_time < 100  # Less than 100ms

    @pytest.mark.asyncio
    async def test_message_processing_performance(self):
        """Test message processing meets performance targets."""
        # Arrange
        mock_redis = AsyncMock()
        mock_redis.ping = AsyncMock(return_value=True)
        mock_redis.zadd = AsyncMock(return_value=1)

        queue = RedisMessageQueueService(redis_client=mock_redis)

        # Act - Measure message enqueue time
        start_time = time.time()
        message_id = await queue.enqueue_message(
            content={"test": "data"},
            priority=MessagePriority.IMMEDIATE
        )
        processing_time = (time.time() - start_time) * 1000  # Convert to ms

        # Assert
        assert message_id is not None
        assert processing_time < 10  # Less than 10ms for immediate priority

    @pytest.mark.asyncio
    async def test_health_check_response_time(self):
        """Test health check meets <100ms response target."""
        # Arrange
        mock_db = AsyncMock()
        mock_db.execute = AsyncMock()
        mock_cache = MagicMock()
        mock_cache.redis = AsyncMock()
        mock_cache.redis.ping = AsyncMock(return_value=True)

        service = WebSocketProductionOptimizationService(
            db=mock_db,
            cache_manager=mock_cache,
            optimization_mode=OptimizationMode.DEVELOPMENT
        )
        await service.initialize()

        # Act - Measure health check time
        start_time = time.time()
        health_results = await service.perform_health_check()
        check_time = (time.time() - start_time) * 1000  # Convert to ms

        # Assert
        assert health_results is not None
        assert check_time < 100  # Less than 100ms
