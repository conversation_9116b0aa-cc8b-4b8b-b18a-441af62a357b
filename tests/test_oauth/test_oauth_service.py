"""
Tests for OAuth service functionality.

This module tests OAuth authentication service including provider management,
authentication flows, token management, and user account linking.

Tests Task 2.1.3 OAuth2 integration implementation.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Exception

from app.services.oauth_service import OAuthService
from app.models.oauth_models import OAuthProvider, OAuthAccount, OAuthToken, OAuthState
from app.models.user import User
from app.schemas.oauth_schemas import OAuthUserProfile, OAuthAuthorizationResponse
from app.core.security import UserRole


class TestOAuthService:
    """Test OAuth service functionality."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        db = AsyncMock()
        db.commit = AsyncMock()
        db.rollback = AsyncMock()
        db.refresh = AsyncMock()
        db.add = MagicMock()
        return db
    
    @pytest.fixture
    def oauth_service(self, mock_db):
        """OAuth service instance with mocked dependencies."""
        service = OAuthService(mock_db)
        service.provider_repo = AsyncMock()
        service.account_repo = AsyncMock()
        service.token_repo = AsyncMock()
        service.state_repo = AsyncMock()
        service.user_repo = AsyncMock()
        return service
    
    @pytest.fixture
    def sample_oauth_provider(self):
        """Sample OAuth provider for testing."""
        return OAuthProvider(
            id=1,
            name="google",
            display_name="Google",
            client_id="test_client_id",
            client_secret_encrypted="encrypted_secret",
            authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
            token_url="https://oauth2.googleapis.com/token",
            user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
            scopes=["openid", "email", "profile"],
            is_active=True
        )
    
    @pytest.fixture
    def sample_user(self):
        """Sample user for testing."""
        return User(
            id=1,
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="Test",
            last_name="User",
            role=UserRole.CUSTOMER,
            is_active=True,
            is_verified=True
        )
    
    @pytest.fixture
    def sample_oauth_account(self, sample_user, sample_oauth_provider):
        """Sample OAuth account for testing."""
        return OAuthAccount(
            id=1,
            user_id=sample_user.id,
            provider_id=sample_oauth_provider.id,
            provider_user_id="google_user_123",
            provider_username="testuser",
            provider_email="<EMAIL>",
            profile_data={"name": "Test User"},
            is_active=True,
            is_verified=True,
            first_connected_at=datetime.utcnow(),
            last_connected_at=datetime.utcnow(),
            user=sample_user,
            provider=sample_oauth_provider
        )
    
    @pytest.fixture
    def sample_oauth_state(self, sample_oauth_provider):
        """Sample OAuth state for testing."""
        return OAuthState(
            id=1,
            state_token="test_state_token",
            provider_id=sample_oauth_provider.id,
            redirect_uri="http://localhost:3000/auth/callback",
            scopes=["openid", "email", "profile"],
            client_ip="127.0.0.1",
            user_agent="Test User Agent",
            is_used=False,
            expires_at=datetime.utcnow() + timedelta(minutes=10),
            provider=sample_oauth_provider
        )
    
    @pytest.fixture
    def sample_user_profile(self):
        """Sample OAuth user profile for testing."""
        return OAuthUserProfile(
            id="google_user_123",
            email="<EMAIL>",
            name="Test User",
            first_name="Test",
            last_name="User",
            picture="https://example.com/avatar.jpg",
            verified_email=True,
            locale="en",
            provider_data={
                "id": "google_user_123",
                "email": "<EMAIL>",
                "name": "Test User",
                "given_name": "Test",
                "family_name": "User"
            }
        )
    
    async def test_get_provider_by_name_success(self, oauth_service, sample_oauth_provider):
        """Test successful OAuth provider retrieval by name."""
        # Arrange
        oauth_service.provider_repo.get_by_name.return_value = sample_oauth_provider
        
        # Act
        result = await oauth_service.get_provider_by_name("google")
        
        # Assert
        assert result == sample_oauth_provider
        oauth_service.provider_repo.get_by_name.assert_called_once_with("google")
    
    async def test_get_provider_by_name_not_found(self, oauth_service):
        """Test OAuth provider not found."""
        # Arrange
        oauth_service.provider_repo.get_by_name.return_value = None
        
        # Act
        result = await oauth_service.get_provider_by_name("nonexistent")
        
        # Assert
        assert result is None
        oauth_service.provider_repo.get_by_name.assert_called_once_with("nonexistent")
    
    async def test_get_active_providers_success(self, oauth_service, sample_oauth_provider):
        """Test successful retrieval of active OAuth providers."""
        # Arrange
        oauth_service.provider_repo.get_active_providers.return_value = [sample_oauth_provider]
        
        # Act
        result = await oauth_service.get_active_providers()
        
        # Assert
        assert result == [sample_oauth_provider]
        oauth_service.provider_repo.get_active_providers.assert_called_once()
    
    async def test_create_authorization_url_success(
        self, 
        oauth_service, 
        sample_oauth_provider, 
        sample_oauth_state
    ):
        """Test successful OAuth authorization URL creation."""
        # Arrange
        oauth_service.provider_repo.get_by_name.return_value = sample_oauth_provider
        oauth_service.state_repo.create_state.return_value = sample_oauth_state
        
        # Act
        result = await oauth_service.create_authorization_url(
            provider_name="google",
            redirect_uri="http://localhost:3000/auth/callback",
            scopes=["openid", "email", "profile"],
            client_ip="127.0.0.1",
            user_agent="Test User Agent"
        )
        
        # Assert
        assert isinstance(result, OAuthAuthorizationResponse)
        assert "accounts.google.com" in result.authorization_url
        assert result.state == sample_oauth_state.state_token
        assert result.expires_in == 600
        
        oauth_service.provider_repo.get_by_name.assert_called_once_with("google")
        oauth_service.state_repo.create_state.assert_called_once()
    
    async def test_create_authorization_url_provider_not_found(self, oauth_service):
        """Test OAuth authorization URL creation with provider not found."""
        # Arrange
        oauth_service.provider_repo.get_by_name.return_value = None
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await oauth_service.create_authorization_url(
                provider_name="nonexistent",
                redirect_uri="http://localhost:3000/auth/callback"
            )
        
        assert exc_info.value.status_code == 404
        assert "not found or inactive" in exc_info.value.detail
    
    @patch('app.services.oauth_service.httpx.AsyncClient')
    async def test_exchange_code_for_tokens_success(
        self, 
        mock_client, 
        oauth_service, 
        sample_oauth_provider
    ):
        """Test successful OAuth code exchange for tokens."""
        # Arrange
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Act
        result = await oauth_service._exchange_code_for_tokens(
            provider=sample_oauth_provider,
            code="test_auth_code",
            redirect_uri="http://localhost:3000/auth/callback"
        )
        
        # Assert
        assert result["access_token"] == "test_access_token"
        assert result["refresh_token"] == "test_refresh_token"
        assert result["token_type"] == "Bearer"
        assert result["expires_in"] == 3600
    
    @patch('app.services.oauth_service.httpx.AsyncClient')
    async def test_get_user_profile_success(
        self, 
        mock_client, 
        oauth_service, 
        sample_oauth_provider
    ):
        """Test successful OAuth user profile retrieval."""
        # Arrange
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "google_user_123",
            "email": "<EMAIL>",
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User",
            "picture": "https://example.com/avatar.jpg",
            "email_verified": True,
            "locale": "en"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Act
        result = await oauth_service._get_user_profile(
            provider=sample_oauth_provider,
            access_token="test_access_token"
        )
        
        # Assert
        assert result.id == "google_user_123"
        assert result.email == "<EMAIL>"
        assert result.name == "Test User"
        assert result.first_name == "Test"
        assert result.last_name == "User"
        assert result.verified_email is True
    
    def test_normalize_profile_data_google(self, oauth_service):
        """Test Google profile data normalization."""
        # Arrange
        google_profile = {
            "id": "google_user_123",
            "email": "<EMAIL>",
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User",
            "picture": "https://example.com/avatar.jpg",
            "email_verified": True,
            "locale": "en"
        }
        
        # Act
        result = oauth_service._normalize_profile_data("google", google_profile)
        
        # Assert
        assert result["id"] == "google_user_123"
        assert result["email"] == "<EMAIL>"
        assert result["name"] == "Test User"
        assert result["first_name"] == "Test"
        assert result["last_name"] == "User"
        assert result["picture"] == "https://example.com/avatar.jpg"
        assert result["verified_email"] is True
        assert result["locale"] == "en"
        assert result["provider_data"] == google_profile
    
    def test_normalize_profile_data_facebook(self, oauth_service):
        """Test Facebook profile data normalization."""
        # Arrange
        facebook_profile = {
            "id": "facebook_user_123",
            "email": "<EMAIL>",
            "name": "Test User",
            "first_name": "Test",
            "last_name": "User",
            "picture": {
                "data": {
                    "url": "https://example.com/avatar.jpg"
                }
            },
            "locale": "en_US"
        }
        
        # Act
        result = oauth_service._normalize_profile_data("facebook", facebook_profile)
        
        # Assert
        assert result["id"] == "facebook_user_123"
        assert result["email"] == "<EMAIL>"
        assert result["name"] == "Test User"
        assert result["first_name"] == "Test"
        assert result["last_name"] == "User"
        assert result["picture"] == "https://example.com/avatar.jpg"
        assert result["locale"] == "en_US"
        assert result["provider_data"] == facebook_profile
    
    def test_get_user_scopes(self, oauth_service):
        """Test user scope generation based on role."""
        # Test customer role
        customer_scopes = oauth_service._get_user_scopes("customer")
        assert "read:profile" in customer_scopes
        assert "update:profile" in customer_scopes
        assert "admin:users" not in customer_scopes
        
        # Test admin role
        admin_scopes = oauth_service._get_user_scopes("admin")
        assert "read:profile" in admin_scopes
        assert "update:profile" in admin_scopes
        assert "admin:users" in admin_scopes
        assert "admin:system" in admin_scopes
        
        # Test vendor role
        vendor_scopes = oauth_service._get_user_scopes("vendor")
        assert "read:profile" in vendor_scopes
        assert "update:profile" in vendor_scopes
        assert "manage:services" in vendor_scopes
        assert "view:analytics" in vendor_scopes
    
    async def test_get_user_oauth_accounts_success(
        self, 
        oauth_service, 
        sample_oauth_account
    ):
        """Test successful retrieval of user OAuth accounts."""
        # Arrange
        oauth_service.account_repo.get_by_user_id.return_value = [sample_oauth_account]
        
        # Act
        result = await oauth_service.get_user_oauth_accounts(1)
        
        # Assert
        assert result == [sample_oauth_account]
        oauth_service.account_repo.get_by_user_id.assert_called_once_with(1)
    
    async def test_unlink_oauth_account_success(
        self, 
        oauth_service, 
        sample_oauth_account
    ):
        """Test successful OAuth account unlinking."""
        # Arrange
        oauth_service.account_repo.get.return_value = sample_oauth_account
        oauth_service.account_repo.deactivate_account.return_value = True
        oauth_service.token_repo.get_active_token.return_value = None
        
        # Act
        result = await oauth_service.unlink_oauth_account(1, 1)
        
        # Assert
        assert result is True
        oauth_service.account_repo.get.assert_called_once_with(1)
        oauth_service.account_repo.deactivate_account.assert_called_once_with(1)
    
    async def test_unlink_oauth_account_not_found(self, oauth_service):
        """Test OAuth account unlinking with account not found."""
        # Arrange
        oauth_service.account_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await oauth_service.unlink_oauth_account(1, 1)
        
        assert exc_info.value.status_code == 404
        assert "not found" in exc_info.value.detail
    
    async def test_unlink_oauth_account_wrong_user(
        self, 
        oauth_service, 
        sample_oauth_account
    ):
        """Test OAuth account unlinking with wrong user."""
        # Arrange
        sample_oauth_account.user_id = 2  # Different user
        oauth_service.account_repo.get.return_value = sample_oauth_account
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await oauth_service.unlink_oauth_account(1, 1)
        
        assert exc_info.value.status_code == 403
        assert "does not belong to user" in exc_info.value.detail
    
    async def test_cleanup_expired_data_success(self, oauth_service):
        """Test successful cleanup of expired OAuth data."""
        # Arrange
        oauth_service.token_repo.cleanup_expired_tokens.return_value = 5
        oauth_service.state_repo.cleanup_expired_states.return_value = 3
        
        # Act
        result = await oauth_service.cleanup_expired_data()
        
        # Assert
        assert result["expired_tokens"] == 5
        assert result["expired_states"] == 3
        oauth_service.token_repo.cleanup_expired_tokens.assert_called_once()
        oauth_service.state_repo.cleanup_expired_states.assert_called_once()
