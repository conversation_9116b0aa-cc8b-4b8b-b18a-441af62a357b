"""
Comprehensive tests for Analytics Service Layer - Phase 7.1.

This test suite validates the analytics service implementations according to
the requirements in Phase 7.1 Analytics & Performance System.

Test Coverage:
- AnalyticsService business logic and validation
- DashboardService widget management and real-time data
- ReportingService KPI calculation and business intelligence
- Service layer error handling and circuit breaker patterns
- Performance optimization validation (<500ms creation, <200ms queries)
- Transaction management and rollback capabilities

Target: >85% code coverage for all analytics service components.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.analytics_service import AnalyticsService
from app.services.dashboard_service import DashboardService
from app.services.reporting_service import ReportingService
from app.models.analytics_models import AnalyticsTimeframe, MetricType
from app.schemas.analytics_schemas import (
    UserAnalyticsCreate, VendorAnalyticsCreate,
    DashboardWidgetCreate, SystemMetricsCreate,
    UserAnalyticsResponse
)


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


class TestAnalyticsService:
    """Test suite for AnalyticsService."""

    @pytest.mark.asyncio
    async def test_create_user_analytics_success(
        self,
        mock_async_session,
        mock_analytics_repository,
        sample_user_analytics_data,
        user_analytics_factory
    ):
        """Test successful user analytics creation with business logic validation."""
        # Arrange
        service = AnalyticsService(mock_async_session)

        user_id = sample_user_analytics_data["user_id"]
        timeframe = AnalyticsTimeframe.DAILY
        period_start = datetime.fromisoformat(sample_user_analytics_data["period_start"].replace('Z', '+00:00'))
        period_end = datetime.fromisoformat(sample_user_analytics_data["period_end"].replace('Z', '+00:00'))
        analytics_data = {k: v for k, v in sample_user_analytics_data.items()
                         if k not in ['user_id', 'timeframe', 'period_start', 'period_end']}

        expected_analytics = user_analytics_factory()
        mock_analytics_repository.create_user_analytics.return_value = expected_analytics

        # Act
        with patch('app.services.analytics_service.AnalyticsRepository', return_value=mock_analytics_repository):
            result = await service.create_user_analytics(
                user_id=user_id,
                timeframe=timeframe,
                period_start=period_start,
                period_end=period_end,
                analytics_data=analytics_data
            )

        # Assert
        assert isinstance(result, UserAnalyticsResponse)
        assert result.uuid == expected_analytics.uuid
        assert result.user_id == expected_analytics.user_id
        assert result.timeframe == expected_analytics.timeframe
        assert result.session_count == expected_analytics.session_count
        assert result.total_spent == expected_analytics.total_spent
        mock_analytics_repository.create_user_analytics.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_user_analytics_validation_error(
        self,
        mock_async_session,
        mock_analytics_repository
    ):
        """Test user analytics creation with validation error."""
        # Arrange
        service = AnalyticsService(mock_async_session)
        service.user_analytics_repo = mock_analytics_repository

        # Invalid data - period_end before period_start
        user_id = 1
        timeframe = AnalyticsTimeframe.DAILY
        period_start = datetime.now(timezone.utc)
        period_end = datetime.now(timezone.utc) - timedelta(days=1)  # Invalid
        analytics_data = {}

        # Act & Assert
        with pytest.raises(ValueError, match="period_end must be after period_start"):
            await service.create_user_analytics(
                user_id=user_id,
                timeframe=timeframe,
                period_start=period_start,
                period_end=period_end,
                analytics_data=analytics_data
            )

    @pytest.mark.asyncio
    async def test_get_user_analytics_aggregated_success(
        self,
        mock_async_session,
        mock_analytics_repository,
        analytics_time_series_data
    ):
        """Test successful user analytics aggregation with trend analysis."""
        # Arrange
        service = AnalyticsService(mock_async_session)
        service.user_analytics_repo = mock_analytics_repository

        user_id = 1
        timeframe = AnalyticsTimeframe.DAILY
        date_range = (
            datetime.now(timezone.utc) - timedelta(days=7),
            datetime.now(timezone.utc)
        )

        # Mock aggregated data
        mock_aggregated_data = {
            "aggregated_data": analytics_time_series_data["user_analytics_series"],
            "summary_metrics": {
                "total_page_views": 175,
                "average_engagement_score": 75.5,
                "total_revenue": 1250.0
            },
            "trend_analysis": {
                "engagement_trend": "increasing",
                "revenue_trend": "stable"
            }
        }
        mock_analytics_repository.get_user_analytics_aggregated.return_value = mock_aggregated_data

        # Act
        result = await service.get_user_analytics_aggregated(
            user_id=user_id,
            timeframe=timeframe,
            date_range=date_range,
            include_trends=True
        )

        # Assert
        assert "aggregated_data" in result
        assert "summary_metrics" in result
        assert "trend_analysis" in result
        mock_analytics_repository.get_user_analytics_aggregated.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_vendor_analytics_success(
        self,
        mock_async_session,
        mock_analytics_repository,
        sample_vendor_analytics_data,
        vendor_analytics_factory
    ):
        """Test successful vendor analytics creation with performance metrics calculation."""
        # Arrange
        service = AnalyticsService(mock_async_session)
        service.vendor_analytics_repo = mock_analytics_repository

        vendor_id = sample_vendor_analytics_data["vendor_id"]
        timeframe = AnalyticsTimeframe.DAILY
        period_start = datetime.fromisoformat(sample_vendor_analytics_data["period_start"].replace('Z', '+00:00'))
        period_end = datetime.fromisoformat(sample_vendor_analytics_data["period_end"].replace('Z', '+00:00'))
        analytics_data = {k: v for k, v in sample_vendor_analytics_data.items()
                         if k not in ['vendor_id', 'timeframe', 'period_start', 'period_end']}

        expected_analytics = vendor_analytics_factory()
        mock_analytics_repository.create_vendor_analytics.return_value = expected_analytics

        # Act
        result = await service.create_vendor_analytics(
            vendor_id=vendor_id,
            timeframe=timeframe,
            period_start=period_start,
            period_end=period_end,
            analytics_data=analytics_data
        )

        # Assert
        assert result == expected_analytics
        mock_analytics_repository.create_vendor_analytics.assert_called_once()

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_analytics_service_performance(
        self,
        mock_async_session,
        mock_analytics_repository,
        performance_test_data
    ):
        """Test analytics service performance targets (<500ms creation, <200ms queries)."""
        # Arrange
        service = AnalyticsService(mock_async_session)
        service.user_analytics_repo = mock_analytics_repository

        # Mock fast responses
        mock_analytics_repository.get_user_analytics_aggregated.return_value = {
            "aggregated_data": [],
            "summary_metrics": {},
            "trend_analysis": {}
        }

        # Act & Assert - Query performance
        start_time = datetime.now()
        await service.get_user_analytics_aggregated(
            user_id=1,
            timeframe=AnalyticsTimeframe.DAILY,
            date_range=(datetime.now(timezone.utc) - timedelta(days=7), datetime.now(timezone.utc)),
            include_trends=True
        )
        end_time = datetime.now()

        # Performance assertion (mock should be very fast)
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        assert execution_time_ms < performance_test_data["query_timeout_ms"]


class TestDashboardService:
    """Test suite for DashboardService."""

    @pytest.mark.asyncio
    async def test_create_dashboard_widget_success(
        self,
        mock_async_session,
        mock_dashboard_repository,
        sample_dashboard_widget_data,
        dashboard_widget_factory
    ):
        """Test successful dashboard widget creation with configuration validation."""
        # Arrange
        service = DashboardService(mock_async_session)
        service.dashboard_widget_repo = mock_dashboard_repository

        widget_data = DashboardWidgetCreate(**sample_dashboard_widget_data)
        expected_widget = dashboard_widget_factory()
        mock_dashboard_repository.create_dashboard_widget.return_value = expected_widget

        # Act
        result = await service.create_dashboard_widget(
            widget_data=widget_data,
            dashboard_id=None
        )

        # Assert
        assert result == expected_widget
        mock_dashboard_repository.create_dashboard_widget.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_dashboard_widget_validation_error(
        self,
        mock_async_session,
        mock_dashboard_repository
    ):
        """Test dashboard widget creation with validation error."""
        # Arrange
        service = DashboardService(mock_async_session)
        service.dashboard_widget_repo = mock_dashboard_repository

        # Invalid widget data - missing required fields
        invalid_widget_data = DashboardWidgetCreate(
            name="",  # Invalid empty name
            widget_type="line_chart",
            data_source="booking_analytics",
            configuration={}
        )

        # Act & Assert
        with pytest.raises(ValueError, match="Widget name cannot be empty"):
            await service.create_dashboard_widget(
                widget_data=invalid_widget_data,
                dashboard_id=None
            )

    @pytest.mark.asyncio
    async def test_record_system_metric_success(
        self,
        mock_async_session,
        mock_dashboard_repository,
        system_metrics_factory
    ):
        """Test successful system metric recording with circuit breaker protection."""
        # Arrange
        service = DashboardService(mock_async_session)
        service.system_metrics_repo = mock_dashboard_repository

        metric_name = "api_response_time"
        metric_type = MetricType.TIMER
        value = Decimal("125.500000")
        timeframe = AnalyticsTimeframe.HOURLY
        tags = {"endpoint": "/api/v1/bookings"}
        metadata = {"server_id": "web-01"}

        expected_metric = system_metrics_factory()
        mock_dashboard_repository.record_system_metric.return_value = expected_metric

        # Act
        result = await service.record_system_metric(
            metric_name=metric_name,
            metric_type=metric_type,
            value=value,
            timeframe=timeframe,
            tags=tags,
            metadata=metadata
        )

        # Assert
        assert result == expected_metric
        mock_dashboard_repository.record_system_metric.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_widget_position_success(
        self,
        mock_async_session,
        mock_dashboard_repository,
        dashboard_widget_factory
    ):
        """Test successful widget position update with grid validation."""
        # Arrange
        service = DashboardService(mock_async_session)
        service.dashboard_widget_repo = mock_dashboard_repository

        widget_id = uuid4()
        position_x = 2
        position_y = 1
        width = 4
        height = 3

        expected_widget = dashboard_widget_factory(
            position_x=position_x,
            position_y=position_y,
            width=width,
            height=height
        )
        mock_dashboard_repository.get_dashboard_widget_by_id.return_value = dashboard_widget_factory()
        mock_dashboard_repository.update_dashboard_widget.return_value = expected_widget

        # Act
        result = await service.update_widget_position(
            widget_id=widget_id,
            position_x=position_x,
            position_y=position_y,
            width=width,
            height=height
        )

        # Assert
        assert result == expected_widget
        assert result.position_x == position_x
        assert result.position_y == position_y
        assert result.width == width
        assert result.height == height
        mock_dashboard_repository.update_dashboard_widget.assert_called_once()


class TestReportingService:
    """Test suite for ReportingService."""

    @pytest.mark.asyncio
    async def test_generate_kpi_report_success(
        self,
        mock_async_session,
        mock_reporting_service
    ):
        """Test successful KPI report generation with business intelligence metrics."""
        # Arrange
        service = ReportingService(mock_async_session)

        kpi_category = "performance"
        timeframe = AnalyticsTimeframe.DAILY
        date_range = (
            datetime.now(timezone.utc) - timedelta(days=30),
            datetime.now(timezone.utc)
        )

        # Mock KPI report data
        expected_report = {
            "report_metadata": {
                "category": kpi_category,
                "timeframe": timeframe.value,
                "generated_at": datetime.now(timezone.utc).isoformat()
            },
            "kpi_results": [
                {
                    "name": "Conversion Rate",
                    "value": 8.5,
                    "target": 10.0,
                    "status": "warning",
                    "trend": "stable"
                }
            ],
            "summary_metrics": {
                "total_kpis": 5,
                "healthy_kpis": 3,
                "warning_kpis": 1,
                "critical_kpis": 1,
                "health_score": 75.0
            }
        }

        # Act
        with patch.object(service, '_get_kpi_definitions_by_category', return_value=[]):
            with patch.object(service, '_calculate_kpi_values', return_value=expected_report["kpi_results"]):
                result = await service.generate_kpi_report(
                    kpi_category=kpi_category,
                    timeframe=timeframe,
                    date_range=date_range,
                    include_trends=True,
                    include_forecasts=False
                )

        # Assert
        assert "report_metadata" in result
        assert "kpi_results" in result
        assert "summary_metrics" in result
        assert result["report_metadata"]["category"] == kpi_category

    @pytest.mark.asyncio
    async def test_generate_conversion_funnel_report_success(
        self,
        mock_async_session
    ):
        """Test successful conversion funnel report generation with segment breakdown."""
        # Arrange
        service = ReportingService(mock_async_session)

        timeframe = AnalyticsTimeframe.DAILY
        date_range = (
            datetime.now(timezone.utc) - timedelta(days=30),
            datetime.now(timezone.utc)
        )

        # Mock funnel data
        expected_funnel = {
            "funnel_steps": {
                "views": 25000,
                "interests": 5000,
                "considerations": 1500,
                "completions": 1200
            },
            "conversion_rates": {
                "view_to_interest": 20.0,
                "interest_to_consideration": 30.0,
                "consideration_to_completion": 80.0,
                "overall_conversion": 4.8
            },
            "revenue_metrics": {
                "completed_booking_value": 125000.0,
                "average_order_value": 104.17
            }
        }

        # Act
        with patch.object(service, '_get_booking_analytics_repository') as mock_repo:
            mock_repo.return_value.get_conversion_funnel_analytics.return_value = expected_funnel
            result = await service.generate_conversion_funnel_report(
                timeframe=timeframe,
                date_range=date_range,
                include_segments=True
            )

        # Assert
        assert "funnel_steps" in result
        assert "conversion_rates" in result
        assert "revenue_metrics" in result
        assert result["conversion_rates"]["overall_conversion"] == 4.8

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_reporting_service_performance(
        self,
        mock_async_session,
        performance_test_data
    ):
        """Test reporting service performance targets (<500ms report generation)."""
        # Arrange
        service = ReportingService(mock_async_session)

        timeframe = AnalyticsTimeframe.DAILY
        date_range = (
            datetime.now(timezone.utc) - timedelta(days=7),
            datetime.now(timezone.utc)
        )

        # Act & Assert - Report generation performance
        start_time = datetime.now()

        with patch.object(service, '_get_kpi_definitions_by_category', return_value=[]):
            with patch.object(service, '_calculate_kpi_values', return_value=[]):
                await service.generate_kpi_report(
                    kpi_category="performance",
                    timeframe=timeframe,
                    date_range=date_range,
                    include_trends=False,
                    include_forecasts=False
                )

        end_time = datetime.now()

        # Performance assertion (mock should be very fast)
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        assert execution_time_ms < performance_test_data["creation_timeout_ms"]
