"""
Comprehensive tests for Analytics API Endpoints - Phase 7.1.

This test suite validates the analytics API endpoint implementations according to
the requirements in Phase 7.1 Analytics & Performance System.

Test Coverage:
- Analytics endpoints with authentication and authorization
- Dashboard endpoints with real-time data and widget management
- Reporting endpoints with KPI generation and business intelligence
- API performance validation (<200ms GET, <500ms POST/PUT)
- Error handling and structured responses with correlation IDs
- Input validation using Pydantic schemas

Target: >85% code coverage for all analytics API endpoints.
"""

import pytest
import json
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.models.analytics_models import AnalyticsTimeframe, MetricType


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


class TestAnalyticsEndpoints:
    """Test suite for Analytics API endpoints."""

    @pytest.mark.asyncio
    async def test_create_user_analytics_success(
        self,
        async_test_client: AsyncClient,
        sample_user_analytics_data,
        analytics_user_auth_headers,
        user_analytics_factory
    ):
        """Test successful user analytics creation via API."""
        # Arrange
        expected_analytics = user_analytics_factory()
        
        with patch('app.api.v1.endpoints.analytics.AnalyticsService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.create_user_analytics.return_value = expected_analytics
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.post(
                "/api/v1/analytics/user-analytics",
                json=sample_user_analytics_data,
                headers=analytics_user_auth_headers
            )

        # Assert
        assert response.status_code == 201
        response_data = response.json()
        assert "uuid" in response_data
        assert response_data["user_id"] == sample_user_analytics_data["user_id"]
        mock_service.create_user_analytics.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_user_analytics_validation_error(
        self,
        async_test_client: AsyncClient,
        analytics_user_auth_headers
    ):
        """Test user analytics creation with validation error."""
        # Arrange
        invalid_data = {
            "user_id": "invalid",  # Should be integer
            "timeframe": "INVALID_TIMEFRAME",
            "period_start": "invalid_date",
            "period_end": "invalid_date"
        }

        # Act
        response = await async_test_client.post(
            "/api/v1/analytics/user-analytics",
            json=invalid_data,
            headers=analytics_user_auth_headers
        )

        # Assert
        assert response.status_code == 422  # Validation error
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_get_user_analytics_aggregated_success(
        self,
        async_test_client: AsyncClient,
        analytics_user_auth_headers,
        analytics_time_series_data
    ):
        """Test successful user analytics aggregated retrieval."""
        # Arrange
        user_id = uuid4()
        mock_aggregated_data = {
            "aggregated_data": analytics_time_series_data["user_analytics_series"],
            "summary_metrics": {
                "total_page_views": 175,
                "average_engagement_score": 75.5,
                "total_revenue": 1250.0
            },
            "trend_analysis": {
                "engagement_trend": "increasing",
                "revenue_trend": "stable"
            }
        }
        
        with patch('app.api.v1.endpoints.analytics.AnalyticsService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.get_user_analytics_aggregated.return_value = mock_aggregated_data
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.get(
                f"/api/v1/analytics/user-analytics/{user_id}/aggregated",
                params={
                    "timeframe": "DAILY",
                    "include_trends": True
                },
                headers=analytics_user_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "aggregated_data" in response_data
        assert "summary_metrics" in response_data
        assert "trend_analysis" in response_data
        mock_service.get_user_analytics_aggregated.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_vendor_analytics_success(
        self,
        async_test_client: AsyncClient,
        sample_vendor_analytics_data,
        vendor_analytics_auth_headers,
        vendor_analytics_factory
    ):
        """Test successful vendor analytics creation via API."""
        # Arrange
        expected_analytics = vendor_analytics_factory()
        
        with patch('app.api.v1.endpoints.analytics.AnalyticsService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.create_vendor_analytics.return_value = expected_analytics
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.post(
                "/api/v1/analytics/vendor-analytics",
                json=sample_vendor_analytics_data,
                headers=vendor_analytics_auth_headers
            )

        # Assert
        assert response.status_code == 201
        response_data = response.json()
        assert "uuid" in response_data
        assert response_data["vendor_id"] == sample_vendor_analytics_data["vendor_id"]
        mock_service.create_vendor_analytics.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_analytics_overview_success(
        self,
        async_test_client: AsyncClient,
        analytics_admin_auth_headers
    ):
        """Test successful analytics overview retrieval."""
        # Arrange
        mock_overview_data = {
            "timeframe": "DAILY",
            "period": {
                "start": "2025-01-08T00:00:00Z",
                "end": "2025-01-15T00:00:00Z",
                "days": 7
            },
            "key_metrics": {
                "total_users": 1250,
                "active_users": 890,
                "total_vendors": 450,
                "active_vendors": 320,
                "total_bookings": 2150,
                "completed_bookings": 1890,
                "total_revenue": 125000.50,
                "conversion_rate": 8.5
            },
            "trends": {
                "user_growth": "+12.5%",
                "vendor_growth": "+8.3%",
                "booking_growth": "+15.2%",
                "revenue_growth": "+18.7%"
            }
        }
        
        with patch('app.api.v1.endpoints.analytics.AnalyticsService') as mock_service_class:
            mock_service = AsyncMock()
            # Mock the overview generation (this would be implemented in the service)
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.get(
                "/api/v1/analytics/analytics/overview",
                params={
                    "timeframe": "DAILY",
                    "days_back": 7
                },
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "key_metrics" in response_data
        assert "trends" in response_data

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_analytics_api_performance(
        self,
        async_test_client: AsyncClient,
        analytics_user_auth_headers,
        performance_test_data
    ):
        """Test analytics API performance targets (<200ms GET requests)."""
        # Arrange
        user_id = uuid4()
        
        with patch('app.api.v1.endpoints.analytics.AnalyticsService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.get_user_analytics_aggregated.return_value = {
                "aggregated_data": [],
                "summary_metrics": {},
                "trend_analysis": {}
            }
            mock_service_class.return_value = mock_service

            # Act & Assert
            start_time = datetime.now()
            response = await async_test_client.get(
                f"/api/v1/analytics/user-analytics/{user_id}/aggregated",
                headers=analytics_user_auth_headers
            )
            end_time = datetime.now()

        # Performance assertion
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        assert execution_time_ms < performance_test_data["query_timeout_ms"]
        assert response.status_code == 200


class TestDashboardEndpoints:
    """Test suite for Dashboard API endpoints."""

    @pytest.mark.asyncio
    async def test_create_dashboard_widget_success(
        self,
        async_test_client: AsyncClient,
        sample_dashboard_widget_data,
        analytics_admin_auth_headers,
        dashboard_widget_factory
    ):
        """Test successful dashboard widget creation via API."""
        # Arrange
        expected_widget = dashboard_widget_factory()
        
        with patch('app.api.v1.endpoints.dashboard.DashboardService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.create_dashboard_widget.return_value = expected_widget
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.post(
                "/api/v1/dashboard/widgets",
                json=sample_dashboard_widget_data,
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 201
        response_data = response.json()
        assert "uuid" in response_data
        assert response_data["name"] == sample_dashboard_widget_data["name"]
        mock_service.create_dashboard_widget.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_dashboard_widgets_success(
        self,
        async_test_client: AsyncClient,
        analytics_user_auth_headers,
        dashboard_widget_factory
    ):
        """Test successful dashboard widgets retrieval."""
        # Arrange
        mock_widgets = [dashboard_widget_factory() for _ in range(3)]
        
        with patch('app.api.v1.endpoints.dashboard.DashboardService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.get_dashboard_widgets.return_value = mock_widgets
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.get(
                "/api/v1/dashboard/widgets",
                params={
                    "is_active": True,
                    "include_data": False
                },
                headers=analytics_user_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 3
        mock_service.get_dashboard_widgets.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_widget_position_success(
        self,
        async_test_client: AsyncClient,
        analytics_admin_auth_headers,
        dashboard_widget_factory
    ):
        """Test successful widget position update."""
        # Arrange
        widget_id = uuid4()
        updated_widget = dashboard_widget_factory(
            position_x=2,
            position_y=1,
            width=4,
            height=3
        )
        
        with patch('app.api.v1.endpoints.dashboard.DashboardService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.update_widget_position.return_value = updated_widget
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.put(
                f"/api/v1/dashboard/widgets/{widget_id}/position",
                params={
                    "position_x": 2,
                    "position_y": 1,
                    "width": 4,
                    "height": 3
                },
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["position_x"] == 2
        assert response_data["position_y"] == 1
        assert response_data["width"] == 4
        assert response_data["height"] == 3
        mock_service.update_widget_position.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_system_metric_success(
        self,
        async_test_client: AsyncClient,
        analytics_admin_auth_headers,
        system_metrics_factory
    ):
        """Test successful system metric recording."""
        # Arrange
        expected_metric = system_metrics_factory()
        
        with patch('app.api.v1.endpoints.dashboard.DashboardService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.record_system_metric.return_value = expected_metric
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.post(
                "/api/v1/dashboard/metrics",
                params={
                    "metric_name": "api_response_time",
                    "metric_type": "TIMER",
                    "value": 125.5,
                    "timeframe": "HOURLY",
                    "tags": '{"endpoint": "/api/v1/bookings"}',
                    "metadata": '{"server_id": "web-01"}'
                },
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 201
        response_data = response.json()
        assert "uuid" in response_data
        assert response_data["metric_name"] == "api_response_time"
        mock_service.record_system_metric.assert_called_once()


class TestReportingEndpoints:
    """Test suite for Reporting API endpoints."""

    @pytest.mark.asyncio
    async def test_generate_kpi_report_success(
        self,
        async_test_client: AsyncClient,
        analytics_admin_auth_headers
    ):
        """Test successful KPI report generation."""
        # Arrange
        mock_kpi_report = {
            "report_metadata": {
                "category": "performance",
                "timeframe": "DAILY",
                "generated_at": datetime.now(timezone.utc).isoformat()
            },
            "kpi_results": [
                {
                    "name": "Conversion Rate",
                    "value": 8.5,
                    "target": 10.0,
                    "status": "warning",
                    "trend": "stable"
                }
            ],
            "summary_metrics": {
                "total_kpis": 5,
                "healthy_kpis": 3,
                "warning_kpis": 1,
                "critical_kpis": 1,
                "health_score": 75.0
            }
        }
        
        with patch('app.api.v1.endpoints.reporting.ReportingService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.generate_kpi_report.return_value = mock_kpi_report
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.get(
                "/api/v1/reporting/kpi-reports/performance",
                params={
                    "timeframe": "DAILY",
                    "include_trends": True,
                    "include_forecasts": False
                },
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "report_metadata" in response_data
        assert "kpi_results" in response_data
        assert "summary_metrics" in response_data
        mock_service.generate_kpi_report.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_conversion_funnel_report_success(
        self,
        async_test_client: AsyncClient,
        analytics_admin_auth_headers
    ):
        """Test successful conversion funnel report generation."""
        # Arrange
        mock_funnel_report = {
            "funnel_steps": {
                "views": 25000,
                "interests": 5000,
                "considerations": 1500,
                "completions": 1200
            },
            "conversion_rates": {
                "view_to_interest": 20.0,
                "interest_to_consideration": 30.0,
                "consideration_to_completion": 80.0,
                "overall_conversion": 4.8
            },
            "revenue_metrics": {
                "completed_booking_value": 125000.0,
                "average_order_value": 104.17
            }
        }
        
        with patch('app.api.v1.endpoints.reporting.ReportingService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service.generate_conversion_funnel_report.return_value = mock_funnel_report
            mock_service_class.return_value = mock_service

            # Act
            response = await async_test_client.get(
                "/api/v1/reporting/conversion-funnel",
                params={
                    "timeframe": "DAILY",
                    "include_segments": True
                },
                headers=analytics_admin_auth_headers
            )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "funnel_steps" in response_data
        assert "conversion_rates" in response_data
        assert "revenue_metrics" in response_data
        assert response_data["conversion_rates"]["overall_conversion"] == 4.8
        mock_service.generate_conversion_funnel_report.assert_called_once()
