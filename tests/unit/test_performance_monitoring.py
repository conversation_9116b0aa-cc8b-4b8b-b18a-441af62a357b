"""
Unit tests for Performance Monitoring Service.

This module provides comprehensive unit tests for the performance monitoring service:
- PerformanceMonitoringService functionality testing
- APM integration validation
- Performance metrics collection testing
- Alerting system validation
- Sentry integration testing

Implements >80% test coverage with pytest patterns and mock dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4

from app.services.performance_monitoring_service import PerformanceMonitoringService
from app.models.monitoring_models import (
    PerformanceMetric, AlertRule, AlertInstance,
    MetricType, AlertSeverity, AlertStatus
)
from app.schemas.monitoring_schemas import (
    PerformanceMetricCreate, AlertRuleCreate, AlertInstanceCreate
)
from app.core.exceptions import ServiceError, ValidationError


class TestPerformanceMonitoringService:
    """Test suite for PerformanceMonitoringService."""

    @pytest.fixture
    def mock_performance_metric_repo(self):
        """Mock performance metric repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_metrics_by_type = AsyncMock()
        repo.get_metrics_summary = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        return repo

    @pytest.fixture
    def mock_alert_rule_repo(self):
        """Mock alert rule repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_active_rules = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        return repo

    @pytest.fixture
    def mock_alert_instance_repo(self):
        """Mock alert instance repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get_by_id = AsyncMock()
        repo.get_active_alerts = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        return repo

    @pytest.fixture
    def mock_sentry_client(self):
        """Mock Sentry client."""
        client = Mock()
        client.capture_message = Mock()
        client.capture_exception = Mock()
        client.set_tag = Mock()
        client.set_context = Mock()
        return client

    @pytest.fixture
    def performance_monitoring_service(
        self, 
        mock_performance_metric_repo, 
        mock_alert_rule_repo, 
        mock_alert_instance_repo,
        mock_sentry_client
    ):
        """Create PerformanceMonitoringService instance with mocked dependencies."""
        return PerformanceMonitoringService(
            performance_metric_repo=mock_performance_metric_repo,
            alert_rule_repo=mock_alert_rule_repo,
            alert_instance_repo=mock_alert_instance_repo,
            sentry_client=mock_sentry_client
        )

    @pytest.fixture
    def sample_performance_metric_data(self):
        """Sample performance metric data."""
        return {
            "metric_type": MetricType.API_RESPONSE_TIME,
            "component": "booking_service",
            "value": Decimal("150.5"),
            "unit": "milliseconds",
            "tags": {"endpoint": "/api/v1/bookings", "method": "POST"},
            "timestamp": datetime.now(timezone.utc)
        }

    @pytest.fixture
    def sample_alert_rule_data(self):
        """Sample alert rule data."""
        return {
            "name": "High API Response Time",
            "metric_type": MetricType.API_RESPONSE_TIME,
            "component": "booking_service",
            "threshold_value": Decimal("500.0"),
            "comparison_operator": "greater_than",
            "severity": AlertSeverity.WARNING,
            "evaluation_window_minutes": 5,
            "notification_channels": ["email", "slack"],
            "is_active": True
        }

    @pytest.mark.asyncio
    async def test_record_performance_metric_success(
        self, 
        performance_monitoring_service, 
        mock_performance_metric_repo, 
        sample_performance_metric_data
    ):
        """Test successful performance metric recording."""
        # Arrange
        metric_id = uuid4()
        mock_metric = PerformanceMetric(id=metric_id, **sample_performance_metric_data)
        mock_performance_metric_repo.create.return_value = mock_metric

        # Act
        result = await performance_monitoring_service.record_performance_metric(sample_performance_metric_data)

        # Assert
        assert result.id == metric_id
        assert result.metric_type == sample_performance_metric_data["metric_type"]
        assert result.component == sample_performance_metric_data["component"]
        assert result.value == sample_performance_metric_data["value"]
        mock_performance_metric_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_performance_metric_validation_error(
        self, 
        performance_monitoring_service, 
        sample_performance_metric_data
    ):
        """Test performance metric recording with validation error."""
        # Arrange
        invalid_data = sample_performance_metric_data.copy()
        invalid_data["value"] = Decimal("-1.0")  # Invalid negative value

        # Act & Assert
        with pytest.raises(ValidationError, match="Performance metric value must be non-negative"):
            await performance_monitoring_service.record_performance_metric(invalid_data)

    @pytest.mark.asyncio
    async def test_create_alert_rule_success(
        self, 
        performance_monitoring_service, 
        mock_alert_rule_repo, 
        sample_alert_rule_data
    ):
        """Test successful alert rule creation."""
        # Arrange
        rule_id = uuid4()
        mock_rule = AlertRule(id=rule_id, **sample_alert_rule_data)
        mock_alert_rule_repo.create.return_value = mock_rule

        # Act
        result = await performance_monitoring_service.create_alert_rule(sample_alert_rule_data)

        # Assert
        assert result.id == rule_id
        assert result.name == sample_alert_rule_data["name"]
        assert result.threshold_value == sample_alert_rule_data["threshold_value"]
        assert result.severity == sample_alert_rule_data["severity"]
        mock_alert_rule_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_evaluate_alert_rules_success(
        self, 
        performance_monitoring_service, 
        mock_alert_rule_repo,
        mock_alert_instance_repo,
        mock_performance_metric_repo
    ):
        """Test successful alert rule evaluation."""
        # Arrange
        mock_rules = [
            AlertRule(
                id=uuid4(),
                name="High Response Time",
                metric_type=MetricType.API_RESPONSE_TIME,
                component="booking_service",
                threshold_value=Decimal("500.0"),
                comparison_operator="greater_than",
                severity=AlertSeverity.WARNING,
                is_active=True
            )
        ]
        mock_alert_rule_repo.get_active_rules.return_value = mock_rules

        mock_metrics = [
            PerformanceMetric(
                id=uuid4(),
                metric_type=MetricType.API_RESPONSE_TIME,
                component="booking_service",
                value=Decimal("750.0"),  # Exceeds threshold
                timestamp=datetime.now(timezone.utc)
            )
        ]
        mock_performance_metric_repo.get_metrics_by_type.return_value = mock_metrics

        mock_alert_instance_repo.create.return_value = AlertInstance(
            id=uuid4(),
            alert_rule_id=mock_rules[0].id,
            metric_value=Decimal("750.0"),
            status=AlertStatus.ACTIVE
        )

        # Act
        result = await performance_monitoring_service.evaluate_alert_rules()

        # Assert
        assert len(result) == 1
        assert result[0].metric_value == Decimal("750.0")
        assert result[0].status == AlertStatus.ACTIVE
        mock_alert_rule_repo.get_active_rules.assert_called_once()
        mock_alert_instance_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_performance_metrics_summary_success(
        self, 
        performance_monitoring_service, 
        mock_performance_metric_repo
    ):
        """Test successful performance metrics summary retrieval."""
        # Arrange
        component = "booking_service"
        metric_type = MetricType.API_RESPONSE_TIME
        hours = 24
        mock_summary = {
            "avg_value": Decimal("150.5"),
            "max_value": Decimal("500.0"),
            "min_value": Decimal("50.0"),
            "total_count": 1000,
            "p95_value": Decimal("300.0"),
            "p99_value": Decimal("450.0")
        }
        mock_performance_metric_repo.get_metrics_summary.return_value = mock_summary

        # Act
        result = await performance_monitoring_service.get_performance_metrics_summary(
            component, metric_type, hours
        )

        # Assert
        assert result == mock_summary
        mock_performance_metric_repo.get_metrics_summary.assert_called_once_with(
            component, metric_type, hours
        )

    @pytest.mark.asyncio
    async def test_send_alert_notification_success(
        self, 
        performance_monitoring_service,
        mock_sentry_client
    ):
        """Test successful alert notification sending."""
        # Arrange
        alert_instance = AlertInstance(
            id=uuid4(),
            alert_rule_id=uuid4(),
            metric_value=Decimal("750.0"),
            status=AlertStatus.ACTIVE,
            triggered_at=datetime.now(timezone.utc)
        )
        alert_rule = AlertRule(
            id=alert_instance.alert_rule_id,
            name="High Response Time",
            severity=AlertSeverity.WARNING,
            notification_channels=["sentry"]
        )

        with patch.object(performance_monitoring_service, '_send_email_notification') as mock_email:
            with patch.object(performance_monitoring_service, '_send_slack_notification') as mock_slack:
                # Act
                await performance_monitoring_service.send_alert_notification(alert_instance, alert_rule)

                # Assert
                mock_sentry_client.capture_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_alert_status_success(
        self, 
        performance_monitoring_service, 
        mock_alert_instance_repo
    ):
        """Test successful alert status update."""
        # Arrange
        alert_id = uuid4()
        new_status = AlertStatus.RESOLVED
        mock_updated_alert = AlertInstance(
            id=alert_id,
            status=new_status,
            resolved_at=datetime.now(timezone.utc)
        )
        mock_alert_instance_repo.update.return_value = mock_updated_alert

        # Act
        result = await performance_monitoring_service.update_alert_status(alert_id, new_status)

        # Assert
        assert result.id == alert_id
        assert result.status == new_status
        assert result.resolved_at is not None
        mock_alert_instance_repo.update.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_alerts_success(
        self, 
        performance_monitoring_service, 
        mock_alert_instance_repo
    ):
        """Test successful active alerts retrieval."""
        # Arrange
        mock_alerts = [
            AlertInstance(
                id=uuid4(),
                status=AlertStatus.ACTIVE,
                triggered_at=datetime.now(timezone.utc)
            ),
            AlertInstance(
                id=uuid4(),
                status=AlertStatus.ACTIVE,
                triggered_at=datetime.now(timezone.utc) - timedelta(minutes=5)
            )
        ]
        mock_alert_instance_repo.get_active_alerts.return_value = mock_alerts

        # Act
        result = await performance_monitoring_service.get_active_alerts()

        # Assert
        assert len(result) == 2
        assert all(alert.status == AlertStatus.ACTIVE for alert in result)
        mock_alert_instance_repo.get_active_alerts.assert_called_once()

    @pytest.mark.asyncio
    async def test_calculate_sla_metrics_success(self, performance_monitoring_service):
        """Test successful SLA metrics calculation."""
        # Arrange
        component = "booking_service"
        hours = 24

        with patch.object(performance_monitoring_service, '_get_uptime_metrics') as mock_uptime:
            with patch.object(performance_monitoring_service, '_get_response_time_metrics') as mock_response:
                mock_uptime.return_value = {"uptime_percentage": Decimal("99.9")}
                mock_response.return_value = {"avg_response_time": Decimal("150.0")}

                # Act
                result = await performance_monitoring_service.calculate_sla_metrics(component, hours)

                # Assert
                assert "uptime_percentage" in result
                assert "avg_response_time" in result
                assert result["uptime_percentage"] == Decimal("99.9")
                mock_uptime.assert_called_once_with(component, hours)
                mock_response.assert_called_once_with(component, hours)

    @pytest.mark.asyncio
    async def test_service_error_handling(
        self, 
        performance_monitoring_service, 
        mock_performance_metric_repo
    ):
        """Test service error handling."""
        # Arrange
        mock_performance_metric_repo.create.side_effect = Exception("Database error")
        metric_data = {
            "metric_type": MetricType.API_RESPONSE_TIME,
            "component": "test_service",
            "value": Decimal("100.0")
        }

        # Act & Assert
        with pytest.raises(ServiceError, match="Failed to record performance metric"):
            await performance_monitoring_service.record_performance_metric(metric_data)

    @pytest.mark.asyncio
    async def test_metric_aggregation_success(self, performance_monitoring_service):
        """Test successful metric aggregation."""
        # Arrange
        component = "booking_service"
        metric_type = MetricType.API_RESPONSE_TIME
        aggregation_window = "1h"

        with patch.object(performance_monitoring_service, '_aggregate_metrics') as mock_aggregate:
            mock_aggregate.return_value = {
                "avg": Decimal("150.0"),
                "max": Decimal("500.0"),
                "min": Decimal("50.0"),
                "count": 100
            }

            # Act
            result = await performance_monitoring_service.aggregate_metrics(
                component, metric_type, aggregation_window
            )

            # Assert
            assert result["avg"] == Decimal("150.0")
            assert result["count"] == 100
            mock_aggregate.assert_called_once_with(component, metric_type, aggregation_window)

    @pytest.mark.asyncio
    async def test_alert_rule_validation(self, performance_monitoring_service):
        """Test alert rule validation."""
        # Test invalid threshold
        with pytest.raises(ValidationError):
            await performance_monitoring_service.create_alert_rule({
                "name": "Test Rule",
                "metric_type": MetricType.API_RESPONSE_TIME,
                "threshold_value": Decimal("-1.0"),  # Invalid negative threshold
                "comparison_operator": "greater_than"
            })

        # Test invalid evaluation window
        with pytest.raises(ValidationError):
            await performance_monitoring_service.create_alert_rule({
                "name": "Test Rule",
                "metric_type": MetricType.API_RESPONSE_TIME,
                "threshold_value": Decimal("100.0"),
                "comparison_operator": "greater_than",
                "evaluation_window_minutes": 0  # Invalid zero window
            })

    @pytest.mark.asyncio
    async def test_performance_baseline_calculation(self, performance_monitoring_service):
        """Test performance baseline calculation."""
        # Arrange
        component = "booking_service"
        metric_type = MetricType.API_RESPONSE_TIME
        days = 7

        with patch.object(performance_monitoring_service, '_calculate_baseline') as mock_baseline:
            mock_baseline.return_value = {
                "baseline_value": Decimal("120.0"),
                "confidence_interval": {"lower": Decimal("100.0"), "upper": Decimal("140.0")},
                "sample_size": 10000
            }

            # Act
            result = await performance_monitoring_service.calculate_performance_baseline(
                component, metric_type, days
            )

            # Assert
            assert result["baseline_value"] == Decimal("120.0")
            assert "confidence_interval" in result
            mock_baseline.assert_called_once_with(component, metric_type, days)
