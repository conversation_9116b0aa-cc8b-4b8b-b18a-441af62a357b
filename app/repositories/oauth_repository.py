"""
OAuth repository for Culture Connect Backend API.

This module provides data access layer for OAuth-related operations including
OAuth provider management, account linking, token storage, and state management.

Implements Task 2.1.3 requirements for OAuth2 integration with comprehensive
repository patterns following the established Phase 1 foundation.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
from sqlalchemy import select, and_, or_, desc, func, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.models.oauth_models import OAuthProvider, OAuthAccount, OAuthToken, OAuthState
from app.models.user import User
from app.repositories.base import BaseRepository
from app.core.security import generate_secure_token

logger = logging.getLogger(__name__)


class OAuthProviderRepository(BaseRepository[OAuthProvider]):
    """
    OAuth provider repository for provider configuration management.
    
    Provides comprehensive OAuth provider data access methods with performance
    optimization and security features for OAuth provider operations.
    """
    
    async def get_by_name(self, name: str) -> Optional[OAuthProvider]:
        """
        Get OAuth provider by name.
        
        Args:
            name: Provider name (google, facebook, etc.)
            
        Returns:
            OAuthProvider instance if found, None otherwise
        """
        try:
            stmt = select(OAuthProvider).where(
                and_(
                    OAuthProvider.name == name.lower(),
                    OAuthProvider.is_active == True
                )
            )
            result = await self.db.execute(stmt)
            provider = result.scalar_one_or_none()
            
            if provider:
                logger.debug(f"OAuth provider found: {name}")
            
            return provider
            
        except Exception as e:
            logger.error(f"Failed to get OAuth provider by name {name}: {str(e)}")
            return None
    
    async def get_active_providers(self) -> List[OAuthProvider]:
        """
        Get all active OAuth providers.
        
        Returns:
            List of active OAuth providers
        """
        try:
            stmt = select(OAuthProvider).where(
                OAuthProvider.is_active == True
            ).order_by(OAuthProvider.name)
            
            result = await self.db.execute(stmt)
            providers = result.scalars().all()
            
            logger.debug(f"Found {len(providers)} active OAuth providers")
            return list(providers)
            
        except Exception as e:
            logger.error(f"Failed to get active OAuth providers: {str(e)}")
            return []
    
    async def update_provider_status(self, provider_id: int, is_active: bool) -> bool:
        """
        Update OAuth provider active status.
        
        Args:
            provider_id: Provider ID
            is_active: New active status
            
        Returns:
            bool: True if update successful
        """
        try:
            stmt = update(OAuthProvider).where(
                OAuthProvider.id == provider_id
            ).values(
                is_active=is_active,
                updated_at=datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            success = result.rowcount > 0
            if success:
                logger.info(f"OAuth provider {provider_id} status updated to {is_active}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update OAuth provider status: {str(e)}")
            await self.db.rollback()
            return False


class OAuthAccountRepository(BaseRepository[OAuthAccount]):
    """
    OAuth account repository for account linking and management.
    
    Provides comprehensive OAuth account data access methods with user
    relationship management and profile synchronization.
    """
    
    async def get_by_provider_user_id(
        self, 
        provider_id: int, 
        provider_user_id: str
    ) -> Optional[OAuthAccount]:
        """
        Get OAuth account by provider and provider user ID.
        
        Args:
            provider_id: OAuth provider ID
            provider_user_id: User ID from OAuth provider
            
        Returns:
            OAuthAccount instance if found, None otherwise
        """
        try:
            stmt = select(OAuthAccount).options(
                selectinload(OAuthAccount.user),
                selectinload(OAuthAccount.provider)
            ).where(
                and_(
                    OAuthAccount.provider_id == provider_id,
                    OAuthAccount.provider_user_id == provider_user_id,
                    OAuthAccount.is_active == True
                )
            )
            
            result = await self.db.execute(stmt)
            account = result.scalar_one_or_none()
            
            if account:
                logger.debug(f"OAuth account found for provider {provider_id}, user {provider_user_id}")
            
            return account
            
        except Exception as e:
            logger.error(f"Failed to get OAuth account: {str(e)}")
            return None
    
    async def get_by_user_id(self, user_id: int) -> List[OAuthAccount]:
        """
        Get all OAuth accounts for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of OAuth accounts for the user
        """
        try:
            stmt = select(OAuthAccount).options(
                selectinload(OAuthAccount.provider)
            ).where(
                and_(
                    OAuthAccount.user_id == user_id,
                    OAuthAccount.is_active == True
                )
            ).order_by(OAuthAccount.created_at.desc())
            
            result = await self.db.execute(stmt)
            accounts = result.scalars().all()
            
            logger.debug(f"Found {len(accounts)} OAuth accounts for user {user_id}")
            return list(accounts)
            
        except Exception as e:
            logger.error(f"Failed to get OAuth accounts for user {user_id}: {str(e)}")
            return []
    
    async def get_by_provider_email(
        self, 
        provider_id: int, 
        email: str
    ) -> Optional[OAuthAccount]:
        """
        Get OAuth account by provider and email.
        
        Args:
            provider_id: OAuth provider ID
            email: Email address from provider
            
        Returns:
            OAuthAccount instance if found, None otherwise
        """
        try:
            stmt = select(OAuthAccount).options(
                selectinload(OAuthAccount.user),
                selectinload(OAuthAccount.provider)
            ).where(
                and_(
                    OAuthAccount.provider_id == provider_id,
                    func.lower(OAuthAccount.provider_email) == email.lower(),
                    OAuthAccount.is_active == True
                )
            )
            
            result = await self.db.execute(stmt)
            account = result.scalar_one_or_none()
            
            if account:
                logger.debug(f"OAuth account found for provider {provider_id}, email {email}")
            
            return account
            
        except Exception as e:
            logger.error(f"Failed to get OAuth account by email: {str(e)}")
            return None
    
    async def update_last_connected(self, account_id: int) -> bool:
        """
        Update last connected timestamp for OAuth account.
        
        Args:
            account_id: OAuth account ID
            
        Returns:
            bool: True if update successful
        """
        try:
            stmt = update(OAuthAccount).where(
                OAuthAccount.id == account_id
            ).values(
                last_connected_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            success = result.rowcount > 0
            if success:
                logger.debug(f"OAuth account {account_id} last connected updated")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update OAuth account last connected: {str(e)}")
            await self.db.rollback()
            return False
    
    async def deactivate_account(self, account_id: int) -> bool:
        """
        Deactivate OAuth account.
        
        Args:
            account_id: OAuth account ID
            
        Returns:
            bool: True if deactivation successful
        """
        try:
            stmt = update(OAuthAccount).where(
                OAuthAccount.id == account_id
            ).values(
                is_active=False,
                updated_at=datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            success = result.rowcount > 0
            if success:
                logger.info(f"OAuth account {account_id} deactivated")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to deactivate OAuth account: {str(e)}")
            await self.db.rollback()
            return False


class OAuthTokenRepository(BaseRepository[OAuthToken]):
    """
    OAuth token repository for secure token storage and management.
    
    Provides comprehensive OAuth token data access methods with encryption
    support and automatic expiration handling.
    """
    
    async def get_active_token(self, oauth_account_id: int) -> Optional[OAuthToken]:
        """
        Get active OAuth token for account.
        
        Args:
            oauth_account_id: OAuth account ID
            
        Returns:
            Active OAuthToken instance if found, None otherwise
        """
        try:
            stmt = select(OAuthToken).where(
                and_(
                    OAuthToken.oauth_account_id == oauth_account_id,
                    OAuthToken.is_active == True,
                    OAuthToken.is_revoked == False
                )
            ).order_by(OAuthToken.created_at.desc())
            
            result = await self.db.execute(stmt)
            token = result.scalar_one_or_none()
            
            if token:
                logger.debug(f"Active OAuth token found for account {oauth_account_id}")
            
            return token
            
        except Exception as e:
            logger.error(f"Failed to get active OAuth token: {str(e)}")
            return None
    
    async def revoke_token(self, token_id: int) -> bool:
        """
        Revoke OAuth token.
        
        Args:
            token_id: OAuth token ID
            
        Returns:
            bool: True if revocation successful
        """
        try:
            stmt = update(OAuthToken).where(
                OAuthToken.id == token_id
            ).values(
                is_revoked=True,
                is_active=False,
                updated_at=datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            success = result.rowcount > 0
            if success:
                logger.info(f"OAuth token {token_id} revoked")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to revoke OAuth token: {str(e)}")
            await self.db.rollback()
            return False
    
    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired OAuth tokens.
        
        Returns:
            int: Number of tokens cleaned up
        """
        try:
            # Delete expired tokens
            stmt = delete(OAuthToken).where(
                and_(
                    OAuthToken.expires_at < datetime.utcnow(),
                    OAuthToken.is_active == False
                )
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            count = result.rowcount
            if count > 0:
                logger.info(f"Cleaned up {count} expired OAuth tokens")
            
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired OAuth tokens: {str(e)}")
            await self.db.rollback()
            return 0


class OAuthStateRepository(BaseRepository[OAuthState]):
    """
    OAuth state repository for CSRF protection and flow management.
    
    Provides comprehensive OAuth state data access methods with automatic
    expiration handling and security validation.
    """
    
    async def create_state(
        self, 
        provider_id: int, 
        redirect_uri: str,
        scopes: Optional[List[str]] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        expires_in_minutes: int = 10
    ) -> OAuthState:
        """
        Create new OAuth state for CSRF protection.
        
        Args:
            provider_id: OAuth provider ID
            redirect_uri: Redirect URI
            scopes: Requested scopes
            client_ip: Client IP address
            user_agent: Client user agent
            expires_in_minutes: State expiration in minutes
            
        Returns:
            Created OAuthState instance
        """
        try:
            state_token = generate_secure_token(32)
            expires_at = datetime.utcnow() + timedelta(minutes=expires_in_minutes)
            
            state = OAuthState(
                state_token=state_token,
                provider_id=provider_id,
                redirect_uri=str(redirect_uri),
                scopes=scopes or [],
                client_ip=client_ip,
                user_agent=user_agent,
                is_used=False,
                expires_at=expires_at
            )
            
            self.db.add(state)
            await self.db.commit()
            await self.db.refresh(state)
            
            logger.debug(f"OAuth state created: {state_token[:8]}...")
            return state
            
        except Exception as e:
            logger.error(f"Failed to create OAuth state: {str(e)}")
            await self.db.rollback()
            raise
    
    async def get_valid_state(self, state_token: str) -> Optional[OAuthState]:
        """
        Get valid OAuth state by token.
        
        Args:
            state_token: State token
            
        Returns:
            Valid OAuthState instance if found, None otherwise
        """
        try:
            stmt = select(OAuthState).options(
                selectinload(OAuthState.provider)
            ).where(
                and_(
                    OAuthState.state_token == state_token,
                    OAuthState.is_used == False,
                    OAuthState.expires_at > datetime.utcnow()
                )
            )
            
            result = await self.db.execute(stmt)
            state = result.scalar_one_or_none()
            
            if state:
                logger.debug(f"Valid OAuth state found: {state_token[:8]}...")
            
            return state
            
        except Exception as e:
            logger.error(f"Failed to get valid OAuth state: {str(e)}")
            return None
    
    async def mark_state_used(self, state_id: int) -> bool:
        """
        Mark OAuth state as used.
        
        Args:
            state_id: OAuth state ID
            
        Returns:
            bool: True if marking successful
        """
        try:
            stmt = update(OAuthState).where(
                OAuthState.id == state_id
            ).values(
                is_used=True,
                used_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            success = result.rowcount > 0
            if success:
                logger.debug(f"OAuth state {state_id} marked as used")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to mark OAuth state as used: {str(e)}")
            await self.db.rollback()
            return False
    
    async def cleanup_expired_states(self) -> int:
        """
        Clean up expired OAuth states.
        
        Returns:
            int: Number of states cleaned up
        """
        try:
            # Delete expired states
            stmt = delete(OAuthState).where(
                OAuthState.expires_at < datetime.utcnow()
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            count = result.rowcount
            if count > 0:
                logger.info(f"Cleaned up {count} expired OAuth states")
            
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired OAuth states: {str(e)}")
            await self.db.rollback()
            return 0
