"""
Integration tests for database setup and migration functionality.

This module tests the complete database setup process including:
- Database connection and configuration
- Migration execution and rollback
- Schema validation and integrity
- Seeding and data consistency
- Performance and indexing
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import patch, AsyncMock
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import settings
from app.db.database import get_engine, get_async_engine
from app.db.session import get_async_session_context
from app.db.migrations import migration_manager, create_backup, restore_backup
from app.db.seed import DatabaseSeeder, seed_database, clear_database
from app.models.user import User
from app.models.vendor import Vendor, VendorProfile, VendorDocument


class TestDatabaseConnection:
    """Test database connection and configuration."""
    
    @pytest.mark.asyncio
    async def test_database_connection(self):
        """Test basic database connectivity."""
        async with get_async_session_context() as session:
            result = await session.execute(text("SELECT 1 as test"))
            assert result.scalar() == 1
    
    @pytest.mark.asyncio
    async def test_async_engine_configuration(self):
        """Test async engine configuration."""
        engine = get_async_engine()
        assert engine is not None
        
        # Test connection
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
    
    def test_sync_engine_configuration(self):
        """Test sync engine configuration."""
        engine = get_engine()
        assert engine is not None
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            assert result.scalar() == 1


class TestMigrationExecution:
    """Test migration execution and management."""
    
    @pytest.fixture
    def temp_database(self):
        """Create temporary database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            temp_db_path = tmp.name
        
        # Create temporary database URL
        temp_db_url = f"sqlite:///{temp_db_path}"
        
        yield temp_db_url
        
        # Cleanup
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)
    
    @pytest.mark.asyncio
    async def test_migration_history(self):
        """Test getting migration history."""
        history = migration_manager.get_migration_history()
        assert isinstance(history, list)
        
        # Should have at least the initial migration
        assert len(history) >= 1
        
        # Check migration info structure
        if history:
            migration = history[0]
            assert hasattr(migration, 'revision')
            assert hasattr(migration, 'description')
            assert hasattr(migration, 'is_head')
            assert hasattr(migration, 'is_current')
    
    @pytest.mark.asyncio
    async def test_current_revision(self):
        """Test getting current database revision."""
        current_rev = migration_manager.get_current_revision()
        
        # Should return a revision string or None
        assert current_rev is None or isinstance(current_rev, str)
    
    @pytest.mark.asyncio
    async def test_migration_validation(self):
        """Test migration validation functionality."""
        # Get the latest migration
        history = migration_manager.get_migration_history()
        if history:
            latest_migration = history[0]
            
            validation_result = migration_manager.validate_migration(latest_migration.revision)
            
            assert hasattr(validation_result, 'is_valid')
            assert hasattr(validation_result, 'errors')
            assert hasattr(validation_result, 'warnings')
            assert hasattr(validation_result, 'recommendations')
            
            # Initial migration should be valid
            assert validation_result.is_valid or len(validation_result.errors) == 0
    
    @pytest.mark.asyncio
    async def test_migration_backup_creation(self):
        """Test migration backup creation."""
        with patch('app.db.migrations.subprocess.run') as mock_run:
            # Mock successful backup
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Backup created"
            
            backup_path = await create_backup("test_revision")
            
            assert backup_path is not None
            assert "test_revision" in backup_path
            assert backup_path.endswith(".sql")


class TestSchemaValidation:
    """Test database schema validation and integrity."""
    
    @pytest.mark.asyncio
    async def test_table_existence(self):
        """Test that all required tables exist."""
        engine = get_engine()
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # Check for core tables
        required_tables = [
            'users',
            'vendors',
            'vendor_profiles',
            'vendor_documents',
            'api_keys',
            'user_sessions'
        ]
        
        for table in required_tables:
            assert table in tables, f"Required table '{table}' not found"
    
    @pytest.mark.asyncio
    async def test_table_columns(self):
        """Test that tables have required columns."""
        engine = get_engine()
        inspector = inspect(engine)
        
        # Test users table columns
        users_columns = [col['name'] for col in inspector.get_columns('users')]
        required_user_columns = [
            'id', 'uuid', 'email', 'hashed_password', 'first_name', 'last_name',
            'role', 'is_active', 'is_verified', 'created_at', 'updated_at'
        ]
        
        for column in required_user_columns:
            assert column in users_columns, f"Required column '{column}' not found in users table"
        
        # Test vendors table columns
        vendors_columns = [col['name'] for col in inspector.get_columns('vendors')]
        required_vendor_columns = [
            'id', 'uuid', 'user_id', 'business_name', 'business_type',
            'verification_status', 'marketplace_status', 'created_at', 'updated_at'
        ]
        
        for column in required_vendor_columns:
            assert column in vendors_columns, f"Required column '{column}' not found in vendors table"
    
    @pytest.mark.asyncio
    async def test_foreign_key_constraints(self):
        """Test foreign key constraints."""
        engine = get_engine()
        inspector = inspect(engine)
        
        # Test vendors table foreign keys
        vendor_fks = inspector.get_foreign_keys('vendors')
        user_fk_found = False
        
        for fk in vendor_fks:
            if fk['referred_table'] == 'users' and 'user_id' in fk['constrained_columns']:
                user_fk_found = True
                break
        
        assert user_fk_found, "Foreign key constraint from vendors.user_id to users.id not found"
        
        # Test vendor_profiles table foreign keys
        profile_fks = inspector.get_foreign_keys('vendor_profiles')
        vendor_fk_found = False
        
        for fk in profile_fks:
            if fk['referred_table'] == 'vendors' and 'vendor_id' in fk['constrained_columns']:
                vendor_fk_found = True
                break
        
        assert vendor_fk_found, "Foreign key constraint from vendor_profiles.vendor_id to vendors.id not found"
    
    @pytest.mark.asyncio
    async def test_indexes_existence(self):
        """Test that performance indexes exist."""
        engine = get_engine()
        inspector = inspect(engine)
        
        # Test users table indexes
        users_indexes = inspector.get_indexes('users')
        index_names = [idx['name'] for idx in users_indexes]
        
        required_user_indexes = [
            'ix_users_email',
            'ix_users_uuid',
            'ix_users_role'
        ]
        
        for index in required_user_indexes:
            assert index in index_names, f"Required index '{index}' not found on users table"
        
        # Test vendors table indexes
        vendors_indexes = inspector.get_indexes('vendors')
        vendor_index_names = [idx['name'] for idx in vendors_indexes]
        
        required_vendor_indexes = [
            'ix_vendors_user_id',
            'ix_vendors_business_name',
            'ix_vendors_business_type'
        ]
        
        for index in required_vendor_indexes:
            assert index in vendor_index_names, f"Required index '{index}' not found on vendors table"


class TestDatabaseSeeding:
    """Test database seeding functionality."""
    
    @pytest.mark.asyncio
    async def test_database_seeding(self):
        """Test complete database seeding process."""
        # Clear database first
        await clear_database()
        
        # Seed database
        counts = await seed_database("testing")
        
        assert isinstance(counts, dict)
        assert 'users' in counts
        assert 'vendors' in counts
        assert 'vendor_profiles' in counts
        
        # Verify data was created
        assert counts['users'] > 0
        assert counts['vendors'] > 0
        assert counts['vendor_profiles'] > 0
    
    @pytest.mark.asyncio
    async def test_seeder_class_functionality(self):
        """Test DatabaseSeeder class methods."""
        seeder = DatabaseSeeder()
        
        async with get_async_session_context() as session:
            # Clear existing data
            await seeder._clear_existing_data(session)
            
            # Seed users
            users = await seeder._seed_users(session, count=10)
            assert len(users) == 10
            
            # Seed vendors (only for vendor users)
            vendor_users = [u for u in users if u.role.value == 'vendor']
            if vendor_users:
                vendors = await seeder._seed_vendors(session, vendor_users)
                assert len(vendors) == len(vendor_users)
                
                # Seed vendor profiles
                profiles = await seeder._seed_vendor_profiles(session, vendors)
                assert len(profiles) == len(vendors)
    
    @pytest.mark.asyncio
    async def test_data_consistency(self):
        """Test data consistency after seeding."""
        # Clear and seed database
        await clear_database()
        await seed_database("testing")
        
        async with get_async_session_context() as session:
            # Check user-vendor relationships
            result = await session.execute(
                text("""
                    SELECT COUNT(*) FROM users u 
                    JOIN vendors v ON u.vendor_id = v.id 
                    WHERE u.role = 'vendor'
                """)
            )
            vendor_relationships = result.scalar()
            assert vendor_relationships > 0
            
            # Check vendor-profile relationships
            result = await session.execute(
                text("""
                    SELECT COUNT(*) FROM vendors v 
                    JOIN vendor_profiles vp ON v.id = vp.vendor_id
                """)
            )
            profile_relationships = result.scalar()
            assert profile_relationships > 0


class TestDatabasePerformance:
    """Test database performance and optimization."""
    
    @pytest.mark.asyncio
    async def test_query_performance_with_indexes(self):
        """Test that indexes improve query performance."""
        # Ensure we have test data
        await seed_database("testing")
        
        async with get_async_session_context() as session:
            # Test email lookup (should use index)
            import time
            
            start_time = time.time()
            result = await session.execute(
                text("SELECT * FROM users WHERE email = '<EMAIL>'")
            )
            email_query_time = time.time() - start_time
            
            # Should be fast with index
            assert email_query_time < 0.1  # Less than 100ms
            
            # Test business name lookup (should use index)
            start_time = time.time()
            result = await session.execute(
                text("SELECT * FROM vendors WHERE business_name LIKE '%Cultural%'")
            )
            name_query_time = time.time() - start_time
            
            # Should be reasonably fast
            assert name_query_time < 0.5  # Less than 500ms
    
    @pytest.mark.asyncio
    async def test_connection_pooling(self):
        """Test database connection pooling."""
        # Test multiple concurrent connections
        async def test_connection():
            async with get_async_session_context() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar()
        
        # Run multiple concurrent queries
        tasks = [test_connection() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert all(result == 1 for result in results)


class TestMigrationRollback:
    """Test migration rollback functionality."""
    
    @pytest.mark.asyncio
    async def test_migration_rollback_safety(self):
        """Test migration rollback safety checks."""
        # This would test rollback functionality
        # For now, we'll test the validation
        
        current_rev = migration_manager.get_current_revision()
        if current_rev:
            # Test that rollback requires confirmation
            result = migration_manager.downgrade_with_validation(current_rev, confirm=False)
            assert result is False  # Should fail without confirmation
