"""
Integration tests for AI Moderation workflows.

This module contains comprehensive integration tests for AI moderation workflows,
testing integration with aimlapi.com and content moderation pipelines.

Implements Task 4.4.6 Phase 3 requirements with AI integration validation.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

from app.services.review_moderation_service import ReviewModerationService
from app.models.review_models import ModerationAction, ReviewStatus
from app.schemas.review_schemas import ReviewModerationCreateSchema


class TestAIModerationIntegration:
    """Test AI moderation integration workflows."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service."""
        return AsyncMock()

    @pytest.fixture
    def mock_notification_service(self):
        """Mock notification service."""
        return AsyncMock()

    @pytest.fixture
    def moderation_service(self, mock_db_session):
        """Create ReviewModerationService instance."""
        service = ReviewModerationService(mock_db_session)
        service.ai_service = AsyncMock()
        service.notification_service = AsyncMock()
        return service

    @pytest.mark.asyncio
    async def test_ai_content_analysis_integration(self, moderation_service):
        """Test AI content analysis integration with aimlapi.com."""
        # Mock AI service response for positive content
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": 0.85,
            "toxicity_score": 0.02,
            "spam_probability": 0.01,
            "language_detection": "en",
            "content_categories": ["positive_feedback", "service_review"],
            "confidence": 0.94
        }

        # Mock moderation decision
        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.APPROVE,
            "confidence_score": 0.94,
            "reasoning": "Content is positive and appropriate"
        }

        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.APPROVE
        mock_moderation.ai_confidence_score = 0.94
        mock_moderation.ai_analysis_results = {
            "sentiment_score": 0.85,
            "toxicity_score": 0.02,
            "spam_probability": 0.01
        }

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Excellent Cultural Experience",
                    "content": "The tour guide was knowledgeable and friendly. I learned so much about the local culture and history. Highly recommended!"
                }
            )

        # Assertions
        assert result.action == ModerationAction.APPROVE
        assert result.ai_confidence_score == 0.94
        assert result.ai_analysis_results["sentiment_score"] == 0.85
        assert result.ai_analysis_results["toxicity_score"] == 0.02

        # Verify AI service calls
        moderation_service.ai_service.analyze_content.assert_called()
        moderation_service.ai_service.make_moderation_decision.assert_called()

    @pytest.mark.asyncio
    async def test_toxic_content_detection_integration(self, moderation_service):
        """Test toxic content detection and rejection workflow."""
        # Mock AI service response for toxic content
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": -0.75,
            "toxicity_score": 0.85,
            "spam_probability": 0.15,
            "language_detection": "en",
            "content_categories": ["toxic", "inappropriate"],
            "confidence": 0.92
        }

        # Mock moderation decision for rejection
        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.REJECT,
            "confidence_score": 0.92,
            "reasoning": "Content contains toxic language and inappropriate material"
        }

        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.REJECT
        mock_moderation.ai_confidence_score = 0.92
        mock_moderation.manual_review_required = False
        mock_moderation.reason = "Content contains toxic language"

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Terrible Service",
                    "content": "This service was absolutely horrible and the staff were rude and unprofessional."
                }
            )

        # Assertions
        assert result.action == ModerationAction.REJECT
        assert result.ai_confidence_score == 0.92
        assert "toxic" in result.reason.lower()

        # Verify notification for rejected content
        moderation_service.notification_service.send_moderation_alert.assert_called()

    @pytest.mark.asyncio
    async def test_spam_detection_integration(self, moderation_service):
        """Test spam detection and flagging workflow."""
        # Mock AI service response for spam content
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": 0.1,
            "toxicity_score": 0.1,
            "spam_probability": 0.88,
            "language_detection": "en",
            "content_categories": ["spam", "promotional"],
            "confidence": 0.89
        }

        # Mock moderation decision for flagging
        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.FLAG,
            "confidence_score": 0.89,
            "reasoning": "Content appears to be spam or promotional material"
        }

        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.FLAG
        mock_moderation.ai_confidence_score = 0.89
        mock_moderation.manual_review_required = True
        mock_moderation.reason = "Potential spam content detected"

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Check out my website",
                    "content": "Visit www.example.com for amazing deals! Use code SAVE50 for 50% off!"
                }
            )

        # Assertions
        assert result.action == ModerationAction.FLAG
        assert result.manual_review_required is True
        assert result.ai_confidence_score == 0.89

        # Verify manual review notification
        moderation_service.notification_service.send_moderation_alert.assert_called()

    @pytest.mark.asyncio
    async def test_low_confidence_manual_review_integration(self, moderation_service):
        """Test low confidence AI decisions requiring manual review."""
        # Mock AI service response with low confidence
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": 0.2,
            "toxicity_score": 0.4,
            "spam_probability": 0.3,
            "language_detection": "en",
            "content_categories": ["ambiguous"],
            "confidence": 0.62
        }

        # Mock moderation decision with low confidence
        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.FLAG,
            "confidence_score": 0.62,
            "reasoning": "Content analysis inconclusive, manual review recommended"
        }

        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.FLAG
        mock_moderation.ai_confidence_score = 0.62
        mock_moderation.manual_review_required = True
        mock_moderation.reason = "Low confidence AI analysis"

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Mixed Experience",
                    "content": "The service had some good points but also some issues that need addressing."
                }
            )

        # Assertions
        assert result.action == ModerationAction.FLAG
        assert result.manual_review_required is True
        assert result.ai_confidence_score < 0.7  # Below confidence threshold

        # Verify manual review queue notification
        moderation_service.notification_service.send_moderation_alert.assert_called()

    @pytest.mark.asyncio
    async def test_multilingual_content_integration(self, moderation_service):
        """Test multilingual content moderation integration."""
        # Mock AI service response for non-English content
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": 0.7,
            "toxicity_score": 0.05,
            "spam_probability": 0.02,
            "language_detection": "es",  # Spanish
            "content_categories": ["positive_feedback"],
            "confidence": 0.88
        }

        # Mock translation service
        moderation_service.ai_service.translate_content.return_value = {
            "translated_text": "Excellent service, very professional and friendly staff.",
            "source_language": "es",
            "target_language": "en",
            "confidence": 0.95
        }

        # Mock moderation decision
        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.APPROVE,
            "confidence_score": 0.88,
            "reasoning": "Positive content in Spanish, translation confirms appropriateness"
        }

        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.APPROVE
        mock_moderation.ai_confidence_score = 0.88
        mock_moderation.ai_analysis_results = {
            "language_detected": "es",
            "translated_content": "Excellent service, very professional and friendly staff."
        }

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Excelente servicio",
                    "content": "Servicio excelente, personal muy profesional y amigable."
                }
            )

        # Assertions
        assert result.action == ModerationAction.APPROVE
        assert result.ai_analysis_results["language_detected"] == "es"
        assert "Excellent service" in result.ai_analysis_results["translated_content"]

        # Verify translation service was called
        moderation_service.ai_service.translate_content.assert_called()

    @pytest.mark.asyncio
    async def test_ai_service_error_handling_integration(self, moderation_service):
        """Test AI service error handling and fallback mechanisms."""
        # Mock AI service failure
        moderation_service.ai_service.analyze_content.side_effect = Exception("AI service temporarily unavailable")

        # Mock fallback to manual review
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.action = ModerationAction.FLAG
        mock_moderation.ai_confidence_score = 0.0
        mock_moderation.manual_review_required = True
        mock_moderation.reason = "AI service unavailable, manual review required"

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            result = await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Service Review",
                    "content": "This is a test review for error handling."
                }
            )

        # Assertions
        assert result.action == ModerationAction.FLAG
        assert result.manual_review_required is True
        assert result.ai_confidence_score == 0.0
        assert "AI service unavailable" in result.reason

        # Verify fallback notification
        moderation_service.notification_service.send_moderation_alert.assert_called()

    @pytest.mark.asyncio
    async def test_performance_ai_moderation_integration(self, moderation_service):
        """Test AI moderation performance requirements."""
        import time

        # Mock fast AI service responses
        moderation_service.ai_service.analyze_content.return_value = {
            "sentiment_score": 0.8,
            "toxicity_score": 0.1,
            "spam_probability": 0.05,
            "confidence": 0.9
        }

        moderation_service.ai_service.make_moderation_decision.return_value = {
            "action": ModerationAction.APPROVE,
            "confidence_score": 0.9,
            "reasoning": "Content is appropriate"
        }

        mock_moderation = MagicMock()
        mock_moderation.action = ModerationAction.APPROVE

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            # Measure AI moderation performance
            start_time = time.time()
            
            await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Performance Test",
                    "content": "Testing AI moderation performance requirements."
                }
            )
            
            moderation_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        # Performance assertion - AI moderation should be fast
        assert moderation_time < 200  # <200ms for AI moderation processing

    @pytest.mark.asyncio
    async def test_bulk_moderation_integration(self, moderation_service):
        """Test bulk content moderation integration."""
        # Mock bulk AI analysis
        moderation_service.ai_service.bulk_analyze_content.return_value = [
            {
                "review_id": 1,
                "action": ModerationAction.APPROVE,
                "confidence_score": 0.92,
                "analysis": {"sentiment_score": 0.8, "toxicity_score": 0.05}
            },
            {
                "review_id": 2,
                "action": ModerationAction.FLAG,
                "confidence_score": 0.65,
                "analysis": {"sentiment_score": 0.2, "toxicity_score": 0.4}
            },
            {
                "review_id": 3,
                "action": ModerationAction.REJECT,
                "confidence_score": 0.95,
                "analysis": {"sentiment_score": -0.8, "toxicity_score": 0.9}
            }
        ]

        # Mock bulk moderation processing
        mock_bulk_result = {
            "processed_count": 3,
            "approved_count": 1,
            "flagged_count": 1,
            "rejected_count": 1,
            "processing_time": 0.5,
            "average_confidence": 0.84
        }

        with patch.object(moderation_service, 'bulk_moderate_reviews', return_value=mock_bulk_result):
            result = await moderation_service.bulk_moderate_reviews(
                review_ids=[1, 2, 3]
            )

        # Assertions
        assert result["processed_count"] == 3
        assert result["approved_count"] == 1
        assert result["flagged_count"] == 1
        assert result["rejected_count"] == 1
        assert result["processing_time"] < 1.0  # Performance requirement

        # Verify bulk AI service was called
        moderation_service.ai_service.bulk_analyze_content.assert_called()
