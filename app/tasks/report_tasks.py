"""
Report generation background tasks for Culture Connect Backend API.

This module provides comprehensive report generation including:
- Vendor performance reports
- Analytics and metrics reports
- Data export operations
- Scheduled report generation
- Report delivery and distribution

Implements Task 6.2.1 requirements for report task processing with
production-grade data processing and export capabilities.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from uuid import UUID

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="report.generate_vendor_report", queue="report")
def generate_vendor_report(self, report_request: Dict[str, Any]):
    """
    Generate vendor performance report.
    
    Args:
        report_request: Report parameters including vendor_id, date_range, metrics
    """
    try:
        async def _generate_report():
            from app.services.vendor_dashboard_services import VendorAnalyticsService
            from app.services.report_service import ReportService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                analytics_service = VendorAnalyticsService(session)
                report_service = ReportService(session)
                
                vendor_id = UUID(report_request["vendor_id"])
                start_date = datetime.fromisoformat(report_request["start_date"])
                end_date = datetime.fromisoformat(report_request["end_date"])
                
                # Generate vendor analytics
                analytics_data = await analytics_service.get_vendor_analytics(
                    vendor_id=vendor_id,
                    start_date=start_date,
                    end_date=end_date,
                    include_trends=True
                )
                
                # Generate report
                report = await report_service.generate_vendor_report(
                    vendor_id=vendor_id,
                    analytics_data=analytics_data,
                    report_format=report_request.get("format", "pdf")
                )
                
                logger.info(f"Vendor report generated for {vendor_id}", extra={
                    "vendor_id": str(vendor_id),
                    "report_id": report.report_id,
                    "format": report_request.get("format", "pdf"),
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "report_id": report.report_id,
                    "file_path": report.file_path,
                    "file_size": report.file_size,
                    "format": report.format
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_generate_report())
            return {
                "status": "success",
                "generated_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to generate vendor report: {str(e)}", extra={
            "report_request": report_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="report.generate_analytics_report", queue="report")
def generate_analytics_report(self, report_request: Dict[str, Any]):
    """
    Generate system analytics report.
    
    Args:
        report_request: Report parameters including metrics, date_range, recipients
    """
    try:
        async def _generate_analytics():
            from app.services.performance_analytics_service import PerformanceAnalyticsService
            from app.services.report_service import ReportService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                analytics_service = PerformanceAnalyticsService(session)
                report_service = ReportService(session)
                
                start_date = datetime.fromisoformat(report_request["start_date"])
                end_date = datetime.fromisoformat(report_request["end_date"])
                metrics = report_request.get("metrics", ["all"])
                
                # Generate analytics data
                analytics_data = await analytics_service.get_system_analytics(
                    start_date=start_date,
                    end_date=end_date,
                    metrics=metrics,
                    include_trends=True
                )
                
                # Generate report
                report = await report_service.generate_analytics_report(
                    analytics_data=analytics_data,
                    report_format=report_request.get("format", "pdf"),
                    include_charts=report_request.get("include_charts", True)
                )
                
                logger.info(f"Analytics report generated", extra={
                    "report_id": report.report_id,
                    "metrics": metrics,
                    "format": report_request.get("format", "pdf"),
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "report_id": report.report_id,
                    "file_path": report.file_path,
                    "file_size": report.file_size,
                    "format": report.format
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_generate_analytics())
            return {
                "status": "success",
                "generated_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to generate analytics report: {str(e)}", extra={
            "report_request": report_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="report.export_booking_data", queue="report")
def export_booking_data(self, export_request: Dict[str, Any]):
    """
    Export booking data to specified format.
    
    Args:
        export_request: Export parameters including date_range, format, filters
    """
    try:
        async def _export_data():
            from app.services.booking_service import BookingService
            from app.services.data_export_service import DataExportService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                booking_service = BookingService(session)
                export_service = DataExportService(session)
                
                start_date = datetime.fromisoformat(export_request["start_date"])
                end_date = datetime.fromisoformat(export_request["end_date"])
                filters = export_request.get("filters", {})
                
                # Get booking data
                bookings = await booking_service.get_bookings_for_export(
                    start_date=start_date,
                    end_date=end_date,
                    filters=filters
                )
                
                # Export data
                export_result = await export_service.export_bookings(
                    bookings=bookings,
                    export_format=export_request.get("format", "csv"),
                    include_related_data=export_request.get("include_related", True)
                )
                
                logger.info(f"Booking data exported", extra={
                    "export_id": export_result.export_id,
                    "record_count": len(bookings),
                    "format": export_request.get("format", "csv"),
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "export_id": export_result.export_id,
                    "file_path": export_result.file_path,
                    "record_count": len(bookings),
                    "file_size": export_result.file_size
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_export_data())
            return {
                "status": "success",
                "exported_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to export booking data: {str(e)}", extra={
            "export_request": export_request,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)
