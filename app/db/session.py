"""
Enhanced database session management for Culture Connect Backend API.

This module provides comprehensive session management utilities including:
- Context managers for database sessions with automatic cleanup
- Transaction management with rollback support
- Connection retry logic with exponential backoff
- FastAPI dependency injection for database sessions
- Performance monitoring and logging integration
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, Any, Dict
from functools import wraps
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import (
    SQLAlchemyError,
    DisconnectionError,
    TimeoutError as SQLTimeoutError,
    OperationalError,
    DatabaseError
)
from sqlalchemy import text
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
    after_log
)

from app.db.database import AsyncSessionLocal, async_engine
from app.core.config import settings
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Database operation timeout settings
DB_OPERATION_TIMEOUT = 30  # seconds
DB_CONNECTION_RETRY_ATTEMPTS = 3
DB_RETRY_WAIT_MIN = 1  # seconds
DB_RETRY_WAIT_MAX = 10  # seconds


class DatabaseError(Exception):
    """Custom database error for application-specific handling."""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)


class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""
    pass


class DatabaseTransactionError(DatabaseError):
    """Raised when database transaction fails."""
    pass


class DatabaseSessionManager:
    """
    Enhanced database session manager with retry logic and monitoring.

    Provides centralized session management with automatic retry logic,
    performance monitoring, and comprehensive error handling.
    """

    def __init__(self):
        self.connection_pool_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "retry_attempts": 0
        }

    @retry(
        stop=stop_after_attempt(DB_CONNECTION_RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=DB_RETRY_WAIT_MIN, max=DB_RETRY_WAIT_MAX),
        retry=retry_if_exception_type((DisconnectionError, OperationalError, SQLTimeoutError)),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO)
    )
    async def _create_session_with_retry(self) -> AsyncSession:
        """
        Create database session with retry logic.

        Returns:
            AsyncSession: Database session instance

        Raises:
            DatabaseConnectionError: If connection fails after all retries
        """
        try:
            session = AsyncSessionLocal()

            # Test connection with a simple query
            await session.execute(text("SELECT 1"))

            self.connection_pool_stats["total_connections"] += 1
            self.connection_pool_stats["active_connections"] += 1

            logger.debug(
                "Database session created successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "pool_stats": self.connection_pool_stats
                }
            )

            return session

        except Exception as e:
            self.connection_pool_stats["failed_connections"] += 1
            self.connection_pool_stats["retry_attempts"] += 1

            logger.error(
                f"Failed to create database session: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__,
                    "pool_stats": self.connection_pool_stats
                }
            )

            if isinstance(e, (DisconnectionError, OperationalError, SQLTimeoutError)):
                raise  # Let tenacity handle the retry
            else:
                raise DatabaseConnectionError(
                    f"Database connection failed: {str(e)}",
                    original_error=e
                )

    async def _close_session_safely(self, session: AsyncSession) -> None:
        """
        Safely close database session with error handling.

        Args:
            session: Database session to close
        """
        try:
            await session.close()
            self.connection_pool_stats["active_connections"] -= 1

            logger.debug(
                "Database session closed successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "pool_stats": self.connection_pool_stats
                }
            )

        except Exception as e:
            logger.warning(
                f"Error closing database session: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__
                }
            )

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Context manager for database sessions with automatic cleanup.

        Yields:
            AsyncSession: Database session instance

        Raises:
            DatabaseConnectionError: If session creation fails
        """
        session = None
        start_time = time.time()

        try:
            session = await self._create_session_with_retry()
            yield session

        except Exception as e:
            if session:
                try:
                    await session.rollback()
                    logger.info(
                        "Database session rolled back due to error",
                        extra={"correlation_id": correlation_id.get('')}
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Failed to rollback session: {str(rollback_error)}",
                        extra={"correlation_id": correlation_id.get('')}
                    )

            raise DatabaseError(f"Database operation failed: {str(e)}", original_error=e)

        finally:
            if session:
                await self._close_session_safely(session)

            # Log session duration for performance monitoring
            duration = time.time() - start_time
            logger.info(
                f"Database session completed in {duration:.3f}s",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "session_duration": duration,
                    "performance_metric": "db_session_duration"
                }
            )

    @asynccontextmanager
    async def get_transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Context manager for database transactions with automatic commit/rollback.

        Yields:
            AsyncSession: Database session within transaction

        Raises:
            DatabaseTransactionError: If transaction fails
        """
        async with self.get_session() as session:
            transaction_start = time.time()

            try:
                # Begin transaction
                await session.begin()

                logger.debug(
                    "Database transaction started",
                    extra={"correlation_id": correlation_id.get('')}
                )

                yield session

                # Commit transaction
                await session.commit()

                transaction_duration = time.time() - transaction_start
                logger.info(
                    f"Database transaction committed successfully in {transaction_duration:.3f}s",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "transaction_duration": transaction_duration,
                        "performance_metric": "db_transaction_duration"
                    }
                )

            except Exception as e:
                try:
                    await session.rollback()
                    logger.warning(
                        f"Database transaction rolled back: {str(e)}",
                        extra={
                            "correlation_id": correlation_id.get(''),
                            "error_type": type(e).__name__
                        }
                    )
                except Exception as rollback_error:
                    logger.error(
                        f"Failed to rollback transaction: {str(rollback_error)}",
                        extra={"correlation_id": correlation_id.get('')}
                    )

                raise DatabaseTransactionError(
                    f"Transaction failed: {str(e)}",
                    original_error=e
                )

    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get current connection pool statistics.

        Returns:
            Dict[str, Any]: Connection pool statistics
        """
        return self.connection_pool_stats.copy()


# Global session manager instance
session_manager = DatabaseSessionManager()


# FastAPI dependency functions
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for database sessions.

    Yields:
        AsyncSession: Database session for request handling

    Example:
        @app.get("/users")
        async def get_users(db: AsyncSession = Depends(get_db_session)):
            # Use db session here
            pass
    """
    async with session_manager.get_session() as session:
        yield session


async def get_db_transaction() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for database transactions.

    Yields:
        AsyncSession: Database session within transaction

    Example:
        @app.post("/users")
        async def create_user(
            user_data: UserCreate,
            db: AsyncSession = Depends(get_db_transaction)
        ):
            # Use db session within transaction
            pass
    """
    async with session_manager.get_transaction() as session:
        yield session


# Context manager aliases for convenience
get_async_session_context = session_manager.get_session
get_async_transaction_context = session_manager.get_transaction


def with_database_error_handling(func):
    """
    Decorator for database operations with comprehensive error handling.

    Args:
        func: Async function to wrap with error handling

    Returns:
        Wrapped function with error handling
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except DatabaseError:
            # Re-raise our custom database errors
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"SQLAlchemy error in {func.__name__}: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "function_name": func.__name__,
                    "error_type": type(e).__name__
                }
            )
            raise DatabaseError(f"Database operation failed: {str(e)}", original_error=e)
        except Exception as e:
            logger.error(
                f"Unexpected error in {func.__name__}: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "function_name": func.__name__,
                    "error_type": type(e).__name__
                }
            )
            raise DatabaseError(f"Unexpected database error: {str(e)}", original_error=e)

    return wrapper


async def test_database_connection() -> bool:
    """
    Test database connection and return status.

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        async with session_manager.get_session() as session:
            await session.execute(text("SELECT 1"))
            return True
    except Exception as e:
        logger.error(
            f"Database connection test failed: {str(e)}",
            extra={"correlation_id": correlation_id.get('')}
        )
        return False


async def get_database_info() -> Dict[str, Any]:
    """
    Get database information and statistics.

    Returns:
        Dict[str, Any]: Database information including version, stats, etc.
    """
    try:
        async with session_manager.get_session() as session:
            # Get PostgreSQL version
            version_result = await session.execute(text("SELECT version()"))
            version = version_result.scalar()

            # Get database size
            size_result = await session.execute(
                text("SELECT pg_size_pretty(pg_database_size(current_database()))")
            )
            database_size = size_result.scalar()

            # Get connection count
            connections_result = await session.execute(
                text("SELECT count(*) FROM pg_stat_activity WHERE datname = current_database()")
            )
            active_connections = connections_result.scalar()

            return {
                "version": version,
                "database_size": database_size,
                "active_connections": active_connections,
                "pool_stats": session_manager.get_connection_stats(),
                "engine_pool_size": async_engine.pool.size(),
                "engine_pool_checked_out": async_engine.pool.checkedout(),
                "engine_pool_overflow": async_engine.pool.overflow(),
            }

    except Exception as e:
        logger.error(
            f"Failed to get database info: {str(e)}",
            extra={"correlation_id": correlation_id.get('')}
        )
        return {"error": str(e)}
