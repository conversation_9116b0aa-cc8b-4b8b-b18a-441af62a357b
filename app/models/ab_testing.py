"""
A/B Testing Models for Culture Connect Backend API.

This module defines comprehensive A/B testing models for payment routing strategy optimization including:
- ABTest: Core A/B test configuration and management
- ABTestAssignment: User assignment tracking with consistent hashing
- ABTestResult: Test result tracking and performance metrics
- ABTestAnalysis: Statistical analysis and significance validation
- RoutingStrategy: Routing strategy enumeration for testing

Implements Phase 2.3 A/B Testing Framework requirements with production-grade
PostgreSQL optimization, statistical significance validation, and seamless integration
with existing geolocation analytics and VPN detection systems.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index, Numeric, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from decimal import Decimal

from app.db.base import BaseModelWithAudit


class RoutingStrategy(str, Enum):
    """Payment routing strategies for A/B testing."""
    GEOLOCATION_BASED = "geolocation_based"
    CURRENCY_BASED = "currency_based"
    HYBRID = "hybrid"
    USER_PREFERENCE = "user_preference"
    VPN_ADJUSTED = "vpn_adjusted"


class ABTestStatus(str, Enum):
    """A/B test status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ABTestType(str, Enum):
    """A/B test type enumeration."""
    ROUTING_STRATEGY = "routing_strategy"
    PROVIDER_SELECTION = "provider_selection"
    CONVERSION_OPTIMIZATION = "conversion_optimization"
    PERFORMANCE_TESTING = "performance_testing"


class StatisticalSignificance(str, Enum):
    """Statistical significance levels."""
    NOT_SIGNIFICANT = "not_significant"
    MARGINALLY_SIGNIFICANT = "marginally_significant"  # p < 0.1
    SIGNIFICANT = "significant"  # p < 0.05
    HIGHLY_SIGNIFICANT = "highly_significant"  # p < 0.01


class ABTest(BaseModelWithAudit):
    """
    Core A/B test configuration and management model.

    Stores comprehensive A/B test configuration including routing strategies,
    traffic allocation, statistical parameters, and test lifecycle management.
    """
    __tablename__ = "ab_tests"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Test configuration
    test_name = Column(String(100), nullable=False, unique=True, index=True)
    test_description = Column(Text, nullable=True)
    test_type = Column(String(30), nullable=False, default=ABTestType.ROUTING_STRATEGY)

    # Test strategies
    strategy_a = Column(String(30), nullable=False)  # Control group strategy
    strategy_b = Column(String(30), nullable=False)  # Treatment group strategy
    strategy_a_name = Column(String(100), nullable=True)  # Human-readable name
    strategy_b_name = Column(String(100), nullable=True)  # Human-readable name

    # Traffic allocation
    traffic_split = Column(Float, nullable=False, default=0.5)  # 0.0 to 1.0
    traffic_allocation_percentage = Column(Float, nullable=False, default=100.0)  # % of total traffic

    # Statistical parameters
    confidence_level = Column(Float, nullable=False, default=0.95)  # 95% confidence
    min_sample_size = Column(Integer, nullable=False, default=1000)
    min_effect_size = Column(Float, nullable=False, default=0.02)  # 2% minimum detectable effect
    statistical_power = Column(Float, nullable=False, default=0.8)  # 80% power

    # Test lifecycle
    status = Column(String(20), nullable=False, default=ABTestStatus.DRAFT, index=True)
    start_date = Column(DateTime(timezone=True), nullable=True, index=True)
    end_date = Column(DateTime(timezone=True), nullable=True, index=True)
    planned_duration_days = Column(Integer, nullable=True)

    # Targeting and filtering
    target_countries = Column(JSONB, nullable=True)  # List of country codes
    exclude_countries = Column(JSONB, nullable=True)  # List of excluded countries
    target_user_segments = Column(JSONB, nullable=True)  # User segment filters
    exclude_vpn_users = Column(Boolean, nullable=False, default=False)

    # Success metrics
    primary_metric = Column(String(50), nullable=False, default="conversion_rate")
    secondary_metrics = Column(JSONB, nullable=True)  # List of secondary metrics
    success_criteria = Column(JSONB, nullable=True)  # Success criteria definition

    # Test configuration metadata
    test_configuration = Column(JSONB, nullable=True)  # Additional test parameters
    randomization_seed = Column(String(32), nullable=True)  # For reproducible randomization

    # Performance tracking
    current_sample_size_a = Column(Integer, nullable=False, default=0)
    current_sample_size_b = Column(Integer, nullable=False, default=0)
    current_conversion_rate_a = Column(Float, nullable=False, default=0.0)
    current_conversion_rate_b = Column(Float, nullable=False, default=0.0)

    # Analysis results
    last_analysis_date = Column(DateTime(timezone=True), nullable=True)
    current_p_value = Column(Float, nullable=True)
    current_significance = Column(String(30), nullable=True)
    is_statistically_significant = Column(Boolean, nullable=False, default=False)

    # Relationships
    assignments = relationship("ABTestAssignment", back_populates="test", cascade="all, delete-orphan")
    results = relationship("ABTestResult", back_populates="test", cascade="all, delete-orphan")
    analyses = relationship("ABTestAnalysis", back_populates="test", cascade="all, delete-orphan")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_ab_test_status_dates', 'status', 'start_date', 'end_date'),
        Index('idx_ab_test_active', 'status', 'start_date', 'end_date'),
        Index('idx_ab_test_type_status', 'test_type', 'status'),
        Index('idx_ab_test_significance', 'is_statistically_significant', 'current_p_value'),
    )


class ABTestAssignment(BaseModelWithAudit):
    """
    User assignment tracking for A/B tests with consistent hashing.

    Tracks user assignments to test groups ensuring consistent assignment
    across sessions and providing audit trail for test participation.
    """
    __tablename__ = "ab_test_assignments"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Test and user identification
    test_id = Column(UUID(as_uuid=True), ForeignKey("ab_tests.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(Integer, nullable=False, index=True)  # Can be null for anonymous users
    session_id = Column(String(100), nullable=True, index=True)  # For anonymous tracking

    # Assignment details
    assigned_group = Column(String(1), nullable=False, index=True)  # 'A' or 'B'
    assigned_strategy = Column(String(30), nullable=False, index=True)
    assignment_hash = Column(String(32), nullable=False)  # MD5 hash for consistency
    assignment_value = Column(Float, nullable=False)  # 0.0 to 1.0 for debugging

    # Assignment context
    assignment_date = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 support
    user_agent = Column(Text, nullable=True)
    country_code = Column(String(2), nullable=True, index=True)
    is_vpn_detected = Column(Boolean, nullable=False, default=False)

    # Assignment metadata
    assignment_metadata = Column(JSONB, nullable=True)  # Additional context

    # Relationships
    test = relationship("ABTest", back_populates="assignments")
    results = relationship("ABTestResult", back_populates="assignment", cascade="all, delete-orphan")

    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint('test_id', 'user_id', name='uq_test_user_assignment'),
        Index('idx_assignment_test_group', 'test_id', 'assigned_group'),
        Index('idx_assignment_strategy', 'assigned_strategy', 'assignment_date'),
        Index('idx_assignment_country_vpn', 'country_code', 'is_vpn_detected'),
    )


class ABTestResult(BaseModelWithAudit):
    """
    Test result tracking and performance metrics.

    Stores individual test results and outcomes for statistical analysis
    and performance comparison between test groups.
    """
    __tablename__ = "ab_test_results"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Test and assignment identification
    test_id = Column(UUID(as_uuid=True), ForeignKey("ab_tests.id", ondelete="CASCADE"), nullable=False, index=True)
    assignment_id = Column(UUID(as_uuid=True), ForeignKey("ab_test_assignments.id", ondelete="CASCADE"), nullable=False, index=True)

    # Result identification
    payment_id = Column(Integer, nullable=True, index=True)  # Associated payment
    transaction_id = Column(String(100), nullable=True, index=True)

    # Test outcome
    is_conversion = Column(Boolean, nullable=False, default=False, index=True)
    conversion_value = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    conversion_currency = Column(String(3), nullable=True)

    # Performance metrics
    processing_time_ms = Column(Float, nullable=True)
    response_time_ms = Column(Float, nullable=True)
    success_rate = Column(Float, nullable=True)  # For this specific result

    # Provider and routing information
    selected_provider = Column(String(20), nullable=True, index=True)
    routing_decision = Column(String(30), nullable=True, index=True)
    routing_confidence = Column(Float, nullable=True)

    # Geographic and VPN context
    country_code = Column(String(2), nullable=True, index=True)
    is_vpn_detected = Column(Boolean, nullable=False, default=False)
    vpn_confidence_score = Column(Float, nullable=True)

    # Timing information
    result_timestamp = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)
    test_exposure_time_seconds = Column(Float, nullable=True)  # Time from assignment to result

    # Additional metrics
    error_code = Column(String(20), nullable=True)
    error_message = Column(Text, nullable=True)
    additional_metrics = Column(JSONB, nullable=True)  # Flexible metrics storage

    # Relationships
    test = relationship("ABTest", back_populates="results")
    assignment = relationship("ABTestAssignment", back_populates="results")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_result_test_conversion', 'test_id', 'is_conversion', 'result_timestamp'),
        Index('idx_result_assignment_outcome', 'assignment_id', 'is_conversion'),
        Index('idx_result_provider_country', 'selected_provider', 'country_code'),
        Index('idx_result_performance', 'processing_time_ms', 'response_time_ms'),
        Index('idx_result_vpn_impact', 'is_vpn_detected', 'is_conversion'),
    )


class ABTestAnalysis(BaseModelWithAudit):
    """
    Statistical analysis and significance validation for A/B tests.

    Stores comprehensive statistical analysis results including significance testing,
    confidence intervals, effect sizes, and recommendations for test decisions.
    """
    __tablename__ = "ab_test_analyses"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Test identification
    test_id = Column(UUID(as_uuid=True), ForeignKey("ab_tests.id", ondelete="CASCADE"), nullable=False, index=True)

    # Analysis metadata
    analysis_date = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)
    analysis_type = Column(String(30), nullable=False, default="statistical_significance")
    analysis_period_start = Column(DateTime(timezone=True), nullable=False)
    analysis_period_end = Column(DateTime(timezone=True), nullable=False)

    # Sample sizes
    sample_size_a = Column(Integer, nullable=False, default=0)
    sample_size_b = Column(Integer, nullable=False, default=0)
    total_sample_size = Column(Integer, nullable=False, default=0)

    # Conversion metrics
    conversions_a = Column(Integer, nullable=False, default=0)
    conversions_b = Column(Integer, nullable=False, default=0)
    conversion_rate_a = Column(Float, nullable=False, default=0.0)
    conversion_rate_b = Column(Float, nullable=False, default=0.0)

    # Statistical analysis results
    effect_size = Column(Float, nullable=True)  # Difference in conversion rates
    relative_effect_size = Column(Float, nullable=True)  # Percentage improvement
    p_value = Column(Float, nullable=True)
    confidence_level = Column(Float, nullable=False, default=0.95)
    confidence_interval_lower = Column(Float, nullable=True)
    confidence_interval_upper = Column(Float, nullable=True)

    # Statistical significance
    is_statistically_significant = Column(Boolean, nullable=False, default=False)
    significance_level = Column(String(30), nullable=True)
    statistical_power = Column(Float, nullable=True)

    # Performance metrics comparison
    avg_processing_time_a = Column(Float, nullable=True)
    avg_processing_time_b = Column(Float, nullable=True)
    processing_time_improvement = Column(Float, nullable=True)

    # Revenue and business impact
    total_revenue_a = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    total_revenue_b = Column(Numeric(15, 2), nullable=False, default=Decimal("0.00"))
    revenue_per_user_a = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))
    revenue_per_user_b = Column(Numeric(10, 2), nullable=False, default=Decimal("0.00"))
    projected_revenue_impact = Column(Numeric(15, 2), nullable=True)

    # Test recommendations
    recommendation = Column(String(50), nullable=True)  # implement_a, implement_b, continue_testing, stop_test
    recommendation_confidence = Column(String(20), nullable=True)  # low, medium, high
    recommendation_reason = Column(Text, nullable=True)

    # Geographic and segment analysis
    geographic_breakdown = Column(JSONB, nullable=True)  # Country-specific results
    vpn_impact_analysis = Column(JSONB, nullable=True)  # VPN vs non-VPN performance
    segment_analysis = Column(JSONB, nullable=True)  # User segment breakdown

    # Quality metrics
    data_quality_score = Column(Float, nullable=False, default=1.0)
    outlier_detection_results = Column(JSONB, nullable=True)
    bias_detection_results = Column(JSONB, nullable=True)

    # Additional analysis metadata
    analysis_methodology = Column(String(50), nullable=False, default="two_proportion_z_test")
    analysis_parameters = Column(JSONB, nullable=True)
    analysis_notes = Column(Text, nullable=True)

    # Relationships
    test = relationship("ABTest", back_populates="analyses")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_analysis_test_date', 'test_id', 'analysis_date'),
        Index('idx_analysis_significance', 'is_statistically_significant', 'p_value'),
        Index('idx_analysis_recommendation', 'recommendation', 'recommendation_confidence'),
        Index('idx_analysis_effect_size', 'effect_size', 'relative_effect_size'),
        Index('idx_analysis_sample_size', 'total_sample_size', 'analysis_date'),
    )
