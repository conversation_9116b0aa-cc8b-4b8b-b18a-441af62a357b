# Culture Connect PWA Vendor Portal API Documentation

**Target Audience**: PWA developers and vendor portal integrators  
**API Version**: v1  
**Base URL**: `https://api.cultureconnect.ng/api/v1`  
**Documentation Version**: 1.0.0  
**Last Updated**: 2025-02-01

## 🏢 Overview

The Culture Connect PWA Vendor Portal API provides comprehensive endpoints for vendor-focused Progressive Web Applications, enabling service providers to manage their offerings, track bookings, analyze performance, and engage with customers. This API is optimized for vendor dashboard functionality with real-time updates and comprehensive analytics.

### Key Features
- **Vendor Dashboard**: Complete business management interface
- **Service Management**: Create, update, and manage cultural experiences
- **Booking Management**: Track and manage customer bookings
- **Analytics & Reporting**: Comprehensive business intelligence
- **Real-time Communication**: WebSocket integration for live updates
- **File Management**: Media upload and management capabilities
- **RBAC Integration**: Role-based access control for vendor teams

## 🔐 Authentication & RBAC

### Vendor Authentication Flow

The PWA Vendor Portal uses JWT authentication with role-based access control (RBAC) for granular permissions management.

#### 1. Vendor Registration
```http
POST /auth/vendor/register
Content-Type: application/json

{
  "business_info": {
    "business_name": "Lagos Cultural Heritage",
    "business_type": "cultural_tour_operator",
    "registration_number": "RC123456789",
    "tax_id": "TIN987654321",
    "business_address": {
      "street": "123 Cultural Street",
      "city": "Lagos",
      "state": "Lagos State",
      "country": "Nigeria",
      "postal_code": "100001"
    }
  },
  "contact_info": {
    "primary_contact": {
      "first_name": "Adebayo",
      "last_name": "Ogundimu",
      "email": "<EMAIL>",
      "phone": "+2341234567890",
      "position": "Managing Director"
    },
    "business_phone": "+2341234567890",
    "business_email": "<EMAIL>",
    "website": "https://lagosculturalheritage.ng"
  },
  "credentials": {
    "email": "<EMAIL>",
    "password": "SecureVendorPassword123!",
    "confirm_password": "SecureVendorPassword123!"
  },
  "verification_documents": [
    {
      "type": "business_registration",
      "file_url": "https://uploads.cultureconnect.ng/docs/business_reg_123.pdf"
    },
    {
      "type": "tax_certificate",
      "file_url": "https://uploads.cultureconnect.ng/docs/tax_cert_123.pdf"
    }
  ],
  "terms_accepted": true,
  "marketing_consent": false
}
```

**Response (201 Created):**
```json
{
  "vendor": {
    "id": "vendor_456",
    "business_name": "Lagos Cultural Heritage",
    "status": "pending_verification",
    "verification_status": {
      "documents": "pending_review",
      "business_verification": "pending",
      "estimated_approval_days": 3
    },
    "contact_info": {
      "primary_contact": {
        "first_name": "Adebayo",
        "last_name": "Ogundimu",
        "email": "<EMAIL>"
      }
    },
    "created_at": "2025-02-01T10:00:00Z"
  },
  "message": "Vendor registration submitted. Please check your email for verification instructions."
}
```

#### 2. Vendor Login with RBAC
```http
POST /auth/vendor/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecureVendorPassword123!"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "vendor": {
    "id": "vendor_456",
    "business_name": "Lagos Cultural Heritage",
    "status": "active",
    "verification_status": {
      "documents": "approved",
      "business_verification": "verified",
      "verified_at": "2025-01-28T14:30:00Z"
    },
    "role": "vendor_admin",
    "permissions": [
      "experience_create",
      "experience_manage",
      "booking_view",
      "booking_manage",
      "analytics_view",
      "team_manage",
      "payment_view"
    ],
    "team_members": [
      {
        "id": "user_789",
        "name": "Funmi Adebayo",
        "email": "<EMAIL>",
        "role": "experience_manager",
        "permissions": ["experience_create", "experience_manage", "booking_view"]
      }
    ]
  }
}
```

### RBAC Permission System

#### Available Roles
- **vendor_admin**: Full access to all vendor features
- **experience_manager**: Manage experiences and view bookings
- **booking_coordinator**: Manage bookings and customer communication
- **analytics_viewer**: View reports and analytics only
- **content_editor**: Manage experience content and media

#### Permission Matrix
| Permission | Admin | Experience Mgr | Booking Coord | Analytics | Content |
|------------|-------|----------------|---------------|-----------|---------|
| experience_create | ✅ | ✅ | ❌ | ❌ | ✅ |
| experience_manage | ✅ | ✅ | ❌ | ❌ | ✅ |
| booking_view | ✅ | ✅ | ✅ | ❌ | ❌ |
| booking_manage | ✅ | ❌ | ✅ | ❌ | ❌ |
| analytics_view | ✅ | ✅ | ✅ | ✅ | ❌ |
| team_manage | ✅ | ❌ | ❌ | ❌ | ❌ |
| payment_view | ✅ | ❌ | ✅ | ✅ | ❌ |

### PWA Authentication Integration

```javascript
// vendor-auth.js
class VendorAuthService {
  constructor() {
    this.baseURL = 'https://api.cultureconnect.ng/api/v1';
    this.setupAxiosInterceptors();
  }

  async login(email, password) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/vendor/login`, {
        email,
        password,
      });

      const { access_token, refresh_token, vendor } = response.data;
      
      // Store tokens in localStorage for PWA
      localStorage.setItem('vendor_access_token', access_token);
      localStorage.setItem('vendor_refresh_token', refresh_token);
      localStorage.setItem('vendor_info', JSON.stringify(vendor));
      
      // Update axios default headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      return { vendor, tokens: { access_token, refresh_token } };
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  async checkPermission(permission) {
    const vendorInfo = this.getVendorInfo();
    return vendorInfo?.permissions?.includes(permission) || false;
  }

  async requirePermission(permission) {
    if (!await this.checkPermission(permission)) {
      throw new Error(`Insufficient permissions. Required: ${permission}`);
    }
  }

  getVendorInfo() {
    const vendorInfo = localStorage.getItem('vendor_info');
    return vendorInfo ? JSON.parse(vendorInfo) : null;
  }

  setupAxiosInterceptors() {
    // Request interceptor
    axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('vendor_access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for token refresh
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshToken();
            const token = localStorage.getItem('vendor_access_token');
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axios(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/vendor/login';
            return Promise.reject(refreshError);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('vendor_refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await axios.post(`${this.baseURL}/auth/refresh`, {
      refresh_token: refreshToken,
    });

    const { access_token, refresh_token: newRefreshToken } = response.data;
    
    localStorage.setItem('vendor_access_token', access_token);
    localStorage.setItem('vendor_refresh_token', newRefreshToken);
    
    axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
  }

  logout() {
    localStorage.removeItem('vendor_access_token');
    localStorage.removeItem('vendor_refresh_token');
    localStorage.removeItem('vendor_info');
    delete axios.defaults.headers.common['Authorization'];
  }

  handleAuthError(error) {
    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 401:
          return new Error('Invalid credentials');
        case 403:
          return new Error('Account not verified or suspended');
        case 422:
          return new Error(data.error?.details?.[0]?.message || 'Validation error');
        default:
          return new Error(data.error?.message || 'Authentication failed');
      }
    }
    return new Error('Network error. Please check your connection.');
  }
}

export default new VendorAuthService();
```

## 🎯 Experience Management

### Create Experience
```http
POST /vendor/experiences
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "basic_info": {
    "title": "Traditional Yoruba Cultural Immersion",
    "short_description": "Authentic 3-hour cultural experience in the heart of Lagos",
    "long_description": "Immerse yourself in the rich traditions of Yoruba culture with our comprehensive 3-hour experience. Led by expert cultural historians and traditional practitioners, this journey takes you through ancient customs, traditional crafts, storytelling, and authentic cuisine. Perfect for cultural enthusiasts and travelers seeking genuine Nigerian experiences.",
    "category": "cultural_tour",
    "subcategory": "traditional_culture",
    "tags": ["yoruba", "traditional", "cultural", "educational", "authentic", "lagos"],
    "language": "en",
    "difficulty_level": "beginner",
    "age_restrictions": {
      "minimum_age": 8,
      "maximum_age": null,
      "requires_adult_supervision": true
    }
  },
  "location": {
    "venue_name": "Lagos Cultural Heritage Center",
    "address": {
      "street": "123 Cultural Street, Lagos Island",
      "city": "Lagos",
      "state": "Lagos State",
      "country": "Nigeria",
      "postal_code": "100001"
    },
    "coordinates": {
      "latitude": 6.5244,
      "longitude": 3.3792
    },
    "meeting_point": {
      "name": "Main Entrance",
      "description": "Meet at the main entrance near the traditional fountain",
      "landmark": "Opposite National Theatre"
    },
    "accessibility": {
      "wheelchair_accessible": true,
      "parking_available": true,
      "public_transport_nearby": true
    }
  },
  "scheduling": {
    "duration_hours": 3,
    "duration_minutes": 0,
    "time_slots": [
      {"start_time": "09:00", "end_time": "12:00"},
      {"start_time": "14:00", "end_time": "17:00"}
    ],
    "availability": {
      "days_of_week": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"],
      "seasonal_availability": {
        "available_months": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        "blackout_dates": ["2025-12-25", "2025-01-01"]
      },
      "advance_booking_days": 1,
      "max_advance_booking_days": 90
    },
    "capacity": {
      "minimum_participants": 2,
      "maximum_participants": 15,
      "optimal_group_size": 8
    }
  },
  "pricing": {
    "base_price": 15000,
    "currency": "NGN",
    "pricing_model": "per_person",
    "child_pricing": {
      "enabled": true,
      "age_threshold": 12,
      "discount_percentage": 25
    },
    "group_discounts": [
      {"minimum_size": 5, "discount_percentage": 10},
      {"minimum_size": 10, "discount_percentage": 20}
    ],
    "seasonal_pricing": [
      {
        "name": "Peak Season",
        "start_date": "2025-12-01",
        "end_date": "2025-01-15",
        "price_multiplier": 1.3
      }
    ]
  },
  "inclusions": [
    "Professional cultural guide",
    "Traditional craft materials",
    "Authentic refreshments",
    "Cultural performance",
    "Certificate of participation"
  ],
  "exclusions": [
    "Transportation to venue",
    "Personal expenses",
    "Gratuities",
    "Photography services"
  ],
  "requirements": [
    "Comfortable walking shoes",
    "Respect for cultural customs",
    "Basic English understanding"
  ],
  "cancellation_policy": {
    "free_cancellation_hours": 24,
    "partial_refund_hours": 12,
    "no_refund_hours": 2,
    "vendor_cancellation_policy": "Full refund if cancelled by vendor"
  }
}
```

**Response (201 Created):**
```json
{
  "experience": {
    "id": "exp_789",
    "title": "Traditional Yoruba Cultural Immersion",
    "status": "draft",
    "vendor": {
      "id": "vendor_456",
      "business_name": "Lagos Cultural Heritage"
    },
    "basic_info": {
      "title": "Traditional Yoruba Cultural Immersion",
      "short_description": "Authentic 3-hour cultural experience in the heart of Lagos",
      "category": "cultural_tour",
      "subcategory": "traditional_culture",
      "tags": ["yoruba", "traditional", "cultural", "educational", "authentic", "lagos"]
    },
    "location": {
      "venue_name": "Lagos Cultural Heritage Center",
      "coordinates": {
        "latitude": 6.5244,
        "longitude": 3.3792
      }
    },
    "scheduling": {
      "duration_hours": 3,
      "time_slots": [
        {"start_time": "09:00", "end_time": "12:00"},
        {"start_time": "14:00", "end_time": "17:00"}
      ],
      "capacity": {
        "minimum_participants": 2,
        "maximum_participants": 15
      }
    },
    "pricing": {
      "base_price": 15000,
      "currency": "NGN",
      "pricing_model": "per_person"
    },
    "media": {
      "featured_image": null,
      "gallery": [],
      "videos": []
    },
    "approval_status": {
      "status": "pending_review",
      "submitted_at": null,
      "reviewed_at": null,
      "reviewer_notes": null
    },
    "created_at": "2025-02-01T10:00:00Z",
    "updated_at": "2025-02-01T10:00:00Z"
  },
  "next_steps": [
    "Add media (images and videos)",
    "Review and submit for approval",
    "Set up availability calendar"
  ]
}
```

### Get Vendor Experiences
```http
GET /vendor/experiences?status={status}&category={category}&page={page}&limit={limit}
Authorization: Bearer {access_token}
```

**Parameters:**
- `status` (optional): Filter by status (draft, pending_review, approved, rejected, active, inactive)
- `category` (optional): Filter by category
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 20, max: 100)

**Response (200 OK):**
```json
{
  "experiences": [
    {
      "id": "exp_789",
      "title": "Traditional Yoruba Cultural Immersion",
      "status": "active",
      "category": "cultural_tour",
      "featured_image": "https://cdn.cultureconnect.ng/experiences/exp_789/featured.jpg",
      "pricing": {
        "base_price": 15000,
        "currency": "NGN"
      },
      "stats": {
        "total_bookings": 45,
        "this_month_bookings": 12,
        "average_rating": 4.8,
        "review_count": 23
      },
      "next_available": "2025-02-05T09:00:00Z",
      "created_at": "2025-01-15T10:00:00Z",
      "updated_at": "2025-02-01T08:30:00Z"
    }
  ],
  "pagination": {
    "total": 8,
    "page": 1,
    "per_page": 20,
    "total_pages": 1
  },
  "summary": {
    "total_experiences": 8,
    "active": 6,
    "draft": 1,
    "pending_review": 1,
    "inactive": 0
  }
}
```

### Update Experience
```http
PUT /vendor/experiences/{experience_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "basic_info": {
    "title": "Enhanced Traditional Yoruba Cultural Immersion",
    "short_description": "Authentic 3-hour cultural experience with hands-on activities"
  },
  "pricing": {
    "base_price": 18000,
    "currency": "NGN"
  }
}
```

### Upload Experience Media
```http
POST /vendor/experiences/{experience_id}/media
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

{
  "file": [binary_file_data],
  "media_type": "image",
  "caption": "Traditional Yoruba craft demonstration",
  "is_featured": true,
  "display_order": 1
}
```

**Response (201 Created):**
```json
{
  "media": {
    "id": "media_123",
    "experience_id": "exp_789",
    "media_type": "image",
    "file_url": "https://cdn.cultureconnect.ng/experiences/exp_789/media_123.jpg",
    "thumbnail_url": "https://cdn.cultureconnect.ng/experiences/exp_789/thumb_media_123.jpg",
    "caption": "Traditional Yoruba craft demonstration",
    "is_featured": true,
    "display_order": 1,
    "file_size": 2048576,
    "dimensions": {
      "width": 1920,
      "height": 1080
    },
    "uploaded_at": "2025-02-01T10:30:00Z"
  }
}
```

## 📅 Booking Management

### Get Vendor Bookings
```http
GET /vendor/bookings?status={status}&date_from={date}&date_to={date}&experience_id={exp_id}&page={page}
Authorization: Bearer {access_token}
```

**Parameters:**
- `status` (optional): Filter by booking status
- `date_from` (optional): Start date filter (YYYY-MM-DD)
- `date_to` (optional): End date filter (YYYY-MM-DD)
- `experience_id` (optional): Filter by specific experience
- `page` (optional): Page number

**Response (200 OK):**
```json
{
  "bookings": [
    {
      "id": "booking_abc123",
      "reference_number": "CC-2025-001234",
      "status": "confirmed",
      "customer": {
        "id": "user_456",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "country": "US"
      },
      "experience": {
        "id": "exp_789",
        "title": "Traditional Yoruba Cultural Immersion",
        "featured_image": "https://cdn.cultureconnect.ng/experiences/exp_789/featured.jpg"
      },
      "booking_details": {
        "date": "2025-02-15",
        "time_slot": "09:00",
        "duration_hours": 3,
        "participant_count": 2,
        "participants": [
          {
            "type": "adult",
            "name": "John Doe",
            "age": 30
          },
          {
            "type": "child",
            "name": "Jane Doe",
            "age": 8
          }
        ],
        "special_requests": "Celebrating anniversary"
      },
      "pricing": {
        "subtotal": 30000,
        "discount": 3000,
        "tax": 2700,
        "total": 29700,
        "currency": "NGN",
        "vendor_commission": 26730,
        "platform_fee": 2970
      },
      "payment": {
        "status": "completed",
        "method": "card",
        "paid_at": "2025-02-01T10:15:00Z"
      },
      "communication": {
        "last_message_at": "2025-02-01T11:00:00Z",
        "unread_messages": 0,
        "customer_rating": null
      },
      "created_at": "2025-02-01T10:00:00Z",
      "updated_at": "2025-02-01T10:15:00Z"
    }
  ],
  "pagination": {
    "total": 156,
    "page": 1,
    "per_page": 20,
    "total_pages": 8
  },
  "summary": {
    "total_bookings": 156,
    "confirmed": 134,
    "pending": 8,
    "completed": 142,
    "cancelled": 6,
    "total_revenue": 4680000,
    "this_month_revenue": 890000
  }
}
```

### Get Booking Details
```http
GET /vendor/bookings/{booking_id}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "id": "booking_abc123",
  "reference_number": "CC-2025-001234",
  "status": "confirmed",
  "customer": {
    "id": "user_456",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "country": "US",
    "profile_image": "https://cdn.cultureconnect.ng/users/user_456/profile.jpg",
    "booking_history": {
      "total_bookings": 3,
      "completed_bookings": 2,
      "average_rating_given": 4.5
    }
  },
  "experience": {
    "id": "exp_789",
    "title": "Traditional Yoruba Cultural Immersion",
    "description": "Authentic 3-hour cultural experience...",
    "meeting_point": {
      "name": "Lagos Cultural Heritage Center",
      "address": "123 Cultural Street, Lagos Island",
      "coordinates": {
        "latitude": 6.5244,
        "longitude": 3.3792
      },
      "instructions": "Meet at the main entrance near the fountain"
    }
  },
  "booking_details": {
    "date": "2025-02-15",
    "time_slot": "09:00",
    "duration_hours": 3,
    "estimated_end_time": "12:00",
    "participant_count": 2,
    "participants": [
      {
        "type": "adult",
        "name": "John Doe",
        "age": 30,
        "dietary_restrictions": ["vegetarian"],
        "special_requirements": "Wheelchair accessible"
      },
      {
        "type": "child",
        "name": "Jane Doe",
        "age": 8,
        "dietary_restrictions": [],
        "special_requirements": null
      }
    ],
    "special_requests": "Celebrating anniversary - please arrange something special",
    "emergency_contact": {
      "name": "Mary Doe",
      "phone": "+1234567891",
      "relationship": "spouse"
    }
  },
  "pricing": {
    "subtotal": 30000,
    "discount": 3000,
    "tax": 2700,
    "total": 29700,
    "currency": "NGN",
    "breakdown": {
      "adult_price": 15000,
      "child_price": 15000,
      "promo_discount": -3000,
      "vat": 2700
    },
    "vendor_earnings": {
      "gross_amount": 29700,
      "platform_fee": 2970,
      "net_amount": 26730,
      "fee_percentage": 10
    }
  },
  "payment": {
    "status": "completed",
    "method": "card",
    "transaction_id": "txn_xyz789",
    "paid_at": "2025-02-01T10:15:00Z",
    "payout_status": "pending",
    "estimated_payout_date": "2025-02-08"
  },
  "communication": {
    "messages": [
      {
        "id": "msg_123",
        "sender": "customer",
        "message": "Looking forward to the experience! Any specific dress code?",
        "timestamp": "2025-02-01T11:00:00Z",
        "read": true
      },
      {
        "id": "msg_124",
        "sender": "vendor",
        "message": "Welcome! Comfortable clothing is recommended. We'll provide traditional attire for the activities.",
        "timestamp": "2025-02-01T11:15:00Z",
        "read": true
      }
    ],
    "unread_count": 0,
    "last_message_at": "2025-02-01T11:15:00Z"
  },
  "status_history": [
    {
      "status": "pending_payment",
      "timestamp": "2025-02-01T10:00:00Z",
      "note": "Booking created, awaiting payment"
    },
    {
      "status": "confirmed",
      "timestamp": "2025-02-01T10:15:00Z",
      "note": "Payment successful, booking confirmed"
    }
  ],
  "actions_available": {
    "can_modify": true,
    "can_cancel": true,
    "can_message": true,
    "can_request_review": false
  },
  "created_at": "2025-02-01T10:00:00Z",
  "updated_at": "2025-02-01T10:15:00Z"
}
```

### Update Booking Status
```http
PATCH /vendor/bookings/{booking_id}/status
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "status": "completed",
  "notes": "Experience completed successfully. Customer was very satisfied.",
  "completion_details": {
    "actual_participants": 2,
    "weather_conditions": "sunny",
    "special_notes": "Customer celebrated anniversary during the experience"
  }
}
```

### Send Message to Customer
```http
POST /vendor/bookings/{booking_id}/messages
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "message": "Thank you for booking with us! We're excited to share our cultural heritage with you. Please arrive 15 minutes early at the main entrance.",
  "message_type": "booking_confirmation",
  "attachments": [
    {
      "type": "document",
      "url": "https://cdn.cultureconnect.ng/docs/preparation_guide.pdf",
      "name": "Experience Preparation Guide"
    }
  ]
}
```

## 📊 Analytics & Business Intelligence

### Get Vendor Dashboard Analytics
```http
GET /vendor/analytics/dashboard?period={period}&timezone={timezone}
Authorization: Bearer {access_token}
```

**Parameters:**
- `period` (optional): Time period (today, week, month, quarter, year, custom)
- `timezone` (optional): Timezone for date calculations (default: vendor's timezone)

**Response (200 OK):**
```json
{
  "period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "timezone": "Africa/Lagos"
  },
  "overview": {
    "total_revenue": 4680000,
    "total_bookings": 156,
    "average_booking_value": 30000,
    "conversion_rate": 0.23,
    "customer_satisfaction": 4.7,
    "growth_metrics": {
      "revenue_growth": 0.15,
      "booking_growth": 0.12,
      "customer_growth": 0.18
    }
  },
  "revenue": {
    "gross_revenue": 4680000,
    "platform_fees": 468000,
    "net_revenue": 4212000,
    "pending_payouts": 890000,
    "paid_out": 3322000,
    "currency": "NGN",
    "daily_breakdown": [
      {
        "date": "2025-01-01",
        "gross_revenue": 150000,
        "net_revenue": 135000,
        "bookings": 5
      }
    ]
  },
  "bookings": {
    "total": 156,
    "confirmed": 134,
    "pending": 8,
    "completed": 142,
    "cancelled": 6,
    "no_show": 2,
    "status_breakdown": {
      "confirmed": 134,
      "pending": 8,
      "completed": 142,
      "cancelled": 6,
      "no_show": 2
    },
    "source_breakdown": {
      "direct": 89,
      "mobile_app": 45,
      "partner_referral": 22
    }
  },
  "experiences": {
    "total_active": 6,
    "top_performing": [
      {
        "id": "exp_789",
        "title": "Traditional Yoruba Cultural Immersion",
        "bookings": 45,
        "revenue": 1350000,
        "rating": 4.8,
        "conversion_rate": 0.28
      }
    ],
    "underperforming": [
      {
        "id": "exp_456",
        "title": "Lagos Street Art Tour",
        "bookings": 3,
        "revenue": 90000,
        "rating": 4.2,
        "conversion_rate": 0.08
      }
    ]
  },
  "customers": {
    "total_unique": 134,
    "new_customers": 89,
    "returning_customers": 45,
    "customer_lifetime_value": 87500,
    "demographics": {
      "age_groups": {
        "18-25": 23,
        "26-35": 45,
        "36-45": 38,
        "46-55": 21,
        "55+": 7
      },
      "countries": {
        "Nigeria": 67,
        "United States": 34,
        "United Kingdom": 18,
        "Canada": 12,
        "Others": 3
      }
    }
  },
  "trends": {
    "peak_booking_hours": ["09:00", "14:00", "16:00"],
    "peak_booking_days": ["Friday", "Saturday", "Sunday"],
    "seasonal_patterns": {
      "high_season": ["December", "January", "July", "August"],
      "low_season": ["March", "April", "September"]
    }
  }
}
```

### Get Revenue Analytics
```http
GET /vendor/analytics/revenue?start_date={start}&end_date={end}&granularity={granularity}
Authorization: Bearer {access_token}
```

**Parameters:**
- `start_date` (required): Start date (YYYY-MM-DD)
- `end_date` (required): End date (YYYY-MM-DD)
- `granularity` (optional): Data granularity (day, week, month)

**Response (200 OK):**
```json
{
  "summary": {
    "total_gross_revenue": 4680000,
    "total_net_revenue": 4212000,
    "total_platform_fees": 468000,
    "average_daily_revenue": 151000,
    "currency": "NGN"
  },
  "breakdown": [
    {
      "period": "2025-01-01",
      "gross_revenue": 150000,
      "net_revenue": 135000,
      "platform_fees": 15000,
      "bookings_count": 5,
      "average_booking_value": 30000
    }
  ],
  "payment_methods": {
    "card": {
      "revenue": 3744000,
      "percentage": 80
    },
    "bank_transfer": {
      "revenue": 936000,
      "percentage": 20
    }
  },
  "experience_performance": [
    {
      "experience_id": "exp_789",
      "title": "Traditional Yoruba Cultural Immersion",
      "revenue": 1350000,
      "bookings": 45,
      "average_value": 30000
    }
  ]
}
```

### Get Customer Analytics
```http
GET /vendor/analytics/customers?period={period}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "overview": {
    "total_customers": 134,
    "new_customers": 89,
    "returning_customers": 45,
    "customer_retention_rate": 0.34,
    "average_customer_lifetime_value": 87500
  },
  "demographics": {
    "age_distribution": {
      "18-25": 23,
      "26-35": 45,
      "36-45": 38,
      "46-55": 21,
      "55+": 7
    },
    "geographic_distribution": {
      "Nigeria": 67,
      "United States": 34,
      "United Kingdom": 18,
      "Canada": 12,
      "Others": 3
    },
    "booking_behavior": {
      "single_booking": 89,
      "multiple_bookings": 45,
      "average_bookings_per_customer": 1.16
    }
  },
  "satisfaction": {
    "average_rating": 4.7,
    "rating_distribution": {
      "5": 89,
      "4": 34,
      "3": 8,
      "2": 2,
      "1": 1
    },
    "review_response_rate": 0.85,
    "net_promoter_score": 72
  },
  "top_customers": [
    {
      "customer_id": "user_123",
      "name": "Sarah Johnson",
      "total_bookings": 5,
      "total_spent": 150000,
      "average_rating_given": 4.8,
      "last_booking": "2025-01-28"
    }
  ]
}
```

## 🔔 Real-time WebSocket Integration

### WebSocket Connection for Vendors
```javascript
// vendor-websocket.js
class VendorWebSocketService {
  constructor(vendorId, accessToken) {
    this.vendorId = vendorId;
    this.accessToken = accessToken;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventHandlers = new Map();
  }

  connect() {
    const wsUrl = `wss://api.cultureconnect.ng/api/v1/websocket/vendor/${this.vendorId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = (event) => {
      console.log('Vendor WebSocket connected');
      this.reconnectAttempts = 0;

      // Authenticate the connection
      this.send({
        type: 'authenticate',
        token: this.accessToken
      });
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('Vendor WebSocket disconnected');
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('Vendor WebSocket error:', error);
    };
  }

  handleMessage(message) {
    const { type, data } = message;

    switch (type) {
      case 'new_booking':
        this.handleNewBooking(data);
        break;
      case 'booking_cancelled':
        this.handleBookingCancelled(data);
        break;
      case 'customer_message':
        this.handleCustomerMessage(data);
        break;
      case 'payment_received':
        this.handlePaymentReceived(data);
        break;
      case 'review_submitted':
        this.handleReviewSubmitted(data);
        break;
      case 'experience_approved':
        this.handleExperienceApproved(data);
        break;
      default:
        console.log('Unknown message type:', type);
    }

    // Trigger registered event handlers
    if (this.eventHandlers.has(type)) {
      this.eventHandlers.get(type).forEach(handler => handler(data));
    }
  }

  handleNewBooking(data) {
    // Show notification
    this.showNotification({
      title: 'New Booking Received!',
      body: `${data.customer_name} booked ${data.experience_title}`,
      icon: '/icons/booking-icon.png',
      tag: 'new-booking',
      data: data
    });

    // Update dashboard counters
    this.updateDashboardCounters();

    // Play notification sound
    this.playNotificationSound();
  }

  handleCustomerMessage(data) {
    this.showNotification({
      title: 'New Customer Message',
      body: `Message from ${data.customer_name}: ${data.message.substring(0, 50)}...`,
      icon: '/icons/message-icon.png',
      tag: 'customer-message',
      data: data
    });

    // Update unread message counter
    this.updateUnreadMessageCounter(data.booking_id);
  }

  handlePaymentReceived(data) {
    this.showNotification({
      title: 'Payment Received',
      body: `₦${data.amount.toLocaleString()} received for booking ${data.booking_reference}`,
      icon: '/icons/payment-icon.png',
      tag: 'payment-received',
      data: data
    });

    // Update revenue dashboard
    this.updateRevenueDashboard();
  }

  showNotification(options) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon,
        tag: options.tag,
        data: options.data,
        requireInteraction: true
      });

      notification.onclick = () => {
        window.focus();
        // Navigate to relevant page based on notification type
        this.handleNotificationClick(options.tag, options.data);
        notification.close();
      };
    }
  }

  handleNotificationClick(tag, data) {
    switch (tag) {
      case 'new-booking':
        window.location.href = `/vendor/bookings/${data.booking_id}`;
        break;
      case 'customer-message':
        window.location.href = `/vendor/bookings/${data.booking_id}#messages`;
        break;
      case 'payment-received':
        window.location.href = `/vendor/revenue`;
        break;
    }
  }

  // Event subscription methods
  on(eventType, handler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType).push(handler);
  }

  off(eventType, handler) {
    if (this.eventHandlers.has(eventType)) {
      const handlers = this.eventHandlers.get(eventType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Usage in PWA
const vendorWS = new VendorWebSocketService(vendorInfo.id, accessToken);
vendorWS.connect();

// Subscribe to specific events
vendorWS.on('new_booking', (data) => {
  // Update booking list in real-time
  updateBookingsList(data);
});

vendorWS.on('customer_message', (data) => {
  // Update message thread
  updateMessageThread(data.booking_id, data.message);
});
```

## 📱 PWA-Specific Features

### Service Worker Integration
```javascript
// sw.js - Service Worker for offline functionality
const CACHE_NAME = 'culture-connect-vendor-v1.0.0';
const API_CACHE_NAME = 'culture-connect-api-v1.0.0';

const STATIC_ASSETS = [
  '/',
  '/vendor/dashboard',
  '/vendor/bookings',
  '/vendor/experiences',
  '/vendor/analytics',
  '/css/app.css',
  '/js/app.js',
  '/icons/icon-192.png',
  '/icons/icon-512.png'
];

const API_ENDPOINTS_TO_CACHE = [
  '/api/v1/vendor/experiences',
  '/api/v1/vendor/bookings',
  '/api/v1/vendor/analytics/dashboard'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_ASSETS))
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith('/api/v1/')) {
    event.respondWith(handleApiRequest(request));
  }
  // Handle static assets
  else {
    event.respondWith(handleStaticRequest(request));
  }
});

async function handleApiRequest(request) {
  const url = new URL(request.url);

  // For GET requests, try cache first, then network
  if (request.method === 'GET') {
    try {
      // Try network first for fresh data
      const networkResponse = await fetch(request);

      if (networkResponse.ok) {
        // Cache successful responses
        const cache = await caches.open(API_CACHE_NAME);
        cache.put(request, networkResponse.clone());
        return networkResponse;
      }
    } catch (error) {
      console.log('Network failed, trying cache:', error);
    }

    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page for failed requests
    return new Response(JSON.stringify({
      error: {
        code: 'OFFLINE',
        message: 'You are offline. Please check your connection.'
      }
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // For POST/PUT/DELETE, always try network
  try {
    return await fetch(request);
  } catch (error) {
    // Store failed requests for retry when online
    await storeFailedRequest(request);

    return new Response(JSON.stringify({
      error: {
        code: 'OFFLINE_QUEUED',
        message: 'Request queued for when you come back online.'
      }
    }), {
      status: 202,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function handleStaticRequest(request) {
  // Cache first strategy for static assets
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }
    throw error;
  }
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'retry-failed-requests') {
    event.waitUntil(retryFailedRequests());
  }
});

async function storeFailedRequest(request) {
  const db = await openDB();
  const transaction = db.transaction(['failed_requests'], 'readwrite');
  const store = transaction.objectStore('failed_requests');

  await store.add({
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries()),
    body: await request.text(),
    timestamp: Date.now()
  });
}

async function retryFailedRequests() {
  const db = await openDB();
  const transaction = db.transaction(['failed_requests'], 'readwrite');
  const store = transaction.objectStore('failed_requests');
  const requests = await store.getAll();

  for (const req of requests) {
    try {
      const response = await fetch(req.url, {
        method: req.method,
        headers: req.headers,
        body: req.body
      });

      if (response.ok) {
        await store.delete(req.id);
      }
    } catch (error) {
      console.log('Retry failed for request:', req.url);
    }
  }
}
```

### Push Notifications for Vendors
```javascript
// push-notifications.js
class VendorPushNotificationService {
  constructor() {
    this.registration = null;
    this.subscription = null;
  }

  async initialize() {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        this.registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered');

        await this.requestPermission();
        await this.subscribeToNotifications();
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  async requestPermission() {
    const permission = await Notification.requestPermission();
    if (permission !== 'granted') {
      throw new Error('Notification permission denied');
    }
  }

  async subscribeToNotifications() {
    try {
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
      });

      // Send subscription to server
      await this.sendSubscriptionToServer(this.subscription);
    } catch (error) {
      console.error('Failed to subscribe to notifications:', error);
    }
  }

  async sendSubscriptionToServer(subscription) {
    const response = await fetch('/api/v1/vendor/notifications/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('vendor_access_token')}`
      },
      body: JSON.stringify({
        subscription: subscription,
        notification_preferences: {
          new_bookings: true,
          payment_received: true,
          customer_messages: true,
          experience_approved: true,
          review_submitted: true
        }
      })
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }
  }

  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

// Initialize push notifications
const pushService = new VendorPushNotificationService();
pushService.initialize();
```

### Offline Data Management
```javascript
// offline-data-manager.js
class OfflineDataManager {
  constructor() {
    this.dbName = 'CultureConnectVendorDB';
    this.dbVersion = 1;
    this.db = null;
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // Experiences store
        if (!db.objectStoreNames.contains('experiences')) {
          const experienceStore = db.createObjectStore('experiences', { keyPath: 'id' });
          experienceStore.createIndex('status', 'status', { unique: false });
          experienceStore.createIndex('category', 'category', { unique: false });
        }

        // Bookings store
        if (!db.objectStoreNames.contains('bookings')) {
          const bookingStore = db.createObjectStore('bookings', { keyPath: 'id' });
          bookingStore.createIndex('status', 'status', { unique: false });
          bookingStore.createIndex('date', 'booking_details.date', { unique: false });
        }

        // Analytics cache store
        if (!db.objectStoreNames.contains('analytics_cache')) {
          const analyticsStore = db.createObjectStore('analytics_cache', { keyPath: 'key' });
          analyticsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Pending actions store
        if (!db.objectStoreNames.contains('pending_actions')) {
          const actionsStore = db.createObjectStore('pending_actions', {
            keyPath: 'id',
            autoIncrement: true
          });
          actionsStore.createIndex('type', 'type', { unique: false });
          actionsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  async cacheExperiences(experiences) {
    const transaction = this.db.transaction(['experiences'], 'readwrite');
    const store = transaction.objectStore('experiences');

    for (const experience of experiences) {
      await store.put({
        ...experience,
        cached_at: Date.now(),
        expires_at: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      });
    }
  }

  async getCachedExperiences() {
    const transaction = this.db.transaction(['experiences'], 'readonly');
    const store = transaction.objectStore('experiences');
    const experiences = await store.getAll();

    // Filter out expired entries
    const now = Date.now();
    return experiences.filter(exp => exp.expires_at > now);
  }

  async cacheBookings(bookings) {
    const transaction = this.db.transaction(['bookings'], 'readwrite');
    const store = transaction.objectStore('bookings');

    for (const booking of bookings) {
      await store.put({
        ...booking,
        cached_at: Date.now()
      });
    }
  }

  async getCachedBookings(filters = {}) {
    const transaction = this.db.transaction(['bookings'], 'readonly');
    const store = transaction.objectStore('bookings');

    let bookings = await store.getAll();

    // Apply filters
    if (filters.status) {
      bookings = bookings.filter(b => b.status === filters.status);
    }
    if (filters.date_from) {
      bookings = bookings.filter(b => b.booking_details.date >= filters.date_from);
    }
    if (filters.date_to) {
      bookings = bookings.filter(b => b.booking_details.date <= filters.date_to);
    }

    return bookings;
  }

  async addPendingAction(action) {
    const transaction = this.db.transaction(['pending_actions'], 'readwrite');
    const store = transaction.objectStore('pending_actions');

    await store.add({
      ...action,
      timestamp: Date.now(),
      retry_count: 0
    });
  }

  async getPendingActions() {
    const transaction = this.db.transaction(['pending_actions'], 'readonly');
    const store = transaction.objectStore('pending_actions');
    return await store.getAll();
  }

  async removePendingAction(id) {
    const transaction = this.db.transaction(['pending_actions'], 'readwrite');
    const store = transaction.objectStore('pending_actions');
    await store.delete(id);
  }

  async syncPendingActions() {
    const actions = await this.getPendingActions();

    for (const action of actions) {
      try {
        await this.executeAction(action);
        await this.removePendingAction(action.id);
      } catch (error) {
        console.error('Failed to sync action:', action, error);
        // Increment retry count
        action.retry_count++;
        if (action.retry_count < 3) {
          const transaction = this.db.transaction(['pending_actions'], 'readwrite');
          const store = transaction.objectStore('pending_actions');
          await store.put(action);
        } else {
          // Remove after 3 failed attempts
          await this.removePendingAction(action.id);
        }
      }
    }
  }

  async executeAction(action) {
    const token = localStorage.getItem('vendor_access_token');

    const response = await fetch(action.url, {
      method: action.method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...action.headers
      },
      body: action.body ? JSON.stringify(action.body) : undefined
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }
}

// Initialize offline data manager
const offlineDataManager = new OfflineDataManager();
offlineDataManager.initialize();

// Sync when coming back online
window.addEventListener('online', () => {
  offlineDataManager.syncPendingActions();
});
```

## ❌ Error Handling & Status Codes

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| 200 | OK | Successful GET requests |
| 201 | Created | Experience/booking created |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Invalid/expired token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Duplicate resource |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |
| 503 | Service Unavailable | Maintenance mode |

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request data contains validation errors",
    "details": [
      {
        "field": "pricing.base_price",
        "message": "Price must be greater than 0",
        "code": "INVALID_PRICE"
      }
    ],
    "correlation_id": "req_vendor_123456789",
    "timestamp": "2025-02-01T10:00:00Z"
  }
}
```

### Vendor-Specific Error Codes
- `VENDOR_NOT_VERIFIED`: Vendor account not yet verified
- `EXPERIENCE_LIMIT_REACHED`: Maximum experience limit reached
- `BOOKING_MODIFICATION_NOT_ALLOWED`: Booking cannot be modified
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `MEDIA_UPLOAD_FAILED`: File upload failed
- `ANALYTICS_DATA_UNAVAILABLE`: Analytics data not ready

## 📚 Additional Resources

### Postman Collection
- **Vendor API Collection**: `https://api.cultureconnect.ng/docs/postman/vendor-api.json`
- **Environment Variables**: Separate collections for development, staging, and production

### PWA Development Resources
- **PWA Starter Kit**: `https://github.com/culture-connect/vendor-pwa-starter`
- **Component Library**: `https://github.com/culture-connect/vendor-ui-components`
- **Design System**: `https://design.cultureconnect.ng/vendor`

### Integration Guides
- **Authentication Setup**: `https://docs.cultureconnect.ng/vendor/auth-setup`
- **WebSocket Integration**: `https://docs.cultureconnect.ng/vendor/websocket-guide`
- **Offline Functionality**: `https://docs.cultureconnect.ng/vendor/offline-guide`
- **Push Notifications**: `https://docs.cultureconnect.ng/vendor/push-notifications`

### Support & Community
- **API Status**: `https://status.cultureconnect.ng`
- **Developer Portal**: `https://developers.cultureconnect.ng/vendor`
- **Support Email**: `<EMAIL>`
- **Slack Community**: `#culture-connect-vendor-dev`
- **Documentation**: `https://docs.cultureconnect.ng/vendor`

### Changelog
- **v1.0.0** (2025-02-01): Initial vendor portal API release
- **Breaking Changes**: None (initial release)
- **New Features**: Complete vendor management API with RBAC, analytics, and real-time updates
