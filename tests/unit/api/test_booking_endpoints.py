"""
Unit tests for booking API endpoints.

This module provides comprehensive testing for the booking workflow API endpoints
including creation, status management, vendor responses, and availability checking.

Tests cover:
- Booking creation with validation
- Booking listing with role-based access control
- Booking details retrieval with permissions
- Booking updates and status management
- Vendor response processing
- Availability checking
- Booking cancellation

Production-grade test implementation following established testing patterns.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import status

from app.schemas.booking_schemas import BookingResponseSchema

# Import test configuration
from tests.conftest_api import *


@pytest.mark.api
@pytest.mark.booking
class TestBookingEndpoints:
    """Test class for booking API endpoints."""

    @patch('app.services.booking_service.BookingService')
    def test_create_booking_success(
        self,
        mock_booking_service,
        test_client,
        mock_user,
        booking_create_data,
        mock_booking_response,
        auth_headers
    ):
        """Test successful booking creation."""
        # Setup service mock
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.create_booking.return_value = BookingResponseSchema(**mock_booking_response)

        # Make request
        response = test_client.post(
            "/api/v1/bookings/",
            json=booking_create_data,
            headers=auth_headers
        )

        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["booking_reference"] == "BK-2025-001234"
        assert data["status"] == "pending"
        assert data["total_amount"] == "110.00"

    @patch('app.services.booking_service.BookingService')
    def test_create_booking_validation_error(
        self,
        mock_booking_service,
        test_client,
        auth_headers
    ):
        """Test booking creation with validation error."""
        # Setup service mock to raise validation error
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.create_booking.side_effect = Exception("ValidationError: Service not found")

        # Make request with invalid data
        invalid_data = {
            "service_id": 999,  # Non-existent service
            "booking_date": "2025-02-15"
        }

        response = test_client.post(
            "/api/v1/bookings/",
            json=invalid_data,
            headers=auth_headers
        )

        # Assertions - Should be validation error or internal server error
        assert response.status_code in [status.HTTP_422_UNPROCESSABLE_ENTITY, status.HTTP_500_INTERNAL_SERVER_ERROR]

    @patch('app.services.booking_service.BookingService')
    def test_list_bookings_customer(
        self,
        mock_booking_service,
        test_client,
        mock_booking_list_response,
        auth_headers
    ):
        """Test listing bookings for customer."""
        # Setup service mock
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.get_customer_bookings.return_value = mock_booking_list_response

        # Make request
        response = test_client.get(
            "/api/v1/bookings/",
            headers=auth_headers
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total"] == 1
        assert len(data["bookings"]) == 1

    @patch('app.services.booking_service.BookingService')
    def test_get_booking_details_success(
        self,
        mock_booking_service,
        test_client,
        mock_user,
        auth_headers
    ):
        """Test getting booking details successfully."""
        # Setup service mock
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance

        # Mock booking with customer ownership
        mock_booking = MagicMock()
        mock_booking.customer_id = mock_user.id
        mock_booking.vendor_id = 2
        mock_service_instance.get_with_details.return_value = mock_booking

        # Make request
        response = test_client.get(
            "/api/v1/bookings/1",
            headers=auth_headers
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK

    @patch('app.services.booking_service.BookingService')
    def test_get_booking_details_access_denied(
        self,
        mock_booking_service,
        test_client,
        auth_headers
    ):
        """Test access denied for booking details."""
        # Setup service mock
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance

        # Mock booking owned by different customer
        mock_booking = MagicMock()
        mock_booking.customer_id = 999  # Different customer
        mock_booking.vendor_id = 2
        mock_service_instance.get_with_details.return_value = mock_booking

        # Make request
        response = test_client.get(
            "/api/v1/bookings/1",
            headers=auth_headers
        )

        # Assertions
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @patch('app.services.booking_service.BookingService')
    def test_check_availability_success(
        self,
        mock_booking_service,
        test_client,
        auth_headers
    ):
        """Test availability checking successfully."""
        # Setup service mock
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.check_availability.return_value = True

        # Make request
        response = test_client.get(
            "/api/v1/bookings/availability/check",
            params={
                "service_id": 123,
                "booking_date": "2025-02-15",
                "booking_time": "14:30:00",
                "duration_hours": 2.5
            },
            headers=auth_headers
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["available"] is True

    def test_create_booking_unauthorized(self, test_client):
        """Test booking creation without authentication."""
        booking_data = {
            "service_id": 123,
            "booking_date": "2025-02-15"
        }

        response = test_client.post("/api/v1/bookings/", json=booking_data)

        # Should require authentication
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
