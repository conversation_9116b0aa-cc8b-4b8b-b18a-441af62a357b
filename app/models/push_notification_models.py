"""
Push Notification models for Culture Connect Backend API.

This module defines comprehensive push notification management models including:
- DeviceToken: FCM device token management with platform-specific handling
- NotificationTemplate: Reusable push notification templates with personalization
- NotificationDelivery: Push notification delivery tracking with status and analytics
- NotificationPreference: User push notification preferences and granular controls
- NotificationQueue: Queued notification management for batch processing and scheduling

Implements Task 2.3.2 requirements for push notification system implementation with
production-grade PostgreSQL optimization and seamless integration with existing
authentication, user management, and email service infrastructure.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Enum as SQLEnum, UniqueConstraint, Numeric
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.db.base import Base


class DevicePlatform(str, Enum):
    """Enumeration for supported device platforms."""
    IOS = "ios"
    ANDROID = "android"
    WEB = "web"


class NotificationStatus(str, Enum):
    """Enumeration for notification delivery status."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class NotificationPriority(str, Enum):
    """Enumeration for notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationCategory(str, Enum):
    """Enumeration for notification categories."""
    AUTHENTICATION = "authentication"
    BOOKING = "booking"
    PAYMENT = "payment"
    PROMOTIONAL = "promotional"
    SYSTEM = "system"
    SECURITY = "security"
    SOCIAL = "social"
    REMINDER = "reminder"


class NotificationFrequency(str, Enum):
    """Enumeration for notification frequency preferences."""
    IMMEDIATE = "immediate"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    DISABLED = "disabled"


class DeviceToken(Base):
    """
    Device token model for FCM device registration and management.

    Stores FCM device tokens with platform-specific information and
    validation status for push notification delivery.
    """
    __tablename__ = "device_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # Device information
    token = Column(String(500), nullable=False, unique=True, index=True)
    platform = Column(SQLEnum(DevicePlatform), nullable=False, index=True)
    device_id = Column(String(255), nullable=True, index=True)
    device_name = Column(String(255), nullable=True)
    app_version = Column(String(50), nullable=True)
    os_version = Column(String(50), nullable=True)

    # Token status and validation
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    is_validated = Column(Boolean, nullable=False, default=False)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    validation_attempts = Column(Integer, nullable=False, default=0)

    # Metadata
    registration_ip = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    device_metadata = Column(JSON, nullable=False, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="device_tokens")
    notification_deliveries = relationship(
        "NotificationDelivery",
        back_populates="device_token",
        cascade="all, delete-orphan"
    )

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_device_token_user_platform', 'user_id', 'platform'),
        Index('idx_device_token_active_validated', 'is_active', 'is_validated'),
        Index('idx_device_token_last_used', 'last_used_at'),
        UniqueConstraint('user_id', 'device_id', 'platform', name='uq_device_token_user_device'),
    )

    def __repr__(self) -> str:
        return f"<DeviceToken(id={self.id}, user_id={self.user_id}, platform='{self.platform.value}', active={self.is_active})>"


class NotificationTemplate(Base):
    """
    Notification template model for reusable push notification content.

    Stores notification templates with support for personalization variables,
    platform-specific customization, and A/B testing capabilities.
    """
    __tablename__ = "notification_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    category = Column(SQLEnum(NotificationCategory), nullable=False, index=True)

    # Template content
    title_template = Column(String(500), nullable=False)
    body_template = Column(Text, nullable=False)

    # Platform-specific content
    ios_payload = Column(JSON, nullable=True)
    android_payload = Column(JSON, nullable=True)
    web_payload = Column(JSON, nullable=True)

    # Template configuration
    variables = Column(JSON, nullable=False, default=dict)
    default_priority = Column(SQLEnum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL)
    version = Column(Integer, nullable=False, default=1)
    is_active = Column(Boolean, nullable=False, default=True, index=True)

    # A/B testing and analytics
    click_action = Column(String(255), nullable=True)
    deep_link = Column(String(500), nullable=True)
    image_url = Column(String(500), nullable=True)

    # Audit fields
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    created_by_user = relationship("User", foreign_keys=[created_by])
    notification_deliveries = relationship(
        "NotificationDelivery",
        back_populates="template",
        cascade="all, delete-orphan"
    )
    notification_queue_items = relationship(
        "NotificationQueue",
        back_populates="template",
        cascade="all, delete-orphan"
    )

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_notification_template_category_active', 'category', 'is_active'),
        Index('idx_notification_template_name_version', 'name', 'version'),
        UniqueConstraint('name', 'version', name='uq_notification_template_name_version'),
    )

    def __repr__(self) -> str:
        return f"<NotificationTemplate(id={self.id}, name='{self.name}', category='{self.category.value}', version={self.version})>"


class NotificationDelivery(Base):
    """
    Notification delivery tracking model for monitoring push notification status and analytics.

    Tracks notification delivery lifecycle from sending to final status with
    comprehensive metadata for performance monitoring and debugging.
    """
    __tablename__ = "notification_deliveries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    device_token_id = Column(UUID(as_uuid=True), ForeignKey("device_tokens.id"), nullable=False, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("notification_templates.id"), nullable=True, index=True)

    # Notification content
    title = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)

    # Delivery tracking
    status = Column(SQLEnum(NotificationStatus), nullable=False, default=NotificationStatus.PENDING, index=True)
    priority = Column(SQLEnum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL)

    # FCM response data
    fcm_message_id = Column(String(255), nullable=True, index=True)
    fcm_response = Column(JSON, nullable=True)
    error_code = Column(String(100), nullable=True, index=True)
    error_message = Column(Text, nullable=True)

    # Timing information
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    clicked_at = Column(DateTime(timezone=True), nullable=True)

    # Retry logic
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    next_retry_at = Column(DateTime(timezone=True), nullable=True)

    # Analytics and metadata
    payload = Column(JSON, nullable=True)
    delivery_metadata = Column(JSON, nullable=False, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="notification_deliveries")
    device_token = relationship("DeviceToken", back_populates="notification_deliveries")
    template = relationship("NotificationTemplate", back_populates="notification_deliveries")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_notification_delivery_user_status', 'user_id', 'status'),
        Index('idx_notification_delivery_status_created', 'status', 'created_at'),
        Index('idx_notification_delivery_fcm_message', 'fcm_message_id'),
        Index('idx_notification_delivery_retry', 'next_retry_at', 'retry_count'),
    )

    def __repr__(self) -> str:
        return f"<NotificationDelivery(id={self.id}, user_id={self.user_id}, status='{self.status.value}', title='{self.title[:50]}...')>"


class NotificationPreference(Base):
    """
    Notification preference model for user-specific push notification settings.

    Manages granular notification preferences including category-specific settings,
    frequency controls, and do-not-disturb configurations.
    """
    __tablename__ = "notification_preferences"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True, index=True)

    # Global notification settings
    push_notifications_enabled = Column(Boolean, nullable=False, default=True)

    # Category-specific preferences
    authentication_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    booking_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    payment_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    promotional_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.DAILY)
    system_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    security_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    social_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)
    reminder_notifications = Column(SQLEnum(NotificationFrequency), nullable=False, default=NotificationFrequency.IMMEDIATE)

    # Do not disturb settings
    dnd_enabled = Column(Boolean, nullable=False, default=False)
    dnd_start_time = Column(String(5), nullable=True)  # Format: "22:00"
    dnd_end_time = Column(String(5), nullable=True)    # Format: "08:00"
    dnd_timezone = Column(String(50), nullable=True, default="UTC")

    # Platform-specific settings
    ios_badge_count = Column(Boolean, nullable=False, default=True)
    android_vibration = Column(Boolean, nullable=False, default=True)
    sound_enabled = Column(Boolean, nullable=False, default=True)

    # Advanced preferences
    language_code = Column(String(10), nullable=False, default="en")
    max_daily_notifications = Column(Integer, nullable=True)

    # Metadata
    preference_metadata = Column(JSON, nullable=False, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="notification_preferences")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_notification_preference_user', 'user_id'),
        Index('idx_notification_preference_enabled', 'push_notifications_enabled'),
    )

    def __repr__(self) -> str:
        return f"<NotificationPreference(id={self.id}, user_id={self.user_id}, enabled={self.push_notifications_enabled})>"


class NotificationQueue(Base):
    """
    Notification queue model for batch processing and scheduled push notification delivery.

    Manages notification queue with priority-based processing, retry logic,
    and integration with Celery for background task processing.
    """
    __tablename__ = "notification_queue"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    device_token_id = Column(UUID(as_uuid=True), ForeignKey("device_tokens.id"), nullable=False, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("notification_templates.id"), nullable=True, index=True)

    # Notification content
    title = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)

    # Queue management
    status = Column(SQLEnum(NotificationStatus), nullable=False, default=NotificationStatus.PENDING, index=True)
    priority = Column(SQLEnum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL, index=True)

    # Scheduling
    scheduled_at = Column(DateTime(timezone=True), nullable=True, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)

    # Processing tracking
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Retry logic
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    next_retry_at = Column(DateTime(timezone=True), nullable=True, index=True)

    # Error tracking
    error_code = Column(String(100), nullable=True)
    error_message = Column(Text, nullable=True)

    # Payload and metadata
    payload = Column(JSON, nullable=True)
    queue_metadata = Column(JSON, nullable=False, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="notification_queue_items")
    device_token = relationship("DeviceToken")
    template = relationship("NotificationTemplate", back_populates="notification_queue_items")

    # Indexes for performance optimization
    __table_args__ = (
        Index('idx_notification_queue_status_priority', 'status', 'priority'),
        Index('idx_notification_queue_scheduled', 'scheduled_at', 'status'),
        Index('idx_notification_queue_retry', 'next_retry_at', 'retry_count'),
        Index('idx_notification_queue_expires', 'expires_at'),
        Index('idx_notification_queue_user_status', 'user_id', 'status'),
    )

    def __repr__(self) -> str:
        return f"<NotificationQueue(id={self.id}, user_id={self.user_id}, status='{self.status.value}', priority='{self.priority.value}')>"
