# Culture Connect Backend - Staging Deployment Workflow
# Automated deployment to staging environment with Docker Swarm

name: Deploy to Staging

on:
  push:
    branches: [ staging ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy'
        required: false
        default: 'latest'
      skip_tests:
        description: 'Skip integration tests'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  ENVIRONMENT: staging

jobs:
  # =============================================================================
  # PRE-DEPLOYMENT VALIDATION
  # =============================================================================
  
  pre-deployment:
    name: Pre-Deployment Validation
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Generate version tag
      id: version
      run: |
        if [[ "${{ github.event.inputs.version }}" != "" ]]; then
          VERSION="${{ github.event.inputs.version }}"
        else
          VERSION="staging-$(date +%Y%m%d)-${GITHUB_SHA::8}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Generated version: $VERSION"

    - name: Validate staging configuration
      run: |
        # Validate staging environment files exist
        if [[ ! -f "environments/staging/.env.staging" ]]; then
          echo "❌ Staging environment file not found"
          exit 1
        fi
        
        if [[ ! -f "docker/staging/docker-compose.swarm.yml" ]]; then
          echo "❌ Staging Docker Swarm configuration not found"
          exit 1
        fi
        
        echo "✅ Staging configuration validation passed"

    - name: Security pre-check
      run: |
        # Basic security validation for staging
        echo "Running staging security pre-check..."
        
        # Check for production secrets in staging config
        if grep -q "live_" environments/staging/.env.staging; then
          echo "❌ Production secrets found in staging configuration"
          exit 1
        fi
        
        echo "✅ Security pre-check passed"

  # =============================================================================
  # STAGING DEPLOYMENT
  # =============================================================================
  
  deploy-staging:
    name: Deploy to Staging Environment
    runs-on: ubuntu-latest
    needs: pre-deployment
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push staging image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-deployment.outputs.version }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging-latest
        build-args: |
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ needs.pre-deployment.outputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Set up staging environment
      run: |
        # Create staging deployment directory
        mkdir -p /tmp/staging-deployment
        cp -r docker/staging/* /tmp/staging-deployment/
        cp environments/staging/.env.staging /tmp/staging-deployment/.env
        
        # Update image version in docker-compose
        sed -i "s|culture-connect-api:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.pre-deployment.outputs.version }}|g" /tmp/staging-deployment/docker-compose.swarm.yml

    - name: Initialize Docker Swarm (if needed)
      run: |
        cd /tmp/staging-deployment
        
        # Check if swarm is already initialized
        if ! docker info | grep -q "Swarm: active"; then
          echo "Initializing Docker Swarm..."
          docker swarm init
        else
          echo "Docker Swarm already active"
        fi

    - name: Create external networks
      run: |
        cd /tmp/staging-deployment
        
        # Create external networks if they don't exist
        docker network create --driver overlay traefik_network || echo "Network traefik_network already exists"

    - name: Deploy to staging with Docker Swarm
      run: |
        cd /tmp/staging-deployment
        
        # Set environment variables
        export CC_VERSION="${{ needs.pre-deployment.outputs.version }}"
        export CC_ENVIRONMENT="staging"
        
        # Deploy stack
        docker stack deploy -c docker-compose.swarm.yml culture-connect-staging
        
        echo "Staging deployment initiated"

    - name: Wait for services to be ready
      run: |
        echo "Waiting for staging services to start..."
        
        # Wait for services to be running
        timeout 600 bash -c '
          while true; do
            running_services=$(docker service ls --filter name=culture-connect-staging --format "{{.Replicas}}" | grep -c "1/1\|2/2\|3/3")
            total_services=$(docker service ls --filter name=culture-connect-staging --quiet | wc -l)
            
            echo "Services ready: $running_services/$total_services"
            
            if [[ $running_services -eq $total_services ]] && [[ $total_services -gt 0 ]]; then
              echo "All services are ready!"
              break
            fi
            
            sleep 10
          done
        '

    - name: Run database migrations
      run: |
        # Get the API service container
        api_container=$(docker ps --filter "name=culture-connect-staging_api" --format "{{.ID}}" | head -1)
        
        if [[ -n "$api_container" ]]; then
          echo "Running migrations in container: $api_container"
          docker exec "$api_container" alembic upgrade head
          docker exec "$api_container" alembic current
        else
          echo "❌ API container not found"
          exit 1
        fi

    - name: Health check
      run: |
        # Wait for application to be ready
        timeout 300 bash -c '
          while true; do
            if curl -f http://localhost:8000/health; then
              echo "✅ Staging health check passed!"
              break
            fi
            echo "Waiting for staging application..."
            sleep 10
          done
        '

    - name: Run smoke tests
      run: |
        echo "Running staging smoke tests..."
        
        # Basic API endpoints test
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost:8000/health/db || exit 1
        curl -f http://localhost:8000/health/redis || exit 1
        curl -f http://localhost:8000/docs || exit 1
        
        echo "✅ Staging smoke tests passed!"

  # =============================================================================
  # INTEGRATION TESTING
  # =============================================================================
  
  integration-tests:
    name: Integration Testing
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: ${{ !github.event.inputs.skip_tests }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install test dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests pytest pytest-asyncio pytest-xdist

    - name: Run integration tests
      run: |
        # Run comprehensive integration tests against staging
        python -m pytest tests/integration/ -v --tb=short --base-url=http://localhost:8000 -n auto
      env:
        TEST_ENVIRONMENT: staging
        API_BASE_URL: http://localhost:8000

    - name: Run API contract tests
      run: |
        # Test API contracts and schemas
        python -m pytest tests/contracts/ -v --tb=short --base-url=http://localhost:8000
      env:
        TEST_ENVIRONMENT: staging
        API_BASE_URL: http://localhost:8000

    - name: Run performance tests
      run: |
        # Basic performance testing
        echo "Running staging performance tests..."
        
        # Test API response times
        for endpoint in "/health" "/health/db" "/health/redis" "/docs"; do
          echo "Testing $endpoint"
          response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:8000$endpoint")
          echo "Response time for $endpoint: ${response_time}s"
          
          # Check if response time is acceptable (< 2 seconds for staging)
          if (( $(echo "$response_time > 2.0" | bc -l) )); then
            echo "❌ Response time too slow for $endpoint: ${response_time}s"
            exit 1
          fi
        done
        
        echo "✅ Performance tests passed!"

    - name: Load testing
      run: |
        # Install and run basic load testing
        pip install locust
        
        # Create basic load test
        cat << 'EOF' > locustfile.py
        from locust import HttpUser, task, between
        
        class StagingUser(HttpUser):
            wait_time = between(1, 3)
            
            @task
            def health_check(self):
                self.client.get("/health")
            
            @task
            def docs(self):
                self.client.get("/docs")
        EOF
        
        # Run load test for 2 minutes with 10 users
        timeout 120 locust -f locustfile.py --headless -u 10 -r 2 -H http://localhost:8000 --run-time 2m || echo "Load test completed"

  # =============================================================================
  # POST-DEPLOYMENT VALIDATION
  # =============================================================================
  
  post-deployment:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: [deploy-staging, integration-tests]
    if: always() && needs.deploy-staging.result == 'success'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Collect deployment information
      run: |
        echo "Collecting staging deployment information..."
        
        # Service status
        docker service ls --filter name=culture-connect-staging
        
        # Container logs (last 100 lines)
        for service in $(docker service ls --filter name=culture-connect-staging --format "{{.Name}}"); do
          echo "=== Logs for $service ==="
          docker service logs --tail 100 "$service" || echo "No logs available for $service"
        done

    - name: Security validation
      run: |
        echo "Running staging security validation..."
        
        # Check for secure headers
        curl -I http://localhost:8000/health | grep -i "x-frame-options\|x-content-type-options\|x-xss-protection" || echo "Security headers missing"
        
        # Check for exposed sensitive information
        curl -s http://localhost:8000/docs | grep -i "password\|secret\|key" && echo "Potential sensitive information exposed" || echo "No sensitive information found"

    - name: Generate staging deployment report
      run: |
        cat << EOF > staging-deployment-report.md
        # Staging Deployment Report
        
        **Deployment Date**: $(date -u)
        **Version**: ${{ needs.pre-deployment.outputs.version }}
        **Commit**: ${{ github.sha }}
        **Branch**: ${{ github.ref }}
        
        ## Deployment Status
        - ✅ Pre-deployment validation: Success
        - ✅ Container build and push: Success
        - ✅ Docker Swarm deployment: Success
        - ✅ Database migrations: Success
        - ✅ Health checks: Success
        - ✅ Smoke tests: Success
        - ${{ needs.integration-tests.result == 'success' && '✅' || '❌' }} Integration tests: ${{ needs.integration-tests.result || 'Skipped' }}
        - ✅ Security validation: Success
        
        ## Service Information
        - **API Endpoint**: http://localhost:8000
        - **Documentation**: http://localhost:8000/docs
        - **Health Check**: http://localhost:8000/health
        - **Environment**: Staging
        - **Deployment Strategy**: Docker Swarm
        
        ## Next Steps
        1. Monitor staging environment performance
        2. Run additional manual testing
        3. Prepare for production deployment when ready
        
        EOF

    - name: Upload deployment report
      uses: actions/upload-artifact@v3
      with:
        name: staging-deployment-report
        path: staging-deployment-report.md

    - name: Notify deployment status
      run: |
        if [[ "${{ needs.integration-tests.result }}" == "success" || "${{ github.event.inputs.skip_tests }}" == "true" ]]; then
          echo "✅ Staging deployment completed successfully!"
          echo "🚀 Application available at: http://localhost:8000"
          echo "📚 API Documentation: http://localhost:8000/docs"
          echo "🔍 Version deployed: ${{ needs.pre-deployment.outputs.version }}"
        else
          echo "⚠️ Staging deployment completed with test failures"
          echo "📋 Check the integration test results for details"
        fi
