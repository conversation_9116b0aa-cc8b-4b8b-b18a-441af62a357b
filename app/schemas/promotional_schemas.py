"""
Promotional schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic V2 schemas for promotional and advertising
management including campaign creation, advertisement management, performance analytics,
and billing integration following Phase 5 implementation requirements.

Implements Task 5.1.1 Phase 2 requirements for promotional schemas with:
- Campaign management schemas with budget tracking and multi-provider payment integration
- Advertisement management schemas with creative assets and placement optimization
- Performance analytics schemas with <200ms query targets
- Promotional billing schemas with payment provider integration
- Targeting schemas for geographic and demographic targeting
- Advanced validation rules with business logic enforcement

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime
from datetime import date as DateType
from decimal import Decimal
from typing import Optional, List, Dict, Any

from pydantic import Field, ConfigDict, field_validator, AnyHttpUrl

from app.schemas.base import BaseSchema, TimestampMixin
from app.models.promotional import (
    CampaignStatus, CampaignType, CampaignObjective, BidStrategy,
    AdFormat, AdStatus, PlacementType, PlacementStatus, SpendCategory
)
from app.core.payment.config import PaymentProviderType


# Base schemas with common configuration
class PromotionalBaseSchema(BaseSchema):
    """Base schema for promotional models with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-27T10:00:00Z",
                "updated_at": "2025-01-27T10:00:00Z"
            }
        }
    )


# Campaign Schemas
class CampaignCreate(PromotionalBaseSchema):
    """Schema for creating a new promotional campaign."""

    name: str = Field(
        ...,
        min_length=3,
        max_length=255,
        description="Campaign name",
        example="Summer Tourism Promotion 2025"
    )

    description: Optional[str] = Field(
        None,
        max_length=2000,
        description="Campaign description and objectives",
        example="Promote summer tourism packages to international visitors"
    )

    campaign_type: CampaignType = Field(
        ...,
        description="Type of promotional campaign",
        example=CampaignType.FEATURED_LISTING
    )

    campaign_objective: CampaignObjective = Field(
        ...,
        description="Primary campaign objective",
        example=CampaignObjective.BOOKING_CONVERSION
    )

    # Budget and financial configuration
    total_budget: Decimal = Field(
        ...,
        ge=Decimal("1000.00"),
        le=Decimal("10000000.00"),
        description="Total campaign budget in NGN (₦1,000 - ₦10,000,000)",
        example=Decimal("50000.00")
    )

    daily_budget: Optional[Decimal] = Field(
        None,
        ge=Decimal("100.00"),
        description="Daily spending limit in NGN (minimum ₦100)",
        example=Decimal("2000.00")
    )

    # Payment integration
    payment_provider: Optional[PaymentProviderType] = Field(
        None,
        description="Preferred payment provider for campaign billing",
        example=PaymentProviderType.PAYSTACK
    )

    payment_method_id: Optional[str] = Field(
        None,
        max_length=255,
        description="Payment method identifier",
        example="pm_1234567890"
    )

    # TODO-PAYSTACK-API-URL: Paystack payment configuration for campaign billing
    # TODO-STRIPE-API-URL: Stripe payment configuration for international campaigns
    # TODO-BUSHA-API-URL: Busha cryptocurrency payment configuration

    # Bidding and optimization
    bid_strategy: BidStrategy = Field(
        default=BidStrategy.MANUAL_CPC,
        description="Bidding strategy for campaign",
        example=BidStrategy.MANUAL_CPC
    )

    target_cpc: Optional[Decimal] = Field(
        None,
        ge=Decimal("1.00"),
        description="Target cost per click in NGN",
        example=Decimal("25.00")
    )

    target_cpa: Optional[Decimal] = Field(
        None,
        ge=Decimal("10.00"),
        description="Target cost per acquisition in NGN",
        example=Decimal("500.00")
    )

    target_roas: Optional[Decimal] = Field(
        None,
        ge=Decimal("1.00"),
        description="Target return on ad spend ratio",
        example=Decimal("3.50")
    )

    # Scheduling
    start_date: Optional[DateType] = Field(
        None,
        description="Campaign start date",
        example="2025-02-01"
    )

    end_date: Optional[DateType] = Field(
        None,
        description="Campaign end date",
        example="2025-02-28"
    )

    timezone: str = Field(
        default="Africa/Lagos",
        max_length=50,
        description="Campaign timezone",
        example="Africa/Lagos"
    )

    # Targeting configuration
    targeting_config: Optional[Dict[str, Any]] = Field(
        None,
        description="JSON configuration for campaign targeting",
        example={
            "age_range": {"min": 25, "max": 55},
            "interests": ["travel", "tourism", "culture"],
            "device_types": ["mobile", "desktop"]
        }
    )

    geographic_targeting: Optional[Dict[str, Any]] = Field(
        None,
        description="Geographic targeting configuration",
        example={
            "countries": ["NG", "US", "GB", "CA"],
            "states": ["Lagos", "Abuja", "Rivers"],
            "radius_km": 50
        }
    )

    demographic_targeting: Optional[Dict[str, Any]] = Field(
        None,
        description="Demographic targeting configuration",
        example={
            "gender": ["male", "female"],
            "income_level": ["middle", "high"],
            "education": ["university", "postgraduate"]
        }
    )

    @field_validator('name')
    @classmethod
    def validate_campaign_name(cls, v: str) -> str:
        """Validate campaign name."""
        # Remove excessive whitespace
        v = ' '.join(v.split())

        # Check for invalid characters
        if any(char in v for char in ['<', '>', '"', "'", '&']):
            raise ValueError('Campaign name contains invalid characters')

        return v

    @field_validator('total_budget')
    @classmethod
    def validate_total_budget(cls, v: Decimal) -> Decimal:
        """Validate total budget constraints."""
        if v < Decimal("1000.00"):
            raise ValueError("Campaign budget must be at least ₦1,000")
        if v > Decimal("10000000.00"):
            raise ValueError("Campaign budget cannot exceed ₦10,000,000")
        return v

    @field_validator('daily_budget')
    @classmethod
    def validate_daily_budget(cls, v: Optional[Decimal]) -> Optional[Decimal]:
        """Validate daily budget constraints."""
        if v is not None:
            if v < Decimal("100.00"):
                raise ValueError("Daily budget must be at least ₦100")
        return v

    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v: Optional[DateType], info) -> Optional[DateType]:
        """Validate end date is after start date."""
        if v is not None and hasattr(info, 'data') and 'start_date' in info.data:
            start_date = info.data.get('start_date')
            if start_date is not None and v <= start_date:
                raise ValueError("End date must be after start date")
        return v

    @field_validator('start_date')
    @classmethod
    def validate_start_date(cls, v: Optional[DateType]) -> Optional[DateType]:
        """Validate start date is not in the past."""
        if v is not None:
            from datetime import date as date_class
            if v < date_class.today():
                raise ValueError("Start date cannot be in the past")
        return v


class CampaignUpdate(PromotionalBaseSchema):
    """Schema for updating an existing campaign."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=255,
        description="Updated campaign name"
    )

    description: Optional[str] = Field(
        None,
        max_length=2000,
        description="Updated campaign description"
    )

    status: Optional[CampaignStatus] = Field(
        None,
        description="Updated campaign status"
    )

    daily_budget: Optional[Decimal] = Field(
        None,
        ge=Decimal("100.00"),
        description="Updated daily spending limit in NGN"
    )

    bid_strategy: Optional[BidStrategy] = Field(
        None,
        description="Updated bidding strategy"
    )

    target_cpc: Optional[Decimal] = Field(
        None,
        ge=Decimal("1.00"),
        description="Updated target cost per click"
    )

    target_cpa: Optional[Decimal] = Field(
        None,
        ge=Decimal("10.00"),
        description="Updated target cost per acquisition"
    )

    target_roas: Optional[Decimal] = Field(
        None,
        ge=Decimal("1.00"),
        description="Updated target return on ad spend"
    )

    end_date: Optional[DateType] = Field(
        None,
        description="Updated campaign end date"
    )

    targeting_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated targeting configuration"
    )

    geographic_targeting: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated geographic targeting"
    )

    demographic_targeting: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated demographic targeting"
    )

    @field_validator('name')
    @classmethod
    def validate_campaign_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate campaign name."""
        if v is not None:
            # Remove excessive whitespace
            v = ' '.join(v.split())

            # Check for invalid characters
            if any(char in v for char in ['<', '>', '"', "'", '&']):
                raise ValueError('Campaign name contains invalid characters')

        return v


class CampaignResponse(PromotionalBaseSchema, TimestampMixin):
    """Schema for campaign response data."""

    id: int = Field(..., description="Campaign ID")
    vendor_id: int = Field(..., description="Associated vendor ID")
    name: str = Field(..., description="Campaign name")
    description: Optional[str] = Field(None, description="Campaign description")
    campaign_type: CampaignType = Field(..., description="Campaign type")
    campaign_objective: CampaignObjective = Field(..., description="Campaign objective")
    status: CampaignStatus = Field(..., description="Campaign status")

    # Budget information
    total_budget: Decimal = Field(..., description="Total campaign budget")
    daily_budget: Optional[Decimal] = Field(None, description="Daily budget limit")
    spent_amount: Decimal = Field(..., description="Amount spent")
    remaining_budget: Decimal = Field(..., description="Remaining budget")

    # Performance metrics
    impressions: int = Field(..., description="Total impressions")
    clicks: int = Field(..., description="Total clicks")
    conversions: int = Field(..., description="Total conversions")
    revenue_generated: Decimal = Field(..., description="Revenue generated")

    # Calculated metrics
    budget_utilization_percentage: float = Field(..., description="Budget utilization %")
    click_through_rate: float = Field(..., description="Click-through rate %")
    conversion_rate: float = Field(..., description="Conversion rate %")
    cost_per_click: float = Field(..., description="Cost per click")
    cost_per_acquisition: float = Field(..., description="Cost per acquisition")
    return_on_ad_spend: float = Field(..., description="Return on ad spend")

    # Scheduling
    start_date: Optional[DateType] = Field(None, description="Campaign start date")
    end_date: Optional[DateType] = Field(None, description="Campaign end date")
    timezone: str = Field(..., description="Campaign timezone")

    # Approval information
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    approved_by: Optional[int] = Field(None, description="Approved by user ID")
    rejection_reason: Optional[str] = Field(None, description="Rejection reason")


class CampaignListResponse(PromotionalBaseSchema):
    """Schema for paginated campaign list response."""

    campaigns: List[CampaignResponse] = Field(..., description="List of campaigns")
    total: int = Field(..., description="Total number of campaigns")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


# Advertisement Schemas
class AdvertisementCreate(PromotionalBaseSchema):
    """Schema for creating a new advertisement."""

    campaign_id: int = Field(
        ...,
        gt=0,
        description="Associated campaign ID",
        example=123
    )

    name: str = Field(
        ...,
        min_length=3,
        max_length=255,
        description="Advertisement name",
        example="Summer Tourism Hero Ad"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Advertisement description",
        example="Hero advertisement for summer tourism promotion"
    )

    ad_format: AdFormat = Field(
        ...,
        description="Advertisement format type",
        example=AdFormat.IMAGE
    )

    # Creative content
    headline: str = Field(
        ...,
        min_length=10,
        max_length=100,
        description="Advertisement headline",
        example="Discover Nigeria's Hidden Cultural Gems This Summer"
    )

    description_text: Optional[str] = Field(
        None,
        max_length=500,
        description="Advertisement description text",
        example="Experience authentic Nigerian culture with our curated tourism packages"
    )

    call_to_action: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="Call to action text",
        example="Book Now"
    )

    # Media assets
    image_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Primary image URL",
        example="https://cdn.cultureconnect.ng/ads/summer-tourism-hero.jpg"
    )

    video_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Video content URL",
        example="https://cdn.cultureconnect.ng/videos/summer-tourism.mp4"
    )

    thumbnail_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Video thumbnail URL",
        example="https://cdn.cultureconnect.ng/thumbnails/summer-tourism.jpg"
    )

    media_assets: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional media assets configuration",
        example={
            "carousel_images": [
                "https://cdn.cultureconnect.ng/carousel/img1.jpg",
                "https://cdn.cultureconnect.ng/carousel/img2.jpg"
            ],
            "alt_text": "Nigerian cultural tourism experience"
        }
    )

    # Targeting and placement
    target_url: AnyHttpUrl = Field(
        ...,
        description="Landing page URL",
        example="https://cultureconnect.ng/services/summer-tourism"
    )

    placement_preferences: Optional[Dict[str, Any]] = Field(
        None,
        description="Preferred placement configuration",
        example={
            "preferred_placements": ["homepage_hero", "category_top"],
            "exclude_placements": ["mobile_banner"],
            "priority_level": 8
        }
    )

    targeting_override: Optional[Dict[str, Any]] = Field(
        None,
        description="Ad-specific targeting overrides",
        example={
            "age_range": {"min": 30, "max": 60},
            "interests": ["luxury_travel", "cultural_experiences"]
        }
    )

    # A/B testing
    variant_group: Optional[str] = Field(
        None,
        max_length=100,
        description="A/B test variant group identifier",
        example="summer_tourism_hero_test"
    )

    variant_name: Optional[str] = Field(
        None,
        max_length=100,
        description="A/B test variant name",
        example="variant_a_cultural_focus"
    )

    test_percentage: Optional[Decimal] = Field(
        None,
        ge=Decimal("0.00"),
        le=Decimal("100.00"),
        description="Percentage of traffic for this variant",
        example=Decimal("50.00")
    )

    # Scheduling
    start_date: Optional[datetime] = Field(
        None,
        description="Advertisement start date",
        example="2025-02-01T00:00:00Z"
    )

    end_date: Optional[datetime] = Field(
        None,
        description="Advertisement end date",
        example="2025-02-28T23:59:59Z"
    )

    @field_validator('headline')
    @classmethod
    def validate_headline(cls, v: str) -> str:
        """Validate advertisement headline."""
        # Remove excessive whitespace
        v = ' '.join(v.split())

        # Check for invalid characters (XSS prevention)
        if any(char in v for char in ['<', '>', '"', "'", '&']):
            raise ValueError('Headline contains invalid characters')

        # Check minimum length
        if len(v) < 10:
            raise ValueError('Headline must be at least 10 characters long')

        return v

    @field_validator('call_to_action')
    @classmethod
    def validate_call_to_action(cls, v: str) -> str:
        """Validate call to action text."""
        # Remove excessive whitespace
        v = ' '.join(v.split())

        # Check for invalid characters
        if any(char in v for char in ['<', '>', '"', "'", '&']):
            raise ValueError('Call to action contains invalid characters')

        return v

    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate end date is after start date."""
        if v is not None and hasattr(info, 'data') and 'start_date' in info.data:
            start_date = info.data.get('start_date')
            if start_date is not None and v <= start_date:
                raise ValueError("End date must be after start date")
        return v


class AdvertisementUpdate(PromotionalBaseSchema):
    """Schema for updating an existing advertisement."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=255,
        description="Updated advertisement name"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Updated advertisement description"
    )

    status: Optional[AdStatus] = Field(
        None,
        description="Updated advertisement status"
    )

    headline: Optional[str] = Field(
        None,
        min_length=10,
        max_length=100,
        description="Updated advertisement headline"
    )

    description_text: Optional[str] = Field(
        None,
        max_length=500,
        description="Updated advertisement description text"
    )

    call_to_action: Optional[str] = Field(
        None,
        min_length=3,
        max_length=50,
        description="Updated call to action text"
    )

    image_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Updated primary image URL"
    )

    video_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Updated video content URL"
    )

    thumbnail_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Updated video thumbnail URL"
    )

    media_assets: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated media assets configuration"
    )

    target_url: Optional[AnyHttpUrl] = Field(
        None,
        description="Updated landing page URL"
    )

    placement_preferences: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated placement preferences"
    )

    targeting_override: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated targeting overrides"
    )

    end_date: Optional[datetime] = Field(
        None,
        description="Updated advertisement end date"
    )

    @field_validator('headline')
    @classmethod
    def validate_headline(cls, v: Optional[str]) -> Optional[str]:
        """Validate advertisement headline."""
        if v is not None:
            # Remove excessive whitespace
            v = ' '.join(v.split())

            # Check for invalid characters (XSS prevention)
            if any(char in v for char in ['<', '>', '"', "'", '&']):
                raise ValueError('Headline contains invalid characters')

            # Check minimum length
            if len(v) < 10:
                raise ValueError('Headline must be at least 10 characters long')

        return v


class AdvertisementResponse(PromotionalBaseSchema, TimestampMixin):
    """Schema for advertisement response data."""

    id: int = Field(..., description="Advertisement ID")
    campaign_id: int = Field(..., description="Associated campaign ID")
    name: str = Field(..., description="Advertisement name")
    description: Optional[str] = Field(None, description="Advertisement description")
    ad_format: AdFormat = Field(..., description="Advertisement format")
    status: AdStatus = Field(..., description="Advertisement status")

    # Creative content
    headline: str = Field(..., description="Advertisement headline")
    description_text: Optional[str] = Field(None, description="Description text")
    call_to_action: str = Field(..., description="Call to action")

    # Media assets
    image_url: Optional[str] = Field(None, description="Primary image URL")
    video_url: Optional[str] = Field(None, description="Video content URL")
    thumbnail_url: Optional[str] = Field(None, description="Video thumbnail URL")
    media_assets: Optional[Dict[str, Any]] = Field(None, description="Media assets")

    # Targeting and placement
    target_url: str = Field(..., description="Landing page URL")
    placement_preferences: Optional[Dict[str, Any]] = Field(None, description="Placement preferences")
    targeting_override: Optional[Dict[str, Any]] = Field(None, description="Targeting overrides")

    # Performance metrics
    impressions: int = Field(..., description="Total impressions")
    clicks: int = Field(..., description="Total clicks")
    conversions: int = Field(..., description="Total conversions")
    spend_amount: Decimal = Field(..., description="Amount spent")

    # Calculated metrics
    click_through_rate: float = Field(..., description="Click-through rate %")
    conversion_rate: float = Field(..., description="Conversion rate %")
    cost_per_click: float = Field(..., description="Cost per click")
    cost_per_conversion: float = Field(..., description="Cost per conversion")

    # A/B testing
    variant_group: Optional[str] = Field(None, description="A/B test variant group")
    variant_name: Optional[str] = Field(None, description="A/B test variant name")
    test_percentage: Optional[Decimal] = Field(None, description="Test traffic percentage")

    # Approval information
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    approved_by: Optional[int] = Field(None, description="Approved by user ID")
    rejection_reason: Optional[str] = Field(None, description="Rejection reason")

    # Scheduling
    start_date: Optional[datetime] = Field(None, description="Start date")
    end_date: Optional[datetime] = Field(None, description="End date")


class AdVariantSchema(PromotionalBaseSchema):
    """Schema for A/B test advertisement variants."""

    variant_group: str = Field(..., description="A/B test variant group identifier")
    variant_name: str = Field(..., description="A/B test variant name")
    test_percentage: Decimal = Field(
        ...,
        ge=Decimal("0.00"),
        le=Decimal("100.00"),
        description="Percentage of traffic for this variant"
    )
    is_control: bool = Field(default=False, description="Whether this is the control variant")
    performance_metrics: Optional[Dict[str, Any]] = Field(
        None,
        description="Performance metrics for this variant"
    )


# Campaign Metrics Schemas
class CampaignMetricsResponse(PromotionalBaseSchema, TimestampMixin):
    """Schema for campaign metrics response data."""

    id: int = Field(..., description="Metrics ID")
    campaign_id: int = Field(..., description="Associated campaign ID")
    metrics_date: DateType = Field(..., description="Metrics date")
    hour: Optional[int] = Field(None, description="Hour of day (0-23) for hourly metrics")

    # Performance metrics
    impressions: int = Field(..., description="Impressions for the period")
    clicks: int = Field(..., description="Clicks for the period")
    conversions: int = Field(..., description="Conversions for the period")
    spend_amount: Decimal = Field(..., description="Spend amount for the period")
    revenue_generated: Decimal = Field(..., description="Revenue generated for the period")

    # Calculated metrics
    click_through_rate: Decimal = Field(..., description="Click-through rate percentage")
    conversion_rate: Decimal = Field(..., description="Conversion rate percentage")
    cost_per_click: Decimal = Field(..., description="Cost per click")
    cost_per_acquisition: Decimal = Field(..., description="Cost per acquisition")
    return_on_ad_spend: Decimal = Field(..., description="Return on ad spend ratio")

    # Quality metrics
    quality_score: Decimal = Field(..., description="Campaign quality score (0-100)")
    relevance_score: Decimal = Field(..., description="Ad relevance score (0-100)")
    landing_page_score: Decimal = Field(..., description="Landing page experience score (0-100)")

    # Engagement metrics
    unique_clicks: int = Field(..., description="Unique clicks for the period")
    bounce_rate: Decimal = Field(..., description="Bounce rate percentage")
    average_session_duration: Decimal = Field(..., description="Average session duration in seconds")
    pages_per_session: Decimal = Field(..., description="Average pages per session")

    # Device breakdown
    desktop_impressions: int = Field(..., description="Desktop impressions")
    mobile_impressions: int = Field(..., description="Mobile impressions")
    tablet_impressions: int = Field(..., description="Tablet impressions")

    # Geographic performance
    top_performing_locations: Optional[Dict[str, Any]] = Field(
        None,
        description="Top performing geographic locations"
    )

    # Optimization insights
    optimization_score: Decimal = Field(..., description="Campaign optimization score (0-100)")
    recommendations: Optional[Dict[str, Any]] = Field(
        None,
        description="Optimization recommendations"
    )


class PerformanceAnalyticsSchema(PromotionalBaseSchema):
    """Schema for comprehensive performance analytics."""

    campaign_id: int = Field(..., description="Campaign ID")
    date_range: Dict[str, DateType] = Field(
        ...,
        description="Date range for analytics",
        example={"start_date": "2025-02-01", "end_date": "2025-02-28"}
    )

    # Aggregate metrics
    total_impressions: int = Field(..., description="Total impressions")
    total_clicks: int = Field(..., description="Total clicks")
    total_conversions: int = Field(..., description="Total conversions")
    total_spend: Decimal = Field(..., description="Total spend amount")
    total_revenue: Decimal = Field(..., description="Total revenue generated")

    # Performance trends
    daily_metrics: List[CampaignMetricsResponse] = Field(
        ...,
        description="Daily performance metrics"
    )

    # Comparative analysis
    period_over_period_change: Dict[str, float] = Field(
        ...,
        description="Period-over-period performance changes",
        example={
            "impressions_change": 15.5,
            "clicks_change": 12.3,
            "conversions_change": 8.7,
            "spend_change": 10.2
        }
    )

    # Top performers
    top_performing_ads: List[AdvertisementResponse] = Field(
        ...,
        description="Top performing advertisements"
    )

    # Insights and recommendations
    performance_insights: List[str] = Field(
        ...,
        description="Performance insights and observations"
    )

    optimization_recommendations: List[Dict[str, Any]] = Field(
        ...,
        description="Optimization recommendations with priority levels"
    )


class OptimizationInsightsSchema(PromotionalBaseSchema):
    """Schema for campaign optimization insights."""

    campaign_id: int = Field(..., description="Campaign ID")
    analysis_date: datetime = Field(..., description="Analysis timestamp")

    # Performance assessment
    overall_performance_score: Decimal = Field(
        ...,
        ge=Decimal("0.00"),
        le=Decimal("100.00"),
        description="Overall performance score (0-100)"
    )

    performance_category: str = Field(
        ...,
        description="Performance category",
        example="excellent"  # excellent, good, average, poor
    )

    # Key insights
    strengths: List[str] = Field(..., description="Campaign strengths")
    weaknesses: List[str] = Field(..., description="Areas for improvement")
    opportunities: List[str] = Field(..., description="Growth opportunities")
    threats: List[str] = Field(..., description="Potential risks")

    # Specific recommendations
    budget_recommendations: List[Dict[str, Any]] = Field(
        ...,
        description="Budget optimization recommendations"
    )

    targeting_recommendations: List[Dict[str, Any]] = Field(
        ...,
        description="Targeting optimization recommendations"
    )

    creative_recommendations: List[Dict[str, Any]] = Field(
        ...,
        description="Creative optimization recommendations"
    )

    # Predicted outcomes
    projected_improvements: Dict[str, float] = Field(
        ...,
        description="Projected performance improvements if recommendations are implemented"
    )

    confidence_level: Decimal = Field(
        ...,
        ge=Decimal("0.00"),
        le=Decimal("100.00"),
        description="Confidence level in recommendations (0-100)"
    )


# Placement Schemas
class PromotionalListingCreate(PromotionalBaseSchema):
    """Schema for creating a new promotional listing."""

    advertisement_id: Optional[int] = Field(
        None,
        gt=0,
        description="Associated advertisement ID (optional)"
    )

    service_id: Optional[int] = Field(
        None,
        gt=0,
        description="Associated service ID (optional)"
    )

    title: str = Field(
        ...,
        min_length=3,
        max_length=255,
        description="Promotional listing title",
        example="Featured Cultural Tourism Experience"
    )

    description: Optional[str] = Field(
        None,
        max_length=2000,
        description="Promotional listing description",
        example="Premium placement for authentic Nigerian cultural experiences"
    )

    placement_type: PlacementType = Field(
        ...,
        description="Type of placement",
        example=PlacementType.HOMEPAGE_HERO
    )

    priority_level: int = Field(
        default=1,
        ge=1,
        le=10,
        description="Placement priority level (1-10, higher = more prominent)",
        example=8
    )

    position_index: Optional[int] = Field(
        None,
        ge=0,
        description="Specific position index within placement type",
        example=1
    )

    # Pricing configuration
    base_price: Decimal = Field(
        ...,
        ge=Decimal("100.00"),
        description="Base price for placement in NGN",
        example=Decimal("5000.00")
    )

    premium_multiplier: Decimal = Field(
        default=Decimal("1.00"),
        ge=Decimal("0.50"),
        le=Decimal("10.00"),
        description="Premium pricing multiplier",
        example=Decimal("1.50")
    )

    billing_cycle: str = Field(
        default="daily",
        description="Billing cycle (daily, weekly, monthly)",
        example="daily"
    )

    # Scheduling
    start_date: datetime = Field(
        ...,
        description="Placement start date",
        example="2025-02-01T00:00:00Z"
    )

    end_date: datetime = Field(
        ...,
        description="Placement end date",
        example="2025-02-28T23:59:59Z"
    )

    timezone: str = Field(
        default="Africa/Lagos",
        max_length=50,
        description="Placement timezone",
        example="Africa/Lagos"
    )

    # Targeting and optimization
    targeting_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Placement-specific targeting configuration",
        example={
            "target_audience": ["tourists", "culture_enthusiasts"],
            "device_preferences": ["desktop", "mobile"]
        }
    )

    optimization_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Placement optimization settings",
        example={
            "auto_optimize": True,
            "performance_threshold": 0.05
        }
    )

    @field_validator('billing_cycle')
    @classmethod
    def validate_billing_cycle(cls, v: str) -> str:
        """Validate billing cycle."""
        allowed_cycles = ['daily', 'weekly', 'monthly']
        if v not in allowed_cycles:
            raise ValueError(f"Billing cycle must be one of: {', '.join(allowed_cycles)}")
        return v

    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v: datetime, info) -> datetime:
        """Validate end date is after start date."""
        if hasattr(info, 'data') and 'start_date' in info.data:
            start_date = info.data.get('start_date')
            if start_date is not None and v <= start_date:
                raise ValueError("End date must be after start date")
        return v


class PromotionalListingUpdate(PromotionalBaseSchema):
    """Schema for updating an existing promotional listing."""

    title: Optional[str] = Field(
        None,
        min_length=3,
        max_length=255,
        description="Updated promotional listing title"
    )

    description: Optional[str] = Field(
        None,
        max_length=2000,
        description="Updated promotional listing description"
    )

    placement_status: Optional[PlacementStatus] = Field(
        None,
        description="Updated placement status"
    )

    priority_level: Optional[int] = Field(
        None,
        ge=1,
        le=10,
        description="Updated placement priority level"
    )

    position_index: Optional[int] = Field(
        None,
        ge=0,
        description="Updated position index"
    )

    premium_multiplier: Optional[Decimal] = Field(
        None,
        ge=Decimal("0.50"),
        le=Decimal("10.00"),
        description="Updated premium pricing multiplier"
    )

    end_date: Optional[datetime] = Field(
        None,
        description="Updated placement end date"
    )

    targeting_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated targeting configuration"
    )

    optimization_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated optimization settings"
    )


class PlacementAvailabilitySchema(PromotionalBaseSchema):
    """Schema for placement availability information."""

    placement_type: PlacementType = Field(..., description="Placement type")
    available_slots: int = Field(..., description="Number of available slots")
    total_slots: int = Field(..., description="Total number of slots")
    next_available_date: Optional[datetime] = Field(
        None,
        description="Next available date for booking"
    )

    pricing_info: Dict[str, Any] = Field(
        ...,
        description="Pricing information for this placement",
        example={
            "base_price": 5000.00,
            "peak_multiplier": 1.5,
            "off_peak_multiplier": 0.8,
            "currency": "NGN"
        }
    )

    performance_metrics: Optional[Dict[str, Any]] = Field(
        None,
        description="Historical performance metrics for this placement"
    )

    recommendations: List[str] = Field(
        default=[],
        description="Recommendations for optimizing this placement"
    )


# Financial Schemas
class AdSpendCreate(PromotionalBaseSchema):
    """Schema for creating a new ad spend record."""

    campaign_id: int = Field(
        ...,
        gt=0,
        description="Associated campaign ID",
        example=123
    )

    advertisement_id: Optional[int] = Field(
        None,
        gt=0,
        description="Associated advertisement ID (optional)",
        example=456
    )

    spend_category: SpendCategory = Field(
        ...,
        description="Category of spend",
        example=SpendCategory.CAMPAIGN_BUDGET
    )

    amount: Decimal = Field(
        ...,
        gt=Decimal("0.00"),
        description="Spend amount in NGN",
        example=Decimal("1500.00")
    )

    currency: str = Field(
        default="NGN",
        max_length=3,
        description="Currency code",
        example="NGN"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Spend description",
        example="Daily campaign budget allocation for homepage hero placement"
    )

    # Payment integration
    payment_provider: PaymentProviderType = Field(
        ...,
        description="Payment provider used",
        example=PaymentProviderType.PAYSTACK
    )

    payment_reference: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Payment reference from provider",
        example="pay_1234567890abcdef"
    )

    # TODO-PAYSTACK-API-URL: Paystack payment tracking for ad spend
    # TODO-STRIPE-API-URL: Stripe payment tracking for international ad spend
    # TODO-BUSHA-API-URL: Busha cryptocurrency payment tracking for ad spend

    # Billing period
    billing_period_start: datetime = Field(
        ...,
        description="Billing period start",
        example="2025-02-01T00:00:00Z"
    )

    billing_period_end: datetime = Field(
        ...,
        description="Billing period end",
        example="2025-02-01T23:59:59Z"
    )

    # Performance attribution
    impressions_attributed: int = Field(
        default=0,
        ge=0,
        description="Impressions attributed to this spend",
        example=1500
    )

    clicks_attributed: int = Field(
        default=0,
        ge=0,
        description="Clicks attributed to this spend",
        example=75
    )

    conversions_attributed: int = Field(
        default=0,
        ge=0,
        description="Conversions attributed to this spend",
        example=5
    )

    revenue_attributed: Decimal = Field(
        default=Decimal("0.00"),
        ge=Decimal("0.00"),
        description="Revenue attributed to this spend",
        example=Decimal("7500.00")
    )

    # Tax and fees
    platform_fee: Decimal = Field(
        default=Decimal("0.00"),
        ge=Decimal("0.00"),
        description="Platform fee amount",
        example=Decimal("150.00")
    )

    tax_amount: Decimal = Field(
        default=Decimal("0.00"),
        ge=Decimal("0.00"),
        description="Tax amount",
        example=Decimal("112.50")
    )

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v: str) -> str:
        """Validate currency code."""
        allowed_currencies = ['NGN', 'USD', 'EUR', 'GBP', 'BTC', 'ETH', 'USDT', 'USDC']
        if v.upper() not in allowed_currencies:
            raise ValueError(f"Currency must be one of: {', '.join(allowed_currencies)}")
        return v.upper()

    @field_validator('billing_period_end')
    @classmethod
    def validate_billing_period_end(cls, v: datetime, info) -> datetime:
        """Validate billing period end is after start."""
        if hasattr(info, 'data') and 'billing_period_start' in info.data:
            start = info.data.get('billing_period_start')
            if start is not None and v <= start:
                raise ValueError("Billing period end must be after start")
        return v


class AdSpendResponse(PromotionalBaseSchema, TimestampMixin):
    """Schema for ad spend response data."""

    id: int = Field(..., description="Ad spend ID")
    campaign_id: int = Field(..., description="Associated campaign ID")
    advertisement_id: Optional[int] = Field(None, description="Associated advertisement ID")
    vendor_id: int = Field(..., description="Associated vendor ID")

    spend_category: SpendCategory = Field(..., description="Category of spend")
    amount: Decimal = Field(..., description="Spend amount")
    currency: str = Field(..., description="Currency code")
    description: Optional[str] = Field(None, description="Spend description")

    # Payment information
    payment_provider: PaymentProviderType = Field(..., description="Payment provider")
    payment_reference: str = Field(..., description="Payment reference")
    payment_status: str = Field(..., description="Payment status")

    # Billing period
    billing_period_start: datetime = Field(..., description="Billing period start")
    billing_period_end: datetime = Field(..., description="Billing period end")

    # Performance attribution
    impressions_attributed: int = Field(..., description="Impressions attributed")
    clicks_attributed: int = Field(..., description="Clicks attributed")
    conversions_attributed: int = Field(..., description="Conversions attributed")
    revenue_attributed: Decimal = Field(..., description="Revenue attributed")

    # Calculated metrics
    cost_per_impression: float = Field(..., description="Cost per impression")
    cost_per_click: float = Field(..., description="Cost per click")
    cost_per_conversion: float = Field(..., description="Cost per conversion")
    return_on_ad_spend: float = Field(..., description="Return on ad spend")

    # Tax and fees
    platform_fee: Decimal = Field(..., description="Platform fee amount")
    tax_amount: Decimal = Field(..., description="Tax amount")
    total_amount: Decimal = Field(..., description="Total amount including fees")

    # Processing information
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")
    processed_by: Optional[int] = Field(None, description="Processed by user ID")

    # Reconciliation
    reconciled: bool = Field(..., description="Whether spend has been reconciled")
    reconciled_at: Optional[datetime] = Field(None, description="Reconciliation timestamp")
    reconciliation_reference: Optional[str] = Field(None, description="Reconciliation reference")


class PromotionalBillingSchema(PromotionalBaseSchema):
    """Schema for promotional billing information."""

    vendor_id: int = Field(..., description="Vendor ID")
    billing_period: Dict[str, datetime] = Field(
        ...,
        description="Billing period",
        example={
            "start": "2025-02-01T00:00:00Z",
            "end": "2025-02-28T23:59:59Z"
        }
    )

    # Campaign billing summary
    total_campaigns: int = Field(..., description="Total number of campaigns")
    active_campaigns: int = Field(..., description="Number of active campaigns")
    total_spend: Decimal = Field(..., description="Total spend amount")

    # Spend breakdown by category
    spend_by_category: Dict[str, Decimal] = Field(
        ...,
        description="Spend breakdown by category",
        example={
            "campaign_budget": 15000.00,
            "placement_fee": 5000.00,
            "platform_fee": 2000.00
        }
    )

    # Payment provider breakdown
    spend_by_provider: Dict[str, Decimal] = Field(
        ...,
        description="Spend breakdown by payment provider",
        example={
            "paystack": 18000.00,
            "stripe": 3000.00,
            "busha": 1000.00
        }
    )

    # Performance summary
    total_impressions: int = Field(..., description="Total impressions")
    total_clicks: int = Field(..., description="Total clicks")
    total_conversions: int = Field(..., description="Total conversions")
    total_revenue: Decimal = Field(..., description="Total revenue generated")

    # Calculated metrics
    average_cpc: Decimal = Field(..., description="Average cost per click")
    average_cpa: Decimal = Field(..., description="Average cost per acquisition")
    overall_roas: Decimal = Field(..., description="Overall return on ad spend")

    # Outstanding amounts
    outstanding_amount: Decimal = Field(..., description="Outstanding payment amount")
    next_payment_due: Optional[datetime] = Field(None, description="Next payment due date")

    # Payment methods
    payment_methods: List[Dict[str, Any]] = Field(
        ...,
        description="Available payment methods for billing"
    )


# Targeting Schemas
class GeographicTargetingSchema(PromotionalBaseSchema):
    """Schema for geographic targeting configuration."""

    countries: Optional[List[str]] = Field(
        None,
        description="Target countries (ISO 2-letter codes)",
        example=["NG", "US", "GB", "CA"]
    )

    states: Optional[List[str]] = Field(
        None,
        description="Target states/provinces",
        example=["Lagos", "Abuja", "Rivers", "Ogun"]
    )

    cities: Optional[List[str]] = Field(
        None,
        description="Target cities",
        example=["Lagos", "Abuja", "Port Harcourt", "Kano"]
    )

    radius_km: Optional[int] = Field(
        None,
        ge=1,
        le=1000,
        description="Targeting radius in kilometers",
        example=50
    )

    coordinates: Optional[Dict[str, float]] = Field(
        None,
        description="Geographic coordinates for radius targeting",
        example={"latitude": 6.5244, "longitude": 3.3792}
    )

    exclude_locations: Optional[List[str]] = Field(
        None,
        description="Locations to exclude from targeting",
        example=["Remote areas", "Conflict zones"]
    )

    timezone_targeting: Optional[List[str]] = Field(
        None,
        description="Target specific timezones",
        example=["Africa/Lagos", "America/New_York"]
    )

    @field_validator('countries')
    @classmethod
    def validate_countries(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate country codes."""
        if v is not None:
            for country in v:
                if len(country) != 2 or not country.isupper():
                    raise ValueError(f"Invalid country code: {country}. Must be 2-letter ISO code.")
        return v


class DemographicTargetingSchema(PromotionalBaseSchema):
    """Schema for demographic targeting configuration."""

    age_range: Optional[Dict[str, int]] = Field(
        None,
        description="Age range targeting",
        example={"min": 25, "max": 55}
    )

    gender: Optional[List[str]] = Field(
        None,
        description="Gender targeting",
        example=["male", "female", "non-binary"]
    )

    income_level: Optional[List[str]] = Field(
        None,
        description="Income level targeting",
        example=["low", "middle", "high", "luxury"]
    )

    education: Optional[List[str]] = Field(
        None,
        description="Education level targeting",
        example=["high_school", "university", "postgraduate", "professional"]
    )

    occupation: Optional[List[str]] = Field(
        None,
        description="Occupation targeting",
        example=["business", "education", "healthcare", "technology"]
    )

    marital_status: Optional[List[str]] = Field(
        None,
        description="Marital status targeting",
        example=["single", "married", "divorced", "widowed"]
    )

    household_size: Optional[Dict[str, int]] = Field(
        None,
        description="Household size targeting",
        example={"min": 1, "max": 6}
    )

    language: Optional[List[str]] = Field(
        None,
        description="Language targeting",
        example=["en", "ha", "ig", "yo"]
    )

    @field_validator('age_range')
    @classmethod
    def validate_age_range(cls, v: Optional[Dict[str, int]]) -> Optional[Dict[str, int]]:
        """Validate age range."""
        if v is not None:
            min_age = v.get('min', 0)
            max_age = v.get('max', 100)

            if min_age < 13:
                raise ValueError("Minimum age must be at least 13")
            if max_age > 100:
                raise ValueError("Maximum age cannot exceed 100")
            if min_age >= max_age:
                raise ValueError("Minimum age must be less than maximum age")

        return v


class BehavioralTargetingSchema(PromotionalBaseSchema):
    """Schema for behavioral targeting configuration."""

    interests: Optional[List[str]] = Field(
        None,
        description="Interest-based targeting",
        example=["travel", "culture", "food", "music", "art", "history"]
    )

    behaviors: Optional[List[str]] = Field(
        None,
        description="Behavioral targeting",
        example=["frequent_traveler", "culture_enthusiast", "event_attendee"]
    )

    device_types: Optional[List[str]] = Field(
        None,
        description="Device type targeting",
        example=["mobile", "desktop", "tablet"]
    )

    operating_systems: Optional[List[str]] = Field(
        None,
        description="Operating system targeting",
        example=["iOS", "Android", "Windows", "macOS"]
    )

    browsers: Optional[List[str]] = Field(
        None,
        description="Browser targeting",
        example=["Chrome", "Safari", "Firefox", "Edge"]
    )

    connection_type: Optional[List[str]] = Field(
        None,
        description="Connection type targeting",
        example=["wifi", "cellular", "broadband"]
    )

    purchase_behavior: Optional[List[str]] = Field(
        None,
        description="Purchase behavior targeting",
        example=["frequent_buyer", "price_conscious", "premium_buyer"]
    )

    engagement_level: Optional[List[str]] = Field(
        None,
        description="Engagement level targeting",
        example=["high_engagement", "medium_engagement", "low_engagement"]
    )

    time_of_day: Optional[List[str]] = Field(
        None,
        description="Time of day targeting",
        example=["morning", "afternoon", "evening", "night"]
    )

    day_of_week: Optional[List[str]] = Field(
        None,
        description="Day of week targeting",
        example=["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    )


class PaymentIntegrationSchema(PromotionalBaseSchema):
    """Schema for payment integration configuration."""

    provider_preferences: Dict[str, Any] = Field(
        ...,
        description="Payment provider preferences",
        example={
            "primary": "paystack",
            "fallback": ["stripe", "busha"],
            "currency_routing": {
                "NGN": "paystack",
                "USD": "stripe",
                "BTC": "busha"
            }
        }
    )

    # TODO-PAYSTACK-API-URL: Paystack configuration for promotional billing
    # TODO-STRIPE-API-URL: Stripe configuration for international promotional billing
    # TODO-BUSHA-API-URL: Busha configuration for cryptocurrency promotional billing

    auto_billing: bool = Field(
        default=True,
        description="Enable automatic billing for campaigns"
    )

    billing_threshold: Decimal = Field(
        default=Decimal("1000.00"),
        ge=Decimal("100.00"),
        description="Minimum amount for automatic billing"
    )

    payment_retry_attempts: int = Field(
        default=3,
        ge=1,
        le=5,
        description="Number of payment retry attempts"
    )

    webhook_endpoints: Dict[str, str] = Field(
        ...,
        description="Webhook endpoints for payment notifications",
        example={
            "payment_success": "/webhooks/promotional/payment-success",
            "payment_failed": "/webhooks/promotional/payment-failed",
            "billing_reminder": "/webhooks/promotional/billing-reminder"
        }
    )


# Export all schemas
__all__ = [
    # Base schemas
    "PromotionalBaseSchema",

    # Campaign schemas
    "CampaignCreate",
    "CampaignUpdate",
    "CampaignResponse",
    "CampaignListResponse",

    # Advertisement schemas
    "AdvertisementCreate",
    "AdvertisementUpdate",
    "AdvertisementResponse",
    "AdVariantSchema",

    # Metrics schemas
    "CampaignMetricsResponse",
    "PerformanceAnalyticsSchema",
    "OptimizationInsightsSchema",

    # Placement schemas
    "PromotionalListingCreate",
    "PromotionalListingUpdate",
    "PlacementAvailabilitySchema",

    # Financial schemas
    "AdSpendCreate",
    "AdSpendResponse",
    "PromotionalBillingSchema",

    # Targeting schemas
    "GeographicTargetingSchema",
    "DemographicTargetingSchema",
    "BehavioralTargetingSchema",
    "PaymentIntegrationSchema",
]
