"""
Unit tests for Enhanced Monitoring Service.

Tests for Task 6.2.2 Phase 4.3 enhanced monitoring service including:
- Real-time job monitoring with heartbeat tracking
- Alert management with escalation logic and delivery tracking
- System health monitoring with availability tracking
- Dashboard data aggregation for monitoring interfaces
- Circuit breaker patterns for external monitoring service reliability
- Integration with WorkflowOrchestrationService and AdvancedSchedulerService

Implements comprehensive test coverage with >80% requirement for
production-grade enhanced monitoring functionality.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.enhanced_monitoring_service import (
    EnhancedMonitoringService, MonitoringCircuitBreaker, CircuitBreakerError
)
from app.models.workflow_models import (
    JobMonitoringSession, AlertTriggerEvent, AlertDeliveryRecord,
    MonitoringStatus, AlertSeverity, AlertDeliveryStatus
)
from app.schemas.workflow_schemas import (
    RealTimeJobStatus, JobPerformanceMetrics, SystemHealthStatus,
    MonitoringDashboardData, HealthCheckRequest, HealthCheckResponse
)
from app.services.base import ServiceError, NotFoundError, ValidationError, ConflictError


class TestMonitoringCircuitBreaker:
    """Test suite for monitoring circuit breaker functionality."""

    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization with default values."""
        cb = MonitoringCircuitBreaker()

        assert cb.failure_threshold == 5
        assert cb.recovery_timeout == 60
        assert cb.call_timeout == 30
        assert cb.failure_count == 0
        assert cb.last_failure_time is None
        assert cb.state == "CLOSED"

    def test_circuit_breaker_custom_initialization(self):
        """Test circuit breaker initialization with custom values."""
        cb = MonitoringCircuitBreaker(
            failure_threshold=3,
            recovery_timeout=30,
            call_timeout=15
        )

        assert cb.failure_threshold == 3
        assert cb.recovery_timeout == 30
        assert cb.call_timeout == 15

    def test_circuit_breaker_closed_state(self):
        """Test circuit breaker behavior in closed state."""
        cb = MonitoringCircuitBreaker()

        assert cb.is_call_allowed() is True

        # Record success should keep it closed
        cb.record_success()
        assert cb.state == "CLOSED"
        assert cb.failure_count == 0

    def test_circuit_breaker_failure_tracking(self):
        """Test circuit breaker failure tracking and state transitions."""
        cb = MonitoringCircuitBreaker(failure_threshold=3)

        # Record failures below threshold
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.state == "CLOSED"
        assert cb.is_call_allowed() is True

        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.state == "CLOSED"

        # Third failure should open circuit
        cb.record_failure()
        assert cb.failure_count == 3
        assert cb.state == "OPEN"
        assert cb.is_call_allowed() is False

    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery from open to half-open state."""
        cb = MonitoringCircuitBreaker(failure_threshold=2, recovery_timeout=1)

        # Open the circuit
        cb.record_failure()
        cb.record_failure()
        assert cb.state == "OPEN"
        assert cb.is_call_allowed() is False

        # Wait for recovery timeout (simulate)
        import time
        cb.last_failure_time = time.time() - 2  # 2 seconds ago

        # Should transition to half-open
        assert cb.is_call_allowed() is True
        assert cb.state == "HALF_OPEN"

        # Success should close circuit
        cb.record_success()
        assert cb.state == "CLOSED"
        assert cb.failure_count == 0


class TestEnhancedMonitoringService:
    """Test suite for enhanced monitoring service functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return AsyncMock()

    @pytest.fixture
    def monitoring_service(self, mock_db_session):
        """Create monitoring service instance with mocked dependencies."""
        service = EnhancedMonitoringService(db_session=mock_db_session)

        # Mock repositories
        service._alert_trigger_repo = AsyncMock()
        service._alert_delivery_repo = AsyncMock()
        service._workflow_repo = AsyncMock()

        # Mock the repository property by patching the private attribute
        service._repository = AsyncMock()

        return service

    @pytest.fixture
    def sample_workflow_execution_id(self):
        """Sample workflow execution ID."""
        return uuid4()

    @pytest.fixture
    def sample_monitoring_session(self, sample_workflow_execution_id):
        """Sample monitoring session."""
        return JobMonitoringSession(
            id=uuid4(),
            workflow_execution_id=sample_workflow_execution_id,
            monitoring_status=MonitoringStatus.ACTIVE,
            heartbeat_interval_seconds=30,
            metrics_collection_enabled=True,
            progress_percentage=Decimal('45.5'),
            current_step="processing_data",
            last_heartbeat=datetime.now(timezone.utc),
            monitoring_started_at=datetime.now(timezone.utc)
        )

    @pytest.fixture
    def sample_alert_trigger_event(self):
        """Sample alert trigger event."""
        return AlertTriggerEvent(
            id=uuid4(),
            alert_id=uuid4(),
            workflow_execution_id=uuid4(),
            trigger_condition="execution_timeout",
            trigger_value="300",
            threshold_value="180",
            severity=AlertSeverity.HIGH,
            alert_message="Workflow execution timeout detected",
            alert_context={"timeout_seconds": 300},
            is_resolved=False,
            escalation_level=0,
            notification_attempts=0,
            successful_deliveries=0,
            failed_deliveries=0,
            triggered_at=datetime.now(timezone.utc)
        )

    # Validation Tests
    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, monitoring_service, sample_workflow_execution_id):
        """Test successful validation of monitoring session creation data."""
        data = {
            "workflow_execution_id": sample_workflow_execution_id,
            "heartbeat_interval_seconds": 60,
            "metrics_collection_enabled": True
        }

        validated_data = await monitoring_service.validate_create_data(data)

        assert validated_data["workflow_execution_id"] == sample_workflow_execution_id
        assert validated_data["heartbeat_interval_seconds"] == 60
        assert validated_data["metrics_collection_enabled"] is True

    @pytest.mark.asyncio
    async def test_validate_create_data_missing_required_field(self, monitoring_service):
        """Test validation failure for missing required field."""
        data = {"heartbeat_interval_seconds": 30}

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_create_data(data)

        assert "Missing required field: workflow_execution_id" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_invalid_uuid(self, monitoring_service):
        """Test validation with invalid UUID format."""
        data = {"workflow_execution_id": "invalid-uuid"}

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_create_data(data)

        assert "Invalid workflow_execution_id format" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_max_sessions_exceeded(self, monitoring_service):
        """Test validation failure when max concurrent sessions exceeded."""
        # Fill up active sessions to max
        monitoring_service.max_concurrent_sessions = 2
        monitoring_service._active_sessions = {uuid4(): {}, uuid4(): {}}

        data = {"workflow_execution_id": uuid4()}

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_create_data(data)

        assert "Maximum concurrent monitoring sessions" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_success(self, monitoring_service):
        """Test successful validation of monitoring session update data."""
        data = {
            "progress_percentage": 75.5,
            "performance_metrics": {"cpu_usage": 45.2, "memory_mb": 512}
        }

        validated_data = await monitoring_service.validate_update_data(data, uuid4())

        assert validated_data["progress_percentage"] == Decimal('75.5')
        assert validated_data["performance_metrics"]["cpu_usage"] == 45.2

    @pytest.mark.asyncio
    async def test_validate_update_data_invalid_progress(self, monitoring_service):
        """Test validation failure for invalid progress percentage."""
        data = {"progress_percentage": 150}  # Invalid: > 100

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_update_data(data, uuid4())

        assert "progress_percentage must be between 0 and 100" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_invalid_metrics_type(self, monitoring_service):
        """Test validation failure for invalid performance metrics type."""
        data = {"performance_metrics": "invalid"}  # Should be dict

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_update_data(data, uuid4())

        assert "performance_metrics must be a dictionary" in str(exc_info.value)

    # Real-time Job Monitoring Tests
    @pytest.mark.asyncio
    async def test_start_monitoring_session_success(
        self, monitoring_service, sample_workflow_execution_id, sample_monitoring_session
    ):
        """Test successful monitoring session start."""
        # Mock workflow execution exists
        monitoring_service.workflow_repo.get_execution_by_id.return_value = MagicMock()

        # Mock repository create
        monitoring_service.repository.create_monitoring_session.return_value = sample_monitoring_session

        with patch('asyncio.create_task') as mock_create_task:
            result = await monitoring_service.start_monitoring_session(
                workflow_execution_id=sample_workflow_execution_id,
                heartbeat_interval_seconds=30,
                metrics_collection_enabled=True
            )

        assert result == sample_monitoring_session
        assert sample_monitoring_session.id in monitoring_service._active_sessions

        # Verify repository calls
        monitoring_service.workflow_repo.get_execution_by_id.assert_called_once()
        monitoring_service.repository.create_monitoring_session.assert_called_once()
        mock_create_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_monitoring_session_workflow_not_found(
        self, monitoring_service, sample_workflow_execution_id
    ):
        """Test monitoring session start with non-existent workflow."""
        # Mock workflow execution not found
        monitoring_service.workflow_repo.get_execution_by_id.return_value = None

        with pytest.raises(NotFoundError) as exc_info:
            await monitoring_service.start_monitoring_session(
                workflow_execution_id=sample_workflow_execution_id
            )

        assert f"Workflow execution {sample_workflow_execution_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_monitoring_heartbeat_success(
        self, monitoring_service, sample_monitoring_session
    ):
        """Test successful monitoring heartbeat update."""
        session_id = sample_monitoring_session.id

        # Add session to active sessions
        monitoring_service._active_sessions[session_id] = {
            "workflow_execution_id": sample_monitoring_session.workflow_execution_id,
            "started_at": datetime.now(timezone.utc),
            "last_heartbeat": datetime.now(timezone.utc)
        }

        # Mock repository update
        updated_session = sample_monitoring_session
        updated_session.progress_percentage = Decimal('60.0')
        monitoring_service.repository.update_heartbeat.return_value = updated_session

        result = await monitoring_service.update_monitoring_heartbeat(
            session_id=session_id,
            current_step="data_validation",
            progress_percentage=60.0,
            performance_metrics={"cpu_usage": 55.3}
        )

        assert result == updated_session
        assert monitoring_service._active_sessions[session_id]["progress"] == 60.0
        assert len(monitoring_service._performance_metrics) == 1

        # Verify repository call
        monitoring_service.repository.update_heartbeat.assert_called_once()

    @pytest.mark.asyncio
    async def test_end_monitoring_session_success(
        self, monitoring_service, sample_monitoring_session
    ):
        """Test successful monitoring session end."""
        session_id = sample_monitoring_session.id

        # Add session to active sessions
        monitoring_service._active_sessions[session_id] = {}

        # Mock repository end
        monitoring_service.repository.end_monitoring_session.return_value = sample_monitoring_session

        result = await monitoring_service.end_monitoring_session(session_id)

        assert result == sample_monitoring_session
        assert session_id not in monitoring_service._active_sessions

        # Verify repository call
        monitoring_service.repository.end_monitoring_session.assert_called_once_with(
            session_id, monitoring_service.correlation_id
        )

    @pytest.mark.asyncio
    async def test_get_real_time_job_status_success(
        self, monitoring_service, sample_workflow_execution_id, sample_monitoring_session
    ):
        """Test successful real-time job status retrieval."""
        # Mock workflow execution
        mock_workflow_execution = MagicMock()
        mock_workflow_execution.workflow_definition_id = uuid4()
        mock_workflow_execution.status = "running"
        mock_workflow_execution.steps_completed = 3
        mock_workflow_execution.steps_total = 5
        mock_workflow_execution.started_at = datetime.now(timezone.utc)
        mock_workflow_execution.estimated_completion_at = datetime.now(timezone.utc) + timedelta(minutes=30)

        monitoring_service.workflow_repo.get_execution_by_id.return_value = mock_workflow_execution
        monitoring_service.repository.get_active_sessions.return_value = [sample_monitoring_session]

        result = await monitoring_service.get_real_time_job_status(sample_workflow_execution_id)

        assert isinstance(result, RealTimeJobStatus)
        assert result.job_id == sample_workflow_execution_id
        assert result.workflow_definition_id == mock_workflow_execution.workflow_definition_id
        assert result.status == "running"
        assert result.progress_percentage == sample_monitoring_session.progress_percentage
        assert result.current_step == sample_monitoring_session.current_step

    @pytest.mark.asyncio
    async def test_get_real_time_job_status_workflow_not_found(
        self, monitoring_service, sample_workflow_execution_id
    ):
        """Test real-time job status with non-existent workflow."""
        monitoring_service.workflow_repo.get_execution_by_id.return_value = None

        with pytest.raises(NotFoundError) as exc_info:
            await monitoring_service.get_real_time_job_status(sample_workflow_execution_id)

        assert f"Workflow execution {sample_workflow_execution_id} not found" in str(exc_info.value)

    # Alert Management Tests
    @pytest.mark.asyncio
    async def test_trigger_alert_success(
        self, monitoring_service, sample_alert_trigger_event
    ):
        """Test successful alert triggering."""
        alert_id = uuid4()

        # Mock repository create
        monitoring_service.alert_trigger_repo.create_trigger_event.return_value = sample_alert_trigger_event

        with patch('asyncio.create_task') as mock_create_task:
            result = await monitoring_service.trigger_alert(
                alert_id=alert_id,
                trigger_condition="execution_timeout",
                alert_message="Workflow execution timeout detected",
                severity=AlertSeverity.HIGH,
                workflow_execution_id=uuid4(),
                trigger_value="300",
                threshold_value="180",
                alert_context={"timeout_seconds": 300}
            )

        assert result == sample_alert_trigger_event
        assert sample_alert_trigger_event.id in monitoring_service._alert_cache

        # Verify repository call
        monitoring_service.alert_trigger_repo.create_trigger_event.assert_called_once()
        mock_create_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_alert_success(
        self, monitoring_service, sample_alert_trigger_event
    ):
        """Test successful alert resolution."""
        trigger_event_id = sample_alert_trigger_event.id

        # Add alert to cache
        monitoring_service._alert_cache[trigger_event_id] = sample_alert_trigger_event

        # Mock repository resolve
        resolved_event = sample_alert_trigger_event
        resolved_event.is_resolved = True
        monitoring_service.alert_trigger_repo.resolve_trigger_event.return_value = resolved_event

        result = await monitoring_service.resolve_alert(
            trigger_event_id=trigger_event_id,
            resolution_message="Issue resolved"
        )

        assert result == resolved_event
        assert trigger_event_id not in monitoring_service._alert_cache

        # Verify repository call
        monitoring_service.alert_trigger_repo.resolve_trigger_event.assert_called_once()

    # System Health Monitoring Tests
    @pytest.mark.asyncio
    async def test_check_system_health_success(self, monitoring_service):
        """Test successful system health check."""
        components = ["database", "redis", "celery"]

        result = await monitoring_service.check_system_health(
            components=components,
            include_dependencies=True,
            timeout_seconds=30
        )

        assert isinstance(result, HealthCheckResponse)
        assert result.overall_status in ["healthy", "degraded", "unhealthy"]
        assert len(result.components) == len(components)
        assert result.response_time_ms > 0
        assert isinstance(result.system_metrics, dict)
        assert isinstance(result.workflow_metrics, dict)
        assert isinstance(result.alerts_summary, dict)

    @pytest.mark.asyncio
    async def test_check_system_health_default_components(self, monitoring_service):
        """Test system health check with default components."""
        result = await monitoring_service.check_system_health()

        assert isinstance(result, HealthCheckResponse)
        # Should check all default components
        expected_components = [
            "database", "redis", "celery", "workflow_orchestration",
            "advanced_scheduler", "monitoring_sessions", "alert_system"
        ]
        assert len(result.components) == len(expected_components)

    @pytest.mark.asyncio
    async def test_get_monitoring_dashboard_data_success(self, monitoring_service):
        """Test successful monitoring dashboard data retrieval."""
        time_range_hours = 24

        result = await monitoring_service.get_monitoring_dashboard_data(time_range_hours)

        assert isinstance(result, MonitoringDashboardData)
        assert result.time_range_start < result.time_range_end
        assert result.total_jobs >= 0
        assert result.successful_jobs >= 0
        assert result.failed_jobs >= 0
        assert result.running_jobs >= 0
        assert result.queued_jobs >= 0
        assert result.success_rate_percentage >= 0
        assert result.throughput_jobs_per_hour >= 0
        assert result.system_health_score >= 0
        assert isinstance(result.top_failing_workflows, list)
        assert isinstance(result.performance_trends, dict)
        assert isinstance(result.resource_utilization, dict)

    # Circuit Breaker Integration Tests
    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self, monitoring_service):
        """Test circuit breaker integration with external services."""
        service_name = "email_service"
        circuit_breaker = monitoring_service._circuit_breakers[service_name]

        # Initially closed
        assert circuit_breaker.state == "CLOSED"
        assert circuit_breaker.is_call_allowed() is True

        # Simulate failures to open circuit
        for _ in range(5):  # Default failure threshold
            circuit_breaker.record_failure()

        assert circuit_breaker.state == "OPEN"
        assert circuit_breaker.is_call_allowed() is False

    # Performance Tests
    @pytest.mark.asyncio
    async def test_monitoring_session_performance_tracking(self, monitoring_service):
        """Test performance metrics tracking in monitoring sessions."""
        session_id = uuid4()

        # Add session to active sessions
        monitoring_service._active_sessions[session_id] = {
            "workflow_execution_id": uuid4(),
            "started_at": datetime.now(timezone.utc),
            "last_heartbeat": datetime.now(timezone.utc)
        }

        # Mock repository update
        mock_session = MagicMock()
        monitoring_service.repository.update_heartbeat.return_value = mock_session

        # Update with performance metrics
        performance_metrics = {
            "cpu_usage_percent": 45.2,
            "memory_usage_mb": 512,
            "disk_io_mbps": 12.5,
            "network_io_mbps": 8.3
        }

        await monitoring_service.update_monitoring_heartbeat(
            session_id=session_id,
            performance_metrics=performance_metrics
        )

        # Verify performance metrics are tracked
        assert len(monitoring_service._performance_metrics) == 1
        recorded_metric = monitoring_service._performance_metrics[0]
        assert recorded_metric["session_id"] == session_id
        assert recorded_metric["metrics"] == performance_metrics

    @pytest.mark.asyncio
    async def test_concurrent_session_limit_enforcement(self, monitoring_service):
        """Test enforcement of concurrent session limits."""
        # Set low limit for testing
        monitoring_service.max_concurrent_sessions = 2

        # Fill up to limit
        for i in range(2):
            session_id = uuid4()
            monitoring_service._active_sessions[session_id] = {}

        # Attempt to exceed limit
        data = {"workflow_execution_id": uuid4()}

        with pytest.raises(ValidationError) as exc_info:
            await monitoring_service.validate_create_data(data)

        assert "Maximum concurrent monitoring sessions" in str(exc_info.value)

    # Error Handling Tests
    @pytest.mark.asyncio
    async def test_service_error_handling(self, monitoring_service):
        """Test comprehensive service error handling."""
        # Mock repository to raise exception
        monitoring_service.workflow_repo.get_execution_by_id.side_effect = Exception("Database error")

        with pytest.raises(ServiceError):
            await monitoring_service.start_monitoring_session(
                workflow_execution_id=uuid4()
            )

    @pytest.mark.asyncio
    async def test_alert_cache_management(self, monitoring_service, sample_alert_trigger_event):
        """Test alert cache management functionality."""
        trigger_event_id = sample_alert_trigger_event.id

        # Add alert to cache
        monitoring_service._alert_cache[trigger_event_id] = sample_alert_trigger_event

        # Verify cache contains alert
        assert trigger_event_id in monitoring_service._alert_cache
        assert len(monitoring_service._alert_cache) == 1

        # Mock repository resolve
        monitoring_service.alert_trigger_repo.resolve_trigger_event.return_value = sample_alert_trigger_event

        # Resolve alert
        await monitoring_service.resolve_alert(trigger_event_id)

        # Verify cache is cleaned up
        assert trigger_event_id not in monitoring_service._alert_cache
        assert len(monitoring_service._alert_cache) == 0

    # Integration Tests
    @pytest.mark.asyncio
    async def test_health_check_component_integration(self, monitoring_service):
        """Test health check component integration."""
        # Test individual component health checks
        components = [
            "database", "redis", "celery", "workflow_orchestration",
            "advanced_scheduler", "monitoring_sessions", "alert_system"
        ]

        for component in components:
            status = await monitoring_service._check_component_health(component, 30)
            assert isinstance(status, SystemHealthStatus)
            assert status.component_name == component
            assert status.status in ["healthy", "degraded", "unhealthy"]
            assert status.response_time_ms >= 0

    @pytest.mark.asyncio
    async def test_system_metrics_collection(self, monitoring_service):
        """Test system metrics collection functionality."""
        system_metrics = await monitoring_service._collect_system_metrics()

        assert isinstance(system_metrics, dict)
        assert "cpu_usage_percent" in system_metrics
        assert "memory_usage_percent" in system_metrics
        assert "disk_usage_percent" in system_metrics
        assert "network_io_mbps" in system_metrics
        assert "active_connections" in system_metrics

    @pytest.mark.asyncio
    async def test_workflow_metrics_collection(self, monitoring_service):
        """Test workflow metrics collection functionality."""
        workflow_metrics = await monitoring_service._collect_workflow_metrics()

        assert isinstance(workflow_metrics, dict)
        assert "active_workflows" in workflow_metrics
        assert "total_executions_today" in workflow_metrics
        assert "average_execution_time_seconds" in workflow_metrics
        assert "success_rate_percent" in workflow_metrics
        assert "queue_depth" in workflow_metrics

    @pytest.mark.asyncio
    async def test_alerts_summary_generation(self, monitoring_service):
        """Test alerts summary generation functionality."""
        # Add sample alerts to cache
        alert1 = MagicMock()
        alert1.severity = AlertSeverity.CRITICAL
        alert2 = MagicMock()
        alert2.severity = AlertSeverity.HIGH
        alert3 = MagicMock()
        alert3.severity = AlertSeverity.MEDIUM

        monitoring_service._alert_cache = {
            uuid4(): alert1,
            uuid4(): alert2,
            uuid4(): alert3
        }

        summary = await monitoring_service._get_alerts_summary()

        assert isinstance(summary, dict)
        assert summary["critical"] == 1
        assert summary["high"] == 1
        assert summary["medium"] == 1
        assert summary["low"] == 0

    @pytest.mark.asyncio
    async def test_health_recommendations_generation(self, monitoring_service):
        """Test health recommendations generation."""
        # Create mock component statuses
        component_statuses = [
            SystemHealthStatus(
                component_name="database",
                status="healthy",
                response_time_ms=Decimal('10'),
                availability_percentage=Decimal('99.9'),
                error_rate_percentage=Decimal('0.1'),
                last_check_at=datetime.now(timezone.utc),
                details={}
            ),
            SystemHealthStatus(
                component_name="redis",
                status="unhealthy",
                response_time_ms=Decimal('1000'),
                availability_percentage=Decimal('50'),
                error_rate_percentage=Decimal('50'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": "Connection timeout"}
            )
        ]

        system_metrics = {
            "memory_usage_percent": 85,  # High memory usage
            "cpu_usage_percent": 90      # High CPU usage
        }

        recommendations = monitoring_service._generate_health_recommendations(
            component_statuses, system_metrics
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) >= 3  # Should have recommendations for unhealthy component and high resource usage
        assert any("unhealthy components" in rec for rec in recommendations)
        assert any("High memory usage" in rec for rec in recommendations)
        assert any("High CPU usage" in rec for rec in recommendations)

    # Performance Validation Tests
    @pytest.mark.asyncio
    async def test_performance_targets_validation(self, monitoring_service):
        """Test that service meets performance targets."""
        import time

        # Test monitoring latency target (<100ms)
        start_time = time.time()
        await monitoring_service._collect_system_metrics()
        latency_ms = (time.time() - start_time) * 1000
        assert latency_ms < 100, f"Monitoring latency {latency_ms}ms exceeds 100ms target"

        # Test alert generation target (<50ms)
        start_time = time.time()
        await monitoring_service._get_alerts_summary()
        alert_generation_ms = (time.time() - start_time) * 1000
        assert alert_generation_ms < 50, f"Alert generation {alert_generation_ms}ms exceeds 50ms target"
