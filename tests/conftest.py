"""
Pytest configuration and shared fixtures for Culture Connect Backend tests.

This module provides common test fixtures and configuration for all tests including:
- Database session management
- Mock service instances
- Test data factories
- Authentication mocks
- SMTP mocking utilities
- Geolocation testing fixtures
- Performance testing utilities

Implements comprehensive testing requirements with >80% test coverage.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from uuid import uuid4
from typing import Async<PERSON>enerator

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from fastapi import FastAPI

from app.db.database import get_async_session
from app.models.email_models import (
    EmailTemplate, EmailDelivery, EmailPreference, EmailQueue,
    EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
)
from app.models.review_models import (
    Review, ReviewResponse, ReviewModeration, ReviewAnalytics,
    ReviewStatus, ResponseStatus, ModerationAction
)


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_async_session():
    """Create a mock async database session."""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    session.close = AsyncMock()
    session.execute = AsyncMock()
    return session


@pytest.fixture
def override_get_async_session(mock_async_session):
    """Override the database session dependency for testing."""
    async def _override_get_async_session():
        yield mock_async_session

    # Return the override function for manual use in tests
    yield _override_get_async_session


@pytest.fixture
def test_client():
    """Create a test client with mocked dependencies."""
    # Create a minimal FastAPI app for testing
    from fastapi import FastAPI
    test_app = FastAPI()
    return TestClient(test_app)


@pytest.fixture
async def async_test_client():
    """Create an async test client with mocked dependencies."""
    from httpx import AsyncClient
    from fastapi import FastAPI

    # Create a minimal FastAPI app for testing
    test_app = FastAPI()

    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


@pytest.fixture
def mock_current_user():
    """Mock current user for authentication tests."""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "role": "customer",
        "is_active": True,
        "is_verified": True
    }


@pytest.fixture
def mock_admin_user():
    """Mock admin user for authentication tests."""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "User",
        "role": "admin",
        "is_active": True,
        "is_verified": True
    }


@pytest.fixture
def mock_vendor_user():
    """Mock vendor user for authentication tests."""
    return {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Vendor",
        "last_name": "User",
        "role": "vendor",
        "is_active": True,
        "is_verified": True
    }


# Email Model Factories
@pytest.fixture
def email_template_factory():
    """Factory for creating email template instances."""
    def _create_template(**kwargs):
        defaults = {
            "id": uuid4(),
            "name": "test_template",
            "category": EmailTemplateCategory.NOTIFICATION,
            "subject_template": "Test Subject {{name}}",
            "body_template": "Test Body {{name}}",
            "variables": {"name": "string"},
            "version": 1,
            "is_active": True,
            "created_by": 1,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        defaults.update(kwargs)
        return EmailTemplate(**defaults)
    return _create_template


@pytest.fixture
def email_delivery_factory():
    """Factory for creating email delivery instances."""
    def _create_delivery(**kwargs):
        defaults = {
            "id": uuid4(),
            "user_id": 1,
            "recipient_email": "<EMAIL>",
            "subject": "Test Subject",
            "body": "Test Body",
            "status": EmailDeliveryStatus.PENDING,
            "priority": 3,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        defaults.update(kwargs)
        return EmailDelivery(**defaults)
    return _create_delivery


@pytest.fixture
def email_preference_factory():
    """Factory for creating email preference instances."""
    def _create_preference(**kwargs):
        defaults = {
            "user_id": 1,
            "marketing_emails": True,
            "booking_notifications": True,
            "security_emails": True,
            "verification_emails": True,
            "system_notifications": False,
            "opted_out": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        defaults.update(kwargs)
        return EmailPreference(**defaults)
    return _create_preference


@pytest.fixture
def email_queue_factory():
    """Factory for creating email queue instances."""
    def _create_queue_item(**kwargs):
        defaults = {
            "id": uuid4(),
            "user_id": 1,
            "recipient_email": "<EMAIL>",
            "subject": "Queued Email",
            "body": "Queued Body",
            "priority": 3,
            "status": EmailQueueStatus.QUEUED,
            "retry_count": 0,
            "max_retries": 3,
            "created_at": datetime.now(timezone.utc),
            "scheduled_at": datetime.now(timezone.utc)
        }
        defaults.update(kwargs)
        return EmailQueue(**defaults)
    return _create_queue_item


# SMTP Mocking Utilities
@pytest.fixture
def mock_smtp_server():
    """Mock SMTP server for email delivery testing."""
    with patch('smtplib.SMTP') as mock_smtp:
        mock_server = MagicMock()
        mock_smtp.return_value = mock_server
        mock_server.starttls.return_value = None
        mock_server.login.return_value = None
        mock_server.send_message.return_value = {}
        mock_server.quit.return_value = None
        yield mock_server


@pytest.fixture
def mock_smtp_ssl_server():
    """Mock SMTP SSL server for secure email delivery testing."""
    with patch('smtplib.SMTP_SSL') as mock_smtp_ssl:
        mock_server = MagicMock()
        mock_smtp_ssl.return_value = mock_server
        mock_server.login.return_value = None
        mock_server.send_message.return_value = {}
        mock_server.quit.return_value = None
        yield mock_server


@pytest.fixture
def mock_email_config():
    """Mock email configuration for testing."""
    return {
        "SMTP_HOST": "smtp.test.com",
        "SMTP_PORT": 587,
        "SMTP_USERNAME": "<EMAIL>",
        "SMTP_PASSWORD": "test_password",
        "SMTP_USE_TLS": True,
        "FROM_EMAIL": "<EMAIL>",
        "FROM_NAME": "Test App"
    }


# Authentication Mocking
@pytest.fixture
def mock_get_current_user(mock_current_user):
    """Mock the get_current_user dependency."""
    with patch('app.api.v1.endpoints.email.get_current_user', return_value=mock_current_user):
        yield mock_current_user


@pytest.fixture
def mock_get_current_admin_user(mock_admin_user):
    """Mock the get_current_admin_user dependency."""
    with patch('app.api.v1.endpoints.email.get_current_admin_user', return_value=mock_admin_user):
        yield mock_admin_user


# Service Mocking
@pytest.fixture
def mock_email_service():
    """Mock EmailService for endpoint testing."""
    with patch('app.api.v1.endpoints.email.EmailService') as mock_service_class:
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        yield mock_service


@pytest.fixture
def mock_correlation_id():
    """Mock correlation ID for logging tests."""
    with patch('app.api.v1.endpoints.email.correlation_id') as mock_corr_id:
        mock_corr_id.get.return_value = 'test-correlation-id'
        yield mock_corr_id


# Test Data Sets
@pytest.fixture
def sample_template_data():
    """Sample template creation data."""
    return {
        "name": "test_template",
        "category": "NOTIFICATION",
        "subject_template": "Test Subject {{name}}",
        "body_template": "Test Body {{name}}",
        "variables": {"name": "string"}
    }


@pytest.fixture
def sample_email_data():
    """Sample email sending data."""
    return {
        "recipient_email": "<EMAIL>",
        "subject": "Test Subject",
        "body": "Test Body"
    }


@pytest.fixture
def sample_batch_email_data():
    """Sample batch email sending data."""
    return {
        "template_id": str(uuid4()),
        "recipients": [
            {"email": "<EMAIL>", "variables": {"name": "User 1"}},
            {"email": "<EMAIL>", "variables": {"name": "User 2"}}
        ]
    }


@pytest.fixture
def sample_preference_data():
    """Sample email preference data."""
    return {
        "marketing_emails": False,
        "booking_notifications": True,
        "system_notifications": False
    }


@pytest.fixture
def sample_verification_data():
    """Sample email verification data."""
    return {
        "user_id": 1,
        "email": "<EMAIL>",
        "user_name": "Test User",
        "verification_token": "test_token_123"
    }


@pytest.fixture
def sample_password_reset_data():
    """Sample password reset email data."""
    return {
        "user_id": 1,
        "email": "<EMAIL>",
        "user_name": "Test User",
        "reset_token": "reset_token_123"
    }


# Review Management Test Fixtures

@pytest.fixture
def sample_review_data():
    """Sample review data for testing."""
    return {
        "customer_id": 1,
        "vendor_id": 2,
        "service_id": 3,
        "booking_id": 123,
        "rating": 5,
        "title": "Excellent Cultural Experience",
        "content": "This was an amazing cultural tour. The guide was knowledgeable and friendly. Highly recommended!",
        "language_code": "en",
        "sentiment_score": 0.95
    }


@pytest.fixture
def sample_review_response_data():
    """Sample review response data for testing."""
    return {
        "review_id": 1,
        "vendor_id": 2,
        "content": "Thank you for your wonderful review! We're delighted you enjoyed the cultural experience.",
        "status": ResponseStatus.PUBLISHED
    }


@pytest.fixture
def sample_review_moderation_data():
    """Sample review moderation data for testing."""
    return {
        "review_id": 1,
        "action": ModerationAction.APPROVE,
        "confidence_score": 0.95,
        "reason": "Content meets community guidelines",
        "ai_analysis_results": {
            "sentiment": "positive",
            "toxicity_score": 0.02,
            "spam_probability": 0.01,
            "language_detected": "en"
        }
    }


@pytest.fixture
def sample_review_analytics_data():
    """Sample review analytics data for testing."""
    from datetime import date
    return {
        "vendor_id": 2,
        "period_start": date(2024, 1, 1),
        "period_end": date(2024, 1, 31),
        "total_reviews": 25,
        "average_rating": 4.6,
        "rating_distribution": {
            "1": 1, "2": 2, "3": 3, "4": 8, "5": 11
        },
        "sentiment_breakdown": {
            "positive": 20, "neutral": 4, "negative": 1
        },
        "response_rate": 0.88,
        "average_response_time": 24,
        "verified_reviews_count": 22,
        "helpful_votes_total": 156,
        "reported_reviews_count": 0
    }


# Review Test Factories

@pytest.fixture
def review_factory():
    """Factory for creating review test instances."""
    def _create_review(**kwargs):
        default_data = {
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_id": 123,
            "rating": 5,
            "title": "Test Review",
            "content": "This is a test review content.",
            "status": ReviewStatus.PUBLISHED,
            "language_code": "en",
            "sentiment_score": 0.8
        }
        default_data.update(kwargs)
        return Review(**default_data)
    return _create_review


@pytest.fixture
def review_response_factory():
    """Factory for creating review response test instances."""
    def _create_response(**kwargs):
        default_data = {
            "review_id": 1,
            "vendor_id": 2,
            "content": "Thank you for your review!",
            "status": ResponseStatus.PUBLISHED
        }
        default_data.update(kwargs)
        return ReviewResponse(**default_data)
    return _create_response


@pytest.fixture
def review_moderation_factory():
    """Factory for creating review moderation test instances."""
    def _create_moderation(**kwargs):
        default_data = {
            "review_id": 1,
            "action": ModerationAction.APPROVE,
            "confidence_score": 0.95,
            "reason": "Content approved",
            "ai_analysis_results": {"sentiment": "positive"}
        }
        default_data.update(kwargs)
        return ReviewModeration(**default_data)
    return _create_moderation


@pytest.fixture
def review_analytics_factory():
    """Factory for creating review analytics test instances."""
    def _create_analytics(**kwargs):
        from datetime import date
        default_data = {
            "vendor_id": 2,
            "period_start": date(2024, 1, 1),
            "period_end": date(2024, 1, 31),
            "total_reviews": 10,
            "average_rating": 4.5,
            "rating_distribution": {"5": 5, "4": 3, "3": 2},
            "sentiment_breakdown": {"positive": 8, "neutral": 2}
        }
        default_data.update(kwargs)
        return ReviewAnalytics(**default_data)
    return _create_analytics


# Mock Services for Review Testing

@pytest.fixture
def mock_review_service():
    """Mock review service for testing."""
    service = AsyncMock()
    service.create_review.return_value = AsyncMock(id=1, rating=5)
    service.update_review.return_value = AsyncMock(id=1, rating=4)
    service.get_review_by_id.return_value = AsyncMock(id=1, rating=5)
    service.get_vendor_reviews.return_value = []
    service.mark_helpful.return_value = True
    service.report_review.return_value = True
    return service


@pytest.fixture
def mock_review_response_service():
    """Mock review response service for testing."""
    service = AsyncMock()
    service.create_response.return_value = AsyncMock(id=1, content="Thank you!")
    service.publish_response.return_value = AsyncMock(id=1, status=ResponseStatus.PUBLISHED)
    service.get_vendor_responses.return_value = []
    return service


@pytest.fixture
def mock_review_moderation_service():
    """Mock review moderation service for testing."""
    service = AsyncMock()
    service.trigger_ai_moderation.return_value = AsyncMock(action=ModerationAction.APPROVE)
    service.manual_moderation.return_value = AsyncMock(action=ModerationAction.APPROVE)
    service.get_pending_moderations.return_value = []
    return service


@pytest.fixture
def mock_review_analytics_service():
    """Mock review analytics service for testing."""
    service = AsyncMock()
    service.calculate_vendor_analytics.return_value = AsyncMock(average_rating=4.5)
    service.get_analytics_trends.return_value = {"trend": "improving"}
    return service


# Database Cleanup Utilities
@pytest.fixture
async def cleanup_email_data():
    """Cleanup email test data after tests."""
    cleanup_items = []

    def add_cleanup(item):
        cleanup_items.append(item)

    yield add_cleanup

    # Cleanup logic would go here for integration tests
    # This is a placeholder for actual database cleanup


# Performance Testing Utilities
@pytest.fixture
def performance_timer():
    """Timer utility for performance testing."""
    import time

    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None

        def start(self):
            self.start_time = time.time()

        def stop(self):
            self.end_time = time.time()

        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

    return Timer()


# Error Simulation Utilities
@pytest.fixture
def smtp_error_simulator():
    """Utility for simulating SMTP errors."""
    import smtplib

    def simulate_error(error_type="connection"):
        if error_type == "connection":
            return smtplib.SMTPConnectError(421, "Connection failed")
        elif error_type == "auth":
            return smtplib.SMTPAuthenticationError(535, "Authentication failed")
        elif error_type == "recipient":
            return smtplib.SMTPRecipientsRefused({"<EMAIL>": (550, "User unknown")})
        elif error_type == "data":
            return smtplib.SMTPDataError(554, "Message rejected")
        else:
            return smtplib.SMTPException("Generic SMTP error")

    return simulate_error


# Async Context Managers for Testing
@pytest.fixture
async def async_context_manager():
    """Utility for testing async context managers."""
    class AsyncContextManager:
        def __init__(self, return_value=None):
            self.return_value = return_value

        async def __aenter__(self):
            return self.return_value

        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass

    return AsyncContextManager


# ============================================================================
# GEOLOCATION TESTING FIXTURES
# ============================================================================

# Import geolocation test fixtures
from tests.fixtures.geolocation_fixtures import *
from tests.conftest_analytics import *

# Configure pytest for geolocation testing
def pytest_configure(config):
    """Configure pytest for comprehensive testing."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "load: mark test as load test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "geolocation: mark test as geolocation test"
    )

    # Set test environment variables
    import os
    os.environ["ENVIRONMENT"] = "testing"
    os.environ["CC_GEOLOCATION_TEST_MODE"] = "true"
    os.environ["CC_GEOLOCATION_LOG_LEVEL"] = "DEBUG"


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add integration marker to integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Add performance marker to performance tests
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)

        # Add load marker to load tests
        if "load" in str(item.fspath):
            item.add_marker(pytest.mark.load)
            item.add_marker(pytest.mark.slow)

        # Add geolocation marker to geolocation tests
        if "geolocation" in str(item.fspath):
            item.add_marker(pytest.mark.geolocation)


@pytest.fixture(scope="function")
def test_environment_setup():
    """Set up test environment variables."""
    import os
    original_env = os.environ.copy()

    # Set test environment variables
    test_env = {
        "ENVIRONMENT": "testing",
        "CC_GEOLOCATION_TEST_MODE": "true",
        "CC_GEOIP_DATABASE_PATH": "./tests/data/test_geoip.mmdb",
        "CC_GEOLOCATION_CACHE_TTL_HOURS": "0.1",
        "CC_GEOLOCATION_TIMEOUT_MS": "1000",
        "CC_GEOLOCATION_MAX_RETRIES": "1",
        "CC_GEOLOCATION_LOG_LEVEL": "DEBUG",
        "CC_GEOLOCATION_REDIS_PREFIX": "geo:test:",
        "CC_GEOLOCATION_REDIS_TTL": "360",
        "MAXMIND_LICENSE_KEY": "test_license_key"
    }

    os.environ.update(test_env)

    yield test_env

    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


# ============================================================================
# PROMOTIONAL SYSTEM TESTING FIXTURES (Phase 5)
# ============================================================================

# Import promotional test fixtures
try:
    from tests.promotional.conftest import (
        campaign_factory, advertisement_factory, campaign_metrics_factory,
        promotional_listing_factory, ad_spend_factory, mock_promotional_repository,
        mock_payment_service, mock_rbac_service, sample_campaign_data,
        sample_advertisement_data, performance_test_data
    )
except ImportError:
    # Promotional fixtures not available yet
    pass


@pytest.fixture(scope="function")
async def clean_test_logs():
    """Clean test logs before and after tests."""
    from pathlib import Path
    log_dir = Path("tests/logs")
    log_dir.mkdir(exist_ok=True)

    # Clean logs before test
    for log_file in log_dir.glob("*.log"):
        log_file.unlink(missing_ok=True)

    yield log_dir


@pytest.fixture(scope="function")
def performance_monitor():
    """Monitor performance during tests."""
    import psutil
    import time

    process = psutil.Process()
    start_time = time.perf_counter()
    start_memory = process.memory_info().rss / 1024 / 1024  # MB

    class PerformanceMonitor:
        def get_metrics(self):
            current_time = time.perf_counter()
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            current_cpu = process.cpu_percent()

            return {
                "elapsed_time": current_time - start_time,
                "memory_usage_mb": current_memory,
                "memory_delta_mb": current_memory - start_memory,
                "cpu_percent": current_cpu
            }

    return PerformanceMonitor()


# Skip markers for CI/CD
def pytest_runtest_setup(item):
    """Setup for individual test runs."""
    import os
    # Skip slow tests in CI unless explicitly requested
    if "CI" in os.environ and not os.environ.get("RUN_SLOW_TESTS"):
        if item.get_closest_marker("slow"):
            pytest.skip("Skipping slow test in CI environment")

    # Skip load tests unless explicitly requested
    if not os.environ.get("RUN_LOAD_TESTS"):
        if item.get_closest_marker("load"):
            pytest.skip("Skipping load test (set RUN_LOAD_TESTS=1 to enable)")


# Custom assertions for geolocation testing
class GeolocationAssertions:
    """Custom assertions for geolocation testing."""

    @staticmethod
    def assert_valid_geolocation_result(result, expected_country=None):
        """Assert that a geolocation result is valid."""
        assert result is not None, "Geolocation result should not be None"
        assert hasattr(result, 'country_code'), "Result should have country_code"
        assert hasattr(result, 'ip_address'), "Result should have ip_address"
        assert hasattr(result, 'confidence_score'), "Result should have confidence_score"
        assert hasattr(result, 'detection_method'), "Result should have detection_method"

        if expected_country:
            assert result.country_code == expected_country, f"Expected country {expected_country}, got {result.country_code}"

        assert 0 <= result.confidence_score <= 1, f"Confidence score {result.confidence_score} should be between 0 and 1"

    @staticmethod
    def assert_performance_within_threshold(actual_ms, threshold_ms, operation_name="operation"):
        """Assert that performance is within threshold."""
        assert actual_ms <= threshold_ms, f"{operation_name} took {actual_ms:.2f}ms, exceeds threshold {threshold_ms}ms"

    @staticmethod
    def assert_success_rate_above_threshold(success_count, total_count, threshold=0.95):
        """Assert that success rate is above threshold."""
        success_rate = success_count / total_count if total_count > 0 else 0
        assert success_rate >= threshold, f"Success rate {success_rate:.2%} below threshold {threshold:.2%}"


@pytest.fixture
def geo_assertions():
    """Provide geolocation assertions."""
    return GeolocationAssertions()


# Test data validation
@pytest.fixture(scope="session", autouse=True)
def validate_test_data():
    """Validate test data availability."""
    from pathlib import Path
    test_db_path = Path("tests/data/test_geoip.mmdb")

    if not test_db_path.exists():
        # Create test database if it doesn't exist
        test_db_path.parent.mkdir(exist_ok=True)
        test_db_path.write_bytes(b"MOCK_GEOIP_DATABASE_FOR_TESTING")

    assert test_db_path.exists(), "Test GeoIP database not found"
    assert test_db_path.stat().st_size > 0, "Test GeoIP database is empty"
