"""
Infrastructure Optimization repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for infrastructure optimization including:
- CacheConfigurationRepository: Redis cache configuration management
- BackgroundTaskRepository: Celery task tracking and management
- PerformanceMonitoringRepository: Query performance tracking
- CacheMetricsRepository: Cache performance analytics
- TaskMetricsRepository: Background task performance analytics

Implements Task 3.2.3 requirements with production-grade PostgreSQL optimization,
async/await patterns, and seamless integration with existing repository architecture.
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import select, update, delete, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.infrastructure_optimization import (
    CacheConfiguration, BackgroundTask, PerformanceMonitoring,
    CacheMetrics, TaskMetrics, TaskStatus, TaskPriority, CacheType
)
from app.schemas.infrastructure_optimization import (
    CacheConfigurationCreate, CacheConfigurationUpdate,
    BackgroundTaskCreate, BackgroundTaskUpdate,
    PerformanceMonitoringCreate, CacheMetricsCreate, TaskMetricsCreate
)
from app.core.logging import get_logger

logger = get_logger(__name__)


class CacheConfigurationRepository(BaseRepository[CacheConfiguration, CacheConfigurationCreate, CacheConfigurationUpdate]):
    """Repository for cache configuration management."""

    def __init__(self, session: AsyncSession):
        super().__init__(CacheConfiguration, session)

    async def get_by_cache_type(self, cache_type: CacheType) -> Optional[CacheConfiguration]:
        """
        Get cache configuration by cache type.
        
        Args:
            cache_type: Type of cache configuration
            
        Returns:
            CacheConfiguration or None if not found
        """
        try:
            query = select(self.model).where(
                and_(
                    self.model.cache_type == cache_type,
                    self.model.is_active == True
                )
            )
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting cache configuration by type {cache_type}: {str(e)}")
            raise

    async def get_active_configurations(self) -> List[CacheConfiguration]:
        """
        Get all active cache configurations.
        
        Returns:
            List of active cache configurations
        """
        try:
            query = select(self.model).where(
                self.model.is_active == True
            ).order_by(self.model.cache_type)
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting active cache configurations: {str(e)}")
            raise

    async def update_ttl(self, cache_type: CacheType, new_ttl: int) -> Optional[CacheConfiguration]:
        """
        Update TTL for specific cache type.
        
        Args:
            cache_type: Type of cache configuration
            new_ttl: New TTL in seconds
            
        Returns:
            Updated cache configuration or None
        """
        try:
            query = update(self.model).where(
                and_(
                    self.model.cache_type == cache_type,
                    self.model.is_active == True
                )
            ).values(
                ttl_seconds=new_ttl,
                updated_at=func.now()
            ).returning(self.model)
            
            result = await self.session.execute(query)
            await self.session.commit()
            return result.scalar_one_or_none()
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error updating TTL for cache type {cache_type}: {str(e)}")
            raise


class BackgroundTaskRepository(BaseRepository[BackgroundTask, BackgroundTaskCreate, BackgroundTaskUpdate]):
    """Repository for background task management."""

    def __init__(self, session: AsyncSession):
        super().__init__(BackgroundTask, session)

    async def get_by_task_id(self, task_id: str) -> Optional[BackgroundTask]:
        """
        Get background task by Celery task ID.
        
        Args:
            task_id: Celery task ID
            
        Returns:
            BackgroundTask or None if not found
        """
        try:
            query = select(self.model).where(self.model.task_id == task_id)
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting background task by task_id {task_id}: {str(e)}")
            raise

    async def get_by_status(self, status: TaskStatus, limit: int = 100) -> List[BackgroundTask]:
        """
        Get background tasks by status.
        
        Args:
            status: Task status to filter by
            limit: Maximum number of tasks to return
            
        Returns:
            List of background tasks
        """
        try:
            query = select(self.model).where(
                self.model.status == status
            ).order_by(desc(self.model.queued_at)).limit(limit)
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting background tasks by status {status}: {str(e)}")
            raise

    async def get_pending_tasks(self, priority: Optional[TaskPriority] = None) -> List[BackgroundTask]:
        """
        Get pending background tasks, optionally filtered by priority.
        
        Args:
            priority: Optional priority filter
            
        Returns:
            List of pending background tasks
        """
        try:
            conditions = [self.model.status == TaskStatus.PENDING]
            if priority:
                conditions.append(self.model.priority == priority)
            
            query = select(self.model).where(
                and_(*conditions)
            ).order_by(
                desc(self.model.priority),  # Higher priority first
                asc(self.model.queued_at)   # Older tasks first
            )
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting pending tasks: {str(e)}")
            raise

    async def update_task_status(
        self, 
        task_id: str, 
        status: TaskStatus,
        worker_name: Optional[str] = None,
        result_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> Optional[BackgroundTask]:
        """
        Update task status and related fields.
        
        Args:
            task_id: Celery task ID
            status: New task status
            worker_name: Optional worker name
            result_data: Optional result data
            error_message: Optional error message
            
        Returns:
            Updated background task or None
        """
        try:
            update_data = {"status": status}
            
            if status == TaskStatus.RUNNING:
                update_data["started_at"] = func.now()
            elif status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]:
                update_data["completed_at"] = func.now()
            
            if worker_name:
                update_data["worker_name"] = worker_name
            if result_data:
                update_data["result_data"] = result_data
            if error_message:
                update_data["error_message"] = error_message
            
            query = update(self.model).where(
                self.model.task_id == task_id
            ).values(**update_data).returning(self.model)
            
            result = await self.session.execute(query)
            await self.session.commit()
            return result.scalar_one_or_none()
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error updating task status for {task_id}: {str(e)}")
            raise

    async def get_task_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get task execution statistics for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dictionary with task statistics
        """
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Get task counts by status
            status_query = select(
                self.model.status,
                func.count(self.model.id).label('count')
            ).where(
                self.model.queued_at >= since
            ).group_by(self.model.status)
            
            status_result = await self.session.execute(status_query)
            status_counts = {row.status: row.count for row in status_result}
            
            # Get average execution time
            exec_time_query = select(
                func.avg(self.model.execution_time_seconds).label('avg_execution_time')
            ).where(
                and_(
                    self.model.completed_at >= since,
                    self.model.execution_time_seconds.isnot(None)
                )
            )
            
            exec_time_result = await self.session.execute(exec_time_query)
            avg_execution_time = exec_time_result.scalar() or 0.0
            
            return {
                "period_hours": hours,
                "status_counts": status_counts,
                "avg_execution_time_seconds": float(avg_execution_time),
                "total_tasks": sum(status_counts.values()),
                "success_rate": (status_counts.get(TaskStatus.SUCCESS, 0) / max(sum(status_counts.values()), 1)) * 100
            }
        except Exception as e:
            logger.error(f"Error getting task statistics: {str(e)}")
            raise


class PerformanceMonitoringRepository(BaseRepository[PerformanceMonitoring, PerformanceMonitoringCreate, None]):
    """Repository for performance monitoring data."""

    def __init__(self, session: AsyncSession):
        super().__init__(PerformanceMonitoring, session)

    async def get_slow_queries(self, threshold_ms: float = 1000.0, limit: int = 50) -> List[PerformanceMonitoring]:
        """
        Get slow queries above the specified threshold.
        
        Args:
            threshold_ms: Execution time threshold in milliseconds
            limit: Maximum number of queries to return
            
        Returns:
            List of slow queries
        """
        try:
            query = select(self.model).where(
                self.model.execution_time_ms >= threshold_ms
            ).order_by(desc(self.model.execution_time_ms)).limit(limit)
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting slow queries: {str(e)}")
            raise

    async def get_query_performance_stats(self, query_type: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance statistics for a specific query type.
        
        Args:
            query_type: Type of query to analyze
            hours: Number of hours to look back
            
        Returns:
            Dictionary with performance statistics
        """
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            query = select(
                func.count(self.model.id).label('total_queries'),
                func.avg(self.model.execution_time_ms).label('avg_execution_time'),
                func.max(self.model.execution_time_ms).label('max_execution_time'),
                func.min(self.model.execution_time_ms).label('min_execution_time'),
                func.avg(self.model.rows_examined).label('avg_rows_examined'),
                func.avg(self.model.rows_returned).label('avg_rows_returned')
            ).where(
                and_(
                    self.model.query_type == query_type,
                    self.model.executed_at >= since
                )
            )
            
            result = await self.session.execute(query)
            row = result.first()
            
            if not row or row.total_queries == 0:
                return {
                    "query_type": query_type,
                    "period_hours": hours,
                    "total_queries": 0,
                    "avg_execution_time_ms": 0.0,
                    "max_execution_time_ms": 0.0,
                    "min_execution_time_ms": 0.0,
                    "avg_rows_examined": 0.0,
                    "avg_rows_returned": 0.0,
                    "efficiency_ratio": 0.0
                }
            
            efficiency_ratio = (row.avg_rows_returned / max(row.avg_rows_examined, 1)) * 100
            
            return {
                "query_type": query_type,
                "period_hours": hours,
                "total_queries": row.total_queries,
                "avg_execution_time_ms": float(row.avg_execution_time),
                "max_execution_time_ms": float(row.max_execution_time),
                "min_execution_time_ms": float(row.min_execution_time),
                "avg_rows_examined": float(row.avg_rows_examined),
                "avg_rows_returned": float(row.avg_rows_returned),
                "efficiency_ratio": float(efficiency_ratio)
            }
        except Exception as e:
            logger.error(f"Error getting query performance stats for {query_type}: {str(e)}")
            raise

    async def record_query_performance(self, performance_data: PerformanceMonitoringCreate) -> PerformanceMonitoring:
        """
        Record query performance data.
        
        Args:
            performance_data: Performance monitoring data
            
        Returns:
            Created performance monitoring record
        """
        try:
            # Determine if this is a slow query
            is_slow = performance_data.execution_time_ms >= 1000.0  # 1 second threshold
            
            db_obj = self.model(
                **performance_data.model_dump(),
                is_slow_query=is_slow
            )
            
            self.session.add(db_obj)
            await self.session.commit()
            await self.session.refresh(db_obj)
            return db_obj
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error recording query performance: {str(e)}")
            raise


class CacheMetricsRepository(BaseRepository[CacheMetrics, CacheMetricsCreate, None]):
    """Repository for cache metrics management."""

    def __init__(self, session: AsyncSession):
        super().__init__(CacheMetrics, session)

    async def get_latest_metrics(self, configuration_id: UUID) -> Optional[CacheMetrics]:
        """
        Get latest cache metrics for a configuration.
        
        Args:
            configuration_id: Cache configuration ID
            
        Returns:
            Latest cache metrics or None
        """
        try:
            query = select(self.model).where(
                self.model.configuration_id == configuration_id
            ).order_by(desc(self.model.recorded_at)).limit(1)
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting latest cache metrics: {str(e)}")
            raise

    async def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get cache metrics summary for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dictionary with cache metrics summary
        """
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            query = select(
                func.avg(self.model.cache_hit_rate).label('avg_hit_rate'),
                func.sum(self.model.cache_hits).label('total_hits'),
                func.sum(self.model.cache_misses).label('total_misses'),
                func.avg(self.model.memory_usage_mb).label('avg_memory_usage'),
                func.avg(self.model.operations_per_second).label('avg_ops_per_second')
            ).where(
                self.model.recorded_at >= since
            )
            
            result = await self.session.execute(query)
            row = result.first()
            
            if not row:
                return {
                    "period_hours": hours,
                    "avg_hit_rate": 0.0,
                    "total_operations": 0,
                    "avg_memory_usage_mb": 0.0,
                    "avg_ops_per_second": 0.0
                }
            
            total_operations = (row.total_hits or 0) + (row.total_misses or 0)
            
            return {
                "period_hours": hours,
                "avg_hit_rate": float(row.avg_hit_rate or 0.0),
                "total_operations": total_operations,
                "avg_memory_usage_mb": float(row.avg_memory_usage or 0.0),
                "avg_ops_per_second": float(row.avg_ops_per_second or 0.0)
            }
        except Exception as e:
            logger.error(f"Error getting cache metrics summary: {str(e)}")
            raise


class TaskMetricsRepository(BaseRepository[TaskMetrics, TaskMetricsCreate, None]):
    """Repository for task metrics management."""

    def __init__(self, session: AsyncSession):
        super().__init__(TaskMetrics, session)

    async def get_task_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get task performance summary for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dictionary with task performance summary
        """
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            query = select(
                func.avg(self.model.execution_time_seconds).label('avg_execution_time'),
                func.avg(self.model.queue_wait_time_seconds).label('avg_wait_time'),
                func.avg(self.model.memory_peak_mb).label('avg_memory_usage'),
                func.avg(self.model.cpu_usage_percent).label('avg_cpu_usage'),
                func.avg(self.model.success_rate_percent).label('avg_success_rate'),
                func.avg(self.model.throughput_items_per_second).label('avg_throughput')
            ).where(
                self.model.measured_at >= since
            )
            
            result = await self.session.execute(query)
            row = result.first()
            
            if not row:
                return {
                    "period_hours": hours,
                    "avg_execution_time_seconds": 0.0,
                    "avg_wait_time_seconds": 0.0,
                    "avg_memory_usage_mb": 0.0,
                    "avg_cpu_usage_percent": 0.0,
                    "avg_success_rate_percent": 100.0,
                    "avg_throughput_items_per_second": 0.0
                }
            
            return {
                "period_hours": hours,
                "avg_execution_time_seconds": float(row.avg_execution_time or 0.0),
                "avg_wait_time_seconds": float(row.avg_wait_time or 0.0),
                "avg_memory_usage_mb": float(row.avg_memory_usage or 0.0),
                "avg_cpu_usage_percent": float(row.avg_cpu_usage or 0.0),
                "avg_success_rate_percent": float(row.avg_success_rate or 100.0),
                "avg_throughput_items_per_second": float(row.avg_throughput or 0.0)
            }
        except Exception as e:
            logger.error(f"Error getting task performance summary: {str(e)}")
            raise
