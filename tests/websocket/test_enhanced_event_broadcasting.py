#!/usr/bin/env python3
"""
Enhanced Event Broadcasting Tests for Task 6.1.1 Phase 7.

This module tests the enhanced event broadcasting system implementation including:
- Event-driven message routing with type-based message handling
- Priority-based message queuing with immediate, standard, and low-priority channels
- Message acknowledgment system with delivery confirmation and retry mechanisms
- Integration with existing WebSocket connection manager from Phase 6

Performance targets: <50ms event processing, <100ms message broadcasting, >99.9% delivery reliability
"""

import pytest
import asyncio
import json
import time
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from typing import Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.enhanced_event_broadcasting_service import (
    EnhancedEventBroadcastingService,
    EventMessage,
    MessagePriority,
    EventCategory,
    SubscriptionTopic
)
from app.services.booking_realtime_integration_service import (
    BookingRealtimeIntegrationService,
    BookingEventType
)
from app.models.user import User
from app.models.booking import Booking
from app.models.booking_communication import BookingMessage


class TestEnhancedEventBroadcastingService:
    """Test suite for enhanced event broadcasting service."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock connection manager."""
        manager = AsyncMock()
        manager.user_connections = {1: {"conn_1"}, 2: {"conn_2"}}
        manager.broadcast_to_user = AsyncMock(return_value=1)
        return manager

    @pytest.fixture
    def broadcasting_service(self, mock_db_session, mock_connection_manager):
        """Create broadcasting service with mocked dependencies."""
        service = EnhancedEventBroadcastingService(mock_db_session, mock_connection_manager)
        return service

    @pytest.fixture
    def mock_user(self):
        """Create a mock user."""
        user = User()
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.role = "customer"
        return user

    @pytest.mark.asyncio
    async def test_event_broadcasting_performance(self, broadcasting_service, mock_user):
        """Test event broadcasting performance targets."""
        # Create test event message
        event_message = EventMessage(
            event_type="test_event",
            category=EventCategory.NOTIFICATION,
            priority=MessagePriority.STANDARD,
            payload={"message": "Test notification"},
            target_user_ids={1, 2}
        )
        
        # Test broadcasting performance
        start_time = time.time()
        result = await broadcasting_service.broadcast_event(event_message, mock_user)
        processing_time = (time.time() - start_time) * 1000
        
        # Verify performance target
        assert processing_time < 50  # <50ms target
        assert result["status"] == "queued"
        assert result["event_id"] == event_message.id
        assert result["priority"] == MessagePriority.STANDARD.value

    @pytest.mark.asyncio
    async def test_priority_based_message_queuing(self, broadcasting_service, mock_user):
        """Test priority-based message queuing system."""
        # Create messages with different priorities
        immediate_message = EventMessage(
            event_type="urgent_alert",
            priority=MessagePriority.IMMEDIATE,
            payload={"alert": "System maintenance"}
        )
        
        standard_message = EventMessage(
            event_type="notification",
            priority=MessagePriority.STANDARD,
            payload={"info": "General update"}
        )
        
        low_message = EventMessage(
            event_type="marketing",
            priority=MessagePriority.LOW,
            payload={"promo": "Special offer"}
        )
        
        # Queue messages
        await broadcasting_service.broadcast_event(immediate_message, mock_user)
        await broadcasting_service.broadcast_event(standard_message, mock_user)
        await broadcasting_service.broadcast_event(low_message, mock_user)
        
        # Verify queue sizes
        metrics = await broadcasting_service.get_broadcasting_metrics()
        assert metrics["queue_sizes"][MessagePriority.IMMEDIATE.value] >= 1
        assert metrics["queue_sizes"][MessagePriority.STANDARD.value] >= 1
        assert metrics["queue_sizes"][MessagePriority.LOW.value] >= 1

    @pytest.mark.asyncio
    async def test_topic_based_subscription_system(self, broadcasting_service, mock_user):
        """Test topic-based subscription for selective message delivery."""
        user_id = 1
        topic = SubscriptionTopic.BOOKING_UPDATES
        
        # Test subscription
        success = await broadcasting_service.subscribe_user_to_topic(user_id, topic, mock_user)
        assert success is True
        
        # Verify subscription
        assert user_id in broadcasting_service.user_subscriptions
        assert topic in broadcasting_service.user_subscriptions[user_id]
        assert user_id in broadcasting_service.topic_subscribers[topic]
        
        # Test unsubscription
        success = await broadcasting_service.unsubscribe_user_from_topic(user_id, topic, mock_user)
        assert success is True
        
        # Verify unsubscription
        assert topic not in broadcasting_service.user_subscriptions.get(user_id, set())
        assert user_id not in broadcasting_service.topic_subscribers[topic]

    @pytest.mark.asyncio
    async def test_message_acknowledgment_system(self, broadcasting_service, mock_user):
        """Test message acknowledgment and delivery confirmation."""
        # Create message requiring acknowledgment
        event_message = EventMessage(
            event_type="important_update",
            payload={"update": "Critical system update"},
            requires_acknowledgment=True
        )
        
        # Broadcast message
        await broadcasting_service.broadcast_event(event_message, mock_user)
        
        # Test acknowledgment
        success = await broadcasting_service.acknowledge_message(event_message.id, mock_user.id, mock_user)
        assert success is True
        
        # Verify acknowledgment tracking
        assert mock_user.id in event_message.acknowledged_by

    @pytest.mark.asyncio
    async def test_offline_message_storage(self, broadcasting_service, mock_user):
        """Test message persistence for offline users."""
        offline_user_id = 999  # User not in connection manager
        
        # Create event for offline user
        event_message = EventMessage(
            event_type="offline_notification",
            payload={"message": "You have a new message"},
            target_user_ids={offline_user_id}
        )
        
        # Store offline message
        await broadcasting_service._store_offline_message(offline_user_id, event_message)
        
        # Verify storage
        assert offline_user_id in broadcasting_service.offline_message_store
        assert len(broadcasting_service.offline_message_store[offline_user_id]) == 1
        
        # Test retrieval
        messages = await broadcasting_service.get_offline_messages(offline_user_id, mock_user)
        assert len(messages) == 1
        assert messages[0]["type"] == "offline_notification"
        
        # Verify cleanup after retrieval
        assert offline_user_id not in broadcasting_service.offline_message_store

    @pytest.mark.asyncio
    async def test_broadcasting_metrics_collection(self, broadcasting_service, mock_user):
        """Test comprehensive metrics collection and reporting."""
        # Generate some activity
        event_message = EventMessage(
            event_type="metrics_test",
            payload={"test": "data"}
        )
        
        await broadcasting_service.broadcast_event(event_message, mock_user)
        
        # Get metrics
        metrics = await broadcasting_service.get_broadcasting_metrics()
        
        # Verify metrics structure
        assert "messages_processed" in metrics
        assert "messages_delivered" in metrics
        assert "queue_sizes" in metrics
        assert "active_subscriptions" in metrics
        assert "total_subscribers" in metrics
        assert "offline_message_count" in metrics
        assert "background_tasks" in metrics
        
        # Verify metrics values
        assert metrics["messages_processed"] >= 1
        assert isinstance(metrics["queue_sizes"], dict)
        assert len(metrics["queue_sizes"]) == 4  # Four priority levels


class TestBookingRealtimeIntegrationService:
    """Test suite for booking real-time integration service."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def mock_broadcasting_service(self):
        """Create a mock broadcasting service."""
        service = AsyncMock()
        service.broadcast_event = AsyncMock(return_value={"status": "queued", "event_id": "test_id"})
        return service

    @pytest.fixture
    def integration_service(self, mock_db_session, mock_broadcasting_service):
        """Create integration service with mocked dependencies."""
        service = BookingRealtimeIntegrationService(mock_db_session, mock_broadcasting_service)
        return service

    @pytest.fixture
    def mock_booking(self):
        """Create a mock booking."""
        booking = Booking()
        booking.id = 1
        booking.user_id = 1  # Customer
        booking.vendor_id = 2  # Vendor
        booking.booking_reference = "BK001"
        booking.service_name = "Test Service"
        return booking

    @pytest.fixture
    def mock_user(self):
        """Create a mock user."""
        user = User()
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        return user

    @pytest.mark.asyncio
    async def test_booking_status_update_broadcasting(self, integration_service, mock_user):
        """Test booking status update broadcasting to participants."""
        booking_id = 1
        status = "confirmed"
        event_data = {"confirmation_time": datetime.now(timezone.utc).isoformat()}
        
        with patch.object(integration_service, '_get_booking_details') as mock_get_booking:
            mock_booking = MagicMock()
            mock_booking.id = booking_id
            mock_booking.user_id = 1
            mock_booking.vendor_id = 2
            mock_booking.booking_reference = "BK001"
            mock_get_booking.return_value = mock_booking
            
            # Test broadcasting
            start_time = time.time()
            result = await integration_service.broadcast_booking_status_update(
                booking_id, status, event_data, mock_user
            )
            processing_time = (time.time() - start_time) * 1000
            
            # Verify performance
            assert processing_time < 50  # <50ms target
            assert result["status"] == "queued"
            assert result["booking_id"] == booking_id
            assert result["status"] == status

    @pytest.mark.asyncio
    async def test_vendor_response_broadcasting(self, integration_service, mock_user):
        """Test vendor response broadcasting with high priority."""
        booking_id = 1
        vendor_id = 2
        response_type = "quote_provided"
        response_data = {"quote_amount": 150.00, "estimated_duration": "2 hours"}
        
        with patch.object(integration_service, '_get_booking_details') as mock_get_booking:
            mock_booking = MagicMock()
            mock_booking.id = booking_id
            mock_booking.user_id = 1
            mock_booking.vendor_id = vendor_id
            mock_booking.booking_reference = "BK001"
            mock_get_booking.return_value = mock_booking
            
            # Test broadcasting
            start_time = time.time()
            result = await integration_service.broadcast_vendor_response(
                booking_id, vendor_id, response_type, response_data, mock_user
            )
            processing_time = (time.time() - start_time) * 1000
            
            # Verify performance
            assert processing_time < 25  # <25ms target for high priority
            assert result["status"] == "queued"
            assert result["vendor_id"] == vendor_id
            assert result["customer_notified"] is True

    @pytest.mark.asyncio
    async def test_booking_message_broadcasting(self, integration_service, mock_user):
        """Test booking conversation message broadcasting."""
        # Create mock booking message
        message = MagicMock()
        message.id = 1
        message.booking_id = 1
        message.thread_id = "thread_123"
        message.sender_id = 1
        message.recipient_id = 2
        message.content = "Hello, I have a question about the service"
        message.message_type = MagicMock()
        message.message_type.value = "text"
        message.created_at = datetime.now(timezone.utc)
        message.attachments = []
        
        with patch.object(integration_service, '_get_booking_details') as mock_get_booking:
            mock_booking = MagicMock()
            mock_booking.id = 1
            mock_booking.user_id = 1
            mock_booking.vendor_id = 2
            mock_booking.booking_reference = "BK001"
            mock_get_booking.return_value = mock_booking
            
            # Test broadcasting
            start_time = time.time()
            result = await integration_service.broadcast_booking_message(message, mock_user)
            processing_time = (time.time() - start_time) * 1000
            
            # Verify performance
            assert processing_time < 50  # <50ms target
            assert result["status"] == "queued"
            assert result["message_id"] == message.id
            assert result["recipients_notified"] == 1

    @pytest.mark.asyncio
    async def test_typing_indicator_broadcasting(self, integration_service, mock_user):
        """Test typing indicator broadcasting with immediate priority."""
        booking_id = 1
        user_id = 1
        is_typing = True
        
        with patch.object(integration_service, '_get_booking_details') as mock_get_booking:
            mock_booking = MagicMock()
            mock_booking.id = booking_id
            mock_booking.user_id = 1
            mock_booking.vendor_id = 2
            mock_get_booking.return_value = mock_booking
            
            # Test broadcasting
            start_time = time.time()
            result = await integration_service.broadcast_typing_indicator(
                booking_id, user_id, is_typing, mock_user
            )
            processing_time = (time.time() - start_time) * 1000
            
            # Verify performance
            assert processing_time < 25  # <25ms target for immediate priority
            assert result["status"] == "queued"
            assert result["user_id"] == user_id
            assert result["is_typing"] is True

    @pytest.mark.asyncio
    async def test_integration_metrics_collection(self, integration_service):
        """Test integration service metrics collection."""
        # Get metrics
        metrics = await integration_service.get_integration_metrics()
        
        # Verify metrics structure
        assert "booking_events_processed" in metrics
        assert "messages_broadcasted" in metrics
        assert "notifications_sent" in metrics
        assert "average_processing_time" in metrics
        assert "failed_broadcasts" in metrics
        assert "broadcasting_service_metrics" in metrics
        assert "integration_health" in metrics
        
        # Verify health status
        assert metrics["integration_health"] in ["healthy", "degraded"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
