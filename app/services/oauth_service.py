"""
OAuth service for Culture Connect Backend API.

This module provides comprehensive OAuth authentication services including:
- OAuth provider integration (Google, Facebook)
- OAuth flow management and CSRF protection
- Token management and refresh mechanisms
- User profile synchronization and account linking
- Security event logging and audit trails

Implements Task 2.1.3 requirements for OAuth2 integration with production-grade
security features and seamless integration with existing JWT authentication.
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from urllib.parse import urlencode
import httpx
from fastapi import HTTPException, status

from app.services.base import BaseService
from app.repositories.oauth_repository import (
    OAuthProviderRepository, OAuthAccountRepository,
    OAuthTokenRepository, OAuthStateRepository
)
from app.repositories.auth_repository import UserRepository
from app.models.oauth_models import OAuthProvider, OAuthAccount, OAuthToken, OAuthState
from app.models.user import User
from app.schemas.oauth_schemas import (
    OAuthProviderCreate, OAuthProviderUpdate, OAuthAccountCreate,
    OAuthTokenCreate, OAuthUserProfile, OAuthAuthorizationResponse,
    OAuthCallbackResponse
)
from app.core.security import create_token_pair, generate_secure_token, get_password_hash
from app.core.config import settings

logger = logging.getLogger(__name__)


class OAuthService(BaseService[OAuthProvider, OAuthProviderRepository]):
    """
    OAuth service for OAuth2 integration and authentication.

    Provides comprehensive OAuth functionality including provider management,
    authentication flows, token management, and user account linking.
    """

    def __init__(self, db):
        """Initialize OAuth service with repositories."""
        super().__init__(OAuthProviderRepository, OAuthProvider, db)
        self.provider_repo = OAuthProviderRepository(db)
        self.account_repo = OAuthAccountRepository(db)
        self.token_repo = OAuthTokenRepository(db)
        self.state_repo = OAuthStateRepository(db)
        self.user_repo = UserRepository(db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for OAuth provider creation."""
        # Basic validation - can be extended as needed
        required_fields = ['name', 'client_id', 'client_secret']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for OAuth provider update."""
        # Basic validation - can be extended as needed
        return data

    async def get_provider_by_name(self, name: str) -> Optional[OAuthProvider]:
        """
        Get OAuth provider by name.

        Args:
            name: Provider name (google, facebook, etc.)

        Returns:
            OAuthProvider instance if found, None otherwise
        """
        try:
            provider = await self.provider_repo.get_by_name(name)
            if not provider:
                logger.warning(f"OAuth provider not found: {name}")
            return provider

        except Exception as e:
            await self.handle_service_error(e, "get_provider_by_name", {"name": name})
            return None

    async def get_active_providers(self) -> List[OAuthProvider]:
        """
        Get all active OAuth providers.

        Returns:
            List of active OAuth providers
        """
        try:
            providers = await self.provider_repo.get_active_providers()
            logger.debug(f"Retrieved {len(providers)} active OAuth providers")
            return providers

        except Exception as e:
            await self.handle_service_error(e, "get_active_providers", {})
            return []

    async def create_authorization_url(
        self,
        provider_name: str,
        redirect_uri: str,
        scopes: Optional[List[str]] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> OAuthAuthorizationResponse:
        """
        Create OAuth authorization URL for user authentication.

        Args:
            provider_name: OAuth provider name
            redirect_uri: Redirect URI after authorization
            scopes: Requested OAuth scopes
            client_ip: Client IP address
            user_agent: Client user agent

        Returns:
            OAuthAuthorizationResponse with authorization URL and state

        Raises:
            HTTPException: If provider not found or configuration invalid
        """
        try:
            # Get OAuth provider
            provider = await self.get_provider_by_name(provider_name)
            if not provider:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"OAuth provider '{provider_name}' not found or inactive"
                )

            # Create OAuth state for CSRF protection
            state = await self.state_repo.create_state(
                provider_id=provider.id,
                redirect_uri=redirect_uri,
                scopes=scopes or provider.scopes,
                client_ip=client_ip,
                user_agent=user_agent,
                expires_in_minutes=10
            )

            # Build authorization URL
            auth_params = {
                'client_id': provider.client_id,
                'redirect_uri': redirect_uri,
                'scope': ' '.join(scopes or provider.scopes),
                'state': state.state_token,
                'response_type': 'code',
                'access_type': 'offline',  # For refresh tokens
                'prompt': 'consent'  # Force consent to get refresh token
            }

            authorization_url = f"{provider.authorization_url}?{urlencode(auth_params)}"

            logger.info(f"OAuth authorization URL created for provider {provider_name}")

            return OAuthAuthorizationResponse(
                authorization_url=authorization_url,
                state=state.state_token,
                expires_in=600  # 10 minutes
            )

        except HTTPException:
            raise
        except Exception as e:
            await self.handle_service_error(e, "create_authorization_url", {
                "provider_name": provider_name,
                "redirect_uri": redirect_uri
            })

    async def handle_oauth_callback(
        self,
        code: str,
        state: str,
        redirect_uri: str,
        client_ip: Optional[str] = None
    ) -> OAuthCallbackResponse:
        """
        Handle OAuth callback and complete authentication flow.

        Args:
            code: OAuth authorization code
            state: OAuth state token for CSRF protection
            redirect_uri: Original redirect URI
            client_ip: Client IP address

        Returns:
            OAuthCallbackResponse with tokens and user information

        Raises:
            HTTPException: If callback processing fails
        """
        try:
            # Validate OAuth state
            oauth_state = await self.state_repo.get_valid_state(state)
            if not oauth_state:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired OAuth state"
                )

            # Mark state as used
            await self.state_repo.mark_state_used(oauth_state.id)

            # Get provider
            provider = oauth_state.provider
            if not provider or not provider.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="OAuth provider is not active"
                )

            # Exchange authorization code for tokens
            oauth_tokens = await self._exchange_code_for_tokens(
                provider, code, redirect_uri
            )

            # Get user profile from provider
            user_profile = await self._get_user_profile(
                provider, oauth_tokens['access_token']
            )

            # Find or create user account
            user, oauth_account, is_new_user = await self._find_or_create_user(
                provider, user_profile, client_ip
            )

            # Store OAuth tokens
            await self._store_oauth_tokens(
                oauth_account, oauth_tokens
            )

            # Update last connected timestamp
            await self.account_repo.update_last_connected(oauth_account.id)

            # Generate JWT tokens for the application
            token_data = {
                "sub": str(user.id),
                "email": user.email,
                "role": user.role,
                "scopes": self._get_user_scopes(user.role)
            }

            jwt_tokens = create_token_pair(token_data)

            logger.info(f"OAuth callback completed for user {user.id}, provider {provider.name}")

            return OAuthCallbackResponse(
                access_token=jwt_tokens.access_token,
                refresh_token=jwt_tokens.refresh_token,
                token_type=jwt_tokens.token_type,
                expires_in=jwt_tokens.expires_in,
                user=user.to_dict(),
                oauth_account={
                    "id": oauth_account.id,
                    "provider_name": provider.name,
                    "provider_user_id": oauth_account.provider_user_id,
                    "provider_email": oauth_account.provider_email
                },
                is_new_user=is_new_user
            )

        except HTTPException:
            raise
        except Exception as e:
            await self.handle_service_error(e, "handle_oauth_callback", {
                "state": state,
                "redirect_uri": redirect_uri
            })

    async def _exchange_code_for_tokens(
        self,
        provider: OAuthProvider,
        code: str,
        redirect_uri: str
    ) -> Dict[str, Any]:
        """
        Exchange authorization code for OAuth tokens.

        Args:
            provider: OAuth provider
            code: Authorization code
            redirect_uri: Redirect URI

        Returns:
            Dict containing OAuth tokens

        Raises:
            HTTPException: If token exchange fails
        """
        try:
            token_data = {
                'client_id': provider.client_id,
                'client_secret': provider.client_secret,
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': redirect_uri
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    provider.token_url,
                    data=token_data,
                    headers={'Accept': 'application/json'}
                )

                if response.status_code != 200:
                    logger.error(f"OAuth token exchange failed: {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Failed to exchange authorization code for tokens"
                    )

                tokens = response.json()

                if 'error' in tokens:
                    logger.error(f"OAuth token error: {tokens}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"OAuth error: {tokens.get('error_description', tokens['error'])}"
                    )

                return tokens

        except httpx.RequestError as e:
            logger.error(f"OAuth token exchange request failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OAuth provider temporarily unavailable"
            )

    async def _get_user_profile(
        self,
        provider: OAuthProvider,
        access_token: str
    ) -> OAuthUserProfile:
        """
        Get user profile from OAuth provider.

        Args:
            provider: OAuth provider
            access_token: OAuth access token

        Returns:
            OAuthUserProfile with user information

        Raises:
            HTTPException: If profile retrieval fails
        """
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json'
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    provider.user_info_url,
                    headers=headers
                )

                if response.status_code != 200:
                    logger.error(f"OAuth profile retrieval failed: {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Failed to retrieve user profile from OAuth provider"
                    )

                profile_data = response.json()

                # Normalize profile data based on provider
                normalized_profile = self._normalize_profile_data(provider.name, profile_data)

                return OAuthUserProfile(**normalized_profile)

        except httpx.RequestError as e:
            logger.error(f"OAuth profile request failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OAuth provider temporarily unavailable"
            )

    def _normalize_profile_data(self, provider_name: str, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize profile data from different OAuth providers.

        Args:
            provider_name: OAuth provider name
            profile_data: Raw profile data from provider

        Returns:
            Normalized profile data
        """
        normalized = {
            'id': str(profile_data.get('id', '')),
            'provider_data': profile_data
        }

        if provider_name == 'google':
            normalized.update({
                'email': profile_data.get('email'),
                'name': profile_data.get('name'),
                'first_name': profile_data.get('given_name'),
                'last_name': profile_data.get('family_name'),
                'picture': profile_data.get('picture'),
                'verified_email': profile_data.get('email_verified', False),
                'locale': profile_data.get('locale')
            })
        elif provider_name == 'facebook':
            normalized.update({
                'email': profile_data.get('email'),
                'name': profile_data.get('name'),
                'first_name': profile_data.get('first_name'),
                'last_name': profile_data.get('last_name'),
                'picture': profile_data.get('picture', {}).get('data', {}).get('url'),
                'locale': profile_data.get('locale')
            })

        return normalized

    def _get_user_scopes(self, role: str) -> List[str]:
        """
        Get user scopes based on role.

        Args:
            role: User role

        Returns:
            List of user scopes
        """
        # This should match the scopes from the existing auth service
        base_scopes = ["read:profile", "update:profile"]

        if role == "admin":
            base_scopes.extend(["admin:users", "admin:system"])
        elif role == "vendor":
            base_scopes.extend(["manage:services", "view:analytics"])

        return base_scopes

    async def _find_or_create_user(
        self,
        provider: OAuthProvider,
        user_profile: OAuthUserProfile,
        client_ip: Optional[str] = None
    ) -> Tuple[User, OAuthAccount, bool]:
        """
        Find existing user or create new user from OAuth profile.

        Args:
            provider: OAuth provider
            user_profile: User profile from OAuth provider
            client_ip: Client IP address

        Returns:
            Tuple of (User, OAuthAccount, is_new_user)
        """
        try:
            # First, try to find existing OAuth account
            oauth_account = await self.account_repo.get_by_provider_user_id(
                provider.id, user_profile.id
            )

            if oauth_account:
                # Update profile data
                oauth_account.profile_data = user_profile.provider_data
                oauth_account.provider_email = user_profile.email
                oauth_account.provider_username = user_profile.username
                await self.db.commit()

                return oauth_account.user, oauth_account, False

            # Try to find user by email if provided
            user = None
            if user_profile.email:
                user = await self.user_repo.get_by_email(user_profile.email)

            # Create new user if not found
            if not user:
                user = await self._create_user_from_profile(user_profile, client_ip)
                is_new_user = True
            else:
                is_new_user = False

            # Create OAuth account
            oauth_account = OAuthAccount(
                user_id=user.id,
                provider_id=provider.id,
                provider_user_id=user_profile.id,
                provider_username=user_profile.username,
                provider_email=user_profile.email,
                profile_data=user_profile.provider_data,
                is_active=True,
                is_verified=user_profile.verified_email or False,
                first_connected_at=datetime.utcnow(),
                last_connected_at=datetime.utcnow()
            )

            self.db.add(oauth_account)
            await self.db.commit()
            await self.db.refresh(oauth_account)

            logger.info(f"OAuth account created for user {user.id}, provider {provider.name}")

            return user, oauth_account, is_new_user

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to find or create user from OAuth profile: {str(e)}")
            raise

    async def _create_user_from_profile(
        self,
        user_profile: OAuthUserProfile,
        client_ip: Optional[str] = None
    ) -> User:
        """
        Create new user from OAuth profile.

        Args:
            user_profile: User profile from OAuth provider
            client_ip: Client IP address

        Returns:
            Created User instance
        """
        try:
            # Generate a secure random password for OAuth users
            random_password = generate_secure_token(32)
            hashed_password = get_password_hash(random_password, validate_strength=False)

            # Extract names from profile
            first_name = user_profile.first_name or "Unknown"
            last_name = user_profile.last_name or "User"

            # If no separate first/last names, try to split the full name
            if user_profile.name and (first_name == "Unknown" or last_name == "User"):
                name_parts = user_profile.name.split(' ', 1)
                first_name = name_parts[0]
                last_name = name_parts[1] if len(name_parts) > 1 else "User"

            user = User(
                email=user_profile.email or f"oauth_{user_profile.id}@example.com",
                hashed_password=hashed_password,
                first_name=first_name,
                last_name=last_name,
                role="customer",  # Default role for OAuth users
                is_active=True,
                is_verified=user_profile.verified_email or False,
                last_login=datetime.utcnow()
            )

            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)

            logger.info(f"New user created from OAuth profile: {user.email}")

            return user

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create user from OAuth profile: {str(e)}")
            raise

    async def _store_oauth_tokens(
        self,
        oauth_account: OAuthAccount,
        oauth_tokens: Dict[str, Any]
    ) -> OAuthToken:
        """
        Store OAuth tokens securely.

        Args:
            oauth_account: OAuth account
            oauth_tokens: OAuth tokens from provider

        Returns:
            Created OAuthToken instance
        """
        try:
            # Revoke existing active tokens
            existing_token = await self.token_repo.get_active_token(oauth_account.id)
            if existing_token:
                await self.token_repo.revoke_token(existing_token.id)

            # Calculate expiration
            expires_in = oauth_tokens.get('expires_in', 3600)
            expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

            # Create new token
            token = OAuthToken(
                oauth_account_id=oauth_account.id,
                access_token=oauth_tokens['access_token'],  # Will be encrypted by model
                refresh_token=oauth_tokens.get('refresh_token'),  # Will be encrypted by model
                token_type=oauth_tokens.get('token_type', 'Bearer'),
                expires_at=expires_at,
                scopes=oauth_tokens.get('scope', '').split() if oauth_tokens.get('scope') else None,
                is_active=True,
                is_revoked=False
            )

            self.db.add(token)
            await self.db.commit()
            await self.db.refresh(token)

            logger.debug(f"OAuth tokens stored for account {oauth_account.id}")

            return token

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to store OAuth tokens: {str(e)}")
            raise

    async def get_user_oauth_accounts(self, user_id: int) -> List[OAuthAccount]:
        """
        Get all OAuth accounts for a user.

        Args:
            user_id: User ID

        Returns:
            List of OAuth accounts
        """
        try:
            accounts = await self.account_repo.get_by_user_id(user_id)
            logger.debug(f"Retrieved {len(accounts)} OAuth accounts for user {user_id}")
            return accounts

        except Exception as e:
            await self.handle_service_error(e, "get_user_oauth_accounts", {"user_id": user_id})
            return []

    async def unlink_oauth_account(self, user_id: int, oauth_account_id: int) -> bool:
        """
        Unlink OAuth account from user.

        Args:
            user_id: User ID
            oauth_account_id: OAuth account ID to unlink

        Returns:
            bool: True if unlinking successful

        Raises:
            HTTPException: If account not found or not owned by user
        """
        try:
            # Get OAuth account
            oauth_account = await self.account_repo.get(oauth_account_id)
            if not oauth_account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="OAuth account not found"
                )

            # Verify ownership
            if oauth_account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="OAuth account does not belong to user"
                )

            # Deactivate account and revoke tokens
            success = await self.account_repo.deactivate_account(oauth_account_id)

            if success:
                # Revoke active tokens
                active_token = await self.token_repo.get_active_token(oauth_account_id)
                if active_token:
                    await self.token_repo.revoke_token(active_token.id)

                logger.info(f"OAuth account {oauth_account_id} unlinked from user {user_id}")

            return success

        except HTTPException:
            raise
        except Exception as e:
            await self.handle_service_error(e, "unlink_oauth_account", {
                "user_id": user_id,
                "oauth_account_id": oauth_account_id
            })
            return False

    async def cleanup_expired_data(self) -> Dict[str, int]:
        """
        Clean up expired OAuth data.

        Returns:
            Dict with cleanup counts
        """
        try:
            tokens_cleaned = await self.token_repo.cleanup_expired_tokens()
            states_cleaned = await self.state_repo.cleanup_expired_states()

            logger.info(f"OAuth cleanup completed: {tokens_cleaned} tokens, {states_cleaned} states")

            return {
                "expired_tokens": tokens_cleaned,
                "expired_states": states_cleaned
            }

        except Exception as e:
            await self.handle_service_error(e, "cleanup_expired_data", {})
            return {"expired_tokens": 0, "expired_states": 0}
