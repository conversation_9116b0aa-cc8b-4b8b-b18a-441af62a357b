"""
VPN and Proxy Detection Models for Culture Connect Backend API.

This module provides comprehensive data models for VPN, proxy, and anonymization detection including:
- VPN detection results with confidence scoring
- Proxy detection with type classification
- Anonymization service detection
- Detection history and analytics
- Integration with existing User and Payment models

Implements Phase 2.1 VPN Detection Models foundation following established Phase 1 patterns
with PostgreSQL table definitions, proper relationships, and audit logging.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

from app.db.base import BaseModelWithAudit


class VPNDetectionMethod(str, Enum):
    """VPN detection method enumeration."""
    IP_RANGE_ANALYSIS = "ip_range_analysis"
    DNS_LEAK_TEST = "dns_leak_test"
    GEOLOCATION_MISMATCH = "geolocation_mismatch"
    PROVIDER_DATABASE = "provider_database"
    BEHAVIORAL_ANALYSIS = "behavioral_analysis"
    MACHINE_LEARNING = "machine_learning"
    THIRD_PARTY_API = "third_party_api"
    COMPOSITE_ANALYSIS = "composite_analysis"


class ProxyType(str, Enum):
    """Proxy type enumeration."""
    HTTP_PROXY = "http_proxy"
    HTTPS_PROXY = "https_proxy"
    SOCKS4_PROXY = "socks4_proxy"
    SOCKS5_PROXY = "socks5_proxy"
    TRANSPARENT_PROXY = "transparent_proxy"
    ANONYMOUS_PROXY = "anonymous_proxy"
    ELITE_PROXY = "elite_proxy"
    RESIDENTIAL_PROXY = "residential_proxy"
    DATACENTER_PROXY = "datacenter_proxy"
    MOBILE_PROXY = "mobile_proxy"


class AnonymizationService(str, Enum):
    """Anonymization service enumeration."""
    TOR_NETWORK = "tor_network"
    VPN_SERVICE = "vpn_service"
    PROXY_SERVICE = "proxy_service"
    CLOUD_HOSTING = "cloud_hosting"
    CDN_SERVICE = "cdn_service"
    HOSTING_PROVIDER = "hosting_provider"
    UNKNOWN_SERVICE = "unknown_service"


class DetectionConfidence(str, Enum):
    """Detection confidence level enumeration."""
    VERY_LOW = "very_low"      # 0.0 - 0.2
    LOW = "low"                # 0.2 - 0.4
    MEDIUM = "medium"          # 0.4 - 0.6
    HIGH = "high"              # 0.6 - 0.8
    VERY_HIGH = "very_high"    # 0.8 - 1.0


class VPNDetectionResult(BaseModelWithAudit):
    """
    VPN detection result model with comprehensive detection metadata.

    Stores VPN detection results with confidence scoring, detection methods,
    and integration with payment routing decisions.
    """
    __tablename__ = "vpn_detection_results"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    ip_address = Column(String(45), nullable=False, index=True)  # IPv4/IPv6 support

    # Detection results
    is_vpn_detected = Column(Boolean, nullable=False, default=False, index=True)
    confidence_score = Column(Float, nullable=False, default=0.0, index=True)
    confidence_level = Column(String(20), nullable=False, default=DetectionConfidence.VERY_LOW)
    detection_method = Column(String(50), nullable=False, default=VPNDetectionMethod.IP_RANGE_ANALYSIS)

    # VPN service details
    vpn_provider = Column(String(100), nullable=True)
    vpn_service_type = Column(String(50), nullable=True)
    vpn_exit_country = Column(String(2), nullable=True)  # ISO country code
    vpn_datacenter_name = Column(String(100), nullable=True)

    # Detection metadata
    detection_timestamp = Column(DateTime(timezone=True), nullable=False, default=func.now())
    detection_duration_ms = Column(Float, nullable=True)
    detection_source = Column(String(50), nullable=False, default="internal")

    # Risk assessment
    risk_score = Column(Float, nullable=False, default=0.0)
    risk_factors = Column(JSONB, nullable=True)  # JSON array of risk factors

    # Geolocation correlation
    reported_country = Column(String(2), nullable=True)  # Country from geolocation
    actual_country = Column(String(2), nullable=True)    # Country from VPN analysis
    country_mismatch = Column(Boolean, nullable=False, default=False)

    # Performance metrics
    cache_hit = Column(Boolean, nullable=False, default=False)
    fallback_used = Column(Boolean, nullable=False, default=False)

    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    user = relationship("User", back_populates="vpn_detections", foreign_keys=[user_id])

    # Additional metadata
    user_agent = Column(Text, nullable=True)
    request_headers = Column(JSONB, nullable=True)
    additional_metadata = Column(JSONB, nullable=True)

    # Indexes for performance
    __table_args__ = (
        Index('idx_vpn_ip_timestamp', 'ip_address', 'detection_timestamp'),
        Index('idx_vpn_confidence_detected', 'confidence_score', 'is_vpn_detected'),
        Index('idx_vpn_user_timestamp', 'user_id', 'detection_timestamp'),
        Index('idx_vpn_country_mismatch', 'country_mismatch', 'detection_timestamp'),
        Index('idx_vpn_risk_score', 'risk_score', 'detection_timestamp'),
    )


class ProxyDetectionResult(BaseModelWithAudit):
    """
    Proxy detection result model with type classification and performance metrics.

    Stores proxy detection results with detailed type classification,
    anonymity levels, and performance characteristics.
    """
    __tablename__ = "proxy_detection_results"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    ip_address = Column(String(45), nullable=False, index=True)

    # Detection results
    is_proxy_detected = Column(Boolean, nullable=False, default=False, index=True)
    proxy_type = Column(String(50), nullable=True)
    confidence_score = Column(Float, nullable=False, default=0.0, index=True)
    confidence_level = Column(String(20), nullable=False, default=DetectionConfidence.VERY_LOW)

    # Proxy characteristics
    anonymity_level = Column(String(20), nullable=True)  # transparent, anonymous, elite
    proxy_port = Column(Integer, nullable=True)
    proxy_protocol = Column(String(20), nullable=True)

    # Provider information
    proxy_provider = Column(String(100), nullable=True)
    hosting_provider = Column(String(100), nullable=True)
    datacenter_name = Column(String(100), nullable=True)

    # Geographic information
    proxy_country = Column(String(2), nullable=True)
    proxy_region = Column(String(100), nullable=True)
    proxy_city = Column(String(100), nullable=True)

    # Detection metadata
    detection_timestamp = Column(DateTime(timezone=True), nullable=False, default=func.now())
    detection_method = Column(String(50), nullable=False)
    detection_duration_ms = Column(Float, nullable=True)

    # Performance characteristics
    response_time_ms = Column(Float, nullable=True)
    bandwidth_estimate = Column(Float, nullable=True)  # Mbps
    uptime_percentage = Column(Float, nullable=True)

    # Risk assessment
    risk_score = Column(Float, nullable=False, default=0.0)
    malicious_activity = Column(Boolean, nullable=False, default=False)

    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    user = relationship("User", back_populates="proxy_detections", foreign_keys=[user_id])

    # Additional metadata
    additional_metadata = Column(JSONB, nullable=True)

    # Indexes for performance
    __table_args__ = (
        Index('idx_proxy_ip_timestamp', 'ip_address', 'detection_timestamp'),
        Index('idx_proxy_type_confidence', 'proxy_type', 'confidence_score'),
        Index('idx_proxy_user_timestamp', 'user_id', 'detection_timestamp'),
        Index('idx_proxy_risk_malicious', 'risk_score', 'malicious_activity'),
    )


class AnonymizationDetectionResult(BaseModelWithAudit):
    """
    Anonymization service detection result model.

    Stores detection results for various anonymization services including
    Tor, VPNs, proxies, and other privacy tools.
    """
    __tablename__ = "anonymization_detection_results"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    ip_address = Column(String(45), nullable=False, index=True)

    # Detection results
    is_anonymized = Column(Boolean, nullable=False, default=False, index=True)
    anonymization_service = Column(String(50), nullable=True)
    confidence_score = Column(Float, nullable=False, default=0.0, index=True)

    # Service details
    service_provider = Column(String(100), nullable=True)
    service_type = Column(String(50), nullable=True)
    exit_node_country = Column(String(2), nullable=True)

    # Detection metadata
    detection_timestamp = Column(DateTime(timezone=True), nullable=False, default=func.now())
    detection_method = Column(String(50), nullable=False)
    detection_sources = Column(JSONB, nullable=True)  # Array of detection sources

    # Risk and privacy assessment
    privacy_level = Column(String(20), nullable=True)  # low, medium, high
    risk_score = Column(Float, nullable=False, default=0.0)
    threat_indicators = Column(JSONB, nullable=True)

    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    user = relationship("User", back_populates="anonymization_detections", foreign_keys=[user_id])

    # Additional metadata
    additional_metadata = Column(JSONB, nullable=True)

    # Indexes for performance
    __table_args__ = (
        Index('idx_anon_ip_timestamp', 'ip_address', 'detection_timestamp'),
        Index('idx_anon_service_confidence', 'anonymization_service', 'confidence_score'),
        Index('idx_anon_user_timestamp', 'user_id', 'detection_timestamp'),
    )


class VPNDetectionAnalytics(BaseModelWithAudit):
    """
    Detection analytics and aggregated metrics model.

    Stores aggregated analytics for detection performance,
    trends, and business intelligence.
    """
    __tablename__ = "vpn_detection_analytics"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Analysis date (for daily aggregations)
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True, unique=True)

    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # hourly, daily, weekly, monthly

    # Detection metrics
    total_detections = Column(Integer, nullable=False, default=0)
    vpn_detections = Column(Integer, nullable=False, default=0)
    proxy_detections = Column(Integer, nullable=False, default=0)
    anonymization_detections = Column(Integer, nullable=False, default=0)

    # Performance metrics
    avg_detection_time_ms = Column(Float, nullable=True)
    cache_hit_rate = Column(Float, nullable=True)
    fallback_rate = Column(Float, nullable=True)

    # Geographic distribution
    top_countries = Column(JSONB, nullable=True)  # Country distribution
    top_providers = Column(JSONB, nullable=True)  # Provider distribution

    # Risk metrics
    avg_risk_score = Column(Float, nullable=True)
    high_risk_detections = Column(Integer, nullable=False, default=0)

    # Business impact
    payment_routing_impacts = Column(Integer, nullable=False, default=0)
    blocked_transactions = Column(Integer, nullable=False, default=0)

    # Additional metrics
    additional_metrics = Column(JSONB, nullable=True)

    # Indexes for performance
    __table_args__ = (
        Index('idx_analytics_period', 'period_start', 'period_end', 'period_type'),
        Index('idx_analytics_detections', 'total_detections', 'period_start'),
    )
