"""Add service models for Task 3.2.1

Revision ID: 20250524_0200
Revises: 20250524_0100
Create Date: 2025-01-24 02:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250524_0200'
down_revision = '20250525_0150_push_notifications'
branch_labels = None
depends_on = None


def upgrade():
    """Create service-related tables for Task 3.2.1."""

    # Create service status enum
    service_status_enum = postgresql.ENUM(
        'draft', 'active', 'paused', 'archived',
        name='servicestatus',
        create_type=False
    )
    service_status_enum.create(op.get_bind(), checkfirst=True)

    # Create pricing type enum
    pricing_type_enum = postgresql.ENUM(
        'fixed', 'hourly', 'daily', 'package', 'custom',
        name='pricingtype',
        create_type=False
    )
    pricing_type_enum.create(op.get_bind(), checkfirst=True)

    # Create availability type enum
    availability_type_enum = postgresql.ENUM(
        'always', 'scheduled', 'on_demand',
        name='availabilitytype',
        create_type=False
    )
    availability_type_enum.create(op.get_bind(), checkfirst=True)

    # Create service_categories table
    op.create_table(
        'service_categories',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('slug', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('parent_id', sa.Integer(), nullable=True),
        sa.Column('display_order', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('icon_url', sa.String(length=500), nullable=True),
        sa.Column('image_url', sa.String(length=500), nullable=True),
        sa.Column('extra_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['parent_id'], ['service_categories.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('slug'),
        sa.UniqueConstraint('uuid')
    )

    # Create indexes for service_categories
    op.create_index('idx_service_categories_active_name', 'service_categories', ['is_active', 'name'])
    op.create_index('idx_service_categories_parent_order', 'service_categories', ['parent_id', 'display_order'])
    op.create_index(op.f('ix_service_categories_id'), 'service_categories', ['id'])
    op.create_index(op.f('ix_service_categories_name'), 'service_categories', ['name'])
    op.create_index(op.f('ix_service_categories_parent_id'), 'service_categories', ['parent_id'])
    op.create_index(op.f('ix_service_categories_slug'), 'service_categories', ['slug'])

    # Create services table
    op.create_table(
        'services',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('slug', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('short_description', sa.String(length=500), nullable=True),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('max_participants', sa.Integer(), nullable=True),
        sa.Column('min_participants', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('location', sa.String(length=500), nullable=True),
        sa.Column('includes', sa.JSON(), nullable=True),
        sa.Column('excludes', sa.JSON(), nullable=True),
        sa.Column('requirements', sa.JSON(), nullable=True),
        sa.Column('pricing_type', pricing_type_enum, nullable=False, server_default='fixed'),
        sa.Column('base_price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False, server_default='USD'),
        sa.Column('pricing_details', sa.JSON(), nullable=True),
        sa.Column('availability_type', availability_type_enum, nullable=False, server_default='scheduled'),
        sa.Column('advance_booking_hours', sa.Integer(), nullable=False, server_default='24'),
        sa.Column('cancellation_hours', sa.Integer(), nullable=False, server_default='24'),
        sa.Column('status', service_status_enum, nullable=False, server_default='draft'),
        sa.Column('is_featured', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_instant_booking', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('total_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=False, server_default='0.00'),
        sa.Column('total_reviews', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('meta_title', sa.String(length=255), nullable=True),
        sa.Column('meta_description', sa.String(length=500), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('extra_data', sa.JSON(), nullable=True),
        sa.CheckConstraint('average_rating >= 0 AND average_rating <= 5', name='ck_services_rating_range'),
        sa.CheckConstraint('base_price >= 0', name='ck_services_base_price_positive'),
        sa.CheckConstraint('max_participants >= min_participants', name='ck_services_participants_valid'),
        sa.ForeignKeyConstraint(['category_id'], ['service_categories.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('vendor_id', 'slug', name='uq_services_vendor_slug')
    )

    # Create indexes for services
    op.create_index('idx_services_category_status', 'services', ['category_id', 'status'])
    op.create_index('idx_services_featured_rating', 'services', ['is_featured', 'average_rating'])
    op.create_index('idx_services_pricing', 'services', ['pricing_type', 'base_price'])
    op.create_index('idx_services_vendor_status', 'services', ['vendor_id', 'status'])
    op.create_index(op.f('ix_services_category_id'), 'services', ['category_id'])
    op.create_index(op.f('ix_services_id'), 'services', ['id'])
    op.create_index(op.f('ix_services_slug'), 'services', ['slug'])
    op.create_index(op.f('ix_services_title'), 'services', ['title'])
    op.create_index(op.f('ix_services_vendor_id'), 'services', ['vendor_id'])

    # Create service_images table
    op.create_table(
        'service_images',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('service_id', sa.Integer(), nullable=False),
        sa.Column('url', sa.String(length=500), nullable=False),
        sa.Column('alt_text', sa.String(length=255), nullable=True),
        sa.Column('caption', sa.String(length=500), nullable=True),
        sa.Column('media_type', sa.String(length=50), nullable=False, server_default='image'),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('dimensions', sa.JSON(), nullable=True),
        sa.Column('display_order', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('is_primary', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_featured', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('extra_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )

    # Create indexes for service_images
    op.create_index('idx_service_images_primary', 'service_images', ['service_id', 'is_primary'])
    op.create_index('idx_service_images_service_order', 'service_images', ['service_id', 'display_order'])
    op.create_index(op.f('ix_service_images_id'), 'service_images', ['id'])
    op.create_index(op.f('ix_service_images_service_id'), 'service_images', ['service_id'])

    # Create service_pricing table
    op.create_table(
        'service_pricing',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('service_id', sa.Integer(), nullable=False),
        sa.Column('tier_name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False, server_default='USD'),
        sa.Column('min_participants', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('max_participants', sa.Integer(), nullable=True),
        sa.Column('valid_from', sa.Date(), nullable=True),
        sa.Column('valid_until', sa.Date(), nullable=True),
        sa.Column('conditions', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.CheckConstraint('max_participants IS NULL OR max_participants >= min_participants', name='ck_service_pricing_participants_valid'),
        sa.CheckConstraint('price >= 0', name='ck_service_pricing_price_positive'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )

    # Create indexes for service_pricing
    op.create_index('idx_service_pricing_participants', 'service_pricing', ['min_participants', 'max_participants'])
    op.create_index('idx_service_pricing_service_active', 'service_pricing', ['service_id', 'is_active'])
    op.create_index(op.f('ix_service_pricing_id'), 'service_pricing', ['id'])
    op.create_index(op.f('ix_service_pricing_service_id'), 'service_pricing', ['service_id'])

    # Create service_availability table
    op.create_table(
        'service_availability',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('uuid', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('service_id', sa.Integer(), nullable=False),
        sa.Column('available_date', sa.Date(), nullable=False),
        sa.Column('start_time', sa.Time(), nullable=True),
        sa.Column('end_time', sa.Time(), nullable=True),
        sa.Column('max_bookings', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('current_bookings', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('price_override', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('is_available', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('is_recurring', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('recurrence_pattern', sa.JSON(), nullable=True),
        sa.Column('recurrence_end_date', sa.Date(), nullable=True),
        sa.CheckConstraint('current_bookings >= 0', name='ck_service_availability_current_bookings_non_negative'),
        sa.CheckConstraint('current_bookings <= max_bookings', name='ck_service_availability_bookings_within_limit'),
        sa.CheckConstraint('end_time IS NULL OR start_time IS NULL OR end_time > start_time', name='ck_service_availability_time_valid'),
        sa.CheckConstraint('max_bookings > 0', name='ck_service_availability_max_bookings_positive'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )

    # Create indexes for service_availability
    op.create_index('idx_service_availability_available', 'service_availability', ['is_available', 'available_date'])
    op.create_index('idx_service_availability_date_time', 'service_availability', ['available_date', 'start_time'])
    op.create_index('idx_service_availability_service_date', 'service_availability', ['service_id', 'available_date'])
    op.create_index(op.f('ix_service_availability_available_date'), 'service_availability', ['available_date'])
    op.create_index(op.f('ix_service_availability_id'), 'service_availability', ['id'])
    op.create_index(op.f('ix_service_availability_service_id'), 'service_availability', ['service_id'])


def downgrade():
    """Drop service-related tables."""

    # Drop indexes for service_availability
    op.drop_index(op.f('ix_service_availability_service_id'), table_name='service_availability')
    op.drop_index(op.f('ix_service_availability_id'), table_name='service_availability')
    op.drop_index(op.f('ix_service_availability_available_date'), table_name='service_availability')
    op.drop_index('idx_service_availability_service_date', table_name='service_availability')
    op.drop_index('idx_service_availability_date_time', table_name='service_availability')
    op.drop_index('idx_service_availability_available', table_name='service_availability')

    # Drop indexes for service_pricing
    op.drop_index(op.f('ix_service_pricing_service_id'), table_name='service_pricing')
    op.drop_index(op.f('ix_service_pricing_id'), table_name='service_pricing')
    op.drop_index('idx_service_pricing_service_active', table_name='service_pricing')
    op.drop_index('idx_service_pricing_participants', table_name='service_pricing')

    # Drop indexes for service_images
    op.drop_index(op.f('ix_service_images_service_id'), table_name='service_images')
    op.drop_index(op.f('ix_service_images_id'), table_name='service_images')
    op.drop_index('idx_service_images_service_order', table_name='service_images')
    op.drop_index('idx_service_images_primary', table_name='service_images')

    # Drop indexes for services
    op.drop_index(op.f('ix_services_vendor_id'), table_name='services')
    op.drop_index(op.f('ix_services_title'), table_name='services')
    op.drop_index(op.f('ix_services_slug'), table_name='services')
    op.drop_index(op.f('ix_services_id'), table_name='services')
    op.drop_index(op.f('ix_services_category_id'), table_name='services')
    op.drop_index('idx_services_vendor_status', table_name='services')
    op.drop_index('idx_services_pricing', table_name='services')
    op.drop_index('idx_services_featured_rating', table_name='services')
    op.drop_index('idx_services_category_status', table_name='services')

    # Drop indexes for service_categories
    op.drop_index(op.f('ix_service_categories_slug'), table_name='service_categories')
    op.drop_index(op.f('ix_service_categories_parent_id'), table_name='service_categories')
    op.drop_index(op.f('ix_service_categories_name'), table_name='service_categories')
    op.drop_index(op.f('ix_service_categories_id'), table_name='service_categories')
    op.drop_index('idx_service_categories_parent_order', table_name='service_categories')
    op.drop_index('idx_service_categories_active_name', table_name='service_categories')

    # Drop tables (in reverse dependency order)
    op.drop_table('service_availability')
    op.drop_table('service_pricing')
    op.drop_table('service_images')
    op.drop_table('services')
    op.drop_table('service_categories')

    # Drop enums
    postgresql.ENUM(name='availabilitytype').drop(op.get_bind(), checkfirst=True)
    postgresql.ENUM(name='pricingtype').drop(op.get_bind(), checkfirst=True)
    postgresql.ENUM(name='servicestatus').drop(op.get_bind(), checkfirst=True)
