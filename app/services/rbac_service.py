"""
Role-Based Access Control (RBAC) service for Culture Connect Backend API.

This module provides comprehensive RBAC management services including:
- Permission checking with role hierarchies
- Role and permission management
- Access control audit logging
- Resource-level permission validation

Implements Task 2.2.3 requirements for complete role-based access control
with production-grade error handling and security features.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Set
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTP<PERSON>xception, status

from app.repositories.rbac_repository import RBACRepository
from app.repositories.auth_repository import UserRepository
from app.models.rbac_models import AccessDecision, ResourceType
from app.schemas.rbac_schemas import (
    PermissionCheck, PermissionCheckResponse, RolePermissions,
    PermissionGrantCreate, PermissionGrantResponse, PermissionGrantUpdate,
    AccessControlLogResponse, UserPermissionSummary, AccessControlStats
)
from app.core.security import UserR<PERSON>, Permission, ROLE_PERMISSIONS, has_permission
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class RBACService:
    """
    Comprehensive RBAC management service.

    Provides production-grade role-based access control operations including
    permission checking, role management, audit logging, and resource-level
    access control with proper error handling and security validation.
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.rbac_repository = RBACRepository(db)
        self.user_repository = UserRepository(db)

    async def check_permission(
        self,
        user_id: str,
        user_role: str,
        permission: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> PermissionCheckResponse:
        """
        Check if user has required permission with comprehensive audit logging.

        Args:
            user_id: User ID to check permissions for
            user_role: User's current role
            permission: Permission to check
            resource_type: Resource type for resource-specific permissions
            resource_id: Resource ID for resource-specific permissions
            endpoint: API endpoint being accessed (for logging)
            method: HTTP method (for logging)
            ip_address: Client IP address (for logging)
            user_agent: Client user agent (for logging)

        Returns:
            PermissionCheckResponse: Permission check result with audit information
        """
        start_time = datetime.utcnow()
        decision = AccessDecision.DENIED
        reason = "Permission denied"
        has_perm = False

        try:
            # Check role-based permissions first
            if has_permission(user_role, permission):
                has_perm = True
                decision = AccessDecision.GRANTED
                reason = f"Permission granted via role: {user_role}"
            else:
                # Check for specific permission grants
                grants = await self.rbac_repository.get_permission_grants(
                    user_id=user_id,
                    permission=permission,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    is_active=True,
                    include_expired=False
                )

                if grants:
                    has_perm = True
                    decision = AccessDecision.GRANTED
                    reason = "Permission granted via specific grant"
                else:
                    # Check role-based grants
                    role_grants = await self.rbac_repository.get_permission_grants(
                        role=user_role,
                        permission=permission,
                        resource_type=resource_type,
                        resource_id=resource_id,
                        is_active=True,
                        include_expired=False
                    )

                    if role_grants:
                        has_perm = True
                        decision = AccessDecision.GRANTED
                        reason = f"Permission granted via role grant: {user_role}"

            # Calculate response time
            response_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)

            # Log access control decision
            if endpoint:
                await self.rbac_repository.log_access_control_decision(
                    user_id=user_id,
                    user_role=user_role,
                    endpoint=endpoint,
                    method=method or "UNKNOWN",
                    required_permission=permission,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    decision=decision,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    correlation_id_value=correlation_id.get(''),
                    response_time_ms=response_time,
                    reason=reason
                )

            logger.info(
                f"Permission check: {user_role} -> {permission} = {decision.value}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "user_role": user_role,
                    "permission": permission,
                    "decision": decision.value,
                    "response_time_ms": response_time
                }
            )

            return PermissionCheckResponse(
                has_permission=has_perm,
                decision=decision,
                reason=reason,
                user_role=user_role,
                checked_permission=permission,
                resource_type=resource_type,
                resource_id=resource_id,
                timestamp=datetime.utcnow()
            )

        except Exception as e:
            # Log error decision
            response_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)

            if endpoint:
                await self.rbac_repository.log_access_control_decision(
                    user_id=user_id,
                    user_role=user_role,
                    endpoint=endpoint,
                    method=method or "UNKNOWN",
                    required_permission=permission,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    decision=AccessDecision.ERROR,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    correlation_id_value=correlation_id.get(''),
                    response_time_ms=response_time,
                    reason=f"Permission check error: {str(e)}"
                )

            logger.error(
                f"Permission check failed: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "permission": permission,
                    "error": str(e)
                }
            )

            return PermissionCheckResponse(
                has_permission=False,
                decision=AccessDecision.ERROR,
                reason=f"Permission check error: {str(e)}",
                user_role=user_role,
                checked_permission=permission,
                resource_type=resource_type,
                resource_id=resource_id,
                timestamp=datetime.utcnow()
            )

    async def get_role_permissions(self, role: str) -> RolePermissions:
        """
        Get comprehensive permissions for a role including inherited permissions.

        Args:
            role: Role to get permissions for

        Returns:
            RolePermissions: Role permissions with hierarchy information

        Raises:
            HTTPException: If role is invalid
        """
        try:
            # Validate role
            valid_roles = [getattr(UserRole, attr) for attr in dir(UserRole) if not attr.startswith('_')]
            if role not in valid_roles:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid role: {role}"
                )

            # Get direct permissions
            direct_permissions = ROLE_PERMISSIONS.get(role, [])

            # Get role hierarchy (for future enhancement)
            parent_roles = []
            child_roles = []
            inherited_permissions = []

            # For now, use static role hierarchy
            # This can be enhanced with database-driven hierarchy
            role_hierarchy = {
                UserRole.SUPER_ADMIN: [],
                UserRole.ADMIN: [UserRole.SUPER_ADMIN],
                UserRole.VENDOR: [UserRole.ADMIN],
                UserRole.CUSTOMER: [],
                UserRole.MODERATOR: [UserRole.ADMIN]
            }

            # Get inherited permissions from parent roles
            for parent_role in role_hierarchy.get(role, []):
                parent_permissions = ROLE_PERMISSIONS.get(parent_role, [])
                inherited_permissions.extend(parent_permissions)
                parent_roles.append(parent_role)

            # Remove duplicates and combine permissions
            all_permissions = list(set(direct_permissions + inherited_permissions))

            logger.info(
                f"Role permissions retrieved: {role}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "role": role,
                    "permission_count": len(all_permissions)
                }
            )

            return RolePermissions(
                role=role,
                permissions=direct_permissions,
                inherited_permissions=inherited_permissions,
                all_permissions=all_permissions,
                parent_roles=parent_roles,
                child_roles=child_roles
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to get role permissions: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "role": role,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve role permissions"
            )

    async def grant_permission(
        self,
        grant_data: PermissionGrantCreate,
        granted_by: str
    ) -> PermissionGrantResponse:
        """
        Grant a specific permission to a user or role.

        Args:
            grant_data: Permission grant data
            granted_by: User ID who is granting the permission

        Returns:
            PermissionGrantResponse: Created permission grant

        Raises:
            HTTPException: If grant creation fails or validation errors
        """
        try:
            # Validate that either user_id or role is provided
            if not grant_data.user_id and not grant_data.role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Either user_id or role must be provided"
                )

            if grant_data.user_id and grant_data.role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot specify both user_id and role"
                )

            # Validate user exists if user_id provided
            if grant_data.user_id:
                user = await self.user_repository.get_by_id(grant_data.user_id)
                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found"
                    )

            # Create permission grant
            grant = await self.rbac_repository.create_permission_grant(
                user_id=grant_data.user_id,
                role=grant_data.role,
                permission=grant_data.permission,
                resource_type=grant_data.resource_type,
                resource_id=grant_data.resource_id,
                granted_by=granted_by,
                expires_at=grant_data.expires_at,
                reason=grant_data.reason,
                metadata=grant_data.metadata
            )

            logger.info(
                f"Permission granted: {grant_data.permission} to {grant_data.user_id or grant_data.role}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "permission": grant_data.permission,
                    "user_id": grant_data.user_id,
                    "role": grant_data.role,
                    "granted_by": granted_by
                }
            )

            return PermissionGrantResponse(
                id=str(grant.id),
                user_id=str(grant.user_id) if grant.user_id else None,
                role=grant.role,
                permission=grant.permission,
                resource_type=grant.resource_type,
                resource_id=grant.resource_id,
                granted_by=str(grant.granted_by) if grant.granted_by else None,
                granted_at=grant.granted_at,
                expires_at=grant.expires_at,
                is_active=grant.is_active,
                reason=grant.reason,
                metadata=grant.metadata,
                created_at=grant.created_at,
                updated_at=grant.updated_at
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to grant permission: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "permission": grant_data.permission,
                    "user_id": grant_data.user_id,
                    "role": grant_data.role,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to grant permission"
            )

    async def get_user_permission_summary(self, user_id: str) -> UserPermissionSummary:
        """
        Get comprehensive permission summary for a user.

        Args:
            user_id: User ID to get permissions for

        Returns:
            UserPermissionSummary: Complete user permission information

        Raises:
            HTTPException: If user not found
        """
        try:
            # Get user
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Get role permissions
            role_permissions_data = await self.get_role_permissions(user.role)
            role_permissions = role_permissions_data.all_permissions

            # Get specific permission grants
            grants = await self.rbac_repository.get_permission_grants(
                user_id=user_id,
                is_active=True,
                include_expired=False
            )

            # Convert grants to response format
            grant_responses = []
            for grant in grants:
                grant_responses.append(PermissionGrantResponse(
                    id=str(grant.id),
                    user_id=str(grant.user_id) if grant.user_id else None,
                    role=grant.role,
                    permission=grant.permission,
                    resource_type=grant.resource_type,
                    resource_id=grant.resource_id,
                    granted_by=str(grant.granted_by) if grant.granted_by else None,
                    granted_at=grant.granted_at,
                    expires_at=grant.expires_at,
                    is_active=grant.is_active,
                    reason=grant.reason,
                    metadata=grant.metadata,
                    created_at=grant.created_at,
                    updated_at=grant.updated_at
                ))

            # Combine all permissions
            granted_permissions = [grant.permission for grant in grants]
            all_permissions = list(set(role_permissions + granted_permissions))

            # Group resource-specific permissions
            resource_permissions = {}
            for grant in grants:
                if grant.resource_type:
                    key = f"{grant.resource_type}:{grant.resource_id or '*'}"
                    if key not in resource_permissions:
                        resource_permissions[key] = []
                    resource_permissions[key].append(grant.permission)

            logger.info(
                f"User permission summary retrieved: {user.email}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "total_permissions": len(all_permissions)
                }
            )

            return UserPermissionSummary(
                user_id=str(user.id),
                user_role=user.role,
                role_permissions=role_permissions,
                granted_permissions=grant_responses,
                all_permissions=all_permissions,
                resource_permissions=resource_permissions,
                last_updated=datetime.utcnow()
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Failed to get user permission summary: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "user_id": user_id,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve user permission summary"
            )

    async def get_access_control_stats(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> AccessControlStats:
        """
        Get access control statistics for monitoring and analytics.

        Args:
            start_date: Statistics start date
            end_date: Statistics end date

        Returns:
            AccessControlStats: Access control statistics

        Raises:
            HTTPException: If statistics retrieval fails
        """
        try:
            # Get statistics from repository
            stats_data = await self.rbac_repository.get_access_control_stats(
                start_date=start_date,
                end_date=end_date
            )

            # Get recent denials for security monitoring
            recent_denials, _ = await self.rbac_repository.get_access_control_logs(
                decision=AccessDecision.DENIED,
                start_date=start_date or (datetime.utcnow() - timedelta(hours=24)),
                end_date=end_date or datetime.utcnow(),
                limit=10
            )

            # Convert to response format
            denial_responses = []
            for denial in recent_denials:
                denial_responses.append(AccessControlLogResponse(
                    id=str(denial.id),
                    user_id=str(denial.user_id) if denial.user_id else None,
                    user_role=denial.user_role,
                    endpoint=denial.endpoint,
                    method=denial.method,
                    required_permission=denial.required_permission,
                    resource_type=denial.resource_type,
                    resource_id=denial.resource_id,
                    decision=denial.decision,
                    ip_address=denial.ip_address,
                    user_agent=denial.user_agent,
                    correlation_id=denial.correlation_id,
                    request_timestamp=denial.request_timestamp,
                    response_time_ms=denial.response_time_ms,
                    reason=denial.reason,
                    metadata=denial.metadata,
                    created_at=denial.created_at,
                    updated_at=denial.updated_at
                ))

            logger.info(
                f"Access control stats retrieved",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "period_start": stats_data["period_start"],
                    "period_end": stats_data["period_end"],
                    "total_requests": stats_data["total_requests"]
                }
            )

            return AccessControlStats(
                total_requests=stats_data["total_requests"],
                granted_requests=stats_data["granted_requests"],
                denied_requests=stats_data["denied_requests"],
                error_requests=stats_data["error_requests"],
                success_rate=stats_data["success_rate"],
                avg_response_time_ms=stats_data["avg_response_time_ms"],
                top_endpoints=stats_data["top_endpoints"],
                top_permissions=[],  # Will be populated if needed
                recent_denials=denial_responses,
                period_start=stats_data["period_start"],
                period_end=stats_data["period_end"]
            )

        except Exception as e:
            logger.error(
                f"Failed to get access control stats: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "start_date": start_date,
                    "end_date": end_date,
                    "error": str(e)
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve access control statistics"
            )
