"""
Payment provider configuration management for Culture Connect Backend API.

This module provides centralized configuration for all payment providers including
Stripe (Diaspora markets), Paystack (African markets), and Busha (Cryptocurrency).

Features:
- Environment-based configuration management
- Provider-specific API endpoint configurations
- Webhook URL management and security settings
- Comprehensive TODO markers for production deployment
- Type-safe configuration classes with validation

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator, AnyHttpUrl
from decimal import Decimal

from app.core.config import settings

logger = logging.getLogger(__name__)


class PaymentProviderType(str, Enum):
    """Supported payment provider types."""
    STRIPE = "stripe"
    PAYSTACK = "paystack" 
    BUSHA = "busha"


class StripeConfig(BaseModel):
    """
    Stripe payment provider configuration for Diaspora markets.
    
    Handles international payments for users outside Africa including
    US, UK, Canada, Europe, and other diaspora markets.
    """
    
    # API Configuration
    # TODO-STRIPE-API-URL: Production Stripe API base URL
    api_base_url: str = Field(
        default="https://api.stripe.com",
        description="Stripe API base URL - TODO-STRIPE-API-URL: Update for production"
    )
    
    # TODO-STRIPE-API-VERSION: Stripe API version for production
    api_version: str = Field(
        default="2023-10-16",
        description="Stripe API version - TODO-STRIPE-API-VERSION: Verify latest version"
    )
    
    # Authentication
    secret_key: str = Field(
        default=settings.STRIPE_SECRET_KEY,
        description="Stripe secret key from environment"
    )
    
    public_key: str = Field(
        default=settings.STRIPE_PUBLIC_KEY,
        description="Stripe publishable key from environment"
    )
    
    # Webhook Configuration
    # TODO-STRIPE-WEBHOOK-URL: Production webhook endpoint URL
    webhook_endpoint_url: str = Field(
        default="/api/v1/webhooks/stripe",
        description="Stripe webhook endpoint - TODO-STRIPE-WEBHOOK-URL: Configure production URL"
    )
    
    webhook_secret: str = Field(
        default=settings.STRIPE_WEBHOOK_SECRET,
        description="Stripe webhook signing secret from environment"
    )
    
    # Payment Configuration
    supported_currencies: List[str] = Field(
        default=["USD", "EUR", "GBP", "CAD", "AUD"],
        description="Currencies supported by Stripe"
    )
    
    # TODO-STRIPE-PAYMENT-METHODS: Configure supported payment methods for production
    supported_payment_methods: List[str] = Field(
        default=["card", "bank_transfer", "apple_pay", "google_pay"],
        description="Payment methods supported - TODO-STRIPE-PAYMENT-METHODS: Verify production methods"
    )
    
    # Fee Configuration
    transaction_fee_percentage: Decimal = Field(
        default=Decimal("2.9"),
        description="Stripe transaction fee percentage"
    )
    
    fixed_fee_cents: int = Field(
        default=30,
        description="Stripe fixed fee in cents"
    )
    
    # Timeout and Retry Configuration
    request_timeout: int = Field(
        default=30,
        description="API request timeout in seconds"
    )
    
    max_retries: int = Field(
        default=3,
        description="Maximum retry attempts for failed requests"
    )
    
    # TODO-STRIPE-CONNECT-CONFIG: Stripe Connect configuration for vendor payouts
    connect_enabled: bool = Field(
        default=True,
        description="Enable Stripe Connect for vendor payouts - TODO-STRIPE-CONNECT-CONFIG"
    )


class PaystackConfig(BaseModel):
    """
    Paystack payment provider configuration for African markets.
    
    Handles payments for African countries including Nigeria, Ghana, 
    Kenya, South Africa, and other supported African markets.
    """
    
    # API Configuration
    # TODO-PAYSTACK-API-URL: Production Paystack API base URL
    api_base_url: str = Field(
        default="https://api.paystack.co",
        description="Paystack API base URL - TODO-PAYSTACK-API-URL: Verify production URL"
    )
    
    # Authentication
    secret_key: str = Field(
        default=settings.PAYSTACK_SECRET_KEY,
        description="Paystack secret key from environment"
    )
    
    public_key: str = Field(
        default=settings.PAYSTACK_PUBLIC_KEY,
        description="Paystack public key from environment"
    )
    
    # Webhook Configuration
    # TODO-PAYSTACK-WEBHOOK-URL: Production webhook endpoint URL
    webhook_endpoint_url: str = Field(
        default="/api/v1/webhooks/paystack",
        description="Paystack webhook endpoint - TODO-PAYSTACK-WEBHOOK-URL: Configure production URL"
    )
    
    webhook_secret: str = Field(
        default=settings.PAYSTACK_WEBHOOK_SECRET,
        description="Paystack webhook secret from environment"
    )
    
    # Payment Configuration
    supported_currencies: List[str] = Field(
        default=["NGN", "GHS", "KES", "ZAR"],
        description="Currencies supported by Paystack"
    )
    
    # TODO-PAYSTACK-PAYMENT-METHODS: Configure supported payment methods for production
    supported_payment_methods: List[str] = Field(
        default=["card", "bank", "ussd", "mobile_money", "bank_transfer"],
        description="Payment methods supported - TODO-PAYSTACK-PAYMENT-METHODS: Verify production methods"
    )
    
    # Fee Configuration
    transaction_fee_percentage: Decimal = Field(
        default=Decimal("1.5"),
        description="Paystack transaction fee percentage"
    )
    
    capped_fee_ngn: int = Field(
        default=2000,
        description="Paystack fee cap in NGN (₦20)"
    )
    
    # Timeout and Retry Configuration
    request_timeout: int = Field(
        default=30,
        description="API request timeout in seconds"
    )
    
    max_retries: int = Field(
        default=3,
        description="Maximum retry attempts for failed requests"
    )
    
    # TODO-PAYSTACK-SUBACCOUNT-CONFIG: Paystack subaccount configuration for vendor payouts
    subaccount_enabled: bool = Field(
        default=True,
        description="Enable Paystack subaccounts for vendor payouts - TODO-PAYSTACK-SUBACCOUNT-CONFIG"
    )


class BushaConfig(BaseModel):
    """
    Busha cryptocurrency payment provider configuration.
    
    Handles cryptocurrency payments including Bitcoin, Ethereum, USDC, USDT,
    and other supported digital currencies for global transactions.
    """
    
    # API Configuration
    # TODO-BUSHA-API-URL: Production Busha API base URL
    api_base_url: str = Field(
        default="https://api.busha.co",
        description="Busha API base URL - TODO-BUSHA-API-URL: Verify production URL"
    )
    
    # TODO-BUSHA-API-VERSION: Busha API version for production
    api_version: str = Field(
        default="v1",
        description="Busha API version - TODO-BUSHA-API-VERSION: Verify latest version"
    )
    
    # Authentication
    api_key: str = Field(
        default=settings.BUSHA_API_KEY,
        description="Busha API key from environment"
    )
    
    secret_key: str = Field(
        default=settings.BUSHA_SECRET_KEY,
        description="Busha secret key from environment"
    )
    
    # Webhook Configuration
    # TODO-BUSHA-WEBHOOK-URL: Production webhook endpoint URL
    webhook_endpoint_url: str = Field(
        default="/api/v1/webhooks/busha",
        description="Busha webhook endpoint - TODO-BUSHA-WEBHOOK-URL: Configure production URL"
    )
    
    webhook_secret: str = Field(
        default=settings.BUSHA_WEBHOOK_SECRET,
        description="Busha webhook secret from environment"
    )
    
    # Cryptocurrency Configuration
    # TODO-BUSHA-SUPPORTED-CURRENCIES: Configure supported cryptocurrencies for production
    supported_currencies: List[str] = Field(
        default=["BTC", "ETH", "USDC", "USDT", "cNGN"],
        description="Cryptocurrencies supported - TODO-BUSHA-SUPPORTED-CURRENCIES: Verify production currencies"
    )
    
    # TODO-BUSHA-NETWORK-CONFIG: Configure blockchain networks for production
    supported_networks: List[str] = Field(
        default=["bitcoin", "ethereum", "polygon"],
        description="Blockchain networks supported - TODO-BUSHA-NETWORK-CONFIG: Verify production networks"
    )
    
    # Fee Configuration
    transaction_fee_percentage: Decimal = Field(
        default=Decimal("1.0"),
        description="Busha transaction fee percentage"
    )
    
    network_fee_buffer: Decimal = Field(
        default=Decimal("0.1"),
        description="Network fee buffer percentage"
    )
    
    # Confirmation Requirements
    btc_confirmations: int = Field(
        default=3,
        description="Bitcoin confirmation requirements"
    )
    
    eth_confirmations: int = Field(
        default=12,
        description="Ethereum confirmation requirements"
    )
    
    # Timeout and Retry Configuration
    request_timeout: int = Field(
        default=45,
        description="API request timeout in seconds (longer for blockchain)"
    )
    
    max_retries: int = Field(
        default=5,
        description="Maximum retry attempts for blockchain operations"
    )
    
    # TODO-BUSHA-WALLET-CONFIG: Busha wallet configuration for crypto storage
    wallet_enabled: bool = Field(
        default=True,
        description="Enable Busha wallet integration - TODO-BUSHA-WALLET-CONFIG"
    )


class PaymentProviderConfig(BaseModel):
    """
    Centralized payment provider configuration management.
    
    Manages all payment provider configurations with environment-based
    settings and comprehensive TODO markers for production deployment.
    """
    
    # Provider Configurations
    stripe: StripeConfig = Field(default_factory=StripeConfig)
    paystack: PaystackConfig = Field(default_factory=PaystackConfig)
    busha: BushaConfig = Field(default_factory=BushaConfig)
    
    # Global Payment Configuration
    default_currency: str = Field(
        default="NGN",
        description="Default currency for the platform"
    )
    
    # TODO-PAYMENT-TIMEOUT-CONFIG: Configure payment timeout settings for production
    payment_timeout_minutes: int = Field(
        default=15,
        description="Payment session timeout - TODO-PAYMENT-TIMEOUT-CONFIG: Verify production timeout"
    )
    
    # TODO-PAYMENT-RETRY-CONFIG: Configure payment retry settings for production
    max_payment_retries: int = Field(
        default=3,
        description="Maximum payment retry attempts - TODO-PAYMENT-RETRY-CONFIG: Verify production retries"
    )
    
    # Feature Flags
    enable_stripe: bool = Field(
        default=True,
        description="Enable Stripe payment provider"
    )
    
    enable_paystack: bool = Field(
        default=True,
        description="Enable Paystack payment provider"
    )
    
    enable_busha: bool = Field(
        default=settings.ENABLE_CRYPTO_PAYMENTS,
        description="Enable Busha cryptocurrency payments"
    )
    
    # TODO-PAYMENT-LIMITS-CONFIG: Configure payment limits for production
    min_payment_amount: Decimal = Field(
        default=Decimal("100.00"),
        description="Minimum payment amount - TODO-PAYMENT-LIMITS-CONFIG: Set production limits"
    )
    
    max_payment_amount: Decimal = Field(
        default=Decimal("1000000.00"),
        description="Maximum payment amount - TODO-PAYMENT-LIMITS-CONFIG: Set production limits"
    )


class PaymentProviderSettings:
    """
    Payment provider settings manager with caching and validation.
    
    Provides centralized access to payment provider configurations
    with environment-based overrides and production-ready defaults.
    """
    
    _config: Optional[PaymentProviderConfig] = None
    
    @classmethod
    def get_config(cls) -> PaymentProviderConfig:
        """Get payment provider configuration with caching."""
        if cls._config is None:
            cls._config = PaymentProviderConfig()
            logger.info("Payment provider configuration initialized")
        return cls._config
    
    @classmethod
    def get_provider_config(cls, provider_type: PaymentProviderType) -> Dict[str, Any]:
        """Get configuration for specific payment provider."""
        config = cls.get_config()
        
        if provider_type == PaymentProviderType.STRIPE:
            return config.stripe.dict()
        elif provider_type == PaymentProviderType.PAYSTACK:
            return config.paystack.dict()
        elif provider_type == PaymentProviderType.BUSHA:
            return config.busha.dict()
        else:
            raise ValueError(f"Unsupported payment provider: {provider_type}")
    
    @classmethod
    def is_provider_enabled(cls, provider_type: PaymentProviderType) -> bool:
        """Check if payment provider is enabled."""
        config = cls.get_config()
        
        if provider_type == PaymentProviderType.STRIPE:
            return config.enable_stripe
        elif provider_type == PaymentProviderType.PAYSTACK:
            return config.enable_paystack
        elif provider_type == PaymentProviderType.BUSHA:
            return config.enable_busha
        else:
            return False


def get_payment_provider_config() -> PaymentProviderConfig:
    """
    Get payment provider configuration instance.
    
    Returns:
        PaymentProviderConfig: Configured payment provider settings
    """
    return PaymentProviderSettings.get_config()
