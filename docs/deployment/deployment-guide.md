# Culture Connect Backend - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Culture Connect Backend across three environments: Development, Staging, and Production. The deployment infrastructure supports multiple strategies including Docker Compose for development, Docker Swarm for staging, and Kubernetes for production.

## Prerequisites

### System Requirements

- **Docker**: Version 20.10+ with Docker Compose V2
- **Docker Swarm**: For staging deployments
- **Kubernetes**: Version 1.24+ for production deployments
- **Git**: For source code management
- **Bash**: For deployment scripts

### Hardware Requirements

#### Development Environment
- **CPU**: 2 cores minimum
- **Memory**: 4GB RAM minimum
- **Storage**: 20GB available space
- **Network**: Internet connection for image pulls

#### Staging Environment
- **CPU**: 4 cores minimum
- **Memory**: 8GB RAM minimum
- **Storage**: 50GB available space
- **Network**: Stable internet connection

#### Production Environment
- **CPU**: 8+ cores recommended
- **Memory**: 16GB+ RAM recommended
- **Storage**: 100GB+ available space
- **Network**: High-speed, reliable connection

## Environment Configuration

### 1. Development Environment

#### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd cultureConnectBackend

# Configure environment variables
cp environments/development/.env.development.example environments/development/.env.development
# Edit the .env.development file with your settings

# Deploy development environment
./scripts/deployment/development/deploy-development.sh
```

#### Development Services

The development environment includes:
- **API Server**: FastAPI application with hot reload
- **PostgreSQL**: Database with development optimizations
- **Redis**: Cache and message broker
- **Celery Worker**: Background task processing
- **Adminer**: Database administration interface
- **MailHog**: Email testing service

#### Access URLs

- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Adminer**: http://localhost:8080
- **MailHog**: http://localhost:8025

#### Development Commands

```bash
# Start all services
docker-compose -f docker/development/docker-compose.yml up -d

# Start with development tools
docker-compose -f docker/development/docker-compose.yml --profile dev up -d

# View logs
docker-compose -f docker/development/docker-compose.yml logs -f

# Stop services
docker-compose -f docker/development/docker-compose.yml down

# Rebuild and restart
docker-compose -f docker/development/docker-compose.yml up -d --build
```

### 2. Staging Environment

#### Deployment Process

```bash
# Configure staging environment
cp environments/staging/.env.staging.example environments/staging/.env.staging
# Edit the .env.staging file with staging-specific settings

# Deploy to staging
./scripts/deployment/staging/deploy-staging.sh
```

#### Staging Features

- **Docker Swarm**: Production-like orchestration
- **Load Balancing**: Built-in load balancing
- **Health Checks**: Comprehensive service monitoring
- **Rolling Updates**: Zero-downtime deployments
- **Scaling**: Horizontal scaling capabilities

#### Staging Commands

```bash
# View stack status
docker stack ls
docker stack services culture-connect-staging

# Scale services
docker service scale culture-connect-staging_api=3

# View service logs
docker service logs -f culture-connect-staging_api

# Remove stack
docker stack rm culture-connect-staging
```

### 3. Production Environment

#### Kubernetes Deployment

```bash
# Configure production environment
cp environments/production/.env.production.example environments/production/.env.production
# Edit with production secrets (use secret management system)

# Deploy to Kubernetes
kubectl apply -f infrastructure/kubernetes/base/
kubectl apply -f infrastructure/kubernetes/overlays/production/
```

#### Production Features

- **Kubernetes Orchestration**: Enterprise-grade container orchestration
- **Auto-scaling**: HPA for automatic scaling
- **Load Balancing**: Ingress controller with SSL termination
- **Monitoring**: Prometheus and Grafana integration
- **Backup**: Automated database and file backups
- **Security**: Network policies and RBAC

## Health Checks

### Automated Health Checks

```bash
# Run comprehensive health checks
./scripts/deployment/health-check.sh

# Environment-specific health checks
./scripts/deployment/health-check.sh --environment production --url https://api.cultureconnect.ng

# Custom timeout
./scripts/deployment/health-check.sh --timeout 60
```

### Manual Health Verification

```bash
# API Health
curl -f http://localhost:8000/health

# Database Health
curl -f http://localhost:8000/health/database

# Redis Health
curl -f http://localhost:8000/health/redis

# Celery Health
curl -f http://localhost:8000/health/celery
```

## Environment Variables

### Required Variables

All environments require these essential variables:

```bash
# Application
CC_ENVIRONMENT=development|staging|production
CC_SECRET_KEY=<secure-random-key>
CC_DATABASE_URL=<database-connection-string>
CC_REDIS_URL=<redis-connection-string>

# Payment Providers
CC_PAYSTACK_SECRET_KEY=<paystack-secret>
CC_STRIPE_SECRET_KEY=<stripe-secret>
CC_BUSHA_API_KEY=<busha-api-key>

# External Services
CC_AIML_API_KEY=<aiml-api-key>
CC_SENTRY_DSN=<sentry-dsn>
```

### Environment-Specific Variables

#### Development
- Debug mode enabled
- Verbose logging
- Mock external services
- Local file storage

#### Staging
- Production-like settings
- Reduced logging
- Real external services
- Cloud storage

#### Production
- Optimized performance
- Minimal logging
- All external services
- Secure cloud storage

## Troubleshooting

### Common Issues

#### 1. Container Startup Failures

```bash
# Check container logs
docker logs <container-name>

# Check resource usage
docker stats

# Verify environment variables
docker exec <container-name> env | grep CC_
```

#### 2. Database Connection Issues

```bash
# Test database connectivity
docker exec <api-container> python -c "from app.db.session import engine; print(engine.execute('SELECT 1').scalar())"

# Check database logs
docker logs <database-container>
```

#### 3. Redis Connection Issues

```bash
# Test Redis connectivity
docker exec <api-container> python -c "import redis; r=redis.from_url('redis://redis:6379'); print(r.ping())"

# Check Redis logs
docker logs <redis-container>
```

### Performance Issues

#### 1. Slow API Responses

```bash
# Check API performance
./scripts/deployment/health-check.sh --environment <env>

# Monitor resource usage
docker stats

# Check database performance
docker exec <db-container> psql -U <user> -d <database> -c "SELECT * FROM pg_stat_activity;"
```

#### 2. High Memory Usage

```bash
# Check memory usage by container
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Optimize container resources
# Edit docker-compose.yml or Kubernetes manifests
```

## Security Considerations

### Development Environment
- Use default credentials (documented)
- No SSL/TLS required
- Open network access for debugging

### Staging Environment
- Use staging-specific credentials
- SSL/TLS recommended
- Restricted network access

### Production Environment
- Use secure credential management
- SSL/TLS mandatory
- Network security policies
- Regular security updates

## Backup and Recovery

### Database Backups

```bash
# Manual backup
docker exec <db-container> pg_dump -U <user> <database> > backup.sql

# Automated backups (configured in production)
# See infrastructure/kubernetes/base/cronjob-backup.yaml
```

### File Backups

```bash
# Backup uploaded files
docker run --rm -v <volume-name>:/data -v $(pwd):/backup alpine tar czf /backup/files-backup.tar.gz /data
```

### Recovery Process

```bash
# Restore database
docker exec -i <db-container> psql -U <user> <database> < backup.sql

# Restore files
docker run --rm -v <volume-name>:/data -v $(pwd):/backup alpine tar xzf /backup/files-backup.tar.gz -C /
```

## Monitoring and Logging

### Log Access

```bash
# Development
docker-compose -f docker/development/docker-compose.yml logs -f

# Staging
docker service logs -f culture-connect-staging_api

# Production
kubectl logs -f deployment/culture-connect-api
```

### Monitoring Dashboards

- **Development**: Basic container stats
- **Staging**: Docker Swarm monitoring
- **Production**: Prometheus + Grafana dashboards

## Support and Maintenance

### Regular Maintenance Tasks

1. **Update Dependencies**: Monthly security updates
2. **Database Maintenance**: Weekly vacuum and analyze
3. **Log Rotation**: Automated log cleanup
4. **Backup Verification**: Weekly backup tests
5. **Security Scans**: Monthly vulnerability assessments

### Emergency Procedures

1. **Service Outage**: Follow incident response plan
2. **Data Loss**: Execute disaster recovery procedures
3. **Security Breach**: Implement security incident response
4. **Performance Degradation**: Scale resources and investigate

For additional support, refer to the project documentation or contact the development team.
