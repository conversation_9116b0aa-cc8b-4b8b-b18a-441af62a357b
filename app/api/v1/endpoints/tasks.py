"""
Task management API endpoints for Culture Connect Backend API.

This module provides comprehensive task management endpoints including:
- Task submission and execution management
- Task status monitoring and cancellation
- Queue management and administration
- System metrics and health monitoring
- Bulk task operations

Implements Task 6.2.1 requirements for task management API with
production-grade monitoring and administration capabilities.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.deps import get_current_user, get_db, require_permissions
from app.core.logging import correlation_id
from app.models.user import User
from app.schemas.task_schemas import (
    TaskSubmissionRequest, TaskSubmissionResponse, TaskStatusRequest, TaskStatusResponse,
    TaskCancellationRequest, TaskCancellationResponse, QueueStatusResponse,
    TaskScheduleRequest, TaskScheduleResponse, TaskMetricsResponse,
    SystemMetricsResponse, BulkTaskSubmissionRequest, BulkTaskSubmissionResponse
)
from app.services.task_service import TaskService

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


@router.post(
    "/submit",
    response_model=TaskSubmissionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Submit Task",
    description="Submit a task for background execution"
)
async def submit_task(
    request: TaskSubmissionRequest,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_submit"]))
) -> TaskSubmissionResponse:
    """
    Submit a task for background execution.

    Requires 'task_submit' permission.
    """
    try:
        task_service = TaskService(session)

        # Submit task
        response = await task_service.submit_task(
            request=request,
            user_id=current_user.id
        )

        logger.info(f"Task submitted via API: {request.task_name}", extra={
            "task_id": response.task_id,
            "task_name": request.task_name,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })

        return response

    except Exception as e:
        logger.error(f"Failed to submit task via API: {str(e)}", extra={
            "task_name": request.task_name,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit task: {str(e)}"
        )


@router.get(
    "/{task_id}/status",
    response_model=TaskStatusResponse,
    summary="Get Task Status",
    description="Get the status and details of a specific task"
)
async def get_task_status(
    task_id: str,
    include_result: bool = Query(False, description="Include task result in response"),
    include_traceback: bool = Query(False, description="Include error traceback if failed"),
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_read"]))
) -> TaskStatusResponse:
    """
    Get task execution status and details.

    Requires 'task_read' permission.
    """
    try:
        task_service = TaskService(session)

        # Get task status
        response = await task_service.get_task_status(
            task_id=task_id,
            include_result=include_result,
            include_traceback=include_traceback
        )

        return response

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get task status via API: {str(e)}", extra={
            "task_id": task_id,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}"
        )


@router.post(
    "/{task_id}/cancel",
    response_model=TaskCancellationResponse,
    summary="Cancel Task",
    description="Cancel a running or pending task"
)
async def cancel_task(
    task_id: str,
    terminate: bool = Query(False, description="Forcefully terminate if running"),
    reason: Optional[str] = Query(None, description="Cancellation reason"),
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_cancel"]))
) -> TaskCancellationResponse:
    """
    Cancel a running or pending task.

    Requires 'task_cancel' permission.
    """
    try:
        task_service = TaskService(session)

        # Create cancellation request
        request = TaskCancellationRequest(
            task_id=task_id,
            terminate=terminate,
            reason=reason
        )

        # Cancel task
        response = await task_service.cancel_task(
            request=request,
            user_id=current_user.id
        )

        logger.info(f"Task cancelled via API: {task_id}", extra={
            "task_id": task_id,
            "terminate": terminate,
            "reason": reason,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })

        return response

    except Exception as e:
        logger.error(f"Failed to cancel task via API: {str(e)}", extra={
            "task_id": task_id,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel task: {str(e)}"
        )


@router.post(
    "/bulk/submit",
    response_model=BulkTaskSubmissionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Submit Bulk Tasks",
    description="Submit multiple tasks in bulk"
)
async def submit_bulk_tasks(
    request: BulkTaskSubmissionRequest,
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_submit", "task_bulk"]))
) -> BulkTaskSubmissionResponse:
    """
    Submit multiple tasks in bulk.

    Requires 'task_submit' and 'task_bulk' permissions.
    """
    try:
        task_service = TaskService(session)

        # Submit bulk tasks
        response = await task_service.submit_bulk_tasks(
            request=request,
            user_id=current_user.id
        )

        logger.info(f"Bulk tasks submitted via API", extra={
            "batch_id": str(response.batch_id),
            "total_tasks": response.total_tasks,
            "successful": response.successful_submissions,
            "failed": response.failed_submissions,
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })

        return response

    except Exception as e:
        logger.error(f"Failed to submit bulk tasks via API: {str(e)}", extra={
            "task_count": len(request.tasks),
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit bulk tasks: {str(e)}"
        )


@router.get(
    "/metrics/system",
    response_model=SystemMetricsResponse,
    summary="Get System Metrics",
    description="Get system-wide task processing metrics"
)
async def get_system_metrics(
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_admin", "metrics_read"]))
) -> SystemMetricsResponse:
    """
    Get system-wide task processing metrics.

    Requires 'task_admin' and 'metrics_read' permissions.
    """
    try:
        task_service = TaskService(session)

        # Get system metrics
        response = await task_service.get_system_metrics()

        return response

    except Exception as e:
        logger.error(f"Failed to get system metrics via API: {str(e)}", extra={
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system metrics: {str(e)}"
        )


@router.get(
    "/health",
    response_model=Dict[str, Any],
    summary="Task System Health Check",
    description="Check the health of the task processing system"
)
async def health_check(
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_read"]))
) -> Dict[str, Any]:
    """
    Check the health of the task processing system.

    Requires 'task_read' permission.
    """
    try:
        from app.core.celery_config import check_celery_health

        # Get Celery health status
        health_status = check_celery_health()

        # Add timestamp
        health_status["checked_at"] = datetime.now(timezone.utc).isoformat()
        health_status["checked_by"] = str(current_user.id)

        return health_status

    except Exception as e:
        logger.error(f"Failed to check task system health: {str(e)}", extra={
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check system health: {str(e)}"
        )


@router.get(
    "/queues",
    response_model=List[QueueStatusResponse],
    summary="Get Queue Status",
    description="Get status of all task queues"
)
async def get_queue_status(
    session: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["task_admin"]))
) -> List[QueueStatusResponse]:
    """
    Get status of all task queues.

    Requires 'task_admin' permission.
    """
    try:
        from app.core.celery_config import celery_app

        # Get queue inspection data
        inspect = celery_app.control.inspect()

        # Get active, scheduled, and reserved tasks
        active_tasks = inspect.active() or {}
        scheduled_tasks = inspect.scheduled() or {}
        reserved_tasks = inspect.reserved() or {}

        # Get queue statistics
        queue_stats = []

        # Define known queues
        known_queues = [
            "critical", "high_priority", "default", "low_priority",
            "email", "notification", "report", "sync", "cleanup", "analytics"
        ]

        for queue_name in known_queues:
            # Calculate queue metrics
            active_count = sum(
                len([task for task in worker_tasks if task.get('delivery_info', {}).get('routing_key') == queue_name])
                for worker_tasks in active_tasks.values()
            )

            scheduled_count = sum(
                len([task for task in worker_tasks if task.get('delivery_info', {}).get('routing_key') == queue_name])
                for worker_tasks in scheduled_tasks.values()
            )

            reserved_count = sum(
                len([task for task in worker_tasks if task.get('delivery_info', {}).get('routing_key') == queue_name])
                for worker_tasks in reserved_tasks.values()
            )

            queue_stats.append(QueueStatusResponse(
                id=UUID('********-0000-0000-0000-********0000'),  # Placeholder
                name=queue_name,
                queue_type=queue_name if queue_name in ["email", "notification", "report", "sync", "cleanup", "analytics"] else "general",
                is_active=True,
                is_paused=False,
                max_workers=4,  # Default value
                prefetch_multiplier=1,
                task_time_limit=1800,
                total_tasks_processed=0,  # Would be retrieved from database
                total_failures=0,  # Would be retrieved from database
                average_execution_time_ms=None,
                active_tasks=active_count,
                scheduled_tasks=scheduled_count,
                reserved_tasks=reserved_count,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            ))

        return queue_stats

    except Exception as e:
        logger.error(f"Failed to get queue status via API: {str(e)}", extra={
            "user_id": str(current_user.id),
            "correlation_id": correlation_id.get()
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get queue status: {str(e)}"
        )
