"""
Unit tests for AlertingSystemService.

This module provides comprehensive unit tests for the AlertingSystemService
including alert configuration management, notification delivery, escalation logic,
circuit breaker patterns, and performance validation.

Tests cover:
- Alert configuration CRUD operations
- Alert triggering and notification delivery
- Alert acknowledgment and resolution workflows
- Escalation logic and timer management
- Circuit breaker patterns for notification services
- Performance metrics and analytics
- Error handling and validation scenarios

Implements Task 6.2.2 Phase 4.4 testing requirements with >80% coverage,
100% test success rate validation, and performance target validation.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import MagicMock, AsyncMock, patch
from uuid import uuid4, UUID

from app.services.alerting_system_service import AlertingSystemService, AlertingCircuitBreaker
from app.services.base import ServiceError, NotFoundError, ValidationError
from app.models.workflow_models import (
    WorkflowAlert, AlertTriggerEvent, AlertDeliveryRecord,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertDeliveryStatus
)
from app.schemas.workflow_schemas import (
    WorkflowAlertCreate, WorkflowAlertUpdate, WorkflowAlertResponse
)


class TestAlertingCircuitBreaker:
    """Test cases for AlertingCircuitBreaker class."""

    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization."""
        cb = AlertingCircuitBreaker("test_service")

        assert cb.service_name == "test_service"
        assert cb.failure_threshold == 5
        assert cb.recovery_timeout == 60
        assert cb.timeout == 30
        assert cb.failure_count == 0
        assert cb.last_failure_time is None
        assert cb.state == "CLOSED"

    def test_circuit_breaker_custom_parameters(self):
        """Test circuit breaker with custom parameters."""
        cb = AlertingCircuitBreaker(
            "custom_service",
            failure_threshold=3,
            recovery_timeout=30,
            timeout=15
        )

        assert cb.service_name == "custom_service"
        assert cb.failure_threshold == 3
        assert cb.recovery_timeout == 30
        assert cb.timeout == 15

    def test_circuit_breaker_closed_state(self):
        """Test circuit breaker in closed state."""
        cb = AlertingCircuitBreaker("test_service")

        assert cb.is_call_allowed() is True
        assert cb.state == "CLOSED"

    def test_circuit_breaker_failure_recording(self):
        """Test circuit breaker failure recording."""
        cb = AlertingCircuitBreaker("test_service", failure_threshold=2)

        # Record first failure
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.state == "CLOSED"
        assert cb.is_call_allowed() is True

        # Record second failure - should open circuit
        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.state == "OPEN"
        assert cb.is_call_allowed() is False

    def test_circuit_breaker_success_recording(self):
        """Test circuit breaker success recording."""
        cb = AlertingCircuitBreaker("test_service")

        # Record some failures
        cb.record_failure()
        cb.record_failure()
        assert cb.failure_count == 2

        # Record success - should reset
        cb.record_success()
        assert cb.failure_count == 0
        assert cb.state == "CLOSED"
        assert cb.last_failure_time is None

    def test_circuit_breaker_half_open_state(self):
        """Test circuit breaker half-open state."""
        cb = AlertingCircuitBreaker("test_service", failure_threshold=1, recovery_timeout=0)

        # Open circuit
        cb.record_failure()
        assert cb.state == "OPEN"
        assert cb.is_call_allowed() is False

        # Wait for recovery timeout (mocked to 0)
        import time
        time.sleep(0.1)  # Small delay to ensure time passes

        # Should transition to half-open
        assert cb.is_call_allowed() is True
        assert cb.state == "HALF_OPEN"


class TestAlertingSystemService:
    """Test cases for AlertingSystemService class."""

    @pytest.fixture
    def mock_repositories(self):
        """Create mock repositories for testing."""
        alert_repo = MagicMock()
        trigger_repo = MagicMock()
        delivery_repo = MagicMock()

        # Configure async methods
        alert_repo.create_alert = AsyncMock()
        alert_repo.update_alert = AsyncMock()
        alert_repo.delete_alert = AsyncMock()
        alert_repo.get_alert_by_id = AsyncMock()
        alert_repo.list_alerts = AsyncMock()

        trigger_repo.create_trigger_event = AsyncMock()
        trigger_repo.acknowledge_trigger_event = AsyncMock()
        trigger_repo.resolve_trigger_event = AsyncMock()
        trigger_repo.escalate_trigger_event = AsyncMock()
        trigger_repo.get_trigger_event_by_id = AsyncMock()
        trigger_repo.get_alert_statistics = AsyncMock()

        delivery_repo.create_delivery_record = AsyncMock()
        delivery_repo.update_delivery_status = AsyncMock()

        return alert_repo, trigger_repo, delivery_repo

    @pytest.fixture
    def alerting_service(self, mock_repositories):
        """Create AlertingSystemService instance for testing."""
        alert_repo, trigger_repo, delivery_repo = mock_repositories

        service = AlertingSystemService(
            repository=alert_repo,
            trigger_repo=trigger_repo,
            delivery_repo=delivery_repo,
            correlation_id="test-correlation-id"
        )

        return service

    @pytest.fixture
    def sample_alert_data(self):
        """Create sample alert data for testing."""
        return {
            "name": "Test Alert",
            "condition_type": "execution_failure",
            "condition_expression": "status == 'failed'",
            "severity": AlertSeverity.HIGH,
            "notification_channels": [AlertChannel.EMAIL.value, AlertChannel.SLACK.value],
            "escalation_delay_minutes": 30,
            "max_escalations": 3,
            "is_active": True,
            "is_muted": False
        }

    @pytest.fixture
    def sample_workflow_alert(self):
        """Create sample WorkflowAlert for testing."""
        return WorkflowAlert(
            id=uuid4(),
            workflow_definition_id=uuid4(),
            name="Test Alert",
            condition_type="execution_failure",
            condition_expression="status == 'failed'",
            condition_data={},
            severity=AlertSeverity.HIGH,
            notification_channels=[AlertChannel.EMAIL.value, AlertChannel.SLACK.value],
            notification_config={},
            escalation_delay_minutes=30,
            escalation_channels=[AlertChannel.EMAIL.value],
            max_escalations=3,
            is_active=True,
            is_muted=False,
            muted_until=None,
            alert_data={},
            alert_metadata={},
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    @pytest.fixture
    def sample_alert_trigger_event(self):
        """Create sample AlertTriggerEvent for testing."""
        return AlertTriggerEvent(
            id=uuid4(),
            alert_id=uuid4(),
            workflow_execution_id=uuid4(),
            trigger_condition="execution_failure",
            trigger_value="failed",
            threshold_value="success",
            severity=AlertSeverity.HIGH,
            alert_message="Workflow execution failed",
            alert_context={},
            triggered_at=datetime.now(timezone.utc),
            escalation_level=0,
            is_acknowledged=False,
            acknowledged_at=None,
            acknowledged_by=None,
            acknowledgment_message=None,
            is_resolved=False,
            resolved_at=None,
            resolved_by=None,
            resolution_message=None,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    @pytest.fixture
    def sample_alert_delivery_record(self):
        """Create sample AlertDeliveryRecord for testing."""
        return AlertDeliveryRecord(
            id=uuid4(),
            trigger_event_id=uuid4(),
            channel=AlertChannel.EMAIL.value,
            delivery_status=AlertDeliveryStatus.PENDING,
            attempt_count=1,
            scheduled_at=datetime.now(timezone.utc),
            sent_at=None,
            delivered_at=None,
            failed_at=None,
            error_message=None,
            delivery_metadata={},
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    # Validation Tests

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, alerting_service, sample_alert_data):
        """Test successful validation of alert creation data."""
        validated_data = await alerting_service.validate_create_data(sample_alert_data)

        assert validated_data["name"] == "Test Alert"
        assert validated_data["condition_type"] == "execution_failure"
        assert validated_data["severity"] == AlertSeverity.HIGH
        assert AlertChannel.EMAIL.value in validated_data["notification_channels"]
        assert validated_data["escalation_delay_minutes"] == 30
        assert validated_data["max_escalations"] == 3
        assert validated_data["is_active"] is True
        assert validated_data["is_muted"] is False

    # Alert Configuration Management Tests

    @pytest.mark.asyncio
    async def test_create_alert_configuration_success(
        self, alerting_service, sample_alert_data, sample_workflow_alert
    ):
        """Test successful alert configuration creation."""
        # Mock repository create
        alerting_service.repository.create_alert.return_value = sample_workflow_alert

        alert_create = WorkflowAlertCreate(**sample_alert_data)
        result = await alerting_service.create_alert_configuration(alert_create)

        assert result == sample_workflow_alert
        assert sample_workflow_alert.id in alerting_service._active_alerts

        # Verify repository call
        alerting_service.repository.create_alert.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_alert_configuration_inactive_alert(
        self, alerting_service, sample_alert_data, sample_workflow_alert
    ):
        """Test alert configuration creation with inactive alert."""
        # Make alert inactive
        sample_workflow_alert.is_active = False
        alerting_service.repository.create_alert.return_value = sample_workflow_alert

        alert_create = WorkflowAlertCreate(**sample_alert_data)
        result = await alerting_service.create_alert_configuration(alert_create, load_to_cache=True)

        assert result == sample_workflow_alert
        assert sample_workflow_alert.id not in alerting_service._active_alerts

    @pytest.mark.asyncio
    async def test_update_alert_configuration_success(
        self, alerting_service, sample_workflow_alert
    ):
        """Test successful alert configuration update."""
        alert_id = sample_workflow_alert.id

        # Mock repository update
        updated_alert = sample_workflow_alert
        updated_alert.name = "Updated Alert"
        alerting_service.repository.update_alert.return_value = updated_alert

        update_data = WorkflowAlertUpdate(name="Updated Alert")
        result = await alerting_service.update_alert_configuration(alert_id, update_data)

        assert result == updated_alert
        assert result.name == "Updated Alert"
        assert alert_id in alerting_service._active_alerts

        # Verify repository call
        alerting_service.repository.update_alert.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_alert_configuration_success(
        self, alerting_service, sample_workflow_alert
    ):
        """Test successful alert configuration deletion."""
        alert_id = sample_workflow_alert.id

        # Add alert to active cache
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        # Mock repository delete
        alerting_service.repository.delete_alert.return_value = True

        result = await alerting_service.delete_alert_configuration(alert_id)

        assert result is True
        assert alert_id not in alerting_service._active_alerts

        # Verify repository call
        alerting_service.repository.delete_alert.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_alert_configuration_from_cache(
        self, alerting_service, sample_workflow_alert
    ):
        """Test getting alert configuration from cache."""
        alert_id = sample_workflow_alert.id

        # Add alert to cache
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        result = await alerting_service.get_alert_configuration(alert_id)

        assert result == sample_workflow_alert
        # Repository should not be called
        alerting_service.repository.get_alert_by_id.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_alert_configuration_from_repository(
        self, alerting_service, sample_workflow_alert
    ):
        """Test getting alert configuration from repository."""
        alert_id = sample_workflow_alert.id

        # Mock repository get
        alerting_service.repository.get_alert_by_id.return_value = sample_workflow_alert

        result = await alerting_service.get_alert_configuration(alert_id)

        assert result == sample_workflow_alert
        assert alert_id in alerting_service._active_alerts  # Should be cached

        # Verify repository call
        alerting_service.repository.get_alert_by_id.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_alert_configurations_success(
        self, alerting_service, sample_workflow_alert
    ):
        """Test successful alert configurations listing."""
        alerts = [sample_workflow_alert]

        # Mock repository list
        alerting_service.repository.list_alerts.return_value = alerts

        result = await alerting_service.list_alert_configurations(
            severity=AlertSeverity.HIGH,
            is_active=True,
            limit=10
        )

        assert result == alerts
        assert sample_workflow_alert.id in alerting_service._active_alerts

        # Verify repository call
        alerting_service.repository.list_alerts.assert_called_once()

    # Alert Triggering Tests

    @pytest.mark.asyncio
    async def test_trigger_alert_success(
        self, alerting_service, sample_workflow_alert, sample_alert_trigger_event
    ):
        """Test successful alert triggering."""
        alert_id = sample_workflow_alert.id

        # Add alert to cache
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        # Mock repository create trigger event
        alerting_service.trigger_repo.create_trigger_event.return_value = sample_alert_trigger_event

        with patch('asyncio.create_task') as mock_create_task:
            result = await alerting_service.trigger_alert(
                alert_id=alert_id,
                trigger_condition="execution_failure",
                alert_message="Test alert message",
                severity=AlertSeverity.HIGH,
                workflow_execution_id=uuid4()
            )

        assert result == sample_alert_trigger_event

        # Verify repository call
        alerting_service.trigger_repo.create_trigger_event.assert_called_once()

        # Verify notification delivery task was created
        assert mock_create_task.call_count >= 1

    @pytest.mark.asyncio
    async def test_trigger_alert_inactive_alert(
        self, alerting_service, sample_workflow_alert
    ):
        """Test triggering inactive alert."""
        alert_id = sample_workflow_alert.id
        sample_workflow_alert.is_active = False

        # Add alert to cache
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        with pytest.raises(ValidationError) as exc_info:
            await alerting_service.trigger_alert(
                alert_id=alert_id,
                trigger_condition="execution_failure",
                alert_message="Test alert message",
                severity=AlertSeverity.HIGH
            )

        assert "Cannot trigger inactive alert" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_trigger_alert_muted_alert(
        self, alerting_service, sample_workflow_alert
    ):
        """Test triggering muted alert."""
        alert_id = sample_workflow_alert.id
        sample_workflow_alert.is_muted = True
        sample_workflow_alert.muted_until = datetime.now(timezone.utc) + timedelta(hours=1)

        # Add alert to cache
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        with pytest.raises(ValidationError) as exc_info:
            await alerting_service.trigger_alert(
                alert_id=alert_id,
                trigger_condition="execution_failure",
                alert_message="Test alert message",
                severity=AlertSeverity.HIGH
            )

        assert "Alert is currently muted" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_acknowledge_alert_success(
        self, alerting_service, sample_alert_trigger_event
    ):
        """Test successful alert acknowledgment."""
        trigger_event_id = sample_alert_trigger_event.id

        # Mock repository acknowledge
        acknowledged_event = sample_alert_trigger_event
        acknowledged_event.is_acknowledged = True
        alerting_service.trigger_repo.acknowledge_trigger_event.return_value = acknowledged_event

        result = await alerting_service.acknowledge_alert(
            trigger_event_id=trigger_event_id,
            acknowledged_by="test_user",
            acknowledgment_message="Acknowledged"
        )

        assert result == acknowledged_event
        assert result.is_acknowledged is True

        # Verify repository call
        alerting_service.trigger_repo.acknowledge_trigger_event.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_alert_success(
        self, alerting_service, sample_alert_trigger_event
    ):
        """Test successful alert resolution."""
        trigger_event_id = sample_alert_trigger_event.id

        # Mock repository resolve
        resolved_event = sample_alert_trigger_event
        resolved_event.is_resolved = True
        alerting_service.trigger_repo.resolve_trigger_event.return_value = resolved_event

        result = await alerting_service.resolve_alert(
            trigger_event_id=trigger_event_id,
            resolved_by="test_user",
            resolution_message="Resolved"
        )

        assert result == resolved_event
        assert result.is_resolved is True

        # Verify repository call
        alerting_service.trigger_repo.resolve_trigger_event.assert_called_once()

    # Notification Delivery Tests

    @pytest.mark.asyncio
    async def test_notification_content_preparation_email(
        self, alerting_service, sample_alert_trigger_event, sample_workflow_alert
    ):
        """Test notification content preparation for email channel."""
        content = await alerting_service._prepare_notification_content(
            sample_alert_trigger_event, sample_workflow_alert, AlertChannel.EMAIL.value
        )

        assert content["alert_name"] == sample_workflow_alert.name
        assert content["severity"] == sample_alert_trigger_event.severity.value
        assert content["message"] == sample_alert_trigger_event.alert_message
        assert "subject" in content
        assert "html_body" in content
        assert f"[{sample_alert_trigger_event.severity.value.upper()}]" in content["subject"]

    @pytest.mark.asyncio
    async def test_notification_content_preparation_slack(
        self, alerting_service, sample_alert_trigger_event, sample_workflow_alert
    ):
        """Test notification content preparation for Slack channel."""
        content = await alerting_service._prepare_notification_content(
            sample_alert_trigger_event, sample_workflow_alert, AlertChannel.SLACK.value
        )

        assert content["alert_name"] == sample_workflow_alert.name
        assert "slack_blocks" in content
        assert isinstance(content["slack_blocks"], list)
        assert len(content["slack_blocks"]) >= 2  # Header and section blocks

    @pytest.mark.asyncio
    async def test_notification_content_preparation_sms(
        self, alerting_service, sample_alert_trigger_event, sample_workflow_alert
    ):
        """Test notification content preparation for SMS channel."""
        content = await alerting_service._prepare_notification_content(
            sample_alert_trigger_event, sample_workflow_alert, AlertChannel.SMS.value
        )

        assert content["alert_name"] == sample_workflow_alert.name
        assert "sms_text" in content
        assert sample_workflow_alert.name in content["sms_text"]
        assert sample_alert_trigger_event.severity.value.upper() in content["sms_text"]

    @pytest.mark.asyncio
    async def test_notification_content_preparation_webhook(
        self, alerting_service, sample_alert_trigger_event, sample_workflow_alert
    ):
        """Test notification content preparation for webhook channel."""
        content = await alerting_service._prepare_notification_content(
            sample_alert_trigger_event, sample_workflow_alert, AlertChannel.WEBHOOK.value
        )

        assert content["alert_name"] == sample_workflow_alert.name
        assert "webhook_payload" in content
        assert content["webhook_payload"]["alert_id"] == str(sample_workflow_alert.id)
        assert content["webhook_payload"]["trigger_event_id"] == str(sample_alert_trigger_event.id)

    @pytest.mark.asyncio
    async def test_send_notification_success(self, alerting_service):
        """Test successful notification sending."""
        # Mock successful delivery
        with patch.object(alerting_service, '_send_notification', return_value=True):
            result = await alerting_service._send_notification("email", {"test": "content"})
            assert result is True

    @pytest.mark.asyncio
    async def test_send_notification_failure(self, alerting_service):
        """Test failed notification sending."""
        # Mock failed delivery
        with patch.object(alerting_service, '_send_notification', return_value=False):
            result = await alerting_service._send_notification("email", {"test": "content"})
            assert result is False

    # Circuit Breaker Integration Tests

    @pytest.mark.asyncio
    async def test_circuit_breaker_integration_email_service(self, alerting_service):
        """Test circuit breaker integration with email service."""
        service_name = "email_service"
        circuit_breaker = alerting_service._circuit_breakers[service_name]

        # Initially closed
        assert circuit_breaker.state == "CLOSED"
        assert circuit_breaker.is_call_allowed() is True

        # Simulate failures to open circuit
        for _ in range(5):  # Default failure threshold
            circuit_breaker.record_failure()

        assert circuit_breaker.state == "OPEN"
        assert circuit_breaker.is_call_allowed() is False

    @pytest.mark.asyncio
    async def test_circuit_breaker_recovery(self, alerting_service):
        """Test circuit breaker recovery after success."""
        service_name = "sms_service"
        circuit_breaker = alerting_service._circuit_breakers[service_name]

        # Open circuit
        for _ in range(5):
            circuit_breaker.record_failure()
        assert circuit_breaker.state == "OPEN"

        # Record success to close circuit
        circuit_breaker.record_success()
        assert circuit_breaker.state == "CLOSED"
        assert circuit_breaker.failure_count == 0

    # Alert Analytics and Statistics Tests

    @pytest.mark.asyncio
    async def test_get_alert_statistics_success(self, alerting_service):
        """Test successful alert statistics retrieval."""
        # Mock repository statistics
        mock_stats = {
            "total_alerts": 100,
            "triggered_alerts": 25,
            "resolved_alerts": 20,
            "acknowledged_alerts": 22
        }
        alerting_service.trigger_repo.get_alert_statistics.return_value = mock_stats

        # Add some delivery metrics
        alerting_service._delivery_metrics = [
            {
                "channel": "email",
                "delivery_time_ms": 150,
                "success": True,
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "channel": "slack",
                "delivery_time_ms": 200,
                "success": True,
                "timestamp": datetime.now(timezone.utc)
            }
        ]

        result = await alerting_service.get_alert_statistics(time_range_hours=24)

        assert result["total_alerts"] == 100
        assert result["triggered_alerts"] == 25
        assert "delivery_success_rate" in result
        assert "average_delivery_time_ms" in result
        assert "total_deliveries" in result

        # Verify repository call
        alerting_service.trigger_repo.get_alert_statistics.assert_called_once()

    def test_calculate_delivery_statistics_with_data(self, alerting_service):
        """Test delivery statistics calculation with data."""
        start_time = datetime.now(timezone.utc) - timedelta(hours=1)

        # Add delivery metrics
        alerting_service._delivery_metrics = [
            {
                "channel": "email",
                "delivery_time_ms": 100,
                "success": True,
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "channel": "slack",
                "delivery_time_ms": 150,
                "success": True,
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "channel": "sms",
                "delivery_time_ms": 200,
                "success": False,
                "timestamp": datetime.now(timezone.utc)
            }
        ]

        stats = alerting_service._calculate_delivery_statistics(start_time)

        assert stats["delivery_success_rate"] == 66.67  # 2/3 * 100
        assert stats["average_delivery_time_ms"] == 125.0  # (100 + 150) / 2
        assert stats["total_deliveries"] == 3

    def test_calculate_delivery_statistics_no_data(self, alerting_service):
        """Test delivery statistics calculation with no data."""
        start_time = datetime.now(timezone.utc) - timedelta(hours=1)

        # No delivery metrics
        alerting_service._delivery_metrics = []

        stats = alerting_service._calculate_delivery_statistics(start_time)

        assert stats["delivery_success_rate"] == 0.0
        assert stats["average_delivery_time_ms"] == 0.0
        assert stats["total_deliveries"] == 0

    def test_calculate_escalation_statistics(self, alerting_service):
        """Test escalation statistics calculation."""
        start_time = datetime.now(timezone.utc) - timedelta(hours=1)

        # Add escalation metrics
        alerting_service._escalation_metrics = [
            {
                "trigger_event_id": str(uuid4()),
                "escalation_level": 1,
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "trigger_event_id": str(uuid4()),
                "escalation_level": 2,
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "trigger_event_id": str(uuid4()),
                "escalation_level": 1,
                "timestamp": datetime.now(timezone.utc)
            }
        ]

        stats = alerting_service._calculate_escalation_statistics(start_time)

        assert stats["total_escalations"] == 3
        assert stats["escalation_levels"][1] == 2  # Two level 1 escalations
        assert stats["escalation_levels"][2] == 1  # One level 2 escalation
        assert stats["escalation_levels"][3] == 0  # No level 3 escalations

    # Error Handling Tests

    @pytest.mark.asyncio
    async def test_service_error_handling(self, alerting_service):
        """Test comprehensive service error handling."""
        # Mock repository to raise exception
        alerting_service.repository.get_alert_by_id.side_effect = Exception("Database error")

        with pytest.raises(ServiceError):
            await alerting_service.get_alert_configuration(uuid4())

    @pytest.mark.asyncio
    async def test_alert_not_found_error(self, alerting_service):
        """Test alert not found error handling."""
        # Mock repository to raise NotFoundError
        alerting_service.repository.get_alert_by_id.side_effect = NotFoundError("Alert not found")

        with pytest.raises(NotFoundError):
            await alerting_service.get_alert_configuration(uuid4())

    # Performance Validation Tests

    @pytest.mark.asyncio
    async def test_performance_targets_validation(self, alerting_service):
        """Test that service meets performance targets."""
        import time

        # Test alert generation target (<50ms)
        start_time = time.time()
        await alerting_service._calculate_delivery_statistics(datetime.now(timezone.utc))
        alert_generation_ms = (time.time() - start_time) * 1000
        assert alert_generation_ms < 50, f"Alert generation {alert_generation_ms}ms exceeds 50ms target"

        # Test notification processing target (<100ms)
        start_time = time.time()
        content = await alerting_service._prepare_notification_content(
            sample_alert_trigger_event=MagicMock(
                severity=AlertSeverity.HIGH,
                alert_message="Test message",
                trigger_condition="test",
                triggered_at=datetime.now(timezone.utc),
                trigger_value=None,
                threshold_value=None,
                workflow_execution_id=None
            ),
            alert_config=MagicMock(
                name="Test Alert",
                id=uuid4()
            ),
            channel=AlertChannel.EMAIL.value
        )
        processing_ms = (time.time() - start_time) * 1000
        assert processing_ms < 100, f"Notification processing {processing_ms}ms exceeds 100ms target"

    @pytest.mark.asyncio
    async def test_concurrent_alert_handling(self, alerting_service, sample_workflow_alert):
        """Test concurrent alert handling capabilities."""
        alert_id = sample_workflow_alert.id
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        # Mock repository methods
        alerting_service.trigger_repo.create_trigger_event.return_value = MagicMock(id=uuid4())

        # Create multiple concurrent alert triggers
        tasks = []
        for i in range(10):
            task = alerting_service.trigger_alert(
                alert_id=alert_id,
                trigger_condition=f"test_condition_{i}",
                alert_message=f"Test message {i}",
                severity=AlertSeverity.MEDIUM
            )
            tasks.append(task)

        # Execute all tasks concurrently
        with patch('asyncio.create_task'):
            results = await asyncio.gather(*tasks, return_exceptions=True)

        # Verify all alerts were processed successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == 10

    @pytest.mark.asyncio
    async def test_alert_cache_performance(self, alerting_service, sample_workflow_alert):
        """Test alert cache performance for frequent access."""
        alert_id = sample_workflow_alert.id
        alerting_service._active_alerts[alert_id] = sample_workflow_alert

        import time

        # Test cache access performance
        start_time = time.time()
        for _ in range(100):
            await alerting_service.get_alert_configuration(alert_id)
        cache_access_time = (time.time() - start_time) * 1000

        # Should be very fast for cached access
        assert cache_access_time < 50, f"Cache access time {cache_access_time}ms too slow"

        # Repository should not be called for cached items
        alerting_service.repository.get_alert_by_id.assert_not_called()
