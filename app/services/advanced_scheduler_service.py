"""
Advanced Scheduler Service for Culture Connect Backend API.

This module provides comprehensive advanced scheduling services including:
- AdvancedSchedulerService: Main scheduling service with lifecycle management
- Real-time schedule execution using CronExpressionParser and ScheduleCalculator
- Conflict detection and resolution using priority-based scheduling
- Integration with WorkflowOrchestrationService and TaskService (Celery)
- Performance monitoring with metrics collection and alerting
- Circuit breaker patterns and comprehensive error handling

Implements Task 6.2.2 Phase 3.3 requirements for advanced scheduler service with
production-grade business logic following established BaseService patterns.

Performance Targets:
- Schedule creation: <500ms for complex schedules
- Schedule calculation: <100ms for next execution time
- Conflict resolution: <50ms for conflict detection
- Monitoring latency: <50ms for status updates
- Throughput: >2000 schedules/minute processing capacity
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.circuit_breaker import BaseCircuitBreaker, CircuitBreakerConfig
from app.core.cron_parser import CronExpressionParser, ParsedCronExpression, CronParseError
from app.core.schedule_calculator import ScheduleCalculator
from app.models.workflow_models import (
    JobSchedule, ScheduleType, HolidayCalendar
)
from app.schemas.workflow_schemas import (
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse,
    ScheduleConflictCheck, ScheduleConflictResult,
    ScheduleCalculation, ScheduleCalculationResult as ScheduleCalculationSchema
)
from app.repositories.workflow_repositories import (
    JobScheduleRepository, WorkflowRepository, WorkflowStepRepository
)
from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.services.workflow_orchestration_service import WorkflowOrchestrationService
from app.services.task_service import TaskService

logger = logging.getLogger(__name__)


class ScheduleExecutionError(ServiceError):
    """Exception for schedule execution errors."""

    def __init__(self, message: str, schedule_id: Optional[UUID] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="SCHEDULE_EXECUTION_ERROR",
            details={"schedule_id": str(schedule_id) if schedule_id else None, **kwargs}
        )


class ScheduleConflictError(ConflictError):
    """Exception for schedule conflict errors."""

    def __init__(self, message: str, conflicting_schedules: Optional[List[UUID]] = None, **kwargs):
        super().__init__(
            message=message,
            details={
                "conflicting_schedules": [str(s) for s in conflicting_schedules] if conflicting_schedules else [],
                **kwargs
            }
        )


class AdvancedSchedulerService(BaseService[JobSchedule, JobScheduleRepository]):
    """
    Advanced scheduler service for comprehensive schedule lifecycle management.

    Provides end-to-end schedule management including:
    - Schedule lifecycle management (create, update, delete, activate, deactivate)
    - Real-time schedule execution using CronExpressionParser and ScheduleCalculator
    - Conflict detection and resolution using priority-based scheduling
    - Integration with WorkflowOrchestrationService and TaskService (Celery)
    - Performance monitoring with metrics collection and alerting
    - Circuit breaker patterns and comprehensive error handling
    """

    def __init__(
        self,
        db_session: AsyncSession,
        workflow_service: Optional[WorkflowOrchestrationService] = None,
        task_service: Optional[TaskService] = None
    ):
        """
        Initialize advanced scheduler service.

        Args:
            db_session: Database session
            workflow_service: Workflow orchestration service
            task_service: Task service for Celery integration
        """
        super().__init__(JobSchedule, JobScheduleRepository, db_session)

        # Service dependencies
        self.workflow_service = workflow_service
        self.task_service = task_service

        # Core scheduling components
        self.cron_parser = CronExpressionParser()
        self.schedule_calculator = ScheduleCalculator()

        # Repository instances
        self.schedule_repository = JobScheduleRepository(db_session)
        self.workflow_repository = WorkflowRepository(db_session)
        self.step_repository = WorkflowStepRepository(db_session)

        # Circuit breaker for external service calls
        self.circuit_breaker = BaseCircuitBreaker(
            CircuitBreakerConfig(
                name="advanced_scheduler_service",
                failure_threshold=5,
                recovery_timeout=30.0,
                expected_exception=Exception
            )
        )

        # Performance tracking
        self._active_schedules: Dict[UUID, Dict[str, Any]] = {}
        self._execution_metrics: Dict[str, Any] = {
            "schedules_created": 0,
            "schedules_executed": 0,
            "conflicts_resolved": 0,
            "execution_failures": 0,
            "average_execution_time": 0.0
        }

        # Schedule execution monitoring
        self._execution_tasks: Dict[UUID, asyncio.Task] = {}
        self._conflict_cache: Dict[str, List[UUID]] = {}

        logger.info(
            "AdvancedSchedulerService initialized",
            extra={
                "service_name": self.get_service_name(),
                "circuit_breaker": self.circuit_breaker.config.name,
                "correlation_id": correlation_id.get()
            }
        )

    def get_service_name(self) -> str:
        """Get service name for logging and monitoring."""
        return "AdvancedSchedulerService"

    # Schedule Lifecycle Management Methods

    async def create_schedule(
        self,
        schedule_data: JobScheduleCreate,
        user_id: Optional[UUID] = None
    ) -> JobScheduleResponse:
        """
        Create a new advanced job schedule with comprehensive validation.

        Args:
            schedule_data: Schedule creation data
            user_id: User creating the schedule

        Returns:
            Created schedule response

        Raises:
            ValidationError: If schedule data is invalid
            ScheduleConflictError: If schedule conflicts with existing schedules
            ServiceError: If creation fails

        Performance Target: <500ms for complex schedules
        """
        correlation = self.log_operation_start(
            "create_schedule",
            schedule_name=schedule_data.name,
            schedule_type=schedule_data.schedule_type.value if hasattr(schedule_data.schedule_type, 'value') else str(schedule_data.schedule_type),
            user_id=str(user_id) if user_id else None
        )
        start_time = time.time()

        try:
            # Validate schedule data
            validated_data = await self._validate_schedule_data(schedule_data)

            # Parse and validate cron expression
            if validated_data.get("cron_expression"):
                parsed_cron = await self._parse_and_validate_cron(
                    validated_data["cron_expression"],
                    validated_data.get("timezone", "UTC"),
                    validated_data.get("business_days_only", False),
                    validated_data.get("exclude_holidays", False),
                    validated_data.get("holiday_calendar")
                )
                validated_data["parsed_cron_metadata"] = {
                    "is_named_schedule": parsed_cron.is_named_schedule,
                    "field_counts": {
                        "seconds": len(parsed_cron.seconds),
                        "minutes": len(parsed_cron.minutes),
                        "hours": len(parsed_cron.hours),
                        "days": len(parsed_cron.days),
                        "months": len(parsed_cron.months),
                        "weekdays": len(parsed_cron.weekdays)
                    }
                }

            # Check for schedule conflicts
            conflicts = await self._detect_schedule_conflicts(validated_data)
            if conflicts and validated_data.get("priority", 5) <= max(c.get("priority", 5) for c in conflicts):
                conflicting_ids = [c["schedule_id"] for c in conflicts]
                raise ScheduleConflictError(
                    f"Schedule conflicts detected with {len(conflicts)} existing schedules",
                    conflicting_schedules=conflicting_ids
                )

            # Calculate next execution time
            if validated_data.get("cron_expression") or validated_data.get("named_schedule"):
                next_execution = await self._calculate_next_execution(validated_data)
                validated_data["next_run_at"] = next_execution

            # Add default is_active if not specified
            if "is_active" not in validated_data:
                validated_data["is_active"] = True

            # Create schedule record
            async with self.get_session_context() as session:
                repository = JobScheduleRepository(session)
                schedule = await repository.create_schedule(
                    JobScheduleCreate(**validated_data),
                    correlation_id=correlation
                )

                # Start schedule monitoring if active
                if validated_data.get("is_active", True):
                    await self._start_schedule_monitoring(schedule.id, correlation)

                # Update metrics
                self._execution_metrics["schedules_created"] += 1
                execution_time = (time.time() - start_time) * 1000

                # Track performance metrics
                metrics_collector.record_business_event("schedule_created", 1)

                self.log_operation_success(
                    correlation,
                    f"Schedule created successfully: {schedule.name}",
                    schedule_id=str(schedule.id),
                    execution_time_ms=execution_time,
                    next_run_at=schedule.next_run_at.isoformat() if schedule.next_run_at else None
                )

                return JobScheduleResponse.model_validate(schedule)

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "create_schedule",
                {
                    "schedule_name": schedule_data.name,
                    "execution_time_ms": execution_time,
                    "user_id": str(user_id) if user_id else None
                }
            )

    async def update_schedule(
        self,
        schedule_id: UUID,
        update_data: JobScheduleUpdate,
        user_id: Optional[UUID] = None
    ) -> JobScheduleResponse:
        """
        Update an existing job schedule with conflict detection.

        Args:
            schedule_id: Schedule ID to update
            update_data: Update data
            user_id: User updating the schedule

        Returns:
            Updated schedule response

        Raises:
            NotFoundError: If schedule not found
            ScheduleConflictError: If update creates conflicts
            ServiceError: If update fails

        Performance Target: <300ms for schedule updates
        """
        correlation = self.log_operation_start(
            "update_schedule",
            schedule_id=str(schedule_id),
            user_id=str(user_id) if user_id else None
        )
        start_time = time.time()

        try:
            # Get existing schedule
            async with self.get_session_context() as session:
                repository = JobScheduleRepository(session)
                existing_schedule = await repository.get_job_schedule_by_id(
                    schedule_id, correlation_id=correlation
                )

                if not existing_schedule:
                    raise NotFoundError(f"Schedule not found: {schedule_id}")

                # Validate update data
                update_dict = update_data.model_dump(exclude_unset=True)
                if not update_dict:
                    return JobScheduleResponse.model_validate(existing_schedule)

                # Parse new cron expression if provided
                if "cron_expression" in update_dict:
                    await self._parse_and_validate_cron(
                        update_dict["cron_expression"],
                        update_dict.get("timezone", existing_schedule.timezone),
                        update_dict.get("business_days_only", existing_schedule.business_days_only),
                        update_dict.get("exclude_holidays", existing_schedule.exclude_holidays),
                        update_dict.get("holiday_calendar", existing_schedule.holiday_calendar)
                    )

                # Check for conflicts with updated data
                merged_data = {**existing_schedule.__dict__, **update_dict}
                conflicts = await self._detect_schedule_conflicts(merged_data, exclude_schedule_id=schedule_id)

                if conflicts and merged_data.get("priority", 5) <= max(c.get("priority", 5) for c in conflicts):
                    conflicting_ids = [c["schedule_id"] for c in conflicts]
                    raise ScheduleConflictError(
                        f"Schedule update would create conflicts with {len(conflicts)} existing schedules",
                        conflicting_schedules=conflicting_ids
                    )

                # Calculate new next execution time if schedule changed
                if any(field in update_dict for field in ["cron_expression", "named_schedule", "timezone", "start_date"]):
                    next_execution = await self._calculate_next_execution(merged_data)
                    update_dict["next_run_at"] = next_execution

                # Update schedule
                updated_schedule = await repository.update_job_schedule(
                    schedule_id, JobScheduleUpdate(**update_dict), correlation_id=correlation
                )

                # Restart monitoring if schedule was modified
                if schedule_id in self._execution_tasks:
                    await self._stop_schedule_monitoring(schedule_id)
                    if updated_schedule.is_active:
                        await self._start_schedule_monitoring(schedule_id, correlation)

                execution_time = (time.time() - start_time) * 1000
                metrics_collector.record_business_event("schedule_updated", 1)

                self.log_operation_success(
                    correlation,
                    f"Schedule updated successfully: {updated_schedule.name}",
                    schedule_id=str(schedule_id),
                    execution_time_ms=execution_time,
                    fields_updated=list(update_dict.keys())
                )

                return JobScheduleResponse.model_validate(updated_schedule)

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "update_schedule",
                {
                    "schedule_id": str(schedule_id),
                    "execution_time_ms": execution_time,
                    "user_id": str(user_id) if user_id else None
                }
            )

    async def delete_schedule(
        self,
        schedule_id: UUID,
        user_id: Optional[UUID] = None
    ) -> bool:
        """
        Delete a job schedule and stop its monitoring.

        Args:
            schedule_id: Schedule ID to delete
            user_id: User deleting the schedule

        Returns:
            True if deleted successfully

        Raises:
            NotFoundError: If schedule not found
            ServiceError: If deletion fails

        Performance Target: <200ms for schedule deletion
        """
        correlation = self.log_operation_start(
            "delete_schedule",
            schedule_id=str(schedule_id),
            user_id=str(user_id) if user_id else None
        )
        start_time = time.time()

        try:
            # Stop monitoring if active
            if schedule_id in self._execution_tasks:
                await self._stop_schedule_monitoring(schedule_id)

            # Delete schedule
            async with self.get_session_context() as session:
                repository = JobScheduleRepository(session)
                deleted = await repository.delete_job_schedule(schedule_id, correlation_id=correlation)

                if not deleted:
                    raise NotFoundError(f"Schedule not found: {schedule_id}")

                # Clean up tracking data
                self._active_schedules.pop(schedule_id, None)

                execution_time = (time.time() - start_time) * 1000
                metrics_collector.record_business_event("schedule_deleted", 1)

                self.log_operation_success(
                    correlation,
                    f"Schedule deleted successfully",
                    schedule_id=str(schedule_id),
                    execution_time_ms=execution_time
                )

                return True

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "delete_schedule",
                {
                    "schedule_id": str(schedule_id),
                    "execution_time_ms": execution_time,
                    "user_id": str(user_id) if user_id else None
                }
            )

    async def activate_schedule(
        self,
        schedule_id: UUID,
        user_id: Optional[UUID] = None
    ) -> JobScheduleResponse:
        """
        Activate a job schedule and start monitoring.

        Args:
            schedule_id: Schedule ID to activate
            user_id: User activating the schedule

        Returns:
            Activated schedule response

        Performance Target: <100ms for schedule activation
        """
        correlation = self.log_operation_start(
            "activate_schedule",
            schedule_id=str(schedule_id),
            user_id=str(user_id) if user_id else None
        )
        start_time = time.time()

        try:
            # Update schedule to active
            update_data = JobScheduleUpdate(is_active=True)
            updated_schedule = await self.update_schedule(schedule_id, update_data, user_id)

            # Start monitoring
            await self._start_schedule_monitoring(schedule_id, correlation)

            execution_time = (time.time() - start_time) * 1000
            metrics_collector.record_business_event("schedule_activated", 1)

            self.log_operation_success(
                correlation,
                f"Schedule activated successfully",
                schedule_id=str(schedule_id),
                execution_time_ms=execution_time
            )

            return updated_schedule

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "activate_schedule",
                {
                    "schedule_id": str(schedule_id),
                    "execution_time_ms": execution_time,
                    "user_id": str(user_id) if user_id else None
                }
            )

    async def deactivate_schedule(
        self,
        schedule_id: UUID,
        user_id: Optional[UUID] = None
    ) -> JobScheduleResponse:
        """
        Deactivate a job schedule and stop monitoring.

        Args:
            schedule_id: Schedule ID to deactivate
            user_id: User deactivating the schedule

        Returns:
            Deactivated schedule response

        Performance Target: <100ms for schedule deactivation
        """
        correlation = self.log_operation_start(
            "deactivate_schedule",
            schedule_id=str(schedule_id),
            user_id=str(user_id) if user_id else None
        )
        start_time = time.time()

        try:
            # Stop monitoring
            if schedule_id in self._execution_tasks:
                await self._stop_schedule_monitoring(schedule_id)

            # Update schedule to inactive
            update_data = JobScheduleUpdate(is_active=False)
            updated_schedule = await self.update_schedule(schedule_id, update_data, user_id)

            execution_time = (time.time() - start_time) * 1000
            metrics_collector.record_business_event("schedule_deactivated", 1)

            self.log_operation_success(
                correlation,
                f"Schedule deactivated successfully",
                schedule_id=str(schedule_id),
                execution_time_ms=execution_time
            )

            return updated_schedule

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "deactivate_schedule",
                {
                    "schedule_id": str(schedule_id),
                    "execution_time_ms": execution_time,
                    "user_id": str(user_id) if user_id else None
                }
            )

    # Conflict Detection and Resolution Methods

    async def detect_schedule_conflicts(
        self,
        conflict_check: ScheduleConflictCheck
    ) -> ScheduleConflictResult:
        """
        Detect conflicts between schedules with comprehensive analysis.

        Args:
            conflict_check: Conflict detection parameters

        Returns:
            Conflict detection result with detailed analysis

        Performance Target: <50ms for conflict detection
        """
        correlation = self.log_operation_start(
            "detect_schedule_conflicts",
            cron_expression=conflict_check.cron_expression,
            timezone=conflict_check.timezone
        )
        start_time = time.time()

        try:
            # Get potential conflicts
            conflicts = await self._detect_schedule_conflicts(
                conflict_check.model_dump(),
                exclude_schedule_id=conflict_check.schedule_id
            )

            # Analyze conflict severity
            conflict_analysis = []
            for conflict in conflicts:
                severity = self._calculate_conflict_severity(
                    conflict_check.model_dump(),
                    conflict
                )
                conflict_analysis.append({
                    "schedule_id": conflict["schedule_id"],
                    "schedule_name": conflict["schedule_name"],
                    "conflict_type": conflict.get("conflict_type", "time_overlap"),
                    "severity": severity,
                    "priority_difference": abs(5 - conflict.get("priority", 5)),  # Default priority 5
                    "overlap_duration_minutes": conflict.get("overlap_duration_minutes", 0)
                })

            # Sort by severity (highest first)
            conflict_analysis.sort(key=lambda x: x["severity"], reverse=True)

            execution_time = (time.time() - start_time) * 1000
            metrics_collector.record_business_event("conflict_detection", 1)

            result = ScheduleConflictResult(
                has_conflicts=len(conflicts) > 0,
                conflicting_schedules=[c["schedule_id"] for c in conflict_analysis],
                conflict_details=conflict_analysis,
                recommendations=self._generate_conflict_resolutions(conflict_analysis)
            )

            self.log_operation_success(
                correlation,
                f"Conflict detection completed: {len(conflicts)} conflicts found"
            )

            return result

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "detect_schedule_conflicts",
                {
                    "cron_expression": conflict_check.cron_expression,
                    "execution_time_ms": execution_time
                }
            )

    async def calculate_schedule_executions(
        self,
        calculation_request: ScheduleCalculation
    ) -> ScheduleCalculationSchema:
        """
        Calculate next execution times for a schedule with performance optimization.

        Args:
            calculation_request: Schedule calculation parameters

        Returns:
            Schedule calculation result with execution times

        Performance Target: <100ms for next execution time calculation
        """
        correlation = self.log_operation_start(
            "calculate_schedule_executions",
            cron_expression=calculation_request.cron_expression,
            max_results=calculation_request.max_results
        )
        start_time = time.time()

        try:
            # Use schedule calculator for computation
            calculation_result = self.schedule_calculator.calculate_next_executions(
                cron_expression=calculation_request.cron_expression,
                start_time=calculation_request.start_date,
                count=calculation_request.max_results,
                timezone=calculation_request.timezone,
                business_days_only=calculation_request.business_days_only,
                exclude_holidays=calculation_request.exclude_holidays,
                holiday_calendar=calculation_request.holiday_calendar
            )

            # Convert to schema format
            executions = []
            for execution in calculation_result.next_executions:
                executions.append({
                    "execution_time": execution.execution_time,
                    "timezone": execution.timezone,
                    "is_business_day": execution.is_business_day,
                    "is_holiday": execution.is_holiday,
                    "conflict_detected": execution.conflict_detected,
                    "conflict_reason": execution.conflict_reason
                })

            execution_time = (time.time() - start_time) * 1000
            metrics_collector.record_business_event("schedule_calculation", 1)

            result = ScheduleCalculationSchema(
                next_run_times=[exec["execution_time"] for exec in executions],
                calculation_time_ms=execution_time,
                is_valid=True,
                validation_errors=[],
                schedule_summary={
                    "total_calculated": calculation_result.total_calculated,
                    "business_days_filtered": calculation_result.business_days_filtered,
                    "holidays_filtered": calculation_result.holidays_filtered,
                    "conflicts_detected": calculation_result.conflicts_detected
                }
            )

            self.log_operation_success(
                correlation,
                f"Schedule calculation completed: {len(executions)} executions calculated"
            )

            return result

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            await self.handle_service_error(
                e, "calculate_schedule_executions",
                {
                    "cron_expression": calculation_request.cron_expression,
                    "execution_time_ms": execution_time
                }
            )

    # Private Helper Methods

    async def _validate_schedule_data(self, schedule_data: JobScheduleCreate) -> Dict[str, Any]:
        """
        Validate schedule creation data with comprehensive checks.

        Args:
            schedule_data: Schedule data to validate

        Returns:
            Validated data dictionary

        Raises:
            ValidationError: If validation fails
        """
        data = schedule_data.model_dump()

        # Validate schedule type requirements
        if data.get("schedule_type") == ScheduleType.CRON:
            if not data.get("cron_expression") and not data.get("named_schedule"):
                raise ValidationError("Cron schedule type requires cron_expression or named_schedule")
        elif data.get("schedule_type") == ScheduleType.INTERVAL:
            if not data.get("interval_seconds"):
                raise ValidationError("Interval schedule type requires interval_seconds")

        # Validate date ranges
        if data.get("start_date") and data.get("end_date"):
            if data["start_date"] >= data["end_date"]:
                raise ValidationError("start_date must be before end_date")

        # Validate priority range
        priority = data.get("priority", 5)
        if not 1 <= priority <= 10:
            raise ValidationError("Priority must be between 1 and 10")

        # Validate max concurrent runs
        max_concurrent = data.get("max_concurrent_runs", 1)
        if max_concurrent < 1:
            raise ValidationError("max_concurrent_runs must be at least 1")

        return data

    async def _parse_and_validate_cron(
        self,
        cron_expression: str,
        timezone: str = "UTC",
        business_days_only: bool = False,
        exclude_holidays: bool = False,
        holiday_calendar: Optional[HolidayCalendar] = None
    ) -> ParsedCronExpression:
        """
        Parse and validate cron expression with error handling.

        Args:
            cron_expression: Cron expression to parse
            timezone: Timezone for validation
            business_days_only: Business days only flag
            exclude_holidays: Exclude holidays flag
            holiday_calendar: Holiday calendar

        Returns:
            Parsed cron expression

        Raises:
            ValidationError: If cron expression is invalid
        """
        try:
            return self.cron_parser.parse(
                cron_expression,
                timezone=timezone,
                business_days_only=business_days_only,
                exclude_holidays=exclude_holidays,
                holiday_calendar=holiday_calendar
            )
        except CronParseError as e:
            raise ValidationError(f"Invalid cron expression: {str(e)}")

    async def _detect_schedule_conflicts(
        self,
        schedule_data: Dict[str, Any],
        exclude_schedule_id: Optional[UUID] = None
    ) -> List[Dict[str, Any]]:
        """
        Detect conflicts with existing schedules.

        Args:
            schedule_data: Schedule data to check
            exclude_schedule_id: Schedule ID to exclude from conflict check

        Returns:
            List of conflicting schedules

        Performance Target: <50ms for conflict detection
        """
        conflicts = []

        # Check cache first
        cache_key = f"conflicts_{hash(str(schedule_data))}"
        if cache_key in self._conflict_cache:
            return self._conflict_cache[cache_key]

        try:
            # Get overlapping schedules from database
            async with self.get_session_context() as session:
                # Build query for potential conflicts
                query = select(JobSchedule).where(
                    and_(
                        JobSchedule.is_active == True,
                        JobSchedule.id != exclude_schedule_id if exclude_schedule_id else True
                    )
                )

                # Add workflow definition filter if specified
                if schedule_data.get("workflow_definition_id"):
                    query = query.where(
                        JobSchedule.workflow_definition_id == schedule_data["workflow_definition_id"]
                    )

                result = await session.execute(query)
                existing_schedules = result.scalars().all()

                # Check each schedule for conflicts
                for existing in existing_schedules:
                    conflict_info = await self._check_schedule_overlap(schedule_data, existing)
                    if conflict_info:
                        conflicts.append(conflict_info)

            # Cache results for 5 minutes
            self._conflict_cache[cache_key] = conflicts
            asyncio.create_task(self._clear_cache_entry(cache_key, 300))

            return conflicts

        except Exception as e:
            logger.error(f"Error detecting schedule conflicts: {e}")
            return []

    async def _check_schedule_overlap(
        self,
        new_schedule: Dict[str, Any],
        existing_schedule: JobSchedule
    ) -> Optional[Dict[str, Any]]:
        """
        Check if two schedules overlap in execution time.

        Args:
            new_schedule: New schedule data
            existing_schedule: Existing schedule

        Returns:
            Conflict information if overlap detected, None otherwise
        """
        try:
            # Calculate next few executions for both schedules
            new_executions = []
            existing_executions = []

            # Get new schedule executions
            if new_schedule.get("cron_expression"):
                calc_result = self.schedule_calculator.calculate_next_executions(
                    new_schedule["cron_expression"],
                    count=10,
                    timezone=new_schedule.get("timezone", "UTC")
                )
                new_executions = [exec.execution_time for exec in calc_result.next_executions]

            # Get existing schedule executions
            if existing_schedule.cron_expression:
                calc_result = self.schedule_calculator.calculate_next_executions(
                    existing_schedule.cron_expression,
                    count=10,
                    timezone=existing_schedule.timezone
                )
                existing_executions = [exec.execution_time for exec in calc_result.next_executions]

            # Check for overlaps within tolerance window (default 5 minutes)
            tolerance_minutes = new_schedule.get("conflict_tolerance_minutes", 5)
            tolerance = timedelta(minutes=tolerance_minutes)

            for new_time in new_executions:
                for existing_time in existing_executions:
                    if abs((new_time - existing_time).total_seconds()) <= tolerance.total_seconds():
                        return {
                            "schedule_id": existing_schedule.id,
                            "schedule_name": existing_schedule.name,
                            "conflict_type": "time_overlap",
                            "priority": existing_schedule.priority,
                            "overlap_duration_minutes": tolerance_minutes,
                            "conflicting_time": new_time.isoformat()
                        }

            return None

        except Exception as e:
            logger.error(f"Error checking schedule overlap: {e}")
            return None

    async def _calculate_next_execution(self, schedule_data: Dict[str, Any]) -> Optional[datetime]:
        """
        Calculate next execution time for a schedule.

        Args:
            schedule_data: Schedule configuration data

        Returns:
            Next execution datetime or None if calculation fails
        """
        try:
            if schedule_data.get("cron_expression"):
                calc_result = self.schedule_calculator.calculate_next_executions(
                    schedule_data["cron_expression"],
                    count=1,
                    timezone=schedule_data.get("timezone", "UTC"),
                    business_days_only=schedule_data.get("business_days_only", False),
                    exclude_holidays=schedule_data.get("exclude_holidays", False),
                    holiday_calendar=schedule_data.get("holiday_calendar")
                )
                if calc_result.next_executions:
                    return calc_result.next_executions[0].execution_time

            elif schedule_data.get("interval_seconds"):
                # For interval schedules, next execution is now + interval
                from datetime import datetime, timezone
                return datetime.now(timezone.utc) + timedelta(seconds=schedule_data["interval_seconds"])

            return None

        except Exception as e:
            logger.error(f"Error calculating next execution: {e}")
            return None

    def _calculate_conflict_severity(
        self,
        new_schedule: Dict[str, Any],
        existing_conflict: Dict[str, Any]
    ) -> int:
        """
        Calculate conflict severity score (1-10, 10 being most severe).

        Args:
            new_schedule: New schedule data
            existing_conflict: Existing conflicting schedule data

        Returns:
            Severity score (1-10)
        """
        severity = 1

        # Priority difference affects severity
        priority_diff = abs(new_schedule.get("priority", 5) - existing_conflict.get("priority", 5))
        severity += min(priority_diff, 3)

        # Same workflow definition is more severe
        if (new_schedule.get("workflow_definition_id") ==
            existing_conflict.get("workflow_definition_id")):
            severity += 3

        # Overlap duration affects severity
        overlap_minutes = existing_conflict.get("overlap_duration_minutes", 0)
        if overlap_minutes > 30:
            severity += 2
        elif overlap_minutes > 10:
            severity += 1

        return min(severity, 10)

    def _generate_conflict_resolutions(self, conflicts: List[Dict[str, Any]]) -> List[str]:
        """
        Generate conflict resolution suggestions.

        Args:
            conflicts: List of conflict information

        Returns:
            List of resolution suggestions
        """
        suggestions = []

        if not conflicts:
            return suggestions

        # Priority-based suggestions
        high_priority_conflicts = [c for c in conflicts if c.get("severity", 0) >= 7]
        if high_priority_conflicts:
            suggestions.append("Consider increasing schedule priority to resolve high-severity conflicts")
            suggestions.append("Adjust execution time to avoid peak conflict periods")

        # Time-based suggestions
        time_conflicts = [c for c in conflicts if c.get("conflict_type") == "time_overlap"]
        if time_conflicts:
            suggestions.append("Modify cron expression to shift execution time")
            suggestions.append("Enable conflict tolerance to allow overlapping executions")

        # Workflow-specific suggestions
        workflow_conflicts = [c for c in conflicts if "workflow" in str(c.get("schedule_name", "")).lower()]
        if workflow_conflicts:
            suggestions.append("Consider using different workflow definitions for conflicting schedules")

        return suggestions

    async def _start_schedule_monitoring(self, schedule_id: UUID, correlation_id: str) -> None:
        """
        Start monitoring a schedule for execution.

        Args:
            schedule_id: Schedule ID to monitor
            correlation_id: Correlation ID for tracking
        """
        try:
            # Create monitoring task
            task = asyncio.create_task(
                self._monitor_schedule_execution(schedule_id, correlation_id)
            )
            self._execution_tasks[schedule_id] = task

            # Track active schedule
            self._active_schedules[schedule_id] = {
                "started_at": datetime.now(),
                "correlation_id": correlation_id,
                "task_id": id(task)
            }

            logger.info(
                f"Started monitoring schedule: {schedule_id}",
                extra={
                    "schedule_id": str(schedule_id),
                    "correlation_id": correlation_id
                }
            )

        except Exception as e:
            logger.error(f"Error starting schedule monitoring: {e}")

    async def _stop_schedule_monitoring(self, schedule_id: UUID) -> None:
        """
        Stop monitoring a schedule.

        Args:
            schedule_id: Schedule ID to stop monitoring
        """
        try:
            if schedule_id in self._execution_tasks:
                task = self._execution_tasks[schedule_id]
                task.cancel()
                del self._execution_tasks[schedule_id]

            self._active_schedules.pop(schedule_id, None)

            logger.info(f"Stopped monitoring schedule: {schedule_id}")

        except Exception as e:
            logger.error(f"Error stopping schedule monitoring: {e}")

    async def _monitor_schedule_execution(self, schedule_id: UUID, correlation_id: str) -> None:
        """
        Monitor schedule execution and trigger jobs when due.

        Args:
            schedule_id: Schedule ID to monitor
            correlation_id: Correlation ID for tracking
        """
        try:
            while True:
                # Get current schedule state
                async with self.get_session_context() as session:
                    repository = JobScheduleRepository(session)
                    schedule = await repository.get_job_schedule_by_id(schedule_id)

                    if not schedule or not schedule.is_active:
                        break

                    # Check if execution is due
                    now = datetime.now()
                    if schedule.next_run_at and now >= schedule.next_run_at:
                        await self._execute_schedule(schedule, correlation_id)

                # Wait before next check (default 30 seconds)
                await asyncio.sleep(30)

        except asyncio.CancelledError:
            logger.info(f"Schedule monitoring cancelled: {schedule_id}")
        except Exception as e:
            logger.error(f"Error in schedule monitoring: {e}")

    async def _execute_schedule(self, schedule: JobSchedule, correlation_id: str) -> None:
        """
        Execute a scheduled job.

        Args:
            schedule: Schedule to execute
            correlation_id: Correlation ID for tracking
        """
        try:
            execution_start = time.time()

            # Execute based on schedule type
            if schedule.workflow_definition_id and self.workflow_service:
                # Execute workflow
                await self._execute_workflow_schedule(schedule, correlation_id)
            else:
                # Execute direct task
                await self._execute_task_schedule(schedule, correlation_id)

            # Update schedule metrics
            execution_time = (time.time() - execution_start) * 1000
            self._execution_metrics["schedules_executed"] += 1
            self._execution_metrics["average_execution_time"] = (
                (self._execution_metrics["average_execution_time"] + execution_time) / 2
            )

            # Calculate next execution time
            next_execution = await self._calculate_next_execution(schedule.__dict__)

            # Update schedule
            async with self.get_session_context() as session:
                repository = JobScheduleRepository(session)
                await repository.update_job_schedule(
                    schedule.id,
                    JobScheduleUpdate(
                        last_run_at=datetime.now(),
                        next_run_at=next_execution,
                        run_count=schedule.run_count + 1
                    )
                )

            logger.info(
                f"Schedule executed successfully: {schedule.name}",
                extra={
                    "schedule_id": str(schedule.id),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

        except Exception as e:
            self._execution_metrics["execution_failures"] += 1
            logger.error(f"Error executing schedule: {e}")

    async def _execute_workflow_schedule(self, schedule: JobSchedule, correlation_id: str) -> None:
        """Execute a workflow-based schedule."""
        if self.workflow_service and schedule.workflow_definition_id:
            await self.workflow_service.create_and_start_execution(
                schedule.workflow_definition_id,
                correlation_id=correlation_id
            )

    async def _execute_task_schedule(self, schedule: JobSchedule, correlation_id: str) -> None:
        """Execute a direct task schedule."""
        if self.task_service and schedule.task_name:
            from app.schemas.task_schemas import TaskSubmissionRequest, TaskPriority

            task_request = TaskSubmissionRequest(
                task_name=schedule.task_name,
                kwargs=schedule.task_configuration or {},
                priority=TaskPriority.STANDARD,
                queue_name=schedule.task_queue,
                correlation_id=correlation_id
            )

            await self.task_service.submit_task(task_request)

    async def _clear_cache_entry(self, cache_key: str, delay_seconds: int) -> None:
        """Clear cache entry after delay."""
        await asyncio.sleep(delay_seconds)
        self._conflict_cache.pop(cache_key, None)

    # Abstract method implementations from BaseService
    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate schedule creation data."""
        return await self._validate_schedule_data(JobScheduleCreate(**data))

    async def validate_update_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate schedule update data."""
        return data  # Basic validation, detailed validation in update_schedule method