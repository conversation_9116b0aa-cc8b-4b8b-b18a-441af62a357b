"""
Unit tests for infrastructure optimization repositories.

Tests for Task 3.2.3 infrastructure optimization repositories including:
- CacheConfigurationRepository CRUD operations and queries
- BackgroundTaskRepository task management and status updates
- PerformanceMonitoringRepository query tracking and analytics
- CacheMetricsRepository and TaskMetricsRepository performance analytics
- Repository integration with database and error handling

Implements comprehensive test coverage with >80% requirement for
production-grade infrastructure optimization functionality.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock

from app.repositories.infrastructure_optimization_repositories import (
    CacheConfigurationRepository, BackgroundTaskRepository,
    PerformanceMonitoringRepository, CacheMetricsRepository, TaskMetricsRepository
)
from app.models.infrastructure_optimization import (
    CacheConfiguration, BackgroundTask, PerformanceMonitoring,
    CacheMetrics, TaskMetrics, CacheType, TaskStatus, TaskPriority
)
from app.schemas.infrastructure_optimization import (
    CacheConfigurationCreate, BackgroundTaskCreate, PerformanceMonitoringCreate,
    CacheMetricsCreate, TaskMetricsCreate
)


@pytest.fixture
def mock_session():
    """Mock database session."""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.add = MagicMock()
    session.refresh = AsyncMock()
    return session


@pytest.fixture
def cache_config_repo(mock_session):
    """Cache configuration repository fixture."""
    return CacheConfigurationRepository(mock_session)


@pytest.fixture
def background_task_repo(mock_session):
    """Background task repository fixture."""
    return BackgroundTaskRepository(mock_session)


@pytest.fixture
def performance_monitoring_repo(mock_session):
    """Performance monitoring repository fixture."""
    return PerformanceMonitoringRepository(mock_session)


@pytest.fixture
def cache_metrics_repo(mock_session):
    """Cache metrics repository fixture."""
    return CacheMetricsRepository(mock_session)


@pytest.fixture
def task_metrics_repo(mock_session):
    """Task metrics repository fixture."""
    return TaskMetricsRepository(mock_session)


class TestCacheConfigurationRepository:
    """Test CacheConfigurationRepository."""

    @pytest.mark.asyncio
    async def test_get_by_cache_type_found(self, cache_config_repo, mock_session):
        """Test getting cache configuration by type when found."""
        # Mock database result
        mock_config = CacheConfiguration(
            cache_type=CacheType.OPTIMIZATION_SCORE,
            cache_key_pattern="opt:{service_id}",
            ttl_seconds=86400,
            is_active=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_config
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_config_repo.get_by_cache_type(CacheType.OPTIMIZATION_SCORE)
        
        # Assertions
        assert result == mock_config
        assert result.cache_type == CacheType.OPTIMIZATION_SCORE
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_cache_type_not_found(self, cache_config_repo, mock_session):
        """Test getting cache configuration by type when not found."""
        # Mock database result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_config_repo.get_by_cache_type(CacheType.OPTIMIZATION_SCORE)
        
        # Assertions
        assert result is None
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_configurations(self, cache_config_repo, mock_session):
        """Test getting all active cache configurations."""
        # Mock database result
        mock_configs = [
            CacheConfiguration(
                cache_type=CacheType.OPTIMIZATION_SCORE,
                cache_key_pattern="opt:{service_id}",
                ttl_seconds=86400,
                is_active=True
            ),
            CacheConfiguration(
                cache_type=CacheType.MARKET_INTELLIGENCE,
                cache_key_pattern="market:{category}",
                ttl_seconds=3600,
                is_active=True
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_configs
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_config_repo.get_active_configurations()
        
        # Assertions
        assert len(result) == 2
        assert all(config.is_active for config in result)
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_ttl_success(self, cache_config_repo, mock_session):
        """Test updating TTL successfully."""
        # Mock database result
        mock_config = CacheConfiguration(
            cache_type=CacheType.OPTIMIZATION_SCORE,
            cache_key_pattern="opt:{service_id}",
            ttl_seconds=7200,  # Updated value
            is_active=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_config
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_config_repo.update_ttl(CacheType.OPTIMIZATION_SCORE, 7200)
        
        # Assertions
        assert result == mock_config
        assert result.ttl_seconds == 7200
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_ttl_not_found(self, cache_config_repo, mock_session):
        """Test updating TTL when configuration not found."""
        # Mock database result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_config_repo.update_ttl(CacheType.OPTIMIZATION_SCORE, 7200)
        
        # Assertions
        assert result is None
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()


class TestBackgroundTaskRepository:
    """Test BackgroundTaskRepository."""

    @pytest.mark.asyncio
    async def test_get_by_task_id_found(self, background_task_repo, mock_session):
        """Test getting background task by task ID when found."""
        # Mock database result
        mock_task = BackgroundTask(
            task_id="celery-123",
            task_name="test.task",
            task_type="test",
            status=TaskStatus.PENDING
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_task
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await background_task_repo.get_by_task_id("celery-123")
        
        # Assertions
        assert result == mock_task
        assert result.task_id == "celery-123"
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_status(self, background_task_repo, mock_session):
        """Test getting background tasks by status."""
        # Mock database result
        mock_tasks = [
            BackgroundTask(
                task_id="celery-123",
                task_name="test.task1",
                task_type="test",
                status=TaskStatus.PENDING
            ),
            BackgroundTask(
                task_id="celery-456",
                task_name="test.task2",
                task_type="test",
                status=TaskStatus.PENDING
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_tasks
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await background_task_repo.get_by_status(TaskStatus.PENDING, limit=100)
        
        # Assertions
        assert len(result) == 2
        assert all(task.status == TaskStatus.PENDING for task in result)
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_pending_tasks_with_priority(self, background_task_repo, mock_session):
        """Test getting pending tasks with priority filter."""
        # Mock database result
        mock_tasks = [
            BackgroundTask(
                task_id="celery-123",
                task_name="test.task",
                task_type="test",
                status=TaskStatus.PENDING,
                priority=TaskPriority.HIGH
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_tasks
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await background_task_repo.get_pending_tasks(priority=TaskPriority.HIGH)
        
        # Assertions
        assert len(result) == 1
        assert result[0].priority == TaskPriority.HIGH
        assert result[0].status == TaskStatus.PENDING
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_task_status_success(self, background_task_repo, mock_session):
        """Test updating task status successfully."""
        # Mock database result
        mock_task = BackgroundTask(
            task_id="celery-123",
            task_name="test.task",
            task_type="test",
            status=TaskStatus.SUCCESS,
            worker_name="worker-01"
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_task
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await background_task_repo.update_task_status(
            "celery-123",
            TaskStatus.SUCCESS,
            worker_name="worker-01",
            result_data={"score": 85.5}
        )
        
        # Assertions
        assert result == mock_task
        assert result.status == TaskStatus.SUCCESS
        assert result.worker_name == "worker-01"
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_task_statistics(self, background_task_repo, mock_session):
        """Test getting task statistics."""
        # Mock database results
        mock_status_result = MagicMock()
        mock_status_result.status = TaskStatus.SUCCESS
        mock_status_result.count = 10
        
        mock_exec_time_result = MagicMock()
        mock_exec_time_result.scalar.return_value = 120.5
        
        # Configure mock session to return different results for different queries
        mock_session.execute.side_effect = [
            MagicMock(fetchall=lambda: [mock_status_result]),
            mock_exec_time_result
        ]
        
        # Test
        result = await background_task_repo.get_task_statistics(24)
        
        # Assertions
        assert "period_hours" in result
        assert "status_counts" in result
        assert "avg_execution_time_seconds" in result
        assert "total_tasks" in result
        assert "success_rate" in result
        assert result["period_hours"] == 24


class TestPerformanceMonitoringRepository:
    """Test PerformanceMonitoringRepository."""

    @pytest.mark.asyncio
    async def test_get_slow_queries(self, performance_monitoring_repo, mock_session):
        """Test getting slow queries."""
        # Mock database result
        mock_queries = [
            PerformanceMonitoring(
                query_type="slow_query",
                query_hash="abc123",
                execution_time_ms=2500.0,
                rows_examined=10000,
                rows_returned=100
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_queries
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await performance_monitoring_repo.get_slow_queries(threshold_ms=1000.0, limit=50)
        
        # Assertions
        assert len(result) == 1
        assert result[0].execution_time_ms == 2500.0
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_query_performance_stats(self, performance_monitoring_repo, mock_session):
        """Test getting query performance statistics."""
        # Mock database result
        mock_row = MagicMock()
        mock_row.total_queries = 100
        mock_row.avg_execution_time = 250.5
        mock_row.max_execution_time = 1000.0
        mock_row.min_execution_time = 50.0
        mock_row.avg_rows_examined = 500.0
        mock_row.avg_rows_returned = 50.0
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await performance_monitoring_repo.get_query_performance_stats("test_query", 24)
        
        # Assertions
        assert result["query_type"] == "test_query"
        assert result["total_queries"] == 100
        assert result["avg_execution_time_ms"] == 250.5
        assert result["efficiency_ratio"] == 10.0  # (50/500) * 100
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_record_query_performance(self, performance_monitoring_repo, mock_session):
        """Test recording query performance."""
        # Mock performance data
        performance_data = PerformanceMonitoringCreate(
            query_type="test_query",
            query_hash="abc123",
            execution_time_ms=500.0,
            rows_examined=100,
            rows_returned=10
        )
        
        # Mock database object
        mock_db_obj = PerformanceMonitoring(**performance_data.model_dump(), is_slow_query=False)
        
        # Test
        result = await performance_monitoring_repo.record_query_performance(performance_data)
        
        # Assertions
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()


class TestCacheMetricsRepository:
    """Test CacheMetricsRepository."""

    @pytest.mark.asyncio
    async def test_get_latest_metrics(self, cache_metrics_repo, mock_session):
        """Test getting latest cache metrics."""
        config_id = uuid4()
        
        # Mock database result
        mock_metrics = CacheMetrics(
            configuration_id=config_id,
            cache_hits=850,
            cache_misses=150,
            memory_usage_mb=256.5,
            period_start=datetime.utcnow() - timedelta(hours=1),
            period_end=datetime.utcnow()
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_metrics
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_metrics_repo.get_latest_metrics(config_id)
        
        # Assertions
        assert result == mock_metrics
        assert result.configuration_id == config_id
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_metrics_summary(self, cache_metrics_repo, mock_session):
        """Test getting cache metrics summary."""
        # Mock database result
        mock_row = MagicMock()
        mock_row.avg_hit_rate = 85.5
        mock_row.total_hits = 8500
        mock_row.total_misses = 1500
        mock_row.avg_memory_usage = 256.5
        mock_row.avg_ops_per_second = 100.0
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await cache_metrics_repo.get_metrics_summary(24)
        
        # Assertions
        assert result["period_hours"] == 24
        assert result["avg_hit_rate"] == 85.5
        assert result["total_operations"] == 10000  # 8500 + 1500
        assert result["avg_memory_usage_mb"] == 256.5
        mock_session.execute.assert_called_once()


class TestTaskMetricsRepository:
    """Test TaskMetricsRepository."""

    @pytest.mark.asyncio
    async def test_get_task_performance_summary(self, task_metrics_repo, mock_session):
        """Test getting task performance summary."""
        # Mock database result
        mock_row = MagicMock()
        mock_row.avg_execution_time = 120.5
        mock_row.avg_wait_time = 5.2
        mock_row.avg_memory_usage = 512.0
        mock_row.avg_cpu_usage = 75.0
        mock_row.avg_success_rate = 97.5
        mock_row.avg_throughput = 50.0
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await task_metrics_repo.get_task_performance_summary(24)
        
        # Assertions
        assert result["period_hours"] == 24
        assert result["avg_execution_time_seconds"] == 120.5
        assert result["avg_wait_time_seconds"] == 5.2
        assert result["avg_memory_usage_mb"] == 512.0
        assert result["avg_cpu_usage_percent"] == 75.0
        assert result["avg_success_rate_percent"] == 97.5
        assert result["avg_throughput_items_per_second"] == 50.0
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_task_performance_summary_no_data(self, task_metrics_repo, mock_session):
        """Test getting task performance summary with no data."""
        # Mock database result with no data
        mock_result = MagicMock()
        mock_result.first.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Test
        result = await task_metrics_repo.get_task_performance_summary(24)
        
        # Assertions
        assert result["period_hours"] == 24
        assert result["avg_execution_time_seconds"] == 0.0
        assert result["avg_success_rate_percent"] == 100.0  # Default success rate
        mock_session.execute.assert_called_once()


class TestRepositoryErrorHandling:
    """Test repository error handling."""

    @pytest.mark.asyncio
    async def test_cache_config_repo_exception_handling(self, cache_config_repo, mock_session):
        """Test cache configuration repository exception handling."""
        # Mock database exception
        mock_session.execute.side_effect = Exception("Database error")
        
        # Test that exception is raised
        with pytest.raises(Exception) as exc_info:
            await cache_config_repo.get_by_cache_type(CacheType.OPTIMIZATION_SCORE)
        
        assert "Database error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_background_task_repo_rollback_on_error(self, background_task_repo, mock_session):
        """Test background task repository rollback on error."""
        # Mock database exception during update
        mock_session.execute.side_effect = Exception("Update failed")
        
        # Test that exception is raised and rollback is called
        with pytest.raises(Exception):
            await background_task_repo.update_task_status("celery-123", TaskStatus.SUCCESS)
        
        mock_session.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_performance_monitoring_repo_rollback_on_error(self, performance_monitoring_repo, mock_session):
        """Test performance monitoring repository rollback on error."""
        # Mock database exception during record creation
        mock_session.commit.side_effect = Exception("Commit failed")
        
        performance_data = PerformanceMonitoringCreate(
            query_type="test_query",
            query_hash="abc123",
            execution_time_ms=500.0
        )
        
        # Test that exception is raised and rollback is called
        with pytest.raises(Exception):
            await performance_monitoring_repo.record_query_performance(performance_data)
        
        mock_session.rollback.assert_called_once()
