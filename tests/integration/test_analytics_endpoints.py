"""
Integration tests for Analytics API endpoints.

This module provides comprehensive integration tests for analytics endpoints:
- User analytics endpoints testing
- Vendor analytics endpoints testing
- Dashboard widget endpoints testing
- Performance metrics endpoints testing
- Cross-system integration validation

Implements >80% test coverage with pytest-asyncio patterns and database rollback.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from httpx import AsyncClient
from fastapi import status

from app.main import app
from app.models.analytics_models import (
    UserAnalytics, VendorAnalytics, DashboardWidget,
    AnalyticsTimeframe, PerformanceMetricType
)
from tests.conftest import TestingSessionLocal, override_get_db


class TestAnalyticsEndpointsIntegration:
    """Integration tests for analytics API endpoints."""

    @pytest.mark.asyncio
    async def test_create_user_analytics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful user analytics creation via API."""
        # Arrange
        analytics_data = {
            "user_id": str(uuid4()),
            "timeframe": "daily",
            "total_bookings": 5,
            "total_spent": "250.00",
            "avg_booking_value": "50.00",
            "favorite_categories": ["tours", "experiences"],
            "booking_frequency": "1.2",
            "satisfaction_score": "4.5",
            "period_start": "2025-01-30T00:00:00Z",
            "period_end": "2025-01-31T00:00:00Z"
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/analytics/user",
            json=analytics_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["user_id"] == analytics_data["user_id"]
        assert response_data["total_bookings"] == analytics_data["total_bookings"]
        assert response_data["timeframe"] == analytics_data["timeframe"]
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_user_analytics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful user analytics retrieval."""
        # Arrange - First create analytics
        user_id = str(uuid4())
        analytics_data = {
            "user_id": user_id,
            "timeframe": "weekly",
            "total_bookings": 10,
            "total_spent": "500.00",
            "period_start": "2025-01-24T00:00:00Z",
            "period_end": "2025-01-31T00:00:00Z"
        }
        
        await client.post(
            "/api/v1/analytics/user",
            json=analytics_data,
            headers=auth_headers
        )
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            f"/api/v1/analytics/user/{user_id}?timeframe=weekly",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)
        if response_data:  # If analytics exist
            analytics = response_data[0]
            assert analytics["user_id"] == user_id
            assert analytics["timeframe"] == "weekly"

    @pytest.mark.asyncio
    async def test_create_vendor_analytics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful vendor analytics creation via API."""
        # Arrange
        analytics_data = {
            "vendor_id": str(uuid4()),
            "timeframe": "monthly",
            "total_bookings": 50,
            "total_revenue": "2500.00",
            "avg_booking_value": "50.00",
            "booking_conversion_rate": "0.25",
            "customer_satisfaction": "4.3",
            "response_time_avg": "120.5",
            "cancellation_rate": "0.05",
            "period_start": "2025-01-01T00:00:00Z",
            "period_end": "2025-01-31T23:59:59Z"
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/analytics/vendor",
            json=analytics_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["vendor_id"] == analytics_data["vendor_id"]
        assert response_data["total_revenue"] == analytics_data["total_revenue"]
        assert response_data["timeframe"] == analytics_data["timeframe"]
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_vendor_analytics_summary_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful vendor analytics summary retrieval."""
        # Arrange
        vendor_id = str(uuid4())
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            f"/api/v1/analytics/vendor/{vendor_id}/summary?timeframe=monthly",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert "total_bookings" in response_data
        assert "total_revenue" in response_data
        assert "avg_booking_value" in response_data
        assert "conversion_rate" in response_data

    @pytest.mark.asyncio
    async def test_create_dashboard_widget_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful dashboard widget creation via API."""
        # Arrange
        widget_data = {
            "user_id": str(uuid4()),
            "widget_type": "revenue_chart",
            "title": "Monthly Revenue",
            "configuration": {
                "chart_type": "line",
                "period": "monthly",
                "metrics": ["revenue", "bookings"]
            },
            "position_x": 0,
            "position_y": 0,
            "width": 6,
            "height": 4,
            "is_active": True
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/analytics/dashboard/widgets",
            json=widget_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["widget_type"] == widget_data["widget_type"]
        assert response_data["title"] == widget_data["title"]
        assert response_data["position_x"] == widget_data["position_x"]
        assert response_data["is_active"] == widget_data["is_active"]
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_user_dashboard_widgets_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful user dashboard widgets retrieval."""
        # Arrange - First create a widget
        user_id = str(uuid4())
        widget_data = {
            "user_id": user_id,
            "widget_type": "booking_stats",
            "title": "Booking Statistics",
            "configuration": {"chart_type": "bar"},
            "position_x": 0,
            "position_y": 0,
            "width": 4,
            "height": 4,
            "is_active": True
        }
        
        await client.post(
            "/api/v1/analytics/dashboard/widgets",
            json=widget_data,
            headers=auth_headers
        )
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            f"/api/v1/analytics/dashboard/widgets?user_id={user_id}",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)
        if response_data:  # If widgets exist
            widget = response_data[0]
            assert widget["user_id"] == user_id
            assert widget["is_active"] is True

    @pytest.mark.asyncio
    async def test_update_dashboard_widget_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful dashboard widget update."""
        # Arrange - First create a widget
        widget_data = {
            "user_id": str(uuid4()),
            "widget_type": "performance_chart",
            "title": "Performance Chart",
            "configuration": {"chart_type": "area"},
            "position_x": 0,
            "position_y": 0,
            "width": 6,
            "height": 4,
            "is_active": True
        }
        
        create_response = await client.post(
            "/api/v1/analytics/dashboard/widgets",
            json=widget_data,
            headers=auth_headers
        )
        widget_id = create_response.json()["id"]
        
        # Update data
        update_data = {
            "title": "Updated Performance Chart",
            "position_x": 6,
            "width": 8
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.put(
            f"/api/v1/analytics/dashboard/widgets/{widget_id}",
            json=update_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.5  # <500ms for PUT operations
        
        response_data = response.json()
        assert response_data["title"] == update_data["title"]
        assert response_data["position_x"] == update_data["position_x"]
        assert response_data["width"] == update_data["width"]

    @pytest.mark.asyncio
    async def test_delete_dashboard_widget_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful dashboard widget deletion."""
        # Arrange - First create a widget
        widget_data = {
            "user_id": str(uuid4()),
            "widget_type": "test_widget",
            "title": "Test Widget",
            "configuration": {},
            "position_x": 0,
            "position_y": 0,
            "width": 4,
            "height": 4,
            "is_active": True
        }
        
        create_response = await client.post(
            "/api/v1/analytics/dashboard/widgets",
            json=widget_data,
            headers=auth_headers
        )
        widget_id = create_response.json()["id"]
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.delete(
            f"/api/v1/analytics/dashboard/widgets/{widget_id}",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert (end_time - start_time) < 0.5  # <500ms for DELETE operations

    @pytest.mark.asyncio
    async def test_get_analytics_dashboard_data_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful analytics dashboard data retrieval."""
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/analytics/dashboard",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert "total_users" in response_data
        assert "total_vendors" in response_data
        assert "total_bookings" in response_data
        assert "total_revenue" in response_data
        assert "recent_analytics" in response_data

    @pytest.mark.asyncio
    async def test_get_performance_metrics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful performance metrics retrieval."""
        # Act
        response = await client.get(
            "/api/v1/analytics/performance/metrics?component=booking_service&metric_type=api_response_time&hours=24",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert "avg_value" in response_data
        assert "max_value" in response_data
        assert "min_value" in response_data
        assert "total_count" in response_data

    @pytest.mark.asyncio
    async def test_analytics_validation_errors(self, client: AsyncClient, auth_headers: dict):
        """Test analytics endpoints with validation errors."""
        # Test invalid user analytics data
        invalid_user_data = {
            "user_id": "invalid-uuid",
            "total_bookings": -1,  # Invalid negative value
            "timeframe": "invalid_timeframe"
        }
        
        response = await client.post(
            "/api/v1/analytics/user",
            json=invalid_user_data,
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Test invalid vendor analytics data
        invalid_vendor_data = {
            "vendor_id": str(uuid4()),
            "total_revenue": "-100.00",  # Invalid negative revenue
            "booking_conversion_rate": "1.5"  # Invalid rate > 1
        }
        
        response = await client.post(
            "/api/v1/analytics/vendor",
            json=invalid_vendor_data,
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Test invalid widget data
        invalid_widget_data = {
            "user_id": str(uuid4()),
            "widget_type": "invalid_type",
            "position_x": -1,  # Invalid negative position
            "width": 0  # Invalid zero width
        }
        
        response = await client.post(
            "/api/v1/analytics/dashboard/widgets",
            json=invalid_widget_data,
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_analytics_unauthorized_access(self, client: AsyncClient):
        """Test analytics endpoints without authentication."""
        # Test user analytics endpoint without auth
        response = await client.get("/api/v1/analytics/user/123")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Test vendor analytics endpoint without auth
        response = await client.post("/api/v1/analytics/vendor", json={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Test dashboard endpoint without auth
        response = await client.get("/api/v1/analytics/dashboard")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_analytics_performance_targets(self, client: AsyncClient, auth_headers: dict):
        """Test that analytics endpoints meet performance targets."""
        # Test user analytics creation performance
        user_data = {
            "user_id": str(uuid4()),
            "timeframe": "daily",
            "total_bookings": 3,
            "total_spent": "150.00"
        }
        
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/analytics/user",
            json=user_data,
            headers=auth_headers
        )
        creation_time = asyncio.get_event_loop().time() - start_time
        
        assert response.status_code == status.HTTP_201_CREATED
        assert creation_time < 0.5  # <500ms target
        
        # Test dashboard data retrieval performance
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/analytics/dashboard",
            headers=auth_headers
        )
        retrieval_time = asyncio.get_event_loop().time() - start_time
        
        assert response.status_code == status.HTTP_200_OK
        assert retrieval_time < 0.2  # <200ms target

    @pytest.mark.asyncio
    async def test_analytics_cross_system_integration(self, client: AsyncClient, auth_headers: dict):
        """Test analytics integration with other systems."""
        # This test would verify that analytics endpoints properly integrate
        # with booking system, vendor management, and other components
        
        # Test analytics data consistency with booking data
        user_id = str(uuid4())
        
        # Create user analytics
        analytics_response = await client.post(
            "/api/v1/analytics/user",
            json={
                "user_id": user_id,
                "timeframe": "daily",
                "total_bookings": 2,
                "total_spent": "100.00"
            },
            headers=auth_headers
        )
        
        assert analytics_response.status_code == status.HTTP_201_CREATED
        
        # Verify analytics data can be retrieved
        get_response = await client.get(
            f"/api/v1/analytics/user/{user_id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == status.HTTP_200_OK
        analytics_data = get_response.json()
        
        # Verify data consistency
        if analytics_data:
            assert analytics_data[0]["user_id"] == user_id
            assert analytics_data[0]["total_bookings"] == 2
