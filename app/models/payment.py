"""
Payment models for Culture Connect Backend API.

This module defines comprehensive payment-related models for transaction processing,
payment methods, and financial management following the Payment & Transaction Management System.

Implements Phase 1 of Payment & Transaction Management System with:
- Core payment transaction models with multi-provider support
- Payment method management with encrypted storage
- Comprehensive audit logging and security features
- Integration with existing User, Vendor, and Booking models
- Performance-optimized database design with proper indexing

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Numeric, CheckConstraint, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit
from app.core.payment.config import PaymentProviderType


class PaymentStatus(str, Enum):
    """Payment status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"
    DISPUTED = "disputed"
    EXPIRED = "expired"


class PaymentMethodType(str, Enum):
    """Payment method type enumeration."""
    CARD = "card"
    BANK_TRANSFER = "bank_transfer"
    MOBILE_MONEY = "mobile_money"
    USSD = "ussd"
    CRYPTO = "crypto"
    WALLET = "wallet"


class TransactionType(str, Enum):
    """Transaction type enumeration."""
    CHARGE = "charge"
    REFUND = "refund"
    PAYOUT = "payout"
    FEE = "fee"
    ADJUSTMENT = "adjustment"
    CHARGEBACK = "chargeback"
    REVERSAL = "reversal"


class Payment(BaseModelWithAudit):
    """
    Primary payment transaction model for Culture Connect Backend API.

    This model stores comprehensive payment information including multi-provider
    support, security features, and integration with booking workflow.

    Features:
    - Multi-provider support (Stripe, Paystack, Busha)
    - Encrypted sensitive data storage
    - Comprehensive audit logging
    - Performance-optimized indexing
    - Integration with booking and user models
    """

    __tablename__ = "payments"

    # Core payment information
    booking_id = Column(
        Integer,
        ForeignKey("bookings.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated booking ID"
    )

    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Customer user ID"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Vendor receiving payment"
    )

    # Financial details
    amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Payment amount"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Payment currency (ISO 4217)"
    )

    # Payment method and provider
    payment_method_id = Column(
        Integer,
        ForeignKey("payment_methods.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated payment method"
    )

    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=False,
        index=True,
        doc="Payment provider (stripe, paystack, busha)"
    )

    # Status and tracking
    status = Column(
        SQLEnum(PaymentStatus),
        nullable=False,
        default=PaymentStatus.PENDING,
        index=True,
        doc="Payment processing status"
    )

    # Provider-specific identifiers
    payment_intent_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider payment intent ID"
    )

    transaction_reference = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique transaction reference"
    )

    provider_reference = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider transaction reference"
    )

    # Payment URLs and client secrets
    payment_url = Column(
        String(500),
        nullable=True,
        doc="Payment authorization URL"
    )

    client_secret = Column(
        Text,
        nullable=True,
        doc="Client secret for payment confirmation (encrypted)"
    )

    # Timing and expiration
    expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Payment expiration timestamp"
    )

    paid_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Payment completion timestamp"
    )

    # Fee and commission tracking
    platform_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform commission fee"
    )

    provider_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Payment provider fee"
    )

    net_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Net amount after fees"
    )

    # Failure and error tracking
    failure_reason = Column(
        String(255),
        nullable=True,
        doc="Payment failure reason"
    )

    failure_code = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Provider failure code"
    )

    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of retry attempts"
    )

    # Metadata and additional information
    payment_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional payment metadata (encrypted)"
    )

    provider_response = Column(
        Text,
        nullable=True,
        doc="Provider response data (encrypted)"
    )

    # Customer information
    customer_ip = Column(
        String(45),
        nullable=True,
        doc="Customer IP address"
    )

    customer_user_agent = Column(
        String(500),
        nullable=True,
        doc="Customer user agent"
    )

    # Geo-location information
    country_code = Column(
        String(2),
        nullable=True,
        index=True,
        doc="Payment country code"
    )

    # Relationships
    booking = relationship(
        "Booking",
        back_populates="payments",
        doc="Associated booking"
    )

    user = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="payments",
        doc="Customer user"
    )

    vendor = relationship(
        "Vendor",
        back_populates="payments",
        doc="Vendor receiving payment"
    )

    payment_method = relationship(
        "PaymentMethod",
        back_populates="payments",
        doc="Payment method used"
    )

    transactions = relationship(
        "Transaction",
        back_populates="payment",
        cascade="all, delete-orphan",
        order_by="Transaction.created_at.desc()",
        doc="Associated transactions"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "amount > 0",
            name="check_payment_amount_positive"
        ),
        CheckConstraint(
            "platform_fee >= 0",
            name="check_platform_fee_non_negative"
        ),
        CheckConstraint(
            "provider_fee >= 0",
            name="check_provider_fee_non_negative"
        ),
        CheckConstraint(
            "net_amount >= 0",
            name="check_net_amount_non_negative"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="check_retry_count_non_negative"
        ),

        # Performance indexes
        Index("idx_payment_booking_status", "booking_id", "status"),
        Index("idx_payment_user_status", "user_id", "status"),
        Index("idx_payment_vendor_status", "vendor_id", "status"),
        Index("idx_payment_provider_status", "provider", "status"),
        Index("idx_payment_currency_amount", "currency", "amount"),
        Index("idx_payment_date_status", "created_at", "status"),
        Index("idx_payment_paid_date", "paid_at", "status"),
        Index("idx_payment_country_provider", "country_code", "provider"),
        Index("idx_payment_reference_provider", "transaction_reference", "provider"),
    )

    def __repr__(self) -> str:
        """String representation of the payment."""
        return f"<Payment(id={self.id}, ref='{self.transaction_reference}', status='{self.status}')>"

    @property
    def is_successful(self) -> bool:
        """Check if payment is successful."""
        return self.status == PaymentStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """Check if payment failed."""
        return self.status in {PaymentStatus.FAILED, PaymentStatus.CANCELLED, PaymentStatus.EXPIRED}

    @property
    def is_refundable(self) -> bool:
        """Check if payment can be refunded."""
        return self.status == PaymentStatus.COMPLETED and self.paid_at is not None

    @property
    def is_expired(self) -> bool:
        """Check if payment is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at.replace(tzinfo=None)

    @property
    def total_fees(self) -> Decimal:
        """Calculate total fees."""
        return self.platform_fee + self.provider_fee


class PaymentMethod(BaseModelWithAudit):
    """
    Payment method model for user payment preferences and provider integration.

    This model stores user payment methods with encrypted sensitive data,
    provider integration, and comprehensive security features.

    Features:
    - Multi-provider support with encrypted storage
    - Default payment method management
    - Verification status tracking
    - Security and compliance features
    """

    __tablename__ = "payment_methods"

    # Core information
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated user ID"
    )

    # Payment method details
    type = Column(
        SQLEnum(PaymentMethodType),
        nullable=False,
        index=True,
        doc="Payment method type"
    )

    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=False,
        index=True,
        doc="Payment provider"
    )

    # Provider-specific information
    provider_method_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider payment method ID"
    )

    # Display information (non-sensitive)
    display_name = Column(
        String(100),
        nullable=True,
        doc="User-friendly display name"
    )

    last_four = Column(
        String(4),
        nullable=True,
        doc="Last four digits for display"
    )

    brand = Column(
        String(50),
        nullable=True,
        doc="Card brand or method brand"
    )

    expiry_month = Column(
        Integer,
        nullable=True,
        doc="Expiry month (for cards)"
    )

    expiry_year = Column(
        Integer,
        nullable=True,
        doc="Expiry year (for cards)"
    )

    # Status and preferences
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Default payment method for user"
    )

    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Payment method is active"
    )

    is_verified = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Payment method is verified"
    )

    # Security and metadata
    encrypted_metadata = Column(
        Text,
        nullable=True,
        doc="Encrypted payment method metadata"
    )

    verification_data = Column(
        Text,
        nullable=True,
        doc="Encrypted verification data"
    )

    # Usage tracking
    last_used_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Last usage timestamp"
    )

    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times used"
    )

    # Failure tracking
    failure_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of failed attempts"
    )

    last_failure_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last failure timestamp"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="payment_methods",
        foreign_keys=[user_id],
        doc="Associated user"
    )

    payments = relationship(
        "Payment",
        back_populates="payment_method",
        doc="Payments using this method"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "usage_count >= 0",
            name="check_usage_count_non_negative"
        ),
        CheckConstraint(
            "failure_count >= 0",
            name="check_failure_count_non_negative"
        ),
        CheckConstraint(
            "expiry_month IS NULL OR (expiry_month >= 1 AND expiry_month <= 12)",
            name="check_expiry_month_range"
        ),
        CheckConstraint(
            "expiry_year IS NULL OR expiry_year >= 2020",
            name="check_expiry_year_valid"
        ),

        # Performance indexes
        Index("idx_payment_method_user_type", "user_id", "type"),
        Index("idx_payment_method_user_default", "user_id", "is_default"),
        Index("idx_payment_method_provider_active", "provider", "is_active"),
        Index("idx_payment_method_user_active", "user_id", "is_active"),
        Index("idx_payment_method_last_used", "last_used_at", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of the payment method."""
        return f"<PaymentMethod(id={self.id}, type='{self.type}', provider='{self.provider}')>"

    @property
    def is_expired(self) -> bool:
        """Check if payment method is expired (for cards)."""
        if not self.expiry_month or not self.expiry_year:
            return False

        current_date = datetime.utcnow()
        expiry_date = datetime(self.expiry_year, self.expiry_month, 1)

        return current_date > expiry_date

    @property
    def masked_display(self) -> str:
        """Get masked display string."""
        if self.last_four:
            return f"****{self.last_four}"
        return f"{self.type.title()} - {self.brand or 'Unknown'}"
