"""
Workflow management models for Culture Connect Backend API.

This module provides comprehensive workflow and job orchestration models including:
- WorkflowDefinition: Workflow templates and configurations
- WorkflowExecution: Workflow instance executions with state tracking
- JobDependency: Job dependency relationships and orchestration
- WorkflowStep: Individual workflow steps with execution tracking
- JobSchedule: Advanced scheduling configurations with cron support
- WorkflowAlert: Monitoring and alerting configurations

Implements Task 6.2.2 requirements for advanced job scheduling and workflow
orchestration with production-grade PostgreSQL optimization and performance.
"""

import enum
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    Boolean, Column, DateTime, Enum, ForeignKey, Index, Integer,
    JSON, Numeric, String, Text, CheckConstraint, UniqueConstraint
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.database import Base


class WorkflowStatus(str, enum.Enum):
    """Workflow definition status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"
    DEPRECATED = "deprecated"


class ExecutionStatus(str, enum.Enum):
    """Workflow execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"
    RETRYING = "retrying"


class DependencyType(str, enum.Enum):
    """Job dependency type enumeration."""
    SUCCESS = "success"  # Depends on successful completion
    COMPLETION = "completion"  # Depends on any completion (success/failure)
    FAILURE = "failure"  # Depends on failure
    CONDITIONAL = "conditional"  # Conditional dependency with custom logic


class StepStatus(str, enum.Enum):
    """Workflow step status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class AlertSeverity(str, enum.Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertChannel(str, enum.Enum):
    """Alert notification channels."""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    PUSH = "push"


class ScheduleType(str, enum.Enum):
    """Schedule type enumeration for advanced scheduling."""
    CRON = "cron"
    INTERVAL = "interval"
    NAMED = "named"
    ONCE = "once"


class NamedSchedule(str, enum.Enum):
    """Named schedule patterns for common scheduling needs."""
    YEARLY = "@yearly"
    ANNUALLY = "@annually"
    MONTHLY = "@monthly"
    WEEKLY = "@weekly"
    DAILY = "@daily"
    HOURLY = "@hourly"
    MINUTELY = "@minutely"
    REBOOT = "@reboot"


class HolidayCalendar(str, enum.Enum):
    """Holiday calendar types for business day scheduling."""
    US = "US"
    UK = "UK"
    EU = "EU"
    CA = "CA"
    AU = "AU"
    JP = "JP"
    CUSTOM = "CUSTOM"


class MonitoringStatus(str, enum.Enum):
    """Monitoring status enumeration for enhanced monitoring."""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"
    ERROR = "error"


class AlertDeliveryStatus(str, enum.Enum):
    """Alert delivery status enumeration."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"


class WorkflowDefinition(Base):
    """
    Workflow definition model for template and configuration management.

    Stores workflow templates with configuration, scheduling, and metadata
    for reusable workflow orchestration patterns.
    """
    __tablename__ = "workflow_definitions"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version: Mapped[str] = mapped_column(String(50), nullable=False, default="1.0.0")
    status: Mapped[WorkflowStatus] = mapped_column(
        Enum(WorkflowStatus),
        nullable=False,
        default=WorkflowStatus.DRAFT
    )

    # Configuration and metadata
    configuration: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    tags: Mapped[List[str]] = mapped_column(
        postgresql.ARRAY(String(100)),
        nullable=True,
        default=list
    )
    workflow_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Ownership and permissions
    created_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True
    )
    team_id: Mapped[Optional[UUID]] = mapped_column(
        postgresql.UUID(as_uuid=True),
        nullable=True
    )

    # Performance and limits
    max_execution_time: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # seconds
    max_retries: Mapped[int] = mapped_column(Integer, nullable=False, default=3)
    retry_delay: Mapped[int] = mapped_column(Integer, nullable=False, default=60)  # seconds

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    executions: Mapped[List["WorkflowExecution"]] = relationship(
        "WorkflowExecution",
        back_populates="workflow_definition",
        cascade="all, delete-orphan"
    )
    steps: Mapped[List["WorkflowStep"]] = relationship(
        "WorkflowStep",
        back_populates="workflow_definition",
        cascade="all, delete-orphan",
        order_by="WorkflowStep.step_order"
    )
    schedules: Mapped[List["JobSchedule"]] = relationship(
        "JobSchedule",
        back_populates="workflow_definition",
        cascade="all, delete-orphan"
    )
    alerts: Mapped[List["WorkflowAlert"]] = relationship(
        "WorkflowAlert",
        back_populates="workflow_definition",
        cascade="all, delete-orphan"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('max_execution_time > 0', name='positive_max_execution_time'),
        CheckConstraint('max_retries >= 0', name='non_negative_max_retries'),
        CheckConstraint('retry_delay >= 0', name='non_negative_retry_delay'),
        UniqueConstraint('name', 'version', name='unique_workflow_name_version'),
        Index('idx_workflow_def_status', 'status'),
        Index('idx_workflow_def_created_by', 'created_by'),
        Index('idx_workflow_def_team', 'team_id'),
        Index('idx_workflow_def_tags', 'tags', postgresql_using='gin'),
        Index('idx_workflow_def_created_at', 'created_at'),
    )


class WorkflowExecution(Base):
    """
    Workflow execution model for tracking workflow instance runs.

    Tracks individual workflow executions with state, timing, results,
    and performance metrics for monitoring and analytics.
    """
    __tablename__ = "workflow_executions"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_definition_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_definitions.id", ondelete="CASCADE"),
        nullable=False
    )

    # Execution state and tracking
    status: Mapped[ExecutionStatus] = mapped_column(
        Enum(ExecutionStatus),
        nullable=False,
        default=ExecutionStatus.PENDING
    )
    execution_context: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    input_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    output_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    error_details: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True)

    # Timing and performance
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    duration_seconds: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True
    )

    # Retry and recovery
    retry_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_retry_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Execution metadata
    triggered_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # schedule, manual, api
    trigger_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    correlation_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Performance metrics
    steps_total: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    steps_completed: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    steps_failed: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_definition: Mapped["WorkflowDefinition"] = relationship(
        "WorkflowDefinition",
        back_populates="executions"
    )
    step_executions: Mapped[List["WorkflowStepExecution"]] = relationship(
        "WorkflowStepExecution",
        back_populates="workflow_execution",
        cascade="all, delete-orphan"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('retry_count >= 0', name='non_negative_retry_count'),
        CheckConstraint('steps_total >= 0', name='non_negative_steps_total'),
        CheckConstraint('steps_completed >= 0', name='non_negative_steps_completed'),
        CheckConstraint('steps_failed >= 0', name='non_negative_steps_failed'),
        CheckConstraint('duration_seconds >= 0', name='non_negative_duration'),
        Index('idx_workflow_exec_definition', 'workflow_definition_id'),
        Index('idx_workflow_exec_status', 'status'),
        Index('idx_workflow_exec_started_at', 'started_at'),
        Index('idx_workflow_exec_correlation', 'correlation_id'),
        Index('idx_workflow_exec_triggered_by', 'triggered_by'),
        Index('idx_workflow_exec_created_at', 'created_at'),
    )


class JobDependency(Base):
    """
    Job dependency model for workflow orchestration and dependency management.

    Defines dependencies between jobs/tasks with support for various dependency
    types and conditional logic for complex workflow orchestration.
    """
    __tablename__ = "job_dependencies"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )

    # Dependency relationship
    parent_job_id: Mapped[str] = mapped_column(String(255), nullable=False)  # Celery task ID
    child_job_id: Mapped[str] = mapped_column(String(255), nullable=False)   # Celery task ID
    dependency_type: Mapped[DependencyType] = mapped_column(
        Enum(DependencyType),
        nullable=False,
        default=DependencyType.SUCCESS
    )

    # Workflow context
    workflow_execution_id: Mapped[Optional[UUID]] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_executions.id", ondelete="CASCADE"),
        nullable=True
    )

    # Conditional logic
    condition_expression: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    condition_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timing and constraints
    timeout_seconds: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    max_wait_time: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Status tracking
    is_satisfied: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    satisfied_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    evaluation_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_evaluation_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Metadata
    dependency_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_execution: Mapped[Optional["WorkflowExecution"]] = relationship(
        "WorkflowExecution",
        foreign_keys=[workflow_execution_id]
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('timeout_seconds > 0', name='positive_timeout_seconds'),
        CheckConstraint('max_wait_time > 0', name='positive_max_wait_time'),
        CheckConstraint('evaluation_count >= 0', name='non_negative_evaluation_count'),
        UniqueConstraint('parent_job_id', 'child_job_id', name='unique_job_dependency'),
        Index('idx_job_dep_parent', 'parent_job_id'),
        Index('idx_job_dep_child', 'child_job_id'),
        Index('idx_job_dep_workflow', 'workflow_execution_id'),
        Index('idx_job_dep_type', 'dependency_type'),
        Index('idx_job_dep_satisfied', 'is_satisfied'),
        Index('idx_job_dep_created_at', 'created_at'),
    )


class WorkflowStep(Base):
    """
    Workflow step model for individual workflow step definitions.

    Defines individual steps within a workflow with configuration,
    ordering, and execution parameters for workflow orchestration.
    """
    __tablename__ = "workflow_steps"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_definition_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_definitions.id", ondelete="CASCADE"),
        nullable=False
    )

    # Step identification and ordering
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    step_order: Mapped[int] = mapped_column(Integer, nullable=False)

    # Task configuration
    task_name: Mapped[str] = mapped_column(String(255), nullable=False)  # Celery task name
    task_configuration: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    task_queue: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    task_priority: Mapped[int] = mapped_column(Integer, nullable=False, default=5)

    # Execution parameters
    timeout_seconds: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    max_retries: Mapped[int] = mapped_column(Integer, nullable=False, default=3)
    retry_delay: Mapped[int] = mapped_column(Integer, nullable=False, default=60)

    # Conditional execution
    condition_expression: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    skip_on_failure: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    continue_on_failure: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    # Dependencies within workflow
    depends_on_steps: Mapped[List[str]] = mapped_column(
        postgresql.ARRAY(String(255)),
        nullable=True,
        default=list
    )

    # Metadata
    tags: Mapped[List[str]] = mapped_column(
        postgresql.ARRAY(String(100)),
        nullable=True,
        default=list
    )
    step_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_definition: Mapped["WorkflowDefinition"] = relationship(
        "WorkflowDefinition",
        back_populates="steps"
    )
    step_executions: Mapped[List["WorkflowStepExecution"]] = relationship(
        "WorkflowStepExecution",
        back_populates="workflow_step",
        cascade="all, delete-orphan"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('step_order >= 0', name='non_negative_step_order'),
        CheckConstraint('timeout_seconds > 0', name='positive_timeout_seconds'),
        CheckConstraint('max_retries >= 0', name='non_negative_max_retries'),
        CheckConstraint('retry_delay >= 0', name='non_negative_retry_delay'),
        CheckConstraint('task_priority >= 0 AND task_priority <= 10', name='valid_task_priority'),
        UniqueConstraint('workflow_definition_id', 'step_order', name='unique_workflow_step_order'),
        UniqueConstraint('workflow_definition_id', 'name', name='unique_workflow_step_name'),
        Index('idx_workflow_step_definition', 'workflow_definition_id'),
        Index('idx_workflow_step_order', 'step_order'),
        Index('idx_workflow_step_task_name', 'task_name'),
        Index('idx_workflow_step_tags', 'tags', postgresql_using='gin'),
        Index('idx_workflow_step_created_at', 'created_at'),
    )


class WorkflowStepExecution(Base):
    """
    Workflow step execution model for tracking individual step runs.

    Tracks execution of individual workflow steps with timing, status,
    and result data for monitoring and debugging workflow executions.
    """
    __tablename__ = "workflow_step_executions"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_execution_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_executions.id", ondelete="CASCADE"),
        nullable=False
    )
    workflow_step_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_steps.id", ondelete="CASCADE"),
        nullable=False
    )

    # Execution tracking
    status: Mapped[StepStatus] = mapped_column(
        Enum(StepStatus),
        nullable=False,
        default=StepStatus.PENDING
    )
    celery_task_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Timing
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    duration_seconds: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), nullable=True)

    # Data and results
    input_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    output_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    error_details: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True)

    # Retry tracking
    retry_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_retry_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_execution: Mapped["WorkflowExecution"] = relationship(
        "WorkflowExecution",
        back_populates="step_executions"
    )
    workflow_step: Mapped["WorkflowStep"] = relationship(
        "WorkflowStep",
        back_populates="step_executions"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('retry_count >= 0', name='non_negative_retry_count'),
        CheckConstraint('duration_seconds >= 0', name='non_negative_duration'),
        UniqueConstraint('workflow_execution_id', 'workflow_step_id', name='unique_step_execution'),
        Index('idx_step_exec_workflow', 'workflow_execution_id'),
        Index('idx_step_exec_step', 'workflow_step_id'),
        Index('idx_step_exec_status', 'status'),
        Index('idx_step_exec_celery_task', 'celery_task_id'),
        Index('idx_step_exec_started_at', 'started_at'),
        Index('idx_step_exec_created_at', 'created_at'),
    )


class JobSchedule(Base):
    """
    Job schedule model for advanced scheduling configurations.

    Supports cron-like scheduling with timezone support, recurrence patterns,
    and advanced scheduling features for workflow automation.

    Enhanced for Task 6.2.2 Phase 3 with:
    - Advanced cron expression support (seconds, years, named schedules)
    - Comprehensive timezone handling with DST support
    - Business day and holiday calendar integration
    - Schedule conflict detection and resolution
    - Execution history and missed run handling
    - Performance optimization for <100ms schedule calculation
    """
    __tablename__ = "job_schedules"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_definition_id: Mapped[Optional[UUID]] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_definitions.id", ondelete="CASCADE"),
        nullable=True
    )

    # Schedule identification
    name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Enhanced scheduling configuration
    cron_expression: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    named_schedule: Mapped[Optional[NamedSchedule]] = mapped_column(Enum(NamedSchedule), nullable=True)
    timezone: Mapped[str] = mapped_column(String(100), nullable=False, default="UTC")
    interval_seconds: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Advanced scheduling features
    schedule_type: Mapped[ScheduleType] = mapped_column(Enum(ScheduleType), nullable=False, default=ScheduleType.CRON)
    business_days_only: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    exclude_holidays: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    holiday_calendar: Mapped[Optional[HolidayCalendar]] = mapped_column(Enum(HolidayCalendar), nullable=True)

    # Schedule constraints
    start_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    max_runs: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Status and control
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    last_run_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    next_run_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    run_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Execution tracking
    success_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    failure_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_success_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    last_failure_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Conflict resolution and priority
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=5)  # 1-10 scale
    allow_overlap: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    max_concurrent_runs: Mapped[int] = mapped_column(Integer, nullable=False, default=1)

    # Missed execution handling
    catchup_missed_runs: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    max_catchup_runs: Mapped[int] = mapped_column(Integer, nullable=False, default=3)

    # Performance and monitoring
    average_duration_seconds: Mapped[Optional[float]] = mapped_column(Numeric(10, 3), nullable=True)
    last_duration_seconds: Mapped[Optional[float]] = mapped_column(Numeric(10, 3), nullable=True)

    # Metadata and configuration
    schedule_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)

    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        onupdate=func.now()
    )
    created_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )
    updated_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )

    # Relationships
    workflow_definition: Mapped[Optional["WorkflowDefinition"]] = relationship(
        "WorkflowDefinition",
        back_populates="schedules"
    )

    # Database constraints and indexes
    __table_args__ = (
        # Check constraints for data validation
        CheckConstraint('priority >= 1 AND priority <= 10', name='valid_priority_range'),
        CheckConstraint('max_concurrent_runs >= 1', name='positive_max_concurrent_runs'),
        CheckConstraint('max_catchup_runs >= 0', name='non_negative_max_catchup_runs'),
        CheckConstraint('run_count >= 0', name='non_negative_run_count'),
        CheckConstraint('success_count >= 0', name='non_negative_success_count'),
        CheckConstraint('failure_count >= 0', name='non_negative_failure_count'),
        CheckConstraint(
            '(cron_expression IS NOT NULL) OR (interval_seconds IS NOT NULL) OR (named_schedule IS NOT NULL)',
            name='schedule_configuration_required'
        ),
        CheckConstraint(
            'start_date IS NULL OR end_date IS NULL OR start_date < end_date',
            name='valid_date_range'
        ),

        # Performance indexes for schedule calculation (<100ms target)
        Index('idx_schedule_active_next_run', 'is_active', 'next_run_at'),
        Index('idx_schedule_workflow_active', 'workflow_definition_id', 'is_active'),
        Index('idx_schedule_type_active', 'schedule_type', 'is_active'),
        Index('idx_schedule_timezone', 'timezone'),
        Index('idx_schedule_priority', 'priority', 'is_active'),
        Index('idx_schedule_name_unique', 'name'),
        Index('idx_schedule_created_at', 'created_at'),
        Index('idx_schedule_last_run', 'last_run_at'),
        Index('idx_schedule_business_days', 'business_days_only', 'exclude_holidays'),

        # Composite indexes for complex queries
        Index('idx_schedule_execution_tracking', 'is_active', 'next_run_at', 'priority'),
        Index('idx_schedule_conflict_resolution', 'allow_overlap', 'max_concurrent_runs', 'priority'),
    )


class WorkflowAlert(Base):
    """
    Workflow alert model for monitoring and alerting configurations.

    Defines alert conditions, notification channels, and escalation rules
    for comprehensive workflow monitoring and incident management.
    """
    __tablename__ = "workflow_alerts"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_definition_id: Mapped[Optional[UUID]] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_definitions.id", ondelete="CASCADE"),
        nullable=True
    )

    # Alert identification
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    severity: Mapped[AlertSeverity] = mapped_column(
        Enum(AlertSeverity),
        nullable=False,
        default=AlertSeverity.MEDIUM
    )

    # Alert conditions
    condition_type: Mapped[str] = mapped_column(String(100), nullable=False)  # failure, timeout, performance
    condition_expression: Mapped[str] = mapped_column(Text, nullable=False)
    condition_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Notification configuration
    notification_channels: Mapped[List[str]] = mapped_column(
        postgresql.ARRAY(String(50)),
        nullable=False,
        default=list
    )
    notification_config: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Escalation rules
    escalation_delay_minutes: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    escalation_channels: Mapped[List[str]] = mapped_column(
        postgresql.ARRAY(String(50)),
        nullable=True,
        default=list
    )
    max_escalations: Mapped[int] = mapped_column(Integer, nullable=False, default=3)

    # Status and control
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    is_muted: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    muted_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Alert statistics
    trigger_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_triggered_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    last_escalated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Configuration
    alert_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    alert_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_definition: Mapped[Optional["WorkflowDefinition"]] = relationship(
        "WorkflowDefinition",
        back_populates="alerts"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('escalation_delay_minutes > 0', name='positive_escalation_delay'),
        CheckConstraint('max_escalations > 0', name='positive_max_escalations'),
        CheckConstraint('trigger_count >= 0', name='non_negative_trigger_count'),
        Index('idx_workflow_alert_definition', 'workflow_definition_id'),
        Index('idx_workflow_alert_severity', 'severity'),
        Index('idx_workflow_alert_active', 'is_active'),
        Index('idx_workflow_alert_condition_type', 'condition_type'),
        Index('idx_workflow_alert_channels', 'notification_channels', postgresql_using='gin'),
        Index('idx_workflow_alert_created_at', 'created_at'),
    )


class JobMonitoringSession(Base):
    """
    Job monitoring session model for real-time job tracking.

    Tracks active monitoring sessions for workflow executions with
    real-time status updates, performance metrics, and heartbeat monitoring.
    Enhanced for Task 6.2.2 Phase 4 monitoring capabilities.
    """
    __tablename__ = "job_monitoring_sessions"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    workflow_execution_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_executions.id", ondelete="CASCADE"),
        nullable=False
    )

    # Monitoring configuration
    monitoring_status: Mapped[MonitoringStatus] = mapped_column(
        Enum(MonitoringStatus),
        nullable=False,
        default=MonitoringStatus.ACTIVE
    )
    heartbeat_interval_seconds: Mapped[int] = mapped_column(Integer, nullable=False, default=30)
    metrics_collection_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)

    # Real-time status tracking
    current_step: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    progress_percentage: Mapped[Decimal] = mapped_column(
        Numeric(5, 2),
        nullable=False,
        default=0.0
    )
    estimated_completion: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Performance metrics
    cpu_usage_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2), nullable=True)
    memory_usage_mb: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)
    execution_latency_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), nullable=True)

    # Monitoring timestamps
    last_heartbeat: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    monitoring_started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    monitoring_ended_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Metadata
    monitoring_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    workflow_execution: Mapped["WorkflowExecution"] = relationship(
        "WorkflowExecution",
        foreign_keys=[workflow_execution_id]
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('heartbeat_interval_seconds > 0', name='positive_heartbeat_interval'),
        CheckConstraint('progress_percentage >= 0 AND progress_percentage <= 100', name='valid_progress_percentage'),
        CheckConstraint('cpu_usage_percent >= 0 AND cpu_usage_percent <= 100', name='valid_cpu_usage'),
        CheckConstraint('memory_usage_mb >= 0', name='non_negative_memory_usage'),
        CheckConstraint('execution_latency_ms >= 0', name='non_negative_execution_latency'),
        UniqueConstraint('workflow_execution_id', name='unique_monitoring_session'),
        Index('idx_monitoring_session_execution', 'workflow_execution_id'),
        Index('idx_monitoring_session_status', 'monitoring_status'),
        Index('idx_monitoring_session_heartbeat', 'last_heartbeat'),
        Index('idx_monitoring_session_progress', 'progress_percentage'),
        Index('idx_monitoring_session_created_at', 'created_at'),
    )


class AlertTriggerEvent(Base):
    """
    Alert trigger event model for tracking alert activations.

    Records individual alert trigger events with delivery tracking,
    escalation management, and resolution status for comprehensive
    alerting system monitoring.
    """
    __tablename__ = "alert_trigger_events"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    alert_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_alerts.id", ondelete="CASCADE"),
        nullable=False
    )
    workflow_execution_id: Mapped[Optional[UUID]] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("workflow_executions.id", ondelete="CASCADE"),
        nullable=True
    )

    # Trigger details
    trigger_condition: Mapped[str] = mapped_column(String(255), nullable=False)
    trigger_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    threshold_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    severity: Mapped[AlertSeverity] = mapped_column(Enum(AlertSeverity), nullable=False)

    # Alert message and context
    alert_message: Mapped[str] = mapped_column(Text, nullable=False)
    alert_context: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Status and resolution
    is_resolved: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    resolution_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Escalation tracking
    escalation_level: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    escalated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    max_escalation_reached: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    # Delivery tracking
    notification_attempts: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    successful_deliveries: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    failed_deliveries: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Timestamps
    triggered_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    alert: Mapped["WorkflowAlert"] = relationship(
        "WorkflowAlert",
        foreign_keys=[alert_id]
    )
    workflow_execution: Mapped[Optional["WorkflowExecution"]] = relationship(
        "WorkflowExecution",
        foreign_keys=[workflow_execution_id]
    )
    delivery_statuses: Mapped[List["AlertDeliveryRecord"]] = relationship(
        "AlertDeliveryRecord",
        back_populates="trigger_event",
        cascade="all, delete-orphan"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('escalation_level >= 0', name='non_negative_escalation_level'),
        CheckConstraint('notification_attempts >= 0', name='non_negative_notification_attempts'),
        CheckConstraint('successful_deliveries >= 0', name='non_negative_successful_deliveries'),
        CheckConstraint('failed_deliveries >= 0', name='non_negative_failed_deliveries'),
        CheckConstraint('successful_deliveries + failed_deliveries <= notification_attempts', name='valid_delivery_counts'),
        Index('idx_alert_trigger_alert', 'alert_id'),
        Index('idx_alert_trigger_execution', 'workflow_execution_id'),
        Index('idx_alert_trigger_severity', 'severity'),
        Index('idx_alert_trigger_resolved', 'is_resolved'),
        Index('idx_alert_trigger_escalation', 'escalation_level'),
        Index('idx_alert_trigger_triggered_at', 'triggered_at'),
        Index('idx_alert_trigger_created_at', 'created_at'),
    )


class AlertDeliveryRecord(Base):
    """
    Alert delivery record model for tracking notification delivery status.

    Tracks individual notification delivery attempts across different channels
    with retry logic, delivery confirmation, and failure analysis for
    comprehensive alerting system reliability monitoring.
    """
    __tablename__ = "alert_delivery_records"

    id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    trigger_event_id: Mapped[UUID] = mapped_column(
        postgresql.UUID(as_uuid=True),
        ForeignKey("alert_trigger_events.id", ondelete="CASCADE"),
        nullable=False
    )

    # Delivery configuration
    notification_channel: Mapped[str] = mapped_column(String(50), nullable=False)  # email, slack, webhook, sms, push
    recipient_address: Mapped[str] = mapped_column(String(500), nullable=False)  # email, phone, webhook URL, etc.

    # Delivery status tracking
    delivery_status: Mapped[AlertDeliveryStatus] = mapped_column(
        Enum(AlertDeliveryStatus),
        nullable=False,
        default=AlertDeliveryStatus.PENDING
    )
    attempt_count: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    max_retry_attempts: Mapped[int] = mapped_column(Integer, nullable=False, default=3)

    # External delivery tracking
    external_delivery_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    external_message_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Delivery timing
    scheduled_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    delivered_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    failed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    next_retry_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Delivery response and error tracking
    delivery_response: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_code: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Performance metrics
    delivery_latency_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), nullable=True)
    processing_time_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), nullable=True)

    # Metadata
    delivery_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now()
    )

    # Relationships
    trigger_event: Mapped["AlertTriggerEvent"] = relationship(
        "AlertTriggerEvent",
        back_populates="delivery_statuses"
    )

    # Constraints and indexes
    __table_args__ = (
        CheckConstraint('attempt_count > 0', name='positive_attempt_count'),
        CheckConstraint('max_retry_attempts >= 0', name='non_negative_max_retry_attempts'),
        CheckConstraint('attempt_count <= max_retry_attempts + 1', name='valid_attempt_count'),
        CheckConstraint('delivery_latency_ms >= 0', name='non_negative_delivery_latency'),
        CheckConstraint('processing_time_ms >= 0', name='non_negative_processing_time'),
        Index('idx_delivery_record_trigger', 'trigger_event_id'),
        Index('idx_delivery_record_channel', 'notification_channel'),
        Index('idx_delivery_record_status', 'delivery_status'),
        Index('idx_delivery_record_scheduled', 'scheduled_at'),
        Index('idx_delivery_record_next_retry', 'next_retry_at'),
        Index('idx_delivery_record_external_id', 'external_delivery_id'),
        Index('idx_delivery_record_created_at', 'created_at'),
    )
