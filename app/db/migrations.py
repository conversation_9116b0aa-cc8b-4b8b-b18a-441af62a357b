"""
Enhanced database migration management for Culture Connect Backend API.

This module provides comprehensive migration utilities including:
- Migration validation and verification
- Rollback procedures with safety checks
- Schema comparison and drift detection
- Data migration helpers
- Migration history tracking
- Backup and restore capabilities
"""

import logging
import os
import subprocess
import tempfile
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

try:
    from alembic.config import Config
    from alembic import command
    from alembic.script import ScriptDirectory
    from alembic.runtime.migration import MigrationContext
    from alembic.runtime.environment import EnvironmentContext
    ALEMBIC_AVAILABLE = True
except ImportError:
    # Alembic not available - create mock classes for testing
    ALEMBIC_AVAILABLE = False
    Config = None
    command = None
    ScriptDirectory = None
    MigrationContext = None
    EnvironmentContext = None

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.engine import Engine

from app.core.config import settings
from app.db.database import sync_engine
from app.db.session import get_async_session_context

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class MigrationInfo:
    """Migration information and metadata."""
    revision: str
    description: str
    branch_labels: Optional[str]
    depends_on: Optional[str]
    created_at: datetime
    is_head: bool
    is_current: bool


@dataclass
class MigrationValidationResult:
    """Migration validation result."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    recommendations: List[str]


@dataclass
class SchemaComparisonResult:
    """Schema comparison result."""
    has_differences: bool
    missing_tables: List[str]
    extra_tables: List[str]
    column_differences: Dict[str, List[str]]
    index_differences: Dict[str, List[str]]
    constraint_differences: Dict[str, List[str]]


class MigrationManager:
    """
    Enhanced migration manager with validation and rollback capabilities.

    Provides comprehensive migration management including validation,
    rollback procedures, schema comparison, and safety checks.
    """

    def __init__(self, alembic_config_path: str = "alembic.ini"):
        self.alembic_config_path = alembic_config_path
        self.alembic_config = Config(alembic_config_path)
        self.script_directory = ScriptDirectory.from_config(self.alembic_config)

    def get_migration_history(self) -> List[MigrationInfo]:
        """
        Get complete migration history.

        Returns:
            List[MigrationInfo]: List of all migrations with metadata
        """
        migrations = []

        try:
            # Get all revisions from script directory
            revisions = list(self.script_directory.walk_revisions())

            # Get current revision from database
            with sync_engine.connect() as connection:
                context = MigrationContext.configure(connection)
                current_rev = context.get_current_revision()

            # Get head revision
            head_rev = self.script_directory.get_current_head()

            for revision in revisions:
                migration_info = MigrationInfo(
                    revision=revision.revision,
                    description=revision.doc or "No description",
                    branch_labels=revision.branch_labels,
                    depends_on=revision.dependencies,
                    created_at=datetime.utcnow(),  # Would need to parse from file
                    is_head=(revision.revision == head_rev),
                    is_current=(revision.revision == current_rev)
                )
                migrations.append(migration_info)

            return migrations

        except Exception as e:
            logger.error(f"Failed to get migration history: {str(e)}")
            return []

    def validate_migration(self, revision: str) -> MigrationValidationResult:
        """
        Validate a specific migration for safety and correctness.

        Args:
            revision: Migration revision to validate

        Returns:
            MigrationValidationResult: Validation results
        """
        errors = []
        warnings = []
        recommendations = []

        try:
            # Get migration script
            script = self.script_directory.get_revision(revision)
            if not script:
                errors.append(f"Migration {revision} not found")
                return MigrationValidationResult(False, errors, warnings, recommendations)

            # Read migration file content
            migration_file = script.path
            if not os.path.exists(migration_file):
                errors.append(f"Migration file {migration_file} not found")
                return MigrationValidationResult(False, errors, warnings, recommendations)

            with open(migration_file, 'r') as f:
                content = f.read()

            # Check for potentially dangerous operations
            dangerous_operations = [
                ("DROP TABLE", "Contains table drop operations"),
                ("DROP COLUMN", "Contains column drop operations"),
                ("ALTER COLUMN", "Contains column alterations"),
                ("DROP INDEX", "Contains index drop operations"),
                ("TRUNCATE", "Contains truncate operations"),
            ]

            for operation, message in dangerous_operations:
                if operation in content.upper():
                    warnings.append(message)

            # Check for missing rollback operations
            if "def downgrade():" in content:
                downgrade_start = content.find("def downgrade():")
                downgrade_content = content[downgrade_start:]
                if "pass" in downgrade_content and len(downgrade_content.strip()) < 50:
                    warnings.append("Migration has empty or minimal downgrade function")
            else:
                errors.append("Migration missing downgrade function")

            # Check for data migration patterns
            data_migration_patterns = [
                "op.execute(",
                "connection.execute(",
                "session.execute(",
            ]

            for pattern in data_migration_patterns:
                if pattern in content:
                    recommendations.append("Migration contains data operations - ensure proper testing")
                    break

            # Check for foreign key operations
            if "foreign_key" in content.lower() or "fk_" in content:
                recommendations.append("Migration affects foreign keys - verify referential integrity")

            # Check for index operations on large tables
            if "create_index" in content.lower():
                recommendations.append("Migration creates indexes - consider concurrent creation for large tables")

            is_valid = len(errors) == 0

            return MigrationValidationResult(is_valid, errors, warnings, recommendations)

        except Exception as e:
            logger.error(f"Migration validation failed: {str(e)}")
            errors.append(f"Validation error: {str(e)}")
            return MigrationValidationResult(False, errors, warnings, recommendations)

    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """
        Create database backup before migration.

        Args:
            backup_name: Optional backup name

        Returns:
            str: Path to backup file
        """
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.sql"

        backup_path = os.path.join(tempfile.gettempdir(), backup_name)

        try:
            # For PostgreSQL
            if settings.DATABASE_URL.startswith("postgresql"):
                # Extract connection details
                db_url = settings.DATABASE_URL
                # This is a simplified backup - in production, use proper pg_dump
                logger.info(f"Creating backup at {backup_path}")
                # Would implement actual pg_dump here

            # For SQLite
            elif settings.DATABASE_URL.startswith("sqlite"):
                import shutil
                db_file = settings.DATABASE_URL.replace("sqlite:///", "")
                shutil.copy2(db_file, backup_path)

            logger.info(f"Database backup created: {backup_path}")
            return backup_path

        except Exception as e:
            logger.error(f"Backup creation failed: {str(e)}")
            raise

    def upgrade_with_validation(self, revision: str = "head", create_backup: bool = True) -> bool:
        """
        Upgrade database with validation and backup.

        Args:
            revision: Target revision
            create_backup: Whether to create backup before upgrade

        Returns:
            bool: True if upgrade successful
        """
        try:
            # Validate current state
            current_rev = self.get_current_revision()
            logger.info(f"Current revision: {current_rev}")

            # Get target revision
            if revision == "head":
                target_rev = self.script_directory.get_current_head()
            else:
                target_rev = revision

            logger.info(f"Target revision: {target_rev}")

            # Validate target migration
            if target_rev != current_rev:
                validation = self.validate_migration(target_rev)
                if not validation.is_valid:
                    logger.error(f"Migration validation failed: {validation.errors}")
                    return False

                if validation.warnings:
                    logger.warning(f"Migration warnings: {validation.warnings}")

                if validation.recommendations:
                    logger.info(f"Migration recommendations: {validation.recommendations}")

            # Create backup if requested
            backup_path = None
            if create_backup:
                backup_path = self.create_backup()
                logger.info(f"Backup created: {backup_path}")

            # Perform upgrade
            logger.info(f"Upgrading database to {revision}")
            command.upgrade(self.alembic_config, revision)

            # Verify upgrade
            new_rev = self.get_current_revision()
            if new_rev == target_rev:
                logger.info(f"Database upgrade successful: {new_rev}")
                return True
            else:
                logger.error(f"Upgrade verification failed. Expected: {target_rev}, Got: {new_rev}")
                return False

        except Exception as e:
            logger.error(f"Database upgrade failed: {str(e)}")
            return False

    def downgrade_with_validation(self, revision: str, confirm: bool = False) -> bool:
        """
        Downgrade database with safety checks.

        Args:
            revision: Target revision to downgrade to
            confirm: Confirmation flag for safety

        Returns:
            bool: True if downgrade successful
        """
        if not confirm:
            logger.error("Downgrade requires explicit confirmation")
            return False

        try:
            current_rev = self.get_current_revision()
            logger.warning(f"Downgrading from {current_rev} to {revision}")

            # Create backup before downgrade
            backup_path = self.create_backup(f"pre_downgrade_{current_rev}_to_{revision}.sql")
            logger.info(f"Pre-downgrade backup created: {backup_path}")

            # Validate downgrade path
            # This would check if downgrade is safe and possible

            # Perform downgrade
            command.downgrade(self.alembic_config, revision)

            # Verify downgrade
            new_rev = self.get_current_revision()
            if new_rev == revision:
                logger.info(f"Database downgrade successful: {new_rev}")
                return True
            else:
                logger.error(f"Downgrade verification failed. Expected: {revision}, Got: {new_rev}")
                return False

        except Exception as e:
            logger.error(f"Database downgrade failed: {str(e)}")
            return False

    def get_current_revision(self) -> Optional[str]:
        """
        Get current database revision.

        Returns:
            Optional[str]: Current revision or None
        """
        try:
            with sync_engine.connect() as connection:
                context = MigrationContext.configure(connection)
                return context.get_current_revision()
        except Exception as e:
            logger.error(f"Failed to get current revision: {str(e)}")
            return None

    def compare_schema(self, target_metadata) -> SchemaComparisonResult:
        """
        Compare current database schema with target metadata.

        Args:
            target_metadata: SQLAlchemy metadata to compare against

        Returns:
            SchemaComparisonResult: Schema comparison results
        """
        try:
            inspector = inspect(sync_engine)
            current_tables = set(inspector.get_table_names())
            target_tables = set(target_metadata.tables.keys())

            missing_tables = list(target_tables - current_tables)
            extra_tables = list(current_tables - target_tables)

            column_differences = {}
            index_differences = {}
            constraint_differences = {}

            # Compare common tables
            common_tables = current_tables & target_tables
            for table_name in common_tables:
                # Compare columns
                current_columns = {col['name'] for col in inspector.get_columns(table_name)}
                target_columns = set(target_metadata.tables[table_name].columns.keys())

                col_diff = []
                if current_columns != target_columns:
                    missing_cols = target_columns - current_columns
                    extra_cols = current_columns - target_columns
                    if missing_cols:
                        col_diff.append(f"Missing columns: {missing_cols}")
                    if extra_cols:
                        col_diff.append(f"Extra columns: {extra_cols}")

                if col_diff:
                    column_differences[table_name] = col_diff

            has_differences = bool(missing_tables or extra_tables or column_differences)

            return SchemaComparisonResult(
                has_differences=has_differences,
                missing_tables=missing_tables,
                extra_tables=extra_tables,
                column_differences=column_differences,
                index_differences=index_differences,
                constraint_differences=constraint_differences
            )

        except Exception as e:
            logger.error(f"Schema comparison failed: {str(e)}")
            return SchemaComparisonResult(
                has_differences=True,
                missing_tables=[],
                extra_tables=[],
                column_differences={"error": [str(e)]},
                index_differences={},
                constraint_differences={}
            )

    def generate_migration(self, message: str, autogenerate: bool = True) -> Optional[str]:
        """
        Generate new migration with validation.

        Args:
            message: Migration message
            autogenerate: Whether to use autogenerate

        Returns:
            Optional[str]: Generated revision ID
        """
        try:
            logger.info(f"Generating migration: {message}")

            # Generate migration
            if autogenerate:
                revision = command.revision(
                    self.alembic_config,
                    message=message,
                    autogenerate=True
                )
            else:
                revision = command.revision(
                    self.alembic_config,
                    message=message
                )

            # Get the generated revision ID
            head_rev = self.script_directory.get_current_head()
            logger.info(f"Generated migration: {head_rev}")

            # Validate generated migration
            validation = self.validate_migration(head_rev)
            if validation.warnings:
                logger.warning(f"Generated migration has warnings: {validation.warnings}")

            return head_rev

        except Exception as e:
            logger.error(f"Migration generation failed: {str(e)}")
            return None


# Global migration manager instance
migration_manager = MigrationManager()


def run_migrations() -> bool:
    """
    Run database migrations with validation.

    Returns:
        bool: True if migrations successful
    """
    return migration_manager.upgrade_with_validation()


def create_migration(message: str, autogenerate: bool = True) -> Optional[str]:
    """
    Create new migration with validation.

    Args:
        message: Migration message
        autogenerate: Whether to use autogenerate

    Returns:
        Optional[str]: Generated revision ID
    """
    return migration_manager.generate_migration(message, autogenerate)


def rollback_migration(revision: str, confirm: bool = False) -> bool:
    """
    Rollback to specific migration with safety checks.

    Args:
        revision: Target revision
        confirm: Confirmation flag

    Returns:
        bool: True if rollback successful
    """
    return migration_manager.downgrade_with_validation(revision, confirm)


def get_migration_status() -> Dict[str, Any]:
    """
    Get current migration status and information.

    Returns:
        Dict[str, Any]: Migration status information
    """
    try:
        current_rev = migration_manager.get_current_revision()
        head_rev = migration_manager.script_directory.get_current_head()
        history = migration_manager.get_migration_history()

        return {
            "current_revision": current_rev,
            "head_revision": head_rev,
            "is_up_to_date": current_rev == head_rev,
            "migration_count": len(history),
            "pending_migrations": len([m for m in history if not m.is_current]),
            "last_migration": history[0] if history else None
        }

    except Exception as e:
        logger.error(f"Failed to get migration status: {str(e)}")
        return {"error": str(e)}


class DataMigrationHelper:
    """
    Helper class for data migrations with safety checks.

    Provides utilities for safe data transformations during migrations
    including batch processing, progress tracking, and rollback support.
    """

    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size

    async def batch_update(self, table_name: str, update_query: str,
                          where_clause: str = "", params: Optional[Dict] = None) -> int:
        """
        Perform batch updates with progress tracking.

        Args:
            table_name: Name of table to update
            update_query: UPDATE SQL query
            where_clause: WHERE clause for filtering
            params: Query parameters

        Returns:
            int: Number of rows updated
        """
        total_updated = 0
        params = params or {}

        try:
            async with get_async_session_context() as session:
                # Get total count
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                if where_clause:
                    count_query += f" WHERE {where_clause}"

                result = await session.execute(text(count_query), params)
                total_rows = result.scalar()

                logger.info(f"Starting batch update of {total_rows} rows in {table_name}")

                # Process in batches
                offset = 0
                while offset < total_rows:
                    batch_query = f"{update_query} LIMIT {self.batch_size} OFFSET {offset}"
                    if where_clause:
                        batch_query = batch_query.replace("WHERE", f"WHERE {where_clause} AND")

                    result = await session.execute(text(batch_query), params)
                    batch_updated = result.rowcount
                    total_updated += batch_updated

                    await session.commit()

                    offset += self.batch_size
                    progress = (offset / total_rows) * 100
                    logger.info(f"Batch update progress: {progress:.1f}% ({total_updated} rows)")

                logger.info(f"Batch update completed: {total_updated} rows updated")
                return total_updated

        except Exception as e:
            logger.error(f"Batch update failed: {str(e)}")
            raise

    async def safe_column_migration(self, table_name: str, old_column: str,
                                   new_column: str, transform_func: str) -> bool:
        """
        Safely migrate data from old column to new column.

        Args:
            table_name: Name of table
            old_column: Source column name
            new_column: Target column name
            transform_func: SQL transformation function

        Returns:
            bool: True if migration successful
        """
        try:
            async with get_async_session_context() as session:
                # Check if columns exist
                inspector = inspect(sync_engine)
                columns = [col['name'] for col in inspector.get_columns(table_name)]

                if old_column not in columns:
                    logger.error(f"Source column {old_column} not found in {table_name}")
                    return False

                if new_column not in columns:
                    logger.error(f"Target column {new_column} not found in {table_name}")
                    return False

                # Perform migration
                update_query = f"""
                    UPDATE {table_name}
                    SET {new_column} = {transform_func}({old_column})
                    WHERE {old_column} IS NOT NULL AND {new_column} IS NULL
                """

                result = await session.execute(text(update_query))
                rows_updated = result.rowcount
                await session.commit()

                logger.info(f"Column migration completed: {rows_updated} rows migrated")
                return True

        except Exception as e:
            logger.error(f"Column migration failed: {str(e)}")
            return False


# Global data migration helper
data_migration_helper = DataMigrationHelper()


# Backup and restore utility functions

async def create_backup(revision: str) -> str:
    """
    Create database backup for migration safety.

    Args:
        revision: Migration revision identifier

    Returns:
        str: Path to backup file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"backup_{revision}_{timestamp}.sql"
    backup_path = os.path.join(tempfile.gettempdir(), backup_filename)

    try:
        if settings.DATABASE_URL.startswith("postgresql"):
            # PostgreSQL backup using pg_dump
            db_url = settings.DATABASE_URL
            # Parse connection details from URL
            # postgresql://user:password@host:port/database
            import urllib.parse
            parsed = urllib.parse.urlparse(db_url)

            cmd = [
                "pg_dump",
                "-h", parsed.hostname or "localhost",
                "-p", str(parsed.port or 5432),
                "-U", parsed.username or "postgres",
                "-d", parsed.path.lstrip('/') if parsed.path else "postgres",
                "-f", backup_path,
                "--verbose",
                "--no-password"
            ]

            # Set PGPASSWORD environment variable
            env = os.environ.copy()
            if parsed.password:
                env["PGPASSWORD"] = parsed.password

            result = subprocess.run(cmd, env=env, capture_output=True, text=True)

            if result.returncode != 0:
                raise Exception(f"pg_dump failed: {result.stderr}")

        elif settings.DATABASE_URL.startswith("sqlite"):
            # SQLite backup using file copy
            import shutil
            db_file = settings.DATABASE_URL.replace("sqlite:///", "")
            if os.path.exists(db_file):
                shutil.copy2(db_file, backup_path)
            else:
                raise Exception(f"SQLite database file not found: {db_file}")
        else:
            raise Exception(f"Unsupported database type for backup: {settings.DATABASE_URL}")

        logger.info(f"Database backup created successfully: {backup_path}")
        return backup_path

    except Exception as e:
        logger.error(f"Failed to create database backup: {str(e)}")
        raise


async def restore_backup(backup_path: str) -> bool:
    """
    Restore database from backup file.

    Args:
        backup_path: Path to backup file

    Returns:
        bool: True if restore successful
    """
    try:
        if not os.path.exists(backup_path):
            raise Exception(f"Backup file not found: {backup_path}")

        if settings.DATABASE_URL.startswith("postgresql"):
            # PostgreSQL restore using psql
            db_url = settings.DATABASE_URL
            import urllib.parse
            parsed = urllib.parse.urlparse(db_url)

            cmd = [
                "psql",
                "-h", parsed.hostname or "localhost",
                "-p", str(parsed.port or 5432),
                "-U", parsed.username or "postgres",
                "-d", parsed.path.lstrip('/') if parsed.path else "postgres",
                "-f", backup_path,
                "--quiet"
            ]

            # Set PGPASSWORD environment variable
            env = os.environ.copy()
            if parsed.password:
                env["PGPASSWORD"] = parsed.password

            result = subprocess.run(cmd, env=env, capture_output=True, text=True)

            if result.returncode != 0:
                raise Exception(f"psql restore failed: {result.stderr}")

        elif settings.DATABASE_URL.startswith("sqlite"):
            # SQLite restore using file copy
            import shutil
            db_file = settings.DATABASE_URL.replace("sqlite:///", "")
            shutil.copy2(backup_path, db_file)
        else:
            raise Exception(f"Unsupported database type for restore: {settings.DATABASE_URL}")

        logger.info(f"Database restored successfully from: {backup_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to restore database backup: {str(e)}")
        raise


async def validate_migration(revision: str) -> MigrationValidationResult:
    """
    Validate migration with comprehensive checks.

    Args:
        revision: Migration revision to validate

    Returns:
        MigrationValidationResult: Validation result
    """
    return migration_manager.validate_migration(revision)


# Export commonly used items
__all__ = [
    'MigrationInfo',
    'MigrationValidationResult',
    'SchemaComparisonResult',
    'MigrationManager',
    'DataMigrationHelper',
    'migration_manager',
    'data_migration_helper',
    'run_migrations',
    'create_migration',
    'rollback_migration',
    'get_migration_status',
    'create_backup',
    'restore_backup',
    'validate_migration',
]
