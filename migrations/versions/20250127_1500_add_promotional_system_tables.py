"""Add promotional system tables for Phase 5 Promotional & Advertising System

Revision ID: promotional_system_tables
Revises: add_review_management_system_tables_for_phase_4
Create Date: 2025-01-27 15:00:00.000000

This migration creates comprehensive promotional system tables including:
- Campaign management with budget tracking and multi-provider payment integration
- Advertisement management with creative assets and placement optimization
- Performance metrics tracking with <200ms query targets
- Featured placement management for marketplace optimization
- Financial tracking and billing integration with existing payment infrastructure
- PostgreSQL-specific optimizations and enum types

Implements Phase 5 of Promotional & Advertising System with:
- Multi-provider payment support (Paystack, Stripe, Busha)
- Geolocation-based targeting capabilities
- Performance analytics and optimization insights
- A/B testing support for advertisement variations
- Comprehensive audit logging and business rule enforcement
- Performance targets: <200ms promotional queries, <500ms campaign creation

Integration with existing systems:
- BaseModelWithAudit patterns for comprehensive audit logging
- Vendor and Service model relationships for marketplace integration
- Payment system integration for billing and financial tracking
- RBAC integration for vendor-specific campaign access control

TODO Markers for Payment Provider Configurations:
- TODO-PAYSTACK-API-URL: Paystack payment configuration for campaign billing
- TODO-STRIPE-API-URL: Stripe payment configuration for international campaigns
- TODO-BUSHA-API-URL: Busha cryptocurrency payment configuration
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'promotional_system_tables'
down_revision = 'd8b8c4817a77'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema - Add promotional system tables."""

    # Create PostgreSQL enum types for promotional system

    # Campaign status types
    op.execute("""
        CREATE TYPE campaignstatus AS ENUM (
            'draft', 'pending_approval', 'approved', 'active',
            'paused', 'completed', 'cancelled', 'rejected'
        )
    """)

    # Campaign types
    op.execute("""
        CREATE TYPE campaigntype AS ENUM (
            'featured_listing', 'sponsored_search', 'discovery_feed',
            'banner_ad', 'promoted_service', 'category_spotlight'
        )
    """)

    # Campaign objectives
    op.execute("""
        CREATE TYPE campaignobjective AS ENUM (
            'brand_awareness', 'lead_generation', 'booking_conversion',
            'traffic_increase', 'engagement', 'reach'
        )
    """)

    # Bid strategies
    op.execute("""
        CREATE TYPE bidstrategy AS ENUM (
            'manual_cpc', 'auto_cpc', 'target_cpa',
            'target_roas', 'maximize_clicks', 'maximize_conversions'
        )
    """)

    # Advertisement formats
    op.execute("""
        CREATE TYPE adformat AS ENUM (
            'image', 'video', 'carousel', 'text', 'rich_media', 'interactive'
        )
    """)

    # Advertisement status types
    op.execute("""
        CREATE TYPE adstatus AS ENUM (
            'draft', 'pending_review', 'approved', 'active',
            'paused', 'rejected', 'expired'
        )
    """)

    # Placement types
    op.execute("""
        CREATE TYPE placementtype AS ENUM (
            'homepage_hero', 'category_top', 'search_results',
            'service_detail', 'vendor_profile', 'mobile_banner', 'email_newsletter'
        )
    """)

    # Placement status types
    op.execute("""
        CREATE TYPE placementstatus AS ENUM (
            'available', 'reserved', 'occupied', 'maintenance'
        )
    """)

    # Spend categories
    op.execute("""
        CREATE TYPE spendcategory AS ENUM (
            'campaign_budget', 'placement_fee', 'creative_production',
            'targeting_premium', 'performance_bonus', 'platform_fee'
        )
    """)

    # 1. Create campaigns table
    op.create_table(
        'campaigns',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),

        # Core campaign information
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('campaign_type', postgresql.ENUM('featured_listing', 'sponsored_search', 'discovery_feed', 'banner_ad', 'promoted_service', 'category_spotlight', name='campaigntype'), nullable=False),
        sa.Column('campaign_objective', postgresql.ENUM('brand_awareness', 'lead_generation', 'booking_conversion', 'traffic_increase', 'engagement', 'reach', name='campaignobjective'), nullable=False),
        sa.Column('status', postgresql.ENUM('draft', 'pending_approval', 'approved', 'active', 'paused', 'completed', 'cancelled', 'rejected', name='campaignstatus'), nullable=False, default='draft'),

        # Budget and financial tracking
        sa.Column('total_budget', sa.Numeric(12, 2), nullable=False),
        sa.Column('daily_budget', sa.Numeric(10, 2), nullable=True),
        sa.Column('spent_amount', sa.Numeric(12, 2), nullable=False, default=0.00),
        sa.Column('remaining_budget', sa.Numeric(12, 2), nullable=False),

        # Payment integration
        # TODO-PAYSTACK-API-URL: Paystack payment configuration for campaign billing
        # TODO-STRIPE-API-URL: Stripe payment configuration for international campaigns
        # TODO-BUSHA-API-URL: Busha cryptocurrency payment configuration
        sa.Column('payment_provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=True),
        sa.Column('payment_method_id', sa.String(255), nullable=True),

        # Bidding and optimization
        sa.Column('bid_strategy', postgresql.ENUM('manual_cpc', 'auto_cpc', 'target_cpa', 'target_roas', 'maximize_clicks', 'maximize_conversions', name='bidstrategy'), nullable=False, default='manual_cpc'),
        sa.Column('target_cpc', sa.Numeric(8, 2), nullable=True),
        sa.Column('target_cpa', sa.Numeric(10, 2), nullable=True),
        sa.Column('target_roas', sa.Numeric(5, 2), nullable=True),

        # Scheduling and duration
        sa.Column('start_date', sa.Date(), nullable=True),
        sa.Column('end_date', sa.Date(), nullable=True),
        sa.Column('timezone', sa.String(50), nullable=False, default='Africa/Lagos'),

        # Targeting configuration (JSONB for fast queries)
        sa.Column('targeting_config', postgresql.JSONB(), nullable=True),
        sa.Column('geographic_targeting', postgresql.JSONB(), nullable=True),
        sa.Column('demographic_targeting', postgresql.JSONB(), nullable=True),

        # Performance tracking
        sa.Column('impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('conversions', sa.Integer(), nullable=False, default=0),
        sa.Column('revenue_generated', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Approval and review
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('approved_by', sa.Integer(), nullable=True),
        sa.Column('rejection_reason', sa.Text(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints for business rules
        sa.CheckConstraint('total_budget >= 1000.00', name='check_campaign_min_budget'),
        sa.CheckConstraint('total_budget <= 10000000.00', name='check_campaign_max_budget'),
        sa.CheckConstraint('daily_budget IS NULL OR daily_budget >= 100.00', name='check_campaign_min_daily_budget'),
        sa.CheckConstraint('spent_amount >= 0.00', name='check_campaign_spent_amount_positive'),
        sa.CheckConstraint('remaining_budget >= 0.00', name='check_campaign_remaining_budget_positive'),
        sa.CheckConstraint('target_cpc IS NULL OR target_cpc >= 1.00', name='check_campaign_min_cpc'),
        sa.CheckConstraint('target_cpa IS NULL OR target_cpa >= 10.00', name='check_campaign_min_cpa'),
        sa.CheckConstraint('target_roas IS NULL OR target_roas >= 1.00', name='check_campaign_min_roas'),
        sa.CheckConstraint('end_date IS NULL OR start_date IS NULL OR end_date >= start_date', name='check_campaign_date_order'),
        sa.CheckConstraint('impressions >= 0', name='check_campaign_impressions_positive'),
        sa.CheckConstraint('clicks >= 0', name='check_campaign_clicks_positive'),
        sa.CheckConstraint('conversions >= 0', name='check_campaign_conversions_positive'),
        sa.CheckConstraint('revenue_generated >= 0.00', name='check_campaign_revenue_positive'),
    )

    # 2. Create advertisements table
    op.create_table(
        'advertisements',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),

        # Core advertisement information
        sa.Column('campaign_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('ad_format', postgresql.ENUM('image', 'video', 'carousel', 'text', 'rich_media', 'interactive', name='adformat'), nullable=False),
        sa.Column('status', postgresql.ENUM('draft', 'pending_review', 'approved', 'active', 'paused', 'rejected', 'expired', name='adstatus'), nullable=False, default='draft'),

        # Creative content
        sa.Column('headline', sa.String(100), nullable=False),
        sa.Column('description_text', sa.String(500), nullable=True),
        sa.Column('call_to_action', sa.String(50), nullable=False),

        # Media assets
        sa.Column('image_url', sa.String(500), nullable=True),
        sa.Column('video_url', sa.String(500), nullable=True),
        sa.Column('thumbnail_url', sa.String(500), nullable=True),
        sa.Column('media_assets', postgresql.JSONB(), nullable=True),

        # Targeting and placement
        sa.Column('target_url', sa.String(500), nullable=False),
        sa.Column('placement_preferences', postgresql.JSONB(), nullable=True),
        sa.Column('targeting_override', postgresql.JSONB(), nullable=True),

        # Performance tracking
        sa.Column('impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('conversions', sa.Integer(), nullable=False, default=0),
        sa.Column('spend_amount', sa.Numeric(10, 2), nullable=False, default=0.00),

        # A/B testing
        sa.Column('variant_group', sa.String(100), nullable=True),
        sa.Column('variant_name', sa.String(100), nullable=True),
        sa.Column('test_percentage', sa.Numeric(5, 2), nullable=True),

        # Approval and review
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('approved_by', sa.Integer(), nullable=True),
        sa.Column('rejection_reason', sa.Text(), nullable=True),

        # Scheduling
        sa.Column('start_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints for business rules
        sa.CheckConstraint('LENGTH(headline) >= 10', name='check_ad_headline_min_length'),
        sa.CheckConstraint('LENGTH(headline) <= 100', name='check_ad_headline_max_length'),
        sa.CheckConstraint('description_text IS NULL OR LENGTH(description_text) <= 500', name='check_ad_description_max_length'),
        sa.CheckConstraint('LENGTH(call_to_action) >= 3', name='check_ad_cta_min_length'),
        sa.CheckConstraint('impressions >= 0', name='check_ad_impressions_positive'),
        sa.CheckConstraint('clicks >= 0', name='check_ad_clicks_positive'),
        sa.CheckConstraint('conversions >= 0', name='check_ad_conversions_positive'),
        sa.CheckConstraint('spend_amount >= 0.00', name='check_ad_spend_positive'),
        sa.CheckConstraint('test_percentage IS NULL OR (test_percentage >= 0.00 AND test_percentage <= 100.00)', name='check_ad_test_percentage_range'),
        sa.CheckConstraint('end_date IS NULL OR start_date IS NULL OR end_date >= start_date', name='check_ad_date_order'),
    )

    # 3. Create campaign_metrics table
    op.create_table(
        'campaign_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),

        # Core metrics information
        sa.Column('campaign_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('hour', sa.Integer(), nullable=True),

        # Performance metrics
        sa.Column('impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('conversions', sa.Integer(), nullable=False, default=0),
        sa.Column('spend_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('revenue_generated', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Calculated metrics
        sa.Column('click_through_rate', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('conversion_rate', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('cost_per_click', sa.Numeric(8, 2), nullable=False, default=0.00),
        sa.Column('cost_per_acquisition', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('return_on_ad_spend', sa.Numeric(8, 2), nullable=False, default=0.00),

        # Quality metrics
        sa.Column('quality_score', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('relevance_score', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('landing_page_score', sa.Numeric(5, 2), nullable=False, default=0.00),

        # Engagement metrics
        sa.Column('unique_clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('bounce_rate', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('average_session_duration', sa.Numeric(8, 2), nullable=False, default=0.00),
        sa.Column('pages_per_session', sa.Numeric(5, 2), nullable=False, default=0.00),

        # Device breakdown
        sa.Column('desktop_impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('mobile_impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('tablet_impressions', sa.Integer(), nullable=False, default=0),

        # Geographic performance and optimization
        sa.Column('top_performing_locations', postgresql.JSONB(), nullable=True),
        sa.Column('optimization_score', sa.Numeric(5, 2), nullable=False, default=0.00),
        sa.Column('recommendations', postgresql.JSONB(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('campaign_id', 'date', 'hour', name='uq_campaign_metrics_period'),

        # Check constraints for business rules
        sa.CheckConstraint('hour IS NULL OR (hour >= 0 AND hour <= 23)', name='check_metrics_hour_range'),
        sa.CheckConstraint('impressions >= 0', name='check_metrics_impressions_positive'),
        sa.CheckConstraint('clicks >= 0', name='check_metrics_clicks_positive'),
        sa.CheckConstraint('conversions >= 0', name='check_metrics_conversions_positive'),
        sa.CheckConstraint('spend_amount >= 0.00', name='check_metrics_spend_positive'),
        sa.CheckConstraint('revenue_generated >= 0.00', name='check_metrics_revenue_positive'),
        sa.CheckConstraint('click_through_rate >= 0.00 AND click_through_rate <= 100.00', name='check_metrics_ctr_range'),
        sa.CheckConstraint('conversion_rate >= 0.00 AND conversion_rate <= 100.00', name='check_metrics_cvr_range'),
        sa.CheckConstraint('quality_score >= 0.00 AND quality_score <= 100.00', name='check_metrics_quality_score_range'),
        sa.CheckConstraint('bounce_rate >= 0.00 AND bounce_rate <= 100.00', name='check_metrics_bounce_rate_range'),
    )

    # 4. Create promotional_listings table
    op.create_table(
        'promotional_listings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),

        # Core listing information
        sa.Column('advertisement_id', sa.Integer(), nullable=True),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('service_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),

        # Placement configuration
        sa.Column('placement_type', postgresql.ENUM('homepage_hero', 'category_top', 'search_results', 'service_detail', 'vendor_profile', 'mobile_banner', 'email_newsletter', name='placementtype'), nullable=False),
        sa.Column('placement_status', postgresql.ENUM('available', 'reserved', 'occupied', 'maintenance', name='placementstatus'), nullable=False, default='available'),
        sa.Column('priority_level', sa.Integer(), nullable=False, default=1),
        sa.Column('position_index', sa.Integer(), nullable=True),

        # Pricing and billing
        sa.Column('base_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('premium_multiplier', sa.Numeric(5, 2), nullable=False, default=1.00),
        sa.Column('final_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('billing_cycle', sa.String(20), nullable=False, default='daily'),

        # Scheduling
        sa.Column('start_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('end_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('timezone', sa.String(50), nullable=False, default='Africa/Lagos'),

        # Performance tracking
        sa.Column('impressions', sa.Integer(), nullable=False, default=0),
        sa.Column('clicks', sa.Integer(), nullable=False, default=0),
        sa.Column('conversions', sa.Integer(), nullable=False, default=0),
        sa.Column('revenue_generated', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Targeting and optimization
        sa.Column('targeting_config', postgresql.JSONB(), nullable=True),
        sa.Column('optimization_settings', postgresql.JSONB(), nullable=True),

        # Approval and review
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('approved_by', sa.Integer(), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['advertisement_id'], ['advertisements.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),

        # Check constraints for business rules
        sa.CheckConstraint('priority_level >= 1 AND priority_level <= 10', name='check_listing_priority_range'),
        sa.CheckConstraint('base_price >= 100.00', name='check_listing_min_price'),
        sa.CheckConstraint('premium_multiplier >= 0.50 AND premium_multiplier <= 10.00', name='check_listing_multiplier_range'),
        sa.CheckConstraint('final_price >= 50.00', name='check_listing_min_final_price'),
        sa.CheckConstraint('end_date > start_date', name='check_listing_date_order'),
        sa.CheckConstraint('impressions >= 0', name='check_listing_impressions_positive'),
        sa.CheckConstraint('clicks >= 0', name='check_listing_clicks_positive'),
        sa.CheckConstraint('conversions >= 0', name='check_listing_conversions_positive'),
        sa.CheckConstraint('revenue_generated >= 0.00', name='check_listing_revenue_positive'),
        sa.CheckConstraint('billing_cycle IN (\'daily\', \'weekly\', \'monthly\')', name='check_listing_billing_cycle'),
    )

    # 5. Create ad_spends table
    op.create_table(
        'ad_spends',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),

        # Core spend information
        sa.Column('campaign_id', sa.Integer(), nullable=False),
        sa.Column('advertisement_id', sa.Integer(), nullable=True),
        sa.Column('vendor_id', sa.Integer(), nullable=False),

        # Spend details
        sa.Column('spend_category', postgresql.ENUM('campaign_budget', 'placement_fee', 'creative_production', 'targeting_premium', 'performance_bonus', 'platform_fee', name='spendcategory'), nullable=False),
        sa.Column('amount', sa.Numeric(10, 2), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),
        sa.Column('description', sa.Text(), nullable=True),

        # Payment integration
        # TODO-PAYSTACK-API-URL: Paystack payment tracking for ad spend
        # TODO-STRIPE-API-URL: Stripe payment tracking for international ad spend
        # TODO-BUSHA-API-URL: Busha cryptocurrency payment tracking for ad spend
        sa.Column('payment_provider', postgresql.ENUM('stripe', 'paystack', 'busha', name='paymentprovidertype'), nullable=False),
        sa.Column('payment_reference', sa.String(255), nullable=False),
        sa.Column('payment_status', sa.String(50), nullable=False, default='pending'),

        # Billing period
        sa.Column('billing_period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('billing_period_end', sa.DateTime(timezone=True), nullable=False),

        # Performance attribution
        sa.Column('impressions_attributed', sa.Integer(), nullable=False, default=0),
        sa.Column('clicks_attributed', sa.Integer(), nullable=False, default=0),
        sa.Column('conversions_attributed', sa.Integer(), nullable=False, default=0),
        sa.Column('revenue_attributed', sa.Numeric(12, 2), nullable=False, default=0.00),

        # Tax and fees
        sa.Column('platform_fee', sa.Numeric(8, 2), nullable=False, default=0.00),
        sa.Column('tax_amount', sa.Numeric(8, 2), nullable=False, default=0.00),
        sa.Column('total_amount', sa.Numeric(10, 2), nullable=False),

        # Processing information
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('processed_by', sa.Integer(), nullable=True),

        # Reconciliation
        sa.Column('reconciled', sa.Boolean(), nullable=False, default=False),
        sa.Column('reconciled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reconciliation_reference', sa.String(255), nullable=True),

        # Constraints
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['advertisement_id'], ['advertisements.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['processed_by'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid'),
        sa.UniqueConstraint('payment_reference'),

        # Check constraints for business rules
        sa.CheckConstraint('amount >= 0.00', name='check_spend_amount_positive'),
        sa.CheckConstraint('platform_fee >= 0.00', name='check_spend_platform_fee_positive'),
        sa.CheckConstraint('tax_amount >= 0.00', name='check_spend_tax_positive'),
        sa.CheckConstraint('total_amount >= amount', name='check_spend_total_amount_valid'),
        sa.CheckConstraint('billing_period_end > billing_period_start', name='check_spend_billing_period_order'),
        sa.CheckConstraint('impressions_attributed >= 0', name='check_spend_impressions_positive'),
        sa.CheckConstraint('clicks_attributed >= 0', name='check_spend_clicks_positive'),
        sa.CheckConstraint('conversions_attributed >= 0', name='check_spend_conversions_positive'),
        sa.CheckConstraint('revenue_attributed >= 0.00', name='check_spend_revenue_positive'),
        sa.CheckConstraint('currency IN (\'NGN\', \'USD\', \'EUR\', \'GBP\', \'BTC\', \'ETH\', \'USDT\', \'USDC\')', name='check_spend_currency_valid'),
    )

    # Create performance-optimized indexes for <200ms query targets

    # Campaign table indexes (9 indexes)
    op.create_index('idx_campaign_vendor_status', 'campaigns', ['vendor_id', 'status'])
    op.create_index('idx_campaign_type_status', 'campaigns', ['campaign_type', 'status'])
    op.create_index('idx_campaign_dates', 'campaigns', ['start_date', 'end_date'])
    op.create_index('idx_campaign_budget', 'campaigns', ['total_budget', 'spent_amount'])
    op.create_index('idx_campaign_performance', 'campaigns', ['impressions', 'clicks', 'conversions'])
    op.create_index('idx_campaign_provider', 'campaigns', ['payment_provider', 'status'])
    op.create_index('idx_campaign_objective', 'campaigns', ['campaign_objective', 'status'])
    op.create_index('idx_campaign_approval', 'campaigns', ['approved_at', 'approved_by'])
    op.create_index('idx_campaign_active_dates', 'campaigns', ['status', 'start_date', 'end_date'])

    # Advertisement table indexes (7 indexes)
    op.create_index('idx_ad_campaign_status', 'advertisements', ['campaign_id', 'status'])
    op.create_index('idx_ad_format_status', 'advertisements', ['ad_format', 'status'])
    op.create_index('idx_ad_dates', 'advertisements', ['start_date', 'end_date'])
    op.create_index('idx_ad_performance', 'advertisements', ['impressions', 'clicks', 'conversions'])
    op.create_index('idx_ad_variant', 'advertisements', ['variant_group', 'variant_name'])
    op.create_index('idx_ad_approval', 'advertisements', ['approved_at', 'approved_by'])
    op.create_index('idx_ad_active_dates', 'advertisements', ['status', 'start_date', 'end_date'])

    # Campaign metrics table indexes (6 indexes)
    op.create_index('idx_metrics_campaign_date', 'campaign_metrics', ['campaign_id', 'date'])
    op.create_index('idx_metrics_date_hour', 'campaign_metrics', ['date', 'hour'])
    op.create_index('idx_metrics_performance', 'campaign_metrics', ['impressions', 'clicks', 'conversions'])
    op.create_index('idx_metrics_spend', 'campaign_metrics', ['spend_amount', 'revenue_generated'])
    op.create_index('idx_metrics_quality', 'campaign_metrics', ['quality_score', 'optimization_score'])
    op.create_index('idx_metrics_device', 'campaign_metrics', ['desktop_impressions', 'mobile_impressions'])

    # Promotional listings table indexes (7 indexes)
    op.create_index('idx_listing_vendor_status', 'promotional_listings', ['vendor_id', 'placement_status'])
    op.create_index('idx_listing_placement_priority', 'promotional_listings', ['placement_type', 'priority_level'])
    op.create_index('idx_listing_dates', 'promotional_listings', ['start_date', 'end_date'])
    op.create_index('idx_listing_performance', 'promotional_listings', ['impressions', 'clicks', 'conversions'])
    op.create_index('idx_listing_pricing', 'promotional_listings', ['base_price', 'final_price'])
    op.create_index('idx_listing_active', 'promotional_listings', ['placement_status', 'start_date', 'end_date'])
    op.create_index('idx_listing_position', 'promotional_listings', ['placement_type', 'position_index'])

    # Ad spends table indexes (8 indexes)
    op.create_index('idx_spend_campaign_status', 'ad_spends', ['campaign_id', 'payment_status'])
    op.create_index('idx_spend_vendor_period', 'ad_spends', ['vendor_id', 'billing_period_start', 'billing_period_end'])
    op.create_index('idx_spend_provider_status', 'ad_spends', ['payment_provider', 'payment_status'])
    op.create_index('idx_spend_category_amount', 'ad_spends', ['spend_category', 'amount'])
    op.create_index('idx_spend_reconciliation', 'ad_spends', ['reconciled', 'reconciled_at'])
    op.create_index('idx_spend_processing', 'ad_spends', ['processed_at', 'processed_by'])
    op.create_index('idx_spend_reference', 'ad_spends', ['payment_reference'])
    op.create_index('idx_spend_performance', 'ad_spends', ['impressions_attributed', 'clicks_attributed', 'conversions_attributed'])

    # Create GIN indexes for JSONB columns for fast JSON queries
    op.create_index('idx_campaign_targeting_gin', 'campaigns', ['targeting_config'], postgresql_using='gin')
    op.create_index('idx_campaign_geographic_gin', 'campaigns', ['geographic_targeting'], postgresql_using='gin')
    op.create_index('idx_campaign_demographic_gin', 'campaigns', ['demographic_targeting'], postgresql_using='gin')
    op.create_index('idx_ad_media_assets_gin', 'advertisements', ['media_assets'], postgresql_using='gin')
    op.create_index('idx_ad_placement_prefs_gin', 'advertisements', ['placement_preferences'], postgresql_using='gin')
    op.create_index('idx_ad_targeting_override_gin', 'advertisements', ['targeting_override'], postgresql_using='gin')
    op.create_index('idx_metrics_locations_gin', 'campaign_metrics', ['top_performing_locations'], postgresql_using='gin')
    op.create_index('idx_metrics_recommendations_gin', 'campaign_metrics', ['recommendations'], postgresql_using='gin')
    op.create_index('idx_listing_targeting_gin', 'promotional_listings', ['targeting_config'], postgresql_using='gin')
    op.create_index('idx_listing_optimization_gin', 'promotional_listings', ['optimization_settings'], postgresql_using='gin')

    # Create audit field indexes for BaseModelWithAudit compatibility
    op.create_index('idx_campaign_created_at', 'campaigns', ['created_at'])
    op.create_index('idx_campaign_updated_at', 'campaigns', ['updated_at'])
    op.create_index('idx_campaign_created_by', 'campaigns', ['created_by'])
    op.create_index('idx_ad_created_at', 'advertisements', ['created_at'])
    op.create_index('idx_ad_updated_at', 'advertisements', ['updated_at'])
    op.create_index('idx_ad_created_by', 'advertisements', ['created_by'])
    op.create_index('idx_metrics_created_at', 'campaign_metrics', ['created_at'])
    op.create_index('idx_listing_created_at', 'promotional_listings', ['created_at'])
    op.create_index('idx_spend_created_at', 'ad_spends', ['created_at'])

    # Add ANALYZE statements for query planner optimization
    op.execute('ANALYZE campaigns')
    op.execute('ANALYZE advertisements')
    op.execute('ANALYZE campaign_metrics')
    op.execute('ANALYZE promotional_listings')
    op.execute('ANALYZE ad_spends')


def downgrade() -> None:
    """Downgrade database schema - Remove promotional system tables."""

    # Drop tables in reverse order to handle foreign key dependencies
    op.drop_table('ad_spends')
    op.drop_table('promotional_listings')
    op.drop_table('campaign_metrics')
    op.drop_table('advertisements')
    op.drop_table('campaigns')

    # Drop enum types
    op.execute('DROP TYPE spendcategory')
    op.execute('DROP TYPE placementstatus')
    op.execute('DROP TYPE placementtype')
    op.execute('DROP TYPE adstatus')
    op.execute('DROP TYPE adformat')
    op.execute('DROP TYPE bidstrategy')
    op.execute('DROP TYPE campaignobjective')
    op.execute('DROP TYPE campaigntype')
    op.execute('DROP TYPE campaignstatus')
