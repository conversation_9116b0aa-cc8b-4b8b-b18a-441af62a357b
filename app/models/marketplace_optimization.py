"""
Marketplace Optimization models for Culture Connect Backend API.

This module defines comprehensive marketplace optimization models including:
- SEOAnalysis: SEO score calculation and content optimization tracking
- PerformanceMetrics: Listing performance analytics and engagement tracking
- CompetitiveAnalysis: Market positioning and competitor insights
- OptimizationRecommendation: Actionable recommendations and improvement strategies
- MobileOptimization: Mobile app preview and responsive design metrics

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade PostgreSQL optimization and seamless integration with
existing service listing and vendor management systems.
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Dict, Any, Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, Numeric, DateTime, Boolean,
    ForeignKey, JSON, Index, CheckConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.db.base import BaseModelWithAudit


class SEOScoreCategory(str, Enum):
    """SEO score categories for optimization tracking."""
    TITLE_OPTIMIZATION = "title_optimization"
    DESCRIPTION_QUALITY = "description_quality"
    MEDIA_QUALITY = "media_quality"
    PERFORMANCE_METRICS = "performance_metrics"
    COMPLETENESS = "completeness"
    KEYWORD_OPTIMIZATION = "keyword_optimization"
    META_TAGS = "meta_tags"
    CONTENT_STRUCTURE = "content_structure"


class PerformanceMetricType(str, Enum):
    """Performance metric types for analytics tracking."""
    LISTING_VIEWS = "listing_views"
    PROFILE_VISITS = "profile_visits"
    CONTACT_CLICKS = "contact_clicks"
    BOOKING_INQUIRIES = "booking_inquiries"
    CONVERSION_RATE = "conversion_rate"
    ENGAGEMENT_RATE = "engagement_rate"
    BOUNCE_RATE = "bounce_rate"
    TIME_ON_PAGE = "time_on_page"
    SEARCH_IMPRESSIONS = "search_impressions"
    CLICK_THROUGH_RATE = "click_through_rate"


class RecommendationType(str, Enum):
    """Optimization recommendation types."""
    SEO_IMPROVEMENT = "seo_improvement"
    CONTENT_ENHANCEMENT = "content_enhancement"
    PRICING_OPTIMIZATION = "pricing_optimization"
    MEDIA_UPGRADE = "media_upgrade"
    PERFORMANCE_BOOST = "performance_boost"
    COMPETITIVE_POSITIONING = "competitive_positioning"
    MOBILE_OPTIMIZATION = "mobile_optimization"
    CONVERSION_OPTIMIZATION = "conversion_optimization"


class RecommendationPriority(str, Enum):
    """Recommendation priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class MobileOptimizationCategory(str, Enum):
    """Mobile optimization categories."""
    RESPONSIVE_DESIGN = "responsive_design"
    LOADING_SPEED = "loading_speed"
    TOUCH_INTERFACE = "touch_interface"
    CONTENT_READABILITY = "content_readability"
    IMAGE_OPTIMIZATION = "image_optimization"
    NAVIGATION_UX = "navigation_ux"


class SEOAnalysis(BaseModelWithAudit):
    """
    SEO analysis model for tracking service listing optimization.

    Stores comprehensive SEO metrics, keyword analysis, and content quality scores
    for service listings with historical tracking and improvement recommendations.
    """
    __tablename__ = "seo_analysis"

    id = Column(Integer, primary_key=True, index=True)

    # Foreign key relationships
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the service being analyzed"
    )
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor owning the service"
    )

    # SEO scoring
    overall_seo_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Overall SEO score (0.00-100.00)"
    )

    title_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Title optimization score (0.00-100.00)"
    )

    description_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Description quality score (0.00-100.00)"
    )

    media_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Media quality score (0.00-100.00)"
    )

    keyword_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Keyword optimization score (0.00-100.00)"
    )

    completeness_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Profile completeness score (0.00-100.00)"
    )

    # Keyword analysis
    primary_keywords = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Primary keywords identified in content"
    )

    keyword_density = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Keyword density analysis results"
    )

    missing_keywords = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Recommended keywords not present in content"
    )

    # Content analysis
    content_quality_metrics = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Content quality analysis metrics"
    )

    readability_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Content readability score (0.00-100.00)"
    )

    # Meta data analysis
    meta_title_analysis = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Meta title optimization analysis"
    )

    meta_description_analysis = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Meta description optimization analysis"
    )

    # Analysis metadata
    analysis_date = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        doc="Date when analysis was performed"
    )

    analysis_version = Column(
        String(20),
        nullable=False,
        default="1.0",
        doc="Version of analysis algorithm used"
    )

    # Relationships
    service = relationship("Service", back_populates="seo_analyses")
    vendor = relationship("Vendor", back_populates="seo_analyses")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "overall_seo_score >= 0 AND overall_seo_score <= 100",
            name="check_overall_seo_score_range"
        ),
        CheckConstraint(
            "title_score >= 0 AND title_score <= 100",
            name="check_title_score_range"
        ),
        CheckConstraint(
            "description_score >= 0 AND description_score <= 100",
            name="check_description_score_range"
        ),
        CheckConstraint(
            "media_score >= 0 AND media_score <= 100",
            name="check_media_score_range"
        ),
        CheckConstraint(
            "keyword_score >= 0 AND keyword_score <= 100",
            name="check_keyword_score_range"
        ),
        CheckConstraint(
            "completeness_score >= 0 AND completeness_score <= 100",
            name="check_completeness_score_range"
        ),
        CheckConstraint(
            "readability_score >= 0 AND readability_score <= 100",
            name="check_readability_score_range"
        ),
        Index("idx_seo_analysis_service_date", "service_id", "analysis_date"),
        Index("idx_seo_analysis_vendor_score", "vendor_id", "overall_seo_score"),
        Index("idx_seo_analysis_score_date", "overall_seo_score", "analysis_date"),
        {"extend_existing": True}
    )

    def __repr__(self) -> str:
        return f"<SEOAnalysis(id={self.id}, service_id={self.service_id}, score={self.overall_seo_score})>"


class PerformanceMetrics(BaseModelWithAudit):
    """
    Performance metrics model for tracking listing analytics and engagement.

    Stores comprehensive performance data including views, clicks, conversions,
    and engagement metrics with time-series tracking for trend analysis.
    """
    __tablename__ = "marketplace_performance_metrics"

    id = Column(Integer, primary_key=True, index=True)

    # Foreign key relationships
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the service being tracked"
    )
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor owning the service"
    )

    # Time period for metrics
    metric_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Date for which metrics are recorded"
    )

    metric_period = Column(
        String(20),
        nullable=False,
        default="daily",
        doc="Period type: daily, weekly, monthly"
    )

    # View metrics
    listing_views = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of listing page views"
    )

    unique_views = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of unique listing views"
    )

    search_impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times listing appeared in search results"
    )

    # Engagement metrics
    profile_visits = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of vendor profile visits"
    )

    contact_clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of contact button clicks"
    )

    booking_inquiries = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of booking inquiries received"
    )

    phone_clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of phone number clicks"
    )

    email_clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of email clicks"
    )

    # Conversion metrics
    conversion_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Conversion rate percentage (0.00-100.00)"
    )

    click_through_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Click-through rate from search results (0.00-100.00)"
    )

    engagement_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Overall engagement rate (0.00-100.00)"
    )

    bounce_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Bounce rate percentage (0.00-100.00)"
    )

    # Time-based metrics
    average_time_on_page = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average time spent on listing page (seconds)"
    )

    # Revenue metrics
    revenue_generated = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Revenue generated during period"
    )

    bookings_completed = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of completed bookings"
    )

    # Additional analytics
    traffic_sources = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Traffic source breakdown"
    )

    device_breakdown = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Device type breakdown (mobile, desktop, tablet)"
    )

    geographic_data = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Geographic distribution of views"
    )

    # Relationships
    service = relationship("Service", back_populates="performance_metrics")
    vendor = relationship("Vendor", back_populates="performance_metrics")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "conversion_rate >= 0 AND conversion_rate <= 100",
            name="check_conversion_rate_range"
        ),
        CheckConstraint(
            "click_through_rate >= 0 AND click_through_rate <= 100",
            name="check_ctr_range"
        ),
        CheckConstraint(
            "engagement_rate >= 0 AND engagement_rate <= 100",
            name="check_engagement_rate_range"
        ),
        CheckConstraint(
            "bounce_rate >= 0 AND bounce_rate <= 100",
            name="check_bounce_rate_range"
        ),
        CheckConstraint(
            "listing_views >= 0",
            name="check_listing_views_positive"
        ),
        CheckConstraint(
            "unique_views >= 0",
            name="check_unique_views_positive"
        ),
        CheckConstraint(
            "revenue_generated >= 0",
            name="check_revenue_positive"
        ),
        Index("idx_performance_service_date", "service_id", "metric_date"),
        Index("idx_performance_vendor_date", "vendor_id", "metric_date"),
        Index("idx_performance_date_period", "metric_date", "metric_period"),
        Index("idx_performance_conversion", "conversion_rate", "metric_date"),
        {"extend_existing": True}
    )

    def __repr__(self) -> str:
        return f"<PerformanceMetrics(id={self.id}, service_id={self.service_id}, date={self.metric_date})>"


class CompetitiveAnalysis(BaseModelWithAudit):
    """
    Competitive analysis model for market positioning and competitor insights.

    Stores comprehensive competitive intelligence including market positioning,
    pricing analysis, feature comparisons, and competitive advantages.
    """
    __tablename__ = "competitive_analysis"

    id = Column(Integer, primary_key=True, index=True)

    # Foreign key relationships
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the service being analyzed"
    )
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor owning the service"
    )

    # Analysis metadata
    analysis_date = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Date when competitive analysis was performed"
    )

    market_category = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Market category for competitive analysis"
    )

    geographic_scope = Column(
        String(100),
        nullable=False,
        default="local",
        doc="Geographic scope of analysis (local, regional, national)"
    )

    # Market positioning
    market_position_rank = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Current market position ranking"
    )

    total_competitors = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of competitors analyzed"
    )

    market_share_percentage = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Estimated market share percentage"
    )

    # Pricing analysis
    price_position = Column(
        String(20),
        nullable=False,
        default="average",
        doc="Price position: budget, average, premium, luxury"
    )

    price_competitiveness_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Price competitiveness score (0.00-100.00)"
    )

    average_competitor_price = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average price of competitors"
    )

    price_difference_percentage = Column(
        Numeric(6, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Price difference from market average (-100.00 to +100.00)"
    )

    # Quality comparison
    quality_score_vs_competitors = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Quality score compared to competitors (0.00-100.00)"
    )

    rating_vs_competitors = Column(
        Numeric(3, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Rating comparison with competitors"
    )

    review_count_vs_competitors = Column(
        Numeric(6, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Review count percentage vs competitors"
    )

    # Feature analysis
    feature_comparison = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Feature comparison with competitors"
    )

    unique_selling_points = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Identified unique selling points"
    )

    competitive_gaps = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Identified competitive gaps and weaknesses"
    )

    # Market insights
    market_trends = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Identified market trends and opportunities"
    )

    competitor_strategies = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Analysis of competitor strategies"
    )

    market_opportunities = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Identified market opportunities"
    )

    threat_analysis = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Competitive threat analysis"
    )

    # Performance comparison
    visibility_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Market visibility score vs competitors (0.00-100.00)"
    )

    engagement_vs_competitors = Column(
        Numeric(6, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Engagement rate vs competitors percentage"
    )

    # Recommendations
    competitive_recommendations = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Strategic recommendations based on competitive analysis"
    )

    # Relationships
    service = relationship("Service", back_populates="competitive_analyses")
    vendor = relationship("Vendor", back_populates="competitive_analyses")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "market_share_percentage >= 0 AND market_share_percentage <= 100",
            name="check_market_share_range"
        ),
        CheckConstraint(
            "price_competitiveness_score >= 0 AND price_competitiveness_score <= 100",
            name="check_price_competitiveness_range"
        ),
        CheckConstraint(
            "quality_score_vs_competitors >= 0 AND quality_score_vs_competitors <= 100",
            name="check_quality_score_range"
        ),
        CheckConstraint(
            "visibility_score >= 0 AND visibility_score <= 100",
            name="check_visibility_score_range"
        ),
        CheckConstraint(
            "price_difference_percentage >= -100 AND price_difference_percentage <= 100",
            name="check_price_difference_range"
        ),
        Index("idx_competitive_service_date", "service_id", "analysis_date"),
        Index("idx_competitive_vendor_category", "vendor_id", "market_category"),
        Index("idx_competitive_position", "market_position_rank", "analysis_date"),
    )

    def __repr__(self) -> str:
        return f"<CompetitiveAnalysis(id={self.id}, service_id={self.service_id}, rank={self.market_position_rank})>"


class OptimizationRecommendation(BaseModelWithAudit):
    """
    Optimization recommendation model for actionable improvement strategies.

    Stores AI-generated and expert recommendations for improving service listings,
    performance, and market positioning with priority levels and implementation tracking.
    """
    __tablename__ = "optimization_recommendations"

    id = Column(Integer, primary_key=True, index=True)

    # Foreign key relationships
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the service for recommendation"
    )
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor receiving recommendation"
    )

    # Recommendation details
    recommendation_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of recommendation"
    )

    priority = Column(
        String(20),
        nullable=False,
        default=RecommendationPriority.MEDIUM.value,
        index=True,
        doc="Recommendation priority level"
    )

    title = Column(
        String(200),
        nullable=False,
        doc="Short title for the recommendation"
    )

    description = Column(
        Text,
        nullable=False,
        doc="Detailed description of the recommendation"
    )

    # Impact and effort
    expected_impact_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Expected impact score (0.00-100.00)"
    )

    implementation_effort = Column(
        String(20),
        nullable=False,
        default="medium",
        doc="Implementation effort: low, medium, high"
    )

    estimated_time_hours = Column(
        Numeric(6, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Estimated implementation time in hours"
    )

    # Implementation details
    implementation_steps = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Step-by-step implementation guide"
    )

    required_resources = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Required resources for implementation"
    )

    success_metrics = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Metrics to measure success"
    )

    # Status tracking
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        index=True,
        doc="Implementation status: pending, in_progress, completed, dismissed"
    )

    implementation_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date when implementation started"
    )

    completion_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date when implementation was completed"
    )

    # Results tracking
    actual_impact_score = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Actual impact score after implementation"
    )

    implementation_notes = Column(
        Text,
        nullable=True,
        doc="Notes about implementation process"
    )

    # AI/Algorithm metadata
    generated_by = Column(
        String(50),
        nullable=False,
        default="system",
        doc="Source of recommendation: system, ai, expert, user"
    )

    algorithm_version = Column(
        String(20),
        nullable=False,
        default="1.0",
        doc="Version of algorithm that generated recommendation"
    )

    confidence_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Confidence score in recommendation (0.00-100.00)"
    )

    # Related data
    related_analysis_data = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Related analysis data that led to recommendation"
    )

    # Relationships
    service = relationship("Service", back_populates="optimization_recommendations")
    vendor = relationship("Vendor", back_populates="optimization_recommendations")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "expected_impact_score >= 0 AND expected_impact_score <= 100",
            name="check_expected_impact_range"
        ),
        CheckConstraint(
            "actual_impact_score IS NULL OR (actual_impact_score >= 0 AND actual_impact_score <= 100)",
            name="check_actual_impact_range"
        ),
        CheckConstraint(
            "confidence_score >= 0 AND confidence_score <= 100",
            name="check_confidence_score_range"
        ),
        CheckConstraint(
            "estimated_time_hours >= 0",
            name="check_time_hours_positive"
        ),
        Index("idx_recommendation_service_priority", "service_id", "priority"),
        Index("idx_recommendation_vendor_status", "vendor_id", "status"),
        Index("idx_recommendation_type_priority", "recommendation_type", "priority"),
        Index("idx_recommendation_impact", "expected_impact_score", "created_at"),
    )

    def __repr__(self) -> str:
        return f"<OptimizationRecommendation(id={self.id}, service_id={self.service_id}, type={self.recommendation_type})>"


class MobileOptimization(BaseModelWithAudit):
    """
    Mobile optimization model for mobile app preview and responsive design metrics.

    Stores comprehensive mobile optimization analysis including responsive design validation,
    loading speed metrics, touch interface optimization, and mobile user experience scoring.
    """
    __tablename__ = "mobile_optimization"

    id = Column(Integer, primary_key=True, index=True)

    # Foreign key relationships
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the service being analyzed"
    )
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the vendor owning the service"
    )

    # Analysis metadata
    analysis_date = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        index=True,
        doc="Date when mobile optimization analysis was performed"
    )

    device_type = Column(
        String(20),
        nullable=False,
        default="mobile",
        doc="Device type analyzed: mobile, tablet, desktop"
    )

    screen_size_category = Column(
        String(20),
        nullable=False,
        default="standard",
        doc="Screen size category: small, standard, large, extra_large"
    )

    # Overall mobile score
    overall_mobile_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Overall mobile optimization score (0.00-100.00)"
    )

    # Responsive design metrics
    responsive_design_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Responsive design score (0.00-100.00)"
    )

    layout_adaptation_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Layout adaptation score for different screen sizes"
    )

    content_readability_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Content readability score on mobile devices"
    )

    # Performance metrics
    loading_speed_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile loading speed score (0.00-100.00)"
    )

    page_load_time_ms = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Page load time in milliseconds"
    )

    first_contentful_paint_ms = Column(
        Integer,
        nullable=False,
        default=0,
        doc="First contentful paint time in milliseconds"
    )

    largest_contentful_paint_ms = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Largest contentful paint time in milliseconds"
    )

    # Touch interface optimization
    touch_interface_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Touch interface optimization score (0.00-100.00)"
    )

    button_size_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Button size optimization score for touch devices"
    )

    tap_target_spacing_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Tap target spacing optimization score"
    )

    # Image optimization
    image_optimization_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Image optimization score for mobile (0.00-100.00)"
    )

    image_compression_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Image compression optimization score"
    )

    image_format_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Image format optimization score (WebP, AVIF support)"
    )

    # Navigation and UX
    navigation_ux_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile navigation UX score (0.00-100.00)"
    )

    menu_accessibility_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile menu accessibility score"
    )

    search_functionality_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile search functionality score"
    )

    # Content optimization
    text_size_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Text size optimization score for mobile reading"
    )

    content_hierarchy_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Content hierarchy optimization score"
    )

    # Technical metrics
    viewport_configuration_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Viewport configuration score"
    )

    css_media_queries_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="CSS media queries implementation score"
    )

    # Detailed analysis data
    responsive_breakpoints = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Analysis of responsive breakpoints"
    )

    performance_metrics = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Detailed performance metrics"
    )

    accessibility_issues = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Identified accessibility issues on mobile"
    )

    optimization_opportunities = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Identified optimization opportunities"
    )

    # Mobile app preview data
    app_preview_data = Column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Mobile app preview generation data"
    )

    preview_image_urls = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Generated preview image URLs for different devices"
    )

    # User experience metrics
    mobile_conversion_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile-specific conversion rate"
    )

    mobile_bounce_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile-specific bounce rate"
    )

    mobile_engagement_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Mobile engagement score"
    )

    # Recommendations
    mobile_recommendations = Column(
        JSONB,
        nullable=False,
        default=list,
        doc="Mobile-specific optimization recommendations"
    )

    # Relationships
    service = relationship("Service", back_populates="mobile_optimizations")
    vendor = relationship("Vendor", back_populates="mobile_optimizations")

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "overall_mobile_score >= 0 AND overall_mobile_score <= 100",
            name="check_overall_mobile_score_range"
        ),
        CheckConstraint(
            "responsive_design_score >= 0 AND responsive_design_score <= 100",
            name="check_responsive_design_score_range"
        ),
        CheckConstraint(
            "loading_speed_score >= 0 AND loading_speed_score <= 100",
            name="check_loading_speed_score_range"
        ),
        CheckConstraint(
            "touch_interface_score >= 0 AND touch_interface_score <= 100",
            name="check_touch_interface_score_range"
        ),
        CheckConstraint(
            "image_optimization_score >= 0 AND image_optimization_score <= 100",
            name="check_image_optimization_score_range"
        ),
        CheckConstraint(
            "navigation_ux_score >= 0 AND navigation_ux_score <= 100",
            name="check_navigation_ux_score_range"
        ),
        CheckConstraint(
            "mobile_conversion_rate >= 0 AND mobile_conversion_rate <= 100",
            name="check_mobile_conversion_rate_range"
        ),
        CheckConstraint(
            "mobile_bounce_rate >= 0 AND mobile_bounce_rate <= 100",
            name="check_mobile_bounce_rate_range"
        ),
        CheckConstraint(
            "page_load_time_ms >= 0",
            name="check_page_load_time_positive"
        ),
        Index("idx_mobile_service_date", "service_id", "analysis_date"),
        Index("idx_mobile_vendor_score", "vendor_id", "overall_mobile_score"),
        Index("idx_mobile_device_score", "device_type", "overall_mobile_score"),
        Index("idx_mobile_performance", "loading_speed_score", "analysis_date"),
    )

    def __repr__(self) -> str:
        return f"<MobileOptimization(id={self.id}, service_id={self.service_id}, score={self.overall_mobile_score})>"
