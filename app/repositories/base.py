"""
Enhanced Base Repository for Culture Connect Backend API.

This module provides a production-grade repository base class with comprehensive
CRUD operations, query optimization, pagination, filtering, and type safety.

Features:
- Async SQLAlchemy 2.0 support with performance optimization
- Generic type safety with comprehensive type hints
- Cursor-based and offset-based pagination
- Dynamic filtering and sorting with query optimization
- Bulk operations with transaction management
- Performance monitoring and query analysis
- Integration with service layer architecture
"""

import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import (
    Generic, TypeVar, Type, Optional, List, Dict, Any, Union, Tuple,
    Sequence, Callable, AsyncGenerator, Literal
)
from dataclasses import dataclass
from datetime import datetime, timezone
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import (
    IntegrityError, SQLAlchemyError, OperationalError,
    DatabaseError, TimeoutError as SQLTimeoutError
)
from sqlalchemy import (
    desc, asc, func, select, update, delete, and_, or_, text,
    Column, Integer, String, DateTime, Boolean
)
from sqlalchemy.orm import selectinload, joinedload, contains_eager
from sqlalchemy.sql import Select
from sqlalchemy.dialects.postgresql import insert

from app.db.base import BaseModel
from app.core.monitoring import metrics_collector

# Type variables for enhanced type safety
ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")

# Logger for repository operations
logger = logging.getLogger(__name__)


# Import bulk operations at the end to avoid circular imports
def _import_bulk_operations():
    """Import bulk operations dynamically to avoid circular imports."""
    try:
        from .bulk_operations import BulkOperationsMixin
        return BulkOperationsMixin
    except ImportError:
        # Return empty mixin if bulk operations not available
        class EmptyMixin:
            pass
        return EmptyMixin


# Data classes for pagination and filtering
@dataclass
class PaginationParams:
    """Parameters for pagination operations."""
    page: int = 1
    size: int = 20
    max_size: int = 100

    def __post_init__(self):
        """Validate pagination parameters."""
        if self.page < 1:
            self.page = 1
        if self.size < 1:
            self.size = 20
        if self.size > self.max_size:
            self.size = self.max_size

    @property
    def offset(self) -> int:
        """Calculate offset for database query."""
        return (self.page - 1) * self.size


@dataclass
class CursorPaginationParams:
    """Parameters for cursor-based pagination."""
    cursor: Optional[str] = None
    size: int = 20
    direction: Literal["forward", "backward"] = "forward"
    max_size: int = 100

    def __post_init__(self):
        """Validate cursor pagination parameters."""
        if self.size < 1:
            self.size = 20
        if self.size > self.max_size:
            self.size = self.max_size


@dataclass
class SortParams:
    """Parameters for sorting operations."""
    field: str
    direction: Literal["asc", "desc"] = "asc"

    def __post_init__(self):
        """Validate sort parameters."""
        if self.direction not in ("asc", "desc"):
            self.direction = "asc"


@dataclass
class FilterParams:
    """Parameters for filtering operations."""
    field: str
    operator: Literal["eq", "ne", "gt", "gte", "lt", "lte", "like", "ilike", "in", "not_in"] = "eq"
    value: Any = None

    def __post_init__(self):
        """Validate filter parameters."""
        valid_operators = ("eq", "ne", "gt", "gte", "lt", "lte", "like", "ilike", "in", "not_in")
        if self.operator not in valid_operators:
            self.operator = "eq"


@dataclass
class QueryResult(Generic[ModelType]):
    """Result container for paginated queries."""
    items: List[ModelType]
    total: int
    page: int
    size: int
    has_next: bool
    has_previous: bool

    @property
    def total_pages(self) -> int:
        """Calculate total number of pages."""
        return (self.total + self.size - 1) // self.size


@dataclass
class CursorQueryResult(Generic[ModelType]):
    """Result container for cursor-based paginated queries."""
    items: List[ModelType]
    next_cursor: Optional[str]
    previous_cursor: Optional[str]
    has_next: bool
    has_previous: bool


@dataclass
class QueryPerformanceMetrics:
    """Performance metrics for query operations."""
    query_time: float
    row_count: int
    query_type: str
    table_name: str
    timestamp: datetime


class RepositoryError(Exception):
    """Base exception for repository operations."""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.original_error = original_error


class BaseRepositoryCore(Generic[ModelType]):
    """
    Core base repository class with basic CRUD operations.

    Provides essential database operations with:
    - Async SQLAlchemy 2.0 support
    - Query optimization and performance monitoring
    - Type safety and error handling
    """

    def __init__(self, model: Type[ModelType], db: AsyncSession):
        """
        Initialize enhanced base repository.

        Args:
            model: SQLAlchemy model class
            db: Async database session
        """
        self.model = model
        self.db = db
        self.table_name = model.__tablename__
        self.logger = logging.getLogger(f"{__name__}.{model.__name__}Repository")

        # Performance tracking
        self._query_metrics: List[QueryPerformanceMetrics] = []

    def _track_query_performance(
        self,
        query_type: str,
        query_time: float,
        row_count: int = 0
    ) -> None:
        """Track query performance metrics."""
        metric = QueryPerformanceMetrics(
            query_time=query_time,
            row_count=row_count,
            query_type=query_type,
            table_name=self.table_name,
            timestamp=datetime.now(timezone.utc)
        )
        self._query_metrics.append(metric)

        # Log slow queries
        if query_time > 1.0:  # Log queries taking more than 1 second
            self.logger.warning(
                f"Slow query detected: {query_type} on {self.table_name} "
                f"took {query_time:.2f}s for {row_count} rows"
            )

        # Report metrics to monitoring system
        try:
            metrics_collector.record_database_query(
                table=self.table_name,
                operation=query_type,
                duration=query_time,
                row_count=row_count
            )
        except Exception as e:
            self.logger.debug(f"Failed to record metrics: {e}")

    async def get(self, id: int) -> Optional[ModelType]:
        """
        Get a single record by ID with performance tracking.

        Args:
            id: Record ID

        Returns:
            Model instance if found, None otherwise

        Raises:
            RepositoryError: If database operation fails
        """
        start_time = time.time()

        try:
            stmt = select(self.model).where(self.model.id == id)
            result = await self.db.execute(stmt)
            instance = result.scalar_one_or_none()

            query_time = time.time() - start_time
            self._track_query_performance("get", query_time, 1 if instance else 0)

            return instance

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_error", query_time, 0)
            raise RepositoryError(f"Failed to get {self.model.__name__} with id {id}", e)

    async def get_by_uuid(self, uuid_value: str) -> Optional[ModelType]:
        """
        Get a single record by UUID with performance tracking.

        Args:
            uuid_value: Record UUID

        Returns:
            Model instance if found, None otherwise

        Raises:
            RepositoryError: If database operation fails
        """
        start_time = time.time()

        try:
            stmt = select(self.model).where(self.model.uuid == uuid_value)
            result = await self.db.execute(stmt)
            instance = result.scalar_one_or_none()

            query_time = time.time() - start_time
            self._track_query_performance("get_by_uuid", query_time, 1 if instance else 0)

            return instance

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_by_uuid_error", query_time, 0)
            raise RepositoryError(f"Failed to get {self.model.__name__} with uuid {uuid_value}", e)

    async def get_all(
        self,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        order_desc: bool = True
    ) -> List[ModelType]:
        """
        Get all records with pagination and ordering.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            order_by: Field name to order by (defaults to 'created_at')
            order_desc: Whether to order in descending order

        Returns:
            List of model instances
        """
        start_time = time.time()

        try:
            stmt = select(self.model)

            # Apply ordering
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if order_desc:
                    stmt = stmt.order_by(order_field.desc())
                else:
                    stmt = stmt.order_by(order_field.asc())
            else:
                # Default ordering by created_at descending
                if hasattr(self.model, 'created_at'):
                    stmt = stmt.order_by(self.model.created_at.desc())

            # Apply pagination
            stmt = stmt.offset(skip).limit(limit)

            result = await self.db.execute(stmt)
            items = result.scalars().all()

            query_time = time.time() - start_time
            self._track_query_performance("get_all", query_time, len(items))

            return items

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_all_error", query_time, 0)
            raise RepositoryError(f"Failed to get all {self.model.__name__} records", e)

    async def create(self, obj_in: Dict[str, Any]) -> ModelType:
        """
        Create a new record.

        Args:
            obj_in: Dictionary with field values

        Returns:
            Created model instance

        Raises:
            IntegrityError: If creation violates database constraints
        """
        try:
            db_obj = self.model(**obj_in)
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            self.db.rollback()
            raise e

    async def update(
        self,
        id: int,
        obj_in: Dict[str, Any],
        exclude: Optional[List[str]] = None
    ) -> Optional[ModelType]:
        """
        Update an existing record.

        Args:
            id: Record ID
            obj_in: Dictionary with field values to update
            exclude: List of field names to exclude from update

        Returns:
            Updated model instance if found, None otherwise
        """
        db_obj = await self.get(id)
        if not db_obj:
            return None

        exclude = exclude or ['id', 'uuid', 'created_at']

        for field, value in obj_in.items():
            if field not in exclude and hasattr(db_obj, field):
                setattr(db_obj, field, value)

        try:
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            self.db.rollback()
            raise e

    async def delete(self, id: int) -> bool:
        """
        Delete a record by ID.

        Args:
            id: Record ID

        Returns:
            True if record was deleted, False if not found
        """
        db_obj = await self.get(id)
        if not db_obj:
            return False

        try:
            self.db.delete(db_obj)
            self.db.commit()
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            raise e

    async def soft_delete(self, id: int) -> Optional[ModelType]:
        """
        Soft delete a record (if model supports it).

        Args:
            id: Record ID

        Returns:
            Updated model instance if found and supports soft delete, None otherwise
        """
        db_obj = await self.get(id)
        if not db_obj:
            return None

        # Check if model supports soft delete
        if hasattr(db_obj, 'soft_delete'):
            db_obj.soft_delete()
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj

        return None

    async def restore(self, id: int) -> Optional[ModelType]:
        """
        Restore a soft deleted record.

        Args:
            id: Record ID

        Returns:
            Restored model instance if found and supports soft delete, None otherwise
        """
        db_obj = await self.get(id)
        if not db_obj:
            return None

        # Check if model supports soft delete restoration
        if hasattr(db_obj, 'restore'):
            db_obj.restore()
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj

        return None

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filters.

        Args:
            filters: Optional dictionary of field filters

        Returns:
            Number of records matching criteria
        """
        query = self.db.query(self.model)

        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.filter(getattr(self.model, field) == value)

        return query.count()

    async def exists(self, id: int) -> bool:
        """
        Check if a record exists by ID.

        Args:
            id: Record ID

        Returns:
            True if record exists, False otherwise
        """
        return self.db.query(self.model).filter(self.model.id == id).first() is not None

    async def exists_by_field(self, field: str, value: Any) -> bool:
        """
        Check if a record exists by field value.

        Args:
            field: Field name to check
            value: Field value to check

        Returns:
            True if record exists, False otherwise
        """
        if not hasattr(self.model, field):
            return False

        return (
            self.db.query(self.model)
            .filter(getattr(self.model, field) == value)
            .first() is not None
        )

    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """
        Get a record by field value.

        Args:
            field: Field name to search by
            value: Field value to search for

        Returns:
            Model instance if found, None otherwise
        """
        if not hasattr(self.model, field):
            return None

        return (
            self.db.query(self.model)
            .filter(getattr(self.model, field) == value)
            .first()
        )

    async def get_many_by_field(
        self,
        field: str,
        value: Any,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """
        Get multiple records by field value.

        Args:
            field: Field name to search by
            value: Field value to search for
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of model instances
        """
        if not hasattr(self.model, field):
            return []

        return (
            self.db.query(self.model)
            .filter(getattr(self.model, field) == value)
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def bulk_create(self, objects: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple records in bulk.

        Args:
            objects: List of dictionaries with field values

        Returns:
            List of created model instances

        Raises:
            IntegrityError: If creation violates database constraints
        """
        try:
            db_objects = [self.model(**obj) for obj in objects]
            self.db.add_all(db_objects)
            self.db.commit()

            # Refresh all objects to get generated IDs
            for obj in db_objects:
                self.db.refresh(obj)

            return db_objects
        except IntegrityError as e:
            self.db.rollback()
            raise e

    async def bulk_update(
        self,
        updates: List[Dict[str, Any]],
        id_field: str = 'id'
    ) -> List[ModelType]:
        """
        Update multiple records in bulk.

        Args:
            updates: List of dictionaries with field values and ID
            id_field: Field name to use for identifying records

        Returns:
            List of updated model instances
        """
        updated_objects = []

        try:
            for update_data in updates:
                if id_field not in update_data:
                    continue

                record_id = update_data.pop(id_field)
                db_obj = await self.get(record_id)

                if db_obj:
                    for field, value in update_data.items():
                        if hasattr(db_obj, field):
                            setattr(db_obj, field, value)
                    updated_objects.append(db_obj)

            self.db.commit()

            # Refresh all updated objects
            for obj in updated_objects:
                self.db.refresh(obj)

            return updated_objects
        except IntegrityError as e:
            self.db.rollback()
            raise e


# Create the main BaseRepository class that combines core functionality with enhanced features
try:
    from .enhanced_base import EnhancedBaseRepository
    from .bulk_operations import BulkOperationsMixin

    class BaseRepository(EnhancedBaseRepository[ModelType], BulkOperationsMixin):
        """
        Complete BaseRepository with all features.

        Combines:
        - Core CRUD operations with async support
        - Enhanced pagination and filtering
        - Bulk operations with optimization
        - Performance monitoring and error handling
        """
        pass

except ImportError:
    # Fallback to core repository if enhanced features not available
    BaseRepository = BaseRepositoryCore
