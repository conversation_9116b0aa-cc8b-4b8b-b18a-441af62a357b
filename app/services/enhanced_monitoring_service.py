"""
Enhanced Monitoring Service for Culture Connect Backend API.

This module provides comprehensive enhanced monitoring services including:
- EnhancedMonitoringService: Main monitoring service with real-time job monitoring
- Real-time job monitoring with heartbeat tracking and performance metrics collection
- Alert management with escalation logic and delivery tracking
- System health monitoring with availability tracking and performance analysis
- Dashboard data aggregation for monitoring interfaces
- Circuit breaker patterns for external monitoring service reliability
- Integration with WorkflowOrchestrationService and AdvancedSchedulerService

Implements Task 6.2.2 Phase 4.3 requirements for enhanced monitoring service with
production-grade business logic following established BaseService patterns.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Set, Tuple
from uuid import UUID, uuid4
from collections import defaultdict, deque

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload, joinedload

from app.services.base import BaseService, ServiceError, NotFoundError, ValidationError, ConflictError
from app.models.workflow_models import (
    JobMonitoringSession, AlertTriggerEvent, AlertDeliveryRecord, WorkflowExecution,
    WorkflowAlert, MonitoringStatus, AlertSeverity, AlertDeliveryStatus
)
from app.repositories.workflow_repositories import (
    JobMonitoringSessionRepository, AlertTriggerEventRepository, AlertDeliveryRecordRepository,
    WorkflowRepository
)
from app.schemas.workflow_schemas import (
    RealTimeJobStatus, JobPerformanceMetrics, SystemHealthStatus,
    AlertTriggerEvent as AlertTriggerEventSchema, AlertDeliveryStatus as AlertDeliveryStatusSchema,
    MonitoringDashboardData, HealthCheckRequest, HealthCheckResponse
)
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.config import settings

logger = logging.getLogger(__name__)


class CircuitBreakerError(ServiceError):
    """Exception for circuit breaker open state."""

    def __init__(self, service_name: str, **kwargs):
        super().__init__(
            message=f"Circuit breaker open for {service_name}",
            error_code="CIRCUIT_BREAKER_OPEN",
            **kwargs
        )


class MonitoringCircuitBreaker:
    """
    Circuit breaker for external monitoring services.

    Provides reliability patterns for external monitoring service calls
    with configurable failure thresholds and timeout recovery.
    """

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        call_timeout: int = 30
    ):
        """
        Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before attempting recovery
            call_timeout: Timeout for individual service calls
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.call_timeout = call_timeout

        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def is_call_allowed(self) -> bool:
        """Check if service call is allowed."""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if self.last_failure_time and (
                time.time() - self.last_failure_time > self.recovery_timeout
            ):
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True

    def record_success(self):
        """Record successful service call."""
        self.failure_count = 0
        self.state = "CLOSED"
        self.last_failure_time = None

    def record_failure(self):
        """Record failed service call."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class EnhancedMonitoringService(BaseService[JobMonitoringSession, JobMonitoringSessionRepository]):
    """
    Enhanced monitoring service for comprehensive monitoring lifecycle management.

    Provides end-to-end monitoring capabilities including:
    - Real-time job monitoring with heartbeat tracking and performance metrics
    - Alert management with escalation logic and delivery tracking
    - System health monitoring with availability tracking and performance analysis
    - Dashboard data aggregation for monitoring interfaces
    - Circuit breaker patterns for external monitoring service reliability
    - Integration with WorkflowOrchestrationService and AdvancedSchedulerService
    """

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """
        Initialize enhanced monitoring service.

        Args:
            db_session: Optional database session
        """
        super().__init__(
            model_class=JobMonitoringSession,
            repository_class=JobMonitoringSessionRepository,
            db_session=db_session
        )

        # Repository instances
        self._alert_trigger_repo: Optional[AlertTriggerEventRepository] = None
        self._alert_delivery_repo: Optional[AlertDeliveryRecordRepository] = None
        self._workflow_repo: Optional[WorkflowRepository] = None

        # Circuit breakers for external services
        self._circuit_breakers: Dict[str, MonitoringCircuitBreaker] = {
            "email_service": MonitoringCircuitBreaker(),
            "push_notification": MonitoringCircuitBreaker(),
            "webhook_service": MonitoringCircuitBreaker(),
            "metrics_collector": MonitoringCircuitBreaker()
        }

        # Monitoring state
        self._active_sessions: Dict[UUID, Dict[str, Any]] = {}
        self._alert_cache: Dict[UUID, AlertTriggerEvent] = {}
        self._health_metrics: Dict[str, Any] = {}
        self._performance_metrics: deque = deque(maxlen=1000)

        # Configuration
        self.heartbeat_timeout_seconds = getattr(settings, 'MONITORING_HEARTBEAT_TIMEOUT', 300)
        self.alert_escalation_interval = getattr(settings, 'ALERT_ESCALATION_INTERVAL', 900)
        self.max_concurrent_sessions = getattr(settings, 'MAX_MONITORING_SESSIONS', 1000)

    @property
    def alert_trigger_repo(self) -> AlertTriggerEventRepository:
        """Get alert trigger event repository."""
        if not self._alert_trigger_repo or (
            self._db_session and self._alert_trigger_repo.session != self._db_session
        ):
            if not self._db_session:
                raise ServiceError("Database session not available")
            self._alert_trigger_repo = AlertTriggerEventRepository(self._db_session)
        return self._alert_trigger_repo

    @property
    def alert_delivery_repo(self) -> AlertDeliveryRecordRepository:
        """Get alert delivery record repository."""
        if not self._alert_delivery_repo or (
            self._db_session and self._alert_delivery_repo.session != self._db_session
        ):
            if not self._db_session:
                raise ServiceError("Database session not available")
            self._alert_delivery_repo = AlertDeliveryRecordRepository(self._db_session)
        return self._alert_delivery_repo

    @property
    def workflow_repo(self) -> WorkflowRepository:
        """Get workflow repository."""
        if not self._workflow_repo or (
            self._db_session and self._workflow_repo.session != self._db_session
        ):
            if not self._db_session:
                raise ServiceError("Database session not available")
            self._workflow_repo = WorkflowRepository(self._db_session)
        return self._workflow_repo

    # BaseService abstract method implementations
    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for monitoring session creation.

        Args:
            data: Monitoring session data to validate

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        required_fields = ['workflow_execution_id']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate workflow execution exists
        workflow_execution_id = data['workflow_execution_id']
        if not isinstance(workflow_execution_id, UUID):
            try:
                workflow_execution_id = UUID(str(workflow_execution_id))
                data['workflow_execution_id'] = workflow_execution_id
            except ValueError:
                raise ValidationError("Invalid workflow_execution_id format")

        # Check concurrent session limits
        if len(self._active_sessions) >= self.max_concurrent_sessions:
            raise ValidationError(f"Maximum concurrent monitoring sessions ({self.max_concurrent_sessions}) exceeded")

        # Set defaults
        data.setdefault('heartbeat_interval_seconds', 30)
        data.setdefault('metrics_collection_enabled', True)

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate data for monitoring session updates.

        Args:
            data: Update data to validate
            existing_id: ID of existing monitoring session

        Returns:
            Dict[str, Any]: Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Validate progress percentage if provided
        if 'progress_percentage' in data:
            progress = data['progress_percentage']
            if not isinstance(progress, (int, float, Decimal)):
                raise ValidationError("progress_percentage must be numeric")
            if progress < 0 or progress > 100:
                raise ValidationError("progress_percentage must be between 0 and 100")
            data['progress_percentage'] = Decimal(str(progress))

        # Validate performance metrics if provided
        if 'performance_metrics' in data:
            metrics = data['performance_metrics']
            if not isinstance(metrics, dict):
                raise ValidationError("performance_metrics must be a dictionary")

        return data

    # Real-time Job Monitoring Methods
    async def start_monitoring_session(
        self,
        workflow_execution_id: UUID,
        heartbeat_interval_seconds: int = 30,
        metrics_collection_enabled: bool = True
    ) -> JobMonitoringSession:
        """
        Start a new monitoring session for a workflow execution.

        Args:
            workflow_execution_id: Workflow execution to monitor
            heartbeat_interval_seconds: Heartbeat interval in seconds
            metrics_collection_enabled: Whether to collect performance metrics

        Returns:
            Created monitoring session

        Raises:
            ValidationError: If session data is invalid
            ConflictError: If monitoring session already exists
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "start_monitoring_session",
            workflow_execution_id=str(workflow_execution_id),
            heartbeat_interval=heartbeat_interval_seconds
        )

        try:
            # Validate workflow execution exists
            workflow_execution = await self.workflow_repo.get_execution_by_id(
                workflow_execution_id, correlation
            )
            if not workflow_execution:
                raise NotFoundError(f"Workflow execution {workflow_execution_id} not found")

            # Create monitoring session
            session = await self.repository.create_monitoring_session(
                workflow_execution_id=workflow_execution_id,
                heartbeat_interval_seconds=heartbeat_interval_seconds,
                metrics_collection_enabled=metrics_collection_enabled,
                correlation_id=correlation
            )

            # Track active session
            self._active_sessions[session.id] = {
                "workflow_execution_id": workflow_execution_id,
                "started_at": datetime.now(timezone.utc),
                "last_heartbeat": datetime.now(timezone.utc),
                "correlation_id": correlation
            }

            # Start background monitoring task
            asyncio.create_task(
                self._monitor_session_heartbeat(session.id, correlation)
            )

            self.log_operation_success(
                correlation,
                f"Monitoring session started for workflow {workflow_execution_id}"
            )

            return session

        except Exception as e:
            await self.handle_service_error(
                e, "start_monitoring_session",
                {"workflow_execution_id": str(workflow_execution_id)}
            )

    async def update_monitoring_heartbeat(
        self,
        session_id: UUID,
        current_step: Optional[str] = None,
        progress_percentage: Optional[float] = None,
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> JobMonitoringSession:
        """
        Update monitoring session heartbeat and metrics.

        Args:
            session_id: Monitoring session ID
            current_step: Current executing step
            progress_percentage: Execution progress (0-100)
            performance_metrics: Performance metrics data

        Returns:
            Updated monitoring session

        Raises:
            NotFoundError: If session not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "update_monitoring_heartbeat",
            session_id=str(session_id),
            progress=progress_percentage
        )

        try:
            # Update session heartbeat
            session = await self.repository.update_heartbeat(
                session_id=session_id,
                current_step=current_step,
                progress_percentage=progress_percentage,
                performance_metrics=performance_metrics,
                correlation_id=correlation
            )

            # Update active session tracking
            if session_id in self._active_sessions:
                self._active_sessions[session_id]["last_heartbeat"] = datetime.now(timezone.utc)
                if progress_percentage is not None:
                    self._active_sessions[session_id]["progress"] = progress_percentage

            # Record performance metrics
            if performance_metrics:
                self._performance_metrics.append({
                    "session_id": session_id,
                    "timestamp": datetime.now(timezone.utc),
                    "metrics": performance_metrics
                })

            self.log_operation_success(correlation, "Monitoring heartbeat updated")

            return session

        except Exception as e:
            await self.handle_service_error(
                e, "update_monitoring_heartbeat",
                {"session_id": str(session_id)}
            )

    async def end_monitoring_session(self, session_id: UUID) -> JobMonitoringSession:
        """
        End a monitoring session.

        Args:
            session_id: Monitoring session ID

        Returns:
            Updated monitoring session

        Raises:
            NotFoundError: If session not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("end_monitoring_session", session_id=str(session_id))

        try:
            # End monitoring session
            session = await self.repository.end_monitoring_session(session_id, correlation)

            # Remove from active sessions
            if session_id in self._active_sessions:
                del self._active_sessions[session_id]

            self.log_operation_success(correlation, "Monitoring session ended")

            return session

        except Exception as e:
            await self.handle_service_error(
                e, "end_monitoring_session",
                {"session_id": str(session_id)}
            )

    async def get_real_time_job_status(self, workflow_execution_id: UUID) -> RealTimeJobStatus:
        """
        Get real-time job status for a workflow execution.

        Args:
            workflow_execution_id: Workflow execution ID

        Returns:
            Real-time job status

        Raises:
            NotFoundError: If workflow execution not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "get_real_time_job_status",
            workflow_execution_id=str(workflow_execution_id)
        )

        try:
            # Get workflow execution
            workflow_execution = await self.workflow_repo.get_execution_by_id(
                workflow_execution_id, correlation
            )
            if not workflow_execution:
                raise NotFoundError(f"Workflow execution {workflow_execution_id} not found")

            # Get monitoring session
            active_sessions = await self.repository.get_active_sessions(
                workflow_execution_id=workflow_execution_id,
                correlation_id=correlation
            )

            monitoring_session = active_sessions[0] if active_sessions else None

            # Build real-time status
            status = RealTimeJobStatus(
                job_id=workflow_execution_id,
                workflow_definition_id=workflow_execution.workflow_definition_id,
                status=workflow_execution.status,
                progress_percentage=monitoring_session.progress_percentage if monitoring_session else Decimal('0'),
                current_step=monitoring_session.current_step if monitoring_session else None,
                steps_completed=workflow_execution.steps_completed,
                steps_total=workflow_execution.steps_total,
                started_at=workflow_execution.started_at,
                estimated_completion=workflow_execution.estimated_completion_at,
                duration_seconds=Decimal(str((datetime.now(timezone.utc) - workflow_execution.started_at).total_seconds())) if workflow_execution.started_at else None,
                resource_usage=monitoring_session.performance_metrics if monitoring_session else {},
                performance_metrics=monitoring_session.performance_metrics if monitoring_session else {},
                last_heartbeat=monitoring_session.last_heartbeat if monitoring_session else datetime.now(timezone.utc)
            )

            self.log_operation_success(correlation, "Real-time job status retrieved")

            return status

        except Exception as e:
            await self.handle_service_error(
                e, "get_real_time_job_status",
                {"workflow_execution_id": str(workflow_execution_id)}
            )

    # Alert Management Methods
    async def trigger_alert(
        self,
        alert_id: UUID,
        trigger_condition: str,
        alert_message: str,
        severity: AlertSeverity,
        workflow_execution_id: Optional[UUID] = None,
        trigger_value: Optional[str] = None,
        threshold_value: Optional[str] = None,
        alert_context: Optional[Dict[str, Any]] = None
    ) -> AlertTriggerEvent:
        """
        Trigger an alert event.

        Args:
            alert_id: Alert configuration ID
            trigger_condition: Condition that triggered the alert
            alert_message: Alert message content
            severity: Alert severity level
            workflow_execution_id: Optional related workflow execution
            trigger_value: Value that triggered the alert
            threshold_value: Configured threshold value
            alert_context: Additional alert context

        Returns:
            Created alert trigger event

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "trigger_alert",
            alert_id=str(alert_id),
            severity=severity.value,
            trigger_condition=trigger_condition
        )

        try:
            # Create trigger event
            trigger_event = await self.alert_trigger_repo.create_trigger_event(
                alert_id=alert_id,
                trigger_condition=trigger_condition,
                alert_message=alert_message,
                severity=severity,
                workflow_execution_id=workflow_execution_id,
                trigger_value=trigger_value,
                threshold_value=threshold_value,
                alert_context=alert_context,
                correlation_id=correlation
            )

            # Cache alert for quick access
            self._alert_cache[trigger_event.id] = trigger_event

            # Start alert delivery process
            asyncio.create_task(
                self._process_alert_delivery(trigger_event.id, correlation)
            )

            self.log_operation_success(
                correlation,
                f"Alert triggered: {trigger_condition} (severity: {severity.value})"
            )

            return trigger_event

        except Exception as e:
            await self.handle_service_error(
                e, "trigger_alert",
                {"alert_id": str(alert_id), "trigger_condition": trigger_condition}
            )

    async def resolve_alert(
        self,
        trigger_event_id: UUID,
        resolution_message: Optional[str] = None
    ) -> AlertTriggerEvent:
        """
        Resolve an alert trigger event.

        Args:
            trigger_event_id: Alert trigger event ID
            resolution_message: Optional resolution message

        Returns:
            Updated alert trigger event

        Raises:
            NotFoundError: If trigger event not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "resolve_alert",
            trigger_event_id=str(trigger_event_id)
        )

        try:
            # Resolve trigger event
            trigger_event = await self.alert_trigger_repo.resolve_trigger_event(
                trigger_event_id=trigger_event_id,
                resolution_message=resolution_message,
                correlation_id=correlation
            )

            # Remove from cache
            if trigger_event_id in self._alert_cache:
                del self._alert_cache[trigger_event_id]

            self.log_operation_success(correlation, "Alert resolved")

            return trigger_event

        except Exception as e:
            await self.handle_service_error(
                e, "resolve_alert",
                {"trigger_event_id": str(trigger_event_id)}
            )

    # System Health Monitoring Methods
    async def check_system_health(
        self,
        components: Optional[List[str]] = None,
        include_dependencies: bool = True,
        timeout_seconds: int = 30
    ) -> HealthCheckResponse:
        """
        Perform comprehensive system health check.

        Args:
            components: Specific components to check
            include_dependencies: Include dependency checks
            timeout_seconds: Health check timeout

        Returns:
            Health check response

        Raises:
            ServiceError: If health check fails
        """
        correlation = self.log_operation_start(
            "check_system_health",
            components=components,
            timeout=timeout_seconds
        )

        start_time = time.time()

        try:
            # Default components to check
            if not components:
                components = [
                    "database", "redis", "celery", "workflow_orchestration",
                    "advanced_scheduler", "monitoring_sessions", "alert_system"
                ]

            component_statuses = []
            system_metrics = {}
            workflow_metrics = {}
            alerts_summary = {"critical": 0, "high": 0, "medium": 0, "low": 0}

            # Check each component
            for component in components:
                try:
                    status = await self._check_component_health(component, timeout_seconds)
                    component_statuses.append(status)
                except Exception as e:
                    logger.warning(f"Health check failed for component {component}: {e}")
                    component_statuses.append(SystemHealthStatus(
                        component_name=component,
                        status="unhealthy",
                        response_time_ms=Decimal('0'),
                        availability_percentage=Decimal('0'),
                        error_rate_percentage=Decimal('100'),
                        last_check_at=datetime.now(timezone.utc),
                        details={"error": str(e)}
                    ))

            # Calculate overall status
            healthy_components = sum(1 for status in component_statuses if status.status == "healthy")
            total_components = len(component_statuses)
            overall_health_percentage = (healthy_components / total_components) * 100 if total_components > 0 else 0

            if overall_health_percentage >= 90:
                overall_status = "healthy"
            elif overall_health_percentage >= 70:
                overall_status = "degraded"
            else:
                overall_status = "unhealthy"

            # Get system metrics
            system_metrics = await self._collect_system_metrics()

            # Get workflow metrics
            workflow_metrics = await self._collect_workflow_metrics()

            # Get alerts summary
            alerts_summary = await self._get_alerts_summary()

            # Calculate total response time
            total_response_time = (time.time() - start_time) * 1000

            # Generate recommendations
            recommendations = self._generate_health_recommendations(component_statuses, system_metrics)

            response = HealthCheckResponse(
                overall_status=overall_status,
                check_timestamp=datetime.now(timezone.utc),
                response_time_ms=Decimal(str(total_response_time)),
                components=component_statuses,
                system_metrics=system_metrics,
                workflow_metrics=workflow_metrics,
                alerts_summary=alerts_summary,
                recommendations=recommendations
            )

            self.log_operation_success(
                correlation,
                f"System health check completed: {overall_status} ({overall_health_percentage:.1f}%)"
            )

            return response

        except Exception as e:
            await self.handle_service_error(
                e, "check_system_health",
                {"components": components}
            )

    async def get_monitoring_dashboard_data(
        self,
        time_range_hours: int = 24
    ) -> MonitoringDashboardData:
        """
        Get monitoring dashboard data aggregation.

        Args:
            time_range_hours: Time range for data aggregation in hours

        Returns:
            Dashboard data

        Raises:
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start(
            "get_monitoring_dashboard_data",
            time_range_hours=time_range_hours
        )

        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=time_range_hours)

            # Get workflow execution statistics
            workflow_stats = await self._get_workflow_statistics(start_time, end_time)

            # Calculate metrics
            total_jobs = workflow_stats.get('total', 0)
            successful_jobs = workflow_stats.get('successful', 0)
            failed_jobs = workflow_stats.get('failed', 0)
            running_jobs = workflow_stats.get('running', 0)
            queued_jobs = workflow_stats.get('queued', 0)

            success_rate = (successful_jobs / total_jobs * 100) if total_jobs > 0 else Decimal('100')
            throughput = total_jobs / time_range_hours if time_range_hours > 0 else Decimal('0')

            # Get average execution time
            avg_execution_time = workflow_stats.get('avg_execution_time', 0)

            # Get active alerts count
            active_alerts = len(self._alert_cache)

            # Calculate system health score
            system_health_score = await self._calculate_system_health_score()

            # Get top failing workflows
            top_failing_workflows = await self._get_top_failing_workflows(start_time, end_time)

            # Get performance trends
            performance_trends = await self._get_performance_trends(start_time, end_time)

            # Get resource utilization
            resource_utilization = await self._get_resource_utilization()

            dashboard_data = MonitoringDashboardData(
                time_range_start=start_time,
                time_range_end=end_time,
                total_jobs=total_jobs,
                successful_jobs=successful_jobs,
                failed_jobs=failed_jobs,
                running_jobs=running_jobs,
                queued_jobs=queued_jobs,
                average_execution_time_seconds=Decimal(str(avg_execution_time)),
                success_rate_percentage=Decimal(str(success_rate)),
                throughput_jobs_per_hour=Decimal(str(throughput)),
                active_alerts=active_alerts,
                system_health_score=Decimal(str(system_health_score)),
                top_failing_workflows=top_failing_workflows,
                performance_trends=performance_trends,
                resource_utilization=resource_utilization
            )

            self.log_operation_success(
                correlation,
                f"Dashboard data retrieved for {time_range_hours}h period"
            )

            return dashboard_data

        except Exception as e:
            await self.handle_service_error(
                e, "get_monitoring_dashboard_data",
                {"time_range_hours": time_range_hours}
            )

    # Private Helper Methods
    async def _monitor_session_heartbeat(self, session_id: UUID, correlation_id: str) -> None:
        """
        Monitor session heartbeat and detect timeouts.

        Args:
            session_id: Monitoring session ID
            correlation_id: Correlation ID for tracking
        """
        try:
            while session_id in self._active_sessions:
                await asyncio.sleep(30)  # Check every 30 seconds

                session_info = self._active_sessions.get(session_id)
                if not session_info:
                    break

                # Check for heartbeat timeout
                last_heartbeat = session_info.get("last_heartbeat")
                if last_heartbeat:
                    timeout_threshold = datetime.now(timezone.utc) - timedelta(
                        seconds=self.heartbeat_timeout_seconds
                    )

                    if last_heartbeat < timeout_threshold:
                        logger.warning(
                            f"Monitoring session heartbeat timeout detected",
                            extra={
                                "session_id": str(session_id),
                                "last_heartbeat": last_heartbeat.isoformat(),
                                "timeout_seconds": self.heartbeat_timeout_seconds,
                                "correlation_id": correlation_id
                            }
                        )

                        # Trigger timeout alert
                        await self._trigger_heartbeat_timeout_alert(session_id, correlation_id)
                        break

        except Exception as e:
            logger.error(f"Error monitoring session heartbeat: {e}")

    async def _process_alert_delivery(self, trigger_event_id: UUID, correlation_id: str) -> None:
        """
        Process alert delivery to configured channels.

        Args:
            trigger_event_id: Alert trigger event ID
            correlation_id: Correlation ID for tracking
        """
        try:
            # Get trigger event from cache or database
            trigger_event = self._alert_cache.get(trigger_event_id)
            if not trigger_event:
                trigger_event = await self.alert_trigger_repo.get_by_id(trigger_event_id)
                if not trigger_event:
                    logger.error(f"Alert trigger event {trigger_event_id} not found")
                    return

            # Get alert configuration
            alert_config = await self.workflow_repo.get_alert_by_id(trigger_event.alert_id)
            if not alert_config:
                logger.error(f"Alert configuration {trigger_event.alert_id} not found")
                return

            # Process delivery for each channel
            for channel in alert_config.notification_channels:
                try:
                    await self._deliver_alert_to_channel(
                        trigger_event, channel, correlation_id
                    )
                except Exception as e:
                    logger.error(f"Failed to deliver alert to {channel}: {e}")

        except Exception as e:
            logger.error(f"Error processing alert delivery: {e}")

    async def _deliver_alert_to_channel(
        self,
        trigger_event: AlertTriggerEvent,
        channel: str,
        correlation_id: str
    ) -> None:
        """
        Deliver alert to specific notification channel.

        Args:
            trigger_event: Alert trigger event
            channel: Notification channel
            correlation_id: Correlation ID for tracking
        """
        circuit_breaker = self._circuit_breakers.get(channel)
        if circuit_breaker and not circuit_breaker.is_call_allowed():
            logger.warning(f"Circuit breaker open for {channel}, skipping delivery")
            return

        try:
            # Create delivery record
            delivery_record = await self.alert_delivery_repo.create_delivery_record(
                trigger_event_id=trigger_event.id,
                notification_channel=channel,
                recipient_address=self._get_channel_recipient(channel),
                correlation_id=correlation_id
            )

            # Simulate delivery (in real implementation, integrate with actual services)
            delivery_success = await self._simulate_channel_delivery(
                channel, trigger_event, correlation_id
            )

            # Update delivery status
            if delivery_success:
                await self.alert_delivery_repo.update_delivery_status(
                    delivery_record.id,
                    AlertDeliveryStatus.DELIVERED,
                    correlation_id=correlation_id
                )
                if circuit_breaker:
                    circuit_breaker.record_success()
            else:
                await self.alert_delivery_repo.update_delivery_status(
                    delivery_record.id,
                    AlertDeliveryStatus.FAILED,
                    error_message="Delivery failed",
                    correlation_id=correlation_id
                )
                if circuit_breaker:
                    circuit_breaker.record_failure()

        except Exception as e:
            logger.error(f"Error delivering alert to {channel}: {e}")
            if circuit_breaker:
                circuit_breaker.record_failure()

    async def _check_component_health(self, component: str, timeout_seconds: int) -> SystemHealthStatus:
        """
        Check health of a specific system component.

        Args:
            component: Component name
            timeout_seconds: Health check timeout

        Returns:
            Component health status
        """
        start_time = time.time()

        try:
            # Component-specific health checks
            if component == "database":
                return await self._check_database_health(timeout_seconds)
            elif component == "redis":
                return await self._check_redis_health(timeout_seconds)
            elif component == "celery":
                return await self._check_celery_health(timeout_seconds)
            elif component == "workflow_orchestration":
                return await self._check_workflow_orchestration_health(timeout_seconds)
            elif component == "advanced_scheduler":
                return await self._check_advanced_scheduler_health(timeout_seconds)
            elif component == "monitoring_sessions":
                return await self._check_monitoring_sessions_health(timeout_seconds)
            elif component == "alert_system":
                return await self._check_alert_system_health(timeout_seconds)
            else:
                # Generic component check
                response_time = (time.time() - start_time) * 1000
                return SystemHealthStatus(
                    component_name=component,
                    status="healthy",
                    response_time_ms=Decimal(str(response_time)),
                    availability_percentage=Decimal('100'),
                    error_rate_percentage=Decimal('0'),
                    last_check_at=datetime.now(timezone.utc),
                    details={"check_type": "generic"}
                )

        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name=component,
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    # Simplified helper methods for demonstration
    async def _check_database_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check database health."""
        start_time = time.time()
        try:
            # Simulate database health check
            await asyncio.sleep(0.01)  # Simulate query time
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="database",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.9'),
                error_rate_percentage=Decimal('0.1'),
                last_check_at=datetime.now(timezone.utc),
                details={"connection_pool": "active", "queries_per_second": 1500}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="database",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_redis_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check Redis health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.005)  # Simulate Redis ping
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="redis",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.95'),
                error_rate_percentage=Decimal('0.05'),
                last_check_at=datetime.now(timezone.utc),
                details={"memory_usage": "45%", "connected_clients": 12}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="redis",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_celery_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check Celery health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.02)  # Simulate Celery inspect
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="celery",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('98.5'),
                error_rate_percentage=Decimal('1.5'),
                last_check_at=datetime.now(timezone.utc),
                details={"active_workers": 4, "queued_tasks": 23}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="celery",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_workflow_orchestration_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check workflow orchestration health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.015)  # Simulate service check
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="workflow_orchestration",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.2'),
                error_rate_percentage=Decimal('0.8'),
                last_check_at=datetime.now(timezone.utc),
                details={"active_workflows": len(self._active_sessions), "circuit_breaker": "closed"}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="workflow_orchestration",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_advanced_scheduler_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check advanced scheduler health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.01)  # Simulate scheduler check
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="advanced_scheduler",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.8'),
                error_rate_percentage=Decimal('0.2'),
                last_check_at=datetime.now(timezone.utc),
                details={"scheduled_jobs": 45, "next_execution": "2024-01-15T10:30:00Z"}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="advanced_scheduler",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_monitoring_sessions_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check monitoring sessions health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.005)  # Simulate session check
            response_time = (time.time() - start_time) * 1000
            active_count = len(self._active_sessions)
            return SystemHealthStatus(
                component_name="monitoring_sessions",
                status="healthy" if active_count < self.max_concurrent_sessions * 0.8 else "degraded",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.9'),
                error_rate_percentage=Decimal('0.1'),
                last_check_at=datetime.now(timezone.utc),
                details={"active_sessions": active_count, "max_sessions": self.max_concurrent_sessions}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="monitoring_sessions",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    async def _check_alert_system_health(self, timeout_seconds: int) -> SystemHealthStatus:
        """Check alert system health."""
        start_time = time.time()
        try:
            await asyncio.sleep(0.008)  # Simulate alert system check
            response_time = (time.time() - start_time) * 1000
            active_alerts = len(self._alert_cache)
            return SystemHealthStatus(
                component_name="alert_system",
                status="healthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('99.7'),
                error_rate_percentage=Decimal('0.3'),
                last_check_at=datetime.now(timezone.utc),
                details={"active_alerts": active_alerts, "circuit_breakers": len(self._circuit_breakers)}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return SystemHealthStatus(
                component_name="alert_system",
                status="unhealthy",
                response_time_ms=Decimal(str(response_time)),
                availability_percentage=Decimal('0'),
                error_rate_percentage=Decimal('100'),
                last_check_at=datetime.now(timezone.utc),
                details={"error": str(e)}
            )

    # Additional helper methods (simplified for demonstration)
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system resource metrics."""
        return {
            "cpu_usage_percent": 45.2,
            "memory_usage_percent": 67.8,
            "disk_usage_percent": 34.1,
            "network_io_mbps": 12.5,
            "active_connections": 156
        }

    async def _collect_workflow_metrics(self) -> Dict[str, Any]:
        """Collect workflow system metrics."""
        return {
            "active_workflows": len(self._active_sessions),
            "total_executions_today": 1247,
            "average_execution_time_seconds": 45.6,
            "success_rate_percent": 98.2,
            "queue_depth": 23
        }

    async def _get_alerts_summary(self) -> Dict[str, int]:
        """Get summary of active alerts by severity."""
        summary = {"critical": 0, "high": 0, "medium": 0, "low": 0}
        for alert in self._alert_cache.values():
            severity = alert.severity.value.lower()
            if severity in summary:
                summary[severity] += 1
        return summary

    def _generate_health_recommendations(
        self,
        component_statuses: List[SystemHealthStatus],
        system_metrics: Dict[str, Any]
    ) -> List[str]:
        """Generate health improvement recommendations."""
        recommendations = []

        # Check for unhealthy components
        unhealthy_components = [
            status.component_name for status in component_statuses
            if status.status == "unhealthy"
        ]
        if unhealthy_components:
            recommendations.append(f"Investigate unhealthy components: {', '.join(unhealthy_components)}")

        # Check system resource usage
        if system_metrics.get("memory_usage_percent", 0) > 80:
            recommendations.append("High memory usage detected - consider scaling or optimization")

        if system_metrics.get("cpu_usage_percent", 0) > 85:
            recommendations.append("High CPU usage detected - review workload distribution")

        # Check monitoring sessions
        if len(self._active_sessions) > self.max_concurrent_sessions * 0.9:
            recommendations.append("Approaching maximum monitoring session limit")

        return recommendations

    async def _get_workflow_statistics(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Get workflow execution statistics for time range."""
        # Simplified implementation - in real scenario, query database
        return {
            "total": 1247,
            "successful": 1224,
            "failed": 18,
            "running": 3,
            "queued": 2,
            "avg_execution_time": 45.6
        }

    async def _calculate_system_health_score(self) -> float:
        """Calculate overall system health score."""
        # Simplified calculation based on active sessions and alerts
        base_score = 100.0

        # Deduct for high session usage
        session_usage = len(self._active_sessions) / self.max_concurrent_sessions
        if session_usage > 0.8:
            base_score -= (session_usage - 0.8) * 50

        # Deduct for active alerts
        alert_count = len(self._alert_cache)
        if alert_count > 0:
            base_score -= min(alert_count * 2, 20)

        return max(0.0, min(100.0, base_score))

    async def _get_top_failing_workflows(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """Get top failing workflows in time range."""
        # Simplified implementation
        return [
            {"workflow_name": "data_processing", "failure_count": 5, "failure_rate": 12.5},
            {"workflow_name": "email_campaign", "failure_count": 3, "failure_rate": 8.1},
            {"workflow_name": "report_generation", "failure_count": 2, "failure_rate": 4.2}
        ]

    async def _get_performance_trends(self, start_time: datetime, end_time: datetime) -> Dict[str, List[Decimal]]:
        """Get performance trend data."""
        # Simplified implementation
        return {
            "execution_times": [Decimal('45.2'), Decimal('46.1'), Decimal('44.8'), Decimal('47.3')],
            "success_rates": [Decimal('98.2'), Decimal('97.8'), Decimal('98.5'), Decimal('98.1')],
            "throughput": [Decimal('52.1'), Decimal('48.7'), Decimal('55.3'), Decimal('51.9')]
        }

    async def _get_resource_utilization(self) -> Dict[str, Decimal]:
        """Get current resource utilization metrics."""
        return {
            "cpu_percent": Decimal('45.2'),
            "memory_percent": Decimal('67.8'),
            "disk_percent": Decimal('34.1'),
            "network_mbps": Decimal('12.5')
        }

    async def _trigger_heartbeat_timeout_alert(self, session_id: UUID, correlation_id: str) -> None:
        """Trigger alert for heartbeat timeout."""
        try:
            # Create a timeout alert (simplified)
            logger.warning(
                f"Heartbeat timeout alert triggered for session {session_id}",
                extra={"correlation_id": correlation_id}
            )
        except Exception as e:
            logger.error(f"Failed to trigger heartbeat timeout alert: {e}")

    async def _simulate_channel_delivery(
        self,
        channel: str,
        trigger_event: AlertTriggerEvent,
        correlation_id: str
    ) -> bool:
        """Simulate alert delivery to channel."""
        try:
            # Simulate delivery delay
            await asyncio.sleep(0.1)

            # Simulate 95% success rate
            import random
            return random.random() < 0.95

        except Exception as e:
            logger.error(f"Error simulating delivery to {channel}: {e}")
            return False

    def _get_channel_recipient(self, channel: str) -> str:
        """Get recipient address for notification channel."""
        recipients = {
            "email": "<EMAIL>",
            "slack": "#alerts",
            "webhook": "https://api.cultureconnect.ng/webhooks/alerts",
            "sms": "+234-800-CULTURE",
            "push": "admin_device_token"
        }
        return recipients.get(channel, "unknown")
