"""
Real-time Synchronization repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for real-time synchronization including:
- DataSyncRecordRepository: Sync operation management with Redis caching and conflict resolution
- SyncConflictRepository: Conflict tracking and resolution with <50ms response times
- SyncBatchRepository: Batch processing management with priority-based queuing
- SyncMetricsRepository: Performance analytics and aggregation with bulk operations support

Implements Task 6.1.2 Phase 2 requirements for real-time synchronization repositories with:
- Redis caching integration with >90% hit rate for sync operations
- Query optimization with <200ms complex queries and <50ms simple lookups
- Bulk operations support for >1000 records/second processing
- Integration with existing WebSocket infrastructure and caching patterns
- Performance monitoring and comprehensive error handling
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import uuid4

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base import BaseRepository, RepositoryError, QueryPerformanceMetrics
from app.repositories.enhanced_base import EnhancedBaseRepository
from app.repositories.bulk_operations import BulkOperationsMixin
from app.models.sync_models import (
    DataSyncRecord, SyncConflict, SyncBatch, SyncMetrics,
    SyncStatus, ConflictResolutionStrategy, SyncPriority, ChangeType, SyncScope
)
from app.core.cache import CacheManager
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


class DataSyncRecordRepository(EnhancedBaseRepository[DataSyncRecord], BulkOperationsMixin):
    """
    Repository for data synchronization record management with Redis caching.

    Provides sync operation lifecycle management, conflict resolution tracking,
    and performance optimization with <200ms query targets and >90% cache hit rate.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(DataSyncRecord, db)
        self.cache = cache_manager
        self._cache_ttl = 600  # 10 minutes for sync records
        self._pending_cache_ttl = 60  # 1 minute for pending syncs
        self._metrics_cache_ttl = 3600  # 1 hour for metrics

    async def create_sync_record(self, sync_data: Dict[str, Any]) -> DataSyncRecord:
        """
        Create a new sync record with automatic sync_id generation.

        Args:
            sync_data: Sync record creation data

        Returns:
            Created sync record

        Raises:
            RepositoryError: If creation fails
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Generate unique sync_id if not provided
            if 'sync_id' not in sync_data:
                sync_data['sync_id'] = str(uuid4())

            # Set default timestamps
            if 'initiated_at' not in sync_data:
                sync_data['initiated_at'] = datetime.now(timezone.utc)

            # Calculate data delta if both before and after are provided
            if 'data_before' in sync_data and 'data_after' in sync_data:
                sync_data['data_delta'] = self._calculate_data_delta(
                    sync_data['data_before'], sync_data['data_after']
                )

            sync_record = await self.create(sync_data)

            # Cache the new sync record
            if self.cache:
                cache_key = f"sync_record:{sync_record.sync_id}"
                await self.cache.set(cache_key, sync_record, self._cache_ttl)

                # Update pending syncs cache
                await self._update_pending_syncs_cache()

            query_time = asyncio.get_event_loop().time() - start_time
            logger.info(
                f"Created sync record {sync_record.sync_id} in {query_time:.3f}s",
                extra={"correlation_id": correlation_id.get()}
            )

            return sync_record

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            logger.error(
                f"Failed to create sync record in {query_time:.3f}s: {str(e)}",
                extra={"correlation_id": correlation_id.get()}
            )
            raise RepositoryError(f"Failed to create sync record", e)

    async def get_by_sync_id(self, sync_id: str) -> Optional[DataSyncRecord]:
        """
        Get sync record by sync_id with caching.

        Args:
            sync_id: Unique sync identifier

        Returns:
            Sync record if found, None otherwise
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"sync_record:{sync_id}"
                cached_record = await self.cache.get(cache_key)
                if cached_record:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for sync record {sync_id} in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_record

            # Query database
            stmt = select(DataSyncRecord).where(DataSyncRecord.sync_id == sync_id)
            result = await self.db.execute(stmt)
            sync_record = result.scalar_one_or_none()

            # Cache the result
            if sync_record and self.cache:
                cache_key = f"sync_record:{sync_id}"
                await self.cache.set(cache_key, sync_record, self._cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_sync_id", query_time, 1 if sync_record else 0)

            return sync_record

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_sync_id_error", query_time, 0)
            raise RepositoryError(f"Failed to get sync record by sync_id {sync_id}", e)

    async def get_pending_syncs(
        self,
        priority: Optional[SyncPriority] = None,
        target_scope: Optional[SyncScope] = None,
        limit: int = 100
    ) -> List[DataSyncRecord]:
        """
        Get pending sync records with priority and scope filtering.

        Args:
            priority: Optional priority filter
            target_scope: Optional scope filter
            limit: Maximum number of records to return

        Returns:
            List of pending sync records
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first for common queries
            cache_key = f"pending_syncs:{priority}:{target_scope}:{limit}"
            if self.cache:
                cached_syncs = await self.cache.get(cache_key)
                if cached_syncs:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for pending syncs in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_syncs

            # Build query
            stmt = select(DataSyncRecord).where(
                DataSyncRecord.status == SyncStatus.PENDING
            )

            if priority:
                stmt = stmt.where(DataSyncRecord.priority == priority)

            if target_scope:
                stmt = stmt.where(DataSyncRecord.target_scope == target_scope)

            # Order by priority and initiation time
            stmt = stmt.order_by(
                DataSyncRecord.priority.desc(),  # Critical first
                DataSyncRecord.initiated_at.asc()  # Oldest first
            ).limit(limit)

            result = await self.db.execute(stmt)
            pending_syncs = result.scalars().all()

            # Cache the result
            if self.cache:
                await self.cache.set(cache_key, pending_syncs, self._pending_cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_pending_syncs", query_time, len(pending_syncs))

            return pending_syncs

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_pending_syncs_error", query_time, 0)
            raise RepositoryError(f"Failed to get pending syncs", e)

    async def update_sync_status(
        self,
        sync_id: str,
        status: SyncStatus,
        processing_time_ms: Optional[float] = None,
        error_message: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update sync record status with performance tracking.

        Args:
            sync_id: Sync identifier
            status: New status
            processing_time_ms: Processing time in milliseconds
            error_message: Error message if failed
            error_details: Detailed error information

        Returns:
            True if updated successfully
        """
        start_time = asyncio.get_event_loop().time()

        try:
            update_data = {
                'status': status,
                'updated_at': datetime.now(timezone.utc)
            }

            if status == SyncStatus.IN_PROGRESS:
                update_data['started_at'] = datetime.now(timezone.utc)
            elif status in [SyncStatus.COMPLETED, SyncStatus.FAILED, SyncStatus.CONFLICT]:
                update_data['completed_at'] = datetime.now(timezone.utc)
                if processing_time_ms is not None:
                    update_data['processing_time_ms'] = processing_time_ms

            if status == SyncStatus.FAILED:
                update_data['error_message'] = error_message
                update_data['error_details'] = error_details

            stmt = (
                update(DataSyncRecord)
                .where(DataSyncRecord.sync_id == sync_id)
                .values(**update_data)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            success = result.rowcount > 0

            # Invalidate cache
            if success and self.cache:
                cache_key = f"sync_record:{sync_id}"
                await self.cache.delete(cache_key)
                await self._update_pending_syncs_cache()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("update_sync_status", query_time, 1 if success else 0)

            return success

        except Exception as e:
            await self.db.rollback()
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("update_sync_status_error", query_time, 0)
            raise RepositoryError(f"Failed to update sync status for {sync_id}", e)

    async def get_syncs_by_entity(
        self,
        entity_type: str,
        entity_id: str,
        limit: int = 50
    ) -> List[DataSyncRecord]:
        """
        Get sync records for a specific entity.

        Args:
            entity_type: Type of entity
            entity_id: Entity identifier
            limit: Maximum number of records

        Returns:
            List of sync records for the entity
        """
        start_time = asyncio.get_event_loop().time()

        try:
            stmt = (
                select(DataSyncRecord)
                .where(
                    and_(
                        DataSyncRecord.entity_type == entity_type,
                        DataSyncRecord.entity_id == entity_id
                    )
                )
                .order_by(DataSyncRecord.initiated_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            sync_records = result.scalars().all()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_syncs_by_entity", query_time, len(sync_records))

            return sync_records

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_syncs_by_entity_error", query_time, 0)
            raise RepositoryError(f"Failed to get syncs for entity {entity_type}:{entity_id}", e)

    async def get_failed_syncs_for_retry(self, max_retries: int = 3) -> List[DataSyncRecord]:
        """
        Get failed sync records that can be retried.

        Args:
            max_retries: Maximum retry attempts

        Returns:
            List of sync records eligible for retry
        """
        start_time = asyncio.get_event_loop().time()

        try:
            current_time = datetime.now(timezone.utc)

            stmt = (
                select(DataSyncRecord)
                .where(
                    and_(
                        DataSyncRecord.status == SyncStatus.FAILED,
                        DataSyncRecord.retry_count < max_retries,
                        or_(
                            DataSyncRecord.next_retry_at.is_(None),
                            DataSyncRecord.next_retry_at <= current_time
                        )
                    )
                )
                .order_by(DataSyncRecord.initiated_at.asc())
                .limit(100)
            )

            result = await self.db.execute(stmt)
            retry_syncs = result.scalars().all()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_failed_syncs_for_retry", query_time, len(retry_syncs))

            return retry_syncs

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_failed_syncs_for_retry_error", query_time, 0)
            raise RepositoryError(f"Failed to get failed syncs for retry", e)

    def _calculate_data_delta(self, data_before: Dict[str, Any], data_after: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate the delta between before and after data states.

        Args:
            data_before: Data state before change
            data_after: Data state after change

        Returns:
            Dictionary containing the changes
        """
        delta = {}

        # Find changed fields
        for key, after_value in data_after.items():
            before_value = data_before.get(key)
            if before_value != after_value:
                delta[key] = {
                    'before': before_value,
                    'after': after_value
                }

        # Find removed fields
        for key in data_before:
            if key not in data_after:
                delta[key] = {
                    'before': data_before[key],
                    'after': None
                }

        return delta

    async def _update_pending_syncs_cache(self) -> None:
        """Update pending syncs cache after modifications."""
        if not self.cache:
            return

        try:
            # Invalidate common pending sync cache keys
            cache_patterns = [
                "pending_syncs:*",
            ]

            for pattern in cache_patterns:
                await self.cache.delete_pattern(pattern)

        except Exception as e:
            logger.warning(f"Failed to update pending syncs cache: {str(e)}")


class SyncConflictRepository(EnhancedBaseRepository[SyncConflict], BulkOperationsMixin):
    """
    Repository for sync conflict management with resolution tracking.

    Provides conflict detection, resolution tracking, and performance optimization
    with <50ms response times for conflict operations.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(SyncConflict, db)
        self.cache = cache_manager
        self._cache_ttl = 300  # 5 minutes for conflicts
        self._resolution_cache_ttl = 1800  # 30 minutes for resolved conflicts

    async def create_conflict(self, conflict_data: Dict[str, Any]) -> SyncConflict:
        """
        Create a new sync conflict with automatic conflict_id generation.

        Args:
            conflict_data: Conflict creation data

        Returns:
            Created conflict record
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Generate unique conflict_id if not provided
            if 'conflict_id' not in conflict_data:
                conflict_data['conflict_id'] = str(uuid4())

            conflict = await self.create(conflict_data)

            # Cache the new conflict
            if self.cache:
                cache_key = f"sync_conflict:{conflict.conflict_id}"
                await self.cache.set(cache_key, conflict, self._cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            logger.info(
                f"Created sync conflict {conflict.conflict_id} in {query_time:.3f}s",
                extra={"correlation_id": correlation_id.get()}
            )

            return conflict

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            logger.error(
                f"Failed to create sync conflict in {query_time:.3f}s: {str(e)}",
                extra={"correlation_id": correlation_id.get()}
            )
            raise RepositoryError(f"Failed to create sync conflict", e)

    async def get_by_conflict_id(self, conflict_id: str) -> Optional[SyncConflict]:
        """
        Get conflict by conflict_id with caching.

        Args:
            conflict_id: Unique conflict identifier

        Returns:
            Conflict record if found, None otherwise
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"sync_conflict:{conflict_id}"
                cached_conflict = await self.cache.get(cache_key)
                if cached_conflict:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for conflict {conflict_id} in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_conflict

            # Query database
            stmt = select(SyncConflict).where(SyncConflict.conflict_id == conflict_id)
            result = await self.db.execute(stmt)
            conflict = result.scalar_one_or_none()

            # Cache the result
            if conflict and self.cache:
                cache_key = f"sync_conflict:{conflict_id}"
                ttl = self._resolution_cache_ttl if conflict.resolved_at else self._cache_ttl
                await self.cache.set(cache_key, conflict, ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_conflict_id", query_time, 1 if conflict else 0)

            return conflict

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_conflict_id_error", query_time, 0)
            raise RepositoryError(f"Failed to get conflict by conflict_id {conflict_id}", e)

    async def get_conflicts_by_sync_record(self, sync_record_id: int) -> List[SyncConflict]:
        """
        Get all conflicts for a sync record.

        Args:
            sync_record_id: Sync record ID

        Returns:
            List of conflicts for the sync record
        """
        start_time = asyncio.get_event_loop().time()

        try:
            stmt = (
                select(SyncConflict)
                .where(SyncConflict.sync_record_id == sync_record_id)
                .order_by(SyncConflict.created_at.desc())
            )

            result = await self.db.execute(stmt)
            conflicts = result.scalars().all()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_conflicts_by_sync_record", query_time, len(conflicts))

            return conflicts

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_conflicts_by_sync_record_error", query_time, 0)
            raise RepositoryError(f"Failed to get conflicts for sync record {sync_record_id}", e)

    async def resolve_conflict(
        self,
        conflict_id: str,
        resolution_strategy: ConflictResolutionStrategy,
        resolution_data: Dict[str, Any],
        resolved_by_user_id: Optional[int] = None,
        resolution_time_ms: Optional[float] = None
    ) -> bool:
        """
        Mark conflict as resolved with resolution details.

        Args:
            conflict_id: Conflict identifier
            resolution_strategy: Strategy used for resolution
            resolution_data: Resolution data and merged result
            resolved_by_user_id: User who resolved the conflict
            resolution_time_ms: Time taken to resolve in milliseconds

        Returns:
            True if resolved successfully
        """
        start_time = asyncio.get_event_loop().time()

        try:
            update_data = {
                'resolution_strategy': resolution_strategy,
                'resolution_data': resolution_data,
                'resolved_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }

            if resolved_by_user_id:
                update_data['resolved_by_user_id'] = resolved_by_user_id

            if resolution_time_ms is not None:
                update_data['resolution_time_ms'] = resolution_time_ms

            stmt = (
                update(SyncConflict)
                .where(SyncConflict.conflict_id == conflict_id)
                .values(**update_data)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            success = result.rowcount > 0

            # Update cache with longer TTL for resolved conflicts
            if success and self.cache:
                cache_key = f"sync_conflict:{conflict_id}"
                await self.cache.delete(cache_key)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("resolve_conflict", query_time, 1 if success else 0)

            return success

        except Exception as e:
            await self.db.rollback()
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("resolve_conflict_error", query_time, 0)
            raise RepositoryError(f"Failed to resolve conflict {conflict_id}", e)

    async def get_unresolved_conflicts(
        self,
        conflict_type: Optional[str] = None,
        limit: int = 100
    ) -> List[SyncConflict]:
        """
        Get unresolved conflicts with optional type filtering.

        Args:
            conflict_type: Optional conflict type filter
            limit: Maximum number of conflicts to return

        Returns:
            List of unresolved conflicts
        """
        start_time = asyncio.get_event_loop().time()

        try:
            stmt = select(SyncConflict).where(SyncConflict.resolved_at.is_(None))

            if conflict_type:
                stmt = stmt.where(SyncConflict.conflict_type == conflict_type)

            stmt = stmt.order_by(SyncConflict.created_at.asc()).limit(limit)

            result = await self.db.execute(stmt)
            conflicts = result.scalars().all()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_unresolved_conflicts", query_time, len(conflicts))

            return conflicts

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_unresolved_conflicts_error", query_time, 0)
            raise RepositoryError(f"Failed to get unresolved conflicts", e)


class SyncBatchRepository(EnhancedBaseRepository[SyncBatch], BulkOperationsMixin):
    """
    Repository for sync batch management with priority-based processing.

    Provides batch creation, scheduling, and processing tracking with
    performance optimization for high-throughput scenarios.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(SyncBatch, db)
        self.cache = cache_manager
        self._cache_ttl = 1800  # 30 minutes for batches
        self._scheduled_cache_ttl = 300  # 5 minutes for scheduled batches

    async def create_batch(self, batch_data: Dict[str, Any]) -> SyncBatch:
        """
        Create a new sync batch with automatic batch_id generation.

        Args:
            batch_data: Batch creation data

        Returns:
            Created batch record
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Generate unique batch_id if not provided
            if 'batch_id' not in batch_data:
                batch_data['batch_id'] = str(uuid4())

            batch = await self.create(batch_data)

            # Cache the new batch
            if self.cache:
                cache_key = f"sync_batch:{batch.batch_id}"
                await self.cache.set(cache_key, batch, self._cache_ttl)

                # Update scheduled batches cache
                await self._update_scheduled_batches_cache()

            query_time = asyncio.get_event_loop().time() - start_time
            logger.info(
                f"Created sync batch {batch.batch_id} in {query_time:.3f}s",
                extra={"correlation_id": correlation_id.get()}
            )

            return batch

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            logger.error(
                f"Failed to create sync batch in {query_time:.3f}s: {str(e)}",
                extra={"correlation_id": correlation_id.get()}
            )
            raise RepositoryError(f"Failed to create sync batch", e)

    async def get_by_batch_id(self, batch_id: str) -> Optional[SyncBatch]:
        """
        Get batch by batch_id with caching.

        Args:
            batch_id: Unique batch identifier

        Returns:
            Batch record if found, None otherwise
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"sync_batch:{batch_id}"
                cached_batch = await self.cache.get(cache_key)
                if cached_batch:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for batch {batch_id} in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_batch

            # Query database
            stmt = select(SyncBatch).where(SyncBatch.batch_id == batch_id)
            result = await self.db.execute(stmt)
            batch = result.scalar_one_or_none()

            # Cache the result
            if batch and self.cache:
                cache_key = f"sync_batch:{batch_id}"
                await self.cache.set(cache_key, batch, self._cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_batch_id", query_time, 1 if batch else 0)

            return batch

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_by_batch_id_error", query_time, 0)
            raise RepositoryError(f"Failed to get batch by batch_id {batch_id}", e)

    async def get_scheduled_batches(
        self,
        priority: Optional[SyncPriority] = None,
        target_scope: Optional[SyncScope] = None,
        limit: int = 50
    ) -> List[SyncBatch]:
        """
        Get scheduled batches ready for processing.

        Args:
            priority: Optional priority filter
            target_scope: Optional scope filter
            limit: Maximum number of batches to return

        Returns:
            List of scheduled batches
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first for common queries
            cache_key = f"scheduled_batches:{priority}:{target_scope}:{limit}"
            if self.cache:
                cached_batches = await self.cache.get(cache_key)
                if cached_batches:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for scheduled batches in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_batches

            current_time = datetime.now(timezone.utc)

            # Build query
            stmt = select(SyncBatch).where(
                and_(
                    SyncBatch.status == SyncStatus.PENDING,
                    SyncBatch.scheduled_at <= current_time
                )
            )

            if priority:
                stmt = stmt.where(SyncBatch.priority == priority)

            if target_scope:
                stmt = stmt.where(SyncBatch.target_scope == target_scope)

            # Order by priority and scheduled time
            stmt = stmt.order_by(
                SyncBatch.priority.desc(),  # Critical first
                SyncBatch.scheduled_at.asc()  # Earliest first
            ).limit(limit)

            result = await self.db.execute(stmt)
            scheduled_batches = result.scalars().all()

            # Cache the result
            if self.cache:
                await self.cache.set(cache_key, scheduled_batches, self._scheduled_cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_scheduled_batches", query_time, len(scheduled_batches))

            return scheduled_batches

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_scheduled_batches_error", query_time, 0)
            raise RepositoryError(f"Failed to get scheduled batches", e)

    async def update_batch_status(
        self,
        batch_id: str,
        status: SyncStatus,
        processing_time_ms: Optional[float] = None,
        processed_records: Optional[int] = None,
        failed_records: Optional[int] = None
    ) -> bool:
        """
        Update batch status and processing metrics.

        Args:
            batch_id: Batch identifier
            status: New status
            processing_time_ms: Processing time in milliseconds
            processed_records: Number of processed records
            failed_records: Number of failed records

        Returns:
            True if updated successfully
        """
        start_time = asyncio.get_event_loop().time()

        try:
            update_data = {
                'status': status,
                'updated_at': datetime.now(timezone.utc)
            }

            if status == SyncStatus.IN_PROGRESS:
                update_data['started_at'] = datetime.now(timezone.utc)
            elif status in [SyncStatus.COMPLETED, SyncStatus.FAILED]:
                update_data['completed_at'] = datetime.now(timezone.utc)
                if processing_time_ms is not None:
                    update_data['processing_time_ms'] = processing_time_ms

            if processed_records is not None:
                update_data['processed_records'] = processed_records

            if failed_records is not None:
                update_data['failed_records'] = failed_records

            stmt = (
                update(SyncBatch)
                .where(SyncBatch.batch_id == batch_id)
                .values(**update_data)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            success = result.rowcount > 0

            # Invalidate cache
            if success and self.cache:
                cache_key = f"sync_batch:{batch_id}"
                await self.cache.delete(cache_key)
                await self._update_scheduled_batches_cache()

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("update_batch_status", query_time, 1 if success else 0)

            return success

        except Exception as e:
            await self.db.rollback()
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("update_batch_status_error", query_time, 0)
            raise RepositoryError(f"Failed to update batch status for {batch_id}", e)

    async def _update_scheduled_batches_cache(self) -> None:
        """Update scheduled batches cache after modifications."""
        if not self.cache:
            return

        try:
            # Invalidate scheduled batch cache keys
            cache_patterns = [
                "scheduled_batches:*",
            ]

            for pattern in cache_patterns:
                await self.cache.delete_pattern(pattern)

        except Exception as e:
            logger.warning(f"Failed to update scheduled batches cache: {str(e)}")


class SyncMetricsRepository(EnhancedBaseRepository[SyncMetrics], BulkOperationsMixin):
    """
    Repository for sync metrics and analytics with aggregation support.

    Provides performance analytics, metrics aggregation, and bulk operations
    with >1000 records/second processing capability.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(SyncMetrics, db)
        self.cache = cache_manager
        self._cache_ttl = 3600  # 1 hour for metrics
        self._daily_cache_ttl = 86400  # 24 hours for daily aggregates

    async def create_or_update_hourly_metrics(
        self,
        metric_date: datetime,
        metric_hour: int,
        scope: SyncScope,
        priority: SyncPriority,
        metrics_data: Dict[str, Any]
    ) -> SyncMetrics:
        """
        Create or update hourly metrics with upsert functionality.

        Args:
            metric_date: Metrics date
            metric_hour: Metrics hour (0-23)
            scope: Metrics scope
            priority: Metrics priority
            metrics_data: Metrics data to update

        Returns:
            Created or updated metrics record
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try to find existing metrics record
            stmt = select(SyncMetrics).where(
                and_(
                    SyncMetrics.metric_date == metric_date,
                    SyncMetrics.metric_hour == metric_hour,
                    SyncMetrics.scope == scope,
                    SyncMetrics.priority == priority
                )
            )

            result = await self.db.execute(stmt)
            existing_metrics = result.scalar_one_or_none()

            if existing_metrics:
                # Update existing metrics
                update_data = {
                    'updated_at': datetime.now(timezone.utc),
                    **metrics_data
                }

                stmt = (
                    update(SyncMetrics)
                    .where(SyncMetrics.id == existing_metrics.id)
                    .values(**update_data)
                )

                await self.db.execute(stmt)
                await self.db.commit()
                await self.db.refresh(existing_metrics)

                metrics_record = existing_metrics
            else:
                # Create new metrics
                create_data = {
                    'metric_date': metric_date,
                    'metric_hour': metric_hour,
                    'scope': scope,
                    'priority': priority,
                    **metrics_data
                }

                metrics_record = await self.create(create_data)

            # Cache the metrics
            if self.cache:
                cache_key = f"sync_metrics:{metric_date.date()}:{metric_hour}:{scope}:{priority}"
                await self.cache.set(cache_key, metrics_record, self._cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("create_or_update_hourly_metrics", query_time, 1)

            return metrics_record

        except Exception as e:
            await self.db.rollback()
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("create_or_update_hourly_metrics_error", query_time, 0)
            raise RepositoryError(f"Failed to create/update hourly metrics", e)

    async def get_daily_aggregates(
        self,
        metric_date: datetime,
        scope: Optional[SyncScope] = None,
        priority: Optional[SyncPriority] = None
    ) -> Dict[str, Any]:
        """
        Get daily aggregated metrics with caching.

        Args:
            metric_date: Date for metrics
            scope: Optional scope filter
            priority: Optional priority filter

        Returns:
            Aggregated metrics data
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Try cache first
            cache_key = f"daily_metrics:{metric_date.date()}:{scope}:{priority}"
            if self.cache:
                cached_metrics = await self.cache.get(cache_key)
                if cached_metrics:
                    query_time = asyncio.get_event_loop().time() - start_time
                    logger.debug(
                        f"Cache hit for daily metrics in {query_time:.3f}s",
                        extra={"correlation_id": correlation_id.get()}
                    )
                    return cached_metrics

            # Build aggregation query
            stmt = select(
                func.sum(SyncMetrics.total_syncs).label('total_syncs'),
                func.sum(SyncMetrics.successful_syncs).label('successful_syncs'),
                func.sum(SyncMetrics.failed_syncs).label('failed_syncs'),
                func.sum(SyncMetrics.conflicts_detected).label('conflicts_detected'),
                func.sum(SyncMetrics.conflicts_resolved).label('conflicts_resolved'),
                func.avg(SyncMetrics.avg_processing_time_ms).label('avg_processing_time_ms'),
                func.max(SyncMetrics.max_processing_time_ms).label('max_processing_time_ms'),
                func.avg(SyncMetrics.avg_conflict_resolution_time_ms).label('avg_conflict_resolution_time_ms'),
                func.sum(SyncMetrics.total_data_size_bytes).label('total_data_size_bytes')
            ).where(
                func.date(SyncMetrics.metric_date) == metric_date.date()
            )

            if scope:
                stmt = stmt.where(SyncMetrics.scope == scope)

            if priority:
                stmt = stmt.where(SyncMetrics.priority == priority)

            result = await self.db.execute(stmt)
            row = result.first()

            if not row or row.total_syncs is None:
                aggregates = {
                    'total_syncs': 0,
                    'successful_syncs': 0,
                    'failed_syncs': 0,
                    'conflicts_detected': 0,
                    'conflicts_resolved': 0,
                    'avg_processing_time_ms': 0.0,
                    'max_processing_time_ms': 0.0,
                    'avg_conflict_resolution_time_ms': 0.0,
                    'success_rate': 0.0,
                    'conflict_resolution_rate': 0.0,
                    'total_data_size_bytes': 0
                }
            else:
                # Calculate derived metrics
                success_rate = (row.successful_syncs / row.total_syncs * 100) if row.total_syncs > 0 else 0.0
                conflict_resolution_rate = (row.conflicts_resolved / row.conflicts_detected * 100) if row.conflicts_detected > 0 else 0.0

                aggregates = {
                    'total_syncs': row.total_syncs or 0,
                    'successful_syncs': row.successful_syncs or 0,
                    'failed_syncs': row.failed_syncs or 0,
                    'conflicts_detected': row.conflicts_detected or 0,
                    'conflicts_resolved': row.conflicts_resolved or 0,
                    'avg_processing_time_ms': float(row.avg_processing_time_ms or 0.0),
                    'max_processing_time_ms': float(row.max_processing_time_ms or 0.0),
                    'avg_conflict_resolution_time_ms': float(row.avg_conflict_resolution_time_ms or 0.0),
                    'success_rate': round(success_rate, 2),
                    'conflict_resolution_rate': round(conflict_resolution_rate, 2),
                    'total_data_size_bytes': row.total_data_size_bytes or 0
                }

            # Cache the result
            if self.cache:
                await self.cache.set(cache_key, aggregates, self._daily_cache_ttl)

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_daily_aggregates", query_time, 1)

            return aggregates

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_daily_aggregates_error", query_time, 0)
            raise RepositoryError(f"Failed to get daily aggregates for {metric_date.date()}", e)

    async def get_performance_summary(
        self,
        hours: int = 24,
        scope: Optional[SyncScope] = None,
        priority: Optional[SyncPriority] = None
    ) -> Dict[str, Any]:
        """
        Get performance summary for the last N hours.

        Args:
            hours: Number of hours to analyze
            scope: Optional scope filter
            priority: Optional priority filter

        Returns:
            Performance summary data
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Calculate time range
            end_time = datetime.now(timezone.utc)
            start_time_range = end_time - timedelta(hours=hours)

            # Build query
            stmt = select(
                func.sum(SyncMetrics.total_syncs).label('total_syncs'),
                func.sum(SyncMetrics.successful_syncs).label('successful_syncs'),
                func.sum(SyncMetrics.failed_syncs).label('failed_syncs'),
                func.sum(SyncMetrics.conflicts_detected).label('conflicts_detected'),
                func.sum(SyncMetrics.conflicts_resolved).label('conflicts_resolved'),
                func.avg(SyncMetrics.avg_processing_time_ms).label('avg_processing_time_ms'),
                func.max(SyncMetrics.max_processing_time_ms).label('max_processing_time_ms'),
                func.avg(SyncMetrics.avg_conflict_resolution_time_ms).label('avg_conflict_resolution_time_ms'),
                func.sum(SyncMetrics.total_data_size_bytes).label('total_data_size_bytes')
            ).where(
                SyncMetrics.metric_date >= start_time_range
            )

            if scope:
                stmt = stmt.where(SyncMetrics.scope == scope)

            if priority:
                stmt = stmt.where(SyncMetrics.priority == priority)

            result = await self.db.execute(stmt)
            row = result.first()

            if not row or row.total_syncs is None:
                summary = {
                    'period_hours': hours,
                    'total_syncs': 0,
                    'successful_syncs': 0,
                    'failed_syncs': 0,
                    'success_rate': 0.0,
                    'avg_processing_time_ms': 0.0,
                    'max_processing_time_ms': 0.0,
                    'conflicts_detected': 0,
                    'conflicts_resolved': 0,
                    'conflict_resolution_rate': 0.0,
                    'avg_conflict_resolution_time_ms': 0.0,
                    'throughput_syncs_per_hour': 0.0,
                    'total_data_size_mb': 0.0
                }
            else:
                # Calculate derived metrics
                success_rate = (row.successful_syncs / row.total_syncs * 100) if row.total_syncs > 0 else 0.0
                conflict_resolution_rate = (row.conflicts_resolved / row.conflicts_detected * 100) if row.conflicts_detected > 0 else 0.0
                throughput = row.total_syncs / hours if hours > 0 else 0.0
                data_size_mb = (row.total_data_size_bytes or 0) / (1024 * 1024)

                summary = {
                    'period_hours': hours,
                    'total_syncs': row.total_syncs or 0,
                    'successful_syncs': row.successful_syncs or 0,
                    'failed_syncs': row.failed_syncs or 0,
                    'success_rate': round(success_rate, 2),
                    'avg_processing_time_ms': round(float(row.avg_processing_time_ms or 0.0), 2),
                    'max_processing_time_ms': float(row.max_processing_time_ms or 0.0),
                    'conflicts_detected': row.conflicts_detected or 0,
                    'conflicts_resolved': row.conflicts_resolved or 0,
                    'conflict_resolution_rate': round(conflict_resolution_rate, 2),
                    'avg_conflict_resolution_time_ms': round(float(row.avg_conflict_resolution_time_ms or 0.0), 2),
                    'throughput_syncs_per_hour': round(throughput, 2),
                    'total_data_size_mb': round(data_size_mb, 2)
                }

            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_performance_summary", query_time, 1)

            return summary

        except Exception as e:
            query_time = asyncio.get_event_loop().time() - start_time
            self._track_query_performance("get_performance_summary_error", query_time, 0)
            raise RepositoryError(f"Failed to get performance summary for {hours} hours", e)
