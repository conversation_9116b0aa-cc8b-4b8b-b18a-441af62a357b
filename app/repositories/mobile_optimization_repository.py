"""
Mobile Optimization Repository for Culture Connect Backend API.

This module provides data access layer for mobile optimization operations including:
- Device-specific analysis and performance benchmarking
- Mobile optimization tracking and responsive design metrics
- Mobile app preview generation and optimization scoring
- Integration with service and vendor mobile optimization workflows

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade async operations and comprehensive mobile optimization capabilities.
"""

import logging
from datetime import datetime, timezone, timedelta, date
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from app.models.marketplace_optimization import MobileOptimization
from app.models.service import Service
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository, RepositoryError


logger = logging.getLogger(__name__)


class MobileOptimizationRepository(BaseRepository[MobileOptimization]):
    """
    Repository for mobile optimization data access operations.
    
    Provides specialized methods for:
    - Device-specific analysis and performance benchmarking
    - Mobile optimization tracking and responsive design metrics
    - Mobile app preview generation and optimization scoring
    - Service and vendor mobile optimization workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize mobile optimization repository."""
        super().__init__(MobileOptimization, db)

    async def get_latest_by_service(
        self,
        service_id: int,
        device_type: str = "mobile"
    ) -> Optional[MobileOptimization]:
        """
        Get the latest mobile optimization analysis for a service.
        
        Args:
            service_id: Service ID to get analysis for
            device_type: Device type filter (mobile, tablet, desktop)
            
        Returns:
            Latest mobile optimization analysis or None if not found
        """
        try:
            result = await self.db.execute(
                Select(MobileOptimization)
                .where(
                    and_(
                        MobileOptimization.service_id == service_id,
                        MobileOptimization.device_type == device_type
                    )
                )
                .order_by(desc(MobileOptimization.analysis_date))
                .limit(1)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get latest mobile optimization for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get latest mobile optimization", e)

    async def get_device_performance_comparison(
        self,
        service_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get performance comparison across different device types.
        
        Args:
            service_id: Service ID to analyze
            days: Number of days to analyze
            
        Returns:
            Device performance comparison data
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            result = await self.db.execute(
                Select(
                    MobileOptimization.device_type,
                    func.avg(MobileOptimization.overall_mobile_score).label('avg_score'),
                    func.avg(MobileOptimization.loading_speed_score).label('avg_loading'),
                    func.avg(MobileOptimization.responsive_design_score).label('avg_responsive'),
                    func.avg(MobileOptimization.touch_interface_score).label('avg_touch'),
                    func.avg(MobileOptimization.mobile_conversion_rate).label('avg_conversion'),
                    func.avg(MobileOptimization.mobile_bounce_rate).label('avg_bounce'),
                    func.avg(MobileOptimization.page_load_time_ms).label('avg_load_time'),
                    func.count(MobileOptimization.id).label('analysis_count')
                )
                .where(
                    and_(
                        MobileOptimization.service_id == service_id,
                        MobileOptimization.analysis_date >= cutoff_date
                    )
                )
                .group_by(MobileOptimization.device_type)
                .order_by(MobileOptimization.device_type)
            )
            rows = result.all()
            
            device_data = {}
            for row in rows:
                device_data[row.device_type] = {
                    "overall_score": round(float(row.avg_score), 2),
                    "loading_speed_score": round(float(row.avg_loading), 2),
                    "responsive_design_score": round(float(row.avg_responsive), 2),
                    "touch_interface_score": round(float(row.avg_touch), 2),
                    "conversion_rate": round(float(row.avg_conversion), 2),
                    "bounce_rate": round(float(row.avg_bounce), 2),
                    "avg_load_time_ms": round(float(row.avg_load_time), 0),
                    "analysis_count": row.analysis_count
                }
            
            return {
                "service_id": service_id,
                "analysis_period": {
                    "start_date": cutoff_date.date().isoformat(),
                    "end_date": datetime.now(timezone.utc).date().isoformat(),
                    "days": days
                },
                "device_performance": device_data,
                "best_performing_device": max(device_data.keys(), key=lambda k: device_data[k]["overall_score"]) if device_data else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get device performance comparison for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get device performance comparison", e)

    async def get_mobile_optimization_trends(
        self,
        service_id: int,
        device_type: str = "mobile",
        days: int = 30
    ) -> Dict[str, List[Tuple[date, float]]]:
        """
        Get mobile optimization trends over time.
        
        Args:
            service_id: Service ID to analyze
            device_type: Device type to analyze
            days: Number of days to analyze
            
        Returns:
            Mobile optimization trends with time-series data
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            result = await self.db.execute(
                Select(
                    MobileOptimization.analysis_date,
                    MobileOptimization.overall_mobile_score,
                    MobileOptimization.loading_speed_score,
                    MobileOptimization.responsive_design_score,
                    MobileOptimization.touch_interface_score,
                    MobileOptimization.mobile_conversion_rate,
                    MobileOptimization.page_load_time_ms
                )
                .where(
                    and_(
                        MobileOptimization.service_id == service_id,
                        MobileOptimization.device_type == device_type,
                        MobileOptimization.analysis_date >= cutoff_date
                    )
                )
                .order_by(asc(MobileOptimization.analysis_date))
            )
            rows = result.all()
            
            return {
                "overall_score": [(row.analysis_date.date(), float(row.overall_mobile_score)) for row in rows],
                "loading_speed": [(row.analysis_date.date(), float(row.loading_speed_score)) for row in rows],
                "responsive_design": [(row.analysis_date.date(), float(row.responsive_design_score)) for row in rows],
                "touch_interface": [(row.analysis_date.date(), float(row.touch_interface_score)) for row in rows],
                "conversion_rate": [(row.analysis_date.date(), float(row.mobile_conversion_rate)) for row in rows],
                "load_time_ms": [(row.analysis_date.date(), float(row.page_load_time_ms)) for row in rows]
            }
            
        except Exception as e:
            logger.error(f"Failed to get mobile optimization trends for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get mobile optimization trends", e)

    async def get_vendor_mobile_summary(
        self,
        vendor_id: int,
        device_type: str = "mobile"
    ) -> Dict[str, Any]:
        """
        Get mobile optimization summary for all vendor services.
        
        Args:
            vendor_id: Vendor ID to summarize for
            device_type: Device type to analyze
            
        Returns:
            Vendor mobile optimization summary
        """
        try:
            # Get latest analysis for each service
            latest_subquery = (
                Select(
                    MobileOptimization.service_id,
                    func.max(MobileOptimization.analysis_date).label('latest_date')
                )
                .where(
                    and_(
                        MobileOptimization.vendor_id == vendor_id,
                        MobileOptimization.device_type == device_type
                    )
                )
                .group_by(MobileOptimization.service_id)
                .subquery()
            )
            
            result = await self.db.execute(
                Select(
                    func.avg(MobileOptimization.overall_mobile_score).label('avg_overall'),
                    func.avg(MobileOptimization.loading_speed_score).label('avg_loading'),
                    func.avg(MobileOptimization.responsive_design_score).label('avg_responsive'),
                    func.avg(MobileOptimization.touch_interface_score).label('avg_touch'),
                    func.avg(MobileOptimization.image_optimization_score).label('avg_image'),
                    func.avg(MobileOptimization.navigation_ux_score).label('avg_navigation'),
                    func.avg(MobileOptimization.mobile_conversion_rate).label('avg_conversion'),
                    func.avg(MobileOptimization.mobile_bounce_rate).label('avg_bounce'),
                    func.avg(MobileOptimization.page_load_time_ms).label('avg_load_time'),
                    func.count(MobileOptimization.id).label('total_services'),
                    func.min(MobileOptimization.overall_mobile_score).label('min_score'),
                    func.max(MobileOptimization.overall_mobile_score).label('max_score')
                )
                .join(
                    latest_subquery,
                    and_(
                        MobileOptimization.service_id == latest_subquery.c.service_id,
                        MobileOptimization.analysis_date == latest_subquery.c.latest_date
                    )
                )
            )
            row = result.first()
            
            return {
                "vendor_id": vendor_id,
                "device_type": device_type,
                "summary": {
                    "total_services_analyzed": row.total_services or 0,
                    "avg_overall_score": round(float(row.avg_overall or 0), 2),
                    "score_range": {
                        "min": round(float(row.min_score or 0), 2),
                        "max": round(float(row.max_score or 0), 2)
                    }
                },
                "category_scores": {
                    "loading_speed": round(float(row.avg_loading or 0), 2),
                    "responsive_design": round(float(row.avg_responsive or 0), 2),
                    "touch_interface": round(float(row.avg_touch or 0), 2),
                    "image_optimization": round(float(row.avg_image or 0), 2),
                    "navigation_ux": round(float(row.avg_navigation or 0), 2)
                },
                "performance_metrics": {
                    "avg_conversion_rate": round(float(row.avg_conversion or 0), 2),
                    "avg_bounce_rate": round(float(row.avg_bounce or 0), 2),
                    "avg_load_time_ms": round(float(row.avg_load_time or 0), 0)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get vendor mobile summary for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get vendor mobile summary", e)

    async def get_mobile_benchmarks(
        self,
        category_id: Optional[int] = None,
        device_type: str = "mobile",
        geographic_scope: str = "local"
    ) -> Dict[str, Any]:
        """
        Get mobile optimization benchmarks for comparison.
        
        Args:
            category_id: Optional category filter
            device_type: Device type for benchmarks
            geographic_scope: Geographic scope for benchmarks
            
        Returns:
            Mobile optimization benchmarks and percentiles
        """
        try:
            # Build query for latest analyses
            latest_subquery = (
                Select(
                    MobileOptimization.service_id,
                    func.max(MobileOptimization.analysis_date).label('latest_date')
                )
                .where(MobileOptimization.device_type == device_type)
                .group_by(MobileOptimization.service_id)
                .subquery()
            )
            
            query = Select(
                func.avg(MobileOptimization.overall_mobile_score).label('avg_score'),
                func.percentile_cont(0.25).within_group(MobileOptimization.overall_mobile_score).label('p25'),
                func.percentile_cont(0.5).within_group(MobileOptimization.overall_mobile_score).label('p50'),
                func.percentile_cont(0.75).within_group(MobileOptimization.overall_mobile_score).label('p75'),
                func.percentile_cont(0.9).within_group(MobileOptimization.overall_mobile_score).label('p90'),
                func.avg(MobileOptimization.loading_speed_score).label('avg_loading'),
                func.avg(MobileOptimization.responsive_design_score).label('avg_responsive'),
                func.avg(MobileOptimization.mobile_conversion_rate).label('avg_conversion'),
                func.avg(MobileOptimization.page_load_time_ms).label('avg_load_time'),
                func.count(MobileOptimization.id).label('total_analyses')
            ).join(
                latest_subquery,
                and_(
                    MobileOptimization.service_id == latest_subquery.c.service_id,
                    MobileOptimization.analysis_date == latest_subquery.c.latest_date
                )
            )
            
            if category_id:
                query = query.join(Service, MobileOptimization.service_id == Service.id)
                query = query.where(Service.category_id == category_id)
            
            result = await self.db.execute(query)
            row = result.first()
            
            return {
                "device_type": device_type,
                "category_id": category_id,
                "benchmarks": {
                    "average_score": round(float(row.avg_score or 0), 2),
                    "percentiles": {
                        "p25": round(float(row.p25 or 0), 2),
                        "p50": round(float(row.p50 or 0), 2),
                        "p75": round(float(row.p75 or 0), 2),
                        "p90": round(float(row.p90 or 0), 2)
                    },
                    "category_averages": {
                        "loading_speed": round(float(row.avg_loading or 0), 2),
                        "responsive_design": round(float(row.avg_responsive or 0), 2)
                    },
                    "performance_averages": {
                        "conversion_rate": round(float(row.avg_conversion or 0), 2),
                        "load_time_ms": round(float(row.avg_load_time or 0), 0)
                    }
                },
                "sample_size": row.total_analyses or 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get mobile benchmarks: {str(e)}")
            raise RepositoryError(f"Failed to get mobile benchmarks", e)

    async def get_optimization_opportunities(
        self,
        service_id: int,
        device_type: str = "mobile",
        threshold_score: float = 70.0
    ) -> Dict[str, Any]:
        """
        Identify mobile optimization opportunities for a service.
        
        Args:
            service_id: Service ID to analyze
            device_type: Device type to analyze
            threshold_score: Score threshold for identifying opportunities
            
        Returns:
            Mobile optimization opportunities and recommendations
        """
        try:
            latest_analysis = await self.get_latest_by_service(service_id, device_type)
            if not latest_analysis:
                return {"error": "No mobile optimization analysis found"}
            
            # Identify areas below threshold
            opportunities = []
            scores = {
                "overall_mobile_score": latest_analysis.overall_mobile_score,
                "responsive_design_score": latest_analysis.responsive_design_score,
                "loading_speed_score": latest_analysis.loading_speed_score,
                "touch_interface_score": latest_analysis.touch_interface_score,
                "image_optimization_score": latest_analysis.image_optimization_score,
                "navigation_ux_score": latest_analysis.navigation_ux_score
            }
            
            for category, score in scores.items():
                if float(score) < threshold_score:
                    priority = "high" if float(score) < 50 else "medium" if float(score) < 65 else "low"
                    opportunities.append({
                        "category": category.replace("_score", "").replace("_", " ").title(),
                        "current_score": float(score),
                        "improvement_needed": threshold_score - float(score),
                        "priority": priority
                    })
            
            # Sort by improvement needed (descending)
            opportunities.sort(key=lambda x: x["improvement_needed"], reverse=True)
            
            # Get specific recommendations from analysis
            recommendations = latest_analysis.mobile_recommendations or []
            
            return {
                "service_id": service_id,
                "device_type": device_type,
                "analysis_date": latest_analysis.analysis_date.isoformat(),
                "current_overall_score": float(latest_analysis.overall_mobile_score),
                "optimization_opportunities": opportunities,
                "specific_recommendations": recommendations,
                "accessibility_issues": latest_analysis.accessibility_issues or [],
                "performance_issues": {
                    "load_time_ms": latest_analysis.page_load_time_ms,
                    "first_contentful_paint_ms": latest_analysis.first_contentful_paint_ms,
                    "largest_contentful_paint_ms": latest_analysis.largest_contentful_paint_ms
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get optimization opportunities for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get optimization opportunities", e)

    async def get_top_mobile_performers(
        self,
        device_type: str = "mobile",
        category_id: Optional[int] = None,
        limit: int = 10
    ) -> List[MobileOptimization]:
        """
        Get top mobile performing services.
        
        Args:
            device_type: Device type to analyze
            category_id: Optional category filter
            limit: Number of top performers to return
            
        Returns:
            List of top mobile optimization analyses
        """
        try:
            # Get latest analysis for each service
            latest_subquery = (
                Select(
                    MobileOptimization.service_id,
                    func.max(MobileOptimization.analysis_date).label('latest_date')
                )
                .where(MobileOptimization.device_type == device_type)
                .group_by(MobileOptimization.service_id)
                .subquery()
            )
            
            query = Select(MobileOptimization).join(
                latest_subquery,
                and_(
                    MobileOptimization.service_id == latest_subquery.c.service_id,
                    MobileOptimization.analysis_date == latest_subquery.c.latest_date
                )
            ).options(
                joinedload(MobileOptimization.service),
                joinedload(MobileOptimization.vendor)
            ).order_by(
                desc(MobileOptimization.overall_mobile_score)
            ).limit(limit)
            
            if category_id:
                query = query.join(Service, MobileOptimization.service_id == Service.id)
                query = query.where(Service.category_id == category_id)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get top mobile performers: {str(e)}")
            raise RepositoryError(f"Failed to get top mobile performers", e)
