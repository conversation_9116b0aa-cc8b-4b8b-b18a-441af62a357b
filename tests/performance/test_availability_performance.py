"""
Performance tests for availability management system.

This module provides comprehensive performance tests for availability operations:
- Real-time availability checking with <50ms response time validation
- Slot generation performance with <200ms for 30-day periods
- Bulk operations performance with throughput validation
- Repository query performance with optimization validation
- Service layer performance with caching validation

Implements Task 4.1.2 Phase 6 requirements with performance benchmarking.
"""

import pytest
import time
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, time as dt_time, timedelta, timezone
from typing import List, Dict, Any
import statistics

from app.services.availability_service import AvailabilityService
from app.repositories.availability_repository import (
    VendorAvailabilityRepository, AvailabilitySlotRepository,
    RecurringAvailabilityRepository, AvailabilityExceptionRepository
)
from app.schemas.availability_schemas import (
    VendorAvailabilityCreateSchema, AvailabilityCheckResponseSchema
)


class TestAvailabilityPerformance:
    """Performance test cases for availability management."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session for performance testing."""
        session = AsyncMock()
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session
    
    @pytest.fixture
    def availability_service(self, mock_db_session):
        """Create AvailabilityService for performance testing."""
        with patch('app.services.availability_service.EmailService'), \
             patch('app.services.availability_service.PushNotificationService'), \
             patch('app.services.availability_service.VendorAvailabilityRepository'), \
             patch('app.services.availability_service.RecurringAvailabilityRepository'), \
             patch('app.services.availability_service.AvailabilitySlotRepository'), \
             patch('app.services.availability_service.AvailabilityExceptionRepository'), \
             patch('app.services.availability_service.BookingRepository'):
            
            service = AvailabilityService(mock_db_session)
            
            # Mock repositories for performance testing
            service.repository = AsyncMock()
            service.recurring_repository = AsyncMock()
            service.slot_repository = AsyncMock()
            service.exception_repository = AsyncMock()
            service.booking_repository = AsyncMock()
            
            return service
    
    @pytest.fixture
    def vendor_availability_response(self):
        """Mock vendor availability response for performance testing."""
        mock_response = MagicMock()
        mock_response.id = 1
        mock_response.vendor_id = 123
        mock_response.min_booking_notice_hours = 24
        mock_response.max_booking_notice_days = 30
        return mock_response
    
    def measure_execution_time(self, func, *args, **kwargs):
        """Measure execution time of a function."""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        return result, execution_time
    
    async def measure_async_execution_time(self, coro):
        """Measure execution time of an async coroutine."""
        start_time = time.perf_counter()
        result = await coro
        end_time = time.perf_counter()
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        return result, execution_time
    
    @pytest.mark.asyncio
    async def test_availability_checking_performance(
        self, availability_service, vendor_availability_response
    ):
        """Test availability checking performance (<50ms target)."""
        vendor_id = 123
        start_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        end_datetime = start_datetime + timedelta(hours=1)
        
        # Mock service responses for fast execution
        with patch.object(availability_service, 'get_vendor_availability') as mock_get_availability:
            mock_get_availability.return_value = vendor_availability_response
            
            availability_service.slot_repository.check_slot_availability.return_value = (True, [])
            availability_service.slot_repository.check_booking_conflicts.return_value = []
            
            # Measure multiple executions for statistical analysis
            execution_times = []
            iterations = 100
            
            for _ in range(iterations):
                result, execution_time = await self.measure_async_execution_time(
                    availability_service.check_availability(
                        vendor_id=vendor_id,
                        start_datetime=start_datetime,
                        end_datetime=end_datetime
                    )
                )
                execution_times.append(execution_time)
            
            # Performance analysis
            avg_time = statistics.mean(execution_times)
            max_time = max(execution_times)
            min_time = min(execution_times)
            p95_time = statistics.quantiles(execution_times, n=20)[18]  # 95th percentile
            
            print(f"\nAvailability Checking Performance Results:")
            print(f"Average time: {avg_time:.2f}ms")
            print(f"Min time: {min_time:.2f}ms")
            print(f"Max time: {max_time:.2f}ms")
            print(f"95th percentile: {p95_time:.2f}ms")
            
            # Performance assertions
            assert avg_time < 50, f"Average availability checking time {avg_time:.2f}ms exceeds 50ms target"
            assert p95_time < 75, f"95th percentile time {p95_time:.2f}ms exceeds 75ms threshold"
            assert max_time < 100, f"Maximum time {max_time:.2f}ms exceeds 100ms threshold"
    
    @pytest.mark.asyncio
    async def test_slot_generation_performance(self, availability_service):
        """Test slot generation performance (<200ms for 30-day periods)."""
        vendor_availability_id = 1
        start_date = date.today() + timedelta(days=1)
        end_date = start_date + timedelta(days=30)  # 30-day period
        
        # Mock repository responses for realistic performance testing
        mock_patterns = []
        for i in range(3):  # 3 recurring patterns
            pattern = MagicMock()
            pattern.id = i + 1
            pattern.pattern_type = "weekly"
            pattern.day_of_week = i
            pattern.start_time = dt_time(10, 0)
            pattern.end_time = dt_time(16, 0)
            pattern.slot_duration_minutes = 60
            pattern.max_bookings_per_slot = 2
            pattern.vendor_availability_id = vendor_availability_id
            mock_patterns.append(pattern)
        
        availability_service.recurring_repository.get_active_patterns_for_vendor.return_value = mock_patterns
        availability_service.exception_repository.get_exceptions_for_date_range.return_value = []
        availability_service.slot_repository.bulk_create_slots.return_value = (180, list(range(1, 181)))
        
        # Mock slot generation for patterns
        with patch.object(availability_service, '_generate_slots_for_pattern') as mock_generate:
            mock_generate.return_value = [{"slot": "data"}] * 60  # 60 slots per pattern
            
            # Measure execution time
            result, execution_time = await self.measure_async_execution_time(
                availability_service.generate_slots_from_patterns(
                    vendor_availability_id=vendor_availability_id,
                    start_date=start_date,
                    end_date=end_date
                )
            )
            
            print(f"\nSlot Generation Performance Results:")
            print(f"Execution time: {execution_time:.2f}ms")
            print(f"Generated slots: {result.created_count}")
            print(f"Patterns processed: {result.performance_metrics.get('patterns_processed', 0)}")
            print(f"Throughput: {result.created_count / (execution_time / 1000):.2f} slots/second")
            
            # Performance assertions
            assert execution_time < 200, f"Slot generation time {execution_time:.2f}ms exceeds 200ms target"
            assert result.created_count > 0, "No slots were generated"
    
    @pytest.mark.asyncio
    async def test_bulk_slot_creation_performance(self, mock_db_session):
        """Test bulk slot creation performance (>1000 slots/second)."""
        slot_repo = AvailabilitySlotRepository(mock_db_session)
        
        # Generate test data for bulk creation
        slots_data = []
        for i in range(1000):  # 1000 slots
            slot_data = {
                "vendor_availability_id": 1,
                "date": date.today() + timedelta(days=i % 30),
                "start_time": dt_time(10 + (i % 8), 0),
                "end_time": dt_time(11 + (i % 8), 0),
                "max_bookings": 2,
                "current_bookings": 0,
                "is_available": True
            }
            slots_data.append(slot_data)
        
        # Mock database response
        mock_result = MagicMock()
        mock_result.fetchall.return_value = [(i,) for i in range(1, 1001)]
        mock_db_session.execute.return_value = mock_result
        
        # Measure execution time
        start_time = time.perf_counter()
        created_count, created_ids = await slot_repo.bulk_create_slots(
            slots_data=slots_data,
            batch_size=1000
        )
        end_time = time.perf_counter()
        
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        throughput = created_count / (execution_time / 1000)  # slots per second
        
        print(f"\nBulk Slot Creation Performance Results:")
        print(f"Execution time: {execution_time:.2f}ms")
        print(f"Created slots: {created_count}")
        print(f"Throughput: {throughput:.2f} slots/second")
        
        # Performance assertions
        assert execution_time < 500, f"Bulk creation time {execution_time:.2f}ms exceeds 500ms target"
        assert throughput > 1000, f"Throughput {throughput:.2f} slots/second below 1000 target"
        assert created_count == 1000, f"Expected 1000 slots, got {created_count}"
    
    @pytest.mark.asyncio
    async def test_slot_availability_checking_performance(self, mock_db_session):
        """Test slot availability checking performance (<50ms target)."""
        slot_repo = AvailabilitySlotRepository(mock_db_session)
        
        # Mock database response with multiple slots
        mock_slots = []
        for i in range(10):
            slot = MagicMock()
            slot.id = i + 1
            slot.max_bookings = 2
            slot.current_bookings = i % 3  # Varying booking counts
            mock_slots.append(slot)
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_slots
        mock_db_session.execute.return_value = mock_result
        
        # Measure multiple executions
        execution_times = []
        iterations = 50
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            is_available, conflicting_slots = await slot_repo.check_slot_availability(
                vendor_availability_id=1,
                target_date=date.today() + timedelta(days=1),
                start_time=dt_time(10, 0),
                end_time=dt_time(11, 0),
                required_capacity=1
            )
            end_time = time.perf_counter()
            
            execution_time = (end_time - start_time) * 1000
            execution_times.append(execution_time)
        
        # Performance analysis
        avg_time = statistics.mean(execution_times)
        max_time = max(execution_times)
        p95_time = statistics.quantiles(execution_times, n=20)[18]
        
        print(f"\nSlot Availability Checking Performance Results:")
        print(f"Average time: {avg_time:.2f}ms")
        print(f"Max time: {max_time:.2f}ms")
        print(f"95th percentile: {p95_time:.2f}ms")
        
        # Performance assertions
        assert avg_time < 50, f"Average checking time {avg_time:.2f}ms exceeds 50ms target"
        assert p95_time < 75, f"95th percentile time {p95_time:.2f}ms exceeds 75ms threshold"
    
    @pytest.mark.asyncio
    async def test_vendor_availability_caching_performance(self, availability_service):
        """Test vendor availability caching performance."""
        vendor_id = 123
        service_id = None
        
        # Mock repository response
        mock_availability = MagicMock()
        mock_availability.id = 1
        availability_service.repository.get_by_vendor_and_service.return_value = mock_availability
        
        # First call (cache miss)
        start_time = time.perf_counter()
        result1 = await availability_service.get_vendor_availability(
            vendor_id=vendor_id,
            service_id=service_id
        )
        first_call_time = (time.perf_counter() - start_time) * 1000
        
        # Second call (cache hit)
        start_time = time.perf_counter()
        result2 = await availability_service.get_vendor_availability(
            vendor_id=vendor_id,
            service_id=service_id
        )
        second_call_time = (time.perf_counter() - start_time) * 1000
        
        print(f"\nCaching Performance Results:")
        print(f"First call (cache miss): {first_call_time:.2f}ms")
        print(f"Second call (cache hit): {second_call_time:.2f}ms")
        print(f"Cache speedup: {first_call_time / second_call_time:.2f}x")
        
        # Performance assertions
        assert second_call_time < first_call_time, "Cache hit should be faster than cache miss"
        assert second_call_time < 10, f"Cache hit time {second_call_time:.2f}ms should be <10ms"
    
    def test_pattern_date_matching_performance(self, availability_service):
        """Test pattern date matching performance."""
        pattern = MagicMock()
        pattern.pattern_type = "weekly"
        pattern.day_of_week = 1  # Monday
        
        # Test multiple date checks
        test_dates = [date.today() + timedelta(days=i) for i in range(365)]
        
        start_time = time.perf_counter()
        results = []
        for test_date in test_dates:
            result = availability_service._pattern_applies_to_date(pattern, test_date)
            results.append(result)
        end_time = time.perf_counter()
        
        execution_time = (end_time - start_time) * 1000
        throughput = len(test_dates) / (execution_time / 1000)
        
        print(f"\nPattern Date Matching Performance Results:")
        print(f"Execution time: {execution_time:.2f}ms")
        print(f"Dates processed: {len(test_dates)}")
        print(f"Throughput: {throughput:.2f} dates/second")
        print(f"Matching dates: {sum(results)}")
        
        # Performance assertions
        assert execution_time < 100, f"Pattern matching time {execution_time:.2f}ms exceeds 100ms"
        assert throughput > 1000, f"Throughput {throughput:.2f} dates/second below 1000 target"
    
    @pytest.mark.asyncio
    async def test_concurrent_availability_checking(self, availability_service, vendor_availability_response):
        """Test concurrent availability checking performance."""
        vendor_id = 123
        
        # Mock service responses
        with patch.object(availability_service, 'get_vendor_availability') as mock_get_availability:
            mock_get_availability.return_value = vendor_availability_response
            
            availability_service.slot_repository.check_slot_availability.return_value = (True, [])
            availability_service.slot_repository.check_booking_conflicts.return_value = []
            
            # Create concurrent requests
            async def check_availability():
                start_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
                end_datetime = start_datetime + timedelta(hours=1)
                return await availability_service.check_availability(
                    vendor_id=vendor_id,
                    start_datetime=start_datetime,
                    end_datetime=end_datetime
                )
            
            # Execute concurrent requests
            concurrent_requests = 50
            start_time = time.perf_counter()
            
            tasks = [check_availability() for _ in range(concurrent_requests)]
            results = await asyncio.gather(*tasks)
            
            end_time = time.perf_counter()
            total_time = (end_time - start_time) * 1000
            avg_time_per_request = total_time / concurrent_requests
            throughput = concurrent_requests / (total_time / 1000)
            
            print(f"\nConcurrent Availability Checking Performance Results:")
            print(f"Total time: {total_time:.2f}ms")
            print(f"Concurrent requests: {concurrent_requests}")
            print(f"Average time per request: {avg_time_per_request:.2f}ms")
            print(f"Throughput: {throughput:.2f} requests/second")
            
            # Performance assertions
            assert avg_time_per_request < 100, f"Average concurrent request time {avg_time_per_request:.2f}ms exceeds 100ms"
            assert throughput > 10, f"Concurrent throughput {throughput:.2f} requests/second below 10 target"
            assert len(results) == concurrent_requests, f"Expected {concurrent_requests} results, got {len(results)}"
