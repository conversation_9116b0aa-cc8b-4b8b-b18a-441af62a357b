"""
Enhanced Event Broadcasting Service for Culture Connect Backend API.

This module provides comprehensive real-time event broadcasting infrastructure including:
- Event-driven message routing with type-based message handling
- Priority-based message queuing with immediate, standard, and low-priority channels
- Message acknowledgment system with delivery confirmation and retry mechanisms
- Integration with existing WebSocket connection manager from Phase 6

Implements Task 6.1.1 Phase 7 requirements for real-time event broadcasting with:
- Advanced message routing with topic-based subscriptions
- User role-based message filtering and geographic routing
- Message persistence for offline users with delivery upon reconnection
- Integration with booking communication system for seamless real-time updates

Performance targets: <50ms event processing, <100ms message broadcasting, >99.9% delivery reliability
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Set, Union
from enum import Enum
from dataclasses import dataclass, field
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.services.base import BaseService, ValidationError, ServiceError, NotFoundError
from app.core.logging import correlation_id
from app.models.user import User
from app.models.booking import Booking
from app.schemas.websocket_schemas import WebSocketEventCreate, EventTypeEnum, EventPriorityEnum
from app.services.websocket_services import WebSocketConnectionService, WebSocketEventService
from app.services.booking_communication_service import BookingMessageService

logger = logging.getLogger(__name__)


class MessagePriority(str, Enum):
    """Message priority levels for event broadcasting."""
    IMMEDIATE = "immediate"      # <10ms processing, critical system events
    HIGH = "high"               # <25ms processing, booking updates, payments
    STANDARD = "standard"       # <50ms processing, general notifications
    LOW = "low"                 # <100ms processing, marketing, analytics


class EventCategory(str, Enum):
    """Event categories for message routing."""
    BOOKING = "booking"         # Booking-related events
    SYSTEM = "system"           # System-wide events
    USER = "user"              # User-specific events
    NOTIFICATION = "notification"  # General notifications
    CHAT = "chat"              # Chat/messaging events


class SubscriptionTopic(str, Enum):
    """Subscription topics for selective message delivery."""
    BOOKING_UPDATES = "booking.updates"
    BOOKING_MESSAGES = "booking.messages"
    SYSTEM_ANNOUNCEMENTS = "system.announcements"
    USER_NOTIFICATIONS = "user.notifications"
    VENDOR_ALERTS = "vendor.alerts"
    PAYMENT_EVENTS = "payment.events"
    SERVICE_UPDATES = "service.updates"


@dataclass
class EventMessage:
    """Enhanced event message with routing and delivery metadata."""

    id: str = field(default_factory=lambda: str(uuid4()))
    event_type: str = ""
    category: EventCategory = EventCategory.NOTIFICATION
    priority: MessagePriority = MessagePriority.STANDARD
    topic: Optional[SubscriptionTopic] = None

    # Message content
    payload: Dict[str, Any] = field(default_factory=dict)

    # Routing information
    target_user_ids: Set[int] = field(default_factory=set)
    target_room_ids: Set[int] = field(default_factory=set)
    target_roles: Set[str] = field(default_factory=set)
    geographic_filter: Optional[str] = None

    # Delivery tracking
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = None
    delivery_attempts: int = 0
    max_delivery_attempts: int = 3

    # Acknowledgment tracking
    requires_acknowledgment: bool = False
    acknowledged_by: Set[int] = field(default_factory=set)

    # Correlation tracking
    correlation_id: str = field(default_factory=lambda: correlation_id.get() or str(uuid4()))
    booking_id: Optional[int] = None

    def to_websocket_message(self) -> Dict[str, Any]:
        """Convert to WebSocket message format."""
        return {
            "id": self.id,
            "type": self.event_type,
            "category": self.category.value,
            "priority": self.priority.value,
            "topic": self.topic.value if self.topic else None,
            "data": self.payload,
            "timestamp": self.created_at.isoformat(),
            "correlation_id": self.correlation_id,
            "requires_ack": self.requires_acknowledgment,
            "booking_id": self.booking_id
        }


class EnhancedEventBroadcastingService(BaseService):
    """
    Enhanced event broadcasting service for real-time communication.

    Provides comprehensive event broadcasting including:
    - Priority-based message queuing and routing
    - Topic-based subscription management
    - Role-based message filtering
    - Message persistence and offline delivery
    - Integration with booking communication system
    """

    def __init__(self, db: AsyncSession, connection_manager=None):
        super().__init__(db)
        self.connection_manager = connection_manager

        # Priority-based message queues
        self.message_queues = {
            MessagePriority.IMMEDIATE: asyncio.Queue(maxsize=1000),
            MessagePriority.HIGH: asyncio.Queue(maxsize=5000),
            MessagePriority.STANDARD: asyncio.Queue(maxsize=10000),
            MessagePriority.LOW: asyncio.Queue(maxsize=20000)
        }

        # Subscription management
        self.user_subscriptions: Dict[int, Set[SubscriptionTopic]] = {}
        self.topic_subscribers: Dict[SubscriptionTopic, Set[int]] = {
            topic: set() for topic in SubscriptionTopic
        }

        # Message persistence for offline users
        self.offline_message_store: Dict[int, List[EventMessage]] = {}

        # Performance metrics
        self.metrics = {
            "messages_processed": 0,
            "messages_delivered": 0,
            "messages_failed": 0,
            "average_processing_time": 0,
            "average_delivery_time": 0,
            "acknowledgments_received": 0
        }

        # Background processing tasks
        self._processing_tasks: Set[asyncio.Task] = set()
        self._start_background_processing()

    async def broadcast_event(
        self,
        event_message: EventMessage,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast event message with priority-based routing.

        Performance target: <50ms event processing.
        """
        start_time = time.time()

        try:
            # Validate event message
            await self._validate_event_message(event_message, current_user)

            # Add to appropriate priority queue
            queue = self.message_queues[event_message.priority]
            await queue.put(event_message)

            # Update metrics
            self.metrics["messages_processed"] += 1
            processing_time = (time.time() - start_time) * 1000

            logger.info(f"Event {event_message.id} queued for broadcasting in {processing_time:.2f}ms")

            return {
                "event_id": event_message.id,
                "status": "queued",
                "priority": event_message.priority.value,
                "processing_time_ms": processing_time,
                "queue_size": queue.qsize()
            }

        except Exception as e:
            logger.error(f"Failed to broadcast event {event_message.id}: {str(e)}")
            self.metrics["messages_failed"] += 1
            raise ServiceError(f"Event broadcasting failed: {str(e)}")

    async def broadcast_booking_event(
        self,
        booking_id: int,
        event_type: str,
        event_data: Dict[str, Any],
        priority: MessagePriority = MessagePriority.HIGH,
        current_user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Broadcast booking-specific event to relevant participants.

        Performance target: <50ms event processing.
        """
        try:
            # Get booking details for participant targeting
            booking = await self._get_booking_details(booking_id)
            if not booking:
                raise NotFoundError(f"Booking {booking_id} not found")

            # Determine target users (customer and vendor)
            target_users = {booking.user_id}  # Customer
            if booking.vendor_id:
                target_users.add(booking.vendor_id)

            # Create event message
            event_message = EventMessage(
                event_type=event_type,
                category=EventCategory.BOOKING,
                priority=priority,
                topic=SubscriptionTopic.BOOKING_UPDATES,
                payload=event_data,
                target_user_ids=target_users,
                booking_id=booking_id,
                requires_acknowledgment=True
            )

            return await self.broadcast_event(event_message, current_user)

        except Exception as e:
            logger.error(f"Failed to broadcast booking event: {str(e)}")
            raise ServiceError(f"Booking event broadcasting failed: {str(e)}")

    async def subscribe_user_to_topic(
        self,
        user_id: int,
        topic: SubscriptionTopic,
        current_user: Optional[User] = None
    ) -> bool:
        """Subscribe user to a specific topic for selective message delivery."""
        try:
            # Validate subscription permissions
            await self._validate_subscription_permissions(user_id, topic, current_user)

            # Add subscription
            if user_id not in self.user_subscriptions:
                self.user_subscriptions[user_id] = set()

            self.user_subscriptions[user_id].add(topic)
            self.topic_subscribers[topic].add(user_id)

            logger.info(f"User {user_id} subscribed to topic {topic.value}")
            return True

        except Exception as e:
            logger.error(f"Failed to subscribe user {user_id} to topic {topic.value}: {str(e)}")
            return False

    async def unsubscribe_user_from_topic(
        self,
        user_id: int,
        topic: SubscriptionTopic,
        current_user: Optional[User] = None
    ) -> bool:
        """Unsubscribe user from a specific topic."""
        try:
            if user_id in self.user_subscriptions:
                self.user_subscriptions[user_id].discard(topic)
                if not self.user_subscriptions[user_id]:
                    del self.user_subscriptions[user_id]

            self.topic_subscribers[topic].discard(user_id)

            logger.info(f"User {user_id} unsubscribed from topic {topic.value}")
            return True

        except Exception as e:
            logger.error(f"Failed to unsubscribe user {user_id} from topic {topic.value}: {str(e)}")
            return False

    async def acknowledge_message(
        self,
        message_id: str,
        user_id: int,
        current_user: Optional[User] = None
    ) -> bool:
        """Acknowledge message delivery and processing."""
        try:
            # Find message in offline store or processing queues
            message = await self._find_message_by_id(message_id)
            if not message:
                logger.warning(f"Message {message_id} not found for acknowledgment")
                return False

            # Add acknowledgment
            message.acknowledged_by.add(user_id)
            self.metrics["acknowledgments_received"] += 1

            logger.debug(f"Message {message_id} acknowledged by user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to acknowledge message {message_id}: {str(e)}")
            return False

    async def get_offline_messages(
        self,
        user_id: int,
        current_user: Optional[User] = None
    ) -> List[Dict[str, Any]]:
        """Get offline messages for user upon reconnection."""
        try:
            if user_id not in self.offline_message_store:
                return []

            messages = self.offline_message_store[user_id]
            websocket_messages = [msg.to_websocket_message() for msg in messages]

            # Clear offline messages after retrieval
            del self.offline_message_store[user_id]

            logger.info(f"Retrieved {len(messages)} offline messages for user {user_id}")
            return websocket_messages

        except Exception as e:
            logger.error(f"Failed to get offline messages for user {user_id}: {str(e)}")
            return []

    async def get_broadcasting_metrics(self) -> Dict[str, Any]:
        """Get comprehensive broadcasting performance metrics."""
        return {
            **self.metrics,
            "queue_sizes": {
                priority.value: queue.qsize()
                for priority, queue in self.message_queues.items()
            },
            "active_subscriptions": len(self.user_subscriptions),
            "total_subscribers": sum(len(subscribers) for subscribers in self.topic_subscribers.values()),
            "offline_message_count": sum(len(messages) for messages in self.offline_message_store.values()),
            "background_tasks": len(self._processing_tasks)
        }

    # Background processing methods

    def _start_background_processing(self) -> None:
        """Start background tasks for message processing."""
        # Start priority-based processors
        for priority in MessagePriority:
            task = asyncio.create_task(self._process_priority_queue(priority))
            self._processing_tasks.add(task)
            task.add_done_callback(self._processing_tasks.discard)

    async def _process_priority_queue(self, priority: MessagePriority) -> None:
        """Process messages from a specific priority queue."""
        queue = self.message_queues[priority]

        # Set processing targets based on priority
        processing_targets = {
            MessagePriority.IMMEDIATE: 10,   # <10ms
            MessagePriority.HIGH: 25,        # <25ms
            MessagePriority.STANDARD: 50,    # <50ms
            MessagePriority.LOW: 100         # <100ms
        }

        target_time = processing_targets[priority]

        while True:
            try:
                # Get message from queue
                message = await queue.get()
                start_time = time.time()

                # Process and deliver message
                await self._deliver_event_message(message)

                # Check processing time
                processing_time = (time.time() - start_time) * 1000
                if processing_time > target_time:
                    logger.warning(f"Processing time {processing_time:.2f}ms exceeded target {target_time}ms for priority {priority.value}")

                # Update metrics
                self._update_processing_metrics(processing_time)

                # Mark task as done
                queue.task_done()

            except Exception as e:
                logger.error(f"Error processing {priority.value} priority message: {str(e)}")
                queue.task_done()

    async def _deliver_event_message(self, message: EventMessage) -> None:
        """Deliver event message to target recipients."""
        start_time = time.time()

        try:
            # Determine target recipients
            target_users = await self._resolve_target_users(message)

            if not target_users:
                logger.debug(f"No target users found for message {message.id}")
                return

            # Get connection manager
            if not self.connection_manager:
                from app.api.v1.endpoints.websocket_endpoints import connection_manager
                self.connection_manager = connection_manager

            # Prepare WebSocket message
            websocket_message = json.dumps(message.to_websocket_message())

            # Deliver to online users
            online_users = set()
            offline_users = set()

            for user_id in target_users:
                if user_id in self.connection_manager.user_connections:
                    # User is online - deliver immediately
                    delivered = await self.connection_manager.broadcast_to_user(user_id, websocket_message)
                    if delivered > 0:
                        online_users.add(user_id)
                        self.metrics["messages_delivered"] += 1
                    else:
                        offline_users.add(user_id)
                else:
                    # User is offline - store for later delivery
                    offline_users.add(user_id)

            # Store messages for offline users
            for user_id in offline_users:
                await self._store_offline_message(user_id, message)

            delivery_time = (time.time() - start_time) * 1000
            logger.info(f"Message {message.id} delivered to {len(online_users)} online users, {len(offline_users)} offline users in {delivery_time:.2f}ms")

        except Exception as e:
            logger.error(f"Failed to deliver message {message.id}: {str(e)}")
            self.metrics["messages_failed"] += 1

            # Retry logic for failed deliveries
            if message.delivery_attempts < message.max_delivery_attempts:
                message.delivery_attempts += 1
                await asyncio.sleep(2 ** message.delivery_attempts)  # Exponential backoff
                await self.message_queues[message.priority].put(message)

    async def _resolve_target_users(self, message: EventMessage) -> Set[int]:
        """Resolve target users based on message routing criteria."""
        target_users = set(message.target_user_ids)

        # Add users subscribed to the message topic
        if message.topic and message.topic in self.topic_subscribers:
            topic_subscribers = self.topic_subscribers[message.topic]
            target_users.update(topic_subscribers)

        # Filter by user roles if specified
        if message.target_roles:
            role_filtered_users = await self._get_users_by_roles(message.target_roles)
            if target_users:
                target_users.intersection_update(role_filtered_users)
            else:
                target_users.update(role_filtered_users)

        # Apply geographic filtering if specified
        if message.geographic_filter:
            geo_filtered_users = await self._get_users_by_geography(message.geographic_filter)
            if target_users:
                target_users.intersection_update(geo_filtered_users)
            else:
                target_users.update(geo_filtered_users)

        return target_users

    async def _store_offline_message(self, user_id: int, message: EventMessage) -> None:
        """Store message for offline user delivery."""
        if user_id not in self.offline_message_store:
            self.offline_message_store[user_id] = []

        self.offline_message_store[user_id].append(message)

        # Limit offline message storage (keep last 100 messages per user)
        if len(self.offline_message_store[user_id]) > 100:
            self.offline_message_store[user_id] = self.offline_message_store[user_id][-100:]

    def _update_processing_metrics(self, processing_time: float) -> None:
        """Update processing time metrics."""
        current_avg = self.metrics["average_processing_time"]
        processed_count = self.metrics["messages_processed"]

        # Calculate new average
        new_avg = ((current_avg * (processed_count - 1)) + processing_time) / processed_count
        self.metrics["average_processing_time"] = new_avg

    # Helper and validation methods

    async def _validate_event_message(
        self,
        message: EventMessage,
        current_user: Optional[User] = None
    ) -> None:
        """Validate event message before broadcasting."""
        if not message.event_type:
            raise ValidationError("Event type is required")

        if not message.payload:
            raise ValidationError("Event payload is required")

        # Validate expiration
        if message.expires_at and message.expires_at <= datetime.now(timezone.utc):
            raise ValidationError("Event message has expired")

        # Validate user permissions for broadcasting
        if current_user and message.category == EventCategory.SYSTEM:
            # Only admins can broadcast system events
            if not hasattr(current_user, 'role') or current_user.role != 'admin':
                raise ValidationError("Insufficient permissions for system event broadcasting")

    async def _validate_subscription_permissions(
        self,
        user_id: int,
        topic: SubscriptionTopic,
        current_user: Optional[User] = None
    ) -> None:
        """Validate user permissions for topic subscription."""
        # Users can only subscribe themselves unless they're admins
        if current_user and current_user.id != user_id:
            if not hasattr(current_user, 'role') or current_user.role != 'admin':
                raise ValidationError("Cannot subscribe other users to topics")

        # Validate topic-specific permissions
        restricted_topics = {SubscriptionTopic.VENDOR_ALERTS, SubscriptionTopic.SYSTEM_ANNOUNCEMENTS}
        if topic in restricted_topics:
            # Check if user has appropriate role for restricted topics
            user = await self._get_user_by_id(user_id)
            if not user or not hasattr(user, 'role') or user.role not in ['vendor', 'admin']:
                raise ValidationError(f"Insufficient permissions for topic {topic.value}")

    async def _find_message_by_id(self, message_id: str) -> Optional[EventMessage]:
        """Find message by ID in offline store or processing queues."""
        # Search in offline message store
        for user_messages in self.offline_message_store.values():
            for message in user_messages:
                if message.id == message_id:
                    return message

        # Note: In a production system, you might also search in persistent storage
        return None

    async def _get_booking_details(self, booking_id: int) -> Optional[Booking]:
        """Get booking details for event targeting."""
        try:
            # This would typically use a booking repository
            # For now, we'll create a mock implementation
            from app.repositories.booking_repository import BookingRepository
            booking_repo = BookingRepository(self.db)
            return await booking_repo.get_by_id(booking_id)
        except Exception as e:
            logger.error(f"Failed to get booking details for {booking_id}: {str(e)}")
            return None

    async def _get_users_by_roles(self, roles: Set[str]) -> Set[int]:
        """Get user IDs by roles for role-based filtering."""
        try:
            # This would typically use a user repository
            # For now, we'll return an empty set
            # In production, this would query the database for users with specified roles
            return set()
        except Exception as e:
            logger.error(f"Failed to get users by roles {roles}: {str(e)}")
            return set()

    async def _get_users_by_geography(self, geographic_filter: str) -> Set[int]:
        """Get user IDs by geographic criteria for location-based filtering."""
        try:
            # This would typically use a user repository with geographic queries
            # For now, we'll return an empty set
            # In production, this would query users based on location data
            return set()
        except Exception as e:
            logger.error(f"Failed to get users by geography {geographic_filter}: {str(e)}")
            return set()

    async def _get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID for permission validation."""
        try:
            # This would typically use a user repository
            # For now, we'll create a mock implementation
            from app.repositories.user_repository import UserRepository
            user_repo = UserRepository(self.db)
            return await user_repo.get_by_id(user_id)
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {str(e)}")
            return None

    async def cleanup(self) -> None:
        """Cleanup resources and stop background tasks."""
        # Cancel all background tasks
        for task in self._processing_tasks:
            task.cancel()

        # Wait for tasks to complete
        if self._processing_tasks:
            await asyncio.gather(*self._processing_tasks, return_exceptions=True)

        # Clear queues
        for queue in self.message_queues.values():
            while not queue.empty():
                try:
                    queue.get_nowait()
                    queue.task_done()
                except asyncio.QueueEmpty:
                    break

        logger.info("Enhanced event broadcasting service cleaned up")
