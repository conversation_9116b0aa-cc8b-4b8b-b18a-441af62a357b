"""
CDN Optimization API endpoints for Culture Connect Backend API.

This module provides comprehensive REST API endpoints for CDN optimization and asset delivery:
- CDN configuration management endpoints with CRUD operations
- Asset optimization endpoints for individual asset processing
- Asset bundling endpoints for multi-asset optimization
- CDN metrics and analytics endpoints for performance monitoring
- Asset delivery tracking endpoints for real-time monitoring

Implements Phase 7.3.4 requirements with FastAPI router patterns, RBAC integration,
comprehensive validation, and seamless integration with enhanced CDN services.
"""

from typing import Dict, Any, Optional, List
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body, UploadFile, File
from fastapi.responses import JSONResponse

from app.api.dependencies import (
    get_current_user, get_db, require_permission,
    get_correlation_id, get_cache_manager
)
from app.models.user import User
from app.schemas.cdn_schemas import (
    CDNConfigurationCreate, CDNConfigurationUpdate, CDNConfigurationResponse,
    AssetOptimizationCreate, AssetOptimizationResponse,
    AssetBundleCreate, AssetBundleResponse,
    CDNMetricsCreate, CDNMetricsResponse,
    AssetDeliveryCreate, AssetDeliveryResponse,
    CDNDashboardData, OptimizationRecommendation
)
from app.services.enhanced_cdn_service import EnhancedCDNService
from app.repositories.cdn_repositories import (
    CDNConfigurationRepository, AssetOptimizationRepository,
    AssetBundleRepository, CDNMetricsRepository, AssetDeliveryRepository
)
from app.core.logging import get_logger
from app.services.base import ValidationError, NotFoundError, PermissionError

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/cdn", tags=["cdn"])


# Dependency injection for services
async def get_enhanced_cdn_service(
    db=Depends(get_db),
    cache_manager=Depends(get_cache_manager)
) -> EnhancedCDNService:
    """Get enhanced CDN service with dependencies."""
    cdn_config_repo = CDNConfigurationRepository(db, cache_manager)
    asset_optimization_repo = AssetOptimizationRepository(db, cache_manager)
    asset_bundle_repo = AssetBundleRepository(db, cache_manager)
    cdn_metrics_repo = CDNMetricsRepository(db, cache_manager)
    asset_delivery_repo = AssetDeliveryRepository(db, cache_manager)

    return EnhancedCDNService(
        cdn_config_repo,
        asset_optimization_repo,
        asset_bundle_repo,
        cdn_metrics_repo,
        asset_delivery_repo
    )


# CDN Configuration Endpoints
@router.post(
    "/configurations",
    response_model=CDNConfigurationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create CDN configuration",
    description="Create a new CDN configuration for asset delivery optimization"
)
async def create_cdn_configuration(
    config_data: CDNConfigurationCreate = Body(..., description="CDN configuration data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_manage"))
) -> CDNConfigurationResponse:
    """
    Create a new CDN configuration.

    Requires 'cdn_manage' permission.
    """
    try:
        logger.info(f"Creating CDN configuration: {config_data.name} for provider {config_data.provider}")

        configuration = await cdn_service.create_cdn_configuration(
            config_data=config_data,
            correlation_id=correlation_id
        )

        return CDNConfigurationResponse.model_validate(configuration)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating CDN configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create CDN configuration"
        )


@router.get(
    "/configurations",
    response_model=List[CDNConfigurationResponse],
    summary="Get CDN configurations",
    description="Retrieve all active CDN configurations"
)
async def get_cdn_configurations(
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> List[CDNConfigurationResponse]:
    """
    Get all active CDN configurations.

    Requires 'cdn_view' permission.
    """
    try:
        configurations = await cdn_service.cdn_config_repo.get_active_configurations()

        return [CDNConfigurationResponse.model_validate(config) for config in configurations]

    except Exception as e:
        logger.error(f"Error getting CDN configurations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CDN configurations"
        )


@router.get(
    "/configurations/{config_id}",
    response_model=CDNConfigurationResponse,
    summary="Get CDN configuration",
    description="Retrieve a specific CDN configuration by ID"
)
async def get_cdn_configuration(
    config_id: UUID = Path(..., description="Configuration ID"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> CDNConfigurationResponse:
    """
    Get a specific CDN configuration.

    Requires 'cdn_view' permission.
    """
    try:
        configuration = await cdn_service.cdn_config_repo.get_by_id(config_id)

        if not configuration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CDN configuration not found: {config_id}"
            )

        return CDNConfigurationResponse.model_validate(configuration)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting CDN configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CDN configuration"
        )


@router.put(
    "/configurations/{config_id}",
    response_model=CDNConfigurationResponse,
    summary="Update CDN configuration",
    description="Update an existing CDN configuration"
)
async def update_cdn_configuration(
    config_id: UUID = Path(..., description="Configuration ID"),
    config_update: CDNConfigurationUpdate = Body(..., description="Configuration update data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_manage"))
) -> CDNConfigurationResponse:
    """
    Update an existing CDN configuration.

    Requires 'cdn_manage' permission.
    """
    try:
        # Check if configuration exists
        existing_config = await cdn_service.cdn_config_repo.get_by_id(config_id)
        if not existing_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CDN configuration not found: {config_id}"
            )

        # Update configuration
        updated_config = await cdn_service.cdn_config_repo.update(
            config_id,
            config_update.model_dump(exclude_unset=True)
        )

        logger.info(f"Updated CDN configuration: {config_id}")

        return CDNConfigurationResponse.model_validate(updated_config)

    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error updating CDN configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update CDN configuration"
        )


@router.delete(
    "/configurations/{config_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete CDN configuration",
    description="Delete a CDN configuration"
)
async def delete_cdn_configuration(
    config_id: UUID = Path(..., description="Configuration ID"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_manage"))
):
    """
    Delete a CDN configuration.

    Requires 'cdn_manage' permission.
    """
    try:
        # Check if configuration exists
        existing_config = await cdn_service.cdn_config_repo.get_by_id(config_id)
        if not existing_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CDN configuration not found: {config_id}"
            )

        # Delete configuration
        await cdn_service.cdn_config_repo.delete(config_id)

        logger.info(f"Deleted CDN configuration: {config_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting CDN configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete CDN configuration"
        )


# Asset Optimization Endpoints
@router.post(
    "/assets/optimize",
    response_model=AssetOptimizationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Optimize asset",
    description="Optimize an asset for CDN delivery with compression and caching"
)
async def optimize_asset(
    asset_path: str = Body(..., description="Path to asset file"),
    cdn_configuration_id: Optional[UUID] = Body(None, description="CDN configuration ID (uses default if None)"),
    optimization_type: str = Body("compression", description="Type of optimization"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_optimize"))
) -> AssetOptimizationResponse:
    """
    Optimize an asset for CDN delivery.

    Requires 'cdn_optimize' permission.
    """
    try:
        logger.info(f"Optimizing asset: {asset_path}")

        from app.models.cdn_models import OptimizationType
        opt_type = OptimizationType.COMPRESSION  # Default
        if optimization_type.lower() == "minification":
            opt_type = OptimizationType.MINIFICATION
        elif optimization_type.lower() == "bundling":
            opt_type = OptimizationType.BUNDLING

        optimization = await cdn_service.optimize_asset(
            asset_path=asset_path,
            cdn_configuration_id=cdn_configuration_id,
            optimization_type=opt_type,
            correlation_id=correlation_id
        )

        return AssetOptimizationResponse.model_validate(optimization)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error optimizing asset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to optimize asset"
        )


@router.get(
    "/assets/optimizations",
    response_model=List[AssetOptimizationResponse],
    summary="Get asset optimizations",
    description="Retrieve asset optimizations with optional filtering"
)
async def get_asset_optimizations(
    status_filter: Optional[str] = Query(None, description="Filter by delivery status"),
    asset_type: Optional[str] = Query(None, description="Filter by asset type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of optimizations to return"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> List[AssetOptimizationResponse]:
    """
    Get asset optimizations with optional filtering.

    Requires 'cdn_view' permission.
    """
    try:
        if status_filter:
            from app.models.cdn_models import DeliveryStatus
            status_enum = DeliveryStatus(status_filter.lower())
            optimizations = await cdn_service.asset_optimization_repo.get_by_status(status_enum, limit)
        elif asset_type:
            from app.models.cdn_models import AssetType
            type_enum = AssetType(asset_type.lower())
            optimizations = await cdn_service.asset_optimization_repo.get_by_asset_type(type_enum, limit)
        else:
            # Get recent optimizations
            optimizations = await cdn_service.asset_optimization_repo.get_all(limit=limit)

        return [AssetOptimizationResponse.model_validate(opt) for opt in optimizations]

    except Exception as e:
        logger.error(f"Error getting asset optimizations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve asset optimizations"
        )


@router.post(
    "/assets/bundles",
    response_model=AssetBundleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create asset bundle",
    description="Create an optimized bundle from multiple assets"
)
async def create_asset_bundle(
    bundle_data: AssetBundleCreate = Body(..., description="Asset bundle data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_optimize"))
) -> AssetBundleResponse:
    """
    Create an optimized asset bundle.

    Requires 'cdn_optimize' permission.
    """
    try:
        logger.info(f"Creating asset bundle: {bundle_data.bundle_name} with {len(bundle_data.asset_paths)} assets")

        bundle = await cdn_service.create_asset_bundle(
            bundle_name=bundle_data.bundle_name,
            asset_paths=bundle_data.asset_paths,
            bundle_type=bundle_data.bundle_type,
            cdn_configuration_id=bundle_data.cdn_configuration_id,
            minification_enabled=bundle_data.minification_enabled,
            correlation_id=correlation_id
        )

        return AssetBundleResponse.model_validate(bundle)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating asset bundle: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create asset bundle"
        )


@router.get(
    "/assets/bundles",
    response_model=List[AssetBundleResponse],
    summary="Get asset bundles",
    description="Retrieve asset bundles with optional filtering"
)
async def get_asset_bundles(
    bundle_type: Optional[str] = Query(None, description="Filter by bundle type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of bundles to return"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> List[AssetBundleResponse]:
    """
    Get asset bundles with optional filtering.

    Requires 'cdn_view' permission.
    """
    try:
        if bundle_type:
            bundles = await cdn_service.asset_bundle_repo.get_by_bundle_type(bundle_type, limit)
        else:
            bundles = await cdn_service.asset_bundle_repo.get_all(limit=limit)

        return [AssetBundleResponse.model_validate(bundle) for bundle in bundles]

    except Exception as e:
        logger.error(f"Error getting asset bundles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve asset bundles"
        )


# CDN Metrics and Analytics Endpoints
@router.post(
    "/metrics",
    response_model=CDNMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record CDN metrics",
    description="Record CDN performance metrics for analytics"
)
async def record_cdn_metrics(
    metrics_data: CDNMetricsCreate = Body(..., description="CDN metrics data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_metrics"))
) -> CDNMetricsResponse:
    """
    Record CDN performance metrics.

    Requires 'cdn_metrics' permission.
    """
    try:
        logger.info(f"Recording CDN metrics for configuration: {metrics_data.cdn_configuration_id}")

        metrics = await cdn_service.cdn_metrics_repo.create_metrics(
            metrics_data.model_dump()
        )

        return CDNMetricsResponse.model_validate(metrics)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error recording CDN metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record CDN metrics"
        )


@router.get(
    "/metrics/summary",
    response_model=Dict[str, Any],
    summary="Get CDN metrics summary",
    description="Get aggregated CDN performance metrics summary"
)
async def get_cdn_metrics_summary(
    cdn_configuration_id: Optional[UUID] = Query(None, description="Filter by CDN configuration"),
    hours: int = Query(24, ge=1, le=168, description="Number of hours of data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> Dict[str, Any]:
    """
    Get aggregated CDN metrics summary.

    Requires 'cdn_view' permission.
    """
    try:
        summary = await cdn_service.cdn_metrics_repo.get_metrics_summary(
            cdn_configuration_id=cdn_configuration_id,
            hours=hours
        )

        return summary

    except Exception as e:
        logger.error(f"Error getting CDN metrics summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CDN metrics summary"
        )


@router.get(
    "/analytics/dashboard",
    response_model=CDNDashboardData,
    summary="Get CDN dashboard data",
    description="Get comprehensive CDN analytics dashboard data"
)
async def get_cdn_dashboard_data(
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> CDNDashboardData:
    """
    Get comprehensive CDN dashboard data.

    Requires 'cdn_view' permission.
    """
    try:
        # Get configuration stats
        configurations = await cdn_service.cdn_config_repo.get_active_configurations()
        total_configurations = len(configurations)
        active_configurations = len([c for c in configurations if c.is_active])

        # Get optimization stats
        optimization_stats = await cdn_service.asset_optimization_repo.get_optimization_stats()

        # Get bundle stats
        bundle_stats = await cdn_service.asset_bundle_repo.get_bundle_stats()

        # Get metrics summary
        metrics_summary = await cdn_service.cdn_metrics_repo.get_metrics_summary()

        # Get recent optimizations
        recent_optimizations = await cdn_service.asset_optimization_repo.get_all(limit=10)

        dashboard_data = CDNDashboardData(
            total_configurations=total_configurations,
            active_configurations=active_configurations,
            total_optimizations=optimization_stats.get("total_optimizations", 0),
            successful_optimizations=optimization_stats.get("successful_optimizations", 0),
            total_bundles=bundle_stats.get("total_bundles", 0),
            avg_compression_ratio=Decimal(str(optimization_stats.get("avg_compression_ratio", 0.0))),
            avg_optimization_time_ms=Decimal(str(optimization_stats.get("avg_optimization_time_ms", 0.0))),
            cache_hit_rate=Decimal(str(metrics_summary.get("cache_hit_rate", 0.0))),
            bandwidth_savings_gb=Decimal(str(metrics_summary.get("bandwidth_savings_gb", 0.0))),
            top_performing_configs=[],  # Would be populated with actual data
            recent_optimizations=[AssetOptimizationResponse.model_validate(opt) for opt in recent_optimizations]
        )

        return dashboard_data

    except Exception as e:
        logger.error(f"Error getting CDN dashboard data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CDN dashboard data"
        )


@router.post(
    "/assets/delivery",
    response_model=AssetDeliveryResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record asset delivery",
    description="Record an asset delivery event for analytics"
)
async def record_asset_delivery(
    delivery_data: AssetDeliveryCreate = Body(..., description="Asset delivery data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_metrics"))
) -> AssetDeliveryResponse:
    """
    Record an asset delivery event.

    Requires 'cdn_metrics' permission.
    """
    try:
        delivery = await cdn_service.record_asset_delivery(
            asset_optimization_id=delivery_data.asset_optimization_id,
            delivery_data=delivery_data,
            correlation_id=correlation_id
        )

        return AssetDeliveryResponse.model_validate(delivery)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error recording asset delivery: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record asset delivery"
        )


@router.get(
    "/assets/delivery/performance",
    response_model=Dict[str, Any],
    summary="Get delivery performance metrics",
    description="Get asset delivery performance metrics and analytics"
)
async def get_delivery_performance_metrics(
    asset_optimization_id: Optional[UUID] = Query(None, description="Filter by asset optimization"),
    hours: int = Query(24, ge=1, le=168, description="Number of hours of data"),
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> Dict[str, Any]:
    """
    Get asset delivery performance metrics.

    Requires 'cdn_view' permission.
    """
    try:
        metrics = await cdn_service.asset_delivery_repo.get_performance_metrics(
            asset_optimization_id=asset_optimization_id,
            hours=hours
        )

        return metrics

    except Exception as e:
        logger.error(f"Error getting delivery performance metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve delivery performance metrics"
        )


@router.get(
    "/recommendations",
    response_model=List[OptimizationRecommendation],
    summary="Get optimization recommendations",
    description="Get AI-powered optimization recommendations for assets"
)
async def get_optimization_recommendations(
    current_user: User = Depends(get_current_user),
    cdn_service: EnhancedCDNService = Depends(get_enhanced_cdn_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("cdn_view"))
) -> List[OptimizationRecommendation]:
    """
    Get optimization recommendations for assets.

    Requires 'cdn_view' permission.
    """
    try:
        # This would implement AI-powered recommendations based on asset analysis
        # For now, return empty list as placeholder
        recommendations = []

        return recommendations

    except Exception as e:
        logger.error(f"Error getting optimization recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve optimization recommendations"
        )
