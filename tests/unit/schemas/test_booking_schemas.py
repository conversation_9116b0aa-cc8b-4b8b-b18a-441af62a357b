"""
Unit tests for booking schemas.

This module provides comprehensive unit tests for booking-related Pydantic schemas:
- BookingCreateSchema: Booking creation validation
- BookingUpdateSchema: Booking update validation
- BookingStatusUpdateSchema: Status change validation
- VendorResponseSchema: Vendor response validation
- BookingResponseSchema: API response serialization
- Field validation and business logic constraints

Implements Task 4.1.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from datetime import datetime, date, time
from decimal import Decimal
from uuid import uuid4
from typing import Dict, Any

from pydantic import ValidationError

from app.schemas.booking_schemas import (
    BookingCreateSchema, BookingUpdateSchema, BookingStatusUpdateSchema,
    VendorResponseSchema, BookingResponseSchema, BookingListResponseSchema
)
from app.models.booking import (
    BookingStatus, VendorResponseType, BookingPriority
)


class TestBookingCreateSchema:
    """Test BookingCreateSchema validation."""

    def test_valid_booking_creation(self):
        """Test valid booking creation data."""
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "availability_id": 456,
            "booking_date": future_date,
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2,
            "special_requirements": "Vegetarian meal required",
            "customer_notes": "Celebrating anniversary",
            "accessibility_requirements": {
                "wheelchair_accessible": True,
                "hearing_assistance": False
            },
            "priority": "normal",
            "booking_source": "web"
        }

        schema = BookingCreateSchema(**booking_data)

        assert schema.service_id == 123
        assert schema.availability_id == 456
        assert schema.booking_date == date.today() + timedelta(days=7)
        assert schema.booking_time == time(14, 30)
        assert schema.duration_hours == Decimal("2.5")
        assert schema.participant_count == 2
        assert schema.special_requirements == "Vegetarian meal required"
        assert schema.customer_notes == "Celebrating anniversary"
        assert schema.accessibility_requirements == {"wheelchair_accessible": True, "hearing_assistance": False}
        assert schema.priority == BookingPriority.NORMAL
        assert schema.booking_source == "web"

    def test_minimal_booking_creation(self):
        """Test booking creation with minimal required fields."""
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "booking_date": future_date
        }

        schema = BookingCreateSchema(**booking_data)

        assert schema.service_id == 123
        assert schema.booking_date == date.today() + timedelta(days=7)
        assert schema.availability_id is None
        assert schema.booking_time is None
        assert schema.duration_hours is None
        assert schema.participant_count == 1  # default
        assert schema.priority == BookingPriority.NORMAL  # default
        assert schema.booking_source == "web"  # default

    def test_booking_date_validation_past_date(self):
        """Test booking date validation for past dates."""
        from datetime import date as date_class
        yesterday = date_class.today().replace(day=date_class.today().day - 1)

        booking_data = {
            "service_id": 123,
            "booking_date": yesterday.isoformat()
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Booking date cannot be in the past" in str(exc_info.value)

    def test_booking_time_validation_business_hours(self):
        """Test booking time validation for business hours."""
        # Test early morning (before 6 AM)
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "booking_time": "05:30:00"
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Booking time must be between 6:00 AM and 11:00 PM" in str(exc_info.value)

        # Test late night (after 11 PM) - this should actually pass since 23:30 is 11:30 PM which is within range
        # Let's test with a time that's actually outside the range
        booking_data["booking_time"] = "23:45:00"  # 11:45 PM - outside the 11 PM limit

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Booking time must be between 6:00 AM and 11:00 PM" in str(exc_info.value)

    def test_participant_count_validation(self):
        """Test participant count validation."""
        # Test zero participants
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "participant_count": 0
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Input should be greater than or equal to 1" in str(exc_info.value)

        # Test too many participants
        booking_data["participant_count"] = 101

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Input should be less than or equal to 100" in str(exc_info.value)

    def test_duration_hours_validation(self):
        """Test duration hours validation."""
        # Test minimum duration
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "duration_hours": "0.25"
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Input should be greater than or equal to 0.5" in str(exc_info.value)

        # Test maximum duration
        booking_data["duration_hours"] = "25.0"

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "Input should be less than or equal to 24" in str(exc_info.value)

    def test_required_field_validation(self):
        """Test required field validation."""
        # Missing service_id
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "booking_date": future_date
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "service_id" in str(exc_info.value)
        assert "Field required" in str(exc_info.value)

        # Missing booking_date
        booking_data = {
            "service_id": 123
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "booking_date" in str(exc_info.value)
        assert "Field required" in str(exc_info.value)

    def test_string_length_validation(self):
        """Test string field length validation."""
        # Test special_requirements max length
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=7)).isoformat()

        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "special_requirements": "x" * 2001  # Exceeds 2000 char limit
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "String should have at most 2000 characters" in str(exc_info.value)

        # Test customer_notes max length
        booking_data = {
            "service_id": 123,
            "booking_date": future_date,
            "customer_notes": "x" * 1001  # Exceeds 1000 char limit
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingCreateSchema(**booking_data)

        assert "String should have at most 1000 characters" in str(exc_info.value)


class TestBookingUpdateSchema:
    """Test BookingUpdateSchema validation."""

    def test_valid_booking_update(self):
        """Test valid booking update data."""
        from datetime import date, timedelta
        future_date = (date.today() + timedelta(days=8)).isoformat()

        update_data = {
            "booking_date": future_date,
            "booking_time": "15:00:00",
            "participant_count": 3,
            "special_requirements": "Updated requirements",
            "priority": "high"
        }

        schema = BookingUpdateSchema(**update_data)

        assert schema.booking_date == date.today() + timedelta(days=8)
        assert schema.booking_time == time(15, 0)
        assert schema.participant_count == 3
        assert schema.special_requirements == "Updated requirements"
        assert schema.priority == BookingPriority.HIGH

    def test_partial_booking_update(self):
        """Test partial booking update with only some fields."""
        update_data = {
            "participant_count": 4
        }

        schema = BookingUpdateSchema(**update_data)

        assert schema.participant_count == 4
        assert schema.booking_date is None
        assert schema.booking_time is None
        assert schema.special_requirements is None

    def test_empty_booking_update(self):
        """Test empty booking update."""
        schema = BookingUpdateSchema()

        assert schema.booking_date is None
        assert schema.booking_time is None
        assert schema.participant_count is None
        assert schema.special_requirements is None

    def test_booking_update_date_validation(self):
        """Test booking update date validation."""
        from datetime import date as date_class
        yesterday = date_class.today().replace(day=date_class.today().day - 1)

        update_data = {
            "booking_date": yesterday.isoformat()
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingUpdateSchema(**update_data)

        assert "Booking date cannot be in the past" in str(exc_info.value)


class TestBookingStatusUpdateSchema:
    """Test BookingStatusUpdateSchema validation."""

    def test_valid_status_update(self):
        """Test valid status update data."""
        status_data = {
            "status": "confirmed",
            "change_reason": "Vendor approved booking",
            "change_notes": "All requirements can be accommodated",
            "notify_customer": True,
            "notify_vendor": False
        }

        schema = BookingStatusUpdateSchema(**status_data)

        assert schema.status == BookingStatus.CONFIRMED
        assert schema.change_reason == "Vendor approved booking"
        assert schema.change_notes == "All requirements can be accommodated"
        assert schema.notify_customer is True
        assert schema.notify_vendor is False

    def test_minimal_status_update(self):
        """Test minimal status update with only required fields."""
        status_data = {
            "status": "rejected"
        }

        schema = BookingStatusUpdateSchema(**status_data)

        assert schema.status == BookingStatus.REJECTED
        assert schema.change_reason is None
        assert schema.change_notes is None
        assert schema.notify_customer is True  # default
        assert schema.notify_vendor is True  # default

    def test_status_update_string_length_validation(self):
        """Test status update string length validation."""
        # Test change_reason max length
        status_data = {
            "status": "confirmed",
            "change_reason": "x" * 256  # Exceeds 255 char limit
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingStatusUpdateSchema(**status_data)

        assert "String should have at most 255 characters" in str(exc_info.value)

        # Test change_notes max length
        status_data = {
            "status": "confirmed",
            "change_notes": "x" * 1001  # Exceeds 1000 char limit
        }

        with pytest.raises(ValidationError) as exc_info:
            BookingStatusUpdateSchema(**status_data)

        assert "String should have at most 1000 characters" in str(exc_info.value)


class TestVendorResponseSchema:
    """Test VendorResponseSchema validation."""

    def test_valid_vendor_response_approved(self):
        """Test valid vendor response - approved."""
        response_data = {
            "response_type": "approved",
            "vendor_notes": "We can accommodate all your requirements"
        }

        schema = VendorResponseSchema(**response_data)

        assert schema.response_type == VendorResponseType.APPROVED
        assert schema.vendor_notes == "We can accommodate all your requirements"
        assert schema.counter_offer_price is None
        assert schema.alternative_dates is None
        assert schema.alternative_times is None

    def test_valid_vendor_response_with_alternatives(self):
        """Test valid vendor response with alternatives."""
        from datetime import date, timedelta

        response_data = {
            "response_type": "modification_requested",
            "vendor_notes": "We can offer alternative dates",
            "alternative_dates": [(date.today() + timedelta(days=10)).isoformat(), (date.today() + timedelta(days=11)).isoformat()],
            "alternative_times": ["14:00:00", "16:00:00"],
            "modifications_required": {
                "participant_count": 3,
                "special_note": "Maximum 3 people due to space constraints"
            }
        }

        schema = VendorResponseSchema(**response_data)

        assert schema.response_type == VendorResponseType.MODIFICATION_REQUESTED
        assert schema.vendor_notes == "We can offer alternative dates"
        assert schema.alternative_dates == [date.today() + timedelta(days=10), date.today() + timedelta(days=11)]
        assert schema.alternative_times == [time(14, 0), time(16, 0)]
        assert schema.modifications_required == {
            "participant_count": 3,
            "special_note": "Maximum 3 people due to space constraints"
        }

    def test_vendor_response_counter_offer(self):
        """Test vendor response with counter offer."""
        response_data = {
            "response_type": "counter_offer",
            "vendor_notes": "We can provide the service with a price adjustment",
            "counter_offer_price": "175.50"
        }

        schema = VendorResponseSchema(**response_data)

        assert schema.response_type == VendorResponseType.COUNTER_OFFER
        assert schema.counter_offer_price == Decimal("175.50")

    def test_vendor_response_alternative_dates_validation(self):
        """Test vendor response alternative dates validation."""
        from datetime import date as date_class
        yesterday = date_class.today().replace(day=date_class.today().day - 1)

        response_data = {
            "response_type": "modification_requested",
            "alternative_dates": [yesterday.isoformat()]
        }

        with pytest.raises(ValidationError) as exc_info:
            VendorResponseSchema(**response_data)

        assert "Alternative dates cannot be in the past" in str(exc_info.value)

    def test_vendor_response_counter_offer_validation(self):
        """Test vendor response counter offer validation."""
        # Test negative counter offer price
        response_data = {
            "response_type": "counter_offer",
            "counter_offer_price": "-50.00"
        }

        with pytest.raises(ValidationError) as exc_info:
            VendorResponseSchema(**response_data)

        assert "Input should be greater than or equal to 0" in str(exc_info.value)


class TestBookingResponseSchema:
    """Test BookingResponseSchema serialization."""

    def test_booking_response_serialization(self):
        """Test booking response schema serialization."""
        from datetime import date, timedelta

        booking_data = {
            "id": 123,
            "uuid": str(uuid4()),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_reference": "BK-2025-001234",
            "booking_date": date.today() + timedelta(days=7),
            "booking_time": time(14, 30),
            "participant_count": 2,
            "status": BookingStatus.CONFIRMED,
            "priority": BookingPriority.NORMAL,
            "base_price": Decimal("150.00"),
            "additional_fees": Decimal("15.00"),
            "discount_amount": Decimal("5.00"),
            "total_amount": Decimal("160.00"),
            "currency": "NGN",
            "payment_status": "paid",
            "completion_confirmed_by_customer": False,
            "completion_confirmed_by_vendor": False,
            "unread_messages_customer": 0,
            "unread_messages_vendor": 1,
            "refund_amount": Decimal("0.00"),
            "booking_source": "web"
        }

        schema = BookingResponseSchema(**booking_data)

        assert schema.id == 123
        assert schema.booking_reference == "BK-2025-001234"
        assert schema.status == BookingStatus.CONFIRMED
        assert schema.total_amount == Decimal("160.00")
        assert schema.payment_status == "paid"

    def test_booking_response_with_optional_fields(self):
        """Test booking response with optional fields."""
        from datetime import date, timedelta

        booking_data = {
            "id": 124,
            "uuid": str(uuid4()),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_reference": "BK-2025-001235",
            "booking_date": date.today() + timedelta(days=8),
            "participant_count": 1,
            "status": BookingStatus.PENDING,
            "priority": BookingPriority.NORMAL,
            "base_price": Decimal("100.00"),
            "additional_fees": Decimal("0.00"),
            "discount_amount": Decimal("0.00"),
            "total_amount": Decimal("100.00"),
            "currency": "NGN",
            "payment_status": "pending",
            "completion_confirmed_by_customer": False,
            "completion_confirmed_by_vendor": False,
            "unread_messages_customer": 0,
            "unread_messages_vendor": 0,
            "refund_amount": Decimal("0.00"),
            "booking_source": "mobile",
            # Optional fields
            "vendor_response_type": VendorResponseType.APPROVED,
            "vendor_notes": "Approved",
            "special_requirements": "None",
            "customer_rating": Decimal("4.5"),
            "customer_review": "Great service!"
        }

        schema = BookingResponseSchema(**booking_data)

        assert schema.vendor_response_type == VendorResponseType.APPROVED
        assert schema.vendor_notes == "Approved"
        assert schema.special_requirements == "None"
        assert schema.customer_rating == Decimal("4.5")
        assert schema.customer_review == "Great service!"


class TestBookingListResponseSchema:
    """Test BookingListResponseSchema pagination."""

    def test_booking_list_response(self):
        """Test booking list response with pagination."""
        from datetime import date, timedelta

        booking_data = {
            "id": 123,
            "uuid": str(uuid4()),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_reference": "BK-2025-001234",
            "booking_date": date.today() + timedelta(days=7),
            "participant_count": 1,
            "status": BookingStatus.CONFIRMED,
            "priority": BookingPriority.NORMAL,
            "base_price": Decimal("100.00"),
            "additional_fees": Decimal("0.00"),
            "discount_amount": Decimal("0.00"),
            "total_amount": Decimal("100.00"),
            "currency": "NGN",
            "payment_status": "paid",
            "completion_confirmed_by_customer": False,
            "completion_confirmed_by_vendor": False,
            "unread_messages_customer": 0,
            "unread_messages_vendor": 0,
            "refund_amount": Decimal("0.00"),
            "booking_source": "web"
        }

        list_data = {
            "bookings": [BookingResponseSchema(**booking_data)],
            "total": 25,
            "page": 1,
            "per_page": 10,
            "pages": 3,
            "has_next": True,
            "has_prev": False
        }

        schema = BookingListResponseSchema(**list_data)

        assert len(schema.bookings) == 1
        assert schema.total == 25
        assert schema.page == 1
        assert schema.per_page == 10
        assert schema.pages == 3
        assert schema.has_next is True
        assert schema.has_prev is False
