"""
Integration Tests for Advanced Search Functionality.

This module provides comprehensive integration tests for advanced search operations including:
- Advanced service search with multiple criteria and filters
- Geospatial search with location-based filtering
- Search suggestions and autocomplete functionality
- Search performance and response time validation
- Search result accuracy and relevance testing

Implements integration testing with database rollback mechanisms,
performance testing with <200ms query targets, and comprehensive search validation.
"""

import pytest
import asyncio
from datetime import datetime, timezone, date, timedelta
from decimal import Decimal
from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.core.deps import get_db
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service, ServiceCategory, ServicePricing
from tests.conftest import (
    TestingSessionLocal,
    create_test_user,
    create_test_vendor,
    create_test_service,
    get_test_token_headers
)


class TestAdvancedSearchEndpoints:
    """Integration test suite for advanced search API endpoints."""

    @pytest.fixture
    async def db_session(self):
        """Create test database session with rollback."""
        async with TestingSessionLocal() as session:
            yield session
            await session.rollback()

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Create test user."""
        return await create_test_user(
            db_session,
            email="<EMAIL>",
            password="testpass123",
            role="customer"
        )

    @pytest.fixture
    async def test_vendor(self, db_session: AsyncSession):
        """Create test vendor."""
        vendor_user = await create_test_user(
            db_session,
            email="<EMAIL>",
            password="testpass123",
            role="vendor"
        )
        return await create_test_vendor(
            db_session,
            user_id=vendor_user.id,
            business_name="Cultural Photography Studio",
            business_type="photography"
        )

    @pytest.fixture
    async def test_category(self, db_session: AsyncSession):
        """Create test service category."""
        category = ServiceCategory(
            name="Photography",
            slug="photography",
            description="Professional photography services",
            is_active=True
        )
        db_session.add(category)
        await db_session.commit()
        return category

    @pytest.fixture
    async def test_services(
        self, 
        db_session: AsyncSession, 
        test_vendor: Vendor, 
        test_category: ServiceCategory
    ):
        """Create test services for search testing."""
        services = []
        
        # Service 1: Wedding Photography
        service1 = await create_test_service(
            db_session,
            vendor_id=test_vendor.id,
            category_id=test_category.id,
            title="Professional Wedding Photography",
            description="Capture your special day with professional wedding photography services",
            location="Lagos, Nigeria",
            tags=["wedding", "photography", "professional", "couples"]
        )
        
        # Add pricing for service 1
        pricing1 = ServicePricing(
            service_id=service1.id,
            tier_name="Standard Package",
            price=Decimal("50000.00"),
            currency="NGN",
            is_active=True
        )
        db_session.add(pricing1)
        services.append(service1)
        
        # Service 2: Portrait Photography
        service2 = await create_test_service(
            db_session,
            vendor_id=test_vendor.id,
            category_id=test_category.id,
            title="Portrait Photography Sessions",
            description="Professional portrait photography for individuals and families",
            location="Abuja, Nigeria",
            tags=["portrait", "photography", "family", "individual"]
        )
        
        # Add pricing for service 2
        pricing2 = ServicePricing(
            service_id=service2.id,
            tier_name="Basic Session",
            price=Decimal("25000.00"),
            currency="NGN",
            is_active=True
        )
        db_session.add(pricing2)
        services.append(service2)
        
        # Service 3: Event Photography
        service3 = await create_test_service(
            db_session,
            vendor_id=test_vendor.id,
            category_id=test_category.id,
            title="Corporate Event Photography",
            description="Professional photography for corporate events and conferences",
            location="Lagos, Nigeria",
            tags=["corporate", "events", "photography", "business"]
        )
        
        # Add pricing for service 3
        pricing3 = ServicePricing(
            service_id=service3.id,
            tier_name="Event Package",
            price=Decimal("75000.00"),
            currency="NGN",
            is_active=True
        )
        db_session.add(pricing3)
        services.append(service3)
        
        await db_session.commit()
        return services

    @pytest.fixture
    async def auth_headers(self, test_user: User):
        """Get authentication headers for test user."""
        return await get_test_token_headers(test_user)

    @pytest.mark.asyncio
    async def test_advanced_search_text_query_success(
        self,
        db_session: AsyncSession,
        test_services: list,
        auth_headers: dict
    ):
        """Test advanced search with text query."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare search request
            search_request = {
                "query": "wedding photography",
                "sort_by": "relevance",
                "page": 1,
                "per_page": 20
            }
            
            # Act
            start_time = datetime.now(timezone.utc)
            response = await client.post(
                "/api/v1/search/search",
                json=search_request,
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            # Performance validation: <200ms for search queries
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            assert execution_time_ms < 200
            
            data = response.json()
            assert "results" in data
            assert "metadata" in data
            assert "aggregations" in data
            
            # Verify metadata
            metadata = data["metadata"]
            assert metadata["page"] == 1
            assert metadata["per_page"] == 20
            assert metadata["query"] == "wedding photography"
            assert metadata["sort_by"] == "relevance"
            assert "search_time_ms" in metadata
            
            # Verify results structure
            if data["results"]:
                result = data["results"][0]
                assert "id" in result
                assert "title" in result
                assert "description" in result
                assert "vendor" in result
                assert "category" in result
                assert "pricing" in result
                assert "average_rating" in result
                assert "total_reviews" in result
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_advanced_search_with_filters_success(
        self,
        db_session: AsyncSession,
        test_services: list,
        test_category: ServiceCategory,
        auth_headers: dict
    ):
        """Test advanced search with multiple filters."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare search request with filters
            search_request = {
                "query": "photography",
                "category_ids": [test_category.id],
                "price_range": {
                    "min_price": 20000,
                    "max_price": 60000,
                    "currency": "NGN"
                },
                "min_rating": 3.0,
                "tags": ["photography"],
                "sort_by": "price_low",
                "page": 1,
                "per_page": 10
            }
            
            # Act
            response = await client.post(
                "/api/v1/search/search",
                json=search_request,
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert "results" in data
            assert "metadata" in data
            assert "aggregations" in data
            
            # Verify filters were applied
            metadata = data["metadata"]
            filters_applied = metadata["filters_applied"]
            assert filters_applied["category_ids"] == [test_category.id]
            assert filters_applied["price_range"]["min_price"] == 20000
            assert filters_applied["price_range"]["max_price"] == 60000
            assert filters_applied["min_rating"] == 3.0
            assert filters_applied["tags"] == ["photography"]
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_location_search_success(
        self,
        db_session: AsyncSession,
        test_services: list,
        auth_headers: dict
    ):
        """Test location-based search."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare location search request
            search_request = {
                "latitude": 6.5244,
                "longitude": 3.3792,
                "radius_km": 50.0,
                "limit": 20
            }
            
            # Act
            start_time = datetime.now(timezone.utc)
            response = await client.post(
                "/api/v1/search/location",
                json=search_request,
                headers=auth_headers
            )
            end_time = datetime.now(timezone.utc)
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            # Performance validation: <200ms for location queries
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            assert execution_time_ms < 200
            
            data = response.json()
            assert "results" in data
            assert "center" in data
            assert "total_found" in data
            
            # Verify center coordinates
            center = data["center"]
            assert center["latitude"] == 6.5244
            assert center["longitude"] == 3.3792
            assert center["radius_km"] == 50.0
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_suggestions_success(
        self,
        db_session: AsyncSession,
        auth_headers: dict
    ):
        """Test search suggestions endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.get(
                "/api/v1/search/suggestions?query=photo&limit=5",
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert "suggestions" in data
            assert "query" in data
            assert data["query"] == "photo"
            assert len(data["suggestions"]) <= 5
            
            # Verify suggestions contain the query term
            for suggestion in data["suggestions"]:
                assert "photo" in suggestion.lower()
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_popular_searches_success(
        self,
        db_session: AsyncSession,
        auth_headers: dict
    ):
        """Test popular searches endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.get(
                "/api/v1/search/popular",
                headers=auth_headers
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            
            data = response.json()
            assert "popular_queries" in data
            assert "trending_categories" in data
            assert "featured_services" in data
            
            # Verify popular queries structure
            assert isinstance(data["popular_queries"], list)
            assert len(data["popular_queries"]) > 0
            
            # Verify trending categories structure
            assert isinstance(data["trending_categories"], list)
            if data["trending_categories"]:
                category = data["trending_categories"][0]
                assert "id" in category
                assert "name" in category
                assert "slug" in category
                assert "trend_score" in category
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_health_check_success(self, db_session: AsyncSession):
        """Test search service health check."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Act
            response = await client.get("/api/v1/search/health")
            
            # Assert
            assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
            
            data = response.json()
            assert "status" in data
            assert "timestamp" in data
            assert "services" in data
            assert "version" in data
            
            # Verify services status
            services = data["services"]
            assert "database" in services
            assert "elasticsearch" in services
            assert "search_api" in services
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_unauthorized_access(self, db_session: AsyncSession):
        """Test search endpoints without authentication."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test advanced search without auth
            search_request = {"query": "photography"}
            response = await client.post("/api/v1/search/search", json=search_request)
            
            # Should allow anonymous search (depending on business requirements)
            # For now, assuming search is public
            assert response.status_code in [status.HTTP_200_OK, status.HTTP_401_UNAUTHORIZED]
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_performance_under_load(
        self,
        db_session: AsyncSession,
        test_services: list,
        auth_headers: dict
    ):
        """Test search performance under concurrent load."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Prepare multiple concurrent search requests
            search_requests = [
                {"query": "photography", "page": i, "per_page": 10}
                for i in range(1, 6)  # 5 concurrent requests
            ]
            
            # Act - Execute concurrent searches
            start_time = datetime.now(timezone.utc)
            
            tasks = []
            for search_request in search_requests:
                task = client.post(
                    "/api/v1/search/search",
                    json=search_request,
                    headers=auth_headers
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            end_time = datetime.now(timezone.utc)
            
            # Assert
            total_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # All requests should succeed
            for response in responses:
                assert response.status_code == status.HTTP_200_OK
            
            # Average response time should be reasonable
            avg_time_ms = total_time_ms / len(responses)
            assert avg_time_ms < 300  # Target: <300ms average under load
            
            # Clean up
            app.dependency_overrides.clear()

    @pytest.mark.asyncio
    async def test_search_pagination_consistency(
        self,
        db_session: AsyncSession,
        test_services: list,
        auth_headers: dict
    ):
        """Test search pagination consistency."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Override database dependency
            app.dependency_overrides[get_db] = lambda: db_session
            
            # Test first page
            search_request = {
                "query": "photography",
                "page": 1,
                "per_page": 2,
                "sort_by": "relevance"
            }
            
            response1 = await client.post(
                "/api/v1/search/search",
                json=search_request,
                headers=auth_headers
            )
            
            # Test second page
            search_request["page"] = 2
            response2 = await client.post(
                "/api/v1/search/search",
                json=search_request,
                headers=auth_headers
            )
            
            # Assert
            assert response1.status_code == status.HTTP_200_OK
            assert response2.status_code == status.HTTP_200_OK
            
            data1 = response1.json()
            data2 = response2.json()
            
            # Verify pagination metadata
            assert data1["metadata"]["page"] == 1
            assert data2["metadata"]["page"] == 2
            assert data1["metadata"]["per_page"] == 2
            assert data2["metadata"]["per_page"] == 2
            
            # Verify no duplicate results between pages
            if data1["results"] and data2["results"]:
                page1_ids = {result["id"] for result in data1["results"]}
                page2_ids = {result["id"] for result in data2["results"]}
                assert page1_ids.isdisjoint(page2_ids)  # No overlapping results
            
            # Clean up
            app.dependency_overrides.clear()
