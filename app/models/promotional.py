"""
Promotional models for Culture Connect Backend API.

This module defines comprehensive promotional and advertising models for campaign management,
analytics, and marketing operations following Phase 5 implementation requirements.

Implements Task 5.1.1 Phase 1 requirements for promotional data models with:
- Core campaign management with budget tracking and multi-provider payment integration
- Advertisement management with creative assets and placement optimization
- Performance tracking and analytics with <200ms query targets
- Featured placement management for marketplace optimization
- Financial tracking and billing integration with existing payment infrastructure

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import enum
from datetime import datetime, date
from decimal import Decimal
from typing import Optional, Dict, Any, List
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Numeric, Enum, JSON, Index, CheckConstraint, UniqueConstraint, Date
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit
from app.core.payment.config import PaymentProviderType


class CampaignStatus(str, enum.Enum):
    """Enumeration of campaign statuses."""
    DRAFT = "draft"
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class CampaignType(str, enum.Enum):
    """Enumeration of campaign types."""
    FEATURED_LISTING = "featured_listing"
    SPONSORED_SEARCH = "sponsored_search"
    DISCOVERY_FEED = "discovery_feed"
    BANNER_AD = "banner_ad"
    PROMOTED_SERVICE = "promoted_service"
    CATEGORY_SPOTLIGHT = "category_spotlight"


class CampaignObjective(str, enum.Enum):
    """Enumeration of campaign objectives."""
    BRAND_AWARENESS = "brand_awareness"
    LEAD_GENERATION = "lead_generation"
    BOOKING_CONVERSION = "booking_conversion"
    TRAFFIC_INCREASE = "traffic_increase"
    ENGAGEMENT = "engagement"
    REACH = "reach"


class BidStrategy(str, enum.Enum):
    """Enumeration of bidding strategies."""
    MANUAL_CPC = "manual_cpc"
    AUTO_CPC = "auto_cpc"
    TARGET_CPA = "target_cpa"
    TARGET_ROAS = "target_roas"
    MAXIMIZE_CLICKS = "maximize_clicks"
    MAXIMIZE_CONVERSIONS = "maximize_conversions"


class TargetingType(str, enum.Enum):
    """Enumeration of targeting types."""
    GEOGRAPHIC = "geographic"
    DEMOGRAPHIC = "demographic"
    BEHAVIORAL = "behavioral"
    INTEREST_BASED = "interest_based"
    LOOKALIKE = "lookalike"
    CUSTOM_AUDIENCE = "custom_audience"


class AdFormat(str, enum.Enum):
    """Enumeration of advertisement formats."""
    IMAGE = "image"
    VIDEO = "video"
    CAROUSEL = "carousel"
    TEXT = "text"
    RICH_MEDIA = "rich_media"
    INTERACTIVE = "interactive"


class AdStatus(str, enum.Enum):
    """Enumeration of advertisement statuses."""
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    ACTIVE = "active"
    PAUSED = "paused"
    REJECTED = "rejected"
    EXPIRED = "expired"


class PlacementType(str, enum.Enum):
    """Enumeration of placement types."""
    HOMEPAGE_HERO = "homepage_hero"
    CATEGORY_TOP = "category_top"
    SEARCH_RESULTS = "search_results"
    SERVICE_DETAIL = "service_detail"
    VENDOR_PROFILE = "vendor_profile"
    MOBILE_BANNER = "mobile_banner"
    EMAIL_NEWSLETTER = "email_newsletter"


class PlacementStatus(str, enum.Enum):
    """Enumeration of placement statuses."""
    AVAILABLE = "available"
    RESERVED = "reserved"
    OCCUPIED = "occupied"
    MAINTENANCE = "maintenance"


class SpendCategory(str, enum.Enum):
    """Enumeration of ad spend categories."""
    CAMPAIGN_BUDGET = "campaign_budget"
    PLACEMENT_FEE = "placement_fee"
    CREATIVE_PRODUCTION = "creative_production"
    TARGETING_PREMIUM = "targeting_premium"
    PERFORMANCE_BONUS = "performance_bonus"
    PLATFORM_FEE = "platform_fee"


class Campaign(BaseModelWithAudit):
    """
    Campaign model for promotional campaign management.

    This model stores comprehensive campaign information including budget tracking,
    targeting configuration, and performance optimization for marketplace vendors.

    Features:
    - Multi-provider payment integration (Paystack, Stripe, Busha)
    - Geolocation-based targeting capabilities
    - Performance metrics tracking with <200ms query targets
    - Comprehensive audit logging and budget management
    - Integration with existing vendor and payment systems
    """

    __tablename__ = "campaigns"

    # Core campaign information
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Campaign name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Campaign description and objectives"
    )

    campaign_type = Column(
        Enum(CampaignType),
        nullable=False,
        index=True,
        doc="Type of promotional campaign"
    )

    campaign_objective = Column(
        Enum(CampaignObjective),
        nullable=False,
        index=True,
        doc="Primary campaign objective"
    )

    status = Column(
        Enum(CampaignStatus),
        nullable=False,
        default=CampaignStatus.DRAFT,
        index=True,
        doc="Current campaign status"
    )

    # Budget and financial tracking
    total_budget = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Total campaign budget in NGN"
    )

    daily_budget = Column(
        Numeric(10, 2),
        nullable=True,
        doc="Daily spending limit in NGN"
    )

    spent_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Total amount spent on campaign"
    )

    remaining_budget = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Remaining campaign budget"
    )

    # Payment integration
    payment_provider = Column(
        Enum(PaymentProviderType),
        nullable=True,
        index=True,
        doc="Payment provider for campaign billing"
    )

    payment_method_id = Column(
        String(255),
        nullable=True,
        doc="Payment method identifier"
    )

    # TODO-PAYSTACK-API-URL: Paystack payment configuration for campaign billing
    # TODO-STRIPE-API-URL: Stripe payment configuration for international campaigns
    # TODO-BUSHA-API-URL: Busha cryptocurrency payment configuration

    # Bidding and optimization
    bid_strategy = Column(
        Enum(BidStrategy),
        nullable=False,
        default=BidStrategy.MANUAL_CPC,
        doc="Bidding strategy for campaign"
    )

    target_cpc = Column(
        Numeric(8, 2),
        nullable=True,
        doc="Target cost per click in NGN"
    )

    target_cpa = Column(
        Numeric(10, 2),
        nullable=True,
        doc="Target cost per acquisition in NGN"
    )

    target_roas = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Target return on ad spend ratio"
    )

    # Scheduling and duration
    start_date = Column(
        Date,
        nullable=True,
        index=True,
        doc="Campaign start date"
    )

    end_date = Column(
        Date,
        nullable=True,
        index=True,
        doc="Campaign end date"
    )

    timezone = Column(
        String(50),
        nullable=False,
        default="Africa/Lagos",
        doc="Campaign timezone"
    )

    # Targeting configuration
    targeting_config = Column(
        JSON,
        nullable=True,
        doc="JSON configuration for campaign targeting"
    )

    geographic_targeting = Column(
        JSON,
        nullable=True,
        doc="Geographic targeting configuration"
    )

    demographic_targeting = Column(
        JSON,
        nullable=True,
        doc="Demographic targeting configuration"
    )

    # Performance tracking
    impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total campaign impressions"
    )

    clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total campaign clicks"
    )

    conversions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total campaign conversions"
    )

    revenue_generated = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Revenue generated from campaign"
    )

    # Approval and review
    approved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Campaign approval timestamp"
    )

    approved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="ID of admin who approved campaign"
    )

    rejection_reason = Column(
        Text,
        nullable=True,
        doc="Reason for campaign rejection"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="campaigns",
        foreign_keys=[vendor_id],
        doc="Associated vendor"
    )

    advertisements = relationship(
        "Advertisement",
        back_populates="campaign",
        cascade="all, delete-orphan",
        doc="Campaign advertisements"
    )

    metrics = relationship(
        "CampaignMetrics",
        back_populates="campaign",
        cascade="all, delete-orphan",
        doc="Campaign performance metrics"
    )

    ad_spends = relationship(
        "AdSpend",
        back_populates="campaign",
        cascade="all, delete-orphan",
        doc="Campaign spending records"
    )

    created_by_user = relationship(
        "User",
        foreign_keys="Campaign.created_by",
        back_populates="created_campaigns",
        doc="User who created this campaign"
    )

    approved_by_user = relationship(
        "User",
        foreign_keys="Campaign.approved_by",
        back_populates="approved_campaigns",
        doc="User who approved this campaign"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "total_budget >= 1000.00",
            name="check_campaign_min_budget"
        ),
        CheckConstraint(
            "total_budget <= 10000000.00",
            name="check_campaign_max_budget"
        ),
        CheckConstraint(
            "daily_budget IS NULL OR daily_budget >= 100.00",
            name="check_campaign_min_daily_budget"
        ),
        CheckConstraint(
            "spent_amount >= 0.00",
            name="check_campaign_spent_amount_positive"
        ),
        CheckConstraint(
            "remaining_budget >= 0.00",
            name="check_campaign_remaining_budget_positive"
        ),
        CheckConstraint(
            "target_cpc IS NULL OR target_cpc >= 1.00",
            name="check_campaign_min_cpc"
        ),
        CheckConstraint(
            "target_cpa IS NULL OR target_cpa >= 10.00",
            name="check_campaign_min_cpa"
        ),
        CheckConstraint(
            "target_roas IS NULL OR target_roas >= 1.00",
            name="check_campaign_min_roas"
        ),
        CheckConstraint(
            "end_date IS NULL OR start_date IS NULL OR end_date >= start_date",
            name="check_campaign_date_order"
        ),
        CheckConstraint(
            "impressions >= 0",
            name="check_campaign_impressions_positive"
        ),
        CheckConstraint(
            "clicks >= 0",
            name="check_campaign_clicks_positive"
        ),
        CheckConstraint(
            "conversions >= 0",
            name="check_campaign_conversions_positive"
        ),
        CheckConstraint(
            "revenue_generated >= 0.00",
            name="check_campaign_revenue_positive"
        ),

        # Performance indexes for <200ms query targets
        Index("idx_campaign_vendor_status", "vendor_id", "status"),
        Index("idx_campaign_type_status", "campaign_type", "status"),
        Index("idx_campaign_dates", "start_date", "end_date"),
        Index("idx_campaign_budget", "total_budget", "spent_amount"),
        Index("idx_campaign_performance", "impressions", "clicks", "conversions"),
        Index("idx_campaign_provider", "payment_provider", "status"),
        Index("idx_campaign_objective", "campaign_objective", "status"),
        Index("idx_campaign_approval", "approved_at", "approved_by"),
        Index("idx_campaign_active_dates", "status", "start_date", "end_date"),
    )

    def __repr__(self) -> str:
        """String representation of the campaign."""
        return f"<Campaign(id={self.id}, name='{self.name}', vendor_id={self.vendor_id}, status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if campaign is currently active."""
        return self.status == CampaignStatus.ACTIVE

    @property
    def is_completed(self) -> bool:
        """Check if campaign is completed."""
        return self.status == CampaignStatus.COMPLETED

    @property
    def budget_utilization_percentage(self) -> float:
        """Calculate budget utilization percentage."""
        if self.total_budget == 0:
            return 0.0
        return float((self.spent_amount / self.total_budget) * 100)

    @property
    def click_through_rate(self) -> float:
        """Calculate click-through rate."""
        if self.impressions == 0:
            return 0.0
        return float((self.clicks / self.impressions) * 100)

    @property
    def conversion_rate(self) -> float:
        """Calculate conversion rate."""
        if self.clicks == 0:
            return 0.0
        return float((self.conversions / self.clicks) * 100)

    @property
    def cost_per_click(self) -> float:
        """Calculate actual cost per click."""
        if self.clicks == 0:
            return 0.0
        return float(self.spent_amount / self.clicks)

    @property
    def cost_per_acquisition(self) -> float:
        """Calculate actual cost per acquisition."""
        if self.conversions == 0:
            return 0.0
        return float(self.spent_amount / self.conversions)

    @property
    def return_on_ad_spend(self) -> float:
        """Calculate actual return on ad spend."""
        if self.spent_amount == 0:
            return 0.0
        return float(self.revenue_generated / self.spent_amount)


class Advertisement(BaseModelWithAudit):
    """
    Advertisement model for individual ad management with creative assets.

    This model stores comprehensive advertisement information including creative assets,
    placement configuration, and performance optimization for campaign ads.

    Features:
    - Creative asset management with multiple formats
    - Placement optimization and targeting
    - Performance tracking with <200ms query targets
    - A/B testing support for ad variations
    - Integration with campaign and placement systems
    """

    __tablename__ = "advertisements"

    # Core advertisement information
    campaign_id = Column(
        Integer,
        ForeignKey("campaigns.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated campaign ID"
    )

    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Advertisement name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Advertisement description"
    )

    ad_format = Column(
        Enum(AdFormat),
        nullable=False,
        index=True,
        doc="Advertisement format type"
    )

    status = Column(
        Enum(AdStatus),
        nullable=False,
        default=AdStatus.DRAFT,
        index=True,
        doc="Current advertisement status"
    )

    # Creative content
    headline = Column(
        String(100),
        nullable=False,
        doc="Advertisement headline"
    )

    description_text = Column(
        String(500),
        nullable=True,
        doc="Advertisement description text"
    )

    call_to_action = Column(
        String(50),
        nullable=False,
        doc="Call to action text"
    )

    # Media assets
    image_url = Column(
        String(500),
        nullable=True,
        doc="Primary image URL"
    )

    video_url = Column(
        String(500),
        nullable=True,
        doc="Video content URL"
    )

    thumbnail_url = Column(
        String(500),
        nullable=True,
        doc="Video thumbnail URL"
    )

    media_assets = Column(
        JSON,
        nullable=True,
        doc="Additional media assets configuration"
    )

    # Targeting and placement
    target_url = Column(
        String(500),
        nullable=False,
        doc="Landing page URL"
    )

    placement_preferences = Column(
        JSON,
        nullable=True,
        doc="Preferred placement configuration"
    )

    targeting_override = Column(
        JSON,
        nullable=True,
        doc="Ad-specific targeting overrides"
    )

    # Performance tracking
    impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total ad impressions"
    )

    clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total ad clicks"
    )

    conversions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total ad conversions"
    )

    spend_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Amount spent on this ad"
    )

    # A/B testing
    variant_group = Column(
        String(100),
        nullable=True,
        index=True,
        doc="A/B test variant group identifier"
    )

    variant_name = Column(
        String(100),
        nullable=True,
        doc="A/B test variant name"
    )

    test_percentage = Column(
        Numeric(5, 2),
        nullable=True,
        doc="Percentage of traffic for this variant"
    )

    # Approval and review
    approved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Advertisement approval timestamp"
    )

    approved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="ID of admin who approved advertisement"
    )

    rejection_reason = Column(
        Text,
        nullable=True,
        doc="Reason for advertisement rejection"
    )

    # Scheduling
    start_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Advertisement start date"
    )

    end_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Advertisement end date"
    )

    # Relationships
    campaign = relationship(
        "Campaign",
        back_populates="advertisements",
        foreign_keys=[campaign_id],
        doc="Associated campaign"
    )

    promotional_listings = relationship(
        "PromotionalListing",
        back_populates="advertisement",
        cascade="all, delete-orphan",
        doc="Promotional placement listings"
    )

    created_by_user = relationship(
        "User",
        foreign_keys="Advertisement.created_by",
        back_populates="created_advertisements",
        doc="User who created this advertisement"
    )

    approved_by_user = relationship(
        "User",
        foreign_keys="Advertisement.approved_by",
        back_populates="approved_advertisements",
        doc="User who approved this advertisement"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "LENGTH(headline) >= 10",
            name="check_ad_headline_min_length"
        ),
        CheckConstraint(
            "LENGTH(headline) <= 100",
            name="check_ad_headline_max_length"
        ),
        CheckConstraint(
            "description_text IS NULL OR LENGTH(description_text) <= 500",
            name="check_ad_description_max_length"
        ),
        CheckConstraint(
            "LENGTH(call_to_action) >= 3",
            name="check_ad_cta_min_length"
        ),
        CheckConstraint(
            "impressions >= 0",
            name="check_ad_impressions_positive"
        ),
        CheckConstraint(
            "clicks >= 0",
            name="check_ad_clicks_positive"
        ),
        CheckConstraint(
            "conversions >= 0",
            name="check_ad_conversions_positive"
        ),
        CheckConstraint(
            "spend_amount >= 0.00",
            name="check_ad_spend_positive"
        ),
        CheckConstraint(
            "test_percentage IS NULL OR (test_percentage >= 0.00 AND test_percentage <= 100.00)",
            name="check_ad_test_percentage_range"
        ),
        CheckConstraint(
            "end_date IS NULL OR start_date IS NULL OR end_date >= start_date",
            name="check_ad_date_order"
        ),

        # Performance indexes for <200ms query targets
        Index("idx_ad_campaign_status", "campaign_id", "status"),
        Index("idx_ad_format_status", "ad_format", "status"),
        Index("idx_ad_dates", "start_date", "end_date"),
        Index("idx_ad_performance", "impressions", "clicks", "conversions"),
        Index("idx_ad_variant", "variant_group", "variant_name"),
        Index("idx_ad_approval", "approved_at", "approved_by"),
        Index("idx_ad_active_dates", "status", "start_date", "end_date"),
    )

    def __repr__(self) -> str:
        """String representation of the advertisement."""
        return f"<Advertisement(id={self.id}, name='{self.name}', campaign_id={self.campaign_id}, status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if advertisement is currently active."""
        return self.status == AdStatus.ACTIVE

    @property
    def click_through_rate(self) -> float:
        """Calculate click-through rate."""
        if self.impressions == 0:
            return 0.0
        return float((self.clicks / self.impressions) * 100)

    @property
    def conversion_rate(self) -> float:
        """Calculate conversion rate."""
        if self.clicks == 0:
            return 0.0
        return float((self.conversions / self.clicks) * 100)

    @property
    def cost_per_click(self) -> float:
        """Calculate cost per click."""
        if self.clicks == 0:
            return 0.0
        return float(self.spend_amount / self.clicks)

    @property
    def cost_per_conversion(self) -> float:
        """Calculate cost per conversion."""
        if self.conversions == 0:
            return 0.0
        return float(self.spend_amount / self.conversions)


class CampaignMetrics(BaseModelWithAudit):
    """
    Campaign metrics model for performance tracking and analytics.

    This model stores detailed performance metrics and analytics data for campaigns
    with time-series tracking and optimization insights.

    Features:
    - Time-series performance tracking with <200ms query targets
    - Comprehensive analytics and KPI calculation
    - Optimization insights and recommendations
    - Integration with campaign and advertisement data
    - Real-time metrics aggregation support
    """

    __tablename__ = "campaign_metrics"

    # Core metrics information
    campaign_id = Column(
        Integer,
        ForeignKey("campaigns.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated campaign ID"
    )

    date = Column(
        Date,
        nullable=False,
        index=True,
        doc="Metrics date"
    )

    hour = Column(
        Integer,
        nullable=True,
        doc="Hour of day (0-23) for hourly metrics"
    )

    # Performance metrics
    impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Impressions for the period"
    )

    clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Clicks for the period"
    )

    conversions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Conversions for the period"
    )

    spend_amount = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Spend amount for the period"
    )

    revenue_generated = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Revenue generated for the period"
    )

    # Calculated metrics
    click_through_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Click-through rate percentage"
    )

    conversion_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Conversion rate percentage"
    )

    cost_per_click = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Cost per click"
    )

    cost_per_acquisition = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Cost per acquisition"
    )

    return_on_ad_spend = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Return on ad spend ratio"
    )

    # Quality metrics
    quality_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Campaign quality score (0-100)"
    )

    relevance_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Ad relevance score (0-100)"
    )

    landing_page_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Landing page experience score (0-100)"
    )

    # Engagement metrics
    unique_clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Unique clicks for the period"
    )

    bounce_rate = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Bounce rate percentage"
    )

    average_session_duration = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average session duration in seconds"
    )

    pages_per_session = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average pages per session"
    )

    # Device and platform breakdown
    desktop_impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Desktop impressions"
    )

    mobile_impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Mobile impressions"
    )

    tablet_impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Tablet impressions"
    )

    # Geographic performance
    top_performing_locations = Column(
        JSON,
        nullable=True,
        doc="Top performing geographic locations"
    )

    # Optimization insights
    optimization_score = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Campaign optimization score (0-100)"
    )

    recommendations = Column(
        JSON,
        nullable=True,
        doc="Optimization recommendations"
    )

    # Relationships
    campaign = relationship(
        "Campaign",
        back_populates="metrics",
        foreign_keys=[campaign_id],
        doc="Associated campaign"
    )

    # Constraints and indexes
    __table_args__ = (
        # Unique constraint for daily metrics
        UniqueConstraint("campaign_id", "date", "hour", name="uq_campaign_metrics_period"),

        # Check constraints
        CheckConstraint(
            "hour IS NULL OR (hour >= 0 AND hour <= 23)",
            name="check_metrics_hour_range"
        ),
        CheckConstraint(
            "impressions >= 0",
            name="check_metrics_impressions_positive"
        ),
        CheckConstraint(
            "clicks >= 0",
            name="check_metrics_clicks_positive"
        ),
        CheckConstraint(
            "conversions >= 0",
            name="check_metrics_conversions_positive"
        ),
        CheckConstraint(
            "spend_amount >= 0.00",
            name="check_metrics_spend_positive"
        ),
        CheckConstraint(
            "revenue_generated >= 0.00",
            name="check_metrics_revenue_positive"
        ),
        CheckConstraint(
            "click_through_rate >= 0.00 AND click_through_rate <= 100.00",
            name="check_metrics_ctr_range"
        ),
        CheckConstraint(
            "conversion_rate >= 0.00 AND conversion_rate <= 100.00",
            name="check_metrics_cvr_range"
        ),
        CheckConstraint(
            "quality_score >= 0.00 AND quality_score <= 100.00",
            name="check_metrics_quality_score_range"
        ),
        CheckConstraint(
            "bounce_rate >= 0.00 AND bounce_rate <= 100.00",
            name="check_metrics_bounce_rate_range"
        ),

        # Performance indexes for <200ms query targets
        Index("idx_metrics_campaign_date", "campaign_id", "date"),
        Index("idx_campaign_metrics_date_hour", "date", "hour"),
        Index("idx_campaign_metrics_performance", "impressions", "clicks", "conversions"),
        Index("idx_metrics_spend", "spend_amount", "revenue_generated"),
        Index("idx_metrics_quality", "quality_score", "optimization_score"),
        Index("idx_metrics_device", "desktop_impressions", "mobile_impressions"),
    )

    def __repr__(self) -> str:
        """String representation of the campaign metrics."""
        return f"<CampaignMetrics(id={self.id}, campaign_id={self.campaign_id}, date='{self.date}')>"


class PromotionalListing(BaseModelWithAudit):
    """
    Promotional listing model for featured placement management.

    This model manages featured placements and promotional listings across
    the marketplace with priority-based positioning and performance tracking.

    Features:
    - Featured placement management with priority positioning
    - Performance tracking with <200ms query targets
    - Integration with advertisement and campaign systems
    - Marketplace optimization and revenue generation
    - Real-time placement availability management
    """

    __tablename__ = "promotional_listings"

    # Core listing information
    advertisement_id = Column(
        Integer,
        ForeignKey("advertisements.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated advertisement ID (optional)"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated service ID (optional)"
    )

    title = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Promotional listing title"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Promotional listing description"
    )

    # Placement configuration
    placement_type = Column(
        Enum(PlacementType),
        nullable=False,
        index=True,
        doc="Type of placement"
    )

    placement_status = Column(
        Enum(PlacementStatus),
        nullable=False,
        default=PlacementStatus.AVAILABLE,
        index=True,
        doc="Current placement status"
    )

    priority_level = Column(
        Integer,
        nullable=False,
        default=1,
        index=True,
        doc="Placement priority level (1-10, higher = more prominent)"
    )

    position_index = Column(
        Integer,
        nullable=True,
        doc="Specific position index within placement type"
    )

    # Pricing and billing
    base_price = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Base price for placement in NGN"
    )

    premium_multiplier = Column(
        Numeric(5, 2),
        nullable=False,
        default=Decimal("1.00"),
        doc="Premium pricing multiplier"
    )

    final_price = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Final calculated price"
    )

    billing_cycle = Column(
        String(20),
        nullable=False,
        default="daily",
        doc="Billing cycle (daily, weekly, monthly)"
    )

    # Scheduling
    start_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Placement start date"
    )

    end_date = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Placement end date"
    )

    timezone = Column(
        String(50),
        nullable=False,
        default="Africa/Lagos",
        doc="Placement timezone"
    )

    # Performance tracking
    impressions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total placement impressions"
    )

    clicks = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total placement clicks"
    )

    conversions = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total placement conversions"
    )

    revenue_generated = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Revenue generated from placement"
    )

    # Targeting and optimization
    targeting_config = Column(
        JSON,
        nullable=True,
        doc="Placement-specific targeting configuration"
    )

    optimization_settings = Column(
        JSON,
        nullable=True,
        doc="Placement optimization settings"
    )

    # Approval and review
    approved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Placement approval timestamp"
    )

    approved_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="ID of admin who approved placement"
    )

    # Relationships
    advertisement = relationship(
        "Advertisement",
        back_populates="promotional_listings",
        foreign_keys=[advertisement_id],
        doc="Associated advertisement"
    )

    vendor = relationship(
        "Vendor",
        back_populates="promotional_listings",
        foreign_keys=[vendor_id],
        doc="Associated vendor"
    )

    service = relationship(
        "Service",
        back_populates="promotional_listings",
        foreign_keys=[service_id],
        doc="Associated service"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "priority_level >= 1 AND priority_level <= 10",
            name="check_listing_priority_range"
        ),
        CheckConstraint(
            "base_price >= 100.00",
            name="check_listing_min_price"
        ),
        CheckConstraint(
            "premium_multiplier >= 0.50 AND premium_multiplier <= 10.00",
            name="check_listing_multiplier_range"
        ),
        CheckConstraint(
            "final_price >= 50.00",
            name="check_listing_min_final_price"
        ),
        CheckConstraint(
            "end_date > start_date",
            name="check_listing_date_order"
        ),
        CheckConstraint(
            "impressions >= 0",
            name="check_listing_impressions_positive"
        ),
        CheckConstraint(
            "clicks >= 0",
            name="check_listing_clicks_positive"
        ),
        CheckConstraint(
            "conversions >= 0",
            name="check_listing_conversions_positive"
        ),
        CheckConstraint(
            "revenue_generated >= 0.00",
            name="check_listing_revenue_positive"
        ),
        CheckConstraint(
            "billing_cycle IN ('daily', 'weekly', 'monthly')",
            name="check_listing_billing_cycle"
        ),

        # Performance indexes for <200ms query targets
        Index("idx_listing_vendor_status", "vendor_id", "placement_status"),
        Index("idx_listing_placement_priority", "placement_type", "priority_level"),
        Index("idx_listing_dates", "start_date", "end_date"),
        Index("idx_listing_performance", "impressions", "clicks", "conversions"),
        Index("idx_listing_pricing", "base_price", "final_price"),
        Index("idx_listing_active", "placement_status", "start_date", "end_date"),
        Index("idx_listing_position", "placement_type", "position_index"),
    )

    def __repr__(self) -> str:
        """String representation of the promotional listing."""
        return f"<PromotionalListing(id={self.id}, title='{self.title}', vendor_id={self.vendor_id}, placement='{self.placement_type}')>"

    @property
    def is_active(self) -> bool:
        """Check if placement is currently active."""
        return self.placement_status == PlacementStatus.OCCUPIED

    @property
    def click_through_rate(self) -> float:
        """Calculate click-through rate."""
        if self.impressions == 0:
            return 0.0
        return float((self.clicks / self.impressions) * 100)

    @property
    def conversion_rate(self) -> float:
        """Calculate conversion rate."""
        if self.clicks == 0:
            return 0.0
        return float((self.conversions / self.clicks) * 100)

    @property
    def revenue_per_impression(self) -> float:
        """Calculate revenue per impression."""
        if self.impressions == 0:
            return 0.0
        return float(self.revenue_generated / self.impressions)


class AdSpend(BaseModelWithAudit):
    """
    Ad spend model for financial tracking and billing integration.

    This model tracks detailed spending records for campaigns and advertisements
    with multi-provider payment integration and comprehensive audit trails.

    Features:
    - Financial tracking with multi-provider payment integration
    - Comprehensive audit logging and billing reconciliation
    - Performance tracking with <200ms query targets
    - Integration with existing payment infrastructure
    - Real-time spend monitoring and budget management
    """

    __tablename__ = "ad_spends"

    # Core spend information
    campaign_id = Column(
        Integer,
        ForeignKey("campaigns.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated campaign ID"
    )

    advertisement_id = Column(
        Integer,
        ForeignKey("advertisements.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Associated advertisement ID (optional)"
    )

    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    # Spend details
    spend_category = Column(
        Enum(SpendCategory),
        nullable=False,
        index=True,
        doc="Category of spend"
    )

    amount = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Spend amount in NGN"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        doc="Currency code"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Spend description"
    )

    # Payment integration
    payment_provider = Column(
        Enum(PaymentProviderType),
        nullable=False,
        index=True,
        doc="Payment provider used"
    )

    payment_reference = Column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        doc="Payment reference from provider"
    )

    payment_status = Column(
        String(50),
        nullable=False,
        default="pending",
        index=True,
        doc="Payment status"
    )

    # TODO-PAYSTACK-API-URL: Paystack payment tracking for ad spend
    # TODO-STRIPE-API-URL: Stripe payment tracking for international ad spend
    # TODO-BUSHA-API-URL: Busha cryptocurrency payment tracking for ad spend

    # Billing period
    billing_period_start = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Billing period start"
    )

    billing_period_end = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Billing period end"
    )

    # Performance attribution
    impressions_attributed = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Impressions attributed to this spend"
    )

    clicks_attributed = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Clicks attributed to this spend"
    )

    conversions_attributed = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Conversions attributed to this spend"
    )

    revenue_attributed = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Revenue attributed to this spend"
    )

    # Tax and fees
    platform_fee = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform fee amount"
    )

    tax_amount = Column(
        Numeric(8, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Tax amount"
    )

    total_amount = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Total amount including fees and taxes"
    )

    # Processing information
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Processing timestamp"
    )

    processed_by = Column(
        Integer,
        nullable=True,
        doc="ID of user who processed the spend"
    )

    # Reconciliation
    reconciled = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Whether spend has been reconciled"
    )

    reconciled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Reconciliation timestamp"
    )

    reconciliation_reference = Column(
        String(255),
        nullable=True,
        doc="Reconciliation reference"
    )

    # Relationships
    campaign = relationship(
        "Campaign",
        back_populates="ad_spends",
        foreign_keys=[campaign_id],
        doc="Associated campaign"
    )

    advertisement = relationship(
        "Advertisement",
        foreign_keys=[advertisement_id],
        doc="Associated advertisement"
    )

    vendor = relationship(
        "Vendor",
        back_populates="ad_spends",
        foreign_keys=[vendor_id],
        doc="Associated vendor"
    )

    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "amount >= 0.00",
            name="check_spend_amount_positive"
        ),
        CheckConstraint(
            "platform_fee >= 0.00",
            name="check_spend_platform_fee_positive"
        ),
        CheckConstraint(
            "tax_amount >= 0.00",
            name="check_spend_tax_positive"
        ),
        CheckConstraint(
            "total_amount >= amount",
            name="check_spend_total_amount_valid"
        ),
        CheckConstraint(
            "billing_period_end > billing_period_start",
            name="check_spend_billing_period_order"
        ),
        CheckConstraint(
            "impressions_attributed >= 0",
            name="check_spend_impressions_positive"
        ),
        CheckConstraint(
            "clicks_attributed >= 0",
            name="check_spend_clicks_positive"
        ),
        CheckConstraint(
            "conversions_attributed >= 0",
            name="check_spend_conversions_positive"
        ),
        CheckConstraint(
            "revenue_attributed >= 0.00",
            name="check_spend_revenue_positive"
        ),
        CheckConstraint(
            "currency IN ('NGN', 'USD', 'EUR', 'GBP', 'BTC', 'ETH', 'USDT', 'USDC')",
            name="check_spend_currency_valid"
        ),

        # Performance indexes for <200ms query targets
        Index("idx_spend_campaign_status", "campaign_id", "payment_status"),
        Index("idx_spend_vendor_period", "vendor_id", "billing_period_start", "billing_period_end"),
        Index("idx_spend_provider_status", "payment_provider", "payment_status"),
        Index("idx_spend_category_amount", "spend_category", "amount"),
        Index("idx_spend_reconciliation", "reconciled", "reconciled_at"),
        Index("idx_spend_processing", "processed_at", "processed_by"),
        Index("idx_spend_reference", "payment_reference"),
        Index("idx_spend_performance", "impressions_attributed", "clicks_attributed", "conversions_attributed"),
    )

    def __repr__(self) -> str:
        """String representation of the ad spend."""
        return f"<AdSpend(id={self.id}, campaign_id={self.campaign_id}, amount={self.amount}, category='{self.spend_category}')>"

    @property
    def is_processed(self) -> bool:
        """Check if spend has been processed."""
        return self.payment_status == "completed" and self.processed_at is not None

    @property
    def cost_per_impression(self) -> float:
        """Calculate cost per impression."""
        if self.impressions_attributed == 0:
            return 0.0
        return float(self.amount / self.impressions_attributed)

    @property
    def cost_per_click(self) -> float:
        """Calculate cost per click."""
        if self.clicks_attributed == 0:
            return 0.0
        return float(self.amount / self.clicks_attributed)

    @property
    def cost_per_conversion(self) -> float:
        """Calculate cost per conversion."""
        if self.conversions_attributed == 0:
            return 0.0
        return float(self.amount / self.conversions_attributed)

    @property
    def return_on_ad_spend(self) -> float:
        """Calculate return on ad spend."""
        if self.amount == 0:
            return 0.0
        return float(self.revenue_attributed / self.amount)
