#!/bin/bash

# Culture Connect Backend - Staging Deployment Script
# This script handles the complete deployment process for the staging environment
# using Docker Swarm for production-like testing and validation.

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-staging}"
VERSION="${VERSION:-staging-latest}"
COMPOSE_FILE="$PROJECT_ROOT/docker/staging/docker-compose.swarm.yml"
ENV_FILE="$PROJECT_ROOT/environments/staging/.env.staging"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

error_exit() {
    log_error "$1"
    exit 1
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Cleanup function
cleanup() {
    if [[ $? -ne 0 ]]; then
        log_error "Staging deployment failed. Check logs for details."
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Validate environment
validate_environment() {
    log_info "Validating staging environment setup"
    
    # Check required files
    local required_files=(
        "$COMPOSE_FILE"
        "$ENV_FILE"
        "$PROJECT_ROOT/Dockerfile"
        "$PROJECT_ROOT/requirements.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error_exit "Required file not found: $file"
        fi
    done
    
    # Check Docker and Docker Swarm
    if ! command -v docker &> /dev/null; then
        error_exit "Docker is not installed or not in PATH"
    fi
    
    # Check if Docker Swarm is initialized
    if ! docker info | grep -q "Swarm: active"; then
        log_warning "Docker Swarm is not initialized. Initializing..."
        docker swarm init
        if [[ $? -eq 0 ]]; then
            log_success "Docker Swarm initialized successfully"
        else
            error_exit "Failed to initialize Docker Swarm"
        fi
    fi
    
    log_success "Environment validation passed"
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks for staging"
    
    cd "$PROJECT_ROOT"
    
    # Check if stack is already deployed
    if docker stack ls | grep -q "culture-connect-staging"; then
        log_warning "Stack is already deployed. Updating..."
    fi
    
    # Validate Docker Compose file
    docker-compose -f "$COMPOSE_FILE" config > /dev/null
    if [[ $? -eq 0 ]]; then
        log_success "Docker Compose file validation passed"
    else
        error_exit "Docker Compose file validation failed"
    fi
    
    log_success "Pre-deployment checks completed"
}

# Build and push images
build_images() {
    log_info "Building Docker images for staging"
    
    cd "$PROJECT_ROOT"
    
    # Build images
    docker build -t culture-connect-api:staging-latest .
    
    if [[ $? -eq 0 ]]; then
        log_success "Docker images built successfully"
    else
        error_exit "Failed to build Docker images"
    fi
    
    # Tag images for registry (if using external registry)
    # docker tag culture-connect-api:staging-latest registry.example.com/culture-connect-api:staging-latest
    # docker push registry.example.com/culture-connect-api:staging-latest
}

# Deploy stack
deploy_stack() {
    log_info "Deploying staging stack with Docker Swarm"
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables
    export CC_VERSION="$VERSION"
    export CC_ENVIRONMENT="$DEPLOYMENT_ENV"
    
    # Deploy stack
    docker stack deploy -c "$COMPOSE_FILE" culture-connect-staging
    
    if [[ $? -eq 0 ]]; then
        log_success "Stack deployed successfully"
    else
        error_exit "Stack deployment failed"
    fi
}

# Wait for services
wait_for_services() {
    log_info "Waiting for services to be ready"
    
    local max_attempts=60
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Checking services... attempt $attempt/$max_attempts"
        
        # Check if all services are running
        local running_services=$(docker stack services culture-connect-staging --format "{{.Replicas}}" | grep -c "1/1")
        local total_services=$(docker stack services culture-connect-staging --format "{{.Name}}" | wc -l)
        
        if [[ $running_services -eq $total_services ]]; then
            log_success "All services are running"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "Services failed to start after $max_attempts attempts"
        fi
        
        sleep 10
        ((attempt++))
    done
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations"
    
    # Get the API service container ID
    local api_container=$(docker ps --filter "name=culture-connect-staging_api" --format "{{.ID}}" | head -n1)
    
    if [[ -z "$api_container" ]]; then
        error_exit "API container not found"
    fi
    
    # Run migrations
    docker exec "$api_container" alembic upgrade head
    
    if [[ $? -eq 0 ]]; then
        log_success "Database migrations completed"
    else
        error_exit "Database migrations failed"
    fi
}

# Health checks
health_checks() {
    log_info "Performing health checks"
    
    local max_attempts=30
    local attempt=1
    
    # Get the published port for the API service
    local api_port=$(docker stack services culture-connect-staging --filter "name=culture-connect-staging_api" --format "{{.Ports}}" | cut -d':' -f2 | cut -d'-' -f1)
    
    if [[ -z "$api_port" ]]; then
        api_port="8000"  # Default port
    fi
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts"
        
        # Check API health
        if curl -f -s "http://localhost:$api_port/health" > /dev/null; then
            log_success "API health check passed"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "Health checks failed after $max_attempts attempts"
        fi
        
        sleep 10
        ((attempt++))
    done
}

# Staging-specific setup
staging_setup() {
    log_info "Setting up staging environment"
    
    # Create staging directories on host (if needed)
    sudo mkdir -p /opt/culture-connect-staging/{data,logs,backups,uploads}
    sudo chown -R 1001:1001 /opt/culture-connect-staging/
    
    # Set up log rotation
    if [[ ! -f /etc/logrotate.d/culture-connect-staging ]]; then
        sudo tee /etc/logrotate.d/culture-connect-staging > /dev/null <<EOF
/opt/culture-connect-staging/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 1001 1001
    postrotate
        docker kill -s USR1 \$(docker ps -q --filter "name=culture-connect-staging_api") 2>/dev/null || true
    endscript
}
EOF
        log_success "Log rotation configured"
    fi
    
    log_success "Staging environment setup completed"
}

# Show deployment information
show_deployment_info() {
    log_success "Staging deployment completed successfully!"
    echo
    log_info "=== Deployment Information ==="
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    echo
    log_info "=== Service Information ==="
    docker stack services culture-connect-staging
    echo
    log_info "=== Service URLs ==="
    log_info "API: http://localhost:8000"
    log_info "API Documentation: http://localhost:8000/docs"
    echo
    log_info "=== Useful Commands ==="
    log_info "View services: docker stack services culture-connect-staging"
    log_info "View logs: docker service logs culture-connect-staging_api"
    log_info "Scale service: docker service scale culture-connect-staging_api=3"
    log_info "Remove stack: docker stack rm culture-connect-staging"
    echo
    log_info "=== Monitoring ==="
    log_info "Service status: docker stack ps culture-connect-staging"
    log_info "Service logs: docker service logs -f culture-connect-staging_api"
}

# Main deployment function
main() {
    log_info "Starting Culture Connect Backend staging deployment"
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    
    validate_environment
    pre_deployment_checks
    build_images
    deploy_stack
    wait_for_services
    run_migrations
    staging_setup
    health_checks
    show_deployment_info
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
