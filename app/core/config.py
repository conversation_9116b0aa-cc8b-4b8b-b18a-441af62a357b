"""
Core configuration settings for Culture Connect Backend API.

This module provides centralized configuration management using Pydantic settings
with environment variable support for different deployment environments.
"""

import os
import re
from typing import Optional, List, Any, Dict, Union
from pydantic import Field, field_validator, AnyHttpUrl
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """
    Application settings with environment variable support.

    All settings can be overridden via environment variables with the prefix 'CC_'
    (Culture Connect). For example: CC_DATABASE_URL, CC_SECRET_KEY, etc.
    """

    # Application Settings
    APP_NAME: str = "Culture Connect Backend API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = Field(default="development", env="CC_ENVIRONMENT")

    # Security Settings
    SECRET_KEY: str = Field(..., env="CC_SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="CC_ACCESS_TOKEN_EXPIRE_MINUTES")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="CC_REFRESH_TOKEN_EXPIRE_DAYS")
    ALGORITHM: str = "HS256"

    # Database Settings
    DATABASE_URL: str = Field(..., env="CC_DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=10, env="CC_DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, env="CC_DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="CC_DATABASE_POOL_TIMEOUT")

    # Redis Settings
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="CC_REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="CC_REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="CC_REDIS_DB")

    # Celery Settings
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CC_CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/1", env="CC_CELERY_RESULT_BACKEND")

    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="CC_BACKEND_CORS_ORIGINS"
    )

    # Payment Provider Settings
    # TODO-PAYMENT-PROVIDER-CONFIG: Centralized payment provider configuration management
    # See app/core/payment/config.py for comprehensive payment provider configurations

    # Paystack (Africa) - TODO-PAYSTACK-CONFIG: Production Paystack configuration
    PAYSTACK_SECRET_KEY: str = Field(..., env="CC_PAYSTACK_SECRET_KEY")
    PAYSTACK_PUBLIC_KEY: str = Field(..., env="CC_PAYSTACK_PUBLIC_KEY")
    PAYSTACK_WEBHOOK_SECRET: str = Field(..., env="CC_PAYSTACK_WEBHOOK_SECRET")

    # Stripe (Diaspora) - TODO-STRIPE-CONFIG: Production Stripe configuration
    STRIPE_SECRET_KEY: str = Field(..., env="CC_STRIPE_SECRET_KEY")
    STRIPE_PUBLIC_KEY: str = Field(..., env="CC_STRIPE_PUBLIC_KEY")
    STRIPE_WEBHOOK_SECRET: str = Field(..., env="CC_STRIPE_WEBHOOK_SECRET")

    # Cryptocurrency Settings (Busha.co) - TODO-BUSHA-CONFIG: Production Busha configuration
    BUSHA_API_KEY: str = Field(..., env="CC_BUSHA_API_KEY")
    BUSHA_SECRET_KEY: str = Field(..., env="CC_BUSHA_SECRET_KEY")
    BUSHA_WEBHOOK_SECRET: str = Field(..., env="CC_BUSHA_WEBHOOK_SECRET")

    # AI/ML API Settings
    AIML_API_KEY: str = Field(..., env="CC_AIML_API_KEY")
    AIML_BASE_URL: str = Field(default="https://api.aimlapi.com/v1", env="CC_AIML_BASE_URL")

    # Geo-location Settings for Payment Routing (Enhanced Phase 1)
    GEOIP_DATABASE_PATH: str = Field(default="./data/GeoLite2-Country.mmdb", env="CC_GEOIP_DATABASE_PATH")
    ENABLE_GEOLOCATION_ROUTING: bool = Field(default=True, env="CC_ENABLE_GEOLOCATION_ROUTING")
    GEOLOCATION_CACHE_TTL_HOURS: int = Field(default=1, env="CC_GEOLOCATION_CACHE_TTL_HOURS")
    GEOLOCATION_FALLBACK_COUNTRY: str = Field(default="NG", env="CC_GEOLOCATION_FALLBACK_COUNTRY")

    # MaxMind Configuration
    MAXMIND_LICENSE_KEY: Optional[str] = Field(default=None, env="MAXMIND_LICENSE_KEY")
    GEOIP_AUTO_UPDATE_ENABLED: bool = Field(default=False, env="CC_GEOIP_AUTO_UPDATE_ENABLED")
    GEOIP_UPDATE_WEBHOOK_URL: Optional[str] = Field(default=None, env="CC_GEOIP_UPDATE_WEBHOOK_URL")

    # Performance Configuration
    GEOLOCATION_TIMEOUT_MS: int = Field(default=5000, env="CC_GEOLOCATION_TIMEOUT_MS")
    GEOLOCATION_MAX_RETRIES: int = Field(default=3, env="CC_GEOLOCATION_MAX_RETRIES")
    GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD: int = Field(default=10, env="CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD")
    GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT: int = Field(default=60, env="CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT")

    # Cache Configuration
    GEOLOCATION_CACHE_MAX_SIZE: int = Field(default=10000, env="CC_GEOLOCATION_CACHE_MAX_SIZE")
    GEOLOCATION_CACHE_COMPRESSION: bool = Field(default=True, env="CC_GEOLOCATION_CACHE_COMPRESSION")

    # Redis Geolocation Caching
    GEOLOCATION_REDIS_PREFIX: str = Field(default="geo:", env="CC_GEOLOCATION_REDIS_PREFIX")
    GEOLOCATION_REDIS_TTL: int = Field(default=86400, env="CC_GEOLOCATION_REDIS_TTL")  # 24 hours
    GEOLOCATION_REDIS_COMPRESSION: bool = Field(default=True, env="CC_GEOLOCATION_REDIS_COMPRESSION")

    # Monitoring and Metrics
    GEOLOCATION_METRICS_ENABLED: bool = Field(default=True, env="CC_GEOLOCATION_METRICS_ENABLED")
    GEOLOCATION_DETAILED_LOGGING: bool = Field(default=False, env="CC_GEOLOCATION_DETAILED_LOGGING")
    GEOLOCATION_PERFORMANCE_TRACKING: bool = Field(default=True, env="CC_GEOLOCATION_PERFORMANCE_TRACKING")
    GEOLOCATION_HEALTH_CHECK_ENABLED: bool = Field(default=True, env="CC_GEOLOCATION_HEALTH_CHECK_ENABLED")

    # VPN/Proxy Detection (Phase 2 preparation)
    VPN_DETECTION_ENABLED: bool = Field(default=False, env="CC_VPN_DETECTION_ENABLED")
    VPN_DETECTION_API_KEY: Optional[str] = Field(default=None, env="CC_VPN_DETECTION_API_KEY")
    VPN_DETECTION_CONFIDENCE_THRESHOLD: float = Field(default=0.7, env="CC_VPN_DETECTION_CONFIDENCE_THRESHOLD")

    # A/B Testing Framework (Phase 2 preparation)
    AB_TESTING_ENABLED: bool = Field(default=False, env="CC_AB_TESTING_ENABLED")
    AB_TESTING_DEFAULT_SPLIT: float = Field(default=0.5, env="CC_AB_TESTING_DEFAULT_SPLIT")
    AB_TESTING_MIN_SAMPLE_SIZE: int = Field(default=1000, env="CC_AB_TESTING_MIN_SAMPLE_SIZE")

    # File Storage Settings
    UPLOAD_DIR: str = Field(default="./uploads", env="CC_UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, env="CC_MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/webp", "application/pdf"],
        env="CC_ALLOWED_FILE_TYPES"
    )

    # AWS S3 Settings (for production file storage)
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, env="CC_AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, env="CC_AWS_SECRET_ACCESS_KEY")
    AWS_S3_BUCKET: Optional[str] = Field(default=None, env="CC_AWS_S3_BUCKET")
    AWS_S3_REGION: str = Field(default="us-east-1", env="CC_AWS_S3_REGION")

    # Email Settings
    SMTP_HOST: str = Field(default="localhost", env="CC_SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="CC_SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="CC_SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="CC_SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="CC_SMTP_USE_TLS")
    FROM_EMAIL: str = Field(default="<EMAIL>", env="CC_FROM_EMAIL")
    FROM_NAME: str = Field(default="Culture Connect", env="CC_FROM_NAME")

    # Rate Limiting Settings
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="CC_RATE_LIMIT_PER_MINUTE")
    RATE_LIMIT_BURST: int = Field(default=100, env="CC_RATE_LIMIT_BURST")

    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", env="CC_LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="CC_LOG_FORMAT"
    )
    LOG_FILE: Optional[str] = Field(default=None, env="CC_LOG_FILE")
    LOG_ROTATION: str = Field(default="1 day", env="CC_LOG_ROTATION")
    LOG_RETENTION: str = Field(default="30 days", env="CC_LOG_RETENTION")

    # Monitoring Settings
    SENTRY_DSN: Optional[str] = Field(default=None, env="CC_SENTRY_DSN")
    SENTRY_ENVIRONMENT: Optional[str] = Field(default=None, env="CC_SENTRY_ENVIRONMENT")
    SENTRY_TRACES_SAMPLE_RATE: float = Field(default=0.1, env="CC_SENTRY_TRACES_SAMPLE_RATE")

    # Performance Settings
    REQUEST_TIMEOUT: int = Field(default=30, env="CC_REQUEST_TIMEOUT")
    MAX_CONNECTIONS: int = Field(default=100, env="CC_MAX_CONNECTIONS")
    KEEPALIVE_TIMEOUT: int = Field(default=5, env="CC_KEEPALIVE_TIMEOUT")

    # Security Settings (Enhanced)
    BCRYPT_ROUNDS: int = Field(default=12, env="CC_BCRYPT_ROUNDS")
    SESSION_TIMEOUT: int = Field(default=3600, env="CC_SESSION_TIMEOUT")  # 1 hour
    MAX_LOGIN_ATTEMPTS: int = Field(default=5, env="CC_MAX_LOGIN_ATTEMPTS")
    LOCKOUT_DURATION: int = Field(default=900, env="CC_LOCKOUT_DURATION")  # 15 minutes

    # API Settings
    API_TITLE: str = Field(default="Culture Connect Backend API", env="CC_API_TITLE")
    API_DESCRIPTION: str = Field(
        default="Culture Connect Backend API - Connecting cultures through authentic experiences",
        env="CC_API_DESCRIPTION"
    )
    API_VERSION: str = Field(default="1.0.0", env="CC_API_VERSION")
    DOCS_URL: Optional[str] = Field(default="/docs", env="CC_DOCS_URL")
    REDOC_URL: Optional[str] = Field(default="/redoc", env="CC_REDOC_URL")
    OPENAPI_URL: Optional[str] = Field(default="/openapi.json", env="CC_OPENAPI_URL")

    # Frontend Settings
    FRONTEND_URL: str = Field(default="http://localhost:3000", env="CC_FRONTEND_URL")
    PWA_URL: str = Field(default="http://localhost:3001", env="CC_PWA_URL")
    MOBILE_APP_DEEP_LINK: str = Field(default="cultureconnect://", env="CC_MOBILE_APP_DEEP_LINK")

    # Feature Flags
    ENABLE_PROMOTIONAL_SYSTEM: bool = Field(default=True, env="CC_ENABLE_PROMOTIONAL_SYSTEM")
    ENABLE_CRYPTO_PAYMENTS: bool = Field(default=True, env="CC_ENABLE_CRYPTO_PAYMENTS")
    ENABLE_AI_OPTIMIZATION: bool = Field(default=True, env="CC_ENABLE_AI_OPTIMIZATION")
    ENABLE_CACHING: bool = Field(default=True, env="CC_ENABLE_CACHING")
    ENABLE_RATE_LIMITING: bool = Field(default=True, env="CC_ENABLE_RATE_LIMITING")
    ENABLE_METRICS: bool = Field(default=True, env="CC_ENABLE_METRICS")
    ENABLE_HEALTH_CHECKS: bool = Field(default=True, env="CC_ENABLE_HEALTH_CHECKS")

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: str | List[str]) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @field_validator("ALLOWED_FILE_TYPES", mode="before")
    @classmethod
    def assemble_file_types(cls, v: str | List[str]) -> List[str]:
        """Parse allowed file types from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @field_validator("SECRET_KEY")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Ensure secret key is sufficiently long."""
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v

    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting."""
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"ENVIRONMENT must be one of: {allowed_envs}")
        return v

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level setting."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"LOG_LEVEL must be one of: {allowed_levels}")
        return v.upper()

    @field_validator("SMTP_PORT")
    @classmethod
    def validate_smtp_port(cls, v: int) -> int:
        """Validate SMTP port range."""
        if not (1 <= v <= 65535):
            raise ValueError("SMTP_PORT must be between 1 and 65535")
        return v

    @field_validator("DATABASE_POOL_SIZE", "DATABASE_MAX_OVERFLOW")
    @classmethod
    def validate_positive_int(cls, v: int) -> int:
        """Validate positive integer values."""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v

    @field_validator("SENTRY_TRACES_SAMPLE_RATE")
    @classmethod
    def validate_sample_rate(cls, v: float) -> float:
        """Validate Sentry traces sample rate."""
        if not (0.0 <= v <= 1.0):
            raise ValueError("SENTRY_TRACES_SAMPLE_RATE must be between 0.0 and 1.0")
        return v

    @field_validator("BCRYPT_ROUNDS")
    @classmethod
    def validate_bcrypt_rounds(cls, v: int) -> int:
        """Validate bcrypt rounds for security."""
        if not (4 <= v <= 31):
            raise ValueError("BCRYPT_ROUNDS must be between 4 and 31")
        return v

    @field_validator("FROM_EMAIL")
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email format."""
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError("Invalid email format")
        return v

    @field_validator("FRONTEND_URL", "PWA_URL")
    @classmethod
    def validate_url(cls, v: str) -> str:
        """Validate URL format."""
        import re
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        if not re.match(url_pattern, v):
            raise ValueError("Invalid URL format")
        return v

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT == "development"

    @property
    def is_staging(self) -> bool:
        """Check if running in staging environment."""
        return self.ENVIRONMENT == "staging"

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT == "production"

    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL."""
        # Handle different database drivers
        url = self.DATABASE_URL

        # PostgreSQL async to sync
        if "postgresql+asyncpg://" in url:
            return url.replace("postgresql+asyncpg://", "postgresql://")

        # SQLite async to sync
        if "sqlite+aiosqlite://" in url:
            return url.replace("sqlite+aiosqlite://", "sqlite://")

        # MySQL async to sync
        if "mysql+aiomysql://" in url:
            return url.replace("mysql+aiomysql://", "mysql://")

        # If already sync or unknown, return as-is
        return url

    @property
    def docs_enabled(self) -> bool:
        """Check if API documentation should be enabled."""
        return self.is_development or self.is_staging

    @property
    def debug_enabled(self) -> bool:
        """Check if debug mode should be enabled."""
        return self.is_development

    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list."""
        return self.BACKEND_CORS_ORIGINS

    def get_sentry_config(self) -> Dict[str, Any]:
        """Get Sentry configuration if enabled."""
        if not self.SENTRY_DSN:
            return {}

        return {
            "dsn": self.SENTRY_DSN,
            "environment": self.SENTRY_ENVIRONMENT or self.ENVIRONMENT,
            "traces_sample_rate": self.SENTRY_TRACES_SAMPLE_RATE,
            "debug": self.is_development,
        }

    def get_email_config(self) -> Dict[str, Any]:
        """Get email configuration."""
        return {
            "host": self.SMTP_HOST,
            "port": self.SMTP_PORT,
            "username": self.SMTP_USERNAME,
            "password": self.SMTP_PASSWORD,
            "use_tls": self.SMTP_USE_TLS,
            "from_email": self.FROM_EMAIL,
            "from_name": self.FROM_NAME,
        }

    def validate_required_secrets(self) -> List[str]:
        """Validate that required secrets are set for production."""
        missing_secrets = []

        if self.is_production:
            required_secrets = [
                "SECRET_KEY",
                "DATABASE_URL",
                "PAYSTACK_SECRET_KEY",
                "STRIPE_SECRET_KEY",
                "BUSHA_API_KEY",
                "AIML_API_KEY",
            ]

            for secret in required_secrets:
                value = getattr(self, secret, None)
                if not value or (isinstance(value, str) and value.startswith("your-")):
                    missing_secrets.append(secret)

        return missing_secrets

    model_config = {
        "env_prefix": "CC_",
        "case_sensitive": True,
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.

    This function uses LRU cache to ensure settings are loaded only once
    and reused throughout the application lifecycle.

    Returns:
        Settings: Application configuration settings
    """
    return Settings()


# Global settings instance
settings = get_settings()


# Environment-specific configurations
def get_database_config() -> Dict[str, Any]:
    """Get database configuration for SQLAlchemy."""
    return {
        "url": settings.DATABASE_URL,
        "pool_size": settings.DATABASE_POOL_SIZE,
        "max_overflow": settings.DATABASE_MAX_OVERFLOW,
        "pool_timeout": settings.DATABASE_POOL_TIMEOUT,
        "pool_pre_ping": True,
        "echo": settings.is_development,
    }


def get_redis_config() -> Dict[str, Any]:
    """Get Redis configuration."""
    return {
        "url": settings.REDIS_URL,
        "password": settings.REDIS_PASSWORD,
        "db": settings.REDIS_DB,
        "decode_responses": True,
        "retry_on_timeout": True,
        "socket_keepalive": True,
        "socket_keepalive_options": {},
    }


def get_celery_config() -> Dict[str, Any]:
    """Get Celery configuration."""
    return {
        "broker_url": settings.CELERY_BROKER_URL,
        "result_backend": settings.CELERY_RESULT_BACKEND,
        "task_serializer": "json",
        "accept_content": ["json"],
        "result_serializer": "json",
        "timezone": "UTC",
        "enable_utc": True,
        "task_track_started": True,
        "task_time_limit": 30 * 60,  # 30 minutes
        "task_soft_time_limit": 25 * 60,  # 25 minutes
        "worker_prefetch_multiplier": 1,
        "worker_max_tasks_per_child": 1000,
    }


def get_geolocation_config() -> Dict[str, Any]:
    """Get geolocation configuration for services."""
    return {
        "database_path": settings.GEOIP_DATABASE_PATH,
        "enable_routing": settings.ENABLE_GEOLOCATION_ROUTING,
        "cache_ttl_hours": settings.GEOLOCATION_CACHE_TTL_HOURS,
        "fallback_country": settings.GEOLOCATION_FALLBACK_COUNTRY,
        "maxmind_license_key": settings.MAXMIND_LICENSE_KEY,
        "auto_update_enabled": settings.GEOIP_AUTO_UPDATE_ENABLED,
        "update_webhook_url": settings.GEOIP_UPDATE_WEBHOOK_URL,
        "timeout_ms": settings.GEOLOCATION_TIMEOUT_MS,
        "max_retries": settings.GEOLOCATION_MAX_RETRIES,
        "circuit_breaker_threshold": settings.GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD,
        "circuit_breaker_timeout": settings.GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT,
        "cache_max_size": settings.GEOLOCATION_CACHE_MAX_SIZE,
        "cache_compression": settings.GEOLOCATION_CACHE_COMPRESSION,
        "redis_prefix": settings.GEOLOCATION_REDIS_PREFIX,
        "redis_ttl": settings.GEOLOCATION_REDIS_TTL,
        "redis_compression": settings.GEOLOCATION_REDIS_COMPRESSION,
        "metrics_enabled": settings.GEOLOCATION_METRICS_ENABLED,
        "detailed_logging": settings.GEOLOCATION_DETAILED_LOGGING,
        "performance_tracking": settings.GEOLOCATION_PERFORMANCE_TRACKING,
        "health_check_enabled": settings.GEOLOCATION_HEALTH_CHECK_ENABLED,
    }


def get_vpn_detection_config() -> Dict[str, Any]:
    """Get VPN detection configuration (Phase 2 preparation)."""
    return {
        "enabled": settings.VPN_DETECTION_ENABLED,
        "api_key": settings.VPN_DETECTION_API_KEY,
        "confidence_threshold": settings.VPN_DETECTION_CONFIDENCE_THRESHOLD,
    }


def get_ab_testing_config() -> Dict[str, Any]:
    """Get A/B testing configuration (Phase 2 preparation)."""
    return {
        "enabled": settings.AB_TESTING_ENABLED,
        "default_split": settings.AB_TESTING_DEFAULT_SPLIT,
        "min_sample_size": settings.AB_TESTING_MIN_SAMPLE_SIZE,
    }
