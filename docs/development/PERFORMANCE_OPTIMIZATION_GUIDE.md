# Performance Optimization Guide - Culture Connect Backend

## 📋 Overview

This guide provides comprehensive performance optimization strategies for the Culture Connect Backend, targeting world-class performance standards used by top-tier organizations like Netflix, Uber, and Airbnb.

## 🎯 Performance Targets

### Response Time Targets
```python
performance_targets = {
    "api_response_time": {
        "standard_endpoints": "<100ms (95th percentile)",
        "complex_queries": "<500ms (95th percentile)",
        "file_uploads": "<2s for 10MB",
        "search_queries": "<50ms (95th percentile)"
    },
    "database_performance": {
        "simple_queries": "<10ms",
        "complex_queries": "<50ms",
        "bulk_operations": "<200ms",
        "connection_time": "<5ms"
    },
    "real_time_features": {
        "websocket_latency": "<100ms",
        "notification_delivery": "<2s",
        "sync_operations": "<2s",
        "message_delivery": "<500ms"
    }
}
```

### Throughput Targets
```python
throughput_targets = {
    "concurrent_users": 10000,
    "requests_per_second": 1000,
    "database_connections": 500,
    "websocket_connections": 1000,
    "background_jobs": 100_per_minute
}
```

## 🗄️ Database Performance Optimization

### 1. Query Optimization Strategies

#### Index Strategy
```sql
-- Primary Performance Indexes
CREATE INDEX CONCURRENTLY idx_experiences_search 
ON experiences (category, location, price, average_rating) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_experiences_location_geo 
ON experiences USING GIST (ST_Point(longitude, latitude));

CREATE INDEX CONCURRENTLY idx_bookings_vendor_status_date 
ON bookings (vendor_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_bookings_tourist_date 
ON bookings (tourist_id, created_at DESC);

-- Partial Indexes for Common Queries
CREATE INDEX CONCURRENTLY idx_experiences_active_featured 
ON experiences (created_at DESC) 
WHERE is_active = true AND is_featured = true;

CREATE INDEX CONCURRENTLY idx_bookings_pending 
ON bookings (created_at DESC) 
WHERE status = 'pending';

-- Composite Indexes for Complex Filters
CREATE INDEX CONCURRENTLY idx_experiences_multi_filter 
ON experiences (category, location, price, average_rating, created_at DESC) 
WHERE is_active = true;
```

#### Query Optimization Examples
```python
# Optimized Experience Search
async def search_experiences_optimized(
    db: AsyncSession,
    filters: ExperienceFilters,
    pagination: PaginationParams
) -> ExperienceSearchResult:
    
    # Use index-friendly query structure
    query = select(Experience).options(
        # Eager load related data to avoid N+1 queries
        selectinload(Experience.vendor).selectinload(Vendor.user),
        selectinload(Experience.reviews.limit(3)),  # Limit related data
        selectinload(Experience.images.limit(5))
    )
    
    # Apply filters in index-friendly order
    if filters.category:
        query = query.where(Experience.category == filters.category)
    
    if filters.location:
        query = query.where(Experience.location.ilike(f"%{filters.location}%"))
    
    if filters.price_range:
        query = query.where(
            Experience.price.between(filters.price_range.min, filters.price_range.max)
        )
    
    # Use covering index for sorting
    query = query.where(Experience.is_active == True)
    query = query.order_by(Experience.created_at.desc())
    
    # Efficient pagination
    query = query.offset(pagination.offset).limit(pagination.limit)
    
    # Execute with proper connection management
    result = await db.execute(query)
    experiences = result.scalars().all()
    
    # Get total count efficiently (use approximate count for large datasets)
    count_query = select(func.count(Experience.id)).where(Experience.is_active == True)
    if filters.category:
        count_query = count_query.where(Experience.category == filters.category)
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    return ExperienceSearchResult(
        items=experiences,
        total=total,
        has_more=pagination.offset + pagination.limit < total
    )
```

### 2. Connection Pool Optimization

#### SQLAlchemy Configuration
```python
# Production Database Configuration
database_config = {
    # Connection Pool Settings
    "pool_size": 20,                    # Base connections
    "max_overflow": 30,                 # Additional connections under load
    "pool_pre_ping": True,              # Validate connections
    "pool_recycle": 3600,               # Recycle connections every hour
    "pool_timeout": 30,                 # Connection timeout
    
    # Query Performance
    "echo": False,                      # Disable SQL logging in production
    "future": True,                     # Use SQLAlchemy 2.0 style
    
    # Connection String Optimizations
    "connect_args": {
        "server_settings": {
            "application_name": "culture_connect_backend",
            "jit": "off",               # Disable JIT for consistent performance
        },
        "command_timeout": 60,
        "options": "-c default_transaction_isolation=read_committed"
    }
}

# Read Replica Configuration
read_replica_config = {
    "pool_size": 15,
    "max_overflow": 25,
    "pool_pre_ping": True,
    "pool_recycle": 3600,
}

# Connection Management
class DatabaseManager:
    def __init__(self):
        self.write_engine = create_async_engine(
            DATABASE_URL,
            **database_config
        )
        self.read_engine = create_async_engine(
            READ_REPLICA_URL,
            **read_replica_config
        )
    
    async def get_write_session(self) -> AsyncSession:
        return AsyncSession(self.write_engine)
    
    async def get_read_session(self) -> AsyncSession:
        return AsyncSession(self.read_engine)
```

### 3. Database Partitioning Strategy

#### Time-Based Partitioning
```sql
-- Partition bookings by month for better performance
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    created_at TIMESTAMP NOT NULL,
    -- other columns
) PARTITION BY RANGE (created_at);

-- Create monthly partitions
CREATE TABLE bookings_2024_01 PARTITION OF bookings 
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE bookings_2024_02 PARTITION OF bookings 
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- Automated partition management
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

## 🚀 API Performance Optimization

### 1. Async/Await Implementation

#### FastAPI Async Patterns
```python
# Optimized Async Endpoint
@router.get("/experiences", response_model=ExperienceListResponse)
async def list_experiences(
    filters: ExperienceFilters = Depends(),
    pagination: PaginationParams = Depends(),
    db: AsyncSession = Depends(get_read_db),
    cache: Redis = Depends(get_redis)
) -> ExperienceListResponse:
    
    # Generate cache key
    cache_key = f"experiences:{hash(filters)}:{pagination.offset}:{pagination.limit}"
    
    # Try cache first
    cached_result = await cache.get(cache_key)
    if cached_result:
        return ExperienceListResponse.parse_raw(cached_result)
    
    # Database query with async operations
    async with db as session:
        result = await search_experiences_optimized(session, filters, pagination)
    
    # Cache the result
    await cache.setex(
        cache_key, 
        300,  # 5 minutes TTL
        result.json()
    )
    
    return result

# Concurrent Operations
async def get_vendor_dashboard_data(vendor_id: UUID, db: AsyncSession) -> VendorDashboard:
    # Execute multiple queries concurrently
    bookings_task = asyncio.create_task(
        get_recent_bookings(vendor_id, db)
    )
    earnings_task = asyncio.create_task(
        calculate_earnings_summary(vendor_id, db)
    )
    analytics_task = asyncio.create_task(
        get_performance_metrics(vendor_id, db)
    )
    
    # Wait for all tasks to complete
    bookings, earnings, analytics = await asyncio.gather(
        bookings_task,
        earnings_task,
        analytics_task
    )
    
    return VendorDashboard(
        bookings=bookings,
        earnings=earnings,
        analytics=analytics
    )
```

### 2. Response Caching Strategy

#### Multi-Layer Caching
```python
# Cache Configuration
cache_config = {
    "redis_cluster": {
        "nodes": [
            {"host": "redis-1", "port": 6379},
            {"host": "redis-2", "port": 6379},
            {"host": "redis-3", "port": 6379}
        ],
        "decode_responses": True,
        "health_check_interval": 30
    },
    "cache_policies": {
        "experiences": {"ttl": 300, "tags": ["experiences"]},
        "vendors": {"ttl": 600, "tags": ["vendors"]},
        "user_sessions": {"ttl": 3600, "tags": ["auth"]},
        "search_results": {"ttl": 180, "tags": ["search"]},
        "analytics": {"ttl": 1800, "tags": ["analytics"]}
    }
}

# Cache Decorator
def cache_response(ttl: int = 300, tags: List[str] = None):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            redis = await get_redis()
            cached_result = await redis.get(cache_key)
            
            if cached_result:
                return json.loads(cached_result)
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache the result
            await redis.setex(cache_key, ttl, json.dumps(result, default=str))
            
            # Add to cache tags for invalidation
            if tags:
                for tag in tags:
                    await redis.sadd(f"tag:{tag}", cache_key)
            
            return result
        return wrapper
    return decorator

# Cache Invalidation
async def invalidate_cache_by_tag(tag: str):
    redis = await get_redis()
    cache_keys = await redis.smembers(f"tag:{tag}")
    
    if cache_keys:
        # Delete all cached items with this tag
        await redis.delete(*cache_keys)
        # Remove the tag set
        await redis.delete(f"tag:{tag}")
```

### 3. Request/Response Optimization

#### Response Compression
```python
# Gzip Compression Middleware
from fastapi.middleware.gzip import GZipMiddleware

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Custom Response Optimization
class OptimizedJSONResponse(JSONResponse):
    def render(self, content: Any) -> bytes:
        # Use faster JSON encoder
        return orjson.dumps(
            content,
            option=orjson.OPT_NON_STR_KEYS | orjson.OPT_SERIALIZE_NUMPY
        )

# Pagination Optimization
class CursorPagination:
    def __init__(self, cursor: str = None, limit: int = 20):
        self.cursor = cursor
        self.limit = min(limit, 100)  # Max 100 items per page
    
    def apply_to_query(self, query, order_column):
        if self.cursor:
            # Decode cursor to get the last item's order value
            last_value = base64.b64decode(self.cursor).decode()
            query = query.where(order_column > last_value)
        
        return query.order_by(order_column).limit(self.limit + 1)
    
    def get_response_data(self, items, order_column):
        has_more = len(items) > self.limit
        if has_more:
            items = items[:-1]  # Remove the extra item
        
        next_cursor = None
        if has_more and items:
            # Create cursor from the last item
            last_value = getattr(items[-1], order_column.key)
            next_cursor = base64.b64encode(str(last_value).encode()).decode()
        
        return {
            "items": items,
            "has_more": has_more,
            "next_cursor": next_cursor
        }
```

## 🔄 Background Processing Optimization

### 1. Celery Task Optimization

#### Task Configuration
```python
# Celery Configuration
celery_config = {
    "broker_url": "redis://redis-cluster:6379/0",
    "result_backend": "redis://redis-cluster:6379/1",
    "task_serializer": "pickle",
    "accept_content": ["pickle", "json"],
    "result_serializer": "pickle",
    "timezone": "UTC",
    "enable_utc": True,
    
    # Performance Settings
    "worker_prefetch_multiplier": 4,
    "task_acks_late": True,
    "worker_disable_rate_limits": True,
    "task_compression": "gzip",
    "result_compression": "gzip",
    
    # Queue Configuration
    "task_routes": {
        "send_notification": {"queue": "high_priority"},
        "process_payment": {"queue": "high_priority"},
        "generate_report": {"queue": "low_priority"},
        "cleanup_data": {"queue": "low_priority"}
    }
}

# Optimized Task Implementation
@celery_app.task(bind=True, max_retries=3)
def process_booking_confirmation(self, booking_id: str):
    try:
        # Use connection pooling
        with get_db_session() as db:
            booking = db.query(Booking).filter(Booking.id == booking_id).first()
            
            if not booking:
                raise ValueError(f"Booking {booking_id} not found")
            
            # Process in batch to reduce database calls
            tasks = [
                send_confirmation_email.delay(booking.tourist_id, booking_id),
                update_vendor_metrics.delay(booking.vendor_id),
                sync_mobile_app.delay(booking_id)
            ]
            
            # Wait for critical tasks
            for task in tasks[:2]:  # Email and metrics are critical
                task.get(timeout=30)
            
            return {"status": "success", "booking_id": booking_id}
            
    except Exception as exc:
        # Exponential backoff retry
        countdown = 2 ** self.request.retries
        raise self.retry(exc=exc, countdown=countdown)
```

### 2. Background Job Patterns

#### Batch Processing
```python
# Batch Processing for Efficiency
@celery_app.task
def process_daily_analytics():
    batch_size = 1000
    
    with get_db_session() as db:
        # Process in batches to avoid memory issues
        offset = 0
        while True:
            bookings = db.query(Booking).filter(
                Booking.created_at >= date.today() - timedelta(days=1)
            ).offset(offset).limit(batch_size).all()
            
            if not bookings:
                break
            
            # Process batch
            analytics_data = []
            for booking in bookings:
                analytics_data.append(calculate_booking_metrics(booking))
            
            # Bulk insert for performance
            db.bulk_insert_mappings(AnalyticsRecord, analytics_data)
            db.commit()
            
            offset += batch_size

# Async Task Processing
async def process_notifications_async(notification_batch: List[Notification]):
    # Use aiohttp for concurrent HTTP requests
    async with aiohttp.ClientSession() as session:
        tasks = []
        for notification in notification_batch:
            task = send_push_notification(session, notification)
            tasks.append(task)
        
        # Process all notifications concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle results and errors
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        return {"success": success_count, "total": len(notification_batch)}
```

## 📊 Monitoring & Performance Tracking

### 1. Custom Metrics Collection

#### Performance Metrics
```python
# Custom Metrics with Prometheus
from prometheus_client import Counter, Histogram, Gauge

# API Metrics
api_requests_total = Counter(
    'api_requests_total',
    'Total API requests',
    ['method', 'endpoint', 'status']
)

api_request_duration = Histogram(
    'api_request_duration_seconds',
    'API request duration',
    ['method', 'endpoint']
)

# Database Metrics
db_connections_active = Gauge(
    'db_connections_active',
    'Active database connections'
)

db_query_duration = Histogram(
    'db_query_duration_seconds',
    'Database query duration',
    ['query_type']
)

# Business Metrics
bookings_created_total = Counter(
    'bookings_created_total',
    'Total bookings created'
)

revenue_total = Counter(
    'revenue_total',
    'Total revenue processed'
)

# Middleware for Automatic Metrics Collection
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    # Record metrics
    duration = time.time() - start_time
    api_request_duration.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    api_requests_total.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    return response
```

### 2. Performance Alerting

#### Alert Configuration
```yaml
# Prometheus Alert Rules
groups:
  - name: api_performance
    rules:
      - alert: HighAPILatency
        expr: histogram_quantile(0.95, api_request_duration_seconds) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API latency detected"
          description: "95th percentile latency is {{ $value }}s"
      
      - alert: HighErrorRate
        expr: rate(api_requests_total{status=~"5.."}[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} requests/second"
      
      - alert: DatabaseConnectionsHigh
        expr: db_connections_active > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "{{ $value }} connections active"
```

## 🎯 Performance Testing Strategy

### 1. Load Testing with Locust

#### Load Test Implementation
```python
# Locust Load Testing
from locust import HttpUser, task, between
import random

class CultureConnectUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Login to get authentication token
        response = self.client.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}
    
    @task(3)
    def search_experiences(self):
        # Simulate experience search
        params = {
            "category": random.choice(["cultural", "adventure", "food"]),
            "limit": 20,
            "offset": random.randint(0, 100)
        }
        self.client.get("/api/v1/experiences", params=params)
    
    @task(2)
    def view_experience_details(self):
        # Simulate viewing experience details
        experience_id = random.choice(self.experience_ids)
        self.client.get(f"/api/v1/experiences/{experience_id}")
    
    @task(1)
    def create_booking(self):
        # Simulate booking creation
        booking_data = {
            "experience_id": random.choice(self.experience_ids),
            "participant_count": random.randint(1, 4),
            "special_requirements": "Test booking"
        }
        self.client.post("/api/v1/bookings", 
                        json=booking_data, 
                        headers=self.headers)

# Performance Test Configuration
load_test_config = {
    "users": 1000,
    "spawn_rate": 10,
    "run_time": "10m",
    "host": "https://api.cultureconnect.com"
}
```

### 2. Continuous Performance Monitoring

#### Performance CI/CD Integration
```yaml
# GitHub Actions Performance Testing
name: Performance Tests
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  performance_test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.10
      
      - name: Install dependencies
        run: |
          pip install locust pytest-benchmark
      
      - name: Run performance tests
        run: |
          locust -f tests/performance/load_test.py \
                 --headless \
                 --users 100 \
                 --spawn-rate 5 \
                 --run-time 5m \
                 --host ${{ secrets.TEST_HOST }}
      
      - name: Benchmark critical functions
        run: |
          pytest tests/performance/benchmark_tests.py \
                 --benchmark-only \
                 --benchmark-json=benchmark_results.json
      
      - name: Performance regression check
        run: |
          python scripts/check_performance_regression.py \
                 benchmark_results.json
```

---

**Performance Optimization Priority**: Focus on database query optimization first (biggest impact), then implement caching, followed by async processing, and finally advanced monitoring and alerting systems.
