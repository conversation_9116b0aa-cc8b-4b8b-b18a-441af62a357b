"""
Tests for Content Moderation schemas.

This module tests the comprehensive content moderation Pydantic V2 schemas including:
- ContentModerationWorkflow schemas with validation and computed fields
- ContentQualityScore schemas with scoring validation
- PlagiarismCheck schemas with similarity analysis
- ContentModerationRule schemas with rule logic validation
- ContentApprovalHistory schemas with audit trail validation

Implements Task 3.2.4 testing requirements with >80% coverage.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from pydantic import ValidationError

from app.schemas.content_moderation import (
    ContentModerationWorkflowCreate, ContentModerationWorkflowUpdate, ContentModerationWorkflowResponse,
    ContentQualityScoreCreate, ContentQualityScoreUpdate, ContentQualityScoreResponse,
    PlagiarismCheckCreate, PlagiarismCheckUpdate, PlagiarismCheckResponse,
    ContentModerationRuleCreate, ContentModerationRuleUpdate, ContentModerationRuleResponse,
    ContentApprovalHistoryCreate, ContentApprovalHistoryResponse,
    ContentModerationDashboard,
    ContentStatus, ContentType, ModerationAction, PlagiarismStatus
)


class TestContentModerationWorkflowSchemas:
    """Test ContentModerationWorkflow schemas."""

    def test_workflow_create_schema_valid(self):
        """Test valid workflow creation schema."""
        workflow_data = {
            "content_type": ContentType.SERVICE_LISTING,
            "content_id": str(uuid4()),
            "vendor_id": str(uuid4()),
            "priority": 8,
            "content_title": "Professional Photography Services",
            "content_summary": "High-quality photography for events and portraits",
            "content_url": "https://example.com/service/123",
            "content_metadata": {
                "category": "photography",
                "location": "Lagos, Nigeria",
                "price_range": "50000-200000"
            }
        }
        
        schema = ContentModerationWorkflowCreate(**workflow_data)
        
        assert schema.content_type == ContentType.SERVICE_LISTING
        assert schema.priority == 8
        assert schema.content_title == "Professional Photography Services"
        assert schema.content_metadata["category"] == "photography"

    def test_workflow_create_schema_validation_errors(self):
        """Test workflow creation schema validation errors."""
        # Test invalid priority
        with pytest.raises(ValidationError) as exc_info:
            ContentModerationWorkflowCreate(
                content_type=ContentType.SERVICE_LISTING,
                content_id=str(uuid4()),
                vendor_id=str(uuid4()),
                priority=15  # Invalid: > 10
            )
        
        assert "priority" in str(exc_info.value)
        
        # Test invalid content_metadata type
        with pytest.raises(ValidationError) as exc_info:
            ContentModerationWorkflowCreate(
                content_type=ContentType.SERVICE_LISTING,
                content_id=str(uuid4()),
                vendor_id=str(uuid4()),
                content_metadata="invalid_type"  # Should be dict
            )
        
        assert "Content metadata must be a dictionary" in str(exc_info.value)

    def test_workflow_update_schema(self):
        """Test workflow update schema."""
        update_data = {
            "status": ContentStatus.UNDER_REVIEW,
            "priority": 9,
            "reviewer_notes": "Content requires additional review for cultural sensitivity",
            "revision_requirements": ["Add more cultural context", "Improve image quality"]
        }
        
        schema = ContentModerationWorkflowUpdate(**update_data)
        
        assert schema.status == ContentStatus.UNDER_REVIEW
        assert schema.priority == 9
        assert "cultural sensitivity" in schema.reviewer_notes
        assert len(schema.revision_requirements) == 2

    def test_workflow_response_schema_computed_fields(self):
        """Test workflow response schema computed fields."""
        workflow_data = {
            "id": str(uuid4()),
            "content_type": ContentType.SERVICE_LISTING,
            "content_id": str(uuid4()),
            "vendor_id": str(uuid4()),
            "status": ContentStatus.APPROVED,
            "priority": 7,
            "moderation_score": 85.5,
            "quality_score": 92.3,
            "plagiarism_score": 8.7,
            "automated_flags": [],
            "manual_flags": [],
            "revision_requirements": [],
            "submitted_at": datetime.utcnow(),
            "review_started_at": datetime.utcnow() - timedelta(hours=2),
            "review_completed_at": datetime.utcnow() - timedelta(minutes=30),
            "auto_approval_deadline": datetime.utcnow() + timedelta(hours=24),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        schema = ContentModerationWorkflowResponse(**workflow_data)
        
        # Test computed fields
        assert schema.overall_health_score > 0
        assert schema.review_duration_hours is not None
        assert schema.review_duration_hours > 0
        assert schema.is_overdue is False

    def test_workflow_response_overdue_calculation(self):
        """Test overdue calculation in workflow response."""
        workflow_data = {
            "id": str(uuid4()),
            "content_type": ContentType.SERVICE_LISTING,
            "content_id": str(uuid4()),
            "vendor_id": str(uuid4()),
            "status": ContentStatus.PENDING,
            "priority": 5,
            "moderation_score": 0.0,
            "quality_score": 0.0,
            "plagiarism_score": 0.0,
            "automated_flags": [],
            "manual_flags": [],
            "revision_requirements": [],
            "submitted_at": datetime.utcnow(),
            "auto_approval_deadline": datetime.utcnow() - timedelta(hours=1),  # Past deadline
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        schema = ContentModerationWorkflowResponse(**workflow_data)
        
        assert schema.is_overdue is True


class TestContentQualityScoreSchemas:
    """Test ContentQualityScore schemas."""

    def test_quality_score_create_schema_valid(self):
        """Test valid quality score creation schema."""
        quality_data = {
            "workflow_id": str(uuid4()),
            "overall_score": 87.5,
            "weighted_score": 89.2,
            "quality_grade": "B+",
            "content_quality_score": 85.0,
            "image_quality_score": 90.0,
            "metadata_completeness_score": 88.0,
            "seo_optimization_score": 92.0,
            "mobile_friendliness_score": 85.0,
            "cultural_appropriateness_score": 95.0,
            "content_analysis": {
                "word_count": 250,
                "sentence_count": 15,
                "readability_score": 75.5
            },
            "recommendations": [
                "Improve image quality",
                "Add more SEO keywords"
            ],
            "estimated_improvement_impact": 12.5,
            "scoring_weights": {
                "content_quality": 0.25,
                "image_quality": 0.20,
                "seo_optimization": 0.20
            }
        }
        
        schema = ContentQualityScoreCreate(**quality_data)
        
        assert schema.overall_score == 87.5
        assert schema.quality_grade == "B+"
        assert schema.content_analysis["word_count"] == 250
        assert len(schema.recommendations) == 2

    def test_quality_score_validation_errors(self):
        """Test quality score validation errors."""
        # Test invalid score range
        with pytest.raises(ValidationError) as exc_info:
            ContentQualityScoreCreate(
                workflow_id=str(uuid4()),
                overall_score=150.0,  # Invalid: > 100
                weighted_score=89.2
            )
        
        assert "overall_score" in str(exc_info.value)
        
        # Test negative score
        with pytest.raises(ValidationError) as exc_info:
            ContentQualityScoreCreate(
                workflow_id=str(uuid4()),
                overall_score=85.0,
                content_quality_score=-10.0  # Invalid: < 0
            )
        
        assert "content_quality_score" in str(exc_info.value)

    def test_quality_score_response_computed_fields(self):
        """Test quality score response computed fields."""
        quality_data = {
            "id": str(uuid4()),
            "workflow_id": str(uuid4()),
            "overall_score": 87.5,
            "weighted_score": 89.2,
            "quality_grade": "B+",
            "content_quality_score": 85.0,
            "image_quality_score": 90.0,
            "metadata_completeness_score": 88.0,
            "seo_optimization_score": 92.0,
            "mobile_friendliness_score": 85.0,
            "cultural_appropriateness_score": 95.0,
            "content_analysis": {},
            "image_analysis": {},
            "seo_analysis": {},
            "mobile_analysis": {},
            "recommendations": [],
            "priority_improvements": [],
            "estimated_improvement_impact": 12.5,
            "scoring_weights": {},
            "scoring_version": "1.0",
            "calculated_at": datetime.utcnow(),
            "calculation_duration_ms": 1500
        }
        
        schema = ContentQualityScoreResponse(**quality_data)
        
        # Test computed fields
        category_scores = schema.category_scores
        assert "content_quality" in category_scores
        assert category_scores["content_quality"] == 85.0
        assert category_scores["image_quality"] == 90.0
        
        improvement_potential = schema.improvement_potential
        assert improvement_potential >= 0
        assert improvement_potential <= 100


class TestPlagiarismCheckSchemas:
    """Test PlagiarismCheck schemas."""

    def test_plagiarism_check_create_schema_valid(self):
        """Test valid plagiarism check creation schema."""
        plagiarism_data = {
            "workflow_id": str(uuid4()),
            "status": PlagiarismStatus.SIMILAR_FOUND,
            "similarity_percentage": 25.5,
            "originality_score": 74.5,
            "confidence_level": 85.0,
            "similar_sources_found": 3,
            "exact_matches_found": 1,
            "paraphrased_matches_found": 2,
            "similar_sources": [
                {
                    "url": "https://example.com/similar",
                    "title": "Similar Content",
                    "similarity_score": 85.5
                }
            ],
            "matching_segments": [
                {
                    "original_text": "Professional photography services",
                    "source_url": "https://example.com/similar",
                    "match_type": "exact"
                }
            ],
            "content_hash": "abc123def456",
            "content_length": 1500,
            "words_checked": 250,
            "sensitivity_level": "medium"
        }
        
        schema = PlagiarismCheckCreate(**plagiarism_data)
        
        assert schema.status == PlagiarismStatus.SIMILAR_FOUND
        assert schema.similarity_percentage == 25.5
        assert schema.content_hash == "abc123def456"
        assert schema.sensitivity_level == "medium"

    def test_plagiarism_check_validation_errors(self):
        """Test plagiarism check validation errors."""
        # Test invalid similarity percentage
        with pytest.raises(ValidationError) as exc_info:
            PlagiarismCheckCreate(
                workflow_id=str(uuid4()),
                similarity_percentage=150.0,  # Invalid: > 100
                content_hash="test_hash"
            )
        
        assert "similarity_percentage" in str(exc_info.value)
        
        # Test invalid sensitivity level
        with pytest.raises(ValidationError) as exc_info:
            PlagiarismCheckCreate(
                workflow_id=str(uuid4()),
                sensitivity_level="invalid_level",  # Should be low/medium/high
                content_hash="test_hash"
            )
        
        assert "sensitivity_level" in str(exc_info.value)

    def test_plagiarism_check_response_computed_fields(self):
        """Test plagiarism check response computed fields."""
        plagiarism_data = {
            "id": str(uuid4()),
            "workflow_id": str(uuid4()),
            "status": PlagiarismStatus.SIMILAR_FOUND,
            "similarity_percentage": 65.0,
            "originality_score": 35.0,
            "confidence_level": 90.0,
            "similar_sources_found": 5,
            "exact_matches_found": 3,
            "paraphrased_matches_found": 2,
            "similar_sources": [],
            "matching_segments": [],
            "source_analysis": {},
            "check_parameters": {},
            "sensitivity_level": "high",
            "excluded_sources": [],
            "content_hash": "test_hash",
            "content_length": 1000,
            "words_checked": 200,
            "processing_time_ms": 2500,
            "checked_at": datetime.utcnow(),
            "check_version": "1.0"
        }
        
        schema = PlagiarismCheckResponse(**plagiarism_data)
        
        # Test computed fields
        assert schema.plagiarism_risk_level == "high"  # 65% similarity
        assert schema.total_matches_found == 5  # 3 exact + 2 paraphrased


class TestContentModerationRuleSchemas:
    """Test ContentModerationRule schemas."""

    def test_moderation_rule_create_schema_valid(self):
        """Test valid moderation rule creation schema."""
        rule_data = {
            "rule_name": "Spam Detection Rule",
            "rule_description": "Detects spam content in service listings",
            "content_types": [ContentType.SERVICE_LISTING, ContentType.SERVICE_DESCRIPTION],
            "rule_category": "spam_detection",
            "rule_severity": "high",
            "detection_criteria": {
                "keywords": ["spam", "fake"],
                "patterns": ["excessive_caps"]
            },
            "action_on_trigger": ModerationAction.FLAG,
            "rule_logic": {
                "conditions": [
                    {"field": "content", "operator": "contains", "value": "spam"}
                ],
                "operator": "OR"
            },
            "custom_keywords": ["spam", "fake", "scam"],
            "regex_patterns": [r"\b[A-Z]{5,}\b"]
        }
        
        schema = ContentModerationRuleCreate(**rule_data)
        
        assert schema.rule_name == "Spam Detection Rule"
        assert schema.rule_severity == "high"
        assert ContentType.SERVICE_LISTING in schema.content_types
        assert "spam" in schema.custom_keywords

    def test_moderation_rule_validation_errors(self):
        """Test moderation rule validation errors."""
        # Test invalid rule logic
        with pytest.raises(ValidationError) as exc_info:
            ContentModerationRuleCreate(
                rule_name="Test Rule",
                rule_category="test",
                detection_criteria={"test": True},
                action_on_trigger=ModerationAction.APPROVE,
                rule_logic={"invalid": "structure"}  # Missing required keys
            )
        
        assert "Rule logic must contain" in str(exc_info.value)
        
        # Test invalid severity
        with pytest.raises(ValidationError) as exc_info:
            ContentModerationRuleCreate(
                rule_name="Test Rule",
                rule_category="test",
                rule_severity="invalid_severity",  # Should be low/medium/high/critical
                detection_criteria={"test": True},
                action_on_trigger=ModerationAction.APPROVE,
                rule_logic={"conditions": [], "operator": "AND"}
            )
        
        assert "rule_severity" in str(exc_info.value)

    def test_moderation_rule_response_computed_fields(self):
        """Test moderation rule response computed fields."""
        rule_data = {
            "id": str(uuid4()),
            "rule_name": "Test Rule",
            "rule_description": "Test rule description",
            "content_types": [ContentType.SERVICE_LISTING],
            "rule_category": "test",
            "rule_severity": "medium",
            "detection_criteria": {},
            "threshold_values": {},
            "action_on_trigger": ModerationAction.FLAG,
            "escalation_threshold": 0.8,
            "rule_logic": {"conditions": [], "operator": "AND"},
            "custom_keywords": [],
            "excluded_keywords": [],
            "regex_patterns": [],
            "is_active": True,
            "auto_apply": True,
            "requires_human_review": False,
            "total_applications": 100,
            "successful_detections": 85,
            "false_positives": 10,
            "accuracy_rate": 85.0,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        schema = ContentModerationRuleResponse(**rule_data)
        
        # Test computed fields
        assert schema.effectiveness_score == 85.0  # 85/100 * 100
        assert schema.false_positive_rate == 10.0  # 10/100 * 100


class TestContentApprovalHistorySchemas:
    """Test ContentApprovalHistory schemas."""

    def test_approval_history_create_schema_valid(self):
        """Test valid approval history creation schema."""
        history_data = {
            "workflow_id": str(uuid4()),
            "action_taken": ModerationAction.APPROVE,
            "previous_status": ContentStatus.UNDER_REVIEW,
            "new_status": ContentStatus.APPROVED,
            "reviewer_id": str(uuid4()),
            "reviewer_role": "content_moderator",
            "decision_reason": "Content meets quality standards",
            "confidence_score": 95.0,
            "time_spent_minutes": 15,
            "quality_score_at_decision": 87.5,
            "revision_requirements": [],
            "decision_factors": ["quality_score", "cultural_appropriateness"]
        }
        
        schema = ContentApprovalHistoryCreate(**history_data)
        
        assert schema.action_taken == ModerationAction.APPROVE
        assert schema.confidence_score == 95.0
        assert schema.reviewer_role == "content_moderator"
        assert "quality_score" in schema.decision_factors

    def test_approval_history_response_computed_fields(self):
        """Test approval history response computed fields."""
        history_data = {
            "id": str(uuid4()),
            "workflow_id": str(uuid4()),
            "action_taken": ModerationAction.APPROVE,
            "previous_status": ContentStatus.UNDER_REVIEW,
            "new_status": ContentStatus.APPROVED,
            "reviewer_id": str(uuid4()),
            "reviewer_role": "senior_moderator",
            "is_automated": False,
            "decision_reason": "High quality content",
            "reviewer_notes": "Excellent service description",
            "confidence_score": 95.0,
            "time_spent_minutes": 20,
            "quality_score_at_decision": 90.0,
            "moderation_score_at_decision": 88.0,
            "plagiarism_score_at_decision": 5.0,
            "revision_requirements": [],
            "estimated_revision_time": 0,
            "revision_priority": "medium",
            "escalated_to": str(uuid4()),
            "escalation_reason": "Complex case",
            "escalation_urgency": "normal",
            "decision_factors": [],
            "automated_flags_considered": [],
            "manual_flags_considered": [],
            "decision_made_at": datetime.utcnow(),
            "review_duration_minutes": 18
        }
        
        schema = ContentApprovalHistoryResponse(**history_data)
        
        # Test computed fields
        assert schema.decision_quality_indicator == "high_quality"  # 95% confidence + 20 min
        assert schema.has_escalation is True  # escalated_to is not None


class TestContentModerationDashboard:
    """Test ContentModerationDashboard schema."""

    def test_dashboard_schema_valid(self):
        """Test valid dashboard schema."""
        dashboard_data = {
            "total_workflows": 1250,
            "pending_reviews": 45,
            "completed_reviews": 1180,
            "auto_approved": 890,
            "rejected_content": 25,
            "flagged_content": 15,
            "average_review_time_hours": 2.5,
            "average_quality_score": 87.3,
            "average_plagiarism_score": 8.2,
            "reviewer_performance": [
                {
                    "reviewer_id": str(uuid4()),
                    "total_decisions": 150,
                    "average_confidence": 88.5
                }
            ],
            "content_type_statistics": {
                "service_listing": 800,
                "service_description": 300,
                "vendor_profile": 150
            },
            "recent_approvals": [],
            "recent_rejections": [],
            "overdue_reviews": []
        }
        
        schema = ContentModerationDashboard(**dashboard_data)
        
        assert schema.total_workflows == 1250
        assert schema.pending_reviews == 45
        assert schema.content_type_statistics["service_listing"] == 800

    def test_dashboard_computed_fields(self):
        """Test dashboard computed fields."""
        dashboard_data = {
            "total_workflows": 1000,
            "pending_reviews": 50,
            "completed_reviews": 900,
            "auto_approved": 700,
            "rejected_content": 50,
            "flagged_content": 20,
            "average_review_time_hours": 3.0,
            "average_quality_score": 85.0,
            "average_plagiarism_score": 10.0,
            "reviewer_performance": [],
            "content_type_statistics": {},
            "quality_trends": [],
            "moderation_rule_effectiveness": [],
            "recent_approvals": [],
            "recent_rejections": [],
            "overdue_reviews": []
        }
        
        schema = ContentModerationDashboard(**dashboard_data)
        
        # Test computed fields
        approval_rate = schema.approval_rate
        assert approval_rate > 0
        assert approval_rate <= 100
        
        automation_rate = schema.automation_rate
        assert automation_rate > 0
        assert automation_rate <= 100

    def test_dashboard_validation_errors(self):
        """Test dashboard validation errors."""
        # Test negative values
        with pytest.raises(ValidationError) as exc_info:
            ContentModerationDashboard(
                total_workflows=-10,  # Invalid: negative
                pending_reviews=0,
                completed_reviews=0,
                auto_approved=0,
                rejected_content=0,
                flagged_content=0,
                average_review_time_hours=0,
                average_quality_score=0,
                average_plagiarism_score=0
            )
        
        assert "total_workflows" in str(exc_info.value)
