"""
Service repository for Culture Connect Backend API.

This module implements the repository pattern for service-related data access
including services, categories, pricing, availability, and media management.

Implements Task 3.2.1 requirements for comprehensive service listing system
with advanced querying capabilities and hierarchical categorization.
"""

from datetime import date, datetime, time
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy.sql import Select

from app.repositories.base import BaseRepository
from app.models.service import (
    Service, ServiceCategory, ServiceImage, ServicePricing, ServiceAvailability,
    ServiceStatus, PricingType, AvailabilityType
)
from app.models.vendor import Vendor
from app.schemas.service_extended import ServiceSearchRequest


class ServiceCategoryRepository(BaseRepository[ServiceCategory]):
    """Repository for service category operations."""

    def __init__(self, db: Session):
        super().__init__(ServiceCategory, db)

    async def get_by_slug(self, slug: str) -> Optional[ServiceCategory]:
        """Get category by slug."""
        return self.db.query(ServiceCategory).filter(
            ServiceCategory.slug == slug,
            ServiceCategory.is_active == True
        ).first()

    async def get_root_categories(self) -> List[ServiceCategory]:
        """Get all root categories (no parent)."""
        return self.db.query(ServiceCategory).filter(
            ServiceCategory.parent_id.is_(None),
            ServiceCategory.is_active == True
        ).order_by(ServiceCategory.display_order, ServiceCategory.name).all()

    async def get_children(self, parent_id: int) -> List[ServiceCategory]:
        """Get child categories of a parent."""
        return self.db.query(ServiceCategory).filter(
            ServiceCategory.parent_id == parent_id,
            ServiceCategory.is_active == True
        ).order_by(ServiceCategory.display_order, ServiceCategory.name).all()

    async def get_category_tree(self, parent_id: Optional[int] = None) -> List[ServiceCategory]:
        """Get category tree with children loaded."""
        query = self.db.query(ServiceCategory).options(
            selectinload(ServiceCategory.children)
        ).filter(ServiceCategory.is_active == True)

        if parent_id is None:
            query = query.filter(ServiceCategory.parent_id.is_(None))
        else:
            query = query.filter(ServiceCategory.parent_id == parent_id)

        return query.order_by(ServiceCategory.display_order, ServiceCategory.name).all()

    async def get_all_active(self) -> List[ServiceCategory]:
        """Get all active categories."""
        return self.db.query(ServiceCategory).filter(
            ServiceCategory.is_active == True
        ).order_by(ServiceCategory.display_order, ServiceCategory.name).all()

    async def get_with_service_count(self, category_id: int) -> Optional[ServiceCategory]:
        """Get category with service count."""
        category = self.db.query(ServiceCategory).filter(
            ServiceCategory.id == category_id
        ).first()

        if category:
            # Add service count as dynamic attribute
            service_count = self.db.query(func.count(Service.id)).filter(
                Service.category_id == category_id,
                Service.status == ServiceStatus.ACTIVE
            ).scalar()
            category.service_count = service_count

        return category


class ServiceImageRepository(BaseRepository[ServiceImage]):
    """Repository for service image operations."""

    def __init__(self, db: Session):
        super().__init__(ServiceImage, db)

    async def get_by_service(self, service_id: int) -> List[ServiceImage]:
        """Get all images for a service."""
        return self.db.query(ServiceImage).filter(
            ServiceImage.service_id == service_id
        ).order_by(ServiceImage.display_order, ServiceImage.id).all()

    async def get_primary_image(self, service_id: int) -> Optional[ServiceImage]:
        """Get primary image for a service."""
        return self.db.query(ServiceImage).filter(
            ServiceImage.service_id == service_id,
            ServiceImage.is_primary == True
        ).first()

    async def set_primary_image(self, service_id: int, image_id: int) -> bool:
        """Set an image as primary for a service."""
        # First, unset all primary images for the service
        self.db.query(ServiceImage).filter(
            ServiceImage.service_id == service_id
        ).update({"is_primary": False})

        # Set the specified image as primary
        result = self.db.query(ServiceImage).filter(
            ServiceImage.id == image_id,
            ServiceImage.service_id == service_id
        ).update({"is_primary": True})

        self.db.commit()
        return result > 0


class ServicePricingRepository(BaseRepository[ServicePricing]):
    """Repository for service pricing operations."""

    def __init__(self, db: Session):
        super().__init__(ServicePricing, db)

    async def get_by_service(self, service_id: int) -> List[ServicePricing]:
        """Get all pricing tiers for a service."""
        return self.db.query(ServicePricing).filter(
            ServicePricing.service_id == service_id,
            ServicePricing.is_active == True
        ).order_by(ServicePricing.min_participants).all()

    async def get_active_pricing(self, service_id: int, participants: int) -> Optional[ServicePricing]:
        """Get applicable pricing for number of participants."""
        return self.db.query(ServicePricing).filter(
            ServicePricing.service_id == service_id,
            ServicePricing.is_active == True,
            ServicePricing.min_participants <= participants,
            or_(
                ServicePricing.max_participants.is_(None),
                ServicePricing.max_participants >= participants
            )
        ).order_by(ServicePricing.min_participants.desc()).first()


class ServiceAvailabilityRepository(BaseRepository[ServiceAvailability]):
    """Repository for service availability operations."""

    def __init__(self, db: Session):
        super().__init__(ServiceAvailability, db)

    async def get_by_service_and_date_range(
        self,
        service_id: int,
        start_date: date,
        end_date: date
    ) -> List[ServiceAvailability]:
        """Get availability for a service within date range."""
        return self.db.query(ServiceAvailability).filter(
            ServiceAvailability.service_id == service_id,
            ServiceAvailability.available_date >= start_date,
            ServiceAvailability.available_date <= end_date,
            ServiceAvailability.is_available == True
        ).order_by(ServiceAvailability.available_date, ServiceAvailability.start_time).all()

    async def get_by_date_range(
        self,
        service_id: int,
        start_date: date,
        end_date: date,
        available_only: bool = True
    ) -> List[ServiceAvailability]:
        """Get availability for a service within date range."""
        query = self.db.query(ServiceAvailability).filter(
            ServiceAvailability.service_id == service_id,
            ServiceAvailability.date >= start_date,
            ServiceAvailability.date <= end_date
        )

        if available_only:
            query = query.filter(ServiceAvailability.is_available == True)

        return query.order_by(ServiceAvailability.date, ServiceAvailability.start_time).all()

    async def get_by_date_time(
        self,
        service_id: int,
        date: date,
        start_time: time
    ) -> Optional[ServiceAvailability]:
        """Get availability slot by service, date, and time."""
        return self.db.query(ServiceAvailability).filter(
            ServiceAvailability.service_id == service_id,
            ServiceAvailability.date == date,
            ServiceAvailability.start_time == start_time
        ).first()

    async def get_available_slots(
        self,
        service_id: int,
        target_date: date
    ) -> List[ServiceAvailability]:
        """Get available slots for a specific date."""
        return self.db.query(ServiceAvailability).filter(
            ServiceAvailability.service_id == service_id,
            ServiceAvailability.available_date == target_date,
            ServiceAvailability.is_available == True,
            ServiceAvailability.current_bookings < ServiceAvailability.max_bookings
        ).order_by(ServiceAvailability.start_time).all()

    async def update_booking_count(self, availability_id: int, increment: int = 1) -> bool:
        """Update booking count for an availability slot."""
        result = self.db.query(ServiceAvailability).filter(
            ServiceAvailability.id == availability_id
        ).update({
            "current_bookings": ServiceAvailability.current_bookings + increment
        })

        self.db.commit()
        return result > 0


class ServiceRepository(BaseRepository[Service]):
    """Repository for service operations with advanced querying capabilities."""

    def __init__(self, db: Session):
        super().__init__(Service, db)
        self.category_repo = ServiceCategoryRepository(db)
        self.image_repo = ServiceImageRepository(db)
        self.pricing_repo = ServicePricingRepository(db)
        self.availability_repo = ServiceAvailabilityRepository(db)

    async def get_with_relations(self, service_id: int) -> Optional[Service]:
        """Get service with all related data loaded."""
        return self.db.query(Service).options(
            joinedload(Service.vendor),
            joinedload(Service.category),
            selectinload(Service.images),
            selectinload(Service.pricing_tiers),
            selectinload(Service.availability_slots)
        ).filter(Service.id == service_id).first()

    async def get_by_vendor(self, vendor_id: int, status: Optional[ServiceStatus] = None) -> List[Service]:
        """Get services by vendor with optional status filter."""
        query = self.db.query(Service).filter(Service.vendor_id == vendor_id)

        if status:
            query = query.filter(Service.status == status)

        return query.order_by(desc(Service.created_at)).all()

    async def get_by_slug(self, vendor_id: int, slug: str) -> Optional[Service]:
        """Get service by vendor and slug."""
        return self.db.query(Service).filter(
            Service.vendor_id == vendor_id,
            Service.slug == slug
        ).first()

    async def search_services(self, search_request: ServiceSearchRequest) -> Tuple[List[Service], int]:
        """Advanced service search with filtering, sorting, and pagination."""
        query = self.db.query(Service).options(
            joinedload(Service.vendor),
            joinedload(Service.category),
            selectinload(Service.images.and_(ServiceImage.is_primary == True))
        )

        # Apply filters
        query = self._apply_search_filters(query, search_request)

        # Get total count before pagination
        total = query.count()

        # Apply sorting
        query = self._apply_sorting(query, search_request.sort_by, search_request.sort_order)

        # Apply pagination
        offset = (search_request.page - 1) * search_request.per_page
        services = query.offset(offset).limit(search_request.per_page).all()

        return services, total

    def _apply_search_filters(self, query: Select, search_request: ServiceSearchRequest) -> Select:
        """Apply search filters to query."""
        # Text search
        if search_request.query:
            search_term = f"%{search_request.query}%"
            query = query.filter(
                or_(
                    Service.title.ilike(search_term),
                    Service.description.ilike(search_term),
                    Service.short_description.ilike(search_term),
                    Service.tags.op('?')(search_request.query)  # JSON contains
                )
            )

        # Category filter
        if search_request.category_id:
            query = query.filter(Service.category_id == search_request.category_id)

        # Vendor filter
        if search_request.vendor_id:
            query = query.filter(Service.vendor_id == search_request.vendor_id)

        # Location filter
        if search_request.location:
            location_term = f"%{search_request.location}%"
            query = query.filter(Service.location.ilike(location_term))

        # Price range filter
        if search_request.min_price is not None:
            query = query.filter(Service.base_price >= search_request.min_price)

        if search_request.max_price is not None:
            query = query.filter(Service.base_price <= search_request.max_price)

        # Currency filter
        if search_request.currency:
            query = query.filter(Service.currency == search_request.currency)

        # Pricing type filter
        if search_request.pricing_type:
            query = query.filter(Service.pricing_type == search_request.pricing_type)

        # Availability type filter
        if search_request.availability_type:
            query = query.filter(Service.availability_type == search_request.availability_type)

        # Status filter
        if search_request.status:
            query = query.filter(Service.status == search_request.status)
        else:
            # Default to active services only
            query = query.filter(Service.status == ServiceStatus.ACTIVE)

        # Featured filter
        if search_request.is_featured is not None:
            query = query.filter(Service.is_featured == search_request.is_featured)

        # Instant booking filter
        if search_request.is_instant_booking is not None:
            query = query.filter(Service.is_instant_booking == search_request.is_instant_booking)

        # Tags filter
        if search_request.tags:
            for tag in search_request.tags:
                query = query.filter(Service.tags.op('?')(tag))

        # Date availability filter
        if search_request.date_from or search_request.date_to:
            availability_subquery = self.db.query(ServiceAvailability.service_id).filter(
                ServiceAvailability.is_available == True
            )

            if search_request.date_from:
                availability_subquery = availability_subquery.filter(
                    ServiceAvailability.available_date >= search_request.date_from
                )

            if search_request.date_to:
                availability_subquery = availability_subquery.filter(
                    ServiceAvailability.available_date <= search_request.date_to
                )

            query = query.filter(Service.id.in_(availability_subquery))

        return query

    def _apply_sorting(self, query: Select, sort_by: str, sort_order: str) -> Select:
        """Apply sorting to query."""
        sort_column = None

        if sort_by == "title":
            sort_column = Service.title
        elif sort_by == "price":
            sort_column = Service.base_price
        elif sort_by == "rating":
            sort_column = Service.average_rating
        elif sort_by == "created_at":
            sort_column = Service.created_at
        else:
            sort_column = Service.created_at

        if sort_order == "asc":
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))

        return query

    async def get_by_vendor_slug(self, vendor_id: int, slug: str) -> Optional[Service]:
        """Get service by vendor ID and slug."""
        return self.db.query(Service).filter(
            Service.vendor_id == vendor_id,
            Service.slug == slug
        ).first()

    async def get_featured_services(
        self,
        limit: int = 10,
        category_id: Optional[int] = None,
        location: Optional[str] = None
    ) -> List[Service]:
        """Get featured services with optional filters."""
        query = self.db.query(Service).filter(
            Service.is_featured == True,
            Service.status == ServiceStatus.ACTIVE
        ).options(
            joinedload(Service.vendor),
            joinedload(Service.category),
            selectinload(Service.images.and_(ServiceImage.is_primary == True))
        )

        if category_id:
            query = query.filter(Service.category_id == category_id)

        if location:
            location_term = f"%{location}%"
            query = query.filter(Service.location.ilike(location_term))

        return query.order_by(desc(Service.average_rating), desc(Service.created_at)).limit(limit).all()

    async def get_similar_services(
        self,
        service_id: int,
        category_id: Optional[int] = None,
        price_range: Optional[Tuple[Decimal, Decimal]] = None,
        limit: int = 5
    ) -> List[Service]:
        """Get services similar to the given service."""
        query = self.db.query(Service).filter(
            Service.id != service_id,
            Service.status == ServiceStatus.ACTIVE
        ).options(
            joinedload(Service.vendor),
            joinedload(Service.category),
            selectinload(Service.images.and_(ServiceImage.is_primary == True))
        )

        if category_id:
            query = query.filter(Service.category_id == category_id)

        if price_range:
            min_price, max_price = price_range
            query = query.filter(
                Service.base_price >= min_price,
                Service.base_price <= max_price
            )

        return query.order_by(desc(Service.average_rating)).limit(limit).all()

    async def search_by_location(
        self,
        latitude: float,
        longitude: float,
        radius_km: float,
        limit: int = 50,
        category_id: Optional[int] = None
    ) -> List[Service]:
        """Search services by geographic location."""
        # This would use PostGIS for real geographic queries
        # For now, return services with location data
        query = self.db.query(Service).filter(
            Service.status == ServiceStatus.ACTIVE,
            Service.location.isnot(None)
        ).options(
            joinedload(Service.vendor),
            joinedload(Service.category),
            selectinload(Service.images.and_(ServiceImage.is_primary == True))
        )

        if category_id:
            query = query.filter(Service.category_id == category_id)

        return query.order_by(desc(Service.average_rating)).limit(limit).all()

    async def get_search_suggestions(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get search suggestions based on partial query."""
        search_term = f"%{query}%"

        # Get service titles that match
        services = self.db.query(Service.title).filter(
            Service.title.ilike(search_term),
            Service.status == ServiceStatus.ACTIVE
        ).distinct().limit(limit).all()

        suggestions = [
            {"type": "service", "text": service.title, "category": "Services"}
            for service in services
        ]

        return suggestions

    # Task 3.2.2: Marketplace optimization query methods
    async def get_services_with_optimization_data(
        self,
        vendor_id: Optional[int] = None,
        include_seo: bool = True,
        include_performance: bool = True,
        include_mobile: bool = True,
        limit: int = 50
    ) -> List[Service]:
        """Get services with their latest optimization data."""
        query = self.db.query(Service).filter(Service.status == ServiceStatus.ACTIVE)

        if vendor_id:
            query = query.filter(Service.vendor_id == vendor_id)

        # Add optimization data relationships
        options = [
            joinedload(Service.vendor),
            joinedload(Service.category)
        ]

        if include_seo:
            options.append(selectinload(Service.seo_analyses))
        if include_performance:
            options.append(selectinload(Service.performance_metrics))
        if include_mobile:
            options.append(selectinload(Service.mobile_optimizations))

        query = query.options(*options)
        return query.order_by(desc(Service.created_at)).limit(limit).all()

    async def get_services_needing_optimization(
        self,
        vendor_id: Optional[int] = None,
        seo_threshold: float = 70.0,
        mobile_threshold: float = 70.0
    ) -> List[Service]:
        """Get services that need optimization improvements."""
        from app.models.marketplace_optimization import SEOAnalysis, MobileOptimization

        # Subquery for latest SEO scores
        latest_seo = self.db.query(
            SEOAnalysis.service_id,
            func.max(SEOAnalysis.analysis_date).label('latest_seo_date')
        ).group_by(SEOAnalysis.service_id).subquery()

        # Subquery for latest mobile scores
        latest_mobile = self.db.query(
            MobileOptimization.service_id,
            func.max(MobileOptimization.analysis_date).label('latest_mobile_date')
        ).group_by(MobileOptimization.service_id).subquery()

        query = self.db.query(Service).filter(Service.status == ServiceStatus.ACTIVE)

        if vendor_id:
            query = query.filter(Service.vendor_id == vendor_id)

        # Join with optimization data
        query = query.outerjoin(
            latest_seo, Service.id == latest_seo.c.service_id
        ).outerjoin(
            SEOAnalysis,
            and_(
                SEOAnalysis.service_id == latest_seo.c.service_id,
                SEOAnalysis.analysis_date == latest_seo.c.latest_seo_date
            )
        ).outerjoin(
            latest_mobile, Service.id == latest_mobile.c.service_id
        ).outerjoin(
            MobileOptimization,
            and_(
                MobileOptimization.service_id == latest_mobile.c.service_id,
                MobileOptimization.analysis_date == latest_mobile.c.latest_mobile_date
            )
        )

        # Filter by thresholds
        query = query.filter(
            or_(
                SEOAnalysis.overall_seo_score < seo_threshold,
                MobileOptimization.overall_mobile_score < mobile_threshold,
                SEOAnalysis.overall_seo_score.is_(None),
                MobileOptimization.overall_mobile_score.is_(None)
            )
        )

        return query.options(
            joinedload(Service.vendor),
            joinedload(Service.category)
        ).order_by(desc(Service.created_at)).all()

    async def get_optimization_summary_by_category(
        self,
        category_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get optimization summary statistics by category."""
        from app.models.marketplace_optimization import SEOAnalysis, PerformanceMetrics, MobileOptimization

        query = self.db.query(Service).filter(Service.status == ServiceStatus.ACTIVE)

        if category_id:
            query = query.filter(Service.category_id == category_id)

        # Get services with their latest optimization data
        services = query.options(
            selectinload(Service.seo_analyses),
            selectinload(Service.performance_metrics),
            selectinload(Service.mobile_optimizations)
        ).all()

        # Calculate summary statistics
        total_services = len(services)
        services_with_seo = sum(1 for s in services if s.seo_analyses)
        services_with_performance = sum(1 for s in services if s.performance_metrics)
        services_with_mobile = sum(1 for s in services if s.mobile_optimizations)

        return {
            "category_id": category_id,
            "total_services": total_services,
            "optimization_coverage": {
                "seo_analysis": {
                    "count": services_with_seo,
                    "percentage": round(services_with_seo / total_services * 100, 2) if total_services > 0 else 0
                },
                "performance_metrics": {
                    "count": services_with_performance,
                    "percentage": round(services_with_performance / total_services * 100, 2) if total_services > 0 else 0
                },
                "mobile_optimization": {
                    "count": services_with_mobile,
                    "percentage": round(services_with_mobile / total_services * 100, 2) if total_services > 0 else 0
                }
            }
        }