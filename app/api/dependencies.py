"""
API Dependencies for Culture Connect Backend API.

This module consolidates all common FastAPI dependencies including:
- Authentication and authorization
- Database session management
- Cache management
- Correlation ID tracking
- Permission checking

Provides a centralized location for all API endpoint dependencies.
"""

import logging
import uuid
from typing import Optional, List, Dict, Any, Generator
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

# Import from existing modules
from app.core.deps import (
    get_current_user as _get_current_user,
    get_current_user_optional,
    get_db_session,
    require_permission as _require_permission,
    require_permission_dependency,
    get_current_admin_user,
    get_current_vendor_user
)
from app.core.logging import correlation_id
from app.core.cache import get_cache_manager as _get_cache_manager
from app.models.user import User

logger = logging.getLogger(__name__)


# Database dependencies
async def get_db() -> AsyncSession:
    """
    Database session dependency.

    Returns:
        AsyncSession: Database session for request
    """
    async for session in get_db_session():
        yield session


# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token.

    Args:
        credentials: HTTP Bearer token credentials
        db: Database session

    Returns:
        User: Authenticated user instance

    Raises:
        HTTPException: If token is invalid or user not found
    """
    return await _get_current_user(credentials, db)


# Permission dependencies
def require_permission(permission: str):
    """
    Dependency factory for requiring a specific permission.

    Args:
        permission: Required permission string

    Returns:
        Dependency function that checks the permission
    """
    return require_permission_dependency(permission)


async def require_permissions(permissions: List[str]):
    """
    Dependency for requiring multiple permissions.

    Args:
        permissions: List of required permissions

    Returns:
        Dependency function that checks all permissions
    """
    async def check_permissions(
        current_user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        for permission in permissions:
            await _require_permission(permission, current_user, db)
        return current_user

    return check_permissions


# Correlation ID dependency
async def get_correlation_id(request: Request = None) -> str:
    """
    Get or generate correlation ID for request tracking.

    Args:
        request: FastAPI request object

    Returns:
        str: Correlation ID for the request
    """
    # Try to get from context first
    current_correlation_id = correlation_id.get('')
    
    if current_correlation_id:
        return current_correlation_id
    
    # Try to get from request headers
    if request:
        request_correlation_id = request.headers.get('X-Correlation-ID')
        if request_correlation_id:
            correlation_id.set(request_correlation_id)
            return request_correlation_id
    
    # Generate new correlation ID
    new_correlation_id = str(uuid.uuid4())
    correlation_id.set(new_correlation_id)
    return new_correlation_id


# Cache management dependency
async def get_cache_manager():
    """
    Get initialized cache manager.

    Returns:
        CacheManager: Initialized cache manager instance
    """
    return await _get_cache_manager()


# Role-based dependencies
async def get_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Current admin user

    Raises:
        HTTPException: If user is not admin
    """
    return await get_current_admin_user(current_user)


async def get_vendor_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current vendor user.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Current vendor user

    Raises:
        HTTPException: If user is not vendor or higher
    """
    return await get_current_vendor_user(current_user)


# Optional authentication dependency
async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current authenticated user (optional).

    Args:
        credentials: HTTP Bearer token credentials (optional)
        db: Database session

    Returns:
        Optional[User]: Authenticated user instance or None
    """
    return await get_current_user_optional(credentials, db)


# Request validation dependencies
async def validate_request_size(request: Request) -> None:
    """
    Validate request size limits.

    Args:
        request: FastAPI request object

    Raises:
        HTTPException: If request is too large
    """
    content_length = request.headers.get('content-length')
    if content_length:
        content_length = int(content_length)
        max_size = 10 * 1024 * 1024  # 10MB limit
        if content_length > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Request too large. Maximum size: {max_size} bytes"
            )


# Rate limiting dependency (placeholder for future implementation)
async def rate_limit_check(request: Request) -> None:
    """
    Check rate limits for the request.

    Args:
        request: FastAPI request object

    Note:
        This is a placeholder for future rate limiting implementation
    """
    # TODO: Implement rate limiting logic
    pass


# Export all dependencies
__all__ = [
    "get_db",
    "get_current_user",
    "get_optional_user",
    "get_admin_user", 
    "get_vendor_user",
    "require_permission",
    "require_permissions",
    "get_correlation_id",
    "get_cache_manager",
    "validate_request_size",
    "rate_limit_check"
]
