"""Add booking workflow tables for Task 4.1.1

Revision ID: booking_workflow_tables
Revises: 035017169552
Create Date: 2025-01-24 12:00:00.000000

This migration creates comprehensive booking workflow tables including:
- Core booking model with complete lifecycle management
- Booking status history for audit trail
- Booking communication for vendor-customer messaging
- Booking modification for change requests
- Proper indexes and constraints for performance
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'booking_workflow_tables'
down_revision = '035017169552'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create booking workflow tables."""

    # Note: Enums are already created manually before running this migration

    # Create bookings table
    op.create_table(
        'bookings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Core booking information
        sa.Column('customer_id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('service_id', sa.Integer(), nullable=False),
        sa.Column('availability_id', sa.Integer(), nullable=True),

        # Booking reference and tracking
        sa.Column('booking_reference', sa.String(20), nullable=False),
        sa.Column('external_reference', sa.String(100), nullable=True),

        # Booking details
        sa.Column('booking_date', sa.Date(), nullable=False),
        sa.Column('booking_time', sa.Time(), nullable=True),
        sa.Column('duration_hours', sa.Numeric(4, 2), nullable=True),
        sa.Column('participant_count', sa.Integer(), nullable=False, default=1),

        # Status and workflow
        sa.Column('status', sa.String(50), nullable=False, default='pending'),
        sa.Column('priority', sa.String(20), nullable=False, default='normal'),

        # Vendor response management
        sa.Column('vendor_response_type', sa.String(50), nullable=True),
        sa.Column('vendor_response_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('vendor_response_deadline', sa.DateTime(timezone=True), nullable=True),
        sa.Column('vendor_notes', sa.Text(), nullable=True),

        # Customer requirements and preferences
        sa.Column('special_requirements', sa.Text(), nullable=True),
        sa.Column('customer_notes', sa.Text(), nullable=True),
        sa.Column('accessibility_requirements', sa.JSON(), nullable=True),

        # Pricing and payment
        sa.Column('base_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('additional_fees', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('discount_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('total_amount', sa.Numeric(10, 2), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='NGN'),
        sa.Column('commission_rate', sa.Numeric(5, 4), nullable=False, default=0.15),
        sa.Column('commission_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('vendor_payout', sa.Numeric(10, 2), nullable=False, default=0.00),

        # Payment tracking
        sa.Column('payment_status', sa.String(50), nullable=False, default='pending'),
        sa.Column('payment_method', sa.String(50), nullable=True),
        sa.Column('payment_reference', sa.String(100), nullable=True),
        sa.Column('paid_at', sa.DateTime(timezone=True), nullable=True),

        # Service delivery tracking
        sa.Column('service_start_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('service_end_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completion_confirmed_by_customer', sa.Boolean(), nullable=False, default=False),
        sa.Column('completion_confirmed_by_vendor', sa.Boolean(), nullable=False, default=False),

        # Communication and notifications
        sa.Column('last_communication_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('unread_messages_customer', sa.Integer(), nullable=False, default=0),
        sa.Column('unread_messages_vendor', sa.Integer(), nullable=False, default=0),
        sa.Column('customer_notified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('vendor_notified_at', sa.DateTime(timezone=True), nullable=True),

        # Cancellation and refund
        sa.Column('cancelled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('cancelled_by', sa.Integer(), nullable=True),
        sa.Column('cancellation_reason', sa.Text(), nullable=True),
        sa.Column('refund_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('refund_processed_at', sa.DateTime(timezone=True), nullable=True),

        # Review and rating
        sa.Column('customer_rating', sa.Numeric(3, 2), nullable=True),
        sa.Column('customer_review', sa.Text(), nullable=True),
        sa.Column('vendor_rating', sa.Numeric(3, 2), nullable=True),
        sa.Column('vendor_review', sa.Text(), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),

        # Metadata and tracking
        sa.Column('booking_source', sa.String(50), nullable=False, default='web'),
        sa.Column('user_agent', sa.String(500), nullable=True),
        sa.Column('ip_address', sa.String(45), nullable=True),
        sa.Column('booking_metadata', sa.JSON(), nullable=True),

        # Foreign key constraints
        sa.ForeignKeyConstraint(['customer_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['availability_id'], ['service_availability.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['cancelled_by'], ['users.id'], ondelete='SET NULL'),

        # Primary key and constraints
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('booking_reference'),

        # Check constraints
        sa.CheckConstraint('participant_count > 0', name='check_participant_count_positive'),
        sa.CheckConstraint('base_price >= 0', name='check_base_price_non_negative'),
        sa.CheckConstraint('total_amount >= 0', name='check_total_amount_non_negative'),
        sa.CheckConstraint('commission_rate >= 0.0 AND commission_rate <= 1.0', name='check_commission_rate_range'),
        sa.CheckConstraint('customer_rating IS NULL OR (customer_rating >= 1.0 AND customer_rating <= 5.0)', name='check_customer_rating_range'),
        sa.CheckConstraint('vendor_rating IS NULL OR (vendor_rating >= 1.0 AND vendor_rating <= 5.0)', name='check_vendor_rating_range'),
        sa.CheckConstraint('unread_messages_customer >= 0', name='check_unread_messages_customer_non_negative'),
        sa.CheckConstraint('unread_messages_vendor >= 0', name='check_unread_messages_vendor_non_negative'),
        sa.CheckConstraint("status IN ('pending', 'vendor_review', 'confirmed', 'payment_pending', 'paid', 'in_progress', 'completed', 'cancelled_by_customer', 'cancelled_by_vendor', 'rejected', 'refunded', 'disputed', 'no_show')", name='check_booking_status_valid'),
        sa.CheckConstraint("priority IN ('low', 'normal', 'high', 'urgent')", name='check_booking_priority_valid'),
        sa.CheckConstraint("vendor_response_type IS NULL OR vendor_response_type IN ('approved', 'rejected', 'modification_requested', 'counter_offer')", name='check_vendor_response_type_valid'),
    )

    # Create performance indexes for bookings table
    op.create_index('idx_booking_customer_status', 'bookings', ['customer_id', 'status'])
    op.create_index('idx_booking_vendor_status', 'bookings', ['vendor_id', 'status'])
    op.create_index('idx_booking_service_date', 'bookings', ['service_id', 'booking_date'])
    op.create_index('idx_booking_date_status', 'bookings', ['booking_date', 'status'])
    op.create_index('idx_booking_reference', 'bookings', ['booking_reference'])
    op.create_index('idx_booking_payment_status', 'bookings', ['payment_status', 'paid_at'])
    op.create_index('idx_booking_vendor_response', 'bookings', ['vendor_id', 'vendor_response_deadline'])
    op.create_index('idx_booking_priority_status', 'bookings', ['priority', 'status'])
    op.create_index('idx_booking_communication', 'bookings', ['last_communication_at', 'status'])
    op.create_index('idx_booking_completion', 'bookings', ['completion_confirmed_by_customer', 'completion_confirmed_by_vendor'])

    # Create booking_status_history table
    op.create_table(
        'booking_status_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Foreign key
        sa.Column('booking_id', sa.Integer(), nullable=False),

        # Status change details
        sa.Column('previous_status', sa.String(50), nullable=True),
        sa.Column('new_status', sa.String(50), nullable=False),
        sa.Column('changed_by', sa.Integer(), nullable=True),
        sa.Column('change_reason', sa.String(255), nullable=True),
        sa.Column('change_notes', sa.Text(), nullable=True),

        # System tracking
        sa.Column('automated_change', sa.Boolean(), nullable=False, default=False),
        sa.Column('system_trigger', sa.String(100), nullable=True),

        # Notification tracking
        sa.Column('customer_notified', sa.Boolean(), nullable=False, default=False),
        sa.Column('vendor_notified', sa.Boolean(), nullable=False, default=False),
        sa.Column('notification_sent_at', sa.DateTime(timezone=True), nullable=True),

        # Metadata
        sa.Column('change_metadata', sa.JSON(), nullable=True),

        # Foreign key constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['changed_by'], ['users.id'], ondelete='SET NULL'),

        # Primary key
        sa.PrimaryKeyConstraint('id'),
    )

    # Create indexes for booking_status_history
    op.create_index('idx_status_history_booking_date', 'booking_status_history', ['booking_id', 'created_at'])
    op.create_index('idx_status_history_status_date', 'booking_status_history', ['new_status', 'created_at'])
    op.create_index('idx_status_history_user_date', 'booking_status_history', ['changed_by', 'created_at'])

    # Create booking_communications table
    op.create_table(
        'booking_communications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Foreign key
        sa.Column('booking_id', sa.Integer(), nullable=False),

        # Communication details
        sa.Column('sender_id', sa.Integer(), nullable=True),
        sa.Column('recipient_id', sa.Integer(), nullable=True),
        sa.Column('communication_type', sa.String(50), nullable=False),
        sa.Column('subject', sa.String(255), nullable=True),
        sa.Column('message', sa.Text(), nullable=False),

        # Message status
        sa.Column('is_read', sa.Boolean(), nullable=False, default=False),
        sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_system_message', sa.Boolean(), nullable=False, default=False),
        sa.Column('requires_response', sa.Boolean(), nullable=False, default=False),
        sa.Column('response_deadline', sa.DateTime(timezone=True), nullable=True),

        # Attachments and media
        sa.Column('has_attachments', sa.Boolean(), nullable=False, default=False),
        sa.Column('attachment_urls', sa.JSON(), nullable=True),
        sa.Column('attachment_metadata', sa.JSON(), nullable=True),

        # Notification tracking
        sa.Column('email_sent', sa.Boolean(), nullable=False, default=False),
        sa.Column('push_sent', sa.Boolean(), nullable=False, default=False),
        sa.Column('sms_sent', sa.Boolean(), nullable=False, default=False),
        sa.Column('notification_sent_at', sa.DateTime(timezone=True), nullable=True),

        # Message metadata
        sa.Column('message_metadata', sa.JSON(), nullable=True),

        # Foreign key constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['sender_id'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['recipient_id'], ['users.id'], ondelete='SET NULL'),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Check constraints
        sa.CheckConstraint("communication_type IN ('message', 'system_notification', 'status_update', 'modification_request', 'payment_reminder', 'review_request')", name='check_communication_type_valid'),
    )

    # Create indexes for booking_communications
    op.create_index('idx_communication_booking_date', 'booking_communications', ['booking_id', 'created_at'])
    op.create_index('idx_communication_sender_date', 'booking_communications', ['sender_id', 'created_at'])
    op.create_index('idx_communication_recipient_unread', 'booking_communications', ['recipient_id', 'is_read'])
    op.create_index('idx_communication_type_date', 'booking_communications', ['communication_type', 'created_at'])
    op.create_index('idx_communication_system_date', 'booking_communications', ['is_system_message', 'created_at'])

    # Create booking_modifications table
    op.create_table(
        'booking_modifications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),

        # Foreign key
        sa.Column('booking_id', sa.Integer(), nullable=False),

        # Modification details
        sa.Column('requested_by', sa.Integer(), nullable=True),
        sa.Column('modification_type', sa.String(50), nullable=False),
        sa.Column('modification_reason', sa.Text(), nullable=True),

        # Original values
        sa.Column('original_values', sa.JSON(), nullable=False),

        # Requested changes
        sa.Column('requested_changes', sa.JSON(), nullable=False),

        # Approval workflow
        sa.Column('approval_status', sa.String(50), nullable=False, default='pending'),
        sa.Column('approved_by', sa.Integer(), nullable=True),
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('approval_notes', sa.Text(), nullable=True),

        # Implementation tracking
        sa.Column('implemented', sa.Boolean(), nullable=False, default=False),
        sa.Column('implemented_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('implemented_by', sa.Integer(), nullable=True),

        # Pricing impact
        sa.Column('price_impact', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('additional_fees', sa.Numeric(10, 2), nullable=False, default=0.00),

        # Notification tracking
        sa.Column('customer_notified', sa.Boolean(), nullable=False, default=False),
        sa.Column('vendor_notified', sa.Boolean(), nullable=False, default=False),

        # Metadata
        sa.Column('modification_metadata', sa.JSON(), nullable=True),

        # Foreign key constraints
        sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['requested_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['implemented_by'], ['users.id'], ondelete='SET NULL'),

        # Primary key
        sa.PrimaryKeyConstraint('id'),

        # Check constraints
        sa.CheckConstraint("modification_type IN ('date_change', 'time_change', 'participant_count', 'service_upgrade', 'special_requirements', 'pricing_adjustment')", name='check_modification_type_valid'),
    )

    # Create indexes for booking_modifications
    op.create_index('idx_modification_booking_date', 'booking_modifications', ['booking_id', 'created_at'])
    op.create_index('idx_modification_status_date', 'booking_modifications', ['approval_status', 'created_at'])
    op.create_index('idx_modification_type_date', 'booking_modifications', ['modification_type', 'created_at'])
    op.create_index('idx_modification_requested_by', 'booking_modifications', ['requested_by', 'created_at'])


def downgrade() -> None:
    """Drop booking workflow tables."""
    # Drop tables in reverse order
    op.drop_table('booking_modifications')
    op.drop_table('booking_communications')
    op.drop_table('booking_status_history')
    op.drop_table('bookings')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS booking_status')
    op.execute('DROP TYPE IF EXISTS vendor_response_type')
    op.execute('DROP TYPE IF EXISTS booking_priority')
    op.execute('DROP TYPE IF EXISTS communication_type')
    op.execute('DROP TYPE IF EXISTS modification_type')
