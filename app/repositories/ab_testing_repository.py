"""
A/B Testing Repository for Culture Connect Backend API.

This module provides comprehensive repository layer for A/B testing framework including:
- ABTestRepository: Core A/B test management and configuration
- ABTestAssignmentRepository: User assignment tracking with consistent hashing
- ABTestResultRepository: Test result collection and performance tracking
- ABTestAnalysisRepository: Statistical analysis and significance validation

Implements Phase 2.3 A/B Testing Framework requirements with production-grade
PostgreSQL optimization, statistical significance validation, and seamless integration
with existing geolocation analytics and VPN detection systems.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import select, update, delete, insert
from decimal import Decimal
import hashlib
import uuid
import logging

from app.repositories.base import BaseRepository, RepositoryError
from app.models.ab_testing import (
    ABTest, ABTestAssignment, ABTestResult, ABTestAnalysis,
    RoutingStrategy, ABTestStatus, ABTestType, StatisticalSignificance
)
from app.models.payment import Payment, PaymentStatus
from app.models.user import User

logger = logging.getLogger(__name__)

# Create exception classes for repository operations
class NotFoundError(RepositoryError):
    """Exception for resource not found errors."""
    pass

class ValidationError(RepositoryError):
    """Exception for validation errors."""
    pass


class ABTestRepository(BaseRepository[ABTest]):
    """
    Repository for A/B test management and configuration.

    Provides comprehensive A/B test lifecycle management including test creation,
    configuration updates, status management, and performance tracking with
    production-grade PostgreSQL optimization and <200ms query performance.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize A/B test repository."""
        super().__init__(ABTest, db_session)

    async def create_test(self, test_data: Dict[str, Any]) -> ABTest:
        """
        Create a new A/B test with comprehensive validation.

        Args:
            test_data: Test configuration data

        Returns:
            Created A/B test instance

        Performance Target: <100ms test creation
        """
        correlation = self.log_operation_start("create_test")

        try:
            # Validate test configuration
            await self._validate_test_configuration(test_data)

            # Generate randomization seed if not provided
            if not test_data.get('randomization_seed'):
                test_data['randomization_seed'] = hashlib.md5(
                    f"{test_data['test_name']}{datetime.utcnow().isoformat()}".encode()
                ).hexdigest()

            # Set default status
            test_data['status'] = test_data.get('status', ABTestStatus.DRAFT)

            # Create test instance
            test = ABTest(**test_data)
            self.db_session.add(test)
            await self.db_session.flush()
            await self.db_session.refresh(test)

            self.log_operation_success(correlation, f"Created A/B test: {test.test_name}")
            return test

        except Exception as e:
            await self.handle_repository_error(e, "create_test", test_data)

    async def get_active_tests(
        self,
        country_code: Optional[str] = None,
        exclude_vpn_users: Optional[bool] = None
    ) -> List[ABTest]:
        """
        Get all active A/B tests with optional filtering.

        Args:
            country_code: Optional country code filter
            exclude_vpn_users: Optional VPN user exclusion filter

        Returns:
            List of active A/B tests

        Performance Target: <50ms for active test retrieval
        """
        correlation = self.log_operation_start("get_active_tests")

        try:
            query = select(ABTest).where(
                and_(
                    ABTest.status == ABTestStatus.ACTIVE,
                    or_(
                        ABTest.start_date.is_(None),
                        ABTest.start_date <= func.now()
                    ),
                    or_(
                        ABTest.end_date.is_(None),
                        ABTest.end_date >= func.now()
                    )
                )
            )

            # Apply country filter
            if country_code:
                query = query.where(
                    or_(
                        ABTest.target_countries.is_(None),
                        ABTest.target_countries.contains([country_code])
                    )
                ).where(
                    or_(
                        ABTest.exclude_countries.is_(None),
                        ~ABTest.exclude_countries.contains([country_code])
                    )
                )

            # Apply VPN filter
            if exclude_vpn_users is not None:
                query = query.where(ABTest.exclude_vpn_users == exclude_vpn_users)

            # Order by priority (newer tests first)
            query = query.order_by(desc(ABTest.created_at))

            result = await self.db_session.execute(query)
            tests = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(tests)} active tests")
            return tests

        except Exception as e:
            await self.handle_repository_error(e, "get_active_tests", {
                "country_code": country_code,
                "exclude_vpn_users": exclude_vpn_users
            })

    async def update_test_status(self, test_id: uuid.UUID, status: ABTestStatus) -> ABTest:
        """
        Update A/B test status with validation.

        Args:
            test_id: Test identifier
            status: New test status

        Returns:
            Updated test instance

        Performance Target: <50ms status update
        """
        correlation = self.log_operation_start("update_test_status")

        try:
            test = await self.get_by_id(test_id)
            if not test:
                raise NotFoundError(f"A/B test not found: {test_id}")

            # Validate status transition
            await self._validate_status_transition(test.status, status)

            # Update status and related fields
            test.status = status
            if status == ABTestStatus.ACTIVE and not test.start_date:
                test.start_date = func.now()
            elif status in [ABTestStatus.COMPLETED, ABTestStatus.CANCELLED]:
                test.end_date = func.now()

            await self.db_session.flush()
            await self.db_session.refresh(test)

            self.log_operation_success(correlation, f"Updated test status: {test.test_name} -> {status}")
            return test

        except Exception as e:
            await self.handle_repository_error(e, "update_test_status", {
                "test_id": str(test_id),
                "status": status
            })

    async def get_tests_by_strategy(
        self,
        strategy: RoutingStrategy,
        status: Optional[ABTestStatus] = None
    ) -> List[ABTest]:
        """
        Get A/B tests by routing strategy.

        Args:
            strategy: Routing strategy to filter by
            status: Optional status filter

        Returns:
            List of matching tests

        Performance Target: <100ms strategy-based retrieval
        """
        correlation = self.log_operation_start("get_tests_by_strategy")

        try:
            query = select(ABTest).where(
                or_(
                    ABTest.strategy_a == strategy,
                    ABTest.strategy_b == strategy
                )
            )

            if status:
                query = query.where(ABTest.status == status)

            query = query.order_by(desc(ABTest.created_at))

            result = await self.db_session.execute(query)
            tests = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(tests)} tests for strategy: {strategy}")
            return tests

        except Exception as e:
            await self.handle_repository_error(e, "get_tests_by_strategy", {
                "strategy": strategy,
                "status": status
            })

    async def update_test_metrics(
        self,
        test_id: uuid.UUID,
        sample_size_a: int,
        sample_size_b: int,
        conversion_rate_a: float,
        conversion_rate_b: float
    ) -> ABTest:
        """
        Update A/B test performance metrics.

        Args:
            test_id: Test identifier
            sample_size_a: Sample size for group A
            sample_size_b: Sample size for group B
            conversion_rate_a: Conversion rate for group A
            conversion_rate_b: Conversion rate for group B

        Returns:
            Updated test instance

        Performance Target: <50ms metrics update
        """
        correlation = self.log_operation_start("update_test_metrics")

        try:
            test = await self.get_by_id(test_id)
            if not test:
                raise NotFoundError(f"A/B test not found: {test_id}")

            # Update metrics
            test.current_sample_size_a = sample_size_a
            test.current_sample_size_b = sample_size_b
            test.current_conversion_rate_a = conversion_rate_a
            test.current_conversion_rate_b = conversion_rate_b

            await self.db_session.flush()
            await self.db_session.refresh(test)

            self.log_operation_success(correlation, f"Updated metrics for test: {test.test_name}")
            return test

        except Exception as e:
            await self.handle_repository_error(e, "update_test_metrics", {
                "test_id": str(test_id),
                "sample_size_a": sample_size_a,
                "sample_size_b": sample_size_b
            })

    async def get_tests_requiring_analysis(self) -> List[ABTest]:
        """
        Get tests that require statistical analysis.

        Returns:
            List of tests requiring analysis

        Performance Target: <100ms analysis queue retrieval
        """
        correlation = self.log_operation_start("get_tests_requiring_analysis")

        try:
            # Tests that are active and have sufficient sample size
            query = select(ABTest).where(
                and_(
                    ABTest.status == ABTestStatus.ACTIVE,
                    ABTest.current_sample_size_a >= ABTest.min_sample_size,
                    ABTest.current_sample_size_b >= ABTest.min_sample_size,
                    or_(
                        ABTest.last_analysis_date.is_(None),
                        ABTest.last_analysis_date < func.now() - text("INTERVAL '1 hour'")
                    )
                )
            )

            result = await self.db_session.execute(query)
            tests = result.scalars().all()

            self.log_operation_success(correlation, f"Found {len(tests)} tests requiring analysis")
            return tests

        except Exception as e:
            await self.handle_repository_error(e, "get_tests_requiring_analysis", {})

    # Helper methods
    async def _validate_test_configuration(self, test_data: Dict[str, Any]) -> None:
        """Validate A/B test configuration."""
        # Check for duplicate test names
        existing_test = await self.db_session.execute(
            select(ABTest).where(ABTest.test_name == test_data.get('test_name'))
        )
        if existing_test.scalar_one_or_none():
            raise ValidationError(f"Test name already exists: {test_data.get('test_name')}")

        # Validate strategies are different
        if test_data.get('strategy_a') == test_data.get('strategy_b'):
            raise ValidationError("Strategy A and Strategy B must be different")

        # Validate traffic split
        traffic_split = test_data.get('traffic_split', 0.5)
        if not 0.1 <= traffic_split <= 0.9:
            raise ValidationError("Traffic split must be between 0.1 and 0.9")

        # Validate date range
        start_date = test_data.get('start_date')
        end_date = test_data.get('end_date')
        if start_date and end_date and start_date >= end_date:
            raise ValidationError("Start date must be before end date")

    async def _validate_status_transition(self, current_status: ABTestStatus, new_status: ABTestStatus) -> None:
        """Validate A/B test status transitions."""
        valid_transitions = {
            ABTestStatus.DRAFT: [ABTestStatus.ACTIVE, ABTestStatus.CANCELLED],
            ABTestStatus.ACTIVE: [ABTestStatus.PAUSED, ABTestStatus.COMPLETED, ABTestStatus.CANCELLED],
            ABTestStatus.PAUSED: [ABTestStatus.ACTIVE, ABTestStatus.CANCELLED],
            ABTestStatus.COMPLETED: [],  # Terminal state
            ABTestStatus.CANCELLED: []   # Terminal state
        }

        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(f"Invalid status transition: {current_status} -> {new_status}")


class ABTestAssignmentRepository(BaseRepository[ABTestAssignment]):
    """
    Repository for A/B test user assignment tracking with consistent hashing.

    Provides user assignment management with consistent hashing algorithms,
    assignment persistence, and audit trail for test participation with
    production-grade performance and <50ms assignment retrieval.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize A/B test assignment repository."""
        super().__init__(ABTestAssignment, db_session)

    async def get_or_create_assignment(
        self,
        test_id: uuid.UUID,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        country_code: Optional[str] = None,
        is_vpn_detected: bool = False
    ) -> ABTestAssignment:
        """
        Get existing assignment or create new one with consistent hashing.

        Args:
            test_id: Test identifier
            user_id: User identifier (optional for anonymous users)
            session_id: Session identifier for anonymous tracking
            ip_address: User IP address
            user_agent: User agent string
            country_code: User's country code
            is_vpn_detected: Whether VPN was detected

        Returns:
            User assignment for the test

        Performance Target: <50ms assignment retrieval/creation
        """
        correlation = self.log_operation_start("get_or_create_assignment")

        try:
            # Check for existing assignment
            query = select(ABTestAssignment).where(ABTestAssignment.test_id == test_id)

            if user_id:
                query = query.where(ABTestAssignment.user_id == user_id)
            elif session_id:
                query = query.where(ABTestAssignment.session_id == session_id)
            else:
                raise ValidationError("Either user_id or session_id must be provided")

            result = await self.db_session.execute(query)
            existing_assignment = result.scalar_one_or_none()

            if existing_assignment:
                self.log_operation_success(correlation, f"Retrieved existing assignment: {existing_assignment.id}")
                return existing_assignment

            # Get test configuration for assignment
            test_query = select(ABTest).where(ABTest.id == test_id)
            test_result = await self.db_session.execute(test_query)
            test = test_result.scalar_one_or_none()

            if not test:
                raise NotFoundError(f"A/B test not found: {test_id}")

            # Generate consistent assignment
            assignment_data = await self._generate_consistent_assignment(
                test, user_id, session_id, ip_address, user_agent, country_code, is_vpn_detected
            )

            # Create assignment
            assignment = ABTestAssignment(**assignment_data)
            self.db_session.add(assignment)
            await self.db_session.flush()
            await self.db_session.refresh(assignment)

            self.log_operation_success(correlation, f"Created new assignment: {assignment.id}")
            return assignment

        except Exception as e:
            await self.handle_repository_error(e, "get_or_create_assignment", {
                "test_id": str(test_id),
                "user_id": user_id,
                "session_id": session_id
            })

    async def get_user_assignments(
        self,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        active_only: bool = True
    ) -> List[ABTestAssignment]:
        """
        Get all assignments for a user or session.

        Args:
            user_id: User identifier
            session_id: Session identifier
            active_only: Whether to return only active test assignments

        Returns:
            List of user assignments

        Performance Target: <100ms user assignment retrieval
        """
        correlation = self.log_operation_start("get_user_assignments")

        try:
            query = select(ABTestAssignment)

            if user_id:
                query = query.where(ABTestAssignment.user_id == user_id)
            elif session_id:
                query = query.where(ABTestAssignment.session_id == session_id)
            else:
                raise ValidationError("Either user_id or session_id must be provided")

            if active_only:
                query = query.join(ABTest).where(ABTest.status == ABTestStatus.ACTIVE)

            query = query.options(joinedload(ABTestAssignment.test))
            query = query.order_by(desc(ABTestAssignment.assignment_date))

            result = await self.db_session.execute(query)
            assignments = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(assignments)} assignments")
            return assignments

        except Exception as e:
            await self.handle_repository_error(e, "get_user_assignments", {
                "user_id": user_id,
                "session_id": session_id,
                "active_only": active_only
            })

    async def get_test_assignments(
        self,
        test_id: uuid.UUID,
        group: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[ABTestAssignment]:
        """
        Get assignments for a specific test.

        Args:
            test_id: Test identifier
            group: Optional group filter ('A' or 'B')
            limit: Optional result limit

        Returns:
            List of test assignments

        Performance Target: <150ms test assignment retrieval
        """
        correlation = self.log_operation_start("get_test_assignments")

        try:
            query = select(ABTestAssignment).where(ABTestAssignment.test_id == test_id)

            if group:
                query = query.where(ABTestAssignment.assigned_group == group)

            query = query.order_by(desc(ABTestAssignment.assignment_date))

            if limit:
                query = query.limit(limit)

            result = await self.db_session.execute(query)
            assignments = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(assignments)} test assignments")
            return assignments

        except Exception as e:
            await self.handle_repository_error(e, "get_test_assignments", {
                "test_id": str(test_id),
                "group": group,
                "limit": limit
            })

    async def get_assignment_statistics(self, test_id: uuid.UUID) -> Dict[str, Any]:
        """
        Get assignment statistics for a test.

        Args:
            test_id: Test identifier

        Returns:
            Assignment statistics

        Performance Target: <100ms statistics calculation
        """
        correlation = self.log_operation_start("get_assignment_statistics")

        try:
            # Get assignment counts by group
            query = select(
                ABTestAssignment.assigned_group,
                func.count(ABTestAssignment.id).label('count'),
                func.count(ABTestAssignment.user_id).label('registered_users'),
                func.count(ABTestAssignment.session_id).label('anonymous_users')
            ).where(
                ABTestAssignment.test_id == test_id
            ).group_by(ABTestAssignment.assigned_group)

            result = await self.db_session.execute(query)
            group_stats = result.all()

            # Get VPN statistics
            vpn_query = select(
                ABTestAssignment.is_vpn_detected,
                func.count(ABTestAssignment.id).label('count')
            ).where(
                ABTestAssignment.test_id == test_id
            ).group_by(ABTestAssignment.is_vpn_detected)

            vpn_result = await self.db_session.execute(vpn_query)
            vpn_stats = vpn_result.all()

            # Get country distribution
            country_query = select(
                ABTestAssignment.country_code,
                func.count(ABTestAssignment.id).label('count')
            ).where(
                ABTestAssignment.test_id == test_id
            ).group_by(ABTestAssignment.country_code).order_by(desc('count')).limit(10)

            country_result = await self.db_session.execute(country_query)
            country_stats = country_result.all()

            statistics = {
                "group_distribution": {row.assigned_group: row.count for row in group_stats},
                "user_types": {
                    "registered": sum(row.registered_users for row in group_stats),
                    "anonymous": sum(row.anonymous_users for row in group_stats)
                },
                "vpn_distribution": {row.is_vpn_detected: row.count for row in vpn_stats},
                "top_countries": [{"country": row.country_code, "count": row.count} for row in country_stats]
            }

            self.log_operation_success(correlation, f"Generated assignment statistics for test: {test_id}")
            return statistics

        except Exception as e:
            await self.handle_repository_error(e, "get_assignment_statistics", {
                "test_id": str(test_id)
            })

    # Helper methods
    async def _generate_consistent_assignment(
        self,
        test: ABTest,
        user_id: Optional[int],
        session_id: Optional[str],
        ip_address: Optional[str],
        user_agent: Optional[str],
        country_code: Optional[str],
        is_vpn_detected: bool
    ) -> Dict[str, Any]:
        """Generate consistent assignment using hashing algorithm."""
        # Create hash input
        hash_input = f"{test.id}:{user_id or session_id}:{test.randomization_seed}"
        assignment_hash = hashlib.md5(hash_input.encode()).hexdigest()

        # Convert hash to float between 0 and 1
        assignment_value = int(assignment_hash[:8], 16) / (2**32 - 1)

        # Determine group assignment based on traffic split
        assigned_group = 'A' if assignment_value < test.traffic_split else 'B'
        assigned_strategy = test.strategy_a if assigned_group == 'A' else test.strategy_b

        return {
            "test_id": test.id,
            "user_id": user_id,
            "session_id": session_id,
            "assigned_group": assigned_group,
            "assigned_strategy": assigned_strategy,
            "assignment_hash": assignment_hash[:32],
            "assignment_value": assignment_value,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "country_code": country_code,
            "is_vpn_detected": is_vpn_detected,
            "assignment_metadata": {
                "hash_algorithm": "md5",
                "traffic_split": test.traffic_split,
                "assignment_timestamp": datetime.utcnow().isoformat()
            }
        }
