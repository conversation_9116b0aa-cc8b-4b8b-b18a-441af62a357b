"""
API endpoint tests for WebSocket functionality.

This module provides comprehensive API endpoint tests for WebSocket infrastructure including:
- HTTP endpoint testing with authentication
- WebSocket endpoint testing with real connections
- Error handling and validation testing
- Performance validation for API responses
- Integration testing with FastAPI test client

Implements Task 6.1.1 Phase 5 requirements with comprehensive endpoint testing.
"""

import pytest
import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import FastAPI, status
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient

from app.api.v1.endpoints.websocket_endpoints import router, connection_manager
from app.schemas.websocket_schemas import PresenceStatusEnum


@pytest.mark.api
class TestWebSocketHTTPEndpoints:
    """API tests for WebSocket HTTP endpoints."""
    
    @pytest.fixture
    def test_app(self):
        """Create test FastAPI app with WebSocket endpoints."""
        app = FastAPI()
        app.include_router(router, prefix="/websocket")
        return app
    
    @pytest.fixture
    async def async_client(self, test_app):
        """Create async test client."""
        async with Async<PERSON><PERSON>(app=test_app, base_url="http://test") as client:
            yield client
    
    async def test_get_user_connections_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful retrieval of user connections."""
        # Arrange
        mock_connections = [
            MagicMock(connection_id="conn_1", status="connected", is_active=True),
            MagicMock(connection_id="conn_2", status="authenticated", is_active=True)
        ]
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                    mock_service.return_value.get_user_connections = AsyncMock(return_value=mock_connections)
                    
                    # Act
                    performance_timer.start()
                    response = await async_client.get("/websocket/connections")
                    performance_timer.stop()
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert len(response_data) == 2
                    performance_timer.assert_under_ms(200, "Get connections endpoint")
    
    async def test_get_user_connections_unauthorized(self, async_client):
        """Test unauthorized access to user connections."""
        # Act
        response = await async_client.get("/websocket/connections")
        
        # Assert
        assert response.status_code == 401
    
    async def test_disconnect_connection_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful connection disconnection."""
        # Arrange
        connection_id = "test_conn_123"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                    mock_service.return_value.disconnect_connection = AsyncMock(return_value=True)
                    
                    # Act
                    performance_timer.start()
                    response = await async_client.post(f"/websocket/connections/{connection_id}/disconnect")
                    performance_timer.stop()
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert "disconnected successfully" in response_data["message"]
                    performance_timer.assert_under_ms(200, "Disconnect connection endpoint")
    
    async def test_disconnect_connection_not_found(
        self, 
        async_client,
        mock_websocket_user
    ):
        """Test disconnection of non-existent connection."""
        # Arrange
        connection_id = "nonexistent_conn"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                    mock_service.return_value.disconnect_connection = AsyncMock(return_value=False)
                    
                    # Act
                    response = await async_client.post(f"/websocket/connections/{connection_id}/disconnect")
                    
                    # Assert
                    assert response.status_code == 404
                    response_data = response.json()
                    assert "not found" in response_data["detail"]
    
    async def test_get_my_presence_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful retrieval of user presence."""
        # Arrange
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.status = "online"
        mock_presence.active_connections_count = 2
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.UserPresenceService') as mock_service:
                    mock_service.return_value.get_user_presence = AsyncMock(return_value=mock_presence)
                    
                    # Mock connection manager
                    with patch('app.api.v1.endpoints.websocket_endpoints.connection_manager') as mock_manager:
                        mock_manager.get_user_connection_count.return_value = 2
                        
                        # Act
                        performance_timer.start()
                        response = await async_client.get("/websocket/presence/me")
                        performance_timer.stop()
                        
                        # Assert
                        assert response.status_code == 200
                        performance_timer.assert_under_ms(200, "Get presence endpoint")
    
    async def test_update_my_presence_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful presence status update."""
        # Arrange
        new_status = "away"
        
        mock_presence = MagicMock()
        mock_presence.user_id = mock_websocket_user.id
        mock_presence.status = new_status
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.UserPresenceService') as mock_service:
                    mock_service.return_value.update_user_status = AsyncMock(return_value=mock_presence)
                    
                    # Mock connection manager
                    with patch('app.api.v1.endpoints.websocket_endpoints.connection_manager') as mock_manager:
                        mock_manager.user_connections = {mock_websocket_user.id: {"conn_1"}}
                        mock_manager.broadcast = AsyncMock(return_value=1)
                        
                        # Act
                        performance_timer.start()
                        response = await async_client.put(f"/websocket/presence/me?status={new_status}")
                        performance_timer.stop()
                        
                        # Assert
                        assert response.status_code == 200
                        response_data = response.json()
                        assert response_data["status"] == new_status
                        performance_timer.assert_under_ms(100, "Update presence endpoint")
    
    async def test_get_online_users_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful retrieval of online users."""
        # Arrange
        mock_online_users = [
            MagicMock(user_id=1, status="online", active_connections_count=1),
            MagicMock(user_id=2, status="online", active_connections_count=2),
            MagicMock(user_id=3, status="away", active_connections_count=1)
        ]
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.UserPresenceService') as mock_service:
                    mock_service.return_value.get_online_users = AsyncMock(return_value=mock_online_users)
                    
                    # Mock connection manager
                    with patch('app.api.v1.endpoints.websocket_endpoints.connection_manager') as mock_manager:
                        mock_manager.get_user_connection_count.side_effect = [1, 2, 1]
                        
                        # Act
                        performance_timer.start()
                        response = await async_client.get("/websocket/presence/online?limit=50")
                        performance_timer.stop()
                        
                        # Assert
                        assert response.status_code == 200
                        response_data = response.json()
                        assert len(response_data) == 3
                        performance_timer.assert_under_ms(200, "Get online users endpoint")
    
    async def test_get_pending_events_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful retrieval of pending events."""
        # Arrange
        mock_events = [
            MagicMock(event_id="evt_1", event_type="message.sent", priority="normal"),
            MagicMock(event_id="evt_2", event_type="presence.updated", priority="high")
        ]
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketEventService') as mock_service:
                    mock_service.return_value.get_pending_events = AsyncMock(return_value=mock_events)
                    
                    # Act
                    performance_timer.start()
                    response = await async_client.get("/websocket/events?limit=100")
                    performance_timer.stop()
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert len(response_data) == 2
                    performance_timer.assert_under_ms(200, "Get pending events endpoint")
    
    async def test_acknowledge_event_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful event acknowledgment."""
        # Arrange
        event_id = "evt_123"
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketEventService') as mock_service:
                    mock_service.return_value.mark_event_acknowledged = AsyncMock(return_value=True)
                    
                    # Act
                    performance_timer.start()
                    response = await async_client.post(f"/websocket/events/{event_id}/acknowledge")
                    performance_timer.stop()
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert "acknowledged successfully" in response_data["message"]
                    performance_timer.assert_under_ms(100, "Acknowledge event endpoint")
    
    async def test_get_metrics_summary_success(
        self, 
        async_client,
        mock_websocket_user,
        performance_timer
    ):
        """Test successful metrics summary retrieval."""
        # Arrange
        mock_summary = {
            "avg_message_delivery_time_ms": 75.0,
            "peak_concurrent_connections": 100,
            "total_messages_processed": 1000
        }
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketMetricsService') as mock_service:
                    mock_service.return_value.get_performance_summary = AsyncMock(return_value=mock_summary)
                    
                    # Mock connection manager
                    with patch('app.api.v1.endpoints.websocket_endpoints.connection_manager') as mock_manager:
                        mock_manager.get_connection_count.return_value = 50
                        mock_manager.get_online_users.return_value = [1, 2, 3]
                        
                        # Act
                        performance_timer.start()
                        response = await async_client.get("/websocket/metrics/summary?hours=24")
                        performance_timer.stop()
                        
                        # Assert
                        assert response.status_code == 200
                        response_data = response.json()
                        assert "avg_message_delivery_time_ms" in response_data
                        assert "real_time" in response_data
                        performance_timer.assert_under_ms(200, "Get metrics summary endpoint")
    
    async def test_websocket_health_check_success(
        self, 
        async_client,
        performance_timer
    ):
        """Test WebSocket health check endpoint."""
        # Arrange
        with patch('app.api.v1.endpoints.websocket_endpoints.connection_manager') as mock_manager:
            mock_manager.get_connection_count.return_value = 25
            mock_manager.get_online_users.return_value = [1, 2, 3, 4, 5]
            
            # Act
            performance_timer.start()
            response = await async_client.get("/websocket/health")
            performance_timer.stop()
            
            # Assert
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "healthy"
            assert response_data["metrics"]["active_connections"] == 25
            assert response_data["metrics"]["online_users"] == 5
            assert "performance" in response_data
            performance_timer.assert_under_ms(100, "Health check endpoint")
    
    async def test_admin_cleanup_stale_connections(
        self, 
        async_client,
        mock_admin_user
    ):
        """Test admin endpoint for cleaning up stale connections."""
        # Arrange
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_admin_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                    mock_service.return_value.cleanup_stale_connections = AsyncMock(return_value=5)
                    
                    # Act
                    response = await async_client.post("/websocket/admin/connections/cleanup?timeout_minutes=30")
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert response_data["cleaned_count"] == 5
                    assert response_data["timeout_minutes"] == 30
    
    async def test_admin_get_daily_metrics(
        self, 
        async_client,
        mock_admin_user,
        performance_timer
    ):
        """Test admin endpoint for getting daily metrics."""
        # Arrange
        mock_daily_metrics = {
            "total_connections": 500,
            "peak_active_connections": 100,
            "total_messages_sent": 10000
        }
        
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_admin_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketMetricsService') as mock_service:
                    mock_service.return_value.get_daily_aggregates = AsyncMock(return_value=mock_daily_metrics)
                    
                    # Act
                    performance_timer.start()
                    response = await async_client.get("/websocket/admin/metrics/daily?date=2025-01-15")
                    performance_timer.stop()
                    
                    # Assert
                    assert response.status_code == 200
                    response_data = response.json()
                    assert response_data["total_connections"] == 500
                    performance_timer.assert_under_ms(200, "Get daily metrics endpoint")
    
    async def test_invalid_date_format_error(
        self, 
        async_client,
        mock_admin_user
    ):
        """Test error handling for invalid date format."""
        # Arrange
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_admin_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                # Act
                response = await async_client.get("/websocket/admin/metrics/daily?date=invalid-date")
                
                # Assert
                assert response.status_code == 400
                response_data = response.json()
                assert "Invalid date format" in response_data["detail"]


@pytest.mark.api
class TestWebSocketErrorHandling:
    """API tests for WebSocket error handling."""
    
    @pytest.fixture
    def test_app(self):
        """Create test FastAPI app with WebSocket endpoints."""
        app = FastAPI()
        app.include_router(router, prefix="/websocket")
        return app
    
    @pytest.fixture
    async def async_client(self, test_app):
        """Create async test client."""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            yield client
    
    async def test_service_error_handling(
        self, 
        async_client,
        mock_websocket_user
    ):
        """Test proper error handling when services fail."""
        # Arrange
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            with patch('app.api.v1.endpoints.websocket_endpoints.get_db_session') as mock_db:
                mock_db.return_value = AsyncMock()
                
                with patch('app.api.v1.endpoints.websocket_endpoints.WebSocketConnectionService') as mock_service:
                    mock_service.return_value.get_user_connections = AsyncMock(
                        side_effect=Exception("Database connection failed")
                    )
                    
                    # Act
                    response = await async_client.get("/websocket/connections")
                    
                    # Assert
                    assert response.status_code == 500
                    response_data = response.json()
                    assert "Failed to retrieve connections" in response_data["detail"]
    
    async def test_validation_error_handling(
        self, 
        async_client,
        mock_websocket_user
    ):
        """Test handling of validation errors."""
        # Arrange
        with patch('app.api.v1.endpoints.websocket_endpoints.get_current_user') as mock_auth:
            mock_auth.return_value = mock_websocket_user
            
            # Act - Invalid query parameter
            response = await async_client.get("/websocket/presence/online?limit=9999")
            
            # Assert - Should handle validation error
            assert response.status_code in [400, 422]  # Validation error
