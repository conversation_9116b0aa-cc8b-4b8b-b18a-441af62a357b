"""
Enhanced Base Repository for Culture Connect Backend API - Part 2.

This module contains the advanced query methods, pagination, filtering,
and bulk operations for the repository pattern implementation.
"""

import base64
import json
from typing import List, Optional, Dict, Any, Union, Tuple

from sqlalchemy import select, func, and_, or_, text, update, delete
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.dialects.postgresql import insert

from .base import (
    BaseRepository, ModelType, PaginationParams, CursorPaginationParams,
    SortParams, FilterParams, QueryResult, CursorQueryResult,
    RepositoryError, time, logger
)


class EnhancedBaseRepository(BaseRepository[ModelType]):
    """
    Enhanced repository with advanced query capabilities.

    Extends BaseRepository with:
    - Advanced pagination (offset and cursor-based)
    - Dynamic filtering and sorting
    - Query optimization patterns
    - Bulk operations with performance optimization
    """

    async def create(self, obj_in: Dict[str, Any]) -> ModelType:
        """
        Create a new record with performance tracking.

        Args:
            obj_in: Dictionary with field values

        Returns:
            Created model instance

        Raises:
            RepositoryError: If creation fails
        """
        start_time = time.time()

        try:
            db_obj = self.model(**obj_in)
            self.db.add(db_obj)
            await self.db.commit()
            await self.db.refresh(db_obj)

            query_time = time.time() - start_time
            self._track_query_performance("create", query_time, 1)

            return db_obj

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("create_error", query_time, 0)
            raise RepositoryError(f"Failed to create {self.model.__name__}", e)

    async def update(
        self,
        id: int,
        obj_in: Dict[str, Any],
        exclude: Optional[List[str]] = None
    ) -> Optional[ModelType]:
        """
        Update an existing record with performance tracking.

        Args:
            id: Record ID
            obj_in: Dictionary with field values to update
            exclude: List of field names to exclude from update

        Returns:
            Updated model instance if found, None otherwise

        Raises:
            RepositoryError: If update fails
        """
        start_time = time.time()

        try:
            db_obj = await self.get(id)
            if not db_obj:
                return None

            exclude = exclude or ['id', 'uuid', 'created_at']

            for field, value in obj_in.items():
                if field not in exclude and hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            await self.db.commit()
            await self.db.refresh(db_obj)

            query_time = time.time() - start_time
            self._track_query_performance("update", query_time, 1)

            return db_obj

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("update_error", query_time, 0)
            raise RepositoryError(f"Failed to update {self.model.__name__} with id {id}", e)

    async def delete(self, id: int) -> bool:
        """
        Delete a record by ID with performance tracking.

        Args:
            id: Record ID

        Returns:
            True if record was deleted, False if not found

        Raises:
            RepositoryError: If deletion fails
        """
        start_time = time.time()

        try:
            db_obj = await self.get(id)
            if not db_obj:
                return False

            await self.db.delete(db_obj)
            await self.db.commit()

            query_time = time.time() - start_time
            self._track_query_performance("delete", query_time, 1)

            return True

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("delete_error", query_time, 0)
            raise RepositoryError(f"Failed to delete {self.model.__name__} with id {id}", e)

    async def count(self, filters: Optional[List[FilterParams]] = None) -> int:
        """
        Count records with optional filters and performance tracking.

        Args:
            filters: Optional list of filter parameters

        Returns:
            Number of records matching criteria

        Raises:
            RepositoryError: If count operation fails
        """
        start_time = time.time()

        try:
            stmt = select(func.count(self.model.id))

            if filters:
                conditions = self._build_filter_conditions(filters)
                if conditions is not None:
                    stmt = stmt.where(conditions)

            result = await self.db.execute(stmt)
            count = result.scalar()

            query_time = time.time() - start_time
            self._track_query_performance("count", query_time, count)

            return count

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("count_error", query_time, 0)
            raise RepositoryError(f"Failed to count {self.model.__name__} records", e)

    async def exists(self, id: int) -> bool:
        """
        Check if a record exists by ID with performance tracking.

        Args:
            id: Record ID

        Returns:
            True if record exists, False otherwise
        """
        start_time = time.time()

        try:
            stmt = select(func.count(self.model.id)).where(self.model.id == id)
            result = await self.db.execute(stmt)
            exists = result.scalar() > 0

            query_time = time.time() - start_time
            self._track_query_performance("exists", query_time, 1 if exists else 0)

            return exists

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("exists_error", query_time, 0)
            raise RepositoryError(f"Failed to check existence of {self.model.__name__} with id {id}", e)

    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """
        Get a record by field value with performance tracking.

        Args:
            field: Field name to search by
            value: Field value to search for

        Returns:
            Model instance if found, None otherwise

        Raises:
            RepositoryError: If query fails
        """
        start_time = time.time()

        try:
            if not hasattr(self.model, field):
                return None

            stmt = select(self.model).where(getattr(self.model, field) == value)
            result = await self.db.execute(stmt)
            instance = result.scalar_one_or_none()

            query_time = time.time() - start_time
            self._track_query_performance("get_by_field", query_time, 1 if instance else 0)

            return instance

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_by_field_error", query_time, 0)
            raise RepositoryError(f"Failed to get {self.model.__name__} by {field}={value}", e)

    def _build_filter_conditions(self, filters: List[FilterParams]):
        """
        Build SQLAlchemy filter conditions from filter parameters.

        Args:
            filters: List of filter parameters

        Returns:
            SQLAlchemy condition expression or None
        """
        if not filters:
            return None

        conditions = []

        for filter_param in filters:
            if not hasattr(self.model, filter_param.field):
                continue

            field = getattr(self.model, filter_param.field)

            if filter_param.operator == "eq":
                conditions.append(field == filter_param.value)
            elif filter_param.operator == "ne":
                conditions.append(field != filter_param.value)
            elif filter_param.operator == "gt":
                conditions.append(field > filter_param.value)
            elif filter_param.operator == "gte":
                conditions.append(field >= filter_param.value)
            elif filter_param.operator == "lt":
                conditions.append(field < filter_param.value)
            elif filter_param.operator == "lte":
                conditions.append(field <= filter_param.value)
            elif filter_param.operator == "like":
                conditions.append(field.like(f"%{filter_param.value}%"))
            elif filter_param.operator == "ilike":
                conditions.append(field.ilike(f"%{filter_param.value}%"))
            elif filter_param.operator == "in":
                if isinstance(filter_param.value, (list, tuple)):
                    conditions.append(field.in_(filter_param.value))
            elif filter_param.operator == "not_in":
                if isinstance(filter_param.value, (list, tuple)):
                    conditions.append(~field.in_(filter_param.value))

        if not conditions:
            return None

        return and_(*conditions) if len(conditions) > 1 else conditions[0]

    def _build_sort_conditions(self, sorts: List[SortParams]):
        """
        Build SQLAlchemy sort conditions from sort parameters.

        Args:
            sorts: List of sort parameters

        Returns:
            List of SQLAlchemy order expressions
        """
        order_expressions = []

        for sort_param in sorts:
            if not hasattr(self.model, sort_param.field):
                continue

            field = getattr(self.model, sort_param.field)

            if sort_param.direction == "desc":
                order_expressions.append(field.desc())
            else:
                order_expressions.append(field.asc())

        return order_expressions

    async def get_paginated(
        self,
        pagination: PaginationParams,
        filters: Optional[List[FilterParams]] = None,
        sorts: Optional[List[SortParams]] = None,
        include_relationships: Optional[List[str]] = None
    ) -> QueryResult[ModelType]:
        """
        Get paginated records with filtering and sorting.

        Args:
            pagination: Pagination parameters
            filters: Optional list of filter parameters
            sorts: Optional list of sort parameters
            include_relationships: Optional list of relationships to eager load

        Returns:
            QueryResult with paginated data and metadata

        Raises:
            RepositoryError: If query fails
        """
        start_time = time.time()

        try:
            # Build base query
            stmt = select(self.model)

            # Apply eager loading for relationships
            if include_relationships:
                for relationship in include_relationships:
                    if hasattr(self.model, relationship):
                        stmt = stmt.options(selectinload(getattr(self.model, relationship)))

            # Apply filters
            if filters:
                conditions = self._build_filter_conditions(filters)
                if conditions is not None:
                    stmt = stmt.where(conditions)

            # Apply sorting
            if sorts:
                order_expressions = self._build_sort_conditions(sorts)
                if order_expressions:
                    stmt = stmt.order_by(*order_expressions)
            else:
                # Default sort by created_at desc if available
                if hasattr(self.model, 'created_at'):
                    stmt = stmt.order_by(self.model.created_at.desc())

            # Get total count
            count_stmt = select(func.count()).select_from(stmt.subquery())
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()

            # Apply pagination
            stmt = stmt.offset(pagination.offset).limit(pagination.size)

            # Execute query
            result = await self.db.execute(stmt)
            items = result.scalars().all()

            query_time = time.time() - start_time
            self._track_query_performance("get_paginated", query_time, len(items))

            return QueryResult(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
                has_next=(pagination.offset + pagination.size) < total,
                has_previous=pagination.page > 1
            )

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_paginated_error", query_time, 0)
            raise RepositoryError(f"Failed to get paginated {self.model.__name__} records", e)

    async def get_cursor_paginated(
        self,
        pagination: CursorPaginationParams,
        cursor_field: str = "id",
        filters: Optional[List[FilterParams]] = None,
        sorts: Optional[List[SortParams]] = None
    ) -> CursorQueryResult[ModelType]:
        """
        Get cursor-based paginated records for better performance on large datasets.

        Args:
            pagination: Cursor pagination parameters
            cursor_field: Field to use for cursor (must be unique and sortable)
            filters: Optional list of filter parameters
            sorts: Optional list of sort parameters

        Returns:
            CursorQueryResult with paginated data and cursor metadata

        Raises:
            RepositoryError: If query fails
        """
        start_time = time.time()

        try:
            if not hasattr(self.model, cursor_field):
                raise RepositoryError(f"Cursor field '{cursor_field}' not found on {self.model.__name__}")

            # Build base query
            stmt = select(self.model)

            # Apply filters
            if filters:
                conditions = self._build_filter_conditions(filters)
                if conditions is not None:
                    stmt = stmt.where(conditions)

            # Apply cursor condition
            cursor_column = getattr(self.model, cursor_field)
            if pagination.cursor:
                try:
                    cursor_value = self._decode_cursor(pagination.cursor)
                    if pagination.direction == "forward":
                        stmt = stmt.where(cursor_column > cursor_value)
                    else:
                        stmt = stmt.where(cursor_column < cursor_value)
                except Exception:
                    # Invalid cursor, ignore
                    pass

            # Apply sorting
            if sorts:
                order_expressions = self._build_sort_conditions(sorts)
                if order_expressions:
                    stmt = stmt.order_by(*order_expressions)

            # Always include cursor field in ordering for consistency
            if pagination.direction == "forward":
                stmt = stmt.order_by(cursor_column.asc())
            else:
                stmt = stmt.order_by(cursor_column.desc())

            # Fetch one extra record to determine if there are more pages
            stmt = stmt.limit(pagination.size + 1)

            # Execute query
            result = await self.db.execute(stmt)
            all_items = result.scalars().all()

            # Determine pagination metadata
            has_next = len(all_items) > pagination.size
            items = all_items[:pagination.size]

            # Generate cursors
            next_cursor = None
            previous_cursor = None

            if items:
                if has_next:
                    next_cursor = self._encode_cursor(getattr(items[-1], cursor_field))
                if pagination.cursor:  # We have a previous page
                    previous_cursor = self._encode_cursor(getattr(items[0], cursor_field))

            query_time = time.time() - start_time
            self._track_query_performance("get_cursor_paginated", query_time, len(items))

            return CursorQueryResult(
                items=items,
                next_cursor=next_cursor,
                previous_cursor=previous_cursor,
                has_next=has_next,
                has_previous=pagination.cursor is not None
            )

        except Exception as e:
            query_time = time.time() - start_time
            self._track_query_performance("get_cursor_paginated_error", query_time, 0)
            raise RepositoryError(f"Failed to get cursor paginated {self.model.__name__} records", e)

    def _encode_cursor(self, value: Any) -> str:
        """Encode cursor value to base64 string."""
        cursor_data = {"value": str(value)}
        json_str = json.dumps(cursor_data)
        return base64.b64encode(json_str.encode()).decode()

    def _decode_cursor(self, cursor: str) -> Any:
        """Decode cursor from base64 string."""
        json_str = base64.b64decode(cursor.encode()).decode()
        cursor_data = json.loads(json_str)
        return cursor_data["value"]
