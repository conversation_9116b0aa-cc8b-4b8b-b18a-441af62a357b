"""
Integration tests for Review Notification workflows.

This module contains comprehensive integration tests for notification workflows,
testing integration with email and push notification systems.

Implements Task 4.4.6 Phase 3 requirements with notification integration validation.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.services.review_service import ReviewService
from app.services.review_response_service import ReviewResponseService
from app.services.review_moderation_service import ReviewModerationService
from app.models.review_models import ReviewStatus, ResponseStatus, ModerationAction
from app.schemas.review_schemas import ReviewCreateSchema, ReviewResponseCreateSchema


class TestReviewNotificationIntegration:
    """Test review notification integration workflows."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def mock_email_service(self):
        """Mock email service."""
        return AsyncMock()

    @pytest.fixture
    def mock_push_service(self):
        """Mock push notification service."""
        return AsyncMock()

    @pytest.fixture
    def review_service(self, mock_db_session):
        """Create ReviewService instance."""
        service = ReviewService(mock_db_session)
        service.notification_service = AsyncMock()
        service.email_service = AsyncMock()
        service.push_service = AsyncMock()
        return service

    @pytest.fixture
    def response_service(self, mock_db_session):
        """Create ReviewResponseService instance."""
        service = ReviewResponseService(mock_db_session)
        service.notification_service = AsyncMock()
        service.email_service = AsyncMock()
        service.push_service = AsyncMock()
        return service

    @pytest.fixture
    def moderation_service(self, mock_db_session):
        """Create ReviewModerationService instance."""
        service = ReviewModerationService(mock_db_session)
        service.notification_service = AsyncMock()
        service.email_service = AsyncMock()
        return service

    @pytest.mark.asyncio
    async def test_review_creation_notification_workflow(self, review_service):
        """Test notification workflow when review is created."""
        # Mock review creation
        mock_review = MagicMock()
        mock_review.id = 1
        mock_review.customer_id = 1
        mock_review.vendor_id = 2
        mock_review.rating = 5
        mock_review.title = "Excellent Service"
        mock_review.content = "Great cultural experience!"

        # Mock vendor and customer data
        mock_vendor = MagicMock()
        mock_vendor.id = 2
        mock_vendor.email = "<EMAIL>"
        mock_vendor.notification_preferences = {"email": True, "push": True}

        mock_customer = MagicMock()
        mock_customer.id = 1
        mock_customer.email = "<EMAIL>"
        mock_customer.first_name = "John"

        with patch.object(review_service, 'create_review', return_value=mock_review):
            review_data = ReviewCreateSchema(
                booking_id=123,
                rating=5,
                title="Excellent Service",
                content="Great cultural experience!"
            )

            await review_service.create_review(
                customer_id=1,
                booking_id=123,
                review_data=review_data
            )

        # Verify notification service was called
        review_service.notification_service.send_review_notification.assert_called()

        # Verify email notification to vendor
        review_service.email_service.send_review_notification_email.assert_called()

        # Verify push notification to vendor
        review_service.push_service.send_review_notification_push.assert_called()

    @pytest.mark.asyncio
    async def test_vendor_response_notification_workflow(self, response_service):
        """Test notification workflow when vendor responds to review."""
        # Mock response creation
        mock_response = MagicMock()
        mock_response.id = 1
        mock_response.review_id = 1
        mock_response.vendor_id = 2
        mock_response.content = "Thank you for your feedback!"

        # Mock review and customer data
        mock_review = MagicMock()
        mock_review.id = 1
        mock_review.customer_id = 1
        mock_review.title = "Excellent Service"

        mock_customer = MagicMock()
        mock_customer.id = 1
        mock_customer.email = "<EMAIL>"
        mock_customer.notification_preferences = {"email": True, "push": True}

        with patch.object(response_service, 'create_response', return_value=mock_response):
            response_data = ReviewResponseCreateSchema(
                review_id=1,
                content="Thank you for your feedback!"
            )

            await response_service.create_response(
                vendor_id=2,
                review_id=1,
                response_data=response_data
            )

        # Verify notification service was called
        response_service.notification_service.send_response_notification.assert_called()

        # Verify email notification to customer
        response_service.email_service.send_response_notification_email.assert_called()

        # Verify push notification to customer
        response_service.push_service.send_response_notification_push.assert_called()

    @pytest.mark.asyncio
    async def test_moderation_alert_notification_workflow(self, moderation_service):
        """Test notification workflow for moderation alerts."""
        # Mock moderation record creation
        mock_moderation = MagicMock()
        mock_moderation.id = 1
        mock_moderation.review_id = 1
        mock_moderation.action = ModerationAction.FLAG
        mock_moderation.manual_review_required = True
        mock_moderation.reason = "Content flagged for manual review"

        # Mock admin users for notification
        mock_admins = [
            MagicMock(id=1, email="<EMAIL>", role="ADMIN"),
            MagicMock(id=2, email="<EMAIL>", role="MODERATOR")
        ]

        with patch.object(moderation_service, 'create_moderation_record', return_value=mock_moderation):
            await moderation_service.create_moderation_record(
                review_id=1,
                review_content={
                    "title": "Questionable Content",
                    "content": "This content needs manual review."
                }
            )

        # Verify moderation alert notification was sent
        moderation_service.notification_service.send_moderation_alert.assert_called()

        # Verify email notifications to admin users
        moderation_service.email_service.send_moderation_alert_email.assert_called()

    @pytest.mark.asyncio
    async def test_email_notification_template_integration(self, review_service):
        """Test email notification template integration."""
        # Mock review creation
        mock_review = MagicMock()
        mock_review.id = 1
        mock_review.customer_id = 1
        mock_review.vendor_id = 2
        mock_review.rating = 4
        mock_review.title = "Good Service"
        mock_review.content = "Service was satisfactory."

        # Mock email template data
        expected_template_data = {
            "vendor_name": "Cultural Tours Inc",
            "customer_name": "John Doe",
            "review_title": "Good Service",
            "review_rating": 4,
            "review_content": "Service was satisfactory.",
            "review_url": "https://app.example.com/reviews/1"
        }

        with patch.object(review_service, 'create_review', return_value=mock_review):
            review_data = ReviewCreateSchema(
                booking_id=123,
                rating=4,
                title="Good Service",
                content="Service was satisfactory."
            )

            await review_service.create_review(
                customer_id=1,
                booking_id=123,
                review_data=review_data
            )

        # Verify email service was called with correct template data
        review_service.email_service.send_review_notification_email.assert_called()
        call_args = review_service.email_service.send_review_notification_email.call_args

        # Verify template data structure
        assert "vendor_name" in str(call_args)
        assert "review_rating" in str(call_args)

    @pytest.mark.asyncio
    async def test_push_notification_integration(self, response_service):
        """Test push notification integration with Firebase."""
        # Mock response creation
        mock_response = MagicMock()
        mock_response.id = 1
        mock_response.review_id = 1
        mock_response.vendor_id = 2

        # Mock push notification data
        expected_push_data = {
            "title": "New Response to Your Review",
            "body": "A vendor has responded to your review",
            "data": {
                "review_id": 1,
                "response_id": 1,
                "type": "vendor_response"
            }
        }

        with patch.object(response_service, 'create_response', return_value=mock_response):
            response_data = ReviewResponseCreateSchema(
                review_id=1,
                content="Thank you for your review!"
            )

            await response_service.create_response(
                vendor_id=2,
                review_id=1,
                response_data=response_data
            )

        # Verify push notification service was called
        response_service.push_service.send_response_notification_push.assert_called()
        call_args = response_service.push_service.send_response_notification_push.call_args

        # Verify push notification structure
        assert "title" in str(call_args)
        assert "vendor_response" in str(call_args)

    @pytest.mark.asyncio
    async def test_notification_preference_integration(self, review_service):
        """Test notification preference handling integration."""
        # Mock review creation
        mock_review = MagicMock()
        mock_review.id = 1
        mock_review.vendor_id = 2

        # Mock vendor with specific notification preferences
        mock_vendor = MagicMock()
        mock_vendor.id = 2
        mock_vendor.notification_preferences = {
            "email": True,
            "push": False,  # Push notifications disabled
            "sms": False
        }

        with patch.object(review_service, 'create_review', return_value=mock_review):
            review_data = ReviewCreateSchema(
                booking_id=123,
                rating=5,
                title="Great Service",
                content="Excellent experience!"
            )

            await review_service.create_review(
                customer_id=1,
                booking_id=123,
                review_data=review_data
            )

        # Verify email notification was sent (preference enabled)
        review_service.email_service.send_review_notification_email.assert_called()

        # Verify push notification was NOT sent (preference disabled)
        review_service.push_service.send_review_notification_push.assert_not_called()

    @pytest.mark.asyncio
    async def test_notification_failure_handling_integration(self, review_service):
        """Test notification failure handling and retry mechanisms."""
        # Mock review creation
        mock_review = MagicMock()
        mock_review.id = 1
        mock_review.vendor_id = 2

        # Mock email service failure
        review_service.email_service.send_review_notification_email.side_effect = Exception("Email service unavailable")

        # Mock notification retry mechanism
        review_service.notification_service.retry_failed_notification = AsyncMock()

        with patch.object(review_service, 'create_review', return_value=mock_review):
            review_data = ReviewCreateSchema(
                booking_id=123,
                rating=5,
                title="Test Review",
                content="Testing notification failure handling."
            )

            # Should not raise exception even if email fails
            await review_service.create_review(
                customer_id=1,
                booking_id=123,
                review_data=review_data
            )

        # Verify retry mechanism was triggered
        review_service.notification_service.retry_failed_notification.assert_called()

    @pytest.mark.asyncio
    async def test_bulk_notification_integration(self, moderation_service):
        """Test bulk notification integration for multiple reviews."""
        # Mock bulk moderation results
        mock_moderations = [
            MagicMock(id=1, review_id=1, action=ModerationAction.FLAG),
            MagicMock(id=2, review_id=2, action=ModerationAction.REJECT),
            MagicMock(id=3, review_id=3, action=ModerationAction.FLAG)
        ]

        # Mock bulk notification processing
        moderation_service.notification_service.send_bulk_moderation_alerts = AsyncMock()

        with patch.object(moderation_service, 'bulk_moderate_reviews', return_value=mock_moderations):
            await moderation_service.bulk_moderate_reviews(
                review_ids=[1, 2, 3]
            )

        # Verify bulk notification was triggered
        moderation_service.notification_service.send_bulk_moderation_alerts.assert_called()

    @pytest.mark.asyncio
    async def test_notification_performance_integration(self, review_service):
        """Test notification performance requirements."""
        import time

        # Mock review creation
        mock_review = MagicMock()
        mock_review.id = 1

        # Mock fast notification services
        review_service.notification_service.send_review_notification = AsyncMock()
        review_service.email_service.send_review_notification_email = AsyncMock()
        review_service.push_service.send_review_notification_push = AsyncMock()

        with patch.object(review_service, 'create_review', return_value=mock_review):
            # Measure notification performance
            start_time = time.time()
            
            review_data = ReviewCreateSchema(
                booking_id=123,
                rating=5,
                title="Performance Test",
                content="Testing notification performance."
            )

            await review_service.create_review(
                customer_id=1,
                booking_id=123,
                review_data=review_data
            )
            
            notification_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        # Performance assertion - notifications should be fast
        assert notification_time < 100  # <100ms for notification processing

        # Verify all notification services were called
        review_service.notification_service.send_review_notification.assert_called()
        review_service.email_service.send_review_notification_email.assert_called()
        review_service.push_service.send_review_notification_push.assert_called()
