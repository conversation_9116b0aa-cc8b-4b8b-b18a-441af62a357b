"""
Password Security schemas for Culture Connect Backend API.

This module defines comprehensive password security schemas including:
- Password strength validation with configurable requirements
- Password reset request and confirmation schemas
- Account lockout status and management schemas
- Password history tracking schemas

Implements Task 2.1.2 requirements for password security and hashing with
production-grade validation and security features.
"""

import re
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr, validator, Field
from enum import Enum


class PasswordStrengthLevel(str, Enum):
    """Password strength levels."""
    VERY_WEAK = "very_weak"
    WEAK = "weak"
    FAIR = "fair"
    GOOD = "good"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


class PasswordRequirements(BaseModel):
    """Password strength requirements configuration."""
    min_length: int = Field(default=8, ge=6, le=128, description="Minimum password length")
    max_length: int = Field(default=128, ge=8, le=256, description="Maximum password length")
    require_uppercase: bool = Field(default=True, description="Require uppercase letters")
    require_lowercase: bool = Field(default=True, description="Require lowercase letters")
    require_numbers: bool = Field(default=True, description="Require numeric characters")
    require_special_chars: bool = Field(default=True, description="Require special characters")
    min_special_chars: int = Field(default=1, ge=0, le=10, description="Minimum special characters")
    forbidden_patterns: List[str] = Field(
        default_factory=lambda: [
            "password", "123456", "qwerty", "admin", "user", "guest",
            "welcome", "login", "root", "test", "demo", "sample"
        ],
        description="Forbidden password patterns"
    )
    forbidden_sequences: List[str] = Field(
        default_factory=lambda: ["123", "abc", "qwe", "asd", "zxc"],
        description="Forbidden character sequences"
    )
    max_repeated_chars: int = Field(default=3, ge=2, le=10, description="Maximum repeated characters")

    class Config:
        json_schema_extra = {
            "example": {
                "min_length": 8,
                "max_length": 128,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special_chars": True,
                "min_special_chars": 1,
                "forbidden_patterns": ["password", "123456", "qwerty"],
                "forbidden_sequences": ["123", "abc", "qwe"],
                "max_repeated_chars": 3
            }
        }


class PasswordValidationResult(BaseModel):
    """Password validation result with detailed feedback."""
    is_valid: bool = Field(description="Whether the password meets all requirements")
    strength_score: int = Field(ge=0, le=100, description="Password strength score (0-100)")
    strength_level: PasswordStrengthLevel = Field(description="Password strength level")
    violations: List[str] = Field(default_factory=list, description="List of requirement violations")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    entropy_bits: float = Field(ge=0, description="Password entropy in bits")
    estimated_crack_time: str = Field(description="Estimated time to crack password")

    class Config:
        json_schema_extra = {
            "example": {
                "is_valid": True,
                "strength_score": 85,
                "strength_level": "strong",
                "violations": [],
                "suggestions": ["Consider adding more special characters"],
                "entropy_bits": 52.3,
                "estimated_crack_time": "centuries"
            }
        }


class PasswordStrengthCheck(BaseModel):
    """Schema for password strength checking."""
    password: str = Field(min_length=1, max_length=256, description="Password to validate")
    requirements: Optional[PasswordRequirements] = Field(
        default=None,
        description="Custom requirements (uses defaults if not provided)"
    )

    @validator('password')
    def validate_password_not_empty(cls, v):
        """Ensure password is not empty or whitespace only."""
        if not v or not v.strip():
            raise ValueError('Password cannot be empty')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "password": "MySecureP@ssw0rd!",
                "requirements": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_numbers": True,
                    "require_special_chars": True
                }
            }
        }


class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr = Field(description="Email address for password reset")
    client_ip: Optional[str] = Field(default=None, description="Client IP address")
    user_agent: Optional[str] = Field(default=None, description="Client user agent")

    @validator('email')
    def validate_email_format(cls, v):
        """Additional email validation."""
        if len(v) > 255:
            raise ValueError('Email address too long')
        return v.lower()

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "client_ip": "*************",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        }


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str = Field(min_length=32, max_length=256, description="Password reset token")
    new_password: str = Field(min_length=8, max_length=128, description="New password")
    confirm_password: str = Field(min_length=8, max_length=128, description="Password confirmation")
    client_ip: Optional[str] = Field(default=None, description="Client IP address")
    user_agent: Optional[str] = Field(default=None, description="Client user agent")

    @validator('token')
    def validate_token_format(cls, v):
        """Validate token format."""
        if not re.match(r'^[A-Za-z0-9_-]+$', v):
            raise ValueError('Invalid token format')
        return v

    @validator('new_password')
    def validate_password_strength(cls, v):
        """Basic password strength validation."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')

        # Check for basic requirements
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not has_upper:
            raise ValueError('Password must contain at least one uppercase letter')
        if not has_lower:
            raise ValueError('Password must contain at least one lowercase letter')
        if not has_digit:
            raise ValueError('Password must contain at least one number')
        if not has_special:
            raise ValueError('Password must contain at least one special character')

        return v

    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """Ensure passwords match."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "token": "abc123def456ghi789jkl012mno345pqr678",
                "new_password": "MyNewSecureP@ssw0rd!",
                "confirm_password": "MyNewSecureP@ssw0rd!",
                "client_ip": "*************",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        }


class PasswordChangeRequest(BaseModel):
    """Schema for password change by authenticated user."""
    current_password: str = Field(min_length=1, max_length=128, description="Current password")
    new_password: str = Field(min_length=8, max_length=128, description="New password")
    confirm_password: str = Field(min_length=8, max_length=128, description="Password confirmation")

    @validator('new_password')
    def validate_password_strength(cls, v):
        """Basic password strength validation."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')

        # Check for basic requirements
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not has_upper:
            raise ValueError('Password must contain at least one uppercase letter')
        if not has_lower:
            raise ValueError('Password must contain at least one lowercase letter')
        if not has_digit:
            raise ValueError('Password must contain at least one number')
        if not has_special:
            raise ValueError('Password must contain at least one special character')

        return v

    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """Ensure passwords match."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    @validator('new_password')
    def validate_password_different(cls, v, values):
        """Ensure new password is different from current."""
        if 'current_password' in values and v == values['current_password']:
            raise ValueError('New password must be different from current password')
        return v

    class Config:
        schema_extra = {
            "example": {
                "current_password": "MyOldPassword123!",
                "new_password": "MyNewSecureP@ssw0rd!",
                "confirm_password": "MyNewSecureP@ssw0rd!"
            }
        }


class AccountLockoutStatus(BaseModel):
    """Schema for account lockout status."""
    is_locked: bool = Field(description="Whether the account is currently locked")
    failed_attempts: int = Field(ge=0, description="Number of failed login attempts")
    max_attempts: int = Field(ge=1, description="Maximum allowed failed attempts")
    locked_until: Optional[datetime] = Field(default=None, description="When the lockout expires")
    remaining_lockout_seconds: Optional[int] = Field(
        default=None,
        ge=0,
        description="Remaining lockout time in seconds"
    )
    last_attempt_at: Optional[datetime] = Field(default=None, description="Last failed attempt timestamp")
    last_attempt_ip: Optional[str] = Field(default=None, description="IP of last failed attempt")
    lockout_reason: Optional[str] = Field(default=None, description="Reason for lockout")

    class Config:
        json_schema_extra = {
            "example": {
                "is_locked": True,
                "failed_attempts": 5,
                "max_attempts": 5,
                "locked_until": "2024-01-15T10:45:00Z",
                "remaining_lockout_seconds": 900,
                "last_attempt_at": "2024-01-15T10:30:00Z",
                "last_attempt_ip": "*************",
                "lockout_reason": "too_many_attempts"
            }
        }


class PasswordHistoryEntry(BaseModel):
    """Schema for password history entry."""
    id: int = Field(description="History entry ID")
    created_at: datetime = Field(description="When the password was set")
    is_current: bool = Field(default=False, description="Whether this is the current password")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 123,
                "created_at": "2024-01-15T10:30:00Z",
                "is_current": False
            }
        }


class PasswordSecurityResponse(BaseModel):
    """Response schema for password security operations."""
    success: bool = Field(description="Whether the operation was successful")
    message: str = Field(description="Human-readable message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional details")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Password updated successfully",
                "details": {
                    "strength_score": 85,
                    "history_entries_added": 1
                }
            }
        }
