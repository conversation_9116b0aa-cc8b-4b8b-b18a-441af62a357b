"""
Payment schemas for Culture Connect Backend API.

This module defines comprehensive payment management schemas including:
- Payment schemas: Payment creation, processing, and status management
- PaymentMethod schemas: Payment method management with encrypted storage
- Paystack schemas: Provider-specific request/response validation
- Webhook schemas: Webhook event processing and validation

Implements Task 4.3.1 Phase 1 requirements for Paystack integration with
production-grade Pydantic V2 validation and seamless integration with existing models.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID
from pydantic import Field, field_validator, ConfigDict, AnyHttpUrl

from app.schemas.base import BaseSchema, TimestampMixin
from app.models.payment import PaymentStatus, PaymentMethodType, TransactionType
from app.core.payment.config import PaymentProviderType


class PaymentBaseSchema(BaseSchema):
    """Base schema for payment with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "created_at": "2025-01-27T10:00:00Z",
                "updated_at": "2025-01-27T10:00:00Z"
            }
        }
    )


# Payment Creation and Management Schemas
class PaymentCreate(PaymentBaseSchema):
    """Schema for creating a new payment."""

    booking_id: int = Field(..., description="Associated booking ID", gt=0)
    amount: Decimal = Field(..., description="Payment amount", gt=0)
    currency: str = Field(default="NGN", description="Payment currency")
    payment_method_id: Optional[int] = Field(None, description="Payment method ID")
    return_url: Optional[AnyHttpUrl] = Field(None, description="Return URL after payment")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    # Cryptocurrency payment fields
    crypto_currency: Optional[str] = Field(None, description="Cryptocurrency type (BTC, ETH, USDT, USDC)")
    preferred_provider: Optional[str] = Field(None, description="Preferred payment provider (paystack, stripe, busha)")

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v: str) -> str:
        """Validate currency code."""
        allowed_currencies = ['NGN', 'USD', 'EUR', 'GBP']
        if v not in allowed_currencies:
            raise ValueError(f"Currency must be one of: {', '.join(allowed_currencies)}")
        return v.upper()

    @field_validator('amount')
    @classmethod
    def validate_amount(cls, v: Decimal) -> Decimal:
        """Validate payment amount."""
        if v <= 0:
            raise ValueError("Amount must be positive")
        if v > Decimal("10000000"):  # ₦10M limit
            raise ValueError("Amount exceeds maximum limit")
        return v

    @field_validator('crypto_currency')
    @classmethod
    def validate_crypto_currency(cls, v: Optional[str]) -> Optional[str]:
        """Validate cryptocurrency type."""
        if v is not None:
            allowed_cryptos = ['BTC', 'ETH', 'USDT', 'USDC']
            if v.upper() not in allowed_cryptos:
                raise ValueError(f"Cryptocurrency must be one of: {', '.join(allowed_cryptos)}")
            return v.upper()
        return v

    @field_validator('preferred_provider')
    @classmethod
    def validate_preferred_provider(cls, v: Optional[str]) -> Optional[str]:
        """Validate preferred payment provider."""
        if v is not None:
            allowed_providers = ['paystack', 'stripe', 'busha']
            if v.lower() not in allowed_providers:
                raise ValueError(f"Provider must be one of: {', '.join(allowed_providers)}")
            return v.lower()
        return v


class PaymentUpdate(PaymentBaseSchema):
    """Schema for updating payment information."""

    status: Optional[PaymentStatus] = Field(None, description="Payment status")
    provider_reference: Optional[str] = Field(None, description="Provider reference")
    failure_reason: Optional[str] = Field(None, description="Failure reason")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class PaymentResponse(PaymentBaseSchema, TimestampMixin):
    """Schema for payment response."""

    id: int = Field(..., description="Payment ID")
    booking_id: int = Field(..., description="Associated booking ID")
    user_id: int = Field(..., description="Customer user ID")
    vendor_id: int = Field(..., description="Service vendor ID")
    amount: Decimal = Field(..., description="Payment amount")
    currency: str = Field(..., description="Payment currency")
    provider: PaymentProviderType = Field(..., description="Payment provider")
    status: PaymentStatus = Field(..., description="Payment status")

    # Provider-specific fields
    payment_intent_id: Optional[str] = Field(None, description="Provider payment intent ID")
    transaction_reference: str = Field(..., description="Unique transaction reference")
    provider_reference: Optional[str] = Field(None, description="Provider transaction reference")
    payment_url: Optional[str] = Field(None, description="Payment authorization URL")

    # Timing
    expires_at: Optional[datetime] = Field(None, description="Payment expiration")
    paid_at: Optional[datetime] = Field(None, description="Payment completion timestamp")

    # Fees
    platform_fee: Decimal = Field(..., description="Platform commission fee")
    provider_fee: Decimal = Field(..., description="Payment provider fee")
    net_amount: Decimal = Field(..., description="Net amount after fees")


# Payment Method Schemas
class PaymentMethodCreate(PaymentBaseSchema):
    """Schema for creating a payment method."""

    method_type: PaymentMethodType = Field(..., description="Payment method type")
    provider: PaymentProviderType = Field(..., description="Payment provider")
    display_name: str = Field(..., description="User-friendly display name", min_length=1, max_length=100)
    is_default: bool = Field(default=False, description="Set as default payment method")

    # Card-specific fields (optional)
    last_four: Optional[str] = Field(None, description="Last four digits", min_length=4, max_length=4)
    brand: Optional[str] = Field(None, description="Card brand", max_length=50)
    expiry_month: Optional[int] = Field(None, description="Expiry month", ge=1, le=12)
    expiry_year: Optional[int] = Field(None, description="Expiry year", ge=2024)

    @field_validator('last_four')
    @classmethod
    def validate_last_four(cls, v: Optional[str]) -> Optional[str]:
        """Validate last four digits."""
        if v is not None and not v.isdigit():
            raise ValueError("Last four must be numeric")
        return v


class PaymentMethodUpdate(PaymentBaseSchema):
    """Schema for updating payment method."""

    display_name: Optional[str] = Field(None, description="Display name", min_length=1, max_length=100)
    is_default: Optional[bool] = Field(None, description="Set as default")
    is_active: Optional[bool] = Field(None, description="Active status")


class PaymentMethodResponse(PaymentBaseSchema, TimestampMixin):
    """Schema for payment method response."""

    id: int = Field(..., description="Payment method ID")
    user_id: int = Field(..., description="User ID")
    method_type: PaymentMethodType = Field(..., description="Payment method type")
    provider: PaymentProviderType = Field(..., description="Payment provider")
    display_name: str = Field(..., description="Display name")
    is_default: bool = Field(..., description="Is default method")
    is_active: bool = Field(..., description="Is active")

    # Display information (non-sensitive)
    last_four: Optional[str] = Field(None, description="Last four digits")
    brand: Optional[str] = Field(None, description="Card brand")
    expiry_month: Optional[int] = Field(None, description="Expiry month")
    expiry_year: Optional[int] = Field(None, description="Expiry year")


# Busha Cryptocurrency Schemas
class BushaCryptoPaymentRequest(PaymentBaseSchema):
    """Schema for Busha cryptocurrency payment request."""

    amount: Decimal = Field(..., description="Payment amount in fiat currency", gt=0)
    currency: str = Field(default="NGN", description="Fiat currency")
    crypto_currency: str = Field(..., description="Cryptocurrency type (BTC, ETH, USDT, USDC)")
    customer_email: str = Field(..., description="Customer email address")
    reference: str = Field(..., description="Unique payment reference")
    callback_url: Optional[str] = Field(None, description="Callback URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    @field_validator('crypto_currency')
    @classmethod
    def validate_crypto_currency(cls, v: str) -> str:
        """Validate cryptocurrency type."""
        allowed_cryptos = ['BTC', 'ETH', 'USDT', 'USDC']
        if v.upper() not in allowed_cryptos:
            raise ValueError(f"Cryptocurrency must be one of: {', '.join(allowed_cryptos)}")
        return v.upper()


class BushaCryptoPaymentResponse(PaymentBaseSchema):
    """Schema for Busha cryptocurrency payment response."""

    payment_id: str = Field(..., description="Busha payment ID")
    reference: str = Field(..., description="Payment reference")
    amount: Decimal = Field(..., description="Fiat amount")
    currency: str = Field(..., description="Fiat currency")
    crypto_currency: str = Field(..., description="Cryptocurrency type")
    crypto_amount: Decimal = Field(..., description="Cryptocurrency amount")
    wallet_address: str = Field(..., description="Crypto wallet address for payment")
    qr_code: Optional[str] = Field(None, description="QR code for payment")
    expires_at: Optional[datetime] = Field(None, description="Payment expiration")
    status: str = Field(..., description="Payment status")
    blockchain_network: str = Field(default="mainnet", description="Blockchain network")


class BushaExchangeRatesResponse(PaymentBaseSchema):
    """Schema for Busha exchange rates response."""

    base_currency: str = Field(..., description="Base currency")
    rates: Dict[str, Decimal] = Field(..., description="Exchange rates")
    timestamp: str = Field(..., description="Rates timestamp")
    provider: str = Field(default="busha", description="Rate provider")


class BushaWebhookEvent(PaymentBaseSchema):
    """Schema for Busha webhook events."""

    event: str = Field(..., description="Event type")
    data: Dict[str, Any] = Field(..., description="Event data")

    @field_validator('event')
    @classmethod
    def validate_event(cls, v: str) -> str:
        """Validate webhook event type."""
        allowed_events = [
            'payment.confirmed',
            'payment.completed',
            'payment.failed',
            'payment.expired'
        ]
        if v not in allowed_events:
            raise ValueError(f"Unsupported event type: {v}")
        return v


# Paystack-Specific Schemas
class PaystackInitializeRequest(PaymentBaseSchema):
    """Schema for Paystack payment initialization."""

    email: str = Field(..., description="Customer email")
    amount: int = Field(..., description="Amount in kobo (NGN * 100)")
    currency: str = Field(default="NGN", description="Currency")
    reference: str = Field(..., description="Unique transaction reference")
    callback_url: Optional[str] = Field(None, description="Callback URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    channels: Optional[List[str]] = Field(None, description="Payment channels")

    @field_validator('amount')
    @classmethod
    def validate_amount(cls, v: int) -> int:
        """Validate amount in kobo."""
        if v <= 0:
            raise ValueError("Amount must be positive")
        return v


class PaystackInitializeResponse(PaymentBaseSchema):
    """Schema for Paystack initialization response."""

    status: bool = Field(..., description="Request status")
    message: str = Field(..., description="Response message")
    data: Dict[str, Any] = Field(..., description="Response data")

    # Extracted data fields
    authorization_url: Optional[str] = Field(None, description="Payment authorization URL")
    access_code: Optional[str] = Field(None, description="Payment access code")
    reference: Optional[str] = Field(None, description="Transaction reference")


class PaystackVerifyResponse(PaymentBaseSchema):
    """Schema for Paystack payment verification response."""

    status: bool = Field(..., description="Request status")
    message: str = Field(..., description="Response message")
    data: Dict[str, Any] = Field(..., description="Transaction data")

    # Extracted transaction data
    reference: Optional[str] = Field(None, description="Transaction reference")
    amount: Optional[int] = Field(None, description="Amount in kobo")
    gateway_response: Optional[str] = Field(None, description="Gateway response")
    paid_at: Optional[str] = Field(None, description="Payment timestamp")
    channel: Optional[str] = Field(None, description="Payment channel")


# Webhook Schemas
class PaystackWebhookEvent(PaymentBaseSchema):
    """Schema for Paystack webhook events."""

    event: str = Field(..., description="Event type")
    data: Dict[str, Any] = Field(..., description="Event data")

    @field_validator('event')
    @classmethod
    def validate_event(cls, v: str) -> str:
        """Validate webhook event type."""
        allowed_events = [
            'charge.success',
            'charge.failed',
            'transfer.success',
            'transfer.failed',
            'transfer.reversed'
        ]
        if v not in allowed_events:
            raise ValueError(f"Unsupported event type: {v}")
        return v


class WebhookProcessingResult(PaymentBaseSchema):
    """Schema for webhook processing result."""

    event_id: str = Field(..., description="Webhook event ID")
    event_type: str = Field(..., description="Event type")
    processed: bool = Field(..., description="Processing status")
    payment_id: Optional[int] = Field(None, description="Associated payment ID")
    message: str = Field(..., description="Processing message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")


# Payment Processing Schemas
class PaymentProcessRequest(PaymentBaseSchema):
    """Schema for payment processing request."""

    payment_id: int = Field(..., description="Payment ID to process")
    provider: Optional[PaymentProviderType] = Field(None, description="Specific provider")
    force_retry: bool = Field(default=False, description="Force retry failed payment")


class PaymentProcessResponse(PaymentBaseSchema):
    """Schema for payment processing response."""

    payment_id: int = Field(..., description="Payment ID")
    status: PaymentStatus = Field(..., description="Payment status")
    authorization_url: Optional[str] = Field(None, description="Payment URL")
    message: str = Field(..., description="Processing message")
    expires_at: Optional[datetime] = Field(None, description="Payment expiration")


# List and Pagination Schemas
class PaymentListResponse(PaymentBaseSchema):
    """Schema for paginated payment list response."""

    items: List[PaymentResponse] = Field(..., description="Payment items")
    total: int = Field(..., description="Total count")
    page: int = Field(..., description="Current page")
    size: int = Field(..., description="Page size")
    has_next: bool = Field(..., description="Has next page")
    has_previous: bool = Field(..., description="Has previous page")


class PaymentMethodListResponse(PaymentBaseSchema):
    """Schema for payment method list response."""

    items: List[PaymentMethodResponse] = Field(..., description="Payment method items")
    total: int = Field(..., description="Total count")


# Export all schemas
__all__ = [
    # Payment schemas
    "PaymentCreate",
    "PaymentUpdate",
    "PaymentResponse",
    "PaymentListResponse",

    # Payment method schemas
    "PaymentMethodCreate",
    "PaymentMethodUpdate",
    "PaymentMethodResponse",
    "PaymentMethodListResponse",

    # Paystack schemas
    "PaystackInitializeRequest",
    "PaystackInitializeResponse",
    "PaystackVerifyResponse",

    # Webhook schemas
    "PaystackWebhookEvent",
    "WebhookProcessingResult",

    # Processing schemas
    "PaymentProcessRequest",
    "PaymentProcessResponse",
]
