"""
Redis-backed Message Queue Service for Culture Connect Backend API.

This module provides comprehensive message queuing infrastructure for high-throughput scenarios including:
- Redis-backed message persistence for reliability and scalability
- Message queue partitioning for improved throughput (>2000 messages/second)
- Dead letter queue implementation for failed message handling
- Queue monitoring and alerting for production deployment

Implements Task 6.1.1 Phase 8 requirements for message queuing optimization with:
- Redis pub/sub for distributed messaging across multiple instances
- Message persistence with configurable TTL and compression
- Queue partitioning and load balancing for optimal throughput
- Dead letter queue handling with retry mechanisms and failure analysis

Performance targets: >2000 messages/second throughput, <50ms message processing, >99.9% delivery reliability
"""

import asyncio
import json
import logging
import time
import zlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from uuid import uuid4, UUID
from collections import defaultdict, deque

import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.logging import correlation_id
from app.core.config import settings
from app.core.cache import CacheManager

logger = logging.getLogger(__name__)


class MessagePriority(str, Enum):
    """Message priority levels."""
    IMMEDIATE = "immediate"  # <10ms processing
    HIGH = "high"           # <25ms processing
    STANDARD = "standard"   # <50ms processing
    LOW = "low"            # <100ms processing
    BATCH = "batch"        # Batch processing


class QueueStatus(str, Enum):
    """Queue status indicators."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    OFFLINE = "offline"


@dataclass
class QueueMessage:
    """Message structure for queue operations."""
    id: str
    content: Dict[str, Any]
    priority: MessagePriority
    created_at: datetime
    expires_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    correlation_id: Optional[str] = None
    source: Optional[str] = None
    target: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class QueueMetrics:
    """Queue performance metrics."""
    queue_name: str
    total_messages: int = 0
    processed_messages: int = 0
    failed_messages: int = 0
    dead_letter_messages: int = 0
    average_processing_time: float = 0.0
    messages_per_second: float = 0.0
    queue_size: int = 0
    oldest_message_age: float = 0.0
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class RedisMessageQueueService:
    """
    Production-grade Redis message queue service with advanced features.

    Provides comprehensive message queuing including:
    - Redis-backed message persistence with compression
    - Queue partitioning for improved throughput
    - Dead letter queue handling with retry mechanisms
    - Real-time monitoring and alerting
    - Horizontal scaling with Redis pub/sub
    """

    def __init__(self, redis_client: Optional[Redis] = None, cache_manager: Optional[CacheManager] = None):
        self.redis = redis_client
        self.cache = cache_manager

        # Queue configuration
        self.queue_prefix = "websocket_queue"
        self.dlq_prefix = "websocket_dlq"
        self.metrics_prefix = "queue_metrics"
        self.pubsub_prefix = "websocket_pubsub"

        # Queue partitioning
        self.partition_count = 8  # Number of partitions per priority
        self.current_partition = 0

        # Message processing
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        self.message_handlers: Dict[str, callable] = {}

        # Performance tracking
        self.metrics: Dict[str, QueueMetrics] = {}
        self.processing_times = deque(maxlen=1000)  # Last 1000 processing times

        # Configuration
        self.config = {
            "max_queue_size": 10000,
            "message_ttl": 3600,  # 1 hour
            "dlq_ttl": 86400,     # 24 hours
            "compression_threshold": 1024,  # Compress messages > 1KB
            "batch_size": 100,
            "processing_timeout": 30,
            "retry_delay": 5,
            "max_retries": 3
        }

        # Background tasks
        self._background_tasks: Set[asyncio.Task] = set()
        self._tasks_started = False

    async def initialize(self):
        """Initialize Redis connection and queue infrastructure."""
        if not self.redis:
            try:
                self.redis = redis.from_url(
                    settings.REDIS_URL,
                    encoding="utf-8",
                    decode_responses=False,  # Handle binary data for compression
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30,
                    max_connections=20
                )

                # Test connection
                await self.redis.ping()
                logger.info("Redis message queue service initialized successfully")

                # Initialize queue structures
                await self._initialize_queues()

                # Start background tasks
                await self._start_background_tasks()

            except Exception as e:
                logger.error(f"Failed to initialize Redis message queue: {str(e)}")
                raise

    async def _initialize_queues(self):
        """Initialize queue structures and partitions."""
        for priority in MessagePriority:
            for partition in range(self.partition_count):
                queue_name = f"{self.queue_prefix}:{priority.value}:{partition}"
                dlq_name = f"{self.dlq_prefix}:{priority.value}:{partition}"

                # Initialize metrics
                self.metrics[queue_name] = QueueMetrics(queue_name=queue_name)
                self.metrics[dlq_name] = QueueMetrics(queue_name=dlq_name)

    async def _start_background_tasks(self):
        """Start background processing and monitoring tasks."""
        if self._tasks_started:
            return

        self._background_tasks.add(asyncio.create_task(self._process_queues()))
        self._background_tasks.add(asyncio.create_task(self._monitor_queues()))
        self._background_tasks.add(asyncio.create_task(self._cleanup_expired_messages()))

        self._tasks_started = True

    async def enqueue_message(
        self,
        content: Dict[str, Any],
        priority: MessagePriority = MessagePriority.STANDARD,
        expires_in: Optional[int] = None,
        correlation_id: Optional[str] = None,
        source: Optional[str] = None,
        target: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Enqueue message with priority-based routing and partitioning.

        Performance target: <10ms for immediate priority, <50ms for standard.
        """
        start_time = time.time()

        try:
            # Create message
            message_id = str(uuid4())
            expires_at = None
            if expires_in:
                expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            message = QueueMessage(
                id=message_id,
                content=content,
                priority=priority,
                created_at=datetime.now(timezone.utc),
                expires_at=expires_at,
                correlation_id=correlation_id or str(uuid4()),
                source=source,
                target=target,
                metadata=metadata or {}
            )

            # Select partition for load balancing
            partition = self._select_partition(message_id)
            queue_name = f"{self.queue_prefix}:{priority.value}:{partition}"

            # Serialize and compress message
            serialized_message = await self._serialize_message(message)

            # Enqueue with priority scoring
            priority_score = self._get_priority_score(priority)

            await self.redis.zadd(
                queue_name,
                {serialized_message: priority_score}
            )

            # Set TTL if specified
            if expires_in:
                await self.redis.expire(queue_name, expires_in)

            # Update metrics
            queue_metrics = self.metrics.get(queue_name)
            if queue_metrics:
                queue_metrics.total_messages += 1
                queue_metrics.queue_size = await self.redis.zcard(queue_name)

            processing_time = (time.time() - start_time) * 1000
            logger.debug(f"Message {message_id} enqueued in {processing_time:.2f}ms")

            # Publish notification for real-time processing
            await self._publish_queue_notification(queue_name, message_id)

            return message_id

        except Exception as e:
            logger.error(f"Failed to enqueue message: {str(e)}")
            raise

    async def dequeue_message(
        self,
        priority: Optional[MessagePriority] = None,
        partition: Optional[int] = None
    ) -> Optional[QueueMessage]:
        """
        Dequeue message with priority-based selection.

        Performance target: <25ms message retrieval.
        """
        try:
            # Determine queue to process
            if priority and partition is not None:
                queue_names = [f"{self.queue_prefix}:{priority.value}:{partition}"]
            elif priority:
                queue_names = [
                    f"{self.queue_prefix}:{priority.value}:{p}"
                    for p in range(self.partition_count)
                ]
            else:
                # Process all queues by priority order
                queue_names = []
                for p in [MessagePriority.IMMEDIATE, MessagePriority.HIGH,
                         MessagePriority.STANDARD, MessagePriority.LOW, MessagePriority.BATCH]:
                    for partition in range(self.partition_count):
                        queue_names.append(f"{self.queue_prefix}:{p.value}:{partition}")

            # Try to dequeue from each queue
            for queue_name in queue_names:
                # Get highest priority message
                result = await self.redis.zpopmax(queue_name, 1)
                if result:
                    serialized_message, score = result[0]
                    message = await self._deserialize_message(serialized_message)

                    # Update metrics
                    queue_metrics = self.metrics.get(queue_name)
                    if queue_metrics:
                        queue_metrics.processed_messages += 1
                        queue_metrics.queue_size = await self.redis.zcard(queue_name)

                    return message

            return None

        except Exception as e:
            logger.error(f"Failed to dequeue message: {str(e)}")
            return None

    async def _serialize_message(self, message: QueueMessage) -> bytes:
        """Serialize and optionally compress message."""
        # Convert to dict
        message_dict = asdict(message)

        # Handle datetime serialization
        message_dict['created_at'] = message.created_at.isoformat()
        if message.expires_at:
            message_dict['expires_at'] = message.expires_at.isoformat()

        # Serialize to JSON
        json_data = json.dumps(message_dict).encode('utf-8')

        # Compress if above threshold
        if len(json_data) > self.config["compression_threshold"]:
            compressed_data = zlib.compress(json_data)
            # Add compression marker
            return b'COMPRESSED:' + compressed_data

        return json_data

    async def _deserialize_message(self, data: bytes) -> QueueMessage:
        """Deserialize and decompress message."""
        # Check for compression
        if data.startswith(b'COMPRESSED:'):
            compressed_data = data[11:]  # Remove marker
            json_data = zlib.decompress(compressed_data)
        else:
            json_data = data

        # Parse JSON
        message_dict = json.loads(json_data.decode('utf-8'))

        # Handle datetime deserialization
        message_dict['created_at'] = datetime.fromisoformat(message_dict['created_at'])
        if message_dict.get('expires_at'):
            message_dict['expires_at'] = datetime.fromisoformat(message_dict['expires_at'])

        # Convert priority back to enum
        message_dict['priority'] = MessagePriority(message_dict['priority'])

        return QueueMessage(**message_dict)

    def _select_partition(self, message_id: str) -> int:
        """Select partition for message using consistent hashing."""
        return hash(message_id) % self.partition_count

    def _get_priority_score(self, priority: MessagePriority) -> float:
        """Get priority score for Redis sorted set."""
        priority_scores = {
            MessagePriority.IMMEDIATE: 1000000,
            MessagePriority.HIGH: 100000,
            MessagePriority.STANDARD: 10000,
            MessagePriority.LOW: 1000,
            MessagePriority.BATCH: 100
        }

        # Add timestamp for FIFO within same priority
        timestamp_score = time.time()
        return priority_scores[priority] + timestamp_score

    async def _publish_queue_notification(self, queue_name: str, message_id: str):
        """Publish notification for real-time message processing."""
        try:
            notification = {
                "queue_name": queue_name,
                "message_id": message_id,
                "timestamp": time.time()
            }

            channel = f"{self.pubsub_prefix}:notifications"
            await self.redis.publish(channel, json.dumps(notification))

        except Exception as e:
            logger.error(f"Failed to publish queue notification: {str(e)}")

    async def _process_queues(self):
        """Background task to process messages from all queues."""
        while True:
            try:
                # Process messages by priority
                for priority in [MessagePriority.IMMEDIATE, MessagePriority.HIGH,
                               MessagePriority.STANDARD, MessagePriority.LOW, MessagePriority.BATCH]:

                    # Process each partition
                    for partition in range(self.partition_count):
                        message = await self.dequeue_message(priority, partition)
                        if message:
                            await self._process_message(message)

                # Small delay to prevent CPU spinning
                await asyncio.sleep(0.01)  # 10ms

            except Exception as e:
                logger.error(f"Error in queue processing: {str(e)}")
                await asyncio.sleep(1)  # Longer delay on error

    async def _process_message(self, message: QueueMessage):
        """Process individual message with error handling and retries."""
        start_time = time.time()

        try:
            # Check if message has expired
            if message.expires_at and datetime.now(timezone.utc) > message.expires_at:
                logger.warning(f"Message {message.id} expired, moving to DLQ")
                await self._move_to_dlq(message, "Message expired")
                return

            # Get message handler
            handler = self.message_handlers.get(message.source)
            if not handler:
                logger.warning(f"No handler found for message source: {message.source}")
                await self._move_to_dlq(message, "No handler found")
                return

            # Process message with timeout
            try:
                await asyncio.wait_for(
                    handler(message),
                    timeout=self.config["processing_timeout"]
                )

                # Record successful processing
                processing_time = (time.time() - start_time) * 1000
                self.processing_times.append(processing_time)

                logger.debug(f"Message {message.id} processed successfully in {processing_time:.2f}ms")

            except asyncio.TimeoutError:
                logger.error(f"Message {message.id} processing timeout")
                await self._handle_message_failure(message, "Processing timeout")

            except Exception as e:
                logger.error(f"Message {message.id} processing failed: {str(e)}")
                await self._handle_message_failure(message, str(e))

        except Exception as e:
            logger.error(f"Error processing message {message.id}: {str(e)}")

    async def _handle_message_failure(self, message: QueueMessage, error: str):
        """Handle message processing failure with retry logic."""
        message.retry_count += 1
        message.metadata["last_error"] = error
        message.metadata["failed_at"] = datetime.now(timezone.utc).isoformat()

        if message.retry_count <= message.max_retries:
            # Retry with exponential backoff
            delay = self.config["retry_delay"] * (2 ** (message.retry_count - 1))

            logger.info(f"Retrying message {message.id} in {delay} seconds (attempt {message.retry_count})")

            # Re-enqueue with delay
            await asyncio.sleep(delay)
            await self._reenqueue_message(message)
        else:
            # Move to dead letter queue
            logger.error(f"Message {message.id} exceeded max retries, moving to DLQ")
            await self._move_to_dlq(message, f"Max retries exceeded: {error}")

    async def _reenqueue_message(self, message: QueueMessage):
        """Re-enqueue message for retry."""
        try:
            partition = self._select_partition(message.id)
            queue_name = f"{self.queue_prefix}:{message.priority.value}:{partition}"

            serialized_message = await self._serialize_message(message)
            priority_score = self._get_priority_score(message.priority)

            await self.redis.zadd(queue_name, {serialized_message: priority_score})

        except Exception as e:
            logger.error(f"Failed to re-enqueue message {message.id}: {str(e)}")

    async def _move_to_dlq(self, message: QueueMessage, reason: str):
        """Move message to dead letter queue."""
        try:
            partition = self._select_partition(message.id)
            dlq_name = f"{self.dlq_prefix}:{message.priority.value}:{partition}"

            # Add DLQ metadata
            message.metadata.update({
                "dlq_reason": reason,
                "dlq_timestamp": datetime.now(timezone.utc).isoformat(),
                "original_queue": f"{self.queue_prefix}:{message.priority.value}:{partition}"
            })

            serialized_message = await self._serialize_message(message)

            await self.redis.zadd(dlq_name, {serialized_message: time.time()})

            # Set TTL for DLQ
            await self.redis.expire(dlq_name, self.config["dlq_ttl"])

            # Update metrics
            dlq_metrics = self.metrics.get(dlq_name)
            if dlq_metrics:
                dlq_metrics.dead_letter_messages += 1

            logger.warning(f"Message {message.id} moved to DLQ: {reason}")

        except Exception as e:
            logger.error(f"Failed to move message {message.id} to DLQ: {str(e)}")

    async def _monitor_queues(self):
        """Background task to monitor queue health and performance."""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds

                for queue_name, metrics in self.metrics.items():
                    # Update queue size
                    metrics.queue_size = await self.redis.zcard(queue_name)

                    # Calculate oldest message age
                    oldest_messages = await self.redis.zrange(queue_name, 0, 0, withscores=True)
                    if oldest_messages:
                        oldest_score = oldest_messages[0][1]
                        metrics.oldest_message_age = time.time() - oldest_score

                    # Calculate messages per second
                    if len(self.processing_times) > 0:
                        metrics.messages_per_second = len(self.processing_times) / 60  # Per minute average

                    # Calculate average processing time
                    if self.processing_times:
                        metrics.average_processing_time = sum(self.processing_times) / len(self.processing_times)

                    metrics.last_updated = datetime.now(timezone.utc)

                # Check for queue health issues
                await self._check_queue_health()

            except Exception as e:
                logger.error(f"Error in queue monitoring: {str(e)}")

    async def _check_queue_health(self):
        """Check queue health and trigger alerts if needed."""
        for queue_name, metrics in self.metrics.items():
            status = QueueStatus.HEALTHY

            # Check for critical conditions
            if (metrics.queue_size > self.config["max_queue_size"] * 0.9 or
                metrics.oldest_message_age > 300):  # 5 minutes
                status = QueueStatus.CRITICAL
            elif (metrics.queue_size > self.config["max_queue_size"] * 0.7 or
                  metrics.oldest_message_age > 120):  # 2 minutes
                status = QueueStatus.WARNING

            # Log health issues
            if status != QueueStatus.HEALTHY:
                logger.warning(f"Queue {queue_name} health: {status.value}")
                logger.warning(f"Queue metrics: {asdict(metrics)}")

    async def _cleanup_expired_messages(self):
        """Background task to cleanup expired messages."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes

                current_time = time.time()

                # Cleanup expired messages from all queues
                for priority in MessagePriority:
                    for partition in range(self.partition_count):
                        queue_name = f"{self.queue_prefix}:{priority.value}:{partition}"

                        # Remove messages older than TTL
                        expired_before = current_time - self.config["message_ttl"]
                        removed = await self.redis.zremrangebyscore(queue_name, 0, expired_before)

                        if removed > 0:
                            logger.info(f"Cleaned up {removed} expired messages from {queue_name}")

                # Cleanup old DLQ messages
                for priority in MessagePriority:
                    for partition in range(self.partition_count):
                        dlq_name = f"{self.dlq_prefix}:{priority.value}:{partition}"

                        # Remove DLQ messages older than DLQ TTL
                        dlq_expired_before = current_time - self.config["dlq_ttl"]
                        removed = await self.redis.zremrangebyscore(dlq_name, 0, dlq_expired_before)

                        if removed > 0:
                            logger.info(f"Cleaned up {removed} expired DLQ messages from {dlq_name}")

            except Exception as e:
                logger.error(f"Error in message cleanup: {str(e)}")

    def register_message_handler(self, source: str, handler: callable):
        """Register message handler for specific source."""
        self.message_handlers[source] = handler
        logger.info(f"Registered message handler for source: {source}")

    async def get_queue_status(self) -> Dict[str, Any]:
        """Get comprehensive queue status and metrics."""
        return {
            "queues": {name: asdict(metrics) for name, metrics in self.metrics.items()},
            "configuration": self.config,
            "processing_stats": {
                "total_handlers": len(self.message_handlers),
                "active_tasks": len(self.processing_tasks),
                "average_processing_time": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
                "messages_per_second": len(self.processing_times) / 60 if self.processing_times else 0
            }
        }

    async def shutdown(self):
        """Graceful shutdown of message queue service."""
        logger.info("Shutting down Redis message queue service...")

        # Cancel background tasks
        for task in self._background_tasks:
            task.cancel()

        # Wait for current processing to complete
        if self.processing_tasks:
            await asyncio.gather(*self.processing_tasks.values(), return_exceptions=True)

        # Close Redis connection
        if self.redis:
            await self.redis.close()

        logger.info("Redis message queue service shutdown complete")


# Global message queue service instance - will be created when needed
message_queue_service = None


def get_message_queue_service(redis_client: Optional[Redis] = None, cache_manager: Optional[CacheManager] = None) -> RedisMessageQueueService:
    """Get or create global message queue service instance."""
    global message_queue_service
    if message_queue_service is None:
        message_queue_service = RedisMessageQueueService(redis_client, cache_manager)
    return message_queue_service
