"""
Main API router for version 1.

This module aggregates all v1 API endpoints and provides a single router
for the FastAPI application to include.
"""

from fastapi import APIRouter

# Import endpoint routers (fully restored)
from .endpoints import health, auth, users, email, payments, webhooks, geolocation, vendors, bookings
from .endpoints import push_notifications, experiences, marketplace_optimization, availability, booking_communication
from .endpoints import reviews, review_responses, review_moderation, review_analytics
from .endpoints import websocket_endpoints, sync_endpoints, tasks
from .endpoints import analytics, dashboard, reporting, scaling_endpoints, cdn_endpoints
from .endpoints import vendor_dashboard, advanced_search
from . import performance_monitoring
# TODO: Import promotional endpoints when implemented
# from .endpoints import promotional

# Create main v1 API router
api_router = APIRouter()

# Include endpoint routers (gradually re-enabling for debugging)
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(email.router, prefix="/email", tags=["email"])

# Payment and webhook routers (Task 4.3.1)
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
api_router.include_router(webhooks.router, prefix="/webhooks", tags=["webhooks"])

# Geolocation enhancement routers (Phase 2.4.1)
api_router.include_router(geolocation.router, prefix="/geolocation", tags=["geolocation"])

# Vendor and booking routers
api_router.include_router(vendors.router, prefix="/vendors", tags=["vendors"])
api_router.include_router(bookings.router, prefix="/bookings", tags=["bookings"])
api_router.include_router(availability.router, prefix="/availability", tags=["availability"])
api_router.include_router(booking_communication.router, prefix="/booking-communication", tags=["booking-communication"])

# Additional service routers (Group 1: Push notifications, Experiences, Marketplace optimization)
api_router.include_router(push_notifications.router, prefix="/push-notifications", tags=["push-notifications"])
api_router.include_router(experiences.router, prefix="/experiences", tags=["experiences"])
api_router.include_router(marketplace_optimization.router, prefix="/marketplace-optimization", tags=["marketplace-optimization"])

# Review management routers (Group 2: Task 4.4.1 Phase 5)
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(review_responses.router, prefix="/review-responses", tags=["review-responses"])
api_router.include_router(review_moderation.router, prefix="/admin/moderation", tags=["review-moderation"])
api_router.include_router(review_analytics.router, prefix="/analytics", tags=["review-analytics"])

# WebSocket and Real-time routers (Group 3: sync_endpoints still has issues - investigate separately)
api_router.include_router(websocket_endpoints.router, prefix="/websocket", tags=["websocket"])
api_router.include_router(sync_endpoints.router, prefix="/sync", tags=["synchronization"])  # ✅ CallableSchema errors resolved

# Task Management routers (Task 6.2.1 Phase 3) - Testing after CallableSchema fix
api_router.include_router(tasks.router, prefix="/tasks", tags=["task-management"])

# Analytics & Performance System routers (Group 4: Phase 7.1 - Analytics Dashboard System)
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(reporting.router, prefix="/reporting", tags=["reporting"])

# Performance Monitoring System routers (Phase 7.2.3 - Performance Monitoring System)
api_router.include_router(performance_monitoring.router, prefix="/performance", tags=["performance-monitoring"])

# Horizontal Scaling & Load Balancing routers (Group 5: Phase 7.3.3 - Horizontal Scaling & Load Balancing)
api_router.include_router(scaling_endpoints.router, prefix="/scaling", tags=["scaling-load-balancing"])

# CDN Optimization & Asset Delivery routers (Group 6: Phase 7.3.4 - CDN Optimization & Asset Delivery)
api_router.include_router(cdn_endpoints.router, prefix="/cdn", tags=["cdn"])

# Vendor Dashboard & Advanced Search routers (Group 7: Task 3.2 & 3.3)
api_router.include_router(vendor_dashboard.router, prefix="/vendor-dashboard", tags=["vendor-dashboard"])
api_router.include_router(advanced_search.router, prefix="/search", tags=["advanced-search"])

# Promotional system routers (Phase 5 - Promotional & Advertising System)
# TODO: Uncomment when promotional endpoints are implemented
# api_router.include_router(promotional.campaigns_router, prefix="/campaigns", tags=["campaigns"])
# api_router.include_router(promotional.advertisements_router, prefix="/advertisements", tags=["advertisements"])
# api_router.include_router(promotional.metrics_router, prefix="/metrics", tags=["metrics"])
# api_router.include_router(promotional.listings_router, prefix="/promotional-listings", tags=["promotional-listings"])
# api_router.include_router(promotional.spend_router, prefix="/ad-spend", tags=["ad-spend"])

# Additional routers will be added as we implement them:
# api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
# api_router.include_router(admin.router, prefix="/admin", tags=["admin"])

__all__ = ["api_router"]
