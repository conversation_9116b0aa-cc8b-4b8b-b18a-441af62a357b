"""
Review Analytics API endpoints for Culture Connect Backend API.

This module provides comprehensive review analytics endpoints including:
- Vendor performance metrics and trend analysis
- Platform-wide analytics and business intelligence
- Bulk analytics processing and automated reporting
- Role-based access control for analytics data

Implements Task 4.4.1 Phase 5 requirements for review analytics API endpoints with
production-grade FastAPI integration, authentication, rate limiting, and error handling.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.review_analytics_service import ReviewAnalyticsService
from app.schemas.review_schemas import ReviewAnalyticsSchema, ReviewSummarySchema
from app.repositories.base import PaginationParams
from app.api.v1.auth import get_current_user
from app.core.permissions import get_current_admin_user
from app.core.security import UserRole
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.get(
    "/vendors/{vendor_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get vendor analytics",
    description="Get comprehensive analytics for a specific vendor including performance metrics and trends."
)
async def get_vendor_analytics(
    vendor_id: int = Path(..., description="Vendor ID", gt=0),
    period_days: Optional[int] = Query(30, description="Analysis period in days", ge=1, le=365),
    include_report: bool = Query(False, description="Include detailed report generation"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> Dict[str, Any]:
    """
    Get comprehensive analytics for a specific vendor.

    **Authorization:**
    - Vendors can only access their own analytics
    - Admins can access any vendor's analytics
    - Customers cannot access vendor analytics

    **Performance Target:** <500ms for analytics calculation, <1000ms with report generation

    Args:
        vendor_id: Vendor ID to get analytics for
        period_days: Analysis period in days (default: 30, max: 365)
        include_report: Whether to include detailed report generation
        current_user: Current authenticated user
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        Dict[str, Any]: Comprehensive vendor analytics with optional detailed report

    Raises:
        HTTPException: 403 if not authorized, 404 if vendor not found, 422 if parameters invalid
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Authorization check
        user_role = current_user.get("role")
        if user_role == UserRole.VENDOR.value and current_user["id"] != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vendors can only access their own analytics"
            )
        elif user_role == UserRole.CUSTOMER.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Customers cannot access vendor analytics"
            )

        # Validate period
        if period_days > 365:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Analysis period cannot exceed 365 days"
            )

        # Initialize analytics service
        analytics_service = ReviewAnalyticsService(db)

        # Calculate period dates
        period_end = date.today()
        period_start = period_end - timedelta(days=period_days)

        # Get vendor analytics
        if include_report:
            # Generate comprehensive report
            analytics = await analytics_service.generate_vendor_report(
                vendor_id=vendor_id,
                period_days=period_days
            )
        else:
            # Calculate basic analytics
            analytics_record = await analytics_service.calculate_vendor_analytics(
                vendor_id=vendor_id,
                period_start=period_start,
                period_end=period_end,
                save_to_db=False
            )

            # Convert to response format
            analytics = {
                "vendor_id": vendor_id,
                "period": {
                    "start_date": period_start,
                    "end_date": period_end,
                    "days": period_days
                },
                "metrics": {
                    "total_reviews": analytics_record.total_reviews,
                    "average_rating": float(analytics_record.average_rating) if analytics_record.average_rating else 0.0,
                    "rating_distribution": analytics_record.rating_distribution or {},
                    "sentiment_breakdown": analytics_record.sentiment_breakdown or {},
                    "response_rate": float(analytics_record.response_rate) if analytics_record.response_rate else 0.0,
                    "verified_reviews_count": analytics_record.verified_reviews_count,
                    "helpful_votes_total": analytics_record.helpful_votes_total,
                    "rating_trend": analytics_record.rating_trend,
                    "review_volume_trend": analytics_record.review_volume_trend
                },
                "generated_at": datetime.now()
            }

        # Track metrics
        metrics_collector.increment_counter(
            "api_vendor_analytics_accessed",
            tags={"vendor_id": str(vendor_id), "user_role": user_role, "include_report": str(include_report)}
        )

        logger.info(
            f"Vendor analytics retrieved",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "vendor_id": vendor_id,
                "period_days": period_days,
                "include_report": include_report
            }
        )

        return analytics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get vendor analytics failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vendor {vendor_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get vendor analytics"
        )


@router.post(
    "/bulk-update",
    response_model=Dict[str, Any],
    status_code=status.HTTP_202_ACCEPTED,
    summary="Bulk update analytics",
    description="Trigger bulk analytics update for multiple vendors. Admin access only."
)
async def bulk_update_analytics(
    background_tasks: BackgroundTasks,
    update_data: Dict[str, Any] = None,
    current_user: dict = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> Dict[str, Any]:
    """
    Trigger bulk analytics update for multiple vendors.

    **Requirements:**
    - Admin role required
    - Processes analytics for specified vendors or all vendors
    - Runs as background task for performance
    - Returns processing status and estimated completion time

    **Performance Target:** >1000 records/second for bulk processing

    Args:
        background_tasks: FastAPI background tasks for async processing
        update_data: Bulk update configuration with vendor_ids and period
        current_user: Current authenticated admin user
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        Dict[str, Any]: Bulk processing status and metadata

    Raises:
        HTTPException: 403 if not admin, 422 if parameters invalid, 500 if processing fails
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate update data
        if not update_data:
            update_data = {}

        vendor_ids = update_data.get("vendor_ids", [])
        period_days = update_data.get("period_days", 30)

        # Validate parameters
        if period_days > 365:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Analysis period cannot exceed 365 days"
            )

        # If no vendor IDs specified, get all vendors (this would require a vendor service)
        if not vendor_ids:
            # For now, return error requiring explicit vendor IDs
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Vendor IDs must be specified for bulk update"
            )

        # Validate vendor IDs
        if not isinstance(vendor_ids, list) or not all(isinstance(vid, int) and vid > 0 for vid in vendor_ids):
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Vendor IDs must be a list of positive integers"
            )

        # Calculate period dates
        period_end = date.today()
        period_start = period_end - timedelta(days=period_days)

        # Initialize analytics service
        analytics_service = ReviewAnalyticsService(db)

        # Add bulk update task to background
        async def bulk_update_task():
            try:
                result = await analytics_service.bulk_update_analytics(
                    vendor_ids=vendor_ids,
                    period_start=period_start,
                    period_end=period_end
                )

                logger.info(
                    f"Bulk analytics update completed",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "admin_id": current_user["id"],
                        "vendor_count": len(vendor_ids),
                        "processed_count": result.get("processed_count", 0),
                        "processing_time": result.get("processing_time_seconds", 0)
                    }
                )
            except Exception as e:
                logger.error(
                    f"Bulk analytics update failed",
                    extra={
                        "correlation_id": correlation_id.get(''),
                        "admin_id": current_user["id"],
                        "vendor_count": len(vendor_ids),
                        "error": str(e)
                    }
                )

        background_tasks.add_task(bulk_update_task)

        # Track metrics
        metrics_collector.increment_counter(
            "api_bulk_analytics_triggered",
            tags={"admin_id": str(current_user["id"]), "vendor_count": str(len(vendor_ids))}
        )

        # Estimate completion time (rough calculation)
        estimated_seconds = len(vendor_ids) * 0.5  # Assume 0.5 seconds per vendor

        response = {
            "message": "Bulk analytics update started",
            "status": "processing",
            "vendor_count": len(vendor_ids),
            "period": {
                "start_date": period_start,
                "end_date": period_end,
                "days": period_days
            },
            "estimated_completion_seconds": estimated_seconds,
            "started_at": datetime.now(),
            "correlation_id": correlation_id.get('')
        }

        logger.info(
            f"Bulk analytics update triggered",
            extra={
                "correlation_id": correlation_id.get(''),
                "admin_id": current_user["id"],
                "vendor_count": len(vendor_ids),
                "period_days": period_days
            }
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bulk update analytics failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger bulk analytics update"
        )


@router.get(
    "/platform-summary",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get platform analytics summary",
    description="Get platform-wide analytics summary with cross-vendor insights. Admin access only."
)
async def get_platform_analytics_summary(
    date_from: Optional[date] = Query(None, description="Filter from date"),
    date_to: Optional[date] = Query(None, description="Filter to date"),
    current_user: dict = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> Dict[str, Any]:
    """
    Get platform-wide analytics summary with cross-vendor insights.

    **Requirements:**
    - Admin role required
    - Provides aggregated platform metrics
    - Includes vendor performance comparisons
    - Optional date range filtering

    **Performance Target:** <200ms for platform summary queries

    Args:
        date_from: Optional start date filter
        date_to: Optional end date filter
        current_user: Current authenticated admin user
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        Dict[str, Any]: Platform-wide analytics summary

    Raises:
        HTTPException: 403 if not admin, 422 if date range invalid, 500 if query fails
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate date range
        date_range = None
        if date_from or date_to:
            if date_from and date_to and date_from > date_to:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="Start date must be before end date"
                )
            date_range = (date_from, date_to)

        # Initialize analytics service
        analytics_service = ReviewAnalyticsService(db)

        # Get platform analytics summary
        summary = await analytics_service.get_platform_analytics_summary(date_range=date_range)

        logger.info(
            f"Platform analytics summary retrieved",
            extra={
                "correlation_id": correlation_id.get(''),
                "admin_id": current_user["id"],
                "date_from": date_from,
                "date_to": date_to,
                "active_vendors": summary.get("active_vendors", 0)
            }
        )

        return summary

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get platform analytics summary failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get platform analytics summary"
        )


@router.get(
    "/vendors/{vendor_id}/report",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Generate vendor report",
    description="Generate comprehensive vendor performance report with trends and recommendations."
)
async def generate_vendor_report(
    vendor_id: int = Path(..., description="Vendor ID", gt=0),
    period_days: Optional[int] = Query(30, description="Report period in days", ge=1, le=365),
    format_type: str = Query("json", description="Report format (json, pdf)", regex="^(json|pdf)$"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> Dict[str, Any]:
    """
    Generate comprehensive vendor performance report.

    **Authorization:**
    - Vendors can only generate reports for their own data
    - Admins can generate reports for any vendor

    **Performance Target:** <1000ms for complete report generation

    Args:
        vendor_id: Vendor ID to generate report for
        period_days: Report period in days (default: 30, max: 365)
        format_type: Report format (json or pdf)
        current_user: Current authenticated user
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        Dict[str, Any]: Comprehensive vendor report with trends and recommendations

    Raises:
        HTTPException: 403 if not authorized, 404 if vendor not found, 422 if parameters invalid
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Authorization check
        user_role = current_user.get("role")
        if user_role == UserRole.VENDOR.value and current_user["id"] != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vendors can only generate reports for their own data"
            )
        elif user_role == UserRole.CUSTOMER.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Customers cannot generate vendor reports"
            )

        # Validate parameters
        if period_days > 365:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Report period cannot exceed 365 days"
            )

        # Initialize analytics service
        analytics_service = ReviewAnalyticsService(db)

        # Generate vendor report
        report = await analytics_service.generate_vendor_report(
            vendor_id=vendor_id,
            period_days=period_days
        )

        # Track metrics
        metrics_collector.increment_counter(
            "api_vendor_report_generated",
            tags={"vendor_id": str(vendor_id), "user_role": user_role, "format": format_type}
        )

        logger.info(
            f"Vendor report generated",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "vendor_id": vendor_id,
                "period_days": period_days,
                "format_type": format_type
            }
        )

        # For PDF format, would need to implement PDF generation
        if format_type == "pdf":
            # TODO: Implement PDF generation
            report["format"] = "pdf"
            report["download_url"] = f"/api/v1/analytics/vendors/{vendor_id}/report.pdf"

        return report

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Generate vendor report failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vendor {vendor_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate vendor report"
        )
