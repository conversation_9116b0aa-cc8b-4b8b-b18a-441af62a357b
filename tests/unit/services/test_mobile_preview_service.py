"""
Unit tests for MobilePreviewService.

This module provides comprehensive unit tests for the mobile preview service
implementing Task 3.2.2 Phase 5 requirements with >80% test coverage.

Test Coverage:
- Mobile optimization analysis
- Mobile preview generation
- Device-specific optimization
- Mobile insights and recommendations
- Error handling and edge cases
- Service method validation
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Dict, Any

from app.services.mobile_preview_service import MobilePreviewService
from app.schemas.marketplace_optimization import MobileOptimizationResponse


class TestableMobilePreviewService(MobilePreviewService):
    """Testable version of MobilePreviewService with implemented abstract methods."""
    
    async def validate_create_data(self, data: dict) -> dict:
        """Mock implementation of abstract method."""
        return data
    
    async def validate_update_data(self, data: dict, record_id: int = None) -> dict:
        """Mock implementation of abstract method."""
        return data


class TestMobilePreviewService:
    """Test suite for MobilePreviewService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_db_session):
        """Create MobilePreviewService instance with mocked dependencies."""
        return TestableMobilePreviewService(mock_db_session)

    @pytest.fixture
    def sample_service_data(self):
        """Sample service data for testing."""
        return MagicMock(
            id=1,
            title="Cultural Heritage Tour in Lagos",
            description="Experience authentic Nigerian culture with our guided heritage tour through historic Lagos. Discover traditional markets, ancient architecture, and local customs.",
            vendor_id=1,
            category_id=1,
            media_urls=["image1.jpg", "image2.jpg", "video1.mp4"],
            price=Decimal("50.00"),
            created_at=datetime.now() - timedelta(days=30)
        )

    @pytest.fixture
    def sample_mobile_optimization(self):
        """Sample mobile optimization response."""
        return MobileOptimizationResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            device_type="mobile",
            screen_size_category="standard",
            overall_mobile_score=Decimal("78.50"),
            responsive_design_score=Decimal("85.00"),
            loading_speed_score=Decimal("72.00"),
            touch_interface_score=Decimal("80.00"),
            image_optimization_score=Decimal("75.00"),
            navigation_score=Decimal("82.00"),
            mobile_readiness_score=Decimal("79.00"),
            core_web_vitals={"lcp": 2.1, "fid": 85, "cls": 0.08},
            optimization_recommendations=[
                "Optimize image sizes for mobile",
                "Improve loading speed",
                "Enhance touch targets"
            ],
            analysis_date=datetime.now(),
            analysis_version="1.0"
        )

    @pytest.mark.asyncio
    async def test_analyze_mobile_optimization_success(self, service, sample_service_data):
        """Test successful mobile optimization analysis."""
        service_id = 1
        device_type = "mobile"
        screen_size_category = "standard"
        force_refresh = False

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_mobile_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock no existing analysis
            mock_mobile_repo.get_latest_by_service_and_device.return_value = None

            # Execute method
            result = await service.analyze_mobile_optimization(
                service_id, device_type, screen_size_category, force_refresh
            )

            # Assertions
            assert isinstance(result, MobileOptimizationResponse)
            assert result.service_id == service_id
            assert result.vendor_id == sample_service_data.vendor_id
            assert result.device_type == device_type
            assert result.screen_size_category == screen_size_category
            assert 0 <= result.overall_mobile_score <= 100
            assert 0 <= result.responsive_design_score <= 100
            assert 0 <= result.loading_speed_score <= 100
            assert 0 <= result.touch_interface_score <= 100
            assert isinstance(result.optimization_recommendations, list)

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)
            mock_mobile_repo.get_latest_by_service_and_device.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_mobile_optimization_existing_analysis_no_refresh(self, service, sample_mobile_optimization):
        """Test mobile optimization analysis returns existing analysis when not forcing refresh."""
        service_id = 1
        device_type = "mobile"
        screen_size_category = "standard"
        force_refresh = False

        with patch.object(service, 'repository') as mock_mobile_repo:
            # Mock existing recent analysis
            mock_mobile_repo.get_latest_by_service_and_device.return_value = sample_mobile_optimization

            # Execute method
            result = await service.analyze_mobile_optimization(
                service_id, device_type, screen_size_category, force_refresh
            )

            # Assertions
            assert result == sample_mobile_optimization
            mock_mobile_repo.get_latest_by_service_and_device.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_mobile_optimization_force_refresh(self, service, sample_service_data, sample_mobile_optimization):
        """Test mobile optimization analysis with force refresh creates new analysis."""
        service_id = 1
        device_type = "mobile"
        screen_size_category = "standard"
        force_refresh = True

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'repository') as mock_mobile_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock existing analysis (should be ignored due to force_refresh)
            mock_mobile_repo.get_latest_by_service_and_device.return_value = sample_mobile_optimization

            # Execute method
            result = await service.analyze_mobile_optimization(
                service_id, device_type, screen_size_category, force_refresh
            )

            # Assertions
            assert isinstance(result, MobileOptimizationResponse)
            assert result.service_id == service_id
            # Should create new analysis, not return existing one
            assert result.analysis_date != sample_mobile_optimization.analysis_date

    @pytest.mark.asyncio
    async def test_analyze_mobile_optimization_service_not_found(self, service):
        """Test mobile optimization analysis when service not found."""
        service_id = 999
        device_type = "mobile"
        screen_size_category = "standard"

        with patch.object(service, 'service_repository') as mock_service_repo:
            mock_service_repo.get_by_id.return_value = None

            with pytest.raises(Exception) as exc_info:
                await service.analyze_mobile_optimization(service_id, device_type, screen_size_category, False)
            
            assert "not found" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_generate_mobile_preview_success(self, service, sample_service_data):
        """Test successful mobile preview generation."""
        service_id = 1
        device_types = ["mobile", "tablet"]

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'preview_generator') as mock_preview_gen:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock preview generation
            mock_preview_gen.generate_device_preview.return_value = {
                "preview_url": "https://example.com/preview.png",
                "optimization_score": 78.5,
                "device_specific_issues": ["small touch targets", "slow loading"]
            }

            # Execute method
            result = await service.generate_mobile_preview(service_id, device_types)

            # Assertions
            assert isinstance(result, dict)
            assert "service_id" in result
            assert "device_previews" in result
            assert "mobile_readiness_score" in result
            assert "optimization_recommendations" in result
            assert "cross_device_comparison" in result

            # Verify device previews
            device_previews = result["device_previews"]
            assert len(device_previews) == len(device_types)
            for device_type in device_types:
                assert device_type in device_previews
                assert "preview_url" in device_previews[device_type]
                assert "optimization_score" in device_previews[device_type]

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_generate_mobile_preview_single_device(self, service, sample_service_data):
        """Test mobile preview generation for single device type."""
        service_id = 1
        device_types = ["mobile"]

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'preview_generator') as mock_preview_gen:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock preview generation
            mock_preview_gen.generate_device_preview.return_value = {
                "preview_url": "https://example.com/mobile_preview.png",
                "optimization_score": 82.0,
                "device_specific_issues": []
            }

            # Execute method
            result = await service.generate_mobile_preview(service_id, device_types)

            # Assertions
            assert isinstance(result, dict)
            assert len(result["device_previews"]) == 1
            assert "mobile" in result["device_previews"]

    @pytest.mark.asyncio
    async def test_get_mobile_optimization_insights_success(self, service, sample_service_data):
        """Test successful mobile optimization insights generation."""
        service_id = 1
        days = 30

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'analytics_repository') as mock_analytics_repo, \
             patch.object(service, 'repository') as mock_mobile_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = sample_service_data
            
            # Mock mobile analytics data
            mock_analytics_repo.get_mobile_analytics.return_value = {
                "mobile_traffic_percentage": 75.0,
                "mobile_conversion_rate": 8.5,
                "mobile_bounce_rate": 35.0,
                "average_mobile_session_duration": 120.0
            }
            
            # Mock optimization history
            mock_mobile_repo.get_optimization_history.return_value = [
                {"date": date.today() - timedelta(days=i), "score": 70 + i}
                for i in range(30)
            ]

            # Execute method
            result = await service.get_mobile_optimization_insights(service_id, days)

            # Assertions
            assert isinstance(result, dict)
            assert "service_id" in result
            assert "mobile_performance_trends" in result
            assert "optimization_opportunities" in result
            assert "mobile_benchmarks" in result
            assert "actionable_insights" in result
            assert "improvement_potential" in result

            # Verify mobile performance trends
            trends = result["mobile_performance_trends"]
            assert "traffic_trends" in trends
            assert "conversion_trends" in trends
            assert "optimization_score_trends" in trends

            # Verify optimization opportunities
            opportunities = result["optimization_opportunities"]
            assert isinstance(opportunities, list)
            assert len(opportunities) > 0

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)
            mock_analytics_repo.get_mobile_analytics.assert_called_once()

    @pytest.mark.asyncio
    async def test_calculate_mobile_scores(self, service, sample_service_data):
        """Test mobile scores calculation."""
        # Execute method (assuming this is a testable method)
        scores = service._calculate_mobile_scores(sample_service_data, "mobile", "standard")

        # Assertions
        assert isinstance(scores, dict)
        assert "overall_mobile_score" in scores
        assert "responsive_design_score" in scores
        assert "loading_speed_score" in scores
        assert "touch_interface_score" in scores
        assert "image_optimization_score" in scores
        assert "navigation_score" in scores

        # Verify score ranges
        for score_key, score_value in scores.items():
            assert 0 <= score_value <= 100

    @pytest.mark.asyncio
    async def test_analyze_responsive_design(self, service):
        """Test responsive design analysis."""
        content_data = {
            "title": "Cultural Heritage Tour",
            "description": "Experience authentic culture",
            "media_urls": ["image1.jpg", "image2.jpg"],
            "layout_structure": "grid"
        }

        # Execute method (assuming this is a testable method)
        responsive_score = service._analyze_responsive_design(content_data, "mobile")

        # Assertions
        assert isinstance(responsive_score, (int, float, Decimal))
        assert 0 <= responsive_score <= 100

    @pytest.mark.asyncio
    async def test_analyze_loading_speed(self, service):
        """Test loading speed analysis."""
        content_data = {
            "media_urls": ["image1.jpg", "image2.jpg", "video1.mp4"],
            "content_length": 1500,
            "external_resources": 3
        }

        # Execute method (assuming this is a testable method)
        loading_score = service._analyze_loading_speed(content_data, "mobile")

        # Assertions
        assert isinstance(loading_score, (int, float, Decimal))
        assert 0 <= loading_score <= 100

    @pytest.mark.asyncio
    async def test_analyze_touch_interface(self, service):
        """Test touch interface analysis."""
        interface_data = {
            "button_count": 5,
            "link_count": 8,
            "form_elements": 2,
            "navigation_type": "hamburger"
        }

        # Execute method (assuming this is a testable method)
        touch_score = service._analyze_touch_interface(interface_data, "mobile")

        # Assertions
        assert isinstance(touch_score, (int, float, Decimal))
        assert 0 <= touch_score <= 100

    @pytest.mark.asyncio
    async def test_generate_optimization_recommendations(self, service):
        """Test optimization recommendations generation."""
        analysis_results = {
            "responsive_design_score": 60.0,
            "loading_speed_score": 45.0,
            "touch_interface_score": 80.0,
            "image_optimization_score": 55.0,
            "navigation_score": 75.0
        }

        # Execute method (assuming this is a testable method)
        recommendations = service._generate_optimization_recommendations(analysis_results, "mobile")

        # Assertions
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0

        # Should prioritize low-scoring areas
        recommendation_text = " ".join(recommendations).lower()
        assert "loading" in recommendation_text or "speed" in recommendation_text
        assert "image" in recommendation_text or "optimization" in recommendation_text

    @pytest.mark.asyncio
    async def test_calculate_core_web_vitals(self, service):
        """Test Core Web Vitals calculation."""
        performance_data = {
            "content_size": 2.5,  # MB
            "image_count": 8,
            "script_count": 3,
            "css_count": 2
        }

        # Execute method (assuming this is a testable method)
        vitals = service._calculate_core_web_vitals(performance_data, "mobile")

        # Assertions
        assert isinstance(vitals, dict)
        assert "lcp" in vitals  # Largest Contentful Paint
        assert "fid" in vitals  # First Input Delay
        assert "cls" in vitals  # Cumulative Layout Shift

        # Verify reasonable ranges
        assert vitals["lcp"] > 0  # Should be positive seconds
        assert vitals["fid"] >= 0  # Should be non-negative milliseconds
        assert vitals["cls"] >= 0  # Should be non-negative score

    @pytest.mark.asyncio
    async def test_compare_device_performance(self, service):
        """Test device performance comparison."""
        device_results = {
            "mobile": {"optimization_score": 75.0, "loading_speed": 3.2},
            "tablet": {"optimization_score": 82.0, "loading_speed": 2.8},
            "desktop": {"optimization_score": 88.0, "loading_speed": 2.1}
        }

        # Execute method (assuming this is a testable method)
        comparison = service._compare_device_performance(device_results)

        # Assertions
        assert isinstance(comparison, dict)
        assert "best_performing_device" in comparison
        assert "performance_gaps" in comparison
        assert "optimization_priorities" in comparison

        # Verify best performing device
        assert comparison["best_performing_device"] == "desktop"

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db_session):
        """Test service initialization with dependencies."""
        service = TestableMobilePreviewService(mock_db_session)
        
        # Verify service is properly initialized
        assert service._db_session == mock_db_session
        assert hasattr(service, 'service_repository')
        assert hasattr(service, 'analytics_repository')
        assert hasattr(service, 'repository')
        assert hasattr(service, 'preview_generator')

    @pytest.mark.asyncio
    async def test_error_handling_database_error(self, service):
        """Test error handling for database errors."""
        service_id = 1
        device_type = "mobile"
        screen_size_category = "standard"
        
        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock database error
            mock_service_repo.get_by_id.side_effect = Exception("Database connection error")

            with pytest.raises(Exception) as exc_info:
                await service.analyze_mobile_optimization(service_id, device_type, screen_size_category, False)
            
            assert "Database connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_mobile_optimization_different_screen_sizes(self, service, sample_service_data):
        """Test mobile optimization analysis for different screen sizes."""
        service_id = 1
        device_type = "mobile"
        screen_sizes = ["small", "standard", "large"]

        for screen_size in screen_sizes:
            with patch.object(service, 'service_repository') as mock_service_repo, \
                 patch.object(service, 'repository') as mock_mobile_repo:

                # Mock service exists
                mock_service_repo.get_by_id.return_value = sample_service_data
                mock_mobile_repo.get_latest_by_service_and_device.return_value = None

                # Execute method
                result = await service.analyze_mobile_optimization(service_id, device_type, screen_size, False)

                # Assertions
                assert isinstance(result, MobileOptimizationResponse)
                assert result.screen_size_category == screen_size
                assert result.device_type == device_type
