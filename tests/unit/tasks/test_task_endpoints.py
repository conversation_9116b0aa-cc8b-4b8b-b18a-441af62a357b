"""
Integration tests for task management API endpoints.

This module provides comprehensive testing for:
- Task submission and management endpoints
- Authentication and authorization
- Request/response validation
- Error handling and status codes
- API performance and reliability

Implements Task 6.2.1 testing requirements with >85% code coverage
and 100% test success rate validation.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4
from fastapi import status
from httpx import AsyncClient

from app.main import app
from app.models.user import User
from app.models.task_models import TaskStatus, TaskPriority
from app.schemas.task_schemas import (
    TaskSubmissionRequest, TaskSubmissionResponse, TaskStatusResponse,
    BulkTaskSubmissionRequest, SystemMetricsResponse
)


class TestTaskEndpoints:
    """Test cases for task management API endpoints."""

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user with task permissions."""
        user = Mock(spec=User)
        user.id = uuid4()
        user.email = "<EMAIL>"
        user.is_active = True
        return user

    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer mock-jwt-token"}

    @pytest.mark.asyncio
    async def test_submit_task_success(self, auth_headers):
        """Test successful task submission via API."""
        task_request = {
            "task_name": "test.sample_task",
            "args": ["arg1", "arg2"],
            "kwargs": {"key1": "value1"},
            "priority": "standard",
            "max_retries": 3
        }
        
        mock_response = TaskSubmissionResponse(
            id=uuid4(),
            task_id="test-task-123",
            task_name="test.sample_task",
            queue_name="default",
            priority=TaskPriority.STANDARD,
            status=TaskStatus.PENDING,
            submitted_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            # Setup mocks
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.submit_task.return_value = mock_response
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post(
                    "/api/v1/tasks/submit",
                    json=task_request,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["task_id"] == "test-task-123"
            assert data["task_name"] == "test.sample_task"
            assert data["status"] == "pending"
            assert data["priority"] == "standard"

    @pytest.mark.asyncio
    async def test_submit_task_validation_error(self, auth_headers):
        """Test task submission with validation errors."""
        # Missing required task_name
        invalid_request = {
            "args": ["arg1"],
            "priority": "standard"
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post(
                    "/api/v1/tasks/submit",
                    json=invalid_request,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_submit_task_unauthorized(self):
        """Test task submission without authentication."""
        task_request = {
            "task_name": "test.sample_task",
            "priority": "standard"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/tasks/submit",
                json=task_request
            )
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_get_task_status_success(self, auth_headers):
        """Test successful task status retrieval."""
        task_id = "test-task-123"
        
        mock_response = TaskStatusResponse(
            task_id=task_id,
            task_name="test.sample_task",
            status=TaskStatus.SUCCESS,
            submitted_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc),
            execution_time_ms=1500.0,
            retry_count=0
        )
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.get_task_status.return_value = mock_response
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    f"/api/v1/tasks/{task_id}/status",
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "success"
            assert data["execution_time_ms"] == 1500.0

    @pytest.mark.asyncio
    async def test_get_task_status_not_found(self, auth_headers):
        """Test task status retrieval for non-existent task."""
        task_id = "non-existent-task"
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.get_task_status.side_effect = ValueError("Task not found")
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    f"/api/v1/tasks/{task_id}/status",
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_cancel_task_success(self, auth_headers):
        """Test successful task cancellation."""
        task_id = "test-task-cancel"
        
        mock_response = {
            "task_id": task_id,
            "status": "cancelled",
            "message": "Task cancelled successfully. Reason: User requested",
            "cancelled_at": datetime.now(timezone.utc).isoformat()
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.cancel_task.return_value = Mock(**mock_response)
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post(
                    f"/api/v1/tasks/{task_id}/cancel",
                    params={"terminate": True, "reason": "User requested"},
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["task_id"] == task_id
            assert data["status"] == "cancelled"

    @pytest.mark.asyncio
    async def test_submit_bulk_tasks_success(self, auth_headers):
        """Test successful bulk task submission."""
        bulk_request = {
            "tasks": [
                {"task_name": "test.task1", "priority": "high"},
                {"task_name": "test.task2", "priority": "standard"},
                {"task_name": "test.task3", "priority": "low"}
            ],
            "batch_name": "test_batch",
            "fail_fast": False
        }
        
        mock_response = {
            "batch_id": str(uuid4()),
            "total_tasks": 3,
            "successful_submissions": 3,
            "failed_submissions": 0,
            "task_ids": ["task-1", "task-2", "task-3"],
            "errors": [],
            "submitted_at": datetime.now(timezone.utc).isoformat()
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.submit_bulk_tasks.return_value = Mock(**mock_response)
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post(
                    "/api/v1/tasks/bulk/submit",
                    json=bulk_request,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["total_tasks"] == 3
            assert data["successful_submissions"] == 3
            assert data["failed_submissions"] == 0

    @pytest.mark.asyncio
    async def test_get_system_metrics_success(self, auth_headers):
        """Test successful system metrics retrieval."""
        mock_response = SystemMetricsResponse(
            total_queues=5,
            active_queues=5,
            total_workers=3,
            active_workers=3,
            total_tasks_today=100,
            successful_tasks_today=85,
            failed_tasks_today=15,
            overall_success_rate=85.0,
            average_execution_time_ms=2500.0,
            peak_throughput_per_minute=50.0,
            total_memory_usage_mb=512.0,
            cpu_utilization_percent=45.0,
            health_score=92.0,
            uptime_hours=24.5,
            recorded_at=datetime.now(timezone.utc)
        )
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            mock_service = AsyncMock()
            mock_service.get_system_metrics.return_value = mock_response
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/tasks/metrics/system",
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total_tasks_today"] == 100
            assert data["overall_success_rate"] == 85.0
            assert data["health_score"] == 92.0

    @pytest.mark.asyncio
    async def test_health_check_success(self, auth_headers):
        """Test successful task system health check."""
        mock_health_status = {
            "status": "healthy",
            "broker_connected": True,
            "active_workers": 3,
            "total_active_tasks": 5,
            "total_scheduled_tasks": 2,
            "total_reserved_tasks": 1
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.check_celery_health', return_value=mock_health_status):
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/tasks/health",
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "healthy"
            assert data["broker_connected"] is True
            assert data["active_workers"] == 3
            assert "checked_at" in data
            assert "checked_by" in data

    @pytest.mark.asyncio
    async def test_get_queue_status_success(self, auth_headers):
        """Test successful queue status retrieval."""
        mock_inspect_data = {
            "active": {"worker1": []},
            "scheduled": {"worker1": []},
            "reserved": {"worker1": []}
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.celery_app') as mock_celery:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            # Mock Celery inspect
            mock_inspect = Mock()
            mock_inspect.active.return_value = mock_inspect_data["active"]
            mock_inspect.scheduled.return_value = mock_inspect_data["scheduled"]
            mock_inspect.reserved.return_value = mock_inspect_data["reserved"]
            
            mock_control = Mock()
            mock_control.inspect.return_value = mock_inspect
            mock_celery.control = mock_control
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get(
                    "/api/v1/tasks/queues",
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert isinstance(data, list)
            assert len(data) > 0
            
            # Check that known queues are included
            queue_names = [queue["name"] for queue in data]
            assert "critical" in queue_names
            assert "default" in queue_names
            assert "email" in queue_names

    @pytest.mark.asyncio
    async def test_permission_enforcement(self, auth_headers):
        """Test that endpoints properly enforce permissions."""
        endpoints_permissions = [
            ("/api/v1/tasks/submit", "POST", ["task_submit"]),
            ("/api/v1/tasks/test-task/status", "GET", ["task_read"]),
            ("/api/v1/tasks/test-task/cancel", "POST", ["task_cancel"]),
            ("/api/v1/tasks/bulk/submit", "POST", ["task_submit", "task_bulk"]),
            ("/api/v1/tasks/metrics/system", "GET", ["task_admin", "metrics_read"]),
            ("/api/v1/tasks/health", "GET", ["task_read"]),
            ("/api/v1/tasks/queues", "GET", ["task_admin"])
        ]
        
        for endpoint, method, required_perms in endpoints_permissions:
            with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
                 patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms:
                
                mock_get_user.return_value = Mock(id=uuid4())
                
                # Mock permission check to verify it's called with correct permissions
                mock_perms.return_value = lambda: None
                
                async with AsyncClient(app=app, base_url="http://test") as client:
                    if method == "POST":
                        await client.post(endpoint, json={}, headers=auth_headers)
                    else:
                        await client.get(endpoint, headers=auth_headers)
                
                # Verify permissions were checked
                mock_perms.assert_called_with(required_perms)

    @pytest.mark.asyncio
    async def test_error_handling_and_logging(self, auth_headers):
        """Test proper error handling and logging."""
        task_request = {
            "task_name": "test.failing_task",
            "priority": "standard"
        }
        
        with patch('app.api.v1.endpoints.tasks.get_current_user') as mock_get_user, \
             patch('app.api.v1.endpoints.tasks.require_permissions') as mock_perms, \
             patch('app.api.v1.endpoints.tasks.TaskService') as mock_service_class, \
             patch('app.api.v1.endpoints.tasks.logger') as mock_logger:
            
            mock_get_user.return_value = Mock(id=uuid4())
            mock_perms.return_value = lambda: None
            
            # Mock service to raise an exception
            mock_service = AsyncMock()
            mock_service.submit_task.side_effect = Exception("Service error")
            mock_service_class.return_value = mock_service
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post(
                    "/api/v1/tasks/submit",
                    json=task_request,
                    headers=auth_headers
                )
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            
            # Verify error was logged
            mock_logger.error.assert_called_once()
            error_call = mock_logger.error.call_args[0][0]
            assert "Failed to submit task via API" in error_call
