"""
User Profile Management Schemas for Culture Connect Backend API.

This module provides comprehensive schemas for user profile management including:
- Profile update and management schemas
- User preferences and settings schemas
- Privacy control schemas
- Profile image upload schemas
- Account settings schemas

Implements Task 2.2.2 requirements for complete user profile management
with comprehensive validation and type safety.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, HttpUrl, validator, root_validator
from enum import Enum

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin


class ProfileVisibility(str, Enum):
    """Profile visibility options."""
    PUBLIC = "public"
    PRIVATE = "private"
    FRIENDS = "friends"


class NotificationFrequency(str, Enum):
    """Notification frequency options."""
    IMMEDIATE = "immediate"
    DAILY = "daily"
    WEEKLY = "weekly"
    NEVER = "never"


class LanguageCode(str, Enum):
    """Supported language codes."""
    EN = "en"
    FR = "fr"
    ES = "es"
    DE = "de"
    IT = "it"
    PT = "pt"
    AR = "ar"
    ZH = "zh"
    JA = "ja"
    KO = "ko"


class TimezoneCode(str, Enum):
    """Common timezone codes."""
    UTC = "UTC"
    WAT = "Africa/Lagos"  # West Africa Time (Nigeria)
    EAT = "Africa/Nairobi"  # East Africa Time
    CAT = "Africa/Johannesburg"  # Central Africa Time
    EST = "America/New_York"
    PST = "America/Los_Angeles"
    GMT = "Europe/London"
    CET = "Europe/Paris"


class UserProfileUpdate(BaseSchema):
    """
    Schema for updating user profile information.
    
    Provides comprehensive profile update capabilities with validation
    for all user profile fields including personal information, preferences,
    and privacy settings.
    """
    
    # Basic profile information
    first_name: Optional[str] = Field(
        None, 
        min_length=1, 
        max_length=50,
        description="User first name"
    )
    last_name: Optional[str] = Field(
        None, 
        min_length=1, 
        max_length=50,
        description="User last name"
    )
    phone_number: Optional[str] = Field(
        None, 
        max_length=20,
        description="User phone number in international format"
    )
    
    # Extended profile information
    bio: Optional[str] = Field(
        None, 
        max_length=500,
        description="User biography/description"
    )
    location: Optional[str] = Field(
        None, 
        max_length=100,
        description="User location/city"
    )
    date_of_birth: Optional[datetime] = Field(
        None,
        description="User date of birth"
    )
    
    # Profile preferences
    timezone: Optional[TimezoneCode] = Field(
        None,
        description="User timezone preference"
    )
    language: Optional[LanguageCode] = Field(
        None,
        description="User language preference"
    )
    
    # Privacy settings
    profile_visibility: Optional[ProfileVisibility] = Field(
        None,
        description="Profile visibility setting"
    )
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        """Validate phone number format."""
        if v is not None:
            # Remove spaces and common separators
            cleaned = ''.join(c for c in v if c.isdigit() or c == '+')
            if not cleaned.startswith('+'):
                cleaned = '+' + cleaned
            # Basic validation for international format
            if len(cleaned) < 8 or len(cleaned) > 16:
                raise ValueError('Phone number must be between 8 and 16 digits')
        return v
    
    @validator('date_of_birth')
    def validate_age(cls, v):
        """Validate minimum age requirement."""
        if v is not None:
            from datetime import timezone
            today = datetime.now(timezone.utc).date()
            age = today.year - v.date().year - ((today.month, today.day) < (v.date().month, v.date().day))
            
            if age < 13:
                raise ValueError('Users must be at least 13 years old')
            
            if age > 120:
                raise ValueError('Invalid date of birth')
        
        return v


class UserPreferences(BaseSchema):
    """
    Schema for user preferences and notification settings.
    
    Manages user preferences for notifications, communications,
    and application behavior settings.
    """
    
    # Notification preferences
    email_notifications: bool = Field(
        default=True,
        description="Enable email notifications"
    )
    sms_notifications: bool = Field(
        default=False,
        description="Enable SMS notifications"
    )
    push_notifications: bool = Field(
        default=True,
        description="Enable push notifications"
    )
    marketing_emails: bool = Field(
        default=False,
        description="Enable marketing emails"
    )
    
    # Notification frequency settings
    booking_notifications: NotificationFrequency = Field(
        default=NotificationFrequency.IMMEDIATE,
        description="Booking notification frequency"
    )
    promotional_notifications: NotificationFrequency = Field(
        default=NotificationFrequency.WEEKLY,
        description="Promotional notification frequency"
    )
    
    # Application preferences
    preferred_currency: str = Field(
        default="NGN",
        pattern=r'^[A-Z]{3}$',
        description="Preferred currency code (ISO 4217)"
    )
    distance_unit: str = Field(
        default="km",
        pattern=r'^(km|mi)$',
        description="Preferred distance unit (km or mi)"
    )
    
    # Privacy preferences
    show_online_status: bool = Field(
        default=True,
        description="Show online status to other users"
    )
    allow_contact_from_vendors: bool = Field(
        default=True,
        description="Allow vendors to contact user"
    )
    data_sharing_consent: bool = Field(
        default=False,
        description="Consent for data sharing with partners"
    )


class UserPreferencesUpdate(BaseSchema):
    """Schema for updating user preferences."""
    
    # Notification preferences
    email_notifications: Optional[bool] = Field(None, description="Enable email notifications")
    sms_notifications: Optional[bool] = Field(None, description="Enable SMS notifications")
    push_notifications: Optional[bool] = Field(None, description="Enable push notifications")
    marketing_emails: Optional[bool] = Field(None, description="Enable marketing emails")
    
    # Notification frequency settings
    booking_notifications: Optional[NotificationFrequency] = Field(
        None, description="Booking notification frequency"
    )
    promotional_notifications: Optional[NotificationFrequency] = Field(
        None, description="Promotional notification frequency"
    )
    
    # Application preferences
    preferred_currency: Optional[str] = Field(
        None, 
        pattern=r'^[A-Z]{3}$',
        description="Preferred currency code (ISO 4217)"
    )
    distance_unit: Optional[str] = Field(
        None, 
        pattern=r'^(km|mi)$',
        description="Preferred distance unit (km or mi)"
    )
    
    # Privacy preferences
    show_online_status: Optional[bool] = Field(None, description="Show online status to other users")
    allow_contact_from_vendors: Optional[bool] = Field(None, description="Allow vendors to contact user")
    data_sharing_consent: Optional[bool] = Field(None, description="Consent for data sharing with partners")


class ProfileImageUpload(BaseSchema):
    """Schema for profile image upload metadata."""
    
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(
        ..., 
        pattern=r'^image\/(jpeg|jpg|png|webp)$',
        description="Image content type"
    )
    size: int = Field(
        ..., 
        gt=0, 
        le=10*1024*1024,  # 10MB max
        description="File size in bytes"
    )


class ProfileImageResponse(BaseSchema, TimestampMixin):
    """Schema for profile image response."""
    
    id: str = Field(..., description="Image ID")
    url: HttpUrl = Field(..., description="Image URL")
    thumbnail_url: HttpUrl = Field(..., description="Thumbnail URL")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="Image content type")
    size: int = Field(..., description="File size in bytes")
    is_active: bool = Field(default=True, description="Image active status")


class AccountSettings(BaseSchema):
    """Schema for account settings and security preferences."""
    
    # Security settings
    two_factor_enabled: bool = Field(default=False, description="Two-factor authentication enabled")
    login_notifications: bool = Field(default=True, description="Notify on new logins")
    session_timeout: int = Field(
        default=30, 
        ge=5, 
        le=1440,  # 5 minutes to 24 hours
        description="Session timeout in minutes"
    )
    
    # Account preferences
    auto_logout_inactive: bool = Field(default=True, description="Auto logout when inactive")
    remember_login_devices: bool = Field(default=True, description="Remember trusted devices")
    
    # Data and privacy
    activity_tracking: bool = Field(default=True, description="Allow activity tracking")
    analytics_consent: bool = Field(default=False, description="Consent for analytics")
    
    # Communication preferences
    account_updates_email: bool = Field(default=True, description="Email account updates")
    security_alerts_email: bool = Field(default=True, description="Email security alerts")


class AccountSettingsUpdate(BaseSchema):
    """Schema for updating account settings."""
    
    # Security settings
    two_factor_enabled: Optional[bool] = Field(None, description="Two-factor authentication enabled")
    login_notifications: Optional[bool] = Field(None, description="Notify on new logins")
    session_timeout: Optional[int] = Field(
        None, 
        ge=5, 
        le=1440,
        description="Session timeout in minutes"
    )
    
    # Account preferences
    auto_logout_inactive: Optional[bool] = Field(None, description="Auto logout when inactive")
    remember_login_devices: Optional[bool] = Field(None, description="Remember trusted devices")
    
    # Data and privacy
    activity_tracking: Optional[bool] = Field(None, description="Allow activity tracking")
    analytics_consent: Optional[bool] = Field(None, description="Consent for analytics")
    
    # Communication preferences
    account_updates_email: Optional[bool] = Field(None, description="Email account updates")
    security_alerts_email: Optional[bool] = Field(None, description="Email security alerts")


# Export all schemas
__all__ = [
    "ProfileVisibility",
    "NotificationFrequency", 
    "LanguageCode",
    "TimezoneCode",
    "UserProfileUpdate",
    "UserPreferences",
    "UserPreferencesUpdate",
    "ProfileImageUpload",
    "ProfileImageResponse",
    "AccountSettings",
    "AccountSettingsUpdate",
]
