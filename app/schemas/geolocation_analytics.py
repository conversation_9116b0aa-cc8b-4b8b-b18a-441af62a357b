"""
Geolocation Analytics Schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic schemas for geolocation analytics including:
- GeolocationAnalyticsResponse: Core analytics response with performance metrics
- ProviderPerformanceResponse: Provider-specific performance analytics
- ConversionAnalyticsResponse: Conversion rate tracking and optimization insights
- RoutingMetricsResponse: Routing decision analytics and effectiveness
- AnalyticsFilterRequest: Request schemas for analytics filtering and aggregation

Implements Phase 2.2 Analytics Dashboard requirements with Pydantic V2 compliance,
comprehensive validation, and seamless integration with analytics models.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, field_validator
from enum import Enum

from app.models.geolocation_analytics import AnalyticsPeriodType, RoutingDecisionType


class MarketSegment(str, Enum):
    """Market segment types for analytics."""
    AFRICAN = "african"
    DIASPORA = "diaspora"
    GLOBAL = "global"
    UNKNOWN = "unknown"


class AnalyticsFilterRequest(BaseModel):
    """Request schema for analytics filtering and aggregation."""
    model_config = ConfigDict(from_attributes=True)
    
    start_date: datetime = Field(description="Analysis start date")
    end_date: datetime = Field(description="Analysis end date")
    period_type: AnalyticsPeriodType = Field(
        default=AnalyticsPeriodType.DAILY,
        description="Analytics aggregation period"
    )
    country_codes: Optional[List[str]] = Field(
        default=None,
        description="Optional list of country codes to filter"
    )
    providers: Optional[List[str]] = Field(
        default=None,
        description="Optional list of providers to filter (paystack, stripe, busha)"
    )
    market_segment: Optional[MarketSegment] = Field(
        default=None,
        description="Optional market segment filter"
    )
    include_vpn_data: bool = Field(
        default=True,
        description="Whether to include VPN/Proxy detection data"
    )

    @field_validator('country_codes')
    @classmethod
    def validate_country_codes(cls, v):
        """Validate country codes format."""
        if v is not None:
            for code in v:
                if not isinstance(code, str) or len(code) != 2:
                    raise ValueError(f"Invalid country code format: {code}. Must be 2-letter ISO code.")
        return v

    @field_validator('providers')
    @classmethod
    def validate_providers(cls, v):
        """Validate provider names."""
        if v is not None:
            valid_providers = {'paystack', 'stripe', 'busha'}
            for provider in v:
                if provider not in valid_providers:
                    raise ValueError(f"Invalid provider: {provider}. Must be one of {valid_providers}")
        return v


class GeolocationAnalyticsResponse(BaseModel):
    """Response schema for geolocation analytics data."""
    model_config = ConfigDict(from_attributes=True)
    
    # Time period information
    analysis_date: datetime = Field(description="Analysis date")
    period_type: AnalyticsPeriodType = Field(description="Analytics period type")
    period_start: datetime = Field(description="Period start timestamp")
    period_end: datetime = Field(description="Period end timestamp")
    
    # Geographic information
    country_code: Optional[str] = Field(description="ISO country code")
    country_name: Optional[str] = Field(description="Country name")
    continent_code: Optional[str] = Field(description="Continent code")
    region: Optional[str] = Field(description="Geographic region")
    
    # Payment volume metrics
    total_payments: int = Field(description="Total payment transactions")
    successful_payments: int = Field(description="Successful payment transactions")
    failed_payments: int = Field(description="Failed payment transactions")
    total_amount: Decimal = Field(description="Total payment amount")
    successful_amount: Decimal = Field(description="Successful payment amount")
    
    # Performance metrics
    conversion_rate: float = Field(description="Payment success rate percentage")
    avg_processing_time_ms: Optional[float] = Field(description="Average processing time in milliseconds")
    avg_detection_time_ms: Optional[float] = Field(description="Average geolocation detection time")
    cache_hit_rate: float = Field(description="Cache hit rate percentage")
    
    # Provider distribution
    paystack_payments: int = Field(description="Paystack payment count")
    stripe_payments: int = Field(description="Stripe payment count")
    busha_payments: int = Field(description="Busha payment count")
    
    # Routing decision analytics
    geolocation_routing_count: int = Field(description="Geolocation-based routing count")
    currency_routing_count: int = Field(description="Currency-based routing count")
    user_preference_routing_count: int = Field(description="User preference routing count")
    fallback_routing_count: int = Field(description="Fallback routing count")
    
    # VPN/Proxy impact metrics
    vpn_detected_payments: int = Field(description="VPN detected payment count")
    proxy_detected_payments: int = Field(description="Proxy detected payment count")
    vpn_impact_on_conversion: float = Field(description="VPN impact on conversion rate")
    
    # User analytics
    unique_users: int = Field(description="Unique user count")
    repeat_users: int = Field(description="Repeat user count")
    avg_transaction_value: Decimal = Field(description="Average transaction value")
    
    # Additional metadata
    provider_performance_data: Optional[Dict[str, Any]] = Field(description="Provider-specific metrics")
    routing_effectiveness_data: Optional[Dict[str, Any]] = Field(description="Routing decision analysis")
    geographic_insights: Optional[Dict[str, Any]] = Field(description="Geographic-specific insights")


class ProviderPerformanceResponse(BaseModel):
    """Response schema for provider performance analytics."""
    model_config = ConfigDict(from_attributes=True)
    
    # Identification
    analysis_date: datetime = Field(description="Analysis date")
    period_type: AnalyticsPeriodType = Field(description="Analytics period type")
    country_code: str = Field(description="ISO country code")
    provider: str = Field(description="Payment provider name")
    
    # Volume metrics
    total_transactions: int = Field(description="Total transaction count")
    successful_transactions: int = Field(description="Successful transaction count")
    failed_transactions: int = Field(description="Failed transaction count")
    
    # Financial metrics
    total_volume: Decimal = Field(description="Total transaction volume")
    successful_volume: Decimal = Field(description="Successful transaction volume")
    avg_transaction_amount: Decimal = Field(description="Average transaction amount")
    
    # Performance metrics
    success_rate: float = Field(description="Transaction success rate percentage")
    avg_processing_time_ms: Optional[float] = Field(description="Average processing time")
    avg_response_time_ms: Optional[float] = Field(description="Average response time")
    
    # Cost and fee analysis
    total_fees: Decimal = Field(description="Total fees charged")
    avg_fee_percentage: float = Field(description="Average fee percentage")
    cost_effectiveness_score: float = Field(description="Cost effectiveness score")
    
    # Quality metrics
    error_rate: float = Field(description="Error rate percentage")
    timeout_rate: float = Field(description="Timeout rate percentage")
    retry_rate: float = Field(description="Retry rate percentage")
    
    # User experience metrics
    user_satisfaction_score: Optional[float] = Field(description="User satisfaction score")
    abandonment_rate: float = Field(description="Payment abandonment rate")
    completion_rate: float = Field(description="Payment completion rate")
    
    # Competitive analysis
    market_share_percentage: float = Field(description="Market share percentage")
    relative_performance_score: float = Field(description="Relative performance score")
    
    # Additional metadata
    currency_breakdown: Optional[Dict[str, Any]] = Field(description="Currency-specific metrics")
    failure_reasons: Optional[Dict[str, Any]] = Field(description="Failure analysis")
    optimization_recommendations: Optional[Dict[str, Any]] = Field(description="Optimization recommendations")


class ConversionAnalyticsResponse(BaseModel):
    """Response schema for conversion analytics data."""
    model_config = ConfigDict(from_attributes=True)
    
    # Identification
    analysis_date: datetime = Field(description="Analysis date")
    period_type: AnalyticsPeriodType = Field(description="Analytics period type")
    country_code: str = Field(description="ISO country code")
    region: Optional[str] = Field(description="Geographic region")
    market_segment: Optional[str] = Field(description="Market segment")
    
    # Conversion funnel metrics
    total_payment_attempts: int = Field(description="Total payment attempts")
    initiated_payments: int = Field(description="Initiated payments")
    authorized_payments: int = Field(description="Authorized payments")
    completed_payments: int = Field(description="Completed payments")
    
    # Conversion rates at each stage
    initiation_rate: float = Field(description="Payment initiation rate")
    authorization_rate: float = Field(description="Payment authorization rate")
    completion_rate: float = Field(description="Payment completion rate")
    overall_conversion_rate: float = Field(description="Overall conversion rate")
    
    # Provider-specific conversion rates
    paystack_conversion_rate: float = Field(description="Paystack conversion rate")
    stripe_conversion_rate: float = Field(description="Stripe conversion rate")
    busha_conversion_rate: float = Field(description="Busha conversion rate")
    
    # VPN/Proxy impact on conversion
    vpn_conversion_rate: float = Field(description="VPN user conversion rate")
    non_vpn_conversion_rate: float = Field(description="Non-VPN user conversion rate")
    vpn_conversion_impact: float = Field(description="VPN impact on conversion")
    
    # Time-based conversion analysis
    avg_time_to_completion_seconds: Optional[float] = Field(description="Average completion time")
    abandonment_rate: float = Field(description="Payment abandonment rate")
    retry_success_rate: float = Field(description="Retry success rate")
    
    # Financial impact
    total_conversion_value: Decimal = Field(description="Total conversion value")
    avg_conversion_value: Decimal = Field(description="Average conversion value")
    lost_revenue_estimate: Decimal = Field(description="Estimated lost revenue")
    
    # Optimization insights
    conversion_optimization_score: float = Field(description="Conversion optimization score")
    improvement_potential_percentage: float = Field(description="Improvement potential percentage")
    recommended_optimizations: Optional[Dict[str, Any]] = Field(description="Optimization recommendations")
    
    # Comparative analysis
    benchmark_conversion_rate: Optional[float] = Field(description="Industry benchmark rate")
    relative_performance: float = Field(description="Performance vs benchmark")
    
    # Additional metadata
    user_segment_breakdown: Optional[Dict[str, Any]] = Field(description="User segment breakdown")
    device_type_breakdown: Optional[Dict[str, Any]] = Field(description="Device type breakdown")
    currency_conversion_impact: Optional[Dict[str, Any]] = Field(description="Currency impact analysis")


class RoutingMetricsResponse(BaseModel):
    """Response schema for routing decision analytics."""
    model_config = ConfigDict(from_attributes=True)
    
    # Identification
    analysis_date: datetime = Field(description="Analysis date")
    period_type: AnalyticsPeriodType = Field(description="Analytics period type")
    routing_decision: RoutingDecisionType = Field(description="Routing decision type")
    country_code: Optional[str] = Field(description="ISO country code")
    selected_provider: str = Field(description="Selected payment provider")
    
    # Decision effectiveness metrics
    total_decisions: int = Field(description="Total routing decisions")
    successful_outcomes: int = Field(description="Successful outcomes")
    failed_outcomes: int = Field(description="Failed outcomes")
    decision_accuracy: float = Field(description="Decision accuracy percentage")
    
    # Performance impact
    avg_success_rate: float = Field(description="Average success rate")
    avg_processing_time_ms: Optional[float] = Field(description="Average processing time")
    cost_efficiency_score: float = Field(description="Cost efficiency score")
    
    # VPN/Proxy impact on routing
    vpn_affected_decisions: int = Field(description="VPN affected decisions count")
    vpn_routing_accuracy: float = Field(description="VPN routing accuracy")
    confidence_score_impact: float = Field(description="Confidence score impact")
    
    # Geographic routing effectiveness
    optimal_routing_percentage: float = Field(description="Optimal routing percentage")
    suboptimal_routing_count: int = Field(description="Suboptimal routing count")
    routing_improvement_potential: float = Field(description="Improvement potential")
    
    # A/B testing integration
    ab_test_group: Optional[str] = Field(description="A/B test group")
    ab_test_name: Optional[str] = Field(description="A/B test name")
    
    # Optimization insights
    optimization_score: float = Field(description="Optimization score")
    recommended_adjustments: Optional[Dict[str, Any]] = Field(description="Recommended adjustments")
    performance_trends: Optional[Dict[str, Any]] = Field(description="Performance trends")


class AnalyticsSummaryResponse(BaseModel):
    """Summary response schema for analytics dashboard."""
    model_config = ConfigDict(from_attributes=True)
    
    # Time period
    period_start: datetime = Field(description="Analysis period start")
    period_end: datetime = Field(description="Analysis period end")
    period_type: AnalyticsPeriodType = Field(description="Analytics period type")
    
    # Overall performance summary
    total_payments: int = Field(description="Total payments in period")
    total_volume: Decimal = Field(description="Total payment volume")
    overall_conversion_rate: float = Field(description="Overall conversion rate")
    avg_processing_time_ms: float = Field(description="Average processing time")
    
    # Provider performance summary
    best_performing_provider: str = Field(description="Best performing provider")
    provider_performance_scores: Dict[str, float] = Field(description="Provider performance scores")
    
    # Geographic insights
    top_performing_countries: List[Dict[str, Any]] = Field(description="Top performing countries")
    geographic_distribution: Dict[str, int] = Field(description="Geographic payment distribution")
    
    # VPN/Proxy impact summary
    vpn_detection_rate: float = Field(description="VPN detection rate")
    vpn_impact_on_performance: float = Field(description="VPN impact on performance")
    
    # Optimization recommendations
    key_insights: List[str] = Field(description="Key analytical insights")
    optimization_opportunities: List[Dict[str, Any]] = Field(description="Optimization opportunities")
    performance_trends: Dict[str, Any] = Field(description="Performance trends")
