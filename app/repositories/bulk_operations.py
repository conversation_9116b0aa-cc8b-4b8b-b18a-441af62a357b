"""
Bulk Operations for Enhanced Repository Pattern.

This module provides optimized bulk operations for the repository layer
with transaction management and performance optimization.
"""

import logging
from typing import List, Dict, Any, Optional, Union, Tuple
from sqlalchemy import update, delete, select, func
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import IntegrityError

from .base import ModelType, RepositoryError, time


class BulkOperationsMixin:
    """
    Mixin class providing bulk operations for repositories.

    Provides optimized bulk operations with:
    - Bulk insert with conflict resolution
    - Bulk update with optimized queries
    - Bulk delete with batch processing
    - Transaction management and rollback
    """

    async def bulk_create(
        self,
        objects: List[Dict[str, Any]],
        batch_size: int = 1000,
        on_conflict: str = "raise"  # "raise", "ignore", "update"
    ) -> List[ModelType]:
        """
        Create multiple records in bulk with optimized performance.

        Args:
            objects: List of dictionaries with field values
            batch_size: Number of records to process in each batch
            on_conflict: How to handle conflicts ("raise", "ignore", "update")

        Returns:
            List of created model instances

        Raises:
            RepositoryError: If bulk creation fails
        """
        start_time = time.time()
        created_objects = []

        try:
            # Process in batches for better memory management
            for i in range(0, len(objects), batch_size):
                batch = objects[i:i + batch_size]

                if on_conflict == "ignore":
                    # Use PostgreSQL INSERT ... ON CONFLICT DO NOTHING
                    stmt = insert(self.model).values(batch)
                    stmt = stmt.on_conflict_do_nothing()
                    await self.db.execute(stmt)

                elif on_conflict == "update":
                    # Use PostgreSQL INSERT ... ON CONFLICT DO UPDATE
                    stmt = insert(self.model).values(batch)
                    # Update all fields except id and created_at
                    update_dict = {
                        col.name: stmt.excluded[col.name]
                        for col in self.model.__table__.columns
                        if col.name not in ('id', 'created_at')
                    }
                    stmt = stmt.on_conflict_do_update(
                        index_elements=['id'],
                        set_=update_dict
                    )
                    await self.db.execute(stmt)

                else:
                    # Standard bulk insert
                    db_objects = [self.model(**obj) for obj in batch]
                    self.db.add_all(db_objects)
                    await self.db.flush()

                    # Refresh objects to get generated IDs
                    for obj in db_objects:
                        await self.db.refresh(obj)

                    created_objects.extend(db_objects)

            await self.db.commit()

            query_time = time.time() - start_time
            self._track_query_performance("bulk_create", query_time, len(objects))

            return created_objects

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("bulk_create_error", query_time, 0)
            raise RepositoryError(f"Failed to bulk create {self.model.__name__} records", e)

    async def bulk_update(
        self,
        updates: List[Dict[str, Any]],
        id_field: str = 'id',
        batch_size: int = 1000
    ) -> int:
        """
        Update multiple records in bulk with optimized performance.

        Args:
            updates: List of dictionaries with field values and ID
            id_field: Field name to use for identifying records
            batch_size: Number of records to process in each batch

        Returns:
            Number of records updated

        Raises:
            RepositoryError: If bulk update fails
        """
        start_time = time.time()
        total_updated = 0

        try:
            # Group updates by common field values for optimization
            update_groups = self._group_updates_by_fields(updates, id_field)

            for update_fields, records in update_groups.items():
                # Process in batches
                for i in range(0, len(records), batch_size):
                    batch = records[i:i + batch_size]
                    ids = [record[id_field] for record in batch]

                    # Build update statement
                    update_data = {field: value for field, value in update_fields.items()}
                    stmt = (
                        update(self.model)
                        .where(getattr(self.model, id_field).in_(ids))
                        .values(**update_data)
                    )

                    result = await self.db.execute(stmt)
                    total_updated += result.rowcount

            await self.db.commit()

            query_time = time.time() - start_time
            self._track_query_performance("bulk_update", query_time, total_updated)

            return total_updated

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("bulk_update_error", query_time, 0)
            raise RepositoryError(f"Failed to bulk update {self.model.__name__} records", e)

    async def bulk_delete(
        self,
        ids: List[Union[int, str]],
        id_field: str = 'id',
        batch_size: int = 1000,
        soft_delete: bool = False
    ) -> int:
        """
        Delete multiple records in bulk with optimized performance.

        Args:
            ids: List of record IDs to delete
            id_field: Field name to use for identifying records
            batch_size: Number of records to process in each batch
            soft_delete: Whether to perform soft delete (if supported)

        Returns:
            Number of records deleted

        Raises:
            RepositoryError: If bulk delete fails
        """
        start_time = time.time()
        total_deleted = 0

        try:
            # Process in batches
            for i in range(0, len(ids), batch_size):
                batch_ids = ids[i:i + batch_size]

                if soft_delete and hasattr(self.model, 'deleted_at'):
                    # Soft delete by setting deleted_at timestamp
                    stmt = (
                        update(self.model)
                        .where(getattr(self.model, id_field).in_(batch_ids))
                        .values(deleted_at=func.now())
                    )
                else:
                    # Hard delete
                    stmt = delete(self.model).where(
                        getattr(self.model, id_field).in_(batch_ids)
                    )

                result = await self.db.execute(stmt)
                total_deleted += result.rowcount

            await self.db.commit()

            query_time = time.time() - start_time
            self._track_query_performance("bulk_delete", query_time, total_deleted)

            return total_deleted

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("bulk_delete_error", query_time, 0)
            raise RepositoryError(f"Failed to bulk delete {self.model.__name__} records", e)

    async def bulk_upsert(
        self,
        objects: List[Dict[str, Any]],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None,
        batch_size: int = 1000
    ) -> int:
        """
        Perform bulk upsert (insert or update) operations.

        Args:
            objects: List of dictionaries with field values
            conflict_columns: Columns to check for conflicts
            update_columns: Columns to update on conflict (if None, update all)
            batch_size: Number of records to process in each batch

        Returns:
            Number of records processed

        Raises:
            RepositoryError: If bulk upsert fails
        """
        start_time = time.time()
        total_processed = 0

        try:
            # Process in batches
            for i in range(0, len(objects), batch_size):
                batch = objects[i:i + batch_size]

                # Build upsert statement
                stmt = insert(self.model).values(batch)

                # Determine update columns
                if update_columns is None:
                    update_dict = {
                        col.name: stmt.excluded[col.name]
                        for col in self.model.__table__.columns
                        if col.name not in conflict_columns and col.name != 'created_at'
                    }
                else:
                    update_dict = {
                        col: stmt.excluded[col]
                        for col in update_columns
                    }

                stmt = stmt.on_conflict_do_update(
                    index_elements=conflict_columns,
                    set_=update_dict
                )

                result = await self.db.execute(stmt)
                total_processed += len(batch)

            await self.db.commit()

            query_time = time.time() - start_time
            self._track_query_performance("bulk_upsert", query_time, total_processed)

            return total_processed

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self._track_query_performance("bulk_upsert_error", query_time, 0)
            raise RepositoryError(f"Failed to bulk upsert {self.model.__name__} records", e)

    def _group_updates_by_fields(
        self,
        updates: List[Dict[str, Any]],
        id_field: str
    ) -> Dict[Tuple, List[Dict[str, Any]]]:
        """
        Group updates by common field values for optimization.

        Args:
            updates: List of update dictionaries
            id_field: ID field name to exclude from grouping

        Returns:
            Dictionary mapping field tuples to record lists
        """
        groups = {}

        for update in updates:
            # Extract fields excluding ID field
            fields = {k: v for k, v in update.items() if k != id_field}
            field_tuple = tuple(sorted(fields.items()))

            if field_tuple not in groups:
                groups[field_tuple] = []
            groups[field_tuple].append(update)

        return {dict(k): v for k, v in groups.items()}


class FullRepository(BulkOperationsMixin):
    """
    Complete repository implementation with all features.

    Combines:
    - Enhanced base repository with async CRUD operations
    - Advanced pagination and filtering
    - Bulk operations with optimization
    - Performance monitoring and error handling
    """

    def __init__(self, model, db):
        """Initialize with model and database session."""
        self.model = model
        self.db = db
        self.table_name = model.__tablename__
        self.logger = logging.getLogger(f"{__name__}.{model.__name__}Repository")
        self._query_metrics = []
