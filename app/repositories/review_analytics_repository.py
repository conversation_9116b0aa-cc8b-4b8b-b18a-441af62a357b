"""
Review Analytics Repository for Culture Connect Backend API.

This module provides comprehensive repository class for review analytics data access operations
including CRUD operations, performance metrics calculation, and business intelligence support.

Implements Task 4.4.1 Phase 3 requirements for review analytics repository layer with:
- Complete analytics lifecycle data access
- Performance metrics calculation and aggregation
- Trend analysis and reporting capabilities
- Bulk operations for analytics updates (>1000 records/second)
- Performance optimization with <200ms query targets

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
from datetime import datetime, date, timedelta, timezone
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from app.repositories.base import BaseRepository, PaginationParams, QueryResult, RepositoryError
from app.models.review_models import ReviewAnalytics, Review, ReviewResponse
from app.models.vendor import Vendor


class ReviewAnalyticsRepository(BaseRepository[ReviewAnalytics]):
    """
    Repository for review analytics data access operations.

    Provides comprehensive analytics data management including:
    - CRUD operations with performance optimization (<200ms targets)
    - Performance metrics calculation and aggregation
    - Trend analysis and reporting capabilities
    - Bulk operations for analytics updates (>1000 records/second)
    - Integration with review and vendor systems
    """

    def __init__(self, db):
        """Initialize review analytics repository."""
        super().__init__(ReviewAnalytics, db)
        self.logger = logging.getLogger(f"{__name__}.ReviewAnalyticsRepository")

    async def create_analytics_record(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date,
        analytics_data: Dict[str, Any]
    ) -> ReviewAnalytics:
        """
        Create a new analytics record with validation.

        Performance Metrics:
        - Target response time: <200ms for analytics record creation
        - Vendor validation: <50ms using vendor_id index
        - Analytics creation: <100ms with optimized insert
        - JSONB operations: <50ms for complex data storage

        Args:
            vendor_id: Vendor ID for analytics
            period_start: Analytics period start date
            period_end: Analytics period end date
            analytics_data: Analytics data (metrics, distributions, trends)

        Returns:
            Created ReviewAnalytics instance

        Raises:
            RepositoryError: If analytics record creation fails
        """
        start_time = time.time()

        try:
            # Validate vendor exists
            vendor = await self._validate_vendor_for_analytics(vendor_id)
            if not vendor:
                raise RepositoryError(f"Vendor {vendor_id} not found for analytics")

            # Check for existing analytics record for the period
            existing_analytics = await self.get_analytics_by_period(
                vendor_id, period_start, period_end
            )
            if existing_analytics:
                raise IntegrityError(
                    f"Analytics record already exists for vendor {vendor_id} period {period_start} to {period_end}",
                    None, None
                )

            # Prepare analytics object data
            analytics_obj_data = {
                "vendor_id": vendor_id,
                "period_start": period_start,
                "period_end": period_end,
                **analytics_data
            }

            # Create analytics record
            analytics = await self.create(analytics_obj_data)

            query_time = time.time() - start_time
            self.logger.info(
                f"Analytics record created successfully",
                extra={
                    "analytics_id": analytics.id,
                    "vendor_id": vendor_id,
                    "period_start": period_start,
                    "period_end": period_end,
                    "total_reviews": analytics.total_reviews,
                    "query_time": query_time
                }
            )

            return analytics

        except IntegrityError:
            # Re-raise integrity errors (duplicate analytics)
            raise
        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to create analytics record",
                extra={
                    "vendor_id": vendor_id,
                    "period_start": period_start,
                    "period_end": period_end,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to create analytics record", e)

    async def get_analytics_by_period(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date
    ) -> Optional[ReviewAnalytics]:
        """
        Get analytics record by vendor and period.

        Uses idx_review_analytics_vendor_period composite index for <50ms response time.

        Args:
            vendor_id: Vendor ID
            period_start: Period start date
            period_end: Period end date

        Returns:
            ReviewAnalytics instance or None if not found
        """
        try:
            stmt = (
                select(ReviewAnalytics)
                .where(
                    and_(
                        ReviewAnalytics.vendor_id == vendor_id,
                        ReviewAnalytics.period_start == period_start,
                        ReviewAnalytics.period_end == period_end
                    )
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to get analytics by period: {str(e)}")
            raise RepositoryError(f"Failed to get analytics by period", e)

    async def get_vendor_analytics(
        self,
        vendor_id: int,
        date_range: Optional[Tuple[date, date]] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewAnalytics]:
        """
        Get analytics records for a specific vendor.

        Performance Metrics:
        - Target response time: <200ms for vendor analytics queries
        - Uses idx_review_analytics_vendor_period composite index
        - Optimized pagination with cursor support

        Args:
            vendor_id: Vendor ID
            date_range: Optional tuple of (start_date, end_date)
            pagination: Pagination parameters

        Returns:
            Query result with vendor analytics
        """
        try:
            stmt = select(ReviewAnalytics).where(ReviewAnalytics.vendor_id == vendor_id)

            # Apply date range filter
            if date_range:
                start_date, end_date = date_range
                stmt = stmt.where(
                    and_(
                        ReviewAnalytics.period_start >= start_date,
                        ReviewAnalytics.period_end <= end_date
                    )
                )

            # Apply ordering (newest periods first)
            stmt = stmt.order_by(desc(ReviewAnalytics.period_end))

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get vendor analytics: {str(e)}")
            raise RepositoryError(f"Failed to get vendor analytics", e)

    async def calculate_vendor_analytics(
        self,
        vendor_id: int,
        period_start: date,
        period_end: date
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive analytics for a vendor and period.

        Performance Metrics:
        - Target response time: <500ms for complex analytics calculation
        - Uses optimized aggregation queries with proper indexing
        - Calculates rating distribution, sentiment breakdown, response metrics

        Args:
            vendor_id: Vendor ID
            period_start: Period start date
            period_end: Period end date

        Returns:
            Dictionary with calculated analytics data
        """
        start_time = time.time()

        try:
            # Base query for reviews in the period
            base_review_query = (
                select(Review)
                .where(
                    and_(
                        Review.vendor_id == vendor_id,
                        Review.created_at >= period_start,
                        Review.created_at <= period_end
                    )
                )
            )

            # Get basic review statistics
            review_stats_query = select(
                func.count(Review.id).label('total_reviews'),
                func.avg(Review.rating).label('average_rating'),
                func.sum(Review.helpful_count).label('total_helpful_votes'),
                func.sum(Review.reported_count).label('total_reported'),
                func.count(func.nullif(Review.is_verified_purchase, False)).label('verified_reviews')
            ).select_from(base_review_query.subquery())

            review_stats_result = await self.db.execute(review_stats_query)
            review_stats = review_stats_result.first()

            # Get rating distribution
            rating_dist_query = select(
                Review.rating,
                func.count(Review.id).label('count')
            ).select_from(base_review_query.subquery()).group_by(Review.rating)

            rating_dist_result = await self.db.execute(rating_dist_query)
            rating_distribution = {str(row.rating): row.count for row in rating_dist_result}

            # Get sentiment breakdown (if sentiment_score is available)
            sentiment_query = select(
                func.case(
                    (Review.sentiment_score > 0.1, 'positive'),
                    (Review.sentiment_score < -0.1, 'negative'),
                    else_='neutral'
                ).label('sentiment'),
                func.count(Review.id).label('count')
            ).select_from(base_review_query.subquery()).group_by('sentiment')

            sentiment_result = await self.db.execute(sentiment_query)
            sentiment_breakdown = {row.sentiment: row.count for row in sentiment_result}

            # Get response metrics
            response_stats_query = select(
                func.count(ReviewResponse.id).label('total_responses'),
                func.avg(
                    func.extract('epoch', ReviewResponse.created_at - Review.created_at) / 3600
                ).label('avg_response_time_hours')
            ).select_from(
                base_review_query.subquery().join(
                    ReviewResponse, Review.id == ReviewResponse.review_id, isouter=True
                )
            )

            response_stats_result = await self.db.execute(response_stats_query)
            response_stats = response_stats_result.first()

            # Calculate response rate
            total_reviews = review_stats.total_reviews or 0
            total_responses = response_stats.total_responses or 0
            response_rate = Decimal(total_responses) / Decimal(total_reviews) if total_reviews > 0 else Decimal('0')

            # Determine trends (simplified - could be enhanced with historical data)
            rating_trend = "stable"  # Default
            if review_stats.average_rating:
                if review_stats.average_rating > 4.0:
                    rating_trend = "improving"
                elif review_stats.average_rating < 3.0:
                    rating_trend = "declining"

            review_volume_trend = "stable"  # Default
            if total_reviews > 10:
                review_volume_trend = "increasing"
            elif total_reviews < 5:
                review_volume_trend = "decreasing"

            analytics_data = {
                'total_reviews': total_reviews,
                'average_rating': review_stats.average_rating,
                'rating_distribution': rating_distribution,
                'sentiment_breakdown': sentiment_breakdown,
                'response_rate': response_rate,
                'average_response_time': response_stats.avg_response_time_hours,
                'verified_reviews_count': review_stats.verified_reviews or 0,
                'helpful_votes_total': review_stats.total_helpful_votes or 0,
                'reported_reviews_count': review_stats.total_reported or 0,
                'rating_trend': rating_trend,
                'review_volume_trend': review_volume_trend
            }

            query_time = time.time() - start_time
            self.logger.info(
                f"Vendor analytics calculated",
                extra={
                    "vendor_id": vendor_id,
                    "period_start": period_start,
                    "period_end": period_end,
                    "total_reviews": total_reviews,
                    "average_rating": float(review_stats.average_rating) if review_stats.average_rating else 0.0,
                    "query_time": query_time
                }
            )

            return analytics_data

        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to calculate vendor analytics",
                extra={
                    "vendor_id": vendor_id,
                    "period_start": period_start,
                    "period_end": period_end,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to calculate vendor analytics", e)

    async def bulk_update_analytics(
        self,
        analytics_updates: List[Dict[str, Any]],
        batch_size: int = 1000
    ) -> int:
        """
        Bulk update analytics records for performance.

        Performance target: >1000 records/second for bulk operations.

        Args:
            analytics_updates: List of analytics update data
            batch_size: Number of records to process in each batch

        Returns:
            Number of records updated
        """
        start_time = time.time()
        total_updated = 0

        try:
            # Process in batches for optimal performance
            for i in range(0, len(analytics_updates), batch_size):
                batch = analytics_updates[i:i + batch_size]
                
                for update_data in batch:
                    analytics_id = update_data.pop('id')
                    await self.update(analytics_id, update_data)
                    total_updated += 1

                # Commit batch
                await self.db.commit()

            query_time = time.time() - start_time
            records_per_second = total_updated / query_time if query_time > 0 else 0

            self.logger.info(
                f"Bulk analytics update completed",
                extra={
                    "total_updated": total_updated,
                    "query_time": query_time,
                    "records_per_second": records_per_second
                }
            )

            return total_updated

        except Exception as e:
            await self.db.rollback()
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to bulk update analytics",
                extra={
                    "total_records": len(analytics_updates),
                    "updated_count": total_updated,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to bulk update analytics", e)

    async def get_analytics_summary(
        self,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get platform-wide analytics summary.

        Performance target: <200ms for summary queries.

        Args:
            date_range: Optional date range filter

        Returns:
            Dictionary with platform analytics summary
        """
        try:
            base_query = select(ReviewAnalytics)

            # Apply date range filter
            if date_range:
                start_date, end_date = date_range
                base_query = base_query.where(
                    and_(
                        ReviewAnalytics.period_start >= start_date,
                        ReviewAnalytics.period_end <= end_date
                    )
                )

            # Get aggregate statistics
            summary_query = select(
                func.count(ReviewAnalytics.id).label('total_analytics_records'),
                func.sum(ReviewAnalytics.total_reviews).label('platform_total_reviews'),
                func.avg(ReviewAnalytics.average_rating).label('platform_avg_rating'),
                func.avg(ReviewAnalytics.response_rate).label('platform_avg_response_rate'),
                func.count(func.distinct(ReviewAnalytics.vendor_id)).label('active_vendors')
            ).select_from(base_query.subquery())

            result = await self.db.execute(summary_query)
            summary = result.first()

            return {
                'total_analytics_records': summary.total_analytics_records or 0,
                'platform_total_reviews': summary.platform_total_reviews or 0,
                'platform_avg_rating': float(summary.platform_avg_rating) if summary.platform_avg_rating else 0.0,
                'platform_avg_response_rate': float(summary.platform_avg_response_rate) if summary.platform_avg_response_rate else 0.0,
                'active_vendors': summary.active_vendors or 0
            }

        except Exception as e:
            self.logger.error(f"Failed to get analytics summary: {str(e)}")
            raise RepositoryError(f"Failed to get analytics summary", e)

    # Private helper methods

    async def _validate_vendor_for_analytics(self, vendor_id: int) -> Optional[Vendor]:
        """
        Validate that vendor exists for analytics.

        Args:
            vendor_id: Vendor ID

        Returns:
            Vendor instance if exists, None otherwise
        """
        try:
            stmt = select(Vendor).where(Vendor.id == vendor_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to validate vendor for analytics: {str(e)}")
            return None

    async def _execute_paginated_query(
        self,
        stmt,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewAnalytics]:
        """Execute paginated query with default pagination."""
        if pagination is None:
            pagination = PaginationParams(page=1, per_page=20)

        return await self.get_paginated(
            stmt=stmt,
            pagination=pagination
        )
