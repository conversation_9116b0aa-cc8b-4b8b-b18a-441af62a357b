"""
Promotional system test package.

This package contains comprehensive tests for the promotional system including:
- Unit tests for models, schemas, repositories, services, and endpoints
- Integration tests for promotional system relationships
- Performance tests for database operations and caching
- Security tests for RBAC and access controls
- End-to-end tests for promotional workflows

Implements Phase 5: Comprehensive Testing Infrastructure with >85% code coverage.
"""
