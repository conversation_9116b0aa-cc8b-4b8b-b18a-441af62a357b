"""
Unit tests for enhanced WebSocket repositories.

This module provides comprehensive unit tests for WebSocket repository layer including:
- WebSocketRoomRepository with room management and caching validation
- WebSocketRoomParticipantRepository with participant tracking and bulk operations
- Enhanced WebSocketConnectionRepository with room integration testing
- Performance validation with <200ms query targets and >90% cache hit rates

Implements Task 6.1.1 Phase 5 requirements with >85% code coverage and real functionality testing.
"""

import pytest
import asyncio
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete

from app.repositories.websocket_repositories import (
    WebSocketRoomRepository, WebSocketRoomParticipantRepository,
    WebSocketConnectionRepository
)
from app.models.websocket_models import (
    WebSocketRoom, WebSocketRoomParticipant, WebSocketConnection,
    ConnectionStatus
)
from app.core.cache import CacheManager
from app.services.base import ServiceError


@pytest.mark.unit
class TestWebSocketRoomRepository:
    """Unit tests for WebSocketRoomRepository with performance validation."""

    @pytest.fixture
    async def room_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomRepository instance for testing."""
        return WebSocketRoomRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_room_data(self):
        """Sample room data for testing."""
        return {
            "room_id": "test_room_123",
            "room_name": "Test Room",
            "room_type": "booking",
            "is_private": True,
            "max_participants": 10,
            "booking_id": 123,
            "owner_id": 1,
            "is_active": True,
            "participant_count": 0,
            "total_messages": 0
        }

    @pytest.mark.asyncio
    async def test_create_room_performance(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test room creation with performance validation."""
        timer = performance_timer()
        
        # Mock database operations
        with patch.object(room_repository, 'create') as mock_create:
            mock_room = WebSocketRoom(**sample_room_data)
            mock_create.return_value = mock_room
            
            # Mock cache operations
            with patch.object(room_repository.cache, 'set') as mock_cache_set:
                mock_cache_set.return_value = None
                
                timer.start()
                result = await room_repository.create_room(sample_room_data)
                timer.stop()
                
                # Performance validation: <100ms for room creation
                timer.assert_under_ms(100, "Room creation")
                
                # Verify result
                assert result.room_id == sample_room_data["room_id"]
                assert result.room_name == sample_room_data["room_name"]
                
                # Verify cache was updated
                mock_cache_set.assert_called()

    @pytest.mark.asyncio
    async def test_get_room_by_id_with_caching(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict,
        performance_timer
    ):
        """Test room retrieval with cache hit validation."""
        timer = performance_timer()
        room_id = sample_room_data["room_id"]
        
        # Test cache hit scenario
        cached_data = {
            "id": 1,
            "room_id": room_id,
            "room_name": sample_room_data["room_name"],
            "room_type": sample_room_data["room_type"],
            "is_active": True,
            "participant_count": 0,
            "max_participants": 10,
            "booking_id": 123,
            "owner_id": 1
        }
        
        with patch.object(room_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = cached_data
            
            with patch.object(room_repository, 'get') as mock_get:
                mock_room = WebSocketRoom(**sample_room_data)
                mock_get.return_value = mock_room
                
                timer.start()
                result = await room_repository.get_room_by_id(room_id)
                timer.stop()
                
                # Performance validation: <50ms for cached lookups
                timer.assert_under_ms(50, "Cached room retrieval")
                
                # Verify cache was checked
                mock_cache_get.assert_called_with(f"room:{room_id}")
                
                # Verify result
                assert result.room_id == room_id

    @pytest.mark.asyncio
    async def test_repository_error_handling(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict
    ):
        """Test repository error handling and rollback."""
        # Test database error scenario
        with patch.object(room_repository, 'create') as mock_create:
            mock_create.side_effect = Exception("Database error")
            
            with patch.object(room_repository.db, 'rollback') as mock_rollback:
                with pytest.raises(ServiceError) as exc_info:
                    await room_repository.create_room(sample_room_data)
                
                # Verify error message
                assert "Failed to create WebSocket room" in str(exc_info.value)
                
                # Verify rollback was called
                mock_rollback.assert_called()

    @pytest.mark.asyncio
    async def test_cache_failure_graceful_degradation(
        self,
        room_repository: WebSocketRoomRepository,
        sample_room_data: dict
    ):
        """Test graceful degradation when cache fails."""
        room_id = sample_room_data["room_id"]
        
        # Mock cache failure
        with patch.object(room_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.side_effect = Exception("Cache error")
            
            # Mock database query
            with patch.object(room_repository.db, 'execute') as mock_execute:
                mock_result = MagicMock()
                mock_result.scalar_one_or_none.return_value = WebSocketRoom(**sample_room_data)
                mock_execute.return_value = mock_result
                
                # Should still work despite cache failure
                result = await room_repository.get_room_by_id(room_id)
                
                # Verify result
                assert result.room_id == room_id
                
                # Verify database was queried
                mock_execute.assert_called()


@pytest.mark.unit
class TestWebSocketRoomParticipantRepository:
    """Unit tests for WebSocketRoomParticipantRepository with bulk operations."""

    @pytest.fixture
    async def participant_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketRoomParticipantRepository instance for testing."""
        return WebSocketRoomParticipantRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_participant_data(self):
        """Sample participant data for testing."""
        return {
            "room_id": 1,
            "user_id": 123,
            "connection_id": 456,
            "role": "participant",
            "is_active": True
        }

    @pytest.mark.asyncio
    async def test_add_participant_with_capacity_check(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict,
        performance_timer
    ):
        """Test participant addition with capacity checking."""
        timer = performance_timer()
        
        # Mock existing participant check
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            # First call: check existing participant (none found)
            mock_existing_result = MagicMock()
            mock_existing_result.scalar_one_or_none.return_value = None
            
            # Second call: get room for capacity check
            mock_room = MagicMock()
            mock_room.participant_count = 5
            mock_room.max_participants = 10
            mock_room_result = MagicMock()
            mock_room_result.scalar_one_or_none.return_value = mock_room
            
            mock_execute.side_effect = [mock_existing_result, mock_room_result]
            
            # Mock participant creation
            with patch.object(participant_repository, 'create') as mock_create:
                mock_participant = WebSocketRoomParticipant(**sample_participant_data)
                mock_create.return_value = mock_participant
                
                with patch.object(participant_repository.db, 'commit') as mock_commit:
                    timer.start()
                    result = await participant_repository.add_participant(
                        room_id=sample_participant_data["room_id"],
                        user_id=sample_participant_data["user_id"],
                        connection_id=sample_participant_data["connection_id"],
                        role=sample_participant_data["role"]
                    )
                    timer.stop()
                    
                    # Performance validation: <100ms for participant addition
                    timer.assert_under_ms(100, "Participant addition")
                    
                    # Verify result
                    assert result.user_id == sample_participant_data["user_id"]
                    assert result.room_id == sample_participant_data["room_id"]

    @pytest.mark.asyncio
    async def test_room_capacity_validation(
        self,
        participant_repository: WebSocketRoomParticipantRepository,
        sample_participant_data: dict
    ):
        """Test room capacity validation during participant addition."""
        # Mock existing participant check (none found)
        with patch.object(participant_repository.db, 'execute') as mock_execute:
            mock_existing_result = MagicMock()
            mock_existing_result.scalar_one_or_none.return_value = None
            
            # Mock room at capacity
            mock_room = MagicMock()
            mock_room.participant_count = 10
            mock_room.max_participants = 10
            mock_room_result = MagicMock()
            mock_room_result.scalar_one_or_none.return_value = mock_room
            
            mock_execute.side_effect = [mock_existing_result, mock_room_result]
            
            # Should raise ServiceError for capacity exceeded
            with pytest.raises(ServiceError) as exc_info:
                await participant_repository.add_participant(
                    room_id=sample_participant_data["room_id"],
                    user_id=sample_participant_data["user_id"]
                )
            
            assert "Room is at capacity" in str(exc_info.value)


@pytest.mark.unit
class TestEnhancedWebSocketConnectionRepository:
    """Unit tests for enhanced WebSocketConnectionRepository with room integration."""

    @pytest.fixture
    async def connection_repository(self, db_session: AsyncSession, cache_manager: CacheManager):
        """Create WebSocketConnectionRepository instance for testing."""
        return WebSocketConnectionRepository(db_session, cache_manager)

    @pytest.fixture
    def sample_connection_data(self):
        """Sample connection data for testing."""
        return {
            "connection_id": "ws_conn_123",
            "user_id": 123,
            "status": ConnectionStatus.CONNECTED,
            "client_type": "web",
            "ip_address": "*************"
        }

    @pytest.mark.asyncio
    async def test_get_connections_in_room_performance(
        self,
        connection_repository: WebSocketConnectionRepository,
        sample_connection_data: dict,
        performance_timer
    ):
        """Test room connection queries with performance validation."""
        timer = performance_timer()
        room_id = 1
        
        # Mock cache miss and database query
        with patch.object(connection_repository.cache, 'get') as mock_cache_get:
            mock_cache_get.return_value = None
            
            with patch.object(connection_repository.db, 'execute') as mock_execute:
                mock_result = MagicMock()
                mock_result.scalars.return_value.all.return_value = [
                    WebSocketConnection(**sample_connection_data)
                ]
                mock_execute.return_value = mock_result
                
                # Mock cache update
                with patch.object(connection_repository.cache, 'set') as mock_cache_set:
                    timer.start()
                    result = await connection_repository.get_connections_in_room(room_id)
                    timer.stop()
                    
                    # Performance validation: <150ms for room connection queries
                    timer.assert_under_ms(150, "Room connection query")
                    
                    # Verify result
                    assert len(result) == 1
                    assert result[0].connection_id == sample_connection_data["connection_id"]
                    
                    # Verify cache was updated
                    mock_cache_set.assert_called()

    @pytest.mark.asyncio
    async def test_repository_error_handling_and_logging(
        self,
        connection_repository: WebSocketConnectionRepository
    ):
        """Test comprehensive error handling and logging."""
        room_id = 1
        
        # Test database error scenario
        with patch.object(connection_repository.db, 'execute') as mock_execute:
            mock_execute.side_effect = Exception("Database connection failed")
            
            with pytest.raises(ServiceError) as exc_info:
                await connection_repository.get_connections_in_room(room_id)
            
            # Verify error message
            assert "Failed to get room connections" in str(exc_info.value)
