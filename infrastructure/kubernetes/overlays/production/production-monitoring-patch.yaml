# Culture Connect Backend - Production Monitoring Patch
# Comprehensive monitoring configurations for production environment

apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
spec:
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        prometheus.io/interval: "15s"
        fluentd.org/include: "true"
        fluentd.org/exclude-path: "/health,/metrics"
        co.elastic.logs/enabled: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.add_error_key: "true"
    spec:
      containers:
      - name: api
        env:
        - name: CC_METRICS_ENABLED
          value: "true"
        - name: CC_METRICS_PORT
          value: "9090"
        - name: CC_HEALTH_CHECK_ENABLED
          value: "true"
        - name: CC_HEALTH_CHECK_PATH
          value: "/health"
        - name: CC_APM_ENABLED
          value: "true"
        - name: CC_APM_SERVICE_NAME
          value: "culture-connect-production"
        - name: CC_APM_ENVIRONMENT
          value: "production"
        - name: CC_SENTRY_TRACES_SAMPLE_RATE
          value: "0.1"
        - name: CC_SENTRY_PROFILES_SAMPLE_RATE
          value: "0.1"
        - name: CC_LOG_FORMAT
          value: "json"
        - name: CC_LOG_DATE_FORMAT
          value: "%Y-%m-%d %H:%M:%S"
        - name: CC_LOG_FILE_PATH
          value: "/app/logs/culture_connect_production.log"
        - name: CC_LOG_MAX_BYTES
          value: "104857600"
        - name: CC_LOG_BACKUP_COUNT
          value: "10"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-worker
spec:
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9540"
        prometheus.io/path: "/metrics"
        prometheus.io/interval: "30s"
        fluentd.org/include: "true"
    spec:
      containers:
      - name: celery-worker
        env:
        - name: CC_CELERY_METRICS_ENABLED
          value: "true"
        - name: CC_CELERY_MONITORING_PORT
          value: "9540"
        - name: CELERY_WORKER_SEND_TASK_EVENTS
          value: "true"
        - name: CELERY_TASK_SEND_SENT_EVENT
          value: "true"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-beat
spec:
  template:
    metadata:
      annotations:
        fluentd.org/include: "true"
    spec:
      containers:
      - name: celery-beat
        env:
        - name: CELERY_BEAT_SCHEDULE_FILENAME
          value: "/app/celerybeat-schedule"
