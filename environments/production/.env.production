# Culture Connect Backend - Production Environment Configuration
# Production-specific configuration for enterprise deployment
#
# This file contains production-specific configuration values optimized for
# enterprise-grade performance, security, and reliability.
#
# Security Notice:
# - This file contains sensitive configuration values
# - Ensure proper file permissions (600) in production
# - Use environment-specific secret management systems
# - Never commit actual production values to version control

# =============================================================================
# ENVIRONMENT IDENTIFICATION
# =============================================================================

# Environment Configuration
ENVIRONMENT=production
CC_ENVIRONMENT=production
CC_VERSION=production-latest
CC_BUILD_ID=production-build-001

# Application Configuration
CC_DEBUG=false
CC_LOG_LEVEL=INFO
CC_WORKERS=4
CC_MAX_CONNECTIONS=1000
CC_KEEPALIVE_TIMEOUT=65
CC_GRACEFUL_TIMEOUT=30

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Configuration
CC_DATABASE_URL=postgresql+asyncpg://culture_connect_prod:REPLACE_WITH_SECURE_PASSWORD@db:5432/culture_connect_prod_db
CC_POSTGRES_DB=culture_connect_prod_db
CC_POSTGRES_USER=culture_connect_prod
CC_POSTGRES_PASSWORD=REPLACE_WITH_SECURE_PASSWORD
CC_POSTGRES_PORT=5432

# Database Replication (Production)
CC_POSTGRES_REPLICATION_USER=replicator_prod
CC_POSTGRES_REPLICATION_PASSWORD=REPLACE_WITH_SECURE_REPLICATION_PASSWORD

# Database Performance (Production Optimized)
CC_DATABASE_POOL_SIZE=20
CC_DATABASE_MAX_OVERFLOW=40
CC_DATABASE_POOL_TIMEOUT=30
CC_DATABASE_POOL_RECYCLE=3600

# PostgreSQL Performance Tuning (Production)
CC_POSTGRES_SHARED_BUFFERS=256MB
CC_POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
CC_POSTGRES_MAINTENANCE_WORK_MEM=64MB
CC_POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
CC_POSTGRES_WAL_BUFFERS=16MB
CC_POSTGRES_DEFAULT_STATISTICS_TARGET=100

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Configuration
CC_REDIS_URL=redis://:REPLACE_WITH_SECURE_REDIS_PASSWORD@redis:6379/0
CC_REDIS_PASSWORD=REPLACE_WITH_SECURE_REDIS_PASSWORD
CC_REDIS_PORT=6379
CC_REDIS_DB=0

# Redis Performance (Production)
CC_REDIS_MAX_CONNECTIONS=100
CC_REDIS_RETRY_ON_TIMEOUT=true
CC_REDIS_SOCKET_KEEPALIVE=true
CC_REDIS_SOCKET_KEEPALIVE_OPTIONS={}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
CC_SECRET_KEY=REPLACE_WITH_SECURE_SECRET_KEY_256_BITS_MINIMUM
CC_JWT_SECRET_KEY=REPLACE_WITH_SECURE_JWT_SECRET_KEY_256_BITS_MINIMUM
CC_JWT_ALGORITHM=HS256
CC_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
CC_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth Configuration (Production)
CC_GOOGLE_CLIENT_ID=REPLACE_WITH_PRODUCTION_GOOGLE_CLIENT_ID
CC_GOOGLE_CLIENT_SECRET=REPLACE_WITH_PRODUCTION_GOOGLE_CLIENT_SECRET
CC_FACEBOOK_APP_ID=REPLACE_WITH_PRODUCTION_FACEBOOK_APP_ID
CC_FACEBOOK_APP_SECRET=REPLACE_WITH_PRODUCTION_FACEBOOK_APP_SECRET

# =============================================================================
# PAYMENT PROVIDER CONFIGURATION (PRODUCTION)
# =============================================================================

# Paystack Configuration (Live Mode)
CC_PAYSTACK_SECRET_KEY=sk_live_REPLACE_WITH_LIVE_PAYSTACK_SECRET_KEY
CC_PAYSTACK_PUBLIC_KEY=pk_live_REPLACE_WITH_LIVE_PAYSTACK_PUBLIC_KEY
CC_PAYSTACK_WEBHOOK_SECRET=REPLACE_WITH_LIVE_PAYSTACK_WEBHOOK_SECRET
CC_PAYSTACK_BASE_URL=https://api.paystack.co

# Stripe Configuration (Live Mode)
CC_STRIPE_SECRET_KEY=sk_live_REPLACE_WITH_LIVE_STRIPE_SECRET_KEY
CC_STRIPE_PUBLIC_KEY=pk_live_REPLACE_WITH_LIVE_STRIPE_PUBLIC_KEY
CC_STRIPE_WEBHOOK_SECRET=whsec_REPLACE_WITH_LIVE_STRIPE_WEBHOOK_SECRET
CC_STRIPE_API_VERSION=2023-10-16

# Busha Cryptocurrency Configuration (Production)
CC_BUSHA_API_KEY=REPLACE_WITH_LIVE_BUSHA_API_KEY
CC_BUSHA_SECRET_KEY=REPLACE_WITH_LIVE_BUSHA_SECRET_KEY
CC_BUSHA_BASE_URL=https://api.busha.co
CC_BUSHA_WEBHOOK_SECRET=REPLACE_WITH_LIVE_BUSHA_WEBHOOK_SECRET

# =============================================================================
# AI/ML INTEGRATION CONFIGURATION (PRODUCTION)
# =============================================================================

# AIML API Configuration
CC_AIML_API_KEY=REPLACE_WITH_PRODUCTION_AIML_API_KEY
CC_AIML_BASE_URL=https://api.aimlapi.com
CC_AIML_MODEL=gpt-4
CC_AIML_MAX_TOKENS=1000
CC_AIML_TEMPERATURE=0.7

# =============================================================================
# GEOLOCATION CONFIGURATION (PRODUCTION)
# =============================================================================

# MaxMind GeoIP Configuration
CC_GEOIP_DATABASE_PATH=/app/data/GeoLite2-Country.mmdb
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_GEOLOCATION_FALLBACK_COUNTRY=NG
MAXMIND_LICENSE_KEY=REPLACE_WITH_PRODUCTION_MAXMIND_LICENSE_KEY

# Geolocation Performance (Production)
CC_GEOLOCATION_TIMEOUT_MS=3000
CC_GEOLOCATION_MAX_RETRIES=3
CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD=10
CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT=60

# Geolocation Caching (Production)
CC_GEOLOCATION_CACHE_TTL_HOURS=24
CC_GEOLOCATION_CACHE_MAX_SIZE=10000
CC_GEOLOCATION_CACHE_COMPRESSION=true
CC_GEOLOCATION_REDIS_PREFIX=geo:prod:
CC_GEOLOCATION_REDIS_TTL=86400  # 24 hours

# =============================================================================
# EXTERNAL SERVICE CONFIGURATION
# =============================================================================

# Frontend URLs (Production)
CC_FRONTEND_URL=https://cultureconnect.ng
CC_PWA_URL=https://app.cultureconnect.ng
CC_ADMIN_URL=https://admin.cultureconnect.ng

# API Configuration
CC_API_BASE_URL=https://api.cultureconnect.ng
CC_API_VERSION=v1
CC_API_TITLE=Culture Connect Backend API
CC_API_DESCRIPTION=Culture Connect Backend API - Production Environment

# =============================================================================
# MONITORING AND LOGGING (PRODUCTION)
# =============================================================================

# Sentry Configuration (Production)
CC_SENTRY_DSN=REPLACE_WITH_PRODUCTION_SENTRY_DSN
CC_SENTRY_ENVIRONMENT=production
CC_SENTRY_TRACES_SAMPLE_RATE=0.1
CC_SENTRY_PROFILES_SAMPLE_RATE=0.1

# Logging Configuration
CC_LOG_FORMAT=json
CC_LOG_DATE_FORMAT=%Y-%m-%d %H:%M:%S
CC_LOG_FILE_PATH=/app/logs/culture_connect_production.log
CC_LOG_MAX_BYTES=104857600  # 100MB
CC_LOG_BACKUP_COUNT=10

# =============================================================================
# EMAIL CONFIGURATION (PRODUCTION)
# =============================================================================

# SMTP Configuration (Production)
CC_SMTP_HOST=smtp.cultureconnect.ng
CC_SMTP_PORT=587
CC_SMTP_USERNAME=REPLACE_WITH_PRODUCTION_SMTP_USERNAME
CC_SMTP_PASSWORD=REPLACE_WITH_PRODUCTION_SMTP_PASSWORD
CC_SMTP_USE_TLS=true
CC_SMTP_USE_SSL=false

# Email Settings
CC_EMAIL_FROM=<EMAIL>
CC_EMAIL_FROM_NAME=Culture Connect
CC_EMAIL_TEMPLATE_DIR=/app/templates/email

# =============================================================================
# CELERY CONFIGURATION (PRODUCTION)
# =============================================================================

# Celery Broker Configuration
CC_CELERY_BROKER_URL=redis://:REPLACE_WITH_SECURE_REDIS_PASSWORD@redis:6379/1
CC_CELERY_RESULT_BACKEND=redis://:REPLACE_WITH_SECURE_REDIS_PASSWORD@redis:6379/2

# Celery Worker Configuration
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_PREFETCH_MULTIPLIER=4
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
CELERY_WORKER_DISABLE_RATE_LIMITS=false

# Celery Task Configuration
CC_CELERY_TASK_SERIALIZER=json
CC_CELERY_RESULT_SERIALIZER=json
CC_CELERY_ACCEPT_CONTENT=["json"]
CC_CELERY_TIMEZONE=UTC
CC_CELERY_ENABLE_UTC=true

# =============================================================================
# FEATURE FLAGS (PRODUCTION)
# =============================================================================

# Feature Toggles
CC_ENABLE_PROMOTIONAL_SYSTEM=true
CC_ENABLE_CRYPTO_PAYMENTS=true
CC_ENABLE_AI_OPTIMIZATION=true
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_ENABLE_WEBSOCKET_COMMUNICATION=true
CC_ENABLE_BACKGROUND_JOBS=true
CC_ENABLE_ANALYTICS_SYSTEM=true
CC_ENABLE_PERFORMANCE_MONITORING=true

# Production Features
CC_ENABLE_DEBUG_TOOLBAR=false
CC_ENABLE_PROFILING=false
CC_ENABLE_HOT_RELOAD=false

# =============================================================================
# DEPLOYMENT CONFIGURATION (PRODUCTION)
# =============================================================================

# Container Configuration
CC_DATA_PATH=/opt/culture-connect/data
CC_LOG_PATH=/opt/culture-connect/logs
CC_BACKUP_PATH=/opt/culture-connect/backups
CC_UPLOAD_PATH=/opt/culture-connect/uploads

# Resource Limits (Production)
CC_MAX_MEMORY_MB=1024
CC_MAX_CPU_PERCENT=80
CC_MAX_CONNECTIONS_TOTAL=1000

# Health Check Configuration
CC_HEALTH_CHECK_INTERVAL=30
CC_HEALTH_CHECK_TIMEOUT=10
CC_HEALTH_CHECK_RETRIES=3

# =============================================================================
# SSL/TLS CONFIGURATION (PRODUCTION)
# =============================================================================

# SSL Configuration
CC_SSL_ENABLED=true
CC_SSL_CERT_PATH=/etc/ssl/certs/cultureconnect.ng.crt
CC_SSL_KEY_PATH=/etc/ssl/private/cultureconnect.ng.key
CC_SSL_CA_PATH=/etc/ssl/certs/ca-certificates.crt

# Security Headers
CC_SECURITY_HEADERS_ENABLED=true
CC_HSTS_MAX_AGE=31536000
CC_CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'

# =============================================================================
# LOAD BALANCER CONFIGURATION (PRODUCTION)
# =============================================================================

# Load Balancer Configuration
CC_LOAD_BALANCER_STRATEGY=round_robin
CC_LOAD_BALANCER_HEALTH_CHECK_PATH=/health
CC_LOAD_BALANCER_HEALTH_CHECK_INTERVAL=30s

# Service Discovery
CC_SERVICE_DISCOVERY_ENABLED=true
CC_SERVICE_NAME=culture-connect-production
CC_SERVICE_PORT=8000

# =============================================================================
# BACKUP CONFIGURATION (PRODUCTION)
# =============================================================================

# Backup Settings
CC_BACKUP_ENABLED=true
CC_BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
CC_BACKUP_RETENTION_DAYS=30
CC_BACKUP_COMPRESSION=true
CC_BACKUP_ENCRYPTION_KEY=REPLACE_WITH_SECURE_BACKUP_ENCRYPTION_KEY

# AWS S3 Backup Configuration
CC_AWS_ACCESS_KEY_ID=REPLACE_WITH_PRODUCTION_AWS_ACCESS_KEY
CC_AWS_SECRET_ACCESS_KEY=REPLACE_WITH_PRODUCTION_AWS_SECRET_KEY
CC_AWS_REGION=us-west-2
CC_AWS_S3_BUCKET=culture-connect-production-backups

# =============================================================================
# RATE LIMITING & SECURITY (PRODUCTION)
# =============================================================================

# Rate Limiting
CC_RATE_LIMITING_ENABLED=true
CC_RATE_LIMIT_REQUESTS=5000
CC_RATE_LIMIT_WINDOW=3600

# Security Headers
CC_ENABLE_SECURITY_HEADERS=true
CC_ENABLE_CORS_PROTECTION=true
CC_ENABLE_CSRF_PROTECTION=true

# =============================================================================
# WEBSOCKET CONFIGURATION (PRODUCTION)
# =============================================================================

# WebSocket Configuration
CC_WEBSOCKET_HOST=0.0.0.0
CC_WEBSOCKET_PORT=8001
CC_WEBSOCKET_PATH=/ws
CC_WEBSOCKET_MAX_CONNECTIONS=10000
CC_WEBSOCKET_PING_INTERVAL=30
CC_WEBSOCKET_PING_TIMEOUT=10

# =============================================================================
# PERFORMANCE MONITORING (PRODUCTION)
# =============================================================================

# Performance Metrics
CC_METRICS_ENABLED=true
CC_METRICS_PORT=9090
CC_HEALTH_CHECK_ENABLED=true
CC_HEALTH_CHECK_PATH=/health

# APM Configuration
CC_APM_ENABLED=true
CC_APM_SERVICE_NAME=culture-connect-production
CC_APM_ENVIRONMENT=production

# =============================================================================
# CACHE CONFIGURATION (PRODUCTION)
# =============================================================================

# Cache Settings
CC_CACHE_TTL=3600
CC_CACHE_MAX_SIZE=10000
CC_CACHE_ENABLED=true

# =============================================================================
# PRODUCTION SPECIFIC SETTINGS
# =============================================================================

# Production Server
CC_RELOAD=false
CC_ACCESS_LOG=true
CC_USE_COLORS=false

# Production Database
CC_ECHO_SQL=false
CC_SHOW_SQL_QUERIES=false

# Production Optimization
CC_ENABLE_GZIP=true
CC_ENABLE_STATIC_FILE_SERVING=false
CC_ENABLE_CORS=true

# =============================================================================
# COMPLIANCE & PRIVACY (PRODUCTION)
# =============================================================================

# GDPR Compliance
CC_GDPR_COMPLIANCE_ENABLED=true
CC_DATA_RETENTION_DAYS=90
CC_ANONYMIZE_IPS=true
CC_CONSENT_REQUIRED=true

# Audit Configuration
CC_AUDIT_ENABLED=true
CC_AUDIT_RETENTION_DAYS=365
CC_AUDIT_ENCRYPTION=true

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================

# 1. Security Setup:
#    - Set file permissions: chmod 600 .env.production
#    - Use secret management system for sensitive values
#    - Rotate keys and tokens regularly
#
# 2. Monitoring Setup:
#    - Configure webhook endpoints for alerts
#    - Set up log aggregation and monitoring
#    - Implement health check endpoints
#
# 3. Performance Optimization:
#    - Tune cache TTL values based on traffic patterns
#    - Monitor and adjust timeout values
#    - Scale Redis cluster as needed
#
# 4. Backup and Recovery:
#    - Verify backup procedures regularly
#    - Test disaster recovery scenarios
#    - Monitor backup storage usage
#
# 5. Compliance:
#    - Review data retention policies
#    - Implement privacy controls
#    - Maintain audit trails
