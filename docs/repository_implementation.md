# Repository Pattern Implementation - Task 1.3.2

## 📋 Overview

This document provides comprehensive documentation for the Repository Pattern implementation completed as part of Task 1.3.2. The implementation provides a production-grade, type-safe, and performance-optimized repository layer for the Culture Connect Backend API.

## 🏗️ Architecture

### Core Components

1. **BaseRepositoryCore** - Core async CRUD operations with performance tracking
2. **EnhancedBaseRepository** - Advanced features including pagination and filtering
3. **BulkOperationsMixin** - Optimized bulk operations for high-performance scenarios
4. **BaseRepository** - Complete repository combining all features
5. **Parameter Classes** - Type-safe parameter containers for queries

### File Structure

```
app/repositories/
├── __init__.py                 # Repository exports and imports
├── base.py                     # Core repository classes and parameter types
├── enhanced_base.py            # Advanced pagination and filtering features
├── bulk_operations.py          # Bulk operations mixin and optimizations
└── vendor_repository.py       # Vendor-specific repository implementations
```

## 🔧 Core Features

### 1. Generic Type Safety

```python
from app.repositories import BaseRepository, PaginationParams
from app.models import User

# Type-safe repository instantiation
user_repository = BaseRepository[User](User, db_session)

# Type hints ensure compile-time safety
user: User = await user_repository.get(1)
users: List[User] = await user_repository.get_all()
```

### 2. Async CRUD Operations

All repository operations are fully async with SQLAlchemy 2.0 support:

```python
# Create
user = await user_repository.create({"name": "John", "email": "<EMAIL>"})

# Read
user = await user_repository.get(1)
user = await user_repository.get_by_uuid("uuid-string")
user = await user_repository.get_by_field("email", "<EMAIL>")

# Update
updated_user = await user_repository.update(1, {"name": "John Doe"})

# Delete
success = await user_repository.delete(1)

# Count and existence checks
count = await user_repository.count()
exists = await user_repository.exists(1)
```

### 3. Advanced Pagination

#### Offset-based Pagination
```python
from app.repositories import PaginationParams

# Create pagination parameters
pagination = PaginationParams(page=2, size=20)  # Page 2, 20 items per page

# Get paginated results
result = await user_repository.get_paginated(pagination)

print(f"Page: {result.page}")
print(f"Total: {result.total}")
print(f"Has next: {result.has_next}")
print(f"Items: {len(result.items)}")
```

#### Cursor-based Pagination
```python
from app.repositories import CursorPaginationParams

# Create cursor pagination parameters
pagination = CursorPaginationParams(size=20, direction="forward")

# Get cursor-based results (better for large datasets)
result = await user_repository.get_cursor_paginated(pagination)

print(f"Next cursor: {result.next_cursor}")
print(f"Has next: {result.has_next}")
```

### 4. Dynamic Filtering

```python
from app.repositories import FilterParams

# Create filter parameters
filters = [
    FilterParams(field="name", operator="like", value="John"),
    FilterParams(field="age", operator="gte", value=18),
    FilterParams(field="status", operator="in", value=["active", "pending"])
]

# Apply filters to queries
result = await user_repository.get_paginated(
    pagination=PaginationParams(page=1, size=10),
    filters=filters
)
```

#### Supported Filter Operators
- `eq` - Equal to
- `ne` - Not equal to
- `gt` - Greater than
- `gte` - Greater than or equal to
- `lt` - Less than
- `lte` - Less than or equal to
- `like` - SQL LIKE pattern matching
- `ilike` - Case-insensitive LIKE
- `in` - Value in list
- `not_in` - Value not in list

### 5. Flexible Sorting

```python
from app.repositories import SortParams

# Create sort parameters
sorts = [
    SortParams(field="created_at", direction="desc"),
    SortParams(field="name", direction="asc")
]

# Apply sorting to queries
result = await user_repository.get_paginated(
    pagination=PaginationParams(page=1, size=10),
    sorts=sorts
)
```

### 6. Bulk Operations

```python
from app.repositories import FullRepository

# Bulk create
users_data = [
    {"name": "User 1", "email": "<EMAIL>"},
    {"name": "User 2", "email": "<EMAIL>"}
]
created_users = await user_repository.bulk_create(users_data)

# Bulk update
updates = [
    {"id": 1, "name": "Updated User 1"},
    {"id": 2, "name": "Updated User 2"}
]
updated_count = await user_repository.bulk_update(updates)

# Bulk delete
deleted_count = await user_repository.bulk_delete([1, 2, 3])

# Bulk upsert (insert or update)
upsert_count = await user_repository.bulk_upsert(
    objects=users_data,
    conflict_columns=["email"],
    update_columns=["name"]
)
```

## 📊 Performance Monitoring

### Automatic Performance Tracking

All repository operations automatically track performance metrics:

```python
# Performance metrics are automatically collected
user = await user_repository.get(1)

# Access performance metrics
metrics = user_repository._query_metrics
for metric in metrics:
    print(f"Operation: {metric.query_type}")
    print(f"Duration: {metric.query_time}s")
    print(f"Rows: {metric.row_count}")
    print(f"Table: {metric.table_name}")
```

### Integration with Monitoring System

Performance data is automatically sent to the monitoring system:

```python
# Metrics are automatically reported to metrics_collector
# No additional code required - happens transparently
```

## 🛡️ Error Handling

### Repository Error Hierarchy

```python
from app.repositories import RepositoryError

try:
    user = await user_repository.get(999)
except RepositoryError as e:
    print(f"Repository error: {e}")
    print(f"Original error: {e.original_error}")
```

### Automatic Error Recovery

- Database connection errors are handled gracefully
- Transaction rollbacks on failures
- Performance metrics tracked even for failed operations
- Detailed error logging with correlation IDs

## 🔧 Usage Examples

### Basic Repository Usage

```python
from app.repositories import BaseRepository
from app.models import User
from app.db.session import get_async_session

async def user_service_example():
    async with get_async_session() as session:
        user_repo = BaseRepository[User](User, session)
        
        # Create a new user
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "age": 30
        }
        user = await user_repo.create(user_data)
        
        # Get paginated users with filters
        pagination = PaginationParams(page=1, size=10)
        filters = [FilterParams(field="age", operator="gte", value=18)]
        sorts = [SortParams(field="created_at", direction="desc")]
        
        result = await user_repo.get_paginated(
            pagination=pagination,
            filters=filters,
            sorts=sorts
        )
        
        return {
            "user": user,
            "paginated_users": result.items,
            "total_users": result.total,
            "has_next_page": result.has_next
        }
```

### Advanced Bulk Operations

```python
async def bulk_operations_example():
    async with get_async_session() as session:
        user_repo = FullRepository(User, session)
        
        # Bulk create with conflict handling
        users_data = [
            {"email": "<EMAIL>", "name": "User 1"},
            {"email": "<EMAIL>", "name": "User 2"}
        ]
        
        # Upsert users (insert new, update existing)
        processed_count = await user_repo.bulk_upsert(
            objects=users_data,
            conflict_columns=["email"],
            update_columns=["name"],
            batch_size=1000
        )
        
        return f"Processed {processed_count} users"
```

## 🧪 Testing

### Test Coverage

The repository implementation includes comprehensive tests:

- **Parameter Validation Tests** - Pagination, filtering, sorting parameter validation
- **CRUD Operation Tests** - All async CRUD operations with mocking
- **Pagination Tests** - Both offset and cursor-based pagination
- **Bulk Operation Tests** - Bulk create, update, delete, upsert operations
- **Error Handling Tests** - Repository error scenarios and recovery
- **Performance Tests** - Query performance tracking and metrics

### Running Tests

```bash
# Run all repository tests
pytest tests/unit/repositories/ -v

# Run specific test files
pytest tests/unit/repositories/test_base_repository.py -v
pytest tests/unit/repositories/test_pagination.py -v
pytest tests/unit/repositories/test_bulk_operations.py -v
```

## 🚀 Next Steps

With Task 1.3.2 completed, the repository layer is ready for:

1. **Task 1.3.3** - Schema validation and serialization
2. **Integration with Service Layer** - Enhanced service-repository integration
3. **Model Implementation** - Database models using the repository pattern
4. **API Endpoint Development** - REST APIs leveraging repository features

## 📚 References

- [SQLAlchemy 2.0 Async Documentation](https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html)
- [FastAPI Async Database Guide](https://fastapi.tiangolo.com/advanced/async-sql-databases/)
- [Repository Pattern Best Practices](https://docs.microsoft.com/en-us/dotnet/architecture/microservices/microservice-ddd-cqrs-patterns/infrastructure-persistence-layer-design)
- [Culture Connect Backend Guardrails](../guardrails.md)
