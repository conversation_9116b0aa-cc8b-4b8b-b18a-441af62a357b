"""
Unit tests for availability schemas.

This module provides comprehensive unit tests for availability Pydantic schemas:
- VendorAvailabilityCreateSchema/UpdateSchema/ResponseSchema: Configuration validation
- RecurringAvailabilityCreateSchema/UpdateSchema/ResponseSchema: Pattern validation
- AvailabilitySlotCreateSchema/UpdateSchema/ResponseSchema: Slot validation
- AvailabilityExceptionCreateSchema/UpdateSchema/ResponseSchema: Exception validation
- BulkSlotCreateSchema/BulkSlotResponseSchema: Bulk operation validation
- AvailabilityCheckResponseSchema: Real-time checking validation

Implements Task 4.1.2 Phase 6 requirements with >85% test coverage.
"""

import pytest
from datetime import datetime, date, time, timedelta
from decimal import Decimal
from typing import Dict, Any
import pytz

from pydantic import ValidationError

from app.schemas.availability_schemas import (
    VendorAvailabilityCreateSchema, VendorAvailabilityUpdateSchema, VendorAvailabilityResponseSchema,
    RecurringAvailabilityCreateSchema, RecurringAvailabilityUpdateSchema, RecurringAvailabilityResponseSchema,
    AvailabilitySlotCreateSchema, AvailabilitySlotUpdateSchema, AvailabilitySlotResponseSchema,
    AvailabilityExceptionCreateSchema, AvailabilityExceptionUpdateSchema, AvailabilityExceptionResponseSchema,
    BulkSlotCreateSchema, BulkSlotResponseSchema, AvailabilityCheckResponseSchema,
    AvailabilitySlotListResponseSchema
)


class TestVendorAvailabilitySchemas:
    """Test cases for vendor availability schemas."""

    @pytest.fixture
    def valid_vendor_availability_data(self):
        """Valid vendor availability data."""
        return {
            "timezone": "America/New_York",
            "advance_booking_days": 90,
            "min_booking_notice_hours": 24,
            "max_booking_notice_days": 30,
            "default_slot_duration_minutes": 60,
            "buffer_time_minutes": 15,
            "earliest_booking_time": "09:00:00",
            "latest_booking_time": "17:00:00"
        }

    def test_vendor_availability_create_schema_valid(self, valid_vendor_availability_data):
        """Test valid vendor availability creation schema."""
        schema = VendorAvailabilityCreateSchema(**valid_vendor_availability_data)

        assert schema.timezone == "America/New_York"
        assert schema.advance_booking_days == 90
        assert schema.min_booking_notice_hours == 24
        assert schema.max_booking_notice_days == 30
        assert schema.default_slot_duration_minutes == 60
        assert schema.buffer_time_minutes == 15
        assert schema.earliest_booking_time == time(9, 0)
        assert schema.latest_booking_time == time(17, 0)

    def test_vendor_availability_timezone_validation(self, valid_vendor_availability_data):
        """Test timezone validation."""
        # Valid timezone
        schema = VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert schema.timezone == "America/New_York"

        # Invalid timezone should raise validation error
        valid_vendor_availability_data["timezone"] = "Invalid/Timezone"
        with pytest.raises(ValidationError) as exc_info:
            VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert "Invalid timezone" in str(exc_info.value)

    def test_vendor_availability_time_range_validation(self, valid_vendor_availability_data):
        """Test time range validation."""
        # Valid time range
        schema = VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert schema.earliest_booking_time < schema.latest_booking_time

        # Invalid time range (earliest after latest)
        valid_vendor_availability_data["earliest_booking_time"] = "18:00:00"
        valid_vendor_availability_data["latest_booking_time"] = "09:00:00"

        with pytest.raises(ValidationError) as exc_info:
            VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert "earliest_booking_time must be before latest_booking_time" in str(exc_info.value)

    def test_vendor_availability_booking_notice_validation(self, valid_vendor_availability_data):
        """Test booking notice period validation."""
        # Valid notice periods
        schema = VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert schema.min_booking_notice_hours <= schema.max_booking_notice_days * 24

        # Invalid notice periods (min > max)
        valid_vendor_availability_data["min_booking_notice_hours"] = 48
        valid_vendor_availability_data["max_booking_notice_days"] = 1

        with pytest.raises(ValidationError) as exc_info:
            VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        assert "min_booking_notice_hours cannot exceed max_booking_notice_days" in str(exc_info.value)

    def test_vendor_availability_advance_booking_validation(self, valid_vendor_availability_data):
        """Test advance booking validation."""
        # Valid advance booking
        schema = VendorAvailabilityCreateSchema(**valid_vendor_availability_data)
        # Note: advance_booking_days can be less than max_booking_notice_days in the current schema
        assert schema.advance_booking_days > 0
        assert schema.max_booking_notice_days > 0

    def test_vendor_availability_response_schema(self, valid_vendor_availability_data):
        """Test vendor availability response schema."""
        # Add response-specific fields
        response_data = {
            **valid_vendor_availability_data,
            "id": 1,
            "vendor_id": 123,
            "service_id": None,
            "is_active": True,
            "max_daily_bookings": None,
            "notes": None
        }

        schema = VendorAvailabilityResponseSchema(**response_data)
        assert schema.id == 1
        assert schema.vendor_id == 123
        assert schema.service_id is None
        assert schema.is_active is True


class TestRecurringAvailabilitySchemas:
    """Test cases for recurring availability schemas."""

    @pytest.fixture
    def valid_recurring_data(self):
        """Valid recurring availability data."""
        return {
            "pattern_type": "weekly",
            "day_of_week": 1,
            "start_time": "10:00:00",
            "end_time": "16:00:00",
            "slot_duration_minutes": 60,
            "max_bookings_per_slot": 2,
            "valid_from": (date.today() + timedelta(days=1)).isoformat(),
            "auto_generate": True
        }

    def test_recurring_availability_create_schema_valid(self, valid_recurring_data):
        """Test valid recurring availability creation schema."""
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)

        assert schema.pattern_type == "weekly"
        assert schema.day_of_week == 1
        assert schema.start_time == time(10, 0)
        assert schema.end_time == time(16, 0)
        assert schema.slot_duration_minutes == 60
        assert schema.max_bookings_per_slot == 2
        assert schema.auto_generate is True

    def test_recurring_pattern_type_validation(self, valid_recurring_data):
        """Test pattern type validation."""
        # Test weekly pattern (valid)
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert schema.pattern_type == "weekly"

        # Test daily pattern (remove day_of_week)
        daily_data = valid_recurring_data.copy()
        daily_data["pattern_type"] = "daily"
        daily_data["day_of_week"] = None
        schema = RecurringAvailabilityCreateSchema(**daily_data)
        assert schema.pattern_type == "daily"

        # Test monthly pattern
        monthly_data = valid_recurring_data.copy()
        monthly_data["pattern_type"] = "monthly"
        monthly_data["day_of_week"] = None
        monthly_data["day_of_month"] = 15
        schema = RecurringAvailabilityCreateSchema(**monthly_data)
        assert schema.pattern_type == "monthly"

        # Invalid pattern type
        invalid_data = valid_recurring_data.copy()
        invalid_data["pattern_type"] = "invalid"
        with pytest.raises(ValidationError):
            RecurringAvailabilityCreateSchema(**invalid_data)

    def test_weekly_pattern_validation(self, valid_recurring_data):
        """Test weekly pattern validation."""
        # Valid weekly pattern
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert schema.pattern_type == "weekly"
        assert schema.day_of_week == 1

        # Invalid day of week
        valid_recurring_data["day_of_week"] = 7  # Should be 0-6
        with pytest.raises(ValidationError):
            RecurringAvailabilityCreateSchema(**valid_recurring_data)

    def test_monthly_pattern_validation(self, valid_recurring_data):
        """Test monthly pattern validation."""
        # Monthly pattern with day of month
        monthly_data = valid_recurring_data.copy()
        monthly_data["pattern_type"] = "monthly"
        monthly_data["day_of_month"] = 15
        monthly_data["day_of_week"] = None

        schema = RecurringAvailabilityCreateSchema(**monthly_data)
        assert schema.pattern_type == "monthly"
        assert schema.day_of_month == 15
        assert schema.day_of_week is None

    def test_time_range_validation(self, valid_recurring_data):
        """Test time range validation."""
        # Valid time range
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert schema.start_time < schema.end_time

        # Invalid time range
        valid_recurring_data["start_time"] = "16:00:00"
        valid_recurring_data["end_time"] = "10:00:00"

        with pytest.raises(ValidationError) as exc_info:
            RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert "start_time must be before end_time" in str(exc_info.value)

    def test_slot_duration_validation(self, valid_recurring_data):
        """Test slot duration validation."""
        # Valid duration
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert 15 <= schema.slot_duration_minutes <= 480

        # Invalid duration (too short)
        valid_recurring_data["slot_duration_minutes"] = 10
        with pytest.raises(ValidationError):
            RecurringAvailabilityCreateSchema(**valid_recurring_data)

        # Invalid duration (too long)
        valid_recurring_data["slot_duration_minutes"] = 500
        with pytest.raises(ValidationError):
            RecurringAvailabilityCreateSchema(**valid_recurring_data)

    def test_future_date_validation(self, valid_recurring_data):
        """Test future date validation."""
        # Valid future date
        schema = RecurringAvailabilityCreateSchema(**valid_recurring_data)
        assert schema.valid_from > date.today()

        # Invalid past date
        past_data = valid_recurring_data.copy()
        past_data["valid_from"] = (date.today() - timedelta(days=1)).isoformat()
        with pytest.raises(ValidationError) as exc_info:
            RecurringAvailabilityCreateSchema(**past_data)
        assert "valid_from cannot be in the past" in str(exc_info.value)


class TestAvailabilitySlotSchemas:
    """Test cases for availability slot schemas."""

    @pytest.fixture
    def valid_slot_data(self):
        """Valid availability slot data."""
        return {
            "availability_date": (date.today() + timedelta(days=1)).isoformat(),
            "start_time": "10:00:00",
            "end_time": "11:00:00",
            "max_bookings": 2
        }

    def test_availability_slot_create_schema_valid(self, valid_slot_data):
        """Test valid availability slot creation schema."""
        schema = AvailabilitySlotCreateSchema(**valid_slot_data)

        assert schema.availability_date == date.today() + timedelta(days=1)
        assert schema.start_time == time(10, 0)
        assert schema.end_time == time(11, 0)
        assert schema.max_bookings == 2

    def test_slot_future_date_validation(self, valid_slot_data):
        """Test future date validation."""
        # Valid future date
        schema = AvailabilitySlotCreateSchema(**valid_slot_data)
        assert schema.availability_date > date.today()

        # Invalid past date
        valid_slot_data["availability_date"] = (date.today() - timedelta(days=1)).isoformat()
        with pytest.raises(ValidationError) as exc_info:
            AvailabilitySlotCreateSchema(**valid_slot_data)
        assert "Availability slots cannot be created for past dates" in str(exc_info.value)

    def test_slot_time_validation(self, valid_slot_data):
        """Test slot time validation."""
        # Valid time range
        schema = AvailabilitySlotCreateSchema(**valid_slot_data)
        assert schema.start_time < schema.end_time

        # Invalid time range
        valid_slot_data["start_time"] = "11:00:00"
        valid_slot_data["end_time"] = "10:00:00"

        with pytest.raises(ValidationError) as exc_info:
            AvailabilitySlotCreateSchema(**valid_slot_data)
        assert "start_time must be before end_time" in str(exc_info.value)

    def test_slot_duration_validation(self, valid_slot_data):
        """Test slot duration validation."""
        # Valid duration (1 hour)
        schema = AvailabilitySlotCreateSchema(**valid_slot_data)
        start_datetime = datetime.combine(schema.availability_date, schema.start_time)
        end_datetime = datetime.combine(schema.availability_date, schema.end_time)
        duration = (end_datetime - start_datetime).total_seconds() / 60
        assert 15 <= duration <= 480  # 15 minutes to 8 hours

        # Invalid duration (too short)
        short_data = valid_slot_data.copy()
        short_data["start_time"] = "10:00:00"
        short_data["end_time"] = "10:10:00"  # 10 minutes

        with pytest.raises(ValidationError) as exc_info:
            AvailabilitySlotCreateSchema(**short_data)
        assert "Slot duration must be at least 15 minutes" in str(exc_info.value)

    def test_slot_capacity_validation(self, valid_slot_data):
        """Test slot capacity validation."""
        # Valid capacity
        schema = AvailabilitySlotCreateSchema(**valid_slot_data)
        assert schema.max_bookings > 0

        # Invalid capacity (zero)
        valid_slot_data["max_bookings"] = 0
        with pytest.raises(ValidationError):
            AvailabilitySlotCreateSchema(**valid_slot_data)

        # Invalid capacity (negative)
        valid_slot_data["max_bookings"] = -1
        with pytest.raises(ValidationError):
            AvailabilitySlotCreateSchema(**valid_slot_data)


class TestAvailabilityExceptionSchemas:
    """Test cases for availability exception schemas."""

    @pytest.fixture
    def valid_exception_data(self):
        """Valid availability exception data."""
        return {
            "exception_date": (date.today() + timedelta(days=7)).isoformat(),
            "exception_type": "unavailable",
            "reason": "Vendor holiday"
        }

    def test_availability_exception_create_schema_valid(self, valid_exception_data):
        """Test valid availability exception creation schema."""
        schema = AvailabilityExceptionCreateSchema(**valid_exception_data)

        assert schema.exception_date == date.today() + timedelta(days=7)
        assert schema.exception_type == "unavailable"
        assert schema.reason == "Vendor holiday"

    def test_exception_type_validation(self, valid_exception_data):
        """Test exception type validation."""
        # Valid unavailable exception
        schema = AvailabilityExceptionCreateSchema(**valid_exception_data)
        assert schema.exception_type == "unavailable"

        # Valid modified exception (requires modified times)
        modified_data = valid_exception_data.copy()
        modified_data["exception_type"] = "modified"
        modified_data["modified_start_time"] = "11:00:00"
        modified_data["modified_end_time"] = "15:00:00"
        schema = AvailabilityExceptionCreateSchema(**modified_data)
        assert schema.exception_type == "modified"

        # Invalid exception type
        invalid_data = valid_exception_data.copy()
        invalid_data["exception_type"] = "invalid"
        with pytest.raises(ValidationError):
            AvailabilityExceptionCreateSchema(**invalid_data)

    def test_modified_exception_validation(self, valid_exception_data):
        """Test modified exception validation."""
        valid_exception_data["exception_type"] = "modified"
        valid_exception_data["modified_start_time"] = "11:00:00"
        valid_exception_data["modified_end_time"] = "15:00:00"

        schema = AvailabilityExceptionCreateSchema(**valid_exception_data)
        assert schema.exception_type == "modified"
        assert schema.modified_start_time == time(11, 0)
        assert schema.modified_end_time == time(15, 0)

    def test_exception_future_date_validation(self, valid_exception_data):
        """Test future date validation."""
        # Valid future date
        schema = AvailabilityExceptionCreateSchema(**valid_exception_data)
        assert schema.exception_date >= date.today()

        # Invalid past date
        past_data = valid_exception_data.copy()
        past_data["exception_date"] = (date.today() - timedelta(days=1)).isoformat()
        with pytest.raises(ValidationError) as exc_info:
            AvailabilityExceptionCreateSchema(**past_data)
        assert "Exceptions cannot be created for past dates" in str(exc_info.value)


class TestBulkOperationSchemas:
    """Test cases for bulk operation schemas."""

    def test_bulk_slot_response_schema(self):
        """Test bulk slot response schema."""
        response_data = {
            "created_slots": 50,
            "skipped_slots": 5,
            "total_requested": 55,
            "created_slot_ids": [1, 2, 3, 4, 5],
            "errors": []
        }

        schema = BulkSlotResponseSchema(**response_data)
        assert schema.created_slots == 50
        assert schema.skipped_slots == 5
        assert schema.total_requested == 55
        assert len(schema.created_slot_ids) == 5
        assert len(schema.errors) == 0

    def test_availability_check_response_schema(self):
        """Test availability check response schema."""
        response_data = {
            "available": True,
            "vendor_id": 123,
            "service_id": None,
            "start_datetime": datetime.now(),
            "end_datetime": datetime.now() + timedelta(hours=1),
            "available_slots": [],
            "conflicts": [],
            "alternative_suggestions": []
        }

        schema = AvailabilityCheckResponseSchema(**response_data)
        assert schema.available is True
        assert schema.vendor_id == 123
        assert schema.service_id is None
        assert isinstance(schema.available_slots, list)
        assert isinstance(schema.conflicts, list)
        assert isinstance(schema.alternative_suggestions, list)
