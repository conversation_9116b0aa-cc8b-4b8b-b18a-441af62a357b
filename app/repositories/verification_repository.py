"""
Document verification repository for Culture Connect Backend API.

This module provides repository layer for document verification workflows,
automated validation rules, and verification history with comprehensive
CRUD operations and audit capabilities.

Implements Task 3.1.2: Document Verification System repository layer with
workflow management, status tracking, and audit trail functionality.
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.repositories.base import BaseRepository
from app.models.verification import (
    DocumentVerificationWorkflow, VerificationHistory, AutomatedValidationRule,
    VerificationWorkflowStatus, VerificationPriority, AutomatedValidationStatus,
    VerificationActionType
)
from app.models.vendor import VendorDocument, DocumentType
from app.schemas.verification import (
    DocumentVerificationWorkflowCreate, DocumentVerificationWorkflowUpdate,
    VerificationHistoryCreate, AutomatedValidationRuleCreate,
    AutomatedValidationRuleUpdate, WorkflowListFilters
)


class DocumentVerificationWorkflowRepository(BaseRepository[DocumentVerificationWorkflow]):
    """Repository for document verification workflow operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(DocumentVerificationWorkflow, db_session)

    async def create_workflow(
        self,
        workflow_data: DocumentVerificationWorkflowCreate
    ) -> DocumentVerificationWorkflow:
        """
        Create a new document verification workflow.

        Args:
            workflow_data: Workflow creation data

        Returns:
            Created workflow instance
        """
        # Generate unique workflow reference
        workflow_reference = await self._generate_workflow_reference()

        # Calculate due date based on priority
        due_date = self._calculate_due_date(workflow_data.priority)

        workflow = DocumentVerificationWorkflow(
            vendor_id=workflow_data.vendor_id,
            document_id=workflow_data.document_id,
            workflow_reference=workflow_reference,
            priority=workflow_data.priority,
            requires_manual_review=workflow_data.requires_manual_review,
            is_expedited=workflow_data.is_expedited,
            workflow_data=workflow_data.workflow_data,
            due_date=due_date,
            submitted_at=datetime.now(timezone.utc)
        )

        return await self.create(workflow)

    async def get_workflow_by_reference(
        self,
        workflow_reference: str
    ) -> Optional[DocumentVerificationWorkflow]:
        """
        Get workflow by reference number.

        Args:
            workflow_reference: Unique workflow reference

        Returns:
            Workflow instance if found, None otherwise
        """
        return await self.get_by_field("workflow_reference", workflow_reference)

    async def get_workflows_by_vendor(
        self,
        vendor_id: int,
        status: Optional[VerificationWorkflowStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[DocumentVerificationWorkflow]:
        """
        Get workflows for a specific vendor.

        Args:
            vendor_id: Vendor ID
            status: Optional status filter
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of workflows
        """
        filters = [DocumentVerificationWorkflow.vendor_id == vendor_id]
        if status:
            filters.append(DocumentVerificationWorkflow.status == status)

        return await self.get_multi_by_filters(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by=[desc(DocumentVerificationWorkflow.submitted_at)]
        )

    async def get_workflows_by_status(
        self,
        status: VerificationWorkflowStatus,
        assigned_to: Optional[int] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[DocumentVerificationWorkflow]:
        """
        Get workflows by status with optional assignee filter.

        Args:
            status: Workflow status
            assigned_to: Optional assigned admin ID
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of workflows
        """
        filters = [DocumentVerificationWorkflow.status == status]
        if assigned_to:
            filters.append(DocumentVerificationWorkflow.assigned_to == assigned_to)

        return await self.get_multi_by_filters(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by=[asc(DocumentVerificationWorkflow.due_date)]
        )

    async def get_overdue_workflows(
        self,
        limit: int = 100,
        offset: int = 0
    ) -> List[DocumentVerificationWorkflow]:
        """
        Get overdue workflows.

        Args:
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of overdue workflows
        """
        current_time = datetime.now(timezone.utc)
        filters = [
            DocumentVerificationWorkflow.due_date < current_time,
            DocumentVerificationWorkflow.completed_at.is_(None),
            DocumentVerificationWorkflow.status.in_([
                VerificationWorkflowStatus.PENDING,
                VerificationWorkflowStatus.IN_PROGRESS,
                VerificationWorkflowStatus.UNDER_REVIEW
            ])
        ]

        return await self.get_multi_by_filters(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by=[asc(DocumentVerificationWorkflow.due_date)]
        )

    async def get_workflows_with_filters(
        self,
        filters: WorkflowListFilters,
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[List[DocumentVerificationWorkflow], int]:
        """
        Get workflows with advanced filtering.

        Args:
            filters: Filter criteria
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            Tuple of (workflows list, total count)
        """
        query_filters = []

        # Status filter
        if filters.status:
            query_filters.append(DocumentVerificationWorkflow.status.in_(filters.status))

        # Priority filter
        if filters.priority:
            query_filters.append(DocumentVerificationWorkflow.priority.in_(filters.priority))

        # Assigned admin filter
        if filters.assigned_to is not None:
            query_filters.append(DocumentVerificationWorkflow.assigned_to == filters.assigned_to)

        # Vendor filter
        if filters.vendor_id is not None:
            query_filters.append(DocumentVerificationWorkflow.vendor_id == filters.vendor_id)

        # Document type filter (requires join with VendorDocument)
        if filters.document_type:
            query_filters.append(
                DocumentVerificationWorkflow.document_id.in_(
                    self.db_session.query(VendorDocument.id).filter(
                        VendorDocument.document_type.in_(filters.document_type)
                    )
                )
            )

        # Overdue filter
        if filters.is_overdue is not None:
            current_time = datetime.now(timezone.utc)
            if filters.is_overdue:
                query_filters.extend([
                    DocumentVerificationWorkflow.due_date < current_time,
                    DocumentVerificationWorkflow.completed_at.is_(None)
                ])
            else:
                query_filters.append(
                    or_(
                        DocumentVerificationWorkflow.due_date >= current_time,
                        DocumentVerificationWorkflow.completed_at.is_not(None)
                    )
                )

        # Manual review filter
        if filters.requires_manual_review is not None:
            query_filters.append(
                DocumentVerificationWorkflow.requires_manual_review == filters.requires_manual_review
            )

        # Expedited filter
        if filters.is_expedited is not None:
            query_filters.append(
                DocumentVerificationWorkflow.is_expedited == filters.is_expedited
            )

        # Date range filters
        if filters.date_from:
            query_filters.append(DocumentVerificationWorkflow.submitted_at >= filters.date_from)
        if filters.date_to:
            query_filters.append(DocumentVerificationWorkflow.submitted_at <= filters.date_to)

        # Get workflows and total count
        workflows = await self.get_multi_by_filters(
            filters=query_filters,
            limit=limit,
            offset=offset,
            order_by=[desc(DocumentVerificationWorkflow.submitted_at)]
        )

        total_count = await self.count_by_filters(query_filters)

        return workflows, total_count

    async def assign_workflow(
        self,
        workflow_id: int,
        assigned_to: int,
        assigned_by: Optional[int] = None
    ) -> Optional[DocumentVerificationWorkflow]:
        """
        Assign workflow to an admin.

        Args:
            workflow_id: Workflow ID
            assigned_to: Admin ID to assign to
            assigned_by: Admin ID who made the assignment

        Returns:
            Updated workflow if found, None otherwise
        """
        workflow = await self.get_by_id(workflow_id)
        if not workflow:
            return None

        update_data = {
            "assigned_to": assigned_to,
            "assigned_at": datetime.now(timezone.utc),
            "status": VerificationWorkflowStatus.IN_PROGRESS
        }

        return await self.update(workflow, update_data)

    async def complete_workflow(
        self,
        workflow_id: int,
        status: VerificationWorkflowStatus,
        reviewed_by: int,
        review_notes: Optional[str] = None,
        rejection_reason: Optional[str] = None
    ) -> Optional[DocumentVerificationWorkflow]:
        """
        Complete workflow with review decision.

        Args:
            workflow_id: Workflow ID
            status: Final status
            reviewed_by: Admin who completed review
            review_notes: Review notes
            rejection_reason: Rejection reason if applicable

        Returns:
            Updated workflow if found, None otherwise
        """
        workflow = await self.get_by_id(workflow_id)
        if not workflow:
            return None

        update_data = {
            "status": status,
            "reviewed_by": reviewed_by,
            "reviewed_at": datetime.now(timezone.utc),
            "completed_at": datetime.now(timezone.utc),
            "review_notes": review_notes,
            "rejection_reason": rejection_reason
        }

        return await self.update(workflow, update_data)

    async def update_automation_results(
        self,
        workflow_id: int,
        validation_status: AutomatedValidationStatus,
        validation_score: Optional[float] = None,
        validation_results: Optional[Dict[str, Any]] = None
    ) -> Optional[DocumentVerificationWorkflow]:
        """
        Update workflow with automated validation results.

        Args:
            workflow_id: Workflow ID
            validation_status: Validation status
            validation_score: Confidence score
            validation_results: Detailed results

        Returns:
            Updated workflow if found, None otherwise
        """
        workflow = await self.get_by_id(workflow_id)
        if not workflow:
            return None

        update_data = {
            "automated_validation_status": validation_status,
            "automated_validation_score": validation_score,
            "automated_validation_results": validation_results
        }

        # If automation passed and no manual review required, auto-approve
        if (validation_status == AutomatedValidationStatus.PASSED and
            not workflow.requires_manual_review):
            update_data.update({
                "status": VerificationWorkflowStatus.APPROVED,
                "completed_at": datetime.now(timezone.utc)
            })

        return await self.update(workflow, update_data)

    async def _generate_workflow_reference(self) -> str:
        """Generate unique workflow reference number."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        count = await self.count_by_filters([
            func.date(DocumentVerificationWorkflow.created_at) == datetime.now(timezone.utc).date()
        ])
        return f"VW{timestamp}{count + 1:04d}"

    def _calculate_due_date(self, priority: VerificationPriority) -> datetime:
        """Calculate due date based on priority."""
        current_time = datetime.now(timezone.utc)

        # Priority-based SLA hours
        sla_hours = {
            VerificationPriority.URGENT: 4,
            VerificationPriority.HIGH: 24,
            VerificationPriority.NORMAL: 72,
            VerificationPriority.LOW: 168  # 1 week
        }

        hours = sla_hours.get(priority, 72)
        return current_time + timezone.timedelta(hours=hours)


class VerificationHistoryRepository(BaseRepository[VerificationHistory]):
    """Repository for verification history operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(VerificationHistory, db_session)

    async def create_history_entry(
        self,
        history_data: VerificationHistoryCreate
    ) -> VerificationHistory:
        """
        Create a new verification history entry.

        Args:
            history_data: History entry data

        Returns:
            Created history entry
        """
        history = VerificationHistory(
            workflow_id=history_data.workflow_id,
            action_type=history_data.action_type,
            performed_by=history_data.performed_by,
            action_description=history_data.action_description,
            previous_status=history_data.previous_status,
            new_status=history_data.new_status,
            action_data=history_data.action_data,
            ip_address=history_data.ip_address,
            user_agent=history_data.user_agent,
            performed_at=datetime.now(timezone.utc)
        )

        return await self.create(history)

    async def get_workflow_history(
        self,
        workflow_id: int,
        limit: int = 100,
        offset: int = 0
    ) -> List[VerificationHistory]:
        """
        Get history entries for a workflow.

        Args:
            workflow_id: Workflow ID
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of history entries
        """
        return await self.get_multi_by_filters(
            filters=[VerificationHistory.workflow_id == workflow_id],
            limit=limit,
            offset=offset,
            order_by=[desc(VerificationHistory.performed_at)]
        )

    async def get_user_actions(
        self,
        user_id: int,
        action_type: Optional[VerificationActionType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[VerificationHistory]:
        """
        Get actions performed by a user.

        Args:
            user_id: User ID
            action_type: Optional action type filter
            date_from: Optional start date
            date_to: Optional end date
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of history entries
        """
        filters = [VerificationHistory.performed_by == user_id]

        if action_type:
            filters.append(VerificationHistory.action_type == action_type)
        if date_from:
            filters.append(VerificationHistory.performed_at >= date_from)
        if date_to:
            filters.append(VerificationHistory.performed_at <= date_to)

        return await self.get_multi_by_filters(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by=[desc(VerificationHistory.performed_at)]
        )

    async def log_workflow_action(
        self,
        workflow_id: int,
        action_type: VerificationActionType,
        performed_by: Optional[int] = None,
        description: Optional[str] = None,
        previous_status: Optional[str] = None,
        new_status: Optional[str] = None,
        action_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> VerificationHistory:
        """
        Log a workflow action for audit trail.

        Args:
            workflow_id: Workflow ID
            action_type: Type of action
            performed_by: User who performed action
            description: Action description
            previous_status: Previous status
            new_status: New status
            action_data: Additional action data
            ip_address: User IP address
            user_agent: User agent string

        Returns:
            Created history entry
        """
        history_data = VerificationHistoryCreate(
            workflow_id=workflow_id,
            action_type=action_type,
            performed_by=performed_by,
            action_description=description,
            previous_status=previous_status,
            new_status=new_status,
            action_data=action_data,
            ip_address=ip_address,
            user_agent=user_agent
        )

        return await self.create_history_entry(history_data)


class AutomatedValidationRuleRepository(BaseRepository[AutomatedValidationRule]):
    """Repository for automated validation rule operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(AutomatedValidationRule, db_session)

    async def create_rule(
        self,
        rule_data: AutomatedValidationRuleCreate,
        created_by: Optional[int] = None
    ) -> AutomatedValidationRule:
        """
        Create a new validation rule.

        Args:
            rule_data: Rule creation data
            created_by: User who created the rule

        Returns:
            Created rule
        """
        rule = AutomatedValidationRule(
            rule_name=rule_data.rule_name,
            rule_description=rule_data.rule_description,
            document_types=rule_data.document_types,
            vendor_types=rule_data.vendor_types,
            validation_type=rule_data.validation_type,
            rule_config=rule_data.rule_config,
            priority=rule_data.priority,
            is_active=rule_data.is_active,
            created_by=created_by
        )

        return await self.create(rule)

    async def get_active_rules(
        self,
        document_type: Optional[DocumentType] = None,
        vendor_type: Optional[str] = None,
        validation_type: Optional[str] = None
    ) -> List[AutomatedValidationRule]:
        """
        Get active validation rules with optional filters.

        Args:
            document_type: Document type filter
            vendor_type: Vendor type filter
            validation_type: Validation type filter

        Returns:
            List of active rules
        """
        filters = [AutomatedValidationRule.is_active == True]

        if document_type:
            filters.append(
                AutomatedValidationRule.document_types.contains([document_type.value])
            )
        if vendor_type:
            filters.append(
                or_(
                    AutomatedValidationRule.vendor_types.is_(None),
                    AutomatedValidationRule.vendor_types.contains([vendor_type])
                )
            )
        if validation_type:
            filters.append(AutomatedValidationRule.validation_type == validation_type)

        return await self.get_multi_by_filters(
            filters=filters,
            order_by=[asc(AutomatedValidationRule.priority)]
        )

    async def get_rule_by_name(
        self,
        rule_name: str
    ) -> Optional[AutomatedValidationRule]:
        """
        Get rule by name.

        Args:
            rule_name: Rule name

        Returns:
            Rule if found, None otherwise
        """
        return await self.get_by_field("rule_name", rule_name)

    async def update_rule_performance(
        self,
        rule_id: int,
        execution_successful: bool
    ) -> Optional[AutomatedValidationRule]:
        """
        Update rule performance metrics.

        Args:
            rule_id: Rule ID
            execution_successful: Whether execution was successful

        Returns:
            Updated rule if found, None otherwise
        """
        rule = await self.get_by_id(rule_id)
        if not rule:
            return None

        # Update execution counts
        total_executions = rule.total_executions + 1
        successful_executions = rule.successful_executions + (1 if execution_successful else 0)
        success_rate = (successful_executions / total_executions) * 100.0

        update_data = {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "success_rate": success_rate,
            "last_executed_at": datetime.now(timezone.utc)
        }

        return await self.update(rule, update_data)

    async def get_rules_by_performance(
        self,
        min_success_rate: float = 0.0,
        min_executions: int = 10,
        limit: int = 50,
        offset: int = 0
    ) -> List[AutomatedValidationRule]:
        """
        Get rules filtered by performance metrics.

        Args:
            min_success_rate: Minimum success rate
            min_executions: Minimum number of executions
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of rules
        """
        filters = [
            AutomatedValidationRule.success_rate >= min_success_rate,
            AutomatedValidationRule.total_executions >= min_executions
        ]

        return await self.get_multi_by_filters(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by=[desc(AutomatedValidationRule.success_rate)]
        )
