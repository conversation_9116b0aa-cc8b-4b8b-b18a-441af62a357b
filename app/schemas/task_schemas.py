"""
Task management schemas for Culture Connect Backend API.

This module provides comprehensive task management schemas including:
- Task execution request/response schemas
- Queue management schemas
- Schedule configuration schemas
- Metrics and monitoring schemas
- Error handling schemas

Implements Task 6.2.1 requirements for Celery task queue setup with
production-grade validation and serialization.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any, List, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin


class TaskStatusEnum(str, Enum):
    """Task execution status enumeration."""
    PENDING = "pending"
    STARTED = "started"
    RETRY = "retry"
    SUCCESS = "success"
    FAILURE = "failure"
    REVOKED = "revoked"
    IGNORED = "ignored"


class TaskPriorityEnum(str, Enum):
    """Task priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    STANDARD = "standard"
    LOW = "low"


class QueueTypeEnum(str, Enum):
    """Queue type classification."""
    EMAIL = "email"
    NOTIFICATION = "notification"
    REPORT = "report"
    SYNC = "sync"
    CLEANUP = "cleanup"
    ANALYTICS = "analytics"
    PAYMENT = "payment"
    GENERAL = "general"


# Task Execution Schemas

class TaskSubmissionRequest(BaseSchema):
    """Schema for task submission requests."""
    
    task_name: str = Field(..., description="Name of the task to execute", max_length=255)
    args: Optional[List[Any]] = Field(None, description="Task arguments")
    kwargs: Optional[Dict[str, Any]] = Field(None, description="Task keyword arguments")
    priority: TaskPriorityEnum = Field(TaskPriorityEnum.STANDARD, description="Task priority level")
    queue_name: Optional[str] = Field(None, description="Target queue name", max_length=100)
    eta: Optional[datetime] = Field(None, description="Estimated time of arrival")
    countdown: Optional[int] = Field(None, description="Countdown in seconds", ge=0)
    max_retries: Optional[int] = Field(3, description="Maximum retry attempts", ge=0, le=10)
    
    @field_validator('task_name')
    @classmethod
    def validate_task_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Task name cannot be empty')
        return v.strip()
    
    @field_validator('countdown')
    @classmethod
    def validate_countdown_with_eta(cls, v, info):
        if v is not None and info.data and info.data.get('eta') is not None:
            raise ValueError('Cannot specify both countdown and eta')
        return v


class TaskSubmissionResponse(BaseSchema, UUIDMixin, TimestampMixin):
    """Schema for task submission responses."""
    
    task_id: str = Field(..., description="Unique task identifier")
    task_name: str = Field(..., description="Name of the submitted task")
    queue_name: str = Field(..., description="Queue where task was submitted")
    priority: TaskPriorityEnum = Field(..., description="Task priority level")
    status: TaskStatusEnum = Field(..., description="Current task status")
    eta: Optional[datetime] = Field(None, description="Estimated time of arrival")
    submitted_at: datetime = Field(..., description="Task submission timestamp")


class TaskStatusRequest(BaseSchema):
    """Schema for task status requests."""
    
    task_id: str = Field(..., description="Task identifier to check")
    include_result: bool = Field(False, description="Include task result in response")
    include_traceback: bool = Field(False, description="Include error traceback if failed")


class TaskStatusResponse(BaseSchema):
    """Schema for task status responses."""
    
    task_id: str = Field(..., description="Task identifier")
    task_name: str = Field(..., description="Task name")
    status: TaskStatusEnum = Field(..., description="Current task status")
    progress: Optional[float] = Field(None, description="Task progress percentage", ge=0, le=100)
    
    # Timing information
    submitted_at: datetime = Field(..., description="Task submission timestamp")
    started_at: Optional[datetime] = Field(None, description="Task start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    
    # Execution details
    result: Optional[Any] = Field(None, description="Task result if completed")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_traceback: Optional[str] = Field(None, description="Error traceback if failed")
    
    # Performance metrics
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")
    retry_count: int = Field(0, description="Number of retry attempts")
    worker_name: Optional[str] = Field(None, description="Worker that processed the task")


class TaskCancellationRequest(BaseSchema):
    """Schema for task cancellation requests."""
    
    task_id: str = Field(..., description="Task identifier to cancel")
    terminate: bool = Field(False, description="Forcefully terminate if running")
    reason: Optional[str] = Field(None, description="Cancellation reason", max_length=500)


class TaskCancellationResponse(BaseSchema):
    """Schema for task cancellation responses."""
    
    task_id: str = Field(..., description="Cancelled task identifier")
    status: str = Field(..., description="Cancellation status")
    message: str = Field(..., description="Cancellation result message")
    cancelled_at: datetime = Field(..., description="Cancellation timestamp")


# Queue Management Schemas

class QueueConfigurationRequest(BaseSchema):
    """Schema for queue configuration requests."""
    
    name: str = Field(..., description="Queue name", max_length=100)
    queue_type: QueueTypeEnum = Field(QueueTypeEnum.GENERAL, description="Queue type")
    max_workers: int = Field(4, description="Maximum number of workers", ge=1, le=50)
    prefetch_multiplier: int = Field(1, description="Prefetch multiplier", ge=1, le=10)
    max_tasks_per_child: int = Field(1000, description="Max tasks per worker child", ge=100, le=10000)
    task_time_limit: int = Field(1800, description="Task time limit in seconds", ge=60, le=7200)
    task_soft_time_limit: int = Field(1500, description="Task soft time limit in seconds", ge=30, le=6000)
    is_active: bool = Field(True, description="Whether queue is active")
    routing_key: Optional[str] = Field(None, description="Routing key", max_length=255)
    
    @field_validator('task_soft_time_limit')
    @classmethod
    def validate_soft_time_limit(cls, v, info):
        time_limit = info.data.get('task_time_limit', 1800) if info.data else 1800
        if v >= time_limit:
            raise ValueError('Soft time limit must be less than time limit')
        return v


class QueueStatusResponse(BaseSchema, UUIDMixin, TimestampMixin):
    """Schema for queue status responses."""
    
    name: str = Field(..., description="Queue name")
    queue_type: QueueTypeEnum = Field(..., description="Queue type")
    is_active: bool = Field(..., description="Whether queue is active")
    is_paused: bool = Field(..., description="Whether queue is paused")
    
    # Configuration
    max_workers: int = Field(..., description="Maximum number of workers")
    prefetch_multiplier: int = Field(..., description="Prefetch multiplier")
    task_time_limit: int = Field(..., description="Task time limit in seconds")
    
    # Statistics
    total_tasks_processed: int = Field(..., description="Total tasks processed")
    total_failures: int = Field(..., description="Total task failures")
    average_execution_time_ms: Optional[float] = Field(None, description="Average execution time")
    
    # Current status
    active_tasks: int = Field(0, description="Currently active tasks")
    scheduled_tasks: int = Field(0, description="Scheduled tasks")
    reserved_tasks: int = Field(0, description="Reserved tasks")


# Task Scheduling Schemas

class TaskScheduleRequest(BaseSchema):
    """Schema for task schedule creation requests."""
    
    name: str = Field(..., description="Schedule name", max_length=255)
    task_name: str = Field(..., description="Task to schedule", max_length=255)
    cron_expression: Optional[str] = Field(None, description="Cron expression", max_length=100)
    interval_seconds: Optional[int] = Field(None, description="Interval in seconds", ge=60)
    queue_name: str = Field("default", description="Target queue", max_length=100)
    priority: TaskPriorityEnum = Field(TaskPriorityEnum.STANDARD, description="Task priority")
    args: Optional[List[Any]] = Field(None, description="Task arguments")
    kwargs: Optional[Dict[str, Any]] = Field(None, description="Task keyword arguments")
    is_enabled: bool = Field(True, description="Whether schedule is enabled")
    
    @field_validator('cron_expression')
    @classmethod
    def validate_cron_with_interval(cls, v, info):
        interval = info.data.get('interval_seconds') if info.data else None
        if v is not None and interval is not None:
            raise ValueError('Cannot specify both cron_expression and interval_seconds')
        if v is None and interval is None:
            raise ValueError('Must specify either cron_expression or interval_seconds')
        return v


class TaskScheduleResponse(BaseSchema, UUIDMixin, TimestampMixin):
    """Schema for task schedule responses."""
    
    name: str = Field(..., description="Schedule name")
    task_name: str = Field(..., description="Scheduled task name")
    cron_expression: Optional[str] = Field(None, description="Cron expression")
    interval_seconds: Optional[int] = Field(None, description="Interval in seconds")
    queue_name: str = Field(..., description="Target queue")
    priority: TaskPriorityEnum = Field(..., description="Task priority")
    is_enabled: bool = Field(..., description="Whether schedule is enabled")
    
    # Execution tracking
    last_run_at: Optional[datetime] = Field(None, description="Last execution time")
    next_run_at: Optional[datetime] = Field(None, description="Next execution time")
    total_runs: int = Field(..., description="Total executions")
    successful_runs: int = Field(..., description="Successful executions")
    failed_runs: int = Field(..., description="Failed executions")


# Metrics and Monitoring Schemas

class TaskMetricsResponse(BaseSchema):
    """Schema for task metrics responses."""
    
    task_execution_id: UUID = Field(..., description="Task execution identifier")
    queue_wait_time_ms: Optional[float] = Field(None, description="Queue wait time in milliseconds")
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")
    memory_peak_mb: Optional[float] = Field(None, description="Peak memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU usage percentage")
    
    # I/O metrics
    disk_read_mb: Optional[float] = Field(None, description="Disk read in MB")
    disk_write_mb: Optional[float] = Field(None, description="Disk write in MB")
    
    # Database metrics
    db_queries_count: Optional[int] = Field(None, description="Number of database queries")
    db_query_time_ms: Optional[float] = Field(None, description="Database query time in milliseconds")
    
    # Cache metrics
    cache_hits: Optional[int] = Field(None, description="Cache hits")
    cache_misses: Optional[int] = Field(None, description="Cache misses")
    
    recorded_at: datetime = Field(..., description="Metrics recording timestamp")


class QueueMetricsResponse(BaseSchema):
    """Schema for queue metrics responses."""
    
    queue_name: str = Field(..., description="Queue name")
    active_tasks: int = Field(..., description="Currently active tasks")
    scheduled_tasks: int = Field(..., description="Scheduled tasks")
    reserved_tasks: int = Field(..., description="Reserved tasks")
    
    # Performance metrics
    throughput_per_minute: float = Field(..., description="Tasks processed per minute")
    average_wait_time_ms: float = Field(..., description="Average queue wait time")
    average_execution_time_ms: float = Field(..., description="Average execution time")
    
    # Error rates
    success_rate_percent: float = Field(..., description="Success rate percentage")
    failure_rate_percent: float = Field(..., description="Failure rate percentage")
    retry_rate_percent: float = Field(..., description="Retry rate percentage")
    
    # Resource utilization
    worker_utilization_percent: float = Field(..., description="Worker utilization percentage")
    memory_usage_mb: float = Field(..., description="Memory usage in MB")
    
    recorded_at: datetime = Field(..., description="Metrics recording timestamp")


class SystemMetricsResponse(BaseSchema):
    """Schema for system-wide task metrics responses."""
    
    total_queues: int = Field(..., description="Total number of queues")
    active_queues: int = Field(..., description="Number of active queues")
    total_workers: int = Field(..., description="Total number of workers")
    active_workers: int = Field(..., description="Number of active workers")
    
    # Task statistics
    total_tasks_today: int = Field(..., description="Total tasks processed today")
    successful_tasks_today: int = Field(..., description="Successful tasks today")
    failed_tasks_today: int = Field(..., description="Failed tasks today")
    
    # Performance metrics
    overall_success_rate: float = Field(..., description="Overall success rate percentage")
    average_execution_time_ms: float = Field(..., description="Average execution time")
    peak_throughput_per_minute: float = Field(..., description="Peak throughput per minute")
    
    # Resource utilization
    total_memory_usage_mb: float = Field(..., description="Total memory usage in MB")
    cpu_utilization_percent: float = Field(..., description="CPU utilization percentage")
    
    # Health indicators
    health_score: float = Field(..., description="Overall system health score", ge=0, le=100)
    uptime_hours: float = Field(..., description="System uptime in hours")
    
    recorded_at: datetime = Field(..., description="Metrics recording timestamp")


# Error and Failure Schemas

class TaskFailureResponse(BaseSchema, UUIDMixin):
    """Schema for task failure responses."""
    
    task_execution_id: UUID = Field(..., description="Task execution identifier")
    exception_type: str = Field(..., description="Exception type")
    exception_message: str = Field(..., description="Exception message")
    retry_attempt: int = Field(..., description="Retry attempt number")
    is_retryable: bool = Field(..., description="Whether failure is retryable")
    failure_category: Optional[str] = Field(None, description="Failure category")
    failed_at: datetime = Field(..., description="Failure timestamp")
    worker_name: Optional[str] = Field(None, description="Worker that failed")


# Bulk Operations Schemas

class BulkTaskSubmissionRequest(BaseSchema):
    """Schema for bulk task submission requests."""
    
    tasks: List[TaskSubmissionRequest] = Field(..., description="List of tasks to submit", min_items=1, max_items=100)
    batch_name: Optional[str] = Field(None, description="Batch name for tracking", max_length=255)
    fail_fast: bool = Field(False, description="Stop on first failure")


class BulkTaskSubmissionResponse(BaseSchema):
    """Schema for bulk task submission responses."""
    
    batch_id: UUID = Field(..., description="Batch identifier")
    total_tasks: int = Field(..., description="Total number of tasks")
    successful_submissions: int = Field(..., description="Successfully submitted tasks")
    failed_submissions: int = Field(..., description="Failed submissions")
    task_ids: List[str] = Field(..., description="List of submitted task IDs")
    errors: List[Dict[str, Any]] = Field(..., description="Submission errors")
    submitted_at: datetime = Field(..., description="Batch submission timestamp")
