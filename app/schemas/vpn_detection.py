"""
VPN and Proxy Detection Schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic schemas for VPN, proxy, and anonymization detection including:
- Request/response schemas with validation
- Detection result models with confidence scoring
- Analytics and reporting schemas
- Integration with existing payment routing schemas

Implements Phase 2.1 VPN Detection Schemas foundation following Pydantic V2 patterns
with comprehensive validation rules and API contract definitions.
"""

from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
import ipaddress

from app.models.vpn_detection import (
    VPNDetectionMethod,
    ProxyType,
    AnonymizationService,
    DetectionConfidence
)


class VPNDetectionRequest(BaseModel):
    """Request schema for VPN detection."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(
        description="IP address to analyze for VPN usage",
        example="*******",
        min_length=7,
        max_length=45
    )
    user_id: Optional[str] = Field(
        description="Optional user ID for tracking and analytics",
        example="123e4567-e89b-12d3-a456-************",
        default=None
    )
    include_geolocation: bool = Field(
        description="Include geolocation analysis in detection",
        default=True
    )
    include_risk_assessment: bool = Field(
        description="Include risk assessment in detection results",
        default=True
    )
    detection_timeout_ms: Optional[int] = Field(
        description="Detection timeout in milliseconds",
        ge=100,
        le=10000,
        default=5000
    )

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v):
        """Validate IP address format."""
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address format')


class ProxyDetectionRequest(BaseModel):
    """Request schema for proxy detection."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(
        description="IP address to analyze for proxy usage",
        example="***********"
    )
    check_anonymity_level: bool = Field(
        description="Check proxy anonymity level",
        default=True
    )
    check_performance: bool = Field(
        description="Check proxy performance characteristics",
        default=False
    )
    user_agent: Optional[str] = Field(
        description="User agent string for enhanced detection",
        default=None
    )

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v):
        """Validate IP address format."""
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address format')


class AnonymizationDetectionRequest(BaseModel):
    """Request schema for anonymization service detection."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(
        description="IP address to analyze for anonymization services",
        example="********"
    )
    deep_analysis: bool = Field(
        description="Perform deep analysis including Tor detection",
        default=False
    )
    include_threat_assessment: bool = Field(
        description="Include threat and malicious activity assessment",
        default=True
    )

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v):
        """Validate IP address format."""
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address format')


# Create/Update schemas for repository operations
class VPNDetectionResultCreate(BaseModel):
    """Schema for creating VPN detection results."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(description="IP address analyzed")
    is_vpn_detected: bool = Field(description="Whether VPN was detected")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Detection confidence score")
    confidence_level: DetectionConfidence = Field(description="Confidence level")
    detection_method: VPNDetectionMethod = Field(description="Detection method used")
    user_id: Optional[str] = Field(default=None, description="Optional user ID")
    additional_metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class VPNDetectionResultUpdate(BaseModel):
    """Schema for updating VPN detection results."""
    model_config = ConfigDict(from_attributes=True)

    confidence_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    confidence_level: Optional[DetectionConfidence] = Field(default=None)
    additional_metadata: Optional[Dict[str, Any]] = Field(default=None)


class ProxyDetectionResultCreate(BaseModel):
    """Schema for creating proxy detection results."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(description="IP address analyzed")
    is_proxy_detected: bool = Field(description="Whether proxy was detected")
    proxy_type: ProxyType = Field(description="Type of proxy detected")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Detection confidence score")
    detection_method: str = Field(description="Detection method used")
    user_id: Optional[str] = Field(default=None, description="Optional user ID")
    additional_metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class ProxyDetectionResultUpdate(BaseModel):
    """Schema for updating proxy detection results."""
    model_config = ConfigDict(from_attributes=True)

    confidence_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    additional_metadata: Optional[Dict[str, Any]] = Field(default=None)


class AnonymizationDetectionResultCreate(BaseModel):
    """Schema for creating anonymization detection results."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(description="IP address analyzed")
    is_anonymized: bool = Field(description="Whether anonymization was detected")
    anonymization_service: str = Field(description="Anonymization service detected")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Detection confidence score")
    user_id: Optional[str] = Field(default=None, description="Optional user ID")


class AnonymizationDetectionResultUpdate(BaseModel):
    """Schema for updating anonymization detection results."""
    model_config = ConfigDict(from_attributes=True)

    confidence_score: Optional[float] = Field(default=None, ge=0.0, le=1.0)


class VPNDetectionResultResponse(BaseModel):
    """Schema for VPN detection result responses."""
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(description="Detection result ID")
    ip_address: str = Field(description="IP address analyzed")
    is_vpn_detected: bool = Field(description="Whether VPN was detected")
    confidence_score: float = Field(description="Detection confidence score")
    detection_method: VPNDetectionMethod = Field(description="Detection method used")
    detection_timestamp: datetime = Field(description="When detection was performed")


class ProxyDetectionResultResponse(BaseModel):
    """Schema for proxy detection result responses."""
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(description="Detection result ID")
    ip_address: str = Field(description="IP address analyzed")
    is_proxy_detected: bool = Field(description="Whether proxy was detected")
    proxy_type: Optional[ProxyType] = Field(description="Type of proxy detected")
    confidence_score: float = Field(description="Detection confidence score")
    detection_timestamp: datetime = Field(description="When detection was performed")


class VPNDetectionAnalyticsResponse(BaseModel):
    """Schema for VPN detection analytics responses."""
    model_config = ConfigDict(from_attributes=True)

    analysis_date: datetime = Field(description="Date of analysis")
    total_detections: int = Field(description="Total detections")
    vpn_detections: int = Field(description="VPN detections")
    proxy_detections: int = Field(description="Proxy detections")
    avg_detection_time_ms: float = Field(description="Average detection time")


class VPNDetectionStatsResponse(BaseModel):
    """Schema for VPN detection statistics responses."""
    model_config = ConfigDict(from_attributes=True)

    total_detections: int = Field(description="Total detections")
    vpn_detections: int = Field(description="VPN detections")
    vpn_detection_rate: float = Field(description="VPN detection rate percentage")
    average_confidence_score: float = Field(description="Average confidence score")
    average_detection_time_ms: float = Field(description="Average detection time")
    method_breakdown: Optional[Dict[str, Any]] = Field(default=None, description="Method breakdown")
    proxy_type_distribution: Optional[Dict[str, int]] = Field(default=None, description="Proxy type distribution")
    analysis_period_days: int = Field(description="Analysis period in days")


class VPNDetectionResponse(BaseModel):
    """Response schema for VPN detection results."""
    model_config = ConfigDict(from_attributes=True)

    # Detection results
    ip_address: str = Field(
        description="Analyzed IP address",
        example="*******"
    )
    is_vpn_detected: bool = Field(
        description="Whether VPN usage was detected",
        example=True
    )
    confidence_score: float = Field(
        description="Detection confidence score (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.95
    )
    confidence_level: DetectionConfidence = Field(
        description="Human-readable confidence level",
        example="very_high"
    )
    detection_method: VPNDetectionMethod = Field(
        description="Primary detection method used",
        example="provider_database"
    )

    # VPN service details
    vpn_provider: Optional[str] = Field(
        description="Detected VPN service provider",
        example="ExpressVPN",
        default=None
    )
    vpn_service_type: Optional[str] = Field(
        description="Type of VPN service",
        example="commercial",
        default=None
    )
    vpn_exit_country: Optional[str] = Field(
        description="VPN exit node country code",
        example="US",
        default=None
    )

    # Risk assessment
    risk_score: float = Field(
        description="Risk score based on VPN usage (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.3
    )
    risk_factors: Optional[List[str]] = Field(
        description="List of identified risk factors",
        example=["commercial_vpn", "country_mismatch"],
        default=None
    )

    # Geolocation correlation
    reported_country: Optional[str] = Field(
        description="Country from standard geolocation",
        example="GB",
        default=None
    )
    actual_country: Optional[str] = Field(
        description="Actual country based on VPN analysis",
        example="US",
        default=None
    )
    country_mismatch: bool = Field(
        description="Whether there's a country mismatch",
        example=True
    )

    # Performance metadata
    detection_timestamp: datetime = Field(
        description="When the detection was performed",
        example="2024-01-15T10:30:00Z"
    )
    detection_duration_ms: Optional[float] = Field(
        description="Detection duration in milliseconds",
        example=150.5,
        default=None
    )
    cache_hit: bool = Field(
        description="Whether result was served from cache",
        example=False
    )


class ProxyDetectionResponse(BaseModel):
    """Response schema for proxy detection results."""
    model_config = ConfigDict(from_attributes=True)

    # Detection results
    ip_address: str = Field(
        description="Analyzed IP address",
        example="***********"
    )
    is_proxy_detected: bool = Field(
        description="Whether proxy usage was detected",
        example=True
    )
    proxy_type: Optional[ProxyType] = Field(
        description="Type of proxy detected",
        example="http_proxy",
        default=None
    )
    confidence_score: float = Field(
        description="Detection confidence score (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.85
    )

    # Proxy characteristics
    anonymity_level: Optional[str] = Field(
        description="Proxy anonymity level",
        example="anonymous",
        default=None
    )
    proxy_provider: Optional[str] = Field(
        description="Proxy service provider",
        example="ProxyMesh",
        default=None
    )

    # Geographic information
    proxy_country: Optional[str] = Field(
        description="Proxy server country",
        example="NL",
        default=None
    )
    proxy_city: Optional[str] = Field(
        description="Proxy server city",
        example="Amsterdam",
        default=None
    )

    # Performance characteristics
    response_time_ms: Optional[float] = Field(
        description="Proxy response time in milliseconds",
        example=250.0,
        default=None
    )

    # Risk assessment
    risk_score: float = Field(
        description="Risk score based on proxy usage (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.4
    )
    malicious_activity: bool = Field(
        description="Whether malicious activity was detected",
        example=False
    )

    # Metadata
    detection_timestamp: datetime = Field(
        description="When the detection was performed",
        example="2024-01-15T10:30:00Z"
    )


class AnonymizationDetectionResponse(BaseModel):
    """Response schema for anonymization service detection results."""
    model_config = ConfigDict(from_attributes=True)

    # Detection results
    ip_address: str = Field(
        description="Analyzed IP address",
        example="********"
    )
    is_anonymized: bool = Field(
        description="Whether anonymization service was detected",
        example=True
    )
    anonymization_service: Optional[AnonymizationService] = Field(
        description="Type of anonymization service detected",
        example="tor_network",
        default=None
    )
    confidence_score: float = Field(
        description="Detection confidence score (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.92
    )

    # Service details
    service_provider: Optional[str] = Field(
        description="Anonymization service provider",
        example="Tor Project",
        default=None
    )
    privacy_level: Optional[str] = Field(
        description="Privacy level provided by the service",
        example="high",
        default=None
    )

    # Risk assessment
    risk_score: float = Field(
        description="Risk score based on anonymization usage (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.7
    )
    threat_indicators: Optional[List[str]] = Field(
        description="List of threat indicators",
        example=["tor_exit_node", "high_anonymity"],
        default=None
    )

    # Metadata
    detection_timestamp: datetime = Field(
        description="When the detection was performed",
        example="2024-01-15T10:30:00Z"
    )


class CompositeDetectionResponse(BaseModel):
    """Composite response schema for all detection types."""
    model_config = ConfigDict(from_attributes=True)

    ip_address: str = Field(
        description="Analyzed IP address",
        example="*******"
    )

    # Overall assessment
    is_suspicious: bool = Field(
        description="Whether any suspicious activity was detected",
        example=True
    )
    overall_risk_score: float = Field(
        description="Overall risk score (0.0 - 1.0)",
        ge=0.0,
        le=1.0,
        example=0.6
    )

    # Individual detection results
    vpn_detection: Optional[VPNDetectionResponse] = Field(
        description="VPN detection results",
        default=None
    )
    proxy_detection: Optional[ProxyDetectionResponse] = Field(
        description="Proxy detection results",
        default=None
    )
    anonymization_detection: Optional[AnonymizationDetectionResponse] = Field(
        description="Anonymization detection results",
        default=None
    )

    # Payment routing recommendation
    payment_routing_recommendation: str = Field(
        description="Recommended payment routing action",
        example="enhanced_verification"
    )

    # Metadata
    detection_timestamp: datetime = Field(
        description="When the detection was performed",
        example="2024-01-15T10:30:00Z"
    )
    total_detection_time_ms: float = Field(
        description="Total detection time in milliseconds",
        example=300.5
    )


class DetectionAnalyticsResponse(BaseModel):
    """Response schema for detection analytics."""
    model_config = ConfigDict(from_attributes=True)

    # Time period
    period_start: datetime = Field(
        description="Analytics period start time",
        example="2024-01-15T00:00:00Z"
    )
    period_end: datetime = Field(
        description="Analytics period end time",
        example="2024-01-15T23:59:59Z"
    )
    period_type: str = Field(
        description="Analytics period type",
        example="daily"
    )

    # Detection metrics
    total_detections: int = Field(
        description="Total number of detections",
        example=1500
    )
    vpn_detections: int = Field(
        description="Number of VPN detections",
        example=450
    )
    proxy_detections: int = Field(
        description="Number of proxy detections",
        example=200
    )
    anonymization_detections: int = Field(
        description="Number of anonymization detections",
        example=50
    )

    # Performance metrics
    avg_detection_time_ms: float = Field(
        description="Average detection time in milliseconds",
        example=125.5
    )
    cache_hit_rate: float = Field(
        description="Cache hit rate percentage",
        example=0.85
    )

    # Geographic distribution
    top_countries: Dict[str, int] = Field(
        description="Top countries by detection count",
        example={"US": 500, "GB": 300, "DE": 200}
    )

    # Risk metrics
    avg_risk_score: float = Field(
        description="Average risk score",
        example=0.35
    )
    high_risk_detections: int = Field(
        description="Number of high-risk detections",
        example=75
    )
