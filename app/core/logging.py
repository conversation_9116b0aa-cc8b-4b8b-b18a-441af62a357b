"""
Comprehensive logging infrastructure for Culture Connect Backend API.

This module provides structured logging configuration with:
- Log rotation and retention management
- Request/response logging with correlation IDs
- Security-focused logging for authentication and permissions
- Environment-specific logging configuration
- Performance monitoring integration
"""

import logging
import logging.handlers
import os
import sys
import uuid
import time
import json
from datetime import datetime
from typing import Dict, Any, Optional
from contextvars import ContextV<PERSON>
from pathlib import Path

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings


# Context variable for correlation ID tracking
correlation_id: ContextVar[str] = ContextVar('correlation_id', default='')


class CorrelationIdFilter(logging.Filter):
    """Logging filter to add correlation ID to log records."""

    def filter(self, record):
        """Add correlation ID to log record."""
        record.correlation_id = correlation_id.get('')
        return True


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def format(self, record):
        """Format log record as JSON."""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'correlation_id': getattr(record, 'correlation_id', ''),
            'environment': settings.ENVIRONMENT,
        }

        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'correlation_id']:
                log_entry[key] = value

        return json.dumps(log_entry)


class SecurityLogger:
    """Specialized logger for security events."""

    def __init__(self):
        self.logger = logging.getLogger('security')

    def log_authentication_attempt(self, email: str, success: bool, ip_address: str, user_agent: str):
        """Log authentication attempt."""
        self.logger.info(
            "Authentication attempt",
            extra={
                'event_type': 'authentication',
                'email': email,
                'success': success,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_permission_check(self, user_id: int, permission: str, resource: str, granted: bool):
        """Log permission check."""
        self.logger.info(
            "Permission check",
            extra={
                'event_type': 'permission_check',
                'user_id': user_id,
                'permission': permission,
                'resource': resource,
                'granted': granted,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_suspicious_activity(self, user_id: Optional[int], activity: str, details: Dict[str, Any]):
        """Log suspicious activity."""
        self.logger.warning(
            "Suspicious activity detected",
            extra={
                'event_type': 'suspicious_activity',
                'user_id': user_id,
                'activity': activity,
                'details': details,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_data_access(self, user_id: int, resource_type: str, resource_id: str, action: str):
        """Log data access for audit trail."""
        self.logger.info(
            "Data access",
            extra={
                'event_type': 'data_access',
                'user_id': user_id,
                'resource_type': resource_type,
                'resource_id': resource_id,
                'action': action,
                'timestamp': datetime.utcnow().isoformat()
            }
        )


class PerformanceLogger:
    """Specialized logger for performance metrics."""

    def __init__(self):
        self.logger = logging.getLogger('performance')

    def log_request_metrics(self, method: str, path: str, status_code: int,
                          duration: float, user_id: Optional[int] = None):
        """Log request performance metrics."""
        self.logger.info(
            "Request metrics",
            extra={
                'event_type': 'request_metrics',
                'method': method,
                'path': path,
                'status_code': status_code,
                'duration': duration,
                'user_id': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_database_query(self, query_type: str, table: str, duration: float, rows_affected: int = 0):
        """Log database query performance."""
        self.logger.info(
            "Database query metrics",
            extra={
                'event_type': 'database_query',
                'query_type': query_type,
                'table': table,
                'duration': duration,
                'rows_affected': rows_affected,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_external_api_call(self, service: str, endpoint: str, duration: float,
                            status_code: int, success: bool):
        """Log external API call performance."""
        self.logger.info(
            "External API call metrics",
            extra={
                'event_type': 'external_api_call',
                'service': service,
                'endpoint': endpoint,
                'duration': duration,
                'status_code': status_code,
                'success': success,
                'timestamp': datetime.utcnow().isoformat()
            }
        )


class MetricsCollector:
    """Metrics collector for application performance monitoring."""

    def __init__(self):
        self.logger = logging.getLogger('metrics')

    def record_request(self, method: str, path: str, status_code: int, response_time: float):
        """Record request metrics."""
        self.logger.info(
            "Request metrics",
            extra={
                'event_type': 'request_metrics',
                'method': method,
                'path': path,
                'status_code': status_code,
                'response_time': response_time,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def record_database_query(self, query_type: str, duration: float, table: str = None):
        """Record database query metrics."""
        self.logger.info(
            "Database query metrics",
            extra={
                'event_type': 'database_metrics',
                'query_type': query_type,
                'duration': duration,
                'table': table,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def record_email_sent(self, recipient_count: int, template_id: str = None, success: bool = True):
        """Record email sending metrics."""
        self.logger.info(
            "Email metrics",
            extra={
                'event_type': 'email_metrics',
                'recipient_count': recipient_count,
                'template_id': template_id,
                'success': success,
                'timestamp': datetime.utcnow().isoformat()
            }
        )


class BusinessLogger:
    """Specialized logger for business events."""

    def __init__(self):
        self.logger = logging.getLogger('business')

    def log_vendor_registration(self, vendor_id: int, user_id: int, business_type: str):
        """Log vendor registration event."""
        self.logger.info(
            "Vendor registration",
            extra={
                'event_type': 'vendor_registration',
                'vendor_id': vendor_id,
                'user_id': user_id,
                'business_type': business_type,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_payment_transaction(self, transaction_id: str, amount: float, currency: str,
                              payment_method: str, status: str, user_id: int):
        """Log payment transaction."""
        self.logger.info(
            "Payment transaction",
            extra={
                'event_type': 'payment_transaction',
                'transaction_id': transaction_id,
                'amount': amount,
                'currency': currency,
                'payment_method': payment_method,
                'status': status,
                'user_id': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

    def log_promotional_campaign(self, campaign_id: int, vendor_id: int,
                               campaign_type: str, budget: float, action: str):
        """Log promotional campaign events."""
        self.logger.info(
            "Promotional campaign",
            extra={
                'event_type': 'promotional_campaign',
                'campaign_id': campaign_id,
                'vendor_id': vendor_id,
                'campaign_type': campaign_type,
                'budget': budget,
                'action': action,
                'timestamp': datetime.utcnow().isoformat()
            }
        )


def setup_logging():
    """
    Configure comprehensive logging for the application.

    Sets up different loggers with appropriate handlers, formatters,
    and rotation policies based on environment configuration.
    """
    # Create logs directory if it doesn't exist
    if settings.LOG_FILE:
        log_dir = Path(settings.LOG_FILE).parent
        log_dir.mkdir(parents=True, exist_ok=True)

    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Clear existing handlers
    root_logger.handlers.clear()

    # Add correlation ID filter to all loggers
    correlation_filter = CorrelationIdFilter()

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)

    if settings.is_production:
        # Use JSON formatter for production
        console_formatter = JSONFormatter()
    else:
        # Use readable formatter for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s'
        )

    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(correlation_filter)
    root_logger.addHandler(console_handler)

    # File handler with rotation (if configured)
    if settings.LOG_FILE:
        # Parse rotation settings
        rotation_when = 'midnight'  # Default
        rotation_interval = 1

        if 'day' in settings.LOG_ROTATION.lower():
            rotation_when = 'midnight'
            rotation_interval = int(settings.LOG_ROTATION.split()[0]) if settings.LOG_ROTATION.split()[0].isdigit() else 1
        elif 'hour' in settings.LOG_ROTATION.lower():
            rotation_when = 'H'
            rotation_interval = int(settings.LOG_ROTATION.split()[0]) if settings.LOG_ROTATION.split()[0].isdigit() else 1

        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=settings.LOG_FILE,
            when=rotation_when,
            interval=rotation_interval,
            backupCount=int(settings.LOG_RETENTION.split()[0]) if settings.LOG_RETENTION.split()[0].isdigit() else 30,
            encoding='utf-8'
        )

        file_formatter = JSONFormatter() if settings.is_production else logging.Formatter(settings.LOG_FORMAT)
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(correlation_filter)
        root_logger.addHandler(file_handler)

    # Configure specific loggers
    loggers_config = {
        'uvicorn.access': logging.WARNING,  # Reduce uvicorn noise
        'uvicorn.error': logging.INFO,
        'sqlalchemy.engine': logging.WARNING if settings.is_production else logging.INFO,
        'security': logging.INFO,
        'performance': logging.INFO,
        'business': logging.INFO,
    }

    for logger_name, level in loggers_config.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)

    logging.getLogger(__name__).info("Logging configuration completed")


# Create specialized logger instances
security_logger = SecurityLogger()
performance_logger = PerformanceLogger()
business_logger = BusinessLogger()
metrics_collector = MetricsCollector()


class CorrelationIdMiddleware(BaseHTTPMiddleware):
    """Middleware to add correlation ID to requests."""

    async def dispatch(self, request: Request, call_next):
        """Add correlation ID to request context."""
        # Get or generate correlation ID
        request_correlation_id = request.headers.get('X-Correlation-ID', str(uuid.uuid4()))

        # Set in context
        correlation_id.set(request_correlation_id)

        # Process request
        response = await call_next(request)

        # Add correlation ID to response headers
        response.headers['X-Correlation-ID'] = request_correlation_id

        return response


class EnhancedRequestLoggingMiddleware(BaseHTTPMiddleware):
    """Enhanced middleware for comprehensive request/response logging."""

    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or ['/health', '/metrics', '/favicon.ico']
        self.logger = logging.getLogger('request')

    async def dispatch(self, request: Request, call_next):
        """Log detailed request and response information."""
        start_time = time.time()

        # Skip logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Extract request information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get('User-Agent', 'Unknown')

        # Log request
        self.logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                'event_type': 'request_start',
                'method': request.method,
                'path': request.url.path,
                'query_params': str(request.query_params),
                'client_ip': client_ip,
                'user_agent': user_agent,
                'content_length': request.headers.get('Content-Length', 0)
            }
        )

        # Process request
        try:
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Log response
            self.logger.info(
                f"Request completed: {response.status_code} in {process_time:.4f}s",
                extra={
                    'event_type': 'request_complete',
                    'method': request.method,
                    'path': request.url.path,
                    'status_code': response.status_code,
                    'duration': process_time,
                    'response_size': response.headers.get('Content-Length', 0)
                }
            )

            # Log performance metrics
            performance_logger.log_request_metrics(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration=process_time
            )

            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)

            return response

        except Exception as e:
            process_time = time.time() - start_time

            # Log error
            self.logger.error(
                f"Request failed: {request.method} {request.url.path} - {str(e)}",
                extra={
                    'event_type': 'request_error',
                    'method': request.method,
                    'path': request.url.path,
                    'duration': process_time,
                    'error': str(e)
                },
                exc_info=True
            )

            raise

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (behind proxy)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()

        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip

        # Fallback to direct client
        return request.client.host if request.client else 'unknown'


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.

    Args:
        name: Logger name (typically __name__)

    Returns:
        logging.Logger: Configured logger instance
    """
    return logging.getLogger(name)


# Export commonly used items
__all__ = [
    'setup_logging',
    'get_logger',
    'security_logger',
    'performance_logger',
    'business_logger',
    'metrics_collector',
    'CorrelationIdMiddleware',
    'EnhancedRequestLoggingMiddleware',
    'correlation_id'
]
