# Culture Connect Backend - Production Docker Compose Configuration
# Production-optimized multi-service setup with scaling, monitoring, and security
# Supports three deployment strategies: Kubernetes, Docker Swarm, and self-hosting

version: '3.8'

services:
  # Culture Connect Backend API - Production Configuration
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      cache_from:
        - culture-connect-api:latest
        - culture-connect-api:cache
    image: culture-connect-api:${CC_VERSION:-latest}
    container_name: culture_connect_api_prod
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        monitor: 60s
        order: start-first
    ports:
      - "8000:8000"
    environment:
      - CC_ENVIRONMENT=production
      - CC_DATABASE_URL=${CC_DATABASE_URL}
      - CC_REDIS_URL=${CC_REDIS_URL}
      - CC_SECRET_KEY=${CC_SECRET_KEY}
      - CC_PAYSTACK_SECRET_KEY=${CC_PAYSTACK_SECRET_KEY}
      - CC_PAYSTACK_PUBLIC_KEY=${CC_PAYSTACK_PUBLIC_KEY}
      - CC_STRIPE_SECRET_KEY=${CC_STRIPE_SECRET_KEY}
      - CC_BUSHA_API_KEY=${CC_BUSHA_API_KEY}
      - CC_AIML_API_KEY=${CC_AIML_API_KEY}
      - CC_FRONTEND_URL=${CC_FRONTEND_URL}
      - CC_PWA_URL=${CC_PWA_URL}
      - CC_SENTRY_DSN=${CC_SENTRY_DSN}
      - CC_ENABLE_PROMOTIONAL_SYSTEM=true
      - CC_ENABLE_CRYPTO_PAYMENTS=true
      - CC_ENABLE_AI_OPTIMIZATION=true
      - CC_LOG_LEVEL=INFO
      - CC_WORKERS=4
      - CC_MAX_CONNECTIONS=1000
      - CC_KEEPALIVE_TIMEOUT=65
      - CC_GRACEFUL_TIMEOUT=30
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
      - geoip_data:/app/data
    networks:
      - culture_connect_prod_network
      - monitoring_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.cultureconnect.ng`)"
      - "traefik.http.routers.api.tls=true"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=8000"
      - "traefik.http.middlewares.api-auth.basicauth.users=${TRAEFIK_BASIC_AUTH}"
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service,environment"

  # PostgreSQL Database - Production Configuration with Replication Ready
  db:
    image: postgres:15-alpine
    container_name: culture_connect_db_prod
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    environment:
      - POSTGRES_DB=${CC_POSTGRES_DB:-culture_connect_db}
      - POSTGRES_USER=${CC_POSTGRES_USER:-culture_connect}
      - POSTGRES_PASSWORD=${CC_POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - PGDATA=/var/lib/postgresql/data/pgdata
      # Replication configuration
      - POSTGRES_REPLICATION_MODE=master
      - POSTGRES_REPLICATION_USER=${CC_POSTGRES_REPLICATION_USER:-replicator}
      - POSTGRES_REPLICATION_PASSWORD=${CC_POSTGRES_REPLICATION_PASSWORD}
      # Performance tuning
      - POSTGRES_SHARED_BUFFERS=256MB
      - POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
      - POSTGRES_MAINTENANCE_WORK_MEM=64MB
      - POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
      - POSTGRES_WAL_BUFFERS=16MB
      - POSTGRES_DEFAULT_STATISTICS_TARGET=100
    ports:
      - "${CC_POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - postgres_backups:/backups
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./scripts/postgres-prod-config.sql:/docker-entrypoint-initdb.d/postgres-prod-config.sql:ro
    networks:
      - culture_connect_prod_network
      - monitoring_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${CC_POSTGRES_USER:-culture_connect} -d ${CC_POSTGRES_DB:-culture_connect_db}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "backup.enable=true"
      - "backup.schedule=0 2 * * *"
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
        labels: "service,environment"
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_connections=200
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=1000

  # Redis Cache - Production Configuration with Clustering Ready
  redis:
    image: redis:7-alpine
    container_name: culture_connect_redis_prod
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    ports:
      - "${CC_REDIS_PORT:-6379}:6379"
    environment:
      - REDIS_PASSWORD=${CC_REDIS_PASSWORD}
      - REDIS_MAXMEMORY=400mb
      - REDIS_MAXMEMORY_POLICY=allkeys-lru
    volumes:
      - redis_data_prod:/data
      - redis_config:/usr/local/etc/redis
    networks:
      - culture_connect_prod_network
      - monitoring_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "backup.enable=true"
      - "backup.schedule=0 4 * * *"
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
        labels: "service,environment"
    command: >
      redis-server
      --requirepass ${CC_REDIS_PASSWORD}
      --maxmemory 400mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
      --auto-aof-rewrite-percentage 100
      --auto-aof-rewrite-min-size 64mb

  # Celery Worker - Background Task Processing
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: culture-connect-api:${CC_VERSION:-latest}
    container_name: culture_connect_celery_worker_prod
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    environment:
      - CC_ENVIRONMENT=production
      - CC_DATABASE_URL=${CC_DATABASE_URL}
      - CC_REDIS_URL=${CC_REDIS_URL}
      - CC_SECRET_KEY=${CC_SECRET_KEY}
      - CC_SENTRY_DSN=${CC_SENTRY_DSN}
      - CC_LOG_LEVEL=INFO
      - CELERY_WORKER_CONCURRENCY=4
      - CELERY_WORKER_PREFETCH_MULTIPLIER=1
      - CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - culture_connect_prod_network
      - monitoring_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_config", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    labels:
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
        labels: "service,environment"
    command: ["celery", "-A", "app.core.celery_config", "worker", "--loglevel=info", "--concurrency=4"]

  # Celery Beat - Task Scheduler
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: culture-connect-api:${CC_VERSION:-latest}
    container_name: culture_connect_celery_beat_prod
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    environment:
      - CC_ENVIRONMENT=production
      - CC_DATABASE_URL=${CC_DATABASE_URL}
      - CC_REDIS_URL=${CC_REDIS_URL}
      - CC_SECRET_KEY=${CC_SECRET_KEY}
      - CC_SENTRY_DSN=${CC_SENTRY_DSN}
      - CC_LOG_LEVEL=INFO
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - celery_beat_data:/app/celerybeat-schedule
    networks:
      - culture_connect_prod_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "test", "-f", "/app/celerybeat-schedule"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "monitoring.enable=true"
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
        labels: "service,environment"
    command: ["celery", "-A", "app.core.celery_config", "beat", "--loglevel=info"]

# Production Volumes with Backup Integration
volumes:
  postgres_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_DATA_PATH:-/opt/culture-connect/data}/postgres
  postgres_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_BACKUP_PATH:-/opt/culture-connect/backups}/postgres
  redis_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_DATA_PATH:-/opt/culture-connect/data}/redis
  redis_config:
    driver: local
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_LOG_PATH:-/opt/culture-connect/logs}
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_UPLOAD_PATH:-/opt/culture-connect/uploads}
  geoip_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CC_DATA_PATH:-/opt/culture-connect/data}/geoip
  celery_beat_data:
    driver: local

# Production Networks with Security Isolation
networks:
  culture_connect_prod_network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: cc_prod_bridge
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      - "environment=production"
      - "project=culture-connect"
  monitoring_network:
    driver: bridge
    external: true
    name: monitoring_network
