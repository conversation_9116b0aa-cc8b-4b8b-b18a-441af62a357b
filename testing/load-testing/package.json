{"name": "culture-connect-load-testing", "version": "1.0.0", "description": "Load testing suite for Culture Connect Backend", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "artillery": "artillery run artillery/load-test-config.yml", "k6": "k6 run k6/load-test-script.js", "benchmark": "node performance/benchmarks/api-benchmark.js", "install-tools": "npm install -g artillery k6 autocannon"}, "keywords": ["load-testing", "performance", "artillery", "k6", "culture-connect"], "author": "Culture Connect Team", "license": "MIT", "dependencies": {"artillery": "^2.0.0", "autocannon": "^7.12.0", "faker": "^5.5.3", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"eslint": "^8.55.0", "prettier": "^3.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}