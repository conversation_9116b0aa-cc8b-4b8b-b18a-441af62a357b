"""
Review Moderation Repository for Culture Connect Backend API.

This module provides comprehensive repository class for review moderation data access operations
including CRUD operations, AI workflow management, and business logic support.

Implements Task 4.4.1 Phase 3 requirements for review moderation repository layer with:
- Complete AI moderation workflow data access
- Manual review escalation and processing
- Performance optimization with <200ms query targets
- Integration with review and admin systems
- Comprehensive audit logging and error handling

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
import time
from datetime import datetime, date, timedelta, timezone
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from app.repositories.base import BaseRepository, PaginationParams, QueryResult, RepositoryError
from app.models.review_models import ReviewModeration, ModerationAction
from app.models.review_models import Review
from app.models.user import User


class ReviewModerationRepository(BaseRepository[ReviewModeration]):
    """
    Repository for review moderation data access operations.

    Provides comprehensive moderation data management including:
    - CRUD operations with performance optimization (<200ms targets)
    - AI moderation workflow management
    - Manual review escalation and processing
    - Moderation analytics and tracking
    - Integration with review and admin systems
    """

    def __init__(self, db):
        """Initialize review moderation repository."""
        super().__init__(ReviewModeration, db)
        self.logger = logging.getLogger(f"{__name__}.ReviewModerationRepository")

    async def create_moderation_record(
        self,
        review_id: int,
        moderation_data: Dict[str, Any],
        moderator_id: Optional[int] = None
    ) -> ReviewModeration:
        """
        Create a new moderation record with validation.

        Performance Metrics:
        - Target response time: <200ms for moderation record creation
        - Review validation: <50ms using review_id index
        - Moderation creation: <100ms with optimized insert
        - AI analysis storage: <50ms for JSONB operations

        Args:
            review_id: Associated review ID
            moderation_data: Moderation creation data (action, reason, AI results)
            moderator_id: Optional admin moderator ID (None for AI moderation)

        Returns:
            Created ReviewModeration instance

        Raises:
            RepositoryError: If moderation record creation fails
        """
        start_time = time.time()

        try:
            # Validate review exists
            review = await self._validate_review_for_moderation(review_id)
            if not review:
                raise RepositoryError(f"Review {review_id} not found for moderation")

            # Prepare moderation object data
            moderation_obj_data = {
                "review_id": review_id,
                "moderator_id": moderator_id,
                "manual_review_required": moderation_data.get("manual_review_required", False),
                **moderation_data
            }

            # Create moderation record
            moderation = await self.create(moderation_obj_data)

            query_time = time.time() - start_time
            self.logger.info(
                f"Moderation record created successfully",
                extra={
                    "moderation_id": moderation.id,
                    "review_id": review_id,
                    "action": moderation.action,
                    "moderator_id": moderator_id,
                    "ai_confidence": moderation.ai_confidence_score,
                    "query_time": query_time
                }
            )

            return moderation

        except Exception as e:
            query_time = time.time() - start_time
            self.logger.error(
                f"Failed to create moderation record",
                extra={
                    "review_id": review_id,
                    "moderator_id": moderator_id,
                    "error": str(e),
                    "query_time": query_time
                }
            )
            raise RepositoryError(f"Failed to create moderation record", e)

    async def get_moderation_by_review(self, review_id: int) -> Optional[ReviewModeration]:
        """
        Get moderation record by review ID with performance optimization.

        Uses idx_review_moderation_review_id index for <50ms response time.

        Args:
            review_id: Review ID

        Returns:
            ReviewModeration instance or None if not found
        """
        try:
            stmt = select(ReviewModeration).where(ReviewModeration.review_id == review_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to get moderation by review: {str(e)}")
            raise RepositoryError(f"Failed to get moderation by review", e)

    async def get_pending_manual_reviews(
        self,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewModeration]:
        """
        Get moderation records requiring manual review.

        Performance Metrics:
        - Target response time: <200ms for pending review queries
        - Uses idx_review_moderation_manual_required index
        - Ordered by creation date for FIFO processing

        Args:
            pagination: Pagination parameters

        Returns:
            Query result with pending manual reviews
        """
        try:
            stmt = (
                select(ReviewModeration)
                .where(
                    and_(
                        ReviewModeration.manual_review_required == True,
                        ReviewModeration.processed_at.is_(None)
                    )
                )
                .order_by(asc(ReviewModeration.created_at))
            )

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get pending manual reviews: {str(e)}")
            raise RepositoryError(f"Failed to get pending manual reviews", e)

    async def get_ai_moderation_queue(
        self,
        confidence_threshold: float = 0.8,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewModeration]:
        """
        Get AI moderation records below confidence threshold.

        Performance Metrics:
        - Target response time: <200ms for AI queue queries
        - Uses idx_review_moderation_ai_confidence index
        - Filtered by confidence score for manual review escalation

        Args:
            confidence_threshold: AI confidence threshold for manual review
            pagination: Pagination parameters

        Returns:
            Query result with low-confidence AI moderation records
        """
        try:
            stmt = (
                select(ReviewModeration)
                .where(
                    and_(
                        ReviewModeration.ai_confidence_score < confidence_threshold,
                        ReviewModeration.processed_at.is_(None)
                    )
                )
                .order_by(asc(ReviewModeration.ai_confidence_score))
            )

            return await self._execute_paginated_query(stmt, pagination)

        except Exception as e:
            self.logger.error(f"Failed to get AI moderation queue: {str(e)}")
            raise RepositoryError(f"Failed to get AI moderation queue", e)

    async def get_moderations_with_details(
        self,
        moderation_ids: List[int]
    ) -> List[ReviewModeration]:
        """
        Get multiple moderation records with associated data.

        Performance optimized with selectinload for related entities.

        Args:
            moderation_ids: List of moderation IDs

        Returns:
            List of ReviewModeration instances with related data
        """
        try:
            stmt = (
                select(ReviewModeration)
                .options(
                    selectinload(ReviewModeration.review),
                    selectinload(ReviewModeration.moderator)
                )
                .where(ReviewModeration.id.in_(moderation_ids))
            )

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(f"Failed to get moderations with details: {str(e)}")
            raise RepositoryError(f"Failed to get moderations with details", e)

    async def process_moderation(
        self,
        moderation_id: int,
        action: ModerationAction,
        moderator_id: Optional[int] = None,
        reason: Optional[str] = None
    ) -> Optional[ReviewModeration]:
        """
        Process a moderation record with final decision.

        Performance target: <100ms for moderation processing.

        Args:
            moderation_id: Moderation record ID
            action: Final moderation action
            moderator_id: Optional admin moderator ID
            reason: Optional reason for the decision

        Returns:
            Updated moderation instance or None if not found
        """
        try:
            moderation = await self.get(moderation_id)
            if not moderation:
                return None

            if moderation.processed_at:
                raise RepositoryError(
                    f"Moderation {moderation_id} has already been processed"
                )

            update_data = {
                "action": action,
                "processed_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            if moderator_id:
                update_data["moderator_id"] = moderator_id

            if reason:
                update_data["reason"] = reason

            updated_moderation = await self.update(moderation_id, update_data)

            self.logger.info(
                f"Moderation processed",
                extra={
                    "moderation_id": moderation_id,
                    "action": action,
                    "moderator_id": moderator_id,
                    "reason": reason
                }
            )

            return updated_moderation

        except Exception as e:
            self.logger.error(f"Failed to process moderation: {str(e)}")
            raise RepositoryError(f"Failed to process moderation", e)

    async def escalate_to_manual_review(
        self,
        moderation_id: int,
        escalation_reason: str
    ) -> Optional[ReviewModeration]:
        """
        Escalate AI moderation to manual review.

        Performance target: <100ms for escalation.

        Args:
            moderation_id: Moderation record ID
            escalation_reason: Reason for escalation

        Returns:
            Updated moderation instance or None if not found
        """
        try:
            moderation = await self.get(moderation_id)
            if not moderation:
                return None

            update_data = {
                "manual_review_required": True,
                "reason": escalation_reason,
                "updated_at": datetime.now(timezone.utc)
            }

            updated_moderation = await self.update(moderation_id, update_data)

            self.logger.info(
                f"Moderation escalated to manual review",
                extra={
                    "moderation_id": moderation_id,
                    "reason": escalation_reason
                }
            )

            return updated_moderation

        except Exception as e:
            self.logger.error(f"Failed to escalate moderation: {str(e)}")
            raise RepositoryError(f"Failed to escalate moderation", e)

    async def get_moderation_statistics(
        self,
        date_range: Optional[Tuple[date, date]] = None
    ) -> Dict[str, Any]:
        """
        Get moderation statistics with performance optimization.

        Performance target: <200ms for statistical queries.

        Args:
            date_range: Optional date range filter

        Returns:
            Dictionary with moderation statistics
        """
        try:
            base_query = select(ReviewModeration)

            # Apply date range filter
            if date_range:
                start_date, end_date = date_range
                base_query = base_query.where(
                    and_(
                        ReviewModeration.created_at >= start_date,
                        ReviewModeration.created_at <= end_date
                    )
                )

            # Get aggregate statistics
            stats_query = select(
                func.count(ReviewModeration.id).label('total_moderations'),
                func.count(func.nullif(ReviewModeration.manual_review_required, False)).label('manual_reviews'),
                func.count(func.nullif(ReviewModeration.processed_at.is_(None), False)).label('processed_moderations'),
                func.avg(ReviewModeration.ai_confidence_score).label('avg_ai_confidence')
            ).select_from(base_query.subquery())

            result = await self.db.execute(stats_query)
            stats = result.first()

            # Get action distribution
            action_dist_query = select(
                ReviewModeration.action,
                func.count(ReviewModeration.id).label('count')
            ).select_from(base_query.subquery()).group_by(ReviewModeration.action)

            action_result = await self.db.execute(action_dist_query)
            action_distribution = {str(row.action): row.count for row in action_result}

            return {
                'total_moderations': stats.total_moderations or 0,
                'manual_reviews': stats.manual_reviews or 0,
                'processed_moderations': stats.processed_moderations or 0,
                'avg_ai_confidence': float(stats.avg_ai_confidence) if stats.avg_ai_confidence else 0.0,
                'action_distribution': action_distribution
            }

        except Exception as e:
            self.logger.error(f"Failed to get moderation statistics: {str(e)}")
            raise RepositoryError(f"Failed to get moderation statistics", e)

    # Private helper methods

    async def _validate_review_for_moderation(self, review_id: int) -> Optional[Review]:
        """
        Validate that review exists for moderation.

        Args:
            review_id: Review ID

        Returns:
            Review instance if exists, None otherwise
        """
        try:
            stmt = select(Review).where(Review.id == review_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(f"Failed to validate review for moderation: {str(e)}")
            return None

    async def _execute_paginated_query(
        self,
        stmt,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[ReviewModeration]:
        """Execute paginated query with default pagination."""
        if pagination is None:
            pagination = PaginationParams(page=1, per_page=20)

        return await self.get_paginated(
            stmt=stmt,
            pagination=pagination
        )
