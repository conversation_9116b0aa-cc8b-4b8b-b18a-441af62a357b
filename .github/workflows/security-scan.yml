# Culture Connect Backend - Security Scanning Workflow
# Comprehensive security scanning for code, dependencies, and containers

name: Security Scan

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'

jobs:
  # =============================================================================
  # CODE SECURITY ANALYSIS
  # =============================================================================
  
  code-security:
    name: Code Security Analysis
    runs-on: ubuntu-latest
    
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety semgrep

    - name: Run Bandit security scan
      run: |
        bandit -r app/ -f json -o bandit-results.json
        bandit -r app/ -f txt -o bandit-results.txt
      continue-on-error: true

    - name: Run Safety dependency scan
      run: |
        safety check --json --output safety-results.json
        safety check --output safety-results.txt
      continue-on-error: true

    - name: Run Semgrep security scan
      run: |
        semgrep --config=auto --json --output=semgrep-results.json app/
        semgrep --config=auto --output=semgrep-results.txt app/
      continue-on-error: true

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: |
          bandit-results.*
          safety-results.*
          semgrep-results.*

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: python
        queries: security-and-quality

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:python"

  # =============================================================================
  # DEPENDENCY VULNERABILITY SCAN
  # =============================================================================
  
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run pip-audit
      run: |
        pip install pip-audit
        pip-audit --format=json --output=pip-audit-results.json
        pip-audit --format=cyclonedx-json --output=sbom.json
      continue-on-error: true

    - name: Generate SBOM (Software Bill of Materials)
      run: |
        pip install cyclonedx-bom
        cyclonedx-py -o sbom-cyclonedx.json

    - name: Upload dependency scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-scan-results
        path: |
          pip-audit-results.json
          sbom.json
          sbom-cyclonedx.json

  # =============================================================================
  # CONTAINER SECURITY SCAN
  # =============================================================================
  
  container-security:
    name: Container Security Scan
    runs-on: ubuntu-latest
    needs: []
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image for scanning
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        load: true
        tags: culture-connect:scan
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'culture-connect:scan'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Run Trivy filesystem scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-fs-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Upload Trivy filesystem scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-fs-results.sarif'

    - name: Run Docker Scout (if available)
      run: |
        if command -v docker-scout &> /dev/null; then
          docker scout cves culture-connect:scan --format sarif --output docker-scout-results.sarif
        else
          echo "Docker Scout not available, skipping..."
        fi
      continue-on-error: true

    - name: Upload container scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: container-scan-results
        path: |
          trivy-results.sarif
          trivy-fs-results.sarif
          docker-scout-results.sarif

  # =============================================================================
  # INFRASTRUCTURE SECURITY SCAN
  # =============================================================================
  
  infrastructure-security:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install security scanning tools
      run: |
        # Install Checkov for infrastructure as code scanning
        pip install checkov
        
        # Install Docker Compose for configuration validation
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose

    - name: Run Checkov scan on Docker files
      run: |
        checkov -f Dockerfile --framework dockerfile --output json --output-file checkov-dockerfile-results.json
        checkov -f Dockerfile --framework dockerfile --output cli --output-file checkov-dockerfile-results.txt
      continue-on-error: true

    - name: Run Checkov scan on Docker Compose files
      run: |
        find . -name "docker-compose*.yml" -exec checkov -f {} --framework docker_compose --output json --output-file checkov-compose-{}.json \;
        find . -name "docker-compose*.yml" -exec checkov -f {} --framework docker_compose --output cli --output-file checkov-compose-{}.txt \;
      continue-on-error: true

    - name: Validate Docker Compose configurations
      run: |
        # Validate all Docker Compose files
        for compose_file in $(find . -name "docker-compose*.yml"); do
          echo "Validating $compose_file"
          docker-compose -f "$compose_file" config --quiet || echo "Validation failed for $compose_file"
        done

    - name: Run security configuration checks
      run: |
        # Check for common security misconfigurations
        echo "Checking for security misconfigurations..."
        
        # Check for exposed secrets in environment files
        find . -name ".env*" -exec grep -H "password\|secret\|key" {} \; > security-config-check.txt || true
        
        # Check for insecure Docker configurations
        grep -r "privileged.*true" docker/ >> security-config-check.txt || true
        grep -r "user.*root" docker/ >> security-config-check.txt || true

    - name: Upload infrastructure scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: infrastructure-scan-results
        path: |
          checkov-*-results.*
          security-config-check.txt

  # =============================================================================
  # SECURITY REPORT GENERATION
  # =============================================================================
  
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [code-security, dependency-scan, container-security, infrastructure-security]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all security scan results
      uses: actions/download-artifact@v3
      with:
        path: security-results/

    - name: Generate comprehensive security report
      run: |
        mkdir -p security-report
        
        echo "# Culture Connect Backend - Security Scan Report" > security-report/README.md
        echo "Generated on: $(date -u)" >> security-report/README.md
        echo "Commit: ${{ github.sha }}" >> security-report/README.md
        echo "Branch: ${{ github.ref }}" >> security-report/README.md
        echo "" >> security-report/README.md
        
        echo "## Scan Results Summary" >> security-report/README.md
        echo "" >> security-report/README.md
        
        # Count findings from each scan type
        if [ -d "security-results/security-scan-results" ]; then
          echo "### Code Security Analysis" >> security-report/README.md
          echo "- Bandit scan completed" >> security-report/README.md
          echo "- Safety dependency scan completed" >> security-report/README.md
          echo "- Semgrep security scan completed" >> security-report/README.md
          echo "" >> security-report/README.md
        fi
        
        if [ -d "security-results/dependency-scan-results" ]; then
          echo "### Dependency Vulnerability Scan" >> security-report/README.md
          echo "- pip-audit scan completed" >> security-report/README.md
          echo "- SBOM generated" >> security-report/README.md
          echo "" >> security-report/README.md
        fi
        
        if [ -d "security-results/container-scan-results" ]; then
          echo "### Container Security Scan" >> security-report/README.md
          echo "- Trivy container scan completed" >> security-report/README.md
          echo "- Trivy filesystem scan completed" >> security-report/README.md
          echo "" >> security-report/README.md
        fi
        
        if [ -d "security-results/infrastructure-scan-results" ]; then
          echo "### Infrastructure Security Scan" >> security-report/README.md
          echo "- Checkov infrastructure scan completed" >> security-report/README.md
          echo "- Docker Compose validation completed" >> security-report/README.md
          echo "" >> security-report/README.md
        fi
        
        echo "## Recommendations" >> security-report/README.md
        echo "1. Review all high and critical severity findings" >> security-report/README.md
        echo "2. Update dependencies with known vulnerabilities" >> security-report/README.md
        echo "3. Address infrastructure security misconfigurations" >> security-report/README.md
        echo "4. Implement additional security controls as needed" >> security-report/README.md

    - name: Upload comprehensive security report
      uses: actions/upload-artifact@v3
      with:
        name: comprehensive-security-report
        path: |
          security-report/
          security-results/

    - name: Comment security report on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('security-report/README.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🔒 Security Scan Report\n\n${report}`
          });
