"""
Transaction models for Culture Connect Backend API.

This module defines comprehensive transaction tracking and audit models for
payment processing, including detailed transaction history and event logging.

Implements Phase 1 of Payment & Transaction Management System with:
- Detailed transaction tracking and audit trails
- Provider response logging with encryption
- Transaction event history for compliance
- Performance-optimized indexing for financial queries
- Integration with payment and reconciliation systems

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, ForeignKey,
    Index, JSON, Numeric, CheckConstraint, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from app.db.base import BaseModelWithAudit
from app.core.payment.config import PaymentProviderType
from app.models.payment import TransactionType


class TransactionStatus(str, Enum):
    """Transaction status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REVERSED = "reversed"
    DISPUTED = "disputed"


class TransactionEventType(str, Enum):
    """Transaction event type enumeration."""
    CREATED = "created"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    DISPUTED = "disputed"
    REVERSED = "reversed"
    WEBHOOK_RECEIVED = "webhook_received"
    PROVIDER_CALLBACK = "provider_callback"


class Transaction(BaseModelWithAudit):
    """
    Transaction model for detailed payment tracking and audit trails.
    
    This model stores comprehensive transaction information including
    provider responses, audit trails, and financial reconciliation data.
    
    Features:
    - Detailed transaction tracking with encrypted provider responses
    - Comprehensive audit logging and event history
    - Performance-optimized indexing for financial queries
    - Integration with payment and reconciliation systems
    """
    
    __tablename__ = "transactions"
    
    # Core transaction information
    payment_id = Column(
        Integer,
        ForeignKey("payments.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated payment ID"
    )
    
    # Transaction details
    type = Column(
        SQLEnum(TransactionType),
        nullable=False,
        index=True,
        doc="Transaction type (charge, refund, payout, fee)"
    )
    
    status = Column(
        SQLEnum(TransactionStatus),
        nullable=False,
        default=TransactionStatus.PENDING,
        index=True,
        doc="Transaction processing status"
    )
    
    # Financial details
    amount = Column(
        Numeric(12, 2),
        nullable=False,
        doc="Transaction amount"
    )
    
    currency = Column(
        String(3),
        nullable=False,
        default="NGN",
        index=True,
        doc="Transaction currency (ISO 4217)"
    )
    
    # Provider information
    provider = Column(
        SQLEnum(PaymentProviderType),
        nullable=False,
        index=True,
        doc="Payment provider"
    )
    
    provider_transaction_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider transaction ID"
    )
    
    reference_id = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        doc="Unique transaction reference"
    )
    
    # Provider response and metadata
    provider_response = Column(
        Text,
        nullable=True,
        doc="Provider response data (encrypted)"
    )
    
    provider_status_code = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Provider status code"
    )
    
    provider_message = Column(
        String(500),
        nullable=True,
        doc="Provider response message"
    )
    
    # Timing information
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Transaction processing completion timestamp"
    )
    
    # Fee tracking
    provider_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Provider transaction fee"
    )
    
    platform_fee = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Platform transaction fee"
    )
    
    net_amount = Column(
        Numeric(12, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Net amount after fees"
    )
    
    # Error and failure tracking
    failure_reason = Column(
        String(255),
        nullable=True,
        doc="Transaction failure reason"
    )
    
    failure_code = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Provider failure code"
    )
    
    retry_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of retry attempts"
    )
    
    # Reconciliation tracking
    reconciled = Column(
        Boolean,
        nullable=False,
        default=False,
        index=True,
        doc="Transaction has been reconciled"
    )
    
    reconciled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Reconciliation timestamp"
    )
    
    reconciliation_reference = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Reconciliation batch reference"
    )
    
    # Metadata and additional information
    transaction_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional transaction metadata"
    )
    
    # Relationships
    payment = relationship(
        "Payment",
        back_populates="transactions",
        doc="Associated payment"
    )
    
    events = relationship(
        "TransactionEvent",
        back_populates="transaction",
        cascade="all, delete-orphan",
        order_by="TransactionEvent.created_at.desc()",
        doc="Transaction event history"
    )
    
    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "amount > 0",
            name="check_transaction_amount_positive"
        ),
        CheckConstraint(
            "provider_fee >= 0",
            name="check_transaction_provider_fee_non_negative"
        ),
        CheckConstraint(
            "platform_fee >= 0",
            name="check_transaction_platform_fee_non_negative"
        ),
        CheckConstraint(
            "net_amount >= 0",
            name="check_transaction_net_amount_non_negative"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="check_transaction_retry_count_non_negative"
        ),
        
        # Performance indexes
        Index("idx_transaction_payment_type", "payment_id", "type"),
        Index("idx_transaction_provider_status", "provider", "status"),
        Index("idx_transaction_type_status", "type", "status"),
        Index("idx_transaction_currency_amount", "currency", "amount"),
        Index("idx_transaction_date_status", "created_at", "status"),
        Index("idx_transaction_processed_date", "processed_at", "status"),
        Index("idx_transaction_reconciled", "reconciled", "reconciled_at"),
        Index("idx_transaction_reference_provider", "reference_id", "provider"),
    )
    
    def __repr__(self) -> str:
        """String representation of the transaction."""
        return f"<Transaction(id={self.id}, ref='{self.reference_id}', type='{self.type}', status='{self.status}')>"
    
    @property
    def is_successful(self) -> bool:
        """Check if transaction is successful."""
        return self.status == TransactionStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if transaction failed."""
        return self.status in {TransactionStatus.FAILED, TransactionStatus.CANCELLED}
    
    @property
    def total_fees(self) -> Decimal:
        """Calculate total fees."""
        return self.provider_fee + self.platform_fee


class TransactionEvent(BaseModelWithAudit):
    """
    Transaction event model for detailed audit trails and compliance logging.
    
    This model tracks all events and state changes throughout the transaction
    lifecycle for audit purposes and regulatory compliance.
    
    Features:
    - Comprehensive event tracking and audit trails
    - Provider webhook and callback logging
    - Performance monitoring and analytics
    - Compliance and regulatory reporting
    """
    
    __tablename__ = "transaction_events"
    
    # Core event information
    transaction_id = Column(
        Integer,
        ForeignKey("transactions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated transaction ID"
    )
    
    # Event details
    event_type = Column(
        SQLEnum(TransactionEventType),
        nullable=False,
        index=True,
        doc="Type of transaction event"
    )
    
    event_source = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Source of the event (system, webhook, api, manual)"
    )
    
    # Event data
    event_data = Column(
        JSON,
        nullable=True,
        doc="Event data and context"
    )
    
    provider_event_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Provider event ID (for webhooks)"
    )
    
    # Status tracking
    previous_status = Column(
        String(50),
        nullable=True,
        doc="Previous transaction status"
    )
    
    new_status = Column(
        String(50),
        nullable=True,
        doc="New transaction status"
    )
    
    # Processing information
    processing_time_ms = Column(
        Integer,
        nullable=True,
        doc="Event processing time in milliseconds"
    )
    
    # Error tracking
    error_message = Column(
        String(500),
        nullable=True,
        doc="Error message if event failed"
    )
    
    error_code = Column(
        String(50),
        nullable=True,
        doc="Error code if event failed"
    )
    
    # Metadata
    event_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional event metadata"
    )
    
    # Relationships
    transaction = relationship(
        "Transaction",
        back_populates="events",
        doc="Associated transaction"
    )
    
    # Constraints and indexes
    __table_args__ = (
        # Check constraints
        CheckConstraint(
            "processing_time_ms IS NULL OR processing_time_ms >= 0",
            name="check_processing_time_non_negative"
        ),
        
        # Performance indexes
        Index("idx_transaction_event_transaction_type", "transaction_id", "event_type"),
        Index("idx_transaction_event_type_date", "event_type", "created_at"),
        Index("idx_transaction_event_source_date", "event_source", "created_at"),
        Index("idx_transaction_event_provider_id", "provider_event_id"),
    )
    
    def __repr__(self) -> str:
        """String representation of the transaction event."""
        return f"<TransactionEvent(id={self.id}, type='{self.event_type}', source='{self.event_source}')>"
