#!/usr/bin/env python3
"""
PostgreSQL Database Setup Script for Culture Connect Backend

This script sets up PostgreSQL database for development and production environments,
ensuring compatibility with the production-grade requirements specified in guardrails.md.

Usage:
    python scripts/setup_postgresql.py --env development
    python scripts/setup_postgresql.py --env production
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any

import asyncpg
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PostgreSQLSetup:
    """PostgreSQL database setup and configuration manager."""
    
    def __init__(self, environment: str = "development"):
        """Initialize PostgreSQL setup manager."""
        self.environment = environment
        self.settings = get_settings()
        
        # Database configuration based on environment
        self.db_configs = {
            "development": {
                "host": "localhost",
                "port": 5432,
                "database": "culture_connect_dev",
                "username": "culture_connect",
                "password": "culture_connect_dev_password",
                "admin_database": "postgres",
                "admin_username": "postgres",
                "admin_password": "postgres"
            },
            "staging": {
                "host": "localhost",
                "port": 5432,
                "database": "culture_connect_staging",
                "username": "culture_connect_staging",
                "password": "culture_connect_staging_password",
                "admin_database": "postgres",
                "admin_username": "postgres",
                "admin_password": "postgres"
            },
            "production": {
                "host": "localhost",  # Will be overridden by environment variables
                "port": 5432,
                "database": "culture_connect_prod",
                "username": "culture_connect_prod",
                "password": "secure_production_password",
                "admin_database": "postgres",
                "admin_username": "postgres",
                "admin_password": "secure_admin_password"
            }
        }
        
        self.config = self.db_configs.get(environment)
        if not self.config:
            raise ValueError(f"Invalid environment: {environment}")
    
    def create_database_sync(self) -> bool:
        """Create database and user using synchronous connection."""
        try:
            # Connect as admin user
            admin_conn = psycopg2.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["admin_database"],
                user=self.config["admin_username"],
                password=self.config["admin_password"]
            )
            admin_conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            admin_cursor = admin_conn.cursor()
            
            # Create user if not exists
            logger.info(f"Creating user: {self.config['username']}")
            admin_cursor.execute(f"""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{self.config['username']}') THEN
                        CREATE USER {self.config['username']} WITH PASSWORD '{self.config['password']}';
                    END IF;
                END
                $$;
            """)
            
            # Create database if not exists
            logger.info(f"Creating database: {self.config['database']}")
            admin_cursor.execute(f"""
                SELECT 1 FROM pg_catalog.pg_database WHERE datname = '{self.config['database']}'
            """)
            
            if not admin_cursor.fetchone():
                admin_cursor.execute(f"""
                    CREATE DATABASE {self.config['database']} 
                    OWNER {self.config['username']} 
                    ENCODING 'UTF8' 
                    LC_COLLATE = 'en_US.UTF-8' 
                    LC_CTYPE = 'en_US.UTF-8'
                """)
                logger.info(f"Database {self.config['database']} created successfully")
            else:
                logger.info(f"Database {self.config['database']} already exists")
            
            # Grant privileges
            admin_cursor.execute(f"""
                GRANT ALL PRIVILEGES ON DATABASE {self.config['database']} TO {self.config['username']}
            """)
            
            admin_cursor.close()
            admin_conn.close()
            
            return True
            
        except psycopg2.Error as e:
            logger.error(f"Error creating database: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return False
    
    async def setup_extensions(self) -> bool:
        """Setup required PostgreSQL extensions."""
        try:
            conn = await asyncpg.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["database"],
                user=self.config["username"],
                password=self.config["password"]
            )
            
            # Required extensions for Culture Connect Backend
            extensions = [
                "uuid-ossp",  # UUID generation
                "pg_trgm",    # Text similarity and indexing
                "btree_gin",  # GIN indexes for better performance
                "btree_gist", # GIST indexes for spatial data
                "postgis"     # Spatial data support (optional, for geo-location)
            ]
            
            for extension in extensions:
                try:
                    await conn.execute(f"CREATE EXTENSION IF NOT EXISTS \"{extension}\"")
                    logger.info(f"Extension {extension} enabled")
                except Exception as e:
                    if extension == "postgis":
                        logger.warning(f"PostGIS extension not available: {e}")
                    else:
                        logger.error(f"Failed to enable extension {extension}: {e}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error setting up extensions: {e}")
            return False
    
    async def verify_connection(self) -> bool:
        """Verify database connection and basic functionality."""
        try:
            conn = await asyncpg.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["database"],
                user=self.config["username"],
                password=self.config["password"]
            )
            
            # Test basic operations
            result = await conn.fetchval("SELECT version()")
            logger.info(f"PostgreSQL version: {result}")
            
            # Test UUID generation
            uuid_result = await conn.fetchval("SELECT uuid_generate_v4()")
            logger.info(f"UUID generation test: {uuid_result}")
            
            # Test JSON operations
            json_result = await conn.fetchval("SELECT '{\"test\": \"value\"}'::jsonb")
            logger.info(f"JSON operations test: {json_result}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Connection verification failed: {e}")
            return False
    
    def generate_env_config(self) -> str:
        """Generate environment configuration for the database."""
        database_url = (
            f"postgresql+asyncpg://{self.config['username']}:"
            f"{self.config['password']}@{self.config['host']}:"
            f"{self.config['port']}/{self.config['database']}"
        )
        
        return f"""
# PostgreSQL Configuration for {self.environment.upper()}
CC_DATABASE_URL={database_url}
CC_DATABASE_POOL_SIZE=10
CC_DATABASE_MAX_OVERFLOW=20
CC_DATABASE_POOL_TIMEOUT=30
"""
    
    async def run_setup(self) -> bool:
        """Run complete PostgreSQL setup process."""
        logger.info(f"Starting PostgreSQL setup for {self.environment} environment")
        
        # Step 1: Create database and user
        if not self.create_database_sync():
            logger.error("Failed to create database and user")
            return False
        
        # Step 2: Setup extensions
        if not await self.setup_extensions():
            logger.error("Failed to setup extensions")
            return False
        
        # Step 3: Verify connection
        if not await self.verify_connection():
            logger.error("Failed to verify connection")
            return False
        
        logger.info("PostgreSQL setup completed successfully!")
        logger.info("Database configuration:")
        print(self.generate_env_config())
        
        return True


async def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup PostgreSQL for Culture Connect Backend")
    parser.add_argument(
        "--env",
        choices=["development", "staging", "production"],
        default="development",
        help="Environment to setup (default: development)"
    )
    parser.add_argument(
        "--verify-only",
        action="store_true",
        help="Only verify existing connection"
    )
    
    args = parser.parse_args()
    
    setup = PostgreSQLSetup(args.env)
    
    if args.verify_only:
        success = await setup.verify_connection()
        if success:
            logger.info("Database connection verified successfully")
        else:
            logger.error("Database connection verification failed")
            sys.exit(1)
    else:
        success = await setup.run_setup()
        if not success:
            logger.error("PostgreSQL setup failed")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
