"""
Unit tests for database session management.

This module tests the enhanced database session management utilities
including context managers, retry logic, and error handling.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import Disconnection<PERSON><PERSON>r, OperationalError, TimeoutError as SQLTimeoutError

from app.db.session import (
    DatabaseSessionManager,
    DatabaseError,
    DatabaseConnectionError,
    DatabaseTransactionError,
    get_db_session,
    get_db_transaction,
    test_database_connection,
    get_database_info,
    with_database_error_handling,
    session_manager
)


class TestDatabaseSessionManager:
    """Test cases for DatabaseSessionManager class."""
    
    @pytest.fixture
    def session_manager_instance(self):
        """Create a fresh session manager instance for testing."""
        return DatabaseSessionManager()
    
    @pytest.mark.asyncio
    async def test_create_session_success(self, session_manager_instance):
        """Test successful session creation."""
        with patch('app.db.session.AsyncSessionLocal') as mock_session_local:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value = mock_session
            
            session = await session_manager_instance._create_session_with_retry()
            
            assert session == mock_session
            mock_session.execute.assert_called_once()
            assert session_manager_instance.connection_pool_stats["total_connections"] == 1
            assert session_manager_instance.connection_pool_stats["active_connections"] == 1
    
    @pytest.mark.asyncio
    async def test_create_session_retry_on_connection_error(self, session_manager_instance):
        """Test session creation retry on connection errors."""
        with patch('app.db.session.AsyncSessionLocal') as mock_session_local:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value = mock_session
            
            # First call raises DisconnectionError, second succeeds
            mock_session.execute.side_effect = [DisconnectionError("Connection lost"), None]
            
            session = await session_manager_instance._create_session_with_retry()
            
            assert session == mock_session
            assert mock_session.execute.call_count == 2
            assert session_manager_instance.connection_pool_stats["retry_attempts"] >= 1
    
    @pytest.mark.asyncio
    async def test_create_session_max_retries_exceeded(self, session_manager_instance):
        """Test session creation failure after max retries."""
        with patch('app.db.session.AsyncSessionLocal') as mock_session_local:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value = mock_session
            
            # Always raise DisconnectionError
            mock_session.execute.side_effect = DisconnectionError("Connection lost")
            
            with pytest.raises(DisconnectionError):
                await session_manager_instance._create_session_with_retry()
            
            assert session_manager_instance.connection_pool_stats["failed_connections"] > 0
    
    @pytest.mark.asyncio
    async def test_close_session_safely_success(self, session_manager_instance):
        """Test successful session closure."""
        mock_session = AsyncMock(spec=AsyncSession)
        
        # Set initial active connections
        session_manager_instance.connection_pool_stats["active_connections"] = 1
        
        await session_manager_instance._close_session_safely(mock_session)
        
        mock_session.close.assert_called_once()
        assert session_manager_instance.connection_pool_stats["active_connections"] == 0
    
    @pytest.mark.asyncio
    async def test_close_session_safely_with_error(self, session_manager_instance):
        """Test session closure with error handling."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session.close.side_effect = Exception("Close error")
        
        # Should not raise exception
        await session_manager_instance._close_session_safely(mock_session)
        
        mock_session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_session_context_manager_success(self, session_manager_instance):
        """Test successful session context manager."""
        with patch.object(session_manager_instance, '_create_session_with_retry') as mock_create:
            with patch.object(session_manager_instance, '_close_session_safely') as mock_close:
                mock_session = AsyncMock(spec=AsyncSession)
                mock_create.return_value = mock_session
                
                async with session_manager_instance.get_session() as session:
                    assert session == mock_session
                
                mock_create.assert_called_once()
                mock_close.assert_called_once_with(mock_session)
    
    @pytest.mark.asyncio
    async def test_get_session_context_manager_with_error(self, session_manager_instance):
        """Test session context manager with error and rollback."""
        with patch.object(session_manager_instance, '_create_session_with_retry') as mock_create:
            with patch.object(session_manager_instance, '_close_session_safely') as mock_close:
                mock_session = AsyncMock(spec=AsyncSession)
                mock_create.return_value = mock_session
                
                with pytest.raises(DatabaseError):
                    async with session_manager_instance.get_session() as session:
                        raise Exception("Test error")
                
                mock_session.rollback.assert_called_once()
                mock_close.assert_called_once_with(mock_session)
    
    @pytest.mark.asyncio
    async def test_get_transaction_context_manager_success(self, session_manager_instance):
        """Test successful transaction context manager."""
        with patch.object(session_manager_instance, 'get_session') as mock_get_session:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_session.return_value.__aenter__.return_value = mock_session
            mock_get_session.return_value.__aexit__.return_value = None
            
            async with session_manager_instance.get_transaction() as session:
                assert session == mock_session
            
            mock_session.begin.assert_called_once()
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_transaction_context_manager_with_error(self, session_manager_instance):
        """Test transaction context manager with error and rollback."""
        with patch.object(session_manager_instance, 'get_session') as mock_get_session:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_session.return_value.__aenter__.return_value = mock_session
            mock_get_session.return_value.__aexit__.return_value = None
            
            with pytest.raises(DatabaseTransactionError):
                async with session_manager_instance.get_transaction() as session:
                    raise Exception("Transaction error")
            
            mock_session.begin.assert_called_once()
            mock_session.rollback.assert_called_once()
    
    def test_get_connection_stats(self, session_manager_instance):
        """Test connection statistics retrieval."""
        # Set some test stats
        session_manager_instance.connection_pool_stats.update({
            "total_connections": 10,
            "active_connections": 5,
            "failed_connections": 2,
            "retry_attempts": 3
        })
        
        stats = session_manager_instance.get_connection_stats()
        
        assert stats["total_connections"] == 10
        assert stats["active_connections"] == 5
        assert stats["failed_connections"] == 2
        assert stats["retry_attempts"] == 3
        
        # Ensure it returns a copy, not the original
        stats["total_connections"] = 999
        assert session_manager_instance.connection_pool_stats["total_connections"] == 10


class TestFastAPIDependencies:
    """Test cases for FastAPI dependency functions."""
    
    @pytest.mark.asyncio
    async def test_get_db_session_dependency(self):
        """Test get_db_session FastAPI dependency."""
        with patch.object(session_manager, 'get_session') as mock_get_session:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_session.return_value.__aenter__.return_value = mock_session
            mock_get_session.return_value.__aexit__.return_value = None
            
            async with get_db_session() as session:
                assert session == mock_session
            
            mock_get_session.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_db_transaction_dependency(self):
        """Test get_db_transaction FastAPI dependency."""
        with patch.object(session_manager, 'get_transaction') as mock_get_transaction:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_transaction.return_value.__aenter__.return_value = mock_session
            mock_get_transaction.return_value.__aexit__.return_value = None
            
            async with get_db_transaction() as session:
                assert session == mock_session
            
            mock_get_transaction.assert_called_once()


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    @pytest.mark.asyncio
    async def test_test_database_connection_success(self):
        """Test successful database connection test."""
        with patch.object(session_manager, 'get_session') as mock_get_session:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_session.return_value.__aenter__.return_value = mock_session
            mock_get_session.return_value.__aexit__.return_value = None
            
            result = await test_database_connection()
            
            assert result is True
            mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_test_database_connection_failure(self):
        """Test database connection test failure."""
        with patch.object(session_manager, 'get_session') as mock_get_session:
            mock_get_session.side_effect = Exception("Connection failed")
            
            result = await test_database_connection()
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_database_info_success(self):
        """Test successful database info retrieval."""
        with patch.object(session_manager, 'get_session') as mock_get_session:
            mock_session = AsyncMock(spec=AsyncSession)
            mock_get_session.return_value.__aenter__.return_value = mock_session
            mock_get_session.return_value.__aexit__.return_value = None
            
            # Mock database query results
            mock_version_result = MagicMock()
            mock_version_result.scalar.return_value = "PostgreSQL 15.0"
            
            mock_size_result = MagicMock()
            mock_size_result.scalar.return_value = "100 MB"
            
            mock_connections_result = MagicMock()
            mock_connections_result.scalar.return_value = 5
            
            mock_session.execute.side_effect = [
                mock_version_result,
                mock_size_result,
                mock_connections_result
            ]
            
            with patch('app.db.session.async_engine') as mock_engine:
                mock_pool = MagicMock()
                mock_pool.size.return_value = 10
                mock_pool.checkedout.return_value = 3
                mock_pool.overflow.return_value = 2
                mock_engine.pool = mock_pool
                
                info = await get_database_info()
            
            assert info["version"] == "PostgreSQL 15.0"
            assert info["database_size"] == "100 MB"
            assert info["active_connections"] == 5
            assert "pool_stats" in info
            assert info["engine_pool_size"] == 10
    
    @pytest.mark.asyncio
    async def test_get_database_info_failure(self):
        """Test database info retrieval failure."""
        with patch.object(session_manager, 'get_session') as mock_get_session:
            mock_get_session.side_effect = Exception("Database error")
            
            info = await get_database_info()
            
            assert "error" in info
            assert "Database error" in info["error"]


class TestErrorHandlingDecorator:
    """Test cases for database error handling decorator."""
    
    @pytest.mark.asyncio
    async def test_with_database_error_handling_success(self):
        """Test decorator with successful function execution."""
        @with_database_error_handling
        async def test_function():
            return "success"
        
        result = await test_function()
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_with_database_error_handling_database_error(self):
        """Test decorator with DatabaseError (should re-raise)."""
        @with_database_error_handling
        async def test_function():
            raise DatabaseError("Test database error")
        
        with pytest.raises(DatabaseError) as exc_info:
            await test_function()
        
        assert "Test database error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_with_database_error_handling_sqlalchemy_error(self):
        """Test decorator with SQLAlchemy error (should wrap)."""
        @with_database_error_handling
        async def test_function():
            raise OperationalError("SQL error", None, None)
        
        with pytest.raises(DatabaseError) as exc_info:
            await test_function()
        
        assert "Database operation failed" in str(exc_info.value)
        assert isinstance(exc_info.value.original_error, OperationalError)
    
    @pytest.mark.asyncio
    async def test_with_database_error_handling_unexpected_error(self):
        """Test decorator with unexpected error (should wrap)."""
        @with_database_error_handling
        async def test_function():
            raise ValueError("Unexpected error")
        
        with pytest.raises(DatabaseError) as exc_info:
            await test_function()
        
        assert "Unexpected database error" in str(exc_info.value)
        assert isinstance(exc_info.value.original_error, ValueError)


class TestDatabaseErrors:
    """Test cases for custom database error classes."""
    
    def test_database_error_creation(self):
        """Test DatabaseError creation."""
        original_error = ValueError("Original error")
        error = DatabaseError("Test message", original_error)
        
        assert error.message == "Test message"
        assert error.original_error == original_error
        assert str(error) == "Test message"
    
    def test_database_connection_error_creation(self):
        """Test DatabaseConnectionError creation."""
        error = DatabaseConnectionError("Connection failed")
        
        assert error.message == "Connection failed"
        assert error.original_error is None
        assert isinstance(error, DatabaseError)
    
    def test_database_transaction_error_creation(self):
        """Test DatabaseTransactionError creation."""
        error = DatabaseTransactionError("Transaction failed")
        
        assert error.message == "Transaction failed"
        assert error.original_error is None
        assert isinstance(error, DatabaseError)
