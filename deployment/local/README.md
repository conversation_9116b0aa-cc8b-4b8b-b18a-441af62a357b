# Culture Connect Backend - Local Development Setup Guide

**🎯 For Beginners**: This guide assumes you're new to Docker and containerized development
**⏱️ Setup Time**: 30-45 minutes
**💻 Works On**: macOS, Windows, and Linux

## 🚀 What You'll Build

By the end of this guide, you'll have:
- ✅ A complete Culture Connect backend running on your computer
- ✅ A database with sample data to work with
- ✅ Development tools to test and modify the application
- ✅ Everything needed to start building features

## 📋 Step 1: Install Required Software

**Don't worry if you've never used these tools before - we'll guide you through everything!**

### Install Docker Desktop (Required)

Docker is like a virtual machine that runs our application in a consistent environment.

#### For macOS:
```bash
# Option 1: Using Homebrew (recommended)
brew install --cask docker

# Option 2: Download from website
# Go to https://docs.docker.com/desktop/install/mac/
# Download and install Docker Desktop for Mac
```

#### For Windows:
```bash
# Download from: https://docs.docker.com/desktop/install/windows/
# Run the installer and follow the setup wizard
# Make sure to enable WSL 2 when prompted
```

#### For Linux (Ubuntu/Debian):
```bash
# Update package list
sudo apt update

# Install Docker
sudo apt install docker.io docker-compose

# Add your user to docker group (so you don't need sudo)
sudo usermod -aG docker $USER

# Log out and log back in for changes to take effect
```

### Install Additional Tools

#### Install Git (if not already installed):
```bash
# macOS
brew install git

# Windows - Download from: https://git-scm.com/download/win
# Linux
sudo apt install git
```

#### Install kubectl (Kubernetes command-line tool):
```bash
# macOS
brew install kubectl

# Windows (using Chocolatey)
choco install kubernetes-cli

# Linux
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
```

#### Install Helm (Kubernetes package manager):
```bash
# macOS
brew install helm

# Windows (using Chocolatey)
choco install kubernetes-helm

# Linux
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

### Verify Everything is Installed
```bash
# Check Docker (should show version)
docker --version

# Check Git (should show version)
git --version

# Check kubectl (should show version)
kubectl version --client

# Check Helm (should show version)
helm version
```

**✅ Success Check**: All commands above should show version numbers without errors.

## 🚀 Step 2: Get the Culture Connect Code

### Download the Project
```bash
# Navigate to where you want to store the project (e.g., your Desktop)
cd ~/Desktop

# Download the Culture Connect backend code
git clone https://github.com/culture-connect/backend.git

# Go into the project folder
cd backend

# You should now see all the project files
ls -la
```

**What just happened?** You downloaded all the Culture Connect code to your computer.

## 🎯 Step 3: Start the Application (Recommended Path)

**We'll use Docker Compose - it's the easiest way for beginners to get everything running.**

### Configure the Application
```bash
# Make sure you're in the backend folder
cd backend

# Check that the environment file exists (it should already be there)
ls -la environments/development/.env.development

# This file contains all the settings our application needs
```

### Start All Services
```bash
# Start the entire Culture Connect application
# This will download and start: database, cache, API server, and background workers
docker-compose -f docker/development/docker-compose.yml up -d

# Wait for everything to start (this might take 2-3 minutes the first time)
echo "Starting services... please wait 2-3 minutes for everything to be ready"
```

**What's happening?** Docker is:
1. 📥 Downloading the required software (PostgreSQL database, Redis cache, etc.)
2. 🏗️ Building the Culture Connect application
3. 🚀 Starting everything in the background

### Check if Everything Started Successfully
```bash
# See all running services
docker-compose -f docker/development/docker-compose.yml ps

# You should see these services running:
# - culture_connect_api_dev (the main application)
# - culture_connect_db_dev (the database)
# - culture_connect_redis_dev (the cache)
# - culture_connect_celery_dev (background worker)
```

### Test Your Setup
```bash
# Test if the API is working
curl http://localhost:8000/health

# You should see something like:
# {"status":"healthy","timestamp":"2025-02-01T10:00:00Z","version":"dev-latest"}
```

**🎉 Success!** If you see the health response, your Culture Connect backend is running!

## � Step 4: Set Up Development Tools

### Access the Database (Optional but Helpful)

You can view and edit your database using a web interface:

```bash
# Start the database admin tool (Adminer)
docker-compose -f docker/development/docker-compose.yml --profile dev up -d adminer

# Open your web browser and go to:
# http://localhost:8080

# Login with these details:
# Server: db
# Username: culture_connect_dev
# Password: dev_password_123
# Database: culture_connect_dev_db
```

### Access Email Testing (Optional)

Culture Connect sends emails (like password resets). You can see these emails in a test inbox:

```bash
# Start the email testing tool (MailHog)
docker-compose -f docker/development/docker-compose.yml --profile dev up -d mailhog

# Open your web browser and go to:
# http://localhost:8025

# All emails sent by the application will appear here
```

### View API Documentation

```bash
# The API documentation is automatically available at:
# http://localhost:8000/docs (Swagger UI - interactive)
# http://localhost:8000/redoc (ReDoc - clean documentation)
```

## 🧪 Step 5: Quick Success Check

Let's make sure everything is working correctly:

### 1. Check All Services Are Running
```bash
# This should show 4 services running (all with "Up" status)
docker-compose -f docker/development/docker-compose.yml ps
```

### 2. Test the API Health
```bash
# Test basic health
curl http://localhost:8000/health

# Test database connection
curl http://localhost:8000/health/database

# Test cache connection
curl http://localhost:8000/health/redis
```

### 3. Test API Documentation
```bash
# Open in your browser - you should see interactive API docs
open http://localhost:8000/docs
```

**✅ All Good?** If all the above work, you're ready to start developing!

## 🛠️ Common Development Tasks

### View Application Logs
```bash
# See what the API is doing
docker-compose -f docker/development/docker-compose.yml logs -f api

# See database logs
docker-compose -f docker/development/docker-compose.yml logs -f db

# See all logs at once
docker-compose -f docker/development/docker-compose.yml logs -f
```

### Stop and Start Services
```bash
# Stop everything
docker-compose -f docker/development/docker-compose.yml down

# Start everything again
docker-compose -f docker/development/docker-compose.yml up -d

# Restart just the API (useful when you make code changes)
docker-compose -f docker/development/docker-compose.yml restart api
```

### Update the Application
```bash
# Get the latest code changes
git pull

# Rebuild and restart (do this when code changes significantly)
docker-compose -f docker/development/docker-compose.yml down
docker-compose -f docker/development/docker-compose.yml up --build -d
```

### Run Database Migrations
```bash
# Apply database schema changes
docker-compose -f docker/development/docker-compose.yml exec api alembic upgrade head
```

## 🚨 Troubleshooting Common Issues

**Don't panic!** These are the most common issues beginners face and how to fix them.

### Issue 1: "Port already in use" Error

**Problem**: You see an error like "port 8000 is already in use"

**Solution**:
```bash
# Find what's using the port
lsof -i :8000

# Kill the process (replace XXXX with the process ID from above)
kill -9 XXXX

# Or use a different port
docker-compose -f docker/development/docker-compose.yml down
# Edit the docker-compose.yml file to change "8000:8000" to "8001:8000"
docker-compose -f docker/development/docker-compose.yml up -d
```

### Issue 2: Docker Desktop Not Running

**Problem**: You see "Cannot connect to the Docker daemon"

**Solution**:
```bash
# Make sure Docker Desktop is running
# Look for the Docker whale icon in your system tray/menu bar
# If it's not there, start Docker Desktop from your Applications folder

# On Linux, start Docker service
sudo systemctl start docker
```

### Issue 3: Services Won't Start

**Problem**: Some containers keep restarting or failing

**Solution**:
```bash
# Check what's wrong
docker-compose -f docker/development/docker-compose.yml logs

# Often it's a resource issue - give Docker more memory
# Docker Desktop → Settings → Resources → Memory: increase to 8GB+

# Or clean up Docker and try again
docker system prune -a
docker-compose -f docker/development/docker-compose.yml up --build -d
```

### Issue 4: Database Connection Failed

**Problem**: API can't connect to database

**Solution**:
```bash
# Check if database is running
docker-compose -f docker/development/docker-compose.yml ps db

# Restart the database
docker-compose -f docker/development/docker-compose.yml restart db

# Wait 30 seconds, then restart the API
docker-compose -f docker/development/docker-compose.yml restart api
```

### Issue 5: "Permission Denied" Errors

**Problem**: You get permission errors when running commands

**Solution**:
```bash
# On Linux/macOS, add your user to the docker group
sudo usermod -aG docker $USER

# Log out and log back in, then try again

# Or run commands with sudo (not recommended for regular use)
sudo docker-compose -f docker/development/docker-compose.yml up -d
```

## 🎯 What's Next?

Congratulations! You now have a fully functional Culture Connect backend. Here's what you can do next:

### 1. Explore the API
```bash
# Open the interactive API documentation
open http://localhost:8000/docs

# Try making API calls directly from the browser
# You can test user registration, login, and other features
```

### 2. Make Your First Code Change
```bash
# The code is in the 'app' folder
ls app/

# Try editing a file, then restart the API to see your changes
docker-compose -f docker/development/docker-compose.yml restart api
```

### 3. Run Tests
```bash
# Run the test suite to make sure everything works
docker-compose -f docker/development/docker-compose.yml exec api pytest tests/ -v
```

### 4. Learn the Project Structure
```
backend/
├── app/                    # Main application code
│   ├── api/               # API endpoints
│   ├── core/              # Core configuration
│   ├── models/            # Database models
│   ├── services/          # Business logic
│   └── schemas/           # Data validation
├── docker/                # Docker configuration
├── environments/          # Environment settings
├── tests/                 # Test files
└── docs/                  # Documentation
```

## 📚 Helpful Resources

### Development Tools URLs
- **API Documentation**: http://localhost:8000/docs
- **Database Admin**: http://localhost:8080 (when adminer is running)
- **Email Testing**: http://localhost:8025 (when mailhog is running)

### Important Commands Reference
```bash
# Start everything
docker-compose -f docker/development/docker-compose.yml up -d

# Stop everything
docker-compose -f docker/development/docker-compose.yml down

# View logs
docker-compose -f docker/development/docker-compose.yml logs -f

# Restart API only
docker-compose -f docker/development/docker-compose.yml restart api

# Run tests
docker-compose -f docker/development/docker-compose.yml exec api pytest

# Access database
docker-compose -f docker/development/docker-compose.yml exec db psql -U culture_connect_dev -d culture_connect_dev_db
```

## 🆘 Getting Help

### If You're Stuck

1. **Check the logs first**:
   ```bash
   docker-compose -f docker/development/docker-compose.yml logs -f
   ```

2. **Make sure Docker Desktop has enough resources**:
   - Go to Docker Desktop → Settings → Resources
   - Set Memory to at least 8GB
   - Set CPUs to at least 4

3. **Try the "nuclear option"** (clean restart):
   ```bash
   # Stop everything
   docker-compose -f docker/development/docker-compose.yml down

   # Remove all containers and images
   docker system prune -a

   # Start fresh
   docker-compose -f docker/development/docker-compose.yml up --build -d
   ```

### Documentation and Support

- **Project Documentation**: Check the `docs/` folder for detailed guides
- **API Reference**: http://localhost:8000/docs (when running)
- **GitHub Issues**: Report bugs and ask questions
- **Slack Community**: #culture-connect-dev

### Advanced Setup (Optional)

If you want to use Kubernetes instead of Docker Compose (more complex but production-like):

```bash
# Enable Kubernetes in Docker Desktop
# Docker Desktop → Settings → Kubernetes → Enable Kubernetes

# Deploy to Kubernetes (advanced users only)
kubectl apply -f infrastructure/kubernetes/base/
kubectl apply -f infrastructure/kubernetes/overlays/development/

# Check deployment
kubectl get pods -n culture-connect-dev
```

## 🎉 You're Ready!

You now have:
- ✅ Culture Connect backend running locally
- ✅ Database with proper schema
- ✅ Development tools for testing and debugging
- ✅ Knowledge of common troubleshooting steps

**Happy coding!** 🚀

---

*This guide was designed for beginners. If you're an experienced developer and want more advanced setup options, check the other deployment guides in the `deployment/` folder.*


