"""
Workflow management repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for workflow and job
orchestration including:
- WorkflowRepository: Workflow definition and execution management
- JobDependencyRepository: Job dependency relationships and resolution
- WorkflowStepRepository: Individual workflow step management
- JobScheduleRepository: Advanced scheduling configurations
- WorkflowAlertRepository: Monitoring and alerting management

Implements Task 6.2.2 Phase 2 requirements for job orchestration engine with
production-grade PostgreSQL optimization, async/await patterns, Redis caching
integration, and <200ms query performance targets.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy import select, and_, or_, func, text, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from app.repositories.base import BaseRepository, QueryResult, PaginationParams, FilterParams, SortParams
from app.models.workflow_models import (
    WorkflowDefinition, WorkflowExecution, JobDependency, WorkflowStep,
    WorkflowStepExecution, JobSchedule, WorkflowAlert,
    WorkflowStatus, ExecutionStatus, DependencyType, StepStatus, AlertSeverity, AlertChannel,
    JobMonitoringSession, AlertTriggerEvent, AlertDeliveryRecord,
    MonitoringStatus, AlertDeliveryStatus
)
from app.schemas.workflow_schemas import (
    WorkflowDefinitionCreate, WorkflowDefinitionUpdate, WorkflowExecutionCreate,
    JobDependencyCreate, WorkflowStepCreate, JobScheduleCreate, WorkflowAlertCreate
)
# Create exception classes for repository operations
class ValidationError(Exception):
    """Exception for validation errors."""
    pass

class NotFoundError(Exception):
    """Exception for resource not found errors."""
    pass

class ConflictError(Exception):
    """Exception for resource conflict errors."""
    pass
from app.core.monitoring import metrics_collector

logger = logging.getLogger(__name__)


class WorkflowRepository(BaseRepository[WorkflowDefinition]):
    """
    Repository for workflow definition and execution management.

    Provides comprehensive CRUD operations for workflow definitions with
    performance optimization, caching integration, and dependency tracking.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(WorkflowDefinition, db_session)

    async def create_workflow_definition(
        self,
        workflow_data: WorkflowDefinitionCreate,
        created_by: Optional[UUID] = None,
        correlation_id: Optional[str] = None
    ) -> WorkflowDefinition:
        """
        Create a new workflow definition.

        Args:
            workflow_data: Workflow creation data
            created_by: User creating the workflow
            correlation_id: Request correlation ID

        Returns:
            Created workflow definition

        Raises:
            ValidationError: If workflow data is invalid
            ConflictError: If workflow name already exists
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Check for existing workflow with same name
            existing = await self._get_by_name(workflow_data.name)
            if existing:
                raise ConflictError(f"Workflow with name '{workflow_data.name}' already exists")

            # Create workflow definition
            workflow = WorkflowDefinition(
                name=workflow_data.name,
                description=workflow_data.description,
                version=workflow_data.version,
                status=workflow_data.status,
                configuration=workflow_data.configuration,
                tags=workflow_data.tags,
                workflow_metadata=workflow_data.workflow_metadata,
                created_by=created_by,
                team_id=workflow_data.team_id,
                max_execution_time=workflow_data.max_execution_time,
                max_retries=workflow_data.max_retries,
                retry_delay=workflow_data.retry_delay,
                is_active=workflow_data.is_active,
                priority=workflow_data.priority
            )

            created_workflow = await self.create(workflow)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Workflow definition created successfully",
                extra={
                    "workflow_id": str(created_workflow.id),
                    "workflow_name": created_workflow.name,
                    "created_by": str(created_by) if created_by else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return created_workflow

        except ConflictError:
            raise
        except Exception as e:
            logger.error(
                "Failed to create workflow definition",
                extra={
                    "workflow_name": workflow_data.name,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create workflow definition: {str(e)}")

    async def get_workflow_with_steps(
        self,
        workflow_id: UUID,
        correlation_id: Optional[str] = None
    ) -> Optional[WorkflowDefinition]:
        """
        Get workflow definition with all steps loaded.

        Args:
            workflow_id: Workflow definition ID
            correlation_id: Request correlation ID

        Returns:
            Workflow definition with steps or None if not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.id == workflow_id
            ).options(
                selectinload(self.model.steps),
                selectinload(self.model.executions),
                selectinload(self.model.schedules),
                selectinload(self.model.alerts)
            )

            result = await self.session.execute(query)
            workflow = result.scalar_one_or_none()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_get_with_steps",
                execution_time_ms=execution_time,
                record_count=1 if workflow else 0,
                correlation_id=correlation_id
            )

            if workflow:
                logger.info(
                    "Workflow retrieved with steps",
                    extra={
                        "workflow_id": str(workflow_id),
                        "step_count": len(workflow.steps),
                        "execution_time_ms": execution_time,
                        "correlation_id": correlation_id
                    }
                )

            return workflow

        except Exception as e:
            logger.error(
                "Failed to retrieve workflow with steps",
                extra={
                    "workflow_id": str(workflow_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def get_active_workflows(
        self,
        team_id: Optional[UUID] = None,
        pagination: Optional[PaginationParams] = None,
        correlation_id: Optional[str] = None
    ) -> QueryResult[WorkflowDefinition]:
        """
        Get all active workflow definitions.

        Args:
            team_id: Optional team filter
            pagination: Pagination parameters
            correlation_id: Request correlation ID

        Returns:
            Query result with active workflows
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                and_(
                    self.model.is_active == True,
                    self.model.status == WorkflowStatus.ACTIVE
                )
            )

            if team_id:
                query = query.where(self.model.team_id == team_id)

            # Apply pagination
            if pagination:
                query = query.offset(pagination.offset).limit(pagination.limit)

            # Order by priority and creation date
            query = query.order_by(desc(self.model.priority), desc(self.model.created_at))

            result = await self.session.execute(query)
            workflows = result.scalars().all()

            # Get total count
            count_query = select(func.count(self.model.id)).where(
                and_(
                    self.model.is_active == True,
                    self.model.status == WorkflowStatus.ACTIVE
                )
            )
            if team_id:
                count_query = count_query.where(self.model.team_id == team_id)

            count_result = await self.session.execute(count_query)
            total_count = count_result.scalar()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_get_active",
                execution_time_ms=execution_time,
                record_count=len(workflows),
                correlation_id=correlation_id
            )

            logger.info(
                "Active workflows retrieved",
                extra={
                    "workflow_count": len(workflows),
                    "team_id": str(team_id) if team_id else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return QueryResult(
                items=workflows,
                total_count=total_count,
                page=pagination.page if pagination else 1,
                page_size=pagination.limit if pagination else len(workflows),
                has_next=pagination and (pagination.offset + pagination.limit) < total_count if pagination else False
            )

        except Exception as e:
            logger.error(
                "Failed to retrieve active workflows",
                extra={
                    "team_id": str(team_id) if team_id else None,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def _get_by_name(self, name: str) -> Optional[WorkflowDefinition]:
        """Get workflow definition by name."""
        query = select(self.model).where(self.model.name == name)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()


class JobDependencyRepository(BaseRepository[JobDependency]):
    """
    Repository for job dependency relationships and resolution.

    Provides comprehensive dependency management with optimized queries for
    dependency resolution, circular dependency detection, and execution ordering.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(JobDependency, db_session)

    async def create_dependency(
        self,
        dependency_data: JobDependencyCreate,
        correlation_id: Optional[str] = None
    ) -> JobDependency:
        """
        Create a new job dependency.

        Args:
            dependency_data: Dependency creation data
            correlation_id: Request correlation ID

        Returns:
            Created job dependency

        Raises:
            ValidationError: If dependency data is invalid
            ConflictError: If circular dependency detected
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Check for circular dependencies
            if await self._would_create_cycle(
                dependency_data.dependent_job_id,
                dependency_data.prerequisite_job_id
            ):
                raise ConflictError("Creating this dependency would create a circular dependency")

            # Create dependency
            dependency = JobDependency(
                dependent_job_id=dependency_data.dependent_job_id,
                prerequisite_job_id=dependency_data.prerequisite_job_id,
                dependency_type=dependency_data.dependency_type,
                condition_expression=dependency_data.condition_expression,
                is_optional=dependency_data.is_optional,
                timeout_seconds=dependency_data.timeout_seconds,
                dependency_metadata=dependency_data.dependency_metadata
            )

            created_dependency = await self.create(dependency)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="dependency_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Job dependency created successfully",
                extra={
                    "dependency_id": str(created_dependency.id),
                    "dependent_job": str(dependency_data.dependent_job_id),
                    "prerequisite_job": str(dependency_data.prerequisite_job_id),
                    "dependency_type": dependency_data.dependency_type.value,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return created_dependency

        except ConflictError:
            raise
        except Exception as e:
            logger.error(
                "Failed to create job dependency",
                extra={
                    "dependent_job": str(dependency_data.dependent_job_id),
                    "prerequisite_job": str(dependency_data.prerequisite_job_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create job dependency: {str(e)}")

    async def get_job_dependencies(
        self,
        job_id: UUID,
        dependency_type: Optional[DependencyType] = None,
        correlation_id: Optional[str] = None
    ) -> List[JobDependency]:
        """
        Get all dependencies for a specific job.

        Args:
            job_id: Job ID to get dependencies for
            dependency_type: Optional dependency type filter
            correlation_id: Request correlation ID

        Returns:
            List of job dependencies
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.dependent_job_id == job_id
            ).options(
                joinedload(self.model.prerequisite_job),
                joinedload(self.model.dependent_job)
            )

            if dependency_type:
                query = query.where(self.model.dependency_type == dependency_type)

            query = query.order_by(self.model.created_at)

            result = await self.session.execute(query)
            dependencies = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="dependency_get_for_job",
                execution_time_ms=execution_time,
                record_count=len(dependencies),
                correlation_id=correlation_id
            )

            logger.info(
                "Job dependencies retrieved",
                extra={
                    "job_id": str(job_id),
                    "dependency_count": len(dependencies),
                    "dependency_type": dependency_type.value if dependency_type else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return dependencies

        except Exception as e:
            logger.error(
                "Failed to retrieve job dependencies",
                extra={
                    "job_id": str(job_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def resolve_execution_order(
        self,
        workflow_id: UUID,
        correlation_id: Optional[str] = None
    ) -> List[UUID]:
        """
        Resolve execution order for workflow jobs based on dependencies.

        Args:
            workflow_id: Workflow ID to resolve execution order for
            correlation_id: Request correlation ID

        Returns:
            List of job IDs in execution order

        Raises:
            ValidationError: If circular dependencies detected
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Get all workflow steps and their dependencies
            steps_query = select(WorkflowStep).where(
                WorkflowStep.workflow_definition_id == workflow_id
            ).order_by(WorkflowStep.step_order)

            steps_result = await self.session.execute(steps_query)
            steps = steps_result.scalars().all()

            if not steps:
                return []

            # Get dependencies for all steps
            step_ids = [step.id for step in steps]
            deps_query = select(self.model).where(
                self.model.dependent_job_id.in_(step_ids)
            )

            deps_result = await self.session.execute(deps_query)
            dependencies = deps_result.scalars().all()

            # Build dependency graph
            dependency_graph = {}
            for step in steps:
                dependency_graph[step.id] = []

            for dep in dependencies:
                if dep.dependent_job_id in dependency_graph:
                    dependency_graph[dep.dependent_job_id].append(dep.prerequisite_job_id)

            # Topological sort for execution order
            execution_order = await self._topological_sort(dependency_graph)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="dependency_resolve_order",
                execution_time_ms=execution_time,
                record_count=len(execution_order),
                correlation_id=correlation_id
            )

            logger.info(
                "Execution order resolved",
                extra={
                    "workflow_id": str(workflow_id),
                    "step_count": len(steps),
                    "dependency_count": len(dependencies),
                    "execution_order_count": len(execution_order),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return execution_order

        except Exception as e:
            logger.error(
                "Failed to resolve execution order",
                extra={
                    "workflow_id": str(workflow_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to resolve execution order: {str(e)}")

    async def _would_create_cycle(
        self,
        dependent_job_id: UUID,
        prerequisite_job_id: UUID
    ) -> bool:
        """
        Check if adding a dependency would create a circular dependency.

        Args:
            dependent_job_id: Job that depends on prerequisite
            prerequisite_job_id: Job that is prerequisite

        Returns:
            True if cycle would be created, False otherwise
        """
        # Check if prerequisite_job_id depends on dependent_job_id (directly or indirectly)
        visited = set()

        async def has_path(from_job: UUID, to_job: UUID) -> bool:
            if from_job == to_job:
                return True

            if from_job in visited:
                return False

            visited.add(from_job)

            # Get dependencies of from_job
            deps_query = select(self.model).where(
                self.model.dependent_job_id == from_job
            )
            deps_result = await self.session.execute(deps_query)
            dependencies = deps_result.scalars().all()

            for dep in dependencies:
                if await has_path(dep.prerequisite_job_id, to_job):
                    return True

            return False

        return await has_path(prerequisite_job_id, dependent_job_id)

    async def _topological_sort(self, dependency_graph: Dict[UUID, List[UUID]]) -> List[UUID]:
        """
        Perform topological sort on dependency graph.

        Args:
            dependency_graph: Graph of job dependencies

        Returns:
            List of job IDs in topological order

        Raises:
            ValidationError: If circular dependency detected
        """
        # Calculate in-degrees
        in_degree = {job_id: 0 for job_id in dependency_graph}

        for job_id, deps in dependency_graph.items():
            for dep in deps:
                if dep in in_degree:
                    in_degree[dep] += 1

        # Find jobs with no dependencies
        queue = [job_id for job_id, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # Remove current job from dependencies
            for dep in dependency_graph.get(current, []):
                if dep in in_degree:
                    in_degree[dep] -= 1
                    if in_degree[dep] == 0:
                        queue.append(dep)

        # Check for circular dependencies
        if len(result) != len(dependency_graph):
            raise ValidationError("Circular dependency detected in workflow")

        return result


class WorkflowStepRepository(BaseRepository[WorkflowStep]):
    """
    Repository for individual workflow step management.

    Provides comprehensive step management with execution tracking,
    conditional logic evaluation, and performance monitoring.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(WorkflowStep, db_session)

    async def create_workflow_step(
        self,
        step_data: WorkflowStepCreate,
        correlation_id: Optional[str] = None
    ) -> WorkflowStep:
        """
        Create a new workflow step.

        Args:
            step_data: Step creation data
            correlation_id: Request correlation ID

        Returns:
            Created workflow step

        Raises:
            ValidationError: If step data is invalid
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Create workflow step
            step = WorkflowStep(
                workflow_definition_id=step_data.workflow_definition_id,
                name=step_data.name,
                description=step_data.description,
                step_type=step_data.step_type,
                step_order=step_data.step_order,
                task_name=step_data.task_name,
                task_parameters=step_data.task_parameters,
                timeout_seconds=step_data.timeout_seconds,
                max_retries=step_data.max_retries,
                retry_delay=step_data.retry_delay,
                condition_expression=step_data.condition_expression,
                skip_on_failure=step_data.skip_on_failure,
                continue_on_failure=step_data.continue_on_failure,
                depends_on_steps=step_data.depends_on_steps,
                tags=step_data.tags,
                step_metadata=step_data.step_metadata
            )

            created_step = await self.create(step)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_step_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Workflow step created successfully",
                extra={
                    "step_id": str(created_step.id),
                    "workflow_id": str(step_data.workflow_definition_id),
                    "step_name": step_data.name,
                    "step_order": step_data.step_order,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return created_step

        except Exception as e:
            logger.error(
                "Failed to create workflow step",
                extra={
                    "workflow_id": str(step_data.workflow_definition_id),
                    "step_name": step_data.name,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create workflow step: {str(e)}")

    async def get_workflow_steps(
        self,
        workflow_id: UUID,
        correlation_id: Optional[str] = None
    ) -> List[WorkflowStep]:
        """
        Get all steps for a workflow ordered by step_order.

        Args:
            workflow_id: Workflow definition ID
            correlation_id: Request correlation ID

        Returns:
            List of workflow steps in execution order
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.workflow_definition_id == workflow_id
            ).options(
                selectinload(self.model.step_executions)
            ).order_by(self.model.step_order)

            result = await self.session.execute(query)
            steps = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_steps_get",
                execution_time_ms=execution_time,
                record_count=len(steps),
                correlation_id=correlation_id
            )

            logger.info(
                "Workflow steps retrieved",
                extra={
                    "workflow_id": str(workflow_id),
                    "step_count": len(steps),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return steps

        except Exception as e:
            logger.error(
                "Failed to retrieve workflow steps",
                extra={
                    "workflow_id": str(workflow_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def get_executable_steps(
        self,
        workflow_id: UUID,
        completed_steps: List[UUID],
        correlation_id: Optional[str] = None
    ) -> List[WorkflowStep]:
        """
        Get steps that are ready for execution based on dependencies.

        Args:
            workflow_id: Workflow definition ID
            completed_steps: List of completed step IDs
            correlation_id: Request correlation ID

        Returns:
            List of steps ready for execution
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Get all workflow steps
            all_steps = await self.get_workflow_steps(workflow_id, correlation_id)

            executable_steps = []

            for step in all_steps:
                # Skip if already completed
                if step.id in completed_steps:
                    continue

                # Check if all dependencies are satisfied
                if step.depends_on_steps:
                    dependencies_satisfied = all(
                        dep_step_name in [s.name for s in all_steps if s.id in completed_steps]
                        for dep_step_name in step.depends_on_steps
                    )
                    if not dependencies_satisfied:
                        continue

                executable_steps.append(step)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_steps_get_executable",
                execution_time_ms=execution_time,
                record_count=len(executable_steps),
                correlation_id=correlation_id
            )

            logger.info(
                "Executable workflow steps identified",
                extra={
                    "workflow_id": str(workflow_id),
                    "total_steps": len(all_steps),
                    "completed_steps": len(completed_steps),
                    "executable_steps": len(executable_steps),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return executable_steps

        except Exception as e:
            logger.error(
                "Failed to get executable workflow steps",
                extra={
                    "workflow_id": str(workflow_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise


class JobScheduleRepository(BaseRepository[JobSchedule]):
    """
    Repository for advanced scheduling configurations.

    Provides comprehensive scheduling management with cron support,
    timezone handling, and schedule optimization.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(JobSchedule, db_session)

    async def create_schedule(
        self,
        schedule_data: JobScheduleCreate,
        correlation_id: Optional[str] = None
    ) -> JobSchedule:
        """
        Create a new job schedule.

        Args:
            schedule_data: Schedule creation data
            correlation_id: Request correlation ID

        Returns:
            Created job schedule

        Raises:
            ValidationError: If schedule data is invalid
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Create job schedule
            schedule = JobSchedule(
                workflow_definition_id=schedule_data.workflow_definition_id,
                name=schedule_data.name,
                description=schedule_data.description,
                schedule_type=schedule_data.schedule_type,
                cron_expression=schedule_data.cron_expression,
                interval_seconds=schedule_data.interval_seconds,
                start_date=schedule_data.start_date,
                end_date=schedule_data.end_date,
                timezone=schedule_data.timezone,
                is_active=getattr(schedule_data, 'is_active', True),  # Default to True if not provided
                max_runs=schedule_data.max_runs,
                run_count=0,
                schedule_metadata=schedule_data.schedule_metadata
            )

            created_schedule = await self.create(schedule)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="job_schedule_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Job schedule created successfully",
                extra={
                    "schedule_id": str(created_schedule.id),
                    "workflow_id": str(schedule_data.workflow_definition_id),
                    "schedule_name": schedule_data.name,
                    "schedule_type": schedule_data.schedule_type.value,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return created_schedule

        except Exception as e:
            logger.error(
                "Failed to create job schedule",
                extra={
                    "workflow_id": str(schedule_data.workflow_definition_id),
                    "schedule_name": schedule_data.name,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create job schedule: {str(e)}")

    async def get_active_schedules(
        self,
        correlation_id: Optional[str] = None
    ) -> List[JobSchedule]:
        """
        Get all active job schedules.

        Args:
            correlation_id: Request correlation ID

        Returns:
            List of active job schedules
        """
        start_time = datetime.now(timezone.utc)

        try:
            current_time = datetime.now(timezone.utc)

            query = select(self.model).where(
                and_(
                    self.model.is_active == True,
                    or_(
                        self.model.start_date.is_(None),
                        self.model.start_date <= current_time
                    ),
                    or_(
                        self.model.end_date.is_(None),
                        self.model.end_date >= current_time
                    ),
                    or_(
                        self.model.max_runs.is_(None),
                        self.model.run_count < self.model.max_runs
                    )
                )
            ).options(
                joinedload(self.model.workflow_definition)
            ).order_by(self.model.next_run_time)

            result = await self.session.execute(query)
            schedules = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="job_schedule_get_active",
                execution_time_ms=execution_time,
                record_count=len(schedules),
                correlation_id=correlation_id
            )

            logger.info(
                "Active job schedules retrieved",
                extra={
                    "schedule_count": len(schedules),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return schedules

        except Exception as e:
            logger.error(
                "Failed to retrieve active job schedules",
                extra={
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def get_due_schedules(
        self,
        correlation_id: Optional[str] = None
    ) -> List[JobSchedule]:
        """
        Get schedules that are due for execution.

        Args:
            correlation_id: Request correlation ID

        Returns:
            List of schedules due for execution
        """
        start_time = datetime.now(timezone.utc)

        try:
            current_time = datetime.now(timezone.utc)

            query = select(self.model).where(
                and_(
                    self.model.is_active == True,
                    self.model.next_run_time <= current_time,
                    or_(
                        self.model.max_runs.is_(None),
                        self.model.run_count < self.model.max_runs
                    )
                )
            ).options(
                joinedload(self.model.workflow_definition)
            ).order_by(self.model.next_run_time)

            result = await self.session.execute(query)
            schedules = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="job_schedule_get_due",
                execution_time_ms=execution_time,
                record_count=len(schedules),
                correlation_id=correlation_id
            )

            logger.info(
                "Due job schedules retrieved",
                extra={
                    "schedule_count": len(schedules),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return schedules

        except Exception as e:
            logger.error(
                "Failed to retrieve due job schedules",
                extra={
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def update_schedule_run_count(
        self,
        schedule_id: UUID,
        next_run_time: Optional[datetime] = None,
        correlation_id: Optional[str] = None
    ) -> JobSchedule:
        """
        Update schedule run count and next run time.

        Args:
            schedule_id: Schedule ID
            next_run_time: Next scheduled run time
            correlation_id: Request correlation ID

        Returns:
            Updated job schedule

        Raises:
            NotFoundError: If schedule not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            schedule = await self.get_by_id(schedule_id)
            if not schedule:
                raise NotFoundError(f"Job schedule with ID {schedule_id} not found")

            # Update run count and next run time
            schedule.run_count += 1
            schedule.last_run_time = datetime.now(timezone.utc)
            if next_run_time:
                schedule.next_run_time = next_run_time

            updated_schedule = await self.update(schedule_id, schedule)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="job_schedule_update_run_count",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Job schedule run count updated",
                extra={
                    "schedule_id": str(schedule_id),
                    "run_count": schedule.run_count,
                    "next_run_time": next_run_time.isoformat() if next_run_time else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return updated_schedule

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to update schedule run count",
                extra={
                    "schedule_id": str(schedule_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to update schedule run count: {str(e)}")


class WorkflowAlertRepository(BaseRepository[WorkflowAlert]):
    """
    Repository for monitoring and alerting management.

    Provides comprehensive alert management with severity levels,
    channel configuration, and notification tracking.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(WorkflowAlert, db_session)

    async def create_alert(
        self,
        alert_data: WorkflowAlertCreate,
        correlation_id: Optional[str] = None
    ) -> WorkflowAlert:
        """
        Create a new workflow alert.

        Args:
            alert_data: Alert creation data
            correlation_id: Request correlation ID

        Returns:
            Created workflow alert

        Raises:
            ValidationError: If alert data is invalid
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Create workflow alert
            alert = WorkflowAlert(
                workflow_definition_id=alert_data.workflow_definition_id,
                name=alert_data.name,
                description=alert_data.description,
                alert_type=alert_data.alert_type,
                severity=alert_data.severity,
                condition_expression=alert_data.condition_expression,
                channels=alert_data.channels,
                is_active=alert_data.is_active,
                cooldown_minutes=alert_data.cooldown_minutes,
                max_alerts_per_hour=alert_data.max_alerts_per_hour,
                alert_metadata=alert_data.alert_metadata
            )

            created_alert = await self.create(alert)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_alert_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Workflow alert created successfully",
                extra={
                    "alert_id": str(created_alert.id),
                    "workflow_id": str(alert_data.workflow_definition_id),
                    "alert_name": alert_data.name,
                    "severity": alert_data.severity.value,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return created_alert

        except Exception as e:
            logger.error(
                "Failed to create workflow alert",
                extra={
                    "workflow_id": str(alert_data.workflow_definition_id),
                    "alert_name": alert_data.name,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create workflow alert: {str(e)}")

    async def get_active_alerts(
        self,
        workflow_id: Optional[UUID] = None,
        severity: Optional[AlertSeverity] = None,
        correlation_id: Optional[str] = None
    ) -> List[WorkflowAlert]:
        """
        Get active workflow alerts.

        Args:
            workflow_id: Optional workflow filter
            severity: Optional severity filter
            correlation_id: Request correlation ID

        Returns:
            List of active workflow alerts
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.is_active == True
            ).options(
                joinedload(self.model.workflow_definition)
            )

            if workflow_id:
                query = query.where(self.model.workflow_definition_id == workflow_id)

            if severity:
                query = query.where(self.model.severity == severity)

            query = query.order_by(desc(self.model.severity), self.model.created_at)

            result = await self.session.execute(query)
            alerts = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_alert_get_active",
                execution_time_ms=execution_time,
                record_count=len(alerts),
                correlation_id=correlation_id
            )

            logger.info(
                "Active workflow alerts retrieved",
                extra={
                    "alert_count": len(alerts),
                    "workflow_id": str(workflow_id) if workflow_id else None,
                    "severity": severity.value if severity else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return alerts

        except Exception as e:
            logger.error(
                "Failed to retrieve active workflow alerts",
                extra={
                    "workflow_id": str(workflow_id) if workflow_id else None,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def update_alert_counts(
        self,
        alert_id: UUID,
        correlation_id: Optional[str] = None
    ) -> WorkflowAlert:
        """
        Update alert trigger count and last triggered time.

        Args:
            alert_id: Alert ID
            correlation_id: Request correlation ID

        Returns:
            Updated workflow alert

        Raises:
            NotFoundError: If alert not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            alert = await self.get_by_id(alert_id)
            if not alert:
                raise NotFoundError(f"Workflow alert with ID {alert_id} not found")

            # Update trigger count and last triggered time
            alert.trigger_count += 1
            alert.last_triggered_at = datetime.now(timezone.utc)

            updated_alert = await self.update(alert_id, alert)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_alert_update_counts",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Workflow alert counts updated",
                extra={
                    "alert_id": str(alert_id),
                    "trigger_count": alert.trigger_count,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return updated_alert

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to update alert counts",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to update alert counts: {str(e)}")

    async def check_alert_rate_limits(
        self,
        alert_id: UUID,
        correlation_id: Optional[str] = None
    ) -> bool:
        """
        Check if alert is within rate limits.

        Args:
            alert_id: Alert ID
            correlation_id: Request correlation ID

        Returns:
            True if alert can be triggered, False if rate limited

        Raises:
            NotFoundError: If alert not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            alert = await self.get_by_id(alert_id)
            if not alert:
                raise NotFoundError(f"Workflow alert with ID {alert_id} not found")

            current_time = datetime.now(timezone.utc)

            # Check cooldown period
            if alert.last_triggered_at and alert.cooldown_minutes:
                cooldown_end = alert.last_triggered_at + timedelta(minutes=alert.cooldown_minutes)
                if current_time < cooldown_end:
                    logger.info(
                        "Alert in cooldown period",
                        extra={
                            "alert_id": str(alert_id),
                            "cooldown_end": cooldown_end.isoformat(),
                            "correlation_id": correlation_id
                        }
                    )
                    return False

            # Check hourly rate limit
            if alert.max_alerts_per_hour:
                one_hour_ago = current_time - timedelta(hours=1)
                # This would require tracking individual alert triggers in a separate table
                # For now, we'll use a simple check based on trigger count and time
                if alert.last_triggered_at and alert.last_triggered_at > one_hour_ago:
                    # Simple rate limiting - would need more sophisticated tracking in production
                    pass

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="workflow_alert_check_rate_limits",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            return True

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to check alert rate limits",
                extra={
                    "alert_id": str(alert_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to check alert rate limits: {str(e)}")


class JobMonitoringSessionRepository(BaseRepository[JobMonitoringSession]):
    """
    Repository for real-time job monitoring sessions.

    Provides comprehensive monitoring session management with heartbeat tracking,
    performance metrics collection, and real-time status updates for workflow executions.
    Enhanced for Task 6.2.2 Phase 4 monitoring capabilities.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(JobMonitoringSession, db_session)

    async def create_monitoring_session(
        self,
        workflow_execution_id: UUID,
        heartbeat_interval_seconds: int = 30,
        metrics_collection_enabled: bool = True,
        correlation_id: Optional[str] = None
    ) -> JobMonitoringSession:
        """
        Create a new job monitoring session.

        Args:
            workflow_execution_id: Workflow execution ID to monitor
            heartbeat_interval_seconds: Heartbeat interval in seconds
            metrics_collection_enabled: Whether to collect performance metrics
            correlation_id: Request correlation ID

        Returns:
            Created job monitoring session

        Raises:
            ValidationError: If session data is invalid
            ConflictError: If monitoring session already exists
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Check if monitoring session already exists
            existing_query = select(self.model).where(
                self.model.workflow_execution_id == workflow_execution_id
            )
            existing_result = await self.session.execute(existing_query)
            existing_session = existing_result.scalar_one_or_none()

            if existing_session:
                raise ConflictError(f"Monitoring session already exists for workflow execution {workflow_execution_id}")

            # Create monitoring session
            session = JobMonitoringSession(
                workflow_execution_id=workflow_execution_id,
                monitoring_status=MonitoringStatus.ACTIVE,
                heartbeat_interval_seconds=heartbeat_interval_seconds,
                metrics_collection_enabled=metrics_collection_enabled,
                progress_percentage=0.0,
                last_heartbeat=datetime.now(timezone.utc),
                monitoring_started_at=datetime.now(timezone.utc)
            )

            self.session.add(session)
            await self.session.flush()
            await self.session.refresh(session)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="monitoring_session_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Job monitoring session created",
                extra={
                    "session_id": str(session.id),
                    "workflow_execution_id": str(workflow_execution_id),
                    "heartbeat_interval": heartbeat_interval_seconds,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return session

        except ConflictError:
            raise
        except Exception as e:
            logger.error(
                "Failed to create monitoring session",
                extra={
                    "workflow_execution_id": str(workflow_execution_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create monitoring session: {str(e)}")

    async def update_heartbeat(
        self,
        session_id: UUID,
        current_step: Optional[str] = None,
        progress_percentage: Optional[float] = None,
        performance_metrics: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ) -> JobMonitoringSession:
        """
        Update monitoring session heartbeat and metrics.

        Args:
            session_id: Monitoring session ID
            current_step: Current executing step
            progress_percentage: Execution progress (0-100)
            performance_metrics: Performance metrics data
            correlation_id: Request correlation ID

        Returns:
            Updated monitoring session

        Raises:
            NotFoundError: If session not found
            ValidationError: If update fails
        """
        start_time = datetime.now(timezone.utc)

        try:
            session = await self.get_by_id(session_id)
            if not session:
                raise NotFoundError(f"Monitoring session with ID {session_id} not found")

            # Update heartbeat and metrics
            session.last_heartbeat = datetime.now(timezone.utc)

            if current_step is not None:
                session.current_step = current_step

            if progress_percentage is not None:
                session.progress_percentage = min(100.0, max(0.0, progress_percentage))

            if performance_metrics:
                session.cpu_usage_percent = performance_metrics.get('cpu_usage_percent')
                session.memory_usage_mb = performance_metrics.get('memory_usage_mb')
                session.execution_latency_ms = performance_metrics.get('execution_latency_ms')

            await self.session.flush()
            await self.session.refresh(session)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="monitoring_session_update_heartbeat",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.debug(
                "Monitoring session heartbeat updated",
                extra={
                    "session_id": str(session_id),
                    "current_step": current_step,
                    "progress": progress_percentage,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return session

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to update monitoring session heartbeat",
                extra={
                    "session_id": str(session_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to update heartbeat: {str(e)}")

    async def get_active_sessions(
        self,
        workflow_execution_id: Optional[UUID] = None,
        correlation_id: Optional[str] = None
    ) -> List[JobMonitoringSession]:
        """
        Get active monitoring sessions.

        Args:
            workflow_execution_id: Optional workflow execution filter
            correlation_id: Request correlation ID

        Returns:
            List of active monitoring sessions
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.monitoring_status == MonitoringStatus.ACTIVE
            ).options(
                joinedload(self.model.workflow_execution)
            )

            if workflow_execution_id:
                query = query.where(self.model.workflow_execution_id == workflow_execution_id)

            query = query.order_by(desc(self.model.last_heartbeat))

            result = await self.session.execute(query)
            sessions = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="monitoring_session_get_active",
                execution_time_ms=execution_time,
                record_count=len(sessions),
                correlation_id=correlation_id
            )

            logger.info(
                "Active monitoring sessions retrieved",
                extra={
                    "session_count": len(sessions),
                    "workflow_execution_id": str(workflow_execution_id) if workflow_execution_id else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return sessions

        except Exception as e:
            logger.error(
                "Failed to retrieve active monitoring sessions",
                extra={
                    "workflow_execution_id": str(workflow_execution_id) if workflow_execution_id else None,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise

    async def end_monitoring_session(
        self,
        session_id: UUID,
        correlation_id: Optional[str] = None
    ) -> JobMonitoringSession:
        """
        End a monitoring session.

        Args:
            session_id: Monitoring session ID
            correlation_id: Request correlation ID

        Returns:
            Updated monitoring session

        Raises:
            NotFoundError: If session not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            session = await self.get_by_id(session_id)
            if not session:
                raise NotFoundError(f"Monitoring session with ID {session_id} not found")

            session.monitoring_status = MonitoringStatus.DISABLED
            session.monitoring_ended_at = datetime.now(timezone.utc)

            await self.session.flush()
            await self.session.refresh(session)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="monitoring_session_end",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Monitoring session ended",
                extra={
                    "session_id": str(session_id),
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return session

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to end monitoring session",
                extra={
                    "session_id": str(session_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to end monitoring session: {str(e)}")


class AlertTriggerEventRepository(BaseRepository[AlertTriggerEvent]):
    """
    Repository for alert trigger events and delivery tracking.

    Provides comprehensive alert trigger management with escalation tracking,
    delivery status monitoring, and resolution management for the alerting system.
    Enhanced for Task 6.2.2 Phase 4 monitoring capabilities.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(AlertTriggerEvent, db_session)

    async def create_trigger_event(
        self,
        alert_id: UUID,
        trigger_condition: str,
        alert_message: str,
        severity: AlertSeverity,
        workflow_execution_id: Optional[UUID] = None,
        trigger_value: Optional[str] = None,
        threshold_value: Optional[str] = None,
        alert_context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None
    ) -> AlertTriggerEvent:
        """
        Create a new alert trigger event.

        Args:
            alert_id: Alert configuration ID
            trigger_condition: Condition that triggered the alert
            alert_message: Alert message content
            severity: Alert severity level
            workflow_execution_id: Optional related workflow execution
            trigger_value: Value that triggered the alert
            threshold_value: Configured threshold value
            alert_context: Additional alert context
            correlation_id: Request correlation ID

        Returns:
            Created alert trigger event

        Raises:
            ValidationError: If trigger event data is invalid
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Create trigger event
            trigger_event = AlertTriggerEvent(
                alert_id=alert_id,
                workflow_execution_id=workflow_execution_id,
                trigger_condition=trigger_condition,
                trigger_value=trigger_value,
                threshold_value=threshold_value,
                severity=severity,
                alert_message=alert_message,
                alert_context=alert_context or {},
                is_resolved=False,
                escalation_level=0,
                notification_attempts=0,
                successful_deliveries=0,
                failed_deliveries=0,
                triggered_at=datetime.now(timezone.utc)
            )

            self.session.add(trigger_event)
            await self.session.flush()
            await self.session.refresh(trigger_event)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_trigger_event_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Alert trigger event created",
                extra={
                    "trigger_event_id": str(trigger_event.id),
                    "alert_id": str(alert_id),
                    "severity": severity.value,
                    "trigger_condition": trigger_condition,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return trigger_event

        except Exception as e:
            logger.error(
                "Failed to create alert trigger event",
                extra={
                    "alert_id": str(alert_id),
                    "trigger_condition": trigger_condition,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create trigger event: {str(e)}")

    async def update_delivery_status(
        self,
        trigger_event_id: UUID,
        successful_deliveries: int = 0,
        failed_deliveries: int = 0,
        correlation_id: Optional[str] = None
    ) -> AlertTriggerEvent:
        """
        Update alert trigger event delivery status.

        Args:
            trigger_event_id: Trigger event ID
            successful_deliveries: Number of successful deliveries to add
            failed_deliveries: Number of failed deliveries to add
            correlation_id: Request correlation ID

        Returns:
            Updated trigger event

        Raises:
            NotFoundError: If trigger event not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            trigger_event = await self.get_by_id(trigger_event_id)
            if not trigger_event:
                raise NotFoundError(f"Alert trigger event with ID {trigger_event_id} not found")

            # Update delivery counts
            trigger_event.notification_attempts += (successful_deliveries + failed_deliveries)
            trigger_event.successful_deliveries += successful_deliveries
            trigger_event.failed_deliveries += failed_deliveries

            await self.session.flush()
            await self.session.refresh(trigger_event)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_trigger_event_update_delivery",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.debug(
                "Alert trigger event delivery status updated",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "successful_deliveries": successful_deliveries,
                    "failed_deliveries": failed_deliveries,
                    "total_attempts": trigger_event.notification_attempts,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return trigger_event

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to update trigger event delivery status",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to update delivery status: {str(e)}")

    async def get_unresolved_events(
        self,
        alert_id: Optional[UUID] = None,
        severity: Optional[AlertSeverity] = None,
        correlation_id: Optional[str] = None
    ) -> List[AlertTriggerEvent]:
        """
        Get unresolved alert trigger events.

        Args:
            alert_id: Optional alert filter
            severity: Optional severity filter
            correlation_id: Request correlation ID

        Returns:
            List of unresolved trigger events
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                self.model.is_resolved == False
            ).options(
                joinedload(self.model.alert),
                selectinload(self.model.delivery_statuses)
            )

            if alert_id:
                query = query.where(self.model.alert_id == alert_id)

            if severity:
                query = query.where(self.model.severity == severity)

            query = query.order_by(desc(self.model.triggered_at))

            result = await self.session.execute(query)
            events = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_trigger_event_get_unresolved",
                execution_time_ms=execution_time,
                record_count=len(events),
                correlation_id=correlation_id
            )

            logger.info(
                "Unresolved alert trigger events retrieved",
                extra={
                    "event_count": len(events),
                    "alert_id": str(alert_id) if alert_id else None,
                    "severity": severity.value if severity else None,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return events

        except Exception as e:
            logger.error(
                "Failed to retrieve unresolved trigger events",
                extra={
                    "alert_id": str(alert_id) if alert_id else None,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise


class AlertDeliveryRecordRepository(BaseRepository[AlertDeliveryRecord]):
    """
    Repository for alert delivery records and retry management.

    Provides comprehensive delivery tracking with retry logic, performance metrics,
    and multi-channel notification management for the alerting system.
    Enhanced for Task 6.2.2 Phase 4 monitoring capabilities.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize repository with database session."""
        super().__init__(AlertDeliveryRecord, db_session)

    async def create_delivery_record(
        self,
        trigger_event_id: UUID,
        notification_channel: str,
        recipient_address: str,
        max_retry_attempts: int = 3,
        correlation_id: Optional[str] = None
    ) -> AlertDeliveryRecord:
        """
        Create a new alert delivery record.

        Args:
            trigger_event_id: Alert trigger event ID
            notification_channel: Delivery channel (email, slack, webhook, sms, push)
            recipient_address: Recipient address/endpoint
            max_retry_attempts: Maximum retry attempts
            correlation_id: Request correlation ID

        Returns:
            Created delivery record

        Raises:
            ValidationError: If delivery record data is invalid
        """
        start_time = datetime.now(timezone.utc)

        try:
            # Create delivery record
            delivery_record = AlertDeliveryRecord(
                trigger_event_id=trigger_event_id,
                notification_channel=notification_channel,
                recipient_address=recipient_address,
                delivery_status=AlertDeliveryStatus.PENDING,
                attempt_count=1,
                max_retry_attempts=max_retry_attempts,
                scheduled_at=datetime.now(timezone.utc)
            )

            self.session.add(delivery_record)
            await self.session.flush()
            await self.session.refresh(delivery_record)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_delivery_record_create",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Alert delivery record created",
                extra={
                    "delivery_record_id": str(delivery_record.id),
                    "trigger_event_id": str(trigger_event_id),
                    "channel": notification_channel,
                    "recipient": recipient_address,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return delivery_record

        except Exception as e:
            logger.error(
                "Failed to create delivery record",
                extra={
                    "trigger_event_id": str(trigger_event_id),
                    "channel": notification_channel,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to create delivery record: {str(e)}")

    async def update_delivery_status(
        self,
        delivery_record_id: UUID,
        status: AlertDeliveryStatus,
        external_delivery_id: Optional[str] = None,
        external_message_id: Optional[str] = None,
        delivery_response: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        error_code: Optional[str] = None,
        delivery_latency_ms: Optional[float] = None,
        correlation_id: Optional[str] = None
    ) -> AlertDeliveryRecord:
        """
        Update delivery record status and metadata.

        Args:
            delivery_record_id: Delivery record ID
            status: New delivery status
            external_delivery_id: External delivery ID
            external_message_id: External message ID
            delivery_response: Delivery response data
            error_message: Error message if failed
            error_code: Error code if failed
            delivery_latency_ms: Delivery latency in milliseconds
            correlation_id: Request correlation ID

        Returns:
            Updated delivery record

        Raises:
            NotFoundError: If delivery record not found
        """
        start_time = datetime.now(timezone.utc)

        try:
            delivery_record = await self.get_by_id(delivery_record_id)
            if not delivery_record:
                raise NotFoundError(f"Delivery record with ID {delivery_record_id} not found")

            # Update status and metadata
            delivery_record.delivery_status = status

            if external_delivery_id:
                delivery_record.external_delivery_id = external_delivery_id

            if external_message_id:
                delivery_record.external_message_id = external_message_id

            if delivery_response:
                delivery_record.delivery_response = delivery_response

            if error_message:
                delivery_record.error_message = error_message

            if error_code:
                delivery_record.error_code = error_code

            if delivery_latency_ms:
                delivery_record.delivery_latency_ms = delivery_latency_ms

            # Update timestamps based on status
            current_time = datetime.now(timezone.utc)
            if status == AlertDeliveryStatus.SENT:
                delivery_record.sent_at = current_time
            elif status == AlertDeliveryStatus.DELIVERED:
                delivery_record.delivered_at = current_time
            elif status == AlertDeliveryStatus.FAILED:
                delivery_record.failed_at = current_time

            await self.session.flush()
            await self.session.refresh(delivery_record)

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_delivery_record_update_status",
                execution_time_ms=execution_time,
                record_count=1,
                correlation_id=correlation_id
            )

            logger.info(
                "Delivery record status updated",
                extra={
                    "delivery_record_id": str(delivery_record_id),
                    "status": status.value,
                    "channel": delivery_record.notification_channel,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return delivery_record

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                "Failed to update delivery record status",
                extra={
                    "delivery_record_id": str(delivery_record_id),
                    "status": status.value if status else None,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise ValidationError(f"Failed to update delivery status: {str(e)}")

    async def get_pending_deliveries(
        self,
        notification_channel: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> List[AlertDeliveryRecord]:
        """
        Get pending delivery records for processing.

        Args:
            notification_channel: Optional channel filter
            correlation_id: Request correlation ID

        Returns:
            List of pending delivery records
        """
        start_time = datetime.now(timezone.utc)

        try:
            query = select(self.model).where(
                or_(
                    self.model.delivery_status == AlertDeliveryStatus.PENDING,
                    and_(
                        self.model.delivery_status == AlertDeliveryStatus.RETRYING,
                        self.model.next_retry_at <= datetime.now(timezone.utc)
                    )
                )
            ).options(
                joinedload(self.model.trigger_event)
            )

            if notification_channel:
                query = query.where(self.model.notification_channel == notification_channel)

            query = query.order_by(self.model.scheduled_at)

            result = await self.session.execute(query)
            records = result.scalars().all()

            # Track performance metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            await metrics_collector.record_query_performance(
                operation="alert_delivery_record_get_pending",
                execution_time_ms=execution_time,
                record_count=len(records),
                correlation_id=correlation_id
            )

            logger.info(
                "Pending delivery records retrieved",
                extra={
                    "record_count": len(records),
                    "channel": notification_channel,
                    "execution_time_ms": execution_time,
                    "correlation_id": correlation_id
                }
            )

            return records

        except Exception as e:
            logger.error(
                "Failed to retrieve pending delivery records",
                extra={
                    "channel": notification_channel,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            raise