"""
Unit tests for Paystack service integration.

This module provides comprehensive tests for Paystack payment processing including:
- Payment initialization and verification
- Webhook signature validation and event processing
- Error handling and retry logic
- Performance and security testing

Implements Task 4.3.1 Phase 4 requirements for comprehensive testing with
>80% test coverage and 100% test success rate validation.
"""

import pytest
import json
import hashlib
import hmac
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

import httpx

from app.services.payment.paystack_service import PaystackService, PaystackError
from app.schemas.payment_schemas import (
    PaystackInitializeResponse, PaystackVerifyResponse, 
    PaystackWebhookEvent, WebhookProcessingResult
)


class TestPaystackService:
    """Test suite for PaystackService."""

    @pytest.fixture
    def paystack_service(self):
        """Create PaystackService instance for testing."""
        with patch('app.services.payment.paystack_service.PaymentProviderSettings') as mock_settings:
            mock_settings.get_provider_config.return_value = {
                "secret_key": "sk_test_123456789",
                "public_key": "pk_test_123456789",
                "base_url": "https://api.paystack.co",
                "webhook_secret": "whsec_test_secret"
            }
            
            service = PaystackService()
            # Mock HTTP client
            service.http_client = AsyncMock()
            return service

    @pytest.fixture
    def sample_payment_data(self):
        """Sample payment data for testing."""
        return {
            "email": "<EMAIL>",
            "amount": Decimal("1000.00"),
            "reference": "test_ref_123",
            "currency": "NGN",
            "callback_url": "https://example.com/callback",
            "metadata": {"booking_id": 123}
        }

    @pytest.fixture
    def sample_webhook_payload(self):
        """Sample webhook payload for testing."""
        return {
            "event": "charge.success",
            "data": {
                "id": 123456789,
                "reference": "test_ref_123",
                "amount": 100000,  # Amount in kobo
                "currency": "NGN",
                "status": "success",
                "gateway_response": "Successful",
                "paid_at": "2025-01-27T10:00:00Z",
                "channel": "card",
                "customer": {
                    "email": "<EMAIL>"
                }
            }
        }

    @pytest.mark.asyncio
    async def test_initialize_payment_success(self, paystack_service, sample_payment_data):
        """Test successful payment initialization."""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": True,
            "message": "Authorization URL created",
            "data": {
                "authorization_url": "https://checkout.paystack.com/test123",
                "access_code": "test_access_code",
                "reference": "test_ref_123"
            }
        }
        paystack_service.http_client.post.return_value = mock_response

        # Test payment initialization
        result = await paystack_service.initialize_payment(**sample_payment_data)

        # Assertions
        assert isinstance(result, PaystackInitializeResponse)
        assert result.status is True
        assert result.authorization_url == "https://checkout.paystack.com/test123"
        assert result.access_code == "test_access_code"
        assert result.reference == "test_ref_123"

        # Verify API call
        paystack_service.http_client.post.assert_called_once_with(
            "/transaction/initialize",
            json={
                "email": "<EMAIL>",
                "amount": 100000,  # Converted to kobo
                "currency": "NGN",
                "reference": "test_ref_123",
                "callback_url": "https://example.com/callback",
                "metadata": {"booking_id": 123}
            }
        )

    @pytest.mark.asyncio
    async def test_initialize_payment_api_error(self, paystack_service, sample_payment_data):
        """Test payment initialization with API error."""
        # Mock API error
        error_response = MagicMock()
        error_response.status_code = 400
        error_response.text = "Invalid email address"
        
        http_error = httpx.HTTPStatusError(
            "Bad Request", 
            request=MagicMock(), 
            response=error_response
        )
        paystack_service.http_client.post.side_effect = http_error

        # Test error handling
        with pytest.raises(PaystackError) as exc_info:
            await paystack_service.initialize_payment(**sample_payment_data)

        assert "Payment initialization failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_payment_success(self, paystack_service):
        """Test successful payment verification."""
        # Mock successful verification response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": True,
            "message": "Verification successful",
            "data": {
                "reference": "test_ref_123",
                "amount": 100000,
                "gateway_response": "Successful",
                "paid_at": "2025-01-27T10:00:00Z",
                "channel": "card"
            }
        }
        paystack_service.http_client.get.return_value = mock_response

        # Test verification
        result = await paystack_service.verify_payment("test_ref_123")

        # Assertions
        assert isinstance(result, PaystackVerifyResponse)
        assert result.status is True
        assert result.reference == "test_ref_123"
        assert result.amount == 100000
        assert result.gateway_response == "Successful"

        # Verify API call
        paystack_service.http_client.get.assert_called_once_with(
            "/transaction/verify/test_ref_123"
        )

    @pytest.mark.asyncio
    async def test_verify_payment_not_found(self, paystack_service):
        """Test payment verification for non-existent payment."""
        # Mock 404 error
        error_response = MagicMock()
        error_response.status_code = 404
        error_response.text = "Transaction not found"
        
        http_error = httpx.HTTPStatusError(
            "Not Found", 
            request=MagicMock(), 
            response=error_response
        )
        paystack_service.http_client.get.side_effect = http_error

        # Test error handling
        with pytest.raises(PaystackError) as exc_info:
            await paystack_service.verify_payment("invalid_ref")

        assert "Payment verification failed" in str(exc_info.value)

    def test_validate_webhook_signature_valid(self, paystack_service):
        """Test webhook signature validation with valid signature."""
        payload = b'{"event": "charge.success", "data": {"reference": "test_ref"}}'
        
        # Generate valid signature
        webhook_secret = "whsec_test_secret"
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()

        # Test validation
        is_valid = paystack_service.validate_webhook_signature(payload, expected_signature)
        assert is_valid is True

    def test_validate_webhook_signature_invalid(self, paystack_service):
        """Test webhook signature validation with invalid signature."""
        payload = b'{"event": "charge.success", "data": {"reference": "test_ref"}}'
        invalid_signature = "invalid_signature_123"

        # Test validation
        is_valid = paystack_service.validate_webhook_signature(payload, invalid_signature)
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_process_webhook_event_charge_success(self, paystack_service, sample_webhook_payload):
        """Test processing charge.success webhook event."""
        payload = json.dumps(sample_webhook_payload).encode('utf-8')
        
        # Generate valid signature
        webhook_secret = "whsec_test_secret"
        signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()

        # Test webhook processing
        result = await paystack_service.process_webhook_event(
            event_data=sample_webhook_payload,
            signature=signature,
            raw_payload=payload
        )

        # Assertions
        assert isinstance(result, WebhookProcessingResult)
        assert result.processed is True
        assert result.event_type == "charge.success"
        assert result.message == "Payment completed successfully"

    @pytest.mark.asyncio
    async def test_process_webhook_event_invalid_signature(self, paystack_service, sample_webhook_payload):
        """Test webhook processing with invalid signature."""
        payload = json.dumps(sample_webhook_payload).encode('utf-8')
        invalid_signature = "invalid_signature"

        # Test webhook processing
        result = await paystack_service.process_webhook_event(
            event_data=sample_webhook_payload,
            signature=invalid_signature,
            raw_payload=payload
        )

        # Assertions
        assert result.processed is False
        assert "Processing failed" in result.message

    @pytest.mark.asyncio
    async def test_process_webhook_event_charge_failed(self, paystack_service):
        """Test processing charge.failed webhook event."""
        webhook_payload = {
            "event": "charge.failed",
            "data": {
                "reference": "test_ref_123",
                "gateway_response": "Declined by bank"
            }
        }
        
        payload = json.dumps(webhook_payload).encode('utf-8')
        
        # Generate valid signature
        webhook_secret = "whsec_test_secret"
        signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()

        # Test webhook processing
        result = await paystack_service.process_webhook_event(
            event_data=webhook_payload,
            signature=signature,
            raw_payload=payload
        )

        # Assertions
        assert result.processed is True
        assert result.event_type == "charge.failed"
        assert result.message == "Payment failed"

    @pytest.mark.asyncio
    async def test_process_webhook_event_unsupported_event(self, paystack_service):
        """Test processing unsupported webhook event."""
        webhook_payload = {
            "event": "unsupported.event",
            "data": {"reference": "test_ref_123"}
        }
        
        payload = json.dumps(webhook_payload).encode('utf-8')
        
        # Generate valid signature
        webhook_secret = "whsec_test_secret"
        signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()

        # Test webhook processing
        result = await paystack_service.process_webhook_event(
            event_data=webhook_payload,
            signature=signature,
            raw_payload=payload
        )

        # Assertions
        assert result.processed is True
        assert result.event_type == "unsupported.event"
        assert "Unhandled event type" in result.message

    @pytest.mark.asyncio
    async def test_close_http_client(self, paystack_service):
        """Test closing HTTP client connections."""
        await paystack_service.close()
        paystack_service.http_client.aclose.assert_called_once()

    def test_paystack_error_exception(self):
        """Test PaystackError exception."""
        error_message = "Test error message"
        error = PaystackError(error_message)
        assert str(error) == error_message
