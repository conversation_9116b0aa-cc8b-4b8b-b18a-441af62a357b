"""
CDN Optimization models for Culture Connect Backend API.

This module defines comprehensive CDN optimization and asset delivery models including:
- CDNConfiguration: CDN provider configuration and settings management
- AssetOptimization: Asset optimization tracking and performance metrics
- AssetBundle: Asset bundling configuration and optimization results
- CDNMetrics: CDN performance metrics and delivery analytics
- AssetDelivery: Asset delivery tracking and performance monitoring

Implements Phase 7.3.4 requirements for CDN optimization and asset delivery with
production-grade PostgreSQL optimization and seamless integration with
existing Phase 7.3.2 caching infrastructure.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from uuid import uuid4
from typing import Dict, Any, Optional

from sqlalchemy import (
    Column, String, Integer, Boolean, DateTime, Text, DECIMAL,
    ForeignKey, Index, CheckConstraint, UniqueConstraint, JSON
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class AssetType(str, Enum):
    """Asset type enumeration."""
    IMAGE = "image"
    STYLESHEET = "stylesheet"
    JAVASCRIPT = "javascript"
    FONT = "font"
    VIDEO = "video"
    DOCUMENT = "document"
    OTHER = "other"


class OptimizationType(str, Enum):
    """Asset optimization type enumeration."""
    COMPRESSION = "compression"
    MINIFICATION = "minification"
    BUNDLING = "bundling"
    RESIZING = "resizing"
    FORMAT_CONVERSION = "format_conversion"
    CACHE_OPTIMIZATION = "cache_optimization"


class DeliveryStatus(str, Enum):
    """Asset delivery status enumeration."""
    PENDING = "pending"
    OPTIMIZING = "optimizing"
    READY = "ready"
    DELIVERED = "delivered"
    FAILED = "failed"
    EXPIRED = "expired"


class CDNProvider(str, Enum):
    """CDN provider enumeration."""
    CLOUDFLARE = "cloudflare"
    AWS_CLOUDFRONT = "aws_cloudfront"
    AZURE_CDN = "azure_cdn"
    GOOGLE_CDN = "google_cdn"
    CUSTOM = "custom"


class CDNConfiguration(Base):
    """
    CDN configuration model for provider settings and optimization policies.

    Tracks CDN provider configurations, cache policies, and optimization
    settings for different asset types and delivery scenarios.
    """
    __tablename__ = "cdn_configurations"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(100), nullable=False, index=True)
    provider = Column(String(50), nullable=False, index=True)
    base_url = Column(String(255), nullable=False)
    api_key_encrypted = Column(Text)  # Encrypted API key for CDN provider

    # Configuration settings
    cache_ttl_seconds = Column(Integer, nullable=False, default=86400)  # 24 hours
    compression_enabled = Column(Boolean, default=True, index=True)
    minification_enabled = Column(Boolean, default=True)
    bundling_enabled = Column(Boolean, default=True)
    image_optimization_enabled = Column(Boolean, default=True)

    # Performance settings
    max_file_size_mb = Column(DECIMAL(10, 2), default=Decimal("10.0"))
    optimization_quality = Column(Integer, default=85)  # 1-100 quality scale
    supported_formats = Column(JSONB, default=list)
    cache_headers = Column(JSONB, default=dict)

    # Geographic settings
    edge_locations = Column(JSONB, default=list)  # List of edge location codes
    geo_restrictions = Column(JSONB, default=dict)  # Geographic access restrictions

    # Metadata
    is_active = Column(Boolean, default=True, index=True)
    is_default = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    asset_optimizations = relationship("AssetOptimization", back_populates="cdn_configuration")
    asset_bundles = relationship("AssetBundle", back_populates="cdn_configuration")
    cdn_metrics = relationship("CDNMetrics", back_populates="cdn_configuration")

    # Indexes and constraints
    __table_args__ = (
        Index("idx_cdn_config_provider_active", "provider", "is_active"),
        Index("idx_cdn_config_created", "created_at"),
        Index("idx_cdn_config_name_provider", "name", "provider"),
        UniqueConstraint("name", "provider", name="uq_cdn_config_name_provider"),
        CheckConstraint("cache_ttl_seconds > 0", name="ck_cdn_config_ttl_positive"),
        CheckConstraint("max_file_size_mb > 0", name="ck_cdn_config_size_positive"),
        CheckConstraint("optimization_quality >= 1 AND optimization_quality <= 100", name="ck_cdn_config_quality_valid"),
    )


class AssetOptimization(Base):
    """
    Asset optimization model for tracking individual asset optimization processes.

    Records optimization results, performance metrics, and delivery status
    for individual assets processed through the CDN optimization pipeline.
    """
    __tablename__ = "asset_optimizations"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    cdn_configuration_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("cdn_configurations.id"), nullable=False)

    # Asset information
    original_path = Column(String(500), nullable=False, index=True)
    optimized_path = Column(String(500), nullable=False)
    asset_type = Column(String(50), nullable=False, index=True)
    optimization_type = Column(String(50), nullable=False, index=True)

    # Size and performance metrics
    original_size_bytes = Column(Integer, nullable=False)
    optimized_size_bytes = Column(Integer, nullable=False)
    compression_ratio = Column(DECIMAL(5, 2), nullable=False)  # Percentage
    optimization_time_ms = Column(Integer, nullable=False)

    # Delivery information
    cdn_url = Column(String(500))
    cache_headers = Column(JSONB, default=dict)
    delivery_status = Column(String(50), nullable=False, default=DeliveryStatus.PENDING, index=True)

    # Performance tracking
    first_byte_time_ms = Column(Integer)  # Time to first byte
    total_download_time_ms = Column(Integer)  # Total download time
    cache_hit_count = Column(Integer, default=0)
    cache_miss_count = Column(Integer, default=0)

    # Error tracking
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    last_error_at = Column(DateTime(timezone=True))

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True))  # Cache expiration

    # Relationships
    cdn_configuration = relationship("CDNConfiguration", back_populates="asset_optimizations")
    asset_deliveries = relationship("AssetDelivery", back_populates="asset_optimization")

    # Indexes and constraints
    __table_args__ = (
        Index("idx_asset_opt_config_type", "cdn_configuration_id", "asset_type"),
        Index("idx_asset_opt_status_created", "delivery_status", "created_at"),
        Index("idx_asset_opt_path", "original_path"),
        Index("idx_asset_opt_compression", "compression_ratio"),
        Index("idx_asset_opt_performance", "optimization_time_ms", "first_byte_time_ms"),
        CheckConstraint("original_size_bytes >= 0", name="ck_asset_opt_original_size_positive"),
        CheckConstraint("optimized_size_bytes >= 0", name="ck_asset_opt_optimized_size_positive"),
        CheckConstraint("compression_ratio >= 0", name="ck_asset_opt_compression_positive"),
        CheckConstraint("optimization_time_ms >= 0", name="ck_asset_opt_time_positive"),
        CheckConstraint("retry_count >= 0", name="ck_asset_opt_retry_positive"),
    )


class AssetBundle(Base):
    """
    Asset bundle model for tracking bundled asset optimization and delivery.

    Records bundling configurations, optimization results, and performance
    metrics for groups of assets bundled together for improved delivery.
    """
    __tablename__ = "asset_bundles"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    cdn_configuration_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("cdn_configurations.id"), nullable=False)

    # Bundle information
    bundle_name = Column(String(100), nullable=False, index=True)
    bundle_type = Column(String(50), nullable=False, index=True)  # css, js, mixed
    asset_paths = Column(JSONB, nullable=False)  # List of original asset paths
    bundled_path = Column(String(500), nullable=False)

    # Size and performance metrics
    total_original_size_bytes = Column(Integer, nullable=False)
    bundled_size_bytes = Column(Integer, nullable=False)
    compression_ratio = Column(DECIMAL(5, 2), nullable=False)
    bundling_time_ms = Column(Integer, nullable=False)

    # Delivery information
    cdn_url = Column(String(500))
    cache_headers = Column(JSONB, default=dict)
    delivery_status = Column(String(50), nullable=False, default=DeliveryStatus.PENDING, index=True)

    # Performance tracking
    first_byte_time_ms = Column(Integer)
    total_download_time_ms = Column(Integer)
    cache_hit_count = Column(Integer, default=0)
    cache_miss_count = Column(Integer, default=0)

    # Bundle configuration
    minification_enabled = Column(Boolean, default=True)
    source_map_enabled = Column(Boolean, default=False)
    bundle_version = Column(String(50))  # Version hash for cache busting

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True))

    # Relationships
    cdn_configuration = relationship("CDNConfiguration", back_populates="asset_bundles")

    # Indexes and constraints
    __table_args__ = (
        Index("idx_asset_bundle_config_type", "cdn_configuration_id", "bundle_type"),
        Index("idx_asset_bundle_name_version", "bundle_name", "bundle_version"),
        Index("idx_asset_bundle_status_created", "delivery_status", "created_at"),
        Index("idx_asset_bundle_compression", "compression_ratio"),
        Index("idx_asset_bundle_performance", "bundling_time_ms", "first_byte_time_ms"),
        UniqueConstraint("bundle_name", "bundle_version", name="uq_asset_bundle_name_version"),
        CheckConstraint("total_original_size_bytes >= 0", name="ck_asset_bundle_original_size_positive"),
        CheckConstraint("bundled_size_bytes >= 0", name="ck_asset_bundle_bundled_size_positive"),
        CheckConstraint("compression_ratio >= 0", name="ck_asset_bundle_compression_positive"),
        CheckConstraint("bundling_time_ms >= 0", name="ck_asset_bundle_time_positive"),
    )


class CDNMetrics(Base):
    """
    CDN metrics model for tracking CDN performance and delivery analytics.

    Records aggregated performance metrics, cache hit rates, and delivery
    statistics for CDN configurations and asset delivery monitoring.
    """
    __tablename__ = "cdn_metrics"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    cdn_configuration_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("cdn_configurations.id"), nullable=False)

    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)

    # Request metrics
    total_requests = Column(Integer, nullable=False, default=0)
    cache_hits = Column(Integer, nullable=False, default=0)
    cache_misses = Column(Integer, nullable=False, default=0)
    cache_hit_rate = Column(DECIMAL(5, 4), nullable=False, default=Decimal("0.0"))  # 0.0-1.0

    # Performance metrics
    avg_response_time_ms = Column(DECIMAL(10, 2), nullable=False, default=Decimal("0.0"))
    avg_first_byte_time_ms = Column(DECIMAL(10, 2), nullable=False, default=Decimal("0.0"))
    avg_download_time_ms = Column(DECIMAL(10, 2), nullable=False, default=Decimal("0.0"))

    # Bandwidth metrics
    total_bytes_served = Column(Integer, nullable=False, default=0)
    total_bytes_saved = Column(Integer, nullable=False, default=0)  # Through compression
    bandwidth_savings_ratio = Column(DECIMAL(5, 4), nullable=False, default=Decimal("0.0"))

    # Error metrics
    error_count = Column(Integer, nullable=False, default=0)
    error_rate = Column(DECIMAL(5, 4), nullable=False, default=Decimal("0.0"))

    # Geographic metrics
    top_regions = Column(JSONB, default=list)  # Top requesting regions
    edge_performance = Column(JSONB, default=dict)  # Performance by edge location

    # Metadata
    recorded_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    cdn_configuration = relationship("CDNConfiguration", back_populates="cdn_metrics")

    # Indexes and constraints
    __table_args__ = (
        Index("idx_cdn_metrics_config_period", "cdn_configuration_id", "period_start", "period_end"),
        Index("idx_cdn_metrics_hit_rate", "cache_hit_rate"),
        Index("idx_cdn_metrics_performance", "avg_response_time_ms", "avg_first_byte_time_ms"),
        Index("idx_cdn_metrics_recorded", "recorded_at"),
        CheckConstraint("total_requests >= 0", name="ck_cdn_metrics_requests_positive"),
        CheckConstraint("cache_hits >= 0", name="ck_cdn_metrics_hits_positive"),
        CheckConstraint("cache_misses >= 0", name="ck_cdn_metrics_misses_positive"),
        CheckConstraint("cache_hit_rate >= 0 AND cache_hit_rate <= 1", name="ck_cdn_metrics_hit_rate_valid"),
        CheckConstraint("error_rate >= 0 AND error_rate <= 1", name="ck_cdn_metrics_error_rate_valid"),
        CheckConstraint("period_end > period_start", name="ck_cdn_metrics_period_valid"),
    )


class AssetDelivery(Base):
    """
    Asset delivery model for tracking individual asset delivery requests.

    Records delivery requests, performance metrics, and user experience
    data for individual asset delivery events and optimization analysis.
    """
    __tablename__ = "asset_deliveries"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    asset_optimization_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("asset_optimizations.id"), nullable=False)

    # Request information
    request_ip = Column(String(45))  # IPv6 support
    user_agent = Column(Text)
    referer = Column(String(500))
    request_headers = Column(JSONB, default=dict)

    # Geographic information
    country_code = Column(String(2), index=True)
    region_code = Column(String(10))
    city = Column(String(100))
    edge_location = Column(String(50), index=True)

    # Performance metrics
    response_time_ms = Column(Integer, nullable=False)
    first_byte_time_ms = Column(Integer, nullable=False)
    download_time_ms = Column(Integer, nullable=False)
    bytes_transferred = Column(Integer, nullable=False)

    # Cache information
    cache_status = Column(String(20), index=True)  # HIT, MISS, STALE, etc.
    cache_age_seconds = Column(Integer)

    # Response information
    http_status_code = Column(Integer, nullable=False, index=True)
    response_headers = Column(JSONB, default=dict)
    error_message = Column(Text)

    # Metadata
    delivered_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # Relationships
    asset_optimization = relationship("AssetOptimization", back_populates="asset_deliveries")

    # Indexes and constraints
    __table_args__ = (
        Index("idx_asset_delivery_opt_delivered", "asset_optimization_id", "delivered_at"),
        Index("idx_asset_delivery_status_time", "http_status_code", "response_time_ms"),
        Index("idx_asset_delivery_cache_status", "cache_status", "delivered_at"),
        Index("idx_asset_delivery_geo", "country_code", "edge_location"),
        Index("idx_asset_delivery_performance", "response_time_ms", "first_byte_time_ms"),
        CheckConstraint("response_time_ms >= 0", name="ck_asset_delivery_response_time_positive"),
        CheckConstraint("first_byte_time_ms >= 0", name="ck_asset_delivery_first_byte_positive"),
        CheckConstraint("download_time_ms >= 0", name="ck_asset_delivery_download_time_positive"),
        CheckConstraint("bytes_transferred >= 0", name="ck_asset_delivery_bytes_positive"),
        CheckConstraint("http_status_code >= 100 AND http_status_code < 600", name="ck_asset_delivery_status_valid"),
    )
