"""
Unit tests for Financial Repository classes.

This module tests the financial repository layer functionality including:
- RevenueRecordRepository: Revenue tracking and analytics with multi-currency support
- ReconciliationRecordRepository: Financial reconciliation and variance detection
- Advanced financial analytics and reporting capabilities
- Performance optimization with composite index utilization
- Compliance and audit trail management

Implements comprehensive test coverage for Step 5 Payment & Transaction Management System.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.financial_repository import RevenueRecordRepository, ReconciliationRecordRepository
from app.models.financial_models import RevenueRecord, ReconciliationRecord, RevenueCategory, ReconciliationStatus
from app.core.payment.config import PaymentProviderType
from app.repositories.base import PaginationParams, QueryResult, RepositoryError


class TestRevenueRecordRepository:
    """Test RevenueRecordRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def revenue_repository(self, mock_db_session):
        """Create RevenueRecordRepository instance with mock session."""
        return RevenueRecordRepository(mock_db_session)

    @pytest.fixture
    def sample_revenue_data(self):
        """Sample revenue record data for testing."""
        return {
            "category": RevenueCategory.SERVICE_BOOKING,
            "gross_amount": Decimal("1000.00"),
            "platform_fee": Decimal("100.00"),
            "processing_fee": Decimal("30.00"),
            "net_vendor_amount": Decimal("870.00"),
            "net_platform_amount": Decimal("130.00"),
            "transaction_date": datetime.now(timezone.utc),
            "reference_id": "REV-20240101-001",
            "booking_id": 123,
            "payment_id": 456,
            "vendor_id": 789,
            "user_id": 101,
            "currency": "NGN"
        }

    @pytest.mark.asyncio
    async def test_create_revenue_record_success(self, revenue_repository, sample_revenue_data):
        """Test successful revenue record creation."""
        # Mock the create method
        mock_revenue = RevenueRecord(
            id=1,
            **sample_revenue_data
        )
        revenue_repository.create = AsyncMock(return_value=mock_revenue)

        # Test revenue record creation
        result = await revenue_repository.create_revenue_record(**sample_revenue_data)

        # Assertions
        assert result.id == 1
        assert result.category == RevenueCategory.SERVICE_BOOKING
        assert result.gross_amount == Decimal("1000.00")
        assert result.platform_fee == Decimal("100.00")
        assert result.processing_fee == Decimal("30.00")
        assert result.net_vendor_amount == Decimal("870.00")
        assert result.net_platform_amount == Decimal("130.00")
        assert result.reference_id == "REV-20240101-001"
        assert result.booking_id == 123
        assert result.vendor_id == 789
        assert result.currency == "NGN"

        # Verify create was called with correct data
        revenue_repository.create.assert_called_once()
        call_args = revenue_repository.create.call_args[0][0]
        assert call_args["category"] == RevenueCategory.SERVICE_BOOKING
        assert call_args["gross_amount"] == Decimal("1000.00")

    @pytest.mark.asyncio
    async def test_get_revenue_by_category(self, revenue_repository):
        """Test getting revenue records by category."""
        # Mock database execution
        mock_revenues = [
            RevenueRecord(id=1, category=RevenueCategory.SERVICE_BOOKING, currency="NGN"),
            RevenueRecord(id=2, category=RevenueCategory.SERVICE_BOOKING, currency="NGN")
        ]
        
        mock_query_result = QueryResult(
            items=mock_revenues,
            total=2,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        revenue_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting revenue by category
        result = await revenue_repository.get_revenue_by_category(
            category=RevenueCategory.SERVICE_BOOKING,
            vendor_id=789,
            currency="NGN"
        )

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(rev.category == RevenueCategory.SERVICE_BOOKING for rev in result.items)
        assert all(rev.currency == "NGN" for rev in result.items)

        # Verify get_paginated was called
        revenue_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vendor_revenue_summary(self, revenue_repository):
        """Test getting vendor revenue summary."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_records = 50
        mock_row.total_gross_amount = Decimal("50000.00")
        mock_row.total_platform_fees = Decimal("5000.00")
        mock_row.total_processing_fees = Decimal("1500.00")
        mock_row.total_net_vendor_amount = Decimal("43500.00")
        mock_row.total_net_platform_amount = Decimal("6500.00")
        mock_row.total_tax_amount = Decimal("3750.00")
        mock_row.service_bookings = 45
        mock_row.platform_fees = 3
        mock_row.processing_fees = 2
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        revenue_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting vendor revenue summary
        result = await revenue_repository.get_vendor_revenue_summary(
            vendor_id=789,
            currency="NGN"
        )

        # Assertions
        assert result["vendor_id"] == 789
        assert result["currency"] == "NGN"
        assert result["total_records"] == 50
        assert result["total_gross_amount"] == 50000.00
        assert result["total_platform_fees"] == 5000.00
        assert result["total_processing_fees"] == 1500.00
        assert result["total_net_vendor_amount"] == 43500.00
        assert result["total_net_platform_amount"] == 6500.00
        assert result["total_tax_amount"] == 3750.00
        assert result["breakdown"]["service_bookings"] == 45
        assert result["breakdown"]["platform_fees"] == 3
        assert result["breakdown"]["processing_fees"] == 2

        # Verify database was queried
        revenue_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_platform_revenue_analytics(self, revenue_repository):
        """Test getting platform revenue analytics."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_records = 1000
        mock_row.total_gross_revenue = Decimal("500000.00")
        mock_row.total_platform_revenue = Decimal("50000.00")
        mock_row.total_vendor_revenue = Decimal("435000.00")
        mock_row.total_tax_collected = Decimal("37500.00")
        mock_row.active_vendors = 25
        mock_row.active_users = 150
        mock_row.avg_transaction_amount = Decimal("500.00")
        mock_row.max_transaction_amount = Decimal("5000.00")
        mock_row.min_transaction_amount = Decimal("50.00")
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        revenue_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting platform revenue analytics
        result = await revenue_repository.get_platform_revenue_analytics(
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK
        )

        # Assertions
        assert result["currency"] == "NGN"
        assert result["provider"] == "paystack"
        assert result["totals"]["records"] == 1000
        assert result["totals"]["gross_revenue"] == 500000.00
        assert result["totals"]["platform_revenue"] == 50000.00
        assert result["totals"]["vendor_revenue"] == 435000.00
        assert result["totals"]["tax_collected"] == 37500.00
        assert result["metrics"]["active_vendors"] == 25
        assert result["metrics"]["active_users"] == 150
        assert result["metrics"]["avg_transaction_amount"] == 500.00
        assert result["metrics"]["max_transaction_amount"] == 5000.00
        assert result["metrics"]["min_transaction_amount"] == 50.00
        assert result["metrics"]["platform_take_rate"] == 10.0  # 50000/500000 * 100

        # Verify database was queried
        revenue_repository.db.execute.assert_called_once()


class TestReconciliationRecordRepository:
    """Test ReconciliationRecordRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def reconciliation_repository(self, mock_db_session):
        """Create ReconciliationRecordRepository instance with mock session."""
        return ReconciliationRecordRepository(mock_db_session)

    @pytest.fixture
    def sample_reconciliation_data(self):
        """Sample reconciliation record data for testing."""
        return {
            "provider": PaymentProviderType.PAYSTACK,
            "period_start": datetime(2024, 1, 1, tzinfo=timezone.utc),
            "period_end": datetime(2024, 1, 31, tzinfo=timezone.utc),
            "expected_amount": Decimal("10000.00"),
            "actual_amount": Decimal("9950.00"),
            "expected_transaction_count": 100,
            "actual_transaction_count": 99,
            "reference_id": "REC-20240201-001",
            "currency": "NGN",
            "provider_statement_id": "STMT-123456"
        }

    @pytest.mark.asyncio
    async def test_create_reconciliation_record_success(self, reconciliation_repository, sample_reconciliation_data):
        """Test successful reconciliation record creation."""
        # Mock the create method
        mock_reconciliation = ReconciliationRecord(
            id=1,
            variance=Decimal("-50.00"),  # 9950 - 10000
            status=ReconciliationStatus.PENDING,
            **sample_reconciliation_data
        )
        reconciliation_repository.create = AsyncMock(return_value=mock_reconciliation)

        # Test reconciliation record creation
        result = await reconciliation_repository.create_reconciliation_record(**sample_reconciliation_data)

        # Assertions
        assert result.id == 1
        assert result.provider == PaymentProviderType.PAYSTACK
        assert result.expected_amount == Decimal("10000.00")
        assert result.actual_amount == Decimal("9950.00")
        assert result.variance == Decimal("-50.00")
        assert result.expected_transaction_count == 100
        assert result.actual_transaction_count == 99
        assert result.reference_id == "REC-20240201-001"
        assert result.currency == "NGN"
        assert result.status == ReconciliationStatus.PENDING

        # Verify create was called with correct data
        reconciliation_repository.create.assert_called_once()
        call_args = reconciliation_repository.create.call_args[0][0]
        assert call_args["provider"] == PaymentProviderType.PAYSTACK
        assert call_args["variance"] == Decimal("-50.00")
        assert call_args["status"] == ReconciliationStatus.PENDING

    @pytest.mark.asyncio
    async def test_update_reconciliation_status_success(self, reconciliation_repository):
        """Test successful reconciliation status update."""
        # Mock the update method
        mock_reconciliation = ReconciliationRecord(
            id=1,
            status=ReconciliationStatus.COMPLETED,
            reconciled_at=datetime.now(timezone.utc),
            reconciled_by=456,
            resolution_notes="Variance resolved - bank processing delay"
        )
        reconciliation_repository.update = AsyncMock(return_value=mock_reconciliation)

        # Test status update
        result = await reconciliation_repository.update_reconciliation_status(
            reconciliation_id=1,
            status=ReconciliationStatus.COMPLETED,
            reconciled_by=456,
            resolution_notes="Variance resolved - bank processing delay"
        )

        # Assertions
        assert result.id == 1
        assert result.status == ReconciliationStatus.COMPLETED
        assert result.reconciled_by == 456
        assert result.resolution_notes == "Variance resolved - bank processing delay"
        assert result.reconciled_at is not None

        # Verify update was called with correct data
        call_args = reconciliation_repository.update.call_args[0][1]
        assert call_args["status"] == ReconciliationStatus.COMPLETED
        assert call_args["reconciled_by"] == 456
        assert call_args["resolution_notes"] == "Variance resolved - bank processing delay"
        assert "reconciled_at" in call_args

    @pytest.mark.asyncio
    async def test_get_reconciliations_by_provider(self, reconciliation_repository):
        """Test getting reconciliation records by provider."""
        # Mock database execution
        mock_reconciliations = [
            ReconciliationRecord(id=1, provider=PaymentProviderType.PAYSTACK),
            ReconciliationRecord(id=2, provider=PaymentProviderType.PAYSTACK)
        ]
        
        mock_query_result = QueryResult(
            items=mock_reconciliations,
            total=2,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        reconciliation_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting reconciliations by provider
        result = await reconciliation_repository.get_reconciliations_by_provider(
            provider=PaymentProviderType.PAYSTACK,
            status=ReconciliationStatus.COMPLETED
        )

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(rec.provider == PaymentProviderType.PAYSTACK for rec in result.items)

        # Verify get_paginated was called
        reconciliation_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_discrepancy_reconciliations(self, reconciliation_repository):
        """Test getting reconciliation records with discrepancies."""
        # Mock database execution
        mock_reconciliations = [
            ReconciliationRecord(id=1, variance=Decimal("100.00")),
            ReconciliationRecord(id=2, variance=Decimal("-75.50"))
        ]
        
        mock_query_result = QueryResult(
            items=mock_reconciliations,
            total=2,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        reconciliation_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting discrepancy reconciliations
        result = await reconciliation_repository.get_discrepancy_reconciliations(
            variance_threshold=Decimal("0.01")
        )

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(abs(rec.variance) > Decimal("0.01") for rec in result.items)

        # Verify get_paginated was called
        reconciliation_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_reconciliation_analytics(self, reconciliation_repository):
        """Test getting reconciliation analytics."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_reconciliations = 50
        mock_row.total_expected_amount = Decimal("250000.00")
        mock_row.total_actual_amount = Decimal("249500.00")
        mock_row.total_variance = Decimal("-500.00")
        mock_row.total_absolute_variance = Decimal("1500.00")
        mock_row.completed_reconciliations = 45
        mock_row.discrepancy_reconciliations = 3
        mock_row.variance_reconciliations = 8
        mock_row.avg_absolute_variance = Decimal("30.00")
        mock_row.max_absolute_variance = Decimal("200.00")
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        reconciliation_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting reconciliation analytics
        result = await reconciliation_repository.get_reconciliation_analytics(
            provider=PaymentProviderType.PAYSTACK,
            currency="NGN"
        )

        # Assertions
        assert result["currency"] == "NGN"
        assert result["provider"] == "paystack"
        assert result["totals"]["reconciliations"] == 50
        assert result["totals"]["expected_amount"] == 250000.00
        assert result["totals"]["actual_amount"] == 249500.00
        assert result["totals"]["variance"] == -500.00
        assert result["totals"]["absolute_variance"] == 1500.00
        assert result["status_breakdown"]["completed"] == 45
        assert result["status_breakdown"]["discrepancies"] == 3
        assert result["status_breakdown"]["with_variance"] == 8
        assert result["metrics"]["completion_rate"] == 90.0  # 45/50 * 100
        assert result["metrics"]["discrepancy_rate"] == 6.0  # 3/50 * 100
        assert result["metrics"]["variance_rate"] == 16.0  # 8/50 * 100
        assert result["metrics"]["avg_absolute_variance"] == 30.00
        assert result["metrics"]["max_absolute_variance"] == 200.00
        assert result["metrics"]["accuracy_percentage"] == 99.4  # (250000-1500)/250000 * 100

        # Verify database was queried
        reconciliation_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_reconciliation_record_failure(self, reconciliation_repository, sample_reconciliation_data):
        """Test reconciliation record creation failure handling."""
        # Mock create method to raise exception
        reconciliation_repository.create = AsyncMock(side_effect=Exception("Database error"))

        # Test reconciliation creation failure
        with pytest.raises(RepositoryError) as exc_info:
            await reconciliation_repository.create_reconciliation_record(**sample_reconciliation_data)

        assert "Failed to create reconciliation record" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_reconciliations_by_provider_empty_result(self, reconciliation_repository):
        """Test getting reconciliations by provider with empty result."""
        # Mock database execution with empty result
        mock_query_result = QueryResult(
            items=[],
            total=0,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        reconciliation_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting reconciliations by provider
        result = await reconciliation_repository.get_reconciliations_by_provider(
            provider=PaymentProviderType.STRIPE
        )

        # Assertions
        assert result.total == 0
        assert len(result.items) == 0

        # Verify get_paginated was called
        reconciliation_repository.get_paginated.assert_called_once()
