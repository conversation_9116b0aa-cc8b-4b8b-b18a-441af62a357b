"""
Test suite for password reset token functionality.

This module tests secure password reset token management including
generation, validation, expiration, and usage tracking.

Tests cover Task 2.1.2 password reset token requirements.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.repositories.password_security_repository import PasswordResetTokenRepository
from app.models.password_security import PasswordResetToken
from app.schemas.password_security import PasswordSecurityResponse


class TestPasswordResetTokens:
    """Test password reset token functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_reset_token_repo(self):
        """Create mock password reset token repository."""
        return Mock(spec=PasswordResetTokenRepository)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        return PasswordSecurityService(mock_db_session)

    @pytest.fixture
    def valid_reset_token(self):
        """Create a valid reset token mock."""
        token = Mock(spec=PasswordResetToken)
        token.id = "test-token-id"
        token.user_id = 1
        token.expires_at = datetime.utcnow() + timedelta(hours=24)
        token.used_at = None
        token.is_active = True
        token.is_valid = True
        return token

    @pytest.fixture
    def expired_reset_token(self):
        """Create an expired reset token mock."""
        token = Mock(spec=PasswordResetToken)
        token.id = "expired-token-id"
        token.user_id = 1
        token.expires_at = datetime.utcnow() - timedelta(hours=1)
        token.used_at = None
        token.is_active = True
        token.is_valid = False
        return token

    @pytest.mark.asyncio
    async def test_create_password_reset_token_success(self, password_service):
        """Test successful password reset token creation."""
        user_id = 1
        client_ip = "*************"
        user_agent = "Mozilla/5.0"
        
        mock_token = Mock(spec=PasswordResetToken)
        mock_token.id = "new-token-id"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            mock_create.return_value = mock_token
            
            token = await password_service.create_password_reset_token(user_id, client_ip, user_agent)
            
            assert token is not None
            assert len(token) == 43  # URL-safe base64 encoded 32 bytes
            mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_password_reset_token_with_expiration(self, password_service):
        """Test password reset token creation with proper expiration."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            with patch('app.core.config.settings') as mock_settings:
                mock_settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS = 2
                
                await password_service.create_password_reset_token(user_id, client_ip)
                
                # Verify expiration time was calculated correctly
                call_args = mock_create.call_args
                expires_at = call_args[1]['expires_at']
                expected_expiry = datetime.utcnow() + timedelta(hours=2)
                
                # Allow 1 minute tolerance for test execution time
                assert abs((expires_at - expected_expiry).total_seconds()) < 60

    @pytest.mark.asyncio
    async def test_validate_reset_token_valid(self, password_service, valid_reset_token):
        """Test validation of a valid reset token."""
        token_string = "valid-token-string"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = valid_reset_token
            
            user_id = await password_service.validate_reset_token(token_string)
            
            assert user_id == 1
            mock_get.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_reset_token_invalid(self, password_service):
        """Test validation of an invalid reset token."""
        token_string = "invalid-token-string"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = None
            
            user_id = await password_service.validate_reset_token(token_string)
            
            assert user_id is None
            mock_get.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_reset_token_error_handling(self, password_service):
        """Test error handling in reset token validation."""
        token_string = "test-token"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.side_effect = Exception("Database error")
            
            user_id = await password_service.validate_reset_token(token_string)
            
            assert user_id is None

    @pytest.mark.asyncio
    async def test_reset_password_with_valid_token(self, password_service, valid_reset_token):
        """Test password reset with valid token."""
        token_string = "valid-token"
        new_password = "NewSecureP@ssw0rd!"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = valid_reset_token
            
            with patch.object(password_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True, strength_score=85, strength_level="strong")
                
                with patch.object(password_service, 'check_password_history') as mock_history:
                    mock_history.return_value = False
                    
                    with patch.object(password_service, 'add_password_to_history') as mock_add_history:
                        with patch.object(password_service.reset_token_repo, 'mark_token_used') as mock_mark_used:
                            with patch.object(password_service, 'reset_lockout_on_success') as mock_reset_lockout:
                                
                                result = await password_service.reset_password_with_token(
                                    token_string, new_password, client_ip
                                )
                                
                                assert result.success is True
                                assert "successfully" in result.message
                                mock_mark_used.assert_called_once()
                                mock_reset_lockout.assert_called_once()

    @pytest.mark.asyncio
    async def test_reset_password_with_invalid_token(self, password_service):
        """Test password reset with invalid token."""
        token_string = "invalid-token"
        new_password = "NewSecureP@ssw0rd!"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = None
            
            result = await password_service.reset_password_with_token(
                token_string, new_password, client_ip
            )
            
            assert result.success is False
            assert "Invalid or expired" in result.message

    @pytest.mark.asyncio
    async def test_reset_password_with_weak_password(self, password_service, valid_reset_token):
        """Test password reset with weak password."""
        token_string = "valid-token"
        weak_password = "weak"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = valid_reset_token
            
            with patch.object(password_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(
                    is_valid=False, 
                    violations=["Password too short", "Missing uppercase"]
                )
                
                result = await password_service.reset_password_with_token(
                    token_string, weak_password, client_ip
                )
                
                assert result.success is False
                assert "does not meet security requirements" in result.message
                assert "violations" in result.details

    @pytest.mark.asyncio
    async def test_reset_password_with_reused_password(self, password_service, valid_reset_token):
        """Test password reset with previously used password."""
        token_string = "valid-token"
        reused_password = "PreviousP@ssw0rd!"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = valid_reset_token
            
            with patch.object(password_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True)
                
                with patch.object(password_service, 'check_password_history') as mock_history:
                    mock_history.return_value = True  # Password was used recently
                    
                    result = await password_service.reset_password_with_token(
                        token_string, reused_password, client_ip
                    )
                    
                    assert result.success is False
                    assert "used recently" in result.message

    @pytest.mark.asyncio
    async def test_token_hash_security(self, password_service):
        """Test that tokens are properly hashed for storage."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            token = await password_service.create_password_reset_token(user_id, client_ip)
            
            # Verify that the stored token hash is different from the plain token
            call_args = mock_create.call_args
            stored_hash = call_args[1]['token_hash']
            
            assert stored_hash != token
            assert len(stored_hash) == 64  # SHA-256 hex digest length

    @pytest.mark.asyncio
    async def test_token_uniqueness(self, password_service):
        """Test that generated tokens are unique."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token'):
            token1 = await password_service.create_password_reset_token(user_id, client_ip)
            token2 = await password_service.create_password_reset_token(user_id, client_ip)
            
            assert token1 != token2

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self, password_service):
        """Test cleanup of expired reset tokens."""
        with patch.object(password_service.reset_token_repo, 'cleanup_expired_tokens') as mock_cleanup:
            mock_cleanup.return_value = 5
            
            count = await password_service.cleanup_expired_tokens()
            
            assert count == 5
            mock_cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens_error_handling(self, password_service):
        """Test error handling in token cleanup."""
        with patch.object(password_service.reset_token_repo, 'cleanup_expired_tokens') as mock_cleanup:
            mock_cleanup.side_effect = Exception("Database error")
            
            count = await password_service.cleanup_expired_tokens()
            
            assert count == 0

    @pytest.mark.asyncio
    async def test_token_single_use_enforcement(self, password_service, valid_reset_token):
        """Test that tokens can only be used once."""
        token_string = "single-use-token"
        new_password = "NewSecureP@ssw0rd!"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.return_value = valid_reset_token
            
            with patch.object(password_service, 'validate_password_strength') as mock_validate:
                mock_validate.return_value = Mock(is_valid=True, strength_score=85, strength_level="strong")
                
                with patch.object(password_service, 'check_password_history') as mock_history:
                    mock_history.return_value = False
                    
                    with patch.object(password_service, 'add_password_to_history'):
                        with patch.object(password_service.reset_token_repo, 'mark_token_used') as mock_mark_used:
                            with patch.object(password_service, 'reset_lockout_on_success'):
                                
                                # First use should succeed
                                result1 = await password_service.reset_password_with_token(
                                    token_string, new_password, client_ip
                                )
                                assert result1.success is True
                                
                                # Token should be marked as used
                                mock_mark_used.assert_called_once_with(valid_reset_token.id, client_ip)

    @pytest.mark.asyncio
    async def test_token_ip_tracking(self, password_service):
        """Test that token creation and usage tracks IP addresses."""
        user_id = 1
        creation_ip = "*************"
        usage_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            await password_service.create_password_reset_token(user_id, creation_ip)
            
            # Verify creation IP is tracked
            call_args = mock_create.call_args
            assert call_args[1]['client_ip'] == creation_ip

    @pytest.mark.asyncio
    async def test_token_user_agent_tracking(self, password_service):
        """Test that token creation tracks user agent."""
        user_id = 1
        client_ip = "*************"
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            await password_service.create_password_reset_token(user_id, client_ip, user_agent)
            
            # Verify user agent is tracked
            call_args = mock_create.call_args
            assert call_args[1]['user_agent'] == user_agent

    @pytest.mark.asyncio
    async def test_concurrent_token_creation(self, password_service):
        """Test handling of concurrent token creation for same user."""
        user_id = 1
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'create_reset_token') as mock_create:
            # Simulate concurrent token creation
            token1 = await password_service.create_password_reset_token(user_id, client_ip)
            token2 = await password_service.create_password_reset_token(user_id, client_ip)
            
            assert token1 != token2
            assert mock_create.call_count == 2

    @pytest.mark.asyncio
    async def test_token_error_handling_in_reset(self, password_service):
        """Test error handling during password reset process."""
        token_string = "test-token"
        new_password = "NewSecureP@ssw0rd!"
        client_ip = "*************"
        
        with patch.object(password_service.reset_token_repo, 'get_valid_token') as mock_get:
            mock_get.side_effect = Exception("Database error")
            
            result = await password_service.reset_password_with_token(
                token_string, new_password, client_ip
            )
            
            assert result.success is False
            assert "error occurred" in result.message
