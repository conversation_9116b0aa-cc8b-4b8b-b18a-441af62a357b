"""
Comprehensive unit tests for VendorService.

This module provides complete test coverage for the VendorService class including:
- Multi-step vendor registration workflow testing
- Business logic validation and error handling scenarios
- Onboarding step progression validation
- Document upload coordination with file validation
- Integration with BaseService patterns and repository mocking
- Public profile visibility and access control testing

Implements >80% test coverage requirements for Task 3.1.1 Phase 3.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.vendor_service import VendorService
from app.models.vendor import (
    Vendor, VendorProfile, VendorDocument,
    VendorType, VerificationStatus, MarketplaceStatus,
    DocumentType, DocumentStatus
)
from app.schemas.vendor import (
    VendorRegistrationRequest, VendorRegistrationResponse,
    VendorProfileUpdate, OnboardingStepUpdate, VendorDocumentCreate
)
from app.services.base import ValidationError, NotFoundError, ConflictError


class TestVendorService:
    """Test suite for VendorService class."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def vendor_service(self, mock_db_session):
        """Create VendorService instance with mocked dependencies."""
        return VendorService(mock_db_session)

    @pytest.fixture
    def sample_vendor(self):
        """Create sample vendor for testing."""
        vendor = MagicMock()
        vendor.id = 1
        vendor.user_id = 123
        vendor.business_name = "Test Business"
        vendor.business_type = VendorType.RESTAURANT
        vendor.business_registration_number = "REG123456"
        vendor.tax_id = "TAX789"
        vendor.verification_status = VerificationStatus.PENDING
        vendor.marketplace_status = MarketplaceStatus.INACTIVE
        vendor.onboarding_completed = False
        vendor.onboarding_step = 1
        vendor.commission_rate = 0.15
        vendor.average_rating = 0.0
        vendor.total_reviews = 0
        vendor.response_rate = 0.0
        vendor.response_time_hours = 0
        vendor.total_earnings = 0.0
        vendor.total_bookings = 0
        vendor.seo_score = 0.0
        vendor.marketplace_ranking = 0
        vendor.listing_quality_score = 0.0
        vendor.created_at = datetime.now()
        vendor.updated_at = datetime.now()
        return vendor

    @pytest.fixture
    def sample_vendor_profile(self):
        """Create sample vendor profile for testing."""
        profile = MagicMock()
        profile.id = 1
        profile.vendor_id = 1
        profile.description = "Test restaurant description"
        profile.short_description = "Great food"
        profile.tagline = "Best in town"
        profile.contact_phone = "+1234567890"
        profile.contact_email = "<EMAIL>"
        profile.website_url = "https://testrestaurant.com"
        profile.business_address = "123 Test St"
        profile.city = "Test City"
        profile.state = "Test State"
        profile.country = "Test Country"
        profile.postal_code = "12345"
        profile.logo_url = None
        profile.cover_image_url = None
        profile.gallery_images = []
        profile.languages_spoken = ["English"]
        profile.years_in_business = 5
        return profile

    @pytest.fixture
    def registration_request(self):
        """Create sample registration request."""
        return VendorRegistrationRequest(
            business_name="Test Business",
            business_type=VendorType.RESTAURANT,
            business_registration_number="REG123456",
            tax_id="TAX789",
            description="Test restaurant description",
            contact_phone="+1234567890",
            contact_email="<EMAIL>",
            website_url="https://testrestaurant.com",
            business_address="123 Test St",
            city="Test City",
            state="Test State",
            country="Test Country",
            postal_code="12345"
        )

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, vendor_service):
        """Test successful validation of vendor creation data."""
        data = {
            'user_id': 123,
            'business_name': 'Test Business',
            'business_type': 'restaurant'
        }

        result = await vendor_service.validate_create_data(data)

        assert result['user_id'] == 123
        assert result['business_name'] == 'Test Business'
        assert result['business_type'] == VendorType.RESTAURANT
        assert result['verification_status'] == VerificationStatus.PENDING
        assert result['marketplace_status'] == MarketplaceStatus.INACTIVE
        assert result['onboarding_completed'] is False
        assert result['onboarding_step'] == 1
        assert result['commission_rate'] == 0.15

    @pytest.mark.asyncio
    async def test_validate_create_data_missing_required_fields(self, vendor_service):
        """Test validation failure with missing required fields."""
        data = {'user_id': 123}  # Missing business_name and business_type

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.validate_create_data(data)

        assert "Missing required field" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_invalid_business_type(self, vendor_service):
        """Test validation failure with invalid business type."""
        data = {
            'user_id': 123,
            'business_name': 'Test Business',
            'business_type': 'invalid_type'
        }

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.validate_create_data(data)

        assert "Invalid business type" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_success(self, vendor_service):
        """Test successful validation of vendor update data."""
        data = {
            'business_type': 'guide',
            'verification_status': 'verified',
            'onboarding_step': 3,
            'commission_rate': 0.20
        }

        result = await vendor_service.validate_update_data(data, 1)

        assert result['business_type'] == VendorType.GUIDE
        assert result['verification_status'] == VerificationStatus.VERIFIED
        assert result['onboarding_step'] == 3
        assert result['commission_rate'] == 0.20

    @pytest.mark.asyncio
    async def test_validate_update_data_invalid_onboarding_step(self, vendor_service):
        """Test validation failure with invalid onboarding step."""
        data = {'onboarding_step': 10}  # Invalid step (should be 1-6)

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.validate_update_data(data, 1)

        assert "Onboarding step must be between 1 and 6" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_update_data_invalid_commission_rate(self, vendor_service):
        """Test validation failure with invalid commission rate."""
        data = {'commission_rate': 1.5}  # Invalid rate (should be 0.0-1.0)

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.validate_update_data(data, 1)

        assert "Commission rate must be between 0.0 and 1.0" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_register_vendor_success(self, vendor_service, registration_request, sample_vendor):
        """Test successful vendor registration."""
        # Mock repository methods
        vendor_service.repository.get_by_user_id = AsyncMock(return_value=None)
        vendor_service.repository.create_vendor = AsyncMock(return_value=sample_vendor)

        # Mock the VendorResponse creation to avoid validation issues
        with patch('app.services.vendor_service.VendorResponse') as mock_vendor_response:
            mock_vendor_response.model_validate.return_value = MagicMock(
                id=1,
                business_name="Test Business",
                business_type="restaurant",
                verification_status="pending",
                onboarding_completed=False
            )

            result = await vendor_service.register_vendor(123, registration_request)

            assert isinstance(result, VendorRegistrationResponse)
            assert result.verification_required is True
            assert len(result.onboarding_steps) == 6
            assert "registration successful" in result.message.lower()

            # Verify repository calls
            vendor_service.repository.get_by_user_id.assert_called_once_with(123)
            vendor_service.repository.create_vendor.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_vendor_already_exists(self, vendor_service, registration_request, sample_vendor):
        """Test vendor registration failure when vendor already exists."""
        # Mock existing vendor
        vendor_service.repository.get_by_user_id = AsyncMock(return_value=sample_vendor)

        with pytest.raises(ConflictError) as exc_info:
            await vendor_service.register_vendor(123, registration_request)

        assert "already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_vendor_by_user_id_success(self, vendor_service, sample_vendor):
        """Test successful vendor retrieval by user ID."""
        vendor_service.repository.get_by_user_id = AsyncMock(return_value=sample_vendor)

        result = await vendor_service.get_vendor_by_user_id(123)

        assert result == sample_vendor
        vendor_service.repository.get_by_user_id.assert_called_once_with(123)

    @pytest.mark.asyncio
    async def test_get_vendor_by_user_id_not_found(self, vendor_service):
        """Test vendor retrieval when vendor not found."""
        vendor_service.repository.get_by_user_id = AsyncMock(return_value=None)

        result = await vendor_service.get_vendor_by_user_id(123)

        assert result is None

    @pytest.mark.asyncio
    async def test_update_vendor_profile_success(self, vendor_service, sample_vendor, sample_vendor_profile):
        """Test successful vendor profile update."""
        profile_update = VendorProfileUpdate(
            description="Updated description",
            contact_phone="+9876543210"
        )

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.repository.update_vendor_profile = AsyncMock(return_value=sample_vendor_profile)

        result = await vendor_service.update_vendor_profile(1, profile_update)

        assert result == sample_vendor_profile
        vendor_service.get_by_id_or_raise.assert_called_once_with(1)
        vendor_service.repository.update_vendor_profile.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_vendor_profile_not_found(self, vendor_service):
        """Test vendor profile update when vendor not found."""
        profile_update = VendorProfileUpdate(description="Updated description")

        vendor_service.get_by_id_or_raise = AsyncMock(side_effect=NotFoundError("Vendor", 1))

        with pytest.raises(NotFoundError):
            await vendor_service.update_vendor_profile(1, profile_update)

    @pytest.mark.asyncio
    async def test_generate_onboarding_steps(self, vendor_service):
        """Test onboarding steps generation for different business types."""
        # Test restaurant type
        steps = vendor_service._generate_onboarding_steps(VendorType.RESTAURANT)

        assert len(steps) == 6
        assert steps[0]["step"] == 1
        assert steps[0]["title"] == "Business Information"
        assert steps[1]["description"].endswith("(Food service license required)")

        # Test guide type
        guide_steps = vendor_service._generate_onboarding_steps(VendorType.GUIDE)
        assert guide_steps[1]["description"].endswith("(Guide license required)")

    @pytest.mark.asyncio
    async def test_requires_verification(self, vendor_service):
        """Test verification requirement for different business types."""
        # All business types should require verification
        assert vendor_service._requires_verification(VendorType.RESTAURANT) is True
        assert vendor_service._requires_verification(VendorType.GUIDE) is True
        assert vendor_service._requires_verification(VendorType.HOTEL) is True
        assert vendor_service._requires_verification(VendorType.ACTIVITY_PROVIDER) is True

    @pytest.mark.asyncio
    async def test_update_onboarding_step_success(self, vendor_service, sample_vendor):
        """Test successful onboarding step update."""
        step_update = OnboardingStepUpdate(
            step=2,
            completed_data={"business_info": "completed"},
            notes="Business information completed"
        )

        updated_vendor = MagicMock()
        updated_vendor.id = sample_vendor.id
        updated_vendor.business_type = sample_vendor.business_type
        updated_vendor.onboarding_step = 2
        updated_vendor.onboarding_completed = False

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.update = AsyncMock(return_value=updated_vendor)

        result = await vendor_service.update_onboarding_step(1, step_update)

        assert result.current_step == 2
        assert result.total_steps == 6
        assert result.completion_percentage == (2 / 6) * 100
        assert len(result.steps) == 6
        assert result.steps[0]["completed"] is True
        assert result.steps[1]["completed"] is True
        assert result.steps[2]["completed"] is False

    @pytest.mark.asyncio
    async def test_update_onboarding_step_backward_movement(self, vendor_service, sample_vendor):
        """Test onboarding step update failure when moving backward."""
        sample_vendor.onboarding_step = 3
        step_update = OnboardingStepUpdate(step=2)  # Moving backward

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.update_onboarding_step(1, step_update)

        assert "cannot move to a previous" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_update_onboarding_step_skip_steps(self, vendor_service, sample_vendor):
        """Test onboarding step update failure when skipping steps."""
        sample_vendor.onboarding_step = 1
        step_update = OnboardingStepUpdate(step=3)  # Skipping step 2

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)

        with pytest.raises(ValidationError) as exc_info:
            await vendor_service.update_onboarding_step(1, step_update)

        assert "cannot skip" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_upload_vendor_document_success(self, vendor_service, sample_vendor):
        """Test successful vendor document upload."""
        document_data = VendorDocumentCreate(
            document_type=DocumentType.BUSINESS_LICENSE,
            document_name="Business License",
            file_name="license.pdf",
            file_type="application/pdf",
            file_size=1024,
            description="Business license document",
            file_path="vendor_documents/1/business_license_license.pdf"
        )

        sample_document = MagicMock()
        sample_document.id = 1
        sample_document.vendor_id = 1
        sample_document.document_type = DocumentType.BUSINESS_LICENSE
        sample_document.document_name = "Business License"
        sample_document.file_name = "license.pdf"
        sample_document.file_type = "application/pdf"
        sample_document.file_size = 1024
        sample_document.description = "Business license document"
        sample_document.file_path = "vendor_documents/1/business_license_license.pdf"
        sample_document.status = DocumentStatus.PENDING
        sample_document.uploaded_at = datetime.now()

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.repository.create_vendor_document = AsyncMock(return_value=sample_document)

        result = await vendor_service.upload_vendor_document(1, document_data)

        assert result == sample_document
        assert result.status == DocumentStatus.PENDING
        vendor_service.repository.create_vendor_document.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vendor_documents_success(self, vendor_service, sample_vendor):
        """Test successful vendor documents retrieval."""
        doc1 = MagicMock()
        doc1.id = 1
        doc1.vendor_id = 1
        doc1.document_type = DocumentType.BUSINESS_LICENSE
        doc1.file_name = "license.pdf"
        doc1.status = DocumentStatus.PENDING

        doc2 = MagicMock()
        doc2.id = 2
        doc2.vendor_id = 1
        doc2.document_type = DocumentType.TAX_CERTIFICATE
        doc2.file_name = "tax_cert.pdf"
        doc2.status = DocumentStatus.VERIFIED

        sample_documents = [doc1, doc2]

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.repository.get_vendor_documents = AsyncMock(return_value=sample_documents)

        result = await vendor_service.get_vendor_documents(1)

        assert len(result) == 2
        assert result[0].document_type == DocumentType.BUSINESS_LICENSE
        assert result[1].document_type == DocumentType.TAX_CERTIFICATE
        vendor_service.repository.get_vendor_documents.assert_called_once_with(1, None)

    @pytest.mark.asyncio
    async def test_get_vendor_documents_with_filter(self, vendor_service, sample_vendor):
        """Test vendor documents retrieval with document type filter."""
        doc1 = MagicMock()
        doc1.id = 1
        doc1.vendor_id = 1
        doc1.document_type = DocumentType.BUSINESS_LICENSE
        doc1.file_name = "license.pdf"
        doc1.status = DocumentStatus.PENDING

        sample_documents = [doc1]

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.repository.get_vendor_documents = AsyncMock(return_value=sample_documents)

        result = await vendor_service.get_vendor_documents(1, DocumentType.BUSINESS_LICENSE)

        assert len(result) == 1
        assert result[0].document_type == DocumentType.BUSINESS_LICENSE
        vendor_service.repository.get_vendor_documents.assert_called_once_with(1, DocumentType.BUSINESS_LICENSE)

    @pytest.mark.asyncio
    async def test_get_public_vendor_profile_success(self, vendor_service, sample_vendor, sample_vendor_profile):
        """Test successful public vendor profile retrieval."""
        # Set vendor as verified and active
        sample_vendor.verification_status = VerificationStatus.VERIFIED
        sample_vendor.marketplace_status = MarketplaceStatus.ACTIVE
        sample_vendor.average_rating = 4.5
        sample_vendor.total_reviews = 100
        sample_vendor.response_rate = 95.0
        sample_vendor.response_time_hours = 2
        sample_vendor.profile = sample_vendor_profile

        vendor_service.repository.get_vendor_with_profile = AsyncMock(return_value=sample_vendor)

        result = await vendor_service.get_public_vendor_profile(1)

        assert result is not None
        assert result["id"] == 1
        assert result["business_name"] == "Test Business"
        assert result["business_type"] == VendorType.RESTAURANT.value
        assert result["verified"] is True
        assert result["average_rating"] == 4.5
        assert result["total_reviews"] == 100
        assert result["profile"]["description"] == "Test restaurant description"

    @pytest.mark.asyncio
    async def test_get_public_vendor_profile_not_found(self, vendor_service):
        """Test public vendor profile retrieval when vendor not found."""
        vendor_service.repository.get_vendor_with_profile = AsyncMock(return_value=None)

        result = await vendor_service.get_public_vendor_profile(1)

        assert result is None

    @pytest.mark.asyncio
    async def test_get_public_vendor_profile_not_verified(self, vendor_service, sample_vendor):
        """Test public vendor profile retrieval when vendor not verified."""
        # Set vendor as not verified
        sample_vendor.verification_status = VerificationStatus.PENDING
        sample_vendor.marketplace_status = MarketplaceStatus.INACTIVE

        vendor_service.repository.get_vendor_with_profile = AsyncMock(return_value=sample_vendor)

        result = await vendor_service.get_public_vendor_profile(1)

        assert result is None

    @pytest.mark.asyncio
    async def test_get_public_vendor_profile_not_active(self, vendor_service, sample_vendor):
        """Test public vendor profile retrieval when vendor not active."""
        # Set vendor as verified but not active
        sample_vendor.verification_status = VerificationStatus.VERIFIED
        sample_vendor.marketplace_status = MarketplaceStatus.INACTIVE

        vendor_service.repository.get_vendor_with_profile = AsyncMock(return_value=sample_vendor)

        result = await vendor_service.get_public_vendor_profile(1)

        assert result is None

    @pytest.mark.asyncio
    async def test_service_error_handling(self, vendor_service):
        """Test service error handling for various scenarios."""
        # Test database connection error
        vendor_service.repository.get_by_user_id = AsyncMock(side_effect=Exception("Database connection failed"))

        with pytest.raises(Exception):
            await vendor_service.get_vendor_by_user_id(123)

    @pytest.mark.asyncio
    async def test_onboarding_completion_tracking(self, vendor_service, sample_vendor):
        """Test onboarding completion tracking when reaching final step."""
        step_update = OnboardingStepUpdate(step=6)

        updated_vendor = MagicMock()
        updated_vendor.id = sample_vendor.id
        updated_vendor.business_type = sample_vendor.business_type
        updated_vendor.onboarding_step = 6
        updated_vendor.onboarding_completed = True

        vendor_service.get_by_id_or_raise = AsyncMock(return_value=sample_vendor)
        vendor_service.update = AsyncMock(return_value=updated_vendor)

        result = await vendor_service.update_onboarding_step(1, step_update)

        assert result.current_step == 6
        assert result.completed is True
        assert result.completion_percentage == 100.0
        assert result.next_step_title is None
        assert result.next_step_description is None

    @pytest.mark.asyncio
    async def test_business_type_specific_onboarding(self, vendor_service):
        """Test business type specific onboarding step customization."""
        # Test different business types have appropriate descriptions
        restaurant_steps = vendor_service._generate_onboarding_steps(VendorType.RESTAURANT)
        guide_steps = vendor_service._generate_onboarding_steps(VendorType.GUIDE)
        hotel_steps = vendor_service._generate_onboarding_steps(VendorType.HOTEL)

        # Verify business verification step has appropriate descriptions
        assert "Food service license required" in restaurant_steps[1]["description"]
        assert "Guide license required" in guide_steps[1]["description"]
        assert "Hospitality license required" in hotel_steps[1]["description"]
