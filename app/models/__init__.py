"""
Models package for Culture Connect Backend API.

This module imports all database models to ensure they are registered
with SQLAlchemy for proper table creation and relationships.
"""

# Import all models to ensure they are registered with SQLAlchemy
from app.models.user import User, <PERSON><PERSON><PERSON>, <PERSON>r<PERSON>ession, TokenBlacklist, SecurityEvent

# Password security models (implemented in Task 2.1.2)
from app.models.password_security import PasswordHistory, PasswordResetToken, AccountLockout

# OAuth models (implemented in Task 2.1.3)
from app.models.oauth_models import OAuthProvider, OAuthAccount, OAuthToken, OAuthState

# RBAC models (implemented in Task 2.2.3)
from app.models.rbac_models import RoleHierarchy, PermissionGrant, AccessControlLog

# Email models (implemented in Task 2.3.1)
from app.models.email_models import EmailTemplate, EmailDelivery, EmailPreference, EmailQueue

# Push notification models (implemented in Task 2.3.2)
from app.models.push_notification_models import (
    DeviceToken, NotificationTemplate, NotificationDelivery,
    NotificationPreference, NotificationQueue
)

# Vendor models (implemented in Phase 1)
from app.models.vendor import Vendor, VendorProfile, VendorDocument

# Verification models (implemented in Task 3.1.2)
from app.models.verification import (
    DocumentVerificationWorkflow, VerificationHistory, AutomatedValidationRule
)

# Service models (implemented in Task 3.2.1)
from app.models.service import (
    Service, ServiceCategory, ServiceImage, ServicePricing, ServiceAvailability,
    ServiceStatus, PricingType, AvailabilityType
)

# Booking models (implemented in Task 4.1.1)
from app.models.booking import (
    Booking, BookingStatusHistory, BookingCommunication, BookingModification,
    BookingStatus, VendorResponseType, BookingPriority, CommunicationType, ModificationType
)

# Availability models (implemented in Task 4.1.2)
from app.models.availability import (
    VendorAvailability, AvailabilitySlot, RecurringAvailability, AvailabilityException
)

# Booking communication models (implemented in Task 4.1.3)
from app.models.booking_communication import (
    BookingMessage, MessageAttachment, MessageTemplate, MessageDeliveryLog,
    MessageType, MessageStatus, DeliveryMethod, DeliveryStatus, TemplateCategory
)

# Payment models (implemented in Phase 1 - Payment & Transaction Management System)
from app.models.payment import (
    Payment, PaymentMethod, PaymentStatus, PaymentMethodType, TransactionType
)

# Transaction models (implemented in Phase 1 - Payment & Transaction Management System)
from app.models.transaction_models import (
    Transaction, TransactionEvent, TransactionStatus, TransactionEventType
)

# Payout models (implemented in Phase 1 - Payment & Transaction Management System)
from app.models.payout_models import (
    VendorPayout, EscrowAccount, PayoutStatus, EscrowStatus, ReleaseCondition
)

# Financial models (implemented in Phase 1 - Payment & Transaction Management System)
from app.models.financial_models import (
    RevenueRecord, ReconciliationRecord, RevenueCategory, ReconciliationStatus
)

# VPN Detection models (implemented in Phase 2.1 - VPN Detection Models)
from app.models.vpn_detection import (
    VPNDetectionResult, ProxyDetectionResult, AnonymizationDetectionResult,
    VPNDetectionAnalytics, VPNDetectionMethod, ProxyType, DetectionConfidence
)

# Promotional models (implemented in Phase 5 - Promotional & Advertising System)
from app.models.promotional import (
    Campaign, Advertisement, CampaignMetrics, PromotionalListing, AdSpend,
    CampaignStatus, CampaignType, CampaignObjective, BidStrategy,
    AdFormat, AdStatus, PlacementType, PlacementStatus, SpendCategory
)

# Geolocation Analytics models (implemented in Phase 2.2 - Analytics Dashboard)
from app.models.geolocation_analytics import (
    GeolocationAnalytics, ProviderPerformanceMetrics, GeolocationRoutingMetrics,
    ConversionAnalytics, AnalyticsPeriodType, RoutingDecisionType
)

# A/B Testing models (implemented in Phase 2.3 - A/B Testing Framework)
from .ab_testing import (
    ABTest, ABTestAssignment, ABTestResult, ABTestAnalysis,
    RoutingStrategy, ABTestStatus, ABTestType, StatisticalSignificance
)

# Review models (implemented in Task 4.4.1)
from app.models.review_models import (
    Review, ReviewResponse, ReviewModeration, ReviewAnalytics,
    ReviewStatus, ResponseStatus, ModerationAction
)

# WebSocket models (implemented in Task 6.1.1)
from app.models.websocket_models import (
    WebSocketConnection, WebSocketEvent, WebSocketConnectionMetrics, UserPresence,
    ConnectionStatus, EventType, EventPriority
)

# Real-time Synchronization models (implemented in Task 6.1.2)
from app.models.sync_models import (
    DataSyncRecord, SyncConflict, SyncBatch, SyncMetrics,
    SyncStatus, ConflictResolutionStrategy, SyncPriority, ChangeType, SyncScope
)

# Analytics models (implemented in Phase 7.1 & 7.2 - Analytics Dashboard System & Performance Monitoring)
from app.models.analytics_models import (
    UserAnalytics, VendorAnalytics, BookingAnalytics, SystemMetrics,
    DashboardWidget, KPIDefinition, AnalyticsTimeframe, MetricType, DashboardWidgetType,
    PerformanceMetrics, SystemHealth, PerformanceMetricType, SystemHealthStatus
)

# Scaling models (implemented in Phase 7.3.3 - Horizontal Scaling & Load Balancing)
from app.models.scaling_models import (
    ScalingMetrics, AutoScalingPolicy, LoadBalancerConfig, ContainerMetrics,
    ScalingEvent, ScalingTriggerType, ScalingDirection, LoadBalancerStrategy,
    ContainerStatus, ScalingEventType
)

# CDN models (implemented in Phase 7.3.4 - CDN Optimization & Asset Delivery)
from app.models.cdn_models import (
    CDNConfiguration, AssetOptimization, AssetBundle, CDNMetrics,
    AssetDelivery, CDNProvider, AssetType, OptimizationType, DeliveryStatus
)

# TODO: Import other models as they are implemented
# from app.models.promotional import Campaign, CampaignMetrics, Advertisement, PromotionalCode

# Export all models for easy importing
__all__ = [
    # User models
    "User",
    "APIKey",
    "UserSession",
    "TokenBlacklist",
    "SecurityEvent",

    # Password security models (implemented in Task 2.1.2)
    "PasswordHistory",
    "PasswordResetToken",
    "AccountLockout",

    # OAuth models (implemented in Task 2.1.3)
    "OAuthProvider",
    "OAuthAccount",
    "OAuthToken",
    "OAuthState",

    # RBAC models (implemented in Task 2.2.3)
    "RoleHierarchy",
    "PermissionGrant",
    "AccessControlLog",

    # Email models (implemented in Task 2.3.1)
    "EmailTemplate",
    "EmailDelivery",
    "EmailPreference",
    "EmailQueue",

    # Push notification models (implemented in Task 2.3.2)
    "DeviceToken",
    "NotificationTemplate",
    "NotificationDelivery",
    "NotificationPreference",
    "NotificationQueue",

    # Vendor models (implemented in Phase 1)
    "Vendor",
    "VendorProfile",
    "VendorDocument",

    # Verification models (implemented in Task 3.1.2)
    "DocumentVerificationWorkflow",
    "VerificationHistory",
    "AutomatedValidationRule",

    # Service models (implemented in Task 3.2.1)
    "Service",
    "ServiceCategory",
    "ServiceImage",
    "ServicePricing",
    "ServiceAvailability",
    "ServiceStatus",
    "PricingType",
    "AvailabilityType",

    # Booking models (implemented in Task 4.1.1)
    "Booking",
    "BookingStatusHistory",
    "BookingCommunication",
    "BookingModification",
    "BookingStatus",
    "VendorResponseType",
    "BookingPriority",
    "CommunicationType",
    "ModificationType",

    # Availability models (implemented in Task 4.1.2)
    "VendorAvailability",
    "AvailabilitySlot",
    "RecurringAvailability",
    "AvailabilityException",

    # Booking communication models (implemented in Task 4.1.3)
    "BookingMessage",
    "MessageAttachment",
    "MessageTemplate",
    "MessageDeliveryLog",
    "MessageType",
    "MessageStatus",
    "DeliveryMethod",
    "DeliveryStatus",
    "TemplateCategory",

    # Payment models (implemented in Phase 1 - Payment & Transaction Management System)
    "Payment",
    "PaymentMethod",
    "PaymentStatus",
    "PaymentMethodType",
    "TransactionType",

    # Transaction models (implemented in Phase 1 - Payment & Transaction Management System)
    "Transaction",
    "TransactionEvent",
    "TransactionStatus",
    "TransactionEventType",

    # Payout models (implemented in Phase 1 - Payment & Transaction Management System)
    "VendorPayout",
    "EscrowAccount",
    "PayoutStatus",
    "EscrowStatus",
    "ReleaseCondition",

    # Financial models (implemented in Phase 1 - Payment & Transaction Management System)
    "RevenueRecord",
    "ReconciliationRecord",
    "RevenueCategory",
    "ReconciliationStatus",

    # VPN Detection models (implemented in Phase 2.1 - VPN Detection Models)
    "VPNDetectionResult",
    "ProxyDetectionResult",
    "AnonymizationDetectionResult",
    "VPNDetectionAnalytics",
    "VPNDetectionMethod",
    "ProxyType",
    "DetectionConfidence",

    # Promotional models (implemented in Phase 5 - Promotional & Advertising System)
    "Campaign",
    "Advertisement",
    "CampaignMetrics",
    "PromotionalListing",
    "AdSpend",
    "CampaignStatus",
    "CampaignType",
    "CampaignObjective",
    "BidStrategy",
    "AdFormat",
    "AdStatus",
    "PlacementType",
    "PlacementStatus",
    "SpendCategory",

    # Geolocation Analytics models (implemented in Phase 2.2 - Analytics Dashboard)
    "GeolocationAnalytics",
    "ProviderPerformanceMetrics",
    "GeolocationRoutingMetrics",
    "ConversionAnalytics",
    "AnalyticsPeriodType",
    "RoutingDecisionType",

    # A/B Testing models (implemented in Phase 2.3 - A/B Testing Framework)
    "ABTest",
    "ABTestAssignment",
    "ABTestResult",
    "ABTestAnalysis",
    "RoutingStrategy",
    "ABTestStatus",
    "ABTestType",
    "StatisticalSignificance",

    # Review models (implemented in Task 4.4.1)
    "Review",
    "ReviewResponse",
    "ReviewModeration",
    "ReviewAnalytics",
    "ReviewStatus",
    "ResponseStatus",
    "ModerationAction",

    # WebSocket models (implemented in Task 6.1.1)
    "WebSocketConnection",
    "WebSocketEvent",
    "WebSocketConnectionMetrics",
    "UserPresence",
    "ConnectionStatus",
    "EventType",
    "EventPriority",

    # Real-time Synchronization models (implemented in Task 6.1.2)
    "DataSyncRecord",
    "SyncConflict",
    "SyncBatch",
    "SyncMetrics",
    "SyncStatus",
    "ConflictResolutionStrategy",
    "SyncPriority",
    "ChangeType",
    "SyncScope",

    # Analytics models (implemented in Phase 7.1 & 7.2 - Analytics Dashboard System & Performance Monitoring)
    "UserAnalytics",
    "VendorAnalytics",
    "BookingAnalytics",
    "SystemMetrics",
    "DashboardWidget",
    "KPIDefinition",
    "AnalyticsTimeframe",
    "MetricType",
    "DashboardWidgetType",
    "PerformanceMetrics",
    "SystemHealth",
    "PerformanceMetricType",
    "SystemHealthStatus",

    # Scaling models (implemented in Phase 7.3.3 - Horizontal Scaling & Load Balancing)
    "ScalingMetrics",
    "AutoScalingPolicy",
    "LoadBalancerConfig",
    "ContainerMetrics",
    "ScalingEvent",
    "ScalingTriggerType",
    "ScalingDirection",
    "LoadBalancerStrategy",
    "ContainerStatus",
    "ScalingEventType",

    # CDN models (implemented in Phase 7.3.4 - CDN Optimization & Asset Delivery)
    "CDNConfiguration",
    "AssetOptimization",
    "AssetBundle",
    "CDNMetrics",
    "AssetDelivery",
    "CDNProvider",
    "AssetType",
    "OptimizationType",
    "DeliveryStatus",

    # TODO: Add other models as they are implemented
    # Promotional models
    # "Campaign",
    # "CampaignMetrics",
    # "Advertisement",
    # "PromotionalCode",
]