"""
Tests for base schema framework and validation utilities.

This module tests the foundational schema components:
- Base schema classes with optimization
- Validation mixins and utilities
- Custom field types and constraints
- Performance monitoring integration
"""

import pytest
from datetime import datetime, timezone, timedelta
from uuid import uuid4, UUID
from typing import Dict, Any

from pydantic import ValidationError, Field

from app.schemas.base import (
    BaseSchema, OptimizedConfig, TimestampMixin, UUIDMixin,
    PaginationMixin, SearchMixin, MetadataMixin,
    PhoneNumber, CountryCode, CurrencyCode, LanguageCode,
    PositiveInt, NonNegativeInt, PositiveFloat, NonNegativeFloat,
    Percentage, NonEmptyStr, ShortStr, MediumStr, LongStr,
    ValidationError as CustomValidationError
)


class TestBaseSchema:
    """Test base schema functionality."""
    
    def test_base_schema_creation(self):
        """Test basic schema creation and configuration."""
        
        class TestSchema(BaseSchema):
            name: str = Field(..., description="Test name")
            value: int = Field(..., description="Test value")
        
        data = {"name": "test", "value": 42}
        schema = TestSchema(**data)
        
        assert schema.name == "test"
        assert schema.value == 42
        assert hasattr(schema, '_created_at')
    
    def test_optimized_serialization(self):
        """Test optimized dict and JSON serialization."""
        
        class TestSchema(BaseSchema):
            name: str
            value: int
            optional_field: str = None
        
        data = {"name": "test", "value": 42}
        schema = TestSchema(**data)
        
        # Test dict serialization with exclude_none
        serialized = schema.dict()
        assert "optional_field" not in serialized  # Should be excluded
        assert serialized["name"] == "test"
        assert serialized["value"] == 42
        
        # Test JSON serialization
        json_str = schema.json()
        assert "test" in json_str
        assert "42" in json_str
        assert "optional_field" not in json_str
    
    def test_performance_tracking(self):
        """Test that performance tracking works without errors."""
        
        class TestSchema(BaseSchema):
            name: str
            items: list = Field(default_factory=list)
        
        # Create schema with large data to potentially trigger slow serialization warning
        large_data = {
            "name": "test",
            "items": [f"item_{i}" for i in range(1000)]
        }
        
        schema = TestSchema(**large_data)
        
        # Should complete without errors (warning may be logged)
        serialized = schema.dict()
        assert len(serialized["items"]) == 1000


class TestTimestampMixin:
    """Test timestamp mixin functionality."""
    
    def test_timestamp_creation(self):
        """Test automatic timestamp creation."""
        
        class TestSchema(BaseSchema, TimestampMixin):
            name: str
        
        schema = TestSchema(name="test")
        
        assert schema.created_at is not None
        assert isinstance(schema.created_at, datetime)
        assert schema.created_at.tzinfo is not None  # Should be timezone-aware
        assert schema.updated_at is None  # Should be None by default
    
    def test_timestamp_validation(self):
        """Test timestamp field validation."""
        
        class TestSchema(BaseSchema, TimestampMixin):
            name: str
        
        # Test with ISO string
        data = {
            "name": "test",
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-02T12:00:00+00:00"
        }
        
        schema = TestSchema(**data)
        
        assert isinstance(schema.created_at, datetime)
        assert isinstance(schema.updated_at, datetime)
        assert schema.created_at.tzinfo is not None
        assert schema.updated_at.tzinfo is not None
    
    def test_invalid_timestamp(self):
        """Test invalid timestamp handling."""
        
        class TestSchema(BaseSchema, TimestampMixin):
            name: str
        
        # Test with invalid timestamp
        data = {
            "name": "test",
            "created_at": "invalid-date"
        }
        
        with pytest.raises(ValidationError):
            TestSchema(**data)


class TestUUIDMixin:
    """Test UUID mixin functionality."""
    
    def test_uuid_validation(self):
        """Test UUID field validation."""
        
        class TestSchema(BaseSchema, UUIDMixin):
            name: str
        
        test_uuid = uuid4()
        
        # Test with UUID object
        schema1 = TestSchema(name="test", id=test_uuid)
        assert schema1.id == test_uuid
        assert isinstance(schema1.id, UUID)
        
        # Test with UUID string
        schema2 = TestSchema(name="test", id=str(test_uuid))
        assert schema2.id == test_uuid
        assert isinstance(schema2.id, UUID)
    
    def test_invalid_uuid(self):
        """Test invalid UUID handling."""
        
        class TestSchema(BaseSchema, UUIDMixin):
            name: str
        
        # Test with invalid UUID string
        data = {
            "name": "test",
            "id": "invalid-uuid"
        }
        
        with pytest.raises(ValidationError):
            TestSchema(**data)


class TestPaginationMixin:
    """Test pagination mixin functionality."""
    
    def test_pagination_defaults(self):
        """Test pagination default values."""
        
        class TestSchema(BaseSchema, PaginationMixin):
            pass
        
        schema = TestSchema()
        
        assert schema.page == 1
        assert schema.size == 20
        assert schema.offset == 0  # (1 - 1) * 20
    
    def test_pagination_calculation(self):
        """Test pagination offset calculation."""
        
        class TestSchema(BaseSchema, PaginationMixin):
            pass
        
        schema = TestSchema(page=3, size=10)
        
        assert schema.page == 3
        assert schema.size == 10
        assert schema.offset == 20  # (3 - 1) * 10
    
    def test_pagination_validation(self):
        """Test pagination parameter validation."""
        
        class TestSchema(BaseSchema, PaginationMixin):
            pass
        
        # Test invalid page number
        with pytest.raises(ValidationError):
            TestSchema(page=0)  # Must be >= 1
        
        with pytest.raises(ValidationError):
            TestSchema(page=10001)  # Must be <= 10000
        
        # Test invalid page size
        with pytest.raises(ValidationError):
            TestSchema(size=0)  # Must be >= 1
        
        with pytest.raises(ValidationError):
            TestSchema(size=101)  # Must be <= 100


class TestSearchMixin:
    """Test search mixin functionality."""
    
    def test_search_query_validation(self):
        """Test search query validation and cleaning."""
        
        class TestSchema(BaseSchema, SearchMixin):
            pass
        
        # Test query cleaning
        schema = TestSchema(q="  multiple   spaces  ")
        assert schema.q == "multiple spaces"  # Whitespace normalized
        
        # Test sort parameters
        schema = TestSchema(sort_by="created_at", sort_order="desc")
        assert schema.sort_by == "created_at"
        assert schema.sort_order == "desc"
    
    def test_search_security_validation(self):
        """Test search query security validation."""
        
        class TestSchema(BaseSchema, SearchMixin):
            pass
        
        # Test invalid characters
        with pytest.raises(ValidationError):
            TestSchema(q="<script>alert('xss')</script>")
        
        with pytest.raises(ValidationError):
            TestSchema(q='SELECT * FROM users WHERE "1"="1"')
    
    def test_sort_field_validation(self):
        """Test sort field name validation."""
        
        class TestSchema(BaseSchema, SearchMixin):
            pass
        
        # Valid field names
        valid_fields = ["name", "created_at", "user_id", "field_name_123"]
        for field in valid_fields:
            schema = TestSchema(sort_by=field)
            assert schema.sort_by == field
        
        # Invalid field names
        invalid_fields = ["123invalid", "field-name", "field.name", "field name"]
        for field in invalid_fields:
            with pytest.raises(ValidationError):
                TestSchema(sort_by=field)


class TestMetadataMixin:
    """Test metadata mixin functionality."""
    
    def test_metadata_defaults(self):
        """Test metadata default values."""
        
        class TestSchema(BaseSchema, MetadataMixin):
            pass
        
        schema = TestSchema()
        
        assert schema.metadata == {}
        assert schema.tags == []
    
    def test_metadata_validation(self):
        """Test metadata size validation."""
        
        class TestSchema(BaseSchema, MetadataMixin):
            pass
        
        # Test large metadata
        large_metadata = {"key": "x" * 10000}  # Very large value
        
        with pytest.raises(ValidationError) as exc_info:
            TestSchema(metadata=large_metadata)
        
        assert "too large" in str(exc_info.value).lower()
    
    def test_tags_validation(self):
        """Test tags validation and cleaning."""
        
        class TestSchema(BaseSchema, MetadataMixin):
            pass
        
        # Test tag cleaning and validation
        tags = ["Valid_Tag", "another-tag", "UPPERCASE", "invalid tag with spaces", "123invalid", "x" * 60]
        
        schema = TestSchema(tags=tags)
        
        # Should only keep valid tags
        valid_tags = [tag for tag in schema.tags if tag in ["valid_tag", "another-tag", "uppercase"]]
        assert len(valid_tags) >= 3  # At least the valid ones should be kept
        
        # Test too many tags
        too_many_tags = [f"tag_{i}" for i in range(60)]
        
        with pytest.raises(ValidationError):
            TestSchema(tags=too_many_tags)


class TestCustomFieldTypes:
    """Test custom field types and constraints."""
    
    def test_phone_number_validation(self):
        """Test phone number field type."""
        
        class TestSchema(BaseSchema):
            phone: PhoneNumber
        
        # Valid phone numbers
        valid_phones = ["+1234567890", "+234567890123", "1234567890"]
        for phone in valid_phones:
            schema = TestSchema(phone=phone)
            assert schema.phone == phone
        
        # Invalid phone numbers
        invalid_phones = ["123", "abc123", "+", "123456789012345678"]
        for phone in invalid_phones:
            with pytest.raises(ValidationError):
                TestSchema(phone=phone)
    
    def test_country_code_validation(self):
        """Test country code field type."""
        
        class TestSchema(BaseSchema):
            country: CountryCode
        
        # Valid country codes
        valid_codes = ["NG", "US", "GB", "CA"]
        for code in valid_codes:
            schema = TestSchema(country=code)
            assert schema.country == code
        
        # Invalid country codes
        invalid_codes = ["ng", "USA", "123", "N", "NGR"]
        for code in invalid_codes:
            with pytest.raises(ValidationError):
                TestSchema(country=code)
    
    def test_currency_code_validation(self):
        """Test currency code field type."""
        
        class TestSchema(BaseSchema):
            currency: CurrencyCode
        
        # Valid currency codes
        valid_codes = ["NGN", "USD", "EUR", "GBP"]
        for code in valid_codes:
            schema = TestSchema(currency=code)
            assert schema.currency == code
        
        # Invalid currency codes
        invalid_codes = ["ngn", "DOLLAR", "12", "NG", "NGNN"]
        for code in invalid_codes:
            with pytest.raises(ValidationError):
                TestSchema(currency=code)
    
    def test_numeric_constraints(self):
        """Test numeric constraint types."""
        
        class TestSchema(BaseSchema):
            positive_int: PositiveInt
            non_negative_int: NonNegativeInt
            positive_float: PositiveFloat
            non_negative_float: NonNegativeFloat
            percentage: Percentage
        
        # Valid values
        schema = TestSchema(
            positive_int=5,
            non_negative_int=0,
            positive_float=1.5,
            non_negative_float=0.0,
            percentage=50.5
        )
        
        assert schema.positive_int == 5
        assert schema.non_negative_int == 0
        assert schema.positive_float == 1.5
        assert schema.non_negative_float == 0.0
        assert schema.percentage == 50.5
        
        # Invalid values
        with pytest.raises(ValidationError):
            TestSchema(
                positive_int=0,  # Must be > 0
                non_negative_int=0,
                positive_float=1.5,
                non_negative_float=0.0,
                percentage=50.5
            )
        
        with pytest.raises(ValidationError):
            TestSchema(
                positive_int=5,
                non_negative_int=0,
                positive_float=1.5,
                non_negative_float=0.0,
                percentage=150.0  # Must be <= 100
            )
    
    def test_string_constraints(self):
        """Test string constraint types."""
        
        class TestSchema(BaseSchema):
            non_empty: NonEmptyStr
            short: ShortStr
            medium: MediumStr
            long: LongStr
        
        # Valid strings
        schema = TestSchema(
            non_empty="test",
            short="x" * 100,
            medium="x" * 500,
            long="x" * 1000
        )
        
        assert schema.non_empty == "test"
        assert len(schema.short) == 100
        assert len(schema.medium) == 500
        assert len(schema.long) == 1000
        
        # Test whitespace stripping
        schema = TestSchema(
            non_empty="  test  ",
            short="  short  ",
            medium="  medium  ",
            long="  long  "
        )
        
        assert schema.non_empty == "test"
        assert schema.short == "short"
        assert schema.medium == "medium"
        assert schema.long == "long"
        
        # Invalid strings
        with pytest.raises(ValidationError):
            TestSchema(
                non_empty="",  # Cannot be empty
                short="test",
                medium="test",
                long="test"
            )
        
        with pytest.raises(ValidationError):
            TestSchema(
                non_empty="test",
                short="x" * 300,  # Too long for short
                medium="test",
                long="test"
            )


class TestCustomValidationError:
    """Test custom validation error functionality."""
    
    def test_validation_error_creation(self):
        """Test custom validation error creation."""
        error = CustomValidationError(
            message="Test error",
            field="test_field",
            code="TEST_ERROR"
        )
        
        assert error.message == "Test error"
        assert error.field == "test_field"
        assert error.code == "TEST_ERROR"
        assert isinstance(error.timestamp, datetime)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
