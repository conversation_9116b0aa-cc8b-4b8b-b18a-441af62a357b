"""
Unit tests for review schemas.

This module provides comprehensive unit tests for review-related Pydantic V2 schemas:
- ReviewCreateSchema: Review creation validation with booking and content validation
- ReviewUpdateSchema: Review update validation with partial field updates
- ReviewResponseSchema: Review response serialization with computed fields
- ReviewResponseCreateSchema: Vendor response creation validation
- ReviewModerationCreateSchema: AI moderation workflow validation
- ReviewAnalyticsSchema: Performance analytics validation and serialization
- Schema validation rules and error handling

Implements Task 4.4.1 Phase 6 requirements with >85% test coverage.
"""

import pytest
from datetime import datetime, date
from decimal import Decimal
from pydantic import ValidationError

from app.schemas.review_schemas import (
    ReviewCreateSchema, ReviewUpdateSchema, ReviewResponseSchema,
    ReviewResponseCreateSchema, ReviewModerationCreateSchema,
    ReviewAnalyticsSchema, ReviewFilterSchema
)
from app.models.review_models import ModerationAction


class TestReviewCreateSchema:
    """Test ReviewCreateSchema validation."""

    def test_valid_review_creation(self):
        """Test valid review creation data."""
        review_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "Excellent Cultural Experience",
            "content": "The tour guide was knowledgeable and friendly. I learned so much about the local culture and history. Highly recommended!"
        }

        schema = ReviewCreateSchema(**review_data)

        assert schema.booking_id == 123
        assert schema.rating == 5
        assert schema.title == "Excellent Cultural Experience"
        assert schema.content == "The tour guide was knowledgeable and friendly. I learned so much about the local culture and history. Highly recommended!"

    def test_rating_validation(self):
        """Test rating field validation."""
        base_data = {
            "booking_id": 123,
            "title": "Test Review",
            "content": "This is a test review with sufficient content length."
        }

        # Valid ratings (1-5)
        for rating in [1, 2, 3, 4, 5]:
            review_data = {**base_data, "rating": rating}
            schema = ReviewCreateSchema(**review_data)
            assert schema.rating == rating

        # Invalid ratings
        for invalid_rating in [0, 6, -1, 10]:
            with pytest.raises(ValidationError) as exc_info:
                ReviewCreateSchema(**{**base_data, "rating": invalid_rating})
            # Check for Pydantic V2 validation messages
            assert any(msg in str(exc_info.value) for msg in [
                "Input should be greater than or equal to 1",
                "Input should be less than or equal to 5"
            ])

    def test_title_validation(self):
        """Test title field validation."""
        base_data = {
            "booking_id": 123,
            "rating": 5,
            "content": "This is a test review with sufficient content length."
        }

        # Valid title
        review_data = {**base_data, "title": "Great Experience"}
        schema = ReviewCreateSchema(**review_data)
        assert schema.title == "Great Experience"

        # Title too short
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "title": "Hi"})
        assert "String should have at least 5 characters" in str(exc_info.value)

        # Title too long
        long_title = "A" * 201
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "title": long_title})
        assert "String should have at most 200 characters" in str(exc_info.value)

    def test_content_validation(self):
        """Test content field validation."""
        base_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "Test Review"
        }

        # Valid content
        review_data = {**base_data, "content": "This is a valid review content with sufficient length."}
        schema = ReviewCreateSchema(**review_data)
        assert schema.content == "This is a valid review content with sufficient length."

        # Content too short
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "content": "Short"})
        assert "String should have at least 10 characters" in str(exc_info.value)

        # Content too long
        long_content = "A" * 2001
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "content": long_content})
        assert "String should have at most 2000 characters" in str(exc_info.value)

    def test_booking_id_validation(self):
        """Test booking_id field validation."""
        base_data = {
            "rating": 5,
            "title": "Test Review",
            "content": "This is a test review with sufficient content length."
        }

        # Valid booking_id
        review_data = {**base_data, "booking_id": 123}
        schema = ReviewCreateSchema(**review_data)
        assert schema.booking_id == 123

        # Invalid booking_id (negative)
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "booking_id": -1})
        assert "Input should be greater than 0" in str(exc_info.value)

        # Invalid booking_id (zero)
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(**{**base_data, "booking_id": 0})
        assert "Input should be greater than 0" in str(exc_info.value)

    def test_optional_fields(self):
        """Test optional fields in review creation."""
        base_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "Test Review",
            "content": "This is a test review with sufficient content length."
        }

        # Test basic creation without optional fields
        schema = ReviewCreateSchema(**base_data)
        assert schema.booking_id == 123
        assert schema.rating == 5
        assert schema.title == "Test Review"
        assert schema.content == "This is a test review with sufficient content length."


class TestReviewUpdateSchema:
    """Test ReviewUpdateSchema validation."""

    def test_partial_update_validation(self):
        """Test partial update validation."""
        # Update only rating
        update_data = {"rating": 4}
        schema = ReviewUpdateSchema(**update_data)
        assert schema.rating == 4
        assert schema.title is None
        assert schema.content is None

        # Update only title
        update_data = {"title": "Updated Title"}
        schema = ReviewUpdateSchema(**update_data)
        assert schema.title == "Updated Title"
        assert schema.rating is None
        assert schema.content is None

        # Update multiple fields
        update_data = {
            "rating": 3,
            "title": "Updated Review",
            "content": "This is an updated review content with sufficient length."
        }
        schema = ReviewUpdateSchema(**update_data)
        assert schema.rating == 3
        assert schema.title == "Updated Review"
        assert schema.content == "This is an updated review content with sufficient length."

    def test_update_validation_rules(self):
        """Test validation rules for update fields."""
        # Invalid rating
        with pytest.raises(ValidationError) as exc_info:
            ReviewUpdateSchema(rating=6)
        assert "Input should be less than or equal to 5" in str(exc_info.value)

        # Invalid title length
        with pytest.raises(ValidationError) as exc_info:
            ReviewUpdateSchema(title="Hi")
        assert "String should have at least 5 characters" in str(exc_info.value)

        # Invalid content length
        with pytest.raises(ValidationError) as exc_info:
            ReviewUpdateSchema(content="Short")
        assert "String should have at least 10 characters" in str(exc_info.value)

    def test_empty_update_validation(self):
        """Test empty update validation."""
        # Empty update should be valid
        schema = ReviewUpdateSchema()
        assert schema.rating is None
        assert schema.title is None
        assert schema.content is None


class TestReviewResponseCreateSchema:
    """Test ReviewResponseCreateSchema validation."""

    def test_valid_response_creation(self):
        """Test valid response creation data."""
        response_data = {
            "review_id": 123,
            "content": "Thank you for your detailed feedback! We're glad you enjoyed the cultural experience and learned about our local traditions."
        }

        schema = ReviewResponseCreateSchema(**response_data)

        assert schema.review_id == 123
        assert schema.content == "Thank you for your detailed feedback! We're glad you enjoyed the cultural experience and learned about our local traditions."

    def test_response_content_validation(self):
        """Test response content validation."""
        base_data = {"review_id": 123}

        # Valid content
        response_data = {**base_data, "content": "Thank you for your feedback!"}
        schema = ReviewResponseCreateSchema(**response_data)
        assert schema.content == "Thank you for your feedback!"

        # Content too short
        with pytest.raises(ValidationError) as exc_info:
            ReviewResponseCreateSchema(**{**base_data, "content": "Hi"})
        assert "String should have at least 10 characters" in str(exc_info.value)

        # Content too long
        long_content = "A" * 1001
        with pytest.raises(ValidationError) as exc_info:
            ReviewResponseCreateSchema(**{**base_data, "content": long_content})
        assert "String should have at most 1000 characters" in str(exc_info.value)

    def test_review_id_validation(self):
        """Test review_id field validation."""
        base_data = {"content": "Thank you for your feedback!"}

        # Valid review_id
        response_data = {**base_data, "review_id": 123}
        schema = ReviewResponseCreateSchema(**response_data)
        assert schema.review_id == 123

        # Invalid review_id (negative)
        with pytest.raises(ValidationError) as exc_info:
            ReviewResponseCreateSchema(**{**base_data, "review_id": -1})
        assert "Input should be greater than 0" in str(exc_info.value)


class TestReviewModerationCreateSchema:
    """Test ReviewModerationCreateSchema validation."""

    def test_valid_moderation_creation(self):
        """Test valid moderation creation data."""
        moderation_data = {
            "review_id": 123,
            "action": "approve",
            "ai_confidence_score": 0.95,
            "manual_review_required": False,
            "reason": "Content appears appropriate and helpful",
            "ai_analysis_results": {
                "sentiment": "positive",
                "toxicity_score": 0.02,
                "spam_probability": 0.01
            }
        }

        schema = ReviewModerationCreateSchema(**moderation_data)

        assert schema.review_id == 123
        assert schema.action.value == "approve"
        assert schema.ai_confidence_score == 0.95
        assert schema.manual_review_required is False
        assert schema.reason == "Content appears appropriate and helpful"
        assert schema.ai_analysis_results["sentiment"] == "positive"

    def test_action_validation(self):
        """Test action field validation."""
        base_data = {
            "review_id": 123,
            "ai_confidence_score": 0.85
        }

        # Valid actions
        valid_actions = ["approve", "reject", "flag", "request_edit", "escalate"]
        for action in valid_actions:
            moderation_data = {**base_data, "action": action}
            schema = ReviewModerationCreateSchema(**moderation_data)
            assert schema.action.value == action

        # Invalid action
        with pytest.raises(ValidationError) as exc_info:
            ReviewModerationCreateSchema(**{**base_data, "action": "invalid_action"})
        assert "Input should be" in str(exc_info.value) and "approve" in str(exc_info.value)

    def test_confidence_score_validation(self):
        """Test AI confidence score validation."""
        base_data = {
            "review_id": 123,
            "action": "approve"
        }

        # Valid confidence scores (0.0 to 1.0)
        for score in [0.0, 0.25, 0.5, 0.75, 1.0]:
            moderation_data = {**base_data, "ai_confidence_score": score}
            schema = ReviewModerationCreateSchema(**moderation_data)
            assert schema.ai_confidence_score == score

        # Invalid confidence scores
        with pytest.raises(ValidationError) as exc_info:
            ReviewModerationCreateSchema(**{**base_data, "ai_confidence_score": -0.1})
        assert "Input should be greater than or equal to 0" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            ReviewModerationCreateSchema(**{**base_data, "ai_confidence_score": 1.1})
        assert "Input should be less than or equal to 1" in str(exc_info.value)

    def test_ai_analysis_results_validation(self):
        """Test AI analysis results validation."""
        base_data = {
            "review_id": 123,
            "action": "approve",
            "ai_confidence_score": 0.9
        }

        # Valid AI analysis results
        ai_results = {
            "sentiment": "positive",
            "toxicity_score": 0.1,
            "spam_probability": 0.05,
            "language_detected": "en"
        }

        moderation_data = {**base_data, "ai_analysis_results": ai_results}
        schema = ReviewModerationCreateSchema(**moderation_data)
        assert schema.ai_analysis_results == ai_results

        # Empty AI analysis results should be valid
        schema = ReviewModerationCreateSchema(**base_data)
        assert schema.ai_analysis_results is None


class TestReviewAnalyticsSchema:
    """Test ReviewAnalyticsSchema validation."""

    def test_valid_analytics_creation(self):
        """Test valid analytics creation data."""
        analytics_data = {
            "id": 101,
            "created_at": datetime(2025, 2, 1, 0, 0, 0),
            "updated_at": datetime(2025, 2, 1, 0, 0, 0),
            "vendor_id": 123,
            "period_start": "2025-01-01",
            "period_end": "2025-01-31",
            "total_reviews": 25,
            "average_rating": "4.6",
            "rating_distribution": {
                "1": 0, "2": 1, "3": 2, "4": 8, "5": 14
            },
            "sentiment_breakdown": {
                "positive": 22, "neutral": 2, "negative": 1
            },
            "response_rate": "0.88",
            "average_response_time": 6,
            "verified_reviews_count": 24,
            "helpful_votes_total": 156,
            "reported_reviews_count": 0
        }

        schema = ReviewAnalyticsSchema(**analytics_data)

        assert schema.vendor_id == 123
        assert schema.total_reviews == 25
        assert schema.average_rating == Decimal("4.6")
        assert schema.rating_distribution["5"] == 14
        assert schema.sentiment_breakdown["positive"] == 22

    def test_rating_distribution_validation(self):
        """Test rating distribution validation."""
        base_data = {
            "id": 102,
            "created_at": datetime(2025, 2, 1, 0, 0, 0),
            "updated_at": datetime(2025, 2, 1, 0, 0, 0),
            "vendor_id": 123,
            "period_start": "2025-01-01",
            "period_end": "2025-01-31",
            "total_reviews": 25,
            "verified_reviews_count": 20,
            "helpful_votes_total": 100,
            "reported_reviews_count": 1
        }

        # Valid rating distribution
        rating_dist = {"1": 2, "2": 3, "3": 5, "4": 10, "5": 5}
        analytics_data = {**base_data, "rating_distribution": rating_dist}
        schema = ReviewAnalyticsSchema(**analytics_data)
        assert schema.rating_distribution == rating_dist

        # Test that rating distribution is optional
        analytics_data_no_dist = {**base_data}
        schema_no_dist = ReviewAnalyticsSchema(**analytics_data_no_dist)
        assert schema_no_dist.rating_distribution is None

    def test_sentiment_breakdown_validation(self):
        """Test sentiment breakdown validation."""
        base_data = {
            "id": 103,
            "created_at": datetime(2025, 2, 1, 0, 0, 0),
            "updated_at": datetime(2025, 2, 1, 0, 0, 0),
            "vendor_id": 123,
            "period_start": "2025-01-01",
            "period_end": "2025-01-31",
            "total_reviews": 25,
            "verified_reviews_count": 20,
            "helpful_votes_total": 100,
            "reported_reviews_count": 1
        }

        # Valid sentiment breakdown
        sentiment_breakdown = {"positive": 20, "neutral": 3, "negative": 2}
        analytics_data = {**base_data, "sentiment_breakdown": sentiment_breakdown}
        schema = ReviewAnalyticsSchema(**analytics_data)
        assert schema.sentiment_breakdown == sentiment_breakdown

        # Test that sentiment breakdown is optional
        analytics_data_no_sentiment = {**base_data}
        schema_no_sentiment = ReviewAnalyticsSchema(**analytics_data_no_sentiment)
        assert schema_no_sentiment.sentiment_breakdown is None

    def test_decimal_field_validation(self):
        """Test decimal field validation."""
        base_data = {
            "id": 104,
            "created_at": datetime(2025, 2, 1, 0, 0, 0),
            "updated_at": datetime(2025, 2, 1, 0, 0, 0),
            "vendor_id": 123,
            "period_start": "2025-01-01",
            "period_end": "2025-01-31",
            "total_reviews": 25,
            "verified_reviews_count": 20,
            "helpful_votes_total": 100,
            "reported_reviews_count": 1
        }

        # Valid decimal values
        analytics_data = {
            **base_data,
            "average_rating": "4.567",
            "response_rate": "0.856"
        }
        schema = ReviewAnalyticsSchema(**analytics_data)
        assert schema.average_rating == Decimal("4.567")
        assert schema.response_rate == Decimal("0.856")

        # Test that decimal fields are optional
        analytics_data_no_decimals = {**base_data}
        schema_no_decimals = ReviewAnalyticsSchema(**analytics_data_no_decimals)
        assert schema_no_decimals.average_rating is None
        assert schema_no_decimals.response_rate is None


class TestReviewFilterSchema:
    """Test ReviewFilterSchema validation."""

    def test_valid_filter_creation(self):
        """Test valid filter creation."""
        filter_data = {
            "search_query": "excellent service",
            "vendor_id": 123,
            "service_id": 456,
            "min_rating": 4,
            "max_rating": 5,
            "status": "approved",
            "date_from": "2025-01-01",
            "date_to": "2025-01-31",
            "sort_by": "created_at",
            "sort_order": "desc"
        }

        schema = ReviewFilterSchema(**filter_data)

        assert schema.search_query == "excellent service"
        assert schema.vendor_id == 123
        assert schema.service_id == 456
        assert schema.min_rating == 4
        assert schema.max_rating == 5
        assert schema.status.value == "approved"

    def test_rating_range_validation(self):
        """Test rating range validation."""
        base_data = {}

        # Valid rating range
        filter_data = {**base_data, "min_rating": 3, "max_rating": 5}
        schema = ReviewFilterSchema(**filter_data)
        assert schema.min_rating == 3
        assert schema.max_rating == 5

        # Invalid rating range (min > max)
        with pytest.raises(ValidationError) as exc_info:
            ReviewFilterSchema(**{**base_data, "min_rating": 5, "max_rating": 3})
        assert "max_rating must be greater than or equal to min_rating" in str(exc_info.value)

        # Invalid rating values
        with pytest.raises(ValidationError) as exc_info:
            ReviewFilterSchema(**{**base_data, "min_rating": 0})
        assert "Input should be greater than or equal to 1" in str(exc_info.value)

    def test_date_range_validation(self):
        """Test date range validation."""
        base_data = {}

        # Valid date range
        filter_data = {**base_data, "date_from": "2025-01-01", "date_to": "2025-01-31"}
        schema = ReviewFilterSchema(**filter_data)
        assert schema.date_from == date(2025, 1, 1)
        assert schema.date_to == date(2025, 1, 31)

        # Invalid date range (from > to)
        with pytest.raises(ValidationError) as exc_info:
            ReviewFilterSchema(**{**base_data, "date_from": "2025-01-31", "date_to": "2025-01-01"})
        assert "date_to must be after date_from" in str(exc_info.value)

    def test_sort_validation(self):
        """Test sort field and order validation."""
        base_data = {}

        # Valid sort options
        valid_sort_fields = ["created_at", "rating", "helpful_count"]
        valid_sort_orders = ["asc", "desc"]

        for sort_by in valid_sort_fields:
            for sort_order in valid_sort_orders:
                filter_data = {**base_data, "sort_by": sort_by, "sort_order": sort_order}
                schema = ReviewFilterSchema(**filter_data)
                assert schema.sort_by == sort_by
                assert schema.sort_order == sort_order

        # Invalid sort field
        with pytest.raises(ValidationError) as exc_info:
            ReviewFilterSchema(**{**base_data, "sort_by": "invalid_field"})
        assert "String should match pattern" in str(exc_info.value)

        # Invalid sort order
        with pytest.raises(ValidationError) as exc_info:
            ReviewFilterSchema(**{**base_data, "sort_order": "invalid_order"})
        assert "String should match pattern" in str(exc_info.value)


class TestReviewResponseSchema:
    """Test ReviewResponseSchema serialization."""

    def test_response_serialization(self):
        """Test review response serialization."""
        response_data = {
            "id": 123,
            "booking_id": 456,
            "customer_id": 789,
            "vendor_id": 101,
            "service_id": 202,
            "rating": 5,
            "title": "Amazing cultural experience!",
            "content": "The tour guide was knowledgeable and friendly...",
            "status": "approved",
            "is_verified_purchase": True,
            "helpful_count": 12,
            "reported_count": 0,
            "sentiment_score": 0.85,
            "language_code": "en",
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

        schema = ReviewResponseSchema(**response_data)

        assert schema.id == 123
        assert schema.rating == 5
        assert schema.title == "Amazing cultural experience!"
        assert schema.status.value == "approved"
        assert schema.is_verified_purchase is True

    def test_computed_fields(self):
        """Test computed fields in response schema."""
        response_data = {
            "id": 123,
            "booking_id": 456,
            "customer_id": 789,
            "vendor_id": 101,
            "service_id": 202,
            "rating": 5,
            "title": "Great service",
            "content": "Excellent experience",
            "status": "approved",
            "is_verified_purchase": True,
            "helpful_count": 5,
            "reported_count": 0,
            "sentiment_score": 0.85,
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

        schema = ReviewResponseSchema(**response_data)

        # Test computed fields
        assert schema.is_positive_review is True  # rating >= 4
        assert schema.has_vendor_response is False  # default


class TestSchemaValidationEdgeCases:
    """Test edge cases and error scenarios."""

    def test_missing_required_fields(self):
        """Test validation with missing required fields."""
        # Missing booking_id in ReviewCreateSchema
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(
                rating=5,
                title="Test Review",
                content="Test content"
            )
        assert "booking_id" in str(exc_info.value)

        # Missing rating in ReviewCreateSchema
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(
                booking_id=123,
                title="Test Review",
                content="Test content"
            )
        assert "rating" in str(exc_info.value)

    def test_field_type_validation(self):
        """Test field type validation."""
        # Invalid type for rating (string instead of int)
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(
                booking_id=123,
                rating="five",
                title="Test Review",
                content="Test content"
            )
        assert "Input should be a valid integer" in str(exc_info.value)

        # Invalid type for booking_id (string instead of int)
        with pytest.raises(ValidationError) as exc_info:
            ReviewCreateSchema(
                booking_id="invalid",
                rating=5,
                title="Test Review",
                content="Test content"
            )
        assert "Input should be a valid integer" in str(exc_info.value)

    def test_whitespace_handling(self):
        """Test whitespace handling in string fields."""
        # Leading/trailing whitespace should be stripped
        review_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "  Test Review  ",
            "content": "  This is test content with whitespace.  "
        }

        schema = ReviewCreateSchema(**review_data)

        assert schema.title == "Test Review"
        assert schema.content == "This is test content with whitespace."

    def test_unicode_content_validation(self):
        """Test unicode content validation."""
        # Unicode content should be valid
        review_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "Excelente experiencia cultural! 🎭",
            "content": "La guía turística fue muy conocedora y amigable. Aprendí mucho sobre la cultura e historia local. ¡Muy recomendado! 👍"
        }

        schema = ReviewCreateSchema(**review_data)

        assert "🎭" in schema.title
        assert "👍" in schema.content
        assert "Excelente" in schema.title

    def test_json_serialization(self):
        """Test JSON serialization of schemas."""
        review_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "Test Review",
            "content": "This is a test review content."
        }

        schema = ReviewCreateSchema(**review_data)
        json_data = schema.model_dump()

        assert json_data["booking_id"] == 123
        assert json_data["rating"] == 5
        assert json_data["title"] == "Test Review"
        assert json_data["content"] == "This is a test review content."
