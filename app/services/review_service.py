"""
Review Service for Culture Connect Backend API.

This module provides comprehensive review business logic orchestration including:
- Complete review lifecycle management (creation, validation, status workflow)
- Booking validation ensuring only completed bookings can be reviewed
- Automatic AI moderation triggering and workflow management
- Integration with email/push notification services for review events
- Performance optimization with <500ms review creation targets
- Comprehensive RBAC integration and audit logging

Implements Task 4.4.1 Phase 4 requirements for review service implementation with
production-grade business logic following established BaseService patterns.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.review_repository import ReviewRepository
from app.repositories.booking_repository import BookingRepository
from app.models.review_models import Review, ReviewStatus
from app.models.booking import Booking, BookingStatus
from app.schemas.review_schemas import (
    ReviewCreateSchema, ReviewUpdateSchema, ReviewResponseSchema,
    ReviewFilterSchema, ReviewListResponseSchema, ReviewSummarySchema
)
from app.repositories.base import PaginationParams, QueryResult
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.services.review_moderation_service import ReviewModerationService
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector
from app.core.config import settings

logger = logging.getLogger(__name__)


class ReviewService(BaseService[Review, ReviewRepository]):
    """
    Review service for business logic orchestration.

    Provides comprehensive review management including:
    - Review creation with booking validation and duplicate prevention
    - Automatic AI moderation triggering and workflow management
    - Status management with audit logging and notifications
    - Advanced search and filtering capabilities
    - Integration with email/push notification services
    - Performance optimization with <500ms creation targets
    """

    def __init__(self, db: AsyncSession):
        """Initialize review service with dependencies."""
        super().__init__(
            repository_class=ReviewRepository,
            model_class=Review,
            db_session=db
        )

        # Initialize dependent services
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)
        self.moderation_service = ReviewModerationService(db)

        # Initialize repositories
        self.booking_repository = BookingRepository(db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate review creation data with business rules.

        Args:
            data: Review creation data

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['booking_id', 'customer_id', 'rating', 'title', 'content']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate rating range
        rating = data.get('rating')
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            raise ValidationError("Rating must be an integer between 1 and 5")

        # Validate content length
        title = data.get('title', '').strip()
        content = data.get('content', '').strip()

        if len(title) < 5 or len(title) > 200:
            raise ValidationError("Title must be between 5 and 200 characters")

        if len(content) < 10 or len(content) > 2000:
            raise ValidationError("Content must be between 10 and 2000 characters")

        # Clean and return validated data
        return {
            **data,
            'title': title,
            'content': content
        }

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate review update data.

        Args:
            data: Review update data
            existing_id: Existing review ID

        Returns:
            Dict[str, Any]: Validated and processed data

        Raises:
            ValidationError: If validation fails
        """
        # Validate rating if provided
        if 'rating' in data:
            rating = data['rating']
            if not isinstance(rating, int) or rating < 1 or rating > 5:
                raise ValidationError("Rating must be an integer between 1 and 5")

        # Validate content length if provided
        if 'title' in data:
            title = data['title'].strip()
            if len(title) < 5 or len(title) > 200:
                raise ValidationError("Title must be between 5 and 200 characters")
            data['title'] = title

        if 'content' in data:
            content = data['content'].strip()
            if len(content) < 10 or len(content) > 2000:
                raise ValidationError("Content must be between 10 and 2000 characters")
            data['content'] = content

        return data

    async def create_review(
        self,
        customer_id: int,
        booking_id: int,
        review_data: ReviewCreateSchema,
        trigger_moderation: bool = True
    ) -> Review:
        """
        Create a new review with comprehensive validation and workflow automation.

        Performance target: <500ms for review creation including moderation trigger.

        Args:
            customer_id: Customer creating the review
            booking_id: Associated completed booking ID
            review_data: Review creation data
            trigger_moderation: Whether to trigger AI moderation (default: True)

        Returns:
            Created Review instance

        Raises:
            ValidationError: If review data is invalid
            ConflictError: If duplicate review exists
            NotFoundError: If booking not found or not eligible
            ServiceError: If creation fails
        """
        correlation = self.log_operation_start(
            "create_review",
            customer_id=customer_id,
            booking_id=booking_id,
            rating=review_data.rating
        )

        try:
            # Validate booking eligibility
            await self._validate_booking_eligibility(booking_id, customer_id)

            # Check for existing review
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)
                existing_review = await repository.get_review_by_booking(booking_id)

                if existing_review:
                    raise ConflictError(f"Review already exists for booking {booking_id}")

                # Prepare review data
                validated_data = await self.validate_create_data({
                    'booking_id': booking_id,
                    'customer_id': customer_id,
                    'rating': review_data.rating,
                    'title': review_data.title,
                    'content': review_data.content
                })

                # Create review
                review = await repository.create_review(
                    customer_id=customer_id,
                    booking_id=booking_id,
                    review_data=validated_data
                )

                # Trigger AI moderation if enabled
                if trigger_moderation:
                    await self._trigger_ai_moderation(review)

                # Send notifications
                await self._send_review_notifications(review, "created")

                # Track metrics
                metrics_collector.increment_counter(
                    "review_created",
                    tags={"rating": str(review.rating), "vendor_id": str(review.vendor_id)}
                )

                self.log_operation_success(
                    correlation,
                    f"Review created with ID: {review.id}, rating: {review.rating}/5"
                )

                return review

        except Exception as e:
            await self.handle_service_error(e, "create_review", {
                "customer_id": customer_id,
                "booking_id": booking_id,
                "rating": review_data.rating
            })

    async def update_review(
        self,
        review_id: int,
        customer_id: int,
        update_data: ReviewUpdateSchema,
        trigger_moderation: bool = True
    ) -> Review:
        """
        Update an existing review with validation and re-moderation.

        Performance target: <200ms for review updates.

        Args:
            review_id: Review ID to update
            customer_id: Customer updating the review
            update_data: Review update data
            trigger_moderation: Whether to trigger re-moderation

        Returns:
            Updated Review instance

        Raises:
            NotFoundError: If review not found
            ValidationError: If update data is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_review",
            review_id=review_id,
            customer_id=customer_id
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                # Get existing review
                review = await repository.get(review_id)
                if not review:
                    raise NotFoundError("Review", review_id)

                # Validate ownership
                if review.customer_id != customer_id:
                    raise ValidationError("Customer can only update their own reviews")

                # Validate review is editable
                if review.status not in [ReviewStatus.PENDING, ReviewStatus.APPROVED]:
                    raise ValidationError(f"Review with status {review.status} cannot be edited")

                # Prepare update data
                validated_data = await self.validate_update_data(
                    update_data.model_dump(exclude_none=True),
                    review_id
                )

                # Update review
                updated_review = await repository.update(review_id, validated_data)

                # Trigger re-moderation if content changed and enabled
                if trigger_moderation and ('title' in validated_data or 'content' in validated_data):
                    await self._trigger_ai_moderation(updated_review)

                # Send notifications
                await self._send_review_notifications(updated_review, "updated")

                self.log_operation_success(
                    correlation,
                    f"Review updated with ID: {review_id}"
                )

                return updated_review

        except Exception as e:
            await self.handle_service_error(e, "update_review", {
                "review_id": review_id,
                "customer_id": customer_id
            })

    async def get_review_by_id(self, review_id: int) -> Optional[Review]:
        """
        Get review by ID with related data.

        Performance target: <100ms for review retrieval.

        Args:
            review_id: Review ID

        Returns:
            Review instance or None if not found
        """
        correlation = self.log_operation_start("get_review_by_id", review_id=review_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)
                reviews = await repository.get_reviews_with_details([review_id])

                review = reviews[0] if reviews else None

                if review:
                    self.log_operation_success(correlation, f"Review found: {review_id}")
                else:
                    self.log_operation_success(correlation, f"Review not found: {review_id}")

                return review

        except Exception as e:
            await self.handle_service_error(e, "get_review_by_id", {"review_id": review_id})

    async def get_vendor_reviews(
        self,
        vendor_id: int,
        filters: Optional[ReviewFilterSchema] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """
        Get reviews for a specific vendor with filtering.

        Performance target: <200ms for vendor review queries.

        Args:
            vendor_id: Vendor ID
            filters: Optional review filters
            pagination: Pagination parameters

        Returns:
            Query result with vendor reviews
        """
        correlation = self.log_operation_start("get_vendor_reviews", vendor_id=vendor_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                # Apply filters
                status_filter = [filters.status] if filters and filters.status else [ReviewStatus.APPROVED]
                rating_filter = None
                if filters and (filters.min_rating or filters.max_rating):
                    rating_filter = (
                        filters.min_rating or 1,
                        filters.max_rating or 5
                    )

                date_range = None
                if filters and (filters.date_from or filters.date_to):
                    date_range = (filters.date_from, filters.date_to)

                result = await repository.get_vendor_reviews(
                    vendor_id=vendor_id,
                    status_filter=status_filter,
                    rating_filter=rating_filter,
                    date_range=date_range,
                    pagination=pagination
                )

                self.log_operation_success(
                    correlation,
                    f"Retrieved {len(result.items)} reviews for vendor {vendor_id}"
                )

                return result

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_reviews", {"vendor_id": vendor_id})

    async def search_reviews(
        self,
        search_query: str,
        filters: Optional[ReviewFilterSchema] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Review]:
        """
        Search reviews using full-text search with advanced filtering.

        Performance target: <200ms for search queries.

        Args:
            search_query: Text to search in title and content
            filters: Optional advanced filters
            pagination: Pagination parameters

        Returns:
            Query result with matching reviews
        """
        correlation = self.log_operation_start("search_reviews", search_query=search_query)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                result = await repository.search_reviews(
                    search_query=search_query,
                    filters=filters,
                    pagination=pagination
                )

                self.log_operation_success(
                    correlation,
                    f"Search returned {len(result.items)} reviews for query: {search_query}"
                )

                return result

        except Exception as e:
            await self.handle_service_error(e, "search_reviews", {"search_query": search_query})

    async def update_review_status(
        self,
        review_id: int,
        new_status: ReviewStatus,
        admin_id: Optional[int] = None,
        reason: Optional[str] = None
    ) -> Review:
        """
        Update review status with audit logging and notifications.

        Performance target: <100ms for status updates.

        Args:
            review_id: Review ID
            new_status: New review status
            admin_id: Optional admin performing the action
            reason: Optional reason for status change

        Returns:
            Updated Review instance

        Raises:
            NotFoundError: If review not found
            ValidationError: If status transition is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_review_status",
            review_id=review_id,
            new_status=new_status.value,
            admin_id=admin_id
        )

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                # Get existing review
                review = await repository.get(review_id)
                if not review:
                    raise NotFoundError("Review", review_id)

                old_status = review.status

                # Validate status transition
                self._validate_status_transition(old_status, new_status)

                # Update status
                updated_review = await repository.update_review_status(
                    review_id=review_id,
                    new_status=new_status,
                    moderation_reason=reason
                )

                # Send notifications for status changes
                await self._send_status_change_notifications(updated_review, old_status, new_status, admin_id)

                # Track metrics
                metrics_collector.increment_counter(
                    "review_status_updated",
                    tags={"old_status": old_status.value, "new_status": new_status.value}
                )

                self.log_operation_success(
                    correlation,
                    f"Review status updated: {old_status.value} → {new_status.value}"
                )

                return updated_review

        except Exception as e:
            await self.handle_service_error(e, "update_review_status", {
                "review_id": review_id,
                "new_status": new_status.value
            })

    async def mark_helpful(self, review_id: int, user_id: int) -> Review:
        """
        Mark a review as helpful.

        Performance target: <50ms for helpful count updates.

        Args:
            review_id: Review ID
            user_id: User marking as helpful

        Returns:
            Updated Review instance

        Raises:
            NotFoundError: If review not found
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("mark_helpful", review_id=review_id, user_id=user_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                # TODO: Add logic to prevent duplicate helpful votes per user
                # This would require a separate user_review_votes table

                updated_review = await repository.increment_helpful_count(review_id)

                if not updated_review:
                    raise NotFoundError("Review", review_id)

                # Track metrics
                metrics_collector.increment_counter("review_marked_helpful")

                self.log_operation_success(
                    correlation,
                    f"Review {review_id} marked helpful by user {user_id}"
                )

                return updated_review

        except Exception as e:
            await self.handle_service_error(e, "mark_helpful", {
                "review_id": review_id,
                "user_id": user_id
            })

    async def report_review(self, review_id: int, user_id: int, reason: str) -> Review:
        """
        Report a review for inappropriate content.

        Performance target: <50ms for reported count updates.

        Args:
            review_id: Review ID
            user_id: User reporting the review
            reason: Reason for reporting

        Returns:
            Updated Review instance

        Raises:
            NotFoundError: If review not found
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("report_review", review_id=review_id, user_id=user_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                updated_review = await repository.increment_reported_count(review_id)

                if not updated_review:
                    raise NotFoundError("Review", review_id)

                # Trigger moderation if report threshold reached
                if updated_review.reported_count >= settings.REVIEW_AUTO_MODERATION_THRESHOLD:
                    await self._trigger_ai_moderation(updated_review)

                # Track metrics
                metrics_collector.increment_counter("review_reported")

                self.log_operation_success(
                    correlation,
                    f"Review {review_id} reported by user {user_id}, reason: {reason}"
                )

                return updated_review

        except Exception as e:
            await self.handle_service_error(e, "report_review", {
                "review_id": review_id,
                "user_id": user_id,
                "reason": reason
            })

    async def get_review_statistics(
        self,
        vendor_id: Optional[int] = None,
        service_id: Optional[int] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """
        Get review statistics with performance optimization.

        Performance target: <200ms for statistical queries.

        Args:
            vendor_id: Optional vendor ID filter
            service_id: Optional service ID filter
            date_range: Optional date range filter

        Returns:
            Dictionary with review statistics
        """
        correlation = self.log_operation_start("get_review_statistics", vendor_id=vendor_id)

        try:
            async with self.get_session_context() as session:
                repository = ReviewRepository(session)

                # Convert datetime to date if provided
                date_range_dates = None
                if date_range:
                    date_range_dates = (date_range[0].date(), date_range[1].date())

                stats = await repository.get_review_statistics(
                    vendor_id=vendor_id,
                    service_id=service_id,
                    date_range=date_range_dates
                )

                self.log_operation_success(
                    correlation,
                    f"Retrieved statistics: {stats['total_reviews']} reviews"
                )

                return stats

        except Exception as e:
            await self.handle_service_error(e, "get_review_statistics", {
                "vendor_id": vendor_id,
                "service_id": service_id
            })

    # Private helper methods

    async def _validate_booking_eligibility(self, booking_id: int, customer_id: int) -> Booking:
        """
        Validate that booking is eligible for review.

        Args:
            booking_id: Booking ID
            customer_id: Customer ID

        Returns:
            Booking instance if eligible

        Raises:
            NotFoundError: If booking not found
            ValidationError: If booking not eligible
        """
        booking = await self.booking_repository.get(booking_id)

        if not booking:
            raise NotFoundError("Booking", booking_id)

        if booking.customer_id != customer_id:
            raise ValidationError("Customer can only review their own bookings")

        if booking.status != BookingStatus.COMPLETED:
            raise ValidationError(f"Only completed bookings can be reviewed (current status: {booking.status})")

        return booking

    async def _trigger_ai_moderation(self, review: Review) -> None:
        """
        Trigger AI moderation for a review.

        Args:
            review: Review to moderate
        """
        try:
            await self.moderation_service.create_moderation_record(
                review_id=review.id,
                review_content={
                    'title': review.title,
                    'content': review.content,
                    'rating': review.rating
                }
            )
        except Exception as e:
            logger.error(
                f"Failed to trigger AI moderation for review {review.id}: {str(e)}",
                extra={"correlation_id": correlation_id.get('')}
            )

    async def _send_review_notifications(self, review: Review, action: str) -> None:
        """
        Send notifications for review events.

        Args:
            review: Review instance
            action: Action performed (created, updated)
        """
        try:
            # TODO: Implement notification logic
            # - Email to vendor about new review
            # - Push notification to vendor
            # - Email to customer confirming review submission
            pass
        except Exception as e:
            logger.error(
                f"Failed to send review notifications: {str(e)}",
                extra={"correlation_id": correlation_id.get('')}
            )

    async def _send_status_change_notifications(
        self,
        review: Review,
        old_status: ReviewStatus,
        new_status: ReviewStatus,
        admin_id: Optional[int] = None
    ) -> None:
        """
        Send notifications for review status changes.

        Args:
            review: Review instance
            old_status: Previous status
            new_status: New status
            admin_id: Optional admin ID
        """
        try:
            # TODO: Implement status change notification logic
            # - Email to customer about status change
            # - Push notification for important status changes
            pass
        except Exception as e:
            logger.error(
                f"Failed to send status change notifications: {str(e)}",
                extra={"correlation_id": correlation_id.get('')}
            )

    def _validate_status_transition(self, old_status: ReviewStatus, new_status: ReviewStatus) -> None:
        """
        Validate review status transition.

        Args:
            old_status: Current status
            new_status: Desired new status

        Raises:
            ValidationError: If transition is invalid
        """
        # Define valid transitions
        valid_transitions = {
            ReviewStatus.PENDING: [ReviewStatus.APPROVED, ReviewStatus.REJECTED, ReviewStatus.FLAGGED],
            ReviewStatus.APPROVED: [ReviewStatus.FLAGGED, ReviewStatus.HIDDEN],
            ReviewStatus.REJECTED: [ReviewStatus.PENDING],  # Allow re-review
            ReviewStatus.FLAGGED: [ReviewStatus.APPROVED, ReviewStatus.REJECTED, ReviewStatus.HIDDEN],
            ReviewStatus.HIDDEN: [ReviewStatus.APPROVED, ReviewStatus.FLAGGED]
        }

        if new_status not in valid_transitions.get(old_status, []):
            raise ValidationError(
                f"Invalid status transition: {old_status.value} → {new_status.value}"
            )
