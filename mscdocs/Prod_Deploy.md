# Phase 9: Production Deployment Tracking
## Culture Connect Backend - Production Deployment Implementation

### Executive Summary

This document serves as the dedicated tracking and implementation guide for Phase 9: Production Deployment of the Culture Connect Backend project. Phase 9 represents the final 10% of the project, focusing on containerization, CI/CD automation, and production infrastructure deployment to achieve 100% project completion.

**Phase 9 Scope**: Complete production deployment infrastructure with Docker containerization, automated CI/CD pipelines, cloud infrastructure setup, monitoring systems, and production optimization.

**Current Status**: 0% Complete - Ready for Implementation
**Estimated Duration**: 1 week (24-30 hours)
**Strategic Value**: CRITICAL - Production readiness and deployment automation
**Dependencies**: Phase 8 (Testing & Quality Assurance) - ✅ 100% Complete

---

## 🎯 Phase 9 Objectives

### Primary Goals
- ✅ **Docker Containerization**: Multi-stage production-optimized containers
- ✅ **CI/CD Pipeline**: Automated testing, building, and deployment workflows
- ✅ **Cloud Infrastructure**: Scalable production deployment with auto-scaling
- ✅ **Production Monitoring**: Comprehensive observability and alerting systems
- ✅ **Performance Optimization**: Production-grade performance tuning
- ✅ **Security Hardening**: Production security configurations and compliance

### Success Criteria
- **100% Automated Deployment**: Zero-downtime deployments with rollback capability
- **Production Performance**: >1000 concurrent users, <200ms GET, <500ms POST/PUT
- **High Availability**: >99.9% uptime with auto-scaling and load balancing
- **Comprehensive Monitoring**: Real-time metrics, logging, and alerting
- **Security Compliance**: Production-grade security with audit trails
- **Documentation Complete**: Deployment guides and operational procedures

---

## 📋 Detailed Task Breakdown

### **Task 9.1: Docker Containerization** ⏸️ **0% COMPLETE**
**Estimated Duration**: 6-8 hours
**Priority**: P1 - Critical

#### **9.1.1 Multi-Stage Docker Optimization** ⏸️ **PENDING**
- ⏸️ **Production Dockerfile Enhancement**
  - Multi-stage build optimization for minimal image size
  - Security hardening with non-root user
  - Health check implementation
  - Build cache optimization

- ⏸️ **Container Security Configuration**
  - Vulnerability scanning integration
  - Secrets management for production
  - Resource limits and constraints
  - Security context configuration

**Implementation Steps**:
```bash
# 1. Optimize Dockerfile for production
# - Current: Basic multi-stage build exists
# - Enhancement: Add security scanning, optimize layers
# - Target: <500MB final image size

# 2. Implement container security
# - Non-root user execution
# - Read-only filesystem where possible
# - Security context hardening
# - Secrets management integration
```

#### **9.1.2 Docker Compose Production Configuration** ⏸️ **PENDING**
- ⏸️ **Production Docker Compose Setup**
  - Production environment configuration
  - Service scaling configuration
  - Network security and isolation
  - Volume management for persistence

- ⏸️ **Environment-Specific Configurations**
  - Development, staging, production environments
  - Environment variable management
  - Service discovery configuration
  - Load balancer integration

**Implementation Steps**:
```bash
# 1. Create production docker-compose.yml
# - Current: Development configuration exists
# - Enhancement: Production-specific settings
# - Target: Production-ready multi-service setup

# 2. Environment configuration management
# - Staging and production environment files
# - Secrets management integration
# - Service scaling parameters
```

**Success Criteria**:
- ✅ Docker image builds successfully with <500MB size
- ✅ Container security scan passes with zero critical vulnerabilities
- ✅ Production docker-compose deploys all services correctly
- ✅ Health checks validate all services are operational

---

### **Task 9.2: CI/CD Pipeline Setup** ⏸️ **0% COMPLETE**
**Estimated Duration**: 8-10 hours
**Priority**: P1 - Critical

#### **9.2.1 GitHub Actions Workflow Implementation** ⏸️ **PENDING**
- ⏸️ **Automated Testing Pipeline**
  - Unit, integration, and performance test automation
  - Code quality checks (linting, security scanning)
  - Test coverage validation (>80% requirement)
  - Performance regression testing

- ⏸️ **Build and Security Pipeline**
  - Docker image building and optimization
  - Container security scanning
  - Dependency vulnerability scanning
  - Code security analysis with Bandit

**Implementation Steps**:
```yaml
# 1. Create .github/workflows/ci.yml
# - Automated testing on PR and push
# - Code quality validation
# - Security scanning integration
# - Performance test execution

# 2. Create .github/workflows/cd.yml
# - Automated deployment to staging
# - Production deployment with approval
# - Rollback capability
# - Deployment monitoring
```

#### **9.2.2 Deployment Automation** ⏸️ **PENDING**
- ⏸️ **Staging Deployment Automation**
  - Automatic deployment to staging environment
  - Integration testing in staging
  - Performance validation
  - Approval gates for production

- ⏸️ **Production Deployment Workflow**
  - Blue-green deployment strategy
  - Zero-downtime deployment process
  - Automated rollback on failure
  - Deployment monitoring and validation

**Implementation Steps**:
```bash
# 1. Staging deployment automation
# - Automatic deployment on main branch
# - Integration test execution
# - Performance validation
# - Approval workflow for production

# 2. Production deployment process
# - Manual approval gate
# - Blue-green deployment
# - Health check validation
# - Automatic rollback on failure
```

**Success Criteria**:
- ✅ CI pipeline runs all tests with 100% success rate
- ✅ Security scans pass with zero critical vulnerabilities
- ✅ Automated deployment to staging works correctly
- ✅ Production deployment process includes approval gates and rollback

---

### **Task 9.3: Kubernetes Production Infrastructure Deployment** ⏸️ **0% COMPLETE**
**Estimated Duration**: 12-15 hours
**Priority**: P1 - Critical

#### **9.3.1 Kubernetes Cluster Setup and Configuration** ⏸️ **PENDING**
- ⏸️ **Managed Kubernetes Cluster Deployment**
  - EKS/GKE/AKS cluster provisioning with node groups
  - Multi-AZ deployment for high availability
  - Cluster autoscaler configuration for >1000 concurrent users
  - RBAC and security policies implementation

- ⏸️ **Integration with Existing Phase 7.3.3 Scaling Services**
  - ContainerOrchestrationService Kubernetes API integration
  - AutoScalingService HPA custom metrics configuration
  - ScalingMetricsService Prometheus metrics exposure
  - LoadBalancerService business-aware routing policies

**Implementation Steps**:
```bash
# 1. Kubernetes cluster provisioning
# - Create managed cluster (EKS/GKE/AKS)
# - Configure node groups with auto-scaling
# - Set up cluster networking and security
# - Install essential add-ons (metrics-server, cluster-autoscaler)

# 2. Integration with existing scaling infrastructure
# - Deploy custom metrics API server
# - Configure HPA with Phase 7.3.3 metrics
# - Set up service mesh for advanced traffic management
# - Integrate with existing LoadBalancerService
```

#### **9.3.2 Kubernetes Service Deployment Manifests** ⏸️ **PENDING**
- ⏸️ **Core Application Deployment**
  - Culture Connect API deployment with HPA integration
  - ConfigMaps and Secrets for environment configuration
  - Service definitions for internal communication
  - Ingress controllers for external access and SSL termination

- ⏸️ **Database and Storage Infrastructure**
  - PostgreSQL StatefulSet with persistent volumes
  - Redis cluster deployment with high availability
  - Backup automation with CronJobs
  - Data encryption at rest and in transit

**Implementation Steps**:
```yaml
# 1. Application deployment manifests
# - Deployment with existing scaling service integration
# - HPA with custom metrics from Phase 7.3.3
# - Services for load balancing
# - Ingress with SSL termination

# 2. Database infrastructure manifests
# - PostgreSQL StatefulSet with replication
# - Redis cluster with sentinel
# - Persistent volume claims
# - Backup and monitoring CronJobs
```

#### **9.3.2 Production Monitoring and Observability** ⏸️ **PENDING**
- ⏸️ **Application Performance Monitoring**
  - Sentry APM integration for error tracking
  - Custom metrics collection and dashboards
  - Real-time performance monitoring
  - Alert configuration for critical metrics

- ⏸️ **Infrastructure Monitoring**
  - Server and container resource monitoring
  - Database performance monitoring
  - Network and security monitoring
  - Log aggregation and analysis

**Implementation Steps**:
```bash
# 1. APM setup
# - Sentry integration for error tracking
# - Custom metrics dashboard
# - Performance monitoring
# - Alert configuration

# 2. Infrastructure monitoring
# - Prometheus/Grafana or cloud monitoring
# - Log aggregation (ELK stack or cloud logging)
# - Security monitoring
# - Automated alerting
```

#### **9.3.3 Production Security and Compliance** ⏸️ **PENDING**
- ⏸️ **Security Hardening**
  - SSL/TLS certificate management
  - Web Application Firewall (WAF) configuration
  - DDoS protection setup
  - Security headers and CORS configuration

- ⏸️ **Compliance and Audit**
  - Audit logging implementation
  - Compliance monitoring (GDPR, PCI DSS considerations)
  - Security incident response procedures
  - Regular security assessments

**Implementation Steps**:
```bash
# 1. Security infrastructure
# - SSL certificate automation (Let's Encrypt)
# - WAF configuration
# - DDoS protection
# - Security headers implementation

# 2. Compliance setup
# - Audit logging
# - Compliance monitoring
# - Security procedures documentation
# - Regular security assessments
```

**Success Criteria**:
- ✅ Production infrastructure supports >1000 concurrent users
- ✅ Auto-scaling works correctly under load
- ✅ Monitoring and alerting systems are operational
- ✅ Security measures pass penetration testing
- ✅ Backup and disaster recovery procedures are tested

---

## 🔧 Implementation Prerequisites

### **Technical Prerequisites**
- ✅ **Phase 8 Complete**: Testing & Quality Assurance (100% complete)
- ✅ **Docker Environment**: Docker and Docker Compose installed
- ✅ **Cloud Account**: AWS, GCP, or Azure account with appropriate permissions
- ✅ **Domain and SSL**: Production domain and SSL certificate setup
- ✅ **Monitoring Tools**: Sentry account and monitoring service setup

### **Infrastructure Requirements**
- **Compute Resources**: Minimum 4 vCPUs, 8GB RAM for production
- **Database**: PostgreSQL 15+ with replication capability
- **Cache**: Redis cluster for high availability
- **Storage**: Persistent storage for database and file uploads
- **Network**: Load balancer with SSL termination
- **Monitoring**: APM and infrastructure monitoring services

### **Security Requirements**
- **SSL Certificates**: Valid SSL certificates for all domains
- **Secrets Management**: Secure secret storage and rotation
- **Access Control**: Role-based access control for infrastructure
- **Audit Logging**: Comprehensive audit trail for all operations
- **Backup Strategy**: Automated backup and disaster recovery

---

## 📊 Quality Gates and Validation

### **Deployment Quality Gates**
1. **Pre-Deployment Validation**
   - ✅ All tests pass with >80% coverage
   - ✅ Security scans pass with zero critical vulnerabilities
   - ✅ Performance tests meet targets (>1000 concurrent users)
   - ✅ Database migrations tested and validated

2. **Deployment Validation**
   - ✅ Health checks pass for all services
   - ✅ Integration tests pass in production environment
   - ✅ Performance validation under load
   - ✅ Security configuration validated

3. **Post-Deployment Validation**
   - ✅ Monitoring and alerting operational
   - ✅ Backup and disaster recovery tested
   - ✅ Documentation updated and complete
   - ✅ Team training completed

### **Production Readiness Checklist**
- ⏸️ **Infrastructure**: Cloud infrastructure provisioned and configured
- ⏸️ **Security**: Security measures implemented and tested
- ⏸️ **Monitoring**: Comprehensive monitoring and alerting setup
- ⏸️ **Backup**: Backup and disaster recovery procedures tested
- ⏸️ **Documentation**: Operational procedures documented
- ⏸️ **Training**: Team trained on production operations

---

## 📈 Timeline and Milestones

### **Week 1: Production Deployment Implementation**

**Days 1-2: Docker Containerization (Task 9.1)**
- Multi-stage Docker optimization
- Container security configuration
- Production docker-compose setup
- Container testing and validation

**Days 3-4: CI/CD Pipeline (Task 9.2)**
- GitHub Actions workflow implementation
- Automated testing pipeline
- Deployment automation
- Security scanning integration

**Days 5-7: Production Infrastructure (Task 9.3)**
- Cloud infrastructure setup
- Monitoring and observability
- Security hardening
- Production deployment and validation

### **Milestone Checkpoints**
- **Day 2**: Docker containerization complete and tested
- **Day 4**: CI/CD pipeline operational with automated testing
- **Day 6**: Production infrastructure deployed and monitored
- **Day 7**: Full production deployment validated and documented

---

## 🎯 Success Metrics

### **Technical Metrics**
- **Performance**: >1000 concurrent users, <200ms GET, <500ms POST/PUT
- **Availability**: >99.9% uptime with auto-scaling
- **Security**: Zero critical vulnerabilities, security compliance
- **Automation**: 100% automated deployment with rollback capability

### **Operational Metrics**
- **Deployment Time**: <30 minutes for full deployment
- **Recovery Time**: <5 minutes for rollback operations
- **Monitoring Coverage**: 100% service and infrastructure monitoring
- **Documentation**: Complete operational procedures and runbooks

### **Business Metrics**
- **Time to Market**: Production-ready deployment capability
- **Operational Efficiency**: Reduced manual deployment effort
- **Risk Mitigation**: Automated testing and deployment validation
- **Scalability**: Infrastructure ready for business growth

---

## 🔧 Detailed Implementation Guides

### **Docker Containerization Implementation**

#### **Current Docker Assets Analysis**
**Existing Files**:
- ✅ `Dockerfile`: Multi-stage production build (67 lines)
- ✅ `docker-compose.yml`: Development configuration (106 lines)
- ✅ `.env.production`: Production environment template

**Enhancement Requirements**:
```dockerfile
# Dockerfile optimizations needed:
# 1. Security scanning integration
# 2. Build cache optimization
# 3. Multi-architecture support
# 4. Health check enhancement

# Current image size target: <500MB
# Security: Non-root user (implemented)
# Health checks: Basic implementation exists
```

#### **Production Docker Compose Configuration**
```yaml
# docker-compose.prod.yml structure needed:
version: '3.8'
services:
  api:
    image: culture-connect-api:latest
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    environment:
      - ENVIRONMENT=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### **CI/CD Pipeline Implementation**

#### **GitHub Actions Workflow Structure**
**Required Workflows**:
1. **`.github/workflows/ci.yml`** - Continuous Integration
2. **`.github/workflows/cd.yml`** - Continuous Deployment
3. **`.github/workflows/security.yml`** - Security Scanning
4. **`.github/workflows/performance.yml`** - Performance Testing

#### **CI Workflow Implementation**
```yaml
# .github/workflows/ci.yml
name: Continuous Integration
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: culture_connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt

    - name: Run tests
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-fail-under=80
      env:
        DATABASE_URL: postgresql://postgres:test@localhost/culture_connect_test
        REDIS_URL: redis://localhost:6379

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### **Production Infrastructure Setup**

#### **Option 1: Kubernetes Production Deployment (Enterprise-Scale)**

**Kubernetes (EKS/GKE/AKS) - Primary Production Platform**
```yaml
# Production Kubernetes Architecture
# 1. Managed Kubernetes cluster (EKS/GKE/AKS)
# 2. Horizontal Pod Autoscaler (HPA) with custom metrics
# 3. Service mesh (Istio/Linkerd) for traffic management
# 4. Ingress controllers for load balancing and SSL
# 5. Persistent volumes for database and storage
# 6. Integration with existing Phase 7.3.3 scaling services
```

**Target Use Case**: Enterprise production with >1000 concurrent users
**Monthly Cost**: $500-2000+ (depending on scale and cloud provider)
**Operational Complexity**: Medium (managed services reduce overhead)

#### **Option 2: Docker Swarm Development Environment**

**Docker Swarm - Development and Testing Environment**
```bash
# Docker Swarm for local development
# 1. Single-node swarm for development
# 2. Multi-node swarm for testing
# 3. Simplified service deployment
# 4. Easy transition to Kubernetes
# 5. Resource-efficient for development
```

**Target Use Case**: Development, testing, and small-scale production
**Monthly Cost**: $50-200 (development environments)
**Operational Complexity**: Low (simple container orchestration)

#### **Option 3: Cost-Effective Self-Hosting (Startup-Optimized)**

**Hetzner Cloud + Portainer + k3s/Docker Swarm - Self-Hosted Infrastructure**
```yaml
# Self-Hosted Architecture
# 1. Hetzner Cloud VPS/dedicated servers (cost-effective)
# 2. Portainer for visual container management
# 3. k3s (lightweight Kubernetes) or Docker Swarm
# 4. Self-managed PostgreSQL with streaming replication
# 5. Self-managed Redis cluster
# 6. Prometheus + Grafana monitoring stack
# 7. Integration with existing Phase 7.3.3 scaling services
```

**Target Use Case**: Startup production with cost optimization priority
**Monthly Cost**: $100-500 (significant cost savings vs. managed cloud)
**Operational Complexity**: Medium-High (self-managed infrastructure)

**Cost Comparison Analysis**:
```bash
# Monthly Infrastructure Costs (1000 concurrent users)
# AWS EKS + RDS + ElastiCache: $800-1200/month
# GCP GKE + Cloud SQL + Memorystore: $700-1000/month
# Hetzner + Self-Managed: $150-300/month (60-75% cost savings)

# Startup Growth Phases:
# Phase 1 (100 users): Hetzner $50-100/month vs AWS $200-400/month
# Phase 2 (1000 users): Hetzner $150-300/month vs AWS $800-1200/month
# Phase 3 (10000+ users): Consider hybrid or migration to managed services
```

### **Self-Hosted Infrastructure Implementation (Option 3)**

#### **Hetzner Cloud Infrastructure Setup**

**Server Configuration for Different Scales**:
```yaml
# Startup Phase 1: 100 concurrent users
hetzner_small_setup:
  web_servers:
    - type: "CX21"  # 2 vCPU, 4GB RAM, 40GB SSD
      count: 2
      monthly_cost: "€5.83 each"
  database_server:
    - type: "CX31"  # 2 vCPU, 8GB RAM, 80GB SSD
      count: 1
      monthly_cost: "€11.90"
  load_balancer:
    - type: "LB11"  # Hetzner Load Balancer
      monthly_cost: "€4.90"
  total_monthly_cost: "€28.46 (~$30)"

# Startup Phase 2: 1000 concurrent users
hetzner_medium_setup:
  web_servers:
    - type: "CX41"  # 4 vCPU, 16GB RAM, 160GB SSD
      count: 3
      monthly_cost: "€23.79 each"
  database_servers:
    - type: "CX51"  # 8 vCPU, 32GB RAM, 320GB SSD
      count: 2  # Primary + replica
      monthly_cost: "€47.57 each"
  redis_server:
    - type: "CX21"  # 2 vCPU, 4GB RAM, 40GB SSD
      count: 1
      monthly_cost: "€5.83"
  load_balancer:
    - type: "LB21"  # Enhanced Load Balancer
      monthly_cost: "€13.90"
  total_monthly_cost: "€184.45 (~$200)"

# Startup Phase 3: 10000+ concurrent users
hetzner_large_setup:
  web_servers:
    - type: "CCX33"  # 8 vCPU, 32GB RAM, 240GB SSD
      count: 5
      monthly_cost: "€47.57 each"
  database_servers:
    - type: "CCX53"  # 16 vCPU, 64GB RAM, 480GB SSD
      count: 3  # Primary + 2 replicas
      monthly_cost: "€95.14 each"
  redis_cluster:
    - type: "CX41"  # 4 vCPU, 16GB RAM, 160GB SSD
      count: 3
      monthly_cost: "€23.79 each"
  monitoring_server:
    - type: "CX21"  # 2 vCPU, 4GB RAM, 40GB SSD
      count: 1
      monthly_cost: "€5.83"
  load_balancer:
    - type: "LB31"  # High-performance Load Balancer
      monthly_cost: "€23.90"
  total_monthly_cost: "€595.33 (~$650)"
```

#### **Portainer + k3s/Docker Swarm Setup**

**Infrastructure as Code (Terraform)**:
```hcl
# hetzner-infrastructure.tf
terraform {
  required_providers {
    hcloud = {
      source  = "hetznercloud/hcloud"
      version = "~> 1.42"
    }
  }
}

variable "hcloud_token" {
  sensitive = true
}

provider "hcloud" {
  token = var.hcloud_token
}

# Create a network for the cluster
resource "hcloud_network" "culture_connect_network" {
  name     = "culture-connect-network"
  ip_range = "10.0.0.0/16"
}

resource "hcloud_network_subnet" "culture_connect_subnet" {
  type         = "cloud"
  network_id   = hcloud_network.culture_connect_network.id
  network_zone = "eu-central"
  ip_range     = "********/24"
}

# Web servers for application
resource "hcloud_server" "web_servers" {
  count       = 3
  name        = "culture-connect-web-${count.index + 1}"
  image       = "ubuntu-22.04"
  server_type = "cx41"
  location    = "nbg1"

  network {
    network_id = hcloud_network.culture_connect_network.id
    ip         = "10.0.1.${count.index + 10}"
  }

  ssh_keys = [hcloud_ssh_key.culture_connect_key.id]

  user_data = file("cloud-init-web.yml")

  labels = {
    role = "web"
    environment = "production"
  }
}

# Database servers
resource "hcloud_server" "database_servers" {
  count       = 2
  name        = "culture-connect-db-${count.index + 1}"
  image       = "ubuntu-22.04"
  server_type = "cx51"
  location    = "nbg1"

  network {
    network_id = hcloud_network.culture_connect_network.id
    ip         = "10.0.1.${count.index + 20}"
  }

  ssh_keys = [hcloud_ssh_key.culture_connect_key.id]

  user_data = file("cloud-init-db.yml")

  labels = {
    role = "database"
    environment = "production"
  }
}

# Load balancer
resource "hcloud_load_balancer" "culture_connect_lb" {
  name               = "culture-connect-lb"
  load_balancer_type = "lb21"
  location           = "nbg1"

  labels = {
    environment = "production"
  }
}

resource "hcloud_load_balancer_network" "culture_connect_lb_network" {
  load_balancer_id = hcloud_load_balancer.culture_connect_lb.id
  network_id       = hcloud_network.culture_connect_network.id
  ip               = "********"
}

# Load balancer targets
resource "hcloud_load_balancer_target" "culture_connect_lb_target" {
  count            = length(hcloud_server.web_servers)
  type             = "server"
  load_balancer_id = hcloud_load_balancer.culture_connect_lb.id
  server_id        = hcloud_server.web_servers[count.index].id
  use_private_ip   = true
}

# Load balancer service
resource "hcloud_load_balancer_service" "culture_connect_lb_service" {
  load_balancer_id = hcloud_load_balancer.culture_connect_lb.id
  protocol         = "http"
  listen_port      = 80
  destination_port = 8000

  health_check {
    protocol = "http"
    port     = 8000
    interval = 15
    timeout  = 10
    retries  = 3
    http {
      path         = "/health"
      status_codes = ["200"]
    }
  }
}
```

#### **k3s Lightweight Kubernetes Setup**

**k3s Installation and Configuration**:
```bash
#!/bin/bash
# k3s-setup.sh - Install k3s on Hetzner servers

# Master node setup
curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="server" sh -s - \
  --cluster-init \
  --disable traefik \
  --disable servicelb \
  --node-external-ip=$(curl -s http://***************/hetzner/v1/metadata/public-ipv4) \
  --flannel-backend=wireguard-native \
  --cluster-cidr=*********/16 \
  --service-cidr=*********/16

# Get node token for worker nodes
K3S_TOKEN=$(cat /var/lib/rancher/k3s/server/node-token)
K3S_URL="https://$(curl -s http://***************/hetzner/v1/metadata/public-ipv4):6443"

# Worker nodes setup (run on each worker)
curl -sfL https://get.k3s.io | K3S_URL=$K3S_URL K3S_TOKEN=$K3S_TOKEN sh -
```

**k3s Culture Connect Deployment**:
```yaml
# culture-connect-k3s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
  namespace: culture-connect
spec:
  replicas: 3
  selector:
    matchLabels:
      app: culture-connect-api
  template:
    metadata:
      labels:
        app: culture-connect-api
    spec:
      containers:
      - name: api
        image: culture-connect-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: culture-connect-secrets
              key: database-url
        # Integration with Phase 7.3.3 services
        - name: SCALING_BACKEND
          value: "k3s"
        - name: METRICS_ENDPOINT
          value: "http://prometheus:9090"
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# HPA for k3s with custom metrics
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: culture-connect-hpa
  namespace: culture-connect
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics from Phase 7.3.3 (adapted for self-hosted)
  - type: External
    external:
      metric:
        name: booking_queue_length
      target:
        type: AverageValue
        averageValue: "10"
```

#### **Portainer Setup for Container Management**

**Portainer Installation**:
```bash
#!/bin/bash
# portainer-setup.sh - Install Portainer for visual container management

# Create Portainer volume
docker volume create portainer_data

# Deploy Portainer (Community Edition)
docker run -d \
  --name portainer \
  --restart=always \
  -p 8000:8000 \
  -p 9443:9443 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v portainer_data:/data \
  portainer/portainer-ce:latest

# For k3s integration
kubectl apply -f - <<EOF
apiVersion: v1
kind: Namespace
metadata:
  name: portainer
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: portainer-sa-clusteradmin
  namespace: portainer
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: portainer-crb-clusteradmin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: portainer-sa-clusteradmin
  namespace: portainer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: portainer
  namespace: portainer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: portainer
  template:
    metadata:
      labels:
        app: portainer
    spec:
      serviceAccountName: portainer-sa-clusteradmin
      containers:
      - name: portainer
        image: portainer/portainer-ce:latest
        ports:
        - containerPort: 9000
        - containerPort: 8000
        volumeMounts:
        - name: data
          mountPath: /data
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: portainer-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: portainer
  namespace: portainer
spec:
  type: NodePort
  ports:
  - port: 9000
    targetPort: 9000
    nodePort: 30777
  selector:
    app: portainer
EOF
```

#### **Self-Managed Database Setup**

**PostgreSQL High Availability with Streaming Replication**:
```bash
#!/bin/bash
# postgresql-ha-setup.sh - Set up PostgreSQL with streaming replication

# Primary server setup
sudo apt update && sudo apt install -y postgresql-15 postgresql-contrib

# Configure PostgreSQL for replication
sudo -u postgres psql -c "CREATE USER replicator REPLICATION LOGIN CONNECTION LIMIT 1 ENCRYPTED PASSWORD 'replicator_password';"

# Primary server postgresql.conf
cat >> /etc/postgresql/15/main/postgresql.conf << EOF
# Replication settings
wal_level = replica
max_wal_senders = 3
max_replication_slots = 3
synchronous_commit = on
synchronous_standby_names = 'standby1'

# Performance settings for Culture Connect
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB

# Connection settings
max_connections = 200
listen_addresses = '*'
EOF

# Primary server pg_hba.conf
cat >> /etc/postgresql/15/main/pg_hba.conf << EOF
# Replication connections
host replication replicator ********/24 md5
# Application connections
host culture_connect_prod culture_connect ********/24 md5
EOF

# Restart PostgreSQL
sudo systemctl restart postgresql

# Standby server setup (run on replica server)
sudo systemctl stop postgresql
sudo -u postgres rm -rf /var/lib/postgresql/15/main/*

# Create base backup
sudo -u postgres pg_basebackup -h ********* -D /var/lib/postgresql/15/main -U replicator -v -P -W

# Standby server postgresql.conf
cat >> /var/lib/postgresql/15/main/postgresql.conf << EOF
# Standby settings
hot_standby = on
primary_conninfo = 'host=********* port=5432 user=replicator password=replicator_password application_name=standby1'
EOF

# Create standby.signal
sudo -u postgres touch /var/lib/postgresql/15/main/standby.signal

# Start standby server
sudo systemctl start postgresql
```

**Redis Cluster Setup for Self-Hosted**:
```bash
#!/bin/bash
# redis-cluster-setup.sh - Set up Redis cluster for caching

# Install Redis
sudo apt update && sudo apt install -y redis-server

# Redis cluster configuration
for port in 7000 7001 7002; do
  mkdir -p /etc/redis/cluster/$port
  cat > /etc/redis/cluster/$port/redis.conf << EOF
port $port
cluster-enabled yes
cluster-config-file nodes-$port.conf
cluster-node-timeout 5000
appendonly yes
dir /var/lib/redis/cluster/$port
bind 0.0.0.0
protected-mode no

# Performance settings for Culture Connect
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
tcp-keepalive 300
timeout 0

# Integration with Phase 7.3.2 caching
notify-keyspace-events Ex
EOF

  # Create data directory
  sudo mkdir -p /var/lib/redis/cluster/$port
  sudo chown redis:redis /var/lib/redis/cluster/$port

  # Create systemd service
  cat > /etc/systemd/system/redis-$port.service << EOF
[Unit]
Description=Redis In-Memory Data Store (port $port)
After=network.target

[Service]
User=redis
Group=redis
ExecStart=/usr/bin/redis-server /etc/redis/cluster/$port/redis.conf
ExecStop=/usr/bin/redis-cli -p $port shutdown
Restart=always

[Install]
WantedBy=multi-user.target
EOF

  sudo systemctl enable redis-$port
  sudo systemctl start redis-$port
done

# Create cluster (run on one node)
redis-cli --cluster create \
  *********:7000 *********:7001 *********:7002 \
  *********:7000 *********:7001 *********:7002 \
  --cluster-replicas 1
```

#### **Phase 7.3.3 Integration for Self-Hosted Environment**

**Adapted AutoScalingService for k3s/Docker Swarm**:
```python
# app/services/scaling/self_hosted_scaling_service.py
from typing import Dict, List, Optional
import asyncio
import aiohttp
from kubernetes import client, config
from app.services.scaling.base_scaling_service import BaseScalingService

class SelfHostedScalingService(BaseScalingService):
    """
    AutoScalingService adapted for self-hosted environments.

    Supports both k3s (lightweight Kubernetes) and Docker Swarm
    with integration to existing Phase 7.3.3 scaling infrastructure.
    """

    def __init__(self, orchestrator: str = "k3s"):
        super().__init__()
        self.orchestrator = orchestrator  # "k3s" or "docker_swarm"
        self.k8s_client = None
        self.docker_client = None

        if orchestrator == "k3s":
            self._init_k3s_client()
        elif orchestrator == "docker_swarm":
            self._init_docker_client()

    def _init_k3s_client(self):
        """Initialize k3s Kubernetes client."""
        try:
            config.load_incluster_config()  # For in-cluster deployment
        except:
            config.load_kube_config()  # For external access

        self.k8s_client = client.AppsV1Api()
        self.k8s_autoscaling = client.AutoscalingV2Api()

    async def scale_application(self, target_replicas: int) -> Dict:
        """Scale application based on orchestrator type."""
        if self.orchestrator == "k3s":
            return await self._scale_k3s_deployment(target_replicas)
        elif self.orchestrator == "docker_swarm":
            return await self._scale_docker_swarm_service(target_replicas)

    async def _scale_k3s_deployment(self, target_replicas: int) -> Dict:
        """Scale k3s deployment using Kubernetes API."""
        try:
            # Get current deployment
            deployment = self.k8s_client.read_namespaced_deployment(
                name="culture-connect-api",
                namespace="culture-connect"
            )

            # Update replica count
            deployment.spec.replicas = target_replicas

            # Apply scaling
            self.k8s_client.patch_namespaced_deployment(
                name="culture-connect-api",
                namespace="culture-connect",
                body=deployment
            )

            return {
                "status": "success",
                "orchestrator": "k3s",
                "target_replicas": target_replicas,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "orchestrator": "k3s"
            }

    async def _scale_docker_swarm_service(self, target_replicas: int) -> Dict:
        """Scale Docker Swarm service."""
        try:
            import docker
            client = docker.from_env()

            # Get service
            service = client.services.get("culture-connect-api")

            # Update service with new replica count
            service.update(mode=docker.types.ServiceMode('replicated', replicas=target_replicas))

            return {
                "status": "success",
                "orchestrator": "docker_swarm",
                "target_replicas": target_replicas,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "orchestrator": "docker_swarm"
            }

    async def get_current_metrics(self) -> Dict:
        """Get current metrics for scaling decisions."""
        # Integration with existing ScalingMetricsService
        metrics = await self.scaling_metrics_service.get_utilization_summary()

        # Add self-hosted specific metrics
        if self.orchestrator == "k3s":
            k8s_metrics = await self._get_k3s_metrics()
            metrics.update(k8s_metrics)
        elif self.orchestrator == "docker_swarm":
            swarm_metrics = await self._get_swarm_metrics()
            metrics.update(swarm_metrics)

        return metrics

    async def _get_k3s_metrics(self) -> Dict:
        """Get k3s-specific metrics."""
        try:
            # Get HPA status
            hpa = self.k8s_autoscaling.read_namespaced_horizontal_pod_autoscaler(
                name="culture-connect-hpa",
                namespace="culture-connect"
            )

            return {
                "current_replicas": hpa.status.current_replicas,
                "desired_replicas": hpa.status.desired_replicas,
                "target_cpu_utilization": hpa.spec.metrics[0].resource.target.average_utilization,
                "current_cpu_utilization": hpa.status.current_metrics[0].resource.current.average_utilization if hpa.status.current_metrics else None
            }
        except Exception as e:
            return {"k3s_metrics_error": str(e)}
```

**Self-Hosted LoadBalancerService Integration**:
```python
# app/services/scaling/self_hosted_load_balancer_service.py
from typing import Dict, List
import asyncio
import aiohttp
from app.services.scaling.load_balancer_service import LoadBalancerService

class SelfHostedLoadBalancerService(LoadBalancerService):
    """
    LoadBalancerService adapted for self-hosted environments.

    Integrates with Hetzner Load Balancer and HAProxy/NGINX
    for business-aware routing decisions.
    """

    def __init__(self, lb_type: str = "hetzner"):
        super().__init__()
        self.lb_type = lb_type  # "hetzner", "haproxy", "nginx"
        self.hetzner_api_token = os.getenv("HETZNER_API_TOKEN")

    async def update_load_balancer_targets(self, targets: List[Dict]) -> Dict:
        """Update load balancer targets based on scaling decisions."""
        if self.lb_type == "hetzner":
            return await self._update_hetzner_lb_targets(targets)
        elif self.lb_type == "haproxy":
            return await self._update_haproxy_targets(targets)
        elif self.lb_type == "nginx":
            return await self._update_nginx_targets(targets)

    async def _update_hetzner_lb_targets(self, targets: List[Dict]) -> Dict:
        """Update Hetzner Load Balancer targets."""
        try:
            headers = {
                "Authorization": f"Bearer {self.hetzner_api_token}",
                "Content-Type": "application/json"
            }

            # Get load balancer ID
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.hetzner.cloud/v1/load_balancers",
                    headers=headers
                ) as response:
                    lb_data = await response.json()
                    lb_id = None

                    for lb in lb_data["load_balancers"]:
                        if lb["name"] == "culture-connect-lb":
                            lb_id = lb["id"]
                            break

                if not lb_id:
                    return {"status": "error", "error": "Load balancer not found"}

                # Update targets
                target_data = {
                    "targets": [
                        {
                            "type": "server",
                            "server": {"id": target["server_id"]},
                            "use_private_ip": True
                        }
                        for target in targets
                    ]
                }

                async with session.post(
                    f"https://api.hetzner.cloud/v1/load_balancers/{lb_id}/actions/add_targets",
                    headers=headers,
                    json=target_data
                ) as response:
                    result = await response.json()

                    return {
                        "status": "success",
                        "lb_type": "hetzner",
                        "targets_updated": len(targets),
                        "action_id": result.get("action", {}).get("id")
                    }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "lb_type": "hetzner"
            }
```

#### **Self-Hosted Monitoring Stack**

**Prometheus + Grafana Setup**:
```yaml
# monitoring-stack.yml - Docker Compose for monitoring
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - monitoring

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
```

**Prometheus Configuration for Culture Connect**:
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "culture_connect_rules.yml"

scrape_configs:
  # Culture Connect API metrics
  - job_name: 'culture-connect-api'
    static_configs:
      - targets: ['*********:8000', '*********:8000', '*********:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['*********:9100', '*********:9100', '*********:9100']

  # Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['*********:8080', '*********:8080', '*********:8080']

  # PostgreSQL metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['*********:9187', '10.0.1.21:9187']

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['*********:9121']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### **Kubernetes Production Manifests**

**Core Application Deployment with Phase 7.3.3 Integration**
```yaml
# culture-connect-api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
  namespace: culture-connect
  labels:
    app: culture-connect-api
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: culture-connect-api
  template:
    metadata:
      labels:
        app: culture-connect-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: api
        image: culture-connect-api:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: culture-connect-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: culture-connect-secrets
              key: redis-url
        # Integration with Phase 7.3.3 scaling services
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: KUBERNETES_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

**Horizontal Pod Autoscaler with Custom Metrics**
```yaml
# culture-connect-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: culture-connect-api-hpa
  namespace: culture-connect
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  # Standard resource metrics
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics from Phase 7.3.3 ScalingMetricsService
  - type: Pods
    pods:
      metric:
        name: booking_queue_length
      target:
        type: AverageValue
        averageValue: "10"
  - type: Pods
    pods:
      metric:
        name: response_time_p95
      target:
        type: AverageValue
        averageValue: "200m"  # 200ms
  - type: Pods
    pods:
      metric:
        name: concurrent_users
      target:
        type: AverageValue
        averageValue: "50"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

#### **Database Production Setup**
```yaml
# PostgreSQL StatefulSet with High Availability
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-primary
  namespace: culture-connect
spec:
  serviceName: postgresql-primary
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-primary
  template:
    metadata:
      labels:
        app: postgresql-primary
    spec:
      containers:
      - name: postgresql
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: culture_connect_prod
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: password
        - name: POSTGRES_REPLICATION_USER
          value: replicator
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: replication-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
      storageClassName: fast-ssd

---
# Redis Cluster for Caching and Session Management
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: culture-connect
spec:
  serviceName: redis-cluster
  replicas: 6  # 3 masters + 3 slaves
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        ports:
        - containerPort: 6379
        - containerPort: 16379
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "1Gi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "500m"
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
```

### **Monitoring and Observability Setup**

#### **Application Performance Monitoring**
```python
# Sentry integration (already implemented)
# Additional monitoring needed:
# 1. Custom metrics collection
# 2. Business metrics tracking
# 3. Performance dashboards
# 4. Alert configuration

# Prometheus metrics endpoint
@app.get("/metrics")
async def metrics():
    return Response(
        generate_latest(REGISTRY),
        media_type="text/plain"
    )
```

#### **Infrastructure Monitoring Stack**
```yaml
# Monitoring stack components:
# 1. Prometheus for metrics collection
# 2. Grafana for visualization
# 3. AlertManager for alerting
# 4. ELK stack for log aggregation
# 5. Jaeger for distributed tracing (optional)
```

---

## 📋 Implementation Checklist

### **Pre-Implementation Validation**
- ✅ **Code Quality**: All tests pass with >80% coverage
- ✅ **Security**: Security scans pass with zero critical vulnerabilities
- ✅ **Performance**: Performance tests validate >1000 concurrent users
- ✅ **Documentation**: Implementation guides and procedures documented

### **Docker Containerization Checklist**
- ⏸️ **Dockerfile Optimization**: Multi-stage build with security hardening
- ⏸️ **Image Security**: Vulnerability scanning and non-root user
- ⏸️ **Production Compose**: Production-specific docker-compose configuration
- ⏸️ **Environment Management**: Secure environment variable handling
- ⏸️ **Health Checks**: Comprehensive health check implementation
- ⏸️ **Resource Limits**: Memory and CPU limits configuration

### **CI/CD Pipeline Checklist**
- ⏸️ **CI Workflow**: Automated testing and quality checks
- ⏸️ **Security Scanning**: Automated security vulnerability scanning
- ⏸️ **Build Process**: Docker image building and optimization
- ⏸️ **Deployment Automation**: Staging and production deployment workflows
- ⏸️ **Rollback Capability**: Automated rollback on deployment failure
- ⏸️ **Approval Gates**: Manual approval for production deployments

### **Deployment Strategy Comparison and Selection**

#### **Option 1: Kubernetes Production Infrastructure Checklist**
- ⏸️ **Kubernetes Cluster**: Managed cluster (EKS/GKE/AKS) with multi-AZ deployment
- ⏸️ **Phase 7.3.3 Integration**: Existing scaling services integrated with Kubernetes APIs
- ⏸️ **HPA Configuration**: Horizontal Pod Autoscaler with custom metrics from ScalingMetricsService
- ⏸️ **Service Mesh**: Istio/Linkerd for advanced traffic management and observability
- ⏸️ **Ingress Controllers**: NGINX/Traefik for load balancing and SSL termination
- ⏸️ **Database StatefulSets**: PostgreSQL with persistent volumes and replication
- ⏸️ **Redis Cluster**: High-availability Redis cluster for caching and sessions
- ⏸️ **Auto-scaling Integration**: ContainerOrchestrationService Kubernetes API integration
- ⏸️ **Custom Metrics**: ScalingMetricsService metrics exposed for HPA scaling decisions
- ⏸️ **Business-Aware Routing**: LoadBalancerService integration with Kubernetes Services
- ⏸️ **Backup Strategy**: Automated backup with Kubernetes CronJobs and persistent volumes
- ⏸️ **Monitoring**: Prometheus/Grafana with existing Sentry APM integration

#### **Option 3: Self-Hosted Infrastructure Checklist**
- ⏸️ **Hetzner Cloud Setup**: VPS/dedicated servers with cost-effective pricing
- ⏸️ **k3s/Docker Swarm**: Lightweight container orchestration
- ⏸️ **Portainer Management**: Visual container management interface
- ⏸️ **Phase 7.3.3 Integration**: Adapted scaling services for self-hosted environment
- ⏸️ **Self-Managed PostgreSQL**: Streaming replication with high availability
- ⏸️ **Self-Managed Redis**: Cluster setup with persistence and monitoring
- ⏸️ **Hetzner Load Balancer**: Cost-effective load balancing with health checks
- ⏸️ **Prometheus/Grafana**: Self-hosted monitoring stack
- ⏸️ **Backup Automation**: Restic/Borg backup with object storage
- ⏸️ **Security Hardening**: Self-managed security configurations
- ⏸️ **Cost Optimization**: 60-75% cost savings vs. managed cloud services
- ⏸️ **Migration Paths**: Clear upgrade paths to managed services

#### **Deployment Strategy Decision Matrix**

**Startup Phase 1 (100 concurrent users)**:
```yaml
recommended_strategy: "Option 3 - Self-Hosted"
reasoning:
  - cost_savings: "75% vs managed cloud ($30 vs $200/month)"
  - complexity: "Manageable for small team"
  - scalability: "Sufficient for initial growth"
  - risk: "Low - simple infrastructure"

infrastructure:
  - servers: "2x CX21 web + 1x CX31 database"
  - orchestration: "Docker Swarm (simpler than k3s)"
  - monitoring: "Basic Prometheus + Grafana"
  - backup: "Daily automated backups"
```

**Startup Phase 2 (1000 concurrent users)**:
```yaml
recommended_strategy: "Option 3 - Self-Hosted with k3s"
reasoning:
  - cost_savings: "65% vs managed cloud ($200 vs $800/month)"
  - complexity: "Medium - requires k3s expertise"
  - scalability: "Good with k3s auto-scaling"
  - risk: "Medium - more complex infrastructure"

infrastructure:
  - servers: "3x CX41 web + 2x CX51 database + 1x CX21 Redis"
  - orchestration: "k3s with HPA integration"
  - monitoring: "Full Prometheus + Grafana + AlertManager"
  - backup: "Automated backup with replication"
```

**Enterprise Phase (10000+ concurrent users)**:
```yaml
recommended_strategy: "Option 1 - Managed Kubernetes or Hybrid"
reasoning:
  - operational_overhead: "Self-hosting becomes complex"
  - reliability_requirements: "Enterprise SLA requirements"
  - team_focus: "Focus on business logic vs infrastructure"
  - compliance: "Managed services for compliance"

migration_path:
  - phase1: "Migrate database to managed services (RDS/Cloud SQL)"
  - phase2: "Migrate application to managed Kubernetes"
  - phase3: "Implement multi-region deployment"
  - phase4: "Add enterprise monitoring and compliance"
```

### **Self-Hosted Backup and Security Implementation**

#### **Automated Backup Strategy**
```bash
#!/bin/bash
# backup-automation.sh - Comprehensive backup solution

# Install Restic for efficient backups
curl -L https://github.com/restic/restic/releases/download/v0.16.2/restic_0.16.2_linux_amd64.bz2 | bunzip2 > /usr/local/bin/restic
chmod +x /usr/local/bin/restic

# Configure backup repository (Hetzner Storage Box or S3-compatible)
export RESTIC_REPOSITORY="sftp:<EMAIL>:23/culture-connect-backups"
export RESTIC_PASSWORD="your-backup-password"

# Initialize repository (run once)
restic init

# PostgreSQL backup function
backup_postgresql() {
    echo "Starting PostgreSQL backup..."

    # Create database dump
    sudo -u postgres pg_dump culture_connect_prod > /tmp/culture_connect_$(date +%Y%m%d_%H%M%S).sql

    # Backup with Restic
    restic backup /tmp/culture_connect_*.sql \
        --tag postgresql \
        --tag $(date +%Y-%m-%d)

    # Cleanup local dump
    rm /tmp/culture_connect_*.sql

    echo "PostgreSQL backup completed"
}

# Redis backup function
backup_redis() {
    echo "Starting Redis backup..."

    # Create Redis dump
    redis-cli --rdb /tmp/redis_backup_$(date +%Y%m%d_%H%M%S).rdb

    # Backup with Restic
    restic backup /tmp/redis_backup_*.rdb \
        --tag redis \
        --tag $(date +%Y-%m-%d)

    # Cleanup local dump
    rm /tmp/redis_backup_*.rdb

    echo "Redis backup completed"
}

# Application data backup function
backup_application_data() {
    echo "Starting application data backup..."

    # Backup uploaded files and logs
    restic backup /var/lib/culture-connect/uploads \
        /var/log/culture-connect \
        --tag application-data \
        --tag $(date +%Y-%m-%d)

    echo "Application data backup completed"
}

# Configuration backup function
backup_configurations() {
    echo "Starting configuration backup..."

    # Backup all configuration files
    restic backup \
        /etc/nginx \
        /etc/postgresql \
        /etc/redis \
        /etc/systemd/system/culture-connect* \
        /opt/culture-connect/docker-compose.yml \
        /opt/culture-connect/.env.production \
        --tag configurations \
        --tag $(date +%Y-%m-%d)

    echo "Configuration backup completed"
}

# Main backup execution
main() {
    echo "Starting Culture Connect backup process..."

    backup_postgresql
    backup_redis
    backup_application_data
    backup_configurations

    # Cleanup old backups (keep 30 days)
    restic forget --keep-daily 30 --prune

    echo "Backup process completed successfully"
}

# Run main function
main

# Create systemd timer for automated backups
cat > /etc/systemd/system/culture-connect-backup.service << EOF
[Unit]
Description=Culture Connect Backup Service
After=network.target

[Service]
Type=oneshot
ExecStart=/opt/culture-connect/scripts/backup-automation.sh
User=root
Environment=RESTIC_REPOSITORY=sftp:<EMAIL>:23/culture-connect-backups
Environment=RESTIC_PASSWORD_FILE=/etc/culture-connect/backup-password
EOF

cat > /etc/systemd/system/culture-connect-backup.timer << EOF
[Unit]
Description=Culture Connect Backup Timer
Requires=culture-connect-backup.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable and start backup timer
systemctl enable culture-connect-backup.timer
systemctl start culture-connect-backup.timer
```

#### **Security Hardening for Self-Hosted Deployment**
```bash
#!/bin/bash
# security-hardening.sh - Comprehensive security setup

# 1. Firewall configuration
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (change port from default 22)
ufw allow 2222/tcp

# Allow HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Allow internal network communication
ufw allow from ********/24

# Allow monitoring ports (restrict to monitoring server)
ufw allow from ********* to any port 9090  # Prometheus
ufw allow from ********* to any port 9100  # Node Exporter
ufw allow from ********* to any port 8080  # cAdvisor

# Enable firewall
ufw --force enable

# 2. SSH hardening
cat >> /etc/ssh/sshd_config << EOF
# Security hardening
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
AllowUsers culture-connect
Protocol 2
EOF

systemctl restart sshd

# 3. Fail2ban setup
apt update && apt install -y fail2ban

cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = 2222
filter = sshd
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[culture-connect-api]
enabled = true
filter = culture-connect-api
logpath = /var/log/culture-connect/api.log
maxretry = 5
bantime = 1800
EOF

# Create custom filter for Culture Connect API
cat > /etc/fail2ban/filter.d/culture-connect-api.conf << EOF
[Definition]
failregex = ^.*"POST /api/v1/auth/login".*"status": 401.*"ip": "<HOST>".*$
            ^.*"POST /api/v1/auth/register".*"status": 429.*"ip": "<HOST>".*$
ignoreregex =
EOF

systemctl enable fail2ban
systemctl start fail2ban

# 4. SSL certificate automation with Let's Encrypt
apt install -y certbot python3-certbot-nginx

# Create SSL certificate (replace with actual domain)
certbot --nginx -d api.cultureconnect.ng --non-interactive --agree-tos --email <EMAIL>

# Auto-renewal
cat > /etc/systemd/system/certbot-renewal.service << EOF
[Unit]
Description=Certbot Renewal Service

[Service]
Type=oneshot
ExecStart=/usr/bin/certbot renew --quiet --no-self-upgrade
EOF

cat > /etc/systemd/system/certbot-renewal.timer << EOF
[Unit]
Description=Certbot Renewal Timer

[Timer]
OnCalendar=daily
RandomizedDelaySec=3600

[Install]
WantedBy=timers.target
EOF

systemctl enable certbot-renewal.timer
systemctl start certbot-renewal.timer

# 5. System hardening
# Disable unnecessary services
systemctl disable bluetooth
systemctl disable cups
systemctl disable avahi-daemon

# Kernel parameter hardening
cat >> /etc/sysctl.conf << EOF
# Network security
net.ipv4.ip_forward = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.tcp_syncookies = 1

# Memory protection
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
kernel.yama.ptrace_scope = 1
EOF

sysctl -p

# 6. Automated security updates
apt install -y unattended-upgrades

cat > /etc/apt/apt.conf.d/50unattended-upgrades << EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
EOF

systemctl enable unattended-upgrades
systemctl start unattended-upgrades

echo "Security hardening completed successfully"
```

#### **Migration Paths Between Deployment Options**

**Self-Hosted to Managed Cloud Migration**:
```yaml
# migration-strategy.yml
migration_phases:
  phase_1_database_migration:
    description: "Migrate database to managed service"
    steps:
      - setup_managed_database: "Create RDS/Cloud SQL instance"
      - data_migration: "Use pg_dump/pg_restore for migration"
      - connection_update: "Update application connection strings"
      - validation: "Verify data integrity and performance"
    estimated_downtime: "2-4 hours"
    rollback_plan: "Restore from backup to self-hosted"

  phase_2_application_migration:
    description: "Migrate application to managed Kubernetes"
    steps:
      - cluster_setup: "Create managed Kubernetes cluster"
      - manifest_adaptation: "Convert k3s manifests to managed K8s"
      - deployment_testing: "Deploy to staging environment"
      - traffic_migration: "Gradual traffic shift with load balancer"
    estimated_downtime: "30 minutes (rolling deployment)"
    rollback_plan: "DNS switch back to self-hosted"

  phase_3_monitoring_migration:
    description: "Migrate to managed monitoring services"
    steps:
      - managed_monitoring: "Set up managed Prometheus/Grafana"
      - metrics_migration: "Migrate custom dashboards and alerts"
      - integration_testing: "Verify monitoring functionality"
      - cleanup: "Decommission self-hosted monitoring"
    estimated_downtime: "None (parallel operation)"

cost_analysis:
  self_hosted_monthly: "$200"
  managed_cloud_monthly: "$800"
  migration_cost: "$2000-5000 (one-time)"
  break_even_point: "6-12 months"
```

### **Security and Compliance Checklist**
- ⏸️ **WAF Configuration**: Web Application Firewall setup
- ⏸️ **DDoS Protection**: DDoS mitigation and rate limiting
- ⏸️ **Secrets Management**: Secure secret storage and rotation
- ⏸️ **Audit Logging**: Comprehensive audit trail implementation
- ⏸️ **Security Headers**: Security headers and CORS configuration
- ⏸️ **Penetration Testing**: Security assessment and vulnerability testing

---

## 🎯 Final Validation and Handover

### **Production Readiness Validation**
1. **Performance Testing**: Load testing with >1000 concurrent users
2. **Security Assessment**: Penetration testing and vulnerability assessment
3. **Disaster Recovery**: Backup and recovery procedure testing
4. **Monitoring Validation**: Alert testing and dashboard verification
5. **Documentation Review**: Operational procedures and runbooks validation

### **Go-Live Checklist**
- ⏸️ **Infrastructure Provisioned**: All production infrastructure ready
- ⏸️ **Deployment Tested**: Deployment process tested in staging
- ⏸️ **Monitoring Active**: All monitoring and alerting operational
- ⏸️ **Team Trained**: Operations team trained on production procedures
- ⏸️ **Documentation Complete**: All operational documentation finalized
- ⏸️ **Stakeholder Approval**: Business stakeholder approval for go-live

### **Post-Deployment Activities**
1. **Performance Monitoring**: 24/7 monitoring for first week
2. **Issue Resolution**: Rapid response to any production issues
3. **Performance Optimization**: Fine-tuning based on production metrics
4. **Documentation Updates**: Update procedures based on production experience
5. **Team Feedback**: Collect feedback and lessons learned

---

## 🏗️ **Technology Stack Integration with Kubernetes**

### **Existing Infrastructure Integration**

#### **Phase 7.3.3 Horizontal Scaling & Load Balancing Integration**
```python
# Kubernetes API Integration in ContainerOrchestrationService
class KubernetesIntegration:
    """
    Integration layer between existing Phase 7.3.3 services and Kubernetes APIs.

    Provides seamless integration of:
    - AutoScalingService with Kubernetes HPA
    - LoadBalancerService with Kubernetes Services
    - ScalingMetricsService with Prometheus metrics
    - ContainerOrchestrationService with Kubernetes API
    """

    async def integrate_hpa_with_scaling_service(self):
        """Integrate HPA with existing AutoScalingService."""
        # Custom metrics from ScalingMetricsService
        metrics = await self.scaling_metrics_service.get_utilization_summary()

        # Update HPA configuration based on business logic
        hpa_config = await self.generate_hpa_config(metrics)

        # Apply to Kubernetes cluster
        await self.kubernetes_client.patch_hpa(hpa_config)

    async def integrate_load_balancer_with_services(self):
        """Integrate LoadBalancerService with Kubernetes Services."""
        # Business-aware routing from LoadBalancerService
        routing_strategy = await self.load_balancer_service.analyze_routing_requirements()

        # Update Kubernetes Service configuration
        service_config = await self.generate_service_config(routing_strategy)

        # Apply to Kubernetes cluster
        await self.kubernetes_client.patch_service(service_config)
```

#### **Phase 7.3.2 Caching & Performance Optimization Integration**
```yaml
# Redis Cluster Integration with Multi-Layer Caching
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
  namespace: culture-connect
data:
  redis.conf: |
    # Integration with Phase 7.3.2 caching strategies
    maxmemory 2gb
    maxmemory-policy allkeys-lru

    # Multi-layer caching configuration
    save 900 1
    save 300 10
    save 60 10000

    # Performance optimization
    tcp-keepalive 300
    timeout 0

    # Cluster configuration
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000

    # Integration with existing cache invalidation
    notify-keyspace-events Ex
```

#### **Phase 7.3.4 CDN Optimization Integration**
```yaml
# Ingress with CDN Integration
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: culture-connect-ingress
  namespace: culture-connect
  annotations:
    # CDN integration annotations
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    # Integration with Phase 7.3.4 CDN optimization
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # CDN cache headers
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-CDN-Cache "HIT";
      }

      # API response caching
      location /api/v1/static/ {
        expires 1h;
        add_header Cache-Control "public";
      }
spec:
  tls:
  - hosts:
    - api.cultureconnect.ng
    secretName: culture-connect-tls
  rules:
  - host: api.cultureconnect.ng
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
```

### **Scalability-First Architecture Design**

#### **Multi-Region Deployment Capabilities**
```yaml
# Multi-Region Kubernetes Deployment Strategy
# Primary Region (us-east-1)
apiVersion: v1
kind: Namespace
metadata:
  name: culture-connect-primary
  labels:
    region: us-east-1
    environment: production

---
# Secondary Region (eu-west-1)
apiVersion: v1
kind: Namespace
metadata:
  name: culture-connect-secondary
  labels:
    region: eu-west-1
    environment: production

---
# Cross-region service discovery
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: culture-connect-cross-region
spec:
  hosts:
  - culture-connect-api.eu-west-1.local
  ports:
  - number: 8000
    name: http
    protocol: HTTP
  location: MESH_EXTERNAL
  resolution: DNS
```

#### **Microservices-Ready Architecture**
```yaml
# Service decomposition preparation
# Even within monolithic structure, prepare for microservices
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-booking-service
  namespace: culture-connect
spec:
  replicas: 3
  selector:
    matchLabels:
      app: booking-service
      component: booking
  template:
    metadata:
      labels:
        app: booking-service
        component: booking
    spec:
      containers:
      - name: booking-service
        image: culture-connect-api:latest
        env:
        - name: SERVICE_MODE
          value: "booking_only"  # Future microservice preparation
        - name: COMPONENT_FOCUS
          value: "booking,payment,communication"
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"

---
# Vendor management service preparation
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-vendor-service
  namespace: culture-connect
spec:
  replicas: 2
  selector:
    matchLabels:
      app: vendor-service
      component: vendor
  template:
    metadata:
      labels:
        app: vendor-service
        component: vendor
    spec:
      containers:
      - name: vendor-service
        image: culture-connect-api:latest
        env:
        - name: SERVICE_MODE
          value: "vendor_only"  # Future microservice preparation
        - name: COMPONENT_FOCUS
          value: "vendor,dashboard,analytics"
```

#### **WebSocket and Real-time Communication Scaling**
```yaml
# WebSocket service with sticky sessions
apiVersion: v1
kind: Service
metadata:
  name: culture-connect-websocket
  namespace: culture-connect
  annotations:
    # Sticky sessions for WebSocket connections
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "websocket-session"
    nginx.ingress.kubernetes.io/session-cookie-expires: "3600"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "3600"
    nginx.ingress.kubernetes.io/session-cookie-path: "/ws"
spec:
  selector:
    app: culture-connect-api
    component: websocket
  ports:
  - port: 8000
    targetPort: 8000
    name: websocket
  type: ClusterIP

---
# WebSocket HPA with connection-based scaling
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: websocket-hpa
  namespace: culture-connect
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-api
  minReplicas: 2
  maxReplicas: 15
  metrics:
  - type: Pods
    pods:
      metric:
        name: websocket_connections
      target:
        type: AverageValue
        averageValue: "500"  # 500 connections per pod
  - type: Pods
    pods:
      metric:
        name: websocket_message_rate
      target:
        type: AverageValue
        averageValue: "100"  # 100 messages/second per pod
```

### **Enterprise-Scale Growth Architecture**

#### **Database Clustering and Replication**
```yaml
# PostgreSQL Primary-Secondary with Read Replicas
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: culture-connect-postgres
  namespace: culture-connect
spec:
  instances: 3

  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"

  bootstrap:
    initdb:
      database: culture_connect_prod
      owner: culture_connect
      secret:
        name: postgresql-secrets

  storage:
    size: 100Gi
    storageClass: fast-ssd

  monitoring:
    enabled: true

  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://culture-connect-backups/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "5d"
      data:
        retention: "30d"
```

#### **Monitoring and Observability at Scale**
```yaml
# Prometheus with custom metrics from Phase 7.3.3
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: culture-connect-metrics
  namespace: culture-connect
spec:
  selector:
    matchLabels:
      app: culture-connect-api
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    # Custom metrics from ScalingMetricsService
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'culture_connect_(.*)'
      targetLabel: service
      replacement: 'culture-connect'

---
# Grafana Dashboard ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: culture-connect-dashboard
  namespace: monitoring
data:
  dashboard.json: |
    {
      "dashboard": {
        "title": "Culture Connect Production Metrics",
        "panels": [
          {
            "title": "Booking Queue Length",
            "type": "graph",
            "targets": [
              {
                "expr": "booking_queue_length",
                "legendFormat": "Queue Length"
              }
            ]
          },
          {
            "title": "Response Time P95",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, response_time_seconds_bucket)",
                "legendFormat": "P95 Response Time"
              }
            ]
          },
          {
            "title": "Concurrent Users",
            "type": "graph",
            "targets": [
              {
                "expr": "concurrent_users_total",
                "legendFormat": "Active Users"
              }
            ]
          }
        ]
      }
    }
```

---

This production deployment tracking document will be updated throughout Phase 9 implementation with progress, completion timestamps, and final results. Upon completion, both this document and the main ToDo.md will be updated to reflect 100% project completion.
