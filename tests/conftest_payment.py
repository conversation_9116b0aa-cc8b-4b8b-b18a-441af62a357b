"""
Pytest configuration and shared fixtures for payment system tests.

This module provides comprehensive test fixtures and configuration for payment system tests:
- Database session management with payment model support
- Mock payment provider services
- Test data factories for payment models
- Security and encryption mocking utilities
- Performance testing utilities

Implements Phase 1 Payment & Transaction Management System testing infrastructure.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from typing import AsyncGenerator, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.models.payment import Payment, PaymentMethod, PaymentStatus, PaymentMethodType
from app.models.transaction_models import Transaction, TransactionEvent, TransactionStatus, TransactionEventType
from app.models.payout_models import VendorPayout, EscrowAccount, PayoutStatus, EscrowStatus, ReleaseCondition
from app.models.financial_models import RevenueRecord, ReconciliationRecord, RevenueCategory, ReconciliationStatus
from app.core.payment.config import PaymentProviderType


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


# Payment Test Data Factories
@pytest.fixture
def sample_payment_data():
    """Sample payment creation data."""
    return {
        "booking_id": 1,
        "user_id": 1,
        "vendor_id": 1,
        "amount": Decimal("1000.00"),
        "currency": "NGN",
        "provider": PaymentProviderType.PAYSTACK,
        "status": PaymentStatus.PENDING,
        "transaction_reference": f"TEST-{uuid4().hex[:8]}",
        "platform_fee": Decimal("50.00"),
        "provider_fee": Decimal("30.00"),
        "net_amount": Decimal("920.00")
    }


@pytest.fixture
def sample_payment_method_data():
    """Sample payment method creation data."""
    return {
        "user_id": 1,
        "type": PaymentMethodType.CARD,
        "provider": PaymentProviderType.STRIPE,
        "display_name": "Visa ending in 4242",
        "last_four": "4242",
        "brand": "visa",
        "expiry_month": 12,
        "expiry_year": 2025,
        "is_default": True,
        "is_active": True,
        "is_verified": True
    }


@pytest.fixture
def sample_transaction_data():
    """Sample transaction creation data."""
    return {
        "payment_id": 1,
        "type": "charge",
        "status": TransactionStatus.PENDING,
        "amount": Decimal("1000.00"),
        "currency": "NGN",
        "provider": PaymentProviderType.PAYSTACK,
        "reference_id": f"TXN-{uuid4().hex[:8]}",
        "provider_fee": Decimal("30.00"),
        "platform_fee": Decimal("50.00"),
        "net_amount": Decimal("920.00")
    }


@pytest.fixture
def sample_vendor_payout_data():
    """Sample vendor payout creation data."""
    period_start = datetime.now(timezone.utc) - timedelta(days=7)
    period_end = datetime.now(timezone.utc)
    
    return {
        "vendor_id": 1,
        "period_start": period_start,
        "period_end": period_end,
        "total_earnings": Decimal("5000.00"),
        "platform_fee": Decimal("250.00"),
        "processing_fee": Decimal("50.00"),
        "net_amount": Decimal("4700.00"),
        "currency": "NGN",
        "provider": PaymentProviderType.PAYSTACK,
        "status": PayoutStatus.PENDING,
        "reference_id": f"PAYOUT-{uuid4().hex[:8]}"
    }


@pytest.fixture
def sample_escrow_account_data():
    """Sample escrow account creation data."""
    return {
        "booking_id": 1,
        "payment_id": 1,
        "amount": Decimal("1000.00"),
        "currency": "NGN",
        "status": EscrowStatus.ACTIVE,
        "release_condition": ReleaseCondition.SERVICE_COMPLETION,
        "hold_until": datetime.now(timezone.utc) + timedelta(days=7),
        "auto_release_enabled": True
    }


@pytest.fixture
def sample_revenue_record_data():
    """Sample revenue record creation data."""
    return {
        "booking_id": 1,
        "payment_id": 1,
        "vendor_id": 1,
        "user_id": 1,
        "category": RevenueCategory.SERVICE_BOOKING,
        "gross_amount": Decimal("1000.00"),
        "platform_fee": Decimal("50.00"),
        "processing_fee": Decimal("30.00"),
        "net_vendor_amount": Decimal("920.00"),
        "net_platform_amount": Decimal("80.00"),
        "currency": "NGN",
        "provider": PaymentProviderType.PAYSTACK,
        "transaction_date": datetime.now(timezone.utc),
        "reference_id": f"REV-{uuid4().hex[:8]}"
    }


@pytest.fixture
def sample_reconciliation_record_data():
    """Sample reconciliation record creation data."""
    period_start = datetime.now(timezone.utc) - timedelta(days=1)
    period_end = datetime.now(timezone.utc)
    
    return {
        "provider": PaymentProviderType.PAYSTACK,
        "period_start": period_start,
        "period_end": period_end,
        "expected_amount": Decimal("10000.00"),
        "actual_amount": Decimal("9950.00"),
        "variance": Decimal("-50.00"),
        "currency": "NGN",
        "status": ReconciliationStatus.COMPLETED,
        "expected_transaction_count": 25,
        "actual_transaction_count": 24,
        "reference_id": f"REC-{uuid4().hex[:8]}"
    }


# Mock Payment Provider Services
@pytest.fixture
def mock_stripe_service():
    """Mock Stripe payment service."""
    service = MagicMock()
    service.create_payment_intent = AsyncMock(return_value={
        "id": "pi_stripe_test_123",
        "client_secret": "pi_stripe_test_123_secret",
        "status": "requires_payment_method"
    })
    service.confirm_payment_intent = AsyncMock(return_value={
        "id": "pi_stripe_test_123",
        "status": "succeeded"
    })
    service.create_payout = AsyncMock(return_value={
        "id": "po_stripe_test_456",
        "status": "pending"
    })
    return service


@pytest.fixture
def mock_paystack_service():
    """Mock Paystack payment service."""
    service = MagicMock()
    service.initialize_transaction = AsyncMock(return_value={
        "reference": "paystack_test_123",
        "authorization_url": "https://checkout.paystack.com/test",
        "access_code": "access_code_test"
    })
    service.verify_transaction = AsyncMock(return_value={
        "reference": "paystack_test_123",
        "status": "success",
        "amount": 100000  # In kobo
    })
    service.create_transfer = AsyncMock(return_value={
        "transfer_code": "TRF_paystack_test_789",
        "status": "pending"
    })
    return service


@pytest.fixture
def mock_busha_service():
    """Mock Busha crypto payment service."""
    service = MagicMock()
    service.create_payment = AsyncMock(return_value={
        "payment_id": "busha_test_123",
        "wallet_address": "bc1qtest123456789",
        "amount_btc": "0.01",
        "status": "pending"
    })
    service.verify_payment = AsyncMock(return_value={
        "payment_id": "busha_test_123",
        "status": "confirmed",
        "confirmations": 6
    })
    return service


# Security and Encryption Mocks
@pytest.fixture
def mock_payment_encryption():
    """Mock payment data encryption utilities."""
    encryption = MagicMock()
    encryption.encrypt_payment_data = MagicMock(return_value="encrypted_data_123")
    encryption.decrypt_payment_data = MagicMock(return_value={"card_number": "****4242"})
    encryption.generate_payment_token = MagicMock(return_value="token_abc123")
    return encryption


@pytest.fixture
def mock_webhook_validator():
    """Mock webhook signature validator."""
    validator = MagicMock()
    validator.validate_stripe_webhook = MagicMock(return_value=True)
    validator.validate_paystack_webhook = MagicMock(return_value=True)
    validator.validate_busha_webhook = MagicMock(return_value=True)
    return validator


# Performance Testing Utilities
@pytest.fixture
def performance_timer():
    """Utility for measuring test performance."""
    import time
    
    class PerformanceTimer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed_ms(self):
            if self.start_time and self.end_time:
                return (self.end_time - self.start_time) * 1000
            return None
        
        def assert_under_ms(self, max_ms: float, operation: str = "Operation"):
            assert self.elapsed_ms is not None, "Timer not properly started/stopped"
            assert self.elapsed_ms < max_ms, f"{operation} took {self.elapsed_ms}ms, expected <{max_ms}ms"
    
    return PerformanceTimer()


# Test Data Generation Utilities
@pytest.fixture
def payment_data_generator():
    """Utility for generating test payment data."""
    class PaymentDataGenerator:
        def __init__(self):
            self.counter = 0
        
        def generate_payment(self, **overrides) -> Dict[str, Any]:
            self.counter += 1
            base_data = {
                "booking_id": self.counter,
                "user_id": 1,
                "vendor_id": 1,
                "amount": Decimal("1000.00"),
                "currency": "NGN",
                "provider": PaymentProviderType.PAYSTACK,
                "status": PaymentStatus.PENDING,
                "transaction_reference": f"GEN-{self.counter}-{uuid4().hex[:8]}"
            }
            base_data.update(overrides)
            return base_data
        
        def generate_payment_method(self, **overrides) -> Dict[str, Any]:
            self.counter += 1
            base_data = {
                "user_id": 1,
                "type": PaymentMethodType.CARD,
                "provider": PaymentProviderType.STRIPE,
                "display_name": f"Test Card {self.counter}",
                "last_four": f"{self.counter:04d}",
                "is_active": True
            }
            base_data.update(overrides)
            return base_data
        
        def generate_transaction(self, payment_id: int = None, **overrides) -> Dict[str, Any]:
            self.counter += 1
            base_data = {
                "payment_id": payment_id or self.counter,
                "type": "charge",
                "status": TransactionStatus.PENDING,
                "amount": Decimal("1000.00"),
                "provider": PaymentProviderType.PAYSTACK,
                "reference_id": f"TXN-GEN-{self.counter}-{uuid4().hex[:8]}"
            }
            base_data.update(overrides)
            return base_data
        
        def generate_bulk_payments(self, count: int, **base_overrides) -> list:
            return [self.generate_payment(**base_overrides) for _ in range(count)]
    
    return PaymentDataGenerator()


# Error Simulation Utilities
@pytest.fixture
def payment_error_simulator():
    """Utility for simulating payment-related errors."""
    def simulate_error(error_type="payment_failed"):
        if error_type == "payment_failed":
            return Exception("Payment processing failed")
        elif error_type == "insufficient_funds":
            return Exception("Insufficient funds")
        elif error_type == "card_declined":
            return Exception("Card declined by issuer")
        elif error_type == "network_timeout":
            return Exception("Network timeout during payment")
        elif error_type == "provider_error":
            return Exception("Payment provider service unavailable")
        else:
            return Exception("Generic payment error")
    
    return simulate_error


# Database Transaction Utilities
@pytest.fixture
def payment_db_utils():
    """Utilities for payment database operations in tests."""
    class PaymentDbUtils:
        @staticmethod
        async def create_test_payment(session: AsyncSession, **kwargs) -> Payment:
            payment_data = {
                "booking_id": 1,
                "user_id": 1,
                "vendor_id": 1,
                "amount": Decimal("1000.00"),
                "transaction_reference": f"TEST-{uuid4().hex[:8]}"
            }
            payment_data.update(kwargs)
            
            payment = Payment(**payment_data)
            session.add(payment)
            await session.flush()
            return payment
        
        @staticmethod
        async def create_test_payment_method(session: AsyncSession, **kwargs) -> PaymentMethod:
            method_data = {
                "user_id": 1,
                "type": PaymentMethodType.CARD,
                "provider": PaymentProviderType.STRIPE,
                "display_name": "Test Card"
            }
            method_data.update(kwargs)
            
            payment_method = PaymentMethod(**method_data)
            session.add(payment_method)
            await session.flush()
            return payment_method
        
        @staticmethod
        async def create_test_transaction(session: AsyncSession, payment_id: int, **kwargs) -> Transaction:
            transaction_data = {
                "payment_id": payment_id,
                "type": "charge",
                "status": TransactionStatus.PENDING,
                "amount": Decimal("1000.00"),
                "provider": PaymentProviderType.PAYSTACK,
                "reference_id": f"TXN-TEST-{uuid4().hex[:8]}"
            }
            transaction_data.update(kwargs)
            
            transaction = Transaction(**transaction_data)
            session.add(transaction)
            await session.flush()
            return transaction
    
    return PaymentDbUtils()


# Provider Configuration Mocks
@pytest.fixture
def mock_payment_provider_config():
    """Mock payment provider configuration."""
    config = MagicMock()
    config.get_provider_for_country = MagicMock(return_value=PaymentProviderType.PAYSTACK)
    config.get_provider_config = MagicMock(return_value={
        "api_key": "test_api_key",
        "secret_key": "test_secret_key",
        "webhook_secret": "test_webhook_secret"
    })
    config.is_provider_available = MagicMock(return_value=True)
    return config
