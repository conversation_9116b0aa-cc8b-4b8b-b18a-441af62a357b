"""
Scaling API endpoints for Culture Connect Backend API.

This module provides comprehensive REST API endpoints for horizontal scaling and load balancing:
- Scaling metrics management and analysis
- Auto-scaling policy configuration and management
- Load balancer configuration and optimization
- Container orchestration and lifecycle management
- Scaling event tracking and statistics

Implements Phase 7.3.3 requirements with FastAPI router patterns, RBAC integration,
comprehensive validation, and seamless integration with scaling services.
"""

from typing import Dict, Any, Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from fastapi.responses import JSONResponse

from app.api.dependencies import (
    get_current_user, get_db, require_permission,
    get_correlation_id, get_cache_manager
)
from app.models.user import User
from app.schemas.scaling_schemas import (
    ScalingMetricsCreate, ScalingMetricsResponse, ScalingMetricsQuery,
    AutoScalingPolicyCreate, AutoScalingPolicyUpdate, AutoScalingPolicyResponse,
    LoadBalancerConfigCreate, LoadBalancerConfigUpdate, LoadBalancerConfigResponse,
    ContainerMetricsCreate, ContainerMetricsResponse,
    ScalingEventResponse, ScalingEventQuery, ScalingDashboardData,
    ScalingRecommendation
)
from app.services.scaling_services import (
    ScalingMetricsService, AutoScalingService, LoadBalancerService,
    ContainerOrchestrationService
)
from app.repositories.scaling_repositories import (
    ScalingMetricsRepository, AutoScalingPolicyRepository,
    LoadBalancerConfigRepository, ContainerMetricsRepository,
    ScalingEventRepository
)
from app.core.logging import get_logger
from app.services.base import ValidationError, NotFoundError, PermissionError

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/scaling", tags=["scaling-load-balancing"])


# Dependency injection for services
async def get_scaling_metrics_service(
    db=Depends(get_db),
    cache_manager=Depends(get_cache_manager)
) -> ScalingMetricsService:
    """Get scaling metrics service with dependencies."""
    scaling_metrics_repo = ScalingMetricsRepository(db, cache_manager)
    return ScalingMetricsService(scaling_metrics_repo)


async def get_auto_scaling_service(
    db=Depends(get_db),
    cache_manager=Depends(get_cache_manager),
    scaling_metrics_service: ScalingMetricsService = Depends(get_scaling_metrics_service)
) -> AutoScalingService:
    """Get auto-scaling service with dependencies."""
    auto_scaling_repo = AutoScalingPolicyRepository(db, cache_manager)
    scaling_event_repo = ScalingEventRepository(db, cache_manager)
    return AutoScalingService(auto_scaling_repo, scaling_metrics_service, scaling_event_repo)


async def get_load_balancer_service(
    db=Depends(get_db),
    cache_manager=Depends(get_cache_manager),
    scaling_metrics_service: ScalingMetricsService = Depends(get_scaling_metrics_service)
) -> LoadBalancerService:
    """Get load balancer service with dependencies."""
    load_balancer_repo = LoadBalancerConfigRepository(db, cache_manager)
    return LoadBalancerService(load_balancer_repo, scaling_metrics_service)


async def get_container_orchestration_service(
    db=Depends(get_db),
    cache_manager=Depends(get_cache_manager),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service)
) -> ContainerOrchestrationService:
    """Get container orchestration service with dependencies."""
    container_metrics_repo = ContainerMetricsRepository(db, cache_manager)
    scaling_event_repo = ScalingEventRepository(db, cache_manager)
    return ContainerOrchestrationService(
        container_metrics_repo, auto_scaling_service, scaling_event_repo
    )


# Scaling Metrics Endpoints
@router.post(
    "/metrics",
    response_model=ScalingMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record scaling metric",
    description="Record a new scaling metric for performance monitoring and scaling decisions"
)
async def record_scaling_metric(
    metric_data: ScalingMetricsCreate = Body(..., description="Scaling metric data"),
    current_user: User = Depends(get_current_user),
    scaling_service: ScalingMetricsService = Depends(get_scaling_metrics_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_manage"))
) -> ScalingMetricsResponse:
    """
    Record a new scaling metric.

    Requires 'scaling_manage' permission.
    """
    try:
        logger.info(f"Recording scaling metric: {metric_data.metric_name} for {metric_data.component}")

        metric = await scaling_service.record_metric(
            metric_data=metric_data,
            correlation_id=correlation_id
        )

        return ScalingMetricsResponse.model_validate(metric)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error recording scaling metric: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record scaling metric"
        )


@router.get(
    "/metrics",
    response_model=List[ScalingMetricsResponse],
    summary="Get scaling metrics",
    description="Retrieve scaling metrics with optional filtering"
)
async def get_scaling_metrics(
    component: Optional[str] = Query(None, description="Filter by component"),
    metric_type: Optional[str] = Query(None, description="Filter by metric type"),
    metric_name: Optional[str] = Query(None, description="Filter by metric name"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of metrics to return"),
    current_user: User = Depends(get_current_user),
    scaling_service: ScalingMetricsService = Depends(get_scaling_metrics_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> List[ScalingMetricsResponse]:
    """
    Get scaling metrics with optional filtering.

    Requires 'scaling_view' permission.
    """
    try:
        if component:
            metrics = await scaling_service.get_component_metrics(
                component=component,
                metric_types=[metric_type] if metric_type else None,
                limit=limit,
                correlation_id=correlation_id
            )
        else:
            # Get utilization summary for all components
            utilization = await scaling_service.get_utilization_summary(
                correlation_id=correlation_id
            )
            # Convert to metrics format for response
            metrics = []

        return [ScalingMetricsResponse.model_validate(metric) for metric in metrics]

    except Exception as e:
        logger.error(f"Error getting scaling metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve scaling metrics"
        )


@router.get(
    "/metrics/{component}/time-series",
    response_model=List[Dict[str, Any]],
    summary="Get metrics time series",
    description="Get time-series metrics data for scaling analysis"
)
async def get_metrics_time_series(
    component: str = Path(..., description="Component name"),
    metric_name: str = Query(..., description="Metric name"),
    hours: int = Query(24, ge=1, le=168, description="Number of hours of data"),
    interval_minutes: int = Query(5, ge=1, le=60, description="Aggregation interval in minutes"),
    current_user: User = Depends(get_current_user),
    scaling_service: ScalingMetricsService = Depends(get_scaling_metrics_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> List[Dict[str, Any]]:
    """
    Get time-series metrics data for analysis.

    Requires 'scaling_view' permission.
    """
    try:
        time_series = await scaling_service.get_metrics_time_series(
            component=component,
            metric_name=metric_name,
            hours=hours,
            interval_minutes=interval_minutes,
            correlation_id=correlation_id
        )

        return time_series

    except Exception as e:
        logger.error(f"Error getting time-series metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve time-series metrics"
        )


@router.get(
    "/metrics/utilization",
    response_model=Dict[str, Dict[str, Any]],
    summary="Get utilization summary",
    description="Get current utilization summary for scaling decisions"
)
async def get_utilization_summary(
    components: Optional[List[str]] = Query(None, description="Filter by components"),
    current_user: User = Depends(get_current_user),
    scaling_service: ScalingMetricsService = Depends(get_scaling_metrics_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> Dict[str, Dict[str, Any]]:
    """
    Get current utilization summary for scaling decisions.

    Requires 'scaling_view' permission.
    """
    try:
        summary = await scaling_service.get_utilization_summary(
            components=components,
            correlation_id=correlation_id
        )

        return summary

    except Exception as e:
        logger.error(f"Error getting utilization summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve utilization summary"
        )


@router.get(
    "/metrics/{component}/analysis",
    response_model=Dict[str, Any],
    summary="Analyze scaling need",
    description="Analyze if scaling is needed for a component"
)
async def analyze_scaling_need(
    component: str = Path(..., description="Component name"),
    current_user: User = Depends(get_current_user),
    scaling_service: ScalingMetricsService = Depends(get_scaling_metrics_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> Dict[str, Any]:
    """
    Analyze if scaling is needed for a component.

    Requires 'scaling_view' permission.
    """
    try:
        analysis = await scaling_service.analyze_scaling_need(
            component=component,
            correlation_id=correlation_id
        )

        return analysis

    except Exception as e:
        logger.error(f"Error analyzing scaling need: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze scaling need"
        )


# Auto-Scaling Policy Endpoints
@router.post(
    "/policies",
    response_model=AutoScalingPolicyResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create auto-scaling policy",
    description="Create a new auto-scaling policy for a component"
)
async def create_auto_scaling_policy(
    policy_data: AutoScalingPolicyCreate = Body(..., description="Auto-scaling policy data"),
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_manage"))
) -> AutoScalingPolicyResponse:
    """
    Create a new auto-scaling policy.

    Requires 'scaling_manage' permission.
    """
    try:
        logger.info(f"Creating auto-scaling policy: {policy_data.name} for {policy_data.component}")

        policy = await auto_scaling_service.create_scaling_policy(
            policy_data=policy_data,
            correlation_id=correlation_id
        )

        return AutoScalingPolicyResponse.model_validate(policy)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating auto-scaling policy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create auto-scaling policy"
        )


@router.get(
    "/policies",
    response_model=List[AutoScalingPolicyResponse],
    summary="Get auto-scaling policies",
    description="Retrieve all active auto-scaling policies"
)
async def get_auto_scaling_policies(
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> List[AutoScalingPolicyResponse]:
    """
    Get all active auto-scaling policies.

    Requires 'scaling_view' permission.
    """
    try:
        policies = await auto_scaling_service.get_active_policies(
            correlation_id=correlation_id
        )

        return [AutoScalingPolicyResponse.model_validate(policy) for policy in policies]

    except Exception as e:
        logger.error(f"Error getting auto-scaling policies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve auto-scaling policies"
        )


@router.get(
    "/policies/{policy_id}",
    response_model=AutoScalingPolicyResponse,
    summary="Get auto-scaling policy",
    description="Retrieve a specific auto-scaling policy by ID"
)
async def get_auto_scaling_policy(
    policy_id: UUID = Path(..., description="Policy ID"),
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_view"))
) -> AutoScalingPolicyResponse:
    """
    Get a specific auto-scaling policy.

    Requires 'scaling_view' permission.
    """
    try:
        # Get policy from repository
        policy = await auto_scaling_service.auto_scaling_repo.get_by_id(policy_id)

        if not policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Auto-scaling policy not found: {policy_id}"
            )

        return AutoScalingPolicyResponse.model_validate(policy)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting auto-scaling policy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve auto-scaling policy"
        )


@router.put(
    "/policies/{policy_id}",
    response_model=AutoScalingPolicyResponse,
    summary="Update auto-scaling policy",
    description="Update an existing auto-scaling policy"
)
async def update_auto_scaling_policy(
    policy_id: UUID = Path(..., description="Policy ID"),
    policy_update: AutoScalingPolicyUpdate = Body(..., description="Policy update data"),
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_manage"))
) -> AutoScalingPolicyResponse:
    """
    Update an existing auto-scaling policy.

    Requires 'scaling_manage' permission.
    """
    try:
        # Check if policy exists
        existing_policy = await auto_scaling_service.auto_scaling_repo.get_by_id(policy_id)
        if not existing_policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Auto-scaling policy not found: {policy_id}"
            )

        # Update policy
        updated_policy = await auto_scaling_service.auto_scaling_repo.update(
            policy_id,
            policy_update.model_dump(exclude_unset=True)
        )

        logger.info(f"Updated auto-scaling policy: {policy_id}")

        return AutoScalingPolicyResponse.model_validate(updated_policy)

    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error updating auto-scaling policy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update auto-scaling policy"
        )


@router.delete(
    "/policies/{policy_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete auto-scaling policy",
    description="Delete an auto-scaling policy"
)
async def delete_auto_scaling_policy(
    policy_id: UUID = Path(..., description="Policy ID"),
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_manage"))
):
    """
    Delete an auto-scaling policy.

    Requires 'scaling_manage' permission.
    """
    try:
        # Check if policy exists
        existing_policy = await auto_scaling_service.auto_scaling_repo.get_by_id(policy_id)
        if not existing_policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Auto-scaling policy not found: {policy_id}"
            )

        # Delete policy
        await auto_scaling_service.auto_scaling_repo.delete(policy_id)

        logger.info(f"Deleted auto-scaling policy: {policy_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting auto-scaling policy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete auto-scaling policy"
        )


# Scaling Decision Endpoints
@router.post(
    "/decisions/evaluate",
    response_model=List[Dict[str, Any]],
    summary="Evaluate scaling policies",
    description="Evaluate all active scaling policies and get scaling decisions"
)
async def evaluate_scaling_policies(
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_manage"))
) -> List[Dict[str, Any]]:
    """
    Evaluate all active scaling policies and get scaling decisions.

    Requires 'scaling_manage' permission.
    """
    try:
        decisions = await auto_scaling_service.evaluate_scaling_policies(
            correlation_id=correlation_id
        )

        return decisions

    except Exception as e:
        logger.error(f"Error evaluating scaling policies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to evaluate scaling policies"
        )


@router.post(
    "/decisions/execute",
    response_model=Dict[str, Any],
    summary="Execute scaling decision",
    description="Execute a specific scaling decision"
)
async def execute_scaling_decision(
    decision: Dict[str, Any] = Body(..., description="Scaling decision to execute"),
    current_user: User = Depends(get_current_user),
    auto_scaling_service: AutoScalingService = Depends(get_auto_scaling_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_execute"))
) -> Dict[str, Any]:
    """
    Execute a specific scaling decision.

    Requires 'scaling_execute' permission.
    """
    try:
        result = await auto_scaling_service.execute_scaling_decision(
            decision=decision,
            correlation_id=correlation_id
        )

        return result

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error executing scaling decision: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute scaling decision"
        )


# Load Balancer Configuration Endpoints
@router.post(
    "/load-balancer/configs",
    response_model=LoadBalancerConfigResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create load balancer configuration",
    description="Create a new load balancer configuration for a service"
)
async def create_load_balancer_config(
    config_data: LoadBalancerConfigCreate = Body(..., description="Load balancer configuration data"),
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_manage"))
) -> LoadBalancerConfigResponse:
    """
    Create a new load balancer configuration.

    Requires 'load_balancer_manage' permission.
    """
    try:
        logger.info(f"Creating load balancer config: {config_data.name} for {config_data.service_name}")

        config = await load_balancer_service.create_load_balancer_config(
            config_data=config_data,
            correlation_id=correlation_id
        )

        return LoadBalancerConfigResponse.model_validate(config)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating load balancer config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create load balancer configuration"
        )


@router.get(
    "/load-balancer/configs",
    response_model=List[LoadBalancerConfigResponse],
    summary="Get load balancer configurations",
    description="Retrieve all active load balancer configurations"
)
async def get_load_balancer_configs(
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_view"))
) -> List[LoadBalancerConfigResponse]:
    """
    Get all active load balancer configurations.

    Requires 'load_balancer_view' permission.
    """
    try:
        configs = await load_balancer_service.load_balancer_repo.get_active_configs()

        return [LoadBalancerConfigResponse.model_validate(config) for config in configs]

    except Exception as e:
        logger.error(f"Error getting load balancer configs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve load balancer configurations"
        )


@router.get(
    "/load-balancer/routing/{service_name}",
    response_model=Dict[str, Any],
    summary="Get optimal routing strategy",
    description="Get optimal routing strategy for a service based on business metrics"
)
async def get_optimal_routing_strategy(
    service_name: str = Path(..., description="Service name"),
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_view"))
) -> Dict[str, Any]:
    """
    Get optimal routing strategy for a service.

    Requires 'load_balancer_view' permission.
    """
    try:
        strategy = await load_balancer_service.get_optimal_routing_strategy(
            service_name=service_name,
            correlation_id=correlation_id
        )

        return strategy

    except Exception as e:
        logger.error(f"Error getting routing strategy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get optimal routing strategy"
        )


@router.get(
    "/load-balancer/health-checks/{service_name}",
    response_model=Dict[str, Any],
    summary="Get health check policies",
    description="Get health check policy recommendations for a service"
)
async def get_health_check_policies(
    service_name: str = Path(..., description="Service name"),
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_view"))
) -> Dict[str, Any]:
    """
    Get health check policy recommendations for a service.

    Requires 'load_balancer_view' permission.
    """
    try:
        policies = await load_balancer_service.evaluate_health_check_policies(
            service_name=service_name,
            correlation_id=correlation_id
        )

        return policies

    except Exception as e:
        logger.error(f"Error getting health check policies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get health check policies"
        )


@router.put(
    "/load-balancer/session-affinity/{service_name}",
    response_model=Dict[str, Any],
    summary="Manage session affinity",
    description="Enable or disable session affinity for a service"
)
async def manage_session_affinity(
    service_name: str = Path(..., description="Service name"),
    enable_affinity: bool = Body(..., description="Whether to enable session affinity"),
    affinity_config: Optional[Dict[str, Any]] = Body(None, description="Affinity configuration"),
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_manage"))
) -> Dict[str, Any]:
    """
    Manage session affinity for a service.

    Requires 'load_balancer_manage' permission.
    """
    try:
        result = await load_balancer_service.manage_session_affinity(
            service_name=service_name,
            enable_affinity=enable_affinity,
            affinity_config=affinity_config,
            correlation_id=correlation_id
        )

        return result

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error managing session affinity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to manage session affinity"
        )


@router.put(
    "/load-balancer/upstream/{service_name}",
    response_model=LoadBalancerConfigResponse,
    summary="Update upstream servers",
    description="Update upstream servers for a load balancer configuration"
)
async def update_upstream_servers(
    service_name: str = Path(..., description="Service name"),
    upstream_servers: List[Dict[str, Any]] = Body(..., description="Upstream server configurations"),
    current_user: User = Depends(get_current_user),
    load_balancer_service: LoadBalancerService = Depends(get_load_balancer_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("load_balancer_manage"))
) -> LoadBalancerConfigResponse:
    """
    Update upstream servers for a load balancer configuration.

    Requires 'load_balancer_manage' permission.
    """
    try:
        config = await load_balancer_service.update_upstream_servers(
            service_name=service_name,
            upstream_servers=upstream_servers,
            correlation_id=correlation_id
        )

        return LoadBalancerConfigResponse.model_validate(config)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error updating upstream servers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update upstream servers"
        )


# Container Orchestration Endpoints
@router.post(
    "/containers/metrics",
    response_model=ContainerMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record container metrics",
    description="Record container performance metrics for scaling decisions"
)
async def record_container_metrics(
    metrics_data: ContainerMetricsCreate = Body(..., description="Container metrics data"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("container_manage"))
) -> ContainerMetricsResponse:
    """
    Record container performance metrics.

    Requires 'container_manage' permission.
    """
    try:
        logger.info(f"Recording container metrics for pod: {metrics_data.pod_name}")

        metrics = await container_service.record_container_metrics(
            metrics_data=metrics_data,
            correlation_id=correlation_id
        )

        return ContainerMetricsResponse.model_validate(metrics)

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error recording container metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record container metrics"
        )


@router.get(
    "/containers/utilization/{namespace}",
    response_model=Dict[str, Any],
    summary="Get namespace utilization",
    description="Get resource utilization summary for a Kubernetes namespace"
)
async def get_namespace_utilization(
    namespace: str = Path(..., description="Kubernetes namespace"),
    time_window_minutes: int = Query(10, ge=1, le=60, description="Time window for metrics aggregation"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("container_view"))
) -> Dict[str, Any]:
    """
    Get resource utilization summary for a namespace.

    Requires 'container_view' permission.
    """
    try:
        utilization = await container_service.get_namespace_utilization(
            namespace=namespace,
            time_window_minutes=time_window_minutes,
            correlation_id=correlation_id
        )

        return utilization

    except Exception as e:
        logger.error(f"Error getting namespace utilization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get namespace utilization"
        )


@router.post(
    "/containers/lifecycle",
    response_model=Dict[str, Any],
    summary="Manage container lifecycle",
    description="Execute container lifecycle operations (scale, restart, update, rollback)"
)
async def manage_container_lifecycle(
    namespace: str = Body(..., description="Kubernetes namespace"),
    deployment_name: str = Body(..., description="Deployment name"),
    action: str = Body(..., description="Lifecycle action (scale, restart, update, rollback)"),
    target_replicas: Optional[int] = Body(None, description="Target replica count for scaling"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("container_execute"))
) -> Dict[str, Any]:
    """
    Execute container lifecycle operations.

    Requires 'container_execute' permission.
    """
    try:
        logger.info(f"Managing container lifecycle: {action} for {deployment_name} in {namespace}")

        result = await container_service.manage_container_lifecycle(
            namespace=namespace,
            deployment_name=deployment_name,
            action=action,
            target_replicas=target_replicas,
            correlation_id=correlation_id
        )

        return result

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error managing container lifecycle: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to manage container lifecycle"
        )


@router.post(
    "/containers/coordination",
    response_model=List[Dict[str, Any]],
    summary="Coordinate cross-service scaling",
    description="Coordinate scaling across dependent services based on business logic"
)
async def coordinate_cross_service_scaling(
    primary_service: str = Body(..., description="Primary service driving scaling decisions"),
    dependent_services: List[str] = Body(..., description="List of dependent services to scale"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("scaling_execute"))
) -> List[Dict[str, Any]]:
    """
    Coordinate scaling across dependent services.

    Requires 'scaling_execute' permission.
    """
    try:
        logger.info(f"Coordinating cross-service scaling: {primary_service} -> {dependent_services}")

        results = await container_service.coordinate_cross_service_scaling(
            primary_service=primary_service,
            dependent_services=dependent_services,
            correlation_id=correlation_id
        )

        return results

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error coordinating cross-service scaling: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to coordinate cross-service scaling"
        )


@router.post(
    "/containers/manifests",
    response_model=Dict[str, str],
    summary="Generate Kubernetes manifests",
    description="Generate Kubernetes manifests for service deployment"
)
async def generate_kubernetes_manifests(
    service_config: Dict[str, Any] = Body(..., description="Service configuration for manifest generation"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("container_manage"))
) -> Dict[str, str]:
    """
    Generate Kubernetes manifests for service deployment.

    Requires 'container_manage' permission.
    """
    try:
        logger.info(f"Generating Kubernetes manifests for service: {service_config.get('name', 'unknown')}")

        manifests = await container_service.generate_kubernetes_manifests(
            service_config=service_config,
            correlation_id=correlation_id
        )

        return manifests

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error generating Kubernetes manifests: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate Kubernetes manifests"
        )


@router.get(
    "/containers/custom-metrics/{namespace}",
    response_model=Dict[str, Any],
    summary="Export custom metrics",
    description="Export custom business metrics for Kubernetes HPA consumption"
)
async def export_custom_metrics(
    namespace: str = Path(..., description="Kubernetes namespace"),
    current_user: User = Depends(get_current_user),
    container_service: ContainerOrchestrationService = Depends(get_container_orchestration_service),
    correlation_id: str = Depends(get_correlation_id),
    _: None = Depends(require_permission("container_view"))
) -> Dict[str, Any]:
    """
    Export custom business metrics for Kubernetes HPA.

    Requires 'container_view' permission.
    """
    try:
        metrics = await container_service.export_custom_metrics(
            namespace=namespace,
            correlation_id=correlation_id
        )

        return metrics

    except Exception as e:
        logger.error(f"Error exporting custom metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export custom metrics"
        )
