"""
Performance validation tests for geolocation functionality.

This module provides comprehensive performance testing for:
- <100ms geolocation detection performance targets
- >90% cache hit rate validation
- Circuit breaker performance under load
- Fallback mechanism response time testing
- Memory and CPU usage profiling

Follows Culture Connect Backend testing standards with performance benchmarks.
"""

import pytest
import asyncio
import time
import psutil
import statistics
from typing import Dict, Any, List, Tuple
from unittest.mock import patch, AsyncMock
import gc
import tracemalloc

from app.services.geolocation_service import get_geolocation_service
from app.core.circuit_breaker import get_geolocation_circuit_breaker
from app.core.geolocation_resilience import get_geolocation_resilience_manager
from tests.fixtures.geolocation_fixtures import (
    GeolocationTestData,
    GeolocationTestHelper,
    TEST_CONFIG
)


class TestGeolocationPerformance:
    """Performance tests for geolocation functionality."""
    
    @pytest.mark.asyncio
    async def test_single_detection_performance(self, mock_geolocation_service):
        """Test single geolocation detection performance target (<100ms)."""
        ip_address = "*******"
        performance_threshold = TEST_CONFIG["PERFORMANCE_THRESHOLDS"]["max_detection_time_ms"]
        
        # Measure detection time
        start_time = time.perf_counter()
        result = await mock_geolocation_service.detect_country_from_ip(ip_address)
        end_time = time.perf_counter()
        
        detection_time_ms = (end_time - start_time) * 1000
        
        # Validate performance
        assert detection_time_ms < performance_threshold, f"Detection time {detection_time_ms:.2f}ms exceeds threshold {performance_threshold}ms"
        assert result.country_code == "US"
        
        print(f"Single detection performance: {detection_time_ms:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_batch_detection_performance(self, mock_geolocation_service, known_country_ips):
        """Test batch geolocation detection performance."""
        ip_addresses = list(known_country_ips.keys())
        batch_threshold_ms = 500  # 500ms for batch of 10 IPs
        
        # Measure batch detection time
        start_time = time.perf_counter()
        
        tasks = [
            mock_geolocation_service.detect_country_from_ip(ip)
            for ip in ip_addresses
        ]
        results = await asyncio.gather(*tasks)
        
        end_time = time.perf_counter()
        total_time_ms = (end_time - start_time) * 1000
        avg_time_per_ip = total_time_ms / len(ip_addresses)
        
        # Validate performance
        assert total_time_ms < batch_threshold_ms, f"Batch detection time {total_time_ms:.2f}ms exceeds threshold {batch_threshold_ms}ms"
        assert avg_time_per_ip < 100, f"Average time per IP {avg_time_per_ip:.2f}ms exceeds 100ms threshold"
        assert len(results) == len(ip_addresses)
        
        print(f"Batch detection performance: {total_time_ms:.2f}ms total, {avg_time_per_ip:.2f}ms per IP")
    
    @pytest.mark.asyncio
    async def test_cache_hit_rate_performance(self, mock_geolocation_service, geolocation_test_helper):
        """Test cache hit rate performance (>90% target)."""
        ip_addresses = ["*******", "*******", "************", "***********", "************"]
        min_cache_hit_rate = TEST_CONFIG["PERFORMANCE_THRESHOLDS"]["min_cache_hit_rate"]
        
        # Test cache performance
        cache_results = await geolocation_test_helper.test_cache_performance(
            mock_geolocation_service, ip_addresses
        )
        
        # Validate cache performance
        cache_hit_rate = cache_results["cache_hit_rate"]
        assert cache_hit_rate >= min_cache_hit_rate, f"Cache hit rate {cache_hit_rate:.2%} below threshold {min_cache_hit_rate:.2%}"
        
        # Validate cache improves performance
        improvement_percent = cache_results["cache_improvement_percent"]
        assert improvement_percent > 0, "Cache should improve performance"
        
        print(f"Cache performance: {cache_hit_rate:.2%} hit rate, {improvement_percent:.1f}% improvement")
    
    @pytest.mark.asyncio
    async def test_concurrent_request_performance(self, mock_geolocation_service, geolocation_test_helper):
        """Test performance under concurrent load."""
        ip_addresses = ["*******", "*******", "************", "***********"]
        concurrent_count = TEST_CONFIG["LOAD_TEST_SETTINGS"]["concurrent_requests"]
        
        # Simulate concurrent requests
        results = await geolocation_test_helper.simulate_concurrent_requests(
            mock_geolocation_service, ip_addresses, concurrent_count
        )
        
        # Validate concurrent performance
        success_rate = results["success_rate"]
        avg_time_ms = results["avg_time_per_request_ms"]
        requests_per_second = results["requests_per_second"]
        
        assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95% threshold"
        assert avg_time_ms < 200, f"Average request time {avg_time_ms:.2f}ms exceeds 200ms threshold"
        assert requests_per_second > 10, f"Throughput {requests_per_second:.1f} RPS below 10 RPS threshold"
        
        print(f"Concurrent performance: {success_rate:.2%} success, {avg_time_ms:.2f}ms avg, {requests_per_second:.1f} RPS")
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_performance(self, test_circuit_breaker, mock_geolocation_service):
        """Test circuit breaker performance under load."""
        failure_threshold = TEST_CONFIG["CIRCUIT_BREAKER_TEST"]["failure_threshold"]
        
        # Configure mock to fail initially
        call_count = 0
        
        async def failing_detect_country(ip_address: str):
            nonlocal call_count
            call_count += 1
            if call_count <= failure_threshold:
                raise Exception("Simulated failure")
            return GeolocationTestHelper.create_test_geolocation_result(ip_address, "US", 0.9)
        
        mock_geolocation_service.detect_country_from_ip.side_effect = failing_detect_country
        
        # Measure circuit breaker response time
        start_time = time.perf_counter()
        
        # Test failures until circuit opens
        for i in range(failure_threshold):
            try:
                await test_circuit_breaker.detect_country_with_protection(
                    mock_geolocation_service, "*******"
                )
            except Exception:
                pass  # Expected failures
        
        # Test circuit breaker rejection (should be fast)
        rejection_start = time.perf_counter()
        try:
            await test_circuit_breaker.detect_country_with_protection(
                mock_geolocation_service, "*******"
            )
        except Exception:
            pass  # Expected rejection
        rejection_time_ms = (time.perf_counter() - rejection_start) * 1000
        
        total_time = time.perf_counter() - start_time
        
        # Validate circuit breaker performance
        assert rejection_time_ms < 10, f"Circuit breaker rejection time {rejection_time_ms:.2f}ms too slow"
        assert total_time < 5, f"Circuit breaker opening took {total_time:.2f}s, should be under 5s"
        
        stats = test_circuit_breaker.get_stats()
        assert stats["state"] == "open"
        
        print(f"Circuit breaker performance: {rejection_time_ms:.2f}ms rejection time")
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism_performance(self, test_resilience_manager, mock_geolocation_service):
        """Test fallback mechanism response time."""
        # Configure mock to always fail
        mock_geolocation_service.detect_country_from_ip.side_effect = Exception("Database unavailable")
        
        # Test different fallback scenarios
        fallback_scenarios = [
            {"name": "default_country", "user_prefs": None},
            {"name": "user_preference", "user_prefs": {"preferred_country": "US"}},
            {"name": "ip_range_mapping", "user_prefs": None, "ip": "***********"},
        ]
        
        for scenario in fallback_scenarios:
            ip_address = scenario.get("ip", "*******")
            user_prefs = scenario.get("user_prefs")
            
            # Measure fallback time
            start_time = time.perf_counter()
            result = await test_resilience_manager.detect_country_with_resilience(
                mock_geolocation_service, ip_address, user_prefs
            )
            fallback_time_ms = (time.perf_counter() - start_time) * 1000
            
            # Validate fallback performance
            assert fallback_time_ms < 50, f"Fallback {scenario['name']} took {fallback_time_ms:.2f}ms, should be under 50ms"
            assert result.country_code is not None
            assert "fallback" in result.detection_method
            
            print(f"Fallback {scenario['name']} performance: {fallback_time_ms:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_memory_usage_performance(self, mock_geolocation_service):
        """Test memory usage during geolocation operations."""
        max_memory_mb = TEST_CONFIG["PERFORMANCE_THRESHOLDS"]["max_memory_usage_mb"]
        
        # Start memory tracking
        tracemalloc.start()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Perform multiple geolocation operations
        ip_addresses = ["*******", "*******", "************"] * 100  # 300 operations
        
        for ip in ip_addresses:
            await mock_geolocation_service.detect_country_from_ip(ip)
        
        # Measure memory usage
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - initial_memory
        
        # Get tracemalloc statistics
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Validate memory usage
        assert memory_increase < max_memory_mb, f"Memory increase {memory_increase:.2f}MB exceeds threshold {max_memory_mb}MB"
        assert peak / 1024 / 1024 < max_memory_mb, f"Peak memory {peak/1024/1024:.2f}MB exceeds threshold {max_memory_mb}MB"
        
        print(f"Memory performance: {memory_increase:.2f}MB increase, {peak/1024/1024:.2f}MB peak")
        
        # Force garbage collection
        gc.collect()
    
    @pytest.mark.asyncio
    async def test_cpu_usage_performance(self, mock_geolocation_service):
        """Test CPU usage during geolocation operations."""
        max_cpu_percent = TEST_CONFIG["PERFORMANCE_THRESHOLDS"]["max_cpu_usage_percent"]
        
        # Monitor CPU usage
        process = psutil.Process()
        cpu_percentages = []
        
        # Perform CPU-intensive geolocation operations
        async def cpu_intensive_task():
            for i in range(100):
                await mock_geolocation_service.detect_country_from_ip(f"8.8.8.{i % 255}")
        
        # Monitor CPU during task execution
        start_time = time.time()
        task = asyncio.create_task(cpu_intensive_task())
        
        while not task.done() and time.time() - start_time < 10:  # Max 10 seconds
            cpu_percent = process.cpu_percent(interval=0.1)
            if cpu_percent > 0:  # Ignore zero readings
                cpu_percentages.append(cpu_percent)
            await asyncio.sleep(0.1)
        
        await task
        
        # Validate CPU usage
        if cpu_percentages:
            avg_cpu = statistics.mean(cpu_percentages)
            max_cpu = max(cpu_percentages)
            
            assert avg_cpu < max_cpu_percent, f"Average CPU {avg_cpu:.1f}% exceeds threshold {max_cpu_percent}%"
            assert max_cpu < max_cpu_percent * 1.5, f"Peak CPU {max_cpu:.1f}% exceeds threshold {max_cpu_percent * 1.5}%"
            
            print(f"CPU performance: {avg_cpu:.1f}% average, {max_cpu:.1f}% peak")
        else:
            print("CPU monitoring: No significant CPU usage detected")
    
    @pytest.mark.asyncio
    async def test_latency_distribution_analysis(self, mock_geolocation_service):
        """Test latency distribution analysis."""
        ip_addresses = ["*******", "*******", "************", "***********", "************"]
        measurements = []
        
        # Collect latency measurements
        for _ in range(100):  # 100 measurements
            for ip in ip_addresses:
                start_time = time.perf_counter()
                await mock_geolocation_service.detect_country_from_ip(ip)
                latency_ms = (time.perf_counter() - start_time) * 1000
                measurements.append(latency_ms)
        
        # Calculate statistics
        avg_latency = statistics.mean(measurements)
        median_latency = statistics.median(measurements)
        p95_latency = statistics.quantiles(measurements, n=20)[18]  # 95th percentile
        p99_latency = statistics.quantiles(measurements, n=100)[98]  # 99th percentile
        std_dev = statistics.stdev(measurements)
        
        # Validate latency distribution
        assert avg_latency < 100, f"Average latency {avg_latency:.2f}ms exceeds 100ms"
        assert p95_latency < 200, f"95th percentile latency {p95_latency:.2f}ms exceeds 200ms"
        assert p99_latency < 500, f"99th percentile latency {p99_latency:.2f}ms exceeds 500ms"
        assert std_dev < 50, f"Latency standard deviation {std_dev:.2f}ms too high"
        
        print(f"Latency distribution: avg={avg_latency:.2f}ms, median={median_latency:.2f}ms, "
              f"p95={p95_latency:.2f}ms, p99={p99_latency:.2f}ms, std={std_dev:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_throughput_scaling(self, mock_geolocation_service, geolocation_test_helper):
        """Test throughput scaling with increasing load."""
        ip_addresses = ["*******", "*******", "************", "***********"]
        load_levels = [10, 25, 50, 100]  # Different concurrent request levels
        
        throughput_results = []
        
        for concurrent_count in load_levels:
            results = await geolocation_test_helper.simulate_concurrent_requests(
                mock_geolocation_service, ip_addresses, concurrent_count
            )
            
            throughput_results.append({
                "concurrent_requests": concurrent_count,
                "requests_per_second": results["requests_per_second"],
                "success_rate": results["success_rate"],
                "avg_time_ms": results["avg_time_per_request_ms"]
            })
        
        # Validate throughput scaling
        for i, result in enumerate(throughput_results):
            concurrent = result["concurrent_requests"]
            rps = result["requests_per_second"]
            success_rate = result["success_rate"]
            
            # Throughput should scale reasonably with load
            expected_min_rps = concurrent * 0.5  # At least 50% efficiency
            assert rps >= expected_min_rps, f"Throughput {rps:.1f} RPS too low for {concurrent} concurrent requests"
            
            # Success rate should remain high
            assert success_rate >= 0.95, f"Success rate {success_rate:.2%} too low at {concurrent} concurrent requests"
            
            print(f"Load {concurrent}: {rps:.1f} RPS, {success_rate:.2%} success, {result['avg_time_ms']:.2f}ms avg")
    
    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, mock_geolocation_service):
        """Test performance under sustained load."""
        duration_seconds = 30  # 30-second sustained test
        target_rps = 20  # Target 20 requests per second
        
        start_time = time.time()
        request_count = 0
        error_count = 0
        latencies = []
        
        # Sustained load test
        while time.time() - start_time < duration_seconds:
            batch_start = time.time()
            
            # Send batch of requests
            tasks = []
            for i in range(target_rps):
                ip = f"8.8.8.{(request_count + i) % 255}"
                task = asyncio.create_task(mock_geolocation_service.detect_country_from_ip(ip))
                tasks.append(task)
            
            # Measure batch latency
            batch_latency_start = time.perf_counter()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            batch_latency = (time.perf_counter() - batch_latency_start) * 1000
            
            latencies.append(batch_latency)
            request_count += len(tasks)
            error_count += sum(1 for r in results if isinstance(r, Exception))
            
            # Wait for next second
            elapsed = time.time() - batch_start
            if elapsed < 1.0:
                await asyncio.sleep(1.0 - elapsed)
        
        # Calculate sustained performance metrics
        actual_duration = time.time() - start_time
        actual_rps = request_count / actual_duration
        error_rate = error_count / request_count
        avg_batch_latency = statistics.mean(latencies)
        
        # Validate sustained performance
        assert actual_rps >= target_rps * 0.8, f"Sustained RPS {actual_rps:.1f} below target {target_rps}"
        assert error_rate < 0.05, f"Error rate {error_rate:.2%} exceeds 5% threshold"
        assert avg_batch_latency < 1000, f"Average batch latency {avg_batch_latency:.2f}ms exceeds 1000ms"
        
        print(f"Sustained load: {actual_rps:.1f} RPS, {error_rate:.2%} errors, {avg_batch_latency:.2f}ms batch latency")
