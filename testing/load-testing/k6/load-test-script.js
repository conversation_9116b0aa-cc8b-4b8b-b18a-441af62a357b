// Culture Connect Backend - k6 Load Testing Script
// Comprehensive load testing for >10,000 concurrent users

import http from 'k6/http';
import ws from 'k6/ws';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomString, randomIntBetween, randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const responseTimeTrend = new Trend('response_time');
const authSuccessRate = new Rate('auth_success_rate');
const bookingSuccessRate = new Rate('booking_success_rate');
const paymentSuccessRate = new Rate('payment_success_rate');
const websocketConnections = new Counter('websocket_connections');

// Test configuration
export const options = {
  stages: [
    // Warm-up
    { duration: '5m', target: 100 },
    // Ramp-up
    { duration: '10m', target: 1000 },
    // Sustained load
    { duration: '30m', target: 5000 },
    // Peak load
    { duration: '15m', target: 10000 },
    // Stress test
    { duration: '10m', target: 15000 },
    // Cool-down
    { duration: '5m', target: 100 },
  ],
  
  thresholds: {
    // Response time thresholds
    'http_req_duration': ['p(95)<500', 'p(99)<1000', 'med<200'],
    
    // Error rate thresholds
    'error_rate': ['rate<0.01'], // < 1% error rate
    'http_req_failed': ['rate<0.02'], // < 2% failed requests
    
    // Success rate thresholds
    'auth_success_rate': ['rate>0.95'], // > 95% auth success
    'booking_success_rate': ['rate>0.90'], // > 90% booking success
    'payment_success_rate': ['rate>0.95'], // > 95% payment success
    
    // Throughput thresholds
    'http_reqs': ['rate>1000'], // > 1000 requests/second
    'vus': ['value>10000'], // > 10,000 virtual users at peak
    
    // WebSocket thresholds
    'websocket_connections': ['count>1000'], // > 1000 WebSocket connections
  },
  
  // Resource limits
  noConnectionReuse: false,
  userAgent: 'k6-load-test/1.0',
  
  // Test data
  setupTimeout: '60s',
  teardownTimeout: '60s',
};

// Test configuration
const BASE_URL = 'https://api.cultureconnect.ng';
const WS_URL = 'wss://api.cultureconnect.ng';
const API_VERSION = 'v1';

// Test data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'LoadTest123!'
};

const LOCATIONS = ['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan'];
const SERVICE_TYPES = ['catering', 'photography', 'decoration', 'music', 'venue'];
const EVENT_TYPES = ['wedding', 'birthday', 'corporate', 'anniversary', 'graduation'];

// Setup function - runs once before the test
export function setup() {
  console.log('Setting up load test environment...');
  
  // Verify API is accessible
  const healthCheck = http.get(`${BASE_URL}/health`);
  check(healthCheck, {
    'API is accessible': (r) => r.status === 200,
    'Health check response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  if (healthCheck.status !== 200) {
    throw new Error('API is not accessible - aborting test');
  }
  
  console.log('Load test setup completed successfully');
  return { baseUrl: BASE_URL };
}

// Main test function
export default function(data) {
  // Distribute load across different scenarios
  const scenario = randomItem(['health_check', 'auth_flow', 'vendor_search', 'booking_flow', 'payment_flow', 'websocket']);
  
  switch (scenario) {
    case 'health_check':
      healthCheckScenario();
      break;
    case 'auth_flow':
      authenticationScenario();
      break;
    case 'vendor_search':
      vendorSearchScenario();
      break;
    case 'booking_flow':
      bookingFlowScenario();
      break;
    case 'payment_flow':
      paymentFlowScenario();
      break;
    case 'websocket':
      websocketScenario();
      break;
  }
  
  // Random think time between requests
  sleep(randomIntBetween(1, 5));
}

// Health check scenario
function healthCheckScenario() {
  group('Health Check', () => {
    const response = http.get(`${BASE_URL}/health`);
    
    const success = check(response, {
      'Health check status is 200': (r) => r.status === 200,
      'Health check response time < 500ms': (r) => r.timings.duration < 500,
      'Health check has status field': (r) => r.json('status') !== undefined,
    });
    
    errorRate.add(!success);
    responseTimeTrend.add(response.timings.duration);
  });
}

// Authentication scenario
function authenticationScenario() {
  group('Authentication Flow', () => {
    // Login
    const loginPayload = {
      email: TEST_USER.email,
      password: TEST_USER.password
    };
    
    const loginResponse = http.post(
      `${BASE_URL}/api/${API_VERSION}/auth/login`,
      JSON.stringify(loginPayload),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    
    const loginSuccess = check(loginResponse, {
      'Login status is 200': (r) => r.status === 200,
      'Login response time < 1s': (r) => r.timings.duration < 1000,
      'Login returns access token': (r) => r.json('access_token') !== undefined,
    });
    
    authSuccessRate.add(loginSuccess);
    errorRate.add(!loginSuccess);
    
    if (loginSuccess) {
      const token = loginResponse.json('access_token');
      
      // Get user profile
      const profileResponse = http.get(
        `${BASE_URL}/api/${API_VERSION}/auth/me`,
        {
          headers: { 'Authorization': `Bearer ${token}` },
        }
      );
      
      check(profileResponse, {
        'Profile status is 200': (r) => r.status === 200,
        'Profile response time < 500ms': (r) => r.timings.duration < 500,
        'Profile returns user data': (r) => r.json('user') !== undefined,
      });
    }
    
    sleep(1);
  });
}

// Vendor search scenario
function vendorSearchScenario() {
  group('Vendor Search', () => {
    const location = randomItem(LOCATIONS);
    const serviceType = randomItem(SERVICE_TYPES);
    
    const searchResponse = http.get(
      `${BASE_URL}/api/${API_VERSION}/vendors/search?location=${location}&service_type=${serviceType}&limit=20`
    );
    
    const searchSuccess = check(searchResponse, {
      'Search status is 200': (r) => r.status === 200,
      'Search response time < 500ms': (r) => r.timings.duration < 500,
      'Search returns vendors': (r) => r.json('vendors') !== undefined,
    });
    
    errorRate.add(!searchSuccess);
    
    if (searchSuccess && searchResponse.json('vendors').length > 0) {
      const vendorId = searchResponse.json('vendors')[0].id;
      
      // Get vendor details
      const vendorResponse = http.get(`${BASE_URL}/api/${API_VERSION}/vendors/${vendorId}`);
      
      check(vendorResponse, {
        'Vendor details status is 200': (r) => r.status === 200,
        'Vendor details response time < 500ms': (r) => r.timings.duration < 500,
        'Vendor details returns vendor': (r) => r.json('vendor') !== undefined,
      });
    }
    
    sleep(2);
  });
}

// Booking flow scenario
function bookingFlowScenario() {
  group('Booking Flow', () => {
    // Authenticate first
    const loginPayload = {
      email: TEST_USER.email,
      password: TEST_USER.password
    };
    
    const loginResponse = http.post(
      `${BASE_URL}/api/${API_VERSION}/auth/login`,
      JSON.stringify(loginPayload),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    
    if (loginResponse.status !== 200) {
      errorRate.add(true);
      return;
    }
    
    const token = loginResponse.json('access_token');
    
    // Search for vendors
    const location = randomItem(LOCATIONS);
    const serviceType = randomItem(SERVICE_TYPES);
    
    const searchResponse = http.get(
      `${BASE_URL}/api/${API_VERSION}/vendors/search?location=${location}&service_type=${serviceType}&limit=5`
    );
    
    if (searchResponse.status !== 200 || searchResponse.json('vendors').length === 0) {
      errorRate.add(true);
      return;
    }
    
    const vendorId = searchResponse.json('vendors')[0].id;
    
    // Create booking
    const bookingPayload = {
      vendor_id: vendorId,
      event_date: '2024-12-25',
      event_type: randomItem(EVENT_TYPES),
      guest_count: randomIntBetween(50, 500),
      budget: randomIntBetween(100000, 2000000),
      location: `${location}, Nigeria`,
      description: `Load test booking ${randomString(8)}`
    };
    
    const bookingResponse = http.post(
      `${BASE_URL}/api/${API_VERSION}/bookings`,
      JSON.stringify(bookingPayload),
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      }
    );
    
    const bookingSuccess = check(bookingResponse, {
      'Booking status is 201': (r) => r.status === 201,
      'Booking response time < 2s': (r) => r.timings.duration < 2000,
      'Booking returns booking data': (r) => r.json('booking') !== undefined,
    });
    
    bookingSuccessRate.add(bookingSuccess);
    errorRate.add(!bookingSuccess);
    
    sleep(3);
  });
}

// Payment flow scenario
function paymentFlowScenario() {
  group('Payment Flow', () => {
    // Authenticate
    const loginPayload = {
      email: TEST_USER.email,
      password: TEST_USER.password
    };
    
    const loginResponse = http.post(
      `${BASE_URL}/api/${API_VERSION}/auth/login`,
      JSON.stringify(loginPayload),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    
    if (loginResponse.status !== 200) {
      errorRate.add(true);
      return;
    }
    
    const token = loginResponse.json('access_token');
    
    // Initialize payment
    const paymentPayload = {
      amount: randomIntBetween(10000, 500000),
      currency: 'NGN',
      payment_method: 'paystack',
      reference: `load_test_${randomString(16)}`
    };
    
    const paymentResponse = http.post(
      `${BASE_URL}/api/${API_VERSION}/payments/initialize`,
      JSON.stringify(paymentPayload),
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      }
    );
    
    const paymentSuccess = check(paymentResponse, {
      'Payment init status is 200': (r) => r.status === 200,
      'Payment init response time < 1s': (r) => r.timings.duration < 1000,
      'Payment init returns URL': (r) => r.json('payment_url') !== undefined,
    });
    
    paymentSuccessRate.add(paymentSuccess);
    errorRate.add(!paymentSuccess);
    
    sleep(2);
  });
}

// WebSocket scenario
function websocketScenario() {
  group('WebSocket Communication', () => {
    const url = `${WS_URL}/api/${API_VERSION}/websocket/rooms/general`;
    
    const response = ws.connect(url, {}, function (socket) {
      websocketConnections.add(1);
      
      socket.on('open', () => {
        console.log('WebSocket connected');
        
        // Join room
        socket.send(JSON.stringify({
          type: 'join_room',
          room: 'general',
          user_id: `load_test_${randomString(8)}`
        }));
      });
      
      socket.on('message', (data) => {
        console.log('WebSocket message received:', data);
      });
      
      socket.on('error', (e) => {
        console.log('WebSocket error:', e);
        errorRate.add(true);
      });
      
      // Send test message
      socket.setTimeout(() => {
        socket.send(JSON.stringify({
          type: 'message',
          room: 'general',
          message: `Load test message ${randomString(8)}`,
          timestamp: new Date().toISOString()
        }));
      }, 1000);
      
      // Keep connection open for 10 seconds
      socket.setTimeout(() => {
        socket.close();
      }, 10000);
    });
    
    check(response, {
      'WebSocket connection successful': (r) => r && r.status === 101,
    });
  });
}

// Teardown function - runs once after the test
export function teardown(data) {
  console.log('Tearing down load test environment...');
  
  // Final health check
  const healthCheck = http.get(`${BASE_URL}/health`);
  check(healthCheck, {
    'API still accessible after test': (r) => r.status === 200,
  });
  
  console.log('Load test teardown completed');
}
