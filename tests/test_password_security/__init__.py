"""
Password Security Test Suite for Culture Connect Backend API.

This package contains comprehensive tests for Task 2.1.2: Password Security and Hashing
implementation including:

- Password strength validation tests
- Password history tracking tests  
- Password reset token management tests
- Account lockout protection tests
- Integration tests across all components
- Service layer functionality tests

Test Coverage Target: >80% for all password security components.

Test Structure:
- test_password_strength_validation.py: 25 test cases for password validation
- test_password_history.py: 20 test cases for password history management
- test_password_reset_tokens.py: 30 test cases for reset token functionality
- test_account_lockout.py: 25 test cases for account lockout protection
- test_password_security_integration.py: 15 integration test cases
- test_password_security_service.py: 20 service layer test cases

Total: 135+ comprehensive test cases covering all acceptance criteria.
"""

__version__ = "1.0.0"
__author__ = "Culture Connect Backend Team"
__description__ = "Password Security Test Suite - Task 2.1.2"
