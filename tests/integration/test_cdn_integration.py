"""
Integration tests for CDN optimization and asset delivery.

This module provides comprehensive integration tests for Phase 7.3.4 CDN components:
- CDN configuration management with database persistence
- Asset optimization with compression and performance tracking
- Asset bundling with multi-file optimization
- CDN metrics collection and analytics validation
- Cross-system integration with Phase 7.2 monitoring and Phase 7.3.2 caching

Implements >80% test coverage with pytest-asyncio patterns and database rollback.
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime, timezone
from decimal import Decimal
from uuid import uuid4
from httpx import AsyncClient
from fastapi import status

from app.main import app
from app.models.cdn_models import (
    CDNConfiguration, AssetOptimization, AssetBundle, CDNMetrics,
    AssetDelivery, CDNProvider, AssetType, OptimizationType, DeliveryStatus
)
from app.schemas.cdn_schemas import (
    CDNConfigurationCreate, AssetOptimizationCreate, AssetBundleCreate,
    CDNMetricsCreate, AssetDeliveryCreate
)
from tests.conftest import TestingSessionLocal, override_get_db


class TestCDNConfigurationIntegration:
    """Integration tests for CDN configuration management."""

    @pytest.mark.asyncio
    async def test_create_cdn_configuration_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN configuration creation via API."""
        # Arrange
        config_data = {
            "name": "test-cloudflare-config",
            "provider": "cloudflare",
            "base_url": "https://cdn.cultureconnect.com",
            "cache_ttl_seconds": 86400,
            "compression_enabled": True,
            "minification_enabled": True,
            "bundling_enabled": True,
            "image_optimization_enabled": True,
            "max_file_size_mb": 10.0,
            "optimization_quality": 85,
            "supported_formats": ["jpg", "png", "css", "js"],
            "is_default": True
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["name"] == "test-cloudflare-config"
        assert response_data["provider"] == "cloudflare"
        assert response_data["base_url"] == "https://cdn.cultureconnect.com"
        assert response_data["is_default"] is True
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_cdn_configurations_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN configurations retrieval."""
        # Arrange - First create a configuration
        config_data = {
            "name": "test-aws-config",
            "provider": "aws_cloudfront",
            "base_url": "https://d123456789.cloudfront.net",
            "cache_ttl_seconds": 3600
        }
        
        await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/cdn/configurations",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)
        if response_data:  # If configurations exist
            config = response_data[0]
            assert "id" in config
            assert "name" in config
            assert "provider" in config

    @pytest.mark.asyncio
    async def test_update_cdn_configuration_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN configuration update."""
        # Arrange - First create a configuration
        config_data = {
            "name": "test-update-config",
            "provider": "azure_cdn",
            "base_url": "https://test.azureedge.net",
            "cache_ttl_seconds": 1800
        }
        
        create_response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        config_id = create_response.json()["id"]
        
        # Update data
        update_data = {
            "cache_ttl_seconds": 7200,
            "compression_enabled": False
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.put(
            f"/api/v1/cdn/configurations/{config_id}",
            json=update_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.5  # <500ms for PUT operations
        
        response_data = response.json()
        assert response_data["cache_ttl_seconds"] == 7200
        assert response_data["compression_enabled"] is False


class TestAssetOptimizationIntegration:
    """Integration tests for asset optimization."""

    @pytest.mark.asyncio
    async def test_optimize_asset_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful asset optimization via API."""
        # Arrange - Create a CDN configuration first
        config_data = {
            "name": "test-optimization-config",
            "provider": "cloudflare",
            "base_url": "https://cdn.test.com",
            "compression_enabled": True
        }
        
        config_response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        config_id = config_response.json()["id"]
        
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.css', delete=False) as f:
            f.write("body { margin: 0; padding: 0; background-color: #ffffff; }")
            test_file_path = f.name
        
        try:
            optimization_data = {
                "asset_path": test_file_path,
                "cdn_configuration_id": config_id,
                "optimization_type": "compression"
            }
            
            # Act
            start_time = asyncio.get_event_loop().time()
            response = await client.post(
                "/api/v1/cdn/assets/optimize",
                json=optimization_data,
                headers=auth_headers
            )
            end_time = asyncio.get_event_loop().time()
            
            # Assert
            assert response.status_code == status.HTTP_201_CREATED
            assert (end_time - start_time) < 2.0  # <2000ms for asset optimization
            
            response_data = response.json()
            assert response_data["original_path"] == test_file_path
            assert response_data["asset_type"] == "stylesheet"
            assert response_data["optimization_type"] == "compression"
            assert response_data["delivery_status"] == "ready"
            assert float(response_data["compression_ratio"]) >= 0
            
        finally:
            # Cleanup
            if os.path.exists(test_file_path):
                os.unlink(test_file_path)

    @pytest.mark.asyncio
    async def test_get_asset_optimizations_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful asset optimizations retrieval."""
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/cdn/assets/optimizations?limit=10",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)


class TestAssetBundleIntegration:
    """Integration tests for asset bundling."""

    @pytest.mark.asyncio
    async def test_create_asset_bundle_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful asset bundle creation via API."""
        # Arrange - Create a CDN configuration first
        config_data = {
            "name": "test-bundle-config",
            "provider": "google_cdn",
            "base_url": "https://cdn.googleapis.com",
            "bundling_enabled": True,
            "minification_enabled": True
        }
        
        config_response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        config_id = config_response.json()["id"]
        
        # Create temporary test files
        test_files = []
        try:
            for i in range(3):
                with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                    f.write(f"function test{i}() {{ console.log('Test {i}'); }}")
                    test_files.append(f.name)
            
            bundle_data = {
                "cdn_configuration_id": config_id,
                "bundle_name": "test-js-bundle",
                "bundle_type": "js",
                "asset_paths": test_files,
                "minification_enabled": True
            }
            
            # Act
            start_time = asyncio.get_event_loop().time()
            response = await client.post(
                "/api/v1/cdn/assets/bundles",
                json=bundle_data,
                headers=auth_headers
            )
            end_time = asyncio.get_event_loop().time()
            
            # Assert
            assert response.status_code == status.HTTP_201_CREATED
            assert (end_time - start_time) < 3.0  # <3000ms for bundle creation
            
            response_data = response.json()
            assert response_data["bundle_name"] == "test-js-bundle"
            assert response_data["bundle_type"] == "js"
            assert len(response_data["asset_paths"]) == 3
            assert response_data["delivery_status"] == "ready"
            assert float(response_data["compression_ratio"]) >= 0
            
        finally:
            # Cleanup
            for file_path in test_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)

    @pytest.mark.asyncio
    async def test_get_asset_bundles_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful asset bundles retrieval."""
        # Act
        response = await client.get(
            "/api/v1/cdn/assets/bundles?bundle_type=js",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert isinstance(response_data, list)


class TestCDNMetricsIntegration:
    """Integration tests for CDN metrics and analytics."""

    @pytest.mark.asyncio
    async def test_record_cdn_metrics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN metrics recording."""
        # Arrange - Create a CDN configuration first
        config_data = {
            "name": "test-metrics-config",
            "provider": "custom",
            "base_url": "https://custom-cdn.test.com"
        }
        
        config_response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        config_id = config_response.json()["id"]
        
        metrics_data = {
            "cdn_configuration_id": config_id,
            "period_start": "2025-01-31T17:00:00Z",
            "period_end": "2025-01-31T18:00:00Z",
            "total_requests": 1000,
            "cache_hits": 850,
            "cache_misses": 150,
            "avg_response_time_ms": 95.5,
            "avg_first_byte_time_ms": 45.2,
            "total_bytes_served": 5000000,
            "total_bytes_saved": 1500000,
            "error_count": 5
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/cdn/metrics",
            json=metrics_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["total_requests"] == 1000
        assert response_data["cache_hits"] == 850
        assert float(response_data["cache_hit_rate"]) == 0.85  # 850/1000
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_cdn_metrics_summary_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN metrics summary retrieval."""
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/cdn/metrics/summary?hours=24",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert "total_requests" in response_data
        assert "cache_hit_rate" in response_data
        assert "avg_response_time_ms" in response_data
        assert "bandwidth_savings_gb" in response_data

    @pytest.mark.asyncio
    async def test_get_cdn_dashboard_data_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful CDN dashboard data retrieval."""
        # Act
        response = await client.get(
            "/api/v1/cdn/analytics/dashboard",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert "total_configurations" in response_data
        assert "active_configurations" in response_data
        assert "total_optimizations" in response_data
        assert "successful_optimizations" in response_data
        assert "total_bundles" in response_data
        assert "avg_compression_ratio" in response_data
        assert "cache_hit_rate" in response_data
        assert "recent_optimizations" in response_data


class TestCDNPerformanceIntegration:
    """Integration tests for CDN performance validation."""

    @pytest.mark.asyncio
    async def test_cdn_performance_targets(self, client: AsyncClient, auth_headers: dict):
        """Test that CDN operations meet performance targets."""
        # Test configuration creation performance
        config_data = {
            "name": "performance-test-config",
            "provider": "cloudflare",
            "base_url": "https://perf-test.cdn.com"
        }
        
        start_time = asyncio.get_event_loop().time()
        config_response = await client.post(
            "/api/v1/cdn/configurations",
            json=config_data,
            headers=auth_headers
        )
        config_creation_time = asyncio.get_event_loop().time() - start_time
        
        assert config_response.status_code == status.HTTP_201_CREATED
        assert config_creation_time < 0.5  # <500ms target
        
        # Test configuration retrieval performance
        start_time = asyncio.get_event_loop().time()
        get_response = await client.get(
            "/api/v1/cdn/configurations",
            headers=auth_headers
        )
        get_time = asyncio.get_event_loop().time() - start_time
        
        assert get_response.status_code == status.HTTP_200_OK
        assert get_time < 0.2  # <200ms target
        
        # Test metrics summary performance
        start_time = asyncio.get_event_loop().time()
        metrics_response = await client.get(
            "/api/v1/cdn/metrics/summary",
            headers=auth_headers
        )
        metrics_time = asyncio.get_event_loop().time() - start_time
        
        assert metrics_response.status_code == status.HTTP_200_OK
        assert metrics_time < 0.2  # <200ms target

    @pytest.mark.asyncio
    async def test_cdn_unauthorized_access(self, client: AsyncClient):
        """Test CDN endpoints without authentication."""
        # Test configuration endpoint without auth
        response = await client.get("/api/v1/cdn/configurations")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Test optimization endpoint without auth
        response = await client.post("/api/v1/cdn/assets/optimize", json={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
