"""
Unit tests for Payout Repository classes.

This module tests the payout repository layer functionality including:
- VendorPayoutRepository: Vendor settlement management with automated calculations
- EscrowAccountRepository: Secure fund holding and dispute management
- Advanced payout processing and scheduling automation
- Escrow release condition checking and automation
- Performance optimization with composite index utilization

Implements comprehensive test coverage for Step 5 Payment & Transaction Management System.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.payout_repository import VendorPayoutRepository, EscrowAccountRepository
from app.models.payout_models import VendorPayout, EscrowAccount, PayoutStatus, EscrowStatus, ReleaseCondition
from app.core.payment.config import PaymentProviderType
from app.repositories.base import PaginationParams, QueryResult, RepositoryError


class TestVendorPayoutRepository:
    """Test VendorPayoutRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def vendor_payout_repository(self, mock_db_session):
        """Create VendorPayoutRepository instance with mock session."""
        return VendorPayoutRepository(mock_db_session)

    @pytest.fixture
    def sample_payout_data(self):
        """Sample vendor payout data for testing."""
        return {
            "vendor_id": 789,
            "period_start": datetime(2024, 1, 1, tzinfo=timezone.utc),
            "period_end": datetime(2024, 1, 31, tzinfo=timezone.utc),
            "total_earnings": Decimal("10000.00"),
            "platform_fee": Decimal("1000.00"),
            "processing_fee": Decimal("200.00"),
            "adjustment_amount": Decimal("0.00"),
            "currency": "NGN",
            "provider": PaymentProviderType.PAYSTACK
        }

    @pytest.mark.asyncio
    async def test_create_vendor_payout_success(self, vendor_payout_repository, sample_payout_data):
        """Test successful vendor payout creation."""
        # Mock the create method
        mock_payout = VendorPayout(
            id=1,
            reference_id="PAYOUT-20240101-789-20240101",
            net_amount=Decimal("8800.00"),  # 10000 - 1000 - 200
            status=PayoutStatus.PENDING,
            **sample_payout_data
        )
        vendor_payout_repository.create = AsyncMock(return_value=mock_payout)

        # Test payout creation
        result = await vendor_payout_repository.create_vendor_payout(**sample_payout_data)

        # Assertions
        assert result.id == 1
        assert result.vendor_id == 789
        assert result.total_earnings == Decimal("10000.00")
        assert result.platform_fee == Decimal("1000.00")
        assert result.processing_fee == Decimal("200.00")
        assert result.net_amount == Decimal("8800.00")
        assert result.currency == "NGN"
        assert result.provider == PaymentProviderType.PAYSTACK
        assert result.status == PayoutStatus.PENDING
        assert "PAYOUT-" in result.reference_id

        # Verify create was called with correct data
        vendor_payout_repository.create.assert_called_once()
        call_args = vendor_payout_repository.create.call_args[0][0]
        assert call_args["vendor_id"] == 789
        assert call_args["net_amount"] == Decimal("8800.00")
        assert call_args["status"] == PayoutStatus.PENDING

    @pytest.mark.asyncio
    async def test_update_payout_status_success(self, vendor_payout_repository):
        """Test successful payout status update."""
        # Mock the update method
        mock_payout = VendorPayout(
            id=1,
            status=PayoutStatus.COMPLETED,
            provider_payout_id="PAYOUT_PROVIDER_123",
            processed_at=datetime.now(timezone.utc)
        )
        vendor_payout_repository.update = AsyncMock(return_value=mock_payout)

        # Test status update
        result = await vendor_payout_repository.update_payout_status(
            payout_id=1,
            status=PayoutStatus.COMPLETED,
            provider_payout_id="PAYOUT_PROVIDER_123"
        )

        # Assertions
        assert result.id == 1
        assert result.status == PayoutStatus.COMPLETED
        assert result.provider_payout_id == "PAYOUT_PROVIDER_123"
        assert result.processed_at is not None

        # Verify update was called with correct data
        call_args = vendor_payout_repository.update.call_args[0][1]
        assert call_args["status"] == PayoutStatus.COMPLETED
        assert call_args["provider_payout_id"] == "PAYOUT_PROVIDER_123"
        assert "processed_at" in call_args

    @pytest.mark.asyncio
    async def test_get_vendor_payouts(self, vendor_payout_repository):
        """Test getting vendor payouts."""
        # Mock database execution
        mock_payouts = [
            VendorPayout(id=1, vendor_id=789, status=PayoutStatus.COMPLETED),
            VendorPayout(id=2, vendor_id=789, status=PayoutStatus.PENDING)
        ]
        
        mock_query_result = QueryResult(
            items=mock_payouts,
            total=2,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        vendor_payout_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting vendor payouts
        result = await vendor_payout_repository.get_vendor_payouts(
            vendor_id=789,
            status=PayoutStatus.COMPLETED
        )

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(payout.vendor_id == 789 for payout in result.items)

        # Verify get_paginated was called
        vendor_payout_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_pending_payouts(self, vendor_payout_repository):
        """Test getting pending payouts."""
        # Mock database execution
        mock_payouts = [
            VendorPayout(id=1, status=PayoutStatus.PENDING, scheduled_at=None),
            VendorPayout(id=2, status=PayoutStatus.PENDING, scheduled_at=datetime.now(timezone.utc) - timedelta(hours=1))
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_payouts
        vendor_payout_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting pending payouts
        result = await vendor_payout_repository.get_pending_payouts(
            scheduled_before=datetime.now(timezone.utc),
            limit=100
        )

        # Assertions
        assert len(result) == 2
        assert all(payout.status == PayoutStatus.PENDING for payout in result)

        # Verify database was queried
        vendor_payout_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_calculate_vendor_earnings(self, vendor_payout_repository):
        """Test calculating vendor earnings."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_revenue = Decimal("15000.00")
        mock_row.total_platform_fees = Decimal("1500.00")
        mock_row.total_provider_fees = Decimal("450.00")
        mock_row.total_net_amount = Decimal("13050.00")
        mock_row.transaction_count = 25
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        vendor_payout_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test calculating vendor earnings
        period_start = datetime(2024, 1, 1, tzinfo=timezone.utc)
        period_end = datetime(2024, 1, 31, tzinfo=timezone.utc)
        
        result = await vendor_payout_repository.calculate_vendor_earnings(
            vendor_id=789,
            period_start=period_start,
            period_end=period_end,
            currency="NGN"
        )

        # Assertions
        assert result["vendor_id"] == 789
        assert result["currency"] == "NGN"
        assert result["total_revenue"] == 15000.00
        assert result["total_platform_fees"] == 1500.00
        assert result["total_provider_fees"] == 450.00
        assert result["total_net_amount"] == 13050.00
        assert result["transaction_count"] == 25
        assert result["vendor_earnings"] == 11550.00  # 13050 - 1500

        # Verify database was queried
        vendor_payout_repository.db.execute.assert_called_once()


class TestEscrowAccountRepository:
    """Test EscrowAccountRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def escrow_repository(self, mock_db_session):
        """Create EscrowAccountRepository instance with mock session."""
        return EscrowAccountRepository(mock_db_session)

    @pytest.fixture
    def sample_escrow_data(self):
        """Sample escrow account data for testing."""
        return {
            "booking_id": 123,
            "payment_id": 456,
            "amount": Decimal("1000.00"),
            "release_condition": ReleaseCondition.SERVICE_COMPLETION,
            "hold_until": datetime.now(timezone.utc) + timedelta(hours=24),
            "currency": "NGN",
            "auto_release_enabled": True
        }

    @pytest.mark.asyncio
    async def test_create_escrow_account_success(self, escrow_repository, sample_escrow_data):
        """Test successful escrow account creation."""
        # Mock the create method
        mock_escrow = EscrowAccount(
            id=1,
            status=EscrowStatus.ACTIVE,
            **sample_escrow_data
        )
        escrow_repository.create = AsyncMock(return_value=mock_escrow)

        # Test escrow creation
        result = await escrow_repository.create_escrow_account(**sample_escrow_data)

        # Assertions
        assert result.id == 1
        assert result.booking_id == 123
        assert result.payment_id == 456
        assert result.amount == Decimal("1000.00")
        assert result.currency == "NGN"
        assert result.status == EscrowStatus.ACTIVE
        assert result.release_condition == ReleaseCondition.SERVICE_COMPLETION
        assert result.auto_release_enabled is True

        # Verify create was called with correct data
        escrow_repository.create.assert_called_once()
        call_args = escrow_repository.create.call_args[0][0]
        assert call_args["booking_id"] == 123
        assert call_args["status"] == EscrowStatus.ACTIVE

    @pytest.mark.asyncio
    async def test_update_escrow_status_released(self, escrow_repository):
        """Test updating escrow status to released."""
        # Mock the update method
        mock_escrow = EscrowAccount(
            id=1,
            status=EscrowStatus.RELEASED,
            released_at=datetime.now(timezone.utc),
            release_reason="Service completed successfully"
        )
        escrow_repository.update = AsyncMock(return_value=mock_escrow)

        # Test status update to released
        result = await escrow_repository.update_escrow_status(
            escrow_id=1,
            status=EscrowStatus.RELEASED,
            release_reason="Service completed successfully"
        )

        # Assertions
        assert result.id == 1
        assert result.status == EscrowStatus.RELEASED
        assert result.release_reason == "Service completed successfully"
        assert result.released_at is not None

        # Verify update was called with correct data
        call_args = escrow_repository.update.call_args[0][1]
        assert call_args["status"] == EscrowStatus.RELEASED
        assert call_args["release_reason"] == "Service completed successfully"
        assert "released_at" in call_args

    @pytest.mark.asyncio
    async def test_update_escrow_status_disputed(self, escrow_repository):
        """Test updating escrow status to disputed."""
        # Mock the update method
        mock_escrow = EscrowAccount(
            id=1,
            status=EscrowStatus.DISPUTED,
            disputed_at=datetime.now(timezone.utc),
            dispute_reason="Service not delivered as promised"
        )
        escrow_repository.update = AsyncMock(return_value=mock_escrow)

        # Test status update to disputed
        result = await escrow_repository.update_escrow_status(
            escrow_id=1,
            status=EscrowStatus.DISPUTED,
            dispute_reason="Service not delivered as promised"
        )

        # Assertions
        assert result.id == 1
        assert result.status == EscrowStatus.DISPUTED
        assert result.dispute_reason == "Service not delivered as promised"
        assert result.disputed_at is not None

        # Verify update was called with correct data
        call_args = escrow_repository.update.call_args[0][1]
        assert call_args["status"] == EscrowStatus.DISPUTED
        assert call_args["dispute_reason"] == "Service not delivered as promised"
        assert "disputed_at" in call_args

    @pytest.mark.asyncio
    async def test_get_escrows_ready_for_release(self, escrow_repository):
        """Test getting escrows ready for release."""
        # Mock database execution
        mock_escrows = [
            EscrowAccount(
                id=1,
                status=EscrowStatus.ACTIVE,
                auto_release_enabled=True,
                hold_until=datetime.now(timezone.utc) - timedelta(hours=1)
            ),
            EscrowAccount(
                id=2,
                status=EscrowStatus.ACTIVE,
                auto_release_enabled=True,
                hold_until=None
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_escrows
        escrow_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting escrows ready for release
        result = await escrow_repository.get_escrows_ready_for_release(
            release_condition=ReleaseCondition.SERVICE_COMPLETION,
            limit=100
        )

        # Assertions
        assert len(result) == 2
        assert all(escrow.status == EscrowStatus.ACTIVE for escrow in result)
        assert all(escrow.auto_release_enabled for escrow in result)

        # Verify database was queried
        escrow_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_disputed_escrows(self, escrow_repository):
        """Test getting disputed escrows."""
        # Mock database execution
        mock_escrows = [
            EscrowAccount(id=1, status=EscrowStatus.DISPUTED),
            EscrowAccount(id=2, status=EscrowStatus.DISPUTED)
        ]
        
        mock_query_result = QueryResult(
            items=mock_escrows,
            total=2,
            page=1,
            per_page=10,
            has_next=False,
            has_prev=False
        )
        
        escrow_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting disputed escrows
        result = await escrow_repository.get_disputed_escrows()

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(escrow.status == EscrowStatus.DISPUTED for escrow in result.items)

        # Verify get_paginated was called
        escrow_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_release_conditions_can_release(self, escrow_repository):
        """Test checking release conditions when escrow can be released."""
        # Mock the get method
        mock_escrow = EscrowAccount(
            id=1,
            status=EscrowStatus.ACTIVE,
            release_condition=ReleaseCondition.SERVICE_COMPLETION,
            auto_release_enabled=True,
            hold_until=datetime.now(timezone.utc) - timedelta(hours=1)
        )
        escrow_repository.get = AsyncMock(return_value=mock_escrow)

        # Test checking release conditions
        result = await escrow_repository.check_release_conditions(escrow_id=1)

        # Assertions
        assert result["escrow_id"] == 1
        assert result["can_release"] is True
        assert result["reason"] == "All conditions met"
        assert result["status"] == "active"
        assert result["release_condition"] == "service_completion"
        assert result["auto_release_enabled"] is True

        # Verify get was called
        escrow_repository.get.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_check_release_conditions_cannot_release(self, escrow_repository):
        """Test checking release conditions when escrow cannot be released."""
        # Mock the get method
        mock_escrow = EscrowAccount(
            id=1,
            status=EscrowStatus.ACTIVE,
            release_condition=ReleaseCondition.TIME_BASED,
            auto_release_enabled=True,
            hold_until=datetime.now(timezone.utc) + timedelta(hours=24)
        )
        escrow_repository.get = AsyncMock(return_value=mock_escrow)

        # Test checking release conditions
        result = await escrow_repository.check_release_conditions(escrow_id=1)

        # Assertions
        assert result["can_release"] is False
        assert "Hold until" in result["reason"]

        # Verify get was called
        escrow_repository.get.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_get_escrow_analytics(self, escrow_repository):
        """Test getting escrow analytics."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_escrows = 100
        mock_row.total_amount = Decimal("500000.00")
        mock_row.active_escrows = 30
        mock_row.released_escrows = 60
        mock_row.disputed_escrows = 10
        mock_row.auto_release_enabled = 80
        mock_row.avg_hold_time_hours = 48.5
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        escrow_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting escrow analytics
        result = await escrow_repository.get_escrow_analytics(currency="NGN")

        # Assertions
        assert result["total_escrows"] == 100
        assert result["total_amount"] == 500000.00
        assert result["active_escrows"] == 30
        assert result["released_escrows"] == 60
        assert result["disputed_escrows"] == 10
        assert result["auto_release_enabled"] == 80
        assert result["avg_hold_time_hours"] == 48.5
        assert result["dispute_rate"] == 10.0  # 10/100 * 100
        assert result["release_rate"] == 60.0  # 60/100 * 100
        assert result["currency"] == "NGN"

        # Verify database was queried
        escrow_repository.db.execute.assert_called_once()
