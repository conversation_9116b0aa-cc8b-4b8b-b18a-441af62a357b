"""
Vendor Profile Service for Culture Connect Backend API.

This module provides comprehensive vendor profile management services including:
- Profile completion tracking and analysis
- Operating hours validation and management
- Media management with file handling
- Business verification status tracking
- Profile optimization recommendations

Implements Task 3.1.3 requirements for vendor profile management with
production-grade business logic, validation, and integration with existing systems.
"""

import logging
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.logging import correlation_id
from app.services.base import NotFoundError, ValidationError, ServiceError
from app.models.vendor import Vendor, VendorProfile, VerificationStatus
from app.repositories.vendor_repository import VendorRepository, VendorProfileRepository
from app.services.base import BaseService
from app.schemas.vendor import (
    VendorProfileManagementRequest,
    VendorProfileManagementResponse,
    ProfileCompletionStatus,
    OperatingHours,
    VendorMediaManagement,
    BusinessVerificationInfo,
    MediaItem
)

logger = logging.getLogger(__name__)


class VendorProfileService(BaseService[VendorProfile, VendorProfileRepository]):
    """
    Vendor profile service for comprehensive profile management operations.

    Provides business logic for:
    - Profile completion tracking and analysis
    - Operating hours configuration and validation
    - Media management with optimization
    - Business verification coordination
    - Profile optimization recommendations
    """

    def __init__(self, db: AsyncSession):
        """Initialize vendor profile service."""
        super().__init__(VendorProfileRepository, VendorProfile, db_session=db)
        self.vendor_repository = VendorRepository(db)

    async def get_vendor_profile_management(
        self,
        vendor_id: int
    ) -> VendorProfileManagementResponse:
        """
        Get comprehensive vendor profile management data.

        Args:
            vendor_id: Vendor ID

        Returns:
            Complete profile management response with completion status

        Raises:
            NotFoundError: If vendor not found
            ServiceError: If operation fails
        """
        correlation = self.log_operation_start("get_vendor_profile_management", vendor_id=vendor_id)

        try:
            # Get vendor with profile
            vendor = await self.vendor_repository.get_with_profile(vendor_id)
            if not vendor:
                raise NotFoundError("Vendor", vendor_id)

            # Calculate completion status
            completion_status = await self._calculate_profile_completion(vendor)

            # Get verification status
            verification_status = vendor.verification_status

            # Check marketplace eligibility
            marketplace_eligibility = await self._check_marketplace_eligibility(vendor)

            # Generate recommendations
            recommendations = await self._generate_profile_recommendations(vendor, completion_status)

            response = VendorProfileManagementResponse(
                profile=vendor.profile,
                completion_status=completion_status,
                verification_status=verification_status,
                marketplace_eligibility=marketplace_eligibility,
                recommendations=recommendations
            )

            self.log_operation_success(correlation, f"Retrieved profile management data for vendor {vendor_id}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "get_vendor_profile_management", {"vendor_id": vendor_id})

    async def update_vendor_profile_comprehensive(
        self,
        vendor_id: int,
        profile_data: VendorProfileManagementRequest
    ) -> VendorProfileManagementResponse:
        """
        Update vendor profile with comprehensive validation and tracking.

        Args:
            vendor_id: Vendor ID
            profile_data: Profile update data

        Returns:
            Updated profile management response

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If profile data is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("update_vendor_profile_comprehensive", vendor_id=vendor_id)

        try:
            # Get existing vendor
            vendor = await self.vendor_repository.get_with_profile(vendor_id)
            if not vendor:
                raise NotFoundError("Vendor", vendor_id)

            # Validate profile data
            await self._validate_profile_data(profile_data)

            # Update profile
            profile_dict = await self._prepare_profile_update_data(profile_data)

            if vendor.profile:
                # Update existing profile
                updated_profile = await self.repository.update(vendor.profile.id, profile_dict)
            else:
                # Create new profile
                profile_dict['vendor_id'] = vendor_id
                updated_profile = await self.repository.create(profile_dict)

            # Refresh vendor to get updated profile (especially important for new profile creation)
            vendor = await self.vendor_repository.get_with_profile(vendor_id)

            # Ensure we have the updated profile reference
            if not updated_profile and vendor.profile:
                updated_profile = vendor.profile

            # Calculate new completion status
            completion_status = await self._calculate_profile_completion(vendor)

            # Update vendor completion percentage
            await self.vendor_repository.update(vendor_id, {
                'completion_percentage': completion_status.overall_percentage
            })

            # Check marketplace eligibility
            marketplace_eligibility = await self._check_marketplace_eligibility(vendor)

            # Generate recommendations
            recommendations = await self._generate_profile_recommendations(vendor, completion_status)

            response = VendorProfileManagementResponse(
                profile=updated_profile,
                completion_status=completion_status,
                verification_status=vendor.verification_status,
                marketplace_eligibility=marketplace_eligibility,
                recommendations=recommendations
            )

            self.log_operation_success(correlation, f"Updated comprehensive profile for vendor {vendor_id}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "update_vendor_profile_comprehensive", {"vendor_id": vendor_id})

    async def update_operating_hours(
        self,
        vendor_id: int,
        operating_hours: OperatingHours
    ) -> Dict[str, Any]:
        """
        Update vendor operating hours with validation.

        Args:
            vendor_id: Vendor ID
            operating_hours: Operating hours configuration

        Returns:
            Updated operating hours data

        Raises:
            NotFoundError: If vendor not found
            ValidationError: If operating hours are invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start("update_operating_hours", vendor_id=vendor_id)

        try:
            # Get vendor profile
            profile = await self.repository.get_by_vendor_id(vendor_id)
            if not profile:
                raise NotFoundError("VendorProfile", vendor_id)

            # Validate operating hours
            await self._validate_operating_hours(operating_hours)

            # Convert to database format
            hours_data = operating_hours.model_dump()

            # Update profile
            updated_profile = await self.repository.update(profile.id, {
                'operating_hours': hours_data,
                'timezone': operating_hours.timezone
            })

            self.log_operation_success(correlation, f"Updated operating hours for vendor {vendor_id}")
            return hours_data

        except Exception as e:
            await self.handle_service_error(e, "update_operating_hours", {"vendor_id": vendor_id})

    async def _calculate_profile_completion(self, vendor: Vendor) -> ProfileCompletionStatus:
        """Calculate profile completion status."""
        if not vendor.profile:
            return ProfileCompletionStatus(
                overall_percentage=0.0,
                basic_info_complete=False,
                contact_info_complete=False,
                address_complete=False,
                operating_hours_complete=False,
                media_complete=False,
                verification_complete=False,
                services_complete=False,
                missing_fields=["All profile fields"],
                recommendations=["Create vendor profile"],
                next_steps=["Complete basic business information"]
            )

        profile = vendor.profile
        completion_factors = []
        missing_fields = []
        recommendations = []
        next_steps = []

        # Basic info completion
        basic_info_complete = bool(
            vendor.business_name and
            vendor.business_type and
            profile.description
        )
        completion_factors.append(basic_info_complete)
        if not basic_info_complete:
            missing_fields.extend(["business_name", "business_type", "description"])
            recommendations.append("Complete basic business information")

        # Contact info completion
        contact_info_complete = bool(
            profile.contact_phone and
            profile.contact_email
        )
        completion_factors.append(contact_info_complete)
        if not contact_info_complete:
            missing_fields.extend(["contact_phone", "contact_email"])
            recommendations.append("Add contact information")

        # Address completion
        address_complete = bool(
            profile.business_address and
            profile.city and
            profile.state and
            profile.country
        )
        completion_factors.append(address_complete)
        if not address_complete:
            missing_fields.extend(["business_address", "city", "state", "country"])
            recommendations.append("Complete business address")

        # Operating hours completion
        operating_hours_complete = bool(profile.operating_hours)
        completion_factors.append(operating_hours_complete)
        if not operating_hours_complete:
            missing_fields.append("operating_hours")
            recommendations.append("Configure operating hours")

        # Media completion
        media_complete = bool(profile.logo_url or profile.cover_image_url)
        completion_factors.append(media_complete)
        if not media_complete:
            missing_fields.extend(["logo_url", "cover_image_url"])
            recommendations.append("Upload business logo and cover image")

        # Verification completion
        verification_complete = vendor.verification_status == VerificationStatus.VERIFIED
        completion_factors.append(verification_complete)
        if not verification_complete:
            missing_fields.append("verification_documents")
            recommendations.append("Complete business verification")

        # Services completion (placeholder - will be implemented in Task 3.2.1)
        services_complete = False  # TODO: Implement when service listing is available
        completion_factors.append(services_complete)

        # Calculate overall percentage
        overall_percentage = (sum(completion_factors) / len(completion_factors)) * 100

        # Generate next steps
        if overall_percentage < 30:
            next_steps.append("Focus on completing basic business information")
        elif overall_percentage < 60:
            next_steps.append("Add contact details and business address")
        elif overall_percentage < 80:
            next_steps.append("Upload media and configure operating hours")
        else:
            next_steps.append("Complete verification and add service listings")

        return ProfileCompletionStatus(
            overall_percentage=overall_percentage,
            basic_info_complete=basic_info_complete,
            contact_info_complete=contact_info_complete,
            address_complete=address_complete,
            operating_hours_complete=operating_hours_complete,
            media_complete=media_complete,
            verification_complete=verification_complete,
            services_complete=services_complete,
            missing_fields=missing_fields,
            recommendations=recommendations,
            next_steps=next_steps
        )

    async def _check_marketplace_eligibility(self, vendor: Vendor) -> bool:
        """Check if vendor is eligible for marketplace."""
        if not vendor.profile:
            return False

        # Basic requirements for marketplace eligibility
        requirements = [
            bool(vendor.business_name),
            bool(vendor.business_type),
            bool(vendor.profile.description),
            bool(vendor.profile.contact_phone),
            bool(vendor.profile.business_address),
            vendor.verification_status == VerificationStatus.VERIFIED,
            vendor.onboarding_completed
        ]

        return all(requirements)

    async def _generate_profile_recommendations(
        self,
        vendor: Vendor,
        completion_status: ProfileCompletionStatus
    ) -> List[str]:
        """Generate profile improvement recommendations."""
        recommendations = []

        if completion_status.overall_percentage < 50:
            recommendations.append("Complete basic profile information to improve visibility")

        if not completion_status.media_complete:
            recommendations.append("Add high-quality images to attract more customers")

        if not completion_status.operating_hours_complete:
            recommendations.append("Set operating hours to help customers know when you're available")

        if not completion_status.verification_complete:
            recommendations.append("Complete verification to build customer trust")

        if vendor.profile and not vendor.profile.specializations:
            recommendations.append("Add specializations to help customers find your services")

        if vendor.profile and not vendor.profile.languages_spoken:
            recommendations.append("List languages spoken to reach more customers")

        return recommendations

    async def _validate_profile_data(self, profile_data: VendorProfileManagementRequest) -> None:
        """Validate profile data."""
        # Validate operating hours if provided
        if profile_data.operating_hours:
            await self._validate_operating_hours(profile_data.operating_hours)

        # Validate coordinates if provided
        if profile_data.latitude is not None and profile_data.longitude is not None:
            if not (-90 <= profile_data.latitude <= 90):
                raise ValidationError("Latitude must be between -90 and 90")
            if not (-180 <= profile_data.longitude <= 180):
                raise ValidationError("Longitude must be between -180 and 180")

    async def _validate_operating_hours(self, operating_hours: OperatingHours) -> None:
        """Validate operating hours configuration."""
        days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

        for day_name in days:
            day_hours = getattr(operating_hours, day_name)
            if day_hours.is_open and not day_hours.is_24_hours:
                if not day_hours.open_time or not day_hours.close_time:
                    raise ValidationError(f"Open and close times required for {day_name}")

                # Validate time format and logic
                try:
                    open_hour, open_min = map(int, day_hours.open_time.split(':'))
                    close_hour, close_min = map(int, day_hours.close_time.split(':'))

                    open_minutes = open_hour * 60 + open_min
                    close_minutes = close_hour * 60 + close_min

                    if open_minutes >= close_minutes:
                        raise ValidationError(f"Close time must be after open time for {day_name}")

                except (ValueError, AttributeError):
                    raise ValidationError(f"Invalid time format for {day_name}")

    async def _prepare_profile_update_data(self, profile_data: VendorProfileManagementRequest) -> Dict[str, Any]:
        """Prepare profile data for database update."""
        update_data = {}

        # Basic fields
        for field in ['description', 'short_description', 'tagline', 'contact_phone',
                     'contact_email', 'website_url', 'business_address', 'city',
                     'state', 'country', 'postal_code', 'latitude', 'longitude',
                     'languages_spoken', 'specializations', 'max_group_size',
                     'min_advance_booking', 'cancellation_policy', 'auto_accept_bookings',
                     'instant_booking_enabled', 'social_media_links', 'additional_info']:
            value = getattr(profile_data, field, None)
            if value is not None:
                update_data[field] = value

        # Operating hours
        if profile_data.operating_hours:
            update_data['operating_hours'] = profile_data.operating_hours.model_dump()
            update_data['timezone'] = profile_data.operating_hours.timezone

        # Media management
        if profile_data.media:
            if profile_data.media.logo:
                update_data['logo_url'] = str(profile_data.media.logo.url)
            if profile_data.media.cover_image:
                update_data['cover_image_url'] = str(profile_data.media.cover_image.url)
            if profile_data.media.gallery:
                update_data['gallery_images'] = [str(item.url) for item in profile_data.media.gallery]

        # Verification info
        if profile_data.verification_info:
            verification_data = profile_data.verification_info.model_dump()
            update_data.update({
                'business_license_number': verification_data.get('business_license_number'),
                'years_in_business': verification_data.get('years_in_business'),
                'certifications': verification_data.get('professional_certifications'),
                'insurance_details': {
                    'provider': verification_data.get('insurance_provider'),
                    'policy_number': verification_data.get('insurance_policy_number'),
                    'coverage_amount': verification_data.get('insurance_coverage_amount'),
                    'expiry_date': verification_data.get('insurance_expiry')
                }
            })

        return update_data

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for creating vendor profile.

        Args:
            data: Profile creation data

        Returns:
            Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Basic validation - can be extended as needed
        if not data:
            raise ValidationError("Profile data cannot be empty")

        return data

    async def validate_update_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data for updating vendor profile.

        Args:
            data: Profile update data

        Returns:
            Validated data

        Raises:
            ValidationError: If validation fails
        """
        # Basic validation - can be extended as needed
        if not data:
            raise ValidationError("Profile update data cannot be empty")

        return data
