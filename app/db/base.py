"""
Base model classes and mixins for Culture Connect Backend API.

This module provides base model classes with common fields and functionality
that can be inherited by all database models.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, Integer, DateTime, String, Boolean, Text, ForeignKey, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import declarative_mixin

from app.db.database import Base

# Import UUID type conditionally based on database type
try:
    from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
    from app.core.config import settings

    # Use String for SQLite, UUID for PostgreSQL
    if settings.DATABASE_URL.startswith("sqlite"):
        UUID = String(36)  # Standard UUID string length
    else:
        UUID = PostgresUUID
except ImportError:
    # Fallback to String if PostgreSQL dialect not available
    UUID = String(36)


@declarative_mixin
class TimestampMixin:
    """
    Mixin for adding timestamp fields to models.

    Provides created_at and updated_at fields with automatic
    timestamp management.
    """

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        doc="Timestamp when the record was created"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True,
        doc="Timestamp when the record was last updated"
    )


@declarative_mixin
class SoftDeleteMixin:
    """
    Mixin for adding soft delete functionality to models.

    Provides deleted_at field and is_deleted flag for soft deletion
    instead of hard deletion from the database.
    """

    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Timestamp when the record was soft deleted"
    )

    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Flag indicating if the record is soft deleted"
    )

    def soft_delete(self):
        """Mark the record as soft deleted."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()

    def restore(self):
        """Restore a soft deleted record."""
        self.is_deleted = False
        self.deleted_at = None


@declarative_mixin
class UUIDMixin:
    """
    Mixin for adding UUID field to models.

    Provides a UUID field for external references and API responses
    while keeping integer primary keys for internal relationships.
    """

    @declared_attr
    def uuid(cls):
        """UUID column that adapts to database type."""
        from app.core.config import settings

        if settings.DATABASE_URL.startswith("sqlite"):
            # For SQLite, store UUID as string
            return Column(
                String(36),
                default=lambda: str(uuid.uuid4()),
                unique=True,
                nullable=False,
                index=True,
                doc="UUID for external references"
            )
        else:
            # For PostgreSQL, use native UUID type
            from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
            return Column(
                PostgresUUID(as_uuid=True),
                default=uuid.uuid4,
                unique=True,
                nullable=False,
                index=True,
                doc="UUID for external references"
            )


@declarative_mixin
class AuditMixin:
    """
    Mixin for adding audit fields to models.

    Provides fields to track who created and last modified records.
    """

    created_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="ID of the user who created this record"
    )

    updated_by = Column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="ID of the user who last updated this record"
    )


@declarative_mixin
class MetadataMixin:
    """
    Mixin for adding metadata fields to models.

    Provides JSON field for storing additional metadata
    and notes field for human-readable comments.
    """

    metadata_json = Column(
        Text,
        nullable=True,
        doc="JSON metadata for additional information"
    )

    notes = Column(
        Text,
        nullable=True,
        doc="Human-readable notes and comments"
    )


class BaseModel(Base, TimestampMixin, UUIDMixin):
    """
    Base model class for all database models.

    Provides common functionality including:
    - Integer primary key
    - UUID for external references
    - Timestamp tracking
    - String representation
    - Dictionary conversion
    """

    __abstract__ = True

    id = Column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        doc="Primary key identifier"
    )

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id}, uuid={self.uuid})>"

    def to_dict(self, exclude: Optional[list] = None) -> Dict[str, Any]:
        """
        Convert model instance to dictionary.

        Args:
            exclude: List of field names to exclude from the dictionary

        Returns:
            Dict: Dictionary representation of the model
        """
        exclude = exclude or []
        result = {}

        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)

                # Handle datetime serialization
                if isinstance(value, datetime):
                    value = value.isoformat()

                # Handle UUID serialization
                elif hasattr(value, 'hex'):
                    value = str(value)

                result[column.name] = value

        return result

    def update_from_dict(self, data: Dict[str, Any], exclude: Optional[list] = None):
        """
        Update model instance from dictionary.

        Args:
            data: Dictionary with field values to update
            exclude: List of field names to exclude from update
        """
        exclude = exclude or ['id', 'uuid', 'created_at']

        for key, value in data.items():
            if key not in exclude and hasattr(self, key):
                setattr(self, key, value)


class BaseModelWithSoftDelete(BaseModel, SoftDeleteMixin):
    """
    Base model class with soft delete functionality.

    Extends BaseModel with soft delete capabilities for models
    that should not be permanently deleted from the database.
    """

    __abstract__ = True


class BaseModelWithAudit(BaseModel, AuditMixin):
    """
    Base model class with audit tracking.

    Extends BaseModel with audit fields to track who created
    and last modified records.
    """

    __abstract__ = True


class BaseModelFull(BaseModel, SoftDeleteMixin, AuditMixin, MetadataMixin):
    """
    Full-featured base model class.

    Extends BaseModel with all available mixins:
    - Soft delete functionality
    - Audit tracking
    - Metadata storage
    """

    __abstract__ = True


# Utility functions for model operations
def get_model_fields(model_class) -> list:
    """
    Get list of field names for a model class.

    Args:
        model_class: SQLAlchemy model class

    Returns:
        list: List of field names
    """
    return [column.name for column in model_class.__table__.columns]


def get_model_relationships(model_class) -> list:
    """
    Get list of relationship names for a model class.

    Args:
        model_class: SQLAlchemy model class

    Returns:
        list: List of relationship names
    """
    from sqlalchemy.orm import class_mapper
    mapper = class_mapper(model_class)
    return [prop.key for prop in mapper.iterate_properties()
            if hasattr(prop, 'direction')]


def serialize_model(instance, include_relationships: bool = False) -> Dict[str, Any]:
    """
    Serialize a model instance to dictionary with optional relationships.

    Args:
        instance: Model instance to serialize
        include_relationships: Whether to include relationship data

    Returns:
        Dict: Serialized model data
    """
    result = instance.to_dict()

    if include_relationships:
        relationships = get_model_relationships(instance.__class__)
        for rel_name in relationships:
            rel_value = getattr(instance, rel_name, None)
            if rel_value is not None:
                if hasattr(rel_value, '__iter__') and not isinstance(rel_value, str):
                    # Collection relationship
                    result[rel_name] = [item.to_dict() for item in rel_value]
                else:
                    # Single relationship
                    result[rel_name] = rel_value.to_dict()

    return result


# Database constraints and indexes
class DatabaseConstraints:
    """Common database constraints and index definitions."""

    # Common index patterns
    CREATED_AT_INDEX = "idx_created_at"
    UPDATED_AT_INDEX = "idx_updated_at"
    UUID_INDEX = "idx_uuid"
    SOFT_DELETE_INDEX = "idx_is_deleted"

    # Common constraint patterns
    POSITIVE_INTEGER = "CHECK (value >= 0)"
    NON_EMPTY_STRING = "CHECK (LENGTH(TRIM(value)) > 0)"
    VALID_EMAIL = "CHECK (value ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$')"
    VALID_PHONE = "CHECK (value ~* '^\\+?[1-9]\\d{1,14}$')"


# Model validation utilities
def validate_model_data(model_class, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate data against model constraints.

    Args:
        model_class: SQLAlchemy model class
        data: Data to validate

    Returns:
        Dict: Validation results with errors if any
    """
    errors = {}
    fields = get_model_fields(model_class)

    # Check for required fields
    for column in model_class.__table__.columns:
        if not column.nullable and column.name not in data:
            if column.default is None and column.server_default is None:
                errors[column.name] = "This field is required"

    # Check for unknown fields
    unknown_fields = set(data.keys()) - set(fields)
    if unknown_fields:
        errors['unknown_fields'] = list(unknown_fields)

    return {
        'is_valid': len(errors) == 0,
        'errors': errors
    }


# Export commonly used items
__all__ = [
    "Base",
    "BaseModel",
    "BaseModelWithSoftDelete",
    "BaseModelWithAudit",
    "BaseModelFull",
    "TimestampMixin",
    "SoftDeleteMixin",
    "UUIDMixin",
    "AuditMixin",
    "MetadataMixin",
    "get_model_fields",
    "get_model_relationships",
    "serialize_model",
    "DatabaseConstraints",
    "validate_model_data",
]
