# Culture Connect Backend - Kubernetes Ingress
# Ingress configuration for external access with SSL termination

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: culture-connect-api
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    # AWS Load Balancer Controller annotations
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: culture-connect-api
    alb.ingress.kubernetes.io/group.name: culture-connect
    
    # SSL/TLS configuration
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:us-west-2:ACCOUNT_ID:certificate/CERT_ID"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS-1-2-2017-01"
    
    # Health check configuration
    alb.ingress.kubernetes.io/healthcheck-path: "/health"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "10"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "3"
    alb.ingress.kubernetes.io/healthcheck-protocol: "HTTP"
    alb.ingress.kubernetes.io/healthcheck-port: "8000"
    
    # Load balancer attributes
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      idle_timeout.timeout_seconds=60,
      routing.http2.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=culture-connect-alb-logs,
      access_logs.s3.prefix=api
    
    # Security and performance
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-west-2:ACCOUNT_ID:regional/webacl/culture-connect-waf/WAF_ID"
    alb.ingress.kubernetes.io/security-groups: "sg-********"
    
    # Rate limiting and protection
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "10"
    
    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://cultureconnect.ng,https://app.cultureconnect.ng,https://admin.cultureconnect.ng"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-expose-headers: "Content-Length,Content-Range"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    
    # Additional security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";
    
    # Performance optimizations
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-buffering: "on"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "4k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "8"
    
    # Compression
    nginx.ingress.kubernetes.io/enable-brotli: "true"
    nginx.ingress.kubernetes.io/brotli-level: "6"
    nginx.ingress.kubernetes.io/brotli-types: "text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json"
    
    # External DNS
    external-dns.alpha.kubernetes.io/hostname: "api.cultureconnect.ng"
    external-dns.alpha.kubernetes.io/ttl: "300"
    
    # Cert Manager
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"

spec:
  ingressClassName: alb
  tls:
  - hosts:
    - api.cultureconnect.ng
    secretName: culture-connect-tls
  rules:
  - host: api.cultureconnect.ng
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
      - path: /health
        pathType: Exact
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
      - path: /openapi.json
        pathType: Exact
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000

---
# Additional ingress for admin subdomain (if needed)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: culture-connect-admin
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: admin-ingress
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: culture-connect
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:us-west-2:ACCOUNT_ID:certificate/CERT_ID"
    
    # Admin-specific security
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: admin-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Culture Connect Admin"
    
    external-dns.alpha.kubernetes.io/hostname: "admin-api.cultureconnect.ng"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

spec:
  ingressClassName: alb
  tls:
  - hosts:
    - admin-api.cultureconnect.ng
    secretName: culture-connect-admin-tls
  rules:
  - host: admin-api.cultureconnect.ng
    http:
      paths:
      - path: /admin
        pathType: Prefix
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: culture-connect-api
            port:
              number: 8000

---
# Network Policy for ingress traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: culture-connect-ingress-policy
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: network-policy
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: aws-load-balancer-controller
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS outbound
    - protocol: TCP
      port: 80    # HTTP outbound
    - protocol: UDP
      port: 53    # DNS
