# Culture Connect Backend - Testing Dependencies
# Python packages required for comprehensive testing suite

# HTTP client libraries
aiohttp==3.9.1
requests==2.31.0
httpx==0.25.2

# WebSocket client
websockets==12.0

# Testing frameworks
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0

# Load testing
locust==2.17.0

# Data generation and manipulation
faker==20.1.0
factory-boy==3.3.0

# JSON and YAML processing
pyyaml==6.0.1
jsonschema==4.20.0

# Security testing
bandit==1.7.5
safety==2.3.5

# Performance monitoring
psutil==5.9.6
memory-profiler==0.61.0

# Database testing
asyncpg==0.29.0
redis==5.0.1

# Utilities
python-dotenv==1.0.0
click==8.1.7
colorama==0.4.6
tabulate==0.9.0

# Reporting
jinja2==3.1.2
matplotlib==3.8.2
seaborn==0.13.0

# Async utilities
asyncio-mqtt==0.16.1
