"""
Tests for Content Moderation services.

This module tests the comprehensive content moderation service layer including:
- ContentQualityService quality assessment algorithms and scoring
- PlagiarismDetectionService content originality verification
- Content analysis algorithms and recommendation generation
- Service integration and error handling

Implements Task 3.2.4 testing requirements with >80% coverage.
"""

import pytest
from datetime import datetime
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.content_moderation_services import (
    ContentQualityService, PlagiarismDetectionService
)
from app.repositories.content_moderation_repositories import (
    ContentQualityScoreRepository, PlagiarismCheckRepository
)
from app.models.content_moderation import (
    ContentQualityScore, PlagiarismCheck, PlagiarismStatus
)
from app.schemas.content_moderation import (
    ContentQualityScoreResponse, PlagiarismCheckResponse
)


@pytest.fixture
def mock_quality_repository():
    """Create mock ContentQualityScoreRepository."""
    return AsyncMock(spec=ContentQualityScoreRepository)


@pytest.fixture
def mock_plagiarism_repository():
    """Create mock PlagiarismCheckRepository."""
    return AsyncMock(spec=PlagiarismCheckRepository)


@pytest.fixture
def quality_service(mock_quality_repository):
    """Create ContentQualityService with mock repository."""
    return ContentQualityService(mock_quality_repository)


@pytest.fixture
def plagiarism_service(mock_plagiarism_repository):
    """Create PlagiarismDetectionService with mock repository."""
    return PlagiarismDetectionService(mock_plagiarism_repository)


class TestContentQualityService:
    """Test ContentQualityService."""

    async def test_calculate_quality_score_comprehensive(self, quality_service, mock_quality_repository):
        """Test comprehensive quality score calculation."""
        workflow_id = uuid4()
        content_data = {
            'text_content': 'This is a comprehensive service description with proper grammar and structure. '
                           'We offer professional photography services for weddings, events, and portraits. '
                           'Our team has over 10 years of experience in capturing beautiful moments. '
                           'Contact us today to discuss your photography needs and get a custom quote.',
            'images': [
                {
                    'width': 1200,
                    'height': 800,
                    'format': 'jpg',
                    'file_size': 450000,
                    'alt_text': 'Professional wedding photography sample'
                }
            ],
            'metadata': {
                'title': 'Professional Photography Services',
                'description': 'High-quality photography for all occasions',
                'category': 'Photography',
                'location': 'Lagos, Nigeria',
                'tags': ['wedding', 'portrait', 'event'],
                'price': '50000-200000'
            },
            'seo_data': {
                'meta_title': 'Professional Photography Services in Lagos',
                'meta_description': 'Expert photographers offering wedding, portrait, and event photography services in Lagos, Nigeria. Book your session today.',
                'keywords': ['photography', 'wedding', 'portrait', 'Lagos'],
                'url_slug': 'professional-photography-services-lagos'
            },
            'mobile_data': {
                'responsive_design': True,
                'touch_friendly_buttons': True,
                'mobile_load_speed': 2.5,
                'mobile_viewport': True
            }
        }
        
        # Mock repository response
        mock_quality_score = MagicMock(spec=ContentQualityScore)
        mock_quality_score.id = uuid4()
        mock_quality_score.workflow_id = workflow_id
        mock_quality_score.overall_score = 87.5
        mock_quality_score.weighted_score = 89.2
        mock_quality_score.quality_grade = "B+"
        mock_quality_score.content_quality_score = 85.0
        mock_quality_score.image_quality_score = 90.0
        mock_quality_score.metadata_completeness_score = 88.0
        mock_quality_score.seo_optimization_score = 92.0
        mock_quality_score.mobile_friendliness_score = 85.0
        mock_quality_score.cultural_appropriateness_score = 95.0
        mock_quality_score.content_analysis = {}
        mock_quality_score.image_analysis = {}
        mock_quality_score.seo_analysis = {}
        mock_quality_score.mobile_analysis = {}
        mock_quality_score.recommendations = []
        mock_quality_score.priority_improvements = []
        mock_quality_score.estimated_improvement_impact = 12.5
        mock_quality_score.scoring_weights = {}
        mock_quality_score.scoring_version = "1.0"
        mock_quality_score.calculated_at = datetime.utcnow()
        mock_quality_score.calculation_duration_ms = 1500
        
        mock_quality_repository.create.return_value = mock_quality_score
        
        # Test quality score calculation
        result = await quality_service.calculate_quality_score(workflow_id, content_data)
        
        assert isinstance(result, ContentQualityScoreResponse)
        assert result.workflow_id == workflow_id
        assert result.overall_score > 0
        assert result.quality_grade is not None
        mock_quality_repository.create.assert_called_once()

    async def test_analyze_content_quality_high_score(self, quality_service):
        """Test content quality analysis with high-quality content."""
        text_content = """
        Professional Photography Services in Lagos, Nigeria
        
        We are a team of experienced photographers specializing in wedding, portrait, and event photography.
        Our services include pre-wedding shoots, traditional ceremonies, white weddings, and corporate events.
        
        With over 10 years of experience, we have captured thousands of beautiful moments for our clients.
        We use state-of-the-art equipment and professional editing techniques to deliver stunning results.
        
        Our packages are affordable and customizable to meet your specific needs and budget.
        Contact us today for a free consultation and quote.
        """
        
        score = await quality_service._analyze_content_quality(text_content)
        
        assert score > 70  # Should be high quality
        assert score <= 100

    async def test_analyze_content_quality_low_score(self, quality_service):
        """Test content quality analysis with low-quality content."""
        text_content = "bad service cheap price call now!!!"
        
        score = await quality_service._analyze_content_quality(text_content)
        
        assert score < 50  # Should be low quality
        assert score >= 0

    async def test_analyze_image_quality_high_resolution(self, quality_service):
        """Test image quality analysis with high-resolution images."""
        images = [
            {
                'width': 1920,
                'height': 1080,
                'format': 'jpg',
                'file_size': 400000,
                'alt_text': 'Professional photography sample'
            },
            {
                'width': 1200,
                'height': 800,
                'format': 'webp',
                'file_size': 300000,
                'alt_text': 'Wedding photography example'
            }
        ]
        
        score = await quality_service._analyze_image_quality(images)
        
        assert score > 80  # Should be high quality
        assert score <= 100

    async def test_analyze_image_quality_no_images(self, quality_service):
        """Test image quality analysis with no images."""
        images = []
        
        score = await quality_service._analyze_image_quality(images)
        
        assert score == 50.0  # Neutral score for no images

    async def test_analyze_metadata_completeness_complete(self, quality_service):
        """Test metadata completeness analysis with complete metadata."""
        metadata = {
            'title': 'Professional Photography Services',
            'description': 'High-quality photography for weddings, events, and portraits with over 10 years of experience',
            'category': 'Photography',
            'location': 'Lagos, Nigeria',
            'tags': ['wedding', 'portrait', 'event'],
            'price': '50000-200000',
            'duration': '2-8 hours',
            'capacity': '1-100 people'
        }
        
        score = await quality_service._analyze_metadata_completeness(metadata)
        
        assert score > 90  # Should be very complete
        assert score <= 100

    async def test_analyze_metadata_completeness_incomplete(self, quality_service):
        """Test metadata completeness analysis with incomplete metadata."""
        metadata = {
            'title': 'Photography'
        }
        
        score = await quality_service._analyze_metadata_completeness(metadata)
        
        assert score < 30  # Should be incomplete
        assert score >= 0

    async def test_analyze_seo_optimization_good(self, quality_service):
        """Test SEO optimization analysis with good SEO data."""
        seo_data = {
            'meta_title': 'Professional Photography Services in Lagos Nigeria',
            'meta_description': 'Expert wedding and event photographers in Lagos offering high-quality photography services. Book your session today for memorable photos.',
            'keywords': ['photography', 'wedding', 'Lagos', 'Nigeria'],
            'url_slug': 'professional-photography-services-lagos-nigeria',
            'schema_markup': True
        }
        
        text_content = 'Professional photography services in Lagos, Nigeria. Wedding photography, event photography.'
        
        score = await quality_service._analyze_seo_optimization(seo_data, text_content)
        
        assert score > 70  # Should be well optimized
        assert score <= 100

    async def test_analyze_mobile_friendliness_optimized(self, quality_service):
        """Test mobile friendliness analysis with optimized settings."""
        mobile_data = {
            'responsive_design': True,
            'touch_friendly_buttons': True,
            'mobile_load_speed': 2.0,
            'mobile_viewport': True
        }
        
        score = await quality_service._analyze_mobile_friendliness(mobile_data)
        
        assert score > 90  # Should be highly mobile-friendly
        assert score <= 100

    async def test_analyze_cultural_appropriateness_appropriate(self, quality_service):
        """Test cultural appropriateness analysis with appropriate content."""
        text_content = 'We celebrate Nigerian culture and traditions through our photography services.'
        images = []
        
        score = await quality_service._analyze_cultural_appropriateness(text_content, images)
        
        assert score >= 95  # Should be culturally appropriate
        assert score <= 100

    async def test_analyze_cultural_appropriateness_inappropriate(self, quality_service):
        """Test cultural appropriateness analysis with inappropriate content."""
        text_content = 'This service contains offensive and inappropriate content that violates community standards.'
        images = []
        
        score = await quality_service._analyze_cultural_appropriateness(text_content, images)
        
        assert score < 90  # Should be penalized for inappropriate content

    async def test_calculate_quality_grade(self, quality_service):
        """Test quality grade calculation."""
        assert quality_service._calculate_quality_grade(97) == "A+"
        assert quality_service._calculate_quality_grade(92) == "A"
        assert quality_service._calculate_quality_grade(87) == "B+"
        assert quality_service._calculate_quality_grade(82) == "B"
        assert quality_service._calculate_quality_grade(77) == "C+"
        assert quality_service._calculate_quality_grade(72) == "C"
        assert quality_service._calculate_quality_grade(65) == "D"
        assert quality_service._calculate_quality_grade(45) == "F"

    async def test_generate_quality_recommendations(self, quality_service):
        """Test quality recommendations generation."""
        recommendations = await quality_service._generate_quality_recommendations(
            content_score=65.0,  # Low
            image_score=85.0,    # Good
            metadata_score=60.0, # Low
            seo_score=75.0,      # Good
            mobile_score=65.0,   # Low
            cultural_score=95.0  # Excellent
        )
        
        assert len(recommendations) > 0
        assert any("content quality" in rec.lower() for rec in recommendations)
        assert any("metadata" in rec.lower() for rec in recommendations)
        assert any("mobile" in rec.lower() for rec in recommendations)

    async def test_estimate_syllables(self, quality_service):
        """Test syllable estimation for readability calculation."""
        # Test simple words
        assert quality_service._estimate_syllables("hello world") >= 3
        assert quality_service._estimate_syllables("photography") >= 4
        assert quality_service._estimate_syllables("a") == 1

    async def test_calculate_keyword_density(self, quality_service):
        """Test keyword density calculation."""
        text = "photography services wedding photography event photography"
        keywords = ["photography", "wedding"]
        
        density = quality_service._calculate_keyword_density(text, keywords)
        
        assert density > 0
        assert density <= 100

    async def test_calculate_readability_score(self, quality_service):
        """Test readability score calculation."""
        text = "This is a simple sentence. This is another simple sentence."
        
        score = quality_service._calculate_readability_score(text)
        
        assert score >= 0
        assert score <= 100


class TestPlagiarismDetectionService:
    """Test PlagiarismDetectionService."""

    async def test_check_plagiarism_new_content(self, plagiarism_service, mock_plagiarism_repository):
        """Test plagiarism check for new content."""
        workflow_id = uuid4()
        content_text = "This is original content about professional photography services in Lagos."
        
        # Mock no existing checks
        mock_plagiarism_repository.get_by_content_hash.return_value = []
        
        # Mock repository response
        mock_plagiarism_check = MagicMock(spec=PlagiarismCheck)
        mock_plagiarism_check.id = uuid4()
        mock_plagiarism_check.workflow_id = workflow_id
        mock_plagiarism_check.status = PlagiarismStatus.ORIGINAL
        mock_plagiarism_check.similarity_percentage = 5.0
        mock_plagiarism_check.originality_score = 95.0
        mock_plagiarism_check.confidence_level = 85.0
        mock_plagiarism_check.similar_sources_found = 0
        mock_plagiarism_check.exact_matches_found = 0
        mock_plagiarism_check.paraphrased_matches_found = 0
        mock_plagiarism_check.similar_sources = []
        mock_plagiarism_check.matching_segments = []
        mock_plagiarism_check.source_analysis = {}
        mock_plagiarism_check.check_parameters = {}
        mock_plagiarism_check.sensitivity_level = "medium"
        mock_plagiarism_check.excluded_sources = []
        mock_plagiarism_check.content_hash = "test_hash"
        mock_plagiarism_check.content_length = len(content_text)
        mock_plagiarism_check.words_checked = len(content_text.split())
        mock_plagiarism_check.processing_time_ms = 1000
        mock_plagiarism_check.checked_at = datetime.utcnow()
        mock_plagiarism_check.check_version = "1.0"
        
        mock_plagiarism_repository.create.return_value = mock_plagiarism_check
        
        # Test plagiarism check
        result = await plagiarism_service.check_plagiarism(workflow_id, content_text)
        
        assert isinstance(result, PlagiarismCheckResponse)
        assert result.workflow_id == workflow_id
        assert result.status == PlagiarismStatus.ORIGINAL
        mock_plagiarism_repository.create.assert_called_once()

    async def test_check_plagiarism_existing_content(self, plagiarism_service, mock_plagiarism_repository):
        """Test plagiarism check for existing content."""
        workflow_id = uuid4()
        content_text = "This content has been checked before."
        
        # Mock existing check
        existing_check = MagicMock(spec=PlagiarismCheck)
        existing_check.id = uuid4()
        existing_check.workflow_id = uuid4()
        existing_check.status = PlagiarismStatus.ORIGINAL
        existing_check.similarity_percentage = 8.0
        existing_check.originality_score = 92.0
        existing_check.confidence_level = 90.0
        existing_check.similar_sources_found = 1
        existing_check.exact_matches_found = 0
        existing_check.paraphrased_matches_found = 1
        existing_check.similar_sources = []
        existing_check.matching_segments = []
        existing_check.source_analysis = {}
        existing_check.check_parameters = {}
        existing_check.sensitivity_level = "medium"
        existing_check.excluded_sources = []
        existing_check.content_hash = "existing_hash"
        existing_check.content_length = len(content_text)
        existing_check.words_checked = len(content_text.split())
        existing_check.processing_time_ms = 800
        existing_check.checked_at = datetime.utcnow()
        existing_check.check_version = "1.0"
        
        mock_plagiarism_repository.get_by_content_hash.return_value = [existing_check]
        
        # Test plagiarism check
        result = await plagiarism_service.check_plagiarism(workflow_id, content_text)
        
        assert isinstance(result, PlagiarismCheckResponse)
        assert result.id == existing_check.id
        # Should not create new check
        mock_plagiarism_repository.create.assert_not_called()

    async def test_calculate_similarity_score_low(self, plagiarism_service):
        """Test similarity score calculation with low similarity content."""
        content_text = "This is completely original content about unique photography services."
        
        score = await plagiarism_service._calculate_similarity_score(content_text, "medium")
        
        assert score >= 0
        assert score <= 100

    async def test_calculate_similarity_score_high_phrases(self, plagiarism_service):
        """Test similarity score calculation with common phrases."""
        content_text = "Welcome to our services. We offer professional services. Contact us to learn more about us."
        
        score = await plagiarism_service._calculate_similarity_score(content_text, "high")
        
        assert score > 0  # Should detect common phrases

    async def test_calculate_confidence_level_short_content(self, plagiarism_service):
        """Test confidence level calculation with short content."""
        content_text = "Short content"
        similarity_percentage = 20.0
        
        confidence = await plagiarism_service._calculate_confidence_level(content_text, similarity_percentage)
        
        assert confidence >= 0
        assert confidence <= 100
        assert confidence < 80  # Lower confidence for short content

    async def test_calculate_confidence_level_long_content(self, plagiarism_service):
        """Test confidence level calculation with long content."""
        content_text = " ".join(["This is a long content piece with many words."] * 20)
        similarity_percentage = 15.0
        
        confidence = await plagiarism_service._calculate_confidence_level(content_text, similarity_percentage)
        
        assert confidence >= 0
        assert confidence <= 100
        assert confidence > 80  # Higher confidence for long content

    async def test_generate_similar_sources_no_similarity(self, plagiarism_service):
        """Test similar sources generation with no similarity."""
        content_text = "Original content"
        similarity_percentage = 5.0
        
        sources = await plagiarism_service._generate_similar_sources(content_text, similarity_percentage)
        
        assert len(sources) == 0

    async def test_generate_similar_sources_high_similarity(self, plagiarism_service):
        """Test similar sources generation with high similarity."""
        content_text = "Common content"
        similarity_percentage = 75.0
        
        sources = await plagiarism_service._generate_similar_sources(content_text, similarity_percentage)
        
        assert len(sources) > 0
        assert all('url' in source for source in sources)
        assert all('similarity_score' in source for source in sources)

    async def test_determine_plagiarism_status(self, plagiarism_service):
        """Test plagiarism status determination."""
        assert plagiarism_service._determine_plagiarism_status(85.0) == PlagiarismStatus.PLAGIARIZED
        assert plagiarism_service._determine_plagiarism_status(65.0) == PlagiarismStatus.SIMILAR_FOUND
        assert plagiarism_service._determine_plagiarism_status(25.0) == PlagiarismStatus.SIMILAR_FOUND
        assert plagiarism_service._determine_plagiarism_status(5.0) == PlagiarismStatus.ORIGINAL
        assert plagiarism_service._determine_plagiarism_status(0.0) == PlagiarismStatus.ORIGINAL

    async def test_calculate_risk_level(self, plagiarism_service):
        """Test plagiarism risk level calculation."""
        assert plagiarism_service._calculate_risk_level(85.0) == "critical"
        assert plagiarism_service._calculate_risk_level(65.0) == "high"
        assert plagiarism_service._calculate_risk_level(45.0) == "medium"
        assert plagiarism_service._calculate_risk_level(25.0) == "low"
        assert plagiarism_service._calculate_risk_level(5.0) == "minimal"

    async def test_generate_plagiarism_recommendations_high_similarity(self, plagiarism_service):
        """Test plagiarism recommendations for high similarity."""
        mock_check = MagicMock()
        mock_check.similarity_percentage = 85.0
        mock_check.exact_matches_found = 3
        mock_check.paraphrased_matches_found = 2
        
        recommendations = plagiarism_service._generate_plagiarism_recommendations(mock_check)
        
        assert len(recommendations) > 0
        assert any("rewrite" in rec.lower() for rec in recommendations)
        assert any("exact matches" in rec.lower() for rec in recommendations)

    async def test_generate_plagiarism_recommendations_original(self, plagiarism_service):
        """Test plagiarism recommendations for original content."""
        mock_check = MagicMock()
        mock_check.similarity_percentage = 5.0
        mock_check.exact_matches_found = 0
        mock_check.paraphrased_matches_found = 0
        
        recommendations = plagiarism_service._generate_plagiarism_recommendations(mock_check)
        
        assert len(recommendations) > 0
        assert any("original" in rec.lower() for rec in recommendations)
