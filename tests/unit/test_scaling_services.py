"""
Unit tests for scaling services.

This module provides comprehensive unit tests for Phase 7.3.3 scaling services:
- ScalingMetricsService unit tests with mocking and validation
- AutoScalingService unit tests with policy evaluation logic
- LoadBalancerService unit tests with business-aware routing
- ContainerOrchestrationService unit tests with Kubernetes integration

Implements >80% test coverage with pytest-asyncio patterns, comprehensive
error handling validation, and performance target verification.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4

from app.services.scaling_services import (
    ScalingMetricsService, AutoScalingService, LoadBalancerService,
    ContainerOrchestrationService
)
from app.models.scaling_models import (
    ScalingMetrics, AutoScalingPolicy, LoadBalancerConfig,
    ContainerMetrics, ScalingEvent, ScalingTriggerType,
    ScalingDirection, LoadBalancerStrategy, ContainerStatus,
    ScalingEventType
)
from app.schemas.scaling_schemas import (
    ScalingMetricsCreate, AutoScalingPolicyCreate, LoadBalancerConfigCreate,
    ContainerMetricsCreate
)
from app.services.base import ValidationError


class TestScalingMetricsService:
    """Test cases for ScalingMetricsService."""

    @pytest.fixture
    def mock_repo(self):
        """Mock scaling metrics repository."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_repo):
        """Create ScalingMetricsService instance with mocked dependencies."""
        return ScalingMetricsService(mock_repo)

    @pytest.mark.asyncio
    async def test_record_metric_success(self, service, mock_repo):
        """Test successful metric recording."""
        # Arrange
        metric_data = ScalingMetricsCreate(
            metric_name="cpu_utilization",
            metric_type="resource",
            component="booking-service",
            current_value=Decimal("75.5"),
            cpu_utilization=Decimal("75.5"),
            memory_utilization=Decimal("60.2")
        )

        expected_metric = ScalingMetrics(
            id=uuid4(),
            metric_name="cpu_utilization",
            metric_type="resource",
            component="booking-service",
            current_value=Decimal("75.5"),
            cpu_utilization=Decimal("75.5"),
            memory_utilization=Decimal("60.2"),
            recorded_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc)
        )

        mock_repo.create_metric.return_value = expected_metric

        # Act
        result = await service.record_metric(metric_data, "test-correlation-id")

        # Assert
        assert result == expected_metric
        mock_repo.create_metric.assert_called_once()
        call_args = mock_repo.create_metric.call_args[1]
        assert call_args["metric_name"] == "cpu_utilization"
        assert call_args["component"] == "booking-service"
        assert call_args["current_value"] == Decimal("75.5")

    @pytest.mark.asyncio
    async def test_record_metric_validation_error(self, service):
        """Test metric recording with validation error."""
        # Arrange
        metric_data = ScalingMetricsCreate(
            metric_name="",  # Invalid empty name
            metric_type="resource",
            component="booking-service",
            current_value=Decimal("75.5")
        )

        # Act & Assert
        with pytest.raises(ValueError, match="Metric name is required"):
            await service.record_metric(metric_data, "test-correlation-id")

    @pytest.mark.asyncio
    async def test_get_component_metrics_success(self, service, mock_repo):
        """Test successful component metrics retrieval."""
        # Arrange
        component = "booking-service"
        expected_metrics = [
            ScalingMetrics(
                id=uuid4(),
                metric_name="cpu_utilization",
                metric_type="resource",
                component=component,
                current_value=Decimal("75.5"),
                recorded_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc)
            )
        ]

        mock_repo.get_latest_metrics_by_component.return_value = expected_metrics

        # Act
        result = await service.get_component_metrics(
            component=component,
            metric_types=["resource"],
            limit=100,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert result == expected_metrics
        mock_repo.get_latest_metrics_by_component.assert_called_once_with(
            component=component,
            metric_types=["resource"],
            limit=100
        )

    @pytest.mark.asyncio
    async def test_analyze_scaling_need_scale_up(self, service, mock_repo):
        """Test scaling need analysis recommending scale up."""
        # Arrange
        component = "booking-service"
        high_cpu_metrics = [
            ScalingMetrics(
                id=uuid4(),
                metric_name="cpu_utilization",
                metric_type="resource",
                component=component,
                current_value=Decimal("85.0"),
                cpu_utilization=Decimal("85.0"),
                memory_utilization=Decimal("70.0"),
                response_time_ms=500,
                error_rate=Decimal("0.02"),
                recorded_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc)
            )
        ]

        mock_repo.get_latest_metrics_by_component.return_value = high_cpu_metrics

        # Act
        result = await service.analyze_scaling_need(component, "test-correlation-id")

        # Assert
        assert result["component"] == component
        assert result["scaling_needed"] is True
        assert result["scaling_direction"] == ScalingDirection.SCALE_UP
        assert result["recommendation"] == "scale_up"
        assert "High CPU utilization" in result["reason"]
        assert result["confidence"] > 0

    @pytest.mark.asyncio
    async def test_analyze_scaling_need_scale_down(self, service, mock_repo):
        """Test scaling need analysis recommending scale down."""
        # Arrange
        component = "booking-service"
        low_utilization_metrics = [
            ScalingMetrics(
                id=uuid4(),
                metric_name="cpu_utilization",
                metric_type="resource",
                component=component,
                current_value=Decimal("15.0"),
                cpu_utilization=Decimal("15.0"),
                memory_utilization=Decimal("25.0"),
                response_time_ms=80,
                error_rate=Decimal("0.001"),
                recorded_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc)
            )
        ]

        mock_repo.get_latest_metrics_by_component.return_value = low_utilization_metrics

        # Act
        result = await service.analyze_scaling_need(component, "test-correlation-id")

        # Assert
        assert result["component"] == component
        assert result["scaling_needed"] is True
        assert result["scaling_direction"] == ScalingDirection.SCALE_DOWN
        assert result["recommendation"] == "scale_down"
        assert "Low resource utilization" in result["reason"]

    @pytest.mark.asyncio
    async def test_get_utilization_summary_success(self, service, mock_repo):
        """Test successful utilization summary retrieval."""
        # Arrange
        components = ["booking-service", "payment-service"]
        expected_summary = {
            "booking-service": {
                "cpu_utilization": 75.5,
                "memory_utilization": 60.2,
                "request_rate": 150,
                "response_time_ms": 200,
                "error_rate": 0.02
            },
            "payment-service": {
                "cpu_utilization": 45.0,
                "memory_utilization": 40.0,
                "request_rate": 80,
                "response_time_ms": 150,
                "error_rate": 0.01
            }
        }

        mock_repo.get_current_utilization_summary.return_value = expected_summary

        # Act
        result = await service.get_utilization_summary(
            components=components,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert result == expected_summary
        mock_repo.get_current_utilization_summary.assert_called_once_with(
            components=components
        )

    @pytest.mark.asyncio
    async def test_cleanup_old_metrics_success(self, service, mock_repo):
        """Test successful old metrics cleanup."""
        # Arrange
        retention_days = 30
        expected_deleted_count = 1500

        mock_repo.cleanup_old_metrics.return_value = expected_deleted_count

        # Act
        result = await service.cleanup_old_metrics(
            retention_days=retention_days,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert result == expected_deleted_count
        mock_repo.cleanup_old_metrics.assert_called_once_with(
            retention_days=retention_days
        )


class TestAutoScalingService:
    """Test cases for AutoScalingService."""

    @pytest.fixture
    def mock_auto_scaling_repo(self):
        """Mock auto-scaling repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_scaling_metrics_service(self):
        """Mock scaling metrics service."""
        return AsyncMock()

    @pytest.fixture
    def mock_scaling_event_repo(self):
        """Mock scaling event repository."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_auto_scaling_repo, mock_scaling_metrics_service, mock_scaling_event_repo):
        """Create AutoScalingService instance with mocked dependencies."""
        return AutoScalingService(
            mock_auto_scaling_repo,
            mock_scaling_metrics_service,
            mock_scaling_event_repo
        )

    @pytest.mark.asyncio
    async def test_create_scaling_policy_success(self, service, mock_auto_scaling_repo, mock_scaling_event_repo):
        """Test successful scaling policy creation."""
        # Arrange
        policy_data = AutoScalingPolicyCreate(
            name="booking-service-policy",
            component="booking-service",
            min_replicas=2,
            max_replicas=10,
            scale_up_threshold=Decimal("80.0"),
            scale_down_threshold=Decimal("30.0"),
            scale_up_cooldown_seconds=300,
            scale_down_cooldown_seconds=600
        )

        expected_policy = AutoScalingPolicy(
            id=uuid4(),
            name="booking-service-policy",
            component="booking-service",
            min_replicas=2,
            max_replicas=10,
            scale_up_threshold=Decimal("80.0"),
            scale_down_threshold=Decimal("30.0"),
            scale_up_cooldown_seconds=300,
            scale_down_cooldown_seconds=600,
            is_enabled=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_auto_scaling_repo.create.return_value = expected_policy
        mock_scaling_event_repo.create_scaling_event.return_value = AsyncMock()

        # Act
        result = await service.create_scaling_policy(policy_data, "test-correlation-id")

        # Assert
        assert result == expected_policy
        mock_auto_scaling_repo.create.assert_called_once()
        mock_scaling_event_repo.create_scaling_event.assert_called_once()

    @pytest.mark.asyncio
    async def test_evaluate_scaling_policies_scale_up_decision(self, service, mock_auto_scaling_repo, mock_scaling_metrics_service):
        """Test scaling policy evaluation with scale up decision."""
        # Arrange
        policy = AutoScalingPolicy(
            id=uuid4(),
            name="booking-service-policy",
            component="booking-service",
            min_replicas=2,
            max_replicas=10,
            target_cpu_utilization=70,
            scale_up_threshold=Decimal("80.0"),
            scale_down_threshold=Decimal("30.0"),
            scale_up_cooldown_seconds=300,
            scale_down_cooldown_seconds=600,
            is_enabled=True,
            last_scaling_event=None,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        utilization_data = {
            "booking-service": {
                "cpu_utilization": 85.0,  # Above scale up threshold
                "memory_utilization": 60.0,
                "request_rate": 200,
                "response_time_ms": 300,
                "error_rate": 0.02
            }
        }

        mock_auto_scaling_repo.get_active_policies.return_value = [policy]
        mock_scaling_metrics_service.get_utilization_summary.return_value = utilization_data

        # Act
        decisions = await service.evaluate_scaling_policies("test-correlation-id")

        # Assert
        assert len(decisions) == 1
        decision = decisions[0]
        assert decision["component"] == "booking-service"
        assert decision["action"] == "scale_up"
        assert decision["trigger"] == "cpu"
        assert decision["trigger_value"] == 85.0
        assert decision["threshold"] == 80.0


class TestLoadBalancerService:
    """Test cases for LoadBalancerService."""

    @pytest.fixture
    def mock_load_balancer_repo(self):
        """Mock load balancer repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_scaling_metrics_service(self):
        """Mock scaling metrics service."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_load_balancer_repo, mock_scaling_metrics_service):
        """Create LoadBalancerService instance with mocked dependencies."""
        return LoadBalancerService(mock_load_balancer_repo, mock_scaling_metrics_service)

    @pytest.mark.asyncio
    async def test_create_load_balancer_config_success(self, service, mock_load_balancer_repo):
        """Test successful load balancer configuration creation."""
        # Arrange
        config_data = LoadBalancerConfigCreate(
            name="booking-service-lb",
            service_name="booking-service",
            strategy=LoadBalancerStrategy.ROUND_ROBIN,
            health_check_path="/health",
            health_check_interval_seconds=30,
            health_check_timeout_seconds=5,
            health_check_retries=3
        )

        expected_config = LoadBalancerConfig(
            id=uuid4(),
            name="booking-service-lb",
            service_name="booking-service",
            strategy=LoadBalancerStrategy.ROUND_ROBIN,
            health_check_path="/health",
            health_check_interval_seconds=30,
            health_check_timeout_seconds=5,
            health_check_retries=3,
            is_enabled=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_load_balancer_repo.create.return_value = expected_config

        # Act
        result = await service.create_load_balancer_config(config_data, "test-correlation-id")

        # Assert
        assert result == expected_config
        mock_load_balancer_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_optimal_routing_strategy_high_load(self, service, mock_scaling_metrics_service):
        """Test optimal routing strategy for high load scenario."""
        # Arrange
        service_name = "booking-service"
        utilization_data = {
            "booking-service": {
                "cpu_utilization": 85.0,  # High CPU
                "memory_utilization": 80.0,  # High memory
                "request_rate": 500,
                "response_time_ms": 300,
                "error_rate": 0.03
            }
        }

        mock_scaling_metrics_service.get_utilization_summary.return_value = utilization_data

        # Act
        strategy = await service.get_optimal_routing_strategy(service_name, "test-correlation-id")

        # Assert
        assert strategy["service_name"] == service_name
        assert strategy["recommended_strategy"] == LoadBalancerStrategy.LEAST_CONNECTIONS
        assert strategy["health_check_frequency"] == "high"
        assert "High resource utilization detected" in strategy["reasoning"]

    @pytest.mark.asyncio
    async def test_get_optimal_routing_strategy_booking_service(self, service, mock_scaling_metrics_service):
        """Test optimal routing strategy for booking service with session affinity."""
        # Arrange
        service_name = "booking-service"
        utilization_data = {
            "booking-service": {
                "cpu_utilization": 45.0,
                "memory_utilization": 40.0,
                "request_rate": 150,
                "response_time_ms": 200,
                "error_rate": 0.01
            }
        }

        mock_scaling_metrics_service.get_utilization_summary.return_value = utilization_data

        # Act
        strategy = await service.get_optimal_routing_strategy(service_name, "test-correlation-id")

        # Assert
        assert strategy["service_name"] == service_name
        assert strategy["session_affinity"] is True
        assert strategy["sticky_session_timeout"] == 1800  # 30 minutes
        assert "Booking service requires session affinity" in strategy["reasoning"]

    @pytest.mark.asyncio
    async def test_manage_session_affinity_enable(self, service, mock_load_balancer_repo):
        """Test enabling session affinity for a service."""
        # Arrange
        service_name = "booking-service"
        config_id = uuid4()
        existing_config = LoadBalancerConfig(
            id=config_id,
            name="booking-service-lb",
            service_name=service_name,
            session_affinity=False,
            is_enabled=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        updated_config = LoadBalancerConfig(
            id=config_id,
            name="booking-service-lb",
            service_name=service_name,
            session_affinity=True,
            sticky_session_timeout=3600,
            is_enabled=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        affinity_config = {"timeout": 3600}

        mock_load_balancer_repo.get_config_by_service.return_value = existing_config
        mock_load_balancer_repo.update.return_value = updated_config

        # Act
        result = await service.manage_session_affinity(
            service_name=service_name,
            enable_affinity=True,
            affinity_config=affinity_config,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert result["service_name"] == service_name
        assert result["session_affinity_enabled"] is True
        mock_load_balancer_repo.update.assert_called_once()


class TestContainerOrchestrationService:
    """Test cases for ContainerOrchestrationService."""

    @pytest.fixture
    def mock_container_metrics_repo(self):
        """Mock container metrics repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_auto_scaling_service(self):
        """Mock auto-scaling service."""
        return AsyncMock()

    @pytest.fixture
    def mock_scaling_event_repo(self):
        """Mock scaling event repository."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_container_metrics_repo, mock_auto_scaling_service, mock_scaling_event_repo):
        """Create ContainerOrchestrationService instance with mocked dependencies."""
        return ContainerOrchestrationService(
            mock_container_metrics_repo,
            mock_auto_scaling_service,
            mock_scaling_event_repo
        )

    @pytest.mark.asyncio
    async def test_record_container_metrics_success(self, service, mock_container_metrics_repo):
        """Test successful container metrics recording."""
        # Arrange
        metrics_data = ContainerMetricsCreate(
            container_id="container-123",
            pod_name="booking-service-pod-1",
            namespace="culture-connect",
            cpu_usage_cores=Decimal("0.5"),
            memory_usage_bytes=512000000,
            request_count=150,
            error_count=2,
            status=ContainerStatus.RUNNING
        )

        expected_metrics = ContainerMetrics(
            id=uuid4(),
            container_id="container-123",
            pod_name="booking-service-pod-1",
            namespace="culture-connect",
            cpu_usage_cores=Decimal("0.5"),
            memory_usage_bytes=512000000,
            request_count=150,
            error_count=2,
            status=ContainerStatus.RUNNING,
            recorded_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc)
        )

        mock_container_metrics_repo.create_container_metric.return_value = expected_metrics

        # Act
        result = await service.record_container_metrics(metrics_data, "test-correlation-id")

        # Assert
        assert result == expected_metrics
        mock_container_metrics_repo.create_container_metric.assert_called_once()

    @pytest.mark.asyncio
    async def test_coordinate_cross_service_scaling_success(self, service, mock_auto_scaling_service):
        """Test successful cross-service scaling coordination."""
        # Arrange
        primary_service = "booking-service"
        dependent_services = ["payment-service", "notification-service"]

        primary_decision = {
            "component": "booking-service",
            "action": "scale_up",
            "target_replicas": 5,
            "current_replicas": 3
        }

        mock_auto_scaling_service.evaluate_scaling_policies.return_value = [primary_decision]

        # Mock the manage_container_lifecycle method
        service.manage_container_lifecycle = AsyncMock()
        service.manage_container_lifecycle.return_value = {"success": True, "actual_replicas": 4}

        # Act
        results = await service.coordinate_cross_service_scaling(
            primary_service=primary_service,
            dependent_services=dependent_services,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert len(results) == 2

        # Check payment service coordination (ratio: 0.8)
        payment_result = next(r for r in results if r["service"] == "payment-service")
        assert payment_result["action"] == "coordinated_scaling"
        assert payment_result["target_replicas"] == 4  # 5 * 0.8 = 4
        assert payment_result["success"] is True

        # Check notification service coordination (ratio: 0.6)
        notification_result = next(r for r in results if r["service"] == "notification-service")
        assert notification_result["action"] == "coordinated_scaling"
        assert notification_result["target_replicas"] == 3  # 5 * 0.6 = 3
        assert notification_result["success"] is True

    @pytest.mark.asyncio
    async def test_generate_kubernetes_manifests_success(self, service):
        """Test successful Kubernetes manifest generation."""
        # Arrange
        service_config = {
            "name": "booking-service",
            "namespace": "culture-connect",
            "image": "booking-service:v1.0.0",
            "port": 8000,
            "replicas": 3,
            "min_replicas": 2,
            "max_replicas": 10,
            "cpu_target": 70,
            "memory_target": 80,
            "expose_externally": True,
            "domain": "api.cultureconnect.com"
        }

        # Act
        manifests = await service.generate_kubernetes_manifests(
            service_config=service_config,
            correlation_id="test-correlation-id"
        )

        # Assert
        assert "deployment.yaml" in manifests
        assert "service.yaml" in manifests
        assert "hpa.yaml" in manifests
        assert "ingress.yaml" in manifests

        # Verify deployment manifest content
        deployment_yaml = manifests["deployment.yaml"]
        assert "booking-service" in deployment_yaml
        assert "culture-connect" in deployment_yaml
        assert "replicas: 3" in deployment_yaml

        # Verify HPA manifest content
        hpa_yaml = manifests["hpa.yaml"]
        assert "HorizontalPodAutoscaler" in hpa_yaml
        assert "minReplicas: 2" in hpa_yaml
        assert "maxReplicas: 10" in hpa_yaml
        assert "averageUtilization: 70" in hpa_yaml

    @pytest.mark.asyncio
    async def test_export_custom_metrics_success(self, service, mock_container_metrics_repo):
        """Test successful custom metrics export for Kubernetes HPA."""
        # Arrange
        namespace = "culture-connect"
        utilization_data = {
            "namespace": namespace,
            "container_count": 5,
            "running_containers": 4,
            "performance": {
                "total_requests": 1000,
                "total_errors": 20,
                "avg_response_time_ms": 200
            }
        }

        mock_container_metrics_repo.get_resource_utilization_summary.return_value = utilization_data

        # Act
        metrics = await service.export_custom_metrics(namespace, "test-correlation-id")

        # Assert
        assert metrics["apiVersion"] == "custom.metrics.k8s.io/v1beta1"
        assert metrics["kind"] == "MetricValueList"
        assert len(metrics["items"]) > 0

        # Check for expected custom metrics
        metric_names = [item["metricName"] for item in metrics["items"]]
        assert "booking_queue_length" in metric_names
        assert "vendor_availability_ratio" in metric_names
        assert "payment_processing_load" in metric_names
