"""
Payout Repository for Culture Connect Backend API.

This module provides comprehensive repository classes for payout-related database operations:
- VendorPayoutRepository: Vendor settlement management with automated calculations
- EscrowAccountRepository: Secure fund holding and dispute management
- Advanced payout processing and scheduling automation
- Escrow release condition checking and automation
- Performance optimization with composite index utilization

Implements Phase 1 Payment & Transaction Management System payout layer.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, case, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.payout_models import VendorPayout, EscrowAccount, PayoutStatus, EscrowStatus, ReleaseCondition
from app.core.payment.config import PaymentProviderType
from app.repositories.base import BaseRepository, PaginationParams, FilterParams, QueryResult, RepositoryError
import logging

logger = logging.getLogger(__name__)


class VendorPayoutRepository(BaseRepository[VendorPayout]):
    """
    Repository for vendor payout management and automation.

    Provides optimized database operations for vendor payouts with:
    - Automated payout calculation and scheduling
    - Multi-provider payout support (Stripe, Paystack, Busha)
    - Performance-optimized queries using composite indexes
    - Payout period management and settlement tracking
    - Comprehensive audit logging and compliance features

    Performance targets:
    - Payout creation: <300ms
    - Status updates: <100ms
    - Settlement queries: <200ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(VendorPayout, db)
        self.logger = logging.getLogger(f"{__name__}.VendorPayoutRepository")

    async def create_vendor_payout(
        self,
        vendor_id: int,
        period_start: datetime,
        period_end: datetime,
        total_earnings: Decimal,
        platform_fee: Decimal,
        processing_fee: Decimal = Decimal("0.00"),
        adjustment_amount: Decimal = Decimal("0.00"),
        currency: str = "NGN",
        provider: Optional[PaymentProviderType] = None,
        **kwargs
    ) -> VendorPayout:
        """
        Create a new vendor payout with automated calculations.

        Args:
            vendor_id: Vendor ID for the payout
            period_start: Payout period start date
            period_end: Payout period end date
            total_earnings: Total earnings for the period
            platform_fee: Platform commission fee
            processing_fee: Payment processing fee
            adjustment_amount: Any adjustments (positive or negative)
            currency: Payout currency
            provider: Payment provider for payout
            **kwargs: Additional payout fields

        Returns:
            Created vendor payout instance
        """
        try:
            # Calculate net amount
            net_amount = total_earnings - platform_fee - processing_fee - adjustment_amount

            # Generate unique reference ID
            reference_id = f"PAYOUT-{datetime.now().strftime('%Y%m%d')}-{vendor_id}-{period_start.strftime('%Y%m%d')}"

            payout_data = {
                "vendor_id": vendor_id,
                "period_start": period_start,
                "period_end": period_end,
                "total_earnings": total_earnings,
                "platform_fee": platform_fee,
                "processing_fee": processing_fee,
                "adjustment_amount": adjustment_amount,
                "net_amount": net_amount,
                "currency": currency,
                "provider": provider,
                "reference_id": reference_id,
                "status": PayoutStatus.PENDING,
                **kwargs
            }

            payout = await self.create(payout_data)

            self.logger.info(
                "Vendor payout created successfully",
                extra={
                    "payout_id": payout.id,
                    "vendor_id": vendor_id,
                    "reference_id": reference_id,
                    "total_earnings": float(total_earnings),
                    "net_amount": float(net_amount),
                    "currency": currency,
                    "period": f"{period_start.date()} to {period_end.date()}"
                }
            )

            return payout

        except Exception as e:
            self.logger.error(
                "Failed to create vendor payout",
                extra={
                    "vendor_id": vendor_id,
                    "total_earnings": float(total_earnings),
                    "period": f"{period_start.date()} to {period_end.date()}",
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create vendor payout: {str(e)}", e)

    async def update_payout_status(
        self,
        payout_id: int,
        status: PayoutStatus,
        provider_payout_id: Optional[str] = None,
        failure_reason: Optional[str] = None,
        **kwargs
    ) -> Optional[VendorPayout]:
        """
        Update payout status with provider tracking.

        Args:
            payout_id: Payout ID to update
            status: New payout status
            provider_payout_id: Provider's payout ID
            failure_reason: Failure reason if status is failed
            **kwargs: Additional fields to update

        Returns:
            Updated payout instance or None if not found
        """
        try:
            update_data = {
                "status": status,
                **kwargs
            }

            if provider_payout_id:
                update_data["provider_payout_id"] = provider_payout_id

            if failure_reason:
                update_data["failure_reason"] = failure_reason

            if status == PayoutStatus.COMPLETED:
                update_data["processed_at"] = datetime.now(timezone.utc)
            elif status in [PayoutStatus.FAILED, PayoutStatus.CANCELLED]:
                update_data["retry_count"] = VendorPayout.retry_count + 1

            payout = await self.update(payout_id, update_data)

            if payout:
                self.logger.info(
                    "Payout status updated",
                    extra={
                        "payout_id": payout_id,
                        "new_status": status.value,
                        "provider_payout_id": provider_payout_id
                    }
                )

            return payout

        except Exception as e:
            self.logger.error(
                "Failed to update payout status",
                extra={
                    "payout_id": payout_id,
                    "status": status.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update payout status: {str(e)}", e)

    async def get_vendor_payouts(
        self,
        vendor_id: int,
        status: Optional[PayoutStatus] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[VendorPayout]:
        """
        Get payouts for a specific vendor with filtering.

        Uses idx_payout_vendor_status and idx_payout_vendor_period indexes.

        Args:
            vendor_id: Vendor ID to filter by
            status: Optional payout status filter
            date_from: Start date filter (period_start)
            date_to: End date filter (period_end)
            pagination: Pagination parameters

        Returns:
            Query result with payouts and metadata
        """
        try:
            query = select(VendorPayout).where(VendorPayout.vendor_id == vendor_id)

            if status:
                query = query.where(VendorPayout.status == status)

            if date_from:
                query = query.where(VendorPayout.period_start >= date_from)

            if date_to:
                query = query.where(VendorPayout.period_end <= date_to)

            # Order by period start date (newest first)
            query = query.order_by(desc(VendorPayout.period_start))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get vendor payouts",
                extra={"vendor_id": vendor_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get vendor payouts: {str(e)}", e)

    async def get_pending_payouts(
        self,
        scheduled_before: Optional[datetime] = None,
        provider: Optional[PaymentProviderType] = None,
        limit: int = 100
    ) -> List[VendorPayout]:
        """
        Get pending payouts ready for processing.

        Uses idx_payout_status_scheduled composite index.

        Args:
            scheduled_before: Optional cutoff time for scheduled payouts
            provider: Optional provider filter
            limit: Maximum number of payouts to return

        Returns:
            List of pending payouts
        """
        try:
            query = select(VendorPayout).where(VendorPayout.status == PayoutStatus.PENDING)

            if scheduled_before:
                query = query.where(
                    or_(
                        VendorPayout.scheduled_at.is_(None),
                        VendorPayout.scheduled_at <= scheduled_before
                    )
                )

            if provider:
                query = query.where(VendorPayout.provider == provider)

            # Order by scheduled time (earliest first), then by creation time
            query = query.order_by(
                asc(VendorPayout.scheduled_at.nullsfirst()),
                asc(VendorPayout.created_at)
            ).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get pending payouts",
                extra={"provider": provider.value if provider else None, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get pending payouts: {str(e)}", e)

    async def calculate_vendor_earnings(
        self,
        vendor_id: int,
        period_start: datetime,
        period_end: datetime,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Calculate vendor earnings for a specific period.

        This method aggregates payment data to calculate earnings.

        Args:
            vendor_id: Vendor ID
            period_start: Period start date
            period_end: Period end date
            currency: Currency filter

        Returns:
            Earnings calculation dictionary
        """
        try:
            # This would typically join with payments table
            # For now, we'll return a placeholder structure
            earnings_query = text("""
                SELECT
                    COALESCE(SUM(p.amount), 0) as total_revenue,
                    COALESCE(SUM(p.platform_fee), 0) as total_platform_fees,
                    COALESCE(SUM(p.provider_fee), 0) as total_provider_fees,
                    COALESCE(SUM(p.net_amount), 0) as total_net_amount,
                    COUNT(p.id) as transaction_count
                FROM payments p
                WHERE p.vendor_id = :vendor_id
                    AND p.currency = :currency
                    AND p.status = 'completed'
                    AND p.paid_at >= :period_start
                    AND p.paid_at <= :period_end
            """)

            result = await self.db.execute(
                earnings_query,
                {
                    "vendor_id": vendor_id,
                    "currency": currency,
                    "period_start": period_start,
                    "period_end": period_end
                }
            )

            row = result.first()

            return {
                "vendor_id": vendor_id,
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "currency": currency,
                "total_revenue": float(row.total_revenue or 0),
                "total_platform_fees": float(row.total_platform_fees or 0),
                "total_provider_fees": float(row.total_provider_fees or 0),
                "total_net_amount": float(row.total_net_amount or 0),
                "transaction_count": row.transaction_count or 0,
                "vendor_earnings": float(row.total_net_amount or 0) - float(row.total_platform_fees or 0)
            }

        except Exception as e:
            self.logger.error(
                "Failed to calculate vendor earnings",
                extra={
                    "vendor_id": vendor_id,
                    "period": f"{period_start.date()} to {period_end.date()}",
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to calculate vendor earnings: {str(e)}", e)


class EscrowAccountRepository(BaseRepository[EscrowAccount]):
    """
    Repository for escrow account management and automation.

    Provides secure database operations for escrow accounts with:
    - Secure fund holding and dispute management
    - Automated release condition checking and processing
    - Conflict detection algorithms with <50ms performance
    - Escrow lifecycle management and tracking
    - Comprehensive audit logging and compliance features

    Performance targets:
    - Escrow creation: <200ms
    - Release condition checking: <50ms
    - Dispute processing: <300ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(EscrowAccount, db)
        self.logger = logging.getLogger(f"{__name__}.EscrowAccountRepository")

    async def create_escrow_account(
        self,
        booking_id: int,
        payment_id: int,
        amount: Decimal,
        release_condition: ReleaseCondition = ReleaseCondition.SERVICE_COMPLETION,
        hold_until: Optional[datetime] = None,
        currency: str = "NGN",
        auto_release_enabled: bool = True,
        **kwargs
    ) -> EscrowAccount:
        """
        Create a new escrow account for secure fund holding.

        Args:
            booking_id: Associated booking ID
            payment_id: Associated payment ID
            amount: Escrow amount
            release_condition: Condition for fund release
            hold_until: Optional hold until date
            currency: Escrow currency
            auto_release_enabled: Whether to enable automatic release
            **kwargs: Additional escrow fields

        Returns:
            Created escrow account instance
        """
        try:
            escrow_data = {
                "booking_id": booking_id,
                "payment_id": payment_id,
                "amount": amount,
                "currency": currency,
                "status": EscrowStatus.ACTIVE,
                "release_condition": release_condition,
                "hold_until": hold_until,
                "auto_release_enabled": auto_release_enabled,
                **kwargs
            }

            escrow = await self.create(escrow_data)

            self.logger.info(
                "Escrow account created successfully",
                extra={
                    "escrow_id": escrow.id,
                    "booking_id": booking_id,
                    "payment_id": payment_id,
                    "amount": float(amount),
                    "currency": currency,
                    "release_condition": release_condition.value,
                    "auto_release_enabled": auto_release_enabled
                }
            )

            return escrow

        except Exception as e:
            self.logger.error(
                "Failed to create escrow account",
                extra={
                    "booking_id": booking_id,
                    "payment_id": payment_id,
                    "amount": float(amount),
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create escrow account: {str(e)}", e)

    async def update_escrow_status(
        self,
        escrow_id: int,
        status: EscrowStatus,
        release_reason: Optional[str] = None,
        dispute_reason: Optional[str] = None,
        **kwargs
    ) -> Optional[EscrowAccount]:
        """
        Update escrow account status with tracking.

        Args:
            escrow_id: Escrow account ID to update
            status: New escrow status
            release_reason: Reason for release if applicable
            dispute_reason: Reason for dispute if applicable
            **kwargs: Additional fields to update

        Returns:
            Updated escrow account instance or None if not found
        """
        try:
            update_data = {
                "status": status,
                **kwargs
            }

            if status == EscrowStatus.RELEASED:
                update_data.update({
                    "released_at": datetime.now(timezone.utc),
                    "release_reason": release_reason
                })
            elif status == EscrowStatus.DISPUTED:
                update_data.update({
                    "disputed_at": datetime.now(timezone.utc),
                    "dispute_reason": dispute_reason
                })

            escrow = await self.update(escrow_id, update_data)

            if escrow:
                self.logger.info(
                    "Escrow status updated",
                    extra={
                        "escrow_id": escrow_id,
                        "new_status": status.value,
                        "release_reason": release_reason,
                        "dispute_reason": dispute_reason
                    }
                )

            return escrow

        except Exception as e:
            self.logger.error(
                "Failed to update escrow status",
                extra={
                    "escrow_id": escrow_id,
                    "status": status.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update escrow status: {str(e)}", e)

    async def get_escrows_ready_for_release(
        self,
        release_condition: Optional[ReleaseCondition] = None,
        auto_release_only: bool = True,
        limit: int = 100
    ) -> List[EscrowAccount]:
        """
        Get escrow accounts ready for automatic release (<50ms target).

        Uses idx_escrow_status_condition and idx_escrow_auto_release indexes.

        Args:
            release_condition: Optional release condition filter
            auto_release_only: Whether to include only auto-release enabled escrows
            limit: Maximum number of escrows to return

        Returns:
            List of escrows ready for release
        """
        try:
            current_time = datetime.now(timezone.utc)

            query = select(EscrowAccount).where(
                and_(
                    EscrowAccount.status == EscrowStatus.ACTIVE,
                    or_(
                        EscrowAccount.hold_until.is_(None),
                        EscrowAccount.hold_until <= current_time
                    )
                )
            )

            if auto_release_only:
                query = query.where(EscrowAccount.auto_release_enabled == True)

            if release_condition:
                query = query.where(EscrowAccount.release_condition == release_condition)

            # Order by creation date (oldest first) and limit results
            query = query.order_by(asc(EscrowAccount.created_at)).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get escrows ready for release",
                extra={"release_condition": release_condition.value if release_condition else None, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get escrows ready for release: {str(e)}", e)

    async def get_disputed_escrows(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[EscrowAccount]:
        """
        Get disputed escrow accounts for resolution.

        Uses idx_escrow_status_disputed index.

        Args:
            date_from: Start date filter (disputed_at)
            date_to: End date filter (disputed_at)
            pagination: Pagination parameters

        Returns:
            Query result with disputed escrows and metadata
        """
        try:
            query = select(EscrowAccount).where(EscrowAccount.status == EscrowStatus.DISPUTED)

            if date_from:
                query = query.where(EscrowAccount.disputed_at >= date_from)

            if date_to:
                query = query.where(EscrowAccount.disputed_at <= date_to)

            # Order by dispute date (newest first)
            query = query.order_by(desc(EscrowAccount.disputed_at))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get disputed escrows",
                extra={"error": str(e)}
            )
            raise RepositoryError(f"Failed to get disputed escrows: {str(e)}", e)

    async def check_release_conditions(
        self,
        escrow_id: int
    ) -> Dict[str, Any]:
        """
        Check if escrow release conditions are met (<50ms target).

        Args:
            escrow_id: Escrow account ID to check

        Returns:
            Release condition check result
        """
        try:
            escrow = await self.get(escrow_id)
            if not escrow:
                return {"can_release": False, "reason": "Escrow not found"}

            current_time = datetime.now(timezone.utc)

            # Check basic conditions
            if escrow.status != EscrowStatus.ACTIVE:
                return {"can_release": False, "reason": f"Escrow status is {escrow.status.value}"}

            if escrow.hold_until and escrow.hold_until > current_time:
                return {"can_release": False, "reason": f"Hold until {escrow.hold_until}"}

            # Check specific release conditions
            release_checks = {
                "escrow_id": escrow_id,
                "status": escrow.status.value,
                "release_condition": escrow.release_condition.value,
                "auto_release_enabled": escrow.auto_release_enabled,
                "hold_until": escrow.hold_until.isoformat() if escrow.hold_until else None,
                "can_release": True,
                "reason": "All conditions met"
            }

            # Additional condition-specific checks would go here
            # For example, checking booking completion status, customer confirmation, etc.

            return release_checks

        except Exception as e:
            self.logger.error(
                "Failed to check release conditions",
                extra={"escrow_id": escrow_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to check release conditions: {str(e)}", e)

    async def get_escrow_analytics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get escrow analytics and metrics.

        Args:
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Escrow analytics dictionary
        """
        try:
            base_query = select(EscrowAccount).where(EscrowAccount.currency == currency)

            if date_from:
                base_query = base_query.where(EscrowAccount.created_at >= date_from)

            if date_to:
                base_query = base_query.where(EscrowAccount.created_at <= date_to)

            # Analytics query
            analytics_query = select(
                func.count(EscrowAccount.id).label('total_escrows'),
                func.sum(EscrowAccount.amount).label('total_amount'),
                func.count(case((EscrowAccount.status == EscrowStatus.ACTIVE, 1))).label('active_escrows'),
                func.count(case((EscrowAccount.status == EscrowStatus.RELEASED, 1))).label('released_escrows'),
                func.count(case((EscrowAccount.status == EscrowStatus.DISPUTED, 1))).label('disputed_escrows'),
                func.count(case((EscrowAccount.auto_release_enabled == True, 1))).label('auto_release_enabled'),
                func.avg(
                    case((EscrowAccount.released_at.isnot(None),
                         func.extract('epoch', EscrowAccount.released_at - EscrowAccount.created_at) / 3600))
                ).label('avg_hold_time_hours')
            ).select_from(base_query.subquery())

            result = await self.db.execute(analytics_query)
            row = result.first()

            return {
                "total_escrows": row.total_escrows or 0,
                "total_amount": float(row.total_amount or 0),
                "active_escrows": row.active_escrows or 0,
                "released_escrows": row.released_escrows or 0,
                "disputed_escrows": row.disputed_escrows or 0,
                "auto_release_enabled": row.auto_release_enabled or 0,
                "avg_hold_time_hours": float(row.avg_hold_time_hours or 0),
                "dispute_rate": (row.disputed_escrows / row.total_escrows * 100) if row.total_escrows > 0 else 0,
                "release_rate": (row.released_escrows / row.total_escrows * 100) if row.total_escrows > 0 else 0,
                "currency": currency
            }

        except Exception as e:
            self.logger.error(
                "Failed to get escrow analytics",
                extra={"currency": currency, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get escrow analytics: {str(e)}", e)
