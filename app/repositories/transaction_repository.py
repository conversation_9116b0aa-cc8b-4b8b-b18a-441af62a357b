"""
Transaction Repository for Culture Connect Backend API.

This module provides comprehensive repository classes for transaction-related database operations:
- TransactionRepository: Detailed transaction tracking with provider integration
- TransactionEventRepository: Transaction event history for compliance and audit trails
- Advanced reconciliation and audit trail management
- Performance optimization with composite index utilization
- Webhook data processing and storage methods

Implements Phase 1 Payment & Transaction Management System transaction layer.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, case, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.transaction_models import Transaction, TransactionEvent, TransactionStatus, TransactionEventType, TransactionType
from app.core.payment.config import PaymentProviderType
from app.repositories.base import BaseRepository, PaginationParams, FilterParams, QueryResult, RepositoryError
import logging

logger = logging.getLogger(__name__)


class TransactionRepository(BaseRepository[Transaction]):
    """
    Repository for transaction management and tracking.

    Provides optimized database operations for transaction processing with:
    - Detailed transaction tracking with provider integration
    - Reconciliation and audit trail management
    - Performance-optimized queries using composite indexes
    - Provider response handling and webhook processing
    - Comprehensive audit logging and compliance features

    Performance targets:
    - Transaction creation: <200ms
    - Status updates: <100ms
    - Reconciliation queries: <200ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(Transaction, db)
        self.logger = logging.getLogger(f"{__name__}.TransactionRepository")

    async def create_transaction(
        self,
        payment_id: int,
        transaction_type: TransactionType,
        amount: Decimal,
        provider: PaymentProviderType,
        currency: str = "NGN",
        **kwargs
    ) -> Transaction:
        """
        Create a new transaction with optimized performance.

        Args:
            payment_id: Associated payment ID
            transaction_type: Type of transaction
            amount: Transaction amount
            provider: Payment provider
            currency: Transaction currency
            **kwargs: Additional transaction fields

        Returns:
            Created transaction instance
        """
        try:
            # Generate unique reference ID
            reference_id = f"TXN-{datetime.now().strftime('%Y%m%d%H%M%S')}-{payment_id}"

            transaction_data = {
                "payment_id": payment_id,
                "type": transaction_type,
                "amount": amount,
                "currency": currency,
                "provider": provider,
                "reference_id": reference_id,
                "status": TransactionStatus.PENDING,
                **kwargs
            }

            transaction = await self.create(transaction_data)

            self.logger.info(
                "Transaction created successfully",
                extra={
                    "transaction_id": transaction.id,
                    "payment_id": payment_id,
                    "reference_id": reference_id,
                    "type": transaction_type.value,
                    "amount": float(amount),
                    "provider": provider.value
                }
            )

            return transaction

        except Exception as e:
            self.logger.error(
                "Failed to create transaction",
                extra={
                    "payment_id": payment_id,
                    "type": transaction_type.value,
                    "amount": float(amount),
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create transaction: {str(e)}", e)

    async def update_transaction_status(
        self,
        transaction_id: int,
        status: TransactionStatus,
        provider_transaction_id: Optional[str] = None,
        provider_response: Optional[str] = None,
        failure_reason: Optional[str] = None,
        **kwargs
    ) -> Optional[Transaction]:
        """
        Update transaction status with provider response handling.

        Args:
            transaction_id: Transaction ID to update
            status: New transaction status
            provider_transaction_id: Provider's transaction ID
            provider_response: Provider response data
            failure_reason: Failure reason if status is failed
            **kwargs: Additional fields to update

        Returns:
            Updated transaction instance or None if not found
        """
        try:
            update_data = {
                "status": status,
                **kwargs
            }

            if provider_transaction_id:
                update_data["provider_transaction_id"] = provider_transaction_id

            if provider_response:
                update_data["provider_response"] = provider_response

            if failure_reason:
                update_data["failure_reason"] = failure_reason

            if status == TransactionStatus.COMPLETED:
                update_data["processed_at"] = datetime.now(timezone.utc)
            elif status in [TransactionStatus.FAILED, TransactionStatus.CANCELLED]:
                update_data["retry_count"] = Transaction.retry_count + 1

            transaction = await self.update(transaction_id, update_data)

            if transaction:
                self.logger.info(
                    "Transaction status updated",
                    extra={
                        "transaction_id": transaction_id,
                        "new_status": status.value,
                        "provider_transaction_id": provider_transaction_id
                    }
                )

            return transaction

        except Exception as e:
            self.logger.error(
                "Failed to update transaction status",
                extra={
                    "transaction_id": transaction_id,
                    "status": status.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update transaction status: {str(e)}", e)

    async def get_transactions_by_payment(
        self,
        payment_id: int,
        transaction_type: Optional[TransactionType] = None,
        status: Optional[TransactionStatus] = None
    ) -> List[Transaction]:
        """
        Get transactions by payment ID with filtering.

        Uses idx_transaction_payment_type composite index.

        Args:
            payment_id: Payment ID to filter by
            transaction_type: Optional transaction type filter
            status: Optional transaction status filter

        Returns:
            List of transactions
        """
        try:
            query = select(Transaction).where(Transaction.payment_id == payment_id)

            if transaction_type:
                query = query.where(Transaction.type == transaction_type)

            if status:
                query = query.where(Transaction.status == status)

            # Order by creation date (newest first)
            query = query.order_by(desc(Transaction.created_at))

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get transactions by payment",
                extra={"payment_id": payment_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get transactions by payment: {str(e)}", e)

    async def get_unreconciled_transactions(
        self,
        provider: Optional[PaymentProviderType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 1000
    ) -> List[Transaction]:
        """
        Get unreconciled transactions for reconciliation processing.

        Uses idx_transaction_reconciled composite index.

        Args:
            provider: Optional provider filter
            date_from: Start date filter
            date_to: End date filter
            limit: Maximum number of transactions to return

        Returns:
            List of unreconciled transactions
        """
        try:
            query = select(Transaction).where(
                and_(
                    Transaction.reconciled == False,
                    Transaction.status == TransactionStatus.COMPLETED
                )
            )

            if provider:
                query = query.where(Transaction.provider == provider)

            if date_from:
                query = query.where(Transaction.created_at >= date_from)

            if date_to:
                query = query.where(Transaction.created_at <= date_to)

            # Order by creation date and limit results
            query = query.order_by(asc(Transaction.created_at)).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get unreconciled transactions",
                extra={"provider": provider.value if provider else None, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get unreconciled transactions: {str(e)}", e)

    async def mark_transactions_reconciled(
        self,
        transaction_ids: List[int],
        reconciliation_reference: str
    ) -> int:
        """
        Mark multiple transactions as reconciled in bulk.

        Args:
            transaction_ids: List of transaction IDs to mark as reconciled
            reconciliation_reference: Reference for the reconciliation batch

        Returns:
            Number of transactions updated
        """
        try:
            result = await self.db.execute(
                update(Transaction)
                .where(Transaction.id.in_(transaction_ids))
                .values(
                    reconciled=True,
                    reconciled_at=datetime.now(timezone.utc),
                    reconciliation_reference=reconciliation_reference
                )
            )

            updated_count = result.rowcount

            self.logger.info(
                "Transactions marked as reconciled",
                extra={
                    "transaction_count": updated_count,
                    "reconciliation_reference": reconciliation_reference
                }
            )

            return updated_count

        except Exception as e:
            self.logger.error(
                "Failed to mark transactions as reconciled",
                extra={
                    "transaction_ids": transaction_ids,
                    "reconciliation_reference": reconciliation_reference,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to mark transactions as reconciled: {str(e)}", e)

    async def get_transaction_analytics(
        self,
        provider: Optional[PaymentProviderType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get transaction analytics with aggregated metrics.

        Uses idx_transaction_provider_status and idx_transaction_currency_amount indexes.

        Args:
            provider: Optional provider filter
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Transaction analytics dictionary
        """
        try:
            base_query = select(Transaction).where(Transaction.currency == currency)

            if provider:
                base_query = base_query.where(Transaction.provider == provider)

            if date_from:
                base_query = base_query.where(Transaction.created_at >= date_from)

            if date_to:
                base_query = base_query.where(Transaction.created_at <= date_to)

            # Aggregate query for analytics
            analytics_query = select(
                func.count(Transaction.id).label('total_transactions'),
                func.sum(Transaction.amount).label('total_amount'),
                func.sum(Transaction.provider_fee).label('total_provider_fees'),
                func.sum(Transaction.platform_fee).label('total_platform_fees'),
                func.count(case((Transaction.status == TransactionStatus.COMPLETED, 1))).label('completed_transactions'),
                func.count(case((Transaction.status == TransactionStatus.FAILED, 1))).label('failed_transactions'),
                func.count(case((Transaction.type == TransactionType.CHARGE, 1))).label('charge_transactions'),
                func.count(case((Transaction.type == TransactionType.REFUND, 1))).label('refund_transactions'),
                func.count(case((Transaction.reconciled == True, 1))).label('reconciled_transactions')
            ).select_from(base_query.subquery())

            result = await self.db.execute(analytics_query)
            row = result.first()

            return {
                "total_transactions": row.total_transactions or 0,
                "total_amount": float(row.total_amount or 0),
                "total_provider_fees": float(row.total_provider_fees or 0),
                "total_platform_fees": float(row.total_platform_fees or 0),
                "completed_transactions": row.completed_transactions or 0,
                "failed_transactions": row.failed_transactions or 0,
                "charge_transactions": row.charge_transactions or 0,
                "refund_transactions": row.refund_transactions or 0,
                "reconciled_transactions": row.reconciled_transactions or 0,
                "success_rate": (row.completed_transactions / row.total_transactions * 100) if row.total_transactions > 0 else 0,
                "reconciliation_rate": (row.reconciled_transactions / row.completed_transactions * 100) if row.completed_transactions > 0 else 0,
                "currency": currency
            }

        except Exception as e:
            self.logger.error(
                "Failed to get transaction analytics",
                extra={"provider": provider.value if provider else None, "currency": currency, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get transaction analytics: {str(e)}", e)


class TransactionEventRepository(BaseRepository[TransactionEvent]):
    """
    Repository for transaction event management and audit trails.

    Provides database operations for transaction events with:
    - Comprehensive audit trail tracking
    - Webhook and provider callback processing
    - Performance monitoring and analytics
    - Event-based transaction status tracking
    - Compliance and regulatory audit support

    Performance targets:
    - Event creation: <50ms
    - Event queries: <100ms
    - Audit trail generation: <200ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(TransactionEvent, db)
        self.logger = logging.getLogger(f"{__name__}.TransactionEventRepository")

    async def create_transaction_event(
        self,
        transaction_id: int,
        event_type: TransactionEventType,
        event_source: str,
        event_data: Optional[Dict[str, Any]] = None,
        previous_status: Optional[str] = None,
        new_status: Optional[str] = None,
        **kwargs
    ) -> TransactionEvent:
        """
        Create a new transaction event for audit tracking.

        Args:
            transaction_id: Associated transaction ID
            event_type: Type of event
            event_source: Source of the event (api, webhook, etc.)
            event_data: Event data payload
            previous_status: Previous transaction status
            new_status: New transaction status
            **kwargs: Additional event fields

        Returns:
            Created transaction event instance
        """
        try:
            event_data_dict = {
                "transaction_id": transaction_id,
                "event_type": event_type,
                "event_source": event_source,
                "event_data": event_data,
                "previous_status": previous_status,
                "new_status": new_status,
                **kwargs
            }

            event = await self.create(event_data_dict)

            self.logger.info(
                "Transaction event created",
                extra={
                    "event_id": event.id,
                    "transaction_id": transaction_id,
                    "event_type": event_type.value,
                    "event_source": event_source,
                    "status_change": f"{previous_status} -> {new_status}" if previous_status and new_status else None
                }
            )

            return event

        except Exception as e:
            self.logger.error(
                "Failed to create transaction event",
                extra={
                    "transaction_id": transaction_id,
                    "event_type": event_type.value,
                    "event_source": event_source,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create transaction event: {str(e)}", e)

    async def get_transaction_events(
        self,
        transaction_id: int,
        event_type: Optional[TransactionEventType] = None,
        event_source: Optional[str] = None
    ) -> List[TransactionEvent]:
        """
        Get events for a specific transaction.

        Uses idx_transaction_event_transaction_type composite index.

        Args:
            transaction_id: Transaction ID to filter by
            event_type: Optional event type filter
            event_source: Optional event source filter

        Returns:
            List of transaction events ordered by creation time
        """
        try:
            query = select(TransactionEvent).where(TransactionEvent.transaction_id == transaction_id)

            if event_type:
                query = query.where(TransactionEvent.event_type == event_type)

            if event_source:
                query = query.where(TransactionEvent.event_source == event_source)

            # Order by creation date (oldest first for audit trail)
            query = query.order_by(asc(TransactionEvent.created_at))

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get transaction events",
                extra={"transaction_id": transaction_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get transaction events: {str(e)}", e)

    async def get_webhook_events(
        self,
        provider_event_id: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 1000
    ) -> List[TransactionEvent]:
        """
        Get webhook events for processing and analysis.

        Uses idx_transaction_event_provider_id index.

        Args:
            provider_event_id: Optional provider event ID filter
            date_from: Start date filter
            date_to: End date filter
            limit: Maximum number of events to return

        Returns:
            List of webhook events
        """
        try:
            query = select(TransactionEvent).where(
                TransactionEvent.event_type == TransactionEventType.WEBHOOK_RECEIVED
            )

            if provider_event_id:
                query = query.where(TransactionEvent.provider_event_id == provider_event_id)

            if date_from:
                query = query.where(TransactionEvent.created_at >= date_from)

            if date_to:
                query = query.where(TransactionEvent.created_at <= date_to)

            # Order by creation date and limit results
            query = query.order_by(desc(TransactionEvent.created_at)).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get webhook events",
                extra={"provider_event_id": provider_event_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get webhook events: {str(e)}", e)

    async def get_performance_metrics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        event_source: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get performance metrics for transaction events.

        Args:
            date_from: Start date filter
            date_to: End date filter
            event_source: Optional event source filter

        Returns:
            Performance metrics dictionary
        """
        try:
            base_query = select(TransactionEvent)

            if date_from:
                base_query = base_query.where(TransactionEvent.created_at >= date_from)

            if date_to:
                base_query = base_query.where(TransactionEvent.created_at <= date_to)

            if event_source:
                base_query = base_query.where(TransactionEvent.event_source == event_source)

            # Performance metrics query
            metrics_query = select(
                func.count(TransactionEvent.id).label('total_events'),
                func.avg(TransactionEvent.processing_time_ms).label('avg_processing_time'),
                func.max(TransactionEvent.processing_time_ms).label('max_processing_time'),
                func.min(TransactionEvent.processing_time_ms).label('min_processing_time'),
                func.count(case((TransactionEvent.error_message.isnot(None), 1))).label('error_events'),
                func.count(case((TransactionEvent.event_type == TransactionEventType.WEBHOOK_RECEIVED, 1))).label('webhook_events'),
                func.count(case((TransactionEvent.event_type == TransactionEventType.COMPLETED, 1))).label('completion_events')
            ).select_from(base_query.subquery())

            result = await self.db.execute(metrics_query)
            row = result.first()

            return {
                "total_events": row.total_events or 0,
                "avg_processing_time_ms": float(row.avg_processing_time or 0),
                "max_processing_time_ms": row.max_processing_time or 0,
                "min_processing_time_ms": row.min_processing_time or 0,
                "error_events": row.error_events or 0,
                "webhook_events": row.webhook_events or 0,
                "completion_events": row.completion_events or 0,
                "error_rate": (row.error_events / row.total_events * 100) if row.total_events > 0 else 0,
                "webhook_rate": (row.webhook_events / row.total_events * 100) if row.total_events > 0 else 0
            }

        except Exception as e:
            self.logger.error(
                "Failed to get performance metrics",
                extra={"event_source": event_source, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get performance metrics: {str(e)}", e)
