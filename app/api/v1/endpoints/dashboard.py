"""
Dashboard API endpoints for Culture Connect Backend API.

This module provides comprehensive dashboard management endpoints including:
- Dashboard widget management endpoints (CRUD operations)
- Real-time dashboard data endpoints with <200ms response targets
- Widget positioning and layout management endpoints
- System metrics endpoints with automatic aggregation

Implements Phase 7.1 requirements for dashboard API endpoints with:
- Performance optimization targeting <200ms for GET requests, <500ms for POST/PUT
- Comprehensive error handling with correlation IDs and structured responses
- Integration with completed DashboardService for business logic
- Proper authentication and authorization using existing auth patterns

Production-grade implementation following monolithic FastAPI architecture patterns.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.dashboard_service import DashboardService
from app.schemas.analytics_schemas import (
    DashboardWidgetCreate, DashboardWidgetUpdate, DashboardWidgetResponse,
    SystemMetricsCreate, SystemMetricsResponse
)
from app.schemas.auth import MessageResponse
from app.schemas.analytics_schemas import PaginatedResponse
from app.models.analytics_models import AnalyticsTimeframe, MetricType
from app.core.deps import get_current_user
from app.core.deps import require_permissions
from app.core.security import Permission
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()
security = HTTPBearer()


@router.post(
    "/widgets",
    response_model=DashboardWidgetResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create dashboard widget",
    description="Create a new dashboard widget with configuration validation"
)
async def create_dashboard_widget(
    widget_data: DashboardWidgetCreate,
    dashboard_id: Optional[UUID] = Query(None, description="Dashboard UUID for widget placement"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DashboardWidgetResponse:
    """
    Create dashboard widget with configuration validation.

    Performance Metrics:
    - Target response time: <500ms for widget creation
    - Configuration validation: <100ms with comprehensive checks
    - Widget creation: <300ms with optimized insert and cache management

    Args:
        widget_data: Widget configuration data
        dashboard_id: Optional dashboard UUID for widget placement
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        DashboardWidgetResponse: Created widget data

    Raises:
        HTTPException: If widget creation fails or validation errors
    """
    try:
        # Check permissions - requires dashboard management permission
        if not require_permissions([Permission.DASHBOARD_MANAGE], current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions"
            )

        # Initialize dashboard service
        dashboard_service = DashboardService(db)

        # Create dashboard widget
        widget = await dashboard_service.create_dashboard_widget(
            widget_data=widget_data,
            dashboard_id=dashboard_id
        )

        logger.info(
            f"Dashboard widget created successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_id": widget.uuid,
                "widget_name": widget.name,
                "dashboard_id": str(dashboard_id) if dashboard_id else None,
                "created_by": current_user["id"]
            }
        )

        return widget

    except ValueError as e:
        logger.warning(
            f"Dashboard widget validation failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_name": widget_data.name,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(
            f"Failed to create dashboard widget: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_name": widget_data.name,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create dashboard widget"
        )


@router.get(
    "/widgets",
    response_model=List[DashboardWidgetResponse],
    status_code=status.HTTP_200_OK,
    summary="Get dashboard widgets",
    description="Retrieve dashboard widgets with optional real-time data aggregation"
)
async def get_dashboard_widgets(
    dashboard_id: Optional[UUID] = Query(None, description="Dashboard UUID filter"),
    is_active: bool = Query(True, description="Filter by active status"),
    include_data: bool = Query(False, description="Include real-time widget data"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[DashboardWidgetResponse]:
    """
    Get dashboard widgets with optional real-time data aggregation.

    Performance Metrics:
    - Target response time: <200ms for widget queries
    - Cache optimization: >90% cache hit rate for frequently accessed widgets
    - Data aggregation: <300ms for real-time data when include_data=True

    Args:
        dashboard_id: Optional dashboard UUID filter
        is_active: Filter by active status
        include_data: Whether to include real-time widget data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        List[DashboardWidgetResponse]: List of dashboard widgets

    Raises:
        HTTPException: If access denied or query fails
    """
    try:
        # Check permissions - requires dashboard read permission
        if not require_permissions([Permission.DASHBOARD_READ], current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions"
            )

        # Initialize dashboard service
        dashboard_service = DashboardService(db)

        # Get dashboard widgets
        widgets = await dashboard_service.get_dashboard_widgets(
            dashboard_id=dashboard_id,
            is_active=is_active,
            include_data=include_data
        )

        logger.info(
            f"Dashboard widgets retrieved successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "dashboard_id": str(dashboard_id) if dashboard_id else None,
                "widget_count": len(widgets),
                "include_data": include_data,
                "requested_by": current_user["id"]
            }
        )

        return widgets

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get dashboard widgets: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "dashboard_id": str(dashboard_id) if dashboard_id else None,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard widgets"
        )


@router.put(
    "/widgets/{widget_id}/position",
    response_model=DashboardWidgetResponse,
    status_code=status.HTTP_200_OK,
    summary="Update widget position",
    description="Update widget position and size with grid validation"
)
async def update_widget_position(
    widget_id: UUID = Path(..., description="Widget UUID"),
    position_x: int = Query(..., ge=0, description="X position on dashboard grid"),
    position_y: int = Query(..., ge=0, description="Y position on dashboard grid"),
    width: Optional[int] = Query(None, gt=0, description="Widget width"),
    height: Optional[int] = Query(None, gt=0, description="Widget height"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DashboardWidgetResponse:
    """
    Update widget position and size with grid validation.

    Performance Metrics:
    - Target response time: <200ms for position updates
    - Position validation: <100ms with conflict detection
    - Cache invalidation: <50ms for related cache keys

    Args:
        widget_id: Widget UUID
        position_x: X position on dashboard grid
        position_y: Y position on dashboard grid
        width: Optional widget width
        height: Optional widget height
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        DashboardWidgetResponse: Updated widget data

    Raises:
        HTTPException: If widget not found, validation fails, or access denied
    """
    try:
        # Check permissions - requires dashboard management permission
        if not require_permissions([Permission.DASHBOARD_MANAGE], current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions"
            )

        # Initialize dashboard service
        dashboard_service = DashboardService(db)

        # Update widget position
        widget = await dashboard_service.update_widget_position(
            widget_id=widget_id,
            position_x=position_x,
            position_y=position_y,
            width=width,
            height=height
        )

        logger.info(
            f"Widget position updated successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_id": str(widget_id),
                "new_position": f"({position_x}, {position_y})",
                "new_size": f"{width}x{height}" if width and height else "unchanged",
                "updated_by": current_user["id"]
            }
        )

        return widget

    except ValueError as e:
        logger.warning(
            f"Widget position validation failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_id": str(widget_id),
                "position": f"({position_x}, {position_y})",
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(
            f"Failed to update widget position: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "widget_id": str(widget_id),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update widget position"
        )


@router.post(
    "/metrics",
    response_model=SystemMetricsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Record system metric",
    description="Record system metric with automatic aggregation and circuit breaker protection"
)
async def record_system_metric(
    metric_name: str = Query(..., description="Name of the metric"),
    metric_type: MetricType = Query(..., description="Type of metric"),
    value: float = Query(..., description="Metric value"),
    timeframe: AnalyticsTimeframe = Query(AnalyticsTimeframe.HOURLY, description="Analytics timeframe"),
    tags: Optional[str] = Query(None, description="JSON string of metric tags"),
    metadata: Optional[str] = Query(None, description="JSON string of metric metadata"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> SystemMetricsResponse:
    """
    Record system metric with automatic aggregation.

    Performance Metrics:
    - Target response time: <100ms for metric recording
    - Automatic aggregation: <50ms for time-series data processing
    - Circuit breaker protection for high-frequency metric recording

    Args:
        metric_name: Name of the metric
        metric_type: Type of metric (counter, gauge, histogram, etc.)
        value: Metric value
        timeframe: Analytics timeframe for aggregation
        tags: Optional JSON string of metric tags
        metadata: Optional JSON string of metric metadata
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        SystemMetricsResponse: Recorded metric data

    Raises:
        HTTPException: If metric recording fails or validation errors
    """
    try:
        # Check permissions - requires metrics write permission
        if not require_permissions([Permission.METRICS_WRITE], current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions"
            )

        # Parse tags and metadata if provided
        import json
        from decimal import Decimal

        parsed_tags = None
        parsed_metadata = None

        if tags:
            try:
                parsed_tags = json.loads(tags)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for tags")

        if metadata:
            try:
                parsed_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for metadata")

        # Initialize dashboard service
        dashboard_service = DashboardService(db)

        # Record system metric
        metric = await dashboard_service.record_system_metric(
            metric_name=metric_name,
            metric_type=metric_type,
            value=Decimal(str(value)),
            timeframe=timeframe,
            tags=parsed_tags,
            metadata=parsed_metadata
        )

        logger.info(
            f"System metric recorded successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "metric_type": metric_type.value,
                "value": str(value),
                "recorded_by": current_user["id"]
            }
        )

        return metric

    except ValueError as e:
        logger.warning(
            f"System metric validation failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(
            f"Failed to record system metric: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record system metric"
        )


@router.get(
    "/metrics/{metric_name}/timeseries",
    response_model=List[SystemMetricsResponse],
    status_code=status.HTTP_200_OK,
    summary="Get metric time-series data",
    description="Get time-series data for a specific metric with PostgreSQL window functions"
)
async def get_metric_timeseries(
    metric_name: str = Path(..., description="Name of the metric"),
    timeframe: AnalyticsTimeframe = Query(AnalyticsTimeframe.HOURLY, description="Analytics timeframe"),
    start_date: Optional[datetime] = Query(None, description="Start date for time-series range"),
    end_date: Optional[datetime] = Query(None, description="End date for time-series range"),
    tags_filter: Optional[str] = Query(None, description="JSON string of tags filter"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[SystemMetricsResponse]:
    """
    Get time-series data for a specific metric.

    Performance Metrics:
    - Target response time: <200ms for time-series queries
    - Uses optimized indexes for metric name and timeframe
    - PostgreSQL window functions for trend analysis

    Args:
        metric_name: Name of the metric
        timeframe: Analytics timeframe
        start_date: Optional start date for range
        end_date: Optional end date for range
        tags_filter: Optional JSON string of tags filter
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        List[SystemMetricsResponse]: Time-series metric data

    Raises:
        HTTPException: If access denied or query fails
    """
    try:
        # Check permissions - requires metrics read permission
        if not require_permissions([Permission.METRICS_READ], current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient permissions"
            )

        # Set default date range if not provided
        if not start_date or not end_date:
            end_date = datetime.now()
            if timeframe == AnalyticsTimeframe.HOURLY:
                start_date = end_date - timedelta(hours=24)
            elif timeframe == AnalyticsTimeframe.DAILY:
                start_date = end_date - timedelta(days=30)
            else:
                start_date = end_date - timedelta(days=7)

        # Parse tags filter if provided
        import json
        parsed_tags_filter = None
        if tags_filter:
            try:
                parsed_tags_filter = json.loads(tags_filter)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for tags filter")

        # Initialize dashboard service and get metrics repository
        dashboard_service = DashboardService(db)

        # Get time-series data using repository method
        async with dashboard_service.get_session_context() as session:
            from app.repositories.dashboard_repositories import SystemMetricsRepository
            metrics_repo = SystemMetricsRepository(session)

            metrics = await metrics_repo.get_metrics_time_series(
                metric_name=metric_name,
                timeframe=timeframe,
                date_range=(start_date, end_date),
                tags_filter=parsed_tags_filter
            )

        # Convert to response schemas
        metric_responses = [SystemMetricsResponse.model_validate(metric) for metric in metrics]

        logger.info(
            f"Metric time-series data retrieved successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "timeframe": timeframe.value,
                "data_points": len(metric_responses),
                "requested_by": current_user["id"]
            }
        )

        return metric_responses

    except ValueError as e:
        logger.warning(
            f"Metric time-series validation failed: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to get metric time-series data: {str(e)}",
            extra={
                "correlation_id": correlation_id.get(''),
                "metric_name": metric_name,
                "timeframe": timeframe.value,
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metric time-series data"
        )
