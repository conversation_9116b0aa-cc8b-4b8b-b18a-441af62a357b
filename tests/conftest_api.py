"""
Enhanced pytest configuration for API endpoint testing.

This module provides comprehensive test fixtures and configuration specifically
for API endpoint testing with proper async handling, database mocking, and
dependency injection overrides.

Key Features:
- Async test client with proper event loop management
- Database session mocking with AsyncMock
- Authentication dependency overrides
- Service layer mocking utilities
- Test data factories for API testing
- Error simulation utilities

Production-grade test infrastructure following established testing patterns.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import AsyncGenerator, Dict, Any, Optional
from datetime import datetime, timezone
from uuid import uuid4

import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.core.deps import get_db, get_current_user
from app.models.user import User
from app.models.booking import BookingStatus, VendorResponseType, BookingPriority
from app.schemas.booking_schemas import BookingResponseSchema


# Configure pytest for async tests
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def mock_db_session():
    """Create a mock async database session for testing."""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    session.close = AsyncMock()
    session.execute = AsyncMock()
    session.scalar = AsyncMock()
    session.scalars = AsyncMock()
    session.get = AsyncMock()
    session.merge = AsyncMock()
    session.delete = AsyncMock()
    session.flush = AsyncMock()
    session.begin = AsyncMock()
    return session


@pytest.fixture
def mock_user():
    """Mock authenticated customer user."""
    user = MagicMock(spec=User)
    user.id = 1
    user.email = "<EMAIL>"
    user.role = "customer"
    user.is_active = True
    user.is_verified = True
    user.first_name = "Test"
    user.last_name = "Customer"
    return user


@pytest.fixture
def mock_vendor_user():
    """Mock authenticated vendor user."""
    user = MagicMock(spec=User)
    user.id = 2
    user.email = "<EMAIL>"
    user.role = "vendor"
    user.is_active = True
    user.is_verified = True
    user.first_name = "Test"
    user.last_name = "Vendor"
    user.vendor_profile = MagicMock()
    user.vendor_profile.id = 1
    return user


@pytest.fixture
def mock_admin_user():
    """Mock authenticated admin user."""
    user = MagicMock(spec=User)
    user.id = 3
    user.email = "<EMAIL>"
    user.role = "admin"
    user.is_active = True
    user.is_verified = True
    user.first_name = "Test"
    user.last_name = "Admin"
    return user


@pytest.fixture
def test_app(mock_db_session, mock_user):
    """Create test FastAPI app with dependency overrides."""
    # Create a copy of the app for testing
    test_app = FastAPI()
    
    # Copy routes from main app
    test_app.router = app.router
    
    # Override dependencies
    test_app.dependency_overrides[get_db] = lambda: mock_db_session
    test_app.dependency_overrides[get_current_user] = lambda: mock_user
    
    yield test_app
    
    # Clean up overrides
    test_app.dependency_overrides.clear()


@pytest.fixture
def test_client(test_app):
    """Create test client with dependency overrides."""
    return TestClient(test_app)


@pytest.fixture
async def async_test_client(test_app):
    """Create async test client for async endpoint testing."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


# Booking Test Data Factories
@pytest.fixture
def booking_create_data():
    """Sample booking creation data."""
    return {
        "service_id": 123,
        "booking_date": "2025-02-15",
        "booking_time": "14:30:00",
        "duration_hours": "2.5",
        "participant_count": 2,
        "special_requirements": "Vegetarian meal required",
        "customer_notes": "Celebrating anniversary",
        "priority": "normal",
        "booking_source": "web"
    }


@pytest.fixture
def booking_update_data():
    """Sample booking update data."""
    return {
        "booking_date": "2025-02-16",
        "booking_time": "15:00:00",
        "participant_count": 3,
        "special_requirements": "Updated requirements",
        "customer_notes": "Updated notes"
    }


@pytest.fixture
def vendor_response_data():
    """Sample vendor response data."""
    return {
        "response_type": "accept",
        "vendor_notes": "Looking forward to hosting you!",
        "proposed_changes": None
    }


@pytest.fixture
def mock_booking_response():
    """Mock booking response data."""
    return {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "booking_reference": "BK-2025-001234",
        "customer_id": 1,
        "vendor_id": 2,
        "service_id": 123,
        "booking_date": "2025-02-15",
        "booking_time": "14:30:00",
        "duration_hours": "2.5",
        "participant_count": 2,
        "status": "pending",
        "priority": "normal",
        "base_price": "100.00",
        "additional_fees": "10.00",
        "discount_amount": "0.00",
        "total_amount": "110.00",
        "currency": "USD",
        "payment_status": "pending",
        "completion_confirmed_by_customer": False,
        "completion_confirmed_by_vendor": False,
        "unread_messages_customer": 0,
        "unread_messages_vendor": 0,
        "refund_amount": "0.00",
        "booking_source": "web",
        "created_at": "2025-01-24T12:00:00Z",
        "updated_at": "2025-01-24T12:00:00Z"
    }


@pytest.fixture
def mock_booking_list_response():
    """Mock booking list response data."""
    return {
        "bookings": [
            {
                "id": 1,
                "booking_reference": "BK-2025-001234",
                "status": "pending",
                "total_amount": "110.00"
            }
        ],
        "total": 1,
        "page": 1,
        "per_page": 20,
        "pages": 1,
        "has_next": False,
        "has_prev": False
    }


# Service Mocking Utilities
@pytest.fixture
def mock_booking_service():
    """Mock BookingService for endpoint testing."""
    service = AsyncMock()
    service.create_booking = AsyncMock()
    service.get_customer_bookings = AsyncMock()
    service.get_vendor_bookings = AsyncMock()
    service.get_with_details = AsyncMock()
    service.update_booking = AsyncMock()
    service.update_status = AsyncMock()
    service.process_vendor_response = AsyncMock()
    service.check_availability = AsyncMock()
    service.cancel_booking = AsyncMock()
    return service


@pytest.fixture
def mock_email_service():
    """Mock EmailService for notification testing."""
    service = AsyncMock()
    service.send_booking_confirmation = AsyncMock()
    service.send_booking_update = AsyncMock()
    service.send_vendor_notification = AsyncMock()
    return service


@pytest.fixture
def mock_push_notification_service():
    """Mock PushNotificationService for testing."""
    service = AsyncMock()
    service.send_booking_notification = AsyncMock()
    service.send_vendor_notification = AsyncMock()
    return service


# Authentication Mocking Utilities
@pytest.fixture
def auth_headers():
    """Standard authentication headers for testing."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def vendor_auth_headers():
    """Vendor authentication headers for testing."""
    return {"Authorization": "Bearer vendor-token"}


@pytest.fixture
def admin_auth_headers():
    """Admin authentication headers for testing."""
    return {"Authorization": "Bearer admin-token"}


# Error Simulation Utilities
@pytest.fixture
def database_error_simulator():
    """Utility for simulating database errors."""
    def simulate_error(error_type="connection"):
        if error_type == "connection":
            return Exception("Database connection failed")
        elif error_type == "timeout":
            return Exception("Database operation timeout")
        elif error_type == "constraint":
            return Exception("Database constraint violation")
        else:
            return Exception("Generic database error")
    
    return simulate_error


@pytest.fixture
def service_error_simulator():
    """Utility for simulating service layer errors."""
    def simulate_error(error_type="validation"):
        if error_type == "validation":
            return ValueError("Validation error: Invalid data")
        elif error_type == "not_found":
            return Exception("Resource not found")
        elif error_type == "permission":
            return Exception("Permission denied")
        else:
            return Exception("Generic service error")
    
    return simulate_error


# Performance Testing Utilities
@pytest.fixture
def performance_timer():
    """Timer utility for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Test Data Validation Utilities
@pytest.fixture
def response_validator():
    """Utility for validating API response structure."""
    def validate_booking_response(response_data: Dict[str, Any]) -> bool:
        required_fields = [
            "id", "booking_reference", "status", "total_amount",
            "customer_id", "service_id", "booking_date"
        ]
        return all(field in response_data for field in required_fields)
    
    def validate_error_response(response_data: Dict[str, Any]) -> bool:
        required_fields = ["detail", "status_code"]
        return all(field in response_data for field in required_fields)
    
    return {
        "booking": validate_booking_response,
        "error": validate_error_response
    }
