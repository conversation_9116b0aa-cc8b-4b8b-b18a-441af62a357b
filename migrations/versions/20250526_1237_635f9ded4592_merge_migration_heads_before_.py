"""Merge migration heads before availability models

Revision ID: 635f9ded4592
Revises: booking_workflow_tables, a78010842013
Create Date: 2025-05-26 12:37:48.969811+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '635f9ded4592'
down_revision = ('booking_workflow_tables', 'a78010842013')
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema."""
    pass


def downgrade() -> None:
    """Downgrade database schema."""
    pass
