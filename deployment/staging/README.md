# Culture Connect Backend - Staging Deployment Guide

**Target Environment**: Hetzner Cloud Infrastructure  
**Audience**: DevOps engineers and mid-level developers  
**Prerequisites**: Terra<PERSON>, kube<PERSON><PERSON>, <PERSON><PERSON>, Hetzner Cloud account  
**Estimated Deployment Time**: 2-3 hours

## 📋 Prerequisites

### Required Tools
- **Terraform**: v1.6+ (Infrastructure as Code)
- **kubectl**: v1.28+ (Kubernetes CLI)
- **Helm**: v3.10+ (Package manager)
- **hcloud CLI**: v1.40+ (Hetzner Cloud CLI)
- **Git**: v2.30+ (Version control)
- **OpenSSL**: For SSL certificate generation

### Hetzner Cloud Account Setup
- **Account**: Active Hetzner Cloud account
- **API Token**: Generated from Hetzner Cloud Console
- **SSH Key**: Uploaded to Hetzner Cloud
- **Project**: Dedicated project for staging environment

### Verification Commands
```bash
# Verify required tools
terraform --version
kubectl version --client
helm version
hcloud version

# Verify Hetzner Cloud access
hcloud auth list
hcloud server list
```

## 🚀 Quick Start (30-Minute Deployment)

### Automated Deployment Script
```bash
# Clone repository
git clone https://github.com/culture-connect/backend.git
cd backend

# Run automated staging deployment
./scripts/deploy-staging.sh

# Monitor deployment progress
./scripts/monitor-staging-deployment.sh

# Verify deployment
curl https://staging-api.cultureconnect.ng/health
```

## 📦 Detailed Deployment Instructions

### Step 1: Infrastructure Preparation

#### 1.1 Hetzner Cloud Setup
```bash
# Install Hetzner Cloud CLI
# macOS
brew install hcloud

# Linux
wget https://github.com/hetznercloud/cli/releases/latest/download/hcloud-linux-amd64.tar.gz
tar -xzf hcloud-linux-amd64.tar.gz
sudo mv hcloud /usr/local/bin/

# Authenticate with Hetzner Cloud
hcloud auth new
# Enter your API token when prompted

# Verify authentication
hcloud context list
hcloud server list
```

#### 1.2 SSH Key Configuration
```bash
# Generate SSH key for staging (if not exists)
ssh-keygen -t ed25519 -f ~/.ssh/culture_connect_staging -C "<EMAIL>"

# Upload SSH key to Hetzner Cloud
hcloud ssh-key create --name culture-connect-staging --public-key-from-file ~/.ssh/culture_connect_staging.pub

# Verify SSH key upload
hcloud ssh-key list
```

#### 1.3 Environment Configuration
```bash
# Create Terraform variables file
cp infrastructure/terraform/hetzner/terraform.tfvars.example infrastructure/terraform/hetzner/terraform.tfvars

# Edit Terraform variables
nano infrastructure/terraform/hetzner/terraform.tfvars

# Required variables:
hcloud_token = "YOUR_HETZNER_API_TOKEN"
ssh_key_name = "culture-connect-staging"
cluster_name = "culture-connect-staging"
environment = "staging"
node_count = 2
server_type = "cx31"  # 2 vCPU, 8GB RAM
location = "nbg1"     # Nuremberg
```

### Step 2: Infrastructure Provisioning

#### 2.1 Terraform Initialization
```bash
# Navigate to Hetzner Terraform directory
cd infrastructure/terraform/hetzner

# Initialize Terraform
terraform init

# Validate configuration
terraform validate

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Review the plan output carefully
# Expected resources: ~15 resources to be created
```

#### 2.2 Infrastructure Deployment
```bash
# Apply Terraform configuration
terraform apply -var-file="terraform.tfvars"

# Type 'yes' when prompted
# Deployment time: ~10-15 minutes

# Verify infrastructure
terraform output

# Expected outputs:
# - cluster_endpoint
# - load_balancer_ip
# - kubeconfig_path
```

#### 2.3 Kubernetes Cluster Access
```bash
# Configure kubectl
export KUBECONFIG=$(terraform output -raw kubeconfig_path)

# Verify cluster access
kubectl cluster-info
kubectl get nodes

# Expected: 2 nodes in Ready state
# NAME                           STATUS   ROLES                  AGE   VERSION
# culture-connect-staging-1      Ready    control-plane,master   5m    v1.28.x
# culture-connect-staging-2      Ready    <none>                 4m    v1.28.x
```

### Step 3: Application Deployment

#### 3.1 Namespace and Secrets Setup
```bash
# Create staging namespace
kubectl create namespace culture-connect-staging

# Create secrets from environment file
kubectl create secret generic culture-connect-secrets \
  --from-env-file=environments/.env.staging \
  -n culture-connect-staging

# Create TLS secret for SSL
kubectl create secret tls staging-tls \
  --cert=certificates/staging.crt \
  --key=certificates/staging.key \
  -n culture-connect-staging
```

#### 3.2 Database Setup
```bash
# Deploy PostgreSQL
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

helm install postgresql bitnami/postgresql \
  --namespace culture-connect-staging \
  --set auth.postgresPassword=staging_postgres_password \
  --set auth.database=culture_connect_staging_db \
  --set primary.persistence.size=20Gi \
  --set primary.resources.requests.memory=1Gi \
  --set primary.resources.requests.cpu=500m

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgresql -n culture-connect-staging --timeout=300s
```

#### 3.3 Redis Cache Setup
```bash
# Deploy Redis
helm install redis bitnami/redis \
  --namespace culture-connect-staging \
  --set auth.password=staging_redis_password \
  --set master.persistence.size=8Gi \
  --set master.resources.requests.memory=512Mi \
  --set master.resources.requests.cpu=250m

# Wait for Redis to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n culture-connect-staging --timeout=300s
```

#### 3.4 Application Deployment
```bash
# Deploy application using Kustomize
kubectl apply -k infrastructure/kubernetes/overlays/staging/

# Verify deployment
kubectl get all -n culture-connect-staging

# Wait for application pods to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=culture-connect -n culture-connect-staging --timeout=600s

# Check pod status
kubectl get pods -n culture-connect-staging
```

### Step 4: DNS and SSL Configuration

#### 4.1 DNS Setup
```bash
# Get load balancer IP
LOAD_BALANCER_IP=$(kubectl get svc culture-connect-ingress -n culture-connect-staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

echo "Load Balancer IP: $LOAD_BALANCER_IP"

# Configure DNS records (manual step)
# Add A record: staging-api.cultureconnect.ng → $LOAD_BALANCER_IP
# Add A record: staging.cultureconnect.ng → $LOAD_BALANCER_IP

# Verify DNS propagation
nslookup staging-api.cultureconnect.ng
dig staging-api.cultureconnect.ng
```

#### 4.2 SSL Certificate Setup
```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Wait for cert-manager to be ready
kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s

# Apply certificate issuer
kubectl apply -f infrastructure/security/certificates/cert-manager-deployment.yaml

# Verify certificate issuance
kubectl get certificates -n culture-connect-staging
kubectl describe certificate staging-tls -n culture-connect-staging
```

### Step 5: Monitoring and Observability

#### 5.1 Prometheus and Grafana Setup
```bash
# Add Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus stack
helm install monitoring prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --set grafana.adminPassword=staging_grafana_password \
  --set prometheus.prometheusSpec.retention=7d \
  --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=20Gi

# Wait for monitoring stack to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n monitoring --timeout=600s
```

#### 5.2 Application Monitoring Configuration
```bash
# Apply ServiceMonitor for application metrics
kubectl apply -f infrastructure/kubernetes/overlays/staging/staging-monitoring-patch.yaml

# Verify metrics collection
kubectl port-forward svc/monitoring-prometheus 9090:9090 -n monitoring &
curl http://localhost:9090/api/v1/targets

# Access Grafana dashboard
kubectl port-forward svc/monitoring-grafana 3000:80 -n monitoring &
# URL: http://localhost:3000
# Username: admin
# Password: staging_grafana_password
```

## 🔍 Validation and Testing

### Health Checks
```bash
# API Health Check
curl https://staging-api.cultureconnect.ng/health
# Expected: {"status": "healthy", "environment": "staging"}

# Database Health Check
curl https://staging-api.cultureconnect.ng/health/database
# Expected: {"status": "healthy", "connection": "active"}

# Redis Health Check
curl https://staging-api.cultureconnect.ng/health/redis
# Expected: {"status": "healthy", "connection": "active"}

# Comprehensive Health Check
curl https://staging-api.cultureconnect.ng/health/detailed
```

### Performance Testing
```bash
# Install k6 for load testing
# macOS
brew install k6

# Linux
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6

# Run load test
k6 run tests/performance/staging-load-test.js

# Expected results:
# - 95th percentile response time < 500ms
# - Error rate < 1%
# - Successful requests > 99%
```

### End-to-End Testing
```bash
# Run staging E2E tests
pytest tests/e2e/staging/ -v --env=staging

# Run API integration tests
pytest tests/api/ -v --base-url=https://staging-api.cultureconnect.ng

# Run WebSocket tests
pytest tests/websocket/ -v --ws-url=wss://staging-api.cultureconnect.ng/api/v1/websocket
```

## 🔧 CI/CD Pipeline Setup

### GitHub Actions Configuration
```bash
# GitHub Secrets to configure:
# - HCLOUD_TOKEN: Hetzner Cloud API token
# - KUBECONFIG_STAGING: Base64 encoded kubeconfig
# - STAGING_ENV_FILE: Base64 encoded .env.staging file

# Verify GitHub Actions workflow
git push origin main

# Monitor deployment in GitHub Actions
# URL: https://github.com/culture-connect/backend/actions
```

### Automated Deployment Pipeline
```yaml
# .github/workflows/staging-deploy.yml
name: Deploy to Staging
on:
  push:
    branches: [develop]
  pull_request:
    branches: [main]

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Deploy to Staging
      run: ./scripts/ci-deploy-staging.sh
    - name: Run Health Checks
      run: ./scripts/staging-health-check.sh
    - name: Run E2E Tests
      run: pytest tests/e2e/staging/ -v
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Issue: Terraform Apply Fails
```bash
# Check Hetzner Cloud API token
hcloud auth list

# Verify SSH key exists
hcloud ssh-key list

# Check resource quotas
hcloud server list
hcloud load-balancer list

# Retry with debug logging
TF_LOG=DEBUG terraform apply -var-file="terraform.tfvars"
```

#### Issue: Kubernetes Pods Not Starting
```bash
# Check node resources
kubectl top nodes
kubectl describe nodes

# Check pod events
kubectl get events -n culture-connect-staging --sort-by='.lastTimestamp'

# Check pod logs
kubectl logs -l app.kubernetes.io/name=culture-connect -n culture-connect-staging

# Check resource constraints
kubectl describe pod POD_NAME -n culture-connect-staging
```

#### Issue: SSL Certificate Not Issued
```bash
# Check cert-manager logs
kubectl logs -l app=cert-manager -n cert-manager

# Check certificate status
kubectl describe certificate staging-tls -n culture-connect-staging

# Check DNS propagation
nslookup staging-api.cultureconnect.ng
dig staging-api.cultureconnect.ng

# Manual certificate verification
openssl s_client -connect staging-api.cultureconnect.ng:443 -servername staging-api.cultureconnect.ng
```

#### Issue: Database Connection Failures
```bash
# Check PostgreSQL pod status
kubectl get pods -l app.kubernetes.io/name=postgresql -n culture-connect-staging

# Check PostgreSQL logs
kubectl logs -l app.kubernetes.io/name=postgresql -n culture-connect-staging

# Test database connection
kubectl exec -it deployment/culture-connect-api -n culture-connect-staging -- python -c "
import asyncpg
import asyncio
async def test():
    conn = await asyncpg.connect('***************************************************************/culture_connect_staging_db')
    result = await conn.fetchval('SELECT version()')
    print(result)
    await conn.close()
asyncio.run(test())
"
```

#### Issue: Load Balancer Not Accessible
```bash
# Check load balancer status
kubectl get svc -n culture-connect-staging
kubectl describe svc culture-connect-ingress -n culture-connect-staging

# Check Hetzner load balancer
hcloud load-balancer list
hcloud load-balancer describe LOAD_BALANCER_ID

# Verify firewall rules
hcloud firewall list
hcloud firewall describe FIREWALL_ID
```

## 📊 Monitoring and Maintenance

### Daily Monitoring Tasks
```bash
# Check cluster health
kubectl get nodes
kubectl get pods --all-namespaces

# Check resource usage
kubectl top nodes
kubectl top pods -n culture-connect-staging

# Check application logs
kubectl logs -l app.kubernetes.io/name=culture-connect -n culture-connect-staging --tail=100

# Check certificate expiration
kubectl get certificates -n culture-connect-staging
```

### Weekly Maintenance Tasks
```bash
# Update Helm repositories
helm repo update

# Check for security updates
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[*].image}{"\n"}{end}' -n culture-connect-staging

# Backup database
kubectl exec -it postgresql-0 -n culture-connect-staging -- pg_dump -U postgres culture_connect_staging_db > staging-backup-$(date +%Y%m%d).sql

# Review monitoring alerts
kubectl port-forward svc/monitoring-grafana 3000:80 -n monitoring
```

### Performance Optimization
```bash
# Analyze resource usage
kubectl top pods -n culture-connect-staging --sort-by=memory
kubectl top pods -n culture-connect-staging --sort-by=cpu

# Scale application if needed
kubectl scale deployment culture-connect-api --replicas=3 -n culture-connect-staging

# Update resource requests/limits
kubectl patch deployment culture-connect-api -n culture-connect-staging -p '{"spec":{"template":{"spec":{"containers":[{"name":"api","resources":{"requests":{"memory":"512Mi","cpu":"250m"},"limits":{"memory":"1Gi","cpu":"500m"}}}]}}}}'
```

## 📚 Next Steps

1. **Production Deployment**: Follow `deployment/production/README.md`
2. **Monitoring Setup**: Configure alerts and dashboards
3. **Backup Strategy**: Implement automated backups
4. **Security Hardening**: Apply security policies
5. **Performance Tuning**: Optimize based on staging metrics

## 🆘 Support

- **Documentation**: `docs/` directory
- **Runbooks**: `docs/runbooks/`
- **Issues**: GitHub Issues
- **Slack**: #culture-connect-staging
- **Email**: <EMAIL>
