"""
Performance monitoring schemas for Culture Connect Backend API.

This module defines comprehensive performance monitoring and system health schemas including:
- PerformanceMetrics schemas: APM integration and system performance tracking
- SystemHealth schemas: Real-time system health monitoring and alerting
- Performance optimization schemas: Query optimization and caching strategies
- Load testing schemas: Performance testing and monitoring dashboards

Implements Phase 7.2 requirements for performance monitoring system with
production-grade Pydantic V2 validation and comprehensive error handling.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum

from app.schemas.base import BaseSchema, TimestampMixin, UUIDMixin
from app.models.analytics_models import PerformanceMetricType, SystemHealthStatus, AnalyticsTimeframe


class PerformanceMetricTypeEnum(str, Enum):
    """Performance metric types for API validation."""
    API_RESPONSE_TIME = "api_response_time"
    DATABASE_QUERY_TIME = "database_query_time"
    CACHE_HIT_RATE = "cache_hit_rate"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    DISK_USAGE = "disk_usage"
    NETWORK_LATENCY = "network_latency"
    ERROR_RATE = "error_rate"
    THROUGHPUT = "throughput"
    CONCURRENT_CONNECTIONS = "concurrent_connections"


class SystemHealthStatusEnum(str, Enum):
    """System health status levels for API validation."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DOWN = "down"
    MAINTENANCE = "maintenance"


class AnalyticsTimeframeEnum(str, Enum):
    """Analytics timeframe for API validation."""
    REAL_TIME = "real_time"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


# Performance Metrics Schemas

class PerformanceMetricsBase(BaseSchema):
    """Base schema for performance metrics."""

    metric_type: PerformanceMetricTypeEnum = Field(
        ...,
        description="Type of performance metric"
    )

    metric_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Specific metric name or identifier"
    )

    component: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="System component (api, database, cache, etc.)"
    )

    timeframe: AnalyticsTimeframeEnum = Field(
        ...,
        description="Time aggregation period"
    )

    value: Decimal = Field(
        ...,
        ge=0,
        description="Primary metric value"
    )

    min_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Minimum value in aggregation period"
    )

    max_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Maximum value in aggregation period"
    )

    avg_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Average value in aggregation period"
    )

    percentile_95: Optional[Decimal] = Field(
        None,
        ge=0,
        description="95th percentile value"
    )

    percentile_99: Optional[Decimal] = Field(
        None,
        ge=0,
        description="99th percentile value"
    )

    sample_count: int = Field(
        1,
        ge=1,
        description="Number of samples in aggregation"
    )

    error_count: int = Field(
        0,
        ge=0,
        description="Number of errors in aggregation period"
    )

    tags: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metric tags and labels"
    )

    metric_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metric metadata"
    )


class PerformanceMetricsCreate(PerformanceMetricsBase):
    """Schema for creating performance metrics."""

    timestamp: Optional[datetime] = Field(
        None,
        description="Metric collection timestamp (defaults to current time)"
    )


class PerformanceMetricsUpdate(BaseSchema):
    """Schema for updating performance metrics."""

    value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Primary metric value"
    )

    min_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Minimum value in aggregation period"
    )

    max_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Maximum value in aggregation period"
    )

    avg_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Average value in aggregation period"
    )

    percentile_95: Optional[Decimal] = Field(
        None,
        ge=0,
        description="95th percentile value"
    )

    percentile_99: Optional[Decimal] = Field(
        None,
        ge=0,
        description="99th percentile value"
    )

    sample_count: Optional[int] = Field(
        None,
        ge=1,
        description="Number of samples in aggregation"
    )

    error_count: Optional[int] = Field(
        None,
        ge=0,
        description="Number of errors in aggregation period"
    )

    tags: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metric tags and labels"
    )

    metric_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metric metadata"
    )


class PerformanceMetricsResponse(PerformanceMetricsBase, TimestampMixin, UUIDMixin):
    """Schema for performance metrics response."""

    id: int = Field(..., description="Performance metrics ID")
    timestamp: datetime = Field(..., description="Metric collection timestamp")

    model_config = ConfigDict(from_attributes=True)


# System Health Schemas

class SystemHealthBase(BaseSchema):
    """Base schema for system health."""

    component: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="System component name"
    )

    service_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Service or subsystem name"
    )

    status: SystemHealthStatusEnum = Field(
        ...,
        description="Current health status"
    )

    response_time: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Component response time in milliseconds"
    )

    uptime_percentage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Uptime percentage over monitoring period"
    )

    error_rate: Optional[Decimal] = Field(
        None,
        ge=0,
        le=1,
        description="Error rate as decimal (0.0 to 1.0)"
    )

    cpu_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="CPU usage percentage"
    )

    memory_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Memory usage percentage"
    )

    disk_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Disk usage percentage"
    )

    dependencies_healthy: bool = Field(
        True,
        description="Whether all dependencies are healthy"
    )

    alert_threshold: Optional[Dict[str, Any]] = Field(
        None,
        description="Alert threshold configuration"
    )

    health_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Detailed health information and metrics"
    )

    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Error details if status is not healthy"
    )


class SystemHealthResponse(SystemHealthBase, TimestampMixin, UUIDMixin):
    """Schema for system health response."""

    id: int = Field(..., description="System health ID")
    previous_status: Optional[SystemHealthStatusEnum] = Field(
        None,
        description="Previous health status for change tracking"
    )
    status_changed_at: Optional[datetime] = Field(
        None,
        description="When status last changed"
    )
    last_check_at: datetime = Field(..., description="Last health check timestamp")
    next_check_at: Optional[datetime] = Field(
        None,
        description="Next scheduled health check"
    )
    alert_sent: bool = Field(False, description="Whether alert has been sent for current status")
    alert_sent_at: Optional[datetime] = Field(
        None,
        description="When alert was last sent"
    )

    model_config = ConfigDict(from_attributes=True)


# Performance Query and Filter Schemas

class PerformanceMetricsFilter(BaseSchema):
    """Schema for filtering performance metrics."""

    metric_type: Optional[PerformanceMetricTypeEnum] = Field(
        None,
        description="Filter by metric type"
    )

    component: Optional[str] = Field(
        None,
        max_length=50,
        description="Filter by component"
    )

    metric_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Filter by metric name"
    )

    timeframe: Optional[AnalyticsTimeframeEnum] = Field(
        None,
        description="Filter by timeframe"
    )

    start_time: Optional[datetime] = Field(
        None,
        description="Filter metrics after this timestamp"
    )

    end_time: Optional[datetime] = Field(
        None,
        description="Filter metrics before this timestamp"
    )

    min_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Filter metrics with value >= this"
    )

    max_value: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Filter metrics with value <= this"
    )


class SystemHealthFilter(BaseSchema):
    """Schema for filtering system health records."""

    component: Optional[str] = Field(
        None,
        max_length=50,
        description="Filter by component"
    )

    service_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Filter by service name"
    )

    status: Optional[SystemHealthStatusEnum] = Field(
        None,
        description="Filter by health status"
    )

    dependencies_healthy: Optional[bool] = Field(
        None,
        description="Filter by dependency health status"
    )

    alert_sent: Optional[bool] = Field(
        None,
        description="Filter by alert status"
    )

    last_check_after: Optional[datetime] = Field(
        None,
        description="Filter records checked after this timestamp"
    )

    last_check_before: Optional[datetime] = Field(
        None,
        description="Filter records checked before this timestamp"
    )


# Performance Dashboard and Aggregation Schemas

class PerformanceOverview(BaseSchema):
    """Schema for performance overview dashboard."""

    timestamp: datetime = Field(..., description="Overview generation timestamp")

    api_performance: Dict[str, Any] = Field(
        ...,
        description="API performance metrics summary"
    )

    database_performance: Dict[str, Any] = Field(
        ...,
        description="Database performance metrics summary"
    )

    cache_performance: Dict[str, Any] = Field(
        ...,
        description="Cache performance metrics summary"
    )

    system_resources: Dict[str, Any] = Field(
        ...,
        description="System resource utilization summary"
    )

    health_summary: Dict[str, Any] = Field(
        ...,
        description="Overall system health summary"
    )

    alerts_summary: Dict[str, Any] = Field(
        ...,
        description="Active alerts and warnings summary"
    )


class PerformanceAlert(BaseSchema):
    """Schema for performance alerts."""

    alert_id: str = Field(..., description="Unique alert identifier")
    alert_type: str = Field(..., description="Type of alert")
    severity: str = Field(..., description="Alert severity level")
    component: str = Field(..., description="Affected component")
    message: str = Field(..., description="Alert message")
    threshold_value: Optional[Decimal] = Field(None, description="Threshold that was breached")
    current_value: Optional[Decimal] = Field(None, description="Current metric value")
    timestamp: datetime = Field(..., description="Alert generation timestamp")
    resolved: bool = Field(False, description="Whether alert has been resolved")
    resolved_at: Optional[datetime] = Field(None, description="Alert resolution timestamp")


class PerformanceRecommendation(BaseSchema):
    """Schema for performance optimization recommendations."""

    recommendation_id: str = Field(..., description="Unique recommendation identifier")
    category: str = Field(..., description="Recommendation category")
    priority: str = Field(..., description="Recommendation priority")
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Detailed recommendation description")
    impact: str = Field(..., description="Expected impact of implementing recommendation")
    effort: str = Field(..., description="Estimated effort to implement")
    component: str = Field(..., description="Affected component")
    metrics_affected: List[str] = Field(..., description="Metrics that would be improved")
    implementation_steps: List[str] = Field(..., description="Steps to implement recommendation")
    timestamp: datetime = Field(..., description="Recommendation generation timestamp")


# Load Testing and Monitoring Schemas

class LoadTestConfiguration(BaseSchema):
    """Schema for load test configuration."""

    test_name: str = Field(..., min_length=1, max_length=100, description="Load test name")
    target_endpoint: str = Field(..., description="Target endpoint for testing")
    concurrent_users: int = Field(..., ge=1, le=10000, description="Number of concurrent users")
    duration_seconds: int = Field(..., ge=1, le=3600, description="Test duration in seconds")
    ramp_up_seconds: int = Field(0, ge=0, description="Ramp-up time in seconds")
    request_rate: Optional[int] = Field(None, ge=1, description="Target requests per second")
    test_data: Optional[Dict[str, Any]] = Field(None, description="Test data configuration")
    headers: Optional[Dict[str, str]] = Field(None, description="Custom headers for requests")

    @field_validator('ramp_up_seconds')
    @classmethod
    def validate_ramp_up(cls, v, info):
        if info.data.get('duration_seconds') and v > info.data['duration_seconds']:
            raise ValueError('Ramp-up time cannot exceed test duration')
        return v


class LoadTestResult(BaseSchema):
    """Schema for load test results."""

    test_id: str = Field(..., description="Unique test identifier")
    test_name: str = Field(..., description="Load test name")
    start_time: datetime = Field(..., description="Test start timestamp")
    end_time: datetime = Field(..., description="Test end timestamp")
    duration_seconds: int = Field(..., description="Actual test duration")

    total_requests: int = Field(..., description="Total number of requests sent")
    successful_requests: int = Field(..., description="Number of successful requests")
    failed_requests: int = Field(..., description="Number of failed requests")

    avg_response_time: Decimal = Field(..., description="Average response time in milliseconds")
    min_response_time: Decimal = Field(..., description="Minimum response time in milliseconds")
    max_response_time: Decimal = Field(..., description="Maximum response time in milliseconds")
    percentile_95_response_time: Decimal = Field(..., description="95th percentile response time")
    percentile_99_response_time: Decimal = Field(..., description="99th percentile response time")

    requests_per_second: Decimal = Field(..., description="Average requests per second")
    error_rate: Decimal = Field(..., description="Error rate as percentage")

    status_code_distribution: Dict[str, int] = Field(..., description="Distribution of HTTP status codes")
    error_details: Optional[List[Dict[str, Any]]] = Field(None, description="Detailed error information")

    performance_grade: str = Field(..., description="Overall performance grade (A-F)")
    recommendations: List[str] = Field(..., description="Performance improvement recommendations")


class SystemHealthUpdate(BaseSchema):
    """Schema for updating system health records."""

    status: Optional[SystemHealthStatusEnum] = Field(
        None,
        description="Current health status"
    )

    response_time: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Component response time in milliseconds"
    )

    uptime_percentage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Uptime percentage over monitoring period"
    )

    error_rate: Optional[Decimal] = Field(
        None,
        ge=0,
        le=1,
        description="Error rate as decimal (0.0 to 1.0)"
    )

    cpu_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="CPU usage percentage"
    )

    memory_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Memory usage percentage"
    )

    disk_usage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Disk usage percentage"
    )

    dependencies_healthy: Optional[bool] = Field(
        None,
        description="Whether all dependencies are healthy"
    )

    last_check_at: Optional[datetime] = Field(
        None,
        description="Last health check timestamp"
    )

    next_check_at: Optional[datetime] = Field(
        None,
        description="Next scheduled health check"
    )

    alert_threshold: Optional[Dict[str, Any]] = Field(
        None,
        description="Alert threshold configuration"
    )

    health_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Detailed health information and metrics"
    )

    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="Error details if status is not healthy"
    )
