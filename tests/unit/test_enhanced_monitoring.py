"""
Unit tests for enhanced database monitoring functionality.

This module tests the enhanced database health checks and performance monitoring
integrated with the session management utilities.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.core.monitoring import <PERSON><PERSON><PERSON><PERSON>, HealthCheckResult


class TestEnhancedDatabaseMonitoring:
    """Test cases for enhanced database monitoring functionality."""
    
    @pytest.fixture
    def health_checker_instance(self):
        """Create a fresh health checker instance for testing."""
        return HealthChecker()
    
    @pytest.mark.asyncio
    async def test_enhanced_database_health_check(self, health_checker_instance):
        """Test enhanced database health check with session manager integration."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.core.monitoring.get_async_session_context') as mock_session_context:
                with patch('app.db.session.get_database_info') as mock_get_db_info:
                    with patch('app.db.session.session_manager') as mock_session_manager:
                        # Setup mocks
                        mock_session = AsyncMock()
                        mock_session_context.return_value.__aenter__.return_value = mock_session
                        mock_session_context.return_value.__aexit__.return_value = None
                        
                        # Mock database info
                        mock_get_db_info.return_value = {
                            "version": "PostgreSQL 15.0",
                            "database_size": "100 MB",
                            "active_connections": 5
                        }
                        
                        # Mock session manager stats
                        mock_session_manager.get_connection_stats.return_value = {
                            "total_connections": 10,
                            "active_connections": 3,
                            "failed_connections": 0,
                            "retry_attempts": 0
                        }
                        
                        # Mock query results
                        mock_result = MagicMock()
                        mock_result.scalar.return_value = 1
                        mock_session.execute.return_value = mock_result
                        
                        # Execute test
                        result = await health_checker_instance.check_database()
                        
                        # Assertions
                        assert isinstance(result, HealthCheckResult)
                        assert result.service == "database"
                        assert result.status in ["healthy", "degraded", "unhealthy"]
                        assert "pool_health" in result.details
                        assert "connection_stats" in result.details
                        assert "database_info" in result.details
                        assert result.response_time >= 0
    
    @pytest.mark.asyncio
    async def test_database_performance_check_success(self, health_checker_instance):
        """Test successful database performance check."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.db.session.test_database_connection') as mock_test_conn:
                with patch('app.db.session.get_database_info') as mock_get_db_info:
                    with patch('app.db.session.session_manager') as mock_session_manager:
                        with patch('app.core.monitoring.get_async_session_context') as mock_session_context:
                            # Setup mocks
                            mock_test_conn.return_value = True
                            mock_get_db_info.return_value = {"version": "PostgreSQL 15.0"}
                            mock_session_manager.get_connection_stats.return_value = {
                                "total_connections": 5,
                                "active_connections": 2,
                                "failed_connections": 0
                            }
                            
                            mock_session = AsyncMock()
                            mock_session_context.return_value.__aenter__.return_value = mock_session
                            mock_session_context.return_value.__aexit__.return_value = None
                            
                            # Mock query results
                            mock_result = MagicMock()
                            mock_result.scalar.return_value = 10
                            mock_session.execute.return_value = mock_result
                            
                            # Execute test
                            result = await health_checker_instance.check_database_performance()
                            
                            # Assertions
                            assert "timestamp" in result
                            assert result["connection_test"] is True
                            assert "query_performance" in result
                            assert "pool_metrics" in result
                            assert "database_info" in result
                            assert "health_status" in result
                            assert result["health_status"] in ["excellent", "good", "acceptable", "poor"]
    
    @pytest.mark.asyncio
    async def test_database_performance_check_connection_failure(self, health_checker_instance):
        """Test database performance check with connection failure."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.db.session.test_database_connection') as mock_test_conn:
                # Setup connection failure
                mock_test_conn.return_value = False
                
                # Execute test
                result = await health_checker_instance.check_database_performance()
                
                # Assertions
                assert "timestamp" in result
                assert result["connection_test"] is False
                assert result["health_status"] == "unknown"
    
    @pytest.mark.asyncio
    async def test_database_performance_check_database_unavailable(self, health_checker_instance):
        """Test database performance check when database is unavailable."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', False):
            # Execute test
            result = await health_checker_instance.check_database_performance()
            
            # Assertions
            assert "error" in result
            assert result["error"] == "Database libraries not available"
    
    @pytest.mark.asyncio
    async def test_database_performance_check_exception_handling(self, health_checker_instance):
        """Test database performance check exception handling."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.db.session.test_database_connection') as mock_test_conn:
                # Setup exception
                mock_test_conn.side_effect = Exception("Database connection error")
                
                # Execute test
                result = await health_checker_instance.check_database_performance()
                
                # Assertions
                assert "error" in result
                assert "Database connection error" in result["error"]
                assert result["health_status"] == "error"
    
    @pytest.mark.asyncio
    async def test_pool_health_calculation(self, health_checker_instance):
        """Test connection pool health calculation logic."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.core.monitoring.get_async_session_context') as mock_session_context:
                with patch('app.db.session.get_database_info') as mock_get_db_info:
                    with patch('app.db.session.session_manager') as mock_session_manager:
                        # Setup mocks
                        mock_session = AsyncMock()
                        mock_session_context.return_value.__aenter__.return_value = mock_session
                        mock_session_context.return_value.__aexit__.return_value = None
                        
                        mock_get_db_info.return_value = {"version": "PostgreSQL 15.0"}
                        
                        # Mock query results
                        mock_result = MagicMock()
                        mock_result.scalar.return_value = 1
                        mock_session.execute.return_value = mock_result
                        
                        # Test healthy pool (no failures)
                        mock_session_manager.get_connection_stats.return_value = {
                            "total_connections": 10,
                            "active_connections": 3,
                            "failed_connections": 0,
                            "retry_attempts": 0
                        }
                        
                        result = await health_checker_instance.check_database()
                        assert result.details["pool_health"] == "healthy"
                        
                        # Test degraded pool (10% failure rate)
                        mock_session_manager.get_connection_stats.return_value = {
                            "total_connections": 10,
                            "active_connections": 3,
                            "failed_connections": 1,  # 10% failure rate
                            "retry_attempts": 1
                        }
                        
                        result = await health_checker_instance.check_database()
                        assert result.details["pool_health"] == "degraded"
                        
                        # Test unhealthy pool (30% failure rate)
                        mock_session_manager.get_connection_stats.return_value = {
                            "total_connections": 10,
                            "active_connections": 3,
                            "failed_connections": 3,  # 30% failure rate
                            "retry_attempts": 3
                        }
                        
                        result = await health_checker_instance.check_database()
                        assert result.details["pool_health"] == "unhealthy"
    
    @pytest.mark.asyncio
    async def test_performance_health_status_calculation(self, health_checker_instance):
        """Test performance-based health status calculation."""
        with patch('app.core.monitoring.DATABASE_AVAILABLE', True):
            with patch('app.db.session.test_database_connection') as mock_test_conn:
                with patch('app.db.session.get_database_info') as mock_get_db_info:
                    with patch('app.db.session.session_manager') as mock_session_manager:
                        with patch('app.core.monitoring.get_async_session_context') as mock_session_context:
                            with patch('time.time') as mock_time:
                                # Setup mocks
                                mock_test_conn.return_value = True
                                mock_get_db_info.return_value = {"version": "PostgreSQL 15.0"}
                                mock_session_manager.get_connection_stats.return_value = {
                                    "total_connections": 5,
                                    "active_connections": 2,
                                    "failed_connections": 0
                                }
                                
                                mock_session = AsyncMock()
                                mock_session_context.return_value.__aenter__.return_value = mock_session
                                mock_session_context.return_value.__aexit__.return_value = None
                                
                                mock_result = MagicMock()
                                mock_result.scalar.return_value = 10
                                mock_session.execute.return_value = mock_result
                                
                                # Test excellent performance (< 100ms total)
                                mock_time.side_effect = [0, 0.01, 0.02, 0.03, 0.04, 0.05]  # 50ms total
                                
                                result = await health_checker_instance.check_database_performance()
                                assert result["health_status"] == "excellent"


class TestMonitoringIntegration:
    """Test cases for monitoring integration with session management."""
    
    @pytest.mark.asyncio
    async def test_monitoring_session_integration(self):
        """Test that monitoring properly integrates with session management."""
        from app.core.monitoring import health_checker
        
        # This test verifies that the monitoring module can import and use
        # the session management utilities without errors
        assert hasattr(health_checker, 'check_database')
        assert hasattr(health_checker, 'check_database_performance')
        
        # Test that the methods are callable
        assert callable(health_checker.check_database)
        assert callable(health_checker.check_database_performance)
    
    def test_health_check_result_structure(self):
        """Test HealthCheckResult structure and attributes."""
        from app.core.monitoring import HealthCheckResult
        from datetime import datetime
        
        # Create a test health check result
        result = HealthCheckResult(
            service="test_service",
            status="healthy",
            response_time=0.123,
            details={"test": "data"},
            timestamp=datetime.utcnow()
        )
        
        # Verify structure
        assert result.service == "test_service"
        assert result.status == "healthy"
        assert result.response_time == 0.123
        assert result.details == {"test": "data"}
        assert isinstance(result.timestamp, datetime)
    
    def test_monitoring_module_imports(self):
        """Test that all required monitoring components can be imported."""
        from app.core.monitoring import (
            HealthCheckResult,
            SystemMetrics,
            BusinessMetrics,
            MetricsCollector,
            HealthChecker,
            metrics_collector,
            health_checker,
            create_health_router
        )
        
        # Verify all imports are successful
        assert HealthCheckResult is not None
        assert SystemMetrics is not None
        assert BusinessMetrics is not None
        assert MetricsCollector is not None
        assert HealthChecker is not None
        assert metrics_collector is not None
        assert health_checker is not None
        assert create_health_router is not None
