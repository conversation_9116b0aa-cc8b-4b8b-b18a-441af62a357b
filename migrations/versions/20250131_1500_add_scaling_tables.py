"""Add scaling tables for horizontal scaling and load balancing

Revision ID: 20250131_1500_add_scaling_tables
Revises: 20250526_0000_optimize_analytics_indexes
Create Date: 2025-01-31 15:00:00.000000

This migration creates comprehensive scaling infrastructure tables for Phase 7.3.3:
- scaling_metrics: Real-time scaling metrics and performance indicators
- auto_scaling_policies: Auto-scaling configuration and policies
- load_balancer_configs: Load balancer configuration and health checks
- container_metrics: Container performance and resource utilization
- scaling_events: Scaling event tracking and audit trail

Implements production-grade indexing, constraints, and performance optimization
for horizontal scaling and load balancing capabilities.
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '20250131_1500_add_scaling_tables'
down_revision = 'd8b8c4817a77'
branch_labels = None
depends_on = None


def upgrade():
    """Create scaling infrastructure tables with comprehensive indexing and constraints."""

    # Create scaling_metrics table
    op.create_table(
        'scaling_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('metric_name', sa.String(100), nullable=False),
        sa.Column('metric_type', sa.String(50), nullable=False),
        sa.Column('component', sa.String(100), nullable=False),
        sa.Column('current_value', sa.Numeric(10, 4), nullable=False),
        sa.Column('threshold_value', sa.Numeric(10, 4), nullable=True),
        sa.Column('target_value', sa.Numeric(10, 4), nullable=True),
        sa.Column('cpu_utilization', sa.Numeric(5, 2), nullable=True),
        sa.Column('memory_utilization', sa.Numeric(5, 2), nullable=True),
        sa.Column('request_rate', sa.Integer, nullable=True),
        sa.Column('response_time_ms', sa.Integer, nullable=True),
        sa.Column('error_rate', sa.Numeric(5, 4), nullable=True),
        sa.Column('tags', postgresql.JSONB, nullable=True),
        sa.Column('metadata', postgresql.JSONB, nullable=True),
        sa.Column('recorded_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Check constraints for data validation
        sa.CheckConstraint('current_value >= 0', name='check_current_value_positive'),
        sa.CheckConstraint('cpu_utilization >= 0 AND cpu_utilization <= 100', name='check_cpu_utilization_range'),
        sa.CheckConstraint('memory_utilization >= 0 AND memory_utilization <= 100', name='check_memory_utilization_range'),
        sa.CheckConstraint('error_rate >= 0 AND error_rate <= 1', name='check_error_rate_range'),
    )

    # Create auto_scaling_policies table
    op.create_table(
        'auto_scaling_policies',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(200), nullable=False, unique=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('component', sa.String(100), nullable=False),
        sa.Column('min_replicas', sa.Integer, nullable=False, default=1),
        sa.Column('max_replicas', sa.Integer, nullable=False, default=10),
        sa.Column('target_cpu_utilization', sa.Integer, nullable=True),
        sa.Column('target_memory_utilization', sa.Integer, nullable=True),
        sa.Column('scale_up_threshold', sa.Numeric(5, 2), nullable=False),
        sa.Column('scale_down_threshold', sa.Numeric(5, 2), nullable=False),
        sa.Column('scale_up_cooldown_seconds', sa.Integer, nullable=False, default=300),
        sa.Column('scale_down_cooldown_seconds', sa.Integer, nullable=False, default=600),
        sa.Column('custom_metrics', postgresql.JSONB, nullable=True),
        sa.Column('scaling_triggers', postgresql.JSONB, nullable=True),
        sa.Column('is_enabled', sa.Boolean, nullable=False, default=True),
        sa.Column('last_scaling_event', sa.DateTime(timezone=True), nullable=True),
        sa.Column('configuration', postgresql.JSONB, nullable=True),
        sa.Column('tags', postgresql.JSONB, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Check constraints for business rules
        sa.CheckConstraint('min_replicas >= 1', name='check_min_replicas_positive'),
        sa.CheckConstraint('max_replicas >= min_replicas', name='check_max_replicas_valid'),
        sa.CheckConstraint('scale_up_threshold > scale_down_threshold', name='check_threshold_order'),
        sa.CheckConstraint('scale_up_cooldown_seconds >= 60', name='check_scale_up_cooldown_minimum'),
        sa.CheckConstraint('scale_down_cooldown_seconds >= 60', name='check_scale_down_cooldown_minimum'),
    )

    # Create load_balancer_configs table
    op.create_table(
        'load_balancer_configs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(200), nullable=False, unique=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('service_name', sa.String(100), nullable=False),
        sa.Column('strategy', sa.String(50), nullable=False, default='round_robin'),
        sa.Column('session_affinity', sa.Boolean, nullable=False, default=False),
        sa.Column('sticky_session_timeout', sa.Integer, nullable=True),
        sa.Column('health_check_path', sa.String(200), nullable=False, default='/health'),
        sa.Column('health_check_interval_seconds', sa.Integer, nullable=False, default=30),
        sa.Column('health_check_timeout_seconds', sa.Integer, nullable=False, default=5),
        sa.Column('health_check_retries', sa.Integer, nullable=False, default=3),
        sa.Column('ssl_enabled', sa.Boolean, nullable=False, default=True),
        sa.Column('ssl_certificate_path', sa.String(500), nullable=True),
        sa.Column('ssl_redirect', sa.Boolean, nullable=False, default=True),
        sa.Column('max_connections', sa.Integer, nullable=True),
        sa.Column('connection_timeout_seconds', sa.Integer, nullable=False, default=60),
        sa.Column('request_timeout_seconds', sa.Integer, nullable=False, default=30),
        sa.Column('upstream_servers', postgresql.JSONB, nullable=True),
        sa.Column('configuration', postgresql.JSONB, nullable=True),
        sa.Column('is_enabled', sa.Boolean, nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Check constraints for configuration validation
        sa.CheckConstraint('health_check_interval_seconds >= 10', name='check_health_check_interval_minimum'),
        sa.CheckConstraint('health_check_timeout_seconds >= 1', name='check_health_check_timeout_minimum'),
        sa.CheckConstraint('health_check_retries >= 1', name='check_health_check_retries_minimum'),
        sa.CheckConstraint('connection_timeout_seconds >= 10', name='check_connection_timeout_minimum'),
        sa.CheckConstraint('request_timeout_seconds >= 5', name='check_request_timeout_minimum'),
    )

    # Create container_metrics table
    op.create_table(
        'container_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('container_id', sa.String(100), nullable=False),
        sa.Column('pod_name', sa.String(200), nullable=False),
        sa.Column('namespace', sa.String(100), nullable=False),
        sa.Column('node_name', sa.String(200), nullable=True),
        sa.Column('cpu_usage_cores', sa.Numeric(8, 4), nullable=True),
        sa.Column('cpu_limit_cores', sa.Numeric(8, 4), nullable=True),
        sa.Column('memory_usage_bytes', sa.Integer, nullable=True),
        sa.Column('memory_limit_bytes', sa.Integer, nullable=True),
        sa.Column('request_count', sa.Integer, nullable=True, default=0),
        sa.Column('error_count', sa.Integer, nullable=True, default=0),
        sa.Column('avg_response_time_ms', sa.Integer, nullable=True),
        sa.Column('status', sa.String(50), nullable=False, default='pending'),
        sa.Column('restart_count', sa.Integer, nullable=False, default=0),
        sa.Column('network_rx_bytes', sa.Integer, nullable=True),
        sa.Column('network_tx_bytes', sa.Integer, nullable=True),
        sa.Column('labels', postgresql.JSONB, nullable=True),
        sa.Column('annotations', postgresql.JSONB, nullable=True),
        sa.Column('recorded_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Check constraints for resource validation
        sa.CheckConstraint('cpu_usage_cores >= 0', name='check_cpu_usage_positive'),
        sa.CheckConstraint('memory_usage_bytes >= 0', name='check_memory_usage_positive'),
        sa.CheckConstraint('request_count >= 0', name='check_request_count_positive'),
        sa.CheckConstraint('error_count >= 0', name='check_error_count_positive'),
        sa.CheckConstraint('restart_count >= 0', name='check_restart_count_positive'),
    )

    # Create scaling_events table
    op.create_table(
        'scaling_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('event_type', sa.String(50), nullable=False),
        sa.Column('component', sa.String(100), nullable=False),
        sa.Column('trigger_type', sa.String(50), nullable=False),
        sa.Column('scaling_direction', sa.String(20), nullable=True),
        sa.Column('previous_replicas', sa.Integer, nullable=True),
        sa.Column('target_replicas', sa.Integer, nullable=True),
        sa.Column('actual_replicas', sa.Integer, nullable=True),
        sa.Column('trigger_value', sa.Numeric(10, 4), nullable=True),
        sa.Column('threshold_value', sa.Numeric(10, 4), nullable=True),
        sa.Column('metric_name', sa.String(100), nullable=True),
        sa.Column('success', sa.Boolean, nullable=False, default=True),
        sa.Column('error_message', sa.Text, nullable=True),
        sa.Column('duration_seconds', sa.Integer, nullable=True),
        sa.Column('policy_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('metadata', postgresql.JSONB, nullable=True),
        sa.Column('context', postgresql.JSONB, nullable=True),
        sa.Column('triggered_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Foreign key constraint
        sa.ForeignKeyConstraint(['policy_id'], ['auto_scaling_policies.id'], ondelete='SET NULL'),

        # Check constraints for event validation
        sa.CheckConstraint('previous_replicas >= 0', name='check_previous_replicas_positive'),
        sa.CheckConstraint('target_replicas >= 0', name='check_target_replicas_positive'),
        sa.CheckConstraint('actual_replicas >= 0', name='check_actual_replicas_positive'),
        sa.CheckConstraint('duration_seconds >= 0', name='check_duration_positive'),
    )

    # Create comprehensive indexes for performance optimization

    # Scaling metrics indexes
    op.create_index('idx_scaling_metrics_component_recorded', 'scaling_metrics', ['component', 'recorded_at'])
    op.create_index('idx_scaling_metrics_type_name', 'scaling_metrics', ['metric_type', 'metric_name'])
    op.create_index('idx_scaling_metrics_recorded_desc', 'scaling_metrics', [sa.text('recorded_at DESC')])
    op.create_index('idx_scaling_metrics_component', 'scaling_metrics', ['component'])
    op.create_index('idx_scaling_metrics_metric_name', 'scaling_metrics', ['metric_name'])

    # Auto scaling policies indexes
    op.create_index('idx_auto_scaling_policies_component', 'auto_scaling_policies', ['component'])
    op.create_index('idx_auto_scaling_policies_enabled', 'auto_scaling_policies', ['is_enabled'])
    op.create_index('idx_auto_scaling_policies_last_event', 'auto_scaling_policies', ['last_scaling_event'])
    op.create_index('idx_auto_scaling_policies_name', 'auto_scaling_policies', ['name'])

    # Load balancer configs indexes
    op.create_index('idx_load_balancer_configs_service', 'load_balancer_configs', ['service_name'])
    op.create_index('idx_load_balancer_configs_enabled', 'load_balancer_configs', ['is_enabled'])
    op.create_index('idx_load_balancer_configs_strategy', 'load_balancer_configs', ['strategy'])
    op.create_index('idx_load_balancer_configs_name', 'load_balancer_configs', ['name'])

    # Container metrics indexes
    op.create_index('idx_container_metrics_pod_recorded', 'container_metrics', ['pod_name', 'recorded_at'])
    op.create_index('idx_container_metrics_namespace_status', 'container_metrics', ['namespace', 'status'])
    op.create_index('idx_container_metrics_recorded_desc', 'container_metrics', [sa.text('recorded_at DESC')])
    op.create_index('idx_container_metrics_container_id', 'container_metrics', ['container_id'])
    op.create_index('idx_container_metrics_namespace', 'container_metrics', ['namespace'])
    op.create_index('idx_container_metrics_pod_name', 'container_metrics', ['pod_name'])

    # Scaling events indexes
    op.create_index('idx_scaling_events_component_triggered', 'scaling_events', ['component', 'triggered_at'])
    op.create_index('idx_scaling_events_type_success', 'scaling_events', ['event_type', 'success'])
    op.create_index('idx_scaling_events_policy_triggered', 'scaling_events', ['policy_id', 'triggered_at'])
    op.create_index('idx_scaling_events_triggered_desc', 'scaling_events', [sa.text('triggered_at DESC')])
    op.create_index('idx_scaling_events_component', 'scaling_events', ['component'])
    op.create_index('idx_scaling_events_event_type', 'scaling_events', ['event_type'])
    op.create_index('idx_scaling_events_trigger_type', 'scaling_events', ['trigger_type'])

    # JSONB GIN indexes for metadata and configuration fields
    op.create_index('idx_scaling_metrics_tags_gin', 'scaling_metrics', ['tags'], postgresql_using='gin')
    op.create_index('idx_scaling_metrics_metadata_gin', 'scaling_metrics', ['metadata'], postgresql_using='gin')
    op.create_index('idx_auto_scaling_policies_custom_metrics_gin', 'auto_scaling_policies', ['custom_metrics'], postgresql_using='gin')
    op.create_index('idx_auto_scaling_policies_scaling_triggers_gin', 'auto_scaling_policies', ['scaling_triggers'], postgresql_using='gin')
    op.create_index('idx_load_balancer_configs_upstream_gin', 'load_balancer_configs', ['upstream_servers'], postgresql_using='gin')
    op.create_index('idx_load_balancer_configs_configuration_gin', 'load_balancer_configs', ['configuration'], postgresql_using='gin')
    op.create_index('idx_container_metrics_labels_gin', 'container_metrics', ['labels'], postgresql_using='gin')
    op.create_index('idx_container_metrics_annotations_gin', 'container_metrics', ['annotations'], postgresql_using='gin')
    op.create_index('idx_scaling_events_metadata_gin', 'scaling_events', ['metadata'], postgresql_using='gin')
    op.create_index('idx_scaling_events_context_gin', 'scaling_events', ['context'], postgresql_using='gin')


def downgrade():
    """Drop scaling infrastructure tables and indexes."""

    # Drop indexes first
    op.drop_index('idx_scaling_events_context_gin', 'scaling_events')
    op.drop_index('idx_scaling_events_metadata_gin', 'scaling_events')
    op.drop_index('idx_container_metrics_annotations_gin', 'container_metrics')
    op.drop_index('idx_container_metrics_labels_gin', 'container_metrics')
    op.drop_index('idx_load_balancer_configs_configuration_gin', 'load_balancer_configs')
    op.drop_index('idx_load_balancer_configs_upstream_gin', 'load_balancer_configs')
    op.drop_index('idx_auto_scaling_policies_scaling_triggers_gin', 'auto_scaling_policies')
    op.drop_index('idx_auto_scaling_policies_custom_metrics_gin', 'auto_scaling_policies')
    op.drop_index('idx_scaling_metrics_metadata_gin', 'scaling_metrics')
    op.drop_index('idx_scaling_metrics_tags_gin', 'scaling_metrics')

    op.drop_index('idx_scaling_events_trigger_type', 'scaling_events')
    op.drop_index('idx_scaling_events_event_type', 'scaling_events')
    op.drop_index('idx_scaling_events_component', 'scaling_events')
    op.drop_index('idx_scaling_events_triggered_desc', 'scaling_events')
    op.drop_index('idx_scaling_events_policy_triggered', 'scaling_events')
    op.drop_index('idx_scaling_events_type_success', 'scaling_events')
    op.drop_index('idx_scaling_events_component_triggered', 'scaling_events')

    op.drop_index('idx_container_metrics_pod_name', 'container_metrics')
    op.drop_index('idx_container_metrics_namespace', 'container_metrics')
    op.drop_index('idx_container_metrics_container_id', 'container_metrics')
    op.drop_index('idx_container_metrics_recorded_desc', 'container_metrics')
    op.drop_index('idx_container_metrics_namespace_status', 'container_metrics')
    op.drop_index('idx_container_metrics_pod_recorded', 'container_metrics')

    op.drop_index('idx_load_balancer_configs_name', 'load_balancer_configs')
    op.drop_index('idx_load_balancer_configs_strategy', 'load_balancer_configs')
    op.drop_index('idx_load_balancer_configs_enabled', 'load_balancer_configs')
    op.drop_index('idx_load_balancer_configs_service', 'load_balancer_configs')

    op.drop_index('idx_auto_scaling_policies_name', 'auto_scaling_policies')
    op.drop_index('idx_auto_scaling_policies_last_event', 'auto_scaling_policies')
    op.drop_index('idx_auto_scaling_policies_enabled', 'auto_scaling_policies')
    op.drop_index('idx_auto_scaling_policies_component', 'auto_scaling_policies')

    op.drop_index('idx_scaling_metrics_metric_name', 'scaling_metrics')
    op.drop_index('idx_scaling_metrics_component', 'scaling_metrics')
    op.drop_index('idx_scaling_metrics_recorded_desc', 'scaling_metrics')
    op.drop_index('idx_scaling_metrics_type_name', 'scaling_metrics')
    op.drop_index('idx_scaling_metrics_component_recorded', 'scaling_metrics')

    # Drop tables in reverse order (respecting foreign key dependencies)
    op.drop_table('scaling_events')
    op.drop_table('container_metrics')
    op.drop_table('load_balancer_configs')
    op.drop_table('auto_scaling_policies')
    op.drop_table('scaling_metrics')
