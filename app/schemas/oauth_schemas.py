"""
OAuth schemas for Culture Connect Backend API.

This module defines comprehensive OAuth authentication schemas including:
- OAuth provider configuration and validation
- OAuth account linking and profile data
- OAuth token management and security
- OAuth flow state management and CSRF protection

Implements Task 2.1.3 requirements for OAuth2 integration with Google and Facebook
providers, following production-grade validation standards and security practices.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator, HttpUrl
from enum import Enum

from app.schemas.base import BaseSchema, TimestampMixin


class OAuthProviderName(str, Enum):
    """Supported OAuth provider names."""
    GOOGLE = "google"
    FACEBOOK = "facebook"
    GITHUB = "github"  # Future support
    TWITTER = "twitter"  # Future support


class OAuthTokenType(str, Enum):
    """OAuth token types."""
    BEARER = "Bearer"
    MAC = "MAC"


class OAuthProviderBase(BaseSchema):
    """Base OAuth provider schema with common fields."""

    name: OAuthProviderName = Field(
        ...,
        description="OAuth provider name"
    )

    display_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Human-readable provider name"
    )

    client_id: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="OAuth client ID"
    )

    authorization_url: HttpUrl = Field(
        ...,
        description="OAuth authorization endpoint URL"
    )

    token_url: HttpUrl = Field(
        ...,
        description="OAuth token endpoint URL"
    )

    user_info_url: HttpUrl = Field(
        ...,
        description="OAuth user info endpoint URL"
    )

    scopes: List[str] = Field(
        default_factory=list,
        description="Required OAuth scopes"
    )

    is_active: bool = Field(
        default=True,
        description="Provider active status"
    )

    icon_url: Optional[HttpUrl] = Field(
        None,
        description="Provider icon URL"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Provider description"
    )


class OAuthProviderCreate(OAuthProviderBase):
    """Schema for creating OAuth provider."""

    client_secret: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="OAuth client secret (will be encrypted)"
    )

    @validator('scopes')
    def validate_scopes(cls, v):
        """Validate OAuth scopes."""
        if not v:
            return v

        # Validate scope format
        for scope in v:
            if not isinstance(scope, str) or not scope.strip():
                raise ValueError("All scopes must be non-empty strings")

        return [scope.strip() for scope in v]


class OAuthProviderUpdate(BaseSchema):
    """Schema for updating OAuth provider."""

    display_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Human-readable provider name"
    )

    client_id: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="OAuth client ID"
    )

    client_secret: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="OAuth client secret (will be encrypted)"
    )

    authorization_url: Optional[HttpUrl] = Field(
        None,
        description="OAuth authorization endpoint URL"
    )

    token_url: Optional[HttpUrl] = Field(
        None,
        description="OAuth token endpoint URL"
    )

    user_info_url: Optional[HttpUrl] = Field(
        None,
        description="OAuth user info endpoint URL"
    )

    scopes: Optional[List[str]] = Field(
        None,
        description="Required OAuth scopes"
    )

    is_active: Optional[bool] = Field(
        None,
        description="Provider active status"
    )

    icon_url: Optional[HttpUrl] = Field(
        None,
        description="Provider icon URL"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Provider description"
    )


class OAuthProviderResponse(OAuthProviderBase, TimestampMixin):
    """Schema for OAuth provider response."""

    id: int = Field(..., description="Provider ID")

    class Config:
        from_attributes = True


class OAuthAccountBase(BaseSchema):
    """Base OAuth account schema with common fields."""

    provider_user_id: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="User ID from OAuth provider"
    )

    provider_username: Optional[str] = Field(
        None,
        max_length=255,
        description="Username from OAuth provider"
    )

    provider_email: Optional[str] = Field(
        None,
        max_length=255,
        description="Email from OAuth provider"
    )

    profile_data: Optional[Dict[str, Any]] = Field(
        None,
        description="OAuth profile data from provider"
    )

    is_active: bool = Field(
        default=True,
        description="OAuth account active status"
    )

    is_verified: bool = Field(
        default=False,
        description="OAuth account verification status"
    )


class OAuthAccountCreate(OAuthAccountBase):
    """Schema for creating OAuth account."""

    user_id: int = Field(
        ...,
        description="Associated user ID"
    )

    provider_id: int = Field(
        ...,
        description="OAuth provider ID"
    )


class OAuthAccountUpdate(BaseSchema):
    """Schema for updating OAuth account."""

    provider_username: Optional[str] = Field(
        None,
        max_length=255,
        description="Username from OAuth provider"
    )

    provider_email: Optional[str] = Field(
        None,
        max_length=255,
        description="Email from OAuth provider"
    )

    profile_data: Optional[Dict[str, Any]] = Field(
        None,
        description="OAuth profile data from provider"
    )

    is_active: Optional[bool] = Field(
        None,
        description="OAuth account active status"
    )

    is_verified: Optional[bool] = Field(
        None,
        description="OAuth account verification status"
    )


class OAuthAccountResponse(OAuthAccountBase, TimestampMixin):
    """Schema for OAuth account response."""

    id: int = Field(..., description="OAuth account ID")
    user_id: int = Field(..., description="Associated user ID")
    provider_id: int = Field(..., description="OAuth provider ID")
    first_connected_at: datetime = Field(..., description="First connection timestamp")
    last_connected_at: datetime = Field(..., description="Last connection timestamp")

    # Include provider information
    provider: Optional[OAuthProviderResponse] = Field(
        None,
        description="OAuth provider information"
    )

    class Config:
        from_attributes = True


class OAuthTokenBase(BaseSchema):
    """Base OAuth token schema with common fields."""

    token_type: OAuthTokenType = Field(
        default=OAuthTokenType.BEARER,
        description="OAuth token type"
    )

    expires_at: Optional[datetime] = Field(
        None,
        description="Access token expiration timestamp"
    )

    scopes: Optional[List[str]] = Field(
        None,
        description="OAuth token scopes"
    )

    is_active: bool = Field(
        default=True,
        description="Token active status"
    )


class OAuthTokenCreate(OAuthTokenBase):
    """Schema for creating OAuth token."""

    oauth_account_id: int = Field(
        ...,
        description="Associated OAuth account ID"
    )

    access_token: str = Field(
        ...,
        min_length=1,
        description="OAuth access token (will be encrypted)"
    )

    refresh_token: Optional[str] = Field(
        None,
        description="OAuth refresh token (will be encrypted)"
    )

    expires_in_seconds: Optional[int] = Field(
        None,
        gt=0,
        description="Token expiration in seconds from now"
    )


class OAuthTokenResponse(OAuthTokenBase, TimestampMixin):
    """Schema for OAuth token response (without sensitive data)."""

    id: int = Field(..., description="OAuth token ID")
    oauth_account_id: int = Field(..., description="Associated OAuth account ID")
    is_revoked: bool = Field(..., description="Token revocation status")
    is_expired: bool = Field(..., description="Token expiration status")

    class Config:
        from_attributes = True


class OAuthStateCreate(BaseSchema):
    """Schema for creating OAuth state."""

    provider_id: int = Field(
        ...,
        description="OAuth provider ID"
    )

    redirect_uri: HttpUrl = Field(
        ...,
        description="OAuth redirect URI"
    )

    scopes: Optional[List[str]] = Field(
        None,
        description="Requested OAuth scopes"
    )

    client_ip: Optional[str] = Field(
        None,
        max_length=45,
        description="Client IP address"
    )

    user_agent: Optional[str] = Field(
        None,
        max_length=1000,
        description="Client user agent"
    )


class OAuthStateResponse(BaseSchema, TimestampMixin):
    """Schema for OAuth state response."""

    id: int = Field(..., description="OAuth state ID")
    state_token: str = Field(..., description="Unique OAuth state token")
    provider_id: int = Field(..., description="OAuth provider ID")
    redirect_uri: str = Field(..., description="OAuth redirect URI")
    scopes: Optional[List[str]] = Field(None, description="Requested OAuth scopes")
    is_used: bool = Field(..., description="State token usage status")
    expires_at: datetime = Field(..., description="State token expiration")
    is_expired: bool = Field(..., description="State token expiration status")
    is_valid: bool = Field(..., description="State token validity status")

    class Config:
        from_attributes = True


# OAuth Flow Schemas

class OAuthAuthorizationRequest(BaseSchema):
    """Schema for OAuth authorization request."""

    provider: OAuthProviderName = Field(
        ...,
        description="OAuth provider name"
    )

    redirect_uri: HttpUrl = Field(
        ...,
        description="Redirect URI after authorization"
    )

    scopes: Optional[List[str]] = Field(
        None,
        description="Requested OAuth scopes"
    )

    state: Optional[str] = Field(
        None,
        description="Custom state parameter"
    )


class OAuthAuthorizationResponse(BaseSchema):
    """Schema for OAuth authorization response."""

    authorization_url: HttpUrl = Field(
        ...,
        description="OAuth authorization URL to redirect user"
    )

    state: str = Field(
        ...,
        description="CSRF protection state token"
    )

    expires_in: int = Field(
        ...,
        description="State token expiration in seconds"
    )


class OAuthCallbackRequest(BaseSchema):
    """Schema for OAuth callback request."""

    code: str = Field(
        ...,
        min_length=1,
        description="OAuth authorization code"
    )

    state: str = Field(
        ...,
        min_length=1,
        description="OAuth state token for CSRF protection"
    )

    error: Optional[str] = Field(
        None,
        description="OAuth error code if authorization failed"
    )

    error_description: Optional[str] = Field(
        None,
        description="OAuth error description"
    )


class OAuthCallbackResponse(BaseSchema):
    """Schema for OAuth callback response."""

    access_token: str = Field(
        ...,
        description="JWT access token for the application"
    )

    refresh_token: str = Field(
        ...,
        description="JWT refresh token for the application"
    )

    token_type: str = Field(
        default="bearer",
        description="Token type"
    )

    expires_in: int = Field(
        ...,
        description="Access token expiration in seconds"
    )

    user: Dict[str, Any] = Field(
        ...,
        description="User information"
    )

    oauth_account: OAuthAccountResponse = Field(
        ...,
        description="OAuth account information"
    )

    is_new_user: bool = Field(
        ...,
        description="Whether this is a new user registration"
    )


class OAuthUserProfile(BaseSchema):
    """Schema for OAuth user profile data."""

    id: str = Field(..., description="Provider user ID")
    email: Optional[str] = Field(None, description="User email")
    name: Optional[str] = Field(None, description="User full name")
    first_name: Optional[str] = Field(None, description="User first name")
    last_name: Optional[str] = Field(None, description="User last name")
    username: Optional[str] = Field(None, description="Username")
    picture: Optional[HttpUrl] = Field(None, description="Profile picture URL")
    verified_email: Optional[bool] = Field(None, description="Email verification status")
    locale: Optional[str] = Field(None, description="User locale")

    # Provider-specific fields
    provider_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional provider-specific data"
    )


class OAuthTokenRefreshRequest(BaseSchema):
    """Schema for OAuth token refresh request."""

    oauth_account_id: int = Field(
        ...,
        description="OAuth account ID"
    )


class OAuthTokenRefreshResponse(BaseSchema):
    """Schema for OAuth token refresh response."""

    access_token: str = Field(
        ...,
        description="New OAuth access token"
    )

    refresh_token: Optional[str] = Field(
        None,
        description="New OAuth refresh token (if rotated)"
    )

    token_type: OAuthTokenType = Field(
        default=OAuthTokenType.BEARER,
        description="Token type"
    )

    expires_in: int = Field(
        ...,
        description="Token expiration in seconds"
    )

    scopes: Optional[List[str]] = Field(
        None,
        description="Token scopes"
    )


class OAuthAccountLinkRequest(BaseSchema):
    """Schema for linking OAuth account to existing user."""

    user_id: int = Field(
        ...,
        description="User ID to link OAuth account to"
    )

    oauth_account_id: int = Field(
        ...,
        description="OAuth account ID to link"
    )


class OAuthAccountUnlinkRequest(BaseSchema):
    """Schema for unlinking OAuth account."""

    oauth_account_id: int = Field(
        ...,
        description="OAuth account ID to unlink"
    )

    confirm: bool = Field(
        ...,
        description="Confirmation flag"
    )


class OAuthErrorResponse(BaseSchema):
    """Schema for OAuth error response."""

    error: str = Field(..., description="Error code")
    error_description: Optional[str] = Field(None, description="Error description")
    error_uri: Optional[HttpUrl] = Field(None, description="Error documentation URI")
    state: Optional[str] = Field(None, description="State parameter if provided")


# Export all schemas
__all__ = [
    "OAuthProviderName",
    "OAuthTokenType",
    "OAuthProviderBase",
    "OAuthProviderCreate",
    "OAuthProviderUpdate",
    "OAuthProviderResponse",
    "OAuthAccountBase",
    "OAuthAccountCreate",
    "OAuthAccountUpdate",
    "OAuthAccountResponse",
    "OAuthTokenBase",
    "OAuthTokenCreate",
    "OAuthTokenResponse",
    "OAuthStateCreate",
    "OAuthStateResponse",
    "OAuthAuthorizationRequest",
    "OAuthAuthorizationResponse",
    "OAuthCallbackRequest",
    "OAuthCallbackResponse",
    "OAuthUserProfile",
    "OAuthTokenRefreshRequest",
    "OAuthTokenRefreshResponse",
    "OAuthAccountLinkRequest",
    "OAuthAccountUnlinkRequest",
    "OAuthErrorResponse",
]
