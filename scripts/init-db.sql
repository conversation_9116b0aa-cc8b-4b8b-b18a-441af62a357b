-- Culture Connect Backend - Database Initialization Script
-- This script initializes the PostgreSQL database for all environments
-- with proper extensions, users, and basic configuration.

-- =============================================================================
-- EXTENSIONS
-- =============================================================================

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- PostGIS extension for geospatial data (if needed)
-- CREATE EXTENSION IF NOT EXISTS "postgis";

-- =============================================================================
-- FUNCTIONS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate correlation IDs
CREATE OR REPLACE FUNCTION generate_correlation_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'cc_' || EXTRACT(EPOCH FROM NOW())::BIGINT || '_' || SUBSTRING(gen_random_uuid()::TEXT FROM 1 FOR 8);
END;
$$ language 'plpgsql';

-- =============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================================================

-- Set default statistics target for better query planning
ALTER DATABASE culture_connect_dev_db SET default_statistics_target = 100;

-- Set work_mem for better sort and hash operations
ALTER DATABASE culture_connect_dev_db SET work_mem = '16MB';

-- Set maintenance_work_mem for better maintenance operations
ALTER DATABASE culture_connect_dev_db SET maintenance_work_mem = '64MB';

-- Set random_page_cost for SSD optimization
ALTER DATABASE culture_connect_dev_db SET random_page_cost = 1.1;

-- =============================================================================
-- LOGGING AND MONITORING
-- =============================================================================

-- Enable query logging for development (adjust for production)
ALTER DATABASE culture_connect_dev_db SET log_statement = 'all';
ALTER DATABASE culture_connect_dev_db SET log_min_duration_statement = 1000; -- Log queries > 1s

-- =============================================================================
-- SECURITY SETTINGS
-- =============================================================================

-- Revoke public schema privileges
REVOKE ALL ON SCHEMA public FROM PUBLIC;
GRANT USAGE ON SCHEMA public TO culture_connect_dev;
GRANT CREATE ON SCHEMA public TO culture_connect_dev;

-- =============================================================================
-- DEVELOPMENT DATA (OPTIONAL)
-- =============================================================================

-- Insert development seed data if needed
-- This section can be used to insert initial data for development

-- Example: Insert default roles
-- INSERT INTO roles (id, name, description, created_at) VALUES
-- (uuid_generate_v4(), 'admin', 'Administrator role', NOW()),
-- (uuid_generate_v4(), 'vendor', 'Vendor role', NOW()),
-- (uuid_generate_v4(), 'user', 'Regular user role', NOW())
-- ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- INDEXES FOR COMMON PATTERNS
-- =============================================================================

-- These indexes will be created by Alembic migrations, but we can prepare
-- the database for common indexing patterns

-- Example: Prepare for UUID primary keys
-- Most tables will use UUID primary keys with btree indexes

-- Example: Prepare for timestamp queries
-- Most tables will have created_at and updated_at columns

-- Example: Prepare for text search
-- Some tables will need full-text search capabilities

-- =============================================================================
-- CLEANUP AND MAINTENANCE
-- =============================================================================

-- Set up automatic vacuum and analyze
ALTER DATABASE culture_connect_dev_db SET autovacuum = on;
ALTER DATABASE culture_connect_dev_db SET autovacuum_vacuum_scale_factor = 0.1;
ALTER DATABASE culture_connect_dev_db SET autovacuum_analyze_scale_factor = 0.05;

-- =============================================================================
-- COMPLETION MESSAGE
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Culture Connect database initialization completed successfully!';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE 'Timestamp: %', NOW();
END $$;
