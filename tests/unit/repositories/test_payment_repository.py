"""
Unit tests for Payment Repository classes.

This module tests the payment repository layer functionality including:
- PaymentRepository: Core payment transaction management
- PaymentMethodRepository: User payment method management
- Performance optimization and query efficiency
- Business logic integration and validation
- Multi-provider support and geo-location filtering

Implements comprehensive test coverage for Step 5 Payment & Transaction Management System.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.payment_repository import PaymentRepository, PaymentMethodRepository
from app.models.payment import Payment, PaymentMethod, PaymentStatus, PaymentMethodType
from app.core.payment.config import PaymentProviderType
from app.repositories.base import PaginationParams, QueryResult, RepositoryError


class TestPaymentRepository:
    """Test PaymentRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def payment_repository(self, mock_db_session):
        """Create PaymentRepository instance with mock session."""
        return PaymentRepository(mock_db_session)

    @pytest.fixture
    def sample_payment_data(self):
        """Sample payment data for testing."""
        return {
            "booking_id": 123,
            "user_id": 456,
            "vendor_id": 789,
            "amount": Decimal("1000.00"),
            "currency": "NGN",
            "provider": PaymentProviderType.PAYSTACK,
            "payment_method_id": 1
        }

    @pytest.mark.asyncio
    async def test_create_payment_success(self, payment_repository, sample_payment_data):
        """Test successful payment creation."""
        # Mock the create method
        mock_payment = Payment(
            id=1,
            transaction_reference="CC-20240101-456-123",
            status=PaymentStatus.PENDING,
            **sample_payment_data
        )
        payment_repository.create = AsyncMock(return_value=mock_payment)

        # Test payment creation
        result = await payment_repository.create_payment(**sample_payment_data)

        # Assertions
        assert result.id == 1
        assert result.booking_id == 123
        assert result.user_id == 456
        assert result.vendor_id == 789
        assert result.amount == Decimal("1000.00")
        assert result.currency == "NGN"
        assert result.provider == PaymentProviderType.PAYSTACK
        assert result.status == PaymentStatus.PENDING
        assert "CC-" in result.transaction_reference

        # Verify create was called with correct data
        payment_repository.create.assert_called_once()
        call_args = payment_repository.create.call_args[0][0]
        assert call_args["booking_id"] == 123
        assert call_args["status"] == PaymentStatus.PENDING

    @pytest.mark.asyncio
    async def test_create_payment_failure(self, payment_repository, sample_payment_data):
        """Test payment creation failure handling."""
        # Mock create method to raise exception
        payment_repository.create = AsyncMock(side_effect=Exception("Database error"))

        # Test payment creation failure
        with pytest.raises(RepositoryError) as exc_info:
            await payment_repository.create_payment(**sample_payment_data)

        assert "Failed to create payment" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_payment_status_success(self, payment_repository):
        """Test successful payment status update."""
        # Mock the update method
        mock_payment = Payment(
            id=1,
            status=PaymentStatus.COMPLETED,
            provider_reference="PAY_123456",
            paid_at=datetime.now(timezone.utc)
        )
        payment_repository.update = AsyncMock(return_value=mock_payment)

        # Test status update
        result = await payment_repository.update_payment_status(
            payment_id=1,
            status=PaymentStatus.COMPLETED,
            provider_reference="PAY_123456"
        )

        # Assertions
        assert result.id == 1
        assert result.status == PaymentStatus.COMPLETED
        assert result.provider_reference == "PAY_123456"
        assert result.paid_at is not None

        # Verify update was called with correct data
        payment_repository.update.assert_called_once()
        call_args = payment_repository.update.call_args[0]
        assert call_args[0] == 1  # payment_id
        update_data = call_args[1]
        assert update_data["status"] == PaymentStatus.COMPLETED
        assert update_data["provider_reference"] == "PAY_123456"
        assert "paid_at" in update_data

    @pytest.mark.asyncio
    async def test_update_payment_status_failed(self, payment_repository):
        """Test payment status update for failed payment."""
        # Mock the update method
        mock_payment = Payment(
            id=1,
            status=PaymentStatus.FAILED,
            failure_reason="Insufficient funds",
            retry_count=1
        )
        payment_repository.update = AsyncMock(return_value=mock_payment)

        # Test failed status update
        result = await payment_repository.update_payment_status(
            payment_id=1,
            status=PaymentStatus.FAILED,
            failure_reason="Insufficient funds"
        )

        # Assertions
        assert result.status == PaymentStatus.FAILED
        assert result.failure_reason == "Insufficient funds"

        # Verify update was called with correct data
        call_args = payment_repository.update.call_args[0][1]
        assert call_args["status"] == PaymentStatus.FAILED
        assert call_args["failure_reason"] == "Insufficient funds"

    @pytest.mark.asyncio
    async def test_get_payments_by_user(self, payment_repository):
        """Test getting payments by user ID."""
        # Mock database execution
        mock_payments = [
            Payment(id=1, user_id=456, status=PaymentStatus.COMPLETED),
            Payment(id=2, user_id=456, status=PaymentStatus.PENDING)
        ]

        mock_query_result = QueryResult(
            items=mock_payments,
            total=2,
            page=1,
            size=10,
            has_next=False,
            has_previous=False
        )

        payment_repository.get_paginated = AsyncMock(return_value=mock_query_result)

        # Test getting payments by user
        result = await payment_repository.get_payments_by_user(
            user_id=456,
            status=PaymentStatus.COMPLETED
        )

        # Assertions
        assert result.total == 2
        assert len(result.items) == 2
        assert all(payment.user_id == 456 for payment in result.items)

        # Verify get_paginated was called
        payment_repository.get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_payment_by_reference(self, payment_repository):
        """Test getting payment by transaction reference."""
        # Mock database execution
        mock_payment = Payment(
            id=1,
            transaction_reference="CC-20240101-456-123",
            status=PaymentStatus.COMPLETED
        )

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_payment
        payment_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting payment by reference
        result = await payment_repository.get_payment_by_reference("CC-20240101-456-123")

        # Assertions
        assert result.id == 1
        assert result.transaction_reference == "CC-20240101-456-123"
        assert result.status == PaymentStatus.COMPLETED

        # Verify database was queried
        payment_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_financial_summary(self, payment_repository):
        """Test getting financial summary."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_payments = 10
        mock_row.total_amount = Decimal("10000.00")
        mock_row.total_platform_fees = Decimal("500.00")
        mock_row.total_provider_fees = Decimal("300.00")
        mock_row.total_net_amount = Decimal("9200.00")
        mock_row.completed_payments = 8
        mock_row.failed_payments = 1
        mock_row.pending_payments = 1

        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        payment_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting financial summary
        result = await payment_repository.get_financial_summary(
            vendor_id=789,
            currency="NGN"
        )

        # Assertions
        assert result["total_payments"] == 10
        assert result["total_amount"] == 10000.00
        assert result["total_platform_fees"] == 500.00
        assert result["total_provider_fees"] == 300.00
        assert result["total_net_amount"] == 9200.00
        assert result["completed_payments"] == 8
        assert result["failed_payments"] == 1
        assert result["pending_payments"] == 1
        assert result["success_rate"] == 80.0  # 8/10 * 100
        assert result["currency"] == "NGN"

        # Verify database was queried
        payment_repository.db.execute.assert_called_once()


class TestPaymentMethodRepository:
    """Test PaymentMethodRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def payment_method_repository(self, mock_db_session):
        """Create PaymentMethodRepository instance with mock session."""
        return PaymentMethodRepository(mock_db_session)

    @pytest.fixture
    def sample_payment_method_data(self):
        """Sample payment method data for testing."""
        return {
            "user_id": 456,
            "method_type": PaymentMethodType.CARD,
            "provider": PaymentProviderType.STRIPE,
            "display_name": "Visa ending in 1234",
            "encrypted_metadata": "encrypted_card_data"
        }

    @pytest.mark.asyncio
    async def test_create_payment_method_success(self, payment_method_repository, sample_payment_method_data):
        """Test successful payment method creation."""
        # Mock the create method
        mock_payment_method = PaymentMethod(
            id=1,
            user_id=456,
            type=PaymentMethodType.CARD,
            provider=PaymentProviderType.STRIPE,
            display_name="Visa ending in 1234",
            encrypted_metadata="encrypted_card_data",
            is_active=True,
            is_verified=False
        )
        payment_method_repository.create = AsyncMock(return_value=mock_payment_method)

        # Test payment method creation
        result = await payment_method_repository.create_payment_method(**sample_payment_method_data)

        # Assertions
        assert result.id == 1
        assert result.user_id == 456
        assert result.type == PaymentMethodType.CARD
        assert result.provider == PaymentProviderType.STRIPE
        assert result.display_name == "Visa ending in 1234"
        assert result.is_active is True
        assert result.is_verified is False

        # Verify create was called with correct data
        payment_method_repository.create.assert_called_once()
        call_args = payment_method_repository.create.call_args[0][0]
        assert call_args["user_id"] == 456
        assert call_args["is_active"] is True
        assert call_args["is_verified"] is False

    @pytest.mark.asyncio
    async def test_get_user_payment_methods(self, payment_method_repository):
        """Test getting user payment methods."""
        # Mock database execution
        mock_payment_methods = [
            PaymentMethod(id=1, user_id=456, is_active=True, is_default=True),
            PaymentMethod(id=2, user_id=456, is_active=True, is_default=False)
        ]

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_payment_methods
        payment_method_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting user payment methods
        result = await payment_method_repository.get_user_payment_methods(
            user_id=456,
            active_only=True
        )

        # Assertions
        assert len(result) == 2
        assert all(pm.user_id == 456 for pm in result)
        assert all(pm.is_active for pm in result)

        # Verify database was queried
        payment_method_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_default_payment_method(self, payment_method_repository):
        """Test setting default payment method."""
        # Mock the update method and database execution
        mock_payment_method = PaymentMethod(
            id=1,
            user_id=456,
            is_default=True
        )
        payment_method_repository.update = AsyncMock(return_value=mock_payment_method)
        payment_method_repository.db.execute = AsyncMock()

        # Test setting default payment method
        result = await payment_method_repository.set_default_payment_method(
            user_id=456,
            payment_method_id=1
        )

        # Assertions
        assert result.id == 1
        assert result.user_id == 456
        assert result.is_default is True

        # Verify database operations were called
        payment_method_repository.db.execute.assert_called_once()  # Unset other defaults
        payment_method_repository.update.assert_called_once_with(1, {"is_default": True})

    @pytest.mark.asyncio
    async def test_update_usage_tracking_success(self, payment_method_repository):
        """Test successful usage tracking update."""
        # Mock the update method
        mock_payment_method = PaymentMethod(
            id=1,
            usage_count=5,
            last_used_at=datetime.now(timezone.utc)
        )
        payment_method_repository.update = AsyncMock(return_value=mock_payment_method)

        # Test successful usage tracking
        result = await payment_method_repository.update_usage_tracking(
            payment_method_id=1,
            success=True
        )

        # Assertions
        assert result.id == 1
        assert result.usage_count == 5
        assert result.last_used_at is not None

        # Verify update was called with correct data
        call_args = payment_method_repository.update.call_args[0][1]
        assert "last_used_at" in call_args
        assert "usage_count" in call_args
        assert "failure_count" not in call_args

    @pytest.mark.asyncio
    async def test_update_usage_tracking_failure(self, payment_method_repository):
        """Test failure usage tracking update."""
        # Mock the update method
        mock_payment_method = PaymentMethod(
            id=1,
            usage_count=5,
            failure_count=2,
            last_used_at=datetime.now(timezone.utc),
            last_failure_at=datetime.now(timezone.utc)
        )
        payment_method_repository.update = AsyncMock(return_value=mock_payment_method)

        # Test failure usage tracking
        result = await payment_method_repository.update_usage_tracking(
            payment_method_id=1,
            success=False
        )

        # Assertions
        assert result.id == 1
        assert result.failure_count == 2
        assert result.last_failure_at is not None

        # Verify update was called with correct data
        call_args = payment_method_repository.update.call_args[0][1]
        assert "last_used_at" in call_args
        assert "usage_count" in call_args
        assert "failure_count" in call_args
        assert "last_failure_at" in call_args

    @pytest.mark.asyncio
    async def test_get_expired_payment_methods(self, payment_method_repository):
        """Test getting expired payment methods."""
        # Mock database execution
        current_date = datetime.now(timezone.utc)
        mock_expired_methods = [
            PaymentMethod(
                id=1,
                type=PaymentMethodType.CARD,
                expiry_year=current_date.year - 1,
                expiry_month=12,
                is_active=True
            ),
            PaymentMethod(
                id=2,
                type=PaymentMethodType.CARD,
                expiry_year=current_date.year,
                expiry_month=current_date.month - 1,
                is_active=True
            )
        ]

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_expired_methods
        payment_method_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting expired payment methods
        result = await payment_method_repository.get_expired_payment_methods(days_ahead=30)

        # Assertions
        assert len(result) == 2
        assert all(pm.type == PaymentMethodType.CARD for pm in result)
        assert all(pm.is_active for pm in result)

        # Verify database was queried
        payment_method_repository.db.execute.assert_called_once()
