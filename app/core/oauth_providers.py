"""
OAuth provider configurations for Culture Connect Backend API.

This module provides OAuth provider configurations and management for Google,
Facebook, and other OAuth providers with production-grade security settings.

Implements Task 2.1.3 requirements for OAuth2 integration with secure
provider configuration and environment-based settings.
"""

import logging
from typing import Dict, List, Optional
from pydantic import BaseModel, HttpUrl, Field

from app.core.config import settings

logger = logging.getLogger(__name__)


class OAuthProviderConfig(BaseModel):
    """OAuth provider configuration model."""
    
    name: str = Field(..., description="Provider name")
    display_name: str = Field(..., description="Human-readable provider name")
    client_id: str = Field(..., description="OAuth client ID")
    client_secret: str = Field(..., description="OAuth client secret")
    authorization_url: HttpUrl = Field(..., description="Authorization endpoint URL")
    token_url: HttpUrl = Field(..., description="Token endpoint URL")
    user_info_url: HttpUrl = Field(..., description="User info endpoint URL")
    scopes: List[str] = Field(default_factory=list, description="Default OAuth scopes")
    icon_url: Optional[HttpUrl] = Field(None, description="Provider icon URL")
    description: Optional[str] = Field(None, description="Provider description")


# Google OAuth Configuration
GOOGLE_OAUTH_CONFIG = OAuthProviderConfig(
    name="google",
    display_name="Google",
    client_id=getattr(settings, 'GOOGLE_OAUTH_CLIENT_ID', ''),
    client_secret=getattr(settings, 'GOOGLE_OAUTH_CLIENT_SECRET', ''),
    authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
    token_url="https://oauth2.googleapis.com/token",
    user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
    scopes=[
        "openid",
        "email",
        "profile"
    ],
    icon_url="https://developers.google.com/identity/images/g-logo.png",
    description="Sign in with your Google account"
)

# Facebook OAuth Configuration
FACEBOOK_OAUTH_CONFIG = OAuthProviderConfig(
    name="facebook",
    display_name="Facebook",
    client_id=getattr(settings, 'FACEBOOK_OAUTH_CLIENT_ID', ''),
    client_secret=getattr(settings, 'FACEBOOK_OAUTH_CLIENT_SECRET', ''),
    authorization_url="https://www.facebook.com/v18.0/dialog/oauth",
    token_url="https://graph.facebook.com/v18.0/oauth/access_token",
    user_info_url="https://graph.facebook.com/v18.0/me",
    scopes=[
        "email",
        "public_profile"
    ],
    icon_url="https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg",
    description="Sign in with your Facebook account"
)

# GitHub OAuth Configuration (for future use)
GITHUB_OAUTH_CONFIG = OAuthProviderConfig(
    name="github",
    display_name="GitHub",
    client_id=getattr(settings, 'GITHUB_OAUTH_CLIENT_ID', ''),
    client_secret=getattr(settings, 'GITHUB_OAUTH_CLIENT_SECRET', ''),
    authorization_url="https://github.com/login/oauth/authorize",
    token_url="https://github.com/login/oauth/access_token",
    user_info_url="https://api.github.com/user",
    scopes=[
        "user:email",
        "read:user"
    ],
    icon_url="https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
    description="Sign in with your GitHub account"
)

# Twitter OAuth Configuration (for future use)
TWITTER_OAUTH_CONFIG = OAuthProviderConfig(
    name="twitter",
    display_name="Twitter",
    client_id=getattr(settings, 'TWITTER_OAUTH_CLIENT_ID', ''),
    client_secret=getattr(settings, 'TWITTER_OAUTH_CLIENT_SECRET', ''),
    authorization_url="https://twitter.com/i/oauth2/authorize",
    token_url="https://api.twitter.com/2/oauth2/token",
    user_info_url="https://api.twitter.com/2/users/me",
    scopes=[
        "tweet.read",
        "users.read"
    ],
    icon_url="https://abs.twimg.com/icons/apple-touch-icon-192x192.png",
    description="Sign in with your Twitter account"
)


# Provider registry
OAUTH_PROVIDERS: Dict[str, OAuthProviderConfig] = {
    "google": GOOGLE_OAUTH_CONFIG,
    "facebook": FACEBOOK_OAUTH_CONFIG,
    "github": GITHUB_OAUTH_CONFIG,
    "twitter": TWITTER_OAUTH_CONFIG
}


def get_oauth_provider_config(provider_name: str) -> Optional[OAuthProviderConfig]:
    """
    Get OAuth provider configuration by name.
    
    Args:
        provider_name: OAuth provider name
        
    Returns:
        OAuthProviderConfig if found, None otherwise
    """
    config = OAUTH_PROVIDERS.get(provider_name.lower())
    
    if not config:
        logger.warning(f"OAuth provider configuration not found: {provider_name}")
        return None
    
    # Validate configuration
    if not config.client_id or not config.client_secret:
        logger.warning(f"OAuth provider {provider_name} missing client credentials")
        return None
    
    return config


def get_active_oauth_providers() -> List[OAuthProviderConfig]:
    """
    Get list of active OAuth providers with valid configurations.
    
    Returns:
        List of active OAuth provider configurations
    """
    active_providers = []
    
    for provider_name, config in OAUTH_PROVIDERS.items():
        if config.client_id and config.client_secret:
            active_providers.append(config)
        else:
            logger.debug(f"OAuth provider {provider_name} not configured (missing credentials)")
    
    return active_providers


def validate_oauth_provider_config(config: OAuthProviderConfig) -> bool:
    """
    Validate OAuth provider configuration.
    
    Args:
        config: OAuth provider configuration
        
    Returns:
        bool: True if configuration is valid
    """
    try:
        # Check required fields
        if not config.name or not config.client_id or not config.client_secret:
            return False
        
        # Check URLs are valid
        if not config.authorization_url or not config.token_url or not config.user_info_url:
            return False
        
        # Check scopes are provided
        if not config.scopes:
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"OAuth provider configuration validation failed: {str(e)}")
        return False


def get_oauth_security_settings() -> Dict[str, any]:
    """
    Get OAuth security settings and configurations.
    
    Returns:
        Dict containing OAuth security settings
    """
    return {
        "state_token_expiry_minutes": getattr(settings, 'OAUTH_STATE_EXPIRY_MINUTES', 10),
        "token_refresh_threshold_minutes": getattr(settings, 'OAUTH_TOKEN_REFRESH_THRESHOLD_MINUTES', 5),
        "max_oauth_accounts_per_user": getattr(settings, 'MAX_OAUTH_ACCOUNTS_PER_USER', 5),
        "require_email_verification": getattr(settings, 'OAUTH_REQUIRE_EMAIL_VERIFICATION', True),
        "auto_link_accounts": getattr(settings, 'OAUTH_AUTO_LINK_ACCOUNTS', True),
        "csrf_protection_enabled": True,
        "token_encryption_enabled": True,
        "audit_logging_enabled": True
    }


def get_provider_specific_settings(provider_name: str) -> Dict[str, any]:
    """
    Get provider-specific OAuth settings.
    
    Args:
        provider_name: OAuth provider name
        
    Returns:
        Dict containing provider-specific settings
    """
    base_settings = {
        "access_type": "offline",
        "prompt": "consent",
        "include_granted_scopes": True
    }
    
    if provider_name == "google":
        return {
            **base_settings,
            "hd": getattr(settings, 'GOOGLE_OAUTH_HOSTED_DOMAIN', None),  # G Suite domain restriction
            "login_hint": None,  # Pre-fill email
            "include_granted_scopes": True
        }
    elif provider_name == "facebook":
        return {
            **base_settings,
            "auth_type": "rerequest",  # Re-request declined permissions
            "display": "popup",  # Display mode
            "response_mode": "query"
        }
    elif provider_name == "github":
        return {
            **base_settings,
            "allow_signup": True,  # Allow new account creation
            "login": None  # Suggest username
        }
    elif provider_name == "twitter":
        return {
            **base_settings,
            "code_challenge_method": "S256",  # PKCE
            "state": None  # Will be generated
        }
    
    return base_settings


def get_oauth_error_messages() -> Dict[str, str]:
    """
    Get OAuth error messages for user-friendly error handling.
    
    Returns:
        Dict containing error codes and messages
    """
    return {
        "access_denied": "You denied access to your account. Please try again if you want to sign in.",
        "invalid_request": "Invalid OAuth request. Please try again.",
        "invalid_client": "OAuth application configuration error. Please contact support.",
        "invalid_grant": "Invalid authorization code. Please try signing in again.",
        "unsupported_response_type": "OAuth configuration error. Please contact support.",
        "invalid_scope": "Requested permissions are not available. Please contact support.",
        "server_error": "OAuth provider is temporarily unavailable. Please try again later.",
        "temporarily_unavailable": "OAuth provider is temporarily unavailable. Please try again later.",
        "invalid_state": "Security validation failed. Please try signing in again.",
        "expired_token": "Your session has expired. Please sign in again.",
        "revoked_token": "Your authorization has been revoked. Please sign in again.",
        "insufficient_scope": "Additional permissions are required. Please sign in again.",
        "provider_not_configured": "This sign-in method is not available. Please use a different method.",
        "account_linking_failed": "Failed to link your account. Please try again or contact support.",
        "profile_sync_failed": "Failed to sync your profile information. Some features may not work correctly."
    }


def log_oauth_security_event(
    event_type: str,
    provider_name: str,
    user_id: Optional[int] = None,
    client_ip: Optional[str] = None,
    details: Optional[Dict[str, any]] = None
) -> None:
    """
    Log OAuth security events for audit and monitoring.
    
    Args:
        event_type: Type of OAuth security event
        provider_name: OAuth provider name
        user_id: User ID if available
        client_ip: Client IP address
        details: Additional event details
    """
    log_data = {
        "event_type": event_type,
        "provider": provider_name,
        "user_id": user_id,
        "client_ip": client_ip,
        "timestamp": "datetime.utcnow().isoformat()",
        "details": details or {}
    }
    
    logger.info(
        f"OAuth security event: {event_type} for provider {provider_name}",
        extra={
            "oauth_security_event": True,
            "event_data": log_data
        }
    )


# Export commonly used items
__all__ = [
    "OAuthProviderConfig",
    "OAUTH_PROVIDERS",
    "get_oauth_provider_config",
    "get_active_oauth_providers",
    "validate_oauth_provider_config",
    "get_oauth_security_settings",
    "get_provider_specific_settings",
    "get_oauth_error_messages",
    "log_oauth_security_event"
]
