"""
Redis caching infrastructure for Culture Connect Backend API.

This module provides comprehensive Redis caching functionality including:
- CacheManager: Main cache management with TTL and compression
- OptimizationCacheService: Specialized caching for marketplace optimization data
- CacheMetricsCollector: Performance monitoring and analytics
- Cache decorators for automatic caching of function results
- Cache invalidation strategies and tag-based management

Implements Task 3.2.3 requirements for infrastructure optimization with
production-grade Redis integration and seamless integration with
existing marketplace optimization services from Task 3.2.2.
"""

import asyncio
import json
import gzip
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union, Callable, TypeVar, Generic
from functools import wraps
from uuid import UUID

import redis.asyncio as redis
from redis.asyncio import Redis
from pydantic import BaseModel

from app.core.config import settings
import logging
from app.core.logging import correlation_id
from app.schemas.infrastructure_optimization import CacheType

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CacheConfig:
    """Cache configuration constants."""

    # Default TTL values (in seconds)
    OPTIMIZATION_SCORE_TTL = 86400  # 24 hours
    MARKET_INTELLIGENCE_TTL = 3600  # 1 hour
    VENDOR_SUMMARY_TTL = 21600  # 6 hours
    PERFORMANCE_ANALYTICS_TTL = 7200  # 2 hours
    COMPETITIVE_ANALYSIS_TTL = 14400  # 4 hours
    SEO_ANALYSIS_TTL = 43200  # 12 hours
    MOBILE_OPTIMIZATION_TTL = 28800  # 8 hours

    # Cache key prefixes
    OPTIMIZATION_PREFIX = "opt"
    MARKET_PREFIX = "market"
    VENDOR_PREFIX = "vendor"
    PERFORMANCE_PREFIX = "perf"
    COMPETITIVE_PREFIX = "comp"
    SEO_PREFIX = "seo"
    MOBILE_PREFIX = "mobile"

    # Compression settings
    COMPRESSION_THRESHOLD = 1024  # Compress data larger than 1KB
    MAX_KEY_LENGTH = 250
    MAX_VALUE_SIZE = 10 * 1024 * 1024  # 10MB


class CacheMetricsCollector:
    """Collects cache performance metrics."""

    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.operations = 0
        self.total_response_time = 0.0
        self.max_response_time = 0.0
        self.start_time = datetime.utcnow()

    def record_hit(self, response_time: float):
        """Record cache hit."""
        self.hits += 1
        self.operations += 1
        self._update_response_time(response_time)

    def record_miss(self, response_time: float):
        """Record cache miss."""
        self.misses += 1
        self.operations += 1
        self._update_response_time(response_time)

    def _update_response_time(self, response_time: float):
        """Update response time metrics."""
        self.total_response_time += response_time
        self.max_response_time = max(self.max_response_time, response_time)

    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        if self.operations == 0:
            return 0.0
        return (self.hits / self.operations) * 100

    @property
    def avg_response_time(self) -> float:
        """Calculate average response time."""
        if self.operations == 0:
            return 0.0
        return self.total_response_time / self.operations

    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        return {
            "hits": self.hits,
            "misses": self.misses,
            "operations": self.operations,
            "hit_rate": self.hit_rate,
            "avg_response_time_ms": self.avg_response_time * 1000,
            "max_response_time_ms": self.max_response_time * 1000,
            "uptime_seconds": (datetime.utcnow() - self.start_time).total_seconds()
        }


class CacheManager:
    """Main Redis cache manager with compression and metrics."""

    def __init__(self, redis_client: Optional[Redis] = None):
        self.redis = redis_client
        self.metrics = CacheMetricsCollector()
        self._connection_pool = None

    async def initialize(self):
        """Initialize Redis connection."""
        if not self.redis:
            try:
                self.redis = redis.from_url(
                    settings.REDIS_URL,
                    encoding="utf-8",
                    decode_responses=False,  # We handle encoding manually for compression
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )

                # Test connection
                await self.redis.ping()
                logger.info("Redis cache manager initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize Redis cache manager: {str(e)}")
                raise

    async def close(self):
        """Close Redis connection."""
        if self.redis:
            await self.redis.close()

    def _generate_key(self, prefix: str, identifier: str, **kwargs) -> str:
        """Generate cache key with optional parameters."""
        key_parts = [prefix, identifier]

        # Add sorted kwargs for consistent key generation
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_suffix = "_".join(f"{k}:{v}" for k, v in sorted_kwargs)
            key_parts.append(key_suffix)

        key = ":".join(key_parts)

        # Ensure key length doesn't exceed Redis limits
        if len(key) > CacheConfig.MAX_KEY_LENGTH:
            # Use hash for long keys
            key_hash = hashlib.md5(key.encode()).hexdigest()
            key = f"{prefix}:hash:{key_hash}"

        return key

    def _compress_data(self, data: bytes) -> bytes:
        """Compress data if it exceeds threshold."""
        if len(data) > CacheConfig.COMPRESSION_THRESHOLD:
            return gzip.compress(data)
        return data

    def _decompress_data(self, data: bytes) -> bytes:
        """Decompress data if it was compressed."""
        try:
            # Try to decompress - if it fails, data wasn't compressed
            return gzip.decompress(data)
        except gzip.BadGzipFile:
            return data

    def _serialize_value(self, value: Any) -> bytes:
        """Serialize and optionally compress value."""
        if isinstance(value, (str, int, float, bool)):
            serialized = json.dumps(value).encode('utf-8')
        elif isinstance(value, BaseModel):
            serialized = value.model_dump_json().encode('utf-8')
        else:
            serialized = json.dumps(value, default=str).encode('utf-8')

        # Check size limit
        if len(serialized) > CacheConfig.MAX_VALUE_SIZE:
            raise ValueError(f"Value too large for cache: {len(serialized)} bytes")

        return self._compress_data(serialized)

    def _deserialize_value(self, data: bytes) -> Any:
        """Decompress and deserialize value."""
        decompressed = self._decompress_data(data)
        return json.loads(decompressed.decode('utf-8'))

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        start_time = asyncio.get_event_loop().time()

        try:
            if not self.redis:
                await self.initialize()

            data = await self.redis.get(key)
            response_time = asyncio.get_event_loop().time() - start_time

            if data is None:
                self.metrics.record_miss(response_time)
                logger.debug(f"Cache miss for key: {key}", extra={"correlation_id": correlation_id.get()})
                return None

            self.metrics.record_hit(response_time)
            logger.debug(f"Cache hit for key: {key}", extra={"correlation_id": correlation_id.get()})

            return self._deserialize_value(data)

        except Exception as e:
            response_time = asyncio.get_event_loop().time() - start_time
            self.metrics.record_miss(response_time)
            logger.error(f"Cache get error for key {key}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with optional TTL."""
        try:
            if not self.redis:
                await self.initialize()

            serialized_value = self._serialize_value(value)

            if ttl:
                await self.redis.setex(key, ttl, serialized_value)
            else:
                await self.redis.set(key, serialized_value)

            logger.debug(f"Cache set for key: {key} (TTL: {ttl})", extra={"correlation_id": correlation_id.get()})
            return True

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return False

    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            if not self.redis:
                await self.initialize()

            result = await self.redis.delete(key)
            logger.debug(f"Cache delete for key: {key}", extra={"correlation_id": correlation_id.get()})
            return result > 0

        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return False

    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern."""
        try:
            if not self.redis:
                await self.initialize()

            keys = await self.redis.keys(pattern)
            if keys:
                deleted = await self.redis.delete(*keys)
                logger.info(f"Cache pattern delete: {pattern} ({deleted} keys)", extra={"correlation_id": correlation_id.get()})
                return deleted
            return 0

        except Exception as e:
            logger.error(f"Cache pattern delete error for pattern {pattern}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return 0

    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            if not self.redis:
                await self.initialize()

            result = await self.redis.exists(key)
            return result > 0

        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return False

    async def get_ttl(self, key: str) -> Optional[int]:
        """Get TTL for key."""
        try:
            if not self.redis:
                await self.initialize()

            ttl = await self.redis.ttl(key)
            return ttl if ttl > 0 else None

        except Exception as e:
            logger.error(f"Cache TTL error for key {key}: {str(e)}", extra={"correlation_id": correlation_id.get()})
            return None

    def get_metrics(self) -> Dict[str, Any]:
        """Get cache performance metrics."""
        return self.metrics.get_metrics()


class OptimizationCacheService:
    """Specialized cache service for marketplace optimization data."""

    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager

    async def get_optimization_score(self, service_id: UUID) -> Optional[Dict[str, Any]]:
        """Get cached optimization score for service."""
        key = self.cache._generate_key(CacheConfig.OPTIMIZATION_PREFIX, str(service_id))
        return await self.cache.get(key)

    async def set_optimization_score(self, service_id: UUID, score_data: Dict[str, Any]) -> bool:
        """Cache optimization score for service."""
        key = self.cache._generate_key(CacheConfig.OPTIMIZATION_PREFIX, str(service_id))
        return await self.cache.set(key, score_data, CacheConfig.OPTIMIZATION_SCORE_TTL)

    async def get_market_intelligence(self, category: str, location: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get cached market intelligence data."""
        key = self.cache._generate_key(
            CacheConfig.MARKET_PREFIX,
            category,
            location=location or "global"
        )
        return await self.cache.get(key)

    async def set_market_intelligence(self, category: str, data: Dict[str, Any], location: Optional[str] = None) -> bool:
        """Cache market intelligence data."""
        key = self.cache._generate_key(
            CacheConfig.MARKET_PREFIX,
            category,
            location=location or "global"
        )
        return await self.cache.set(key, data, CacheConfig.MARKET_INTELLIGENCE_TTL)

    async def get_vendor_summary(self, vendor_id: UUID) -> Optional[Dict[str, Any]]:
        """Get cached vendor optimization summary."""
        key = self.cache._generate_key(CacheConfig.VENDOR_PREFIX, str(vendor_id))
        return await self.cache.get(key)

    async def set_vendor_summary(self, vendor_id: UUID, summary_data: Dict[str, Any]) -> bool:
        """Cache vendor optimization summary."""
        key = self.cache._generate_key(CacheConfig.VENDOR_PREFIX, str(vendor_id))
        return await self.cache.set(key, summary_data, CacheConfig.VENDOR_SUMMARY_TTL)

    async def invalidate_service_cache(self, service_id: UUID) -> bool:
        """Invalidate all cache entries for a service."""
        patterns = [
            f"{CacheConfig.OPTIMIZATION_PREFIX}:{service_id}*",
            f"{CacheConfig.PERFORMANCE_PREFIX}:{service_id}*",
            f"{CacheConfig.COMPETITIVE_PREFIX}:{service_id}*",
            f"{CacheConfig.SEO_PREFIX}:{service_id}*",
            f"{CacheConfig.MOBILE_PREFIX}:{service_id}*"
        ]

        total_deleted = 0
        for pattern in patterns:
            deleted = await self.cache.delete_pattern(pattern)
            total_deleted += deleted

        logger.info(f"Invalidated {total_deleted} cache entries for service {service_id}")
        return total_deleted > 0

    async def invalidate_vendor_cache(self, vendor_id: UUID) -> bool:
        """Invalidate all cache entries for a vendor."""
        pattern = f"{CacheConfig.VENDOR_PREFIX}:{vendor_id}*"
        deleted = await self.cache.delete_pattern(pattern)

        logger.info(f"Invalidated {deleted} cache entries for vendor {vendor_id}")
        return deleted > 0


# Global cache manager instance
cache_manager = CacheManager()
optimization_cache = OptimizationCacheService(cache_manager)


def cache_result(
    cache_type: CacheType,
    ttl: Optional[int] = None,
    key_generator: Optional[Callable] = None
):
    """
    Decorator for caching function results.

    Args:
        cache_type: Type of cache data
        ttl: Time to live in seconds
        key_generator: Custom key generation function
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            # Generate cache key
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                if args:
                    key_parts.extend(str(arg) for arg in args)
                if kwargs:
                    key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
                cache_key = cache_manager._generate_key(cache_type.value, "_".join(key_parts))

            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = await func(*args, **kwargs)

            # Determine TTL based on cache type
            if ttl is None:
                ttl_map = {
                    CacheType.OPTIMIZATION_SCORE: CacheConfig.OPTIMIZATION_SCORE_TTL,
                    CacheType.MARKET_INTELLIGENCE: CacheConfig.MARKET_INTELLIGENCE_TTL,
                    CacheType.VENDOR_SUMMARY: CacheConfig.VENDOR_SUMMARY_TTL,
                    CacheType.PERFORMANCE_ANALYTICS: CacheConfig.PERFORMANCE_ANALYTICS_TTL,
                    CacheType.COMPETITIVE_ANALYSIS: CacheConfig.COMPETITIVE_ANALYSIS_TTL,
                    CacheType.SEO_ANALYSIS: CacheConfig.SEO_ANALYSIS_TTL,
                    CacheType.MOBILE_OPTIMIZATION: CacheConfig.MOBILE_OPTIMIZATION_TTL,
                }
                cache_ttl = ttl_map.get(cache_type, 3600)  # Default 1 hour
            else:
                cache_ttl = ttl

            await cache_manager.set(cache_key, result, cache_ttl)
            return result

        return wrapper
    return decorator


async def get_cache_manager() -> CacheManager:
    """Get initialized cache manager."""
    if not cache_manager.redis:
        await cache_manager.initialize()
    return cache_manager


async def get_optimization_cache() -> OptimizationCacheService:
    """Get optimization cache service."""
    if not optimization_cache.cache.redis:
        await optimization_cache.cache.initialize()
    return optimization_cache
