#!/bin/bash

# Culture Connect Backend - Production Deployment Script
# Automated production deployment with health checks and rollback capability

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${1:-production}"
VERSION="${2:-latest}"
ROLLBACK_VERSION="${3:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -f /tmp/culture-connect-deploy-*.tmp
}

# Set trap for cleanup
trap cleanup EXIT

# Validate environment
validate_environment() {
    log_info "Validating deployment environment: $DEPLOYMENT_ENV"
    
    case $DEPLOYMENT_ENV in
        production|staging|development)
            log_success "Environment validation passed: $DEPLOYMENT_ENV"
            ;;
        *)
            error_exit "Invalid environment: $DEPLOYMENT_ENV. Must be production, staging, or development"
            ;;
    esac
    
    # Check required files
    local required_files=(
        "docker/production/docker-compose.prod.yml"
        "environments/$DEPLOYMENT_ENV/.env.$DEPLOYMENT_ENV"
        "Dockerfile"
        "requirements.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$PROJECT_ROOT/$file" ]]; then
            error_exit "Required file not found: $file"
        fi
    done
    
    log_success "All required files found"
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error_exit "Docker is not installed or not in PATH"
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose is not installed or not in PATH"
    fi
    
    # Check disk space (minimum 5GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local min_space=5242880  # 5GB in KB
    
    if [[ $available_space -lt $min_space ]]; then
        error_exit "Insufficient disk space. Available: $(($available_space/1024/1024))GB, Required: 5GB"
    fi
    
    # Check memory (minimum 2GB)
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    local min_memory=2048  # 2GB in MB
    
    if [[ $available_memory -lt $min_memory ]]; then
        log_warning "Low available memory: ${available_memory}MB. Recommended: 2GB+"
    fi
    
    log_success "Pre-deployment checks passed"
}

# Build Docker images
build_images() {
    log_info "Building Docker images for version: $VERSION"
    
    cd "$PROJECT_ROOT"
    
    # Build with build arguments
    docker build \
        --target production \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
        --build-arg VERSION="$VERSION" \
        --tag "culture-connect-api:$VERSION" \
        --tag "culture-connect-api:latest" \
        .
    
    if [[ $? -eq 0 ]]; then
        log_success "Docker image built successfully: culture-connect-api:$VERSION"
    else
        error_exit "Failed to build Docker image"
    fi
    
    # Verify image size
    local image_size=$(docker images culture-connect-api:$VERSION --format "table {{.Size}}" | tail -n 1)
    log_info "Image size: $image_size"
    
    # Security scan (if available)
    if command -v docker scan &> /dev/null; then
        log_info "Running security scan..."
        docker scan culture-connect-api:$VERSION || log_warning "Security scan failed or found vulnerabilities"
    fi
}

# Database migration
run_migrations() {
    log_info "Running database migrations..."
    
    # Create temporary container for migrations
    local migration_container="culture-connect-migration-$(date +%s)"
    
    docker run --rm \
        --name "$migration_container" \
        --env-file "$PROJECT_ROOT/.env.$DEPLOYMENT_ENV" \
        --network culture_connect_prod_network \
        "culture-connect-api:$VERSION" \
        alembic upgrade head
    
    if [[ $? -eq 0 ]]; then
        log_success "Database migrations completed successfully"
    else
        error_exit "Database migration failed"
    fi
}

# Deploy services
deploy_services() {
    log_info "Deploying services for environment: $DEPLOYMENT_ENV"
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables
    export CC_VERSION="$VERSION"
    export CC_ENVIRONMENT="$DEPLOYMENT_ENV"
    
    # Deploy with Docker Compose
    docker-compose -f "docker/production/docker-compose.prod.yml" --env-file "environments/$DEPLOYMENT_ENV/.env.$DEPLOYMENT_ENV" up -d
    
    if [[ $? -eq 0 ]]; then
        log_success "Services deployed successfully"
    else
        error_exit "Service deployment failed"
    fi
}

# Health checks
health_checks() {
    log_info "Running health checks..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:8000/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts"
        
        if curl -f -s "$health_url" > /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    error_exit "Health checks failed after $max_attempts attempts"
}

# Smoke tests
smoke_tests() {
    log_info "Running smoke tests..."
    
    local api_base="http://localhost:8000"
    local test_endpoints=(
        "/health"
        "/health/db"
        "/health/redis"
        "/docs"
    )
    
    for endpoint in "${test_endpoints[@]}"; do
        log_info "Testing endpoint: $endpoint"
        
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$api_base$endpoint")
        
        if [[ $response_code -eq 200 ]]; then
            log_success "Endpoint $endpoint: OK ($response_code)"
        else
            log_error "Endpoint $endpoint: FAILED ($response_code)"
            return 1
        fi
    done
    
    log_success "All smoke tests passed"
}

# Rollback function
rollback() {
    if [[ -z "$ROLLBACK_VERSION" ]]; then
        error_exit "No rollback version specified"
    fi
    
    log_warning "Rolling back to version: $ROLLBACK_VERSION"
    
    export CC_VERSION="$ROLLBACK_VERSION"
    
    cd "$PROJECT_ROOT"
    docker-compose -f "docker/production/docker-compose.prod.yml" --env-file "environments/$DEPLOYMENT_ENV/.env.$DEPLOYMENT_ENV" up -d
    
    if [[ $? -eq 0 ]]; then
        log_success "Rollback completed successfully"
    else
        error_exit "Rollback failed"
    fi
}

# Post-deployment tasks
post_deployment() {
    log_info "Running post-deployment tasks..."
    
    # Clean up old Docker images
    log_info "Cleaning up old Docker images..."
    docker image prune -f
    
    # Log deployment
    local deployment_log="/var/log/culture-connect-deployments.log"
    echo "$(date -u +'%Y-%m-%d %H:%M:%S UTC') - Deployed version $VERSION to $DEPLOYMENT_ENV" >> "$deployment_log"
    
    # Send notification (if configured)
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"Culture Connect Backend deployed successfully to $DEPLOYMENT_ENV (version: $VERSION)\"}" \
            "$SLACK_WEBHOOK_URL" || log_warning "Failed to send Slack notification"
    fi
    
    log_success "Post-deployment tasks completed"
}

# Main deployment function
main() {
    log_info "Starting Culture Connect Backend deployment"
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
    
    validate_environment
    pre_deployment_checks
    build_images
    
    # Backup current state before deployment
    if [[ "$DEPLOYMENT_ENV" == "production" ]]; then
        log_info "Creating backup before production deployment..."
        # Add backup logic here
    fi
    
    deploy_services
    run_migrations
    health_checks
    smoke_tests
    post_deployment
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at: http://localhost:8000"
    log_info "API documentation: http://localhost:8000/docs"
}

# Handle script arguments
case "${1:-deploy}" in
    deploy)
        main
        ;;
    rollback)
        if [[ -z "$ROLLBACK_VERSION" ]]; then
            error_exit "Rollback version required: $0 rollback <current_version> <rollback_version>"
        fi
        rollback
        ;;
    health)
        health_checks
        ;;
    smoke)
        smoke_tests
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health|smoke} [environment] [version] [rollback_version]"
        echo "  deploy   - Full deployment (default)"
        echo "  rollback - Rollback to previous version"
        echo "  health   - Run health checks only"
        echo "  smoke    - Run smoke tests only"
        exit 1
        ;;
esac
