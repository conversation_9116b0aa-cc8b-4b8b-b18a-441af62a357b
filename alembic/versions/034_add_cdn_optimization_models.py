"""Add CDN optimization models

Revision ID: 034_add_cdn_optimization_models
Revises: 033_add_scaling_models
Create Date: 2025-01-31 17:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '034_add_cdn_optimization_models'
down_revision = '033_add_scaling_models'
branch_labels = None
depends_on = None


def upgrade():
    """Create CDN optimization tables with comprehensive indexing and constraints."""
    
    # Create CDN configurations table
    op.create_table('cdn_configurations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('base_url', sa.String(length=255), nullable=False),
        sa.Column('api_key_encrypted', sa.Text(), nullable=True),
        sa.Column('cache_ttl_seconds', sa.Integer(), nullable=False),
        sa.Column('compression_enabled', sa.<PERSON>an(), nullable=True),
        sa.Column('minification_enabled', sa.Boolean(), nullable=True),
        sa.Column('bundling_enabled', sa.Boolean(), nullable=True),
        sa.Column('image_optimization_enabled', sa.Boolean(), nullable=True),
        sa.Column('max_file_size_mb', sa.DECIMAL(precision=10, scale=2), nullable=True),
        sa.Column('optimization_quality', sa.Integer(), nullable=True),
        sa.Column('supported_formats', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('cache_headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('edge_locations', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('geo_restrictions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.CheckConstraint('cache_ttl_seconds > 0', name='ck_cdn_config_ttl_positive'),
        sa.CheckConstraint('max_file_size_mb > 0', name='ck_cdn_config_size_positive'),
        sa.CheckConstraint('optimization_quality >= 1 AND optimization_quality <= 100', name='ck_cdn_config_quality_valid'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name', 'provider', name='uq_cdn_config_name_provider')
    )
    
    # Create asset optimizations table
    op.create_table('asset_optimizations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cdn_configuration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('original_path', sa.String(length=500), nullable=False),
        sa.Column('optimized_path', sa.String(length=500), nullable=False),
        sa.Column('asset_type', sa.String(length=50), nullable=False),
        sa.Column('optimization_type', sa.String(length=50), nullable=False),
        sa.Column('original_size_bytes', sa.Integer(), nullable=False),
        sa.Column('optimized_size_bytes', sa.Integer(), nullable=False),
        sa.Column('compression_ratio', sa.DECIMAL(precision=5, scale=2), nullable=False),
        sa.Column('optimization_time_ms', sa.Integer(), nullable=False),
        sa.Column('cdn_url', sa.String(length=500), nullable=True),
        sa.Column('cache_headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('delivery_status', sa.String(length=50), nullable=False),
        sa.Column('first_byte_time_ms', sa.Integer(), nullable=True),
        sa.Column('total_download_time_ms', sa.Integer(), nullable=True),
        sa.Column('cache_hit_count', sa.Integer(), nullable=True),
        sa.Column('cache_miss_count', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True),
        sa.Column('last_error_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('original_size_bytes >= 0', name='ck_asset_opt_original_size_positive'),
        sa.CheckConstraint('optimized_size_bytes >= 0', name='ck_asset_opt_optimized_size_positive'),
        sa.CheckConstraint('compression_ratio >= 0', name='ck_asset_opt_compression_positive'),
        sa.CheckConstraint('optimization_time_ms >= 0', name='ck_asset_opt_time_positive'),
        sa.CheckConstraint('retry_count >= 0', name='ck_asset_opt_retry_positive'),
        sa.ForeignKeyConstraint(['cdn_configuration_id'], ['cdn_configurations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create asset bundles table
    op.create_table('asset_bundles',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cdn_configuration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('bundle_name', sa.String(length=100), nullable=False),
        sa.Column('bundle_type', sa.String(length=50), nullable=False),
        sa.Column('asset_paths', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('bundled_path', sa.String(length=500), nullable=False),
        sa.Column('total_original_size_bytes', sa.Integer(), nullable=False),
        sa.Column('bundled_size_bytes', sa.Integer(), nullable=False),
        sa.Column('compression_ratio', sa.DECIMAL(precision=5, scale=2), nullable=False),
        sa.Column('bundling_time_ms', sa.Integer(), nullable=False),
        sa.Column('cdn_url', sa.String(length=500), nullable=True),
        sa.Column('cache_headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('delivery_status', sa.String(length=50), nullable=False),
        sa.Column('first_byte_time_ms', sa.Integer(), nullable=True),
        sa.Column('total_download_time_ms', sa.Integer(), nullable=True),
        sa.Column('cache_hit_count', sa.Integer(), nullable=True),
        sa.Column('cache_miss_count', sa.Integer(), nullable=True),
        sa.Column('minification_enabled', sa.Boolean(), nullable=True),
        sa.Column('source_map_enabled', sa.Boolean(), nullable=True),
        sa.Column('bundle_version', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('total_original_size_bytes >= 0', name='ck_asset_bundle_original_size_positive'),
        sa.CheckConstraint('bundled_size_bytes >= 0', name='ck_asset_bundle_bundled_size_positive'),
        sa.CheckConstraint('compression_ratio >= 0', name='ck_asset_bundle_compression_positive'),
        sa.CheckConstraint('bundling_time_ms >= 0', name='ck_asset_bundle_time_positive'),
        sa.ForeignKeyConstraint(['cdn_configuration_id'], ['cdn_configurations.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('bundle_name', 'bundle_version', name='uq_asset_bundle_name_version')
    )
    
    # Create CDN metrics table
    op.create_table('cdn_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cdn_configuration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
        sa.Column('total_requests', sa.Integer(), nullable=False),
        sa.Column('cache_hits', sa.Integer(), nullable=False),
        sa.Column('cache_misses', sa.Integer(), nullable=False),
        sa.Column('cache_hit_rate', sa.DECIMAL(precision=5, scale=4), nullable=False),
        sa.Column('avg_response_time_ms', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('avg_first_byte_time_ms', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('avg_download_time_ms', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('total_bytes_served', sa.Integer(), nullable=False),
        sa.Column('total_bytes_saved', sa.Integer(), nullable=False),
        sa.Column('bandwidth_savings_ratio', sa.DECIMAL(precision=5, scale=4), nullable=False),
        sa.Column('error_count', sa.Integer(), nullable=False),
        sa.Column('error_rate', sa.DECIMAL(precision=5, scale=4), nullable=False),
        sa.Column('top_regions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('edge_performance', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('recorded_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.CheckConstraint('total_requests >= 0', name='ck_cdn_metrics_requests_positive'),
        sa.CheckConstraint('cache_hits >= 0', name='ck_cdn_metrics_hits_positive'),
        sa.CheckConstraint('cache_misses >= 0', name='ck_cdn_metrics_misses_positive'),
        sa.CheckConstraint('cache_hit_rate >= 0 AND cache_hit_rate <= 1', name='ck_cdn_metrics_hit_rate_valid'),
        sa.CheckConstraint('error_rate >= 0 AND error_rate <= 1', name='ck_cdn_metrics_error_rate_valid'),
        sa.CheckConstraint('period_end > period_start', name='ck_cdn_metrics_period_valid'),
        sa.ForeignKeyConstraint(['cdn_configuration_id'], ['cdn_configurations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create asset deliveries table
    op.create_table('asset_deliveries',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('asset_optimization_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('request_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('referer', sa.String(length=500), nullable=True),
        sa.Column('request_headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('country_code', sa.String(length=2), nullable=True),
        sa.Column('region_code', sa.String(length=10), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=True),
        sa.Column('edge_location', sa.String(length=50), nullable=True),
        sa.Column('response_time_ms', sa.Integer(), nullable=False),
        sa.Column('first_byte_time_ms', sa.Integer(), nullable=False),
        sa.Column('download_time_ms', sa.Integer(), nullable=False),
        sa.Column('bytes_transferred', sa.Integer(), nullable=False),
        sa.Column('cache_status', sa.String(length=20), nullable=True),
        sa.Column('cache_age_seconds', sa.Integer(), nullable=True),
        sa.Column('http_status_code', sa.Integer(), nullable=False),
        sa.Column('response_headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('delivered_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.CheckConstraint('response_time_ms >= 0', name='ck_asset_delivery_response_time_positive'),
        sa.CheckConstraint('first_byte_time_ms >= 0', name='ck_asset_delivery_first_byte_positive'),
        sa.CheckConstraint('download_time_ms >= 0', name='ck_asset_delivery_download_time_positive'),
        sa.CheckConstraint('bytes_transferred >= 0', name='ck_asset_delivery_bytes_positive'),
        sa.CheckConstraint('http_status_code >= 100 AND http_status_code < 600', name='ck_asset_delivery_status_valid'),
        sa.ForeignKeyConstraint(['asset_optimization_id'], ['asset_optimizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create comprehensive indexes for CDN configurations
    op.create_index('idx_cdn_config_provider_active', 'cdn_configurations', ['provider', 'is_active'])
    op.create_index('idx_cdn_config_created', 'cdn_configurations', ['created_at'])
    op.create_index('idx_cdn_config_name_provider', 'cdn_configurations', ['name', 'provider'])
    op.create_index('idx_cdn_config_compression', 'cdn_configurations', ['compression_enabled'])
    op.create_index('idx_cdn_config_default', 'cdn_configurations', ['is_default'])

    # Create comprehensive indexes for asset optimizations
    op.create_index('idx_asset_opt_config_type', 'asset_optimizations', ['cdn_configuration_id', 'asset_type'])
    op.create_index('idx_asset_opt_status_created', 'asset_optimizations', ['delivery_status', 'created_at'])
    op.create_index('idx_asset_opt_path', 'asset_optimizations', ['original_path'])
    op.create_index('idx_asset_opt_compression', 'asset_optimizations', ['compression_ratio'])
    op.create_index('idx_asset_opt_performance', 'asset_optimizations', ['optimization_time_ms', 'first_byte_time_ms'])
    op.create_index('idx_asset_opt_type', 'asset_optimizations', ['asset_type'])
    op.create_index('idx_asset_opt_status', 'asset_optimizations', ['delivery_status'])

    # Create comprehensive indexes for asset bundles
    op.create_index('idx_asset_bundle_config_type', 'asset_bundles', ['cdn_configuration_id', 'bundle_type'])
    op.create_index('idx_asset_bundle_name_version', 'asset_bundles', ['bundle_name', 'bundle_version'])
    op.create_index('idx_asset_bundle_status_created', 'asset_bundles', ['delivery_status', 'created_at'])
    op.create_index('idx_asset_bundle_compression', 'asset_bundles', ['compression_ratio'])
    op.create_index('idx_asset_bundle_performance', 'asset_bundles', ['bundling_time_ms', 'first_byte_time_ms'])
    op.create_index('idx_asset_bundle_type', 'asset_bundles', ['bundle_type'])

    # Create comprehensive indexes for CDN metrics
    op.create_index('idx_cdn_metrics_config_period', 'cdn_metrics', ['cdn_configuration_id', 'period_start', 'period_end'])
    op.create_index('idx_cdn_metrics_hit_rate', 'cdn_metrics', ['cache_hit_rate'])
    op.create_index('idx_cdn_metrics_performance', 'cdn_metrics', ['avg_response_time_ms', 'avg_first_byte_time_ms'])
    op.create_index('idx_cdn_metrics_recorded', 'cdn_metrics', ['recorded_at'])
    op.create_index('idx_cdn_metrics_period_start', 'cdn_metrics', ['period_start'])

    # Create comprehensive indexes for asset deliveries
    op.create_index('idx_asset_delivery_opt_delivered', 'asset_deliveries', ['asset_optimization_id', 'delivered_at'])
    op.create_index('idx_asset_delivery_status_time', 'asset_deliveries', ['http_status_code', 'response_time_ms'])
    op.create_index('idx_asset_delivery_cache_status', 'asset_deliveries', ['cache_status', 'delivered_at'])
    op.create_index('idx_asset_delivery_geo', 'asset_deliveries', ['country_code', 'edge_location'])
    op.create_index('idx_asset_delivery_performance', 'asset_deliveries', ['response_time_ms', 'first_byte_time_ms'])
    op.create_index('idx_asset_delivery_delivered', 'asset_deliveries', ['delivered_at'])
    op.create_index('idx_asset_delivery_status', 'asset_deliveries', ['http_status_code'])
    op.create_index('idx_asset_delivery_cache', 'asset_deliveries', ['cache_status'])
    op.create_index('idx_asset_delivery_edge', 'asset_deliveries', ['edge_location'])

    # Set default values for boolean columns
    op.execute("UPDATE cdn_configurations SET compression_enabled = true WHERE compression_enabled IS NULL")
    op.execute("UPDATE cdn_configurations SET minification_enabled = true WHERE minification_enabled IS NULL")
    op.execute("UPDATE cdn_configurations SET bundling_enabled = true WHERE bundling_enabled IS NULL")
    op.execute("UPDATE cdn_configurations SET image_optimization_enabled = true WHERE image_optimization_enabled IS NULL")
    op.execute("UPDATE cdn_configurations SET is_active = true WHERE is_active IS NULL")
    op.execute("UPDATE cdn_configurations SET is_default = false WHERE is_default IS NULL")
    
    op.execute("UPDATE asset_optimizations SET cache_hit_count = 0 WHERE cache_hit_count IS NULL")
    op.execute("UPDATE asset_optimizations SET cache_miss_count = 0 WHERE cache_miss_count IS NULL")
    op.execute("UPDATE asset_optimizations SET retry_count = 0 WHERE retry_count IS NULL")
    
    op.execute("UPDATE asset_bundles SET cache_hit_count = 0 WHERE cache_hit_count IS NULL")
    op.execute("UPDATE asset_bundles SET cache_miss_count = 0 WHERE cache_miss_count IS NULL")
    op.execute("UPDATE asset_bundles SET minification_enabled = true WHERE minification_enabled IS NULL")
    op.execute("UPDATE asset_bundles SET source_map_enabled = false WHERE source_map_enabled IS NULL")


def downgrade():
    """Drop CDN optimization tables and indexes."""
    
    # Drop tables in reverse order (respecting foreign key constraints)
    op.drop_table('asset_deliveries')
    op.drop_table('cdn_metrics')
    op.drop_table('asset_bundles')
    op.drop_table('asset_optimizations')
    op.drop_table('cdn_configurations')
