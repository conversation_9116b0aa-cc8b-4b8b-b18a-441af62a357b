"""
Email Queue Service for Culture Connect Backend API.

This module provides comprehensive email queue management including:
- Email queue processing with priority-based scheduling
- Batch email processing and retry logic
- Queue monitoring and analytics
- Integration with Celery for background processing
- Failed email handling and retry mechanisms

Implements Task 2.3.1 Phase 4 requirements for email service implementation with
production-grade queue management, background processing, and retry logic.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError
from app.models.email_models import EmailQueue, EmailQueueStatus
from app.repositories.email_repository import EmailQueueRepository
from app.schemas.email_schemas import (
    EmailQueueResponse, EmailQueueListResponse
)
from app.core.logging import correlation_id

# Configure logging
logger = logging.getLogger(__name__)


class EmailQueueService(BaseService[EmailQueue, EmailQueueRepository]):
    """
    Email queue service for managing email processing queue.

    Provides comprehensive queue management with priority-based processing,
    retry logic, and integration with background task systems.
    """

    def __init__(self, db: AsyncSession):
        """Initialize email queue service."""
        super().__init__(EmailQueueRepository, EmailQueue, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for email queue creation."""
        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for email queue updates."""
        return data

    async def enqueue_email(
        self,
        user_id: Optional[int],
        template_id: Optional[UUID],
        recipient_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        priority: int = 3,
        scheduled_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EmailQueueResponse:
        """
        Add email to the processing queue.

        Args:
            user_id: Optional associated user ID
            template_id: Optional template ID
            recipient_email: Recipient email address
            subject: Email subject
            body: Email body
            html_body: Optional HTML body
            priority: Email priority (1=highest, 5=lowest)
            scheduled_at: Optional scheduled delivery time
            metadata: Optional additional metadata

        Returns:
            EmailQueueResponse: Queued email data

        Raises:
            ValidationError: If email data is invalid
            ServiceError: If queueing fails
        """
        try:
            # Validate email data
            self._validate_email_data(recipient_email, subject, body, priority)

            # Set default scheduled time if not provided
            if not scheduled_at:
                scheduled_at = datetime.utcnow()

            # Enqueue email
            queue_item = await self.repository.enqueue_email(
                user_id=user_id,
                template_id=template_id,
                recipient_email=recipient_email,
                subject=subject,
                body=body,
                html_body=html_body,
                priority=priority,
                scheduled_at=scheduled_at,
                metadata=metadata or {}
            )

            logger.info(
                f"Email enqueued successfully",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_item.id),
                    "recipient": recipient_email,
                    "priority": priority,
                    "scheduled_at": scheduled_at.isoformat()
                }
            )

            return EmailQueueResponse.model_validate(queue_item)

        except ValidationError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error enqueuing email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "recipient": recipient_email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to enqueue email: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error enqueuing email: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "recipient": recipient_email,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Unexpected error enqueuing email: {str(e)}")

    async def get_next_batch(
        self,
        batch_size: int = 10,
        max_priority: int = 5
    ) -> List[EmailQueueResponse]:
        """
        Get next batch of emails to process from queue.

        Args:
            batch_size: Maximum number of emails to retrieve
            max_priority: Maximum priority level to include (1=highest, 5=lowest)

        Returns:
            List[EmailQueueResponse]: List of emails ready for processing

        Raises:
            ServiceError: If operation fails
        """
        try:
            if batch_size <= 0 or batch_size > 100:
                raise ValidationError("Batch size must be between 1 and 100")

            if max_priority < 1 or max_priority > 5:
                raise ValidationError("Priority must be between 1 and 5")

            # Get next batch from repository
            queue_items = await self.repository.get_next_batch(
                batch_size=batch_size,
                max_priority=max_priority
            )

            # Mark items as processing
            for item in queue_items:
                await self.repository.update_queue_status(
                    queue_id=item.id,
                    status=EmailQueueStatus.PROCESSING
                )

            logger.info(
                f"Retrieved email batch for processing",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "batch_size": len(queue_items),
                    "max_priority": max_priority
                }
            )

            return [EmailQueueResponse.model_validate(item) for item in queue_items]

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                f"Error retrieving email batch: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "batch_size": batch_size,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve email batch: {str(e)}")

    async def mark_as_sent(self, queue_id: UUID) -> EmailQueueResponse:
        """
        Mark queued email as successfully sent.

        Args:
            queue_id: Queue item UUID

        Returns:
            EmailQueueResponse: Updated queue item

        Raises:
            NotFoundError: If queue item not found
            ServiceError: If operation fails
        """
        try:
            queue_item = await self.repository.get_by_id(queue_id)
            if not queue_item:
                raise NotFoundError(f"Queue item {queue_id} not found")

            updated_item = await self.repository.update_queue_status(
                queue_id=queue_id,
                status=EmailQueueStatus.SENT,
                processed_at=datetime.utcnow()
            )

            logger.info(
                f"Queue item marked as sent",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "recipient": queue_item.recipient_email
                }
            )

            return EmailQueueResponse.model_validate(updated_item)

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error marking queue item as sent: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to mark queue item as sent: {str(e)}")

    async def mark_as_failed(
        self,
        queue_id: UUID,
        error_message: str,
        retry: bool = True
    ) -> EmailQueueResponse:
        """
        Mark queued email as failed with optional retry.

        Args:
            queue_id: Queue item UUID
            error_message: Error description
            retry: Whether to retry the email

        Returns:
            EmailQueueResponse: Updated queue item

        Raises:
            NotFoundError: If queue item not found
            ServiceError: If operation fails
        """
        try:
            queue_item = await self.repository.get_by_id(queue_id)
            if not queue_item:
                raise NotFoundError(f"Queue item {queue_id} not found")

            # Increment attempt count
            new_attempts = queue_item.attempts + 1

            # Determine new status
            if retry and new_attempts < queue_item.max_attempts:
                new_status = EmailQueueStatus.RETRY
                # Schedule retry with exponential backoff
                retry_delay = min(300 * (2 ** new_attempts), 3600)  # Max 1 hour
                scheduled_at = datetime.utcnow() + timedelta(seconds=retry_delay)
            else:
                new_status = EmailQueueStatus.FAILED
                scheduled_at = None

            updated_item = await self.repository.update_queue_status(
                queue_id=queue_id,
                status=new_status,
                attempts=new_attempts,
                error_message=error_message,
                scheduled_at=scheduled_at,
                processed_at=datetime.utcnow()
            )

            logger.warning(
                f"Queue item marked as failed",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "recipient": queue_item.recipient_email,
                    "attempts": new_attempts,
                    "max_attempts": queue_item.max_attempts,
                    "will_retry": new_status == EmailQueueStatus.RETRY,
                    "error": error_message
                }
            )

            return EmailQueueResponse.model_validate(updated_item)

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error marking queue item as failed: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to mark queue item as failed: {str(e)}")

    async def cancel_queued_email(self, queue_id: UUID) -> EmailQueueResponse:
        """
        Cancel a queued email.

        Args:
            queue_id: Queue item UUID

        Returns:
            EmailQueueResponse: Updated queue item

        Raises:
            NotFoundError: If queue item not found
            ValidationError: If email cannot be cancelled
            ServiceError: If operation fails
        """
        try:
            queue_item = await self.repository.get_by_id(queue_id)
            if not queue_item:
                raise NotFoundError(f"Queue item {queue_id} not found")

            if queue_item.status in [EmailQueueStatus.SENT, EmailQueueStatus.PROCESSING]:
                raise ValidationError(f"Cannot cancel email with status: {queue_item.status.value}")

            updated_item = await self.repository.update_queue_status(
                queue_id=queue_id,
                status=EmailQueueStatus.CANCELLED,
                processed_at=datetime.utcnow()
            )

            logger.info(
                f"Queue item cancelled",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "recipient": queue_item.recipient_email
                }
            )

            return EmailQueueResponse.model_validate(updated_item)

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(
                f"Error cancelling queue item: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "queue_id": str(queue_id),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to cancel queue item: {str(e)}")

    async def list_queue_items(
        self,
        status: Optional[EmailQueueStatus] = None,
        user_id: Optional[int] = None,
        priority: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> EmailQueueListResponse:
        """
        List queue items with filtering.

        Args:
            status: Optional status filter
            user_id: Optional user ID filter
            priority: Optional priority filter
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            EmailQueueListResponse: List of queue items with metadata
        """
        try:
            queue_items, total = await self.repository.list_queue_items(
                status=status,
                user_id=user_id,
                priority=priority,
                skip=skip,
                limit=limit
            )

            queue_responses = [
                EmailQueueResponse.model_validate(item)
                for item in queue_items
            ]

            return EmailQueueListResponse(
                queue_items=queue_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(
                f"Error listing queue items: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "status": status.value if status else None,
                    "user_id": user_id,
                    "priority": priority,
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to list queue items: {str(e)}")

    async def get_queue_statistics(self) -> Dict[str, Any]:
        """
        Get email queue statistics for monitoring.

        Returns:
            Dict[str, Any]: Queue statistics

        Raises:
            ServiceError: If operation fails
        """
        try:
            stats = await self.repository.get_queue_statistics()

            logger.info(
                f"Retrieved queue statistics",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "total_queued": stats.get("total_queued", 0),
                    "pending": stats.get("pending", 0),
                    "processing": stats.get("processing", 0),
                    "failed": stats.get("failed", 0)
                }
            )

            return stats

        except Exception as e:
            logger.error(
                f"Error retrieving queue statistics: {str(e)}",
                extra={
                    "correlation_id": correlation_id.get(''),
                    "error_type": type(e).__name__
                }
            )
            raise ServiceError(f"Failed to retrieve queue statistics: {str(e)}")

    def _validate_email_data(
        self,
        recipient_email: str,
        subject: str,
        body: str,
        priority: int
    ) -> None:
        """
        Validate email data for queueing.

        Args:
            recipient_email: Recipient email address
            subject: Email subject
            body: Email body
            priority: Email priority

        Raises:
            ValidationError: If data is invalid
        """
        if not recipient_email or not recipient_email.strip():
            raise ValidationError("Recipient email is required")

        if not subject or not subject.strip():
            raise ValidationError("Email subject is required")

        if not body or not body.strip():
            raise ValidationError("Email body is required")

        if priority < 1 or priority > 5:
            raise ValidationError("Priority must be between 1 (highest) and 5 (lowest)")

        if len(subject) > 500:
            raise ValidationError("Subject cannot exceed 500 characters")

        if len(body) > 1000000:  # 1MB limit
            raise ValidationError("Body cannot exceed 1MB")
