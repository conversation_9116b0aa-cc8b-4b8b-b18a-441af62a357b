"""
Content Moderation models for Culture Connect Backend API.

This module defines comprehensive content moderation models including:
- ContentModerationWorkflow: Content review and approval workflow tracking
- ContentQualityScore: Quality assessment and scoring metrics
- PlagiarismCheck: Plagiarism detection results and analysis
- ContentModerationRule: Configurable moderation rules and criteria
- ContentApprovalHistory: Audit trail for content approval decisions

Implements Task 3.2.4 requirements for content management and quality assurance
with production-grade PostgreSQL optimization and seamless integration with
existing marketplace systems from Tasks 3.2.1, 3.2.2, and 3.2.3.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class ContentStatus(str, Enum):
    """Content moderation status enumeration."""
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    FLAGGED = "flagged"
    REQUIRES_REVISION = "requires_revision"
    AUTO_APPROVED = "auto_approved"


class ContentType(str, Enum):
    """Content type enumeration for moderation."""
    SERVICE_LISTING = "service_listing"
    SERVICE_DESCRIPTION = "service_description"
    SERVICE_IMAGE = "service_image"
    VENDOR_PROFILE = "vendor_profile"
    VENDOR_DESCRIPTION = "vendor_description"
    REVIEW_CONTENT = "review_content"
    PROMOTIONAL_CONTENT = "promotional_content"


class ModerationAction(str, Enum):
    """Moderation action enumeration."""
    APPROVE = "approve"
    REJECT = "reject"
    FLAG = "flag"
    REQUEST_REVISION = "request_revision"
    ESCALATE = "escalate"
    AUTO_APPROVE = "auto_approve"
    AUTO_REJECT = "auto_reject"


class QualityCategory(str, Enum):
    """Quality assessment category enumeration."""
    CONTENT_QUALITY = "content_quality"
    IMAGE_QUALITY = "image_quality"
    METADATA_COMPLETENESS = "metadata_completeness"
    SEO_OPTIMIZATION = "seo_optimization"
    MOBILE_FRIENDLINESS = "mobile_friendliness"
    CULTURAL_APPROPRIATENESS = "cultural_appropriateness"


class PlagiarismStatus(str, Enum):
    """Plagiarism detection status enumeration."""
    NOT_CHECKED = "not_checked"
    CHECKING = "checking"
    ORIGINAL = "original"
    SIMILAR_FOUND = "similar_found"
    PLAGIARIZED = "plagiarized"
    INCONCLUSIVE = "inconclusive"


class ContentModerationWorkflow(Base):
    """
    Content moderation workflow model for tracking review and approval processes.

    Manages the complete lifecycle of content moderation from submission
    to final approval or rejection with comprehensive audit trail.
    """
    __tablename__ = "content_moderation_workflows"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    content_type = Column(String(50), nullable=False, index=True)
    content_id = Column(PostgreSQLUUID(as_uuid=True), nullable=False, index=True)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)

    # Workflow status and tracking
    status = Column(String(30), nullable=False, default=ContentStatus.PENDING, index=True)
    priority = Column(Integer, default=5, nullable=False)  # 1-10 scale, 10 = highest
    assigned_reviewer_id = Column(Integer, ForeignKey("users.id"))

    # Content metadata
    content_title = Column(String(255))
    content_summary = Column(Text)
    content_url = Column(String(500))
    content_metadata = Column(JSONB, default=dict)

    # Moderation results
    moderation_score = Column(Float, default=0.0)
    quality_score = Column(Float, default=0.0)
    plagiarism_score = Column(Float, default=0.0)
    automated_flags = Column(JSONB, default=list)
    manual_flags = Column(JSONB, default=list)

    # Review details
    reviewer_notes = Column(Text)
    rejection_reason = Column(Text)
    revision_requirements = Column(JSONB, default=list)
    escalation_reason = Column(Text)

    # Timing information
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())
    review_started_at = Column(DateTime(timezone=True))
    review_completed_at = Column(DateTime(timezone=True))
    auto_approval_deadline = Column(DateTime(timezone=True))

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor = relationship("Vendor", back_populates="content_workflows")
    assigned_reviewer = relationship("User", foreign_keys=[assigned_reviewer_id])
    quality_scores = relationship("ContentQualityScore", back_populates="workflow")
    plagiarism_checks = relationship("PlagiarismCheck", back_populates="workflow")
    approval_history = relationship("ContentApprovalHistory", back_populates="workflow")

    # Indexes
    __table_args__ = (
        Index("idx_content_workflow_vendor_status", "vendor_id", "status"),
        Index("idx_content_workflow_type_status", "content_type", "status"),
        Index("idx_content_workflow_priority", "priority", "submitted_at"),
        Index("idx_content_workflow_reviewer", "assigned_reviewer_id", "status"),
        Index("idx_content_workflow_deadline", "auto_approval_deadline"),
        UniqueConstraint("content_type", "content_id", name="uq_content_workflow_content"),
        CheckConstraint("priority >= 1 AND priority <= 10", name="ck_content_workflow_priority"),
        CheckConstraint("moderation_score >= 0 AND moderation_score <= 100", name="ck_content_workflow_mod_score"),
        CheckConstraint("quality_score >= 0 AND quality_score <= 100", name="ck_content_workflow_quality_score"),
        CheckConstraint("plagiarism_score >= 0 AND plagiarism_score <= 100", name="ck_content_workflow_plagiarism_score"),
    )


class ContentQualityScore(Base):
    """
    Content quality score model for comprehensive quality assessment.

    Tracks detailed quality metrics across multiple categories with
    configurable scoring weights and improvement recommendations.
    """
    __tablename__ = "content_quality_scores"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    workflow_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("content_moderation_workflows.id"), nullable=False)

    # Overall quality metrics
    overall_score = Column(Float, nullable=False, default=0.0)
    weighted_score = Column(Float, nullable=False, default=0.0)
    quality_grade = Column(String(2))  # A+, A, B+, B, C+, C, D, F

    # Category-specific scores
    content_quality_score = Column(Float, default=0.0)
    image_quality_score = Column(Float, default=0.0)
    metadata_completeness_score = Column(Float, default=0.0)
    seo_optimization_score = Column(Float, default=0.0)
    mobile_friendliness_score = Column(Float, default=0.0)
    cultural_appropriateness_score = Column(Float, default=0.0)

    # Detailed analysis
    content_analysis = Column(JSONB, default=dict)  # Grammar, readability, completeness
    image_analysis = Column(JSONB, default=dict)    # Resolution, format, appropriateness
    seo_analysis = Column(JSONB, default=dict)      # Keywords, meta tags, structure
    mobile_analysis = Column(JSONB, default=dict)   # Responsiveness, touch-friendliness

    # Improvement recommendations
    recommendations = Column(JSONB, default=list)
    priority_improvements = Column(JSONB, default=list)
    estimated_improvement_impact = Column(Float, default=0.0)

    # Scoring configuration
    scoring_weights = Column(JSONB, default=dict)
    scoring_version = Column(String(20), default="1.0")

    # Metadata
    calculated_at = Column(DateTime(timezone=True), server_default=func.now())
    calculation_duration_ms = Column(Integer, default=0)

    # Relationships
    workflow = relationship("ContentModerationWorkflow", back_populates="quality_scores")

    # Indexes
    __table_args__ = (
        Index("idx_quality_score_workflow", "workflow_id"),
        Index("idx_quality_score_overall", "overall_score"),
        Index("idx_quality_score_grade", "quality_grade"),
        Index("idx_quality_score_calculated", "calculated_at"),
        CheckConstraint("overall_score >= 0 AND overall_score <= 100", name="ck_quality_score_overall"),
        CheckConstraint("weighted_score >= 0 AND weighted_score <= 100", name="ck_quality_score_weighted"),
        CheckConstraint("content_quality_score >= 0 AND content_quality_score <= 100", name="ck_quality_score_content"),
        CheckConstraint("image_quality_score >= 0 AND image_quality_score <= 100", name="ck_quality_score_image"),
        CheckConstraint("metadata_completeness_score >= 0 AND metadata_completeness_score <= 100", name="ck_quality_score_metadata"),
        CheckConstraint("seo_optimization_score >= 0 AND seo_optimization_score <= 100", name="ck_quality_score_seo"),
        CheckConstraint("mobile_friendliness_score >= 0 AND mobile_friendliness_score <= 100", name="ck_quality_score_mobile"),
        CheckConstraint("cultural_appropriateness_score >= 0 AND cultural_appropriateness_score <= 100", name="ck_quality_score_cultural"),
    )


class PlagiarismCheck(Base):
    """
    Plagiarism check model for content originality verification.

    Tracks plagiarism detection results with similarity analysis,
    source identification, and originality scoring.
    """
    __tablename__ = "plagiarism_checks"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    workflow_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("content_moderation_workflows.id"), nullable=False)

    # Check status and results
    status = Column(String(20), nullable=False, default=PlagiarismStatus.NOT_CHECKED, index=True)
    similarity_percentage = Column(Float, default=0.0)
    originality_score = Column(Float, default=100.0)
    confidence_level = Column(Float, default=0.0)

    # Detection details
    similar_sources_found = Column(Integer, default=0)
    exact_matches_found = Column(Integer, default=0)
    paraphrased_matches_found = Column(Integer, default=0)

    # Similar content analysis
    similar_sources = Column(JSONB, default=list)  # URLs, titles, similarity scores
    matching_segments = Column(JSONB, default=list)  # Text segments with matches
    source_analysis = Column(JSONB, default=dict)   # Source credibility, type

    # Check configuration
    check_parameters = Column(JSONB, default=dict)
    sensitivity_level = Column(String(20), default="medium")  # low, medium, high
    excluded_sources = Column(JSONB, default=list)

    # Processing details
    content_hash = Column(String(64), index=True)
    content_length = Column(Integer, default=0)
    words_checked = Column(Integer, default=0)
    processing_time_ms = Column(Integer, default=0)

    # Metadata
    checked_at = Column(DateTime(timezone=True), server_default=func.now())
    check_version = Column(String(20), default="1.0")

    # Relationships
    workflow = relationship("ContentModerationWorkflow", back_populates="plagiarism_checks")

    # Indexes
    __table_args__ = (
        Index("idx_plagiarism_workflow", "workflow_id"),
        Index("idx_plagiarism_status", "status"),
        Index("idx_plagiarism_similarity", "similarity_percentage"),
        Index("idx_plagiarism_hash", "content_hash"),
        Index("idx_plagiarism_checked", "checked_at"),
        CheckConstraint("similarity_percentage >= 0 AND similarity_percentage <= 100", name="ck_plagiarism_similarity"),
        CheckConstraint("originality_score >= 0 AND originality_score <= 100", name="ck_plagiarism_originality"),
        CheckConstraint("confidence_level >= 0 AND confidence_level <= 100", name="ck_plagiarism_confidence"),
        CheckConstraint("similar_sources_found >= 0", name="ck_plagiarism_sources_positive"),
        CheckConstraint("exact_matches_found >= 0", name="ck_plagiarism_exact_positive"),
        CheckConstraint("paraphrased_matches_found >= 0", name="ck_plagiarism_paraphrased_positive"),
    )


class ContentModerationRule(Base):
    """
    Content moderation rule model for configurable moderation criteria.

    Defines automated moderation rules with configurable parameters,
    thresholds, and actions for different content types.
    """
    __tablename__ = "content_moderation_rules"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    rule_name = Column(String(100), nullable=False, unique=True)
    rule_description = Column(Text)

    # Rule configuration
    content_types = Column(JSONB, default=list)  # Applicable content types
    rule_category = Column(String(50), nullable=False, index=True)
    rule_severity = Column(String(20), default="medium")  # low, medium, high, critical

    # Rule parameters
    detection_criteria = Column(JSONB, nullable=False)
    threshold_values = Column(JSONB, default=dict)
    action_on_trigger = Column(String(30), nullable=False)
    escalation_threshold = Column(Float, default=0.8)

    # Rule logic
    rule_logic = Column(JSONB, nullable=False)  # Conditions and operators
    custom_keywords = Column(JSONB, default=list)
    excluded_keywords = Column(JSONB, default=list)
    regex_patterns = Column(JSONB, default=list)

    # Rule status and metadata
    is_active = Column(Boolean, default=True, index=True)
    auto_apply = Column(Boolean, default=True)
    requires_human_review = Column(Boolean, default=False)

    # Performance tracking
    total_applications = Column(Integer, default=0)
    successful_detections = Column(Integer, default=0)
    false_positives = Column(Integer, default=0)
    accuracy_rate = Column(Float, default=0.0)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    last_modified_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    modifier = relationship("User", foreign_keys=[last_modified_by])

    # Indexes
    __table_args__ = (
        Index("idx_moderation_rule_category", "rule_category"),
        Index("idx_moderation_rule_severity", "rule_severity"),
        Index("idx_moderation_rule_active", "is_active"),
        Index("idx_moderation_rule_accuracy", "accuracy_rate"),
        CheckConstraint("escalation_threshold >= 0 AND escalation_threshold <= 1", name="ck_moderation_rule_escalation"),
        CheckConstraint("accuracy_rate >= 0 AND accuracy_rate <= 100", name="ck_moderation_rule_accuracy"),
        CheckConstraint("total_applications >= 0", name="ck_moderation_rule_applications_positive"),
        CheckConstraint("successful_detections >= 0", name="ck_moderation_rule_detections_positive"),
        CheckConstraint("false_positives >= 0", name="ck_moderation_rule_false_positives_positive"),
    )


class ContentApprovalHistory(Base):
    """
    Content approval history model for audit trail of approval decisions.

    Maintains comprehensive audit trail of all content approval decisions
    with reviewer information, timestamps, and decision rationale.
    """
    __tablename__ = "content_approval_history"

    id = Column(PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4)
    workflow_id = Column(PostgreSQLUUID(as_uuid=True), ForeignKey("content_moderation_workflows.id"), nullable=False)

    # Approval decision details
    action_taken = Column(String(30), nullable=False, index=True)
    previous_status = Column(String(30), nullable=False)
    new_status = Column(String(30), nullable=False)

    # Reviewer information
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    reviewer_role = Column(String(50))
    is_automated = Column(Boolean, default=False, index=True)

    # Decision details
    decision_reason = Column(Text)
    reviewer_notes = Column(Text)
    confidence_score = Column(Float, default=0.0)
    time_spent_minutes = Column(Integer, default=0)

    # Quality and scoring at time of decision
    quality_score_at_decision = Column(Float, default=0.0)
    moderation_score_at_decision = Column(Float, default=0.0)
    plagiarism_score_at_decision = Column(Float, default=0.0)

    # Revision requirements (if applicable)
    revision_requirements = Column(JSONB, default=list)
    estimated_revision_time = Column(Integer, default=0)  # minutes
    revision_priority = Column(String(20), default="medium")

    # Escalation details (if applicable)
    escalated_to = Column(Integer, ForeignKey("users.id"))
    escalation_reason = Column(Text)
    escalation_urgency = Column(String(20), default="normal")

    # Decision metadata
    decision_factors = Column(JSONB, default=list)  # Factors that influenced decision
    automated_flags_considered = Column(JSONB, default=list)
    manual_flags_considered = Column(JSONB, default=list)

    # Timing information
    decision_made_at = Column(DateTime(timezone=True), server_default=func.now())
    review_duration_minutes = Column(Integer, default=0)

    # Relationships
    workflow = relationship("ContentModerationWorkflow", back_populates="approval_history")
    reviewer = relationship("User", foreign_keys=[reviewer_id])
    escalated_reviewer = relationship("User", foreign_keys=[escalated_to])

    # Indexes
    __table_args__ = (
        Index("idx_approval_history_workflow", "workflow_id"),
        Index("idx_approval_history_reviewer", "reviewer_id"),
        Index("idx_approval_history_action", "action_taken"),
        Index("idx_approval_history_automated", "is_automated"),
        Index("idx_approval_history_decision_time", "decision_made_at"),
        Index("idx_approval_history_escalated", "escalated_to"),
        CheckConstraint("confidence_score >= 0 AND confidence_score <= 100", name="ck_approval_history_confidence"),
        CheckConstraint("quality_score_at_decision >= 0 AND quality_score_at_decision <= 100", name="ck_approval_history_quality"),
        CheckConstraint("moderation_score_at_decision >= 0 AND moderation_score_at_decision <= 100", name="ck_approval_history_moderation"),
        CheckConstraint("plagiarism_score_at_decision >= 0 AND plagiarism_score_at_decision <= 100", name="ck_approval_history_plagiarism"),
        CheckConstraint("time_spent_minutes >= 0", name="ck_approval_history_time_positive"),
        CheckConstraint("estimated_revision_time >= 0", name="ck_approval_history_revision_time_positive"),
        CheckConstraint("review_duration_minutes >= 0", name="ck_approval_history_duration_positive"),
    )
