"""
Production-grade dependency injection for Culture Connect Backend API.

This module provides comprehensive dependency injection utilities including:
- JWT-based authentication with role validation
- Role-based access control (RBAC) with granular permissions
- Database session management with transaction support
- Request correlation ID tracking for audit trails
- Rate limiting and security middleware integration
"""

import logging
import uuid
from typing import Optional, List, Dict, Any, Generator
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from jose import JWTError

from app.core.config import settings
from app.core.security import verify_token, TokenData, UserRole
from app.core.logging import correlation_id
from app.db.session import get_db_session
from app.models.user import User
from app.models.rbac_models import AccessDecision, ResourceType
from app.repositories.auth_repository import UserRepository
from app.repositories.rbac_repository import RBACRepository

logger = logging.getLogger(__name__)
security = HTTPBearer()


class AuthenticationError(HTTPException):
    """Custom authentication error with detailed logging."""

    def __init__(self, detail: str = "Authentication failed", correlation_id: str = ""):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )
        logger.warning(
            f"Authentication failed: {detail}",
            extra={"correlation_id": correlation_id}
        )


class AuthorizationError(HTTPException):
    """Custom authorization error with detailed logging."""

    def __init__(self, detail: str = "Insufficient permissions", correlation_id: str = ""):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )
        logger.warning(
            f"Authorization failed: {detail}",
            extra={"correlation_id": correlation_id}
        )


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """
    Get current authenticated user from JWT token.

    Args:
        credentials: HTTP Bearer token credentials
        db: Database session

    Returns:
        User: Authenticated user instance

    Raises:
        AuthenticationError: If token is invalid or user not found
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    try:
        # Verify JWT token
        token_data = await verify_token(credentials.credentials, "access")
        if not token_data or not token_data.user_id:
            raise AuthenticationError("Invalid token", correlation)

        # Get user from database
        user_repo = UserRepository(db)
        user = await user_repo.get_by_id_with_roles(token_data.user_id)

        if not user:
            raise AuthenticationError("User not found", correlation)

        if not user.is_active:
            raise AuthenticationError("User account is disabled", correlation)

        # Log successful authentication
        logger.info(
            "User authenticated successfully",
            extra={
                "user_id": user.id,
                "email": user.email,
                "correlation_id": correlation
            }
        )

        return user

    except JWTError as e:
        logger.error(
            f"JWT verification failed: {str(e)}",
            extra={"correlation_id": correlation}
        )
        raise AuthenticationError("Invalid token format", correlation)
    except Exception as e:
        logger.error(
            f"Authentication error: {str(e)}",
            extra={"correlation_id": correlation}
        )
        raise AuthenticationError("Authentication failed", correlation)


async def get_current_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current authenticated admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Admin user instance

    Raises:
        AuthorizationError: If user is not an admin
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    # Check if user has admin role
    admin_roles = [UserRole.SUPER_ADMIN, UserRole.ADMIN]
    if current_user.role not in admin_roles:
        raise AuthorizationError(
            f"Admin access required. Current role: {current_user.role.value}",
            correlation
        )

    logger.info(
        "Admin user authenticated",
        extra={
            "user_id": current_user.id,
            "role": current_user.role.value,
            "correlation_id": correlation
        }
    )

    return current_user


async def get_current_super_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current authenticated super admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Super admin user instance

    Raises:
        AuthorizationError: If user is not a super admin
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    if current_user.role != UserRole.SUPER_ADMIN:
        raise AuthorizationError(
            f"Super admin access required. Current role: {current_user.role.value}",
            correlation
        )

    return current_user


async def get_current_vendor_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current authenticated vendor user.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Vendor user instance

    Raises:
        AuthorizationError: If user is not a vendor
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    if current_user.role != UserRole.VENDOR:
        raise AuthorizationError(
            f"Vendor access required. Current role: {current_user.role.value}",
            correlation
        )

    if not current_user.vendor_profile:
        raise AuthorizationError("Vendor profile not found", correlation)

    return current_user


async def require_permission(
    permission: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
) -> None:
    """
    Require specific permission for the current user.

    Args:
        permission: Required permission string (e.g., "verification:create")
        current_user: Current authenticated user
        db: Database session

    Raises:
        AuthorizationError: If user doesn't have required permission
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    try:
        # Super admins have all permissions
        if current_user.role == UserRole.SUPER_ADMIN:
            logger.debug(
                f"Super admin granted permission: {permission}",
                extra={
                    "user_id": current_user.id,
                    "permission": permission,
                    "correlation_id": correlation
                }
            )
            return

        # Use existing RBAC service for permission checking
        from app.services.rbac_service import RBACService
        rbac_service = RBACService(db)

        # Check permission using existing RBAC system
        permission_result = await rbac_service.check_permission(
            user_id=str(current_user.id),
            user_role=current_user.role,
            permission=permission
        )

        if not permission_result.has_permission:
            logger.warning(
                f"Permission denied: {permission}",
                extra={
                    "user_id": current_user.id,
                    "role": current_user.role,
                    "required_permission": permission,
                    "decision": permission_result.decision,
                    "reason": permission_result.reason,
                    "correlation_id": correlation
                }
            )
            raise AuthorizationError(
                f"Permission denied: {permission_result.reason}",
                correlation
            )

        logger.debug(
            f"Permission granted: {permission}",
            extra={
                "user_id": current_user.id,
                "permission": permission,
                "reason": permission_result.reason,
                "correlation_id": correlation
            }
        )

    except AuthorizationError:
        raise
    except Exception as e:
        logger.error(
            f"Permission check failed: {str(e)}",
            extra={
                "user_id": current_user.id,
                "permission": permission,
                "error": str(e),
                "correlation_id": correlation
            }
        )
        raise AuthorizationError("Permission check failed", correlation)


async def require_role(
    required_role: UserRole,
    current_user: User = Depends(get_current_user)
) -> None:
    """
    Require specific role for the current user.

    Args:
        required_role: Required user role
        current_user: Current authenticated user

    Raises:
        AuthorizationError: If user doesn't have required role
    """
    correlation = correlation_id.get() or str(uuid.uuid4())

    if current_user.role != required_role:
        logger.warning(
            f"Role access denied: required {required_role.value}, user has {current_user.role.value}",
            extra={
                "user_id": current_user.id,
                "user_role": current_user.role.value,
                "required_role": required_role.value,
                "correlation_id": correlation
            }
        )
        raise AuthorizationError(
            f"Access denied. Required role: {required_role.value}",
            correlation
        )

    logger.debug(
        f"Role access granted: {required_role.value}",
        extra={
            "user_id": current_user.id,
            "role": current_user.role.value,
            "correlation_id": correlation
        }
    )


def require_permissions(permissions: List[str]):
    """
    Dependency factory for requiring multiple permissions.

    Args:
        permissions: List of required permissions

    Returns:
        Dependency function that checks all permissions
    """
    async def check_permissions(
        current_user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db_session)
    ) -> None:
        for permission in permissions:
            await require_permission(permission, current_user, db)

    return check_permissions


def require_permission_dependency(permission: str):
    """
    Dependency factory for requiring a single permission.

    Args:
        permission: Required permission string

    Returns:
        Dependency function that checks the permission
    """
    async def check_permission(
        current_user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db_session)
    ) -> User:
        await require_permission(permission, current_user, db)
        return current_user

    return check_permission


def get_correlation_id(request: Request) -> str:
    """
    Get or generate correlation ID for request tracking.

    Args:
        request: FastAPI request object

    Returns:
        str: Correlation ID for request tracking
    """
    # Try to get correlation ID from header
    correlation = request.headers.get("X-Correlation-ID")

    if not correlation:
        # Generate new correlation ID
        correlation = str(uuid.uuid4())

    # Set in context for logging
    correlation_id.set(correlation)

    return correlation


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db_session)
) -> Optional[User]:
    """
    Get current authenticated user from JWT token (optional).

    This dependency allows endpoints to work with or without authentication.
    If no token is provided or token is invalid, returns None instead of raising an error.

    Args:
        credentials: HTTP Bearer token credentials (optional)
        db: Database session

    Returns:
        Optional[User]: Authenticated user instance or None if not authenticated
    """
    if not credentials:
        return None

    try:
        # Verify JWT token
        token_data = await verify_token(credentials.credentials, "access")
        if not token_data or not token_data.user_id:
            return None

        # Get user from database
        user_repo = UserRepository(db)
        user = await user_repo.get_by_id_with_roles(token_data.user_id)

        if not user or not user.is_active:
            return None

        return user

    except Exception as e:
        # Log the error but don't raise it - this is optional authentication
        logger.debug(f"Optional authentication failed: {str(e)}")
        return None


async def get_db() -> AsyncSession:
    """
    Database session dependency.

    Returns:
        AsyncSession: Database session for request
    """
    async for session in get_db_session():
        yield session


def get_pagination_params(
    page: int = 1,
    page_size: int = 50,
    max_page_size: int = 100
) -> Dict[str, int]:
    """
    Get pagination parameters with validation.

    Args:
        page: Page number (1-based)
        page_size: Items per page
        max_page_size: Maximum allowed page size

    Returns:
        Dict with pagination parameters

    Raises:
        HTTPException: If parameters are invalid
    """
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Page number must be >= 1"
        )

    if page_size < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Page size must be >= 1"
        )

    if page_size > max_page_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Page size cannot exceed {max_page_size}"
        )

    offset = (page - 1) * page_size

    return {
        "page": page,
        "page_size": page_size,
        "offset": offset,
        "limit": page_size
    }
