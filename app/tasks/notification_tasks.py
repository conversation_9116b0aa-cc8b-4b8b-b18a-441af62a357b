"""
Notification background tasks for Culture Connect Backend API.

This module provides comprehensive notification task processing including:
- Push notification delivery
- SMS notification sending
- Batch notification processing
- Notification template rendering
- Delivery status tracking

Implements Task 6.2.1 requirements for notification task processing with
production-grade reliability and performance.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from uuid import UUID

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="notification.send_push_notification", queue="notification")
def send_push_notification(self, notification_data: Dict[str, Any]):
    """
    Send push notification to user device.
    
    Args:
        notification_data: Notification data including user_id, title, message, data
    """
    try:
        async def _send_notification():
            from app.services.push_notification_service import PushNotificationService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                push_service = PushNotificationService(session)
                
                # Extract notification parameters
                user_id = notification_data["user_id"]
                title = notification_data["title"]
                message = notification_data["message"]
                data = notification_data.get("data", {})
                priority = notification_data.get("priority", "normal")
                
                # Send push notification
                result = await push_service.send_notification(
                    user_id=UUID(user_id),
                    title=title,
                    message=message,
                    data=data,
                    priority=priority
                )
                
                logger.info(f"Push notification sent to user {user_id}", extra={
                    "user_id": user_id,
                    "title": title,
                    "priority": priority,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "notification_id": result.notification_id,
                    "delivery_status": result.delivery_status,
                    "devices_reached": result.devices_reached
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_notification())
            return {
                "status": "success",
                "sent_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to send push notification: {str(e)}", extra={
            "notification_data": notification_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=30, max_retries=3)


@celery_app.task(bind=True, name="notification.send_sms_notification", queue="notification")
def send_sms_notification(self, sms_data: Dict[str, Any]):
    """
    Send SMS notification to user.
    
    Args:
        sms_data: SMS data including phone_number, message, user_id
    """
    try:
        async def _send_sms():
            from app.services.sms_service import SMSService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                sms_service = SMSService(session)
                
                # Extract SMS parameters
                phone_number = sms_data["phone_number"]
                message = sms_data["message"]
                user_id = sms_data.get("user_id")
                
                # Send SMS
                result = await sms_service.send_sms(
                    phone_number=phone_number,
                    message=message,
                    user_id=UUID(user_id) if user_id else None
                )
                
                logger.info(f"SMS sent to {phone_number}", extra={
                    "phone_number": phone_number,
                    "user_id": user_id,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "message_id": result.message_id,
                    "delivery_status": result.delivery_status,
                    "cost": result.cost
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_sms())
            return {
                "status": "success",
                "sent_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to send SMS notification: {str(e)}", extra={
            "sms_data": sms_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="notification.process_notification_batch", queue="notification")
def process_notification_batch(self, batch_data: Dict[str, Any]):
    """
    Process batch of notifications.
    
    Args:
        batch_data: Batch data including notifications list and batch settings
    """
    try:
        async def _process_batch():
            from app.services.push_notification_service import PushNotificationService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                push_service = PushNotificationService(session)
                
                notifications = batch_data["notifications"]
                batch_id = batch_data.get("batch_id", "unknown")
                
                results = []
                successful = 0
                failed = 0
                
                for notification in notifications:
                    try:
                        result = await push_service.send_notification(
                            user_id=UUID(notification["user_id"]),
                            title=notification["title"],
                            message=notification["message"],
                            data=notification.get("data", {}),
                            priority=notification.get("priority", "normal")
                        )
                        
                        results.append({
                            "user_id": notification["user_id"],
                            "status": "success",
                            "notification_id": result.notification_id
                        })
                        successful += 1
                        
                    except Exception as e:
                        results.append({
                            "user_id": notification["user_id"],
                            "status": "failed",
                            "error": str(e)
                        })
                        failed += 1
                
                logger.info(f"Notification batch processed: {successful} successful, {failed} failed", extra={
                    "batch_id": batch_id,
                    "total_notifications": len(notifications),
                    "successful": successful,
                    "failed": failed,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "batch_id": batch_id,
                    "total_notifications": len(notifications),
                    "successful": successful,
                    "failed": failed,
                    "results": results
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_batch())
            return {
                "status": "completed",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process notification batch: {str(e)}", extra={
            "batch_data": batch_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=120, max_retries=2)
