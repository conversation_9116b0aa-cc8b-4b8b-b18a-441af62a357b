#!/bin/bash

# Culture Connect Backend - Comprehensive Health Check Script
# This script performs comprehensive health checks across all environments
# and validates system readiness for deployment.

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ENVIRONMENT="${ENVIRONMENT:-development}"
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
TIMEOUT="${TIMEOUT:-30}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Health check results
HEALTH_CHECKS_PASSED=0
HEALTH_CHECKS_FAILED=0
HEALTH_CHECK_RESULTS=()

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Health check result logging
log_check_pass() {
    local check_name="$1"
    local message="$2"
    echo -e "${GREEN}[PASS]${NC} $check_name: $message"
    HEALTH_CHECKS_PASSED=$((HEALTH_CHECKS_PASSED + 1))
    HEALTH_CHECK_RESULTS+=("PASS: $check_name - $message")
}

log_check_fail() {
    local check_name="$1"
    local message="$2"
    echo -e "${RED}[FAIL]${NC} $check_name: $message"
    HEALTH_CHECKS_FAILED=$((HEALTH_CHECKS_FAILED + 1))
    HEALTH_CHECK_RESULTS+=("FAIL: $check_name - $message")
}

log_check_warn() {
    local check_name="$1"
    local message="$2"
    echo -e "${YELLOW}[WARN]${NC} $check_name: $message"
    HEALTH_CHECK_RESULTS+=("WARN: $check_name - $message")
}

# =============================================================================
# HEALTH CHECK FUNCTIONS
# =============================================================================

# Check API availability
check_api_health() {
    log_info "Checking API health endpoint..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json --max-time "$TIMEOUT" "$API_BASE_URL/health" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            local health_data=$(cat /tmp/health_response.json)
            log_check_pass "API Health" "API is responding (HTTP $status_code)"
            
            # Parse health response for additional details
            if command -v jq &> /dev/null; then
                local status=$(echo "$health_data" | jq -r '.status // "unknown"')
                local timestamp=$(echo "$health_data" | jq -r '.timestamp // "unknown"')
                log_info "Health status: $status, Timestamp: $timestamp"
            fi
        else
            log_check_fail "API Health" "API returned HTTP $status_code"
        fi
    else
        log_check_fail "API Health" "API is not responding or unreachable"
    fi
    
    rm -f /tmp/health_response.json
}

# Check database connectivity
check_database_health() {
    log_info "Checking database connectivity..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/db_health.json --max-time "$TIMEOUT" "$API_BASE_URL/health/database" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            log_check_pass "Database Health" "Database is accessible"
        else
            log_check_fail "Database Health" "Database health check failed (HTTP $status_code)"
        fi
    else
        log_check_fail "Database Health" "Database health endpoint unreachable"
    fi
    
    rm -f /tmp/db_health.json
}

# Check Redis connectivity
check_redis_health() {
    log_info "Checking Redis connectivity..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/redis_health.json --max-time "$TIMEOUT" "$API_BASE_URL/health/redis" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            log_check_pass "Redis Health" "Redis is accessible"
        else
            log_check_fail "Redis Health" "Redis health check failed (HTTP $status_code)"
        fi
    else
        log_check_fail "Redis Health" "Redis health endpoint unreachable"
    fi
    
    rm -f /tmp/redis_health.json
}

# Check Celery worker status
check_celery_health() {
    log_info "Checking Celery worker status..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/celery_health.json --max-time "$TIMEOUT" "$API_BASE_URL/health/celery" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            log_check_pass "Celery Health" "Celery workers are active"
        else
            log_check_fail "Celery Health" "Celery health check failed (HTTP $status_code)"
        fi
    else
        log_check_warn "Celery Health" "Celery health endpoint unreachable (may not be implemented)"
    fi
    
    rm -f /tmp/celery_health.json
}

# Check API documentation
check_api_docs() {
    log_info "Checking API documentation availability..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null --max-time "$TIMEOUT" "$API_BASE_URL/docs" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            log_check_pass "API Documentation" "Swagger UI is accessible"
        else
            log_check_fail "API Documentation" "Swagger UI returned HTTP $status_code"
        fi
    else
        log_check_fail "API Documentation" "Swagger UI is not accessible"
    fi
}

# Check API performance
check_api_performance() {
    log_info "Checking API performance..."
    
    local start_time=$(date +%s%N)
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null --max-time "$TIMEOUT" "$API_BASE_URL/health" 2>/dev/null); then
        local end_time=$(date +%s%N)
        local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
        status_code="${response: -3}"
        
        if [[ "$status_code" == "200" ]]; then
            if [[ $duration -lt 500 ]]; then
                log_check_pass "API Performance" "Response time: ${duration}ms (< 500ms target)"
            elif [[ $duration -lt 1000 ]]; then
                log_check_warn "API Performance" "Response time: ${duration}ms (acceptable but above 500ms target)"
            else
                log_check_fail "API Performance" "Response time: ${duration}ms (exceeds 1000ms threshold)"
            fi
        else
            log_check_fail "API Performance" "Performance test failed (HTTP $status_code)"
        fi
    else
        log_check_fail "API Performance" "Performance test failed (timeout or unreachable)"
    fi
}

# Check environment-specific configurations
check_environment_config() {
    log_info "Checking environment-specific configurations..."
    
    local env_file="$PROJECT_ROOT/environments/$ENVIRONMENT/.env.$ENVIRONMENT"
    
    if [[ -f "$env_file" ]]; then
        log_check_pass "Environment Config" "Environment file exists: $env_file"
        
        # Check for required environment variables
        local required_vars=("CC_ENVIRONMENT" "CC_DATABASE_URL" "CC_REDIS_URL" "CC_SECRET_KEY")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" "$env_file"; then
                missing_vars+=("$var")
            fi
        done
        
        if [[ ${#missing_vars[@]} -eq 0 ]]; then
            log_check_pass "Required Variables" "All required environment variables are present"
        else
            log_check_fail "Required Variables" "Missing variables: ${missing_vars[*]}"
        fi
    else
        log_check_fail "Environment Config" "Environment file not found: $env_file"
    fi
}

# Check Docker containers (if applicable)
check_docker_containers() {
    if command -v docker &> /dev/null; then
        log_info "Checking Docker containers..."
        
        local containers
        if [[ "$ENVIRONMENT" == "development" ]]; then
            containers=$(docker ps --filter "name=culture_connect" --format "{{.Names}}" 2>/dev/null || true)
        elif [[ "$ENVIRONMENT" == "staging" ]]; then
            containers=$(docker stack ps culture-connect-staging --format "{{.Name}}" 2>/dev/null || true)
        else
            containers=$(docker ps --filter "name=culture-connect" --format "{{.Names}}" 2>/dev/null || true)
        fi
        
        if [[ -n "$containers" ]]; then
            log_check_pass "Docker Containers" "Containers are running: $(echo $containers | tr '\n' ' ')"
        else
            log_check_warn "Docker Containers" "No Culture Connect containers found running"
        fi
    else
        log_check_warn "Docker Containers" "Docker not available for container check"
    fi
}

# Check disk space
check_disk_space() {
    log_info "Checking disk space..."
    
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $disk_usage -lt 80 ]]; then
        log_check_pass "Disk Space" "Disk usage: ${disk_usage}% (< 80% threshold)"
    elif [[ $disk_usage -lt 90 ]]; then
        log_check_warn "Disk Space" "Disk usage: ${disk_usage}% (approaching 90% threshold)"
    else
        log_check_fail "Disk Space" "Disk usage: ${disk_usage}% (exceeds 90% threshold)"
    fi
}

# Check memory usage
check_memory_usage() {
    log_info "Checking memory usage..."
    
    if command -v free &> /dev/null; then
        local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        
        if [[ $memory_usage -lt 80 ]]; then
            log_check_pass "Memory Usage" "Memory usage: ${memory_usage}% (< 80% threshold)"
        elif [[ $memory_usage -lt 90 ]]; then
            log_check_warn "Memory Usage" "Memory usage: ${memory_usage}% (approaching 90% threshold)"
        else
            log_check_fail "Memory Usage" "Memory usage: ${memory_usage}% (exceeds 90% threshold)"
        fi
    else
        log_check_warn "Memory Usage" "Memory check not available (free command not found)"
    fi
}

# =============================================================================
# MAIN HEALTH CHECK FUNCTION
# =============================================================================

run_health_checks() {
    log_info "Starting comprehensive health checks for $ENVIRONMENT environment"
    log_info "API Base URL: $API_BASE_URL"
    log_info "Timeout: ${TIMEOUT}s"
    echo
    
    # Run all health checks
    check_api_health
    check_database_health
    check_redis_health
    check_celery_health
    check_api_docs
    check_api_performance
    check_environment_config
    check_docker_containers
    check_disk_space
    check_memory_usage
    
    echo
    log_info "=== Health Check Summary ==="
    log_info "Checks Passed: $HEALTH_CHECKS_PASSED"
    log_info "Checks Failed: $HEALTH_CHECKS_FAILED"
    log_info "Total Checks: $((HEALTH_CHECKS_PASSED + HEALTH_CHECKS_FAILED))"
    
    if [[ $HEALTH_CHECKS_FAILED -eq 0 ]]; then
        log_success "All health checks passed! System is ready."
        return 0
    else
        log_error "$HEALTH_CHECKS_FAILED health check(s) failed. System may not be ready."
        return 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -u|--url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment ENV    Environment to check (default: development)"
            echo "  -u, --url URL           API base URL (default: http://localhost:8000)"
            echo "  -t, --timeout SECONDS   Request timeout (default: 30)"
            echo "  -h, --help              Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run health checks
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    run_health_checks
fi
