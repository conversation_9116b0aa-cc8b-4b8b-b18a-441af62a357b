"""
Unit tests for Repository Pagination Features.

This test suite validates the pagination functionality including:
- Offset-based pagination
- Cursor-based pagination
- Filtering and sorting integration
- Performance optimization
"""

import pytest
import base64
import json
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List

from app.repositories.base import (
    PaginationParams, CursorPaginationParams, SortParams, FilterParams,
    QueryResult, CursorQueryResult
)
from app.repositories.enhanced_base import EnhancedBaseRepository
from tests.unit.repositories.test_base_repository import MockModel


@pytest.fixture
def mock_async_session():
    """Create mock async database session."""
    session = AsyncMock()
    session.execute = AsyncMock()
    return session


@pytest.fixture
def enhanced_repository(mock_async_session):
    """Create enhanced repository instance."""
    return EnhancedBaseRepository(MockModel, mock_async_session)


@pytest.mark.asyncio
class TestOffsetPagination:
    """Test offset-based pagination functionality."""

    async def test_get_paginated_basic(self, enhanced_repository, mock_async_session):
        """Test basic paginated query."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(1, 6)]
        
        # Mock count query
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 10
        
        # Mock items query
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        # Setup execute to return different results for different queries
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=1, size=5)
        result = await enhanced_repository.get_paginated(pagination)

        assert isinstance(result, QueryResult)
        assert len(result.items) == 5
        assert result.total == 10
        assert result.page == 1
        assert result.size == 5
        assert result.has_next is True
        assert result.has_previous is False
        assert result.total_pages == 2

    async def test_get_paginated_with_filters(self, enhanced_repository, mock_async_session):
        """Test paginated query with filters."""
        # Mock data
        mock_items = [MockModel(id=1, name="test_item")]
        
        # Mock responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 1
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=1, size=10)
        filters = [FilterParams(field="name", operator="like", value="test")]
        
        result = await enhanced_repository.get_paginated(pagination, filters=filters)

        assert len(result.items) == 1
        assert result.total == 1
        assert result.has_next is False

    async def test_get_paginated_with_sorting(self, enhanced_repository, mock_async_session):
        """Test paginated query with sorting."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(3, 0, -1)]
        
        # Mock responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 3
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=1, size=10)
        sorts = [SortParams(field="name", direction="desc")]
        
        result = await enhanced_repository.get_paginated(pagination, sorts=sorts)

        assert len(result.items) == 3
        # Items should be in descending order by name
        assert result.items[0].name == "item_3"
        assert result.items[1].name == "item_2"
        assert result.items[2].name == "item_1"

    async def test_get_paginated_second_page(self, enhanced_repository, mock_async_session):
        """Test paginated query for second page."""
        # Mock data for second page
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(6, 11)]
        
        # Mock responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 15
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=2, size=5)
        result = await enhanced_repository.get_paginated(pagination)

        assert result.page == 2
        assert result.has_next is True
        assert result.has_previous is True
        assert len(result.items) == 5

    async def test_get_paginated_last_page(self, enhanced_repository, mock_async_session):
        """Test paginated query for last page."""
        # Mock data for last page (partial)
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(11, 13)]
        
        # Mock responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 12
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=3, size=5)
        result = await enhanced_repository.get_paginated(pagination)

        assert result.page == 3
        assert result.has_next is False
        assert result.has_previous is True
        assert len(result.items) == 2


@pytest.mark.asyncio
class TestCursorPagination:
    """Test cursor-based pagination functionality."""

    async def test_get_cursor_paginated_first_page(self, enhanced_repository, mock_async_session):
        """Test cursor-based pagination for first page."""
        # Mock data (6 items, but we request 5 to test has_next)
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(1, 7)]
        
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = mock_items
        mock_async_session.execute.return_value = mock_result

        pagination = CursorPaginationParams(size=5)
        result = await enhanced_repository.get_cursor_paginated(pagination)

        assert isinstance(result, CursorQueryResult)
        assert len(result.items) == 5  # Should exclude the extra item
        assert result.has_next is True
        assert result.has_previous is False
        assert result.next_cursor is not None
        assert result.previous_cursor is None

    async def test_get_cursor_paginated_with_cursor(self, enhanced_repository, mock_async_session):
        """Test cursor-based pagination with existing cursor."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(6, 11)]
        
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = mock_items
        mock_async_session.execute.return_value = mock_result

        # Create a cursor for ID 5
        cursor = enhanced_repository._encode_cursor(5)
        pagination = CursorPaginationParams(cursor=cursor, size=5)
        
        result = await enhanced_repository.get_cursor_paginated(pagination)

        assert len(result.items) == 5
        assert result.has_previous is True
        assert result.previous_cursor is not None

    async def test_get_cursor_paginated_backward(self, enhanced_repository, mock_async_session):
        """Test cursor-based pagination in backward direction."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(5, 0, -1)]
        
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = mock_items
        mock_async_session.execute.return_value = mock_result

        # Create a cursor for ID 10
        cursor = enhanced_repository._encode_cursor(10)
        pagination = CursorPaginationParams(cursor=cursor, direction="backward", size=5)
        
        result = await enhanced_repository.get_cursor_paginated(pagination)

        assert len(result.items) == 5
        assert result.has_previous is True

    async def test_cursor_encoding_decoding(self, enhanced_repository):
        """Test cursor encoding and decoding."""
        # Test with integer value
        original_value = 123
        encoded = enhanced_repository._encode_cursor(original_value)
        decoded = enhanced_repository._decode_cursor(encoded)
        
        assert decoded == str(original_value)  # Values are stored as strings
        
        # Test with string value
        original_value = "test_cursor"
        encoded = enhanced_repository._encode_cursor(original_value)
        decoded = enhanced_repository._decode_cursor(encoded)
        
        assert decoded == original_value

    async def test_cursor_pagination_invalid_cursor(self, enhanced_repository, mock_async_session):
        """Test cursor pagination with invalid cursor."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"item_{i}") for i in range(1, 6)]
        
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = mock_items
        mock_async_session.execute.return_value = mock_result

        # Use invalid cursor
        pagination = CursorPaginationParams(cursor="invalid_cursor", size=5)
        
        # Should not raise error, just ignore invalid cursor
        result = await enhanced_repository.get_cursor_paginated(pagination)
        
        assert len(result.items) == 5

    async def test_cursor_pagination_invalid_field(self, enhanced_repository):
        """Test cursor pagination with invalid cursor field."""
        pagination = CursorPaginationParams(size=5)
        
        with pytest.raises(Exception):  # Should raise RepositoryError
            await enhanced_repository.get_cursor_paginated(
                pagination, 
                cursor_field="nonexistent_field"
            )


@pytest.mark.asyncio
class TestFilterAndSortBuilding:
    """Test filter and sort condition building."""

    def test_build_filter_conditions_single(self, enhanced_repository):
        """Test building single filter condition."""
        filters = [FilterParams(field="name", operator="eq", value="test")]
        
        # Mock the model to have the field
        with patch.object(MockModel, 'name', create=True):
            condition = enhanced_repository._build_filter_conditions(filters)
            assert condition is not None

    def test_build_filter_conditions_multiple(self, enhanced_repository):
        """Test building multiple filter conditions."""
        filters = [
            FilterParams(field="name", operator="eq", value="test"),
            FilterParams(field="id", operator="gt", value=5)
        ]
        
        # Mock the model to have the fields
        with patch.object(MockModel, 'name', create=True), \
             patch.object(MockModel, 'id', create=True):
            condition = enhanced_repository._build_filter_conditions(filters)
            assert condition is not None

    def test_build_filter_conditions_operators(self, enhanced_repository):
        """Test different filter operators."""
        operators_to_test = ["eq", "ne", "gt", "gte", "lt", "lte", "like", "ilike", "in", "not_in"]
        
        for operator in operators_to_test:
            filters = [FilterParams(field="name", operator=operator, value="test")]
            
            with patch.object(MockModel, 'name', create=True):
                condition = enhanced_repository._build_filter_conditions(filters)
                # Should not raise error for any valid operator
                assert condition is not None or operator in ["in", "not_in"]  # These need list values

    def test_build_sort_conditions(self, enhanced_repository):
        """Test building sort conditions."""
        sorts = [
            SortParams(field="name", direction="asc"),
            SortParams(field="created_at", direction="desc")
        ]
        
        # Mock the model to have the fields
        with patch.object(MockModel, 'name', create=True), \
             patch.object(MockModel, 'created_at', create=True):
            order_expressions = enhanced_repository._build_sort_conditions(sorts)
            assert len(order_expressions) == 2

    def test_build_filter_conditions_nonexistent_field(self, enhanced_repository):
        """Test filter with nonexistent field."""
        filters = [FilterParams(field="nonexistent", operator="eq", value="test")]
        
        condition = enhanced_repository._build_filter_conditions(filters)
        assert condition is None

    def test_build_sort_conditions_nonexistent_field(self, enhanced_repository):
        """Test sort with nonexistent field."""
        sorts = [SortParams(field="nonexistent", direction="asc")]
        
        order_expressions = enhanced_repository._build_sort_conditions(sorts)
        assert len(order_expressions) == 0


@pytest.mark.asyncio
class TestPaginationIntegration:
    """Test pagination integration scenarios."""

    async def test_pagination_with_filters_and_sorts(self, enhanced_repository, mock_async_session):
        """Test pagination combined with filters and sorts."""
        # Mock data
        mock_items = [MockModel(id=i, name=f"test_item_{i}") for i in range(1, 4)]
        
        # Mock responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 3
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = mock_items
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=1, size=10)
        filters = [FilterParams(field="name", operator="like", value="test")]
        sorts = [SortParams(field="name", direction="asc")]
        
        result = await enhanced_repository.get_paginated(
            pagination, 
            filters=filters, 
            sorts=sorts
        )

        assert len(result.items) == 3
        assert result.total == 3
        assert result.has_next is False

    async def test_empty_result_pagination(self, enhanced_repository, mock_async_session):
        """Test pagination with empty results."""
        # Mock empty responses
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 0
        
        mock_items_result = AsyncMock()
        mock_items_result.scalars.return_value.all.return_value = []
        
        mock_async_session.execute.side_effect = [mock_count_result, mock_items_result]

        pagination = PaginationParams(page=1, size=10)
        result = await enhanced_repository.get_paginated(pagination)

        assert len(result.items) == 0
        assert result.total == 0
        assert result.has_next is False
        assert result.has_previous is False
        assert result.total_pages == 0
