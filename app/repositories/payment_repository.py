"""
Payment Repository for Culture Connect Backend API.

This module provides comprehensive repository classes for payment-related database operations:
- PaymentRepository: Core payment transaction management with multi-provider support
- PaymentMethodRepository: User payment method management with encrypted storage
- Advanced query optimization with composite index utilization
- Business logic integration for payment workflows and status transitions
- Performance targets: <500ms payment creation, <100ms status updates, <200ms queries

Implements Phase 1 Payment & Transaction Management System repository layer.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, case, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.payment import Payment, PaymentMethod, PaymentStatus, PaymentMethodType
from app.core.payment.config import PaymentProviderType
from app.repositories.base import BaseRepository, PaginationParams, FilterParams, QueryResult, RepositoryError
import logging

logger = logging.getLogger(__name__)


class PaymentRepository(BaseRepository[Payment]):
    """
    Repository for payment transaction management.

    Provides optimized database operations for payment processing with:
    - Multi-provider payment support (Stripe, Paystack, Busha)
    - Performance-optimized queries using composite indexes
    - Payment status transition validation and business rules
    - Geo-location based payment filtering and analytics
    - Comprehensive audit logging and security features

    Performance targets:
    - Payment creation: <500ms
    - Status updates: <100ms
    - Financial queries: <200ms
    """

    def __init__(self, db: AsyncSession):
        super().__init__(Payment, db)
        self.logger = logging.getLogger(f"{__name__}.PaymentRepository")

    async def create_payment(
        self,
        booking_id: int,
        user_id: int,
        vendor_id: int,
        amount: Decimal,
        currency: str = "NGN",
        provider: PaymentProviderType = PaymentProviderType.PAYSTACK,
        payment_method_id: Optional[int] = None,
        **kwargs
    ) -> Payment:
        """
        Create a new payment with optimized performance (<500ms target).

        Args:
            booking_id: Associated booking ID
            user_id: Customer user ID
            vendor_id: Service vendor ID
            amount: Payment amount
            currency: Payment currency (default: NGN)
            provider: Payment provider (default: Paystack)
            payment_method_id: Optional payment method ID
            **kwargs: Additional payment fields

        Returns:
            Created payment instance

        Raises:
            RepositoryError: If payment creation fails
        """
        try:
            # Generate unique transaction reference
            transaction_reference = f"CC-{datetime.now().strftime('%Y%m%d')}-{user_id}-{booking_id}"

            payment_data = {
                "booking_id": booking_id,
                "user_id": user_id,
                "vendor_id": vendor_id,
                "amount": amount,
                "currency": currency,
                "provider": provider,
                "payment_method_id": payment_method_id,
                "transaction_reference": transaction_reference,
                "status": PaymentStatus.PENDING,
                **kwargs
            }

            payment = await self.create(payment_data)

            self.logger.info(
                "Payment created successfully",
                extra={
                    "payment_id": payment.id,
                    "transaction_reference": payment.transaction_reference,
                    "amount": float(amount),
                    "currency": currency,
                    "provider": provider.value,
                    "user_id": user_id,
                    "vendor_id": vendor_id
                }
            )

            return payment

        except Exception as e:
            self.logger.error(
                "Failed to create payment",
                extra={
                    "booking_id": booking_id,
                    "user_id": user_id,
                    "vendor_id": vendor_id,
                    "amount": float(amount),
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create payment: {str(e)}", e)

    async def update_payment_status(
        self,
        payment_id: int,
        status: PaymentStatus,
        provider_reference: Optional[str] = None,
        failure_reason: Optional[str] = None,
        **kwargs
    ) -> Optional[Payment]:
        """
        Update payment status with optimized performance (<100ms target).

        Args:
            payment_id: Payment ID to update
            status: New payment status
            provider_reference: Provider transaction reference
            failure_reason: Failure reason if status is failed
            **kwargs: Additional fields to update

        Returns:
            Updated payment instance or None if not found
        """
        try:
            update_data = {
                "status": status,
                **kwargs
            }

            if provider_reference:
                update_data["provider_reference"] = provider_reference

            if failure_reason:
                update_data["failure_reason"] = failure_reason

            if status == PaymentStatus.COMPLETED:
                update_data["paid_at"] = datetime.now(timezone.utc)
            elif status in [PaymentStatus.FAILED, PaymentStatus.CANCELLED]:
                update_data["retry_count"] = Payment.retry_count + 1

            payment = await self.update(payment_id, update_data)

            if payment:
                self.logger.info(
                    "Payment status updated",
                    extra={
                        "payment_id": payment_id,
                        "old_status": payment.status.value if hasattr(payment, 'status') else None,
                        "new_status": status.value,
                        "provider_reference": provider_reference
                    }
                )

            return payment

        except Exception as e:
            self.logger.error(
                "Failed to update payment status",
                extra={
                    "payment_id": payment_id,
                    "status": status.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update payment status: {str(e)}", e)

    async def get_payments_by_user(
        self,
        user_id: int,
        status: Optional[PaymentStatus] = None,
        provider: Optional[PaymentProviderType] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Payment]:
        """
        Get payments by user with optimized filtering.

        Uses idx_payment_user_status composite index for performance.

        Args:
            user_id: User ID to filter by
            status: Optional payment status filter
            provider: Optional payment provider filter
            pagination: Pagination parameters

        Returns:
            Query result with payments and metadata
        """
        try:
            query = select(Payment).where(Payment.user_id == user_id)

            if status:
                query = query.where(Payment.status == status)

            if provider:
                query = query.where(Payment.provider == provider)

            # Order by creation date (newest first) for optimal index usage
            query = query.order_by(desc(Payment.created_at))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get payments by user",
                extra={"user_id": user_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get payments by user: {str(e)}", e)

    async def get_payments_by_vendor(
        self,
        vendor_id: int,
        status: Optional[PaymentStatus] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[Payment]:
        """
        Get payments by vendor with date filtering.

        Uses idx_payment_vendor_status and idx_payment_date_status indexes.

        Args:
            vendor_id: Vendor ID to filter by
            status: Optional payment status filter
            date_from: Start date filter
            date_to: End date filter
            pagination: Pagination parameters

        Returns:
            Query result with payments and metadata
        """
        try:
            query = select(Payment).where(Payment.vendor_id == vendor_id)

            if status:
                query = query.where(Payment.status == status)

            if date_from:
                query = query.where(Payment.created_at >= date_from)

            if date_to:
                query = query.where(Payment.created_at <= date_to)

            # Order by creation date for optimal index usage
            query = query.order_by(desc(Payment.created_at))

            return await self.get_paginated(query, pagination)

        except Exception as e:
            self.logger.error(
                "Failed to get payments by vendor",
                extra={"vendor_id": vendor_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get payments by vendor: {str(e)}", e)

    async def get_payment_by_reference(self, transaction_reference: str) -> Optional[Payment]:
        """
        Get payment by transaction reference.

        Uses idx_payment_reference_provider index for performance.

        Args:
            transaction_reference: Unique transaction reference

        Returns:
            Payment instance or None if not found
        """
        try:
            query = select(Payment).where(Payment.transaction_reference == transaction_reference)
            result = await self.db.execute(query)
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(
                "Failed to get payment by reference",
                extra={"transaction_reference": transaction_reference, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get payment by reference: {str(e)}", e)

    async def get_financial_summary(
        self,
        vendor_id: Optional[int] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get financial summary with optimized aggregation queries (<200ms target).

        Uses idx_payment_currency_amount and idx_payment_date_status indexes.

        Args:
            vendor_id: Optional vendor ID filter
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Financial summary dictionary
        """
        try:
            base_query = select(Payment).where(Payment.currency == currency)

            if vendor_id:
                base_query = base_query.where(Payment.vendor_id == vendor_id)

            if date_from:
                base_query = base_query.where(Payment.created_at >= date_from)

            if date_to:
                base_query = base_query.where(Payment.created_at <= date_to)

            # Aggregate query for financial metrics
            summary_query = select(
                func.count(Payment.id).label('total_payments'),
                func.sum(Payment.amount).label('total_amount'),
                func.sum(Payment.platform_fee).label('total_platform_fees'),
                func.sum(Payment.provider_fee).label('total_provider_fees'),
                func.sum(Payment.net_amount).label('total_net_amount'),
                func.count(case((Payment.status == PaymentStatus.COMPLETED, 1))).label('completed_payments'),
                func.count(case((Payment.status == PaymentStatus.FAILED, 1))).label('failed_payments'),
                func.count(case((Payment.status == PaymentStatus.PENDING, 1))).label('pending_payments')
            ).select_from(base_query.subquery())

            result = await self.db.execute(summary_query)
            row = result.first()

            return {
                "total_payments": row.total_payments or 0,
                "total_amount": float(row.total_amount or 0),
                "total_platform_fees": float(row.total_platform_fees or 0),
                "total_provider_fees": float(row.total_provider_fees or 0),
                "total_net_amount": float(row.total_net_amount or 0),
                "completed_payments": row.completed_payments or 0,
                "failed_payments": row.failed_payments or 0,
                "pending_payments": row.pending_payments or 0,
                "success_rate": (row.completed_payments / row.total_payments * 100) if row.total_payments > 0 else 0,
                "currency": currency
            }

        except Exception as e:
            self.logger.error(
                "Failed to get financial summary",
                extra={"vendor_id": vendor_id, "currency": currency, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get financial summary: {str(e)}", e)


class PaymentMethodRepository(BaseRepository[PaymentMethod]):
    """
    Repository for payment method management.

    Provides secure database operations for payment methods with:
    - Encrypted sensitive data storage and retrieval
    - Default payment method management
    - Payment method validation and expiry checking
    - Usage tracking and failure monitoring
    - Provider-specific payment method handling

    Security features:
    - PCI compliance ready with encrypted metadata storage
    - Data masking for sensitive payment information
    - Comprehensive audit logging for all operations
    """

    def __init__(self, db: AsyncSession):
        super().__init__(PaymentMethod, db)
        self.logger = logging.getLogger(f"{__name__}.PaymentMethodRepository")

    async def create_payment_method(
        self,
        user_id: int,
        method_type: PaymentMethodType,
        provider: PaymentProviderType,
        display_name: str,
        encrypted_metadata: Optional[str] = None,
        **kwargs
    ) -> PaymentMethod:
        """
        Create a new payment method with security features.

        Args:
            user_id: User ID who owns the payment method
            method_type: Type of payment method
            provider: Payment provider
            display_name: User-friendly display name
            encrypted_metadata: Encrypted sensitive payment data
            **kwargs: Additional payment method fields

        Returns:
            Created payment method instance
        """
        try:
            payment_method_data = {
                "user_id": user_id,
                "type": method_type,
                "provider": provider,
                "display_name": display_name,
                "encrypted_metadata": encrypted_metadata,
                "is_active": True,
                "is_verified": False,
                **kwargs
            }

            payment_method = await self.create(payment_method_data)

            self.logger.info(
                "Payment method created successfully",
                extra={
                    "payment_method_id": payment_method.id,
                    "user_id": user_id,
                    "type": method_type.value,
                    "provider": provider.value,
                    "display_name": display_name
                }
            )

            return payment_method

        except Exception as e:
            self.logger.error(
                "Failed to create payment method",
                extra={
                    "user_id": user_id,
                    "type": method_type.value,
                    "provider": provider.value,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to create payment method: {str(e)}", e)

    async def get_user_payment_methods(
        self,
        user_id: int,
        active_only: bool = True,
        provider: Optional[PaymentProviderType] = None
    ) -> List[PaymentMethod]:
        """
        Get user's payment methods with filtering.

        Uses idx_payment_method_user_active composite index.

        Args:
            user_id: User ID to filter by
            active_only: Whether to return only active payment methods
            provider: Optional provider filter

        Returns:
            List of payment methods
        """
        try:
            query = select(PaymentMethod).where(PaymentMethod.user_id == user_id)

            if active_only:
                query = query.where(PaymentMethod.is_active == True)

            if provider:
                query = query.where(PaymentMethod.provider == provider)

            # Order by default first, then by last used
            query = query.order_by(
                desc(PaymentMethod.is_default),
                desc(PaymentMethod.last_used_at)
            )

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get user payment methods",
                extra={"user_id": user_id, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get user payment methods: {str(e)}", e)

    async def set_default_payment_method(
        self,
        user_id: int,
        payment_method_id: int
    ) -> Optional[PaymentMethod]:
        """
        Set a payment method as default for a user.

        Args:
            user_id: User ID
            payment_method_id: Payment method ID to set as default

        Returns:
            Updated payment method or None if not found
        """
        try:
            # First, unset all other default payment methods for the user
            await self.db.execute(
                update(PaymentMethod)
                .where(
                    and_(
                        PaymentMethod.user_id == user_id,
                        PaymentMethod.id != payment_method_id
                    )
                )
                .values(is_default=False)
            )

            # Set the specified payment method as default
            payment_method = await self.update(payment_method_id, {"is_default": True})

            if payment_method:
                self.logger.info(
                    "Default payment method updated",
                    extra={
                        "user_id": user_id,
                        "payment_method_id": payment_method_id
                    }
                )

            return payment_method

        except Exception as e:
            self.logger.error(
                "Failed to set default payment method",
                extra={
                    "user_id": user_id,
                    "payment_method_id": payment_method_id,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to set default payment method: {str(e)}", e)

    async def update_usage_tracking(
        self,
        payment_method_id: int,
        success: bool = True
    ) -> Optional[PaymentMethod]:
        """
        Update payment method usage tracking.

        Args:
            payment_method_id: Payment method ID
            success: Whether the usage was successful

        Returns:
            Updated payment method or None if not found
        """
        try:
            update_data = {
                "last_used_at": datetime.now(timezone.utc),
                "usage_count": PaymentMethod.usage_count + 1
            }

            if not success:
                update_data.update({
                    "failure_count": PaymentMethod.failure_count + 1,
                    "last_failure_at": datetime.now(timezone.utc)
                })

            return await self.update(payment_method_id, update_data)

        except Exception as e:
            self.logger.error(
                "Failed to update usage tracking",
                extra={
                    "payment_method_id": payment_method_id,
                    "success": success,
                    "error": str(e)
                }
            )
            raise RepositoryError(f"Failed to update usage tracking: {str(e)}", e)

    async def get_expired_payment_methods(
        self,
        days_ahead: int = 30
    ) -> List[PaymentMethod]:
        """
        Get payment methods that are expired or expiring soon.

        Args:
            days_ahead: Number of days ahead to check for expiry

        Returns:
            List of expired/expiring payment methods
        """
        try:
            current_date = datetime.now(timezone.utc)
            expiry_threshold = current_date + timedelta(days=days_ahead)

            query = select(PaymentMethod).where(
                and_(
                    PaymentMethod.type == PaymentMethodType.CARD,
                    PaymentMethod.is_active == True,
                    or_(
                        # Already expired
                        and_(
                            PaymentMethod.expiry_year < current_date.year,
                        ),
                        and_(
                            PaymentMethod.expiry_year == current_date.year,
                            PaymentMethod.expiry_month < current_date.month
                        ),
                        # Expiring soon
                        and_(
                            PaymentMethod.expiry_year == expiry_threshold.year,
                            PaymentMethod.expiry_month <= expiry_threshold.month
                        )
                    )
                )
            )

            result = await self.db.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                "Failed to get expired payment methods",
                extra={"days_ahead": days_ahead, "error": str(e)}
            )
            raise RepositoryError(f"Failed to get expired payment methods: {str(e)}", e)
