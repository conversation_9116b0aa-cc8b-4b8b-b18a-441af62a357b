"""
Unit tests for MarketplaceOptimizationService.

This module provides comprehensive unit tests for the marketplace optimization service
implementing Task 3.2.2 Phase 5 requirements with >80% test coverage.

Test Coverage:
- Optimization dashboard generation
- Comprehensive analysis workflows
- Vendor optimization overview
- Cross-component integration
- Error handling and edge cases
- Service method validation
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, date
from decimal import Decimal

from app.services.marketplace_optimization import MarketplaceOptimizationService
from app.schemas.marketplace_optimization import (
    MarketplaceOptimizationDashboard, OptimizationAnalysisRequest,
    SEOAnalysisResponse, PerformanceMetricsResponse, CompetitiveAnalysisResponse,
    MobileOptimizationResponse, OptimizationRecommendationResponse
)


class TestableMarketplaceOptimizationService(MarketplaceOptimizationService):
    """Testable version of MarketplaceOptimizationService with implemented abstract methods."""

    async def validate_create_data(self, data: dict) -> dict:
        """Mock implementation of abstract method."""
        return data

    async def validate_update_data(self, data: dict, record_id: int = None) -> dict:
        """Mock implementation of abstract method."""
        return data


class TestMarketplaceOptimizationService:
    """Test suite for MarketplaceOptimizationService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_db_session):
        """Create MarketplaceOptimizationService instance with mocked dependencies."""
        return TestableMarketplaceOptimizationService(mock_db_session)

    @pytest.fixture
    def sample_seo_analysis(self):
        """Sample SEO analysis response."""
        return SEOAnalysisResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            overall_seo_score=Decimal("75.50"),
            title_score=Decimal("80.00"),
            description_score=Decimal("70.00"),
            media_score=Decimal("85.00"),
            keyword_score=Decimal("65.00"),
            completeness_score=Decimal("90.00"),
            primary_keywords=["cultural tour", "heritage experience"],
            keyword_density={"cultural": 2.5, "tour": 1.8},
            missing_keywords=["authentic", "traditional"],
            content_quality_metrics={"word_count": 250, "sentence_length": 15},
            readability_score=Decimal("78.00"),
            meta_title_analysis={"length": 55, "keyword_present": True},
            meta_description_analysis={"length": 145, "call_to_action": True},
            analysis_date=datetime.now(),
            analysis_version="1.0"
        )

    @pytest.fixture
    def sample_performance_metrics(self):
        """Sample performance metrics response."""
        return PerformanceMetricsResponse(
            id=1,
            service_id=1,
            vendor_id=1,
            views_count=1500,
            clicks_count=150,
            conversion_rate=Decimal("10.00"),
            bounce_rate=Decimal("25.00"),
            average_session_duration=Decimal("180.50"),
            engagement_score=Decimal("85.00"),
            performance_score=Decimal("78.50"),
            click_through_rate=Decimal("10.00"),
            cost_per_click=Decimal("2.50"),
            return_on_investment=Decimal("150.00"),
            metric_date=date.today(),
            metric_period="daily"
        )

    @pytest.fixture
    def sample_optimization_request(self):
        """Sample optimization analysis request."""
        return OptimizationAnalysisRequest(
            service_id=1,
            analysis_types=["seo", "performance", "competitive", "mobile"],
            include_recommendations=True,
            analysis_depth="comprehensive"
        )

    @pytest.mark.asyncio
    async def test_get_optimization_dashboard_success(self, service, sample_seo_analysis, sample_performance_metrics):
        """Test successful optimization dashboard generation."""
        service_id = 1
        include_recommendations = True

        # Mock repository methods
        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'seo_repository') as mock_seo_repo, \
             patch.object(service, 'performance_repository') as mock_perf_repo, \
             patch.object(service, 'competitive_repository') as mock_comp_repo, \
             patch.object(service, 'mobile_repository') as mock_mobile_repo, \
             patch.object(service, 'repository') as mock_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = MagicMock(id=service_id, title="Test Service")

            # Mock optimization data
            mock_seo_repo.get_latest_by_service.return_value = sample_seo_analysis
            mock_perf_repo.get_latest_by_service.return_value = sample_performance_metrics
            mock_comp_repo.get_latest_by_service.return_value = None
            mock_mobile_repo.get_latest_by_service.return_value = None

            # Mock recommendations
            mock_repo.get_by_service.return_value = []

            # Execute method
            result = await service.get_optimization_dashboard(service_id, include_recommendations)

            # Assertions
            assert isinstance(result, MarketplaceOptimizationDashboard)
            assert result.service_id == service_id
            assert result.overall_optimization_score > 0
            assert result.seo_analysis == sample_seo_analysis
            assert result.performance_metrics == sample_performance_metrics
            assert isinstance(result.recommendations, list)

            # Verify repository calls
            mock_service_repo.get_by_id.assert_called_once_with(service_id)
            mock_seo_repo.get_latest_by_service.assert_called_once_with(service_id)
            mock_perf_repo.get_latest_by_service.assert_called_once_with(service_id)

    @pytest.mark.asyncio
    async def test_get_optimization_dashboard_service_not_found(self, service):
        """Test optimization dashboard when service not found."""
        service_id = 999

        with patch.object(service, 'service_repository') as mock_service_repo:
            mock_service_repo.get_by_id.return_value = None

            with pytest.raises(Exception) as exc_info:
                await service.get_optimization_dashboard(service_id, True)

            assert "not found" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_run_comprehensive_analysis_success(self, service, sample_optimization_request):
        """Test successful comprehensive analysis execution."""
        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'seo_service') as mock_seo_service, \
             patch.object(service, 'performance_service') as mock_perf_service, \
             patch.object(service, 'competitive_service') as mock_comp_service, \
             patch.object(service, 'mobile_service') as mock_mobile_service:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = MagicMock(id=1, title="Test Service")

            # Mock analysis services
            mock_seo_service.analyze_service_seo.return_value = MagicMock()
            mock_perf_service.calculate_service_performance.return_value = MagicMock()
            mock_comp_service.analyze_market_position.return_value = MagicMock()
            mock_mobile_service.analyze_mobile_optimization.return_value = MagicMock()

            # Execute method
            result = await service.run_comprehensive_analysis(sample_optimization_request)

            # Assertions
            assert isinstance(result, MarketplaceOptimizationDashboard)
            assert result.service_id == sample_optimization_request.service_id

            # Verify analysis services were called
            mock_seo_service.analyze_service_seo.assert_called_once()
            mock_perf_service.calculate_service_performance.assert_called_once()
            mock_comp_service.analyze_market_position.assert_called_once()
            mock_mobile_service.analyze_mobile_optimization.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_comprehensive_analysis_partial_types(self, service):
        """Test comprehensive analysis with partial analysis types."""
        request = OptimizationAnalysisRequest(
            service_id=1,
            analysis_types=["seo", "performance"],  # Only SEO and performance
            include_recommendations=True,
            analysis_depth="standard"
        )

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'seo_service') as mock_seo_service, \
             patch.object(service, 'performance_service') as mock_perf_service, \
             patch.object(service, 'competitive_service') as mock_comp_service, \
             patch.object(service, 'mobile_service') as mock_mobile_service:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = MagicMock(id=1, title="Test Service")

            # Mock analysis services
            mock_seo_service.analyze_service_seo.return_value = MagicMock()
            mock_perf_service.calculate_service_performance.return_value = MagicMock()

            # Execute method
            result = await service.run_comprehensive_analysis(request)

            # Assertions
            assert isinstance(result, MarketplaceOptimizationDashboard)

            # Verify only requested analysis services were called
            mock_seo_service.analyze_service_seo.assert_called_once()
            mock_perf_service.calculate_service_performance.assert_called_once()
            mock_comp_service.analyze_market_position.assert_not_called()
            mock_mobile_service.analyze_mobile_optimization.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_vendor_optimization_overview_success(self, service):
        """Test successful vendor optimization overview generation."""
        vendor_id = 1
        include_services = True
        days = 30

        with patch.object(service, 'vendor_repository') as mock_vendor_repo, \
             patch.object(service, 'service_repository') as mock_service_repo:

            # Mock vendor exists
            mock_vendor_repo.get_by_id.return_value = MagicMock(id=vendor_id, business_name="Test Vendor")

            # Mock vendor services
            mock_service_repo.get_by_vendor.return_value = [
                MagicMock(id=1, title="Service 1"),
                MagicMock(id=2, title="Service 2")
            ]

            # Execute method
            result = await service.get_vendor_optimization_overview(vendor_id, include_services, days)

            # Assertions
            assert isinstance(result, dict)
            assert "vendor_id" in result
            assert "optimization_summary" in result
            assert "pending_recommendations" in result
            assert result["vendor_id"] == vendor_id

            # Verify repository calls
            mock_vendor_repo.get_by_id.assert_called_once_with(vendor_id)
            mock_service_repo.get_by_vendor.assert_called_once_with(vendor_id)

    @pytest.mark.asyncio
    async def test_get_vendor_optimization_overview_vendor_not_found(self, service):
        """Test vendor optimization overview when vendor not found."""
        vendor_id = 999

        with patch.object(service, 'vendor_repository') as mock_vendor_repo:
            mock_vendor_repo.get_by_id.return_value = None

            with pytest.raises(Exception) as exc_info:
                await service.get_vendor_optimization_overview(vendor_id, True, 30)

            assert "not found" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_calculate_overall_optimization_score(self, service, sample_seo_analysis, sample_performance_metrics):
        """Test overall optimization score calculation."""
        # Mock optimization data
        optimization_data = {
            "seo_analysis": sample_seo_analysis,
            "performance_metrics": sample_performance_metrics,
            "competitive_analysis": None,
            "mobile_optimization": None
        }

        # Execute method (assuming this is a private method we can test)
        score = service._calculate_overall_optimization_score(optimization_data)

        # Assertions
        assert isinstance(score, (int, float, Decimal))
        assert 0 <= score <= 100

    @pytest.mark.asyncio
    async def test_generate_cross_component_recommendations(self, service):
        """Test cross-component recommendations generation."""
        optimization_data = {
            "seo_analysis": MagicMock(overall_seo_score=Decimal("60.0")),
            "performance_metrics": MagicMock(performance_score=Decimal("70.0")),
            "competitive_analysis": None,
            "mobile_optimization": None
        }

        # Execute method (assuming this is a private method we can test)
        recommendations = service._generate_cross_component_recommendations(optimization_data)

        # Assertions
        assert isinstance(recommendations, list)
        # Should generate recommendations for low-scoring components
        assert len(recommendations) > 0

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db_session):
        """Test service initialization with dependencies."""
        service = TestableMarketplaceOptimizationService(mock_db_session)

        # Verify service is properly initialized
        assert service._db_session == mock_db_session
        assert hasattr(service, 'service_repository')
        assert hasattr(service, 'seo_repository')
        assert hasattr(service, 'performance_repository')
        assert hasattr(service, 'competitive_repository')
        assert hasattr(service, 'mobile_repository')
        assert hasattr(service, 'repository')

    @pytest.mark.asyncio
    async def test_error_handling_database_error(self, service):
        """Test error handling for database errors."""
        service_id = 1

        with patch.object(service, 'service_repository') as mock_service_repo:
            # Mock database error
            mock_service_repo.get_by_id.side_effect = Exception("Database connection error")

            with pytest.raises(Exception) as exc_info:
                await service.get_optimization_dashboard(service_id, True)

            assert "Database connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_optimization_dashboard_without_recommendations(self, service, sample_seo_analysis):
        """Test optimization dashboard generation without recommendations."""
        service_id = 1
        include_recommendations = False

        with patch.object(service, 'service_repository') as mock_service_repo, \
             patch.object(service, 'seo_repository') as mock_seo_repo, \
             patch.object(service, 'performance_repository') as mock_perf_repo, \
             patch.object(service, 'competitive_repository') as mock_comp_repo, \
             patch.object(service, 'mobile_repository') as mock_mobile_repo, \
             patch.object(service, 'repository') as mock_repo:

            # Mock service exists
            mock_service_repo.get_by_id.return_value = MagicMock(id=service_id, title="Test Service")

            # Mock optimization data
            mock_seo_repo.get_latest_by_service.return_value = sample_seo_analysis
            mock_perf_repo.get_latest_by_service.return_value = None
            mock_comp_repo.get_latest_by_service.return_value = None
            mock_mobile_repo.get_latest_by_service.return_value = None

            # Execute method
            result = await service.get_optimization_dashboard(service_id, include_recommendations)

            # Assertions
            assert isinstance(result, MarketplaceOptimizationDashboard)
            assert result.service_id == service_id
            assert len(result.recommendations) == 0

            # Verify recommendations repository was not called
            mock_repo.get_by_service.assert_not_called()
