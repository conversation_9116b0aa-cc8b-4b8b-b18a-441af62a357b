"""Add JWT authentication tables

Revision ID: jwt_auth_tables
Revises: postgresql_optimized
Create Date: 2025-05-24 08:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'jwt_auth_tables'
down_revision = 'postgresql_optimized'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add JWT authentication tables."""
    
    # Create token_blacklist table
    op.create_table('token_blacklist',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('jti', sa.String(length=255), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('token_type', sa.String(length=20), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=False),
        sa.Column('reason', sa.String(length=100), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for token_blacklist
    op.create_index('idx_token_blacklist_jti', 'token_blacklist', ['jti'], unique=True)
    op.create_index('idx_token_blacklist_user_id', 'token_blacklist', ['user_id'])
    op.create_index('idx_token_blacklist_expires_at', 'token_blacklist', ['expires_at'])
    op.create_index('idx_token_blacklist_user_type', 'token_blacklist', ['user_id', 'token_type'])
    op.create_index(op.f('ix_token_blacklist_id'), 'token_blacklist', ['id'], unique=False)
    op.create_index(op.f('ix_token_blacklist_uuid'), 'token_blacklist', ['uuid'], unique=True)
    op.create_index(op.f('ix_token_blacklist_jti'), 'token_blacklist', ['jti'], unique=True)
    op.create_index(op.f('ix_token_blacklist_user_id'), 'token_blacklist', ['user_id'], unique=False)
    op.create_index(op.f('ix_token_blacklist_created_at'), 'token_blacklist', ['created_at'], unique=False)
    op.create_index(op.f('ix_token_blacklist_updated_at'), 'token_blacklist', ['updated_at'], unique=False)
    op.create_index(op.f('ix_token_blacklist_created_by'), 'token_blacklist', ['created_by'], unique=False)
    op.create_index(op.f('ix_token_blacklist_updated_by'), 'token_blacklist', ['updated_by'], unique=False)
    op.create_index(op.f('ix_token_blacklist_expires_at'), 'token_blacklist', ['expires_at'], unique=False)

    # Create security_events table
    op.create_table('security_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', sa.UUID(), nullable=False),
        sa.Column('event_type', sa.String(length=50), nullable=False),
        sa.Column('event_category', sa.String(length=30), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('correlation_id', sa.String(length=255), nullable=True),
        sa.Column('additional_data', sa.JSON(), nullable=True),
        sa.Column('resolved', sa.Boolean(), nullable=False),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.Column('resolved_by', sa.Integer(), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for security_events
    op.create_index('idx_security_events_type', 'security_events', ['event_type'])
    op.create_index('idx_security_events_category', 'security_events', ['event_category'])
    op.create_index('idx_security_events_severity', 'security_events', ['severity'])
    op.create_index('idx_security_events_user_id', 'security_events', ['user_id'])
    op.create_index('idx_security_events_ip', 'security_events', ['ip_address'])
    op.create_index('idx_security_events_correlation', 'security_events', ['correlation_id'])
    op.create_index('idx_security_events_created', 'security_events', ['created_at'])
    op.create_index('idx_security_events_unresolved', 'security_events', ['resolved'], postgresql_where=sa.text('resolved = false'))
    op.create_index('idx_security_events_user_type', 'security_events', ['user_id', 'event_type'])
    op.create_index(op.f('ix_security_events_id'), 'security_events', ['id'], unique=False)
    op.create_index(op.f('ix_security_events_uuid'), 'security_events', ['uuid'], unique=True)
    op.create_index(op.f('ix_security_events_event_type'), 'security_events', ['event_type'], unique=False)
    op.create_index(op.f('ix_security_events_event_category'), 'security_events', ['event_category'], unique=False)
    op.create_index(op.f('ix_security_events_user_id'), 'security_events', ['user_id'], unique=False)
    op.create_index(op.f('ix_security_events_ip_address'), 'security_events', ['ip_address'], unique=False)
    op.create_index(op.f('ix_security_events_correlation_id'), 'security_events', ['correlation_id'], unique=False)
    op.create_index(op.f('ix_security_events_created_at'), 'security_events', ['created_at'], unique=False)
    op.create_index(op.f('ix_security_events_updated_at'), 'security_events', ['updated_at'], unique=False)
    op.create_index(op.f('ix_security_events_created_by'), 'security_events', ['created_by'], unique=False)
    op.create_index(op.f('ix_security_events_updated_by'), 'security_events', ['updated_by'], unique=False)


def downgrade() -> None:
    """Remove JWT authentication tables."""
    
    # Drop security_events table and indexes
    op.drop_index(op.f('ix_security_events_updated_by'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_created_by'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_updated_at'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_created_at'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_correlation_id'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_ip_address'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_user_id'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_event_category'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_event_type'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_uuid'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_id'), table_name='security_events')
    op.drop_index('idx_security_events_user_type', table_name='security_events')
    op.drop_index('idx_security_events_unresolved', table_name='security_events')
    op.drop_index('idx_security_events_created', table_name='security_events')
    op.drop_index('idx_security_events_correlation', table_name='security_events')
    op.drop_index('idx_security_events_ip', table_name='security_events')
    op.drop_index('idx_security_events_user_id', table_name='security_events')
    op.drop_index('idx_security_events_severity', table_name='security_events')
    op.drop_index('idx_security_events_category', table_name='security_events')
    op.drop_index('idx_security_events_type', table_name='security_events')
    op.drop_table('security_events')
    
    # Drop token_blacklist table and indexes
    op.drop_index(op.f('ix_token_blacklist_expires_at'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_updated_by'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_created_by'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_updated_at'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_created_at'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_user_id'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_jti'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_uuid'), table_name='token_blacklist')
    op.drop_index(op.f('ix_token_blacklist_id'), table_name='token_blacklist')
    op.drop_index('idx_token_blacklist_user_type', table_name='token_blacklist')
    op.drop_index('idx_token_blacklist_expires_at', table_name='token_blacklist')
    op.drop_index('idx_token_blacklist_user_id', table_name='token_blacklist')
    op.drop_index('idx_token_blacklist_jti', table_name='token_blacklist')
    op.drop_table('token_blacklist')
