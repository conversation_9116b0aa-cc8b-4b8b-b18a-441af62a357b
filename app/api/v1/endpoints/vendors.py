"""
Vendor Management API endpoints for Culture Connect Backend API.

This module provides comprehensive vendor management endpoints including:
- Multi-step vendor registration with validation
- Vendor profile management with business logic validation
- Onboarding status and progress tracking
- Document upload and verification coordination
- Public vendor profile for customer viewing

Implements Task 3.1.1 Phase 2 requirements for vendor API endpoints with
production-grade FastAPI integration, authentication, rate limiting, and error handling.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query, Path
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.vendor_service import VendorService
from app.schemas.vendor import (
    VendorRegistrationRequest, VendorRegistrationResponse,
    VendorResponse, VendorUpdate,
    VendorProfileUpdate, VendorProfileResponse,
    VendorDocumentCreate, VendorDocumentResponse,
    OnboardingStepUpdate, OnboardingStatusResponse,
    # Task 3.1.3: Enhanced Profile Management schemas
    VendorProfileManagementRequest, VendorProfileManagementResponse,
    OperatingHoursUpdateRequest, MediaUploadRequest, MediaUploadResponse,
    ProfileCompletionStatus
)
from app.schemas.auth import UserResponse
from app.api.v1.auth import get_current_user
from app.core.permissions import require_permission, require_role
from app.core.logging import correlation_id
from app.models.vendor import DocumentType

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Security scheme
security = HTTPBearer()


@router.post(
    "/register",
    response_model=VendorRegistrationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register as vendor",
    description="Register current user as a vendor with multi-step onboarding workflow"
)
async def register_vendor(
    registration_data: VendorRegistrationRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorRegistrationResponse:
    """
    Register the current user as a vendor with multi-step onboarding workflow.

    This endpoint allows authenticated users to register as vendors in the marketplace.
    The registration process includes business information collection, verification
    requirements, and a structured onboarding workflow.

    **Rate Limiting**: 5 requests per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Vendor registration request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "business_type": registration_data.business_type.value
        }
    )

    try:
        vendor_service = VendorService(db)
        result = await vendor_service.register_vendor(current_user.id, registration_data)

        logger.info(
            f"Vendor registration successful",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": result.vendor.id,
                "business_name": result.vendor.business_name
            }
        )

        return result

    except Exception as e:
        logger.error(f"Vendor registration failed: {str(e)}")
        if "already exists" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User is already registered as a vendor"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register vendor"
        )


@router.get(
    "/me",
    response_model=VendorResponse,
    summary="Get current vendor profile",
    description="Get the current user's vendor profile with completion status"
)
async def get_current_vendor_profile(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorResponse:
    """
    Get the current user's vendor profile.

    Returns comprehensive vendor information including business details,
    verification status, onboarding progress, and performance metrics.

    **Authentication Required**: Bearer token
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get vendor profile request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)

        if not vendor:
            logger.warning(
                f"Vendor profile not found",
                extra={
                    "correlation_id": correlation,
                    "user_id": current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        logger.info(
            f"Vendor profile retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id
            }
        )

        return VendorResponse.model_validate(vendor)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get vendor profile failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve vendor profile"
        )


@router.put(
    "/me",
    response_model=VendorResponse,
    summary="Update current vendor profile",
    description="Update the current user's vendor profile with business logic validation"
)
async def update_current_vendor_profile(
    vendor_update: VendorUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorResponse:
    """
    Update the current user's vendor profile.

    Allows vendors to update their business information, contact details,
    and other profile settings with comprehensive validation.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 20 updates per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update vendor profile request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get current vendor
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Update vendor
        update_data = vendor_update.model_dump(exclude_unset=True)
        updated_vendor = await vendor_service.update(vendor.id, update_data)

        logger.info(
            f"Vendor profile updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id
            }
        )

        return VendorResponse.model_validate(updated_vendor)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update vendor profile failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update vendor profile"
        )


@router.get(
    "/me/onboarding",
    response_model=OnboardingStatusResponse,
    summary="Get onboarding status",
    description="Get current onboarding status and next steps for the vendor"
)
async def get_onboarding_status(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> OnboardingStatusResponse:
    """
    Get the current vendor's onboarding status and progress.

    Returns detailed information about completed steps, current progress,
    and next actions required to complete the onboarding process.

    **Authentication Required**: Bearer token
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get onboarding status request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get current vendor
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Generate onboarding status
        onboarding_steps = vendor_service._generate_onboarding_steps(vendor.business_type)

        # Mark completed steps
        for step in onboarding_steps:
            if step["step"] <= vendor.onboarding_step:
                step["completed"] = True

        completion_percentage = (vendor.onboarding_step / 6) * 100

        result = OnboardingStatusResponse(
            current_step=vendor.onboarding_step,
            total_steps=6,
            completed=vendor.onboarding_completed,
            completion_percentage=completion_percentage,
            steps=onboarding_steps,
            next_step_title=onboarding_steps[vendor.onboarding_step]["title"] if vendor.onboarding_step < 6 else None,
            next_step_description=onboarding_steps[vendor.onboarding_step]["description"] if vendor.onboarding_step < 6 else None
        )

        logger.info(
            f"Onboarding status retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "current_step": vendor.onboarding_step
            }
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get onboarding status failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve onboarding status"
        )


@router.put(
    "/me/onboarding",
    response_model=OnboardingStatusResponse,
    summary="Update onboarding step",
    description="Update onboarding step with progress tracking and validation"
)
async def update_onboarding_step(
    step_update: OnboardingStepUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> OnboardingStatusResponse:
    """
    Update the current vendor's onboarding step.

    Allows vendors to progress through the onboarding workflow with
    validation to ensure steps are completed in sequence.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 10 updates per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update onboarding step request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "new_step": step_update.step
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get current vendor
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Update onboarding step
        result = await vendor_service.update_onboarding_step(vendor.id, step_update)

        logger.info(
            f"Onboarding step updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "new_step": step_update.step
            }
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update onboarding step failed: {str(e)}")
        if "cannot move to a previous" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot move to a previous onboarding step"
            )
        elif "cannot skip" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot skip onboarding steps"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update onboarding step"
        )


@router.post(
    "/me/documents",
    response_model=VendorDocumentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Upload verification document",
    description="Upload verification documents with file validation and security"
)
async def upload_vendor_document(
    document_type: DocumentType,
    file: UploadFile = File(...),
    description: Optional[str] = None,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorDocumentResponse:
    """
    Upload a verification document for the current vendor.

    Supports various document types required for business verification
    including licenses, certificates, and identification documents.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 10 uploads per hour per user
    **File Limits**: Max 10MB, PDF/JPG/PNG formats only
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Upload vendor document request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "document_type": document_type.value,
            "filename": file.filename
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get current vendor
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )

        # Check file size (10MB limit)
        if file.size and file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File size exceeds 10MB limit"
            )

        # Check file type
        allowed_types = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only PDF, JPG, and PNG files are allowed"
            )

        # Read file content
        file_content = await file.read()

        # Create document data
        document_data = VendorDocumentCreate(
            document_type=document_type,
            file_name=file.filename,
            file_size=len(file_content),
            mime_type=file.content_type,
            description=description,
            file_path=f"vendor_documents/{vendor.id}/{document_type.value}_{file.filename}"
        )

        # Upload document
        document = await vendor_service.upload_vendor_document(vendor.id, document_data)

        logger.info(
            f"Vendor document uploaded successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "document_id": document.id,
                "document_type": document_type.value
            }
        )

        return VendorDocumentResponse.model_validate(document)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload vendor document failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload document"
        )


@router.get(
    "/me/documents",
    response_model=List[VendorDocumentResponse],
    summary="List vendor documents",
    description="List vendor documents with status tracking and filtering"
)
async def list_vendor_documents(
    document_type: Optional[DocumentType] = Query(None, description="Filter by document type"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[VendorDocumentResponse]:
    """
    List all documents for the current vendor.

    Returns a list of uploaded documents with their verification status,
    upload dates, and other metadata. Supports filtering by document type.

    **Authentication Required**: Bearer token
    """
    correlation = correlation_id.get('')
    logger.info(
        f"List vendor documents request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "document_type_filter": document_type.value if document_type else None
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get current vendor
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Get documents
        documents = await vendor_service.get_vendor_documents(vendor.id, document_type)

        logger.info(
            f"Vendor documents retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "document_count": len(documents)
            }
        )

        return [VendorDocumentResponse.model_validate(doc) for doc in documents]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"List vendor documents failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve documents"
        )


@router.get(
    "/{vendor_id}",
    response_model=Dict[str, Any],
    summary="Get public vendor profile",
    description="Get public vendor profile for customer viewing (verified vendors only)"
)
async def get_public_vendor_profile(
    vendor_id: int = Path(..., description="Vendor ID"),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get public vendor profile for customer viewing.

    Returns publicly available vendor information for verified and active vendors.
    This endpoint does not require authentication and is used by customers
    to view vendor profiles in the marketplace.

    **No Authentication Required**
    **Rate Limiting**: 100 requests per minute per IP
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get public vendor profile request",
        extra={
            "correlation_id": correlation,
            "vendor_id": vendor_id
        }
    )

    try:
        vendor_service = VendorService(db)

        # Get public profile
        public_profile = await vendor_service.get_public_vendor_profile(vendor_id)

        if not public_profile:
            logger.warning(
                f"Public vendor profile not found or not available",
                extra={
                    "correlation_id": correlation,
                    "vendor_id": vendor_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found or not publicly available"
            )

        logger.info(
            f"Public vendor profile retrieved successfully",
            extra={
                "correlation_id": correlation,
                "vendor_id": vendor_id,
                "business_name": public_profile.get("business_name")
            }
        )

        return public_profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get public vendor profile failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve vendor profile"
        )


# Task 3.1.3: Enhanced Vendor Profile Management Endpoints

@router.get(
    "/me/profile/management",
    response_model=VendorProfileManagementResponse,
    summary="Get comprehensive profile management data",
    description="Get vendor profile with completion status, recommendations, and analytics"
)
async def get_vendor_profile_management(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorProfileManagementResponse:
    """
    Get comprehensive vendor profile management data.

    Returns detailed profile information including completion status,
    marketplace eligibility, and personalized recommendations for improvement.

    **Authentication Required**: Bearer token
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get vendor profile management request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        from app.services.vendor_profile_service import VendorProfileService

        # Get current vendor
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Get comprehensive profile management data
        profile_service = VendorProfileService(db)
        management_data = await profile_service.get_vendor_profile_management(vendor.id)

        logger.info(
            f"Vendor profile management data retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "completion_percentage": management_data.completion_status.overall_percentage
            }
        )

        return management_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get vendor profile management failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile management data"
        )


@router.put(
    "/me/profile/management",
    response_model=VendorProfileManagementResponse,
    summary="Update vendor profile comprehensively",
    description="Update vendor profile with comprehensive validation and completion tracking"
)
async def update_vendor_profile_comprehensive(
    profile_data: VendorProfileManagementRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> VendorProfileManagementResponse:
    """
    Update vendor profile with comprehensive validation and tracking.

    Allows vendors to update all aspects of their profile including business
    information, operating hours, media, and verification details with
    automatic completion tracking and recommendations.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 10 updates per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update vendor profile comprehensive request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        from app.services.vendor_profile_service import VendorProfileService

        # Get current vendor
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Update profile comprehensively
        profile_service = VendorProfileService(db)
        updated_data = await profile_service.update_vendor_profile_comprehensive(
            vendor.id, profile_data
        )

        logger.info(
            f"Vendor profile updated comprehensively",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "new_completion_percentage": updated_data.completion_status.overall_percentage
            }
        )

        return updated_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update vendor profile comprehensive failed: {str(e)}")
        if "validation" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Profile validation failed: {str(e)}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update vendor profile"
        )


@router.put(
    "/me/profile/operating-hours",
    response_model=Dict[str, Any],
    summary="Update operating hours",
    description="Update vendor operating hours with validation and timezone support"
)
async def update_vendor_operating_hours(
    hours_data: OperatingHoursUpdateRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Update vendor operating hours with validation.

    Allows vendors to configure their operating hours for each day of the week,
    including break times, special hours, and timezone settings.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 20 updates per hour per user
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Update vendor operating hours request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        from app.services.vendor_profile_service import VendorProfileService

        # Get current vendor
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Update operating hours
        profile_service = VendorProfileService(db)
        updated_hours = await profile_service.update_operating_hours(
            vendor.id, hours_data.operating_hours
        )

        logger.info(
            f"Vendor operating hours updated successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "timezone": hours_data.operating_hours.timezone
            }
        )

        return {
            "operating_hours": updated_hours,
            "message": "Operating hours updated successfully",
            "timezone": hours_data.operating_hours.timezone
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update vendor operating hours failed: {str(e)}")
        if "validation" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Operating hours validation failed: {str(e)}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update operating hours"
        )


@router.get(
    "/me/profile/completion",
    response_model=ProfileCompletionStatus,
    summary="Get profile completion status",
    description="Get detailed profile completion analysis with recommendations"
)
async def get_profile_completion_status(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> ProfileCompletionStatus:
    """
    Get detailed profile completion status and recommendations.

    Returns comprehensive analysis of profile completeness including
    missing fields, completion percentage, and actionable recommendations.

    **Authentication Required**: Bearer token
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Get profile completion status request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id
        }
    )

    try:
        from app.services.vendor_profile_service import VendorProfileService

        # Get current vendor
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Get completion status
        profile_service = VendorProfileService(db)
        completion_status = await profile_service._calculate_profile_completion(vendor)

        logger.info(
            f"Profile completion status retrieved successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "completion_percentage": completion_status.overall_percentage
            }
        )

        return completion_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get profile completion status failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve completion status"
        )


@router.post(
    "/me/profile/media/upload",
    response_model=MediaUploadResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Upload media file",
    description="Upload media files (logo, cover image, gallery) with optimization"
)
async def upload_vendor_media(
    media_request: MediaUploadRequest,
    file: UploadFile = File(...),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> MediaUploadResponse:
    """
    Upload media files for vendor profile.

    Supports uploading logo, cover images, gallery images, and videos
    with automatic optimization and validation.

    **Authentication Required**: Bearer token
    **Rate Limiting**: 20 uploads per hour per user
    **File Limits**: Max 10MB for images, 50MB for videos
    """
    correlation = correlation_id.get('')
    logger.info(
        f"Upload vendor media request",
        extra={
            "correlation_id": correlation,
            "user_id": current_user.id,
            "media_type": media_request.media_type,
            "filename": file.filename
        }
    )

    try:
        # Get current vendor
        vendor_service = VendorService(db)
        vendor = await vendor_service.get_vendor_by_user_id(current_user.id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor profile not found. Please register as a vendor first."
            )

        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )

        # Check file size based on media type
        max_size = 50 * 1024 * 1024 if media_request.media_type == "video" else 10 * 1024 * 1024
        if file.size and file.size > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds {max_size // (1024*1024)}MB limit"
            )

        # Check file type
        if media_request.media_type == "video":
            allowed_types = ['video/mp4', 'video/mpeg', 'video/quicktime']
        else:
            allowed_types = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp']

        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type for {media_request.media_type}"
            )

        # TODO: Implement actual file upload to cloud storage
        # For now, return a mock response
        upload_url = f"https://storage.cultureconnect.ng/vendors/{vendor.id}/{media_request.media_type}/{file.filename}"

        from app.schemas.vendor import MediaItem
        media_item = MediaItem(
            url=upload_url,
            type=media_request.media_type,
            title=media_request.title,
            description=media_request.description,
            alt_text=media_request.alt_text,
            order=media_request.order or 0,
            is_featured=media_request.is_featured,
            file_size=file.size
        )

        response = MediaUploadResponse(
            media_item=media_item,
            upload_url=upload_url,
            success=True,
            message=f"{media_request.media_type.title()} uploaded successfully"
        )

        logger.info(
            f"Vendor media uploaded successfully",
            extra={
                "correlation_id": correlation,
                "user_id": current_user.id,
                "vendor_id": vendor.id,
                "media_type": media_request.media_type,
                "upload_url": upload_url
            }
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload vendor media failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload media file"
        )
