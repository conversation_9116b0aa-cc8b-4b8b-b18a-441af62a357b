"""
Unit tests for email API endpoints.

This module provides comprehensive unit tests for email API endpoints:
- Email template management endpoints (CRUD operations)
- Email sending and delivery tracking endpoints
- User email preference management endpoints
- Email queue monitoring endpoints (admin only)
- Email notification workflow endpoints
- Email analytics and health check endpoints

Implements Task 2.3.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import status
from datetime import datetime, timezone
from uuid import uuid4

from app.main import app
from app.models.email_models import EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
from app.schemas.email_schemas import (
    EmailTemplateResponse, EmailSendResponse, EmailDeliveryResponse,
    EmailPreferenceResponse, EmailVerificationResponse, PasswordResetEmailResponse
)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def authenticated_client(mock_current_user):
    """Create test client with authenticated user."""
    from app.api.v1.endpoints.email import get_current_user, get_async_session

    def override_get_current_user():
        return mock_current_user

    def override_get_async_session():
        return AsyncMock()

    app.dependency_overrides[get_current_user] = override_get_current_user
    app.dependency_overrides[get_async_session] = override_get_async_session

    yield TestClient(app)

    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def admin_client(mock_admin_user):
    """Create test client with admin user."""
    from app.api.v1.endpoints.email import get_current_admin_user, get_async_session

    def override_get_admin_user():
        return mock_admin_user

    def override_get_async_session():
        return AsyncMock()

    app.dependency_overrides[get_current_admin_user] = override_get_admin_user
    app.dependency_overrides[get_async_session] = override_get_async_session

    yield TestClient(app)

    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def mock_current_user():
    """Mock current user for authentication."""
    class MockUser:
        def __init__(self):
            self.id = 1
            self.email = "<EMAIL>"
            self.first_name = "Test"
            self.last_name = "User"
            self.role = "customer"
            self.is_active = True
            self.is_verified = True
            self.full_name = "Test User"
            self.profile_completion = 75.0
            self.created_at = datetime.now(timezone.utc)
            self.updated_at = datetime.now(timezone.utc)

    return MockUser()


@pytest.fixture
def mock_admin_user():
    """Mock admin user for authentication."""
    class MockUser:
        def __init__(self):
            self.id = 1
            self.email = "<EMAIL>"
            self.first_name = "Admin"
            self.last_name = "User"
            self.role = "admin"
            self.is_active = True
            self.is_verified = True
            self.full_name = "Admin User"
            self.profile_completion = 100.0
            self.created_at = datetime.now(timezone.utc)
            self.updated_at = datetime.now(timezone.utc)

    return MockUser()


@pytest.fixture
def sample_template_response():
    """Sample email template response."""
    return EmailTemplateResponse(
        id=uuid4(),
        name="welcome_email",
        category=EmailTemplateCategory.NOTIFICATION,
        subject_template="Welcome {{user_name}}!",
        body_template="Hello {{user_name}}, welcome!",
        variables={"user_name": "string"},
        version=1,
        is_active=True,
        created_by=1,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def sample_delivery_response():
    """Sample email delivery response."""
    return EmailDeliveryResponse(
        id=uuid4(),
        user_id=1,
        recipient_email="<EMAIL>",
        subject="Test Subject",
        status=EmailDeliveryStatus.SENT,
        created_at=datetime.now(timezone.utc),
        sent_at=datetime.now(timezone.utc)
    )


class TestEmailTemplateEndpoints:
    """Test email template management endpoints."""

    def test_create_email_template_success(self, admin_client, sample_template_response):
        """Test successful email template creation."""
        template_data = {
            "name": "welcome_email",
            "category": "notification",
            "subject_template": "Welcome {{user_name}}!",
            "body_template": "Hello {{user_name}}, welcome!",
            "variables": {"user_name": "string"}
        }

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.create_template.return_value = sample_template_response
                mock_service.return_value = mock_service_instance

                response = admin_client.post("/api/v1/email/templates", json=template_data)

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == "welcome_email"
        assert response_data["category"] == "notification"

    def test_create_email_template_unauthorized(self, client):
        """Test email template creation without admin access."""
        template_data = {
            "name": "test_template",
            "category": "notification",
            "subject_template": "Test",
            "body_template": "Test Body"
        }

        # Mock non-admin user
        with patch('app.api.v1.endpoints.email.get_current_admin_user',
                  side_effect=Exception("Admin access required")):
            response = client.post("/api/v1/email/templates", json=template_data)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_email_template_success(self, authenticated_client, sample_template_response):
        """Test successful email template retrieval."""
        template_id = str(sample_template_response.id)

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_template.return_value = sample_template_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get(f"/api/v1/email/templates/{template_id}")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["id"] == template_id

    def test_get_email_template_not_found(self, authenticated_client):
        """Test email template retrieval when not found."""
        template_id = str(uuid4())

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_template.return_value = None
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get(f"/api/v1/email/templates/{template_id}")

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_list_email_templates_success(self, authenticated_client, sample_template_response):
        """Test successful email template listing."""
        from app.schemas.email_schemas import EmailTemplateListResponse

        list_response = EmailTemplateListResponse(
            templates=[sample_template_response],
            total=1,
            page=1,
            size=50,
            pages=1
        )

        with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service_instance.list_templates.return_value = list_response
            mock_service.return_value = mock_service_instance

            response = authenticated_client.get("/api/v1/email/templates")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["total"] == 1
        assert len(response_data["templates"]) == 1

    def test_update_email_template_success(self, admin_client, sample_template_response):
        """Test successful email template update."""
        template_id = str(sample_template_response.id)
        update_data = {
            "subject_template": "Updated Subject {{user_name}}",
            "is_active": False
        }

        # Update the sample response
        updated_response = sample_template_response.copy()
        updated_response.subject_template = update_data["subject_template"]
        updated_response.is_active = update_data["is_active"]

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.update_template.return_value = updated_response
                mock_service.return_value = mock_service_instance

                response = admin_client.put(f"/api/v1/email/templates/{template_id}", json=update_data)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["subject_template"] == update_data["subject_template"]

    def test_delete_email_template_success(self, admin_client):
        """Test successful email template deletion."""
        template_id = str(uuid4())

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.delete_template.return_value = True
                mock_service.return_value = mock_service_instance

                response = admin_client.delete(f"/api/v1/email/templates/{template_id}")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True


class TestEmailSendingEndpoints:
    """Test email sending and delivery endpoints."""

    def test_send_email_success(self, authenticated_client):
        """Test successful email sending."""
        email_data = {
            "recipient_email": "<EMAIL>",
            "subject": "Test Subject",
            "body": "Test Body"
        }

        send_response = EmailSendResponse(
            id=uuid4(),
            recipient_email=email_data["recipient_email"],
            subject=email_data["subject"],
            status=EmailDeliveryStatus.PENDING,
            created_at=datetime.now(timezone.utc),
            message="Email queued for delivery"
        )

        with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
            mock_service_instance = AsyncMock()
            mock_service_instance.send_email.return_value = send_response
            mock_service.return_value = mock_service_instance

            response = authenticated_client.post("/api/v1/email/send", json=email_data)

        assert response.status_code == status.HTTP_202_ACCEPTED
        response_data = response.json()
        assert response_data["recipient_email"] == email_data["recipient_email"]
        assert response_data["status"] == "pending"

    def test_send_email_invalid_data(self, authenticated_client):
        """Test email sending with invalid data."""
        email_data = {
            "recipient_email": "invalid-email",  # Invalid email format
            "subject": "Test Subject",
            "body": "Test Body"
        }

        response = authenticated_client.post("/api/v1/email/send", json=email_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_send_batch_emails_success(self, authenticated_client):
        """Test successful batch email sending."""
        from app.schemas.email_schemas import EmailBatchSendResponse

        batch_data = {
            "template_id": str(uuid4()),
            "recipients": [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "recipient_variables": {
                "<EMAIL>": {"name": "User 1"},
                "<EMAIL>": {"name": "User 2"}
            }
        }

        batch_response = EmailBatchSendResponse(
            batch_id="batch_123",
            total_recipients=2,
            queued_count=2,
            failed_count=0,
            message="Batch processing completed"
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.send_batch_emails.return_value = batch_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.post("/api/v1/email/send/batch", json=batch_data)

        assert response.status_code == status.HTTP_202_ACCEPTED
        response_data = response.json()
        assert response_data["total_recipients"] == 2
        assert response_data["queued_count"] == 2

    def test_get_delivery_status_success(self, authenticated_client, sample_delivery_response):
        """Test successful delivery status retrieval."""
        delivery_id = str(sample_delivery_response.id)

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_delivery_status.return_value = sample_delivery_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get(f"/api/v1/email/deliveries/{delivery_id}")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["id"] == delivery_id
        assert response_data["status"] == "sent"

    def test_get_delivery_status_access_denied(self, authenticated_client, sample_delivery_response):
        """Test delivery status retrieval with access denied."""
        delivery_id = str(sample_delivery_response.id)

        # Mock delivery belonging to different user
        sample_delivery_response.user_id = 999

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_delivery_status.return_value = sample_delivery_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get(f"/api/v1/email/deliveries/{delivery_id}")

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_list_deliveries_success(self, authenticated_client, sample_delivery_response):
        """Test successful delivery listing."""
        from app.schemas.email_schemas import EmailDeliveryListResponse

        list_response = EmailDeliveryListResponse(
            deliveries=[sample_delivery_response],
            total=1,
            skip=0,
            limit=50,
            page=1,
            size=50,
            pages=1
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.list_deliveries.return_value = list_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get("/api/v1/email/deliveries")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["total"] == 1
        assert len(response_data["deliveries"]) == 1


class TestEmailPreferenceEndpoints:
    """Test email preference management endpoints."""

    def test_get_email_preferences_success(self, authenticated_client):
        """Test successful email preferences retrieval."""
        preference_response = EmailPreferenceResponse(
            id=uuid4(),
            user_id=1,
            marketing_emails=True,
            booking_notifications=True,
            security_emails=True,
            verification_emails=True,
            system_notifications=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_user_preferences.return_value = preference_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.get("/api/v1/email/preferences")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["user_id"] == 1
        assert response_data["marketing_emails"] is True

    def test_update_email_preferences_success(self, authenticated_client):
        """Test successful email preferences update."""
        update_data = {
            "marketing_emails": False,
            "booking_notifications": True
        }

        updated_preference = EmailPreferenceResponse(
            id=uuid4(),
            user_id=1,
            marketing_emails=False,
            booking_notifications=True,
            security_emails=True,
            verification_emails=True,
            system_notifications=True,
            opted_out=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.update_user_preferences.return_value = updated_preference
                mock_service.return_value = mock_service_instance

                response = authenticated_client.put("/api/v1/email/preferences", json=update_data)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["marketing_emails"] is False


class TestEmailNotificationEndpoints:
    """Test email notification workflow endpoints."""

    def test_send_verification_email_success(self, authenticated_client):
        """Test successful verification email sending."""
        verification_data = {
            "user_id": 1,
            "email": "<EMAIL>",
            "user_name": "John Doe",
            "verification_token": "token_123"
        }

        verification_response = EmailVerificationResponse(
            user_id=1,
            email="<EMAIL>",
            delivery_id=uuid4(),
            status=EmailDeliveryStatus.PENDING,
            message="Verification email sent successfully"
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.send_verification_email.return_value = verification_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.post("/api/v1/email/notifications/verification", json=verification_data)

        assert response.status_code == status.HTTP_202_ACCEPTED
        response_data = response.json()
        assert "sent successfully" in response_data["message"]

    def test_send_password_reset_email_success(self, authenticated_client):
        """Test successful password reset email sending."""
        reset_data = {
            "user_id": 1,
            "email": "<EMAIL>",
            "user_name": "John Doe",
            "reset_token": "reset_token_123"
        }

        reset_response = PasswordResetEmailResponse(
            user_id=1,
            email="<EMAIL>",
            delivery_id=uuid4(),
            status=EmailDeliveryStatus.PENDING,
            message="Password reset email sent successfully"
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.send_password_reset_email.return_value = reset_response
                mock_service.return_value = mock_service_instance

                response = authenticated_client.post("/api/v1/email/notifications/password-reset", json=reset_data)

        assert response.status_code == status.HTTP_202_ACCEPTED
        response_data = response.json()
        assert "sent successfully" in response_data["message"]


class TestEmailAnalyticsEndpoints:
    """Test email analytics and monitoring endpoints."""

    def test_get_email_analytics_success(self, admin_client):
        """Test successful email analytics retrieval."""
        from app.schemas.email_schemas import EmailAnalyticsResponse

        analytics_response = EmailAnalyticsResponse(
            total_sent=1000,
            total_delivered=950,
            total_failed=50,
            total_bounced=25,
            delivery_rate=95.0,
            bounce_rate=2.5,
            by_category={},
            by_status={},
            period_start=datetime.now(timezone.utc),
            period_end=datetime.now(timezone.utc)
        )

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_email_analytics.return_value = analytics_response
                mock_service.return_value = mock_service_instance

                response = admin_client.get("/api/v1/email/analytics")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["total_sent"] == 1000
        assert response_data["delivery_rate"] == 95.0

    def test_email_health_check_success(self, admin_client):
        """Test successful email service health check."""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": {
                "smtp": {"status": "healthy"},
                "database": {"status": "healthy"},
                "queue": {"status": "healthy"}
            }
        }

        with patch('app.api.v1.endpoints.email.get_async_session'):
            with patch('app.api.v1.endpoints.email.EmailService') as mock_service:
                mock_service_instance = AsyncMock()
                mock_service_instance.health_check.return_value = health_status
                mock_service.return_value = mock_service_instance

                response = admin_client.get("/api/v1/email/health")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["status"] == "healthy"
