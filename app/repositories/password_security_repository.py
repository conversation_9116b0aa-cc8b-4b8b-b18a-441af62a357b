"""
Password Security repositories for Culture Connect Backend API.

This module provides comprehensive password security data access including:
- PasswordHistoryRepository: Password history tracking and validation
- PasswordResetTokenRepository: Secure password reset token management
- AccountLockoutRepository: Account lockout and failed attempt tracking

Implements Task 2.1.2 requirements for password security and hashing with
production-grade data access patterns and PostgreSQL optimization.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.repositories.base import BaseRepositoryCore
from app.models.password_security import PasswordHistory, PasswordResetToken, AccountLockout
from app.core.security import verify_password, get_password_hash
from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)


class PasswordHistoryRepository(BaseRepositoryCore[PasswordHistory]):
    """
    Repository for password history management.

    Handles password history tracking to prevent password reuse and
    maintain security compliance with configurable history limits.
    """

    def __init__(self, db: AsyncSession):
        """Initialize password history repository."""
        super().__init__(PasswordHistory, db)

    async def get_user_password_history(
        self,
        user_id: int,
        limit: int = None
    ) -> List[PasswordHistory]:
        """
        Get user's password history ordered by creation date (newest first).

        Args:
            user_id: User ID to get history for
            limit: Maximum number of entries to return (defaults to config)

        Returns:
            List[PasswordHistory]: Password history entries
        """
        try:
            if limit is None:
                limit = getattr(settings, 'PASSWORD_HISTORY_COUNT', 5)

            query = self.db.query(PasswordHistory).filter(
                PasswordHistory.user_id == user_id
            ).order_by(desc(PasswordHistory.created_at)).limit(limit)

            result = query.all()
            logger.debug(f"Retrieved {len(result)} password history entries for user {user_id}")
            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error getting password history for user {user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting password history for user {user_id}: {str(e)}")
            raise

    async def add_password_to_history(
        self,
        user_id: int,
        password_hash: str
    ) -> PasswordHistory:
        """
        Add a new password to user's history.

        Args:
            user_id: User ID
            password_hash: Bcrypt hashed password

        Returns:
            PasswordHistory: Created history entry
        """
        try:
            # Create new history entry
            history_entry = PasswordHistory(
                user_id=user_id,
                password_hash=password_hash,
                created_at=datetime.utcnow()
            )

            self.db.add(history_entry)
            self.db.flush()  # Get the ID without committing

            # Clean up old entries to maintain limit
            await self._cleanup_old_passwords(user_id)

            logger.info(f"Added password to history for user {user_id}")
            return history_entry

        except SQLAlchemyError as e:
            logger.error(f"Database error adding password to history for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error adding password to history for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def check_password_reuse(
        self,
        user_id: int,
        new_password: str
    ) -> bool:
        """
        Check if a password has been used recently.

        Args:
            user_id: User ID
            new_password: Plain text password to check

        Returns:
            bool: True if password was used recently (should be rejected)
        """
        try:
            # Get recent password history
            history_limit = getattr(settings, 'PASSWORD_HISTORY_COUNT', 5)
            recent_passwords = await self.get_user_password_history(user_id, history_limit)

            # Check against each historical password
            for history_entry in recent_passwords:
                if verify_password(new_password, history_entry.password_hash):
                    logger.warning(f"Password reuse detected for user {user_id}")
                    return True

            logger.debug(f"Password reuse check passed for user {user_id}")
            return False

        except Exception as e:
            logger.error(f"Error checking password reuse for user {user_id}: {str(e)}")
            # In case of error, allow the password change for security
            return False

    async def _cleanup_old_passwords(self, user_id: int) -> int:
        """
        Clean up old password history entries beyond the configured limit.

        Args:
            user_id: User ID to clean up

        Returns:
            int: Number of entries removed
        """
        try:
            history_limit = getattr(settings, 'PASSWORD_HISTORY_COUNT', 5)

            # Get entries beyond the limit
            old_entries = self.db.query(PasswordHistory).filter(
                PasswordHistory.user_id == user_id
            ).order_by(desc(PasswordHistory.created_at)).offset(history_limit).all()

            # Delete old entries
            deleted_count = 0
            for entry in old_entries:
                self.db.delete(entry)
                deleted_count += 1

            if deleted_count > 0:
                logger.debug(f"Cleaned up {deleted_count} old password entries for user {user_id}")

            return deleted_count

        except SQLAlchemyError as e:
            logger.error(f"Database error cleaning up password history for user {user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error cleaning up password history for user {user_id}: {str(e)}")
            raise

    async def get_password_history_count(self, user_id: int) -> int:
        """
        Get the count of password history entries for a user.

        Args:
            user_id: User ID

        Returns:
            int: Number of password history entries
        """
        try:
            count = self.db.query(func.count(PasswordHistory.id)).filter(
                PasswordHistory.user_id == user_id
            ).scalar()

            return count or 0

        except SQLAlchemyError as e:
            logger.error(f"Database error getting password history count for user {user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting password history count for user {user_id}: {str(e)}")
            raise


class PasswordResetTokenRepository(BaseRepositoryCore[PasswordResetToken]):
    """
    Repository for password reset token management.

    Handles secure password reset tokens with expiration, usage tracking,
    and comprehensive security features.
    """

    def __init__(self, db: AsyncSession):
        """Initialize password reset token repository."""
        super().__init__(PasswordResetToken, db)

    async def create_reset_token(
        self,
        user_id: int,
        token_hash: str,
        expires_at: datetime,
        client_ip: str,
        user_agent: Optional[str] = None
    ) -> PasswordResetToken:
        """
        Create a new password reset token.

        Args:
            user_id: User ID
            token_hash: Hashed reset token
            expires_at: Token expiration timestamp
            client_ip: Client IP address
            user_agent: Client user agent

        Returns:
            PasswordResetToken: Created token
        """
        try:
            # Invalidate any existing active tokens for this user
            await self.invalidate_user_tokens(user_id)

            # Create new token
            reset_token = PasswordResetToken(
                user_id=user_id,
                token_hash=token_hash,
                expires_at=expires_at,
                created_ip=client_ip,
                user_agent=user_agent,
                is_active=True
            )

            self.db.add(reset_token)
            self.db.flush()  # Get the ID without committing

            logger.info(f"Created password reset token for user {user_id}")
            return reset_token

        except SQLAlchemyError as e:
            logger.error(f"Database error creating reset token for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating reset token for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def get_valid_token(self, token_hash: str) -> Optional[PasswordResetToken]:
        """
        Get a valid (active and not expired) reset token.

        Args:
            token_hash: Hashed token to find

        Returns:
            Optional[PasswordResetToken]: Token if valid, None otherwise
        """
        try:
            current_time = datetime.utcnow()

            token = self.db.query(PasswordResetToken).filter(
                and_(
                    PasswordResetToken.token_hash == token_hash,
                    PasswordResetToken.is_active == True,
                    PasswordResetToken.expires_at > current_time,
                    PasswordResetToken.used_at.is_(None)
                )
            ).first()

            if token:
                logger.debug(f"Found valid reset token for user {token.user_id}")
            else:
                logger.warning(f"No valid reset token found for hash")

            return token

        except SQLAlchemyError as e:
            logger.error(f"Database error getting valid token: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting valid token: {str(e)}")
            raise

    async def mark_token_used(
        self,
        token_id: str,
        client_ip: str
    ) -> bool:
        """
        Mark a reset token as used.

        Args:
            token_id: Token ID to mark as used
            client_ip: Client IP address

        Returns:
            bool: True if token was marked as used
        """
        try:
            token = self.db.query(PasswordResetToken).filter(
                PasswordResetToken.id == token_id
            ).first()

            if not token:
                logger.warning(f"Token {token_id} not found for marking as used")
                return False

            token.used_at = datetime.utcnow()
            token.used_ip = client_ip
            token.is_active = False

            logger.info(f"Marked reset token {token_id} as used for user {token.user_id}")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database error marking token {token_id} as used: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error marking token {token_id} as used: {str(e)}")
            self.db.rollback()
            raise

    async def invalidate_user_tokens(self, user_id: int) -> int:
        """
        Invalidate all active reset tokens for a user.

        Args:
            user_id: User ID

        Returns:
            int: Number of tokens invalidated
        """
        try:
            updated_count = self.db.query(PasswordResetToken).filter(
                and_(
                    PasswordResetToken.user_id == user_id,
                    PasswordResetToken.is_active == True
                )
            ).update({
                'is_active': False
            })

            if updated_count > 0:
                logger.info(f"Invalidated {updated_count} reset tokens for user {user_id}")

            return updated_count

        except SQLAlchemyError as e:
            logger.error(f"Database error invalidating tokens for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error invalidating tokens for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired reset tokens.

        Returns:
            int: Number of tokens cleaned up
        """
        try:
            current_time = datetime.utcnow()

            expired_tokens = self.db.query(PasswordResetToken).filter(
                PasswordResetToken.expires_at < current_time
            ).all()

            deleted_count = 0
            for token in expired_tokens:
                self.db.delete(token)
                deleted_count += 1

            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} expired reset tokens")

            return deleted_count

        except SQLAlchemyError as e:
            logger.error(f"Database error cleaning up expired tokens: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error cleaning up expired tokens: {str(e)}")
            self.db.rollback()
            raise


class AccountLockoutRepository(BaseRepositoryCore[AccountLockout]):
    """
    Repository for account lockout management.

    Handles failed login attempt tracking and account lockout functionality
    to protect against brute force attacks.
    """

    def __init__(self, db: AsyncSession):
        """Initialize account lockout repository."""
        super().__init__(AccountLockout, db)

    async def get_lockout_status(self, user_id: int) -> Optional[AccountLockout]:
        """
        Get current lockout status for a user.

        Args:
            user_id: User ID

        Returns:
            Optional[AccountLockout]: Lockout record if exists
        """
        try:
            lockout = self.db.query(AccountLockout).filter(
                AccountLockout.user_id == user_id
            ).first()

            if lockout:
                logger.debug(f"Found lockout record for user {user_id}: {lockout.failed_attempts} attempts")

            return lockout

        except SQLAlchemyError as e:
            logger.error(f"Database error getting lockout status for user {user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting lockout status for user {user_id}: {str(e)}")
            raise

    async def record_failed_attempt(
        self,
        user_id: int,
        client_ip: str
    ) -> AccountLockout:
        """
        Record a failed login attempt and potentially lock the account.

        Args:
            user_id: User ID
            client_ip: Client IP address

        Returns:
            AccountLockout: Updated lockout record
        """
        try:
            current_time = datetime.utcnow()
            max_attempts = getattr(settings, 'ACCOUNT_LOCKOUT_ATTEMPTS', 5)
            lockout_duration = getattr(settings, 'ACCOUNT_LOCKOUT_DURATION_MINUTES', 15)

            # Get or create lockout record
            lockout = await self.get_lockout_status(user_id)

            if not lockout:
                lockout = AccountLockout(
                    user_id=user_id,
                    failed_attempts=0,
                    last_attempt_at=current_time,
                    last_attempt_ip=client_ip
                )
                self.db.add(lockout)

            # Increment failed attempts
            lockout.failed_attempts += 1
            lockout.last_attempt_at = current_time
            lockout.last_attempt_ip = client_ip

            # Lock account if max attempts reached
            if lockout.failed_attempts >= max_attempts:
                lockout.locked_until = current_time + timedelta(minutes=lockout_duration)
                lockout.lockout_reason = "too_many_attempts"
                logger.warning(f"Account locked for user {user_id} after {lockout.failed_attempts} failed attempts")
            else:
                logger.info(f"Recorded failed attempt {lockout.failed_attempts}/{max_attempts} for user {user_id}")

            self.db.flush()
            return lockout

        except SQLAlchemyError as e:
            logger.error(f"Database error recording failed attempt for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error recording failed attempt for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def reset_failed_attempts(self, user_id: int) -> bool:
        """
        Reset failed login attempts for a user (on successful login).

        Args:
            user_id: User ID

        Returns:
            bool: True if reset was successful
        """
        try:
            lockout = await self.get_lockout_status(user_id)

            if lockout:
                lockout.failed_attempts = 0
                lockout.locked_until = None
                lockout.lockout_reason = None
                logger.info(f"Reset failed attempts for user {user_id}")
                return True

            return False

        except SQLAlchemyError as e:
            logger.error(f"Database error resetting failed attempts for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error resetting failed attempts for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def lock_account(
        self,
        user_id: int,
        duration_minutes: int = None,
        reason: str = "manual_lock"
    ) -> AccountLockout:
        """
        Manually lock an account for a specified duration.

        Args:
            user_id: User ID
            duration_minutes: Lock duration in minutes
            reason: Reason for lockout

        Returns:
            AccountLockout: Updated lockout record
        """
        try:
            if duration_minutes is None:
                duration_minutes = getattr(settings, 'ACCOUNT_LOCKOUT_DURATION_MINUTES', 15)

            current_time = datetime.utcnow()

            # Get or create lockout record
            lockout = await self.get_lockout_status(user_id)

            if not lockout:
                lockout = AccountLockout(
                    user_id=user_id,
                    failed_attempts=0,
                    last_attempt_at=current_time,
                    last_attempt_ip="system"
                )
                self.db.add(lockout)

            # Set lockout
            lockout.locked_until = current_time + timedelta(minutes=duration_minutes)
            lockout.lockout_reason = reason

            logger.warning(f"Manually locked account for user {user_id} for {duration_minutes} minutes")

            self.db.flush()
            return lockout

        except SQLAlchemyError as e:
            logger.error(f"Database error locking account for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error locking account for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def unlock_account(self, user_id: int) -> bool:
        """
        Manually unlock an account.

        Args:
            user_id: User ID

        Returns:
            bool: True if account was unlocked
        """
        try:
            lockout = await self.get_lockout_status(user_id)

            if lockout:
                lockout.failed_attempts = 0
                lockout.locked_until = None
                lockout.lockout_reason = None
                logger.info(f"Manually unlocked account for user {user_id}")
                return True

            return False

        except SQLAlchemyError as e:
            logger.error(f"Database error unlocking account for user {user_id}: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error unlocking account for user {user_id}: {str(e)}")
            self.db.rollback()
            raise

    async def cleanup_expired_lockouts(self) -> int:
        """
        Clean up expired account lockouts.

        Returns:
            int: Number of lockouts cleaned up
        """
        try:
            current_time = datetime.utcnow()

            # Find expired lockouts
            expired_lockouts = self.db.query(AccountLockout).filter(
                and_(
                    AccountLockout.locked_until.isnot(None),
                    AccountLockout.locked_until < current_time
                )
            ).all()

            # Clear expired lockouts
            updated_count = 0
            for lockout in expired_lockouts:
                lockout.locked_until = None
                lockout.lockout_reason = None
                updated_count += 1

            if updated_count > 0:
                logger.info(f"Cleaned up {updated_count} expired account lockouts")

            return updated_count

        except SQLAlchemyError as e:
            logger.error(f"Database error cleaning up expired lockouts: {str(e)}")
            self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"Unexpected error cleaning up expired lockouts: {str(e)}")
            self.db.rollback()
            raise

    async def is_account_locked(self, user_id: int) -> bool:
        """
        Check if an account is currently locked.

        Args:
            user_id: User ID

        Returns:
            bool: True if account is locked
        """
        try:
            lockout = await self.get_lockout_status(user_id)

            if not lockout or not lockout.locked_until:
                return False

            current_time = datetime.utcnow()
            is_locked = current_time < lockout.locked_until

            # Auto-unlock if expired
            if not is_locked and lockout.locked_until:
                lockout.locked_until = None
                lockout.lockout_reason = None
                logger.debug(f"Auto-unlocked expired lockout for user {user_id}")

            return is_locked

        except Exception as e:
            logger.error(f"Error checking account lock status for user {user_id}: {str(e)}")
            # In case of error, assume not locked for security
            return False
