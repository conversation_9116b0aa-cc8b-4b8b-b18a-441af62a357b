# Culture Connect Backend - Production Deployment Guide

**Target Environment**: AWS Infrastructure (EKS, RDS, ElastiCache)  
**Audience**: Senior DevOps engineers and platform engineers  
**Prerequisites**: AWS CLI, Terraform, kubectl, Helm, AWS account with appropriate permissions  
**Estimated Deployment Time**: 4-6 hours (including Reserved Instance setup)

## 📋 Prerequisites

### Required Tools and Versions
- **AWS CLI**: v2.13+ (AWS Command Line Interface)
- **Terraform**: v1.6+ (Infrastructure as Code)
- **kubectl**: v1.28+ (Kubernetes CLI)
- **Helm**: v3.10+ (Package manager)
- **eksctl**: v0.165+ (EKS cluster management)
- **Git**: v2.30+ (Version control)

### AWS Account Requirements
- **Account**: AWS account with production-grade setup
- **IAM User**: With programmatic access and required permissions
- **Service Limits**: Verified for production workload
- **Billing**: Configured with alerts and budgets
- **Support**: Business or Enterprise support plan recommended

### Required AWS Permissions
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:*", "eks:*", "rds:*", "elasticache:*",
        "s3:*", "iam:*", "kms:*", "route53:*",
        "acm:*", "wafv2:*", "cloudwatch:*",
        "logs:*", "guardduty:*", "config:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### Verification Commands
```bash
# Verify AWS CLI configuration
aws --version
aws sts get-caller-identity

# Verify Terraform
terraform --version

# Verify kubectl and eksctl
kubectl version --client
eksctl version

# Verify Helm
helm version
```

## 🚀 Quick Start (Production Deployment)

### Automated Production Deployment
```bash
# Clone repository
git clone https://github.com/culture-connect/backend.git
cd backend

# Configure AWS credentials
aws configure
# Enter Access Key ID, Secret Access Key, Region (us-west-2), Output format (json)

# Run production deployment script
./scripts/deploy-production.sh

# Monitor deployment progress
./scripts/monitor-production-deployment.sh

# Verify deployment
curl https://api.cultureconnect.ng/health
```

## 📦 Detailed Production Deployment

### Step 1: AWS Account Setup and Cost Optimization

#### 1.1 AWS Account Configuration
```bash
# Configure AWS CLI with production credentials
aws configure --profile culture-connect-prod
# Access Key ID: [Your Production Access Key]
# Secret Access Key: [Your Production Secret Key]
# Default region: us-west-2
# Default output format: json

# Set AWS profile for session
export AWS_PROFILE=culture-connect-prod

# Verify account access
aws sts get-caller-identity
aws ec2 describe-regions
```

#### 1.2 Reserved Instance Strategy (Cost Optimization)
```bash
# Purchase Reserved Instances for cost savings (40-60% reduction)

# EKS Node Group Reserved Instances (3-year, All Upfront)
aws ec2 purchase-reserved-instances-offering \
  --reserved-instances-offering-id r-0********9abcdef0 \
  --instance-count 3 \
  --limit-price 945.00

# RDS Reserved Instance (3-year, All Upfront)
aws rds purchase-reserved-db-instances-offering \
  --reserved-db-instances-offering-id ********-1234-1234-1234-************ \
  --db-instance-count 1

# ElastiCache Reserved Instance (3-year, All Upfront)
aws elasticache purchase-reserved-cache-nodes-offering \
  --reserved-cache-nodes-offering-id ********-1234-1234-1234-************ \
  --cache-node-count 1

# Verify Reserved Instance purchases
aws ec2 describe-reserved-instances
aws rds describe-reserved-db-instances
aws elasticache describe-reserved-cache-nodes
```

#### 1.3 Cost Monitoring Setup
```bash
# Create cost budget
aws budgets create-budget \
  --account-id $(aws sts get-caller-identity --query Account --output text) \
  --budget file://scripts/aws/production-budget.json

# Set up billing alerts
aws cloudwatch put-metric-alarm \
  --alarm-name "Production-High-Billing" \
  --alarm-description "Alert when monthly bill exceeds $1000" \
  --metric-name EstimatedCharges \
  --namespace AWS/Billing \
  --statistic Maximum \
  --period 86400 \
  --threshold 1000 \
  --comparison-operator GreaterThanThreshold
```

### Step 2: Infrastructure Provisioning

#### 2.1 Terraform Configuration
```bash
# Navigate to AWS Terraform directory
cd infrastructure/terraform/aws

# Create production variables file
cp terraform.tfvars.example terraform.tfvars.production

# Edit production variables
nano terraform.tfvars.production

# Critical production variables:
aws_region = "us-west-2"
cluster_name = "culture-connect-production"
environment = "production"
node_group_instance_types = ["t3.large"]
node_group_desired_size = 3
node_group_max_size = 10
node_group_min_size = 3
rds_instance_class = "db.t3.medium"
rds_allocated_storage = 100
rds_multi_az = true
elasticache_node_type = "cache.t3.medium"
enable_vpc_flow_logs = true
enable_guardduty = true
enable_config = true
enable_cloudtrail = true
```

#### 2.2 Terraform Deployment
```bash
# Initialize Terraform
terraform init

# Validate configuration
terraform validate

# Plan deployment (review carefully)
terraform plan -var-file="terraform.tfvars.production" -out=production.tfplan

# Review plan output:
# - ~50 resources to be created
# - EKS cluster, RDS, ElastiCache, VPC, Security Groups, IAM roles
# - Estimated monthly cost: ~$500-600

# Apply infrastructure
terraform apply production.tfplan

# Deployment time: 20-30 minutes
# Monitor progress and verify no errors
```

#### 2.3 Post-Infrastructure Verification
```bash
# Verify infrastructure outputs
terraform output

# Expected outputs:
# - cluster_endpoint
# - cluster_security_group_id
# - rds_endpoint
# - elasticache_endpoint
# - s3_bucket_name

# Configure kubectl for EKS
aws eks update-kubeconfig --region us-west-2 --name culture-connect-production

# Verify EKS cluster access
kubectl cluster-info
kubectl get nodes

# Expected: 3 nodes in Ready state
```

### Step 3: Security Configuration

#### 3.1 IAM and RBAC Setup
```bash
# Create service account for application
kubectl create serviceaccount culture-connect-api -n culture-connect

# Create RBAC policies
kubectl apply -f infrastructure/security/rbac/production-rbac.yaml

# Verify RBAC configuration
kubectl auth can-i create pods --as=system:serviceaccount:culture-connect:culture-connect-api -n culture-connect
```

#### 3.2 Security Monitoring Setup
```bash
# Verify GuardDuty is enabled
aws guardduty list-detectors

# Verify AWS Config is recording
aws configservice describe-configuration-recorders

# Verify CloudTrail is logging
aws cloudtrail describe-trails

# Verify VPC Flow Logs
aws ec2 describe-flow-logs
```

#### 3.3 SSL Certificate Management
```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Wait for cert-manager to be ready
kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s

# Apply production certificate configuration
kubectl apply -f infrastructure/security/certificates/cert-manager-deployment.yaml

# Verify certificate issuance
kubectl get certificates -n culture-connect
kubectl describe certificate culture-connect-tls -n culture-connect
```

### Step 4: Application Deployment

#### 4.1 Namespace and Secrets Setup
```bash
# Create production namespace
kubectl create namespace culture-connect

# Create secrets from AWS Secrets Manager
aws secretsmanager create-secret \
  --name culture-connect/production/app-secrets \
  --description "Production application secrets" \
  --secret-string file://environments/.env.production

# Create Kubernetes secret from AWS Secrets Manager
kubectl create secret generic culture-connect-secrets \
  --from-literal=secret-arn="arn:aws:secretsmanager:us-west-2:ACCOUNT:secret:culture-connect/production/app-secrets" \
  -n culture-connect
```

#### 4.2 Database Setup and Migration
```bash
# Get RDS endpoint
RDS_ENDPOINT=$(terraform output -raw rds_endpoint)

# Create database and user
kubectl run postgres-client --rm -i --tty --image postgres:15 -- psql -h $RDS_ENDPOINT -U postgres

# In PostgreSQL prompt:
CREATE DATABASE culture_connect_production_db;
CREATE USER culture_connect_prod WITH PASSWORD 'SECURE_PRODUCTION_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE culture_connect_production_db TO culture_connect_prod;
\q

# Run database migrations
kubectl apply -f infrastructure/kubernetes/jobs/migration-job.yaml
kubectl wait --for=condition=complete job/database-migration -n culture-connect --timeout=600s
```

#### 4.3 Application Deployment with Blue-Green Strategy
```bash
# Deploy blue environment (current production)
kubectl apply -k infrastructure/kubernetes/overlays/production/

# Wait for blue deployment to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=culture-connect,version=blue -n culture-connect --timeout=600s

# Verify blue deployment health
kubectl exec -it deployment/culture-connect-api-blue -n culture-connect -- curl http://localhost:8000/health

# Deploy green environment (new version)
kubectl apply -k infrastructure/kubernetes/overlays/production-green/

# Wait for green deployment to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=culture-connect,version=green -n culture-connect --timeout=600s

# Run smoke tests on green environment
./scripts/smoke-test-green.sh

# Switch traffic to green (zero-downtime deployment)
kubectl patch service culture-connect-api -n culture-connect -p '{"spec":{"selector":{"version":"green"}}}'

# Verify traffic switch
curl https://api.cultureconnect.ng/health

# Remove blue environment after verification
kubectl delete deployment culture-connect-api-blue -n culture-connect
```

### Step 5: Monitoring and Observability

#### 5.1 Prometheus and Grafana Setup
```bash
# Add Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus stack with production configuration
helm install monitoring prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values infrastructure/monitoring/prometheus/production-values.yaml \
  --set grafana.adminPassword=$(openssl rand -base64 32)

# Wait for monitoring stack to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n monitoring --timeout=600s
```

#### 5.2 Alertmanager Configuration
```bash
# Apply Alertmanager configuration
kubectl apply -f infrastructure/monitoring/alertmanager/

# Verify Alertmanager is running
kubectl get pods -l app.kubernetes.io/name=alertmanager -n monitoring

# Test alert routing
kubectl apply -f infrastructure/monitoring/test-alert.yaml
```

#### 5.3 Application Performance Monitoring
```bash
# Deploy ServiceMonitor for application metrics
kubectl apply -f infrastructure/kubernetes/overlays/production/production-monitoring-patch.yaml

# Verify metrics collection
kubectl port-forward svc/monitoring-prometheus 9090:9090 -n monitoring &
curl http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.labels.job=="culture-connect")'

# Import Grafana dashboards
kubectl apply -f infrastructure/monitoring/grafana/dashboards/
```

### Step 6: DNS and CDN Configuration

#### 6.1 Route53 DNS Setup
```bash
# Get load balancer hostname
ALB_HOSTNAME=$(kubectl get ingress culture-connect-ingress -n culture-connect -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')

# Create Route53 records
aws route53 change-resource-record-sets \
  --hosted-zone-id Z********90ABC \
  --change-batch file://scripts/aws/route53-records.json

# Verify DNS propagation
nslookup api.cultureconnect.ng
dig api.cultureconnect.ng
```

#### 6.2 CloudFront CDN Setup
```bash
# Create CloudFront distribution
aws cloudfront create-distribution \
  --distribution-config file://scripts/aws/cloudfront-config.json

# Get distribution domain name
CLOUDFRONT_DOMAIN=$(aws cloudfront list-distributions --query 'DistributionList.Items[0].DomainName' --output text)

# Update Route53 to point to CloudFront
aws route53 change-resource-record-sets \
  --hosted-zone-id Z********90ABC \
  --change-batch file://scripts/aws/route53-cloudfront-records.json
```

## 🔍 Production Validation and Testing

### Health Checks and Smoke Tests
```bash
# Comprehensive health check
curl https://api.cultureconnect.ng/health/detailed

# Database connectivity test
curl https://api.cultureconnect.ng/health/database

# Redis connectivity test
curl https://api.cultureconnect.ng/health/redis

# Authentication flow test
curl -X POST https://api.cultureconnect.ng/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "testpassword"}'

# WebSocket connectivity test
wscat -c wss://api.cultureconnect.ng/api/v1/websocket/rooms/test
```

### Performance Benchmarking
```bash
# Install k6 for load testing
curl https://github.com/grafana/k6/releases/download/v0.46.0/k6-v0.46.0-linux-amd64.tar.gz -L | tar xvz --strip-components 1

# Run production load test (>10,000 concurrent users)
./k6 run tests/performance/production-load-test.js

# Expected results:
# - 95th percentile response time < 200ms
# - 99th percentile response time < 500ms
# - Error rate < 0.1%
# - Throughput > 1000 RPS
```

### Security Validation
```bash
# Run security scan
kubectl apply -f infrastructure/security/security-scan-job.yaml

# Check for vulnerabilities
kubectl logs job/security-scan -n culture-connect

# Verify SSL configuration
ssllabs-scan --host api.cultureconnect.ng

# Test WAF rules
curl -H "User-Agent: sqlmap" https://api.cultureconnect.ng/
# Expected: 403 Forbidden
```

## 🔄 Disaster Recovery and Backup

### Automated Backup Setup
```bash
# Apply backup CronJobs
kubectl apply -f infrastructure/kubernetes/overlays/production/production-backup-patch.yaml

# Verify backup jobs
kubectl get cronjobs -n culture-connect

# Test backup restoration
./scripts/test-backup-restore.sh
```

### Disaster Recovery Testing
```bash
# Simulate AZ failure
kubectl cordon ip-10-0-1-100.us-west-2.compute.internal
kubectl drain ip-10-0-1-100.us-west-2.compute.internal --ignore-daemonsets --delete-emptydir-data

# Verify application continues to function
curl https://api.cultureconnect.ng/health

# Restore AZ
kubectl uncordon ip-10-0-1-100.us-west-2.compute.internal
```

## 🐛 Production Troubleshooting

### Common Production Issues

#### Issue: High Response Times
```bash
# Check application metrics
kubectl top pods -n culture-connect

# Check database performance
aws rds describe-db-instances --db-instance-identifier culture-connect-production

# Check ElastiCache performance
aws elasticache describe-cache-clusters --cache-cluster-id culture-connect-production

# Scale application if needed
kubectl scale deployment culture-connect-api --replicas=5 -n culture-connect
```

#### Issue: Database Connection Pool Exhaustion
```bash
# Check database connections
kubectl exec -it deployment/culture-connect-api -n culture-connect -- python -c "
import asyncpg
import asyncio
async def check():
    conn = await asyncpg.connect('$DATABASE_URL')
    result = await conn.fetchval('SELECT count(*) FROM pg_stat_activity')
    print(f'Active connections: {result}')
    await conn.close()
asyncio.run(check())
"

# Increase connection pool size
kubectl patch deployment culture-connect-api -n culture-connect -p '{"spec":{"template":{"spec":{"containers":[{"name":"api","env":[{"name":"CC_DATABASE_POOL_SIZE","value":"20"}]}]}}}}'
```

#### Issue: Memory Leaks
```bash
# Monitor memory usage
kubectl top pods -n culture-connect --sort-by=memory

# Check for memory leaks
kubectl exec -it deployment/culture-connect-api -n culture-connect -- python -c "
import psutil
import gc
process = psutil.Process()
print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB')
print(f'GC stats: {gc.get_stats()}')
"

# Restart pods with memory issues
kubectl rollout restart deployment/culture-connect-api -n culture-connect
```

## 📊 Production Monitoring and Maintenance

### Daily Monitoring Checklist
```bash
# Check cluster health
kubectl get nodes
kubectl get pods --all-namespaces | grep -v Running

# Check application health
curl https://api.cultureconnect.ng/health/detailed

# Check resource usage
kubectl top nodes
kubectl top pods -n culture-connect

# Check error rates
kubectl logs -l app.kubernetes.io/name=culture-connect -n culture-connect --since=24h | grep ERROR | wc -l

# Check certificate expiration
kubectl get certificates -n culture-connect
```

### Weekly Maintenance Tasks
```bash
# Update Helm charts
helm repo update
helm list -A

# Check for security updates
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[*].image}{"\n"}{end}' -n culture-connect

# Review monitoring alerts
kubectl port-forward svc/monitoring-grafana 3000:80 -n monitoring

# Backup verification
./scripts/verify-backups.sh

# Cost optimization review
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost
```

### Monthly Optimization Tasks
```bash
# Review Reserved Instance utilization
aws ec2 describe-reserved-instances-modifications

# Optimize resource requests/limits
kubectl describe nodes | grep -A 5 "Allocated resources"

# Review and update auto-scaling policies
kubectl get hpa -n culture-connect

# Security audit
aws config get-compliance-summary
aws guardduty get-findings --detector-id $(aws guardduty list-detectors --query 'DetectorIds[0]' --output text)
```

## 📚 Production Runbooks

### Incident Response Procedures
1. **High Error Rate**: `docs/runbooks/high-error-rate.md`
2. **Database Issues**: `docs/runbooks/database-issues.md`
3. **Performance Degradation**: `docs/runbooks/performance-issues.md`
4. **Security Incidents**: `docs/runbooks/security-incidents.md`
5. **Disaster Recovery**: `docs/runbooks/disaster-recovery.md`

### Scaling Procedures
1. **Horizontal Scaling**: `docs/runbooks/horizontal-scaling.md`
2. **Database Scaling**: `docs/runbooks/database-scaling.md`
3. **Cache Scaling**: `docs/runbooks/cache-scaling.md`

## 🆘 Production Support

- **On-Call**: PagerDuty integration configured
- **Escalation**: Follow incident response procedures
- **Documentation**: `docs/production/` directory
- **Runbooks**: `docs/runbooks/` directory
- **Emergency Contact**: <EMAIL>
- **AWS Support**: Business/Enterprise support case
