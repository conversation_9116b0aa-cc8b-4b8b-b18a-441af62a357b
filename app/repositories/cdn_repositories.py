"""
CDN Optimization repositories for Culture Connect Backend API.

This module provides comprehensive repository layer for CDN optimization and asset delivery:
- CDNConfigurationRepository: CDN provider configuration management
- AssetOptimizationRepository: Asset optimization tracking and performance metrics
- AssetBundleRepository: Asset bundling configuration and optimization results
- CDNMetricsRepository: CDN performance metrics and delivery analytics
- AssetDeliveryRepository: Asset delivery tracking and performance monitoring

Implements Phase 7.3.4 requirements with high-performance data access, Redis caching
integration, and seamless integration with existing Phase 7.3.2 caching infrastructure.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import UUID

from sqlalchemy import and_, or_, desc, asc, func, text
from sqlalchemy.orm import Session, selectinload
from sqlalchemy.exc import IntegrityError

from app.core.cache import CacheManager
import logging
from app.models.cdn_models import (
    CDNConfiguration, AssetOptimization, AssetBundle,
    CDNMetrics, AssetDelivery, DeliveryStatus, AssetType
)
# Simple base repository for synchronous operations
from typing import TypeVar, Generic, Type, Optional, Dict, Any, List
from uuid import UUID

ModelType = TypeVar("ModelType")

class BaseRepository(Generic[ModelType]):
    """Simple base repository for synchronous database operations."""

    def __init__(self, model: Type[ModelType], db: Session, cache_manager: Optional[CacheManager] = None):
        self.model = model
        self.db = db
        self.cache_manager = cache_manager

    def get_by_id(self, id: UUID) -> Optional[ModelType]:
        """Get record by ID."""
        return self.db.query(self.model).filter(self.model.id == id).first()

    def create(self, obj_data: Dict[str, Any]) -> ModelType:
        """Create new record."""
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def update(self, id: UUID, obj_data: Dict[str, Any]) -> Optional[ModelType]:
        """Update existing record."""
        db_obj = self.get_by_id(id)
        if not db_obj:
            return None

        for field, value in obj_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)

        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def delete(self, id: UUID) -> bool:
        """Delete record."""
        db_obj = self.get_by_id(id)
        if not db_obj:
            return False

        self.db.delete(db_obj)
        self.db.commit()
        return True

    def get_all(self, limit: int = 100) -> List[ModelType]:
        """Get all records with limit."""
        return self.db.query(self.model).limit(limit).all()

logger = logging.getLogger(__name__)


class CDNConfigurationRepository(BaseRepository[CDNConfiguration]):
    """Repository for CDN configuration management."""

    def __init__(self, db: Session, cache_manager: Optional[CacheManager] = None):
        super().__init__(CDNConfiguration, db, cache_manager)
        self.cache_prefix = "cdn_config"
        self.default_ttl = 3600  # 1 hour

    async def get_active_configurations(self) -> List[CDNConfiguration]:
        """Get all active CDN configurations."""
        cache_key = f"{self.cache_prefix}:active"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        configurations = self.db.query(self.model).filter(
            self.model.is_active == True
        ).order_by(
            desc(self.model.is_default),
            asc(self.model.name)
        ).all()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                configurations,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:list"]
            )

        return configurations

    async def get_default_configuration(self) -> Optional[CDNConfiguration]:
        """Get the default CDN configuration."""
        cache_key = f"{self.cache_prefix}:default"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        configuration = self.db.query(self.model).filter(
            and_(
                self.model.is_active == True,
                self.model.is_default == True
            )
        ).first()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                configuration,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:default"]
            )

        return configuration

    async def get_by_provider(self, provider: str) -> List[CDNConfiguration]:
        """Get CDN configurations by provider."""
        cache_key = f"{self.cache_prefix}:provider:{provider}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        configurations = self.db.query(self.model).filter(
            and_(
                self.model.provider == provider,
                self.model.is_active == True
            )
        ).order_by(asc(self.model.name)).all()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                configurations,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:provider"]
            )

        return configurations

    async def set_default_configuration(self, configuration_id: UUID) -> bool:
        """Set a configuration as default (unset others)."""
        try:
            # Unset all default flags
            self.db.query(self.model).filter(
                self.model.is_default == True
            ).update({"is_default": False})

            # Set new default
            result = self.db.query(self.model).filter(
                self.model.id == configuration_id
            ).update({"is_default": True})

            self.db.commit()

            # Invalidate cache
            if self.cache_manager:
                await self.cache_manager.invalidate_by_tags([
                    f"{self.cache_prefix}:default",
                    f"{self.cache_prefix}:list"
                ])

            return result > 0

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to set default configuration: {str(e)}")
            return False


class AssetOptimizationRepository(BaseRepository[AssetOptimization]):
    """Repository for asset optimization tracking."""

    def __init__(self, db: Session, cache_manager: Optional[CacheManager] = None):
        super().__init__(AssetOptimization, db, cache_manager)
        self.cache_prefix = "asset_opt"
        self.default_ttl = 1800  # 30 minutes

    async def create_optimization(self, optimization_data: Dict[str, Any]) -> AssetOptimization:
        """Create a new asset optimization record."""
        optimization = self.model(**optimization_data)
        self.db.add(optimization)
        self.db.commit()
        self.db.refresh(optimization)

        # Invalidate related cache
        if self.cache_manager:
            await self.cache_manager.invalidate_by_tags([
                f"{self.cache_prefix}:stats",
                f"{self.cache_prefix}:config:{optimization.cdn_configuration_id}"
            ])

        return optimization

    async def get_by_status(self, status: DeliveryStatus, limit: int = 100) -> List[AssetOptimization]:
        """Get optimizations by delivery status."""
        cache_key = f"{self.cache_prefix}:status:{status}:limit:{limit}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        optimizations = self.db.query(self.model).filter(
            self.model.delivery_status == status
        ).order_by(desc(self.model.created_at)).limit(limit).all()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                optimizations,
                ttl=300,  # 5 minutes for status queries
                tags=[f"{self.cache_prefix}:status"]
            )

        return optimizations

    async def get_optimization_stats(
        self,
        cdn_configuration_id: Optional[UUID] = None,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get optimization statistics."""
        cache_key = f"{self.cache_prefix}:stats:config:{cdn_configuration_id}:hours:{hours}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Build query
        query = self.db.query(self.model)

        if cdn_configuration_id:
            query = query.filter(self.model.cdn_configuration_id == cdn_configuration_id)

        # Time filter
        since = datetime.utcnow() - timedelta(hours=hours)
        query = query.filter(self.model.created_at >= since)

        # Calculate statistics
        optimizations = query.all()

        if not optimizations:
            return {
                "total_optimizations": 0,
                "successful_optimizations": 0,
                "avg_compression_ratio": 0.0,
                "avg_optimization_time_ms": 0.0,
                "total_bytes_saved": 0,
                "success_rate": 0.0
            }

        successful = [opt for opt in optimizations if opt.delivery_status == DeliveryStatus.READY]

        stats = {
            "total_optimizations": len(optimizations),
            "successful_optimizations": len(successful),
            "avg_compression_ratio": float(sum(opt.compression_ratio for opt in successful) / len(successful)) if successful else 0.0,
            "avg_optimization_time_ms": float(sum(opt.optimization_time_ms for opt in successful) / len(successful)) if successful else 0.0,
            "total_bytes_saved": sum(opt.original_size_bytes - opt.optimized_size_bytes for opt in successful),
            "success_rate": len(successful) / len(optimizations) if optimizations else 0.0
        }

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                stats,
                ttl=600,  # 10 minutes for stats
                tags=[f"{self.cache_prefix}:stats"]
            )

        return stats

    async def get_by_asset_type(
        self,
        asset_type: AssetType,
        limit: int = 100
    ) -> List[AssetOptimization]:
        """Get optimizations by asset type."""
        cache_key = f"{self.cache_prefix}:type:{asset_type}:limit:{limit}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        optimizations = self.db.query(self.model).filter(
            self.model.asset_type == asset_type
        ).order_by(desc(self.model.created_at)).limit(limit).all()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                optimizations,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:type"]
            )

        return optimizations

    async def update_optimization_status(
        self,
        optimization_id: UUID,
        status: DeliveryStatus,
        **kwargs
    ) -> bool:
        """Update optimization status and related fields."""
        try:
            update_data = {"delivery_status": status, "updated_at": datetime.utcnow()}
            update_data.update(kwargs)

            result = self.db.query(self.model).filter(
                self.model.id == optimization_id
            ).update(update_data)

            self.db.commit()

            # Invalidate cache
            if self.cache_manager:
                await self.cache_manager.invalidate_by_tags([
                    f"{self.cache_prefix}:status",
                    f"{self.cache_prefix}:stats"
                ])

            return result > 0

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update optimization status: {str(e)}")
            return False


class AssetBundleRepository(BaseRepository[AssetBundle]):
    """Repository for asset bundle management."""

    def __init__(self, db: Session, cache_manager: Optional[CacheManager] = None):
        super().__init__(AssetBundle, db, cache_manager)
        self.cache_prefix = "asset_bundle"
        self.default_ttl = 1800  # 30 minutes

    async def create_bundle(self, bundle_data: Dict[str, Any]) -> AssetBundle:
        """Create a new asset bundle record."""
        bundle = self.model(**bundle_data)
        self.db.add(bundle)
        self.db.commit()
        self.db.refresh(bundle)

        # Invalidate related cache
        if self.cache_manager:
            await self.cache_manager.invalidate_by_tags([
                f"{self.cache_prefix}:stats",
                f"{self.cache_prefix}:config:{bundle.cdn_configuration_id}"
            ])

        return bundle

    async def get_by_bundle_type(self, bundle_type: str, limit: int = 100) -> List[AssetBundle]:
        """Get bundles by type."""
        cache_key = f"{self.cache_prefix}:type:{bundle_type}:limit:{limit}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Query database
        bundles = self.db.query(self.model).filter(
            self.model.bundle_type == bundle_type
        ).order_by(desc(self.model.created_at)).limit(limit).all()

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                bundles,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:type"]
            )

        return bundles

    async def get_bundle_stats(
        self,
        cdn_configuration_id: Optional[UUID] = None,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get bundle statistics."""
        cache_key = f"{self.cache_prefix}:stats:config:{cdn_configuration_id}:hours:{hours}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Build query
        query = self.db.query(self.model)

        if cdn_configuration_id:
            query = query.filter(self.model.cdn_configuration_id == cdn_configuration_id)

        # Time filter
        since = datetime.utcnow() - timedelta(hours=hours)
        query = query.filter(self.model.created_at >= since)

        # Calculate statistics
        bundles = query.all()

        if not bundles:
            return {
                "total_bundles": 0,
                "successful_bundles": 0,
                "avg_compression_ratio": 0.0,
                "avg_bundling_time_ms": 0.0,
                "total_bytes_saved": 0
            }

        successful = [bundle for bundle in bundles if bundle.delivery_status == DeliveryStatus.READY]

        stats = {
            "total_bundles": len(bundles),
            "successful_bundles": len(successful),
            "avg_compression_ratio": float(sum(bundle.compression_ratio for bundle in successful) / len(successful)) if successful else 0.0,
            "avg_bundling_time_ms": float(sum(bundle.bundling_time_ms for bundle in successful) / len(successful)) if successful else 0.0,
            "total_bytes_saved": sum(bundle.total_original_size_bytes - bundle.bundled_size_bytes for bundle in successful)
        }

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                stats,
                ttl=600,  # 10 minutes for stats
                tags=[f"{self.cache_prefix}:stats"]
            )

        return stats


class CDNMetricsRepository(BaseRepository[CDNMetrics]):
    """Repository for CDN metrics management."""

    def __init__(self, db: Session, cache_manager: Optional[CacheManager] = None):
        super().__init__(CDNMetrics, db, cache_manager)
        self.cache_prefix = "cdn_metrics"
        self.default_ttl = 900  # 15 minutes

    async def create_metrics(self, metrics_data: Dict[str, Any]) -> CDNMetrics:
        """Create CDN metrics record with calculated fields."""
        # Calculate derived fields
        total_requests = metrics_data.get("total_requests", 0)
        cache_hits = metrics_data.get("cache_hits", 0)
        cache_misses = metrics_data.get("cache_misses", 0)
        error_count = metrics_data.get("error_count", 0)
        total_bytes_served = metrics_data.get("total_bytes_served", 0)
        total_bytes_saved = metrics_data.get("total_bytes_saved", 0)

        # Calculate rates
        metrics_data["cache_hit_rate"] = Decimal(str(cache_hits / total_requests)) if total_requests > 0 else Decimal("0.0")
        metrics_data["error_rate"] = Decimal(str(error_count / total_requests)) if total_requests > 0 else Decimal("0.0")
        metrics_data["bandwidth_savings_ratio"] = Decimal(str(total_bytes_saved / total_bytes_served)) if total_bytes_served > 0 else Decimal("0.0")

        metrics = self.model(**metrics_data)
        self.db.add(metrics)
        self.db.commit()
        self.db.refresh(metrics)

        # Invalidate related cache
        if self.cache_manager:
            await self.cache_manager.invalidate_by_tags([
                f"{self.cache_prefix}:summary",
                f"{self.cache_prefix}:config:{metrics.cdn_configuration_id}"
            ])

        return metrics

    async def get_metrics_summary(
        self,
        cdn_configuration_id: Optional[UUID] = None,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get aggregated metrics summary."""
        cache_key = f"{self.cache_prefix}:summary:config:{cdn_configuration_id}:hours:{hours}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Build query
        query = self.db.query(self.model)

        if cdn_configuration_id:
            query = query.filter(self.model.cdn_configuration_id == cdn_configuration_id)

        # Time filter
        since = datetime.utcnow() - timedelta(hours=hours)
        query = query.filter(self.model.period_start >= since)

        # Calculate aggregated metrics
        metrics = query.all()

        if not metrics:
            return {
                "total_requests": 0,
                "cache_hit_rate": 0.0,
                "avg_response_time_ms": 0.0,
                "bandwidth_savings_gb": 0.0,
                "error_rate": 0.0
            }

        total_requests = sum(m.total_requests for m in metrics)
        total_cache_hits = sum(m.cache_hits for m in metrics)
        total_bytes_saved = sum(m.total_bytes_saved for m in metrics)
        total_errors = sum(m.error_count for m in metrics)

        # Weighted averages
        weighted_response_time = sum(m.avg_response_time_ms * m.total_requests for m in metrics)
        avg_response_time = float(weighted_response_time / total_requests) if total_requests > 0 else 0.0

        summary = {
            "total_requests": total_requests,
            "cache_hit_rate": float(total_cache_hits / total_requests) if total_requests > 0 else 0.0,
            "avg_response_time_ms": avg_response_time,
            "bandwidth_savings_gb": float(total_bytes_saved / (1024**3)),  # Convert to GB
            "error_rate": float(total_errors / total_requests) if total_requests > 0 else 0.0
        }

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                summary,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:summary"]
            )

        return summary


class AssetDeliveryRepository(BaseRepository[AssetDelivery]):
    """Repository for asset delivery tracking."""

    def __init__(self, db: Session, cache_manager: Optional[CacheManager] = None):
        super().__init__(AssetDelivery, db, cache_manager)
        self.cache_prefix = "asset_delivery"
        self.default_ttl = 600  # 10 minutes

    async def create_delivery_record(self, delivery_data: Dict[str, Any]) -> AssetDelivery:
        """Create asset delivery record."""
        delivery = self.model(**delivery_data)
        self.db.add(delivery)
        self.db.commit()
        self.db.refresh(delivery)

        # Update optimization cache hit/miss counts
        if delivery.cache_status in ["HIT", "STALE"]:
            self.db.query(AssetOptimization).filter(
                AssetOptimization.id == delivery.asset_optimization_id
            ).update({"cache_hit_count": AssetOptimization.cache_hit_count + 1})
        else:
            self.db.query(AssetOptimization).filter(
                AssetOptimization.id == delivery.asset_optimization_id
            ).update({"cache_miss_count": AssetOptimization.cache_miss_count + 1})

        self.db.commit()

        return delivery

    async def get_performance_metrics(
        self,
        asset_optimization_id: Optional[UUID] = None,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get delivery performance metrics."""
        cache_key = f"{self.cache_prefix}:performance:asset:{asset_optimization_id}:hours:{hours}"

        # Try cache first
        if self.cache_manager:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Build query
        query = self.db.query(self.model)

        if asset_optimization_id:
            query = query.filter(self.model.asset_optimization_id == asset_optimization_id)

        # Time filter
        since = datetime.utcnow() - timedelta(hours=hours)
        query = query.filter(self.model.delivered_at >= since)

        # Calculate performance metrics
        deliveries = query.all()

        if not deliveries:
            return {
                "total_deliveries": 0,
                "avg_response_time_ms": 0.0,
                "avg_first_byte_time_ms": 0.0,
                "cache_hit_rate": 0.0,
                "success_rate": 0.0
            }

        cache_hits = len([d for d in deliveries if d.cache_status in ["HIT", "STALE"]])
        successful = len([d for d in deliveries if 200 <= d.http_status_code < 300])

        metrics = {
            "total_deliveries": len(deliveries),
            "avg_response_time_ms": float(sum(d.response_time_ms for d in deliveries) / len(deliveries)),
            "avg_first_byte_time_ms": float(sum(d.first_byte_time_ms for d in deliveries) / len(deliveries)),
            "cache_hit_rate": cache_hits / len(deliveries),
            "success_rate": successful / len(deliveries)
        }

        # Cache result
        if self.cache_manager:
            await self.cache_manager.set(
                cache_key,
                metrics,
                ttl=self.default_ttl,
                tags=[f"{self.cache_prefix}:performance"]
            )

        return metrics
