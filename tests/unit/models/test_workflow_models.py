"""
Unit tests for workflow models.

Tests for Task 6.2.2: Background Job Processing Implementation
- WorkflowDefinition model tests
- WorkflowExecution model tests
- JobDependency model tests
- WorkflowStep model tests
- JobSchedule model tests
- WorkflowAlert model tests
"""

import pytest
from datetime import datetime, timezone
from decimal import Decimal
from uuid import uuid4

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.models.workflow_models import (
    WorkflowDefinition, WorkflowExecution, JobDependency, WorkflowStep,
    WorkflowStepExecution, JobSchedule, WorkflowAlert,
    WorkflowStatus, ExecutionStatus, DependencyType, StepStatus, AlertSeverity
)


class TestWorkflowDefinition:
    """Test WorkflowDefinition model."""

    def test_create_workflow_definition(self, db: Session):
        """Test creating a workflow definition."""
        workflow = WorkflowDefinition(
            name="Test Workflow",
            description="A test workflow",
            version="1.0.0",
            status=WorkflowStatus.DRAFT,
            configuration={"key": "value"},
            tags=["test", "workflow"],
            workflow_metadata={"author": "test"},
            max_execution_time=3600,
            max_retries=3,
            retry_delay=60
        )

        db.add(workflow)
        db.commit()
        db.refresh(workflow)

        assert workflow.id is not None
        assert workflow.name == "Test Workflow"
        assert workflow.description == "A test workflow"
        assert workflow.version == "1.0.0"
        assert workflow.status == WorkflowStatus.DRAFT
        assert workflow.configuration == {"key": "value"}
        assert workflow.tags == ["test", "workflow"]
        assert workflow.workflow_metadata == {"author": "test"}
        assert workflow.max_execution_time == 3600
        assert workflow.max_retries == 3
        assert workflow.retry_delay == 60
        assert workflow.created_at is not None
        assert workflow.updated_at is not None

    def test_workflow_definition_unique_name_version(self, db: Session):
        """Test unique constraint on name and version."""
        workflow1 = WorkflowDefinition(
            name="Test Workflow",
            version="1.0.0"
        )
        workflow2 = WorkflowDefinition(
            name="Test Workflow",
            version="1.0.0"
        )

        db.add(workflow1)
        db.commit()

        db.add(workflow2)
        with pytest.raises(IntegrityError):
            db.commit()

    def test_workflow_definition_constraints(self, db: Session):
        """Test model constraints."""
        # Test positive max_execution_time
        workflow = WorkflowDefinition(
            name="Test Workflow",
            max_execution_time=-1
        )
        db.add(workflow)
        with pytest.raises(IntegrityError):
            db.commit()

    def test_workflow_definition_defaults(self, db: Session):
        """Test default values."""
        workflow = WorkflowDefinition(name="Test Workflow")

        db.add(workflow)
        db.commit()
        db.refresh(workflow)

        assert workflow.version == "1.0.0"
        assert workflow.status == WorkflowStatus.DRAFT
        assert workflow.configuration == {}
        assert workflow.tags == []
        assert workflow.workflow_metadata == {}
        assert workflow.max_retries == 3
        assert workflow.retry_delay == 60


class TestWorkflowExecution:
    """Test WorkflowExecution model."""

    def test_create_workflow_execution(self, db: Session):
        """Test creating a workflow execution."""
        # Create workflow definition first
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        execution = WorkflowExecution(
            workflow_definition_id=workflow_def.id,
            status=ExecutionStatus.PENDING,
            execution_context={"context": "test"},
            input_data={"input": "data"},
            triggered_by="manual",
            correlation_id="test-correlation-123"
        )

        db.add(execution)
        db.commit()
        db.refresh(execution)

        assert execution.id is not None
        assert execution.workflow_definition_id == workflow_def.id
        assert execution.status == ExecutionStatus.PENDING
        assert execution.execution_context == {"context": "test"}
        assert execution.input_data == {"input": "data"}
        assert execution.triggered_by == "manual"
        assert execution.correlation_id == "test-correlation-123"
        assert execution.retry_count == 0
        assert execution.steps_total == 0
        assert execution.steps_completed == 0
        assert execution.steps_failed == 0

    def test_workflow_execution_timing(self, db: Session):
        """Test execution timing fields."""
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        start_time = datetime.now(timezone.utc)
        end_time = datetime.now(timezone.utc)
        duration = Decimal("123.456")

        execution = WorkflowExecution(
            workflow_definition_id=workflow_def.id,
            started_at=start_time,
            completed_at=end_time,
            duration_seconds=duration
        )

        db.add(execution)
        db.commit()
        db.refresh(execution)

        assert execution.started_at == start_time
        assert execution.completed_at == end_time
        assert execution.duration_seconds == duration

    def test_workflow_execution_constraints(self, db: Session):
        """Test model constraints."""
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        # Test negative retry_count
        execution = WorkflowExecution(
            workflow_definition_id=workflow_def.id,
            retry_count=-1
        )
        db.add(execution)
        with pytest.raises(IntegrityError):
            db.commit()


class TestJobDependency:
    """Test JobDependency model."""

    def test_create_job_dependency(self, db: Session):
        """Test creating a job dependency."""
        dependency = JobDependency(
            parent_job_id="parent-job-123",
            child_job_id="child-job-456",
            dependency_type=DependencyType.SUCCESS,
            condition_expression="result.status == 'success'",
            condition_data={"threshold": 100},
            timeout_seconds=300,
            max_wait_time=600,
            dependency_metadata={"priority": "high"}
        )

        db.add(dependency)
        db.commit()
        db.refresh(dependency)

        assert dependency.id is not None
        assert dependency.parent_job_id == "parent-job-123"
        assert dependency.child_job_id == "child-job-456"
        assert dependency.dependency_type == DependencyType.SUCCESS
        assert dependency.condition_expression == "result.status == 'success'"
        assert dependency.condition_data == {"threshold": 100}
        assert dependency.timeout_seconds == 300
        assert dependency.max_wait_time == 600
        assert dependency.is_satisfied is False
        assert dependency.evaluation_count == 0
        assert dependency.dependency_metadata == {"priority": "high"}

    def test_job_dependency_unique_constraint(self, db: Session):
        """Test unique constraint on parent and child job IDs."""
        dependency1 = JobDependency(
            parent_job_id="parent-job-123",
            child_job_id="child-job-456"
        )
        dependency2 = JobDependency(
            parent_job_id="parent-job-123",
            child_job_id="child-job-456"
        )

        db.add(dependency1)
        db.commit()

        db.add(dependency2)
        with pytest.raises(IntegrityError):
            db.commit()

    def test_job_dependency_constraints(self, db: Session):
        """Test model constraints."""
        # Test positive timeout_seconds
        dependency = JobDependency(
            parent_job_id="parent-job-123",
            child_job_id="child-job-456",
            timeout_seconds=-1
        )
        db.add(dependency)
        with pytest.raises(IntegrityError):
            db.commit()


class TestWorkflowStep:
    """Test WorkflowStep model."""

    def test_create_workflow_step(self, db: Session):
        """Test creating a workflow step."""
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        step = WorkflowStep(
            workflow_definition_id=workflow_def.id,
            name="Test Step",
            description="A test step",
            step_order=1,
            task_name="test_task",
            task_configuration={"param": "value"},
            task_queue="default",
            task_priority=5,
            timeout_seconds=300,
            max_retries=3,
            retry_delay=60,
            condition_expression="input.ready == true",
            skip_on_failure=False,
            continue_on_failure=True,
            depends_on_steps=["step1", "step2"],
            tags=["test", "step"],
            step_metadata={"category": "processing"}
        )

        db.add(step)
        db.commit()
        db.refresh(step)

        assert step.id is not None
        assert step.workflow_definition_id == workflow_def.id
        assert step.name == "Test Step"
        assert step.description == "A test step"
        assert step.step_order == 1
        assert step.task_name == "test_task"
        assert step.task_configuration == {"param": "value"}
        assert step.task_queue == "default"
        assert step.task_priority == 5
        assert step.timeout_seconds == 300
        assert step.max_retries == 3
        assert step.retry_delay == 60
        assert step.condition_expression == "input.ready == true"
        assert step.skip_on_failure is False
        assert step.continue_on_failure is True
        assert step.depends_on_steps == ["step1", "step2"]
        assert step.tags == ["test", "step"]
        assert step.step_metadata == {"category": "processing"}

    def test_workflow_step_unique_constraints(self, db: Session):
        """Test unique constraints."""
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        # Test unique step order
        step1 = WorkflowStep(
            workflow_definition_id=workflow_def.id,
            name="Step 1",
            step_order=1,
            task_name="task1"
        )
        step2 = WorkflowStep(
            workflow_definition_id=workflow_def.id,
            name="Step 2",
            step_order=1,  # Same order
            task_name="task2"
        )

        db.add(step1)
        db.commit()

        db.add(step2)
        with pytest.raises(IntegrityError):
            db.commit()

    def test_workflow_step_constraints(self, db: Session):
        """Test model constraints."""
        workflow_def = WorkflowDefinition(name="Test Workflow")
        db.add(workflow_def)
        db.commit()

        # Test invalid task priority
        step = WorkflowStep(
            workflow_definition_id=workflow_def.id,
            name="Test Step",
            step_order=1,
            task_name="test_task",
            task_priority=15  # Invalid priority > 10
        )
        db.add(step)
        with pytest.raises(IntegrityError):
            db.commit()
