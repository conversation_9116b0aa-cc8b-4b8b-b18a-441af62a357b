"""
Clean Booking Communication Services for Culture Connect Backend API.

This module provides comprehensive service layer implementation for booking communication management.
"""

import logging
import time
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import ValidationError, NotFoundError, ServiceError
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService
from app.models.booking_communication import (
    BookingMessage, MessageType, MessageStatus, DeliveryMethod, DeliveryStatus, TemplateCategory
)
from app.repositories.booking_communication_repository import (
    BookingMessageRepository, MessageAttachmentRepository,
    MessageTemplateRepository, MessageDeliveryLogRepository
)
from app.repositories.booking_repository import BookingRepository
from app.repositories.auth_repository import UserRepository
from app.schemas.booking_communication_schemas import (
    BookingMessageCreate, BookingMessageResponse,
    MessageAttachmentCreate, MessageAttachmentResponse,
    MessageTemplateCreate, MessageTemplateResponse,
    MessageAnalyticsResponse
)
from app.repositories.base import PaginationParams, QueryResult
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


# BookingCommunicationService will be defined at the end of the file


class BookingMessageService:
    """Comprehensive booking message service for conversation management."""

    def __init__(self, db: AsyncSession):
        """Initialize booking message service with dependencies."""
        self.db = db
        self.repository = BookingMessageRepository(db)
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)
        self.booking_repository = BookingRepository(db)
        self.user_repository = UserRepository(db)
        self.attachment_repository = MessageAttachmentRepository(db)
        self.delivery_repository = MessageDeliveryLogRepository(db)

    async def send_message(
        self,
        booking_id: int,
        sender_id: Optional[int],
        message_data: BookingMessageCreate,
        attachments: Optional[List[MessageAttachmentCreate]] = None
    ) -> BookingMessageResponse:
        """Send a message with optional attachments and multi-channel delivery."""
        start_time = time.time()
        logger.info(f"Starting send_message for booking_id={booking_id}")

        try:
            # Prepare message data
            message_dict = message_data.model_dump()
            message_dict['booking_id'] = booking_id
            message_dict['sender_id'] = sender_id

            # Create the message
            message = await self.repository.create_message(
                booking_id=booking_id,
                sender_id=sender_id,
                recipient_id=message_dict.get('recipient_id'),
                message_data=message_dict
            )

            # Process attachments if provided
            if attachments:
                await self._process_message_attachments(message.id, attachments)

            # Initialize multi-channel delivery
            await self._initialize_message_delivery(message)

            # Send real-time notifications
            await self._send_message_notifications(message)

            operation_time = time.time() - start_time
            logger.info(f"Message sent successfully in {operation_time:.3f}s, message_id={message.id}")

            return BookingMessageResponse.model_validate(message)

        except Exception as e:
            logger.error(f"Failed to send message: {str(e)}")
            raise ServiceError(f"Failed to send message: {str(e)}")

    async def get_thread_messages(
        self,
        thread_id: UUID,
        user_id: Optional[int] = None,
        message_type: Optional[MessageType] = None,
        unread_only: bool = False,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[BookingMessageResponse]:
        """Get messages for a conversation thread with access control."""
        logger.info(f"Getting thread messages for thread_id={thread_id}")

        try:
            # Validate thread access if user_id provided
            if user_id:
                await self._validate_thread_access(thread_id, user_id)

            # Get messages from repository
            result = await self.repository.get_thread_messages(
                thread_id=thread_id,
                user_id=user_id,
                message_type=message_type,
                unread_only=unread_only,
                pagination=pagination
            )

            # Convert to response schemas
            message_schemas = [
                BookingMessageResponse.model_validate(msg)
                for msg in result.items
            ]

            logger.info(f"Retrieved {len(message_schemas)} thread messages")

            return QueryResult(
                items=message_schemas,
                total=result.total,
                page=result.page,
                size=result.size,
                has_next=result.has_next,
                has_previous=result.has_previous
            )

        except Exception as e:
            logger.error(f"Failed to get thread messages: {str(e)}")
            raise ServiceError(f"Failed to get thread messages: {str(e)}")

    async def mark_message_as_read(
        self,
        message_id: int,
        user_id: int
    ) -> Optional[BookingMessageResponse]:
        """Mark message as read with authorization validation."""
        logger.info(f"Marking message {message_id} as read by user {user_id}")

        try:
            message = await self.repository.mark_message_as_read(message_id, user_id)
            if not message:
                raise NotFoundError("Message not found or user not authorized")

            logger.info(f"Message {message_id} marked as read by user {user_id}")
            return BookingMessageResponse.model_validate(message)

        except Exception as e:
            logger.error(f"Failed to mark message as read: {str(e)}")
            raise ServiceError(f"Failed to mark message as read: {str(e)}")

    async def get_conversation_analytics(
        self,
        booking_id: int,
        thread_id: Optional[UUID] = None,
        user_id: Optional[int] = None
    ) -> MessageAnalyticsResponse:
        """Get conversation analytics with access control."""
        logger.info(f"Getting conversation analytics for booking_id={booking_id}")

        try:
            # Validate access if user_id provided
            if user_id:
                await self._validate_booking_access(booking_id, user_id)

            # Get analytics from repository
            analytics = await self.repository.get_conversation_analytics(
                booking_id=booking_id,
                thread_id=thread_id
            )

            logger.info(f"Retrieved conversation analytics for booking {booking_id}")
            return MessageAnalyticsResponse.model_validate(analytics)

        except Exception as e:
            logger.error(f"Failed to get conversation analytics: {str(e)}")
            raise ServiceError(f"Failed to get conversation analytics: {str(e)}")

    async def get_booking_messages(
        self,
        booking_id: int,
        user_id: Optional[int] = None,
        message_type: Optional[MessageType] = None,
        unread_only: bool = False,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[BookingMessageResponse]:
        """
        Get messages for a booking conversation with access control and performance optimization.

        Performance Metrics:
        - Target response time: <200ms for message retrieval
        - Database query time: <150ms with optimized pagination
        - Repository integration: Utilizes enhanced bulk operations and caching

        Args:
            booking_id: Booking ID to get messages for
            user_id: User ID for access control validation
            message_type: Optional message type filter
            unread_only: Whether to show only unread messages
            pagination: Pagination parameters

        Returns:
            QueryResult with BookingMessageResponse instances

        Raises:
            NotFoundError: If booking not found
            ValidationError: If user not authorized
            ServiceError: If retrieval fails
        """
        start_time = time.time()
        logger.info(f"Getting booking messages for booking_id={booking_id}")

        try:
            # Validate booking access if user_id provided
            if user_id:
                await self._validate_booking_access(booking_id, user_id)

            # Get messages from repository with enhanced performance
            result = await self.repository.get_booking_messages(
                booking_id=booking_id,
                user_id=user_id,
                message_type=message_type,
                unread_only=unread_only,
                pagination=pagination
            )

            # Convert to response schemas
            message_schemas = [
                BookingMessageResponse.model_validate(msg)
                for msg in result.items
            ]

            operation_time = time.time() - start_time
            logger.info(f"Retrieved {len(message_schemas)} booking messages in {operation_time:.3f}s", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "message_count": len(message_schemas),
                "operation_time": operation_time
            })

            return QueryResult(
                items=message_schemas,
                total=result.total,
                page=result.page,
                size=result.size,
                has_next=result.has_next,
                has_previous=result.has_previous
            )

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get booking messages in {operation_time:.3f}s: {str(e)}", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to get booking messages: {str(e)}")

    async def get_message(
        self,
        message_id: int,
        user_id: Optional[int] = None
    ) -> Optional[BookingMessageResponse]:
        """
        Get a single message with access control and enhanced repository integration.

        Performance Metrics:
        - Target response time: <100ms for single message retrieval
        - Database query time: <50ms with optimized single record access
        - Repository integration: Utilizes enhanced caching and eager loading

        Args:
            message_id: Message ID to retrieve
            user_id: User ID for access control validation

        Returns:
            BookingMessageResponse instance if found and authorized

        Raises:
            NotFoundError: If message not found
            ValidationError: If user not authorized
            ServiceError: If retrieval fails
        """
        start_time = time.time()
        logger.info(f"Getting message {message_id}")

        try:
            # Get message from repository
            message = await self.repository.get(message_id)
            if not message:
                raise NotFoundError(f"Message {message_id} not found")

            # Validate access if user_id provided
            if user_id:
                await self._validate_booking_access(message.booking_id, user_id)

            operation_time = time.time() - start_time
            logger.info(f"Message {message_id} retrieved in {operation_time:.3f}s", extra={
                "message_id": message_id,
                "user_id": user_id,
                "booking_id": message.booking_id,
                "operation_time": operation_time
            })

            return BookingMessageResponse.model_validate(message)

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get message {message_id} in {operation_time:.3f}s: {str(e)}", extra={
                "message_id": message_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to get message: {str(e)}")

    async def bulk_mark_messages_read(
        self,
        message_ids: List[int],
        user_id: int
    ) -> List[BookingMessageResponse]:
        """
        Mark multiple messages as read using enhanced repository bulk operations.

        Performance Metrics:
        - Target response time: <200ms for bulk read operations
        - Database transaction time: <150ms with optimized batch updates
        - Throughput: >1000 messages/second for bulk operations

        Args:
            message_ids: List of message IDs to mark as read
            user_id: User ID marking messages as read

        Returns:
            List of updated BookingMessageResponse instances

        Raises:
            ValidationError: If user not authorized for any messages
            ServiceError: If bulk update fails
        """
        start_time = time.time()
        logger.info(f"Bulk marking {len(message_ids)} messages as read for user {user_id}")

        try:
            # Use enhanced repository bulk operations
            updated_messages = await self.repository.bulk_mark_messages_read(
                message_ids=message_ids,
                user_id=user_id
            )

            # Convert to response schemas
            message_schemas = [
                BookingMessageResponse.model_validate(msg)
                for msg in updated_messages
            ]

            operation_time = time.time() - start_time
            logger.info(f"Bulk marked {len(message_schemas)} messages as read in {operation_time:.3f}s", extra={
                "user_id": user_id,
                "message_count": len(message_schemas),
                "operation_time": operation_time
            })

            return message_schemas

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to bulk mark messages as read in {operation_time:.3f}s: {str(e)}", extra={
                "user_id": user_id,
                "message_count": len(message_ids),
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to bulk mark messages as read: {str(e)}")

    async def bulk_send_messages(
        self,
        messages_data: List[Dict[str, Any]]
    ) -> List[BookingMessageResponse]:
        """
        Send multiple messages using enhanced repository bulk operations.

        Performance Metrics:
        - Target response time: <300ms for bulk message creation
        - Database transaction time: <200ms with optimized batch inserts
        - Throughput: >1000 messages/second for bulk operations

        Args:
            messages_data: List of message creation data dictionaries

        Returns:
            List of created BookingMessageResponse instances

        Raises:
            ValidationError: If any message data is invalid
            ServiceError: If bulk creation fails
        """
        start_time = time.time()
        logger.info(f"Bulk sending {len(messages_data)} messages")

        try:
            # Use enhanced repository bulk operations
            created_messages = await self.repository.bulk_create_messages(messages_data)

            # Initialize delivery for all messages
            for message in created_messages:
                await self._initialize_message_delivery(message)
                await self._send_message_notifications(message)

            # Convert to response schemas
            message_schemas = [
                BookingMessageResponse.model_validate(msg)
                for msg in created_messages
            ]

            operation_time = time.time() - start_time
            logger.info(f"Bulk sent {len(message_schemas)} messages in {operation_time:.3f}s", extra={
                "message_count": len(message_schemas),
                "operation_time": operation_time
            })

            return message_schemas

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to bulk send messages in {operation_time:.3f}s: {str(e)}", extra={
                "message_count": len(messages_data),
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to bulk send messages: {str(e)}")

    async def get_conversation_summary(
        self,
        booking_id: int,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get conversation summary with enhanced analytics and participant information.

        Performance Metrics:
        - Target response time: <150ms for conversation summary
        - Database query optimization with aggregated data
        - Cached participant information for performance

        Args:
            booking_id: Booking ID to get conversation summary for
            user_id: User ID for access control validation

        Returns:
            Dictionary with conversation summary and analytics

        Raises:
            NotFoundError: If booking not found
            ValidationError: If user not authorized
            ServiceError: If summary generation fails
        """
        start_time = time.time()
        logger.info(f"Getting conversation summary for booking_id={booking_id}")

        try:
            # Validate booking access if user_id provided
            if user_id:
                await self._validate_booking_access(booking_id, user_id)

            # Get conversation analytics
            analytics = await self.repository.get_conversation_analytics(booking_id=booking_id)

            # Get recent messages for preview
            recent_messages = await self.repository.get_booking_messages(
                booking_id=booking_id,
                pagination=PaginationParams(page=1, per_page=5)
            )

            # Get participant information
            booking = await self.booking_repository.get(booking_id)
            participants = []

            if booking:
                # Add customer participant
                if booking.customer:
                    participants.append({
                        "user_id": booking.customer_id,
                        "name": f"{booking.customer.first_name} {booking.customer.last_name}",
                        "role": "customer",
                        "avatar_url": getattr(booking.customer, 'avatar_url', None)
                    })

                # Add vendor participant
                if booking.vendor and booking.vendor.user:
                    participants.append({
                        "user_id": booking.vendor.user_id,
                        "name": booking.vendor.business_name or f"{booking.vendor.user.first_name} {booking.vendor.user.last_name}",
                        "role": "vendor",
                        "avatar_url": getattr(booking.vendor, 'logo_url', None)
                    })

            operation_time = time.time() - start_time
            logger.info(f"Conversation summary generated in {operation_time:.3f}s", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "participant_count": len(participants),
                "operation_time": operation_time
            })

            return {
                "booking_id": booking_id,
                "analytics": analytics,
                "recent_messages": [
                    BookingMessageResponse.model_validate(msg) for msg in recent_messages.items
                ],
                "participants": participants,
                "conversation_status": "active" if analytics.get("total_messages", 0) > 0 else "inactive",
                "last_activity": analytics.get("last_message_at"),
                "unread_count": analytics.get("unread_count", 0)
            }

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get conversation summary in {operation_time:.3f}s: {str(e)}", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to get conversation summary: {str(e)}")

    async def mark_conversation_as_read(
        self,
        booking_id: int,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Mark all messages in a conversation as read for a specific user.

        Performance Metrics:
        - Target response time: <200ms for conversation read marking
        - Bulk update optimization for multiple messages
        - Efficient unread count recalculation

        Args:
            booking_id: Booking ID to mark conversation as read
            user_id: User ID marking conversation as read

        Returns:
            Dictionary with update results and statistics

        Raises:
            NotFoundError: If booking not found
            ValidationError: If user not authorized
            ServiceError: If marking fails
        """
        start_time = time.time()
        logger.info(f"Marking conversation as read for booking_id={booking_id}, user_id={user_id}")

        try:
            # Validate booking access
            await self._validate_booking_access(booking_id, user_id)

            # Get all unread messages for the user in this conversation
            unread_messages = await self.repository.get_booking_messages(
                booking_id=booking_id,
                user_id=user_id,
                unread_only=True,
                pagination=PaginationParams(page=1, per_page=1000)  # Get all unread
            )

            if not unread_messages.items:
                return {
                    "booking_id": booking_id,
                    "user_id": user_id,
                    "messages_marked": 0,
                    "status": "no_unread_messages"
                }

            # Extract message IDs
            message_ids = [msg.id for msg in unread_messages.items]

            # Bulk mark messages as read
            updated_messages = await self.bulk_mark_messages_read(message_ids, user_id)

            operation_time = time.time() - start_time
            logger.info(f"Conversation marked as read in {operation_time:.3f}s", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "messages_marked": len(updated_messages),
                "operation_time": operation_time
            })

            return {
                "booking_id": booking_id,
                "user_id": user_id,
                "messages_marked": len(updated_messages),
                "status": "success",
                "operation_time": operation_time
            }

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to mark conversation as read in {operation_time:.3f}s: {str(e)}", extra={
                "booking_id": booking_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to mark conversation as read: {str(e)}")

    # Helper methods
    async def _validate_thread_access(self, thread_id: UUID, user_id: int) -> None:
        """Validate user has access to thread."""
        try:
            messages = await self.repository.get_thread_messages(
                thread_id=thread_id,
                pagination=PaginationParams(page=1, per_page=1)
            )

            if not messages.items:
                raise NotFoundError("Thread not found")

            booking_id = messages.items[0].booking_id
            await self._validate_booking_access(booking_id, user_id)

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Thread access validation failed: {str(e)}")
            raise ValidationError("Thread access validation failed")

    async def _validate_booking_access(self, booking_id: int, user_id: int) -> None:
        """Validate user has access to booking."""
        try:
            booking = await self.booking_repository.get(booking_id)
            if not booking:
                raise NotFoundError("Booking not found")

            # Check if user is customer, vendor, or admin
            if user_id == booking.customer_id:
                return  # Customer authorized

            if booking.vendor and user_id == booking.vendor.user_id:
                return  # Vendor authorized

            # Check admin role
            user = await self.user_repository.get(user_id)
            if user and hasattr(user, 'role') and user.role in ['admin', 'system']:
                return  # Admin authorized

            raise ValidationError("User not authorized for this booking")

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Booking access validation failed: {str(e)}")
            raise ValidationError("Booking access validation failed")

    async def _process_message_attachments(
        self,
        message_id: int,
        attachments: List[MessageAttachmentCreate]
    ) -> None:
        """Process message attachments with security validation."""
        try:
            attachment_repo = MessageAttachmentRepository(self.db)

            for attachment_data in attachments:
                attachment_dict = attachment_data.model_dump()
                await attachment_repo.create_attachment(message_id, attachment_dict)

        except Exception as e:
            logger.error(f"Failed to process attachments: {str(e)}")
            raise ServiceError(f"Attachment processing failed: {str(e)}")

    async def _initialize_message_delivery(self, message: BookingMessage) -> None:
        """Initialize multi-channel delivery tracking."""
        try:
            delivery_repo = MessageDeliveryLogRepository(self.db)

            # Initialize delivery for different channels
            delivery_methods = [DeliveryMethod.WEBSOCKET]  # Always deliver via WebSocket

            # Add email delivery if recipient has email notifications enabled
            if message.recipient_id:
                delivery_methods.append(DeliveryMethod.EMAIL)
                delivery_methods.append(DeliveryMethod.PUSH)

            for method in delivery_methods:
                await delivery_repo.create_delivery_log(
                    message_id=message.id,
                    delivery_method=method,
                    delivery_data={"recipient_id": message.recipient_id}
                )

        except Exception as e:
            logger.error(f"Failed to initialize delivery tracking: {str(e)}")
            # Don't fail the message creation for delivery tracking issues

    async def _send_message_notifications(self, message: BookingMessage) -> None:
        """Send real-time notifications for new message."""
        try:
            if not message.recipient_id:
                return

            # Get booking for context
            booking = await self.booking_repository.get(message.booking_id)
            if not booking:
                return

            # Send email notification (async)
            try:
                await self.email_service.send_booking_message_notification(
                    user_id=message.recipient_id,
                    booking_reference=booking.booking_reference,
                    sender_name=await self._get_sender_name(message, booking),
                    message_preview=message.content[:100] + "..." if len(message.content) > 100 else message.content
                )
            except Exception as e:
                logger.warning(f"Email notification failed: {str(e)}")

            # Send push notification (async)
            try:
                await self.push_service.send_notification_to_user(
                    user_id=message.recipient_id,
                    title="New Booking Message",
                    body=f"You have a new message about booking {booking.booking_reference}",
                    payload={
                        "booking_id": booking.id,
                        "message_id": message.id,
                        "thread_id": str(message.thread_id),
                        "type": "new_message"
                    }
                )
            except Exception as e:
                logger.warning(f"Push notification failed: {str(e)}")

        except Exception as e:
            logger.error(f"Failed to send message notifications: {str(e)}")
            # Don't fail the message creation for notification issues

    async def _get_sender_name(self, message: BookingMessage, booking) -> str:
        """Get sender display name for notifications."""
        try:
            if not message.sender_id:
                return "System"

            if message.sender_id == booking.customer_id:
                return f"{booking.customer.first_name} {booking.customer.last_name}"
            elif booking.vendor and message.sender_id == booking.vendor.user_id:
                return booking.vendor.business_name or "Vendor"
            else:
                # Get user details
                user = await self.user_repository.get(message.sender_id)
                if user:
                    return f"{user.first_name} {user.last_name}"
                return "Unknown User"

        except Exception as e:
            logger.error(f"Failed to get sender name: {str(e)}")
            return "Unknown Sender"


class MessageAttachmentService:
    """Comprehensive message attachment service for file management."""

    def __init__(self, db: AsyncSession):
        """Initialize message attachment service with dependencies."""
        self.db = db
        self.repository = MessageAttachmentRepository(db)
        self.message_repository = BookingMessageRepository(db)
        self.user_repository = UserRepository(db)

    async def create_attachment(
        self,
        message_id: int,
        attachment_data: MessageAttachmentCreate,
        user_id: int
    ) -> MessageAttachmentResponse:
        """Create attachment with authorization and security validation."""
        logger.info(f"Creating attachment for message {message_id}")

        try:
            # Validate message exists and user has access
            message = await self.message_repository.get(message_id)
            if not message:
                raise NotFoundError("Message not found")

            # Create attachment
            attachment_dict = attachment_data.model_dump()
            attachment_dict['message_id'] = message_id

            attachment = await self.repository.create_attachment(message_id, attachment_dict)
            logger.info(f"Attachment created for message {message_id}")

            return MessageAttachmentResponse.model_validate(attachment)

        except Exception as e:
            logger.error(f"Failed to create attachment: {str(e)}")
            raise ServiceError(f"Failed to create attachment: {str(e)}")

    async def get_message_attachments(
        self,
        message_id: int,
        user_id: int,
        include_unsafe: bool = False
    ) -> List[MessageAttachmentResponse]:
        """Get attachments for message with access control."""
        logger.info(f"Getting attachments for message {message_id}")

        try:
            # Validate message access
            message = await self.message_repository.get(message_id)
            if not message:
                raise NotFoundError("Message not found")

            # Get attachments
            attachments = await self.repository.get_message_attachments(
                message_id=message_id,
                include_unsafe=include_unsafe
            )

            logger.info(f"Retrieved {len(attachments)} attachments for message {message_id}")

            return [
                MessageAttachmentResponse.model_validate(attachment)
                for attachment in attachments
            ]

        except Exception as e:
            logger.error(f"Failed to get attachments: {str(e)}")
            raise ServiceError(f"Failed to get attachments: {str(e)}")


class MessageTemplateService:
    """Comprehensive message template service for template management."""

    def __init__(self, db: AsyncSession):
        """Initialize message template service with dependencies."""
        self.db = db
        self.repository = MessageTemplateRepository(db)
        self.user_repository = UserRepository(db)

    async def create_template(
        self,
        template_data: MessageTemplateCreate,
        user_id: int
    ) -> MessageTemplateResponse:
        """Create template with authorization and syntax validation."""
        logger.info(f"Creating template with key: {template_data.template_key}")

        try:
            # Validate user authorization (admin only for now)
            await self._validate_template_authorization(user_id, 'create')

            # Create template
            template_dict = template_data.model_dump()
            template_dict['created_by'] = user_id

            template = await self.repository.create_template(template_dict)
            logger.info(f"Template created with key: {template.template_key}")

            return MessageTemplateResponse.model_validate(template)

        except Exception as e:
            logger.error(f"Failed to create template: {str(e)}")
            raise ServiceError(f"Failed to create template: {str(e)}")

    async def get_template_by_key(
        self,
        template_key: str,
        user_id: Optional[int] = None
    ) -> Optional[MessageTemplateResponse]:
        """Get template by key with access control."""
        logger.info(f"Getting template by key: {template_key}")

        try:
            # Validate access if user_id provided
            if user_id:
                await self._validate_template_authorization(user_id, 'read')

            # Get template
            template = await self.repository.get_template_by_key(template_key)
            if not template:
                return None

            logger.info(f"Template retrieved: {template_key}")
            return MessageTemplateResponse.model_validate(template)

        except Exception as e:
            logger.error(f"Failed to get template: {str(e)}")
            raise ServiceError(f"Failed to get template: {str(e)}")

    async def render_template(
        self,
        template_key: str,
        variables: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> Dict[str, str]:
        """Render template with variable substitution."""
        logger.info(f"Rendering template: {template_key}")

        try:
            # Get template
            template = await self.repository.get_template_by_key(template_key)
            if not template:
                raise NotFoundError(f"Template not found: {template_key}")

            # Validate access if user_id provided
            if user_id:
                await self._validate_template_authorization(user_id, 'read')

            # Render template
            rendered = await self._render_template_content(template, variables)
            logger.info(f"Template rendered: {template_key}")

            return rendered

        except Exception as e:
            logger.error(f"Failed to render template: {str(e)}")
            raise ServiceError(f"Failed to render template: {str(e)}")

    async def _validate_template_authorization(self, user_id: int, action: str) -> None:
        """Validate user is authorized for template operations."""
        try:
            user = await self.user_repository.get(user_id)
            if not user:
                raise NotFoundError("User not found")

            # For now, only admin users can manage templates
            if not hasattr(user, 'role') or user.role not in ['admin', 'system']:
                raise ValidationError(f"User not authorized for template {action}")

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Template authorization failed: {str(e)}")
            raise ValidationError("Template authorization failed")

    async def _render_template_content(
        self,
        template,
        variables: Dict[str, Any]
    ) -> Dict[str, str]:
        """Render template content with Jinja2."""
        try:
            from jinja2 import Template, TemplateError

            # Render subject
            subject_template = Template(template.subject_template)
            rendered_subject = subject_template.render(**variables)

            # Render content
            content_template = Template(template.content_template)
            rendered_content = content_template.render(**variables)

            return {
                "subject": rendered_subject,
                "content": rendered_content
            }

        except TemplateError as e:
            logger.error(f"Template rendering failed: {str(e)}")
            raise ValidationError(f"Template rendering failed: {str(e)}")
        except Exception as e:
            logger.error(f"Template rendering error: {str(e)}")
            raise ServiceError(f"Template rendering error: {str(e)}")


class MessageDeliveryService:
    """Comprehensive message delivery service for multi-channel orchestration."""

    def __init__(self, db: AsyncSession):
        """Initialize message delivery service with dependencies."""
        self.db = db
        self.repository = MessageDeliveryLogRepository(db)
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)
        self.message_repository = BookingMessageRepository(db)
        self.user_repository = UserRepository(db)

    async def create_delivery_log(
        self,
        message_id: int,
        delivery_method: DeliveryMethod,
        delivery_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create delivery log for message delivery tracking."""
        logger.info(f"Creating delivery log for message {message_id}")

        try:
            delivery_log = await self.repository.create_delivery_log(
                message_id=message_id,
                delivery_method=delivery_method,
                delivery_data=delivery_data or {}
            )

            logger.info(f"Delivery log created for message {message_id} via {delivery_method.value}")

            return {
                "id": delivery_log.id,
                "message_id": delivery_log.message_id,
                "delivery_method": delivery_log.delivery_method.value,
                "delivery_status": delivery_log.delivery_status.value,
                "attempted_at": delivery_log.attempted_at,
                "delivered_at": delivery_log.delivered_at,
                "retry_count": delivery_log.retry_count
            }

        except Exception as e:
            logger.error(f"Failed to create delivery log: {str(e)}")
            raise ServiceError(f"Failed to create delivery log: {str(e)}")

    async def update_delivery_status(
        self,
        delivery_log_id: int,
        status: DeliveryStatus,
        error_message: Optional[str] = None,
        delivery_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Update delivery status with retry logic."""
        logger.info(f"Updating delivery status for log {delivery_log_id}")

        try:
            delivery_log = await self.repository.update_delivery_status(
                delivery_log_id=delivery_log_id,
                status=status,
                error_message=error_message,
                delivery_metadata=delivery_metadata
            )

            if not delivery_log:
                raise NotFoundError(f"Delivery log {delivery_log_id} not found")

            logger.info(f"Delivery status updated to {status.value}")

            return {
                "id": delivery_log.id,
                "message_id": delivery_log.message_id,
                "delivery_method": delivery_log.delivery_method.value,
                "delivery_status": delivery_log.delivery_status.value,
                "attempted_at": delivery_log.attempted_at,
                "delivered_at": delivery_log.delivered_at,
                "retry_count": delivery_log.retry_count,
                "error_message": delivery_log.error_message
            }

        except Exception as e:
            logger.error(f"Failed to update delivery status: {str(e)}")
            raise ServiceError(f"Failed to update delivery status: {str(e)}")

    async def get_delivery_analytics(
        self,
        message_id: Optional[int] = None,
        delivery_method: Optional[DeliveryMethod] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get delivery analytics with access control."""
        logger.info("Getting delivery analytics")

        try:
            # Validate access if user_id provided
            if user_id:
                await self._validate_analytics_authorization(user_id)

            # Get analytics from repository
            analytics = await self.repository.get_delivery_analytics(
                message_id=message_id,
                delivery_method=delivery_method,
                start_date=start_date,
                end_date=end_date
            )

            logger.info("Retrieved delivery analytics")
            return analytics

        except Exception as e:
            logger.error(f"Failed to get delivery analytics: {str(e)}")
            raise ServiceError(f"Failed to get delivery analytics: {str(e)}")

    async def orchestrate_multi_channel_delivery(
        self,
        message_id: int,
        delivery_channels: Optional[List[DeliveryMethod]] = None
    ) -> Dict[str, Any]:
        """
        Orchestrate multi-channel delivery with retry logic and performance tracking.

        Performance Metrics:
        - Target response time: <100ms for delivery orchestration
        - Retry logic: Exponential backoff with max 3 retries per channel
        - Success rate tracking: >95% delivery success rate target

        Args:
            message_id: Message ID to deliver
            delivery_channels: Optional list of delivery channels (defaults to all)

        Returns:
            Dictionary with delivery results and performance metrics

        Raises:
            NotFoundError: If message not found
            ServiceError: If delivery orchestration fails
        """
        start_time = time.time()
        logger.info(f"Orchestrating multi-channel delivery for message {message_id}")

        try:
            # Get message details
            message = await self.message_repository.get(message_id)
            if not message:
                raise NotFoundError(f"Message {message_id} not found")

            # Default delivery channels if not specified
            if delivery_channels is None:
                delivery_channels = [DeliveryMethod.WEBSOCKET, DeliveryMethod.EMAIL, DeliveryMethod.PUSH]

            delivery_results = {}

            # Process each delivery channel
            for channel in delivery_channels:
                try:
                    # Create delivery log
                    delivery_log = await self.create_delivery_log(
                        message_id=message_id,
                        delivery_method=channel,
                        delivery_data={"recipient_id": message.recipient_id}
                    )

                    # Attempt delivery with retry logic
                    delivery_result = await self._attempt_delivery_with_retry(
                        message=message,
                        delivery_method=channel,
                        delivery_log_id=delivery_log["id"]
                    )

                    delivery_results[channel.value] = delivery_result

                except Exception as e:
                    logger.error(f"Failed delivery for channel {channel.value}: {str(e)}")
                    delivery_results[channel.value] = {
                        "status": "failed",
                        "error": str(e),
                        "retry_count": 0
                    }

            operation_time = time.time() - start_time
            logger.info(f"Multi-channel delivery completed in {operation_time:.3f}s", extra={
                "message_id": message_id,
                "channels": [c.value for c in delivery_channels],
                "operation_time": operation_time,
                "results": delivery_results
            })

            return {
                "message_id": message_id,
                "delivery_results": delivery_results,
                "operation_time": operation_time,
                "total_channels": len(delivery_channels),
                "successful_channels": len([r for r in delivery_results.values() if r.get("status") == "delivered"])
            }

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to orchestrate delivery in {operation_time:.3f}s: {str(e)}", extra={
                "message_id": message_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise ServiceError(f"Failed to orchestrate multi-channel delivery: {str(e)}")

    async def _attempt_delivery_with_retry(
        self,
        message: BookingMessage,
        delivery_method: DeliveryMethod,
        delivery_log_id: int,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        Attempt delivery with exponential backoff retry logic.

        Performance Metrics:
        - Retry intervals: 1s, 2s, 4s (exponential backoff)
        - Max retry attempts: 3 per delivery method
        - Circuit breaker: Fail fast after max retries

        Args:
            message: BookingMessage instance to deliver
            delivery_method: Delivery method to use
            delivery_log_id: Delivery log ID for tracking
            max_retries: Maximum retry attempts

        Returns:
            Dictionary with delivery result and retry information
        """
        import asyncio

        retry_count = 0
        last_error = None

        while retry_count <= max_retries:
            try:
                # Calculate retry delay (exponential backoff)
                if retry_count > 0:
                    delay = 2 ** (retry_count - 1)  # 1s, 2s, 4s
                    logger.info(f"Retrying delivery after {delay}s delay (attempt {retry_count + 1})")
                    await asyncio.sleep(delay)

                # Attempt delivery based on method
                if delivery_method == DeliveryMethod.EMAIL:
                    await self._deliver_via_email(message)
                elif delivery_method == DeliveryMethod.PUSH:
                    await self._deliver_via_push(message)
                elif delivery_method == DeliveryMethod.WEBSOCKET:
                    await self._deliver_via_websocket(message)
                elif delivery_method == DeliveryMethod.SMS:
                    await self._deliver_via_sms(message)

                # Update delivery status to delivered
                await self.update_delivery_status(
                    delivery_log_id=delivery_log_id,
                    status=DeliveryStatus.DELIVERED,
                    delivery_metadata={"retry_count": retry_count}
                )

                logger.info(f"Delivery successful via {delivery_method.value} after {retry_count} retries")

                return {
                    "status": "delivered",
                    "delivery_method": delivery_method.value,
                    "retry_count": retry_count,
                    "delivered_at": datetime.now().isoformat()
                }

            except Exception as e:
                last_error = str(e)
                retry_count += 1

                logger.warning(f"Delivery attempt {retry_count} failed via {delivery_method.value}: {last_error}")

                # Update delivery status to failed if max retries exceeded
                if retry_count > max_retries:
                    await self.update_delivery_status(
                        delivery_log_id=delivery_log_id,
                        status=DeliveryStatus.FAILED,
                        error_message=last_error,
                        delivery_metadata={"retry_count": retry_count - 1}
                    )

        logger.error(f"Delivery failed via {delivery_method.value} after {max_retries} retries: {last_error}")

        return {
            "status": "failed",
            "delivery_method": delivery_method.value,
            "retry_count": retry_count - 1,
            "error": last_error,
            "failed_at": datetime.now().isoformat()
        }

    async def _deliver_via_email(self, message: BookingMessage) -> None:
        """Deliver message via email channel."""
        try:
            if not message.recipient_id:
                raise ValidationError("No recipient for email delivery")

            # Get booking for context
            booking = await self.message_repository.db.get(message.booking_id)
            if not booking:
                raise NotFoundError("Booking not found for email context")

            # Send email notification
            await self.email_service.send_booking_message_notification(
                user_id=message.recipient_id,
                booking_reference=booking.booking_reference,
                sender_name="Message Sender",  # This would be enhanced with actual sender name
                message_preview=message.content[:100] + "..." if len(message.content) > 100 else message.content
            )

            logger.info(f"Email delivered successfully for message {message.id}")

        except Exception as e:
            logger.error(f"Email delivery failed for message {message.id}: {str(e)}")
            raise ServiceError(f"Email delivery failed: {str(e)}")

    async def _deliver_via_push(self, message: BookingMessage) -> None:
        """Deliver message via push notification channel."""
        try:
            if not message.recipient_id:
                raise ValidationError("No recipient for push delivery")

            # Send push notification
            await self.push_service.send_notification_to_user(
                user_id=message.recipient_id,
                title="New Booking Message",
                body=f"You have a new message: {message.content[:50]}...",
                payload={
                    "booking_id": message.booking_id,
                    "message_id": message.id,
                    "thread_id": str(message.thread_id),
                    "type": "new_message"
                }
            )

            logger.info(f"Push notification delivered successfully for message {message.id}")

        except Exception as e:
            logger.error(f"Push delivery failed for message {message.id}: {str(e)}")
            raise ServiceError(f"Push delivery failed: {str(e)}")

    async def _deliver_via_websocket(self, message: BookingMessage) -> None:
        """
        Deliver message via WebSocket channel with real-time integration.

        Enhanced WebSocket delivery with connection management and real-time broadcasting.
        Integrates with existing WebSocket infrastructure for immediate message delivery.
        """
        try:
            if not message.recipient_id:
                raise ValidationError("No recipient for WebSocket delivery")

            # Get booking for context
            booking = await self.message_repository.db.get(message.booking_id)
            if not booking:
                raise NotFoundError("Booking not found for WebSocket context")

            # Prepare WebSocket message payload
            websocket_payload = {
                "type": "new_message",
                "event": "booking_message_received",
                "data": {
                    "message_id": message.id,
                    "booking_id": message.booking_id,
                    "thread_id": str(message.thread_id),
                    "sender_id": message.sender_id,
                    "content": message.content,
                    "message_type": message.message_type.value,
                    "created_at": message.created_at.isoformat(),
                    "booking_reference": booking.booking_reference if booking else None
                },
                "timestamp": datetime.now().isoformat(),
                "correlation_id": correlation_id.get('')
            }

            # Real-time WebSocket delivery integration
            # This integrates with the existing WebSocket infrastructure
            try:
                # Import WebSocket manager (assuming it exists in the codebase)
                from app.services.websocket_service import WebSocketService
                websocket_service = WebSocketService(self.db)

                # Send message to recipient's active connections
                await websocket_service.send_to_user(
                    user_id=message.recipient_id,
                    message=websocket_payload
                )

                logger.info(f"WebSocket message delivered successfully to user {message.recipient_id}")

            except ImportError:
                # Fallback: Log for future WebSocket integration
                logger.info(f"WebSocket delivery prepared for message {message.id} (WebSocket service not available)")

            except Exception as ws_error:
                # Handle WebSocket-specific errors but don't fail the delivery
                logger.warning(f"WebSocket delivery failed, will retry: {str(ws_error)}")
                raise ServiceError(f"WebSocket delivery failed: {str(ws_error)}")

        except Exception as e:
            logger.error(f"WebSocket delivery failed for message {message.id}: {str(e)}")
            raise ServiceError(f"WebSocket delivery failed: {str(e)}")

    async def _deliver_via_sms(self, message: BookingMessage) -> None:
        """Deliver message via SMS channel."""
        try:
            if not message.recipient_id:
                raise ValidationError("No recipient for SMS delivery")

            # SMS delivery would integrate with SMS provider
            logger.info(f"SMS delivery simulated for message {message.id}")

            # In a real implementation, this would:
            # 1. Get user's phone number
            # 2. Format message for SMS
            # 3. Send via SMS provider (Twilio, etc.)
            # 4. Handle delivery confirmations

        except Exception as e:
            logger.error(f"SMS delivery failed for message {message.id}: {str(e)}")
            raise ServiceError(f"SMS delivery failed: {str(e)}")

    async def _validate_analytics_authorization(self, user_id: int) -> None:
        """Validate user is authorized for analytics access."""
        try:
            user = await self.user_repository.get(user_id)
            if not user:
                raise NotFoundError("User not found")

            # For now, only admin users can access delivery analytics
            if not hasattr(user, 'role') or user.role not in ['admin', 'system']:
                raise ValidationError("User not authorized for delivery analytics")

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Analytics authorization failed: {str(e)}")
            raise ValidationError("Analytics authorization failed")


# Main service class that combines all communication functionality
class BookingCommunicationService:
    """
    Main booking communication service that orchestrates all communication functionality.

    This service provides a unified interface for booking communication management,
    combining message handling, template management, and delivery orchestration.
    """

    def __init__(self, db: AsyncSession):
        """Initialize booking communication service with all sub-services."""
        self.db = db
        self.message_service = BookingMessageService(db)
        self.template_service = MessageTemplateService(db)
        self.delivery_service = MessageDeliveryService(db)

    # Delegate message operations to message service
    async def send_message(self, booking_id: int, sender_id: Optional[int], message_data: BookingMessageCreate, attachments: Optional[List[MessageAttachmentCreate]] = None):
        """Send a message through the message service."""
        return await self.message_service.send_message(booking_id, sender_id, message_data, attachments)

    async def get_booking_messages(self, booking_id: int, user_id: Optional[int] = None, message_type: Optional[MessageType] = None, unread_only: bool = False, pagination: Optional[PaginationParams] = None):
        """Get booking messages through the message service."""
        return await self.message_service.get_booking_messages(booking_id, user_id, message_type, unread_only, pagination)

    async def mark_message_as_read(self, message_id: int, user_id: int):
        """Mark message as read through the message service."""
        return await self.message_service.mark_message_as_read(message_id, user_id)

    async def get_conversation_analytics(self, booking_id: int, thread_id: Optional[UUID] = None, user_id: Optional[int] = None):
        """Get conversation analytics through the message service."""
        return await self.message_service.get_conversation_analytics(booking_id, thread_id, user_id)

    # Delegate template operations to template service
    async def create_template(self, template_data: MessageTemplateCreate):
        """Create message template through the template service."""
        return await self.template_service.create_template(template_data)

    async def render_template(self, template_id: int, context_data: Dict[str, Any]):
        """Render message template through the template service."""
        return await self.template_service.render_template(template_id, context_data)

    # Delegate delivery operations to delivery service
    async def track_delivery_status(self, message_id: int, delivery_method: DeliveryMethod):
        """Track delivery status through the delivery service."""
        return await self.delivery_service.track_delivery_status(message_id, delivery_method)


class BookingModificationService:
    """
    Booking modification service for handling booking change requests.

    This service manages booking modification workflows including:
    - Modification request creation and validation
    - Approval workflow management
    - Notification integration for modification updates
    - Integration with booking communication system
    """

    def __init__(self, db: AsyncSession):
        """Initialize booking modification service with dependencies."""
        self.db = db
        self.booking_repository = BookingRepository(db)
        self.user_repository = UserRepository(db)
        self.email_service = EmailService(db)
        self.push_service = PushNotificationService(db)

    async def create_modification_request(self, booking_id: int, modification_data: Dict[str, Any], user_id: int):
        """Create a new booking modification request."""
        try:
            # Validate booking exists and user has access
            booking = await self.booking_repository.get(booking_id)
            if not booking:
                raise NotFoundError("Booking not found")

            # Create modification request (placeholder implementation)
            modification = {
                'booking_id': booking_id,
                'requested_by': user_id,
                'modification_data': modification_data,
                'status': 'pending',
                'created_at': datetime.utcnow()
            }

            logger.info(f"Modification request created for booking {booking_id}")
            return modification

        except Exception as e:
            logger.error(f"Failed to create modification request: {str(e)}")
            raise ServiceError(f"Failed to create modification request: {str(e)}")

    async def approve_modification(self, modification_id: int, approver_id: int):
        """Approve a booking modification request."""
        try:
            # Placeholder implementation
            logger.info(f"Modification {modification_id} approved by user {approver_id}")
            return {"status": "approved", "approved_by": approver_id}

        except Exception as e:
            logger.error(f"Failed to approve modification: {str(e)}")
            raise ServiceError(f"Failed to approve modification: {str(e)}")

    async def reject_modification(self, modification_id: int, rejector_id: int, reason: str = None):
        """Reject a booking modification request."""
        try:
            # Placeholder implementation
            logger.info(f"Modification {modification_id} rejected by user {rejector_id}")
            return {"status": "rejected", "rejected_by": rejector_id, "reason": reason}

        except Exception as e:
            logger.error(f"Failed to reject modification: {str(e)}")
            raise ServiceError(f"Failed to reject modification: {str(e)}")
