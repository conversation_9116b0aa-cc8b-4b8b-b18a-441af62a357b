"""
Payment background tasks for Culture Connect Backend API.

This module provides comprehensive payment task processing including:
- Payment processing and verification
- Fraud detection and analysis
- Payment reconciliation
- Payout processing
- Transaction monitoring

Implements Task 6.2.1 requirements for payment task processing with
production-grade security and reliability.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from uuid import UUID

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="payment.process_payment", queue="critical")
def process_payment(self, payment_data: Dict[str, Any]):
    """
    Process payment transaction.
    
    Args:
        payment_data: Payment data including amount, currency, payment_method
    """
    try:
        async def _process_payment():
            from app.services.payment_service import PaymentService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                payment_service = PaymentService(session)
                
                # Extract payment parameters
                booking_id = UUID(payment_data["booking_id"])
                amount = payment_data["amount"]
                currency = payment_data["currency"]
                payment_method = payment_data["payment_method"]
                user_id = UUID(payment_data["user_id"])
                
                # Process payment
                payment_result = await payment_service.process_payment(
                    booking_id=booking_id,
                    amount=amount,
                    currency=currency,
                    payment_method=payment_method,
                    user_id=user_id,
                    metadata=payment_data.get("metadata", {})
                )
                
                logger.info(f"Payment processed for booking {booking_id}", extra={
                    "booking_id": str(booking_id),
                    "amount": amount,
                    "currency": currency,
                    "payment_id": payment_result.payment_id,
                    "status": payment_result.status,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "payment_id": payment_result.payment_id,
                    "status": payment_result.status,
                    "transaction_id": payment_result.transaction_id,
                    "provider_response": payment_result.provider_response
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_payment())
            return {
                "status": "success",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process payment: {str(e)}", extra={
            "payment_data": payment_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=30, max_retries=3)


@celery_app.task(bind=True, name="payment.verify_payment", queue="critical")
def verify_payment(self, verification_data: Dict[str, Any]):
    """
    Verify payment transaction status.
    
    Args:
        verification_data: Payment verification data
    """
    try:
        async def _verify_payment():
            from app.services.payment_service import PaymentService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                payment_service = PaymentService(session)
                
                payment_id = UUID(verification_data["payment_id"])
                provider_transaction_id = verification_data.get("provider_transaction_id")
                
                # Verify payment
                verification_result = await payment_service.verify_payment(
                    payment_id=payment_id,
                    provider_transaction_id=provider_transaction_id
                )
                
                logger.info(f"Payment verification completed for {payment_id}", extra={
                    "payment_id": str(payment_id),
                    "verification_status": verification_result.status,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "payment_id": str(payment_id),
                    "verification_status": verification_result.status,
                    "verified_amount": verification_result.verified_amount,
                    "provider_status": verification_result.provider_status
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_verify_payment())
            return {
                "status": "success",
                "verified_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to verify payment: {str(e)}", extra={
            "verification_data": verification_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="payment.detect_fraud", queue="critical")
def detect_fraud(self, fraud_check_data: Dict[str, Any]):
    """
    Perform fraud detection analysis.
    
    Args:
        fraud_check_data: Data for fraud analysis
    """
    try:
        async def _detect_fraud():
            from app.services.fraud_detection_service import FraudDetectionService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                fraud_service = FraudDetectionService(session)
                
                transaction_id = UUID(fraud_check_data["transaction_id"])
                user_id = UUID(fraud_check_data["user_id"])
                transaction_data = fraud_check_data["transaction_data"]
                
                # Perform fraud detection
                fraud_result = await fraud_service.analyze_transaction(
                    transaction_id=transaction_id,
                    user_id=user_id,
                    transaction_data=transaction_data,
                    real_time=True
                )
                
                logger.info(f"Fraud detection completed for transaction {transaction_id}", extra={
                    "transaction_id": str(transaction_id),
                    "risk_score": fraud_result.risk_score,
                    "fraud_detected": fraud_result.fraud_detected,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "transaction_id": str(transaction_id),
                    "risk_score": fraud_result.risk_score,
                    "fraud_detected": fraud_result.fraud_detected,
                    "risk_factors": fraud_result.risk_factors,
                    "recommended_action": fraud_result.recommended_action
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_detect_fraud())
            return {
                "status": "success",
                "analyzed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to detect fraud: {str(e)}", extra={
            "fraud_check_data": fraud_check_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=30, max_retries=2)


@celery_app.task(bind=True, name="payment.process_payout", queue="high_priority")
def process_payout(self, payout_data: Dict[str, Any]):
    """
    Process vendor payout.
    
    Args:
        payout_data: Payout data including vendor_id, amount, payout_method
    """
    try:
        async def _process_payout():
            from app.services.payout_service import PayoutService
            from app.db.session import get_async_session_context
            
            async with get_async_session_context() as session:
                payout_service = PayoutService(session)
                
                vendor_id = UUID(payout_data["vendor_id"])
                amount = payout_data["amount"]
                currency = payout_data["currency"]
                payout_method = payout_data["payout_method"]
                
                # Process payout
                payout_result = await payout_service.process_payout(
                    vendor_id=vendor_id,
                    amount=amount,
                    currency=currency,
                    payout_method=payout_method,
                    metadata=payout_data.get("metadata", {})
                )
                
                logger.info(f"Payout processed for vendor {vendor_id}", extra={
                    "vendor_id": str(vendor_id),
                    "amount": amount,
                    "currency": currency,
                    "payout_id": payout_result.payout_id,
                    "status": payout_result.status,
                    "correlation_id": correlation_id.get()
                })
                
                return {
                    "payout_id": payout_result.payout_id,
                    "status": payout_result.status,
                    "transaction_id": payout_result.transaction_id,
                    "estimated_arrival": payout_result.estimated_arrival
                }
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_process_payout())
            return {
                "status": "success",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **result
            }
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Failed to process payout: {str(e)}", extra={
            "payout_data": payout_data,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=120, max_retries=3)
