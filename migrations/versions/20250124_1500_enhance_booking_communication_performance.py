"""Enhance booking communication performance optimization for Task 4.1.3

Revision ID: enhance_booking_communication_performance
Revises: booking_workflow_tables
Create Date: 2025-01-24 15:00:00.000000

This migration enhances booking communication system performance with:
- Advanced composite indexes for conversation queries
- Message delivery tracking optimization
- Thread-based conversation performance
- Bulk operations optimization
- Real-time messaging performance enhancements
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'enhance_booking_comm_perf'
down_revision = 'booking_workflow_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade database schema - Enhance booking communication performance."""

    # ============================================================================
    # Enhanced Booking Messages Performance Indexes
    # ============================================================================

    # Conversation-based composite indexes for enhanced performance
    op.create_index(
        'idx_booking_communications_conversation_performance',
        'booking_communications',
        ['booking_id', 'created_at'],
        postgresql_using='btree'
    )

    # Unread message optimization for notification systems
    op.create_index(
        'idx_booking_communications_unread_optimization',
        'booking_communications',
        ['recipient_id', 'is_read', 'booking_id', 'created_at'],
        postgresql_using='btree',
        postgresql_where=sa.text('is_read = false')
    )

    # Bulk operations optimization for conversation management
    op.create_index(
        'idx_booking_communications_bulk_operations',
        'booking_communications',
        ['booking_id', 'recipient_id', 'is_read'],
        postgresql_using='btree'
    )

    # Message type and automation filtering
    op.create_index(
        'idx_booking_communications_type_automation',
        'booking_communications',
        ['communication_type', 'is_system_message', 'created_at'],
        postgresql_using='btree'
    )

    # ============================================================================
    # Enhanced Booking Communications Performance Indexes
    # ============================================================================

    # Communication response tracking optimization
    op.create_index(
        'idx_booking_communications_response_tracking',
        'booking_communications',
        ['requires_response', 'response_deadline', 'created_at'],
        postgresql_using='btree'
    )

    # Notification delivery optimization
    op.create_index(
        'idx_booking_communications_notification_delivery',
        'booking_communications',
        ['email_sent', 'push_sent', 'notification_sent_at'],
        postgresql_using='btree'
    )

    # Attachment handling optimization
    op.create_index(
        'idx_booking_communications_attachments',
        'booking_communications',
        ['has_attachments', 'created_at'],
        postgresql_using='btree'
    )

    # ============================================================================
    # Enhanced Constraints for Data Integrity
    # ============================================================================

    # Message content validation for booking communications
    op.create_check_constraint(
        'ck_booking_communications_message_length',
        'booking_communications',
        'length(message) >= 1 AND length(message) <= 10000'
    )

    # Subject length validation
    op.create_check_constraint(
        'ck_booking_communications_subject_length',
        'booking_communications',
        'subject IS NULL OR length(subject) <= 255'
    )

    # ============================================================================
    # Performance Optimization Comments for Monitoring
    # ============================================================================

    # Add performance monitoring comments
    op.execute("""
        COMMENT ON INDEX idx_booking_communications_conversation_performance IS
        'Performance index for conversation queries - Target: <200ms retrieval';
    """)

    op.execute("""
        COMMENT ON INDEX idx_booking_communications_unread_optimization IS
        'Partial index for unread message queries - Target: <100ms status updates';
    """)

    op.execute("""
        COMMENT ON INDEX idx_booking_communications_notification_delivery IS
        'Multi-channel delivery tracking - Target: <100ms delivery orchestration';
    """)


def downgrade() -> None:
    """Downgrade database schema - Remove enhanced booking communication indexes."""

    # Drop enhanced performance indexes
    op.drop_index('idx_booking_communications_conversation_performance', 'booking_communications')
    op.drop_index('idx_booking_communications_unread_optimization', 'booking_communications')
    op.drop_index('idx_booking_communications_bulk_operations', 'booking_communications')
    op.drop_index('idx_booking_communications_type_automation', 'booking_communications')
    op.drop_index('idx_booking_communications_response_tracking', 'booking_communications')
    op.drop_index('idx_booking_communications_notification_delivery', 'booking_communications')
    op.drop_index('idx_booking_communications_attachments', 'booking_communications')

    # Drop enhanced constraints
    op.drop_constraint('ck_booking_communications_message_length', 'booking_communications')
    op.drop_constraint('ck_booking_communications_subject_length', 'booking_communications')
