"""
Review Response Management API endpoints for Culture Connect Backend API.

This module provides comprehensive vendor response management endpoints including:
- Vendor response CRUD operations with workflow management
- Response status transitions (draft → published → hidden)
- Vendor response analytics and performance tracking
- Integration with authentication, RBAC, and notification systems

Implements Task 4.4.1 Phase 5 requirements for review response API endpoints with
production-grade FastAPI integration, authentication, rate limiting, and error handling.
"""

import logging
from datetime import datetime, date
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.review_response_service import ReviewResponseService
from app.schemas.review_schemas import (
    ReviewResponseCreateSchema, ReviewResponseUpdateSchema, VendorResponseSchema
)
from app.models.review_models import ResponseStatus
from app.repositories.base import PaginationParams
from app.api.v1.auth import get_current_user
from app.core.deps import require_role
from app.core.security import UserRole
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.post(
    "/reviews/{review_id}/response",
    response_model=VendorResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create vendor response",
    description="Create a vendor response to a review. Only vendors can respond to reviews of their services."
)
async def create_vendor_response(
    review_id: int = Path(..., description="Review ID", gt=0),
    response_data: ReviewResponseCreateSchema = None,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> VendorResponseSchema:
    """
    Create a vendor response to a review.

    **Requirements:**
    - User must be authenticated as a vendor
    - Review must be approved and belong to the vendor's services
    - Only one response per review is allowed
    - Response can be created as draft or published directly

    **Performance Target:** <200ms for response creation including validation

    Args:
        review_id: Review ID to respond to
        response_data: Response creation data with content and status
        current_user: Current authenticated user from JWT token
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        VendorResponseSchema: Created response with status and metadata

    Raises:
        HTTPException: 400 if review not eligible, 403 if not vendor's review, 409 if response exists
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate vendor role
        if current_user.get("role") != UserRole.VENDOR.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only vendors can create responses to reviews"
            )

        # Initialize response service
        response_service = ReviewResponseService(db)

        # Create vendor response
        response = await response_service.create_response(
            vendor_id=current_user["id"],
            review_id=review_id,
            response_data=response_data
        )

        # Track metrics
        metrics_collector.increment_counter(
            "api_vendor_response_created",
            tags={"status": response.status.value, "vendor_id": str(current_user["id"])}
        )

        logger.info(
            f"Vendor response created successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "vendor_id": current_user["id"],
                "review_id": review_id,
                "response_id": response.id,
                "status": response.status.value
            }
        )

        return VendorResponseSchema.model_validate(response)

    except ValueError as e:
        logger.warning(f"Vendor response creation validation failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Review {review_id} not found"
            )
        elif "can only respond to their own" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vendors can only respond to reviews of their services"
            )
        elif "not eligible" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Review is not eligible for vendor response"
            )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Vendor response creation failed: {str(e)}")
        if "already exists" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Response already exists for this review"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create vendor response"
        )


@router.put(
    "/responses/{response_id}",
    response_model=VendorResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Update vendor response",
    description="Update an existing vendor response. Only the response author can update their responses."
)
async def update_vendor_response(
    response_id: int = Path(..., description="Response ID", gt=0),
    response_data: ReviewResponseUpdateSchema = None,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> VendorResponseSchema:
    """
    Update an existing vendor response.

    **Requirements:**
    - User must be the original response author
    - Response must not be hidden
    - Content changes are allowed for draft and published responses

    **Performance Target:** <200ms for response updates

    Args:
        response_id: Response ID to update
        response_data: Response update data
        current_user: Current authenticated user from JWT token
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        VendorResponseSchema: Updated response information

    Raises:
        HTTPException: 403 if not response author, 404 if response not found, 422 if validation fails
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate vendor role
        if current_user.get("role") != UserRole.VENDOR.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only vendors can update responses"
            )

        # Initialize response service
        response_service = ReviewResponseService(db)

        # Update vendor response
        updated_response = await response_service.update_response(
            response_id=response_id,
            vendor_id=current_user["id"],
            update_data=response_data
        )

        logger.info(
            f"Vendor response updated successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "vendor_id": current_user["id"],
                "response_id": response_id
            }
        )

        return VendorResponseSchema.model_validate(updated_response)

    except ValueError as e:
        logger.warning(f"Vendor response update validation failed: {str(e)}")
        if "can only update their own" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only update your own responses"
            )
        elif "cannot be edited" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Vendor response update failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Response {response_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update vendor response"
        )


@router.post(
    "/responses/{response_id}/publish",
    response_model=VendorResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Publish vendor response",
    description="Publish a draft vendor response. Only draft responses can be published."
)
async def publish_vendor_response(
    response_id: int = Path(..., description="Response ID", gt=0),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> VendorResponseSchema:
    """
    Publish a draft vendor response.

    **Requirements:**
    - User must be the response author
    - Response must be in draft status
    - Published responses trigger customer notifications

    **Performance Target:** <100ms for status update

    Args:
        response_id: Response ID to publish
        current_user: Current authenticated user from JWT token
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        VendorResponseSchema: Published response information

    Raises:
        HTTPException: 403 if not response author, 404 if response not found, 400 if not draft
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate vendor role
        if current_user.get("role") != UserRole.VENDOR.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only vendors can publish responses"
            )

        # Initialize response service
        response_service = ReviewResponseService(db)

        # Publish vendor response
        published_response = await response_service.publish_response(
            response_id=response_id,
            vendor_id=current_user["id"]
        )

        # Track metrics
        metrics_collector.increment_counter(
            "api_vendor_response_published",
            tags={"vendor_id": str(current_user["id"])}
        )

        logger.info(
            f"Vendor response published successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "vendor_id": current_user["id"],
                "response_id": response_id
            }
        )

        return VendorResponseSchema.model_validate(published_response)

    except ValueError as e:
        logger.warning(f"Vendor response publish validation failed: {str(e)}")
        if "can only publish their own" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only publish your own responses"
            )
        elif "only draft responses" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only draft responses can be published"
            )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Vendor response publish failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Response {response_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish vendor response"
        )


@router.post(
    "/responses/{response_id}/hide",
    response_model=VendorResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Hide vendor response",
    description="Hide a published vendor response. Hidden responses are not visible to customers."
)
async def hide_vendor_response(
    response_id: int = Path(..., description="Response ID", gt=0),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> VendorResponseSchema:
    """
    Hide a published vendor response.

    **Requirements:**
    - User must be the response author
    - Response can be in any status except already hidden
    - Hidden responses can be unhidden by updating status

    **Performance Target:** <100ms for status update

    Args:
        response_id: Response ID to hide
        current_user: Current authenticated user from JWT token
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        VendorResponseSchema: Hidden response information

    Raises:
        HTTPException: 403 if not response author, 404 if response not found
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Validate vendor role
        if current_user.get("role") != UserRole.VENDOR.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only vendors can hide responses"
            )

        # Initialize response service
        response_service = ReviewResponseService(db)

        # Hide vendor response
        hidden_response = await response_service.hide_response(
            response_id=response_id,
            vendor_id=current_user["id"]
        )

        logger.info(
            f"Vendor response hidden successfully",
            extra={
                "correlation_id": correlation_id.get(''),
                "vendor_id": current_user["id"],
                "response_id": response_id
            }
        )

        return VendorResponseSchema.model_validate(hidden_response)

    except ValueError as e:
        logger.warning(f"Vendor response hide validation failed: {str(e)}")
        if "can only hide their own" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only hide your own responses"
            )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Vendor response hide failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Response {response_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to hide vendor response"
        )


@router.get(
    "/vendors/{vendor_id}/responses",
    response_model=List[VendorResponseSchema],
    status_code=status.HTTP_200_OK,
    summary="Get vendor responses",
    description="Get all responses for a specific vendor with filtering and pagination."
)
async def get_vendor_responses(
    vendor_id: int = Path(..., description="Vendor ID", gt=0),
    status_filter: Optional[List[ResponseStatus]] = Query(None, description="Response status filter"),
    date_from: Optional[date] = Query(None, description="Filter responses from date"),
    date_to: Optional[date] = Query(None, description="Filter responses to date"),
    page: int = Query(1, description="Page number", ge=1),
    per_page: int = Query(20, description="Items per page", ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
    request: Request = None
) -> List[VendorResponseSchema]:
    """
    Get all responses for a specific vendor.

    **Performance Target:** <200ms for vendor response queries with filtering

    Args:
        vendor_id: Vendor ID to get responses for
        status_filter: Optional list of response statuses to filter
        date_from: Optional start date filter
        date_to: Optional end date filter
        page: Page number for pagination
        per_page: Items per page (max 100)
        current_user: Current authenticated user from JWT token
        db: Database session dependency
        request: FastAPI request object for correlation tracking

    Returns:
        List[VendorResponseSchema]: List of vendor responses

    Raises:
        HTTPException: 403 if not authorized to view vendor responses
    """
    try:
        # Set correlation ID for request tracking
        correlation_id.set(getattr(request.state, 'correlation_id', ''))

        # Authorization check - vendors can only see their own responses
        if current_user.get("role") == UserRole.VENDOR.value and current_user["id"] != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vendors can only view their own responses"
            )

        # Initialize response service
        response_service = ReviewResponseService(db)

        # Build date range
        date_range = None
        if date_from or date_to:
            date_range = (
                datetime.combine(date_from, datetime.min.time()) if date_from else None,
                datetime.combine(date_to, datetime.max.time()) if date_to else None
            )

        # Build pagination
        pagination = PaginationParams(page=page, per_page=per_page)

        # Get vendor responses
        result = await response_service.get_vendor_responses(
            vendor_id=vendor_id,
            status_filter=status_filter,
            date_range=date_range,
            pagination=pagination
        )

        logger.info(
            f"Vendor responses retrieved",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user["id"],
                "vendor_id": vendor_id,
                "results_count": len(result.items)
            }
        )

        return [VendorResponseSchema.model_validate(response) for response in result.items]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get vendor responses failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get vendor responses"
        )
