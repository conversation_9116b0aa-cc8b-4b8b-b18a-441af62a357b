"""
Email management endpoints for Culture Connect Backend API.

This module provides comprehensive email service endpoints including:
- Email template management (CRUD operations)
- Email sending and delivery tracking
- User email preference management
- Email queue monitoring and statistics
- Email notification workflows (verification, password reset, security alerts)

Implements Task 2.3.1 Phase 5 requirements for email service API endpoints with
production-grade FastAPI integration, authentication, and error handling.
"""

import logging
from typing import Dict, Any, Optional, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.services.email_service import EmailService
from app.schemas.email_schemas import (
    # Template schemas
    EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailTemplateListResponse,
    # Email sending schemas
    EmailSendRequest, EmailSendResponse, EmailBatchSendRequest, EmailBatchSendResponse,
    # Delivery tracking schemas
    EmailDeliveryResponse, EmailDeliveryListResponse, EmailDeliveryStatusUpdate,
    # Preference management schemas
    EmailPreferenceUpdate, EmailPreferenceResponse, EmailOptOutRequest, EmailOptOutResponse,
    # Queue management schemas
    EmailQueueResponse, EmailQueueListResponse,
    # Notification schemas
    EmailVerificationRequest, EmailVerificationResponse,
    PasswordResetEmailRequest, PasswordResetEmailResponse,
    # Analytics schemas
    EmailAnalyticsResponse
)
from app.schemas.auth import UserResponse, MessageResponse
from app.api.v1.auth import get_current_user, get_current_admin_user, require_permission
from app.models.email_models import EmailTemplateCategory, EmailDeliveryStatus, EmailQueueStatus
from app.core.logging import correlation_id
from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


# ============================================================================
# EMAIL TEMPLATE MANAGEMENT ENDPOINTS
# ============================================================================

@router.post(
    "/templates",
    response_model=EmailTemplateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create email template",
    description="Create a new email template with Jinja2 support and validation"
)
async def create_email_template(
    template_data: EmailTemplateCreate,
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailTemplateResponse:
    """
    Create a new email template.

    Args:
        template_data: Template creation data
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        EmailTemplateResponse: Created template information

    Raises:
        HTTPException: 400 if template data is invalid
        HTTPException: 409 if template name/version already exists
        HTTPException: 500 if template creation fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Create template
        template = await email_service.create_template(
            template_data=template_data,
            created_by=current_user.id
        )

        logger.info(
            f"Email template created: {template.name} v{template.version}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "template_id": str(template.id),
                "template_name": template.name
            }
        )

        return template

    except ValueError as e:
        logger.warning(f"Invalid template data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid template data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Template creation failed: {str(e)}")
        if "already exists" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create email template"
        )


@router.get(
    "/templates/{template_id}",
    response_model=EmailTemplateResponse,
    status_code=status.HTTP_200_OK,
    summary="Get email template",
    description="Retrieve email template by ID"
)
async def get_email_template(
    template_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailTemplateResponse:
    """
    Get email template by ID.

    Args:
        template_id: Template unique identifier
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailTemplateResponse: Template information

    Raises:
        HTTPException: 404 if template not found
        HTTPException: 500 if retrieval fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Get template
        template = await email_service.get_template(template_id)

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Email template {template_id} not found"
            )

        logger.info(
            f"Email template retrieved: {template.name}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Template retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve email template"
        )


@router.get(
    "/templates",
    response_model=EmailTemplateListResponse,
    status_code=status.HTTP_200_OK,
    summary="List email templates",
    description="List email templates with filtering and pagination"
)
async def list_email_templates(
    category: Optional[EmailTemplateCategory] = Query(None, description="Filter by template category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search in template name and content"),
    skip: int = Query(0, ge=0, description="Number of templates to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of templates to return"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailTemplateListResponse:
    """
    List email templates with filtering and pagination.

    Args:
        category: Optional category filter
        is_active: Optional active status filter
        search: Optional search term
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailTemplateListResponse: Paginated list of templates

    Raises:
        HTTPException: 500 if listing fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # List templates
        templates_response = await email_service.list_templates(
            category=category,
            is_active=is_active,
            search=search,
            skip=skip,
            limit=limit
        )

        logger.info(
            f"Email templates listed: {len(templates_response.templates)} templates",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "category": category.value if category else None,
                "is_active": is_active,
                "search": search
            }
        )

        return templates_response

    except Exception as e:
        logger.error(f"Template listing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list email templates"
        )


@router.put(
    "/templates/{template_id}",
    response_model=EmailTemplateResponse,
    status_code=status.HTTP_200_OK,
    summary="Update email template",
    description="Update email template with validation"
)
async def update_email_template(
    template_id: UUID,
    template_data: EmailTemplateUpdate,
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailTemplateResponse:
    """
    Update email template.

    Args:
        template_id: Template unique identifier
        template_data: Template update data
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        EmailTemplateResponse: Updated template information

    Raises:
        HTTPException: 404 if template not found
        HTTPException: 400 if update data is invalid
        HTTPException: 500 if update fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Update template
        template = await email_service.update_template(
            template_id=template_id,
            template_data=template_data
        )

        logger.info(
            f"Email template updated: {template.name}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return template

    except ValueError as e:
        logger.warning(f"Invalid template update data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid template data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Template update failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update email template"
        )


@router.delete(
    "/templates/{template_id}",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Delete email template",
    description="Delete email template (soft delete)"
)
async def delete_email_template(
    template_id: UUID,
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Delete email template (soft delete).

    Args:
        template_id: Template unique identifier
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        MessageResponse: Deletion confirmation

    Raises:
        HTTPException: 404 if template not found
        HTTPException: 500 if deletion fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Delete template (soft delete by setting is_active=False)
        success = await email_service.delete_template(template_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Email template {template_id} not found"
            )

        logger.info(
            f"Email template deleted: {template_id}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "template_id": str(template_id)
            }
        )

        return MessageResponse(
            message="Email template deleted successfully",
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Template deletion failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete email template"
        )


# ============================================================================
# EMAIL SENDING AND DELIVERY ENDPOINTS
# ============================================================================

@router.post(
    "/send",
    response_model=EmailSendResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send email",
    description="Send email with template or direct content"
)
async def send_email(
    email_request: EmailSendRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailSendResponse:
    """
    Send email with template or direct content.

    Args:
        email_request: Email sending request data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailSendResponse: Email sending confirmation with delivery ID

    Raises:
        HTTPException: 400 if email request is invalid
        HTTPException: 404 if template not found
        HTTPException: 500 if email sending fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send email
        send_response = await email_service.send_email(
            email_request=email_request,
            user_id=current_user.id
        )

        logger.info(
            f"Email sent: {email_request.recipient_email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "recipient": email_request.recipient_email,
                "delivery_id": str(send_response.id)
            }
        )

        return send_response

    except ValueError as e:
        logger.warning(f"Invalid email request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid email request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Email sending failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send email"
        )


@router.post(
    "/send/batch",
    response_model=EmailBatchSendResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send batch emails",
    description="Send emails to multiple recipients using template"
)
async def send_batch_emails(
    batch_request: EmailBatchSendRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailBatchSendResponse:
    """
    Send emails to multiple recipients using template.

    Args:
        batch_request: Batch email sending request data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailBatchSendResponse: Batch sending confirmation with statistics

    Raises:
        HTTPException: 400 if batch request is invalid
        HTTPException: 404 if template not found
        HTTPException: 500 if batch sending fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send batch emails
        batch_response = await email_service.send_batch_emails(
            batch_request=batch_request,
            user_id=current_user.id
        )

        logger.info(
            f"Batch emails sent: {batch_response.queued_count}/{batch_response.total_recipients}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "batch_id": batch_response.batch_id,
                "total_recipients": batch_response.total_recipients,
                "queued_count": batch_response.queued_count
            }
        )

        return batch_response

    except ValueError as e:
        logger.warning(f"Invalid batch request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid batch request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Batch email sending failed: {str(e)}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send batch emails"
        )


@router.get(
    "/deliveries/{delivery_id}",
    response_model=EmailDeliveryResponse,
    status_code=status.HTTP_200_OK,
    summary="Get email delivery status",
    description="Get email delivery status and tracking information"
)
async def get_email_delivery_status(
    delivery_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailDeliveryResponse:
    """
    Get email delivery status and tracking information.

    Args:
        delivery_id: Delivery unique identifier
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailDeliveryResponse: Delivery status and tracking information

    Raises:
        HTTPException: 404 if delivery not found
        HTTPException: 403 if access denied
        HTTPException: 500 if retrieval fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Get delivery status
        delivery = await email_service.get_delivery_status(delivery_id)

        if not delivery:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Email delivery {delivery_id} not found"
            )

        # Check access permissions (user can only see their own deliveries unless admin)
        if (delivery.user_id != current_user.id and
            current_user.role not in ['admin', 'super_admin']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to delivery information"
            )

        logger.info(
            f"Email delivery status retrieved: {delivery_id}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "delivery_id": str(delivery_id),
                "status": delivery.status
            }
        )

        return delivery

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delivery status retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve delivery status"
        )


@router.get(
    "/deliveries",
    response_model=EmailDeliveryListResponse,
    status_code=status.HTTP_200_OK,
    summary="List email deliveries",
    description="List email deliveries with filtering and pagination"
)
async def list_email_deliveries(
    status_filter: Optional[EmailDeliveryStatus] = Query(None, description="Filter by delivery status"),
    days_back: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    skip: int = Query(0, ge=0, description="Number of deliveries to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of deliveries to return"),
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailDeliveryListResponse:
    """
    List email deliveries with filtering and pagination.

    Args:
        status_filter: Optional status filter
        days_back: Number of days to look back
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailDeliveryListResponse: Paginated list of deliveries

    Raises:
        HTTPException: 500 if listing fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # List deliveries (users see only their own, admins see all)
        user_id = None if current_user.role in ['admin', 'super_admin'] else current_user.id

        deliveries_response = await email_service.list_deliveries(
            user_id=user_id,
            status_filter=status_filter,
            days_back=days_back,
            skip=skip,
            limit=limit
        )

        logger.info(
            f"Email deliveries listed: {len(deliveries_response.deliveries)} deliveries",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "status_filter": status_filter if status_filter else None,
                "days_back": days_back
            }
        )

        return deliveries_response

    except Exception as e:
        logger.error(f"Delivery listing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list email deliveries"
        )


# ============================================================================
# EMAIL PREFERENCE MANAGEMENT ENDPOINTS
# ============================================================================

@router.get(
    "/preferences",
    response_model=EmailPreferenceResponse,
    status_code=status.HTTP_200_OK,
    summary="Get email preferences",
    description="Get current user email notification preferences"
)
async def get_email_preferences(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailPreferenceResponse:
    """
    Get current user email notification preferences.

    Args:
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailPreferenceResponse: User email preferences

    Raises:
        HTTPException: 500 if retrieval fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Get user preferences
        preferences = await email_service.get_user_preferences(current_user.id)

        logger.info(
            f"Email preferences retrieved for user: {current_user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id
            }
        )

        return preferences

    except Exception as e:
        logger.error(f"Email preferences retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve email preferences"
        )


@router.put(
    "/preferences",
    response_model=EmailPreferenceResponse,
    status_code=status.HTTP_200_OK,
    summary="Update email preferences",
    description="Update user email notification preferences"
)
async def update_email_preferences(
    preferences_data: EmailPreferenceUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailPreferenceResponse:
    """
    Update user email notification preferences.

    Args:
        preferences_data: Email preference update data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailPreferenceResponse: Updated email preferences

    Raises:
        HTTPException: 400 if preference data is invalid
        HTTPException: 500 if update fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Update preferences
        preferences = await email_service.update_user_preferences(
            user_id=current_user.id,
            preferences_data=preferences_data
        )

        logger.info(
            f"Email preferences updated for user: {current_user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id
            }
        )

        return preferences

    except ValueError as e:
        logger.warning(f"Invalid preference data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid preference data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Email preferences update failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update email preferences"
        )


@router.post(
    "/preferences/opt-out",
    response_model=EmailOptOutResponse,
    status_code=status.HTTP_200_OK,
    summary="Opt out of emails",
    description="Opt out of email communications (GDPR compliant)"
)
async def opt_out_emails(
    opt_out_request: EmailOptOutRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailOptOutResponse:
    """
    Opt out of email communications (GDPR compliant).

    Args:
        opt_out_request: Opt-out request data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailOptOutResponse: Opt-out confirmation

    Raises:
        HTTPException: 400 if opt-out request is invalid
        HTTPException: 500 if opt-out fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Process opt-out
        opt_out_response = await email_service.opt_out_user(
            user_id=current_user.id,
            opt_out_request=opt_out_request
        )

        logger.info(
            f"User opted out of emails: {current_user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "reason": opt_out_request.reason
            }
        )

        return opt_out_response

    except ValueError as e:
        logger.warning(f"Invalid opt-out request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid opt-out request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Email opt-out failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process email opt-out"
        )


# ============================================================================
# EMAIL QUEUE MONITORING ENDPOINTS
# ============================================================================

@router.get(
    "/queue",
    response_model=EmailQueueListResponse,
    status_code=status.HTTP_200_OK,
    summary="List email queue items",
    description="List email queue items with filtering and pagination (admin only)"
)
async def list_email_queue(
    status_filter: Optional[EmailQueueStatus] = Query(None, description="Filter by queue status"),
    priority: Optional[int] = Query(None, ge=1, le=5, description="Filter by priority"),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of items to return"),
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailQueueListResponse:
    """
    List email queue items with filtering and pagination (admin only).

    Args:
        status_filter: Optional status filter
        priority: Optional priority filter
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        EmailQueueListResponse: Paginated list of queue items

    Raises:
        HTTPException: 500 if listing fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # List queue items
        queue_response = await email_service.list_queue_items(
            status_filter=status_filter,
            priority=priority,
            skip=skip,
            limit=limit
        )

        logger.info(
            f"Email queue listed: {len(queue_response.queue_items)} items",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "status_filter": status_filter if status_filter else None,
                "priority": priority
            }
        )

        return queue_response

    except Exception as e:
        logger.error(f"Email queue listing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list email queue"
        )


@router.get(
    "/queue/statistics",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get email queue statistics",
    description="Get email queue processing statistics (admin only)"
)
async def get_email_queue_statistics(
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Get email queue processing statistics (admin only).

    Args:
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        Dict[str, Any]: Queue statistics and metrics

    Raises:
        HTTPException: 500 if statistics retrieval fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Get queue statistics
        statistics = await email_service.get_queue_statistics()

        logger.info(
            f"Email queue statistics retrieved",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id
            }
        )

        return statistics

    except Exception as e:
        logger.error(f"Email queue statistics retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve email queue statistics"
        )


@router.post(
    "/queue/{queue_id}/cancel",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Cancel queued email",
    description="Cancel a queued email before processing (admin only)"
)
async def cancel_queued_email(
    queue_id: UUID,
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Cancel a queued email before processing (admin only).

    Args:
        queue_id: Queue item unique identifier
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        MessageResponse: Cancellation confirmation

    Raises:
        HTTPException: 404 if queue item not found
        HTTPException: 400 if item cannot be cancelled
        HTTPException: 500 if cancellation fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Cancel queued email
        success = await email_service.cancel_queued_email(queue_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Queue item {queue_id} not found or cannot be cancelled"
            )

        logger.info(
            f"Queued email cancelled: {queue_id}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "queue_id": str(queue_id)
            }
        )

        return MessageResponse(
            message="Queued email cancelled successfully",
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email cancellation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel queued email"
        )


# ============================================================================
# EMAIL NOTIFICATION WORKFLOW ENDPOINTS
# ============================================================================

@router.post(
    "/notifications/verification",
    response_model=EmailVerificationResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send verification email",
    description="Send email verification notification"
)
async def send_verification_email(
    verification_request: EmailVerificationRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailVerificationResponse:
    """
    Send email verification notification.

    Args:
        verification_request: Email verification request data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        EmailVerificationResponse: Verification email confirmation

    Raises:
        HTTPException: 400 if verification request is invalid
        HTTPException: 500 if verification email fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send verification email
        verification_response = await email_service.send_verification_email(
            verification_request=verification_request
        )

        logger.info(
            f"Verification email sent: {verification_request.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "target_user_id": verification_request.user_id,
                "email": verification_request.email
            }
        )

        return verification_response

    except ValueError as e:
        logger.warning(f"Invalid verification request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid verification request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Verification email failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email"
        )


@router.post(
    "/notifications/password-reset",
    response_model=PasswordResetEmailResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send password reset email",
    description="Send password reset notification"
)
async def send_password_reset_email(
    reset_request: PasswordResetEmailRequest,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> PasswordResetEmailResponse:
    """
    Send password reset notification.

    Args:
        reset_request: Password reset email request data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        PasswordResetEmailResponse: Password reset email confirmation

    Raises:
        HTTPException: 400 if reset request is invalid
        HTTPException: 500 if password reset email fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send password reset email
        reset_response = await email_service.send_password_reset_email(
            reset_request=reset_request
        )

        logger.info(
            f"Password reset email sent: {reset_request.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "target_user_id": reset_request.user_id,
                "email": reset_request.email
            }
        )

        return reset_response

    except ValueError as e:
        logger.warning(f"Invalid password reset request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid password reset request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Password reset email failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send password reset email"
        )


@router.post(
    "/notifications/security-alert",
    response_model=MessageResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send security alert email",
    description="Send security alert notification"
)
async def send_security_alert_email(
    alert_data: Dict[str, Any],
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Send security alert notification.

    Args:
        alert_data: Security alert data
        current_user: Current authenticated user
        db: Database session dependency

    Returns:
        MessageResponse: Security alert confirmation

    Raises:
        HTTPException: 400 if alert data is invalid
        HTTPException: 500 if security alert fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send security alert
        await email_service.send_security_alert(
            user_id=current_user.id,
            alert_data=alert_data
        )

        logger.info(
            f"Security alert sent: {current_user.email}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "alert_type": alert_data.get("alert_type", "unknown")
            }
        )

        return MessageResponse(
            message="Security alert sent successfully",
            success=True
        )

    except ValueError as e:
        logger.warning(f"Invalid security alert data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid security alert data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Security alert failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send security alert"
        )


@router.post(
    "/notifications/welcome",
    response_model=MessageResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Send welcome email",
    description="Send welcome notification for new users"
)
async def send_welcome_email(
    user_data: Dict[str, Any],
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> MessageResponse:
    """
    Send welcome notification for new users.

    Args:
        user_data: New user data for welcome email
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        MessageResponse: Welcome email confirmation

    Raises:
        HTTPException: 400 if user data is invalid
        HTTPException: 500 if welcome email fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Send welcome email
        await email_service.send_welcome_email(
            user_data=user_data
        )

        logger.info(
            f"Welcome email sent: {user_data.get('email', 'unknown')}",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "target_user_id": user_data.get("user_id"),
                "email": user_data.get("email")
            }
        )

        return MessageResponse(
            message="Welcome email sent successfully",
            success=True
        )

    except ValueError as e:
        logger.warning(f"Invalid welcome email data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid welcome email data: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Welcome email failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send welcome email"
        )


# ============================================================================
# EMAIL ANALYTICS AND MONITORING ENDPOINTS
# ============================================================================

@router.get(
    "/analytics",
    response_model=EmailAnalyticsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get email analytics",
    description="Get comprehensive email analytics and metrics (admin only)"
)
async def get_email_analytics(
    days_back: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> EmailAnalyticsResponse:
    """
    Get comprehensive email analytics and metrics (admin only).

    Args:
        days_back: Number of days to analyze
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        EmailAnalyticsResponse: Comprehensive email analytics

    Raises:
        HTTPException: 500 if analytics retrieval fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Get email analytics
        analytics = await email_service.get_email_analytics(days_back=days_back)

        logger.info(
            f"Email analytics retrieved for {days_back} days",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "days_back": days_back
            }
        )

        return analytics

    except Exception as e:
        logger.error(f"Email analytics retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve email analytics"
        )


@router.get(
    "/health",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Email service health check",
    description="Check email service health and connectivity"
)
async def email_service_health_check(
    current_user: UserResponse = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Check email service health and connectivity.

    Args:
        current_user: Current authenticated admin user
        db: Database session dependency

    Returns:
        Dict[str, Any]: Health check results

    Raises:
        HTTPException: 500 if health check fails
    """
    try:
        # Initialize email service
        email_service = EmailService(db)

        # Perform health check
        health_status = await email_service.health_check()

        logger.info(
            f"Email service health check completed",
            extra={
                "correlation_id": correlation_id.get(''),
                "user_id": current_user.id,
                "status": health_status.get("status", "unknown")
            }
        )

        return health_status

    except Exception as e:
        logger.error(f"Email service health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email service health check failed"
        )
