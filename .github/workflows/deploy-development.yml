# Culture Connect Backend - Development Deployment Workflow
# Automated deployment to development environment with Docker Compose

name: Deploy to Development

on:
  push:
    branches: [ develop ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  ENVIRONMENT: development

jobs:
  # =============================================================================
  # DEVELOPMENT DEPLOYMENT
  # =============================================================================
  
  deploy-development:
    name: Deploy to Development Environment
    runs-on: ubuntu-latest
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Generate version tag
      id: version
      run: |
        VERSION="dev-$(date +%Y%m%d)-${GITHUB_SHA::8}"
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Generated version: $VERSION"

    - name: Build and push development image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:dev-latest
        build-args: |
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
          VCS_REF=${{ github.sha }}
          VERSION=${{ steps.version.outputs.version }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Set up development environment
      run: |
        # Create development environment configuration
        mkdir -p /tmp/dev-deployment
        cp -r docker/development/* /tmp/dev-deployment/
        cp environments/development/.env.development /tmp/dev-deployment/.env
        
        # Update image version in docker-compose
        sed -i "s|culture-connect-api:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}|g" /tmp/dev-deployment/docker-compose.yml

    - name: Deploy to development environment
      run: |
        cd /tmp/dev-deployment
        
        # Pull latest images
        docker-compose pull
        
        # Stop existing containers
        docker-compose down --remove-orphans
        
        # Start services
        docker-compose up -d
        
        # Wait for services to be ready
        echo "Waiting for services to start..."
        sleep 30

    - name: Run database migrations
      run: |
        cd /tmp/dev-deployment
        
        # Run migrations
        docker-compose exec -T api alembic upgrade head
        
        # Verify migration status
        docker-compose exec -T api alembic current

    - name: Health check
      run: |
        cd /tmp/dev-deployment
        
        # Wait for application to be ready
        timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'
        
        echo "Development deployment health check passed!"

    - name: Run smoke tests
      run: |
        cd /tmp/dev-deployment
        
        # Basic API endpoints test
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost:8000/health/db || exit 1
        curl -f http://localhost:8000/health/redis || exit 1
        curl -f http://localhost:8000/docs || exit 1
        
        echo "Development smoke tests passed!"

    - name: Run development integration tests
      run: |
        cd /tmp/dev-deployment
        
        # Run integration tests against development environment
        docker-compose exec -T api python -m pytest tests/integration/ -v --tb=short
        
        echo "Development integration tests completed!"

    - name: Collect deployment logs
      if: always()
      run: |
        cd /tmp/dev-deployment
        
        # Collect logs from all services
        mkdir -p deployment-logs
        docker-compose logs api > deployment-logs/api.log
        docker-compose logs db > deployment-logs/db.log
        docker-compose logs redis > deployment-logs/redis.log
        docker-compose logs celery_worker > deployment-logs/celery_worker.log || true
        docker-compose logs celery_beat > deployment-logs/celery_beat.log || true

    - name: Upload deployment logs
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: development-deployment-logs
        path: /tmp/dev-deployment/deployment-logs/

    - name: Cleanup on failure
      if: failure()
      run: |
        cd /tmp/dev-deployment
        
        echo "Deployment failed, cleaning up..."
        docker-compose down --remove-orphans
        docker-compose logs

    - name: Notify deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ Development deployment successful!"
          echo "🚀 Application available at: http://localhost:8000"
          echo "📚 API Documentation: http://localhost:8000/docs"
          echo "🔍 Version deployed: ${{ steps.version.outputs.version }}"
        else
          echo "❌ Development deployment failed!"
          echo "📋 Check the logs for more details"
        fi

  # =============================================================================
  # POST-DEPLOYMENT VALIDATION
  # =============================================================================
  
  post-deployment-validation:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: deploy-development
    if: success()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install test dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests pytest pytest-asyncio

    - name: Run end-to-end tests
      run: |
        # Run comprehensive end-to-end tests
        python -m pytest tests/e2e/ -v --tb=short --base-url=http://localhost:8000
      env:
        TEST_ENVIRONMENT: development
        API_BASE_URL: http://localhost:8000

    - name: Performance baseline test
      run: |
        # Basic performance test
        echo "Running performance baseline test..."
        
        # Test API response times
        for endpoint in "/health" "/health/db" "/health/redis"; do
          echo "Testing $endpoint"
          time curl -f "http://localhost:8000$endpoint"
        done

    - name: Security validation
      run: |
        # Basic security checks
        echo "Running security validation..."
        
        # Check for secure headers
        curl -I http://localhost:8000/health | grep -i "x-frame-options\|x-content-type-options\|x-xss-protection" || echo "Security headers missing"
        
        # Check for exposed sensitive information
        curl -s http://localhost:8000/docs | grep -i "password\|secret\|key" && echo "Potential sensitive information exposed" || echo "No sensitive information found"

    - name: Generate deployment report
      run: |
        cat << EOF > development-deployment-report.md
        # Development Deployment Report
        
        **Deployment Date**: $(date -u)
        **Version**: ${{ needs.deploy-development.outputs.version }}
        **Commit**: ${{ github.sha }}
        **Branch**: ${{ github.ref }}
        
        ## Deployment Status
        - ✅ Container build and push: Success
        - ✅ Service deployment: Success
        - ✅ Database migrations: Success
        - ✅ Health checks: Success
        - ✅ Smoke tests: Success
        - ✅ Integration tests: Success
        - ✅ End-to-end tests: Success
        - ✅ Performance baseline: Success
        - ✅ Security validation: Success
        
        ## Service Information
        - **API Endpoint**: http://localhost:8000
        - **Documentation**: http://localhost:8000/docs
        - **Health Check**: http://localhost:8000/health
        
        ## Next Steps
        1. Monitor application performance and logs
        2. Run additional manual testing as needed
        3. Prepare for staging deployment when ready
        
        EOF

    - name: Upload deployment report
      uses: actions/upload-artifact@v3
      with:
        name: development-deployment-report
        path: development-deployment-report.md

    - name: Comment deployment status on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('development-deployment-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🚀 Development Deployment Report\n\n${report}`
          })
