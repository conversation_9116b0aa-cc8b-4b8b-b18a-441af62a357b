"""
Isolated unit tests for booking API endpoints.

This module provides completely isolated testing for the booking workflow API endpoints
with full mocking of all external dependencies including database, authentication,
and service layers.

Key Features:
- Complete isolation from database connections
- Full mocking of authentication and authorization
- Service layer mocking with proper async handling
- No external dependencies or real network calls
- Fast execution with reliable results

Production-grade isolated test implementation following established testing patterns.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import status
from fastapi.testclient import TestClient

from app.schemas.booking_schemas import BookingResponseSchema


class TestBookingEndpointsIsolated:
    """Isolated test class for booking API endpoints with complete mocking."""

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated customer user."""
        user = MagicMock()
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        user.is_active = True
        user.is_verified = True
        return user

    @pytest.fixture
    def mock_vendor_user(self):
        """Mock authenticated vendor user."""
        user = MagicMock()
        user.id = 2
        user.email = "<EMAIL>"
        user.role = "vendor"
        user.is_active = True
        user.is_verified = True
        return user

    @pytest.fixture
    def booking_create_data(self):
        """Sample booking creation data."""
        return {
            "service_id": 123,
            "booking_date": "2025-02-15",
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2,
            "special_requirements": "Vegetarian meal required",
            "customer_notes": "Celebrating anniversary",
            "priority": "normal",
            "booking_source": "web"
        }

    @pytest.fixture
    def mock_booking_response(self):
        """Mock booking response data."""
        return {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "booking_reference": "BK-2025-001234",
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 123,
            "booking_date": "2025-02-15",
            "booking_time": "14:30:00",
            "duration_hours": "2.5",
            "participant_count": 2,
            "status": "pending",
            "priority": "normal",
            "base_price": "100.00",
            "additional_fees": "10.00",
            "discount_amount": "0.00",
            "total_amount": "110.00",
            "currency": "USD",
            "payment_status": "pending",
            "completion_confirmed_by_customer": False,
            "completion_confirmed_by_vendor": False,
            "unread_messages_customer": 0,
            "unread_messages_vendor": 0,
            "refund_amount": "0.00",
            "booking_source": "web",
            "created_at": "2025-01-24T12:00:00Z",
            "updated_at": "2025-01-24T12:00:00Z"
        }

    @pytest.fixture
    def mock_booking_list_response(self):
        """Mock booking list response data."""
        return {
            "bookings": [
                {
                    "id": 1,
                    "booking_reference": "BK-2025-001234",
                    "status": "pending",
                    "total_amount": "110.00"
                }
            ],
            "total": 1,
            "page": 1,
            "per_page": 20,
            "pages": 1,
            "has_next": False,
            "has_prev": False
        }

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_create_booking_success(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user,
        booking_create_data,
        mock_booking_response
    ):
        """Test successful booking creation with complete isolation."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.create_booking.return_value = BookingResponseSchema(**mock_booking_response)

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.post(
            "/api/v1/bookings/",
            json=booking_create_data,
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["booking_reference"] == "BK-2025-001234"
        assert data["status"] == "pending"
        assert data["total_amount"] == "110.00"

        # Verify service was called
        mock_service_instance.create_booking.assert_called_once()

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_create_booking_validation_error(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user
    ):
        """Test booking creation with validation error."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service to raise validation error
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.create_booking.side_effect = ValueError("Service not found")

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request with invalid data
        invalid_data = {
            "service_id": 999,  # Non-existent service
            "booking_date": "2025-02-15"
        }

        response = client.post(
            "/api/v1/bookings/",
            json=invalid_data,
            headers={"Authorization": "Bearer test-token"}
        )

        # Should be validation error or internal server error
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY, 
            status.HTTP_500_INTERNAL_SERVER_ERROR
        ]

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_list_bookings_customer(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user,
        mock_booking_list_response
    ):
        """Test listing bookings for customer."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.get_customer_bookings.return_value = mock_booking_list_response

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/",
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total"] == 1
        assert len(data["bookings"]) == 1

        # Verify service was called
        mock_service_instance.get_customer_bookings.assert_called_once()

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_get_booking_details_success(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user
    ):
        """Test getting booking details successfully."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        
        # Mock booking with customer ownership
        mock_booking = MagicMock()
        mock_booking.customer_id = mock_user.id
        mock_booking.vendor_id = 2
        mock_service_instance.get_with_details.return_value = mock_booking

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/1",
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called
        mock_service_instance.get_with_details.assert_called_once_with(1)

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_get_booking_details_access_denied(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user
    ):
        """Test access denied for booking details."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        
        # Mock booking owned by different customer
        mock_booking = MagicMock()
        mock_booking.customer_id = 999  # Different customer
        mock_booking.vendor_id = 2
        mock_service_instance.get_with_details.return_value = mock_booking

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/1",
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @patch('app.core.deps.get_current_user')
    @patch('app.core.deps.get_db')
    @patch('app.services.booking_service.BookingService')
    def test_check_availability_success(
        self,
        mock_booking_service,
        mock_get_db,
        mock_get_current_user,
        mock_user
    ):
        """Test availability checking successfully."""
        # Setup mocks
        mock_get_current_user.return_value = mock_user
        mock_get_db.return_value = AsyncMock()
        
        # Mock service
        mock_service_instance = AsyncMock()
        mock_booking_service.return_value = mock_service_instance
        mock_service_instance.check_availability.return_value = True

        # Import and create test client after mocking
        from app.main import app
        client = TestClient(app)

        # Make request
        response = client.get(
            "/api/v1/bookings/availability/check",
            params={
                "service_id": 123,
                "booking_date": "2025-02-15",
                "booking_time": "14:30:00",
                "duration_hours": 2.5
            },
            headers={"Authorization": "Bearer test-token"}
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["available"] is True

        # Verify service was called
        mock_service_instance.check_availability.assert_called_once()

    def test_create_booking_unauthorized(self):
        """Test booking creation without authentication."""
        # Import and create test client
        from app.main import app
        client = TestClient(app)
        
        booking_data = {
            "service_id": 123,
            "booking_date": "2025-02-15"
        }

        response = client.post("/api/v1/bookings/", json=booking_data)
        
        # Should require authentication
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED, 
            status.HTTP_403_FORBIDDEN
        ]
