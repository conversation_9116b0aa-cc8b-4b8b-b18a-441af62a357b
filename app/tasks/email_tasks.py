"""
Email background tasks for Culture Connect Backend API.

This module provides comprehensive email task processing including:
- User verification emails
- Password reset emails
- Booking confirmation emails
- Notification digest emails
- Marketing campaign emails

Implements Task 6.2.1 requirements for email task processing with
production-grade reliability and performance.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from uuid import UUID

from app.core.celery_config import celery_app
from app.core.logging import correlation_id

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="email.send_verification_email", queue="email")
def send_verification_email(self, user_id: str, email: str, verification_token: str):
    """
    Send email verification email to user.

    Args:
        user_id: User identifier
        email: User email address
        verification_token: Email verification token
    """
    try:
        async def _send_email():
            from app.services.email_service import EmailService
            from app.db.session import get_async_session_context

            async with get_async_session_context() as session:
                email_service = EmailService(session)

                # Prepare email content
                from app.core.config import settings

                template_data = {
                    "user_id": user_id,
                    "verification_token": verification_token,
                    "verification_url": f"{settings.FRONTEND_URL}/verify-email?token={verification_token}",
                    "expires_at": (datetime.utcnow() + timedelta(hours=24)).isoformat()
                }

                # Send verification email
                result = await email_service.send_template_email(
                    to_email=email,
                    template_name="email_verification",
                    template_data=template_data,
                    priority="high"
                )

                logger.info(f"Verification email sent to {email}", extra={
                    "user_id": user_id,
                    "email": email,
                    "correlation_id": correlation_id.get()
                })

                return result

        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_email())
            return {
                "status": "success",
                "user_id": user_id,
                "email": email,
                "sent_at": datetime.utcnow().isoformat(),
                "message_id": result.get("message_id")
            }
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Failed to send verification email to {email}: {str(e)}", extra={
            "user_id": user_id,
            "email": email,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="email.send_password_reset", queue="email")
def send_password_reset_email(self, user_id: str, email: str, reset_token: str):
    """
    Send password reset email to user.

    Args:
        user_id: User identifier
        email: User email address
        reset_token: Password reset token
    """
    try:
        async def _send_email():
            from app.services.email_service import EmailService
            from app.db.session import get_async_session_context
            from app.core.config import settings

            async with get_async_session_context() as session:
                email_service = EmailService(session)

                # Prepare email content
                template_data = {
                    "user_id": user_id,
                    "reset_token": reset_token,
                    "reset_url": f"{settings.FRONTEND_URL}/reset-password?token={reset_token}",
                    "expires_at": (datetime.utcnow() + timedelta(hours=2)).isoformat()
                }

                # Send password reset email
                result = await email_service.send_template_email(
                    to_email=email,
                    template_name="password_reset",
                    template_data=template_data,
                    priority="high"
                )

                logger.info(f"Password reset email sent to {email}", extra={
                    "user_id": user_id,
                    "email": email,
                    "correlation_id": correlation_id.get()
                })

                return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_email())
            return {
                "status": "success",
                "user_id": user_id,
                "email": email,
                "sent_at": datetime.utcnow().isoformat(),
                "message_id": result.get("message_id")
            }
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Failed to send password reset email to {email}: {str(e)}", extra={
            "user_id": user_id,
            "email": email,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="email.send_booking_confirmation", queue="email")
def send_booking_confirmation_email(self, booking_id: str, user_email: str, vendor_email: str):
    """
    Send booking confirmation emails to user and vendor.

    Args:
        booking_id: Booking identifier
        user_email: User email address
        vendor_email: Vendor email address
    """
    try:
        async def _send_emails():
            from app.services.email_service import EmailService
            from app.services.booking_service import BookingService
            from app.db.session import get_async_session_context

            async with get_async_session_context() as session:
                email_service = EmailService(session)
                booking_service = BookingService(session)

                # Get booking details
                booking = await booking_service.get_booking_by_id(UUID(booking_id))
                if not booking:
                    raise ValueError(f"Booking not found: {booking_id}")

                # Prepare email data
                booking_data = {
                    "booking_id": booking_id,
                    "service_name": booking.service.name,
                    "vendor_name": booking.service.vendor.business_name,
                    "booking_date": booking.booking_date.isoformat(),
                    "total_amount": float(booking.total_amount),
                    "status": booking.status
                }

                results = []

                # Send confirmation to user
                user_result = await email_service.send_template_email(
                    to_email=user_email,
                    template_name="booking_confirmation_user",
                    template_data=booking_data,
                    priority="high"
                )
                results.append({"recipient": "user", "result": user_result})

                # Send notification to vendor
                vendor_result = await email_service.send_template_email(
                    to_email=vendor_email,
                    template_name="booking_confirmation_vendor",
                    template_data=booking_data,
                    priority="high"
                )
                results.append({"recipient": "vendor", "result": vendor_result})

                logger.info(f"Booking confirmation emails sent for booking {booking_id}", extra={
                    "booking_id": booking_id,
                    "user_email": user_email,
                    "vendor_email": vendor_email,
                    "correlation_id": correlation_id.get()
                })

                return results

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            results = loop.run_until_complete(_send_emails())
            return {
                "status": "success",
                "booking_id": booking_id,
                "sent_at": datetime.utcnow().isoformat(),
                "results": results
            }
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Failed to send booking confirmation emails for {booking_id}: {str(e)}", extra={
            "booking_id": booking_id,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=120, max_retries=3)


@celery_app.task(bind=True, name="email.send_notification_digest", queue="email")
def send_notification_digest(self, user_id: str, email: str, digest_type: str = "daily"):
    """
    Send notification digest email to user.

    Args:
        user_id: User identifier
        email: User email address
        digest_type: Type of digest (daily, weekly, monthly)
    """
    try:
        async def _send_digest():
            from app.services.email_service import EmailService
            from app.services.notification_service import NotificationService
            from app.db.session import get_async_session_context

            async with get_async_session_context() as session:
                email_service = EmailService(session)
                notification_service = NotificationService(session)

                # Get digest period
                if digest_type == "daily":
                    since = datetime.utcnow() - timedelta(days=1)
                elif digest_type == "weekly":
                    since = datetime.utcnow() - timedelta(weeks=1)
                elif digest_type == "monthly":
                    since = datetime.utcnow() - timedelta(days=30)
                else:
                    since = datetime.utcnow() - timedelta(days=1)

                # Get user notifications for digest
                notifications = await notification_service.get_user_notifications(
                    user_id=UUID(user_id),
                    since=since,
                    limit=50
                )

                # Prepare digest data
                digest_data = {
                    "user_id": user_id,
                    "digest_type": digest_type,
                    "period_start": since.isoformat(),
                    "period_end": datetime.utcnow().isoformat(),
                    "notification_count": len(notifications),
                    "notifications": [
                        {
                            "title": notif.title,
                            "message": notif.message,
                            "type": notif.notification_type,
                            "created_at": notif.created_at.isoformat()
                        }
                        for notif in notifications[:10]  # Top 10 notifications
                    ]
                }

                # Send digest email
                result = await email_service.send_template_email(
                    to_email=email,
                    template_name=f"notification_digest_{digest_type}",
                    template_data=digest_data,
                    priority="standard"
                )

                logger.info(f"{digest_type.title()} digest sent to {email}", extra={
                    "user_id": user_id,
                    "email": email,
                    "digest_type": digest_type,
                    "notification_count": len(notifications),
                    "correlation_id": correlation_id.get()
                })

                return result

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_digest())
            return {
                "status": "success",
                "user_id": user_id,
                "email": email,
                "digest_type": digest_type,
                "sent_at": datetime.utcnow().isoformat(),
                "message_id": result.get("message_id")
            }
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Failed to send {digest_type} digest to {email}: {str(e)}", extra={
            "user_id": user_id,
            "email": email,
            "digest_type": digest_type,
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="email.send_bulk_emails", queue="email")
def send_bulk_emails(self, email_batch: List[Dict[str, Any]], template_name: str):
    """
    Send bulk emails using batch processing.

    Args:
        email_batch: List of email data dictionaries
        template_name: Email template name
    """
    try:
        async def _send_bulk():
            from app.services.email_service import EmailService
            from app.db.session import get_async_session_context

            async with get_async_session_context() as session:
                email_service = EmailService(session)

                results = []
                successful = 0
                failed = 0

                for email_data in email_batch:
                    try:
                        result = await email_service.send_template_email(
                            to_email=email_data["email"],
                            template_name=template_name,
                            template_data=email_data.get("data", {}),
                            priority="standard"
                        )
                        results.append({
                            "email": email_data["email"],
                            "status": "success",
                            "message_id": result.get("message_id")
                        })
                        successful += 1

                    except Exception as e:
                        results.append({
                            "email": email_data["email"],
                            "status": "failed",
                            "error": str(e)
                        })
                        failed += 1

                logger.info(f"Bulk email batch completed: {successful} successful, {failed} failed", extra={
                    "template_name": template_name,
                    "batch_size": len(email_batch),
                    "successful": successful,
                    "failed": failed,
                    "correlation_id": correlation_id.get()
                })

                return {
                    "batch_size": len(email_batch),
                    "successful": successful,
                    "failed": failed,
                    "results": results
                }

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_send_bulk())
            return {
                "status": "completed",
                "template_name": template_name,
                "processed_at": datetime.utcnow().isoformat(),
                **result
            }
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"Failed to send bulk emails with template {template_name}: {str(e)}", extra={
            "template_name": template_name,
            "batch_size": len(email_batch),
            "correlation_id": correlation_id.get()
        })
        raise self.retry(exc=e, countdown=180, max_retries=2)
