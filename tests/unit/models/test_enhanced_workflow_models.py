"""
Unit tests for enhanced workflow models (Task 6.2.2 Phase 3).

Tests for advanced scheduling system models including:
- Enhanced JobSchedule model with advanced features
- New enum types (ScheduleType, NamedSchedule, HolidayCalendar)
- Database constraints and validation
- Performance optimization features
- Conflict resolution and priority handling
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.models.workflow_models import (
    JobSchedule, ScheduleType, NamedSchedule, HolidayCalendar,
    WorkflowDefinition, WorkflowStatus
)


class TestEnhancedJobScheduleModel:
    """Test enhanced JobSchedule model functionality."""

    def test_job_schedule_creation_with_cron(self, db_session: Session):
        """Test creating job schedule with cron expression."""
        schedule = JobSchedule(
            name="test-cron-schedule",
            description="Test cron schedule",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            priority=5
        )
        
        db_session.add(schedule)
        db_session.commit()
        
        assert schedule.id is not None
        assert schedule.name == "test-cron-schedule"
        assert schedule.cron_expression == "0 0 * * *"
        assert schedule.schedule_type == ScheduleType.CRON
        assert schedule.priority == 5
        assert schedule.is_active is True
        assert schedule.run_count == 0
        assert schedule.success_count == 0
        assert schedule.failure_count == 0

    def test_job_schedule_creation_with_named_schedule(self, db_session: Session):
        """Test creating job schedule with named schedule."""
        schedule = JobSchedule(
            name="test-named-schedule",
            description="Test named schedule",
            named_schedule=NamedSchedule.DAILY,
            timezone="America/New_York",
            schedule_type=ScheduleType.NAMED,
            business_days_only=True,
            exclude_holidays=True,
            holiday_calendar=HolidayCalendar.US
        )
        
        db_session.add(schedule)
        db_session.commit()
        
        assert schedule.named_schedule == NamedSchedule.DAILY
        assert schedule.schedule_type == ScheduleType.NAMED
        assert schedule.business_days_only is True
        assert schedule.exclude_holidays is True
        assert schedule.holiday_calendar == HolidayCalendar.US

    def test_job_schedule_creation_with_interval(self, db_session: Session):
        """Test creating job schedule with interval."""
        schedule = JobSchedule(
            name="test-interval-schedule",
            description="Test interval schedule",
            interval_seconds=3600,  # 1 hour
            timezone="UTC",
            schedule_type=ScheduleType.INTERVAL,
            allow_overlap=True,
            max_concurrent_runs=3
        )
        
        db_session.add(schedule)
        db_session.commit()
        
        assert schedule.interval_seconds == 3600
        assert schedule.schedule_type == ScheduleType.INTERVAL
        assert schedule.allow_overlap is True
        assert schedule.max_concurrent_runs == 3

    def test_job_schedule_with_workflow_definition(self, db_session: Session):
        """Test job schedule with workflow definition relationship."""
        # Create workflow definition
        workflow = WorkflowDefinition(
            name="test-workflow",
            version="1.0.0",
            status=WorkflowStatus.ACTIVE,
            created_by=uuid4()
        )
        db_session.add(workflow)
        db_session.flush()
        
        # Create schedule linked to workflow
        schedule = JobSchedule(
            workflow_definition_id=workflow.id,
            name="test-workflow-schedule",
            cron_expression="0 */6 * * *",  # Every 6 hours
            timezone="UTC",
            schedule_type=ScheduleType.CRON
        )
        
        db_session.add(schedule)
        db_session.commit()
        
        assert schedule.workflow_definition_id == workflow.id
        assert schedule.workflow_definition is not None
        assert schedule.workflow_definition.name == "test-workflow"

    def test_job_schedule_execution_tracking(self, db_session: Session):
        """Test job schedule execution tracking fields."""
        schedule = JobSchedule(
            name="test-tracking-schedule",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON
        )
        
        db_session.add(schedule)
        db_session.flush()
        
        # Update execution tracking
        now = datetime.now(timezone.utc)
        schedule.run_count = 5
        schedule.success_count = 4
        schedule.failure_count = 1
        schedule.last_run_at = now
        schedule.last_success_at = now - timedelta(hours=1)
        schedule.last_failure_at = now - timedelta(hours=2)
        schedule.average_duration_seconds = Decimal('125.5')
        schedule.last_duration_seconds = Decimal('130.2')
        
        db_session.commit()
        
        assert schedule.run_count == 5
        assert schedule.success_count == 4
        assert schedule.failure_count == 1
        assert schedule.last_run_at == now
        assert schedule.average_duration_seconds == Decimal('125.5')

    def test_job_schedule_missed_execution_handling(self, db_session: Session):
        """Test missed execution handling configuration."""
        schedule = JobSchedule(
            name="test-catchup-schedule",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            catchup_missed_runs=True,
            max_catchup_runs=5
        )
        
        db_session.add(schedule)
        db_session.commit()
        
        assert schedule.catchup_missed_runs is True
        assert schedule.max_catchup_runs == 5

    def test_job_schedule_unique_name_constraint(self, db_session: Session):
        """Test unique name constraint."""
        schedule1 = JobSchedule(
            name="duplicate-name",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON
        )
        
        schedule2 = JobSchedule(
            name="duplicate-name",
            cron_expression="0 12 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON
        )
        
        db_session.add(schedule1)
        db_session.commit()
        
        db_session.add(schedule2)
        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_job_schedule_priority_constraint(self, db_session: Session):
        """Test priority range constraint."""
        # Valid priority
        schedule1 = JobSchedule(
            name="valid-priority",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            priority=1
        )
        
        db_session.add(schedule1)
        db_session.commit()
        
        # Invalid priority (too low)
        schedule2 = JobSchedule(
            name="invalid-priority-low",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            priority=0
        )
        
        db_session.add(schedule2)
        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_job_schedule_max_concurrent_runs_constraint(self, db_session: Session):
        """Test max concurrent runs constraint."""
        # Valid max concurrent runs
        schedule1 = JobSchedule(
            name="valid-concurrent",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            max_concurrent_runs=1
        )
        
        db_session.add(schedule1)
        db_session.commit()
        
        # Invalid max concurrent runs (zero)
        schedule2 = JobSchedule(
            name="invalid-concurrent",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            max_concurrent_runs=0
        )
        
        db_session.add(schedule2)
        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_job_schedule_date_range_constraint(self, db_session: Session):
        """Test date range constraint."""
        now = datetime.now(timezone.utc)
        
        # Valid date range
        schedule1 = JobSchedule(
            name="valid-date-range",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            start_date=now,
            end_date=now + timedelta(days=30)
        )
        
        db_session.add(schedule1)
        db_session.commit()
        
        # Invalid date range (end before start)
        schedule2 = JobSchedule(
            name="invalid-date-range",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            start_date=now,
            end_date=now - timedelta(days=1)
        )
        
        db_session.add(schedule2)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestScheduleEnums:
    """Test schedule enumeration types."""

    def test_schedule_type_enum_values(self):
        """Test ScheduleType enum values."""
        assert ScheduleType.CRON == "cron"
        assert ScheduleType.INTERVAL == "interval"
        assert ScheduleType.NAMED == "named"
        assert ScheduleType.ONCE == "once"

    def test_named_schedule_enum_values(self):
        """Test NamedSchedule enum values."""
        assert NamedSchedule.YEARLY == "@yearly"
        assert NamedSchedule.ANNUALLY == "@annually"
        assert NamedSchedule.MONTHLY == "@monthly"
        assert NamedSchedule.WEEKLY == "@weekly"
        assert NamedSchedule.DAILY == "@daily"
        assert NamedSchedule.HOURLY == "@hourly"
        assert NamedSchedule.MINUTELY == "@minutely"
        assert NamedSchedule.REBOOT == "@reboot"

    def test_holiday_calendar_enum_values(self):
        """Test HolidayCalendar enum values."""
        assert HolidayCalendar.US == "US"
        assert HolidayCalendar.UK == "UK"
        assert HolidayCalendar.EU == "EU"
        assert HolidayCalendar.CA == "CA"
        assert HolidayCalendar.AU == "AU"
        assert HolidayCalendar.JP == "JP"
        assert HolidayCalendar.CUSTOM == "CUSTOM"


class TestJobScheduleIndexes:
    """Test job schedule database indexes for performance."""

    def test_schedule_indexes_exist(self, db_session: Session):
        """Test that performance indexes exist."""
        # This test would verify index existence in a real database
        # For unit tests, we verify the model structure supports indexing
        
        schedule = JobSchedule(
            name="index-test-schedule",
            cron_expression="0 0 * * *",
            timezone="UTC",
            schedule_type=ScheduleType.CRON,
            is_active=True,
            priority=5
        )
        
        # Verify fields that should be indexed are accessible
        assert hasattr(schedule, 'is_active')
        assert hasattr(schedule, 'next_run_at')
        assert hasattr(schedule, 'workflow_definition_id')
        assert hasattr(schedule, 'schedule_type')
        assert hasattr(schedule, 'timezone')
        assert hasattr(schedule, 'priority')
        assert hasattr(schedule, 'business_days_only')
        assert hasattr(schedule, 'exclude_holidays')
