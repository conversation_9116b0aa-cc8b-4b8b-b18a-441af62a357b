# Culture Connect Backend - Staging Environment Configuration
# Staging-specific configuration for pre-production testing and validation
#
# This file contains staging-specific configuration values that mirror production
# settings but with staging-appropriate values for testing and validation.
#
# Security Notice:
# - This file contains staging configuration values
# - Ensure proper file permissions (600) in staging environment
# - Use staging-specific secret management systems
# - Never use production secrets in staging environment

# =============================================================================
# ENVIRONMENT IDENTIFICATION
# =============================================================================

# Environment Configuration
ENVIRONMENT=staging
CC_ENVIRONMENT=staging
CC_VERSION=staging-latest
CC_BUILD_ID=staging-build-001

# Application Configuration
CC_DEBUG=false
CC_LOG_LEVEL=DEBUG
CC_WORKERS=2
CC_MAX_CONNECTIONS=500
CC_KEEPALIVE_TIMEOUT=65
CC_GRACEFUL_TIMEOUT=30

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Configuration
CC_DATABASE_URL=postgresql+asyncpg://culture_connect_staging:staging_password_here@db:5432/culture_connect_staging_db
CC_POSTGRES_DB=culture_connect_staging_db
CC_POSTGRES_USER=culture_connect_staging
CC_POSTGRES_PASSWORD=staging_password_here
CC_POSTGRES_PORT=5432

# Database Replication (Staging)
CC_POSTGRES_REPLICATION_USER=replicator_staging
CC_POSTGRES_REPLICATION_PASSWORD=replication_staging_password_here

# Database Performance (Staging Optimized)
CC_DATABASE_POOL_SIZE=10
CC_DATABASE_MAX_OVERFLOW=20
CC_DATABASE_POOL_TIMEOUT=30
CC_DATABASE_POOL_RECYCLE=3600

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Configuration
CC_REDIS_URL=redis://:staging_redis_password@redis:6379/0
CC_REDIS_PASSWORD=staging_redis_password
CC_REDIS_PORT=6379
CC_REDIS_DB=0

# Redis Performance (Staging)
CC_REDIS_MAX_CONNECTIONS=50
CC_REDIS_RETRY_ON_TIMEOUT=true
CC_REDIS_SOCKET_KEEPALIVE=true
CC_REDIS_SOCKET_KEEPALIVE_OPTIONS={}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
CC_SECRET_KEY=staging_secret_key_replace_with_secure_random_string_here
CC_JWT_SECRET_KEY=staging_jwt_secret_key_replace_with_secure_random_string_here
CC_JWT_ALGORITHM=HS256
CC_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
CC_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth Configuration (Staging)
CC_GOOGLE_CLIENT_ID=staging_google_client_id_here
CC_GOOGLE_CLIENT_SECRET=staging_google_client_secret_here
CC_FACEBOOK_APP_ID=staging_facebook_app_id_here
CC_FACEBOOK_APP_SECRET=staging_facebook_app_secret_here

# =============================================================================
# PAYMENT PROVIDER CONFIGURATION (STAGING)
# =============================================================================

# Paystack Configuration (Test Mode)
CC_PAYSTACK_SECRET_KEY=sk_test_staging_paystack_secret_key_here
CC_PAYSTACK_PUBLIC_KEY=pk_test_staging_paystack_public_key_here
CC_PAYSTACK_WEBHOOK_SECRET=staging_paystack_webhook_secret_here
CC_PAYSTACK_BASE_URL=https://api.paystack.co

# Stripe Configuration (Test Mode)
CC_STRIPE_SECRET_KEY=sk_test_staging_stripe_secret_key_here
CC_STRIPE_PUBLIC_KEY=pk_test_staging_stripe_public_key_here
CC_STRIPE_WEBHOOK_SECRET=whsec_staging_stripe_webhook_secret_here
CC_STRIPE_API_VERSION=2023-10-16

# Busha Cryptocurrency Configuration (Staging)
CC_BUSHA_API_KEY=staging_busha_api_key_here
CC_BUSHA_SECRET_KEY=staging_busha_secret_key_here
CC_BUSHA_BASE_URL=https://staging-api.busha.co
CC_BUSHA_WEBHOOK_SECRET=staging_busha_webhook_secret_here

# =============================================================================
# AI/ML INTEGRATION CONFIGURATION (STAGING)
# =============================================================================

# AIML API Configuration
CC_AIML_API_KEY=staging_aiml_api_key_here
CC_AIML_BASE_URL=https://api.aimlapi.com
CC_AIML_MODEL=gpt-4
CC_AIML_MAX_TOKENS=1000
CC_AIML_TEMPERATURE=0.7

# =============================================================================
# GEOLOCATION CONFIGURATION (STAGING)
# =============================================================================

# MaxMind GeoIP Configuration
CC_GEOIP_DATABASE_PATH=/app/data/GeoLite2-Country.mmdb
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_GEOLOCATION_FALLBACK_COUNTRY=NG
MAXMIND_LICENSE_KEY=staging_maxmind_license_key_here

# Geolocation Performance (Staging)
CC_GEOLOCATION_TIMEOUT_MS=5000
CC_GEOLOCATION_MAX_RETRIES=3
CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD=10
CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT=60

# Geolocation Caching (Staging)
CC_GEOLOCATION_CACHE_TTL_HOURS=12
CC_GEOLOCATION_CACHE_MAX_SIZE=5000
CC_GEOLOCATION_CACHE_COMPRESSION=true
CC_GEOLOCATION_REDIS_PREFIX=geo:staging:
CC_GEOLOCATION_REDIS_TTL=43200  # 12 hours

# =============================================================================
# EXTERNAL SERVICE CONFIGURATION
# =============================================================================

# Frontend URLs (Staging)
CC_FRONTEND_URL=https://staging.cultureconnect.ng
CC_PWA_URL=https://staging-app.cultureconnect.ng
CC_ADMIN_URL=https://staging-admin.cultureconnect.ng

# API Configuration
CC_API_BASE_URL=https://staging-api.cultureconnect.ng
CC_API_VERSION=v1
CC_API_TITLE=Culture Connect Backend API (Staging)
CC_API_DESCRIPTION=Culture Connect Backend API - Staging Environment

# =============================================================================
# MONITORING AND LOGGING (STAGING)
# =============================================================================

# Sentry Configuration (Staging)
CC_SENTRY_DSN=https://<EMAIL>/project_id
CC_SENTRY_ENVIRONMENT=staging
CC_SENTRY_TRACES_SAMPLE_RATE=1.0
CC_SENTRY_PROFILES_SAMPLE_RATE=1.0

# Logging Configuration
CC_LOG_FORMAT=json
CC_LOG_DATE_FORMAT=%Y-%m-%d %H:%M:%S
CC_LOG_FILE_PATH=/app/logs/culture_connect_staging.log
CC_LOG_MAX_BYTES=10485760  # 10MB
CC_LOG_BACKUP_COUNT=5

# =============================================================================
# EMAIL CONFIGURATION (STAGING)
# =============================================================================

# SMTP Configuration (Staging)
CC_SMTP_HOST=smtp.mailtrap.io
CC_SMTP_PORT=587
CC_SMTP_USERNAME=staging_smtp_username_here
CC_SMTP_PASSWORD=staging_smtp_password_here
CC_SMTP_USE_TLS=true
CC_SMTP_USE_SSL=false

# Email Settings
CC_EMAIL_FROM=<EMAIL>
CC_EMAIL_FROM_NAME=Culture Connect (Staging)
CC_EMAIL_TEMPLATE_DIR=/app/templates/email

# =============================================================================
# CELERY CONFIGURATION (STAGING)
# =============================================================================

# Celery Broker Configuration
CC_CELERY_BROKER_URL=redis://:staging_redis_password@redis:6379/1
CC_CELERY_RESULT_BACKEND=redis://:staging_redis_password@redis:6379/2

# Celery Worker Configuration
CELERY_WORKER_CONCURRENCY=2
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_WORKER_MAX_TASKS_PER_CHILD=500
CELERY_WORKER_DISABLE_RATE_LIMITS=false

# Celery Task Configuration
CC_CELERY_TASK_SERIALIZER=json
CC_CELERY_RESULT_SERIALIZER=json
CC_CELERY_ACCEPT_CONTENT=["json"]
CC_CELERY_TIMEZONE=UTC
CC_CELERY_ENABLE_UTC=true

# =============================================================================
# FEATURE FLAGS (STAGING)
# =============================================================================

# Feature Toggles
CC_ENABLE_PROMOTIONAL_SYSTEM=true
CC_ENABLE_CRYPTO_PAYMENTS=true
CC_ENABLE_AI_OPTIMIZATION=true
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_ENABLE_WEBSOCKET_COMMUNICATION=true
CC_ENABLE_BACKGROUND_JOBS=true
CC_ENABLE_ANALYTICS_SYSTEM=true
CC_ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# DEPLOYMENT CONFIGURATION (STAGING)
# =============================================================================

# Container Configuration
CC_DATA_PATH=/opt/culture-connect-staging/data
CC_LOG_PATH=/opt/culture-connect-staging/logs
CC_BACKUP_PATH=/opt/culture-connect-staging/backups
CC_UPLOAD_PATH=/opt/culture-connect-staging/uploads

# Resource Limits (Staging)
CC_MAX_MEMORY_MB=512
CC_MAX_CPU_PERCENT=50
CC_MAX_CONNECTIONS_TOTAL=500

# Health Check Configuration
CC_HEALTH_CHECK_INTERVAL=30
CC_HEALTH_CHECK_TIMEOUT=10
CC_HEALTH_CHECK_RETRIES=3

# =============================================================================
# SSL/TLS CONFIGURATION (STAGING)
# =============================================================================

# SSL Configuration
CC_SSL_ENABLED=true
CC_SSL_CERT_PATH=/etc/ssl/certs/staging.cultureconnect.ng.crt
CC_SSL_KEY_PATH=/etc/ssl/private/staging.cultureconnect.ng.key
CC_SSL_CA_PATH=/etc/ssl/certs/ca-certificates.crt

# Security Headers
CC_SECURITY_HEADERS_ENABLED=true
CC_HSTS_MAX_AGE=31536000
CC_CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# =============================================================================
# LOAD BALANCER CONFIGURATION (STAGING)
# =============================================================================

# Traefik Configuration
TRAEFIK_BASIC_AUTH=staging_user:staging_password_hash_here
CC_LOAD_BALANCER_STRATEGY=round_robin
CC_LOAD_BALANCER_HEALTH_CHECK_PATH=/health
CC_LOAD_BALANCER_HEALTH_CHECK_INTERVAL=30s

# Service Discovery
CC_SERVICE_DISCOVERY_ENABLED=true
CC_SERVICE_REGISTRY_URL=consul://consul:8500
CC_SERVICE_NAME=culture-connect-staging
CC_SERVICE_PORT=8000

# =============================================================================
# BACKUP CONFIGURATION (STAGING)
# =============================================================================

# Backup Settings
CC_BACKUP_ENABLED=true
CC_BACKUP_SCHEDULE=0 3 * * *  # Daily at 3 AM
CC_BACKUP_RETENTION_DAYS=7
CC_BACKUP_COMPRESSION=true
CC_BACKUP_ENCRYPTION_KEY=staging_backup_encryption_key_here

# =============================================================================
# TESTING CONFIGURATION (STAGING)
# =============================================================================

# Test Database
CC_TEST_DATABASE_URL=postgresql+asyncpg://culture_connect_test:test_password@db:5432/culture_connect_test_db

# Test Configuration
CC_TESTING_MODE=false
CC_TEST_EMAIL_BACKEND=console
CC_TEST_PAYMENT_MODE=true
CC_TEST_AI_RESPONSES=true
