"""
Service Media Service for Culture Connect Backend API.

This module provides comprehensive service media management functionality including:
- Image upload and optimization for service listings
- Media validation and processing
- Image resizing and thumbnail generation
- Media metadata management and storage
- Integration with service listing system

Implements Task 3.2.1 requirements for media management service with
production-grade image processing, validation, and storage integration.
"""

import logging
import os
import io
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from PIL import Image
import uuid

from app.core.logging import correlation_id
from app.services.base import BaseService, NotFoundError, ValidationError, ServiceError
from app.models.service import ServiceImage, Service
from app.repositories.service_repository import ServiceImageRepository, ServiceRepository
from app.schemas.service import (
    ServiceImageCreate, ServiceImageUpdate, ServiceImageResponse
)

logger = logging.getLogger(__name__)


class ServiceMediaService(BaseService[ServiceImage, ServiceImageRepository]):
    """
    Service media service for comprehensive media management operations.

    Provides business logic for:
    - Image upload and validation
    - Image optimization and resizing
    - Thumbnail generation
    - Media metadata management
    - Integration with service listing system
    """

    def __init__(self, db: AsyncSession):
        """Initialize service media service."""
        super().__init__(ServiceImageRepository, ServiceImage, db_session=db)
        self.service_repository = ServiceRepository(db)

        # Media configuration
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.allowed_formats = {'JPEG', 'PNG', 'WEBP'}
        self.thumbnail_sizes = {
            'small': (150, 150),
            'medium': (400, 300),
            'large': (800, 600)
        }
        self.upload_path = "uploads/services"

    async def upload_service_image(
        self,
        service_id: int,
        vendor_id: int,
        image_file: bytes,
        filename: str,
        content_type: str,
        alt_text: Optional[str] = None,
        is_primary: bool = False
    ) -> ServiceImageResponse:
        """
        Upload and process a service image.

        Args:
            service_id: ID of the service
            vendor_id: ID of the vendor (for ownership verification)
            image_file: Image file bytes
            filename: Original filename
            content_type: MIME content type
            alt_text: Optional alt text for accessibility
            is_primary: Whether this should be the primary image

        Returns:
            ServiceImageResponse: Uploaded image details

        Raises:
            NotFoundError: If service not found
            ValidationError: If image validation fails
            ConflictError: If vendor doesn't own the service
        """
        correlation = self.log_operation_start(
            "upload_service_image",
            service_id=service_id,
            vendor_id=vendor_id,
            filename=filename,
            is_primary=is_primary
        )

        try:
            # Verify service exists and ownership
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            if service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Validate image file
            await self._validate_image_file(image_file, filename, content_type)

            # Process and save image
            image_data = await self._process_image(image_file, filename)

            # Handle primary image logic
            if is_primary:
                await self._unset_existing_primary(service_id)

            # Create image record
            image_dict = {
                "service_id": service_id,
                "filename": image_data["filename"],
                "original_filename": filename,
                "file_path": image_data["file_path"],
                "file_size": image_data["file_size"],
                "width": image_data["width"],
                "height": image_data["height"],
                "format": image_data["format"],
                "alt_text": alt_text or f"Image for {service.title}",
                "is_primary": is_primary,
                "display_order": await self._get_next_display_order(service_id),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Add thumbnail URLs
            image_dict.update(image_data["thumbnails"])

            image = await self.repository.create(image_dict)

            response = ServiceImageResponse.model_validate(image)

            self.log_operation_success(
                correlation,
                f"Image uploaded successfully for service {service_id}: {filename}"
            )

            return response

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "upload_service_image",
                {"service_id": service_id, "vendor_id": vendor_id, "filename": filename}
            )

    async def update_service_image(
        self,
        image_id: int,
        vendor_id: int,
        image_data: ServiceImageUpdate
    ) -> ServiceImageResponse:
        """
        Update service image metadata.

        Args:
            image_id: ID of the image to update
            vendor_id: ID of the vendor (for ownership verification)
            image_data: Image update data

        Returns:
            ServiceImageResponse: Updated image details

        Raises:
            NotFoundError: If image not found
            ValidationError: If update data is invalid
        """
        correlation = self.log_operation_start(
            "update_service_image",
            image_id=image_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing image
            image = await self.repository.get_by_id(image_id)
            if not image:
                raise NotFoundError(f"Image with ID {image_id} not found")

            # Verify ownership through service
            service = await self.service_repository.get_by_id(image.service_id)
            if not service or service.vendor_id != vendor_id:
                raise ValidationError("Image does not belong to this vendor")

            # Handle primary image logic
            if hasattr(image_data, 'is_primary') and image_data.is_primary and not image.is_primary:
                await self._unset_existing_primary(image.service_id)

            # Update image
            update_dict = image_data.model_dump(exclude_unset=True)
            update_dict["updated_at"] = datetime.now(timezone.utc)

            updated_image = await self.repository.update(image_id, update_dict)

            response = ServiceImageResponse.model_validate(updated_image)

            self.log_operation_success(
                correlation,
                f"Image updated successfully: {image.filename} (ID: {image_id})"
            )

            return response

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "update_service_image",
                {"image_id": image_id, "vendor_id": vendor_id}
            )

    async def delete_service_image(
        self,
        image_id: int,
        vendor_id: int
    ) -> bool:
        """
        Delete a service image.

        Args:
            image_id: ID of the image to delete
            vendor_id: ID of the vendor (for ownership verification)

        Returns:
            bool: True if deletion successful

        Raises:
            NotFoundError: If image not found
            ValidationError: If vendor doesn't own the image
        """
        correlation = self.log_operation_start(
            "delete_service_image",
            image_id=image_id,
            vendor_id=vendor_id
        )

        try:
            # Get existing image
            image = await self.repository.get_by_id(image_id)
            if not image:
                raise NotFoundError(f"Image with ID {image_id} not found")

            # Verify ownership through service
            service = await self.service_repository.get_by_id(image.service_id)
            if not service or service.vendor_id != vendor_id:
                raise ValidationError("Image does not belong to this vendor")

            # Delete physical files
            await self._delete_image_files(image)

            # Delete database record
            await self.repository.delete(image_id)

            # If this was the primary image, set another image as primary
            if image.is_primary:
                await self._set_new_primary_image(image.service_id)

            self.log_operation_success(
                correlation,
                f"Image deleted successfully: {image.filename} (ID: {image_id})"
            )

            return True

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "delete_service_image",
                {"image_id": image_id, "vendor_id": vendor_id}
            )

    async def list_service_images(
        self,
        service_id: int,
        vendor_id: Optional[int] = None
    ) -> List[ServiceImageResponse]:
        """
        List all images for a service.

        Args:
            service_id: ID of the service
            vendor_id: Optional vendor ID for ownership verification

        Returns:
            List[ServiceImageResponse]: List of service images

        Raises:
            NotFoundError: If service not found
            ValidationError: If vendor doesn't own the service
        """
        correlation = self.log_operation_start(
            "list_service_images",
            service_id=service_id,
            vendor_id=vendor_id
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Verify ownership if vendor_id provided
            if vendor_id and service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Get images
            images = await self.repository.get_by_service(service_id)

            responses = [
                ServiceImageResponse.model_validate(image)
                for image in images
            ]

            self.log_operation_success(
                correlation,
                f"Listed {len(responses)} images for service {service_id}"
            )

            return responses

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "list_service_images",
                {"service_id": service_id, "vendor_id": vendor_id}
            )

    async def reorder_service_images(
        self,
        service_id: int,
        vendor_id: int,
        image_order: List[int]
    ) -> List[ServiceImageResponse]:
        """
        Reorder service images.

        Args:
            service_id: ID of the service
            vendor_id: ID of the vendor (for ownership verification)
            image_order: List of image IDs in desired order

        Returns:
            List[ServiceImageResponse]: Reordered images

        Raises:
            NotFoundError: If service not found
            ValidationError: If vendor doesn't own the service or invalid image IDs
        """
        correlation = self.log_operation_start(
            "reorder_service_images",
            service_id=service_id,
            vendor_id=vendor_id,
            image_count=len(image_order)
        )

        try:
            # Verify service exists and ownership
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            if service.vendor_id != vendor_id:
                raise ValidationError("Service does not belong to this vendor")

            # Get existing images
            existing_images = await self.repository.get_by_service(service_id)
            existing_ids = {img.id for img in existing_images}

            # Validate image IDs
            if set(image_order) != existing_ids:
                raise ValidationError("Image order must include all existing images")

            # Update display orders
            for order, image_id in enumerate(image_order, 1):
                await self.repository.update(image_id, {
                    "display_order": order,
                    "updated_at": datetime.now(timezone.utc)
                })

            # Get updated images
            updated_images = await self.repository.get_by_service(service_id)

            responses = [
                ServiceImageResponse.model_validate(image)
                for image in updated_images
            ]

            self.log_operation_success(
                correlation,
                f"Reordered {len(responses)} images for service {service_id}"
            )

            return responses

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "reorder_service_images",
                {"service_id": service_id, "vendor_id": vendor_id}
            )

    async def _validate_image_file(
        self,
        image_file: bytes,
        filename: str,
        content_type: str
    ) -> None:
        """Validate uploaded image file."""
        # Check file size
        if len(image_file) > self.max_file_size:
            raise ValidationError(f"File size exceeds maximum limit of {self.max_file_size // (1024*1024)}MB")

        # Check content type
        if not content_type.startswith('image/'):
            raise ValidationError("File must be an image")

        # Validate image format using PIL
        try:
            image = Image.open(io.BytesIO(image_file))
            if image.format not in self.allowed_formats:
                raise ValidationError(f"Image format {image.format} not supported. Allowed: {', '.join(self.allowed_formats)}")
        except Exception as e:
            raise ValidationError(f"Invalid image file: {str(e)}")

    async def _process_image(self, image_file: bytes, filename: str) -> Dict[str, Any]:
        """Process and save image with thumbnails."""
        import io

        # Generate unique filename
        file_extension = os.path.splitext(filename)[1].lower()
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Open image
        image = Image.open(io.BytesIO(image_file))

        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')

        # Create upload directory
        os.makedirs(self.upload_path, exist_ok=True)

        # Save original image
        file_path = os.path.join(self.upload_path, unique_filename)
        image.save(file_path, format='JPEG', quality=90, optimize=True)

        # Generate thumbnails
        thumbnails = {}
        for size_name, (width, height) in self.thumbnail_sizes.items():
            thumbnail = image.copy()
            thumbnail.thumbnail((width, height), Image.Resampling.LANCZOS)

            thumb_filename = f"{uuid.uuid4()}_thumb_{size_name}.jpg"
            thumb_path = os.path.join(self.upload_path, thumb_filename)
            thumbnail.save(thumb_path, format='JPEG', quality=85, optimize=True)

            thumbnails[f"thumbnail_{size_name}_url"] = thumb_path

        return {
            "filename": unique_filename,
            "file_path": file_path,
            "file_size": len(image_file),
            "width": image.width,
            "height": image.height,
            "format": image.format,
            "thumbnails": thumbnails
        }

    async def _unset_existing_primary(self, service_id: int) -> None:
        """Unset existing primary image for service."""
        existing_primary = await self.repository.get_primary_image(service_id)
        if existing_primary:
            await self.repository.update(existing_primary.id, {
                "is_primary": False,
                "updated_at": datetime.now(timezone.utc)
            })

    async def _set_new_primary_image(self, service_id: int) -> None:
        """Set a new primary image if none exists."""
        images = await self.repository.get_by_service(service_id)
        if images:
            # Set the first image as primary
            await self.repository.update(images[0].id, {
                "is_primary": True,
                "updated_at": datetime.now(timezone.utc)
            })

    async def _get_next_display_order(self, service_id: int) -> int:
        """Get the next display order for a service image."""
        images = await self.repository.get_by_service(service_id)
        if not images:
            return 1
        return max(img.display_order for img in images) + 1

    async def _delete_image_files(self, image: ServiceImage) -> None:
        """Delete physical image files."""
        try:
            # Delete main image
            if os.path.exists(image.file_path):
                os.remove(image.file_path)

            # Delete thumbnails
            for size_name in self.thumbnail_sizes.keys():
                thumb_attr = f"thumbnail_{size_name}_url"
                if hasattr(image, thumb_attr):
                    thumb_path = getattr(image, thumb_attr)
                    if thumb_path and os.path.exists(thumb_path):
                        os.remove(thumb_path)
        except Exception as e:
            logger.warning(f"Failed to delete image files: {str(e)}")
