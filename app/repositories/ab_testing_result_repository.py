"""
A/B Testing Result and Analysis Repository for Culture Connect Backend API.

This module provides repository layer for A/B testing results and statistical analysis including:
- ABTestResultRepository: Test result collection and performance tracking
- ABTestAnalysisRepository: Statistical analysis and significance validation

Implements Phase 2.3 A/B Testing Framework requirements with production-grade
PostgreSQL optimization, statistical significance validation, and seamless integration
with existing geolocation analytics and VPN detection systems.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import select, update, delete, insert
from decimal import Decimal
import uuid
import logging
import statistics
import math

from app.repositories.base import BaseRepository, RepositoryError
from app.models.ab_testing import (
    ABTest, ABTestAssignment, ABTestResult, ABTestAnalysis,
    RoutingStrategy, ABTestStatus, ABTestType, StatisticalSignificance
)
from app.models.payment import Payment, PaymentStatus

logger = logging.getLogger(__name__)

# Create exception classes for repository operations
class NotFoundError(RepositoryError):
    """Exception for resource not found errors."""
    pass

class ValidationError(RepositoryError):
    """Exception for validation errors."""
    pass


class ABTestResultRepository(BaseRepository[ABTestResult]):
    """
    Repository for A/B test result collection and performance tracking.
    
    Provides comprehensive test result management including result submission,
    performance metrics calculation, and conversion tracking with production-grade
    PostgreSQL optimization and <100ms result processing.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize A/B test result repository."""
        super().__init__(ABTestResult, db_session)

    async def create_result(self, result_data: Dict[str, Any]) -> ABTestResult:
        """
        Create a new A/B test result.

        Args:
            result_data: Test result data

        Returns:
            Created test result instance

        Performance Target: <50ms result creation
        """
        correlation = self.log_operation_start("create_result")

        try:
            # Validate assignment exists
            assignment_id = result_data.get('assignment_id')
            if assignment_id:
                assignment_query = select(ABTestAssignment).where(ABTestAssignment.id == assignment_id)
                assignment_result = await self.db_session.execute(assignment_query)
                assignment = assignment_result.scalar_one_or_none()
                
                if not assignment:
                    raise NotFoundError(f"Assignment not found: {assignment_id}")
                
                # Auto-populate test_id from assignment
                result_data['test_id'] = assignment.test_id

            # Set result timestamp if not provided
            if 'result_timestamp' not in result_data:
                result_data['result_timestamp'] = datetime.now(timezone.utc)

            # Create result instance
            result = ABTestResult(**result_data)
            self.db_session.add(result)
            await self.db_session.flush()
            await self.db_session.refresh(result)

            self.log_operation_success(correlation, f"Created A/B test result: {result.id}")
            return result

        except Exception as e:
            await self.handle_repository_error(e, "create_result", result_data)

    async def get_test_results(
        self,
        test_id: uuid.UUID,
        group: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[ABTestResult]:
        """
        Get results for a specific test with optional filtering.

        Args:
            test_id: Test identifier
            group: Optional group filter ('A' or 'B')
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Optional result limit

        Returns:
            List of test results

        Performance Target: <150ms result retrieval
        """
        correlation = self.log_operation_start("get_test_results")

        try:
            query = select(ABTestResult).where(ABTestResult.test_id == test_id)

            # Apply filters
            if group:
                query = query.join(ABTestAssignment).where(ABTestAssignment.assigned_group == group)

            if start_date:
                query = query.where(ABTestResult.result_timestamp >= start_date)

            if end_date:
                query = query.where(ABTestResult.result_timestamp <= end_date)

            query = query.order_by(desc(ABTestResult.result_timestamp))

            if limit:
                query = query.limit(limit)

            result = await self.db_session.execute(query)
            results = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(results)} test results")
            return results

        except Exception as e:
            await self.handle_repository_error(e, "get_test_results", {
                "test_id": str(test_id),
                "group": group,
                "start_date": start_date.isoformat() if start_date else None,
                "end_date": end_date.isoformat() if end_date else None
            })

    async def get_conversion_metrics(
        self,
        test_id: uuid.UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get conversion metrics for a test.

        Args:
            test_id: Test identifier
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            Conversion metrics by group

        Performance Target: <100ms metrics calculation
        """
        correlation = self.log_operation_start("get_conversion_metrics")

        try:
            # Base query with assignment join
            base_query = select(
                ABTestAssignment.assigned_group,
                func.count(ABTestResult.id).label('total_results'),
                func.sum(func.cast(ABTestResult.is_conversion, func.Integer())).label('conversions'),
                func.sum(ABTestResult.conversion_value).label('total_value'),
                func.avg(ABTestResult.processing_time_ms).label('avg_processing_time'),
                func.avg(ABTestResult.response_time_ms).label('avg_response_time')
            ).select_from(
                ABTestResult.__table__.join(ABTestAssignment.__table__)
            ).where(
                ABTestResult.test_id == test_id
            )

            # Apply date filters
            if start_date:
                base_query = base_query.where(ABTestResult.result_timestamp >= start_date)
            if end_date:
                base_query = base_query.where(ABTestResult.result_timestamp <= end_date)

            base_query = base_query.group_by(ABTestAssignment.assigned_group)

            result = await self.db_session.execute(base_query)
            metrics_data = result.all()

            # Calculate conversion rates and format metrics
            metrics = {}
            for row in metrics_data:
                group = row.assigned_group
                total_results = row.total_results or 0
                conversions = row.conversions or 0
                conversion_rate = (conversions / total_results * 100) if total_results > 0 else 0

                metrics[f"group_{group}"] = {
                    "total_results": total_results,
                    "conversions": conversions,
                    "conversion_rate": conversion_rate,
                    "total_value": float(row.total_value or 0),
                    "avg_processing_time_ms": float(row.avg_processing_time or 0),
                    "avg_response_time_ms": float(row.avg_response_time or 0)
                }

            self.log_operation_success(correlation, f"Calculated conversion metrics for test: {test_id}")
            return metrics

        except Exception as e:
            await self.handle_repository_error(e, "get_conversion_metrics", {
                "test_id": str(test_id),
                "start_date": start_date.isoformat() if start_date else None,
                "end_date": end_date.isoformat() if end_date else None
            })

    async def get_performance_comparison(
        self,
        test_id: uuid.UUID,
        metric: str = "conversion_rate"
    ) -> Dict[str, Any]:
        """
        Get performance comparison between test groups.

        Args:
            test_id: Test identifier
            metric: Metric to compare (conversion_rate, processing_time, etc.)

        Returns:
            Performance comparison data

        Performance Target: <150ms comparison calculation
        """
        correlation = self.log_operation_start("get_performance_comparison")

        try:
            # Get conversion metrics
            conversion_metrics = await self.get_conversion_metrics(test_id)

            if not conversion_metrics:
                return {}

            group_a_data = conversion_metrics.get("group_A", {})
            group_b_data = conversion_metrics.get("group_B", {})

            # Calculate comparison based on metric
            if metric == "conversion_rate":
                value_a = group_a_data.get("conversion_rate", 0)
                value_b = group_b_data.get("conversion_rate", 0)
            elif metric == "processing_time":
                value_a = group_a_data.get("avg_processing_time_ms", 0)
                value_b = group_b_data.get("avg_response_time_ms", 0)
            else:
                value_a = group_a_data.get(metric, 0)
                value_b = group_b_data.get(metric, 0)

            # Calculate improvement
            improvement = 0
            if value_a > 0:
                if metric == "processing_time":
                    # For processing time, lower is better
                    improvement = ((value_a - value_b) / value_a) * 100
                else:
                    # For conversion rate and revenue, higher is better
                    improvement = ((value_b - value_a) / value_a) * 100

            comparison = {
                "metric": metric,
                "group_a_value": value_a,
                "group_b_value": value_b,
                "improvement_percentage": improvement,
                "better_group": "B" if improvement > 0 else "A" if improvement < 0 else "tie",
                "sample_sizes": {
                    "group_a": group_a_data.get("total_results", 0),
                    "group_b": group_b_data.get("total_results", 0)
                }
            }

            self.log_operation_success(correlation, f"Calculated performance comparison for test: {test_id}")
            return comparison

        except Exception as e:
            await self.handle_repository_error(e, "get_performance_comparison", {
                "test_id": str(test_id),
                "metric": metric
            })

    async def get_results_by_provider(
        self,
        test_id: uuid.UUID,
        provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get results breakdown by payment provider.

        Args:
            test_id: Test identifier
            provider: Optional provider filter

        Returns:
            Results breakdown by provider

        Performance Target: <100ms provider analysis
        """
        correlation = self.log_operation_start("get_results_by_provider")

        try:
            query = select(
                ABTestResult.selected_provider,
                ABTestAssignment.assigned_group,
                func.count(ABTestResult.id).label('total_results'),
                func.sum(func.cast(ABTestResult.is_conversion, func.Integer())).label('conversions'),
                func.avg(ABTestResult.processing_time_ms).label('avg_processing_time')
            ).select_from(
                ABTestResult.__table__.join(ABTestAssignment.__table__)
            ).where(
                ABTestResult.test_id == test_id
            )

            if provider:
                query = query.where(ABTestResult.selected_provider == provider)

            query = query.group_by(
                ABTestResult.selected_provider,
                ABTestAssignment.assigned_group
            ).order_by(ABTestResult.selected_provider, ABTestAssignment.assigned_group)

            result = await self.db_session.execute(query)
            provider_data = result.all()

            # Format results
            provider_breakdown = {}
            for row in provider_data:
                provider_name = row.selected_provider or "unknown"
                group = row.assigned_group
                
                if provider_name not in provider_breakdown:
                    provider_breakdown[provider_name] = {}

                total_results = row.total_results or 0
                conversions = row.conversions or 0
                conversion_rate = (conversions / total_results * 100) if total_results > 0 else 0

                provider_breakdown[provider_name][f"group_{group}"] = {
                    "total_results": total_results,
                    "conversions": conversions,
                    "conversion_rate": conversion_rate,
                    "avg_processing_time_ms": float(row.avg_processing_time or 0)
                }

            self.log_operation_success(correlation, f"Generated provider breakdown for test: {test_id}")
            return provider_breakdown

        except Exception as e:
            await self.handle_repository_error(e, "get_results_by_provider", {
                "test_id": str(test_id),
                "provider": provider
            })


class ABTestAnalysisRepository(BaseRepository[ABTestAnalysis]):
    """
    Repository for A/B test statistical analysis and significance validation.
    
    Provides comprehensive statistical analysis including significance testing,
    confidence intervals, effect sizes, and recommendations with production-grade
    PostgreSQL optimization and <200ms analysis processing.
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize A/B test analysis repository."""
        super().__init__(ABTestAnalysis, db_session)

    async def create_analysis(self, analysis_data: Dict[str, Any]) -> ABTestAnalysis:
        """
        Create a new A/B test analysis.

        Args:
            analysis_data: Analysis data

        Returns:
            Created analysis instance

        Performance Target: <100ms analysis creation
        """
        correlation = self.log_operation_start("create_analysis")

        try:
            # Set analysis date if not provided
            if 'analysis_date' not in analysis_data:
                analysis_data['analysis_date'] = datetime.now(timezone.utc)

            # Create analysis instance
            analysis = ABTestAnalysis(**analysis_data)
            self.db_session.add(analysis)
            await self.db_session.flush()
            await self.db_session.refresh(analysis)

            self.log_operation_success(correlation, f"Created A/B test analysis: {analysis.id}")
            return analysis

        except Exception as e:
            await self.handle_repository_error(e, "create_analysis", analysis_data)

    async def get_latest_analysis(self, test_id: uuid.UUID) -> Optional[ABTestAnalysis]:
        """
        Get the latest analysis for a test.

        Args:
            test_id: Test identifier

        Returns:
            Latest analysis or None

        Performance Target: <50ms latest analysis retrieval
        """
        correlation = self.log_operation_start("get_latest_analysis")

        try:
            query = select(ABTestAnalysis).where(
                ABTestAnalysis.test_id == test_id
            ).order_by(desc(ABTestAnalysis.analysis_date)).limit(1)

            result = await self.db_session.execute(query)
            analysis = result.scalar_one_or_none()

            self.log_operation_success(correlation, f"Retrieved latest analysis for test: {test_id}")
            return analysis

        except Exception as e:
            await self.handle_repository_error(e, "get_latest_analysis", {
                "test_id": str(test_id)
            })

    async def get_analysis_history(
        self,
        test_id: uuid.UUID,
        limit: Optional[int] = 10
    ) -> List[ABTestAnalysis]:
        """
        Get analysis history for a test.

        Args:
            test_id: Test identifier
            limit: Optional result limit

        Returns:
            List of analyses ordered by date

        Performance Target: <100ms history retrieval
        """
        correlation = self.log_operation_start("get_analysis_history")

        try:
            query = select(ABTestAnalysis).where(
                ABTestAnalysis.test_id == test_id
            ).order_by(desc(ABTestAnalysis.analysis_date))

            if limit:
                query = query.limit(limit)

            result = await self.db_session.execute(query)
            analyses = result.scalars().all()

            self.log_operation_success(correlation, f"Retrieved {len(analyses)} analyses for test: {test_id}")
            return analyses

        except Exception as e:
            await self.handle_repository_error(e, "get_analysis_history", {
                "test_id": str(test_id),
                "limit": limit
            })

    async def calculate_statistical_significance(
        self,
        test_id: uuid.UUID,
        confidence_level: float = 0.95
    ) -> Dict[str, Any]:
        """
        Calculate statistical significance for a test.

        Args:
            test_id: Test identifier
            confidence_level: Confidence level (default: 0.95)

        Returns:
            Statistical significance results

        Performance Target: <200ms significance calculation
        """
        correlation = self.log_operation_start("calculate_statistical_significance")

        try:
            # Get conversion metrics from result repository
            result_repo = ABTestResultRepository(self.db_session)
            metrics = await result_repo.get_conversion_metrics(test_id)

            if not metrics or len(metrics) < 2:
                return {"error": "Insufficient data for statistical analysis"}

            group_a_data = metrics.get("group_A", {})
            group_b_data = metrics.get("group_B", {})

            # Extract data for statistical test
            n_a = group_a_data.get("total_results", 0)
            n_b = group_b_data.get("total_results", 0)
            x_a = group_a_data.get("conversions", 0)
            x_b = group_b_data.get("conversions", 0)

            if n_a == 0 or n_b == 0:
                return {"error": "No results available for one or both groups"}

            # Calculate proportions
            p_a = x_a / n_a
            p_b = x_b / n_b

            # Calculate pooled proportion
            p_pooled = (x_a + x_b) / (n_a + n_b)

            # Calculate standard error
            se = math.sqrt(p_pooled * (1 - p_pooled) * (1/n_a + 1/n_b))

            # Calculate z-score
            z_score = (p_b - p_a) / se if se > 0 else 0

            # Calculate p-value (two-tailed test)
            p_value = 2 * (1 - self._normal_cdf(abs(z_score)))

            # Determine significance level
            if p_value < 0.01:
                significance = StatisticalSignificance.HIGHLY_SIGNIFICANT
            elif p_value < 0.05:
                significance = StatisticalSignificance.SIGNIFICANT
            elif p_value < 0.1:
                significance = StatisticalSignificance.MARGINALLY_SIGNIFICANT
            else:
                significance = StatisticalSignificance.NOT_SIGNIFICANT

            # Calculate confidence interval
            alpha = 1 - confidence_level
            z_critical = self._inverse_normal_cdf(1 - alpha/2)
            margin_of_error = z_critical * se
            
            ci_lower = (p_b - p_a) - margin_of_error
            ci_upper = (p_b - p_a) + margin_of_error

            # Calculate effect size
            effect_size = p_b - p_a
            relative_effect_size = ((p_b - p_a) / p_a * 100) if p_a > 0 else 0

            results = {
                "sample_size_a": n_a,
                "sample_size_b": n_b,
                "conversion_rate_a": p_a * 100,
                "conversion_rate_b": p_b * 100,
                "effect_size": effect_size,
                "relative_effect_size": relative_effect_size,
                "z_score": z_score,
                "p_value": p_value,
                "confidence_level": confidence_level,
                "confidence_interval_lower": ci_lower,
                "confidence_interval_upper": ci_upper,
                "is_statistically_significant": p_value < (1 - confidence_level),
                "significance_level": significance,
                "statistical_power": self._calculate_power(n_a, n_b, effect_size, 0.05)
            }

            self.log_operation_success(correlation, f"Calculated statistical significance for test: {test_id}")
            return results

        except Exception as e:
            await self.handle_repository_error(e, "calculate_statistical_significance", {
                "test_id": str(test_id),
                "confidence_level": confidence_level
            })

    # Helper methods for statistical calculations
    def _normal_cdf(self, x: float) -> float:
        """Calculate normal cumulative distribution function."""
        return (1.0 + math.erf(x / math.sqrt(2.0))) / 2.0

    def _inverse_normal_cdf(self, p: float) -> float:
        """Calculate inverse normal cumulative distribution function (approximation)."""
        if p < 0.5:
            return -self._inverse_normal_cdf(1 - p)
        
        # Beasley-Springer-Moro algorithm approximation
        a = [0, -3.969683028665376e+01, 2.209460984245205e+02, -2.759285104469687e+02, 1.383577518672690e+02, -3.066479806614716e+01, 2.506628277459239e+00]
        b = [0, -5.447609879822406e+01, 1.615858368580409e+02, -1.556989798598866e+02, 6.680131188771972e+01, -1.328068155288572e+01]
        
        if p > 0.5:
            p = 1 - p
            
        t = math.sqrt(-2 * math.log(p))
        
        numerator = a[6]
        for i in range(5, -1, -1):
            numerator = numerator * t + a[i]
            
        denominator = b[5]
        for i in range(4, -1, -1):
            denominator = denominator * t + b[i]
            
        return t - numerator / denominator

    def _calculate_power(self, n_a: int, n_b: int, effect_size: float, alpha: float) -> float:
        """Calculate statistical power (simplified approximation)."""
        if n_a == 0 or n_b == 0 or effect_size == 0:
            return 0.0
        
        # Simplified power calculation
        pooled_n = (n_a + n_b) / 2
        z_alpha = self._inverse_normal_cdf(1 - alpha/2)
        z_beta = abs(effect_size) * math.sqrt(pooled_n / 2) - z_alpha
        
        return self._normal_cdf(z_beta)
