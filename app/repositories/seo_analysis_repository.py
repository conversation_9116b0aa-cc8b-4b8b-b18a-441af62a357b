"""
SEO Analysis Repository for Culture Connect Backend API.

This module provides data access layer for SEO analysis operations including:
- SEO score tracking and historical analysis
- Keyword analysis and content optimization queries
- Performance trending and comparative analysis
- Integration with service and vendor optimization workflows

Implements Task 3.2.2 requirements for marketplace optimization tools with
production-grade async operations and comprehensive query capabilities.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from app.models.marketplace_optimization import SEOAnalysis
from app.models.service import Service
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository, RepositoryError


logger = logging.getLogger(__name__)


class SEOAnalysisRepository(BaseRepository[SEOAnalysis]):
    """
    Repository for SEO analysis data access operations.
    
    Provides specialized methods for:
    - SEO score tracking and historical analysis
    - Keyword analysis and optimization queries
    - Performance trending and comparative analysis
    - Service and vendor SEO optimization workflows
    """

    def __init__(self, db: AsyncSession):
        """Initialize SEO analysis repository."""
        super().__init__(SEOAnalysis, db)

    async def get_latest_by_service(self, service_id: int) -> Optional[SEOAnalysis]:
        """
        Get the latest SEO analysis for a service.
        
        Args:
            service_id: Service ID to get analysis for
            
        Returns:
            Latest SEO analysis or None if not found
        """
        try:
            result = await self.db.execute(
                Select(SEOAnalysis)
                .where(SEOAnalysis.service_id == service_id)
                .order_by(desc(SEOAnalysis.analysis_date))
                .limit(1)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get latest SEO analysis for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get latest SEO analysis", e)

    async def get_latest_by_vendor(self, vendor_id: int) -> List[SEOAnalysis]:
        """
        Get latest SEO analyses for all services of a vendor.
        
        Args:
            vendor_id: Vendor ID to get analyses for
            
        Returns:
            List of latest SEO analyses for vendor's services
        """
        try:
            # Subquery to get latest analysis date for each service
            latest_subquery = (
                Select(
                    SEOAnalysis.service_id,
                    func.max(SEOAnalysis.analysis_date).label('latest_date')
                )
                .where(SEOAnalysis.vendor_id == vendor_id)
                .group_by(SEOAnalysis.service_id)
                .subquery()
            )
            
            # Main query to get latest analyses
            result = await self.db.execute(
                Select(SEOAnalysis)
                .join(
                    latest_subquery,
                    and_(
                        SEOAnalysis.service_id == latest_subquery.c.service_id,
                        SEOAnalysis.analysis_date == latest_subquery.c.latest_date
                    )
                )
                .options(
                    joinedload(SEOAnalysis.service),
                    joinedload(SEOAnalysis.vendor)
                )
                .order_by(desc(SEOAnalysis.overall_seo_score))
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get latest SEO analyses for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get vendor SEO analyses", e)

    async def get_historical_scores(
        self,
        service_id: int,
        days: int = 30
    ) -> List[Tuple[datetime, Decimal]]:
        """
        Get historical SEO scores for trending analysis.
        
        Args:
            service_id: Service ID to get history for
            days: Number of days to look back
            
        Returns:
            List of (date, score) tuples for trending
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            result = await self.db.execute(
                Select(SEOAnalysis.analysis_date, SEOAnalysis.overall_seo_score)
                .where(
                    and_(
                        SEOAnalysis.service_id == service_id,
                        SEOAnalysis.analysis_date >= cutoff_date
                    )
                )
                .order_by(asc(SEOAnalysis.analysis_date))
            )
            return result.all()
            
        except Exception as e:
            logger.error(f"Failed to get SEO score history for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get SEO score history", e)

    async def get_keyword_analysis(
        self,
        service_id: int,
        keyword: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get keyword analysis data for a service.
        
        Args:
            service_id: Service ID to analyze
            keyword: Optional specific keyword to analyze
            
        Returns:
            Keyword analysis data including density and recommendations
        """
        try:
            latest_analysis = await self.get_latest_by_service(service_id)
            if not latest_analysis:
                return {}
            
            analysis_data = {
                "primary_keywords": latest_analysis.primary_keywords,
                "keyword_density": latest_analysis.keyword_density,
                "missing_keywords": latest_analysis.missing_keywords,
                "keyword_score": latest_analysis.keyword_score
            }
            
            if keyword and keyword in latest_analysis.keyword_density:
                analysis_data["specific_keyword"] = {
                    "keyword": keyword,
                    "density": latest_analysis.keyword_density[keyword],
                    "is_primary": keyword in latest_analysis.primary_keywords,
                    "is_missing": keyword in latest_analysis.missing_keywords
                }
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"Failed to get keyword analysis for service {service_id}: {str(e)}")
            raise RepositoryError(f"Failed to get keyword analysis", e)

    async def get_score_distribution(
        self,
        vendor_id: Optional[int] = None,
        category_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get SEO score distribution for benchmarking.
        
        Args:
            vendor_id: Optional vendor filter
            category_id: Optional category filter
            
        Returns:
            Score distribution statistics
        """
        try:
            query = Select(
                func.avg(SEOAnalysis.overall_seo_score).label('avg_score'),
                func.min(SEOAnalysis.overall_seo_score).label('min_score'),
                func.max(SEOAnalysis.overall_seo_score).label('max_score'),
                func.percentile_cont(0.25).within_group(SEOAnalysis.overall_seo_score).label('q1'),
                func.percentile_cont(0.5).within_group(SEOAnalysis.overall_seo_score).label('median'),
                func.percentile_cont(0.75).within_group(SEOAnalysis.overall_seo_score).label('q3'),
                func.count(SEOAnalysis.id).label('total_analyses')
            )
            
            # Apply filters
            conditions = []
            if vendor_id:
                conditions.append(SEOAnalysis.vendor_id == vendor_id)
            if category_id:
                query = query.join(Service, SEOAnalysis.service_id == Service.id)
                conditions.append(Service.category_id == category_id)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            result = await self.db.execute(query)
            row = result.first()
            
            return {
                "average_score": float(row.avg_score) if row.avg_score else 0.0,
                "min_score": float(row.min_score) if row.min_score else 0.0,
                "max_score": float(row.max_score) if row.max_score else 0.0,
                "quartiles": {
                    "q1": float(row.q1) if row.q1 else 0.0,
                    "median": float(row.median) if row.median else 0.0,
                    "q3": float(row.q3) if row.q3 else 0.0
                },
                "total_analyses": row.total_analyses
            }
            
        except Exception as e:
            logger.error(f"Failed to get SEO score distribution: {str(e)}")
            raise RepositoryError(f"Failed to get score distribution", e)

    async def get_top_performing_services(
        self,
        limit: int = 10,
        category_id: Optional[int] = None
    ) -> List[SEOAnalysis]:
        """
        Get top performing services by SEO score.
        
        Args:
            limit: Number of top services to return
            category_id: Optional category filter
            
        Returns:
            List of top SEO analyses with service data
        """
        try:
            # Subquery for latest analysis per service
            latest_subquery = (
                Select(
                    SEOAnalysis.service_id,
                    func.max(SEOAnalysis.analysis_date).label('latest_date')
                )
                .group_by(SEOAnalysis.service_id)
                .subquery()
            )
            
            query = (
                Select(SEOAnalysis)
                .join(
                    latest_subquery,
                    and_(
                        SEOAnalysis.service_id == latest_subquery.c.service_id,
                        SEOAnalysis.analysis_date == latest_subquery.c.latest_date
                    )
                )
                .options(
                    joinedload(SEOAnalysis.service),
                    joinedload(SEOAnalysis.vendor)
                )
                .order_by(desc(SEOAnalysis.overall_seo_score))
                .limit(limit)
            )
            
            if category_id:
                query = query.join(Service, SEOAnalysis.service_id == Service.id)
                query = query.where(Service.category_id == category_id)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get top performing services: {str(e)}")
            raise RepositoryError(f"Failed to get top performing services", e)

    async def get_improvement_opportunities(
        self,
        vendor_id: int,
        threshold_score: float = 70.0
    ) -> List[SEOAnalysis]:
        """
        Get services with SEO improvement opportunities.
        
        Args:
            vendor_id: Vendor ID to analyze
            threshold_score: Score threshold below which services need improvement
            
        Returns:
            List of SEO analyses for services needing improvement
        """
        try:
            # Get latest analyses for vendor's services below threshold
            latest_subquery = (
                Select(
                    SEOAnalysis.service_id,
                    func.max(SEOAnalysis.analysis_date).label('latest_date')
                )
                .where(SEOAnalysis.vendor_id == vendor_id)
                .group_by(SEOAnalysis.service_id)
                .subquery()
            )
            
            result = await self.db.execute(
                Select(SEOAnalysis)
                .join(
                    latest_subquery,
                    and_(
                        SEOAnalysis.service_id == latest_subquery.c.service_id,
                        SEOAnalysis.analysis_date == latest_subquery.c.latest_date
                    )
                )
                .where(SEOAnalysis.overall_seo_score < threshold_score)
                .options(
                    joinedload(SEOAnalysis.service),
                    joinedload(SEOAnalysis.vendor)
                )
                .order_by(asc(SEOAnalysis.overall_seo_score))
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get improvement opportunities for vendor {vendor_id}: {str(e)}")
            raise RepositoryError(f"Failed to get improvement opportunities", e)
