"""
WebSocket Infrastructure repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for WebSocket infrastructure including:
- WebSocketConnectionRepository: Connection lifecycle management with Redis caching
- WebSocketEventRepository: Event querying and delivery tracking with performance optimization
- UserPresenceRepository: Real-time status management with <50ms response times
- WebSocketMetricsRepository: Analytics and aggregation with bulk operations support

Implements Task 6.1.1 Phase 2 requirements with production-grade PostgreSQL optimization,
Redis caching integration (>90% hit rate), async/await patterns, and seamless integration
with existing repository architecture following established BaseRepository patterns.

Performance targets: <200ms complex queries, <50ms simple lookups, >1000 records/second bulk operations.
"""

import asyncio
import time
import json
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, text, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base import BaseRepository, RepositoryError, QueryPerformanceMetrics
from app.repositories.enhanced_base import EnhancedBaseRepository
from app.repositories.bulk_operations import BulkOperationsMixin
from app.models.websocket_models import (
    WebSocketConnection, WebSocketEvent, UserPresence, WebSocketConnectionMetrics,
    WebSocketRoom, WebSocketRoomParticipant,
    ConnectionStatus, EventType, EventPriority
)
from app.core.cache import CacheManager, cache_result, CacheType
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

import logging

logger = logging.getLogger(__name__)


class WebSocketConnectionRepository(EnhancedBaseRepository[WebSocketConnection], BulkOperationsMixin):
    """
    Repository for WebSocket connection management with Redis caching.

    Provides connection lifecycle management, performance monitoring,
    and real-time connection tracking with <50ms response times.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketConnection, db)
        self.cache = cache_manager
        self._cache_ttl = 300  # 5 minutes for active connections
        self._metrics_cache_ttl = 3600  # 1 hour for metrics

    async def create_connection(self, connection_data: Dict[str, Any]) -> WebSocketConnection:
        """
        Create a new WebSocket connection with caching.

        Args:
            connection_data: Connection creation data

        Returns:
            Created WebSocket connection

        Raises:
            RepositoryError: If creation fails
        """
        start_time = time.time()

        try:
            # Create connection
            connection = await self.create(connection_data)

            # Cache active connection
            if self.cache:
                cache_key = f"ws_connection:{connection.connection_id}"
                await self.cache.set(cache_key, {
                    "id": connection.id,
                    "user_id": connection.user_id,
                    "status": connection.status,
                    "connected_at": connection.connected_at.isoformat(),
                    "last_activity_at": connection.last_activity_at.isoformat()
                }, self._cache_ttl)

                # Cache user's active connections
                user_connections_key = f"user_connections:{connection.user_id}"
                await self._update_user_connections_cache(connection.user_id)

            operation_time = time.time() - start_time
            logger.info(f"WebSocket connection created in {operation_time:.3f}s",
                       extra={"correlation_id": correlation_id.get()})

            return connection

        except Exception as e:
            logger.error(f"Failed to create WebSocket connection: {str(e)}")
            raise RepositoryError(f"Failed to create WebSocket connection: {str(e)}")

    async def get_by_connection_id(self, connection_id: str) -> Optional[WebSocketConnection]:
        """
        Get connection by connection ID with Redis caching.

        Args:
            connection_id: Unique connection identifier

        Returns:
            WebSocket connection if found, None otherwise
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"ws_connection:{connection_id}"
                cached_data = await self.cache.get(cache_key)

                if cached_data:
                    # Get full object from database using cached ID
                    connection = await self.get(cached_data["id"])
                    if connection:
                        operation_time = time.time() - start_time
                        logger.debug(f"Connection retrieved from cache in {operation_time:.3f}s")
                        return connection

            # Fallback to database query
            query = select(WebSocketConnection).where(
                WebSocketConnection.connection_id == connection_id
            )
            result = await self.db.execute(query)
            connection = result.scalar_one_or_none()

            # Cache the result if found
            if connection and self.cache:
                cache_key = f"ws_connection:{connection_id}"
                await self.cache.set(cache_key, {
                    "id": connection.id,
                    "user_id": connection.user_id,
                    "status": connection.status,
                    "connected_at": connection.connected_at.isoformat(),
                    "last_activity_at": connection.last_activity_at.isoformat()
                }, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Connection retrieved from database in {operation_time:.3f}s")

            return connection

        except Exception as e:
            logger.error(f"Failed to get connection by ID {connection_id}: {str(e)}")
            raise RepositoryError(f"Failed to get connection: {str(e)}")

    async def get_active_connections_by_user(self, user_id: int) -> List[WebSocketConnection]:
        """
        Get all active connections for a user with caching.

        Args:
            user_id: User ID

        Returns:
            List of active WebSocket connections
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"user_connections:{user_id}"
                cached_connections = await self.cache.get(cache_key)

                if cached_connections:
                    # Get full objects from database
                    connection_ids = [conn["id"] for conn in cached_connections]
                    if connection_ids:
                        query = select(WebSocketConnection).where(
                            WebSocketConnection.id.in_(connection_ids),
                            WebSocketConnection.status.in_([
                                ConnectionStatus.CONNECTED,
                                ConnectionStatus.AUTHENTICATED
                            ])
                        )
                        result = await self.db.execute(query)
                        connections = result.scalars().all()

                        operation_time = time.time() - start_time
                        logger.debug(f"User connections retrieved from cache in {operation_time:.3f}s")
                        return list(connections)

            # Fallback to database query
            query = select(WebSocketConnection).where(
                and_(
                    WebSocketConnection.user_id == user_id,
                    WebSocketConnection.status.in_([
                        ConnectionStatus.CONNECTED,
                        ConnectionStatus.AUTHENTICATED
                    ])
                )
            ).order_by(desc(WebSocketConnection.last_activity_at))

            result = await self.db.execute(query)
            connections = result.scalars().all()

            # Cache the results
            if self.cache:
                await self._update_user_connections_cache(user_id)

            operation_time = time.time() - start_time
            logger.debug(f"User connections retrieved from database in {operation_time:.3f}s")

            return list(connections)

        except Exception as e:
            logger.error(f"Failed to get active connections for user {user_id}: {str(e)}")
            raise RepositoryError(f"Failed to get user connections: {str(e)}")

    async def update_connection_status(
        self,
        connection_id: str,
        status: ConnectionStatus,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Update connection status with cache invalidation.

        Args:
            connection_id: Connection identifier
            status: New connection status
            error_message: Optional error message

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            # Get connection first
            connection = await self.get_by_connection_id(connection_id)
            if not connection:
                return False

            # Update status
            update_data = {
                "status": status,
                "last_activity_at": datetime.now(timezone.utc)
            }

            if status == ConnectionStatus.AUTHENTICATED:
                update_data["authenticated_at"] = datetime.now(timezone.utc)
            elif status == ConnectionStatus.DISCONNECTED:
                update_data["disconnected_at"] = datetime.now(timezone.utc)
            elif status == ConnectionStatus.ERROR and error_message:
                update_data["last_error"] = error_message
                update_data["last_error_at"] = datetime.now(timezone.utc)
                update_data["error_count"] = connection.error_count + 1

            # Update in database
            query = update(WebSocketConnection).where(
                WebSocketConnection.connection_id == connection_id
            ).values(**update_data)

            await self.db.execute(query)
            await self.db.commit()

            # Invalidate cache
            if self.cache:
                cache_key = f"ws_connection:{connection_id}"
                await self.cache.delete(cache_key)
                await self._update_user_connections_cache(connection.user_id)

            operation_time = time.time() - start_time
            logger.info(f"Connection status updated in {operation_time:.3f}s")

            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update connection status: {str(e)}")
            raise RepositoryError(f"Failed to update connection status: {str(e)}")

    async def cleanup_stale_connections(self, timeout_minutes: int = 30) -> int:
        """
        Clean up stale connections that haven't been active.

        Args:
            timeout_minutes: Minutes of inactivity before considering stale

        Returns:
            Number of connections cleaned up
        """
        start_time = time.time()

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=timeout_minutes)

            # Update stale connections to disconnected
            query = update(WebSocketConnection).where(
                and_(
                    WebSocketConnection.last_activity_at < cutoff_time,
                    WebSocketConnection.status.in_([
                        ConnectionStatus.CONNECTED,
                        ConnectionStatus.AUTHENTICATED
                    ])
                )
            ).values(
                status=ConnectionStatus.DISCONNECTED,
                disconnected_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            cleaned_count = result.rowcount

            # Clear relevant caches
            if self.cache and cleaned_count > 0:
                # This is a simplified cache invalidation
                # In production, you might want to track which users were affected
                await self.cache.delete_pattern("user_connections:*")
                await self.cache.delete_pattern("ws_connection:*")

            operation_time = time.time() - start_time
            logger.info(f"Cleaned up {cleaned_count} stale connections in {operation_time:.3f}s")

            return cleaned_count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to cleanup stale connections: {str(e)}")
            raise RepositoryError(f"Failed to cleanup stale connections: {str(e)}")

    async def _update_user_connections_cache(self, user_id: int) -> None:
        """Update user's active connections cache."""
        if not self.cache:
            return

        try:
            query = select(WebSocketConnection).where(
                and_(
                    WebSocketConnection.user_id == user_id,
                    WebSocketConnection.status.in_([
                        ConnectionStatus.CONNECTED,
                        ConnectionStatus.AUTHENTICATED
                    ])
                )
            ).order_by(desc(WebSocketConnection.last_activity_at))

            result = await self.db.execute(query)
            connections = result.scalars().all()

            cache_data = [
                {
                    "id": conn.id,
                    "connection_id": conn.connection_id,
                    "status": conn.status,
                    "connected_at": conn.connected_at.isoformat(),
                    "last_activity_at": conn.last_activity_at.isoformat()
                }
                for conn in connections
            ]

            cache_key = f"user_connections:{user_id}"
            await self.cache.set(cache_key, cache_data, self._cache_ttl)

        except Exception as e:
            logger.warning(f"Failed to update user connections cache: {str(e)}")

    # ============================================================================
    # Enhanced Real-time Communication Methods for Task 6.1.1 Phase 2
    # ============================================================================

    async def get_connections_in_room(self, room_id: int) -> List[WebSocketConnection]:
        """
        Get all active WebSocket connections for participants in a room.

        Performance Metrics:
        - Target response time: <150ms for room connection queries
        - Cache integration for frequently accessed rooms
        - Database query optimization with room participant joins

        Args:
            room_id: Room ID to get connections for

        Returns:
            List of active WebSocket connections in the room
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"room_connections:{room_id}"
                cached_connections = await self.cache.get(cache_key)
                if cached_connections:
                    operation_time = time.time() - start_time
                    logger.debug(f"Room connections retrieved from cache in {operation_time:.3f}s")
                    # Convert cached data back to models
                    connection_ids = [conn["id"] for conn in cached_connections]
                    return await self.get_many(connection_ids)

            # Query database with optimized join
            query = select(WebSocketConnection).join(
                WebSocketRoomParticipant,
                and_(
                    WebSocketConnection.id == WebSocketRoomParticipant.connection_id,
                    WebSocketRoomParticipant.room_id == room_id,
                    WebSocketRoomParticipant.is_active == True
                )
            ).where(
                WebSocketConnection.status.in_([ConnectionStatus.CONNECTED, ConnectionStatus.AUTHENTICATED])
            )

            result = await self.db.execute(query)
            connections = result.scalars().all()

            # Cache the results
            if self.cache and connections:
                cache_key = f"room_connections:{room_id}"
                cached_data = [{
                    "id": conn.id,
                    "connection_id": conn.connection_id,
                    "user_id": conn.user_id,
                    "status": conn.status
                } for conn in connections]
                await self.cache.set(cache_key, cached_data, 180)  # 3 minutes cache

            operation_time = time.time() - start_time
            logger.debug(f"Room connections retrieved in {operation_time:.3f}s", extra={
                "room_id": room_id,
                "connection_count": len(connections),
                "operation_time": operation_time
            })

            return list(connections)

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get room connections in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get room connections: {str(e)}")

    async def get_user_active_connections_with_rooms(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get user's active connections with associated room information.

        Performance Metrics:
        - Target response time: <100ms for user connection queries
        - Cache integration for frequently accessed users
        - Optimized query with room participant joins

        Args:
            user_id: User ID to get connections for

        Returns:
            List of connection dictionaries with room information
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"user_connections_rooms:{user_id}"
                cached_data = await self.cache.get(cache_key)
                if cached_data:
                    operation_time = time.time() - start_time
                    logger.debug(f"User connections with rooms retrieved from cache in {operation_time:.3f}s")
                    return cached_data

            # Query database with room information
            query = select(
                WebSocketConnection.id,
                WebSocketConnection.connection_id,
                WebSocketConnection.status,
                WebSocketConnection.connected_at,
                WebSocketConnection.last_activity_at,
                WebSocketRoomParticipant.room_id,
                WebSocketRoom.room_id.label("room_identifier"),
                WebSocketRoom.room_name,
                WebSocketRoom.room_type
            ).outerjoin(
                WebSocketRoomParticipant,
                and_(
                    WebSocketConnection.id == WebSocketRoomParticipant.connection_id,
                    WebSocketRoomParticipant.is_active == True
                )
            ).outerjoin(
                WebSocketRoom,
                WebSocketRoomParticipant.room_id == WebSocketRoom.id
            ).where(
                and_(
                    WebSocketConnection.user_id == user_id,
                    WebSocketConnection.status.in_([ConnectionStatus.CONNECTED, ConnectionStatus.AUTHENTICATED])
                )
            )

            result = await self.db.execute(query)
            rows = result.all()

            connections_data = []
            for row in rows:
                connection_data = {
                    "id": row.id,
                    "connection_id": row.connection_id,
                    "status": row.status,
                    "connected_at": row.connected_at.isoformat(),
                    "last_activity_at": row.last_activity_at.isoformat(),
                    "room": None
                }

                if row.room_id:
                    connection_data["room"] = {
                        "id": row.room_id,
                        "room_id": row.room_identifier,
                        "room_name": row.room_name,
                        "room_type": row.room_type
                    }

                connections_data.append(connection_data)

            # Cache the results
            if self.cache:
                cache_key = f"user_connections_rooms:{user_id}"
                await self.cache.set(cache_key, connections_data, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"User connections with rooms retrieved in {operation_time:.3f}s", extra={
                "user_id": user_id,
                "connection_count": len(connections_data),
                "operation_time": operation_time
            })

            return connections_data

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get user connections with rooms in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get user connections with rooms: {str(e)}")

    async def broadcast_to_room_connections(self, room_id: int, event_data: Dict[str, Any]) -> int:
        """
        Get connection IDs for broadcasting to all participants in a room.

        Performance Metrics:
        - Target response time: <100ms for broadcast preparation
        - Optimized query for connection ID retrieval
        - Cache integration for active room connections

        Args:
            room_id: Room ID to broadcast to
            event_data: Event data for broadcasting (for logging purposes)

        Returns:
            Number of connections that can receive the broadcast

        Note:
            This method prepares the connection list for broadcasting.
            Actual broadcasting is handled by the WebSocket service layer.
        """
        start_time = time.time()

        try:
            # Get active connections in the room
            connections = await self.get_connections_in_room(room_id)
            connection_count = len(connections)

            operation_time = time.time() - start_time
            logger.info(f"Room broadcast prepared in {operation_time:.3f}s", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "connection_count": connection_count,
                "event_type": event_data.get("type", "unknown"),
                "operation_time": operation_time
            })

            return connection_count

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to prepare room broadcast in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to prepare room broadcast: {str(e)}")

    async def get_connection_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get connection metrics for the specified time period.

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with connection metrics
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"connection_metrics:{hours}h"
                cached_metrics = await self.cache.get(cache_key)
                if cached_metrics:
                    return cached_metrics

            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            # Get metrics from database
            query = select(
                func.count(WebSocketConnection.id).label("total_connections"),
                func.count(
                    func.case(
                        (WebSocketConnection.status.in_([
                            ConnectionStatus.CONNECTED,
                            ConnectionStatus.AUTHENTICATED
                        ]), 1)
                    )
                ).label("active_connections"),
                func.count(
                    func.case(
                        (WebSocketConnection.status == ConnectionStatus.AUTHENTICATED, 1)
                    )
                ).label("authenticated_connections"),
                func.count(
                    func.case(
                        (WebSocketConnection.status == ConnectionStatus.ERROR, 1)
                    )
                ).label("error_connections"),
                func.avg(WebSocketConnection.message_count).label("avg_messages"),
                func.sum(WebSocketConnection.bytes_sent).label("total_bytes_sent"),
                func.sum(WebSocketConnection.bytes_received).label("total_bytes_received")
            ).where(WebSocketConnection.connected_at >= cutoff_time)

            result = await self.db.execute(query)
            row = result.first()

            metrics = {
                "total_connections": row.total_connections or 0,
                "active_connections": row.active_connections or 0,
                "authenticated_connections": row.authenticated_connections or 0,
                "error_connections": row.error_connections or 0,
                "avg_messages_per_connection": float(row.avg_messages or 0),
                "total_bytes_sent": row.total_bytes_sent or 0,
                "total_bytes_received": row.total_bytes_received or 0,
                "period_hours": hours,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            # Cache the metrics
            if self.cache:
                cache_key = f"connection_metrics:{hours}h"
                await self.cache.set(cache_key, metrics, self._metrics_cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Connection metrics calculated in {operation_time:.3f}s")

            return metrics

        except Exception as e:
            logger.error(f"Failed to get connection metrics: {str(e)}")
            raise RepositoryError(f"Failed to get connection metrics: {str(e)}")


class WebSocketEventRepository(EnhancedBaseRepository[WebSocketEvent], BulkOperationsMixin):
    """
    Repository for WebSocket event management with delivery tracking.

    Provides event querying, delivery tracking, retry logic,
    and performance optimization with <200ms query targets.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketEvent, db)
        self.cache = cache_manager
        self._cache_ttl = 600  # 10 minutes for events
        self._retry_cache_ttl = 1800  # 30 minutes for retry queue

    async def create_event(self, event_data: Dict[str, Any]) -> WebSocketEvent:
        """
        Create a new WebSocket event with automatic payload size calculation.

        Args:
            event_data: Event creation data

        Returns:
            Created WebSocket event
        """
        start_time = time.time()

        try:
            # Calculate payload size
            if "payload" in event_data:
                payload_str = json.dumps(event_data["payload"])
                event_data["payload_size"] = len(payload_str.encode('utf-8'))

            # Set default values
            event_data.setdefault("delivery_status", "pending")
            event_data.setdefault("retry_count", 0)
            event_data.setdefault("max_retries", 3)

            # Create event
            event = await self.create(event_data)

            # Cache pending events for retry processing
            if self.cache and event.delivery_status == "pending":
                cache_key = f"pending_events:{event.priority}"
                await self._add_to_pending_queue(event)

            operation_time = time.time() - start_time
            logger.info(f"WebSocket event created in {operation_time:.3f}s")

            return event

        except Exception as e:
            logger.error(f"Failed to create WebSocket event: {str(e)}")
            raise RepositoryError(f"Failed to create WebSocket event: {str(e)}")

    async def get_pending_events(
        self,
        priority: Optional[EventPriority] = None,
        limit: int = 100
    ) -> List[WebSocketEvent]:
        """
        Get pending events for delivery with priority ordering.

        Args:
            priority: Optional priority filter
            limit: Maximum number of events to return

        Returns:
            List of pending events ordered by priority and creation time
        """
        start_time = time.time()

        try:
            # Build query
            query = select(WebSocketEvent).where(
                WebSocketEvent.delivery_status == "pending"
            )

            if priority:
                query = query.where(WebSocketEvent.priority == priority)

            # Order by priority (critical first) and creation time
            priority_order = func.case(
                (WebSocketEvent.priority == EventPriority.CRITICAL, 1),
                (WebSocketEvent.priority == EventPriority.HIGH, 2),
                (WebSocketEvent.priority == EventPriority.NORMAL, 3),
                (WebSocketEvent.priority == EventPriority.LOW, 4),
                else_=5
            )

            query = query.order_by(
                priority_order,
                WebSocketEvent.created_at
            ).limit(limit)

            result = await self.db.execute(query)
            events = result.scalars().all()

            operation_time = time.time() - start_time
            logger.debug(f"Retrieved {len(events)} pending events in {operation_time:.3f}s")

            return list(events)

        except Exception as e:
            logger.error(f"Failed to get pending events: {str(e)}")
            raise RepositoryError(f"Failed to get pending events: {str(e)}")

    async def get_retry_events(self, limit: int = 50) -> List[WebSocketEvent]:
        """
        Get events that need to be retried.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of events ready for retry
        """
        start_time = time.time()

        try:
            current_time = datetime.now(timezone.utc)

            query = select(WebSocketEvent).where(
                and_(
                    WebSocketEvent.delivery_status == "pending",
                    WebSocketEvent.retry_count > 0,
                    WebSocketEvent.next_retry_at <= current_time,
                    WebSocketEvent.retry_count < WebSocketEvent.max_retries
                )
            ).order_by(WebSocketEvent.next_retry_at).limit(limit)

            result = await self.db.execute(query)
            events = result.scalars().all()

            operation_time = time.time() - start_time
            logger.debug(f"Retrieved {len(events)} retry events in {operation_time:.3f}s")

            return list(events)

        except Exception as e:
            logger.error(f"Failed to get retry events: {str(e)}")
            raise RepositoryError(f"Failed to get retry events: {str(e)}")

    async def mark_event_sent(self, event_id: str, processing_time_ms: Optional[float] = None) -> bool:
        """
        Mark event as sent with optional processing time.

        Args:
            event_id: Event identifier
            processing_time_ms: Processing time in milliseconds

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            update_data = {
                "delivery_status": "sent",
                "sent_at": datetime.now(timezone.utc)
            }

            if processing_time_ms is not None:
                update_data["processing_time_ms"] = processing_time_ms

            query = update(WebSocketEvent).where(
                WebSocketEvent.event_id == event_id
            ).values(**update_data)

            result = await self.db.execute(query)
            await self.db.commit()

            # Remove from pending cache
            if self.cache and result.rowcount > 0:
                await self._remove_from_pending_queue(event_id)

            operation_time = time.time() - start_time
            logger.debug(f"Event marked as sent in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to mark event as sent: {str(e)}")
            raise RepositoryError(f"Failed to mark event as sent: {str(e)}")

    async def mark_event_delivered(self, event_id: str) -> bool:
        """
        Mark event as delivered.

        Args:
            event_id: Event identifier

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            query = update(WebSocketEvent).where(
                WebSocketEvent.event_id == event_id
            ).values(
                delivery_status="delivered",
                delivered_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            operation_time = time.time() - start_time
            logger.debug(f"Event marked as delivered in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to mark event as delivered: {str(e)}")
            raise RepositoryError(f"Failed to mark event as delivered: {str(e)}")

    async def mark_event_acknowledged(self, event_id: str) -> bool:
        """
        Mark event as acknowledged by client.

        Args:
            event_id: Event identifier

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            query = update(WebSocketEvent).where(
                WebSocketEvent.event_id == event_id
            ).values(
                delivery_status="acknowledged",
                acknowledged_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            operation_time = time.time() - start_time
            logger.debug(f"Event marked as acknowledged in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to mark event as acknowledged: {str(e)}")
            raise RepositoryError(f"Failed to mark event as acknowledged: {str(e)}")

    async def mark_event_failed(self, event_id: str, error_message: str, error_code: Optional[str] = None) -> bool:
        """
        Mark event as failed with error details.

        Args:
            event_id: Event identifier
            error_message: Error description
            error_code: Optional error code

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            query = update(WebSocketEvent).where(
                WebSocketEvent.event_id == event_id
            ).values(
                delivery_status="failed",
                error_message=error_message,
                error_code=error_code
            )

            result = await self.db.execute(query)
            await self.db.commit()

            # Remove from pending cache
            if self.cache and result.rowcount > 0:
                await self._remove_from_pending_queue(event_id)

            operation_time = time.time() - start_time
            logger.debug(f"Event marked as failed in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to mark event as failed: {str(e)}")
            raise RepositoryError(f"Failed to mark event as failed: {str(e)}")

    async def increment_retry_count(self, event_id: str, next_retry_delay_seconds: int = 60) -> bool:
        """
        Increment retry count and set next retry time.

        Args:
            event_id: Event identifier
            next_retry_delay_seconds: Delay before next retry

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            next_retry_at = datetime.now(timezone.utc) + timedelta(seconds=next_retry_delay_seconds)

            query = update(WebSocketEvent).where(
                WebSocketEvent.event_id == event_id
            ).values(
                retry_count=WebSocketEvent.retry_count + 1,
                next_retry_at=next_retry_at
            )

            result = await self.db.execute(query)
            await self.db.commit()

            operation_time = time.time() - start_time
            logger.debug(f"Event retry count incremented in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to increment retry count: {str(e)}")
            raise RepositoryError(f"Failed to increment retry count: {str(e)}")

    async def get_events_by_connection(
        self,
        connection_id: int,
        limit: int = 100,
        offset: int = 0
    ) -> List[WebSocketEvent]:
        """
        Get events for a specific connection.

        Args:
            connection_id: WebSocket connection ID
            limit: Maximum number of events to return
            offset: Number of events to skip

        Returns:
            List of events for the connection
        """
        start_time = time.time()

        try:
            query = select(WebSocketEvent).where(
                WebSocketEvent.connection_id == connection_id
            ).order_by(desc(WebSocketEvent.created_at)).limit(limit).offset(offset)

            result = await self.db.execute(query)
            events = result.scalars().all()

            operation_time = time.time() - start_time
            logger.debug(f"Retrieved {len(events)} events for connection in {operation_time:.3f}s")

            return list(events)

        except Exception as e:
            logger.error(f"Failed to get events by connection: {str(e)}")
            raise RepositoryError(f"Failed to get events by connection: {str(e)}")

    async def cleanup_old_events(self, days: int = 7) -> int:
        """
        Clean up old events to prevent database bloat.

        Args:
            days: Number of days to keep events

        Returns:
            Number of events cleaned up
        """
        start_time = time.time()

        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

            # Delete old events that are delivered or failed
            query = delete(WebSocketEvent).where(
                and_(
                    WebSocketEvent.created_at < cutoff_date,
                    WebSocketEvent.delivery_status.in_(["delivered", "acknowledged", "failed"])
                )
            )

            result = await self.db.execute(query)
            await self.db.commit()

            cleaned_count = result.rowcount

            operation_time = time.time() - start_time
            logger.info(f"Cleaned up {cleaned_count} old events in {operation_time:.3f}s")

            return cleaned_count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to cleanup old events: {str(e)}")
            raise RepositoryError(f"Failed to cleanup old events: {str(e)}")

    async def _add_to_pending_queue(self, event: WebSocketEvent) -> None:
        """Add event to pending queue cache."""
        if not self.cache:
            return

        try:
            cache_key = f"pending_events:{event.priority}"
            # This is a simplified implementation
            # In production, you might use Redis lists or sorted sets
            await self.cache.set(f"event:{event.event_id}", {
                "id": event.id,
                "event_id": event.event_id,
                "priority": event.priority,
                "created_at": event.created_at.isoformat()
            }, self._retry_cache_ttl)

        except Exception as e:
            logger.warning(f"Failed to add event to pending queue: {str(e)}")

    async def _remove_from_pending_queue(self, event_id: str) -> None:
        """Remove event from pending queue cache."""
        if not self.cache:
            return

        try:
            await self.cache.delete(f"event:{event_id}")

        except Exception as e:
            logger.warning(f"Failed to remove event from pending queue: {str(e)}")


class UserPresenceRepository(EnhancedBaseRepository[UserPresence], BulkOperationsMixin):
    """
    Repository for user presence management with real-time status tracking.

    Provides <50ms response times for presence updates and status queries
    with Redis caching for optimal performance.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(UserPresence, db)
        self.cache = cache_manager
        self._cache_ttl = 60  # 1 minute for presence data
        self._online_users_ttl = 30  # 30 seconds for online users list

    async def get_or_create_presence(self, user_id: int) -> UserPresence:
        """
        Get or create user presence record.

        Args:
            user_id: User ID

        Returns:
            User presence record
        """
        start_time = time.time()

        try:
            # Try to get existing presence
            query = select(UserPresence).where(UserPresence.user_id == user_id)
            result = await self.db.execute(query)
            presence = result.scalar_one_or_none()

            if not presence:
                # Create new presence record
                presence_data = {
                    "user_id": user_id,
                    "status": "offline",
                    "active_connections_count": 0,
                    "auto_away_enabled": True
                }
                presence = await self.create(presence_data)

            operation_time = time.time() - start_time
            logger.debug(f"User presence retrieved/created in {operation_time:.3f}s")

            return presence

        except Exception as e:
            logger.error(f"Failed to get/create user presence: {str(e)}")
            raise RepositoryError(f"Failed to get/create user presence: {str(e)}")

    async def update_user_status(
        self,
        user_id: int,
        status: str,
        activity_type: Optional[str] = None
    ) -> bool:
        """
        Update user presence status with <50ms target.

        Args:
            user_id: User ID
            status: New presence status (online, away, offline)
            activity_type: Optional activity type

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            update_data = {
                "status": status,
                "last_seen_at": datetime.now(timezone.utc)
            }

            if activity_type:
                update_data["last_activity_type"] = activity_type

            query = update(UserPresence).where(
                UserPresence.user_id == user_id
            ).values(**update_data)

            result = await self.db.execute(query)
            await self.db.commit()

            # Update cache
            if self.cache and result.rowcount > 0:
                await self._update_presence_cache(user_id)
                await self._update_online_users_cache()

            operation_time = time.time() - start_time
            logger.debug(f"User status updated in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update user status: {str(e)}")
            raise RepositoryError(f"Failed to update user status: {str(e)}")

    async def increment_connections(self, user_id: int) -> bool:
        """
        Increment user's active connection count.

        Args:
            user_id: User ID

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            # Ensure presence record exists
            await self.get_or_create_presence(user_id)

            query = update(UserPresence).where(
                UserPresence.user_id == user_id
            ).values(
                active_connections_count=UserPresence.active_connections_count + 1,
                status="online",
                last_seen_at=datetime.now(timezone.utc),
                last_activity_type="connection_established"
            )

            result = await self.db.execute(query)
            await self.db.commit()

            # Update cache
            if self.cache and result.rowcount > 0:
                await self._update_presence_cache(user_id)
                await self._update_online_users_cache()

            operation_time = time.time() - start_time
            logger.debug(f"User connections incremented in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to increment connections: {str(e)}")
            raise RepositoryError(f"Failed to increment connections: {str(e)}")

    async def decrement_connections(self, user_id: int) -> bool:
        """
        Decrement user's active connection count.

        Args:
            user_id: User ID

        Returns:
            True if updated successfully
        """
        start_time = time.time()

        try:
            # Get current presence
            presence = await self.get_or_create_presence(user_id)
            new_count = max(0, presence.active_connections_count - 1)
            new_status = "offline" if new_count == 0 else presence.status

            query = update(UserPresence).where(
                UserPresence.user_id == user_id
            ).values(
                active_connections_count=new_count,
                status=new_status,
                last_seen_at=datetime.now(timezone.utc),
                last_activity_type="connection_closed"
            )

            result = await self.db.execute(query)
            await self.db.commit()

            # Update cache
            if self.cache and result.rowcount > 0:
                await self._update_presence_cache(user_id)
                await self._update_online_users_cache()

            operation_time = time.time() - start_time
            logger.debug(f"User connections decremented in {operation_time:.3f}s")

            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to decrement connections: {str(e)}")
            raise RepositoryError(f"Failed to decrement connections: {str(e)}")

    async def get_user_presence(self, user_id: int) -> Optional[UserPresence]:
        """
        Get user presence with caching for <50ms response.

        Args:
            user_id: User ID

        Returns:
            User presence if found
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"user_presence:{user_id}"
                cached_presence = await self.cache.get(cache_key)

                if cached_presence:
                    # Get full object from database using cached ID
                    presence = await self.get(cached_presence["id"])
                    if presence:
                        operation_time = time.time() - start_time
                        logger.debug(f"User presence retrieved from cache in {operation_time:.3f}s")
                        return presence

            # Fallback to database
            query = select(UserPresence).where(UserPresence.user_id == user_id)
            result = await self.db.execute(query)
            presence = result.scalar_one_or_none()

            # Cache the result
            if presence and self.cache:
                await self._update_presence_cache(user_id)

            operation_time = time.time() - start_time
            logger.debug(f"User presence retrieved from database in {operation_time:.3f}s")

            return presence

        except Exception as e:
            logger.error(f"Failed to get user presence: {str(e)}")
            raise RepositoryError(f"Failed to get user presence: {str(e)}")

    async def get_online_users(self, limit: int = 100) -> List[UserPresence]:
        """
        Get list of online users with caching.

        Args:
            limit: Maximum number of users to return

        Returns:
            List of online user presence records
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = "online_users"
                cached_users = await self.cache.get(cache_key)

                if cached_users:
                    # Get full objects from database
                    user_ids = [user["user_id"] for user in cached_users[:limit]]
                    if user_ids:
                        query = select(UserPresence).where(
                            and_(
                                UserPresence.user_id.in_(user_ids),
                                UserPresence.status == "online",
                                UserPresence.active_connections_count > 0
                            )
                        )
                        result = await self.db.execute(query)
                        users = result.scalars().all()

                        operation_time = time.time() - start_time
                        logger.debug(f"Online users retrieved from cache in {operation_time:.3f}s")
                        return list(users)

            # Fallback to database
            query = select(UserPresence).where(
                and_(
                    UserPresence.status == "online",
                    UserPresence.active_connections_count > 0
                )
            ).order_by(desc(UserPresence.last_seen_at)).limit(limit)

            result = await self.db.execute(query)
            users = result.scalars().all()

            # Cache the results
            if self.cache:
                await self._update_online_users_cache()

            operation_time = time.time() - start_time
            logger.debug(f"Online users retrieved from database in {operation_time:.3f}s")

            return list(users)

        except Exception as e:
            logger.error(f"Failed to get online users: {str(e)}")
            raise RepositoryError(f"Failed to get online users: {str(e)}")

    async def set_auto_away_users(self, minutes_inactive: int = 15) -> int:
        """
        Set users to away status if they've been inactive.

        Args:
            minutes_inactive: Minutes of inactivity before setting away

        Returns:
            Number of users set to away
        """
        start_time = time.time()

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=minutes_inactive)

            query = update(UserPresence).where(
                and_(
                    UserPresence.status == "online",
                    UserPresence.auto_away_enabled == True,
                    UserPresence.last_seen_at < cutoff_time,
                    UserPresence.active_connections_count > 0
                )
            ).values(
                status="away",
                last_activity_type="auto_away"
            )

            result = await self.db.execute(query)
            await self.db.commit()

            away_count = result.rowcount

            # Update cache if users were set to away
            if self.cache and away_count > 0:
                await self._update_online_users_cache()

            operation_time = time.time() - start_time
            logger.info(f"Set {away_count} users to away in {operation_time:.3f}s")

            return away_count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to set auto away users: {str(e)}")
            raise RepositoryError(f"Failed to set auto away users: {str(e)}")

    async def _update_presence_cache(self, user_id: int) -> None:
        """Update user presence cache."""
        if not self.cache:
            return

        try:
            query = select(UserPresence).where(UserPresence.user_id == user_id)
            result = await self.db.execute(query)
            presence = result.scalar_one_or_none()

            if presence:
                cache_key = f"user_presence:{user_id}"
                cache_data = {
                    "id": presence.id,
                    "user_id": presence.user_id,
                    "status": presence.status,
                    "active_connections_count": presence.active_connections_count,
                    "last_seen_at": presence.last_seen_at.isoformat()
                }
                await self.cache.set(cache_key, cache_data, self._cache_ttl)

        except Exception as e:
            logger.warning(f"Failed to update presence cache: {str(e)}")

    async def _update_online_users_cache(self) -> None:
        """Update online users cache."""
        if not self.cache:
            return

        try:
            query = select(UserPresence).where(
                and_(
                    UserPresence.status == "online",
                    UserPresence.active_connections_count > 0
                )
            ).order_by(desc(UserPresence.last_seen_at)).limit(200)

            result = await self.db.execute(query)
            users = result.scalars().all()

            cache_data = [
                {
                    "user_id": user.user_id,
                    "status": user.status,
                    "last_seen_at": user.last_seen_at.isoformat()
                }
                for user in users
            ]

            cache_key = "online_users"
            await self.cache.set(cache_key, cache_data, self._online_users_ttl)

        except Exception as e:
            logger.warning(f"Failed to update online users cache: {str(e)}")


class WebSocketMetricsRepository(EnhancedBaseRepository[WebSocketConnectionMetrics], BulkOperationsMixin):
    """
    Repository for WebSocket metrics and analytics with aggregation support.

    Provides performance analytics, metrics aggregation, and bulk operations
    with >1000 records/second processing capability.
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketConnectionMetrics, db)
        self.cache = cache_manager
        self._cache_ttl = 3600  # 1 hour for metrics
        self._daily_cache_ttl = 86400  # 24 hours for daily aggregates

    async def create_or_update_hourly_metrics(
        self,
        metric_date: datetime,
        metric_hour: int,
        metrics_data: Dict[str, Any]
    ) -> WebSocketConnectionMetrics:
        """
        Create or update hourly metrics record.

        Args:
            metric_date: Date for metrics
            metric_hour: Hour of the day (0-23)
            metrics_data: Metrics data to update

        Returns:
            Created or updated metrics record
        """
        start_time = time.time()

        try:
            # Try to get existing record
            query = select(WebSocketConnectionMetrics).where(
                and_(
                    func.date(WebSocketConnectionMetrics.metric_date) == metric_date.date(),
                    WebSocketConnectionMetrics.metric_hour == metric_hour
                )
            )
            result = await self.db.execute(query)
            metrics = result.scalar_one_or_none()

            if metrics:
                # Update existing record
                for key, value in metrics_data.items():
                    if hasattr(metrics, key):
                        setattr(metrics, key, value)
                await self.db.commit()
            else:
                # Create new record
                create_data = {
                    "metric_date": metric_date,
                    "metric_hour": metric_hour,
                    **metrics_data
                }
                metrics = await self.create(create_data)

            # Invalidate cache
            if self.cache:
                cache_key = f"hourly_metrics:{metric_date.date()}:{metric_hour}"
                await self.cache.delete(cache_key)

            operation_time = time.time() - start_time
            logger.debug(f"Hourly metrics created/updated in {operation_time:.3f}s")

            return metrics

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create/update hourly metrics: {str(e)}")
            raise RepositoryError(f"Failed to create/update hourly metrics: {str(e)}")

    async def get_hourly_metrics(
        self,
        date: datetime,
        hour: int
    ) -> Optional[WebSocketConnectionMetrics]:
        """
        Get hourly metrics with caching.

        Args:
            date: Date for metrics
            hour: Hour of the day (0-23)

        Returns:
            Hourly metrics if found
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"hourly_metrics:{date.date()}:{hour}"
                cached_metrics = await self.cache.get(cache_key)

                if cached_metrics:
                    metrics = await self.get(cached_metrics["id"])
                    if metrics:
                        operation_time = time.time() - start_time
                        logger.debug(f"Hourly metrics retrieved from cache in {operation_time:.3f}s")
                        return metrics

            # Fallback to database
            query = select(WebSocketConnectionMetrics).where(
                and_(
                    func.date(WebSocketConnectionMetrics.metric_date) == date.date(),
                    WebSocketConnectionMetrics.metric_hour == hour
                )
            )
            result = await self.db.execute(query)
            metrics = result.scalar_one_or_none()

            # Cache the result
            if metrics and self.cache:
                cache_key = f"hourly_metrics:{date.date()}:{hour}"
                cache_data = {
                    "id": metrics.id,
                    "metric_date": metrics.metric_date.isoformat(),
                    "metric_hour": metrics.metric_hour
                }
                await self.cache.set(cache_key, cache_data, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Hourly metrics retrieved from database in {operation_time:.3f}s")

            return metrics

        except Exception as e:
            logger.error(f"Failed to get hourly metrics: {str(e)}")
            raise RepositoryError(f"Failed to get hourly metrics: {str(e)}")

    async def get_daily_aggregates(self, date: datetime) -> Dict[str, Any]:
        """
        Get daily aggregated metrics with caching.

        Args:
            date: Date for aggregation

        Returns:
            Dictionary with daily aggregated metrics
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"daily_metrics:{date.date()}"
                cached_aggregates = await self.cache.get(cache_key)
                if cached_aggregates:
                    operation_time = time.time() - start_time
                    logger.debug(f"Daily aggregates retrieved from cache in {operation_time:.3f}s")
                    return cached_aggregates

            # Calculate aggregates from database
            query = select(
                func.sum(WebSocketConnectionMetrics.total_connections).label("total_connections"),
                func.max(WebSocketConnectionMetrics.active_connections).label("peak_active_connections"),
                func.sum(WebSocketConnectionMetrics.authenticated_connections).label("total_authenticated"),
                func.sum(WebSocketConnectionMetrics.failed_connections).label("total_failed"),
                func.avg(WebSocketConnectionMetrics.avg_connection_duration_seconds).label("avg_duration"),
                func.avg(WebSocketConnectionMetrics.avg_message_delivery_time_ms).label("avg_delivery_time"),
                func.sum(WebSocketConnectionMetrics.total_messages_sent).label("total_messages"),
                func.sum(WebSocketConnectionMetrics.total_bytes_transferred).label("total_bytes"),
                func.sum(WebSocketConnectionMetrics.total_errors).label("total_errors"),
                func.avg(WebSocketConnectionMetrics.error_rate_percent).label("avg_error_rate")
            ).where(
                func.date(WebSocketConnectionMetrics.metric_date) == date.date()
            )

            result = await self.db.execute(query)
            row = result.first()

            aggregates = {
                "date": date.date().isoformat(),
                "total_connections": row.total_connections or 0,
                "peak_active_connections": row.peak_active_connections or 0,
                "total_authenticated_connections": row.total_authenticated or 0,
                "total_failed_connections": row.total_failed or 0,
                "avg_connection_duration_seconds": float(row.avg_duration or 0),
                "avg_message_delivery_time_ms": float(row.avg_delivery_time or 0),
                "total_messages_sent": row.total_messages or 0,
                "total_bytes_transferred": row.total_bytes or 0,
                "total_errors": row.total_errors or 0,
                "avg_error_rate_percent": float(row.avg_error_rate or 0),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            # Cache the aggregates
            if self.cache:
                cache_key = f"daily_metrics:{date.date()}"
                await self.cache.set(cache_key, aggregates, self._daily_cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Daily aggregates calculated in {operation_time:.3f}s")

            return aggregates

        except Exception as e:
            logger.error(f"Failed to get daily aggregates: {str(e)}")
            raise RepositoryError(f"Failed to get daily aggregates: {str(e)}")

    async def get_metrics_range(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 1000
    ) -> List[WebSocketConnectionMetrics]:
        """
        Get metrics for a date range with performance optimization.

        Args:
            start_date: Start date for range
            end_date: End date for range
            limit: Maximum number of records to return

        Returns:
            List of metrics records
        """
        start_time = time.time()

        try:
            query = select(WebSocketConnectionMetrics).where(
                and_(
                    WebSocketConnectionMetrics.metric_date >= start_date,
                    WebSocketConnectionMetrics.metric_date <= end_date
                )
            ).order_by(
                WebSocketConnectionMetrics.metric_date,
                WebSocketConnectionMetrics.metric_hour
            ).limit(limit)

            result = await self.db.execute(query)
            metrics = result.scalars().all()

            operation_time = time.time() - start_time
            logger.debug(f"Retrieved {len(metrics)} metrics records in {operation_time:.3f}s")

            return list(metrics)

        except Exception as e:
            logger.error(f"Failed to get metrics range: {str(e)}")
            raise RepositoryError(f"Failed to get metrics range: {str(e)}")

    async def bulk_create_metrics(self, metrics_data: List[Dict[str, Any]]) -> int:
        """
        Bulk create metrics records for high-throughput scenarios.

        Args:
            metrics_data: List of metrics data dictionaries

        Returns:
            Number of records created
        """
        start_time = time.time()

        try:
            if not metrics_data:
                return 0

            # Use bulk operations for performance
            created_count = await self.bulk_create(metrics_data)

            # Invalidate relevant caches
            if self.cache and created_count > 0:
                # Invalidate hourly and daily caches for affected dates
                affected_dates = set()
                for data in metrics_data:
                    if "metric_date" in data:
                        affected_dates.add(data["metric_date"].date())

                for date in affected_dates:
                    await self.cache.delete_pattern(f"hourly_metrics:{date}:*")
                    await self.cache.delete(f"daily_metrics:{date}")

            operation_time = time.time() - start_time
            logger.info(f"Bulk created {created_count} metrics records in {operation_time:.3f}s")

            return created_count

        except Exception as e:
            logger.error(f"Failed to bulk create metrics: {str(e)}")
            raise RepositoryError(f"Failed to bulk create metrics: {str(e)}")

    async def cleanup_old_metrics(self, days: int = 90) -> int:
        """
        Clean up old metrics records to prevent database bloat.

        Args:
            days: Number of days to keep metrics

        Returns:
            Number of records cleaned up
        """
        start_time = time.time()

        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

            query = delete(WebSocketConnectionMetrics).where(
                WebSocketConnectionMetrics.metric_date < cutoff_date
            )

            result = await self.db.execute(query)
            await self.db.commit()

            cleaned_count = result.rowcount

            # Clear relevant caches
            if self.cache and cleaned_count > 0:
                await self.cache.delete_pattern("hourly_metrics:*")
                await self.cache.delete_pattern("daily_metrics:*")

            operation_time = time.time() - start_time
            logger.info(f"Cleaned up {cleaned_count} old metrics records in {operation_time:.3f}s")

            return cleaned_count

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to cleanup old metrics: {str(e)}")
            raise RepositoryError(f"Failed to cleanup old metrics: {str(e)}")

    async def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.

        Args:
            hours: Number of hours to analyze

        Returns:
            Dictionary with performance summary
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"performance_summary:{hours}h"
                cached_summary = await self.cache.get(cache_key)
                if cached_summary:
                    return cached_summary

            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            query = select(
                func.avg(WebSocketConnectionMetrics.avg_message_delivery_time_ms).label("avg_delivery_time"),
                func.min(WebSocketConnectionMetrics.avg_message_delivery_time_ms).label("min_delivery_time"),
                func.max(WebSocketConnectionMetrics.avg_message_delivery_time_ms).label("max_delivery_time"),
                func.avg(WebSocketConnectionMetrics.error_rate_percent).label("avg_error_rate"),
                func.max(WebSocketConnectionMetrics.active_connections).label("peak_connections"),
                func.sum(WebSocketConnectionMetrics.total_messages_sent).label("total_messages"),
                func.sum(WebSocketConnectionMetrics.total_bytes_transferred).label("total_bytes")
            ).where(WebSocketConnectionMetrics.metric_date >= cutoff_time)

            result = await self.db.execute(query)
            row = result.first()

            summary = {
                "period_hours": hours,
                "avg_message_delivery_time_ms": float(row.avg_delivery_time or 0),
                "min_message_delivery_time_ms": float(row.min_delivery_time or 0),
                "max_message_delivery_time_ms": float(row.max_delivery_time or 0),
                "avg_error_rate_percent": float(row.avg_error_rate or 0),
                "peak_concurrent_connections": row.peak_connections or 0,
                "total_messages_processed": row.total_messages or 0,
                "total_bytes_transferred": row.total_bytes or 0,
                "performance_status": "excellent" if (row.avg_delivery_time or 0) < 100 else "good" if (row.avg_delivery_time or 0) < 200 else "needs_attention",
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            # Cache the summary
            if self.cache:
                cache_key = f"performance_summary:{hours}h"
                await self.cache.set(cache_key, summary, 600)  # 10 minutes cache

            operation_time = time.time() - start_time
            logger.debug(f"Performance summary calculated in {operation_time:.3f}s")

            return summary

        except Exception as e:
            logger.error(f"Failed to get performance summary: {str(e)}")
            raise RepositoryError(f"Failed to get performance summary: {str(e)}")


# ============================================================================
# Enhanced Real-time Communication Repositories for Task 6.1.1 Phase 2
# ============================================================================

class WebSocketRoomRepository(EnhancedBaseRepository[WebSocketRoom], BulkOperationsMixin):
    """
    Repository for WebSocket room management with real-time capabilities.

    Provides comprehensive room management including:
    - Room lifecycle management (create, update, close)
    - Participant tracking and capacity management
    - Real-time room status monitoring
    - Performance-optimized queries with <200ms response times
    - Integration with booking communication system
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketRoom, db)
        self.cache = cache_manager
        self._cache_ttl = 600  # 10 minutes for room data
        self._active_rooms_ttl = 300  # 5 minutes for active rooms list

    async def create_room(self, room_data: Dict[str, Any]) -> WebSocketRoom:
        """
        Create a new WebSocket room with automatic configuration.

        Performance Metrics:
        - Target response time: <100ms for room creation
        - Database transaction time: <50ms with optimized inserts
        - Cache update time: <20ms for room indexing
        - Participant initialization: <30ms for owner setup

        Args:
            room_data: Room creation data

        Returns:
            Created WebSocket room

        Raises:
            RepositoryError: If room creation fails
        """
        start_time = time.time()

        try:
            # Set default values
            room_data.setdefault("is_active", True)
            room_data.setdefault("participant_count", 0)
            room_data.setdefault("total_messages", 0)
            room_data.setdefault("created_at", datetime.now(timezone.utc))
            room_data.setdefault("last_activity_at", datetime.now(timezone.utc))

            # Create room
            room = await self.create(room_data)

            # Cache the room
            if self.cache:
                cache_key = f"room:{room.room_id}"
                await self.cache.set(cache_key, {
                    "id": room.id,
                    "room_id": room.room_id,
                    "room_name": room.room_name,
                    "room_type": room.room_type,
                    "is_active": room.is_active,
                    "participant_count": room.participant_count,
                    "max_participants": room.max_participants,
                    "booking_id": room.booking_id,
                    "owner_id": room.owner_id
                }, self._cache_ttl)

                # Update active rooms cache
                await self._update_active_rooms_cache()

            operation_time = time.time() - start_time
            logger.info(f"WebSocket room created in {operation_time:.3f}s", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room.room_id,
                "room_type": room.room_type,
                "operation_time": operation_time
            })

            return room

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to create WebSocket room in {operation_time:.3f}s: {str(e)}", extra={
                "correlation_id": correlation_id.get(''),
                "error": str(e),
                "operation_time": operation_time
            })
            raise RepositoryError(f"Failed to create WebSocket room: {str(e)}")

    async def get_room_by_id(self, room_id: str) -> Optional[WebSocketRoom]:
        """
        Get room by room ID with caching support.

        Performance Metrics:
        - Target response time: <50ms for cached lookups
        - Database query time: <100ms for uncached lookups
        - Cache hit rate: >90% for active rooms

        Args:
            room_id: Unique room identifier

        Returns:
            WebSocket room if found, None otherwise
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"room:{room_id}"
                cached_room = await self.cache.get(cache_key)
                if cached_room:
                    operation_time = time.time() - start_time
                    logger.debug(f"Room retrieved from cache in {operation_time:.3f}s")
                    # Convert cached data back to model (simplified)
                    return await self.get(cached_room["id"])

            # Query database
            query = select(WebSocketRoom).where(WebSocketRoom.room_id == room_id)
            result = await self.db.execute(query)
            room = result.scalar_one_or_none()

            if room and self.cache:
                # Cache the result
                cache_key = f"room:{room_id}"
                await self.cache.set(cache_key, {
                    "id": room.id,
                    "room_id": room.room_id,
                    "room_name": room.room_name,
                    "room_type": room.room_type,
                    "is_active": room.is_active,
                    "participant_count": room.participant_count,
                    "max_participants": room.max_participants,
                    "booking_id": room.booking_id,
                    "owner_id": room.owner_id
                }, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Room retrieved in {operation_time:.3f}s")

            return room

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get room in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get room: {str(e)}")

    async def get_rooms_by_booking(self, booking_id: int) -> List[WebSocketRoom]:
        """
        Get all rooms associated with a booking.

        Performance Metrics:
        - Target response time: <150ms for booking room queries
        - Database query optimization with booking index
        - Cache integration for frequently accessed bookings

        Args:
            booking_id: Booking ID to search for

        Returns:
            List of WebSocket rooms for the booking
        """
        start_time = time.time()

        try:
            # Try cache first
            if self.cache:
                cache_key = f"booking_rooms:{booking_id}"
                cached_rooms = await self.cache.get(cache_key)
                if cached_rooms:
                    operation_time = time.time() - start_time
                    logger.debug(f"Booking rooms retrieved from cache in {operation_time:.3f}s")
                    # Convert cached data back to models
                    room_ids = [room["id"] for room in cached_rooms]
                    return await self.get_many(room_ids)

            # Query database with optimized index usage
            query = select(WebSocketRoom).where(
                and_(
                    WebSocketRoom.booking_id == booking_id,
                    WebSocketRoom.is_active == True
                )
            ).order_by(desc(WebSocketRoom.created_at))

            result = await self.db.execute(query)
            rooms = result.scalars().all()

            # Cache the results
            if self.cache and rooms:
                cache_key = f"booking_rooms:{booking_id}"
                cached_data = [{
                    "id": room.id,
                    "room_id": room.room_id,
                    "room_name": room.room_name,
                    "room_type": room.room_type,
                    "is_active": room.is_active,
                    "participant_count": room.participant_count
                } for room in rooms]
                await self.cache.set(cache_key, cached_data, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Booking rooms retrieved in {operation_time:.3f}s", extra={
                "booking_id": booking_id,
                "room_count": len(rooms),
                "operation_time": operation_time
            })

            return list(rooms)

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get booking rooms in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get booking rooms: {str(e)}")

    async def update_room_activity(self, room_id: str) -> bool:
        """
        Update room last activity timestamp.

        Performance Metrics:
        - Target response time: <50ms for activity updates
        - Optimized single-field update query
        - Cache invalidation for real-time consistency

        Args:
            room_id: Room ID to update

        Returns:
            True if update successful, False otherwise
        """
        start_time = time.time()

        try:
            # Update database
            query = update(WebSocketRoom).where(
                WebSocketRoom.room_id == room_id
            ).values(
                last_activity_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            # Invalidate cache
            if self.cache:
                cache_key = f"room:{room_id}"
                await self.cache.delete(cache_key)

            operation_time = time.time() - start_time
            success = result.rowcount > 0

            logger.debug(f"Room activity updated in {operation_time:.3f}s", extra={
                "room_id": room_id,
                "success": success,
                "operation_time": operation_time
            })

            return success

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to update room activity in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to update room activity: {str(e)}")

    async def increment_message_count(self, room_id: str) -> bool:
        """
        Increment room message count and update activity.

        Performance Metrics:
        - Target response time: <50ms for counter updates
        - Atomic increment operation
        - Cache invalidation for consistency

        Args:
            room_id: Room ID to update

        Returns:
            True if update successful, False otherwise
        """
        start_time = time.time()

        try:
            # Atomic increment with activity update
            query = update(WebSocketRoom).where(
                WebSocketRoom.room_id == room_id
            ).values(
                total_messages=WebSocketRoom.total_messages + 1,
                last_activity_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            # Invalidate cache
            if self.cache:
                cache_key = f"room:{room_id}"
                await self.cache.delete(cache_key)

            operation_time = time.time() - start_time
            success = result.rowcount > 0

            logger.debug(f"Room message count incremented in {operation_time:.3f}s", extra={
                "room_id": room_id,
                "success": success,
                "operation_time": operation_time
            })

            return success

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to increment message count in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to increment message count: {str(e)}")

    async def _update_active_rooms_cache(self) -> None:
        """Update active rooms cache for quick lookups."""
        if not self.cache:
            return

        try:
            # Get active rooms
            query = select(WebSocketRoom.room_id, WebSocketRoom.room_type, WebSocketRoom.participant_count).where(
                WebSocketRoom.is_active == True
            ).order_by(desc(WebSocketRoom.last_activity_at)).limit(100)

            result = await self.db.execute(query)
            active_rooms = [
                {
                    "room_id": row.room_id,
                    "room_type": row.room_type,
                    "participant_count": row.participant_count
                }
                for row in result
            ]

            await self.cache.set("active_rooms", active_rooms, self._active_rooms_ttl)

        except Exception as e:
            logger.warning(f"Failed to update active rooms cache: {str(e)}")


class WebSocketRoomParticipantRepository(EnhancedBaseRepository[WebSocketRoomParticipant], BulkOperationsMixin):
    """
    Repository for WebSocket room participant management with real-time tracking.

    Provides comprehensive participant management including:
    - Participant join/leave operations with role management
    - Activity monitoring and presence tracking
    - Bulk participant operations for scalability
    - Performance-optimized queries with <200ms response times
    - Integration with room capacity management
    """

    def __init__(self, db: AsyncSession, cache_manager: Optional[CacheManager] = None):
        super().__init__(WebSocketRoomParticipant, db)
        self.cache = cache_manager
        self._cache_ttl = 300  # 5 minutes for participant data
        self._room_participants_ttl = 180  # 3 minutes for room participant lists

    async def add_participant(
        self,
        room_id: int,
        user_id: int,
        connection_id: Optional[int] = None,
        role: str = "participant"
    ) -> WebSocketRoomParticipant:
        """
        Add a participant to a WebSocket room with capacity checking.

        Performance Metrics:
        - Target response time: <100ms for participant addition
        - Database transaction time: <50ms with optimized inserts
        - Capacity check time: <20ms with room cache lookup
        - Participant count update: <30ms with atomic increment

        Args:
            room_id: Room ID to join
            user_id: User ID of participant
            connection_id: Optional WebSocket connection ID
            role: Participant role (owner, moderator, participant)

        Returns:
            Created participant record

        Raises:
            RepositoryError: If participant addition fails or room is full
        """
        start_time = time.time()

        try:
            # Check if participant already exists (active)
            existing_query = select(WebSocketRoomParticipant).where(
                and_(
                    WebSocketRoomParticipant.room_id == room_id,
                    WebSocketRoomParticipant.user_id == user_id,
                    WebSocketRoomParticipant.is_active == True
                )
            )
            existing_result = await self.db.execute(existing_query)
            existing_participant = existing_result.scalar_one_or_none()

            if existing_participant:
                # Update connection if provided
                if connection_id:
                    existing_participant.connection_id = connection_id
                    existing_participant.last_seen_at = datetime.now(timezone.utc)
                    await self.db.commit()

                operation_time = time.time() - start_time
                logger.debug(f"Participant already exists, updated in {operation_time:.3f}s")
                return existing_participant

            # Check room capacity
            room_query = select(WebSocketRoom).where(WebSocketRoom.id == room_id)
            room_result = await self.db.execute(room_query)
            room = room_result.scalar_one_or_none()

            if not room:
                raise RepositoryError("Room not found")

            if room.participant_count >= room.max_participants:
                raise RepositoryError("Room is at capacity")

            # Create participant
            participant_data = {
                "room_id": room_id,
                "user_id": user_id,
                "connection_id": connection_id,
                "role": role,
                "is_active": True,
                "joined_at": datetime.now(timezone.utc),
                "last_seen_at": datetime.now(timezone.utc)
            }

            participant = await self.create(participant_data)

            # Update room participant count
            room_update_query = update(WebSocketRoom).where(
                WebSocketRoom.id == room_id
            ).values(
                participant_count=WebSocketRoom.participant_count + 1,
                last_activity_at=datetime.now(timezone.utc)
            )
            await self.db.execute(room_update_query)
            await self.db.commit()

            # Invalidate caches
            if self.cache:
                await self.cache.delete(f"room_participants:{room_id}")
                await self.cache.delete(f"user_rooms:{user_id}")
                await self.cache.delete(f"room:{room.room_id}")

            operation_time = time.time() - start_time
            logger.info(f"Participant added in {operation_time:.3f}s", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_id": user_id,
                "role": role,
                "operation_time": operation_time
            })

            return participant

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to add participant in {operation_time:.3f}s: {str(e)}", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise RepositoryError(f"Failed to add participant: {str(e)}")

    async def remove_participant(self, room_id: int, user_id: int) -> bool:
        """
        Remove a participant from a WebSocket room.

        Performance Metrics:
        - Target response time: <100ms for participant removal
        - Database transaction time: <50ms with optimized updates
        - Participant count update: <30ms with atomic decrement
        - Cache invalidation: <20ms for consistency

        Args:
            room_id: Room ID to leave
            user_id: User ID of participant

        Returns:
            True if participant was removed, False if not found

        Raises:
            RepositoryError: If removal operation fails
        """
        start_time = time.time()

        try:
            # Update participant as inactive
            participant_update_query = update(WebSocketRoomParticipant).where(
                and_(
                    WebSocketRoomParticipant.room_id == room_id,
                    WebSocketRoomParticipant.user_id == user_id,
                    WebSocketRoomParticipant.is_active == True
                )
            ).values(
                is_active=False,
                left_at=datetime.now(timezone.utc)
            )

            participant_result = await self.db.execute(participant_update_query)

            if participant_result.rowcount > 0:
                # Update room participant count
                room_update_query = update(WebSocketRoom).where(
                    WebSocketRoom.id == room_id
                ).values(
                    participant_count=func.greatest(WebSocketRoom.participant_count - 1, 0),
                    last_activity_at=datetime.now(timezone.utc)
                )
                await self.db.execute(room_update_query)

            await self.db.commit()

            # Invalidate caches
            if self.cache:
                await self.cache.delete(f"room_participants:{room_id}")
                await self.cache.delete(f"user_rooms:{user_id}")

            operation_time = time.time() - start_time
            success = participant_result.rowcount > 0

            logger.info(f"Participant removal completed in {operation_time:.3f}s", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_id": user_id,
                "success": success,
                "operation_time": operation_time
            })

            return success

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to remove participant in {operation_time:.3f}s: {str(e)}", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_id": user_id,
                "error": str(e),
                "operation_time": operation_time
            })
            raise RepositoryError(f"Failed to remove participant: {str(e)}")

    async def get_room_participants(self, room_id: int, active_only: bool = True) -> List[WebSocketRoomParticipant]:
        """
        Get all participants in a room with caching support.

        Performance Metrics:
        - Target response time: <100ms for participant queries
        - Cache hit rate: >85% for active rooms
        - Database query optimization with room index

        Args:
            room_id: Room ID to get participants for
            active_only: Whether to return only active participants

        Returns:
            List of room participants
        """
        start_time = time.time()

        try:
            # Try cache first for active participants
            if self.cache and active_only:
                cache_key = f"room_participants:{room_id}"
                cached_participants = await self.cache.get(cache_key)
                if cached_participants:
                    operation_time = time.time() - start_time
                    logger.debug(f"Room participants retrieved from cache in {operation_time:.3f}s")
                    # Convert cached data back to models
                    participant_ids = [p["id"] for p in cached_participants]
                    return await self.get_many(participant_ids)

            # Query database
            query = select(WebSocketRoomParticipant).where(
                WebSocketRoomParticipant.room_id == room_id
            )

            if active_only:
                query = query.where(WebSocketRoomParticipant.is_active == True)

            query = query.order_by(WebSocketRoomParticipant.joined_at)

            result = await self.db.execute(query)
            participants = result.scalars().all()

            # Cache active participants
            if self.cache and active_only and participants:
                cache_key = f"room_participants:{room_id}"
                cached_data = [{
                    "id": p.id,
                    "user_id": p.user_id,
                    "role": p.role,
                    "joined_at": p.joined_at.isoformat(),
                    "last_seen_at": p.last_seen_at.isoformat()
                } for p in participants]
                await self.cache.set(cache_key, cached_data, self._room_participants_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"Room participants retrieved in {operation_time:.3f}s", extra={
                "room_id": room_id,
                "participant_count": len(participants),
                "active_only": active_only,
                "operation_time": operation_time
            })

            return list(participants)

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get room participants in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get room participants: {str(e)}")

    async def get_user_rooms(self, user_id: int, active_only: bool = True) -> List[WebSocketRoomParticipant]:
        """
        Get all rooms a user is participating in.

        Performance Metrics:
        - Target response time: <150ms for user room queries
        - Cache integration for frequently accessed users
        - Database query optimization with user index

        Args:
            user_id: User ID to get rooms for
            active_only: Whether to return only active participations

        Returns:
            List of user's room participations
        """
        start_time = time.time()

        try:
            # Try cache first for active rooms
            if self.cache and active_only:
                cache_key = f"user_rooms:{user_id}"
                cached_rooms = await self.cache.get(cache_key)
                if cached_rooms:
                    operation_time = time.time() - start_time
                    logger.debug(f"User rooms retrieved from cache in {operation_time:.3f}s")
                    # Convert cached data back to models
                    participation_ids = [r["id"] for r in cached_rooms]
                    return await self.get_many(participation_ids)

            # Query database
            query = select(WebSocketRoomParticipant).where(
                WebSocketRoomParticipant.user_id == user_id
            )

            if active_only:
                query = query.where(WebSocketRoomParticipant.is_active == True)

            query = query.order_by(desc(WebSocketRoomParticipant.last_seen_at))

            result = await self.db.execute(query)
            participations = result.scalars().all()

            # Cache active participations
            if self.cache and active_only and participations:
                cache_key = f"user_rooms:{user_id}"
                cached_data = [{
                    "id": p.id,
                    "room_id": p.room_id,
                    "role": p.role,
                    "joined_at": p.joined_at.isoformat(),
                    "last_seen_at": p.last_seen_at.isoformat()
                } for p in participations]
                await self.cache.set(cache_key, cached_data, self._cache_ttl)

            operation_time = time.time() - start_time
            logger.debug(f"User rooms retrieved in {operation_time:.3f}s", extra={
                "user_id": user_id,
                "room_count": len(participations),
                "active_only": active_only,
                "operation_time": operation_time
            })

            return list(participations)

        except Exception as e:
            operation_time = time.time() - start_time
            logger.error(f"Failed to get user rooms in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to get user rooms: {str(e)}")

    async def update_participant_activity(self, room_id: int, user_id: int) -> bool:
        """
        Update participant last seen timestamp.

        Performance Metrics:
        - Target response time: <50ms for activity updates
        - Optimized single-field update query
        - Cache invalidation for real-time consistency

        Args:
            room_id: Room ID
            user_id: User ID

        Returns:
            True if update successful, False otherwise
        """
        start_time = time.time()

        try:
            # Update last seen timestamp
            query = update(WebSocketRoomParticipant).where(
                and_(
                    WebSocketRoomParticipant.room_id == room_id,
                    WebSocketRoomParticipant.user_id == user_id,
                    WebSocketRoomParticipant.is_active == True
                )
            ).values(
                last_seen_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(query)
            await self.db.commit()

            operation_time = time.time() - start_time
            success = result.rowcount > 0

            logger.debug(f"Participant activity updated in {operation_time:.3f}s", extra={
                "room_id": room_id,
                "user_id": user_id,
                "success": success,
                "operation_time": operation_time
            })

            return success

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed to update participant activity in {operation_time:.3f}s: {str(e)}")
            raise RepositoryError(f"Failed to update participant activity: {str(e)}")

    async def bulk_remove_participants(self, room_id: int, user_ids: List[int]) -> int:
        """
        Remove multiple participants from a room in bulk.

        Performance Metrics:
        - Target response time: <200ms for bulk operations
        - Batch processing for >100 participants
        - Atomic transaction for consistency

        Args:
            room_id: Room ID
            user_ids: List of user IDs to remove

        Returns:
            Number of participants removed

        Raises:
            RepositoryError: If bulk removal fails
        """
        start_time = time.time()

        try:
            if not user_ids:
                return 0

            # Bulk update participants as inactive
            participant_update_query = update(WebSocketRoomParticipant).where(
                and_(
                    WebSocketRoomParticipant.room_id == room_id,
                    WebSocketRoomParticipant.user_id.in_(user_ids),
                    WebSocketRoomParticipant.is_active == True
                )
            ).values(
                is_active=False,
                left_at=datetime.now(timezone.utc)
            )

            participant_result = await self.db.execute(participant_update_query)
            removed_count = participant_result.rowcount

            if removed_count > 0:
                # Update room participant count
                room_update_query = update(WebSocketRoom).where(
                    WebSocketRoom.id == room_id
                ).values(
                    participant_count=func.greatest(WebSocketRoom.participant_count - removed_count, 0),
                    last_activity_at=datetime.now(timezone.utc)
                )
                await self.db.execute(room_update_query)

            await self.db.commit()

            # Invalidate caches
            if self.cache:
                await self.cache.delete(f"room_participants:{room_id}")
                for user_id in user_ids:
                    await self.cache.delete(f"user_rooms:{user_id}")

            operation_time = time.time() - start_time
            logger.info(f"Bulk participant removal completed in {operation_time:.3f}s", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_count": len(user_ids),
                "removed_count": removed_count,
                "operation_time": operation_time
            })

            return removed_count

        except Exception as e:
            await self.db.rollback()
            operation_time = time.time() - start_time
            logger.error(f"Failed bulk participant removal in {operation_time:.3f}s: {str(e)}", extra={
                "correlation_id": correlation_id.get(''),
                "room_id": room_id,
                "user_count": len(user_ids),
                "error": str(e),
                "operation_time": operation_time
            })
            raise RepositoryError(f"Failed bulk participant removal: {str(e)}")
