"""
Service models for Culture Connect Backend API.

This module defines service-related models for service listings,
categories, pricing, availability, and media management.

Implements Task 3.2.1 requirements for comprehensive service listing system
with hierarchical categorization, flexible pricing models, and availability management.
"""

import enum
from datetime import datetime, date, time
from decimal import Decimal
from typing import Optional, List, Dict, Any

from sqlalchemy import (
    Column, Integer, String, Text, Numeric, Boolean, DateTime, Date, Time,
    ForeignKey, Enum, JSON, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.orm import relationship, validates
from sqlalchemy.ext.hybrid import hybrid_property

from app.db.base import BaseModelWithAudit


class ServiceStatus(enum.Enum):
    """Service status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"


class PricingType(enum.Enum):
    """Pricing type enumeration."""
    FIXED = "fixed"
    HOURLY = "hourly"
    DAILY = "daily"
    PACKAGE = "package"
    CUSTOM = "custom"


class AvailabilityType(enum.Enum):
    """Availability type enumeration."""
    ALWAYS = "always"
    SCHEDULED = "scheduled"
    ON_DEMAND = "on_demand"


class ServiceCategory(BaseModelWithAudit):
    """
    Service category model for hierarchical service organization.

    Supports multi-level categorization with parent-child relationships
    for organizing services into logical groups and subcategories.
    """

    __tablename__ = "service_categories"

    # Category information
    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Category name"
    )

    slug = Column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        doc="URL-friendly category identifier"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Category description"
    )

    # Hierarchical structure
    parent_id = Column(
        Integer,
        ForeignKey("service_categories.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Parent category ID for hierarchical structure"
    )

    # Display and ordering
    display_order = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Display order within parent category"
    )

    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether category is active"
    )

    # Media
    icon_url = Column(
        String(500),
        nullable=True,
        doc="Category icon URL"
    )

    image_url = Column(
        String(500),
        nullable=True,
        doc="Category image URL"
    )

    # Additional data
    extra_data = Column(
        JSON,
        nullable=True,
        doc="Additional category metadata"
    )

    # Relationships
    parent = relationship(
        "ServiceCategory",
        remote_side="ServiceCategory.id",
        back_populates="children"
    )

    children = relationship(
        "ServiceCategory",
        back_populates="parent",
        cascade="all, delete-orphan"
    )

    services = relationship(
        "Service",
        back_populates="category",
        cascade="all, delete-orphan"
    )

    # Indexes
    __table_args__ = (
        Index('idx_service_categories_parent_order', 'parent_id', 'display_order'),
        Index('idx_service_categories_active_name', 'is_active', 'name'),
    )

    @validates('slug')
    def validate_slug(self, key, slug):
        """Validate slug format."""
        if slug:
            # Convert to lowercase and replace spaces with hyphens
            slug = slug.lower().replace(' ', '-')
            # Remove special characters except hyphens
            import re
            slug = re.sub(r'[^a-z0-9\-]', '', slug)
        return slug

    @hybrid_property
    def full_path(self) -> str:
        """Get full category path from root to current category."""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

    def __repr__(self) -> str:
        return f"<ServiceCategory(id={self.id}, name='{self.name}', slug='{self.slug}')>"


class Service(BaseModelWithAudit):
    """
    Service model for cultural service listings.

    Represents individual services offered by vendors including
    pricing, availability, media, and detailed service information.
    """

    __tablename__ = "services"

    # Foreign keys
    vendor_id = Column(
        Integer,
        ForeignKey("vendors.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated vendor ID"
    )

    category_id = Column(
        Integer,
        ForeignKey("service_categories.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Service category ID"
    )

    # Basic service information
    title = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Service title"
    )

    slug = Column(
        String(255),
        nullable=False,
        index=True,
        doc="URL-friendly service identifier"
    )

    description = Column(
        Text,
        nullable=False,
        doc="Detailed service description"
    )

    short_description = Column(
        String(500),
        nullable=True,
        doc="Short service description for listings"
    )

    # Service details
    duration_minutes = Column(
        Integer,
        nullable=True,
        doc="Service duration in minutes"
    )

    max_participants = Column(
        Integer,
        nullable=True,
        doc="Maximum number of participants"
    )

    min_participants = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Minimum number of participants"
    )

    # Location and logistics
    location = Column(
        String(500),
        nullable=True,
        doc="Service location or meeting point"
    )

    includes = Column(
        JSON,
        nullable=True,
        doc="What's included in the service"
    )

    excludes = Column(
        JSON,
        nullable=True,
        doc="What's not included in the service"
    )

    requirements = Column(
        JSON,
        nullable=True,
        doc="Service requirements and restrictions"
    )

    # Pricing
    pricing_type = Column(
        Enum(PricingType),
        nullable=False,
        default=PricingType.FIXED,
        doc="Type of pricing model"
    )

    base_price = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Base price for the service"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="USD",
        doc="Currency code (ISO 4217)"
    )

    pricing_details = Column(
        JSON,
        nullable=True,
        doc="Detailed pricing information"
    )

    # Availability
    availability_type = Column(
        Enum(AvailabilityType),
        nullable=False,
        default=AvailabilityType.SCHEDULED,
        doc="Type of availability"
    )

    advance_booking_hours = Column(
        Integer,
        nullable=False,
        default=24,
        doc="Minimum advance booking hours"
    )

    cancellation_hours = Column(
        Integer,
        nullable=False,
        default=24,
        doc="Cancellation deadline in hours"
    )

    # Status and visibility
    status = Column(
        Enum(ServiceStatus),
        nullable=False,
        default=ServiceStatus.DRAFT,
        doc="Service status"
    )

    is_featured = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether service is featured"
    )

    is_instant_booking = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether instant booking is enabled"
    )

    # Performance metrics
    total_bookings = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of bookings"
    )

    average_rating = Column(
        Numeric(3, 2),
        nullable=False,
        default=Decimal("0.00"),
        doc="Average customer rating (0.00-5.00)"
    )

    total_reviews = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of reviews"
    )

    # SEO and metadata
    meta_title = Column(
        String(255),
        nullable=True,
        doc="SEO meta title"
    )

    meta_description = Column(
        String(500),
        nullable=True,
        doc="SEO meta description"
    )

    tags = Column(
        JSON,
        nullable=True,
        doc="Service tags for search and filtering"
    )

    extra_data = Column(
        JSON,
        nullable=True,
        doc="Additional service metadata"
    )

    # Relationships
    vendor = relationship(
        "Vendor",
        back_populates="services"
    )

    category = relationship(
        "ServiceCategory",
        back_populates="services"
    )

    images = relationship(
        "ServiceImage",
        back_populates="service",
        cascade="all, delete-orphan",
        order_by="ServiceImage.display_order"
    )

    availability_slots = relationship(
        "ServiceAvailability",
        back_populates="service",
        cascade="all, delete-orphan"
    )

    pricing_tiers = relationship(
        "ServicePricing",
        back_populates="service",
        cascade="all, delete-orphan"
    )

    # Marketplace optimization relationships (TODO: Implement these models)
    # seo_analyses = relationship(
    #     "SEOAnalysis",
    #     back_populates="service",
    #     cascade="all, delete-orphan"
    # )

    # performance_metrics = relationship(
    #     "PerformanceMetrics",
    #     back_populates="service",
    #     cascade="all, delete-orphan"
    # )

    # competitive_analyses = relationship(
    #     "CompetitiveAnalysis",
    #     back_populates="service",
    #     cascade="all, delete-orphan"
    # )

    # optimization_recommendations = relationship(
    #     "OptimizationRecommendation",
    #     back_populates="service",
    #     cascade="all, delete-orphan"
    # )

    # mobile_optimizations = relationship(
    #     "MobileOptimization",
    #     back_populates="service",
    #     cascade="all, delete-orphan"
    # )

    # Booking relationships (implemented in Task 4.1.1)
    bookings = relationship(
        "Booking",
        back_populates="service",
        cascade="all, delete-orphan",
        doc="Service bookings"
    )

    # Review relationships (Task 4.4.1)
    reviews = relationship(
        "Review",
        back_populates="service",
        cascade="all, delete-orphan",
        doc="Reviews for this service"
    )

    # Promotional relationships (Phase 5 - Promotional & Advertising System)
    promotional_listings = relationship(
        "PromotionalListing",
        back_populates="service",
        cascade="all, delete-orphan",
        doc="Featured promotional listings for this service"
    )

    # Indexes and constraints
    __table_args__ = (
        Index('idx_services_vendor_status', 'vendor_id', 'status'),
        Index('idx_services_category_status', 'category_id', 'status'),
        Index('idx_services_featured_rating', 'is_featured', 'average_rating'),
        Index('idx_services_pricing', 'pricing_type', 'base_price'),
        UniqueConstraint('vendor_id', 'slug', name='uq_services_vendor_slug'),
        CheckConstraint('base_price >= 0', name='ck_services_base_price_positive'),
        CheckConstraint('max_participants >= min_participants', name='ck_services_participants_valid'),
        CheckConstraint('average_rating >= 0 AND average_rating <= 5', name='ck_services_rating_range'),
    )

    @validates('slug')
    def validate_slug(self, key, slug):
        """Validate slug format."""
        if slug:
            # Convert to lowercase and replace spaces with hyphens
            slug = slug.lower().replace(' ', '-')
            # Remove special characters except hyphens
            import re
            slug = re.sub(r'[^a-z0-9\-]', '', slug)
        return slug

    @validates('currency')
    def validate_currency(self, key, currency):
        """Validate currency code."""
        if currency:
            currency = currency.upper()
            # Basic validation for ISO 4217 format
            if len(currency) != 3 or not currency.isalpha():
                raise ValueError("Currency must be a valid 3-letter ISO 4217 code")
        return currency

    @hybrid_property
    def is_available(self) -> bool:
        """Check if service is currently available for booking."""
        return self.status == ServiceStatus.ACTIVE

    def __repr__(self) -> str:
        return f"<Service(id={self.id}, title='{self.title}', vendor_id={self.vendor_id})>"


class ServiceImage(BaseModelWithAudit):
    """
    Service image model for service media management.

    Stores images, videos, and other media associated with services
    including display order and metadata.
    """

    __tablename__ = "service_images"

    # Foreign key
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated service ID"
    )

    # Image information
    url = Column(
        String(500),
        nullable=False,
        doc="Image URL"
    )

    alt_text = Column(
        String(255),
        nullable=True,
        doc="Alt text for accessibility"
    )

    caption = Column(
        String(500),
        nullable=True,
        doc="Image caption"
    )

    # Media type and properties
    media_type = Column(
        String(50),
        nullable=False,
        default="image",
        doc="Media type (image, video, document)"
    )

    file_size = Column(
        Integer,
        nullable=True,
        doc="File size in bytes"
    )

    dimensions = Column(
        JSON,
        nullable=True,
        doc="Image dimensions (width, height)"
    )

    # Display properties
    display_order = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Display order"
    )

    is_primary = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the primary image"
    )

    is_featured = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether image is featured"
    )

    # Additional data
    extra_data = Column(
        JSON,
        nullable=True,
        doc="Additional image metadata"
    )

    # Relationships
    service = relationship(
        "Service",
        back_populates="images"
    )

    # Indexes
    __table_args__ = (
        Index('idx_service_images_service_order', 'service_id', 'display_order'),
        Index('idx_service_images_primary', 'service_id', 'is_primary'),
    )

    def __repr__(self) -> str:
        return f"<ServiceImage(id={self.id}, service_id={self.service_id}, media_type='{self.media_type}')>"


class ServicePricing(BaseModelWithAudit):
    """
    Service pricing model for flexible pricing structures.

    Supports multiple pricing tiers, group discounts, and seasonal pricing
    for comprehensive pricing management.
    """

    __tablename__ = "service_pricing"

    # Foreign key
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated service ID"
    )

    # Pricing tier information
    tier_name = Column(
        String(255),
        nullable=False,
        doc="Pricing tier name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Pricing tier description"
    )

    # Pricing details
    price = Column(
        Numeric(10, 2),
        nullable=False,
        doc="Price for this tier"
    )

    currency = Column(
        String(3),
        nullable=False,
        default="USD",
        doc="Currency code (ISO 4217)"
    )

    # Participant ranges
    min_participants = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Minimum participants for this tier"
    )

    max_participants = Column(
        Integer,
        nullable=True,
        doc="Maximum participants for this tier"
    )

    # Validity and conditions
    valid_from = Column(
        Date,
        nullable=True,
        doc="Pricing valid from date"
    )

    valid_until = Column(
        Date,
        nullable=True,
        doc="Pricing valid until date"
    )

    conditions = Column(
        JSON,
        nullable=True,
        doc="Special conditions for this pricing tier"
    )

    # Status
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether pricing tier is active"
    )

    # Relationships
    service = relationship(
        "Service",
        back_populates="pricing_tiers"
    )

    # Indexes and constraints
    __table_args__ = (
        Index('idx_service_pricing_service_active', 'service_id', 'is_active'),
        Index('idx_service_pricing_participants', 'min_participants', 'max_participants'),
        CheckConstraint('price >= 0', name='ck_service_pricing_price_positive'),
        CheckConstraint('max_participants IS NULL OR max_participants >= min_participants',
                       name='ck_service_pricing_participants_valid'),
    )

    def __repr__(self) -> str:
        return f"<ServicePricing(id={self.id}, service_id={self.service_id}, tier='{self.tier_name}')>"


class ServiceAvailability(BaseModelWithAudit):
    """
    Service availability model for scheduling and calendar management.

    Manages service availability slots, recurring schedules, and
    booking calendar integration.
    """

    __tablename__ = "service_availability"

    # Foreign key
    service_id = Column(
        Integer,
        ForeignKey("services.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated service ID"
    )

    # Date and time
    available_date = Column(
        Date,
        nullable=False,
        index=True,
        doc="Available date"
    )

    start_time = Column(
        Time,
        nullable=True,
        doc="Start time (null for all-day availability)"
    )

    end_time = Column(
        Time,
        nullable=True,
        doc="End time (null for all-day availability)"
    )

    # Capacity
    max_bookings = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Maximum number of bookings for this slot"
    )

    current_bookings = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Current number of bookings"
    )

    # Pricing override
    price_override = Column(
        Numeric(10, 2),
        nullable=True,
        doc="Price override for this specific slot"
    )

    # Status and notes
    is_available = Column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether slot is available for booking"
    )

    notes = Column(
        Text,
        nullable=True,
        doc="Special notes for this availability slot"
    )

    # Recurring schedule
    is_recurring = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is a recurring availability"
    )

    recurrence_pattern = Column(
        JSON,
        nullable=True,
        doc="Recurrence pattern details"
    )

    recurrence_end_date = Column(
        Date,
        nullable=True,
        doc="End date for recurring availability"
    )

    # Relationships
    service = relationship(
        "Service",
        back_populates="availability_slots"
    )

    # Booking relationships (implemented in Task 4.1.1)
    bookings = relationship(
        "Booking",
        back_populates="availability",
        cascade="all, delete-orphan",
        doc="Bookings for this availability slot"
    )

    # Indexes and constraints
    __table_args__ = (
        Index('idx_service_availability_service_date', 'service_id', 'available_date'),
        Index('idx_service_availability_date_time', 'available_date', 'start_time'),
        Index('idx_service_availability_available', 'is_available', 'available_date'),
        CheckConstraint('max_bookings > 0', name='ck_service_availability_max_bookings_positive'),
        CheckConstraint('current_bookings >= 0', name='ck_service_availability_current_bookings_non_negative'),
        CheckConstraint('current_bookings <= max_bookings', name='ck_service_availability_bookings_within_limit'),
        CheckConstraint('end_time IS NULL OR start_time IS NULL OR end_time > start_time',
                       name='ck_service_availability_time_valid'),
    )

    @hybrid_property
    def available_spots(self) -> int:
        """Calculate available spots for booking."""
        return self.max_bookings - self.current_bookings

    @hybrid_property
    def is_fully_booked(self) -> bool:
        """Check if slot is fully booked."""
        return self.current_bookings >= self.max_bookings

    def __repr__(self) -> str:
        return f"<ServiceAvailability(id={self.id}, service_id={self.service_id}, date={self.available_date})>"
