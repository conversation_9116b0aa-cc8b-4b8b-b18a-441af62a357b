#!/usr/bin/env python3
"""
WebSocket test runner for Culture Connect Backend.

This script provides comprehensive test execution for WebSocket infrastructure including:
- Unit tests with coverage reporting
- Integration tests with database rollback
- Performance tests with target validation
- Security tests with vulnerability assessment
- Complete test suite execution with detailed reporting

Implements Task 6.1.1 Phase 5 requirements with >85% code coverage validation.
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional


class WebSocketTestRunner:
    """
    Comprehensive test runner for WebSocket infrastructure.
    
    Provides test execution, coverage reporting, performance validation,
    and detailed test result analysis.
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.test_dir = self.project_root / "tests" / "websocket"
        self.coverage_target = 85.0
        self.performance_targets = {
            "connection_creation": 500,  # ms
            "authentication": 200,       # ms
            "status_updates": 100,       # ms
            "query_operations": 200,     # ms
            "bulk_operations": 1000      # records/second
        }
    
    def run_unit_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run unit tests with coverage reporting.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Test results dictionary
        """
        print("🧪 Running WebSocket Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_websocket_services.py"),
            "-m", "unit",
            "--cov=app.services.websocket_services",
            "--cov=app.api.v1.endpoints.websocket_endpoints",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/websocket_unit",
            f"--cov-fail-under={self.coverage_target}",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        
        print(f"✅ Unit Tests {'PASSED' if success else 'FAILED'} in {execution_time:.2f}s")
        
        if not success:
            print("❌ Unit Test Failures:")
            print(result.stdout)
            print(result.stderr)
        
        return {
            "type": "unit",
            "success": success,
            "execution_time": execution_time,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_integration_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run integration tests with database rollback.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Test results dictionary
        """
        print("🔗 Running WebSocket Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_websocket_integration.py"),
            "-m", "integration",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        
        print(f"✅ Integration Tests {'PASSED' if success else 'FAILED'} in {execution_time:.2f}s")
        
        if not success:
            print("❌ Integration Test Failures:")
            print(result.stdout)
            print(result.stderr)
        
        return {
            "type": "integration",
            "success": success,
            "execution_time": execution_time,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_performance_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run performance tests with target validation.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Test results dictionary
        """
        print("⚡ Running WebSocket Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_websocket_performance.py"),
            "-m", "performance",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        # Set environment variable for performance test execution
        env = os.environ.copy()
        env["RUN_PERFORMANCE_TESTS"] = "1"
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True, env=env)
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        
        print(f"✅ Performance Tests {'PASSED' if success else 'FAILED'} in {execution_time:.2f}s")
        
        if not success:
            print("❌ Performance Test Failures:")
            print(result.stdout)
            print(result.stderr)
        
        return {
            "type": "performance",
            "success": success,
            "execution_time": execution_time,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_security_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run security tests with vulnerability assessment.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Test results dictionary
        """
        print("🔒 Running WebSocket Security Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_websocket_security.py"),
            "-m", "security",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        
        print(f"✅ Security Tests {'PASSED' if success else 'FAILED'} in {execution_time:.2f}s")
        
        if not success:
            print("❌ Security Test Failures:")
            print(result.stdout)
            print(result.stderr)
        
        return {
            "type": "security",
            "success": success,
            "execution_time": execution_time,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_api_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run API endpoint tests.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Test results dictionary
        """
        print("🌐 Running WebSocket API Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_websocket_endpoints.py"),
            "-m", "api",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        
        print(f"✅ API Tests {'PASSED' if success else 'FAILED'} in {execution_time:.2f}s")
        
        if not success:
            print("❌ API Test Failures:")
            print(result.stdout)
            print(result.stderr)
        
        return {
            "type": "api",
            "success": success,
            "execution_time": execution_time,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def run_all_tests(self, verbose: bool = True) -> Dict[str, any]:
        """
        Run complete WebSocket test suite.
        
        Args:
            verbose: Enable verbose output
            
        Returns:
            Complete test results
        """
        print("🚀 Running Complete WebSocket Test Suite...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all test categories
        results = {
            "unit": self.run_unit_tests(verbose),
            "integration": self.run_integration_tests(verbose),
            "performance": self.run_performance_tests(verbose),
            "security": self.run_security_tests(verbose),
            "api": self.run_api_tests(verbose)
        }
        
        total_time = time.time() - start_time
        
        # Calculate overall results
        all_passed = all(result["success"] for result in results.values())
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result["success"])
        
        print("=" * 60)
        print("📊 WebSocket Test Suite Results:")
        print(f"   Total Test Categories: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        print(f"   Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
        print(f"   Total Execution Time: {total_time:.2f}s")
        
        if all_passed:
            print("✅ ALL WEBSOCKET TESTS PASSED!")
        else:
            print("❌ SOME WEBSOCKET TESTS FAILED!")
            for test_type, result in results.items():
                if not result["success"]:
                    print(f"   - {test_type.upper()} tests failed")
        
        return {
            "overall_success": all_passed,
            "total_time": total_time,
            "results": results,
            "summary": {
                "total_categories": total_tests,
                "passed": passed_tests,
                "failed": total_tests - passed_tests,
                "success_rate": (passed_tests / total_tests) * 100
            }
        }
    
    def generate_coverage_report(self) -> None:
        """Generate comprehensive coverage report."""
        print("📈 Generating Coverage Report...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "--cov=app.services.websocket_services",
            "--cov=app.api.v1.endpoints.websocket_endpoints",
            "--cov=app.repositories.websocket_repositories",
            "--cov=app.models.websocket_models",
            "--cov=app.schemas.websocket_schemas",
            "--cov-report=html:htmlcov/websocket_complete",
            "--cov-report=term-missing",
            "--cov-report=json:coverage.json",
            f"--cov-fail-under={self.coverage_target}",
            "--tb=no",
            "-q"
        ]
        
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Coverage report generated (target: {self.coverage_target}%)")
            print("   HTML report: htmlcov/websocket_complete/index.html")
        else:
            print(f"❌ Coverage below target ({self.coverage_target}%)")
            print(result.stdout)


def main():
    """Main test runner entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="WebSocket Test Runner")
    parser.add_argument("--test-type", choices=["unit", "integration", "performance", "security", "api", "all"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage report")
    
    args = parser.parse_args()
    
    runner = WebSocketTestRunner()
    
    if args.test_type == "all":
        results = runner.run_all_tests(args.verbose)
        success = results["overall_success"]
    else:
        test_method = getattr(runner, f"run_{args.test_type}_tests")
        result = test_method(args.verbose)
        success = result["success"]
    
    if args.coverage:
        runner.generate_coverage_report()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
