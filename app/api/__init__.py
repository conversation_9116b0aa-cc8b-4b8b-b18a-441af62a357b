"""
API package for Culture Connect Backend.

This package contains all API endpoints organized by version and functionality:
- v1: Current stable API version
- v2: Future API version (reserved)
- Common API utilities and middleware
"""

from fastapi import APIRouter

# Import API routers
from .v1.api import api_router as v1_router

# Create main API router
api_router = APIRouter()

# Include version-specific routers
api_router.include_router(v1_router, prefix="/v1")

__all__ = ["api_router"]