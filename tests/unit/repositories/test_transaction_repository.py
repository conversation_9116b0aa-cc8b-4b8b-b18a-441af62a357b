"""
Unit tests for Transaction Repository classes.

This module tests the transaction repository layer functionality including:
- TransactionRepository: Detailed transaction tracking with provider integration
- TransactionEventRepository: Transaction event history for compliance and audit trails
- Reconciliation and audit trail management
- Performance optimization and query efficiency
- Webhook data processing and storage methods

Implements comprehensive test coverage for Step 5 Payment & Transaction Management System.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.transaction_repository import TransactionRepository, TransactionEventRepository
from app.models.transaction_models import Transaction, TransactionEvent, TransactionStatus, TransactionEventType, TransactionType
from app.core.payment.config import PaymentProviderType
from app.repositories.base import PaginationParams, QueryResult, RepositoryError


class TestTransactionRepository:
    """Test TransactionRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def transaction_repository(self, mock_db_session):
        """Create TransactionRepository instance with mock session."""
        return TransactionRepository(mock_db_session)

    @pytest.fixture
    def sample_transaction_data(self):
        """Sample transaction data for testing."""
        return {
            "payment_id": 123,
            "transaction_type": TransactionType.CHARGE,
            "amount": Decimal("1000.00"),
            "provider": PaymentProviderType.PAYSTACK,
            "currency": "NGN"
        }

    @pytest.mark.asyncio
    async def test_create_transaction_success(self, transaction_repository, sample_transaction_data):
        """Test successful transaction creation."""
        # Mock the create method
        mock_transaction = Transaction(
            id=1,
            reference_id="TXN-20240101120000-123",
            status=TransactionStatus.PENDING,
            **sample_transaction_data
        )
        transaction_repository.create = AsyncMock(return_value=mock_transaction)

        # Test transaction creation
        result = await transaction_repository.create_transaction(**sample_transaction_data)

        # Assertions
        assert result.id == 1
        assert result.payment_id == 123
        assert result.type == TransactionType.CHARGE
        assert result.amount == Decimal("1000.00")
        assert result.provider == PaymentProviderType.PAYSTACK
        assert result.currency == "NGN"
        assert result.status == TransactionStatus.PENDING
        assert "TXN-" in result.reference_id

        # Verify create was called with correct data
        transaction_repository.create.assert_called_once()
        call_args = transaction_repository.create.call_args[0][0]
        assert call_args["payment_id"] == 123
        assert call_args["status"] == TransactionStatus.PENDING

    @pytest.mark.asyncio
    async def test_update_transaction_status_success(self, transaction_repository):
        """Test successful transaction status update."""
        # Mock the update method
        mock_transaction = Transaction(
            id=1,
            status=TransactionStatus.COMPLETED,
            provider_transaction_id="TXN_PROVIDER_123",
            provider_response='{"status": "success"}',
            processed_at=datetime.now(timezone.utc)
        )
        transaction_repository.update = AsyncMock(return_value=mock_transaction)

        # Test status update
        result = await transaction_repository.update_transaction_status(
            transaction_id=1,
            status=TransactionStatus.COMPLETED,
            provider_transaction_id="TXN_PROVIDER_123",
            provider_response='{"status": "success"}'
        )

        # Assertions
        assert result.id == 1
        assert result.status == TransactionStatus.COMPLETED
        assert result.provider_transaction_id == "TXN_PROVIDER_123"
        assert result.provider_response == '{"status": "success"}'
        assert result.processed_at is not None

        # Verify update was called with correct data
        call_args = transaction_repository.update.call_args[0][1]
        assert call_args["status"] == TransactionStatus.COMPLETED
        assert call_args["provider_transaction_id"] == "TXN_PROVIDER_123"
        assert "processed_at" in call_args

    @pytest.mark.asyncio
    async def test_get_transactions_by_payment(self, transaction_repository):
        """Test getting transactions by payment ID."""
        # Mock database execution
        mock_transactions = [
            Transaction(id=1, payment_id=123, type=TransactionType.CHARGE),
            Transaction(id=2, payment_id=123, type=TransactionType.REFUND)
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_transactions
        transaction_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting transactions by payment
        result = await transaction_repository.get_transactions_by_payment(
            payment_id=123,
            transaction_type=TransactionType.CHARGE
        )

        # Assertions
        assert len(result) == 2
        assert all(txn.payment_id == 123 for txn in result)

        # Verify database was queried
        transaction_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_unreconciled_transactions(self, transaction_repository):
        """Test getting unreconciled transactions."""
        # Mock database execution
        mock_transactions = [
            Transaction(id=1, reconciled=False, status=TransactionStatus.COMPLETED),
            Transaction(id=2, reconciled=False, status=TransactionStatus.COMPLETED)
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_transactions
        transaction_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting unreconciled transactions
        result = await transaction_repository.get_unreconciled_transactions(
            provider=PaymentProviderType.PAYSTACK,
            limit=100
        )

        # Assertions
        assert len(result) == 2
        assert all(not txn.reconciled for txn in result)
        assert all(txn.status == TransactionStatus.COMPLETED for txn in result)

        # Verify database was queried
        transaction_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_transactions_reconciled(self, transaction_repository):
        """Test marking transactions as reconciled."""
        # Mock database execution
        mock_result = MagicMock()
        mock_result.rowcount = 5
        transaction_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test marking transactions as reconciled
        result = await transaction_repository.mark_transactions_reconciled(
            transaction_ids=[1, 2, 3, 4, 5],
            reconciliation_reference="REC-20240101-001"
        )

        # Assertions
        assert result == 5

        # Verify database was updated
        transaction_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_transaction_analytics(self, transaction_repository):
        """Test getting transaction analytics."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_transactions = 100
        mock_row.total_amount = Decimal("50000.00")
        mock_row.total_provider_fees = Decimal("1500.00")
        mock_row.total_platform_fees = Decimal("2500.00")
        mock_row.completed_transactions = 85
        mock_row.failed_transactions = 10
        mock_row.charge_transactions = 90
        mock_row.refund_transactions = 10
        mock_row.reconciled_transactions = 80
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        transaction_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting transaction analytics
        result = await transaction_repository.get_transaction_analytics(
            provider=PaymentProviderType.PAYSTACK,
            currency="NGN"
        )

        # Assertions
        assert result["total_transactions"] == 100
        assert result["total_amount"] == 50000.00
        assert result["total_provider_fees"] == 1500.00
        assert result["total_platform_fees"] == 2500.00
        assert result["completed_transactions"] == 85
        assert result["failed_transactions"] == 10
        assert result["charge_transactions"] == 90
        assert result["refund_transactions"] == 10
        assert result["reconciled_transactions"] == 80
        assert result["success_rate"] == 85.0  # 85/100 * 100
        assert result["reconciliation_rate"] == 94.12  # 80/85 * 100 (approximately)
        assert result["currency"] == "NGN"

        # Verify database was queried
        transaction_repository.db.execute.assert_called_once()


class TestTransactionEventRepository:
    """Test TransactionEventRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def transaction_event_repository(self, mock_db_session):
        """Create TransactionEventRepository instance with mock session."""
        return TransactionEventRepository(mock_db_session)

    @pytest.fixture
    def sample_event_data(self):
        """Sample transaction event data for testing."""
        return {
            "transaction_id": 123,
            "event_type": TransactionEventType.CREATED,
            "event_source": "api",
            "event_data": {"user_id": 456, "amount": "1000.00"},
            "previous_status": None,
            "new_status": "pending"
        }

    @pytest.mark.asyncio
    async def test_create_transaction_event_success(self, transaction_event_repository, sample_event_data):
        """Test successful transaction event creation."""
        # Mock the create method
        mock_event = TransactionEvent(
            id=1,
            **sample_event_data
        )
        transaction_event_repository.create = AsyncMock(return_value=mock_event)

        # Test event creation
        result = await transaction_event_repository.create_transaction_event(**sample_event_data)

        # Assertions
        assert result.id == 1
        assert result.transaction_id == 123
        assert result.event_type == TransactionEventType.CREATED
        assert result.event_source == "api"
        assert result.event_data == {"user_id": 456, "amount": "1000.00"}
        assert result.previous_status is None
        assert result.new_status == "pending"

        # Verify create was called with correct data
        transaction_event_repository.create.assert_called_once()
        call_args = transaction_event_repository.create.call_args[0][0]
        assert call_args["transaction_id"] == 123
        assert call_args["event_type"] == TransactionEventType.CREATED

    @pytest.mark.asyncio
    async def test_get_transaction_events(self, transaction_event_repository):
        """Test getting transaction events."""
        # Mock database execution
        mock_events = [
            TransactionEvent(id=1, transaction_id=123, event_type=TransactionEventType.CREATED),
            TransactionEvent(id=2, transaction_id=123, event_type=TransactionEventType.PROCESSING),
            TransactionEvent(id=3, transaction_id=123, event_type=TransactionEventType.COMPLETED)
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_events
        transaction_event_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting transaction events
        result = await transaction_event_repository.get_transaction_events(
            transaction_id=123,
            event_type=TransactionEventType.CREATED
        )

        # Assertions
        assert len(result) == 3
        assert all(event.transaction_id == 123 for event in result)

        # Verify database was queried
        transaction_event_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_webhook_events(self, transaction_event_repository):
        """Test getting webhook events."""
        # Mock database execution
        mock_events = [
            TransactionEvent(
                id=1,
                event_type=TransactionEventType.WEBHOOK_RECEIVED,
                provider_event_id="webhook_123"
            ),
            TransactionEvent(
                id=2,
                event_type=TransactionEventType.WEBHOOK_RECEIVED,
                provider_event_id="webhook_456"
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_events
        transaction_event_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting webhook events
        result = await transaction_event_repository.get_webhook_events(
            provider_event_id="webhook_123",
            limit=100
        )

        # Assertions
        assert len(result) == 2
        assert all(event.event_type == TransactionEventType.WEBHOOK_RECEIVED for event in result)

        # Verify database was queried
        transaction_event_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_performance_metrics(self, transaction_event_repository):
        """Test getting performance metrics."""
        # Mock database execution
        mock_row = MagicMock()
        mock_row.total_events = 1000
        mock_row.avg_processing_time = 150.5
        mock_row.max_processing_time = 500
        mock_row.min_processing_time = 50
        mock_row.error_events = 25
        mock_row.webhook_events = 300
        mock_row.completion_events = 800
        
        mock_result = MagicMock()
        mock_result.first.return_value = mock_row
        transaction_event_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting performance metrics
        result = await transaction_event_repository.get_performance_metrics(
            event_source="webhook"
        )

        # Assertions
        assert result["total_events"] == 1000
        assert result["avg_processing_time_ms"] == 150.5
        assert result["max_processing_time_ms"] == 500
        assert result["min_processing_time_ms"] == 50
        assert result["error_events"] == 25
        assert result["webhook_events"] == 300
        assert result["completion_events"] == 800
        assert result["error_rate"] == 2.5  # 25/1000 * 100
        assert result["webhook_rate"] == 30.0  # 300/1000 * 100

        # Verify database was queried
        transaction_event_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_transaction_event_failure(self, transaction_event_repository, sample_event_data):
        """Test transaction event creation failure handling."""
        # Mock create method to raise exception
        transaction_event_repository.create = AsyncMock(side_effect=Exception("Database error"))

        # Test event creation failure
        with pytest.raises(RepositoryError) as exc_info:
            await transaction_event_repository.create_transaction_event(**sample_event_data)

        assert "Failed to create transaction event" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_transaction_events_empty_result(self, transaction_event_repository):
        """Test getting transaction events with empty result."""
        # Mock database execution with empty result
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        transaction_event_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting transaction events
        result = await transaction_event_repository.get_transaction_events(transaction_id=999)

        # Assertions
        assert len(result) == 0

        # Verify database was queried
        transaction_event_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_webhook_events_with_date_filter(self, transaction_event_repository):
        """Test getting webhook events with date filtering."""
        # Mock database execution
        mock_events = [
            TransactionEvent(
                id=1,
                event_type=TransactionEventType.WEBHOOK_RECEIVED,
                created_at=datetime.now(timezone.utc)
            )
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_events
        transaction_event_repository.db.execute = AsyncMock(return_value=mock_result)

        # Test getting webhook events with date filter
        date_from = datetime.now(timezone.utc) - timedelta(days=7)
        date_to = datetime.now(timezone.utc)
        
        result = await transaction_event_repository.get_webhook_events(
            date_from=date_from,
            date_to=date_to,
            limit=50
        )

        # Assertions
        assert len(result) == 1
        assert result[0].event_type == TransactionEventType.WEBHOOK_RECEIVED

        # Verify database was queried
        transaction_event_repository.db.execute.assert_called_once()
