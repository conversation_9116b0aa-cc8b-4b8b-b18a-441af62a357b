"""
Unit tests for availability models.

This module provides comprehensive unit tests for availability model classes:
- VendorAvailability: Vendor availability configuration with timezone support
- RecurringAvailability: Recurring pattern management with validation
- AvailabilitySlot: Individual slot management with capacity tracking
- AvailabilityException: Exception handling for pattern overrides

Implements Task 4.1.2 Phase 6 requirements with >85% test coverage.
"""

import pytest
from datetime import datetime, date, time, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import MagicMock

from sqlalchemy.exc import IntegrityError
import pytz

from app.models.availability import (
    VendorAvailability, RecurringAvailability, AvailabilitySlot, AvailabilityException
)


class TestVendorAvailability:
    """Test cases for VendorAvailability model."""

    @pytest.fixture
    def vendor_availability_data(self):
        """Sample vendor availability data."""
        return {
            "vendor_id": 1,
            "service_id": None,
            "timezone": "America/New_York",
            "advance_booking_days": 90,
            "min_booking_notice_hours": 24,
            "max_booking_notice_days": 30,
            "default_slot_duration_minutes": 60,
            "buffer_time_minutes": 15,
            "earliest_booking_time": time(9, 0),
            "latest_booking_time": time(17, 0),
            "is_active": True
        }

    def test_vendor_availability_creation(self, vendor_availability_data):
        """Test successful vendor availability creation."""
        availability = VendorAvailability(**vendor_availability_data)

        assert availability.vendor_id == 1
        assert availability.service_id is None
        assert availability.timezone == "America/New_York"
        assert availability.advance_booking_days == 90
        assert availability.min_booking_notice_hours == 24
        assert availability.max_booking_notice_days == 30
        assert availability.default_slot_duration_minutes == 60
        assert availability.buffer_time_minutes == 15
        assert availability.earliest_booking_time == time(9, 0)
        assert availability.latest_booking_time == time(17, 0)
        assert availability.is_active is True

    def test_vendor_availability_with_service(self, vendor_availability_data):
        """Test vendor availability with specific service."""
        vendor_availability_data["service_id"] = 123
        availability = VendorAvailability(**vendor_availability_data)

        assert availability.service_id == 123

    def test_vendor_availability_timezone_validation(self, vendor_availability_data):
        """Test timezone validation."""
        # Valid timezone
        availability = VendorAvailability(**vendor_availability_data)
        assert availability.timezone == "America/New_York"

        # Test with different valid timezones
        vendor_availability_data["timezone"] = "Europe/London"
        availability = VendorAvailability(**vendor_availability_data)
        assert availability.timezone == "Europe/London"

    def test_vendor_availability_time_range_validation(self, vendor_availability_data):
        """Test time range validation."""
        # Valid time range
        availability = VendorAvailability(**vendor_availability_data)
        assert availability.earliest_booking_time < availability.latest_booking_time

        # Test edge case - same time (should be allowed for 24/7 operations)
        vendor_availability_data["earliest_booking_time"] = time(0, 0)
        vendor_availability_data["latest_booking_time"] = time(23, 59)
        availability = VendorAvailability(**vendor_availability_data)
        assert availability.earliest_booking_time < availability.latest_booking_time

    def test_vendor_availability_booking_notice_validation(self, vendor_availability_data):
        """Test booking notice period validation."""
        availability = VendorAvailability(**vendor_availability_data)

        # Verify notice periods are reasonable
        assert availability.min_booking_notice_hours > 0
        assert availability.max_booking_notice_days > 0
        assert availability.advance_booking_days >= availability.max_booking_notice_days

    def test_vendor_availability_defaults(self):
        """Test default values for vendor availability."""
        minimal_data = {
            "vendor_id": 1,
            "timezone": "UTC"
        }
        availability = VendorAvailability(**minimal_data)

        assert availability.vendor_id == 1
        assert availability.timezone == "UTC"
        assert availability.service_id is None
        # Note: is_active defaults are set by SQLAlchemy, not in constructor


class TestRecurringAvailability:
    """Test cases for RecurringAvailability model."""

    @pytest.fixture
    def recurring_availability_data(self):
        """Sample recurring availability data."""
        return {
            "vendor_availability_id": 1,
            "pattern_type": "weekly",
            "day_of_week": 1,  # Monday
            "start_time": time(10, 0),
            "end_time": time(16, 0),
            "slot_duration_minutes": 60,
            "max_bookings_per_slot": 2,
            "valid_from": date.today() + timedelta(days=1),
            "auto_generate": True,
            "is_active": True
        }

    def test_recurring_availability_creation(self, recurring_availability_data):
        """Test successful recurring availability creation."""
        pattern = RecurringAvailability(**recurring_availability_data)

        assert pattern.vendor_availability_id == 1
        assert pattern.pattern_type == "weekly"
        assert pattern.day_of_week == 1
        assert pattern.start_time == time(10, 0)
        assert pattern.end_time == time(16, 0)
        assert pattern.slot_duration_minutes == 60
        assert pattern.max_bookings_per_slot == 2
        assert pattern.auto_generate is True
        assert pattern.is_active is True

    def test_daily_pattern(self, recurring_availability_data):
        """Test daily recurring pattern."""
        recurring_availability_data["pattern_type"] = "daily"
        recurring_availability_data["day_of_week"] = None

        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.pattern_type == "daily"
        assert pattern.day_of_week is None

    def test_monthly_pattern(self, recurring_availability_data):
        """Test monthly recurring pattern."""
        recurring_availability_data["pattern_type"] = "monthly"
        recurring_availability_data["day_of_month"] = 15
        recurring_availability_data["day_of_week"] = None

        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.pattern_type == "monthly"
        assert pattern.day_of_month == 15
        assert pattern.day_of_week is None

    def test_nth_weekday_monthly_pattern(self, recurring_availability_data):
        """Test Nth weekday of month pattern."""
        recurring_availability_data["pattern_type"] = "monthly"
        recurring_availability_data["day_of_week"] = 1  # Monday
        recurring_availability_data["day_of_month"] = None

        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.pattern_type == "monthly"
        assert pattern.day_of_week == 1
        assert pattern.day_of_month is None

    def test_time_range_validation(self, recurring_availability_data):
        """Test time range validation."""
        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.start_time < pattern.end_time

    def test_slot_duration_validation(self, recurring_availability_data):
        """Test slot duration validation."""
        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.slot_duration_minutes > 0
        assert pattern.slot_duration_minutes <= 480  # Max 8 hours

    def test_capacity_validation(self, recurring_availability_data):
        """Test booking capacity validation."""
        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.max_bookings_per_slot > 0
        assert pattern.max_bookings_per_slot <= 100  # Reasonable limit

    def test_valid_date_range(self, recurring_availability_data):
        """Test valid date range."""
        future_date = date.today() + timedelta(days=30)
        recurring_availability_data["valid_until"] = future_date

        pattern = RecurringAvailability(**recurring_availability_data)
        assert pattern.valid_from < pattern.valid_until


class TestAvailabilitySlot:
    """Test cases for AvailabilitySlot model."""

    @pytest.fixture
    def availability_slot_data(self):
        """Sample availability slot data."""
        return {
            "vendor_availability_id": 1,
            "date": date.today() + timedelta(days=1),
            "start_time": time(10, 0),
            "end_time": time(11, 0),
            "max_bookings": 2,
            "current_bookings": 0,
            "is_available": True
        }

    def test_availability_slot_creation(self, availability_slot_data):
        """Test successful availability slot creation."""
        slot = AvailabilitySlot(**availability_slot_data)

        assert slot.vendor_availability_id == 1
        assert slot.date == date.today() + timedelta(days=1)
        assert slot.start_time == time(10, 0)
        assert slot.end_time == time(11, 0)
        assert slot.max_bookings == 2
        assert slot.current_bookings == 0
        assert slot.is_available is True

    def test_slot_capacity_tracking(self, availability_slot_data):
        """Test slot capacity tracking."""
        slot = AvailabilitySlot(**availability_slot_data)

        # Test initial state
        assert slot.current_bookings == 0
        assert slot.max_bookings == 2

        # Test booking increment
        availability_slot_data["current_bookings"] = 1
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.current_bookings == 1

        # Test full capacity
        availability_slot_data["current_bookings"] = 2
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.current_bookings == slot.max_bookings

    def test_slot_availability_status(self, availability_slot_data):
        """Test slot availability status."""
        # Available slot
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.is_available is True

        # Unavailable slot
        availability_slot_data["is_available"] = False
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.is_available is False

    def test_slot_time_validation(self, availability_slot_data):
        """Test slot time validation."""
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.start_time < slot.end_time

    def test_slot_future_date_validation(self, availability_slot_data):
        """Test future date validation."""
        # Future date (valid)
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.date > date.today()

        # Today (should be allowed)
        availability_slot_data["date"] = date.today()
        slot = AvailabilitySlot(**availability_slot_data)
        assert slot.date >= date.today()

    def test_slot_capacity_constraints(self, availability_slot_data):
        """Test capacity constraints."""
        slot = AvailabilitySlot(**availability_slot_data)

        # Current bookings should not exceed max bookings
        assert slot.current_bookings <= slot.max_bookings

        # Max bookings should be positive
        assert slot.max_bookings > 0


class TestAvailabilityException:
    """Test cases for AvailabilityException model."""

    @pytest.fixture
    def availability_exception_data(self):
        """Sample availability exception data."""
        return {
            "vendor_availability_id": 1,
            "recurring_availability_id": 1,
            "exception_date": date.today() + timedelta(days=7),
            "exception_type": "unavailable",
            "reason": "Vendor holiday",
            "is_active": True
        }

    def test_availability_exception_creation(self, availability_exception_data):
        """Test successful availability exception creation."""
        exception = AvailabilityException(**availability_exception_data)

        assert exception.vendor_availability_id == 1
        assert exception.recurring_availability_id == 1
        assert exception.exception_date == date.today() + timedelta(days=7)
        assert exception.exception_type == "unavailable"
        assert exception.reason == "Vendor holiday"
        assert exception.is_active is True

    def test_exception_types(self, availability_exception_data):
        """Test different exception types."""
        # Unavailable exception
        exception = AvailabilityException(**availability_exception_data)
        assert exception.exception_type == "unavailable"

        # Modified exception
        availability_exception_data["exception_type"] = "modified"
        availability_exception_data["modified_start_time"] = time(11, 0)
        availability_exception_data["modified_end_time"] = time(15, 0)

        exception = AvailabilityException(**availability_exception_data)
        assert exception.exception_type == "modified"
        assert exception.modified_start_time == time(11, 0)
        assert exception.modified_end_time == time(15, 0)

    def test_exception_without_recurring_pattern(self, availability_exception_data):
        """Test exception without specific recurring pattern."""
        availability_exception_data["recurring_availability_id"] = None

        exception = AvailabilityException(**availability_exception_data)
        assert exception.recurring_availability_id is None
        assert exception.vendor_availability_id == 1

    def test_exception_date_validation(self, availability_exception_data):
        """Test exception date validation."""
        exception = AvailabilityException(**availability_exception_data)

        # Exception date should be in the future or today
        assert exception.exception_date >= date.today()

    def test_modified_exception_time_validation(self, availability_exception_data):
        """Test modified exception time validation."""
        availability_exception_data["exception_type"] = "modified"
        availability_exception_data["modified_start_time"] = time(11, 0)
        availability_exception_data["modified_end_time"] = time(15, 0)

        exception = AvailabilityException(**availability_exception_data)

        if exception.modified_start_time and exception.modified_end_time:
            assert exception.modified_start_time < exception.modified_end_time

    def test_exception_reason_tracking(self, availability_exception_data):
        """Test exception reason tracking."""
        exception = AvailabilityException(**availability_exception_data)
        assert exception.reason == "Vendor holiday"

        # Test with different reasons
        availability_exception_data["reason"] = "Equipment maintenance"
        exception = AvailabilityException(**availability_exception_data)
        assert exception.reason == "Equipment maintenance"
