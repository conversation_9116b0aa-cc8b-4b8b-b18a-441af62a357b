# Culture Connect Backend API Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
CC_ENVIRONMENT=development
CC_SECRET_KEY=your-super-secret-key-here-at-least-32-characters-long
CC_ACCESS_TOKEN_EXPIRE_MINUTES=30
CC_REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Settings
CC_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/culture_connect_db
CC_DATABASE_POOL_SIZE=10
CC_DATABASE_MAX_OVERFLOW=20
CC_DATABASE_POOL_TIMEOUT=30

# Redis Settings
CC_REDIS_URL=redis://localhost:6379/0
CC_REDIS_PASSWORD=
CC_REDIS_DB=0

# Celery Settings
CC_CELERY_BROKER_URL=redis://localhost:6379/1
CC_CELERY_RESULT_BACKEND=redis://localhost:6379/1

# CORS Settings
CC_BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:3001

# Payment Provider Settings
# TODO-PAYMENT-PROVIDER-CONFIG: Configure all payment provider settings for production deployment
# See app/core/payment/config.py for comprehensive payment provider configuration management

# Paystack (Africa) - TODO-PAYSTACK-CONFIG: Production Paystack configuration
# TODO-PAYSTACK-API-URL: Verify production Paystack API endpoint (https://api.paystack.co)
# TODO-PAYSTACK-WEBHOOK-URL: Configure production webhook endpoint URL
CC_PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
CC_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
CC_PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret

# Stripe (Diaspora) - TODO-STRIPE-CONFIG: Production Stripe configuration
# TODO-STRIPE-API-URL: Verify production Stripe API endpoint (https://api.stripe.com)
# TODO-STRIPE-WEBHOOK-URL: Configure production webhook endpoint URL
# TODO-STRIPE-API-VERSION: Verify latest Stripe API version for production
CC_STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
CC_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
CC_STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Cryptocurrency Settings (Busha.co) - TODO-BUSHA-CONFIG: Production Busha configuration
# TODO-BUSHA-API-URL: Verify production Busha API endpoint (https://api.busha.co)
# TODO-BUSHA-WEBHOOK-URL: Configure production webhook endpoint URL
# TODO-BUSHA-API-VERSION: Verify latest Busha API version for production
CC_BUSHA_API_KEY=your_busha_api_key
CC_BUSHA_SECRET_KEY=your_busha_secret_key
CC_BUSHA_WEBHOOK_SECRET=your_busha_webhook_secret

# AI/ML API Settings
CC_AIML_API_KEY=your_aiml_api_key
CC_AIML_BASE_URL=https://api.aimlapi.com/v1

# Geo-location Settings
CC_GEOIP_DATABASE_PATH=./data/GeoLite2-Country.mmdb

# File Storage Settings
CC_UPLOAD_DIR=./uploads
CC_MAX_FILE_SIZE=10485760
CC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# AWS S3 Settings (for production)
CC_AWS_ACCESS_KEY_ID=your_aws_access_key_id
CC_AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
CC_AWS_S3_BUCKET=your_s3_bucket_name
CC_AWS_S3_REGION=us-east-1

# Email Settings
CC_SMTP_HOST=localhost
CC_SMTP_PORT=587
CC_SMTP_USERNAME=your_smtp_username
CC_SMTP_PASSWORD=your_smtp_password
CC_SMTP_USE_TLS=true
CC_FROM_EMAIL=<EMAIL>
CC_FROM_NAME=Culture Connect

# Rate Limiting Settings
CC_RATE_LIMIT_PER_MINUTE=60
CC_RATE_LIMIT_BURST=100

# Logging Settings
CC_LOG_LEVEL=INFO
CC_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
CC_LOG_FILE=
CC_LOG_ROTATION=1 day
CC_LOG_RETENTION=30 days

# Monitoring Settings
CC_SENTRY_DSN=your_sentry_dsn_url
CC_SENTRY_ENVIRONMENT=
CC_SENTRY_TRACES_SAMPLE_RATE=0.1

# Performance Settings
CC_REQUEST_TIMEOUT=30
CC_MAX_CONNECTIONS=100
CC_KEEPALIVE_TIMEOUT=5

# Security Settings (Enhanced)
CC_BCRYPT_ROUNDS=12
CC_SESSION_TIMEOUT=3600
CC_MAX_LOGIN_ATTEMPTS=5
CC_LOCKOUT_DURATION=900

# API Settings
CC_API_TITLE=Culture Connect Backend API
CC_API_DESCRIPTION=Culture Connect Backend API - Connecting cultures through authentic experiences
CC_API_VERSION=1.0.0
CC_DOCS_URL=/docs
CC_REDOC_URL=/redoc
CC_OPENAPI_URL=/openapi.json

# Frontend Settings
CC_FRONTEND_URL=http://localhost:3000
CC_PWA_URL=http://localhost:3001
CC_MOBILE_APP_DEEP_LINK=cultureconnect://

# Feature Flags
CC_ENABLE_PROMOTIONAL_SYSTEM=true
CC_ENABLE_CRYPTO_PAYMENTS=true
CC_ENABLE_AI_OPTIMIZATION=true
CC_ENABLE_CACHING=true
CC_ENABLE_RATE_LIMITING=true
CC_ENABLE_METRICS=true
CC_ENABLE_HEALTH_CHECKS=true

# ===================================================================
# IMPORTANT SECURITY NOTES FOR PRODUCTION:
# ===================================================================
# 1. Generate a strong SECRET_KEY (at least 32 characters)
# 2. Use real API keys from payment providers (Paystack, Stripe, Busha)
# 3. Set up proper SMTP credentials for email functionality
# 4. Configure Sentry DSN for error monitoring
# 5. Set ENVIRONMENT=production for production deployment
# 6. Use strong database passwords
# 7. Enable HTTPS in production (update FRONTEND_URL, PWA_URL)
# 8. Review and adjust rate limiting settings based on expected traffic
# ===================================================================