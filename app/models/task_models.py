"""
Task management models for Culture Connect Backend API.

This module provides comprehensive task management models including:
- TaskExecution: Main task execution tracking
- TaskQueue: Queue management and configuration
- TaskSchedule: Scheduled task management
- TaskMetrics: Performance monitoring and analytics
- TaskFailure: Error tracking and analysis

Implements Task 6.2.1 requirements for Celery task queue setup with
production-grade task tracking and monitoring capabilities.
"""

from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Optional, Dict, Any
from uuid import UUID, uuid4

from sqlalchemy import Column, String, DateTime, Integer, Float, Boolean, Text, JSON, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.database import Base


class TaskStatus(str, Enum):
    """Task execution status enumeration."""
    PENDING = "pending"
    STARTED = "started"
    RETRY = "retry"
    SUCCESS = "success"
    FAILURE = "failure"
    REVOKED = "revoked"
    IGNORED = "ignored"


class TaskPriority(str, Enum):
    """Task priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    STANDARD = "standard"
    LOW = "low"


class QueueType(str, Enum):
    """Queue type classification."""
    EMAIL = "email"
    NOTIFICATION = "notification"
    REPORT = "report"
    SYNC = "sync"
    CLEANUP = "cleanup"
    ANALYTICS = "analytics"
    PAYMENT = "payment"
    GENERAL = "general"


class TaskExecution(Base):
    """
    Task execution tracking model.
    
    Tracks individual task executions with comprehensive metadata,
    performance metrics, and error handling information.
    """
    __tablename__ = "task_executions"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_id = Column(String(255), unique=True, nullable=False, index=True)
    task_name = Column(String(255), nullable=False, index=True)
    queue_name = Column(String(100), nullable=False, index=True)
    
    # Task metadata
    priority = Column(String(20), nullable=False, default=TaskPriority.STANDARD)
    status = Column(String(20), nullable=False, default=TaskStatus.PENDING, index=True)
    
    # Execution details
    args = Column(JSONB, nullable=True)
    kwargs = Column(JSONB, nullable=True)
    result = Column(JSONB, nullable=True)
    
    # Timing information
    submitted_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    eta = Column(DateTime(timezone=True), nullable=True)
    
    # Performance metrics
    execution_time_ms = Column(Float, nullable=True)
    memory_usage_mb = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    
    # Error handling
    retry_count = Column(Integer, nullable=False, default=0)
    max_retries = Column(Integer, nullable=False, default=3)
    error_message = Column(Text, nullable=True)
    error_traceback = Column(Text, nullable=True)
    
    # Worker information
    worker_name = Column(String(255), nullable=True)
    worker_pid = Column(Integer, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    
    # Relationships
    failures = relationship("TaskFailure", back_populates="task_execution", cascade="all, delete-orphan")
    metrics = relationship("TaskMetrics", back_populates="task_execution", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index('idx_task_executions_status_priority', 'status', 'priority'),
        Index('idx_task_executions_queue_status', 'queue_name', 'status'),
        Index('idx_task_executions_submitted_at', 'submitted_at'),
        Index('idx_task_executions_task_name_status', 'task_name', 'status'),
    )

    def __repr__(self):
        return f"<TaskExecution(id={self.id}, task_name={self.task_name}, status={self.status})>"


class TaskQueue(Base):
    """
    Task queue configuration and management model.
    
    Manages queue configurations, routing rules, and performance settings.
    """
    __tablename__ = "task_queues"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(100), unique=True, nullable=False, index=True)
    queue_type = Column(String(50), nullable=False, default=QueueType.GENERAL)
    
    # Queue configuration
    max_workers = Column(Integer, nullable=False, default=4)
    prefetch_multiplier = Column(Integer, nullable=False, default=1)
    max_tasks_per_child = Column(Integer, nullable=False, default=1000)
    
    # Performance settings
    task_time_limit = Column(Integer, nullable=False, default=1800)  # 30 minutes
    task_soft_time_limit = Column(Integer, nullable=False, default=1500)  # 25 minutes
    
    # Queue status
    is_active = Column(Boolean, nullable=False, default=True)
    is_paused = Column(Boolean, nullable=False, default=False)
    
    # Routing configuration
    routing_key = Column(String(255), nullable=True)
    exchange = Column(String(255), nullable=True)
    
    # Monitoring
    total_tasks_processed = Column(Integer, nullable=False, default=0)
    total_failures = Column(Integer, nullable=False, default=0)
    average_execution_time_ms = Column(Float, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<TaskQueue(name={self.name}, type={self.queue_type}, active={self.is_active})>"


class TaskSchedule(Base):
    """
    Scheduled task management model.
    
    Manages periodic and scheduled task execution with cron-like functionality.
    """
    __tablename__ = "task_schedules"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), unique=True, nullable=False, index=True)
    task_name = Column(String(255), nullable=False)
    
    # Schedule configuration
    cron_expression = Column(String(100), nullable=True)  # e.g., "0 0 * * *" for daily at midnight
    interval_seconds = Column(Integer, nullable=True)  # Alternative to cron for simple intervals
    
    # Task configuration
    queue_name = Column(String(100), nullable=False, default="default")
    priority = Column(String(20), nullable=False, default=TaskPriority.STANDARD)
    args = Column(JSONB, nullable=True)
    kwargs = Column(JSONB, nullable=True)
    
    # Schedule status
    is_enabled = Column(Boolean, nullable=False, default=True)
    last_run_at = Column(DateTime(timezone=True), nullable=True)
    next_run_at = Column(DateTime(timezone=True), nullable=True)
    
    # Execution tracking
    total_runs = Column(Integer, nullable=False, default=0)
    successful_runs = Column(Integer, nullable=False, default=0)
    failed_runs = Column(Integer, nullable=False, default=0)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<TaskSchedule(name={self.name}, task_name={self.task_name}, enabled={self.is_enabled})>"


class TaskMetrics(Base):
    """
    Task performance metrics model.
    
    Stores detailed performance metrics for task execution analysis.
    """
    __tablename__ = "task_metrics"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_execution_id = Column(PostgresUUID(as_uuid=True), ForeignKey("task_executions.id"), nullable=False)
    
    # Performance metrics
    queue_wait_time_ms = Column(Float, nullable=True)
    execution_time_ms = Column(Float, nullable=True)
    memory_peak_mb = Column(Float, nullable=True)
    memory_average_mb = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    
    # I/O metrics
    disk_read_mb = Column(Float, nullable=True)
    disk_write_mb = Column(Float, nullable=True)
    network_in_mb = Column(Float, nullable=True)
    network_out_mb = Column(Float, nullable=True)
    
    # Database metrics
    db_queries_count = Column(Integer, nullable=True)
    db_query_time_ms = Column(Float, nullable=True)
    
    # Cache metrics
    cache_hits = Column(Integer, nullable=True)
    cache_misses = Column(Integer, nullable=True)
    
    # Custom metrics
    custom_metrics = Column(JSONB, nullable=True)
    
    # Timestamp
    recorded_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="metrics")

    def __repr__(self):
        return f"<TaskMetrics(task_execution_id={self.task_execution_id}, execution_time_ms={self.execution_time_ms})>"


class TaskFailure(Base):
    """
    Task failure tracking model.
    
    Tracks task failures with detailed error information for debugging and analysis.
    """
    __tablename__ = "task_failures"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    task_execution_id = Column(PostgresUUID(as_uuid=True), ForeignKey("task_executions.id"), nullable=False)
    
    # Failure details
    exception_type = Column(String(255), nullable=False)
    exception_message = Column(Text, nullable=False)
    exception_traceback = Column(Text, nullable=True)
    
    # Context information
    retry_attempt = Column(Integer, nullable=False, default=0)
    worker_name = Column(String(255), nullable=True)
    worker_pid = Column(Integer, nullable=True)
    
    # Environment context
    python_version = Column(String(50), nullable=True)
    celery_version = Column(String(50), nullable=True)
    system_info = Column(JSONB, nullable=True)
    
    # Failure classification
    is_retryable = Column(Boolean, nullable=False, default=True)
    failure_category = Column(String(100), nullable=True)  # e.g., "network", "database", "validation"
    
    # Timestamp
    failed_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="failures")

    def __repr__(self):
        return f"<TaskFailure(task_execution_id={self.task_execution_id}, exception_type={self.exception_type})>"
