"""Add email tables for Task 2.3.1

Revision ID: email_tables
Revises: oauth_tables
Create Date: 2025-05-24 22:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'email_tables'
down_revision = 'oauth_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add email tables."""

    # Create email_templates table
    op.create_table('email_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('category', sa.Enum('VERIFICATION', 'PASSWORD_RESET', 'NOTIFICATION', 'MARKETING', 'BOOKING', 'SECURITY', 'SYSTEM', name='emailtemplatecategory'), nullable=False),
        sa.Column('subject_template', sa.Text(), nullable=False),
        sa.Column('body_template', sa.Text(), nullable=False),
        sa.Column('html_template', sa.Text(), nullable=True),
        sa.Column('variables', sa.JSON(), nullable=False, default={}),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id', name='pk_email_templates'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], name='fk_email_templates_created_by_users'),
        sa.UniqueConstraint('name', 'version', name='uq_email_template_name_version')
    )

    # Create indexes for email_templates
    op.create_index('idx_email_template_category_active', 'email_templates', ['category', 'is_active'])
    op.create_index('idx_email_template_name_version', 'email_templates', ['name', 'version'])
    op.create_index('idx_email_template_name', 'email_templates', ['name'])
    op.create_index('idx_email_template_category', 'email_templates', ['category'])
    op.create_index('idx_email_template_is_active', 'email_templates', ['is_active'])

    # Create email_deliveries table
    op.create_table('email_deliveries',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('recipient_email', sa.String(length=255), nullable=False),
        sa.Column('subject', sa.String(length=500), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'REJECTED', name='emaildeliverystatus'), nullable=False, default='PENDING'),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('opened_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('email_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('correlation_id', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id', name='pk_email_deliveries'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_email_deliveries_user_id_users'),
        sa.ForeignKeyConstraint(['template_id'], ['email_templates.id'], name='fk_email_deliveries_template_id_email_templates')
    )

    # Create indexes for email_deliveries
    op.create_index('idx_email_delivery_user_status', 'email_deliveries', ['user_id', 'status'])
    op.create_index('idx_email_delivery_created_status', 'email_deliveries', ['created_at', 'status'])
    op.create_index('idx_email_delivery_recipient_status', 'email_deliveries', ['recipient_email', 'status'])
    op.create_index('idx_email_delivery_user_id', 'email_deliveries', ['user_id'])
    op.create_index('idx_email_delivery_template_id', 'email_deliveries', ['template_id'])
    op.create_index('idx_email_delivery_status', 'email_deliveries', ['status'])
    op.create_index('idx_email_delivery_correlation_id', 'email_deliveries', ['correlation_id'])
    op.create_index('idx_email_delivery_created_at', 'email_deliveries', ['created_at'])

    # Create email_preferences table
    op.create_table('email_preferences',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('verification_emails', sa.Boolean(), nullable=False, default=True),
        sa.Column('security_emails', sa.Boolean(), nullable=False, default=True),
        sa.Column('marketing_emails', sa.Boolean(), nullable=False, default=False),
        sa.Column('booking_notifications', sa.Boolean(), nullable=False, default=True),
        sa.Column('vendor_notifications', sa.Boolean(), nullable=False, default=True),
        sa.Column('system_notifications', sa.Boolean(), nullable=False, default=True),
        sa.Column('opted_out_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id', name='pk_email_preferences'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_email_preferences_user_id_users'),
        sa.UniqueConstraint('user_id', name='uq_email_preferences_user_id')
    )

    # Create indexes for email_preferences
    op.create_index('idx_email_preference_user', 'email_preferences', ['user_id'])

    # Create email_queue table
    op.create_table('email_queue',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('recipient_email', sa.String(length=255), nullable=False),
        sa.Column('subject', sa.String(length=500), nullable=False),
        sa.Column('body', sa.Text(), nullable=False),
        sa.Column('html_body', sa.Text(), nullable=True),
        sa.Column('priority', sa.Integer(), nullable=False, default=3),
        sa.Column('scheduled_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('attempts', sa.Integer(), nullable=False, default=0),
        sa.Column('max_attempts', sa.Integer(), nullable=False, default=3),
        sa.Column('status', sa.Enum('QUEUED', 'PROCESSING', 'SENT', 'FAILED', 'CANCELLED', 'RETRY', name='emailqueuestatus'), nullable=False, default='QUEUED'),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('queue_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id', name='pk_email_queue'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_email_queue_user_id_users'),
        sa.ForeignKeyConstraint(['template_id'], ['email_templates.id'], name='fk_email_queue_template_id_email_templates')
    )

    # Create indexes for email_queue
    op.create_index('idx_email_queue_status_priority', 'email_queue', ['status', 'priority'])
    op.create_index('idx_email_queue_scheduled', 'email_queue', ['scheduled_at', 'status'])
    op.create_index('idx_email_queue_attempts', 'email_queue', ['attempts', 'max_attempts', 'status'])
    op.create_index('idx_email_queue_user_id', 'email_queue', ['user_id'])
    op.create_index('idx_email_queue_template_id', 'email_queue', ['template_id'])
    op.create_index('idx_email_queue_status', 'email_queue', ['status'])
    op.create_index('idx_email_queue_priority', 'email_queue', ['priority'])
    op.create_index('idx_email_queue_scheduled_at', 'email_queue', ['scheduled_at'])


def downgrade() -> None:
    """Remove email tables."""

    # Drop tables in reverse order due to foreign key constraints
    op.drop_table('email_queue')
    op.drop_table('email_preferences')
    op.drop_table('email_deliveries')
    op.drop_table('email_templates')
