"""
Test suite for password history functionality.

This module tests password history tracking and validation to prevent
password reuse according to security policies.

Tests cover Task 2.1.2 password history requirements.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.password_security_service import PasswordSecurityService
from app.repositories.password_security_repository import PasswordHistoryRepository
from app.models.password_security import PasswordHistory
from app.core.security import get_password_hash


class TestPasswordHistory:
    """Test password history functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_password_history_repo(self):
        """Create mock password history repository."""
        return Mock(spec=PasswordHistoryRepository)

    @pytest.fixture
    def password_service(self, mock_db_session):
        """Create password security service instance."""
        service = PasswordSecurityService(mock_db_session)
        return service

    @pytest.fixture
    def sample_password_history(self):
        """Create sample password history entries."""
        user_id = 1
        passwords = ["OldP@ss1A!", "OldP@ss2B!", "OldP@ss3C!", "OldP@ss4D!", "CurrentP@ss5E!"]

        history_entries = []
        for i, password in enumerate(passwords):
            entry = Mock(spec=PasswordHistory)
            entry.id = i + 1
            entry.user_id = user_id
            entry.password_hash = get_password_hash(password)
            entry.created_at = datetime.utcnow()
            history_entries.append(entry)

        return history_entries

    @pytest.mark.asyncio
    async def test_check_password_history_no_reuse(self, password_service, sample_password_history):
        """Test password history check when password is not reused."""
        user_id = 1
        new_password = "NewUniqueP@ssw0rd!"

        # Mock repository to return sample history
        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = sample_password_history

            is_reused = await password_service.check_password_history(user_id, new_password)

            assert is_reused is False
            mock_get_history.assert_called_once_with(user_id, 5)

    @pytest.mark.asyncio
    async def test_check_password_history_with_reuse(self, password_service, sample_password_history):
        """Test password history check when password is reused."""
        user_id = 1
        reused_password = "OldP@ss2B!"  # This password exists in history

        # Mock repository to return sample history
        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = sample_password_history

            is_reused = await password_service.check_password_history(user_id, reused_password)

            assert is_reused is True
            mock_get_history.assert_called_once_with(user_id, 5)

    @pytest.mark.asyncio
    async def test_check_password_history_empty_history(self, password_service):
        """Test password history check with empty history."""
        user_id = 1
        new_password = "FirstP@ssw0rd!"

        # Mock repository to return empty history
        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = []

            is_reused = await password_service.check_password_history(user_id, new_password)

            assert is_reused is False
            mock_get_history.assert_called_once_with(user_id, 5)

    @pytest.mark.asyncio
    async def test_add_password_to_history_success(self, password_service):
        """Test successfully adding password to history."""
        user_id = 1
        password_hash = get_password_hash("NewP@ssw0rd!")

        # Mock repository method
        with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
            mock_add.return_value = Mock(spec=PasswordHistory)

            result = await password_service.add_password_to_history(user_id, password_hash)

            assert result is True
            mock_add.assert_called_once_with(user_id, password_hash)

    @pytest.mark.asyncio
    async def test_add_password_to_history_failure(self, password_service):
        """Test handling failure when adding password to history."""
        user_id = 1
        password_hash = get_password_hash("NewP@ssw0rd!")

        # Mock repository to raise exception
        with patch.object(password_service.password_history_repo, 'add_password_to_history') as mock_add:
            mock_add.side_effect = Exception("Database error")

            result = await password_service.add_password_to_history(user_id, password_hash)

            assert result is False
            mock_add.assert_called_once_with(user_id, password_hash)

    @pytest.mark.asyncio
    async def test_check_password_history_error_handling(self, password_service):
        """Test error handling in password history check."""
        user_id = 1
        new_password = "TestP@ssw0rd!"

        # Mock repository to raise exception
        with patch.object(password_service.password_history_repo, 'check_password_reuse') as mock_check:
            mock_check.side_effect = Exception("Database error")

            # Should return False (allow password) in case of error for security
            is_reused = await password_service.check_password_history(user_id, new_password)

            assert is_reused is False

    @pytest.mark.asyncio
    async def test_password_history_limit_configuration(self, password_service):
        """Test that password history respects configured limit."""
        user_id = 1
        new_password = "TestP@ssw0rd!"

        # Test with different history limits
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.PASSWORD_HISTORY_COUNT = 3

            with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
                mock_get_history.return_value = []

                await password_service.check_password_history(user_id, new_password)

                # Should use configured limit
                mock_get_history.assert_called_once_with(user_id, 3)

    @pytest.mark.asyncio
    async def test_password_hash_comparison_accuracy(self, password_service):
        """Test accuracy of password hash comparison."""
        user_id = 1

        # Create history with specific password
        test_password = "TestP@ssw0rd123!"
        history_entry = Mock(spec=PasswordHistory)
        history_entry.password_hash = get_password_hash(test_password)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = [history_entry]

            # Test exact match
            is_reused_exact = await password_service.check_password_history(user_id, test_password)
            assert is_reused_exact is True

            # Test similar but different password
            is_reused_different = await password_service.check_password_history(user_id, "TestP@ssw0rd124!")
            assert is_reused_different is False

    @pytest.mark.asyncio
    async def test_password_history_case_sensitivity(self, password_service):
        """Test that password history comparison is case sensitive."""
        user_id = 1

        # Create history with specific case
        original_password = "TestP@ssw0rd!"
        history_entry = Mock(spec=PasswordHistory)
        history_entry.password_hash = get_password_hash(original_password)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = [history_entry]

            # Test different case
            is_reused = await password_service.check_password_history(user_id, "testp@ssw0rd!")
            assert is_reused is False

    @pytest.mark.asyncio
    async def test_multiple_users_password_isolation(self, password_service):
        """Test that password history is isolated between users."""
        password = "SharedP@ssw0rd!"

        # User 1 has this password in history
        user1_history = Mock(spec=PasswordHistory)
        user1_history.password_hash = get_password_hash(password)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            # User 1 check - should find reuse
            mock_get_history.return_value = [user1_history]
            is_reused_user1 = await password_service.check_password_history(1, password)
            assert is_reused_user1 is True

            # User 2 check - should not find reuse (empty history)
            mock_get_history.return_value = []
            is_reused_user2 = await password_service.check_password_history(2, password)
            assert is_reused_user2 is False

    @pytest.mark.asyncio
    async def test_password_history_chronological_order(self, password_service):
        """Test that password history considers chronological order."""
        user_id = 1

        # Create history entries with different timestamps
        old_entry = Mock(spec=PasswordHistory)
        old_entry.password_hash = get_password_hash("OldP@ssw0rd!")
        old_entry.created_at = datetime(2024, 1, 1)

        recent_entry = Mock(spec=PasswordHistory)
        recent_entry.password_hash = get_password_hash("RecentP@ssw0rd!")
        recent_entry.created_at = datetime(2024, 5, 1)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            # Repository should return entries in chronological order (newest first)
            mock_get_history.return_value = [recent_entry, old_entry]

            # Both passwords should be detected as reused
            is_old_reused = await password_service.check_password_history(user_id, "OldP@ssw0rd!")
            is_recent_reused = await password_service.check_password_history(user_id, "RecentP@ssw0rd!")

            assert is_old_reused is True
            assert is_recent_reused is True

    @pytest.mark.asyncio
    async def test_password_history_with_special_characters(self, password_service):
        """Test password history with various special characters."""
        user_id = 1

        special_passwords = [
            "P@ssw0rd1!",
            "P#ssw$rd2%",
            "P^ssw&rd3*",
            "P(ssw)rd4_",
            "P+ssw=rd5{",
            "P}ssw|rd6:",
            "P;ssw'rd7\"",
            "P<ssw>rd8,",
            "P.ssw?rd9/"
        ]

        # Create history entries for all special passwords
        history_entries = []
        for password in special_passwords:
            entry = Mock(spec=PasswordHistory)
            entry.password_hash = get_password_hash(password)
            history_entries.append(entry)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = history_entries

            # Test each password for reuse detection
            for password in special_passwords:
                is_reused = await password_service.check_password_history(user_id, password)
                assert is_reused is True, f"Failed to detect reuse for password with special chars: {password}"

    @pytest.mark.asyncio
    async def test_password_history_unicode_support(self, password_service):
        """Test password history with unicode characters."""
        user_id = 1

        unicode_password = "Pässwörd123!ñ"
        history_entry = Mock(spec=PasswordHistory)
        history_entry.password_hash = get_password_hash(unicode_password)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            mock_get_history.return_value = [history_entry]

            is_reused = await password_service.check_password_history(user_id, unicode_password)
            assert is_reused is True

    @pytest.mark.asyncio
    async def test_password_history_performance_with_large_history(self, password_service):
        """Test password history performance with large history."""
        user_id = 1
        new_password = "NewP@ssw0rd!"

        # Create large history (but only last 5 should be checked)
        large_history = []
        for i in range(100):
            entry = Mock(spec=PasswordHistory)
            entry.password_hash = get_password_hash(f"OldP@ss{i}A!")
            large_history.append(entry)

        with patch.object(password_service.password_history_repo, 'get_user_password_history') as mock_get_history:
            # Repository should only return configured limit
            mock_get_history.return_value = large_history[:5]

            is_reused = await password_service.check_password_history(user_id, new_password)

            assert is_reused is False
            mock_get_history.assert_called_once_with(user_id, 5)
