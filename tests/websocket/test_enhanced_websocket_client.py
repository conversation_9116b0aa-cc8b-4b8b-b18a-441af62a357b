"""
WebSocket-specific test infrastructure for enhanced real-time communication system.

This module provides comprehensive WebSocket-specific test infrastructure including:
- WebSocket test clients for connection simulation and message exchange
- Real-time event delivery and acknowledgment testing
- Connection lifecycle testing (connect, authenticate, join rooms, disconnect)
- Broadcasting and participant notification testing with timing validation
- WebSocket connection reliability and message delivery testing

Implements Task 6.1.1 Phase 5 requirements with WebSocket-specific testing capabilities.
"""

import pytest
import asyncio
import json
import time
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, List, Any, Optional
import websockets
from websockets.exceptions import ConnectionClosed

from app.api.v1.endpoints.websocket_endpoints import connection_manager
from app.schemas.websocket_schemas import (
    WebSocketMessage, WebSocketAuthMessage, WebSocketAckMessage,
    EventTypeEnum, EventPriorityEnum
)
from app.models.user import User
from app.core.security import create_access_token


@pytest.mark.websocket
class TestWebSocketClientInfrastructure:
    """WebSocket-specific test infrastructure and client simulation."""

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user for WebSocket testing."""
        user = MagicMock(spec=User)
        user.id = 1
        user.email = "<EMAIL>"
        user.role = "customer"
        return user

    @pytest.fixture
    def auth_token(self, mock_user):
        """Create authentication token for WebSocket testing."""
        return create_access_token(data={"sub": str(mock_user.id)})

    @pytest.fixture
    async def websocket_test_client(self, auth_token):
        """Create WebSocket test client for connection simulation."""
        
        class WebSocketTestClient:
            """Test client for WebSocket connection simulation."""
            
            def __init__(self, auth_token: str):
                self.auth_token = auth_token
                self.connection_id = None
                self.websocket = None
                self.received_messages = []
                self.connection_status = "disconnected"
                self.last_ping_time = None
                self.message_queue = asyncio.Queue()
                
            async def connect(self, endpoint: str = "ws://localhost:8000/ws") -> str:
                """Simulate WebSocket connection establishment."""
                try:
                    # Mock WebSocket connection
                    self.websocket = MagicMock()
                    self.websocket.send = AsyncMock()
                    self.websocket.recv = AsyncMock()
                    self.websocket.close = AsyncMock()
                    self.websocket.ping = AsyncMock()
                    
                    # Generate connection ID
                    self.connection_id = f"test_conn_{int(time.time() * 1000)}"
                    self.connection_status = "connected"
                    
                    return self.connection_id
                    
                except Exception as e:
                    raise ConnectionError(f"Failed to connect: {str(e)}")
            
            async def authenticate(self) -> bool:
                """Simulate WebSocket authentication."""
                if not self.websocket:
                    raise ConnectionError("Not connected")
                
                auth_message = WebSocketAuthMessage(
                    type="auth",
                    token=self.auth_token,
                    timestamp=datetime.now(timezone.utc)
                )
                
                # Mock sending authentication message
                await self.websocket.send(auth_message.model_dump_json())
                
                # Mock receiving authentication response
                auth_response = {
                    "type": "auth_response",
                    "status": "authenticated",
                    "user_id": 1,
                    "connection_id": self.connection_id,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
                self.websocket.recv.return_value = json.dumps(auth_response)
                response = await self.websocket.recv()
                response_data = json.loads(response)
                
                if response_data.get("status") == "authenticated":
                    self.connection_status = "authenticated"
                    return True
                
                return False
            
            async def join_room(self, room_id: str) -> bool:
                """Simulate joining a WebSocket room."""
                if self.connection_status != "authenticated":
                    raise ConnectionError("Not authenticated")
                
                join_message = {
                    "type": "join_room",
                    "room_id": room_id,
                    "connection_id": self.connection_id,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
                # Mock sending join message
                await self.websocket.send(json.dumps(join_message))
                
                # Mock receiving join response
                join_response = {
                    "type": "room_joined",
                    "room_id": room_id,
                    "status": "success",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
                self.websocket.recv.return_value = json.dumps(join_response)
                response = await self.websocket.recv()
                response_data = json.loads(response)
                
                return response_data.get("status") == "success"
            
            async def send_message(self, message_data: Dict[str, Any]) -> bool:
                """Simulate sending a message through WebSocket."""
                if self.connection_status != "authenticated":
                    raise ConnectionError("Not authenticated")
                
                message = WebSocketMessage(
                    type=message_data.get("type", "message"),
                    content=message_data.get("content", ""),
                    room_id=message_data.get("room_id"),
                    sender_id=message_data.get("sender_id", 1),
                    timestamp=datetime.now(timezone.utc)
                )
                
                # Mock sending message
                await self.websocket.send(message.model_dump_json())
                
                # Mock message acknowledgment
                ack_message = WebSocketAckMessage(
                    type="message_ack",
                    message_id=message_data.get("message_id", "test_msg_123"),
                    status="delivered",
                    timestamp=datetime.now(timezone.utc)
                )
                
                self.websocket.recv.return_value = ack_message.model_dump_json()
                return True
            
            async def receive_message(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
                """Simulate receiving a message from WebSocket."""
                try:
                    # Mock receiving message with timeout
                    await asyncio.wait_for(self.message_queue.get(), timeout=timeout)
                    
                    # Mock message data
                    message_data = {
                        "type": "message",
                        "content": "Test message",
                        "sender_id": 2,
                        "room_id": "test_room",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                    
                    self.received_messages.append(message_data)
                    return message_data
                    
                except asyncio.TimeoutError:
                    return None
            
            async def ping(self) -> float:
                """Simulate WebSocket ping for connection health check."""
                if not self.websocket:
                    raise ConnectionError("Not connected")
                
                start_time = time.time()
                await self.websocket.ping()
                
                # Mock pong response
                self.last_ping_time = time.time() - start_time
                return self.last_ping_time * 1000  # Return in milliseconds
            
            async def disconnect(self) -> bool:
                """Simulate WebSocket disconnection."""
                if self.websocket:
                    await self.websocket.close()
                    self.connection_status = "disconnected"
                    self.websocket = None
                    return True
                return False
            
            def get_connection_stats(self) -> Dict[str, Any]:
                """Get connection statistics for testing."""
                return {
                    "connection_id": self.connection_id,
                    "status": self.connection_status,
                    "messages_sent": len([m for m in self.received_messages if m.get("type") == "message"]),
                    "messages_received": len(self.received_messages),
                    "last_ping_ms": self.last_ping_time * 1000 if self.last_ping_time else None
                }
        
        return WebSocketTestClient(auth_token)

    @pytest.mark.asyncio
    async def test_websocket_connection_lifecycle(
        self,
        websocket_test_client,
        performance_timer
    ):
        """Test complete WebSocket connection lifecycle."""
        timer = performance_timer()
        client = websocket_test_client
        
        timer.start()
        
        # Step 1: Connect
        connection_id = await client.connect()
        assert connection_id is not None
        assert client.connection_status == "connected"
        
        # Step 2: Authenticate
        auth_success = await client.authenticate()
        assert auth_success is True
        assert client.connection_status == "authenticated"
        
        # Step 3: Join room
        room_join_success = await client.join_room("test_room_123")
        assert room_join_success is True
        
        # Step 4: Send message
        message_data = {
            "type": "message",
            "content": "Hello WebSocket!",
            "room_id": "test_room_123",
            "sender_id": 1
        }
        send_success = await client.send_message(message_data)
        assert send_success is True
        
        # Step 5: Ping for health check
        ping_time = await client.ping()
        assert ping_time < 100  # Should be under 100ms
        
        # Step 6: Disconnect
        disconnect_success = await client.disconnect()
        assert disconnect_success is True
        assert client.connection_status == "disconnected"
        
        timer.stop()
        
        # Performance validation: <2000ms for complete lifecycle
        timer.assert_under_ms(2000, "WebSocket connection lifecycle")

    @pytest.mark.asyncio
    async def test_real_time_message_delivery(
        self,
        websocket_test_client,
        performance_timer
    ):
        """Test real-time message delivery and acknowledgment."""
        timer = performance_timer()
        client = websocket_test_client
        
        # Setup connection
        await client.connect()
        await client.authenticate()
        await client.join_room("delivery_test_room")
        
        # Test message delivery timing
        messages_to_send = 10
        delivery_times = []
        
        for i in range(messages_to_send):
            message_data = {
                "type": "message",
                "content": f"Test message {i+1}",
                "room_id": "delivery_test_room",
                "sender_id": 1,
                "message_id": f"msg_{i+1}"
            }
            
            timer.start()
            send_success = await client.send_message(message_data)
            timer.stop()
            
            assert send_success is True
            delivery_times.append(timer.elapsed_ms())
        
        # Verify all messages were delivered quickly
        avg_delivery_time = sum(delivery_times) / len(delivery_times)
        max_delivery_time = max(delivery_times)
        
        # Performance targets: <100ms average, <200ms maximum
        assert avg_delivery_time < 100, f"Average delivery time {avg_delivery_time:.2f}ms exceeds 100ms"
        assert max_delivery_time < 200, f"Maximum delivery time {max_delivery_time:.2f}ms exceeds 200ms"
        
        await client.disconnect()

    @pytest.mark.asyncio
    async def test_connection_reliability_under_load(
        self,
        auth_token,
        performance_timer
    ):
        """Test WebSocket connection reliability under load."""
        timer = performance_timer()
        
        # Create multiple concurrent connections
        num_connections = 20
        clients = []
        
        # Create clients
        for i in range(num_connections):
            client = await self.websocket_test_client(auth_token)
            clients.append(client)
        
        timer.start()
        
        # Connect all clients concurrently
        connection_tasks = [client.connect() for client in clients]
        connection_results = await asyncio.gather(*connection_tasks, return_exceptions=True)
        
        # Authenticate all clients concurrently
        auth_tasks = [client.authenticate() for client in clients]
        auth_results = await asyncio.gather(*auth_tasks, return_exceptions=True)
        
        timer.stop()
        
        # Performance validation: <5000ms for 20 concurrent connections
        timer.assert_under_ms(5000, "20 concurrent WebSocket connections")
        
        # Verify connection success rate
        successful_connections = sum(1 for result in connection_results if isinstance(result, str))
        successful_auths = sum(1 for result in auth_results if result is True)
        
        connection_success_rate = successful_connections / num_connections
        auth_success_rate = successful_auths / num_connections
        
        # Reliability targets: >95% success rate
        assert connection_success_rate > 0.95, f"Connection success rate {connection_success_rate:.2%} below 95%"
        assert auth_success_rate > 0.95, f"Authentication success rate {auth_success_rate:.2%} below 95%"
        
        # Cleanup
        disconnect_tasks = [client.disconnect() for client in clients]
        await asyncio.gather(*disconnect_tasks, return_exceptions=True)

    @pytest.mark.asyncio
    async def test_broadcasting_and_participant_notification(
        self,
        auth_token,
        performance_timer
    ):
        """Test broadcasting and participant notification timing."""
        timer = performance_timer()
        
        # Create multiple clients for broadcast testing
        num_participants = 5
        clients = []
        
        for i in range(num_participants):
            client = await self.websocket_test_client(auth_token)
            await client.connect()
            await client.authenticate()
            await client.join_room("broadcast_test_room")
            clients.append(client)
        
        # Simulate broadcast message
        broadcast_data = {
            "type": "room_broadcast",
            "content": "Hello everyone in the room!",
            "room_id": "broadcast_test_room",
            "sender_id": 1
        }
        
        timer.start()
        
        # Send broadcast from first client
        await clients[0].send_message(broadcast_data)
        
        # Simulate all other clients receiving the broadcast
        receive_tasks = []
        for client in clients[1:]:
            # Mock receiving broadcast message
            client.message_queue.put_nowait(broadcast_data)
            receive_tasks.append(client.receive_message(timeout=2.0))
        
        received_messages = await asyncio.gather(*receive_tasks, return_exceptions=True)
        
        timer.stop()
        
        # Performance validation: <500ms for broadcast delivery
        timer.assert_under_ms(500, "Broadcast message delivery")
        
        # Verify all participants received the broadcast
        successful_receives = sum(1 for msg in received_messages if msg is not None)
        receive_success_rate = successful_receives / (num_participants - 1)
        
        # Reliability target: >99% delivery success
        assert receive_success_rate > 0.99, f"Broadcast delivery success rate {receive_success_rate:.2%} below 99%"
        
        # Cleanup
        for client in clients:
            await client.disconnect()

    @pytest.mark.asyncio
    async def test_websocket_error_handling_and_recovery(
        self,
        websocket_test_client
    ):
        """Test WebSocket error handling and connection recovery."""
        client = websocket_test_client
        
        # Test connection failure handling
        with patch.object(client, 'connect') as mock_connect:
            mock_connect.side_effect = ConnectionError("Connection failed")
            
            with pytest.raises(ConnectionError) as exc_info:
                await client.connect()
            
            assert "Connection failed" in str(exc_info.value)
        
        # Test authentication failure handling
        await client.connect()
        
        with patch.object(client.websocket, 'recv') as mock_recv:
            auth_failure_response = {
                "type": "auth_response",
                "status": "failed",
                "error": "Invalid token"
            }
            mock_recv.return_value = json.dumps(auth_failure_response)
            
            auth_success = await client.authenticate()
            assert auth_success is False
        
        # Test message send failure handling
        client.connection_status = "authenticated"
        
        with patch.object(client.websocket, 'send') as mock_send:
            mock_send.side_effect = ConnectionClosed(None, None)
            
            message_data = {
                "type": "message",
                "content": "Test message",
                "room_id": "test_room"
            }
            
            with pytest.raises(ConnectionClosed):
                await client.send_message(message_data)

    @pytest.mark.asyncio
    async def test_websocket_performance_metrics_collection(
        self,
        websocket_test_client,
        performance_timer
    ):
        """Test WebSocket performance metrics collection."""
        timer = performance_timer()
        client = websocket_test_client
        
        # Setup connection and perform operations
        await client.connect()
        await client.authenticate()
        await client.join_room("metrics_test_room")
        
        # Send multiple messages to collect metrics
        num_messages = 50
        for i in range(num_messages):
            message_data = {
                "type": "message",
                "content": f"Metrics test message {i+1}",
                "room_id": "metrics_test_room",
                "sender_id": 1
            }
            await client.send_message(message_data)
        
        # Perform ping tests
        ping_times = []
        for _ in range(10):
            ping_time = await client.ping()
            ping_times.append(ping_time)
        
        # Get connection statistics
        stats = client.get_connection_stats()
        
        # Verify metrics collection
        assert stats["connection_id"] is not None
        assert stats["status"] == "authenticated"
        assert stats["messages_sent"] >= 0
        assert stats["last_ping_ms"] is not None
        
        # Verify ping performance
        avg_ping = sum(ping_times) / len(ping_times)
        max_ping = max(ping_times)
        
        # Performance targets: <50ms average ping, <100ms maximum ping
        assert avg_ping < 50, f"Average ping {avg_ping:.2f}ms exceeds 50ms target"
        assert max_ping < 100, f"Maximum ping {max_ping:.2f}ms exceeds 100ms target"
        
        await client.disconnect()

    async def websocket_test_client(self, auth_token: str):
        """Helper method to create WebSocket test client."""
        
        class WebSocketTestClient:
            def __init__(self, auth_token: str):
                self.auth_token = auth_token
                self.connection_id = None
                self.websocket = None
                self.received_messages = []
                self.connection_status = "disconnected"
                self.last_ping_time = None
                self.message_queue = asyncio.Queue()
                
            async def connect(self, endpoint: str = "ws://localhost:8000/ws") -> str:
                self.websocket = MagicMock()
                self.websocket.send = AsyncMock()
                self.websocket.recv = AsyncMock()
                self.websocket.close = AsyncMock()
                self.websocket.ping = AsyncMock()
                
                self.connection_id = f"test_conn_{int(time.time() * 1000)}"
                self.connection_status = "connected"
                return self.connection_id
                
            async def authenticate(self) -> bool:
                if not self.websocket:
                    raise ConnectionError("Not connected")
                
                auth_response = {
                    "type": "auth_response",
                    "status": "authenticated",
                    "user_id": 1,
                    "connection_id": self.connection_id
                }
                
                self.websocket.recv.return_value = json.dumps(auth_response)
                self.connection_status = "authenticated"
                return True
            
            async def join_room(self, room_id: str) -> bool:
                join_response = {
                    "type": "room_joined",
                    "room_id": room_id,
                    "status": "success"
                }
                self.websocket.recv.return_value = json.dumps(join_response)
                return True
            
            async def send_message(self, message_data: Dict[str, Any]) -> bool:
                await self.websocket.send(json.dumps(message_data))
                return True
            
            async def receive_message(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
                try:
                    await asyncio.wait_for(self.message_queue.get(), timeout=timeout)
                    message_data = {
                        "type": "message",
                        "content": "Test message",
                        "sender_id": 2,
                        "room_id": "test_room"
                    }
                    self.received_messages.append(message_data)
                    return message_data
                except asyncio.TimeoutError:
                    return None
            
            async def ping(self) -> float:
                await self.websocket.ping()
                self.last_ping_time = 0.025  # 25ms mock ping
                return self.last_ping_time * 1000
            
            async def disconnect(self) -> bool:
                if self.websocket:
                    await self.websocket.close()
                    self.connection_status = "disconnected"
                    return True
                return False
            
            def get_connection_stats(self) -> Dict[str, Any]:
                return {
                    "connection_id": self.connection_id,
                    "status": self.connection_status,
                    "messages_sent": len([m for m in self.received_messages if m.get("type") == "message"]),
                    "messages_received": len(self.received_messages),
                    "last_ping_ms": self.last_ping_time * 1000 if self.last_ping_time else None
                }
        
        return WebSocketTestClient(auth_token)
