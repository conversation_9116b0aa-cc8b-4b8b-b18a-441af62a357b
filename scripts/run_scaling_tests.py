#!/usr/bin/env python3
"""
Test runner script for Phase 7.3.3 - Horizontal Scaling & Load Balancing.

This script executes comprehensive testing and validation for all scaling components:
- Unit tests for all scaling services with >80% coverage
- Integration tests for API endpoints with database rollback
- Performance tests validating all established targets
- Database migration validation with timing
- Coverage reporting and quality gate validation

Implements systematic testing approach with detailed reporting and validation.
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ScalingTestRunner:
    """Test runner for scaling components with comprehensive validation."""

    def __init__(self):
        self.project_root = project_root
        self.test_results = {
            "unit_tests": {},
            "integration_tests": {},
            "performance_tests": {},
            "migration_tests": {},
            "coverage": {},
            "quality_gates": {}
        }
        self.start_time = time.time()

    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for scaling services."""
        print("🧪 Running Unit Tests for Scaling Services...")
        
        try:
            # Run unit tests with coverage
            cmd = [
                "python", "-m", "pytest",
                "tests/unit/test_scaling_services.py",
                "-v",
                "--cov=app.services.scaling_services",
                "--cov-report=json:coverage_unit.json",
                "--cov-report=term-missing",
                "--tb=short"
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            success = result.returncode == 0
            execution_time = end_time - start_time
            
            self.test_results["unit_tests"] = {
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            if success:
                print(f"✅ Unit tests completed successfully in {execution_time:.2f}s")
            else:
                print(f"❌ Unit tests failed in {execution_time:.2f}s")
                print(f"Error: {result.stderr}")
            
            return self.test_results["unit_tests"]
            
        except Exception as e:
            print(f"❌ Error running unit tests: {str(e)}")
            self.test_results["unit_tests"] = {
                "success": False,
                "error": str(e)
            }
            return self.test_results["unit_tests"]

    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests for scaling API endpoints."""
        print("🔗 Running Integration Tests for Scaling API Endpoints...")
        
        try:
            # Run integration tests
            cmd = [
                "python", "-m", "pytest",
                "tests/integration/test_scaling_endpoints.py",
                "-v",
                "--cov=app.api.v1.endpoints.scaling_endpoints",
                "--cov-report=json:coverage_integration.json",
                "--cov-report=term-missing",
                "--tb=short"
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            success = result.returncode == 0
            execution_time = end_time - start_time
            
            self.test_results["integration_tests"] = {
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            if success:
                print(f"✅ Integration tests completed successfully in {execution_time:.2f}s")
            else:
                print(f"❌ Integration tests failed in {execution_time:.2f}s")
                print(f"Error: {result.stderr}")
            
            return self.test_results["integration_tests"]
            
        except Exception as e:
            print(f"❌ Error running integration tests: {str(e)}")
            self.test_results["integration_tests"] = {
                "success": False,
                "error": str(e)
            }
            return self.test_results["integration_tests"]

    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests for scaling components."""
        print("⚡ Running Performance Tests for Scaling Components...")
        
        try:
            # Run performance tests
            cmd = [
                "python", "-m", "pytest",
                "tests/performance/test_scaling_performance.py",
                "-v",
                "--tb=short",
                "-s"  # Show print statements for performance metrics
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            success = result.returncode == 0
            execution_time = end_time - start_time
            
            self.test_results["performance_tests"] = {
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            if success:
                print(f"✅ Performance tests completed successfully in {execution_time:.2f}s")
                self._extract_performance_metrics()
            else:
                print(f"❌ Performance tests failed in {execution_time:.2f}s")
                print(f"Error: {result.stderr}")
            
            return self.test_results["performance_tests"]
            
        except Exception as e:
            print(f"❌ Error running performance tests: {str(e)}")
            self.test_results["performance_tests"] = {
                "success": False,
                "error": str(e)
            }
            return self.test_results["performance_tests"]

    def validate_database_migration(self) -> Dict[str, Any]:
        """Validate database migration performance and correctness."""
        print("🗄️ Validating Database Migration for Scaling Models...")
        
        try:
            # Run migration validation
            cmd = [
                "python", "-m", "pytest",
                "tests/unit/test_scaling_repositories.py::TestScalingRepositories::test_migration_performance",
                "-v",
                "--tb=short"
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            success = result.returncode == 0
            execution_time = end_time - start_time
            
            # Validate migration time (<60 seconds target)
            migration_time_valid = execution_time < 60
            
            self.test_results["migration_tests"] = {
                "success": success and migration_time_valid,
                "execution_time": execution_time,
                "migration_time_valid": migration_time_valid,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            if success and migration_time_valid:
                print(f"✅ Migration validation completed successfully in {execution_time:.2f}s")
            else:
                print(f"❌ Migration validation failed")
                if not migration_time_valid:
                    print(f"Migration time {execution_time:.2f}s exceeded 60s target")
            
            return self.test_results["migration_tests"]
            
        except Exception as e:
            print(f"❌ Error validating migration: {str(e)}")
            self.test_results["migration_tests"] = {
                "success": False,
                "error": str(e)
            }
            return self.test_results["migration_tests"]

    def generate_coverage_report(self) -> Dict[str, Any]:
        """Generate comprehensive coverage report."""
        print("📊 Generating Coverage Report...")
        
        try:
            # Combine coverage from unit and integration tests
            cmd = [
                "python", "-m", "pytest",
                "tests/unit/test_scaling_services.py",
                "tests/integration/test_scaling_endpoints.py",
                "--cov=app.services.scaling_services",
                "--cov=app.api.v1.endpoints.scaling_endpoints",
                "--cov=app.repositories.scaling_repositories",
                "--cov-report=json:coverage_combined.json",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-fail-under=80"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            # Parse coverage data
            coverage_data = {}
            try:
                with open(self.project_root / "coverage_combined.json", "r") as f:
                    coverage_json = json.load(f)
                    coverage_data = {
                        "total_coverage": coverage_json.get("totals", {}).get("percent_covered", 0),
                        "files": coverage_json.get("files", {}),
                        "summary": coverage_json.get("totals", {})
                    }
            except FileNotFoundError:
                print("⚠️ Coverage file not found")
            
            success = result.returncode == 0
            coverage_target_met = coverage_data.get("total_coverage", 0) >= 80
            
            self.test_results["coverage"] = {
                "success": success,
                "coverage_target_met": coverage_target_met,
                "total_coverage": coverage_data.get("total_coverage", 0),
                "coverage_data": coverage_data,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            if success and coverage_target_met:
                print(f"✅ Coverage target met: {coverage_data.get('total_coverage', 0):.1f}% (>80%)")
            else:
                print(f"❌ Coverage target not met: {coverage_data.get('total_coverage', 0):.1f}% (<80%)")
            
            return self.test_results["coverage"]
            
        except Exception as e:
            print(f"❌ Error generating coverage report: {str(e)}")
            self.test_results["coverage"] = {
                "success": False,
                "error": str(e)
            }
            return self.test_results["coverage"]

    def validate_quality_gates(self) -> Dict[str, Any]:
        """Validate all quality gates for Phase 7.3.3."""
        print("🎯 Validating Quality Gates...")
        
        quality_gates = {
            "unit_tests_pass": self.test_results["unit_tests"].get("success", False),
            "integration_tests_pass": self.test_results["integration_tests"].get("success", False),
            "performance_tests_pass": self.test_results["performance_tests"].get("success", False),
            "migration_time_valid": self.test_results["migration_tests"].get("migration_time_valid", False),
            "coverage_target_met": self.test_results["coverage"].get("coverage_target_met", False),
            "total_coverage": self.test_results["coverage"].get("total_coverage", 0)
        }
        
        all_gates_passed = all([
            quality_gates["unit_tests_pass"],
            quality_gates["integration_tests_pass"],
            quality_gates["performance_tests_pass"],
            quality_gates["migration_time_valid"],
            quality_gates["coverage_target_met"]
        ])
        
        self.test_results["quality_gates"] = {
            "all_passed": all_gates_passed,
            "gates": quality_gates
        }
        
        print("\n📋 Quality Gates Summary:")
        print(f"  ✅ Unit Tests: {'PASS' if quality_gates['unit_tests_pass'] else 'FAIL'}")
        print(f"  ✅ Integration Tests: {'PASS' if quality_gates['integration_tests_pass'] else 'FAIL'}")
        print(f"  ✅ Performance Tests: {'PASS' if quality_gates['performance_tests_pass'] else 'FAIL'}")
        print(f"  ✅ Migration Performance: {'PASS' if quality_gates['migration_time_valid'] else 'FAIL'}")
        print(f"  ✅ Coverage Target (>80%): {'PASS' if quality_gates['coverage_target_met'] else 'FAIL'} ({quality_gates['total_coverage']:.1f}%)")
        print(f"\n🎯 Overall Result: {'✅ ALL QUALITY GATES PASSED' if all_gates_passed else '❌ QUALITY GATES FAILED'}")
        
        return self.test_results["quality_gates"]

    def _extract_performance_metrics(self):
        """Extract performance metrics from test output."""
        # This would parse the performance test output to extract specific metrics
        # For now, we'll add a placeholder for the metrics extraction logic
        pass

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and validations."""
        print("🚀 Starting Comprehensive Testing for Phase 7.3.3 - Horizontal Scaling & Load Balancing")
        print("=" * 80)
        
        # Run all test suites
        self.run_unit_tests()
        self.run_integration_tests()
        self.run_performance_tests()
        self.validate_database_migration()
        self.generate_coverage_report()
        self.validate_quality_gates()
        
        # Calculate total execution time
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 80)
        print(f"🏁 Testing completed in {total_time:.2f} seconds")
        
        # Save results to file
        results_file = self.project_root / "test_results_scaling.json"
        with open(results_file, "w") as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"📄 Detailed results saved to: {results_file}")
        
        return self.test_results


def main():
    """Main entry point for test runner."""
    runner = ScalingTestRunner()
    results = runner.run_all_tests()
    
    # Exit with appropriate code
    all_passed = results["quality_gates"].get("all_passed", False)
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
