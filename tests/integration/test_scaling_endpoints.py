"""
Integration tests for scaling API endpoints.

This module provides comprehensive integration tests for Phase 7.3.3 scaling endpoints:
- API endpoint integration with database transactions and rollback mechanisms
- RBAC permission validation with authentication middleware
- Request/response validation with Pydantic schemas
- Performance testing validating <200ms GET and <500ms POST/PUT targets
- Error handling and correlation ID tracking validation

Implements >80% test coverage with pytest-asyncio patterns and database rollback.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from decimal import Decimal
from uuid import uuid4
from httpx import AsyncClient
from fastapi import status

from app.main import app
from app.models.scaling_models import (
    ScalingMetrics, AutoScalingPolicy, LoadBalancerConfig,
    ContainerMetrics, ScalingEvent, LoadBalancerStrategy,
    ContainerStatus, ScalingEventType
)
from app.schemas.scaling_schemas import (
    ScalingMetricsCreate, AutoScalingPolicyCreate, LoadBalancerConfigCreate,
    ContainerMetricsCreate
)
from tests.conftest import TestingSessionLocal, override_get_db


class TestScalingMetricsEndpoints:
    """Integration tests for scaling metrics endpoints."""

    @pytest.mark.asyncio
    async def test_record_scaling_metric_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful scaling metric recording via API."""
        # Arrange
        metric_data = {
            "metric_name": "cpu_utilization",
            "metric_type": "resource",
            "component": "booking-service",
            "current_value": 75.5,
            "cpu_utilization": 75.5,
            "memory_utilization": 60.2,
            "request_rate": 150,
            "response_time_ms": 200,
            "error_rate": 0.02
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/scaling/metrics",
            json=metric_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["metric_name"] == "cpu_utilization"
        assert response_data["component"] == "booking-service"
        assert float(response_data["current_value"]) == 75.5
        assert "id" in response_data
        assert "created_at" in response_data

    @pytest.mark.asyncio
    async def test_record_scaling_metric_validation_error(self, client: AsyncClient, auth_headers: dict):
        """Test scaling metric recording with validation error."""
        # Arrange
        invalid_metric_data = {
            "metric_name": "",  # Invalid empty name
            "metric_type": "resource",
            "component": "booking-service",
            "current_value": 75.5
        }
        
        # Act
        response = await client.post(
            "/api/v1/scaling/metrics",
            json=invalid_metric_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Validation error" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_scaling_metrics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful scaling metrics retrieval."""
        # Arrange - First create a metric
        metric_data = {
            "metric_name": "memory_utilization",
            "metric_type": "resource",
            "component": "payment-service",
            "current_value": 65.0,
            "memory_utilization": 65.0
        }
        
        await client.post(
            "/api/v1/scaling/metrics",
            json=metric_data,
            headers=auth_headers
        )
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/scaling/metrics?component=payment-service",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)
        if response_data:  # If metrics exist
            assert response_data[0]["component"] == "payment-service"

    @pytest.mark.asyncio
    async def test_get_utilization_summary_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful utilization summary retrieval."""
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/scaling/metrics/utilization",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, dict)

    @pytest.mark.asyncio
    async def test_analyze_scaling_need_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful scaling need analysis."""
        # Act
        response = await client.get(
            "/api/v1/scaling/metrics/booking-service/analysis",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["component"] == "booking-service"
        assert "scaling_needed" in response_data
        assert "recommendation" in response_data
        assert "confidence" in response_data

    @pytest.mark.asyncio
    async def test_scaling_metrics_unauthorized(self, client: AsyncClient):
        """Test scaling metrics endpoint without authentication."""
        # Act
        response = await client.get("/api/v1/scaling/metrics")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestAutoScalingPolicyEndpoints:
    """Integration tests for auto-scaling policy endpoints."""

    @pytest.mark.asyncio
    async def test_create_auto_scaling_policy_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful auto-scaling policy creation."""
        # Arrange
        policy_data = {
            "name": "test-booking-service-policy",
            "component": "booking-service",
            "min_replicas": 2,
            "max_replicas": 10,
            "target_cpu_utilization": 70,
            "scale_up_threshold": 80.0,
            "scale_down_threshold": 30.0,
            "scale_up_cooldown_seconds": 300,
            "scale_down_cooldown_seconds": 600
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/scaling/policies",
            json=policy_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["name"] == "test-booking-service-policy"
        assert response_data["component"] == "booking-service"
        assert response_data["min_replicas"] == 2
        assert response_data["max_replicas"] == 10
        assert "id" in response_data

    @pytest.mark.asyncio
    async def test_get_auto_scaling_policies_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful auto-scaling policies retrieval."""
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.get(
            "/api/v1/scaling/policies",
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.2  # <200ms for GET operations
        
        response_data = response.json()
        assert isinstance(response_data, list)

    @pytest.mark.asyncio
    async def test_update_auto_scaling_policy_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful auto-scaling policy update."""
        # Arrange - First create a policy
        policy_data = {
            "name": "test-update-policy",
            "component": "payment-service",
            "min_replicas": 1,
            "max_replicas": 5,
            "scale_up_threshold": 75.0,
            "scale_down_threshold": 25.0
        }
        
        create_response = await client.post(
            "/api/v1/scaling/policies",
            json=policy_data,
            headers=auth_headers
        )
        policy_id = create_response.json()["id"]
        
        # Update data
        update_data = {
            "max_replicas": 8,
            "scale_up_threshold": 85.0
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.put(
            f"/api/v1/scaling/policies/{policy_id}",
            json=update_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (end_time - start_time) < 0.5  # <500ms for PUT operations
        
        response_data = response.json()
        assert response_data["max_replicas"] == 8
        assert float(response_data["scale_up_threshold"]) == 85.0

    @pytest.mark.asyncio
    async def test_delete_auto_scaling_policy_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful auto-scaling policy deletion."""
        # Arrange - First create a policy
        policy_data = {
            "name": "test-delete-policy",
            "component": "notification-service",
            "min_replicas": 1,
            "max_replicas": 3,
            "scale_up_threshold": 70.0,
            "scale_down_threshold": 20.0
        }
        
        create_response = await client.post(
            "/api/v1/scaling/policies",
            json=policy_data,
            headers=auth_headers
        )
        policy_id = create_response.json()["id"]
        
        # Act
        response = await client.delete(
            f"/api/v1/scaling/policies/{policy_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify policy is deleted
        get_response = await client.get(
            f"/api/v1/scaling/policies/{policy_id}",
            headers=auth_headers
        )
        assert get_response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_evaluate_scaling_policies_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful scaling policy evaluation."""
        # Act
        response = await client.post(
            "/api/v1/scaling/decisions/evaluate",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert isinstance(response_data, list)


class TestLoadBalancerEndpoints:
    """Integration tests for load balancer endpoints."""

    @pytest.mark.asyncio
    async def test_create_load_balancer_config_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful load balancer configuration creation."""
        # Arrange
        config_data = {
            "name": "test-booking-lb",
            "service_name": "booking-service",
            "strategy": "round_robin",
            "health_check_path": "/health",
            "health_check_interval_seconds": 30,
            "health_check_timeout_seconds": 5,
            "health_check_retries": 3,
            "ssl_enabled": True
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/scaling/load-balancer/configs",
            json=config_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["name"] == "test-booking-lb"
        assert response_data["service_name"] == "booking-service"
        assert response_data["strategy"] == "round_robin"

    @pytest.mark.asyncio
    async def test_get_optimal_routing_strategy_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful optimal routing strategy retrieval."""
        # Act
        response = await client.get(
            "/api/v1/scaling/load-balancer/routing/booking-service",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["service_name"] == "booking-service"
        assert "recommended_strategy" in response_data
        assert "reasoning" in response_data

    @pytest.mark.asyncio
    async def test_manage_session_affinity_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful session affinity management."""
        # Arrange - First create a load balancer config
        config_data = {
            "name": "test-session-lb",
            "service_name": "session-test-service",
            "strategy": "round_robin"
        }
        
        await client.post(
            "/api/v1/scaling/load-balancer/configs",
            json=config_data,
            headers=auth_headers
        )
        
        affinity_data = {
            "enable_affinity": True,
            "affinity_config": {"timeout": 3600}
        }
        
        # Act
        response = await client.put(
            "/api/v1/scaling/load-balancer/session-affinity/session-test-service",
            json=affinity_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["service_name"] == "session-test-service"
        assert response_data["session_affinity_enabled"] is True


class TestContainerOrchestrationEndpoints:
    """Integration tests for container orchestration endpoints."""

    @pytest.mark.asyncio
    async def test_record_container_metrics_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful container metrics recording."""
        # Arrange
        metrics_data = {
            "container_id": "test-container-123",
            "pod_name": "booking-service-pod-1",
            "namespace": "culture-connect",
            "cpu_usage_cores": 0.5,
            "memory_usage_bytes": 512000000,
            "request_count": 150,
            "error_count": 2,
            "status": "running"
        }
        
        # Act
        start_time = asyncio.get_event_loop().time()
        response = await client.post(
            "/api/v1/scaling/containers/metrics",
            json=metrics_data,
            headers=auth_headers
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (end_time - start_time) < 0.5  # <500ms for POST operations
        
        response_data = response.json()
        assert response_data["container_id"] == "test-container-123"
        assert response_data["pod_name"] == "booking-service-pod-1"
        assert response_data["namespace"] == "culture-connect"

    @pytest.mark.asyncio
    async def test_generate_kubernetes_manifests_success(self, client: AsyncClient, auth_headers: dict):
        """Test successful Kubernetes manifest generation."""
        # Arrange
        service_config = {
            "name": "test-service",
            "namespace": "culture-connect",
            "image": "test-service:v1.0.0",
            "port": 8000,
            "replicas": 3,
            "min_replicas": 2,
            "max_replicas": 10,
            "expose_externally": True
        }
        
        # Act
        response = await client.post(
            "/api/v1/scaling/containers/manifests",
            json=service_config,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert "deployment.yaml" in response_data
        assert "service.yaml" in response_data
        assert "hpa.yaml" in response_data
        assert "ingress.yaml" in response_data
        
        # Verify manifest content
        assert "test-service" in response_data["deployment.yaml"]
        assert "HorizontalPodAutoscaler" in response_data["hpa.yaml"]
