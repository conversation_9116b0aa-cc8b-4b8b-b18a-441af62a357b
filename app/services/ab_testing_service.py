"""
A/B Testing Service for Culture Connect Backend API.

This module provides comprehensive A/B testing service for payment routing strategy optimization including:
- A/B test management and configuration
- User assignment with consistent hashing algorithms
- Statistical significance validation and performance comparison metrics
- Integration with VPN detection and analytics dashboard capabilities
- Controlled testing of different routing strategies with comprehensive analysis

Implements Phase 2.3 A/B Testing Framework requirements with production-grade
performance targets (<200ms test operations), comprehensive business logic,
and seamless integration with existing geolocation analytics and VPN detection systems.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
import uuid
import logging

from app.services.base import BaseService, ServiceError, ValidationError
from app.repositories.ab_testing_repository import ABTestRepository, ABTestAssignmentRepository
from app.repositories.ab_testing_result_repository import ABTestResultRepository, ABTestAnalysisRepository
from app.models.ab_testing import (
    ABTest, ABTestAssignment, ABTestResult, ABTestAnalysis,
    RoutingStrategy, ABTestStatus, ABTestType, StatisticalSignificance
)
from app.schemas.ab_testing import (
    ABTestCreate, ABTestUpdate, ABTestResponse, ABTestAssignmentResponse,
    ABTestResultCreate, ABTestAnalysisResponse, ABTestDashboardResponse,
    ABTestFilterRequest, ABTestSummarySchema
)

logger = logging.getLogger(__name__)


class ABTestingService(BaseService[ABTest, ABTestRepository]):
    """
    A/B testing service for payment routing strategy optimization.

    Provides comprehensive A/B testing capabilities including:
    - Test configuration and lifecycle management
    - User assignment with consistent hashing
    - Statistical analysis and significance validation
    - Performance comparison and optimization insights
    - Integration with VPN detection and analytics systems
    """

    def __init__(self, db_session: AsyncSession):
        """Initialize A/B testing service."""
        super().__init__(ABTestRepository, ABTest, db_session=db_session)

        # Initialize additional repositories
        self.assignment_repo = ABTestAssignmentRepository(db_session)
        self.result_repo = ABTestResultRepository(db_session)
        self.analysis_repo = ABTestAnalysisRepository(db_session)

    async def create_test(self, test_data: ABTestCreate) -> ABTestResponse:
        """
        Create a new A/B test with comprehensive validation.

        Args:
            test_data: Test configuration data

        Returns:
            Created A/B test response

        Performance Target: <150ms test creation
        """
        correlation = self.log_operation_start("create_test")

        try:
            # Validate test configuration
            await self._validate_test_creation(test_data)

            # Convert to dict and create test
            test_dict = test_data.dict(exclude_unset=True)
            test = await self.repository.create_test(test_dict)

            # Convert to response schema
            response = ABTestResponse.from_orm(test)

            self.log_operation_success(correlation, f"Created A/B test: {test.test_name}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "create_test", {
                "test_name": test_data.test_name
            })

    async def get_user_assignment(
        self,
        test_id: uuid.UUID,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        country_code: Optional[str] = None,
        is_vpn_detected: bool = False
    ) -> ABTestAssignmentResponse:
        """
        Get or create user assignment for A/B test with consistent hashing.

        Args:
            test_id: Test identifier
            user_id: User identifier (optional for anonymous users)
            session_id: Session identifier for anonymous tracking
            ip_address: User IP address
            user_agent: User agent string
            country_code: User's country code
            is_vpn_detected: Whether VPN was detected

        Returns:
            User assignment response

        Performance Target: <100ms assignment retrieval/creation
        """
        correlation = self.log_operation_start("get_user_assignment")

        try:
            # Validate test is active and eligible
            test = await self.repository.get_by_id(test_id)
            if not test:
                raise ValidationError(f"A/B test not found: {test_id}")

            if test.status != ABTestStatus.ACTIVE:
                raise ValidationError(f"Test is not active: {test.status}")

            # Check test eligibility
            if not await self._is_user_eligible_for_test(test, country_code, is_vpn_detected):
                raise ValidationError("User not eligible for this test")

            # Get or create assignment
            assignment = await self.assignment_repo.get_or_create_assignment(
                test_id=test_id,
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                country_code=country_code,
                is_vpn_detected=is_vpn_detected
            )

            # Convert to response schema
            response = ABTestAssignmentResponse.from_orm(assignment)

            self.log_operation_success(correlation, f"Retrieved assignment: {assignment.id}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "get_user_assignment", {
                "test_id": str(test_id),
                "user_id": user_id,
                "session_id": session_id
            })

    async def submit_test_result(self, result_data: ABTestResultCreate) -> Dict[str, Any]:
        """
        Submit A/B test result for analysis.

        Args:
            result_data: Test result data

        Returns:
            Result submission confirmation

        Performance Target: <100ms result submission
        """
        correlation = self.log_operation_start("submit_test_result")

        try:
            # Validate test and assignment
            test = await self.repository.get_by_id(result_data.test_id)
            if not test:
                raise ValidationError(f"A/B test not found: {result_data.test_id}")

            # Convert to dict and create result
            result_dict = result_data.dict(exclude_unset=True)
            result = await self.result_repo.create_result(result_dict)

            # Update test metrics asynchronously
            await self._update_test_metrics(test.id)

            response = {
                "result_id": str(result.id),
                "test_id": str(test.id),
                "status": "submitted",
                "timestamp": result.result_timestamp.isoformat()
            }

            self.log_operation_success(correlation, f"Submitted test result: {result.id}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "submit_test_result", {
                "test_id": str(result_data.test_id)
            })

    async def analyze_test_performance(
        self,
        test_id: uuid.UUID,
        force_analysis: bool = False
    ) -> ABTestAnalysisResponse:
        """
        Perform comprehensive statistical analysis of A/B test.

        Args:
            test_id: Test identifier
            force_analysis: Whether to force new analysis

        Returns:
            Comprehensive analysis response

        Performance Target: <300ms analysis generation
        """
        correlation = self.log_operation_start("analyze_test_performance")

        try:
            # Get test
            test = await self.repository.get_by_id(test_id)
            if not test:
                raise ValidationError(f"A/B test not found: {test_id}")

            # Check if analysis is needed
            if not force_analysis:
                latest_analysis = await self.analysis_repo.get_latest_analysis(test_id)
                if latest_analysis and latest_analysis.analysis_date > datetime.now(timezone.utc) - timedelta(hours=1):
                    return ABTestAnalysisResponse.from_orm(latest_analysis)

            # Perform statistical analysis
            statistical_results = await self.analysis_repo.calculate_statistical_significance(test_id)

            if "error" in statistical_results:
                raise ValidationError(statistical_results["error"])

            # Get performance comparison
            performance_comparison = await self.result_repo.get_performance_comparison(test_id)

            # Generate recommendations
            recommendations = await self._generate_test_recommendations(test, statistical_results, performance_comparison)

            # Create analysis record
            analysis_data = {
                "test_id": test_id,
                "analysis_period_start": test.start_date or test.created_at,
                "analysis_period_end": datetime.now(timezone.utc),
                "sample_size_a": statistical_results.get("sample_size_a", 0),
                "sample_size_b": statistical_results.get("sample_size_b", 0),
                "total_sample_size": statistical_results.get("sample_size_a", 0) + statistical_results.get("sample_size_b", 0),
                "conversions_a": int(statistical_results.get("sample_size_a", 0) * statistical_results.get("conversion_rate_a", 0) / 100),
                "conversions_b": int(statistical_results.get("sample_size_b", 0) * statistical_results.get("conversion_rate_b", 0) / 100),
                "conversion_rate_a": statistical_results.get("conversion_rate_a", 0),
                "conversion_rate_b": statistical_results.get("conversion_rate_b", 0),
                "effect_size": statistical_results.get("effect_size", 0),
                "relative_effect_size": statistical_results.get("relative_effect_size", 0),
                "p_value": statistical_results.get("p_value"),
                "confidence_level": statistical_results.get("confidence_level", 0.95),
                "confidence_interval_lower": statistical_results.get("confidence_interval_lower"),
                "confidence_interval_upper": statistical_results.get("confidence_interval_upper"),
                "is_statistically_significant": statistical_results.get("is_statistically_significant", False),
                "significance_level": statistical_results.get("significance_level"),
                "statistical_power": statistical_results.get("statistical_power"),
                "recommendation": recommendations.get("recommendation"),
                "recommendation_confidence": recommendations.get("confidence"),
                "recommendation_reason": recommendations.get("reason")
            }

            analysis = await self.analysis_repo.create_analysis(analysis_data)

            # Update test with latest analysis results
            await self.repository.update_test_metrics(
                test_id,
                statistical_results.get("sample_size_a", 0),
                statistical_results.get("sample_size_b", 0),
                statistical_results.get("conversion_rate_a", 0),
                statistical_results.get("conversion_rate_b", 0)
            )

            # Convert to response schema
            response = ABTestAnalysisResponse.from_orm(analysis)

            self.log_operation_success(correlation, f"Generated analysis for test: {test.test_name}")
            return response

        except Exception as e:
            await self.handle_service_error(e, "analyze_test_performance", {
                "test_id": str(test_id)
            })

    async def get_active_tests_for_user(
        self,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        country_code: Optional[str] = None,
        is_vpn_detected: bool = False
    ) -> List[ABTestResponse]:
        """
        Get all active A/B tests applicable to a user.

        Args:
            user_id: User identifier
            session_id: Session identifier
            country_code: User's country code
            is_vpn_detected: Whether VPN was detected

        Returns:
            List of applicable active tests

        Performance Target: <150ms active tests retrieval
        """
        correlation = self.log_operation_start("get_active_tests_for_user")

        try:
            # Get active tests with filtering
            active_tests = await self.repository.get_active_tests(
                country_code=country_code,
                exclude_vpn_users=is_vpn_detected
            )

            # Filter tests based on eligibility
            eligible_tests = []
            for test in active_tests:
                if await self._is_user_eligible_for_test(test, country_code, is_vpn_detected):
                    eligible_tests.append(test)

            # Convert to response schemas
            responses = [ABTestResponse.from_orm(test) for test in eligible_tests]

            self.log_operation_success(correlation, f"Retrieved {len(responses)} active tests")
            return responses

        except Exception as e:
            await self.handle_service_error(e, "get_active_tests_for_user", {
                "user_id": user_id,
                "session_id": session_id,
                "country_code": country_code
            })

    async def get_test_dashboard(self) -> ABTestDashboardResponse:
        """
        Get comprehensive A/B testing dashboard data.

        Returns:
            Dashboard response with summary and insights

        Performance Target: <400ms dashboard generation
        """
        correlation = self.log_operation_start("get_test_dashboard")

        try:
            # Get active and completed tests
            active_tests = await self.repository.get_active_tests()
            completed_tests = await self.repository.get_by_filters({"status": ABTestStatus.COMPLETED})

            # Generate test summaries
            active_test_summaries = []
            for test in active_tests:
                summary = await self._generate_test_summary(test)
                active_test_summaries.append(summary)

            # Calculate overall metrics
            overall_metrics = await self._calculate_overall_metrics(active_tests + completed_tests)

            # Generate insights and recommendations
            recent_insights = await self._generate_recent_insights(active_tests)
            optimization_recommendations = await self._generate_optimization_recommendations(active_tests)

            # Generate performance trends
            performance_trends = await self._calculate_performance_trends(active_tests)

            dashboard = ABTestDashboardResponse(
                dashboard_generated_at=datetime.now(timezone.utc),
                total_active_tests=len(active_tests),
                total_completed_tests=len(completed_tests),
                active_tests=active_test_summaries,
                overall_metrics=overall_metrics,
                recent_insights=recent_insights,
                optimization_recommendations=optimization_recommendations,
                performance_trends=performance_trends,
                geographic_performance=await self._get_geographic_performance(active_tests),
                vpn_impact_summary=await self._get_vpn_impact_summary(active_tests)
            )

            self.log_operation_success(correlation, "Generated A/B testing dashboard")
            return dashboard

        except Exception as e:
            await self.handle_service_error(e, "get_test_dashboard", {})

    # Abstract methods from BaseService
    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for A/B test creation."""
        required_fields = ['test_name', 'strategy_a', 'strategy_b']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        # Validate strategies are different
        if data['strategy_a'] == data['strategy_b']:
            raise ValidationError("Strategy A and Strategy B must be different")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """Validate data for A/B test updates."""
        # Only allow certain fields to be updated
        allowed_fields = ['test_description', 'status', 'end_date', 'traffic_allocation_percentage', 'exclude_vpn_users']
        filtered_data = {k: v for k, v in data.items() if k in allowed_fields}
        return filtered_data

    # Helper methods for A/B testing operations
    async def _validate_test_creation(self, test_data: ABTestCreate) -> None:
        """Validate A/B test creation data."""
        # Check for conflicting active tests with same strategies
        active_tests = await self.repository.get_active_tests()

        for test in active_tests:
            if ((test.strategy_a == test_data.strategy_a and test.strategy_b == test_data.strategy_b) or
                (test.strategy_a == test_data.strategy_b and test.strategy_b == test_data.strategy_a)):
                raise ValidationError(f"Active test already exists with these strategies: {test.test_name}")

        # Validate date range
        if test_data.start_date and test_data.end_date:
            if test_data.start_date >= test_data.end_date:
                raise ValidationError("Start date must be before end date")

        # Validate sample size requirements
        if test_data.min_sample_size < 100:
            raise ValidationError("Minimum sample size must be at least 100")

    async def _is_user_eligible_for_test(
        self,
        test: ABTest,
        country_code: Optional[str],
        is_vpn_detected: bool
    ) -> bool:
        """Check if user is eligible for the test."""
        # Check VPN exclusion
        if test.exclude_vpn_users and is_vpn_detected:
            return False

        # Check country targeting
        if test.target_countries and country_code:
            if country_code not in test.target_countries:
                return False

        # Check country exclusion
        if test.exclude_countries and country_code:
            if country_code in test.exclude_countries:
                return False

        return True

    async def _update_test_metrics(self, test_id: uuid.UUID) -> None:
        """Update test metrics based on latest results."""
        try:
            # Get conversion metrics
            metrics = await self.result_repo.get_conversion_metrics(test_id)

            if metrics:
                group_a_data = metrics.get("group_A", {})
                group_b_data = metrics.get("group_B", {})

                await self.repository.update_test_metrics(
                    test_id,
                    group_a_data.get("total_results", 0),
                    group_b_data.get("total_results", 0),
                    group_a_data.get("conversion_rate", 0),
                    group_b_data.get("conversion_rate", 0)
                )
        except Exception as e:
            logger.warning(f"Failed to update test metrics for {test_id}: {e}")

    async def _generate_test_recommendations(
        self,
        test: ABTest,
        statistical_results: Dict[str, Any],
        performance_comparison: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate test recommendations based on analysis."""
        recommendations = {
            "recommendation": "continue_testing",
            "confidence": "low",
            "reason": "Insufficient data for recommendation"
        }

        # Check statistical significance
        is_significant = statistical_results.get("is_statistically_significant", False)
        p_value = statistical_results.get("p_value", 1.0)
        sample_size_a = statistical_results.get("sample_size_a", 0)
        sample_size_b = statistical_results.get("sample_size_b", 0)
        effect_size = statistical_results.get("relative_effect_size", 0)

        # Check if we have sufficient sample size
        min_sample_reached = (sample_size_a >= test.min_sample_size and
                             sample_size_b >= test.min_sample_size)

        if min_sample_reached:
            if is_significant and abs(effect_size) >= test.min_effect_size * 100:
                if effect_size > 0:
                    recommendations = {
                        "recommendation": "implement_b",
                        "confidence": "high" if p_value < 0.01 else "medium",
                        "reason": f"Strategy B shows {effect_size:.1f}% improvement with statistical significance"
                    }
                else:
                    recommendations = {
                        "recommendation": "implement_a",
                        "confidence": "high" if p_value < 0.01 else "medium",
                        "reason": f"Strategy A shows {abs(effect_size):.1f}% better performance with statistical significance"
                    }
            elif not is_significant and (sample_size_a + sample_size_b) > test.min_sample_size * 4:
                recommendations = {
                    "recommendation": "stop_test",
                    "confidence": "medium",
                    "reason": "No significant difference detected with large sample size"
                }

        return recommendations

    async def _generate_test_summary(self, test: ABTest) -> ABTestSummarySchema:
        """Generate test summary for dashboard."""
        # Calculate days running
        days_running = None
        if test.start_date:
            days_running = (datetime.now(timezone.utc) - test.start_date).days

        # Determine winning strategy
        winning_strategy = None
        if test.current_conversion_rate_b > test.current_conversion_rate_a:
            winning_strategy = test.strategy_b
        elif test.current_conversion_rate_a > test.current_conversion_rate_b:
            winning_strategy = test.strategy_a

        # Calculate projected improvement
        projected_improvement = None
        if test.current_conversion_rate_a > 0:
            improvement = ((test.current_conversion_rate_b - test.current_conversion_rate_a) /
                          test.current_conversion_rate_a * 100)
            projected_improvement = f"{improvement:+.1f}%"

        return ABTestSummarySchema(
            test_id=test.id,
            test_name=test.test_name,
            test_status=test.status,
            strategy_a=test.strategy_a,
            strategy_b=test.strategy_b,
            strategy_a_name=test.strategy_a_name,
            strategy_b_name=test.strategy_b_name,
            current_sample_size_a=test.current_sample_size_a,
            current_sample_size_b=test.current_sample_size_b,
            current_conversion_rate_a=test.current_conversion_rate_a,
            current_conversion_rate_b=test.current_conversion_rate_b,
            is_statistically_significant=test.is_statistically_significant,
            current_p_value=test.current_p_value,
            start_date=test.start_date,
            end_date=test.end_date,
            days_running=days_running,
            winning_strategy=winning_strategy,
            confidence_level="high" if test.current_p_value and test.current_p_value < 0.01 else "medium" if test.current_p_value and test.current_p_value < 0.05 else "low",
            projected_improvement=projected_improvement
        )

    async def _calculate_overall_metrics(self, tests: List[ABTest]) -> Dict[str, Any]:
        """Calculate overall A/B testing metrics."""
        if not tests:
            return {}

        total_tests = len(tests)
        active_tests = len([t for t in tests if t.status == ABTestStatus.ACTIVE])
        significant_tests = len([t for t in tests if t.is_statistically_significant])

        avg_conversion_rate = sum(
            (t.current_conversion_rate_a + t.current_conversion_rate_b) / 2
            for t in tests if t.current_conversion_rate_a > 0 or t.current_conversion_rate_b > 0
        ) / max(1, len([t for t in tests if t.current_conversion_rate_a > 0 or t.current_conversion_rate_b > 0]))

        return {
            "total_tests": total_tests,
            "active_tests": active_tests,
            "significant_tests": significant_tests,
            "significance_rate": (significant_tests / total_tests * 100) if total_tests > 0 else 0,
            "average_conversion_rate": avg_conversion_rate,
            "total_sample_size": sum(t.current_sample_size_a + t.current_sample_size_b for t in tests)
        }

    async def _generate_recent_insights(self, active_tests: List[ABTest]) -> List[str]:
        """Generate recent insights from active tests."""
        insights = []

        if not active_tests:
            return insights

        # Find tests with significant results
        significant_tests = [t for t in active_tests if t.is_statistically_significant]
        if significant_tests:
            insights.append(f"{len(significant_tests)} tests showing statistically significant results")

        # Find tests with large sample sizes
        large_sample_tests = [t for t in active_tests if (t.current_sample_size_a + t.current_sample_size_b) > 10000]
        if large_sample_tests:
            insights.append(f"{len(large_sample_tests)} tests have reached large sample sizes (>10k)")

        # Find best performing strategy
        strategy_performance = {}
        for test in active_tests:
            if test.current_conversion_rate_a > test.current_conversion_rate_b:
                strategy_performance[test.strategy_a] = strategy_performance.get(test.strategy_a, 0) + 1
            elif test.current_conversion_rate_b > test.current_conversion_rate_a:
                strategy_performance[test.strategy_b] = strategy_performance.get(test.strategy_b, 0) + 1

        if strategy_performance:
            best_strategy = max(strategy_performance.keys(), key=lambda k: strategy_performance[k])
            insights.append(f"{best_strategy} strategy showing best performance across tests")

        return insights

    async def _generate_optimization_recommendations(self, active_tests: List[ABTest]) -> List[Dict[str, Any]]:
        """Generate optimization recommendations."""
        recommendations = []

        for test in active_tests:
            # Check if test needs more sample size
            total_sample = test.current_sample_size_a + test.current_sample_size_b
            if total_sample < test.min_sample_size * 2:
                recommendations.append({
                    "type": "sample_size",
                    "test_name": test.test_name,
                    "recommendation": "Increase traffic allocation to reach minimum sample size faster",
                    "priority": "medium"
                })

            # Check for tests running too long
            if test.start_date:
                days_running = (datetime.now(timezone.utc) - test.start_date).days
                if days_running > 30 and not test.is_statistically_significant:
                    recommendations.append({
                        "type": "duration",
                        "test_name": test.test_name,
                        "recommendation": "Consider stopping test - no significance after 30 days",
                        "priority": "high"
                    })

        return recommendations

    async def _calculate_performance_trends(self, active_tests: List[ABTest]) -> Dict[str, Any]:
        """Calculate performance trends."""
        if not active_tests:
            return {}

        # Calculate average conversion rates
        total_conversion_a = sum(t.current_conversion_rate_a for t in active_tests)
        total_conversion_b = sum(t.current_conversion_rate_b for t in active_tests)

        avg_conversion_a = total_conversion_a / len(active_tests) if active_tests else 0
        avg_conversion_b = total_conversion_b / len(active_tests) if active_tests else 0

        return {
            "average_conversion_rate_a": avg_conversion_a,
            "average_conversion_rate_b": avg_conversion_b,
            "overall_improvement": ((avg_conversion_b - avg_conversion_a) / avg_conversion_a * 100) if avg_conversion_a > 0 else 0,
            "tests_favoring_b": len([t for t in active_tests if t.current_conversion_rate_b > t.current_conversion_rate_a])
        }

    async def _get_geographic_performance(self, active_tests: List[ABTest]) -> Dict[str, Any]:
        """Get geographic performance breakdown."""
        # This would integrate with assignment repository to get geographic data
        return {
            "top_performing_countries": [],
            "geographic_distribution": {},
            "country_specific_insights": []
        }

    async def _get_vpn_impact_summary(self, active_tests: List[ABTest]) -> Dict[str, Any]:
        """Get VPN impact summary."""
        vpn_excluding_tests = len([t for t in active_tests if t.exclude_vpn_users])

        return {
            "tests_excluding_vpn": vpn_excluding_tests,
            "vpn_impact_on_performance": 0.0,  # Would be calculated from actual data
            "vpn_detection_rate": 0.0  # Would be calculated from actual data
        }
