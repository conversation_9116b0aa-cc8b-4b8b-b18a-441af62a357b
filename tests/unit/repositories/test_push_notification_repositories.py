"""
Unit tests for push notification repositories.

This module provides comprehensive unit tests for push notification repository classes:
- DeviceTokenRepository: Device token CRUD operations with platform filtering and validation
- NotificationTemplateRepository: Template management with versioning and categorization
- NotificationDeliveryRepository: Delivery tracking with analytics and performance monitoring
- NotificationPreferenceRepository: User notification preferences and bulk preference updates
- NotificationQueueRepository: Queue management with priority handling and batch processing

Implements Task 2.3.2 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.repositories.push_notification_repositories import (
    DeviceTokenRepository, NotificationTemplateRepository, NotificationDeliveryRepository,
    NotificationPreferenceRepository, NotificationQueueRepository
)
from app.models.push_notification_models import (
    DeviceToken, NotificationTemplate, NotificationDelivery,
    NotificationPreference, NotificationQueue,
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)


@pytest.fixture
def mock_db_session():
    """Mock database session for testing."""
    session = AsyncMock(spec=AsyncSession)
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def sample_device_token():
    """Sample device token for testing."""
    return DeviceToken(
        id=uuid4(),
        user_id=1,
        token="test_fcm_token_123",
        platform=DevicePlatform.ANDROID,
        device_id="device_android_123",
        device_name="Samsung Galaxy S21",
        app_version="1.0.0",
        os_version="Android 12",
        is_active=True,
        is_validated=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        last_used_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def sample_notification_template():
    """Sample notification template for testing."""
    return NotificationTemplate(
        id=uuid4(),
        name="Welcome Template",
        category=NotificationCategory.AUTHENTICATION,
        title_template="Welcome {{user_name}}!",
        body_template="Hello {{user_name}}, welcome to our platform!",
        required_variables=["user_name"],
        created_by=1,
        version=1,
        is_active=True,
        usage_count=5,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


class TestDeviceTokenRepository:
    """Test DeviceTokenRepository functionality."""

    @pytest.mark.asyncio
    async def test_create_device_token(self, mock_db_session):
        """Test creating a device token."""
        repository = DeviceTokenRepository(mock_db_session)
        
        device_data = {
            "user_id": 1,
            "token": "test_fcm_token",
            "platform": DevicePlatform.ANDROID,
            "device_id": "device_123",
            "device_name": "Test Device",
            "is_active": True
        }
        
        # Mock the database operations
        mock_device = DeviceToken(**device_data, id=uuid4())
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch.object(repository, '_create_instance', return_value=mock_device):
            result = await repository.create(device_data)
        
        assert result.user_id == 1
        assert result.token == "test_fcm_token"
        assert result.platform == DevicePlatform.ANDROID
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_device_by_token(self, mock_db_session, sample_device_token):
        """Test getting device by FCM token."""
        repository = DeviceTokenRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none = AsyncMock(return_value=sample_device_token)
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_by_token("test_fcm_token_123")
        
        assert result == sample_device_token
        assert result.token == "test_fcm_token_123"
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_devices_by_platform(self, mock_db_session, sample_device_token):
        """Test getting user devices filtered by platform."""
        repository = DeviceTokenRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.scalars = MagicMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[sample_device_token])
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_user_devices(1, DevicePlatform.ANDROID, True)
        
        assert len(result) == 1
        assert result[0].platform == DevicePlatform.ANDROID
        assert result[0].user_id == 1
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_last_used(self, mock_db_session, sample_device_token):
        """Test updating device last used timestamp."""
        repository = DeviceTokenRepository(mock_db_session)
        
        # Mock get operation
        with patch.object(repository, 'get', return_value=sample_device_token):
            result = await repository.update_last_used(sample_device_token.id)
        
        assert result is True
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_inactive_tokens(self, mock_db_session):
        """Test cleaning up inactive device tokens."""
        repository = DeviceTokenRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.rowcount = 5  # 5 tokens cleaned up
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.cleanup_inactive_tokens(90)
        
        assert result == 5
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_update_validation_status(self, mock_db_session):
        """Test bulk updating device validation status."""
        repository = DeviceTokenRepository(mock_db_session)
        
        device_ids = [uuid4(), uuid4(), uuid4()]
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.rowcount = 3  # 3 tokens updated
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.bulk_update_validation_status(device_ids, True)
        
        assert result == 3
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_validation_stats(self, mock_db_session):
        """Test getting device validation statistics."""
        repository = DeviceTokenRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.fetchone = AsyncMock(return_value=(100, 85, 15))  # total, validated, unvalidated
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_validation_stats()
        
        assert result["total_devices"] == 100
        assert result["validated_devices"] == 85
        assert result["unvalidated_devices"] == 15
        assert result["validation_rate"] == 85.0
        mock_db_session.execute.assert_called_once()


class TestNotificationTemplateRepository:
    """Test NotificationTemplateRepository functionality."""

    @pytest.mark.asyncio
    async def test_create_template(self, mock_db_session):
        """Test creating a notification template."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        template_data = {
            "name": "Test Template",
            "category": NotificationCategory.SYSTEM,
            "title_template": "Test {{variable}}",
            "body_template": "Test message with {{variable}}",
            "required_variables": ["variable"],
            "created_by": 1,
            "version": 1
        }
        
        # Mock the database operations
        mock_template = NotificationTemplate(**template_data, id=uuid4())
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch.object(repository, '_create_instance', return_value=mock_template):
            result = await repository.create(template_data)
        
        assert result.name == "Test Template"
        assert result.category == NotificationCategory.SYSTEM
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_category(self, mock_db_session, sample_notification_template):
        """Test getting templates by category."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.scalars = MagicMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[sample_notification_template])
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Mock count query
        mock_count_result = MagicMock()
        mock_count_result.scalar = AsyncMock(return_value=1)
        mock_db_session.execute = AsyncMock(side_effect=[mock_result, mock_count_result])
        
        result = await repository.get_by_category(NotificationCategory.AUTHENTICATION, True)
        
        assert result.total == 1
        assert len(result.items) == 1
        assert result.items[0].category == NotificationCategory.AUTHENTICATION

    @pytest.mark.asyncio
    async def test_create_new_version(self, mock_db_session, sample_notification_template):
        """Test creating a new template version."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        # Mock getting existing template
        with patch.object(repository, 'get', return_value=sample_notification_template):
            # Mock creating new version
            new_version = NotificationTemplate(
                id=uuid4(),
                name=sample_notification_template.name,
                category=sample_notification_template.category,
                title_template="Updated {{user_name}}!",
                body_template="Updated welcome message",
                version=2,
                created_by=1
            )
            
            with patch.object(repository, 'create', return_value=new_version):
                result = await repository.create_new_version(
                    sample_notification_template.id,
                    {"title_template": "Updated {{user_name}}!"},
                    1
                )
        
        assert result.version == 2
        assert result.title_template == "Updated {{user_name}}!"

    @pytest.mark.asyncio
    async def test_validate_template_variables(self, mock_db_session, sample_notification_template):
        """Test validating template variables."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        # Mock getting template
        with patch.object(repository, 'get', return_value=sample_notification_template):
            # Test with valid variables
            result = await repository.validate_template_variables(
                sample_notification_template.id,
                {"user_name": "John Doe"}
            )
        
        assert result["valid"] is True
        assert result["missing_variables"] == []

    @pytest.mark.asyncio
    async def test_validate_template_variables_missing(self, mock_db_session, sample_notification_template):
        """Test validating template variables with missing variables."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        # Mock getting template
        with patch.object(repository, 'get', return_value=sample_notification_template):
            # Test with missing variables
            result = await repository.validate_template_variables(
                sample_notification_template.id,
                {}  # No variables provided
            )
        
        assert result["valid"] is False
        assert "user_name" in result["missing_variables"]

    @pytest.mark.asyncio
    async def test_increment_usage_count(self, mock_db_session, sample_notification_template):
        """Test incrementing template usage count."""
        repository = NotificationTemplateRepository(mock_db_session)
        
        # Mock get operation
        with patch.object(repository, 'get', return_value=sample_notification_template):
            result = await repository.increment_usage_count(sample_notification_template.id)
        
        assert result is True
        mock_db_session.commit.assert_called_once()


class TestNotificationDeliveryRepository:
    """Test NotificationDeliveryRepository functionality."""

    @pytest.mark.asyncio
    async def test_create_delivery(self, mock_db_session):
        """Test creating a notification delivery."""
        repository = NotificationDeliveryRepository(mock_db_session)
        
        delivery_data = {
            "user_id": 1,
            "device_token_id": uuid4(),
            "title": "Test Notification",
            "body": "Test message",
            "status": NotificationStatus.PENDING,
            "priority": NotificationPriority.NORMAL
        }
        
        # Mock the database operations
        mock_delivery = NotificationDelivery(**delivery_data, id=uuid4())
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch.object(repository, '_create_instance', return_value=mock_delivery):
            result = await repository.create(delivery_data)
        
        assert result.title == "Test Notification"
        assert result.status == NotificationStatus.PENDING
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_delivery_status(self, mock_db_session):
        """Test updating delivery status."""
        repository = NotificationDeliveryRepository(mock_db_session)
        
        delivery_id = uuid4()
        mock_delivery = NotificationDelivery(
            id=delivery_id,
            user_id=1,
            device_token_id=uuid4(),
            title="Test",
            body="Test message",
            status=NotificationStatus.PENDING
        )
        
        # Mock get operation
        with patch.object(repository, 'get', return_value=mock_delivery):
            result = await repository.update_delivery_status(
                delivery_id,
                NotificationStatus.SENT,
                fcm_message_id="fcm_123"
            )
        
        assert result is True
        assert mock_delivery.status == NotificationStatus.SENT
        assert mock_delivery.fcm_message_id == "fcm_123"
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_failed_deliveries_for_retry(self, mock_db_session):
        """Test getting failed deliveries for retry."""
        repository = NotificationDeliveryRepository(mock_db_session)
        
        # Mock query result
        failed_delivery = NotificationDelivery(
            id=uuid4(),
            user_id=1,
            device_token_id=uuid4(),
            title="Failed Notification",
            body="Failed message",
            status=NotificationStatus.FAILED,
            retry_count=1
        )
        
        mock_result = MagicMock()
        mock_result.scalars = MagicMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[failed_delivery])
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_failed_deliveries_for_retry(10)
        
        assert len(result) == 1
        assert result[0].status == NotificationStatus.FAILED
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_delivery_analytics(self, mock_db_session):
        """Test getting delivery analytics."""
        repository = NotificationDeliveryRepository(mock_db_session)
        
        # Mock analytics query result
        mock_result = MagicMock()
        mock_result.fetchone = AsyncMock(return_value=(100, 85, 10, 5))  # total, sent, failed, pending
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        start_date = datetime.now(timezone.utc) - timedelta(days=7)
        end_date = datetime.now(timezone.utc)
        
        result = await repository.get_delivery_analytics(start_date, end_date)
        
        assert result["total_deliveries"] == 100
        assert result["successful_deliveries"] == 85
        assert result["failed_deliveries"] == 10
        assert result["pending_deliveries"] == 5
        assert result["success_rate"] == 85.0
        mock_db_session.execute.assert_called_once()


class TestNotificationPreferenceRepository:
    """Test NotificationPreferenceRepository functionality."""

    @pytest.mark.asyncio
    async def test_get_by_user_id(self, mock_db_session):
        """Test getting preferences by user ID."""
        repository = NotificationPreferenceRepository(mock_db_session)
        
        preference = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True,
            authentication_notifications=NotificationFrequency.IMMEDIATE
        )
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none = AsyncMock(return_value=preference)
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_by_user_id(1)
        
        assert result.user_id == 1
        assert result.push_notifications_enabled is True
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_default_preferences(self, mock_db_session):
        """Test creating default preferences for a user."""
        repository = NotificationPreferenceRepository(mock_db_session)
        
        # Mock the database operations
        mock_preference = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True,
            authentication_notifications=NotificationFrequency.IMMEDIATE
        )
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch.object(repository, '_create_instance', return_value=mock_preference):
            result = await repository.create_default_preferences(1)
        
        assert result.user_id == 1
        assert result.push_notifications_enabled is True
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_update_category_preferences(self, mock_db_session):
        """Test bulk updating category preferences."""
        repository = NotificationPreferenceRepository(mock_db_session)
        
        user_ids = [1, 2, 3]
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.rowcount = 3  # 3 preferences updated
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.bulk_update_category_preferences(
            user_ids,
            NotificationCategory.PROMOTIONAL,
            NotificationFrequency.DISABLED
        )
        
        assert result == 3
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()


class TestNotificationQueueRepository:
    """Test NotificationQueueRepository functionality."""

    @pytest.mark.asyncio
    async def test_get_pending_notifications(self, mock_db_session):
        """Test getting pending notifications from queue."""
        repository = NotificationQueueRepository(mock_db_session)
        
        # Mock queue items
        queue_item = NotificationQueue(
            id=uuid4(),
            user_id=1,
            device_token_id=uuid4(),
            title="Queued Notification",
            body="Queued message",
            status=NotificationStatus.PENDING,
            priority=NotificationPriority.HIGH
        )
        
        mock_result = MagicMock()
        mock_result.scalars = MagicMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[queue_item])
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_pending_notifications(10, NotificationPriority.HIGH)
        
        assert len(result) == 1
        assert result[0].status == NotificationStatus.PENDING
        assert result[0].priority == NotificationPriority.HIGH
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_as_processing(self, mock_db_session):
        """Test marking notifications as processing."""
        repository = NotificationQueueRepository(mock_db_session)
        
        queue_ids = [uuid4(), uuid4()]
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.rowcount = 2  # 2 items marked as processing
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.mark_as_processing(queue_ids)
        
        assert result == 2
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_processing(self, mock_db_session):
        """Test completing notification processing."""
        repository = NotificationQueueRepository(mock_db_session)
        
        queue_id = uuid4()
        mock_queue_item = NotificationQueue(
            id=queue_id,
            user_id=1,
            device_token_id=uuid4(),
            title="Test",
            body="Test message",
            status=NotificationStatus.PROCESSING
        )
        
        # Mock get operation
        with patch.object(repository, 'get', return_value=mock_queue_item):
            result = await repository.complete_processing(
                queue_id,
                NotificationStatus.SENT
            )
        
        assert result is True
        assert mock_queue_item.status == NotificationStatus.SENT
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_expired_notifications(self, mock_db_session):
        """Test cleaning up expired notifications."""
        repository = NotificationQueueRepository(mock_db_session)
        
        # Mock query result
        mock_result = MagicMock()
        mock_result.rowcount = 10  # 10 expired notifications cleaned
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.cleanup_expired_notifications()
        
        assert result == 10
        mock_db_session.execute.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_queue_stats(self, mock_db_session):
        """Test getting queue statistics."""
        repository = NotificationQueueRepository(mock_db_session)
        
        # Mock stats query result
        mock_result = MagicMock()
        mock_result.fetchone = AsyncMock(return_value=(50, 30, 15, 5))  # pending, processing, sent, failed
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        result = await repository.get_queue_stats()
        
        assert result["pending_count"] == 50
        assert result["processing_count"] == 30
        assert result["sent_count"] == 15
        assert result["failed_count"] == 5
        assert result["total_count"] == 100
        mock_db_session.execute.assert_called_once()
