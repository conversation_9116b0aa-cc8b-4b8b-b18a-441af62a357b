"""
Unit tests for payout models.

This module provides comprehensive unit tests for payout-related database models:
- VendorPayout: Vendor settlement management with automated calculations
- EscrowAccount: Secure fund holding and dispute management
- Enum validation and business logic constraints
- Automated payout processing and scheduling features
- Escrow release conditions and dispute resolution

Implements Phase 1 Payment & Transaction Management System with >85% test coverage.
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4
from sqlalchemy.exc import IntegrityError

from app.models.payout_models import (
    VendorPayout, EscrowAccount, PayoutStatus, EscrowStatus, ReleaseCondition
)
from app.core.payment.config import PaymentProviderType


class TestVendorPayoutModel:
    """Test VendorPayout model functionality."""

    def test_vendor_payout_creation(self):
        """Test basic vendor payout creation."""
        period_start = datetime.now(timezone.utc) - timedelta(days=7)
        period_end = datetime.now(timezone.utc)

        payout = VendorPayout(
            vendor_id=1,
            period_start=period_start,
            period_end=period_end,
            total_earnings=Decimal("5000.00"),
            platform_fee=Decimal("250.00"),
            processing_fee=Decimal("50.00"),
            adjustment_amount=Decimal("0.00"),
            net_amount=Decimal("4700.00"),
            currency="NGN",
            provider=PaymentProviderType.PAYSTACK,
            status=PayoutStatus.PENDING,
            reference_id="PAYOUT-2025-001234"
        )

        assert payout.vendor_id == 1
        assert payout.period_start == period_start
        assert payout.period_end == period_end
        assert payout.total_earnings == Decimal("5000.00")
        assert payout.platform_fee == Decimal("250.00")
        assert payout.processing_fee == Decimal("50.00")
        assert payout.adjustment_amount == Decimal("0.00")
        assert payout.net_amount == Decimal("4700.00")
        assert payout.currency == "NGN"
        assert payout.provider == PaymentProviderType.PAYSTACK
        assert payout.status == PayoutStatus.PENDING
        assert payout.reference_id == "PAYOUT-2025-001234"

    def test_payout_status_enum_values(self):
        """Test payout status enum values."""
        expected_statuses = {
            "pending", "processing", "completed", "failed",
            "cancelled", "reversed", "on_hold"
        }
        actual_statuses = {status.value for status in PayoutStatus}
        assert actual_statuses == expected_statuses

    def test_vendor_payout_defaults(self):
        """Test vendor payout default values."""
        period_start = datetime.now(timezone.utc) - timedelta(days=7)
        period_end = datetime.now(timezone.utc)

        payout = VendorPayout(
            vendor_id=1,
            period_start=period_start,
            period_end=period_end,
            reference_id="PAYOUT-DEFAULTS-001"
        )

        # Test default values (SQLAlchemy defaults are applied on save, not instantiation)
        # For unit tests, we test the column defaults are defined correctly
        from app.models.payout_models import VendorPayout as VendorPayoutModel

        # Check that defaults are defined in the model
        assert VendorPayoutModel.__table__.columns['total_earnings'].default.arg == Decimal("0.00")
        assert VendorPayoutModel.__table__.columns['platform_fee'].default.arg == Decimal("0.00")
        assert VendorPayoutModel.__table__.columns['processing_fee'].default.arg == Decimal("0.00")
        assert VendorPayoutModel.__table__.columns['adjustment_amount'].default.arg == Decimal("0.00")
        assert VendorPayoutModel.__table__.columns['net_amount'].default.arg == Decimal("0.00")
        assert VendorPayoutModel.__table__.columns['currency'].default.arg == "NGN"
        assert VendorPayoutModel.__table__.columns['status'].default.arg == 'pending'
        assert VendorPayoutModel.__table__.columns['retry_count'].default.arg == 0

    def test_vendor_payout_properties(self):
        """Test vendor payout model properties."""
        period_start = datetime.now(timezone.utc) - timedelta(days=7)
        period_end = datetime.now(timezone.utc)

        # Test successful payout
        successful_payout = VendorPayout(
            vendor_id=1,
            period_start=period_start,
            period_end=period_end,
            status=PayoutStatus.COMPLETED,
            reference_id="PAYOUT-SUCCESS-001",
            processed_at=datetime.now(timezone.utc)
        )

        assert successful_payout.is_successful is True
        assert successful_payout.is_failed is False

        # Test failed payout
        failed_payout = VendorPayout(
            vendor_id=2,
            period_start=period_start,
            period_end=period_end,
            status=PayoutStatus.FAILED,
            reference_id="PAYOUT-FAILED-001"
        )

        assert failed_payout.is_successful is False
        assert failed_payout.is_failed is True

        # Test pending payout
        pending_payout = VendorPayout(
            vendor_id=3,
            period_start=period_start,
            period_end=period_end,
            status=PayoutStatus.PENDING,
            reference_id="PAYOUT-PENDING-001"
        )

        assert pending_payout.is_successful is False
        assert pending_payout.is_failed is False

    def test_vendor_payout_calculations(self):
        """Test vendor payout calculation properties."""
        payout = VendorPayout(
            vendor_id=1,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            total_earnings=Decimal("5000.00"),
            platform_fee=Decimal("250.00"),
            processing_fee=Decimal("50.00"),
            adjustment_amount=Decimal("-100.00"),  # Negative adjustment
            reference_id="PAYOUT-CALC-001"
        )

        # Test total fees calculation (available property)
        assert payout.total_fees == Decimal("300.00")  # 250 + 50

    def test_vendor_payout_period_validation(self):
        """Test vendor payout period validation."""
        period_start = datetime.now(timezone.utc) - timedelta(days=7)
        period_end = datetime.now(timezone.utc)

        payout = VendorPayout(
            vendor_id=1,
            period_start=period_start,
            period_end=period_end,
            reference_id="PAYOUT-PERIOD-001"
        )

        # Test period validation
        assert payout.period_start < payout.period_end

    def test_vendor_payout_scheduling(self):
        """Test vendor payout scheduling features."""
        scheduled_time = datetime.now(timezone.utc) + timedelta(hours=24)

        payout = VendorPayout(
            vendor_id=1,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            scheduled_at=scheduled_time,
            reference_id="PAYOUT-SCHEDULED-001"
        )

        assert payout.scheduled_at == scheduled_time

        # Test unscheduled payout
        unscheduled_payout = VendorPayout(
            vendor_id=2,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            reference_id="PAYOUT-UNSCHEDULED-001"
        )

        assert unscheduled_payout.scheduled_at is None

    def test_vendor_payout_failure_tracking(self):
        """Test vendor payout failure tracking."""
        payout = VendorPayout(
            vendor_id=1,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            status=PayoutStatus.FAILED,
            failure_reason="Bank account not found",
            failure_code="ACCOUNT_NOT_FOUND",
            retry_count=2,
            reference_id="PAYOUT-FAILURE-001"
        )

        assert payout.failure_reason == "Bank account not found"
        assert payout.failure_code == "ACCOUNT_NOT_FOUND"
        assert payout.retry_count == 2

    def test_vendor_payout_repr(self):
        """Test vendor payout string representation."""
        payout = VendorPayout(
            id=123,
            vendor_id=456,
            reference_id="PAYOUT-REPR-001",
            status=PayoutStatus.COMPLETED
        )

        expected_repr = "<VendorPayout(id=123, vendor_id=456, ref='PAYOUT-REPR-001', status='PayoutStatus.COMPLETED')>"
        assert repr(payout) == expected_repr


class TestEscrowAccountModel:
    """Test EscrowAccount model functionality."""

    def test_escrow_account_creation(self):
        """Test basic escrow account creation."""
        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            currency="NGN",
            status=EscrowStatus.ACTIVE,
            release_condition=ReleaseCondition.SERVICE_COMPLETION,
            hold_until=datetime.now(timezone.utc) + timedelta(days=7),
            auto_release_enabled=True
        )

        assert escrow.booking_id == 1
        assert escrow.payment_id == 1
        assert escrow.amount == Decimal("1000.00")
        assert escrow.currency == "NGN"
        assert escrow.status == EscrowStatus.ACTIVE
        assert escrow.release_condition == ReleaseCondition.SERVICE_COMPLETION
        assert escrow.hold_until is not None
        assert escrow.auto_release_enabled is True

    def test_escrow_status_enum_values(self):
        """Test escrow status enum values."""
        expected_statuses = {
            "active", "held", "released", "disputed",
            "expired", "cancelled"
        }
        actual_statuses = {status.value for status in EscrowStatus}
        assert actual_statuses == expected_statuses

    def test_release_condition_enum_values(self):
        """Test release condition enum values."""
        expected_conditions = {
            "service_completion", "customer_confirmation",
            "time_based", "manual_release", "dispute_resolution"
        }
        actual_conditions = {condition.value for condition in ReleaseCondition}
        assert actual_conditions == expected_conditions

    def test_escrow_account_defaults(self):
        """Test escrow account default values."""
        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00")
        )

        # Test default values (SQLAlchemy defaults are applied on save, not instantiation)
        # For unit tests, we test the column defaults are defined correctly
        from app.models.payout_models import EscrowAccount as EscrowAccountModel

        # Check that defaults are defined in the model
        assert EscrowAccountModel.__table__.columns['currency'].default.arg == "NGN"
        assert EscrowAccountModel.__table__.columns['status'].default.arg == 'active'
        assert EscrowAccountModel.__table__.columns['release_condition'].default.arg == 'service_completion'
        assert EscrowAccountModel.__table__.columns['auto_release_enabled'].default.arg is True

    def test_escrow_account_properties(self):
        """Test escrow account model properties."""
        # Test active escrow
        active_escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            status=EscrowStatus.ACTIVE
        )

        assert active_escrow.is_active is True
        assert active_escrow.is_released is False
        assert active_escrow.is_disputed is False

        # Test released escrow
        released_escrow = EscrowAccount(
            booking_id=2,
            payment_id=2,
            amount=Decimal("500.00"),
            status=EscrowStatus.RELEASED,
            released_at=datetime.now(timezone.utc)
        )

        assert released_escrow.is_active is False
        assert released_escrow.is_released is True
        assert released_escrow.is_disputed is False

        # Test disputed escrow
        disputed_escrow = EscrowAccount(
            booking_id=3,
            payment_id=3,
            amount=Decimal("750.00"),
            status=EscrowStatus.DISPUTED,
            disputed_at=datetime.now(timezone.utc)
        )

        assert disputed_escrow.is_active is False
        assert disputed_escrow.is_released is False
        assert disputed_escrow.is_disputed is True

    def test_escrow_account_expiry(self):
        """Test escrow account expiry functionality."""
        # Test escrow ready for release (available property)
        ready_escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            status=EscrowStatus.ACTIVE,
            auto_release_enabled=True,
            hold_until=datetime.now(timezone.utc) - timedelta(hours=1)
        )

        assert ready_escrow.is_ready_for_release is True

        # Test escrow not ready for release
        not_ready_escrow = EscrowAccount(
            booking_id=2,
            payment_id=2,
            amount=Decimal("500.00"),
            status=EscrowStatus.ACTIVE,
            auto_release_enabled=True,
            hold_until=datetime.now(timezone.utc) + timedelta(hours=24)
        )

        assert not_ready_escrow.is_ready_for_release is False

        # Test escrow without auto release
        no_auto_escrow = EscrowAccount(
            booking_id=3,
            payment_id=3,
            amount=Decimal("750.00"),
            status=EscrowStatus.ACTIVE,
            auto_release_enabled=False,
            hold_until=None
        )

        assert no_auto_escrow.is_ready_for_release is False

    def test_escrow_account_release_tracking(self):
        """Test escrow account release tracking."""
        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            status=EscrowStatus.RELEASED,
            released_at=datetime.now(timezone.utc),
            released_by=123,
            release_reason="Service completed successfully"
        )

        assert escrow.released_at is not None
        assert escrow.released_by == 123
        assert escrow.release_reason == "Service completed successfully"

    def test_escrow_account_dispute_tracking(self):
        """Test escrow account dispute tracking."""
        dispute_time = datetime.now(timezone.utc)
        resolution_time = dispute_time + timedelta(days=3)

        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            status=EscrowStatus.DISPUTED,
            disputed_at=dispute_time,
            disputed_by=456,
            dispute_reason="Service not delivered as promised",
            dispute_resolved_at=resolution_time
        )

        assert escrow.disputed_at == dispute_time
        assert escrow.disputed_by == 456
        assert escrow.dispute_reason == "Service not delivered as promised"
        assert escrow.dispute_resolved_at == resolution_time

    def test_escrow_account_metadata(self):
        """Test escrow account metadata handling."""
        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            escrow_metadata={
                "service_type": "cultural_tour",
                "expected_completion": "2025-02-15",
                "special_conditions": ["weather_dependent"]
            },
            release_conditions_data={
                "completion_criteria": ["tour_completed", "customer_satisfied"],
                "auto_release_delay_hours": 24
            }
        )

        assert escrow.escrow_metadata["service_type"] == "cultural_tour"
        assert escrow.release_conditions_data["auto_release_delay_hours"] == 24

    def test_escrow_account_repr(self):
        """Test escrow account string representation."""
        escrow = EscrowAccount(
            id=789,
            booking_id=123,
            payment_id=456,
            amount=Decimal("1000.00"),
            status=EscrowStatus.ACTIVE
        )

        expected_repr = "<EscrowAccount(id=789, booking_id=123, amount=1000.00, status='EscrowStatus.ACTIVE')>"
        assert repr(escrow) == expected_repr


class TestPayoutModelConstraints:
    """Test payout model constraints and validations."""

    def test_vendor_payout_amount_constraints(self):
        """Test vendor payout amount validation constraints."""
        payout = VendorPayout(
            vendor_id=1,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            total_earnings=Decimal("5000.00"),  # Must be >= 0
            platform_fee=Decimal("250.00"),     # Must be >= 0
            processing_fee=Decimal("50.00"),    # Must be >= 0
            net_amount=Decimal("4700.00"),      # Must be >= 0
            retry_count=2,                      # Must be >= 0
            reference_id="PAYOUT-CONSTRAINT-001"
        )

        assert payout.total_earnings >= 0
        assert payout.platform_fee >= 0
        assert payout.processing_fee >= 0
        assert payout.net_amount >= 0
        assert payout.retry_count >= 0

    def test_vendor_payout_period_constraints(self):
        """Test vendor payout period validation constraints."""
        period_start = datetime.now(timezone.utc) - timedelta(days=7)
        period_end = datetime.now(timezone.utc)

        payout = VendorPayout(
            vendor_id=1,
            period_start=period_start,
            period_end=period_end,
            reference_id="PAYOUT-PERIOD-CONSTRAINT-001"
        )

        # Test period start < period end constraint
        assert payout.period_start < payout.period_end

    def test_escrow_account_amount_constraints(self):
        """Test escrow account amount validation constraints."""
        escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00")  # Must be > 0
        )

        assert escrow.amount > 0


class TestPayoutModelIntegration:
    """Test payout model integration features."""

    def test_vendor_payout_provider_integration(self):
        """Test vendor payout integration with different providers."""
        # Paystack payout
        paystack_payout = VendorPayout(
            vendor_id=1,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            provider=PaymentProviderType.PAYSTACK,
            provider_payout_id="payout_paystack_123",
            reference_id="PAYOUT-PAYSTACK-001"
        )

        assert paystack_payout.provider == PaymentProviderType.PAYSTACK
        assert paystack_payout.provider_payout_id == "payout_paystack_123"

        # Stripe payout
        stripe_payout = VendorPayout(
            vendor_id=2,
            period_start=datetime.now(timezone.utc) - timedelta(days=7),
            period_end=datetime.now(timezone.utc),
            provider=PaymentProviderType.STRIPE,
            provider_payout_id="po_stripe_456",
            reference_id="PAYOUT-STRIPE-001"
        )

        assert stripe_payout.provider == PaymentProviderType.STRIPE
        assert stripe_payout.provider_payout_id == "po_stripe_456"

    def test_escrow_release_conditions(self):
        """Test escrow account release condition scenarios."""
        # Service completion release
        service_escrow = EscrowAccount(
            booking_id=1,
            payment_id=1,
            amount=Decimal("1000.00"),
            release_condition=ReleaseCondition.SERVICE_COMPLETION
        )

        assert service_escrow.release_condition == ReleaseCondition.SERVICE_COMPLETION

        # Time-based release
        time_escrow = EscrowAccount(
            booking_id=2,
            payment_id=2,
            amount=Decimal("500.00"),
            release_condition=ReleaseCondition.TIME_BASED,
            hold_until=datetime.now(timezone.utc) + timedelta(days=7)
        )

        assert time_escrow.release_condition == ReleaseCondition.TIME_BASED
        assert time_escrow.hold_until is not None

        # Manual release
        manual_escrow = EscrowAccount(
            booking_id=3,
            payment_id=3,
            amount=Decimal("750.00"),
            release_condition=ReleaseCondition.MANUAL_RELEASE,
            auto_release_enabled=False
        )

        assert manual_escrow.release_condition == ReleaseCondition.MANUAL_RELEASE
        assert manual_escrow.auto_release_enabled is False
