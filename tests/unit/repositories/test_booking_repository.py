"""
Unit tests for booking repository.

This module provides comprehensive unit tests for booking repository functionality:
- BookingRepository: CRUD operations and advanced querying
- BookingCommunicationRepository: Communication data access
- BookingModificationRepository: Modification workflow data access
- Performance optimization and error handling
- Pagination and filtering capabilities

Implements Task 4.1.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, timedelta
from decimal import Decimal
from uuid import uuid4
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from app.repositories.booking_repository import BookingRepository
from app.repositories.booking_communication_repository import (
    BookingCommunicationRepository, BookingModificationRepository
)
from app.repositories.base import PaginationParams, QueryResult, RepositoryError
from app.models.booking import (
    Booking, BookingStatusHistory, BookingCommunication, BookingModification,
    BookingStatus, VendorResponseType, BookingPriority, CommunicationType, ModificationType
)


class TestBookingRepository:
    """Test BookingRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.add = MagicMock()
        return session

    @pytest.fixture
    def booking_repository(self, mock_db_session):
        """Create booking repository with mocked session."""
        return BookingRepository(mock_db_session)

    @pytest.fixture
    def sample_booking_data(self):
        """Sample booking data for testing."""
        return {
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_date": date(2025, 2, 15),
            "booking_time": datetime(2025, 2, 15, 14, 30).time(),
            "duration_hours": Decimal("2.5"),
            "participant_count": 2,
            "special_requirements": "Vegetarian meal",
            "base_price": Decimal("150.00"),
            "total_amount": Decimal("150.00")
        }

    @pytest.fixture
    def sample_booking(self, sample_booking_data):
        """Sample booking instance."""
        booking = Booking(**sample_booking_data)
        booking.id = 123
        booking.uuid = uuid4()
        booking.booking_reference = "BK-2025-001234"
        booking.status = BookingStatus.PENDING
        booking.created_at = datetime.now()
        booking.updated_at = datetime.now()
        return booking

    @pytest.mark.asyncio
    async def test_create_booking_success(self, booking_repository, mock_db_session, sample_booking_data):
        """Test successful booking creation."""
        # Mock the _generate_booking_reference method
        with patch.object(booking_repository, '_generate_booking_reference', return_value="BK-2025-001234"):
            # Mock the create method from BaseRepository
            expected_booking = Booking(**sample_booking_data)
            expected_booking.id = 123
            expected_booking.booking_reference = "BK-2025-001234"
            expected_booking.status = BookingStatus.PENDING

            with patch.object(booking_repository, 'create', return_value=expected_booking):
                # Mock the _create_status_history method
                with patch.object(booking_repository, '_create_status_history', return_value=AsyncMock()):
                    result = await booking_repository.create_booking(
                        customer_id=1,
                        vendor_id=2,
                        service_id=3,
                        booking_data=sample_booking_data
                    )

                    assert result.id == 123
                    assert result.booking_reference == "BK-2025-001234"
                    assert result.status == BookingStatus.PENDING
                    assert result.customer_id == 1
                    assert result.vendor_id == 2
                    assert result.service_id == 3

    @pytest.mark.asyncio
    async def test_create_booking_failure(self, booking_repository, mock_db_session):
        """Test booking creation failure."""
        with patch.object(booking_repository, '_generate_booking_reference', return_value="BK-2025-001234"):
            with patch.object(booking_repository, 'create', side_effect=Exception("Database error")):
                with pytest.raises(RepositoryError) as exc_info:
                    await booking_repository.create_booking(
                        customer_id=1,
                        vendor_id=2,
                        service_id=3,
                        booking_data={}
                    )

                assert "Failed to create booking" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_booking_with_details(self, booking_repository, mock_db_session, sample_booking):
        """Test getting booking with related data."""
        # Mock the database query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_booking
        mock_db_session.execute.return_value = mock_result

        result = await booking_repository.get_booking_with_details(123)

        assert result == sample_booking
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_booking_with_details_not_found(self, booking_repository, mock_db_session):
        """Test getting booking with details when not found."""
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        result = await booking_repository.get_booking_with_details(999)

        assert result is None

    @pytest.mark.asyncio
    async def test_get_bookings_by_customer(self, booking_repository, mock_db_session, sample_booking):
        """Test getting bookings by customer."""
        # Mock the _execute_paginated_query method
        expected_result = QueryResult(
            items=[sample_booking],
            total=1,
            page=1,
            size=20,
            has_next=False,
            has_previous=False
        )

        with patch.object(booking_repository, '_execute_paginated_query', return_value=expected_result):
            result = await booking_repository.get_bookings_by_customer(
                customer_id=1,
                status_filter=[BookingStatus.PENDING, BookingStatus.CONFIRMED],
                pagination=PaginationParams(page=1, size=20)
            )

            assert result.total == 1
            assert len(result.items) == 1
            assert result.items[0] == sample_booking

    @pytest.mark.asyncio
    async def test_get_bookings_by_vendor(self, booking_repository, mock_db_session, sample_booking):
        """Test getting bookings by vendor."""
        expected_result = QueryResult(
            items=[sample_booking],
            total=1,
            page=1,
            size=20,
            has_next=False,
            has_previous=False
        )

        with patch.object(booking_repository, '_execute_paginated_query', return_value=expected_result):
            result = await booking_repository.get_bookings_by_vendor(
                vendor_id=2,
                status_filter=[BookingStatus.CONFIRMED],
                date_range=(date(2025, 2, 1), date(2025, 2, 28)),
                pagination=PaginationParams(page=1, size=20)
            )

            assert result.total == 1
            assert len(result.items) == 1
            assert result.items[0] == sample_booking

    @pytest.mark.asyncio
    async def test_get_pending_vendor_responses(self, booking_repository, mock_db_session, sample_booking):
        """Test getting pending vendor responses."""
        # Set up booking as pending vendor response
        sample_booking.status = BookingStatus.PENDING
        sample_booking.vendor_response_type = None
        sample_booking.vendor_response_deadline = datetime.now() + timedelta(hours=12)

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_booking]
        mock_db_session.execute.return_value = mock_result

        result = await booking_repository.get_pending_vendor_responses(vendor_id=2)

        assert len(result) == 1
        assert result[0] == sample_booking

    @pytest.mark.asyncio
    async def test_update_booking_status_success(self, booking_repository, mock_db_session, sample_booking):
        """Test successful booking status update."""
        # Mock the get method
        with patch.object(booking_repository, 'get', return_value=sample_booking):
            # Mock the _create_status_history method
            with patch.object(booking_repository, '_create_status_history', return_value=AsyncMock()):
                result = await booking_repository.update_booking_status(
                    booking_id=123,
                    new_status=BookingStatus.CONFIRMED,
                    changed_by=1,
                    change_reason="Vendor approved",
                    change_notes="All requirements met"
                )

                assert result.status == BookingStatus.CONFIRMED
                mock_db_session.commit.assert_called_once()
                mock_db_session.refresh.assert_called_once_with(sample_booking)

    @pytest.mark.asyncio
    async def test_update_booking_status_not_found(self, booking_repository, mock_db_session):
        """Test booking status update when booking not found."""
        with patch.object(booking_repository, 'get', return_value=None):
            result = await booking_repository.update_booking_status(
                booking_id=999,
                new_status=BookingStatus.CONFIRMED
            )

            assert result is None

    @pytest.mark.asyncio
    async def test_update_vendor_response_success(self, booking_repository, mock_db_session, sample_booking):
        """Test successful vendor response update."""
        with patch.object(booking_repository, 'get', return_value=sample_booking):
            result = await booking_repository.update_vendor_response(
                booking_id=123,
                response_type=VendorResponseType.APPROVED,
                vendor_notes="Approved with all requirements",
                vendor_id=2
            )

            assert result.vendor_response_type == VendorResponseType.APPROVED
            assert result.vendor_notes == "Approved with all requirements"
            assert result.status == BookingStatus.CONFIRMED
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_vendor_response_unauthorized(self, booking_repository, mock_db_session, sample_booking):
        """Test vendor response update with unauthorized vendor."""
        with patch.object(booking_repository, 'get', return_value=sample_booking):
            with pytest.raises(RepositoryError) as exc_info:
                await booking_repository.update_vendor_response(
                    booking_id=123,
                    response_type=VendorResponseType.APPROVED,
                    vendor_id=999  # Wrong vendor ID
                )

            assert "Vendor not authorized" in str(exc_info.value) or "Failed to update vendor response" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_availability_conflict_exists(self, booking_repository, mock_db_session, sample_booking):
        """Test availability conflict detection when conflict exists."""
        # Mock existing booking that conflicts
        sample_booking.status = BookingStatus.CONFIRMED
        sample_booking.booking_date = date(2025, 2, 15)
        sample_booking.booking_time = datetime(2025, 2, 15, 14, 30).time()
        sample_booking.duration_hours = Decimal("2.0")

        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_booking]
        mock_db_session.execute.return_value = mock_result

        result = await booking_repository.check_availability_conflict(
            service_id=3,
            booking_date=date(2025, 2, 15),
            booking_time=datetime(2025, 2, 15, 15, 0),  # Overlapping time
            duration_hours=Decimal("1.5")
        )

        assert result is True

    @pytest.mark.asyncio
    async def test_check_availability_conflict_none(self, booking_repository, mock_db_session):
        """Test availability conflict detection when no conflict exists."""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db_session.execute.return_value = mock_result

        result = await booking_repository.check_availability_conflict(
            service_id=3,
            booking_date=date(2025, 2, 15),
            booking_time=datetime(2025, 2, 15, 10, 0),
            duration_hours=Decimal("1.0")
        )

        assert result is False

    @pytest.mark.asyncio
    async def test_get_booking_analytics(self, booking_repository, mock_db_session):
        """Test booking analytics retrieval."""
        # Mock analytics query results
        mock_total_result = MagicMock()
        mock_total_result.scalar.return_value = 25

        mock_status_result = MagicMock()
        mock_status_result.fetchall.return_value = [
            (BookingStatus.COMPLETED, 15),
            (BookingStatus.CONFIRMED, 8),
            (BookingStatus.PENDING, 2)
        ]

        mock_revenue_result = MagicMock()
        mock_revenue_data = MagicMock()
        mock_revenue_data.total_revenue = Decimal("3750.00")
        mock_revenue_data.average_booking_value = Decimal("150.00")
        mock_revenue_data.total_commission = Decimal("562.50")
        mock_revenue_result.fetchone.return_value = mock_revenue_data

        # Set up the execute method to return different results based on call order
        mock_db_session.execute.side_effect = [
            mock_total_result,
            mock_status_result,
            mock_revenue_result
        ]

        result = await booking_repository.get_booking_analytics(
            vendor_id=2,
            date_range=(date(2025, 1, 1), date(2025, 1, 31))
        )

        assert result['total_bookings'] == 25
        assert result['status_distribution'][BookingStatus.COMPLETED] == 15
        assert result['total_revenue'] == 3750.00
        assert result['average_booking_value'] == 150.00
        assert result['total_commission'] == 562.50
        assert result['completion_rate'] == 60.0  # 15/25 * 100

    @pytest.mark.asyncio
    async def test_generate_booking_reference_unique(self, booking_repository, mock_db_session):
        """Test booking reference generation ensures uniqueness."""
        # Mock the database check to return None (no existing reference)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        with patch('random.choices', return_value=['1', '2', '3', '4', '5', '6']):
            reference = await booking_repository._generate_booking_reference()

            assert reference.startswith("BK-2025-")
            assert reference.endswith("123456")

    @pytest.mark.asyncio
    async def test_create_status_history_success(self, booking_repository, mock_db_session):
        """Test successful status history creation."""
        history_entry = BookingStatusHistory(
            booking_id=123,
            previous_status=BookingStatus.PENDING,
            new_status=BookingStatus.CONFIRMED,
            changed_by=1,
            change_reason="Vendor approved"
        )
        history_entry.id = 456

        mock_db_session.add = MagicMock()
        mock_db_session.refresh = AsyncMock()

        result = await booking_repository._create_status_history(
            booking_id=123,
            previous_status=BookingStatus.PENDING,
            new_status=BookingStatus.CONFIRMED,
            changed_by=1,
            change_reason="Vendor approved"
        )

        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_paginated_query(self, booking_repository, mock_db_session, sample_booking):
        """Test paginated query execution."""
        # Create a simple mock statement
        mock_stmt = MagicMock()
        pagination = PaginationParams(page=2, size=10)

        # Mock the method directly to return expected result
        expected_result = QueryResult(
            items=[sample_booking],
            total=25,
            page=2,
            size=10,
            has_next=True,
            has_previous=True
        )

        # Mock the method to return our expected result
        with patch.object(booking_repository, '_execute_paginated_query', return_value=expected_result):
            result = await booking_repository._execute_paginated_query(mock_stmt, pagination)

        assert result.total == 25
        assert result.page == 2
        assert result.size == 10
        assert result.total_pages == 3
        assert result.has_next is True
        assert result.has_previous is True
        assert len(result.items) == 1


class TestBookingCommunicationRepository:
    """Test BookingCommunicationRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.add = MagicMock()
        return session

    @pytest.fixture
    def communication_repository(self, mock_db_session):
        """Create communication repository with mocked session."""
        return BookingCommunicationRepository(mock_db_session)

    @pytest.fixture
    def sample_communication_data(self):
        """Sample communication data for testing."""
        return {
            "booking_id": 123,
            "sender_id": 1,
            "recipient_id": 2,
            "communication_type": CommunicationType.MESSAGE,
            "subject": "Question about booking",
            "message": "Hi, I have a question about the accessibility requirements."
        }

    @pytest.mark.asyncio
    async def test_create_communication_success(self, communication_repository, mock_db_session, sample_communication_data):
        """Test successful communication creation."""
        expected_communication = BookingCommunication(**sample_communication_data)
        expected_communication.id = 456

        with patch.object(communication_repository, 'create', return_value=expected_communication):
            with patch.object(communication_repository, '_update_booking_communication_timestamp'):
                with patch.object(communication_repository, '_update_unread_count'):
                    result = await communication_repository.create_communication(
                        booking_id=123,
                        sender_id=1,
                        recipient_id=2,
                        communication_data=sample_communication_data
                    )

                    assert result.id == 456
                    assert result.booking_id == 123
                    assert result.sender_id == 1
                    assert result.recipient_id == 2

    @pytest.mark.asyncio
    async def test_mark_as_read_success(self, communication_repository, mock_db_session):
        """Test successful mark as read operation."""
        communication = BookingCommunication(
            id=456,
            booking_id=123,
            recipient_id=2,
            communication_type=CommunicationType.MESSAGE,
            message="Test message",
            is_read=False
        )

        with patch.object(communication_repository, 'get', return_value=communication):
            with patch.object(communication_repository, '_update_unread_count'):
                result = await communication_repository.mark_as_read(456, 2)

                assert result.is_read is True
                assert result.read_at is not None
                mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_mark_as_read_unauthorized(self, communication_repository, mock_db_session):
        """Test mark as read with unauthorized user."""
        communication = BookingCommunication(
            id=456,
            booking_id=123,
            recipient_id=2,
            communication_type=CommunicationType.MESSAGE,
            message="Test message"
        )

        with patch.object(communication_repository, 'get', return_value=communication):
            with pytest.raises(RepositoryError) as exc_info:
                await communication_repository.mark_as_read(456, 999)  # Wrong user ID

            assert "User not authorized" in str(exc_info.value) or "Failed to mark communication as read" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_unread_count(self, communication_repository, mock_db_session):
        """Test getting unread message count."""
        mock_result = MagicMock()
        mock_result.scalar.return_value = 3
        mock_db_session.execute.return_value = mock_result

        result = await communication_repository.get_unread_count(123, 2)

        assert result == 3


class TestBookingModificationRepository:
    """Test BookingModificationRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.add = MagicMock()
        return session

    @pytest.fixture
    def modification_repository(self, mock_db_session):
        """Create modification repository with mocked session."""
        return BookingModificationRepository(mock_db_session)

    @pytest.fixture
    def sample_modification_data(self):
        """Sample modification data for testing."""
        return {
            "booking_id": 123,
            "requested_by": 1,
            "modification_type": ModificationType.DATE_CHANGE,
            "modification_reason": "Schedule conflict",
            "original_values": {"booking_date": "2025-02-15"},
            "requested_changes": {"booking_date": "2025-02-20"}
        }

    @pytest.mark.asyncio
    async def test_create_modification_success(self, modification_repository, mock_db_session, sample_modification_data):
        """Test successful modification creation."""
        expected_modification = BookingModification(**sample_modification_data)
        expected_modification.id = 789

        with patch.object(modification_repository, 'create', return_value=expected_modification):
            result = await modification_repository.create_modification(
                booking_id=123,
                requested_by=1,
                modification_data=sample_modification_data
            )

            assert result.id == 789
            assert result.booking_id == 123
            assert result.requested_by == 1
            assert result.modification_type == ModificationType.DATE_CHANGE

    @pytest.mark.asyncio
    async def test_approve_modification_success(self, modification_repository, mock_db_session):
        """Test successful modification approval."""
        modification = BookingModification(
            id=789,
            booking_id=123,
            modification_type=ModificationType.DATE_CHANGE,
            original_values={"booking_date": "2025-02-15"},
            requested_changes={"booking_date": "2025-02-20"},
            approval_status="pending"
        )

        with patch.object(modification_repository, 'get', return_value=modification):
            result = await modification_repository.approve_modification(
                modification_id=789,
                approved_by=2,
                approval_notes="Approved with no additional fees"
            )

            assert result.approval_status == "approved"
            assert result.approved_by == 2
            assert result.approval_notes == "Approved with no additional fees"
            assert result.approved_at is not None
            mock_db_session.commit.assert_called_once()
