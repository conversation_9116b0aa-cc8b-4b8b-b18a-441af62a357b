"""
Unit tests for push notification services.

This module provides comprehensive unit tests for push notification service classes:
- DeviceTokenService: Device registration and management with FCM token validation
- NotificationTemplateService: Template management with Jinja2 rendering and validation
- NotificationDeliveryService: Delivery orchestration with FCM integration and retry logic
- NotificationPreferenceService: User preference management with DND scheduling
- NotificationQueueService: Queue management with priority processing and batch operations
- PushNotificationService: Main orchestration service for end-to-end workflows

Implements Task 2.3.2 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from uuid import uuid4
from jinja2 import TemplateError

from app.services.push_notification_services import (
    DeviceTokenService, NotificationTemplateService, NotificationDeliveryService,
    NotificationPreferenceService, NotificationQueueService, PushNotificationService
)
from app.models.push_notification_models import (
    DeviceToken, NotificationTemplate, NotificationDelivery,
    NotificationPreference, NotificationQueue,
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)
from app.schemas.push_notification_schemas import (
    DeviceTokenCreate, NotificationTemplateCreate, NotificationSendRequest,
    NotificationPreferenceUpdate
)
from app.services.base import ValidationError, NotFoundError


@pytest.fixture
def mock_db_session():
    """Mock database session for testing."""
    session = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def sample_device_token():
    """Sample device token for testing."""
    return DeviceToken(
        id=uuid4(),
        user_id=1,
        token="test_fcm_token_123",
        platform=DevicePlatform.ANDROID,
        device_id="device_android_123",
        device_name="Samsung Galaxy S21",
        is_active=True,
        is_validated=True
    )


@pytest.fixture
def sample_notification_template():
    """Sample notification template for testing."""
    return NotificationTemplate(
        id=uuid4(),
        name="Welcome Template",
        category=NotificationCategory.AUTHENTICATION,
        title_template="Welcome {{user_name}}!",
        body_template="Hello {{user_name}}, welcome to our platform!",
        required_variables=["user_name"],
        created_by=1,
        version=1,
        is_active=True
    )


class TestDeviceTokenService:
    """Test DeviceTokenService functionality."""

    @pytest.mark.asyncio
    async def test_register_device_new(self, mock_db_session):
        """Test registering a new device token."""
        service = DeviceTokenService(mock_db_session)

        device_data = DeviceTokenCreate(
            token="new_fcm_token",
            platform=DevicePlatform.IOS,
            device_id="device_ios_123",
            device_name="iPhone 13"
        )

        # Mock repository methods
        with patch.object(service.repository, 'get_by_token', return_value=None):
            with patch.object(service.repository, 'create') as mock_create:
                mock_device = DeviceToken(
                    id=uuid4(),
                    user_id=1,
                    token=device_data.token,
                    platform=device_data.platform,
                    device_id=device_data.device_id,
                    device_name=device_data.device_name,
                    is_active=True
                )
                mock_create.return_value = mock_device

                result = await service.register_device(1, device_data)

        assert result.token == "new_fcm_token"
        assert result.platform == DevicePlatform.IOS
        assert result.user_id == 1
        mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_device_existing(self, mock_db_session, sample_device_token):
        """Test registering an existing device token (update)."""
        service = DeviceTokenService(mock_db_session)

        device_data = DeviceTokenCreate(
            token=sample_device_token.token,
            platform=sample_device_token.platform,
            device_id=sample_device_token.device_id,
            device_name="Updated Device Name"
        )

        # Mock repository methods
        with patch.object(service.repository, 'get_by_token', return_value=sample_device_token):
            with patch.object(service.repository, 'update') as mock_update:
                with patch.object(service.repository, 'update_last_used') as mock_update_last_used:
                    updated_device = DeviceToken(**sample_device_token.__dict__)
                    updated_device.device_name = "Updated Device Name"
                    mock_update.return_value = updated_device

                    result = await service.register_device(1, device_data)

        assert result.device_name == "Updated Device Name"
        mock_update.assert_called_once()
        mock_update_last_used.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_devices(self, mock_db_session, sample_device_token):
        """Test getting user devices."""
        service = DeviceTokenService(mock_db_session)

        # Mock repository method
        with patch.object(service.repository, 'get_user_devices', return_value=[sample_device_token]):
            result = await service.get_user_devices(1, DevicePlatform.ANDROID, True)

        assert len(result) == 1
        assert result[0].user_id == 1
        assert result[0].platform == DevicePlatform.ANDROID

    @pytest.mark.asyncio
    async def test_deactivate_device_success(self, mock_db_session, sample_device_token):
        """Test successfully deactivating a device."""
        service = DeviceTokenService(mock_db_session)

        # Mock repository methods
        with patch.object(service.repository, 'get', return_value=sample_device_token):
            with patch.object(service.repository, 'update', return_value=True) as mock_update:
                result = await service.deactivate_device(sample_device_token.id, 1)

        assert result is True
        mock_update.assert_called_once_with(sample_device_token.id, {"is_active": False})

    @pytest.mark.asyncio
    async def test_deactivate_device_access_denied(self, mock_db_session, sample_device_token):
        """Test deactivating device with access denied."""
        service = DeviceTokenService(mock_db_session)

        # Mock repository method - device belongs to different user
        sample_device_token.user_id = 2
        with patch.object(service.repository, 'get', return_value=sample_device_token):
            with pytest.raises(NotFoundError):
                await service.deactivate_device(sample_device_token.id, 1)

    @pytest.mark.asyncio
    async def test_cleanup_inactive_devices(self, mock_db_session):
        """Test cleaning up inactive devices."""
        service = DeviceTokenService(mock_db_session)

        # Mock repository method
        with patch.object(service.repository, 'cleanup_inactive_tokens', return_value=5):
            result = await service.cleanup_inactive_devices(90)

        assert result == 5

    @pytest.mark.asyncio
    async def test_validate_fcm_tokens(self, mock_db_session):
        """Test validating FCM tokens."""
        service = DeviceTokenService(mock_db_session)

        device_ids = [uuid4(), uuid4()]

        # Mock repository method
        with patch.object(service.repository, 'bulk_update_validation_status', return_value=2):
            result = await service.validate_fcm_tokens(device_ids)

        assert result["validated_count"] == 2
        assert result["failed_count"] == 0


class TestNotificationTemplateService:
    """Test NotificationTemplateService functionality."""

    @pytest.mark.asyncio
    async def test_create_template_success(self, mock_db_session):
        """Test successfully creating a template."""
        service = NotificationTemplateService(mock_db_session)

        template_data = NotificationTemplateCreate(
            name="Test Template",
            category=NotificationCategory.SYSTEM,
            title_template="Test {{variable}}",
            body_template="Test message with {{variable}}",
            required_variables=["variable"]
        )

        # Mock repository method
        mock_template = NotificationTemplate(
            id=uuid4(),
            name=template_data.name,
            category=template_data.category,
            title_template=template_data.title_template,
            body_template=template_data.body_template,
            created_by=1,
            version=1
        )

        with patch.object(service, 'create', return_value=mock_template):
            result = await service.create_template(template_data, 1)

        assert result.name == "Test Template"
        assert result.category == NotificationCategory.SYSTEM
        assert result.created_by == 1

    @pytest.mark.asyncio
    async def test_create_template_invalid_syntax(self, mock_db_session):
        """Test creating template with invalid Jinja2 syntax."""
        service = NotificationTemplateService(mock_db_session)

        template_data = NotificationTemplateCreate(
            name="Invalid Template",
            category=NotificationCategory.SYSTEM,
            title_template="Invalid {{variable",  # Missing closing brace
            body_template="Test message"
        )

        with pytest.raises(ValidationError):
            await service.create_template(template_data, 1)

    @pytest.mark.asyncio
    async def test_render_template_success(self, mock_db_session, sample_notification_template):
        """Test successfully rendering a template."""
        service = NotificationTemplateService(mock_db_session)

        variables = {"user_name": "John Doe"}

        # Mock repository methods
        with patch.object(service.repository, 'get', return_value=sample_notification_template):
            with patch.object(service.repository, 'validate_template_variables') as mock_validate:
                mock_validate.return_value = {"valid": True, "missing_variables": []}

                result = await service.render_template(sample_notification_template.id, variables)

        assert result["title"] == "Welcome John Doe!"
        assert result["body"] == "Hello John Doe, welcome to our platform!"

    @pytest.mark.asyncio
    async def test_render_template_missing_variables(self, mock_db_session, sample_notification_template):
        """Test rendering template with missing variables."""
        service = NotificationTemplateService(mock_db_session)

        variables = {}  # No variables provided

        # Mock repository methods
        with patch.object(service.repository, 'get', return_value=sample_notification_template):
            with patch.object(service.repository, 'validate_template_variables') as mock_validate:
                mock_validate.return_value = {"valid": False, "missing_variables": ["user_name"]}

                with pytest.raises(ValidationError) as exc_info:
                    await service.render_template(sample_notification_template.id, variables)

                assert "Missing required variables" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_templates_by_category(self, mock_db_session, sample_notification_template):
        """Test getting templates by category."""
        service = NotificationTemplateService(mock_db_session)

        # Mock repository method
        mock_result = MagicMock()
        mock_result.items = [sample_notification_template]

        with patch.object(service.repository, 'get_by_category', return_value=mock_result):
            result = await service.get_templates_by_category(NotificationCategory.AUTHENTICATION, True)

        assert len(result) == 1
        assert result[0].category == NotificationCategory.AUTHENTICATION


class TestNotificationDeliveryService:
    """Test NotificationDeliveryService functionality."""

    @pytest.mark.asyncio
    async def test_send_notification_success(self, mock_db_session, sample_device_token):
        """Test successfully sending a notification."""
        service = NotificationDeliveryService(mock_db_session)

        # Mock repository method and FCM sending
        mock_delivery = NotificationDelivery(
            id=uuid4(),
            user_id=sample_device_token.user_id,
            device_token_id=sample_device_token.id,
            title="Test Notification",
            body="Test message",
            status=NotificationStatus.PENDING
        )

        with patch.object(service.repository, 'create', return_value=mock_delivery):
            with patch.object(service, '_send_to_fcm') as mock_fcm:
                with patch.object(service.repository, 'update_delivery_status'):
                    mock_fcm.return_value = {"success": True, "message_id": "fcm_123"}

                    result = await service.send_notification(
                        sample_device_token,
                        "Test Notification",
                        "Test message"
                    )

        assert result.title == "Test Notification"
        assert result.user_id == sample_device_token.user_id
        mock_fcm.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_notification_fcm_failure(self, mock_db_session, sample_device_token):
        """Test sending notification with FCM failure."""
        service = NotificationDeliveryService(mock_db_session)

        # Mock repository method and FCM sending failure
        mock_delivery = NotificationDelivery(
            id=uuid4(),
            user_id=sample_device_token.user_id,
            device_token_id=sample_device_token.id,
            title="Test Notification",
            body="Test message",
            status=NotificationStatus.PENDING
        )

        with patch.object(service.repository, 'create', return_value=mock_delivery):
            with patch.object(service, '_send_to_fcm') as mock_fcm:
                with patch.object(service.repository, 'update_delivery_status') as mock_update:
                    mock_fcm.return_value = {
                        "success": False,
                        "error_code": "INVALID_TOKEN",
                        "error_message": "Invalid FCM token"
                    }

                    result = await service.send_notification(
                        sample_device_token,
                        "Test Notification",
                        "Test message"
                    )

        # Verify failure status was updated
        mock_update.assert_called_with(
            mock_delivery.id,
            NotificationStatus.FAILED,
            error_code="INVALID_TOKEN",
            error_message="Invalid FCM token"
        )

    @pytest.mark.asyncio
    async def test_get_delivery_analytics(self, mock_db_session):
        """Test getting delivery analytics."""
        service = NotificationDeliveryService(mock_db_session)

        # Mock repository method
        mock_analytics = {
            "total_deliveries": 100,
            "successful_deliveries": 85,
            "failed_deliveries": 10,
            "pending_deliveries": 5,
            "success_rate": 85.0
        }

        with patch.object(service.repository, 'get_delivery_analytics', return_value=mock_analytics):
            result = await service.get_delivery_analytics()

        assert result["total_deliveries"] == 100
        assert result["success_rate"] == 85.0

    @pytest.mark.asyncio
    async def test_retry_failed_deliveries(self, mock_db_session):
        """Test retrying failed deliveries."""
        service = NotificationDeliveryService(mock_db_session)

        # Mock failed deliveries
        failed_deliveries = [
            NotificationDelivery(
                id=uuid4(),
                user_id=1,
                device_token_id=uuid4(),
                title="Failed Notification",
                body="Failed message",
                status=NotificationStatus.FAILED
            )
        ]

        with patch.object(service.repository, 'get_failed_deliveries_for_retry', return_value=failed_deliveries):
            result = await service.retry_failed_deliveries(10)

        assert result == 1

    def test_get_android_config(self, mock_db_session):
        """Test getting Android-specific FCM configuration."""
        service = NotificationDeliveryService(mock_db_session)

        config = service._get_android_config(NotificationPriority.HIGH)

        assert config["priority"] == "high"
        assert config["notification"]["icon"] == "ic_notification"

    def test_get_apns_config(self, mock_db_session):
        """Test getting APNs-specific FCM configuration."""
        service = NotificationDeliveryService(mock_db_session)

        config = service._get_apns_config(NotificationPriority.CRITICAL)

        assert config["headers"]["apns-priority"] == "10"
        assert config["payload"]["aps"]["sound"] == "default"

    def test_get_webpush_config(self, mock_db_session):
        """Test getting WebPush-specific FCM configuration."""
        service = NotificationDeliveryService(mock_db_session)

        config = service._get_webpush_config(NotificationPriority.NORMAL)

        assert config["headers"]["Urgency"] == "normal"
        assert config["notification"]["icon"] == "/icons/notification.png"


class TestNotificationPreferenceService:
    """Test NotificationPreferenceService functionality."""

    @pytest.mark.asyncio
    async def test_get_user_preferences_existing(self, mock_db_session):
        """Test getting existing user preferences."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock existing preferences
        mock_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True,
            authentication_notifications=NotificationFrequency.IMMEDIATE
        )

        with patch.object(service.repository, 'get_by_user_id', return_value=mock_preferences):
            result = await service.get_user_preferences(1)

        assert result.user_id == 1
        assert result.push_notifications_enabled is True

    @pytest.mark.asyncio
    async def test_get_user_preferences_create_default(self, mock_db_session):
        """Test getting user preferences with default creation."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock no existing preferences, then default creation
        mock_default_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True
        )

        with patch.object(service.repository, 'get_by_user_id', return_value=None):
            with patch.object(service.repository, 'create_default_preferences', return_value=mock_default_preferences):
                result = await service.get_user_preferences(1)

        assert result.user_id == 1

    @pytest.mark.asyncio
    async def test_update_user_preferences(self, mock_db_session):
        """Test updating user preferences."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock existing preferences
        existing_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True
        )

        updated_preferences = NotificationPreference(
            id=existing_preferences.id,
            user_id=1,
            push_notifications_enabled=False
        )

        preference_data = NotificationPreferenceUpdate(
            push_notifications_enabled=False
        )

        with patch.object(service.repository, 'get_by_user_id', return_value=existing_preferences):
            with patch.object(service.repository, 'update', return_value=updated_preferences):
                result = await service.update_user_preferences(1, preference_data)

        assert result.push_notifications_enabled is False

    @pytest.mark.asyncio
    async def test_check_notification_allowed_enabled(self, mock_db_session):
        """Test checking if notification is allowed (enabled)."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock preferences with notifications enabled
        mock_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=True,
            authentication_notifications=NotificationFrequency.IMMEDIATE,
            dnd_enabled=False
        )

        with patch.object(service, 'get_user_preferences') as mock_get_prefs:
            mock_get_prefs.return_value = mock_preferences

            result = await service.check_notification_allowed(1, NotificationCategory.AUTHENTICATION)

        assert result is True

    @pytest.mark.asyncio
    async def test_check_notification_allowed_disabled(self, mock_db_session):
        """Test checking if notification is allowed (disabled)."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock preferences with notifications disabled
        mock_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            push_notifications_enabled=False
        )

        with patch.object(service, 'get_user_preferences') as mock_get_prefs:
            mock_get_prefs.return_value = mock_preferences

            result = await service.check_notification_allowed(1, NotificationCategory.AUTHENTICATION)

        assert result is False

    def test_is_in_dnd_period_same_day(self, mock_db_session):
        """Test DND period check for same day."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock preferences with DND enabled
        mock_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            dnd_enabled=True,
            dnd_start_time="22:00",
            dnd_end_time="08:00"
        )

        # Test time within DND period (23:00)
        test_time = datetime.now(timezone.utc).replace(hour=23, minute=0)
        result = service._is_in_dnd_period(mock_preferences, test_time)

        assert result is True

    def test_is_in_dnd_period_outside(self, mock_db_session):
        """Test DND period check outside DND hours."""
        service = NotificationPreferenceService(mock_db_session)

        # Mock preferences with DND enabled
        mock_preferences = NotificationPreference(
            id=uuid4(),
            user_id=1,
            dnd_enabled=True,
            dnd_start_time="22:00",
            dnd_end_time="08:00"
        )

        # Test time outside DND period (10:00)
        test_time = datetime.now(timezone.utc).replace(hour=10, minute=0)
        result = service._is_in_dnd_period(mock_preferences, test_time)

        assert result is False


class TestNotificationQueueService:
    """Test NotificationQueueService functionality."""

    @pytest.mark.asyncio
    async def test_enqueue_notification(self, mock_db_session):
        """Test enqueuing a notification."""
        service = NotificationQueueService(mock_db_session)

        # Mock repository method
        mock_queue_item = NotificationQueue(
            id=uuid4(),
            user_id=1,
            device_token_id=uuid4(),
            title="Queued Notification",
            body="Queued message",
            status=NotificationStatus.PENDING,
            priority=NotificationPriority.HIGH
        )

        with patch.object(service, 'create', return_value=mock_queue_item):
            result = await service.enqueue_notification(
                user_id=1,
                device_token_id=uuid4(),
                title="Queued Notification",
                body="Queued message",
                priority=NotificationPriority.HIGH
            )

        assert result.title == "Queued Notification"
        assert result.priority == NotificationPriority.HIGH

    @pytest.mark.asyncio
    async def test_get_pending_batch(self, mock_db_session):
        """Test getting pending notification batch."""
        service = NotificationQueueService(mock_db_session)

        # Mock pending notifications
        pending_notifications = [
            NotificationQueue(
                id=uuid4(),
                user_id=1,
                device_token_id=uuid4(),
                title="Pending 1",
                body="Message 1",
                status=NotificationStatus.PENDING
            ),
            NotificationQueue(
                id=uuid4(),
                user_id=2,
                device_token_id=uuid4(),
                title="Pending 2",
                body="Message 2",
                status=NotificationStatus.PENDING
            )
        ]

        with patch.object(service.repository, 'get_pending_notifications', return_value=pending_notifications):
            with patch.object(service.repository, 'mark_as_processing') as mock_mark:
                result = await service.get_pending_batch(10)

        assert len(result) == 2
        mock_mark.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_notification_success(self, mock_db_session):
        """Test completing notification processing successfully."""
        service = NotificationQueueService(mock_db_session)

        queue_id = uuid4()

        with patch.object(service.repository, 'complete_processing', return_value=True):
            result = await service.complete_notification(
                queue_id,
                NotificationStatus.SENT
            )

        assert result is True

    @pytest.mark.asyncio
    async def test_cleanup_expired_notifications(self, mock_db_session):
        """Test cleaning up expired notifications."""
        service = NotificationQueueService(mock_db_session)

        with patch.object(service.repository, 'cleanup_expired_notifications', return_value=15):
            result = await service.cleanup_expired_notifications()

        assert result == 15

    @pytest.mark.asyncio
    async def test_get_queue_statistics(self, mock_db_session):
        """Test getting queue statistics."""
        service = NotificationQueueService(mock_db_session)

        mock_stats = {
            "pending_count": 50,
            "processing_count": 10,
            "sent_count": 100,
            "failed_count": 5,
            "total_count": 165
        }

        with patch.object(service.repository, 'get_queue_stats', return_value=mock_stats):
            result = await service.get_queue_statistics()

        assert result["pending_count"] == 50
        assert result["total_count"] == 165


class TestPushNotificationService:
    """Test PushNotificationService orchestration functionality."""

    @pytest.mark.asyncio
    async def test_send_notification_with_template(self, mock_db_session):
        """Test sending notification with template."""
        service = PushNotificationService(mock_db_session)

        # Mock request
        request = NotificationSendRequest(
            user_id=1,
            template_id=uuid4(),
            template_variables={"user_name": "John Doe"},
            priority=NotificationPriority.HIGH
        )

        # Mock device response
        from app.schemas.push_notification_schemas import DeviceTokenResponse
        mock_device_response = DeviceTokenResponse(
            id=uuid4(),
            user_id=1,
            token="test_token",
            platform=DevicePlatform.ANDROID,
            device_id="device_123",
            device_name="Test Device",
            is_active=True,
            is_validated=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            last_used_at=datetime.now(timezone.utc)
        )

        # Mock delivery
        mock_delivery = NotificationDelivery(
            id=uuid4(),
            user_id=1,
            device_token_id=mock_device_response.id,
            title="Welcome John Doe!",
            body="Hello John Doe, welcome!",
            status=NotificationStatus.SENT
        )

        # Mock service calls
        with patch.object(service.device_service, 'get_user_devices', return_value=[mock_device_response]):
            with patch.object(service.preference_service, 'check_notification_allowed', return_value=True):
                with patch.object(service.template_service, 'render_template') as mock_render:
                    with patch.object(service.delivery_service, 'send_notification', return_value=mock_delivery):
                        mock_render.return_value = {"title": "Welcome John Doe!", "body": "Hello John Doe, welcome!"}

                        result = await service.send_notification(request)

        assert result.total_sent == 1
        assert result.failed_count == 0
        assert len(result.delivery_ids) == 1

    @pytest.mark.asyncio
    async def test_get_notification_analytics(self, mock_db_session):
        """Test getting comprehensive notification analytics."""
        service = PushNotificationService(mock_db_session)

        # Mock analytics data
        mock_delivery_analytics = {
            "total_deliveries": 100,
            "successful_deliveries": 85,
            "failed_deliveries": 10,
            "success_rate": 85.0
        }

        mock_queue_stats = {
            "pending_count": 20,
            "processing_count": 5,
            "sent_count": 100,
            "failed_count": 10
        }

        mock_device_stats = {
            "total_devices": 50,
            "validated_devices": 45,
            "validation_rate": 90.0
        }

        # Mock service calls
        with patch.object(service.delivery_service, 'get_delivery_analytics', return_value=mock_delivery_analytics):
            with patch.object(service.queue_service, 'get_queue_statistics', return_value=mock_queue_stats):
                with patch('app.repositories.push_notification_repositories.DeviceTokenRepository') as mock_repo_class:
                    mock_repo = mock_repo_class.return_value
                    mock_repo.get_validation_stats.return_value = mock_device_stats

                    result = await service.get_notification_analytics()

        assert result["delivery_analytics"]["total_deliveries"] == 100
        assert result["queue_statistics"]["pending_count"] == 20
        assert result["device_statistics"]["total_devices"] == 50