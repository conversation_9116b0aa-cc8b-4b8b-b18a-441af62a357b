#!/usr/bin/env python3
"""
Culture Connect Backend - Integration Testing Suite
Comprehensive end-to-end testing for production readiness
"""

import asyncio
import json
import logging
import os
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

import aiohttp
import pytest
import websockets
from faker import Faker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(f'integration-test-{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Test configuration
class TestConfig:
    BASE_URL = os.getenv('API_BASE_URL', 'https://api.cultureconnect.ng')
    WS_URL = os.getenv('WS_BASE_URL', 'wss://api.cultureconnect.ng')
    API_VERSION = 'v1'
    
    # Test credentials
    TEST_USER_EMAIL = '<EMAIL>'
    TEST_USER_PASSWORD = 'IntegrationTest123!'
    
    # Performance thresholds
    MAX_RESPONSE_TIME = 5.0  # seconds
    MAX_WEBSOCKET_LATENCY = 1.0  # seconds
    
    # Test data
    TEST_LOCATIONS = ['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']
    TEST_SERVICE_TYPES = ['catering', 'photography', 'decoration', 'music', 'venue']
    TEST_EVENT_TYPES = ['wedding', 'birthday', 'corporate', 'anniversary', 'graduation']

# Test results tracking
class TestResults:
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_details = []
        self.start_time = time.time()
    
    def add_test_result(self, test_name: str, passed: bool, duration: float, details: str = ""):
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        self.test_details.append({
            'test_name': test_name,
            'passed': passed,
            'duration': duration,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{status}: {test_name} ({duration:.2f}s) - {details}")
    
    def get_summary(self) -> Dict[str, Any]:
        total_duration = time.time() - self.start_time
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        return {
            'total_tests': self.total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': round(success_rate, 2),
            'total_duration': round(total_duration, 2),
            'test_details': self.test_details
        }

# HTTP Client wrapper
class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = None
        self.auth_token = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=TestConfig.MAX_RESPONSE_TIME)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def request(self, method: str, path: str, **kwargs) -> aiohttp.ClientResponse:
        url = f"{self.base_url}{path}"
        headers = kwargs.get('headers', {})
        
        if self.auth_token:
            headers['Authorization'] = f'Bearer {self.auth_token}'
        
        kwargs['headers'] = headers
        
        start_time = time.time()
        response = await self.session.request(method, url, **kwargs)
        duration = time.time() - start_time
        
        logger.debug(f"{method} {path} - {response.status} ({duration:.3f}s)")
        return response
    
    async def authenticate(self, email: str, password: str) -> bool:
        try:
            response = await self.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/auth/login',
                json={'email': email, 'password': password}
            )
            
            if response.status == 200:
                data = await response.json()
                self.auth_token = data.get('access_token')
                return True
            return False
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False

# Integration test suite
class IntegrationTestSuite:
    def __init__(self):
        self.results = TestResults()
        self.faker = Faker()
        self.test_data = {}
    
    async def run_all_tests(self):
        """Run all integration tests"""
        logger.info("🚀 Starting Integration Testing Suite")
        logger.info(f"Target: {TestConfig.BASE_URL}")
        logger.info("=" * 60)
        
        async with APIClient(TestConfig.BASE_URL) as client:
            # Core API tests
            await self.test_health_check(client)
            await self.test_authentication_flow(client)
            await self.test_user_management(client)
            await self.test_vendor_management(client)
            await self.test_booking_flow(client)
            await self.test_payment_integration(client)
            await self.test_search_functionality(client)
            
            # External service integration tests
            await self.test_email_service_integration(client)
            await self.test_geolocation_integration(client)
            await self.test_ai_ml_integration(client)
            
            # Real-time communication tests
            await self.test_websocket_communication()
            
            # Performance and stress tests
            await self.test_concurrent_requests(client)
            await self.test_rate_limiting(client)
        
        # Generate final report
        self.generate_report()
    
    async def test_health_check(self, client: APIClient):
        """Test API health check endpoint"""
        test_name = "Health Check"
        start_time = time.time()
        
        try:
            response = await client.request('GET', '/health')
            duration = time.time() - start_time
            
            if response.status == 200:
                data = await response.json()
                if data.get('status') == 'healthy':
                    self.results.add_test_result(test_name, True, duration, "API is healthy")
                else:
                    self.results.add_test_result(test_name, False, duration, f"Unhealthy status: {data}")
            else:
                self.results.add_test_result(test_name, False, duration, f"HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_authentication_flow(self, client: APIClient):
        """Test user authentication flow"""
        test_name = "Authentication Flow"
        start_time = time.time()
        
        try:
            # Test login
            success = await client.authenticate(TestConfig.TEST_USER_EMAIL, TestConfig.TEST_USER_PASSWORD)
            
            if success:
                # Test authenticated endpoint
                response = await client.request('GET', f'/api/{TestConfig.API_VERSION}/auth/me')
                duration = time.time() - start_time
                
                if response.status == 200:
                    user_data = await response.json()
                    self.test_data['user_id'] = user_data.get('user', {}).get('id')
                    self.results.add_test_result(test_name, True, duration, "Authentication successful")
                else:
                    self.results.add_test_result(test_name, False, duration, f"Auth check failed: HTTP {response.status}")
            else:
                duration = time.time() - start_time
                self.results.add_test_result(test_name, False, duration, "Login failed")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_user_management(self, client: APIClient):
        """Test user management operations"""
        test_name = "User Management"
        start_time = time.time()
        
        try:
            # Create test user
            test_user_data = {
                'email': f'test-{uuid.uuid4()}@example.com',
                'password': 'TestPassword123!',
                'first_name': self.faker.first_name(),
                'last_name': self.faker.last_name(),
                'phone': self.faker.phone_number(),
                'location': self.faker.random_element(TestConfig.TEST_LOCATIONS)
            }
            
            response = await client.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/auth/register',
                json=test_user_data
            )
            
            duration = time.time() - start_time
            
            if response.status == 201:
                user_data = await response.json()
                self.test_data['created_user_id'] = user_data.get('user', {}).get('id')
                self.results.add_test_result(test_name, True, duration, "User created successfully")
            else:
                self.results.add_test_result(test_name, False, duration, f"User creation failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_vendor_management(self, client: APIClient):
        """Test vendor management operations"""
        test_name = "Vendor Management"
        start_time = time.time()
        
        try:
            # Search vendors
            response = await client.request(
                'GET',
                f'/api/{TestConfig.API_VERSION}/vendors/search',
                params={
                    'location': 'Lagos',
                    'service_type': 'catering',
                    'limit': 10
                }
            )
            
            if response.status == 200:
                vendors_data = await response.json()
                vendors = vendors_data.get('vendors', [])
                
                if vendors:
                    # Get vendor details
                    vendor_id = vendors[0]['id']
                    detail_response = await client.request(
                        'GET',
                        f'/api/{TestConfig.API_VERSION}/vendors/{vendor_id}'
                    )
                    
                    duration = time.time() - start_time
                    
                    if detail_response.status == 200:
                        self.test_data['test_vendor_id'] = vendor_id
                        self.results.add_test_result(test_name, True, duration, f"Found {len(vendors)} vendors")
                    else:
                        self.results.add_test_result(test_name, False, duration, "Vendor details fetch failed")
                else:
                    duration = time.time() - start_time
                    self.results.add_test_result(test_name, False, duration, "No vendors found")
            else:
                duration = time.time() - start_time
                self.results.add_test_result(test_name, False, duration, f"Vendor search failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_booking_flow(self, client: APIClient):
        """Test complete booking flow"""
        test_name = "Booking Flow"
        start_time = time.time()
        
        try:
            if not self.test_data.get('test_vendor_id'):
                self.results.add_test_result(test_name, False, 0, "No vendor available for booking")
                return
            
            # Create booking
            booking_data = {
                'vendor_id': self.test_data['test_vendor_id'],
                'event_date': (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
                'event_type': self.faker.random_element(TestConfig.TEST_EVENT_TYPES),
                'guest_count': self.faker.random_int(min=50, max=500),
                'budget': self.faker.random_int(min=100000, max=2000000),
                'location': f"{self.faker.random_element(TestConfig.TEST_LOCATIONS)}, Nigeria",
                'description': f'Integration test booking {uuid.uuid4()}'
            }
            
            response = await client.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/bookings',
                json=booking_data
            )
            
            duration = time.time() - start_time
            
            if response.status == 201:
                booking_response = await response.json()
                self.test_data['test_booking_id'] = booking_response.get('booking', {}).get('id')
                self.results.add_test_result(test_name, True, duration, "Booking created successfully")
            else:
                self.results.add_test_result(test_name, False, duration, f"Booking creation failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_payment_integration(self, client: APIClient):
        """Test payment processing integration"""
        test_name = "Payment Integration"
        start_time = time.time()
        
        try:
            # Initialize payment
            payment_data = {
                'amount': 50000,
                'currency': 'NGN',
                'payment_method': 'paystack',
                'reference': f'integration_test_{uuid.uuid4()}'
            }
            
            response = await client.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/payments/initialize',
                json=payment_data
            )
            
            duration = time.time() - start_time
            
            if response.status == 200:
                payment_response = await response.json()
                if payment_response.get('payment_url'):
                    self.results.add_test_result(test_name, True, duration, "Payment initialization successful")
                else:
                    self.results.add_test_result(test_name, False, duration, "No payment URL returned")
            else:
                self.results.add_test_result(test_name, False, duration, f"Payment initialization failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_search_functionality(self, client: APIClient):
        """Test search functionality"""
        test_name = "Search Functionality"
        start_time = time.time()
        
        try:
            # Test various search scenarios
            search_scenarios = [
                {'location': 'Lagos', 'service_type': 'catering'},
                {'location': 'Abuja', 'service_type': 'photography'},
                {'q': 'wedding', 'limit': 5},
                {'price_min': 100000, 'price_max': 500000}
            ]
            
            successful_searches = 0
            
            for scenario in search_scenarios:
                response = await client.request(
                    'GET',
                    f'/api/{TestConfig.API_VERSION}/vendors/search',
                    params=scenario
                )
                
                if response.status == 200:
                    successful_searches += 1
            
            duration = time.time() - start_time
            
            if successful_searches == len(search_scenarios):
                self.results.add_test_result(test_name, True, duration, f"All {len(search_scenarios)} search scenarios passed")
            else:
                self.results.add_test_result(test_name, False, duration, f"Only {successful_searches}/{len(search_scenarios)} searches passed")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_email_service_integration(self, client: APIClient):
        """Test email service integration"""
        test_name = "Email Service Integration"
        start_time = time.time()
        
        try:
            # Test email sending (this would typically be a test endpoint)
            email_data = {
                'to': '<EMAIL>',
                'subject': 'Integration Test Email',
                'template': 'test_template',
                'data': {'name': 'Test User'}
            }
            
            response = await client.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/email/send-test',
                json=email_data
            )
            
            duration = time.time() - start_time
            
            if response.status in [200, 202]:  # 202 for async processing
                self.results.add_test_result(test_name, True, duration, "Email service integration successful")
            else:
                self.results.add_test_result(test_name, False, duration, f"Email service failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_geolocation_integration(self, client: APIClient):
        """Test geolocation service integration"""
        test_name = "Geolocation Integration"
        start_time = time.time()
        
        try:
            # Test geolocation detection
            response = await client.request(
                'GET',
                f'/api/{TestConfig.API_VERSION}/geolocation/detect',
                headers={'X-Forwarded-For': '************'}  # Nigerian IP
            )
            
            duration = time.time() - start_time
            
            if response.status == 200:
                geo_data = await response.json()
                if geo_data.get('country_code') == 'NG':
                    self.results.add_test_result(test_name, True, duration, "Geolocation detection successful")
                else:
                    self.results.add_test_result(test_name, False, duration, f"Incorrect country detected: {geo_data}")
            else:
                self.results.add_test_result(test_name, False, duration, f"Geolocation failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_ai_ml_integration(self, client: APIClient):
        """Test AI/ML service integration"""
        test_name = "AI/ML Integration"
        start_time = time.time()
        
        try:
            # Test AI recommendation
            ai_request = {
                'user_preferences': {
                    'location': 'Lagos',
                    'budget': 500000,
                    'event_type': 'wedding'
                },
                'request_type': 'vendor_recommendation'
            }
            
            response = await client.request(
                'POST',
                f'/api/{TestConfig.API_VERSION}/ai/recommend',
                json=ai_request
            )
            
            duration = time.time() - start_time
            
            if response.status == 200:
                ai_response = await response.json()
                if ai_response.get('recommendations'):
                    self.results.add_test_result(test_name, True, duration, "AI/ML integration successful")
                else:
                    self.results.add_test_result(test_name, False, duration, "No recommendations returned")
            else:
                self.results.add_test_result(test_name, False, duration, f"AI/ML service failed: HTTP {response.status}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_websocket_communication(self):
        """Test WebSocket real-time communication"""
        test_name = "WebSocket Communication"
        start_time = time.time()
        
        try:
            ws_url = f"{TestConfig.WS_URL}/api/{TestConfig.API_VERSION}/websocket/rooms/test"
            
            async with websockets.connect(ws_url) as websocket:
                # Send join message
                join_message = {
                    'type': 'join_room',
                    'room': 'test',
                    'user_id': f'integration_test_{uuid.uuid4()}'
                }
                
                await websocket.send(json.dumps(join_message))
                
                # Wait for response
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                
                duration = time.time() - start_time
                
                if response_data.get('type') == 'room_joined':
                    self.results.add_test_result(test_name, True, duration, "WebSocket communication successful")
                else:
                    self.results.add_test_result(test_name, False, duration, f"Unexpected response: {response_data}")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_concurrent_requests(self, client: APIClient):
        """Test concurrent request handling"""
        test_name = "Concurrent Requests"
        start_time = time.time()
        
        try:
            # Create multiple concurrent requests
            tasks = []
            for i in range(50):  # 50 concurrent requests
                task = client.request('GET', '/health')
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            duration = time.time() - start_time
            successful_requests = sum(1 for r in responses if hasattr(r, 'status') and r.status == 200)
            
            if successful_requests >= 45:  # Allow for some failures
                self.results.add_test_result(test_name, True, duration, f"{successful_requests}/50 requests successful")
            else:
                self.results.add_test_result(test_name, False, duration, f"Only {successful_requests}/50 requests successful")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    async def test_rate_limiting(self, client: APIClient):
        """Test rate limiting functionality"""
        test_name = "Rate Limiting"
        start_time = time.time()
        
        try:
            # Send requests rapidly to trigger rate limiting
            rate_limited = False
            
            for i in range(200):  # Send many requests quickly
                response = await client.request('GET', '/health')
                if response.status == 429:  # Too Many Requests
                    rate_limited = True
                    break
            
            duration = time.time() - start_time
            
            if rate_limited:
                self.results.add_test_result(test_name, True, duration, "Rate limiting is working")
            else:
                self.results.add_test_result(test_name, False, duration, "Rate limiting not triggered")
        except Exception as e:
            duration = time.time() - start_time
            self.results.add_test_result(test_name, False, duration, str(e))
    
    def generate_report(self):
        """Generate comprehensive test report"""
        summary = self.results.get_summary()
        
        logger.info("=" * 60)
        logger.info("📊 INTEGRATION TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {summary['total_tests']}")
        logger.info(f"Passed: {summary['passed_tests']}")
        logger.info(f"Failed: {summary['failed_tests']}")
        logger.info(f"Success Rate: {summary['success_rate']}%")
        logger.info(f"Total Duration: {summary['total_duration']}s")
        
        if summary['failed_tests'] > 0:
            logger.info("\n❌ FAILED TESTS:")
            for test in summary['test_details']:
                if not test['passed']:
                    logger.info(f"   - {test['test_name']}: {test['details']}")
        
        # Save JSON report
        report_file = f"integration-test-report-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"\n📄 Report saved to: {report_file}")
        
        if summary['failed_tests'] == 0:
            logger.info("🎉 ALL INTEGRATION TESTS PASSED!")
            logger.info("✅ System is ready for production deployment")
        else:
            logger.info("⚠️  INTEGRATION ISSUES DETECTED")
            logger.info("❌ System requires fixes before production deployment")

# Main execution
async def main():
    """Main execution function"""
    test_suite = IntegrationTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
