"""
Push Notification repositories for Culture Connect Backend API.

This module provides comprehensive repository classes for push notification management including:
- DeviceTokenRepository: Device token CRUD operations with platform filtering and validation
- NotificationTemplateRepository: Template management with versioning and categorization
- NotificationDeliveryRepository: Delivery tracking with analytics and performance monitoring
- NotificationPreferenceRepository: User notification preferences and bulk preference updates
- NotificationQueueRepository: Queue management with priority handling and batch processing

Implements Task 2.3.2 Phase 3 requirements for push notification system with
production-grade PostgreSQL optimization, comprehensive error handling, and
seamless integration with Phase 1 models and Phase 2 schemas.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union, Tuple
from uuid import UUID

from sqlalchemy import select, func, and_, or_, update, delete, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base import BaseRepository, PaginationParams, QueryResult, RepositoryError
from app.models.push_notification_models import (
    DeviceToken, NotificationTemplate, NotificationDelivery,
    NotificationPreference, NotificationQueue,
    DevicePlatform, NotificationStatus, NotificationPriority,
    NotificationCategory, NotificationFrequency
)

# Create exception classes for repository operations
DatabaseError = RepositoryError

class ValidationError(RepositoryError):
    """Exception for validation errors."""
    pass

class NotFoundError(RepositoryError):
    """Exception for resource not found errors."""
    pass

class ConflictError(RepositoryError):
    """Exception for resource conflict errors."""
    pass

logger = logging.getLogger(__name__)


class DeviceTokenRepository(BaseRepository[DeviceToken]):
    """
    Repository for device token management with platform filtering and validation tracking.

    Provides comprehensive device token operations including:
    - Platform-specific filtering and queries
    - Validation status tracking and bulk updates
    - User device management with cleanup operations
    - Performance optimization for FCM token operations
    """

    def __init__(self, db: AsyncSession):
        super().__init__(DeviceToken, db)

    async def get_by_token(self, token: str) -> Optional[DeviceToken]:
        """
        Get device token by FCM token string.

        Args:
            token: FCM device token

        Returns:
            DeviceToken instance if found, None otherwise
        """
        try:
            stmt = select(DeviceToken).where(DeviceToken.token == token)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except SQLAlchemyError as e:
            logger.error(f"Error getting device token by token: {str(e)}")
            raise DatabaseError(f"Failed to get device token: {str(e)}")

    async def get_user_devices(
        self,
        user_id: int,
        platform: Optional[DevicePlatform] = None,
        active_only: bool = True
    ) -> List[DeviceToken]:
        """
        Get all device tokens for a user with optional platform filtering.

        Args:
            user_id: User ID
            platform: Optional platform filter
            active_only: Whether to return only active tokens

        Returns:
            List of user's device tokens
        """
        try:
            stmt = select(DeviceToken).where(DeviceToken.user_id == user_id)

            if platform:
                stmt = stmt.where(DeviceToken.platform == platform)

            if active_only:
                stmt = stmt.where(DeviceToken.is_active == True)

            stmt = stmt.order_by(desc(DeviceToken.last_used_at))

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting user devices: {str(e)}")
            raise DatabaseError(f"Failed to get user devices: {str(e)}")

    async def get_by_platform(
        self,
        platform: DevicePlatform,
        active_only: bool = True,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[DeviceToken]:
        """
        Get device tokens by platform with pagination.

        Args:
            platform: Device platform
            active_only: Whether to return only active tokens
            pagination: Pagination parameters

        Returns:
            Paginated query result
        """
        try:
            stmt = select(DeviceToken).where(DeviceToken.platform == platform)

            if active_only:
                stmt = stmt.where(DeviceToken.is_active == True)

            # Get total count
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()

            # Apply pagination
            if pagination:
                stmt = stmt.offset(pagination.offset).limit(pagination.limit)

            stmt = stmt.order_by(desc(DeviceToken.created_at))

            result = await self.db.execute(stmt)
            items = result.scalars().all()

            return QueryResult(
                items=items,
                total=total,
                page=pagination.page if pagination else 1,
                per_page=pagination.limit if pagination else len(items),
                pages=((total - 1) // pagination.limit + 1) if pagination and pagination.limit > 0 else 1
            )

        except SQLAlchemyError as e:
            logger.error(f"Error getting devices by platform: {str(e)}")
            raise DatabaseError(f"Failed to get devices by platform: {str(e)}")

    async def update_last_used(self, device_id: UUID) -> bool:
        """
        Update the last used timestamp for a device token.

        Args:
            device_id: Device token ID

        Returns:
            True if updated successfully
        """
        try:
            stmt = (
                update(DeviceToken)
                .where(DeviceToken.id == device_id)
                .values(last_used_at=datetime.utcnow())
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error updating last used timestamp: {str(e)}")
            raise DatabaseError(f"Failed to update last used timestamp: {str(e)}")

    async def bulk_update_validation_status(
        self,
        device_ids: List[UUID],
        is_validated: bool
    ) -> int:
        """
        Bulk update validation status for multiple device tokens.

        Args:
            device_ids: List of device token IDs
            is_validated: New validation status

        Returns:
            Number of updated records
        """
        try:
            stmt = (
                update(DeviceToken)
                .where(DeviceToken.id.in_(device_ids))
                .values(
                    is_validated=is_validated,
                    validation_attempts=DeviceToken.validation_attempts + 1,
                    updated_at=datetime.utcnow()
                )
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            logger.info(
                f"Bulk updated validation status for {result.rowcount} devices",
                extra={"device_count": result.rowcount, "is_validated": is_validated}
            )

            return result.rowcount

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error bulk updating validation status: {str(e)}")
            raise DatabaseError(f"Failed to bulk update validation status: {str(e)}")

    async def cleanup_inactive_tokens(self, days_inactive: int = 90) -> int:
        """
        Clean up inactive device tokens older than specified days.

        Args:
            days_inactive: Number of days to consider a token inactive

        Returns:
            Number of cleaned up tokens
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_inactive)

            stmt = delete(DeviceToken).where(
                or_(
                    DeviceToken.last_used_at < cutoff_date,
                    and_(
                        DeviceToken.last_used_at.is_(None),
                        DeviceToken.created_at < cutoff_date
                    )
                )
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            logger.info(
                f"Cleaned up {result.rowcount} inactive device tokens",
                extra={"cleaned_count": result.rowcount, "days_inactive": days_inactive}
            )

            return result.rowcount

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error cleaning up inactive tokens: {str(e)}")
            raise DatabaseError(f"Failed to cleanup inactive tokens: {str(e)}")

    async def get_validation_stats(self) -> Dict[str, Any]:
        """
        Get device token validation statistics.

        Returns:
            Dictionary with validation statistics
        """
        try:
            # Total tokens
            total_stmt = select(func.count()).select_from(DeviceToken)
            total_result = await self.db.execute(total_stmt)
            total = total_result.scalar()

            # Validated tokens
            validated_stmt = select(func.count()).select_from(DeviceToken).where(
                DeviceToken.is_validated == True
            )
            validated_result = await self.db.execute(validated_stmt)
            validated = validated_result.scalar()

            # Active tokens
            active_stmt = select(func.count()).select_from(DeviceToken).where(
                DeviceToken.is_active == True
            )
            active_result = await self.db.execute(active_stmt)
            active = active_result.scalar()

            # Platform breakdown
            platform_stmt = select(
                DeviceToken.platform,
                func.count().label('count')
            ).group_by(DeviceToken.platform)
            platform_result = await self.db.execute(platform_stmt)
            platform_stats = {row.platform.value: row.count for row in platform_result}

            return {
                "total_tokens": total,
                "validated_tokens": validated,
                "active_tokens": active,
                "validation_rate": (validated / total * 100) if total > 0 else 0,
                "platform_breakdown": platform_stats
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting validation stats: {str(e)}")
            raise DatabaseError(f"Failed to get validation stats: {str(e)}")


class NotificationTemplateRepository(BaseRepository[NotificationTemplate]):
    """
    Repository for notification template management with versioning and categorization.

    Provides comprehensive template operations including:
    - Category-based filtering and organization
    - Template versioning and lifecycle management
    - Variable validation and template compilation
    - Performance optimization for template queries
    """

    def __init__(self, db: AsyncSession):
        super().__init__(NotificationTemplate, db)

    async def get_by_name_and_version(
        self,
        name: str,
        version: Optional[int] = None
    ) -> Optional[NotificationTemplate]:
        """
        Get template by name and version (latest if version not specified).

        Args:
            name: Template name
            version: Template version (latest if None)

        Returns:
            NotificationTemplate instance if found
        """
        try:
            stmt = select(NotificationTemplate).where(
                and_(
                    NotificationTemplate.name == name,
                    NotificationTemplate.is_active == True
                )
            )

            if version:
                stmt = stmt.where(NotificationTemplate.version == version)
            else:
                # Get latest version
                stmt = stmt.order_by(desc(NotificationTemplate.version))

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except SQLAlchemyError as e:
            logger.error(f"Error getting template by name and version: {str(e)}")
            raise DatabaseError(f"Failed to get template: {str(e)}")

    async def get_by_category(
        self,
        category: NotificationCategory,
        active_only: bool = True,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[NotificationTemplate]:
        """
        Get templates by category with pagination.

        Args:
            category: Notification category
            active_only: Whether to return only active templates
            pagination: Pagination parameters

        Returns:
            Paginated query result
        """
        try:
            stmt = select(NotificationTemplate).where(
                NotificationTemplate.category == category
            )

            if active_only:
                stmt = stmt.where(NotificationTemplate.is_active == True)

            # Get total count
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()

            # Apply pagination
            if pagination:
                stmt = stmt.offset(pagination.offset).limit(pagination.limit)

            stmt = stmt.order_by(desc(NotificationTemplate.created_at))

            result = await self.db.execute(stmt)
            items = result.scalars().all()

            return QueryResult(
                items=items,
                total=total,
                page=pagination.page if pagination else 1,
                per_page=pagination.limit if pagination else len(items),
                pages=((total - 1) // pagination.limit + 1) if pagination and pagination.limit > 0 else 1
            )

        except SQLAlchemyError as e:
            logger.error(f"Error getting templates by category: {str(e)}")
            raise DatabaseError(f"Failed to get templates by category: {str(e)}")

    async def create_new_version(
        self,
        template_id: UUID,
        updates: Dict[str, Any]
    ) -> NotificationTemplate:
        """
        Create a new version of an existing template.

        Args:
            template_id: Original template ID
            updates: Fields to update in new version

        Returns:
            New template version
        """
        try:
            # Get original template
            original = await self.get(template_id)
            if not original:
                raise ValidationError("Original template not found")

            # Get next version number
            max_version_stmt = select(func.max(NotificationTemplate.version)).where(
                NotificationTemplate.name == original.name
            )
            max_version_result = await self.db.execute(max_version_stmt)
            max_version = max_version_result.scalar() or 0

            # Create new version
            new_template_data = {
                "name": original.name,
                "category": original.category,
                "title_template": original.title_template,
                "body_template": original.body_template,
                "variables": original.variables,
                "default_priority": original.default_priority,
                "ios_payload": original.ios_payload,
                "android_payload": original.android_payload,
                "web_payload": original.web_payload,
                "click_action": original.click_action,
                "deep_link": original.deep_link,
                "image_url": original.image_url,
                "version": max_version + 1,
                "created_by": updates.get("created_by", original.created_by),
                "is_active": True
            }

            # Apply updates
            new_template_data.update(updates)

            new_template = NotificationTemplate(**new_template_data)
            self.db.add(new_template)
            await self.db.commit()
            await self.db.refresh(new_template)

            logger.info(
                f"Created new template version {new_template.version} for {original.name}",
                extra={
                    "template_name": original.name,
                    "old_version": original.version,
                    "new_version": new_template.version
                }
            )

            return new_template

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error creating new template version: {str(e)}")
            raise DatabaseError(f"Failed to create new template version: {str(e)}")

    async def deactivate_template(self, template_id: UUID) -> bool:
        """
        Deactivate a template (soft delete).

        Args:
            template_id: Template ID to deactivate

        Returns:
            True if deactivated successfully
        """
        try:
            stmt = (
                update(NotificationTemplate)
                .where(NotificationTemplate.id == template_id)
                .values(
                    is_active=False,
                    updated_at=datetime.utcnow()
                )
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error deactivating template: {str(e)}")
            raise DatabaseError(f"Failed to deactivate template: {str(e)}")

    async def validate_template_variables(
        self,
        template_id: UUID,
        variables: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate provided variables against template requirements.

        Args:
            template_id: Template ID
            variables: Variables to validate

        Returns:
            Validation result with missing/extra variables
        """
        try:
            template = await self.get(template_id)
            if not template:
                raise ValidationError("Template not found")

            required_vars = set(template.variables.keys())
            provided_vars = set(variables.keys())

            missing_vars = required_vars - provided_vars
            extra_vars = provided_vars - required_vars

            return {
                "valid": len(missing_vars) == 0,
                "missing_variables": list(missing_vars),
                "extra_variables": list(extra_vars),
                "required_variables": list(required_vars)
            }

        except SQLAlchemyError as e:
            logger.error(f"Error validating template variables: {str(e)}")
            raise DatabaseError(f"Failed to validate template variables: {str(e)}")

    async def get_template_usage_stats(self, template_id: UUID) -> Dict[str, Any]:
        """
        Get usage statistics for a template.

        Args:
            template_id: Template ID

        Returns:
            Usage statistics
        """
        try:
            # Get delivery count
            delivery_stmt = select(func.count()).select_from(NotificationDelivery).where(
                NotificationDelivery.template_id == template_id
            )
            delivery_result = await self.db.execute(delivery_stmt)
            delivery_count = delivery_result.scalar()

            # Get queue count
            queue_stmt = select(func.count()).select_from(NotificationQueue).where(
                NotificationQueue.template_id == template_id
            )
            queue_result = await self.db.execute(queue_stmt)
            queue_count = queue_result.scalar()

            # Get success rate
            success_stmt = select(func.count()).select_from(NotificationDelivery).where(
                and_(
                    NotificationDelivery.template_id == template_id,
                    NotificationDelivery.status == NotificationStatus.DELIVERED
                )
            )
            success_result = await self.db.execute(success_stmt)
            success_count = success_result.scalar()

            return {
                "total_deliveries": delivery_count,
                "queued_notifications": queue_count,
                "successful_deliveries": success_count,
                "success_rate": (success_count / delivery_count * 100) if delivery_count > 0 else 0
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting template usage stats: {str(e)}")
            raise DatabaseError(f"Failed to get template usage stats: {str(e)}")


class NotificationDeliveryRepository(BaseRepository[NotificationDelivery]):
    """
    Repository for notification delivery tracking with analytics and performance monitoring.

    Provides comprehensive delivery operations including:
    - Status tracking and retry logic management
    - Analytics queries and performance metrics
    - Bulk status updates and cleanup operations
    - FCM response tracking and error analysis
    """

    def __init__(self, db: AsyncSession):
        super().__init__(NotificationDelivery, db)

    async def get_by_status(
        self,
        status: NotificationStatus,
        pagination: Optional[PaginationParams] = None
    ) -> QueryResult[NotificationDelivery]:
        """
        Get deliveries by status with pagination.

        Args:
            status: Notification status
            pagination: Pagination parameters

        Returns:
            Paginated query result
        """
        try:
            stmt = select(NotificationDelivery).where(NotificationDelivery.status == status)

            # Get total count
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()

            # Apply pagination
            if pagination:
                stmt = stmt.offset(pagination.offset).limit(pagination.limit)

            stmt = stmt.order_by(desc(NotificationDelivery.created_at))

            result = await self.db.execute(stmt)
            items = result.scalars().all()

            return QueryResult(
                items=items,
                total=total,
                page=pagination.page if pagination else 1,
                per_page=pagination.limit if pagination else len(items),
                pages=((total - 1) // pagination.limit + 1) if pagination and pagination.limit > 0 else 1
            )

        except SQLAlchemyError as e:
            logger.error(f"Error getting deliveries by status: {str(e)}")
            raise DatabaseError(f"Failed to get deliveries by status: {str(e)}")

    async def get_failed_deliveries_for_retry(self, limit: int = 100) -> List[NotificationDelivery]:
        """
        Get failed deliveries that are eligible for retry.

        Args:
            limit: Maximum number of deliveries to return

        Returns:
            List of failed deliveries ready for retry
        """
        try:
            stmt = select(NotificationDelivery).where(
                and_(
                    NotificationDelivery.status == NotificationStatus.FAILED,
                    NotificationDelivery.retry_count < NotificationDelivery.max_retries,
                    or_(
                        NotificationDelivery.next_retry_at.is_(None),
                        NotificationDelivery.next_retry_at <= datetime.utcnow()
                    )
                )
            ).order_by(NotificationDelivery.created_at).limit(limit)

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting failed deliveries for retry: {str(e)}")
            raise DatabaseError(f"Failed to get failed deliveries for retry: {str(e)}")

    async def update_delivery_status(
        self,
        delivery_id: UUID,
        status: NotificationStatus,
        fcm_message_id: Optional[str] = None,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Update delivery status with optional FCM response data.

        Args:
            delivery_id: Delivery ID
            status: New status
            fcm_message_id: FCM message ID
            error_code: Error code if failed
            error_message: Error message if failed

        Returns:
            True if updated successfully
        """
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }

            if fcm_message_id:
                update_data["fcm_message_id"] = fcm_message_id

            if status == NotificationStatus.SENT:
                update_data["sent_at"] = datetime.utcnow()
            elif status == NotificationStatus.DELIVERED:
                update_data["delivered_at"] = datetime.utcnow()
            elif status == NotificationStatus.FAILED:
                update_data["error_code"] = error_code
                update_data["error_message"] = error_message

            stmt = (
                update(NotificationDelivery)
                .where(NotificationDelivery.id == delivery_id)
                .values(**update_data)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error updating delivery status: {str(e)}")
            raise DatabaseError(f"Failed to update delivery status: {str(e)}")

    async def get_delivery_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get delivery analytics for specified date range.

        Args:
            start_date: Start date for analytics
            end_date: End date for analytics

        Returns:
            Analytics data
        """
        try:
            # Base query with date filtering
            base_stmt = select(NotificationDelivery)
            if start_date:
                base_stmt = base_stmt.where(NotificationDelivery.created_at >= start_date)
            if end_date:
                base_stmt = base_stmt.where(NotificationDelivery.created_at <= end_date)

            # Total deliveries
            total_stmt = select(func.count()).select_from(base_stmt.subquery())
            total_result = await self.db.execute(total_stmt)
            total = total_result.scalar()

            # Status breakdown
            status_stmt = select(
                NotificationDelivery.status,
                func.count().label('count')
            )
            if start_date:
                status_stmt = status_stmt.where(NotificationDelivery.created_at >= start_date)
            if end_date:
                status_stmt = status_stmt.where(NotificationDelivery.created_at <= end_date)
            status_stmt = status_stmt.group_by(NotificationDelivery.status)

            status_result = await self.db.execute(status_stmt)
            status_breakdown = {row.status.value: row.count for row in status_result}

            # Calculate rates
            delivered = status_breakdown.get('delivered', 0)
            failed = status_breakdown.get('failed', 0)

            return {
                "total_deliveries": total,
                "status_breakdown": status_breakdown,
                "delivery_rate": (delivered / total * 100) if total > 0 else 0,
                "failure_rate": (failed / total * 100) if total > 0 else 0,
                "date_range": {
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                }
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting delivery analytics: {str(e)}")
            raise DatabaseError(f"Failed to get delivery analytics: {str(e)}")


class NotificationPreferenceRepository(BaseRepository[NotificationPreference]):
    """
    Repository for user notification preferences with bulk preference updates.

    Provides comprehensive preference operations including:
    - User preference management with category-specific settings
    - Bulk preference updates and default preference creation
    - Do not disturb (DND) scheduling and timezone handling
    - Preference analytics and usage statistics
    """

    def __init__(self, db: AsyncSession):
        super().__init__(NotificationPreference, db)

    async def get_by_user_id(self, user_id: int) -> Optional[NotificationPreference]:
        """
        Get notification preferences for a user.

        Args:
            user_id: User ID

        Returns:
            NotificationPreference instance if found
        """
        try:
            stmt = select(NotificationPreference).where(NotificationPreference.user_id == user_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except SQLAlchemyError as e:
            logger.error(f"Error getting user preferences: {str(e)}")
            raise DatabaseError(f"Failed to get user preferences: {str(e)}")

    async def create_default_preferences(self, user_id: int) -> NotificationPreference:
        """
        Create default notification preferences for a new user.

        Args:
            user_id: User ID

        Returns:
            Created preference instance
        """
        try:
            preferences = NotificationPreference(
                user_id=user_id,
                push_notifications_enabled=True,
                authentication_notifications=NotificationFrequency.IMMEDIATE,
                booking_notifications=NotificationFrequency.IMMEDIATE,
                payment_notifications=NotificationFrequency.IMMEDIATE,
                promotional_notifications=NotificationFrequency.DAILY,
                system_notifications=NotificationFrequency.IMMEDIATE,
                security_notifications=NotificationFrequency.IMMEDIATE,
                social_notifications=NotificationFrequency.IMMEDIATE,
                reminder_notifications=NotificationFrequency.IMMEDIATE,
                dnd_enabled=False,
                dnd_timezone="UTC",
                ios_badge_count=True,
                android_vibration=True,
                sound_enabled=True,
                language_code="en",
                preference_metadata={}
            )

            self.db.add(preferences)
            await self.db.commit()
            await self.db.refresh(preferences)

            logger.info(f"Created default preferences for user {user_id}")

            return preferences

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error creating default preferences: {str(e)}")
            raise DatabaseError(f"Failed to create default preferences: {str(e)}")

    async def bulk_update_category_preference(
        self,
        user_ids: List[int],
        category: str,
        frequency: NotificationFrequency
    ) -> int:
        """
        Bulk update notification frequency for a specific category across multiple users.

        Args:
            user_ids: List of user IDs
            category: Notification category field name
            frequency: New frequency setting

        Returns:
            Number of updated records
        """
        try:
            # Validate category field exists
            valid_categories = [
                'authentication_notifications', 'booking_notifications',
                'payment_notifications', 'promotional_notifications',
                'system_notifications', 'security_notifications',
                'social_notifications', 'reminder_notifications'
            ]

            if category not in valid_categories:
                raise ValidationError(f"Invalid category: {category}")

            stmt = (
                update(NotificationPreference)
                .where(NotificationPreference.user_id.in_(user_ids))
                .values({category: frequency, "updated_at": datetime.utcnow()})
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            logger.info(
                f"Bulk updated {category} to {frequency.value} for {result.rowcount} users",
                extra={"category": category, "frequency": frequency.value, "user_count": result.rowcount}
            )

            return result.rowcount

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error bulk updating category preference: {str(e)}")
            raise DatabaseError(f"Failed to bulk update category preference: {str(e)}")


class NotificationQueueRepository(BaseRepository[NotificationQueue]):
    """
    Repository for notification queue management with priority handling and batch processing.

    Provides comprehensive queue operations including:
    - Priority-based queue processing and scheduling
    - Batch processing with retry logic and error handling
    - Queue cleanup and maintenance operations
    - Performance monitoring and queue analytics
    """

    def __init__(self, db: AsyncSession):
        super().__init__(NotificationQueue, db)

    async def get_pending_notifications(
        self,
        limit: int = 100,
        priority: Optional[NotificationPriority] = None
    ) -> List[NotificationQueue]:
        """
        Get pending notifications for processing, ordered by priority and creation time.

        Args:
            limit: Maximum number of notifications to return
            priority: Optional priority filter

        Returns:
            List of pending notifications
        """
        try:
            stmt = select(NotificationQueue).where(
                and_(
                    NotificationQueue.status == NotificationStatus.PENDING,
                    or_(
                        NotificationQueue.scheduled_at.is_(None),
                        NotificationQueue.scheduled_at <= datetime.utcnow()
                    ),
                    or_(
                        NotificationQueue.expires_at.is_(None),
                        NotificationQueue.expires_at > datetime.utcnow()
                    )
                )
            )

            if priority:
                stmt = stmt.where(NotificationQueue.priority == priority)

            # Order by priority (critical first) then by creation time
            stmt = stmt.order_by(
                NotificationQueue.priority.desc(),
                NotificationQueue.created_at.asc()
            ).limit(limit)

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting pending notifications: {str(e)}")
            raise DatabaseError(f"Failed to get pending notifications: {str(e)}")

    async def mark_as_processing(self, queue_ids: List[UUID]) -> int:
        """
        Mark notifications as being processed.

        Args:
            queue_ids: List of queue item IDs

        Returns:
            Number of updated records
        """
        try:
            stmt = (
                update(NotificationQueue)
                .where(NotificationQueue.id.in_(queue_ids))
                .values(
                    processing_started_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error marking notifications as processing: {str(e)}")
            raise DatabaseError(f"Failed to mark notifications as processing: {str(e)}")

    async def complete_processing(
        self,
        queue_id: UUID,
        status: NotificationStatus,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Mark notification processing as complete.

        Args:
            queue_id: Queue item ID
            status: Final status
            error_code: Error code if failed
            error_message: Error message if failed

        Returns:
            True if updated successfully
        """
        try:
            update_data = {
                "status": status,
                "processed_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

            if status == NotificationStatus.FAILED:
                update_data["error_code"] = error_code
                update_data["error_message"] = error_message

            stmt = (
                update(NotificationQueue)
                .where(NotificationQueue.id == queue_id)
                .values(**update_data)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error completing notification processing: {str(e)}")
            raise DatabaseError(f"Failed to complete notification processing: {str(e)}")

    async def cleanup_expired_notifications(self) -> int:
        """
        Clean up expired notifications from the queue.

        Returns:
            Number of cleaned up notifications
        """
        try:
            stmt = delete(NotificationQueue).where(
                and_(
                    NotificationQueue.expires_at.is_not(None),
                    NotificationQueue.expires_at <= datetime.utcnow(),
                    NotificationQueue.status.in_([
                        NotificationStatus.PENDING,
                        NotificationStatus.FAILED
                    ])
                )
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            logger.info(f"Cleaned up {result.rowcount} expired notifications")

            return result.rowcount

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error cleaning up expired notifications: {str(e)}")
            raise DatabaseError(f"Failed to cleanup expired notifications: {str(e)}")

    async def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get queue statistics and performance metrics.

        Returns:
            Queue statistics
        """
        try:
            # Total queue items
            total_stmt = select(func.count()).select_from(NotificationQueue)
            total_result = await self.db.execute(total_stmt)
            total = total_result.scalar()

            # Status breakdown
            status_stmt = select(
                NotificationQueue.status,
                func.count().label('count')
            ).group_by(NotificationQueue.status)
            status_result = await self.db.execute(status_stmt)
            status_breakdown = {row.status.value: row.count for row in status_result}

            # Priority breakdown
            priority_stmt = select(
                NotificationQueue.priority,
                func.count().label('count')
            ).group_by(NotificationQueue.priority)
            priority_result = await self.db.execute(priority_stmt)
            priority_breakdown = {row.priority.value: row.count for row in priority_result}

            return {
                "total_queue_items": total,
                "status_breakdown": status_breakdown,
                "priority_breakdown": priority_breakdown,
                "pending_count": status_breakdown.get('pending', 0),
                "processing_count": len([k for k, v in status_breakdown.items() if 'processing' in k]),
                "completed_count": status_breakdown.get('sent', 0) + status_breakdown.get('delivered', 0)
            }

        except SQLAlchemyError as e:
            logger.error(f"Error getting queue stats: {str(e)}")
            raise DatabaseError(f"Failed to get queue stats: {str(e)}")
