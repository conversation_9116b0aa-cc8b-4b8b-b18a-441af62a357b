#!/usr/bin/env python3
"""
Create a minimal test MaxMind GeoIP database for testing purposes.

This script creates a mock GeoIP database file that can be used for testing
geolocation functionality without requiring a real MaxMind database.
"""

import os
import struct
from pathlib import Path

def create_test_geoip_database():
    """Create a minimal test GeoIP database file."""
    
    # Create test database directory
    test_data_dir = Path(__file__).parent
    test_db_path = test_data_dir / "test_geoip.mmdb"
    
    # Create a minimal binary file that resembles a MaxMind database
    # This is just for testing - not a real MaxMind database format
    test_data = b"MOCK_MAXMIND_GEOIP_DATABASE_FOR_TESTING_PURPOSES_ONLY"
    test_data += b"\x00" * 1000  # Pad to make it look like a real database file
    
    with open(test_db_path, "wb") as f:
        f.write(test_data)
    
    print(f"Created test GeoIP database: {test_db_path}")
    print(f"File size: {len(test_data)} bytes")
    
    return test_db_path

if __name__ == "__main__":
    create_test_geoip_database()
