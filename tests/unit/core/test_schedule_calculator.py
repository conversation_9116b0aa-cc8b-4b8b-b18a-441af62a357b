"""
Comprehensive tests for Schedule Calculator - Task 6.2.2 Phase 3.2

Tests cover:
- Next run time calculation based on cron expressions
- Timezone-aware execution with DST handling
- Business day and holiday calendar integration
- Performance benchmarking (<100ms calculation targets)
- Edge cases and error handling
"""

import pytest
from datetime import datetime, timedelta
import time
import zoneinfo

from app.core.schedule_calculator import (
    ScheduleCalculator,
    ScheduleExecution,
    ScheduleCalculationResult,
    HolidayProvider
)
from app.models.workflow_models import HolidayCalendar


class TestScheduleCalculator:
    """Test suite for ScheduleCalculator."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = ScheduleCalculator()
        self.test_start_time = datetime(2024, 1, 1, 0, 0, 0, tzinfo=zoneinfo.ZoneInfo("UTC"))
    
    def test_basic_schedule_calculation(self):
        """Test basic schedule calculation."""
        # Daily at midnight
        result = self.calculator.calculate_next_executions(
            "0 0 * * *",
            start_time=self.test_start_time,
            count=5
        )
        
        assert len(result.next_executions) == 5
        assert result.total_calculated == 5
        
        # Check that executions are daily
        for i in range(1, len(result.next_executions)):
            prev_exec = result.next_executions[i-1].execution_time
            curr_exec = result.next_executions[i].execution_time
            diff = curr_exec - prev_exec
            assert diff.days == 1
    
    def test_hourly_schedule_calculation(self):
        """Test hourly schedule calculation."""
        result = self.calculator.calculate_next_executions(
            "0 * * * *",
            start_time=self.test_start_time,
            count=24
        )
        
        assert len(result.next_executions) == 24
        
        # Check that executions are hourly
        for i in range(1, len(result.next_executions)):
            prev_exec = result.next_executions[i-1].execution_time
            curr_exec = result.next_executions[i].execution_time
            diff = curr_exec - prev_exec
            assert diff.total_seconds() == 3600  # 1 hour
    
    def test_specific_time_schedule(self):
        """Test schedule at specific times."""
        # Every day at 9:30 AM
        result = self.calculator.calculate_next_executions(
            "30 9 * * *",
            start_time=self.test_start_time,
            count=3
        )
        
        assert len(result.next_executions) == 3
        
        for execution in result.next_executions:
            assert execution.execution_time.hour == 9
            assert execution.execution_time.minute == 30
    
    def test_weekday_schedule(self):
        """Test weekday-based schedule."""
        # Every Monday at 9 AM
        result = self.calculator.calculate_next_executions(
            "0 9 * * 1",
            start_time=self.test_start_time,
            count=4
        )
        
        assert len(result.next_executions) == 4
        
        for execution in result.next_executions:
            # Monday is weekday 0 in Python (0=Monday, 6=Sunday)
            assert execution.execution_time.weekday() == 0
            assert execution.execution_time.hour == 9
            assert execution.execution_time.minute == 0
    
    def test_step_values_schedule(self):
        """Test step values in schedule."""
        # Every 15 minutes
        result = self.calculator.calculate_next_executions(
            "*/15 * * * *",
            start_time=self.test_start_time,
            count=8
        )
        
        assert len(result.next_executions) == 8
        
        # Check 15-minute intervals
        for execution in result.next_executions:
            assert execution.execution_time.minute in [0, 15, 30, 45]
    
    def test_timezone_aware_calculation(self):
        """Test timezone-aware schedule calculation."""
        # Test with different timezones
        timezones = ["UTC", "America/New_York", "Europe/London", "Asia/Tokyo"]
        
        for tz in timezones:
            start_time = datetime(2024, 6, 1, 12, 0, 0, tzinfo=zoneinfo.ZoneInfo(tz))
            
            result = self.calculator.calculate_next_executions(
                "0 0 * * *",  # Daily at midnight
                start_time=start_time,
                count=3,
                timezone=tz
            )
            
            assert len(result.next_executions) == 3
            
            for execution in result.next_executions:
                assert execution.timezone == tz
                assert execution.execution_time.hour == 0
                assert execution.execution_time.minute == 0
    
    def test_business_days_only_filtering(self):
        """Test business days only filtering."""
        # Start on a Friday
        start_time = datetime(2024, 1, 5, 0, 0, 0, tzinfo=zoneinfo.ZoneInfo("UTC"))  # Friday
        
        result = self.calculator.calculate_next_executions(
            "0 9 * * *",  # Daily at 9 AM
            start_time=start_time,
            count=5,
            business_days_only=True
        )
        
        assert len(result.next_executions) == 5
        assert result.business_days_filtered >= 0
        
        # All executions should be on business days
        for execution in result.next_executions:
            assert execution.is_business_day
            # Monday=0, Friday=4
            assert execution.execution_time.weekday() < 5
    
    def test_holiday_exclusion(self):
        """Test holiday exclusion."""
        # Start near New Year's Day
        start_time = datetime(2023, 12, 30, 0, 0, 0, tzinfo=zoneinfo.ZoneInfo("UTC"))
        
        result = self.calculator.calculate_next_executions(
            "0 9 * * *",  # Daily at 9 AM
            start_time=start_time,
            count=5,
            exclude_holidays=True,
            holiday_calendar=HolidayCalendar.US
        )
        
        assert len(result.next_executions) == 5
        
        # Check that New Year's Day (Jan 1) is excluded
        for execution in result.next_executions:
            exec_date = execution.execution_time.date()
            if exec_date.month == 1 and exec_date.day == 1:
                assert not execution.is_holiday or result.holidays_filtered > 0
    
    def test_performance_target_single_calculation(self):
        """Test <50ms performance target for single next execution."""
        start_time = time.time()
        
        result = self.calculator.calculate_next_executions(
            "0 0 * * *",
            start_time=self.test_start_time,
            count=1
        )
        
        end_time = time.time()
        calculation_time_ms = (end_time - start_time) * 1000
        
        assert calculation_time_ms < 50, f"Single calculation took {calculation_time_ms}ms, expected <50ms"
        assert result.calculation_time_ms < 50
    
    def test_performance_target_multiple_calculations(self):
        """Test <100ms performance target for 10 executions."""
        start_time = time.time()
        
        result = self.calculator.calculate_next_executions(
            "*/15 9-17 * * 1-5",  # Complex expression
            start_time=self.test_start_time,
            count=10
        )
        
        end_time = time.time()
        calculation_time_ms = (end_time - start_time) * 1000
        
        assert calculation_time_ms < 100, f"Multiple calculations took {calculation_time_ms}ms, expected <100ms"
        assert result.calculation_time_ms < 100
        assert len(result.next_executions) == 10
    
    def test_complex_schedule_calculation(self):
        """Test complex schedule calculations."""
        # Every 15 minutes during business hours on weekdays
        result = self.calculator.calculate_next_executions(
            "*/15 9-17 * * 1-5",
            start_time=self.test_start_time,
            count=20
        )
        
        assert len(result.next_executions) == 20
        
        for execution in result.next_executions:
            # Should be during business hours (9-17)
            assert 9 <= execution.execution_time.hour <= 17
            # Should be on weekdays (Monday=0 to Friday=4)
            assert execution.execution_time.weekday() < 5
            # Should be on 15-minute intervals
            assert execution.execution_time.minute in [0, 15, 30, 45]
    
    def test_edge_case_end_of_month(self):
        """Test edge case with end of month."""
        # Last day of month
        start_time = datetime(2024, 1, 30, 0, 0, 0, tzinfo=zoneinfo.ZoneInfo("UTC"))
        
        result = self.calculator.calculate_next_executions(
            "0 0 31 * *",  # 31st of each month
            start_time=start_time,
            count=3
        )
        
        # Should find executions in months with 31 days
        assert len(result.next_executions) >= 1
        
        for execution in result.next_executions:
            assert execution.execution_time.day == 31
    
    def test_edge_case_leap_year(self):
        """Test edge case with leap year."""
        # February 29th in leap year
        start_time = datetime(2024, 2, 28, 0, 0, 0, tzinfo=zoneinfo.ZoneInfo("UTC"))
        
        result = self.calculator.calculate_next_executions(
            "0 0 29 2 *",  # Feb 29th
            start_time=start_time,
            count=1
        )
        
        # Should find Feb 29, 2024 (leap year)
        assert len(result.next_executions) == 1
        execution = result.next_executions[0]
        assert execution.execution_time.month == 2
        assert execution.execution_time.day == 29
        assert execution.execution_time.year == 2024
    
    def test_no_valid_executions(self):
        """Test case where no valid executions are found."""
        # Invalid date that doesn't exist
        result = self.calculator.calculate_next_executions(
            "0 0 31 2 *",  # Feb 31st (doesn't exist)
            start_time=self.test_start_time,
            count=1,
            max_iterations=100  # Limit iterations
        )
        
        # Should return empty results
        assert len(result.next_executions) == 0
    
    def test_calculation_result_metadata(self):
        """Test calculation result metadata."""
        result = self.calculator.calculate_next_executions(
            "0 9 * * 1-5",  # Weekdays at 9 AM
            start_time=self.test_start_time,
            count=5,
            business_days_only=True
        )
        
        assert isinstance(result, ScheduleCalculationResult)
        assert isinstance(result.next_executions, list)
        assert isinstance(result.calculation_time_ms, float)
        assert isinstance(result.total_calculated, int)
        assert isinstance(result.business_days_filtered, int)
        assert isinstance(result.holidays_filtered, int)
        assert isinstance(result.conflicts_detected, int)
        
        assert result.total_calculated == len(result.next_executions)
        assert result.calculation_time_ms > 0


class TestHolidayProvider:
    """Test suite for HolidayProvider."""
    
    def test_us_holidays(self):
        """Test US holiday detection."""
        from datetime import date
        
        # New Year's Day
        assert HolidayProvider.is_holiday(date(2024, 1, 1), HolidayCalendar.US)
        # Independence Day
        assert HolidayProvider.is_holiday(date(2024, 7, 4), HolidayCalendar.US)
        # Christmas
        assert HolidayProvider.is_holiday(date(2024, 12, 25), HolidayCalendar.US)
        
        # Non-holiday
        assert not HolidayProvider.is_holiday(date(2024, 6, 15), HolidayCalendar.US)
    
    def test_business_day_detection(self):
        """Test business day detection."""
        from datetime import date
        
        # Monday (business day)
        assert HolidayProvider.is_business_day(date(2024, 1, 1))  # Monday
        # Friday (business day)
        assert HolidayProvider.is_business_day(date(2024, 1, 5))  # Friday
        
        # Saturday (weekend)
        assert not HolidayProvider.is_business_day(date(2024, 1, 6))  # Saturday
        # Sunday (weekend)
        assert not HolidayProvider.is_business_day(date(2024, 1, 7))  # Sunday
    
    def test_custom_calendar(self):
        """Test custom calendar handling."""
        from datetime import date
        
        # Custom calendar should return False (not implemented)
        assert not HolidayProvider.is_holiday(date(2024, 1, 1), HolidayCalendar.CUSTOM)
