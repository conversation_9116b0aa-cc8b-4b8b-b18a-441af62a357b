# Culture Connect Backend - Certificate Manager Deployment
# Automated SSL/TLS certificate management using cert-manager

apiVersion: v1
kind: Namespace
metadata:
  name: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/instance: cert-manager

---
# ClusterIssuer for Let's Encrypt production certificates
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: cluster-issuer
spec:
  acme:
    # ACME server URL for Let's Encrypt production
    server: https://acme-v02.api.letsencrypt.org/directory
    
    # Email address for ACME registration
    email: <EMAIL>
    
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    
    # ACME HTTP-01 challenge solver
    solvers:
    - http01:
        ingress:
          class: nginx
          podTemplate:
            metadata:
              labels:
                app.kubernetes.io/name: cert-manager
                app.kubernetes.io/component: http01-solver
            spec:
              securityContext:
                runAsNonRoot: true
                runAsUser: 65534
                runAsGroup: 65534
              containers:
              - name: acme-http01-solver
                securityContext:
                  allowPrivilegeEscalation: false
                  readOnlyRootFilesystem: true
                  runAsNonRoot: true
                  runAsUser: 65534
                  runAsGroup: 65534
                  capabilities:
                    drop:
                    - ALL
                resources:
                  requests:
                    memory: "16Mi"
                    cpu: "10m"
                  limits:
                    memory: "32Mi"
                    cpu: "20m"
    
    # DNS-01 challenge solver for wildcard certificates (optional)
    - dns01:
        route53:
          region: us-west-2
          accessKeyID: REPLACE_WITH_AWS_ACCESS_KEY_ID
          secretAccessKeySecretRef:
            name: route53-credentials
            key: secret-access-key
      selector:
        dnsNames:
        - "*.cultureconnect.ng"

---
# ClusterIssuer for Let's Encrypt staging (for testing)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: cluster-issuer
spec:
  acme:
    # ACME server URL for Let's Encrypt staging
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    
    # Email address for ACME registration
    email: <EMAIL>
    
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-staging-private-key
    
    # ACME HTTP-01 challenge solver
    solvers:
    - http01:
        ingress:
          class: nginx

---
# Certificate for main API domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: culture-connect-api-tls
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: certificate
spec:
  secretName: culture-connect-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - api.cultureconnect.ng
  - cultureconnect.ng
  - www.cultureconnect.ng
  usages:
  - digital signature
  - key encipherment
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days

---
# Certificate for monitoring services
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: culture-connect-monitoring-tls
  namespace: monitoring
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: certificate
spec:
  secretName: monitoring-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - grafana.cultureconnect.ng
  - prometheus.cultureconnect.ng
  - alertmanager.cultureconnect.ng
  usages:
  - digital signature
  - key encipherment
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days

---
# Certificate for staging environment
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: culture-connect-staging-tls
  namespace: culture-connect-staging
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: certificate
spec:
  secretName: staging-tls
  issuerRef:
    name: letsencrypt-staging
    kind: ClusterIssuer
  dnsNames:
  - staging-api.cultureconnect.ng
  - staging.cultureconnect.ng
  usages:
  - digital signature
  - key encipherment
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days

---
# Secret for Route53 DNS credentials (for wildcard certificates)
apiVersion: v1
kind: Secret
metadata:
  name: route53-credentials
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: dns-credentials
type: Opaque
data:
  # Base64 encoded AWS secret access key
  secret-access-key: REPLACE_WITH_BASE64_ENCODED_AWS_SECRET_ACCESS_KEY

---
# ServiceMonitor for cert-manager metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager-metrics
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
      app.kubernetes.io/component: controller
  endpoints:
  - port: http-metrics
    interval: 60s
    path: /metrics
    honorLabels: true

---
# PrometheusRule for cert-manager alerting
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cert-manager-alerts
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: alerting
spec:
  groups:
  - name: cert-manager.alerts
    rules:
    - alert: CertManagerCertificateExpiringSoon
      expr: certmanager_certificate_expiration_timestamp_seconds - time() < 604800
      for: 1h
      labels:
        severity: warning
        component: certificate
      annotations:
        summary: "Certificate expiring soon"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} expires in less than 7 days"
        runbook_url: "https://docs.cultureconnect.ng/runbooks/certificate-expiring"

    - alert: CertManagerCertificateNotReady
      expr: certmanager_certificate_ready_status == 0
      for: 10m
      labels:
        severity: critical
        component: certificate
      annotations:
        summary: "Certificate not ready"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} is not ready"
        runbook_url: "https://docs.cultureconnect.ng/runbooks/certificate-not-ready"

    - alert: CertManagerACMEChallengesFailing
      expr: increase(certmanager_acme_client_request_count{status!~"2.."}[5m]) > 0
      for: 5m
      labels:
        severity: warning
        component: acme
      annotations:
        summary: "ACME challenges failing"
        description: "ACME challenges are failing for cert-manager"
        runbook_url: "https://docs.cultureconnect.ng/runbooks/acme-challenges-failing"
