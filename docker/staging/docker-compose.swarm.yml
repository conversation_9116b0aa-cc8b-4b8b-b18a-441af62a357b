# Culture Connect Backend - Docker Swarm Configuration
# Docker Swarm deployment for development and staging environments

version: '3.8'

services:
  # Culture Connect Backend API - Swarm Configuration
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: culture-connect-api:${CC_VERSION:-latest}
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 30s
        order: start-first
      placement:
        constraints:
          - node.role == worker
        preferences:
          - spread: node.labels.zone
    ports:
      - target: 8000
        published: 8000
        protocol: tcp
        mode: ingress
    environment:
      - CC_ENVIRONMENT=${CC_ENVIRONMENT:-staging}
      - CC_DATABASE_URL=${CC_DATABASE_URL}
      - CC_REDIS_URL=${CC_REDIS_URL}
      - CC_SECRET_KEY=${CC_SECRET_KEY}
      - CC_SENTRY_DSN=${CC_SENTRY_DSN}
      - CC_LOG_LEVEL=${CC_LOG_LEVEL:-INFO}
      - CC_WORKERS=2
    depends_on:
      - db
      - redis
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - culture_connect_swarm_network
      - traefik_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-swarm.rule=Host(`${CC_API_DOMAIN:-api-staging.cultureconnect.ng}`)"
      - "traefik.http.routers.api-swarm.tls=true"
      - "traefik.http.routers.api-swarm.tls.certresolver=letsencrypt"
      - "traefik.http.services.api-swarm.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik_network"

  # PostgreSQL Database - Swarm Configuration
  db:
    image: postgres:15-alpine
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role == manager
    environment:
      - POSTGRES_DB=${CC_POSTGRES_DB:-culture_connect_staging_db}
      - POSTGRES_USER=${CC_POSTGRES_USER:-culture_connect_staging}
      - POSTGRES_PASSWORD=${CC_POSTGRES_PASSWORD}
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - target: 5432
        published: 5432
        protocol: tcp
        mode: host
    volumes:
      - postgres_data_swarm:/var/lib/postgresql/data
      - postgres_backups_swarm:/backups
    networks:
      - culture_connect_swarm_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${CC_POSTGRES_USER:-culture_connect_staging} -d ${CC_POSTGRES_DB:-culture_connect_staging_db}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis Cache - Swarm Configuration
  redis:
    image: redis:7-alpine
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role == manager
    environment:
      - REDIS_PASSWORD=${CC_REDIS_PASSWORD}
    ports:
      - target: 6379
        published: 6379
        protocol: tcp
        mode: host
    volumes:
      - redis_data_swarm:/data
    networks:
      - culture_connect_swarm_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: >
      redis-server
      --requirepass ${CC_REDIS_PASSWORD}
      --maxmemory 200mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes

  # Celery Worker - Swarm Configuration
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: culture-connect-api:${CC_VERSION:-latest}
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role == worker
    environment:
      - CC_ENVIRONMENT=${CC_ENVIRONMENT:-staging}
      - CC_DATABASE_URL=${CC_DATABASE_URL}
      - CC_REDIS_URL=${CC_REDIS_URL}
      - CC_SECRET_KEY=${CC_SECRET_KEY}
      - CC_SENTRY_DSN=${CC_SENTRY_DSN}
      - CELERY_WORKER_CONCURRENCY=2
    depends_on:
      - db
      - redis
    volumes:
      - app_logs:/app/logs
    networks:
      - culture_connect_swarm_network
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_config", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    command: ["celery", "-A", "app.core.celery_config", "worker", "--loglevel=info", "--concurrency=2"]

  # Traefik Load Balancer - Swarm Configuration
  traefik:
    image: traefik:v3.0
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role == manager
    ports:
      - target: 80
        published: 80
        protocol: tcp
        mode: ingress
      - target: 443
        published: 443
        protocol: tcp
        mode: ingress
      - target: 8080
        published: 8080
        protocol: tcp
        mode: ingress
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true
      - TRAEFIK_PROVIDERS_DOCKER=true
      - TRAEFIK_PROVIDERS_DOCKER_SWARMMODE=true
      - TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
      - TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=${CC_ACME_EMAIL}
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_STORAGE=/letsencrypt/acme.json
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_HTTPCHALLENGE_ENTRYPOINT=web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
    networks:
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.${CC_DOMAIN:-staging.cultureconnect.ng}`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

# Swarm Volumes
volumes:
  postgres_data_swarm:
    driver: local
  postgres_backups_swarm:
    driver: local
  redis_data_swarm:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  traefik_letsencrypt:
    driver: local

# Swarm Networks
networks:
  culture_connect_swarm_network:
    driver: overlay
    attachable: true
    labels:
      - "environment=${CC_ENVIRONMENT:-staging}"
      - "project=culture-connect"
  traefik_network:
    driver: overlay
    external: true
