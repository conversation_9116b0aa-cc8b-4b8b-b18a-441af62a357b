"""Add performance monitoring tables for Phase 7.2

Revision ID: 20250525_2300_perf_monitoring
Revises: 20250524_0200
Create Date: 2025-01-25 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250525_2300_perf_monitoring'
down_revision = '20250524_0200'
branch_labels = None
depends_on = None


def upgrade():
    """Create performance monitoring tables for Phase 7.2."""
    
    # Create performance_metrics table
    op.create_table(
        'performance_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('metric_type', sa.Enum(
            'API_RESPONSE_TIME', 'DATABASE_QUERY_TIME', 'CACHE_HIT_RATE', 
            'MEMORY_USAGE', 'CPU_USAGE', 'DISK_USAGE', 'NETWORK_LATENCY', 
            'ERROR_RATE', 'THROUGHPUT', 'CONCURRENT_CONNECTIONS',
            name='performancemetrictype'
        ), nullable=False),
        sa.Column('metric_name', sa.String(length=100), nullable=False),
        sa.Column('component', sa.String(length=50), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('timeframe', sa.Enum(
            'REAL_TIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY',
            name='analyticstimeframe'
        ), nullable=False),
        sa.Column('value', sa.Numeric(precision=15, scale=4), nullable=False),
        sa.Column('min_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('max_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('avg_value', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('percentile_95', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('percentile_99', sa.Numeric(precision=15, scale=4), nullable=True),
        sa.Column('sample_count', sa.Integer(), nullable=False, default=1),
        sa.Column('error_count', sa.Integer(), nullable=False, default=0),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metric_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.CheckConstraint('value >= 0', name='non_negative_value'),
        sa.CheckConstraint('sample_count > 0', name='positive_sample_count'),
        sa.CheckConstraint('error_count >= 0', name='non_negative_error_count'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for performance_metrics
    op.create_index('idx_performance_metrics_type_component', 'performance_metrics', ['metric_type', 'component'])
    op.create_index('idx_performance_metrics_timestamp', 'performance_metrics', ['timestamp'])
    op.create_index('idx_performance_metrics_timeframe_timestamp', 'performance_metrics', ['timeframe', 'timestamp'])
    op.create_index('idx_performance_metrics_component_timestamp', 'performance_metrics', ['component', 'timestamp'])
    op.create_index('idx_performance_metrics_name_timestamp', 'performance_metrics', ['metric_name', 'timestamp'])
    op.create_index('idx_performance_metrics_metric_type', 'performance_metrics', ['metric_type'])
    op.create_index('idx_performance_metrics_component', 'performance_metrics', ['component'])
    op.create_index('idx_performance_metrics_metric_name', 'performance_metrics', ['metric_name'])
    
    # Create system_health table
    op.create_table(
        'system_health',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('component', sa.String(length=50), nullable=False),
        sa.Column('service_name', sa.String(length=100), nullable=False),
        sa.Column('status', sa.Enum(
            'HEALTHY', 'WARNING', 'CRITICAL', 'DOWN', 'MAINTENANCE',
            name='systemhealthstatus'
        ), nullable=False),
        sa.Column('previous_status', sa.Enum(
            'HEALTHY', 'WARNING', 'CRITICAL', 'DOWN', 'MAINTENANCE',
            name='systemhealthstatus'
        ), nullable=True),
        sa.Column('status_changed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('response_time', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('uptime_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('error_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('cpu_usage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('memory_usage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('disk_usage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('dependencies_healthy', sa.Boolean(), nullable=False, default=True),
        sa.Column('last_check_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('next_check_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('alert_threshold', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('alert_sent', sa.Boolean(), nullable=False, default=False),
        sa.Column('alert_sent_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('health_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.CheckConstraint('uptime_percentage >= 0 AND uptime_percentage <= 100', name='valid_uptime_percentage'),
        sa.CheckConstraint('error_rate >= 0 AND error_rate <= 1', name='valid_error_rate'),
        sa.CheckConstraint('cpu_usage >= 0 AND cpu_usage <= 100', name='valid_cpu_usage'),
        sa.CheckConstraint('memory_usage >= 0 AND memory_usage <= 100', name='valid_memory_usage'),
        sa.CheckConstraint('disk_usage >= 0 AND disk_usage <= 100', name='valid_disk_usage'),
        sa.CheckConstraint('response_time >= 0', name='non_negative_response_time'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uuid')
    )
    
    # Create indexes for system_health
    op.create_index('idx_system_health_component_status', 'system_health', ['component', 'status'])
    op.create_index('idx_system_health_service_status', 'system_health', ['service_name', 'status'])
    op.create_index('idx_system_health_last_check', 'system_health', ['last_check_at'])
    op.create_index('idx_system_health_next_check', 'system_health', ['next_check_at'])
    op.create_index('idx_system_health_status_changed', 'system_health', ['status_changed_at'])
    op.create_index('idx_system_health_alert_status', 'system_health', ['alert_sent', 'status'])
    op.create_index('idx_system_health_component', 'system_health', ['component'])
    op.create_index('idx_system_health_service_name', 'system_health', ['service_name'])
    op.create_index('idx_system_health_status', 'system_health', ['status'])


def downgrade():
    """Drop performance monitoring tables."""
    
    # Drop indexes first
    op.drop_index('idx_system_health_status', table_name='system_health')
    op.drop_index('idx_system_health_service_name', table_name='system_health')
    op.drop_index('idx_system_health_component', table_name='system_health')
    op.drop_index('idx_system_health_alert_status', table_name='system_health')
    op.drop_index('idx_system_health_status_changed', table_name='system_health')
    op.drop_index('idx_system_health_next_check', table_name='system_health')
    op.drop_index('idx_system_health_last_check', table_name='system_health')
    op.drop_index('idx_system_health_service_status', table_name='system_health')
    op.drop_index('idx_system_health_component_status', table_name='system_health')
    
    op.drop_index('idx_performance_metrics_metric_name', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_component', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_metric_type', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_name_timestamp', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_component_timestamp', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_timeframe_timestamp', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_timestamp', table_name='performance_metrics')
    op.drop_index('idx_performance_metrics_type_component', table_name='performance_metrics')
    
    # Drop tables
    op.drop_table('system_health')
    op.drop_table('performance_metrics')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS systemhealthstatus')
    op.execute('DROP TYPE IF EXISTS performancemetrictype')
