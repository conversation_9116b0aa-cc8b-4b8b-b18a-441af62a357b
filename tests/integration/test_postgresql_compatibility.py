"""
PostgreSQL Compatibility Integration Tests

This module tests PostgreSQL-specific features and ensures compatibility
with the production database environment.
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, Any, List
from uuid import uuid4

from sqlalchemy import text, select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.dialects import postgresql

from app.db.session import get_async_session_context
from app.models.user import User
from app.models.vendor import Vendor, VendorProfile, VendorDocument
from app.core.config import settings


class TestPostgreSQLCompatibility:
    """Test PostgreSQL-specific features and compatibility."""

    @pytest.mark.asyncio
    async def test_database_connection(self):
        """Test basic PostgreSQL connection."""
        async with get_async_session_context() as session:
            result = await session.execute(text("SELECT version()"))
            version = result.scalar()
            
            assert "PostgreSQL" in version
            assert "15." in version  # Ensure PostgreSQL 15+

    @pytest.mark.asyncio
    async def test_uuid_extension(self):
        """Test UUID extension functionality."""
        async with get_async_session_context() as session:
            # Test uuid_generate_v4() function
            result = await session.execute(text("SELECT uuid_generate_v4()"))
            uuid_value = result.scalar()
            
            assert uuid_value is not None
            assert len(str(uuid_value)) == 36  # Standard UUID format

    @pytest.mark.asyncio
    async def test_jsonb_operations(self):
        """Test JSONB column operations."""
        async with get_async_session_context() as session:
            # Create test vendor profile with JSONB data
            user = User(
                email="<EMAIL>",
                hashed_password="hashed",
                first_name="Test",
                last_name="User",
                role="vendor"
            )
            session.add(user)
            await session.flush()

            vendor = Vendor(
                user_id=user.id,
                business_name="Test Business",
                business_type="cultural_tour"
            )
            session.add(vendor)
            await session.flush()

            profile = VendorProfile(
                vendor_id=vendor.id,
                languages_spoken=["English", "Yoruba", "Igbo"],
                specializations=["cultural_tours", "historical_sites"],
                operating_hours={
                    "monday": {"open": "09:00", "close": "17:00"},
                    "tuesday": {"open": "09:00", "close": "17:00"}
                }
            )
            session.add(profile)
            await session.commit()

            # Test JSONB queries
            # Test array contains
            result = await session.execute(
                select(VendorProfile).where(
                    VendorProfile.languages_spoken.op('@>')(['English'])
                )
            )
            found_profile = result.scalar_one_or_none()
            assert found_profile is not None

            # Test JSON path queries
            result = await session.execute(
                select(VendorProfile).where(
                    VendorProfile.operating_hours['monday']['open'].astext == '09:00'
                )
            )
            found_profile = result.scalar_one_or_none()
            assert found_profile is not None

    @pytest.mark.asyncio
    async def test_full_text_search(self):
        """Test PostgreSQL full-text search capabilities."""
        async with get_async_session_context() as session:
            # Create test users with search content
            users_data = [
                {
                    "email": "<EMAIL>",
                    "first_name": "John",
                    "last_name": "Doe",
                    "bio": "Cultural tour guide specializing in Nigerian heritage"
                },
                {
                    "email": "<EMAIL>",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "bio": "Expert in traditional African art and crafts"
                }
            ]

            for user_data in users_data:
                user = User(
                    email=user_data["email"],
                    hashed_password="hashed",
                    first_name=user_data["first_name"],
                    last_name=user_data["last_name"],
                    bio=user_data["bio"],
                    role="vendor"
                )
                session.add(user)

            await session.commit()

            # Test full-text search (assuming search_vector is populated by triggers)
            result = await session.execute(
                text("""
                    SELECT * FROM users 
                    WHERE search_vector @@ plainto_tsquery('english', 'cultural guide')
                """)
            )
            search_results = result.fetchall()
            assert len(search_results) > 0

    @pytest.mark.asyncio
    async def test_gin_indexes(self):
        """Test GIN index functionality for JSONB columns."""
        async with get_async_session_context() as session:
            # Check if GIN indexes exist
            result = await session.execute(
                text("""
                    SELECT indexname, indexdef 
                    FROM pg_indexes 
                    WHERE indexdef LIKE '%gin%' 
                    AND tablename IN ('vendor_profiles', 'users')
                """)
            )
            gin_indexes = result.fetchall()
            assert len(gin_indexes) > 0

    @pytest.mark.asyncio
    async def test_spatial_data_support(self):
        """Test spatial data operations for geo-location features."""
        async with get_async_session_context() as session:
            # Create vendor profile with coordinates
            user = User(
                email="<EMAIL>",
                hashed_password="hashed",
                first_name="Spatial",
                last_name="Test",
                role="vendor"
            )
            session.add(user)
            await session.flush()

            vendor = Vendor(
                user_id=user.id,
                business_name="Spatial Business",
                business_type="cultural_tour"
            )
            session.add(vendor)
            await session.flush()

            profile = VendorProfile(
                vendor_id=vendor.id,
                latitude=Decimal("6.5244"),  # Lagos coordinates
                longitude=Decimal("3.3792"),
                city="Lagos",
                country="Nigeria"
            )
            session.add(profile)
            await session.commit()

            # Test spatial queries (distance calculation)
            result = await session.execute(
                text("""
                    SELECT *, 
                    SQRT(POW(latitude - 6.5244, 2) + POW(longitude - 3.3792, 2)) as distance
                    FROM vendor_profiles 
                    WHERE latitude IS NOT NULL AND longitude IS NOT NULL
                    ORDER BY distance
                """)
            )
            spatial_results = result.fetchall()
            assert len(spatial_results) > 0

    @pytest.mark.asyncio
    async def test_enum_types(self):
        """Test PostgreSQL enum type functionality."""
        async with get_async_session_context() as session:
            # Check if enum types exist
            result = await session.execute(
                text("""
                    SELECT typname FROM pg_type 
                    WHERE typtype = 'e' 
                    AND typname IN ('user_role', 'verification_status', 'marketplace_status')
                """)
            )
            enum_types = result.fetchall()
            # Note: Enums might not be created yet if migration hasn't run
            # This test validates the capability

    @pytest.mark.asyncio
    async def test_advanced_constraints(self):
        """Test PostgreSQL advanced constraint features."""
        async with get_async_session_context() as session:
            # Test check constraints
            result = await session.execute(
                text("""
                    SELECT conname, consrc 
                    FROM pg_constraint 
                    WHERE contype = 'c' 
                    AND conrelid IN (
                        SELECT oid FROM pg_class 
                        WHERE relname IN ('users', 'vendors', 'vendor_profiles')
                    )
                """)
            )
            constraints = result.fetchall()
            assert len(constraints) > 0

    @pytest.mark.asyncio
    async def test_connection_pooling(self):
        """Test connection pooling functionality."""
        async with get_async_session_context() as session:
            # Test multiple concurrent connections
            tasks = []
            for i in range(5):
                task = asyncio.create_task(self._test_concurrent_query(i))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            assert all(result is True for result in results)

    async def _test_concurrent_query(self, query_id: int) -> bool:
        """Helper method for concurrent connection testing."""
        try:
            async with get_async_session_context() as session:
                result = await session.execute(
                    text(f"SELECT {query_id} as query_id, pg_backend_pid() as pid")
                )
                row = result.fetchone()
                return row is not None
        except Exception:
            return False

    @pytest.mark.asyncio
    async def test_transaction_isolation(self):
        """Test PostgreSQL transaction isolation levels."""
        async with get_async_session_context() as session:
            # Test current isolation level
            result = await session.execute(
                text("SHOW transaction_isolation")
            )
            isolation_level = result.scalar()
            assert isolation_level in ['read committed', 'repeatable read', 'serializable']

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test PostgreSQL performance monitoring capabilities."""
        async with get_async_session_context() as session:
            # Check if pg_stat_statements extension is available
            result = await session.execute(
                text("""
                    SELECT EXISTS(
                        SELECT 1 FROM pg_extension 
                        WHERE extname = 'pg_stat_statements'
                    )
                """)
            )
            has_pg_stat_statements = result.scalar()
            
            # Test basic performance stats
            result = await session.execute(
                text("""
                    SELECT 
                        schemaname, 
                        tablename, 
                        n_tup_ins, 
                        n_tup_upd, 
                        n_tup_del 
                    FROM pg_stat_user_tables 
                    WHERE schemaname = 'public'
                """)
            )
            table_stats = result.fetchall()
            # Should have stats for our tables

    @pytest.mark.asyncio
    async def test_backup_and_recovery_readiness(self):
        """Test backup and recovery readiness."""
        async with get_async_session_context() as session:
            # Test WAL (Write-Ahead Logging) status
            result = await session.execute(
                text("SELECT pg_is_in_recovery()")
            )
            is_in_recovery = result.scalar()
            assert is_in_recovery is False  # Should not be in recovery mode

            # Test current WAL location
            result = await session.execute(
                text("SELECT pg_current_wal_lsn()")
            )
            wal_location = result.scalar()
            assert wal_location is not None

    @pytest.mark.asyncio
    async def test_database_size_and_stats(self):
        """Test database size and statistics queries."""
        async with get_async_session_context() as session:
            # Test database size
            result = await session.execute(
                text("SELECT pg_size_pretty(pg_database_size(current_database()))")
            )
            db_size = result.scalar()
            assert db_size is not None

            # Test table sizes
            result = await session.execute(
                text("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                """)
            )
            table_sizes = result.fetchall()
            assert len(table_sizes) > 0


class TestPostgreSQLMigrationCompatibility:
    """Test migration compatibility between SQLite and PostgreSQL."""

    @pytest.mark.asyncio
    async def test_data_type_compatibility(self):
        """Test data type compatibility between SQLite and PostgreSQL."""
        async with get_async_session_context() as session:
            # Test various data types
            test_data = {
                "string_field": "test string",
                "integer_field": 42,
                "decimal_field": Decimal("123.45"),
                "boolean_field": True,
                "datetime_field": datetime.now(timezone.utc),
                "json_field": {"key": "value", "nested": {"data": [1, 2, 3]}}
            }

            # Create test record
            user = User(
                email="<EMAIL>",
                hashed_password="hashed",
                first_name="DataType",
                last_name="Test",
                role="customer",
                is_active=test_data["boolean_field"],
                created_at=test_data["datetime_field"]
            )
            session.add(user)
            await session.commit()

            # Verify data integrity
            result = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            retrieved_user = result.scalar_one()
            
            assert retrieved_user.first_name == test_data["string_field"].replace("test string", "DataType")
            assert retrieved_user.is_active == test_data["boolean_field"]
            assert retrieved_user.created_at is not None

    @pytest.mark.asyncio
    async def test_migration_rollback_capability(self):
        """Test migration rollback capabilities."""
        # This would typically be tested with actual migration scripts
        # For now, we test the basic rollback functionality
        async with get_async_session_context() as session:
            # Test transaction rollback
            try:
                user = User(
                    email="<EMAIL>",
                    hashed_password="hashed",
                    first_name="Rollback",
                    last_name="Test",
                    role="customer"
                )
                session.add(user)
                await session.flush()
                
                # Force an error to test rollback
                await session.execute(text("SELECT 1/0"))  # Division by zero
                await session.commit()
                
            except Exception:
                await session.rollback()
                
                # Verify rollback worked
                result = await session.execute(
                    select(User).where(User.email == "<EMAIL>")
                )
                rolled_back_user = result.scalar_one_or_none()
                assert rolled_back_user is None
