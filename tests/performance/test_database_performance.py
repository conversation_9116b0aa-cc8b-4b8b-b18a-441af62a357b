"""
Performance tests for database components.

This module provides performance testing for:
- Connection pool performance and scalability
- Query execution performance
- Session management overhead
- Migration performance
- Health check performance
"""

import pytest
import asyncio
import time
import statistics
from typing import List
from concurrent.futures import ThreadPoolExecutor

from app.db.session import (
    session_manager,
    get_db_session,
    test_database_connection,
    DatabaseSessionManager
)
from app.db.performance import (
    performance_analyzer,
    get_performance_report,
    monitor_query_performance
)
from app.core.monitoring import health_checker


class TestConnectionPoolPerformance:
    """Performance tests for database connection pooling."""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_connection_pool_scalability(self):
        """Test connection pool performance under load."""
        try:
            # Test different concurrency levels
            concurrency_levels = [1, 5, 10, 20]
            results = {}
            
            for concurrency in concurrency_levels:
                start_time = time.time()
                
                async def create_session():
                    async with session_manager.get_session() as session:
                        from sqlalchemy import text
                        result = await session.execute(text("SELECT 1"))
                        return result.scalar()
                
                # Run concurrent sessions
                tasks = [create_session() for _ in range(concurrency)]
                await asyncio.gather(*tasks, return_exceptions=True)
                
                total_time = time.time() - start_time
                results[concurrency] = total_time
                
                print(f"Concurrency {concurrency}: {total_time:.3f}s total, {total_time/concurrency:.3f}s per session")
            
            # Verify that performance scales reasonably
            # Higher concurrency should not be dramatically slower per session
            single_session_time = results[1]
            high_concurrency_time = results[20] / 20
            
            # Allow up to 3x overhead for high concurrency
            assert high_concurrency_time < single_session_time * 3
            
            print("✅ Connection pool scalability test passed")
            
        except Exception as e:
            pytest.skip(f"Connection pool performance test failed: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_session_creation_overhead(self):
        """Test session creation and cleanup overhead."""
        try:
            # Measure session creation overhead
            iterations = 50
            times = []
            
            for _ in range(iterations):
                start_time = time.time()
                
                async with session_manager.get_session() as session:
                    from sqlalchemy import text
                    await session.execute(text("SELECT 1"))
                
                elapsed = time.time() - start_time
                times.append(elapsed)
            
            # Calculate statistics
            avg_time = statistics.mean(times)
            median_time = statistics.median(times)
            max_time = max(times)
            min_time = min(times)
            
            print(f"Session creation performance over {iterations} iterations:")
            print(f"  Average: {avg_time:.3f}s")
            print(f"  Median: {median_time:.3f}s")
            print(f"  Min: {min_time:.3f}s")
            print(f"  Max: {max_time:.3f}s")
            
            # Verify performance is reasonable
            assert avg_time < 1.0  # Less than 1 second average
            assert max_time < 5.0  # No session takes more than 5 seconds
            
            print("✅ Session creation overhead test passed")
            
        except Exception as e:
            pytest.skip(f"Session creation overhead test failed: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_connection_pool_exhaustion_recovery(self):
        """Test connection pool behavior under exhaustion."""
        try:
            # Create a session manager with small pool for testing
            test_manager = DatabaseSessionManager()
            
            # Try to exhaust the connection pool
            sessions = []
            max_sessions = 50  # Try to create more sessions than pool size
            
            start_time = time.time()
            
            for i in range(max_sessions):
                try:
                    session_context = test_manager.get_session()
                    session = await session_context.__aenter__()
                    sessions.append((session_context, session))
                    
                    # Execute a simple query
                    from sqlalchemy import text
                    await session.execute(text("SELECT 1"))
                    
                except Exception as e:
                    print(f"Session {i} failed: {e}")
                    break
            
            # Clean up sessions
            for session_context, session in sessions:
                try:
                    await session_context.__aexit__(None, None, None)
                except Exception:
                    pass
            
            total_time = time.time() - start_time
            successful_sessions = len(sessions)
            
            print(f"Created {successful_sessions} sessions in {total_time:.3f}s")
            print(f"Average time per session: {total_time/successful_sessions:.3f}s")
            
            # Verify that at least some sessions were created
            assert successful_sessions > 0
            
            # Verify system recovers after exhaustion
            recovery_test = await test_database_connection()
            assert recovery_test is True
            
            print("✅ Connection pool exhaustion recovery test passed")
            
        except Exception as e:
            pytest.skip(f"Connection pool exhaustion test failed: {e}")


class TestQueryPerformance:
    """Performance tests for query execution."""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_simple_query_performance(self):
        """Test performance of simple queries."""
        try:
            queries = [
                "SELECT 1",
                "SELECT 1, 2, 3",
                "SELECT 'test' as text_value",
                "SELECT CURRENT_TIMESTAMP",
                "SELECT 1 WHERE 1=1",
            ]
            
            results = {}
            
            for query in queries:
                times = []
                iterations = 20
                
                for _ in range(iterations):
                    start_time = time.time()
                    
                    async with session_manager.get_session() as session:
                        from sqlalchemy import text
                        result = await session.execute(text(query))
                        result.fetchall()  # Ensure all data is fetched
                    
                    elapsed = time.time() - start_time
                    times.append(elapsed)
                
                avg_time = statistics.mean(times)
                results[query] = avg_time
                
                print(f"Query '{query}': {avg_time:.3f}s average")
            
            # Verify all queries complete in reasonable time
            for query, avg_time in results.items():
                assert avg_time < 1.0, f"Query '{query}' too slow: {avg_time:.3f}s"
            
            print("✅ Simple query performance test passed")
            
        except Exception as e:
            pytest.skip(f"Query performance test failed: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_complex_query_performance(self):
        """Test performance of more complex queries."""
        try:
            # Create a temporary table with test data
            async with session_manager.get_session() as session:
                from sqlalchemy import text
                
                # Create test table
                await session.execute(text("""
                    CREATE TEMP TABLE perf_test (
                        id INTEGER PRIMARY KEY,
                        name TEXT,
                        value INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                
                # Insert test data
                for i in range(100):
                    await session.execute(text("""
                        INSERT INTO perf_test (id, name, value) 
                        VALUES (:id, :name, :value)
                    """), {"id": i, "name": f"Record {i}", "value": i * 10})
            
            # Test complex queries
            complex_queries = [
                "SELECT COUNT(*) FROM perf_test",
                "SELECT * FROM perf_test WHERE value > 500",
                "SELECT name, COUNT(*) FROM perf_test GROUP BY name HAVING COUNT(*) > 0",
                "SELECT * FROM perf_test ORDER BY value DESC LIMIT 10",
                "SELECT AVG(value), MAX(value), MIN(value) FROM perf_test",
            ]
            
            for query in complex_queries:
                start_time = time.time()
                
                async with session_manager.get_session() as session:
                    from sqlalchemy import text
                    result = await session.execute(text(query))
                    result.fetchall()
                
                elapsed = time.time() - start_time
                print(f"Complex query '{query[:50]}...': {elapsed:.3f}s")
                
                # Complex queries should still complete quickly
                assert elapsed < 2.0, f"Complex query too slow: {elapsed:.3f}s"
            
            print("✅ Complex query performance test passed")
            
        except Exception as e:
            pytest.skip(f"Complex query performance test failed: {e}")


class TestMonitoringPerformance:
    """Performance tests for monitoring and health checks."""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_health_check_performance(self):
        """Test performance of health check operations."""
        try:
            # Test basic health checks
            health_check_times = []
            iterations = 10
            
            for _ in range(iterations):
                start_time = time.time()
                
                # Test database health check
                health_result = await health_checker.check_database()
                
                elapsed = time.time() - start_time
                health_check_times.append(elapsed)
                
                # Verify health check completed
                assert health_result.service == "database"
            
            avg_health_check_time = statistics.mean(health_check_times)
            print(f"Health check average time: {avg_health_check_time:.3f}s")
            
            # Health checks should be fast
            assert avg_health_check_time < 2.0
            
            print("✅ Health check performance test passed")
            
        except Exception as e:
            pytest.skip(f"Health check performance test failed: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_performance_report_generation(self):
        """Test performance of performance report generation."""
        try:
            report_times = []
            iterations = 5
            
            for _ in range(iterations):
                start_time = time.time()
                
                # Generate performance report
                report = await get_performance_report()
                
                elapsed = time.time() - start_time
                report_times.append(elapsed)
                
                # Verify report was generated
                assert report is not None
                assert hasattr(report, 'overall_health')
            
            avg_report_time = statistics.mean(report_times)
            print(f"Performance report generation average time: {avg_report_time:.3f}s")
            
            # Report generation should be reasonably fast
            assert avg_report_time < 5.0
            
            print("✅ Performance report generation test passed")
            
        except Exception as e:
            pytest.skip(f"Performance report generation test failed: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_query_monitoring_overhead(self):
        """Test overhead of query performance monitoring."""
        try:
            # Test queries without monitoring
            unmonitored_times = []
            iterations = 20
            
            for _ in range(iterations):
                start_time = time.time()
                
                async with session_manager.get_session() as session:
                    from sqlalchemy import text
                    result = await session.execute(text("SELECT 1"))
                    result.scalar()
                
                elapsed = time.time() - start_time
                unmonitored_times.append(elapsed)
            
            # Test queries with monitoring
            monitored_times = []
            
            for _ in range(iterations):
                start_time = time.time()
                
                async with monitor_query_performance("test_query") as session:
                    from sqlalchemy import text
                    result = await session.execute(text("SELECT 1"))
                    result.scalar()
                
                elapsed = time.time() - start_time
                monitored_times.append(elapsed)
            
            avg_unmonitored = statistics.mean(unmonitored_times)
            avg_monitored = statistics.mean(monitored_times)
            overhead = avg_monitored - avg_unmonitored
            overhead_percent = (overhead / avg_unmonitored) * 100
            
            print(f"Unmonitored query average: {avg_unmonitored:.3f}s")
            print(f"Monitored query average: {avg_monitored:.3f}s")
            print(f"Monitoring overhead: {overhead:.3f}s ({overhead_percent:.1f}%)")
            
            # Monitoring overhead should be minimal (less than 50% overhead)
            assert overhead_percent < 50
            
            print("✅ Query monitoring overhead test passed")
            
        except Exception as e:
            pytest.skip(f"Query monitoring overhead test failed: {e}")


class TestStressTests:
    """Stress tests for database components."""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    @pytest.mark.stress
    async def test_sustained_load(self):
        """Test database performance under sustained load."""
        try:
            duration = 30  # seconds
            start_time = time.time()
            completed_operations = 0
            errors = 0
            
            async def perform_operation():
                nonlocal completed_operations, errors
                try:
                    async with session_manager.get_session() as session:
                        from sqlalchemy import text
                        result = await session.execute(text("SELECT 1"))
                        result.scalar()
                    completed_operations += 1
                except Exception:
                    errors += 1
            
            # Run operations continuously for the duration
            while time.time() - start_time < duration:
                # Create batch of concurrent operations
                tasks = [perform_operation() for _ in range(5)]
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.1)
            
            total_time = time.time() - start_time
            operations_per_second = completed_operations / total_time
            error_rate = errors / (completed_operations + errors) * 100
            
            print(f"Sustained load test results:")
            print(f"  Duration: {total_time:.1f}s")
            print(f"  Completed operations: {completed_operations}")
            print(f"  Errors: {errors}")
            print(f"  Operations per second: {operations_per_second:.1f}")
            print(f"  Error rate: {error_rate:.1f}%")
            
            # Verify reasonable performance under load
            assert operations_per_second > 1  # At least 1 operation per second
            assert error_rate < 10  # Less than 10% error rate
            
            print("✅ Sustained load test passed")
            
        except Exception as e:
            pytest.skip(f"Sustained load test failed: {e}")


# Performance test configuration
pytestmark = [pytest.mark.performance, pytest.mark.slow]
