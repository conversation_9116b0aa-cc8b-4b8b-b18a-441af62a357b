"""
Document verification API endpoints for Culture Connect Backend API.

This module provides FastAPI endpoints for document verification workflows,
automated validation, manual review processes, and verification analytics.

Implements Task 3.1.2: Document Verification System API endpoints with
comprehensive CRUD operations, workflow management, and admin controls.
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_session
from app.core.permissions import require_permission, get_current_user, get_current_admin_user
from app.core.logging import correlation_id
from app.models.user import User
from app.models.verification import VerificationWorkflowStatus, VerificationPriority
from app.schemas.verification import (
    DocumentVerificationWorkflowCreate, DocumentVerificationWorkflowResponse,
    DocumentVerificationWorkflowUpdate, VerificationHistoryResponse,
    AutomatedValidationRuleCreate, AutomatedValidationRuleResponse,
    AutomatedValidationRuleUpdate, WorkflowAssignmentRequest,
    WorkflowReviewRequest, AutomatedValidationRequest, AutomatedValidationResponse,
    WorkflowListFilters, WorkflowListResponse, VerificationStatistics
)
from app.services.verification_service import DocumentVerificationService
from app.repositories.verification_repository import (
    DocumentVerificationWorkflowRepository, VerificationHistoryRepository,
    AutomatedValidationRuleRepository
)
from app.repositories.vendor_repository import VendorRepository, VendorDocumentRepository
from app.services.email_service import EmailService
from app.services.push_notification_services import PushNotificationService

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Create router with prefix and tags
router = APIRouter(prefix="/verification", tags=["Document Verification"])


# Dependency to get verification service
async def get_verification_service(
    db: AsyncSession = Depends(get_async_session)
) -> DocumentVerificationService:
    """Get verification service with dependencies."""
    workflow_repo = DocumentVerificationWorkflowRepository(db)
    history_repo = VerificationHistoryRepository(db)
    rule_repo = AutomatedValidationRuleRepository(db)
    vendor_repo = VendorRepository(db)
    document_repo = VendorDocumentRepository(db)
    email_service = EmailService(db)
    notification_service = PushNotificationService(db)

    return DocumentVerificationService(
        workflow_repository=workflow_repo,
        history_repository=history_repo,
        rule_repository=rule_repo,
        vendor_repository=vendor_repo,
        document_repository=document_repo,
        email_service=email_service,
        notification_service=notification_service
    )


# Workflow Management Endpoints

@router.post(
    "/workflows",
    response_model=DocumentVerificationWorkflowResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Document Verification Workflow",
    description="Create a new document verification workflow for a vendor document"
)
@require_permission("verification:create")
async def create_verification_workflow(
    document_id: int = Query(..., description="Document ID to verify"),
    priority: VerificationPriority = Query(
        VerificationPriority.NORMAL,
        description="Verification priority level"
    ),
    is_expedited: bool = Query(False, description="Whether this is expedited verification"),
    current_user: User = Depends(get_current_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Create a new document verification workflow.

    **Required Permissions:** `verification:create`

    **Features:**
    - Automatic priority-based SLA assignment
    - Automated validation initiation
    - Audit trail creation
    - Notification sending
    """
    try:
        # Get correlation ID from context
        current_correlation_id = correlation_id.get()

        workflow = await verification_service.create_verification_workflow(
            document_id=document_id,
            priority=priority,
            is_expedited=is_expedited,
            user_id=current_user.id,
            correlation_id=current_correlation_id
        )

        logger.info(
            "Verification workflow created successfully",
            extra={
                "workflow_id": workflow.id,
                "document_id": document_id,
                "user_id": current_user.id,
                "correlation_id": current_correlation_id
            }
        )

        return workflow

    except Exception as e:
        logger.error(
            "Failed to create verification workflow",
            extra={
                "document_id": document_id,
                "user_id": current_user.id,
                "error": str(e),
                "correlation_id": correlation_id.get()
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create verification workflow: {str(e)}"
        )


@router.get(
    "/workflows/{workflow_id}",
    response_model=DocumentVerificationWorkflowResponse,
    summary="Get Verification Workflow",
    description="Get detailed information about a specific verification workflow"
)
@require_permission("verification:read")
async def get_verification_workflow(
    workflow_id: int = Path(..., description="Workflow ID"),
    current_user: User = Depends(get_current_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Get verification workflow by ID.

    **Required Permissions:** `verification:read`
    """
    try:
        workflow = await verification_service.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Verification workflow {workflow_id} not found"
            )

        return DocumentVerificationWorkflowResponse.model_validate(workflow)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get verification workflow",
            extra={
                "workflow_id": workflow_id,
                "user_id": current_user.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get verification workflow: {str(e)}"
        )


@router.get(
    "/workflows",
    response_model=WorkflowListResponse,
    summary="List Verification Workflows",
    description="Get paginated list of verification workflows with advanced filtering"
)
@require_permission("verification:read")
async def list_verification_workflows(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    status_filter: Optional[List[VerificationWorkflowStatus]] = Query(
        None, description="Filter by workflow status"
    ),
    priority_filter: Optional[List[VerificationPriority]] = Query(
        None, description="Filter by priority"
    ),
    vendor_id: Optional[int] = Query(None, description="Filter by vendor ID"),
    assigned_to: Optional[int] = Query(None, description="Filter by assigned admin"),
    is_overdue: Optional[bool] = Query(None, description="Filter overdue workflows"),
    requires_manual_review: Optional[bool] = Query(
        None, description="Filter by manual review requirement"
    ),
    is_expedited: Optional[bool] = Query(None, description="Filter expedited workflows"),
    date_from: Optional[datetime] = Query(None, description="Filter from date"),
    date_to: Optional[datetime] = Query(None, description="Filter to date"),
    current_user: User = Depends(get_current_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    List verification workflows with advanced filtering and pagination.

    **Required Permissions:** `verification:read`

    **Features:**
    - Advanced filtering by status, priority, vendor, admin
    - Date range filtering
    - Overdue workflow detection
    - Pagination support
    """
    try:
        # Check permissions
        await require_permission(current_user, "verification:read")

        # Build filters
        filters = WorkflowListFilters(
            status=status_filter,
            priority=priority_filter,
            vendor_id=vendor_id,
            assigned_to=assigned_to,
            is_overdue=is_overdue,
            requires_manual_review=requires_manual_review,
            is_expedited=is_expedited,
            date_from=date_from,
            date_to=date_to
        )

        result = await verification_service.get_workflows_with_filters(
            filters=filters,
            page=page,
            page_size=page_size,
            correlation_id=correlation_id
        )

        return result

    except Exception as e:
        logger.error(
            "Failed to list verification workflows",
            extra={
                "user_id": current_user.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list verification workflows: {str(e)}"
        )


@router.post(
    "/workflows/{workflow_id}/assign",
    response_model=DocumentVerificationWorkflowResponse,
    summary="Assign Verification Workflow",
    description="Assign a verification workflow to an admin for review"
)
async def assign_verification_workflow(
    workflow_id: int = Path(..., description="Workflow ID"),
    assignment_request: WorkflowAssignmentRequest = ...,
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Assign verification workflow to an admin.

    **Required Permissions:** `verification:assign` (Admin only)

    **Features:**
    - Admin assignment with notes
    - Priority adjustment
    - Automatic status transition
    - Notification sending
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:assign")

        workflow = await verification_service.assign_workflow(
            workflow_id=workflow_id,
            assignment_request=assignment_request,
            assigned_by=current_admin.id,
            correlation_id=correlation_id
        )

        logger.info(
            "Verification workflow assigned successfully",
            extra={
                "workflow_id": workflow_id,
                "assigned_to": assignment_request.assigned_to,
                "assigned_by": current_admin.id,
                "correlation_id": correlation_id
            }
        )

        return workflow

    except Exception as e:
        logger.error(
            "Failed to assign verification workflow",
            extra={
                "workflow_id": workflow_id,
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign verification workflow: {str(e)}"
        )


@router.post(
    "/workflows/{workflow_id}/review",
    response_model=DocumentVerificationWorkflowResponse,
    summary="Review Verification Workflow",
    description="Complete verification workflow review with decision"
)
async def review_verification_workflow(
    workflow_id: int = Path(..., description="Workflow ID"),
    review_request: WorkflowReviewRequest = ...,
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Complete verification workflow review.

    **Required Permissions:** `verification:review` (Admin only)

    **Features:**
    - Approval/rejection decisions
    - Review notes and rejection reasons
    - Document status updates
    - Notification sending
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:review")

        workflow = await verification_service.review_workflow(
            workflow_id=workflow_id,
            review_request=review_request,
            reviewed_by=current_admin.id,
            correlation_id=correlation_id
        )

        logger.info(
            "Verification workflow reviewed successfully",
            extra={
                "workflow_id": workflow_id,
                "decision": review_request.status.value,
                "reviewed_by": current_admin.id,
                "correlation_id": correlation_id
            }
        )

        return workflow

    except Exception as e:
        logger.error(
            "Failed to review verification workflow",
            extra={
                "workflow_id": workflow_id,
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to review verification workflow: {str(e)}"
        )


# Automated Validation Endpoints

@router.post(
    "/validate",
    response_model=AutomatedValidationResponse,
    summary="Run Automated Validation",
    description="Run automated validation on a document"
)
async def run_automated_validation(
    validation_request: AutomatedValidationRequest = ...,
    current_user: User = Depends(get_current_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Run automated validation on a document.

    **Required Permissions:** `verification:validate`

    **Features:**
    - OCR text extraction
    - Format validation
    - Business logic validation
    - Confidence scoring
    """
    try:
        # Check permissions
        await require_permission(current_user, "verification:validate")

        result = await verification_service.run_automated_validation(
            validation_request=validation_request,
            correlation_id=correlation_id
        )

        logger.info(
            "Automated validation completed",
            extra={
                "document_id": validation_request.document_id,
                "overall_status": result.overall_status.value,
                "user_id": current_user.id,
                "correlation_id": correlation_id
            }
        )

        return result

    except Exception as e:
        logger.error(
            "Failed to run automated validation",
            extra={
                "document_id": validation_request.document_id,
                "user_id": current_user.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to run automated validation: {str(e)}"
        )


# Validation Rules Management Endpoints

@router.post(
    "/rules",
    response_model=AutomatedValidationRuleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Validation Rule",
    description="Create a new automated validation rule"
)
async def create_validation_rule(
    rule_data: AutomatedValidationRuleCreate = ...,
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Create a new automated validation rule.

    **Required Permissions:** `verification:rules:create` (Admin only)

    **Features:**
    - Document type targeting
    - Vendor type filtering
    - Configurable validation logic
    - Priority-based execution
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:rules:create")

        rule = await verification_service.rule_repository.create_rule(
            rule_data=rule_data,
            created_by=current_admin.id
        )

        logger.info(
            "Validation rule created successfully",
            extra={
                "rule_id": rule.id,
                "rule_name": rule.rule_name,
                "created_by": current_admin.id,
                "correlation_id": correlation_id
            }
        )

        return AutomatedValidationRuleResponse.model_validate(rule)

    except Exception as e:
        logger.error(
            "Failed to create validation rule",
            extra={
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create validation rule: {str(e)}"
        )


@router.get(
    "/rules",
    response_model=List[AutomatedValidationRuleResponse],
    summary="List Validation Rules",
    description="Get list of automated validation rules"
)
async def list_validation_rules(
    active_only: bool = Query(True, description="Show only active rules"),
    validation_type: Optional[str] = Query(None, description="Filter by validation type"),
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    List automated validation rules.

    **Required Permissions:** `verification:rules:read` (Admin only)
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:rules:read")

        if active_only:
            rules = await verification_service.rule_repository.get_active_rules(
                validation_type=validation_type
            )
        else:
            rules = await verification_service.rule_repository.get_multi()

        return [AutomatedValidationRuleResponse.model_validate(rule) for rule in rules]

    except Exception as e:
        logger.error(
            "Failed to list validation rules",
            extra={
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list validation rules: {str(e)}"
        )


@router.get(
    "/rules/{rule_id}",
    response_model=AutomatedValidationRuleResponse,
    summary="Get Validation Rule",
    description="Get detailed information about a validation rule"
)
async def get_validation_rule(
    rule_id: int = Path(..., description="Rule ID"),
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Get validation rule by ID.

    **Required Permissions:** `verification:rules:read` (Admin only)
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:rules:read")

        rule = await verification_service.rule_repository.get_by_id(rule_id)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Validation rule {rule_id} not found"
            )

        return AutomatedValidationRuleResponse.model_validate(rule)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get validation rule",
            extra={
                "rule_id": rule_id,
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get validation rule: {str(e)}"
        )


@router.put(
    "/rules/{rule_id}",
    response_model=AutomatedValidationRuleResponse,
    summary="Update Validation Rule",
    description="Update an existing validation rule"
)
async def update_validation_rule(
    rule_id: int = Path(..., description="Rule ID"),
    rule_update: AutomatedValidationRuleUpdate = ...,
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Update validation rule.

    **Required Permissions:** `verification:rules:update` (Admin only)
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:rules:update")

        rule = await verification_service.rule_repository.get_by_id(rule_id)
        if not rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Validation rule {rule_id} not found"
            )

        updated_rule = await verification_service.rule_repository.update(
            rule, rule_update.model_dump(exclude_unset=True)
        )

        logger.info(
            "Validation rule updated successfully",
            extra={
                "rule_id": rule_id,
                "updated_by": current_admin.id,
                "correlation_id": correlation_id
            }
        )

        return AutomatedValidationRuleResponse.model_validate(updated_rule)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update validation rule",
            extra={
                "rule_id": rule_id,
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update validation rule: {str(e)}"
        )


# Analytics and Statistics Endpoints

@router.get(
    "/statistics",
    response_model=VerificationStatistics,
    summary="Get Verification Statistics",
    description="Get comprehensive verification statistics and analytics"
)
async def get_verification_statistics(
    date_from: Optional[datetime] = Query(None, description="Statistics from date"),
    date_to: Optional[datetime] = Query(None, description="Statistics to date"),
    current_admin: User = Depends(get_current_admin_user),
    verification_service: DocumentVerificationService = Depends(get_verification_service)
):
    """
    Get verification statistics and analytics.

    **Required Permissions:** `verification:statistics:read` (Admin only)

    **Features:**
    - Workflow status distribution
    - Processing time analytics
    - Approval/rejection rates
    - Automation effectiveness metrics
    """
    try:
        # Check permissions
        await require_permission(current_admin, "verification:statistics:read")

        statistics = await verification_service.get_verification_statistics(
            date_from=date_from,
            date_to=date_to,
            correlation_id=correlation_id
        )

        return statistics

    except Exception as e:
        logger.error(
            "Failed to get verification statistics",
            extra={
                "admin_id": current_admin.id,
                "error": str(e),
                "correlation_id": correlation_id
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get verification statistics: {str(e)}"
        )
