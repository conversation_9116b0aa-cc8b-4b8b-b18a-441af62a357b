"""
Unit tests for workflow orchestration service.

Tests for Task 6.2.2 Phase 2 Step 2: Workflow Orchestration Service
- WorkflowOrchestrationService tests
- Workflow execution lifecycle management
- Dependency validation and circuit breaker patterns
- Performance and error handling scenarios
"""

import pytest
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.workflow_orchestration_service import (
    WorkflowOrchestrationService, WorkflowExecutionError
)
from app.models.workflow_models import (
    WorkflowDefinition, WorkflowExecution, WorkflowStep,
    WorkflowStatus, ExecutionStatus, StepStatus
)
from app.schemas.workflow_schemas import WorkflowExecutionCreate
from app.services.base import NotFoundError, ValidationError


class TestWorkflowOrchestrationService:
    """Test cases for WorkflowOrchestrationService."""

    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_task_service(self):
        """Create mock task service."""
        return AsyncMock()

    @pytest.fixture
    def mock_cache_manager(self):
        """Create mock cache manager."""
        return AsyncMock()

    @pytest.fixture
    def workflow_service(self, mock_session, mock_task_service, mock_cache_manager):
        """Create WorkflowOrchestrationService instance."""
        return WorkflowOrchestrationService(
            db=mock_session,
            task_service=mock_task_service,
            cache_manager=mock_cache_manager
        )

    @pytest.fixture
    def sample_workflow_definition(self):
        """Create sample workflow definition."""
        # Use MagicMock to avoid SQLAlchemy model instantiation issues
        mock_workflow = MagicMock()
        mock_workflow.id = uuid4()
        mock_workflow.name = "Test Workflow"
        mock_workflow.description = "Test workflow description"
        mock_workflow.version = "1.0.0"
        mock_workflow.status = WorkflowStatus.ACTIVE
        mock_workflow.configuration = {"test": "config"}
        mock_workflow.tags = ["test"]
        mock_workflow.workflow_metadata = {"meta": "data"}
        mock_workflow.max_execution_time = 3600
        mock_workflow.max_retries = 3
        mock_workflow.retry_delay = 60
        mock_workflow.is_active = True
        mock_workflow.priority = 5
        mock_workflow.created_at = datetime.now(timezone.utc)
        mock_workflow.updated_at = datetime.now(timezone.utc)
        return mock_workflow

    @pytest.fixture
    def sample_workflow_steps(self, sample_workflow_definition):
        """Create sample workflow steps."""
        # Use MagicMock to avoid SQLAlchemy model instantiation issues
        step1 = MagicMock()
        step1.id = uuid4()
        step1.workflow_definition_id = sample_workflow_definition.id
        step1.name = "Step 1"
        step1.step_order = 1
        step1.task_name = "task1"
        step1.task_configuration = {"param": "value1"}
        step1.task_priority = 5
        step1.task_queue = "general"
        step1.timeout_seconds = 300
        step1.max_retries = 3
        step1.retry_delay = 60
        step1.created_at = datetime.now(timezone.utc)
        step1.updated_at = datetime.now(timezone.utc)

        step2 = MagicMock()
        step2.id = uuid4()
        step2.workflow_definition_id = sample_workflow_definition.id
        step2.name = "Step 2"
        step2.step_order = 2
        step2.task_name = "task2"
        step2.task_configuration = {"param": "value2"}
        step2.task_priority = 5
        step2.task_queue = "general"
        step2.timeout_seconds = 300
        step2.max_retries = 3
        step2.retry_delay = 60
        step2.created_at = datetime.now(timezone.utc)
        step2.updated_at = datetime.now(timezone.utc)

        return [step1, step2]

    @pytest.fixture
    def sample_execution_data(self):
        """Create sample execution creation data."""
        return WorkflowExecutionCreate(
            workflow_definition_id=uuid4(),
            execution_context={"context": "test"},
            input_data={"input": "data"},
            triggered_by="manual",
            trigger_data={"trigger": "test"}
        )

    @pytest.mark.asyncio
    async def test_create_workflow_execution_success(
        self, workflow_service, sample_workflow_definition, sample_workflow_steps, sample_execution_data
    ):
        """Test successful workflow execution creation."""
        workflow_id = sample_workflow_definition.id
        user_id = uuid4()

        # Mock workflow definition retrieval
        sample_workflow_definition.steps = sample_workflow_steps
        with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=sample_workflow_definition):
            # Mock session context
            mock_session = AsyncMock()
            with patch.object(workflow_service, 'get_session_context') as mock_context:
                mock_context.return_value.__aenter__.return_value = mock_session

                result = await workflow_service.create_workflow_execution(
                    workflow_id=workflow_id,
                    execution_data=sample_execution_data,
                    user_id=user_id,
                    correlation_id_val="test-correlation-id"
                )

        assert isinstance(result, WorkflowExecution)
        assert result.workflow_definition_id == workflow_id
        assert result.status == ExecutionStatus.PENDING
        assert result.steps_total == len(sample_workflow_steps)

    @pytest.mark.asyncio
    async def test_create_workflow_execution_workflow_not_found(
        self, workflow_service, sample_execution_data
    ):
        """Test workflow execution creation with non-existent workflow."""
        workflow_id = uuid4()

        # Mock workflow not found
        with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=None):
            with pytest.raises(NotFoundError) as exc_info:
                await workflow_service.create_workflow_execution(
                    workflow_id=workflow_id,
                    execution_data=sample_execution_data,
                    correlation_id_val="test-correlation-id"
                )

        assert f"Workflow definition {workflow_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_workflow_execution_inactive_workflow(
        self, workflow_service, sample_workflow_definition, sample_execution_data
    ):
        """Test workflow execution creation with inactive workflow."""
        workflow_id = sample_workflow_definition.id
        sample_workflow_definition.status = WorkflowStatus.PAUSED

        # Mock workflow definition retrieval
        with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=sample_workflow_definition):
            with pytest.raises(ValidationError) as exc_info:
                await workflow_service.create_workflow_execution(
                    workflow_id=workflow_id,
                    execution_data=sample_execution_data,
                    correlation_id_val="test-correlation-id"
                )

        assert f"Workflow {workflow_id} is not active" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_start_workflow_execution_success(
        self, workflow_service, sample_workflow_definition, sample_workflow_steps
    ):
        """Test successful workflow execution start."""
        execution_id = uuid4()
        user_id = uuid4()

        # Create mock execution
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = sample_workflow_definition.id
        mock_execution.status = ExecutionStatus.PENDING
        mock_execution.steps_total = len(sample_workflow_steps)
        mock_execution.created_at = datetime.now(timezone.utc)

        # Mock workflow definition with steps
        sample_workflow_definition.steps = sample_workflow_steps

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=sample_workflow_definition):
                with patch.object(workflow_service.dependency_repo, 'resolve_execution_order', return_value=[step.id for step in sample_workflow_steps]):
                    with patch.object(workflow_service, '_execute_workflow_steps') as mock_execute:
                        result = await workflow_service.start_workflow_execution(
                            execution_id=execution_id,
                            user_id=user_id,
                            correlation_id_val="test-correlation-id"
                        )

        assert result.status == ExecutionStatus.RUNNING
        assert result.started_at is not None
        mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_workflow_execution_not_found(self, workflow_service):
        """Test workflow execution start with non-existent execution."""
        execution_id = uuid4()

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = None
            mock_context.return_value.__aenter__.return_value = mock_session

            with pytest.raises(NotFoundError) as exc_info:
                await workflow_service.start_workflow_execution(
                    execution_id=execution_id,
                    correlation_id_val="test-correlation-id"
                )

        assert f"Workflow execution {execution_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_start_workflow_execution_invalid_status(self, workflow_service):
        """Test workflow execution start with invalid status."""
        execution_id = uuid4()

        # Create mock execution with invalid status
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = uuid4()
        mock_execution.status = ExecutionStatus.COMPLETED
        mock_execution.created_at = datetime.now(timezone.utc)

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            with pytest.raises(ValidationError) as exc_info:
                await workflow_service.start_workflow_execution(
                    execution_id=execution_id,
                    correlation_id_val="test-correlation-id"
                )

        assert f"Execution {execution_id} cannot be started from status" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_pause_workflow_execution_success(self, workflow_service):
        """Test successful workflow execution pause."""
        execution_id = uuid4()
        user_id = uuid4()

        # Create mock execution
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = uuid4()
        mock_execution.status = ExecutionStatus.RUNNING
        mock_execution.created_at = datetime.now(timezone.utc)

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            result = await workflow_service.pause_workflow_execution(
                execution_id=execution_id,
                user_id=user_id,
                correlation_id_val="test-correlation-id"
            )

        assert result.status == ExecutionStatus.PENDING  # Paused state

    @pytest.mark.asyncio
    async def test_cancel_workflow_execution_success(self, workflow_service):
        """Test successful workflow execution cancellation."""
        execution_id = uuid4()
        user_id = uuid4()

        # Create mock execution
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = uuid4()
        mock_execution.status = ExecutionStatus.RUNNING
        mock_execution.started_at = datetime.now(timezone.utc)
        mock_execution.created_at = datetime.now(timezone.utc)

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            result = await workflow_service.cancel_workflow_execution(
                execution_id=execution_id,
                user_id=user_id,
                correlation_id_val="test-correlation-id"
            )

        assert result.status == ExecutionStatus.CANCELLED
        assert result.completed_at is not None
        assert result.duration_seconds is not None

    @pytest.mark.asyncio
    async def test_get_workflow_execution_status_success(self, workflow_service):
        """Test successful workflow execution status retrieval."""
        execution_id = uuid4()

        # Create mock execution
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = uuid4()
        mock_execution.status = ExecutionStatus.RUNNING
        mock_execution.steps_total = 5
        mock_execution.steps_completed = 3
        mock_execution.steps_failed = 0
        mock_execution.started_at = datetime.now(timezone.utc) - timedelta(minutes=10)
        mock_execution.created_at = datetime.now(timezone.utc)

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            result = await workflow_service.get_workflow_execution_status(
                execution_id=execution_id,
                correlation_id_val="test-correlation-id"
            )

        assert result["execution_id"] == execution_id
        assert result["status"] == ExecutionStatus.RUNNING
        assert result["progress_percentage"] == 60.0  # 3/5 * 100
        assert result["steps_total"] == 5
        assert result["steps_completed"] == 3
        assert result["estimated_completion"] is not None

    @pytest.mark.asyncio
    async def test_validate_workflow_dependencies_success(
        self, workflow_service, sample_workflow_definition, sample_workflow_steps
    ):
        """Test successful workflow dependency validation."""
        workflow_id = sample_workflow_definition.id

        # Mock workflow definition with steps
        sample_workflow_definition.steps = sample_workflow_steps
        step_ids = [step.id for step in sample_workflow_steps]

        with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=sample_workflow_definition):
            with patch.object(workflow_service.dependency_repo, 'resolve_execution_order', return_value=step_ids):
                result = await workflow_service.validate_workflow_dependencies(
                    workflow_id=workflow_id,
                    correlation_id_val="test-correlation-id"
                )

        assert result["is_valid"] is True
        assert result["execution_order"] == step_ids
        assert result["total_steps"] == len(sample_workflow_steps)
        assert result["resolvable_steps"] == len(step_ids)
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_validate_workflow_dependencies_circular_dependency(
        self, workflow_service, sample_workflow_definition, sample_workflow_steps
    ):
        """Test workflow dependency validation with circular dependencies."""
        workflow_id = sample_workflow_definition.id

        # Mock workflow definition with steps
        sample_workflow_definition.steps = sample_workflow_steps

        with patch.object(workflow_service.workflow_repo, 'get_workflow_with_steps', return_value=sample_workflow_definition):
            with patch.object(workflow_service.dependency_repo, 'resolve_execution_order', side_effect=Exception("Circular dependency detected")):
                result = await workflow_service.validate_workflow_dependencies(
                    workflow_id=workflow_id,
                    correlation_id_val="test-correlation-id"
                )

        assert result["is_valid"] is False
        assert result["execution_order"] == []
        assert result["total_steps"] == len(sample_workflow_steps)
        assert result["resolvable_steps"] == 0
        assert len(result["errors"]) > 0
        assert "Circular dependency detected" in result["errors"][0]

    @pytest.mark.asyncio
    async def test_get_workflow_execution_metrics_success(self, workflow_service):
        """Test successful workflow execution metrics retrieval."""
        workflow_id = uuid4()

        # Create mock executions
        exec1 = MagicMock()
        exec1.id = uuid4()
        exec1.workflow_definition_id = workflow_id
        exec1.status = ExecutionStatus.COMPLETED
        exec1.duration_seconds = 120.5
        exec1.created_at = datetime.now(timezone.utc)

        exec2 = MagicMock()
        exec2.id = uuid4()
        exec2.workflow_definition_id = workflow_id
        exec2.status = ExecutionStatus.FAILED
        exec2.created_at = datetime.now(timezone.utc)

        exec3 = MagicMock()
        exec3.id = uuid4()
        exec3.workflow_definition_id = workflow_id
        exec3.status = ExecutionStatus.RUNNING
        exec3.created_at = datetime.now(timezone.utc)

        mock_executions = [exec1, exec2, exec3]

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = mock_executions
            mock_session.execute.return_value = mock_result
            mock_context.return_value.__aenter__.return_value = mock_session

            result = await workflow_service.get_workflow_execution_metrics(
                workflow_id=workflow_id,
                correlation_id_val="test-correlation-id"
            )

        assert result["total_executions"] == 3
        assert result["completed_executions"] == 1
        assert result["failed_executions"] == 1
        assert result["running_executions"] == 1
        assert result["success_rate_percentage"] == 33.33  # 1/3 * 100
        assert result["average_duration_seconds"] == 120.5

    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, workflow_service):
        """Test circuit breaker functionality."""
        operation = "test_operation"

        # Initially circuit should be closed
        assert await workflow_service._check_circuit_breaker(operation) is True

        # Record failures to trigger circuit breaker
        for _ in range(workflow_service.circuit_breaker_threshold):
            await workflow_service._record_circuit_breaker_failure(operation)

        # Circuit should now be open
        assert await workflow_service._check_circuit_breaker(operation) is False

        # Record success to reset circuit breaker
        await workflow_service._record_circuit_breaker_success(operation)
        assert await workflow_service._check_circuit_breaker(operation) is True

    @pytest.mark.asyncio
    async def test_execute_single_step_success(self, workflow_service, sample_workflow_steps):
        """Test successful single step execution."""
        execution = MagicMock()
        execution.id = uuid4()
        execution.workflow_definition_id = uuid4()
        execution.status = ExecutionStatus.RUNNING
        execution.created_at = datetime.now(timezone.utc)
        step = sample_workflow_steps[0]
        correlation = "test-correlation-id"

        # Mock Celery task submission
        mock_celery_task = MagicMock()
        mock_celery_task.id = "task-123"

        with patch('app.services.workflow_orchestration_service.celery_app') as mock_celery:
            mock_celery.send_task.return_value = mock_celery_task

            # Should not raise any exception
            await workflow_service._execute_single_step(execution, step, correlation)

            # Verify task was submitted with correct parameters
            mock_celery.send_task.assert_called_once_with(
                step.task_name,
                kwargs=step.task_configuration,
                queue=step.task_queue or "general",
                priority=step.task_priority,
                retry=True,
                retry_policy={
                    'max_retries': step.max_retries,
                    'interval_start': 0,
                    'interval_step': 0.2,
                    'interval_max': step.retry_delay,
                }
            )

    @pytest.mark.asyncio
    async def test_execute_workflow_steps_success(self, workflow_service, sample_workflow_steps):
        """Test successful workflow steps execution."""
        execution = MagicMock()
        execution.id = uuid4()
        execution.workflow_definition_id = uuid4()
        execution.status = ExecutionStatus.RUNNING
        execution.created_at = datetime.now(timezone.utc)
        execution_order = [step.id for step in sample_workflow_steps]
        correlation = "test-correlation-id"

        with patch.object(workflow_service, '_execute_single_step') as mock_execute_step:
            await workflow_service._execute_workflow_steps(
                execution, sample_workflow_steps, execution_order, correlation
            )

            # Verify each step was executed
            assert mock_execute_step.call_count == len(sample_workflow_steps)

    @pytest.mark.asyncio
    async def test_workflow_execution_error_handling(self, workflow_service):
        """Test workflow execution error handling."""
        execution_id = uuid4()

        # Mock session to raise an exception
        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_context.side_effect = Exception("Database error")

            with pytest.raises(WorkflowExecutionError) as exc_info:
                await workflow_service.get_workflow_execution_status(
                    execution_id=execution_id,
                    correlation_id_val="test-correlation-id"
                )

        assert "Failed to get workflow execution status" in str(exc_info.value)
        assert "Database error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_performance_targets_validation(self, workflow_service):
        """Test that service operations meet performance targets."""
        import time

        execution_id = uuid4()

        # Create mock execution
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.workflow_definition_id = uuid4()
        mock_execution.status = ExecutionStatus.RUNNING
        mock_execution.steps_total = 5
        mock_execution.steps_completed = 3
        mock_execution.created_at = datetime.now(timezone.utc)

        with patch.object(workflow_service, 'get_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_session.get.return_value = mock_execution
            mock_context.return_value.__aenter__.return_value = mock_session

            # Test status query performance (<200ms target)
            start_time = time.time()
            await workflow_service.get_workflow_execution_status(
                execution_id=execution_id,
                correlation_id_val="test-correlation-id"
            )
            query_time = (time.time() - start_time) * 1000

            # Should be well under 200ms for mocked operations
            assert query_time < 200, f"Query took {query_time}ms, exceeding 200ms target"
