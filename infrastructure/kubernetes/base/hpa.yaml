# Culture Connect Backend - Horizontal Pod Autoscaler
# Auto-scaling configuration for >10,000 concurrent users

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: culture-connect-api-hpa
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/instance: culture-connect
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: hpa
    app.kubernetes.io/part-of: culture-connect-backend
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "Horizontal Pod Autoscaler for Culture Connect API"
    target-users: ">10,000 concurrent users"
    scaling-response-time: "<2 minutes"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-api
  minReplicas: 3
  maxReplicas: 50
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics for request rate
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  # Custom metrics for response time
  - type: Pods
    pods:
      metric:
        name: http_request_duration_seconds
      target:
        type: AverageValue
        averageValue: "500m"  # 500ms
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60   # 1 minute
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: culture-connect-celery-worker-hpa
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-worker-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-celery-worker
  minReplicas: 2
  maxReplicas: 20
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  # Custom metrics for queue length
  - type: Object
    object:
      metric:
        name: celery_queue_length
      target:
        type: Value
        value: "50"
      describedObject:
        apiVersion: v1
        kind: Service
        name: culture-connect-redis
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes
      policies:
      - type: Percent
        value: 20
        periodSeconds: 120
      - type: Pods
        value: 1
        periodSeconds: 120
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 120  # 2 minutes
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 3
        periodSeconds: 60
      selectPolicy: Max

---
# Vertical Pod Autoscaler for resource optimization
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: culture-connect-api-vpa
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: culture-connect-api
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: api
      minAllowed:
        cpu: 100m
        memory: 256Mi
      maxAllowed:
        cpu: 2
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits
    - containerName: nginx
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 200m
        memory: 256Mi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod Disruption Budget for high availability
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: culture-connect-api-pdb
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: pdb
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: api

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: culture-connect-celery-worker-pdb
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: celery-worker-pdb
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/component: celery-worker

---
# Custom Resource for advanced scaling metrics
apiVersion: v1
kind: ConfigMap
metadata:
  name: culture-connect-scaling-config
  namespace: culture-connect
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/component: scaling-config
data:
  scaling-policy.yaml: |
    # Advanced scaling configuration
    scaling:
      api:
        target_concurrent_users: 10000
        max_response_time_ms: 500
        target_cpu_utilization: 70
        target_memory_utilization: 80
        scale_up_threshold: 85
        scale_down_threshold: 50
        min_replicas: 3
        max_replicas: 50
        scale_up_cooldown: 60s
        scale_down_cooldown: 300s
      
      celery_worker:
        target_queue_length: 50
        max_task_processing_time_ms: 5000
        target_cpu_utilization: 75
        target_memory_utilization: 85
        min_replicas: 2
        max_replicas: 20
        scale_up_cooldown: 120s
        scale_down_cooldown: 600s
      
      performance_targets:
        api_response_time_p95: 500ms
        api_response_time_p99: 1000ms
        database_connection_pool_utilization: 80%
        redis_memory_utilization: 85%
        error_rate_threshold: 1%
        
      alerts:
        high_cpu_utilization: 85%
        high_memory_utilization: 90%
        high_response_time: 1000ms
        high_error_rate: 5%
        queue_backlog: 100
        
  prometheus-rules.yaml: |
    groups:
    - name: culture-connect-scaling
      rules:
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API response time detected"
          description: "95th percentile response time is {{ $value }}s"
      
      - alert: HighCPUUtilization
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 85
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High CPU utilization detected"
          description: "CPU utilization is {{ $value }}%"
      
      - alert: HighMemoryUtilization
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 90
        for: 5m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "High memory utilization detected"
          description: "Memory utilization is {{ $value }}%"
      
      - alert: CeleryQueueBacklog
        expr: celery_queue_length > 100
        for: 3m
        labels:
          severity: warning
          component: celery
        annotations:
          summary: "Celery queue backlog detected"
          description: "Queue length is {{ $value }} tasks"
