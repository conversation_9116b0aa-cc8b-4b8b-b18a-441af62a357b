// Culture Connect Backend - Artillery Load Test Functions
// Supporting functions for comprehensive load testing

const crypto = require('crypto');
const faker = require('faker');

// Generate random test data
function generateTestData(context, events, done) {
  // Generate random user data
  context.vars.random_email = faker.internet.email();
  context.vars.random_password = faker.internet.password(12);
  context.vars.random_name = faker.name.findName();
  context.vars.random_phone = faker.phone.phoneNumber();
  
  // Generate random booking data
  context.vars.random_event_type = faker.random.arrayElement([
    'wedding', 'birthday', 'corporate', 'anniversary', 'graduation'
  ]);
  context.vars.random_location = faker.random.arrayElement([
    'Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan'
  ]);
  context.vars.random_guest_count = faker.random.number({ min: 50, max: 500 });
  context.vars.random_budget = faker.random.number({ min: 100000, max: 2000000 });
  
  // Generate random service types
  context.vars.random_service_type = faker.random.arrayElement([
    'catering', 'photography', 'decoration', 'music', 'venue'
  ]);
  
  // Generate random payment data
  context.vars.random_amount = faker.random.number({ min: 10000, max: 500000 });
  context.vars.random_reference = `load_test_${crypto.randomBytes(8).toString('hex')}`;
  
  // Generate timestamps
  context.vars.current_timestamp = new Date().toISOString();
  context.vars.future_date = faker.date.future().toISOString().split('T')[0];
  
  return done();
}

// Validate response times
function validateResponseTime(requestParams, response, context, ee, next) {
  const responseTime = response.timings.response;
  
  // Log slow responses
  if (responseTime > 1000) {
    console.log(`Slow response detected: ${requestParams.url} - ${responseTime}ms`);
  }
  
  // Track response times by endpoint
  if (!context.vars.response_times) {
    context.vars.response_times = {};
  }
  
  const endpoint = requestParams.url.split('?')[0];
  if (!context.vars.response_times[endpoint]) {
    context.vars.response_times[endpoint] = [];
  }
  
  context.vars.response_times[endpoint].push(responseTime);
  
  return next();
}

// Custom authentication flow
function authenticateUser(context, events, done) {
  const request = require('request');
  
  const authData = {
    email: context.vars.test_user_email || '<EMAIL>',
    password: context.vars.test_user_password || 'LoadTest123!'
  };
  
  request.post({
    url: `${context.vars.target}/api/v1/auth/login`,
    json: authData,
    timeout: 30000
  }, (error, response, body) => {
    if (error) {
      console.error('Authentication error:', error);
      return done(error);
    }
    
    if (response.statusCode === 200 && body.access_token) {
      context.vars.auth_token = body.access_token;
      context.vars.user_id = body.user.id;
    } else {
      console.error('Authentication failed:', response.statusCode, body);
    }
    
    return done();
  });
}

// Simulate realistic user behavior
function simulateUserBehavior(context, events, done) {
  // Simulate reading time
  const readingTime = faker.random.number({ min: 1000, max: 5000 });
  context.vars.think_time = readingTime / 1000;
  
  // Simulate user preferences
  context.vars.user_preferences = {
    location: faker.random.arrayElement(['Lagos', 'Abuja', 'Port Harcourt']),
    budget_range: faker.random.arrayElement(['low', 'medium', 'high']),
    service_priority: faker.random.arrayElement(['price', 'quality', 'availability'])
  };
  
  return done();
}

// Generate realistic search queries
function generateSearchQuery(context, events, done) {
  const searchTerms = [
    'wedding catering Lagos',
    'birthday photography Abuja',
    'corporate event venue',
    'anniversary decoration',
    'graduation party music',
    'traditional wedding',
    'modern catering services',
    'professional photography',
    'event planning services',
    'luxury venue rental'
  ];
  
  context.vars.search_query = faker.random.arrayElement(searchTerms);
  context.vars.search_location = faker.random.arrayElement([
    'Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan', 'Kaduna'
  ]);
  
  return done();
}

// Validate booking creation
function validateBooking(requestParams, response, context, ee, next) {
  if (response.statusCode === 201 && response.body) {
    try {
      const booking = JSON.parse(response.body);
      if (booking.booking && booking.booking.id) {
        context.vars.created_booking_id = booking.booking.id;
        console.log(`Booking created successfully: ${booking.booking.id}`);
      }
    } catch (error) {
      console.error('Error parsing booking response:', error);
    }
  }
  
  return next();
}

// Validate payment initialization
function validatePayment(requestParams, response, context, ee, next) {
  if (response.statusCode === 200 && response.body) {
    try {
      const payment = JSON.parse(response.body);
      if (payment.payment_url) {
        context.vars.payment_url = payment.payment_url;
        context.vars.payment_reference = payment.reference;
        console.log(`Payment initialized: ${payment.reference}`);
      }
    } catch (error) {
      console.error('Error parsing payment response:', error);
    }
  }
  
  return next();
}

// Monitor system metrics
function monitorMetrics(context, events, done) {
  const metrics = {
    timestamp: new Date().toISOString(),
    virtual_users: context.vars.vusers || 0,
    requests_per_second: context.vars.rps || 0,
    response_time_p95: context.vars.response_time_p95 || 0,
    error_rate: context.vars.error_rate || 0
  };
  
  // Log metrics for external monitoring
  console.log('Load Test Metrics:', JSON.stringify(metrics));
  
  return done();
}

// Cleanup function
function cleanup(context, events, done) {
  // Clean up any created test data
  if (context.vars.created_booking_id) {
    // Note: In a real scenario, you might want to clean up test bookings
    console.log(`Test booking created: ${context.vars.created_booking_id}`);
  }
  
  if (context.vars.auth_token) {
    // Logout or invalidate token if needed
    console.log('Cleaning up authentication token');
  }
  
  return done();
}

// Error handling
function handleError(requestParams, response, context, ee, next) {
  if (response.statusCode >= 400) {
    console.error(`Error ${response.statusCode} for ${requestParams.url}:`, response.body);
    
    // Track error types
    if (!context.vars.error_counts) {
      context.vars.error_counts = {};
    }
    
    const errorType = `${response.statusCode}`;
    context.vars.error_counts[errorType] = (context.vars.error_counts[errorType] || 0) + 1;
  }
  
  return next();
}

// Performance monitoring
function trackPerformance(requestParams, response, context, ee, next) {
  const responseTime = response.timings.response;
  const endpoint = requestParams.url.split('?')[0];
  
  // Emit custom metrics
  ee.emit('customStat', {
    stat: `response_time_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
    value: responseTime
  });
  
  // Track SLA violations
  if (responseTime > 500) {
    ee.emit('customStat', {
      stat: 'sla_violations',
      value: 1
    });
  }
  
  return next();
}

// WebSocket message handler
function handleWebSocketMessage(message, context, events, done) {
  try {
    const data = JSON.parse(message);
    console.log('WebSocket message received:', data.type);
    
    // Track WebSocket metrics
    if (!context.vars.websocket_messages) {
      context.vars.websocket_messages = 0;
    }
    context.vars.websocket_messages++;
    
  } catch (error) {
    console.error('Error parsing WebSocket message:', error);
  }
  
  return done();
}

// Export functions
module.exports = {
  generateTestData,
  validateResponseTime,
  authenticateUser,
  simulateUserBehavior,
  generateSearchQuery,
  validateBooking,
  validatePayment,
  monitorMetrics,
  cleanup,
  handleError,
  trackPerformance,
  handleWebSocketMessage
};
