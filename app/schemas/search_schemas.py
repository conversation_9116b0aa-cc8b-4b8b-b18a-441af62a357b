"""
Search Schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic V2 schemas for advanced search operations including:
- Search request schemas with validation for multiple criteria and filters
- Search response schemas with structured results and aggregations
- Geospatial search schemas with location-based filtering
- Search result schemas with comprehensive service information
- Aggregation schemas for faceted search and filtering

Implements Task 3.3 requirements for advanced search schemas with
Pydantic V2 validation, comprehensive filtering, and structured responses.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, ConfigDict, validator, field_validator
from enum import Enum

from app.schemas.base import BaseSchema
from app.schemas.service_extended import ServiceResponse
from app.schemas.vendor import VendorResponse


class SortOption(str, Enum):
    """Sort options for search results."""
    RELEVANCE = "relevance"
    PRICE_LOW = "price_low"
    PRICE_HIGH = "price_high"
    RATING = "rating"
    NEWEST = "newest"
    DISTANCE = "distance"


class LocationFilter(BaseModel):
    """Location-based search filter."""
    model_config = ConfigDict(from_attributes=True)
    
    latitude: float = Field(..., ge=-90, le=90, description="Latitude coordinate")
    longitude: float = Field(..., ge=-180, le=180, description="Longitude coordinate")
    radius_km: float = Field(default=10.0, ge=0.1, le=100.0, description="Search radius in kilometers")


class PriceRangeFilter(BaseModel):
    """Price range filter for search."""
    model_config = ConfigDict(from_attributes=True)
    
    min_price: Optional[float] = Field(None, ge=0, description="Minimum price filter")
    max_price: Optional[float] = Field(None, ge=0, description="Maximum price filter")
    currency: str = Field(default="NGN", description="Currency code")
    
    @field_validator('max_price')
    @classmethod
    def validate_price_range(cls, v, info):
        """Validate that max_price is greater than min_price."""
        if v is not None and info.data.get('min_price') is not None:
            if v <= info.data['min_price']:
                raise ValueError('max_price must be greater than min_price')
        return v


class AdvancedSearchRequest(BaseModel):
    """Advanced search request schema with comprehensive filtering."""
    model_config = ConfigDict(from_attributes=True)
    
    # Text search
    query: Optional[str] = Field(None, max_length=500, description="Search query text")
    
    # Location-based search
    location: Optional[LocationFilter] = Field(None, description="Location-based search filter")
    
    # Category and service filters
    category_ids: Optional[List[int]] = Field(None, description="List of category IDs to filter by")
    tags: Optional[List[str]] = Field(None, max_items=10, description="List of tags to filter by")
    
    # Price and rating filters
    price_range: Optional[PriceRangeFilter] = Field(None, description="Price range filter")
    min_rating: Optional[float] = Field(None, ge=0, le=5, description="Minimum rating filter")
    
    # Availability filter
    availability_date: Optional[date] = Field(None, description="Filter by availability on specific date")
    
    # Vendor filters
    verified_vendors_only: bool = Field(default=False, description="Show only verified vendors")
    
    # Sorting and pagination
    sort_by: SortOption = Field(default=SortOption.RELEVANCE, description="Sort criteria")
    page: int = Field(default=1, ge=1, description="Page number for pagination")
    per_page: int = Field(default=20, ge=1, le=100, description="Results per page")
    
    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Validate tags list."""
        if v is not None:
            # Remove empty strings and duplicates
            v = list(set([tag.strip() for tag in v if tag.strip()]))
        return v


class SearchResultLocation(BaseModel):
    """Location information in search results."""
    model_config = ConfigDict(from_attributes=True)
    
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    address: Optional[str] = Field(None, description="Human-readable address")
    distance_km: Optional[float] = Field(None, description="Distance from search center in kilometers")


class SearchResultPricing(BaseModel):
    """Pricing information in search results."""
    model_config = ConfigDict(from_attributes=True)
    
    min_price: float = Field(..., description="Minimum price")
    max_price: float = Field(..., description="Maximum price")
    currency: str = Field(..., description="Currency code")
    price_display: str = Field(..., description="Formatted price display")


class SearchResultService(BaseModel):
    """Service information in search results."""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="Service ID")
    title: str = Field(..., description="Service title")
    description: str = Field(..., description="Service description")
    short_description: Optional[str] = Field(None, description="Short service description")
    
    # Category information
    category: Dict[str, Any] = Field(..., description="Category information")
    
    # Vendor information
    vendor: VendorResponse = Field(..., description="Vendor information")
    
    # Location and pricing
    location: Optional[SearchResultLocation] = Field(None, description="Location information")
    pricing: SearchResultPricing = Field(..., description="Pricing information")
    
    # Performance metrics
    average_rating: float = Field(..., description="Average rating")
    total_reviews: int = Field(..., description="Total number of reviews")
    
    # Service details
    duration_minutes: Optional[int] = Field(None, description="Service duration in minutes")
    max_participants: Optional[int] = Field(None, description="Maximum participants")
    tags: List[str] = Field(default_factory=list, description="Service tags")
    
    # Availability
    has_availability: bool = Field(default=True, description="Has available slots")
    next_available_date: Optional[date] = Field(None, description="Next available date")
    
    # Search relevance
    relevance_score: Optional[float] = Field(None, description="Search relevance score")
    boost_score: Optional[float] = Field(None, description="Boost score for ranking")
    
    # Media
    featured_image_url: Optional[str] = Field(None, description="Featured image URL")
    image_count: int = Field(default=0, description="Total number of images")


class AggregationBucket(BaseModel):
    """Aggregation bucket for faceted search."""
    model_config = ConfigDict(from_attributes=True)
    
    key: Union[str, int] = Field(..., description="Bucket key")
    doc_count: int = Field(..., description="Document count in bucket")
    name: Optional[str] = Field(None, description="Human-readable name")


class RangeAggregationBucket(BaseModel):
    """Range aggregation bucket for numeric ranges."""
    model_config = ConfigDict(from_attributes=True)
    
    key: str = Field(..., description="Range key")
    from_value: Optional[float] = Field(None, alias="from", description="Range start value")
    to_value: Optional[float] = Field(None, alias="to", description="Range end value")
    doc_count: int = Field(..., description="Document count in range")


class SearchAggregations(BaseModel):
    """Search aggregations for faceted search."""
    model_config = ConfigDict(from_attributes=True)
    
    categories: Dict[str, List[AggregationBucket]] = Field(
        default_factory=dict, 
        description="Category aggregations"
    )
    price_ranges: Dict[str, List[RangeAggregationBucket]] = Field(
        default_factory=dict, 
        description="Price range aggregations"
    )
    ratings: Dict[str, List[RangeAggregationBucket]] = Field(
        default_factory=dict, 
        description="Rating aggregations"
    )
    locations: Dict[str, List[AggregationBucket]] = Field(
        default_factory=dict, 
        description="Location aggregations"
    )


class SearchMetadata(BaseModel):
    """Search metadata and performance information."""
    model_config = ConfigDict(from_attributes=True)
    
    total_results: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Results per page")
    total_pages: int = Field(..., description="Total number of pages")
    search_time_ms: Optional[float] = Field(None, description="Search execution time in milliseconds")
    has_more: bool = Field(..., description="Whether there are more results")
    
    # Search context
    query: Optional[str] = Field(None, description="Original search query")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    sort_by: str = Field(..., description="Sort criteria used")


class AdvancedSearchResponse(BaseModel):
    """Advanced search response with results and aggregations."""
    model_config = ConfigDict(from_attributes=True)
    
    results: List[SearchResultService] = Field(..., description="Search results")
    metadata: SearchMetadata = Field(..., description="Search metadata")
    aggregations: SearchAggregations = Field(..., description="Search aggregations")
    
    # Search suggestions
    suggestions: List[str] = Field(default_factory=list, description="Search suggestions")
    did_you_mean: Optional[str] = Field(None, description="Did you mean suggestion")


class LocationSearchRequest(BaseModel):
    """Location-based search request."""
    model_config = ConfigDict(from_attributes=True)
    
    latitude: float = Field(..., ge=-90, le=90, description="Search center latitude")
    longitude: float = Field(..., ge=-180, le=180, description="Search center longitude")
    radius_km: float = Field(default=10.0, ge=0.1, le=100.0, description="Search radius in kilometers")
    category_id: Optional[int] = Field(None, description="Category filter")
    limit: int = Field(default=50, ge=1, le=100, description="Maximum results")


class LocationSearchResponse(BaseModel):
    """Location-based search response."""
    model_config = ConfigDict(from_attributes=True)
    
    results: List[SearchResultService] = Field(..., description="Location search results")
    center: LocationFilter = Field(..., description="Search center coordinates")
    total_found: int = Field(..., description="Total services found")


class SearchSuggestionRequest(BaseModel):
    """Search suggestion request."""
    model_config = ConfigDict(from_attributes=True)
    
    query: str = Field(..., min_length=1, max_length=100, description="Partial search query")
    limit: int = Field(default=10, ge=1, le=20, description="Maximum suggestions")


class SearchSuggestionResponse(BaseModel):
    """Search suggestion response."""
    model_config = ConfigDict(from_attributes=True)
    
    suggestions: List[str] = Field(..., description="Search suggestions")
    query: str = Field(..., description="Original query")


class PopularSearchesResponse(BaseModel):
    """Popular searches response."""
    model_config = ConfigDict(from_attributes=True)
    
    popular_queries: List[str] = Field(..., description="Popular search queries")
    trending_categories: List[Dict[str, Any]] = Field(..., description="Trending categories")
    featured_services: List[SearchResultService] = Field(..., description="Featured services")
