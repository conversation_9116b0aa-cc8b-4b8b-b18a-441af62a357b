# Culture Connect Backend - Development Environment Configuration
# Development-specific configuration for local development and testing
#
# This file contains development-specific configuration values optimized for
# local development, debugging, and rapid iteration.
#
# Security Notice:
# - This file contains development configuration values
# - Safe to use default values for local development
# - Never use these values in staging or production environments

# =============================================================================
# ENVIRONMENT IDENTIFICATION
# =============================================================================

# Environment Configuration
ENVIRONMENT=development
CC_ENVIRONMENT=development
CC_VERSION=dev-latest
CC_BUILD_ID=dev-build-001

# Application Configuration
CC_DEBUG=true
CC_LOG_LEVEL=DEBUG
CC_WORKERS=1
CC_MAX_CONNECTIONS=100
CC_KEEPALIVE_TIMEOUT=65
CC_GRACEFUL_TIMEOUT=30

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Configuration
CC_DATABASE_URL=postgresql+asyncpg://culture_connect:password@localhost:5432/culture_connect_dev_db
CC_POSTGRES_DB=culture_connect_dev_db
CC_POSTGRES_USER=culture_connect
CC_POSTGRES_PASSWORD=password
CC_POSTGRES_PORT=5432

# Database Performance (Development)
CC_DATABASE_POOL_SIZE=5
CC_DATABASE_MAX_OVERFLOW=10
CC_DATABASE_POOL_TIMEOUT=30
CC_DATABASE_POOL_RECYCLE=3600

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Configuration
CC_REDIS_URL=redis://localhost:6379/0
CC_REDIS_PASSWORD=
CC_REDIS_PORT=6379
CC_REDIS_DB=0

# Redis Performance (Development)
CC_REDIS_MAX_CONNECTIONS=10
CC_REDIS_RETRY_ON_TIMEOUT=true
CC_REDIS_SOCKET_KEEPALIVE=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
CC_SECRET_KEY=your-secret-key-here-development-only
CC_JWT_SECRET_KEY=your-jwt-secret-key-here-development-only
CC_JWT_ALGORITHM=HS256
CC_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
CC_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth Configuration (Development)
CC_GOOGLE_CLIENT_ID=dev_google_client_id_here
CC_GOOGLE_CLIENT_SECRET=dev_google_client_secret_here
CC_FACEBOOK_APP_ID=dev_facebook_app_id_here
CC_FACEBOOK_APP_SECRET=dev_facebook_app_secret_here

# =============================================================================
# PAYMENT PROVIDER CONFIGURATION (DEVELOPMENT)
# =============================================================================

# Paystack Configuration (Test Mode)
CC_PAYSTACK_SECRET_KEY=sk_test_dev_paystack_secret_key_here
CC_PAYSTACK_PUBLIC_KEY=pk_test_dev_paystack_public_key_here
CC_PAYSTACK_WEBHOOK_SECRET=dev_paystack_webhook_secret_here
CC_PAYSTACK_BASE_URL=https://api.paystack.co

# Stripe Configuration (Test Mode)
CC_STRIPE_SECRET_KEY=sk_test_dev_stripe_secret_key_here
CC_STRIPE_PUBLIC_KEY=pk_test_dev_stripe_public_key_here
CC_STRIPE_WEBHOOK_SECRET=whsec_dev_stripe_webhook_secret_here
CC_STRIPE_API_VERSION=2023-10-16

# Busha Cryptocurrency Configuration (Development)
CC_BUSHA_API_KEY=dev_busha_api_key_here
CC_BUSHA_SECRET_KEY=dev_busha_secret_key_here
CC_BUSHA_BASE_URL=https://sandbox-api.busha.co
CC_BUSHA_WEBHOOK_SECRET=dev_busha_webhook_secret_here

# =============================================================================
# AI/ML INTEGRATION CONFIGURATION (DEVELOPMENT)
# =============================================================================

# AIML API Configuration
CC_AIML_API_KEY=dev_aiml_api_key_here
CC_AIML_BASE_URL=https://api.aimlapi.com
CC_AIML_MODEL=gpt-4
CC_AIML_MAX_TOKENS=1000
CC_AIML_TEMPERATURE=0.7

# =============================================================================
# GEOLOCATION CONFIGURATION (DEVELOPMENT)
# =============================================================================

# MaxMind GeoIP Configuration
CC_GEOIP_DATABASE_PATH=/app/data/GeoLite2-Country.mmdb
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_GEOLOCATION_FALLBACK_COUNTRY=NG
MAXMIND_LICENSE_KEY=dev_maxmind_license_key_here

# Geolocation Performance (Development)
CC_GEOLOCATION_TIMEOUT_MS=5000
CC_GEOLOCATION_MAX_RETRIES=3
CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD=10
CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT=60

# Geolocation Caching (Development)
CC_GEOLOCATION_CACHE_TTL_HOURS=1
CC_GEOLOCATION_CACHE_MAX_SIZE=1000
CC_GEOLOCATION_CACHE_COMPRESSION=false
CC_GEOLOCATION_REDIS_PREFIX=geo:dev:
CC_GEOLOCATION_REDIS_TTL=3600  # 1 hour

# =============================================================================
# EXTERNAL SERVICE CONFIGURATION
# =============================================================================

# Frontend URLs (Development)
CC_FRONTEND_URL=http://localhost:3000
CC_PWA_URL=http://localhost:3001
CC_ADMIN_URL=http://localhost:3002

# API Configuration
CC_API_BASE_URL=http://localhost:8000
CC_API_VERSION=v1
CC_API_TITLE=Culture Connect Backend API (Development)
CC_API_DESCRIPTION=Culture Connect Backend API - Development Environment

# =============================================================================
# MONITORING AND LOGGING (DEVELOPMENT)
# =============================================================================

# Sentry Configuration (Development)
CC_SENTRY_DSN=
CC_SENTRY_ENVIRONMENT=development
CC_SENTRY_TRACES_SAMPLE_RATE=1.0
CC_SENTRY_PROFILES_SAMPLE_RATE=1.0

# Logging Configuration
CC_LOG_FORMAT=json
CC_LOG_DATE_FORMAT=%Y-%m-%d %H:%M:%S
CC_LOG_FILE_PATH=/app/logs/culture_connect_dev.log
CC_LOG_MAX_BYTES=10485760  # 10MB
CC_LOG_BACKUP_COUNT=3

# =============================================================================
# EMAIL CONFIGURATION (DEVELOPMENT)
# =============================================================================

# SMTP Configuration (Development)
CC_SMTP_HOST=localhost
CC_SMTP_PORT=1025
CC_SMTP_USERNAME=
CC_SMTP_PASSWORD=
CC_SMTP_USE_TLS=false
CC_SMTP_USE_SSL=false

# Email Settings
CC_EMAIL_FROM=noreply@localhost
CC_EMAIL_FROM_NAME=Culture Connect (Development)
CC_EMAIL_TEMPLATE_DIR=/app/templates/email

# =============================================================================
# CELERY CONFIGURATION (DEVELOPMENT)
# =============================================================================

# Celery Broker Configuration
CC_CELERY_BROKER_URL=redis://localhost:6379/1
CC_CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Celery Worker Configuration
CELERY_WORKER_CONCURRENCY=1
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_WORKER_MAX_TASKS_PER_CHILD=100
CELERY_WORKER_DISABLE_RATE_LIMITS=true

# Celery Task Configuration
CC_CELERY_TASK_SERIALIZER=json
CC_CELERY_RESULT_SERIALIZER=json
CC_CELERY_ACCEPT_CONTENT=["json"]
CC_CELERY_TIMEZONE=UTC
CC_CELERY_ENABLE_UTC=true

# =============================================================================
# FEATURE FLAGS (DEVELOPMENT)
# =============================================================================

# Feature Toggles
CC_ENABLE_PROMOTIONAL_SYSTEM=true
CC_ENABLE_CRYPTO_PAYMENTS=true
CC_ENABLE_AI_OPTIMIZATION=true
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_ENABLE_WEBSOCKET_COMMUNICATION=true
CC_ENABLE_BACKGROUND_JOBS=true
CC_ENABLE_ANALYTICS_SYSTEM=true
CC_ENABLE_PERFORMANCE_MONITORING=true

# Development Features
CC_ENABLE_DEBUG_TOOLBAR=true
CC_ENABLE_PROFILING=true
CC_ENABLE_HOT_RELOAD=true

# =============================================================================
# DEPLOYMENT CONFIGURATION (DEVELOPMENT)
# =============================================================================

# Container Configuration
CC_DATA_PATH=./data
CC_LOG_PATH=./logs
CC_BACKUP_PATH=./backups
CC_UPLOAD_PATH=./uploads

# Resource Limits (Development)
CC_MAX_MEMORY_MB=256
CC_MAX_CPU_PERCENT=25
CC_MAX_CONNECTIONS_TOTAL=100

# Health Check Configuration
CC_HEALTH_CHECK_INTERVAL=30
CC_HEALTH_CHECK_TIMEOUT=10
CC_HEALTH_CHECK_RETRIES=3

# =============================================================================
# TESTING CONFIGURATION (DEVELOPMENT)
# =============================================================================

# Test Database
CC_TEST_DATABASE_URL=postgresql+asyncpg://culture_connect_test:test_password@localhost:5432/culture_connect_test_db

# Test Configuration
CC_TESTING_MODE=true
CC_TEST_EMAIL_BACKEND=console
CC_TEST_PAYMENT_MODE=true
CC_TEST_AI_RESPONSES=true
CC_MOCK_EXTERNAL_SERVICES=true

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Development Server
CC_RELOAD=true
CC_ACCESS_LOG=true
CC_USE_COLORS=true

# Development Database
CC_ECHO_SQL=true
CC_SHOW_SQL_QUERIES=true
