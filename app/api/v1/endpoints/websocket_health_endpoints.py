"""
WebSocket Health Check Endpoints for Culture Connect Backend API.

This module provides comprehensive health check and monitoring endpoints for production deployment including:
- Production health checks with detailed system status
- Performance metrics and monitoring endpoints
- Connection pool status and optimization recommendations
- Message queue health and throughput monitoring

Implements Task 6.1.1 Phase 8 requirements for production readiness with:
- Comprehensive health checks for all WebSocket infrastructure components
- Real-time performance metrics and monitoring endpoints
- Production optimization status and recommendations
- Integration with existing monitoring infrastructure

Performance targets: <100ms health check response, comprehensive system status, production monitoring
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.deps import get_db
from app.core.cache import get_cache_manager, CacheManager

from app.services.websocket_production_optimization_service import (
    get_production_optimization_service,
    WebSocketProductionOptimizationService,
    OptimizationMode
)
from app.schemas.base_schemas import BaseResponse
from app.core.monitoring import health_checker

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/websocket/health", tags=["WebSocket Health"])


@router.get("/status", response_model=Dict[str, Any])
async def get_websocket_health_status(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Get comprehensive WebSocket infrastructure health status.

    Returns detailed health information for all WebSocket components including
    connection pool, message queue, Redis connectivity, and overall system health.

    Performance target: <100ms response time.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Perform comprehensive health check
        health_results = await optimization_service.perform_health_check()

        return JSONResponse(
            status_code=status.HTTP_200_OK if health_results["overall_status"] == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_results
        )

    except Exception as e:
        logger.error(f"Health status check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_status": "critical",
                "error": str(e)
            }
        )


@router.get("/metrics", response_model=Dict[str, Any])
async def get_websocket_metrics(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Get real-time WebSocket performance metrics.

    Returns comprehensive performance metrics including connection statistics,
    message throughput, latency measurements, and resource utilization.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Get production status with metrics
        production_status = await optimization_service.get_production_status()

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": production_status["performance_metrics"],
            "connection_pool_metrics": production_status["connection_pool"]["metrics"],
            "message_queue_metrics": production_status["message_queue"]["processing_stats"],
            "health_status": production_status["health_status"]
        }

    except Exception as e:
        logger.error(f"Failed to get WebSocket metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve metrics: {str(e)}"
        )


@router.get("/performance-report", response_model=Dict[str, Any])
async def get_performance_report(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Generate comprehensive WebSocket performance report.

    Returns detailed performance analysis with optimization recommendations
    and system utilization statistics.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Generate performance report
        performance_report = await optimization_service.get_performance_report()

        return performance_report

    except Exception as e:
        logger.error(f"Failed to generate performance report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate performance report: {str(e)}"
        )


@router.get("/connection-pool/status", response_model=Dict[str, Any])
async def get_connection_pool_status(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Get detailed connection pool status and metrics.

    Returns connection pool health, capacity utilization, connection states,
    and performance statistics.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Get connection pool status
        pool_status = await optimization_service.connection_pool.get_pool_status()

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "pool_status": pool_status
        }

    except Exception as e:
        logger.error(f"Failed to get connection pool status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve connection pool status: {str(e)}"
        )


@router.get("/message-queue/status", response_model=Dict[str, Any])
async def get_message_queue_status(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Get detailed message queue status and metrics.

    Returns message queue health, throughput statistics, queue sizes,
    and processing performance metrics.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Get message queue status
        queue_status = await optimization_service.message_queue.get_queue_status()

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "queue_status": queue_status
        }

    except Exception as e:
        logger.error(f"Failed to get message queue status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve message queue status: {str(e)}"
        )


@router.post("/optimize/high-traffic", response_model=BaseResponse)
async def optimize_for_high_traffic(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Optimize WebSocket infrastructure for high-traffic scenarios.

    Applies high-traffic optimizations including increased connection limits,
    enhanced message processing, and aggressive resource management.
    """
    try:
        # Get production optimization service
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )

        # Apply high-traffic optimizations
        await optimization_service.optimize_for_high_traffic()

        return BaseResponse(
            success=True,
            message="WebSocket infrastructure optimized for high-traffic scenarios",
            data={
                "optimization_applied": "high_traffic",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )

    except Exception as e:
        logger.error(f"Failed to optimize for high traffic: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply high-traffic optimization: {str(e)}"
        )


@router.get("/system-health", response_model=Dict[str, Any])
async def get_system_health(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Get comprehensive system health including WebSocket and infrastructure components.

    Returns overall system health status including database, Redis, WebSocket
    infrastructure, and external service connectivity.
    """
    try:
        # Get WebSocket health
        optimization_service = await get_production_optimization_service(
            db=db,
            cache_manager=cache_manager
        )
        websocket_health = await optimization_service.perform_health_check()

        # Get general system health
        system_health = await health_checker.get_comprehensive_health()

        # Combine health results
        combined_health = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_status": "healthy",
            "websocket_health": websocket_health,
            "system_health": system_health
        }

        # Determine overall status
        if (websocket_health["overall_status"] == "critical" or
            system_health.get("status") == "unhealthy"):
            combined_health["overall_status"] = "critical"
        elif (websocket_health["overall_status"] == "warning" or
              system_health.get("status") == "degraded"):
            combined_health["overall_status"] = "warning"

        return JSONResponse(
            status_code=status.HTTP_200_OK if combined_health["overall_status"] == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE,
            content=combined_health
        )

    except Exception as e:
        logger.error(f"System health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_status": "critical",
                "error": str(e)
            }
        )


@router.get("/readiness", response_model=Dict[str, Any])
async def check_readiness(
    db: AsyncSession = Depends(get_db),
    cache_manager: CacheManager = Depends(get_cache_manager)
):
    """
    Kubernetes readiness probe endpoint.

    Performs quick health checks to determine if the service is ready
    to receive traffic. Used by load balancers and orchestration systems.
    """
    try:
        # Quick readiness checks
        checks = {
            "database": False,
            "redis": False,
            "websocket_service": False
        }

        # Check database connectivity
        try:
            await db.execute("SELECT 1")
            checks["database"] = True
        except Exception:
            pass

        # Check Redis connectivity
        try:
            if cache_manager and cache_manager.redis:
                await cache_manager.redis.ping()
                checks["redis"] = True
        except Exception:
            pass

        # Check WebSocket service
        try:
            optimization_service = await get_production_optimization_service(
                db=db,
                cache_manager=cache_manager
            )
            if optimization_service:
                checks["websocket_service"] = True
        except Exception:
            pass

        # Determine readiness
        ready = all(checks.values())

        return JSONResponse(
            status_code=status.HTTP_200_OK if ready else status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "ready": ready,
                "checks": checks,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )

    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "ready": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.get("/liveness", response_model=Dict[str, Any])
async def check_liveness():
    """
    Kubernetes liveness probe endpoint.

    Simple health check to determine if the service is alive and responsive.
    Used by orchestration systems to restart unhealthy containers.
    """
    try:
        return {
            "alive": True,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "websocket_health"
        }

    except Exception as e:
        logger.error(f"Liveness check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "alive": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
