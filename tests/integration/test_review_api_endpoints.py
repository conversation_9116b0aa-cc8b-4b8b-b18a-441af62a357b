"""
Integration tests for review API endpoints.

This module provides comprehensive integration tests for review management API endpoints:
- Review CRUD operations with authentication and authorization
- Review response management with vendor permissions
- Review moderation admin endpoints with role-based access
- Review analytics endpoints with performance validation
- Error handling, validation, and security testing
- Performance benchmarks and response time validation

Implements Task 4.4.1 Phase 6 requirements with >85% test coverage.
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi import status
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any
from unittest.mock import AsyncMock, patch

from app.main import app
from app.models.review_models import ReviewStatus, ResponseStatus, ModerationAction
from app.core.security import UserRole


class TestReviewEndpoints:
    """Test review management API endpoints."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    def customer_token(self):
        """Mock customer JWT token."""
        return "Bearer customer_jwt_token"

    @pytest.fixture
    def vendor_token(self):
        """Mock vendor JWT token."""
        return "Bearer vendor_jwt_token"

    @pytest.fixture
    def admin_token(self):
        """Mock admin JWT token."""
        return "Bearer admin_jwt_token"

    @pytest.fixture
    def sample_review_data(self):
        """Sample review creation data."""
        return {
            "booking_id": 123,
            "rating": 5,
            "title": "Excellent Cultural Experience",
            "content": "The tour guide was knowledgeable and friendly. I learned so much about the local culture and history. Highly recommended!"
        }

    @pytest.fixture
    def sample_review_response(self):
        """Sample review API response."""
        return {
            "id": 1,
            "booking_id": 123,
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "rating": 5,
            "title": "Excellent Cultural Experience",
            "content": "The tour guide was knowledgeable and friendly. I learned so much about the local culture and history. Highly recommended!",
            "status": "approved",
            "is_verified_purchase": True,
            "helpful_count": 0,
            "reported_count": 0,
            "sentiment_score": 0.85,
            "language_code": "en",
            "is_positive_review": True,
            "has_vendor_response": False,
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

    @pytest.mark.asyncio
    async def test_create_review_success(self, async_client, customer_token, sample_review_data, sample_review_response):
        """Test successful review creation."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.create_review') as mock_service:
                mock_service.return_value = sample_review_response

                response = await async_client.post(
                    "/api/v1/reviews",
                    json=sample_review_data,
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_201_CREATED
                data = response.json()
                assert data["rating"] == 5
                assert data["title"] == "Excellent Cultural Experience"
                assert data["status"] == "approved"

    @pytest.mark.asyncio
    async def test_create_review_unauthorized(self, async_client, sample_review_data):
        """Test review creation without authentication."""
        response = await async_client.post(
            "/api/v1/reviews",
            json=sample_review_data
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_create_review_invalid_data(self, async_client, customer_token):
        """Test review creation with invalid data."""
        invalid_data = {
            "booking_id": -1,  # Invalid booking ID
            "rating": 6,       # Invalid rating
            "title": "Hi",     # Too short
            "content": "Bad"   # Too short
        }

        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            response = await async_client.post(
                "/api/v1/reviews",
                json=invalid_data,
                headers={"Authorization": customer_token}
            )

            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            data = response.json()
            assert "detail" in data

    @pytest.mark.asyncio
    async def test_get_review_by_id(self, async_client, sample_review_response):
        """Test getting review by ID."""
        with patch('app.services.review_service.ReviewService.get_review_by_id') as mock_service:
            mock_service.return_value = sample_review_response

            response = await async_client.get("/api/v1/reviews/1")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == 1
            assert data["rating"] == 5

    @pytest.mark.asyncio
    async def test_get_review_not_found(self, async_client):
        """Test getting non-existent review."""
        with patch('app.services.review_service.ReviewService.get_review_by_id') as mock_service:
            mock_service.return_value = None

            response = await async_client.get("/api/v1/reviews/999")

            assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_update_review_success(self, async_client, customer_token, sample_review_response):
        """Test successful review update."""
        update_data = {
            "rating": 4,
            "title": "Updated Review Title"
        }

        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.update_review') as mock_service:
                updated_response = {**sample_review_response, **update_data}
                mock_service.return_value = updated_response

                response = await async_client.put(
                    "/api/v1/reviews/1",
                    json=update_data,
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["rating"] == 4
                assert data["title"] == "Updated Review Title"

    @pytest.mark.asyncio
    async def test_delete_review_success(self, async_client, customer_token):
        """Test successful review deletion."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.delete_review') as mock_service:
                mock_service.return_value = True

                response = await async_client.delete(
                    "/api/v1/reviews/1",
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.asyncio
    async def test_get_reviews_with_filters(self, async_client, sample_review_response):
        """Test getting reviews with filters."""
        mock_response = {
            "items": [sample_review_response],
            "total": 1,
            "page": 1,
            "per_page": 10,
            "pages": 1
        }

        with patch('app.services.review_service.ReviewService.get_reviews_with_filters') as mock_service:
            mock_service.return_value = mock_response

            response = await async_client.get(
                "/api/v1/reviews",
                params={
                    "vendor_id": 2,
                    "min_rating": 4,
                    "status_filter": "approved",
                    "page": 1,
                    "per_page": 10
                }
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 1
            assert len(data["items"]) == 1
            assert data["items"][0]["rating"] == 5

    @pytest.mark.asyncio
    async def test_mark_review_helpful(self, async_client, customer_token, sample_review_response):
        """Test marking review as helpful."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "customer"}

            with patch('app.services.review_service.ReviewService.mark_review_helpful') as mock_service:
                helpful_response = {**sample_review_response, "helpful_count": 1}
                mock_service.return_value = helpful_response

                response = await async_client.post(
                    "/api/v1/reviews/1/helpful",
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["helpful_count"] == 1

    @pytest.mark.asyncio
    async def test_report_review(self, async_client, customer_token, sample_review_response):
        """Test reporting a review."""
        report_data = {
            "reason": "Inappropriate content"
        }

        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 3, "role": "customer"}

            with patch('app.services.review_service.ReviewService.report_review') as mock_service:
                reported_response = {**sample_review_response, "reported_count": 1}
                mock_service.return_value = reported_response

                response = await async_client.post(
                    "/api/v1/reviews/1/report",
                    json=report_data,
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["reported_count"] == 1

    @pytest.mark.asyncio
    async def test_get_vendor_reviews_summary(self, async_client):
        """Test getting vendor reviews summary."""
        mock_summary = {
            "total_reviews": 25,
            "average_rating": "4.6",
            "rating_distribution": {"1": 0, "2": 1, "3": 2, "4": 8, "5": 14},
            "recent_reviews": 5
        }

        with patch('app.services.review_service.ReviewService.get_vendor_reviews_summary') as mock_service:
            mock_service.return_value = mock_summary

            response = await async_client.get("/api/v1/reviews/vendor/2/summary")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total_reviews"] == 25
            assert data["average_rating"] == "4.6"
            assert data["rating_distribution"]["5"] == 14

    @pytest.mark.asyncio
    async def test_performance_review_creation(self, async_client, customer_token, sample_review_data):
        """Test review creation performance."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.create_review') as mock_service:
                mock_service.return_value = {"id": 1, "status": "pending"}

                # Measure response time
                start_time = datetime.now()

                response = await async_client.post(
                    "/api/v1/reviews",
                    json=sample_review_data,
                    headers={"Authorization": customer_token}
                )

                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds() * 1000  # Convert to milliseconds

                assert response.status_code == status.HTTP_201_CREATED
                assert response_time < 500  # Performance target: <500ms

    @pytest.mark.asyncio
    async def test_concurrent_review_operations(self, async_client, customer_token, sample_review_data):
        """Test concurrent review operations."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.create_review') as mock_service:
                mock_service.return_value = {"id": 1, "status": "pending"}

                # Create multiple concurrent requests
                tasks = []
                for i in range(5):
                    task = async_client.post(
                        "/api/v1/reviews",
                        json={**sample_review_data, "booking_id": 123 + i},
                        headers={"Authorization": customer_token}
                    )
                    tasks.append(task)

                # Execute concurrently
                responses = await asyncio.gather(*tasks, return_exceptions=True)

                # Verify all requests completed
                for response in responses:
                    if not isinstance(response, Exception):
                        assert response.status_code in [status.HTTP_201_CREATED, status.HTTP_409_CONFLICT]

    @pytest.mark.asyncio
    async def test_review_pagination(self, async_client):
        """Test review pagination."""
        mock_reviews = [{"id": i, "rating": 5} for i in range(1, 26)]  # 25 reviews
        mock_response = {
            "items": mock_reviews[:10],  # First 10 items
            "total": 25,
            "page": 1,
            "per_page": 10,
            "pages": 3
        }

        with patch('app.services.review_service.ReviewService.get_reviews_with_filters') as mock_service:
            mock_service.return_value = mock_response

            response = await async_client.get(
                "/api/v1/reviews",
                params={"page": 1, "per_page": 10}
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 25
            assert data["page"] == 1
            assert data["pages"] == 3
            assert len(data["items"]) == 10

    @pytest.mark.asyncio
    async def test_review_search_functionality(self, async_client, sample_review_response):
        """Test review search functionality."""
        mock_response = {
            "items": [sample_review_response],
            "total": 1,
            "page": 1,
            "per_page": 10,
            "pages": 1
        }

        with patch('app.services.review_service.ReviewService.get_reviews_with_filters') as mock_service:
            mock_service.return_value = mock_response

            response = await async_client.get(
                "/api/v1/reviews",
                params={
                    "search_query": "excellent cultural",
                    "min_rating": 4
                }
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 1
            assert "Excellent Cultural Experience" in data["items"][0]["title"]


class TestReviewResponseEndpoints:
    """Test review response API endpoints."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    def vendor_token(self):
        """Mock vendor JWT token."""
        return "Bearer vendor_jwt_token"

    @pytest.fixture
    def sample_response_data(self):
        """Sample response creation data."""
        return {
            "review_id": 123,
            "content": "Thank you for your detailed feedback! We're glad you enjoyed the cultural experience."
        }

    @pytest.fixture
    def sample_response_result(self):
        """Sample response API result."""
        return {
            "id": 1,
            "review_id": 123,
            "vendor_id": 2,
            "content": "Thank you for your detailed feedback! We're glad you enjoyed the cultural experience.",
            "status": "draft",
            "is_official_response": True,
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

    @pytest.mark.asyncio
    async def test_create_response_success(self, async_client, vendor_token, sample_response_data, sample_response_result):
        """Test successful response creation."""
        with patch('app.api.v1.endpoints.review_responses.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_response_service.ReviewResponseService.create_response') as mock_service:
                mock_service.return_value = sample_response_result

                response = await async_client.post(
                    "/api/v1/review-responses",
                    json=sample_response_data,
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_201_CREATED
                data = response.json()
                assert data["review_id"] == 123
                assert data["status"] == "draft"

    @pytest.mark.asyncio
    async def test_create_response_unauthorized(self, async_client, sample_response_data):
        """Test response creation without authentication."""
        response = await async_client.post(
            "/api/v1/review-responses",
            json=sample_response_data
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_publish_response(self, async_client, vendor_token, sample_response_result):
        """Test publishing a response."""
        with patch('app.api.v1.endpoints.review_responses.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_response_service.ReviewResponseService.publish_response') as mock_service:
                published_response = {**sample_response_result, "status": "published"}
                mock_service.return_value = published_response

                response = await async_client.post(
                    "/api/v1/review-responses/1/publish",
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "published"

    @pytest.mark.asyncio
    async def test_get_vendor_responses(self, async_client, vendor_token, sample_response_result):
        """Test getting vendor responses."""
        mock_response = {
            "items": [sample_response_result],
            "total": 1,
            "page": 1,
            "per_page": 10,
            "pages": 1
        }

        with patch('app.api.v1.endpoints.review_responses.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_response_service.ReviewResponseService.get_vendor_responses') as mock_service:
                mock_service.return_value = mock_response

                response = await async_client.get(
                    "/api/v1/review-responses/vendor/2",
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["total"] == 1
                assert len(data["items"]) == 1


class TestReviewModerationEndpoints:
    """Test review moderation API endpoints."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    def admin_token(self):
        """Mock admin JWT token."""
        return "Bearer admin_jwt_token"

    @pytest.fixture
    def sample_moderation_data(self):
        """Sample moderation data."""
        return {
            "review_id": 123,
            "action": "approve",
            "reason": "Content is appropriate and helpful"
        }

    @pytest.fixture
    def sample_moderation_result(self):
        """Sample moderation API result."""
        return {
            "id": 1,
            "review_id": 123,
            "moderator_id": 1,
            "action": "approve",
            "ai_confidence_score": 0.95,
            "manual_review_required": False,
            "reason": "Content is appropriate and helpful",
            "ai_analysis_results": {
                "sentiment": "positive",
                "toxicity_score": 0.02,
                "spam_probability": 0.01
            },
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

    @pytest.mark.asyncio
    async def test_process_manual_moderation(self, async_client, admin_token, sample_moderation_data, sample_moderation_result):
        """Test manual moderation processing."""
        with patch('app.api.v1.endpoints.review_moderation.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "admin"}

            with patch('app.services.review_moderation_service.ReviewModerationService.process_manual_moderation') as mock_service:
                mock_service.return_value = sample_moderation_result

                response = await async_client.post(
                    "/api/v1/review-moderation/1/process",
                    json=sample_moderation_data,
                    headers={"Authorization": admin_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["action"] == "approve"
                assert data["moderator_id"] == 1

    @pytest.mark.asyncio
    async def test_get_pending_moderations(self, async_client, admin_token, sample_moderation_result):
        """Test getting pending moderations."""
        mock_response = {
            "items": [sample_moderation_result],
            "total": 1,
            "page": 1,
            "per_page": 20,
            "pages": 1
        }

        with patch('app.api.v1.endpoints.review_moderation.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "admin"}

            with patch('app.services.review_moderation_service.ReviewModerationService.get_pending_moderations') as mock_service:
                mock_service.return_value = mock_response

                response = await async_client.get(
                    "/api/v1/review-moderation/pending",
                    headers={"Authorization": admin_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["total"] == 1
                assert len(data["items"]) == 1

    @pytest.mark.asyncio
    async def test_moderation_unauthorized_access(self, async_client, sample_moderation_data):
        """Test moderation access without admin role."""
        with patch('app.api.v1.endpoints.review_moderation.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "customer"}  # Non-admin user

            response = await async_client.post(
                "/api/v1/review-moderation/1/process",
                json=sample_moderation_data,
                headers={"Authorization": "Bearer customer_token"}
            )

            assert response.status_code == status.HTTP_403_FORBIDDEN


class TestReviewAnalyticsEndpoints:
    """Test review analytics API endpoints."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.fixture
    def vendor_token(self):
        """Mock vendor JWT token."""
        return "Bearer vendor_jwt_token"

    @pytest.fixture
    def sample_analytics_result(self):
        """Sample analytics API result."""
        return {
            "id": 1,
            "vendor_id": 2,
            "period_start": "2025-01-01",
            "period_end": "2025-01-31",
            "total_reviews": 25,
            "average_rating": "4.6",
            "rating_distribution": {"1": 0, "2": 1, "3": 2, "4": 8, "5": 14},
            "sentiment_breakdown": {"positive": 22, "neutral": 2, "negative": 1},
            "response_rate": "0.88",
            "average_response_time": 6,
            "verified_reviews_count": 24,
            "helpful_votes_total": 156,
            "reported_reviews_count": 0,
            "created_at": "2025-01-27T12:00:00Z",
            "updated_at": "2025-01-27T12:00:00Z"
        }

    @pytest.mark.asyncio
    async def test_get_vendor_analytics(self, async_client, vendor_token, sample_analytics_result):
        """Test getting vendor analytics."""
        with patch('app.api.v1.endpoints.review_analytics.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_analytics_service.ReviewAnalyticsService.calculate_vendor_analytics') as mock_service:
                mock_service.return_value = sample_analytics_result

                response = await async_client.get(
                    "/api/v1/review-analytics/vendor/2",
                    params={
                        "period_start": "2025-01-01",
                        "period_end": "2025-01-31"
                    },
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["total_reviews"] == 25
                assert data["average_rating"] == "4.6"
                assert data["rating_distribution"]["5"] == 14

    @pytest.mark.asyncio
    async def test_get_analytics_trends(self, async_client, vendor_token):
        """Test getting analytics trends."""
        mock_trends = {
            "rating_trend": "improving",
            "review_volume_trend": "increasing",
            "response_rate_trend": "stable",
            "sentiment_trend": "positive"
        }

        with patch('app.api.v1.endpoints.review_analytics.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_analytics_service.ReviewAnalyticsService.get_analytics_trends') as mock_service:
                mock_service.return_value = mock_trends

                response = await async_client.get(
                    "/api/v1/review-analytics/vendor/2/trends",
                    params={"months": 6},
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["rating_trend"] == "improving"
                assert data["review_volume_trend"] == "increasing"

    @pytest.mark.asyncio
    async def test_get_performance_benchmarks(self, async_client, vendor_token):
        """Test getting performance benchmarks."""
        mock_benchmarks = {
            "industry_average_rating": "4.2",
            "percentile_ranking": 85,
            "response_rate_benchmark": "0.75",
            "improvement_suggestions": [
                "Increase response rate to reviews",
                "Focus on addressing negative feedback"
            ]
        }

        with patch('app.api.v1.endpoints.review_analytics.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_analytics_service.ReviewAnalyticsService.calculate_performance_benchmarks') as mock_service:
                mock_service.return_value = mock_benchmarks

                response = await async_client.get(
                    "/api/v1/review-analytics/vendor/2/benchmarks",
                    headers={"Authorization": vendor_token}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["industry_average_rating"] == "4.2"
                assert data["percentile_ranking"] == 85
                assert len(data["improvement_suggestions"]) == 2

    @pytest.mark.asyncio
    async def test_analytics_performance_validation(self, async_client, vendor_token, sample_analytics_result):
        """Test analytics endpoint performance."""
        with patch('app.api.v1.endpoints.review_analytics.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 2, "role": "vendor"}

            with patch('app.services.review_analytics_service.ReviewAnalyticsService.calculate_vendor_analytics') as mock_service:
                mock_service.return_value = sample_analytics_result

                # Measure response time
                start_time = datetime.now()

                response = await async_client.get(
                    "/api/v1/review-analytics/vendor/2",
                    params={
                        "period_start": "2025-01-01",
                        "period_end": "2025-01-31"
                    },
                    headers={"Authorization": vendor_token}
                )

                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds() * 1000  # Convert to milliseconds

                assert response.status_code == status.HTTP_200_OK
                assert response_time < 200  # Performance target: <200ms for analytics queries


class TestErrorHandlingAndSecurity:
    """Test error handling and security scenarios."""

    @pytest.fixture
    async def async_client(self):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    @pytest.mark.asyncio
    async def test_sql_injection_prevention(self, async_client):
        """Test SQL injection prevention."""
        malicious_query = "'; DROP TABLE reviews; --"

        response = await async_client.get(
            "/api/v1/reviews",
            params={"search_query": malicious_query}
        )

        # Should not cause server error, should handle gracefully
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]

    @pytest.mark.asyncio
    async def test_rate_limiting(self, async_client, customer_token):
        """Test rate limiting for resource-intensive operations."""
        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            # Simulate multiple rapid requests
            tasks = []
            for i in range(20):  # Exceed rate limit
                task = async_client.post(
                    "/api/v1/reviews/1/helpful",
                    headers={"Authorization": customer_token}
                )
                tasks.append(task)

            responses = await asyncio.gather(*tasks, return_exceptions=True)

            # Some requests should be rate limited
            rate_limited_count = sum(
                1 for r in responses
                if not isinstance(r, Exception) and r.status_code == status.HTTP_429_TOO_MANY_REQUESTS
            )
            assert rate_limited_count > 0

    @pytest.mark.asyncio
    async def test_input_sanitization(self, async_client, customer_token):
        """Test input sanitization."""
        malicious_data = {
            "booking_id": 123,
            "rating": 5,
            "title": "<script>alert('xss')</script>",
            "content": "This contains <script>malicious code</script> that should be sanitized."
        }

        with patch('app.api.v1.endpoints.reviews.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": 1, "role": "customer"}

            with patch('app.services.review_service.ReviewService.create_review') as mock_service:
                # Service should sanitize input
                sanitized_response = {
                    "id": 1,
                    "title": "alert('xss')",  # Script tags removed
                    "content": "This contains malicious code that should be sanitized.",
                    "rating": 5
                }
                mock_service.return_value = sanitized_response

                response = await async_client.post(
                    "/api/v1/reviews",
                    json=malicious_data,
                    headers={"Authorization": customer_token}
                )

                assert response.status_code == status.HTTP_201_CREATED
                data = response.json()
                assert "<script>" not in data["title"]
                assert "<script>" not in data["content"]

    @pytest.mark.asyncio
    async def test_cors_headers(self, async_client):
        """Test CORS headers are properly set."""
        response = await async_client.options("/api/v1/reviews")

        assert "Access-Control-Allow-Origin" in response.headers
        assert "Access-Control-Allow-Methods" in response.headers
        assert "Access-Control-Allow-Headers" in response.headers

    @pytest.mark.asyncio
    async def test_error_response_format(self, async_client):
        """Test consistent error response format."""
        response = await async_client.get("/api/v1/reviews/999")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], str)
