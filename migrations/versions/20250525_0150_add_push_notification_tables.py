"""Add push notification tables for Task 2.3.2

Revision ID: 20250525_0150_push_notifications
Revises: email_tables
Create Date: 2025-01-25 01:50:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250525_0150_push_notifications'
down_revision = 'email_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add push notification tables."""

    # Create device platform enum
    device_platform_enum = postgresql.ENUM(
        'ios', 'android', 'web',
        name='deviceplatform',
        create_type=False
    )
    device_platform_enum.create(op.get_bind(), checkfirst=True)

    # Create notification status enum
    notification_status_enum = postgresql.ENUM(
        'pending', 'sent', 'delivered', 'failed', 'expired', 'cancelled',
        name='notificationstatus',
        create_type=False
    )
    notification_status_enum.create(op.get_bind(), checkfirst=True)

    # Create notification priority enum
    notification_priority_enum = postgresql.ENUM(
        'low', 'normal', 'high', 'critical',
        name='notificationpriority',
        create_type=False
    )
    notification_priority_enum.create(op.get_bind(), checkfirst=True)

    # Create notification category enum
    notification_category_enum = postgresql.ENUM(
        'authentication', 'booking', 'payment', 'promotional', 'system', 'security', 'social', 'reminder',
        name='notificationcategory',
        create_type=False
    )
    notification_category_enum.create(op.get_bind(), checkfirst=True)

    # Create notification frequency enum
    notification_frequency_enum = postgresql.ENUM(
        'immediate', 'hourly', 'daily', 'weekly', 'disabled',
        name='notificationfrequency',
        create_type=False
    )
    notification_frequency_enum.create(op.get_bind(), checkfirst=True)

    # Create device_tokens table
    op.create_table(
        'device_tokens',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False, index=True),
        sa.Column('token', sa.String(500), nullable=False, unique=True, index=True),
        sa.Column('platform', device_platform_enum, nullable=False, index=True),
        sa.Column('device_id', sa.String(255), nullable=True, index=True),
        sa.Column('device_name', sa.String(255), nullable=True),
        sa.Column('app_version', sa.String(50), nullable=True),
        sa.Column('os_version', sa.String(50), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, index=True),
        sa.Column('is_validated', sa.Boolean(), nullable=False, default=False),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('validation_attempts', sa.Integer(), nullable=False, default=0),
        sa.Column('registration_ip', sa.String(45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('device_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    )

    # Create indexes for device_tokens
    op.create_index('idx_device_token_user_platform', 'device_tokens', ['user_id', 'platform'])
    op.create_index('idx_device_token_active_validated', 'device_tokens', ['is_active', 'is_validated'])
    op.create_index('idx_device_token_last_used', 'device_tokens', ['last_used_at'])
    op.create_unique_constraint('uq_device_token_user_device', 'device_tokens', ['user_id', 'device_id', 'platform'])

    # Create notification_templates table
    op.create_table(
        'notification_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('category', notification_category_enum, nullable=False, index=True),
        sa.Column('title_template', sa.String(500), nullable=False),
        sa.Column('body_template', sa.Text(), nullable=False),
        sa.Column('ios_payload', sa.JSON(), nullable=True),
        sa.Column('android_payload', sa.JSON(), nullable=True),
        sa.Column('web_payload', sa.JSON(), nullable=True),
        sa.Column('variables', sa.JSON(), nullable=False, default={}),
        sa.Column('default_priority', notification_priority_enum, nullable=False, default='normal'),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, index=True),
        sa.Column('click_action', sa.String(255), nullable=True),
        sa.Column('deep_link', sa.String(500), nullable=True),
        sa.Column('image_url', sa.String(500), nullable=True),
        sa.Column('created_by', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    )

    # Create indexes for notification_templates
    op.create_index('idx_notification_template_category_active', 'notification_templates', ['category', 'is_active'])
    op.create_index('idx_notification_template_name_version', 'notification_templates', ['name', 'version'])
    op.create_unique_constraint('uq_notification_template_name_version', 'notification_templates', ['name', 'version'])

    # Create notification_deliveries table
    op.create_table(
        'notification_deliveries',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False, index=True),
        sa.Column('device_token_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('device_tokens.id'), nullable=False, index=True),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('notification_templates.id'), nullable=True, index=True),
        sa.Column('title', sa.String(500), nullable=False),
        sa.Column('body', sa.Text(), nullable=False),
        sa.Column('status', notification_status_enum, nullable=False, default='pending', index=True),
        sa.Column('priority', notification_priority_enum, nullable=False, default='normal'),
        sa.Column('fcm_message_id', sa.String(255), nullable=True, index=True),
        sa.Column('fcm_response', sa.JSON(), nullable=True),
        sa.Column('error_code', sa.String(100), nullable=True, index=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('clicked_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=False, default=3),
        sa.Column('next_retry_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('payload', sa.JSON(), nullable=True),
        sa.Column('delivery_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    )

    # Create indexes for notification_deliveries
    op.create_index('idx_notification_delivery_user_status', 'notification_deliveries', ['user_id', 'status'])
    op.create_index('idx_notification_delivery_status_created', 'notification_deliveries', ['status', 'created_at'])
    op.create_index('idx_notification_delivery_fcm_message', 'notification_deliveries', ['fcm_message_id'])
    op.create_index('idx_notification_delivery_retry', 'notification_deliveries', ['next_retry_at', 'retry_count'])

    # Create notification_preferences table
    op.create_table(
        'notification_preferences',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False, unique=True, index=True),
        sa.Column('push_notifications_enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('authentication_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('booking_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('payment_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('promotional_notifications', notification_frequency_enum, nullable=False, default='daily'),
        sa.Column('system_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('security_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('social_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('reminder_notifications', notification_frequency_enum, nullable=False, default='immediate'),
        sa.Column('dnd_enabled', sa.Boolean(), nullable=False, default=False),
        sa.Column('dnd_start_time', sa.String(5), nullable=True),
        sa.Column('dnd_end_time', sa.String(5), nullable=True),
        sa.Column('dnd_timezone', sa.String(50), nullable=True, default='UTC'),
        sa.Column('ios_badge_count', sa.Boolean(), nullable=False, default=True),
        sa.Column('android_vibration', sa.Boolean(), nullable=False, default=True),
        sa.Column('sound_enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('language_code', sa.String(10), nullable=False, default='en'),
        sa.Column('max_daily_notifications', sa.Integer(), nullable=True),
        sa.Column('preference_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    )

    # Create indexes for notification_preferences
    op.create_index('idx_notification_preference_user', 'notification_preferences', ['user_id'])
    op.create_index('idx_notification_preference_enabled', 'notification_preferences', ['push_notifications_enabled'])

    # Create notification_queue table
    op.create_table(
        'notification_queue',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False, index=True),
        sa.Column('device_token_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('device_tokens.id'), nullable=False, index=True),
        sa.Column('template_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('notification_templates.id'), nullable=True, index=True),
        sa.Column('title', sa.String(500), nullable=False),
        sa.Column('body', sa.Text(), nullable=False),
        sa.Column('status', notification_status_enum, nullable=False, default='pending', index=True),
        sa.Column('priority', notification_priority_enum, nullable=False, default='normal', index=True),
        sa.Column('scheduled_at', sa.DateTime(timezone=True), nullable=True, index=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True, index=True),
        sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=False, default=3),
        sa.Column('next_retry_at', sa.DateTime(timezone=True), nullable=True, index=True),
        sa.Column('error_code', sa.String(100), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('payload', sa.JSON(), nullable=True),
        sa.Column('queue_metadata', sa.JSON(), nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    )

    # Create indexes for notification_queue
    op.create_index('idx_notification_queue_status_priority', 'notification_queue', ['status', 'priority'])
    op.create_index('idx_notification_queue_scheduled', 'notification_queue', ['scheduled_at', 'status'])
    op.create_index('idx_notification_queue_retry', 'notification_queue', ['next_retry_at', 'retry_count'])
    op.create_index('idx_notification_queue_expires', 'notification_queue', ['expires_at'])
    op.create_index('idx_notification_queue_user_status', 'notification_queue', ['user_id', 'status'])


def downgrade() -> None:
    """Remove push notification tables."""

    # Drop tables in reverse order
    op.drop_table('notification_queue')
    op.drop_table('notification_preferences')
    op.drop_table('notification_deliveries')
    op.drop_table('notification_templates')
    op.drop_table('device_tokens')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS notificationfrequency')
    op.execute('DROP TYPE IF EXISTS notificationcategory')
    op.execute('DROP TYPE IF EXISTS notificationpriority')
    op.execute('DROP TYPE IF EXISTS notificationstatus')
    op.execute('DROP TYPE IF EXISTS deviceplatform')
