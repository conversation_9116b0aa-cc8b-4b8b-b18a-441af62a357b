"""
OAuth models for Culture Connect Backend API.

This module defines comprehensive OAuth authentication models including:
- OAuthProvider: OAuth provider configuration and metadata
- OAuthAccount: User OAuth account linking and profile data
- OAuthToken: Secure OAuth token storage with encryption
- OAuthState: CSRF protection for OAuth flows

Implements Task 2.1.3 requirements for OAuth2 integration with Google and Facebook
providers, following production-grade security standards and PostgreSQL optimization.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Index, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.db.base import BaseModelWithAudit
from app.core.security import encrypt_data, decrypt_data


class OAuthProvider(BaseModelWithAudit):
    """
    OAuth provider configuration and metadata.

    This model stores OAuth provider information including client configuration,
    endpoints, and provider-specific settings for Google, Facebook, etc.
    """

    __tablename__ = "oauth_providers"

    # Provider identification
    name = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="OAuth provider name (google, facebook, etc.)"
    )

    display_name = Column(
        String(100),
        nullable=False,
        doc="Human-readable provider name"
    )

    # OAuth configuration
    client_id = Column(
        String(255),
        nullable=False,
        doc="OAuth client ID"
    )

    client_secret_encrypted = Column(
        Text,
        nullable=False,
        doc="Encrypted OAuth client secret"
    )

    # OAuth endpoints
    authorization_url = Column(
        String(500),
        nullable=False,
        doc="OAuth authorization endpoint URL"
    )

    token_url = Column(
        String(500),
        nullable=False,
        doc="OAuth token endpoint URL"
    )

    user_info_url = Column(
        String(500),
        nullable=False,
        doc="OAuth user info endpoint URL"
    )

    # Provider settings
    scopes = Column(
        JSON,
        nullable=False,
        default=list,
        doc="Required OAuth scopes"
    )

    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Provider active status"
    )

    # Provider metadata
    icon_url = Column(
        String(500),
        nullable=True,
        doc="Provider icon URL"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Provider description"
    )

    # Relationships
    oauth_accounts = relationship(
        "OAuthAccount",
        back_populates="provider",
        cascade="all, delete-orphan"
    )

    oauth_states = relationship(
        "OAuthState",
        back_populates="provider",
        cascade="all, delete-orphan"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_oauth_provider_name_active", "name", "is_active"),
        Index("idx_oauth_provider_active", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of the OAuth provider."""
        return f"<OAuthProvider(id={self.id}, name='{self.name}', active={self.is_active})>"

    @property
    def client_secret(self) -> str:
        """Decrypt and return client secret."""
        return decrypt_data(self.client_secret_encrypted)

    @client_secret.setter
    def client_secret(self, value: str) -> None:
        """Encrypt and store client secret."""
        self.client_secret_encrypted = encrypt_data(value)


class OAuthAccount(BaseModelWithAudit):
    """
    User OAuth account linking and profile data.

    This model links user accounts with OAuth providers and stores
    OAuth-specific profile information and account metadata.
    """

    __tablename__ = "oauth_accounts"

    # User relationship
    user_id = Column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated user ID"
    )

    # Provider relationship
    provider_id = Column(
        Integer,
        ForeignKey("oauth_providers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="OAuth provider ID"
    )

    # OAuth account identification
    provider_user_id = Column(
        String(255),
        nullable=False,
        index=True,
        doc="User ID from OAuth provider"
    )

    provider_username = Column(
        String(255),
        nullable=True,
        doc="Username from OAuth provider"
    )

    provider_email = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Email from OAuth provider"
    )

    # Profile information
    profile_data = Column(
        JSON,
        nullable=True,
        doc="OAuth profile data from provider"
    )

    # Account status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="OAuth account active status"
    )

    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="OAuth account verification status"
    )

    # OAuth metadata
    first_connected_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="First connection timestamp"
    )

    last_connected_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Last connection timestamp"
    )

    # Relationships
    user = relationship(
        "User",
        back_populates="oauth_accounts",
        foreign_keys=[user_id]
    )

    provider = relationship(
        "OAuthProvider",
        back_populates="oauth_accounts"
    )

    oauth_tokens = relationship(
        "OAuthToken",
        back_populates="oauth_account",
        cascade="all, delete-orphan"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_oauth_account_user_provider", "user_id", "provider_id"),
        Index("idx_oauth_account_provider_user", "provider_id", "provider_user_id"),
        Index("idx_oauth_account_email", "provider_email"),
        Index("idx_oauth_account_active", "is_active"),
        # Unique constraint for provider + provider_user_id
        Index("idx_oauth_account_unique", "provider_id", "provider_user_id", unique=True),
    )

    def __repr__(self) -> str:
        """String representation of the OAuth account."""
        return f"<OAuthAccount(id={self.id}, user_id={self.user_id}, provider_id={self.provider_id})>"


class OAuthToken(BaseModelWithAudit):
    """
    Secure OAuth token storage with encryption.

    This model stores OAuth access and refresh tokens with encryption
    and automatic expiration handling for secure token management.
    """

    __tablename__ = "oauth_tokens"

    # OAuth account relationship
    oauth_account_id = Column(
        Integer,
        ForeignKey("oauth_accounts.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated OAuth account ID"
    )

    # Token data (encrypted)
    access_token_encrypted = Column(
        Text,
        nullable=False,
        doc="Encrypted OAuth access token"
    )

    refresh_token_encrypted = Column(
        Text,
        nullable=True,
        doc="Encrypted OAuth refresh token"
    )

    # Token metadata
    token_type = Column(
        String(50),
        default="Bearer",
        nullable=False,
        doc="OAuth token type"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Access token expiration timestamp"
    )

    scopes = Column(
        JSON,
        nullable=True,
        doc="OAuth token scopes"
    )

    # Token status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Token active status"
    )

    is_revoked = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Token revocation status"
    )

    # Relationships
    oauth_account = relationship(
        "OAuthAccount",
        back_populates="oauth_tokens"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_oauth_token_account_active", "oauth_account_id", "is_active"),
        Index("idx_oauth_token_expires", "expires_at"),
        Index("idx_oauth_token_revoked", "is_revoked"),
    )

    def __repr__(self) -> str:
        """String representation of the OAuth token."""
        return f"<OAuthToken(id={self.id}, account_id={self.oauth_account_id}, active={self.is_active})>"

    @property
    def access_token(self) -> str:
        """Decrypt and return access token."""
        return decrypt_data(self.access_token_encrypted)

    @access_token.setter
    def access_token(self, value: str) -> None:
        """Encrypt and store access token."""
        self.access_token_encrypted = encrypt_data(value)

    @property
    def refresh_token(self) -> Optional[str]:
        """Decrypt and return refresh token."""
        if self.refresh_token_encrypted:
            return decrypt_data(self.refresh_token_encrypted)
        return None

    @refresh_token.setter
    def refresh_token(self, value: Optional[str]) -> None:
        """Encrypt and store refresh token."""
        if value:
            self.refresh_token_encrypted = encrypt_data(value)
        else:
            self.refresh_token_encrypted = None

    @property
    def is_expired(self) -> bool:
        """Check if access token is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    def set_expiration(self, expires_in_seconds: int) -> None:
        """Set token expiration based on seconds from now."""
        self.expires_at = datetime.utcnow() + timedelta(seconds=expires_in_seconds)


class OAuthState(BaseModelWithAudit):
    """
    CSRF protection for OAuth flows.

    This model stores OAuth state parameters for CSRF protection
    and tracks OAuth flow progress and security.
    """

    __tablename__ = "oauth_states"

    # State identification
    state_token = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique OAuth state token"
    )

    # Provider relationship
    provider_id = Column(
        Integer,
        ForeignKey("oauth_providers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="OAuth provider ID"
    )

    # State metadata
    redirect_uri = Column(
        String(500),
        nullable=False,
        doc="OAuth redirect URI"
    )

    scopes = Column(
        JSON,
        nullable=True,
        doc="Requested OAuth scopes"
    )

    # Security tracking
    client_ip = Column(
        String(45),
        nullable=True,
        doc="Client IP address"
    )

    user_agent = Column(
        Text,
        nullable=True,
        doc="Client user agent"
    )

    # State status
    is_used = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="State token usage status"
    )

    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="State token expiration"
    )

    used_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="State token usage timestamp"
    )

    # Relationships
    provider = relationship(
        "OAuthProvider",
        back_populates="oauth_states"
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_oauth_state_token_used", "state_token", "is_used"),
        Index("idx_oauth_state_expires", "expires_at"),
        Index("idx_oauth_state_provider", "provider_id"),
    )

    def __repr__(self) -> str:
        """String representation of the OAuth state."""
        return f"<OAuthState(id={self.id}, token='{self.state_token[:8]}...', used={self.is_used})>"

    @property
    def is_expired(self) -> bool:
        """Check if state token is expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if state token is valid (not used and not expired)."""
        return not self.is_used and not self.is_expired

    def mark_used(self) -> None:
        """Mark state token as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()
