"""
WebSocket Infrastructure schemas for Culture Connect Backend API.

This module defines comprehensive Pydantic schemas for WebSocket communication including:
- WebSocket connection management and authentication
- Real-time event handling and validation
- Message delivery and acknowledgment tracking
- Performance metrics and monitoring
- User presence and activity tracking

Implements Task 6.1.1 Phase 1 requirements for WebSocket schemas with:
- Type-safe event definitions with validation
- Performance monitoring and metrics collection
- Scalable message routing and delivery tracking
- Integration with existing RBAC and notification systems
- Production-grade error handling and validation

Production-grade implementation following Pydantic V2 patterns and monolithic FastAPI architecture.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, field_validator


class WebSocketBaseSchema(BaseModel):
    """Base schema for WebSocket-related models."""

    model_config = ConfigDict(
        from_attributes=True,
        use_enum_values=True,
        validate_assignment=True,
        arbitrary_types_allowed=True
    )


class ConnectionStatusEnum(str, Enum):
    """WebSocket connection status enumeration."""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    RECONNECTING = "reconnecting"


class EventTypeEnum(str, Enum):
    """WebSocket event type enumeration."""
    # Connection events
    CONNECTION_ESTABLISHED = "connection.established"
    CONNECTION_AUTHENTICATED = "connection.authenticated"
    CONNECTION_CLOSED = "connection.closed"
    CONNECTION_ERROR = "connection.error"

    # Booking events
    BOOKING_CREATED = "booking.created"
    BOOKING_UPDATED = "booking.updated"
    BOOKING_STATUS_CHANGED = "booking.status_changed"
    BOOKING_CANCELLED = "booking.cancelled"

    # Message events
    MESSAGE_SENT = "message.sent"
    MESSAGE_RECEIVED = "message.received"
    MESSAGE_READ = "message.read"
    TYPING_START = "typing.start"
    TYPING_STOP = "typing.stop"

    # Review events
    REVIEW_CREATED = "review.created"
    REVIEW_UPDATED = "review.updated"
    REVIEW_RESPONSE_ADDED = "review.response_added"

    # Notification events
    NOTIFICATION_SENT = "notification.sent"
    NOTIFICATION_READ = "notification.read"
    NOTIFICATION_DELIVERED = "notification.delivered"
    NOTIFICATION_DISMISSED = "notification.dismissed"

    # Real-time communication events
    CONVERSATION_STARTED = "conversation.started"
    CONVERSATION_ENDED = "conversation.ended"
    PARTICIPANT_JOINED = "participant.joined"
    PARTICIPANT_LEFT = "participant.left"
    CONVERSATION_SUMMARY_UPDATED = "conversation.summary_updated"

    # Enhanced user presence events
    USER_ONLINE = "user.online"
    USER_OFFLINE = "user.offline"
    USER_AWAY = "user.away"
    USER_PRESENCE_CHANGED = "user.presence_changed"
    USER_ACTIVITY_DETECTED = "user.activity_detected"

    # System events
    SYSTEM_MAINTENANCE = "system.maintenance"
    SYSTEM_ANNOUNCEMENT = "system.announcement"
    SYSTEM_BROADCAST = "system.broadcast"
    SYSTEM_HEALTH_CHECK = "system.health_check"

    # Connection management events
    CONNECTION_HEARTBEAT = "connection.heartbeat"
    CONNECTION_RECONNECT = "connection.reconnect"
    CONNECTION_RATE_LIMITED = "connection.rate_limited"


class EventPriorityEnum(str, Enum):
    """Event priority levels for message routing."""
    CRITICAL = "critical"
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"


class PresenceStatusEnum(str, Enum):
    """User presence status enumeration."""
    ONLINE = "online"
    AWAY = "away"
    OFFLINE = "offline"


# WebSocket Connection Schemas

class WebSocketConnectionCreate(WebSocketBaseSchema):
    """Schema for creating WebSocket connections."""

    connection_id: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Unique connection identifier",
        example="ws_conn_123456789"
    )

    session_id: Optional[str] = Field(
        None,
        max_length=255,
        description="Session identifier for connection grouping",
        example="session_abc123"
    )

    user_id: int = Field(
        ...,
        gt=0,
        description="Connected user ID",
        example=123
    )

    client_type: Optional[str] = Field(
        None,
        max_length=50,
        description="Client type (web, mobile, pwa)",
        example="web"
    )

    user_agent: Optional[str] = Field(
        None,
        description="Client user agent string",
        example="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    )

    ip_address: Optional[str] = Field(
        None,
        max_length=45,
        description="Client IP address",
        example="*************"
    )

    metadata_json: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional connection metadata"
    )


class WebSocketConnectionUpdate(WebSocketBaseSchema):
    """Schema for updating WebSocket connections."""

    status: Optional[ConnectionStatusEnum] = Field(
        None,
        description="Connection status"
    )

    authenticated_at: Optional[datetime] = Field(
        None,
        description="Authentication timestamp"
    )

    auth_token_hash: Optional[str] = Field(
        None,
        max_length=255,
        description="Hashed authentication token"
    )

    last_activity_at: Optional[datetime] = Field(
        None,
        description="Last activity timestamp"
    )

    disconnected_at: Optional[datetime] = Field(
        None,
        description="Disconnection timestamp"
    )

    error_count: Optional[int] = Field(
        None,
        ge=0,
        description="Number of errors encountered"
    )

    last_error: Optional[str] = Field(
        None,
        description="Last error message"
    )

    metadata_json: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional connection metadata"
    )


class WebSocketConnectionResponse(WebSocketBaseSchema):
    """Schema for WebSocket connection responses."""

    id: int = Field(..., description="Connection database ID")
    connection_id: str = Field(..., description="Unique connection identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    user_id: int = Field(..., description="Connected user ID")
    status: ConnectionStatusEnum = Field(..., description="Connection status")
    client_type: Optional[str] = Field(None, description="Client type")
    ip_address: Optional[str] = Field(None, description="Client IP address")

    # Timestamps
    connected_at: datetime = Field(..., description="Connection establishment timestamp")
    last_activity_at: datetime = Field(..., description="Last activity timestamp")
    authenticated_at: Optional[datetime] = Field(None, description="Authentication timestamp")
    disconnected_at: Optional[datetime] = Field(None, description="Disconnection timestamp")

    # Performance metrics
    message_count: int = Field(..., description="Total messages sent")
    bytes_sent: int = Field(..., description="Total bytes sent")
    bytes_received: int = Field(..., description="Total bytes received")

    # Error tracking
    error_count: int = Field(..., description="Number of errors encountered")
    last_error: Optional[str] = Field(None, description="Last error message")
    last_error_at: Optional[datetime] = Field(None, description="Last error timestamp")

    # Metadata
    metadata_json: Optional[Dict[str, Any]] = Field(None, description="Connection metadata")

    # Computed properties
    is_active: bool = Field(..., description="Whether connection is active")
    is_authenticated: bool = Field(..., description="Whether connection is authenticated")


# WebSocket Event Schemas

class WebSocketEventCreate(WebSocketBaseSchema):
    """Schema for creating WebSocket events."""

    connection_id: int = Field(
        ...,
        gt=0,
        description="WebSocket connection ID",
        example=123
    )

    event_type: EventTypeEnum = Field(
        ...,
        description="Type of event",
        example=EventTypeEnum.MESSAGE_SENT
    )

    priority: EventPriorityEnum = Field(
        EventPriorityEnum.NORMAL,
        description="Event priority level"
    )

    payload: Dict[str, Any] = Field(
        ...,
        description="Event payload data",
        example={
            "message": "Hello, world!",
            "timestamp": "2025-01-27T12:00:00Z"
        }
    )

    max_retries: Optional[int] = Field(
        3,
        ge=0,
        le=10,
        description="Maximum retry attempts"
    )

    metadata_json: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional event metadata"
    )

    @field_validator('payload')
    @classmethod
    def validate_payload_size(cls, v):
        """Validate payload size to prevent DoS attacks."""
        import json
        payload_str = json.dumps(v)
        if len(payload_str) > 100000:  # 100KB limit
            raise ValueError("Event payload too large (max 100KB)")
        return v


class WebSocketEventUpdate(WebSocketBaseSchema):
    """Schema for updating WebSocket events."""

    delivery_status: Optional[str] = Field(
        None,
        pattern=r'^(pending|sent|delivered|acknowledged|failed)$',
        description="Delivery status"
    )

    delivered_at: Optional[datetime] = Field(
        None,
        description="Event delivery confirmation timestamp"
    )

    acknowledged_at: Optional[datetime] = Field(
        None,
        description="Event acknowledgment timestamp"
    )

    retry_count: Optional[int] = Field(
        None,
        ge=0,
        description="Number of delivery retry attempts"
    )

    next_retry_at: Optional[datetime] = Field(
        None,
        description="Next retry attempt timestamp"
    )

    processing_time_ms: Optional[float] = Field(
        None,
        ge=0,
        description="Event processing time in milliseconds"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message if delivery failed"
    )

    error_code: Optional[str] = Field(
        None,
        max_length=50,
        description="Error code for categorization"
    )


class WebSocketEventResponse(WebSocketBaseSchema):
    """Schema for WebSocket event responses."""

    id: int = Field(..., description="Event database ID")
    event_id: str = Field(..., description="Unique event identifier")
    connection_id: int = Field(..., description="WebSocket connection ID")
    event_type: EventTypeEnum = Field(..., description="Type of event")
    priority: EventPriorityEnum = Field(..., description="Event priority level")

    # Event data
    payload: Dict[str, Any] = Field(..., description="Event payload data")
    payload_size: int = Field(..., description="Event payload size in bytes")

    # Delivery tracking
    sent_at: datetime = Field(..., description="Event sent timestamp")
    delivered_at: Optional[datetime] = Field(None, description="Event delivery confirmation timestamp")
    acknowledged_at: Optional[datetime] = Field(None, description="Event acknowledgment timestamp")

    # Retry logic
    retry_count: int = Field(..., description="Number of delivery retry attempts")
    max_retries: int = Field(..., description="Maximum retry attempts")
    next_retry_at: Optional[datetime] = Field(None, description="Next retry attempt timestamp")

    # Status and performance
    delivery_status: str = Field(..., description="Delivery status")
    processing_time_ms: Optional[float] = Field(None, description="Event processing time in milliseconds")

    # Error tracking
    error_message: Optional[str] = Field(None, description="Error message if delivery failed")
    error_code: Optional[str] = Field(None, description="Error code for categorization")

    # Metadata
    metadata_json: Optional[Dict[str, Any]] = Field(None, description="Event metadata")

    # Computed properties
    is_delivered: bool = Field(..., description="Whether event has been delivered")
    is_failed: bool = Field(..., description="Whether event delivery has failed")
    can_retry: bool = Field(..., description="Whether event can be retried")


# User Presence Schemas

class UserPresenceCreate(WebSocketBaseSchema):
    """Schema for creating user presence records."""

    user_id: int = Field(
        ...,
        gt=0,
        description="User ID",
        example=123
    )

    status: PresenceStatusEnum = Field(
        PresenceStatusEnum.OFFLINE,
        description="User presence status"
    )

    primary_device_type: Optional[str] = Field(
        None,
        max_length=50,
        description="Primary device type (web, mobile, pwa)",
        example="web"
    )

    status_message: Optional[str] = Field(
        None,
        max_length=200,
        description="Custom status message",
        example="Working on exciting projects!"
    )

    auto_away_enabled: bool = Field(
        True,
        description="Whether to automatically set away status"
    )


class UserPresenceUpdate(WebSocketBaseSchema):
    """Schema for updating user presence."""

    status: Optional[PresenceStatusEnum] = Field(
        None,
        description="User presence status"
    )

    last_activity_type: Optional[str] = Field(
        None,
        max_length=50,
        description="Type of last activity"
    )

    active_connections_count: Optional[int] = Field(
        None,
        ge=0,
        description="Number of active WebSocket connections"
    )

    primary_device_type: Optional[str] = Field(
        None,
        max_length=50,
        description="Primary device type"
    )

    status_message: Optional[str] = Field(
        None,
        max_length=200,
        description="Custom status message"
    )

    auto_away_enabled: Optional[bool] = Field(
        None,
        description="Whether to automatically set away status"
    )


class UserPresenceResponse(WebSocketBaseSchema):
    """Schema for user presence responses."""

    id: int = Field(..., description="Presence record database ID")
    user_id: int = Field(..., description="User ID")
    status: PresenceStatusEnum = Field(..., description="User presence status")

    # Activity tracking
    last_seen_at: datetime = Field(..., description="Last activity timestamp")
    last_activity_type: Optional[str] = Field(None, description="Type of last activity")

    # Connection tracking
    active_connections_count: int = Field(..., description="Number of active WebSocket connections")

    # Device and status
    primary_device_type: Optional[str] = Field(None, description="Primary device type")
    status_message: Optional[str] = Field(None, description="Custom status message")
    auto_away_enabled: bool = Field(..., description="Whether to automatically set away status")

    # Computed properties
    is_online: bool = Field(..., description="Whether user is currently online")
    is_away: bool = Field(..., description="Whether user is away")
    is_offline: bool = Field(..., description="Whether user is offline")


# WebSocket Message Schemas

class WebSocketMessage(WebSocketBaseSchema):
    """Schema for WebSocket messages."""

    type: EventTypeEnum = Field(
        ...,
        description="Message type",
        example=EventTypeEnum.MESSAGE_SENT
    )

    data: Dict[str, Any] = Field(
        ...,
        description="Message data payload",
        example={
            "content": "Hello, world!",
            "timestamp": "2025-01-27T12:00:00Z"
        }
    )

    timestamp: datetime = Field(
        default=None,
        description="Message timestamp"
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Correlation ID for tracking related messages"
    )

    priority: EventPriorityEnum = Field(
        EventPriorityEnum.NORMAL,
        description="Message priority"
    )

    @field_validator('data')
    @classmethod
    def validate_data_size(cls, v):
        """Validate data size to prevent DoS attacks."""
        import json
        data_str = json.dumps(v)
        if len(data_str) > 50000:  # 50KB limit for individual messages
            raise ValueError("Message data too large (max 50KB)")
        return v


class WebSocketAuthMessage(WebSocketBaseSchema):
    """Schema for WebSocket authentication messages."""

    type: str = Field(
        "auth",
        description="Message type for authentication"
    )

    token: str = Field(
        ...,
        min_length=1,
        description="Authentication token (JWT)",
        example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    )

    client_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional client information",
        example={
            "version": "1.0.0",
            "platform": "web",
            "user_agent": "Mozilla/5.0..."
        }
    )


class WebSocketAckMessage(WebSocketBaseSchema):
    """Schema for WebSocket acknowledgment messages."""

    type: str = Field(
        "ack",
        description="Message type for acknowledgment"
    )

    event_id: str = Field(
        ...,
        description="Event ID being acknowledged",
        example="evt_123456789"
    )

    status: str = Field(
        ...,
        pattern=r'^(received|processed|error)$',
        description="Acknowledgment status",
        example="received"
    )

    timestamp: datetime = Field(
        default=None,
        description="Acknowledgment timestamp"
    )

    error_message: Optional[str] = Field(
        None,
        description="Error message if status is error"
    )


# WebSocket Connection Metrics Schemas

class WebSocketConnectionMetricsCreate(WebSocketBaseSchema):
    """Schema for creating WebSocket connection metrics."""

    metric_date: datetime = Field(
        ...,
        description="Date for this metric period"
    )

    metric_hour: int = Field(
        ...,
        ge=0,
        le=23,
        description="Hour of the day (0-23)"
    )

    total_connections: int = Field(
        0,
        ge=0,
        description="Total connections established"
    )

    active_connections: int = Field(
        0,
        ge=0,
        description="Peak active connections"
    )

    authenticated_connections: int = Field(
        0,
        ge=0,
        description="Successfully authenticated connections"
    )

    failed_connections: int = Field(
        0,
        ge=0,
        description="Failed connection attempts"
    )


class WebSocketConnectionMetricsResponse(WebSocketBaseSchema):
    """Schema for WebSocket connection metrics responses."""

    id: int = Field(..., description="Metrics record database ID")
    metric_date: datetime = Field(..., description="Date for this metric period")
    metric_hour: int = Field(..., description="Hour of the day (0-23)")

    # Connection metrics
    total_connections: int = Field(..., description="Total connections established")
    active_connections: int = Field(..., description="Peak active connections")
    authenticated_connections: int = Field(..., description="Successfully authenticated connections")
    failed_connections: int = Field(..., description="Failed connection attempts")

    # Performance metrics
    avg_connection_duration_seconds: Optional[float] = Field(None, description="Average connection duration in seconds")
    avg_message_delivery_time_ms: Optional[float] = Field(None, description="Average message delivery time in milliseconds")
    total_messages_sent: int = Field(..., description="Total messages sent")
    total_bytes_transferred: int = Field(..., description="Total bytes transferred")

    # Error metrics
    total_errors: int = Field(..., description="Total errors encountered")
    error_rate_percent: Optional[float] = Field(None, description="Error rate as percentage")


# Real-time Event Schemas for specific use cases

class BookingEventData(WebSocketBaseSchema):
    """Schema for booking-related event data."""

    booking_id: int = Field(..., gt=0, description="Booking ID")
    customer_id: int = Field(..., gt=0, description="Customer ID")
    vendor_id: int = Field(..., gt=0, description="Vendor ID")
    status: str = Field(..., description="Booking status")
    booking_reference: str = Field(..., description="Booking reference number")

    # Optional fields for different event types
    previous_status: Optional[str] = Field(None, description="Previous booking status")
    vendor_notes: Optional[str] = Field(None, description="Vendor notes")
    customer_notes: Optional[str] = Field(None, description="Customer notes")

    # Timestamps
    event_timestamp: datetime = Field(
        default=None,
        description="Event timestamp"
    )


class MessageEventData(WebSocketBaseSchema):
    """Schema for message-related event data."""

    message_id: int = Field(..., gt=0, description="Message ID")
    booking_id: int = Field(..., gt=0, description="Booking ID")
    sender_id: int = Field(..., gt=0, description="Sender user ID")
    recipient_id: int = Field(..., gt=0, description="Recipient user ID")
    content: str = Field(..., min_length=1, max_length=2000, description="Message content")

    # Optional fields
    thread_id: Optional[str] = Field(None, description="Thread identifier")
    message_type: Optional[str] = Field("text", description="Message type")

    # Timestamps
    sent_at: datetime = Field(
        default=None,
        description="Message sent timestamp"
    )


class ReviewEventData(WebSocketBaseSchema):
    """Schema for review-related event data."""

    review_id: int = Field(..., gt=0, description="Review ID")
    booking_id: int = Field(..., gt=0, description="Booking ID")
    customer_id: int = Field(..., gt=0, description="Customer ID")
    vendor_id: int = Field(..., gt=0, description="Vendor ID")
    rating: int = Field(..., ge=1, le=5, description="Review rating")

    # Optional fields
    title: Optional[str] = Field(None, description="Review title")
    content: Optional[str] = Field(None, description="Review content")
    response_content: Optional[str] = Field(None, description="Vendor response content")

    # Timestamps
    created_at: datetime = Field(
        default=None,
        description="Review creation timestamp"
    )


# ============================================================================
# Enhanced Real-time Communication Schemas for Task 6.1.1
# ============================================================================

class RoomTypeEnum(str, Enum):
    """WebSocket room type enumeration."""
    BOOKING = "booking"
    GROUP = "group"
    BROADCAST = "broadcast"
    SYSTEM = "system"


class ParticipantRoleEnum(str, Enum):
    """Room participant role enumeration."""
    OWNER = "owner"
    MODERATOR = "moderator"
    PARTICIPANT = "participant"


class WebSocketRoomCreate(WebSocketBaseSchema):
    """Schema for creating WebSocket rooms."""

    room_id: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Unique room identifier",
        example="booking_room_123"
    )

    room_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Human-readable room name",
        example="Booking #123 Communication"
    )

    room_type: RoomTypeEnum = Field(
        ...,
        description="Room type",
        example=RoomTypeEnum.BOOKING
    )

    is_private: bool = Field(
        True,
        description="Whether room is private"
    )

    max_participants: int = Field(
        100,
        gt=0,
        le=1000,
        description="Maximum number of participants"
    )

    booking_id: Optional[int] = Field(
        None,
        gt=0,
        description="Associated booking ID for booking rooms"
    )

    owner_id: Optional[int] = Field(
        None,
        gt=0,
        description="Room owner/creator ID"
    )

    room_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional room configuration and metadata"
    )


class WebSocketRoomUpdate(WebSocketBaseSchema):
    """Schema for updating WebSocket rooms."""

    room_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="Human-readable room name"
    )

    is_private: Optional[bool] = Field(
        None,
        description="Whether room is private"
    )

    max_participants: Optional[int] = Field(
        None,
        gt=0,
        le=1000,
        description="Maximum number of participants"
    )

    room_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional room configuration and metadata"
    )


class WebSocketRoomResponse(WebSocketBaseSchema):
    """Schema for WebSocket room responses."""

    id: int = Field(..., description="Room database ID")
    room_id: str = Field(..., description="Unique room identifier")
    room_name: str = Field(..., description="Human-readable room name")
    room_type: RoomTypeEnum = Field(..., description="Room type")

    # Room configuration
    is_active: bool = Field(..., description="Whether room is active")
    is_private: bool = Field(..., description="Whether room is private")
    max_participants: int = Field(..., description="Maximum number of participants")

    # Associated entities
    booking_id: Optional[int] = Field(None, description="Associated booking ID")
    owner_id: Optional[int] = Field(None, description="Room owner/creator ID")

    # Room lifecycle
    created_at: datetime = Field(..., description="Room creation timestamp")
    closed_at: Optional[datetime] = Field(None, description="Room closure timestamp")
    last_activity_at: datetime = Field(..., description="Last activity in room")

    # Room statistics
    participant_count: int = Field(..., description="Current number of participants")
    total_messages: int = Field(..., description="Total messages sent in room")

    # Metadata
    room_metadata: Optional[Dict[str, Any]] = Field(None, description="Room metadata")

    # Computed properties
    is_full: bool = Field(..., description="Whether room is at capacity")
    is_open: bool = Field(..., description="Whether room is open for new participants")


class WebSocketRoomParticipantCreate(WebSocketBaseSchema):
    """Schema for creating room participants."""

    room_id: int = Field(
        ...,
        gt=0,
        description="Room ID"
    )

    user_id: int = Field(
        ...,
        gt=0,
        description="Participant user ID"
    )

    connection_id: Optional[int] = Field(
        None,
        gt=0,
        description="Active WebSocket connection ID"
    )

    role: ParticipantRoleEnum = Field(
        ParticipantRoleEnum.PARTICIPANT,
        description="Participant role"
    )

    participant_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional participant metadata"
    )


class WebSocketRoomParticipantResponse(WebSocketBaseSchema):
    """Schema for room participant responses."""

    id: int = Field(..., description="Participant database ID")
    room_id: int = Field(..., description="Room ID")
    user_id: int = Field(..., description="Participant user ID")
    connection_id: Optional[int] = Field(None, description="Active WebSocket connection ID")

    # Participant status
    is_active: bool = Field(..., description="Whether participant is active in room")
    role: ParticipantRoleEnum = Field(..., description="Participant role")

    # Participation lifecycle
    joined_at: datetime = Field(..., description="Room join timestamp")
    left_at: Optional[datetime] = Field(None, description="Room leave timestamp")
    last_seen_at: datetime = Field(..., description="Last activity timestamp")

    # Metadata
    participant_metadata: Optional[Dict[str, Any]] = Field(None, description="Participant metadata")


# Real-time Event Schemas

class ConversationEventData(WebSocketBaseSchema):
    """Schema for conversation-related event data."""

    conversation_id: str = Field(..., description="Conversation identifier")
    booking_id: int = Field(..., gt=0, description="Associated booking ID")
    participants: List[int] = Field(..., description="List of participant user IDs")

    # Event-specific data
    event_data: Optional[Dict[str, Any]] = Field(None, description="Additional event data")

    # Timestamps
    timestamp: datetime = Field(
        default=None,
        description="Event timestamp"
    )


class RealTimeNotificationData(WebSocketBaseSchema):
    """Schema for real-time notification data."""

    notification_id: str = Field(..., description="Notification identifier")
    notification_type: str = Field(..., description="Type of notification")
    title: str = Field(..., min_length=1, max_length=200, description="Notification title")
    message: str = Field(..., min_length=1, max_length=1000, description="Notification message")

    # Targeting
    target_user_ids: List[int] = Field(..., description="Target user IDs")
    priority: EventPriorityEnum = Field(EventPriorityEnum.NORMAL, description="Notification priority")

    # Additional data
    action_url: Optional[str] = Field(None, description="Action URL for notification")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional notification metadata")

    # Timestamps
    created_at: datetime = Field(
        default=None,
        description="Notification creation timestamp"
    )

    expires_at: Optional[datetime] = Field(None, description="Notification expiration timestamp")
