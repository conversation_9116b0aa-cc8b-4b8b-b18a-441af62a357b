"""
Unit tests for availability service.

This module provides comprehensive unit tests for AvailabilityService:
- Vendor availability configuration management with validation
- Real-time availability checking with conflict detection
- Slot generation from recurring patterns with performance optimization
- Booking system integration with capacity management
- Exception handling and pattern override management

Implements Task 4.1.2 Phase 6 requirements with >85% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, time, timedelta, timezone
from decimal import Decimal
from typing import List, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.availability_service import AvailabilityService
from app.schemas.availability_schemas import (
    VendorAvailabilityCreateSchema, VendorAvailabilityResponseSchema,
    AvailabilityCheckResponseSchema, BulkSlotResponseSchema
)
from app.services.base import ValidationError, NotFoundError, ConflictError


class TestAvailabilityService:
    """Test cases for AvailabilityService."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = AsyncMock(spec=AsyncSession)
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        return session
    
    @pytest.fixture
    def availability_service(self, mock_db_session):
        """Create AvailabilityService instance with mocked dependencies."""
        with patch('app.services.availability_service.EmailService'), \
             patch('app.services.availability_service.PushNotificationService'), \
             patch('app.services.availability_service.VendorAvailabilityRepository'), \
             patch('app.services.availability_service.RecurringAvailabilityRepository'), \
             patch('app.services.availability_service.AvailabilitySlotRepository'), \
             patch('app.services.availability_service.AvailabilityExceptionRepository'), \
             patch('app.services.availability_service.BookingRepository'):
            
            service = AvailabilityService(mock_db_session)
            
            # Mock repositories
            service.repository = AsyncMock()
            service.recurring_repository = AsyncMock()
            service.slot_repository = AsyncMock()
            service.exception_repository = AsyncMock()
            service.booking_repository = AsyncMock()
            
            return service
    
    @pytest.fixture
    def vendor_availability_create_data(self):
        """Sample vendor availability creation data."""
        return VendorAvailabilityCreateSchema(
            timezone="America/New_York",
            advance_booking_days=90,
            min_booking_notice_hours=24,
            max_booking_notice_days=30,
            default_slot_duration_minutes=60,
            buffer_time_minutes=15,
            earliest_booking_time=time(9, 0),
            latest_booking_time=time(17, 0)
        )
    
    @pytest.fixture
    def vendor_availability_response(self):
        """Sample vendor availability response."""
        return VendorAvailabilityResponseSchema(
            id=1,
            vendor_id=123,
            service_id=None,
            timezone="America/New_York",
            advance_booking_days=90,
            min_booking_notice_hours=24,
            max_booking_notice_days=30,
            default_slot_duration_minutes=60,
            buffer_time_minutes=15,
            earliest_booking_time=time(9, 0),
            latest_booking_time=time(17, 0),
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
    
    @pytest.mark.asyncio
    async def test_create_vendor_availability_success(
        self, availability_service, vendor_availability_create_data, vendor_availability_response
    ):
        """Test successful vendor availability creation."""
        vendor_id = 123
        
        # Mock repository responses
        availability_service.repository.get_by_vendor_and_service.return_value = None
        availability_service.repository.create.return_value = MagicMock()
        
        with patch.object(availability_service, '_notify_availability_created'), \
             patch.object(availability_service, '_clear_vendor_cache'):
            
            # Execute method
            result = await availability_service.create_vendor_availability(
                availability_data=vendor_availability_create_data,
                vendor_id=vendor_id
            )
            
            # Assertions
            availability_service.repository.get_by_vendor_and_service.assert_called_once_with(
                vendor_id=vendor_id,
                service_id=vendor_availability_create_data.service_id
            )
            availability_service.repository.create.assert_called_once()
            assert isinstance(result, VendorAvailabilityResponseSchema)
    
    @pytest.mark.asyncio
    async def test_create_vendor_availability_conflict(
        self, availability_service, vendor_availability_create_data
    ):
        """Test vendor availability creation conflict."""
        vendor_id = 123
        
        # Mock existing availability
        existing_availability = MagicMock()
        availability_service.repository.get_by_vendor_and_service.return_value = existing_availability
        
        # Execute method and expect conflict error
        with pytest.raises(ConflictError) as exc_info:
            await availability_service.create_vendor_availability(
                availability_data=vendor_availability_create_data,
                vendor_id=vendor_id
            )
        
        assert "already exists" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_vendor_availability_success(
        self, availability_service, vendor_availability_response
    ):
        """Test successful vendor availability retrieval."""
        vendor_id = 123
        service_id = None
        
        # Mock repository response
        availability_service.repository.get_by_vendor_and_service.return_value = MagicMock()
        
        # Execute method
        result = await availability_service.get_vendor_availability(
            vendor_id=vendor_id,
            service_id=service_id
        )
        
        # Assertions
        availability_service.repository.get_by_vendor_and_service.assert_called_once_with(
            vendor_id=vendor_id,
            service_id=service_id
        )
        assert isinstance(result, VendorAvailabilityResponseSchema)
    
    @pytest.mark.asyncio
    async def test_get_vendor_availability_not_found(
        self, availability_service
    ):
        """Test vendor availability not found."""
        vendor_id = 999
        service_id = None
        
        # Mock repository response
        availability_service.repository.get_by_vendor_and_service.return_value = None
        
        # Execute method
        result = await availability_service.get_vendor_availability(
            vendor_id=vendor_id,
            service_id=service_id
        )
        
        # Assertions
        assert result is None
    
    @pytest.mark.asyncio
    async def test_check_availability_available(
        self, availability_service, vendor_availability_response
    ):
        """Test availability check - available."""
        vendor_id = 123
        start_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        end_datetime = start_datetime + timedelta(hours=1)
        
        # Mock service responses
        with patch.object(availability_service, 'get_vendor_availability') as mock_get_availability:
            mock_get_availability.return_value = vendor_availability_response
            
            availability_service.slot_repository.check_slot_availability.return_value = (True, [])
            availability_service.slot_repository.check_booking_conflicts.return_value = []
            
            # Execute method
            result = await availability_service.check_availability(
                vendor_id=vendor_id,
                start_datetime=start_datetime,
                end_datetime=end_datetime
            )
            
            # Assertions
            assert isinstance(result, AvailabilityCheckResponseSchema)
            assert result.is_available is True
            assert result.reason == "Available"
            assert len(result.conflicting_bookings) == 0
    
    @pytest.mark.asyncio
    async def test_check_availability_not_found(
        self, availability_service
    ):
        """Test availability check - vendor not found."""
        vendor_id = 999
        start_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        end_datetime = start_datetime + timedelta(hours=1)
        
        # Mock service responses
        with patch.object(availability_service, 'get_vendor_availability') as mock_get_availability:
            mock_get_availability.return_value = None
            
            # Execute method and expect error
            with pytest.raises(NotFoundError):
                await availability_service.check_availability(
                    vendor_id=vendor_id,
                    start_datetime=start_datetime,
                    end_datetime=end_datetime
                )
    
    @pytest.mark.asyncio
    async def test_check_availability_validation_error(
        self, availability_service
    ):
        """Test availability check validation errors."""
        vendor_id = 123
        start_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        end_datetime = start_datetime - timedelta(hours=1)  # Invalid: end before start
        
        # Execute method and expect validation error
        with pytest.raises(ValidationError) as exc_info:
            await availability_service.check_availability(
                vendor_id=vendor_id,
                start_datetime=start_datetime,
                end_datetime=end_datetime
            )
        
        assert "start_datetime must be before end_datetime" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_check_availability_past_date(
        self, availability_service
    ):
        """Test availability check for past date."""
        vendor_id = 123
        start_datetime = datetime.now(timezone.utc) - timedelta(hours=1)  # Past date
        end_datetime = start_datetime + timedelta(hours=1)
        
        # Execute method and expect validation error
        with pytest.raises(ValidationError) as exc_info:
            await availability_service.check_availability(
                vendor_id=vendor_id,
                start_datetime=start_datetime,
                end_datetime=end_datetime
            )
        
        assert "Cannot check availability for past dates" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_check_availability_notice_period_violation(
        self, availability_service, vendor_availability_response
    ):
        """Test availability check with notice period violation."""
        vendor_id = 123
        # Request booking in 12 hours (less than 24-hour minimum notice)
        start_datetime = datetime.now(timezone.utc) + timedelta(hours=12)
        end_datetime = start_datetime + timedelta(hours=1)
        
        # Mock service responses
        with patch.object(availability_service, 'get_vendor_availability') as mock_get_availability:
            mock_get_availability.return_value = vendor_availability_response
            
            # Execute method
            result = await availability_service.check_availability(
                vendor_id=vendor_id,
                start_datetime=start_datetime,
                end_datetime=end_datetime
            )
            
            # Assertions
            assert isinstance(result, AvailabilityCheckResponseSchema)
            assert result.is_available is False
            assert "minimum notice period" in result.reason
    
    @pytest.mark.asyncio
    async def test_generate_slots_from_patterns_success(
        self, availability_service
    ):
        """Test successful slot generation from patterns."""
        vendor_availability_id = 1
        start_date = date.today() + timedelta(days=1)
        end_date = start_date + timedelta(days=7)
        
        # Mock repository responses
        mock_patterns = [MagicMock(), MagicMock()]
        availability_service.recurring_repository.get_active_patterns_for_vendor.return_value = mock_patterns
        availability_service.exception_repository.get_exceptions_for_date_range.return_value = []
        availability_service.slot_repository.bulk_create_slots.return_value = (10, [1, 2, 3, 4, 5])
        
        with patch.object(availability_service, '_generate_slots_for_pattern') as mock_generate:
            mock_generate.return_value = [{"slot": "data"}] * 5
            
            # Execute method
            result = await availability_service.generate_slots_from_patterns(
                vendor_availability_id=vendor_availability_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Assertions
            assert isinstance(result, BulkSlotResponseSchema)
            assert result.created_count == 10
            assert len(result.created_slot_ids) == 5
            assert result.performance_metrics["patterns_processed"] == 2
    
    @pytest.mark.asyncio
    async def test_generate_slots_validation_error(
        self, availability_service
    ):
        """Test slot generation validation errors."""
        vendor_availability_id = 1
        start_date = date.today() + timedelta(days=7)
        end_date = start_date - timedelta(days=1)  # Invalid: end before start
        
        # Execute method and expect validation error
        with pytest.raises(ValidationError) as exc_info:
            await availability_service.generate_slots_from_patterns(
                vendor_availability_id=vendor_availability_id,
                start_date=start_date,
                end_date=end_date
            )
        
        assert "start_date must be before end_date" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_reserve_slot_for_booking_success(
        self, availability_service
    ):
        """Test successful slot reservation for booking."""
        vendor_availability_id = 1
        booking_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        duration_hours = 1.0
        booking_id = 123
        
        # Mock repository responses
        mock_slot = MagicMock()
        availability_service.slot_repository.get_available_slots_for_booking.return_value = [mock_slot]
        availability_service.slot_repository.update_slot_booking_count.return_value = mock_slot
        
        # Execute method
        result = await availability_service.reserve_slot_for_booking(
            vendor_availability_id=vendor_availability_id,
            booking_datetime=booking_datetime,
            duration_hours=duration_hours,
            booking_id=booking_id
        )
        
        # Assertions
        assert result is True
        availability_service.slot_repository.get_available_slots_for_booking.assert_called_once()
        availability_service.slot_repository.update_slot_booking_count.assert_called_once_with(
            slot_id=mock_slot.id,
            increment=True,
            count=1
        )
    
    @pytest.mark.asyncio
    async def test_reserve_slot_for_booking_no_slots(
        self, availability_service
    ):
        """Test slot reservation when no slots available."""
        vendor_availability_id = 1
        booking_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        duration_hours = 1.0
        booking_id = 123
        
        # Mock repository responses
        availability_service.slot_repository.get_available_slots_for_booking.return_value = []
        
        # Execute method
        result = await availability_service.reserve_slot_for_booking(
            vendor_availability_id=vendor_availability_id,
            booking_datetime=booking_datetime,
            duration_hours=duration_hours,
            booking_id=booking_id
        )
        
        # Assertions
        assert result is False
    
    @pytest.mark.asyncio
    async def test_release_slot_from_booking_success(
        self, availability_service
    ):
        """Test successful slot release from booking."""
        vendor_availability_id = 1
        booking_datetime = datetime.now(timezone.utc) + timedelta(days=1, hours=10)
        duration_hours = 1.0
        booking_id = 123
        
        # Mock repository responses
        mock_slot = MagicMock()
        mock_slot.id = 1
        mock_slot.date = booking_datetime.date()
        mock_slot.start_time = booking_datetime.time()
        mock_slot.end_time = (booking_datetime + timedelta(hours=1)).time()
        
        availability_service.slot_repository.get_slots_by_date_range.return_value = [mock_slot]
        availability_service.slot_repository.update_slot_booking_count.return_value = mock_slot
        
        # Execute method
        result = await availability_service.release_slot_from_booking(
            vendor_availability_id=vendor_availability_id,
            booking_datetime=booking_datetime,
            duration_hours=duration_hours,
            booking_id=booking_id
        )
        
        # Assertions
        assert result is True
        availability_service.slot_repository.update_slot_booking_count.assert_called_once_with(
            slot_id=mock_slot.id,
            increment=False,
            count=1
        )
    
    def test_pattern_applies_to_date_daily(self, availability_service):
        """Test daily pattern date matching."""
        pattern = MagicMock()
        pattern.pattern_type = "daily"
        
        target_date = date.today() + timedelta(days=1)
        
        result = availability_service._pattern_applies_to_date(pattern, target_date)
        assert result is True
    
    def test_pattern_applies_to_date_weekly(self, availability_service):
        """Test weekly pattern date matching."""
        pattern = MagicMock()
        pattern.pattern_type = "weekly"
        pattern.day_of_week = 1  # Monday
        
        # Find next Monday
        today = date.today()
        days_ahead = 1 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        target_date = today + timedelta(days=days_ahead)
        
        result = availability_service._pattern_applies_to_date(pattern, target_date)
        assert result is True
        
        # Test non-matching day
        target_date = today + timedelta(days=days_ahead + 1)  # Tuesday
        result = availability_service._pattern_applies_to_date(pattern, target_date)
        assert result is False
    
    def test_pattern_applies_to_date_monthly(self, availability_service):
        """Test monthly pattern date matching."""
        pattern = MagicMock()
        pattern.pattern_type = "monthly"
        pattern.day_of_month = 15
        pattern.day_of_week = None
        
        # Test matching day of month
        target_date = date(2025, 3, 15)
        result = availability_service._pattern_applies_to_date(pattern, target_date)
        assert result is True
        
        # Test non-matching day of month
        target_date = date(2025, 3, 16)
        result = availability_service._pattern_applies_to_date(pattern, target_date)
        assert result is False
