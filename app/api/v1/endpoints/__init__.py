"""
API endpoints package for version 1.

This package contains all API endpoint modules organized by functionality:
- auth: Authentication and authorization endpoints
- users: User management endpoints
- vendors: Vendor management endpoints (Phase 3)
- bookings: Booking management endpoints (Phase 4)
- payments: Payment processing endpoints (Phase 4)
- experiences: Experience/service listing endpoints (Phase 3)
- promotions: Promotional campaign endpoints (Phase 5)
- analytics: Analytics and reporting endpoints (Phase 7)
- push_notifications: Push notification management endpoints (Task 2.3.2)
- admin: Administrative endpoints (Phase 7)
- health: Health check and monitoring endpoints
"""

__all__ = []
