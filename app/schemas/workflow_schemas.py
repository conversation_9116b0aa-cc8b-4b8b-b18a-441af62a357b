"""
Workflow management schemas for Culture Connect Backend API.

This module provides comprehensive Pydantic V2 schemas for workflow and job
orchestration including:
- Workflow definition and execution schemas
- Job dependency and scheduling schemas
- Monitoring and alerting configuration schemas
- Performance metrics and execution status schemas
- Error handling and validation schemas

Implements Task 6.2.2 requirements for advanced job scheduling and workflow
orchestration with comprehensive validation and serialization.
"""

from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any, List, Optional
from uuid import UUID
import zoneinfo

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator
from typing_extensions import Annotated

from app.models.workflow_models import (
    WorkflowStatus, ExecutionStatus, DependencyType,
    AlertSeverity, AlertChannel, ScheduleType, NamedSchedule, HolidayCalendar
)


# Type definitions for Pydantic V2
ConstrainedStr = Annotated[str, Field(min_length=1, max_length=255)]
ConstrainedShortStr = Annotated[str, Field(min_length=1, max_length=50)]
ConstrainedMediumStr = Annotated[str, Field(min_length=1, max_length=100)]
PositiveInt = Annotated[int, Field(gt=0)]
NonNegativeInt = Annotated[int, Field(ge=0)]
TaskPriority = Annotated[int, Field(ge=0, le=10)]
SchedulePriority = Annotated[int, Field(ge=1, le=10)]
CronExpression = Annotated[str, Field(min_length=9, max_length=255)]  # "0 0 * * *" minimum
TimezoneStr = Annotated[str, Field(min_length=3, max_length=100)]
PositiveFloat = Annotated[float, Field(gt=0.0)]


# Base configuration for all workflow schemas
class WorkflowBaseSchema(BaseModel):
    """Base schema configuration for workflow models."""
    model_config = ConfigDict(
        from_attributes=True,
        use_enum_values=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None,
            Decimal: lambda v: float(v) if v else None,
            UUID: lambda v: str(v) if v else None,
        }
    )


# Workflow Definition Schemas
class WorkflowDefinitionCreate(WorkflowBaseSchema):
    """Schema for creating workflow definitions."""
    name: ConstrainedStr = Field(..., description="Workflow name")
    description: Optional[str] = Field(None, description="Workflow description")
    version: ConstrainedShortStr = Field(default="1.0.0", description="Workflow version")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Workflow configuration")
    tags: List[str] = Field(default_factory=list, description="Workflow tags")
    workflow_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    team_id: Optional[UUID] = Field(None, description="Team identifier")
    max_execution_time: Optional[PositiveInt] = Field(None, description="Maximum execution time in seconds")
    max_retries: NonNegativeInt = Field(default=3, description="Maximum retry attempts")
    retry_delay: NonNegativeInt = Field(default=60, description="Retry delay in seconds")

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Validate workflow tags."""
        if v and len(v) > 20:
            raise ValueError("Maximum 20 tags allowed")
        return [tag.strip().lower() for tag in v if tag.strip()]

    @field_validator('configuration')
    @classmethod
    def validate_configuration(cls, v):
        """Validate workflow configuration."""
        if not isinstance(v, dict):
            raise ValueError("Configuration must be a dictionary")
        return v


class WorkflowDefinitionUpdate(WorkflowBaseSchema):
    """Schema for updating workflow definitions."""
    name: Optional[ConstrainedStr] = None
    description: Optional[str] = None
    status: Optional[WorkflowStatus] = None
    configuration: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    workflow_metadata: Optional[Dict[str, Any]] = None
    max_execution_time: Optional[PositiveInt] = None
    max_retries: Optional[NonNegativeInt] = None
    retry_delay: Optional[NonNegativeInt] = None


class WorkflowDefinitionResponse(WorkflowBaseSchema):
    """Schema for workflow definition responses."""
    id: UUID
    name: str
    description: Optional[str]
    version: str
    status: WorkflowStatus
    configuration: Dict[str, Any]
    tags: List[str]
    workflow_metadata: Dict[str, Any]
    created_by: Optional[UUID]
    team_id: Optional[UUID]
    max_execution_time: Optional[int]
    max_retries: int
    retry_delay: int
    created_at: datetime
    updated_at: datetime


# Workflow Execution Schemas
class WorkflowExecutionCreate(WorkflowBaseSchema):
    """Schema for creating workflow executions."""
    workflow_definition_id: UUID = Field(..., description="Workflow definition ID")
    execution_context: Dict[str, Any] = Field(default_factory=dict, description="Execution context")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="Input data")
    triggered_by: Optional[str] = Field(None, description="Trigger source")
    trigger_data: Dict[str, Any] = Field(default_factory=dict, description="Trigger data")
    correlation_id: Optional[str] = Field(None, description="Correlation ID for tracking")


class WorkflowExecutionUpdate(WorkflowBaseSchema):
    """Schema for updating workflow executions."""
    status: Optional[ExecutionStatus] = None
    execution_context: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_details: Optional[Dict[str, Any]] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[Decimal] = None


class WorkflowExecutionResponse(WorkflowBaseSchema):
    """Schema for workflow execution responses."""
    id: UUID
    workflow_definition_id: UUID
    status: ExecutionStatus
    execution_context: Dict[str, Any]
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    error_details: Optional[Dict[str, Any]]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    duration_seconds: Optional[Decimal]
    retry_count: int
    last_retry_at: Optional[datetime]
    triggered_by: Optional[str]
    trigger_data: Dict[str, Any]
    correlation_id: Optional[str]
    steps_total: int
    steps_completed: int
    steps_failed: int
    created_at: datetime
    updated_at: datetime


# Job Dependency Schemas
class JobDependencyCreate(WorkflowBaseSchema):
    """Schema for creating job dependencies."""
    parent_job_id: ConstrainedStr = Field(..., description="Parent job ID")
    child_job_id: ConstrainedStr = Field(..., description="Child job ID")
    dependency_type: DependencyType = Field(default=DependencyType.SUCCESS, description="Dependency type")
    workflow_execution_id: Optional[UUID] = Field(None, description="Workflow execution ID")
    condition_expression: Optional[str] = Field(None, description="Conditional expression")
    condition_data: Dict[str, Any] = Field(default_factory=dict, description="Condition data")
    timeout_seconds: Optional[PositiveInt] = Field(None, description="Timeout in seconds")
    max_wait_time: Optional[PositiveInt] = Field(None, description="Maximum wait time")
    dependency_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    @model_validator(mode='after')
    def validate_condition_expression(self):
        """Validate conditional expression for conditional dependencies."""
        if self.dependency_type == DependencyType.CONDITIONAL and not self.condition_expression:
            raise ValueError("Condition expression required for conditional dependencies")
        return self


class JobDependencyResponse(WorkflowBaseSchema):
    """Schema for job dependency responses."""
    id: UUID
    parent_job_id: str
    child_job_id: str
    dependency_type: DependencyType
    workflow_execution_id: Optional[UUID]
    condition_expression: Optional[str]
    condition_data: Dict[str, Any]
    timeout_seconds: Optional[int]
    max_wait_time: Optional[int]
    is_satisfied: bool
    satisfied_at: Optional[datetime]
    evaluation_count: int
    last_evaluation_at: Optional[datetime]
    dependency_metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


# Workflow Step Schemas
class WorkflowStepCreate(WorkflowBaseSchema):
    """Schema for creating workflow steps."""
    workflow_definition_id: UUID = Field(..., description="Workflow definition ID")
    name: ConstrainedStr = Field(..., description="Step name")
    description: Optional[str] = Field(None, description="Step description")
    step_order: NonNegativeInt = Field(..., description="Step execution order")
    task_name: ConstrainedStr = Field(..., description="Celery task name")
    task_configuration: Dict[str, Any] = Field(default_factory=dict, description="Task configuration")
    task_queue: Optional[str] = Field(None, description="Task queue name")
    task_priority: TaskPriority = Field(default=5, description="Task priority (0-10)")
    timeout_seconds: Optional[PositiveInt] = Field(None, description="Task timeout")
    max_retries: NonNegativeInt = Field(default=3, description="Maximum retries")
    retry_delay: NonNegativeInt = Field(default=60, description="Retry delay")
    condition_expression: Optional[str] = Field(None, description="Execution condition")
    skip_on_failure: bool = Field(default=False, description="Skip step on failure")
    continue_on_failure: bool = Field(default=False, description="Continue workflow on step failure")
    depends_on_steps: List[str] = Field(default_factory=list, description="Step dependencies")
    tags: List[str] = Field(default_factory=list, description="Step tags")
    step_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class WorkflowStepUpdate(WorkflowBaseSchema):
    """Schema for updating workflow steps."""
    name: Optional[ConstrainedStr] = None
    description: Optional[str] = None
    step_order: Optional[NonNegativeInt] = None
    task_configuration: Optional[Dict[str, Any]] = None
    task_queue: Optional[str] = None
    task_priority: Optional[TaskPriority] = None
    timeout_seconds: Optional[PositiveInt] = None
    max_retries: Optional[NonNegativeInt] = None
    retry_delay: Optional[NonNegativeInt] = None
    condition_expression: Optional[str] = None
    skip_on_failure: Optional[bool] = None
    continue_on_failure: Optional[bool] = None
    depends_on_steps: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    step_metadata: Optional[Dict[str, Any]] = None


class WorkflowStepResponse(WorkflowBaseSchema):
    """Schema for workflow step responses."""
    id: UUID
    workflow_definition_id: UUID
    name: str
    description: Optional[str]
    step_order: int
    task_name: str
    task_configuration: Dict[str, Any]
    task_queue: Optional[str]
    task_priority: int
    timeout_seconds: Optional[int]
    max_retries: int
    retry_delay: int
    condition_expression: Optional[str]
    skip_on_failure: bool
    continue_on_failure: bool
    depends_on_steps: List[str]
    tags: List[str]
    step_metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


# Advanced Job Schedule Schemas (Enhanced for Phase 3)
class JobScheduleCreate(WorkflowBaseSchema):
    """
    Schema for creating advanced job schedules.

    Enhanced for Task 6.2.2 Phase 3 with:
    - Advanced cron expression support with validation
    - Named schedule patterns (@daily, @weekly, etc.)
    - Comprehensive timezone handling with DST support
    - Business day and holiday calendar integration
    - Schedule conflict detection and resolution
    - Performance optimization for <100ms schedule calculation
    """
    workflow_definition_id: Optional[UUID] = Field(None, description="Workflow definition ID")
    name: ConstrainedStr = Field(..., description="Unique schedule name")
    description: Optional[str] = Field(None, description="Schedule description")

    # Enhanced scheduling configuration
    cron_expression: Optional[CronExpression] = Field(None, description="Cron expression (supports seconds)")
    named_schedule: Optional[NamedSchedule] = Field(None, description="Named schedule pattern")
    timezone: TimezoneStr = Field(default="UTC", description="Timezone for schedule execution")
    interval_seconds: Optional[PositiveInt] = Field(None, description="Interval in seconds")

    # Advanced scheduling features
    schedule_type: ScheduleType = Field(default=ScheduleType.CRON, description="Schedule type")
    business_days_only: bool = Field(default=False, description="Execute only on business days")
    exclude_holidays: bool = Field(default=False, description="Exclude holidays from execution")
    holiday_calendar: Optional[HolidayCalendar] = Field(None, description="Holiday calendar to use")

    # Schedule constraints
    start_date: Optional[datetime] = Field(None, description="Schedule start date")
    end_date: Optional[datetime] = Field(None, description="Schedule end date")
    max_runs: Optional[PositiveInt] = Field(None, description="Maximum number of runs")

    # Conflict resolution and priority
    priority: SchedulePriority = Field(default=5, description="Schedule priority (1-10)")
    allow_overlap: bool = Field(default=False, description="Allow overlapping executions")
    max_concurrent_runs: PositiveInt = Field(default=1, description="Maximum concurrent runs")

    # Missed execution handling
    catchup_missed_runs: bool = Field(default=False, description="Catch up missed runs on restart")
    max_catchup_runs: NonNegativeInt = Field(default=3, description="Maximum catchup runs")

    # Metadata and configuration
    schedule_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    @model_validator(mode='after')
    def validate_schedule_configuration(self):
        """Validate schedule configuration based on schedule type."""
        # Validate schedule expression based on type
        if self.schedule_type == ScheduleType.CRON:
            if not self.cron_expression and not self.named_schedule:
                raise ValueError("Cron expression or named schedule required for cron type")
        elif self.schedule_type == ScheduleType.INTERVAL:
            if not self.interval_seconds:
                raise ValueError("Interval seconds required for interval type")
        elif self.schedule_type == ScheduleType.NAMED:
            if not self.named_schedule:
                raise ValueError("Named schedule required for named type")
        elif self.schedule_type == ScheduleType.ONCE:
            if not self.start_date:
                raise ValueError("Start date required for once type")

        # Validate mutually exclusive configurations
        config_count = sum([
            bool(self.cron_expression),
            bool(self.interval_seconds),
            bool(self.named_schedule and self.schedule_type == ScheduleType.NAMED)
        ])

        if config_count > 1:
            raise ValueError("Only one scheduling method should be specified")

        # Validate holiday calendar requirement
        if self.exclude_holidays and not self.holiday_calendar:
            raise ValueError("Holiday calendar required when exclude_holidays is True")

        # Validate business days with holiday calendar
        if self.business_days_only and self.exclude_holidays and not self.holiday_calendar:
            raise ValueError("Holiday calendar recommended for business days scheduling")

        return self

    @field_validator('cron_expression')
    @classmethod
    def validate_cron_expression(cls, v):
        """Enhanced validation of cron expression format."""
        if v:
            v = v.strip()
            parts = v.split()

            # Support standard cron (5 parts) and extended cron with seconds (6 parts)
            if len(parts) not in [5, 6]:
                raise ValueError("Cron expression must have 5 or 6 parts (minute hour day month weekday [second])")

            # Basic validation of cron parts
            for i, part in enumerate(parts):
                if not part or part.isspace():
                    raise ValueError(f"Cron expression part {i+1} cannot be empty")

                # Allow common cron characters
                valid_chars = set('0123456789*,-/LW#?')
                if not all(c in valid_chars for c in part):
                    raise ValueError(f"Invalid characters in cron expression part {i+1}: {part}")

        return v

    @field_validator('timezone')
    @classmethod
    def validate_timezone(cls, v):
        """Validate timezone string."""
        try:
            zoneinfo.ZoneInfo(v)
        except zoneinfo.ZoneInfoNotFoundError:
            raise ValueError(f"Invalid timezone: {v}")
        return v

    @model_validator(mode='after')
    def validate_date_constraints(self):
        """Validate date constraints and relationships."""
        if self.end_date and self.start_date and self.end_date <= self.start_date:
            raise ValueError("End date must be after start date")

        # Validate start date is not in the past for future schedules
        if self.start_date and self.start_date < datetime.now(timezone.utc):
            # Allow past dates for testing, but warn
            pass

        return self


class JobScheduleUpdate(WorkflowBaseSchema):
    """Schema for updating advanced job schedules."""
    name: Optional[ConstrainedStr] = None
    description: Optional[str] = None

    # Enhanced scheduling configuration
    cron_expression: Optional[CronExpression] = None
    named_schedule: Optional[NamedSchedule] = None
    timezone: Optional[TimezoneStr] = None
    interval_seconds: Optional[PositiveInt] = None

    # Advanced scheduling features
    schedule_type: Optional[ScheduleType] = None
    business_days_only: Optional[bool] = None
    exclude_holidays: Optional[bool] = None
    holiday_calendar: Optional[HolidayCalendar] = None

    # Schedule constraints
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    max_runs: Optional[PositiveInt] = None

    # Status and control
    is_active: Optional[bool] = None

    # Conflict resolution and priority
    priority: Optional[SchedulePriority] = None
    allow_overlap: Optional[bool] = None
    max_concurrent_runs: Optional[PositiveInt] = None

    # Missed execution handling
    catchup_missed_runs: Optional[bool] = None
    max_catchup_runs: Optional[NonNegativeInt] = None

    # Metadata and configuration
    schedule_metadata: Optional[Dict[str, Any]] = None


class JobScheduleResponse(WorkflowBaseSchema):
    """Schema for advanced job schedule responses."""
    id: UUID
    workflow_definition_id: Optional[UUID]
    name: str
    description: Optional[str]

    # Enhanced scheduling configuration
    cron_expression: Optional[str]
    named_schedule: Optional[NamedSchedule]
    timezone: str
    interval_seconds: Optional[int]

    # Advanced scheduling features
    schedule_type: ScheduleType
    business_days_only: bool
    exclude_holidays: bool
    holiday_calendar: Optional[HolidayCalendar]

    # Schedule constraints
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    max_runs: Optional[int]

    # Status and control
    is_active: bool
    last_run_at: Optional[datetime]
    next_run_at: Optional[datetime]
    run_count: int

    # Execution tracking
    success_count: int
    failure_count: int
    last_success_at: Optional[datetime]
    last_failure_at: Optional[datetime]

    # Conflict resolution and priority
    priority: int
    allow_overlap: bool
    max_concurrent_runs: int

    # Missed execution handling
    catchup_missed_runs: bool
    max_catchup_runs: int

    # Performance and monitoring
    average_duration_seconds: Optional[float]
    last_duration_seconds: Optional[float]

    # Metadata and configuration
    schedule_metadata: Dict[str, Any]

    # Audit fields
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: Optional[UUID]
    updated_by: Optional[UUID]


# Advanced Scheduling Schemas (Phase 3 Specific)
class ScheduleConflictCheck(WorkflowBaseSchema):
    """Schema for checking schedule conflicts."""
    schedule_id: Optional[UUID] = Field(None, description="Existing schedule ID to exclude from conflict check")
    cron_expression: Optional[str] = Field(None, description="Cron expression to check")
    named_schedule: Optional[NamedSchedule] = Field(None, description="Named schedule to check")
    interval_seconds: Optional[int] = Field(None, description="Interval to check")
    timezone: str = Field(default="UTC", description="Timezone for conflict check")
    start_date: Optional[datetime] = Field(None, description="Start date for conflict check")
    end_date: Optional[datetime] = Field(None, description="End date for conflict check")
    max_concurrent_runs: int = Field(default=1, description="Maximum concurrent runs allowed")


class ScheduleConflictResult(WorkflowBaseSchema):
    """Schema for schedule conflict check results."""
    has_conflicts: bool = Field(..., description="Whether conflicts were found")
    conflicting_schedules: List[UUID] = Field(default_factory=list, description="IDs of conflicting schedules")
    conflict_details: List[Dict[str, Any]] = Field(default_factory=list, description="Detailed conflict information")
    recommendations: List[str] = Field(default_factory=list, description="Conflict resolution recommendations")


class ScheduleCalculation(WorkflowBaseSchema):
    """Schema for schedule calculation requests."""
    cron_expression: Optional[str] = Field(None, description="Cron expression")
    named_schedule: Optional[NamedSchedule] = Field(None, description="Named schedule")
    interval_seconds: Optional[int] = Field(None, description="Interval in seconds")
    timezone: str = Field(default="UTC", description="Timezone for calculation")
    start_date: Optional[datetime] = Field(None, description="Start date")
    end_date: Optional[datetime] = Field(None, description="End date")
    business_days_only: bool = Field(default=False, description="Business days only")
    exclude_holidays: bool = Field(default=False, description="Exclude holidays")
    holiday_calendar: Optional[HolidayCalendar] = Field(None, description="Holiday calendar")
    max_results: int = Field(default=10, description="Maximum number of next run times to calculate")


class ScheduleCalculationResult(WorkflowBaseSchema):
    """Schema for schedule calculation results."""
    next_run_times: List[datetime] = Field(..., description="Next scheduled run times")
    calculation_time_ms: float = Field(..., description="Time taken for calculation in milliseconds")
    is_valid: bool = Field(..., description="Whether the schedule is valid")
    validation_errors: List[str] = Field(default_factory=list, description="Validation error messages")
    schedule_summary: Dict[str, Any] = Field(default_factory=dict, description="Schedule summary information")


class SchedulePerformanceMetrics(WorkflowBaseSchema):
    """Schema for schedule performance metrics."""
    schedule_id: UUID
    total_runs: int
    successful_runs: int
    failed_runs: int
    average_duration_seconds: Optional[float]
    min_duration_seconds: Optional[float]
    max_duration_seconds: Optional[float]
    success_rate: float
    last_run_at: Optional[datetime]
    next_run_at: Optional[datetime]
    performance_trend: str  # improving, stable, degrading
    reliability_score: float  # 0.0 to 1.0


class BulkScheduleOperation(WorkflowBaseSchema):
    """Schema for bulk schedule operations."""
    operation: str = Field(..., description="Operation type: enable, disable, delete, update")
    schedule_ids: List[UUID] = Field(..., description="Schedule IDs to operate on")
    update_data: Optional[Dict[str, Any]] = Field(None, description="Update data for bulk update operations")
    force: bool = Field(default=False, description="Force operation even if conflicts exist")


class BulkScheduleResult(WorkflowBaseSchema):
    """Schema for bulk schedule operation results."""
    operation: str
    total_schedules: int
    successful_operations: int
    failed_operations: int
    success_details: List[Dict[str, Any]] = Field(default_factory=list)
    failure_details: List[Dict[str, Any]] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)


# Workflow Alert Schemas
class WorkflowAlertCreate(WorkflowBaseSchema):
    """Schema for creating workflow alerts."""
    workflow_definition_id: Optional[UUID] = Field(None, description="Workflow definition ID")
    name: ConstrainedStr = Field(..., description="Alert name")
    description: Optional[str] = Field(None, description="Alert description")
    severity: AlertSeverity = Field(default=AlertSeverity.MEDIUM, description="Alert severity")
    condition_type: ConstrainedMediumStr = Field(..., description="Alert condition type")
    condition_expression: str = Field(..., description="Alert condition expression")
    condition_data: Dict[str, Any] = Field(default_factory=dict, description="Condition data")
    notification_channels: List[str] = Field(..., description="Notification channels")
    notification_config: Dict[str, Any] = Field(default_factory=dict, description="Notification configuration")
    escalation_delay_minutes: Optional[PositiveInt] = Field(None, description="Escalation delay")
    escalation_channels: List[str] = Field(default_factory=list, description="Escalation channels")
    max_escalations: PositiveInt = Field(default=3, description="Maximum escalations")
    alert_data: Dict[str, Any] = Field(default_factory=dict, description="Alert configuration")
    alert_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    @field_validator('notification_channels')
    @classmethod
    def validate_notification_channels(cls, v):
        """Validate notification channels."""
        if not v:
            raise ValueError("At least one notification channel is required")

        valid_channels = [channel.value for channel in AlertChannel]
        for channel in v:
            if channel not in valid_channels:
                raise ValueError(f"Invalid notification channel: {channel}")

        return v

    @field_validator('condition_type')
    @classmethod
    def validate_condition_type(cls, v):
        """Validate condition type."""
        valid_types = ['failure', 'timeout', 'performance', 'custom']
        if v not in valid_types:
            raise ValueError(f"Invalid condition type. Must be one of: {valid_types}")
        return v


class WorkflowAlertUpdate(WorkflowBaseSchema):
    """Schema for updating workflow alerts."""
    name: Optional[ConstrainedStr] = None
    description: Optional[str] = None
    severity: Optional[AlertSeverity] = None
    condition_expression: Optional[str] = None
    condition_data: Optional[Dict[str, Any]] = None
    notification_channels: Optional[List[str]] = None
    notification_config: Optional[Dict[str, Any]] = None
    escalation_delay_minutes: Optional[PositiveInt] = None
    escalation_channels: Optional[List[str]] = None
    max_escalations: Optional[PositiveInt] = None
    is_active: Optional[bool] = None
    is_muted: Optional[bool] = None
    muted_until: Optional[datetime] = None
    alert_data: Optional[Dict[str, Any]] = None
    alert_metadata: Optional[Dict[str, Any]] = None


class WorkflowAlertResponse(WorkflowBaseSchema):
    """Schema for workflow alert responses."""
    id: UUID
    workflow_definition_id: Optional[UUID]
    name: str
    description: Optional[str]
    severity: AlertSeverity
    condition_type: str
    condition_expression: str
    condition_data: Dict[str, Any]
    notification_channels: List[str]
    notification_config: Dict[str, Any]
    escalation_delay_minutes: Optional[int]
    escalation_channels: List[str]
    max_escalations: int
    is_active: bool
    is_muted: bool
    muted_until: Optional[datetime]
    trigger_count: int
    last_triggered_at: Optional[datetime]
    last_escalated_at: Optional[datetime]
    alert_data: Dict[str, Any]
    alert_metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


# Performance and Monitoring Schemas
class WorkflowMetrics(WorkflowBaseSchema):
    """Schema for workflow performance metrics."""
    workflow_definition_id: UUID
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_duration_seconds: Optional[Decimal]
    success_rate: Decimal
    last_execution_at: Optional[datetime]
    next_scheduled_at: Optional[datetime]


class WorkflowExecutionMetrics(WorkflowBaseSchema):
    """Schema for workflow execution metrics."""
    execution_id: UUID
    total_steps: int
    completed_steps: int
    failed_steps: int
    skipped_steps: int
    execution_progress: Decimal
    estimated_completion: Optional[datetime]


# Enhanced Monitoring Schemas for Phase 4
class RealTimeJobStatus(WorkflowBaseSchema):
    """Schema for real-time job status monitoring."""
    job_id: UUID = Field(..., description="Job execution ID")
    workflow_definition_id: UUID = Field(..., description="Workflow definition ID")
    status: ExecutionStatus = Field(..., description="Current execution status")
    progress_percentage: Decimal = Field(..., ge=0, le=100, description="Execution progress (0-100%)")
    current_step: Optional[str] = Field(None, description="Current executing step")
    steps_completed: int = Field(..., ge=0, description="Number of completed steps")
    steps_total: int = Field(..., ge=0, description="Total number of steps")
    started_at: Optional[datetime] = Field(None, description="Execution start time")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    duration_seconds: Optional[Decimal] = Field(None, ge=0, description="Current execution duration")
    resource_usage: Dict[str, Any] = Field(default_factory=dict, description="Resource usage metrics")
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="Performance metrics")
    last_heartbeat: datetime = Field(..., description="Last monitoring heartbeat")


class JobPerformanceMetrics(WorkflowBaseSchema):
    """Schema for job performance metrics collection."""
    job_id: UUID = Field(..., description="Job execution ID")
    workflow_definition_id: UUID = Field(..., description="Workflow definition ID")
    cpu_usage_percent: Optional[Decimal] = Field(None, ge=0, le=100, description="CPU usage percentage")
    memory_usage_mb: Optional[Decimal] = Field(None, ge=0, description="Memory usage in MB")
    disk_io_mb: Optional[Decimal] = Field(None, ge=0, description="Disk I/O in MB")
    network_io_mb: Optional[Decimal] = Field(None, ge=0, description="Network I/O in MB")
    execution_latency_ms: Optional[Decimal] = Field(None, ge=0, description="Execution latency in milliseconds")
    queue_wait_time_ms: Optional[Decimal] = Field(None, ge=0, description="Queue wait time in milliseconds")
    step_execution_times: Dict[str, Decimal] = Field(default_factory=dict, description="Individual step execution times")
    error_count: int = Field(default=0, ge=0, description="Number of errors encountered")
    retry_count: int = Field(default=0, ge=0, description="Number of retries performed")
    throughput_ops_per_second: Optional[Decimal] = Field(None, ge=0, description="Operations per second")
    collected_at: datetime = Field(..., description="Metrics collection timestamp")


class SystemHealthStatus(WorkflowBaseSchema):
    """Schema for system health status monitoring."""
    component_name: str = Field(..., description="System component name")
    status: str = Field(..., description="Health status (healthy/degraded/unhealthy)")
    response_time_ms: Decimal = Field(..., ge=0, description="Component response time in milliseconds")
    availability_percentage: Decimal = Field(..., ge=0, le=100, description="Availability percentage")
    error_rate_percentage: Decimal = Field(..., ge=0, le=100, description="Error rate percentage")
    last_check_at: datetime = Field(..., description="Last health check timestamp")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional health details")
    dependencies: List[str] = Field(default_factory=list, description="Component dependencies")


class AlertTriggerEvent(WorkflowBaseSchema):
    """Schema for alert trigger events."""
    alert_id: UUID = Field(..., description="Alert configuration ID")
    trigger_id: UUID = Field(..., description="Unique trigger event ID")
    workflow_definition_id: Optional[UUID] = Field(None, description="Related workflow definition ID")
    job_execution_id: Optional[UUID] = Field(None, description="Related job execution ID")
    severity: AlertSeverity = Field(..., description="Alert severity level")
    condition_type: str = Field(..., description="Alert condition type")
    condition_met: bool = Field(..., description="Whether alert condition was met")
    trigger_value: Optional[str] = Field(None, description="Value that triggered the alert")
    threshold_value: Optional[str] = Field(None, description="Configured threshold value")
    message: str = Field(..., description="Alert message")
    triggered_at: datetime = Field(..., description="Alert trigger timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Alert resolution timestamp")
    escalation_level: int = Field(default=0, ge=0, description="Current escalation level")
    notification_channels: List[str] = Field(default_factory=list, description="Notification channels used")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional alert metadata")


class AlertDeliveryStatus(WorkflowBaseSchema):
    """Schema for alert delivery status tracking."""
    trigger_id: UUID = Field(..., description="Alert trigger event ID")
    channel: str = Field(..., description="Notification channel")
    delivery_id: Optional[str] = Field(None, description="External delivery ID")
    status: str = Field(..., description="Delivery status (pending/sent/delivered/failed)")
    attempt_count: int = Field(default=1, ge=1, description="Delivery attempt count")
    sent_at: Optional[datetime] = Field(None, description="Delivery sent timestamp")
    delivered_at: Optional[datetime] = Field(None, description="Delivery confirmed timestamp")
    failed_at: Optional[datetime] = Field(None, description="Delivery failure timestamp")
    error_message: Optional[str] = Field(None, description="Delivery error message")
    response_data: Dict[str, Any] = Field(default_factory=dict, description="Delivery response data")
    retry_after: Optional[datetime] = Field(None, description="Next retry timestamp")


class MonitoringDashboardData(WorkflowBaseSchema):
    """Schema for monitoring dashboard data aggregation."""
    time_range_start: datetime = Field(..., description="Dashboard time range start")
    time_range_end: datetime = Field(..., description="Dashboard time range end")
    total_jobs: int = Field(..., ge=0, description="Total jobs in time range")
    successful_jobs: int = Field(..., ge=0, description="Successful jobs count")
    failed_jobs: int = Field(..., ge=0, description="Failed jobs count")
    running_jobs: int = Field(..., ge=0, description="Currently running jobs")
    queued_jobs: int = Field(..., ge=0, description="Queued jobs count")
    average_execution_time_seconds: Optional[Decimal] = Field(None, ge=0, description="Average execution time")
    success_rate_percentage: Decimal = Field(..., ge=0, le=100, description="Success rate percentage")
    throughput_jobs_per_hour: Decimal = Field(..., ge=0, description="Jobs per hour throughput")
    active_alerts: int = Field(..., ge=0, description="Active alerts count")
    system_health_score: Decimal = Field(..., ge=0, le=100, description="Overall system health score")
    top_failing_workflows: List[Dict[str, Any]] = Field(default_factory=list, description="Top failing workflows")
    performance_trends: Dict[str, List[Decimal]] = Field(default_factory=dict, description="Performance trend data")
    resource_utilization: Dict[str, Decimal] = Field(default_factory=dict, description="Resource utilization metrics")


class HealthCheckRequest(WorkflowBaseSchema):
    """Schema for health check requests."""
    components: Optional[List[str]] = Field(None, description="Specific components to check")
    include_dependencies: bool = Field(default=True, description="Include dependency checks")
    timeout_seconds: int = Field(default=30, ge=1, le=300, description="Health check timeout")
    detailed: bool = Field(default=False, description="Include detailed diagnostic information")


class HealthCheckResponse(WorkflowBaseSchema):
    """Schema for health check responses."""
    overall_status: str = Field(..., description="Overall system health status")
    check_timestamp: datetime = Field(..., description="Health check timestamp")
    response_time_ms: Decimal = Field(..., ge=0, description="Total health check response time")
    components: List[SystemHealthStatus] = Field(..., description="Individual component health status")
    system_metrics: Dict[str, Any] = Field(default_factory=dict, description="System resource metrics")
    workflow_metrics: Dict[str, Any] = Field(default_factory=dict, description="Workflow system metrics")
    alerts_summary: Dict[str, int] = Field(default_factory=dict, description="Active alerts summary")
    recommendations: List[str] = Field(default_factory=list, description="Health improvement recommendations")


class JobDependencyStatus(WorkflowBaseSchema):
    """Schema for job dependency status."""
    dependency_id: UUID
    parent_job_id: str
    child_job_id: str
    dependency_type: DependencyType
    is_satisfied: bool
    evaluation_count: int
    last_evaluation_at: Optional[datetime]


# Error and Validation Schemas
class WorkflowError(WorkflowBaseSchema):
    """Schema for workflow error responses."""
    error_code: str
    error_message: str
    error_details: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str]


class ValidationError(WorkflowBaseSchema):
    """Schema for validation error responses."""
    field: str
    message: str
    invalid_value: Any