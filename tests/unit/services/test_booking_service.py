"""
Unit tests for booking service.

This module provides comprehensive unit tests for booking service functionality:
- BookingService: Business logic and workflow management
- BookingCommunicationService: Communication workflow management
- BookingModificationService: Modification workflow management
- Integration with email and push notification services
- Error handling and validation

Implements Task 4.1.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, date, timedelta
from decimal import Decimal
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.booking_service import BookingService
from app.services.booking_communication_service import (
    BookingCommunicationService, BookingModificationService
)
from app.services.base import ValidationError, NotFoundError, ConflictError, ServiceError
from app.models.booking import (
    Booking, BookingCommunication, BookingModification,
    BookingStatus, VendorResponseType, BookingPriority, CommunicationType, ModificationType
)
from app.models.user import User
from app.models.vendor import Vendor
from app.models.service import Service
from app.schemas.booking_schemas import (
    BookingCreateSchema, BookingUpdateSchema, BookingStatusUpdateSchema,
    VendorResponseSchema, BookingResponseSchema
)
from app.schemas.booking_communication_schemas import (
    BookingCommunicationCreateSchema, BookingModificationCreateSchema
)


class TestBookingService:
    """Test BookingService functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def booking_service(self, mock_db_session):
        """Create booking service with mocked dependencies."""
        service = BookingService(mock_db_session)
        
        # Mock dependent services
        service.email_service = AsyncMock()
        service.push_service = AsyncMock()
        service.user_repository = AsyncMock()
        service.vendor_repository = AsyncMock()
        service.communication_repository = AsyncMock()
        service.modification_repository = AsyncMock()
        
        return service

    @pytest.fixture
    def sample_booking_create_data(self):
        """Sample booking creation data."""
        return {
            "service_id": 123,
            "booking_date": date(2025, 2, 15),
            "booking_time": datetime(2025, 2, 15, 14, 30).time(),
            "duration_hours": Decimal("2.5"),
            "participant_count": 2,
            "special_requirements": "Vegetarian meal required"
        }

    @pytest.fixture
    def sample_booking(self):
        """Sample booking instance."""
        booking = Booking(
            id=123,
            uuid=uuid4(),
            customer_id=1,
            vendor_id=2,
            service_id=3,
            booking_reference="BK-2025-001234",
            booking_date=date(2025, 2, 15),
            participant_count=2,
            status=BookingStatus.PENDING,
            base_price=Decimal("150.00"),
            total_amount=Decimal("150.00"),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        return booking

    @pytest.fixture
    def sample_user(self):
        """Sample user instance."""
        return User(
            id=1,
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            is_active=True
        )

    @pytest.fixture
    def sample_vendor(self):
        """Sample vendor instance."""
        return Vendor(
            id=2,
            user_id=3,
            business_name="Test Vendor",
            is_active=True
        )

    @pytest.fixture
    def sample_service(self):
        """Sample service instance."""
        return Service(
            id=3,
            vendor_id=2,
            name="Test Service",
            base_price=Decimal("150.00"),
            pricing_model="fixed",
            is_active=True
        )

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, booking_service, sample_user, sample_vendor, sample_service):
        """Test successful booking creation data validation."""
        booking_service.user_repository.get.return_value = sample_user
        booking_service.vendor_repository.get.return_value = sample_vendor
        
        # Mock service repository
        with patch('app.repositories.service_repository.ServiceRepository') as mock_service_repo:
            mock_service_repo.return_value.get.return_value = sample_service
            
            # Mock pricing calculation
            with patch.object(booking_service, '_calculate_booking_pricing', return_value={
                "base_price": Decimal("150.00"),
                "total_amount": Decimal("150.00"),
                "currency": "NGN"
            }):
                data = {
                    "customer_id": 1,
                    "vendor_id": 2,
                    "service_id": 3,
                    "booking_date": date(2025, 2, 15)
                }

                result = await booking_service.validate_create_data(data)

                assert result["customer_id"] == 1
                assert result["vendor_id"] == 2
                assert result["service_id"] == 3
                assert result["base_price"] == Decimal("150.00")
                assert result["total_amount"] == Decimal("150.00")
                assert "vendor_response_deadline" in result

    @pytest.mark.asyncio
    async def test_validate_create_data_customer_not_found(self, booking_service):
        """Test validation failure when customer not found."""
        booking_service.user_repository.get.return_value = None

        data = {
            "customer_id": 999,
            "vendor_id": 2,
            "service_id": 3,
            "booking_date": date(2025, 2, 15)
        }

        with pytest.raises(ValidationError) as exc_info:
            await booking_service.validate_create_data(data)
        
        assert "Customer not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_vendor_not_found(self, booking_service, sample_user):
        """Test validation failure when vendor not found."""
        booking_service.user_repository.get.return_value = sample_user
        booking_service.vendor_repository.get.return_value = None

        data = {
            "customer_id": 1,
            "vendor_id": 999,
            "service_id": 3,
            "booking_date": date(2025, 2, 15)
        }

        with pytest.raises(ValidationError) as exc_info:
            await booking_service.validate_create_data(data)
        
        assert "Vendor not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_vendor_inactive(self, booking_service, sample_user, sample_vendor):
        """Test validation failure when vendor is inactive."""
        sample_vendor.is_active = False
        booking_service.user_repository.get.return_value = sample_user
        booking_service.vendor_repository.get.return_value = sample_vendor

        data = {
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_date": date(2025, 2, 15)
        }

        with pytest.raises(ValidationError) as exc_info:
            await booking_service.validate_create_data(data)
        
        assert "Vendor is not active" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_create_data_past_date(self, booking_service, sample_user, sample_vendor):
        """Test validation failure for past booking date."""
        booking_service.user_repository.get.return_value = sample_user
        booking_service.vendor_repository.get.return_value = sample_vendor

        yesterday = date.today() - timedelta(days=1)
        data = {
            "customer_id": 1,
            "vendor_id": 2,
            "service_id": 3,
            "booking_date": yesterday
        }

        with pytest.raises(ValidationError) as exc_info:
            await booking_service.validate_create_data(data)
        
        assert "Booking date cannot be in the past" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_booking_success(self, booking_service, sample_booking_create_data, sample_booking):
        """Test successful booking creation."""
        booking_create_schema = BookingCreateSchema(**sample_booking_create_data)
        
        # Mock the repository and transaction context
        with patch.object(booking_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_repository.BookingRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_booking.return_value = sample_booking
                
                # Mock availability check
                booking_service.repository = AsyncMock()
                booking_service.repository.check_availability_conflict.return_value = False
                
                # Mock notification sending
                with patch.object(booking_service, '_send_booking_created_notifications'):
                    result = await booking_service.create_booking(
                        customer_id=1,
                        booking_data=booking_create_schema,
                        check_availability=True
                    )

                    assert isinstance(result, BookingResponseSchema)
                    assert result.id == 123
                    assert result.booking_reference == "BK-2025-001234"

    @pytest.mark.asyncio
    async def test_create_booking_availability_conflict(self, booking_service, sample_booking_create_data):
        """Test booking creation with availability conflict."""
        booking_create_schema = BookingCreateSchema(**sample_booking_create_data)
        
        # Mock availability conflict
        booking_service.repository = AsyncMock()
        booking_service.repository.check_availability_conflict.return_value = True

        with pytest.raises(ConflictError) as exc_info:
            await booking_service.create_booking(
                customer_id=1,
                booking_data=booking_create_schema,
                check_availability=True
            )
        
        assert "Booking conflicts with existing reservation" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_booking_status_success(self, booking_service, sample_booking):
        """Test successful booking status update."""
        status_update = BookingStatusUpdateSchema(
            status=BookingStatus.CONFIRMED,
            change_reason="Vendor approved",
            notify_customer=True,
            notify_vendor=False
        )

        with patch.object(booking_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_repository.BookingRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get.return_value = sample_booking
                
                updated_booking = Booking(**sample_booking.__dict__)
                updated_booking.status = BookingStatus.CONFIRMED
                mock_repo.update_booking_status.return_value = updated_booking
                
                # Mock status transition validation
                with patch.object(booking_service, '_validate_status_transition'):
                    # Mock notification sending
                    with patch.object(booking_service, '_send_status_update_notifications'):
                        result = await booking_service.update_booking_status(
                            booking_id=123,
                            status_update=status_update,
                            updated_by=1
                        )

                        assert isinstance(result, BookingResponseSchema)
                        assert result.status == BookingStatus.CONFIRMED

    @pytest.mark.asyncio
    async def test_update_booking_status_not_found(self, booking_service):
        """Test booking status update when booking not found."""
        status_update = BookingStatusUpdateSchema(status=BookingStatus.CONFIRMED)

        with patch.object(booking_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_repository.BookingRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.get.return_value = None

                with pytest.raises(NotFoundError) as exc_info:
                    await booking_service.update_booking_status(
                        booking_id=999,
                        status_update=status_update,
                        updated_by=1
                    )
                
                assert "Booking not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_process_vendor_response_success(self, booking_service, sample_booking):
        """Test successful vendor response processing."""
        vendor_response = VendorResponseSchema(
            response_type=VendorResponseType.APPROVED,
            vendor_notes="We can accommodate all requirements"
        )

        with patch.object(booking_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_repository.BookingRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                
                updated_booking = Booking(**sample_booking.__dict__)
                updated_booking.vendor_response_type = VendorResponseType.APPROVED
                updated_booking.status = BookingStatus.CONFIRMED
                mock_repo.update_vendor_response.return_value = updated_booking
                
                # Mock notification sending
                with patch.object(booking_service, '_send_vendor_response_notifications'):
                    result = await booking_service.process_vendor_response(
                        booking_id=123,
                        vendor_id=2,
                        response_data=vendor_response
                    )

                    assert isinstance(result, BookingResponseSchema)
                    assert result.vendor_response_type == VendorResponseType.APPROVED

    @pytest.mark.asyncio
    async def test_check_availability_success(self, booking_service):
        """Test availability checking."""
        booking_service.repository = AsyncMock()
        booking_service.repository.check_availability_conflict.return_value = False

        result = await booking_service.check_availability(
            service_id=123,
            booking_date=date(2025, 2, 15),
            booking_time=datetime(2025, 2, 15, 14, 30),
            duration_hours=Decimal("2.0")
        )

        assert result is True  # Available (no conflict)

    @pytest.mark.asyncio
    async def test_get_customer_bookings_success(self, booking_service, sample_booking):
        """Test getting customer bookings."""
        from app.repositories.base import QueryResult
        
        mock_result = QueryResult(
            items=[sample_booking],
            total=1,
            page=1,
            per_page=20,
            pages=1,
            has_next=False,
            has_prev=False
        )
        
        booking_service.repository = AsyncMock()
        booking_service.repository.get_bookings_by_customer.return_value = mock_result

        result = await booking_service.get_customer_bookings(
            customer_id=1,
            status_filter=[BookingStatus.PENDING],
            page=1,
            per_page=20
        )

        assert result["total"] == 1
        assert len(result["bookings"]) == 1
        assert result["page"] == 1
        assert result["has_next"] is False

    @pytest.mark.asyncio
    async def test_calculate_booking_pricing(self, booking_service, sample_service):
        """Test booking pricing calculation."""
        result = await booking_service._calculate_booking_pricing(
            service=sample_service,
            participant_count=2,
            duration_hours=Decimal("2.0"),
            special_requirements="Vegetarian meal"
        )

        assert "base_price" in result
        assert "additional_fees" in result
        assert "total_amount" in result
        assert "commission_amount" in result
        assert "vendor_payout" in result
        assert result["currency"] == "NGN"

    @pytest.mark.asyncio
    async def test_validate_status_transition_valid(self, booking_service):
        """Test valid status transition validation."""
        # This should not raise an exception
        await booking_service._validate_status_transition(
            BookingStatus.PENDING,
            BookingStatus.CONFIRMED
        )

    @pytest.mark.asyncio
    async def test_validate_status_transition_invalid(self, booking_service):
        """Test invalid status transition validation."""
        with pytest.raises(ValidationError) as exc_info:
            await booking_service._validate_status_transition(
                BookingStatus.COMPLETED,
                BookingStatus.PENDING
            )
        
        assert "Invalid status transition" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_send_booking_created_notifications(self, booking_service, sample_booking):
        """Test booking created notifications."""
        # Mock the booking with related objects
        sample_booking.service = sample_service = MagicMock()
        sample_service.name = "Test Service"
        sample_booking.customer = sample_customer = MagicMock()
        sample_customer.first_name = "John"
        sample_customer.last_name = "Doe"
        sample_booking.vendor = sample_vendor = MagicMock()
        sample_vendor.user_id = 3

        await booking_service._send_booking_created_notifications(sample_booking)

        # Verify email service calls
        booking_service.email_service.send_booking_confirmation_email.assert_called_once()
        booking_service.email_service.send_booking_notification_email.assert_called_once()
        
        # Verify push service calls
        booking_service.push_service.send_notification_to_user.assert_called_once()

    def test_get_status_message(self, booking_service):
        """Test status message generation."""
        message = booking_service._get_status_message(BookingStatus.CONFIRMED)
        assert "confirmed" in message.lower()

        message = booking_service._get_status_message(BookingStatus.CANCELLED_BY_CUSTOMER)
        assert "cancelled" in message.lower()


class TestBookingCommunicationService:
    """Test BookingCommunicationService functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def communication_service(self, mock_db_session):
        """Create communication service with mocked dependencies."""
        service = BookingCommunicationService(mock_db_session)
        
        # Mock dependent services
        service.email_service = AsyncMock()
        service.push_service = AsyncMock()
        service.booking_repository = AsyncMock()
        
        return service

    @pytest.fixture
    def sample_communication_data(self):
        """Sample communication creation data."""
        return {
            "booking_id": 123,
            "communication_type": CommunicationType.MESSAGE,
            "subject": "Question about booking",
            "message": "Hi, I have a question about the accessibility requirements."
        }

    @pytest.fixture
    def sample_booking(self):
        """Sample booking instance."""
        booking = Booking(
            id=123,
            customer_id=1,
            vendor_id=2,
            booking_reference="BK-2025-001234"
        )
        booking.vendor = MagicMock()
        booking.vendor.user_id = 3
        return booking

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, communication_service, sample_booking):
        """Test successful communication creation data validation."""
        communication_service.booking_repository.get.return_value = sample_booking

        data = {
            "booking_id": 123,
            "sender_id": 1,
            "message": "Test message"
        }

        result = await communication_service.validate_create_data(data)

        assert result["booking_id"] == 123
        assert result["sender_id"] == 1
        assert result["recipient_id"] == 3  # Auto-determined vendor user_id

    @pytest.mark.asyncio
    async def test_validate_create_data_booking_not_found(self, communication_service):
        """Test validation failure when booking not found."""
        communication_service.booking_repository.get.return_value = None

        data = {
            "booking_id": 999,
            "message": "Test message"
        }

        with pytest.raises(ValidationError) as exc_info:
            await communication_service.validate_create_data(data)
        
        assert "Booking not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_send_message_success(self, communication_service):
        """Test successful message sending."""
        message_data = BookingCommunicationCreateSchema(
            booking_id=123,
            communication_type=CommunicationType.MESSAGE,
            message="Test message"
        )

        sample_communication = BookingCommunication(
            id=456,
            booking_id=123,
            sender_id=1,
            communication_type=CommunicationType.MESSAGE,
            message="Test message"
        )

        with patch.object(communication_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_communication_repository.BookingCommunicationRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_communication.return_value = sample_communication
                
                # Mock notification sending
                with patch.object(communication_service, '_send_message_notifications'):
                    result = await communication_service.send_message(
                        booking_id=123,
                        sender_id=1,
                        message_data=message_data
                    )

                    assert result.id == 456
                    assert result.message == "Test message"


class TestBookingModificationService:
    """Test BookingModificationService functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def modification_service(self, mock_db_session):
        """Create modification service with mocked dependencies."""
        service = BookingModificationService(mock_db_session)
        
        # Mock dependent services
        service.email_service = AsyncMock()
        service.push_service = AsyncMock()
        service.booking_repository = AsyncMock()
        
        return service

    @pytest.fixture
    def sample_booking(self):
        """Sample booking instance."""
        booking = Booking(
            id=123,
            customer_id=1,
            vendor_id=2,
            status=BookingStatus.CONFIRMED,
            booking_date=date(2025, 2, 15),
            booking_time=datetime(2025, 2, 15, 14, 30).time()
        )
        booking.can_be_modified = True
        return booking

    @pytest.mark.asyncio
    async def test_validate_create_data_success(self, modification_service, sample_booking):
        """Test successful modification creation data validation."""
        modification_service.booking_repository.get.return_value = sample_booking

        data = {
            "booking_id": 123,
            "modification_type": ModificationType.DATE_CHANGE,
            "requested_changes": {"booking_date": "2025-02-20"}
        }

        # Mock the validation method
        with patch.object(modification_service, '_validate_modification_changes'):
            result = await modification_service.validate_create_data(data)

            assert result["booking_id"] == 123
            assert result["modification_type"] == ModificationType.DATE_CHANGE
            assert result["approval_status"] == "pending"

    @pytest.mark.asyncio
    async def test_validate_create_data_booking_cannot_be_modified(self, modification_service, sample_booking):
        """Test validation failure when booking cannot be modified."""
        sample_booking.can_be_modified = False
        modification_service.booking_repository.get.return_value = sample_booking

        data = {
            "booking_id": 123,
            "modification_type": ModificationType.DATE_CHANGE,
            "requested_changes": {"booking_date": "2025-02-20"}
        }

        with pytest.raises(ValidationError) as exc_info:
            await modification_service.validate_create_data(data)
        
        assert "Booking cannot be modified" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_request_modification_success(self, modification_service, sample_booking):
        """Test successful modification request."""
        modification_data = BookingModificationCreateSchema(
            booking_id=123,
            modification_type=ModificationType.DATE_CHANGE,
            requested_changes={"booking_date": "2025-02-20"}
        )

        sample_modification = BookingModification(
            id=789,
            booking_id=123,
            modification_type=ModificationType.DATE_CHANGE,
            requested_changes={"booking_date": "2025-02-20"},
            original_values={"booking_date": "2025-02-15"}
        )

        with patch.object(modification_service, 'get_transaction_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.booking_communication_repository.BookingModificationRepository') as mock_repo_class:
                mock_repo = AsyncMock()
                mock_repo_class.return_value = mock_repo
                mock_repo.create_modification.return_value = sample_modification
                
                modification_service.booking_repository.get.return_value = sample_booking
                
                # Mock helper methods
                with patch.object(modification_service, '_extract_original_values', return_value={"booking_date": "2025-02-15"}):
                    with patch.object(modification_service, '_send_modification_request_notifications'):
                        result = await modification_service.request_modification(
                            booking_id=123,
                            requested_by=1,
                            modification_data=modification_data
                        )

                        assert result.id == 789
                        assert result.modification_type == ModificationType.DATE_CHANGE
