# Culture Connect Backend - Staging Monitoring Patch
# Comprehensive monitoring configurations for staging environment
# Mirrors production setup with staging-appropriate intervals and resource limits

apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-api
spec:
  template:
    metadata:
      annotations:
        # Prometheus monitoring configuration
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        prometheus.io/interval: "30s"
        prometheus.io/timeout: "10s"

        # Fluentd/logging configuration
        fluentd.org/include: "true"
        fluentd.org/exclude-path: "/health,/metrics"
        fluentd.org/parser: "json"

        # Elastic logging configuration
        co.elastic.logs/enabled: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/multiline.pattern: "^\\d{4}-\\d{2}-\\d{2}"
        co.elastic.logs/multiline.negate: "true"
        co.elastic.logs/multiline.match: "after"

        # Monitoring labels for staging
        monitoring.coreos.com/enabled: "true"
        monitoring.environment: "staging"
        monitoring.tier: "application"
    spec:
      containers:
      - name: api
        env:
        # Core monitoring configuration
        - name: CC_METRICS_ENABLED
          value: "true"
        - name: CC_METRICS_PORT
          value: "9090"
        - name: CC_HEALTH_CHECK_ENABLED
          value: "true"
        - name: CC_HEALTH_CHECK_PATH
          value: "/health"
        - name: CC_HEALTH_CHECK_INTERVAL
          value: "30"
        - name: CC_HEALTH_CHECK_TIMEOUT
          value: "10"
        - name: CC_HEALTH_CHECK_RETRIES
          value: "3"

        # APM (Application Performance Monitoring) configuration
        - name: CC_APM_ENABLED
          value: "true"
        - name: CC_APM_SERVICE_NAME
          value: "culture-connect-staging"
        - name: CC_APM_ENVIRONMENT
          value: "staging"
        - name: CC_APM_SERVER_URL
          value: "http://apm-server:8200"

        # Sentry configuration for staging
        - name: CC_SENTRY_TRACES_SAMPLE_RATE
          value: "0.5"
        - name: CC_SENTRY_PROFILES_SAMPLE_RATE
          value: "0.5"
        - name: CC_SENTRY_ENVIRONMENT
          value: "staging"

        # Logging configuration
        - name: CC_LOG_FORMAT
          value: "json"
        - name: CC_LOG_DATE_FORMAT
          value: "%Y-%m-%d %H:%M:%S"
        - name: CC_LOG_FILE_PATH
          value: "/app/logs/culture_connect_staging.log"
        - name: CC_LOG_MAX_BYTES
          value: "52428800"  # 50MB
        - name: CC_LOG_BACKUP_COUNT
          value: "5"
        - name: CC_LOG_CORRELATION_ID_ENABLED
          value: "true"

        # Performance monitoring
        - name: CC_ENABLE_REQUEST_LOGGING
          value: "true"
        - name: CC_ENABLE_PERFORMANCE_TRACKING
          value: "true"
        - name: CC_ENABLE_SLOW_QUERY_LOGGING
          value: "true"
        - name: CC_SLOW_QUERY_THRESHOLD_MS
          value: "1000"

        # Load testing configuration for staging
        - name: CC_ENABLE_LOAD_TESTING
          value: "true"
        - name: CC_LOAD_TEST_MAX_USERS
          value: "1000"
        - name: CC_LOAD_TEST_DURATION
          value: "300"

        # Staging-specific monitoring features
        - name: CC_ENABLE_PROFILING
          value: "true"
        - name: CC_PROFILING_SAMPLE_RATE
          value: "0.1"
        - name: CC_ENABLE_MEMORY_PROFILING
          value: "true"
        - name: CC_ENABLE_CPU_PROFILING
          value: "true"

      - name: nginx
        env:
        # Nginx monitoring configuration
        - name: NGINX_STATUS_ENABLED
          value: "true"
        - name: NGINX_STATUS_PATH
          value: "/nginx_status"
        - name: NGINX_ACCESS_LOG_FORMAT
          value: "json"
        - name: NGINX_ERROR_LOG_LEVEL
          value: "warn"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-worker
spec:
  template:
    metadata:
      annotations:
        # Prometheus monitoring for Celery workers
        prometheus.io/scrape: "true"
        prometheus.io/port: "9540"
        prometheus.io/path: "/metrics"
        prometheus.io/interval: "60s"
        prometheus.io/timeout: "15s"

        # Fluentd configuration for worker logs
        fluentd.org/include: "true"
        fluentd.org/parser: "json"
        fluentd.org/tag: "celery.worker"

        # Elastic logging for workers
        co.elastic.logs/enabled: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.add_error_key: "true"

        # Monitoring labels
        monitoring.coreos.com/enabled: "true"
        monitoring.environment: "staging"
        monitoring.tier: "worker"
    spec:
      containers:
      - name: celery-worker
        env:
        # Celery monitoring configuration
        - name: CC_CELERY_METRICS_ENABLED
          value: "true"
        - name: CC_CELERY_MONITORING_PORT
          value: "9540"
        - name: CC_CELERY_METRICS_INTERVAL
          value: "30"

        # Celery event monitoring
        - name: CELERY_WORKER_SEND_TASK_EVENTS
          value: "true"
        - name: CELERY_TASK_SEND_SENT_EVENT
          value: "true"
        - name: CELERY_WORKER_PREFETCH_MULTIPLIER
          value: "4"
        - name: CELERY_WORKER_MAX_TASKS_PER_CHILD
          value: "1000"

        # Performance monitoring for workers
        - name: CC_CELERY_TASK_TIME_LIMIT
          value: "300"  # 5 minutes
        - name: CC_CELERY_TASK_SOFT_TIME_LIMIT
          value: "240"  # 4 minutes
        - name: CC_CELERY_WORKER_LOG_LEVEL
          value: "INFO"

        # Health check configuration
        - name: CC_CELERY_HEALTH_CHECK_ENABLED
          value: "true"
        - name: CC_CELERY_HEALTH_CHECK_INTERVAL
          value: "60"

        # Staging-specific worker configuration
        - name: CC_CELERY_ENABLE_TASK_PROFILING
          value: "true"
        - name: CC_CELERY_TASK_PROFILING_SAMPLE_RATE
          value: "0.1"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: culture-connect-celery-beat
spec:
  template:
    metadata:
      annotations:
        # Fluentd configuration for beat scheduler
        fluentd.org/include: "true"
        fluentd.org/parser: "json"
        fluentd.org/tag: "celery.beat"

        # Elastic logging for beat scheduler
        co.elastic.logs/enabled: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.add_error_key: "true"

        # Monitoring labels
        monitoring.coreos.com/enabled: "true"
        monitoring.environment: "staging"
        monitoring.tier: "scheduler"
    spec:
      containers:
      - name: celery-beat
        env:
        # Celery Beat monitoring configuration
        - name: CC_CELERY_BEAT_LOG_LEVEL
          value: "INFO"
        - name: CC_CELERY_BEAT_SCHEDULE_FILENAME
          value: "/app/celerybeat-schedule"
        - name: CC_CELERY_BEAT_MAX_LOOP_INTERVAL
          value: "300"  # 5 minutes

        # Health check configuration for beat
        - name: CC_CELERY_BEAT_HEALTH_CHECK_ENABLED
          value: "true"
        - name: CC_CELERY_BEAT_HEALTH_CHECK_INTERVAL
          value: "120"  # 2 minutes

        # Staging-specific beat configuration
        - name: CC_CELERY_BEAT_ENABLE_MONITORING
          value: "true"
        - name: CC_CELERY_BEAT_SCHEDULE_BACKUP_ENABLED
          value: "true"
        - name: CC_CELERY_BEAT_SCHEDULE_BACKUP_INTERVAL
          value: "3600"  # 1 hour

---
# ServiceMonitor for Prometheus to scrape staging metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: culture-connect-staging-metrics
  namespace: culture-connect-staging
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/environment: staging
    monitoring.coreos.com/enabled: "true"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: culture-connect
      app.kubernetes.io/environment: staging
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    timeout: 10s
    honorLabels: true
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_container_name]
      targetLabel: container
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: namespace
    - replacement: staging
      targetLabel: environment
  - port: celery-metrics
    path: /metrics
    interval: 60s
    timeout: 15s
    honorLabels: true
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_container_name]
      targetLabel: container
    - replacement: staging
      targetLabel: environment

---
# PrometheusRule for staging-specific alerting
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: culture-connect-staging-alerts
  namespace: culture-connect-staging
  labels:
    app.kubernetes.io/name: culture-connect
    app.kubernetes.io/environment: staging
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: culture-connect-staging.alerts
    interval: 30s
    rules:
    # Staging-specific alerts with relaxed thresholds
    - alert: StagingAPIHighErrorRate
      expr: culture_connect:api_error_rate{environment="staging"} > 0.10
      for: 5m
      labels:
        severity: warning
        component: api
        environment: staging
      annotations:
        summary: "High API error rate in staging environment"
        description: "Staging API error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
        runbook_url: "https://docs.cultureconnect.ng/runbooks/staging-api-high-error-rate"

    - alert: StagingAPIHighResponseTime
      expr: culture_connect:api_response_time_p95{environment="staging"} > 1.0
      for: 5m
      labels:
        severity: warning
        component: api
        environment: staging
      annotations:
        summary: "High API response time in staging environment"
        description: "Staging 95th percentile response time is {{ $value }}s"
        runbook_url: "https://docs.cultureconnect.ng/runbooks/staging-api-high-response-time"

    - alert: StagingDatabaseSlowQueries
      expr: culture_connect:database_slow_queries{environment="staging"} > 10
      for: 10m
      labels:
        severity: warning
        component: database
        environment: staging
      annotations:
        summary: "High number of slow queries in staging database"
        description: "Staging database has {{ $value }} slow queries in the last 10 minutes"

    - alert: StagingCeleryTaskFailures
      expr: culture_connect:celery_task_failures{environment="staging"} > 5
      for: 5m
      labels:
        severity: warning
        component: celery
        environment: staging
      annotations:
        summary: "High Celery task failure rate in staging"
        description: "Staging Celery has {{ $value }} task failures in the last 5 minutes"

    - alert: StagingHighMemoryUsage
      expr: culture_connect:memory_usage_percent{environment="staging"} > 85
      for: 10m
      labels:
        severity: warning
        component: infrastructure
        environment: staging
      annotations:
        summary: "High memory usage in staging environment"
        description: "Staging memory usage is {{ $value }}%"

    - alert: StagingHighCPUUsage
      expr: culture_connect:cpu_usage_percent{environment="staging"} > 80
      for: 10m
      labels:
        severity: warning
        component: infrastructure
        environment: staging
      annotations:
        summary: "High CPU usage in staging environment"
        description: "Staging CPU usage is {{ $value }}%"
