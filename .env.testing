# Culture Connect Backend - Testing Environment Configuration
# Geolocation Enhancement System - Testing Settings
#
# This file contains testing-specific configuration for the MaxMind GeoIP
# geolocation enhancement system. Settings are optimized for automated
# testing, CI/CD pipelines, and quality assurance.
#
# Testing Notice:
# - This file is safe to commit to version control
# - Contains no production secrets or sensitive data
# - Uses fast, deterministic settings for testing
# - Includes comprehensive test coverage configuration

# =============================================================================
# MAXMIND GEOIP CONFIGURATION
# =============================================================================

# MaxMind Database Configuration (Testing)
CC_GEOIP_DATABASE_PATH=./tests/data/test_geoip.mmdb
CC_ENABLE_GEOLOCATION_ROUTING=true
CC_GEOLOCATION_FALLBACK_COUNTRY=NG

# MaxMind License Configuration (Testing - Mock)
MAXMIND_LICENSE_KEY=test_maxmind_license_key_for_testing

# Database Update Configuration (Disabled for Testing)
CC_GEOIP_AUTO_UPDATE_ENABLED=false
CC_GEOIP_UPDATE_SCHEDULE=""  # Disabled for testing
CC_GEOIP_UPDATE_WEBHOOK_URL=http://localhost:8888/test/webhooks/geoip

# =============================================================================
# GEOLOCATION PERFORMANCE CONFIGURATION
# =============================================================================

# Performance Thresholds (Fast for Testing)
CC_GEOLOCATION_TIMEOUT_MS=1000   # 1 second (fast)
CC_GEOLOCATION_MAX_RETRIES=1     # Single retry for speed
CC_GEOLOCATION_CIRCUIT_BREAKER_THRESHOLD=3
CC_GEOLOCATION_CIRCUIT_BREAKER_TIMEOUT=10

# Cache Configuration (Testing Optimized)
CC_GEOLOCATION_CACHE_TTL_HOURS=0.1  # 6 minutes (very short)
CC_GEOLOCATION_CACHE_MAX_SIZE=100
CC_GEOLOCATION_CACHE_COMPRESSION=false  # Disabled for speed

# =============================================================================
# REDIS GEOLOCATION CACHING
# =============================================================================

# Redis Cache Configuration (Testing)
CC_GEOLOCATION_REDIS_PREFIX=geo:test:
CC_GEOLOCATION_REDIS_TTL=360  # 6 minutes in seconds
CC_GEOLOCATION_REDIS_COMPRESSION=false  # Disabled for speed
CC_GEOLOCATION_REDIS_MAX_CONNECTIONS=2

# Redis Cluster Configuration (Disabled for Testing)
CC_GEOLOCATION_REDIS_CLUSTER_ENABLED=false
CC_GEOLOCATION_REDIS_CLUSTER_NODES=""

# =============================================================================
# MONITORING AND METRICS
# =============================================================================

# Monitoring Configuration (Testing)
CC_GEOLOCATION_METRICS_ENABLED=true
CC_GEOLOCATION_DETAILED_LOGGING=true   # Enabled for test debugging
CC_GEOLOCATION_PERFORMANCE_TRACKING=false  # Disabled for speed
CC_GEOLOCATION_HEALTH_CHECK_ENABLED=true

# Metrics Collection (Testing)
CC_GEOLOCATION_METRICS_INTERVAL=10  # seconds (very frequent)
CC_GEOLOCATION_METRICS_RETENTION_DAYS=1  # Minimal retention
CC_GEOLOCATION_METRICS_EXPORT_ENABLED=false  # Disabled for testing

# Alerting Configuration (Disabled for Testing)
CC_GEOLOCATION_ALERT_THRESHOLD_ERROR_RATE=1.0   # 100% (disabled)
CC_GEOLOCATION_ALERT_THRESHOLD_LATENCY_MS=10000 # 10s (disabled)
CC_GEOLOCATION_ALERT_THRESHOLD_SUCCESS_RATE=0.0 # 0% (disabled)

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Settings (Disabled for Testing)
CC_GEOLOCATION_SECURE_HEADERS=false
CC_GEOLOCATION_RATE_LIMITING=false
CC_GEOLOCATION_RATE_LIMIT_PER_MINUTE=999999  # Unlimited
CC_GEOLOCATION_IP_WHITELIST=""  # No restrictions

# Encryption Configuration (Disabled for Testing)
CC_GEOLOCATION_ENCRYPT_CACHE=false
CC_GEOLOCATION_ENCRYPTION_KEY_ROTATION_DAYS=999  # Disabled

# =============================================================================
# VPN/PROXY DETECTION CONFIGURATION
# =============================================================================

# VPN Detection Settings (Testing)
CC_VPN_DETECTION_ENABLED=true
CC_VPN_DETECTION_API_KEY=test_vpn_detection_key
CC_VPN_DETECTION_CONFIDENCE_THRESHOLD=0.3  # Low threshold for testing
CC_VPN_DETECTION_TIMEOUT_MS=1000

# ASN Analysis Configuration (Testing)
CC_ENABLE_ASN_ANALYSIS=true
CC_ASN_ANALYSIS_CACHE_TTL_HOURS=1  # Short cache

# Hosting Provider Detection (Testing)
CC_ENABLE_HOSTING_DETECTION=true
CC_HOSTING_DETECTION_CACHE_TTL_HOURS=1  # Short cache

# =============================================================================
# PAYMENT PROVIDER ROUTING
# =============================================================================

# Geolocation-Based Payment Routing (Testing)
CC_PAYMENT_ROUTING_ENABLED=true
CC_PAYMENT_ROUTING_FALLBACK_PROVIDER=paystack
CC_PAYMENT_ROUTING_CONFIDENCE_THRESHOLD=0.3  # Low threshold for testing

# Provider Selection Rules (Testing)
CC_AFRICAN_COUNTRIES_PRIMARY_PROVIDER=paystack
CC_DIASPORA_COUNTRIES_PRIMARY_PROVIDER=stripe
CC_CRYPTO_PAYMENTS_PRIMARY_PROVIDER=busha

# Routing Performance (Testing)
CC_PAYMENT_ROUTING_CACHE_TTL_MINUTES=1  # Very short cache
CC_PAYMENT_ROUTING_MAX_RETRIES=1

# =============================================================================
# A/B TESTING CONFIGURATION
# =============================================================================

# A/B Testing Framework (Testing)
CC_AB_TESTING_ENABLED=true
CC_AB_TESTING_DEFAULT_SPLIT=0.5  # 50/50 split
CC_AB_TESTING_MIN_SAMPLE_SIZE=10  # Very low sample size
CC_AB_TESTING_CONFIDENCE_LEVEL=0.80  # Lower confidence for testing

# Test Configuration (Testing)
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_ENABLED=true
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_SPLIT=0.5
CC_AB_TEST_GEOLOCATION_VS_CURRENCY_DURATION_DAYS=1  # Very short duration

# =============================================================================
# ANALYTICS AND REPORTING
# =============================================================================

# Analytics Configuration (Testing)
CC_GEOLOCATION_ANALYTICS_ENABLED=true
CC_GEOLOCATION_ANALYTICS_RETENTION_DAYS=1  # Minimal retention
CC_GEOLOCATION_ANALYTICS_AGGREGATION_INTERVAL=60  # 1 minute

# Reporting Configuration (Disabled for Testing)
CC_GEOLOCATION_REPORTING_ENABLED=false
CC_GEOLOCATION_DAILY_REPORTS=false
CC_GEOLOCATION_WEEKLY_REPORTS=false
CC_GEOLOCATION_MONTHLY_REPORTS=false

# Dashboard Configuration (Testing)
CC_GEOLOCATION_DASHBOARD_ENABLED=true
CC_GEOLOCATION_DASHBOARD_REFRESH_INTERVAL=10  # 10 seconds (very frequent)
CC_GEOLOCATION_DASHBOARD_RETENTION_DAYS=1

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================

# Backup Configuration (Disabled for Testing)
CC_GEOIP_BACKUP_ENABLED=false
CC_GEOIP_BACKUP_RETENTION_DAYS=1
CC_GEOIP_BACKUP_COMPRESSION=false
CC_GEOIP_BACKUP_ENCRYPTION=false

# Disaster Recovery (Disabled for Testing)
CC_GEOIP_DR_ENABLED=false
CC_GEOIP_DR_BACKUP_LOCATION=./tests/backups/geoip/
CC_GEOIP_DR_REPLICATION_ENABLED=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Levels (Testing - Verbose for Debugging)
CC_GEOLOCATION_LOG_LEVEL=DEBUG
CC_GEOLOCATION_LOG_FORMAT=text  # Human-readable for test debugging
CC_GEOLOCATION_LOG_ROTATION=never  # No rotation for testing
CC_GEOLOCATION_LOG_RETENTION_DAYS=1

# Log Destinations (Testing)
CC_GEOLOCATION_LOG_FILE=./tests/logs/geolocation_test.log
CC_GEOLOCATION_LOG_SYSLOG=false
CC_GEOLOCATION_LOG_REMOTE_ENDPOINT=""

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# External Service Integration (Testing - Fast)
CC_GEOLOCATION_EXTERNAL_APIS_ENABLED=true
CC_GEOLOCATION_EXTERNAL_API_TIMEOUT=2000  # 2 seconds (fast)
CC_GEOLOCATION_EXTERNAL_API_RETRIES=1

# Webhook Configuration (Testing)
CC_GEOLOCATION_WEBHOOKS_ENABLED=true
CC_GEOLOCATION_WEBHOOK_TIMEOUT=1000  # 1 second (fast)
CC_GEOLOCATION_WEBHOOK_RETRIES=1

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Environment Identification
ENVIRONMENT=testing
CC_GEOLOCATION_ENVIRONMENT=testing
CC_GEOLOCATION_VERSION=1.0.0-test
CC_GEOLOCATION_BUILD_ID=test-build-ci

# Resource Limits (Minimal for Testing)
CC_GEOLOCATION_MAX_MEMORY_MB=256  # Lower limit
CC_GEOLOCATION_MAX_CPU_PERCENT=50  # Lower limit
CC_GEOLOCATION_MAX_CONNECTIONS=10

# Health Check Configuration (Testing)
CC_GEOLOCATION_HEALTH_CHECK_INTERVAL=10  # seconds (frequent)
CC_GEOLOCATION_HEALTH_CHECK_TIMEOUT=2    # seconds (fast)
CC_GEOLOCATION_HEALTH_CHECK_RETRIES=1

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles (All Enabled for Testing)
CC_FEATURE_ENHANCED_GEOLOCATION=true
CC_FEATURE_VPN_DETECTION=true
CC_FEATURE_ANALYTICS_DASHBOARD=true
CC_FEATURE_AB_TESTING=true
CC_FEATURE_REAL_TIME_MONITORING=true

# Experimental Features (Enabled for Testing)
CC_FEATURE_EXPERIMENTAL_ROUTING=true
CC_FEATURE_BETA_ANALYTICS=true
CC_FEATURE_DEBUG_MODE=true

# =============================================================================
# TESTING-SPECIFIC CONFIGURATION
# =============================================================================

# Test Mode Configuration
CC_GEOLOCATION_TEST_MODE=true
CC_GEOLOCATION_TEST_DATA_ENABLED=true
CC_GEOLOCATION_TEST_FIXTURES_PATH=./tests/fixtures/geolocation/
CC_GEOLOCATION_TEST_MOCK_EXTERNAL_APIS=true

# Test Database Configuration
CC_GEOLOCATION_TEST_DB_RESET=true
CC_GEOLOCATION_TEST_DB_ISOLATION=true
CC_GEOLOCATION_TEST_DB_TRANSACTIONS=true

# Mock Configuration
CC_GEOLOCATION_MOCK_MODE=false  # Use real logic, not mocks
CC_GEOLOCATION_MOCK_RESPONSES=false
CC_GEOLOCATION_MOCK_DELAYS=false
CC_GEOLOCATION_MOCK_ERRORS=false

# Test Coverage Configuration
CC_GEOLOCATION_TEST_COVERAGE_ENABLED=true
CC_GEOLOCATION_TEST_COVERAGE_THRESHOLD=80  # 80% minimum coverage
CC_GEOLOCATION_TEST_COVERAGE_REPORT=true

# Performance Testing
CC_GEOLOCATION_PERF_TEST_ENABLED=true
CC_GEOLOCATION_PERF_TEST_THRESHOLD_MS=100  # 100ms threshold
CC_GEOLOCATION_PERF_TEST_ITERATIONS=100

# =============================================================================
# CI/CD CONFIGURATION
# =============================================================================

# Continuous Integration
CC_GEOLOCATION_CI_MODE=true
CC_GEOLOCATION_CI_PARALLEL_TESTS=true
CC_GEOLOCATION_CI_FAST_FAIL=true
CC_GEOLOCATION_CI_VERBOSE_OUTPUT=true

# Test Reporting
CC_GEOLOCATION_TEST_REPORTS_ENABLED=true
CC_GEOLOCATION_TEST_REPORTS_FORMAT=junit
CC_GEOLOCATION_TEST_REPORTS_PATH=./tests/reports/

# Quality Gates
CC_GEOLOCATION_QUALITY_GATE_COVERAGE=80
CC_GEOLOCATION_QUALITY_GATE_PERFORMANCE=100  # 100ms
CC_GEOLOCATION_QUALITY_GATE_SUCCESS_RATE=95  # 95%

# =============================================================================
# COMPLIANCE AND PRIVACY
# =============================================================================

# Data Privacy Configuration (Disabled for Testing)
CC_GEOLOCATION_GDPR_COMPLIANCE=false
CC_GEOLOCATION_DATA_RETENTION_DAYS=1
CC_GEOLOCATION_ANONYMIZE_IPS=false
CC_GEOLOCATION_CONSENT_REQUIRED=false

# Audit Configuration (Minimal for Testing)
CC_GEOLOCATION_AUDIT_ENABLED=false
CC_GEOLOCATION_AUDIT_RETENTION_DAYS=1
CC_GEOLOCATION_AUDIT_ENCRYPTION=false

# =============================================================================
# TESTING SETUP NOTES
# =============================================================================

# 1. Test Environment Setup:
#    - Copy this file to .env.test for testing
#    - Create test database: ./tests/data/test_geoip.mmdb
#    - Run: pytest tests/integration/test_geolocation_integration.py
#
# 2. Test Data:
#    - Use test fixtures in ./tests/fixtures/geolocation/
#    - Mock external APIs with CC_GEOLOCATION_TEST_MOCK_EXTERNAL_APIS=true
#    - Reset database between tests with CC_GEOLOCATION_TEST_DB_RESET=true
#
# 3. Performance Testing:
#    - Enable with CC_GEOLOCATION_PERF_TEST_ENABLED=true
#    - Set threshold with CC_GEOLOCATION_PERF_TEST_THRESHOLD_MS=100
#    - Run: pytest tests/performance/test_geolocation_performance.py
#
# 4. Coverage Testing:
#    - Enable with CC_GEOLOCATION_TEST_COVERAGE_ENABLED=true
#    - Set threshold with CC_GEOLOCATION_TEST_COVERAGE_THRESHOLD=80
#    - Generate report with CC_GEOLOCATION_TEST_COVERAGE_REPORT=true
#
# 5. CI/CD Integration:
#    - Enable CI mode with CC_GEOLOCATION_CI_MODE=true
#    - Use parallel testing with CC_GEOLOCATION_CI_PARALLEL_TESTS=true
#    - Generate reports in ./tests/reports/ directory
