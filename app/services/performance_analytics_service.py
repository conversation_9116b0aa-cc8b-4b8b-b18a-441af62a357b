"""
Performance Analytics Service for Culture Connect Backend API.

This module provides comprehensive performance analytics and metrics calculation including:
- Metrics calculation and trend analysis algorithms
- Performance benchmarking and comparative analytics
- Conversion rate optimization and funnel analysis
- Revenue tracking and ROI measurement
- Predictive analytics and forecasting capabilities

Implements Task 3.2.2 requirements for performance analytics tools with
production-grade algorithms, comprehensive metrics, and actionable insights.
"""

import logging
from datetime import datetime, timezone, timedelta, date
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
import statistics

from app.services.base import BaseService, ServiceError, NotFoundError, ValidationError
from app.models.marketplace_optimization import PerformanceMetrics
from app.models.service import Service
from app.repositories.performance_metrics_repository import PerformanceMetricsRepository
from app.repositories.service_repository import ServiceRepository
from app.schemas.marketplace_optimization import PerformanceMetricsCreate, PerformanceMetricsUpdate


logger = logging.getLogger(__name__)


class PerformanceAnalyticsService(BaseService[PerformanceMetrics, PerformanceMetricsRepository]):
    """
    Performance analytics service for comprehensive metrics calculation and analysis.

    Provides production-grade algorithms for:
    - Metrics calculation and trend analysis
    - Performance benchmarking and comparative analytics
    - Conversion rate optimization and funnel analysis
    - Revenue tracking and ROI measurement
    - Predictive analytics and forecasting
    """

    def __init__(self, db: AsyncSession):
        """Initialize performance analytics service."""
        super().__init__(PerformanceMetricsRepository, PerformanceMetrics, db_session=db)
        self.service_repository = ServiceRepository(db)

    async def calculate_service_performance(
        self,
        service_id: int,
        start_date: date,
        end_date: date,
        metric_period: str = "daily"
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive performance metrics for a service.

        Args:
            service_id: Service ID to analyze
            start_date: Start date for analysis
            end_date: End date for analysis
            metric_period: Period type (daily, weekly, monthly)

        Returns:
            Dict[str, Any]: Comprehensive performance analysis

        Raises:
            NotFoundError: If service not found
            ServiceError: If calculation fails
        """
        correlation = self.log_operation_start(
            "calculate_service_performance",
            service_id=service_id,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            metric_period=metric_period
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Get time-series data
            time_series_data = await self.repository.get_time_series_data(
                service_id, start_date, end_date, metric_period
            )

            # Get aggregated metrics
            aggregated_metrics = await self.repository.get_aggregated_metrics(
                service_id, start_date, end_date
            )

            # Calculate performance trends
            trends = self._calculate_performance_trends(time_series_data)

            # Calculate conversion funnel
            conversion_funnel = self._calculate_conversion_funnel(aggregated_metrics)

            # Calculate performance scores
            performance_scores = self._calculate_performance_scores(aggregated_metrics)

            # Get comparative metrics
            comparative_metrics = await self.repository.get_comparative_metrics(
                service_id, days=(end_date - start_date).days
            )

            # Generate insights and recommendations
            insights = self._generate_performance_insights(
                aggregated_metrics, trends, performance_scores
            )

            result = {
                "service_id": service_id,
                "analysis_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "metric_period": metric_period,
                    "total_days": (end_date - start_date).days
                },
                "aggregated_metrics": aggregated_metrics,
                "time_series_data": [
                    {
                        "date": metric.metric_date.isoformat(),
                        "views": metric.listing_views,
                        "unique_views": metric.unique_views,
                        "contacts": metric.contact_clicks,
                        "inquiries": metric.booking_inquiries,
                        "conversion_rate": float(metric.conversion_rate),
                        "revenue": float(metric.revenue_generated)
                    }
                    for metric in time_series_data
                ],
                "trends": trends,
                "conversion_funnel": conversion_funnel,
                "performance_scores": performance_scores,
                "comparative_metrics": comparative_metrics,
                "insights": insights,
                "analysis_date": datetime.now(timezone.utc).isoformat()
            }

            self.log_operation_success(
                correlation,
                f"Calculated performance metrics for service {service_id}"
            )

            return result

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "calculate_service_performance",
                {"service_id": service_id, "start_date": start_date, "end_date": end_date}
            )

    async def get_performance_benchmarks(
        self,
        service_id: int,
        category_id: Optional[int] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get performance benchmarks for a service against market averages.

        Args:
            service_id: Service ID to benchmark
            category_id: Optional category for comparison
            days: Number of days for benchmark analysis

        Returns:
            Dict[str, Any]: Performance benchmarks and comparisons

        Raises:
            NotFoundError: If service not found
            ServiceError: If benchmarking fails
        """
        correlation = self.log_operation_start(
            "get_performance_benchmarks",
            service_id=service_id,
            category_id=category_id,
            days=days
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Get service performance metrics
            end_date = datetime.now(timezone.utc).date()
            start_date = end_date - timedelta(days=days)

            service_metrics = await self.repository.get_aggregated_metrics(
                service_id, start_date, end_date
            )

            # Get comparative metrics
            comparative_metrics = await self.repository.get_comparative_metrics(
                service_id, category_id, days
            )

            # Calculate benchmark scores
            benchmark_scores = self._calculate_benchmark_scores(
                service_metrics, comparative_metrics
            )

            # Get top performers for comparison
            top_performers = await self.repository.get_top_performing_services(
                "conversion_rate", limit=10, start_date=start_date, end_date=end_date
            )

            # Calculate percentile rankings
            percentile_rankings = self._calculate_percentile_rankings(
                service_metrics, comparative_metrics
            )

            result = {
                "service_id": service_id,
                "benchmark_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "service_performance": service_metrics,
                "market_averages": comparative_metrics.get("market_averages", {}),
                "benchmark_scores": benchmark_scores,
                "percentile_rankings": percentile_rankings,
                "top_performers": [
                    {
                        "service_id": performer["service_id"],
                        "metric_value": performer["metric_value"],
                        "total_views": performer["total_views"],
                        "total_revenue": performer["total_revenue"]
                    }
                    for performer in top_performers[:5]
                ],
                "improvement_opportunities": self._identify_improvement_opportunities(
                    service_metrics, comparative_metrics
                ),
                "analysis_date": datetime.now(timezone.utc).isoformat()
            }

            self.log_operation_success(
                correlation,
                f"Generated performance benchmarks for service {service_id}"
            )

            return result

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "get_performance_benchmarks",
                {"service_id": service_id, "category_id": category_id, "days": days}
            )

    async def predict_performance_trends(
        self,
        service_id: int,
        forecast_days: int = 30,
        historical_days: int = 90
    ) -> Dict[str, Any]:
        """
        Predict performance trends using historical data analysis.

        Args:
            service_id: Service ID to predict for
            forecast_days: Number of days to forecast
            historical_days: Number of historical days to analyze

        Returns:
            Dict[str, Any]: Performance predictions and trends

        Raises:
            NotFoundError: If service not found
            ServiceError: If prediction fails
        """
        correlation = self.log_operation_start(
            "predict_performance_trends",
            service_id=service_id,
            forecast_days=forecast_days,
            historical_days=historical_days
        )

        try:
            # Verify service exists
            service = await self.service_repository.get_by_id(service_id)
            if not service:
                raise NotFoundError(f"Service with ID {service_id} not found")

            # Get historical performance trends
            trends = await self.repository.get_performance_trends(service_id, historical_days)

            # Calculate trend predictions
            predictions = self._calculate_trend_predictions(trends, forecast_days)

            # Calculate confidence intervals
            confidence_intervals = self._calculate_confidence_intervals(trends, predictions)

            # Identify seasonal patterns
            seasonal_patterns = self._identify_seasonal_patterns(trends)

            # Generate forecast insights
            forecast_insights = self._generate_forecast_insights(
                predictions, confidence_intervals, seasonal_patterns
            )

            result = {
                "service_id": service_id,
                "forecast_period": {
                    "forecast_days": forecast_days,
                    "historical_days": historical_days,
                    "forecast_start": datetime.now(timezone.utc).date().isoformat(),
                    "forecast_end": (datetime.now(timezone.utc).date() + timedelta(days=forecast_days)).isoformat()
                },
                "historical_trends": trends,
                "predictions": predictions,
                "confidence_intervals": confidence_intervals,
                "seasonal_patterns": seasonal_patterns,
                "forecast_insights": forecast_insights,
                "analysis_date": datetime.now(timezone.utc).isoformat()
            }

            self.log_operation_success(
                correlation,
                f"Generated performance predictions for service {service_id}"
            )

            return result

        except NotFoundError:
            raise
        except Exception as e:
            await self.handle_service_error(
                e, "predict_performance_trends",
                {"service_id": service_id, "forecast_days": forecast_days, "historical_days": historical_days}
            )

    def _calculate_performance_trends(self, time_series_data: List[PerformanceMetrics]) -> Dict[str, Any]:
        """Calculate performance trends from time-series data."""
        if len(time_series_data) < 2:
            return {"trend_direction": "insufficient_data", "trend_strength": 0}

        # Calculate trends for key metrics
        views_trend = self._calculate_metric_trend([m.listing_views for m in time_series_data])
        conversion_trend = self._calculate_metric_trend([float(m.conversion_rate) for m in time_series_data])
        revenue_trend = self._calculate_metric_trend([float(m.revenue_generated) for m in time_series_data])

        return {
            "views_trend": views_trend,
            "conversion_trend": conversion_trend,
            "revenue_trend": revenue_trend,
            "overall_trend": self._calculate_overall_trend([views_trend, conversion_trend, revenue_trend])
        }

    def _calculate_metric_trend(self, values: List[float]) -> Dict[str, Any]:
        """Calculate trend for a specific metric."""
        if len(values) < 2:
            return {"direction": "insufficient_data", "strength": 0, "change_percentage": 0}

        # Simple linear trend calculation
        x_values = list(range(len(values)))

        # Calculate correlation coefficient as trend strength
        if len(values) > 1:
            try:
                correlation = statistics.correlation(x_values, values)
                trend_strength = abs(correlation)
            except statistics.StatisticsError:
                correlation = 0
                trend_strength = 0
        else:
            correlation = 0
            trend_strength = 0

        # Calculate percentage change
        if values[0] != 0:
            change_percentage = ((values[-1] - values[0]) / values[0]) * 100
        else:
            change_percentage = 0

        # Determine trend direction
        if correlation > 0.1:
            direction = "increasing"
        elif correlation < -0.1:
            direction = "decreasing"
        else:
            direction = "stable"

        return {
            "direction": direction,
            "strength": round(trend_strength, 3),
            "change_percentage": round(change_percentage, 2),
            "correlation": round(correlation, 3)
        }

    def _calculate_overall_trend(self, individual_trends: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall trend from individual metric trends."""
        strengths = [trend.get("strength", 0) for trend in individual_trends]
        changes = [trend.get("change_percentage", 0) for trend in individual_trends]

        avg_strength = statistics.mean(strengths) if strengths else 0
        avg_change = statistics.mean(changes) if changes else 0

        if avg_change > 5:
            direction = "improving"
        elif avg_change < -5:
            direction = "declining"
        else:
            direction = "stable"

        return {
            "direction": direction,
            "strength": round(avg_strength, 3),
            "average_change": round(avg_change, 2)
        }

    def _calculate_conversion_funnel(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate conversion funnel metrics."""
        totals = metrics.get("totals", {})

        impressions = totals.get("impressions", 0)
        views = totals.get("views", 0)
        contacts = totals.get("contacts", 0)
        inquiries = totals.get("inquiries", 0)
        bookings = totals.get("bookings", 0)

        funnel = {
            "impressions": impressions,
            "views": views,
            "contacts": contacts,
            "inquiries": inquiries,
            "bookings": bookings,
            "conversion_rates": {
                "impression_to_view": (views / impressions * 100) if impressions > 0 else 0,
                "view_to_contact": (contacts / views * 100) if views > 0 else 0,
                "contact_to_inquiry": (inquiries / contacts * 100) if contacts > 0 else 0,
                "inquiry_to_booking": (bookings / inquiries * 100) if inquiries > 0 else 0,
                "overall_conversion": (bookings / impressions * 100) if impressions > 0 else 0
            }
        }

        # Round conversion rates
        for key, value in funnel["conversion_rates"].items():
            funnel["conversion_rates"][key] = round(value, 2)

        return funnel

    def _calculate_performance_scores(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance scores based on metrics."""
        averages = metrics.get("averages", {})

        # Score conversion rate (0-100 scale)
        conversion_rate = averages.get("conversion_rate", 0)
        conversion_score = min(100, conversion_rate * 10)  # 10% conversion = 100 score

        # Score engagement rate
        engagement_rate = averages.get("engagement_rate", 0)
        engagement_score = min(100, engagement_rate)

        # Score bounce rate (inverted - lower is better)
        bounce_rate = averages.get("bounce_rate", 100)
        bounce_score = max(0, 100 - bounce_rate)

        # Calculate overall performance score
        overall_score = (conversion_score * 0.4 + engagement_score * 0.3 + bounce_score * 0.3)

        return {
            "conversion_score": round(conversion_score, 2),
            "engagement_score": round(engagement_score, 2),
            "bounce_score": round(bounce_score, 2),
            "overall_performance_score": round(overall_score, 2)
        }

    def _calculate_benchmark_scores(
        self,
        service_metrics: Dict[str, Any],
        comparative_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate benchmark scores comparing service to market averages."""
        service_averages = service_metrics.get("averages", {})
        market_averages = comparative_metrics.get("market_averages", {})

        benchmark_scores = {}

        for metric in ["conversion_rate", "engagement_rate", "bounce_rate"]:
            service_value = service_averages.get(metric, 0)
            market_value = market_averages.get(metric, 0)

            if market_value > 0:
                if metric == "bounce_rate":  # Lower is better for bounce rate
                    score = max(0, (market_value - service_value) / market_value * 100)
                else:  # Higher is better for other metrics
                    score = (service_value / market_value) * 100
                benchmark_scores[f"{metric}_benchmark"] = round(score, 2)
            else:
                benchmark_scores[f"{metric}_benchmark"] = 0

        # Calculate overall benchmark score
        scores = [score for score in benchmark_scores.values() if score > 0]
        overall_benchmark = statistics.mean(scores) if scores else 0
        benchmark_scores["overall_benchmark_score"] = round(overall_benchmark, 2)

        return benchmark_scores

    def _calculate_percentile_rankings(
        self,
        service_metrics: Dict[str, Any],
        comparative_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate percentile rankings for service performance."""
        performance_vs_market = comparative_metrics.get("performance_vs_market", {})

        rankings = {}

        # Map percentile ranks to descriptive categories
        percentile_rank = performance_vs_market.get("percentile_rank", "below_median")

        rank_mapping = {
            "top_10_percent": {"percentile": 90, "description": "Top 10%"},
            "top_25_percent": {"percentile": 75, "description": "Top 25%"},
            "above_median": {"percentile": 60, "description": "Above Average"},
            "below_median": {"percentile": 40, "description": "Below Average"}
        }

        rankings["conversion_rate_ranking"] = rank_mapping.get(
            percentile_rank, {"percentile": 40, "description": "Below Average"}
        )

        return rankings

    def _identify_improvement_opportunities(
        self,
        service_metrics: Dict[str, Any],
        comparative_metrics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Identify specific improvement opportunities."""
        opportunities = []

        service_averages = service_metrics.get("averages", {})
        market_averages = comparative_metrics.get("market_averages", {})

        # Check conversion rate
        service_conversion = service_averages.get("conversion_rate", 0)
        market_conversion = market_averages.get("conversion_rate", 0)

        if service_conversion < market_conversion * 0.8:  # 20% below market
            opportunities.append({
                "area": "conversion_rate",
                "current_value": service_conversion,
                "market_average": market_conversion,
                "improvement_potential": round((market_conversion - service_conversion), 2),
                "priority": "high",
                "recommendations": [
                    "Optimize service description and highlights",
                    "Improve pricing strategy",
                    "Enhance service images and media",
                    "Add customer testimonials and reviews"
                ]
            })

        # Check engagement rate
        service_engagement = service_averages.get("engagement_rate", 0)
        market_engagement = market_averages.get("engagement_rate", 0)

        if service_engagement < market_engagement * 0.8:
            opportunities.append({
                "area": "engagement_rate",
                "current_value": service_engagement,
                "market_average": market_engagement,
                "improvement_potential": round((market_engagement - service_engagement), 2),
                "priority": "medium",
                "recommendations": [
                    "Create more engaging content",
                    "Improve response time to inquiries",
                    "Add interactive elements to listings",
                    "Enhance social media presence"
                ]
            })

        # Check bounce rate
        service_bounce = service_averages.get("bounce_rate", 100)
        market_bounce = market_averages.get("bounce_rate", 100)

        if service_bounce > market_bounce * 1.2:  # 20% above market (worse)
            opportunities.append({
                "area": "bounce_rate",
                "current_value": service_bounce,
                "market_average": market_bounce,
                "improvement_potential": round((service_bounce - market_bounce), 2),
                "priority": "high",
                "recommendations": [
                    "Improve page loading speed",
                    "Enhance content quality and relevance",
                    "Optimize mobile experience",
                    "Improve navigation and user experience"
                ]
            })

        return opportunities

    def _generate_performance_insights(
        self,
        metrics: Dict[str, Any],
        trends: Dict[str, Any],
        scores: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate actionable performance insights."""
        insights = []

        # Analyze overall performance
        overall_score = scores.get("overall_performance_score", 0)
        if overall_score >= 80:
            insights.append({
                "type": "positive",
                "category": "overall_performance",
                "message": "Excellent overall performance with strong conversion and engagement metrics",
                "action": "Continue current strategies and consider scaling successful approaches"
            })
        elif overall_score >= 60:
            insights.append({
                "type": "neutral",
                "category": "overall_performance",
                "message": "Good performance with room for optimization",
                "action": "Focus on improving conversion rate and reducing bounce rate"
            })
        else:
            insights.append({
                "type": "warning",
                "category": "overall_performance",
                "message": "Performance below market standards - immediate optimization needed",
                "action": "Implement comprehensive optimization strategy across all metrics"
            })

        # Analyze trends
        overall_trend = trends.get("overall_trend", {})
        trend_direction = overall_trend.get("direction", "stable")

        if trend_direction == "improving":
            insights.append({
                "type": "positive",
                "category": "trends",
                "message": "Performance is trending upward across key metrics",
                "action": "Maintain current strategies and monitor for continued improvement"
            })
        elif trend_direction == "declining":
            insights.append({
                "type": "warning",
                "category": "trends",
                "message": "Performance is declining - intervention needed",
                "action": "Analyze recent changes and implement corrective measures"
            })

        # Analyze conversion funnel
        conversion_score = scores.get("conversion_score", 0)
        if conversion_score < 50:
            insights.append({
                "type": "opportunity",
                "category": "conversion",
                "message": "Low conversion rate indicates optimization opportunities",
                "action": "Focus on improving service presentation, pricing, and customer trust signals"
            })

        return insights

    def _calculate_trend_predictions(
        self,
        trends: Dict[str, List[Tuple[date, float]]],
        forecast_days: int
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Calculate trend predictions using simple linear extrapolation."""
        predictions = {}

        for metric, trend_data in trends.items():
            if len(trend_data) < 2:
                predictions[metric] = []
                continue

            # Extract values and calculate simple linear trend
            values = [point[1] for point in trend_data]

            if len(values) >= 2:
                # Simple linear trend calculation
                recent_values = values[-7:]  # Use last 7 data points
                if len(recent_values) >= 2:
                    trend_slope = (recent_values[-1] - recent_values[0]) / len(recent_values)
                else:
                    trend_slope = 0

                # Generate predictions
                last_value = values[-1]
                last_date = trend_data[-1][0]

                metric_predictions = []
                for i in range(1, forecast_days + 1):
                    predicted_date = last_date + timedelta(days=i)
                    predicted_value = max(0, last_value + (trend_slope * i))

                    metric_predictions.append({
                        "date": predicted_date.isoformat(),
                        "predicted_value": round(predicted_value, 2),
                        "confidence": max(0.3, 1.0 - (i / forecast_days) * 0.7)  # Decreasing confidence
                    })

                predictions[metric] = metric_predictions
            else:
                predictions[metric] = []

        return predictions

    def _calculate_confidence_intervals(
        self,
        trends: Dict[str, List[Tuple[date, float]]],
        predictions: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate confidence intervals for predictions."""
        confidence_intervals = {}

        for metric, trend_data in trends.items():
            if len(trend_data) < 3:
                confidence_intervals[metric] = {"lower_bound": 0, "upper_bound": 0}
                continue

            values = [point[1] for point in trend_data]

            # Calculate standard deviation
            try:
                std_dev = statistics.stdev(values)
                mean_value = statistics.mean(values)

                # 95% confidence interval (approximately 2 standard deviations)
                confidence_intervals[metric] = {
                    "lower_bound": round(max(0, mean_value - 2 * std_dev), 2),
                    "upper_bound": round(mean_value + 2 * std_dev, 2),
                    "standard_deviation": round(std_dev, 2)
                }
            except statistics.StatisticsError:
                confidence_intervals[metric] = {"lower_bound": 0, "upper_bound": 0}

        return confidence_intervals

    def _identify_seasonal_patterns(
        self,
        trends: Dict[str, List[Tuple[date, float]]]
    ) -> Dict[str, Any]:
        """Identify seasonal patterns in performance data."""
        seasonal_patterns = {
            "has_seasonal_pattern": False,
            "peak_periods": [],
            "low_periods": [],
            "pattern_strength": 0
        }

        # This is a simplified seasonal analysis
        # In production, this would use more sophisticated time series analysis

        for metric, trend_data in trends.items():
            if len(trend_data) >= 30:  # Need at least 30 data points
                values = [point[1] for point in trend_data]
                dates = [point[0] for point in trend_data]

                # Group by day of week to identify weekly patterns
                weekly_averages = {}
                for i, date_point in enumerate(dates):
                    day_of_week = date_point.weekday()
                    if day_of_week not in weekly_averages:
                        weekly_averages[day_of_week] = []
                    weekly_averages[day_of_week].append(values[i])

                # Calculate average for each day
                day_averages = {}
                for day, day_values in weekly_averages.items():
                    day_averages[day] = statistics.mean(day_values)

                # Check for significant variation
                if day_averages:
                    max_avg = max(day_averages.values())
                    min_avg = min(day_averages.values())
                    variation = (max_avg - min_avg) / max_avg if max_avg > 0 else 0

                    if variation > 0.2:  # 20% variation indicates seasonal pattern
                        seasonal_patterns["has_seasonal_pattern"] = True
                        seasonal_patterns["pattern_strength"] = round(variation, 2)

                        # Identify peak and low days
                        days_of_week = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
                        for day, avg in day_averages.items():
                            if avg == max_avg:
                                seasonal_patterns["peak_periods"].append(days_of_week[day])
                            elif avg == min_avg:
                                seasonal_patterns["low_periods"].append(days_of_week[day])

        return seasonal_patterns

    def _generate_forecast_insights(
        self,
        predictions: Dict[str, List[Dict[str, Any]]],
        confidence_intervals: Dict[str, Dict[str, float]],
        seasonal_patterns: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate insights from forecast analysis."""
        insights = []

        # Analyze prediction trends
        for metric, metric_predictions in predictions.items():
            if not metric_predictions:
                continue

            first_prediction = metric_predictions[0]["predicted_value"]
            last_prediction = metric_predictions[-1]["predicted_value"]

            if last_prediction > first_prediction * 1.1:  # 10% increase
                insights.append({
                    "type": "positive",
                    "category": "forecast",
                    "metric": metric,
                    "message": f"Predicted growth in {metric} over forecast period",
                    "confidence": "medium"
                })
            elif last_prediction < first_prediction * 0.9:  # 10% decrease
                insights.append({
                    "type": "warning",
                    "category": "forecast",
                    "metric": metric,
                    "message": f"Predicted decline in {metric} - proactive measures recommended",
                    "confidence": "medium"
                })

        # Seasonal insights
        if seasonal_patterns.get("has_seasonal_pattern"):
            insights.append({
                "type": "info",
                "category": "seasonal",
                "message": f"Seasonal patterns detected with {seasonal_patterns['pattern_strength']*100:.0f}% variation",
                "peak_periods": seasonal_patterns.get("peak_periods", []),
                "low_periods": seasonal_patterns.get("low_periods", [])
            })

        return insights
