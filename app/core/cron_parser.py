"""
Comprehensive Cron Expression Parser & Validator for Task 6.2.2 Phase 3.2

This module provides production-grade cron expression parsing and validation with:
- Standard 5-part and extended 6-part cron expression support
- Named schedule patterns (@daily, @weekly, etc.)
- Advanced validation with timezone and business day support
- Performance-optimized parsing with <50ms validation targets
- Integration with existing workflow scheduling system

Performance Targets:
- <50ms validation time for complex expressions
- <100ms for next 10 execution times calculation
- >99.9% parsing accuracy for valid expressions
- Zero technical debt with comprehensive error handling
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import zoneinfo
from app.models.workflow_models import NamedSchedule, HolidayCalendar

logger = logging.getLogger(__name__)


class CronFieldType(Enum):
    """Cron field types for validation."""
    SECOND = "second"
    MINUTE = "minute"
    HOUR = "hour"
    DAY = "day"
    MONTH = "month"
    WEEKDAY = "weekday"


@dataclass
class CronField:
    """Represents a single cron field with validation rules."""
    field_type: CronFieldType
    min_value: int
    max_value: int
    aliases: Optional[Dict[str, int]] = None

    def __post_init__(self):
        """Initialize field aliases."""
        if self.aliases is None:
            self.aliases = {}


@dataclass
class ParsedCronExpression:
    """Represents a parsed and validated cron expression."""
    original_expression: str
    seconds: List[int]
    minutes: List[int]
    hours: List[int]
    days: List[int]
    months: List[int]
    weekdays: List[int]
    is_named_schedule: bool = False
    named_schedule: Optional[NamedSchedule] = None
    timezone: str = "UTC"
    business_days_only: bool = False
    exclude_holidays: bool = False
    holiday_calendar: Optional[HolidayCalendar] = None


class CronParseError(Exception):
    """Custom exception for cron parsing errors."""

    def __init__(self, message: str, field: Optional[str] = None, position: Optional[int] = None):
        self.message = message
        self.field = field
        self.position = position
        super().__init__(self.format_error())

    def format_error(self) -> str:
        """Format error message with context."""
        error_msg = f"Cron parsing error: {self.message}"
        if self.field:
            error_msg += f" (field: {self.field})"
        if self.position is not None:
            error_msg += f" (position: {self.position})"
        return error_msg


class CronExpressionParser:
    """
    Comprehensive cron expression parser with advanced validation.

    Supports:
    - Standard 5-part cron expressions (minute hour day month weekday)
    - Extended 6-part cron expressions (second minute hour day month weekday)
    - Named schedules (@daily, @weekly, @monthly, @yearly, @hourly, @minutely, @reboot)
    - Special characters: *, -, /, ?, L, W, #
    - Range expressions: 1-5, MON-FRI
    - Step values: */15, 0-23/2
    - Timezone-aware validation
    - Business day and holiday calendar integration
    """

    # Cron field definitions with validation rules
    CRON_FIELDS = {
        CronFieldType.SECOND: CronField(CronFieldType.SECOND, 0, 59),
        CronFieldType.MINUTE: CronField(CronFieldType.MINUTE, 0, 59),
        CronFieldType.HOUR: CronField(CronFieldType.HOUR, 0, 23),
        CronFieldType.DAY: CronField(CronFieldType.DAY, 1, 31),
        CronFieldType.MONTH: CronField(
            CronFieldType.MONTH, 1, 12,
            aliases={
                'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
                'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
            }
        ),
        CronFieldType.WEEKDAY: CronField(
            CronFieldType.WEEKDAY, 0, 7,  # 0 and 7 both represent Sunday
            aliases={
                'SUN': 0, 'MON': 1, 'TUE': 2, 'WED': 3, 'THU': 4, 'FRI': 5, 'SAT': 6
            }
        )
    }

    # Named schedule mappings to cron expressions
    NAMED_SCHEDULES = {
        NamedSchedule.YEARLY: "0 0 1 1 *",
        NamedSchedule.ANNUALLY: "0 0 1 1 *",
        NamedSchedule.MONTHLY: "0 0 1 * *",
        NamedSchedule.WEEKLY: "0 0 * * 0",
        NamedSchedule.DAILY: "0 0 * * *",
        NamedSchedule.HOURLY: "0 * * * *",
        NamedSchedule.MINUTELY: "* * * * *",
        NamedSchedule.REBOOT: "0 0 * * *"  # Special handling required
    }

    # Regex patterns for validation
    CRON_PATTERNS = {
        'number': r'^\d+$',
        'range': r'^(\d+)-(\d+)$',
        'step': r'^(\*|\d+|\d+-\d+)/(\d+)$',
        'list': r'^(\d+|\d+-\d+)(,(\d+|\d+-\d+))*$',
        'wildcard': r'^\*$',
        'question': r'^\?$',
        'last': r'^L$',
        'weekday': r'^(\d+)W$',
        'nth_weekday': r'^(\d+)#(\d+)$'
    }

    def __init__(self):
        """Initialize the cron parser."""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def parse(
        self,
        expression: str,
        timezone: str = "UTC",
        business_days_only: bool = False,
        exclude_holidays: bool = False,
        holiday_calendar: Optional[HolidayCalendar] = None
    ) -> ParsedCronExpression:
        """
        Parse and validate a cron expression.

        Args:
            expression: Cron expression to parse
            timezone: Timezone for schedule execution
            business_days_only: Whether to restrict to business days
            exclude_holidays: Whether to exclude holidays
            holiday_calendar: Holiday calendar to use

        Returns:
            ParsedCronExpression: Parsed and validated cron expression

        Raises:
            CronParseError: If expression is invalid
        """
        start_time = datetime.now()

        try:
            # Validate timezone
            self._validate_timezone(timezone)

            # Check for named schedules
            if expression.startswith('@'):
                return self._parse_named_schedule(
                    expression, timezone, business_days_only, exclude_holidays, holiday_calendar
                )

            # Parse regular cron expression
            parsed = self._parse_regular_expression(
                expression, timezone, business_days_only, exclude_holidays, holiday_calendar
            )

            # Log performance metrics
            parse_time = (datetime.now() - start_time).total_seconds() * 1000
            self.logger.info(
                f"Cron expression parsed successfully",
                extra={
                    "expression": expression,
                    "parse_time_ms": parse_time,
                    "timezone": timezone,
                    "business_days_only": business_days_only,
                    "exclude_holidays": exclude_holidays
                }
            )

            return parsed

        except Exception as e:
            parse_time = (datetime.now() - start_time).total_seconds() * 1000
            self.logger.error(
                f"Failed to parse cron expression: {str(e)}",
                extra={
                    "expression": expression,
                    "parse_time_ms": parse_time,
                    "error": str(e)
                }
            )
            if isinstance(e, CronParseError):
                raise
            raise CronParseError(f"Unexpected error during parsing: {str(e)}")

    def _validate_timezone(self, timezone: str) -> None:
        """Validate timezone string."""
        try:
            zoneinfo.ZoneInfo(timezone)
        except Exception:
            raise CronParseError(f"Invalid timezone: {timezone}")

    def _parse_named_schedule(
        self,
        expression: str,
        timezone: str,
        business_days_only: bool,
        exclude_holidays: bool,
        holiday_calendar: Optional[HolidayCalendar]
    ) -> ParsedCronExpression:
        """Parse named schedule expression."""
        try:
            named_schedule = NamedSchedule(expression)
        except ValueError:
            raise CronParseError(f"Invalid named schedule: {expression}")

        # Convert named schedule to cron expression
        cron_expr = self.NAMED_SCHEDULES[named_schedule]

        # Parse the equivalent cron expression
        parsed = self._parse_regular_expression(
            cron_expr, timezone, business_days_only, exclude_holidays, holiday_calendar
        )

        # Mark as named schedule
        parsed.is_named_schedule = True
        parsed.named_schedule = named_schedule
        parsed.original_expression = expression

        return parsed

    def _parse_regular_expression(
        self,
        expression: str,
        timezone: str,
        business_days_only: bool,
        exclude_holidays: bool,
        holiday_calendar: Optional[HolidayCalendar]
    ) -> ParsedCronExpression:
        """Parse regular cron expression (5-part or 6-part)."""
        fields = expression.strip().split()

        # Determine if 5-part or 6-part expression
        if len(fields) == 5:
            # Standard 5-part: minute hour day month weekday
            field_values = [
                [0],  # seconds (default to 0)
                self._parse_field(fields[0], CronFieldType.MINUTE),
                self._parse_field(fields[1], CronFieldType.HOUR),
                self._parse_field(fields[2], CronFieldType.DAY),
                self._parse_field(fields[3], CronFieldType.MONTH),
                self._parse_field(fields[4], CronFieldType.WEEKDAY)
            ]
        elif len(fields) == 6:
            # Extended 6-part: second minute hour day month weekday
            field_values = [
                self._parse_field(fields[0], CronFieldType.SECOND),
                self._parse_field(fields[1], CronFieldType.MINUTE),
                self._parse_field(fields[2], CronFieldType.HOUR),
                self._parse_field(fields[3], CronFieldType.DAY),
                self._parse_field(fields[4], CronFieldType.MONTH),
                self._parse_field(fields[5], CronFieldType.WEEKDAY)
            ]
        else:
            raise CronParseError(
                f"Invalid cron expression format. Expected 5 or 6 fields, got {len(fields)}"
            )

        # Validate day/weekday mutual exclusivity
        self._validate_day_weekday_exclusivity(fields)

        return ParsedCronExpression(
            original_expression=expression,
            seconds=field_values[0],
            minutes=field_values[1],
            hours=field_values[2],
            days=field_values[3],
            months=field_values[4],
            weekdays=field_values[5],
            timezone=timezone,
            business_days_only=business_days_only,
            exclude_holidays=exclude_holidays,
            holiday_calendar=holiday_calendar
        )

    def _parse_field(self, field_str: str, field_type: CronFieldType) -> List[int]:
        """Parse a single cron field into list of valid values."""
        field_def = self.CRON_FIELDS[field_type]

        # Handle wildcard
        if field_str == '*':
            return list(range(field_def.min_value, field_def.max_value + 1))

        # Handle question mark (only valid for day and weekday)
        if field_str == '?':
            if field_type not in [CronFieldType.DAY, CronFieldType.WEEKDAY]:
                raise CronParseError(f"'?' is only valid for day and weekday fields", field_type.value)
            return []

        # Handle comma-separated lists
        if ',' in field_str:
            values = []
            for part in field_str.split(','):
                values.extend(self._parse_field_part(part.strip(), field_type))
            return sorted(list(set(values)))

        # Parse single field part
        return self._parse_field_part(field_str, field_type)

    def _parse_field_part(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse a single part of a cron field."""
        field_def = self.CRON_FIELDS[field_type]

        # Handle step values (*/n or range/n)
        if '/' in part:
            return self._parse_step_values(part, field_type)

        # Handle ranges (n-m)
        if '-' in part and not part.startswith('-'):
            return self._parse_range_values(part, field_type)

        # Handle special weekday expressions
        if field_type == CronFieldType.WEEKDAY:
            if part.endswith('L'):
                # Last occurrence of weekday in month
                return self._parse_last_weekday(part, field_type)
            if '#' in part:
                # Nth occurrence of weekday in month
                return self._parse_nth_weekday(part, field_type)

        # Handle special day expressions
        if field_type == CronFieldType.DAY:
            if part == 'L':
                # Last day of month
                return [-1]  # Special marker for last day
            if part.endswith('W'):
                # Nearest weekday
                return self._parse_nearest_weekday(part, field_type)

        # Handle single value
        value = self._parse_single_value(part, field_type)
        self._validate_field_value(value, field_type)
        return [value]

    def _parse_step_values(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse step values like */15 or 0-23/2."""
        field_def = self.CRON_FIELDS[field_type]

        if '/' not in part:
            raise CronParseError(f"Invalid step expression: {part}", field_type.value)

        range_part, step_part = part.split('/', 1)

        try:
            step = int(step_part)
            if step <= 0:
                raise CronParseError(f"Step value must be positive: {step}", field_type.value)
        except ValueError:
            raise CronParseError(f"Invalid step value: {step_part}", field_type.value)

        # Determine range
        if range_part == '*':
            start, end = field_def.min_value, field_def.max_value
        elif '-' in range_part:
            start_str, end_str = range_part.split('-', 1)
            start = self._parse_single_value(start_str, field_type)
            end = self._parse_single_value(end_str, field_type)
        else:
            start = self._parse_single_value(range_part, field_type)
            end = field_def.max_value

        # Validate range
        self._validate_field_value(start, field_type)
        self._validate_field_value(end, field_type)

        if start > end:
            raise CronParseError(f"Invalid range: {start}-{end}", field_type.value)

        # Generate step values
        values = []
        current = start
        while current <= end:
            values.append(current)
            current += step

        return values

    def _parse_range_values(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse range values like 1-5 or MON-FRI."""
        if part.count('-') != 1:
            raise CronParseError(f"Invalid range expression: {part}", field_type.value)

        start_str, end_str = part.split('-', 1)
        start = self._parse_single_value(start_str, field_type)
        end = self._parse_single_value(end_str, field_type)

        self._validate_field_value(start, field_type)
        self._validate_field_value(end, field_type)

        if start > end:
            # Handle wrap-around for weekdays (e.g., FRI-MON)
            if field_type == CronFieldType.WEEKDAY:
                values = list(range(start, 8)) + list(range(0, end + 1))
                # Convert 7 to 0 (both represent Sunday)
                return [0 if v == 7 else v for v in values]
            else:
                raise CronParseError(f"Invalid range: {start}-{end}", field_type.value)

        return list(range(start, end + 1))

    def _parse_single_value(self, value_str: str, field_type: CronFieldType) -> int:
        """Parse a single value, handling aliases."""
        field_def = self.CRON_FIELDS[field_type]

        # Check for aliases first
        if field_def.aliases and value_str.upper() in field_def.aliases:
            return field_def.aliases[value_str.upper()]

        # Parse as integer
        try:
            value = int(value_str)
            # Handle Sunday as both 0 and 7 for weekday
            if field_type == CronFieldType.WEEKDAY and value == 7:
                return 0
            return value
        except ValueError:
            raise CronParseError(f"Invalid value: {value_str}", field_type.value)

    def _validate_field_value(self, value: int, field_type: CronFieldType) -> None:
        """Validate a field value is within acceptable range."""
        field_def = self.CRON_FIELDS[field_type]

        # Special handling for weekday (0 and 7 both valid for Sunday)
        if field_type == CronFieldType.WEEKDAY:
            if not (0 <= value <= 7):
                raise CronParseError(
                    f"Weekday value {value} out of range (0-7)", field_type.value
                )
            return

        # Special handling for day field (can have -1 for last day)
        if field_type == CronFieldType.DAY and value == -1:
            return

        if not (field_def.min_value <= value <= field_def.max_value):
            raise CronParseError(
                f"{field_type.value.title()} value {value} out of range "
                f"({field_def.min_value}-{field_def.max_value})",
                field_type.value
            )

    def _parse_last_weekday(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse last weekday expressions like 5L (last Friday)."""
        if not part.endswith('L'):
            raise CronParseError(f"Invalid last weekday expression: {part}", field_type.value)

        weekday_str = part[:-1]
        weekday = self._parse_single_value(weekday_str, field_type)
        self._validate_field_value(weekday, field_type)

        # Return special marker for last weekday (negative value)
        return [-(weekday + 10)]  # -10 to -16 for last weekday occurrences

    def _parse_nth_weekday(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse nth weekday expressions like 5#3 (3rd Friday)."""
        if '#' not in part:
            raise CronParseError(f"Invalid nth weekday expression: {part}", field_type.value)

        weekday_str, nth_str = part.split('#', 1)
        weekday = self._parse_single_value(weekday_str, field_type)

        try:
            nth = int(nth_str)
            if not (1 <= nth <= 5):
                raise CronParseError(f"Nth value must be 1-5: {nth}", field_type.value)
        except ValueError:
            raise CronParseError(f"Invalid nth value: {nth_str}", field_type.value)

        self._validate_field_value(weekday, field_type)

        # Return special marker for nth weekday (weekday * 10 + nth)
        return [weekday * 10 + nth]

    def _parse_nearest_weekday(self, part: str, field_type: CronFieldType) -> List[int]:
        """Parse nearest weekday expressions like 15W (nearest weekday to 15th)."""
        if not part.endswith('W'):
            raise CronParseError(f"Invalid nearest weekday expression: {part}", field_type.value)

        day_str = part[:-1]
        day = self._parse_single_value(day_str, field_type)
        self._validate_field_value(day, field_type)

        # Return special marker for nearest weekday (negative value)
        return [-(day + 100)]  # -101 to -131 for nearest weekday

    def _validate_day_weekday_exclusivity(self, fields: List[str]) -> None:
        """Validate that day and weekday fields are mutually exclusive when using ?."""
        if len(fields) < 5:
            return

        day_field = fields[-3] if len(fields) == 5 else fields[-3]
        weekday_field = fields[-1]

        # Both cannot be specified (not ? or *)
        if (day_field not in ['*', '?'] and weekday_field not in ['*', '?']):
            # This is actually allowed in some cron implementations
            # We'll log a warning but not fail
            self.logger.warning(
                f"Both day ({day_field}) and weekday ({weekday_field}) specified. "
                f"This may lead to unexpected behavior."
            )

    def validate_expression(self, expression: str) -> Dict[str, Any]:
        """
        Validate a cron expression and return validation results.

        Args:
            expression: Cron expression to validate

        Returns:
            Dict containing validation results and metadata
        """
        start_time = datetime.now()

        try:
            parsed = self.parse(expression)
            validation_time = (datetime.now() - start_time).total_seconds() * 1000

            return {
                "valid": True,
                "parsed": parsed,
                "validation_time_ms": validation_time,
                "field_counts": {
                    "seconds": len(parsed.seconds),
                    "minutes": len(parsed.minutes),
                    "hours": len(parsed.hours),
                    "days": len(parsed.days),
                    "months": len(parsed.months),
                    "weekdays": len(parsed.weekdays)
                },
                "is_named_schedule": parsed.is_named_schedule,
                "named_schedule": parsed.named_schedule.value if parsed.named_schedule else None
            }

        except CronParseError as e:
            validation_time = (datetime.now() - start_time).total_seconds() * 1000

            return {
                "valid": False,
                "error": str(e),
                "error_field": e.field,
                "error_position": e.position,
                "validation_time_ms": validation_time
            }
        except Exception as e:
            validation_time = (datetime.now() - start_time).total_seconds() * 1000

            return {
                "valid": False,
                "error": f"Unexpected validation error: {str(e)}",
                "validation_time_ms": validation_time
            }