"""
Unit tests for booking models.

This module provides comprehensive unit tests for booking-related database models:
- Booking: Core booking entity with lifecycle management
- BookingStatusHistory: Audit trail for booking status changes
- BookingCommunication: Vendor-customer messaging system
- BookingModification: Booking change request workflows
- Enum validation and business logic constraints

Implements Task 4.1.1 Phase 6 requirements with >80% test coverage.
"""

import pytest
from datetime import datetime, timezone, date, time
from decimal import Decimal
from uuid import uuid4
from sqlalchemy.exc import IntegrityError

from app.models.booking import (
    Booking, BookingStatusHistory, BookingCommunication, BookingModification,
    BookingStatus, VendorResponseType, BookingPriority, CommunicationType, ModificationType
)


class TestBookingModel:
    """Test Booking model functionality."""

    def test_booking_creation(self):
        """Test basic booking creation."""
        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001234",
            booking_date=date(2025, 2, 15),
            booking_time=time(14, 30),
            duration_hours=Decimal("2.5"),
            participant_count=2,
            status=BookingStatus.PENDING,
            priority=BookingPriority.NORMAL,
            base_price=Decimal("150.00"),
            total_amount=Decimal("150.00"),
            currency="NGN"
        )

        assert booking.customer_id == 1
        assert booking.vendor_id == 1
        assert booking.service_id == 1
        assert booking.booking_reference == "BK-2025-001234"
        assert booking.booking_date == date(2025, 2, 15)
        assert booking.booking_time == time(14, 30)
        assert booking.duration_hours == Decimal("2.5")
        assert booking.participant_count == 2
        assert booking.status == BookingStatus.PENDING
        assert booking.priority == BookingPriority.NORMAL
        assert booking.base_price == Decimal("150.00")
        assert booking.total_amount == Decimal("150.00")
        assert booking.currency == "NGN"

    def test_booking_with_optional_fields(self):
        """Test booking creation with optional fields."""
        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001235",
            booking_date=date(2025, 2, 16),
            participant_count=1,
            status=BookingStatus.PENDING,
            base_price=Decimal("100.00"),
            total_amount=Decimal("100.00"),
            special_requirements="Vegetarian meal required",
            customer_notes="Celebrating anniversary",
            accessibility_requirements={"wheelchair_accessible": True},
            vendor_response_type=VendorResponseType.APPROVED,
            vendor_notes="We can accommodate all requirements"
        )

        assert booking.special_requirements == "Vegetarian meal required"
        assert booking.customer_notes == "Celebrating anniversary"
        assert booking.accessibility_requirements == {"wheelchair_accessible": True}
        assert booking.vendor_response_type == VendorResponseType.APPROVED
        assert booking.vendor_notes == "We can accommodate all requirements"

    def test_booking_pricing_calculations(self):
        """Test booking pricing and commission calculations."""
        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001236",
            booking_date=date(2025, 2, 17),
            participant_count=3,
            status=BookingStatus.CONFIRMED,
            base_price=Decimal("200.00"),
            additional_fees=Decimal("20.00"),
            discount_amount=Decimal("10.00"),
            total_amount=Decimal("210.00"),
            commission_rate=Decimal("0.15"),
            commission_amount=Decimal("31.50"),
            vendor_payout=Decimal("178.50")
        )

        # Verify pricing calculations
        subtotal = booking.base_price + booking.additional_fees - booking.discount_amount
        assert subtotal == Decimal("210.00")
        assert booking.total_amount == subtotal
        assert booking.commission_amount == booking.total_amount * booking.commission_rate
        assert booking.vendor_payout == booking.total_amount - booking.commission_amount

    def test_booking_status_enum_validation(self):
        """Test booking status enum validation."""
        # Test all valid statuses
        valid_statuses = [
            BookingStatus.PENDING,
            BookingStatus.VENDOR_REVIEW,
            BookingStatus.CONFIRMED,
            BookingStatus.PAYMENT_PENDING,
            BookingStatus.PAID,
            BookingStatus.IN_PROGRESS,
            BookingStatus.COMPLETED,
            BookingStatus.CANCELLED_BY_CUSTOMER,
            BookingStatus.CANCELLED_BY_VENDOR,
            BookingStatus.REJECTED,
            BookingStatus.REFUNDED,
            BookingStatus.DISPUTED,
            BookingStatus.NO_SHOW
        ]

        for status in valid_statuses:
            booking = Booking(
                customer_id=1,
                vendor_id=1,
                service_id=1,
                booking_reference=f"BK-2025-{status.value}",
                booking_date=date(2025, 2, 15),
                participant_count=1,
                status=status,
                base_price=Decimal("100.00"),
                total_amount=Decimal("100.00")
            )
            assert booking.status == status

    def test_booking_priority_enum_validation(self):
        """Test booking priority enum validation."""
        priorities = [
            BookingPriority.LOW,
            BookingPriority.NORMAL,
            BookingPriority.HIGH,
            BookingPriority.URGENT
        ]

        for priority in priorities:
            booking = Booking(
                customer_id=1,
                vendor_id=1,
                service_id=1,
                booking_reference=f"BK-2025-{priority.value}",
                booking_date=date(2025, 2, 15),
                participant_count=1,
                status=BookingStatus.PENDING,
                priority=priority,
                base_price=Decimal("100.00"),
                total_amount=Decimal("100.00")
            )
            assert booking.priority == priority

    def test_vendor_response_type_enum_validation(self):
        """Test vendor response type enum validation."""
        response_types = [
            VendorResponseType.APPROVED,
            VendorResponseType.REJECTED,
            VendorResponseType.MODIFICATION_REQUESTED,
            VendorResponseType.COUNTER_OFFER
        ]

        for response_type in response_types:
            booking = Booking(
                customer_id=1,
                vendor_id=1,
                service_id=1,
                booking_reference=f"BK-2025-{response_type.value}",
                booking_date=date(2025, 2, 15),
                participant_count=1,
                status=BookingStatus.PENDING,
                vendor_response_type=response_type,
                base_price=Decimal("100.00"),
                total_amount=Decimal("100.00")
            )
            assert booking.vendor_response_type == response_type

    def test_booking_defaults(self):
        """Test booking default values."""
        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001237",
            booking_date=date(2025, 2, 15),
            base_price=Decimal("100.00"),
            total_amount=Decimal("100.00")
        )

        # Test default values (SQLAlchemy defaults are applied on save, not instantiation)
        # For unit tests, we test the column defaults are defined correctly
        from app.models.booking import Booking as BookingModel

        # Check that defaults are defined in the model
        assert BookingModel.participant_count.default.arg == 1
        assert BookingModel.status.default.arg == BookingStatus.PENDING
        assert BookingModel.priority.default.arg == BookingPriority.NORMAL
        assert BookingModel.additional_fees.default.arg == Decimal("0.00")
        assert BookingModel.discount_amount.default.arg == Decimal("0.00")
        assert BookingModel.commission_rate.default.arg == Decimal("0.15")
        assert BookingModel.commission_amount.default.arg == Decimal("0.00")
        assert BookingModel.vendor_payout.default.arg == Decimal("0.00")
        assert BookingModel.currency.default.arg == "NGN"
        assert BookingModel.payment_status.default.arg == "pending"
        assert BookingModel.completion_confirmed_by_customer.default.arg is False
        assert BookingModel.completion_confirmed_by_vendor.default.arg is False
        assert BookingModel.unread_messages_customer.default.arg == 0
        assert BookingModel.unread_messages_vendor.default.arg == 0
        assert BookingModel.refund_amount.default.arg == Decimal("0.00")
        assert BookingModel.booking_source.default.arg == "web"

    def test_booking_json_fields(self):
        """Test booking JSON field handling."""
        accessibility_reqs = {
            "wheelchair_accessible": True,
            "hearing_assistance": False,
            "visual_assistance": True,
            "dietary_restrictions": ["vegetarian", "gluten-free"]
        }

        booking_metadata = {
            "source_campaign": "summer_promotion",
            "referral_code": "FRIEND2025",
            "user_agent": "Mozilla/5.0...",
            "utm_source": "google"
        }

        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001238",
            booking_date=date(2025, 2, 15),
            participant_count=1,
            status=BookingStatus.PENDING,
            base_price=Decimal("100.00"),
            total_amount=Decimal("100.00"),
            accessibility_requirements=accessibility_reqs,
            booking_metadata=booking_metadata
        )

        assert booking.accessibility_requirements == accessibility_reqs
        assert booking.booking_metadata == booking_metadata
        assert booking.accessibility_requirements["wheelchair_accessible"] is True
        assert "vegetarian" in booking.accessibility_requirements["dietary_restrictions"]
        assert booking.booking_metadata["source_campaign"] == "summer_promotion"

    def test_booking_timestamps(self):
        """Test booking timestamp handling."""
        now = datetime.now(timezone.utc)

        booking = Booking(
            customer_id=1,
            vendor_id=1,
            service_id=1,
            booking_reference="BK-2025-001239",
            booking_date=date(2025, 2, 15),
            participant_count=1,
            status=BookingStatus.PENDING,
            base_price=Decimal("100.00"),
            total_amount=Decimal("100.00"),
            vendor_response_deadline=now,
            service_start_time=now,
            service_end_time=now
        )

        assert booking.vendor_response_deadline == now
        assert booking.service_start_time == now
        assert booking.service_end_time == now


class TestBookingStatusHistory:
    """Test BookingStatusHistory model functionality."""

    def test_status_history_creation(self):
        """Test basic status history creation."""
        history = BookingStatusHistory(
            booking_id=1,
            previous_status=BookingStatus.PENDING,
            new_status=BookingStatus.CONFIRMED,
            changed_by=1,
            change_reason="Vendor approved booking",
            change_notes="All requirements can be accommodated"
        )

        assert history.booking_id == 1
        assert history.previous_status == BookingStatus.PENDING
        assert history.new_status == BookingStatus.CONFIRMED
        assert history.changed_by == 1
        assert history.change_reason == "Vendor approved booking"
        assert history.change_notes == "All requirements can be accommodated"

    def test_status_history_automated_change(self):
        """Test automated status history creation."""
        history = BookingStatusHistory(
            booking_id=1,
            previous_status=BookingStatus.PAYMENT_PENDING,
            new_status=BookingStatus.PAID,
            automated_change=True,
            system_trigger="payment_webhook",
            customer_notified=True,
            vendor_notified=True
        )

        assert history.automated_change is True
        assert history.system_trigger == "payment_webhook"
        assert history.customer_notified is True
        assert history.vendor_notified is True

    def test_status_history_defaults(self):
        """Test status history default values."""
        history = BookingStatusHistory(
            booking_id=1,
            new_status=BookingStatus.CONFIRMED
        )

        # Test that defaults are defined in the model
        from app.models.booking import BookingStatusHistory as StatusHistoryModel

        assert history.previous_status is None
        assert history.changed_by is None
        assert StatusHistoryModel.automated_change.default.arg is False
        assert StatusHistoryModel.customer_notified.default.arg is False
        assert StatusHistoryModel.vendor_notified.default.arg is False


class TestBookingCommunication:
    """Test BookingCommunication model functionality."""

    def test_communication_creation(self):
        """Test basic communication creation."""
        communication = BookingCommunication(
            booking_id=1,
            sender_id=1,
            recipient_id=2,
            communication_type=CommunicationType.MESSAGE,
            subject="Question about booking",
            message="Hi, I have a question about the accessibility requirements."
        )

        assert communication.booking_id == 1
        assert communication.sender_id == 1
        assert communication.recipient_id == 2
        assert communication.communication_type == CommunicationType.MESSAGE
        assert communication.subject == "Question about booking"
        assert communication.message == "Hi, I have a question about the accessibility requirements."

    def test_communication_type_enum_validation(self):
        """Test communication type enum validation."""
        comm_types = [
            CommunicationType.MESSAGE,
            CommunicationType.SYSTEM_NOTIFICATION,
            CommunicationType.STATUS_UPDATE,
            CommunicationType.MODIFICATION_REQUEST,
            CommunicationType.PAYMENT_REMINDER,
            CommunicationType.REVIEW_REQUEST
        ]

        for comm_type in comm_types:
            communication = BookingCommunication(
                booking_id=1,
                communication_type=comm_type,
                message=f"Test message for {comm_type.value}"
            )
            assert communication.communication_type == comm_type

    def test_communication_defaults(self):
        """Test communication default values."""
        communication = BookingCommunication(
            booking_id=1,
            communication_type=CommunicationType.MESSAGE,
            message="Test message"
        )

        # Test that defaults are defined in the model
        from app.models.booking import BookingCommunication as CommunicationModel

        assert CommunicationModel.is_read.default.arg is False
        assert CommunicationModel.is_system_message.default.arg is False
        assert CommunicationModel.requires_response.default.arg is False
        assert CommunicationModel.has_attachments.default.arg is False
        assert CommunicationModel.email_sent.default.arg is False
        assert CommunicationModel.push_sent.default.arg is False
        assert CommunicationModel.sms_sent.default.arg is False

    def test_communication_with_attachments(self):
        """Test communication with attachments."""
        attachment_urls = [
            "https://example.com/file1.pdf",
            "https://example.com/image1.jpg"
        ]

        attachment_metadata = {
            "file1.pdf": {"size": 1024, "type": "application/pdf"},
            "image1.jpg": {"size": 2048, "type": "image/jpeg"}
        }

        communication = BookingCommunication(
            booking_id=1,
            communication_type=CommunicationType.MESSAGE,
            message="Please see attached files",
            has_attachments=True,
            attachment_urls=attachment_urls,
            attachment_metadata=attachment_metadata
        )

        assert communication.has_attachments is True
        assert communication.attachment_urls == attachment_urls
        assert communication.attachment_metadata == attachment_metadata


class TestBookingModification:
    """Test BookingModification model functionality."""

    def test_modification_creation(self):
        """Test basic modification creation."""
        original_values = {"booking_date": "2025-02-15", "booking_time": "14:30:00"}
        requested_changes = {"booking_date": "2025-02-20", "booking_time": "15:00:00"}

        modification = BookingModification(
            booking_id=1,
            requested_by=1,
            modification_type=ModificationType.DATE_CHANGE,
            modification_reason="Client requested different date due to schedule conflict",
            original_values=original_values,
            requested_changes=requested_changes
        )

        assert modification.booking_id == 1
        assert modification.requested_by == 1
        assert modification.modification_type == ModificationType.DATE_CHANGE
        assert modification.modification_reason == "Client requested different date due to schedule conflict"
        assert modification.original_values == original_values
        assert modification.requested_changes == requested_changes

    def test_modification_type_enum_validation(self):
        """Test modification type enum validation."""
        mod_types = [
            ModificationType.DATE_CHANGE,
            ModificationType.TIME_CHANGE,
            ModificationType.PARTICIPANT_COUNT,
            ModificationType.SERVICE_UPGRADE,
            ModificationType.SPECIAL_REQUIREMENTS,
            ModificationType.PRICING_ADJUSTMENT
        ]

        for mod_type in mod_types:
            modification = BookingModification(
                booking_id=1,
                modification_type=mod_type,
                original_values={"test": "value"},
                requested_changes={"test": "new_value"}
            )
            assert modification.modification_type == mod_type

    def test_modification_defaults(self):
        """Test modification default values."""
        modification = BookingModification(
            booking_id=1,
            modification_type=ModificationType.DATE_CHANGE,
            original_values={"test": "value"},
            requested_changes={"test": "new_value"}
        )

        # Test that defaults are defined in the model
        from app.models.booking import BookingModification as ModificationModel

        assert ModificationModel.approval_status.default.arg == "pending"
        assert ModificationModel.implemented.default.arg is False
        assert ModificationModel.price_impact.default.arg == Decimal("0.00")
        assert ModificationModel.additional_fees.default.arg == Decimal("0.00")
        assert ModificationModel.customer_notified.default.arg is False
        assert ModificationModel.vendor_notified.default.arg is False

    def test_modification_approval_workflow(self):
        """Test modification approval workflow."""
        modification = BookingModification(
            booking_id=1,
            modification_type=ModificationType.PARTICIPANT_COUNT,
            original_values={"participant_count": 2},
            requested_changes={"participant_count": 4},
            approval_status="approved",
            approved_by=2,
            approval_notes="Approved with additional fee",
            implemented=True,
            implemented_by=2,
            price_impact=Decimal("50.00"),
            additional_fees=Decimal("25.00")
        )

        assert modification.approval_status == "approved"
        assert modification.approved_by == 2
        assert modification.approval_notes == "Approved with additional fee"
        assert modification.implemented is True
        assert modification.implemented_by == 2
        assert modification.price_impact == Decimal("50.00")
        assert modification.additional_fees == Decimal("25.00")
