"""
Transaction Services for Culture Connect Backend API.

This module provides comprehensive transaction functionality including:
- TransactionService: Core transaction processing and tracking
- TransactionEventService: Transaction event history and audit trails
- TransactionWorkflowService: Transaction workflow orchestration

Implements Step 6 Payment & Transaction Management System service layer with
production-grade functionality, provider integration, and comprehensive audit logging.
"""

import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Optional, Dict, Any, List
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService, ServiceError, ValidationError, NotFoundError, ConflictError
from app.repositories.transaction_repository import TransactionRepository, TransactionEventRepository
from app.models.transaction_models import (
    Transaction, TransactionEvent, TransactionStatus,
    TransactionEventType, TransactionType
)
from app.core.payment.config import PaymentProviderType
# TODO: Import transaction schemas when implemented
# from app.schemas.transaction_schemas import (
#     TransactionCreate, TransactionUpdate, TransactionResponse,
#     TransactionEventCreate, TransactionEventResponse
# )
from app.core.logging import correlation_id
from app.core.monitoring import metrics_collector

# Configure logging
logger = logging.getLogger(__name__)


class TransactionService(BaseService[Transaction, TransactionRepository]):
    """
    Transaction service for detailed transaction tracking and management.

    Provides comprehensive transaction operations including:
    - Transaction creation with provider integration
    - Status tracking and workflow automation
    - Reconciliation and audit trail management
    - Performance-optimized transaction queries
    - Provider response handling and webhook processing
    """

    def __init__(self, db: AsyncSession):
        """Initialize transaction service."""
        super().__init__(TransactionRepository, Transaction, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate transaction creation data with business rules.

        Args:
            data: Transaction creation data

        Returns:
            Validated transaction data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['payment_id', 'type', 'amount', 'provider']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate amount
        amount = data.get('amount')
        if not isinstance(amount, (int, float, Decimal)) or amount <= 0:
            raise ValidationError("Amount must be positive", field="amount")

        # Validate transaction type
        transaction_type = data.get('type')
        if not isinstance(transaction_type, TransactionType):
            raise ValidationError("Invalid transaction type", field="type")

        # Validate provider
        provider = data.get('provider')
        if not isinstance(provider, PaymentProviderType):
            raise ValidationError("Invalid payment provider", field="provider")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate transaction update data with business rules.

        Args:
            data: Transaction update data
            existing_id: Existing transaction ID

        Returns:
            Validated transaction data

        Raises:
            ValidationError: If validation fails
        """
        # Prevent modification of critical fields
        protected_fields = ['payment_id', 'type', 'amount', 'provider']
        for field in protected_fields:
            if field in data:
                raise ValidationError(f"Cannot modify {field} after creation", field=field)

        # Validate status transitions
        if 'status' in data:
            status = data['status']
            if not isinstance(status, TransactionStatus):
                raise ValidationError("Invalid transaction status", field="status")

        return data

    async def create_transaction(
        self,
        payment_id: int,
        transaction_type: TransactionType,
        amount: Decimal,
        provider: PaymentProviderType,
        currency: str = "NGN",
        provider_transaction_id: Optional[str] = None,
        **kwargs
    ) -> Transaction:
        """
        Create a new transaction with provider integration.

        Args:
            payment_id: Associated payment ID
            transaction_type: Type of transaction
            amount: Transaction amount
            provider: Payment provider
            currency: Transaction currency
            provider_transaction_id: Provider transaction ID
            **kwargs: Additional transaction fields

        Returns:
            Created transaction instance

        Raises:
            ValidationError: If transaction data is invalid
            ServiceError: If transaction creation fails
        """
        correlation = self.log_operation_start(
            "create_transaction",
            payment_id=payment_id,
            transaction_type=transaction_type.value,
            amount=float(amount),
            provider=provider.value
        )

        try:
            # Create transaction using repository
            async with self.get_transaction_context() as session:
                repository = TransactionRepository(session)
                transaction = await repository.create_transaction(
                    payment_id=payment_id,
                    type=transaction_type,
                    amount=amount,
                    provider=provider,
                    currency=currency,
                    provider_transaction_id=provider_transaction_id,
                    **kwargs
                )

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Transaction created with ID: {transaction.id}, reference: {transaction.reference_id}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "transaction_created",
                    tags={
                        "type": transaction_type.value,
                        "provider": provider.value,
                        "currency": currency
                    }
                )

                return transaction

        except Exception as e:
            await self.handle_service_error(e, "create_transaction", {
                "payment_id": payment_id,
                "transaction_type": transaction_type.value,
                "amount": float(amount)
            })

    async def update_transaction_status(
        self,
        transaction_id: int,
        status: TransactionStatus,
        provider_response: Optional[Dict[str, Any]] = None,
        failure_reason: Optional[str] = None,
        **kwargs
    ) -> Transaction:
        """
        Update transaction status with provider response handling.

        Args:
            transaction_id: Transaction ID to update
            status: New transaction status
            provider_response: Provider response data
            failure_reason: Failure reason if status is failed
            **kwargs: Additional fields to update

        Returns:
            Updated transaction instance

        Raises:
            NotFoundError: If transaction not found
            ValidationError: If status transition is invalid
            ServiceError: If update fails
        """
        correlation = self.log_operation_start(
            "update_transaction_status",
            transaction_id=transaction_id,
            status=status.value
        )

        try:
            # Validate status transition
            await self._validate_status_transition(transaction_id, status)

            # Update transaction using repository
            async with self.get_transaction_context() as session:
                repository = TransactionRepository(session)
                transaction = await repository.update_transaction_status(
                    transaction_id=transaction_id,
                    status=status,
                    provider_response=provider_response,
                    failure_reason=failure_reason,
                    **kwargs
                )

                if not transaction:
                    raise NotFoundError("Transaction", transaction_id)

                # Log successful update
                self.log_operation_success(
                    correlation,
                    f"Transaction {transaction_id} status updated to {status.value}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "transaction_status_updated",
                    tags={"status": status.value, "provider": transaction.provider.value}
                )

                return transaction

        except Exception as e:
            await self.handle_service_error(e, "update_transaction_status", {
                "transaction_id": transaction_id,
                "status": status.value
            })

    async def get_payment_transactions(
        self,
        payment_id: int,
        transaction_type: Optional[TransactionType] = None,
        status: Optional[TransactionStatus] = None
    ) -> List[Transaction]:
        """
        Get transactions for a specific payment.

        Args:
            payment_id: Payment ID to filter by
            transaction_type: Optional transaction type filter
            status: Optional transaction status filter

        Returns:
            List of transactions

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = TransactionRepository(session)
                return await repository.get_payment_transactions(
                    payment_id=payment_id,
                    transaction_type=transaction_type,
                    status=status
                )

        except Exception as e:
            await self.handle_service_error(e, "get_payment_transactions", {"payment_id": payment_id})

    async def get_unreconciled_transactions(
        self,
        provider: PaymentProviderType,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> List[Transaction]:
        """
        Get unreconciled transactions for provider reconciliation.

        Args:
            provider: Payment provider
            date_from: Start date filter
            date_to: End date filter

        Returns:
            List of unreconciled transactions

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = TransactionRepository(session)
                return await repository.get_unreconciled_transactions(
                    provider=provider,
                    date_from=date_from,
                    date_to=date_to
                )

        except Exception as e:
            await self.handle_service_error(e, "get_unreconciled_transactions", {
                "provider": provider.value
            })

    async def mark_transactions_reconciled(
        self,
        transaction_ids: List[int],
        reconciliation_reference: str
    ) -> int:
        """
        Mark transactions as reconciled in bulk.

        Args:
            transaction_ids: List of transaction IDs to mark as reconciled
            reconciliation_reference: Reconciliation reference

        Returns:
            Number of transactions updated

        Raises:
            ServiceError: If bulk update fails
        """
        correlation = self.log_operation_start(
            "mark_transactions_reconciled",
            transaction_count=len(transaction_ids),
            reconciliation_reference=reconciliation_reference
        )

        try:
            async with self.get_transaction_context() as session:
                repository = TransactionRepository(session)
                updated_count = await repository.mark_transactions_reconciled(
                    transaction_ids=transaction_ids,
                    reconciliation_reference=reconciliation_reference
                )

                # Log successful update
                self.log_operation_success(
                    correlation,
                    f"Marked {updated_count} transactions as reconciled"
                )

                return updated_count

        except Exception as e:
            await self.handle_service_error(e, "mark_transactions_reconciled", {
                "transaction_count": len(transaction_ids),
                "reconciliation_reference": reconciliation_reference
            })

    async def _validate_status_transition(self, transaction_id: int, new_status: TransactionStatus) -> None:
        """
        Validate transaction status transition.

        Args:
            transaction_id: Transaction ID
            new_status: New transaction status

        Raises:
            ValidationError: If transition is invalid
        """
        # Get current transaction
        transaction = await self.get_by_id(transaction_id)
        if not transaction:
            raise NotFoundError("Transaction", transaction_id)

        current_status = transaction.status

        # Define valid transitions
        valid_transitions = {
            TransactionStatus.PENDING: [TransactionStatus.PROCESSING, TransactionStatus.CANCELLED],
            TransactionStatus.PROCESSING: [TransactionStatus.COMPLETED, TransactionStatus.FAILED],
            TransactionStatus.COMPLETED: [TransactionStatus.REFUNDED],
            TransactionStatus.FAILED: [TransactionStatus.PENDING],  # Allow retry
            TransactionStatus.CANCELLED: [],  # Terminal state
            TransactionStatus.REFUNDED: []  # Terminal state
        }

        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(
                f"Invalid status transition from {current_status.value} to {new_status.value}",
                field="status"
            )


class TransactionEventService(BaseService[TransactionEvent, TransactionEventRepository]):
    """
    Transaction event service for audit trails and compliance tracking.

    Provides comprehensive transaction event operations including:
    - Event creation for transaction lifecycle tracking
    - Audit trail management and compliance reporting
    - Event querying and filtering for analysis
    - Automated event generation for status changes
    """

    def __init__(self, db: AsyncSession):
        """Initialize transaction event service."""
        super().__init__(TransactionEventRepository, TransactionEvent, db_session=db)

    async def validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate transaction event creation data.

        Args:
            data: Transaction event creation data

        Returns:
            Validated transaction event data

        Raises:
            ValidationError: If validation fails
        """
        # Validate required fields
        required_fields = ['transaction_id', 'event_type']
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"{field} is required", field=field)

        # Validate event type
        event_type = data.get('event_type')
        if not isinstance(event_type, TransactionEventType):
            raise ValidationError("Invalid transaction event type", field="event_type")

        return data

    async def validate_update_data(self, data: Dict[str, Any], existing_id: Any) -> Dict[str, Any]:
        """
        Validate transaction event update data.

        Args:
            data: Transaction event update data
            existing_id: Existing transaction event ID

        Returns:
            Validated transaction event data

        Raises:
            ValidationError: If validation fails
        """
        # Transaction events are immutable once created
        raise ValidationError("Transaction events cannot be modified after creation")

    async def create_transaction_event(
        self,
        transaction_id: int,
        event_type: TransactionEventType,
        event_data: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        **kwargs
    ) -> TransactionEvent:
        """
        Create a new transaction event for audit tracking.

        Args:
            transaction_id: Associated transaction ID
            event_type: Type of transaction event
            event_data: Optional event data
            user_id: Optional user ID who triggered the event
            **kwargs: Additional event fields

        Returns:
            Created transaction event instance

        Raises:
            ValidationError: If event data is invalid
            ServiceError: If event creation fails
        """
        correlation = self.log_operation_start(
            "create_transaction_event",
            transaction_id=transaction_id,
            event_type=event_type.value,
            user_id=user_id
        )

        try:
            # Create transaction event using repository
            async with self.get_transaction_context() as session:
                repository = TransactionEventRepository(session)
                event = await repository.create_transaction_event(
                    transaction_id=transaction_id,
                    event_type=event_type,
                    event_data=event_data,
                    user_id=user_id,
                    **kwargs
                )

                # Log successful creation
                self.log_operation_success(
                    correlation,
                    f"Transaction event created with ID: {event.id}"
                )

                # Track metrics
                metrics_collector.increment_counter(
                    "transaction_event_created",
                    tags={"event_type": event_type.value}
                )

                return event

        except Exception as e:
            await self.handle_service_error(e, "create_transaction_event", {
                "transaction_id": transaction_id,
                "event_type": event_type.value
            })

    async def get_transaction_events(
        self,
        transaction_id: int,
        event_type: Optional[TransactionEventType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> List[TransactionEvent]:
        """
        Get events for a specific transaction.

        Args:
            transaction_id: Transaction ID to filter by
            event_type: Optional event type filter
            date_from: Start date filter
            date_to: End date filter

        Returns:
            List of transaction events

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = TransactionEventRepository(session)
                return await repository.get_transaction_events(
                    transaction_id=transaction_id,
                    event_type=event_type,
                    date_from=date_from,
                    date_to=date_to
                )

        except Exception as e:
            await self.handle_service_error(e, "get_transaction_events", {
                "transaction_id": transaction_id
            })

    async def get_user_transaction_events(
        self,
        user_id: int,
        event_type: Optional[TransactionEventType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """
        Get transaction events for a specific user with pagination.

        Args:
            user_id: User ID to filter by
            event_type: Optional event type filter
            date_from: Start date filter
            date_to: End date filter
            page: Page number
            size: Page size

        Returns:
            Paginated transaction events

        Raises:
            ServiceError: If query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = TransactionEventRepository(session)

                from app.repositories.base import PaginationParams
                pagination = PaginationParams(page=page, size=size)

                result = await repository.get_user_transaction_events(
                    user_id=user_id,
                    event_type=event_type,
                    date_from=date_from,
                    date_to=date_to,
                    pagination=pagination
                )

                return {
                    "items": result.items,
                    "total": result.total,
                    "page": result.page,
                    "size": result.size,
                    "has_next": result.has_next,
                    "has_previous": result.has_previous
                }

        except Exception as e:
            await self.handle_service_error(e, "get_user_transaction_events", {"user_id": user_id})

    async def get_transaction_analytics(
        self,
        provider: Optional[PaymentProviderType] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        currency: str = "NGN"
    ) -> Dict[str, Any]:
        """
        Get transaction analytics and performance metrics.

        Args:
            provider: Optional provider filter
            date_from: Start date filter
            date_to: End date filter
            currency: Currency filter

        Returns:
            Transaction analytics dictionary

        Raises:
            ServiceError: If analytics query fails
        """
        try:
            async with self.get_session_context() as session:
                repository = TransactionEventRepository(session)
                return await repository.get_transaction_analytics(
                    provider=provider,
                    date_from=date_from,
                    date_to=date_to,
                    currency=currency
                )

        except Exception as e:
            await self.handle_service_error(e, "get_transaction_analytics", {
                "provider": provider.value if provider else None,
                "currency": currency
            })
